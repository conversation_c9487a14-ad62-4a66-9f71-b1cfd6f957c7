<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ $t('分组规则配置') }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="slider-content">
        <div class="rule-group">
          <div class="group-header">{{ $t('基础信息') }}</div>
          <div class="group-description"></div>
          <div class="group-content">
            <mt-form v-if="groupDetail.strategyCode" :rules="formRules">
              <mt-form-item :label="$t('分组规则编号')">
                <mt-input
                  :readonly="true"
                  v-model="groupDetail.strategyCode"
                  type="text"
                  :show-clear-button="false"
                  :placeholder="$t('分组规则编号')"
                ></mt-input
              ></mt-form-item>
              <mt-form-item :label="$t('分组规则名称')">
                <mt-input
                  v-model="groupDetail.strategyName"
                  type="text"
                  :show-clear-button="false"
                  css-class="rule-element"
                  :placeholder="$t('分组规则名称')"
                ></mt-input></mt-form-item
            ></mt-form>
          </div>
        </div>
        <!-- <div class="rule-group">
          <div class="group-header">{{ $t("筛选规则") }}</div>
          <div class="group-description">
            {{ $t("多个匹配值请使用英文分好进行区分 “;”") }}
          </div>
          <div class="group-content">
            <mt-rule-config
              ref="ruleRef"
              v-model="groupLinkRules"
             :query-fields="{ text: 'headerText', value: 'field' }"
              :query-data-source="queryDataSource"
            ></mt-rule-config>
          </div>
        </div> -->
        <div class="rule-group">
          <div class="group-header">{{ $t('分组方式') }}</div>
          <div class="group-description">
            {{ $t('若不配置分组方式，则会将以上筛选条件结果，自动分为一组。') }}
          </div>
          <div class="group-content">
            <mt-rule-config
              ref="simpleRuleRef"
              type="simple"
              class="fixed-rule"
              v-model="fixedField1"
              :query-fields="{ text: 'headerText', value: 'field' }"
              :query-data-source="fixedDataSource1"
            ></mt-rule-config>
            <mt-rule-config
              ref="simpleRuleRef"
              type="simple"
              class="fixed-rule"
              v-model="fixedField2"
              :query-fields="{ text: 'headerText', value: 'field' }"
              :query-data-source="fixedDataSource2"
            ></mt-rule-config>
            <mt-rule-config
              ref="simpleRuleRef"
              type="simple"
              v-model="groupSimpleRules"
              :query-fields="{ text: 'headerText', value: 'field' }"
              :query-data-source="queryDataSource"
            ></mt-rule-config>
          </div>
        </div>
        <!-- <div class="rule-group">
          <div class="group-header">{{ $t("输出建议") }}</div>
          <div class="group-content">
            <mt-form>
              <mt-form-item :label="$t('请选择合同类型')">
                <mt-select
                  css-class="rule-element"
                  v-model="groupDetail.contactType"
                  float-label-type="Never"
                  :allow-filtering="true"
                  :data-source="propsData.recContractList"
                  :fields="{ text: 'itemName', value: 'id' }"
                  :placeholder="$t('请选择合同类型')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item :label="$t('请选择采购组织')"
                ><mt-select
                  css-class="rule-element"
                  v-model="groupDetail.purchaseOrgId"
                  float-label-type="Never"
                  :allow-filtering="true"
                  :data-source="propsData.purchaseOrganizList"
                  :fields="{ text: 'organizationName', value: 'id' }"
                  :placeholder="$t('请选择采购组织')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item :label="$t('请选择业务类型')"
                ><mt-select
                  css-class="rule-element"
                  v-model="groupDetail.businessTypeId"
                  float-label-type="Never"
                  :allow-filtering="true"
                  :data-source="propsData.businessTypeList"
                  :fields="{ text: 'itemName', value: 'id' }"
                  :placeholder="$t('请选择业务类型')"
                ></mt-select> </mt-form-item
            ></mt-form>
          </div>
        </div> -->
      </div>
      <div class="slider-footer mt-flex">
        <span @click="confirm">{{ $t('确定') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import MtRuleConfig from '@mtech/common-rule-config'
Vue.component('mt-rule-config', MtRuleConfig)

export default {
  components: {},
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {
          formRules: {}
        }
      }
    }
  },
  data() {
    return {
      configId: null,
      groupLinkRules: [
        {
          groupLeft: '',
          dataSource: '',
          dataValue: '',
          symbol: '',
          groupRight: '',
          groupLink: 'or'
        }
      ],
      fixedGroup: ['businessTypeName', 'purGroupName'],
      fixedField1: [
        {
          groupLeft: '',
          dataSource: 'businessTypeName',
          dataValue: '',
          symbol: '=',
          groupRight: '',
          groupLink: 'or'
        }
      ],
      fixedDataSource1: [
        {
          field: 'businessTypeName',
          headerText: this.$t('业务类型'),
          type: 'text'
        }
      ],
      fixedField2: [
        {
          groupLeft: '',
          dataSource: 'purGroupName',
          dataValue: '',
          symbol: '=',
          groupRight: '',
          groupLink: 'or'
        }
      ],
      fixedDataSource2: [
        {
          field: 'purGroupName',
          headerText: this.$t('采购组织'),
          type: 'text'
        }
      ],
      groupSimpleRules: [
        {
          groupLeft: '',
          dataSource: '',
          dataValue: '',
          symbol: '=',
          groupRight: '',
          groupLink: 'or'
        }
      ],
      queryDataSource: [],
      groupDetail: {
        // businessTypeId: 0, //业务类型
        // contactType: 0, //合同类型
        // purchaseOrgId: 0, //组织机构ID
        strategyName: '', //分组规则名称
        strategyCode: '' //分组规则编号
      }
    }
  },
  mounted() {
    if (this.modalData && this.modalData.configId) {
      this.configId = this.modalData.configId
      this.getGroupDetailById()
    }
    if (this.modalData && this.modalData.queryDataSource) {
      this.queryDataSource = this.modalData.queryDataSource.filter((e) => {
        return this.fixedGroup.indexOf(e.field) < 0
      })
    }
    this.getFormValidRules('formRules', this.$API.moduleConfig.saveStrategyConfigGroupValid)
  },
  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    }
  },
  methods: {
    getGroupDetailById() {
      //根据configId获取配置详情
      this.$API.moduleConfig
        .getStrategyConfigGroupDetail({ configId: this.configId })
        .then((res) => {
          let {
            strategyName,
            strategyCode,
            groupFields
            // conditions,
            // businessTypeId,
            // contactType,
            // purchaseOrgId,
          } = res.data
          this.$set(this, 'groupDetail', {
            strategyCode,
            strategyName
            // businessTypeId,
            // contactType,
            // purchaseOrgId,
          })
          // let _linkRules = [];
          // for (let i in conditions) {
          //   let _temp = conditions[i];
          //   let _condition = {
          //     groupLeft: "",
          //     dataSource: "",
          //     dataValue: "",
          //     symbol: "",
          //     groupRight: "",
          //     groupLink: "",
          //   };
          //   if (_temp["join"]) {
          //     _condition.groupLink = _temp["join"];
          //   }
          //   if (_temp["bracket"]) {
          //     if (_temp["bracket"] == "(") {
          //       _condition.groupLeft = "(";
          //     } else {
          //       _condition.groupRight = ")";
          //     }
          //   }
          //   let _exp = _temp.expression;
          //   _condition.dataSource = _exp.leftValue.field;
          //   _condition.dataValue = _exp.rightValue.value;
          //   _condition.symbol = _exp.operation;
          //   _linkRules.push(_condition);
          // }
          // this.groupLinkRules = _linkRules;

          let _simpleRules = []
          for (let i in groupFields) {
            let _condition = {
              groupLeft: '',
              dataSource: '',
              dataValue: '',
              symbol: '=',
              groupRight: '',
              groupLink: ''
            }
            _condition['dataSource'] = groupFields[i]
            _simpleRules.push(_condition)
          }
          let _filters = []
          _simpleRules.forEach((e) => {
            if (this.fixedGroup.indexOf(e) < 0) {
              _filters.push(e)
            }
          })
          if (_filters.length < 1) {
            _filters.push({
              groupLeft: '',
              dataSource: '',
              dataValue: '',
              symbol: '=',
              groupRight: '',
              groupLink: 'or'
            })
          }
          this.groupSimpleRules = _filters
        })
    },
    confirm() {
      let _linkRules = [],
        _simpleRules = []
      // this.$refs.ruleRef
      //   .getValidResult()
      //   .then((e) => {
      //     let _rules = JSON.parse(JSON.stringify(e.rules));
      //     for (let i in _rules) {
      //       let _temp = _rules[i];
      //       let _bracket = "";
      //       if (_temp["groupLeft"] == "(" && _temp["groupRight"] == ")") {
      //         _temp["groupLeft"] = "";
      //         _temp["groupRight"] = "";
      //       } else if (_temp["groupLeft"] == "(") {
      //         _bracket = "(";
      //       } else if (_temp["groupRight"] == ")") {
      //         _bracket = ")";
      //       }
      //       let _join = "";
      //       if (_temp["groupLink"]) {
      //         _join = _temp["groupLink"];
      //       }
      //       let _stringTypeList = [
      //         "purGroupName",
      //         "supplierName",
      //         "deptName",
      //         "itemName",
      //         "itemCode",
      //         "purName",
      //         "businessTypeName",
      //         "siteName",
      //         "stockSite",
      //         "categoryName",
      //       ];
      //       _linkRules.push({
      //         id: +i,
      //         expression: {
      //           leftValue: {
      //             field: _temp["dataSource"],
      //             type: "",
      //           },
      //           operation: _temp["symbol"],
      //           rightValue: {
      //             value: _temp["dataValue"],
      //             stringType: _stringTypeList.indexOf(_temp["dataSource"]) > -1,
      //           },
      //         },
      //         bracket: _bracket,
      //         join: _join,
      //       });
      //     }
      //     return this.$refs.simpleRuleRef.getValidResult();
      //   })
      this.$refs.simpleRuleRef
        .getValidResult()
        .then((s) => {
          let _rules = JSON.parse(JSON.stringify(s.rules))
          for (let i in _rules) {
            let _temp = _rules[i]
            _simpleRules.push(_temp['dataSource'])
          }
          let _filters = []
          _simpleRules.forEach((e) => {
            if (this.fixedGroup.indexOf(e) < 0) {
              _filters.push(e)
            }
          })
          let _params = {
            conditions: _linkRules,
            groupFields: _filters.concat(this.fixedGroup),
            // businessTypeId: this.groupDetail.businessTypeId, //业务类型
            // contactType: this.groupDetail.contactType, //合同类型
            // purchaseOrgId: this.groupDetail.purchaseOrgId, //组织机构ID
            strategyName: this.groupDetail.strategyName //策略名称
          }
          if (this.configId) {
            //更新分组策略
            _params.configId = this.configId
            this.$API.moduleConfig.updateStrategyConfigGroup(_params).then((res) => {
              this.$emit('confirm-function')
              console.log('update-updateStrategyConfigGroup--', res)
            })
          } else {
            //保存分组策略
            this.$API.moduleConfig.saveStrategyConfigGroup(_params).then((res) => {
              this.$emit('confirm-function')
              console.log('save-updateStrategyConfigGroup--', res)
            })
          }
        })
        .catch((e) => {
          console.warn('valid-error', e)
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.group-content {
  display: flex;
  flex-direction: column;
  .fixed-rule {
    margin-bottom: 10px;
    /deep/.rule-icons {
      visibility: hidden;
    }
  }
}
</style>
