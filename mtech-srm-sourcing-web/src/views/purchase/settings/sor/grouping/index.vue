<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { groupToolbar, groupColumnData } from './config'

export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: groupToolbar,
          grid: {
            allowFiltering: true,
            columnData: groupColumnData,
            asyncConfig: {
              url: this.$API.moduleConfig.getStrategyConfigs,
              defaultRules: [
                {
                  condition: 'and',
                  field: 'strategy_type',
                  operator: 'equal',
                  type: 'int',
                  value: 1 //0 分配策略  1分组建议
                }
              ]
            }
          }
        }
      ],
      queryDataSource: [] //新增、编辑中用到的下拉列表
    }
  },

  mounted() {
    this.getStrategyConfigCondition()
  },

  methods: {
    getStrategyConfigCondition() {
      //获取规则下拉框字段列表
      this.$API.moduleConfig.getStrategyConfigCondition().then((res) => {
        let _res = res.data
        let _conditions = []
        for (let i in _res) {
          _conditions.push({
            field: i,
            headerText: _res[i],
            type: 'text'
          })
        }
        _conditions.forEach((e) => {
          if (e.field == 'requiredDeliveryDate' || e.field == 'applyTime') {
            e.type = 'date'
          }
        })
        this.queryDataSource = _conditions
        this.getGroupConfigList()
      })
    },
    handleSelectTab(e) {
      console.log('use-handleSelectTab', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'strategyCode') {
        this.handleEditGroupConfig(e.data)
      }
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'start' || e.toolbar.id == 'stop' || e.toolbar.id == 'Delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Add') {
        this.handleAddGroupConfig()
      } else if (e.toolbar.id == 'start') {
        this.handleBatchUpdateStart(_selectGridRecords)
      } else if (e.toolbar.id == 'stop') {
        this.handleBatchUpdateStop(_selectGridRecords)
      } else if (e.toolbar.id == 'Delete') {
        this.handleBatchUpdateDelete(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        //编辑操作
        this.handleEditGroupConfig(e.data)
      } else if (e.tool.id == 'start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.id], 1)
      } else if (e.tool.id == 'stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.id], 2)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteConfig([e.data.id])
      }
    },
    //批量启动操作
    handleBatchUpdateStart(_selectGridRecords) {
      //状态 0 未启用 1 启用 2 停用
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.enableStatus === 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在‘启用’状态
        this.$toast({
          content: this.$t("选中数据中，存在'启用'状态的数据"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 1)
      }
    },
    //批量停用操作
    handleBatchUpdateStop(_selectGridRecords) {
      //状态 0 未启用 1 启用 2 停用
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.enableStatus !== 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在非‘启用’状态
        this.$toast({
          content: this.$t("只有启用状态可执行'停用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 2)
      }
    },
    //批量删除操作
    handleBatchUpdateDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //新增分组规则
    handleAddGroupConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/sor/grouping/components/groupConfig" */ './components/groupConfig.vue'
          ),
        data: {
          queryDataSource: this.queryDataSource
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑分组规则
    handleEditGroupConfig(data) {
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/settings/sor/grouping/components/groupConfig" */ './components/groupConfig.vue'
            ),
          data: {
            configId: data.id,
            queryDataSource: this.queryDataSource
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新分组规则的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      // strategyType	策略类型 0 分配 1 建议
      //状态 0 未启用 1 启用 2 停用
      let _params = {
        configIds: ids.join(','),
        enableStatus: status,
        strategyType: 1
      }
      let _statusMap = [this.$t('草稿'), this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {
          this.$API.moduleConfig.updateStrategyConfigEnable(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除规则
    handleDeleteConfig(ids) {
      //todo 弹框  确认
      let _params = {
        configIds: ids.join(','),
        strategyType: 1
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.moduleConfig.deleteStrategyConfig(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
