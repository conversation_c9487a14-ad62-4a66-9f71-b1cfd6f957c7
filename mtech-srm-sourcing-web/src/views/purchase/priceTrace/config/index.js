import { i18n, permission } from '@/main.js'
import { Formatter } from '@/utils/ej/dataGrid/index'

export const statusList = [
  { value: 0, text: i18n.t('未同步'), cssClass: 'title-#9baac1' },
  { value: 1, text: i18n.t('同步中'), cssClass: 'title-#6386c1' },
  { value: 2, text: i18n.t('同步成功'), cssClass: 'title-#6386c1' },
  { value: 3, text: i18n.t('同步失败'), cssClass: 'title-#9baac1' }
]
export const quoteAttributeList = [
  { value: 'mailing_price', text: i18n.t('寄售价'), cssClass: '' },
  { value: 'standard_price', text: i18n.t('标准价'), cssClass: '' }
]
export const quoteModeList = [
  { value: 'in_warehouse', text: i18n.t('按照入库'), cssClass: '' },
  { value: 'out_warehouse', text: i18n.t('按出库'), cssClass: '' },
  { value: 'order_date', text: i18n.t('按订单日期'), cssClass: '' }
]

const columnData = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'pushSapStatus',
    headerText: i18n.t('推送SAP状态'),
    width: 120,
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂'),
    width: 100
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商代码'),
    width: 100
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: 150
  },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码'),
    width: 150
  },
  {
    field: 'materialName',
    headerText: i18n.t('物料描述'),
    width: 150
  },
  {
    field: 'retrospectStartDate',
    headerText: i18n.t('追溯开始日期'),
    width: 120,
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'retrospectEndDate',
    headerText: i18n.t('追溯截止日期'),
    width: 120,
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'checkYear',
    headerText: i18n.t('核算年'),
    width: 80
  },
  {
    field: 'checkMonth',
    headerText: i18n.t('核算月'),
    width: 80
  },
  {
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织'),
    width: 100
  },
  {
    field: 'quoteAttr',
    headerText: i18n.t('报价属性'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'priceEffectiveMethod',
    headerText: i18n.t('价格生效方式'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'directDeliverAddr',
    headerText: i18n.t('直送地'),
    width: 100
  },
  {
    field: 'effectiveDate',
    headerText: i18n.t('价格生效日期'),
    width: 120,
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'createTime',
    headerText: i18n.t('追溯创建日期'),
    width: 120,
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'approvePassDate',
    headerText: i18n.t('SAP审核通过日期'),
    width: 120,
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'retrospectPrice',
    headerText: i18n.t('追溯价格'),
    width: 100
  },
  {
    field: 'lastPrice',
    headerText: i18n.t('上次价格'),
    width: 100
  }
]
export const pageConfig = [
  {
    gridId: permission.gridId['purchase']['priceTrace'],
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'push', icon: 'icon_solid_Createorder', title: i18n.t('推送SAP') },
          { id: 'download', icon: 'icon_solid_export', title: i18n.t('导出') }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      columnData,
      asyncConfig: {
        url: '/price/tenant/priceRecord/retrospect/pageQuery'
      }
    }
  }
]

const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const supplierColumn = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    operator: 'contains'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    operator: 'contains'
  }
]
export const dialogPageConfig = (url, type, params) => {
  const gridId = {
    material: '44174cbf-7edf-4ccc-adf0-ddb89c266c07',
    supplier: 'b5c96abb-0dc8-4ff0-a6c5-4eb5ac19f566'
  }
  const column = {
    material: materialColumn,
    supplier: supplierColumn
  }
  return [
    {
      gridId: gridId[type],
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: column[type],
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}
