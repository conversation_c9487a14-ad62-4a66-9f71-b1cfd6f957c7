<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <mt-select
                v-model="searchFormModel.companyCode"
                css-class="rule-element"
                :data-source="companyList"
                :fields="{ text: 'text', value: 'orgCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择公司')"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <mt-select
                v-model="searchFormModel.factoryCode"
                css-class="rule-element"
                :data-source="factoryList"
                :fields="{ text: 'text', value: 'orgCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择工厂')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <magnifier-input
                ref="supplierCode"
                :config="supplierDialogCofig"
                @change="(e) => (searchFormModel.supplierCode = e.supplierCode)"
              />
            </mt-form-item>
            <mt-form-item prop="materialCode" :label="$t('物料')" label-style="top">
              <magnifier-input
                ref="materialCode"
                :config="materialDialogCofig"
                @change="(e) => (searchFormModel.materialCode = e.itemCode)"
              />
            </mt-form-item>
            <mt-form-item prop="approveDate" :label="$t('SAP通过日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.approveDate"
                :allow-edit="false"
                :placeholder="$t('请选择SAP通过日期')"
                @change="handleApproveDateChange"
              />
            </mt-form-item>
            <mt-form-item prop="pushSapStatus" :label="$t('SAP推送状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.pushSapStatus"
                css-class="rule-element"
                :data-source="statusList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择SAP推送状态')"
              />
            </mt-form-item>
            <mt-form-item prop="checkYear" :label="$t('核算年')" label-style="top">
              <mt-input
                v-model="searchFormModel.checkYear"
                :show-clear-button="true"
                :placeholder="$t('请输入核算年')"
              />
            </mt-form-item>
            <mt-form-item prop="checkMonth" :label="$t('核算月')" label-style="top">
              <mt-input
                v-model="searchFormModel.checkMonth"
                :show-clear-button="true"
                :placeholder="$t('请输入核算月')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { statusList, pageConfig, dialogPageConfig } from './config/index'
import magnifierInput from '@/components/magnifierInput'
import { download } from '@/utils/utils'
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'

export default {
  components: {
    magnifierInput,
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        checkYear: this.getPreMonthInfo().preYear,
        checkMonth: this.getPreMonthInfo().preMonth
      },
      pageConfig,
      statusList,
      companyList: [],
      factoryList: [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: dialogPageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: dialogPageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      }
    }
  },
  created() {
    this.getSelectList()
  },
  methods: {
    // 获取前一个月对应的年、月
    getPreMonthInfo() {
      const curYear = new Date().getFullYear()
      const curMonth = new Date().getMonth()
      const preYear = curMonth === 0 ? curYear - 1 : curYear
      const m = curMonth === 0 ? 12 : curMonth
      const preMonth = m <= 9 ? '0' + m : m
      return {
        preYear,
        preMonth
      }
    },
    getSelectList() {
      this.getCompanyList()
      this.getFactoryList()
    },
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    // 获取工厂下拉列表
    async getFactoryList() {
      const res = await this.$API.customization.getPermissionSiteList({})
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.factoryList = res.data
      }
    },
    // 日期格式化
    handleApproveDateChange(e) {
      if (e.startDate) {
        this.searchFormModel['approveStartDate'] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['approveEndDate'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['approveStartDate'] = null
        this.searchFormModel['approveEndDate'] = null
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.$refs.materialCode.handleClear()
      this.$refs.supplierCode.handleClear()
    },
    // 点击按钮工具栏
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      if (toolbar.id === 'push') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        this.handlePush(selectedRecords)
      } else if (toolbar.id === 'download') {
        this.handleExport()
      }
    },
    // 推送SAP
    handlePush(list) {
      const idList = []
      for (let item of list) {
        if ([1, 2].includes(item.pushSapStatus)) {
          const title = {
            1: this.$t('同步中'),
            2: this.$t('同步成功')
          }
          this.$toast({
            content: this.$t(`包含${title[item.pushSapStatus]}单据，无需再次同步！`),
            type: 'warning'
          })
          return
        }
        idList.push(item.id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认推送？')
        },
        success: () => {
          this.$loading()
          this.$API.priceTrace
            .pushSap({ idList })
            .then(() => {
              this.$toast({ content: this.$t('推送成功！'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    // 导出
    handleExport() {
      // const page = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.pageSettings
      // 暂时默认导出1000条，解决pageSettings获取错误问题
      const params = {
        ...this.searchFormModel,
        page: {
          current: 1,
          size: 1000
        }
      }
      this.$loading()
      this.$API.priceTrace
        .exportList(params)
        .then((res) => {
          download({
            fileName: this.$t('价格追溯.xlsx'),
            blob: new Blob([res.data])
          })
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
