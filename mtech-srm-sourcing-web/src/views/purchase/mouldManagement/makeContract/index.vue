<!--模具制造审批表-->

<template>
  <div class="full-height" style="padding-top: 15px">
    <mt-local-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="contractType" :label="$t('合同类型')" label-style="left">
              <mt-input v-model="searchFormModel.contractType"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="left">
              <mt-input v-model="searchFormModel.supplierName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="left">
              <mt-input v-model="searchFormModel.supplierCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="modCode" :label="$t('模具编码')" label-style="left">
              <mt-input v-model="searchFormModel.modCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="sourceOrderNo" :label="$t('来源单号')" label-style="left">
              <mt-input v-model="searchFormModel.sourceOrderNo"></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { PAGE_PLUGIN } from './config/index'
import Vue from 'vue'
export default {
  components: {
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      pageConfig: null,
      copySearchFormModel: null,
      currentModel: null,
      searchFormModel: {
        contractType: '',
        supplierName: '',
        supplierCode: '',
        modCode: '',
        sourceOrderNo: ''
      }
    }
  },
  computed: {},
  created() {
    this.pageConfig = PAGE_PLUGIN()
  },
  mounted() {
    this.copySearchFormModel = { ...this.searchFormModel }
  },
  methods: {
    // 重置
    handleCustomReset() {
      this.searchFormModel = { ...this.copySearchFormModel }
      this.currentModel = { ...this.copySearchFormModel }
    },
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (_selectRows.length <= 0 && e.toolbar.id == 'Download') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //打印
      if (e.toolbar.id === 'Download') {
        this.handleClickSAP(_selectRows)
      }
    },
    //打印
    handleClickSAP(_selectRows) {
      let parameter = { id: _selectRows[0].id }
      this.$API.makeContract.modPrint(parameter).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = function () {
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        let pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        window.open(`${pdfUrl}`, '_blank')
        // window.URL.revokeObjectURL(pdfUrl) // 不释放，会导致文件下载不了的
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
