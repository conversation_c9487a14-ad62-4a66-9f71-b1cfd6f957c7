import { i18n } from '@/main.js'
const toolbar = [{ id: 'Download', icon: 'icon_solid_Download', title: i18n.t('打印') }]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'printStatusDesc',
    headerText: i18n.t('状态')
  },
  {
    field: 'contractTypeDesc',
    headerText: i18n.t('合同类型')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'modCode',
    headerText: i18n.t('模具编码')
  },
  {
    field: 'sourceOrderNo',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'contractNumber',
    headerText: i18n.t('合同编号')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('合同生成时间')
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]
export const PAGE_PLUGIN = () => {
  return [
    {
      toolbar: [toolbar, ['Filter', 'Refresh', 'Setting']],
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false,
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      grid: {
        columnData,
        asyncConfig: {
          url: '/sourcing/tenant/mod/modContractList',
          params: {}
        }
      }
    }
  ]
}
