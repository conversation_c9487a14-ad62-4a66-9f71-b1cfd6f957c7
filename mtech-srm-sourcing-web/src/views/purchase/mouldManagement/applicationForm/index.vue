<!--模具制造审批表-->

<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { PAGE_PLUGIN } from './config/index'
export default {
  data() {
    return {
      pageConfig: PAGE_PLUGIN
    }
  },
  computed: {},
  mounted() {},
  methods: {
    //行内
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field === 'id') {
        this.$router.push({
          name: `purchase-mouldManagement-applicationForm-detail`,
          query: {
            id: e.data.id,
            status: e.data.currentStatus,
            key: this.$utils.randomString()
          }
        })
      }
      if (e.field == 'oaUrl') {
        this.handleClickCellTitleOaUrl(e)
      }
    },
    handleClickCellTitleOaUrl(e) {
      console.log(e.data.oaUrl, 'oaUrl')
      if (e.data.oaUrl != '') {
        var a = document.createElement('a')
        a.href = e.data.oaUrl
        a.id = 'oaUrl'
        a.download = 'a.pdf'
        a.style = 'display:none'
        document.body.appendChild(a)
        document.getElementById('oaUrl').click()
        setTimeout(function () {
          document.getElementById('oaUrl').remove()
        }, 600)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
