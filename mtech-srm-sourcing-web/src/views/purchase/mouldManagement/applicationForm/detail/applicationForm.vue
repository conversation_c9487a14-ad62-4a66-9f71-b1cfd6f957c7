<!--盘点单列表-->
<template>
  <div class="details">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <div class="paymentMain">
      <div class="payment">
        <div class="instructions">
          <p class="header">{{ $t('付款条件说明') }}</p>
          <p>1、{{ $t('样件合格甲方发布物料认可书后付合同总价70%') }};</p>
          <p>
            2、{{
              $t('模具验收合格后(连续正常量产3个月或者3万啤，甲方发布《模具验收表》)付余款30%')
            }};
          </p>
        </div>
        <div class="paymentWay">
          <p class="header">{{ $t('付款方式说明') }}</p>
          <div class="way">
            <mt-input type="number" v-model="formObject.spotExchange"></mt-input>
            <span>%{{ $t('现汇，') }}</span>
            <mt-input type="number" v-model="formObject.bankAcceptance"></mt-input>
            <span>%180{{ $t('天银行承兑，') }}</span>
            <mt-input type="number" v-model="formObject.cash"></mt-input>
            <span>%{{ $t('的') }}</span>
            <mt-input type="number" v-model="formObject.fate"></mt-input>
            <span>{{ $t('天金单') }}</span>
          </div>
        </div>
      </div>
      <div class="totalPrice">
        <div>
          <span>{{ $t('模具套数') }}</span>
          <mt-input type="number" v-model="formObject.mouldNumber" :disabled="true"></mt-input>
          <span>{{ $t('总价') }}</span>
          <mt-input type="number" v-model="formObject.totalPrice" :disabled="true"></mt-input>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { pageConfig } from './config/applicationForm'
export default {
  props: {
    applicationFormObj: {
      type: Array,
      required: true,
      defaut: () => {
        return {}
      }
    },
    detailObj: {
      type: Object,
      required: true,
      defaut: () => {
        return {}
      }
    }
  },
  watch: {
    applicationFormObj() {
      this.detailObjFunction()
    }
  },
  data() {
    return {
      pageConfig: null,
      formObject: {
        spotExchange: '', //现汇
        bankAcceptance: '', //180天银行承兑
        cash: '', //金单百分比
        fate: '', //金单天数
        mouldNumber: '', //模具套数
        totalPrice: '' //总价
      }
    }
  },
  computed: {
    status() {
      return this.$route?.query?.status
    },
    orderId() {
      return this.$route?.query?.id
    }
  },

  methods: {
    //点击头部
    handleClickToolBar(e) {
      //导入SAP
      if (e.toolbar.id === 'SAP') {
        this.handleClickSAP()
      }
      //保存 - 当前页数据
      if (e.toolbar.id === 'Save') {
        this.$refs.tepPage.getCurrentTabRef()?.grid.endEdit()
        setTimeout(() => {
          this.handleSave()
        }, 50)
      }
    },
    handleSave() {
      // 当前编辑页面
      const currentViewSouce = this.$refs.tepPage.getCurrentTabRef().grid.getCurrentViewRecords()
      // let submitData = currentViewSouce.map((item) => {
      //   return {
      //     id: item.id,
      //     remark: item.remark
      //   }
      // })
      this.$API.applicationForm.saveBatchMoldDetail(currentViewSouce).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.getTableData()
        }
      })
    },
    // 获取详情接口，取其中模具制造审批表字段
    getTableData() {
      this.$API.applicationForm.queryMoldDetailList({ id: this.orderId }).then((res) => {
        if (res.code === 200) {
          this.$set(this.pageConfig[0].grid, 'dataSource', [...res.data])
        }
      })
    },

    //导入SAP
    handleClickSAP() {
      let parameter = { id: this.$route?.query?.id }
      this.$API.applicationForm.modSyncSap(parameter).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('导入SAP成功'),
            type: 'success'
          })
        }
        this.$emit('refreshCurrentGridData')
        this.$refs.tepPage.refreshCurrentGridData()
      })
    },
    detailObjFunction() {
      let mouldNumber = 0
      let totalPrice = 0
      this.applicationFormObj.map((item) => {
        mouldNumber += item.projectNum
        totalPrice += item.price
      })
      this.pageConfig = pageConfig(this.status)
      this.$set(this.pageConfig[0].grid, 'dataSource', this.applicationFormObj)
      let { spotExchange, bankAcceptance, cash, fate } = this.detailObj
      this.formObject = {
        spotExchange,
        bankAcceptance,
        cash,
        fate,
        mouldNumber,
        totalPrice
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.common-template-page {
  height: 100%;
  .e-gridcontent {
    min-height: 300px;
  }
}

.paymentMain {
  padding: 0 20px;
  box-sizing: border-box;
  .payment {
    width: 100%;
    font-family: PingFangSC;
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    // 左边
    .instructions {
      width: 50%;
      font-size: 16px;
    }
    //右边
    .paymentWay {
      width: 50%;
      .way {
        display: flex;
        // justify-content: space-between;
        text-align: center;
        .mt-input {
          width: 80px;
          /deep/ input {
            text-align: center;
          }
        }
        span {
          display: block;
          height: 38px;
          line-height: 38px;
          margin-left: 5px;
        }
      }
    }
    p {
      width: 100%;
      height: 30px;
      line-height: 30px;
    }
    .header {
      font-weight: 600;
    }
  }
  .balance {
    width: 100%;
    height: 30px;
    line-height: 30px;
  }
  .totalPrice {
    box-sizing: border-box;
    display: flex;
    justify-content: right;
    div {
      width: 50%;
      display: flex;
      justify-content: right;
      .mt-input {
        width: 80px;
        /deep/ input {
          text-align: center;
        }
      }
      span {
        display: block;
        height: 38px;
        line-height: 38px;
        margin: 0 10px;
      }
    }
  }
}
</style>
