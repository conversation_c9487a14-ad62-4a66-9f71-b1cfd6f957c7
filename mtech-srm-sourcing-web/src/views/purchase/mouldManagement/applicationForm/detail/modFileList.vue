<template>
  <div class="details">
    <mt-template-page ref="tepPage" :template-config="pageConfig1"></mt-template-page>
  </div>
</template>
<script>
import { pageConfig1 } from './config/applicationForm'
export default {
  props: {
    modFileList: {
      type: Array,
      required: true,
      defaut: () => {
        return {}
      }
    },
    detailObj: {
      type: Object,
      required: true,
      defaut: () => {
        return {}
      }
    },
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  watch: {
    modFileList() {
      this.detailObjFunction()
    }
  },
  data() {
    return {
      pageConfig1: pageConfig1(this.detailObj.contractType)
    }
  },
  mounted() {
    this.$set(this.pageConfig1[0].grid, 'dataSource', this.modFileList)
    this.$bus.$on('refreshCurrentGridData', () => {
      this.refreshGridData()
    })
  },
  beforeDestroy() {
    this.$bus.$off('refreshCurrentGridData')
  },
  methods: {
    detailObjFunction() {
      this.pageConfig1 = pageConfig1(this.detailObj.contractType)
      this.$set(this.pageConfig1[0].grid, 'dataSource', this.modFileList)
    },
    refreshGridData() {
      this.$API.applicationForm
        .queryMoldDetailFilesList({ id: this.$route?.query?.id })
        .then((res) => {
          if (res.code === 200) {
            res.data.forEach((item) => {
              item.parentId = this.parentId
            })
            this.$set(this.pageConfig1[0].grid, 'dataSource', [...res.data])
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.common-template-page {
  height: 100%;
  .e-gridcontent {
    min-height: 300px;
  }
}
</style>
