<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap"></div>
        <div class="btns-wrap">
          <mt-button
            v-if="status == '0' || status == '-1' || status == '2'"
            class="e-flat"
            @click="clickButtonSave"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            v-if="status == '0' || status == '-1' || status == '2'"
            class="e-flat"
            @click="clickButtonSubmit"
            >{{ $t('提交') }}</mt-button
          >
          <mt-button
            class="e-flat"
            @click="$router.push('purchase-mouldManagement-applicationForm')"
            >{{ $t('返回') }}</mt-button
          >
          <mt-button class="e-flat" v-if="status !== 0" @click="clickButtonOA">{{
            $t('OA审批记录')
          }}</mt-button>
          <!-- <div class="pack-up">
            <mt-button class="e-flat">{{ $t("收起") }}</mt-button>
            <div class="rotate">
              <MtIcon name="MT_DownArrow" />
            </div>
          </div> -->
        </div>
      </div>
      <div class="formInput">
        <mt-form ref="dialogRef" :model="formObject">
          <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
            <mt-input
              v-model="formObject.supplierCode"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('供应商编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="supplierName" :label="$t('供应商名称')">
            <mt-input
              v-model="formObject.supplierName"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('供应商名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="sourceOrderNo" :label="$t('来源单号')">
            <mt-input
              v-model="formObject.sourceOrderNo"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('来源单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="contractTypeName" :label="$t('合同类型')">
            <mt-input
              v-model="formObject.contractTypeName"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('合同类型')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </nav>
  </div>
</template>
<script>
export default {
  props: {
    detailObj: {
      type: Object,
      required: true,
      defaut: () => {
        return {}
      }
    }
  },
  watch: {
    detailObj() {
      this.detailObjFunction()
    }
  },

  data() {
    return {
      formObject: {
        supplierCode: '', //供应商编码
        supplierName: '', //供应商名称
        sourceOrderNo: '', //来源单号
        contractTypeName: '', //合同类型
        oaUrl: '', //跳转OA链接
        status: '' // 单据状态
      }
    }
  },
  created() {
    this.status = this.$route.query.status
  },
  methods: {
    //表头赋值
    detailObjFunction() {
      this.formObject.supplierCode = this.detailObj.supplierCode
      this.formObject.supplierName = this.detailObj.supplierName
      this.formObject.sourceOrderNo = this.detailObj.sourceOrderNo
      this.formObject.contractTypeName = this.detailObj.contractTypeName
      this.formObject.oaUrl = this.detailObj.oaUrl
    },
    //保存
    clickButtonSave() {
      this.$emit('clickButtonSave')
    },
    //提交
    clickButtonSubmit() {
      this.$emit('clickButtonSubmit')
    },
    //OA审批记录
    clickButtonOA() {
      const _win = window.open('about:blank')
      _win.location.href = this.formObject.oaUrl
    }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  width: 100%;
  background-color: #fff;
  nav {
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
        // .pack-up {
        //   display: inline-block;
        //   position: relative;
        //   .rotate {
        //     position: absolute;
        //     right: -5px;
        //     top: 5px;
        //     transform: rotate(180deg);
        //     .mt-icons {
        //       color: #c8d5e9;
        //     }
        //   }
        // }
      }
    }
    //表单
    .formInput {
      width: 100%;
      .mt-form {
        width: 100%;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 24%;
        }
      }
    }
  }
}
</style>
