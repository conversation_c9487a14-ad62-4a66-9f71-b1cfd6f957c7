<template>
  <div class="cell-upload" :id="'cell-upload-' + data.index">
    <mt-input :id="field" style="display: none" :value="data[field]"></mt-input>
    <div @click="showFileBaseInfo" class="cell-operable-title">
      {{ data[field] | listNumFormat }}
    </div>

    <!-- 附件弹框 -->
    <uploader-dialog @submit="submit" ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
// import bus from '@/utils/bus'
import { cloneDeep } from 'lodash'
import { i18n } from '@/main.js'
// 草稿或审批拒绝状态才能再次上传
export default {
  components: {
    UploaderDialog: () => import('COMPONENTS/NormalEdit/Upload/uploaderDialog.vue')
  },
  data() {
    return {
      data: {},
      uploadFileList: [] // 上传的附件(初始值赋值之前上传过的)
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    },
    field() {
      return this.data.column?.field
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100,
    listNumFormat(value) {
      if (value && value.length > 0) {
        return `${i18n.t('查看/上传')}(${value.length})`
      } else {
        return i18n.t('点击上传')
      }
    }
  },
  mounted() {},
  methods: {
    showFileBaseInfo() {
      const dialogParams = {
        fileData: cloneDeep(this.data[this.field]),
        isView: false, //是否可上传
        required: false, // 是否必须
        title: i18n.t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 点击行附件上传的确认按钮
    submit(e) {
      this.handleUploadFiles(e)
    },
    //执行上传文件
    handleUploadFiles(fileData) {
      // 如果直接点确定 列表数据不做改变 则fileData为空
      if (fileData && fileData.length <= 0) {
        this.$toast({
          content: this.$t('请上传附件'),
          type: 'warning'
        })
        return
      }
      let params = []
      fileData.map((item) => {
        const sysFileId = item.sysFileId || item.id
        delete item.id
        params.push({
          parentId: this.data.parentId,
          docId: this.data.id,
          fileDetailInfo: this.field,
          ...item,
          sysFileId
        })
      })
      this.$API.applicationForm.modBatchFileUpload(params).then(() => {
        this.$bus.$emit('refreshCurrentGridData')
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
      })
    }
  }
}
</script>

<style scoped>
.cell-operable-title {
  display: inline-block;
  padding: 10px;

  color: #00469c;
  font-size: 14px;
  cursor: pointer;
}
</style>
