<template>
  <div class="inventory">
    <!-- 头部 -->
    <top-info
      ref="topInfo"
      :detail-obj="detailObj"
      @clickButtonSave="clickButtonSave"
      @clickButtonSubmit="clickButtonSubmit"
    ></top-info>
    <!-- tabs切换 -->
    <mt-tabs
      tab-id="message-template"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 申请表and附件 -->
    <application-form
      ref="applicationForm"
      v-show="handleSelect == '1'"
      :application-form-obj="applicationFormObj"
      :detail-obj="detailObj"
      @refreshCurrentGridData="refreshCurrentGridData"
    ></application-form>
    <!-- 附件 -->
    <modFileList
      ref="modFileList"
      v-show="handleSelect == '2'"
      :detail-obj="detailObj"
      :mod-file-list="modFileList"
      :parent-id="id"
      @refreshCurrentGridData="refreshCurrentGridData"
    ></modFileList>
  </div>
</template>
<script>
import topInfo from './topInfo.vue'
import applicationForm from './applicationForm.vue'
import modFileList from './modFileList.vue'
export default {
  components: {
    topInfo,
    applicationForm,
    modFileList
  },
  data() {
    return {
      dataSource: [
        {
          title: this.$t('模具制造审批表'),
          code: '1'
        },
        {
          title: this.$t('模具制造审批表附件'),
          code: '2'
        }
      ], //列表切换
      detailObj: {}, //详情信息
      applicationFormObj: [], //详情--模具制造审批表
      modFileList: [], //详情--模具制造审批表附件
      handleSelect: '1', //表头切换
      id: null // 请求参数id
    }
  },
  mounted() {
    // 初始调用
    this.id = this.$route.query.id
    this.initialNewCall()
  },
  methods: {
    //请求详情接口--获取数据
    initialNewCall() {
      let parameter = {
        id: this.id
      }
      this.$API.applicationForm.detailQueryBuilder(parameter).then((res) => {
        this.detailObj = res.data
        this.applicationFormObj = this.detailObj.modDetailDTOList
        // 给数据中添加parentId
        if (this.detailObj?.modFileList && this.detailObj?.modFileList.length > 0) {
          this.detailObj?.modFileList.forEach((item) => {
            item.parentId = this.id
          })
        }
        this.modFileList = this.detailObj.modFileList
      })
    },
    //切换头部
    handleSelectTab(e, item) {
      this.handleSelect = item.code
    },
    //保存
    clickButtonSave() {
      let applicationForm = this.$refs.applicationForm.formObject
      if (!applicationForm.spotExchange && applicationForm.spotExchange !== 0) {
        this.$toast({ content: this.$t('现汇值为空'), type: 'warning' })
        return
      }
      if (!applicationForm.bankAcceptance && applicationForm.bankAcceptance !== 0) {
        this.$toast({ content: '180天银行承兑为空', type: 'warning' })
        return
      }
      if (!applicationForm.cash && applicationForm.cash !== 0) {
        this.$toast({ content: this.$t('金单百分比为空'), type: 'warning' })
        return
      }
      let totalPercentage =
        Number(applicationForm.spotExchange) +
        Number(applicationForm.bankAcceptance) +
        Number(applicationForm.cash)
      if (totalPercentage != 100) {
        this.$toast({ content: this.$t('百分比为总值不等于100%'), type: 'warning' })
        return
      }
      if (!applicationForm.fate && applicationForm.fate !== 0) {
        this.$toast({ content: this.$t('金单天数为空'), type: 'warning' })
        return
      }
      let parameter = {}
      parameter.spotExchange = applicationForm.spotExchange
      parameter.bankAcceptance = applicationForm.bankAcceptance
      parameter.cash = applicationForm.cash
      parameter.fate = applicationForm.fate
      parameter.id = this.id
      this.$API.applicationForm.modSave(parameter).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.initialNewCall()
      })
    },
    //提交
    clickButtonSubmit() {
      let applicationForm = this.$refs.applicationForm.formObject

      if (!applicationForm.spotExchange && applicationForm.spotExchange !== 0) {
        this.$toast({ content: this.$t('现汇值为空'), type: 'warning' })
        return
      }
      if (!applicationForm.bankAcceptance && applicationForm.bankAcceptance !== 0) {
        this.$toast({ content: '180天银行承兑为空', type: 'warning' })
        return
      }
      if (!applicationForm.cash && applicationForm.cash !== 0) {
        this.$toast({ content: this.$t('金单百分比为空'), type: 'warning' })
        return
      }
      let totalPercentage =
        Number(applicationForm.spotExchange) +
        Number(applicationForm.bankAcceptance) +
        Number(applicationForm.cash)
      if (totalPercentage != 100) {
        this.$toast({ content: this.$t('百分比为总值不等于100%'), type: 'warning' })
        return
      }
      if (!applicationForm.fate && applicationForm.fate !== 0) {
        this.$toast({ content: this.$t('金单天数为空'), type: 'warning' })
        return
      }
      let parameter = {}
      parameter.spotExchange = applicationForm.spotExchange
      parameter.bankAcceptance = applicationForm.bankAcceptance
      parameter.cash = applicationForm.cash
      parameter.fate = applicationForm.fate
      parameter.id = this.id
      this.$API.applicationForm.modModCommit(parameter).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$router.push({
          name: `purchase-mouldManagement-applicationForm`
        })
      })
    },
    //刷新页面
    refreshCurrentGridData() {
      this.initialNewCall()
    }
  }
}
</script>
<style lang="scss" scoped>
.inventory {
  height: 100%;
}
</style>
