import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import cellUpload from '../components/cellUpload' // 单元格上传
const toolbar = [
  {
    id: 'SAP',
    icon: 'icon_table_new',
    title: i18n.t('导入SAP')
  }
]
const toolbarSave = [
  {
    id: 'SAP',
    icon: 'icon_table_new',
    title: i18n.t('导入SAP')
  },
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存')
  }
]
export const columnData1 = [
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   allowEditing: false
  // },
  {
    field: 'currentStatusDesc',
    headerText: i18n.t('推送状态'),
    allowEditing: false,
    width: 120
  },
  {
    field: 'model',
    headerText: i18n.t('机型'),
    width: 120
  },
  {
    field: 'modName',
    headerText: i18n.t('名称'),
    width: 120
  },
  {
    field: 'partNumber',
    headerText: i18n.t('零件编号P/N'),
    width: 120
  },
  {
    field: 'insideCode',
    headerText: i18n.t('内部编码'),
    width: 120
  },
  {
    field: 'modCode',
    headerText: i18n.t('模具编码'),
    allowEditing: false,
    width: 120
  },
  {
    field: 'outModNum',
    headerText: i18n.t('出模数'),
    editType: 'numericedit',
    width: 80,
    edit: {
      params: {
        min: 0
      }
    }
  },
  {
    field: 't1Time',
    headerText: i18n.t('T1时间'),
    editType: 'datepickeredit',
    type: 'date',
    format: 'yyyy-MM-dd',
    width: 120,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return utils.formatTime(new Date(e), 'YYYY-mm-dd')
        }
      }
    }
  },
  {
    field: 'projectNum',
    headerText: i18n.t('数量'),
    editType: 'numericedit',
    width: 80,
    edit: {
      params: {
        min: 0
      }
    }
  },
  {
    field: 'price',
    headerText: i18n.t('模具价格'),
    editType: 'numericedit',
    width: 80,
    edit: {
      params: {
        min: 0
      }
    }
  },
  {
    field: 'standNum',
    headerText: i18n.t('标准规划量（K）'),
    editType: 'numericedit',
    width: 140,
    edit: {
      params: {
        min: 0
      }
    }
  },
  {
    field: 'remark',
    width: '280',
    headerText: i18n.t('备注')
  }
]
export const columnData2 = (contractType) => [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'costEvaluationForm',
    headerText:
      contractType == 1 || contractType == 2
        ? i18n.t('基础/复制模具-MKP、费用评估表')
        : i18n.t('改模-MKP、费用评估表'),
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'biddingForm',
    headerText: i18n.t('资源选择表、竞价表'),
    visible: contractType == 1 || contractType == 2,
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'priceDecision',
    headerText: i18n.t('价格决定表'),
    visible: contractType == 3 || contractType == 4,
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'moldOpeningProcess',
    headerText: i18n.t('开模流程'),
    visible: contractType == 1 || contractType == 2,
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'moldModificationProcess',
    headerText: i18n.t('改模流程（改模资料）'),
    visible: contractType == 3 || contractType == 4,
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'partApprovalProcess',
    headerText: i18n.t('部品认可流程'),
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'moldStorageBook',
    headerText: i18n.t('模具保管书'),
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'moldMaterialNameplate',
    headerText: i18n.t('模具实物铭牌'),
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'quotation',
    headerText: i18n.t('报价单'),
    template: function () {
      return {
        template: cellUpload
      }
    }
  }
]
export const pageConfig = (status) => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [status == '0' || status == '2' ? toolbarSave : toolbar, []] // 草稿、驳回状态下可以编辑
    },
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: columnData1,
      editSettings: {
        allowEditing: status == '0' || status == '2' ? true : false
      }
    }
  }
]
export const pageConfig1 = (contractType) => [
  {
    toolbar: {
      useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
    },
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: columnData2(contractType)
    }
  }
]
