import { i18n } from '@/main.js'
import { searchOptionsList } from '@/constants'
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'oaUrl',
    headerText: i18n.t('OA审批查看链接'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == '') {
          return '--'
        } else {
          return i18n.t('OA审批查看')
        }
      }
    },
    ignore: true
  },
  {
    field: 'id',
    headerText: i18n.t('审批表编号'),
    cssClass: 'field-content'
  },
  {
    field: 'currentStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-2': i18n.t('审批废弃'),
        '-1': i18n.t('关闭'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批驳回'),
        3: i18n.t('审批通过'),
        4: i18n.t('推动SAP失败'),
        5: i18n.t('已完成')
      }
    }
  },
  {
    field: 'failReason',
    headerText: i18n.t('状态信息')
  },
  {
    field: 'contractType',
    headerText: i18n.t('合同类型'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('首套模'),
        2: i18n.t('复制模'),
        3: i18n.t('修改模'),
        4: i18n.t('复制修改模')
      }
    }
  },
  {
    field: 'modCodes',
    headerText: i18n.t('模具编码')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'sourceOrderNo',
    headerText: i18n.t('寻源单号')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  }
]
export const PAGE_PLUGIN = [
  {
    toolbar: [],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    gridId: '0860be89-bcbe-40fa-beff-af9143293ae0',
    grid: {
      columnData,
      asyncConfig: {
        url: '/sourcing/tenant/mod/queryBuilder',
        params: {}
      }
    }
  }
]
