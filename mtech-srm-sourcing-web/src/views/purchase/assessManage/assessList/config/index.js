import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export const perPublishCols = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'claimCode',
    headerText: i18n.t('考核单编码'),
    cellTools: ['edit', 'delete']
  },
  {
    field: 'claimTypeName',
    headerText: i18n.t('考核类型')
  },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已取消'),
        0: i18n.t('新建'),
        1: i18n.t('已提交'),
        3: i18n.t('审批拒绝'),
        10: i18n.t('待反馈'),
        11: i18n.t('已反馈'),
        12: i18n.t('已确认'),
        13: i18n.t('申诉处理审批中'),
        14: i18n.t('申诉处理审批拒绝'),
        15: i18n.t('已改判'),
        16: i18n.t('不改判'),
        17: i18n.t('已付款')
      }
    }
  },
  {
    field: 'createType',
    headerText: i18n.t('单据来源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('手动创建'), 1: i18n.t('自动创建') }
    }
  },
  {
    field: 'referenceClaim',
    headerText: i18n.t('关联单据号')
  },
  {
    field: 'offlineEnsure',
    headerText: i18n.t('供应商是否已线下确认'),
    valueConverter: {
      type: 'map',
      map: { false: i18n.t('否'), true: i18n.t('是') }
    }
  },
  {
    field: 'claimTotalAmount',
    headerText: i18n.t('索赔总额')
  },
  {
    field: 'claimMonth',
    headerText: i18n.t('考核月份')
  },
  {
    field: 'itemName',
    headerText: i18n.t('考核品类')
  },
  {
    field: 'feedbackEndTime',
    headerText: i18n.t('要求反馈日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  }
]
export const assessColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'claimCode',
    headerText: i18n.t('考核单编码'),
    cellTools: []
  },
  {
    field: 'claimTypeName',
    headerText: i18n.t('考核维度')
  },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已取消'),
        0: i18n.t('新建'),
        1: i18n.t('已提交'),
        3: i18n.t('审批拒绝'),
        10: i18n.t('待反馈'),
        11: i18n.t('已反馈'),
        12: i18n.t('已确认'),
        13: i18n.t('申诉处理审批中'),
        14: i18n.t('申诉处理审批拒绝'),
        15: i18n.t('已改判'),
        16: i18n.t('不改判'),
        17: i18n.t('已付款')
      }
    }
  },
  {
    field: 'createType',
    headerText: i18n.t('单据来源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('手动创建'), 1: i18n.t('自动创建') }
    }
  },
  {
    field: 'referenceClaim',
    headerText: i18n.t('关联单据号')
  },
  {
    field: 'offlineEnsure',
    headerText: i18n.t('供应商是否签字确认'),
    valueConverter: {
      type: 'map',
      map: { false: i18n.t('否'), true: i18n.t('是') }
    }
  },
  {
    field: 'claimTotalAmount',
    headerText: i18n.t('索赔总额')
  },
  {
    field: 'claimMonth',
    headerText: i18n.t('考核月份')
  },
  {
    field: 'itemName',
    headerText: i18n.t('考核品类')
  },
  {
    field: 'feedbackEndTime',
    headerText: i18n.t('要求反馈日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'realFeedbackTime',
    headerText: i18n.t('实际反馈日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
