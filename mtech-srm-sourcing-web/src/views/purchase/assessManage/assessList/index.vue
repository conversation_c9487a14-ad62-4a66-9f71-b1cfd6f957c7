<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { perPublishCols, assessColumn } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('待发布'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                'Add',
                'Edit',
                {
                  id: 'publish',
                  icon: 'icon_table_new',
                  title: this.$t('提交')
                },
                'Delete'
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: perPublishCols,
            frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claim/pageClaim',
              params: { type: 0 }
            }
          }
        },
        {
          title: this.$t('考核单汇总'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'commit',
                  icon: 'icon_table_new',
                  title: this.$t('确认扣款')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: assessColumn,
            frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claim/pageClaim',
              params: { type: 1 }
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (
        records.length <= 0 &&
        !(
          item.toolbar.id == 'Add' ||
          item.toolbar.id == 'Filter' ||
          item.toolbar.id == 'Refresh' ||
          item.toolbar.id == 'Setting'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          data: {
            title: this.$t('新建考核单')
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (item.toolbar.id == 'Edit') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
          return
        } else {
          this.$dialog({
            modal: () => import('./components/addDialog.vue'),
            data: {
              title: this.$t('编辑考核单'),
              isEdit: true,
              info: records[0]
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      } else if (item.toolbar.id == 'Delete') {
        let _idList = records.map((e) => e.id)
        this.deleteRecord(_idList)
      }
    },
    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (['edit', 'delete'].includes(tool.id)) {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        if (tool.id === 'edit') {
          this.$dialog({
            modal: () => import('./components/addDialog.vue'),
            data: {
              title: this.$t('编辑考核单'),
              isEdit: true,
              info: data
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
        tool.id === 'delete' && this.deleteRecord([data.id])
      }
    },
    deleteRecord(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？'),
          confirm: () =>
            this.$API.assessManage.deleteClaim({
              idList: ids
            })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'purchase-assessmanage-assessListDetail',
        query: {
          type: data.status,
          id: data.id
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
