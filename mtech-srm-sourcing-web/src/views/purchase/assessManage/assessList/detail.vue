<template>
  <div class="full-height">
    <div class="detail-card">
      <div class="desc">
        <div class="desc-title-box">
          <span class="title">
            <div class="detail-item">
              <span>{{ $t('考核单编号：') }}</span>
              <span>{{ formObject.claimCode }}</span>
            </div>
          </span>
        </div>
        <div class="desc-detail-box mb_7">
          <div class="detail-item">
            <span>{{ $t('状态：') }}</span>
            <span>{{ statusLabel }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('考核维度：') }}</span>
            <span>{{ formObject.claimTypeName }}</span>
          </div>
          <div class="detail-item">
            <span style="display: inline-block">{{ $t('考核月份：') }}</span>
            <span class="reason">{{ formObject.claimMonth }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('所属公司：') }}</span>
            <span>{{ formObject.companyName }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('供应商名称：') }}</span>
            <span>{{ formObject.supplierName }}</span>
          </div>
        </div>
        <div class="desc-detail-box">
          <div class="detail-item">
            <span>{{ $t('考核品类：') }}</span>
            <span>{{ formObject.itemName }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('协议书模板：') }}</span>
            <span>{{ formObject.agreementName }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('创建人：') }}</span>
            <span>{{ formObject.createUserName }}</span>
          </div>
          <div class="detail-item">
            <span>{{ $t('创建时间：') }}</span>
            <span>{{ createTime }}</span>
          </div>
        </div>
      </div>
      <div class="buttons-box">
        <span class="btn" @click="cancel">{{ $t('返回') }}</span>
        <span class="btn" v-if="!!isPerPublish" @click="save">{{ $t('保存') }}</span>
        <span class="btn" v-if="!!isPerPublish" @click="saveAndCommit">{{ $t('保存并提交') }}</span>
      </div>
    </div>
    <mt-tabs
      :tab-id="tabId"
      :e-tab="false"
      :data-source="pageConfig"
      @handleSelectTab="handleSelectTab"
      v-if="!isPerPublish"
    ></mt-tabs>
    <claim-detail
      ref="claimDetail"
      v-show="currentTabIndex === 0"
      :info="formObject"
    ></claim-detail>
    <supplier-feedback
      ref="supplierFeedback"
      v-if="!isPerPublish"
      v-show="currentTabIndex === 1"
      :info="formObject"
    ></supplier-feedback>
    <appeal-history
      ref="appealHistory"
      v-if="!isPerPublish"
      v-show="currentTabIndex === 2"
      :info="formObject"
    ></appeal-history>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import claimDetail from './pages/claimDetail.vue'
import supplierFeedback from './pages/supplierFeedback.vue'
import appealHistory from './pages/appealHistory.vue'
export default {
  components: {
    claimDetail,
    supplierFeedback,
    appealHistory
  },
  data() {
    return {
      statusList: [
        { code: -1, label: this.$t('已取消') },
        { code: 0, label: this.$t('新建') },
        { code: 1, label: this.$t('已提交') },
        { code: 3, label: this.$t('审批拒绝') },
        { code: 10, label: this.$t('待反馈') },
        { code: 11, label: this.$t('已反馈') },
        { code: 12, label: this.$t('已确认') },
        { code: 13, label: this.$t('申诉处理审批中') },
        { code: 14, label: this.$t('申诉处理审批拒绝') },
        { code: 15, label: this.$t('已改判') },
        { code: 16, label: this.$t('不改判') },
        { code: 17, label: this.$t('已付款') }
      ],
      isPerPublish: false,
      isShow: false,
      tabId: 'assessListDetailTabs',
      formObject: {
        projectCode: null
      },
      currentTabIndex: 0,
      pageConfig: [
        { title: this.$t('单据详情') },
        { title: this.$t('供方反馈') },
        { title: this.$t('申诉历史查看') }
      ]
    }
  },

  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    },
    createTime() {
      return this.formatTime(this.formObject.createTime)
    },
    statusLabel() {
      return this.statusList.find((e) => e.code == this.formObject.status).label
    }
  },
  mounted() {
    if (this.queryType == 0) {
      this.isPerPublish = true
    }
    this.getDetail()
  },
  methods: {
    formatTime(e) {
      if (e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return utils.formatTime(e)
        } else if (typeof e == 'string') {
          if (e.indexOf('T') != -1) {
            return e.substr(0, 10)
          } else {
            let val = parseInt(e)
            return utils.formatTime(new Date(val))
          }
        } else if (typeof e == 'number') {
          return utils.formatTime(new Date(e))
        } else {
          return e
        }
      } else {
        return e
      }
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    getDetail() {
      if (this.queryType == 12) {
        this.$API.assessManage.detailPublishedClaim({ id: this.queryId }).then((res) => {
          if (res.code === 200) {
            this.formObject = { ...res.data }
          }
        })
      } else {
        this.$API.assessManage.detailClaim({ id: this.queryId }).then((res) => {
          if (res.code === 200) {
            this.formObject = { ...res.data }
          }
        })
      }
    },
    cancel() {
      this.$router.push({
        name: 'purchase-assessmanage-assessList'
      })
    },
    save() {
      this.$refs.claimDetail.$refs.costCenterTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      this.$refs.claimDetail.$refs.assessIndexTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      setTimeout(() => {
        let _costCenterList =
          this.$refs.claimDetail.$refs.costCenterTemplateRef.getCurrentUsefulRef().ejsRef
            .ej2Instances.currentViewData
        let _standDetailList =
          this.$refs.claimDetail.$refs.assessIndexTemplateRef.getCurrentUsefulRef().ejsRef
            .ej2Instances.currentViewData
        let _attachmentList =
          this.$refs.claimDetail.$refs.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances
            .currentViewData
        _standDetailList.forEach((e) => {
          e.happenTime = Number(new Date(e.happenTime))
        })
        if (_standDetailList.some((e) => !e.happenTime)) {
          this.$toast({
            content: this.$t('考核指标详情明细行发生时间不能为空'),
            type: 'warning'
          })
          return
        }
        let _tempData = {
          ...this.$refs.claimDetail.$refs.formInfo.model,
          feedbackEndTime: Number(this.$refs.claimDetail.$refs.formInfo.model.feedbackEndTime),
          reasonDesc: this.$refs.claimDetail.$refs.editorRef.value,
          costCenterList: _costCenterList,
          standDetailList: _standDetailList,
          attachmentList: _attachmentList,
          id: this.queryId
        }
        console.log('saveAssessDetail=', this, _tempData)
        this.$API.assessManage.completeClaim(_tempData).then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          }
        })
      }, 100)
    },
    saveAndCommit() {
      this.$refs.claimDetail.$refs.costCenterTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      this.$refs.claimDetail.$refs.assessIndexTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.endEdit()
      setTimeout(() => {
        let _costCenterList =
          this.$refs.claimDetail.$refs.costCenterTemplateRef.getCurrentUsefulRef().ejsRef
            .ej2Instances.currentViewData
        let _standDetailList =
          this.$refs.claimDetail.$refs.assessIndexTemplateRef.getCurrentUsefulRef().ejsRef
            .ej2Instances.currentViewData
        let _attachmentList =
          this.$refs.claimDetail.$refs.attachmentRef.getCurrentUsefulRef().ejsRef.ej2Instances
            .currentViewData

        if (!_costCenterList.length) {
          this.$toast({
            content: this.$t('成本中心分摊比例总计应为100%'),
            type: 'warning'
          })
          return
        } else {
          let _ratio = _costCenterList.map((e) => e.shareRatio)
          let _shareRatio = this.sum(_ratio)
          if (_shareRatio !== 100) {
            this.$toast({
              content: this.$t('成本中心分摊比例总计应为100%'),
              type: 'warning'
            })
            return
          }
          console.log('_shareRatio', _shareRatio)
        }
        if (!_standDetailList.length) {
          this.$toast({
            content: this.$t('考核指标详情不能为空'),
            type: 'warning'
          })

          return
        } else {
          if (_standDetailList.some((e) => !e.happenTime)) {
            this.$toast({
              content: this.$t('考核指标详情明细行发生时间不能为空'),
              type: 'warning'
            })
            return
          }
        }
        _standDetailList.forEach((e) => {
          e.happenTime = Number(new Date(e.happenTime))
        })
        let _tempData = {
          ...this.$refs.claimDetail.$refs.formInfo.model,
          feedbackEndTime: Number(this.$refs.claimDetail.$refs.formInfo.model.feedbackEndTime),
          reasonDesc: this.$refs.claimDetail.$refs.editorRef.value,
          costCenterList: _costCenterList,
          standDetailList: _standDetailList,
          attachmentList: _attachmentList,
          id: this.queryId
        }
        console.log('saveAssessDetail=', this, _tempData)
        this.$API.assessManage.completeAndSubmitClaim(_tempData).then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存并提交成功'),
              type: 'success'
            })
            this.cancel()
          }
        })
      }, 100)
    },
    sum(arr) {
      return eval(arr.join('+'))
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-card {
  width: 100%;
  height: 100px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 70px 20px 20px;
  display: flex;
  .logo {
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    background: #00469c;
    font-size: 40px;
    font-weight: bold;
    color: #fff;
    border-radius: 50%;
    margin-right: 22px;
  }
  .desc {
    flex: 1;
    height: 57px;
    .desc-title-box {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      .title {
        font-size: 20px;
        font-weight: bold;
        color: #292929;
      }
      .tag {
        font-size: 12px;
        display: inline-block;
        padding: 4px;
        border-radius: 2px;
      }
      .status {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
        margin: 0 10px;
      }
      .group {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
    .desc-detail-box {
      display: flex;
      .detail-item {
        font-size: 12px;
        color: #9a9a9a;
        margin-right: 20px;
      }
    }
  }
  .buttons-box {
    display: flex;
    align-items: center;
    .btn {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      color: #00469c;
      margin-left: 25px;
      cursor: pointer;
    }
    .is-disabled {
      color: #ccc;
    }
  }
}
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.operateButton {
  position: absolute;
  right: 20px;
  z-index: 1;
  top: 10px;
}
.mt-tabs {
  width: 100%;
  background-color: #fafafa;
  /deep/.mt-tabs-container {
    width: 100%;
    margin-right: 155px;
  }
}
.reason {
  max-width: 350px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mt-tooptip {
  display: inline-block;
}
.mb_7 {
  margin-bottom: 7px;
}
</style>
