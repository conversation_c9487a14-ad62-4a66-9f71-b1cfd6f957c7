import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Select from '../editComponents/Select.vue'
import InputNumber from '../editComponents/InputNumber.vue'
import cellChanged from '../editComponents/cellChanged.vue'
import selectedItemCode from 'COMPONENTS/NormalEdit/selectItemCode' // 物料
import Vue from 'vue'
export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}

export const costCenterColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'costCenterCode',
    headerText: i18n.t('成本中心代码'),
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('成本中心代码')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'costCenterDesc',
    headerText: i18n.t('成本中心描述'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'shareRatio',
    headerText: i18n.t('分摊比例(%)'),
    editTemplate: () => {
      return { template: InputNumber }
    },
    // editType: "numericedit",
    // edit: {
    //   params: {
    //     min: 0,
    //     max: 100,
    //     decimals: 0,
    //     format: "###",
    //     validateDecimalOnType: true,
    //     htmlAttributes: { type: "number" },
    //     showSpinButton: false,
    //   },
    // },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('分摊比例(%)')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'untaxedPrice',
    headerText: i18n.t('不含税金额'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  }
]

export const assessmentIndexColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'standCode',
    headerText: i18n.t('考核指标代码'),
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('考核指标代码')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'standName',
    headerText: i18n.t('考核指标描述'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'standDesc',
    headerText: i18n.t('考核指标说明'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'happenTime',
    width: 230,
    headerText: i18n.t('发生时间'),
    editType: 'dateTimePickerEdit',
    edit: {
      params: {
        // format: "yyyy-MM-d hh:mm",
        max: new Date()
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('发生时间')}}</span>
              </div>
            `
        })
      }
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e, 'YYYY-mm-dd HH:MM')
          } else if (typeof e == 'string') {
            if (e.indexOf(':') != -1) {
              return e
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val), 'YYYY-mm-dd HH:MM')
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e), 'YYYY-mm-dd HH:MM')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    editTemplate: () => {
      return {
        template: selectedItemCode
      }
    }
  },
  // {
  //   field: "itemName",
  //   headerText: i18n.t("物料名称"),
  // },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'quantity',
    headerText: i18n.t('数量'),
    editType: 'numericedit',
    edit: {
      params: {
        min: 0,
        decimals: 3,
        format: '###',
        validateDecimalOnType: true,
        htmlAttributes: { type: 'number' },
        showSpinButton: false
      }
    }
  },
  {
    field: 'unitPrice',
    headerText: i18n.t('单价'),
    editType: 'numericedit',
    edit: {
      params: {
        min: 0,
        decimals: 3,
        format: '###.###',
        validateDecimalOnType: true,
        htmlAttributes: { type: 'number' },
        showSpinButton: false
      }
    }
  },
  {
    field: 'claimDesc',
    headerText: i18n.t('考核说明')
  },
  {
    field: 'taxInclusiveName',
    headerText: i18n.t('是否含税'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'taxInclusive',
    headerText: i18n.t('是否含税'),
    visible: false,
    allowEditing: false
  },
  {
    field: 'taxTypeName',
    headerText: i18n.t('税率'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'taxAmount',
    headerText: i18n.t('税额'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'untaxedPrice',
    headerText: i18n.t('不含税金额'),
    editTemplate: () => {
      return { template: InputNumber }
    }
  },
  {
    field: 'taxedPrice',
    headerText: i18n.t('含税金额'),
    editTemplate: () => {
      return { template: InputNumber }
    }
  },
  {
    field: 'refRes',
    headerText: i18n.t('联带物品')
  },
  {
    field: 'refResUnitName',
    headerText: i18n.t('单位'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'refResQuantity',
    headerText: i18n.t('联带数量'),
    editType: 'numericedit',
    edit: {
      params: {
        min: 0,
        decimals: 3,
        format: '###',
        validateDecimalOnType: true,
        htmlAttributes: { type: 'number' },
        showSpinButton: false
      }
    }
  },
  {
    field: 'refAmount',
    headerText: i18n.t('联带金额'),
    editType: 'numericedit',
    edit: {
      params: {
        min: 0,
        decimals: 3,
        format: '###.###',
        validateDecimalOnType: true,
        htmlAttributes: { type: 'number' },
        showSpinButton: false
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const attachmentColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'attachmentName',
    headerText: i18n.t('附件名称')
  },
  {
    field: 'attachmentSize',
    headerText: i18n.t('附件大小')
  },
  {
    field: 'uploadUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
