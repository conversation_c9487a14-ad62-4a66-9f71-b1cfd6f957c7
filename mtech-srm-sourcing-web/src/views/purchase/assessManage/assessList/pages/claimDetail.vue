<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('基本信息') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="basicInfo" :rules="rules" :auto-complete="false">
          <mt-form-item class="form-item" :label="$t('币种')" label-style="top" prop="currencyCode">
            <mt-select
              :disabled="queryType != 0"
              v-model="basicInfo.currencyCode"
              :data-source="currencyList"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择')"
              :fields="{ text: 'currencyName', value: 'currencyCode' }"
              @change="changeCurrencyCode"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('反馈截止时间')"
            label-style="top"
            prop="feedbackEndTime"
          >
            <mt-date-time-picker
              :disabled="queryType != 0"
              :width="300"
              v-model="basicInfo.feedbackEndTime"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('关联单据')"
            label-style="top"
            prop="referenceClaimCode"
          >
            <mt-select
              :disabled="queryType != 0"
              :width="300"
              :data-source="referenceList"
              v-model="basicInfo.referenceClaim"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('索赔总额')"
            label-style="top"
            prop="claimTotalAmount"
          >
            <mt-input
              :disabled="true"
              :width="300"
              v-model="basicInfo.claimTotalAmount"
              :min="0"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('是否已线下确认')"
            label-style="top"
            prop="offlineEnsure"
          >
            <mt-select
              :disabled="queryType != 0"
              :width="300"
              :data-source="booleanList"
              v-model="basicInfo.offlineEnsure"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !handleAssessExpand && 'top-filter-small']"
      class="top-filter"
      v-if="queryType != 0"
    >
      <div class="accordion-title">{{ $t('申诉处理') }}</div>
      <div class="sort-box" @click="handleAssessExpand = !handleAssessExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="handleAssessExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="formInfo" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('申诉处理')"
            label-style="top"
            prop="dealAppeal"
          >
            <mt-select
              :disabled="queryType != 0"
              v-model="basicInfo.dealAppeal"
              :data-source="handleClaimList"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择')"
              @change="changeDealAppeal"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item class="form-item full-width" :label="$t('决议说明')" label-style="top">
            <mt-input
              :disabled="queryType != 0"
              v-model="basicInfo.remark"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !costCenterExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('成本中心分摊') }}</div>
      <div class="sort-box" @click="costCenterExpand = !costCenterExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="costCenterExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="costCenterTemplateRef"
          :template-config="costCenterConfig"
          @actionBegin="costCenterActionBegin"
          @actionComplete="costCenterActionComplete"
          @handleClickToolBar="handleClickCostCenterToolBar"
          @selectedChanged="costCenterSelectedChanged"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !assessIndexExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('考核指标详情') }}</div>
      <div class="sort-box" @click="assessIndexExpand = !assessIndexExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="assessIndexExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="assessIndexTemplateRef"
          :template-config="assessmentIndexConfig"
          @actionBegin="assessIndexActionBegin"
          @actionComplete="assessIndexActionComplete"
          @handleClickToolBar="handleClickAssessToolBar"
          @selectedChanged="assessIndexSelectedChanged"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !reasonExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('原因说明') }}</div>
      <div class="sort-box" @click="reasonExpand = !reasonExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="reasonExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-input
          :disabled="queryType != 0"
          ref="editorRef"
          v-model="templateText"
          :multiline="true"
          :rows="2"
          maxlength="200"
          float-label-type="Never"
          :placeholder="$t('字数不超过200字')"
        ></mt-input>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !purAttachExpand && 'top-filter-small', 'mb-30']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('采方附件') }}</div>
      <div class="sort-box" @click="purAttachExpand = !purAttachExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="purAttachExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="attachmentRef"
          :template-config="purchaseAttachmentConfig"
          @handleClickToolBar="handleClickPurchaseAttachmentToolBar"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import {
  costCenterColumn,
  assessmentIndexColumn,
  attachmentColumn,
  editSettings
} from './config/index'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    }
  },
  mounted() {
    this.getCurrencyList()
    this.listAvailableClaim()
    console.log('mounted=', this.info)
    this.basicInfo = {
      ...this.info,
      feedbackEndTime: new Date(Number(this.info.feedbackEndTime))
    }

    this.info.standDetailList.forEach(
      (e) => (e.happenTime = utils.formatTime(new Date(Number(e.happenTime)), 'YYYY-mm-dd HH:MM'))
    )
    this.info.costCenterList.forEach((e) => (e.costCenterAddId = this.costCenterAddId++))
    this.info.standDetailList.forEach((e) => (e.addId = this.addId++))
    this.costCenterConfig[0].grid.dataSource = this.info.costCenterList
    this.assessmentIndexConfig[0].grid.dataSource = this.info.standDetailList
    this.purchaseAttachmentConfig[0].grid.dataSource = this.info.attachmentList
    this.templateText = this.info.reasonDesc
  },
  data() {
    return {
      costCenterNowEditRowFlag: '', //当前编辑的行id
      costCenterAddId: '1',
      totalUntaxedPrice: 0,
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      templateText: null,
      selectedOtherInfo: {},
      selectedAssessIndexOtherInfo: {},
      isInner: '',
      booleanList: [
        { text: this.$t('否'), value: false },
        { text: this.$t('是'), value: true }
      ],
      isPerPublish: false,
      referenceList: [],
      currencyList: [],
      basicExpand: true,
      handleAssessExpand: true,
      costCenterExpand: true,
      assessIndexExpand: true,
      reasonExpand: true,
      purAttachExpand: true,
      basicInfo: {},
      formInfo: {},
      rules: {
        currencyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        feedbackEndTime: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        offlineEnsure: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      handleClaimList: [
        {
          text: this.$t('维持原判'),
          value: 0
        },
        {
          text: this.$t('减免金额'),
          value: 1
        },
        {
          text: this.$t('改判指标'),
          value: 2
        },
        {
          text: this.$t('取消考核'),
          value: 3
        }
      ],
      costCenterConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar  endEdit
            tools: [['Add', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            lineIndex: 1,
            columnData: costCenterColumn,
            height: 200,
            dataSource: []
          }
        }
      ],
      assessmentIndexConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            lineIndex: 1,
            columnData: assessmentIndexColumn,
            height: 200,
            dataSource: []
          }
        }
      ],
      purchaseAttachmentConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Edit', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 200,
            dataSource: []
          }
        }
      ]
    }
  },
  created() {
    if (this.queryType != 0) {
      delete this.costCenterConfig[0].toolbar
      delete this.assessmentIndexConfig[0].toolbar
      delete this.purchaseAttachmentConfig[0].toolbar
    }
  },
  provide() {
    return {
      assessIndexInfo: this.selectedAssessIndexOtherInfo
    }
  },
  methods: {
    listAvailableClaim() {
      this.$API.assessManage.listAvailableClaim().then((res) => {
        this.referenceList = res.data
      })
    },
    changeCurrencyCode(e) {
      if (e.value) {
        this.basicInfo.currencyName = e.itemData.currencyName
      }
    },
    changeDealAppeal() {},
    costCenterSelectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedOtherInfo, val.itemInfo || {})
      console.log(this.selectedOtherInfo, 'costCenterSelectedChanged最新的额外数据导入的')
    },
    assessIndexSelectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedAssessIndexOtherInfo, val.itemInfo || {})
      console.log(
        this.selectedAssessIndexOtherInfo,
        'assessIndexSelectedChanged最新的额外数据导入的'
      )
    },
    costCenterActionBegin(args) {
      let { data, requestType } = args
      // 行内数据新增
      if (requestType == 'Add') {
        args.data.costCenterAddId = this.costCenterAddId++
        this.costCenterNowEditRowFlag = args.data.costCenterAddId
      } else if (requestType == 'beginEdit') {
        this.costCenterNowEditRowFlag = args.rowData.costCenterAddId
        Object.assign(this.selectedAssessIndexOtherInfo, {
          totalUntaxedPrice: this.totalUntaxedPrice
        })
      } else if (requestType == 'save') {
        data.isInner = this.isInner
      }
    },
    assessIndexActionBegin(args) {
      let { data, requestType } = args
      console.log('assessIndexActionBegin=', args)
      // 行内数据新增
      if (requestType == 'Add') {
        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      } else if (requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        let _tempData = cloneDeep(args)
        Object.assign(this.selectedAssessIndexOtherInfo, {
          taxInclusive: _tempData.rowData.taxInclusive,
          taxRate: _tempData.rowData.taxRate,
          taxedPrice: _tempData.rowData.taxedPrice
        })
      } else if (requestType == 'save') {
        data.isInner = this.isInner
      }
    },
    costCenterActionComplete(item) {
      let { data, requestType } = item
      if (requestType == 'save') {
        // 验证必输
        if (!data.costCenterCode || !data.shareRatio) {
          this.$toast({
            content: this.$t('有字段未输入'),
            type: 'warning'
          })
        }
      }
      let row = this.getCostCenterRow()
      console.log('actionComplete', item, row)
      this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    assessIndexActionComplete(item) {
      let { requestType } = item
      if (requestType == 'save') {
        // 验证必输
        // if (!data.costCenterCode || !data.shareRatio || !data.untaxedPrice) {
        //   this.$toast({
        //     content: this.$t("有字段未输入"),
        //     type: "warning",
        //   });
        // }
      }
      let row = this.getAssessIndexRow()
      console.log('actionComplete', item, row)
      this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    getCostCenterRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.costCenterTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.costCenterAddId == this.costCenterNowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    getAssessIndexRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.assessIndexTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedAssessIndexOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        console.log('getAssessIndexRow=', item.addId, this.nowEditRowFlag)
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      let _tempNum = 0
      this.totalUntaxedPrice = 0
      for (let i = 0; i < currentRecords.length; i++) {
        if (currentRecords[i].taxInclusive && currentRecords[i]?.taxedPrice) {
          _tempNum = _tempNum + Number(currentRecords[i].taxedPrice)
          this.totalUntaxedPrice = this.totalUntaxedPrice + Number(currentRecords[i].untaxedPrice)
        } else if (!currentRecords[i].taxInclusive && currentRecords[i]?.untaxedPrice) {
          _tempNum = _tempNum + Number(currentRecords[i].untaxedPrice)
          this.totalUntaxedPrice = this.totalUntaxedPrice + Number(currentRecords[i].untaxedPrice)
        }
      }
      this.basicInfo.claimTotalAmount = _tempNum
      let currentCostCenterRecords = this.$refs.costCenterTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      if (currentCostCenterRecords.length > 0) {
        currentCostCenterRecords.forEach((e) => {
          if (e.shareRatio) {
            e.untaxedPrice = (
              (Number(this.totalUntaxedPrice) * Number(e.shareRatio)) /
              100
            ).toFixed(2)
          }
        })
      }
      this.$set(this.costCenterConfig[0].grid, 'dataSource', currentCostCenterRecords)
      return info
    },
    handleClickCostCenterToolBar(args) {
      const { toolbar, grid } = args
      console.log('handleClickCostCenterToolBar=', this, args)
      if (toolbar.id == 'Add') {
        // 新增
        this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'Delete') {
        if (this.type == 'edit') {
          this.$toast({
            content: this.$t('编辑状态下不可删除行数据'),
            type: 'warning'
          })
        } else {
          // 选中的数据
          let selectedRecords = grid.getSelectedRecords()
          if (selectedRecords.length > 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
              }
            })
          }
        }
      } else if (toolbar.id == 'endEdit') {
        this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    handleClickAssessToolBar(args) {
      const { toolbar, grid } = args
      console.log('handleClickAssessToolBar=', this, args)
      if (toolbar.id == 'Add') {
        // 新增
        this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'Delete') {
        if (this.type == 'edit') {
          this.$toast({
            content: this.$t('编辑状态下不可删除行数据'),
            type: 'warning'
          })
        } else {
          // 选中的数据
          let selectedRecords = grid.getSelectedRecords()
          if (selectedRecords.length > 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                this.$refs.assessIndexTemplateRef
                  .getCurrentUsefulRef()
                  .gridRef.ejsRef.deleteRecord()
              }
            })
          } else {
            this.$toast({
              content: this.$t('请选择数据'),
              type: 'warning'
            })
          }
        }
      } else if (toolbar.id == 'endEdit') {
        this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    getCurrencyList() {
      this.$API.masterData.queryAllCurrency().then((res) => {
        this.currencyList = res.data
      })
    },
    handleClickPurchaseAttachmentToolBar(e) {
      let sltList = e.grid.getSelectedRecords()
      let selectedRowIndexes = e.grid.getSelectedRowIndexes()
      if ((!sltList || sltList.length <= 0) && e.toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      console.log('handleClickPurchaseAttachmentToolBar=', e)
      if (e.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./editComponents/uploadDialog.vue'),
          data: {
            fileData: [],
            isView: false, // 是否为预览
            required: false, // 是否必须
            title: this.$t('新增采方附件')
          },
          success: (res) => {
            this.handleUploadFiles(res)
          }
        })
      } else if (e.toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./editComponents/uploadDialog.vue'),
          data: {
            title: this.$t('编辑采方附件'),
            isEdit: true,
            info: {
              ...sltList[0],
              fileName: sltList[0].attachmentName,
              fileSize: sltList[0].attachmentSize,
              url: sltList[0].attachmentUrl
            },
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _tempData)
            this.$refs.attachmentRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id == 'Delete') {
        let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
        let _fileIds = sltList.map((x) => x.id)
        let _newData = _tempData.filter((element) => !_fileIds.includes(element.id))
        this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _newData)
      }
    },
    handleUploadFiles(data) {
      console.log('handleUploadFiles=', data)
      let _tempData = {
        ...data,
        attachmentId: data.id,
        attachmentName: data.fileName,
        attachmentSize: data.fileSize,
        attachmentUrl: data.url
      }
      this.purchaseAttachmentConfig[0].grid.dataSource.push(_tempData)
    }
  }
}
</script>
<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
/deep/ .mt-rich-text-editor {
  margin: 30px 10px 0 10px;
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-toolbar-items {
    margin-left: -59px;
  }
  .e-rte-content {
    height: 300px !important;
  }
}
.mb-30 {
  margin-bottom: 30px;
}
</style>
