<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !supplierFeedbackExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('供方反馈') }}</div>
      <div class="sort-box" @click="supplierFeedbackExpand = !supplierFeedbackExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="supplierFeedbackExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="formInfo" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('是否申诉')"
            label-style="top"
            prop="claimTypeCode"
          >
            <mt-select
              :disabled="true"
              v-model="formInfo.claimTypeCode"
              :data-source="claimTypeList"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择')"
              :fields="{ text: 'typeName', value: 'typeCode' }"
              @change="changeclaimType"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('申诉内容')"
            label-style="top"
            prop="claimTypeCode"
          >
            <mt-select
              :disabled="true"
              v-model="formInfo.claimTypeCode"
              :data-source="claimTypeList"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择')"
              :fields="{ text: 'typeName', value: 'typeCode' }"
              @change="changeclaimType"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item class="form-item full-width" :label="$t('决议说明')" label-style="top">
            <mt-input
              :disabled="true"
              v-model="formInfo.remark"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !supplierAttachmentExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('供方附件') }}</div>
      <div class="sort-box" @click="supplierAttachmentExpand = !supplierAttachmentExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="supplierAttachmentExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="attachmentRef"
          :template-config="supplierAttachmentConfig"
          @handleClickToolBar="clickSupAttachmentToolBar"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>

<script>
import { attachmentColumn, editSettings } from './config/index'
export default {
  components: {},
  props: {},
  data() {
    return {
      formInfo: {},
      supplierFeedbackExpand: true,
      supplierAttachmentExpand: true,
      rules: {},
      claimTypeList: [],
      supplierAttachmentConfig: [
        {
          useToolTemplate: false,
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 200,
            dataSource: []
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    clickSupAttachmentToolBar() {},
    changeclaimType() {}
  }
}
</script>

<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
</style>
