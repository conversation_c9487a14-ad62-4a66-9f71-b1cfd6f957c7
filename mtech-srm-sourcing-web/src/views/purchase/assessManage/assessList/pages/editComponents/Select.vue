<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
import bus from '@/utils/bus'
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      costCenterCode: '',
      isDisabled: false,
      siteName: ''
    }
  },
  async mounted() {
    if (this.data.column.field === 'costCenterCode') {
      bus.$emit('costCenterCodeChange', this.data.costCenterCode)
      //成本中心下拉
      this.getCostCenter()
      this.fields = { text: 'costCenterCode', value: 'costCenterCode' }
    }
    if (this.data.column.field === 'taxInclusiveName') {
      // bus.$emit("taxInclusiveNameChange", this.data.taxInclusiveName);
      this.dataSource = [
        { label: this.$t('否'), value: false },
        { label: this.$t('是'), value: true }
      ]
      this.fields = { text: 'label', value: 'label' }
    }
    if (this.data.column.field === 'unitName' || this.data.column.field === 'refResUnitName') {
      this.getUnitList()
      this.fields = { text: 'unitName', value: 'unitName' }
    }
    if (this.data.column.field === 'taxTypeName') {
      console.log('this.data.taxInclusiveName', this.data)
      if (!this.data.taxInclusive) {
        this.isDisabled = true
      }
      this.$bus.$on('taxInclusiveNameChange', (val) => {
        console.log('taxInclusiveNameChange=', val)
        this.isDisabled = !val.itemData.value
        if (!val.itemData.value) {
          this.data.taxTypeName = null
        }
      })
      this.getTaxItemList()
      this.fields = { text: 'taxItemName', value: 'taxItemName' }
    }
    if (this.data.column.field === 'standCode') {
      this.getAssessIndexList()
      this.fields = { text: 'claimTypeCode', value: 'claimTypeCode' }
    }
    if (this.data.column.field === 'siteName') {
      this.getFactoryList()
      this.fields = { text: 'siteName', value: 'siteName' }
    }
  },
  methods: {
    getFactoryList() {
      this.$API.masterData
        .getSiteList({
          condition: '',
          page: {
            current: 1,
            size: 1000
          },
          pageFlag: false
        })
        .then((res) => {
          this.dataSource = res.data.records
        })
    },
    getAssessIndexList() {
      this.$API.assessManage.listAvailableClaimStand().then((res) => {
        this.dataSource = res.data
      })
    },
    getTaxItemList() {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        this.dataSource = res.data
      })
    },
    getUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.dataSource = res.data.records
      })
    },
    async getCostCenter() {
      this.$store.commit('startLoading')
      await this.$API.masterData
        .postCostCenterCriteriaQuery()
        .then((res) => {
          this.$store.commit('endLoading')
          this.dataSource = res.data
        })
        .catch((err) => {
          this.$store.commit('endLoading')
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    serchText(val) {
      console.log('搜索值', val, this.data.column.field)
      val.updateData(this.dataSource.filter((e) => e[this.fields.value].includes(val.text)))
    },
    startOpen() {
      // // 成本中心
      // if (this.data.column.field === "costCenterCode") {
      //   console.log("startOpen=", this);
      //   if (!!this.data.companyId) {
      //     this.getCostCenter();
      //   }
      // }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log('val', val, this.data)
      if (this.data.column.field === 'costCenterCode') {
        bus.$emit('costCenterCodeChange', val) //传给描述
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'costCenterCode',
          itemInfo: {
            costCenterDesc: val.itemData.costCenterDesc
            // costCenterDesc: "val.itemData.costCenterDesc",
          }
        })
        bus.$emit('costCenterDescChange', val.itemData.costCenterDesc)
      }
      if (this.data.column.field === 'standCode') {
        bus.$emit('standCodeChange', val) //传给描述
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'standCode',
          itemInfo: {
            standDesc: val.itemData.remark,
            standName: val.itemData.claimTypeName
          }
        })
        bus.$emit('standNameChange', val.itemData.claimTypeName)
        bus.$emit('standDescChange', val.itemData.remark)
      }
      if (this.data.column.field === 'taxInclusiveName') {
        bus.$emit('taxInclusiveNameChange', val)
        if (!val.itemData.value) {
          bus.$emit('taxAmountChange', null)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxInclusiveName',
            itemInfo: {
              taxInclusive: val.itemData.value,
              taxAmount: null,
              taxedPrice: null
            }
          })
        } else {
          bus.$emit('taxAmountChange', null)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxInclusiveName',
            itemInfo: {
              taxInclusive: val.itemData.value,
              taxAmount: null,
              untaxedPrice: null
            }
          })
        }
      }
      if (this.data.column.field === 'taxTypeName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'taxTypeName',
          itemInfo: {
            taxTypeId: val.itemData.id,
            taxTypeCode: val.itemData.taxItemCode,
            taxRate: val.itemData.taxRate
          }
        })
        bus.$emit('taxedPriceChange', val)
      }
      if (this.data.column.field === 'siteName') {
        sessionStorage.setItem('organizationId', val.itemData.id)
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'siteName',
          itemInfo: {
            siteId: val.itemData.id,
            siteCode: val.itemData.siteCode
          }
        })
      }
      if (this.data.column.field === 'unitName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'unitName',
          itemInfo: {
            unitCode: val.itemData.unitCode
          }
        })
      }
      if (this.data.column.field === 'refResUnitName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'refResUnitName',
          itemInfo: {
            refResUnitCode: val.itemData.unitCode
          }
        })
      }
    }
  }
}
</script>
