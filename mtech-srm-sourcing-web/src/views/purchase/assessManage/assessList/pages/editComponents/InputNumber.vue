<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <mt-inputNumber
      v-model="data[data.column.field]"
      :min="min"
      :max="max"
      :disabled="isDisabled"
      :step="1"
      precision="3"
      :show-clear-button="false"
      @input="numberChange"
    ></mt-inputNumber>
  </div>
</template>
<script>
// var bigDecimal = require("js-big-decimal");
export default {
  inject: ['assessIndexInfo'],
  data() {
    return {
      data: {},
      min: 0,
      max: null,
      stockQuantity: 0,
      isDisabled: false
    }
  },
  mounted() {
    if (this.data.column.field === 'shareRatio') {
      this.max = 100
    }
    if (this.data.column.field === 'taxedPrice') {
      this.$bus.$emit('taxedPriceChange', this.data.taxedPrice)
      if (!this.data.taxInclusive) {
        this.isDisabled = true
      }
      this.$bus.$on('taxInclusiveNameChange', (val) => {
        this.isDisabled = !val.itemData.value
        if (!val.itemData.value) {
          this.data.taxedPrice = null
        }
        console.log('接受的单位', this.data, val)
      }) //接受的单位
    }
    if (this.data.column.field === 'untaxedPrice') {
      if (this.data.taxInclusive) {
        this.isDisabled = true
      }
      this.$bus.$on('taxInclusiveNameChange', (val) => {
        this.isDisabled = val.itemData.value
        if (val.itemData.value) {
          this.data.untaxedPrice = null
        }
        console.log('接受的单位', this.data, val)
      })
      this.$bus.$on('taxedPriceChange', (val) => {
        console.log('taxedPriceChange=', this.data, this.assessIndexInfo, val)
        if (
          this.assessIndexInfo.taxInclusive &&
          this.assessIndexInfo.taxRate &&
          this.assessIndexInfo.taxedPrice
        ) {
          let _taxedPrice = this.assessIndexInfo.taxedPrice
          let _taxRate = this.assessIndexInfo.taxRate
          let _untaxedPrice = _taxedPrice / (1 + _taxRate)
          this.data.untaxedPrice = _untaxedPrice.toFixed(2)
          this.data.taxAmount = _taxedPrice - this.data.untaxedPrice
          this.$bus.$emit('taxAmountChange', this.data.taxAmount.toFixed(2))
        }
      })
    }
  },
  methods: {
    numberChange(val) {
      if (this.data.column.field === 'shareRatio') {
        console.log('numberChange-shareRatio=', val)
        let _tempNum = ((this.assessIndexInfo.totalUntaxedPrice * val) / 100).toFixed(2)
        this.$bus.$emit('untaxedPriceChange', _tempNum)
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'shareRatio',
          itemInfo: {
            untaxedPrice: _tempNum
          }
        })
      }
      if (this.data.column.field === 'untaxedPrice') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'untaxedPrice',
          itemInfo: {
            untaxedPrice: val
          }
        })
      }
      if (this.data.column.field === 'taxedPrice') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'taxedPrice',
          itemInfo: {
            taxedPrice: val
          }
        })
        this.$bus.$emit('taxedPriceChange', val)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.mt-input-number {
  margin-bottom: 0px;
}
</style>
