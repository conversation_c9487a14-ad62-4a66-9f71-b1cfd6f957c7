<template>
  <mt-dialog ref="addDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form
        ref="formInfo"
        class="form-box"
        :model="formInfo"
        :rules="rules"
        :auto-complete="false"
      >
        <mt-form-item
          class="form-item"
          :label="$t('考核类型')"
          label-style="top"
          prop="claimTypeCode"
        >
          <mt-select
            v-model="formInfo.claimTypeCode"
            :data-source="claimTypeList"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'typeName', value: 'typeCode' }"
            @change="changeclaimType"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('考核月份')" label-style="top" prop="claimMonth">
          <mt-select
            v-model="formInfo.claimMonth"
            :placeholder="$t('请输入')"
            :data-source="months"
            :maxlength="30"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('所属公司')"
          label-style="top"
          prop="companyCode"
        >
          <mt-select
            v-model="formInfo.companyCode"
            :show-clear-button="true"
            :data-source="companyList"
            :placeholder="$t('请选择')"
            @change="companyChange"
            :fields="{ text: 'orgName', value: 'orgCode' }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('供应商')" label-style="top" prop="supplierCode">
          <mt-select
            v-model="formInfo.supplierCode"
            :data-source="supplierList"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            @change="supplierChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('考核品类')" label-style="top" prop="itemCode">
          <mt-select
            v-model="formInfo.itemCode"
            :data-source="itemList"
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            @change="itemChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('协议书模板')"
          label-style="top"
          prop="agreementCode"
        >
          <mt-select
            v-model="formInfo.agreementCode"
            :data-source="claimAgreeTempList"
            :placeholder="$t('请选择')"
            :fields="{ text: 'agreementName', value: 'agreementCode' }"
            @change="agreementChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('考核单描述')"
          label-style="top"
          prop="claimDesc"
        >
          <mt-input
            v-model="formInfo.claimDesc"
            :placeholder="$t('请输入')"
            float-label-type="Never"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      formInfo: {},
      claimTypeList: [],
      claimAgreeTempList: [],
      supplierList: [],
      companyList: [],
      itemList: [],
      months: [
        { text: '1', value: 1 },
        { text: '2', value: 2 },
        { text: '3', value: 3 },
        { text: '4', value: 4 },
        { text: '5', value: 5 },
        { text: '6', value: 6 },
        { text: '7', value: 7 },
        { text: '8', value: 8 },
        { text: '9', value: 9 },
        { text: '10', value: 10 },
        { text: '11', value: 11 },
        { text: '12', value: 12 }
      ],
      rules: {
        claimTypeCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        claimMonth: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        claimDesc: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        itemCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        agreementCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmAndEnter,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    this.initData()
  },
  methods: {
    show() {
      this.$refs['addDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['addDialog'].ejsRef.hide()
    },

    async initData() {
      await this.getOrgs()
      await this.getAvailableClaimType()
      await this.getCategoryList()
      await this.listClaimAgreeTemp()
      if (this.isEdit) {
        this.$API.assessManage
          .detailSuperficialClaim({
            id: this.info.id
          })
          .then((res) => {
            if (res.code == 200) {
              this.$nextTick(() => {
                this.formInfo = { ...res.data }
              })
            }
          })
      }
      this.show()
    },
    // 获取品类列表
    getCategoryList() {
      this.$API.masterData
        .getCategoryListByLevel({
          level: 1
        })
        .then((res) => {
          this.itemList = res.data
        })
    },
    //  获取供应商列表
    getSupplierList(param) {
      this.$API.masterData.getSupplierList(param).then((res) => {
        this.supplierList = res.data
      })
    },

    //获取公司
    async getOrgs() {
      await this.$API.masterData.findSpecifiedChildrenLevelOrgs().then((res) => {
        this.companyList = res.data
      })
    },
    async getAvailableClaimType() {
      await this.$API.assessManage.getAvailableClaimType().then((res) => {
        this.claimTypeList = res.data
      })
    },
    async listClaimAgreeTemp() {
      await this.$API.assessManage.listClaimAgreeTemp().then((res) => {
        this.claimAgreeTempList = res.data
      })
    },
    agreementChange(e) {
      if (e.value) {
        this.formInfo.agreementId = e.itemData.id
        this.formInfo.agreementName = e.itemData.agreementName
      }
    },
    itemChange(e) {
      if (e.value) {
        this.formInfo.itemId = e.itemData.id
        this.formInfo.itemName = e.itemData.categoryName
      }
    },
    changeclaimType(e) {
      console.log('changeclaimType=', e)
      if (e.value) {
        this.formInfo.claimTypeId = e.itemData.id
        this.formInfo.claimTypeName = e.itemData.typeName
      }
    },
    companyChange(e) {
      console.log('companyChange=', e)
      if (e.value) {
        this.formInfo.companyId = e.itemData.id
        this.formInfo.companyName = e.itemData.orgName
        this.getSupplierList({ organizationCode: e.value })
      }
    },
    supplierChange(e) {
      this.formInfo.supplierName = e.itemData.supplierName
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$API.assessManage
            .saveClaim({ ...this.formInfo, createType: 0 })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    confirmAndEnter() {},
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
}
</style>
