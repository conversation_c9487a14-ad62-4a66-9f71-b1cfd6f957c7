import Vue from 'vue'
import { i18n } from '@/main.js'
import Select from '../components/editComponents/Select.vue'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'typeCode',
    headerText: i18n.t('考核类型编码'),
    cellTools: ['edit', 'delete']
  },
  {
    field: 'typeName',
    headerText: i18n.t('考核类型名称')
  },
  {
    field: 'companyStr',
    headerText: i18n.t('所属公司')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('新建'), 1: i18n.t('启用'), '-1': i18n.t('停用') }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data.status != 1
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data.status == 1
        }
      }
    ]
  },
  {
    field: 'allowAppeal',
    headerText: i18n.t('是否允许申诉'),
    valueConverter: {
      type: 'map',
      map: { false: i18n.t('否'), true: i18n.t('是') }
    }
  },
  {
    field: 'appealDays',
    headerText: i18n.t('申诉处理时长/天')
  },
  {
    field: 'autoEnsure',
    headerText: i18n.t('是否超期自动确认'),
    valueConverter: {
      type: 'map',
      map: { false: i18n.t('否'), true: i18n.t('是') }
    }
  },
  {
    field: 'feedbackDays',
    headerText: i18n.t('默认反馈时长/天')
  },
  {
    field: 'allowReverse',
    headerText: i18n.t('是否允许冲销'),
    valueConverter: {
      type: 'map',
      map: { false: i18n.t('否'), true: i18n.t('是') }
    }
  },
  {
    field: 'allowWithhold',
    headerText: i18n.t('是否允许预扣'),
    valueConverter: {
      type: 'map',
      map: { false: i18n.t('否'), true: i18n.t('是') }
    }
  },
  {
    field: 'currencyName',
    headerText: i18n.t('申诉仲裁人'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="showDetail">${i18n.t(
            '申诉仲裁人维护'
          )}</a>`,
          data() {
            return { data: {} }
          },
          methods: {
            showDetail() {
              this.$dialog({
                modal: () => import('../components/arbitratorList.vue'),
                data: {
                  title: i18n.t('申诉仲裁人维护'),
                  id: this.data.id
                }
              })
            }
          }
        })
      }
    }
  }
]

export const arbitratorColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'companyId',
    headerText: i18n.t('所属公司'),
    visible: false,
    allowEditing: false
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'siteId',
    headerText: i18n.t('工厂'),
    visible: false,
    allowEditing: false
  },
  {
    field: 'admOrgName',
    headerText: i18n.t('行政组织'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'admOrgId',
    headerText: i18n.t('行政组织'),
    visible: false,
    allowEditing: false
  },
  {
    field: 'employeeName',
    headerText: i18n.t('仲裁人'),
    editTemplate: () => {
      return { template: Select }
    },
    width: 200
  }
]
