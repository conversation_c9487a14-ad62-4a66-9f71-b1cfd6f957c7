import Vue from 'vue'
import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export let claimTypeList = []
export let taxItemList = []
export let assessmentIndexData = []
export let costCenterData = []
export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}
// 列表是否可编辑
export let editFlag = {
  appealDeal: '-1'
}

export const costCenterColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'costCenterCode',
    headerText: i18n.t('成本中心代码')
  },
  {
    field: 'costCenterDesc',
    headerText: i18n.t('成本中心描述')
  },
  {
    field: 'shareRatio',
    headerText: i18n.t('分摊比例（%）')
  },
  {
    field: 'untaxedPrice',
    headerText: i18n.t('不含税金额')
  }
]

export const assessmentIndexColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'standCode',
    headerText: i18n.t('考核指标代码'),
    width: '255',
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-select
          width="100%"
          :data-source="claimTypeList"
          placeholder="请选择"
          @change="businessTypeChange"
          @focus="getAssessIndexList"
          css-class="input-select"
          :disabled="!isEditable"
          :fields="{ text: 'claimTypeCode', value: 'claimTypeCode' }"
          v-model="data.standCode"
        ></mt-select>`,
          data() {
            return { data: {}, claimTypeList: [], isEditable: null }
          },
          created() {
            this.claimTypeList = claimTypeList
          },
          mounted() {
            this.isEditable = false
            console.log('standCode===', claimTypeList, this.data.standCode)
            if (editFlag.appealDeal == 2) {
              this.isEditable = true
            }

            // console.log("====", this.data, this.isEditable);
          },
          methods: {
            // 获取考核指标下拉
            getAssessIndexList() {
              this.$API.assessManage.listAvailableClaimStand().then((res) => {
                this.claimTypeList.length = 0
                res.data.forEach((e) => this.claimTypeList.push(e))
              })
            },
            businessTypeChange() {
              // orderStrategySpecData[this.data.index].businessTypeCode =
              //   e.itemData.businessTypeCode;
              // orderStrategySpecData[this.data.index].businessTypeId =
              //   e.itemData.businessTypeId;
              // orderStrategySpecData[this.data.index].businessTypeName =
              //   e.itemData.businessTypeName;
              // orderStrategyDataSource[this.data.index].purExecutionMethod =
              //   e.value;
            }
          }
        })
      }
    }
  },
  {
    field: 'standName',
    headerText: i18n.t('考核指标描述')
  },
  {
    field: 'standDesc',
    headerText: i18n.t('考核指标说明')
  },
  {
    field: 'happenTime',
    headerText: i18n.t('发生时间')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    field: 'unitPrice',
    headerText: i18n.t('单价')
  },
  {
    field: 'claimDesc',
    headerText: i18n.t('考核说明')
  },
  {
    field: 'taxInclusiveName',
    headerText: i18n.t('是否含税'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-select
          width="100%"
          :data-source="booleanList"
          @change="taxInclusiveChange"
          css-class="input-select"
          :disabled="!isEditable"
          v-model="data.taxInclusiveName"
        ></mt-select>`,
          data() {
            return {
              data: {},
              booleanList: [
                { text: i18n.t('否'), value: '否' },
                { text: i18n.t('是'), value: '是' }
              ],
              isEditable: null
            }
          },
          mounted() {
            this.isEditable = false
            // this.businessTypeList = businessTypeList;
            if (editFlag.appealDeal == 1 || editFlag.appealDeal == 2) {
              this.isEditable = true
            }

            // console.log("====", this.data, this.isEditable);
          },
          methods: {
            taxInclusiveChange(e) {
              this.data.taxInclusive = e.value
              assessmentIndexData[this.data.index].taxInclusiveName = e.value
              assessmentIndexData[this.data.index].taxInclusive = e.value == '是' ? true : false
              if (e.value == '是') {
                assessmentIndexData[this.data.index].taxInclusive = true
                assessmentIndexData[this.data.index].untaxedPrice = null
              } else {
                assessmentIndexData[this.data.index].taxInclusive = false
                assessmentIndexData[this.data.index].taxedPrice = null
                assessmentIndexData[this.data.index].taxTypeCode = null
                assessmentIndexData[this.data.index].taxAmount = null
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'taxTypeCode',
    headerText: i18n.t('税率'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-select
          width="100%"
          :data-source="taxItemList"
          placeholder="请选择"
          @change="taxTypeCodeChange"
          @focus="getTaxItemList"
          css-class="input-select"
          :disabled="!isEditable"
          :fields="{ text: 'taxItemName', value: 'taxItemCode' }"
          v-model="data.taxTypeCode"
        ></mt-select>`,
          data() {
            return { data: {}, taxItemList: [], isEditable: null }
          },
          created() {
            this.taxItemList = taxItemList
          },
          mounted() {
            this.isEditable = false
            if ((editFlag.appealDeal == 1 || editFlag.appealDeal == 2) && this.data.taxInclusive) {
              this.isEditable = true
            }
            // console.log("====", this.data, this.isEditable);
          },
          methods: {
            // 获取税率下拉
            getTaxItemList() {
              this.$API.masterData.queryAllTaxItem().then((res) => {
                this.taxItemList = res.data
              })
            },
            taxTypeCodeChange(e) {
              console.log('taxTypeCodeChange=', e)
              assessmentIndexData[this.data.index].taxTypeCode = e.value
              assessmentIndexData[this.data.index].taxRate = e.itemData.taxRate
              if (assessmentIndexData[this.data.index].taxedPrice) {
                assessmentIndexData[this.data.index].untaxedPrice = (
                  assessmentIndexData[this.data.index].taxedPrice /
                  (1 + e.itemData.taxRate)
                ).toFixed(2)
                assessmentIndexData[this.data.index].taxAmount = (
                  assessmentIndexData[this.data.index].taxedPrice -
                  assessmentIndexData[this.data.index].untaxedPrice
                ).toFixed(2)
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'taxAmount',
    headerText: i18n.t('税额')
  },
  {
    field: 'untaxedPrice',
    headerText: i18n.t('不含税金额'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-input-number
          width="100%"
          @change="untaxedPriceChange"
          css-class="input-select"
          :disabled="!isEditable"
          v-model="data.untaxedPrice"
        ></mt-input-number>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = false
            // this.businessTypeList = businessTypeList;
            if ((editFlag.appealDeal == 1 || editFlag.appealDeal == 2) && !this.data.taxInclusive) {
              this.isEditable = true
            }

            // console.log("====", this.data, this.isEditable);
          },
          methods: {
            untaxedPriceChange() {}
          }
        })
      }
    }
  },
  {
    field: 'taxedPrice',
    headerText: i18n.t('含税金额'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-input-number
          width="100%"
          @change="taxedPriceChange"
          css-class="input-select"
          :disabled="!isEditable"
          v-model="data.taxedPrice"
        ></mt-input-number>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = false
            // this.businessTypeList = businessTypeList;
            if ((editFlag.appealDeal == 1 || editFlag.appealDeal == 2) && this.data.taxInclusive) {
              this.isEditable = true
            }

            // console.log("====", this.data, this.isEditable);
          },
          methods: {
            taxedPriceChange(e) {
              console.log('taxedPriceChange=', e)
              assessmentIndexData[this.data.index].untaxedPrice = (
                e /
                (1 + assessmentIndexData[this.data.index].taxRate)
              ).toFixed(2)
              assessmentIndexData[this.data.index].taxAmount = (
                e - assessmentIndexData[this.data.index].untaxedPrice
              ).toFixed(2)
            }
          }
        })
      }
    }
  },
  {
    field: 'refRes',
    headerText: i18n.t('联带物品')
  },
  {
    field: 'refResUnitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'refResQuantity',
    headerText: i18n.t('联带数量')
  },
  {
    field: 'refAmount',
    headerText: i18n.t('联带金额')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const attachmentColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'attachmentName',
    headerText: i18n.t('附件名称'),
    cssClass: '',
    cellTools: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  },
  {
    field: 'attachmentSize',
    headerText: i18n.t('附件大小')
  },
  {
    field: 'uploadUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        console.log('uploadTime=', e)
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
