<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { appealDealCols } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'deal',
                  icon: 'icon_table_new',
                  title: this.$t('申诉处理')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: appealDealCols,
            frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claim/pageClaim',
              params: { type: 2 }
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'deal') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能处理一行'), type: 'warning' })
          return
        } else {
          this.$router.push({
            name: 'purchase-assessmanage-appealDealDetail',
            query: {
              type: 'edit',
              id: records[0].id
            }
          })
        }
      }
    },
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'purchase-assessmanage-appealDealDetail',
        query: {
          type: data.status,
          id: data.id
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
