<template>
  <div class="register-box">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>
<script>
import { registColumn } from './config/column'
export default {
  data() {
    return {
      approvalRemark: '',
      selectData: {},
      templateConfig: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'Add', icon: 'icon_table_new', title: this.$t('新增') },
                { id: 'Edit', icon: 'icon_table_edit', title: this.$t('编辑') },
                {
                  id: 'Delete',
                  icon: 'icon_table_delete',
                  title: this.$t('删除')
                },
                {
                  id: 'enable',
                  icon: 'icon_card_invite',
                  title: this.$t('启用')
                },
                {
                  id: 'disable',
                  icon: 'icon_card_invite',
                  title: this.$t('停用')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: registColumn,
            frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claimAgreementTemplate/pageClaimAgreementTemplate'
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      let name = item.toolbar ? item.toolbar.id : item.tool.id
      if (
        records.length <= 0 &&
        !(name == 'Add' || name == 'Filter' || name == 'Refresh' || name == 'Setting')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (name == 'Add') {
        //新增：打开弹窗
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          data: {
            title: this.$t('新增'),
            type: 'add',
            record: []
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          },
          close: () => {}
        })
      } else if (name == 'Edit') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
          return
        } else if (records[0].status === 1) {
          this.$toast({
            content: this.$t('启用状态的数据无法编辑'),
            type: 'warning'
          })
          return
        } else {
          this.editRecord(records[0])
        }
      } else if (name == 'Delete') {
        if (records.some((item) => item.status === 1)) {
          this.$toast({
            content: this.$t('存在处于启用状态的数据，不可删除'),
            type: 'warning'
          })
          return
        } else {
          let _idList = records.map((e) => e.id)
          this.deleteRecord(_idList)
        }
      } else if (item.toolbar.id === 'enable' || item.toolbar.id === 'disable') {
        const toStatus = item.toolbar.id === 'enable' ? 1 : -1
        if (records && records.length) {
          let len = records.length
          let ids = []
          for (let i = 0; i < len; i++) {
            const { id, status } = records[i]
            if (status === toStatus) {
              this.$toast({
                content: `${this.$t('当前已有处于')}${
                  item.toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')
                }${this.$t('状态')}`,
                type: 'warning'
              })
              return
            }
            ids.push(id)
          }

          let params = {
            operationType: toStatus,
            idList: ids
          }
          this.updateStatus(params)
        }
      }
    },
    updateStatus(params) {
      this.$API.assessManage.turnClaimAgreeTempState(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteRecord(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？'),
          confirm: () =>
            this.$API.assessManage.deleteClaimAgreeTemp({
              idList: ids
            })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editRecord(data) {
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('编辑协议书模板'),
          isEdit: true,
          info: data
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool-', e)
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (['edit', 'delete'].includes(tool.id)) {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        if (tool.id === 'edit') {
          this.editRecord(data)
        }
        tool.id === 'delete' && this.deleteRecord([data.id])
      }
      if (['enable', 'disable'].includes(tool.id)) {
        const toStatus = tool.id === 'enable' ? 1 : -1
        let params = {
          operationType: toStatus,
          idList: [data.id]
        }
        this.updateStatus(params)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.register-box {
  width: 100%;
  height: 100%;
}
/deep/.status-label {
  width: 44px;
  height: 20px;
  background: rgba(99, 134, 193, 0.1);
  border-radius: 2px;
  font-size: 12px;
  font-weight: 500;
  color: rgba(99, 134, 193, 1);
}
/deep/ .status-enable {
  width: 44px;
  height: 20px;
  background: rgba(154, 154, 154, 0.1);
  border-radius: 2px;
  font-size: 12px;
  font-weight: 500;
  color: rgba(154, 154, 154, 1);
}
</style>
