<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>

      <div class="slider-content">
        <mt-form
          ref="ruleForm"
          class="ruleForm"
          :model="ruleForm"
          :rules="rules"
          :auto-complete="false"
        >
          <mt-form-item :label="$t('协议书名称')" prop="agreementName">
            <mt-input v-model="ruleForm.agreementName"></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('考核类型')" prop="claimTypeCode">
            <mt-select
              v-model="ruleForm.claimTypeCode"
              :data-source="claimTypeList"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
              :fields="{ text: 'typeName', value: 'typeCode' }"
              @change="changeclaimType"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="company" class="full-width" :label="$t('所属公司')">
            <mt-multi-select
              v-model="ruleForm.company"
              :show-clear-button="true"
              :data-source="companyList"
              :placeholder="$t('请选择')"
              @change="companyChange"
              :fields="{ text: 'orgName', value: 'orgCode' }"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item class="full-width" :label="$t('备注')" prop="remark">
            <mt-input
              v-model="ruleForm.remark"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
        <div class="edit-box">
          <mt-rich-text-editor
            :created="onCreate"
            ref="editorRef"
            :height="600"
            css-class="rich-editor"
            v-model.trim="templateText"
            :toolbar-settings="toolbarSettings"
            :quick-toolbar-settings="quickToolbarSettings"
            @change="changeText"
          >
          </mt-rich-text-editor>
        </div>
      </div>
      <div class="slider-footer mt-flex">
        <span @click="confirm">{{ $t('保存') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { defaultTemp } from '../config/column'
import Vue from 'vue'
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
Vue.use(MtRichTextEditor)
let proxy = undefined

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      templateText: '',
      ruleForm: {
        agreementName: '',
        company: '',
        companyList: [],
        remark: ''
      },
      claimTypeList: [],
      companyList: [],
      rules: {
        agreementName: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        company: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        claimTypeCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      toolbarSettings: {
        items: [
          {
            tooltipText: 'Insert Symbol',
            undo: true,
            click: this.onClick.bind(this),
            template: function () {
              return {
                template: Vue.component('actionOption', {
                  template: `<mt-select v-model="varValue" :dataSource="variableList" id="custom_tbar" :fields="{ value: 'itemCode', text: 'itemName' }" placeholder="变量库"  @select="insertVar" ></mt-select>`,
                  data() {
                    return {
                      data: {},
                      variableList: [],
                      varValue: ''
                    }
                  },
                  mounted() {
                    this.$API.masterData
                      .dictionaryGetList({
                        dictCode: 'ClaimAgreementTemplateVariable'
                      })
                      .then((res) => {
                        this.variableList = res.data
                      })
                  },
                  computed: {},
                  methods: {
                    insertVar(e) {
                      console.log('insertVar==', e, this.data, proxy)
                      proxy.$refs.editorRef.ejsRef.executeCommand(
                        'insertText',
                        e.itemData.itemCode,
                        {
                          undo: true
                        }
                      )
                    }
                  }
                })
              }
            }
          },
          'insertCode',
          'Bold',
          'Italic',
          'Underline',
          'StrikeThrough',
          'FontName',
          'FontSize',
          'FontColor',
          'BackgroundColor',
          'LowerCase',
          'UpperCase',
          'SuperScript',
          'SubScript',
          '|',
          'Formats',
          'Alignments',
          'NumberFormatList',
          'BulletFormatList',
          'Outdent',
          'Indent',
          '|',
          'CreateTable',
          'CreateLink',
          'Image',
          'FileManager',
          '|',
          'ClearFormat',
          'Print',
          'SourceCode',
          'FullScreen',
          '|',
          'Undo',
          'Redo'
        ]
      },
      quickToolbarSettings: {
        table: [
          'TableHeader',
          'TableRows',
          'TableColumns',
          'TableCell',
          '-',
          'BackgroundColor',
          'TableRemove',
          'TableCellVerticalAlign',
          'Styles'
        ]
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  async created() {},
  mounted() {
    this.initData()
  },
  methods: {
    onCreate() {
      console.log('onCreate===')
      proxy = this
    },
    onClick() {
      console.log('onClick==', this.$refs.editorRef)
    },
    async initData() {
      await this.getOrgs()
      await this.getAvailableClaimType()
      if (this.isEdit) {
        this.$API.assessManage
          .detailClaimAgreeTemp({
            id: this.info.id
          })
          .then((res) => {
            if (res.code == 200) {
              this.$nextTick(() => {
                this.ruleForm = {
                  ...res.data,
                  company: res.data.companyList.map((e) => e.companyCode)
                }
              })
              this.templateText = res.data.templateText
            }
          })
      } else {
        this.templateText = defaultTemp
      }
    },
    //获取公司
    async getOrgs() {
      await this.$API.masterData.findSpecifiedChildrenLevelOrgs().then((res) => {
        this.companyList = res.data
      })
    },
    async getAvailableClaimType() {
      await this.$API.assessManage.getAvailableClaimType().then((res) => {
        this.claimTypeList = res.data
      })
    },
    changeclaimType(e) {
      console.log('changeclaimType=', e)
      if (e.value) {
        this.ruleForm.claimTypeId = e.itemData.id
        this.ruleForm.claimTypeName = e.itemData.typeName
      }
    },
    companyChange(e) {
      this.ruleForm.companyList.length = 0
      if (e.value.length > 0) {
        this.companyList.forEach((e) => {
          if (this.ruleForm.company.includes(e.orgCode)) {
            this.ruleForm.companyList.push({
              companyCode: e.orgCode,
              companyName: e.orgName,
              companyId: e.id
            })
          }
        })
        console.log('companyChange=', e, this.ruleForm.companyList)
      }
    },
    changeText(e) {
      console.log('changeText=', e)
      // this.ruleForm.templateText = e.value;
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      console.log(
        'confirm',
        this.templateText,
        this.$refs.editorRef.ejsRef.getContent(),
        this.$refs.editorRef.ejsRef.getHtml()
      )
      if (
        this.$refs.editorRef.ejsRef.getHtml().length > 0 &&
        this.$refs.editorRef.ejsRef.getHtml() != '<p><br></p>'
      ) {
        this.ruleForm.templateText = this.templateText
      } else {
        this.$toast({ content: this.$t('请先添加模板'), type: 'warning' })
        return
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$API.assessManage
            .saveClaimAgreeTemp(this.ruleForm)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px 10px 10px;
}
/deep/ .select-container {
  height: 46px;
  padding: 5px;
}
/deep/ .e-input-group {
  padding-left: 5px;
}
.search-box {
  display: flex;
  border-bottom: 1px solid #e8e8e8;

  .searchInput {
    width: calc(100% - 34px);
    padding-right: 10px;
    /deep/ .e-input-group {
      border: none;
    }
  }
}

.slider-panel-container {
  .slider-content {
    overflow-y: auto;
  }
  .slider-modal {
    width: 850px;
    border: none;
    .slider-header {
      height: 58px;
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }

    .add-rule-group {
      margin-top: 30px;
      height: 14px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      width: 100%;
      text-align: center;
      cursor: pointer;
    }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
.ruleForm {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .fbox {
    display: flex;
  }
  .mt-form-item {
    width: 365px;
  }
  .full-width {
    width: 810px;
  }
}
.edit-box {
  width: 100%;
  background: #fff;
  .setting-banner {
    width: 100%;
    height: 50px;
    cursor: pointer;
    padding: 0 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(79, 91, 109, 1);

    i {
      font-size: 16px;
      line-height: 14px;
      display: inline-block;
      height: 16px;
      width: 16px;
      cursor: pointer;
    }

    span {
      display: inline-block;
      margin-left: 6px;
      cursor: pointer;
    }
  }
  .rich-editor {
    height: 100%;
  }
  .mt-rich-text-editor {
    height: 100%;
  }
}
/deep/ .mt-rich-text-editor {
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    height: 600px !important;
  }
}
</style>
