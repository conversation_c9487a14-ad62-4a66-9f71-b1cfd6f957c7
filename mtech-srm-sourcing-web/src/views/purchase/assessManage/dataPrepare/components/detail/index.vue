// 计划清单详情
<template>
  <div class="full-height">
    <div class="top-info">
      <div class="header">
        <!-- 左侧的信息 -->
        <div class="plan-title">{{ topInfo.planName || '' }}</div>
        <div :class="['status-box', 'status-box' + '-' + topInfo.status]">
          <span v-if="topInfo.status == 10">{{ $t('进行中') }}</span>
          <span v-if="topInfo.status == 15">{{ $t('待提交') }}</span>
          <span v-if="topInfo.status == 20">{{ $t('已提交') }}</span>
          <span v-if="topInfo.status == 30">{{ $t('已驳回') }}</span>
          <span v-if="topInfo.status == 40">{{ $t('已完成') }}</span>
          <span v-if="topInfo.status == 50">{{ $t('已解冻') }}</span>
          <span v-if="topInfo.status == 60">{{ $t('已过期') }}</span>
        </div>

        <div class="middle-blank"></div>

        <!-- 右侧操作按钮 -->
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack(0)">{{
          $t('返回')
        }}</mt-button>
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack(1)" v-if="show">{{
          $t('保存')
        }}</mt-button>
        <!-- <mt-button css-class="e-flat" :is-primary="true" @click="goBack(2)"
          >{{ $t("保存并提交") }}</mt-button> -->
      </div>
      <div class="middle">
        <span>{{ $t('项目编码') }}：{{ topInfo.templateCode }}</span>
        <span>{{ $t('创建人') }}：{{ topInfo.createUserName }}{{ topInfo.createTime }}</span>
        <!-- <span>项目类型：{{analysisList[topInfo.templateType]}}</span> -->
      </div>
      <div class="bottom">
        <span>{{ $t('期号') }}：{{ topInfo.issueNum }}</span>
        <span>{{ $t('提交截止日期') }}:{{ topInfo.submitDeadline }}</span>
        <span>{{ $t('所属组织') }}：{{ topInfo.orgName }}</span>
      </div>
    </div>
    <div style="height: calc(100% - 230px)">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @input="input"
      />
    </div>

    <mt-dialog
      ref="dialog"
      :header="$t('批量打分')"
      css-class="bule-bg"
      :width="900"
      min-height="600"
      :buttons="buttons"
    >
      <div class="uploader-box">
        <mt-form ref="formInfo" :rules="rulesCustom" :model="formInfo">
          <mt-form-item prop="score" :label="$t('批量设置分值')">
            <mt-input
              v-model="formInfo.score"
              type="number"
              @blur="inputblur"
              :placeholder="$t('请输入分值')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="reason" :label="$t('原因')">
            <mt-input
              v-model="formInfo.reason"
              type="text"
              :placeholder="$t('请输入原因')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
        <!--  -->
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { pageConfig } from './config/columns'
import axios from 'axios'
export default {
  // props: {
  //   topInfo: {
  //     type: Object,
  //     default: () => {},
  //   },
  // },
  data() {
    return {
      arr: [],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.piliang,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formInfo: {
        score: 0,
        reason: ''
      },
      pageConfig: [],
      rulesCustom: {
        score: [
          { validator: this.score, trigger: 'blur' },
          { required: true, message: this.$t('请输入分值'), trigger: 'blur' }
        ]
      },
      topInfo: {},
      id: '',
      valueArr: []
    }
  },
  created() {
    this.id = this.$route.query.id
    this.init()
  },
  mounted() {
    // if (this.modalData && this.modalData.data && this.modalData.data.id) {
    //   this.getFormDetail(this.modalData.data.id);
    // }
    // this.getDimensionSelectList();
  },
  computed: {
    show() {
      if (this.topInfo.status == 20 || this.topInfo.status == 40) {
        return false
      } else {
        return true
      }
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'supplierEnterpriseCode') {
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            supplierCode: e.data.supplierEnterpriseCode,
            orgCode: this.topInfo.orgCode
          }
        })
      }
    },
    score(rule, value, callback) {
      let maxValue = 0
      let minValue = 0
      this.topInfo.scoreDetailList.forEach((ele) => {
        if (this.arr.includes(ele.i)) {
          maxValue = ele.maxValue
          minValue = ele.minValue
          return
        }
      })
      if (this.formInfo.score > maxValue) {
        this.formInfo.score = ''
        callback(new Error(this.$t('得分大于最大值，请重新设置')))
        return
      } else if (this.formInfo.score < minValue) {
        this.formInfo.score = ''
        callback(new Error(this.$t('得分小于最小值，请重新设置')))
        return
      } else {
        callback()
      }
    },
    inputblur(e) {
      if (e) {
        let num = e * 1
        this.formInfo.score = num.toFixed(this.topInfo.keepDecimal)

        // this.$parent.$emit('input',this.data.i,'score',this.data.score)
      }
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    piliang() {
      this.$refs['formInfo'].validate((valid) => {
        if (valid) {
          this.topInfo.scoreDetailList.forEach((ele) => {
            if (this.arr.includes(ele.i)) {
              ele.reason = this.formInfo.reason
              ele.score = this.formInfo.score
            }
          })
          this.valueArr.forEach((ele) => {
            if (this.arr.includes(ele.key)) {
              ele.reason = this.formInfo.reason
              ele.score = this.formInfo.score
            }
          })
          this.$refs['dialog'].ejsRef.hide()
        }
      })
    },
    input(index, key, value) {
      this.valueArr.forEach((ele) => {
        if (ele.key == index) {
          // if(key=='reason'){

          // }
          ele[key] = value
        }
      })
      //
      // this.$set(this.topInfo.scoreDetailList[index],key,value)
    },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id == 'dafen') {
        if (_selectGridRecords.length <= 0) {
          this.$toast({ content: this.$t('请选中需批量打分得数据'), type: 'warning' })
          return
        } else {
          let indexCode = _selectGridRecords[0].indexCode
          let indexVersion = _selectGridRecords[0].indexVersion
          let ret = false
          for (let i = 0; i < _selectGridRecords.length; i++) {
            const ele = _selectGridRecords[i]
            if (ele.indexType != '2') {
              this.$toast({ content: this.$t('只可选中指标'), type: 'warning' })
              ret = true
              return
            }
            if (indexCode != ele.indexCode || indexVersion != ele.indexVersion) {
              this.$toast({ content: this.$t('只可选中相同的指标'), type: 'warning' })
              ret = true
              return
            }
          }
          if (ret) {
            return
          }
          this.arr = _selectGridRecords.map((item) => {
            return item.i
          })
          this.formInfo = {
            score: '',
            reason: ''
          }
          this.$refs['dialog'].ejsRef.show()
        }
      } else if (e.toolbar.id == 'import') {
        this.$dialog({
          modal: () => import('./components/Excelimport.vue'),
          data: {
            title: this.$t('导入'),
            archiveId: this.id
          },
          success: (val) => {
            val.forEach((item) => {
              if (item.errorInfo == '') {
                this.topInfo.scoreDetailList.forEach((ele) => {
                  if (
                    item.supplierCode + '-' + item.categoryCode + '-' + item.indexCode ==
                    ele.supplierEnterpriseCode + '-' + ele.categoryCode + '-' + ele.indexCode
                  ) {
                    ele.reason = item.reason
                    ele.score = item.score
                  }
                })
                this.valueArr.forEach((ele) => {
                  if (
                    item.supplierCode + '-' + item.categoryCode + '-' + item.indexCode ==
                    ele.sole
                  ) {
                    ele.reason = item.reason
                    ele.score = item.score
                  }
                })
              }
            })
          }
        })
      } else if (e.toolbar.id == 'download') {
        let params = new FormData()
        params.append('archiveId', this.id)
        axios
          .post(`/api/analysis/tenant/buyer/assess/archive/manual/detail/export`, params, {
            responseType: 'blob'
          })
          .then((res) => {
            const filename = this.$t(`供应商绩效评分模板.xlsx`)
            const url = window.URL.createObjectURL(new Blob([res.data]))
            const link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.download = filename
            document.body.appendChild(link)
            link.click()
            URL.revokeObjectURL(link.href) // 释放URL 对象
            document.body.removeChild(link)
          })
      }
    },
    arrFn(arr = []) {
      arr.forEach((ele) => {
        if (ele.indexLevel == '1') {
          ele['name'] = ele.dimensionName
        } else {
          ele['name'] = ele.indexName
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.arrFn(ele.childrenList)
        }
      })
    },
    init() {
      this.valueArr = []
      this.$API.DataPreparation.manualDetail({ archiveId: this.id }).then((res) => {
        this.topInfo = null
        let arr = { ...res.data }
        if (!arr.scoreDetailList) arr.scoreDetailList = []
        // if(arr.scoreDetailList.length>0){
        //   this.arrFn(arr.scoreDetailList)
        // }
        this.topInfo = JSON.parse(JSON.stringify(arr))
        this.topInfo.scoreDetailList = this.topInfo.scoreDetailList.map((item, i) => {
          item.score = item.scoreStr
          this.valueArr.push({
            key: i,
            reason: item.reason,
            score: item.score,
            sole: item.supplierEnterpriseCode + '-' + item.categoryCode + '-' + item.indexCode
          })
          return { ...item, i: i, keepDecimal: this.topInfo.keepDecimal }
        })
        this.pageConfig = pageConfig()
        this.$nextTick(() => {
          this.pageConfig[0].grid.dataSource = this.topInfo.scoreDetailList
        })
        // this.topInfo=arr
        // this.$refs.template1.init(this.topInfo.itemDTOList)

        // this.currentTabIndex=0
      })
    },
    // 返回按钮
    goBack(num) {
      if (num == 1 || num == 2) {
        this.$store.commit('startLoading')
        let list = []
        this.valueArr.forEach((ele) => {
          let datalist = this.topInfo.scoreDetailList
          if (
            ele.key == datalist[ele.key].i &&
            datalist[ele.key].indexType == '2' &&
            ele.score !== ''
          ) {
            let obj = {
              archiveId: datalist[ele.key].archiveId,
              categoryCode: datalist[ele.key].categoryCode,
              categoryId: datalist[ele.key].categoryId,
              manualId: datalist[ele.key].manualId,
              parentTemplateItemId: datalist[ele.key].parentTemplateItemId,
              raterId: datalist[ele.key].raterId,
              reason: ele.reason,
              score: ele.score,
              scoreDetailId: datalist[ele.key].scoreDetailId,
              supplierEnterpriseId: datalist[ele.key].supplierEnterpriseId,
              templateItemId: datalist[ele.key].templateItemId
            }
            list.push(obj)
          }
        })

        this.$API.DataPreparation.manualDetailsave({
          indexScoreDTOList: list
        }).then((res) => {
          this.$store.commit('endLoading')
          if (res && res.code === 200) {
            this.init()
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          } else {
            this.$toast({
              content: res.msg || this.$t('保存失败，请重试'),
              type: 'error'
            })
          }
        })
      } else {
        this.back()
      }
    },
    digui(arr, data) {
      data.forEach((ele) => {
        if (ele.childrens && ele.childrens.length > 0) {
          this.digui(arr, ele.childrens)
        } else {
          let obj = {
            categoryCode: ele.categoryCode,
            categoryId: ele.id,
            categoryName: ele.categoryName,
            // "id": 0,
            rangeType: this.$refs.template2.rangeType,
            // "remark": "",
            templateId: this.topInfo.id
            // "tenantId": 0
          }
          arr.push(obj)
        }
      })
    },
    back() {
      // this.$router.go(-1)
      this.$router.push({
        path: '/supplier/dataPrepare',
        query: {
          tab: 1
        }
      })
    },
    // 切换Tab页
    handleSelectTab(e) {
      this.currentTabIndex = e
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: calc(100% - 29px);
  display: flex;
  flex-direction: column;
  padding: 0 20px;
  overflow: hidden;
  ::v-deep .detail-content {
    height: 100%;
  }
  ::v-deep .common-template-page .repeat-template {
    height: 100%;
  }
  ::v-deep .grid-container {
    height: calc(100% - 50px);
  }
}
.mt-tabs {
  width: 100%;
  background-color: #fafafa;
}
.plan-title {
  font-size: 20px;
  font-family: PingFangSC;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
}
.middle-blank {
  flex: 1;
}
.top-info {
  width: 100%;
  height: 132px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  display: flex;
  flex-direction: column;
  padding: 26px 0 28px 30px;
  margin: 20px 0 25px 0;
  .status-box {
    line-height: 12px;
    padding: 4px;
    border-radius: 2px;
    margin: 0 28px 0 16px;
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    &-0 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
    &-1 {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
    &-2 {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
  }
  .header {
    width: 100%;
    height: 20px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .middle {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin: 10px 0 20px 0;
    span {
      margin-right: 20px;
    }
  }
  .bottom {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    span {
      margin-right: 24px;
    }
  }
}
</style>
