import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'standCode',
    headerText: i18n.t('考核指标编码'),
    cssClass: '',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.status != 1
        }
      },
      {
        id: 'del',
        icon: 'icon_list_delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    field: 'standName',
    headerText: i18n.t('考核指标名称')
  },
  {
    field: 'claimTypeName',
    headerText: i18n.t('考核类型')
  },
  {
    field: 'claimCalcTypeStr',
    headerText: i18n.t('计算方式')
    // // 1公式，2手动，3接口
    // template: () => {
    //   return {
    //     template: Vue.component("ruleType", {
    //       template: `<div>
    //           <span v-if="ruleType == 1">{{ $t("公式") }}</span>
    //           <span v-if="ruleType == 2">{{ $t("手动") }}</span>
    //           <span v-if="ruleType == 3">{{ $t("接口") }}</span>
    //         </div>`,
    //       data: function () {},
    //       computed: {
    //         ruleType() {
    //           return this.data.claimStandCalcRuleList[0].ruleType;
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'claimCalcValue',
    headerText: i18n.t('索赔金额')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('启用'),
        '-1': i18n.t('停用')
      }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] != '1'
        }
      },
      {
        id: 'Disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['status'] == '1'
        }
      }
    ]
  },
  // {
  //   field: "range",
  //   headerText: i18n.t("分值范围"),
  //   template: () => {
  //     return {
  //       template: Vue.component("range", {
  //         template: `<span>{{minValue}}-{{maxValue}}</span>`,
  //         data: function () {},
  //         computed: {
  //           maxValue() {
  //             return this.data.claimStandCalcRuleList[0].maxValue;
  //           },
  //           minValue() {
  //             return this.data.claimStandCalcRuleList[0].minValue;
  //           },
  //         },
  //       }),
  //     };
  //   },
  // },
  // {
  //   field: "version",
  //   headerText: i18n.t("版本"),
  // },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
