<template>
  <div class="perform-box">
    <mt-template-page
      ref="indexTable"
      :template-config="tabConfig"
      :hidden-tabs="true"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <mt-dialog
      ref="dialog"
      v-if="dialogVisible"
      css-class="index-dialog"
      :header="dialogHeader"
      :buttons="buttons"
      @close="hide"
      :open="onOpen"
      :position="{ X: 'right', Y: 'top' }"
      height="100%"
      width="60%"
    >
      <supply-index-edit ref="supplyIndex" :disabled="disabled" :data="data"></supply-index-edit>
    </mt-dialog>
  </div>
</template>

<script>
import supplyIndexEdit from './Edit.vue'
import { columnData } from './config/index'
export default {
  components: {
    supplyIndexEdit
  },
  data() {
    return {
      tabConfig: [
        {
          title: '',
          toolbar: [
            'Add',
            'Delete',
            {
              id: 'Edit',
              icon: 'icon_solid_edit',
              title: this.$t('编辑')
            },
            {
              id: 'Enable',
              icon: 'icon_table_enable',
              title: this.$t('启用')
            },
            {
              id: 'Disable',
              icon: 'icon_table_disable',
              title: this.$t('停用')
            }
          ],
          grid: {
            columnData,
            frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claimStand/pageClaimStand'
            }
          }
        }
      ],
      dialogHeader: this.$t('新增指标'),
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: async () => {
            let arr = this.$refs.supplyIndex.claimStandCalcRuleList
            arr.map((v, index) => {
              v.ruleName = this.$t('规则') + (index + 1)
            })
            let rangeArr = this.$refs.supplyIndex.claimStandRangeList
            rangeArr.forEach((item) => {
              delete item.children
            })
            let res
            let data = {
              ...this.$refs.supplyIndex.dataEdit,
              indexDescribe: this.$refs.supplyIndex.dataEdit.remark,
              claimStandCalcRuleList: arr,
              claimStandRangeList: rangeArr
            }
            if (this.dialogHeader == this.$t('新增指标')) {
              res = await this.$API.assessManage.saveClaimStand(data)
            } else if (this.dialogHeader == this.$t('编辑指标')) {
              res = await this.$API.assessManage.saveClaimStand(data)
            }
            if (res.code == 200) {
              this.hide()
              this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.indexTable.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg || this.$t('操作失败'),
                type: 'warning'
              })
            }
          },
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      disabled: false,
      data: {
        // 指标名称
        indexName: '',
        // 维度
        dimensionId: '',
        dimensionCode: '',
        dimensionName: '',
        remark: '',
        claimStandCalcRuleList: [],
        claimStandRangeList: []
      },
      dialogVisible: false
    }
  },
  created() {},
  methods: {
    handleClickCellTitle(e) {
      console.log(e.field, ' ==indexCode')
      if (e.field == 'indexCode') {
        this.$API.assessManage.getIndexDetail({ id: e.data.id }).then((res) => {
          if (res.code == 200) {
            this.data = res.data
            ;(this.dialogHeader = this.$t('指标详情')), (this.disabled = true)
            this.buttons = [
              {
                click: this.hide,
                buttonModel: { content: this.$t('取消') }
              }
            ]
            this.show()
          } else {
            this.$toast({
              content: res.msg || this.$t('获取指标信息失败'),
              type: 'warning'
            })
          }
        })
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'edit') {
        // 编辑
        this.dialogHeader = this.$t('编辑指标')
        this.disabled = false
        this.buttons = [
          {
            click: this.hide,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: async () => {
              let arr = this.$refs.supplyIndex.claimStandCalcRuleList
              arr.map((v, index) => {
                v.ruleName = this.$t('规则') + (index + 1)
              })
              let rangeArr = this.$refs.supplyIndex.claimStandRangeList
              rangeArr.forEach((item) => {
                delete item.children
              })
              let res
              let data = {
                ...this.$refs.supplyIndex.dataEdit,
                indexDescribe: this.$refs.supplyIndex.dataEdit.remark,
                claimStandCalcRuleList: arr,
                claimStandRangeList: rangeArr
              }
              if (this.dialogHeader == this.$t('新增指标')) {
                res = await this.$API.assessManage.saveClaimStand(data)
              } else if (this.dialogHeader == this.$t('编辑指标')) {
                res = await this.$API.assessManage.saveClaimStand(data)
              }
              if (res.code == 200) {
                this.hide()
                this.$toast({
                  content: res.msg || this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.indexTable.refreshCurrentGridData()
              } else {
                this.$toast({
                  content: res.msg || this.$t('操作失败'),
                  type: 'warning'
                })
              }
            },
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
        // 获取指标详情
        this.$API.assessManage.getIndexDetail({ id: e.data.id }).then((res) => {
          if (res.code == 200) {
            this.data = res.data
            this.show()
          } else {
            this.$toast({
              content: res.msg || this.$t('获取指标信息失败'),
              type: 'warning'
            })
          }
        })
      } else if (e.tool.id == 'del') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('请确认是否删除此指标？')
          },
          success: () => {
            this.$API.assessManage
              .deleteClaimStand({
                idList: [e.data.id]
              })
              .then((res) => {
                if (res.code == '200') {
                  this.$toast({
                    content: res.msg || this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$refs.indexTable.refreshCurrentGridData()
                } else {
                  this.$toast({
                    content: res.msg || this.$t('操作失败'),
                    type: 'warning'
                  })
                }
              })
          }
        })
      } else if (e.tool.id == 'Enable' || e.tool.id == 'Disable') {
        this.$API.assessManage
          .changeClaimStandStatus({
            idList: [e.data.id],
            operationType: e.tool.id == 'Enable' ? '1' : '-1'
          })
          .then((res) => {
            if (res.code == '200') {
              this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.indexTable.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg || this.$t('操作失败'),
                type: 'warning'
              })
            }
          })
      }
    },
    handleClickToolBar(e) {
      if (e.gridRef.getMtechGridRecords().length <= 0 && e.toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.dialogHeader = this.$t('新增指标')
        this.disabled = false
        this.buttons = [
          {
            click: this.hide,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: async () => {
              let arr = this.$refs.supplyIndex.claimStandCalcRuleList
              arr.map((v, index) => {
                v.ruleName = this.$t('规则') + (index + 1)
              })
              let rangeArr = this.$refs.supplyIndex.claimStandRangeList
              rangeArr.forEach((item) => {
                delete item.children
              })
              let res
              let data = {
                ...this.$refs.supplyIndex.dataEdit,
                indexDescribe: this.$refs.supplyIndex.dataEdit.remark,
                claimStandCalcRuleList: arr,
                claimStandRangeList: rangeArr
              }
              if (this.dialogHeader == this.$t('新增指标')) {
                res = await this.$API.assessManage.saveClaimStand(data)
              } else if (this.dialogHeader == this.$t('编辑指标')) {
                res = await this.$API.assessManage.saveClaimStand(data)
              }
              if (res.code == 200) {
                this.hide()
                this.$toast({
                  content: res.msg || this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.indexTable.refreshCurrentGridData()
              } else {
                this.$toast({
                  content: res.msg || this.$t('操作失败'),
                  type: 'warning'
                })
              }
            },
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
        this.data = {
          // 指标名称
          indexName: '',
          // 维度
          dimensionId: '',
          dimensionCode: '',
          dimensionName: ''
        }
        this.show()
      } else if (e.toolbar.id == 'Delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('请确认是否删除此指标？')
          },
          success: () => {
            this.$API.assessManage
              .deleteClaimStand({
                idList: e.gridRef.getMtechGridRecords().map((v) => {
                  return v.id
                })
              })
              .then((res) => {
                if (res.code == '200') {
                  this.$toast({
                    content: res.msg || this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$refs.indexTable.refreshCurrentGridData()
                } else {
                  this.$toast({
                    content: res.msg || this.$t('操作失败'),
                    type: 'warning'
                  })
                }
              })
          }
        })
      } else if (e.toolbar.id == 'Enable') {
        // 启用
        this.$API.assessManage
          .changeClaimStandStatus({
            idList: e.gridRef.getMtechGridRecords().map((v) => {
              return v.id
            }),
            operationType: 1
          })
          .then((res) => {
            if (res.code == '200') {
              this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.indexTable.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg || this.$t('操作失败'),
                type: 'warning'
              })
            }
          })
      } else if (e.toolbar.id == 'Disable') {
        // 停用
        this.$API.assessManage
          .changeClaimStandStatus({
            idList: e.gridRef.getMtechGridRecords().map((v) => {
              return v.id
            }),
            operationType: -1
          })
          .then((res) => {
            if (res.code == '200') {
              this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.indexTable.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg || this.$t('操作失败'),
                type: 'warning'
              })
            }
          })
      } else if (e.toolbar.id == 'Edit') {
        // 编辑
        if (e.gridRef.getMtechGridRecords().length > 1) {
          this.$toast({
            content: this.$t('请勾选单条数据进行编辑'),
            type: 'warning'
          })
          return
        }
        if (e.gridRef.getMtechGridRecords()[0].status == 1) {
          this.$toast({
            content: this.$t('启用状态下不可进行编辑'),
            type: 'warning'
          })
          return
        }
        this.dialogHeader = this.$t('编辑指标')
        this.disabled = false
        this.buttons = [
          {
            click: this.hide,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: async () => {
              let arr = this.$refs.supplyIndex.claimStandCalcRuleList
              arr.map((v, index) => {
                v.ruleName = this.$t('规则') + (index + 1)
              })
              let rangeArr = this.$refs.supplyIndex.claimStandRangeList
              rangeArr.forEach((item) => {
                delete item.children
              })
              let res
              let data = {
                ...this.$refs.supplyIndex.dataEdit,
                indexDescribe: this.$refs.supplyIndex.dataEdit.remark,
                claimStandCalcRuleList: arr,
                claimStandRangeList: rangeArr
              }
              if (this.dialogHeader == this.$t('新增指标')) {
                res = await this.$API.assessManage.saveClaimStand(data)
              } else if (this.dialogHeader == this.$t('编辑指标')) {
                res = await this.$API.assessManage.saveClaimStand(data)
              }
              if (res.code == 200) {
                this.hide()
                this.$toast({
                  content: res.msg || this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.indexTable.refreshCurrentGridData()
              } else {
                this.$toast({
                  content: res.msg || this.$t('操作失败'),
                  type: 'warning'
                })
              }
            },
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
        // 获取指标详情
        this.$API.assessManage
          .getIndexDetail({ id: e.gridRef.getMtechGridRecords()[0].id })
          .then((res) => {
            if (res.code == 200) {
              this.data = res.data
              this.show()
            } else {
              this.$toast({
                content: res.msg || this.$t('获取指标信息失败'),
                type: 'warning'
              })
            }
          })
      }
    },
    onOpen(args) {
      args.preventFocus = true
    },
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.show()
      })
    },
    hide() {
      this.dialogVisible = false
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.hide()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
.perform-box {
  width: 100%;
  position: relative;
  height: 100%;
  .red {
    background-color: rgba(237, 86, 51, 1);
  }
  .green {
    background-color: rgba(138, 204, 64, 1);
  }
  .yellow {
    background-color: rgba(237, 161, 51, 1);
  }
  .yellowcolor {
    color: rgba(237, 161, 51, 1) !important;
  }
  .bluecolor {
    color: #6386c1 !important;
  }
  .blurbgcolor {
    background-color: #00469c;
  }
  ::v-deep .now-status {
    padding: 3px 5px;
    color: #6386c1;
    background-color: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
  }
  ::v-deep .now-status-stop {
    padding: 3px 5px;
    color: #9a9a9a;
    background-color: rgba(154, 154, 154, 0.1);
    border-radius: 2px;
  }
  ::v-deep .change-status {
    color: #6386c1;
    margin-top: 5px;
    cursor: pointer;
  }
}
</style>
