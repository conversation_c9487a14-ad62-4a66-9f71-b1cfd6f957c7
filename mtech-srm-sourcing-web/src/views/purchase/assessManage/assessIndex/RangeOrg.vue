<template>
  <div>
    <mt-form ref="formInfo" class="form-box">
      <div v-for="(item, index) in orgList" :key="item.id" class="org-list">
        <mt-form-item prop="ruleType" class="form-item" :label="$t('适用组织')">
          <mt-DropDownTree
            :key="item.id"
            :enabled="!disabled"
            v-model="item.orgIds"
            ref="dropDownTree"
            :id="`dropdowntree-${Math.random() * 1000000000000000000000}`"
            :fields="item.fields"
            :show-check-box="true"
            ::placeholder="$t('请选择适用组织')"
            :allow-multi-selection="true"
            :auto-check="true"
            @change="orgChange($event, item, index)"
          ></mt-DropDownTree>
        </mt-form-item>

        <mt-form-item
          prop="rule"
          class="form-item"
          :label="$t('适用规则')"
          style="margin-left: 24px"
        >
          <mt-select
            ref="selectRule"
            v-model="item.rule"
            :disabled="disabled"
            :data-source="getRuleList(index)"
            :show-clear-button="true"
            @select="changeRange"
            :placeholder="$t('请选择适用规则')"
          ></mt-select>
        </mt-form-item>
      </div>
      <div v-if="!disabled" class="add-org" @click="addOrg">
        <mt-icon name="icon_card_plus"></mt-icon>
        {{ $t('添加组织适用规则') }}
      </div>
    </mt-form>

    <mt-icon name="icon_input_search" class="search-icon"></mt-icon>
    <mt-input
      class="search-selected"
      type="text"
      v-model="searchValue"
      :disabled="disabled"
      :placeholder="$t('搜索组织/规则')"
    ></mt-input>
    <mt-template-page
      ref="rangeTable"
      class="range-table"
      :template-config="tabConfig"
      :hidden-tabs="true"
    ></mt-template-page>

    <div class="org-title">
      *{{ $t('未在此设定的组织，在应用此指标时，将默认以规则一进行计算') }}
    </div>
  </div>
</template>

<script>
let timer
function toTree(data) {
  let result = []
  if (!Array.isArray(data)) {
    return result
  }
  data.forEach((item) => {
    delete item.children
  })
  let map = {}
  data.forEach((item) => {
    map[item.id] = item
  })
  data.forEach((item) => {
    let parent = map[item.parentId]
    if (parent) {
      ;(parent.children || (parent.children = [])).push(item)
    } else {
      result.push(item)
    }
  })
  return result
}
export default {
  name: 'RangeOrg',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    // 适用组织树
    orgTree: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 规则list
    ruleList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 适用组织list（后端传参）
    claimStandRangeList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      searchValue: '',
      // 组织list（前端展示）
      orgList: [
        {
          id: Math.random(),
          org: [
            {
              id: '',
              parentId: '',
              companyCode: '',
              companyId: '',
              companyName: ''
            }
          ],
          orgIds: [],
          rule: undefined,
          fields: {
            dataSource: [],
            value: 'id',
            text: 'name',
            child: 'children'
          }
        }
      ],
      // orgTree展开（所有的组织树）
      allOrgList: [],
      tabConfig: [
        {
          title: '',
          treeGrid: {
            childMapping: 'children',
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            dataSource: [],
            columnData: [
              {
                field: 'companyName',
                headerText: this.$t('组织')
              },
              {
                field: 'claimRuleName',
                headerText: this.$t('规则')
              }
            ]
          }
        }
      ]
    }
  },
  created() {
    this.recursion(this.orgTree)
    this.orgList[0].fields.dataSource = this.orgTree
    // 转成前端展示数据
    if (this.claimStandRangeList && this.claimStandRangeList.length > 0) {
      this.creatOrgList()
    }
  },
  watch: {
    searchValue(val) {
      // 防抖
      clearTimeout(timer)
      timer = setTimeout(() => {
        this.searchSelected(val)
      }, 500)
    }
  },
  methods: {
    getIdAndPid(companyId) {
      let obj = {}
      for (let i = 0; i < this.allOrgList.length; i++) {
        if (this.allOrgList[i].companyId == companyId) {
          obj = {
            id: this.allOrgList[i].id,
            parentId: this.allOrgList[i].parentId
          }
        }
      }
      return obj
    },
    creatOrgList() {
      let rangeList = []
      const range = JSON.parse(JSON.stringify(this.claimStandRangeList))
      let map = {}
      range.forEach((v) => {
        let obj = this.getIdAndPid(v.companyId)
        if (map[v.claimRuleName]) {
          map[v.claimRuleName].org.push({
            ...obj,
            companyCode: v.companyCode,
            companyId: v.companyId,
            companyName: v.companyName
          })
          map[v.claimRuleName].orgIds.push(v.companyId)
        } else {
          map[v.claimRuleName] = {
            id: Math.random(),
            org: [
              {
                ...obj,
                companyCode: v.companyCode,
                companyId: v.companyId,
                companyName: v.companyName
              }
            ],
            orgIds: [v.companyId]
          }
        }
        rangeList.push({
          ...obj,
          claimRuleName: v.claimRuleName,
          companyCode: v.companyCode,
          companyId: v.companyId,
          companyName: v.companyName
        })
      })
      let orgList = []
      Object.keys(map).forEach((v) => {
        orgList.push({
          rule: v,
          ...map[v]
        })
      })
      orgList.forEach((v, index) => {
        v.fields = this.getTreeFields(index, orgList)
      })
      this.orgList = orgList
      // 数组转树
      this.tabConfig[0].treeGrid.dataSource = toTree(rangeList)
    },
    searchSelected(val) {
      let arr = JSON.parse(JSON.stringify(this.claimStandRangeList))
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] && arr[i].companyName && arr[i].companyName.indexOf(val) === -1) {
          if (arr[i] && arr[i].claimRuleName && arr[i].claimRuleName.indexOf(val) === -1) {
            arr.splice(i, 1)
            i--
          } else if (!arr[i] || !arr[i].claimRuleName || arr[i].claimRuleName == '') {
            arr.splice(i, 1)
            i--
          }
        }
      }
      this.tabConfig[0].treeGrid.dataSource = toTree(arr)
    },
    recursion(list) {
      list.forEach((v) => {
        this.allOrgList.push({
          id: v.id,
          parentId: v.parentId,
          companyCode: v.orgCode,
          companyId: v.id,
          companyName: v.name
        })
        if (v.children && v.children.length > -1) {
          this.recursion(v.children)
        }
      })
    },
    orgChange(node, val, index) {
      if (!node.isInteracted) {
        return
      }
      const list = node.value
      let arr = []
      this.allOrgList.forEach((v) => {
        if (list.indexOf(v.companyId) > -1) {
          arr.push({
            id: v.id,
            parentId: v.parentId,
            companyCode: v.companyCode,
            companyId: v.companyId,
            companyName: v.companyName
          })
        }
      })
      val.org = arr
      val.orgIds = list
      // 重新设置下拉树选择项
      this.orgList.forEach((v, i) => {
        if (i != index) {
          v.id = Math.random()
          v.fields = this.getTreeFields(i)
        }
      })

      this.changeRange()
    },
    addOrg() {
      const dataSource = this.getTreeFields(this.orgList.length).dataSource
      this.orgList.push({
        id: Math.random(),
        org: [
          {
            id: '',
            parentId: '',
            companyCode: '',
            companyId: '',
            companyName: ''
          }
        ],
        orgIds: [],
        rule: undefined,
        fields: {
          dataSource: dataSource,
          value: 'id',
          text: 'name',
          child: 'children'
        }
      })
      this.changeRange()
    },
    // 修改后台需要的数据
    changeRange() {
      this.$nextTick(() => {
        let rangeList = []
        let selectedOrgIds = []
        this.orgList.forEach((v) => {
          v.org.forEach((k) => {
            if (k.companyName && k.companyName != '') {
              rangeList.push({
                id: k.id,
                parentId: k.parentId,
                claimRuleName: v.rule,
                companyCode: k.companyCode,
                companyId: k.companyId,
                companyName: k.companyName
              })
              selectedOrgIds.push(k.id)
            }
          })
        })
        this.$emit('update:claimStandRangeList', rangeList)
        // 数组转树
        this.tabConfig[0].treeGrid.dataSource = toTree(rangeList)
      })
    },
    getRuleList(index) {
      // 获取除了本身所有已经选择的内容
      let arr = []
      this.orgList.forEach((v, i) => {
        if (v.rule && v.rule != '' && i != index) {
          arr.push(v.rule)
        }
      })
      let end = []
      this.ruleList.forEach((v) => {
        if (arr.indexOf(v) < 0) {
          end.push(v)
        }
      })
      return end
    },
    // 获取当前值的下拉树
    getTreeFields(index, list) {
      // 获取除了本身所有已经选择的内容
      let delArr = []
      const arr = list || this.orgList
      arr.forEach((v, i) => {
        if (i != index) {
          delArr = delArr.concat(v.orgIds)
        }
      })
      let end = JSON.parse(JSON.stringify(this.orgTree))
      this.filterOrgTree(end, delArr)
      return Object.assign(
        {},
        {
          dataSource: end,
          value: 'id',
          text: 'name',
          child: 'children'
        }
      )
    },
    filterOrgTree(val, del) {
      for (let i = 0; i < val.length; i++) {
        if (del.indexOf(val[i].id) > -1) {
          val.splice(i, 1)
          i--
        }
        if (val && val[i] && val[i].children && val[i].children.length > 0) {
          this.filterOrgTree(val[i].children, del)
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .org-list {
  .form-item {
    width: calc((100% - 24px) / 2);
    display: inline-block;
  }
}
.form-box {
  margin-top: 14px;
}
.add-org {
  cursor: pointer;
  color: #6386c1;
  display: inline-block;
}
::v-deep .range-table {
  td {
    text-align: left !important;
  }
  .e-grid {
    border: 0;
  }
  .e-gridheader {
    display: none;
  }
  .e-gridcontent {
    .e-content {
      height: auto !important;
    }
    td {
      border: 0;
      height: 40px !important;
    }
  }
}
::v-deep .search-selected {
  height: 40px;
  line-height: 40px;
  span {
    border: 1px solid rgb(230, 233, 237) !important;
    border-bottom: 0px !important;
    margin: 0;
    padding: 0 10px 0 30px;
    border-radius: 4px 4px 0 0;
  }
}
::v-deep .search-icon {
  position: relative;
  color: #979797;
  top: 33px;
  left: 10px;
}
.org-title {
  color: #ed5633;
  margin-top: 24px;
}
</style>
