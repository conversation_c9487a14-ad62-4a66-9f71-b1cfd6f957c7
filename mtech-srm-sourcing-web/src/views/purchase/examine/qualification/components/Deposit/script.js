import { EXAMINE_METHOD_MAP } from '@/constants/examine'
import { createEditInstance } from '@/utils/ej/dataGrid'
import { getValueByPath } from '@/utils/obj'
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import Checkbox from '../Checkbox'
import ExamineStatusNum from './ExamineStatusNum'
import TopSticky from './TopSticky'
import TimerClock from './TimerClock'

export default {
  props: {
    examineId: {
      type: [Number, String],
      default: null
    },
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    examineInfo: {
      type: Object,
      default: () => ({})
    }
  },
  mounted() {
    // 获取数据
    this.initData()
    const observer = this.initTopSticky()
    this.$once('hook:beforeDestroy', () => {
      observer.disconnect()
    })
  },

  components: {
    Checkbox,
    ExamineStatusNum,
    TopSticky,
    TimerClock
  },
  computed: {
    total() {
      return {
        examineStatus: this.rfxExamineSupplierRelV2ResponseList.map((e) => e.examineStatus),
        oldExamineStatus: this.rfxExamineSupplierRelV2ResponseList.map((e) => e.$oldExamineStatus)
      }
    },
    // allowEditSupplierIds() {
    //   return this.rfxExamineSupplierRelV2ResponseList
    //     .filter((e) => +e.examineStatus === 1)
    //     .map((e) => e.supplierId);
    // },
    supplierList() {
      return this.rfxExamineSupplierRelV2ResponseList.map((e) => {
        return {
          supplierId: e.supplierId,
          supplierName: e.supplierName
        }
      })
    },
    filterrfxExamineSupplierRelV2ResponseList() {
      if (this.filterSupplierIds.length === 0) {
        return this.rfxExamineSupplierRelV2ResponseList
      }
      return this.rfxExamineSupplierRelV2ResponseList.filter((e) =>
        this.filterSupplierIds.includes(e.supplierId)
      )
    }
  },
  data() {
    return {
      allowEditSupplierIds: [],
      EXAMINE_METHOD_MAP,
      expand: false,
      rfxExamineSupplierRelV2ResponseList: [],
      filterSupplierIds: []
    }
  },
  methods: {
    getAllowEditSupplierIds() {
      this.allowEditSupplierIds = this.rfxExamineSupplierRelV2ResponseList
        .filter((e) => +e.examineStatus === 1)
        .map((e) => e.supplierId)
    },
    initTopSticky() {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((item) => {
            if (item.isIntersecting) {
              this.$refs.stickyTop.$el.classList.remove('show')
            } else {
              this.$refs.stickyTop.$el.classList.add('show')
              this.$refs.stickyTop.$el.style.left = this.$refs.topRow.offsetLeft + 'px'
            }
          })
        },
        {
          threshold: 0.5,
          root: null
        }
      )
      observer.observe(this.$refs.topRow)
      return observer
    },
    async initData() {
      const res = await this.$API.examine
        .viewItemListByExamineId({
          queryBuilderDTO: {
            page: { current: 1, size: 1e3 }
          },
          rfxId: this.$route.query.rfxId,
          examineId: this.examineId
        })
        .catch(() => {})
      // res.data.rfxExamineSupplierRelV2ResponseList = new Array(10)
      //   .fill(0)
      //   .map(() => {
      //     return [...res.data.rfxExamineSupplierRelV2ResponseList];
      //   })
      //   .flat();
      this.rfxExamineSupplierRelV2ResponseList = res.data.rfxExamineSupplierRelV2ResponseList.map(
        (e, i) => {
          const data = {
            ...e,
            $oldExamineStatus: e.examineStatus,
            $checked: false,
            $expand: false,
            $grid: {
              actionComplete: this.actionComplete,
              columnData: columnData(this),
              dataSource: serializeListDrawing(e.rfxExamineItemSupplierRelResponses),
              editSettings: {
                allowAdding: true,
                allowEditing: true,
                allowDeleting: true,
                mode: 'Normal',
                showConfirmDialog: false,
                showDeleteConfirmDialog: true,
                newRowPosition: 'Top'
              }
            },
            index: i
          }
          data.$grid.actionComplete = this.actionComplete.bind(this, data)
          return data
        }
      )
      this.getAllowEditSupplierIds()
    },
    setExamineStatusAll(examineStatus) {
      let _flag = false
      this.rfxExamineSupplierRelV2ResponseList.forEach((rfxExamineSupplierRel) => {
        if (
          rfxExamineSupplierRel.$checked &&
          this.allowEditSupplierIds.includes(rfxExamineSupplierRel.supplierId)
        ) {
          _flag = true
          rfxExamineSupplierRel.examineStatus = examineStatus
        }
      })
      if (!_flag) {
        this.$toast({
          content: '请选择供应商后再操作',
          type: 'warning'
        })
      }
    },
    actionComplete(data, args) {
      if (args.requestType == 'save' && args.action == 'edit') {
        data.$grid.dataSource[args.rowIndex] = args.rowData
      }
    },
    async save(showToast = false) {
      if (showToast) {
        this.$store.commit('startLoading')
      }
      this.rfxExamineSupplierRelV2ResponseList.forEach((e, i) => {
        let a = 'grid' + i
        this.$refs[a][0].$refs.ejsRef.endEdit()
      })
      const params = {
        rfxId: this.$route.query.rfxId,
        supplierItemDetailList: this.rfxExamineSupplierRelV2ResponseList
          .map((e) => e.$grid.dataSource)
          .flat()
          .map((e) => {
            return {
              id: e.id,
              supplierId: e.supplierId,
              examineItemId: e.examineItemId,
              examineItemCheck: e.examineItemCheck,
              supplierReply: e.supplierReply
            }
          }),
        supplierPassedList: this.rfxExamineSupplierRelV2ResponseList
          .filter((e) => e.examineStatus === 2)
          .map((e) => e.supplierId)
          .filter((e) => this.allowEditSupplierIds.includes(e)),
        supplierRejectedList: this.rfxExamineSupplierRelV2ResponseList
          .filter((e) => e.examineStatus === 0)
          .map((e) => e.supplierId)
          .filter((e) => this.allowEditSupplierIds.includes(e))
      }
      const res = await this.$API.examine.bigSaveExamineForAllSupplier(params).catch(() => {})
      this.initData()
      if (showToast) {
        this.$store.commit('endLoading')
      }
      if (res?.code === 200) {
        if (showToast) {
          this.$toast({
            content: '保存成功',
            type: 'success'
          })
        }
        return true
      }
      return false
    }
  }
}

function columnData(that) {
  const editInstance = createEditInstance()

  return [
    {
      field: 'examineItemCode',
      headerText: that.$t('资质审查项目编码'),
      allowEditing: false
    },
    {
      field: 'examineItemName',
      headerText: that.$t('资质审查项目名称'),
      allowEditing: false
    },
    {
      field: 'examineItemSpec',
      headerText: that.$t('资质审查项目说明'),
      allowEditing: false
    },
    {
      field: 'needFile',
      headerText: that.$t('是否需要附件'),
      allowEditing: false,
      formatter: function ({ field }, item) {
        const cellVal = getValueByPath(item, field)
        const dataSource = [
          { value: 0, text: that.$t('否') },
          { value: 1, text: that.$t('是') }
        ]
        return dataSource.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'drawing',
      headerText: that.$t('附件'),
      allowEditing: false,
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: function () {
        return {
          template: cellFileView
        }
      }
    },
    {
      field: 'purOpinion',
      headerText: that.$t('供应商回复'),
      allowEditing: false
    },
    {
      field: 'supplierReply',
      headerText: that.$t('采方审核意见'),
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'text',
          disabled: !that.allowEditSupplierIds.includes(rowData.supplierId)
        })
      })
    },
    {
      field: 'examineItemCheck',
      headerText: that.$t('审核结果'),
      formatter: function ({ field }, item) {
        const cellVal = getValueByPath(item, field)
        const dataSource = [
          { value: 0, text: that.$t('不通过') },
          { value: 1, text: that.$t('通过') }
        ]
        return dataSource.find((e) => e.value === cellVal)?.text ?? cellVal
      },
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          disabled: !that.allowEditSupplierIds.includes(rowData.supplierId),
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 0, text: that.$t('不通过') },
            { value: 1, text: that.$t('通过') }
          ]
        })
      })
    }
  ]
}

function serializeListDrawing(list) {
  list.forEach((e) => {
    e.drawing = e.sourcingFileResponseList ? JSON.stringify(e.sourcingFileResponseList) : null
  })
  return list
}
