<template>
  <div>
    <span :class="valStyle(val)">
      {{ total.oldExamineStatus.filter((e) => e === val).length }}
    </span>
    <span class="inline-block ml-5 mr-5">{{ valText(val) }}</span>
    <span
      class="inline-block add"
      v-if="
        total.examineStatus.filter((e) => e === val).length -
        total.oldExamineStatus.filter((e) => e === val).length
      "
    >
      {{
        total.examineStatus.filter((e) => e === val).length -
        total.oldExamineStatus.filter((e) => e === val).length
      }}
    </span>
  </div>
</template>

<script>
export default {
  props: {
    total: {
      type: Object,
      default: () => ({})
    },
    val: {
      type: Number,
      default: null
    }
  },
  methods: {
    valText(val) {
      const examineStatusMap = {
        0: this.$t('未通过'),
        1: this.$t('待审核'),
        2: this.$t('已通过')
      }
      return examineStatusMap[val] ?? val
    },
    valStyle(val) {
      const examineStatusMap = {
        0: 'inline-block num fail',
        1: 'inline-block num',
        2: 'inline-block num success'
      }
      return examineStatusMap[val] ?? ''
    }
  }
}
</script>

<style scoped lang="scss">
span.num {
  font-size: 20px;
  font-weight: bold;

  &.success {
    color: rgba(138, 204, 64, 1);
  }
  &.fail {
    color: #ed5633;
  }
}

span.add {
  font-size: 12px;
  font-weight: normal;
  color: rgba(41, 41, 41, 1);
}
</style>
