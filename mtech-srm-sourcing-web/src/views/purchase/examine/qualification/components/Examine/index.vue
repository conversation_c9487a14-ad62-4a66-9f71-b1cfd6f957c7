<template>
  <div class="full-height">
    <div class="flex top-row">
      <div class="flex mr-20">
        <div class="mr-5">{{ $t('是否需要保证金') }}:</div>
        <b>{{ examineInfo.needEarnestMoney ? $t('是') : $t('否') }}</b>
      </div>
      <div class="flex mr-20">
        <div class="mr-5">{{ $t('保证金金额') }}:</div>
        <b>{{ examineInfo.earnestMoney || '-' }}</b>
      </div>
    </div>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看

function serializeListDrawing(list) {
  list.forEach((e) => {
    e.examineStatus = e.examineStatus ?? 0
    e.moneyStatus = e.moneyStatus ?? 1
    e.drawing = e.sourcingFileResponseList ? JSON.stringify(e.sourcingFileResponseList) : null
  })
  return list
}

export default {
  props: {
    examineId: {
      type: [Number, String],
      default: null
    },
    examineInfo: {
      type: Object,
      default: () => ({})
    },
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false,
          buttonQuantity: 6,
          toolbar: [
            [
              {
                id: 'methods@earnestPass',
                icon: 'icon_solid_edit',
                title: this.$t('保证金查询'),
                permission: ['O_02_1314']
              },
              {
                id: 'methods@earnestRefund',
                icon: 'icon_solid_Submit',
                title: this.$t('保证金退款'),
                permission: ['O_02_1315']
              },
              {
                id: 'methods@paymentStatus',
                icon: 'icon_solid_Submit',
                title: this.$t('退款状态查询'),
                permission: ['O_02_1316']
              },
              {
                id: 'methods@earnestConfirm',
                icon: 'icon_solid_edit',
                title: this.$t('保证金确认'),
                permission: ['O_02_1317']
              },
              {
                id: 'methods@earnestReject',
                icon: 'icon_solid_edit',
                title: this.$t('保证金驳回'),
                permission: ['O_02_1715']
              },
              {
                id: 'methods@earnestReturn',
                icon: 'icon_solid_edit',
                title: this.$t('保证金退回'),
                permission: ['O_02_1732']
              }
            ],
            ['Refresh']
          ],
          grid: {
            gridId: this.$permission.gridId.purchase.examine.tab.view.deposit,
            asyncConfig: {
              url: this.$API.examine.viewMoneyStatusListV1,
              queryBuilderWrap: 'queryBuilderDTO',
              params: {
                rfxId: this.$route.query.rfxId,
                examineId: this.examineId
              },
              serializeList: serializeListDrawing
            },
            columnData: [
              {
                width: '60',
                type: 'checkbox'
              },
              {
                field: 'supplierCode',
                headerText: this.$t('供应商编码')
              },
              {
                field: 'supplierName',
                headerText: this.$t('供应商名称')
              },
              {
                field: 'moneyStatus',
                headerText: this.$t('保证金缴纳'),
                valueConverter: {
                  type: 'map',
                  map: {
                    0: this.$t('已提交凭证'),
                    1: this.$t('未缴纳'),
                    2: this.$t('已缴纳'),
                    3: this.$t('退款中'),
                    4: this.$t('已全部退款'),
                    5: this.$t('无需保证金'),
                    6: this.$t('部分退款'),
                    7: this.$t('发起退款中'),
                    8: this.$t('保证金已驳回'),
                    9: this.$t('保证金已退回')
                  }
                }
              },
              {
                field: 'drawing',
                headerText: this.$t('保证金附件'),
                template: function () {
                  return {
                    template: cellFileView
                  }
                },
                editTemplate: function () {
                  return {
                    template: cellFileView
                  }
                }
              }
            ],
            dataSource: []
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(arg) {
      if (arg.toolbar.id.indexOf('methods@') === 0) {
        this[arg.toolbar.id.replace(/^methods@/, '')](arg)
      }
    },
    // 审核通过
    async earnestPass({ grid }) {
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      const res = await this.$API.examine.earnestPass({
        examineId: this.examineId,
        rfxId: this.$route.query.rfxId,
        supplierIdList: records.map((e) => e.supplierId).filter((e) => e)
      })
      if (res?.code === 200) {
        this.$toast({
          content: res.msg,
          type: 'success'
        })
      }
    },
    // 保证金退款
    async earnestRefund({ grid }) {
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      const res = await this.$API.examine.earnestRefund({
        examineId: this.examineId,
        rfxId: this.$route.query.rfxId,
        supplierIdList: records.map((e) => e.supplierId).filter((e) => e)
      })
      if (res?.code === 200) {
        this.$toast({
          content: this.$t('保证金退款成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // 退款状态查询
    async paymentStatus() {
      const res = await this.$API.examine.paymentStatus({
        rfxId: this.$route.query.rfxId
      })
      if (res?.code === 200) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      }
    },
    earnestConfirm({ grid }) {
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (records.some((e) => e.moneyStatus != 1 && e.moneyStatus != 0)) {
        this.$toast({ content: this.$t('只能确认未缴纳或已提交凭证状态的数据'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('请确认供应商是否已缴纳保证金，确认后不可更改！')
        },
        success: () => {
          this.$API.examine
            .earnestConfirm({
              examineId: this.examineId,
              rfxId: this.$route.query.rfxId,
              supplierIdList: records.map((e) => e.supplierId).filter((e) => e)
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    // 保证金驳回
    earnestReject({ grid }) {
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否确认驳回！')
        },
        success: () => {
          this.$API.examine
            .earnestReject({
              rfxId: this.$route.query.rfxId,
              idList: records.map((e) => e.id).filter((e) => e)
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    // 保证金退回
    earnestReturn({ grid }) {
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否确认退回？')
        },
        success: () => {
          this.$API.examine
            .earnestReturn({
              rfxCode: this.detailInfo.rfxCode,
              examineCode: records[0].examineCode,
              idList: records.map((e) => e.id).filter((e) => e)
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
}
.mr-10 {
  margin-right: 10px;
}
.mr-20 {
  margin-right: 20px;
}
.ml-5 {
  margin-left: 5px;
}
.mr-5 {
  margin-right: 5px;
}
.mt-10 {
  margin-top: 10px;
}

.top-row {
  padding: 20px;
  line-height: 24px;
  height: 24px;
  vertical-align: baseline;
}
</style>
