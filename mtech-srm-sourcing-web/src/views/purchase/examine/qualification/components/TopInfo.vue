<template>
  <!-- copy: @/views/purchase/rfx/detail/components/topInfo.vue -->
  <div>
    <div class="top-info">
      <div class="timer-clock" v-if="showTimer && detailInfo.status === 1">
        <timer-clock
          :start-time="timerStartTime"
          :end-time="timerEndTime"
          :timer-string="timerString"
          :server-time="timerServerTime"
        ></timer-clock>
      </div>
      <div class="lf-wrap">
        <div class="name-wrap">
          <div class="first-line">
            <span class="code">{{ detailInfo.rfxCode || '-' }}</span>
            <span class="tags tags-1" v-if="detailInfo && detailInfo.businessTypeName"
              >{{ detailInfo.businessTypeName || '-' }}
            </span>
            <span class="tags tags-2">{{ detailInfo.docStatusDesc }} </span>
          </div>
          <div class="second-line">
            <div :title="detailInfo.rfxName">
              {{ detailInfo.rfxName || '' }}
            </div>
            <div>{{ $t('采购组织') }}：{{ detailInfo.purOrgName || '-' }}</div>
            <div>{{ $t('执行人') }}：{{ detailInfo.purExecutorName || '-' }}</div>
          </div>
        </div>
        <div class="btns-wrap">
          <mt-button
            v-show="editable && showBtnSave && !examineInfo.onlyEnableEarnest"
            flat
            @click.native="editProject"
            >{{ $t('提交') }}</mt-button
          >
          <mt-button @click.native="$router.back()">{{ $t('返回') }}</mt-button>
        </div>
      </div>
    </div>
    <div class="top-form-generator">
      <transition name="fade">
        <FormGenerator
          ref="form"
          v-show="showHeaderForm"
          style="flex-basis: 100%; margin: 0 -10px 0 10px"
          :field-defines="fieldDefines"
          :form-field-config="formFieldConfig"
          :sourcing-expand-list="sourcingExpandList"
        ></FormGenerator
      ></transition>
    </div>
    <div class="top-shrink" @click="showHeaderForm = !showHeaderForm">
      <mt-icon v-show="!showHeaderForm" name="a-iconxl"></mt-icon>
      <mt-icon v-show="showHeaderForm" name="a-iconsq"></mt-icon>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import TimerClock from 'COMPONENTS/SourcingProject/timerClock.vue'
import FormGenerator from '@/views/purchase/rfx/detail/components/FormGenerator.vue'
import { transferStatusList } from '@/constants'
import { createFiltering } from '@/utils/ej/select'

export default {
  name: 'RFXHeader',
  components: {
    TimerClock,
    FormGenerator
  },
  props: {
    moduleType: {
      type: Number,
      default: -1
    },
    detailInfo: {
      type: Object,
      required: true
    },
    examineInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    showBtnSave: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      currentTurnInfo: {},
      showTimer: true,
      timerString: '',
      timerStartTime: 0,
      timerEndTime: 0,
      showHeaderForm: false,
      formData: {},
      fieldDefines: [],
      formFieldConfig: {},
      sourcingExpandList: [],
      timer: null,
      timerServerTime: 0,
      priceQuotaIsPush: 0
    }
  },
  watch: {
    detailInfo: {
      handler(val) {
        if (val.id) this.getUserConfigFields()
        // 更新倒计时
        this.getCurrentTurnsInfo()
        if (val.transferStatus === 100) {
          this.checkPriceQuotaIsPush()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getCurrentTurnsInfo()
    this.$bus.$on(`updateTime`, () => {
      this.getCurrentTurnsInfo()
    })
    this.timer = setInterval(() => {
      // (34,"定标中"), (20, "已提交定点"),(0, "立项准备"),(100, "已完成");(40, "技术评标中"),(43, "商务评标中"),
      let statusArr = [34, 20, 0, 100, 40, 43]
      if (!statusArr.includes(this.detailInfo.transferStatus)) {
        this.getCurrentTurnsInfo()
      }
    }, 10000)

    const timer = setInterval(() => {
      this.$bus.$emit('TOP_INFO_TIMER_CLOCK_DATA_EMIT', {
        'start-time': this.timerStartTime,
        'end-time': this.timerEndTime,
        'timer-string': this.timerString,
        'server-time': this.timerServerTime,
        isShow: this.showTimer && this.detailInfo.status === 1
      })
    }, 500)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer)
    })
  },
  beforeDestroy() {
    this.$bus.$off('updateTime')
    clearInterval(this.timer)
  },
  deactivated() {
    this.$bus.$off('updateTime')
    clearInterval(this.timer)
  },
  computed: {
    isDisabled() {
      // 已完成和关闭要禁止编辑
      return this.detailInfo?.status === 8 || this.detailInfo?.status === -1
    },
    editable() {
      return (
        this.detailInfo.approveStatus === 0 ||
        (this.detailInfo.transferStatus != 20 && this.detailInfo.transferStatus != 100)
      )
    },
    getCurrentTransferStatus() {
      let _transferStatus = this?.detailInfo?.transferStatus
      return transferStatusList[_transferStatus] || ''
    },
    isOnlyEarnest() {
      return !!this.examineInfo?.onlyEnableEarnest
    }
  },
  methods: {
    checkPriceQuotaIsPush() {
      this.$API.rfxDetail
        .checkPriceQuotaIsPush({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          if (res.code === 200 && typeof res.data === 'number') {
            this.priceQuotaIsPush = res.data
          }
        })
    },
    getUserConfigFields() {
      this.$API.rfxList
        .getUserConfigFields({
          sourcingObj: this.detailInfo.sourcingObj,
          sourcingMode: this.detailInfo.sourcingMode,
          businessTypeCode: this.detailInfo.businessTypeCode
        })
        .then((res) => {
          this.formData = {
            sourcingObj: this.detailInfo.sourcingObj,
            sourcingObjType: this.detailInfo.sourcingObjType,
            purExecutorName: this.detailInfo.purExecutorName,
            purExecutorId: this.detailInfo.purExecutorId
          }

          const setReadonly = (o) => {
            Object.keys(o).forEach((k) => {
              o[k].readonly = true
            })
            return o
          }
          this.formFieldConfig = setReadonly({
            // strategyConfigName: {
            //   type: "text",
            //   defaultValue: () => this.detailInfo.strategyConfigName,
            //   readonly: true,
            // },
            //标题
            rfxName: {
              defaultValue: () => this.detailInfo.rfxName,
              type: 'text',
              readonly: this.detailInfo.approveStatus !== 0
            },
            //寻源方式
            sourcingMode: {
              type: 'select',
              defaultValue: () => this.detailInfo.sourcingMode,
              readonly: true,
              dataSource: [
                { text: this.$t('询报价'), value: 'rfq' },
                // { text: this.$t("直接定价"), value: "direct_pricing" },
                { text: this.$t('招投标'), value: 'invite_bids' },
                { text: this.$t('竞价'), value: 'bidding_price' }
              ]
            },
            //寻源对象
            sourcingObj: {
              type: 'text',
              defaultValue: () => this.detailInfo.sourcingObj,
              readonly: true
            },
            //询价类型
            sourcingType: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              dataSource: [
                { text: this.$t('新品'), value: 'new_products' },
                { text: this.$t('二次'), value: 'second_inquiry' },
                { text: this.$t('已有'), value: 'exist' }
              ],
              defaultValue: () => this.detailInfo.sourcingType
            },
            //价格分类
            priceClassification: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              dataSource: [
                { text: this.$t('暂估价格'), value: 'predict_price' },
                { text: this.$t('SRM价格'), value: 'srm_price' },
                { text: this.$t('执行价格'), value: 'execute_price' },
                { text: this.$t('基价'), value: 'basic_price' }
              ],
              defaultValue: () => this.detailInfo.priceClassification
            },
            //公司名称
            companyName:
              this.detailInfo.approveStatus !== 0 &&
              this.detailInfo.transferStatus === 100 &&
              this.detailInfo.transferStatus === 20
                ? {
                    defaultValue: () => this.detailInfo.companyName,
                    type: 'text',
                    readonly: this.detailInfo.approveStatus !== 0
                  }
                : {
                    type: 'select',
                    readonly: this.detailInfo.approveStatus !== 0,
                    handler: (ej2EventObject) => {
                      if (ej2EventObject.itemData != null) {
                        Object.assign(this.formData, ej2EventObject.itemData.data)
                      }
                    },
                    defaultValue: () => this.detailInfo.companyName,
                    api: this.$API.masterData.permissionCompanyList().then((res) => {
                      return res?.data.map((item) => ({
                        text: item.orgName,
                        value: item.orgName,
                        data: {
                          companyCode: item.orgCode,
                          companyName: item.orgName,
                          companyId: item.id
                        }
                      }))
                    })
                  },
            //扩展
            sourcingExpand:
              this.detailInfo.transferStatus == 100 || this.detailInfo.transferStatus == 20
                ? {
                    defaultValue: () => {
                      let rfxHeaderExpandResponseList = []
                      if (this.detailInfo.rfxHeaderExpandResponseList) {
                        this.detailInfo.rfxHeaderExpandResponseList.forEach((v) => {
                          rfxHeaderExpandResponseList.push(v.purOrgName + '+' + v.siteName)
                        })
                      }
                      return rfxHeaderExpandResponseList.toString()
                    },
                    type: 'text',
                    readonly: true
                  }
                : {
                    type: 'multiSelect',
                    readonly: false,
                    handler: (ej2EventObject) => {
                      if (ej2EventObject) {
                        this.formData.rfxHeaderExpandResponseList = ej2EventObject
                      }
                    },
                    defaultValue: () => this.detailInfo.sourcingExpand,
                    api: this.$API.rfxDetail
                      .purOrgWithSite({
                        companyId: this.detailInfo.companyId
                      })
                      .then((res) => {
                        let dataSource = []
                        res.data.forEach((v) => {
                          if (v.siteOrgs) {
                            v.siteOrgs.forEach((x) => {
                              dataSource.push({
                                text: v.businessOrganizationName + '+' + x.orgName,
                                value: v.businessOrganizationCode + '+' + x.orgCode,
                                data: {
                                  purOrgCode: v.businessOrganizationCode,
                                  purOrgId: v.id,
                                  purOrgName: v.businessOrganizationName,
                                  siteCode: x.orgCode,
                                  siteId: x.id,
                                  siteName: x.orgName
                                }
                              })
                            })
                          }
                        })
                        console.error(this.detailInfo, 'this.detailInfo')
                        let text = this.detailInfo.purOrgName + '+' + this.detailInfo.siteName
                        this.sourcingExpandList = dataSource
                        dataSource = dataSource.filter((x) => x.text !== text)
                        return dataSource
                      })
                  },
            // 币种 currencyName currencyName--非草稿状态的单据，头部的币种是只读
            currencyName: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              handler: (ej2EventObject) => {
                if (ej2EventObject.itemData != null) {
                  Object.assign(this.formData, ej2EventObject.itemData.data)
                }
              },
              defaultValue: () => this.detailInfo.currencyCode,
              api: this.$API.masterData.queryAllCurrency().then((res) => {
                return res.data.map((item) => ({
                  text: item.currencyName,
                  value: item.currencyCode,
                  data: {
                    currencyName: item.currencyName,
                    currencyCode: item.currencyCode
                  }
                }))
              })
            },
            //采购组织  purOrgName  purOrgCode  purOrgId
            purOrgName:
              this.detailInfo.approveStatus !== 0 &&
              this.detailInfo.transferStatus === 100 &&
              this.detailInfo.transferStatus === 20
                ? {
                    defaultValue: () => this.detailInfo.purOrgName,
                    type: 'text',
                    readonly: this.detailInfo.approveStatus !== 0
                  }
                : {
                    type: 'select',
                    readonly: this.detailInfo.approveStatus !== 0,
                    handler: (ej2EventObject) => {
                      if (ej2EventObject.itemData == null) {
                        Object.assign(this.formData, {
                          purOrgCode: null,
                          purOrgId: null,
                          purOrgName: null
                        })
                      } else {
                        Object.assign(this.formData, ej2EventObject.itemData.data)
                      }
                    },
                    defaultValue: () => this.detailInfo.purOrgId,
                    api: this.$API.masterData
                      .permissionOrgList({
                        orgId: this.detailInfo.companyId
                      })
                      .then((res) => {
                        return res.data.map((item) => ({
                          text: item.organizationName,
                          value: item.id,
                          data: {
                            purOrgName: item.organizationName,
                            purOrgCode: item.organizationCode,
                            purOrgId: item.id
                          }
                        }))
                      })
                  },
            //业务类型
            businessTypeName: {
              type: 'select',
              handler: (ej2EventObject) => {
                if (ej2EventObject.itemData != null) {
                  Object.assign(this.formData, ej2EventObject.itemData.data)
                }
              },
              defaultValue: () => this.detailInfo.businessTypeCode,
              api: this.$API.masterData
                .dictionaryGetBusinessType()
                // .dictionaryGetList({
                //   dictCode: "businessType",
                // })
                .then((res) => {
                  return res.data.map((item) => ({
                    text: item.itemName,
                    value: item.itemCode,
                    data: {
                      businessTypeName: item.itemName,
                      businessTypeCode: item.itemCode,
                      businessTypeId: item.id
                    }
                  }))
                }),
              readonly: true
            },
            //采购员  purExecutorName  purExecutorId
            purExecutorName: {
              type: 'text',
              defaultValue: () => this.detailInfo.purExecutorName,
              readonly: true
              // type: "debounce-select",
              // readonly: true,
              // // readonly: this.detailInfo.approveStatus !== 0,
              // defaultValue: () => this.detailInfo.purExecutorId,
              // handler: (ej2EventObject) => {
              //   if (ej2EventObject?.itemData) {
              //     Object.assign(this.formData, {
              //       purExecutorName: ej2EventObject.itemData?.employeeName,
              //       purExecutorId: ej2EventObject.itemData?.uid,
              //     });
              //   }
              // },
              // type: "select",
              // readonly: this.detailInfo.approveStatus !== 0,
              // handler: (ej2EventObject) => {
              //   Object.assign(this.formData, ej2EventObject.itemData.data);
              // },
              // defaultValue: () => this.detailInfo.purExecutorId,
              // api: this.$API.masterData
              //   .getUserPageList(DEFAULTPARAM)
              //   .then((res) => {
              //     return res?.data?.records.map((item) => ({
              //       text: item.employeeName,
              //       value: item.id,
              //       data: {
              //         purExecutorName: item.employeeName,
              //         purExecutorId: item.id,
              //       },
              //     }));
              //   }),
            },
            // supplierRange: { //后端定义的  supplierRange：拓展(定制)  暂不渲染
            //   type: "select",
            //   dataSource: [
            //     { text: "品类合格", value: "category_qualified" },
            //     { text: "品类合格+有价格记录", value: "price_record" },
            //     { text: "品类合格+无价格记录", value: "non_price_Record" },
            //     { text: "所有供应商", value: "all_supplier" },
            //   ],
            // },
            //备注
            remark: {
              col: 1,
              readonly: this.detailInfo.approveStatus !== 0,
              defaultValue: () => this.detailInfo.remark,
              type: 'text'
            },
            //需求部门  deptName  deptCode deptId
            deptName: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              allowFiltering: true,
              filtering: createFiltering('text'),
              handler: (ej2EventObject) => {
                if (ej2EventObject.itemData != null) {
                  Object.assign(this.formData, ej2EventObject.itemData.data)
                }
              },
              defaultValue: () => this.detailInfo.deptName,
              api: this.$API.masterData
                .getDepartmentList({
                  departmentName: ''
                })
                .then((res) => {
                  return res.data.map((item) => ({
                    text: item.departmentName,
                    value: item.departmentName,
                    data: {
                      deptCode: item.departmentCode,
                      deptName: item.departmentName
                    }
                  }))
                })
            },
            //工厂 siteName siteId  siteCode
            siteName:
              this.detailInfo.approveStatus !== 0 &&
              this.detailInfo.transferStatus === 100 &&
              this.detailInfo.transferStatus === 20
                ? {
                    defaultValue: () => this.detailInfo.siteName,
                    type: 'text',
                    readonly: this.detailInfo.approveStatus !== 0
                  }
                : {
                    type: 'select',
                    handler: (ej2EventObject) => {
                      if (ej2EventObject.itemData == null) {
                        Object.assign(this.formData, {
                          siteCode: null,
                          siteId: null,
                          siteName: null
                        })
                      } else {
                        Object.assign(this.formData, ej2EventObject.itemData.data)
                      }
                    },
                    readonly: this.detailInfo.approveStatus !== 0,
                    // todo 这里产品没有说明，后端不知道从哪里取值，
                    // dataSource: [],
                    defaultValue: () => this.detailInfo.siteName,
                    api: this.$API.masterData
                      .permissionSiteList({
                        buOrgId: this.detailInfo.purOrgId,
                        companyId:
                          sessionStorage.getItem('selectCompanyId') ?? this.detailInfo.companyId,
                        orgLevelTypeCode: 'ORG06'
                      })
                      .then((res) => {
                        console.log(12342134)
                        if (res.data == null) {
                          return []
                        }
                        let siteNameArr = []
                        for (let item of res.data) {
                          siteNameArr.push({
                            text: item.orgName,
                            value: item.orgName,
                            data: {
                              siteName: item.orgName,
                              siteId: item.id,
                              siteCode: item.orgCode
                            }
                          })
                        }
                        return siteNameArr
                      })
                  }
          })
          res.data.fieldDefines.forEach((e) => {
            // 强制必填字段
            if (['companyName', 'purOrgName', 'purExecutorId'].includes(e.fieldCode)) {
              e.required = 1
            }
          })
          this.fieldDefines = res.data.fieldDefines
          this.fieldDefines.sort((a, b) => b.sortValue - a.sortValue)
        })
    },
    getCurrentTurnsInfo() {
      if (!this.$route.query.rfxId) return
      // this.$API.rfxDetail
      //   .getCurrentTurn({
      //     rfxId: this.$route.query.rfxId,
      //   })
      //   .then((res) => {
      //     this.timerStartTime = new Date(res.data?.currentTime).getTime();
      //     this.timerEndTime = new Date(res.data?.endTime).getTime();
      //   });
      this.$API.rfxDetail
        .getRfxCountdown({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          if (res?.data && JSON.stringify(res?.data) != '{}') {
            this.timerStartTime = new Date().getTime()
            this.timerEndTime = +res.data?.countDown
            this.timerServerTime = +res.data?.currentServerTime
            this.timerString = res.data?.title
            this.showTimer = true
          } else {
            this.showTimer = false
          }
        })
    },
    //保存
    editProject() {
      this.$emit('editProject')
    },
    bidOpenBefore() {
      // 商务标 1 ，技术标 0
      let type = this.moduleType
      if (type == 21) {
        // 技术开标的时候:true：有问题,就提示开标会关闭单据
        // false：没问题,就执行开标操作
        let rfxId = this.$route.query.rfxId
        this.$API.rfxExpert.checkJoinQuantity(rfxId).then((res) => {
          if (res.data) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('不满足最少供应商数量，提前开标会关闭单据，请确认')
              },
              success: () => {
                this.bidOpening()
              }
            })
          } else {
            this.bidOpening()
          }
        })
      } else {
        this.bidOpening()
      }
    },
    bidOpening() {
      // moduleType 商务 22 技术 21
      // 商务标 1 ，技术标 0
      let type = this.moduleType
      let params = {
        openBiddingType: type == 22 ? 1 : 0,
        rfxId: this.$route.query.rfxId
      }
      this.$store.commit('startLoading')
      this.$API.rfxExpert
        .getExpertAdd(params)
        .then(() => {
          this.$store.commit('endLoading')
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$router.go(0)
          return
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    sendExpertRateScore() {
      // if (this.detailInfo.transferStatus != 37) {
      //   this.$toast({ content: "该单据暂未开标", type: "warning" });
      //   return;
      // }
      let params = {
        rfxCode: this.detailInfo.rfxCode
      }
      this.$API.rfxExpert.getExpertIssue(params).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        return
      })
    },
    //复制操作
    copyProject() {
      this.$dialog({
        data: {
          title: i18n.t('提示'),
          message: i18n.t("确认执行'复制'操作？")
        },
        success: () => {
          this.$emit('copyProject')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-info {
  min-height: 100px;
  flex-wrap: nowrap;
  display: flex;
  justify-content: space-between;
  background: rgba(245, 248, 251, 1);

  .lf-wrap {
    flex: 1;
    transition: all 2s ease-in-out;
    background: linear-gradient(rgba(99, 134, 193, 0.06), rgba(99, 134, 193, 0.06)),
      linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
    padding: 20px 20px 3px 20px;
    display: flex;
    line-height: 1;

    .name-wrap {
      flex: 1;
      .first-line {
        display: flex;
        align-items: center;
        .code {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
          margin-left: 10px;

          &-1 {
            color: rgba(237, 161, 51, 1);
            background: rgba(237, 161, 51, 0.1);
          }
          &-2 {
            color: #6386c1;
            background: rgba(99, 134, 193, 0.1);
          }
        }
      }

      .second-line {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 15px;
        div {
          margin: 10px 10px 0 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          @extend .text-ellipsis;

          &:not(:first-of-type) {
            color: rgba(157, 170, 191, 1);
          }
          &:first-of-type {
            max-width: 250px;
          }
        }
      }
    }

    .btns-wrap {
      max-width: 300px;
      flex-shrink: 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      /deep/ .mt-button {
        margin-right: 0;
        height: 30px;
        button {
          background: transparent;
          border-radius: 4px;
          box-shadow: unset;
          padding: 6px 12px 4px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
      }
    }
  }
  .timer-clock {
    // width: 220px;
    flex-shrink: 0;
  }
}

.top-form-generator {
  background: rgba(245, 248, 251, 1);
  // padding-bottom: 10px;
  /deep/ .mt-button {
    margin-right: 0;
    height: 30px;
    button {
      background: transparent;
      border-radius: 4px;
      box-shadow: unset;
      padding: 6px 12px 4px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
  }
}

.top-shrink {
  color: #9bb0cb;
  display: flex;
  justify-content: center;
}
</style>
