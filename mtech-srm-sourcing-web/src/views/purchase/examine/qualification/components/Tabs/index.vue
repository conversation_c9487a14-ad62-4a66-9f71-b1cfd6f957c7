<template>
  <mt-tabs
    v-bind="$attrs"
    :e-tab="false"
    :data-source="dataSource"
    @handleSelectTab="handleSelectTab"
  ></mt-tabs>
</template>

<script>
export default {
  props: {
    value: {
      type: Number,
      default: 0
    },
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleSelectTab(idx) {
      this.$emit('input', this.dataSource[idx].value ?? idx)
    }
  }
}
</script>
