import Deposit from './components/Deposit'
import Examine from './components/Examine'
import Tabs from './components/Tabs'

export default {
  components: {
    Tabs,
    Deposit,
    Examine,
    UploaderDialog: () => import('@/components/NormalEdit/Upload/uploaderDialog.vue'),
    // TopInfo: () => import("@/views/purchase/rfx/detail/components/topInfo.vue"),
    TopInfo: () => import('./components/TopInfo.vue')
  },
  data() {
    return {
      tabVal: 0,
      detailInfo: null,
      examineInfo: null,
      activatedRender: true
    }
  },
  computed: {
    toolbarVisible() {
      return this.detailInfo?.status === 0
    },
    examineId() {
      return this.examineInfo?.id
    },
    isOnlyEarnest() {
      return !!this.examineInfo?.onlyEnableEarnest
    },
    tabSource() {
      return this.isOnlyEarnest
        ? [{ title: this.$t('保证金'), value: 1 }]
        : [
            { title: this.$t('审查项目'), value: 0 },
            { title: this.$t('保证金'), value: 1 }
          ]
    }
  },
  async activated() {
    this.tabVal = 0
    this.activatedRender = false
    this.$nextTick(() => {
      this.activatedRender = true
    })
  },
  mounted() {
    this.getRFXDetailById()
    this.getRfxSupRelListAdd()
  },
  methods: {
    async getRFXDetailById() {
      const res = await this.$API.rfxDetail.getRFXDetailById({
        id: this.$route.query.rfxId
      })
      this.detailInfo = res.data
    },
    async getRfxSupRelListAdd() {
      const res = await this.$API.rfxSupRel.getRfxSupRelListAdd({
        id: this.$route.query.rfxId
      })
      this.examineInfo = res.data
    },
    async save(...arg) {
      if (this.$refs.deposit) {
        return await this.$refs.deposit.save(...arg)
      }
      return Promise.resolve(true)
    }
  }
}
