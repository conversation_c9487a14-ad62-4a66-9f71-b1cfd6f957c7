<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="app-form" ref="appForm" :model="form.data" :rules="form.rules">
        <mt-form-item
          prop="examineItemCode"
          :label="$t('审查项编码')"
          label-style="left"
          label-width="140px"
        >
          <mt-input v-model="form.data.examineItemCode"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="examineItemName"
          :label="$t('审查项名称')"
          label-style="left"
          label-width="140px"
        >
          <mt-input v-model="form.data.examineItemName"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="needFile"
          :label="$t('是否必须上传附件')"
          label-style="left"
          label-width="140px"
        >
          <mt-select
            :fields="{ text: 'text', value: 'value' }"
            :data-source="form.dataSource.needFile"
            v-model="form.data.needFile"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="examineItemSpec"
          :label="$t('审查项说明')"
          label-style="left"
          label-width="140px"
        >
          <mt-input v-model="form.data.examineItemSpec"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script src="./script.js" />

<style scoped lang="scss">
.app-form {
  .mt-form-item,
  .mt-input {
    width: 100% !important;
  }
}
</style>
