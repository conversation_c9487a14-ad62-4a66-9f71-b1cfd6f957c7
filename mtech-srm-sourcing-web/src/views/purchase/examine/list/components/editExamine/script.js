export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.submit,
          buttonModel: { isPrimary: 'true', content: this.$t('提交') }
        }
      ],
      form: {
        data: {
          examineItemCode: null,
          examineItemName: null,
          examineItemSpec: null,
          needFile: null
        },
        rules: {
          examineItemCode: [
            {
              required: true,
              message: this.$t('审查项目代码不能为空'),
              trigger: 'blur'
            }
          ],
          examineItemName: [
            {
              required: true,
              message: this.$t('审查项目名称不能为空'),
              trigger: 'blur'
            }
          ],
          examineItemSpec: [
            {
              required: true,
              message: this.$t('审查项目说明不能为空'),
              trigger: 'blur'
            }
          ],
          needFile: [
            {
              required: true,
              message: this.$t('是否必须上传附件不能为空'),
              trigger: 'blur'
            }
          ]
        },
        dataSource: {
          // 0非必须 1必须
          needFile: [
            { text: '否', value: 0 },
            { text: '是', value: 1 }
          ]
        }
      }
    }
  },
  mounted() {
    this.getInfo()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    getInfo() {
      if (this.modalData.data) {
        const formKeys = Object.keys(this.form.data)
        const pData = this.modalData.data
        if (!pData) return
        for (let i = 0; i < formKeys.length; i++) {
          const formKey = formKeys[i]
          if (typeof pData[formKey] !== 'undefined') {
            this.form.data[formKey] = pData[formKey]
          }
        }
      }
    },
    cancel() {
      this.emitConfirm()
    },
    emitConfirm(...arg) {
      this.$emit('confirm-function', ...arg)
    },
    async submit() {
      const validate = await this.formValidateAsync('appForm')
      if (!validate) {
        return
      }
      const editId = this.modalData?.data?.id
      const res = await this.$API.examineBaseItem
        .save({
          ...this.form.data,
          id: editId ? editId : undefined
        })
        .catch(() => {})
      if (res.code === 200) {
        this.emitConfirm()
      }
    },
    formValidateAsync(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    }
  }
}
