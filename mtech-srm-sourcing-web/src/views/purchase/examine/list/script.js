function columnData() {
  return [
    {
      type: 'checkbox',
      width: '60'
    },
    {
      field: 'examineItemCode',
      headerText: this.$t('审查项编码'),
      cssClass: 'field-content',
      cellTools: [
        {
          id: 'edit',
          icon: 'icon_solid_edit',
          title: this.$t('编辑')
        },
        { id: 'del', icon: 'icon_solid_Delete', title: this.$t('删除') }
      ]
    },
    {
      field: 'examineItemName',
      headerText: this.$t('审查项名称')
    },
    {
      field: 'examineItemSpec',
      headerText: this.$t('审查项目说明')
    },
    {
      field: 'needFile',
      headerText: this.$t('是否需要上传附件'),
      valueConverter: {
        type: 'map',
        map: { 0: this.$t('否'), 1: this.$t('是') }
      }
    }
  ]
}

export default {
  data() {
    return {
      form: {
        data: {},
        rules: {}
      },
      pageConfig: [
        {
          toolbar: [
            {
              id: 'add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增')
            },
            { id: 'del', icon: 'icon_solid_Delete', title: this.$t('删除') }
          ],
          fieldDefines: [],
          grid: {
            gridId: this.$permission.gridId.purchase.examine.list,
            asyncConfig: {
              url: this.$API.examineBaseItem.queryBuilder
            },
            columnData: columnData.call(this),
            dataSource: [],
            allowFiltering: false,
            class: 'pe-edit-grid custom-toolbar-grid'
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar } = e
      switch (toolbar.id) {
        case 'add':
          this.handleClickCellToolAdd()
          break
        case 'del':
          this.handleClickCellToolDel()
          break
        default:
          break
      }
    },
    handleClickCellTool(e) {
      const { tool } = e
      switch (tool.id) {
        case 'edit':
          this.showEditExamine(this.$t('编辑审查项'), e.data)
          break
        case 'del':
          this.delRecords([e.data])
          break
        default:
          break
      }
    },
    handleClickCellToolAdd() {
      this.showEditExamine(this.$t('新建审查项'))
    },
    handleClickCellToolDel() {
      const grid = this.$refs.templateRef.getCurrentTabRef().grid
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      this.delRecords(records)
    },
    delRecords(records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否删除数据?')
        },
        success: async () => {
          await this.$API.examineBaseItem
            .delete({
              ids: records.map((e) => e.id)
            })
            .catch(() => {})
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    showEditExamine(title, data) {
      const modal = () => import('./components/editExamine/index.vue')
      this.$dialog({
        modal,
        data: {
          title,
          data
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
