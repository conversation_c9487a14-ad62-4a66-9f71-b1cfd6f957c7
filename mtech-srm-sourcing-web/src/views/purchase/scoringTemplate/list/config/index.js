import { i18n, permission } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'Enable', icon: 'icon_table_enable', title: i18n.t('启用') },
  { id: 'Cancel', icon: 'icon_solid_Cancel', title: i18n.t('禁用') }
]
const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    width: '100',
    field: 'status',
    headerText: i18n.t('状态'), //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('启用'), cssClass: 'title-#eda133' },
        { status: 2, label: i18n.t('禁用'), cssClass: 'title-#ed5633' }
      ],
      fields: { text: 'label', value: 'status' }
    }
    // cellTools: [
    //   {
    //     id: "Stop",
    //     icon: "icon_solid_Cancel",
    //     title: i18n.t("禁用"),
    //     visibleCondition: (data) => {
    //       return data["status"] == 1;
    //     },
    //   },
    // ],
  },
  {
    width: '120',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    width: '200',
    field: 'scoreModelCode',
    headerText: i18n.t('评分模板编码'),
    cssClass: 'field-content'
  },
  {
    width: '150',
    field: 'scoreModelName',
    headerText: i18n.t('评分模板名称')
  },
  {
    width: '120',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
    // type: "date",
    // format: "yyyy-MM-dd",
  },
  {
    width: '120',
    field: 'createMethod',
    headerText: i18n.t('创建方式'),
    valueConverter: {
      type: 'map',
      map: [{ createMethod: 0, label: i18n.t('手动创建'), cssClass: '' }],
      fields: { text: 'label', value: 'createMethod' }
    }
  }
]
export const pageConfig = (url) => [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    gridId: permission.gridId['purchase']['scoringTemplate']['list'],
    grid: {
      allowFiltering: true,
      allowSorting: false,
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
