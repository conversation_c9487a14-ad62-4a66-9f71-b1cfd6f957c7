<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.scoringTemplateGradingRules.queryScoreDetails)
      // pageConfig: pageConfig(),
    }
  },
  methods: {
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Delete' || e.toolbar.id == 'Enable' || e.toolbar.id == 'Cancel')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 新增
      if (e.toolbar.id == 'Add') {
        this.handleBatchAdd()
      }
      // 删除
      else if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectGridRecords)
      }
      // 启用
      else if (e.toolbar.id == 'Enable') {
        this.handleBatchEnable(_selectGridRecords)
      }
      // 禁用
      else if (e.toolbar.id == 'Cancel') {
        this.handleBatchCancel(_selectGridRecords)
      }
    },
    //单元格按钮
    handleClickCellTool(e) {
      if (e.tool.id == 'Stop') {
        this.handleStopModel([e.data.id], 0)
      }
    },

    //删除
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.scoreDetailCode)
      })
      this.handleDelete(_selectIds)
    },
    //执行删除操作
    handleDelete(cords) {
      let _params = {
        codeList: cords
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          // this.$store.commit("startLoading");
          this.$API.scoringTemplateGradingRules.deleteScoringItems(_params).then((res) => {
            if (res.code == 200) {
              this.$store.commit('endLoading')
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              console.log('删除')
            }
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // 启用
    handleBatchEnable(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.scoreDetailCode)
      })
      console.log('............启用', _selectIds)
      // //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
      // let _disableStatusRecords = _selectGridRecords.filter((s) => {
      //   return s.status !== 3;
      // });
      // if (_disableStatusRecords.length > 0) {
      //   this.$toast({
      //     content: "只有'禁用'状态的数据可以执行该操作",
      //     type: "warning",
      //   });
      //   return;
      // } else {
      this.handleBatchEnables(_selectIds)
      // }
    },
    //执行启用操作
    handleBatchEnables(idLists) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行启用操作？')
        },
        success: () => {
          this.$API.scoringTemplateGradingRules
            .enableScoringItems({
              codeList: idLists
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    // 禁用
    handleBatchCancel(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.scoreDetailCode)
      })
      //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
      // let _disableStatusRecords = _selectGridRecords.filter((s) => {
      //   return s.status !== 3;
      // });
      // if (_disableStatusRecords.length > 0) {
      //   this.$toast({
      //     content: "只有'启用'状态的数据可以执行该操作",
      //     type: "warning",
      //   });
      //   return;
      // } else {
      this.handleBatchCancels(_selectIds)
      // }
    },
    //执行禁用操作
    handleBatchCancels(idLists) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行禁用操作？')
        },
        success: () => {
          this.$API.scoringTemplateGradingRules
            .disableScoringItems({
              codeList: idLists
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    //新增成本模型
    handleBatchAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/scoringTemplate/gradingRules/components/AddDialog" */ './components/AddDialog.vue'
          ),
        data: {
          title: this.$t('新增')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //单元格标题
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
