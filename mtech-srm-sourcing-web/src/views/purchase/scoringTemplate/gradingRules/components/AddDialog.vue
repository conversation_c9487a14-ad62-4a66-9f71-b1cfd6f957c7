<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="scoreDetailName" :label="$t('评分细项名称')">
          <mt-input
            v-model="formObject.scoreDetailName"
            float-label-type="Never"
            :placeholder="$t('请输入评分细项名称')"
            @change="DetailsName"
            maxlength="64"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="scoreItem" :label="$t('评分细则')">
          <mt-input
            v-model="formObject.scoreItem"
            float-label-type="Never"
            @change="gradingRules"
            :placeholder="$t('请输入评分细则')"
            maxlength="256"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemType" :label="$t('细项类型')">
          <mt-select
            style="width: 100%"
            :data-source="itemTypeList"
            v-model="formObject.itemType"
            :placeholder="$t('请选择细项类型')"
            :show-clear-button="true"
            float-label-type="Never"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      itemTypeList: [
        { text: this.$t('商务类'), value: '1' },
        { text: this.$t('技术类'), value: '0' }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        scoreDetailName: '', //评分细项名称
        scoreItem: '', // 评分细则
        itemType: '' //细项类型
      },
      //必填项
      formRules: {
        scoreDetailName: [
          {
            required: true,
            message: this.$t('必填项不能为空'),
            trigger: 'blur'
          }
        ],
        scoreItem: [
          {
            required: true,
            message: this.$t('必填项不能为空'),
            trigger: 'blur'
          }
        ],
        itemType: [
          {
            required: true,
            message: this.$t('细项类型不能为空'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //评分细项名称
    DetailsName(e) {
      if (e.length > 64) {
        this.$toast({
          content: this.$t('评分细项名称不得超过64个字符'),
          type: 'warning'
        })
      }
    },
    //评分细则
    gradingRules(e) {
      if (e.length > 256) {
        this.$toast({
          content: this.$t('评分细则不得超过256个字符'),
          type: 'warning'
        })
      }
    },
    //点击确认
    confirm() {
      if (this.formObject.scoreDetailName.length > 64) {
        this.$toast({
          content: this.$t('评分细项名称不得超过64个字符'),
          type: 'warning'
        })
        return
      }
      if (this.formObject.scoreDetailName.length == '') {
        this.$toast({
          content: this.$t('评分细项名称为空'),
          type: 'warning'
        })
        return
      }
      if (this.formObject.scoreItem.length > 256) {
        this.$toast({
          content: this.$t('评分细则不得超过256个字符'),
          type: 'warning'
        })
        return
      }
      if (this.formObject.scoreItem.length == '') {
        this.$toast({
          content: this.$t('评分细则为空'),
          type: 'warning'
        })
        return
      }
      if (this.formObject.itemType == '') {
        this.$toast({
          content: this.$t('细项类型为空'),
          type: 'warning'
        })
        return
      }
      let parameter = {
        scoreDetailName: this.formObject.scoreDetailName,
        scoreItem: this.formObject.scoreItem,
        itemType: this.formObject.itemType
      }
      this.$API.scoringTemplateGradingRules.scoreDetailSave(parameter).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$emit('confirm-function')
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
