<template>
  <div class="fields-config-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <div class="detail-info">
        <div class="name-wrap">
          <div class="first-line">
            <div class="divCode" v-if="$route.query.configId">
              <!--:disabled="!$route.query.configId || $route.query.configId.length === 0" -->
              <input
                class="strategy-code"
                type="text"
                disabled
                v-model="configInfo.scoreModelCode"
              />
              <span v-show="configInfo.status == 0" class="tags tags-0">{{ status[0] }}</span>
              <span v-show="configInfo.status == 1" class="tags tags-1">{{ status[1] }}</span>
              <span v-show="configInfo.status == 2" class="tags tags-2">{{ status[2] }}</span>
            </div>
            <!-- <section v-if="$route.query.configId">
              <span v-show="configInfo.status == 0" class="tags tags-0">{{
                status[0]
              }}</span>
              <span v-show="configInfo.status == 1" class="tags tags-1">{{
                status[1]
              }}</span>
              <span v-show="configInfo.status == 2" class="tags tags-2">{{
                status[2]
              }}</span>
            </section> -->
          </div>
        </div>
        <div class="btns-wrap">
          <mt-button v-show="configInfo.status != 1" @click.native="saveDetail">{{
            $t('保存')
          }}</mt-button>
          <!-- <mt-button @click.native="previewDetail">提交</mt-button> -->
          <mt-button @click.native="backToBusinessConfig">{{ $t('返回') }}</mt-button>
        </div>
      </div>
      <div class="detail-name">
        <div class="cai-name">
          <span>{{ $t('评分模板名称') }} : </span>
          <mt-input
            class="strategy-name"
            type="text"
            :disabled="configInfo.status == 1"
            float-label-type="Never"
            v-model="configInfo.scoreModelName"
            @change="scoreModelName"
            :placeholder="$t('这是评分模板名称')"
          />
        </div>
        <div class="cai-name">
          <span>{{ $t('业务类型') }} : </span>
          <mt-select
            :disabled="configInfo.status == 1"
            class="strategy-name"
            ref="businessTypeRef"
            v-model="configInfo.businessTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="businessTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择业务类型')"
            @change="onChangeBusinessTypeCode"
            :show-clear-button="true"
          ></mt-select>
        </div>
        <div class="cai-note">
          <span>{{ $t('备注') }} : </span
          ><mt-input
            :disabled="configInfo.status == 1"
            class="strategy-remark"
            float-label-type="Never"
            v-model="configInfo.remark"
            type="text"
            @change="strategyRemark"
            maxlength="200"
            :placeholder="$t('字数不超过200字')"
          />
        </div>
      </div>
    </div>
    <div class="tabsWeight">
      <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
      <div class="weight">
        <!-- 商务 -->
        <div v-show="tabIndex == 1" class="business">
          <mt-switch
            style="margin-left: 15px"
            v-model="autoMarkScore"
            :on-label="$t('自动评分')"
            :off-label="$t('自动评分')"
            :active-value="1"
            :inactive-value="0"
            @change="autoMarkScoreChange"
            :disabled="configInfo.status == 1"
          ></mt-switch>
          <span style="margin-left: 15px">{{ $t('自动评分公式') }}</span>
          <mt-tooltip class="weightInput" style="width: 400px" :content="scoreExpression"
            ><mt-input
              style="width: 400px"
              type="text"
              disabled
              v-model="scoreExpression"
              float-label-type="Never"
          /></mt-tooltip>
          <span>{{ $t('权重 : ') }}</span>
          <mt-input
            class="weightInput"
            type="number"
            v-model.number="weightobj.Business"
            float-label-type="Never"
            :disabled="configInfo.status == 1"
            @blur="changeBusiness"
          />
          <span class="percent">%</span>
        </div>
        <!-- 技术 -->
        <div v-show="tabIndex == 2" class="technology">
          <span>{{ $t('权重 : ') }}</span>
          <mt-input
            class="weightInput"
            type="number"
            :disabled="configInfo.status == 1"
            v-model.number="weightobj.Technical"
            float-label-type="Never"
            @blur="changeTechnical"
          />
          <span class="percent">%</span>
        </div>
      </div>
    </div>

    <!-- 公司 -->
    <!--  @tabParams="tabParams" -->
    <tab-params
      ref="params"
      v-show="tabIndex == 0"
      :details-data="detailsData"
      :status="configInfo.status"
    ></tab-params>
    <!-- 商务评分项 -->
    <business-scoring
      ref="businessScoring"
      :details-data="detailsData"
      :weightobj="weightobj"
      :auto-mark-score="autoMarkScore"
      v-show="tabIndex == 1"
      :status="configInfo.status"
    ></business-scoring>
    <!-- 技术评分项 -->
    <technical-scoring
      ref="technicalScoring"
      :details-data="detailsData"
      :weightobj="weightobj"
      v-show="tabIndex == 2"
      :status="configInfo.status"
    ></technical-scoring>
    <!-- 不区分 -->
    <not-distinguish-between
      ref="notDistinguishBetween"
      v-show="tabIndex == 3"
      :details-data="detailsData"
      :details-query="detailsQuery"
      :status="configInfo.status"
    ></not-distinguish-between>
  </div>
</template>
<script>
import { i18n } from '@/main.js'
export default {
  components: {
    //成本模型详情页
    //公司
    tabParams: () =>
      import(
        /* webpackChunkName: "router/purchase/scoringTemplate/details/tabs/detail" */ './tabs/company/index.vue'
      ),
    //商务评分项
    businessScoring: () =>
      import(
        /* webpackChunkName: "router/purchase/scoringTemplate/details/tabs/businessScoring" */ './tabs/businessScoring/index.vue'
      ),
    //技术评分项
    technicalScoring: () =>
      import(
        /* webpackChunkName: "router/purchase/scoringTemplate/details/tabs/technicalScoring" */ './tabs/technicalScoring/index.vue'
      ),
    //不区分
    notDistinguishBetween: () =>
      import(
        /* webpackChunkName: "router/purchase/scoringTemplate/details/tabs/notDistinguishBetween" */ './tabs/notDistinguishBetween/index.vue'
      )
  },
  data() {
    return {
      autoMarkScore: 0, //自动评分；0非自动评分，1自动评分
      scoreExpression: this.$t(
        '递减：{1-(该供应商报价-最低价)/最低价}*100 递增：{1+(供应商报价-最高价)/最高价}*100'
      ), //自动评分公式
      tabIndex: 0,
      //tabs标题
      tabSource: [
        {
          title: this.$t('公司')
        },
        {
          title: this.$t('商务评分项')
        },
        {
          title: this.$t('技术评分项')
        },
        {
          title: this.$t('不区分')
        }
      ],
      //表头信息
      configInfo: {
        scoreModelCode: '', //策略编号
        scoreModelName: '', //评分模板名称
        remark: '', //备注
        status: '1', //状态
        businessTypeId: '', //业务类型
        businessTypeCode: '',
        businessTypeName: ''
      },
      //表头信息状态
      status: [this.$t('草稿'), this.$t('启用'), this.$t('禁用')],
      //权重
      weightobj: {
        Business: 0, //商务
        Technical: 0 //技术
      },
      //细项所有启用
      detailsQuery: [],
      //公司
      scoreModelCompanyRelDTOS: [],
      //商务
      bizScoreModelItemDTOS: [],
      //技术
      techScoreModelItemDTOS: [],
      //不区分
      allScoreModelItemDTOS: [],
      //详情数据
      detailsData: {},
      businessTypeList: []
    }
  },
  mounted() {
    //点击code
    if (this.$route.query.configId) {
      let code = { scoreModelCode: this.$route.query.configId }
      this.configInfo.status = 1
      this.scoringTemplateDetails(code)
    } else {
      let parameter = {
        allScoreModelItemDTOS: [],
        bizScoreModelItemDTOS: [],
        remark: '',
        scoreModelCode: '',
        scoreModelCompanyRelDTOS: [],
        scoreModelName: '',
        techScoreModelItemDTOS: []
      }
      this.detailsData = parameter
      this.configInfo.status = 0
      localStorage.setItem('parameter', JSON.stringify(parameter))
    }

    // 2. 细项列表查询
    this.queryDetailsList()

    this.getBusinessTypeList()
  },
  methods: {
    autoMarkScoreChange(autoMarkScore) {
      if (autoMarkScore == 1) {
        this.$refs.businessScoring.pageConfig[0].toolbar = []
        this.weightobj.Business = 0
      } else {
        this.$refs.businessScoring.pageConfig[0].toolbar = [
          [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: i18n.t('新增')
            },
            { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
          ]
        ]
      }
    },
    // 1. 查询评分模板详情
    scoringTemplateDetails(code) {
      this.$API.scoringTemplateDetails.ItemQuery(code).then((res) => {
        // this.scoreModelCompanyRelDTOS = res.data.scoreModelCompanyRelDTOS;
        // this.scoreModelCompanyRelDTOS.forEach((item, index) => {
        //   item.lineIndex = index;
        // });
        if (res.code == 200) {
          this.autoMarkScore = res.data.bizAutoScore
          if (this.autoMarkScore == 1) {
            this.$refs.businessScoring.pageConfig[0].toolbar = []
            this.weightobj.Business = 0
          } else {
            this.$refs.businessScoring.pageConfig[0].toolbar = [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: i18n.t('新增')
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: i18n.t('删除')
                }
              ]
            ]
          }
          this.scoreExpression = res.data.scoreExpression
          this.configInfo = {
            scoreModelCode: res.data.scoreModelCode, //请输入策略编号
            scoreModelName: res.data.scoreModelName, //评分模板名称
            remark: res.data.remark, //备注
            status: res.data.status, //状态
            businessTypeId: res.data.businessTypeId,
            businessTypeCode: res.data.businessTypeId,
            businessTypeName: res.data.businessTypeId
          }
          if (this.configInfo.status != 1) {
            const toolbar = [
              {
                id: 'Add',
                icon: 'icon_solid_Createorder',
                title: i18n.t('新增')
              },
              {
                id: 'Delete',
                icon: 'icon_solid_Delete',
                title: i18n.t('删除')
              }
            ]
            this.$set(this.$refs.params.pageConfig[0], 'toolbar', [toolbar])
            this.$set(this.$refs.businessScoring.pageConfig[0], 'toolbar', [toolbar])
            this.$set(this.$refs.technicalScoring.pageConfig[0], 'toolbar', [toolbar])
            this.$set(this.$refs.notDistinguishBetween.pageConfig[0], 'toolbar', [toolbar])
          } else {
            this.$set(this.$refs.params.pageConfig[0], 'toolbar', [[]])
            this.$set(this.$refs.businessScoring.pageConfig[0], 'toolbar', [[]])
            this.$set(this.$refs.technicalScoring.pageConfig[0], 'toolbar', [[]])
            this.$set(this.$refs.notDistinguishBetween.pageConfig[0], 'toolbar', [[]])
          }
          this.detailsData = res.data
          //商务
          if (res.data.bizAutoScore == 1) {
            this.weightobj.Business = this.detailsData.bizAutoScoreWeight
            this.weightobj.Technical = 100 - this.detailsData.bizAutoScoreWeight
          } else {
            if (this.detailsData.bizScoreModelItemDTOS.length > 0) {
              this.weightobj.Business = this.detailsData.bizScoreModelItemDTOS[0].itemWeight
            } else {
              this.weightobj.Business = 0
            }
          }

          //技术
          if (this.detailsData.techScoreModelItemDTOS.length > 0) {
            this.weightobj.Technical = this.detailsData.techScoreModelItemDTOS[0].itemWeight
          } else {
            this.weightobj.Technical = 0
          }
          let parameter = JSON.parse(localStorage.parameter)
          // parameter.scoreModelCode = res.data.scoreModelCode;
          // parameter.scoreModelName = res.data.scoreModelName;
          // parameter.remark = res.data.remark;
          // parameter.status = res.data.status;
          parameter = res.data
          localStorage.setItem('parameter', JSON.stringify(parameter))
        }
      })
    },
    // 2. 细项列表查询
    queryDetailsList() {
      this.$API.scoringTemplateDetails
        .queryScoreDetails({
          page: { current: 1, size: 1000 },
          defaultRules: [
            {
              condition: 'and',
              field: 'status',
              operator: 'equal',
              value: 1 //状态
            }
          ]
        })
        .then((res) => {
          if (res.code == 200) {
            this.detailsQuery = res.data.records
          }
        })
    },
    //备注
    strategyRemark(e) {
      if (e.length > 200) {
        this.$toast({
          content: this.$t('备注不得超过200字符'),
          type: 'warning'
        })
      }
      let parameter = JSON.parse(localStorage.parameter)
      parameter.remark = e
      localStorage.setItem('parameter', JSON.stringify(parameter))
    },
    //模板名称
    scoreModelName(e) {
      if (e.length > 64) {
        this.$toast({
          content: this.$t('模板名称不得超过64个字符'),
          type: 'warning'
        })
      }
      let parameter = JSON.parse(localStorage.parameter)
      parameter.scoreModelName = e
      localStorage.setItem('parameter', JSON.stringify(parameter))
    },
    //切换tabs
    handleSelectTab(e) {
      this.tabIndex = e
    },
    //点击返回
    backToBusinessConfig() {
      this.$router.go(-1)
    },
    // 商务评分项权重 change
    changeBusiness(e) {
      if (!isNaN(e)) {
        if (e > 100 || e < 0) {
          this.weightobj.Business = 0
          this.$toast({
            content: this.$t('请输入0-100之间的数值'),
            type: 'warning'
          })
        } else {
          this.weightobj.Business = e
          this.weightobj.Technical = 100 - e
        }
      } else {
        this.weightobj.Business = 0
        this.$toast({
          content: this.$t('请输入0-100之间的数值'),
          type: 'warning'
        })
      }
    },
    //技术评分权重 change
    changeTechnical(e) {
      if (!isNaN(e)) {
        if (e > 100 || e < 0) {
          this.weightobj.Technical = 0
          this.$toast({
            content: this.$t('请输入0-100之间的数值'),
            type: 'warning'
          })
        } else {
          this.weightobj.Technical = e
          this.weightobj.Business = 100 - e
        }
      } else {
        this.weightobj.Technical = 0
        this.$toast({
          content: this.$t('请输入0-100之间的数值'),
          type: 'warning'
        })
      }
    },
    //点击保存
    saveDetail() {
      let parameter = JSON.parse(localStorage.parameter)
      let bizScoreNumber = 0
      let techScoreNumber = 0
      if (parameter.scoreModelName == '') {
        this.$toast({
          content: this.$t('评分模板名称不能为空'),
          type: 'warning'
        })
        return
      }
      if (parameter.scoreModelName.length > 64) {
        this.$toast({
          content: this.$t('模板名称不得超过64个字符'),
          type: 'warning'
        })
        return
      }
      if (!parameter.businessTypeName || parameter.businessTypeName == '') {
        this.$toast({
          content: this.$t('业务类型不能为空'),
          type: 'warning'
        })
        return
      }
      // if (parameter.remark == "") {
      //   this.$toast({
      //     content: this.$t("备注不能为空"),
      //     type: "warning",
      //   });
      //   return;
      // }
      if (parameter.scoreModelName.length > 200) {
        this.$toast({
          content: this.$t('备注不得超过200个字符'),
          type: 'warning'
        })
        return
      }
      if (parameter.bizScoreModelItemDTOS.length > 0) {
        parameter.bizScoreModelItemDTOS.forEach((item) => {
          item.itemWeight = this.weightobj.Business
          bizScoreNumber += Number(item.detailWeight)
        })
        if (bizScoreNumber != 100) {
          this.$toast({
            content: this.$t('商务权重值总和不等于100,请重新输入'),
            type: 'warning'
          })
          return
        }
      }
      if (parameter.techScoreModelItemDTOS.length > 0) {
        parameter.techScoreModelItemDTOS.forEach((item) => {
          item.itemWeight = this.weightobj.Technical
          techScoreNumber += Number(item.detailWeight)
        })
        if (techScoreNumber != 100) {
          this.$toast({
            content: this.$t('技术权重值总和不等于100,请重新输入'),
            type: 'warning'
          })
          return
        }
      }
      parameter.allScoreModelItemDTOS.forEach((item) => {
        item.itemWeight = 100
      })

      parameter.bizAutoScore = this.autoMarkScore
      if (this.autoMarkScore == 1) {
        parameter.bizScoreModelItemDTOS = []
        parameter.bizAutoScoreWeight = this.weightobj.Business
      }
      parameter.scoreExpression = null
      this.$store.commit('startLoading')
      this.$API.scoringTemplateDetails.AddUpdateScoringDetails(parameter).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.$router.go(-1)
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
      this.$store.commit('endLoading')
    },
    previewDetail() {
      this.$dialog({
        // modal: () =>
        //   import(
        //     /* webpackChunkName: "router/purchase/settings/cost/detail/components/modelDetailDialog" */ "./components/modelDetailDialog/index.vue"
        //   ),
        // data: {
        //   title: "成本模型详情预览",
        // },
        success: () => {
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    },
    getBusinessTypeList() {
      // 业务类型 - businessType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .then((res) => {
          this.businessTypeList = res.data
        })
    },
    onChangeBusinessTypeCode({ itemData }) {
      if (itemData?.itemName) {
        let parameter = JSON.parse(localStorage.parameter)
        parameter.businessTypeId = itemData.id
        parameter.businessTypeCode = itemData.itemCode
        parameter.businessTypeName = itemData.itemName
        localStorage.setItem('parameter', JSON.stringify(parameter))
        this.configInfo.businessTypeId = itemData.id
        this.configInfo.businessTypeCode = itemData.itemCode
        this.configInfo.businessTypeName = itemData.itemName
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.fields-config-page {
  width: 100%;
  height: 100%;
  padding-top: 20px;
  background: #fafafa;
}
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-info {
  // flex-shrink: 0;
  width: 100%;
  height: 160px;
  box-sizing: border-box;
  padding: 20px 20px 0 20px;
  // justify-content: space-between;
  background: rgba(245, 248, 251, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 0 8px 0 0;

  .detail-info {
    display: flex;
    line-height: 1;

    .name-wrap {
      flex: 1;
      .first-line {
        .divCode {
          .strategy-code {
            font-size: 20px;
            font-family: DINAlternate;
            color: rgba(41, 41, 41, 1);
            font-weight: bold;
            outline: none;
            border: none;
            background: transparent;
            width: 300px;
          }
          .tags {
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: 500;
            padding: 4px;
            border-radius: 2px;
            margin-left: 10px;
          }
          .tags-0 {
            color: rgb(155, 170, 193);
            background: rgba(155, 170, 193, 0.1);
          }
          .tags-1 {
            color: rgba(237, 161, 51, 1);
            background: rgba(237, 161, 51, 0.1);
          }
          .tags-2 {
            color: rgb(237, 86, 51);
            background: rgba(237, 86, 51, 0.1);
          }
        }
        // section {
        //   .tags {
        //     font-size: 12px;
        //     font-family: PingFangSC;
        //     font-weight: 500;
        //     padding: 4px;
        //     border-radius: 2px;
        //   }
        //   .tags-0 {
        //     color: rgb(155, 170, 193);
        //     background: rgba(155, 170, 193, 0.1);
        //     margin-left: 50px;
        //   }
        //   .tags-1 {
        //     color: rgba(237, 161, 51, 1);
        //     background: rgba(237, 161, 51, 0.1);
        //     margin-left: 50px;
        //   }
        //   .tags-2 {
        //     color: rgb(237, 86, 51);
        //     background: rgba(237, 86, 51, 0.1);
        //     margin-left: 50px;
        //   }
        // }
      }
    }
    .btns-wrap {
      /deep/ .mt-button {
        margin-right: 0;
        button {
          width: 76px;
          height: 34px;
          background: transparent;
          //border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          box-shadow: unset;
          padding: 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
      }
    }
  }

  .detail-name {
    .cai-name {
      width: 300px;
      // height: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: block;
        width: 92px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
      }
      .strategy-name {
        width: 200px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        outline: none;
        border: none;
        background: transparent;
        &:focus {
          outline: none;
          border: none;
          background: transparent;
        }
      }
    }
    .cai-note {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: inline-block;
        width: 40px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
      }
      .strategy-remark {
        display: inline-block;
        margin-left: 10px;
        width: 100%;
        // border: 1px solid #d7d7d7;
        // outline: #e8e8e8;
        font-size: 14px;
      }
    }
    /deep/.e-input-group {
      border: none;
    }
  }
}
.tabsWeight {
  margin-top: 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .mt-tabs {
    display: inline-block;
    vertical-align: middle;
    background-color: transparent;
    /deep/.mt-tabs-container {
      background-color: transparent;
    }
  }
  .weight {
    margin-right: 20px;
    font-size: 14px;
    position: relative;
    span {
      display: inline-block;
    }
    .weightInput {
      display: inline-block;
      width: 40px;
      /deep/.e-input {
        text-align: center;
      }
    }
    .percent {
      font-size: 12px;
    }
  }
}

.config-container {
  flex: 1;
  margin-top: 10px;
  overflow: auto;
  min-width: 1000px;
}
/deep/ .mt-data-grid {
  .checked {
    color: #54bf00;
    font-size: 16px;
  }

  .uncheck {
    color: #ed5633;
    font-size: 16px;
  }
}
/deep/ .config-custom-tabs {
  background: #fafafa;
  padding: 0;
  /deep/ .e-tab-header {
    background: transparent;
  }
  .tab-wrap {
    padding: 0;
    height: 50px;
  }
  ul.tab-container {
    display: flex;
    li.tab-item {
      flex-shrink: 0;
      color: #292929;
      font-size: 14px;
      font-weight: 400;
      height: 46px;
      line-height: 46px;
      min-width: 60px;
      position: relative;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .item-content {
        cursor: pointer;
        padding: 0;
        display: flex;
        position: relative;
        min-width: 60px;
        color: #4f5b6d;
        align-items: center;
        justify-content: center;

        .config-checkbox {
          &.mt-icon-a-icon_MultipleChoice_on {
            color: #6386c1;
          }
          &.mt-icon-a-icon_MultipleChoice_off {
            color: #9daabf;
          }
        }
        &.fixed {
          .config-checkbox {
            &.mt-icon-a-icon_MultipleChoice_on {
              color: #9a9a9a;
            }
          }
        }
      }

      &.active,
      &:hover {
        border-color: transparent;
        background: transparent;
      }

      &.active {
        color: #00469c;
        font-weight: 600;

        &:after {
          content: '';
          border: 1px solid #00469c;
          width: 60%;
          animation: active-tab 0.3s ease;
          position: absolute;
          bottom: 6px;
          left: 20%;
        }
        @keyframes active-tab {
          0% {
            width: 0;
            left: 50%;
          }
          100% {
            width: 60%;
            left: 20%;
          }
        }
      }
    }
  }
}
/deep/ .ext-tabs-container {
  height: 100%;
  width: 100%;
  background: #e8e8e8;
  .left-nav-tabs {
    flex-shrink: 0;
    background: #fff;
    width: 172px;
    margin-right: 10px;
    .nav-container {
      .nav-item {
        height: 54px;
        padding: 0 20px;
        position: relative;
        background: #ffffff;
        color: #232b39;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e8e8e8;
        .svg-option-item {
          display: flex;
        }
        .config-checkbox {
          &.mt-icon-a-icon_MultipleChoice_on {
            color: #6386c1;
          }
          &.mt-icon-a-icon_MultipleChoice_off {
            color: #9daabf;
          }
        }
        .config-arrow {
          display: none;
        }
        &.active {
          color: #6386c1;
          background: #f5f6f9;
          .mt-icons {
            display: block;
          }
        }
        &:hover {
          color: #6386c1;
          background: #fafbfd;
        }
      }
    }
  }
  .ext-content-container {
    flex: 1;
    background: #fff;
  }
}
/deep/ .ext-files-container {
  height: 100%;
  .tree-view--wrap {
    min-width: 300px;
    box-shadow: inset -1px 0 0 0 rgba(232, 232, 232, 1);
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 50px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 10px;
    }
  }
}
/deep/ td.e-rowcell {
  &:first-of-type {
    &.e-templatecell {
      padding-right: 0 !important;
    }
    .field-group {
      border-right: 1px solid #e8e8e8;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;
      display: flex;
    }
  }
}
// .mt-tabs {
//   flex-shrink: 0;
// }
</style>
