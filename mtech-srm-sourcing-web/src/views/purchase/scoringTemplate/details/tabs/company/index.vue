<!--公司-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { utils } from '@mtech-common/utils'
import { i18n } from '@/main.js'
export default {
  props: {
    detailsData: {
      type: Object,
      default: () => {}
    },
    status: {
      type: Number,
      default: () => 1
    }
  },
  data() {
    return {
      pageConfig,
      companyData: [],
      scoreModelCompanyRelDTOS: []
    }
  },

  watch: {
    'detailsData.scoreModelCompanyRelDTOS': {
      handler() {
        this.detailsDataArr()
      },
      immediate: true
    }
  },
  mounted() {
    this.accessCompanyData()
    if (this.status == 0) {
      const toolbar = [
        {
          id: 'Add',
          icon: 'icon_solid_Createorder',
          title: i18n.t('新增')
        },
        {
          id: 'Delete',
          icon: 'icon_solid_Delete',
          title: i18n.t('删除')
        }
      ]
      this.$set(this.pageConfig[0], 'toolbar', [toolbar])
    }
  },
  methods: {
    accessCompanyData() {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .then((res) => {
          if (res.code == 200) {
            this.companyData = res.data
          }
        })
    },
    detailsDataArr() {
      this.$set(this.pageConfig[0].grid, 'dataSource', this.detailsData.scoreModelCompanyRelDTOS)
    },

    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      console.error(_selectGridRecords, '_selectGridRecords')
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 新增
      if (e.toolbar.id == 'Add') {
        this.handleBatchAdd()
      }
      // 删除
      else if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id === 'filterDataByLocal') {
        this.handleFilterDataGrid()
      }
    },
    //新增
    handleBatchAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/scoringTemplate/details/company/components/AddDialog" */ './components/AddDialog.vue'
          ),
        data: {
          title: this.$t('新增公司'),
          companyData: this.companyData
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          this.scoreModelCompanyRelDTOS = _dataSource
          let parameter = JSON.parse(localStorage.parameter)
          parameter.scoreModelCompanyRelDTOS = _dataSource
          localStorage.setItem('parameter', JSON.stringify(parameter))
        }
      })
    },
    //删除
    handleBatchDelete(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.id)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.id) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _res)
          this.scoreModelCompanyRelDTOS = _res
          let parameter = JSON.parse(localStorage.parameter || '{}')
          parameter.scoreModelCompanyRelDTOS = _res
          localStorage.setItem('parameter', JSON.stringify(parameter))
        }
      })
    },
    // 查询数据
    handleFilterDataGrid() {}
  }
}
</script>
