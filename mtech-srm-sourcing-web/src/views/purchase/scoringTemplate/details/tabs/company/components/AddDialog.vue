<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="companyName" :label="$t('公司名称')">
          <mt-select
            v-model="formObject.companyName"
            float-label-type="Never"
            :data-source="companyData"
            :fields="{ text: 'orgName', value: 'orgName' }"
            @change="changeTaxItem"
            :placeholder="$t('请选择公司名称')"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司编码')">
          <mt-input
            v-model="formObject.companyCode"
            float-label-type="Never"
            disabled
            :placeholder="$t('请先选择公司名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        companyName: '', //公司名称
        companyCode: '' // 公司编码
      },
      //必填项
      formRules: {
        companyName: [
          {
            required: true,
            message: this.$t('公司名称'),
            trigger: 'blur'
          }
        ],
        companyCode: [
          {
            required: true,
            message: this.$t('公司编码'),
            trigger: 'blur'
          }
        ]
      },
      company: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    companyData() {
      return this.modalData.companyData
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.company = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    changeTaxItem(e) {
      this.formObject.companyCode = e.itemData.orgCode
    },
    //点击确认
    confirm() {
      // let parameter = {
      //   companyName: this.formObject.companyName,
      //   companyCode: this.formObject.companyCode,
      // };
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.company) {
            delete params.id
          }
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
