// import Vue from "vue";
import { i18n, permission } from '@/main.js'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Delete", icon: "icon_solid_Delete", title: i18n.t("删除") },
]
const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  }
]
export const pageConfig = [
  {
    toolbar: [toolbar],
    useBaseConfig: false,
    useToolTemplate: false,
    gridId: permission.gridId['purchase']['scoringTemplate']['detail']['tabs']['company'],
    grid: {
      allowFiltering: true,
      columnData: columnData,
      allowPaging: false,
      dataSource: []
    }
  }
]
