<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="scoreDetailName" :label="$t('评分项名称')">
          <mt-select
            v-model="formObject.scoreDetailName"
            float-label-type="Never"
            :data-source="seletArr"
            :fields="{ text: 'scoreDetailName', value: 'scoreDetailName' }"
            :disabled="modalType === 'edit'"
            @change="changeTaxItem"
            :placeholder="$t('请选择评分项名称')"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="scoreDetailCode" :label="$t('评分项编码')">
          <mt-input
            v-model="formObject.scoreDetailCode"
            float-label-type="Never"
            disabled
            :placeholder="$t('请输入评分项编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="scoreItem" :label="$t('评分细则')">
          <mt-input
            v-model="formObject.scoreItem"
            float-label-type="Never"
            disabled
            :placeholder="$t('请输入评分细则')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="minScore" :label="$t('最低分')">
          <mt-input-number
            v-model.number="formObject.minScore"
            float-label-type="Never"
            :placeholder="$t('请输入最低分')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="maxScore" :label="$t('最高分')">
          <mt-input-number
            v-model.number="formObject.maxScore"
            float-label-type="Never"
            :placeholder="$t('请输入最高分')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="defaultScore" :label="$t('缺省分值')">
          <mt-input-number
            v-model.number="formObject.defaultScore"
            float-label-type="Never"
            :placeholder="$t('请输入缺省分值')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="detailWeight" :label="$t('权重')">
          <mt-input-number
            v-model.number="formObject.detailWeight"
            float-label-type="Never"
            :placeholder="$t('请输入权重')"
          ></mt-input-number>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        scoreDetailCode: '', //评分项编码
        scoreDetailName: '', // 评分项名称
        scoreItem: '', //评分细则
        minScore: '', //最低分
        maxScore: '', //最高分
        defaultScore: '', //缺省分值
        detailWeight: '' //权重
      },
      //必填项
      formRules: {
        scoreDetailName: [
          {
            required: true,
            message: this.$t('评分项名称'),
            trigger: 'blur'
          }
        ],
        scoreItem: [
          {
            required: true,
            message: this.$t('评分细则'),
            trigger: 'blur'
          }
        ],
        minScore: [
          {
            required: true,
            message: this.$t('最低分'),
            trigger: 'blur'
          },
          {
            validator: this.validateScopeScore,
            trigger: 'blur'
          }
        ],
        maxScore: [
          {
            required: true,
            message: this.$t('最高分'),
            trigger: 'blur'
          },
          {
            validator: this.validateMaxScore,
            trigger: 'blur'
          }
        ],
        defaultScore: [
          {
            required: true,
            message: this.$t('缺省分值'),
            trigger: 'blur'
          },
          {
            validator: this.validateDefaultScore,
            trigger: 'blur'
          }
        ],
        detailWeight: [
          {
            required: true,
            message: this.$t('权重'),
            trigger: 'blur'
          },
          {
            validator: this.validateScopeScore,
            trigger: 'blur'
          }
        ]
      },
      company: false,
      seletArr: [] //细项数据
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    detailArr() {
      return this.modalData.detailArr
    },
    modalType() {
      return this.modalData?.type || 'add'
    }
  },
  mounted() {
    this.getGradeItem()
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.company = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    getGradeItem() {
      this.$API.scoringTemplateDetails.queryByType(0).then((res) => {
        this.seletArr = res.data.scoreDetailRespList
      })
    },
    //联动值
    changeTaxItem(e) {
      this.formObject.scoreDetailCode = e.itemData.scoreDetailCode
      this.formObject.scoreItem = e.itemData.scoreItem
    },
    validateMaxScore(rule, value, callback) {
      const realValue = +value || 0
      const minScore = +this.formObject.minScore || 0

      if (realValue < 0 || realValue > 100) {
        callback(new Error(this.$t('请输入0-100之间的值')))
      } else if (minScore > realValue) {
        callback(new Error(this.$t('最高分必须大于最低分')))
      } else {
        callback()
      }
    },
    validateScopeScore(rule, value, callback) {
      const realValue = +value || 0

      if (realValue >= 0 && realValue <= 100) {
        callback()
      } else if (realValue < 0 || realValue > 100) {
        callback(new Error(this.$t('请输入0-100之间的值')))
      } else {
        callback()
      }
    },
    validateDefaultScore(rule, value, callback) {
      const realValue = +value || 0
      const minScore = +this.formObject.minScore || 0
      const maxScore = +this.formObject.maxScore || 0

      if (realValue < 0 || realValue > 100) {
        callback(new Error(this.$t('请输入0-100之间的值')))
      } else if (minScore > realValue || maxScore < realValue) {
        callback(new Error(this.$t('缺省分值应位于最低分和最高分之间')))
      } else {
        callback()
      }
    },
    //点击确认
    confirm() {
      // let parameter = {
      //   scoreDetailCode: this.formObject.scoreDetailCode,
      //   scoreDetailName: this.formObject.scoreDetailName,
      // };

      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.company) {
            delete params.id
          }
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 390px;
        }
      }
    }
  }
}
</style>
