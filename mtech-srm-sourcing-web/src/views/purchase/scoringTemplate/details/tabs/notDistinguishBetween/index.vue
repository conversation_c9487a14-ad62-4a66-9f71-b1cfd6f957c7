<!--不区分-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { utils } from '@mtech-common/utils'
import cloneDeep from 'lodash/cloneDeep'
export default {
  props: {
    detailsQuery: {
      type: Array,
      default: () => []
    },
    detailsData: {
      type: Object,
      default: () => {}
    },
    status: {
      type: Number,
      default: () => 1
    }
  },
  watch: {
    detailsQuery() {
      this.detailsQueryArr()
    },
    'detailsData.allScoreModelItemDTOS': {
      handler() {
        this.detailsDataArr()
      },
      immediate: true
    }
  },
  data() {
    return {
      pageConfig: null,
      //--维护商务评分项数组--
      allScoreModelItemDTOS: [
        // {
        //   createMethod: "", //创建方式 0手动创建
        //   defaultScore: "", //缺省分值
        //   detailWeight: "", //细项权重
        //   itemWeight: "", //明细权重
        //   maxScore: "", //最高分
        //   minScore: "", //最低分
        //   scoreDetailCode: "", //评分细项编码
        //   scoreDetailName: "", //评分细项名称
        //   scoreItem: "", //评分细则
        //   scoreItemCode: "", //评分明细编码
        //   scoreModelCode: "", //评分模板编码
        //   scoreType: "", //评分项类型；0不区分，1商务评分项，2技术评分项
        // },
      ],
      detailArr: []
    }
  },
  mounted() {
    // 直接取url上status
    this.pageConfig = pageConfig(this.$route?.query.status == '0')
  },
  methods: {
    detailsDataArr() {
      this.$set(this.pageConfig[0].grid, 'dataSource', this.detailsData.allScoreModelItemDTOS)
    },
    detailsQueryArr() {
      this.detailArr = this.detailsQuery
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 新增
      if (e.toolbar.id == 'Add') {
        this.handleBatchAdd()
      }
      // 删除
      else if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectGridRecords)
      }
    },
    // 点击cellTitle事件
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'scoreDetailCode') {
        this.openDialog('edit', data)
      }
    },
    //新增
    handleBatchAdd() {
      this.openDialog('add')
    },
    // open 弹框事件
    openDialog(type, data) {
      const _title = type === 'add' ? this.$t('新增不区分') : this.$t('编辑不区分')
      const _data = type === 'add' ? null : cloneDeep(data)
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/scoringTemplate/details/businessScoring/components/AddDialog" */ './components/AddDialog.vue'
          ),
        data: {
          title: _title,
          data: _data,
          type
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])

          if (type === 'edit') {
            // 编辑 - 根据字段scoreDetailCode替换
            _dataSource = _dataSource.map((item) => {
              if (item.scoreDetailCode === data.scoreDetailCode) {
                return data
              }
              return item
            })
          } else {
            // 新增 - 直接添加
            _dataSource.push(data)
          }
          // 添加lineInex
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          let parameter = JSON.parse(localStorage.parameter)
          parameter.allScoreModelItemDTOS = _dataSource
          localStorage.setItem('parameter', JSON.stringify(parameter))
        }
      })
    },
    //删除
    handleBatchDelete(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.scoreDetailCode)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.scoreDetailCode) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _res)
          this.allScoreModelItemDTOS = _res
          let parameter = JSON.parse(localStorage.parameter)
          parameter.allScoreModelItemDTOS = _res
          localStorage.setItem('parameter', JSON.stringify(parameter))
        }
      })
    }
  }
}
</script>
