import { i18n, permission } from '@/main.js'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
  }
]
const columnData = (canEdit) => [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'scoreDetailCode',
    headerText: i18n.t('评分项编码'),
    cellTools: canEdit ? [] : null
  },
  {
    field: 'scoreDetailName',
    headerText: i18n.t('评分项名称')
  },
  {
    field: 'scoreItem',
    headerText: i18n.t('评分细则')
  },
  {
    field: 'minScore',
    headerText: i18n.t('最低分')
  },

  {
    field: 'maxScore',
    headerText: i18n.t('最高分')
  },
  {
    field: 'defaultScore',
    headerText: i18n.t('缺省分值')
  },
  {
    field: 'detailWeight',
    headerText: i18n.t('权重')
  }
]
export const pageConfig = (canEdit) => {
  return [
    {
      toolbar: canEdit ? [toolbar] : [],
      useBaseConfig: false,
      useToolTemplate: false,
      gridId: permission.gridId['purchase']['scoringTemplate']['detail']['tabs']['business'],
      grid: {
        allowFiltering: true,
        columnData: columnData(canEdit),
        allowPaging: false,
        dataSource: []
      }
    }
  ]
}
