// import Vue from "vue";
import { i18n, permission } from '@/main.js'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title:  i18n.t("新增") },
  // { id: "delete", icon: "icon_solid_edit", title:  i18n.t("删除") },
  { id: 'save', icon: 'icon_solid_edit', title: i18n.t('操作记录') }
]

const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '120',
    field: 'subAccount',
    headerText: i18n.t('子账户'),
    cssClass: 'field-content'
  },
  {
    width: '120',
    field: 'expertSource',
    headerText: i18n.t('专家来源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('内部'), 1: i18n.t('外聘') }
    }
  },
  {
    width: '120',
    field: 'expertName',
    headerText: i18n.t('专家姓名')
  },
  {
    width: '120',
    field: 'expertLevel',
    headerText: i18n.t('专家级别'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('初级'), 1: i18n.t('中级'), 2: i18n.t('高级') }
    }
  },
  {
    width: '120',
    field: 'expertCategory',
    headerText: i18n.t('专家类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('商务'),
        1: i18n.t('技术'),
        2: i18n.t('商务&技术'),
        4: i18n.t('现场考察')
      }
    }
  },
  {
    width: '120',
    field: 'mobilePhone',
    headerText: i18n.t('移动电话')
  },
  {
    width: '120',
    field: 'createTime',
    headerText: i18n.t('注册日期'),
    valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
  }
]

export const pageConfig = (url) => [
  {
    toolbar,
    useToolTemplate: false,
    gridId: permission.gridId['purchase']['expertInfo'],
    grid: {
      allowFiltering: true,
      allowSorting: false,
      columnData,
      asyncConfig: { url }
    }
  }
]
