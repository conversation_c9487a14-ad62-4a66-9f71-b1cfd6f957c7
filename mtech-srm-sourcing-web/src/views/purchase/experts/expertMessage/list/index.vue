<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.expert.expertLists)
    }
  },
  methods: {
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if ((_selectGridRecords.length <= 0 && e.toolbar.id == 'delete') || e.toolbar.id == 'save') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.$router.push({
          path: `purchase-expertMessage-detail`
        })
      } else if (e.toolbar.id == 'delete') {
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id == 'save') {
        this.handleBatchSave()
      }
    },
    // handleClickAdd() {
    //   this.$dialog({
    //     modal: () =>
    //       import(
    //         /* webpackChunkName: "router/purchase/expert/list/components/index" */ "./components/index.vue"
    //       ),
    //     data: {
    //       title: this.$t("新增专家类型"),
    //     },
    //     success: () => {
    //       this.$refs.templateRef.refreshCurrentGridData();
    //     },
    //   });
    // },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //删除规则
    handleDeleteConfig(idList) {
      let _params = {
        idList
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.expert.expertDeletes(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleBatchSave() {},
    //单元格标题
    handleClickCellTitle(e) {
      if (e.field == 'subAccount') {
        this.$router.push({
          path: `purchase-expertMessage-detail`,
          query: {
            configId: e.data.id
          }
        })
      }
    }
  }
}
</script>
<style lang="scss">
.pem-container {
  .action-boxs {
    .delete-line {
      color: #ed5633;
    }
  }
}
</style>
