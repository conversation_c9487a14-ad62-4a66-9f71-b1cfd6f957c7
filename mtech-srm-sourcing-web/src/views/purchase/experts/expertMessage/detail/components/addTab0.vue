<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject">
        <mt-form-item prop="specialtyName" :label="$t('专业领域名称')">
          <mt-input
            v-model="formObject.specialtyName"
            float-label-type="Never"
            :placeholder="$t('请输入领域名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="specialtyCode" :label="$t('专业领域代码')">
          <mt-input
            v-model="formObject.specialtyCode"
            float-label-type="Never"
            :placeholder="$t('请输入专业代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="primarySpecialtyField" :label="$t('首要专业领域')">
          <mt-select
            :data-source="exTypeArr"
            v-model="formObject.specialtyCode"
            :show-clear-button="true"
            :placeholder="$t('请选择首要专业领域')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      editStatus: false,
      formObject: {
        id: 0,
        primarySpecialtyField: '', //首要专业领域
        specialtyCode: '', //专业领域代码
        specialtyName: '' //专业领域名称
      },
      exTypeArr: [
        { text: this.$t('否'), value: 0 },
        { text: this.$t('是'), value: 1 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
