<template>
  <div class="supplier-box mt-flex" style="width: 100%">
    <div class="miam-container mt-flex-direction-column" style="width: 100%">
      <div class="operate-bars">
        <div class="operate-bar">
          <div class="op-item mt-flex" @click="submitForm">
            {{ $t('返回') }}
          </div>

          <!-- <div class="op-item mt-flex">
            <i class="mt-icons mt-icon-Delete"></i>
            提交
          </div>
          <div class="op-item mt-flex" @click="expertSave">
            <i class="mt-icons mt-icon-icon_table_new"></i>
            保存
          </div> -->
        </div>
        <!-- 顶部主要信息 -->
        <div class="mian-info">
          <mt-form ref="ruleForms" disabled :model="ruleForms">
            <mt-form-item prop="expertName" :label="$t('姓名：')">
              <mt-input
                width="100%"
                v-model="ruleForms.expertName"
                float-label-type="Never"
                :placeholder="$t('请输入姓名')"
                disabled
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="gender" :label="$t('性别：')">
              <mt-select
                disabled
                width="100%"
                :css-class="cssClassObj['gender']"
                :data-source="sexArr"
                v-model="ruleForms.gender"
                :show-clear-button="true"
                :placeholder="$t('请选择性别')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="birthday" :label="$t('出生日期：')">
              <!-- <span class="label-txt">备注：</span> -->
              <mt-date-picker
                v-model="ruleForms.birthday"
                disabled
                :open-on-focus="true"
                float-label-type="Never"
                width="100%"
                :placeholder="$t('请选择出生日期')"
              ></mt-date-picker>
            </mt-form-item>

            <mt-form-item ref="formItemFullname" prop="expertLevel" label="$t('专家级别：')">
              <mt-select
                disabled
                width="100%"
                :css-class="cssClassObj['expertLevel']"
                :data-source="exLevelArr"
                v-model="ruleForms.expertLevel"
                :show-clear-button="true"
                :placeholder="$t('请选择专家级别')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="expertCategory" ref="formItememail" label="$t('专家类别：')">
              <mt-select
                disabled
                width="100%"
                :css-class="cssClassObj['expertCategory']"
                :data-source="exTypeArr2"
                v-model="ruleForms.expertCategory"
                :show-clear-button="true"
                :placeholder="$t('请选择专家类别')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="fixedPhone" :label="$t('固定电话：')">
              <mt-input
                width="100%"
                float-label-type="Never"
                v-model="ruleForms.fixedPhone"
                disabled
                type="text"
                :placeholder="$t('请输入固定电话')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="mobilePhone" :label="$t('移动电话：')">
              <mt-input
                width="100%"
                float-label-type="Never"
                v-model="ruleForms.mobilePhone"
                type="text"
                :placeholder="$t('请输入移动电话')"
                disabled
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="email" :label="$t('邮箱：')">
              <mt-input
                width="100%"
                float-label-type="Never"
                v-model="ruleForms.email"
                type="text"
                :placeholder="$t('请输入邮箱')"
                disabled
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="certificateType" :label="$t('证件类型：')">
              <mt-select
                disabled
                width="100%"
                :css-class="cssClassObj['certificateType']"
                :data-source="cardTypeArr"
                v-model="ruleForms.certificateType"
                :show-clear-button="true"
                :placeholder="$t('请选择证件类型')"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="certificateNumber" :label="$t('证件号码：')">
              <mt-input
                width="100%"
                float-label-type="Never"
                v-model="ruleForms.certificateNumber"
                type="text"
                :placeholder="$t('请输入证件号码')"
                disabled
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="subAccount" :label="$t('登录账号：')">
              <mt-input
                width="100%"
                float-label-type="Never"
                v-model="ruleForms.subAccount"
                type="text"
                :placeholder="$t('请输入登录账号')"
                disabled
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="birthday" :label="$t('注册日期：')">
              <mt-date-picker
                v-model="ruleForms.birthday"
                :disabled="true"
                :open-on-focus="true"
                width="100%"
                float-label-type="Never"
                :placeholder="$t('请选择注册日期')"
              ></mt-date-picker>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态：')">
              <mt-select
                disabled
                width="100%"
                :css-class="cssClassObj['status']"
                :data-source="exTypeArr"
                v-model="ruleForms.status"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              ></mt-select>
            </mt-form-item>
          </mt-form>
        </div>
      </div>

      <div class="relation-ships">
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleSelectTab="handleSelectTab"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config'
import { utils } from '@mtech-common/utils'
export default {
  watch: {
    'ruleForms.supplierTypes'() {}
  },
  data() {
    const validateEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入联系人邮箱'))
      } else {
        let regexp = new RegExp(/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/)
        if (regexp.test(value)) {
          callback()
        } else {
          callback(new Error('邮箱格式有误'))
        }
      }
    }

    return {
      pageConfig,
      cssClass: 'normal-width e-outline',
      cssClassObj: {
        applycode: 'e-outline',
        exLevel: 'e-outline',
        exType: 'e-outline',
        exType2: 'e-outline',
        area: 'e-outline',
        contact: 'e-outline',
        Fullname: 'e-outline',
        email: 'e-outline'
      },
      shareOrganization: [],
      exLevelArr: [
        { text: this.$t('初级'), value: 0 },
        { text: this.$t('中级'), value: 1 },
        { text: this.$t('高级'), value: 2 }
      ],
      sexArr: [
        { text: this.$t('男'), value: 0 },
        { text: this.$t('女'), value: 1 }
      ],
      exTypeArr: [
        { text: this.$t('禁用'), value: 0 },
        { text: this.$t('启用'), value: 1 }
      ],
      exTypeArr2: [
        { text: this.$t('商务'), value: 0 },
        { text: this.$t('技术'), value: 1 },
        { text: this.$t('商务&技术'), value: 2 },
        { text: this.$t('现场考察'), value: 3 }
      ],
      cardTypeArr: [
        { text: this.$t('身份证'), value: 0 },
        { text: this.$t('护照'), value: 1 }
      ],
      sportsData: [
        'Badminton',
        'Basketball',
        'Cricket',
        'Football',
        'Golf',
        'Gymnastics',
        'Hockey',
        'Rugby',
        'Snooker',
        'Tennis'
      ],
      typesArray: [
        {
          label: this.$t('集团'),
          cssClass: 'label-text',
          value: 0
        },
        {
          label: this.$t('公司'),
          cssClass: 'label-text',
          value: 1
        }
      ],
      ruleForms: {
        status: '', //专家状态
        id: 0,
        expertId: '', //专家id
        birthday: '',
        certificateNumber: '',
        certificateType: 0,
        email: '',
        expertCategory: 0,
        expertCode: '',
        expertLevel: 0,
        expertName: '',
        expertSource: 0,
        fixedPhone: '',
        gender: 0,
        mobilePhone: '',
        subAccount: ''
      },
      expertSpecialtyFieldDTOS: [], //专家领域
      expertSpecialtyResultDTOS: [], //专业成果
      expertJobHistoryDTOS: [], //职业履历
      expertEduHistoryDTOS: [], //教育经历
      expertSourcingFileDTOS: [], //附件列表
      editStatus: false,
      activeTabIndex: 0,
      formRules: {
        expertLevel: [
          {
            required: true,
            message: this.$t('请选择专家级别'),
            trigger: 'blur'
          }
        ],
        expertSource: [
          {
            required: true,
            message: this.$t('请选择专家来源'),
            trigger: 'blur'
          }
        ],
        expertCategory: [
          {
            required: true,
            message: this.$t('请选择专家类别'),
            trigger: 'blur'
          }
        ],
        applyType: [
          {
            required: true,
            message: this.$t('请选择申请类型'),
            trigger: 'blur'
          }
        ],
        expertName: [
          {
            required: true,
            message: this.$t('请输入姓名'),
            trigger: 'blur'
          }
        ],
        email: [
          {
            required: true,
            validator: validateEmail,
            trigger: 'blur'
          }
        ],
        gender: [
          {
            required: true,
            message: this.$t('请选择性别'),
            trigger: 'blur'
          }
        ],
        // fixedPhone: [
        //   {
        //     required: true,
        //     message: this.$t("请输入固定电话"),
        //     trigger: "blur",
        //   },
        // ],
        subAccount: [
          {
            required: true,
            message: this.$t('请输入账号'),
            trigger: 'blur'
          }
        ],
        certificateType: [
          {
            required: true,
            message: this.$t('请选择证件类型'),
            trigger: 'blur'
          }
        ],
        certificateNumber: [
          {
            required: true,
            message: this.$t('请输入证件号码'),
            trigger: 'blur'
          }
        ],
        mobilePhone: [
          {
            required: true,
            message: this.$t('请输入移动电话'),
            trigger: 'blur'
          }
        ],
        birthday: [
          {
            required: true,
            message: this.$t('请选择注册日期'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('请选择状态'),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: false
          }
        ]
      },
      tabId: 0
    }
  },
  methods: {
    expertsListDetail(query) {
      // const query = { applyId: this.$route.query.configId };
      this.$API.expert.expertsaveDetails(query).then((res) => {
        this.ruleForms = res.data.expertBasicInfoDTO
        this.expertSpecialtyFieldDTOS = res.data.expertSpecialtyFieldDTOS
        this.expertSpecialtyFieldDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[0].grid, 'dataSource', this.expertSpecialtyFieldDTOS)
        this.expertSpecialtyResultDTOS = res.data.expertSpecialtyResultDTOS
        this.expertSpecialtyResultDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[1].grid, 'dataSource', this.expertSpecialtyResultDTOS)
        this.expertJobHistoryDTOS = res.data.expertJobHistoryDTOS
        this.expertJobHistoryDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[2].grid, 'dataSource', this.expertJobHistoryDTOS)
        this.expertEduHistoryDTOS = res.data.expertEduHistoryDTOS
        this.expertEduHistoryDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[3].grid, 'dataSource', this.expertEduHistoryDTOS)
        this.expertSourcingFileDTOS = res.data.expertSourcingFileDTOS
        this.expertSourcingFileDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[4].grid, 'dataSource', this.expertSourcingFileDTOS)
      })
    },
    submitForm() {
      this.$router.go(-1)
    },
    handleSelectTab(e) {
      this.activeTabIndex = e
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'DeleteTab0' ||
          e.toolbar.id == 'DeleteTab1' ||
          e.toolbar.id == 'DeleteTab2' ||
          e.toolbar.id == 'DeleteTab3' ||
          e.toolbar.id == 'DeleteTab4')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'AddTab0') {
        this.handleAddTab0()
      } else if (e.toolbar.id == 'DeleteTab0') {
        this.handleBatchDeleteTab0(_selectGridRecords)
      } else if (e.toolbar.id == 'AddTab1') {
        this.handleAddTab1()
      } else if (e.toolbar.id == 'DeleteTab1') {
        this.handleBatchDeleteTab1(_selectGridRecords)
      } else if (e.toolbar.id == 'AddTab2') {
        this.handleAddTab2()
      } else if (e.toolbar.id == 'DeleteTab2') {
        this.handleBatchDeleteTab2(_selectGridRecords)
      } else if (e.toolbar.id == 'AddTab3') {
        this.handleAddTab3()
      } else if (e.toolbar.id == 'DeleteTab3') {
        this.handleBatchDeleteTab3(_selectGridRecords)
      } else if (e.toolbar.id == 'AddTab4') {
        this.handleAddTab4()
      } else if (e.toolbar.id == 'DeleteTab4') {
        this.handleBatchDeleteTab4(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      if (this.activeTabIndex === 0) {
        if (e.tool.id == 'edit') {
          //编辑操作
          this.handleEditTab0(e.data)
        } else if (e.tool.id == 'delete') {
          //删除操作
          this.handleDeleteTab0(e.data)
        }
      } else if (this.activeTabIndex === 1) {
        if (e.tool.id == 'edit') {
          //编辑操作
          this.handleEditTab1(e.data)
        } else if (e.tool.id == 'delete') {
          //删除操作
          this.handleDeleteTab1(e.data)
        }
      } else if (this.activeTabIndex === 2) {
        if (e.tool.id == 'edit') {
          //编辑操作
          this.handleEditTab2(e.data)
        } else if (e.tool.id == 'delete') {
          //删除操作
          this.handleDeleteTab2(e.data)
        } else if (this.activeTabIndex === 3) {
          if (e.tool.id == 'edit') {
            //编辑操作
            this.handleEditTab3(e.data)
          } else if (e.tool.id == 'delete') {
            //删除操作
            this.handleDeleteTab3(e.data)
          }
        } else if (this.activeTabIndex === 4) {
          if (e.tool.id == 'edit') {
            //编辑操作
            this.handleEditTab4(e.data)
          } else if (e.tool.id == 'delete') {
            //删除操作
            this.handleDeleteTab4(e.data)
          }
        }
      }
    },
    handleAddTab0() {
      this.$dialog({
        modal: () => import('./components/addTab0.vue'),
        data: {
          title: this.$t('新增专家领域')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          this.expertSpecialtyFieldDTOS = _dataSource
        }
      })
    },
    handleEditTab0(data) {
      this.$dialog({
        modal: () => import('./components/addTab0.vue'),
        data: {
          title: this.$t('编辑专家领域'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          this.expertSpecialtyFieldDTOS = _dataSource
        }
      })
    },
    handleDeleteTab0(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          this.expertSpecialtyFieldDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab0(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _res)
          this.expertSpecialtyFieldDTOS = _res
        }
      })
    },
    handleAddTab1() {
      this.$dialog({
        modal: () => import('./components/addTab1.vue'),
        data: {
          title: this.$t('新增专业成果')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[1].grid, 'dataSource', _dataSource)
          this.expertSpecialtyResultDTOS = _dataSource
        }
      })
    },
    handleEditTab1(data) {
      this.$dialog({
        modal: () => import('./components/addTab1.vue'),
        data: {
          title: this.$t('编辑专业成果'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[1].grid, 'dataSource', _dataSource)
          this.expertSpecialtyResultDTOS = _dataSource
        }
      })
    },
    handleDeleteTab1(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[1].grid, 'dataSource', _dataSource)
          this.expertSpecialtyResultDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab1(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[1].grid, 'dataSource', _res)
          this.expertSpecialtyResultDTOS = _res
        }
      })
    },
    handleAddTab2() {
      this.$dialog({
        modal: () => import('./components/addTab2.vue'),
        data: {
          title: this.$t('新增专家领域')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[2].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[2].grid, 'dataSource', _dataSource)
          this.expertJobHistoryDTOS = _dataSource
        }
      })
    },
    handleEditTab2(data) {
      this.$dialog({
        modal: () => import('./components/addTab2.vue'),
        data: {
          title: this.$t('编辑专家领域'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[2].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[2].grid, 'dataSource', _dataSource)
          this.expertJobHistoryDTOS = _dataSource
        }
      })
    },
    handleDeleteTab2(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[2].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[2].grid, 'dataSource', _dataSource)
          this.expertJobHistoryDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab2(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[2].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[2].grid, 'dataSource', _res)
          this.expertJobHistoryDTOS = _res
        }
      })
    },
    handleAddTab3() {
      this.$dialog({
        modal: () => import('./components/addTab3.vue'),
        data: {
          title: this.$t('新增专家领域')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[3].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[3].grid, 'dataSource', _dataSource)
          this.expertEduHistoryDTOS = _dataSource
        }
      })
    },
    handleEditTab3(data) {
      this.$dialog({
        modal: () => import('./components/addTab3.vue'),
        data: {
          title: this.$t('编辑专家领域'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[3].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[3].grid, 'dataSource', _dataSource)
          this.expertEduHistoryDTOS = _dataSource
        }
      })
    },
    handleDeleteTab3(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[3].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[3].grid, 'dataSource', _dataSource)
          this.expertEduHistoryDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab3(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[3].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[3].grid, 'dataSource', _res)
          this.expertEduHistoryDTOS = _res
        }
      })
    },
    handleAddTab4() {
      this.$dialog({
        modal: () => import('./components/addTab4.vue'),
        data: {
          title: this.$t('新增专家领域')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[4].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[4].grid, 'dataSource', _dataSource)
          this.expertSourcingFileDTOS = _dataSource
        }
      })
    },
    handleEditTab4(data) {
      this.$dialog({
        modal: () => import('./components/addTab4.vue'),
        data: {
          title: this.$t('编辑专家领域'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[4].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[4].grid, 'dataSource', _dataSource)
          this.expertSourcingFileDTOS = _dataSource
        }
      })
    },
    handleDeleteTab4(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[4].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[4].grid, 'dataSource', _dataSource)
          this.expertSourcingFileDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab4(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[4].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[4].grid, 'dataSource', _res)
          this.expertSourcingFileDTOS = _res
        }
      })
    },
    expertSave() {
      this.$refs.ruleForms.validate((valid) => {
        if (valid) {
          let params = { ...this.ruleForms }
          if (!this.editStatus) {
            delete params.id
          } else {
            const query = { id: this.$route.query.configId }
            params.id = query.id
          }
          let _FieldDTOS = utils.cloneDeep(this.expertSpecialtyFieldDTOS)
          _FieldDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSpecialtyFieldDTOS = _FieldDTOS
          let _ResultDTOS = utils.cloneDeep(this.expertSpecialtyResultDTOS)
          _ResultDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSpecialtyResultDTOS = _ResultDTOS
          let _JobHistoryDTOS = utils.cloneDeep(this.expertJobHistoryDTOS)
          _JobHistoryDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertJobHistoryDTOS = _JobHistoryDTOS
          let _EduHistoryDTOS = utils.cloneDeep(this.expertEduHistoryDTOS)
          _EduHistoryDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertEduHistoryDTOS = _EduHistoryDTOS
          let _FileList = utils.cloneDeep(this.expertSourcingFileDTOS)
          _FileList.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSourcingFileDTOS = _FileList
          this.$API.expert.expertsaves(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
              this.$toast({
                content: res.msg,
                type: 'success'
              })
            }
          })
        }
      })
    }
  },
  mounted() {
    const query = { expertId: this.$route.query.configId }
    if (query.expertId) {
      this.expertsListDetail(query)
    }
  }
}
</script>

<style lang="scss">
.supplier-box {
  .mt-form-item-label {
    display: flex !important;
    justify-content: space-between !important;
  }
}
</style>
<style lang="scss" scoped>
/deep/.mt-form-item {
  width: calc(20% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .flex1 {
    flex: 1;
  }

  .miam-container {
    flex: 1;
    background: #fff;
    padding: 0 20px;
    .operate-bars {
    }
    .operate-bar {
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;
      // font-size: 14px;
      .op-item {
        cursor: pointer;
        // color: #4f5b6d;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }

    .flex-d-c {
      flex-direction: column;
    }

    .mian-info {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      // padding: 20px;
      .normal-title {
        width: 100%;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        color: #292929;
        font-family: PingFangSC;
        font-weight: 500;

        &:before {
          content: ' ';
          display: inline-block;
          vertical-align: middle;
          width: 2px;
          height: 10px;
          background: rgba(0, 70, 156, 1);
          border-radius: 1px;
          margin-right: 10px;
        }
      }

      .flex-d-c {
        flex-direction: column;
      }

      .input-item {
        margin-top: 20px;
        padding-right: 50px;
        .label-txt {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #292929;
        }
        .label-value {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #35404e;
        }
        .select-container {
          height: 40px;
        }
        .e-label {
          color: #35404e;
        }
        .label-text {
          color: #35404e;
        }
      }
      .input-item /deep/ .normal-width {
        width: 240px;
      }
      .input-item /deep/ .e-radio + label .e-label {
        color: #35404e;
      }
    }

    .relation-ships {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      // padding: 20px;
      margin-top: 20px;

      .tab-box {
        width: 100%;
        height: 40px;
        // border-bottom: 1px solid #e8e8e8;
        position: relative;

        .tab-item {
          font-size: 14px;
          height: 40px;
          line-height: 40px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(40, 41, 41, 1);
          padding: 0 38px;
          cursor: pointer;
        }
        .active {
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          border-bottom: 4px solid #00469c;
        }

        .right-btn {
          height: 40px;
          position: absolute;
          right: 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          transform: all 0.6s ease-in;
          i {
            margin-right: 4px;
            color: #4f5b6d;
          }
          .op-item {
            color: #4f5b6d;
            align-items: center;
            margin-right: 20px;
            align-items: center;
            cursor: pointer;
          }
          .add-new {
            i {
              color: #6386c1;
            }
            color: #6386c1;
          }
        }
      }
      .tab-content {
        .grid-search {
          height: 60px;
          line-height: 60px;
          justify-content: flex-end;
          .search-box {
            .label-txt {
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
            }
          }
        }
        /deep/ .common-template-page .page-grid-container {
          padding: 0 !important;
        }
      }
    }
  }

  .grid-content {
    padding-top: 20px;
  }
}
</style>
