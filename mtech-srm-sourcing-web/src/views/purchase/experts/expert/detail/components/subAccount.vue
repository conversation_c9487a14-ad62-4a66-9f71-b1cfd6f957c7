<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject">
        <mt-form-item prop="mobilePhone" :label="$t('移动电话')">
          <mt-input
            v-model="formObject.mobilePhone"
            float-label-type="Never"
            :placeholder="$t('请输入移动电话')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="email" :label="$t('邮箱')">
          <mt-input
            v-model="formObject.email"
            float-label-type="Never"
            :placeholder="$t('请输入邮箱')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      editStatus: false,
      formObject: {
        email: '',
        mobilePhone: ''
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
    // this.getFormValidRules("formRules", this.$API.costModel.saveModelValid);
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          this.$API.rfxExpert.getExpertItemAdd(params).then((res) => {
            if (res.code == 200) {
              if (res.data) {
                this.$emit('confirm-function', res.data)
              } else {
                this.$toast({
                  content: this.$t('未查询到用户信息'),
                  type: 'warning'
                })
              }
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
