<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="specialtyResultSpec" :label="$t('专业领域')">
          <mt-input
            v-model="formObject.specialtyResultSpec"
            float-label-type="Never"
            :placeholder="$t('请输入专业领域')"
            maxlength="110"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('附件')">
          <div class="div-auth" @click="handleUploadDialog">
            {{ getFormFileText }}
          </div>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formRules: {},
      formObject: {
        id: null,
        specialtyResultSpec: null, //专业领域
        sourcingFileSaveRequest: [] //附件
      },
      sourcingFileSaveRequest: [], //附件
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData[0],
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    getFormFileText() {
      let _list = this?.sourcingFileSaveRequest ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.fileName)
        })
        return _name.join(',')
      } else {
        return this.$t('上传附件')
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = [{ ...this.modalData.data }]
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          let _fileList = utils.cloneDeep(this.sourcingFileSaveRequest),
            _saveFile = []
          _fileList.forEach((e) => {
            _saveFile.push({
              // docId: 0,
              // docType: "",
              // fileDetailId: 0,
              // fileDetailInfo: "",
              fileName: e.fileName,
              fileSize: e.fileSize,
              fileType: e.fileType,
              // id: e.id,
              // lineNo: 0,
              // nodeCode: "",
              // nodeName: "",
              // nodeType: 0,
              // parentId: 0,
              // roundNo: "",
              // supplierCode: "",
              // supplierId: 0,
              // supplierName: "",
              // syncStatus: 0,
              sysFileId: e.id,
              url: e.url
            })
          })
          params.sourcingFileSaveRequest = _saveFile[0]
          this.$emit('confirm-function', params)
        }
      })
    },
    //上传附件弹框
    handleUploadDialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: utils.cloneDeep(this.sourcingFileSaveRequest),
          isView: false, // 是否为预览
          required: false, // 是否必须
          title: this.$t('附件')
        },
        success: (res) => {
          this.sourcingFileSaveRequest = res
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #e3e1e1;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
