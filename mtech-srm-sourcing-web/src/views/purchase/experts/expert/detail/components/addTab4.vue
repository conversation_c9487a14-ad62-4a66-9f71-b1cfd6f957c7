<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject">
        <mt-form-item prop="specialtyName" :label="$t('时间从')">
          <mt-date-picker
            v-model="formObject.startTime"
            :open-on-focus="true"
            :placeholder="$t('请选择开始时间')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="specialtyCode" :label="$t('时间至')">
          <mt-date-picker
            v-model="formObject.endTime"
            :open-on-focus="true"
            :placeholder="$t('请选择结束时间')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="eduInstitution" :label="$t('学校学院或其他机构')">
          <mt-input
            v-model="formObject.eduInstitution"
            float-label-type="Never"
            :placeholder="$t('请输入学校学院或其他机构')"
            maxlength="110"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="eduExperience" :label="$t('教育经历')">
          <mt-input
            v-model="formObject.eduExperience"
            float-label-type="Never"
            :placeholder="$t('请输入教育经历')"
            maxlength="110"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="eduDegree" :label="$t('学位')">
          <mt-input
            v-model="formObject.eduDegree"
            float-label-type="Never"
            :placeholder="$t('请输入学位')"
            maxlength="110"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="aptitude" :label="$t('执照、许可或资格')">
          <mt-input
            v-model="formObject.aptitude"
            float-label-type="Never"
            :placeholder="$t('请输入执照、许可或资格')"
            maxlength="110"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="certificateNo" :label="$t('证书编号')">
          <mt-input
            v-model="formObject.certificateNo"
            float-label-type="Never"
            :placeholder="$t('请输入证书编号')"
            maxlength="110"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="highestQualifications" :label="$t('最高学历')">
          <mt-input
            v-model="formObject.highestQualifications"
            float-label-type="Never"
            :placeholder="$t('请输入最高学历')"
            maxlength="110"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      editStatus: false,
      formObject: {
        aptitude: '',
        certificateNo: '',
        eduDegree: '',
        eduExperience: '',
        eduInstitution: '',
        endTime: '',
        highestQualifications: '',
        id: 0,
        startTime: ''
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
