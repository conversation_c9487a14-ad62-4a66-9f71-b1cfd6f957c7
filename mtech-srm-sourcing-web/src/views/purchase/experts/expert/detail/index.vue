<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container mt-flex-direction-column" style="width: 100%">
      <div class="operate-bars">
        <div class="operate-bar">
          <div class="op-item mt-flex" @click="submitForm">
            {{ $t('返回') }}
          </div>
          <div v-show="!isNotDraft" class="op-item mt-flex" @click="expertSubmit">
            {{ $t('提交') }}
          </div>
          <div v-show="!isNotDraft" class="op-item mt-flex" @click="expertSave">
            {{ $t('保存') }}
          </div>
        </div>
        <!-- 顶部主要信息 -->
        <div class="mian-info">
          <mt-form ref="ruleForm" :model="ruleForm" :rules="formRules">
            <mt-form-item ref="formItemarea" prop="id" :label="$t('申请单编号：')">
              <mt-input
                width="100%"
                v-model="ruleForm.id"
                float-label-type="Never"
                disabled
                :placeholder="$t('请输入申请单编号')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="applyType" :label="$t('申请类型：')">
              <mt-select
                width="100%"
                :css-class="cssClassObj['applyType']"
                :data-source="applyTypeList"
                v-model="ruleForm.applyType"
                :show-clear-button="true"
                :placeholder="$t('请选择申请类型')"
                @select="getAddSave($event, 2)"
                :disabled="isNotDraft"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="expertSource" :label="$t('专家来源：')">
              <mt-select
                width="100%"
                :css-class="cssClassObj['expertSource']"
                :data-source="exTypeArr"
                v-model="ruleForm.expertSource"
                :show-clear-button="true"
                @select="getAddSave($event, 1)"
                :placeholder="$t('请选择专家来源')"
                :disabled="isNotDraft"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="subAccount" :label="$t('账号：')">
              <!-- <div v-show="flage" @click="getExpertAdd">
                <mt-input
                  width="100%"
                  v-model="ruleForm.subAccount"
                  :disabled="disabled"
                  float-label-type="Never"
                  :placeholder="$t('请输入账号')"
                  @blur="searchInfo"
                ></mt-input>
              </div> -->
              <div v-show="flage">
                <mt-select
                  width="100%"
                  :value="ruleForm.subAccount"
                  float-label-type="Never"
                  :data-source="tenantUserList"
                  :show-clear-button="true"
                  :placeholder="$t('请输入账号')"
                  :allow-filtering="enableFilter"
                  :filtering="filterUserList"
                  @input="changeSelectUser"
                  :disabled="isNotDraft"
                ></mt-select>
              </div>
              <div v-show="content">
                <mt-input
                  width="100%"
                  v-model="ruleForm.subAccount"
                  :disabled="disabled || isNotDraft"
                  float-label-type="Never"
                  :placeholder="$t('请输入账号')"
                ></mt-input>
              </div>
            </mt-form-item>

            <mt-form-item prop="expertName" ref="formItemphone" :label="$t('姓名：')">
              <mt-input
                width="100%"
                v-model="ruleForm.expertName"
                float-label-type="Never"
                :disabled="disabled || isNotDraft"
                :placeholder="$t('请输入姓名')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="gender" :label="$t('性别：')">
              <mt-select
                width="100%"
                :css-class="cssClassObj['gender']"
                :data-source="sexArr"
                v-model="ruleForm.gender"
                :disabled="disabled || isNotDraft"
                :show-clear-button="true"
                :placeholder="$t('请选择性别')"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="mobilePhone" :label="$t('移动电话：')">
              <mt-input
                width="100%"
                v-model="ruleForm.mobilePhone"
                :disabled="disabled || isNotDraft"
                float-label-type="Never"
                :placeholder="$t('请输入移动电话')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="expertCategory" :label="$t('专家类别：')">
              <mt-select
                width="100%"
                :css-class="cssClassObj['expertCategory']"
                :data-source="exTypeArr2"
                v-model="ruleForm.expertCategory"
                :show-clear-button="true"
                :placeholder="$t('请选择专家类别')"
                :disabled="ruleForm.applyType == 2 || isNotDraft"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="email" :label="$t('邮箱：')">
              <mt-input
                width="100%"
                v-model="ruleForm.email"
                :disabled="disabled || isNotDraft"
                float-label-type="Never"
                :placeholder="$t('请输入邮箱')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item ref="formItemFullname" prop="expertLevel" :label="$t('专家级别：')">
              <mt-select
                width="100%"
                :css-class="cssClassObj['expertLevel']"
                :data-source="exLevelArr"
                v-model="ruleForm.expertLevel"
                :show-clear-button="true"
                :placeholder="$t('请选择专家级别')"
                :disabled="ruleForm.applyType == 2 || isNotDraft"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="certificateType" :label="$t('证件类型：')">
              <mt-select
                width="100%"
                :css-class="cssClassObj['certificateType']"
                :disabled="outdDisabled || isNotDraft"
                :data-source="cardTypeArr"
                v-model="ruleForm.certificateType"
                :show-clear-button="true"
                :placeholder="$t('请选择证件类型')"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="certificateNumber" :label="$t('证件号码：')">
              <mt-input
                width="100%"
                v-model="ruleForm.certificateNumber"
                :disabled="outdDisabled || isNotDraft"
                float-label-type="Never"
                :placeholder="$t('请输入证件号码')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="fixedPhone" :label="$t('固定电话：')">
              <mt-input
                width="100%"
                v-model="ruleForm.fixedPhone"
                :disabled="outdDisabled || isNotDraft"
                float-label-type="Never"
                :placeholder="$t('请输入固定电话')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="birthday" :label="$t('出生日期：')">
              <mt-date-picker
                v-model="ruleForm.birthday"
                :open-on-focus="true"
                float-label-type="Never"
                width="100%"
                :placeholder="$t('请选择出生日期')"
                :disabled="ruleForm.applyType == 2 || isNotDraft"
              ></mt-date-picker>
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="relation-ships">
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleSelectTab="handleSelectTab"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config'
import { isNullOrUndefined } from '@/utils/is'
import { utils } from '@mtech-common/utils'
export default {
  watch: {
    'ruleForm.supplierTypes'() {}
  },
  data() {
    return {
      pageConfig: [],
      flage: true,
      content: false,
      tenantUserList: [],
      enableFilter: false,
      // tabSource: [
      //   {
      //     title: this.$t("专家领域"),
      //   },
      //   {
      //     title: this.$t("专业成果"),
      //   },
      //   {
      //     title: this.$t("职业履历"),
      //   },
      //   {
      //     title: this.$t("教育经历"),
      //   },
      //   {
      //     title: this.$t("上传附件"),
      //   },
      // ],
      cssClass: 'normal-width e-outline',
      cssClassObj: {
        applycode: 'e-outline',
        exLevel: 'e-outline',
        exType: 'e-outline',
        exType2: 'e-outline',
        area: 'e-outline',
        contact: 'e-outline',
        Fullname: 'e-outline',
        email: 'e-outline'
      },
      shareOrganization: [],
      exLevelArr: [
        { text: this.$t('初级'), value: 0 },
        { text: this.$t('中级'), value: 1 },
        { text: this.$t('高级'), value: 2 }
      ],
      sexArr: [
        { text: this.$t('男'), value: 0 },
        { text: this.$t('女'), value: 1 },
        { text: this.$t('未知'), value: 2 }
      ],
      exTypeArr: [
        { text: this.$t('内部'), value: 0 },
        { text: this.$t('外聘'), value: 1 }
      ],
      exTypeArr2: [
        { text: this.$t('商务'), value: 0 },
        { text: this.$t('技术'), value: 1 },
        { text: this.$t('商务&技术'), value: 2 },
        { text: this.$t('现场考察'), value: 3 }
      ],
      applyTypeList: [
        { text: this.$t('新增专家'), value: 0 },
        { text: this.$t('专家变更'), value: 1 },
        { text: this.$t('专家退出'), value: 2 }
      ],
      cardTypeArr: [
        { text: this.$t('身份证'), value: 0 },
        { text: this.$t('护照'), value: 1 }
      ],
      sportsData: [
        'Badminton',
        'Basketball',
        'Cricket',
        'Football',
        'Golf',
        'Gymnastics',
        'Hockey',
        'Rugby',
        'Snooker',
        'Tennis'
      ],
      typesArray: [
        {
          label: this.$t('集团'),
          cssClass: 'label-text',
          value: 0
        },
        {
          label: this.$t('公司'),
          cssClass: 'label-text',
          value: 1
        }
      ],
      ruleForm: {
        id: 0,
        userId: '',
        //expertId: "", //专家id
        applyCode: '', //申请编码
        expertCode: '', //专家编码
        applyType: '', //申请类型
        certificateNumber: '', //证件号码
        certificateType: '', //证件类型
        birthday: '', //注册时间
        expertLevel: '', //专家级别
        expertName: '', //专家姓名
        expertSource: '', //专家来源
        expertCategory: '', //专家类型
        gender: '', //专家性别
        mobilePhone: '', //移动电话
        fixedPhone: '', //固定电话
        email: '', //邮箱
        subAccount: '' //专家账号
      },
      subMitStatus: 0,
      expertSpecialtyFieldDTOS: [], //专家领域
      expertSpecialtyResultDTOS: [], //专业成果
      expertJobHistoryDTOS: [], //职业履历
      expertEduHistoryDTOS: [], //教育经历
      expertSourcingFileDTOS: [], //附件列表
      editStatus: false,
      formRules: {
        // applycode: [
        //   {
        //     required: true,
        //     message: this.$t("请输入申请单编号"),
        //     trigger: "blur",
        //   },
        // ],
        expertLevel: [
          {
            required: true,
            message: this.$t('请选择专家级别'),
            trigger: 'blur'
          }
        ],
        expertSource: [
          {
            required: true,
            message: this.$t('请选择专家来源'),
            trigger: 'blur'
          }
        ],
        expertCategory: [
          {
            required: true,
            message: this.$t('请选择专家类别'),
            trigger: 'blur'
          }
        ],
        applyType: [
          {
            required: true,
            message: this.$t('请选择申请类型'),
            trigger: 'blur'
          }
        ],
        expertName: [
          {
            required: true,
            message: this.$t('请输入姓名'),
            trigger: 'blur'
          }
        ],
        email: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('邮箱格式有误'),
            pattern:
              /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          }
        ],
        gender: [
          {
            required: true,
            message: this.$t('请选择性别'),
            trigger: 'blur'
          }
        ],
        // fixedPhone: [
        //   {
        //     required: true,
        //     message: this.$t("请输入固定电话"),
        //     trigger: "blur",
        //   },
        // ],
        subAccount: [
          {
            required: false,
            message: this.$t('请输入账号'),
            trigger: 'blur'
          }
        ],
        certificateType: [
          // {
          //   required: true,
          //   message: this.$t("请选择证件类型"),
          //   trigger: "blur",
          // },
        ],
        certificateNumber: {
          // required: true,
          // trigger: "blur",
          validator: (_, value, callback) => {
            if (!value) {
              callback()
              return
            }
            if (this.ruleForm.certificateType === null || this.ruleForm.certificateType === '') {
              callback()
              return
            } else if (Number(this.ruleForm.certificateType) === 0) {
              if (
                !/^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/.test(
                  value
                )
              ) {
                callback(new Error(this.$t('身份证格式有误')))
              }
            } else if (Number(this.ruleForm.certificateType) === 1) {
              if (
                !/(^[EeKkGgDdSsPpHh]\d{8}$)|(^(([Ee][a-fA-F])|([DdSsPp][Ee])|([Kk][Jj])|([Mm][Aa])|(1[45]))\d{7}$)/.test(
                  value
                )
              ) {
                callback(new Error(this.$t('护照格式有误')))
              }
            }

            callback()
          }
        },
        mobilePhone: [
          {
            required: true,
            message: this.$t('请输入移动电话'),
            trigger: 'blur'
          }
        ],
        // birthday: [
        //   {
        //     required: true,
        //     message: this.$t("请选择出生日期"),
        //     trigger: "blur",
        //   },
        // ],
        remark: [
          {
            required: false
          }
        ]
      },
      activeTabIndex: 0,
      disabled: false,
      outdDisabled: false,
      isNotDraft: false //不是草稿状态
    }
  },
  mounted() {
    const query = { applyId: this.$route.query.configId }
    if (this.$route.query.applyStatus == 0) {
      this.isNotDraft = false
    } else {
      this.isNotDraft = true
    }
    this.pageConfig = pageConfig(this.isNotDraft)
    if (query.applyId) {
      this.editStatus = true
      this.expertsListDetail(query)
    }
    // this.getFormValidRules("formRules", this.$API.expert.expertsListVaild());
  },
  methods: {
    changeSelectUser(val) {
      if (val) {
        const row = this.tenantUserList.find((e) => e.value === val)
        if (
          (this.ruleForm.applyType === 1 || this.ruleForm.applyType === 2) &&
          (this.ruleForm.expertSource === 0 || this.ruleForm.expertSource === 1)
        ) {
          let param = {
            expertCode: val,
            expertSource: this.ruleForm.expertSource
          }
          this.$API.masterData.getDetail(param).then((res) => {
            this.ruleForm = {
              ...this.ruleForm,
              expertName: res?.data.expertName, //专家姓名
              gender: isNullOrUndefined(res?.data.gender) ? null : Number(res?.data.gender), //专家性别
              mobilePhone: res?.data.mobilePhone, //移动电话
              email: res?.data.email, //邮箱
              subAccount: row.value,
              fixedPhone: res?.data.fixedPhone, //固定电话
              certificateNumber: res?.data.certificateNumber, //证件号码
              certificateType: isNullOrUndefined(res?.data.certificateType)
                ? null
                : Number(res?.data.certificateType), //证件类型
              // subAccount: row?.userName + `(${row?.externalCode})`, //专家账号
              expertLevel: res?.data.expertLevel,
              expertCategory: res?.data.expertCategory
            }
          })
        } else if (row) {
          this.ruleForm = {
            ...this.ruleForm,
            expertName: row?.userName, //专家姓名
            gender: isNullOrUndefined(row?.gender.value) ? null : Number(row?.gender.value), //专家性别
            mobilePhone: row?.telephone, //移动电话
            email: row?.email, //邮箱
            subAccount: row.value,
            userId: row.id
          }
        }
      } else {
        this.ruleForm = {
          ...this.ruleForm,
          expertName: '', //专家姓名
          gender: '', //专家性别
          mobilePhone: '', //移动电话
          email: '', //邮箱
          subAccount: '',
          fixedPhone: '', //固定电话
          certificateNumber: '', //证件号码
          certificateType: '' //证件类型
          // subAccount: row?.userName + `(${row?.externalCode})`, //专家账号
        }
      }
    },
    getAddSave(e, type) {
      if (type == 1) {
        if (this.ruleForm.applyType === 0) {
          if (e.itemData.value == 1) {
            this.disabled = false
            this.content = true
            this.flage = false
          } else if (e.itemData.value == 0) {
            this.disabled = true
            this.flage = true
            this.content = false
            // this.getExpertAdd();
            this.getUserAdd(e.value)
            this.enableFilter = true
          }
        } else {
          this.disabled = true
          this.flage = true
          this.content = false
          // this.getExpertAdd();
          // this.getUserAdd(e.value);
          this.enableFilter = true
        }
        this.tenantUserList = []
      } else if (type == 2) {
        // 为专家退出时，不显示弹出框
        this.disabled = true
        if (e.itemData.value == 2) {
          this.outdDisabled = true
          this.disabled = true
          this.content = false
          this.flage = true
        } else {
          this.outdDisabled = false
          this.disabled = true
          this.content = false
          this.flage = true
          this.ruleForm = {
            applyType: e.itemData.value
          }
          this.pageConfig[0].grid.dataSource = this.expertSpecialtyFieldDTOS = []
          this.pageConfig[1].grid.dataSource = this.expertSpecialtyResultDTOS = []
          this.pageConfig[2].grid.dataSource = this.expertJobHistoryDTOS = []
          this.pageConfig[3].grid.dataSource = this.expertEduHistoryDTOS = []
          this.pageConfig[4].grid.dataSource = this.expertSourcingFileDTOS = []
        }
        this.tenantUserList = []
        this.ruleForm = {
          applyType: e.itemData.value
        }
      }
    },
    getUserList(userName) {
      this.$API.masterData.getUserList({ userName }).then((res) => {
        console.log(222, res)
        res.data.forEach((u) => {
          u.text = u.externalCode.trim() ? u.userName + `(${u.externalCode})` : u.userName
          u.value = u.userCode
        })
        this.tenantUserList = res.data
      })
    },
    getUserAdd(e) {
      if (e === 0) {
        this.getUserList(e)
      }
    },
    filterUserList(e = { text: '' }) {
      if (
        (this.ruleForm.applyType === 1 || this.ruleForm.applyType === 2) &&
        (this.ruleForm.expertSource === 0 || this.ruleForm.expertSource === 1)
      ) {
        let param = {
          expertCode: e.text,
          expertSource: this.ruleForm.expertSource
        }
        this.$API.masterData.getfuzzy(param).then((res) => {
          let tenantUserList = []
          for (let item of res.data.expertCodeList) {
            tenantUserList.push({ text: item, value: item })
          }
          this.tenantUserList = tenantUserList
        })
      } else {
        this.getUserList(e.text)
      }
    },

    // 离开事件：调用接口返回值进行回显
    searchInfo(e) {
      this.$store.commit('startLoading')
      this.$API.expert.queryDetail({ subAccount: e }).then((res) => {
        this.$store.commit('endLoading')
        this.ruleForm.userId = res.data.expertBasicInfoDTO.userId
        this.ruleForm.birthday = res.data.expertBasicInfoDTO.birthday
        this.ruleForm.certificateNumber = res.data.expertBasicInfoDTO.certificateNumber
        this.ruleForm.certificateType = res.data.expertBasicInfoDTO.certificateType
        this.ruleForm.email = res.data.expertBasicInfoDTO.email
        this.ruleForm.expertCategory = res.data.expertBasicInfoDTO.expertCategory
        this.ruleForm.expertCode = res.data.expertBasicInfoDTO.expertCode
        this.ruleForm.expertLevel = res.data.expertBasicInfoDTO.expertLevel
        this.ruleForm.expertName = res.data.expertBasicInfoDTO.expertName
        this.ruleForm.expertSource = res.data.expertBasicInfoDTO.expertSource
        this.ruleForm.fixedPhone = res.data.expertBasicInfoDTO.fixedPhone
        this.ruleForm.gender = res.data.expertBasicInfoDTO.gender
        this.ruleForm.id = res.data.expertBasicInfoDTO.id
        this.ruleForm.mobilePhone = res.data.expertBasicInfoDTO.mobilePhone
        this.ruleForm.status = res.data.expertBasicInfoDTO.status
        this.ruleForm.subAccount = res.data.expertBasicInfoDTO.subAccount
        this.pageConfig[0].grid.dataSource = this.expertSpecialtyFieldDTOS =
          res.data.expertSpecialtyFieldDTOS
        this.pageConfig[1].grid.dataSource = this.expertSpecialtyResultDTOS =
          res.data.expertSpecialtyResultDTOS
        this.pageConfig[2].grid.dataSource = this.expertJobHistoryDTOS =
          res.data.expertJobHistoryDTOS
        this.pageConfig[3].grid.dataSource = this.expertEduHistoryDTOS =
          res.data.expertEduHistoryDTOS
        this.pageConfig[4].grid.dataSource = this.expertSourcingFileDTOS =
          res.data.expertSourcingFileDTOS
      })
    },
    getExpertAdd() {
      this.$dialog({
        modal: () => import('./components/subAccount.vue'),
        data: {
          title: this.$t('账号信息')
        },
        success: (res) => {
          console.log(res, this.$t('查看回显的数据'))
          this.ruleForm.email = res.email
          this.ruleForm.subAccount = res.subAccount
          this.ruleForm.gender = res.gender
          this.ruleForm.expertName = res.expertName
          this.ruleForm.mobilePhone = res.mobilePhone
          this.ruleForm.userId = res.userId
          this.ruleForm.subAccount = res.subAccount
          this.formRules.subAccount[0].required = false
          this.$refs.ruleForm.validate()
        }
      })
    },
    expertsListDetail(query) {
      // const query = { applyId: this.$route.query.configId };
      this.$API.expert.expertsaveDetail(query).then((res) => {
        this.ruleForm = res.data.expertApplyInfoDTO
        if (this.flage) {
          this.getUserList(0)
        }
        this.expertSpecialtyFieldDTOS = res.data.expertSpecialtyFieldDTOS
        this.expertSpecialtyFieldDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[0].grid, 'dataSource', this.expertSpecialtyFieldDTOS)
        this.expertSpecialtyResultDTOS = res.data.expertSpecialtyResultDTOS
        this.expertSpecialtyResultDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[1].grid, 'dataSource', this.expertSpecialtyResultDTOS)
        this.expertJobHistoryDTOS = res.data.expertJobHistoryDTOS
        this.expertJobHistoryDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[2].grid, 'dataSource', this.expertJobHistoryDTOS)
        this.expertEduHistoryDTOS = res.data.expertEduHistoryDTOS
        this.expertEduHistoryDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[3].grid, 'dataSource', this.expertEduHistoryDTOS)
        this.expertSourcingFileDTOS = res.data.expertSourcingFileDTOS
        this.expertSourcingFileDTOS.forEach((item, index) => {
          item.lineIndex = index
        })
        this.$set(this.pageConfig[4].grid, 'dataSource', this.expertSourcingFileDTOS)
      })
    },
    submitForm() {
      this.$router.go(-1)
    },
    handleSelectTab(e) {
      this.activeTabIndex = e
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'DeleteTab0' ||
          e.toolbar.id == 'DeleteTab1' ||
          e.toolbar.id == 'DeleteTab2' ||
          e.toolbar.id == 'DeleteTab3' ||
          e.toolbar.id == 'DeleteTab4')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'AddTab0') {
        this.handleAddTab0()
      } else if (e.toolbar.id == 'DeleteTab0') {
        this.handleBatchDeleteTab0(_selectGridRecords)
      } else if (e.toolbar.id == 'AddTab1') {
        this.handleAddTab1()
      } else if (e.toolbar.id == 'DeleteTab1') {
        this.handleBatchDeleteTab1(_selectGridRecords)
      } else if (e.toolbar.id == 'AddTab2') {
        this.handleAddTab2()
      } else if (e.toolbar.id == 'DeleteTab2') {
        this.handleBatchDeleteTab2(_selectGridRecords)
      } else if (e.toolbar.id == 'AddTab3') {
        this.handleAddTab3()
      } else if (e.toolbar.id == 'DeleteTab3') {
        this.handleBatchDeleteTab3(_selectGridRecords)
      } else if (e.toolbar.id == 'AddTab4') {
        this.handleAddTab4()
      } else if (e.toolbar.id == 'DeleteTab4') {
        this.handleBatchDeleteTab4(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      if (this.activeTabIndex === 0) {
        if (e.tool.id == 'edit') {
          //编辑操作
          this.handleEditTab0(e.data)
        } else if (e.tool.id == 'delete') {
          //删除操作
          this.handleDeleteTab0(e.data)
        }
      } else if (this.activeTabIndex === 1) {
        if (e.tool.id == 'edit') {
          //编辑操作
          this.handleEditTab1(e.data)
        } else if (e.tool.id == 'delete') {
          //删除操作
          this.handleDeleteTab1(e.data)
        }
      } else if (this.activeTabIndex === 2) {
        if (e.tool.id == 'edit') {
          //编辑操作
          this.handleEditTab2(e.data)
        } else if (e.tool.id == 'delete') {
          //删除操作
          this.handleDeleteTab2(e.data)
        }
      } else if (this.activeTabIndex === 3) {
        if (e.tool.id == 'edit') {
          //编辑操作
          this.handleEditTab3(e.data)
        } else if (e.tool.id == 'delete') {
          //删除操作
          this.handleDeleteTab3(e.data)
        }
      } else if (this.activeTabIndex === 4) {
        if (e.tool.id == 'edit') {
          //编辑操作
          this.handleEditTab4(e.data)
        } else if (e.tool.id == 'delete') {
          //删除操作
          this.handleDeleteTab4(e.data)
        }
      }
    },
    handleAddTab0() {
      this.$dialog({
        modal: () => import('./components/professionalField'),
        data: {
          title: this.$t('新增专家领域')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          console.log(619, data)
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          this.expertSpecialtyFieldDTOS = _dataSource
        }
      })
    },
    handleEditTab0(data) {
      this.$dialog({
        modal: () => import('./components/professionalField'),
        data: {
          title: this.$t('编辑专家领域'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          this.expertSpecialtyFieldDTOS = _dataSource
        }
      })
    },
    handleDeleteTab0(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          this.expertSpecialtyFieldDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab0(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _res)
          this.expertSpecialtyFieldDTOS = _res
        }
      })
    },
    handleAddTab1() {
      this.$dialog({
        modal: () => import('./components/ProfessionalOutcomes'),
        data: {
          title: this.$t('新增专业成果')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[1].grid, 'dataSource', _dataSource)
          this.expertSpecialtyResultDTOS = _dataSource
        }
      })
    },
    handleEditTab1(data) {
      this.$dialog({
        modal: () => import('./components/ProfessionalOutcomes'),
        data: {
          title: this.$t('编辑专业成果'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[1].grid, 'dataSource', _dataSource)
          this.expertSpecialtyResultDTOS = _dataSource
        }
      })
    },
    handleDeleteTab1(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[1].grid, 'dataSource', _dataSource)
          this.expertSpecialtyResultDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab1(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[1].grid, 'dataSource', _res)
          this.expertSpecialtyResultDTOS = _res
        }
      })
    },
    handleAddTab2() {
      this.$dialog({
        modal: () => import('./components/EducationResume'),
        data: {
          title: this.$t('新增职业履历')
        },
        success: (data) => {
          console.log(this.$t('职业履历'), data)
          let _dataSource = utils.cloneDeep(this.pageConfig[2].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[2].grid, 'dataSource', _dataSource)
          this.expertJobHistoryDTOS = _dataSource
        }
      })
    },
    handleEditTab2(data) {
      this.$dialog({
        modal: () => import('./components/EducationResume'),
        data: {
          title: this.$t('编辑职业履历'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[2].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[2].grid, 'dataSource', _dataSource)
          this.expertJobHistoryDTOS = _dataSource
        }
      })
    },
    handleDeleteTab2(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[2].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[2].grid, 'dataSource', _dataSource)
          this.expertJobHistoryDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab2(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[2].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[2].grid, 'dataSource', _res)
          this.expertJobHistoryDTOS = _res
        }
      })
    },
    handleAddTab3() {
      this.$dialog({
        modal: () => import('./components/addTab4.vue'),
        data: {
          title: this.$t('新增教育经历')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[3].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[3].grid, 'dataSource', _dataSource)
          this.expertEduHistoryDTOS = _dataSource
        }
      })
    },
    handleEditTab3(data) {
      this.$dialog({
        modal: () => import('./components/addTab4.vue'),
        data: {
          title: this.$t('编辑教育经历'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[3].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[3].grid, 'dataSource', _dataSource)
          this.expertEduHistoryDTOS = _dataSource
        }
      })
    },
    handleDeleteTab3(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[3].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[3].grid, 'dataSource', _dataSource)
          this.expertEduHistoryDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab3(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[3].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[3].grid, 'dataSource', _res)
          this.expertEduHistoryDTOS = _res
        }
      })
    },
    handleAddTab4() {
      this.$dialog({
        modal: () => import('./components/addTab5.vue'),
        data: {
          title: this.$t('新增上传附件')
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[4].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[4].grid, 'dataSource', _dataSource)
          this.expertSourcingFileDTOS = _dataSource
        }
      })
    },
    handleEditTab4(data) {
      this.$dialog({
        modal: () => import('./components/addTab5.vue'),
        data: {
          title: this.$t('编辑上传附件'),
          data
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[4].grid['dataSource'])
          _dataSource.forEach((item, index) => {
            if (item.lineIndex === data.lineIndex) {
              _dataSource[index] = data
            }
          })
          this.$set(this.pageConfig[4].grid, 'dataSource', _dataSource)
          this.expertSourcingFileDTOS = _dataSource
        }
      })
    },
    handleDeleteTab4(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _dataSource = utils.cloneDeep(this.pageConfig[4].grid['dataSource'])
          _dataSource.splice(+data.lineIndex, 1)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[4].grid, 'dataSource', _dataSource)
          this.expertSourcingFileDTOS = _dataSource
        }
      })
    },
    handleBatchDeleteTab4(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _indexList = []
          _records.forEach((e) => {
            _indexList.push(e.lineIndex)
          })
          let _res = []
          let _dataSource = utils.cloneDeep(this.pageConfig[4].grid['dataSource'])
          _dataSource.forEach((e) => {
            if (_indexList.indexOf(e.lineIndex) < 0) {
              _res.push(e)
            }
          })
          _res.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[4].grid, 'dataSource', _res)
          this.expertSourcingFileDTOS = _res
        }
      })
    },
    expertSubmit() {
      this.formRules.subAccount[0].required = false
      if (this.ruleForm.expertSource == 1) {
        this.formRules.subAccount[0].required = true
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = { ...this.ruleForm, subMitStatus: 5 }
          if (!this.editStatus) {
            delete params.id
          } else {
            const query = { id: this.$route.query.configId }
            params.id = query.id
          }
          let _FieldDTOS = utils.cloneDeep(this.expertSpecialtyFieldDTOS)
          _FieldDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSpecialtyFieldDTOS = _FieldDTOS
          let _ResultDTOS = utils.cloneDeep(this.expertSpecialtyResultDTOS)
          _ResultDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSpecialtyResultDTOS = _ResultDTOS
          let _JobHistoryDTOS = utils.cloneDeep(this.expertJobHistoryDTOS)
          _JobHistoryDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertJobHistoryDTOS = _JobHistoryDTOS
          let _EduHistoryDTOS = utils.cloneDeep(this.expertEduHistoryDTOS)
          _EduHistoryDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertEduHistoryDTOS = _EduHistoryDTOS
          let _FileList = utils.cloneDeep(this.expertSourcingFileDTOS)
          _FileList.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSourcingFileDTOS = _FileList
          params.userId = this.ruleForm.userId
          this.$store.commit('startLoading')
          this.$API.expert
            .expertsave(params)
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$router.push({
                  path: `purchase-expert`
                })
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    expertSave() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = { ...this.ruleForm }
          if (!this.editStatus) {
            delete params.id
          } else {
            const query = { id: this.$route.query.configId }
            params.id = query.id
          }
          let _FieldDTOS = utils.cloneDeep(this.expertSpecialtyFieldDTOS)
          _FieldDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSpecialtyFieldDTOS = _FieldDTOS
          let _ResultDTOS = utils.cloneDeep(this.expertSpecialtyResultDTOS)
          _ResultDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSpecialtyResultDTOS = _ResultDTOS
          let _JobHistoryDTOS = utils.cloneDeep(this.expertJobHistoryDTOS)
          _JobHistoryDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertJobHistoryDTOS = _JobHistoryDTOS
          let _EduHistoryDTOS = utils.cloneDeep(this.expertEduHistoryDTOS)
          _EduHistoryDTOS.forEach((item) => {
            delete item.lineIndex
          })
          params.expertEduHistoryDTOS = _EduHistoryDTOS
          let _FileList = utils.cloneDeep(this.expertSourcingFileDTOS)
          _FileList.forEach((item) => {
            delete item.lineIndex
          })
          params.expertSourcingFileDTOS = _FileList
          params.userId = this.ruleForm.userId
          this.$store.commit('startLoading')
          this.$API.expert
            .expertsave(params)
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$router.push({
                  path: `purchase-expert`
                })
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mt-template-page {
  /deep/ .repeat-template {
    padding: 20px;
  }
}
/deep/.mt-form-item {
  width: calc(20% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .flex1 {
    flex: 1;
  }

  .miam-container {
    flex: 1;
    background: #fff;
    padding: 0 20px;

    .operate-bars {
      border-radius: 8px 8px 0 0;
    }
    .operate-bar {
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;
      // font-size: 14px;
      .op-item {
        cursor: pointer;
        // color: #4f5b6d;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }

    .flex-d-c {
      flex-direction: column;
    }

    .mian-info {
      background: rgba(255, 255, 255, 1);
      // border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      // padding: 20px;
      min-width: 1300px;
      border: 1px rgba(232, 232, 232, 1);
      border-radius: 8px 8px 0 0;

      .normal-title {
        width: 100%;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        color: #292929;
        font-family: PingFangSC;
        font-weight: 500;

        &:before {
          content: ' ';
          display: inline-block;
          vertical-align: middle;
          width: 2px;
          height: 10px;
          background: rgba(0, 70, 156, 1);
          border-radius: 1px;
          margin-right: 10px;
        }
      }

      .flex-d-c {
        flex-direction: column;
      }

      .input-item {
        margin-top: 20px;
        padding-right: 50px;
        .label-txt {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #292929;
        }
        .label-value {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #35404e;
        }
        .select-container {
          height: 40px;
        }
        .e-label {
          color: #35404e;
        }
        .label-text {
          color: #35404e;
        }
      }
      .input-item /deep/ .normal-width {
        width: 240px;
      }
      .input-item /deep/ .e-radio + label .e-label {
        color: #35404e;
      }
    }

    .relation-ships {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      // border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      // padding: 20px;
      margin-top: 20px;

      .tab-box {
        width: 100%;
        height: 40px;
        border-bottom: 1px solid #e8e8e8;
        position: relative;

        .tab-item {
          font-size: 14px;
          height: 40px;
          line-height: 40px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(40, 41, 41, 1);
          padding: 0 38px;
          cursor: pointer;
        }
        .active {
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          border-bottom: 4px solid #00469c;
        }

        .right-btn {
          height: 40px;
          position: absolute;
          right: 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          transform: all 0.6s ease-in;
          i {
            margin-right: 4px;
            color: #4f5b6d;
          }
          .op-item {
            color: #4f5b6d;
            align-items: center;
            margin-right: 20px;
            align-items: center;
            font-size: 14px;
            cursor: pointer;
          }
          .add-new {
            i {
              color: #6386c1;
            }
            color: #6386c1;
          }
        }
      }
      .tab-content {
        .grid-search {
          height: 60px;
          line-height: 60px;
          justify-content: flex-end;
          .search-box {
            .label-txt {
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
            }
          }
        }
        /deep/ .common-template-page .page-grid-container {
          padding: 0 !important;
        }
      }
    }
  }

  .grid-content {
    padding-top: 20px;
  }
}
</style>
