<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject">
        <mt-form-item prop="costModelName" :label="$t('专家姓名')">
          <mt-input
            v-model="formObject.costModelName"
            float-label-type="Never"
            :placeholder="$t('请输入专家姓名')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="costModelData" :label="$t('注册日期')">
          <mt-date-picker
            v-model="formObject.costModelData"
            :placeholder="$t('请选择注册日期')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="costModelSave" :label="$t('专家性别')">
          <mt-select
            :data-source="costModelNames"
            v-model="formObject.costModelSave"
            :show-clear-button="true"
            :placeholder="$t('请选择专家性别')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      costModelNames: [
        { text: this.$t('男'), value: 0 },
        { text: this.$t('女'), value: 1 }
      ],
      editStatus: false,
      formObject: {
        costModelName: '',
        costModelData: '',
        costModelSave: ''
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
    // this.getFormValidRules("formRules", this.$API.costModel.saveModelValid);
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          // this.$API.costModel.saveModel([params]).then((res) => {
          //   if (res.code == 200) {
          //     this.$emit("confirm-function");
          //   }
          // });
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
