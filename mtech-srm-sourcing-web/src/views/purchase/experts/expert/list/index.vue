<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.expert.expertList)
    }
  },
  methods: {
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length < 1 && (e.toolbar.id == 'delete' || e.toolbar.id == '')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.$router.push({
          path: `purchase-expert-detail`,
          query: {
            key: this.$utils.randomString(),
            applyStatus: 0
          }
        })
      } else if (e.toolbar.id == 'delete') {
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id == 'Submit') {
        this.handleDeleteConfigs(_selectGridRecords)
      }
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //删除规则
    handleDeleteConfig(idList) {
      let _params = {
        idList
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.expert.expertDelete(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleDeleteConfigs(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfigList(_selectIds)
    },
    handleDeleteConfigList(idList) {
      let params = {
        idList
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交数据')
        },
        success: () => {
          this.$API.expert.expertSubmit(params).then(() => {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //单元格标题
    handleClickCellTitle(e) {
      if (e.field == 'applyCode') {
        this.$router.push({
          path: `purchase-expert-detail`,
          query: {
            configId: e.data.id,
            applyStatus: e.data.applyStatus
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
