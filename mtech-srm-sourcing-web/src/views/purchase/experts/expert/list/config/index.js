import { i18n, permission } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_edit', title: i18n.t('删除') },
  { id: 'Submit', icon: 'icon_solid_edit', title: i18n.t('提交') }
]

const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '200',
    field: 'applyCode',
    headerText: i18n.t('申请单编号'),
    cssClass: 'field-content'
  },
  {
    width: '100',
    field: 'applyType',
    headerText: i18n.t('申请类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新增专家'),
        1: i18n.t('专家变更'),
        2: i18n.t('专家退出')
      }
    }
  },
  {
    width: '120',
    field: 'expertName',
    headerText: i18n.t('专家姓名')
  },
  {
    width: '120',
    field: 'createTime',
    headerText: i18n.t('注册日期'),
    valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
  },
  {
    width: '100',
    field: 'gender',
    headerText: i18n.t('专家性别'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('男'), 1: i18n.t('女'), 2: i18n.t('未知') }
    }
  },
  {
    width: '120',
    field: 'subAccount',
    headerText: i18n.t('账号')
  },
  {
    width: '100',
    field: 'expertSource',
    headerText: i18n.t('专家来源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('内部'), 1: i18n.t('外聘') }
    }
  },
  {
    width: '100',
    field: 'applyStatus',
    headerText: i18n.t('单据状态'),
    valueConverter: {
      type: 'map',
      map: [
        { applyStatus: -1, label: i18n.t('已停用'), cssClass: 'title-#9a9a9a' },
        { applyStatus: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { applyStatus: 1, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
        {
          applyStatus: 3,
          label: i18n.t('审批拒绝'),
          cssClass: 'title-#ed5633'
        },
        { applyStatus: 4, label: i18n.t('已生效'), cssClass: 'title-#6386c1' },
        {
          applyStatus: 5,
          label: i18n.t('提交状态'),
          cssClass: 'title-#6386c1'
        }
      ],
      fields: { text: 'label', value: 'applyStatus' }
    },
    cellTools: [
      {
        id: 'Stop',
        icon: 'icon_solid_Cancel',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['applyStatus'] == 3
        }
      }
    ]
  }
]

export const pageConfig = (url) => [
  {
    toolbar,
    useToolTemplate: false,
    gridId: permission.gridId['purchase']['expert']['list'],
    grid: {
      allowFiltering: true,
      allowSorting: false,
      columnData,
      asyncConfig: { url }
    }
  }
]
