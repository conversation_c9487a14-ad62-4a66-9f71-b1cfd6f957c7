<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="expert-input">
        <div class="expert_box" style="width: 100%">
          <div style="width: 200px">
            <p>{{ $t('驳回原因') }}：</p>
          </div>
          <div style="width: 100%">
            <mt-input
              :multiline="true"
              :rows="4"
              style="width: 100%"
              maxlength="200"
              v-model="reason"
              float-label-type="Never"
              :placeholder="!modalData.needButton == true ? '' : $t('请输入200字以内')"
              :disabled="!modalData.needButton"
            ></mt-input>
          </div>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      reason: ''
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.reason = this.modalData.reason
    if (this.modalData.needButton) {
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    } else {
      this.buttons = []
    }
  },
  methods: {
    confirm() {
      if (this.reason) {
        this.$emit('confirm-function', this.reason)
      } else {
        this.$toast({ content: this.$t('请填写驳回原因'), type: 'warning' })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expert-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  height: 100%;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}
</style>
