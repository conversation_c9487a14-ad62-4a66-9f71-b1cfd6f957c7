<template>
  <div class="full-height mt-flex-direction-column">
    <div style="flex: 1">
      <mt-template-page
        ref="template0"
        :template-config="pageConfig0"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        v-if="tabIndex == 0"
      />
      <mt-template-page
        ref="template1"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        v-if="tabIndex == 1"
      />
    </div>
  </div>
</template>

<script>
import { columnDataExpert, columnDataSupplier, toolbar } from './config/config.js'
export default {
  name: 'ScoreTab',
  data() {
    return {
      tabIndex: 0,
      pageConfig0: [
        {
          useToolTemplate: false,
          toolbar: this.$route.query.scoreOperate == 1 ? toolbar : [],
          grid: {
            allowFiltering: true,
            lineIndex: true,
            columnData: columnDataExpert,
            dataSource: []
          }
        }
      ],
      expertList: [],
      supplierList: [],
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowFiltering: true,
            lineIndex: true,
            columnData: columnDataSupplier(this.$route.query.scoreGroup),
            asyncConfig: {
              url: this.$API.rfxExpert.getSupplierDimensionList,
              queryBuilderWrap: 'requestParams',
              serializeList: (list) => {
                list.forEach((item) => {
                  if (this.$route.query.scoreOperate == 1) {
                    if (item.leaderEvaluation == null) {
                      item.comprehensiveAssessment = this.$t('综合评价')
                    } else {
                      item.comprehensiveAssessment = this.$t('查看')
                    }
                  } else {
                    item.comprehensiveAssessment = this.$t('查看')
                  }
                })
                return list
              },
              afterAsyncData: (res) => {
                this.supplierList = res.data.records
              },
              params: {
                bidType: this.$route.query.scoreGroup,
                rfxCode: this.$route.query.rfxCode
              }
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.handleSelectTab(this.tabIndex)
    this.getExpertDimensionList()
  },
  methods: {
    /**
     * 评标一览表
     */
    async preview() {
      let buffer = await this.$API.rfxPricing
        .previewGrade({
          rfxCode: this.$route.query.rfxCode,
          tableType: this.$route.query.scoreGroup
        })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'text/html') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    // tab切换
    handleSelectTab(e) {
      if (e === 0) {
        this.getExpertDimensionList({})
      }
      this.tabIndex = e
    },
    handleClickCellTitle(e) {
      if (e.field == 'viewScoreDetail') {
        let supplierList = []
        for (let item of e.data.bidScoreSupplierDTOList) {
          supplierList.push({
            supplierCode: item.supplierCode,
            supplierId: item.supplierId,
            supplierName: item.supplierName
          })
        }
        // 打分明细弹框
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ '../components/index.vue'
            ),
          data: {
            title: this.$t('评分明细'),
            rfxCode: this.$route.query.rfxCode,
            expertList: this.expertList,
            supplierList: supplierList,
            currentExpertCode: e.data.expertCode,
            bidType: this.$route.query.scoreGroup,
            scoreProcess: e.data.scoreProcess,
            supplierCode: e.data.supplierCode,
            supplierId: e.data.supplierId
          },
          success: () => {
            this.$refs.template0.refreshCurrentGridData()
          }
        })
      } else if (e.field == 'viewScoreSupplierDetail') {
        let supplierArr = []
        for (let item of this.supplierList) {
          supplierArr.push({
            supplierName: item.supplierName,
            supplierCode: item.supplierCode,
            supplierId: item.supplierId,
            totalScore: item.totalScore
          })
        }
        this.$dialog({
          modal: () => import('./components/suplierExpert.vue'),
          data: {
            title: this.$t('评分明细'),
            rfxCode: this.$route.query.rfxCode,
            dataInfo: e.data,
            supplierArr: supplierArr,
            scoreGroup: this.$route.query.scoreGroup,
            scoreProcess: e.data.scoreProcess,
            supplierCode: e.data.supplierCode,
            supplierId: e.data.supplierId
          }
        })
      } else if (e.field == 'comprehensiveAssessment') {
        this.$dialog({
          modal: () => import('./components/comprehensiveAssessment.vue'),
          data: {
            title: this.$t('综合评价'),
            leaderEvaluation: e.data.leaderEvaluation,
            needButton:
              e.data.comprehensiveAssessment == this.$t('综合评价') ||
              e.data.comprehensiveAssessment == this.$t('编辑')
                ? true
                : false
          },
          success: (data) => {
            const grid = this.$refs.template1.getCurrentTabRef().grid
            let dataSource = grid.$options.propsData.dataSource
            dataSource.forEach((item) => {
              if (item.id == e.data.id) {
                item.comprehensiveAssessment = this.$t('编辑')
                item.leaderEvaluation = data
              }
            })
            grid.refresh() //刷新表格
            this.$emit('editLeaderEvaluationComplete', {
              id: e.data.id,
              leaderEvaluation: data
            })
          }
        })
      } else if (e.field == 'rejectReason') {
        this.$dialog({
          modal: () => import('./components/remarkScoreReason.vue'),
          data: {
            title: this.$t('驳回'),
            reason: e.data.rejectReason,
            needButton: false
          }
        })
      }
    },
    submitReMarkScore(_selectGridRecords) {
      this.$dialog({
        modal: () => import('./components/remarkScoreReason.vue'),
        data: {
          title: this.$t('驳回'),
          reason: '',
          needButton: true
        },
        success: (data) => {
          let expertRejectDTOS = []
          _selectGridRecords.map((item) => {
            expertRejectDTOS.push({
              expertCode: item.expertCode,
              rejectReason: data
            })
          })
          let params = {
            rfxCode: this.$route.query.rfxCode,
            bidType: this.$route.query.scoreGroup,
            expertRejectDTOS: expertRejectDTOS
          }
          this.$API.rfxExpert.expertScoreRescore(params).then((res) => {
            this.$router.push({ name: 'expert-rating' })
            this.$toast({
              content: res.msg,
              type: 'success'
            })
          })
        }
      })
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'gradePreview') {
        this.preview()
        return
      }
      if (e.toolbar.id == 'reMarkScore') {
        let _selectGridRecords = e.grid.getSelectedRecords()
        if (_selectGridRecords.length < 1) {
          this.$toast({ content: this.$t('至少选择一行'), type: 'warning' })
          return
        }
        this.submitReMarkScore(_selectGridRecords)
      }
    },
    getExpertDimensionList() {
      this.$API.rfxExpert
        .getExpertScoreSummaryQuery({
          bidType: this.$route.query.scoreGroup,
          rfxCode: this.$route.query.rfxCode,
          requestParams: {
            page: { current: 1, size: 10000 }
          }
        })
        .then((res) => {
          let supplierCols = []
          let supplierList = []
          if (res.code == 200) {
            if (res.data?.records.length > 0) {
              for (let supplier of res.data.records[0].bidScoreSupplierDTOList) {
                supplierCols.push({
                  field: supplier.supplierCode || supplier.supplierId,
                  headerText: this.$t('供应商') + supplier.supplierName + this.$t('得分')
                })
                supplierList.push({
                  supplierName: supplier.supplierName,
                  supplierCode: supplier.supplierCode,
                  supplierId: supplier.supplierId
                })
              }
              this.expertList = res.data.records
              for (let item of res.data.records) {
                for (let supplier of item.bidScoreSupplierDTOList) {
                  let _key = supplier.supplierCode || supplier.supplierId
                  item[_key] = supplier.totalScore
                }
              }
            }
          }
          let columnData = this.pageConfig0[0].grid.columnData.slice(0, 5)
          this.$set(
            this.pageConfig0[0].grid,
            'columnData',
            columnData.concat(supplierCols).concat([
              {
                field: 'viewScoreDetail',
                headerText: this.$t('查看评分明细'),
                cssClass: 'field-content',
                valueConverter: {
                  type: 'placeholder',
                  placeholder: this.$t('明细')
                }
              },
              {
                field: 'rejectReason',
                headerText: this.$t('驳回原因'),
                cssClass: 'field-content',
                valueConverter: {
                  type: 'function',
                  filter: (e) => {
                    if (e == null) {
                      return '--'
                    } else {
                      return this.$t('查看')
                    }
                  }
                }
              }
            ])
          )
          this.supplierList = supplierList
          this.$set(this.pageConfig0[0].grid, 'dataSource', res.data.records)
        })
    }
  }
}
</script>
