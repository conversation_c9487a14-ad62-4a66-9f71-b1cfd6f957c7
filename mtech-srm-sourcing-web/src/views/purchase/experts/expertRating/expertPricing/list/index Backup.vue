<template>
  <div class="full-height mt-flex-direction-column">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="operate-content">
          <p class="operate-input">{{ scheduleTemplateInfo.rfxCode }}</p>
        </div>
        <div class="btns-wrap">
          <mt-button v-show="$route.query.scoreOperate == 1" @click.native="getAddexpert">{{
            $t('评分确认')
          }}</mt-button>
          <mt-button @click.native="comeBack">{{ $t('返回') }}</mt-button>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="forObject">
          <mt-form-item ref="rfxCode" prop="id" :label="$t('招标编号')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.rfxCode"
              :placeholder="$t('请输入招标编号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="rfxName" prop="id" :label="$t('标题')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.rfxName"
              :placeholder="$t('请输入标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="companyName" prop="id" :label="$t('公司')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.companyName"
              :placeholder="$t('请输入公司')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="openBidTime" prop="id" :label="$t('开标时间')">
            <mt-date-time-picker
              width="300"
              float-label-type="Never"
              disabled
              time-stamp
              format="yyyy-MM-dd HH:mm:ss"
              v-model="forObject.openBidTime"
              :placeholder="$t('请输入开标时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item ref="currentRound" prop="id" :label="$t('轮次')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.currentRound"
              :placeholder="$t('请输入轮次')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="sourcingMode" prop="id" :label="$t('寻源方式')">
            <mt-select
              :width="300"
              :data-source="sourcingModeArr"
              disabled
              v-model="forObject.sourcingMode"
              :show-clear-button="true"
              :placeholder="$t('请选择寻源方式')"
            ></mt-select>
          </mt-form-item>
          <!-- <mt-form-item ref="formItemarea" prop="id" :label="$t('寻源方式')">
            <mt-input
              width="300"
              float-label-type="Never"
              :placeholder="$t('请输入寻源方式')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="formItemarea" prop="id" :label="$t('寻源方式')">
            <mt-input
              width="300"
              float-label-type="Never"
              :placeholder="$t('请输入寻源方式')"
            ></mt-input>
          </mt-form-item> -->
        </mt-form>
      </div>
    </div>
    <div class="relation-ships">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </div>
</template>

<script>
import { columnDataScore, toolbar, columnDataBid } from './config'
import { utils } from '@mtech-common/utils'
export default {
  name: 'ExpertRating',
  data() {
    return {
      pageConfig: [],
      forObject: {
        openBidTime: '', //开标时间
        sourcingMode: '', //寻源方式
        rfxCode: '',
        rfxName: '',
        companyName: '',
        currentRound: '',
        bidType: ''
      },
      sourcingModeArr: [
        { text: this.$t('询报价'), value: 'rfq' },
        // { text: this.$t("直接定价"), value: "direct_pricing" },
        { text: this.$t('招投标'), value: 'invite_bids' },
        { text: this.$t('竞价'), value: 'bidding_price' }
      ],
      scheduleTemplateInfo: {},
      tenderItemsData: [],
      expertList: [],
      supplierList: []
    }
  },
  mounted() {
    this.pageConfig = [
      {
        title: this.$t('评分'),
        useToolTemplate: false,
        toolbar: this.$route.query.scoreOperate == 1 ? toolbar : [],
        grid: {
          allowFiltering: true,
          lineIndex: true,
          columnData: columnDataScore,
          dataSource: []
        }
      },
      {
        title: this.$t('标的'),
        useToolTemplate: false,
        toolbar,
        grid: {
          allowFiltering: true,
          lineIndex: true,
          columnData: columnDataBid(this.$route.query.scoreGroup),
          dataSource: []
        }
      }
    ]

    this.scheduleTemplateInfo = localStorage?.scheduleTemplateInfo
      ? JSON.parse(localStorage.scheduleTemplateInfo)
      : {}
    this.getExpertList()
    this.getTableTitle()
  },
  methods: {
    comeBack() {
      this.$router.push({ name: 'expert-rating' })
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'reMarkScore') {
        this.submitReMarkScore(_selectGridRecords)
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'viewScoreDetail') {
        // 打分明细弹框
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ './components/index.vue'
            ),
          data: {
            title: this.$t('评分明细'),
            rfxCode: this.$route.query.rfxCode,
            expertList: this.expertList,
            supplierList: this.supplierList,
            currentExpertCode: e.data.expertCode,
            bidType: this.$route.query.scoreGroup
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (e.field == 'viewSupplyFilelist') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
            ),
          data: {
            fileData: utils.cloneDeep(e.data.fileList),
            isView: true, // 是否为预览
            required: false, // 是否必须
            needButton: false,
            title: this.$t('查看附件')
          }
        })
      }
    },
    getTableTitle() {
      let rfxCode = this.$route.query.rfxCode
      let params = { rfxCode }
      this.$API.rfxExpert.findByRfxCode(params).then((res) => {
        this.forObject.rfxCode = res.data.rfxCode
        this.forObject.rfxName = res.data.rfxName
        this.forObject.openBidTime = res.data.strategyResponse.openBidTime
        this.forObject.sourcingMode = res.data.sourcingMode
        this.forObject.companyName = res.data.companyName
        this.forObject.currentRound = res.data.currentRound
      })
    },
    getExpertList() {
      this.$API.rfxExpert
        .getExpertScoreSummaryQuery({
          bidType: this.$route.query.scoreGroup,
          rfxCode: this.$route.query.rfxCode,
          requestParams: {
            page: { current: 1, size: 10000 }
          }
        })
        .then((res) => {
          let supplierCols = []
          let expertList = []
          let supplierList = []
          if (res.code == 200) {
            if (res.data?.records.length > 0) {
              for (let supplier of res.data.records[0].bidScoreSupplierDTOList) {
                supplierCols.push({
                  field: supplier.supplierCode,
                  headerText: this.$t('供应商') + supplier.supplierName + this.$t('得分')
                })
                supplierList.push({
                  supplierName: supplier.supplierName,
                  supplierCode: supplier.supplierCode
                })
              }
              for (let item of res.data.records) {
                let expert = {
                  expertName: item.expertName,
                  expertCode: item.expertCode
                }
                for (let supplier of item.bidScoreSupplierDTOList) {
                  item[supplier.supplierCode] = supplier.totalScore
                  expert[supplier.supplierCode] = supplier.totalScore
                  expert[supplier.supplierCode + '-remark'] = supplier.remark
                }
                expertList.push(expert)
              }
            }
          }
          let columnData = this.pageConfig[0].grid.columnData.slice(0, 5)
          this.$set(
            this.pageConfig[0].grid,
            'columnData',
            columnData.concat(supplierCols).concat({
              field: 'viewScoreDetail',
              headerText: this.$t('查看评分明细'),
              cssClass: 'field-content',
              valueConverter: {
                type: 'placeholder',
                placeholder: this.$t('明细')
              }
            })
          )
          this.expertList = expertList
          this.supplierList = supplierList
          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
        })

      this.$set(this.pageConfig[1].grid, 'asyncConfig', {
        url: this.$API.rfxExpert.getExpertScore,
        queryBuilderWrap: 'requestParams',
        params: {
          rfxCode: this.$route.query.rfxCode
        }
      })
    },
    submitReMarkScore(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.expertCode)
      })
      let params = {
        rfxCode: this.$route.query.rfxCode,
        bidType: this.$route.query.scoreGroup,
        expertCodeList: _selectIds
      }
      this.$API.rfxExpert.expertScoreRescore(params).then((res) => {
        this.$router.push({ name: 'expert-rating' })
        this.$toast({
          content: res.msg,
          type: 'success'
        })
      })
    },

    getAddexpert() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行评分操作？`)
        },
        success: () => {
          // const bidType = this.forObject.bidType;
          let params = {
            bidType: this.$route.query.scoreGroup,
            rfxCode: this.$route.query.rfxCode
          }
          this.$API.rfxExpert.getRfxAddSaveExpert(params).then((res) => {
            if (res.code == 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({
                content: res.msg,
                type: 'success'
              })
              this.$router.push({
                name: `expert-rating` // 专家评分列表页面
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
/deep/.mt-template-page {
  height: 100%;
  /deep/.common-template-page {
    height: 100%;
  }
}
.operate-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.operate-bar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4f5b6d;
  font-size: 14px;
  padding: 20px;
  .btns-wrap {
    /deep/ .mt-button {
      margin-right: 0;
      button {
        width: 76px;
        height: 34px;
        background: transparent;
        //border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        box-shadow: unset;
        padding: 0;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
    }
  }
}
.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(180deg);
  }
}
.miam-container {
  background: #fff;
  padding: 0 20px;

  .flex-d-c {
    flex-direction: column;
  }

  .mian-info {
    // height: 300px;
    background: rgba(255, 255, 255, 1);
    // border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    padding: 20px;
    min-width: 1300px;
    .normal-title {
      width: 100%;
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      color: #292929;
      font-family: PingFangSC;
      font-weight: 500;

      &:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        width: 2px;
        height: 10px;
        background: rgba(0, 70, 156, 1);
        border-radius: 1px;
        margin-right: 10px;
      }
    }

    .flex-d-c {
      flex-direction: column;
    }

    .input-item {
      margin-top: 20px;
      padding-right: 50px;
      .label-txt {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #292929;
      }
      .label-value {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #35404e;
      }
      .select-container {
        height: 40px;
      }
      .e-label {
        color: #35404e;
      }
      .label-text {
        color: #35404e;
      }
    }
    .input-item /deep/ .normal-width {
      width: 240px;
    }
    .input-item /deep/ .e-radio + label .e-label {
      color: #35404e;
    }
  }
}
.relation-ships {
  flex: 1;
  background: rgba(255, 255, 255, 1);
}
</style>
