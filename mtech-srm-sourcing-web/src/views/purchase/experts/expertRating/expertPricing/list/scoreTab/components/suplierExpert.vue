<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog ref="dialog" css-class="create-proj-dialog" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <div class="expert-input">
        <div class="expert_box">
          <mt-select
            v-if="currentSupplierCode"
            width="200"
            v-model="currentSupplierCode"
            :data-source="supplierList"
            @change="supplierChange"
            :fields="{
              text: 'supplierName',
              value: 'supplierCode'
            }"
            float-label-type="Never"
            :placeholder="$t('请选择供应商')"
            :show-clear-button="true"
          ></mt-select>
          <mt-select
            v-else
            width="200"
            v-model="currentSupplierId"
            :data-source="supplierList"
            @change="supplierChange"
            :fields="{
              text: 'supplierName',
              value: 'supplierId'
            }"
            float-label-type="Never"
            :placeholder="$t('请选择供应商')"
            :show-clear-button="true"
          ></mt-select>
        </div>
        <div class="expert_box">
          <p>{{ $t('得分：') }}</p>
          <mt-input width="150" float-label-type="Never" disabled v-model="totalScore"></mt-input>
        </div>
      </div>
      <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      totalScore: '',
      supplierList: [],
      currentSupplierCode: '',
      currentSupplierId: ''
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.currentSupplierCode = this.modalData.dataInfo.supplierCode
    this.currentSupplierId = this.modalData.dataInfo.supplierId
    this.supplierList = this.modalData.supplierArr
    this.totalScore = this.modalData.dataInfo.totalScore
  },
  methods: {
    supplierChange(e) {
      this.totalScore = e.itemData.totalScore
      this.$nextTick(() => {
        this.getExpertList()
      })
    },
    getExpertList() {
      this.$API.rfxExperttechnicalBid
        .getExpertScoreAddList({
          rfxCode: this.modalData.rfxCode,
          bidType: this.modalData.scoreGroup,
          supplierCode: this.currentSupplierCode,
          supplierId: this.currentSupplierId,
          requestParams: {
            page: { current: 1, size: 10000 }
          }
        })
        .then((res) => {
          let records = res.data.bidScoreSupplierDetailBySupplierDTOIPage.records
          let expertCols = []
          records.forEach((item, index) => {
            if (index == 0) {
              for (let expert of item.bidExpertScoreBySupplierDetailDTOS) {
                expertCols.push({
                  field: expert.expertCode,
                  headerText: this.$t('专家') + expert.expertName + this.$t('评分'),
                  valueConverter: {
                    type: 'placeholder',
                    placeholder: '--'
                  }
                })
              }
            }
            for (let expert of item.bidExpertScoreBySupplierDetailDTOS) {
              item[expert.expertCode] = expert.score
            }
          })

          let columnData = this.pageConfig[0].grid.columnData.slice(0, 6)
          this.$set(this.pageConfig[0].grid, 'columnData', columnData.concat(expertCols))
          this.$set(this.pageConfig[0].grid, 'dataSource', records)
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expert-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  height: 100%;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}
</style>
