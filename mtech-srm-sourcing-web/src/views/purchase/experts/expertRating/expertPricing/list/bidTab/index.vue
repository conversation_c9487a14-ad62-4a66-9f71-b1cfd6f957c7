<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { columnDataBid } from './config/config'
import { utils } from '@mtech-common/utils'
export default {
  name: 'BidTab',
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: toolbar,
          grid: {
            allowFiltering: true,
            lineIndex: true,
            columnData: columnDataBid(this.$route.query.scoreGroup),
            asyncConfig: {
              url: this.$API.rfxExpert.getExpertScore,
              queryBuilderWrap: 'requestParams',
              params: {
                rfxCode: this.$route.query.rfxCode
              },
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.fileList == null) {
                    item.viewSupplyFilelist = ''
                  } else {
                    item.viewSupplyFilelist = this.$t('查看')
                  }
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'viewSupplyFilelist') {
        this.$dialog({
          modal: () => import('COMPONENTS/Upload/uploaderDialog.vue'),
          data: {
            fileData: utils.cloneDeep(e.data.fileList),
            isView: true, // 是否为预览
            required: false, // 是否必须
            needButton: false,
            title: this.$t('查看附件')
          }
        })
      }
    }
  }
}
</script>
