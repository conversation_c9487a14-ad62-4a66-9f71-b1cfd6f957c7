import { i18n } from '@/main.js'
export const columnDataExpert = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'expertName',
    headerText: i18n.t('评分人')
  },
  {
    field: 'mobilePhone',
    headerText: i18n.t('电话')
  },
  {
    field: 'email',
    headerText: i18n.t('邮箱')
  },
  {
    field: 'scoreProcess',
    headerText: i18n.t('评分进度')
  }
]
export const columnDataSupplier = (scoreGroup) => {
  return [
    {
      width: '60',
      type: 'checkbox'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'biddingCode',
      headerText: i18n.t('投标编号')
    },
    {
      field: 'totalScore',
      headerText: scoreGroup == 0 ? i18n.t('技术总得分') : i18n.t('商务总得分')
    },
    {
      field: 'viewScoreSupplierDetail',
      headerText: i18n.t('评分明细'),
      cssClass: 'field-content',
      valueConverter: {
        type: 'placeholder',
        placeholder: i18n.t('查看')
      }
    },
    {
      field: 'comprehensiveAssessment',
      headerText: i18n.t('综合评价'),
      cssClass: 'field-content'
    }
  ]
}
export const toolbar = [
  {
    id: 'reMarkScore',
    icon: 'icon_solid_Createorder',
    title: i18n.t('驳回')
  },
  {
    id: 'gradePreview',
    icon: 'icon_table_filter',
    title: i18n.t('评分一览表')
  }
]
