<template>
  <div class="expert-file">
    <div>{{ $t('专家评分附件') }}</div>
    <mt-template-page
      ref="templatePage"
      :template-config="scoreFileConfig"
      @handleClickCellTitle="handleScoreCellTitle"
      @handleClickCellTool="handleScoreCellTool"
    />
  </div>
</template>
<script>
import { download } from '@/utils/utils'
import { scoreFileConfig } from './config/experts'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      scoreFileConfig: scoreFileConfig(this.$API.rfxExpert.getScoreFileListUrl, this.modalData),
      scoreFileList: [],
      saveUrl: '/api/file/user/file/uploadPublic?useType=1', // 文件上传路径待
      downloadUrl: '/api/file/user/file/downloadPublicFile' //文件下载
    }
  },
  watch: {
    'modalData.supplierCode'(val) {
      if (val) {
        this.scoreFileConfig = scoreFileConfig(
          this.$API.rfxExpert.getScoreFileListUrl,
          this.modalData
        )
      }
    },
    'modalData.supplierId'(val) {
      if (!this.modalData.supplierCode && val && val !== '0') {
        this.scoreFileConfig = scoreFileConfig(
          this.$API.rfxExpert.getScoreFileListUrl,
          this.modalData
        )
      }
    }
  },
  methods: {
    handleScoreCellTitle(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e?.data?.sysFileId || e?.data?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    handleScoreCellTool(e) {
      if (e.tool.id === 'download') {
        this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId }).then((res) => {
          download({
            fileName: e.data.fileName,
            blob: new Blob([res.data])
          })
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.expert-file {
  margin: 16px 0;
  ::v-deep .toolbar-container {
    padding: 0 !important;
  }
  .common-uploader {
    display: none;
  }
}
</style>
