<template>
  <div class="full-height mt-flex-direction-column">
    <div class="miam-contaFiner">
      <div class="operate-bar">
        <div class="operate-content">
          <p class="operate-input">{{ scheduleTemplateInfo.rfxCode }}</p>
        </div>
        <div class="btns-wrap">
          <mt-button v-show="$route.query.scoreOperate == 1" @click.native="submitScoreConfirm">{{
            $t('评分确认')
          }}</mt-button>
          <mt-button @click.native="comeBack">{{ $t('返回') }}</mt-button>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="forObject">
          <mt-form-item ref="rfxCode" prop="id" :label="$t('招标编号')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.rfxCode"
              :placeholder="$t('请输入招标编号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="rfxName" prop="id" :label="$t('标题')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.rfxName"
              :placeholder="$t('请输入标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="companyName" prop="id" :label="$t('公司')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.companyName"
              :placeholder="$t('请输入公司')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="openBidTime" prop="id" :label="$t('开标时间')">
            <mt-date-time-picker
              width="300"
              float-label-type="Never"
              disabled
              time-stamp
              format="yyyy-MM-dd HH:mm:ss"
              v-model="forObject.openBidTime"
              :placeholder="$t('请输入开标时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item ref="currentRound" prop="id" :label="$t('轮次')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.currentRound"
              :placeholder="$t('请输入轮次')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="sourcingMode" prop="id" :label="$t('寻源方式')">
            <mt-select
              :width="300"
              :data-source="sourcingModeArr"
              disabled
              v-model="forObject.sourcingMode"
              :show-clear-button="true"
              :placeholder="$t('请选择寻源方式')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div class="relation-ships mt-flex-direction-column">
      <div class="top-info">
        <mt-tabs
          :e-tab="false"
          :data-source="tabSource"
          @handleSelectTab="handleSelectTab"
        ></mt-tabs>
        <div class="expert_box" v-show="tabIndex == 0">
          <div class="dimensionButton" v-show="dimension == 'expertDimension'">
            <img src="@/assets/iconSvg/expertDimension.svg" /><span
              style="color: #00469c; font-weight: bold"
              >{{ $t('专家维度') }}</span
            >
          </div>
          <div
            class="dimensionButton"
            v-show="dimension != 'expertDimension'"
            @click="switchDimension('expertDimension')"
          >
            <img src="@/assets/iconSvg/expertDimensionGrey.svg" /><span>{{ $t('专家维度') }}</span>
          </div>
          <div class="dimensionButton" v-show="dimension == 'supplierDimension'">
            <img src="@/assets/iconSvg/supplierDimension.svg" /><span
              style="color: #00469c; font-weight: bold"
              >{{ $t('供应商维度') }}</span
            >
          </div>
          <div
            class="dimensionButton"
            @click="switchDimension('supplierDimension')"
            v-show="dimension != 'supplierDimension'"
          >
            <img src="@/assets/iconSvg/supplierDimensionGrey.svg" /><span>{{
              $t('供应商维度')
            }}</span>
          </div>
        </div>
      </div>
      <div style="flex: 1">
        <scoreTab
          ref="scoreTab"
          @editLeaderEvaluationComplete="editLeaderEvaluationComplete"
          v-if="tabIndex == 0"
        ></scoreTab>
        <bidTab v-if="tabIndex == 1"></bidTab>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExpertRating',
  components: {
    scoreTab: () => import('./scoreTab/index.vue'),
    bidTab: () => import('./bidTab/index.vue')
  },
  data() {
    return {
      dimension: 'expertDimension',
      tabIndex: 0,
      tabSource: [
        {
          title: this.$t('评分')
        },
        {
          title: this.$t('标的')
        }
      ],
      forObject: {
        openBidTime: '', //开标时间
        sourcingMode: '', //寻源方式
        rfxCode: '',
        rfxName: '',
        companyName: '',
        currentRound: '',
        bidType: ''
      },
      sourcingModeArr: [
        { text: this.$t('询报价'), value: 'rfq' },
        { text: this.$t('招投标'), value: 'invite_bids' },
        { text: this.$t('竞价'), value: 'bidding_price' }
      ],
      scheduleTemplateInfo: {},
      leaderEvaluationList: []
    }
  },
  mounted() {
    this.scheduleTemplateInfo = localStorage?.scheduleTemplateInfo
      ? JSON.parse(localStorage.scheduleTemplateInfo)
      : {}
    this.getTableTitle()
  },
  methods: {
    switchDimension(type) {
      this.dimension = type
      if (type == 'expertDimension') {
        this.$refs.scoreTab.tabIndex = 0
      } else if (type == 'supplierDimension') {
        this.$refs.scoreTab.tabIndex = 1
      }
    },
    editLeaderEvaluationComplete(data) {
      let flag = true
      for (let item of this.leaderEvaluationList) {
        if (item.id == data.id) {
          item.leaderEvaluation = data.leaderEvaluation
          flag = false
          break
        }
      }
      if (flag) {
        this.leaderEvaluationList.push(data)
      }
    },
    submitScoreConfirm() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行评分操作？`)
        },
        success: () => {
          let params = {
            bidType: this.$route.query.scoreGroup,
            rfxCode: this.$route.query.rfxCode,
            scoreLeaderConfirmDTOList: this.leaderEvaluationList
          }
          this.$API.rfxExpert.getRfxAddSaveExpert(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: res.msg,
                type: 'success'
              })
              this.$router.push({
                name: `expert-rating` // 专家评分列表页面
              })
            }
          })
        }
      })
    },
    getTableTitle() {
      let rfxCode = this.$route.query.rfxCode
      let params = { rfxCode }
      this.$API.rfxExpert.findByRfxCode(params).then((res) => {
        this.forObject.rfxCode = res.data.rfxCode
        this.forObject.rfxName = res.data.rfxName
        this.forObject.openBidTime = res.data.strategyResponse.openBidTime
        this.forObject.sourcingMode = res.data.sourcingMode
        this.forObject.companyName = res.data.companyName
        this.forObject.currentRound = res.data.currentRound
      })
    },
    comeBack() {
      this.$router.push({ name: 'expert-rating' })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>

<style scoped lang="scss">
.expert_box {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-right: 20px;
  .dimensionButton {
    height: 28px;
    line-height: 24px;
    width: 100px;
    border: 1px solid #e8e8e8;
    border-radius: 4px 0 0 4px;
    text-align: center;
    cursor: pointer;
    img {
      vertical-align: middle;
    }
    span {
      margin-left: 5px;
      font-size: 12px;
    }
  }
}
.top-info {
  width: 100%;
  border-radius: 0 8px 0 0;
  display: flex;
  align-items: center;

  /deep/ .mt-tabs-container {
    background: #fff;
    .tabs-arrow {
      display: none;
    }
    .tab-wrap {
      padding: 0;
      height: 50px;
      .tab-item {
        padding: 6px 10px;
        span {
          line-height: 1;
        }
      }
    }
  }
}
/deep/.mt-template-page {
  height: 100%;
  /deep/.common-template-page {
    height: 100%;
  }
}
.operate-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.operate-bar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4f5b6d;
  font-size: 14px;
  padding: 20px;
  .btns-wrap {
    /deep/ .mt-button {
      margin-right: 0;
      button {
        width: 76px;
        height: 34px;
        background: transparent;
        //border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        box-shadow: unset;
        padding: 0;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
    }
  }
}
.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(180deg);
  }
}
.miam-container {
  background: #fff;
  padding: 0 20px;

  .flex-d-c {
    flex-direction: column;
  }

  .mian-info {
    // height: 300px;
    background: rgba(255, 255, 255, 1);
    // border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    padding: 20px;
    min-width: 1300px;
    .normal-title {
      width: 100%;
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      color: #292929;
      font-family: PingFangSC;
      font-weight: 500;

      &:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        width: 2px;
        height: 10px;
        background: rgba(0, 70, 156, 1);
        border-radius: 1px;
        margin-right: 10px;
      }
    }

    .flex-d-c {
      flex-direction: column;
    }

    .input-item {
      margin-top: 20px;
      padding-right: 50px;
      .label-txt {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #292929;
      }
      .label-value {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #35404e;
      }
      .select-container {
        height: 40px;
      }
      .e-label {
        color: #35404e;
      }
      .label-text {
        color: #35404e;
      }
    }
    .input-item /deep/ .normal-width {
      width: 240px;
    }
    .input-item /deep/ .e-radio + label .e-label {
      color: #35404e;
    }
  }
}
.relation-ships {
  flex: 1;
  background: rgba(255, 255, 255, 1);
}
</style>
