<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog ref="dialog" css-class="create-proj-dialog" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <div>
        <div class="expert_box" style="padding-bottom: 10px">
          <div style="width: 50%; padding-right: 5px">
            <div>{{ $t('供应商') }}</div>
            <mt-select
              v-if="currentSupplierCode"
              style="width: 100%"
              :data-source="supplierList"
              v-model="currentSupplierCode"
              :fields="{
                text: 'supplierName',
                value: 'supplierCode'
              }"
              @change="currentSupplierCodeChange"
              :show-clear-button="true"
            ></mt-select>
            <mt-select
              v-else
              style="width: 100%"
              :data-source="supplierList"
              v-model="currentSupplierId"
              :fields="{
                text: 'supplierName',
                value: 'supplierId'
              }"
              @change="currentSupplierCodeChange"
              :show-clear-button="true"
            ></mt-select>
          </div>
          <div style="width: 50%; padding-left: 5px">
            <div>{{ $t('评分人') }}</div>
            <mt-select
              style="width: 100%"
              :data-source="expertList"
              v-model="currentExpertCode"
              :fields="{
                text: 'expertName',
                value: 'expertCode'
              }"
              @change="currentExpertCodeChange"
              :show-clear-button="true"
            ></mt-select>
          </div>
        </div>
        <!-- 专家评分附件 -->
        <score-file :modal-data="modalData" />
        <div class="dialog-box">
          <div style="width: 100%">
            <div style="width: 105px">
              <p>{{ $t('综合评价') }}：</p>
            </div>
            <div style="width: 100%; padding-top: 10px; padding-bottom: 20px">
              <mt-input
                :multiline="true"
                :rows="1"
                style="width: 100%"
                maxlength="1000"
                v-model="remark"
                float-label-type="Never"
                :disabled="true"
              ></mt-input>
            </div>
          </div>
        </div>
        <div class="expert_box" style="justify-content: flex-start; padding-bottom: 20px">
          <p style="font-weight: bold; line-height: 32px">{{ $t('得分') }}：</p>
          <div class="currentScoreDiv">
            {{ currentScore }}
          </div>
          <!-- <mt-input
            width="150"
            float-label-type="Never"
            disabled
            v-model="currentScore"
          ></mt-input> -->
        </div>
      </div>

      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
import ScoreFile from './expertScoreFile.vue'

export default {
  components: {
    ScoreFile
  },
  data() {
    return {
      currentExpertCode: '',
      currentSupplierCode: '',
      currentSupplierId: '',
      expertList: [],
      supplierList: [],
      currentScore: 0,
      pageConfig: pageConfig,
      remark: ''
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  created() {
    this.supplierList = this.modalData.supplierList
    this.expertList = this.modalData.expertList
    this.currentExpertCode = this.modalData.currentExpertCode
    this.currentSupplierCode = this.modalData.supplierList[0].supplierCode
    this.currentSupplierId = this.modalData.supplierList[0].supplierId
    this.modalData.supplierCode = this.modalData.supplierList[0].supplierCode
    this.modalData.supplierId = this.modalData.supplierList[0].supplierId
    this.getExpertList()
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    currentSupplierCodeChange(val) {
      this.modalData.supplierCode = val.itemData.supplierCode
      this.modalData.supplierId = val.itemData.supplierId
      if (val.itemData) {
        this.currentSupplierCode = val.itemData.supplierCode
        this.currentSupplierId = val.itemData.supplierId
      } else {
        this.currentSupplierCode = ''
        this.currentSupplierId = ''
      }
      this.getExpertList()
    },
    currentExpertCodeChange(val) {
      if (val.itemData) {
        this.currentExpertCode = val.itemData.expertCode
      } else {
        this.currentExpertCode = ''
      }
      this.getExpertList()
    },
    getExpertList() {
      this.$API.rfxExpert
        .getExpertSummaryQueryDetail({
          rfxCode: this.modalData.rfxCode,
          bidType: this.modalData.bidType,
          expertCode: this.currentExpertCode,
          supplierCode: this.currentSupplierCode,
          supplierId: this.currentSupplierId,
          requestParams: {
            page: { current: 1, size: 10000 }
          }
        })
        .then((res) => {
          this.currentScore = res.data.score
          this.remark = res.data.remark
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            res.data.bidScoreSupplierDetailDTOIPage.records
          )
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.currentScoreDiv {
  width: 75px;
  height: 32px;
  line-height: 32px;
  background-color: rgba($color: #ed5633, $alpha: 0.1);
  color: #ed5633;
  text-align: center;
  font-weight: bold;
}
.expert_box {
  display: flex;
  align-content: center;
  justify-content: center;
}
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
}
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expert-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.dialog-content {
  font-size: 16px;
  height: 100%;
  padding-bottom: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}
</style>
