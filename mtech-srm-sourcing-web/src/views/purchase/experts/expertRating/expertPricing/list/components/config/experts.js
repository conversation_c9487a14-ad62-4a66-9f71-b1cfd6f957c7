import { i18n } from '@/main.js'

const scoreFileCols = [
  {
    field: 'fileName',
    headerText: i18n.t('文件名称'),
    cssClass: 'field-content'
  },
  {
    field: 'opration',
    headerText: i18n.t('操作'),
    cellTools: [
      {
        id: 'download',
        title: i18n.t('下载')
      }
    ]
  }
]
export const scoreFileConfig = (url, modalData) => [
  {
    useToolTemplate: false,
    useBaseConfig: false,
    toolbar: Number(modalData.scoreProcess || 0) < 1 ? toolbar : [],
    grid: {
      lineIndex: true,
      columnData: scoreFileCols,
      height: 'auto',
      allowPaging: false,
      dataSource: [],
      asyncConfig: {
        loading: false,
        url: url,
        recordsPosition: 'data',
        params: {
          rfxCode: modalData.rfxCode,
          supplierCode: modalData.supplierCode,
          supplierId: modalData.supplierId
        }
      }
    }
  }
]
