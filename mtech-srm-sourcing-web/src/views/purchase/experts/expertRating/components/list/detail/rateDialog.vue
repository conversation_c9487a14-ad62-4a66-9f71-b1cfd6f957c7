<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    height="900"
    :header="header"
    :buttons="buttons"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div style="padding-bottom: 20px">
        <p>{{ $t('评分供应商') }}：</p>
        <!-- 此处作兼容，有可能supplierCode没有，则取supplierId -->
        <mt-select
          v-if="modalData.supplierCode"
          style="width: 100%"
          float-label-type="Never"
          :data-source="modalData.supplierArr"
          :fields="{
            text: 'supplierName',
            value: 'supplierCode'
          }"
          :value="modalData.supplierCode"
          @change="changeSupplier"
          :show-clear-button="true"
        ></mt-select>
        <mt-select
          v-else
          style="width: 100%"
          float-label-type="Never"
          :data-source="modalData.supplierArr"
          :fields="{
            text: 'supplierName',
            value: 'supplierId'
          }"
          :value="modalData.supplierId"
          @change="changeSupplier"
          :show-clear-button="true"
        ></mt-select>
      </div>

      <div class="box_height">
        <div style="font-weight: bold">{{ $t('供应商方案附件') }}</div>
        <mt-template-page
          style="margin-top: 10px"
          :template-config="pageConfigFileList"
          @handleClickCellTitle="handleClickCellTitle"
        />
      </div>
      <!-- 专家评分附件 -->
      <score-file ref="scoreFile" :modal-data="modalData" />
      <div style="padding-bottom: 20px">
        <div style="font-weight: bold">{{ $t('评分评价') }}</div>
        <div class="box">
          <!-- <div class="op-item mt-flex">
            <span @click="handleSaveBtn"
              ><i class="mt-icons mt-icon-icon_table_new"></i
              >{{ $t('保存') }}&nbsp;&nbsp;&nbsp;</span
            >
            <span @click="comeBack"
              ><i class="mt-icons mt-icon-icon_table_new"></i>{{ $t('返回') }}</span
            >
          </div> -->
          <!-- <mt-button @click.native="comeBack">{{ $t("返回") }}</mt-button> -->
          <div class="expert-input">
            <div class="expert_box" style="padding-top: 10px; padding-bottom: 10px">
              <p style="font-weight: bold">{{ $t('得分计算') }}：</p>
              <span class="scoreValue">{{ formObject.totalScore }}</span>
            </div>
          </div>
        </div>
        <mt-template-page class="table" ref="templateRef" :template-config="pageConfig" />
      </div>
      <div class="dialog-box">
        <div style="width: 100%">
          <div>
            <span>{{ $t('综合评价') }}</span>
            <img style="margin-left: 10px" src="@/assets/iconSvg/quoteIcon.svg" />
            <span class="lastRemark" v-show="lastRemark != null" @click="quoteLastRemark">
              {{ $t('引用上次评价') }}
            </span>
          </div>
          <div style="width: 100%">
            <mt-input
              :multiline="true"
              :rows="3"
              style="width: 100%"
              maxlength="1000"
              v-model="formObject.remark"
              float-label-type="Never"
              :placeholder="$t('请输入1000字以内的评价')"
            ></mt-input>
          </div>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config/index'
import { pageConfigFileList, scoreFileConfig } from './config/experts'
import { download } from '@/utils/utils'
import ScoreFile from './expertScoreFile.vue'
export default {
  components: {
    ScoreFile
  },
  data() {
    return {
      lastRemark: null,
      pageConfigFileList: pageConfigFileList(
        this.$API.rfxExpert.getSkillFileListUrl,
        this.modalData
      ),
      scoreFileConfig: scoreFileConfig(this.$API.rfxExpert.getSkillFileListUrl, this.modalData),
      pageConfig: [
        {
          useToolTemplate: false,
          grid: {
            allowFiltering: true,
            // lineIndex: true,
            columnData: columnData,
            height: 'auto',
            allowPaging: false,
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Bottom'
            },
            asyncConfig: {
              url: this.$API.rfxExpert.getSaveRfxTexpert,
              recordsPosition: 'data.bidScoreSupplierDetailDTOIPage.records',
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.score == null) {
                    item.score = item.defaultScore
                  }
                })
                return list
              },
              afterAsyncData: this.updateTabTitle,
              page: {
                current: 1,
                size: 1000
              },
              queryBuilderWrap: 'requestParams',
              params: {
                rfxCode: this.modalData.rfxCode,
                bidType: 0,
                supplierCode: this.modalData.supplierCode,
                supplierId: this.modalData.supplierId,
                supplierTenantId: this.modalData.supplierTenantId
              }
            }
          }
        }
      ],
      formObject: {
        totalScore: '',
        remark: ''
      },
      buttons: [
        {
          click: this.comeBack,
          buttonModel: { content: this.$t('返回') }
        },
        {
          click: this.handleSaveBtn,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    quoteLastRemark() {
      this.formObject.remark = this.lastRemark
    },
    changeSupplier(obj) {
      this.modalData.supplierCode = obj.itemData.supplierCode
      this.modalData.supplierName = obj.itemData.supplierName
      this.modalData.supplierId = obj.itemData.supplierId
      this.modalData.supplierTenantId = obj.itemData.supplierTenantId
      this.pageConfigFileList = pageConfigFileList(
        this.$API.rfxExpert.getSkillFileListUrl,
        this.modalData
      )
      // 重置供应商参数
      let params = Object.assign({}, this.pageConfig[0].grid.asyncConfig.params, {
        supplierId: this.modalData.supplierId,
        supplierCode: this.modalData.supplierCode
      })
      this.$set(this.pageConfig[0].grid.asyncConfig, 'params', params)
    },
    updateTabTitle(res) {
      this.formObject.remark = res.data.remark
      this.formObject.totalScore = res.data.totalScore
      this.lastRemark = res.data.lastRemark
    },
    //单元格title文字点击，文件名点击预览
    handleClickCellTitle(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e?.data?.sysFileId || e?.data?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      } else if (e.field == 'operaDownload') {
        this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId }).then((res) => {
          download({
            fileName: e.data.fileName,
            blob: new Blob([res.data])
          })
        })
      }
    },
    handleSaveBtn() {
      this.endEdit()
      setTimeout(() => {
        this.saveDataGrid()
      }, 100)
    },
    comeBack() {
      this.cancel()
    },
    endEdit() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      _currentTabRef?.grid?.endEdit()
    },
    saveDataGrid() {
      if (this.modalData.rfxGeneralType === 2) {
        if (!this.$refs.scoreFile.scoreFileList.length) {
          this.$toast({
            content: this.$t('专家评分附件不能为空'),
            type: 'warning'
          })
          return
        }
      }
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
      if (_selectRecords.length) {
        for (let item of _selectRecords) {
          if (item.score < item.minScore || item.score > item.maxScore) {
            this.$toast({
              content: this.$t('得分分值不应低于最低分或高于最高分'),
              type: 'warning'
            })
            return
          }
        }
        let params = {
          rfxCode: this.modalData.rfxCode,
          bidType: 0,
          remark: this.formObject.remark,
          supplierCode: this.modalData.supplierCode,
          supplierId: this.modalData.supplierId,
          bidScoreSupplierDetailDTOS: _selectRecords
        }

        this.$API.rfxExpert.getRfxAddSave(params).then((res) => {
          if (res.code == 200) {
            this.$emit('confirm-function')
          }
        })
      }
    },

    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
// .box_height {
//   // height: 100%;
// }
.lastRemark {
  font-size: 14px;
  color: #00469c;
  cursor: pointer;
}
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.box {
  display: flex;
  align-items: center;
  justify-content: right;
  // .table {
  //   height: 200px;
  // }
}
.expert-input {
  display: flex;
  align-items: center;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
    .scoreValue {
      font-weight: bold;
      color: #ed5633;
      font-size: 16px;
      width: 50px;
    }
  }
}
.dialog-content {
  font-size: 16px;
  height: 100%;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}

// .repeat-template
//   .grid-container
//   .mt-data-grid
//   .e-control
//   .e-gridcontent
//   .e-content
//   .e-table {
//   height: 20px;
// }
</style>
