import { i18n } from '@/main.js'
const columnData = [
  {
    field: 'fileName',
    headerText: i18n.t('文件名称'),
    cssClass: 'field-content'
  },
  {
    field: 'fileDetailInfo',
    headerText: i18n.t('备注')
  },
  {
    field: 'operaDownload',
    headerText: i18n.t('操作'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('下载')
    }
  }
]
export const pageConfigFileList = (url, modalData) => [
  {
    useToolTemplate: false,
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: columnData,
      height: 'auto',
      allowPaging: false,
      dataSource: [],
      asyncConfig: {
        loading: false,
        url: url,
        recordsPosition: 'data',
        params: {
          rfxCode: modalData.rfxCode,
          bidType: 0,
          supplierCode: modalData.supplierCode,
          supplierId: modalData.supplierId
        }
      }
    }
  }
]

const scoreFileCols = (modalData) => [
  {
    field: 'fileName',
    headerText: i18n.t('文件名称'),
    cssClass: 'field-content'
  },
  {
    field: 'opration',
    headerText: i18n.t('操作'),
    cellTools: [
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: () => {
          return [0, 3, 4].includes(Number(modalData.scoreStatus))
        }
      },
      {
        id: 'download',
        title: i18n.t('下载')
      }
    ]
  }
]
const toolbar = [
  [
    { id: 'Add', icon: 'icon_solid_upload', title: i18n.t('上传附件') }
    // { id: 'Save', icon: 'icon_solid_save', title: i18n.t('保存') }
  ],
  []
]
export const scoreFileConfig = (url, modalData) => [
  {
    useToolTemplate: false,
    useBaseConfig: false,
    toolbar: [0, 3, 4].includes(Number(modalData.scoreStatus)) ? toolbar : [],
    grid: {
      lineIndex: true,
      columnData: scoreFileCols(modalData),
      height: 'auto',
      allowPaging: false,
      dataSource: [],
      asyncConfig: {
        loading: false,
        url: url,
        recordsPosition: 'data',
        params: {
          rfxCode: modalData.rfxCode,
          supplierCode: modalData.supplierCode,
          supplierId: modalData.supplierId
        }
      }
    }
  }
]
