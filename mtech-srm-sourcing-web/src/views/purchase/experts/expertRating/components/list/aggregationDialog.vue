<template>
  <div class="full-height mt-flex-direction-column">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="operate-content">
          <p class="operate-input">{{ scheduleTemplateInfo.rfxCode }}</p>
        </div>
        <div class="btns-wrap">
          <mt-button @click.native="submit">{{ $t('提交') }}</mt-button>
          <mt-button @click.native="comeBack">{{ $t('返回') }}</mt-button>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="forObject">
          <mt-form-item ref="rfxCode" prop="id" :label="$t('招标编号')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.rfxCode"
              :placeholder="$t('请输入招标编号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="rfxName" prop="id" :label="$t('标题')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.rfxName"
              :placeholder="$t('请输入标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="companyName" prop="id" :label="$t('公司')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.companyName"
              :placeholder="$t('请输入公司')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="openBidTime" prop="id" :label="$t('开标时间')">
            <mt-date-time-picker
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.openBidTime"
              time-stamp
              format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('请输入开标时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item ref="currentRound" prop="id" :label="$t('轮次')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.currentRound"
              :placeholder="$t('请输入轮次')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="sourcingMode" prop="id" :label="$t('寻源方式')">
            <mt-select
              :width="300"
              :data-source="sourcingModeArr"
              disabled
              v-model="forObject.sourcingMode"
              :show-clear-button="true"
              :placeholder="$t('请选择寻源方式')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div class="relation-ships">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  name: 'ExpertRating',
  data() {
    return {
      rejectReason: '',
      pageConfig: pageConfig,
      forObject: {
        openBidTime: '', //开标时间
        sourcingMode: '', //寻源方式
        rfxCode: '',
        rfxName: '',
        companyName: '',
        currentRound: ''
      },
      sourcingModeArr: [
        { text: this.$t('询报价'), value: 'rfq' },
        // { text: this.$t("直接定价"), value: "direct_pricing" },
        { text: this.$t('招投标'), value: 'invite_bids' },
        { text: this.$t('竞价'), value: 'bidding_price' }
      ],
      scheduleTemplateInfo: {}
    }
  },
  mounted() {
    this.scheduleTemplateInfo = localStorage?.scheduleTemplateInfo
      ? JSON.parse(localStorage.scheduleTemplateInfo)
      : {}
    this.getExpertList()
    this.getListExpert()
  },
  methods: {
    comeBack() {
      this.$router.push({ name: 'expert-rating' })
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'viewRejectReason') {
        this.$dialog({
          modal: () => import('../../expertPricing/list/scoreTab/components/remarkScoreReason.vue'),
          data: {
            title: this.$t('驳回'),
            reason: this.rejectReason,
            needButton: false
          }
        })
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'markScore') {
        // 打分明细弹框
        let records = this.$refs.templateRef.getCurrentTabRef().grid.dataSource
        let supplierArr = []
        for (let item of records) {
          supplierArr.push({
            supplierCode: item.supplierCode,
            supplierName: item.supplierName,
            supplierId: item.supplierId,
            supplierTenantId: item.supplierTenantId
          })
        }
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ './detail/rateDialog.vue'
            ),
          data: {
            title: this.$t('打分'),
            supplierCode: e.data.supplierCode,
            supplierName: e.data.supplierName,
            supplierId: e.data.supplierId,
            supplierTenantId: e.data.supplierTenantId,
            rfxCode: this.$route.query.rfxCode,
            supplierArr: supplierArr,
            scoreStatus: this.scheduleTemplateInfo?.scoreStatus,
            rfxGeneralType: this.forObject.rfxGeneralType
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    getListExpert() {
      let rfxCode = this.$route.query.rfxCode
      let params = { rfxCode }
      this.$API.rfxExpert.findByRfxCode(params).then((res) => {
        this.forObject.rfxCode = res.data.rfxCode
        this.forObject.rfxName = res.data.rfxName
        this.forObject.openBidTime = res.data.strategyResponse.openBidTime
        this.forObject.sourcingMode = res.data.sourcingMode
        this.forObject.companyName = res.data.companyName
        this.forObject.currentRound = res.data.currentRound
        this.forObject.rfxGeneralType = res.data.rfxGeneralType
      })
    },
    getExpertList() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxExpert.getRfxExpert,
        recordsPosition: 'data.supplierListRespIPage.records',
        queryBuilderWrap: 'requestParams',
        afterAsyncData: (res) => {
          if (res.data.rejectReason == null) {
            this.rejectReason = ''
            this.$set(this.pageConfig[0], 'toolbar', [])
          } else {
            this.rejectReason = res.data.rejectReason
            this.$set(this.pageConfig[0], 'toolbar', [
              { id: 'viewRejectReason', title: this.$t('查看驳回原因') }
            ])
          }
        },
        params: {
          rfxCode: this.$route.query.rfxCode,
          bidType: 0
        }
      })
      this.$set(this.pageConfig[1].grid, 'asyncConfig', {
        url: this.$API.rfxExpert.getExpertScore,
        queryBuilderWrap: 'requestParams',
        params: {
          rfxCode: this.$route.query.rfxCode
        }
      })
    },
    submit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行提交操作?`)
        },
        success: () => {
          let params = {
            bidType: 0,
            rfxCode: this.$route.query.rfxCode
          }
          this.$API.rfxExpert.getRfxSubmitExpert(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: res.msg,
                type: 'success'
              })
              this.$router.push({
                name: `expert-rating` // 专家评分列表页面
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.operate-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.operate-bar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4f5b6d;
  font-size: 14px;
  padding: 20px;
  .btns-wrap {
    /deep/ .mt-button {
      margin-right: 0;
      button {
        width: 76px;
        height: 34px;
        background: transparent;
        //border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        box-shadow: unset;
        padding: 0;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
    }
  }
}
.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(180deg);
  }
}
.miam-container {
  background: #fff;
  padding: 0 20px;
  .mian-info {
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    padding: 20px;
    min-width: 1300px;
    .normal-title {
      width: 100%;
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      color: #292929;
      font-family: PingFangSC;
      font-weight: 500;

      &:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        width: 2px;
        height: 10px;
        background: rgba(0, 70, 156, 1);
        border-radius: 1px;
        margin-right: 10px;
      }
    }

    .input-item {
      margin-top: 20px;
      padding-right: 50px;
      .label-txt {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #292929;
      }
      .label-value {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #35404e;
      }
      .select-container {
        height: 40px;
      }
      .e-label {
        color: #35404e;
      }
      .label-text {
        color: #35404e;
      }
    }
    .input-item /deep/ .normal-width {
      width: 240px;
    }
    .input-item /deep/ .e-radio + label .e-label {
      color: #35404e;
    }
  }
}
.relation-ships {
  flex: 1;
  background: rgba(255, 255, 255, 1);
}
</style>
