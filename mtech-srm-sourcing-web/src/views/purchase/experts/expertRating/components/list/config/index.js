import { i18n, permission } from '@/main.js'
const columnDataTechnologyRate = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'biddingCode',
    headerText: i18n.t('投标编号')
  },
  {
    field: 'scoreProcess',
    headerText: i18n.t('技术评分进度')
  },
  {
    field: 'totalScore',
    headerText: i18n.t('技术得分'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(e)) {
          return '--'
        } else {
          return Number(e).toFixed(2)
        }
      }
    }
  },
  {
    field: 'markScore',
    headerText: i18n.t('打分'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('打分')
    }
  }
]
const columnDataBid = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'spec',
    headerText: i18n.t('规格描述')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'requireQuantity',
    headerText: i18n.t('需求数量')
  },
  {
    field: 'bidTaxRateValue',
    headerText: i18n.t('税率')
  },
  {
    field: 'requireDate',
    headerText: i18n.t('需求日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(parseInt(e)) || e == 0) {
          return '--'
        } else {
          let date = new Date(Number(e))
          let year = date.getFullYear()
          let month = date.getMonth() + 1
          let day = date.getDate()
          month = month < 10 ? '0' + month : month
          day = day < 10 ? '0' + day : day
          return year + '-' + month + '-' + day
        }
      }
    }
  },
  {
    field: 'requireEndDate',
    headerText: i18n.t('截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(parseInt(e)) || e == 0) {
          return '--'
        } else {
          let date = new Date(Number(e))
          let year = date.getFullYear()
          let month = date.getMonth() + 1
          let day = date.getDate()
          month = month < 10 ? '0' + month : month
          day = day < 10 ? '0' + day : day
          return year + '-' + month + '-' + day
        }
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'requireDesc',
    headerText: i18n.t('需求描述')
  },
  {
    field: 'requireName',
    headerText: i18n.t('需求名称')
  }
]
export const pageConfig = [
  {
    title: i18n.t('技术评分'),
    useToolTemplate: false,
    toolbar: [],
    gridId: permission.gridId['purchase']['expertRating']['detail']['tech']['score'],
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: columnDataTechnologyRate,
      dataSource: [{ expertLevel: i18n.t('打分') }]
    }
  },
  {
    title: i18n.t('标的'),
    useToolTemplate: false,
    toolbar: [],
    gridId: permission.gridId['purchase']['expertRating']['detail']['tech']['category'],
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: columnDataBid,
      dataSource: []
    }
  }
]
