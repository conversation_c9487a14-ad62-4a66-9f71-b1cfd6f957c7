<template>
  <div class="expert-file">
    <div style="font-weight: bold">{{ $t('专家评分附件') }}</div>
    <mt-template-page
      ref="templatePage"
      :template-config="scoreFileConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleScoreCellTitle"
      @handleClickCellTool="handleScoreCellTool"
    />
    <mt-common-uploader
      ref="uploader"
      class="common-uploader"
      :save-url="saveUrl"
      :is-single-file="false"
      type="line"
      v-model="scoreFileList"
      @confirm="addFile"
    ></mt-common-uploader>
  </div>
</template>
<script>
import { scoreFileConfig } from './config/experts'
import { download } from '@/utils/utils'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      scoreFileConfig: scoreFileConfig(this.$API.rfxExpert.getScoreFileListUrl, this.modalData),
      scoreFileList: [],
      saveUrl: '/api/file/user/file/uploadPublic?useType=1', // 文件上传路径待
      downloadUrl: '/api/file/user/file/downloadPublicFile' //文件下载
    }
  },
  watch: {
    // 兼容处理
    'modalData.supplierCode'(val) {
      if (val) {
        this.scoreFileConfig = scoreFileConfig(
          this.$API.rfxExpert.getScoreFileListUrl,
          this.modalData
        )
      }
    },
    'modalData.supplierId'(val) {
      if (!this.modalData.supplierCode && val && val !== '0') {
        this.scoreFileConfig = scoreFileConfig(
          this.$API.rfxExpert.getScoreFileListUrl,
          this.modalData
        )
      }
    }
  },
  methods: {
    async addFile() {
      const fileList = this.scoreFileList.map((item) => {
        item.sysFileId = item.id
        return item
      })
      fileList.forEach((item) => {
        item.supplierCode = this.modalData.supplierCode
        item.supplierName = this.modalData.supplierName
        item.supplierId = this.modalData.supplierId
        delete item.id
      })
      const params = {
        rfxCode: this.modalData.rfxCode,
        supplierCode: this.modalData.supplierCode,
        supplierId: this.modalData.supplierId,
        sourcingFileSaveRequestList: fileList
      }
      const res = await this.$API.rfxExpert.saveScoreFileList(params)
      if (res.code === 200) {
        this.$refs.templatePage.refreshCurrentGridData()
        this.scoreFileList = []
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.$refs.uploader.$children[1].showFileBaseInfo()
      }
    },
    handleScoreCellTitle(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e?.data?.sysFileId || e?.data?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    handleScoreCellTool(e) {
      if (e.tool.id === 'delete') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除该数据？')
          },
          success: async () => {
            const params = {
              fileId: e.data.id,
              rfxCode: this.modalData.rfxCode,
              supplierCode: this.modalData.supplierCode,
              supplierId: this.modalData.supplierId
            }
            const res = await this.$API.rfxExpert.deleteScoreFileList(params)
            if (res.code === 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templatePage.refreshCurrentGridData()
            }
          }
        })
      } else if (e.tool.id === 'download') {
        this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId }).then((res) => {
          download({
            fileName: e.data.fileName,
            blob: new Blob([res.data])
          })
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.expert-file {
  margin: 16px 0;
  ::v-deep .toolbar-container {
    padding: 0 !important;
  }
  .common-uploader {
    display: none;
  }
}
</style>
