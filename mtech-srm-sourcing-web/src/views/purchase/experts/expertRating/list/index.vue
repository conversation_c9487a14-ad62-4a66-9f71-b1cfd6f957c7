<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  name: 'ExpertRating',
  data() {
    return {
      pageConfig: pageConfig(
        this.$API.rfxExpert.getRfxExpertList,
        this.$API.rfxExpert.getRfxExpertConfirmList,
        this.$route.name
      )
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'ExportRfx') {
        this.handleExport()
      }
    },
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          // {
          //   condition: 'and',
          //   field: 'rfx_item.rfxHeaderId',
          //   operator: 'equal',
          //   value: this.rfxId
          // }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.rfxExpert.exportRfxExpertList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTitle(e) {
      const { data, field } = e
      const isTargetColumn = field === 'rfxCode' || field === 'scoreOperate'
      if (isTargetColumn) {
        localStorage.scheduleTemplateInfo = JSON.stringify(e.data)
      }
      if (data.scoreOperate === 1 || data.scoreOperate === 2) {
        this.$router.push({
          name: `expert-rate-aggregation`, // 评分确认&&评分汇总
          query: {
            rfxCode: e.data.rfxCode,
            scoreGroup: e.data.scoreGroup,
            scoreOperate: data.scoreOperate
          }
        })
      } else if (data.scoreGroup === 0 && isTargetColumn) {
        this.$router.push({
          name: `expert-ratexpert`, // 技术组打分
          query: {
            rfxCode: e.data.rfxCode
          }
        })
      } else if (data.scoreGroup === 1 && isTargetColumn) {
        this.$router.push({
          name: `expert-rate`, // 商务组打分
          query: {
            rfxCode: e.data.rfxCode
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
