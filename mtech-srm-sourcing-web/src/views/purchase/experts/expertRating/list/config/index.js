import { i18n, permission } from '@/main.js'
const columnData = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    width: '100',
    field: 'scoreOperate',
    headerText: i18n.t('操作'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('评分'), 1: i18n.t('评分确认'), 2: i18n.t('查看') }
    }
  },
  {
    width: '200',
    field: 'rfxCode',
    headerText: i18n.t('寻源单号'),
    cssClass: 'field-content'
  },
  {
    width: '100',
    field: 'scoreStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      // map: { 0: i18n.t("评分中"), 1: i18n.t("已提交"), 2: i18n.t("评分汇总") },
      map: {
        0: i18n.t('评分中'),
        1: i18n.t('已提交'),
        2: i18n.t('评分完成'),
        3: i18n.t('待下发'),
        4: i18n.t('已驳回')
      }
    }
  },
  {
    width: '100',
    field: 'scoreGroup',
    headerText: i18n.t('评分模式'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('技术组'), 1: i18n.t('商务组') }
    }
  },
  {
    width: '120',
    field: 'rfxName',
    headerText: i18n.t('标题')
  },
  {
    width: '150',
    field: 'sourcingObj',
    headerText: i18n.t('询价对象')
  },
  {
    width: '120',
    field: 'sourcingMode',
    headerText: i18n.t('寻源类别'),
    valueConverter: {
      type: 'map',
      map: {
        rfq: i18n.t('询报价'),
        invite_bids: i18n.t('招投标'),
        bidding_price: i18n.t('竞价')
      }
    }
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    width: '120',
    field: 'purExecutorName',
    headerText: i18n.t('执行人')
  }
]
const toolbar = [{ id: 'ExportRfx', icon: 'icon_solid_edit', title: i18n.t('导出') }]
export const pageConfig = (getRfxExpertListUrl, getRfxExpertConfirmListUrl, routerName) => [
  {
    useToolTemplate: false,
    toolbar,
    gridId: permission.gridId['purchase']['expertRating']['list'],
    grid: {
      allowFiltering: true,
      allowSorting: false,
      columnData,
      dataSource: [],
      asyncConfig: {
        url: routerName == 'expert-rating' ? getRfxExpertListUrl : getRfxExpertConfirmListUrl
        // defaultRules: [
        //   {
        //     label: "状态",
        //     field: "status",
        //     type: "number",
        //     operator: "equal",
        //     value: "0",
        //   },
        // ],
      }
    }
  }
]
