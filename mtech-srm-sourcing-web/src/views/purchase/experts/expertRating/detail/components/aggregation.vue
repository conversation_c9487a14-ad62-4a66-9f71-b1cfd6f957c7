<template>
  <mt-dialog ref="dialog" css-class="create-proj-dialog" :header="header">
    <div class="dialog-content mt-flex-direction-column">
      <div class="dialog-box">
        <div class="expert_box">
          <p>{{ $t('评分供应商') }}：</p>
          <mt-select
            :width="200"
            float-label-type="Never"
            :data-source="modalData.supplierArr"
            :fields="{
              text: 'supplierName',
              value: 'supplierCode'
            }"
            :value="modalData.supplierCode"
            @change="changeSupplier"
            :show-clear-button="true"
          ></mt-select>
        </div>
        <!-- <div class="op-item mt-flex">
          <i class="mt-icons mt-icon-icon_table_new"></i>
          {{ $t("返回") }}
        </div> -->
      </div>
      <div class="dialog-box">
        <div class="expert_box" style="width: 100%">
          <div style="width: 120px">
            <p>&nbsp;&nbsp;&nbsp;&nbsp;{{ $t('综合评价') }}：</p>
            <p class="lastRemark" v-show="lastRemark != null" @click="quoteLastRemark">
              {{ $t('引用上次评价') }}
            </p>
          </div>
          <div style="width: 100%">
            <mt-input
              :multiline="true"
              :rows="3"
              style="width: 100%"
              maxlength="1000"
              v-model="formObject.remark"
              float-label-type="Never"
              :placeholder="$t('请输入1000字以内')"
            ></mt-input>
          </div>
        </div>
      </div>

      <div>
        <div>{{ $t('评分区') }}</div>
        <div class="box">
          <div class="op-item mt-flex" @click="handleSaveBtn">
            <i class="mt-icons mt-icon-icon_table_new"></i>
            {{ $t('保存') }}&nbsp;&nbsp;&nbsp;
            <span class="op-item mt-flex">
              <i class="mt-icons mt-icon-icon_table_new"></i>
              {{ $t('返回') }}
            </span>
          </div>
          <div class="expert-input">
            <div class="expert_box">
              <p>{{ $t('得分') }}：</p>
              <mt-input
                width="150"
                v-model="formObject.totalScore"
                float-label-type="Never"
                disabled
              ></mt-input>
            </div>
          </div>
        </div>
      </div>
      <div class="grid-wrap">
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      lastRemark: null,
      pageConfig: pageConfig(
        this.$API.rfxExpert.getSaveRfxTexpert,
        this.modalData,
        this.updateTabTitle
      ),
      formObject: {
        totalScore: '',
        remark: ''
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    quoteLastRemark() {
      this.formObject.remark = this.lastRemark
    },
    changeSupplier(obj) {
      this.modalData.supplierCode = obj.itemData.supplierCode
      this.pageConfig = pageConfig(
        this.$API.rfxExpert.getSaveRfxTexpert,
        this.modalData,
        this.updateTabTitle
      )
    },
    updateTabTitle(res) {
      this.formObject.totalScore = res.data.totalScore
      this.formObject.remark = res.data.remark
      this.lastRemark = res.data.lastRemark
    },
    handleSaveBtn() {
      this.endEdit()
      setTimeout(() => {
        this.handGridSave()
      }, 100)
    },
    endEdit() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      _currentTabRef?.grid?.endEdit()
    },
    handGridSave() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
      if (_selectRecords.length) {
        for (let item of _selectRecords) {
          if (item.score < item.minScore || item.score > item.maxScore) {
            this.$toast({
              content: this.$t('得分分值不应低于最低分或高于最高分'),
              type: 'warning'
            })
            return
          }
        }
        let bidScoreSupplierDetailDTOS = []
        _selectRecords.forEach((e) => {
          bidScoreSupplierDetailDTOS.push({
            scoreDetailCode: e.scoreDetailCode,
            scoreDetailName: e.scoreDetailName,
            scoreItem: e.scoreItem,
            maxScore: e.maxScore,
            minScore: e.minScore,
            detailWeight: e.detailWeight,
            remark: e.remark,
            score: e.score
          })
        })

        let params = {
          rfxCode: this.modalData.rfxCode,
          bidType: 1,
          remark: this.formObject.remark,
          supplierCode: this.modalData.supplierCode,
          bidScoreSupplierDetailDTOS: bidScoreSupplierDetailDTOS
        }
        this.$API.rfxExpert.getRfxAddSave(params).then((res) => {
          if (res.code == 200) {
            this.$emit('confirm-function')
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.lastRemark {
  font-size: 14px;
  color: #00469c;
  cursor: pointer;
}
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expert-input {
  display: flex;
  align-items: center;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.dialog-content {
  // padding-top: 18px;
  font-size: 16px;
  height: 100%;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }

  .grid-wrap {
    flex: 1;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}
</style>
