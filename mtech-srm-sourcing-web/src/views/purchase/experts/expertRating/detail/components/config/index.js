import { i18n } from '@/main.js'
import Vue from 'vue'
const columnData = [
  {
    field: 'lineIndexComponent',
    headerText: i18n.t('序号'),
    allowFiltering: false, // 序号列，不参与数据筛选
    allowResizing: false, // 序号列，不参与列顺序变化
    allowSorting: false, // 序号列，不参与列宽变化
    allowEditing: false, // 序号列，不参与数据编辑
    ignore: true, // 序号列，不参与数据筛选
    width: 80,
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: '<div>{{+data.index+1}}</div>',
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'scoreDetailCode',
    headerText: i18n.t('评分项编码'),
    allowEditing: false
  },
  {
    field: 'scoreDetailName',
    headerText: i18n.t('评分项名称'),
    allowEditing: false
  },
  {
    field: 'scoreItem',
    headerText: i18n.t('评分细则'),
    allowEditing: false
  },
  {
    field: 'minScore',
    headerText: i18n.t('最低分'),
    allowEditing: false
  },
  {
    field: 'maxScore',
    headerText: i18n.t('最高分'),
    allowEditing: false
  },
  {
    field: 'lastScore',
    headerText: i18n.t('上次得分'),
    allowEditing: false
  },
  {
    field: 'score',
    headerText: i18n.t('得分'),
    editType: 'numericedit'
  },
  {
    field: 'remark',
    headerText: i18n.t('打分说明')
  },
  {
    field: 'detailWeight',
    headerText: i18n.t('权重'),
    allowEditing: false
  }
]
export const pageConfig = (url, modalData, afterAsyncDataFunc) => [
  {
    // useToolTemplate: false,
    // toolbar: [],
    grid: {
      allowFiltering: true,
      // lineIndex: true,
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Bottom'
      },
      asyncConfig: {
        url: url,
        recordsPosition: 'data.bidScoreSupplierDetailDTOIPage.records',
        serializeList: (list) => {
          list.forEach((item) => {
            if (item.score == null) {
              item.score = item.defaultScore
            }
          })
          return list
        },
        afterAsyncData: afterAsyncDataFunc,
        page: {
          current: 1,
          size: 1000
        },
        queryBuilderWrap: 'requestParams',
        params: {
          rfxCode: modalData.rfxCode,
          bidType: 1,
          supplierCode: modalData.supplierCode
        }
      },
      allowPaging: false,
      columnData: columnData,
      dataSource: []
    }
  }
]
