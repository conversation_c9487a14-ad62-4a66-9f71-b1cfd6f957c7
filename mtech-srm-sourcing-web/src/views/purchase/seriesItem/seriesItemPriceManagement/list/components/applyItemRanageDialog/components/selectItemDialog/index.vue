<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
export default {
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId:
            this.$permission.gridId['purchase']['seriesItem']['seriesItemPriceManagement'][
              'applyItemRanageDialog'
            ]['selectItemDialog']['list'],
          grid: {
            allowFiltering: true,
            ignoreFields: ['isPriceRecord'],
            columnData: columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            asyncConfig: {
              url: this.$API.masterData.getItemListPageBySite,
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.categoryResponse == null) {
                    item.categoryCode = ''
                    item.categoryName = ''
                    item.categoryId = ''
                  } else {
                    item.categoryCode = item.categoryResponse.categoryCode
                    item.categoryName = item.categoryResponse.categoryName
                    item.categoryId = item.categoryResponse.id
                  }
                })
                return list
              }
              // params: {
              //   orgId: this.modalData.siteParam.organizationId,
              //   // siteCode: this.modalData.siteParam.siteCode,
              // },
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
