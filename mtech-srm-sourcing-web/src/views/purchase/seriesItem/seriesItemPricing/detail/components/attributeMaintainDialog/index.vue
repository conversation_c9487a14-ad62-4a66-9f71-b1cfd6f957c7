<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { createEditInstance } from '@/utils/ej/dataGrid'
export default {
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          gridId:
            this.$permission.gridId['purchase']['seriesItem']['seriesItemPriceManagement']['list'],
          grid: {
            lineIndex: true,
            columnData: [],
            dataSource: [],
            editSettings: {
              allowAdding: true,
              allowEditing: true,
              allowDeleting: true,
              mode: 'Normal',
              allowEditOnDblClick: true,
              showConfirmDialog: false,
              showDeleteConfirmDialog: true,
              newRowPosition: 'Top'
            }
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.setListColumnData()
    this.$API.seriesItem
      .itemApplyAttrQuery({
        itemCode: this.modalData.itemCode,
        itemId: this.modalData.itemId,
        seriesItemId: this.modalData.seriesItemId,
        applyId: this.modalData.applyId
      })
      .then((res) => {
        this.$set(this.pageConfig[0].grid, 'dataSource', res.data.seriesItemAttrDTOList)
      })
  },
  methods: {
    setListColumnData() {
      const listColumnData = () => {
        const editInstance = createEditInstance().onChange((ctx, { field, rowData }, value) => {
          const grid = this.$refs.templateRef.getCurrentTabRef().grid //获取表格实例
          let dataSource = grid.$options.propsData.dataSource
          dataSource[
            dataSource.findIndex((e) => {
              return e.attrCode == rowData.attrCode
            })
          ] = rowData
          console.log(ctx, { field, rowData }, value)
        })
        return [
          {
            field: 'attrName',
            headerText: this.$t('属性名称'),
            allowEditing: false
          },
          {
            field: 'attrValue',
            headerText: this.$t('属性值'),
            edit: editInstance.create({
              getEditConfig: () => ({
                type: 'number'
              })
            })
          }
        ]
      }
      this.$set(this.pageConfig[0].grid, 'columnData', listColumnData())
    },
    confirm() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      //获取当前行的数据
      const grid = this.$refs.templateRef.getCurrentTabRef().grid //获取表格实例
      let data = grid.$options.propsData.dataSource
      let strArr = []
      for (let index in data) {
        if (data[index].attrValue) {
          let arr = String(data[index].attrValue).split('.')
          if ((arr[0] + '').length > 10) {
            strArr.push(Number(index) + 1)
          } else if (arr[1] && (arr[1] + '').length > 2) {
            strArr.push(Number(index) + 1)
          }
        }
      }
      if (strArr.length > 0) {
        this.$toast({
          content:
            '第' + strArr.join(',') + '行的属性值超过限制，整数位最多10位数字，小数位最多2位数字',
          type: 'warning'
        })
        return
      }
      this.$API.seriesItem
        .itemApplyAttrSave({
          applyId: this.modalData.applyId,
          seriesItemId: this.modalData.seriesItemId,
          seriesItemCode: this.modalData.seriesItemCode,
          itemCode: this.modalData.itemCode,
          itemId: this.modalData.itemId,
          itemName: this.modalData.itemName,
          seriesItemAttrDTOList: data
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            this.$emit('confirm-function')
          }
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
