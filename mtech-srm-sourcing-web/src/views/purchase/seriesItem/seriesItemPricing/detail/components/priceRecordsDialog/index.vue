<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog custom-price-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(
        this.$API.priceService.getPriceRecordsNewSeires,
        this.recordDoubleClickFun,
        this.modalData
      ),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    recordDoubleClickFun(e) {
      this.$emit('confirm-function', [e.rowData])
    },
    confirm() {
      //获取当前行的数据
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style>
.custom-price-dialog.e-dialog .e-dlg-content {
  padding: 0 18px !important;
}
</style>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
