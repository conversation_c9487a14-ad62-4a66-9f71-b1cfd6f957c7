<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    width="1450"
    height="800"
  >
    <div class="dialog-content">
      <mt-form ref="editForm" :model="formData" :rules="formRules">
        <mt-row :gutter="20">
          <mt-col
            v-for="(item, index) in formItems"
            :span="item.type === 'line' ? 24 : 6"
            :key="index"
          >
            <!-- 分割线 -->
            <hr v-if="item.type === 'line'" class="part-line" />
            <mt-form-item v-else :prop="item.fieldCode" :label="item.fieldName">
              <!-- 文本输入框 -->
              <mt-input
                v-if="item.type === 'text'"
                type="text"
                v-model="formData[item.fieldCode]"
                :show-clear-button="true"
                :disabled="item.disabled"
                :placeholder="$t('请输入') + item.fieldName"
              />
              <!-- 数值输入框 -->
              <mt-input-number
                v-else-if="item.type === 'number'"
                v-model="formData[item.fieldCode]"
                :precision="item.precision || null"
                :min="item.min || null"
                :max="item.max || null"
                :show-clear-button="true"
                float-label-type="Never"
                :placeholder="$t('请输入') + item.fieldName"
                @change="(e) => handleValueChange(item, e)"
              />
              <!-- 下拉选择框 -->
              <mt-select
                v-else-if="item.type === 'select'"
                :value="formData[item.fieldCode]"
                :data-source="item.dataSource || modalData.dropdownData[item.dataSourceName]"
                :fields="item.dropdownFields || { text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择') + item.fieldName"
                @change="(e) => handleValueChange(item, e)"
              />
              <!-- 日期选择框 -->
              <mt-date-picker
                v-else-if="item.type === 'date'"
                v-model="formData[item.fieldCode]"
                :open-on-focus="true"
                :show-clear-button="true"
                :allow-edit="false"
                :disabled="item.disabled"
                :format="item.format"
                :value-format="item.format"
                :min="item.min ? formData[item.min] : null"
                :max="item.max ? formData[item.max] : null"
                :placeholder="$t('请选择') + item.fieldName"
                @change="(e) => handleValueChange(item, e)"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { formItems, formRules } from './config'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      formItems: [],
      formRules: {},
      formData: {},
      disabledEndDate: false,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (this.modalData.companyCode === '1503') {
        this.disabledEndDate = true
      }
      this.formData = { ...this.modalData.rowData }
      this.formItems = formItems(this)
      this.formRules = formRules(this.modalData.rowData.needAttrs || [])
      this.$refs['dialog'].ejsRef.show()
    },
    // 修改值
    handleValueChange(item, e) {
      const { fieldCode, type, dropdownFields } = item
      let _calcArr = [
        'length',
        'width',
        'height',
        'makeup',
        'mouldCost',
        'otherFee',
        'pages',
        'projectCost',
        'firstEditionNumber',
        'weight'
      ]
      // 下拉选择，需要赋值操作，否则值不会被修改
      if (type === 'select') {
        this.formData[fieldCode] = e.itemData ? e.itemData[dropdownFields?.value || 'value'] : null
        this.$refs.editForm.validateField(fieldCode)
      }

      if (fieldCode === 'currencyCode') {
        this.$set(this.formData, 'currencyName', e.itemData?.currencyName || null)
      } else if (fieldCode === 'taxCode') {
        this.$set(this.formData, 'taxRate', e.itemData?.taxRate || null)
        this.$set(this.formData, 'taxName', e.itemData?.taxItemName || null)

        // 重新计算单价（含税）
        const { untaxedUnitPrice, taxRate } = this.formData
        let taxedUnitPrice = (Number(untaxedUnitPrice) * (1 + Number(taxRate))).toFixed(5)
        this.$set(this.formData, 'taxedUnitPrice', taxedUnitPrice || untaxedUnitPrice)
      } else if (fieldCode === 'purUnitCode') {
        this.$set(this.formData, 'purUnitName', e.itemData?.unitName || null)
      } else if (fieldCode === 'untaxedUnitPrice') {
        // 计算单价（含税）
        const { untaxedUnitPrice, taxRate } = this.formData
        let taxedUnitPrice = (Number(untaxedUnitPrice) * (1 + Number(taxRate))).toFixed(5)
        this.$set(this.formData, 'taxedUnitPrice', taxedUnitPrice || untaxedUnitPrice)
      } else if (fieldCode === 'validStartTime' && this.modalData.companyCode === '1503') {
        let date = new Date()
        let year = date.getFullYear()
        let yearTwo = date.getFullYear() + 1
        let datae = year + '-' + '6' + '-' + '30'
        let dateTwo = year + '-' + '7' + '-' + '1'
        if (new Date(e).getTime() >= new Date(dateTwo).getTime()) {
          this.$set(this.formData, 'validEndTime', yearTwo + '-' + '12' + '-' + '31 ')
        } else if (new Date(e).getTime() <= new Date(datae).getTime()) {
          this.$set(this.formData, 'validEndTime', year + '-' + '12' + '-' + '31 ')
        }
      } else if (_calcArr.includes(fieldCode)) {
        let param = {
          firstEditionNumber: this.formData.firstEditionNumber,
          height: this.formData.height,
          length: this.formData.length,
          makeup: this.formData.makeup,
          mouldCost: this.formData.mouldCost,
          otherFee: this.formData.otherFee,
          pages: this.formData.pages,
          projectCost: this.formData.projectCost,
          seriesItemId: this.formData.id,
          weight: this.formData.weight,
          width: this.formData.width
        }
        this.$API.seriesItem.calcPrice(param).then((res) => {
          let price = ''
          price = res.data
          if (price) {
            this.$set(this.formData, 'untaxedUnitPrice', Number(price))
            let taxedUnitPrice =
              Number(this.formData.untaxedUnitPrice) +
              Number(this.formData.untaxedUnitPrice) * Number(this.formData.taxRate)
            this.$set(this.formData, 'taxedUnitPrice', Number(taxedUnitPrice).toFixed(5))
          }
        })
      }
    },
    confirm() {
      this.$refs.editForm.validate((val) => {
        if (val) {
          this.$API.seriesItem
            .itemApplyPriceUpdate({
              batchSaveFlag: 0,
              applyId: this.modalData.id,
              seriesPriceItems: [this.formData]
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: res.msg || this.$t('保存成功'),
                  type: 'success'
                })
                this.$emit('confirm-function')
              } else {
                this.$toast({
                  content: res.msg || this.$t('保存失败，请重试'),
                  type: 'warning'
                })
              }
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.part-line {
  border: 1px dashed #31374e;
  margin: 15px 0 25px 0;
}
/deep/ .mt-form {
  margin: 20px 10px 0 10px;
}
/deep/ .mt-form-item {
  display: flex;
  margin-bottom: 15px;
}
</style>
