<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-local-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="left">
                <mt-input v-model="searchFormModel.itemCode"></mt-input>
              </mt-form-item>
              <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="left">
                <mt-input v-model="searchFormModel.categoryCode"></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
import MtLocalTemplatePage from '@/components/template-page'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    MtLocalTemplatePage
  },
  data() {
    return {
      data: {},
      searchFormModel: {
        itemCode: '',
        categoryCode: ''
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId:
            this.$permission.gridId['purchase']['seriesItem']['seriesItemPriceManagement'][
              'applyItemRanageDialog'
            ]['selectItemDialog']['list'],
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            allowFiltering: true,
            ignoreFields: ['isPriceRecord'],
            columnData: columnData,
            allowSorting: true,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            dataSource: [],
            asyncConfig: {
              url: this.$API.seriesItem.itemApplyPriceQueryUrl,
              params: {
                applyId: this.modalData.applyId,
                itemId: this.modalData.itemId
              }
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    // this.getData()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    getData() {
      this.$API.seriesItem
        .itemApplyPriceQuery({
          applyId: this.modalData.applyId,
          itemId: this.modalData.itemId
        })
        .then((res) => {
          let data = res.data
          this.$set(this.pageConfig[0].grid, 'dataSource', data)
        })
    },
    confirm() {
      let _records = []
      _records = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()

      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (this.modalData.sourcingType == 'new_products') {
        if (this.modalData.priceClassification == 3) {
          if (
            _records[0].isPriceRecord == 1 &&
            (_records[0].predictPrice != 0 || _records[0].executePrice != 0)
          ) {
            this.$toast({
              content: this.$t('请选择没有价格记录且执行价类型和暂估价类型为否的物料'),
              type: 'warning'
            })
            return
          }
        } else if (this.modalData.priceClassification == 4) {
          // 执行价判断执行类型
          if (_records[0].isPriceRecord == 1 && _records[0].executePrice != 0) {
            this.$toast({
              content: this.$t('请选择没有价格记录且执行价类型为否的物料'),
              type: 'warning'
            })
            return
          }
        }
      } else if (
        this.modalData.sourcingType == 'second_inquiry' ||
        this.modalData.sourcingType == 'exist'
      ) {
        if (_records[0].isPriceRecord == 0) {
          this.$toast({
            content: this.$t('请选择有价格记录的物料'),
            type: 'warning'
          })
          return
        }
      }
      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
