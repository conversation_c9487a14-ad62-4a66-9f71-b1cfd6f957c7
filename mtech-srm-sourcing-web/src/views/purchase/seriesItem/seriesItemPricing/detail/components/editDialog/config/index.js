import { i18n } from '@/main.js'
import { MAX_SAFE_INTEGER } from '@/constants/editConfig'
import { makeTextFields } from '@/views/common/columnData/utils'

export const formItems = (that) => {
  return [
    {
      type: 'text',
      fieldCode: 'siteCode',
      fieldName: i18n.t('工厂编码'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'siteName',
      fieldName: i18n.t('工厂名称'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'seriesItemCode',
      fieldName: i18n.t('系列物料编码'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'seriesItemName',
      fieldName: i18n.t('系列物料名称'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'itemCode',
      fieldName: i18n.t('物料编码'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'itemName',
      fieldName: i18n.t('物料名称'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'spec',
      fieldName: i18n.t('物料规格'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'texture',
      fieldName: i18n.t('物料材质'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'categoryCode',
      fieldName: i18n.t('品类编码'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'categoryName',
      fieldName: i18n.t('品类名称'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'unitName',
      fieldName: i18n.t('基本单位'),
      disabled: true
    },
    { type: 'line' },
    {
      type: 'text',
      fieldCode: 'supplierCode',
      fieldName: i18n.t('供应商编码'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'supplierName',
      fieldName: i18n.t('供应商名称'),
      disabled: true
    },
    {
      type: 'select',
      fieldCode: 'deliveryPlace',
      fieldName: i18n.t('直送地'),
      disabled: false,
      dataSourceName: 'deliveryPlaceList',
      dropdownFields: { text: 'itemName', value: 'itemName' }
    },
    {
      type: 'text',
      fieldCode: 'priceUnit',
      fieldName: i18n.t('价格单位'),
      disabled: true
    },
    {
      type: 'text',
      fieldCode: 'formulaContent',
      fieldName: i18n.t('计算公式'),
      disabled: true
    },
    {
      type: 'number',
      fieldCode: 'length',
      fieldName: i18n.t('长(mm)'),
      disabled: false,
      min: 0
    },
    {
      type: 'number',
      fieldCode: 'width',
      fieldName: i18n.t('宽(mm)'),
      disabled: false,
      min: 0
    },
    {
      type: 'number',
      fieldCode: 'height',
      fieldName: i18n.t('高(mm)'),
      disabled: false,
      min: 0
    },
    {
      type: 'number',
      fieldCode: 'makeup',
      fieldName: i18n.t('拼板数量'),
      disabled: false,
      min: 0,
      precision: 0
    },
    {
      type: 'number',
      fieldCode: 'pages',
      fieldName: i18n.t('页数'),
      disabled: false,
      min: 0,
      precision: 0
    },
    {
      type: 'number',
      fieldCode: 'mouldCost',
      fieldName: i18n.t('模具费'),
      disabled: false,
      min: 0
    },
    {
      type: 'number',
      fieldCode: 'firstEditionNumber',
      fieldName: i18n.t('首次打板数量'),
      disabled: false,
      min: 0,
      precision: 0
    },
    {
      type: 'number',
      fieldCode: 'projectCost',
      fieldName: i18n.t('工程费'),
      disabled: false
    },
    {
      type: 'number',
      fieldCode: 'weight',
      fieldName: i18n.t('重量(g)'),
      disabled: false,
      min: 0
    },
    {
      type: 'number',
      fieldCode: 'otherFee',
      fieldName: i18n.t('其他费用'),
      disabled: false,
      min: 0
    },
    {
      type: 'number',
      fieldCode: 'untaxedUnitPrice',
      fieldName: i18n.t('单价（未税）'),
      disabled: false,
      min: 0
    },
    {
      type: 'text',
      fieldCode: 'taxedUnitPrice',
      fieldName: i18n.t('单价（含税）'),
      disabled: true
    },
    {
      type: 'number',
      fieldCode: 'bidHistoryPrice',
      fieldName: i18n.t('历史价格'),
      disabled: true,
      min: 0
    },
    {
      type: 'select',
      fieldCode: 'purUnitCode',
      fieldName: i18n.t('订单单位编码'),
      disabled: false,
      dataSourceName: 'purUnitList',
      dropdownFields: { text: '__text', value: 'unitCode' }
    },
    {
      type: 'number',
      fieldCode: 'bidConversionRate',
      fieldName: i18n.t('转换率'),
      disabled: false,
      min: 0,
      max: MAX_SAFE_INTEGER,
      precision: 2
    },
    { type: 'line' },
    {
      type: 'date',
      fieldCode: 'validStartTime',
      fieldName: i18n.t('生效日期'),
      format: 'yyyy-MM-dd',
      disabled: false,
      max: that.formData.validEndTime
    },
    {
      type: 'date',
      fieldCode: 'validEndTime',
      fieldName: i18n.t('失效日期'),
      format: 'yyyy-MM-dd',
      disabled: that.disabledEndDate,
      min: that.formData.validStartTime
    },
    {
      type: 'select',
      fieldCode: 'quoteMode',
      fieldName: i18n.t('报价方式'),
      disabled: false,
      dataSource: [
        {
          text: i18n.t('按照入库'),
          value: 'in_warehouse'
        },
        {
          text: i18n.t('按出库'),
          value: 'out_warehouse'
        },
        {
          text: i18n.t('按订单日期'),
          value: 'order_date'
        }
      ]
    },
    {
      type: 'select',
      fieldCode: 'quoteAttribute',
      fieldName: i18n.t('报价属性'),
      disabled: false,
      dataSource: [
        {
          text: i18n.t('标准价'),
          value: 'standard_price'
        },
        {
          text: i18n.t('寄售价'),
          value: 'mailing_price'
        }
      ]
    },
    {
      type: 'select',
      fieldCode: 'currencyCode',
      fieldName: i18n.t('币种编码'),
      disabled: false,
      dataSourceName: 'currencyList',
      dropdownFields: { text: '__text', value: 'currencyCode' }
    },
    {
      type: 'text',
      fieldCode: 'currencyName',
      fieldName: i18n.t('币种名称'),
      disabled: true
    },
    {
      type: 'select',
      fieldCode: 'taxCode',
      fieldName: i18n.t('税率编码'),
      disabled: false,
      dataSourceName: 'taxList',
      dropdownFields: makeTextFields('taxItemCode')
    },
    {
      type: 'text',
      fieldCode: 'taxName',
      fieldName: i18n.t('税率名称'),
      disabled: true
    },
    {
      type: 'number',
      fieldCode: 'minPackageQuantity',
      fieldName: i18n.t('最小包装量'),
      disabled: false
    },
    {
      type: 'number',
      fieldCode: 'minPurchaseQuantity',
      fieldName: i18n.t('最小采购量'),
      disabled: false,
      min: 1,
      max: MAX_SAFE_INTEGER,
      precision: 2
    },
    {
      type: 'number',
      fieldCode: 'leadTime',
      fieldName: i18n.t('L/T'),
      disabled: false,
      min: 1,
      max: MAX_SAFE_INTEGER,
      precision: 2
    },
    {
      type: 'number',
      fieldCode: 'unconditionalLeadTime',
      fieldName: i18n.t('无条件L/T'),
      disabled: false,
      min: 1,
      max: MAX_SAFE_INTEGER,
      precision: 2
    },
    {
      type: 'text',
      fieldCode: 'remark',
      fieldName: i18n.t('备注'),
      disabled: false
    }
    // {
    //   type: 'text',
    //   fieldCode: 'taxRate',
    //   fieldName: i18n.t('税率值'),
    //   disabled: true
    // },
    // {
    //   type: 'text',
    //   fieldCode: 'purUnitName',
    //   fieldName: i18n.t('订单单位名称'),
    //   disabled: true
    // },
  ]
}

export const formRules = (needAttrs) => {
  return {
    length: [
      { required: needAttrs.includes('length'), message: i18n.t('请填写长(mm)'), trigger: 'blur' }
    ],
    width: [
      { required: needAttrs.includes('width'), message: i18n.t('请填写宽(mm)'), trigger: 'blur' }
    ],
    height: [
      { required: needAttrs.includes('height'), message: i18n.t('请填写高(mm)'), trigger: 'blur' }
    ],
    weight: [
      { required: needAttrs.includes('weight'), message: i18n.t('请填写重量(g)'), trigger: 'blur' }
    ],
    projectCost: [
      {
        required: needAttrs.includes('projectCost'),
        message: i18n.t('请填写工程费'),
        trigger: 'blur'
      }
    ],
    pages: [
      { required: needAttrs.includes('pages'), message: i18n.t('请填写页数'), trigger: 'blur' }
    ],
    mouldCost: [
      {
        required: needAttrs.includes('mouldCost'),
        message: i18n.t('请填写模具费'),
        trigger: 'blur'
      }
    ],
    otherFee: [
      {
        required: needAttrs.includes('otherFee'),
        message: i18n.t('请填写其他费用'),
        trigger: 'blur'
      }
    ],
    makeup: [
      { required: needAttrs.includes('makeup'), message: i18n.t('请填写拼板数量'), trigger: 'blur' }
    ],
    firstEditionNumber: [
      {
        required: needAttrs.includes('firstEditionNumber'),
        message: i18n.t('请填写首次打板数量'),
        trigger: 'blur'
      }
    ],

    minPurchaseQuantity: [{ required: true, message: i18n.t('请填写最小采购量'), trigger: 'blur' }],
    minPackageQuantity: [{ required: true, message: i18n.t('请填写最小包装量'), trigger: 'blur' }],
    leadTime: [{ required: true, message: i18n.t('请填写L/T'), trigger: 'blur' }],
    unconditionalLeadTime: [
      { required: true, message: i18n.t('请填写无条件L/T'), trigger: 'blur' }
    ],
    deliveryPlace: [{ required: true, message: i18n.t('请选择直送地'), trigger: 'blur' }],
    validStartTime: [{ required: true, message: i18n.t('请选择生效日期'), trigger: 'blur' }],
    validEndTime: [{ required: true, message: i18n.t('请选择失效日期'), trigger: 'blur' }]
  }
}
