import { i18n, permission } from '@/main.js'

const listColumnData = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'itemDescription',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('禁用') }
    }
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: {
        mailing_price: i18n.t('寄售价'),
        standard_price: i18n.t('标准价')
      }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: {
        in_warehouse: i18n.t('按照入库'),
        out_warehouse: i18n.t('按出库'),
        order_date: i18n.t('按订单日期')
      }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    format: 'yyyy-MM-dd',
    type: 'date',
    searchOptions: {
      operator: 'greaterthanorequal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    format: 'yyyy-MM-dd',
    type: 'date',
    searchOptions: {
      operator: 'lessthanorequal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'leadTime',
    headerText: i18n.t('L/T')
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'paymentCondition',
    headerText: i18n.t('付款条件')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url, recordDoubleClickFun, modalData) => [
  {
    useToolTemplate: false,
    toolbar: [],
    gridId: permission.gridId['purchase']['seriesItem']['seriesItemPriceManagement']['list'],
    grid: {
      lineIndex: true,
      allowFiltering: true,
      columnData: listColumnData,
      asyncConfig: {
        url: url,
        defaultRules: [
          {
            label: '',
            field: 'priceType',
            type: 'string',
            operator: 'equal',
            value: 7
          },
          {
            label: '',
            field: 'siteCode',
            type: 'string',
            operator: 'equal',
            value: modalData.siteCode
          },
          {
            label: '',
            field: 'companyCode',
            type: 'string',
            operator: 'equal',
            value: modalData.companyCode
          }
        ],
        serializeList: (list) => {
          list.forEach((item) => {
            if (item.siteList) {
              item.companyName = item.siteList[0].companyName
              item.siteName = item.siteList[0].siteName
              item.purOrgName = item.siteList[0].purOrgName
            }
          })
          return list
        }
      },
      allowSorting: true,
      allowSelection: true,
      selectionSettings: {
        type: 'Multiple',
        mode: 'Row'
      },
      recordDoubleClick: recordDoubleClickFun
    }
  }
]
