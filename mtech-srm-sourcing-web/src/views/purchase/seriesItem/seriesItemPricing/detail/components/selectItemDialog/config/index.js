import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'spec',
    headerText: i18n.t('物料规格'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'deliveryPlace',
    headerText: i18n.t('直送地'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'formulaContent',
    headerText: i18n.t('计算公式'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'texture',
    headerText: i18n.t('物料材质'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'seriesItemCode',
    headerText: i18n.t('系列材质编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'seriesItemName',
    headerText: i18n.t('系列材质名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'isPriceRecord',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有价格记录'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'basicPrice',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有基价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'srmPrice',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有SRM价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'predictPrice',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有暂估价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'executePrice',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有执行价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]
