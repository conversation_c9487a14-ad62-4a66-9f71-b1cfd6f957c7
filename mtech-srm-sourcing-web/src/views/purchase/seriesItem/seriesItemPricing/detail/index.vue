<template>
  <div class="full-height mt-flex-direction-column">
    <div class="top-info">
      <div class="lf-wrap">
        <div class="detail-info">
          <div class="name-wrap">
            <div class="first-line">
              <span class="code">{{ this.formObject.pointNo || '-' }}</span>
            </div>
          </div>
          <div class="btns-wrap">
            <mt-button flat @click="comeBack()">{{ $t('返回') }}</mt-button>
            <mt-button v-if="!isDisabled" @click="save()">{{ $t('保存') }}</mt-button>
            <mt-button v-if="!isDisabled" flat @click="submit()">{{ $t('提交') }}</mt-button>
          </div>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div style="padding: 20px; padding-bottom: 10px" v-show="showHeaderForm">
        <mt-form
          :model="formObject"
          class="form-generator-form"
          style="justify-content: start"
          :rules="formRules"
        >
          <mt-form-item
            :label="$t('定价单号')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-input disabled float-label-type="Never" v-model="formObject.pointNo"></mt-input>
          </mt-form-item>
          <mt-form-item
            :label="$t('标题')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-input
              :disabled="isDisabled"
              float-label-type="Never"
              v-model="formObject.title"
              :placeholder="$t('请输入标题')"
              maxlength="50"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            :label="$t('定价对象')"
            prop="priceObjectName"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-input
              disabled
              float-label-type="Never"
              v-model="formObject.priceObjectName"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            :label="$t('公司')"
            prop="companyCode"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              :disabled="!enableChangePointType"
              :data-source="companyNameSelectOptions"
              v-model="formObject.companyCode"
              :placeholder="$t('请选择公司')"
              float-label-type="Never"
              @select="companyNameChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            :label="$t('采购组织')"
            prop="purOrgCode"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              :disabled="!enableChangePointType"
              :data-source="purOrgNameSelectOptions"
              v-model="formObject.purOrgCode"
              :placeholder="$t('请选择采购组织')"
              float-label-type="Never"
              @select="purOrgNameChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            :label="$t('工厂')"
            prop="siteName"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              :disabled="!enableChangePointType"
              :data-source="factorySelectOptions"
              v-model="formObject.siteName"
              :placeholder="$t('请选择工厂')"
              float-label-type="Never"
              @change="siteIdChange"
            ></mt-select>
            <!-- @select="factoryNameChange" -->
          </mt-form-item>
          <mt-form-item
            :label="$t('采购人员')"
            prop="purName"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-input
              :disabled="isDisabled"
              v-model="formObject.purName"
              :placeholder="$t('请选择采购人员')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            :label="$t('定价单类型')"
            prop="decidePriceType"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              disabled
              :data-source="[{ text: $t('系列物料定价'), value: 2 }]"
              v-model="formObject.decidePriceType"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            :label="$t('状态')"
            prop="status"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              disabled
              :data-source="[
                { text: $t('草稿'), value: 0 },
                { text: $t('已保存'), value: 1 },
                { text: $t('已提交'), value: 2 },
                { text: $t('审批通过'), value: 3 },
                { text: $t('审批驳回'), value: 4 }
              ]"
              v-model="formObject.status"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            :label="$t('价格分类')"
            prop="priceClassification"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              :disabled="isDisabled"
              :data-source="[
                { text: $t('基价'), value: 1 },
                { text: $t('SRM价格'), value: 2 },
                { text: $t('暂估价格'), value: 3 },
                { text: $t('执行价格'), value: 4 }
              ]"
              v-model="formObject.priceClassification"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            :label="$t('定价类型')"
            prop="pointType"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              :disabled="!enableChangePointType"
              :data-source="[
                { text: $t('新品'), value: 'new_products' },
                { text: $t('二次'), value: 'second_inquiry' },
                { text: $t('已有'), value: 'exist' }
              ]"
              v-model="formObject.pointType"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            :label="$t('拓展')"
            prop="expand"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-multi-select
              :key="sourcingExpandKey"
              :disabled="isDisabled"
              :data-source="dataSource.sourcingExpandList"
              v-model="expand"
              :title="sourcingExpandTitle"
              :item-template="selectItemTemplate"
              :show-clear-button="false"
              :placeholder="$t('拓展')"
              :allow-filtering="true"
              filter-type="Contains"
              @change="handleExpand"
            />
          </mt-form-item>
          <mt-form-item
            :label="$t('备注')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-input
              :disabled="isDisabled"
              :placeholder="$t('字数不超过500字')"
              v-model="formObject.remark"
              maxlength="500"
              :multiline="true"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="top-shrink" @click="showHeaderForm = !showHeaderForm">
        <mt-icon v-show="!showHeaderForm" name="a-iconxl"></mt-icon>
        <mt-icon v-show="showHeaderForm" name="a-iconsq"></mt-icon>
      </div>
    </div>

    <div class="relation-ships">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickToolBar="handleClickToolBar"
        @actionComplete="actionComplete"
      >
        <template slot="slot-3"> <attachment-tab :detail-info="formObject" /></template>
      </mt-template-page>
    </div>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import {
  columnDataSeriesItem,
  pageConfig,
  columnDataPriceItem,
  columnDataQuota,
  toolbar,
  toolbarSeriesItem,
  toolbarTwo
} from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import { sliceArray } from '@/utils/arr'
import ItemTemplate from '../list/components/addDialog/ItemTemplate.vue'
import AttachmentTab from './attachment.vue'
export default {
  name: 'SeriesItemPricingDetail',
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    AttachmentTab
  },
  data() {
    return {
      selectItemTemplate: () => {
        return {
          template: ItemTemplate
        }
      },
      isExecuteMounted: false, //是否执行过mounted
      expand: '',
      showHeaderForm: false,
      seriesItemId: null,
      sourcingExpandTitle: '',
      sourcingExpandKey: new Date().getTime(),
      pageConfig: pageConfig(
        this.$API.seriesItem.itemApplyDetailPrice,
        this.$route.query.id,
        this,
        this.serializeList,
        this.rowDataBound
      ),
      formObject: {
        sourcingExpand: '',
        expandSaveRequestList: []
      },
      formRules: {
        //定价单类型
        decidePriceType: {
          required: true,
          message: this.$t('请输入定价单类型'),
          trigger: 'blur'
        },
        status: {
          required: true,
          message: this.$t('请输入定价单类型'),
          trigger: 'blur'
        },
        // 采购员
        purName: {
          required: true,
          message: this.$t('请输入采购员'),
          trigger: 'blur'
        },
        // 标题
        title: {
          required: true,
          message: this.$t('请输入标题'),
          trigger: 'blur'
        },
        // 公司
        companyCode: {
          required: true,
          message: this.$t('请输入公司'),
          trigger: 'blur'
        },
        // 	采购组织
        purOrgCode: {
          required: true,
          message: this.$t('请输入采购组织'),
          trigger: 'blur'
        },
        // 定价对对像
        priceObjectName: {
          required: true,
          message: this.$t('请输入定价对象'),
          trigger: 'blur'
        },
        priceClassification: {
          required: true,
          message: this.$t('请选择价格分类'),
          trigger: 'blur'
        },
        PricingType: {
          required: true,
          message: this.$t('请选择定价类型'),
          trigger: 'blur'
        },
        siteName: {
          required: true,
          message: this.$t('请输入工厂'),
          trigger: 'blur'
        },
        pointType: {
          required: true,
          message: this.$t('请输入定价类型'),
          trigger: 'blur'
        }
      },
      companyNameSelectOptions: [],
      purOrgNameSelectOptions: [],
      factorySelectOptions: [],
      isDisabled: null,
      requestUrls: {},
      downTemplateName: this.$t('采购明细模板'),
      downTemplateParams: {
        page: {
          current: 1,
          size: 10
        },
        rules: []
      },
      currencyNameData: null, //币种信息
      taxRateNameList: null,
      unitNameList: null,
      deptNameList: null,
      deliveryPlaceData: null,
      editColumns: [],
      enableChangePointType: false,
      dataSource: {
        sourcingExpandList: []
      }
    }
  },
  activated() {
    window.addEventListener('resize', this.resetGridHeight)
    if (this.isExecutedMounted) {
      this.isExecutedMounted = false
      return
    }
    this.getDetail()
  },
  deactivated() {
    window.removeEventListener('resize', this.resetGridHeight)
  },
  async mounted() {
    this.isExecuteMounted = true
    await this.getDropDownData().catch(() => {})
    this.getCompanyNameSelectOptions()
    await this.getDetail()
    this.handleUnionColumns()
    this.$set(this.pageConfig[0].grid, 'columnData', columnDataSeriesItem)
    this.$set(this.pageConfig[1].grid, 'columnData', this.editColumns)
    this.$set(this.pageConfig[2].grid, 'columnData', columnDataQuota())
    this.getSourcingExpandTitle()
  },
  methods: {
    // 头部 - btn - 保存
    save() {
      this.$API.seriesItem.itemApplySave(this.formObject).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: res.msg,
            type: 'success'
          })
        }
      })
    },
    // 头部- btn - 提交
    submit() {
      this.$API.seriesItem
        .itemApplySubmit({
          id: this.$route.query.id
        })
        .then((res) => {
          if (res.code == 200) {
            this.comeBack()
            this.$toast({
              content: res.msg,
              type: 'success'
            })
          }
        })
    },
    // 获取详细信息（头信息）
    async getDetail() {
      let param = {
        id: this.$route.query.id
      }
      await this.$API.seriesItem.itemApplyDetail(param).then((res) => {
        let seriesItemApplyDTO = res.data.seriesItemApplyDTO
        let seriesItemDTOS = res.data.seriesItemDTOS
        let status = res.data.seriesItemApplyDTO.status
        // this.seriesItemId = res.data.seriesItemDTO.itemId;
        this.isDisabled = [2, 3].includes(Number(status))
        // 是否可以查看OA审批进度
        const isViewOA = [2, 3, 4].includes(Number(status))
        if (seriesItemApplyDTO.companyId) {
          this.changeTaxItem(this.formObject.siteCode)
        }
        let arr = []
        res.data.pointExpands.forEach((x) => {
          arr.push(x.companyCode + '+' + x.purOrgCode + '+' + x.siteCode)
        })
        this.expand = arr
        this.formObject = seriesItemApplyDTO
        this.enableChangePointType = res.data.enableChangePointType
        this.$set(this.pageConfig[0].grid, 'dataSource', seriesItemDTOS)
        this.getPurOrgNameSelectOptions(this.formObject.companyId)
        this.getpermissionSiteList()
        this.$set(
          this.pageConfig[1],
          'toolbar',
          toolbar(!this.isDisabled, isViewOA) // 已提交,审批通过 禁止编辑
        )
        this.$set(
          this.pageConfig[0],
          'toolbar',
          toolbarSeriesItem(!this.isDisabled, isViewOA) // 已提交,审批通过 禁止编辑
        )
        this.$set(
          this.pageConfig[2],
          'toolbar',
          toolbarTwo(!this.isDisabled, isViewOA) // 已提交,审批通过 禁止编辑
        )
      })
    },
    // 获取“公司”下拉数据
    getCompanyNameSelectOptions() {
      this.$API.masterData.permissionCompanyList().then((res) => {
        this.companyNameSelectOptions = res?.data.map((item) => ({
          text: item.orgCode + '-' + item.orgName,
          value: item.orgCode,
          data: {
            companyCode: item.orgCode,
            companyName: item.orgName,
            companyId: item.id
          }
        }))
      })
      this.getpermissionSiteList()
    },
    // 获取“工厂”下拉数据
    getpermissionSiteList() {
      if (this.formObject.companyId && this.formObject.purOrgId) {
        this.$API.masterData
          .permissionSiteList({
            companyId: this.formObject.companyId,
            buOrgId: this.formObject.purOrgId,
            orgLevelTypeCode: 'ORG06'
          })
          .then((res) => {
            this.factorySelectOptions = res?.data.map((item) => ({
              text: item.orgCode + '-' + item.orgName,
              value: item.orgName,
              data: {
                siteName: item.orgName,
                siteId: item.id,
                siteCode: item.orgCode
              }
            }))
          })
      }
    },
    // 获取“采购组织”下拉数据
    getPurOrgNameSelectOptions(companyId) {
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.purOrgNameSelectOptions = res.data.map((item) => ({
            text: item.organizationCode + '-' + item.organizationName,
            value: item.organizationCode,
            data: {
              purOrgName: item.organizationName,
              purOrgCode: item.organizationCode,
              purOrgId: item.id
            }
          }))
        })
    },
    // 获取“拓展工厂”下拉数据
    async changeTaxItem(siteCode) {
      this.dataSource.sourcingExpandList = []
      const res = await this.$API.rfxDetail
        .purAllOrgWithSite({ fuzzyParam: siteCode, purOrgCode: this.formObject.purOrgCode })
        .catch(() => {})
      if (res && res.data) {
        let dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            dataSource.push({
              text:
                v.companyCode +
                '-' +
                v.businessOrganizationCode +
                '-' +
                v.businessOrganizationName +
                '+' +
                x.orgCode +
                '-' +
                x.orgName,
              value: v.companyCode + '+' + v.businessOrganizationCode + '+' + x.orgCode,
              data: {
                companyCode: v.companyCode,
                companyId: v.companyId,
                companyName: v.companyName,
                purOrgCode: v.businessOrganizationCode,
                purOrgId: v.id,
                purOrgName: v.businessOrganizationName,
                siteCode: x.orgCode,
                siteId: x.id,
                siteName: x.orgName
              }
            })
          })
        })
        this.dataSource.sourcingExpandList = dataSource
      }
    },
    // 获取“拓展工厂”title
    getSourcingExpandTitle() {
      let _textList = []
      this.dataSource.sourcingExpandList?.forEach((item) => {
        if (this.expand.includes(item.value)) {
          _textList.push(item.text)
        }
      })
      this.sourcingExpandTitle = _textList?.join(',')
      this.sourcingExpandKey = new Date().getTime()
    },
    // 获取“列表相关字段”下拉数据
    async getDropDownData() {
      const tasks = []
      // 获取币种
      tasks.push(() =>
        this.$API.masterData.queryAllCurrency().then((res) => {
          this.currencyNameData = res.data || []
        })
      )
      //税率信息
      tasks.push(() =>
        this.$API.masterData.queryAllTaxItem().then((res) => {
          this.taxRateNameList = res?.data || []
        })
      )
      //基本单位
      tasks.push(() =>
        this.$API.masterData.pagedQueryUnit().then((res) => {
          this.unitNameList = res?.data?.records || []
          this.purUnitNameList = res?.data?.records || [] // FIXME 采购单位和基本单位的区别
        })
      )
      //直送地
      tasks.push(() =>
        this.$API.masterData.dictionaryGetList({ dictCode: 'DELIVERY_PLACE' }).then((res) => {
          this.deliveryPlaceData = res?.data || []
        })
      )
      for (const task of sliceArray(tasks, 5)) {
        await Promise.all(task.map((fn) => fn())).catch(() => {})
      }
    },
    // 切换 - 拓展工厂
    handleExpand(e) {
      let expandSaveRequestList = []
      e.value.forEach((v) => {
        this.dataSource.sourcingExpandList.forEach((x) => {
          if (v == x.value) {
            expandSaveRequestList.push(x.data)
          }
        })
      })
      this.formObject.expandSaveRequestList = expandSaveRequestList
      this.getSourcingExpandTitle()
    },
    // 切换 - 采购组织
    purOrgNameChange(e) {
      this.formObject.purOrgName = e.itemData.data.purOrgName
      this.formObject.purOrgId = e.itemData.data.purOrgId
      this.formObject.siteName = ''
      this.formObject.siteCode = ''
      this.formObject.siteId = ''
      this.getpermissionSiteList()
    },
    // 切换 - 工厂
    siteIdChange(e) {
      let _data = e.itemData
      this.formObject.siteCode = _data.data.siteCode
      this.formObject.siteId = _data.data.id
      this.formObject.siteName = _data.siteName
      this.changeTaxItem(this.formObject.siteCode)
    },
    // 切换 - 公司
    companyNameChange(e) {
      this.formObject.companyName = e.itemData.companyName
      this.formObject.companyId = e.itemData.data.companyId
      this.formObject.purOrgName = ''
      this.formObject.purOrgCode = ''
      this.formObject.purOrgId = ''
      this.formObject.siteName = ''
      this.formObject.siteCode = ''
      this.formObject.siteId = ''
      this.formObject.sourcingExpand = ''
      this.dataSource.sourcingExpandList = []
      this.getPurOrgNameSelectOptions(e.itemData.data.companyId)
    },
    // 列配置处理
    handleUnionColumns() {
      let params = {
        currencyNameData: this.currencyNameData,
        taxRateNameList: this.taxRateNameList,
        unitNameList: this.unitNameList, // 基本单位
        purUnitNameList: this.purUnitNameList, // 采购单位
        deliveryPlaceData: this.deliveryPlaceData,
        // rfxId: this.$route.query.rfxId,
        self: this,
        calcPrice: this.$API.seriesItem.calcPrice
      }
      this.editColumns = columnDataPriceItem(params, {
        $on: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$on(event, fn)
        },
        $emit: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$emit(event, fn)
        }
      })
    },
    // 表格 - rowDataBound
    rowDataBound(args) {
      if (args.data.isGray == 1) {
        args.row.classList.add('backgroundRed')
      }
    },
    // 表格 - 数据处理
    serializeList(list) {
      let arr = []
      list.forEach((x) => {
        x.allocationRatio && (x.allocationRatio = Number(x.allocationRatio) * 100)
        arr.push(
          JSON.stringify({
            itemCode: x.itemCode,
            siteCode: x.siteCode
          })
        )
      })
      arr = [...new Set(arr)]
      arr = arr.filter((item, index) => index % 2 !== 0)
      list.forEach((x) => {
        arr.forEach((v) => {
          if (x.itemCode == JSON.parse(v).itemCode && x.siteCode == JSON.parse(v).siteCode) {
            x.isGray = true
          }
        })
      })
      return list
    },
    // 表格 - 表格监听
    actionComplete(e) {
      if (e.action == 'edit') {
        let rowData = [e.data]
        let needAttrs = e.rowData.needAttrs || []
        // 0 == '' 是true，所以判断是否等于'' 不能用== ，要用===
        if (
          needAttrs.includes('length') &&
          (rowData[0].length == null || rowData[0].length === '')
        ) {
          this.$toast({
            content: this.$t('请填写长(mm)'),
            type: 'warning'
          })
          return
        }
        if (needAttrs.includes('width') && (rowData[0].width == null || rowData[0].width === '')) {
          this.$toast({
            content: this.$t('请填写宽(mm)'),
            type: 'warning'
          })
          return
        }
        if (
          needAttrs.includes('weight') &&
          (rowData[0].weight == null || rowData[0].weight === '')
        ) {
          this.$toast({
            content: this.$t('请填写重量(g)'),
            type: 'warning'
          })
          return
        }
        if (
          needAttrs.includes('projectCost') &&
          (rowData[0].projectCost == null || rowData[0].projectCost === '')
        ) {
          this.$toast({
            content: this.$t('请填写工程费'),
            type: 'warning'
          })
          return
        }
        if (needAttrs.includes('pages') && (rowData[0].pages == null || rowData[0].pages === '')) {
          this.$toast({
            content: this.$t('请填写页数'),
            type: 'warning'
          })
          return
        }
        if (
          needAttrs.includes('otherFee') &&
          (rowData[0].otherFee == null || rowData[0].otherFee === '')
        ) {
          this.$toast({
            content: this.$t('请填写其他费用'),
            type: 'warning'
          })
          return
        }
        if (
          needAttrs.includes('mouldCost') &&
          (rowData[0].mouldCost == null || rowData[0].mouldCost === '')
        ) {
          this.$toast({
            content: this.$t('请填写模具费'),
            type: 'warning'
          })
          return
        }
        if (
          needAttrs.includes('makeup') &&
          (rowData[0].makeup == null || rowData[0].makeup === '')
        ) {
          this.$toast({
            content: this.$t('请填写拼版数量'),
            type: 'warning'
          })
          return
        }
        if (
          needAttrs.includes('height') &&
          (rowData[0].height == null || rowData[0].height === '')
        ) {
          this.$toast({
            content: this.$t('请填写高(mm)'),
            type: 'warning'
          })
          return
        }
        if (
          needAttrs.includes('firstEditionNumber') &&
          (rowData[0].firstEditionNumber == null || rowData[0].firstEditionNumber === '')
        ) {
          this.$toast({
            content: this.$t('请填写首次打板数量'),
            type: 'warning'
          })
          return
        }
      }
    },
    // 表格 - 按钮操作
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'redirectOA') {
        this.handleRedirectOaLink() // OA审批进度
        return
      }
      if (e.tabIndex == 0) {
        if (e.toolbar.id == 'Add') {
          this.$dialog({
            modal: () => import('./components/priceRecordsDialog/index.vue'),
            data: {
              title: this.$t('选择价格记录'),
              siteCode: this.formObject.siteCode,
              companyCode: this.formObject.companyCode
            },
            success: (data) => {
              const itemDTOList = []
              data.forEach((item) => {
                itemDTOList.push({
                  ...item,
                  id: null,
                  siteCode: item.siteList[0].siteCode,
                  siteName: item.siteList[0].siteName,
                  siteId: item.siteList[0].siteId,
                  texture: item.material
                })
              })
              const params = {
                applyId: this.$route.query.id,
                itemDTOList
              }
              this.$API.seriesItem.itemApplySeriesSave(params).then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: res.msg,
                    type: 'success'
                  })
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
            }
          })
        } else if (e.toolbar.id == 'del') {
          if (_selectGridRecords.length < 0) {
            this.$toast({
              content: '系列物料未配置,请添加并保存系列物料后再试',
              type: 'warning'
            })
          } else {
            this.$API.seriesItem
              .itemApplySeriesDelete({
                applyId: this.$route.query.id,
                seriesItemIds: idList
              })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: res.msg,
                    type: 'success'
                  })
                  this.$refs.templateRef.refreshCurrentGridData()
                  // this.seriesItemId = null;
                  // this.$set(
                  //   this.seriesItemPageConfig[0].grid,
                  //   "dataSource",
                  //   []
                  // );
                }
              })
          }
        } else if (e.toolbar.id == 'import') {
          this.requestUrls = {
            templateUrlPre: 'rfxRequireDetail',
            templateUrl: 'download',
            uploadUrl: 'import',
            pointId: this.$route.query.id
          }
          this.showUploadExcel(true)
        }
      } else if (e.tabIndex == 1) {
        if (e.toolbar.id == 'Save') {
          this.handleSaveFn(e) //保存
        } else if (e.toolbar.id == 'del') {
          this.handleDelFn(_selectGridRecords, idList) //删除
        } else if (e.toolbar.id == 'Add') {
          this.handleAddFn(e) //新增
        } else if (e.toolbar.id == 'download') {
          this.downloadFiles()
        }
      } else if (e.tabIndex == 2) {
        if (e.toolbar.id == 'Save') {
          this.handleSaveFn(e) //保存
        } else if (e.toolbar.id == 'import') {
          this.requestUrls = {
            templateUrlPre: 'whitePoint',
            uploadUrl: 'importItemQuota',
            noDown: true
          }
          this.showUploadExcel(true)
        } else if (e.toolbar.id == 'download') {
          this.downloadFilesTwo()
        }
      }
    },
    // 表格 - 单元格点击操作
    handleClickCellTitle(e) {
      if (e.field == 'attributeMaintain') {
        let _current = this.$refs.templateRef.getCurrentTabRef()
        _current?.grid.endEdit()
        this.$dialog({
          modal: () => import('./components/attributeMaintainDialog/index.vue'),
          data: {
            title: this.$t('属性维护'),
            applyId: this.$route.query.id,
            seriesItemId: e.data.seriesItemId,
            seriesItemCode: e.data.seriesItemCode,
            itemId: e.data.itemId,
            itemCode: e.data.itemCode,
            itemName: e.data.itemName
          },
          success: () => {
            this.$API.seriesItem
              .itemApplyPriceCalculate({
                applyId: this.$route.query.id,
                seriesItemCode: e.data.seriesItemCode,
                itemId: e.data.itemId,
                itemCode: e.data.itemCode,
                seriesItemId: e.data.seriesItemId
              })
              .then((res) => {
                if (res.code == 200) {
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.tabIndex === 1 && e.field == 'itemCode') {
        this.$dialog({
          modal: () => import('./components/editDialog/index.vue'),
          data: {
            title: this.$t('编辑定价物料'),
            id: this.$route.query.id,
            rowData: e.data,
            companyCode: this.formObject.companyCode,
            dropdownData: {
              deliveryPlaceList: this.deliveryPlaceData,
              currencyList: this.currencyNameData,
              taxList: this.taxRateNameList,
              purUnitList: this.purUnitNameList
            }
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    // common tab - btn - OA审批进度
    handleRedirectOaLink() {
      this.$API.seriesItem.getOaLink({ pointId: this.$route.query.id }).then((res) => {
        if (res.code === 200 && res?.data) {
          window.open(res.data)
        }
      })
    },
    // 定价物料 - btn - 新增
    handleAddFn() {
      this.$dialog({
        modal: () => import('./components/selectItemDialog/index.vue'),
        data: {
          title: this.$t('选择物料'),
          // itemId: this.seriesItemId,
          applyId: this.$route.query.id,
          priceClassification: this.formObject.priceClassification,
          sourcingType: this.formObject.pointType
        },
        success: (rows) => {
          for (let item of rows) {
            item.id = undefined
          }
          this.$API.seriesItem
            .itemApplyPriceSave({
              applyId: this.$route.query.id,
              seriesPriceItems: rows
            })
            .then(() => {
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    },
    // 定价物料、配额分配 - btn - 保存
    handleSaveFn(e) {
      this.endEdit()
      let _selectGridRecords = e.grid.getCurrentViewRecords()
      if (e.tabIndex == 1) {
        let minPurchaseQuantityflag = false
        let minPackageQuantityflag = false
        let leadTimeflag = false
        let unconditionalLeadTimeflag = false
        let deliveryPlace = false
        let validStartTime = false
        let validEndTime = false

        for (let item = 0; item < _selectGridRecords.length; item++) {
          if (
            !_selectGridRecords[item].minPurchaseQuantity ||
            _selectGridRecords[item].minPurchaseQuantity == 0
          ) {
            minPurchaseQuantityflag = true
            break
          }
          if (
            !_selectGridRecords[item].minPackageQuantity ||
            _selectGridRecords[item].minPackageQuantity == 0
          ) {
            minPackageQuantityflag = true
            break
          }
          if (!_selectGridRecords[item].leadTime || _selectGridRecords[item].leadTime == 0) {
            leadTimeflag = true
            break
          }
          if (
            !_selectGridRecords[item].unconditionalLeadTime ||
            _selectGridRecords[item].unconditionalLeadTime == 0
          ) {
            unconditionalLeadTimeflag = true
            break
          }
          if (!_selectGridRecords[item].deliveryPlace) {
            deliveryPlace = true
            break
          }
          if (!_selectGridRecords[item].validStartTime) {
            validStartTime = true
            break
          }
          if (!_selectGridRecords[item].validEndTime) {
            validEndTime = true
            break
          }
        }
        if (minPurchaseQuantityflag) {
          this.$toast({
            content: this.$t('请填写最小采购量'),
            type: 'warning'
          })
          return
        }
        if (minPackageQuantityflag) {
          this.$toast({
            content: this.$t('请填写最小包装量'),
            type: 'warning'
          })
          return
        }
        if (leadTimeflag) {
          this.$toast({ content: this.$t('请填写L/T'), type: 'warning' })
          return
        }
        if (unconditionalLeadTimeflag) {
          this.$toast({ content: this.$t('请填写无条件L/T'), type: 'warning' })
          return
        }
        if (deliveryPlace) {
          this.$toast({
            content: this.$t('请填写直送地'),
            type: 'warning'
          })
          return
        }
        if (validStartTime) {
          this.$toast({
            content: this.$t('请填写生效日期'),
            type: 'warning'
          })
          return
        }
        if (validEndTime) {
          this.$toast({
            content: this.$t('请填写失效日期'),
            type: 'warning'
          })
          return
        }
        this.$API.seriesItem
          .itemApplyPriceUpdate({
            batchSaveFlag: 1,
            applyId: this.$route.query.id,
            seriesPriceItems: _selectGridRecords
          })
          .then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: res.msg,
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
      } else if (e.tabIndex == 2) {
        let _selectGridRecords = e.grid.getCurrentViewRecords()
        let str = []
        _selectGridRecords.forEach((item, index) => {
          if (item.allocationRatio < 0 || item.allocationRatio > 100) {
            str.push(this.$t(`第${index + 1}行`))
          }
        })
        if (str.length != 0) {
          this.$toast({
            content: str.join(',') + '的配额字段，请输入（0-100）的数字',
            type: 'warning'
          })
          return
        }
        let ids = _selectGridRecords.map((x) => {
          return {
            allocationQuantity: x.allocationQuantity,
            allocationRatio: (x.allocationRatio / 100).toFixed(2),
            minSplitQuantity: x.minSplitQuantity,
            id: x.id,
            minPurQuantity: x.minPurQuantity,
            itemCode: x.itemCode,
            siteCode: x.siteCode,
            stepValue: x?.stepValue || 0
          }
        })
        this.$API.seriesItem
          .saveQuota({
            pointId: this.$route.query.id,
            quotaDTOList: ids
          })
          .then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: res.msg,
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
      }
    },
    // 定价物料 - btn - 删除
    handleDelFn(_selectGridRecords, idList) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          applyId: this.$route.query.id,
          seriesItemIds: idList
        }
        this.$API.seriesItem.itemApplyPriceDelete(params).then(() => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },
    // 定价物料 - btn - 导出
    downloadFiles() {
      let id = this.$route.query?.id || this.detailInfo.rfxEvaluate.id
      let params = {
        id: id,
        queryBuilderDTO: {
          page: {
            current: 1,
            size: 100
          }
        }
      }
      this.$API.priceService.exportPrice(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 配额分配 - btn - 导出
    downloadFilesTwo() {
      let id = this.$route.query?.id || this.detailInfo.rfxEvaluate.id
      let params = {
        page: { current: 1, size: 20 },
        defaultRules: [
          {
            label: '',
            field: 'pointId',
            type: 'string',
            operator: 'equal',
            value: id
          },
          {
            label: '',
            field: 'sourceDocType',
            type: 'string',
            operator: 'equal',
            value: 'serial_item_point'
          }
        ]
      }
      this.$API.whitePoint.exportItemQuota(params).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    },

    // common - 导入 - 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // common - 导入 - 上传弹窗
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // util - 重置表格高度
    resetGridHeight() {
      this.$refs.templateRef.resetGridHeight()
    },
    // util - 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
    },
    // util - 返回上一页
    comeBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped lang="scss">
.top-shrink {
  width: 100%;
  color: #9bb0cb;
  display: flex;
  justify-content: center;
}

.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.top-info {
  min-height: 100px;
  flex-wrap: wrap;
  display: flex;
  flex-shrink: 0;
  background: rgba(245, 248, 251, 1);
  border-radius: 0 8px 0 0;
  position: relative;
  .lf-wrap {
    flex: 1;
    transition: all 2s ease-in-out;
    background: linear-gradient(rgba(99, 134, 193, 0.06), rgba(99, 134, 193, 0.06)),
      linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      line-height: 1;

      .name-wrap {
        flex: 1;
        .first-line {
          display: flex;
          align-items: center;
          .code {
            font-size: 20px;
            font-family: DINAlternate;
            font-weight: bold;
            color: rgba(41, 41, 41, 1);
          }
        }
      }

      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
      }
    }
  }
}
.form-generator-form {
  display: flex;
  flex-flow: row wrap;
  row-gap: 20px;
  margin: 0 -10px;
  .form-generator-form-item {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 0;
  }

  .form-generator-item-col-4 {
    display: block;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .form-generator-item-col-2 {
    display: block;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .form-generator-item-col-1 {
    display: block;
    flex: 0 0 100%;
    max-width: 100%;
  }
}
.relation-ships {
  flex: 1;
  background: rgba(255, 255, 255, 1);
}
</style>
