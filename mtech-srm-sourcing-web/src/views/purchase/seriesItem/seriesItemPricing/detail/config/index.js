import { i18n, permission } from '@/main.js'
import { createEditInstance, Formatter } from '@/utils/ej/dataGrid'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'
// import * as combination from '../utils/combination'
import { MAX_SAFE_INTEGER } from '@/constants/editConfig'
import { getValueByPath } from '@/utils/obj'
import { fieldLinkUpdate } from '@/utils/ej/dataGrid/utils'
import { useFiltering } from '@/utils/ej/select'
import {
  addArrTextField,
  makeTextFields,
  filteringByText,
  addArrCodeField
} from '@/views/common/columnData/utils'
export const columnDataSeriesItem = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'itemCode',
    headerText: i18n.t('系列物料编码'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'itemName',
    headerText: i18n.t('系列物料名称'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'texture',
    headerText: i18n.t('物料材质'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价（含税）'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价（未税）'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号'),
    allowEditing: false,
    with: '150'
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    with: '150',
    allowEditing: false,
    formatter: ({ field }, item) => {
      const cellVal = item[field]
      if (typeof cellVal == 'string') {
        return cellVal.substring(0, 10)
      } else {
        return ''
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    with: '150',
    allowEditing: false,
    formatter: ({ field }, item) => {
      const cellVal = item[field]
      if (typeof cellVal == 'string') {
        return cellVal.substring(0, 10)
      } else {
        return ''
      }
    }
  }
]

export const columnDataPriceItem = ({
  currencyNameData = [],
  taxRateNameList = [],
  purUnitNameList = [],
  deliveryPlaceData = [],
  self,
  calcPrice
} = {}) => {
  const quoteAttributeList = [
    {
      text: i18n.t('标准价'),
      value: 'standard_price'
    },
    {
      text: i18n.t('寄售价'),
      value: 'mailing_price'
    }
  ]
  const quoteModeList = [
    {
      text: i18n.t('按照入库'),
      value: 'in_warehouse'
    },
    {
      text: i18n.t('按出库'),
      value: 'out_warehouse'
    },
    {
      text: i18n.t('按订单日期'),
      value: 'order_date'
    }
  ]
  // 币种
  const currencyNameDataCN = addArrCodeField(currencyNameData, 'currencyCode', 'currencyName')

  // 税率
  const taxRateNameListCN = addArrCodeField(taxRateNameList, 'taxItemCode', 'taxName', 'taxRate')

  // 单位
  // const unitNameListCN = addArrTextField(unitNameList, 'unitCode', 'unitName')
  const purUnitNameListCN = addArrTextField(purUnitNameList, 'unitCode', 'unitName')

  //直送地
  const deliveryPlaceDataCN = addArrTextField(deliveryPlaceData, 'itemCode', 'itemName')

  const editInstance = createEditInstance()
    .onChange(async (ctx, { field, rowData }, event) => {
      //   ctx.getOptions(field);
      if (rowData.validEndDateEditAble == 0) {
        ctx.setOptions('validEndTime', {
          readonly: true,
          disabled: true
        })
        ctx.setOptions('validStartTime', {
          readonly: true,
          disabled: true
        })
      } else {
        ctx.setOptions('validEndTime', {
          readonly: self.formObject.companyCode == '1503',
          disabled: self.formObject.companyCode == '1503'
        })
        ctx.setOptions('validStartTime', {
          readonly: false,
          disabled: false
        })
      }

      // const val = event.value === null ? '' : event.value
      if (field === 'taxCode') {
        let taxedUnitPrice =
          Number(rowData.untaxedUnitPrice) +
          Number(rowData.untaxedUnitPrice) * Number(rowData.taxRate)
        if (event.itemData) {
          ctx.setValueByField('taxRate', event.itemData.taxRate)
          ctx.setValueByField('taxCode', event.itemData.taxItemCode)
          ctx.setValueByField('taxName', event.itemData.taxItemName)
          ctx.setValueByField('taxedUnitPrice', Number(taxedUnitPrice))
          return
        } else {
          ctx.setValueByField('taxRate', '')
          ctx.setValueByField('taxCode', '')
          ctx.setValueByField('taxeName', '')
          return
        }
      }
      if (field === 'untaxedUnitPrice') {
        let taxedUnitPrice =
          Number(rowData.untaxedUnitPrice) +
          Number(rowData.untaxedUnitPrice) * Number(rowData.taxRate)
        ctx.setValueByField('taxedUnitPrice', Number(taxedUnitPrice).toFixed(5))
      } else if (field === 'validStartTime') {
        // 针对1503公司，设置失效日期的值
        if (self.formObject.companyCode == '1503') {
          var date = new Date()
          var year = date.getFullYear()
          var yearTwo = date.getFullYear() + 1
          let datae = year + '-' + '6' + '-' + '30'
          let dateTwo = year + '-' + '7' + '-' + '1'
          if (new Date(event).getTime() >= new Date(dateTwo).getTime()) {
            ctx.setValueByField('validEndTime', yearTwo + '-' + '12' + '-' + '31 ')
          } else if (new Date(event).getTime() <= new Date(datae).getTime()) {
            ctx.setValueByField('validEndTime', year + '-' + '12' + '-' + '31 ')
          }
        }
        ctx.setOptions(field.replace('validStartTime', 'validEndTime'), {
          // 失效日期必须大于当前时间
          min: new Date(Math.max(new Date(event).getTime(), Date.now()))
        })
      }
    })
    .onInput((ctx, { field, rowData }) => {
      if (
        field === 'length' ||
        field === 'width' ||
        field === 'height' ||
        field === 'makeup' ||
        field === 'mouldCost' ||
        field === 'otherFee' ||
        field === 'pages' ||
        field === 'projectCost' ||
        field === 'firstEditionNumber' ||
        field === 'weight'
      ) {
        let param = {
          firstEditionNumber: [rowData][0].firstEditionNumber,
          height: [rowData][0].height,
          length: [rowData][0].length,
          makeup: [rowData][0].makeup,
          mouldCost: [rowData][0].mouldCost,
          otherFee: [rowData][0].otherFee,
          pages: [rowData][0].pages,
          projectCost: [rowData][0].projectCost,
          seriesItemId: [rowData][0].id,
          weight: [rowData][0].weight,
          width: [rowData][0].width
        }
        calcPrice(param).then((res) => {
          let price = ''
          price = res.data
          if (price) {
            ctx.setValueByField('untaxedUnitPrice', Number(price))
            ctx.setValueByField('formulaResult', Number(price))
            let taxedUnitPrice =
              Number(rowData.untaxedUnitPrice) +
              Number(rowData.untaxedUnitPrice) * Number(rowData.taxRate)
            ctx.setValueByField('taxedUnitPrice', Number(taxedUnitPrice).toFixed(5))
          }
        })
      }

      if (field === 'currencyCode') {
        fieldLinkUpdate(ctx, field, {
          currencyName: 'currencyName'
        })
      }
      if (field === 'purUnitName') {
        fieldLinkUpdate(ctx, field, {
          purUnitCode: 'unitCode'
        })
      }
    })
  return [
    {
      width: '50',
      type: 'checkbox',
      allowEditing: false
    },
    {
      width: '150',
      field: 'itemCode',
      allowEditing: false,
      headerText: i18n.t('物料编号'),
      cssClass: self.isDisabled ? '' : 'field-content'
    },
    {
      width: '150',
      field: 'itemName',
      allowEditing: false,
      headerText: i18n.t('物料名称')
    },
    {
      width: '150',
      field: 'spec',
      allowEditing: false,
      headerText: i18n.t('物料规格')
    },
    {
      width: '150',
      field: 'texture',
      headerText: i18n.t('物料材质'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'categoryCode',
      allowEditing: false,
      headerText: i18n.t('品类编码')
    },
    {
      width: '150',
      field: 'categoryName',
      allowEditing: false,
      headerText: i18n.t('品类名称')
    },
    {
      width: '150',
      field: 'deliveryPlace',
      headerText: i18n.t('直送地'),
      edit: editInstance.create({
        getEditConfig: () => {
          // const readonly = !!detailInfo.currencyCode;
          return {
            type: 'select',
            'show-clear-button': true,
            fields: makeTextFields('itemName'),
            dataSource: deliveryPlaceDataCN,
            placeholder: i18n.t('选择直送地'),
            // 创建标案选择的币种要带入采购明细，且不能编辑
            readonly: false,
            disabled: false,
            'allow-filtering': true,
            filtering: useFiltering(filteringByText)
          }
        }
      })
    },
    {
      width: '150',
      field: 'formulaContent',
      allowEditing: false,
      headerText: i18n.t('计算公式')
    },
    {
      width: '150',
      field: 'formulaResult',
      allowEditing: false,
      headerText: i18n.t('计算结果'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    // {
    //   width: '150',
    //   field: 'attributeMaintain',
    //   headerText: i18n.t('属性维护'),
    //   cssClass: 'field-content',
    //   allowEditing: false,
    //   valueConverter: {
    //     type: 'placeholder',
    //     placeholder: i18n.t('属性值维护')
    //   }
    // },
    {
      width: '150',
      field: 'length',
      allowEditing: true,
      headerText: i18n.t('长(mm)'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'width',
      allowEditing: true,
      headerText: i18n.t('宽(mm)'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'height',
      allowEditing: true,
      headerText: i18n.t('高(mm)'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'makeup',
      allowEditing: true,
      headerText: i18n.t('拼版数量'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'pages',
      allowEditing: true,
      headerText: i18n.t('页数'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'mouldCost',
      allowEditing: true,
      headerText: i18n.t('模具费'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'firstEditionNumber',
      allowEditing: true,
      headerText: i18n.t('首次打板数量'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'projectCost',
      allowEditing: true,
      headerText: i18n.t('工程费'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'weight',
      allowEditing: true,
      headerText: i18n.t('重量(g)'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'otherFee',
      allowEditing: true,
      headerText: i18n.t('其他费用'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'taxedUnitPrice',
      headerText: i18n.t('单价（含税）'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      width: '150',
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价（未税）'),
      allowEditing: true,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'seriesItemCode',
      headerText: i18n.t('系物料编码'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'seriesItemName',
      headerText: i18n.t('系物料名称'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'bidHistoryPrice',
      headerText: i18n.t('历史价格'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'rangeDiscount',
      headerText: i18n.t('降幅(单位: %)'),
      allowEditing: false
    },
    {
      field: 'priceUnit', //BOM信息-价格单位  成本因子-价格单位  价格信息-价格单位
      headerText: i18n.t('价格单位'),
      edit: editInstance.create({
        getEditConfig: () => {
          return {
            ...PRICE_EDIT_CONFIG,
            type: 'number',
            readonly: true
          }
        }
      })
    },
    {
      width: '150',
      field: 'quoteMode',
      headerText: i18n.t('报价方式'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          readonly: true,
          disabled: true,
          dataSource: quoteModeList,
          placeholder: i18n.t('请选择报价方式')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return quoteModeList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      width: '150',
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          readonly: true,
          disabled: true,
          dataSource: quoteAttributeList,
          placeholder: i18n.t('请选择报价属性')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return quoteAttributeList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      width: '150',
      field: 'currencyName',
      headerText: i18n.t('币种'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      width: '150',
      field: 'currencyCode',
      headerText: i18n.t('币种编码'),
      edit: editInstance.create({
        getEditConfig: () => {
          // const readonly = !!detailInfo.currencyCode;
          return {
            type: 'select',
            'show-clear-button': true,
            fields: makeTextFields('currencyCode'),
            dataSource: currencyNameDataCN,
            placeholder: i18n.t('选择币种编码'),
            // 创建标案选择的币种要带入采购明细，且不能编辑
            readonly: true,
            disabled: true,
            'allow-filtering': true,
            filtering: useFiltering(filteringByText)
          }
        }
      })
    },
    {
      field: 'validStartTime',
      headerText: i18n.t('生效日期'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          // "time-stamp": true,
          placeholder: i18n.t('请输入生效日期')
        })
      })
    },
    {
      field: 'validEndTime',
      headerText: i18n.t('失效日期'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          readonly: self.formObject.companyCode === '1503',
          disabled: self.formObject.companyCode === '1503',
          // "time-stamp": true,
          placeholder: i18n.t('请输入失效日期')
        })
      })
    },
    {
      width: '150',
      field: 'taxName',
      headerText: i18n.t('税率名称'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      width: '150',
      field: 'taxCode',
      headerText: i18n.t('税率编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          readonly: true,
          disabled: true,
          'show-clear-button': true,
          fields: makeTextFields('taxItemCode'),
          placeholder: i18n.t('请选择币种编码'),
          dataSource: taxRateNameListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        })
      })
    },
    {
      width: '150',
      field: 'taxRate',
      headerText: i18n.t('税率值'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2, //限制两小数点
          disabled: true
        })
      })
    },
    {
      width: '150',
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      width: '150',
      field: 'purUnitName',
      headerText: i18n.t('订单单位'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          'show-clear-button': true,
          // readonly: rowData.itemCode ? true : false,
          // disabled: rowData.itemCode ? true : false,
          fields: makeTextFields('unitName'),
          'allow-filtering': true,
          filtering: useFiltering(filteringByText),
          dataSource: purUnitNameListCN
        })
      })
    },
    {
      width: '150',
      field: 'purUnitCode',
      headerText: i18n.t('订单编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      width: '150',
      field: 'bidConversionRate',
      headerText: i18n.t('转换率'),
      allowEditing: true,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2, //限制两小数点
          disabled: false
        })
      })
    },
    {
      width: '150',
      field: 'minPurchaseQuantity',
      headerText: i18n.t('最小采购量'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 1,
          max: MAX_SAFE_INTEGER,
          precision: 2, //限制两小数点
          disabled: false
        })
      })
    },
    {
      width: '150',
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装量'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 1,
          max: MAX_SAFE_INTEGER,
          precision: 2, //限制两小数点
          disabled: false
        })
      })
    },
    {
      width: '150',
      field: 'leadTime',
      headerText: i18n.t('L/T'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 1,
          max: MAX_SAFE_INTEGER,
          precision: 2, //限制两小数点
          disabled: false
        })
      })
    },
    {
      width: '150',
      field: 'unconditionalLeadTime',
      headerText: i18n.t('无条件L/T'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 1,
          max: MAX_SAFE_INTEGER,
          precision: 2, //限制两小数点
          disabled: false
        })
      })
    },
    {
      width: '150',
      field: 'remark',
      headerText: i18n.t('备注'),
      allowEditing: true
    }
  ]
}
export const columnDataQuota = () => {
  const editInstance = createEditInstance().onInput((ctx, { field, rowData, value }) => {
    console.log('onInput', { ctx, rowData, value, field })
  })
  return [
    {
      width: '50',
      type: 'checkbox',
      allowEditing: false
    },
    {
      width: '150',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'itemCode',
      allowEditing: false,
      headerText: i18n.t('物料编号')
    },
    {
      width: '150',
      field: 'itemName',
      allowEditing: false,
      headerText: i18n.t('物料名称')
    },
    {
      width: '150',
      field: 'untaxedUnitPrice',
      allowEditing: false,
      headerText: i18n.t('单价（未税）')
    },
    {
      width: '150',
      field: 'taxedUnitPrice',
      allowEditing: false,
      headerText: i18n.t('单价（含税）')
    },
    {
      field: 'allocationRatio',
      with: '150',
      headerText: i18n.t('配额比例(%)'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          precision: '0',
          min: 0,
          max: 100
        })
      }),
      formatter: ({ field }, item) => {
        let val = item[field] || 0
        return val + '%'
      }
    },
    {
      field: 'quotaEffectiveStartDate',
      with: '150',
      headerText: i18n.t('生效日期'),
      allowEditing: false,
      formatter: ({ field }, item) => {
        const cellVal = item[field]
        if (typeof cellVal == 'string') {
          return cellVal.substring(0, 10)
        } else {
          return ''
        }
      }
    },
    {
      field: 'quotaEffectiveEndDate',
      with: '150',
      headerText: i18n.t('失效日期'),
      allowEditing: false,
      formatter: ({ field }, item) => {
        const cellVal = item[field]
        if (typeof cellVal == 'string') {
          return cellVal.substring(0, 10)
        } else {
          return ''
        }
      }
    },
    {
      field: 'unitName',
      with: '150',
      headerText: i18n.t('基本单位'),
      allowEditing: false
    },
    {
      field: 'priceUnitCode',
      headerText: i18n.t('价格单位'),
      allowEditing: false
    },
    {
      field: 'purUnitName',
      with: '150',
      headerText: i18n.t('采购单位'),
      allowEditing: false
    },
    {
      field: 'sourceType',
      with: '150',
      headerText: i18n.t('来源'),
      valueConverter: {
        type: 'map',
        map: { 0: i18n.t('内部'), 1: i18n.t('外部') }
      },
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          readonly: true,
          disabled: true,
          dataSource: [
            { text: i18n.t('内部'), value: 0 },
            { text: i18n.t('外部'), value: 1 }
          ],
          placeholder: ''
        })
      })
    },
    {
      field: 'minSplitQuantity',
      with: '150',
      headerText: i18n.t('最小起拆点'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      width: '150',
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装量'),
      allowEditing: false
    },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          mailing_price: i18n.t('寄售价'),
          standard_price: i18n.t('标准价'),
          K: i18n.t('寄售价'),
          '': i18n.t('标准价'),
          outsource: i18n.t('委外价')
        }
      }
    }
  ]
}

export const toolbar = (enabledAdd = false, isViewOA = false) => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'del',
    icon: 'icon_solid_Cancel',
    title: i18n.t('删除'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'download',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  },
  {
    id: 'redirectOA',
    icon: 'icon_solid_edit',
    title: i18n.t('OA审批进度'),
    visibleCondition: () => isViewOA
  }
]
export const toolbarSeriesItem = (enabledAdd = false, isViewOA = false) => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'del',
    icon: 'icon_solid_Cancel',
    title: i18n.t('删除'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'import',
    icon: 'icon_solid_upload',
    title: i18n.t('导入')
  },
  {
    id: 'redirectOA',
    icon: 'icon_solid_edit',
    title: i18n.t('OA审批进度'),
    visibleCondition: () => isViewOA
  }
]
export const toolbarTwo = (enabledAdd = false, isViewOA = false) => [
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'import',
    icon: 'icon_solid_upload',
    title: i18n.t('导入')
  },
  {
    id: 'download',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  },
  {
    id: 'redirectOA',
    icon: 'icon_solid_edit',
    title: i18n.t('OA审批进度'),
    visibleCondition: () => isViewOA
  }
]
export const pageConfig = (url, id, self, serializeList, rowDataBound) => [
  {
    title: i18n.t('系列物料'),
    useToolTemplate: false,
    toolbar: toolbarSeriesItem(),
    gridId:
      permission.gridId['purchase']['seriesItem']['seriesItemPriceManagement']['detail'][
        'seriPricingItem'
      ],
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: [{}],
      dataSource: [],
      asyncConfig: {
        url,
        queryBuilderWrap: 'queryBuilderDTO',
        params: {
          id,
          itemType: 1
        }
      },
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      }
    }
  },
  {
    title: i18n.t('定价物料'),
    useToolTemplate: false,
    toolbar: [],
    gridId:
      permission.gridId['purchase']['seriesItem']['seriesItemPriceManagement']['detail'][
        'pricingItem'
      ],
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: [],
      dataSource: [],
      asyncConfig: {
        url,
        queryBuilderWrap: 'queryBuilderDTO',
        params: { id: id }
      },
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      }
    }
  },
  {
    title: i18n.t('配额分配'),
    useToolTemplate: false,
    toolbar: toolbarTwo(),
    gridId:
      permission.gridId['purchase']['seriesItem']['seriesItemPriceManagement']['detail'][
        'quotaAllocation'
      ],
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: [],
      rowDataBound,
      dataSource: [],
      asyncConfig: {
        url: self.$API.seriesItem.queryBuilder,
        // params: { pointId: id, sourceDocType: "serial_item_point" },
        // queryBuilderWrap: "queryBuilderDTO",
        defaultRules: [
          {
            label: '',
            field: 'pointId',
            type: 'string',
            operator: 'equal',
            value: id
          },
          {
            label: '',
            field: 'sourceDocType',
            type: 'string',
            operator: 'equal',
            value: 'serial_item_point'
          }
        ],
        serializeList
      },
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      }
    }
  },
  { title: i18n.t('附件') }
]
