<template>
  <div class="full-height r-d-container mt-flex">
    <div class="tree-view--wrap">
      <div class="trew-node--add">
        <div class="node-title">{{ $t('层级') }}</div>
      </div>
      <mt-common-tree
        v-if="treeViewData.dataSource.length"
        ref="treeView"
        class="tree-view--template"
        :un-button="true"
        :fields="treeViewData"
        :selected-nodes="selectedNodes"
        :expanded-nodes="expandedNodes"
        @nodeSelected="nodeSelected"
      />
    </div>
    <div class="table-container">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </div>
</template>

<script>
import { treeViewData, pageConfig, toolObj } from './config/attachment.js'
import { download } from '@/utils/utils'

export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      selectedNodes: [],
      expandedNodes: [],
      treeDataHash: {},
      treeViewData,
      pageConfig
    }
  },
  computed: {
    isDisabled() {
      // 非草稿禁止编辑
      return [2, 3].includes(Number(this.detailInfo?.status))
    }
  },
  mounted() {
    if (!this.isDisabled && !this.pageConfig[0].toolbar.includes('delete')) {
      this.pageConfig[0].toolbar.push('delete')
    }
    this.getFileFolderData()
  },
  methods: {
    // 获取侧边节点信息
    getFileFolderData() {
      let _params = {
        docId: this.$route.query.id
      }
      this.$API.seriesItem.getFileNodeList(_params).then((res) => {
        this.$set(this.treeViewData, 'dataSource', res.data)
        let treeDataHash = {}
        let recursiveQuery = function (children) {
          for (let item of children) {
            treeDataHash[item.id] = item
            if (Array.isArray(item.fileNodeResponseList) && item.fileNodeResponseList.length > 0) {
              recursiveQuery(item.fileNodeResponseList)
            }
          }
        }
        recursiveQuery(res.data)

        this.treeDataHash = treeDataHash
        if (Array.isArray(res.data) && res.data.length) {
          this.selectNodeId = res.data[0]['id']
          this.$set(this.pageConfig[0].grid, 'asyncConfig', {
            url: this.$API.seriesItem.getFileList,
            params: {
              docId: this.$route.query.id,
              parentId: this.selectNodeId
            },
            methods: 'get',
            recordsPosition: 'data'
          })
          this.selectedNodes = [res.data[0]['id']]
          this.expandedNodes = [res.data[0]['id']]
        }
      })
    },
    // 选择节点
    nodeSelected(event) {
      if (event?.nodeData?.id) {
        this.selectNodeId = event?.nodeData?.id
        if (this.treeDataHash[this.selectNodeId].nodeCode.substring(0, 3) == 'sup') {
          if (this.pageConfig[0].grid.columnData.length == 6) {
            this.pageConfig[0].grid.columnData.push({
              field: 'supplierName',
              headerText: this.$t('上传供应商')
            })
          }
          // 供方
          this.$set(this.pageConfig[0], 'toolbar', [toolObj['download']])
          this.$set(this.pageConfig[0].grid.columnData[1], 'cellTools', [toolObj['download']])
        } else {
          // 采方
          if (this.pageConfig[0].grid.columnData.length == 7) {
            this.pageConfig[0].grid.columnData.splice(-1, 1)
          }
          if (this.isDisabled) {
            this.$set(this.pageConfig[0], 'toolbar', [toolObj['download']])
            this.$set(this.pageConfig[0].grid.columnData[1], 'cellTools', [toolObj['download']])
          } else {
            if (event.nodeData.hasChildren == false) {
              const toolbar = [toolObj['upload'], toolObj['download']]
              this.$set(this.pageConfig[0], 'toolbar', toolbar)
            } else {
              this.$set(this.pageConfig[0], 'toolbar', [toolObj['download']])
            }
            if (!this.isDisabled) {
              this.pageConfig[0].toolbar.push(toolObj['delete'])
            }
            this.$set(this.pageConfig[0].grid.columnData[1], 'cellTools', [
              toolObj['download'],
              toolObj['delete']
            ])
          }
        }
        this.$set(this.pageConfig[0].grid, 'asyncConfig', {
          url: this.$API.seriesItem.getFileList,
          params: {
            docId: this.$route.query.id,
            parentId: this.selectNodeId
          },
          methods: 'get',
          recordsPosition: 'data'
        })
      }
    },
    // 工具栏按钮点击
    handleClickToolBar(e) {
      const _selectRows = e.gridRef.getMtechGridRecords()
      if (['download', 'delete'].includes(e.toolbar.id) && !_selectRows.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'upload') {
        this.$dialog({
          modal: () => import('COMPONENTS/Upload/index.vue'),
          data: {
            title: this.$t('上传')
          },
          success: (data) => {
            this.handleUploadFiles(data)
          }
        })
      } else if (e.toolbar.id == 'download') {
        for (let item of _selectRows) {
          this.$API.fileService.downloadPrivateFile({ id: item.sysFileId }).then((res) => {
            download({
              fileName: item.fileName,
              blob: new Blob([res.data])
            })
          })
        }
      } else if (e.toolbar.id == 'delete') {
        this.handleBatchDelete(_selectRows)
      }
    },
    // 单元格按钮点击
    handleClickCellTool(e) {
      if (e.tool.id == 'download') {
        if (e.data.sysFileId == -999) {
          this.$toast({
            content: this.$t('报价未截止不能下载'),
            type: 'warning'
          })
          return
        }

        this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId }).then((res) => {
          download({ fileName: e.data.fileName, blob: new Blob([res.data]) })
        })
      } else if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
    },
    // 单元格title文字点击
    handleClickCellTitle(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e?.data?.sysFileId || e?.data?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    // 上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, url, remoteUrl, sysName } = data
      let _params = {
        docId: this.$route.query.id,
        parentId: this.selectNodeId,
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        remoteUrl: remoteUrl,
        sysFileId: id,
        sysName: sysName,
        url: url
      }
      this.$API.seriesItem.saveFileList(_params).then(() => {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDelete(_selectIds)
    },
    //删除文件
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.rfxFiles.deleteSourcingFileById({ idList: ids }).then(() => {
            this.$store.commit('endLoading')
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.r-d-container {
  width: 100%;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .tree-view--wrap {
    min-width: 150px;
    background: #fff;
    margin-right: 10px;
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 50px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 #e8e8e8;
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 10px;
      padding-left: 20px;
    }
    /deep/.mt-commom-tree-view {
      padding-left: 0;
    }
    /deep/.mt-commom-tree-view .expandedLevel1 {
      background: transparent !important;
    }
  }
}
</style>
