<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { toolBar, columnData } from './config'

export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: toolBar,
          useToolTemplate: false,
          gridId: this.$permission.gridId['purchase']['seriesItem']['seriesItemPricing']['list'],
          grid: {
            allowFiltering: true,
            asyncConfig: {
              url: this.$API.seriesItem.itemApplyList
            },
            lineIndex: true,
            columnData: columnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    //新增
    handleAddFn() {
      this.$dialog({
        modal: () => import('./components/addDialog/index.vue'),
        data: {
          title: this.$t('创建定价')
        },
        success: (data) => {
          this.$router.push({
            name: 'series-item-pricing-detail',
            query: {
              id: data.id,
              decidePriceType: data.decidePriceType,
              status: data.status
            }
          })
        }
      })
    },

    //删除
    handleDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          ids: idList
        }
        this.$API.seriesItem.itemApplyDelete(params).then(() => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },

    //提交
    handleSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        if (selectGridRecords[0].status == 0 || selectGridRecords[0].status == 1) {
          let params = {
            id: selectGridRecords[0].id
          }
          this.$API.seriesItem.itemApplySubmit(params).then(() => {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        } else {
          this.$toast({
            content: this.$t('只草稿/已保存状态的定价单可提交'),
            type: 'warning'
          })
        }
      }
    },

    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAddFn() //新增
      } else if (e.toolbar.id == 'del') {
        this.handleDelFn(_selectGridRecords, idList) //删除
      } else {
        this.handleSubmitFn(_selectGridRecords) //提交
      }
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field == 'pointNo') {
        this.$router.push({
          name: 'series-item-pricing-detail',
          query: {
            id: e.data.id,
            decidePriceType: e.data.decidePriceType,
            status: e.data.status
          }
        })
      }
    }
  }
}
</script>
