import { i18n } from '@/main.js'
export const toolBar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'del', icon: 'icon_solid_Cancel', title: i18n.t('删除') },
  { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') }
]

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('已保存'),
        2: i18n.t('已提交'),
        3: i18n.t('审批通过'),
        4: i18n.t('审批驳回')
      }
    }
  },
  {
    field: 'pointNo',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'title',
    headerText: i18n.t('标题')
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价单类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('直接定价'),
        1: i18n.t('寻源结果定价'),
        2: i18n.t('系列物料定价')
      }
    }
  },
  {
    field: 'priceObjectName',
    headerText: i18n.t('定价对象')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]
