<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="appForm" :model="formData" :rules="formRules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="priceObjectName" :label="$t('定价对象')">
              <mt-select
                v-model="formData.priceObjectName"
                :data-source="[{ text: $t('系列物料定价'), value: $t('系列物料定价') }]"
                :placeholder="$t('请选择定价对象')"
                :disabled="true"
                :readonly="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="decidePriceType" :label="$t('定价单类型')">
              <mt-select
                v-model="formData.decidePriceType"
                :data-source="[{ text: $t('系列物料定价'), value: 2 }]"
                :placeholder="$t('请选择定价单类型')"
                :disabled="true"
                :readonly="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="title" :label="$t('标题')">
              <mt-input
                v-model="formData.title"
                :placeholder="$t('请填写标题')"
                :show-clear-button="true"
                maxlength="50"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="companyCode" :label="$t('公司')">
              <mt-select
                v-model="formData.companyCode"
                :data-source="companyNameSelectOptions"
                :placeholder="$t('请选择公司')"
                :show-clear-button="true"
                @select="companyNameChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="purId" :label="$t('采购员')">
              <debounce-filter-select
                v-model="formData.purId"
                :request="getCurrentEmployees"
                :data-source="currentEmployees"
                @change="personnelChange"
                :show-clear-button="false"
                :fields="{ text: 'text', value: 'uid' }"
                :placeholder="$t('请选择采购员')"
              ></debounce-filter-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="purOrgCode" :label="$t('采购组织')">
              <mt-select
                v-model="formData.purOrgCode"
                :data-source="purOrgNameSelectOptions"
                :placeholder="$t('请选择采购组织')"
                :show-clear-button="true"
                @select="purOrgNameChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="siteName" :label="$t('工厂')">
              <mt-select
                :data-source="factorySelectOptions"
                v-model="formData.siteName"
                :placeholder="$t('请选择工厂')"
                float-label-type="Never"
                @change="siteIdChange"
              ></mt-select>
              <!-- @select="factoryNameChange" -->
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="priceClassification" :label="$t('价格分类')">
              <mt-select
                v-model="formData.priceClassification"
                :data-source="priceClassificationSelectOptions"
                :placeholder="$t('请选择价格分类')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="pointType" :label="$t('定价类型')">
              <mt-select
                v-model="formData.pointType"
                :data-source="pointTypeOptions"
                :placeholder="$t('请选择定价类型')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="sourcingExpand" :label="$t('拓展')">
              <mt-multi-select
                :data-source="dataSource.sourcingExpandList"
                v-model="formData.sourcingExpand"
                :item-template="selectItemTemplate"
                :show-clear-button="false"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('扩展')"
                @change="handleExpand"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="remark" :label="$t('备注')">
              <mt-input v-model="formData.remark"></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import ItemTemplate from './ItemTemplate'
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
export default {
  components: {
    debounceFilterSelect
  },
  data() {
    return {
      selectItemTemplate: () => {
        return {
          template: ItemTemplate
        }
      },
      currentEmployees: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveRule,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      purOrgNameSelectOptions: [],
      factorySelectOptions: [],
      companyNameSelectOptions: [],
      priceClassificationSelectOptions: [
        { text: this.$t('基价'), value: 1 },
        { text: this.$t('SRM价格'), value: 2 },
        { text: this.$t('暂估价格'), value: 3 },
        { text: this.$t('执行价格'), value: 4 }
      ],

      pointTypeOptions: [
        { text: this.$t('新品'), value: 'new_products' },
        { text: this.$t('二次'), value: 'second_inquiry' },
        { text: this.$t('已有'), value: 'exist' }
      ],
      formData: {
        companyName: '', // 公司
        companyCode: '',
        companyId: '',
        priceObjectName: this.$t('系列物料定价'), // 定价对象
        purOrgName: '', // 	采购组织
        purOrgCode: '',
        purOrgId: '',
        decidePriceType: 2, //定价单类型
        // id: "",
        // pointNo: "", //定价单号
        remark: '',
        title: '',
        purId: '', //采购员
        purName: '',
        status: 0,
        priceClassification: '',
        pointType: '',
        sourcingExpand: '',
        expandSaveRequestList: [],
        siteName: '',
        siteCode: '',
        siteId: ''
      },
      formRules: {
        //定价单类型
        decidePriceType: {
          required: true,
          message: this.$t('请输入定价单类型'),
          trigger: 'blur'
        },
        // 采购员
        purId: {
          required: true,
          message: this.$t('请输入采购员'),
          trigger: 'blur'
        },
        // 标题
        title: {
          required: true,
          message: this.$t('请输入标题'),
          trigger: 'blur'
        },
        // 公司
        companyCode: {
          required: true,
          message: this.$t('请输入公司'),
          trigger: 'blur'
        },
        // 	采购组织
        purOrgCode: {
          required: true,
          message: this.$t('请输入采购组织'),
          trigger: 'blur'
        },
        // 定价对对像
        priceObjectName: {
          required: true,
          message: this.$t('请输入定价对象'),
          trigger: 'blur'
        },
        priceClassification: {
          required: true,
          message: this.$t('请选择价格分类'),
          trigger: 'blur'
        },
        PricingType: {
          required: true,
          message: this.$t('请选择定价类型'),
          trigger: 'blur'
        },
        siteName: {
          required: true,
          message: this.$t('请输入工厂'),
          trigger: 'blur'
        },
        pointType: {
          required: true,
          message: this.$t('请输入定价类型'),
          trigger: 'blur'
        }
      },
      dataSource: {
        sourcingExpandList: []
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  beforeMount() {},
  mounted() {
    this.getCurrentEmployees({ text: '' })
    this.getCompanyNameSelectOptions()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 扩展
    async changeTaxItem(siteCode) {
      const res = await this.$API.rfxDetail
        .purAllOrgWithSite({ fuzzyParam: siteCode, purOrgCode: this.formData.purOrgCode })
        .catch(() => {})
      if (res && res.data) {
        let dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            dataSource.push({
              text:
                v.companyCode +
                '-' +
                v.businessOrganizationCode +
                '-' +
                v.businessOrganizationName +
                '+' +
                x.orgCode +
                '-' +
                x.orgName,
              value: v.companyCode + '+' + v.businessOrganizationCode + '+' + x.orgCode,
              data: {
                companyCode: v.companyCode,
                companyId: v.companyId,
                companyName: v.companyName,
                purOrgCode: v.businessOrganizationCode,
                purOrgId: v.id,
                purOrgName: v.businessOrganizationName,
                siteCode: x.orgCode,
                siteId: x.id,
                siteName: x.orgName
              }
            })
          })
        })
        this.dataSource.sourcingExpandList = dataSource
      }
    },
    handleExpand(e) {
      let expandSaveRequestList = []
      e.value.forEach((v) => {
        this.dataSource.sourcingExpandList.forEach((x) => {
          if (v == x.value) {
            expandSaveRequestList.push(x.data)
          }
        })
      })
      this.formData.expandSaveRequestList = expandSaveRequestList
    },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.currentEmployees = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.currentEmployees = tmp
        if (fuzzyName == '') {
          this.formData.purId = this.currentEmployees[0].uid
        }
      })
    },
    personnelChange(e) {
      this.formData.purName = e.itemData.employeeName
    },
    purOrgNameChange(e) {
      this.formData.purOrgName = e.itemData.data.purOrgName
      this.formData.purOrgId = e.itemData.data.purOrgId
      // this.formData.siteName = "";
      // this.formData.siteCode = "";
      // this.formData.siteId = "";
      this.getpermissionSiteList()
    },
    siteIdChange(e) {
      let _data = e.itemData
      this.formData.siteCode = _data.data.siteCode
      this.formData.siteId = _data.data.siteId
      this.formData.siteName = _data.data.siteName
      this.changeTaxItem(this.formData.siteCode)
    },
    getpermissionSiteList() {
      if (this.formData.companyId && this.formData.purOrgId) {
        this.$API.masterData
          .permissionSiteList({
            companyId: this.formData.companyId,
            buOrgId: this.formData.purOrgId,
            orgLevelTypeCode: 'ORG06'
          })
          .then((res) => {
            this.factorySelectOptions = res?.data.map((item) => ({
              text: item.orgCode + '-' + item.orgName,
              value: item.orgName,
              data: {
                siteName: item.orgName,
                siteId: item.id,
                siteCode: item.orgCode
              }
            }))
          })
      }
    },
    companyNameChange(e) {
      this.formData.companyName = e.itemData.data.companyName
      this.formData.companyId = e.itemData.data.companyId
      this.formData.companyCode = e.itemData.data.companyCode
      this.formData.purOrgName = ''
      this.formData.purOrgCode = ''
      this.formData.purOrgId = ''
      this.formData.siteName = ''
      this.formData.siteCode = ''
      this.formData.siteId = ''
      this.getPurOrgNameSelectOptions(e.itemData.data.companyId)
    },

    async saveRule() {
      const validate = await this.asyncFormValidate('appForm')
      if (!validate) {
        return
      }
      this.$API.seriesItem.itemApplySave(this.formData).then((res) => {
        this.emitConfirm(res.data)
      })
    },

    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    emitConfirm(data) {
      this.$emit('confirm-function', data)
    },

    //采购组织
    getPurOrgNameSelectOptions(companyId) {
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.purOrgNameSelectOptions = res.data.map((item) => ({
            text: item.organizationCode + '-' + item.organizationName,
            value: item.organizationCode,
            data: {
              purOrgName: item.organizationName,
              purOrgCode: item.organizationCode,
              purOrgId: item.id
            }
          }))
        })
    },
    //获取公司
    getCompanyNameSelectOptions() {
      this.$API.masterData.permissionCompanyList().then((res) => {
        this.companyNameSelectOptions = res?.data.map((item) => ({
          text: item.orgCode + '-' + item.orgName,
          value: item.orgCode,
          data: {
            companyCode: item.orgCode,
            companyName: item.orgName,
            companyId: item.id
          }
        }))
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
