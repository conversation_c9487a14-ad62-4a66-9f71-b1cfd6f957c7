<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import MixIn from 'ROUTER_PURCHASE_RFX/list/config/mixin'
import { pageConfig } from 'ROUTER_PURCHASE_RFX/list/config/index'
export default {
  mixins: [MixIn],
  data() {
    return {
      pageConfig: pageConfig(this.$API.rfxList.getRFXList, 'bidding_price'),
      biddingSource: 'bidding_price' //竞价
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
