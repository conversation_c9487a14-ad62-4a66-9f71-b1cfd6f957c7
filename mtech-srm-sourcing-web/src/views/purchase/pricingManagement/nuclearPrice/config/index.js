import { i18n } from '@/main.js'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'submit', icon: 'icon_solid_Submit', title: i18n.t('提交') }
  // { id: 'export', icon: 'icon_solid_Download ', title: i18n.t('导出') }
]

const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'priceNo',
    headerText: i18n.t('核价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'oaUrl',
    headerText: i18n.t('OA审批查看链接'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == '0' || e == '3') {
          return '--'
        } else {
          return i18n.t('OA审批查看')
        }
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('审批'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        4: i18n.t('审批废弃')
      }
    }
  },
  {
    field: 'supplierCode',
    // width: "200",
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    // width: "200",
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'isNotHvac',
    // width: "200",
    headerText: i18n.t('是否暖通')
  },
  {
    field: 'createUserName',
    // width: "200",
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    // width: "200",
    headerText: i18n.t('创建日期')
  },
  {
    field: 'remark',
    // width: "200",
    headerText: i18n.t('备注')
  }
]
export const pageConfig = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      asyncConfig: {
        url: '/sourcing/tenant/setPriceHeader/queryBuilder',
        params: {},
        serializeList: (list) => {
          list.forEach((e) => {
            e.oaUrl = e.status
          })
          return list
        }
      }
    }
  }
]
