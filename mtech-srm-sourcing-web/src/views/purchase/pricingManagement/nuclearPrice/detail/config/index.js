import { i18n } from '@/main.js'
import Vue from 'vue'
import material from '../components/material.vue' // 物料选择
import selectFactory from '../components/selectFactory' // 工厂下拉
import organization from '../components/organization.vue' // 采购组织下拉
import supplierSelect from '../components/supplierSelect.vue' // 采购组织下拉
import cellChanged from '../components/cellChanged.vue'

export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'edit', icon: 'icon_list_edit', title: i18n.t('编辑') }
]
export const columnData = (orgParame) => {
  return [
    {
      width: '50',
      type: 'checkbox',
      allowEditing: false
    },
    // {
    //   field: "companyName",
    //   headerText: i18n.t("公司"),
    //   allowEditing: false,
    // },
    {
      field: 'purOrgName',
      headerText: i18n.t('采购组织'),
      allowEditing: true,
      orgParame: orgParame,
      editTemplate: () => {
        return {
          template: organization
        }
      }
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂'),
      allowEditing: true,
      orgParame: orgParame,
      editTemplate: () => {
        return {
          template: selectFactory
        }
      }
    },
    {
      field: 'siteCode',
      // width: "0",
      headerText: i18n.t('工厂编码'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      allowEditing: true,
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ $t('物料编码') }}</span>
              </div>
            `
          })
        }
      },
      editTemplate: () => {
        return {
          template: material
        }
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: true,
      orgParame: orgParame,
      editTemplate: () => {
        return {
          template: supplierSelect
        }
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'formula',
      headerText: i18n.t('公式'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    // {
    //   field: "formulaQuotaInfo",
    //   headerText: i18n.t("公式定额"),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged,
    //     };
    //   },
    // },
    {
      field: 'currencyName',
      headerText: i18n.t('币种'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'taxid',
      headerText: i18n.t('税率'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'unitName',
      headerText: i18n.t('单位'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'priceUnit',
      headerText: i18n.t('价格单位'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'taxedBasePrice',
      headerText: i18n.t('含税基价'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'taxedAveragePrice',
      headerText: i18n.t('含税均价'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'executeTaxedUnitPrice',
      headerText: i18n.t('含税执行价'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'executeUntaxedUnitPrice',
      headerText: i18n.t('未税执行价'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'historyLowPrice',
      headerText: i18n.t('历史最低价'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'priceDifference',
      headerText: i18n.t('价格差异'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    // {
    //   field: "pricingTemplate",
    //   headerText: i18n.t("核价模板"),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged,
    //     };
    //   },
    // },
    {
      field: 'purName',
      headerText: i18n.t('采购员'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('价格属性'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'planDeliveryDate',
      headerText: i18n.t('计划交货时间'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'priceValidDateStart',
      headerText: i18n.t('有效期从'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'priceValidDateEnd',
      headerText: i18n.t('有效期至'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    // {
    //   field: "buyerOrgName",
    //   headerText: i18n.t("采购组"),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged,
    //     };
    //   },
    // },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    }
  ]
}
export const delList = {
  categoryCode: null, //品类编码
  categoryName: null, //品类名称
  // supplierCode: null, //供应商编码
  // supplierName: null, //供应商名称
  formula: null, //公式
  formulaQuotaInfo: null, //公式定额
  currencyName: null, //币种
  taxid: null, //税率
  unitName: null, //单位
  priceUnit: null, //价格单位
  taxedBasePrice: null, //含税基价
  taxedAveragePrice: null, //含税均价
  executeTaxedUnitPrice: null, //含税执行价
  executeUntaxedUnitPrice: null, //未税执行价
  historyLowPrice: null, //历史最低价
  priceDifference: null, //价格差异
  pricingTemplate: null, //核价模板
  purName: null, //采购员
  quoteAttribute: null, //价格属性
  planDeliveryDate: null, //计划交货时间
  priceValidDateStart: null, //有效期从
  priceValidDateEnd: null, //有效期至
  buyerOrgName: null, //采购组
  remark: null //备注
}
