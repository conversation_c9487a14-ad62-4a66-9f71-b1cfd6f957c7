<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap"></div>
        <div class="btns-wrap">
          <mt-button class="e-flat" @click="clickButtonSave">{{ $t('保存') }}</mt-button>
          <mt-button class="e-flat" @click="clickButtonSubmit">{{ $t('提交') }}</mt-button>
          <mt-button class="e-flat" @click="clickButtonDelete">{{ $t('删除') }}</mt-button>
          <mt-button class="e-flat" @click="$router.push('pricingManagement-nuclearPrice')">{{
            $t('返回')
          }}</mt-button>
        </div>
      </div>
      <div class="formInput">
        <mt-form ref="dialogRef" class="dialogRef" :model="formObject">
          <mt-form-item prop="priceNo" :label="$t('核价单号')">
            <mt-input
              v-model="formObject.priceNo"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('核价单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司')">
            <mt-select
              v-model="formObject.companyCode"
              float-label-type="Never"
              :data-source="companySelect"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              @change="changeOrgNameSelect"
              :disabled="OrgDisabled"
              :placeholder="$t('请选择公司')"
            ></mt-select>
          </mt-form-item>
          <!-- <mt-form-item prop="supplierCode" :label="$t('供应商')">
            <mt-select
              v-model="formObject.supplierCode"
              float-label-type="Never"
              :data-source="supplierArr"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :allow-filtering="true"
              :show-clear-button="true"
              :filtering="filteringsupplierCode"
              @change="changesupplierSelect"
              :placeholder="$t('请选择供应商')"
            ></mt-select>
          </mt-form-item> -->
          <mt-form-item prop="status" :label="$t('状态')">
            <mt-input
              v-model="formObject.status"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('状态')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="createUserName" :label="$t('创建人')">
            <mt-input
              v-model="formObject.createUserName"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('创建人')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建日期')">
            <mt-input
              v-model="formObject.createTime"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('创建日期')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="contractType" :label="$t('合同类型')">
            <mt-select
              v-model="formObject.contractType"
              float-label-type="Never"
              :data-source="contractTypeArr"
              :placeholder="$t('请选择合同类型')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="isNotHvac" :label="$t('是否暖通')">
            <mt-select
              v-model="formObject.isNotHvac"
              float-label-type="Never"
              :data-source="isNotHvacArr"
              :placeholder="$t('是否暖通')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
        <mt-form ref="dialogRef1" :model="formObject">
          <mt-form-item prop="remark" :label="$t('备注')">
            <mt-input
              v-model="formObject.remark"
              float-label-type="Never"
              maxlength="200"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </nav>

    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @selectedChanged="selectedChanged"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    ></mt-template-page>
  </div>
</template>
<script>
import { toolbar, columnData } from './config/index'
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              // showDeleteConfirmDialog: true,
              newRowPosition: 'Top'
            },
            columnData: columnData(this.siteSelect),
            dataSource: [],
            queryCellInfo: this.customiseCell,
            class: 'pe-edit-grid custom-toolbar-grid'
          }
        }
      ],
      formObject: {
        priceNo: ''
      },
      //公司下拉框
      companySelect: [],
      //工厂下拉
      siteSelect: [],
      //供应商
      supplierArr: [],
      //合同类型
      contractTypeArr: [],
      //是否暖通
      isNotHvacArr: [
        { text: this.$t('否'), value: '0' },
        { text: this.$t('是'), value: '1' }
      ],
      //进入的行信息
      actionBeginRowData: {},
      routeId: '',
      OrgDisabled: false
    }
  },
  watch: {
    pageConfig: {
      handler(v) {
        if (v[0].grid.dataSource.length > 0) {
          this.OrgDisabled = true
        } else {
          this.OrgDisabled = false
        }
      },
      deep: true
    }
  },
  mounted() {
    this.routeId = this.$route?.query?.id ?? ''
    this.filteringsupplierCode = utils.debounce(this.filteringsupplierCode, 1000)

    //新增
    if (this.routeId == '') {
      this.initialInterfaceCall()
      this.callHeaderselectInterface()
    }
    //编辑
    else {
      this.editortochoose()
    }
  },
  methods: {
    //请求头部下拉框的值
    callHeaderselectInterface() {
      //公司
      let parameter = {
        fuzzyParam: '',
        organizationLevelCodes: ['ORG02'],
        orgType: 'ORG001PRO'
      }
      this.$API.configuration.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
        this.companySelect = res.data
      })
      //合同模板
      this.$API.nuclearPrice.initContractPrint().then((res) => {
        for (const key in res.data) {
          this.contractTypeArr.push({
            text: res.data[key],
            value: key
          })
        }
      })
    },
    //编辑
    async editortochoose() {
      let parameter = {
        requestId: this.$route.query.id
      }
      let data = null
      await this.$API.nuclearPrice.queryDetail(parameter).then((res) => {
        data = res.data
      })
      await this.callHeaderselectInterface()
      await this.assignmentOperation(data)
    },
    assignmentOperation(data) {
      let {
        companyCode,
        companyName,
        companyId,
        contractType,
        createTime,
        createUserName,
        isNotHvac,
        priceNo,
        remark,
        supplierCode,
        supplierName,
        tenantId,
        setPriceItemRequestList
      } = data
      this.formObject = {
        companyCode,
        companyName,
        companyId,
        contractType,
        createTime,
        createUserName,
        isNotHvac: String(isNotHvac),
        priceNo,
        remark,
        tenantId
      }
      let parameter = {
        organizationCode: companyCode,
        fuzzyNameOrCode: supplierName
      }
      this.$API.configuration.criteriaQuery(parameter).then((res) => {
        this.supplierArr = res.data
        this.formObject.supplierCode = supplierCode
        this.formObject.supplierName = supplierName
      })
      this.$set(
        this.pageConfig[0].grid,
        'columnData',
        columnData({ id: companyId, organizationCode: companyCode })
      )
      // let params = {
      //   parentId: companyId,
      //   tenantId: 10000,
      // };
      // this.$API.configuration.findSiteInfoByParentId(params).then((res) => {
      //   this.siteSelect = res.data;

      // });
      this.$set(this.pageConfig[0].grid, 'dataSource', setPriceItemRequestList)
    },
    //新增
    initialInterfaceCall() {},
    //公司下拉
    changeOrgNameSelect(e) {
      if (!this.routeId) {
        let id = e.itemData.id
        this.formObject.companyId = id
        this.formObject.companyName = e.itemData.orgName
        this.formObject.companyCode = e.itemData.orgCode
        this.formObject.supplierName = null
        this.formObject.supplierCode = null
        this.$set(
          this.pageConfig[0].grid,
          'columnData',
          columnData({ id: id, organizationCode: e.itemData.orgCode })
        )
        // let parameter = {
        //   parentId: id,
        //   tenantId: 10000,
        // };
        // this.$API.configuration
        //   .findSiteInfoByParentId(parameter)
        //   .then((res) => {
        //     this.siteSelect = res.data;
        //     this.$set(
        //       this.pageConfig[0].grid,
        //       "columnData",
        //       columnData({id:id,organizationCode:e.itemData.orgCode})
        //     );
        //   });
        // //供应商
        // let data = {
        //   organizationCode: e.itemData.orgCode,
        //   fuzzyNameOrCode: "",
        // };
        // this.$API.configuration.criteriaQuery(data).then((res) => {
        //   this.supplierArr = res.data;
        // });
      }
    },
    // //供应商
    // changesupplierSelect(e) {
    //   this.formObject.supplierName = e.itemData.supplierName;
    //   this.formObject.supplierCode = e.itemData.supplierCode;
    // },
    // filteringsupplierCode(e) {
    //   //供应商
    //   let data = {
    //     organizationCode: this.formObject.companyCode,
    //     fuzzyNameOrCode: e?.text ?? "",
    //   };
    //   this.$API.configuration.criteriaQuery(data).then((res) => {
    //     this.supplierArr = res.data;
    //   });
    // },
    //删除
    clickButtonDelete() {
      console.log('删除')
    },
    //保存
    clickButtonSave() {
      const grid = this.$refs.tepPage.getCurrentTabRef().grid
      let dataSource = grid.getCurrentViewRecords()
      if (!this.formObject.companyName) {
        this.$toast({
          content: this.$t('公司不能为空'),
          type: 'warning'
        })
        return
      }
      if (dataSource.length < 1) {
        this.$toast({
          content: this.$t('列表不能为空'),
          type: 'warning'
        })
        return
      }
      let parameter = {
        ...this.formObject,
        button: '0',
        id: this.routeId,
        deleteIds: [],
        setPriceItemRequestList: [...dataSource]
      }
      console.log(parameter)
      this.$API.nuclearPrice.saveSetPriceHeader(parameter).then(() => {
        this.$router.push({
          name: `pricingManagement-nuclearPrice`
        })
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    //提交
    clickButtonSubmit() {
      const grid = this.$refs.tepPage.getCurrentTabRef().grid
      let dataSource = grid.getCurrentViewRecords()
      if (!this.formObject.companyName) {
        this.$toast({
          content: this.$t('公司不能为空'),
          type: 'warning'
        })
        return
      }
      if (dataSource.length < 1) {
        this.$toast({
          content: this.$t('列表不能为空'),
          type: 'warning'
        })
        return
      }
      let parameter = {
        ...this.formObject,
        button: '1',
        id: this.routeId,
        deleteIds: [],
        setPriceItemRequestList: [...dataSource]
      }
      console.log(parameter)
      this.$API.nuclearPrice.saveSetPriceHeader(parameter).then(() => {
        this.$router.push({
          name: `pricingManagement-nuclearPrice`
        })
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    //点击表头-新增/删除
    handleClickToolBar(e) {
      const _selectRows = e.gridRef.getMtechGridRecords()
      if (_selectRows.length <= 0 && e.toolbar.id == 'delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        if (!this.formObject.companyId) {
          this.$toast({ content: this.$t('请选择公司'), type: 'warning' })
          return
        } else {
          // let _dataSource = utils.cloneDeep(this.pageConfig[0].grid.dataSource);
          // _dataSource.unshift(listData);

          // _dataSource.map((item, i) => {
          //   item.index = i;
          // });
          // this.$set(this.pageConfig[0].grid, "dataSource", _dataSource);
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        }
        // this.$dialog({
        //   modal: () =>
        //     import(
        //       /* webpackChunkName: "router/purchase/pricingManagement/nuclearPrice/detail/components/AddDialog" */ "./components/AddDialog.vue"
        //     ),
        //   data: {
        //     title: this.$t("新增"),
        //     headStates: "Add",
        //     formObject: this.formObject,
        //   },
        //   success: (listData) => {
        //     let _dataSource = utils.cloneDeep(
        //       this.pageConfig[0].grid.dataSource
        //     );
        //     _dataSource.unshift(...listData);
        //     _dataSource.map((item, i) => {
        //       item.index = i;
        //     });
        //     this.$set(this.pageConfig[0].grid, "dataSource", _dataSource);
        //     this.$refs.tepPage.refreshCurrentGridData();
        //   },
        // });
      }
      if (e.toolbar.id == 'delete') {
        this.handleClickToolBarDelete(_selectRows)
      }
      if (e.toolbar.id == 'edit') {
        this.handleClickToolBarEdit(_selectRows)
      }
    },
    //删除
    handleClickToolBarDelete() {
      // 弹框
      // const grid = this.$refs.tepPage.getCurrentTabRef().grid;
      // let _dataSource = grid.getCurrentViewRecords();
      // let set = _selectRows.map((item) => item.index);
      // let resArr = _dataSource.filter((item) => !set.includes(item.index));
      // this.$set(this.pageConfig[0].grid, "dataSource", resArr);
      //------行内
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
    },
    //编辑
    handleClickToolBarEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/pricingManagement/nuclearPrice/detail/components/AddDialog" */ './components/AddDialog.vue'
          ),
        data: {
          title: this.$t('编辑'),
          headStates: 'edit',
          formObject: this.formObject,
          selectRows: _selectRows[0]
        },
        success: (listData) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid.dataSource)
          _dataSource = _dataSource.map((item) =>
            item.index === listData[0].index ? listData[0] : item
          )
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          console.log(this.pageConfig[0].grid.dataSource)
          // this.$refs.tepPage.refreshCurrentGridData();
        }
      })
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      } else {
        args.cell.classList.add('bg-orange')
      }
    },
    //监听下拉框
    selectedChanged(e) {
      let { fieldCode, itemInfo } = e
      if (fieldCode == 'siteCode') {
        this.actionBeginRowData.siteCode = itemInfo.siteCode
        this.actionBeginRowData.siteName = itemInfo.siteName
        // let _nuclearPriceObj = JSON.parse(
        //   sessionStorage.getItem("nuclearPriceObj")
        // ); //formObject 的值
        // _nuclearPriceObj.siteCode = itemInfo.siteCode;
        // _nuclearPriceObj.organizationId = itemInfo.organizationId;
        // _nuclearPriceObj.siteName = itemInfo.siteName;
        // sessionStorage.setItem(
        //   "nuclearPriceObj",
        //   JSON.stringify(_nuclearPriceObj)
        // );
      }
    },
    //进入
    actionBegin(e) {
      if (e.requestType == 'beginEdit') {
        // this.actionBeginRowData = e.rowData;
        console.log(e.rowData)
      }
    },
    //离开
    actionComplete(e) {
      if (e.requestType == 'save') {
        const grid = this.$refs.tepPage.getCurrentTabRef().grid
        let _dataSource = grid.getCurrentViewRecords()
        console.log(_dataSource)
        // _dataSource.forEach((item, index) => {
        //   console.log(index, this.actionBeginRowData.index);
        //   if (index == this.actionBeginRowData.index) {
        //     item.siteCode = this.actionBeginRowData.siteCode;
        //     item.siteName = this.actionBeginRowData.siteName;
        //   }
        // });
        // this.$set(this.pageConfig[0].grid, "dataSource", _dataSource);
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.tepPage.getCurrentTabRef()
      _current?.grid.endEdit()
    }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  height: 100%;
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  nav {
    flex-shrink: 0;
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
        // .pack-up {
        //   display: inline-block;
        //   position: relative;
        //   .rotate {
        //     position: absolute;
        //     right: -5px;
        //     top: 5px;
        //     transform: rotate(180deg);
        //     .mt-icons {
        //       color: #c8d5e9;
        //     }
        //   }
        // }
      }
    }
    //表单
    .formInput {
      width: 100%;
      display: flex;
      flex-direction: column;
      // .mt-form {
      //   width: 100%;
      //   padding: 20px;
      //   box-sizing: border-box;
      //   display: flex;
      //   align-items: center;
      //   // justify-content: space-between;
      //   justify-content: flex-start;
      //   flex-wrap: wrap;
      //   .mt-form-item {
      //     width: 360px;
      //   }
      //   .mt-form-item:nth-last-of-type(1) {
      //     width: 100%;
      //   }
      // }
      .dialogRef {
        width: 100%;
        display: grid;
        justify-content: space-between;
        flex-wrap: wrap;
        grid-template-columns: repeat(auto-fill, 300px);
        grid-gap: 10px;
        .mt-form-item {
          width: 300px;
        }
      }
    }
  }
  .mt-template-page {
    flex: 1;
    height: 100%;
  }
}
/deep/.mt-icons {
  line-height: 2 !important;
}
</style>
