<template>
  <div
    :class="['cell-changed', 'cell-changed-disabled' && !data.column.allowEditing]"
    id="cell-changed"
  >
    <mt-input
      :id="data.column.field"
      style="display: none"
      :value="data[data.column.field]"
    ></mt-input>
    <mt-input v-model="data[data.column.field]" disabled></mt-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      fieldName: ''
    }
  },
  created() {},
  mounted() {
    var that = this
    this.fieldName = this.data.column.field
    console.log(this.fieldName, 'this.fieldName')
    this.$bus.$off(`${this.fieldName}Change`) // 为了解决textWrapper拿不到的问题
    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      console.log('￥emit的监听到了被展示的数据------', this.fieldName, txt)
      // that.data[_field] = txt;
      that.$set(that.data, this.fieldName, txt)
    })
  }
}
</script>

<style lang="scss" scoped>
#cell-changed /deep/ .e-input.e-disabled {
  height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
  padding-left: 10px !important;
  background: #f5f5f5 !important;
}
</style>
