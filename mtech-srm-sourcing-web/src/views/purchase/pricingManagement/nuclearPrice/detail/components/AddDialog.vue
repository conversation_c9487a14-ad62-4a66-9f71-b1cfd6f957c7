<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="dialog-main"
      :buttons="buttons"
      :header="headers"
      @beforeClose="cancel"
    >
      <div class="dialog-content" v-show="!dialogShow">
        <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
          <mt-form-item prop="siteName" :label="$t('工厂名称')">
            <mt-select
              v-model="formObject.siteCode"
              float-label-type="Never"
              :data-source="siteSelect"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              :disabled="weight <= 1"
              @change="changesiteSelect"
              :placeholder="$t('请先选择公司')"
            ></mt-select>
          </mt-form-item>
          <!-- <mt-form-item v-else prop="siteName" :label="$t('工厂名称')">
            <mt-input
              v-model="formObject.siteName"
              float-label-type="Never"
              :disabled="weight <= 1 || headStates == 'edit'"
              :placeholder="$t('请先选择公司')"
            ></mt-input>
          </mt-form-item> -->
          <mt-form-item prop="itemCode" :label="$t('物料名称')">
            <mt-input
              :width="370"
              style="display: inline-block"
              v-model="formObject.itemName"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('请选择物料')"
            ></mt-input>
            <mt-icon
              :class="[weight == 3 ? '' : 'allowed']"
              style="width: 20px; margin-left: 10px"
              name="icon_input_search"
              @click.native="showDialog"
            ></mt-icon>
          </mt-form-item>
          <!-- <mt-form-item v-else prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="formObject.itemName"
              float-label-type="Never"
              :disabled="weight <= 1 || headStates == 'edit'"
              :placeholder="$t('请先选择工厂')"
            ></mt-input>
          </mt-form-item> -->
        </mt-form>
      </div>
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
      >
        <!-- @recordDoubleClick="recordDoubleClick"双击物料确认数据 -->
      </mt-template-page>
    </mt-dialog>
  </div>
</template>
<script>
import { pageConfig } from './config/index'
import { listData } from './config/moduls'
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        siteName: '', // 工厂名称
        itemName: '' //物料名称
      },
      //必填项
      formRules: {
        siteName: [
          {
            required: true,
            message: this.$t('工厂名称'),
            trigger: 'blur'
          }
        ],
        itemName: [
          {
            required: true,
            message: this.$t('物料名称'),
            trigger: 'blur'
          }
        ]
      },
      //工厂下拉框
      siteSelect: [],
      //物料下拉框
      itemSelect: [],
      weight: 1, //权重值判断那些下拉框可以编辑
      //--------------物料弹框
      headerTxt: this.$t('选择物料'),
      dialogShow: false,
      pageConfig: pageConfig,
      listData: listData
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    topObj() {
      return this.modalData.formObject
    },
    selectRows() {
      return this.modalData.selectRows
    },
    headStates() {
      return this.modalData.headStates
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.headers = this.header
    if (this.headStates == 'Add') {
      this.initialCallInterface()
    } else if (this.headStates == 'edit') {
      this.editCallInterface()
    }
  },
  methods: {
    //新增调用接口
    initialCallInterface() {
      let parameter = {
        parentId: this.topObj.companyId,
        tenantId: 10000
      }
      this.weight = 2 //让工厂下拉框可以选择
      this.$API.configuration.findSiteInfoByParentId(parameter).then((res) => {
        this.siteSelect = res.data
      })
    },
    //编辑
    async editCallInterface() {
      await this.initialCallInterface()
      await this.formObjFunction()
    },
    // 编辑 -赋值
    formObjFunction() {
      this.formObject.siteName = this.selectRows.siteName
      this.formObject.siteCode = this.selectRows.siteCode
      this.formObject.itemName = this.selectRows.itemName
      this.formObject.itemCode = this.selectRows.itemCode
      let id = this.selectRows.siteId
      console.log(this.selectRows)
      this.pageConfigAsyncConfig(id)
    },
    //编辑--调用物料接口
    pageConfigAsyncConfig(id) {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
        // recordsPosition: "data", 更改指向
        params: {
          condition: 'and',
          tenantId: '10000',
          defaultRules: [
            {
              field: 'organizationId',
              operator: 'equal',
              type: 'long',
              value: id
            }
          ]
        },
        serializeList: (list) => {
          return list.map((item) => {
            return {
              ...item,
              categoryCode: item?.categoryResponse?.categoryCode ?? ''
            }
          })
        }
      })
      this.weight = 3
    },
    //工厂下拉框事件
    changesiteSelect(e) {
      if (this.headStates == 'Add') {
        // console.log(e);
        let id = e.itemData.organizationId
        this.formObject.organizationId = id
        this.formObject.siteCode = e.itemData.siteCode
        this.formObject.siteName = e.itemData.siteName
        this.formObject.itemName = ''
        this.formObject.itemCode = ''
        this.pageConfigAsyncConfig(id)
      }
    },
    //物料---弹框显示
    showDialog() {
      console.log('哈哈哈')
      if (this.weight == '3') {
        this.dialogShow = true
        this.headers = this.$t('请选择物料/品项编码')
        this.buttons = [
          {
            click: this.itemCancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.itemConfirm,
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
      }
    },
    //点击确认
    confirm() {
      let _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      this.queryByCompanyAndRelfunction(_records[0])
    },
    //物料确认
    itemConfirm() {
      let _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      this.formObject.itemCode = _records[0].itemCode
      this.formObject.itemName = _records[0].itemName
      this.formObject.categoryCode = _records[0].categoryCode
      this.itemCancel()
    },
    queryByCompanyAndRelfunction(data) {
      console.log(data)
      let parameter = {
        itemCode: data?.itemCode ?? this.selectRows.itemCode,
        siteCode: this.formObject.siteCode,
        siteId: this.formObject.organizationId
      }
      this.$API.nuclearPrice.queryQuotaPrice(parameter).then((res) => {
        let listData = [res.data]
        if (this.headStates == 'edit') {
          listData[0].index = this.selectRows.index
        }
        this.$emit('confirm-function', listData)
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    },
    //物料取消
    itemCancel() {
      this.dialogShow = false
      this.newdialogheader()
    },
    newdialogheader() {
      this.headers = this.$t('新建')
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  min-height: 350px;
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
          .mt-icons {
            cursor: pointer;
          }
          .allowed {
            cursor: no-drop;
          }
        }
      }
    }
    .common-template-page {
      height: 100%;
      .e-gridcontent {
        max-height: 340px;
      }
      .mt-pagertemplate {
        overflow: inherit !important;
      }
    }
  }
}
</style>
