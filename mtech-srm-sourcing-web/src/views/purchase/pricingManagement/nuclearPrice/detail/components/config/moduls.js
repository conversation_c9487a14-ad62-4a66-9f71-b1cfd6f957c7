export let listData = [
  {
    companyName: '公司', //公司
    purOrgName: '采购组织', //采购组织
    siteName: '惠州王牌内销工厂', //工厂名称
    siteCode: '1010', //工厂编码
    categoryCode: '品类编码', //品类编码
    categoryName: '品类名称', //品类名称
    itemCode: 'AMD9900', //物料编码
    itemName: 'AMD-9900', //物料名称
    supplierCode: '供应商编码', //供应商编码
    supplierName: '供应商名称', //供应商名称
    formula: '公式', //公式
    formulaQuotaInfo: '公式定额', //公式定额
    currencyName: '币种', //币种
    taxid: '税率', //税率
    unitName: '单位', //单位
    priceUnit: '价格单位', //价格单位
    taxedBasePrice: '含税基价', //含税基价
    taxedAveragePrice: '含税均价', //含税均价
    executeTaxedUnitPrice: '含税执行价', //含税执行价
    executeUntaxedUnitPrice: '未税执行价', //未税执行价
    historyLowPrice: '历史最低价', //历史最低价
    priceDifference: '价格差异', //价格差异
    pricingTemplate: '核价模板', //核价模板
    purName: '采购员', //采购员
    quoteAttribute: '价格属性', //价格属性
    planDeliveryDate: '计划交货时间', //计划交货时间
    priceValidDateStart: '有效期从', //有效期从
    priceValidDateEnd: '有效期至', //有效期至
    buyerOrgName: '采购组', //采购组
    remark: '备注' //备注
  }
]
