<template>
  <div>
    <mt-select
      id="siteName"
      v-model="data.siteName"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
    ></mt-select>
  </div>
</template>
<script>
export default {
  data() {
    return {
      placeholder: this.$t('请选择工厂'),
      fields: { text: 'siteName', value: 'siteName' },
      dataSource: [],
      isDisabled: false
    }
  },
  mounted() {
    this.getDataSource()
  },
  methods: {
    getDataSource() {
      let id = this.data.column.orgParame.id
      let parameter = {
        parentId: id,
        tenantId: 10000
      }
      this.$API.configuration.findSiteInfoByParentId(parameter).then((res) => {
        this.dataSource = res.data
      })
    },
    startOpen() {
      if (!this.dataSource.length) {
        this.getDataSource()
      }
    },
    selectChange(val) {
      console.log(val)
      // this.data["siteCode"] = val.itemData.siteCode;
      // this.data["siteName"] = val.itemData.siteName;
      this.$bus.$emit('siteCodeChange', val.itemData.siteCode)
      // this.$parent.$emit("selectedChanged", {
      //   fieldCode: "siteCode",
      //   itemInfo: val.itemData,
      // });
    }
  }
}
</script>
