<template>
  <div>
    <mt-select
      id="supplierName"
      v-model="data.supplierName"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      :allow-filtering="true"
      :show-clear-button="true"
      :filtering="filteringsupplierCode"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
    ></mt-select>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      placeholder: this.$t('请选择供应商'),
      fields: { text: 'supplierName', value: 'supplierName' },
      dataSource: [],
      isDisabled: false
    }
  },
  mounted() {
    this.filteringsupplierCode = utils.debounce(this.filteringsupplierCode, 1000)
    this.getDataSource()
  },
  methods: {
    getDataSource() {
      console.log(this.data)
      let organizationCode = this.data.column.orgParame.organizationCode
      let params = {
        organizationCode: organizationCode,
        fuzzyNameOrCode: this.data?.supplierName ?? ''
      }
      this.$API.configuration.criteriaQuery(params).then((res) => {
        this.dataSource = res.data
      })
    },
    startOpen() {
      if (!this.dataSource.length) {
        this.getDataSource()
      }
    },
    filteringsupplierCode(e) {
      //供应商
      let data = {
        organizationCode: this.formObject.companyCode,
        fuzzyNameOrCode: e?.text ?? ''
      }
      this.$API.configuration.criteriaQuery(data).then((res) => {
        this.dataSource = res.data
      })
    },
    selectChange(val) {
      this.$bus.$emit('supplierCodeChange', val.itemData.supplierCode)
    }
  }
}
</script>
