<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <!-- 选择物料/品项编码、SKU -->
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="headerTxt"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { itemCodeColumnData } from '../config/pcSelection.js' // 命名要与field code一致

export default {
  data() {
    return {
      data: {
        // itemCode: null,
      },
      fieldName: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [],
      itemCodeColumnData,
      fields: {},
      headerTxt: this.$t('请选择物料/品项编码'),
      requestUrl: [], // 因为主数据的物料和sku的下拉和弹窗是两个接口给的，所以这里有两个值。第一个是下拉的，第二个是弹窗的
      changedFieldArr: [],
      changedRowArr: [],
      clearFieldArr: [],
      clearRowArr: [],
      allowEditing: true,
      dialogShow: false,
      siteCode: null
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.$bus.$on('siteCodeChange', (txt) => {
      this.siteCode = txt
    })
    this.initDialog()
  },

  methods: {
    // 双击物料行，也进行提交
    recordDoubleClick(args) {
      this.confirm([args.rowData])
    },

    // 提交
    confirm(records) {
      let _records = records
      if (!_records || !_records.length) {
        _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (!_records.length) return
      if (_records[0]) {
        let selectedRowInfo = _records[0]
        if (this.fieldName == 'itemCode') {
          selectedRowInfo = {
            itemId: selectedRowInfo?.id, // 物料数据
            itemCode: selectedRowInfo?.itemCode,
            itemName: selectedRowInfo?.itemName
          }
        }
        let parameter = {
          itemCode: selectedRowInfo?.itemCode,
          siteCode: this.siteCode ? this.siteCode : this.data.siteCode
        }
        this.$API.nuclearPrice.queryQuotaPrice(parameter).then((res) => {
          this.changeOtherCol(res.data)
          this.data['itemCode'] = selectedRowInfo['itemCode']
          this.$bus.$emit(`itemNameChange`, selectedRowInfo?.itemName || null)
        })

        // 改变额外值、清空其他额外值
        // this.setCellInfo(selectedRowInfo);

        // if (this.$route.path.includes("purchase-coordination")) {
        //   //清空价格记录
        //   this.handleClearPriceArr();
        // }

        // 关闭弹窗
        this.handleClose()
      }
    },
    // handleClearPriceArr() {
    //   //清空价格记录
    //   this.$bus.$emit("taxidChange", null);
    //   this.$bus.$emit("taxPriceChange", null);
    //   this.$bus.$emit("taxTotalChange", null);
    //   this.$bus.$emit("freePriceChange", null);
    //   this.$bus.$emit("freeTotalChange", null);
    //   this.$bus.$emit("contractRelChange2", null);
    // },
    // 调整其他列的数据：包括赋值和清空
    changeOtherCol(data) {
      let listObj = {
        // purOrgName: data?.purOrgName ?? "", //采购组织
        // siteName: data.siteName, //工厂名称
        siteCode: data?.siteCode ?? '', //工厂编码
        categoryCode: data?.categoryCode ?? '', //品类编码
        categoryName: data?.categoryName ?? '', //品类名称
        // supplierCode: data?.supplierCode ?? "", //供应商编码
        // supplierName: data?.supplierName ?? "", //供应商名称
        formula: data?.formula ?? '', //公式
        formulaQuotaInfo: data?.formulaQuotaInfo ?? '', //公式定额
        currencyName: data?.currencyName ?? '', //币种
        taxid: data?.taxid ?? '', //税率
        unitName: data?.unitName ?? '', //单位
        priceUnit: data?.priceUnit ?? '', //价格单位
        taxedBasePrice: data?.taxedBasePrice ?? '', //含税基价
        taxedAveragePrice: data?.taxedAveragePrice ?? '', //含税均价
        executeTaxedUnitPrice: data?.executeTaxedUnitPrice ?? '', //含税执行价
        executeUntaxedUnitPrice: data?.executeUntaxedUnitPrice ?? '', //未税执行价
        historyLowPrice: data?.historyLowPrice ?? '', //历史最低价
        priceDifference: data?.priceDifference ?? '', //价格差异
        pricingTemplate: data?.pricingTemplate ?? '', //核价模板
        purName: data?.purName ?? '', //采购员
        quoteAttribute: data?.quoteAttribute ?? '', //价格属性
        planDeliveryDate: data?.planDeliveryDate ?? '', //计划交货时间
        priceValidDateStart: data?.priceValidDateStart ?? '', //有效期从
        priceValidDateEnd: data?.priceValidDateEnd ?? '', //有效期至
        buyerOrgName: data?.buyerOrgName ?? '', //采购组
        remark: data?.remark ?? '' //备注
      }
      let listData = Object.keys(listObj)
      // 如果配置了改变另一些列，就监听
      listData.forEach((i) => {
        this.$bus.$emit(`${i}Change`, listObj[i] || null)
        // 如果物料改变了，需要再广播一下物料id。供工厂下拉去获取到
        // if (i == "itemCode") {
        //   this.$bus.$emit("itemIdChange1", itemData.itemId);
        //   this.$bus.$emit("itemIdChange", itemData.itemId, itemData.itemCode);
        //   this.$bus.$emit("itemCodeChange", itemData.itemCode);
        //   this.$bus.$emit("itemCodeChange1", itemData.itemCode);
        //   console.log(this.$t("是物料改变"), itemData);
        // }
      })
      // // 配置了 清空另一些列
      // this.clearFieldArr.forEach((item) => {
      //   this.$bus.$emit(`${item}Change`, null);
      // });

      // 如果物料改变了，需要再广播一下物料id。供工厂下拉去获取到
      // if (this.fieldName == "itemCode") {
      //   this.$bus.$emit("itemIdChange", itemData.itemId, itemData.itemCode);
      //   this.$bus.$emit("itemIdChange1", itemData.itemId);
      //   this.$bus.$emit("itemIdChange2", itemData.itemId);
      //   this.$bus.$emit("itemCodeChange", itemData.itemCode);
      //   this.$bus.$emit("itemCodeChange1", itemData.itemCode);
      //   this.$bus.$emit("itemCodeChange2", itemData.itemCode);
      //   console.log("是物料改变2222", itemData);
      // }
      //如果SKU变了 ,传下SKU数据到价格记录组件
      // if (this.fieldName == "skuCode") {
      //   this.$bus.$emit("skuChange", {
      //     skuCode: itemData.skuCode,
      //     skuId: itemData.skuId,
      //   });
      // }
    },

    // 调整额外值：包括赋值和清空
    // setCellInfo(itemData) {
    //   // 记录下这行的id、code、name
    //   let _data = itemData;

    //   // 也要清空这一行中 被物料/sku 改变的数据
    //   this.clearFieldArr.forEach((item) => {
    //     _data[item] = null;
    //   });

    //   // 清空其他额外值
    //   this.clearRowArr.forEach((item) => {
    //     _data[item] = null;
    //   });

    //   this.$parent.$emit("selectedChanged", {
    //     fieldCode: this.fieldName,
    //     itemInfo: _data,
    //   });

    //   // console.log(this.$t("组合后的数据"), _data);
    // },

    // 点击 清除数据
    handleClear() {
      this.data[this.fieldName] = null
      // 改变其他列
      this.changeOtherCol(this.delList)
      this.$bus.$emit(`itemNameChange`, null)
    },
    showDialog() {
      if (!this.data.siteCode && !this.siteCode) {
        this.$toast({ content: this.$t('请选择工厂'), type: 'warning' })
        return
      }
      this.dialogShow = true
      this.initDialog()
      this.$refs.dialog.ejsRef.show()
    },

    initDialog() {
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            height: 352,
            allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: this[`${this.fieldName}ColumnData`],
            asyncConfig: {
              url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
              params: {
                page: {
                  current: 1,
                  size: 10
                }
              }
            }
          }
        }
      ]
    },

    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-pagertemplate {
  padding: 0px !important;
}
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
    .not-allowed {
      cursor: not-allowed !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: 396px;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
