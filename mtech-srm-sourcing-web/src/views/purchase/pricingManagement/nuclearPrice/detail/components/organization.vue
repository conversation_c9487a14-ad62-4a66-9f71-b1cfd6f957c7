<template>
  <div>
    <mt-select
      id="purOrgName"
      v-model="data.purOrgName"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
    ></mt-select>
  </div>
</template>
<script>
export default {
  data() {
    return {
      placeholder: this.$t('请选择采购组织'),
      fields: { text: 'organizationName', value: 'organizationName' },
      dataSource: [],
      isDisabled: false
    }
  },
  mounted() {
    this.getDataSource()
  },
  methods: {
    getDataSource() {
      let id = this.data.column.orgParame.id
      let params = {
        fuzzyParam: '',
        orgId: id
      }
      this.$API.nuclearPrice.getBusinessOrganizationByOrgId(params).then((res) => {
        this.dataSource = res.data
      })
    },
    startOpen() {
      if (!this.dataSource.length) {
        this.getDataSource()
      }
    },
    selectChange(val) {
      this.$bus.$emit('purOrgNameChange', val.itemData.organizationName)
    }
  }
}
</script>
