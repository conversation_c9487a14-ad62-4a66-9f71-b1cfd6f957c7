<!--核价定价-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.gridRef.getMtechGridRecords()
      if (_selectRows.length <= 0 && e.toolbar.id == 'delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleClickToolBarAdd()
      }
      if (e.toolbar.id == 'delete') {
        this.handleClickToolBarDelete(_selectRows)
      }
      if (e.toolbar.id == 'submit') {
        this.handleClickToolBarSubmit(_selectRows)
      }
      if (e.toolbar.id === 'export') this.handleExport()
    },
    //新增
    handleClickToolBarAdd() {
      this.$router.push({
        path: 'pricingManagement-nuclearPrice-detail',
        query: { key: this.$utils.randomString() }
      })
    },
    //删除
    handleClickToolBarDelete(_selectRows) {
      let ids = _selectRows.map((item) => item.id)
      let parameter = {
        ids: ids
      }
      this.$API.nuclearPrice.deleteHeader(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    //提交
    handleClickToolBarSubmit(_selectRows) {
      let ids = _selectRows.map((item) => item.id)
      let parameter = {
        ids: ids
      }
      this.$API.nuclearPrice.submit(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    //点击行内跳转链接
    handleClickCellTitle(e) {
      //复制模-MKP、费用评估表
      if (e.field === 'priceNo') {
        this.$router.push({
          path: 'pricingManagement-nuclearPrice-detail',
          query: {
            id: e.data.id,
            key: this.$utils.randomString()
          }
        })
      }
      //oa
      if (e.field == 'oaUrl') {
        this.handleClickCellTitleOaUrl(e)
      }
    },
    handleClickCellTitleOaUrl(e) {
      if (e.data.oaUrl == '0' || e.data.oaUrl == '3') return
      let parameter = {
        docId: e.data.id
      }
      this.$API.configuration.getQuotaOaLink(parameter).then(({ data }) => {
        var a = document.createElement('a')
        a.href = data
        a.id = 'oaUrl'
        a.download = 'a.pdf'
        a.style = 'display:none'
        document.body.appendChild(a)
        document.getElementById('oaUrl').click()
        setTimeout(function () {
          document.getElementById('oaUrl').remove()
        }, 600)
      })
    },
    //导出
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$API.configuration.rulePriceCfgExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
