import { Formatter } from '@/utils/ej/dataGrid'
import { fmtDatetime } from '@/utils/ej/dataGrid/formatter'
import { useFiltering } from '@/utils/ej/select'
import { addArrTextField, makeTextFields, filteringByText } from './utils'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'

export function columnData({ prefix = 'biddingItemLogisticsDTO.', currencyList = [] }) {
  const currencyListCN = addArrTextField(currencyList, 'currencyCode', 'currencyName')

  return [
    // 起运机场
    {
      field: prefix + 'startAirport',
      editConfig: {
        type: 'text'
      }
    },
    // eta ETA
    {
      field: prefix + 'eta',
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: new Date()
      }
    },
    // etd ETD
    {
      field: prefix + 'etd',
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: new Date()
      }
    },
    // airFee 航空运费
    {
      field: prefix + 'airFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // startAirportHandleFee 机场操作费（起运港）
    {
      field: prefix + 'startAirportHandleFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // manifestFee 舱单录入费
    {
      field: prefix + 'manifestFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // billDeclarationFee 买单报关费
    {
      field: prefix + 'billDeclarationFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // customsClearanceFee 清关费
    {
      field: prefix + 'customsClearanceFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // endAirportHandleFee 机场操作费（目的港）
    {
      field: prefix + 'endAirportHandleFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // distributionFee 配送费
    {
      field: prefix + 'distributionFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // agencyFee 代理费（报关代理）
    {
      field: prefix + 'agencyFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },

    // totalFee 总计费用
    {
      field: prefix + 'totalFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },

    // agencyFeeCurrencyCode 代理费币种编码
    {
      field: prefix + 'agencyFeeCurrencyCode',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyCode'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },
    // agencyFeeCurrencyName 代理费币种
    {
      field: prefix + 'agencyFeeCurrencyName',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyName'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },

    // 总计费用币种编码
    {
      field: prefix + 'totalFeeCurrencyCode',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyCode'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },
    //  总计费用币种名称
    {
      field: prefix + 'totalFeeCurrencyName',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyName'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },

    // airFeeCurrencyCode 航空运费币种编码
    {
      field: prefix + 'airFeeCurrencyCode',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyCode'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },
    // airFeeCurrencyName 航空运费币种
    {
      field: prefix + 'airFeeCurrencyName',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyName'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },

    // transportFeeCurrencyCode 运费币种编码
    {
      field: prefix + 'transportFeeCurrencyCode',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyCode'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },
    // transportFeeCurrencyName 运费币种
    {
      field: prefix + 'transportFeeCurrencyName',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyName'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },

    // startPortFeeCode 起运港杂费币种编码
    {
      field: prefix + 'startPortFeeCode',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyCode'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },
    // startPortFeeName 起运港杂费币种
    {
      field: prefix + 'startPortFeeName',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyName'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },

    // endPortFeeCode 目的港杂费币种编码
    {
      field: prefix + 'endPortFeeCode',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyCode'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },
    // endPortFeeName 目的港杂费币种
    {
      field: prefix + 'endPortFeeName',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyName'),
        dataSource: currencyListCN,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },

    // twentyGpQuantity 20GP可提供数量
    {
      field: prefix + 'twentyGpQuantity',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // fortyGpQuantity 40GP可提供数量
    {
      field: prefix + 'fortyGpQuantity',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // fortyHcQuantity 40HC可提供数量
    {
      field: prefix + 'fortyHcQuantity',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // fortyFiveHcQuantity 45HC可提供数量
    {
      field: prefix + 'fortyFiveHcQuantity',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // twentyGpFee 20GP海运费用
    {
      field: prefix + 'twentyGpFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // fortyGpFee 40GP海运费用
    {
      field: prefix + 'fortyGpFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // fortyHcFee 40HC海运费用
    {
      field: prefix + 'fortyHcFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // fortyFiveHcFee 45HC海运费用
    {
      field: prefix + 'fortyFiveHcFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // twentyGpThcFee 20GPTHC费用
    {
      field: prefix + 'twentyGpThcFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // fortyGpThcFee 40GPTHC费用
    {
      field: prefix + 'fortyGpThcFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // fortyHcThcFee 40HCTHC费用
    {
      field: prefix + 'fortyHcThcFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // fortyFiveHcThcFee 45HCTHC费用
    {
      field: prefix + 'fortyFiveHcThcFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },

    {
      field: prefix + 'doc',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    {
      field: prefix + 'seal',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    {
      field: prefix + 'eir',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // otherFee 其他费用
    {
      field: prefix + 'otherFee',
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // seaTransportFeeTotal 海运费总价
    {
      field: prefix + 'seaTransportFeeTotal',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // landTransportMode 陆运模式
    {
      field: prefix + 'landTransportMode',
      editConfig: {
        type: 'text'
      }
    },
    // landTransportFeePrice 陆运费报价
    {
      field: prefix + 'landTransportFeePrice',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // friendshipDrivingFee 友谊关代驾费
    {
      field: prefix + 'friendshipDrivingFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // nightDrivingFee 代驾压夜费
    {
      field: prefix + 'nightDrivingFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // nightCarFee 车辆滞留压夜费
    {
      field: prefix + 'nightCarFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // declarationFee 报关费
    {
      field: prefix + 'declarationFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // transportEffective 运输时效
    {
      field: prefix + 'transportEffective',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // loadingUnloadingMode 装卸方式
    {
      field: prefix + 'loadingUnloadingMode',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // completeVehiclePrice 整车总价（1天压夜）
    {
      field: prefix + 'completeVehiclePrice',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // transportFee 运费
    {
      field: prefix + 'transportFee',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // quoteEffectiveStartDate 报价有效期从
    {
      field: prefix + 'quoteEffectiveStartDate',
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      editConfig: {
        type: 'date',
        format: 'yyyy-MM-dd',
        'time-stamp': true,
        'show-clear-button': false
        // min: new Date(),
      }
    },
    // quoteEffectiveEndDate 报价有效期至
    {
      field: prefix + 'quoteEffectiveEndDate',
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      editConfig: {
        type: 'date',
        format: 'yyyy-MM-dd',
        'time-stamp': true,
        'show-clear-button': false
        // min: new Date(),
      }
    }
  ]
}
