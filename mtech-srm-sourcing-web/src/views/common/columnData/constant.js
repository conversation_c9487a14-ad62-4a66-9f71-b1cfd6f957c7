// 阶梯报价时需要阶梯展示的价格字段（暂时列举这些，后续根据功能添加）mt_supplier_bidding_item
// 单价(未税)、单价（含税）、历史价格、上次报价未税、上次报价含税、降幅%、降幅、
// 是否成本模型报价、成本模型id、成本模型合并、成本分析成本测算不合并
export const stepPriceField = [
  'biddingItemDTO.untaxedUnitPrice', //单价(未税)
  'biddingItemDTO.taxedUnitPrice', //单价（含税）
  'biddingItemDTO.bidHistoryPrice', //历史价格
  'biddingItemDTO.lastQuoteUntaxed', //上次报价未税
  'biddingItemDTO.lastQuoteTaxed', //上次报价含税
  'biddingItemDTO.declinePercent', //降幅%
  // 'biddingItemDTO.costModelQuote', //是否成本模型报价、
  // 'biddingItemDTO.costModelId', //成本模型id
  'biddingItemDTO.costModelName', //成本模型
  'biddingItemDTO.transportCost', // 运费
  'biddingItemDTO.processCost', // 加工费
  'biddingItemDTO.processPartyMaterialCost', //加工费材料费
  ''
]

export const stepPriceFieldArr = [
  'untaxedUnitPrice', //单价(未税)
  'taxedUnitPrice', //单价（含税）
  'bidHistoryPrice', //历史价格
  'lastQuoteUntaxed', //上次报价未税
  'lastQuoteTaxed', //上次报价含税
  'declinePercent', //降幅%
  // 'costModelQuote', //是否成本模型报价、
  // 'costModelId', //成本模型id
  'costModelName', //成本模型
  'transportCost', // 运费
  'processCost', // 加工费
  'processPartyMaterialCost' //加工费材料费
]

export const stepPriceFieldNoPrefix = [
  'untaxedUnitPrice', //单价(未税)
  'taxedUnitPrice', //单价（含税）
  'bidHistoryPrice', //历史价格
  'lastQuoteUntaxed', //上次报价未税
  'lastQuoteTaxed', //上次报价含税
  'declinePercent', //降幅%
  // 'costModelQuote', //是否成本模型报价、
  // 'costModelId', //成本模型id
  'costModelName', //成本模型
  'transportCost', // 运费
  'processCost', // 加工费
  'processPartyMaterialCost' //加工费材料费
]

// 不合并的field - no prefix
export const notMergeFieldnoPrefix = [
  ...stepPriceFieldNoPrefix,
  'stepNum',
  'itemExtMap.referItemUnitPriceUntaxed'
]

// 不合并的field - 报价
export const stepNotMergeField = [
  'stepNum', //阶梯数量
  'biddingItemDTO.untaxedUnitPrice', //单价(未税)
  'biddingItemDTO.taxedUnitPrice', //单价（含税）
  'biddingItemDTO.untaxedTotalPrice', //总价（未税）
  'biddingItemDTO.taxedTotalPrice', //总价（含税）
  'biddingItemDTO.bidHistoryPrice', //历史价格
  'biddingItemDTO.lastQuoteUntaxed', //上次报价未税
  'biddingItemDTO.lastQuoteTaxed', //上次报价含税
  'biddingItemDTO.declinePercent', //降幅%
  'biddingItemDTO.transportCost', // 运费
  'biddingItemDTO.processCost', // 加工费
  'biddingItemDTO.processPartyMaterialCost', //加工费材料费
  'itemExtMap.referItemUnitPriceUntaxed', // 参考物料单价（未税）
  'itemExtMap.referItemUnitPriceTaxed', // 参考物料单价（含税）
  'costAnalysis', //成本分析
  'costModelName', //成本模型
  'costEstimation', //成本测算
  'costModelEstimatePrice' //测算价格
]

export const stepNotMergeFieldNoPrefix = [
  'stepNum', //阶梯数量
  'untaxedUnitPrice', //单价(未税)
  'taxedUnitPrice', //单价（含税）
  'untaxedTotalPrice', //总价（含税）
  'taxedTotalPrice', //总价（含税）
  'bidHistoryPrice', //历史价格
  'lastQuoteUntaxed', //上次报价未税
  'lastQuoteTaxed', //上次报价含税
  'declinePercent', //降幅%
  'transportCost', // 运费
  'processCost', // 加工费
  'processPartyMaterialCost', //加工费材料费
  'referItemUnitPriceUntaxed', // 参考物料单价（未税）
  'referItemUnitPriceTaxed', // 参考物料单价（含税）
  'costAnalysis', //成本分析
  'costModelName', //成本模型
  'costEstimation', //成本测算
  'costModelEstimatePrice' //测算价格
]
