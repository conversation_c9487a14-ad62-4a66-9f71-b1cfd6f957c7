/**
 * 物流相关字段
 */
import { i18n } from '@/main.js'
import { getValueByPath } from '@/utils/obj'
import { fmtDatetime } from '@/utils/ej/dataGrid/formatter'

export function useColumn(detailInfo) {
  return [
    'sea_transport', // 海运
    'air_transport', // 空运
    'railway_transport', // 铁路
    'land_transport', // 陆运
    'trailer_transport' // 拖车
  ].includes(detailInfo.sourcingObjType)
}

export function columnData({
  prefix = 'itemLogisticsResponse.',
  dictItems = [],
  handleSelectChange,
  detailInfo
}) {
  if (detailInfo && !useColumn(detailInfo)) {
    return []
  }
  const formatter = {
    select: function ({ field, editConfig }, item) {
      const cellVal = getValueByPath(item, field)
      const { value, text } = editConfig.fields
      return editConfig.dataSource.find((e) => e[value] === cellVal)?.[text] ?? cellVal
    }
  }

  return [
    // 运输方式
    {
      field: prefix + 'transportType',
      headerText: i18n.t('运输方式'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        readonly: true,
        disabled: true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 'sea_transport', text: i18n.t('海运') },
          { value: 'air_transport', text: i18n.t('空运') },
          { value: 'railway_transport', text: i18n.t('铁路') },
          { value: 'land_transport', text: i18n.t('陆运') },
          { value: 'trailer_transport', text: i18n.t('拖车') }
        ],
        placeholder: i18n.t('选择运输方式'),
        callback: handleSelectChange
      }
    },
    // 起始岗
    {
      field: prefix + 'startPort',
      headerText: i18n.t('起运港'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'itemCode', text: 'itemName' },
        dataSource: dictItems.filter((e) => e.dictCode === 'START-PORT'),
        placeholder: i18n.t('选择起运港'),
        callback: handleSelectChange
      }
    },
    // 目的港
    {
      field: prefix + 'endPort',
      headerText: i18n.t('目的港'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'itemCode', text: 'itemName' },
        dataSource: dictItems.filter((e) => e.dictCode === 'DESTINATION-PORT'),
        placeholder: i18n.t('选择目的港'),
        callback: handleSelectChange
      }
    },
    // 品名
    {
      field: 'itemName',
      headerText: i18n.t('品名'),
      editConfig: {
        type: 'text',
        placeholder: i18n.t('请输入品名'),
        callback: handleSelectChange
      }
    },
    // 贸易条款
    {
      field: prefix + 'logisticsTerm',
      headerText: i18n.t('贸易条款'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'itemName', text: 'itemName' },
        dataSource: dictItems.filter((e) => e.dictCode === 'TradeClause'),
        placeholder: i18n.t('选择贸易条款'),
        callback: handleSelectChange
      }
    },
    // 报关方式
    {
      field: prefix + 'declareMode',
      headerText: i18n.t('报关方式'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 'oneself_declare', text: i18n.t('自理报关') },
          { value: 'direct_agency_declare', text: i18n.t('直接代理报关') },
          { value: 'indirect_agency_declare', text: i18n.t('间接代理报关') }
        ],
        placeholder: i18n.t('选择报关方式'),
        callback: handleSelectChange
      }
    },
    // 服务模式
    {
      field: prefix + 'serviceMode',
      headerText: i18n.t('服务模式'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 'CY_CY', text: 'CY-CY' },
          { value: 'CY_DOOR', text: 'CY-DOOR' }
        ],
        placeholder: i18n.t('选择服务模式'),
        callback: handleSelectChange
      }
    },
    // twentyGp 20GP
    {
      field: prefix + 'twentyGp',
      headerText: '20GP',
      editConfig: {
        type: 'number',
        min: 0,
        placeholder: i18n.t('请输入'),
        callback: handleSelectChange
      }
    },
    // fortyGp 40GP
    {
      field: prefix + 'fortyGp',
      headerText: '40GP',
      editConfig: {
        type: 'number',
        min: 0,
        placeholder: i18n.t('请输入'),
        callback: handleSelectChange
      }
    },
    // fortyHc 40HC
    {
      field: prefix + 'fortyHc',
      headerText: '40HC',
      editConfig: {
        type: 'number',
        min: 0,
        placeholder: i18n.t('请输入'),
        callback: handleSelectChange
      }
    },
    // fortyFiveHc 45HC
    {
      field: prefix + 'fortyFiveHc',
      headerText: '45HC',
      editConfig: {
        type: 'number',
        min: 0,
        placeholder: i18n.t('请输入'),
        callback: handleSelectChange
      }
    },
    // expectLoadingTime 期望装货时间
    {
      field: prefix + 'expectLoadingTime',
      headerText: i18n.t('期望装货时间'),
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'value-format': 'yyyy-MM-dd HH:mm:ss',
        'show-clear-button': false,
        min: new Date(),
        callback: handleSelectChange
      }
    },
    // lastDeliveryTime 最晚发运时间
    {
      field: prefix + 'lastDeliveryTime',
      headerText: i18n.t('最晚发运时间'),
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'value-format': 'yyyy-MM-dd HH:mm:ss',
        'show-clear-button': false,
        min: new Date(),
        callback: handleSelectChange
      }
    },
    // expectArriveTime 期望送达时间
    {
      field: prefix + 'expectArriveTime',
      headerText: i18n.t('期望送达时间'),
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'value-format': 'yyyy-MM-dd HH:mm:ss',
        'show-clear-button': false,
        min: new Date(),
        callback: handleSelectChange
      }
    },
    // startPlace  始发地
    {
      field: prefix + 'startPlace',
      headerText: i18n.t('始发地'),
      editConfig: {
        type: 'text',
        callback: handleSelectChange
      }
    },
    // endPlace 目的地
    {
      field: prefix + 'endPlace',
      headerText: i18n.t('目的地'),
      editConfig: {
        type: 'text',
        callback: handleSelectChange
      }
    },
    // transportLine 运段（运输路线）
    {
      field: prefix + 'transportLine',
      headerText: i18n.t('运段（运输路线）'),
      editConfig: {
        type: 'text',
        callback: handleSelectChange,
        readonly: true
      }
    },
    // goodsFinishTime // 货好时间
    {
      field: prefix + 'goodsFinishTime',
      headerText: i18n.t('货好时间'),
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'value-format': 'yyyy-MM-dd HH:mm:ss',
        'show-clear-button': false,
        min: new Date(),
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'transportQuantity',
      headerText: i18n.t('件数（需求量）'),
      editConfig: {
        type: 'number',
        min: 0,
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'arriveAddress',
      headerText: i18n.t('送货地址'),
      editConfig: {
        type: 'text',
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'specSize',
      headerText: i18n.t('尺寸'),
      editConfig: {
        type: 'text',
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'specGrossWeight',
      headerText: i18n.t('毛重（KG）'),
      editConfig: {
        type: 'number',
        min: 0,
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'specVolume',
      headerText: i18n.t('体积（CBM）'),
      editConfig: {
        type: 'number',
        min: 0,
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'specVolumeWeight',
      headerText: i18n.t('体积重（KG）'),
      editConfig: {
        type: 'number',
        min: 0,
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'loadingAddress',
      headerText: i18n.t('装货地址'),
      editConfig: {
        type: 'text',
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'carType',
      headerText: i18n.t('载重车型'),
      editConfig: {
        type: 'text',
        callback: handleSelectChange
      }
    },
    // demandStartTime // 需求起运时间
    {
      field: prefix + 'demandStartTime',
      headerText: i18n.t('需求起运时间'),
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'value-format': 'yyyy-MM-dd HH:mm:ss',
        'show-clear-button': false,
        min: new Date(),
        callback: handleSelectChange
      }
    },
    // demandDeliveryTime // 需求送达时间
    {
      field: prefix + 'demandDeliveryTime',
      headerText: i18n.t('需求起运时间'),
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'value-format': 'yyyy-MM-dd HH:mm:ss',
        'show-clear-button': false,
        min: new Date(),
        callback: handleSelectChange
      }
    }
  ]
}
