// 运输相关字段
import { i18n } from '@/main.js'
import { getValueByPath } from '@/utils/obj'
import { dateUtils } from '@mtech-common/utils'
import { MAX_SAFE_INTEGER, PRICE_EDIT_CONFIG } from '@/constants/editConfig'

export function columnData({
  prefix = 'itemExtMap.',
  dictItems = [],
  handleSelectChange,
  detailInfo = {},
  submitTableData = [],
  moduleType = ''
}) {
  const formatter = {
    select: function ({ field, editConfig }, item) {
      const cellVal = getValueByPath(item, field)
      const { value, text } = editConfig.fields
      return editConfig.dataSource.find((e) => e[value] == cellVal)?.[text] ?? cellVal
    }
  }
  const {
    id,
    sourcingType,
    sourcingObjType,
    sourcingMode,
    companyCode,
    priceClassification,
    docSource,
    buType
  } = detailInfo

  return [
    // 直送地
    {
      field: prefix + 'deliveryPlace',
      headerText: i18n.t('直送地'),
      formatter: formatter.select,
      editConfig: {
        type: 'multiSelect',
        'show-clear-button': true,
        fields: { value: 'itemName', text: 'itemName' },
        dataSource: dictItems.filter((e) => {
          if (buType === 'TX') {
            return e.dictCode === 'DELIVERY_PLACE_TX'
          }
          return e.dictCode === 'DELIVERY_PLACE'
        }),
        placeholder: i18n.t('选择直送地'),
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'packageFee',
      headerText: i18n.t('包装费'),
      allowEditing: true,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    {
      field: prefix + 'startingPrice',
      headerText: i18n.t('起价（未税）'),
      allowEditing: true,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        precision: 5,
        type: 'number'
      }
    },
    {
      field: prefix + 'budgetUnitPriceTaxed',
      headerText: i18n.t('预算单价(含税)'),
      allowEditing: true,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number',
        disabled: true
      }
    },
    {
      field: prefix + 'budgetUnitPriceUntaxed',
      headerText: i18n.t('预算单价(未税)'),
      allowEditing: true,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    {
      field: prefix + 'budgetTotalPriceTaxed',
      headerText: i18n.t('预算总价(含税)'),
      allowEditing: true,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number',
        disabled: docSource !== 0
      }
    },
    {
      field: prefix + 'budgetTotalPriceUntaxed',
      headerText: i18n.t('预算总价(未税)'),
      allowEditing: true,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number',
        disabled: docSource !== 0
      }
    },
    {
      field: prefix + 'requireQuantity',
      headerText: i18n.t('需求数量'),
      allowEditing: true,
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER
      }
    },
    {
      field: prefix + 'requireEndDate',
      headerText: i18n.t('需求截止日期'),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        const fmt = 'YYYY-MM-DD'
        if (typeof cellVal === 'object' && cellVal !== null) {
          return dateUtils(cellVal).format(fmt)
        } else if (/^\d{13}$/.test(cellVal)) {
          return dateUtils(new Date(Number(cellVal))).format(fmt)
        } else if (cellVal && typeof cellVal === 'string') {
          return dateUtils(new Date(cellVal)).format(fmt)
        }
        return cellVal
      },
      editConfig: {
        type: 'date',
        format: 'yyyy-MM-dd',
        'time-stamp': true,
        'show-clear-button': false,
        min: new Date(),
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'maxQuotePercent',
      headerText: i18n.t('价格上限'),
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number',
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'minQuotePercent',
      headerText: i18n.t('价格下限'),
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number',
        callback: handleSelectChange
      }
    },
    {
      field: prefix + 'minQuoteRange',
      headerText: i18n.t('最小竞价幅度'),
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number',
        callback: handleSelectChange
      },
      formatter: function ({ field }, item) {
        const cellVal = getValueByPath(item, field)
        const typeVal = getValueByPath(item, 'itemExtMap.minQuoteRangeType')
        return typeVal === 1 ? (cellVal || 0) + '%' : cellVal
      }
    },
    {
      field: prefix + 'minQuoteRangeType',
      headerText: i18n.t('最小竞价幅度类型'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 0, text: i18n.t('按金额') },
          { value: 1, text: i18n.t('按比例') }
        ],
        placeholder: i18n.t('选择最小竞价幅度类型'),
        callback: handleSelectChange
      }
    },

    // 无条件L/T
    {
      field: prefix + 'unconditionalLeadTime',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    },
    // L/T
    {
      field: prefix + 'leadTime',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    },
    // 建议L/T
    {
      field: prefix + 'adviseLeadTime',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    },
    // 建议无条件L/T
    {
      field: prefix + 'adviseUnconLeadTime',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    },
    // 建议最小采购量
    {
      field: prefix + 'adviseMinPurQuantity',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    },
    // 建议最小包装数量
    {
      field: prefix + 'adviseMinPackageQuantity',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    },
    // 价格来源
    {
      field: prefix + 'referChannel',
      headerText: i18n.t('价格来源'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          // { value: -1, text: i18n.t('无') },
          { value: 0, text: i18n.t('手工定价') },
          { value: 2, text: i18n.t('价格记录') }
        ],
        placeholder: i18n.t('选择价格来源'),
        callback: handleSelectChange
      }
    },
    // 参考物料编码
    {
      field: prefix + 'referItemCode',
      headerText: i18n.t('参考物料编码'),
      width: 200,
      // 整机/模组外购、整机外发，弹窗选择
      editConfig: ['module_out_buy', 'module_out_going'].includes(sourcingObjType)
        ? {
            type: 'selectReferItem',
            field: 'referItemCode',
            sourcingObjType,
            companyCode
          }
        : {
            type: 'text',
            'show-clear-button': true
          }
    },
    // 参考物料名称
    {
      field: prefix + 'referItemName',
      headerText: i18n.t('参考物料名称'),
      width: 200,
      editConfig: {
        type: moduleType === 'supplier' ? 'text' : 'selectReferItem',
        field: 'referItemName',
        sourcingObjType,
        companyCode
      }
    },
    // 销售凭证
    {
      field: prefix + 'salesVouchers',
      headerText: i18n.t('销售凭证'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 'PANELROWA1', text: 'PANELROWA1' },
          { value: 'empty', text: i18n.t('-') }
        ],
        placeholder: i18n.t('选择销售凭证')
      }
    },
    {
      field: prefix + 'screenCode',
      headerText: i18n.t('屏编码'),
      width: 200,
      editConfig: {
        type: moduleType === 'supplier' ? 'text' : 'checkSelectedItemCode',
        field: 'screenCode',
        rfxId: id,
        submitTableData,
        sourcingType,
        source: sourcingMode,
        sourcingObjType,
        companyCode,
        priceClassification
      }
    },
    {
      field: prefix + 'tconCode',
      headerText: i18n.t('T-CON编码'),
      width: 200,
      editConfig: {
        type: moduleType === 'supplier' ? 'text' : 'checkSelectedItemCode',
        field: 'tconCode',
        rfxId: id,
        submitTableData,
        sourcingType,
        source: sourcingMode,
        sourcingObjType,
        companyCode,
        priceClassification
      }
    },
    // 参考屏编码
    {
      field: prefix + 'refScreenCode',
      editConfig: {
        type: 'text',
        'show-clear-button': true
      }
    },
    // 参考屏价格
    {
      field: prefix + 'refScreenPrice',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    },
    // 参考T-CON编码
    {
      field: prefix + 'refTconCode',
      editConfig: {
        type: 'text',
        'show-clear-button': true
      }
    },
    // 参考T-CON价格
    {
      field: prefix + 'refTconPrice',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    },
    // 参考总价
    {
      field: prefix + 'refTotalPrice',
      editConfig: {
        type: 'number',
        min: 0,
        max: MAX_SAFE_INTEGER,
        precision: 2
      }
    }
  ]
}
