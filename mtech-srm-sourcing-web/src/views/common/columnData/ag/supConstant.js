/**
 *  供方
 * 编辑相关常量整理
 */
export const tableNameMap = {
  mt_supplier_rfx_item: {
    key: ''
  },
  mt_supplier_bidding_item: {
    key: 'biddingItemDTO'
  },
  mt_supplier_rfx_item_ext: {
    key: 'itemExtMap'
  },
  mt_supplier_rfx_item_logistics: {
    key: 'rfxItemLogisticsDTO'
  },
  mt_supplier_bidding_item_logistics: {
    key: 'biddingItemLogisticsDTO'
  },
  mt_supplier_rfx_item_die: {
    key: 'rfxItemDie'
  },
  mt_rfx_annual_logistics_sea_item: {
    key: 'rfxAnnualLogisticsSeaDTO'
  },
  mt_rfx_annual_logistics_sea_bidding_item: {
    key: 'rfxAnnualLogisticsSeaDTO'
  },
  logistics_railway_item: {
    key: 'annualLogisticsRailwayItemDTO'
  },
  bidding_annual_railway: {
    key: 'annualLogisticsRailwayItemDTO'
  },
  annual_logistics_transport: {
    key: 'annualLogisticsTrunkItem'
  }
}
// 寻源类型 => tableName （针对于物流阶梯使用）
export const logisticsTableNameMap = {
  trunk_transport_annual: {
    tableName: 'annual_logistics_transport',
    resKey: 'annualLogisticsTrunkItem'
  },
  sea_transport_annual: {
    tableName: 'mt_rfx_annual_logistics_sea_item',
    resKey: 'rfxAnnualLogisticsSeaDTO'
  },
  railway_transport_annual: {
    tableName: 'logistics_railway_item',
    resKey: 'annualLogisticsRailwayItemDTO'
  }
}
// 非提交的字段
export const notSubmitFields = [
  'index', //序号
  'lineNo', //行号
  'biddingItemDTO.priceStatus' //状态
]
// 不允许编辑的字段（强控逻辑后续全部迁移到此配置）
export const notAllowEditFields = [
  'index', //序号
  'lineNo', //行号
  'categoryName',
  'biddingItemDTO.priceStatus', //状态
  'biddingItemDTO.bidHistoryPrice', //历史价格
  'biddingItemDTO.lastQuoteUntaxed', //上次报价未税
  'biddingItemDTO.lastQuoteTaxed', //上次报价含税
  'biddingItemDTO.bidPurUnitCode', //订单单位编码
  'biddingItemDTO.taxedTotalPrice', //总价含税
  'biddingItemDTO.untaxedTotalPrice', //总价未税
  'biddingItemDTO.bidCurrencyCode', //币种编码
  'biddingItemDTO.bidTaxRateCode', //供方税率编码
  'biddingItemDTO.bidTaxRateValue', //供方税率编码
  'biddingItemDTO.priceStatus', //状态
  'biddingItemLogisticsDTO.exchangeRate', //汇率
  'biddingItemLogisticsDTO.totalFee', //总费用
  'biddingItemDTO.totalPrice', //汇总价格
  'biddingItemDTO.priceUnitName', //价格单位
  'biddingItemDTO.categoryName', //品类
  'biddingItemDTO.totalQty' //总数
  // 'biddingItemDTO.paymentTermsName', //付款条件名称
  // 'biddingItemDTO.paymentTermsCode' //付款条件编码
]

// 字段 - 数字框
export const numberEditFields = [
  'transportCost', //价格信息-运费
  'processCost', //价格信息-加工费
  'diePriceUnTaxed', //价格信息-模具价格（未税)
  'diePriceTaxed', //价格信息-模具价格（含税）
  'notScreenPrice', //价格信息-非屏价格（含税）
  'untaxedTotalPrice', //价格信息-总价（未税）
  'taxedTotalPrice', //价格信息-总价（含税）
  'declinePercent', //价格信息-降幅%
  'dargaining', //价格信息-还价
  'discussPrice', //价格信息-议价
  'lastQuoteUntaxed', //价格信息-上次报价（未税）
  'lastQuoteTaxed', //价格信息-上次报价（含税）
  'untaxedUnitPrice', //价格信息-单价（未税）
  'conversionRate', //物料信息-转换率   todo
  'exchangeRate', //价格信息-汇率   todo
  'lastCostUntaxed', //模具信息-上次模具费用（未税）
  'lastCostTaxed', //模具信息-上次模具费用（含税）
  'bomProductPrice', //BOM信息-BOM成品价格
  'minPricePercent', //成本因子-价格下限%
  'maxPricePercent', //成本因子-价格上限%
  'lastShareCostUntaxed', //模具信息-上次模具分摊费用（未税)
  'tconPrice', //BOM信息-T-CON价格
  'lastShareCostTaxed', //模具信息-上次模具分摊费用（含税）
  'minQuoteRange', //价格信息-最小报价幅度
  'basicPrice', //成本因子-基准价
  'sharePriceUntaxed', //模具信息-模具分摊价格（未税）
  'minQuotePercent', //价格信息-报价下限
  'screenPrice', //BOM信息-屏价格
  'maxQuotePercent', //价格信息-报价上限
  'sharePriceTaxed', //模具信息-模具分摊价格（含税）
  'distributionQuantity', //数量信息-分配数量
  'quota', //数量信息-配额
  'shareQuantity', //模具信息-实际分摊数量（上传SAP）
  'adviseMinPurQuantity', //数量信息-建议最小采购量
  'planQuantity', //模具信息-规划量
  'subjectTotal', //财务信息-科目总额
  'minPurQuantity', //数量信息-最小采购量
  'referItemMinPur', //参考物料-参考物料最小采购量
  'singleQuantity', //数量信息-单机用量
  'budgetUnitPriceUntaxed', //价格信息-预估单价（未税）  财务信息-预算单价（未税）   todo多个
  'budgetUnitPriceTaxed', //价格信息-预估单价（含税）  财务信息-预算总价（含税）  todo多个
  'yearMinQuantity', //数量信息-年度最小用量
  'budgetTotalPriceTaxed', //财务信息-预算总价（含税）
  'referItemUnitPriceTaxed', //参考物料-参考物料单价（含税）
  'marketPrice', //价格信息-市场价格
  'yearAverageQuantity', //数量信息-年度平均用量
  'minPackageQuantity', //包装信息-最小包装数量
  'historyPrice', //价格信息-历史价格
  'yearMaxQuantity', //数量信息-年度最大用量
  'budgetTotalPriceUntaxed', //财务信息-预算总价（未税）
  'requireDelivery', //时间信息-要求交期（天）
  'adviseMinPackageQuantity', //包装信息-建议最小包装数量
  'forecastQuantity', //数量信息-预测采购量
  'purchaseTargetPrice', //价格信息-采购目标价
  'financeTargetPrice', //价格信息-财务目标价
  'requireQuantity', //数量信息-需求数量
  'specVolumeWeight', //物流信息-体积重（KG）
  'specVolume', //物流信息-体积（CBM）
  'specGrossWeight', //物流信息-毛重（KG）
  'transportQuantity', //物流信息-件数（需求量）
  'adviseLeadTime', // 建议L/T
  'adviseUnconLeadTime', // 建议无条件L/T
  'leadTime', // L/T
  'unconditionalLeadTime' // 无条件L/T
]

// 子表自读字段
export const childGridReadonlyFields = [
  // 单价（未税）
  // "mt_supplier_bidding_item.untaxedUnitPrice",
  // 单价（含税）
  // "mt_supplier_bidding_item.taxedUnitPrice",

  // // 规划分摊价（未税）
  // "mt_supplier_bidding_item.planSharePriceUntaxed",
  // // 规划分摊价（含税）
  // "mt_supplier_bidding_item.planSharePriceTaxed",

  // // 实际分摊价（未税）
  // "mt_supplier_bidding_item.realSharePriceUntaxed",
  // // 实际分摊价（含税）
  // "mt_supplier_bidding_item.realSharePriceTaxed",

  //上次规划分摊价（未税）
  'mt_supplier_bidding_item.lastPlanSharePriceUntaxed',
  //上次规划分摊价（含税）
  'mt_supplier_bidding_item.lastPlanSharePriceTaxed',

  //上次实际分摊价（未税）
  'mt_supplier_bidding_item.lastRealSharePriceUntaxed',
  //上次实际分摊价（含税）
  'mt_supplier_bidding_item.lastRealSharePriceTaxed',

  // 上次分摊后单价（未税）
  'mt_supplier_bidding_item.lastSharePriceUntaxed',
  // 上次分摊后单价（含税）
  'mt_supplier_bidding_item.lastSharePriceTaxed',

  // 上次分摊费用（未税)
  'mt_supplier_bidding_item.lastShareCostUntaxed',
  // 上次分摊费用（含税）
  'mt_supplier_bidding_item.lastShareCostTaxed',

  //分摊后单价（未税）
  'mt_supplier_bidding_item.sharePriceUntaxed',
  //分摊后单价（含税）
  'mt_supplier_bidding_item.sharePriceTaxed',

  //历史价格
  'mt_supplier_bidding_item.bidHistoryPrice',
  //汇总价格
  'mt_supplier_bidding_item.totalPrice'
]

// 物流海运禁用字段
export const logisticsNotAllowEditFields = [
  'freightFeeCurrencyName', // 海运费币种名称
  'freightFeeCurrencyCode', // 海运费币种编码
  'exportFeeCurrencyName', // 出口费币种名称
  'exportFeeCurrencyCode', // 出口费币种编码
  'polCurrencyName', // 起运费币种名称
  'polCurrencyCode', // 起运费币种编码
  'podCurrencyName', // 目的港杂费名称
  'podCurrencyCode' // 起运费币种编码
]

// 物流启用字段
export const logisticsEditFields = [
  'declareCustomsFee', // 报关费
  'remark', // 备注
  'podOutFreeCabinetDay', // 目的港场外免柜天数
  'podInFreeCabinetDay', // 目的港场内免柜天数
  'podFreeCabinetDay', // 目的港免柜+免堆天数
  'freeTimeType', // Free time type
  'rentalContainerFee', // 铁运（年约需求） - 租箱费
  'trailerFee', // 铁运（年约需求） - 拖车费
  'replacementStorageFee', // 铁运（年约需求） - 换装入库费
  'domesticRailwayFreightFee', // 铁运（年约需求） - 内贸铁路运费
  'railwayFreightFee', // 铁运（年约需求） - 铁路运费
  'qbjAroundFee', // 铁运（年约需求） - 青白江绕园费
  'socDeclareCustomsFee', // 铁运（年约需求） - soc报关费
  'socInsurance', // 铁运（年约需求） - soc保险费
  'cocDeclareCustomsFee', // 铁运（年约需求） - coc报关费
  'cocInsurance', // 铁运（年约需求） - coc保险费
  'stationDeclareCustomsFee', // 铁运（年约需求） - 门到站报关费
  'stationInsurance', // 铁运（年约需求） - 门到站保险费
  'vehicleFee', // 铁运（年约需求） - 压车费
  'detentionOverSixtyDayFee', // 铁运（年约需求） - 超免箱期60天后费用标准
  'returnFee', // 铁运（年约需求） - 返空费
  'customsInspectionFee', // 铁运（年约需求） - 海关检查费
  'otherFee', // 铁运（年约需求） - 其他费用
  'actualTimeliness' // 实际时效性
]

// 物流数字框字段
export const logisticsNumberEditFields = [
  'twentyGpBaseFee', // 海运（年约需求） - 基础海运费(20GP)
  'fortyGpBaseFee', // 海运（年约需求） - 基础海运费(40GP)
  'fortyHqBaseFee', // 海运（年约需求） - 基础海运费(40HQ)
  'twentyGpBaf', // 海运（年约需求） - BAF(20GP)
  'fortyGpBaf', // 海运（年约需求） - BAF(40GP)"
  'fortyHqBaf', // 海运（年约需求） - BAF(40HQ)
  'insurance', // 海运（年约需求） - 安保费
  'ams', // 海运（年约需求） - AMS
  'css', // 海运（年约需求） - CSS
  'msc', // 海运（年约需求） - MSC
  'cas', // 海运（年约需求） - CAS
  'arb', // 海运（年约需求） - ARB
  'polThcFee', // 海运（年约需求） - 起运港THC
  'polDocTlxFee', // 海运（年约需求） - 起运港DOC/TLX
  'sealFee', // 海运（年约需求） - 封条费
  'intimidateFee', // 海运（年约需求） - 打单费用
  'exportServiceFee', // 海运（年约需求） - 出口服务费
  'shipCertificateFee', // 海运（年约需求） - 船证费
  'sealFee', // 海运（年约需求） - 封条费
  'vgm', // 海运（年约需求） - VGM
  'sundryFee', // 海运（年约需求） - 杂费
  'portCharges', // 海运（年约需求） - 港务费
  'steelLockFee', // 海运（年约需求） - 钢锁费
  'csc', // 海运（年约需求） - CSC
  'plw', // 海运（年约需求） - PLW
  'isps', // 海运（年约需求） - ISPS
  'podThcFee', // 海运（年约需求） - 目的港THC费用
  'podDocTlxFee', // 海运（年约需求） - 目的港DOC/TLX
  'isf', // 海运（年约需求） - ISF
  'psf', // 海运（年约需求） - PSF
  'thd', // 海运（年约需求） - THD
  'ens', // 海运（年约需求） - ENS
  'twentyGpPodTrailerFee', // 海运（年约需求） - 拖车费(20GP)
  'fortyGpPodTrailerFee', // 海运（年约需求） - 拖车费(40GP)
  'fortyHqPodTrailerFee', // 海运（年约需求） - 拖车费(40HQ)
  'declareCustomsFee', // 海运（年约需求） - 报关费
  'rentalContainerFee', // 铁运（年约需求） - 租箱费
  'trailerFee', // 铁运（年约需求） - 拖车费
  'replacementStorageFee', // 铁运（年约需求） - 换装入库费
  'domesticRailwayFreightFee', // 铁运（年约需求） - 内贸铁路运费
  'railwayFreightFee', // 铁运（年约需求） - 铁路运费
  'qbjAroundFee', // 铁运（年约需求） - 青白江绕园费
  'socDeclareCustomsFee', // 铁运（年约需求） - soc报关费
  'socInsurance', // 铁运（年约需求） - soc保险费
  'cocDeclareCustomsFee', // 铁运（年约需求） - coc报关费
  'cocInsurance', // 铁运（年约需求） - coc保险费
  'stationDeclareCustomsFee', // 铁运（年约需求） - 门到站报关费
  'stationInsurance' // 铁运（年约需求） - 门到站保险费
  // 'vehicleFee', // 铁运（年约需求） - 压车费
  // 'detentionOverSixtyDayFee', // 铁运（年约需求） - 超免箱期60天后费用标准
  // 'returnFee', // 铁运（年约需求） - 返空费
  // 'customsInspectionFee', // 铁运（年约需求） - 海关检查费
  // 'otherFee' // 铁运（年约需求） - 其他费用
]
// 物流海运整数数字框
export const logisticsIntEditFields = [
  'podOutFreeCabinetDay', // 海运（年约需求） - 目的港场外免柜天数
  'podInFreeCabinetDay', // 海运（年约需求） - 目的港场内免柜天数
  'podFreeCabinetDay' // 海运（年约需求） - 目的港免柜+免堆天数
  // 'requireTimeliness' // 海运（年约需求） - 需求时效性
  // 'actualTimeliness' // 海运（年约需求） - 实际时效性
]
