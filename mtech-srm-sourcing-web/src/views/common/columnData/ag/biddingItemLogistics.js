import { useFiltering } from '@/utils/ej/select'
import { addArrTextField, makeTextFields, filteringByText } from '../utils'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'

export function columnData({ prefix = 'biddingItemLogisticsDTO.', currencyList = [] }) {
  const currencyListCN = addArrTextField(currencyList, 'currencyCode', 'currencyName')

  return [
    // 起运机场
    {
      field: prefix + 'startAirport',
      option: 'customEdit',
      editConfig: {
        type: 'text'
      }
    },
    // eta ETA
    {
      field: prefix + 'eta',
      option: 'customEdit',
      editConfig: {
        type: 'datetime',
        props: {
          format: 'yyyy-MM-dd HH:mm:ss',
          'time-stamp': true,
          'show-clear-button': false,
          min: new Date()
        }
      }
    },
    // etd ETD
    {
      field: prefix + 'etd',
      option: 'customEdit',
      editConfig: {
        type: 'datetime',
        props: {
          format: 'yyyy-MM-dd HH:mm:ss',
          'time-stamp': true,
          'show-clear-button': false,
          min: new Date()
        }
      }
    },
    // airFee 航空运费
    {
      field: prefix + 'airFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // startAirportHandleFee 机场操作费（起运港）
    {
      field: prefix + 'startAirportHandleFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // manifestFee 舱单录入费
    {
      field: prefix + 'manifestFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // billDeclarationFee 买单报关费
    {
      field: prefix + 'billDeclarationFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // customsClearanceFee 清关费
    {
      field: prefix + 'customsClearanceFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // endAirportHandleFee 机场操作费（目的港）
    {
      field: prefix + 'endAirportHandleFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // distributionFee 配送费
    {
      field: prefix + 'distributionFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // otherFeeCurrencyCode 其他费用币种编码 (只显示)
    {
      field: prefix + 'otherFeeCurrencyCode'
    },
    // otherFeeCurrencyCode 其他费用币种名称
    {
      field: prefix + 'otherFeeCurrencyName',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },
    // agencyFee 代理费（报关代理）
    {
      field: prefix + 'agencyFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // fileFee 文件费用
    {
      field: prefix + 'fileFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },

    // usdTotalFeeSum 总计费用USD
    {
      field: prefix + 'usdTotalFeeSum',
      editable: false,
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          disabled: true,
          readonly: true
        }
      }
    },
    // cnyTotalFeeSum 总计费用CNY
    {
      field: prefix + 'cnyTotalFeeSum',
      editable: false,
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          disabled: true,
          readonly: true
        }
      }
    },
    // totalFee 总计费用（费用USD）
    {
      field: prefix + 'totalFee',
      editable: false,
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          disabled: true,
          readonly: true,
          ...PRICE_EDIT_CONFIG
        }
      }
    },

    // agencyFeeCurrencyCode 代理费币种编码
    {
      field: prefix + 'agencyFeeCurrencyCode',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyCode'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },
    // agencyFeeCurrencyName 代理费币种
    {
      field: prefix + 'agencyFeeCurrencyName',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // 总计费用币种编码
    {
      field: prefix + 'totalFeeCurrencyCode',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        props: {
          fields: makeTextFields('currencyCode'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },
    //  总计费用币种名称
    {
      field: prefix + 'totalFeeCurrencyName',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // airFeeCurrencyCode 航空运费币种编码
    {
      field: prefix + 'airFeeCurrencyCode',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyCode'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },
    // airFeeCurrencyName 航空运费币种
    {
      field: prefix + 'airFeeCurrencyName',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // transportFeeCurrencyCode 运费币种编码
    {
      field: prefix + 'transportFeeCurrencyCode',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyCode'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },
    // transportFeeCurrencyName 运费币种
    {
      field: prefix + 'transportFeeCurrencyName',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // startPortFeeCode 起运港杂费币种编码
    {
      field: prefix + 'startPortFeeCode',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyCode'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },
    // startPortFeeName 起运港杂费币种
    {
      field: prefix + 'startPortFeeName',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // endPortFeeCode 目的港杂费币种编码
    {
      field: prefix + 'endPortFeeCode',
      option: 'customEdit'
      // editConfig: {
      //   type: 'select',
      //   props: {
      //     'show-clear-button': true,
      //     fields: makeTextFields('currencyCode'),
      //     dataSource: currencyListCN,
      //     'allow-filtering': true,
      //     filtering: useFiltering(filteringByText)
      //   }
      // }
    },
    // endPortFeeName 目的港杂费币种
    {
      field: prefix + 'endPortFeeName',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // twentyGpQuantity 20GP可提供数量
    {
      field: prefix + 'twentyGpQuantity',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // fortyGpQuantity 40GP可提供数量
    {
      field: prefix + 'fortyGpQuantity',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // fortyHcQuantity 40HC可提供数量
    {
      field: prefix + 'fortyHcQuantity',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // fortyFiveHcQuantity 45HC可提供数量
    {
      field: prefix + 'fortyFiveHcQuantity',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // twentyGpFee 20GP海运费用
    {
      field: prefix + 'twentyGpFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },
    // fortyGpFee 40GP海运费用
    {
      field: prefix + 'fortyGpFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },
    // fortyHcFee 40HC海运费用
    {
      field: prefix + 'fortyHcFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },
    // fortyFiveHcFee 45HC海运费用
    {
      field: prefix + 'fortyFiveHcFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },
    // twentyGpThcFee 20GPTHC费用
    {
      field: prefix + 'twentyGpThcFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },
    // fortyGpThcFee 40GPTHC费用
    {
      field: prefix + 'fortyGpThcFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },
    // fortyHcThcFee 40HCTHC费用
    {
      field: prefix + 'fortyHcThcFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },
    // fortyFiveHcThcFee 45HCTHC费用
    {
      field: prefix + 'fortyFiveHcThcFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },

    {
      field: prefix + 'doc',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    {
      field: prefix + 'seal',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    {
      field: prefix + 'eir',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // otherFee 其他费用
    {
      field: prefix + 'otherFee',
      option: 'customEdit',
      editConfig: {
        props: {
          ...PRICE_EDIT_CONFIG
        },
        type: 'number'
      }
    },
    // seaTransportFeeTotal 海运费总价
    {
      field: prefix + 'seaTransportFeeTotal',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // landTransportMode 陆运模式
    {
      field: prefix + 'landTransportMode',
      option: 'customEdit',
      editConfig: {
        type: 'text'
      }
    },
    // landTransportFeePrice 陆运费报价
    {
      field: prefix + 'landTransportFeePrice',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // friendshipDrivingFee 友谊关代驾费
    {
      field: prefix + 'friendshipDrivingFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // nightDrivingFee 代驾压夜费
    {
      field: prefix + 'nightDrivingFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // nightCarFee 车辆滞留压夜费
    {
      field: prefix + 'nightCarFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // declarationFee 报关费
    {
      field: prefix + 'declarationFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // transportEffective 运输时效
    {
      field: prefix + 'transportEffective',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // loadingUnloadingMode 装卸方式
    {
      field: prefix + 'loadingUnloadingMode',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // completeVehiclePrice 整车总价（1天压夜）
    {
      field: prefix + 'completeVehiclePrice',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // transportFee 运费
    {
      field: prefix + 'transportFee',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // quoteEffectiveStartDate 报价有效期从
    {
      field: prefix + 'quoteEffectiveStartDate',
      option: 'customEdit',
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false
          // min: new Date(),
        }
      }
    },
    // quoteEffectiveEndDate 报价有效期至
    {
      field: prefix + 'quoteEffectiveEndDate',
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false
          // min: new Date(),
        }
      }
    },
    // 汇率
    {
      field: prefix + 'exchangeRate',
      option: 'customEdit',
      editConfig: {
        type: 'text',
        props: {
          disabled: true,
          readonly: true
        }
      }
    },
    // bidDetention 场外免箱期
    {
      field: prefix + 'bidDetention',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // bidStorage 境外免堆
    {
      field: prefix + 'bidStorage',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // bidDemurrage 场内免箱期
    {
      field: prefix + 'bidDemurrage',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        min: 0
      }
    },
    // 可提供货柜量
    {
      field: prefix + 'cabinetQuantity',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    // 单价（All in） USD/HQ
    {
      field: prefix + 'untaxedUnitPrice',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    {
      field: prefix + 'actualContainerQty',
      headerText: '实际分配柜量',
      option: 'customEdit',
      editConfig: {
        type: 'number',
        min: 0
      }
    }
    // // 物流意见
    // {
    //   field: prefix + 'suggestOpinionRemark',
    //   option: 'customEdit',
    //   editable: true
    // }
  ]
}
