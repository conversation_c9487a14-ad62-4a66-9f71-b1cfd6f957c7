/**
 *  采方
 * 编辑相关字段整理
 */
export const tableNameMap = {
  rfx_item: {
    key: ''
  },
  rfx_bidding_item: {
    key: '' // 采方，数据不在biddingItemDTO中
  },
  rfx_item_logistics: {
    key: 'itemLogisticsResponse'
  },
  rfx_item_ext: {
    key: 'itemExtMap'
  },
  mt_supplier_rfx_item_ext: {
    key: 'itemExtMap'
  },
  rfx_item_die: {
    key: 'itemDieResponse'
  },
  rfx_bidding_item_logistics: {
    key: 'biddingItemLogisticsResponse'
  },
  mt_rfx_annual_logistics_sea_item: {
    key: 'seaItemResponse'
  },
  logistics_railway_item: {
    key: 'annualLogisticsRailwayItemDTO'
  },
  annual_logistics_transport: {
    key: 'annualLogisticsTrunkItem'
  }
}
// 评标/定标
export const quoTableNameMap = {
  rfx_item: {
    key: ''
  },
  rfx_bidding_item: {
    key: '' // 采方，数据不在biddingItemDTO中
  },
  rfx_item_logistics: {
    key: 'itemLogisticsResponse'
  },
  rfx_item_ext: {
    key: 'itemExtMap'
  },
  mt_supplier_rfx_item_ext: {
    key: 'itemExtMap'
  },
  rfx_item_die: {
    key: 'itemDieResponse'
  },
  rfx_bidding_item_logistics: {
    key: 'biddingItemLogisticsResponse'
  },
  mt_rfx_annual_logistics_sea_item: {
    key: 'rfxAnnualLogisticsSeaDTO'
  },
  mt_rfx_annual_logistics_sea_bidding_item: {
    key: 'rfxAnnualLogisticsSeaDTO'
  },
  logistics_railway_item: {
    key: 'annualLogisticsRailwayItemDTO'
  },
  bidding_annual_railway: {
    key: 'annualLogisticsRailwayItemDTO'
  },
  annual_logistics_transport: {
    key: 'annualLogisticsTrunkItem'
  },
  bidding_annual_trunk: {
    key: 'annualLogisticsTrunkItem'
  }
}

// 寻源类型 => tableName （针对于物流阶梯使用）
export const logisticsTableNameMap = {
  trunk_transport_annual: {
    tableName: 'annual_logistics_transport',
    resKey: 'annualLogisticsTrunkItem'
  },
  sea_transport_annual: {
    tableName: 'mt_rfx_annual_logistics_sea_item',
    resKey: 'seaItemResponse'
  },
  railway_transport_annual: {
    tableName: 'logistics_railway_item',
    resKey: 'annualLogisticsRailwayItemDTO'
  }
}
// 不允许编辑的字段 （采方禁用字段可整理到此，如果是公共禁用，可以直接配置文件强控）
export const notAllowEditFields = [
  'porLineNo', //基础信息-采购申请行号
  'lineNo', //基础信息-行号
  'currencyCode', //成本因子-币种编码
  'bidCurrencyCode', //价格信息-币种编码
  'baseCurrencyCode', //价格信息-本位币
  'supplierCode', //供应商信息-供应商编码
  'referItemSupplierCode', //参考物料-参考物料供应商编码
  'siteId', //基础信息-工厂id
  'siteName', //基础信息-工厂名称
  'itemId', //物料信息-物料id  成本因子-成本因子编码id  BOM信息-物料id  todo多个
  'categoryId', //物料信息-品类id
  'skuCode', //物料信息-SKU编码
  'skuId', //物料信息-SKUId
  'taxRateCode', //成本因子-税率编码
  'taxRateValue', //价格信息-税率值
  'bidTaxRateCode', //价格信息-税率编码
  'requireUserId', //基础信息-需求人id
  'unitId', //成本因子-单位id  物料信息-基本单位id  BOM信息-单位id  todo多个
  'unitCode', //成本因子-单位id  物料信息-基本单位id  BOM信息-单位id  todo多个
  'companyCode', //基础信息-公司code
  'companyId', //基础信息-公司id
  'purUnitId', //物料信息-订单单位id
  'purUnitCode', //物料信息-订单单位id
  'porId', //基础信息-采购申请Id
  'requireDeptId', //基础信息-需求部门id
  'requireDeptCode', //基础信息-需求部门编码
  'costModelId', //物料信息-成本模型id
  // 'temporaryItemCode', //临时物料编码
  'spec', //规格描述
  'budgetTotalPriceTaxed', //预算单价含税
  'purGroupName', // 采购组名称
  'costEstimation', // 成本测算
  'costModelEstimatePrice', // 测算价格
  'stepValue', //阶梯数量
  'countryCode', // 国家编码
  'refUntaxedUnitPrice', //参考单价(未税)
  'pcbCode', //pcb版本号
  'logisticsMethodCode', // 物流方式编码
  'totalQty', // 总数量
  'stepTrunkQty', // 总数量
  'logisticsMethodName', // 物流方式
  'requirementType' // 需求类型
]
// 不允许编辑字段 - kt
export const ktNotAllowEditFields = [
  'priceUnitName' // 价格单位
]
// 不允许编辑字段 - 成本因子
export const costFactorNotAllowEditFields = [
  'itemCode',
  'itemName',
  'unitCode',
  'unitName',
  'minQuotePercent',
  'minQuotePercent'
]

// 字段 - 数字框 - 通用数字框（2位小数）
export const generalNumberEditFields = [
  'quota', //数量信息-配额
  // 'shareQuantity', //模具信息-实际分摊数量（上传SAP）
  'adviseMinPurQuantity', //数量信息-建议最小采购量
  // 'planQuantity', //模具信息-规划量
  'subjectTotal', //财务信息-科目总额
  'minPurQuantity', //数量信息-最小采购量
  'referItemMinPur', //参考物料-参考物料最小采购量
  'singleQuantity', //数量信息-单机用量
  'yearMinQuantity', //数量信息-年度最小用量
  'yearAverageQuantity', //数量信息-年度平均用量
  'minPackageQuantity', //包装信息-最小包装数量
  'yearMaxQuantity', //数量信息-年度最大用量
  'requireDelivery', //时间信息-要求交期（天）
  'adviseMinPackageQuantity', //包装信息-建议最小包装数量
  'forecastQuantity', //数量信息-预测采购量
  'specVolumeWeight', //物流信息-体积重（KG）
  'specVolume', //物流信息-体积（CBM）
  'specGrossWeight', //物流信息-毛重（KG）
  'transportQuantity', //物流信息-件数（需求量）
  'adviseLeadTime', // 建议L/T
  'adviseUnconLeadTime', // 建议无条件L/T
  'leadTime', // L/T
  'unconditionalLeadTime' // 无条件L/T
]
// 字段 - 数字框 - 整数数字框
export const integerNumberEditFields = [
  'requireQuantity' // 需求数量
  // 'planQuantity', //规划量
  // 'shareQuantity', //实际分摊量
  // 'alreadyShareQuantity' //已分摊数量
]
// 字段 - 数字框 - 5位小数数字框
export const fiveDecimalNumberEditFields = [
  'startingPrice' // 起价（未税）
]
// 字段 - 数字框 - 价格数字框 (2位小数)
export const priceNumberEditFields = [
  'packageFee', //包装费
  'transportCost', //价格信息-运费
  'processCost', //价格信息-加工费
  'diePriceUnTaxed', //价格信息-模具价格（未税)
  'diePriceTaxed', //价格信息-模具价格（含税）
  'notScreenPrice', //价格信息-非屏价格（含税）
  'untaxedTotalPrice', //价格信息-总价（未税）
  'taxedTotalPrice', //价格信息-总价（含税）
  'declinePercent', //价格信息-降幅%
  'dargaining', //价格信息-还价
  'discussPrice', //价格信息-议价
  'lastQuoteUntaxed', //价格信息-上次报价（未税）
  'lastQuoteTaxed', //价格信息-上次报价（含税）
  'untaxedUnitPrice', //价格信息-单价（未税）
  'conversionRate', //物料信息-转换率   todo
  'exchangeRate', //价格信息-汇率   todo
  'lastCostUntaxed', //模具信息-上次模具费用（未税）
  'lastCostTaxed', //模具信息-上次模具费用（含税）
  'bomProductPrice', //BOM信息-BOM成品价格
  'minPricePercent', //成本因子-价格下限%
  'maxPricePercent', //成本因子-价格上限%
  'lastShareCostUntaxed', //模具信息-上次模具分摊费用（未税)
  'tconPrice', //BOM信息-T-CON价格
  'lastShareCostTaxed', //模具信息-上次模具分摊费用（含税）
  'minQuoteRange', //价格信息-最小报价幅度
  'basicPrice', //成本因子-基准价
  'sharePriceUntaxed', //模具信息-模具分摊价格（未税）
  'minQuotePercent', //价格信息-报价下限
  'screenPrice', //BOM信息-屏价格
  'maxQuotePercent', //价格信息-报价上限
  'sharePriceTaxed', //模具信息-模具分摊价格（含税）
  'distributionQuantity', //数量信息-分配数量
  'budgetUnitPriceUntaxed', //价格信息-预估单价（未税）  财务信息-预算单价（未税）   todo多个
  'budgetUnitPriceTaxed', //价格信息-预估单价（含税）  财务信息-预算总价（含税）  todo多个
  'budgetTotalPriceTaxed', //财务信息-预算总价（含税）
  'referItemUnitPriceTaxed', //参考物料-参考物料单价（含税）
  'marketPrice', //价格信息-市场价格
  'historyPrice', //价格信息-历史价格
  'budgetTotalPriceUntaxed', //财务信息-预算总价（未税）
  'purchaseTargetPrice', //价格信息-采购目标价
  'financeTargetPrice', //价格信息-财务目标价
  'conversionRate', // 物料信息 - 转换率,
  'refScreenPrice', // 参考屏价格
  'refTconPrice', // 参考T-CON价格
  'refTotalPrice', // 参考总价
  'refTaxedUnitPrice', // 参考单价（含税）
  'refUntaxedUnitPrice', // 参考单价（未税）
  'referItemUnitPriceUntaxed' //参考物料单价（未税）
]

// 字段 - 日期框
export const dateEditFields = [
  'effectiveEndDate', //成本因子-失效日期
  'adviseEffectiveStartDate', //时间信息-建议报价有效期从
  'adviseEffectiveEndDate', //时间信息-建议报价有效期至
  'referItemQuoteEndDate', //参考物料-参考物料报价有效期至
  'referItemQuoteStartDate', //参考物料-参考物料报价有效期从
  'quoteEffectiveEndDate', //时间信息-报价有效期至
  'quoteEffectiveStartDate', //时间信息-报价有效期从
  'requireDate' //时间信息-需求日期
  // 'expectArriveTime', //物流信息-期望送达时间
  // 'lastDeliveryTime', //物流信息-最晚发运时间
  // 'goodsFinishTime' //物流信息-货好时间
]

// 字段 - 布尔下拉框
export const booleanEditField = [
  'customized', //物料信息-是否定制
  'shareUpload', //模具信息-分摊是否上传
  'stepQuote', //价格信息-是否阶梯报价
  'modifyDie', //模具信息-是否改模
  'costModelQuote' //物料信息-是否成本模型报价  成本因子-是否成本模型报价   todo多个
]
