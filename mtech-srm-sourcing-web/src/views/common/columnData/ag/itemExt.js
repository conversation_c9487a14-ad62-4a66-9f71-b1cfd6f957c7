/*
 * itemExtMap
 *（目前整理阶梯相关字段，后续持续改进）
 **/
import { i18n } from '@/main.js'
import { getValueByPath } from '@/utils/obj'
import { dateUtils } from '@mtech-common/utils'
// 价格来源字段转换
const referChannelMap = {
  '-1': '',
  0: i18n.t('手工定价'),
  2: i18n.t('价格记录')
}

export function columnData({ prefix = 'itemExtMap.', isPur = false, dictItems = {}, ...args }) {
  return [
    {
      field: prefix + 'deliveryPlace',
      headerText: i18n.t('直送地'),
      option: 'customEdit',
      editConfig: {
        type: 'multiSelect',
        props: {
          'show-clear-button': true,
          fields: { value: 'dictName', text: 'dictName' },
          placeholder: i18n.t('选择直送地'),
          dataSource: dictItems['DELIVERY_PLACE']
        }
      },
      valueFormatter: (params) => {
        let _deliveryPlace = params.data?.itemExtMap?.deliveryPlace
        return Array.isArray(_deliveryPlace) ? _deliveryPlace.join(',') : _deliveryPlace
      }
    },
    {
      field: prefix + 'requireQuantity',
      headerText: i18n.t('需求数量'),
      editable: isPur //强控 （整理阶梯修改）
    },
    {
      field: prefix + 'drawingUrl',
      headerText: i18n.t('附件')
    },
    {
      field: prefix + 'referChannel',
      headerText: i18n.t('价格来源'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            // { value: -1, text: i18n.t('无') },
            { value: 0, text: i18n.t('手工定价') },
            { value: 2, text: i18n.t('价格记录') }
          ],
          placeholder: i18n.t('选择价格来源')
        }
      },
      valueFormatter: (params) => {
        return referChannelMap[params.value]
      }
    },
    // {
    //   field: prefix + 'budgetUnitPriceTaxed',
    //   headerText: i18n.t('预算单价(含税)'),
    //   editable: false
    // },
    {
      field: prefix + 'adviseEffectiveStartDate',
      headerText: i18n.t('建议报价有效期从'),
      width: 150
    },
    // 销售凭证
    {
      field: prefix + 'salesVouchers',
      headerText: i18n.t('销售凭证'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 'PANELROWA1', text: 'PANELROWA1' },
            { value: 'empty', text: i18n.t('-') }
          ],
          placeholder: i18n.t('选择销售凭证')
        }
      }
    },
    {
      field: prefix + 'requireEndDate',
      headerText: i18n.t('需求截止日期'),
      option: 'customEdit',
      valueFormatter: (params) => {
        let cellVal = params.data?.itemExtMap?.requireEndDate
        const fmt = 'YYYY-MM-DD'
        if (typeof cellVal === 'object' && cellVal !== null) {
          return dateUtils(cellVal).format(fmt)
        } else if (/^\d{13}$/.test(cellVal)) {
          return dateUtils(new Date(Number(cellVal))).format(fmt)
        } else if (cellVal && typeof cellVal === 'string') {
          return dateUtils(new Date(cellVal)).format(fmt)
        }
        return cellVal
      },
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false,
          min: new Date()
        }
      }
    },
    {
      field: prefix + 'minQuoteRange',
      headerText: i18n.t('最小竞价幅度'),
      option: 'customEdit',
      valueFormatter: (params) => {
        const cellVal = getValueByPath(params.data, 'itemExtMap.minQuoteRange')
        const typeVal = getValueByPath(params.data, 'itemExtMap.minQuoteRangeType')
        return typeVal === 1 ? (cellVal || 0) + '%' : cellVal
      }
    },
    {
      field: prefix + 'minQuoteRangeType',
      headerText: i18n.t('最小报价幅度类型'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 0, text: i18n.t('按金额') },
            { value: 1, text: i18n.t('按比例') }
          ],
          placeholder: i18n.t('选择最小报价幅度类型')
        }
      }
    },
    {
      field: prefix + 'screenCode',
      headerText: i18n.t('屏编码'),
      option: 'customEdit',
      width: 200
    },
    {
      field: prefix + 'tconCode',
      headerText: i18n.t('T-CON编码'),
      option: 'customEdit',
      width: 200
    },
    {
      field: prefix + 'referItemCode',
      headerText: i18n.t('参考物料编码'),
      width: 200,
      option: 'customEdit',
      editConfig: ['module_out_buy', 'module_out_going'].includes(args.sourcingObjType)
        ? {
            type: 'selectSearch',
            props: {
              rfxId: args?.rfxId,
              sourcingType: args?.sourcingType,
              sourcingObjType: args?.sourcingObjType,
              source: args?.source
            }
          }
        : { type: 'text' }
    },
    {
      field: prefix + 'referItemName',
      headerText: i18n.t('参考物料名称'),
      width: 200,
      option: 'customEdit',
      editConfig: {
        type: 'selectSearch', //带弹框的下拉框
        props: {
          rfxId: args?.rfxId,
          sourcingType: args?.sourcingType,
          sourcingObjType: args?.sourcingObjType,
          source: args?.source
        }
      }
    }
    // {
    //   field: prefix + 'countryName',
    //   headerText: i18n.t('国家'),
    //   width: 200,
    //   option: 'customEdit',
    //   editConfig: {
    //     type: 'cellRemoteSelect',
    //     props: {
    //       url: '/masterDataManagement/tenant/country/paged-query',
    //       searchFields: ['shortName', 'countryCode'],
    //       fields: { text: 'countryCode-shortName', value: 'shortName' }
    //     }
    //   },
    //   valueFormatter: (params) => {
    //     return params.data.itemExtMap?.countryName
    //       ? params.data.itemExtMap?.countryCode + '-' + params.data.itemExtMap?.countryName
    //       : ''
    //   }
    // }
  ]
}
