/**
 * 物流相关字段
 *
 */
import { i18n } from '@/main.js'
export function columnData({ prefix = 'seaItemResponse.', dictItems = {} }) {
  return [
    // 出口费报价单位
    {
      field: prefix + 'exportFeeQuoteUnit',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'dictCode', text: 'dictName' },
          dataSource: dictItems['LOGISTICS_ANNUAL_POL_QUOTE_UNIT'] || [],
          placeholder: i18n.t('选择报价单位')
        }
      }
    },
    // 起运港报价单位
    {
      field: prefix + 'polQuoteUnit',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'dictCode', text: 'dictName' },
          dataSource: dictItems['LOGISTICS_ANNUAL_POL_QUOTE_UNIT'] || [],
          placeholder: i18n.t('选择报价单位')
        }
      }
    },
    // 目的港报价单位
    {
      field: prefix + 'podQuoteUnit',
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'dictCode', text: 'dictName' },
          dataSource: dictItems['LOGISTICS_ANNUAL_POL_QUOTE_UNIT'] || [],
          placeholder: i18n.t('选择报价单位')
        }
      }
    }
  ]
}
