/**
 * 物流铁运相关字段
 *
 */
import { i18n } from '@/main.js'

export function useColumn(detailInfo) {
  return [
    'sea_transport', // 海运
    'air_transport', // 空运
    'railway_transport', // 铁路
    'land_transport', // 陆运
    'trailer_transport' // 拖车
  ].includes(detailInfo.sourcingObjType)
}

export function columnData({ prefix = 'annualLogisticsTrunkItemDTO.', dictItems = {} }) {
  return [
    {
      field: prefix + 'logisticsMethodName',
      headerText: i18n.t('物流方式'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { text: 'dictName', value: 'dictCode' },
          'allow-filtering': true,
          dataSource: dictItems.LOGISTICS_METHOD_CODE || []
        }
      }
    },
    {
      field: prefix + 'requirementType',
      headerText: i18n.t('需求类型'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { text: 'dictName', value: 'dictCode' },
          'allow-filtering': true,
          dataSource: dictItems.LOGISTICS_DEMAND_TYPE || []
        }
      }
    }
  ]
}
