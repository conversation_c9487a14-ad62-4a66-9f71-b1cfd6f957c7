/*
 * 报价明细
 *（目前整理阶梯相关字段，后续持续改进）
 **/
import { i18n } from '@/main.js'
import { useFiltering } from '@/utils/ej/select'
import { addArrTextField, makeTextFields, filteringByText } from '../utils'
import { formatTime } from '@/utils/utils'
const MAX_SAFE_INTEGER = 999999999999
// 供应商列表字段转换
const priceStatusMap = {
  0: i18n.t('未提交'),
  1: i18n.t('已报价'),
  2: i18n.t('已接收'),
  3: i18n.t('已拒绝'),
  4: i18n.t('已议价'),
  5: i18n.t('已中标'),
  6: i18n.t('待定点'),
  7: i18n.t('议价中')
}
// 价格单位 - 通采
const generalPriceUnitList = [
  { text: '1', value: '1' },
  { text: '1000', value: '1000' }
]
// 价格单位 - 非采
const priceUnitList = [
  { text: i18n.t('元'), value: '1' },
  { text: i18n.t('万元'), value: '0.0001' }
]

// 列配置
export function columnData({
  prefix = 'biddingItemDTO.',
  companyCode = '',
  ktFlag = false,
  quotedPriceData = {},
  purUnitList = [],
  currencyList = [],
  taxList = [],
  dictItems = {},
  isFc = false
}) {
  // const currencyListCN = addArrTextField(currencyList, 'currencyCode', 'currencyName')
  // const taxListCN = addArrTextField(taxList, 'taxItemCode', 'taxItemName')
  const purUnitListCN = addArrTextField(purUnitList, 'unitCode', 'unitName')
  return [
    {
      field: prefix + 'priceStatus',
      headerText: i18n.t('状态'),
      valueFormatter: (params) => {
        return !params.value && params.value !== 0 ? i18n.t('未报价') : priceStatusMap[params.value]
      }
    },
    {
      field: prefix + 'taxedUnitPrice',
      width: 120,
      headerText: i18n.t('单价含税'),
      editable: () => {
        return ktFlag || quotedPriceData?.sourcingObjType === 'single_module'
      }, //除了KT || 单独模具是可以编辑的
      cellClass: () => {
        return ktFlag || quotedPriceData?.sourcingObjType === 'single_module'
          ? ''
          : 'singleCellDisable'
      }
    },
    {
      field: prefix + 'untaxedUnitPrice',
      width: 120,
      headerText: i18n.t('单价未税'), // 如果配置了成本模型（成本模型为是） || 单独模具 || 整机模组外购 || 空调，单价未税不可以编辑
      option: 'customEdit',
      editable: (params) => {
        return (
          !ktFlag &&
          (!params.data?.costModelQuote ||
            ['single_module', 'module_out_going'].includes(quotedPriceData?.sourcingObjType))
        )
      },
      cellClass: (params) => {
        return !ktFlag &&
          (!params.data?.costModelQuote ||
            ['single_module', 'module_out_going'].includes(quotedPriceData?.sourcingObjType))
          ? ''
          : 'singleCellDisable'
      }
    },
    {
      field: prefix + 'bidHistoryPrice',
      width: 120,
      headerText: i18n.t('历史价格'),
      editable: false // 强控
    },
    {
      field: prefix + 'lastQuoteUntaxed',
      width: 140,
      headerText: i18n.t('上次报价(未税)'),
      editable: false // 强控
    },
    {
      field: prefix + 'lastQuoteTaxed',
      width: 140,
      headerText: i18n.t('上次报价(含税)'),
      editable: false // 强控
    },
    {
      field: prefix + 'bidPurUnitCode',
      headerText: i18n.t('订单单位编码'),
      editable: false // 强控
    },
    {
      field: prefix + 'bidPurUnitName',
      headerText: i18n.t('订单单位'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('unitName'),
          dataSource: purUnitListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      },
      valueFormatter: (params) => {
        return !params.value
          ? ''
          : params.data.biddingItemDTO.bidPurUnitCode +
              '-' +
              params.data.biddingItemDTO.bidPurUnitName
      }
    },
    {
      field: prefix + 'bidConversionRate',
      headerText: i18n.t('转换率'),
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    {
      field: prefix + 'taxedTotalPrice',
      headerText: i18n.t('总价含税'),
      editable: false // 强控
    },
    {
      field: prefix + 'untaxedTotalPrice',
      headerText: i18n.t('总价未税'),
      editable: false // 强控
    },
    {
      field: prefix + 'bidCurrencyCode',
      headerText: i18n.t('币种编码'),
      editable: false // 强控
    },
    {
      field: prefix + 'bidCurrencyName',
      headerText: i18n.t('币种名称'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyList,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      },
      valueFormatter: (params) => {
        return !params.value
          ? ''
          : params.data.biddingItemDTO.bidCurrencyCode +
              '-' +
              params.data.biddingItemDTO.bidCurrencyName
      }
    },
    {
      field: prefix + 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 'standard_price', text: i18n.t('标准价') },
            { value: 'mailing_price', text: i18n.t('寄售价') },
            { value: 'outsource', text: i18n.t('委外价') }
          ]
        }
      }
    },
    {
      field: prefix + 'quoteMode',
      headerText: i18n.t('报价生效方式'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 'in_warehouse', text: i18n.t('按照入库') },
            { value: 'out_warehouse', text: i18n.t('按出库') },
            { value: 'order_date', text: i18n.t('按订单日期') }
          ]
        }
      }
    },
    {
      field: prefix + 'quoteEffectiveStartDate',
      headerText: i18n.t('报价有效期从'),
      option: 'customEdit',
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'show-clear-button': false
        }
      }
    },
    {
      field: prefix + 'quoteEffectiveEndDate',
      headerText: i18n.t('报价有效期至'),
      option: 'customEdit',
      editable:
        quotedPriceData?.sourcingScenarios !== 'LOGISTICS' && companyCode === '1503' ? false : true, // 1503公司禁用
      cellClass: () => {
        return quotedPriceData?.sourcingScenarios !== 'LOGISTICS' && companyCode === '1503'
          ? 'singleCellDisable'
          : ''
      },
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'show-clear-button': false,
          min: new Date()
        }
      }
    },
    {
      field: prefix + 'bidTaxRateName',
      headerText: i18n.t('供方税率名称'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('taxItemName'),
          dataSource: taxList,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      },
      valueFormatter: (params) => {
        return !params.value
          ? ''
          : params.data.biddingItemDTO.bidTaxRateCode +
              '-' +
              params.data.biddingItemDTO.bidTaxRateName
      }
    },
    {
      field: prefix + 'bidTaxRateCode',
      headerText: i18n.t('供方税率编码'),
      editable: false // 强控
    },
    {
      field: prefix + 'bidTaxRateValue',
      headerText: i18n.t('供方税率值'),
      editable: false // 强控
    },
    {
      field: prefix + 'supplierItemCode',
      headerText: i18n.t('供应商编码')
    },
    {
      field: prefix + 'minPurQuantity',
      headerText: i18n.t('最小采购量'),
      option: 'customEdit'
    },
    {
      field: prefix + 'unconditionalLeadTime',
      headerText: i18n.t('无条件L/T'),
      option: 'customEdit'
    },
    {
      field: prefix + 'leadTime',
      headerText: i18n.t('L/T'),
      option: 'customEdit'
    },
    {
      field: prefix + 'minPackageQuantity',
      headerText: i18n.t('最小包装量'),
      option: 'customEdit'
    },
    {
      field: prefix + 'shipModeName',
      headerText: i18n.t('出货模式'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { text: 'dictName', value: 'dictName' },
          dataSource: dictItems?.SHIP_MODE
        }
      }
    },
    {
      field: prefix + 'shipModeCode',
      headerText: i18n.t('出货模式编码'),
      hide: true
    },
    {
      field: prefix + 'supplyTypeCode',
      headerText: i18n.t('屏供应方式编码'),
      hide: true
    },
    {
      field: prefix + 'supplyTypeName',
      headerText: i18n.t('屏供应方式'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { text: 'dictName', value: 'dictName' },
          dataSource: dictItems?.SUPPLY_TYPE
        }
      }
    },
    {
      field: prefix + 'screenPrice',
      headerText: i18n.t('屏价格'),
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 5
        }
      }
    },
    {
      field: prefix + 'screenPriceMonth',
      headerText: i18n.t('屏价格月份'),
      option: 'customEdit',
      editConfig: {
        type: 'dateMonth',
        props: {
          format: 'yMM',
          'show-clear-button': false,
          start: 'Year',
          depth: 'Year'
        }
      },
      valueFormatter: (params) => {
        if (params.value) {
          if (typeof params.value === 'string') {
            return params.value.substring(0, 6)
          } else {
            return formatTime(new Date(params.value), 'Ymm')
          }
        }
        return ''
      }
    },
    {
      field: prefix + 'priceUnitName',
      headerText: i18n.t('价格单位'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          dataSource: isFc ? priceUnitList : generalPriceUnitList
        }
      }
    }
  ]
}
