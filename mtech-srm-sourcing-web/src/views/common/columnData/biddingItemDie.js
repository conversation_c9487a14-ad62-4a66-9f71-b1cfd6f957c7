import { i18n } from '@/main.js'
import { createFmtDatetime } from '@/utils/ej/dataGrid/formatter'
import { getValueByPath } from '@/utils/obj'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'
export function columnData({ prefix = 'itemDieResponse.', handleSelectChange }) {
  const formatter = {
    select: function ({ field, editConfig }, item) {
      const cellVal = getValueByPath(item, field)
      const { value, text } = editConfig.fields
      return editConfig.dataSource.find((e) => e[value] === cellVal)?.[text] ?? cellVal
    },
    integer: function ({ field }, item) {
      return parseInt(getValueByPath(item, field) ?? 0)
    }
  }

  return [
    // 模具T1时间
    // 必须晚于当前时间，只填写日期，不需要时间（时、分、秒）
    {
      field: prefix + 'dieT1',
      headerText: i18n.t('模具T1时间'),
      formatter: createFmtDatetime('YYYY-MM-DD'),
      editConfig: {
        type: 'date',
        format: 'yyyy-MM-dd',
        'time-stamp': true,
        'show-clear-button': false,
        min: new Date()
      }
    },
    //模具类型
    {
      field: prefix + 'dieType',
      headerText: i18n.t('模具类型'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 0, text: '--未选择--' },
          { value: 1, text: i18n.t('基础模具') },
          { value: 2, text: i18n.t('复制模具') },
          { value: 3, text: i18n.t('基础模改模') },
          { value: 4, text: i18n.t('复制模改模') }
        ],
        placeholder: i18n.t('选择模具类型'),
        callback: handleSelectChange
      }
    },
    // 模具临时编码
    {
      field: prefix + 'dieTempCode',
      headerText: i18n.t('模具临时编码'),
      editConfig: {
        type: 'text'
      }
    },
    // 正式模具编码
    {
      field: prefix + 'dieFormalCode',
      headerText: i18n.t('正式模具编码'),
      editConfig: {
        type: 'text'
      }
    },
    // 基础模具号
    {
      field: prefix + 'basicDieCode',
      headerText: i18n.t('基础模具号'),
      editConfig: {
        type: 'text'
      }
    },

    // 规划量  //按条件只读
    {
      field: prefix + 'planQuantity',
      headerText: i18n.t('规划量'),
      formatter: formatter.integer,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number',
        min: 1
      }
    },
    // 实际分摊数量 //按条件只读
    {
      field: prefix + 'shareQuantity',
      headerText: i18n.t('实际分摊数量'),
      formatter: formatter.integer,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number',
        min: 1
      }
    },
    // 已分摊数量
    {
      field: prefix + 'alreadyShareQuantity',
      headerText: i18n.t('已分摊数量'),
      formatter: formatter.integer,
      editConfig: {
        ...PRICE_EDIT_CONFIG,
        type: 'number'
      }
    },
    // 累加至本次
    {
      field: prefix + 'addUp',
      headerText: i18n.t('累加至本次'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 0, text: i18n.t('否') },
          { value: 1, text: i18n.t('是') }
        ],
        placeholder: i18n.t('是否累加至本次'),
        callback: handleSelectChange
      }
    },
    //是否原始模具
    {
      field: prefix + 'originalDie',
      headerText: i18n.t('是否原始模具'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 0, text: i18n.t('否') },
          { value: 1, text: i18n.t('是') }
        ],
        placeholder: i18n.t('是否原始模具'),
        callback: handleSelectChange
      }
    },
    //是否分摊
    {
      field: prefix + 'shareFlag',
      headerText: i18n.t('是否分摊'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 0, text: i18n.t('否') },
          { value: 1, text: i18n.t('是') }
        ],
        disabled: true,
        readonly: true,
        placeholder: i18n.t('是否分摊'),
        callback: handleSelectChange
      }
    },
    //分摊是否上传
    {
      field: prefix + 'shareUpload',
      headerText: i18n.t('分摊是否上传'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 0, text: i18n.t('否') },
          { value: 1, text: i18n.t('是') }
        ],
        placeholder: i18n.t('分摊是否上传'),
        callback: handleSelectChange
      }
    },
    //是否生成模具审批表
    {
      field: prefix + 'needDieCreateApply',
      headerText: i18n.t('是否生成模具审批表'),
      formatter: formatter.select,
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 0, text: i18n.t('否') },
          { value: 1, text: i18n.t('是') }
        ],
        placeholder: i18n.t('是否生成模具审批表'),
        callback: handleSelectChange
      }
    },
    // 含税单价
    {
      field: prefix + 'untaxedUnitPrice',
      headerText: i18n.t('含税单价'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 未税单价
    {
      field: prefix + 'taxedUnitPrice',
      headerText: i18n.t('未税单价'),
      //allowEditing: false,
      editConfig: { type: 'number', disabled: true }
    },
    // 实际分摊价（含税）
    {
      field: prefix + 'realSharePriceTaxed',
      headerText: i18n.t('实际分摊价（含税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 实际分摊价（未税）
    {
      field: prefix + 'realSharePriceUntaxed',
      headerText: i18n.t('实际分摊价（未税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 分摊价格（未税）
    {
      field: prefix + 'sharePriceUntaxed',
      headerText: i18n.t('分摊价格（未税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 分摊价格（含税）
    {
      field: prefix + 'sharePriceTaxed',
      headerText: i18n.t('分摊价格（含税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 规划分摊价（含税）
    {
      field: prefix + 'planSharePriceTaxed',
      headerText: i18n.t('规划分摊价（含税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 规划分摊价（未税）
    {
      field: prefix + 'planSharePriceUntaxed',
      headerText: i18n.t('规划分摊价（未税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 上次规划分摊价（未税）
    {
      field: prefix + 'lastPlanSharePriceUntaxed',
      headerText: i18n.t('上次规划分摊价（未税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 上次规划分摊价（含税）
    {
      field: prefix + 'lastPlanSharePriceTaxed',
      headerText: i18n.t('上次规划分摊价（含税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 上次实际分摊价（未税）
    {
      field: prefix + 'lastRealSharePriceUntaxed',
      headerText: i18n.t('上次实际分摊价（未税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    //上次实际分摊价（含税）
    {
      field: prefix + 'lastRealSharePriceTaxed',
      headerText: i18n.t('上次实际分摊价（含税））'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 上次分摊后单价（含税）
    {
      field: prefix + 'lastSharePriceTaxed',
      headerText: i18n.t('上次分摊后单价（含税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 上次分摊后单价（未税）
    {
      field: prefix + 'lastSharePriceUntaxed',
      headerText: i18n.t('上次分摊后单价（未税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    //上次分摊费用（含税）
    {
      field: prefix + 'lastShareCostTaxed',
      headerText: i18n.t('上次分摊费用（含税）'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 上次分摊费用（未税)
    {
      field: prefix + 'lastShareCostUntaxed',
      headerText: i18n.t('上次分摊费用（未税)'),
      //allowEditing: false,
      editConfig: { ...PRICE_EDIT_CONFIG, type: 'number', disabled: true }
    },
    // 模具T1时间
    // 必须晚于当前时间，只填写日期，不需要时间（时、分、秒）
    {
      field: prefix + 'dieT1',
      headerText: i18n.t('模具T1时间'),
      formatter: createFmtDatetime('YYYY-MM-DD'), //有效期取消时分秒
      editConfig: {
        type: 'date',
        format: 'yyyy-MM-dd',
        'time-stamp': true,
        'show-clear-button': false,
        min: new Date(),
        callback: handleSelectChange
      }
    }
  ]
}
