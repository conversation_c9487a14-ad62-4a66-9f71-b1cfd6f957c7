import { stepNotMergeFieldNoPrefix } from '@/views/common/columnData/constant'
// 添加显示字段
export const addArrTextField = (arr, code, name) => {
  arr.forEach((e) => {
    e.__text = `${e[code]} - ${e[name]}`
  })
  return arr
}

export const addArrCodeField = (arr, code) => {
  arr.forEach((e) => {
    e.__text = `${e[code]}`
  })
  return arr
}

// 根据显示字段生成fields
export const makeTextFields = (value) => ({ value, text: '__text' })

// 通用过滤事件
export const filteringByText = function (e) {
  if (!e.text) {
    e.updateData(this.dataSource)
  } else {
    e.updateData(
      this.dataSource.filter(
        ({ __text }) => __text.toUpperCase().indexOf(e.text.toUpperCase()) > -1
      )
    )
  }
}

// 设置阶梯格式
export const setStepField = (columns, stepField) => {
  let _mergeArr = [] // 阶梯报价数量信息相关字段
  let _preNotMergeArr = []
  let _nextNotMergeArr = []
  if (!columns || columns?.length < 0) {
    return []
  }
  let _index = columns.findIndex((item) => item.field === 'stepNum')
  columns.forEach((item, i) => {
    // if (item.field === 'stepNum') return
    if (stepField.includes(item.field)) {
      item.width = 150
      item.allowFiltering = false
      _mergeArr.push(item)
    } else {
      if (i <= _index) {
        _preNotMergeArr.push(item)
      } else if (i > _index) {
        _nextNotMergeArr.push(item)
      }
    }
  })
  // 阶梯数量按照固定格式排序
  _mergeArr.sort((a, b) => stepField.indexOf(a.field) - stepField.indexOf(b.name))
  return [..._preNotMergeArr, ..._mergeArr, ..._nextNotMergeArr]
}
// 给每组数据的第一行添加rowSpan、isFirstLine
export const addRowSpan = (data) => {
  const result = data.reduce((acc, curr) => {
    let id = curr.id || '0'
    let sId = curr.supplierId || '0'
    let sCode = curr.supplierCode || 'a'
    let groupId = curr?.biddingItemDTO?.quoteGroupingKey
      ? curr.biddingItemDTO.quoteGroupingKey
      : curr.quoteGroupingKey
      ? curr.quoteGroupingKey
      : curr.itemGroupId
    let key =
      groupId !== '0'
        ? groupId + '__' + sId + '__' + sCode
        : groupId + '__' + sId + '__' + sCode + '__' + id
    const group = acc[key] || []
    group.push(curr)
    acc[key] = group
    return acc
  }, {})
  let _index = 1
  for (const groupId in result) {
    if (Object.prototype.hasOwnProperty.call(result, groupId)) {
      const group = result[groupId]
      if (groupId.split('__')[0] !== '0') {
        group[0].rowSpan = group.length
        group[0].isFirstLine = true
      }
      group[0].rowGroupIndex = _index++
    }
  }
  return data
}

// 子集样式优化
export const setChildWidth = (columns, defaultW, parentColumns) => {
  // 子集样式优化
  let _w = 100
  let _len = columns?.length || 0
  let _plen = parentColumns.length || 0
  defaultW = defaultW || '120'
  columns.forEach((item) => {
    let _itemW = item.width || defaultW
    _w += Number(_itemW)
  })
  _w = _len > _plen ? '100%' : _w + 'px'
  return _w
}

// 判断两个数据是否相同
export const judgeDataIsSame = (oldData, newData) => {
  return JSON.stringify(oldData) === JSON.stringify(newData)
}

const renameFieldAlias = {
  mt_supplier_rfx_header: 'rfx_header',
  mt_supplier_rfx_item: 'rfx_item',
  mt_supplier_rfx_item_ext: 'rfx_item_ext',
  mt_supplier_rfx_item_die: 'rfx_item_die',
  mt_supplier_bidding_item: 'bidding_item',
  mt_supplier_rfx_item_logistics: 'rfx_item_logistics',
  mt_supplier_bidding_item_logistics: 'bidding_item_logistics'
}
function getFieldAlias(field, tableName) {
  return field.replace(/.*?\./, renameFieldAlias[tableName] + '.')
}

export const fieldDefinesMap = (e) => {
  if (!e.tableName || e.field.indexOf('.') === -1 || !renameFieldAlias[e.tableName]) {
    return e
  }
  return {
    ...e,
    searchOptions: {
      renameField: getFieldAlias(e.field, e.tableName)
    }
  }
}

/**
 * ag样式相关公共函数
 */
// 单元格合并
export const rowSpan = (params) => {
  let _field = params.colDef ? params.colDef.field : undefined
  _field = _field.split('.')?.length > 1 ? _field.split('.')[1] : _field
  let _isFirstLine = params.data?.isFirstLine
  if (!stepNotMergeFieldNoPrefix.includes(_field) && _isFirstLine) {
    return params.data?.rowSpan
  }
  return 1
}
// 单元格合并样式
export const cellClass = (params) => {
  //合并、合并禁用、非合并、非合并禁用
  let _field = params.colDef ? params.colDef.field : undefined
  _field = _field.split('.')?.length > 1 ? _field.split('.')[1] : _field
  let _editable = params.colDef?.editable // 可编辑
  let _handleable = params.colDef?.cellRendererParams?.handleable // 可操作
  let _isFirstLine = params.data?.isFirstLine
  let _isPur = params.data?.customType === 'pur'
  // 特殊场景
  if (_field === 'untaxedUnitPrice' && !_isPur) {
    _editable = !params.data.costModelQuote
  }
  if (_field === 'costAnalysis' && !_isPur) {
    _handleable = params.data.costModelQuote
  }
  if (!stepNotMergeFieldNoPrefix.includes(_field) && _isFirstLine) {
    return _editable || _handleable ? 'mergeCell' : 'mergeCellDisable'
  }
  return _editable || _handleable ? 'singleCell' : 'singleCellDisable'
}
// 单元格合并样式
export const cellClassNoBg = (params) => {
  //合并、合并禁用、非合并、非合并禁用
  let _field = params.colDef ? params.colDef.field : undefined
  _field = _field.split('.')?.length > 1 ? _field.split('.')[1] : _field
  let _isFirstLine = params.data?.isFirstLine
  if (!stepNotMergeFieldNoPrefix.includes(_field) && _isFirstLine) {
    return 'mergeCell'
  }
  return 'singleCell'
}
