<template>
  <div class="full-height"></div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {}
  },
  created() {
    this.$store.commit('startLoading')
    this.$API.fileService
      .downloadPrivateFile({
        id: this.$route.query.id
      })
      .then((res) => {
        // this.$toast({
        //   content: this.$t("成功执行文件下载"),
        //   type: "success",
        // });
        const fileName = getHeadersFileName(res)
        this.$store.commit('endLoading')
        download({ fileName: fileName, blob: res.data })
      })
  }
}
</script>
