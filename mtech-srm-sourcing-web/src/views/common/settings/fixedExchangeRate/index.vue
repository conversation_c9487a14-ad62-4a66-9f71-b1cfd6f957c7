<template>
  <div class="set-country">
    <mt-local-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-local-template-page>

    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
export default {
  components: {
    MtLocalTemplatePage,
    addDialog: require('./component/addDialog.vue').default
  },
  data() {
    return {
      currentTabIndex: 0,
      pageConfig: [
        {
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'active',
                  icon: 'icon_Activation',
                  title: this.$t('启用')
                },
                {
                  id: 'inactive',
                  icon: 'icon_solid_Disable1',
                  title: this.$t('禁用')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              },
              {
                width: '190',
                field: 'companyCode',
                headerText: this.$t('公司'),
                valueAccessor: (_field, data) => {
                  return data.companyCode + '-' + data.companyName
                },
                cellTools: ['Edit', 'Delete']
              },
              {
                width: '120',
                field: 'sourceCurrencyCode',
                headerText: this.$t('币种')
              },
              {
                width: '120',
                field: 'sourceCurrencyName',
                headerText: this.$t('币种名称')
              },
              {
                width: '120',
                field: 'targetCurrencyCode',
                headerText: this.$t('兑换币种')
              },
              {
                width: '120',
                field: 'targetCurrencyName',
                headerText: this.$t('兑换币种名称')
              },
              {
                width: '120',
                field: 'rate',
                headerText: this.$t('兑换汇率（%）')
              },
              {
                width: '120',
                field: 'status',
                headerText: this.$t('状态'),
                valueConverter: {
                  type: 'map',
                  map: { 0: this.$t('草稿'), 1: this.$t('激活'), 2: this.$t('失效') }
                }
              },
              {
                width: '120',
                field: 'startTime',
                headerText: this.$t('生效日期')
              },
              {
                width: '120',
                field: 'endTime',
                headerText: this.$t('失效日期')
              },
              {
                field: 'createUserName',
                headerText: this.$t('创建人')
              },
              {
                field: 'updateTime',
                headerText: this.$t('最后更新时间')
              },
              {
                field: 'remark',
                headerText: this.$t('备注')
              }
            ],
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              url: `/masterDataManagement/tenant/currency/exchange/vn/paged-query`
            }
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      console.log(e.grid.getSelectedRecords(), e)
      if (
        e.grid.getSelectedRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.grid.getSelectedRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      } else if (e.toolbar.id == 'active') {
        this.handleUpdateStatus(_id, 1, this.$t('确认启用？'))
      } else if (e.toolbar.id == 'inactive') {
        this.handleUpdateStatus(_id, 2, this.$t('确认禁用？'))
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        let _row = e.data
        delete _row.cellTools
        delete _row.column
        delete _row.gridTemplate
        delete _row.gridRef
        delete _row.tabIndex
        this.handleEdit(_row)
      }
    },
    // 新增
    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addExchangeRateData'
      }
    },
    // 删除
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除？')
        },
        success: () => {
          this.$loading()
          this.$API.masterData
            .exchangeDelete({ ids: ids })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    // 编辑
    handleEdit(row) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        requestUrl: 'editExchangeRateData',
        row: row
      }
    },
    // 更新状态
    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$loading()
          this.$API.masterData
            .exchangeStatusUpdate({ ids: ids, status: flag })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    // 弹框显示
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },
    // 确认提交
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style></style>
