<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="companyCode" :label="$t('公司')">
        <mt-select
          v-model="addForm.companyCode"
          css-class="rule-element"
          :data-source="companySource"
          :fields="{ text: 'text', value: 'orgCode' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择公司')"
          @change="handleCompanyChange"
        />
      </mt-form-item>
      <mt-form-item prop="sourceCurrencyCode" :label="$t('币种')">
        <mt-select
          ref="sourceSelect"
          v-model="addForm.sourceCurrencyCode"
          :data-source="currencySource"
          @change="handleCurrencyChange($event, 'sourceCurrency')"
          :allow-filtering="true"
          :show-clear-button="true"
          :fields="{ text: 'text', value: 'currencyCode' }"
          :placeholder="$t('请选择币种')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="targetCurrencyCode" :label="$t('兑换币种')">
        <mt-select
          ref="targetSelect"
          v-model="addForm.targetCurrencyCode"
          :data-source="currencySource"
          :allow-filtering="true"
          :show-clear-button="true"
          :fields="{ text: 'text', value: 'currencyCode' }"
          @change="handleCurrencyChange($event, 'targetCurrency')"
          :placeholder="$t('请选择兑换币种')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="rate" :label="$t('兑换汇率（%）')">
        <mt-input-number
          v-model.number="addForm.rate"
          :show-clear-button="true"
          :placeholder="$t('请输入兑换汇率')"
        ></mt-input-number>
      </mt-form-item>

      <mt-form-item prop="effectiveTime" :label="$t('有效时间')" class="fullWidth">
        <mt-date-range-picker
          v-model="addForm.effectiveTime"
          format="yyyy-MM-dd"
          :show-clear-button="true"
          :placeholder="$t('选择开始时间和结束时间')"
        ></mt-date-range-picker>
      </mt-form-item>

      <mt-form-item prop="remark" :label="$t('说明')" class="fullWidth">
        <mt-input
          v-model.trim="addForm.remark"
          :disabled="false"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { formatTime } from '@/utils/utils'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        targetCurrencyId: '',
        sourceCurrencyId: '',
        rate: null,
        effectiveTime: null,
        remark: ''
      },
      rules: {
        companyCode: [{ required: true, message: this.$t('请输入公司名称'), trigger: 'blur' }],
        sourceCurrencyCode: [{ required: true, message: this.$t('请选择币种'), trigger: 'blur' }],
        targetCurrencyCode: [
          { required: true, message: this.$t('请选择兑换币种'), trigger: 'blur' }
        ],
        rate: [{ required: true, message: this.$t('请输入兑换汇率'), trigger: 'blur' }],
        effectiveTime: [{ required: true, message: this.$t('请选择有效时间'), trigger: 'blur' }]
      },
      currencySource: [],
      companySource: []
    }
  },
  mounted() {
    this.getCompanyList()
    this.getCurrencyList()
    this.$refs.dialog.ejsRef.show()
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
    }
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = { ...this.dialogData?.row }
      if (_addForm.startTime && _addForm.endTime) {
        _addForm.effectiveTime = [_addForm.startTime, _addForm.endTime]
      }
      this.addForm = { ..._addForm }
    }
  },

  methods: {
    // 获取币种
    async getCurrencyList() {
      this.$API.masterData.getCurrencyDataAll().then((res) => {
        res.data.forEach((item) => {
          item.text = item.currencyCode + '-' + item.currencyName
        })
        this.currencySource = res.data
      })
    },
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companySource = res.data
      }
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleCurrencyChange(e, key) {
      const { value } = e
      console.log(value)
      const _item = this.currencySource.find((item) => item.currencyCode === value)
      this.$set(this.addForm, `${key}Id`, _item.id)
      this.$set(this.addForm, `${key}Name`, _item.currencyName)
    },
    handleCompanyChange(e) {
      const { value } = e
      const _item = this.companySource.find((item) => item.orgCode != value)
      this.$set(this.addForm, `companyId`, _item.id)
      this.$set(this.addForm, `companyName`, _item.orgName)
    },
    // 确认提交
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = { ...this.addForm }
          console.log(this.addForm)

          if (this.addForm.effectiveTime && this.addForm.effectiveTime.length > 0) {
            params.startTime = formatTime(
              new Date(this.addForm.effectiveTime[0]),
              'YYYY-mm-dd HH:MM:SS'
            )
            params.endTime = formatTime(
              new Date(this.addForm.effectiveTime[1]),
              'YYYY-mm-dd HH:MM:SS'
            )
          }
          delete params.effectiveTime
          this.$API.masterData.exchangeSave(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        this.$API.masterData.getAddRulesExchangeRate().then((res) => {
          if (res.code == 200) this.rules = this.formatRules(res.data)
          this.rules.effectiveTime = [
            {
              required: true,
              message: this.$t('请选择有效时间'),
              trigger: 'blur'
            }
          ]
        })
      } else {
        this.$API.baseMainData.getUpdateRulesExchangeRate().then((res) => {
          if (res.code == 200) this.rules = this.formatRules(res.data)
          this.rules.effectiveTime = [
            {
              required: true,
              message: this.$t('请选择有效时间'),
              trigger: 'blur'
            }
          ]
        })
      }
    },
    formatRules(rules) {
      if (Object.prototype.toString.call(rules) != '[object Object]') {
        return {}
      }
      let res = {}
      for (var i in rules) {
        let _oneRule = []
        for (var j in rules[i]) {
          if (typeof rules[i][j][0] == 'boolean' && j != 'required') {
            _oneRule.push({
              type: j,
              message: rules[i][j][1],
              trigger: 'blur'
            })
          } else {
            _oneRule.push({
              [j]: rules[i][j][0],
              message: rules[i][j][1],
              trigger: 'blur'
            })
          }
        }
        res[i] = _oneRule
      }
      return res
    }
  }
}
</script>

<style></style>
