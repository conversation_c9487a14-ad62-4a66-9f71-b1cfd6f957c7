<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.businessConfig.getBusinessConfigs)
    }
  },
  methods: {
    handleClickCellTool(e) {
      if (e.tool.id == 'edit' || e.tool.id == 'add') {
        this.handleModuleConfigManagement(e.data)
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'businessTypeCode') {
        this.handleModuleConfigManagement(e.data)
      }
    },
    handleModuleConfigManagement(data) {
      let _query = {
        businessTypeId: data.businessTypeId,
        businessTypeCode: data.businessTypeCode,
        businessTypeName: data.businessTypeName
      }
      localStorage.sourceModuleConfigInfo = JSON.stringify(_query)
      this.$router.push({
        path: `/sourcing/business-config-detail`
      })
    }
  }
}
</script>
