import { i18n } from '@/main.js'
//模块功能流程配置
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'businessTypeCode',
    headerText: i18n.t('业务类型编码'),
    width: '200',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.id
        }
      },
      {
        id: 'add',
        icon: 'icon_solid_add',
        title: i18n.t('新增'),
        visibleCondition: (data) => {
          return !data.id
        }
      }
    ]
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型名称')
  },
  {
    field: 'enableStatus',
    headerText: i18n.t('业务类型状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('停用') }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后编辑人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('最后编辑时间'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    valueConverter: { type: 'placeholder', placeholder: i18n.t('无数据') }
  }
]

export const pageConfig = (url) => [
  {
    toolbar: [],
    grid: {
      allowFiltering: true,
      columnData,
      asyncConfig: {
        url,
        recordsPosition: 'data'
      }
    }
  }
]
