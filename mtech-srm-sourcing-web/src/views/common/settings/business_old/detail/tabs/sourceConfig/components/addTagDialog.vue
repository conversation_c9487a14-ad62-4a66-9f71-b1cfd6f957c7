// 新增/编辑tab名称
<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <span class="source-label label-left">tab{{ $t('名称：') }}</span>
      <mt-input
        :width="350"
        v-model="tabName"
        float-label-type="Never"
        :placeholder="$t('请输入tab名称')"
      ></mt-input>
      <div class="tips">{{ $t('最多输入10字符') }}</div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      tabName: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons,
        cssClass: ('small-dialog ' + this.modalData.cssClass).trim()
      }
    },
    header() {
      // let _smallHeader =
      //   '<i class="mt-icons mt-icon-icon_solid_Warning"></i><div class="header-text" id="_title">' +
      //   this.modalData.title +
      //   "</div>";
      // return _smallHeader;
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.tabName) {
      this.tabName = this.modalData.tabName
    }
  },
  methods: {
    confirm() {
      if (this.tabName.trim().length < 4 || this.tabName.trim().length > 10) {
        this.$toast({ type: 'warning', content: this.$t('tab名称长度为4~10个字符') })
        return
      }
      this.$emit('confirm-function', this.tabName.trim())
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  .tips {
    color: #aaa;
    font-size: 12px;
    padding-left: 72px;
  }
}
</style>
