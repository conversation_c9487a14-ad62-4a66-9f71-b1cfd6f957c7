import { i18n } from '@/main.js'
import Vue from 'vue'
export const supplierConfigColumnData = [
  {
    field: 'fieldGroup',
    headerText: i18n.t('字段分类')
  },
  {
    field: 'fieldName',
    headerText: i18n.t('字段名')
  },
  // {
  //   field: "BasicFixed",
  //   headerText: i18n.t("固定字段"),
  //   template: function () {
  //     return {
  //       template: Vue.component("actionOption", {
  //         template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.BasicFixed" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
  //         data() {
  //           return { data: {} };
  //         },
  //       }),
  //     };
  //   },
  // },
  {
    headerText: i18n.t('寻源需求'),
    columns: [
      {
        field: 'BasicNecessary',
        headerText: i18n.t('是否需要'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.BasicNecessary" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
              data() {
                return { data: {} }
              }
            })
          }
        }
      },
      {
        field: 'BasicRequired',
        headerText: i18n.t('是否必填'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.BasicRequired" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
              data() {
                return { data: {} }
              }
            })
          }
        }
      }
    ]
  },
  {
    headerText: i18n.t('供应商报价单'),
    columns: [
      {
        field: 'necessary',
        headerText: i18n.t('是否需要'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<mt-checkbox v-model="data.necessary" @change="handleChangeCellCheckBox"></mt-checkbox>`,
              data() {
                return { data: {} }
              },
              methods: {
                handleChangeCellCheckBox(e) {
                  this.data.necessary = e.checked
                  this.$parent.$emit('handleChangeCellCheckBox', {
                    index: this.data.index,
                    key: 'necessary',
                    value: e.checked
                  })
                }
              }
            })
          }
        }
      },
      {
        field: 'required',
        headerText: i18n.t('是否必填'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<mt-checkbox v-model="data.required" @change="handleChangeCellCheckBox"></mt-checkbox>`,
              data() {
                return { data: {} }
              },
              methods: {
                handleChangeCellCheckBox(e) {
                  this.data.required = e.checked
                  this.$parent.$emit('handleChangeCellCheckBox', {
                    index: this.data.index,
                    key: 'required',
                    value: e.checked
                  })
                }
              }
            })
          }
        }
      }
    ]
  }
]

export const getSupplierConfigDataSource = (len = 10) => {
  let res = []
  for (let i = 0; i < len; i++) {
    res.push({
      fieldName: '字段名-' + i,
      fixed: parseInt(Math.random(10) * 10) % 3 === 0,
      necessary: parseInt(Math.random(10) * 10) % 3 === 0,
      required: parseInt(Math.random(10) * 10) % 3 === 0,
      necessary1: parseInt(Math.random(10) * 10) % 3 === 0,
      required1: parseInt(Math.random(10) * 10) % 3 === 0
    })
  }
  return res
}
export const supplierConfigTabList = [
  {
    text: i18n.t('需求明细'),
    checked: false
  },
  {
    text: i18n.t('推荐供方'),
    checked: true
  },
  {
    text: i18n.t('候选供方'),
    checked: true
  },
  {
    text: i18n.t('寻源团队'),
    checked: false
  },
  {
    text: i18n.t('任务计划'),
    checked: true
  },
  {
    text: i18n.t('相关文件'),
    checked: false
  },
  {
    text: i18n.t('描述说明'),
    checked: false
  }
]
