<template>
  <div class="full-height supplier-offer-config mt-flex-direction-column">
    <div class="mt-flex supplier-config-container">
      <mt-tabs
        class="config-custom-tabs"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :selected-item="activeTab"
        @handleSelectTab="selectTab"
        :tabs-solt="true"
      >
        <template #templateContent="{ props }">
          <div class="item-content">
            <mt-icon
              @click.native="handleClickTabIcon(props)"
              class="config-checkbox"
              :name="props.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
            />
            <span @click="handleClickTabTitle(props)">{{ props.text }}</span>
          </div>
        </template>
      </mt-tabs>
      <div class="btns-wrap">
        <mt-button @click.native="saveModuleConfig">{{ $t('保存') }}</mt-button>
      </div>
    </div>

    <div class="supplier-offer-config-table-container business-config-container">
      <!-- 配置表格字段 -->
      <div
        v-for="(item, index) in tableList"
        :key="'table-' + index"
        v-show="currentTabObject.moduleKey == item.moduleKey"
      >
        <mt-data-grid
          :ref="'tab-grid-' + item.moduleKey"
          :data-source="item.grid.dataSource"
          :column-data="item.grid.columnData"
          :allow-filtering="false"
          :allow-sorting="false"
          :allow-paging="false"
          :query-cell-info="queryCellInfoEvent"
          @handleChangeCellCheckBox="handleChangeCellCheckBox(index, $event)"
        ></mt-data-grid>
      </div>
      <!-- 配置左侧第三层Tab菜单 -->
      <div
        class="ext-tabs-container mt-flex"
        v-for="(item, index) in leftNavList"
        :key="'left-nav-' + index"
        v-show="currentTabObject.moduleKey == item.moduleKey"
      >
        <div class="left-nav-tabs">
          <ul class="nav-container mt-flex-direction-column">
            <li
              :class="['nav-item', { active: _index == activeNav }]"
              v-for="(nav, _index) in item.navList"
              :key="'nav-item-' + index + '-' + _index"
              @click="activeNav = _index"
            >
              <div class="svg-option-item">
                <mt-icon
                  @click.native="handleClickNavIcon(nav)"
                  class="config-checkbox"
                  :name="nav.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
                />
                <span @click="handleClickNavTitle(nav)">{{ nav.text }}</span>
              </div>

              <mt-icon class="config-arrow" name="a-icon_Packup" />
            </li>
          </ul>
        </div>
        <div class="ext-content-container"></div>
      </div>
      <!-- 配置相关文件 -->
      <div
        class="ext-files-container mt-flex"
        v-for="(item, index) in fileConfigList"
        :key="'file-config-' + index"
        v-show="configId && currentTabObject.moduleKey == item.moduleKey"
      >
        <div class="tree-view--wrap">
          <div class="trew-node--add">
            <div class="node-title">{{ $t('层级') }}</div>
            <mt-button
              icon-css="mt-icons mt-icon-icon_solid_Createorder"
              css-class="e-flat"
              icon-position="Right"
              @click.native="addNewRootNode"
              >{{ $t('增加目录') }}</mt-button
            >
          </div>
          <mt-common-tree
            :checked-nodes="fileChekedNodes"
            v-if="treeViewData.dataSource.length"
            ref="treeView"
            class="tree-view--template"
            :allow-editing="true"
            :fields="treeViewData"
            @onButton="clickCustomButton"
            @nodeEdited="nodeEdited"
            :show-check-box="true"
          ></mt-common-tree>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { supplierConfigColumnData } from './config'
import Vue from 'vue'
export default {
  data() {
    return {
      configId: null, //业务类型ID
      docType: 'supplier_bid', //请求参数中的doctype  rfx配置
      configInfo: {},
      leftNavList: [], //左侧第三层Tab列表
      activeNav: 0, //左侧第三层Tab，Active序号
      tabList: [], //顶部Tab对象数组
      activeTab: 0, //顶部可勾选的Tab，Active序号
      currentTabObject: {}, //当前选中的Tab对象
      tableList: [], //所有配置项中，存在的表格，都在tableList中
      basicPorDataSource: [], //寻源需求中带过来的表格数据
      fileDocType: 'supplier_bid', //文件目录结构，docType
      isEditing: true, //相关文件-目录结构-编辑状态
      fileChekedNodes: [], //相关文件-目录结构-加载数据时，勾选的节点列表
      fileConfigList: [], //存在相关文件的所有配置项
      treeViewData: {
        //相关文件-目录结构-原数据
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxs">
                            <div>{{mtData.nodeName}}</div>
                          </div>`,
              data() {
                return { data: {} }
              },
              props: {
                mtData: {
                  // eslint-disable-line
                  // 拿到数据
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'id',
        text: 'nodeName',
        child: 'moduleFileList'
      },
      isStartRootNodeFormNull: false, // treeView根节点，只需要根据初始化的datasource维护一次
      gridHeight: 'auto'
    }
  },
  mounted() {
    //业务类型基本信息，存储在localstorage，不同Tab下数据保持一致
    this.configInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    this.getPorBaseModules() //先获取POR中的表格数据-再加载当前页数据
    this.resizeGridHeight()
  },
  methods: {
    // 自定义计算表格高度
    resizeGridHeight() {
      let itemList = document.getElementsByClassName('business-config-container')
      let _height = null
      if (itemList.length) {
        _height = itemList[0].offsetHeight
      }
      if (typeof _height === 'number' && _height > 40) {
        if (typeof this.allowPaging === 'boolean' && this.allowPaging) {
          _height = _height - 44
        }
        _height = _height - 47
      } else {
        _height = 'auto'
      }
      this.gridHeight = _height
    },
    //添加目录，根节点
    addNewRootNode() {
      if (this.treeViewData.dataSource.length === 0 && !this.isStartRootNodeFormNull) {
        this.isStartRootNodeFormNull = true // 只需要根据初始化的datasource维护一次
        this.$nextTick(() => {
          this.addTreeNode({ id: 0 })
        })
      } else {
        this.addTreeNode({
          id: 0
        })
      }
    },
    //common-tree-view右侧自定义按钮的电机
    clickCustomButton(e) {
      if (e.onBtn.text === this.$t('新增下级')) {
        this.addTreeNode(e)
      } else if (e.onBtn.text === this.$t('删除')) {
        this.deleteTreeNode(e)
      }
    },
    //新增下级
    addTreeNode(event) {
      const treeView = this.$refs.treeView[0]
      const instance = treeView?.getCommonMethods()
      const newNodeName = this.$t('目录名称')
      return this.$API.businessConfig
        .saveFileConfig({
          configId: this.configId,
          docType: this.fileDocType,
          moduleFileList: [],
          nodeCode: '',
          nodeName: newNodeName,
          parentId: event.id,
          required: 0
        })
        .then((res) => {
          if (res.code === 200) {
            const { id } = res.data
            instance.addNodes([res.data], event.id)
            // 直接进入编辑
            this.isEditing = true
            instance.beginEdit(id)
          }
        })
    },
    //节点编辑
    nodeEdited(event) {
      if (!this.isEditing) return
      this.isEditing = false
      const { newText, nodeData } = event
      const treeView = this.$refs.treeView[0]
      const instance = treeView?.getCommonMethods()
      this.$API.businessConfig
        .saveFileConfig({
          configId: this.configId,
          docType: this.fileDocType,
          id: nodeData.id,
          // moduleFileList: [],
          // nodeCode: "",
          nodeName: newText,
          parentId: nodeData.parentID
          // required: 0,
        })
        .then((res) => {
          if (res.code === 200) {
            // ej2 的 updateNode 只能修改 text ,所以将新建节点的ajax前置
            instance.updateNode(nodeData.id, newText)
          }
        })
    },
    // 删除树节点
    deleteTreeNode(event) {
      const treeView = this.$refs.treeView[0]
      const instance = treeView?.getCommonMethods()
      this.$API.businessConfig.deleteFileConfig({ idList: [event.id] }).then((res) => {
        if (res.code === 200 && res.data) {
          instance.removeNodes([event.id])
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //获取保存时，文件List需要的参数
    getSaveFileConfig() {
      const treeView = this.$refs.treeView[0]
      const instance = treeView?.getCommonMethods()
      let _nodes = instance.getAllCheckedNodes()
      let _records = [...this.treeViewData.dataSource]
      this.updateTreeViewNodes(_records, _nodes)
      return _records
    },
    //递归处理：根据tree-view的节点，更新fileConfig中的数据
    updateTreeViewNodes(_list, nodes) {
      _list.forEach((e) => {
        e.required = 0
        if (nodes.indexOf(e.id) > -1) {
          e.required = 1
        }
        if (Array.isArray(e?.moduleFileList) && e.moduleFileList.length) {
          this.updateTreeViewNodes(e.moduleFileList, nodes)
        }
      })
    },
    //数据初始化时，根据fileConfig，渲染tree-view中已勾选的节点
    serializeFileCheckedNodes() {
      let _records = [...this.treeViewData.dataSource]
      this.fileChekedNodes = this.getTreeViewNodes(_records)
    },
    //递归处理：初始化tree-view节点
    getTreeViewNodes(_list) {
      let _res = []
      _list.forEach((e) => {
        if (e.required > 0) {
          _res.push(e.id)
        }
        if (Array.isArray(e?.moduleFileList) && e.moduleFileList.length) {
          _res = _res.concat(this.getTreeViewNodes(e.moduleFileList))
        }
      })
      return _res
    },
    //获取默认的文件目录结构，实用默认预留的文件目录
    getPorFileConfig() {
      this.$API.businessConfig
        .getFileConfig({ configId: '0', docType: this.fileDocType })
        .then((res) => {
          this.$set(this.treeViewData, 'dataSource', res.data)
          this.serializeFileCheckedNodes()
        })
    },
    //切换顶部Tab，更新activeTab、currentTabObject
    selectTab(e) {
      this.activeTab = e
      this.currentTabObject = this.tabList[+e]
    },
    // 获取POR模块的通用配置，主要是获取'需求明细的表格配置'，用于渲染'表格参考列'
    getPorBaseModules() {
      this.$store.commit('startLoading')
      this.$API.commonConfig.getBaseModules({ docType: 'por' }).then((res) => {
        let { modules } = res.data
        modules.forEach((e) => {
          if (e.moduleType == 0) {
            //moduleType=0，代表需求明细模块
            this.basicPorDataSource = e.fields
          }
        })
        this.getPorConfigDetail()
      })
    },
    //获取配置POR基本信息,主要是获取'需求明细'下的参考数据
    getPorConfigDetail() {
      this.$API.businessConfig
        .getBusinessConfigDetail({
          businessTypeCode: this.configInfo.businessTypeCode,
          docType: 'por'
        })
        .then((res) => {
          let modules = res?.data?.modules
          if (Array.isArray(modules) && modules.length) {
            modules.forEach((module) => {
              if (module.moduleType == 0) {
                //只处理需求明细的表格数据。moduleType=0，代表需求明细；formType=0，代表非自定义表单
                //存在表格数据，处理表格数据
                let _baseFields = [...this.basicPorDataSource]
                let _fields = module['fields']
                _baseFields.forEach((f) => {
                  f.BasicNecessary = false
                  f.BasicRequired = f.required > 0
                  f.BasicFixed = f.fixed > 0
                  let _find = _fields.filter((e) => {
                    return e.fieldKey === f.fieldKey
                  })
                  if (_find.length > 0) {
                    f.BasicNecessary = true //在基础数据中，匹配到，设置necessary为已勾选
                    f.BasicRequired = _find[0].required > 0
                    f.BasicFixed = _find[0].fixed > 0
                  }
                  delete f.necessary
                  delete f.required
                  delete f.fixed
                })
                this.basicPorDataSource = _baseFields
              }
            })
          } else {
            let _basic = [...this.basicPorDataSource]
            _basic.forEach((f) => {
              f.BasicNecessary = true
              f.BasicRequired = f.required > 0
              f.BasicFixed = f.fixed > 0
              f.necessary = false //数据重置
              f.required = false //数据重置
              f.fixed = false //数据重置
            })
            this.basicPorDataSource = _basic
          }
          //按照commonBase中的数据，渲染使用表格数据
          this.getCommonBaseModules()
        })
        .catch(() => {
          let _basic = [...this.basicPorDataSource]
          _basic.forEach((f) => {
            f.BasicNecessary = false
            f.BasicRequired = f.required > 0
            f.BasicFixed = f.fixed > 0
            f.necessary = false //数据重置
            f.required = false //数据重置
            f.fixed = false //数据重置
          })
          this.basicPorDataSource = _basic
          this.getCommonBaseModules()
        })
    },
    //重新设置配置数据，主要是更新version
    resetModuleConfigInfo(res) {
      let _version = res?.data?.version
      if (_version) {
        this.$set(this.configInfo, 'version', _version)
        localStorage.sourceModuleConfigInfo = JSON.stringify(this.configInfo)
      }
      this.configId = res?.data?.configId
    },
    // 获取模块的通用配置，如果不需要其他配置的参考数据，那么配置项从这个方法开始
    getCommonBaseModules() {
      this.$API.commonConfig.getBaseModules({ docType: this.docType }).then((res) => {
        let { modules } = res.data
        let _tabList = []
        let _leftNavList = []
        let _fileConfigList = []
        modules.map((item) => {
          //构建-顶部Tab数组
          _tabList.push({
            text: item.moduleName,
            moduleKey: item.moduleKey,
            moduleId: item.moduleId,
            containField: item.containField,
            subModuleItems: item.subModuleItems,
            modules: item,
            fixed: item.fixed,
            fields: item.fields,
            checked: item.fixed > 0
          })
          //构建-文件配置对应的参数
          if (item.moduleType === 2 && item.moduleName === this.$t('相关文件')) {
            _fileConfigList.push({
              text: item.moduleName,
              moduleKey: item.moduleKey,
              moduleId: item.moduleId,
              containField: item.containField,
              subModuleItems: item.subModuleItems,
              modules: item,
              fixed: item.fixed,
              fields: item.fields,
              checked: item.fixed > 0
            })
          }
          //构建-左侧第二层Tab数据
          if (item.subModuleItems && item.subModuleItems.length) {
            //当前tab存在二级Tabs，显示在左侧
            let _subItems = item.subModuleItems
            let _navItem = []
            _subItems.forEach((sub) => {
              _navItem.push({
                parentModuleKey: item.moduleKey,
                parentModuleId: item.moduleId,
                text: sub.moduleName,
                modules: sub,
                moduleKey: sub.moduleKey,
                moduleId: sub.moduleId,
                checked: false
              })
            })
            _leftNavList.push({
              moduleKey: item.moduleKey,
              moduleId: item.moduleId,
              navList: _navItem
            })
          }
        })
        this.tabList = _tabList //顶部Tabs
        this.leftNavList = _leftNavList //左侧Navs
        this.fileConfigList = _fileConfigList //文件配置
        this.getBusinessConfigDetail()
      })
    },
    //获取配置基本信息
    getBusinessConfigDetail() {
      this.$API.businessConfig
        .getBusinessConfigDetail({
          businessTypeCode: this.configInfo.businessTypeCode,
          docType: this.docType
        })
        .then((res) => {
          this.resetModuleConfigInfo(res)
          let modules = res?.data?.modules
          let moduleFileList = res?.data?.moduleFileList
          if (this.configId && Array.isArray(moduleFileList) && moduleFileList.length) {
            //存在configId 并且moduleFileList存在数据，按照moduleFileList渲染数据
            this.$set(this.treeViewData, 'dataSource', moduleFileList)
            this.serializeFileCheckedNodes()
          } else {
            //从base数据中，获取基础文件目录信息
            this.getPorFileConfig()
          }
          let _tableList = []
          if (Array.isArray(modules) && modules.length) {
            modules.map((module) => {
              this.tabList.forEach((tab) => {
                if (tab.moduleKey == module.moduleKey) {
                  //存在moduleKey，对应的Tab设置勾选
                  tab.checked = true
                  tab.modules = module
                  tab.moduleId = module.moduleId

                  if (tab.containField > 0) {
                    //存在表格数据，处理表格数据
                    let _baseFields = tab.fields
                    let _fields = module['fields']
                    _baseFields.forEach((f) => {
                      f.necessary = false //在基础数据中，匹配到，设置necessary为已勾选
                      f.required = f.required > 0
                      f.fixed = f.fixed > 0
                      let _find = _fields.filter((e) => {
                        return e.fieldKey === f.fieldKey
                      })
                      if (_find.length > 0) {
                        f.necessary = true //在基础数据中，匹配到，设置necessary为已勾选
                        f.required = _find[0].required > 0
                        f.fixed = _find[0].fixed > 0
                      }
                    })

                    let _mergeFields = this.mergeBasicTableDataSource(_baseFields)
                    _mergeFields = this.serializeFieldsGroup(_mergeFields)
                    _tableList.push({
                      title: module.moduleName,
                      moduleKey: module.moduleKey,
                      moduleId: module.moduleId,
                      grid: {
                        allowFiltering: true,
                        dataSource: _mergeFields,
                        columnData: supplierConfigColumnData
                      }
                    })
                  }

                  if (tab.subModuleItems && tab.subModuleItems.length) {
                    //当前tab存在二级Tabs，显示在左侧
                    this.leftNavList.forEach((nav) => {
                      if (module.moduleKey == nav.moduleKey) {
                        let _navList = nav.navList
                        let _moduleSubList = module.subModuleItems
                        _navList.forEach((n) => {
                          _moduleSubList.forEach((m) => {
                            if (n.moduleKey == m.moduleKey) {
                              n.checked = true
                              n.modules = m
                              n.moduleId = m.moduleId
                            }
                          })
                        })
                      }
                    })
                  }
                }
              })
            })
          } else {
            this.tabList.forEach((tab) => {
              if (tab.containField > 0) {
                //存在表格数据
                let _baseFields = tab.fields
                _baseFields.forEach((f) => {
                  f.necessary = true
                  f.required = f.required > 0
                  f.fixed = f.fixed > 0
                })
                let _mergeFields = this.mergeBasicTableDataSource(_baseFields) //如果存在参考数据，则将表格数据合并
                _mergeFields = this.serializeFieldsGroup(_mergeFields) //如果存在表格第一列，根据fieldGroup合并单元格，执行序列化group
                //根据序列化后的fields，依次构建配置中需要的table属性
                _tableList.push({
                  title: tab.moduleName,
                  moduleKey: tab.moduleKey,
                  moduleId: tab.moduleId,
                  grid: {
                    allowFiltering: true,
                    dataSource: _mergeFields,
                    columnData: supplierConfigColumnData
                  }
                })
              }
              if (tab.subModuleItems && tab.subModuleItems.length) {
                //当前tab存在二级Tabs，显示在左侧
                this.leftNavList.forEach((nav) => {
                  if (module.moduleKey == nav.moduleKey) {
                    let _navList = nav.navList
                    let _moduleSubList = module.subModuleItems
                    _navList.forEach((n) => {
                      _moduleSubList.forEach((m) => {
                        if (n.moduleKey == m.moduleKey) {
                          n.checked = true
                          n.modules = m
                          n.moduleId = m.moduleId
                        }
                      })
                    })
                  }
                })
              }
            })
          }
          this.$nextTick(() => {
            this.$set(this, 'tableList', _tableList)
          })
          this.currentTabObject = this.tabList[0]
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    //合并POR需求明细数据+当前页列字段
    mergeBasicTableDataSource(_fields) {
      let _baseFields = [...this.basicPorDataSource]
      let _res = []
      _baseFields.forEach((b) => {
        let _useRecord = { ...b } //先用base里的单项，占位
        _fields.forEach((f) => {
          if (b.fieldKey == f.fieldKey) {
            let _item = { ...f }
            _item.BasicNecessary = b.BasicNecessary
            _item.BasicRequired = b.BasicRequired
            _item.BasicFixed = b.BasicFixed
            _useRecord = _item
            //如果在新的表格中，找到了fieldKey匹配项，用新数据，替换_useField
          }
        })
        _res.push(_useRecord)
      })
      return _res
    },
    //单元格内的checkbox勾选时，改变原数据
    handleChangeCellCheckBox(e, f) {
      this.$set(this.tableList[e]['grid']['dataSource'][f.index], f.key, f.value)
    },
    //合并单元格，主要处理fieldGroup列
    queryCellInfoEvent(args) {
      if (args.column.field == 'fieldGroup') {
        let _data = args.data
        if (_data?.isFirstGroupField) {
          args.rowSpan = _data.groupCount
        }
      }
    },
    //将数据按照fieldGroup数据序列化，排序、分组
    serializeFieldsGroup(fields) {
      let _map = {},
        _array = []
      for (let i = 0; i < fields.length; i++) {
        let _group = fields[i]['fieldGroup']
        if (_map && Object.prototype.hasOwnProperty.call(_map, _group)) {
          _map[_group].count++
          _map[_group].data.push(fields[i])
        } else {
          _array.push(_group)
          _map[_group] = {
            group: _group,
            count: 1,
            data: [fields[i]]
          }
        }
      }
      let _res = []
      for (let i in _array) {
        let _temp = _map[_array[i]]
        let _data = _temp.data
        let _count = _temp.count
        _data[0]['isFirstGroupField'] = true
        _data[0]['groupCount'] = _count
        _res = _res.concat(_data)
      }
      return _res
    },
    //Tab点击Icon，checkbox切换
    handleClickTabIcon(e) {
      let _fixed = e.fixed
      if (_fixed > 0) {
        this.$toast({ content: `'${e.text}'为固定项.`, type: 'warning' })
        return
      } else {
        e.checked = !e.checked
      }
    },
    //Tab点击Title，Tab切换
    handleClickTabTitle(e) {
      console.log('handleClickTabTitle', e)
    },
    //左侧Nav点击，checkbox切换
    handleClickNavIcon(e) {
      console.log('handleClickNavIcon', e)
      //当前，左侧Tab未设置关于fixed的要求
      // let _fixed = e.fixed;
      // if (_fixed > 0) {
      //   this.$toast({ content: `'${e.text}'为固定项.`, type: "warning" });
      //   return;
      // } else {
      //   e.checked = !e.checked;
      // }
      e.checked = !e.checked
    },
    //左侧Nav Title点击，Tab切换
    handleClickNavTitle(e) {
      console.log('handleClickNavTitle', e)
    },
    //保存Module配置项
    saveModuleConfig() {
      this.$store.commit('startLoading')
      let _moduleItems = []
      this.tabList.forEach((t) => {
        if (t.checked) {
          _moduleItems.push(t.modules)
        }
      })
      this.leftNavList.forEach((l) => {
        let _navList = l.navList
        _navList.forEach((n) => {
          if (n.checked) {
            _moduleItems.push(n.modules)
          }
        })
      })
      _moduleItems.forEach((r) => {
        if (r.containField > 0) {
          let _table = this.tableList.find((e) => {
            return e.moduleKey === r.moduleKey
          })

          let allRecords = _table.grid.dataSource
          let _filterRecords = allRecords.filter((e) => {
            return e.necessary
          })
          _filterRecords.forEach((_record) => {
            delete _record.BasicNecessary
            delete _record.BasicRequired
            delete _record.BasicFixed
            delete _record.necessary
            _record.fixed = _record.fixed ? 1 : 0
            _record.required = _record.required ? 1 : 0
          })
          r.fields = _filterRecords
        }
      })
      let { businessTypeId, businessTypeCode } = this.configInfo
      let _save = {
        docType: this.docType,
        businessTypeId,
        businessTypeCode,
        moduleItems: _moduleItems,
        moduleFileList: this.getSaveFileConfig()
      }
      if (this.configInfo?.version) {
        _save.version = this.configInfo.version
      }
      this.$API.businessConfig
        .saveBusinessConfig(_save)
        .then((res) => {
          this.$store.commit('endLoading')
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.resetModuleConfigInfo(res)
          this.getCommonBaseModules() //保存完毕，重新获取数据
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.supplier-offer-config {
  border: 1px solid rgba(232, 232, 232, 1);
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .supplier-config-container {
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    background: #fafafa;
    .btns-wrap {
      display: flex;
      /deep/ .mt-button {
        button {
          width: 76px;
          height: 34px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          box-shadow: unset;
          padding: 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
      }
    }
  }
  .config-custom-tabs {
    flex-shrink: 0;
  }
  .supplier-offer-config-table-container {
    flex: 1;
    overflow: auto;
    background: #fff;
    height: 100%;
    padding: 0 20px;
  }
}
</style>
