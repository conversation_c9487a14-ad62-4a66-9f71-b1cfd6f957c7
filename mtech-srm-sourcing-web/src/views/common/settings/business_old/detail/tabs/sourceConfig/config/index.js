import { i18n } from '@/main.js'
import Vue from 'vue'
export const sourceConfigColumnData = [
  {
    field: 'fieldGroup',
    headerText: i18n.t('字段分类'),

    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="field-group">{{data.fieldGroup}}</div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'fieldName',
    headerText: i18n.t('字段名')
  },
  {
    field: 'fixed',
    headerText: i18n.t('固定字段'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.fixed" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'necessary',
    headerText: i18n.t('是否需要'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="data.necessary" @change="handleChangeCellCheckBox"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.necessary = e.checked
              this.$parent.$emit('handleChangeCellCheckBox', {
                index: this.data.index,
                key: 'necessary',
                value: e.checked
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'required',
    headerText: i18n.t('是否必填'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="data.required" @change="handleChangeCellCheckBox"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.required = e.checked
              this.$parent.$emit('handleChangeCellCheckBox', {
                index: this.data.index,
                key: 'required',
                value: e.checked
              })
            }
          }
        })
      }
    }
  }
]
