<template>
  <div class="full-height source-config mt-flex-direction-column">
    <div class="mt-flex source-config-container">
      <mt-tabs
        class="config-custom-tabs"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :selected-item="activeTab"
        @handleSelectTab="selectTab"
        @handleEditTab="handleEditTab"
        @handleDeleteTab="handleDeleteTab"
        :tabs-solt="true"
      >
        <template #templateContent="{ props }">
          <div :class="['item-content', { fixed: props.fixed }]">
            <mt-icon
              @click.native="handleClickTabIcon(props)"
              class="config-checkbox"
              :name="props.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
            />
            <span @click="handleClickTabTitle(props)">{{ props.text }}</span>
          </div>
        </template>
      </mt-tabs>
      <div class="btns-wrap">
        <mt-button
          icon-css="mt-icons mt-icon-icon_solid_add"
          class="submit-btn"
          @click.native="addCustomFormTab"
          >{{ $t('添加') }}</mt-button
        >
        <mt-button @click.native="saveModuleConfig">{{ $t('保存') }}</mt-button>
      </div>
    </div>

    <div class="source-config-table-container business-config-container">
      <!-- 配置表格字段 -->
      <div
        v-for="(item, index) in tableList"
        :key="'table-' + index"
        v-show="currentTabObject.moduleKey == item.moduleKey"
      >
        <mt-data-grid
          :ref="'tab-grid-' + item.moduleKey"
          :data-source="item.grid.dataSource"
          :column-data="item.grid.columnData"
          :allow-filtering="false"
          :allow-sorting="false"
          :allow-paging="false"
          :height="gridHeight"
          :query-cell-info="queryCellInfoEvent"
          @handleChangeCellCheckBox="handleChangeCellCheckBox(index, $event)"
        ></mt-data-grid>
      </div>
      <!-- 配置自定义表单 -->
      <div
        v-for="(item, index) in customFormTabs"
        :key="'custom-form-' + index"
        v-show="currentTabObject.moduleKey == item.moduleKey"
      >
        <deylop
          @onSave="updateCustomFormData(item, $event)"
          :data-source="item.data.template"
        ></deylop>
      </div>
      <!-- 配置左侧第三层Tab菜单 -->
      <div
        class="ext-tabs-container mt-flex"
        v-for="(item, index) in leftNavList"
        :key="'left-nav-' + index"
        v-show="currentTabObject.moduleKey == item.moduleKey"
      >
        <div class="left-nav-tabs">
          <ul class="nav-container mt-flex-direction-column">
            <li
              :class="['nav-item', { active: _index == activeNav }]"
              v-for="(nav, _index) in item.navList"
              :key="'nav-item-' + index + '-' + _index"
              @click="activeNav = _index"
            >
              <div class="svg-option-item">
                <mt-icon
                  @click.native="handleClickNavIcon(nav)"
                  class="config-checkbox"
                  :name="nav.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
                />
                <span @click="handleClickNavTitle(nav)">{{ nav.text }}</span>
              </div>

              <mt-icon class="config-arrow" name="a-icon_Packup" />
            </li>
          </ul>
        </div>
        <div class="ext-content-container"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { v1 as uuidv1 } from 'uuid'
import { sourceConfigColumnData } from './config'
import Deylop from '@mtech-form-design/deploy'
export default {
  data() {
    return {
      customFormTabs: [],
      leftNavList: [],
      activeNav: 0,
      activeTab: 0,
      currentTabObject: {},
      docType: 'por', //请求参数中的doctype  rfx配置
      tabList: [],
      tableList: [],
      configInfo: {},
      configModules: [], // config接口里modules配置
      basicPorDataSource: [], //寻源需求中带过来的表格数据
      gridHeight: 'auto'
    }
  },
  components: {
    Deylop
  },
  mounted() {
    this.configInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    this.getCommonBaseModules() //先获取POR中的表格数据-再加载当前页数据
    this.resizeGridHeight()
  },
  methods: {
    // 自定义计算表格高度
    resizeGridHeight() {
      let itemList = document.getElementsByClassName('business-config-container')
      let _height = null
      if (itemList.length) {
        _height = itemList[0].offsetHeight
      }
      if (typeof _height === 'number' && _height > 40) {
        if (typeof this.allowPaging === 'boolean' && this.allowPaging) {
          _height = _height - 44
        }
        _height = _height - 47
      } else {
        _height = 'auto'
      }
      this.gridHeight = _height
    },
    selectTab(e) {
      this.activeTab = e
      this.currentTabObject = this.tabList[+e]
    },
    //重新设置配置数据，主要是更新version
    resetModuleConfigInfo(res) {
      let _version = res?.data?.version
      if (_version) {
        this.$set(this.configInfo, 'version', _version)
        localStorage.sourceModuleConfigInfo = JSON.stringify(this.configInfo)
      }
    },
    // 获取模块的通用配置
    getCommonBaseModules() {
      this.$API.commonConfig.getBaseModules({ docType: this.docType }).then((res) => {
        let { modules } = res.data
        let _tabList = []
        let _leftNavList = []
        modules.map((item) => {
          _tabList.push({
            text: item.moduleName,
            moduleName: module.moduleName,
            moduleKey: item.moduleKey,
            moduleId: item.moduleId,
            containField: item.containField,
            subModuleItems: item.subModuleItems,
            formType: item.formType,
            modules: item,
            fixed: item.fixed,
            fields: item.fields,
            checked: item.fixed > 0
          })
          if (item.subModuleItems && item.subModuleItems.length) {
            //当前tab存在二级Tabs，显示在左侧
            let _subItems = item.subModuleItems
            let _navItem = []
            _subItems.forEach((sub) => {
              _navItem.push({
                parentModuleKey: item.moduleKey,
                parentModuleId: item.moduleId,
                text: sub.moduleName,
                moduleName: sub.moduleName,
                modules: sub,
                moduleKey: sub.moduleKey,
                moduleId: sub.moduleId,
                checked: false
              })
            })
            _leftNavList.push({
              moduleKey: item.moduleKey,
              moduleId: item.moduleId,
              navList: _navItem
            })
          }
        })
        this.tabList = _tabList
        this.leftNavList = _leftNavList
        this.getBusinessConfigDetail()
      })
    },
    // 获取配置基本信息
    getBusinessConfigDetail() {
      this.$API.businessConfig
        .getBusinessConfigDetail({
          businessTypeCode: this.configInfo.businessTypeCode,
          docType: this.docType
        })
        .then((res) => {
          this.resetModuleConfigInfo(res)
          let modules = res?.data?.modules
          let _tableList = []
          if (Array.isArray(modules) && modules.length) {
            modules.map((module) => {
              if (module.formType < 1) {
                //formType: 0 固定模块， 1 扩展模块(用户自定义表单)
                this.tabList.forEach((tab) => {
                  if (tab.moduleKey == module.moduleKey) {
                    //存在moduleKey，对应的Tab设置勾选
                    tab.checked = true
                    tab.modules = module
                    tab.moduleId = module.moduleId

                    if (tab.containField > 0) {
                      //存在表格数据，处理表格数据
                      let _baseFields = tab.fields
                      let _fields = module['fields']
                      _baseFields.forEach((f) => {
                        f.necessary = false //在基础数据中，匹配到，设置necessary为已勾选
                        f.required = f.required > 0
                        f.fixed = f.fixed > 0
                        let _find = _fields.filter((e) => {
                          return e.fieldKey === f.fieldKey
                        })
                        if (_find.length > 0) {
                          f.necessary = true //在基础数据中，匹配到，设置necessary为已勾选
                          f.required = _find[0].required > 0
                          f.fixed = _find[0].fixed > 0
                        }
                      })
                      _baseFields = this.serializeFieldsGroup(_baseFields)
                      _tableList.push({
                        title: module.moduleName,
                        moduleName: module.moduleName,
                        moduleKey: module.moduleKey,
                        moduleId: module.moduleId,
                        grid: {
                          allowFiltering: true,
                          dataSource: _baseFields,
                          columnData: sourceConfigColumnData
                        }
                      })
                    }

                    if (tab.subModuleItems && tab.subModuleItems.length) {
                      //当前tab存在二级Tabs，显示在左侧
                      this.leftNavList.forEach((nav) => {
                        if (module.moduleKey == nav.moduleKey) {
                          let _navList = nav.navList
                          let _moduleSubList = module.subModuleItems
                          _navList.forEach((n) => {
                            _moduleSubList.forEach((m) => {
                              if (n.moduleKey == m.moduleKey) {
                                n.checked = true
                                n.modules = m
                                n.moduleId = m.moduleId
                              }
                            })
                          })
                        }
                      })
                    }
                  }
                })
              } else {
                //formType: 0 固定模块， 1 扩展模块(用户自定义表单)
                // 根据Id获取表单设计器的template
                this.$API.formDesignService
                  .getFormDesignInfo({ id: module.formTemplateId })
                  .then((res) => {
                    //避免有些Tab下的数据未成功加载，表单结构获取之后，再追加Tab数据
                    this.tabList.push({
                      checked: true, //当前用户定义的表单，默认勾选操作
                      text: module.moduleName,
                      moduleName: module.moduleName,
                      formType: module.formType,
                      moduleKey: module.moduleKey,
                      moduleId: module.moduleId,
                      containField: module.containField,
                      subModuleItems: module.subModuleItems,
                      modules: module,
                      fixed: module.fixed,
                      fields: module.fields,
                      formTemplateId: module.formTemplateId,
                      formLatestVersion: module.formLatestVersion,
                      titleCanChange: true
                    })
                    if (res?.data?.template?.saveFormTem) {
                      res.data.template.saveFormItem = true
                    }
                    this.customFormTabs.push({
                      text: module.moduleName,
                      moduleName: module.moduleName,
                      moduleKey: module.moduleKey,
                      moduleId: module.moduleId,
                      containField: module.containField,
                      subModuleItems: module.subModuleItems,
                      formTemplateId: module.formTemplateId,
                      formLatestVersion: module.formLatestVersion,
                      data: res.data
                    })
                  })
              }
            })
          } else {
            this.tabList.forEach((tab) => {
              if (tab.containField > 0) {
                //存在表格数据
                let _baseFields = tab.fields
                _baseFields.forEach((f) => {
                  f.necessary = true
                  f.required = f.required > 0
                  f.fixed = f.fixed > 0
                })
                _baseFields = this.serializeFieldsGroup(_baseFields)
                _tableList.push({
                  title: tab.moduleName,
                  moduleName: tab.moduleName,
                  moduleKey: tab.moduleKey,
                  moduleId: tab.moduleId,
                  grid: {
                    allowFiltering: true,
                    dataSource: _baseFields,
                    columnData: sourceConfigColumnData
                  }
                })
              }
              if (tab.subModuleItems && tab.subModuleItems.length) {
                //当前tab存在二级Tabs，显示在左侧
                this.leftNavList.forEach((nav) => {
                  let _navList = nav.navList
                  let _moduleSubList = tab.subModuleItems
                  _navList.forEach((n) => {
                    _moduleSubList.forEach((m) => {
                      if (n.moduleKey == m.moduleKey) {
                        n.checked = false
                        n.modules = m
                        n.moduleId = m.moduleId
                      }
                    })
                  })
                })
              }
            })
          }
          this.$nextTick(() => {
            this.$set(this, 'tableList', _tableList)
          })
          this.currentTabObject = this.tabList[0]
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    queryCellInfoEvent(args) {
      if (args.column.field == 'fieldGroup') {
        let _data = args.data
        if (_data?.isFirstGroupField) {
          args.rowSpan = _data.groupCount
        }
      }
    },
    serializeFieldsGroup(fields) {
      let _map = {},
        _array = []
      for (let i = 0; i < fields.length; i++) {
        let _group = fields[i]['fieldGroup']
        if (_map && Object.prototype.hasOwnProperty.call(_map, _group)) {
          _map[_group].count++
          _map[_group].data.push(fields[i])
        } else {
          _array.push(_group)
          _map[_group] = {
            group: _group,
            count: 1,
            data: [fields[i]]
          }
        }
      }
      let _res = []
      for (let i in _array) {
        let _temp = _map[_array[i]]
        let _data = _temp.data
        let _count = _temp.count
        _data[0]['isFirstGroupField'] = true
        _data[0]['groupCount'] = _count
        _res = _res.concat(_data)
      }
      return _res
    },
    // 保存表单设计器
    updateCustomFormData(item, formData) {
      let _url = 'addFormDesign'
      let _params = {
        template: formData,
        name: item.text
      }
      if (item.formTemplateId) {
        _url = 'updateFormDesign'
        _params.id = item.formTemplateId
      } else {
        //表单新增时，需要传额外的参数
        _params.code = 'BUSINESS_CONFIG'
      }
      this.$API.formDesignService[_url](_params).then((res) => {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.tabList.forEach((tab) => {
          if (tab.moduleKey === item.moduleKey) {
            tab.formTemplateId = res.data.id
            tab.formLatestVersion = res.data.version
          }
        })
      })
    },
    //Tab点击Icon，checkbox切换
    handleClickTabIcon(e) {
      let _fixed = e.fixed
      if (_fixed > 0) {
        this.$toast({ content: `'${e.text}'为固定项.`, type: 'warning' })
        return
      } else {
        e.checked = !e.checked
      }
    },
    //Tab点击Title，Tab切换
    handleClickTabTitle(e) {
      console.log('handleClickTabTitle', e)
    },
    //左侧Nav Title点击，Tab切换
    handleClickNavTitle(e) {
      console.log('handleClickNavTitle', e)
    },
    saveModuleConfig() {
      let _moduleItems = []
      this.tabList.forEach((t) => {
        if (t.checked) {
          _moduleItems.push(t.modules)
        }
      })
      this.leftNavList.forEach((l) => {
        let _navList = l.navList
        _navList.forEach((n) => {
          if (n.checked) {
            _moduleItems.push(n.modules)
          }
        })
      })
      _moduleItems.forEach((r) => {
        if (r.formType < 1) {
          //formType: 0 固定模块， 1 扩展模块(用户自定义表单)
          if (r.containField > 0) {
            let _table = this.tableList.find((e) => {
              return e.moduleKey === r.moduleKey
            })

            let allRecords = _table.grid.dataSource
            let _filterRecords = allRecords.filter((e) => {
              return e.necessary
            })
            _filterRecords.forEach((_record) => {
              delete _record.necessary
              _record.fixed = _record.fixed ? 1 : 0
              _record.required = _record.required ? 1 : 0
            })
            r.fields = _filterRecords
          }
        } else {
          //formType: 0 固定模块， 1 扩展模块(用户自定义表单)
          let _tab = this.tabList.find((e) => {
            return e.moduleKey === r.moduleKey
          })
          r.formTemplateId = _tab.formTemplateId
          r.formLatestVersion = _tab.formLatestVersion
          r.formType = 1
          r.moduleName = _tab.moduleName
          r.moduleKey = _tab.moduleKey
        }
      })
      let { businessTypeId, businessTypeCode } = this.configInfo
      let _save = {
        docType: this.docType,
        businessTypeId,
        businessTypeCode,
        moduleItems: _moduleItems
      }
      if (this.configInfo?.version) {
        _save.version = this.configInfo.version
      }
      this.$store.commit('startLoading')
      this.$API.businessConfig
        .saveBusinessConfig(_save)
        .then((res) => {
          this.$store.commit('endLoading')
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.resetModuleConfigInfo(res)
          this.getCommonBaseModules() //保存完毕，重新获取数据
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    handleChangeCellCheckBox(e, f) {
      this.$set(this.tableList[e]['grid']['dataSource'][f.index], f.key, f.value)
    },
    handleAddCustomTab(tabName) {
      let _uid = `custom_form_${uuidv1().replace(/-/g, '_')}`
      let _module = {
        formType: 1,
        formTemplateId: null,
        formLatestVersion: null,
        moduleName: tabName,
        moduleKey: _uid
      }
      this.tabList.push({
        checked: true, //当前用户新增的表单Tab，默认勾选操作
        modules: _module,
        text: _module.moduleName,
        moduleName: _module.moduleName,
        formType: _module.formType,
        moduleKey: _module.moduleKey,
        moduleId: _uid,
        containField: 0,
        subModuleItems: [],
        formTemplateId: null,
        formLatestVersion: null,
        titleCanChange: true
      })
      this.customFormTabs.push({
        text: _module.moduleName,
        moduleName: _module.moduleName,
        moduleKey: _module.moduleKey,
        moduleId: _uid,
        containField: 0,
        subModuleItems: [],
        formTemplateId: null,
        formLatestVersion: null,
        data: {
          name: tabName,
          template: {
            customButton: [],
            saveFormItem: true,
            downloadVue: true,
            showDemo: true,
            fields: [],
            formRef: 'mtForm',
            formModel: 'formData',
            size: 'medium',
            labelPosition: 'right',
            labelWidth: 100,
            formRules: 'rules',
            disabled: false,
            span: 24,
            parserData: [],
            lifecycleEvents: { mounted: [], beforeDestroy: [] },
            methods: []
          }
        }
      })
    },
    //用户新增自定义的Tabs
    addCustomFormTab() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/common/settings/business/detail/tabs/sourceConfig/components/addTagDialog" */ './components/addTagDialog.vue'
          ),
        data: {
          title: this.$t('新增tab')
        },
        success: (tabName) => {
          this.handleAddCustomTab(tabName)
        }
      })
    },
    //编辑用户自定义的Tabs
    handleEditTab(title, index) {
      console.log('handleEditTab--', title, index)
      let _tab = this.tabList[index]
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/common/settings/business/detail/tabs/sourceConfig/components/addTagDialog" */ './components/addTagDialog.vue'
          ),
        data: {
          title: this.$t('修改tab'),
          tabName: _tab.text
        },
        success: (tabName) => {
          this.$set(this.tabList[index], 'text', tabName)
          this.$set(this.tabList[index], 'moduleName', tabName)
        }
      })
    },
    //删除用户自定义的Tabs
    handleDeleteTab(title, index) {
      console.log('handleDeleteTab--', title, index)
      let _tab = this.tabList[index]
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: `确认删除名为${_tab.text}的Tab？`
        },
        success: () => {
          this.tabList.splice(index, 1)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.source-config {
  border: 1px solid rgba(232, 232, 232, 1);
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .source-config-container {
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    background: #fafafa;
    .btns-wrap {
      display: flex;
      /deep/ .mt-button {
        &.submit-btn {
          margin-right: 20px;
        }
        button {
          width: 76px;
          height: 34px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          box-shadow: unset;
          padding: 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
      }
    }
  }
  .source-config-table-container {
    flex: 1;
    overflow: auto;
    background: #fff;
    height: 100%;
  }
}
</style>
