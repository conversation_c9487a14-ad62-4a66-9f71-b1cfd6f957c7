import { i18n } from '@/main.js'
import Vue from 'vue'
export const rfxConfigTabList = [
  {
    text: i18n.t('大厅'),
    checked: true
  },
  {
    text: i18n.t('需求明细'),
    checked: true
  },
  {
    text: i18n.t('推荐供方'),
    checked: true
  },
  {
    text: i18n.t('候选供方'),
    checked: true
  },
  {
    text: i18n.t('寻源团队'),
    checked: true
  },
  {
    text: i18n.t('任务计划'),
    checked: true
  },
  {
    text: i18n.t('相关文件'),
    checked: false
  },
  {
    text: i18n.t('描述说明'),
    checked: false
  },
  {
    text: i18n.t('核价'),
    checked: true
  },
  {
    text: i18n.t('评标'),
    checked: true
  },
  {
    text: i18n.t('操作日志'),
    checked: true
  }
]
export const rfxStepsConfigStepsData = [
  {
    name: i18n.t('立项'),
    desc: '激活RFX后，进行供应商招募和选择，策略配置立项'
    // status: "completed",
  },
  {
    name: i18n.t('询标'),
    desc: '发布RFX，供应商报价投标，采方开标和比价，期间会发起多轮'
    // status: "process",
  },
  {
    name: i18n.t('标后'),
    desc: '采方对供应商报价进行核价，评标、决标处理'
    // status: "process",
  }
]
export const rfxConfigColumnData = [
  {
    field: 'fieldGroup',
    headerText: i18n.t('字段分类'),

    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="field-group">{{data.fieldGroup}}</div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'fieldName',
    headerText: i18n.t('字段名')
  },
  // {
  //   field: "BasicFixed",
  //   headerText: i18n.t("固定字段"),
  //   template: function () {
  //     return {
  //       template: Vue.component("actionOption", {
  //         template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.BasicFixed" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
  //         data() {
  //           return { data: {} };
  //         },
  //       }),
  //     };
  //   },
  // },
  {
    headerText: i18n.t('寻源需求'),
    columns: [
      {
        field: 'BasicNecessary',
        headerText: i18n.t('是否需要'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.BasicNecessary" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
              data() {
                return { data: {} }
              }
            })
          }
        }
      },
      {
        field: 'BasicRequired',
        headerText: i18n.t('是否必填'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.BasicRequired" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
              data() {
                return { data: {} }
              }
            })
          }
        }
      }
    ]
  },
  {
    headerText: i18n.t('寻源RFX'),
    columns: [
      {
        field: 'necessary',
        headerText: i18n.t('是否需要'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<mt-checkbox v-model="data.necessary" @change="handleChangeCellCheckBox"></mt-checkbox>`,
              data() {
                return { data: {} }
              },
              methods: {
                handleChangeCellCheckBox(e) {
                  this.data.necessary = e.checked
                  this.$parent.$emit('handleChangeCellCheckBox', {
                    index: this.data.index,
                    key: 'necessary',
                    value: e.checked
                  })
                }
              }
            })
          }
        }
      },
      {
        field: 'required',
        headerText: i18n.t('是否必填'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<mt-checkbox v-model="data.required" @change="handleChangeCellCheckBox"></mt-checkbox>`,
              data() {
                return { data: {} }
              },
              methods: {
                handleChangeCellCheckBox(e) {
                  this.data.required = e.checked
                  this.$parent.$emit('handleChangeCellCheckBox', {
                    index: this.data.index,
                    key: 'required',
                    value: e.checked
                  })
                }
              }
            })
          }
        }
      }
    ]
  }
]
