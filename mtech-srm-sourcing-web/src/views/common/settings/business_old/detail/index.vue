<template>
  <div class="full-height fields-config-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <div class="detail-info">
        <div class="name-wrap">
          <div class="first-line">
            <span class="code">{{ configInfo.businessTypeCode }}</span>
            <span
              :class="['tags', `tags-${index + 1}`]"
              v-for="(item, index) in tagList"
              :key="index"
              >{{ item }}</span
            >
          </div>
          <div class="second-line">
            <div class="cai-name">{{ configInfo.businessTypeId }}</div>
          </div>
        </div>
        <div class="btns-wrap">
          <!-- <mt-button>{{ $t('保存') }}</mt-button> -->
          <mt-button @click.native="backToBusinessConfig">{{ $t('返回') }}</mt-button>
        </div>
      </div>

      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
    </div>
    <div class="config-container">
      <!-- 0. 寻源需求配置 -->
      <tab-source-config v-if="tabIndex == 0"></tab-source-config>
      <!-- 1. RFX阶段配置 -->
      <tab-rfx-steps-config v-if="tabIndex == 1"></tab-rfx-steps-config>
      <!-- 2. 供应商报价单 -->
      <tab-supplier-offer-config v-if="tabIndex == 2"></tab-supplier-offer-config>
    </div>
  </div>
</template>
<script>
const mainTabList = [
  {
    title: this.$t('寻源需求配置')
  },
  {
    title: this.$t('RFX阶段配置')
  },
  {
    title: this.$t('供应商报价单')
  }
]
export default {
  components: {
    //寻源需求配置
    tabSourceConfig: () =>
      import(
        /* webpackChunkName: "router/common/settings/business/detail/tabs/sourceConfig" */ './tabs/sourceConfig/index.vue'
      ),
    //RFX阶段配置
    tabRfxStepsConfig: () =>
      import(
        /* webpackChunkName: "router/common/settings/business/detail/tabs/rfxStepsConfig" */ './tabs/rfxStepsConfig/index.vue'
      ),
    //供应商报价单
    tabSupplierOfferConfig: () =>
      import(
        /* webpackChunkName: "router/common/settings/business/detail/tabs/supplierOfferConfig" */ './tabs/supplierOfferConfig/index.vue'
      )
  },
  data() {
    return {
      tabIndex: 0,
      tagList: [],
      configInfo: {},
      tabSource: mainTabList
    }
  },
  mounted() {
    this.configInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    this.tagList = [this.configInfo.businessTypeName]
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    backToBusinessConfig() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.fields-config-page {
  padding-top: 20px;
  width: 100%;
}
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-info {
  width: 100%;
  height: 120px;
  padding: 20px;
  justify-content: space-between;
  background: rgba(245, 248, 251, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 0 8px 0 0;

  .detail-info {
    padding: 0;
    display: flex;
    line-height: 1;

    .name-wrap {
      flex: 1;

      .first-line {
        display: flex;
        align-items: center;
        .code {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: #292929;
        }
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
          margin-left: 10px;

          &-1 {
            color: rgba(237, 161, 51, 1);
            background: rgba(237, 161, 51, 0.1);
          }
          &-2 {
            color: #6386c1;
            background: rgba(99, 134, 193, 0.1);
          }
        }
      }

      .second-line {
        display: flex;
        align-items: center;
        margin-top: 10px;
        .cai-name {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          @extend .text-ellipsis;
        }
        .cai-desc {
          margin-left: 60px;
        }
        ul {
          display: flex;
          li {
            margin-left: 30px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(157, 170, 191, 1);

            .mt-icons {
              font-size: 12px;
            }
            span {
              vertical-align: text-bottom;
              margin-left: 4px;
              @extend .text-ellipsis;
            }
          }
        }
      }
    }
    .btns-wrap {
      /deep/ .mt-button {
        margin-right: 20px;
        button {
          width: 76px;
          height: 34px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          box-shadow: unset;
          padding: 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
      }
    }
  }

  /deep/ .mt-tabs-container {
    background: transparent;
    .tabs-arrow {
      display: none;
    }
    .tab-wrap {
      padding: 0;
      height: 50px;
      .tab-item {
        padding: 6px 10px;
        span {
          line-height: 1;
        }
      }
    }
  }
}
.config-container {
  flex: 1;
  margin-top: 10px;
  overflow: auto;
  min-width: 1000px;
}
/deep/ .mt-data-grid {
  .checked {
    color: #54bf00;
    font-size: 16px;
  }

  .uncheck {
    color: #ed5633;
    font-size: 16px;
  }
}
/deep/ .config-custom-tabs {
  background: #fafafa;
  padding: 0;
  /deep/ .e-tab-header {
    background: transparent;
  }
  .tab-wrap {
    padding: 0;
    height: 50px;
  }
  ul.tab-container {
    display: flex;
    li.tab-item {
      flex-shrink: 0;
      color: #292929;
      font-size: 14px;
      font-weight: 400;
      height: 46px;
      line-height: 46px;
      min-width: 60px;
      position: relative;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .item-content {
        cursor: pointer;
        padding: 0;
        display: flex;
        position: relative;
        min-width: 60px;
        color: #4f5b6d;
        align-items: center;
        justify-content: center;
        .mt-icons {
          position: relative;
          top: -1px;
          margin-right: 6px;
        }
        .config-checkbox {
          &.mt-icon-a-icon_MultipleChoice_on {
            color: #6386c1;
          }
          &.mt-icon-a-icon_MultipleChoice_off {
            color: #9daabf;
          }
        }
        &.fixed {
          .config-checkbox {
            &.mt-icon-a-icon_MultipleChoice_on {
              color: #9a9a9a;
            }
          }
        }
      }

      &.active,
      &:hover {
        border-color: transparent;
        background: transparent;
      }

      &.active {
        color: #00469c;
        font-weight: 600;

        &:after {
          content: '';
          border: 1px solid #00469c;
          width: 60%;
          animation: active-tab 0.3s ease;
          position: absolute;
          bottom: 6px;
          left: 20%;
        }
        @keyframes active-tab {
          0% {
            width: 0;
            left: 50%;
          }
          100% {
            width: 60%;
            left: 20%;
          }
        }
      }
    }
  }
}
/deep/ .ext-tabs-container {
  height: 100%;
  width: 100%;
  background: #e8e8e8;
  .left-nav-tabs {
    flex-shrink: 0;
    background: #fff;
    width: 172px;
    margin-right: 10px;
    .nav-container {
      .nav-item {
        height: 54px;
        padding: 0 20px;
        position: relative;
        background: #ffffff;
        color: #232b39;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e8e8e8;
        .svg-option-item {
          display: flex;
        }
        .config-checkbox {
          &.mt-icon-a-icon_MultipleChoice_on {
            color: #6386c1;
          }
          &.mt-icon-a-icon_MultipleChoice_off {
            color: #9daabf;
          }
        }
        .config-arrow {
          display: none;
        }
        &.active {
          color: #6386c1;
          background: #f5f6f9;
          .mt-icons {
            display: block;
          }
        }
        &:hover {
          color: #6386c1;
          background: #fafbfd;
        }
      }
    }
  }
  .ext-content-container {
    flex: 1;
    background: #fff;
  }
}
/deep/ .ext-files-container {
  height: 100%;
  .tree-view--wrap {
    min-width: 300px;
    box-shadow: inset -1px 0 0 0 rgba(232, 232, 232, 1);
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 50px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 10px;
    }
  }
}
/deep/ td.e-rowcell {
  &:first-of-type {
    &.e-templatecell {
      padding-right: 0 !important;
    }
    .field-group {
      border-right: 1px solid #e8e8e8;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;
      display: flex;
    }
  }
}
</style>
