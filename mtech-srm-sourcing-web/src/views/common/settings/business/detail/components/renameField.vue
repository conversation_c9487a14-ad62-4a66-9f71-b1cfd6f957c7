<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="small-dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div class="dialog-content">
        <mt-form ref="dialogRef" :model="formObjects" :rules="formRules">
          <mt-form-item prop="defaultFieldName" :label="$t('原字段描述')">
            <mt-input
              v-model="formObjects.defaultFieldName"
              disabled
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('请输入字段描述')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="fieldName" :label="$t('新字段描述')">
            <mt-input
              v-model="formObjects.fieldName"
              float-label-type="Never"
              :placeholder="$t('请输入字段描述')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObjects: {
        defaultFieldName: '',
        fieldName: ''
      },
      formRules: {
        fieldName: [
          {
            required: true,
            message: this.$t('字段描述不能为空'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      let _data = this.modalData.data
      this.formObjects = {
        defaultFieldName: _data.defaultFieldName,
        fieldName: _data.fieldName
      }
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', { ...this.formObjects })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
