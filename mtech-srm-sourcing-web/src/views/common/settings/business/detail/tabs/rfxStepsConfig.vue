<template>
  <div class="full-height rfx-steps-config mt-flex-direction-column">
    <div class="mt-flex steps-config-container">
      <div class="rfx-steps mt-flex">
        <div
          class="step-item mt-flex"
          :class="{ active: _index == currentStep }"
          v-for="(item, _index) in stepsData"
          :key="'step-' + _index"
        >
          <div class="step-content mt-flex" @click="changeStepsStatus(_index)">
            <div class="step-span step-number">{{ _index + 1 }}</div>
            <div class="step-span step-name mt-flex-direction-column">
              <span class="step-name-title">{{ $t(item.stageName) }}</span>
              <span class="step-name-desc">{{ $t(item.stageDesc) }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="btns-wrap">
        <mt-button @click.native="previewModuleConfig" v-show="showPreview">{{
          $t('预览')
        }}</mt-button>
        <mt-button @click.native="saveModuleConfig">{{ $t('保存') }}</mt-button>
      </div>
    </div>
    <mt-tabs
      class="config-custom-tabs"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="activeTab"
      @handleSelectTab="selectTab"
      :tabs-solt="true"
    >
      <template #templateContent="{ props }">
        <div :class="['item-content', { fixed: props.fixed }]">
          <mt-icon
            @click.native="handleClickTabIcon(props)"
            class="config-checkbox"
            :name="props.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
          />
          <span @click="handleClickTabTitle(props)">{{ $t(props.text) }}</span>
        </div>
      </template>
      <!-- <template v-slot:default="item">
        <div :class="['item-content', { fixed: item.fixed }]">
          <mt-icon
            @click.native="handleClickTabIcon(item)"
            class="config-checkbox"
            :name="
              item.checked
                ? 'a-icon_MultipleChoice_on'
                : 'a-icon_MultipleChoice_off'
            "
          />
          <span @click="handleClickTabTitle(item)">{{ item.text }}</span>
        </div>
      </template> -->
    </mt-tabs>
    <div class="rfx-steps-config-table-container business-config-container">
      <!-- 配置表格字段 -->
      <div
        v-for="(item, index) in tableList"
        :key="'table-' + index"
        v-show="currentTab.moduleKey == item.moduleKey"
      >
        <div class="structure-container">
          <div
            class="select-structure"
            v-if="
              tableModuleType.indexOf(item.moduleType) > -1 &&
              transportTypeList.indexOf(configInfo.sourcingObjType) < 0
            "
          >
            <div class="structure-title">{{ $t('结构类型') }}</div>
            <div class="structure-form">
              <mt-form>
                <mt-form-item :label="$t('结构类型')">
                  <mt-select
                    css-class="rule-element"
                    ref="structureListRef"
                    float-label-type="Never"
                    :disabled="
                      isHIERARCHY ||
                      configInfo.version > 0 ||
                      currentStep > 0 ||
                      item.moduleType == 9 ||
                      item.moduleType == 20
                    "
                    v-model="currentStructure"
                    :data-source="structureList"
                    :placeholder="$t('请选择结构类型')"
                    @change="changeStructureType"
                  ></mt-select>
                </mt-form-item>
              </mt-form>
            </div>
            <div class="structure-grid">
              <mt-tree-grid
                ref="treeGridRef"
                :data-source="treeGridDataSource[item.moduleType]"
                :columns="getTreeGridColumnData(item.moduleType)"
                child-mapping="subtasks"
                :allow-reordering="false"
                :allow-resizing="false"
                :allow-paging="false"
                :allow-filtering="false"
                :allow-sorting="false"
                :allow-selection="true"
                :selection-settings="selectionSettings"
                @rowSelected="treeGridRowSelected"
              ></mt-tree-grid>
            </div>
          </div>
          <div class="fields-container">
            <div class="field-group">
              <ul class="field-ul">
                <li
                  :class="[
                    'field-item',
                    { active: groupItemIsActive(__index) },
                    { 'hidden-item': !fields.visible }
                  ]"
                  v-for="(fields, __index) in getVisibleFieldGroup"
                  :key="'group-' + __index"
                  @click="currentTab.currentGroup = __index"
                >
                  {{ $t(fields.group) }}
                </li>
              </ul>
            </div>
            <div class="field-grid" v-if="getCurrentDataSource.length">
              <mt-data-grid
                :ref="'tab-grid-' + item.moduleKey"
                :data-source="getCurrentDataSource"
                :column-data="gridColumnData"
                :allow-selection="false"
                :allow-filtering="false"
                :allow-sorting="false"
                :allow-paging="false"
                :height="gridHeight"
                @handleChangeCellCheckBox="handleChangeCellCheckBox"
                @changeFieldName="changeFieldName"
              ></mt-data-grid>
            </div>
          </div>
        </div>
      </div>
      <!-- 配置左侧第三层Tab菜单 -->
      <div
        class="ext-tabs-container mt-flex"
        v-for="(item, index) in leftNavList"
        :key="'left-nav-' + index"
        v-show="currentTab.moduleKey == item.moduleKey"
      >
        <div class="left-nav-tabs">
          <ul class="nav-container mt-flex-direction-column">
            <li
              :class="['nav-item', { active: _index == activeNav }]"
              v-for="(nav, _index) in item.navList"
              :key="'nav-item-' + index + '-' + _index"
              @click="activeNav = _index"
            >
              <div class="svg-option-item">
                <mt-icon
                  @click.native="handleClickNavIcon(nav)"
                  class="config-checkbox"
                  :name="nav.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
                />
                <span @click="handleClickNavTitle(nav)">{{ $t(nav.text) }}</span>
              </div>

              <mt-icon class="config-arrow" name="a-icon_Packup" />
            </li>
          </ul>
        </div>
        <div class="ext-content-container"></div>
      </div>
      <!-- 配置相关文件 -->
      <div
        class="ext-files-container mt-flex"
        v-for="(item, index) in fileConfigList"
        :key="'file-config-' + index"
        v-show="configId && currentTab.moduleKey == item.moduleKey"
      >
        <div class="tree-view--wrap">
          <div class="trew-node--add">
            <div class="node-title">{{ $t('层级') }}</div>
            <!-- <mt-button
              icon-css="mt-icons mt-icon-icon_solid_Createorder"
              css-class="e-flat"
              icon-position="Right"
              @click.native="addNewRootNode"
              >{{ $t("增加目录") }}</mt-button
            > -->
          </div>
          <mt-common-tree
            :checked-nodes="fileChekedNodes"
            v-if="treeViewData.dataSource.length"
            ref="treeView"
            class="tree-view--template"
            :allow-editing="false"
            :fields="treeViewData"
            @onButton="clickCustomButton"
            @nodeEdited="nodeEdited"
            :un-button="true"
            :show-check-box="true"
          ></mt-common-tree>
        </div>
      </div>
    </div>
    <mt-dialog
      ref="previewDialog"
      css-class="previewDialog"
      :header="$t('预览')"
      :buttons="buttons"
      :open="onOpen"
    >
      <div class="content">
        <!-- 父级 -->
        <div class="parentLevel">
          <mt-template-page ref="templateRef" :template-config="pageConfig"></mt-template-page>
        </div>
        <!-- 子级别 -->
        <div class="childLevel" v-if="currentStructure === 2">
          <mt-template-page
            ref="childTemplateRef"
            :template-config="childPageConfig"
          ></mt-template-page>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import MixIn from '../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      fileDocType: 'rfx', //文件目录结构，docType
      docType: 'rfx'
    }
  },
  async mounted() {
    this.tableList = []
    this.resetStructureTag()
    //业务类型基本信息，存储在localstorage，不同Tab下数据保持一致
    this.configInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    await this.$API.businessConfig
      .getStageList({ docType: 'rfx', configId: this.configInfo.id })
      .then((res) => {
        this.stepsData = res.data
      })
    this.getCommonBaseModules()
    this.resizeGridHeight()
    let _this = this
    this.$bus.$on('handleChangeStructureParams', (params) => {
      _this.changeStructureParams(params)
    })
    if (this.configInfo?.sourcingObjType === 'combination') {
      this.structureList[2]['text'] = this.$t('组合结构')
    }
  },
  methods: {
    getStageKeyParams() {
      return { stageKey: this.stepsData[this.currentStep]['stageKey'] }
    },
    checkEnableCurrentConfig(index) {
      this.$store.commit('startLoading')
      this.$API.businessConfig
        .configDetail({
          templateCode: this.configInfo.templateCode,
          docType: this.docType,
          stageKey: this.stepsData[index - 1]['stageKey']
        })
        .then((res) => {
          this.$store.commit('endLoading')
          let modules = res?.data?.modules
          if (Array.isArray(modules) && modules.length) {
            //前一阶段进行过配置，执行后续操作
            this.currentStep = index
            for (let i in this.stepsData) {
              this.stepsData[i]['status'] = i <= index ? 'completed' : 'process'
            }
            this.activeTab = 0
            this.activeNav = 0
            this.$set(this.treeViewData, 'dataSource', [])
            modules.forEach((module) => {
              if (module.moduleType === 14 && !this.isSetStructure) {
                this.setStructureTag()
                this.currentStructure = module.structureType
                // console.error("currentStructure赋值--4", this.currentStructure);
                this.baseStructure = this.setStructure(module?.structures)
                // console.error("前一阶段赋值", module, this.baseStructure);
              }
            })
            this.getCommonBaseModules()
            // this.getBusinessConfigDetail();
          } else {
            // rfx未进行过配置，没有可以参考的structur采购明细结构
            this.$toast({
              content: this.$t('“前一阶段”未进行过字段配置，请先维护数据'),
              type: 'warning'
            })
            return
          }
        })
    },
    changeStepsStatus(index) {
      if (this.currentStep !== index) {
        this.resetStructureTag()
        this.tableList = []
        if (index > 0) {
          //如果不是第一阶段，需要校验‘第一阶段’是否进行过配置
          this.checkEnableCurrentConfig(index)
        } else {
          this.currentStep = index
          for (let i in this.stepsData) {
            this.stepsData[i]['status'] = i <= index ? 'completed' : 'process'
          }
          this.activeTab = 0
          this.activeNav = 0
          this.$set(this.treeViewData, 'dataSource', [])
          this.getCommonBaseModules()
          // this.getBusinessConfigDetail();
        }
      }
    }
  }
}
</script>
<style lang="scss">
.previewDialog {
  width: 80% !important;
  height: 100% !important;
}
</style>
<style lang="scss" scoped>
.rfx-steps-config {
  /deep/th.e-headercell {
    vertical-align: middle;
  }
  /deep/.e-content {
    overflow: auto !important;
  }
  border: 1px solid rgba(232, 232, 232, 1);
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .steps-config-container {
    justify-content: space-between;
    padding: 10px 20px;
    background: #fafafa;
    flex-shrink: 0;
    .rfx-steps {
      padding: 0;
      flex: 1;
      overflow-x: auto;
      overflow-y: hidden;
      background: #fafafa;
      // &:hover {
      //   &::-webkit-scrollbar {
      //     display: block;
      //   }
      // }
      .step-item {
        align-items: center;
        padding-right: 60px;
        min-width: 150px;
        flex-shrink: 0;
        .step-content {
          cursor: pointer;
          z-index: 2;
          background: #fafafa;
          .step-span {
            color: #9a9a9a;
            font-size: 14px;
          }
          .step-number {
            margin-left: 10px;
            height: 20px;
            width: 20px;
            line-height: 20px;
            background: #fafafa;
            color: #9a9a9a;
            border: 1px solid #9a9a9a;
            border-radius: 50%;
            text-align: center;
            display: inline-block;
          }
          .step-name {
            margin: 0 10px;
            justify-content: space-between;
            .step-name-title {
              font-size: 16px;
              font-family: PingFangSC;
              font-weight: 500;
            }
            .step-name-desc {
              font-size: 12px;
              font-family: PingFangSC;
              font-weight: normal;
            }
          }
        }

        &:after {
          content: '';
          border-top: 1px dashed #9a9a9a;
          width: 100%;
          position: absolute;
        }
        &:first-of-type {
          .step-number {
            margin-left: 0;
          }
        }
        &:last-of-type {
          padding-right: 0;
          .step-name {
            margin-right: 0;
          }
          &:after {
            display: none;
          }
        }

        &.active {
          .step-span {
            color: #6386c1;
          }
          .step-number {
            border: none;
            background: #6386c1;
            color: #fafafa;
          }
          // &:after {
          //   border-top: 1px dashed #aebfdd;
          // }
        }
      }
      //scrollBar-color
      $scrollBar-track-color: #ffffff;
      $scrollBar-thumb-color: #d8d8d8;
      $scrollBar-thumb-hover-color: rgb(200, 200, 200);
      $scrollBar-thumb-active-color: rgb(190, 190, 190);
      //修改谷歌内核浏览器滚动条样式
      &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
        // display: none;
      }
      &::-webkit-scrollbar-track {
        border-radius: 2px;
        background-color: $scrollBar-track-color;
      }

      &::-webkit-scrollbar-thumb {
        background-color: $scrollBar-thumb-color;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: $scrollBar-thumb-hover-color;
      }

      &::-webkit-scrollbar-thumb:active {
        background-color: $scrollBar-thumb-hover-color;
      }
    }
    .btns-wrap {
      /deep/ .mt-button {
        button {
          background: transparent;
          border-radius: 4px;
          box-shadow: unset;
          padding: 6px 12px 4px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          border: none;
          color: #00469c;
        }
      }
    }
  }
  .config-custom-tabs {
    flex-shrink: 0;
  }

  .rfx-steps-config-table-container {
    flex: 1;
    overflow: auto;
    background: #fff;
    height: 100%;
    padding: 0 20px 0 0;
    .structure-container {
      width: 100%;
      height: 100%;
      background: #f4f4f4;
      display: flex;
      .select-structure {
        width: 400px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        padding: 20px;
        margin-right: 4px;
        background: #fff;
        .structure-title {
          flex-shrink: 0;
          display: inline-block;
          padding-left: 13px;
          position: relative;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(41, 41, 41, 1);

          &:before {
            content: '';
            position: absolute;
            width: 3px;
            height: 14px;
            background: #6386c1;
            border-radius: 0 2px 2px 0;
            left: 0;
            top: 2px;
          }
        }
        .structure-form {
          padding: 0 10px;
          margin-top: 35px;
          /deep/.label {
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
          }
        }
        .structure-grid {
        }
      }
      .fields-container {
        display: flex;
        flex: 1;
        background: #fff;
        padding: 20px 0 20px 20px;

        .field-group {
          width: 160px;
          flex-shrink: 0;
          margin-right: 20px;

          .field-ul {
            width: 100%;
            .field-item {
              cursor: pointer;
              border-left: 1px solid #f4f4f4;
              height: 34px;
              padding-left: 30px;
              display: flex;
              align-items: center;
              background: #ffffff;
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(41, 41, 41, 1);
              &.active {
                background: #f5f6f9;
                color: rgba(0, 70, 156, 1);
                border-left: 1px solid #6386c1;
              }
              &.hidden-item {
                display: none;
              }
            }
          }
        }
        .field-grid {
          flex: 1;
        }
      }
    }
  }
}
</style>
