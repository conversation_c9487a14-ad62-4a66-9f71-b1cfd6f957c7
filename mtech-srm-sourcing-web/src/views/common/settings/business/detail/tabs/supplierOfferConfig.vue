<template>
  <div class="full-height rfx-steps-config mt-flex-direction-column" v-if="enableConfig">
    <div class="mt-flex steps-config-container">
      <mt-tabs
        class="config-custom-tabs"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :selected-item="activeTab"
        @handleSelectTab="selectTab"
        :tabs-solt="true"
      >
        <template #templateContent="{ props }">
          <div class="item-content">
            <mt-icon
              @click.native="handleClickTabIcon(props)"
              class="config-checkbox"
              :name="props.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
            />
            <span @click="handleClickTabTitle(props)">{{ $t(props.text) }}</span>
          </div>
        </template>
      </mt-tabs>
      <div class="btns-wrap">
        <mt-button @click.native="previewModuleConfig" v-show="showPreview">{{
          $t('预览')
        }}</mt-button>
        <mt-button @click.native="saveModuleConfig">{{ $t('保存') }}</mt-button>
      </div>
    </div>
    <div class="rfx-steps-config-table-container business-config-container">
      <!-- 配置表格字段 -->
      <div
        v-for="(item, index) in tableList"
        :key="'table-' + index"
        v-show="currentTab.moduleKey == item.moduleKey"
      >
        <div class="structure-container">
          <div
            class="select-structure"
            v-if="
              tableModuleType.indexOf(item.moduleType) > -1 &&
              transportTypeList.indexOf(configInfo.sourcingObjType) < 0
            "
          >
            <div class="structure-title">{{ $t('结构类型') }}</div>
            <div class="structure-form">
              <mt-form>
                <mt-form-item :label="$t('结构类型')"
                  ><mt-select
                    css-class="rule-element"
                    ref="structureListRef"
                    float-label-type="Never"
                    :disabled="true"
                    v-model="currentStructure"
                    :data-source="structureList"
                    :placeholder="$t('请选择结构类型')"
                    @change="changeStructureType"
                  ></mt-select>
                </mt-form-item>
              </mt-form>
            </div>
            <div class="structure-grid">
              <mt-tree-grid
                ref="treeGridRef"
                :data-source="treeGridDataSource[item.moduleType]"
                :columns="getTreeGridColumnData(item.moduleType)"
                child-mapping="subtasks"
                :allow-reordering="false"
                :allow-resizing="false"
                :allow-paging="false"
                :allow-filtering="false"
                :allow-sorting="false"
                :allow-selection="true"
                :selection-settings="selectionSettings"
                @rowSelected="treeGridRowSelected"
              ></mt-tree-grid>
            </div>
          </div>
          <div class="fields-container">
            <div class="field-group">
              <ul class="field-ul">
                <li
                  :class="[
                    'field-item',
                    { active: groupItemIsActive(__index) },
                    { 'hidden-item': !fields.visible }
                  ]"
                  v-for="(fields, __index) in getVisibleFieldGroup"
                  :key="'group-' + __index"
                  @click="currentTab.currentGroup = __index"
                >
                  {{ $t(fields.group) }}
                </li>
              </ul>
            </div>
            <div class="field-grid" v-if="getCurrentDataSource.length">
              <mt-data-grid
                :ref="'tab-grid-' + item.moduleKey"
                :data-source="getCurrentDataSource"
                :column-data="gridColumnData"
                :allow-selection="false"
                :allow-filtering="false"
                :allow-sorting="false"
                :allow-paging="false"
                :height="gridHeight"
                @handleChangeCellCheckBox="handleChangeCellCheckBox"
                @changeFieldName="changeFieldName"
              ></mt-data-grid>
            </div>
          </div>
        </div>
      </div>
      <!-- 配置左侧第三层Tab菜单 -->
      <div
        class="ext-tabs-container mt-flex"
        v-for="(item, index) in leftNavList"
        :key="'left-nav-' + index"
        v-show="currentTab.moduleKey == item.moduleKey"
      >
        <div class="left-nav-tabs">
          <ul class="nav-container mt-flex-direction-column">
            <li
              :class="['nav-item', { active: _index == activeNav }]"
              v-for="(nav, _index) in item.navList"
              :key="'nav-item-' + index + '-' + _index"
              @click="activeNav = _index"
            >
              <div class="svg-option-item">
                <mt-icon
                  @click.native="handleClickNavIcon(nav)"
                  class="config-checkbox"
                  :name="nav.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
                />
                <span @click="handleClickNavTitle(nav)">{{ $t(nav.text) }}</span>
              </div>

              <mt-icon class="config-arrow" name="a-icon_Packup" />
            </li>
          </ul>
        </div>
        <div class="ext-content-container"></div>
      </div>
      <!-- 配置相关文件 -->
      <div
        class="ext-files-container mt-flex"
        v-for="(item, index) in fileConfigList"
        :key="'file-config-' + index"
        v-show="configId && currentTab.moduleKey == item.moduleKey"
      >
        <div class="tree-view--wrap">
          <div class="trew-node--add">
            <div class="node-title">{{ $t('层级') }}</div>
            <!-- <mt-button
              icon-css="mt-icons mt-icon-icon_solid_Createorder"
              css-class="e-flat"
              icon-position="Right"
              @click.native="addNewRootNode"
              >{{ $t("增加目录") }}</mt-button
            > -->
          </div>
          <mt-common-tree
            :checked-nodes="fileChekedNodes"
            v-if="treeViewData.dataSource.length"
            ref="treeView"
            class="tree-view--template"
            :allow-editing="false"
            :fields="treeViewData"
            @onButton="clickCustomButton"
            @nodeEdited="nodeEdited"
            :un-button="true"
            :show-check-box="true"
          ></mt-common-tree>
        </div>
      </div>
    </div>
    <mt-dialog
      ref="previewDialog"
      css-class="previewDialog"
      :header="$t('预览')"
      :buttons="buttons"
      :open="onOpen"
    >
      <div class="content">
        <!-- 父级 -->
        <div class="parentLevel">
          <mt-template-page ref="templateRef" :template-config="pageConfig"></mt-template-page>
        </div>
        <!-- 子级别 -->
        <div class="childLevel" v-if="currentStructure === 2">
          <mt-template-page
            ref="childTemplateRef"
            :template-config="childPageConfig"
          ></mt-template-page>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import MixIn from '../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      fileDocType: 'supplier_bid', //文件目录结构，docType
      docType: 'supplier_bid'
    }
  },
  async mounted() {
    this.tableList = []
    //业务类型基本信息，存储在localstorage，不同Tab下数据保持一致
    this.configInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    this.checkEnableCurrentConfig()
    this.resizeGridHeight()
    let _this = this
    this.$bus.$on('handleChangeStructureParams', (params) => {
      _this.changeStructureParams(params)
    })
    if (this.configInfo?.sourcingObjType === 'combination') {
      this.structureList[2]['text'] = this.$t('组合结构')
    }
  },
  methods: {
    getStageKeyParams() {
      return { stageKey: '' }
    },
    checkEnableCurrentConfig() {
      this.$store.commit('startLoading')
      this.$API.businessConfig
        .configDetail({
          templateCode: this.configInfo.templateCode,
          docType: 'rfx',
          stageKey: 'PROJECT_PREPARE'
        })
        .then((res) => {
          this.$store.commit('endLoading')
          let modules = res?.data?.modules
          if (Array.isArray(modules) && modules.length) {
            this.enableConfig = true
            modules.forEach((module) => {
              if (module.moduleType === 14 && !this.isSetStructure) {
                this.setStructureTag()
                this.currentStructure = module.structureType
                this.baseStructure = this.setStructure(module?.structures)
                // console.error("前一阶段赋值", module, this.baseStructure);
              }
            })
            this.getCommonBaseModules()
          } else {
            this.enableConfig = false
            this.$toast({
              content: this.$t('“RFX阶段”未进行过字段配置，请先维护RFX阶段配置'),
              type: 'warning'
            })
            return
          }
        })
    }
  }
}
</script>
<style lang="scss">
.previewDialog {
  width: 80% !important;
  height: 100% !important;
}
</style>
<style lang="scss" scoped>
.rfx-steps-config {
  /deep/th.e-headercell {
    vertical-align: middle;
  }
  /deep/.e-content {
    overflow: auto !important;
  }
  border: 1px solid rgba(232, 232, 232, 1);
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .steps-config-container {
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    background: #fafafa;
    .btns-wrap {
      display: flex;
      /deep/ .mt-button {
        button {
          background: transparent;
          border-radius: 4px;
          box-shadow: unset;
          padding: 6px 12px 4px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          border: none;
          color: #00469c;
        }
      }
    }
  }
  .config-custom-tabs {
    flex-shrink: 0;
  }

  .rfx-steps-config-table-container {
    flex: 1;
    overflow: auto;
    background: #fff;
    height: 100%;
    padding: 0 20px 0 0;
    .structure-container {
      width: 100%;
      height: 100%;
      background: #f4f4f4;
      display: flex;
      .select-structure {
        width: 400px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        padding: 20px;
        margin-right: 4px;
        background: #fff;
        .structure-title {
          flex-shrink: 0;
          display: inline-block;
          padding-left: 13px;
          position: relative;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(41, 41, 41, 1);

          &:before {
            content: '';
            position: absolute;
            width: 3px;
            height: 14px;
            background: #6386c1;
            border-radius: 0 2px 2px 0;
            left: 0;
            top: 2px;
          }
        }
        .structure-form {
          padding: 0 10px;
          margin-top: 35px;
          /deep/.label {
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
          }
        }
      }
      .fields-container {
        display: flex;
        flex: 1;
        background: #fff;
        padding: 20px 0 20px 20px;

        .field-group {
          width: 160px;
          flex-shrink: 0;
          margin-right: 20px;

          .field-ul {
            width: 100%;
            .field-item {
              cursor: pointer;
              border-left: 1px solid #f4f4f4;
              height: 34px;
              padding-left: 30px;
              display: flex;
              align-items: center;
              background: #ffffff;
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(41, 41, 41, 1);
              &.active {
                background: #f5f6f9;
                color: rgba(0, 70, 156, 1);
                border-left: 1px solid #6386c1;
              }
              &.hidden-item {
                display: none;
              }
            }
          }
        }
        .field-grid {
          flex: 1;
        }
      }
    }
  }
}
</style>
