import { i18n } from '@/main.js'
import { utils } from '@mtech-common/utils'
export default {
  methods: {
    //修改表格数据，包括checkbox、fieldName
    handleChangeGridData(f) {
      let { index, key, value } = f
      let _moduleType = this.currentTab?.moduleType
      let _currentGroup = this.currentTab.currentGroup
      if (this.tableModuleType.indexOf(_moduleType) > -1) {
        //采购明细  评标  定标
        let _activeLine = this.activeGridLine[_moduleType]
        if (_activeLine < 1) {
          let _fieldGroup = utils.cloneDeep(this.treeGridDataSource[_moduleType][0]['fieldGroup'])
          _fieldGroup[_currentGroup]['data'][index][key] = value
          this.treeGridDataSource[_moduleType][0]['fieldGroup'] = _fieldGroup
          this.tabList.forEach((t) => {
            if (t.moduleType === _moduleType) {
              t.structure = this.treeGridDataSource[_moduleType]
              t.activeStructure = this.treeGridDataSource[_moduleType][0]
            }
          })
          this.tableList.forEach((t) => {
            if (t.moduleType === _moduleType) {
              t.structure = this.treeGridDataSource[_moduleType]
              t.activeStructure = this.treeGridDataSource[_moduleType][0]
            }
          })
        } else {
          let _fieldGroup = utils.cloneDeep(
            this.treeGridDataSource[_moduleType][0]['subtasks'][0]['fieldGroup']
          )
          _fieldGroup[_currentGroup]['data'][index][key] = value
          this.treeGridDataSource[_moduleType][0]['subtasks'][0]['fieldGroup'] = _fieldGroup
          this.tabList.forEach((t) => {
            // if (this.tableModuleType.indexOf(t.moduleType) > -1) {
            //   t.structure = this.treeGridDataSource[_moduleType];
            //   t.activeStructure =
            //     this.treeGridDataSource[_moduleType][0]["subtasks"][0];
            // }
            if (t.moduleType === _moduleType) {
              t.structure = this.treeGridDataSource[_moduleType]
              t.activeStructure = this.treeGridDataSource[_moduleType][0]['subtasks'][0]
            }
          })
          this.tableList.forEach((t) => {
            // if (this.tableModuleType.indexOf(t.moduleType) > -1) {
            //   t.structure = this.treeGridDataSource[_moduleType];
            //   t.activeStructure =
            //     this.treeGridDataSource[_moduleType][0]["subtasks"][0];
            // }
            if (t.moduleType === _moduleType) {
              t.structure = this.treeGridDataSource[_moduleType]
              t.activeStructure = this.treeGridDataSource[_moduleType][0]['subtasks'][0]
            }
          })
        }
      } else {
        let _fieldGroup = utils.cloneDeep(
          this.tabList[this.currentTab.index]['structure'][0]['fieldGroup']
        )
        _fieldGroup[_currentGroup]['data'][index][key] = value
        this.$set(this.tabList[this.currentTab.index]['structure'][0], 'fieldGroup', _fieldGroup)
        this.$set(this.tabList[this.currentTab.index]['activeStructure'], 'fieldGroup', _fieldGroup)
        this.initCurrentFieldDataGrid()
      }
      console.log('表格checkbox勾选', this.tableList, this.tabList)
    },
    //修改表格数据，重命名字段
    changeFieldName(f) {
      // 修改表名
      if (f.key === 'tableName') {
        this.$dialog({
          modal: () => import('../components/renameTable.vue'),
          data: {
            title: i18n.t('修改表名'),
            data: f.data
          },
          success: (data) => {
            let { tableName } = data
            this.handleChangeGridData({
              index: f.index,
              key: f.key,
              value: tableName
            })
          }
        })
        return
      }
      // 修改字段名
      this.$dialog({
        modal: () => import('../components/renameField.vue'),
        data: {
          title: i18n.t('修改字段描述'),
          data: f.data
        },
        success: (data) => {
          let { fieldName } = data
          this.handleChangeGridData({
            index: f.index,
            key: f.key,
            value: fieldName
          })
        }
      })
    },

    //单元格内的checkbox勾选时，改变原数据
    handleChangeCellCheckBox(f) {
      this.handleChangeGridData(f)
    },
    //重新设置配置数据，主要是更新version
    resetModuleConfigInfo(res) {
      let _version = res?.data?.version
      if (_version) {
        this.$set(this.configInfo, 'version', _version)
        localStorage.sourceModuleConfigInfo = JSON.stringify(this.configInfo)
      }
      this.configId = res?.data?.configId
    },
    getStageKeyParams() {
      return { stageKey: '' }
    },
    // 自定义计算表格高度
    resizeGridHeight() {
      let itemList = document.getElementsByClassName('business-config-container') //structure-grid    business-config-container
      let _height = null
      if (itemList.length) {
        _height = itemList[0].offsetHeight
      }
      if (typeof _height === 'number' && _height > 40) {
        if (typeof this.allowPaging === 'boolean' && this.allowPaging) {
          _height = _height - 44
        }
        _height = _height - 47 - 40
      } else {
        _height = 'auto'
      }
      this.gridHeight = _height
    },
    selectTab(e) {
      this.activeTab = e
      this.currentTab = utils.cloneDeep(this.tabList[+e])
    },
    //Tab点击Icon，checkbox切换
    handleClickTabIcon(e) {
      let _fixed = e.fixed
      if (_fixed > 0) {
        this.$toast({ content: `'${e.text}'为固定项.`, type: 'warning' })
        return
      } else {
        e.checked = !e.checked
      }
    },
    //Tab点击Title，Tab切换
    handleClickTabTitle(e) {
      console.log('handleClickTabTitle', e)
    },
    //左侧Nav点击，checkbox切换
    handleClickNavIcon(e) {
      console.log('handleClickNavIcon', e)
      // let _fixed = e.fixed;
      // if (_fixed > 0) {
      //   this.$toast({ content: `'${e.text}'为固定项.`, type: "warning" });
      //   return;
      // } else {
      e.checked = !e.checked
      // }
    },
    //左侧Nav Title点击，Tab切换
    handleClickNavTitle(e) {
      console.log('handleClickNavTitle', e)
    }
  }
}
