import { utils } from '@mtech-common/utils'
import { v1 as uuidv1 } from 'uuid'
import { gridColumnData, treeGridColumnData, hiddenGroup } from './index'
import { sourcingObjTypeList } from '@/constants'
import fileMixin from './fileMixin'
import extMixin from './extMixin'
import cloneDeep from 'lodash/cloneDeep'
export default {
  inject: ['reload'],
  mixins: [fileMixin, extMixin],
  data() {
    return {
      docType: 'rfx',
      configId: null,
      configInfo: {
        version: 0
      },
      isHIERARCHY: false,
      stepsData: [],
      enableConfig: true,
      activeTab: 0,
      activeNav: 0,
      tabList: [],
      leftNavList: [],
      currentTab: {},
      currentStep: 0,
      tableModuleType: [9, 14, 20],
      tableList: [],
      isEditing: true,
      fileChekedNodes: [],
      fileConfigList: [],
      isStartRootNodeFormNull: false,
      gridHeight: 'auto',
      activeGridLine: {},
      getCurrentDataSource: [],
      getVisibleFieldGroup: [],
      treeGridDataSource: {},
      selectionSettings: { type: 'Single', mode: 'Row', enableToggle: false },
      baseModuleFieldGroup: {},
      fieldsGroup: [],
      gridColumnData,
      currentStructure: 0,
      isSetStructure: false, //用于标记，已经配置过表格结构
      baseStructure: {},
      structureList: [
        { text: this.$t('单行结构'), value: 0 },
        { text: this.$t('组结构'), value: 1 },
        { text: this.$t('父子结构'), value: 2 }
      ],
      // 物流模板对应的模型信息
      transportTypeList: [
        'sea_transport', //海运
        'air_transport', //空运
        'railway_transport', //铁路
        'land_transport', //陆运
        'trailer_transport' //拖车
      ],
      //排序预览
      showPreview: false,
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Setting']]
            // tools: []
          },
          grid: {
            height: 280,
            columnData: [{ fieldCode: 'id' }],
            dataSource: [],
            allowFiltering: false,
            allowPaging: false,
            allowSorting: false
          }
        }
      ],
      childPageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Setting']]
            // tools: []
          },
          grid: {
            height: 280,
            columnData: [{ fieldCode: 'id' }],
            dataSource: [],
            allowFiltering: false,
            allowPaging: false,
            allowSorting: false
          }
        }
      ],
      buttons: [
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        },
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      moduleType: null
    }
  },
  computed: {
    groupItemIsActive() {
      return (index) => {
        return this.currentTab.currentGroup == index
      }
    },
    getTreeGridColumnData() {
      return (moduleType) => {
        //未执行过保存，没有版本号  true ||
        let _version = this.configInfo?.version < 1
        //是第一个阶段
        let _isFirstStage = this.getStageKeyParams()['stageKey'] === 'PROJECT_PREPARE'
        //是rfx阶段配置
        let _isRfxType = this.docType === 'rfx'
        //是'采购明细'Tab
        let _isItemTab = moduleType === 14
        let enableEdit = _version && _isFirstStage && _isRfxType && _isItemTab
        return treeGridColumnData(enableEdit)
      }
    }
  },
  watch: {
    treeGridDataSource: {
      handler() {
        this.initCurrentFieldDataGrid()
      },
      deep: true
      // immediate: true,
    },
    activeTab: {
      handler(v) {
        this.initCurrentFieldDataGrid()
        const _arr = [14, 9, 20] //采购明细、评标、定标
        let _find = this.tabList.find((item, index) => index === v)
        this.moduleType = _find?.moduleType
        if (_arr.includes(_find?.moduleType || 0)) {
          this.showPreview = true
        } else {
          this.showPreview = false
        }
      },
      deep: true
      // immediate: true,
    },
    'currentTab.currentGroup': {
      handler() {
        this.initCurrentFieldDataGrid()
      },
      deep: true
      // immediate: true,
    }
  },
  mounted() {},
  methods: {
    save() {
      let _columns = this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.getColumns()
      if (this.currentStructure === 2) {
        let _childColumns = this.$refs.childTemplateRef
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getColumns()
        _columns = [..._columns, ..._childColumns]
      }

      let _columnsCode = _columns.map((item) => {
        return item.foreignKeyField + '__' + item.structureKey
      })
      let _currentModules = {
        moduleType: this.moduleType,
        fields: _columnsCode
      }
      this.saveModuleConfig('preview', _currentModules)
    },
    cancel() {
      this.$refs.previewDialog.ejsRef.hide()
    },
    previewModuleConfig() {
      this.showDialog()
      this.handleDialogConfig()
    },
    // 处理预览弹框配置
    handleDialogConfig() {
      let _find = this.tabList.find((item, index) => index === this.activeTab)
      let _fileModulesFields = this.groupModules(_find?.modules)
      let _structure = cloneDeep(_find['structure'])
      let _parentFields = []
      let _childFields = []
      if (Array.isArray(_structure) && _structure.length) {
        let _fields = this.setResultFields(_structure)
        // 如果是父子结构
        if (this.currentStructure === 2) {
          let _parentStructureKey = _find.activeStructure?.parentStructureKey
            ? _find.activeStructure?.parentStructureKey
            : _find.activeStructure?.structureKey

          _fields.forEach((item) => {
            if (item.structureKey === _parentStructureKey) {
              let _f = _fileModulesFields.parent.find((r) => r.fieldCode === item.fieldCode)
              item.sortLocation = _f?.sortLocation || 0
              _parentFields.push(item)
            } else {
              let _f = _fileModulesFields.child.find((r) => r.fieldCode === item.fieldCode)
              item.sortLocation = _f?.sortLocation || 0
              _childFields.push(item)
            }
          })
        } else {
          // 单行结构
          _fields.forEach((item) => {
            let _f = _fileModulesFields.parent.find((r) => r.fieldCode === item.fieldCode)
            item.sortLocation = _f?.sortLocation || 0
            _parentFields.push(item)
          })
        }
      }
      if (!_parentFields.length) return
      this.setPageConfig(_parentFields, 'parent')
      if (!_childFields.length) return
      this.setPageConfig(_childFields, 'child')
    },
    // 给已勾选的数据分组
    groupModules(modules) {
      let _fields = modules?.fields
      let _structures = modules.structures
      let _parentFields = []
      let _childFields = []
      if (!_structures || _structures?.length < 2) {
        _parentFields = cloneDeep(_fields)
        return {
          parent: _parentFields,
          child: _childFields
        }
      }
      _structures.forEach((item) => {
        let group = _fields.filter((x) => x.structureKey === item.structureKey)
        if (item.parentStructureKey) {
          _childFields.push(...group)
        } else {
          _parentFields.push(...group)
        }
      })
      return {
        parent: _parentFields,
        child: _childFields
      }
    },
    // 添加sortLocation
    addSortLocatiton(modules, baseModules) {
      let _groupFields = this.groupModules(modules) // 数据库缓存的分组fields数据
      let _baseGroupFields = this.groupModules(baseModules) // 页面选择的分组fields数据
      let _newFields = []
      for (var i in _baseGroupFields) {
        _baseGroupFields[i]?.forEach((item) => {
          let _find = _groupFields[i]?.find((x) => x.fieldCode === item.fieldCode)
          item.sortLocation = _find?.sortLocation
          _newFields.push(item)
        })
      }
      baseModules.fields = utils.cloneDeep(_newFields)
      return baseModules
    },
    // pageConfig设置
    setPageConfig(fields, type) {
      // 初始排序
      fields.sort((a, b) => a.sortLocation - b.sortLocation)
      let _columnData = []
      fields.forEach((item) => {
        let _w = item.fieldName.length * 20
        _columnData.push({
          field: item.fieldCode,
          headerText: item.fieldName,
          structureKey: item.structureKey,
          width: _w
        })
      })
      // 设置columns
      if (type === 'child') {
        this.$set(this.childPageConfig[0].grid, 'columnData', _columnData)
      } else {
        this.$set(this.pageConfig[0].grid, 'columnData', _columnData)
      }
    },
    showDialog() {
      this.$refs.previewDialog.ejsRef.show()
    },
    hide() {
      this.$refs.previewDialog.ejsRef.hide()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    //重置Structure标记
    resetStructureTag() {
      this.isSetStructure = false
    },
    //设置Structure标记
    setStructureTag() {
      this.isSetStructure = true
    },
    //设置baseStructure
    setStructure(structures) {
      let res = []
      if (Array.isArray(structures) && structures.length) {
        structures.forEach((e, index) => {
          res.push({
            levelName: structures.length === 2 ? (index > 0 ? '子级' : '父级') : '-',
            priceStatus: e['priceStatus'] > 0,
            objType: e['objType']
          })
        })
      } else {
        res = this.resetStructure()
      }
      return res
    },
    //初始设置baseStructure
    resetStructure() {
      return [
        {
          levelName: '-',
          priceStatus: true,
          objType: 'item'
        }
      ]
    },
    //初始设置baseStructure 父子结构
    resetTreeStructure() {
      return [
        {
          levelName: '父级',
          priceStatus: true,
          objType: 'item'
        },
        {
          levelName: '子级',
          priceStatus: true,
          objType: 'item'
        }
      ]
    },
    //根据baseStructrue 初始化结构数据
    serializeStructure(structures) {
      let _parentKey = uuidv1(),
        _key = uuidv1()
      let _baseStructure = utils.cloneDeep(this.baseStructure)
      if (_baseStructure.length === 2) {
        let _subStructures = structures.length > 1 ? structures[1] : structures[0]
        return [
          {
            ...structures[0],
            parentStructureKey: '',
            structureKey: _parentKey,
            ..._baseStructure[0],
            subtasks: [
              {
                ..._subStructures,
                parentStructureKey: _parentKey,
                structureKey: _key,
                ..._baseStructure[1]
              }
            ]
          }
        ]
      } else {
        return [
          {
            ...structures[0],
            parentStructureKey: '',
            structureKey: _key,
            ..._baseStructure[0]
          }
        ]
      }
    },
    //定义普通表格的结构
    getCustomStructure(fieldGroup = []) {
      let _key = uuidv1()
      return [
        {
          levelName: '-',
          priceStatus: true,
          objType: '',
          parentStructureKey: '',
          structureKey: _key,
          fieldGroup
        }
      ]
    },
    getModuleStructure(_s) {
      if (Array.isArray(_s) && _s.length > 0) {
        return _s
      } else {
        return this.getCustomStructure()
      }
    },
    //定义树形表格的基础结构
    getCustomTreeStructure(fieldGroup) {
      let _parentKey = uuidv1(),
        _key = uuidv1()
      return [
        {
          levelName: '父级',
          priceStatus: true,
          objType: 'item',
          structureKey: _parentKey,
          fieldGroup,
          subtasks: [
            {
              levelName: '子级',
              priceStatus: true,
              objType: 'item',
              parentStructureKey: _parentKey,
              structureKey: _key,
              fieldGroup
            }
          ]
        }
      ]
    },
    //基于baseModule，序列化普通表格
    serializeBaseGridData(_base) {
      let _baseFields = utils.cloneDeep(_base)
      _baseFields.forEach((f) => {
        f.necessary = f.fixed > 0
        f.required = f.fixed > 0
        f.fixed = f.fixed > 0
      })
      return _baseFields
    },
    //通过比对数据，序列化普通表格
    serializeGridData(_base, _current) {
      let _baseFields = utils.cloneDeep(_base)
      let _fields = utils.cloneDeep(_current)
      _baseFields.forEach((f) => {
        f.necessary = f.fixed > 0 //在基础数据中，匹配到，设置necessary为已勾选
        f.required = f.fixed > 0
        f.fixed = f.fixed > 0
        f.fieldName = this.$t(f.fieldName)
        let _find = _fields.filter((e) => {
          return e.fieldKey === f.fieldKey
        })
        if (_find.length > 0) {
          f.necessary = true //在基础数据中，匹配到，设置necessary为已勾选
          f.required = _find[0].required > 0
          f.fixed = _find[0].fixed > 0
          f.fieldName = this.$t(_find[0].fieldName)
        }
      })
      return _baseFields
    },
    //初始加载数据，重置‘结构表格’的选中状态
    selectStructureGrid() {
      //重置 表格active Line
      this.tableList.forEach((e) => {
        this.activeGridLine[e.moduleType] = 0
      })
      setTimeout(() => {
        //执行第一行数据勾选
        let _treeRef = this.$refs.treeGridRef
        if (Array.isArray(_treeRef)) {
          _treeRef.forEach((e) => {
            if (e?.ejsRef && typeof e?.ejsRef?.selectRows === 'function') {
              e?.ejsRef?.selectRows([0])
            }
          })
        }
      }, 500)
    },
    //序列化 配置过的表格数据
    serializeTableContent(tab, module, _tableList) {
      if (tab.containField > 0) {
        //存在表格数据，处理表格数据
        let _baseFields = this.serializeGridData(tab.fields, module['fields'])
        // 普通结构，无层级
        let _structure = []
        if (this.tableModuleType.indexOf(tab.moduleType) > -1) {
          //采购明细  评标  定标
          // module.structures = module.structures
          _structure = utils.cloneDeep(this.getModuleStructure(module.structures))
          if (module.moduleType === 14 && !this.isSetStructure) {
            this.setStructureTag()
            this.currentStructure = module.structureType
            // console.error("currentStructure赋值--2", this.currentStructure);
            this.baseStructure = this.setStructure(module.structures)
          }
          if (_structure.length === 1) {
            let fieldGroup = this.serializeFieldsGroup(
              utils.cloneDeep(_baseFields),
              _structure[0].objType,
              tab
            )
            _structure = this.serializeStructure([
              {
                fieldGroup
              }
            ])
          } else if (_structure.length === 2) {
            let _tempStructure = utils.cloneDeep(_structure)
            let _str = []
            _tempStructure.forEach((st) => {
              let __fields = utils.cloneDeep(module['fields'])
              let _stFields = utils.cloneDeep(
                __fields.filter((_f) => _f.structureKey === st.structureKey)
              )
              let __baseFields = this.serializeGridData(tab.fields, _stFields)
              _str.push({
                fieldGroup: this.serializeFieldsGroup(
                  utils.cloneDeep(__baseFields),
                  st.objType,
                  tab
                )
              })
            })
            _structure = this.serializeStructure(_str)
          }
          this.initBaseModuleFieldGroup(tab)
          this.treeGridDataSource[tab.moduleType] = _structure
        } else {
          _structure = this.getCustomStructure(
            this.serializeFieldsGroup(utils.cloneDeep(_baseFields), '', tab)
          )
        }
        _tableList.push({
          ...module,
          title: module.moduleName,
          structure: _structure,
          activeStructure: _structure[0]
        })
        tab.currentGroup = this.initFieldActiveGroupIndex(_structure[0]['fieldGroup'])
        tab.structure = _structure
        tab.activeStructure = _structure[0]
      }
    },
    //获取当前，使用的第一层结构
    getActiveObjType() {
      let res = []
      let _baseStructure = utils.cloneDeep(this.baseStructure)
      if (Array.isArray(_baseStructure) && _baseStructure.length) {
        _baseStructure.forEach((e) => {
          res.push(e.objType)
        })
      } else {
        res.push('item')
      }
      return res
    },
    //序列化 未设置过的表格数据
    serializeUnMatchTableContent(tab, _tableList) {
      console.log('未配置Tab数据，初始操作', tab)
      //存在表格数据
      let _baseFields = this.serializeBaseGridData(tab.fields)
      let _structure = []
      if (this.tableModuleType.indexOf(tab.moduleType) > -1) {
        //采购明细  评标  定标
        if (!this.isSetStructure) {
          this.setStructureTag()
          if (
            ['item_mould', 'HIERARCHY', 'combination'].includes(this.configInfo?.sourcingObjType)
          ) {
            this.currentStructure = 2
            this.isHIERARCHY = true
            // console.error("currentStructure赋值--4", this.currentStructure);
            this.baseStructure = this.resetTreeStructure()
          } else {
            this.isHIERARCHY = false
            this.currentStructure = 0
            // console.error("currentStructure赋值--3", this.currentStructure);
            this.baseStructure = this.resetStructure()
          }
        }
        let _objType = this.getActiveObjType(),
          _fieldGroupList = []
        _objType.forEach((e) => {
          _fieldGroupList.push({
            fieldGroup: this.serializeFieldsGroup(_baseFields, e, tab)
          })
        })
        _structure = this.serializeStructure(_fieldGroupList)
        this.baseModuleFieldGroup[tab.moduleType] = _fieldGroupList[0]['fieldGroup']
        this.treeGridDataSource[tab.moduleType] = _structure
      } else {
        _structure = this.getCustomStructure(
          this.serializeFieldsGroup(utils.cloneDeep(_baseFields), '', tab)
        )
      }
      _tableList.push({
        title: tab.moduleName,
        moduleType: tab.moduleType,
        moduleKey: tab.moduleKey,
        moduleId: tab.moduleId,
        structure: _structure,
        activeStructure: _structure[0]
      })
      tab.currentGroup = this.initFieldActiveGroupIndex(_structure[0]['fieldGroup'])
      tab.structure = _structure
      tab.activeStructure = _structure[0]
    },
    //序列化 二级Modules
    serializeSubModule(tab) {
      if (tab.subModuleItems && tab.subModuleItems.length) {
        //当前tab存在二级Tabs，显示在左侧
        this.leftNavList.forEach((nav) => {
          if (module.moduleKey == nav.moduleKey) {
            let _navList = nav.navList
            let _moduleSubList = module.subModuleItems
            _navList.forEach((n) => {
              _moduleSubList.forEach((m) => {
                if (n.moduleKey == m.moduleKey) {
                  n.checked = true
                  n.modules = m
                  n.moduleId = m.moduleId
                }
              })
            })
          }
        })
      }
    },
    //序列化当前字段的分组
    initCurrentFieldDataGrid() {
      let _fields = utils.cloneDeep(
        this.tabList[this.currentTab.index]?.activeStructure?.fieldGroup ?? []
      )
      this.getVisibleFieldGroup = _fields
      let _fieldGroup = utils.cloneDeep(_fields[this.currentTab.currentGroup])
      this.getCurrentDataSource = _fieldGroup?.data ?? []
      console.log(
        '序列化当前字段的分组',
        this.tabList[this.currentTab.index],
        this.currentTab,
        _fieldGroup,
        this.getVisibleFieldGroup,
        this.getCurrentDataSource
      )
    },
    //切换结构表格数据，是否报价、字段配置
    changeStructureParams(params) {
      let { index, key, value } = params
      let _moduleType = this.currentTab?.moduleType
      if (index < 1) {
        this.treeGridDataSource[_moduleType][index][key] = value
      } else {
        this.treeGridDataSource[_moduleType][0]['subtasks'][0][key] = value
      }
      if (key === 'objType') {
        let _fieldGroup = utils.cloneDeep(this.baseModuleFieldGroup[_moduleType])
        _fieldGroup.forEach((g) => {
          // '采购明细'Tab
          let _hiddenGroup = Object.prototype.hasOwnProperty.call(hiddenGroup, value)
            ? hiddenGroup[value]
            : []
          g.visible = _hiddenGroup.indexOf(g.group) < 0
        })
        if (index < 1) {
          this.treeGridDataSource[_moduleType][index]['fieldGroup'] = utils.cloneDeep(_fieldGroup)
        } else {
          this.treeGridDataSource[_moduleType][0]['subtasks'][0]['fieldGroup'] =
            utils.cloneDeep(_fieldGroup)
        }
      }
      this.tabList.forEach((t) => {
        if (t.moduleType === _moduleType) {
          t.structure = utils.cloneDeep(this.treeGridDataSource[_moduleType])
          if (index < 1) {
            t.activeStructure = utils.cloneDeep(this.treeGridDataSource[_moduleType][0])
            let _currentGroup = this.initFieldActiveGroupIndex(
              this.treeGridDataSource[_moduleType][0]['fieldGroup']
            )
            t.currentGroup = _currentGroup
            this.currentTab.currentGroup = _currentGroup
          } else {
            t.activeStructure = this.treeGridDataSource[_moduleType][0]['subtasks'][0]
            let _currentGroup = this.initFieldActiveGroupIndex(
              this.treeGridDataSource[_moduleType][0]['subtasks'][0]['fieldGroup']
            )
            t.currentGroup = _currentGroup
            this.currentTab.currentGroup = _currentGroup
          }
        }
      })
      this.tableList.forEach((t) => {
        if (t.moduleType === _moduleType) {
          t.structure = this.treeGridDataSource[_moduleType]
          if (index < 1) {
            t.activeStructure = this.treeGridDataSource[_moduleType][0]
          } else {
            t.activeStructure = this.treeGridDataSource[_moduleType][0]['subtasks'][0]
          }
        }
      })
      this.$forceUpdate()
      this.initCurrentFieldDataGrid()
      console.log('结构参数变化', this.tableList, this.tabList)
    },
    //结构表格，字段类型切换，初始化结构
    initStructure(moduleType, type) {
      let _sampleList = [0, 1]
      let _fixedList = [2]
      let _parentKey = uuidv1(),
        _key = uuidv1()
      if (_sampleList.indexOf(type) > -1) {
        this.treeGridDataSource[moduleType] = this.getCustomStructure(
          utils.cloneDeep(this.baseModuleFieldGroup[moduleType])
        )
      } else if (_fixedList.indexOf(type) > -1) {
        this.treeGridDataSource[moduleType] = [
          {
            levelName: this.$t('父级'),
            priceStatus: true,
            objType: 'item',
            parentStructureKey: '',
            structureKey: _parentKey,
            fieldGroup: utils.cloneDeep(this.baseModuleFieldGroup[moduleType]),
            subtasks: [
              {
                levelName: this.$t('子级'),
                priceStatus: true,
                objType: 'item',
                parentStructureKey: _parentKey,
                structureKey: _key,
                fieldGroup: utils.cloneDeep(this.baseModuleFieldGroup[moduleType])
              }
            ]
          }
        ]
      }
    },
    //表格类型下拉框--切换
    changeStructureType(e) {
      this.tabList.forEach((t) => {
        if (this.tableModuleType.indexOf(t.moduleType) > -1) {
          this.initStructure(t.moduleType, e?.itemData?.value ?? 0)
          t.structure = this.treeGridDataSource[t.moduleType]
          t.activeStructure = this.treeGridDataSource[t.moduleType][0]
          let _currentGroup = this.initFieldActiveGroupIndex(
            this.treeGridDataSource[t.moduleType][0]['fieldGroup']
          )
          t.currentGroup = _currentGroup
          this.currentTab.currentGroup = _currentGroup
        }
      })
      this.tableList.forEach((t) => {
        if (this.tableModuleType.indexOf(t.moduleType) > -1) {
          t.structure = this.treeGridDataSource[t.moduleType]
          t.activeStructure = this.treeGridDataSource[t.moduleType][0]
          t.currentGroup = this.initFieldActiveGroupIndex(
            this.treeGridDataSource[t.moduleType][0]['fieldGroup']
          )
        }
      })
      console.log('表格类型下拉框--切换：', this.tableList, this.tabList)
    },
    //针对父子结构的数据，切换当前选中行
    treeGridRowSelected(e) {
      let _index = 0
      if (Array.isArray(e.data) && e.data.length) {
        _index = e.data[0].index ?? 0
      } else {
        _index = e.data.index ?? 0
      }
      let _moduleType = this.currentTab?.moduleType
      if (this.activeGridLine[_moduleType] !== _index) {
        //如果activeLine发生变化，再执行
        this.tabList.forEach((t) => {
          if (_moduleType === t.moduleType) {
            let _strObject = _index > 0 ? t['structure'][0]['subtasks'][0] : t['structure'][0]
            t.activeStructure = _strObject
            let _currentGroup = this.initFieldActiveGroupIndex(_strObject['fieldGroup'])
            t.currentGroup = _currentGroup
            this.currentTab.currentGroup = _currentGroup
          }
        })
        this.tableList.forEach((t) => {
          if (_moduleType === t.moduleType) {
            let _strObject = _index > 0 ? t['structure'][0]['subtasks'][0] : t['structure'][0]
            t.activeStructure = _strObject
          }
        })
        this.initCurrentFieldDataGrid()
        this.activeGridLine[_moduleType] = _index
        console.log('表格列表-active行-切换：', this.tableList, this.tabList)
      }
    },
    //初始化 BaseModuleField
    initBaseModuleFieldGroup(tab) {
      let _baseFields = this.serializeBaseGridData(tab.fields)
      let _objType = 'item'
      let _fieldGroup = this.serializeFieldsGroup(utils.cloneDeep(_baseFields), _objType, tab)
      this.baseModuleFieldGroup[tab.moduleType] = _fieldGroup
    },
    //表格分组，存在不同位置的‘hidden’，获取第一个active的group
    initFieldActiveGroupIndex(_fieldGroup) {
      let _activeIndex = 0
      if (Array.isArray(_fieldGroup) && _fieldGroup.length) {
        for (let i = 0; i < _fieldGroup.length; i++) {
          if (_fieldGroup[i]['visible']) {
            //找到第一个设置为visible的group，并赋值
            _activeIndex = i
            break
          }
        }
      }
      return _activeIndex
    },
    //序列化 表格字段的分组 头部、采购明细等Tab
    serializeFieldsGroup(fields, objType, tab) {
      let _map = {},
        _array = []
      for (let i = 0; i < fields.length; i++) {
        let _group = fields[i]['fieldGroup']
        if (_map && Object.prototype.hasOwnProperty.call(_map, _group)) {
          _map[_group].data.push(fields[i])
        } else {
          _array.push(_group)
          let _configInfo = utils.cloneDeep(this.configInfo)
          let _sourcingMode = _configInfo?.sourcingMode
          let _moduleType = tab?.moduleType
          let visible = true
          if (_moduleType === 19) {
            visible = false
            // '头部'Tab
            if (_sourcingMode === 'rfq') {
              //询报价
              visible = _group === '询价信息'
            } else if (_sourcingMode === 'invite_bids') {
              //招投标
              visible = _group === '招标信息'
            } else if (_sourcingMode === 'bidding_price') {
              //竞价
              visible = _group === '竞标信息'
            }
          }
          if (this.tableModuleType.indexOf(_moduleType) > -1) {
            // '采购明细'Tab
            if (this.transportTypeList.indexOf(this.configInfo.sourcingObjType) < 0) {
              let _hiddenGroup = Object.prototype.hasOwnProperty.call(hiddenGroup, objType)
                ? hiddenGroup[objType]
                : []
              visible = _hiddenGroup.indexOf(_group) < 0
            } else {
              let _transportValue = sourcingObjTypeList.find(
                (e) => e.value === this.configInfo.sourcingObjType
              )
              visible = _group === _transportValue?.text
              // if (this.transportFinanceModuleType.indexOf(_moduleType) > -1) {
              //   if (_group === "价格信息") {
              //     visible = true;
              //   }
              // }
            }
          }
          _map[_group] = {
            group: this.$t(_group),
            visible,
            data: [fields[i]]
          }
        }
      }
      let _res = []
      for (let i in _map) {
        _res.push(_map[i])
      }
      console.log('serializeFieldsGroup:', _res)
      return _res
    },
    // 获取模块的通用配置
    getCommonBaseModules() {
      this.$API.commonConfig.getBaseModules({ docType: this.docType }).then((res) => {
        let { modules } = res.data
        let _tabList = []
        let _leftNavList = []
        let _fileConfigList = []
        modules.map((item, index) => {
          //构建-顶部Tab数组
          _tabList.push({
            ...item,
            currentGroup: 0,
            modules: item,
            text: item.moduleName,
            checked: item.fixed > 0,
            index
          })
          //构建-文件配置对应的参数
          if (item.moduleType === 2 && item.moduleName === '相关文件') {
            _fileConfigList.push({
              ...item,
              modules: item,
              text: item.moduleName,
              checked: item.fixed > 0
            })
          }
          //构建-左侧第二层Tab数据
          if (item.subModuleItems && item.subModuleItems.length) {
            //当前tab存在二级Tabs，显示在左侧
            let _subItems = item.subModuleItems
            let _navItem = []
            _subItems.forEach((sub) => {
              _navItem.push({
                ...sub,
                parentModuleKey: item.moduleKey,
                parentModuleId: item.moduleId,
                text: sub.moduleName,
                modules: sub,
                checked: false
              })
            })
            _leftNavList.push({
              moduleKey: item.moduleKey,
              moduleId: item.moduleId,
              navList: _navItem
            })
          }
        })
        // _tabList.sort((a, b) => a.sortValue - b.sortValue);
        this.tabList = _tabList //顶部Tabs
        this.leftNavList = _leftNavList //左侧Navs
        this.fileConfigList = _fileConfigList //文件配置
        this.getBusinessConfigDetail()
      })
    },
    // 获取配置基本信息
    getBusinessConfigDetail() {
      this.$API.businessConfig
        .configDetail({
          templateCode: this.configInfo.templateCode,
          docType: this.docType,
          ...this.getStageKeyParams()
        })
        .then((res) => {
          this.resetModuleConfigInfo(res)
          let modules = res?.data?.modules
          if (this.configId) {
            this.getPorFileConfig(this.configId)
          } else {
            this.getPorFileConfig()
          }
          let _tableList = []
          if (Array.isArray(modules) && modules.length) {
            console.log('配置过数据', modules)
            let _equalModules = []
            modules.map((module) => {
              this.tabList.forEach((tab) => {
                if (tab.moduleKey == module.moduleKey) {
                  _equalModules.push(module.moduleKey)
                  //存在moduleKey，对应的Tab设置勾选
                  tab.checked = true
                  tab.modules = module
                  tab.moduleId = module.moduleId
                  this.serializeTableContent(tab, module, _tableList)
                  this.serializeSubModule(tab)
                }
              })
            })
            this.tabList.forEach((tab) => {
              // 数据遍历之后，找一下未使用过的moduleKey，是否存在表格数据
              if (_equalModules.indexOf(tab.moduleKey) < 0 && tab.containField > 0) {
                console.log('配置过数据，但是有些Tab下未设置数据', modules)
                this.serializeUnMatchTableContent(tab, _tableList)
              }
            })
          } else {
            console.log('未配置过数据，初始操作', modules)
            this.tabList.forEach((tab) => {
              if (tab.containField > 0) {
                this.serializeUnMatchTableContent(tab, _tableList)
              }
            })
          }
          this.$nextTick(() => {
            this.$set(this, 'tableList', _tableList)
            console.log('初始化-表格数据', this.tableList, this.tabList)
            this.selectStructureGrid()
          })
          this.currentTab = this.tabList[0]
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    setResultFields(_structure) {
      let _fields = []
      if (Array.isArray(_structure)) {
        _structure.forEach((s) => {
          if (Array.isArray(s?.fieldGroup)) {
            s?.fieldGroup.forEach((sf) => {
              if (sf.visible) {
                let _filterRecords = sf.data.filter((e) => {
                  return e.necessary
                })
                _filterRecords.forEach((_record) => {
                  delete _record.necessary
                  _record.fixed = _record.fixed ? 1 : 0
                  _record.required = _record.required ? 1 : 0
                  _record.selfStructureKey = s.fieldId
                  _record.structureKey = s.structureKey
                  _fields.push(_record)
                })
              }
            })
          }
          if (Array.isArray(s?.subtasks)) {
            _fields = _fields.concat(this.setResultFields(s.subtasks))
          }
        })
      }
      return _fields
    },
    // 改造保存接口,如果type为preview(预览保存),按照排过序的字段更新sortLocation
    saveModuleConfig(type, currentModules) {
      this.$store.commit('startLoading')
      let _moduleItems = []
      let _tabList = utils.cloneDeep(this.tabList)
      _tabList.forEach((t) => {
        let _modules = utils.cloneDeep(t['modules'])
        let _structure = utils.cloneDeep(t['structure'])
        if (Array.isArray(_structure) && _structure.length) {
          let _fields = this.setResultFields(_structure)
          // console.log("保存数据", t, _structure, _fields);
          t.modules.fields = _fields
          if (this.tableModuleType.indexOf(t.moduleType) > -1) {
            let _saveStructure = []
            _structure.forEach((f) => {
              delete f.fieldGroup
              f.priceStatus = f.priceStatus ? 1 : 0
              if (Array.isArray(f.subtasks)) {
                let _sub = utils.cloneDeep(f.subtasks)
                delete f.subtasks
                _saveStructure.push(f)
                _sub.forEach((sub) => {
                  delete sub.fieldGroup
                  sub.priceStatus = sub.priceStatus ? 1 : 0
                  _saveStructure.push(sub)
                })
              } else {
                _saveStructure.push(f)
              }
            })
            t.modules.structures = _saveStructure
            t.modules.structureType = this.currentStructure
          }
        }
        if (t.checked) {
          if ([14, 9, 20].includes(t.moduleType)) {
            // 采购明细、评标、定标 保存时给字段添加sortLocation，值来源于details中的数据
            t.modules = this.addSortLocatiton(_modules, t.modules)
          }
          // 预览保存
          if (type === 'preview' && currentModules.moduleType === t.modules.moduleType) {
            t.modules?.fields.forEach((item) => {
              item.sortLocation =
                currentModules.fields.indexOf(item.fieldCode + '__' + item.structureKey) + 1
            })
          }
          _moduleItems.push(t.modules)
        }
      })
      let _leftNavList = utils.cloneDeep(this.leftNavList)
      _leftNavList.forEach((l) => {
        let _navList = l.navList
        _navList.forEach((n) => {
          let _fieldGroup = n['_fieldGroup']
          if (Array.isArray(_fieldGroup) && _fieldGroup.length) {
            let _fields = []
            _fieldGroup.forEach((f) => {
              let _filterRecords = f.data.filter((e) => {
                return e.necessary
              })
              _filterRecords.forEach((_record) => {
                delete _record.necessary
                _record.fixed = _record.fixed ? 1 : 0
                _record.required = _record.required ? 1 : 0
                _fields.push(_record)
              })
            })
            n.modules.fields = _fields
          }
          if (n.checked) {
            _moduleItems.push(n.modules)
          }
        })
      })
      let _save = {
        templateCode: this.configInfo.templateCode,
        docType: this.docType,
        moduleFileList: this.getSaveFileConfig(),
        // moduleFileList: [],
        moduleItems: _moduleItems,
        ...this.getStageKeyParams()
      }
      if (typeof this.configInfo?.version === 'number') {
        _save.version = this.configInfo.version
      }
      console.log('保存配置---', _save)
      this.$API.businessConfig
        .saveConfigDetail(_save)
        .then((res) => {
          this.$store.commit('endLoading')
          this.$toast({
            content: '保存成功',
            type: 'success'
          })
          this.resetModuleConfigInfo(res)
          if (type === 'preview') {
            this.reload()
          }

          // location.reload();
          // this.getPorFileConfig(this.configId);
          // this.getCommonBaseModules(); //保存完毕，重新获取数据
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
