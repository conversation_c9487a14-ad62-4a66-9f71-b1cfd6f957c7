import Vue from 'vue'
import { i18n } from '@/main.js'
export const gridColumnData = [
  {
    field: 'fieldName',
    headerText: i18n.t('字段描述'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="grid-field-name">
                      <span>{{data.fieldName}}（{{data.fieldCode}}）</span>
                      <mt-icon name="icon_list_edit" @click.native="changeFieldName"></mt-icon>
                    </div>`,
          data() {
            return { data: {} }
          },
          methods: {
            changeFieldName() {
              this.$parent.$emit('changeFieldName', {
                index: this.data.index,
                key: 'fieldName',
                data: this.data
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'fixed',
    headerText: i18n.t('固定字段'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.fixed" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<mt-checkbox v-model="data.fixed" @change="handleChangeCellCheckBox"></mt-checkbox>`,
    //       data() {
    //         return { data: {} };
    //       },
    //       methods: {
    //         handleChangeCellCheckBox(e) {
    //           this.data.fixed = e.checked;
    //           this.$parent.$emit("handleChangeCellCheckBox", {
    //             index: this.data.index,
    //             key: "fixed",
    //             value: e.checked,
    //           });
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'necessary',
    headerText: i18n.t('是否需要'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs">
                      <mt-icon name="icon_V" class="checked" v-if="data.fixed" src=""></mt-icon>
                      <mt-checkbox v-else v-model="data.necessary" @change="handleChangeCellCheckBox"></mt-checkbox>
                    </div>`,
          // template: `<mt-checkbox v-model="data.necessary" @change="handleChangeCellCheckBox"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.necessary = e.checked
              this.$parent.$emit('handleChangeCellCheckBox', {
                index: this.data.index,
                key: 'necessary',
                value: e.checked
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'required',
    headerText: i18n.t('是否必填'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs">
                      <mt-icon name="icon_V" class="checked" v-if="data.fixed" src=""></mt-icon>
                      <mt-checkbox v-else v-model="data.required" @change="handleChangeCellCheckBox"></mt-checkbox>
                    </div>`,
          // template: `<mt-checkbox v-model="data.required" @change="handleChangeCellCheckBox"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.required = e.checked
              this.$parent.$emit('handleChangeCellCheckBox', {
                index: this.data.index,
                key: 'required',
                value: e.checked
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'tableName',
    headerText: i18n.t('表名'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="grid-field-name">
                      <span>{{data.tableName}}</span>
                      <mt-icon name="icon_list_edit" @click.native="changeFieldName"></mt-icon>
                    </div>`,
          data() {
            return { data: {} }
          },
          methods: {
            changeFieldName() {
              this.$parent.$emit('changeFieldName', {
                index: this.data.index,
                key: 'tableName',
                data: this.data
              })
            }
          }
        })
      }
    }
  }
  // {
  //   field: "defaultValue",
  //   headerText: i18n.t("默认值"),
  // },
]

export const treeGridColumnData = (enable = false) => [
  {
    field: 'levelName',
    headerText: i18n.t('层级'),
    width: 100
  },
  {
    field: 'priceStatus',
    headerText: i18n.t('是否报价'),
    width: 100,
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox :disabled="!enable" v-model="data.priceStatus" @change="handleChange"></mt-checkbox>`,
          data() {
            return { data: {}, enable }
          },
          methods: {
            handleChange(e) {
              console.log('切换单个类型', e, 'priceStatus')
              this.data.priceStatus = e.checked
              this.$bus.$emit('handleChangeStructureParams', {
                index: this.data.index,
                key: 'priceStatus',
                value: e.checked
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'objType',
    headerText: i18n.t('字段配置'),
    width: 100,
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-select :disabled="!enable"  v-model="data.objType" :dataSource="typeList" @change="handleChange"></mt-select>`,
          data() {
            return {
              data: {},
              enable,
              typeList: [
                { text: i18n.t('物料'), value: 'item' },
                { text: i18n.t('成本因子'), value: 'cost_model_factor' },
                // { text: i18n.t("模具"), value: "die" },
                { text: i18n.t('BOM'), value: 'bom' }
                // { text: i18n.t("物流信息"), value: "logistics" },
              ]
            }
          },
          methods: {
            handleChange(e) {
              console.log('切换单个类型', e, 'objType')
              this.$bus.$emit('handleChangeStructureParams', {
                index: this.data.index,
                key: 'objType',
                value: e.value
              })
            }
          }
        })
      }
    }
  }
]

export const hiddenGroup = {
  //物料报价
  item: [
    // '成本因子',
    '模具信息',
    'BOM信息',
    //"运输信息",
    '物流信息',
    '海运/铁路',
    '海运/空运',
    '海运',
    '陆运',
    '铁路',
    '空运',
    '拖车'
  ],
  //成本因子
  cost_model_factor: [
    '物料信息',
    '模具信息',
    'BOM信息',
    //"运输信息",
    '物流信息',
    '海运/铁路',
    '海运/空运',
    '海运',
    '陆运',
    '铁路',
    '空运',
    '拖车'
  ],
  //模具信息
  die: [
    '物料信息',
    '成本因子',
    'BOM信息',
    //"运输信息",
    '物流信息',
    '海运/铁路',
    '海运/空运',
    '海运',
    '陆运',
    '铁路',
    '空运',
    '拖车'
  ],
  //BOM信息
  bom: [
    '物料信息',
    // '成本因子',
    '模具信息',
    //"运输信息",
    '物流信息',
    '海运/铁路',
    '海运/空运',
    '海运',
    '陆运',
    '铁路',
    '空运',
    '拖车'
  ],
  //物流信息
  logistics: ['物料信息', '成本因子', '模具信息', 'BOM信息']
}
