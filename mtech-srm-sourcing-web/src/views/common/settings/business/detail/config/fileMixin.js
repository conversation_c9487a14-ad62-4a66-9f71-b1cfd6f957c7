import Vue from 'vue'
import { utils } from '@mtech-common/utils'
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      fileDocType: 'rfx', //文件目录结构，docType
      treeViewData: {
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxs">
                            <div>{{getNodeName}}</div>
                          </div>`,
              data() {
                return { data: {} }
              },
              computed: {
                getNodeName() {
                  return i18n.t(this?.mtData?.nodeName)
                }
              },
              props: {
                mtData: {
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'id',
        text: 'nodeName',
        child: 'moduleFileList'
      }
    }
  },
  methods: {
    addNewRootNode() {
      if (this.treeViewData.dataSource.length === 0 && !this.isStartRootNodeFormNull) {
        this.isStartRootNodeFormNull = true // 只需要根据初始化的datasource维护一次
        this.$nextTick(() => {
          this.addTreeNode({ id: 0 })
        })
      } else {
        this.addTreeNode({
          id: 0
        })
      }
    },
    clickCustomButton(e) {
      if (e.onBtn.text === '新增下级') {
        this.addTreeNode(e)
      } else if (e.onBtn.text === '删除') {
        this.deleteTreeNode(e)
      }
    },
    //新增下级
    addTreeNode(event) {
      const treeView = this.$refs.treeView[0]
      const instance = treeView?.getCommonMethods()
      const newNodeName = this.$t('目录名称')
      return this.$API.businessConfig
        .saveFileConfig({
          configId: this.configId,
          docType: this.fileDocType,
          moduleFileList: [],
          nodeCode: '',
          nodeName: newNodeName,
          parentId: event.id,
          required: 0
        })
        .then((res) => {
          if (res.code === 200) {
            const { id } = res.data
            instance.addNodes([res.data], event.id)
            this.isEditing = true
            instance.beginEdit(id)
          }
        })
    },
    //节点编辑
    nodeEdited(event) {
      if (!this.isEditing) return
      this.isEditing = false
      const { newText, nodeData } = event
      const treeView = this.$refs.treeView[0]
      const instance = treeView?.getCommonMethods()
      this.$API.businessConfig
        .saveFileConfig({
          configId: this.configId,
          docType: this.fileDocType,
          id: nodeData.id,
          nodeName: newText,
          parentId: nodeData.parentID || 0
        })
        .then((res) => {
          if (res.code === 200) {
            // ej2 的 updateNode 只能修改 text ,所以将新建节点的ajax前置
            instance.updateNode(nodeData.id, newText)
          }
        })
    },
    // 删除树节点
    deleteTreeNode(event) {
      const treeView = this.$refs.treeView[0]
      const instance = treeView?.getCommonMethods()
      this.$API.businessConfig.deleteFileConfig({ idList: [event.id] }).then((res) => {
        if (res.code === 200 && res.data) {
          instance.removeNodes([event.id])
          this.$toast({
            content: i18n.t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    getSaveFileConfig() {
      if (Array.isArray(this.$refs.treeView) && this.$refs.treeView.length) {
        const treeView = this.$refs.treeView[0]
        const instance = treeView?.getCommonMethods()
        let _nodes = instance.getAllCheckedNodes()
        // let _records = [...this.treeViewData.dataSource];
        let _records = utils.cloneDeep(this.treeViewData.dataSource)
        this.updateTreeViewNodes(_records, _nodes)
        return _records
      } else {
        return []
      }
    },
    updateTreeViewNodes(_list, nodes) {
      _list.forEach((e) => {
        e.required = 0
        if (nodes.indexOf(e.id) > -1) {
          e.required = 1
        }
        if (Array.isArray(e?.moduleFileList) && e.moduleFileList.length) {
          this.updateTreeViewNodes(e.moduleFileList, nodes)
        }
      })
    },
    serializeFileCheckedNodes() {
      let _records = [...this.treeViewData.dataSource]
      this.fileChekedNodes = this.getTreeViewNodes(_records)
    },
    getTreeViewNodes(_list) {
      let _res = []
      _list.forEach((e) => {
        if (e.required) {
          _res.push(e.id)
        }
        if (Array.isArray(e?.moduleFileList) && e.moduleFileList.length) {
          _res = _res.concat(this.getTreeViewNodes(e.moduleFileList))
        }
      })
      return _res
    },
    serializeTreeViewDataSource(_list) {
      _list.forEach((e) => {
        e.required = e.required ? true : false
        if (Array.isArray(e?.moduleFileList) && e.moduleFileList.length) {
          this.serializeTreeViewDataSource(e.moduleFileList)
        }
      })
    },
    //获取默认的文件目录结构
    getPorFileConfig(configId = '0') {
      this.$API.businessConfig
        .getFileConfig({ configId, docType: this.fileDocType })
        .then((res) => {
          if (Array.isArray(res.data) && res.data.length) {
            this.serializeTreeViewDataSource(res.data)
            this.$set(this.treeViewData, 'dataSource', res.data)
            this.serializeFileCheckedNodes()
          } else {
            this.getPorFileConfig()
          }
        })
    }
  }
}
