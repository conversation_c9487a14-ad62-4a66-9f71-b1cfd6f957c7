<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <div v-if="modalData.type === 'export'">
        {{ modalData.data }}
      </div>
      <div v-else>
        <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
          <mt-form-item prop="remark" label=" ">
            <mt-input
              type="text"
              :multiline="true"
              :rows="23"
              v-model="formObject.remark"
              :placeholder="$t('请输入导入内容')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        remark: '' // 导入内容
      },
      formRules: {
        remark: [
          {
            required: true,
            message: this.$t('请输入导入内容'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      if (this.modalData.type === 'export') {
        this.$emit('confirm-function')
        return
      }
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          const isJson = this.isJSON(this.formObject.remark)
          if (isJson) {
            this.$loading()
            const params = JSON.parse(this.formObject.remark)

            this.$API.businessConfig
              .businessCfgImportJson(params)
              .then((res) => {
                if (res.code === 200) {
                  this.$emit('confirm-function', res)
                  this.$hloading()
                }
              })
              .catch(() => {
                this.$hloading()
              })
          } else {
            this.$toast({
              content: this.$t('导入内容数据格式有误'),
              type: 'warning'
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    //判断一个字符串是不是json格式
    isJSON(str) {
      if (typeof str === 'string') {
        try {
          const obj = JSON.parse(str)
          if (typeof obj === 'object' && obj) {
            return true
          } else {
            return false
          }
        } catch (e) {
          return false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .mt-form textarea.e-input,
  .mt-form .e-input-group textarea.e-input,
  .mt-form .e-input-group.e-control-wrapper textarea.e-input {
    height: 100% !important;
  }
}
</style>
