import { i18n } from '@/main.js'
export const statusList = [
  { value: 0, text: i18n.t('启用') },
  { value: 1, text: i18n.t('停用') }
]

export const rfxTableList = [
  { value: '', text: i18n.t('空') },
  { value: 'rfx_header', text: 'rfx_header' },
  { value: 'rfx_item', text: 'rfx_item' },
  { value: 'rfx_item_ext', text: 'rfx_item_ext' },
  { value: 'rfx_item_die', text: 'rfx_item_die' },
  { value: 'rfx_item_logistics', text: 'rfx_item_logistics' },
  { value: 'mt_supplier_bidding_item', text: 'mt_supplier_bidding_item' },
  { value: 'rfx_bidding_item', text: 'rfx_bidding_item' },
  { value: 'rfx_bidding_item_logistics', text: 'rfx_bidding_item_logistics' },
  { value: 'rfx_item_stage', text: 'rfx_item_stage' },
  { value: 'mt_rfx_annual_logistics_sea_item', text: 'mt_rfx_annual_logistics_sea_item' }
]
export const supplierTableList = [
  { value: '', text: i18n.t('空') },
  { value: 'mt_supplier_rfx_header', text: 'mt_supplier_rfx_header' },
  { value: 'mt_supplier_rfx_item', text: 'mt_supplier_rfx_item' },
  { value: 'mt_supplier_rfx_item_ext', text: 'mt_supplier_rfx_item_ext' },
  { value: 'mt_supplier_rfx_item_die', text: 'mt_supplier_rfx_item_die' },
  { value: 'mt_supplier_bidding_item', text: 'mt_supplier_bidding_item' },
  { value: 'mt_supplier_rfx_item_logistics', text: 'mt_supplier_rfx_item_logistics' },
  { value: 'mt_supplier_bidding_item_logistics', text: 'mt_supplier_bidding_item_logistics' },
  { value: 'mt_rfx_annual_logistics_sea_item', text: 'mt_rfx_annual_logistics_sea_item' }
]
