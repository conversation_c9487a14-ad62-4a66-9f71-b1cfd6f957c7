import { i18n } from '@/main.js'
// toolbar 配置
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'Import', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download ', title: i18n.t('导出') }
]

// 列配置
export const columnData = (t) => [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false
  },
  {
    width: 100,
    field: 'sortValue',
    headerText: i18n.t('排序序号')
  },
  {
    field: 'fieldCode',
    headerText: i18n.t('字段Code')
  },
  {
    field: 'fieldName',
    headerText: i18n.t('字段名称')
  },
  {
    field: 'tableName',
    headerText: i18n.t('所属表名称'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        let { docType } = t.searchForm
        let _dataSource = docType === 'rfx' ? t.rfxTableList : t.supplierTableList
        let _view = _dataSource.find((item) => item.value === data)
        return _view?.text
      }
    },
    editorRender(h, scoped) {
      let { docType } = t.searchForm
      let _dataSource = docType === 'rfx' ? t.rfxTableList : t.supplierTableList

      return (
        <div>
          <mt-select
            v-model={scoped.tableName}
            dataSource={_dataSource}
            allow-filtering={true}
            clearable
            filter-type='Contains'
            placeholder={i18n.t('请选择表名称')}
            onChange={(e) => {
              scoped.tableName = e.value
            }}
          />
        </div>
      )
    }
  }
]

// pageConfig配置
export const pageConfig = (t) => [
  {
    useToolTemplate: false,
    toolbar: toolbar,
    gridId: '00fb085b-dcb1-41ee-a343-13e8d02ef4d8',
    isUseCustomEditor: true,
    grid: {
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal', // 默认normal模式
        newRowPosition: 'Top'
      },
      height: 'auto',
      columnData: columnData(t),
      asyncConfig: {
        url: '/sourcing/tenant/business/fieldGroup/baseField',
        params: {}
      }
    }
  }
]
