<!-- 业务字段明细配置 -->
<template>
  <div class="notice-hander hander">
    <mt-form>
      <mt-row :gutter="20">
        <mt-col :span="6">
          <mt-form-item :label="$t('文档类型')">
            <mt-select
              v-model="searchForm.docType"
              :data-source="docTypeList"
              @change="docTypeChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="6">
          <mt-form-item :label="$t('模块')">
            <mt-select
              v-model="searchForm.moduleId"
              :fields="{ text: 'moduleName', value: 'moduleId' }"
              :data-source="moduleList"
              @change="moduleChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="6">
          <mt-form-item :label="$t('字段组')">
            <mt-select
              v-model="searchForm.fieldGroup"
              :data-source="fieldGroupList"
              :fields="{ text: 'fieldGroup', value: 'fieldGroup' }"
              @change="fieldGroupChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="6">
          <mt-form-item :label="$t('是否历史表')">
            <mt-select
              v-model="searchForm.historyFlag"
              :data-source="historyFlagList"
              @change="fieldGroupChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
    <mt-local-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    />
  </div>
</template>
<script>
import MtLocalTemplatePage from '@/components/template-page'
import { statusList, rfxTableList, supplierTableList } from './config/variable'
import { pageConfig } from './config'
import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'

export default {
  components: {
    MtLocalTemplatePage
  },
  data() {
    return {
      i18n,
      pageConfig: null,
      searchForm: {
        docType: '',
        moduleId: '',
        fieldGroup: '',
        historyFlag: 0
      },
      moduleKey: '',
      docTypeList: [
        { text: 'rfx', value: 'rfx' },
        { text: 'supplier_bid', value: 'supplier_bid' }
      ], //DOCTYPE下拉列表
      moduleList: [], // 模块下拉列表
      fieldGroupList: [], // 字段组下拉列表
      statusList,
      rfxTableList,
      supplierTableList,
      historyFlagList: [
        { value: 0, text: this.$t('否') },
        { value: 1, text: this.$t('是') }
      ]
    }
  },
  created() {},
  mounted() {
    this.pageConfig = pageConfig(this)
  },
  methods: {
    // 表头操作
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length <= 0 && ['Delete', 'Download'].includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const ids = _selectGridRecords.map((item) => item.id)
      if (e.toolbar.id === 'Add') {
        if (!this.searchForm.fieldGroup) {
          this.$toast({ content: this.$t('请先选择字段组'), type: 'warning' })
          return
        }
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (e.toolbar.id === 'Delete') {
        this.handleDelete(ids)
      } else if (e.toolbar.id === 'Import') {
        this.handleImport()
      } else if (e.toolbar.id === 'Download') {
        this.handleExport(ids)
      }
    },
    // 组件事件开始监听
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        let flag = this.validRowData(data)
        if (!flag) args.cancel = true
      }
    },
    // 组件事件结束监听
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.saveRowData(data, rowIndex)
      }
    },
    // 删除
    handleDelete(ids) {
      this.$API.businessConfig.deleteBaseField(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 导入
    handleImport() {
      this.$dialog({
        modal: () => import('./components/importDialog.vue'),
        data: {
          title: this.$t('导入JSON')
        },
        success: (res) => {
          this.$toast({
            content: res.msg ? res.msg : this.$t('导入成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 导出
    handleExport(ids) {
      this.$API.businessConfig
        .exportBaseField(ids)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.$dialog({
              data: {
                title: this.$t('导出JSON(点击"确认"复制到剪切板，原有内容将会被覆盖)'),
                message: data,
                cssClass: 'export-dialog'
              },
              success: () => {
                this.copyToClipboard(JSON.stringify(data))
                this.$toast({
                  content: res.message ? res.message : this.$t('复制成功'),
                  type: 'success'
                })
              }
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },

    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    // 校验行数据
    validRowData(data) {
      let flag = true
      const validateMap = {
        sortValue: {
          value: data.sortValue,
          msg: this.$t('排序序号')
        },
        fieldCode: {
          value: data.fieldCode,
          msg: this.$t('字段Code')
        },
        fieldName: {
          value: data.fieldName,
          msg: this.$t('字段名称')
        }
        // tableName: { // 不校验所属表名称
        //   value: data.tableName,
        //   msg: this.$t('所属表名称')
        // }
      }
      for (const key in validateMap) {
        if (Object.hasOwnProperty.call(validateMap, key)) {
          const element = validateMap[key]
          if (!element.value && element.value !== 0) {
            this.$toast({ content: element.msg, type: 'warning' })
            flag = false
            break
          }
        }
      }
      return flag
    },
    // 保存行数据
    saveRowData(data, rowIndex = 0) {
      let params = {
        ...this.searchForm,
        ...data,
        moduleKey: this.moduleKey
      }
      this.$API.businessConfig
        .saveItemFileConfig(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({ content: res.msg, type: 'warning' })
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg, type: 'warning' })
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 文档类型切换
    docTypeChange(e) {
      this.getModuleListByDocType(e.value)
      this.searchForm.moduleId = ''
      this.searchForm.fieldGroup = ''
    },
    // 模块类型切换
    moduleChange(e) {
      this.getfieldGroupList(e.value)
      this.moduleKey = e.itemData?.moduleKey
    },
    // 字段组类型切换
    fieldGroupChange() {
      // 重置searchForm 达到查询的目的
      this.$set(this.pageConfig[0]['grid']['asyncConfig'], 'params', this.searchForm)
    },
    // 根据docType 获取模块下拉数据
    getModuleListByDocType(docType) {
      this.$API.commonConfig.getBaseModules({ docType: docType }).then((res) => {
        if (res.code === 200) {
          this.moduleList = cloneDeep(res.data?.modules)
        }
      })
    },
    // 根据模块 获取字段组下拉数据
    getfieldGroupList(moduleId) {
      let params = {
        docType: this.searchForm.docType,
        moduleId
      }
      this.$API.businessConfig.queryFieldGroup(params).then((res) => {
        if (res.code === 200) {
          this.fieldGroupList = cloneDeep(res.data)
        }
      })
    }
  }
}
</script>
<style>
.notice-hander .mt-select-index {
  display: inline-block;
}
.export-dialog.e-dialog.small-dialog {
  width: 800px !important;
  height: 500px !important;
}
</style>
<style lang="scss" scope>
.hander {
  padding-top: 15px;
  .mt-form {
    padding-left: 15px;
  }
  .e-content {
    height: calc(100vh - 294px) !important;
  }
}
</style>
