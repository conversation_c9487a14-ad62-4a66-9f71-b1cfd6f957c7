<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="companyCode" :label="$t('公司编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.companyCode"
            :show-clear-button="true"
            :placeholder="$t('请输入公司编码')"
          />
        </mt-form-item>
        <mt-form-item prop="componentCode" :label="$t('组件编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.componentCode"
            :show-clear-button="true"
            :placeholder="$t('请输入组件编码')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入物料编码')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="f976dd8f-d024-4a67-8bab-21c7f624b487"
      :loading="loading"
      :is-show-refresh-bth="true"
      :edit-config="editConfig"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { getTimeList } from '@/utils/obj'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { statusList, listToolbar } from './config/index'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      statusList,
      toolbar: listToolbar,
      editConfig: {
        trigger: 'click',
        mode: 'row'
      },
      searchFormModel: {
        createUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null,
        createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list',
      columns: [],
      companyList: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left'
        },
        {
          width: 50,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 80,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return row.companyCode ? row.companyCode + '-' + row.companyName : ''
            },
            edit: ({ row }) => {
              let companyList = this.companyList
              return [
                <div>
                  <vxe-select
                    v-model={row.companyCode}
                    options={companyList}
                    placeholder={this.$t('请选择')}
                    transfer
                    size='mini'
                    clearable
                    filterable
                    onChange={(e) => {
                      const selectItem = companyList.find((t) => t.orgCode === e.value)
                      row.companyCode = selectItem.orgCode
                      row.companyName = selectItem.orgName
                    }}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'componentCode',
          title: this.$t('组件编码'),
          minWidth: 180,
          editRender: {},
          slots: {
            edit: ({ row, column }) => {
              return [
                <div style='display: flex'>
                  <vxe-input
                    v-model={row.componentCode}
                    readonly='true'
                    clearable
                    type='search'
                    on-search-click={() => this.handleSearchItem(row, column)}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'componentName',
          title: this.$t('组件名称'),
          minWidth: 180
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 180,
          editRender: {
            enabled: true
          },
          slots: {
            edit: ({ row, column }) => {
              return [
                <div style='display: flex'>
                  <vxe-input
                    v-model={row.itemCode}
                    readonly='true'
                    clearable
                    type='search'
                    on-search-click={() => this.handleSearchItem(row, column)}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 180
        },
        {
          field: 'normalComponentPoints',
          title: this.$t('普通元件点数'),
          editRender: { name: 'input', attrs: { type: 'number' } }
        },
        {
          field: 'diffComponentPoints',
          title: this.$t('异形元件点数'),
          editRender: { name: 'input', attrs: { type: 'number' } }
        },
        {
          field: 'totalPoints',
          title: this.$t('总点数')
        },
        {
          field: 'baseSt',
          title: this.$t('基础ST（S）'),
          editRender: { name: 'input', attrs: { type: 'number' } }
        },
        {
          field: 'ckdPackageSt',
          title: this.$t('CKD包装ST（S）'),
          editRender: { name: 'input', attrs: { type: 'number' } }
        },
        {
          field: 'tinStripUseQty',
          title: this.$t('锡条用量（g））'),
          editRender: { name: 'input', attrs: { type: 'number' } }
        },
        {
          field: 'tinWireUseQty',
          title: this.$t('锡线用量（g）'),
          editRender: { name: 'input', attrs: { type: 'number' } }
        },
        {
          field: 'fluxUseQty',
          title: this.$t('助焊剂用量（g）'),
          editRender: { name: 'input', attrs: { type: 'number' } }
        },
        {
          field: 'workHourQuote',
          title: this.$t('工时报价'),
          editRender: { name: 'input', attrs: { type: 'number' } }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          editRender: { name: 'input' }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    this.getCompanyList()
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 物料选择放大镜弹框
    handleSearchItem(row, column) {
      this.$dialog({
        modal: () => import('@/views/common/components/dialog/itemCodeDialog.vue'),
        data: {
          title: column.field === 'componentCode' ? this.$t('组件选择') : this.$t('物料选择'),
          valueSet: 'item'
        },
        success: (data) => {
          if (column.field === 'componentCode') {
            row.componentCode = data.itemCode
            row.componentName = data.itemName
          } else {
            row.itemCode = data.itemCode
            row.itemName = data.itemName
          }
          this.tableRef.setEditRow(row)
        }
      })
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.smtComponentItem
        .querySmtList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      let hasDraftExt = selectedRecords.filter((item) => item.status !== 0)
      let hasEnabled = selectedRecords.filter((item) => item.status === 1)
      let hasEnabledExt = selectedRecords.filter((item) => item.status !== 1)
      if (['batchUpdate', 'save', 'enable', 'disable'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      // 删除 只能删除草稿状态数据
      if (['delete'].includes(e.code)) {
        if (hasDraftExt.length) {
          this.$toast({ content: this.$t('只能删除草稿状态的数据'), type: 'warning' })
          return
        }
      }
      // 启用 启用草稿和禁用的数据
      if (['enable'].includes(e.code)) {
        if (hasEnabled.length) {
          this.$toast({ content: this.$t('只能更新草稿状态的数据'), type: 'warning' })
          return
        }
      }
      // 禁用 禁用启用的数据
      if (['disable'].includes(e.code)) {
        if (hasEnabledExt.length) {
          this.$toast({ content: this.$t('只能更新草稿状态的数据'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd() //新增
          break
        case 'delete': // 删除
          this.handleDelete(selectedRecords)
          break
        case 'save': // 保存
          this.handleSave(selectedRecords)
          break
        case 'enable': // 启用
          this.handleStatus(selectedRecords, 1)
          break
        case 'disable': // 禁用
          this.handleStatus(selectedRecords, 0)
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      const newRow = { status: 0 }
      this.tableData.unshift(newRow)
      // 设置行为编辑状态
      this.tableRef.setEditRow(newRow)
    },
    // 保存
    handleSave(list) {
      const _list = list.map((item) => {
        if (item.id.indexOf('row_') !== -1) delete item.id
        return item
      })
      this.$API.smtComponentItem.saveSmtList(_list).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.handleSearch()
        }
      })
    },
    // 删除
    handleDelete(list) {
      const ids = list.map((item) => item.id)
      const params = {
        ids
      }
      this.$API.smtComponentItem.batchDelList(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('作废成功'),
            type: 'success'
          })
          this.handleSearch()
        }
      })
    },
    // 启用/禁用
    handleStatus(list, status) {
      const ids = list.map((item) => item.id)
      const params = {
        ids,
        status
      }
      this.$API.smtComponentItem.batchUpdateList(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('状态更新成功'),
            type: 'success'
          })
          this.handleSearch()
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    },

    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.label = item.orgCode + '-' + item.orgName
          item.value = item.orgCode
        })
        this.companyList = res.data
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
