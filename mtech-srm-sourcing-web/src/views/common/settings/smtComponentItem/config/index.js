import { i18n } from '@/main'

// 状态列表
export const statusList = [
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('启用') },
  { value: 2, text: i18n.t('禁用') }
]

// 查询列表-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info' },
  { code: 'delete', name: i18n.t('删除'), status: 'info' },
  { code: 'save', name: i18n.t('保存'), status: 'info' },
  { code: 'enable', name: i18n.t('启用'), status: 'info' },
  { code: 'disable', name: i18n.t('禁用'), status: 'info' }
]
