import { i18n } from '@/main.js'
const supplierColumnData = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: 70
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: 150
  },
  {
    field: 'totalRankNum',
    headerText: i18n.t('排名'),
    width: 80,
    formatter: function ({ field }, item) {
      const cellVal = item[field]
      return cellVal == -999 || cellVal == -99 ? '**' : cellVal
    }
  }
]

export const supplierPageConfig = [
  {
    useToolTemplate: false,
    gridId: '9633a2df-1416-4b8a-9990-deecd84e1e61',
    grid: {
      columnData: supplierColumnData,
      allowPaging: false
    }
  }
]

const itemSupplierColumnData = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: 70
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: 150
  },
  {
    field: 'rankNum',
    headerText: i18n.t('排名'),
    width: 80,
    formatter: function ({ field }, item) {
      const cellVal = item[field]
      return cellVal == -999 || cellVal == -99 ? '**' : cellVal
    }
  }
]

export const itemSupplierPageConfig = [
  {
    useToolTemplate: false,
    gridId: '9633a2df-1416-4b8a-9990-deecd84e1e60',
    grid: {
      columnData: itemSupplierColumnData,
      allowPaging: false
    }
  }
]
