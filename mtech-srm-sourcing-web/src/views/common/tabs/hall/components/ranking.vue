<template>
  <div class="time-rank">
    <title-name
      icon-name="icon_solid_Rank1"
      :name="$t('供应商报价信息')"
      :tab-index="0"
      :has-select="false"
      @wuliaoSelect="wuliaoSelect"
    ></title-name>

    <div class="rank-wrap">
      <ul v-if="rankList && rankList.length">
        <li
          v-for="(item, index) in rankList"
          :key="index"
          :class="['rank-li', `rank-li-${index + 1}`]"
        >
          <div class="ra-index">
            <!-- <svg-icon
              :icon-class="`icon_rank_${index + 1}`"
              v-if="index < 3"
            ></svg-icon>
            <span v-else>{{ index + 1 }}</span> -->
          </div>

          <div :class="['names', !item.supplierName && 'names-empty']" :title="item.supplierName">
            {{ item.supplierName == -99 ? '**' : item.supplierName || '-' }}
          </div>

          <div class="price" :title="item.untaxedUnitPrice">
            ¥
            {{ item.untaxedUnitPrice == -99 ? '**' : item.untaxedUnitPrice }}
          </div>

          <div class="times">
            <div class="time-num">
              {{ utils.formatTime(new Date(Number(item.createTime)), 'mm-dd HH:MM:SS') }}
            </div>
            <!-- <div class="time-title">{{ $t("报价次数") }}</div> -->
          </div>

          <!-- <div class="up-down">
            <mt-icon
              name="icon_red"
              v-if="item.trend == 'up'"
              style="color: #ed5633"
            ></mt-icon>
            <mt-icon
              name="icon_blue"
              v-else-if="item.trend == 'down'"
              style="color: #6386c1"
            ></mt-icon>
            <div v-else class="icon_grey"></div>
          </div> -->
        </li>
      </ul>
      <div v-else class="empty">{{ $t('暂无数据') }}~</div>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  components: {
    titleName: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/panel-title" */ 'COMPONENTS/SourcingProject/panelTitle.vue'
      )
  },
  props: {
    tabFrom: {
      type: String,
      default: ''
    },
    purPriceRow: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    purPriceRow: {
      deep: true,
      handler() {
        this.$nextTick(this.getData)
      }
    }
  },
  data() {
    return {
      utils,
      rankList: [],
      packageId: ''
    }
  },
  mounted() {
    this.getData()
    this.$bus.$on('updateRankInfo', (data) => {
      this.getData(data)
    })
  },
  methods: {
    async getData(data) {
      const params = {
        rfxId: this.$route.query.rfxId,
        itemCode: data ? data.itemCode : this.purPriceRow?.itemCode,
        lineNo: data ? data.lineNo : this.purPriceRow?.lineNo
      }
      const reqFn =
        this.tabFrom === 'sup'
          ? this.$API.rfxStatisticSup.priceRank
          : this.$API.rfxDetail.getRFXPriceRank

      const res = await reqFn(params).catch(() => {})
      this.rankList = res?.data?.rfxSupplierPriceRankStatistsicList || []
    },
    // 物料筛选
    wuliaoSelect(id) {
      this.packageId = id
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 8px;
}

.time-rank {
  .rank-wrap {
    width: 100%;
    overflow-y: auto;
    padding: 0 20px;

    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    height: 400px;
    background: #fff;

    .rank-li {
      width: 100%;
      height: 48px;
      margin: 10px 0;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      display: flex;
      align-items: center;
      padding: 0 20px;

      &-1 {
        background: linear-gradient(90deg, rgba(238, 185, 31, 0.1) 0%, rgba(238, 185, 31, 0) 100%);
      }

      &-2 {
        background: linear-gradient(
          90deg,
          rgba(125, 124, 148, 0.1) 0%,
          rgba(126, 125, 149, 0) 100%
        );
      }

      &-3 {
        background: linear-gradient(
          90deg,
          rgba(194, 138, 100, 0.1) 0%,
          rgba(194, 138, 100, 0) 100%
        );
      }

      .ra-index {
        width: 26px;
        text-align: center;
        margin-right: 10px;
        /deep/ .svg-icon {
          width: 26px;
          height: 28px;
        }
      }

      .names {
        flex: 2;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        text-align: left;
        color: rgba(41, 41, 41, 1);
        @extend .text-ellipsis;
        &.names-empty {
          text-align: center;
        }
      }

      .price {
        flex: 2;
        font-size: 14px;
        font-family: DINAlternate;
        font-weight: bold;
        color: rgba(41, 41, 41, 1);
        @extend .text-ellipsis;
      }

      .times {
        flex: 1;
        margin-right: 10px;
        .time-num {
          font-size: 14px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        .time-title {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(154, 154, 154, 1);
        }
      }

      .up-down {
        .mt-icons {
          font-size: 12px;
        }
        .icon_grey {
          width: 16px;
          height: 2px;
          background: rgba(154, 154, 154, 0.5);
          border-radius: 1px;
        }
      }
    }
  }
}

.empty {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(154, 154, 154, 1);
}
</style>
