<template>
  <div class="time-bid">
    <title-name icon-name="icon_solid_Graph" :name="$t('实时竞价')" :tab-index="0" />
    <div class="chart-box">
      <div ref="chart" class="chart" />
      <div class="empty" v-if="!purPriceRankInfo || isNull">{{ this.$t('暂无数据') }}~</div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import utils from '@/utils/utils'
export default {
  components: {
    titleName: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/panel-title" */ 'COMPONENTS/SourcingProject/panelTitle.vue'
      )
  },
  props: {
    tabFrom: {
      type: String,
      default: ''
    },
    purPriceRow: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // chartColors: ["#6386C1", "#EDA133", "#54BF00", "#ED5633", "#9963FD"],
      purPriceRankInfo: null,
      chart: null,
      isNull: false
    }
  },
  watch: {
    purPriceRow: {
      deep: true,
      handler() {
        this.$nextTick(this.getData)
      }
    }
  },
  mounted() {
    this.chart = echarts.init(this.$refs.chart)
    this.getData()
    this.$bus.$on('updateRankInfo', (data) => {
      this.getData(data)
    })
  },
  methods: {
    async getData(data) {
      let params = {
        rfxId: this.$route.query.rfxId,
        itemCode: data ? data.itemCode : this.purPriceRow?.itemCode,
        lineNo: data ? data.lineNo : this.purPriceRow?.lineNo
      }
      const reqFn =
        this.tabFrom === 'sup'
          ? this.$API.rfxStatisticSup.priceInfo
          : this.$API.rfxDetail.getRFXPriceInfo
      const res = await reqFn(params).catch(() => {})
      if (res?.data) {
        this.purPriceRankInfo = res.data
        this.setData()
      }
    },
    setData() {
      const series = this.purPriceRankInfo[0].rfxRealTimeBiddingItemDTOS.map(
        (v) => v.untaxedUnitPrice
      )
      const xAxis = [
        {
          data: this.purPriceRankInfo[0].rfxRealTimeBiddingItemDTOS.map((v) =>
            utils.formatTime(new Date(Number(v.createTime)), 'mm-dd HH:MM:SS')
          )
          // axisLabel: {
          //   show: false,
          // },
        }
      ]
      const option = {
        tooltip: {
          trigger: 'axis',
          show: this.purPriceRankInfo[0].publicQuotation == 1
        },
        grid: { allowFiltering: true, bottom: '3%', containLabel: true },
        xAxis,
        yAxis: {
          type: 'value',
          axisLabel: {
            show: this.purPriceRankInfo[0].publicQuotation == 1
          }
        },
        series: [
          {
            data: series,
            type: 'line',
            smooth: true
          }
        ]
      }
      if (xAxis[0].data.length !== 0) {
        this.chart.setOption(option)
      } else {
        this.isNull = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chart-box {
  width: 100%;
  display: flex;
  overflow: hidden;
  position: relative;

  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  height: 400px;
  background: #fff;

  .chart {
    width: 100%;
    height: 100%;
  }

  .empty {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    color: rgba(154, 154, 154, 1);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
