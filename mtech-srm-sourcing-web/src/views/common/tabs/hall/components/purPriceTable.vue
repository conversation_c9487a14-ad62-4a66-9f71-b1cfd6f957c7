<template>
  <div>
    <div class="aggregate-container" v-if="showTotal">
      <span></span>
      <div class="amount" v-if="tabFrom === 'sup'">
        <div v-if="useTaxedPrice">
          {{ $t('总金额（含税）：') }}<span>{{ taxedTotalPrice }}</span>
        </div>
        <div v-else>
          {{ $t('总金额（未税）：') }}<span>{{ untaxedTotalPrice }}</span>
        </div>
        <div v-if="useTaxedPrice">
          {{ $t('总金额最优价（含税）：') }}<span>{{ bestUntaxedTotalPrice }}</span>
        </div>
        <div v-else>
          {{ $t('总金额最优价（未税）：') }}<span>{{ bestTaxedTotalPrice }}</span>
        </div>
        <div>
          {{ $t('排名：') }}<span>{{ totalRankNum }}</span>
        </div>
      </div>
      <div class="amount" v-else>
        <div>
          {{ $t('总金额最优价（未税）：') }}<span>{{ bestUntaxedTotalPrice }}</span>
        </div>
        <!-- <div class="rank" @click.stop="showRankDialog">排名</div> -->
      </div>
    </div>
    <mt-data-grid :row-selected="rowSelected" v-bind="grid" />
    <mt-dialog ref="dialog" :header="header" css-class="rankDialog">
      <div class="dialog-content">
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </mt-dialog>
    <mt-dialog ref="itemDialog" :header="header">
      <div class="item-dialog-content">
        <mt-template-page ref="itemTemplateRef" :template-config="itemSupplierPageConfig" />
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { getValueByPath } from '@/utils/obj'
// import rankTemplate from '@/components/rankTemplate'
import { supplierPageConfig, itemSupplierPageConfig } from './config'
import Vue from 'vue'
export default {
  props: {
    tabFrom: {
      type: String,
      default: ''
    },
    isLoop: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      grid: {
        allowFiltering: false,
        columnData: [
          {
            field: 'itemCode',
            headerText: this.$t('物料编码/需求描述'),
            formatter: function ({ field }, item) {
              const cellVal1 = item[field]
              const cellVal2 = item['requireDesc']
              if (cellVal1 && cellVal2) {
                return cellVal1 + '/' + cellVal2
              } else if (cellVal1 && !cellVal2) {
                return cellVal1
              } else if (!cellVal1 && cellVal2) {
                return cellVal2
              } else {
                return ''
              }
            }
          },
          {
            field: 'itemName',
            headerText: this.$t('物料名称/需求名称'),
            formatter: function ({ field }, item) {
              const cellVal1 = item[field]
              const cellVal2 = item['requireName']
              if (cellVal1 && cellVal2) {
                return cellVal1 + '/' + cellVal2
              } else if (cellVal1 && !cellVal2) {
                return cellVal1
              } else if (!cellVal1 && cellVal2) {
                return cellVal2
              } else {
                return ''
              }
            }
          },
          {
            field: 'optimalPrice',
            headerText: this.$t('最优价'),
            // formatter: function ({ field }, item) {
            //   const cellVal = item[field]
            //   return cellVal == -999 || cellVal == -99 ? '**' : utils.dataFormat(cellVal)
            // }
            template: function () {
              return {
                template: Vue.component('actionOption', {
                  template: `<div style="color:red;">
                                {{ data.optimalPrice == -999 || data.optimalPrice == -99 ? '**' : data.optimalPrice}}
                              </div>`,
                  data() {
                    return { data: {} }
                  }
                })
              }
            }
          },
          {
            field: 'startingPrice',
            headerText: this.$t('起价（未税）'),
            formatter: function ({ field }, item) {
              const cellVal = item[field]
              return cellVal
            }
          },
          {
            field: 'taxedStartingPrice',
            headerText: this.$t('起价（含税）'),
            formatter: function ({ field }, item) {
              const cellVal = item[field]
              return cellVal
            }
          },
          {
            field: 'minQuoteRange',
            headerText: this.$t('最小竞价幅度'),
            formatter: function ({ field }, item) {
              const cellVal = getValueByPath(item, field)
              const typeVal = getValueByPath(item, 'minQuoteRangeType')
              return typeVal === 1 ? (cellVal || 0) * 100 + '%' : cellVal
            }
          },
          {
            field: 'joinQuantity',
            headerText: this.$t('参与者数量'),
            formatter: function ({ field }, item) {
              const cellVal = item[field]
              return cellVal == -99 || cellVal == -999 ? '**' : cellVal
            }
          }
        ],
        dataSource: []
      },
      bestUntaxedTotalPrice: null,
      bestTaxedTotalPrice: null,
      totalRankNum: null,
      untaxedTotalPrice: null,
      taxedTotalPrice: null,
      useTaxedPrice: false,
      header: this.$t('排名'),
      pageConfig: supplierPageConfig,
      itemSupplierPageConfig: itemSupplierPageConfig,
      socket: null,
      showTotal: false
    }
  },
  mounted() {
    this.initData()
    this.$bus.$on(`procurementRefreshPage`, () => {
      this.initData()
    })
  },
  beforeDestroy() {
    this.$bus.$off(`procurementRefreshPage`)
    clearTimeout(this.socket)
    this.socket = null
  },
  methods: {
    async initData() {
      if (!this.$route.query.rfxId) return
      // 执行初始数据获取
      await this.fetchTableData()
      await this.fetchTotalRanking()
    },

    // 获取表格信息
    async fetchTableData(type) {
      const reqFn =
        this.tabFrom === 'sup'
          ? this.$API.rfxStatisticSup.priceTable
          : this.$API.rfxStatistic.purPriceTable
      const res = await reqFn({
        rfxId: this.$route.query.rfxId
      }).catch(() => {})
      if (res?.data) {
        let _fields = this.grid.columnData.map((e) => e.field)
        if (type !== 'loop') {
          if (this.tabFrom === 'sup' && !_fields.includes('rankNum')) {
            this.grid.columnData.push({
              field: 'rankNum',
              headerText: this.$t('排名'),
              template: function () {
                return {
                  template: Vue.component('actionOption', {
                    template: `<div style="color:red;">
                              {{ data.rankNum == 0 ? '--' : data.rankNum == -999 || data.rankNum == -99 ? '**' : data.rankNum }}
                            </div>`,
                    data() {
                      return { data: {} }
                    }
                  })
                }
              }
            })
          } else {
            // 采方添加报价供应商数量
            this.grid.columnData.push({
              field: 'biddingCount',
              headerText: this.$t('报价供应商数量'),
              formatter: function ({ field }, item) {
                const cellVal = item[field]
                return cellVal == -99 || cellVal == -999 ? '**' : cellVal
              }
            })
          }
        }
        this.grid.dataSource = res.data
        if (res.data?.length > 0 && type !== 'loop') {
          this.$emit('rowSelected', res.data[0])
        }
        // 供应商设置轮询
        if (this.tabFrom === 'sup' && this.isLoop) {
          clearTimeout(this.socket)
          this.socket = null
          this.socket = setTimeout(async () => {
            await this.fetchTableData('loop')
            await this.fetchTotalRanking()
          }, 5000)
        }
      } else {
        clearTimeout(this.socket)
        this.socket = null
      }
    },
    // 获取表格头部排名信息
    async fetchTotalRanking() {
      const reqFnTotalRanking =
        this.tabFrom === 'sup'
          ? this.$API.rfxStatisticSup.totalRanking
          : this.$API.rfxStatistic.totalRanking

      const resRanking = await reqFnTotalRanking({
        rfxId: this.$route.query.rfxId
      })

      if (resRanking.code === 200 && resRanking.data) {
        this.useTaxedPrice = resRanking.data.useTaxedPrice
        this.totalRankNum =
          resRanking.data?.totalRankNum === -99 ? '**' : resRanking.data?.totalRankNum
        this.untaxedTotalPrice =
          resRanking.data?.untaxedTotalPrice === -99 ? '**' : resRanking.data?.untaxedTotalPrice
        this.bestUntaxedTotalPrice =
          resRanking.data?.bestUntaxedTotalPrice === -99
            ? '**'
            : resRanking.data?.bestUntaxedTotalPrice
        this.taxedTotalPrice =
          resRanking.data?.taxedTotalPrice === -99 ? '**' : resRanking.data?.taxedTotalPrice
        this.bestTaxedTotalPrice =
          resRanking.data?.bestTaxedTotalPrice === -99 ? '**' : resRanking.data?.bestTaxedTotalPrice
        this.showTotal = true
      }
    },
    rowSelected({ data }) {
      this.$bus.$emit('updateRankInfo', data)
    },
    // 总价排名弹框
    showRankDialog() {
      this.$refs['dialog'].ejsRef.show()
      this.getSupplierRankData()
    },
    // 获取列表数据
    getSupplierRankData() {
      this.$API.comparativePrice
        .queryTotalPriceRanking({ id: this.$route?.query?.rfxId })
        .then((res) => {
          if (res.code === 200) {
            const _res = res.data
            this.$set(this.pageConfig[0].grid, 'dataSource', _res)
          }
        })
    },
    // 明细排名弹框
    rankItemDialogShow(rfxItemId) {
      this.$refs['itemDialog'].ejsRef.show()
      this.getItemSupplierRankData(rfxItemId)
    },
    // 明细获取列表数据
    getItemSupplierRankData(rfxItemId) {
      let params = {
        rfxId: this.$route?.query?.rfxId,
        rfxItemId: rfxItemId
      }
      this.$API.rfxStatistic.rank(params).then((res) => {
        if (res.code === 200) {
          const _res = res.data
          this.$set(this.itemSupplierPageConfig[0].grid, 'dataSource', _res)
        }
      })
    }
  }
}
</script>
<style>
.rankDialog .e-content {
  height: 100% !important;
}
.item-dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
.rankItem {
  cursor: pointer;
  color: #00469c;
}
</style>

<style scoped lang="scss">
.aggregate-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
  width: 100%;
  padding: 5px 20px;
  border: 1px solid #e8e8e8;
  border-bottom: none;

  span {
    color: #9a9a9a;
  }
  .amount {
    display: flex;
    color: #9a9a9a;
    span {
      color: red;
    }
    div {
      &:last-of-type,
      &:nth-of-type(2) {
        margin-left: 40px;
      }
      &.rank {
        cursor: pointer;
        color: #00469c;
      }
    }
  }
}
</style>
