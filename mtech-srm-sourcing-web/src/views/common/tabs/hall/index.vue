<template>
  <div class="hall-view">
    <!-- 表格 -->
    <purPriceTable class="mb10" @rowSelected="rowSelected" :is-loop="isLoop" :tab-from="tabFrom" />
    <mt-row :gutter="20" v-if="!tableHidden">
      <mt-col :span="12">
        <!-- 实时竞价 -->
        <bidding
          :tab-from="tabFrom"
          v-if="purPriceRow"
          :pur-price-row="purPriceRow"
          ref="bidding"
        />
      </mt-col>
      <mt-col :span="12">
        <!-- 实时排名 -->
        <ranking
          :tab-from="tabFrom"
          v-if="purPriceRow"
          :pur-price-row="purPriceRow"
          ref="ranking"
        />
      </mt-col>
    </mt-row>
  </div>
</template>

<script>
export default {
  props: {
    tabFrom: {
      type: String,
      default: ''
    },
    moduleType: {
      type: Number,
      default: 0
    },
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    purPriceTable: () => import('./components/purPriceTable.vue'),
    bidding: () => import('./components/bidding.vue'),
    ranking: () => import('./components/ranking.vue')
  },
  data() {
    return {
      purPriceRow: null
    }
  },
  computed: {
    detailInfo() {
      return this.quotedPriceData ? this.quotedPriceData : {}
    },
    tableHidden() {
      // 图表隐藏
      return this.tabFrom === 'sup'
    },
    // 是否轮询
    isLoop() {
      let statusList = [10, 11, 13]
      return (
        !statusList.includes(this.quotedPriceData.summaryStatus) &&
        this.quotedPriceData.status == 1 &&
        this.quotedPriceData.joinStatus == 1 &&
        [31, 41].includes(Number(this.quotedPriceData.transferStatus))
      )
    }
  },
  beforeDestroy() {
    this.$bus.$off('updateRankInfo')
  },
  methods: {
    rowSelected(data) {
      this.purPriceRow = data
      // if (this.$route.query.source == "bidding_price") {
      //   this.sokect = setInterval(() => {
      //     this.getDatas();
      //   }, 800);
      // }
    }
  }
}
</script>

<style scoped>
.hall-view {
  padding: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
