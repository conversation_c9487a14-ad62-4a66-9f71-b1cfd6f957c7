<template>
  <div class="hall-view">
    <!-- 物流干线竞价表格 -->
    <logisticsPurPriceTable
      v-if="isLogisticsTrunk"
      class="mb10"
      ref="purPriceTable"
      @rowSelected="rowSelected"
      :tab-from="tabFrom"
      :socket-info="socketInfo"
      :tax-item-list="taxItemList"
      @dataBound="handleDataBound"
    />
    <!-- 表格 -->
    <purPriceTable
      v-else
      class="mb10"
      ref="purPriceTable"
      @rowSelected="rowSelected"
      :tab-from="tabFrom"
      :socket-info="socketInfo"
      :tax-item-list="taxItemList"
      @dataBound="handleDataBound"
    />
    <mt-row v-if="!tableHidden" :gutter="20">
      <mt-col :span="12">
        <!-- 实时竞价 - 物流 -->
        <logisticsBidding
          v-if="isLogisticsTrunk"
          :tab-from="tabFrom"
          :pur-price-row="purPriceRow"
          :socket-info="socketInfo"
          ref="bidding"
        />
        <!-- 实时竞价 -->
        <bidding
          v-else
          :tab-from="tabFrom"
          :pur-price-row="purPriceRow"
          :socket-info="socketInfo"
          ref="bidding"
        />
      </mt-col>
      <mt-col :span="12">
        <!-- 实时排名 物流-->
        <logisticsRanking
          v-if="isLogisticsTrunk"
          :tab-from="tabFrom"
          :pur-price-row="purPriceRow"
          ref="ranking"
          :socket-info="socketInfo"
        />
        <!-- 实时排名 -->
        <ranking
          v-else
          :tab-from="tabFrom"
          :pur-price-row="purPriceRow"
          ref="ranking"
          :socket-info="socketInfo"
        />
      </mt-col>
    </mt-row>
  </div>
</template>

<script>
import Encrypt from 'encryptlong'
import jsrsasign from 'jsrsasign'
import crypto from 'crypto'
import Base64 from 'base-64'
export default {
  props: {
    tabFrom: {
      type: String,
      default: ''
    },
    moduleType: {
      type: Number,
      default: 0
    },
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    strategyConfig: {
      type: Array,
      default: () => []
    },
    ktFlag: {
      type: [Number, String],
      default: () => {}
    },
    isLogisticsTrunk: {
      type: Boolean,
      default: false
    }
  },
  components: {
    purPriceTable: () => import('./components/purPriceTable.vue'),
    logisticsPurPriceTable: () => import('./components/logisticsPurPriceTable.vue'), // 物流干线竞价大厅
    bidding: () => import('./components/bidding.vue'),
    logisticsBidding: () => import('./components/logisticsBidding.vue'),
    ranking: () => import('./components/ranking.vue'),
    logisticsRanking: () => import('./components/logisticsRanking.vue')
  },
  data() {
    return {
      purPriceRow: null,
      dataSource: [],
      sokect: null,
      socketInfo: '',
      taxItemList: [],
      extObj: {
        extTimes: 0,
        extTimeSpan: 0,
        restExtTimes: 0,
        rfxTransferStatus: 0
      }
    }
  },
  computed: {
    detailInfo() {
      return this.quotedPriceData ? this.quotedPriceData : {}
    },
    tableHidden() {
      // 图表隐藏
      return this.tabFrom === 'sup'
    }
  },
  activated() {
    if (!this.sokect) {
      this.getDatas()
    }
  },
  destroyed() {
    clearTimeout(this.sokect)
    this.sokect = null
  },
  deactivated() {
    clearTimeout(this.sokect)
    this.sokect = null
  },
  created() {
    if (this.tabFrom == 'sup') {
      this.initTaxList()
    }
    this.getRsa()
  },
  mounted() {
    // if (this.$route.query.source == "bidding_price") {
    //   this.getDatas();
    // }
  },
  methods: {
    async getRsa() {
      const res = await this.$API.rfxStatisticSup.rsaKey(this.$route.query.rfxId).catch(() => {})
      if (res.code == 200) {
        let data = Base64.decode(res.data)
        this.private = JSON.parse(data).private
        this.public = JSON.parse(data).public
        if (this.$route.query.source == 'bidding_price') {
          this.getDatas()
        }
      }
    },
    // 解密编码
    rsaDecrypt(cipherText, privateKey, isKeyBase64, isTextBase64, isURLCode) {
      const encryptor = new Encrypt({
        default_key_size: 1024,
        padding: crypto.constants.RSA_PKCS1_PADDING
      })

      if (isKeyBase64) {
        encryptor.setPrivateKey(privateKey)
      } else {
        encryptor.setPrivateKey(jsrsasign.hextob64(privateKey))
      }
      cipherText = isURLCode ? decodeURIComponent(cipherText) : cipherText
      cipherText = isTextBase64 ? cipherText : jsrsasign.b64tohex(cipherText)
      return encryptor.decryptLong(cipherText)
    },
    handleDataBound() {
      const grid = this?.$refs?.purPriceTable?.$refs?.templateRef?.$refs?.ejsRef
      if (typeof grid?.selectRows === 'function') {
        // 默认选中暂时隐藏 有可能导致轮询速度加快 待验证
        // grid?.selectRows([0]);
      }
    },
    rowSelected(data) {
      this.purPriceRow = data
      if (this.$route.query.source == 'bidding_price') {
        this.$store.commit('startLoading')
        clearTimeout(this.sokect)
        this.sokect = null
        this.getDatas()
        setTimeout(() => {
          this.$store.commit('endLoading')
        }, 800)
      }
    },
    async getDatas() {
      const params = {
        rfxId: this.$route.query.rfxId,
        rfxItemId: this.purPriceRow ? this.purPriceRow.rfxItemId : '',
        itemCode: this.purPriceRow ? this.purPriceRow.itemCode : '',
        pk: this.public
      }
      if (!params.rfxId || this.$route.query.source !== 'bidding_price') return
      let reqFn = ''
      if (this.isLogisticsTrunk) {
        reqFn =
          this.tabFrom == 'sup'
            ? this.$API.rfxStatisticSup.priceLogisticRankInfoSup
            : this.$API.rfxStatisticSup.priceLogisticRankInfoPur
      } else {
        reqFn =
          this.tabFrom == 'sup'
            ? this.$API.rfxStatisticSup.priceRankInfoSup
            : this.$API.rfxStatisticSup.priceRankInfoPur
      }
      const res = await reqFn(params).catch(() => {})
      if (res.code == 200) {
        if (res.data.encryData) {
          let info = this.rsaDecrypt(res.data.encryData, this.private, true, true, false)
          let info2 = decodeURIComponent(escape(window.atob(info)))
          if (JSON.parse(info2)) {
            res.data.rfxRealTimeBiddingItemDTOS = JSON.parse(info2)
          }
        }
        this.socketInfo = JSON.stringify(res.data)
        // 根据状态降低活关闭轮询,预留量5分钟
        let now = new Date().getTime()
        let startTime = res.data.biddingStartTime
        let endtTime = res.data.biddingEndTime
        if (res.data.biddingStatus == 0) {
          clearTimeout(this.sokect)
          this.sokect = null
          this.sokect = setTimeout(() => {
            this.getDatas()
          }, 30000)
        } else if (
          res.data.biddingStatus == 1 ||
          (startTime &&
            Number(startTime) - Number(now) < 3000000 &&
            Number(startTime) - Number(now) > 0) ||
          (endtTime &&
            Number(endtTime) - Number(now) < 3000000 &&
            Number(endtTime) - Number(now) > 0)
        ) {
          clearTimeout(this.sokect)
          this.sokect = null
          this.sokect = setTimeout(() => {
            this.getDatas()
          }, 800)
        } else {
          clearTimeout(this.sokect)
          this.sokect = null
        }
        // 提示扩展次数
        let obj = {
          extTimes: res.data.extTimes,
          extTimeSpan: res.data.extTimeSpan,
          restExtTimes: res.data.restExtTimes,
          rfxTransferStatus: res.data.rfxTransferStatus
        }
        if (
          res.data.biddingStatus == 1 &&
          (this.extObj.restExtTimes !== res.data.restExtTimes ||
            this.extObj.rfxTransferStatus !== res.data.rfxTransferStatus)
        ) {
          this.$bus.$emit(`updateTime`)
        }
        if (JSON.stringify(this.extObj) !== JSON.stringify(obj) && obj.extTimes > 0) {
          this.$bus.$emit(
            `updateRound`,
            `提示: 可扩展${obj.extTimes}次,已扩展${obj.extTimes - obj.restExtTimes}次,扩展时间为${
              obj.extTimeSpan
            }min`
          )
        }
        this.extObj = obj
      }
    },
    // 获取物流大厅数据
    async getLogisticsData() {
      const reqFn =
        this.tabFrom == 'sup'
          ? this.$API.rfxStatisticSup.priceLogisticRankInfoSup
          : this.$API.rfxStatisticSup.priceLogisticRankInfoPur
      const params = {
        rfxId: this.$route.query.rfxId
      }
      const res = await reqFn(params).catch(() => {})
      if (res.code === 200) {
        this.socketInfo = JSON.stringify(res.data)
        clearTimeout(this.sokect)
        this.sokect = null
        this.sokect = setTimeout(() => {
          this.getLogisticsData()
        }, 1000)
      }
    },
    quotedPrice() {
      this.$dialog({
        modal: () => import('./components/priceDialog.vue'),
        data: {
          dataSource: this.socketInfo,
          rfxId: this.$route.query.rfxId,
          taxItemList: this.taxItemList,
          quotedPriceData: this.quotedPriceData,
          strategyConfig: this.strategyConfig,
          ktFlag: this.ktFlag,
          fcFlag: this.quotedPriceData.rfxGeneralType === 2 // 非采标识
        },
        success: () => {
          // this.$emit("updateTime", data);
        }
      })
    },
    initTaxList() {
      this.$API.pur
        .getRates({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          this.taxItemList = res.data
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.hall-view {
  padding: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
/deep/ .e-spin-hide {
  display: none !important;
}
</style>
