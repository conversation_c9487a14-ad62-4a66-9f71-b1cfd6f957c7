<template>
  <div class="time-rank">
    <title-name
      icon-name="icon_solid_Rank1"
      :name="$t('供应商报价信息')"
      :tab-index="0"
      :has-select="false"
    ></title-name>

    <div class="rank-wrap">
      <ul v-if="rankList && rankList.length">
        <li
          v-for="(item, index) in rankList"
          :key="index"
          :class="['rank-li', `rank-li-${index + 1}`]"
        >
          <div
            :class="['names', !item.supplierName && 'names-empty']"
            :title="
              item.supplierName == -99 || item.supplierName == -999 ? '**' : item.supplierName
            "
          >
            {{ item.supplierName == -99 || item.supplierName == -999 ? '**' : item.supplierName }}
          </div>
          <!-- 供方 -->
          <div
            v-if="!isPur"
            class="price"
            :title="
              supplierId == item.supplierId
                ? item.untaxedUnitPrice
                : !publicQuotationFlag
                ? '**'
                : item.untaxedUnitPrice
            "
          >
            {{
              supplierId == item.supplierId
                ? item.untaxedUnitPrice
                : !publicQuotationFlag
                ? '**'
                : item.untaxedUnitPrice
            }}
          </div>
          <!-- 采方 -->
          <div v-else class="price" :title="openBidFlag != 1 ? '**' : item.untaxedUnitPrice">
            {{ openBidFlag != 1 ? '**' : item.untaxedUnitPrice }}
          </div>

          <div class="times">
            <div class="time-num">
              {{ utils.formatTime(new Date(Number(item.bidTime)), 'mm-dd HH:MM:SS') }}
            </div>
          </div>
        </li>
      </ul>
      <div v-else class="empty">{{ $t('暂无数据') }}~</div>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  components: {
    titleName: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/panel-title" */ 'COMPONENTS/SourcingProject/panelTitle.vue'
      )
  },
  props: {
    socketInfo: {
      type: String,
      default: ''
    },
    tabFrom: {
      type: String,
      default: ''
    }
  },
  watch: {
    socketInfo: {
      deep: true,
      handler(val) {
        if (this.$route.query.source == 'bidding_price') {
          this.rankList = JSON.parse(val).rfxRealTimeBiddingItemDTOS?.reverse()
        }
      }
    }
  },
  computed: {
    isPur() {
      return this.tabFrom !== 'sup'
    },
    publicQuotationFlag() {
      return JSON.parse(this.socketInfo).publicQuotationFlag
    },
    openBidFlag() {
      return JSON.parse(this.socketInfo).openBidFlag
    },
    supplierId() {
      return JSON.parse(this.socketInfo).supplierId
    }
  },
  data() {
    return {
      utils,
      rankList: [],
      packageId: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 8px;
}

.time-rank {
  .rank-wrap {
    width: 100%;
    overflow-y: auto;
    padding: 0 20px;

    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    height: 400px;
    background: #fff;

    .rank-li {
      width: 100%;
      height: 48px;
      margin: 10px 0;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      display: flex;
      align-items: center;
      padding: 0 20px;

      // &-1 {
      //   background: linear-gradient(
      //     90deg,
      //     rgba(238, 185, 31, 0.1) 0%,
      //     rgba(238, 185, 31, 0) 100%
      //   );
      // }

      // &-2 {
      //   background: linear-gradient(
      //     90deg,
      //     rgba(125, 124, 148, 0.1) 0%,
      //     rgba(126, 125, 149, 0) 100%
      //   );
      // }

      // &-3 {
      //   background: linear-gradient(
      //     90deg,
      //     rgba(194, 138, 100, 0.1) 0%,
      //     rgba(194, 138, 100, 0) 100%
      //   );
      // }

      .ra-index {
        width: 26px;
        text-align: center;
        margin-right: 10px;
        /deep/ .svg-icon {
          width: 26px;
          height: 28px;
        }
      }

      .names {
        flex: 2;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        text-align: left;
        color: rgba(41, 41, 41, 1);
        @extend .text-ellipsis;
        &.names-empty {
          text-align: center;
        }
      }

      .price {
        flex: 1;
        font-size: 14px;
        font-family: DINAlternate;
        font-weight: bold;
        color: rgba(41, 41, 41, 1);
        @extend .text-ellipsis;
      }

      .times {
        flex: 1;
        margin-right: 10px;
        .time-num {
          font-size: 14px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        .time-title {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(154, 154, 154, 1);
        }
      }

      .up-down {
        .mt-icons {
          font-size: 12px;
        }
        .icon_grey {
          width: 16px;
          height: 2px;
          background: rgba(154, 154, 154, 0.5);
          border-radius: 1px;
        }
      }
    }
  }
}

.empty {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(154, 154, 154, 1);
}
</style>
