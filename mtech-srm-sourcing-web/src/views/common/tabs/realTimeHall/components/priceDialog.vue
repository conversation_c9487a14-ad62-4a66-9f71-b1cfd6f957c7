<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :enable-resize="false"
    :position="{ X: 'right', Y: 'top' }"
    :buttons="buttons"
    :header="$t('快速报价')"
    @beforeClose="cancel"
    :loading="isLoading"
  >
    <div class="dialog-content">
      <div class="bid-tips" v-if="bidTips">
        <mt-icon name="icon_card_info"></mt-icon>{{ $t(bidTips) }}
      </div>
      <timer-clock
        :start-time="timerStartTime"
        :end-time="timerEndTime"
        :timer-string="timerString"
        :server-time="timerServerTime"
      ></timer-clock>
      <div class="btnBox">
        <!-- <mt-button type="text" v-if="modalData.ktFlag != 1" icon-css="mt-icons mt-icon-icon_table_punish" @click="quoteAuto">{{
          $t('按照最小竞价幅度报价')
        }}</mt-button>
        <span v-else></span> -->
        <mt-button type="text" icon-css="mt-icons mt-icon-icon_table_punish" @click="quoteAuto">{{
          $t('按照最小竞价幅度报价')
        }}</mt-button>
        <mt-button
          type="text"
          icon-css="mt-icons mt-icon-icon_table_fresh"
          @click="getList('refresh')"
          :autofocus="true"
          >{{ $t('刷新') }}</mt-button
        >
      </div>
      <div class="aggregate-container">
        <span>{{ $t('汇总') }}</span>
        <div class="amount">
          <div>
            {{ $t('上次总金额（未税）：') }}<span>{{ lastTotal }}</span>
          </div>
          <div>
            {{ $t('本次总金额（未税）：') }}<span>{{ currentTotal }}</span>
          </div>
        </div>
      </div>
      <table>
        <thead>
          <tr class="bg-highlight">
            <th style="width: 50px" v-if="isSelectPriceControl">
              <!-- <mt-checkbox @change="changeAllItem"></mt-checkbox> -->
              <input
                class="customCheckbox"
                type="checkbox"
                @click="changeAllItem"
                v-model="allCheckBox"
              />
            </th>
            <th style="width: 120px">{{ $t('物料编码/需求描述') }}</th>
            <th style="width: 120px">{{ $t('物料名称/需求名称') }}</th>
            <th style="width: 100">{{ $t('临时物料编码') }}</th>
            <th style="width: 60px">{{ $t('最小报价幅度') }}</th>
            <th style="width: 100">{{ $t('幅度类型') }}</th>
            <th style="width: 100; color: red">{{ $t('最优价') }}</th>
            <th style="width: 100; color: red" v-if="modalData.ktFlag == 1">{{ $t('排名') }}</th>
            <th style="width: 50px" v-if="modalData.ktFlag == 1">{{ $t('降幅比例（%）') }}</th>
            <th style="width: 50px" v-if="modalData.ktFlag == 1">{{ $t('降价幅度（%）') }}</th>
            <th style="width: 100px">{{ $t('单价(含税)') }}</th>
            <th style="width: 100px">{{ $t('单价(未税)') }}</th>
            <th style="width: 150px">{{ $t('税率') }}</th>
            <th style="width: 80">{{ $t('基本单位') }}</th>
            <th style="width: 80">{{ $t('价格单位') }}</th>
            <th style="width: 80px">{{ $t('上次报价(未税)') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in list" :key="item.rfxItemId">
            <td v-if="isSelectPriceControl">
              <input class="customCheckbox" type="checkbox" v-model="item.checked" />
              <!-- <mt-checkbox v-model="item.checked"></mt-checkbox> -->
            </td>
            <td style="max-width: 120px">
              <mt-tooltip :content="getValue(item.itemCode, item.requireDesc)">
                {{ getValue(item.itemCode, item.requireDesc) }}
              </mt-tooltip>
            </td>
            <td style="max-width: 120px">
              <mt-tooltip :content="getValue(item.itemName, item.requireName)">
                <p id="pbox">
                  {{ getValue(item.itemName, item.requireName) }}
                </p>
              </mt-tooltip>
            </td>
            <td>
              {{ item.temporaryItemCode }}
            </td>
            <td>
              {{ item.minQuoteRangeType == 0 ? item.minQuoteRange : item.minQuoteRange + '%' }}
            </td>
            <td>{{ item.minQuoteRangeType == 0 ? $t('金额型') : $t('比例型') }}</td>
            <td style="color: red">{{ item.bestPrice == -999 ? '**' : item.bestPrice }}</td>
            <td style="color: red" v-if="modalData.ktFlag == 1">
              {{
                item.rankNum == 0
                  ? '--'
                  : item.rankNum == -999 || item.rankNum == -99
                  ? '**'
                  : item.rankNum
              }}
            </td>
            <td v-if="modalData.ktFlag == 1">
              {{
                item.taxedFirstPrice
                  ? ((item.taxedUnitPrice / item.taxedFirstPrice) * 100).toFixed(2)
                  : '--'
              }}
            </td>
            <td v-if="modalData.ktFlag == 1">
              <mt-input-number
                css-class="precent-price"
                v-if="item.taxedFirstPrice"
                v-model="item.calPercent"
                @input="inputPercent($event, item)"
                min="0"
                max="100"
                precision="2"
                :show-spin-button="false"
                :show-clear-button="false"
              ></mt-input-number>
            </td>
            <td>
              <mt-input-number
                v-if="modalData.ktFlag == 1"
                v-model="item.taxedUnitPrice"
                @input="inputTaxedPrice($event, item, 'inputPrice')"
                min="0"
                precision="5"
                :unit-switch="true"
              ></mt-input-number>
              <span v-else>{{ item.taxedUnitPrice }}</span>
            </td>
            <td style="padding: 0 3px">
              <mt-input-number
                v-if="modalData.ktFlag != 1"
                v-model="item.calculatePrice"
                @input="inputPrice($event, item)"
                min="0"
                :unit-switch="true"
              ></mt-input-number>
              <span v-else> {{ item.calculatePrice }} </span>
            </td>
            <td>
              <mt-select
                ref="taxRef"
                v-model="item.bidTaxRateCode"
                float-label-type="Never"
                :data-source="taxItemList"
                :fields="{ text: 'taxItemName', value: 'taxItemCode' }"
                :placeholder="$t('请选择适用税率')"
                :allow-filtering="true"
                :filtering="filteringTaxItem"
                popup-width="250px"
                @change="changeBidTaxRateValue($event, item)"
              ></mt-select>
            </td>
            <td>
              {{ item.unitName }}
            </td>
            <td>{{ item.priceUnitNameText }}</td>
            <td>
              {{ item.untaxedUnitPrice ? item.untaxedUnitPrice : item.startingPrice }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </mt-dialog>
</template>

<script>
import Decimal from 'decimal.js'
import cloneDeep from 'lodash/cloneDeep'
import TimerClock from 'COMPONENTS/SourcingProject/timerClock.vue'
import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'
export default {
  components: {
    TimerClock
  },
  data() {
    return {
      list: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: {
            content: this.$t('取消')
          }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('提交报价') }
        }
      ],
      socketInfo: {},
      taxItemList: [],
      isLoading: false,
      timerString: '',
      timerStartTime: 0,
      timerEndTime: 0,
      timerServerTime: 0,
      timer: null,
      lastTotal: 0,
      currentTotal: 0,
      allCheckBox: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    bidTips() {
      if (
        !Array.isArray(this?.modalData?.strategyConfig) ||
        !this?.modalData?.strategyConfig.length
      ) {
        return ''
      }
      let _priceControl = this?.modalData?.strategyConfig[0]['priceControl']
      const STR1 = this.$t('说明：本次寻源不限制报价行数，请勾选您需要提交报价的行')
      const STR2 = this.$t('说明：本次是整单报价，您需要对所有进行报价')
      const STR3 = this.$t('说明：本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行')
      let _tip = ''
      switch (_priceControl) {
        case 'unlimited':
          _tip = STR1
          break
        case 'all':
          _tip = STR2
          break
        case 'first_all':
          //firstPrice  1 首次 0 非首次
          if (this?.modalData?.quotedPriceData?.firstPrice === 1) {
            _tip = STR2
          } else {
            _tip = STR3
          }
          break
        default:
          _tip = ''
          break
      }
      return _tip
    },
    isSelectPriceControl() {
      let isSelect = false
      let _priceControl = this?.modalData?.strategyConfig[0]['priceControl']
      if (_priceControl) {
        //无限制   首次整单报价(非首次，使用勾选数据)
        if (
          _priceControl === 'unlimited' ||
          (_priceControl === 'first_all' && this?.modalData?.quotedPriceData?.firstPrice === 0)
        ) {
          isSelect = true
        }
      }
      return isSelect
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.dataSource) {
      // this.list = JSON.parse(this.modalData.dataSource).rfxItemList;
      this.getList()
      this.socketInfo = JSON.parse(this.modalData.dataSource)
      this.taxItemList = this.modalData.taxItemList
      this.getCurrentTurnsInfo()
      this.getLastTotal()
      this.timer = setInterval(() => {
        this.getCurrentTurnsInfo()
      }, 10000)
    }
  },
  deactivated() {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    getValue(value1, value2) {
      if (value1 && value2) {
        return value1 + '/' + value2
      } else if (value1 && !value2) {
        return value1
      } else if (!value1 && value2) {
        return value2
      } else {
        return ''
      }
    },
    inputPercent(e, item) {
      let _sourcingDirection = JSON.parse(this.modalData.dataSource).sourcingDirection
      let _taxedUnitPrice
      if (_sourcingDirection == 'forward') {
        _taxedUnitPrice = ((item.taxedCurrentPrice * (100 + e)) / 100).toFixed(5)
      } else {
        _taxedUnitPrice = ((item.taxedCurrentPrice * (100 - e)) / 100).toFixed(5)
      }
      item.taxedUnitPrice = _taxedUnitPrice
      this.inputTaxedPrice(_taxedUnitPrice, item, 'inputPercent')
    },
    // 获取采购明细
    async getTenderItems() {
      const res = await this.$API.supplyQdetail
        .tenderItems({
          rfxId: this.modalData.rfxId,
          tabType: 0,
          queryBuilderDTO: {
            page: {
              current: 1,
              size: 999
            }
          }
        })
        .catch(() => {})
      if (res) {
        let records = res.data.records
        let list = this.list
        list.forEach((x) => {
          records.forEach((item, i) => {
            if (x.rfxItemId === item.rfxItemId) {
              this.$set(x, 'unitCode', records[i].unitCode)
              this.$set(x, 'unitName', records[i].unitName)
              // 非采价格单位的转换
              let priceUnit = records[i].priceUnitName
              if (priceUnit === '0.0001') {
                priceUnit = this.$t('万元')
              } else if (priceUnit === '1') {
                priceUnit = this.$t('元')
              }
              this.$set(x, 'priceUnitNameText', priceUnit)
              this.$set(x, 'priceUnitName', records[i].priceUnitName)
              this.$set(x, 'temporaryItemCode', records[i].temporaryItemCode)
              this.$set(x, 'taxedFirstPrice', records[i].taxedFirstPrice)
              this.$set(x, 'taxedCurrentPrice', records[i].biddingItemDTO.taxedUnitPrice)
              this.$set(x, 'calPercent', 0)
              x.bidTaxRateCode = records[i].biddingItemDTO.bidTaxRateCode
              x.bidTaxRateName = records[i].biddingItemDTO.bidTaxRateName
              x.bidTaxRateValue = records[i].biddingItemDTO.bidTaxRateValue
              x.currencyCode = records[i].biddingItemDTO.currencyCode
            }
          })
        })
        this.list = list
      }
    },
    getCurrentTurnsInfo() {
      if (!this.modalData.rfxId) return
      this.$API.supplyQdetail
        .getRfxCountdown({
          rfxId: this.modalData.rfxId
        })
        .then((res) => {
          if (res?.data && JSON.stringify(res?.data) != '{}') {
            this.timerStartTime = new Date().getTime()
            this.timerEndTime = +res.data?.countDown
            this.timerServerTime = +res.data?.currentServerTime
            this.timerString = res.data?.title
          }
        })
    },
    getCurrentTotal() {
      let _total = 0
      let _cloneObj = cloneDeep(this.list)
      if (Array.isArray(_cloneObj) && _cloneObj.length) {
        _cloneObj.forEach((rowData) => {
          // 数量
          let requireQuantity = rowData?.requireQuantity || 1 //未定义数量，按照1计算
          let untaxedUnitPrice = rowData?.calculatePrice || 0
          _total += untaxedUnitPrice * requireQuantity
        })
      }
      this.currentTotal = _total.toFixed(
        this.modalData.ktFlag == 1 || this.modalData.fcFlag ? 5 : PRICE_FRACTION_DIGITS
      )
    },
    getLastTotal() {
      let _params = {
        rfxId: this.modalData.rfxId
        // tabType: this.moduleType,
      }
      this.$API.supplyQdetail
        .getRfxBidAmount(_params)
        .then((res) => {
          if (res.code == 200) {
            this.lastTotal = res?.data?.lastTotalAmount || 0
            this.lastTotal = this.lastTotal.toFixed(
              this.modalData.ktFlag == 1 || this.modalData.fcFlag ? 5 : PRICE_FRACTION_DIGITS
            )
          }
        })
        .catch(() => {
          this.lastTotal = 0
          this.lastTotal = this.lastTotal.toFixed(
            this.modalData.ktFlag == 1 || this.modalData.fcFlag ? 5 : PRICE_FRACTION_DIGITS
          )
        })
    },
    //点击提交
    async confirm() {
      let list = null
      let _priceControl = this?.modalData?.strategyConfig[0]['priceControl']
      if (_priceControl) {
        const quotedPriceData = this.modalData?.quotedPriceData
        //无限制(使用勾选数据)
        const isUnlimit = _priceControl === 'unlimited'
        // 通采 + 首次整单报价(非首次，使用勾选数据)
        const isNotFirstPrice = _priceControl === 'first_all' && quotedPriceData?.firstPrice == 0
        // 非采 + 首次整单报价(非首次，使用勾选数据)
        const isNotFirstPriceByFc =
          _priceControl === 'first_all' &&
          quotedPriceData?.rfxGeneralType === 2 &&
          quotedPriceData?.firstPrice == 0
        if (isUnlimit || isNotFirstPrice || isNotFirstPriceByFc) {
          //firstPrice 1 首次 0 非首次
          let _selectGridRecords = this.list.filter((e) => {
            return (
              (typeof e?.checked === 'boolean' && e.checked) ||
              (typeof e?.checked?.checked === 'boolean' && e.checked.checked)
            )
          })
          if (_selectGridRecords.length < 1) {
            const msg =
              _priceControl === 'unlimited'
                ? this.$t('本次寻源不限制报价行数，请勾选您需要提交报价的行')
                : this.$t('本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行')
            this.$toast({
              content: this.$t(msg),
              type: 'warning'
            })
            this.isLoading = false
            return
          } else {
            list = cloneDeep(_selectGridRecords)
          }
        } else {
          list = cloneDeep(this.list)
        }
      } else {
        list = cloneDeep(this.list)
      }
      list.forEach((v) => {
        v.untaxedUnitPrice = v.calculatePrice
        if (v.minQuoteRangeType == 1) {
          v.minQuoteRange = new Decimal(v.minQuoteRange).div(new Decimal(100)).toNumber()
        }
        delete v.checked //移除临时变量
      })
      const params = {
        abateFlag: false,
        bidPriceItems: list,
        rfxId: this.modalData.rfxId,
        submitStatus: 1
      }
      if (this.modalData.quotedPriceData?.rfxGeneralType === 2) {
        const res = await this.$API.supplyQdetail.getPriceFloatMsg(params)
        if (res.code === 200 && res.data.quoteFloatFlag) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('当前报价与上次报价浮动达到10%，是否确定提交报价？')
            },
            success: () => {
              this.submitData(params)
            }
          })
          return
        }
      }
      this.submitData(params)
    },
    submitData(params) {
      this.isLoading = true
      this.$API.rfxStatisticSup
        .fastSubmit(params)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: res.message ? res.message : this.$t('操作成功'),
              type: 'success'
            })
            // 如果为首次报价，报价完后重载一下以更新提交状态
            if (this.modalData?.quotedPriceData?.submitStatus === 0) {
              this.$set(this.modalData.quotedPriceData, 'submitStatus', 1)
            }
            this.$emit('confirm-function', res.data)
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    async getList(refresh) {
      const params = {
        rfxId: this.modalData.rfxId,
        rfxItemId: '',
        itemCode: ''
      }
      const reqFn = this.$API.rfxStatisticSup.priceRankInfoSup
      const res = await reqFn(params).catch(() => {})
      if (res.code == 200) {
        if (refresh) {
          // 刷新时只更新最优价
          this.socketInfo = res.data
          this.list.forEach((x) => {
            res.data.rfxItemList?.forEach((v) => {
              if (x.itemCode == v.itemCode) {
                x.bestPrice = v.bestPrice
                x.rankNum = v.rankNum
              }
            })
          })
        } else {
          this.list = res.data.rfxItemList
          this.list?.forEach((x) => {
            if (this.modalData.ktFlag != 1) {
              this.$set(x, 'calculatePrice', '')
            } else {
              this.$set(x, 'calculatePrice', x.untaxedUnitPrice)
            }
            // 创造priceUnitNameText字段是为了做价格单位显示，因为后端需要接口原本的priceUnitName去做后续处理，所以不能对直接对priceUnitName做映射
            this.$set(x, 'priceUnitNameText', x.priceUnitName)
            if (!x.minQuoteRange) {
              x.minQuoteRange = 0
            } else if (x.minQuoteRangeType == 1) {
              x.minQuoteRange = new Decimal(x.minQuoteRange).mul(new Decimal(100)).toNumber()
            }
            x.checked = false
          })
        }
        this.getTenderItems()
      }
    },
    quoteAuto() {
      let sourcingDirection = JSON.parse(this.modalData.dataSource).sourcingDirection
      this.list.forEach((x) => {
        if (this.modalData.ktFlag != 1) {
          // 正向
          let untaxedUnitPrice = x.untaxedUnitPrice ? x.untaxedUnitPrice : x.startingPrice
          if (sourcingDirection == 'forward') {
            if (x.minQuoteRangeType == 0) {
              // 金额型
              x.calculatePrice = new Decimal(untaxedUnitPrice)
                .add(new Decimal(x.minQuoteRange))
                .toNumber()
                .toFixed(this.modalData.fcFlag ? 5 : 2)
            } else if (x.minQuoteRangeType == 1) {
              // 比例型
              x.calculatePrice = new Decimal(untaxedUnitPrice)
                .add(
                  new Decimal(untaxedUnitPrice).mul(
                    new Decimal(x.minQuoteRange).div(new Decimal(100))
                  )
                )
                .toNumber()
                .toFixed(this.modalData.fcFlag ? 5 : 2)
            }
          } else {
            // 反向和无限制
            if (x.minQuoteRangeType == 0) {
              x.calculatePrice = new Decimal(untaxedUnitPrice)
                .sub(new Decimal(x.minQuoteRange))
                .toNumber()
                .toFixed(this.modalData.fcFlag ? 5 : 2)
            } else if (x.minQuoteRangeType == 1) {
              x.calculatePrice = new Decimal(untaxedUnitPrice)
                .sub(
                  new Decimal(untaxedUnitPrice).mul(
                    new Decimal(x.minQuoteRange).div(new Decimal(100))
                  )
                )
                .toNumber()
                .toFixed(this.modalData.fcFlag ? 5 : 2)
            }
          }
          x.calculatePrice = x.calculatePrice < 0 ? 0 : x.calculatePrice
          if (x.bidTaxRateValue > 0) {
            x.taxedUnitPrice = new Decimal(x.calculatePrice)
              .add(new Decimal(x.calculatePrice).mul(new Decimal(x.bidTaxRateValue)))
              .toNumber()
              .toFixed(this.modalData.fcFlag ? 5 : 2)
          } else if (x.bidTaxRateValue == 0) {
            x.taxedUnitPrice = x.calculatePrice
          }
        } else {
          // 正向
          let taxedUnitPrice = x.taxedUnitPrice ? x.taxedUnitPrice : x.startingPrice
          if (sourcingDirection == 'forward') {
            if (x.minQuoteRangeType == 0) {
              // 金额型
              x.taxedUnitPrice = new Decimal(taxedUnitPrice)
                .add(new Decimal(x.minQuoteRange))
                .toNumber()
                .toFixed(5)
            } else if (x.minQuoteRangeType == 1) {
              // 比例型
              x.taxedUnitPrice = new Decimal(taxedUnitPrice)
                .add(
                  new Decimal(taxedUnitPrice).mul(
                    new Decimal(x.minQuoteRange).div(new Decimal(100))
                  )
                )
                .toNumber()
                .toFixed(5)
            }
          } else {
            // 反向和无限制
            if (x.minQuoteRangeType == 0) {
              x.taxedUnitPrice = new Decimal(taxedUnitPrice)
                .sub(new Decimal(x.minQuoteRange))
                .toNumber()
                .toFixed(5)
            } else if (x.minQuoteRangeType == 1) {
              x.taxedUnitPrice = new Decimal(taxedUnitPrice)
                .sub(
                  new Decimal(taxedUnitPrice).mul(
                    new Decimal(x.minQuoteRange).div(new Decimal(100))
                  )
                )
                .toNumber()
                .toFixed(5)
            }
          }
          x.taxedUnitPrice = x.taxedUnitPrice < 0 ? 0 : x.taxedUnitPrice
          if (x.bidTaxRateValue > 0) {
            x.calculatePrice = new Decimal(x.taxedUnitPrice)
              .div(new Decimal(x.bidTaxRateValue + 1))
              .toNumber()
              .toFixed(5)
          } else if (x.bidTaxRateValue == 0) {
            x.calculatePrice = x.taxedUnitPrice
          }
        }
      })
    },
    filteringTaxItem(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(this.taxItemList.filter((f) => f?.taxItemName.indexOf(e.text) > -1))
      } else {
        e.updateData(this.taxItemList)
      }
    },
    changeBidTaxRateValue(e, item) {
      item.bidTaxRateValue = e.itemData.taxRate
      item.bidTaxRateCode = e.itemData.taxItemCode
      item.bidTaxRateName = e.itemData.taxItemName
      if (this.modalData.ktFlag != 1) {
        if (item.calculatePrice && item.bidTaxRateValue > 0) {
          item.taxedUnitPrice = new Decimal(item.calculatePrice)
            .add(new Decimal(item.calculatePrice).mul(new Decimal(item.bidTaxRateValue)))
            .toNumber()
            .toFixed(this.modalData.fcFlag ? 5 : 2)
        } else if (item.calculatePrice && item.bidTaxRateValue == 0) {
          item.taxedUnitPrice = item.calculatePrice
        }
      } else {
        if (item.taxedUnitPrice && item.bidTaxRateValue > 0) {
          item.calculatePrice = new Decimal(item.taxedUnitPrice)
            .div(new Decimal(item.bidTaxRateValue + 1))
            .toNumber()
            .toFixed(5)
        } else if (item.taxedUnitPrice && item.bidTaxRateValue == 0) {
          item.calculatePrice = item.taxedUnitPrice
        }
      }
      this.getCurrentTotal()
    },
    changepurUnitCode(e, item) {
      item.bidPurUnitName = e.itemData.unitName
      item.bidPurUnitCode = e.itemData.unitCode
    },
    inputPrice(e, item) {
      // 组件bug能输入字母e
      console.error(e)
      if (e < 0 || e == '-e' || e == 'e') {
        item.calculatePrice = 0
      }
      let reg = this.modalData.fcFlag ? /^\d+(\.\d{1,5})?$/ : /^\d+(\.\d{1,2})?$/
      if (!reg.test(e)) {
        item.calculatePrice = item.calculatePrice.toFixed(this.modalData.fcFlag ? 5 : 2)
      }
      if (item.bidTaxRateValue) {
        item.taxedUnitPrice = new Decimal(e)
          .add(new Decimal(e).mul(new Decimal(item.bidTaxRateValue)))
          .toNumber()
          .toFixed(this.modalData.fcFlag ? 5 : 2)
      } else {
        item.taxedUnitPrice = item.calculatePrice
      }
      if (!e) {
        item.taxedUnitPrice == ''
      }
      this.getCurrentTotal()
    },
    inputTaxedPrice(e, item, type) {
      // 组件bug能输入字母e
      console.error(e)
      if (e < 0 || e == '-e' || e == 'e') {
        item.taxedUnitPrice = 0
      }
      if (!/^\d+(\.\d{1,5})?$/.test(e)) {
        item.taxedUnitPrice = item.taxedUnitPrice.toFixed(this.modalData.ktFlag == 1 ? 5 : 2)
      }
      if (item.bidTaxRateValue) {
        item.calculatePrice = new Decimal(e)
          .div(new Decimal(item.bidTaxRateValue + 1))
          .toNumber()
          .toFixed(this.modalData.ktFlag == 1 ? 5 : 2)
      } else {
        item.calculatePrice = item.taxedUnitPrice
      }

      if (type == 'inputPrice') {
        let _sourcingDirection = JSON.parse(this.modalData.dataSource).sourcingDirection
        if (_sourcingDirection == 'forward' && item.taxedUnitPrice > item.taxedCurrentPrice) {
          item.calPercent = ((item.taxedUnitPrice / item.taxedCurrentPrice - 1) * 100).toFixed(2)
        }
        if (_sourcingDirection == 'reverse' && item.taxedUnitPrice < item.taxedCurrentPrice) {
          item.calPercent = ((1 - item.taxedUnitPrice / item.taxedCurrentPrice) * 100).toFixed(2)
        }
      }
      if (!e) {
        item.calculatePrice = ''
        item.calPercent = null
      }
      this.getCurrentTotal()
    },
    changeAllItem() {
      this.allCheckBox = !this.allCheckBox
      this.list.forEach((e) => {
        e.checked = this.allCheckBox
      })
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-input-number .input--wrap .precent-price {
  padding-right: 0px !important;
}
</style>
<style lang="scss">
.customCheckbox {
  width: 16px;
  height: 16px;
  visibility: visible !important;
}
.dialog-main {
  height: 100% !important;
  width: 70vw !important;
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 10px;
    }
  }
  .btnBox {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .aggregate-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fafafa;
    width: 100%;
    padding: 5px 20px;
    border: 1px solid #e8e8e8;
    border-bottom: none;

    span {
      color: #9a9a9a;
    }
    .amount {
      display: flex;
      color: #9a9a9a;
      span {
        color: #00469c;
      }
      div {
        &:last-of-type {
          margin-left: 40px;
        }
      }
    }
  }
}
.dialog-content .bid-tips {
  color: #4d5b6f;
}
.dialog-content table,
.dialog-content td,
.dialog-content th {
  border: solid 1px #e0e0e0;
  padding: 5px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  // white-space: nowrap;
  font-size: 12px;
}

.dialog-content table {
  width: 100%;
  text-align: center;
  border-collapse: collapse;
}
.bg-highlight {
  background-color: rgb(250, 250, 250);
  height: 40px;
  -webkit-print-color-adjust: exact;
}
#pbox {
  width: 100px;
  overflow: hidden;
  height: 18px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
