<template>
  <div class="time-bid">
    <title-name icon-name="icon_solid_Graph" :name="$t('实时竞价')" :tab-index="0" />
    <div class="chart-box">
      <div ref="chart" class="chart" />
      <div class="empty" v-show="purPriceRankInfo.length == 0">{{ this.$t('暂无数据') }}~</div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import utils from '@/utils/utils'
export default {
  components: {
    titleName: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/panel-title" */ 'COMPONENTS/SourcingProject/panelTitle.vue'
      )
  },
  props: {
    tabFrom: {
      type: String,
      default: ''
    },
    purPriceRow: {
      type: Object,
      default: () => {}
    },
    socketInfo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      purPriceRankInfo: [],
      chart: null,
      publicQuotationFlag: true //是否公开报价
    }
  },
  watch: {
    socketInfo: {
      handler(val) {
        if (this.$route.query.source == 'bidding_price') {
          // 供应商竞价 大厅Tab不显示曲线图
          let _ktFlag = sessionStorage.getItem('purDetailKtFlag')
          if (_ktFlag != null && _ktFlag == 1) {
            this.purPriceRankInfo.length = 0
          } else {
            this.purPriceRankInfo = JSON.parse(val).rfxRealTimeBiddingItemDTOS || []
            this.publicQuotationFlag = JSON.parse(val).publicQuotationFlag
            this.newSetData()
          }
        }
      }
    }
  },
  mounted() {
    this.chart = echarts.init(this.$refs.chart)
  },
  methods: {
    newSetData() {
      const series = this.purPriceRankInfo.map((v) => v.taxedTotalPrice)
      const xAxis = [
        {
          type: 'category',
          boundaryGap: false,
          data: this.purPriceRankInfo.map((v) =>
            utils.formatTime(new Date(Number(v.bidTime)), 'mm-dd HH:MM:SS')
          ),
          show: this.purPriceRankInfo.length > 0
        }
      ]
      const option = {
        tooltip: {
          trigger: 'axis',
          show: this.publicQuotationFlag
        },
        grid: { allowFiltering: true, bottom: '3%', containLabel: true },
        xAxis,
        yAxis: {
          type: 'value',
          axisLabel: {
            show: this.publicQuotationFlag
          }
        },
        series: [
          {
            data: series,
            type: 'line',
            smooth: true
          }
        ]
      }
      this.chart?.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chart-box {
  width: 100%;
  display: flex;
  overflow: hidden;
  position: relative;

  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  height: 400px;
  background: #fff;

  .chart {
    width: 100%;
    height: 100%;
  }

  .empty {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    color: rgba(154, 154, 154, 1);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
