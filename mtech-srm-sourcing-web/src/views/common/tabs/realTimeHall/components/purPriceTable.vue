<template>
  <div>
    <div class="aggregate-container">
      <span></span>
      <div class="amount" v-if="tabFrom === 'sup'">
        <div>
          {{ $t('总金额（未税）：') }}<span>{{ untaxedTotalPrice }}</span>
        </div>
        <div>
          {{ $t('总金额最优价（未税）：') }}<span>{{ bestUntaxedTotalPrice }}</span>
        </div>
        <div>
          {{ $t('排名：') }}<span>{{ totalRankNum }}</span>
        </div>
      </div>
      <div class="amount" v-else>
        <div>
          {{ $t('总金额最优价（未税）：') }}<span>{{ bestUntaxedTotalPrice }}</span>
        </div>
        <!-- <div class="rank" @click.stop="showRankDialog">{{ $t('排名') }}</div> -->
      </div>
    </div>
    <mt-data-grid
      ref="templateRef"
      @dataBound="dataBound"
      :row-selected="rowSelected"
      v-bind="grid"
    />
    <mt-dialog ref="dialog" :header="header" css-class="rankDialog">
      <div class="dialog-content">
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </mt-dialog>
    <mt-dialog ref="itemDialog" :header="header">
      <div class="item-dialog-content">
        <mt-template-page ref="itemTemplateRef" :template-config="itemSupplierPageConfig" />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import Decimal from 'decimal.js'
import { supplierPageConfig, itemSupplierPageConfig, minQuoteRangeTypeConfig } from './config'
export default {
  props: {
    tabFrom: {
      type: String,
      default: ''
    },
    socketInfo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeIndex: 0,
      grid: {
        allowFiltering: false,
        columnData: [
          {
            field: 'itemCode',
            headerText: this.$t('物料编码/需求描述'),
            formatter: function ({ field }, item) {
              const cellVal1 = item[field]
              const cellVal2 = item['requireDesc']
              if (cellVal1 && cellVal2) {
                return cellVal1 + '/' + cellVal2
              } else if (cellVal1 && !cellVal2) {
                return cellVal1
              } else if (!cellVal1 && cellVal2) {
                return cellVal2
              } else {
                return ''
              }
            }
          },
          {
            field: 'itemName',
            headerText: this.$t('物料名称/需求名称'),
            formatter: function ({ field }, item) {
              const cellVal1 = item[field]
              const cellVal2 = item['requireName']
              if (cellVal1 && cellVal2) {
                return cellVal1 + '/' + cellVal2
              } else if (cellVal1 && !cellVal2) {
                return cellVal1
              } else if (!cellVal1 && cellVal2) {
                return cellVal2
              } else {
                return ''
              }
            }
          },
          {
            field: 'bestPrice',
            headerText: this.$t('最优价'),
            headerTemplate: () => {
              return {
                template: Vue.component('requiredCell', {
                  template: `
                  <div class="headers">
                    <span style="color: red" class="e-headertext">${this.$t('最优价')}</span>
                  </div>
                `
                })
              }
            },
            // formatter: function ({ field }, item) {
            //   const cellVal = item[field]
            //   return cellVal == -999 || cellVal == -99 ? '**' : utils.dataFormat(cellVal)
            // }
            template: function () {
              return {
                template: Vue.component('actionOption', {
                  template: `<div style="color:red;">
                                {{ data.bestPrice == -999 || data.bestPrice == -99 ? '**' : data.bestPrice}}
                              </div>`,
                  data() {
                    return { data: {} }
                  }
                })
              }
            }
          },
          {
            field: 'taxedStartingPrice',
            headerText: this.$t('起拍价(含税)'),
            formatter: function ({ field }, item) {
              const cellVal = item[field]
              return cellVal == -999 || cellVal == -99 ? '**' : cellVal
            }
          },
          {
            field: 'startingPrice',
            headerText: this.$t('起拍价(未税)'),
            formatter: function ({ field }, item) {
              const cellVal = item[field]
              return cellVal == -999 || cellVal == -99 ? '**' : cellVal
            }
          },
          {
            field: 'minQuoteRangeType',
            headerText: this.$t('幅度类型'),
            formatter: function ({ field }, item) {
              const cellVal = item[field]
              return minQuoteRangeTypeConfig[cellVal]
            }
          },
          {
            field: 'minQuoteRange',
            headerText: this.$t('最小竞价幅度'),
            formatter: function ({ field }, item) {
              const cellVal = item[field]
              const type = item['minQuoteRangeType']
              return type === 1 && cellVal > 0
                ? new Decimal(cellVal).mul(new Decimal(100)).toNumber() + '%'
                : cellVal
            }
          },
          {
            field: 'joinQuantity',
            headerText: this.$t('参与者数量'),
            formatter: function ({ field }, item) {
              const cellVal = item[field]
              return cellVal == -999 || cellVal == -99 ? '**' : cellVal
            }
          }
        ],
        dataSource: []
      },
      bestUntaxedTotalPrice: null,
      totalRankNum: null,
      untaxedTotalPrice: null,
      showTotalRanking: false,
      header: this.$t('排名'),
      pageConfig: supplierPageConfig,
      itemSupplierPageConfig: itemSupplierPageConfig
    }
  },
  watch: {
    socketInfo: {
      deep: true,
      handler(val) {
        if (JSON.stringify(this.grid.dataSource) !== JSON.stringify(JSON.parse(val).rfxItemList)) {
          this.grid.dataSource = JSON.parse(val).rfxItemList
          // setTimeout(() => {
          //   let _treeRef = this.$refs.treeGridRef;
          //   if (
          //     _treeRef?.ejsRef &&
          //     typeof _treeRef?.ejsRef?.selectRows === "function"
          //   ) {
          //     _treeRef.ejsRef?.selectRows([
          //       this.activeIndex ? this.activeIndex : 0,
          //     ]);
          //   }
          // }, 500);
        }
      }
    },
    tabFrom: {
      immediate: true,
      handler(val) {
        let supData = []
        if (val == 'sup') {
          supData = [
            {
              field: 'rankNum',
              headerText: this.$t('排名'),
              headerTemplate: () => {
                return {
                  template: Vue.component('requiredCell', {
                    template: `
                  <div class="headers">
                    <span style="color: red" class="e-headertext">${this.$t('排名')}</span>
                  </div>
                `
                  })
                }
              },
              // formatter: function ({ field }, item) {
              //   const cellVal = item[field]
              //   return cellVal == 0 ? '--' : cellVal == -999 || cellVal == -99 ? '**' : cellVal
              // }
              template: function () {
                return {
                  template: Vue.component('actionOption', {
                    template: `<div style="color:red;">
                                {{ data.rankNum == 0 ? '--' : data.rankNum == -999 || data.rankNum == -99 ? '**' : data.rankNum}}
                              </div>`,
                    data() {
                      return { data: {} }
                    }
                  })
                }
              }
            },
            {
              field: 'reduceAmount',
              headerText: this.$t('降幅')
            },
            {
              field: 'untaxedUnitPrice',
              headerText: this.$t('单价(未税)')
            },
            {
              field: 'bidTaxRateValue',
              headerText: this.$t('税率')
            },
            {
              field: 'taxedUnitPrice',
              headerText: this.$t('单价(含税)')
            }
          ]
        } else {
          supData = [
            {
              field: 'biddingCount',
              headerText: this.$t('报价供应商数量'),
              formatter: function ({ field }, item) {
                const cellVal = item[field]
                return cellVal == -99 || cellVal == -999 ? '**' : cellVal
              }
            }
          ]
          // supData = [
          //   {
          //     field: 'ranking',
          //     headerText: this.$t('排名'),
          //     template: function () {
          //       return {
          //         template: Vue.component('actionOption', {
          //           template: `<div class="rankItem" @click.stop="rank">{{ $t('排名') }}</div>`,
          //           data() {
          //             return { data: {} }
          //           },
          //           methods: {
          //             rank() {
          //               _this.rankItemDialogShow(this.data.rfxItemId)
          //             }
          //           }
          //         })
          //       }
          //     }
          //   }
          // ]
          // supData = []
        }
        this.grid.columnData = [...this.grid.columnData, ...supData]
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    async initData() {
      // 表头排名信息
      const reqFnTotalRanking =
        this.tabFrom === 'sup'
          ? this.$API.rfxStatisticSup.totalRanking
          : this.$API.rfxStatistic.totalRanking
      const resRanking = await reqFnTotalRanking({
        rfxId: this.$route.query.rfxId
      })
      if (resRanking.code === 200 && resRanking.data) {
        this.totalRankNum =
          resRanking.data?.totalRankNum === -99 ? '**' : resRanking.data?.totalRankNum
        this.untaxedTotalPrice =
          resRanking.data?.untaxedTotalPrice === -99 ? '**' : resRanking.data?.untaxedTotalPrice
        this.bestUntaxedTotalPrice =
          resRanking.data?.bestUntaxedTotalPrice === -99
            ? '**'
            : resRanking.data?.bestUntaxedTotalPrice
      }
    },
    rowSelected({ data }) {
      // this.grid.dataSource.forEach((x, i) => {
      //   if (JSON.stringify(x) === JSON.stringify(data)) {
      //     this.activeIndex = i;
      //   }
      // });
      this.$emit('rowSelected', data)
    },
    dataBound({ data }) {
      this.$emit('dataBound', data)
      // const grid = this?.$refs?.templateRef?.ejsRef.getCurrentViewRecords
      // if (typeof grid?.selectRows === 'function') {
      //   // 默认选中暂时隐藏 有可能导致轮询速度加快 待验证
      //   // grid?.selectRows([0]);
      // }
    },
    // 总价排名弹框
    showRankDialog() {
      this.$refs['dialog'].ejsRef.show()
      this.getSupplierRankData()
    },
    // 获取列表数据
    getSupplierRankData() {
      this.$API.comparativePrice
        .queryTotalPriceRanking({ id: this.$route?.query?.rfxId })
        .then((res) => {
          if (res.code === 200) {
            const _res = res.data
            this.$set(this.pageConfig[0].grid, 'dataSource', _res)
          }
        })
    },
    // 明细排名弹框
    rankItemDialogShow(rfxItemId) {
      this.$refs['itemDialog'].ejsRef.show()
      this.getItemSupplierRankData(rfxItemId)
    },
    // 明细获取列表数据
    getItemSupplierRankData(rfxItemId) {
      let params = {
        rfxId: this.$route?.query?.rfxId,
        rfxItemId: rfxItemId
      }
      this.$API.rfxStatistic.rank(params).then((res) => {
        if (res.code === 200) {
          const _res = res.data
          this.$set(this.itemSupplierPageConfig[0].grid, 'dataSource', _res)
        }
      })
    }
  }
}
</script>
<style>
.rankDialog .e-content {
  height: 100% !important;
}
.item-dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
.rankItem {
  cursor: pointer;
  color: #00469c;
}
</style>
<style lang="scss" scoped>
/deep/ .e-grid td.e-rowcell:first-of-type {
  padding-left: 10px !important;
}
/deep/ .e-grid .e-dialog.e-checkboxfilter {
  height: 322px;
}
/deep/.e-grid:not(.e-row-responsive) .e-gridcontent tr.e-row .e-rowcell {
  border-right: 1px solid #ccc !important;
}
.aggregate-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
  width: 100%;
  padding: 5px 20px;
  border: 1px solid #e8e8e8;
  border-bottom: none;

  span {
    color: #9a9a9a;
  }
  .amount {
    display: flex;
    color: #9a9a9a;
    span {
      color: #00469c;
    }
    div {
      &:last-of-type,
      &:nth-of-type(2) {
        margin-left: 40px;
      }
      &.rank {
        cursor: pointer;
        color: #00469c;
      }
    }
  }
}
</style>
