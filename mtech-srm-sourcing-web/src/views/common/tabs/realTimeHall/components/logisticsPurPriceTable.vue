<template>
  <div>
    <mt-data-grid ref="templateRef" @dataBound="dataBound" v-bind="grid" />
    <mt-dialog ref="dialog" :header="header" css-class="rankDialog">
      <div class="dialog-content">
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </mt-dialog>
    <mt-dialog ref="itemDialog" :header="header">
      <div class="item-dialog-content">
        <mt-template-page ref="itemTemplateRef" :template-config="itemSupplierPageConfig" />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { supplierPageConfig, itemSupplierPageConfig } from './config'
export default {
  props: {
    tabFrom: {
      type: String,
      default: ''
    },
    socketInfo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeIndex: 0,
      grid: {
        allowFiltering: false,
        columnData: [],
        dataSource: []
      },
      header: this.$t('排名'),
      pageConfig: supplierPageConfig,
      itemSupplierPageConfig: itemSupplierPageConfig
    }
  },
  watch: {
    socketInfo: {
      deep: true,
      handler(val) {
        if (
          JSON.stringify(this.grid.dataSource) !==
          JSON.stringify(JSON.parse(val)?.supplierTotalPriceStatisticList)
        ) {
          this.$set(this.grid, 'dataSource', JSON.parse(val)?.supplierTotalPriceStatisticList)
        }
      }
    },
    tabFrom: {
      immediate: true,
      handler(val) {
        let columnData = []
        if (val == 'sup') {
          columnData = [
            {
              field: 'totalRankNum',
              headerText: this.$t('排名'),
              formatter: function ({ field }, item) {
                const cellVal = item[field]
                return cellVal == -999 || cellVal == -99 ? '**' : cellVal
              }
            },
            {
              field: 'untaxedTotalPrice',
              headerText: this.$t('总价(未税)'),
              formatter: function ({ field }, item) {
                const cellVal = item[field]
                return cellVal == -999 || cellVal == -99 ? '**' : cellVal
              }
            },
            {
              field: 'taxedTotalPrice',
              headerText: this.$t('总价(含税)'),
              formatter: function ({ field }, item) {
                const cellVal = item[field]
                return cellVal == -999 || cellVal == -99 ? '**' : cellVal
              }
            }
          ]
        } else {
          columnData = [
            {
              field: 'supplierCode',
              headerText: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              headerText: this.$t('供应商名称')
            },
            {
              field: 'totalRankNum',
              headerText: this.$t('排名'),
              formatter: function ({ field }, item) {
                const cellVal = item[field]
                return cellVal == -999 || cellVal == -99 ? '**' : cellVal
              }
            },
            {
              field: 'untaxedTotalPrice',
              headerText: this.$t('总价(未税)'),
              formatter: function ({ field }, item) {
                const cellVal = item[field]
                return cellVal == -999 || cellVal == -99 ? '**' : cellVal
              }
            },
            {
              field: 'taxedTotalPrice',
              headerText: this.$t('总价(含税)'),
              formatter: function ({ field }, item) {
                const cellVal = item[field]
                return cellVal == -999 || cellVal == -99 ? '**' : cellVal
              }
            }
          ]
        }
        this.grid.columnData = [...columnData]
      }
    }
  },

  methods: {
    async initData() {
      // 整体排名
      const reqFnTotalRanking =
        this.tabFrom == 'sup'
          ? this.$API.rfxStatisticSup.priceLogisticRankInfoSup
          : this.$API.rfxStatisticSup.priceLogisticRankInfoPur
      const res = await reqFnTotalRanking({
        rfxId: this.$route.query.rfxId
      })
      if (res.code === 200) {
        this.$set(this.grid, 'dataSource', [...res.data])
      }
    },

    dataBound({ data }) {
      this.$emit('dataBound', data)
    },
    // 总价排名弹框
    showRankDialog() {
      this.$refs['dialog'].ejsRef.show()
      this.getSupplierRankData()
    },
    // 获取列表数据
    getSupplierRankData() {
      this.$API.comparativePrice
        .queryTotalPriceRanking({ id: this.$route?.query?.rfxId })
        .then((res) => {
          if (res.code === 200) {
            const _res = res.data
            this.$set(this.pageConfig[0].grid, 'dataSource', _res)
          }
        })
    },
    // 明细排名弹框
    rankItemDialogShow(rfxItemId) {
      this.$refs['itemDialog'].ejsRef.show()
      this.getItemSupplierRankData(rfxItemId)
    },
    // 明细获取列表数据
    getItemSupplierRankData(rfxItemId) {
      let params = {
        rfxId: this.$route?.query?.rfxId,
        rfxItemId: rfxItemId
      }
      this.$API.rfxStatistic.rank(params).then((res) => {
        if (res.code === 200) {
          const _res = res.data
          this.$set(this.itemSupplierPageConfig[0].grid, 'dataSource', _res)
        }
      })
    }
  }
}
</script>
<style>
.rankDialog .e-content {
  height: 100% !important;
}
.item-dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
.rankItem {
  cursor: pointer;
  color: #00469c;
}
</style>
<style lang="scss" scoped>
/deep/ .e-grid td.e-rowcell:first-of-type {
  padding-left: 10px !important;
}
/deep/ .e-grid .e-dialog.e-checkboxfilter {
  height: 322px;
}
/deep/.e-grid:not(.e-row-responsive) .e-gridcontent tr.e-row .e-rowcell {
  border-right: 1px solid #ccc !important;
}
.aggregate-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
  width: 100%;
  padding: 5px 20px;
  border: 1px solid #e8e8e8;
  border-bottom: none;

  span {
    color: #9a9a9a;
  }
  .amount {
    display: flex;
    color: #9a9a9a;
    span {
      color: #00469c;
    }
    div {
      &:last-of-type,
      &:nth-of-type(2) {
        margin-left: 40px;
      }
      &.rank {
        cursor: pointer;
        color: #00469c;
      }
    }
  }
}
</style>
