export default {
  data() {
    return {}
  },
  computed: {
    defaultColumns() {
      return [
        {
          // field: 'nodeName',
          field: 'name',
          title: this.$t('成本构成'),
          fixed: 'left',
          minWidth: 200,
          treeNode: true
        },
        {
          field: 'result',
          title: this.$t('成本'),
          minWidth: 180,
          slots: {
            default: ({ row, level }) => {
              let tip = ''
              if (level === 0) {
                tip = this.$t('合计：')
              } else if (row.itemList?.length) {
                tip = this.$t('小计：')
              }
              return [
                <div>
                  <span>
                    {tip}
                    {row.result}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'percent',
          title: this.$t('占销价百分比（%）'),
          minWidth: 180,
          slots: {
            default: ({ row, level }) => {
              let tip = ''
              if (level === 0) {
                tip = this.$t('合计：')
              } else if (row.itemList?.length) {
                tip = this.$t('小计：')
              }
              return [
                <div>
                  <span>
                    {tip}
                    {row.percent}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} clearable />]
            }
          }
        },
        {
          field: 'size',
          title: this.$t('尺寸'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input type='number' v-model={row.size} clearable />]
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
