export const leftTableData = [
  { id: 1000, name: 'test abc1', type: 'mp3', size: 1024, date: '2020-08-01' },
  {
    id: 1005,
    name: 'Test2',
    type: 'mp4',
    size: null,
    date: '2021-04-01',
    children: [
      { id: 24300, name: 'Test3', type: 'avi', size: 1024, date: '2020-03-01' },
      { id: 20045, name: 'test abc4', type: 'html', size: 600, date: '2021-04-01' },
      {
        id: 10053,
        name: 'test abc96',
        type: 'avi',
        size: null,
        date: '2021-04-01',
        children: [
          { id: 24330, name: 'test abc5', type: 'txt', size: 25, date: '2021-10-01' },
          { id: 21011, name: 'Test6', type: 'pdf', size: 512, date: '2020-01-01' },
          { id: 22200, name: 'Test7', type: 'js', size: 1024, date: '2021-06-01' }
        ]
      }
    ]
  },
  { id: 23666, name: 'Test8', type: 'xlsx', size: 2048, date: '2020-11-01' },
  { id: 24555, name: 'test abc9', type: 'avi', size: 224, date: '2020-10-01' }
]

export const rightTableData = [
  { id: 1000, name: 'test abc1', type: 'mp3', size: 1000, date: '2020-08-01' },
  {
    id: 1005,
    name: 'Test2',
    type: 'mp4',
    size: null,
    date: '2021-04-01',
    children: [
      { id: 24300, name: 'Test3', type: 'avi', size: 1500, date: '2020-03-01' },
      { id: 20045, name: 'test abc4', type: 'html', size: 800, date: '2021-04-01' },
      {
        id: 10053,
        name: 'test abc96',
        type: 'avi',
        size: null,
        date: '2021-04-01',
        children: [
          { id: 24330, name: 'test abc5', type: 'txt', size: 20, date: '2021-10-01' },
          { id: 21011, name: 'Test6', type: 'pdf', size: 500, date: '2020-01-01' },
          { id: 22200, name: 'Test7', type: 'js', size: 2000, date: '2021-06-01' }
        ]
      }
    ]
  },
  { id: 23666, name: 'Test8', type: 'xlsx', size: 2048, date: '2020-11-01' },
  { id: 24555, name: 'test abc9', type: 'avi', size: 200, date: '2020-10-01' }
]

export const formItems = [
  {
    fieldName: 'item-1',
    fieldCode: 'item1'
  },
  {
    fieldName: 'item-2',
    fieldCode: 'item2'
  },
  {
    fieldName: 'item-3',
    fieldCode: 'item3'
  },
  {
    fieldName: 'item-4',
    fieldCode: 'item4'
  }
]
