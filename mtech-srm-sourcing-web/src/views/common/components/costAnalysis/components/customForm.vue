<template>
  <mt-form ref="formRef" :model="formData">
    <mt-row :gutter="20">
      <mt-col v-for="(item, index) in formItems" :span="6" :key="index">
        <mt-form-item :prop="item.fieldCode" :label="item.fieldName" label-style="top">
          <!-- 文本、数值输入框 -->
          <vxe-input
            v-if="item.type !== 'select'"
            v-model="formData[item.fieldCode]"
            :type="item.type"
            clearable
            min="0"
            :disabled="item.disabled"
            :placeholder="$t('请输入') + item.fieldName"
            @blur="handleFormValueChange"
            @prev-number="handleNumberChange"
            @next-number="handleNumberChange"
          />
          <!-- 选择框 -->
          <div v-else class="select-dialog">
            <vxe-select
              v-model="formData[item.fieldCode]"
              :options="[]"
              clearable
              filterable
              :placeholder="$t('请选择') + item.fieldName"
            />
          </div>
        </mt-form-item>
      </mt-col>
    </mt-row>
  </mt-form>
</template>
<script>
import debounce from 'lodash.debounce'

export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    formItems: {
      type: Array,
      default: () => []
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  mixins: [],
  data() {
    return {
      formData: {}
    }
  },
  computed: {},
  watch: {
    data: {
      handler(newValue) {
        this.formData = newValue
      },
      deep: true
    }
  },
  mounted() {
    this.formData = this.data
  },
  methods: {
    handleFocus() {
      console.log('111111-docus')
    },
    // 数值输入框，在点击右侧向上/向下按钮时触发该事件
    handleNumberChange: debounce(function () {
      this.handleFormValueChange()
    }, 1000),
    handleFormValueChange() {
      this.$emit('change', this.formData)
    }
  }
}
</script>
<style lang="scss" scoped>
.select-dialog {
  display: flex;
  .select-dialog-icon {
    margin: 0 0 0 5px;
    line-height: 35px !important;
  }
}

::v-deep {
  .mt-form-item .label {
    margin: 0 10px 5px 0;
  }
  .vxe-input {
    width: 100%;
  }
}
</style>
