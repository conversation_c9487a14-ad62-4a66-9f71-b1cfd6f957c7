<template>
  <div class="full-height" :class="[!leftExpand && 'left-hidden', !rightExpand && 'right-hidden']">
    <!-- 左侧内容 -->
    <div class="letf container">
      <div class="content">
        <div class="form-info">
          <div class="title">
            <i class="vxe-icon-caret-down" />
            <span>{{ $t('报价信息') }}</span>
          </div>
          <div class="body">
            <custom-form :data="curFormData" :form-items="curFormItems" type="cur" />
            <div class="tip">**{{ $t('公式') }}</div>
            <custom-form :data="curFormData" :form-items="curFormItems" type="cur" />
          </div>
        </div>
        <div class="table-info">
          <div class="title">
            <i class="vxe-icon-caret-down" />
            <span>{{ $t('其他信息') }}</span>
          </div>
          <div class="body">
            <sc-table
              style="padding-top: 0"
              ref="leftTableRef"
              row-id="id"
              :keep-source="true"
              :tree-config="treeConfig"
              :edit-config="editConfig"
              :is-show-refresh-bth="true"
              :loading="loading"
              :columns="columns"
              :table-data="leftTableData"
              :cell-style="(e) => handleCellStyle('left', e)"
              @scroll="(e) => handleScroll('left', e)"
            />
          </div>
        </div>
      </div>
      <div v-show="rightExpand" class="operate left-operate">
        <i class="vxe-icon-arrow-left" @click="handleToggle('left')" />
      </div>
    </div>
    <!-- 右侧内容 -->
    <div class="right container">
      <div v-show="leftExpand" class="operate right-operate">
        <i class="vxe-icon-arrow-right" @click="handleToggle('right')" />
      </div>
      <div class="content">
        <div class="form-info">
          <div class="title">
            <i class="vxe-icon-caret-down" />
            <span>{{ $t('报价信息') }}</span>
          </div>
          <div class="body">
            <custom-form :data="curFormData" :form-items="curFormItems" type="cur" />
            <div class="tip">**{{ $t('公式') }}</div>
            <custom-form :data="curFormData" :form-items="curFormItems" type="cur" />
          </div>
        </div>
        <div class="table-info">
          <div class="title">
            <i class="vxe-icon-caret-down" />
            <span>{{ $t('其他信息') }}</span>
          </div>
          <div class="body">
            <sc-table
              style="padding-top: 0"
              ref="rightTableRef"
              row-id="id"
              :keep-source="true"
              :tree-config="treeConfig"
              :edit-config="editConfig"
              :is-show-refresh-bth="true"
              :loading="loading"
              :columns="columns"
              :table-data="rightTableData"
              :cell-style="(e) => handleCellStyle('right', e)"
              @scroll="(e) => handleScroll('right', e)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import CustomForm from './components/customForm.vue'
import mixin from './config/mixin'
import XEUtils from 'xe-utils'

import { leftTableData, rightTableData, formItems } from './config/mockData'

export default {
  components: { ScTable, CustomForm },
  mixins: [mixin],
  data() {
    return {
      treeConfig: {
        children: 'children',
        expandAll: true
      },
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      columns: [],
      tableData: [],
      leftTableData,
      rightTableData,
      loading: false,
      curFormItems: formItems,
      curFormData: {},
      leftExpand: true,
      rightExpand: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.columns = this.defaultColumns
    },
    // 左右两个Tabl一起滚动
    handleScroll(type, e) {
      const { isX, isY, scrollLeft, scrollTop } = e
      const prefix = type === 'left' ? 'right' : 'left'
      if (this[prefix + 'Expand']) {
        const tableRef = this.$refs[prefix + 'TableRef']?.$refs?.xGrid
        isX && tableRef?.scrollTo(scrollLeft, null)
        isY && tableRef?.scrollTo(null, scrollTop)
      }
    },
    getTableList() {},
    handleClickToolBar() {},
    // 控制左右两部分内容展开、折叠
    handleToggle(type) {
      this[type + 'Expand'] = !this[type + 'Expand']
    },
    // 单元格样式
    handleCellStyle(type, e) {
      const { row, column } = e
      const tableData = type === 'left' ? this.rightTableData : this.leftTableData
      const tempRow = XEUtils.findTree(tableData, (item) => item.id === row.id)?.item
      if (tempRow && column.field === 'size') {
        if (row[column.field] > tempRow[column.field]) {
          return { color: 'red' }
        }
        if (row[column.field] < tempRow[column.field]) {
          return { color: '#06d006' }
        }
      }
      return null
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  display: flex;
  justify-content: space-between;
}
.container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;

  .title {
    height: 35px;
    line-height: 35px;
    font-weight: bold;
    color: #fff;
    background-color: #3c435e;
    i {
      margin: auto 8px;
    }
  }
  .body {
    padding: 10px 15px;
    .tip {
      height: 32px;
      line-height: 32px;
      color: red;
      font-weight: bold;
    }
  }

  .content {
    width: calc(100% - 20px);
    border: 1px solid #e8e8e8;
    margin-bottom: 10px;
  }
  .operate {
    margin: auto;
    width: 15px;
    height: 80px;
    line-height: 80px;
    border: 1px solid #e8e8e8;
    color: #3c435e;
    font-weight: bold;
    text-align: center;
  }
  .left-operate {
    border-radius: 0 8px 8px 0;
    margin-right: 5px;
  }
  .right-operate {
    border-radius: 8px 0 0 8px;
    margin-left: 5px;
  }
}
.left-hidden {
  .container:first-child {
    width: 20px;
    overflow: hidden;
    .content {
      display: none;
    }
    .left-operate {
      transform: rotate(180deg);
      border-radius: 8px 0 0 8px;
    }
  }
  .container:last-child {
    .content {
      width: 100%;
    }
    .operate {
      display: none;
    }
  }
}
.right-hidden {
  .container:first-child {
    .content {
      width: 100%;
    }
    .operate {
      display: none;
    }
  }
  .container:last-child {
    width: 20px;
    overflow: hidden;
    .content {
      display: none;
    }
    .right-operate {
      transform: rotate(180deg);
      border-radius: 0 8px 8px 0;
    }
  }
}
::v-deep {
  .mt-form-item {
    display: flex;
    margin: 5px 0;
  }
}
</style>
