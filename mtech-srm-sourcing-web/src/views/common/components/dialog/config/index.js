import { i18n } from '@/main.js'
import comparativePrice from '@/apis/modules/purchase/comparativePrice'
import masterData from '@/apis/modules/service/masterData'

/***** 动态列（选择弹窗） ******/
// 单位
const unitColumns = [
  {
    field: 'unitCode',
    headerText: i18n.t('单位编码')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位名称')
  }
]
export const unitPageConfig = [
  {
    gridId: '9a495838-4bcc-49d4-8697-5792ee4b42df',
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: unitColumns,
      asyncConfig: {
        url: masterData.APIS.pagedQueryUnitUrl
      }
    }
  }
]
// 成本因子
const costFactorColumns = [
  {
    field: 'itemCode',
    headerText: i18n.t('成本因子编码'),
    cssClass: 'click'
  },
  {
    field: 'itemName',
    headerText: i18n.t('成本因子名称'),
    cssClass: 'click'
  },
  {
    field: 'costFactorSpecification',
    headerText: i18n.t('规格'),
    cssClass: 'click'
  },
  {
    field: 'costFactorBrand',
    headerText: i18n.t('品牌'),
    cssClass: 'click'
  },
  {
    field: 'costFactorProperty',
    headerText: i18n.t('属性大类'),
    cssClass: 'click'
  },
  {
    field: 'costFactorGroup',
    headerText: i18n.t('属性中类'),
    cssClass: 'click'
  },
  {
    field: 'baseMeasureUnitName',
    headerText: i18n.t('单位'),
    cssClass: 'click'
  },
  {
    field: 'minQuotePercent',
    headerText: i18n.t('报价下限%')
  },
  {
    field: 'maxQuotePercent',
    headerText: i18n.t('报价上限%')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  }
]
export const costFactorPageConfig = (that) => {
  const defaultRules = [
    {
      label: i18n.t('是否生效'),
      field: 'statusId',
      type: 'string',
      operator: 'equal',
      value: '1'
    }
  ]
  if (that.modalData?.companyCode) {
    defaultRules.push({
      label: i18n.t('公司'),
      field: 'companyCode',
      type: 'string',
      operator: 'equal',
      value: that.modalData.companyCode
    })
  }
  return [
    {
      gridId: 'dcb1487e-d657-4d7d-b6d8-7f299030aa23',
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        allowSelection: true,
        selectionSettings: {
          checkboxOnly: false
        },
        columnData: costFactorColumns,
        asyncConfig: {
          url: comparativePrice.APIS.costFactor,
          defaultRules,
          params: {
            onlyCurrentLevel: 0
          }
        }
      }
    }
  ]
}
// 物料、基价
const itemColumn = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  { width: '150', field: 'itemName', headerText: i18n.t('物料名称') },
  {
    width: '150',
    field: 'categoryResponse.categoryCode',
    headerText: i18n.t('品类')
  },
  {
    width: '150',
    field: 'itemDescription',
    headerText: i18n.t('规格型号')
  },
  {
    width: '150',
    field: 'oldItemCode',
    headerText: i18n.t('旧物料编号')
  },
  {
    width: '150',
    field: 'manufacturerName',
    headerText: i18n.t('制造商')
  }
]
export const itemPageConfig = [
  {
    gridId: 'c74bcb6a-927a-48b9-97c2-2e6c45d83818',
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: itemColumn,
      asyncConfig: {
        url: masterData.APIS.getItemListUrlPage
      }
    }
  }
]
// 条件基价
const conditionBasePriceColumns = [
  {
    width: '150',
    field: 'attributeCode',
    headerText: i18n.t('属性编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'attributeName',
    headerText: i18n.t('属性名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'parentAttributeCode',
    headerText: i18n.t('父类编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'parentAttributeName',
    headerText: i18n.t('父类'),
    cssClass: 'click'
  }
]
export const conditionBasePricePageConfig = [
  {
    gridId: 'bea0a90e-8497-4555-9c5b-b1497ddcefbd',
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: conditionBasePriceColumns,
      asyncConfig: {
        url: comparativePrice.APIS.costFactor,
        params: {
          onlyCurrentLevel: 1
        },
        defaultRules: [
          {
            field: 'attributeTypeCode',
            type: 'number',
            operator: 'equal',
            value: '1'
          },
          {
            label: i18n.t('是否生效'),
            field: 'statusId',
            type: 'string',
            operator: 'equal',
            value: '1'
          }
        ]
      }
    }
  }
]
// 定额、物料顶额、成本因子顶额
const quotaColumns = [
  {
    width: '150',
    field: 'attributeCode',
    headerText: i18n.t('属性编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'attributeName',
    headerText: i18n.t('属性名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'attributeTypeCode',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('实际定额'), 1: i18n.t('条件基价') }
    },
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'parentAttributeName',
    headerText: i18n.t('父类'),
    cssClass: 'click'
  }
]
export const quotePageConfig = [
  {
    gridId: '5248eff9-c7a3-4401-9a8f-4bdf4586eed7',
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: quotaColumns,
      asyncConfig: {
        url: comparativePrice.APIS.costFactor,
        defaultRules: [
          {
            label: i18n.t('是否生效'),
            field: 'statusId',
            type: 'string',
            operator: 'equal',
            value: '1'
          }
        ],
        params: {
          onlyCurrentLevel: 1
        }
      }
    }
  }
]
export const multiCostFactorPageConfig = (that) => {
  const defaultRules = [
    {
      label: i18n.t('是否生效'),
      field: 'statusId',
      type: 'string',
      operator: 'equal',
      value: '1'
    },
    {
      label: i18n.t('公司'),
      field: 'relationCode',
      type: 'string',
      operator: 'equal',
      value: that.modalData.companyCode
    }
  ]
  let columnData = [
    {
      width: '60',
      type: 'checkbox'
    },
    ...costFactorColumns
  ]
  return [
    {
      gridId: 'dcb1487e-d657-4d7d-b6d8-7f299030aa23',
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        allowSelection: true,
        columnData,
        asyncConfig: {
          url: comparativePrice.APIS.costFactor,
          defaultRules,
          params: {
            onlyCurrentLevel: 0
          }
        }
      }
    }
  ]
}
