<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- 自定义查询条件 -->
      <collapse-search
        class="toggle-container"
        :default-expand="false"
        @reset="handleReset"
        @search="handleSearch"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="costFactorCode" :label="$t('成本因子编码')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorCode"
              :show-clear-button="true"
              :placeholder="$t('请输入成本因子编码')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorName" :label="$t('成本因子名称')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorName"
              :show-clear-button="true"
              :placeholder="$t('请输入成本因子名称')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorSpec" :label="$t('规格')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorSpec"
              :show-clear-button="true"
              :placeholder="$t('请输入规格')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorBrand" :label="$t('品牌')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorBrand"
              :show-clear-button="true"
              :placeholder="$t('请输入品牌')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorAttr" :label="$t('属性大类')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorAttr"
              :show-clear-button="true"
              :placeholder="$t('请输入属性大类')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorGroup" :label="$t('属性中类')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorGroup"
              :show-clear-button="true"
              :placeholder="$t('请输入属性中类')"
            />
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <!-- 表格 -->
      <sc-table
        ref="sctableRef"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        @refresh="handleSearch"
        @cell-dblclick="handleDbclick"
      />
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { formatTime } from '@/utils/utils'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  components: { ScTable, CollapseSearch },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      searchFormModel: {},
      loading: false,
      tableData: [],
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalRecordsCount: 0,
        totalPages: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    columns() {
      const defaultColumns = [
        {
          field: 'costFactorCode',
          title: this.$t('成本因子编码'),
          minWidth: 160
        },
        {
          field: 'costFactorName',
          title: this.$t('成本因子名称'),
          minWidth: 160
        },
        {
          field: 'costFactorSpec',
          title: this.$t('规格'),
          minWidth: 160
        },
        {
          field: 'costFactorBrand',
          title: this.$t('品牌')
        },
        {
          field: 'basicMeasureUnitName',
          title: this.$t('单位')
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('单价')
        },
        {
          field: 'costFactorAttr',
          title: this.$t('属性大类')
        },
        {
          field: 'costFactorGroup',
          title: this.$t('属性中类')
        },
        {
          field: 'priceUnitName',
          title: this.$t('价格单位')
        },
        {
          field: 'priceValidStartDate',
          title: this.$t('价格有效期从'),
          slots: {
            default: ({ row }) => {
              const startDate = formatTime(new Date(row.priceValidStartDate), 'YYYY-mm-dd')
              return [<span>{startDate}</span>]
            }
          }
        },
        {
          field: 'priceValidEndDate',
          title: this.$t('价格有效期至'),
          slots: {
            default: ({ row }) => {
              const endDate = formatTime(new Date(row.priceValidEndDate), 'YYYY-mm-dd')
              return [<span>{endDate}</span>]
            }
          }
        }
      ]
      return defaultColumns
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.handleSearch()
  },
  methods: {
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        rfxId: this.modalData.rfxId,
        nodeCode: this.modalData.nodeCode,
        priceClassify: 1, // 1：内部价格，2：外部价格
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.costFactorInternalPricing['getCfPriceList' + this.modalData.type](
        params
      ).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    },
    // 双击行
    handleDbclick(e) {
      this.$emit('confirm-function', e.row || {})
    },
    confirm() {
      const currow = this.$refs.sctableRef.$refs.xGrid.getCurrentRecord()
      this.$emit('confirm-function', currow)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 5px 0 0 0;
  height: 100%;
  width: 100%;
  .collapse-search-area {
    padding: 0;
  }
  .mt-pagertemplate {
    margin: 10px 0 0 0;
  }
}
</style>
