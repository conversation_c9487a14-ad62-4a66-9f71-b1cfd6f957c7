<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="table" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import {
  unitPageConfig,
  costFactorPageConfig,
  itemPageConfig,
  conditionBasePricePageConfig,
  quotePageConfig,
  multiCostFactorPageConfig
} from './config'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    pageConfig() {
      let _tempConfig = []
      switch (this.modalData.valueSet) {
        case 'unit':
          _tempConfig = unitPageConfig
          break
        case 'item':
        case 'base_price':
          _tempConfig = itemPageConfig
          break
        case 'cost_factor':
          _tempConfig = costFactorPageConfig(this)
          break
        case 'conditions_base_price':
          _tempConfig = conditionBasePricePageConfig
          break
        case 'quota_no':
        case 'item_quota_no':
        case 'cost_factor_quota_no':
          _tempConfig = quotePageConfig
          break
        case 'multi_cost_factor':
          _tempConfig = multiCostFactorPageConfig(this)
          break
        default:
          break
      }
      if (this.modalData.valueSet !== 'multi_cost_factor') {
        // multi_cost_factor 多选
        _tempConfig[0].grid.recordDoubleClick = this.recordDoubleClick
      }

      return _tempConfig
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    recordDoubleClick(e) {
      this.$emit('confirm-function', e.rowData)
    },
    confirm() {
      const _selectRecords = this.$refs.table.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      if (!_selectRecords.length) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (this.modalData.valueSet === 'multi_cost_factor') {
        // multi_cost_factor 多选
        this.$emit('confirm-function', _selectRecords)
        return
      }
      this.$emit('confirm-function', _selectRecords[0])
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
