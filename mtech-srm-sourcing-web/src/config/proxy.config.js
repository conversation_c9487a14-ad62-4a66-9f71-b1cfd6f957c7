/*
  李长江: 10.14.242.177:9114
  何雪峰: 10.14.242.166:9114
  冯春杰: 10.14.243.139:8014
  何杰:10.14.242.192:9114
*/
// const LOCAL_PROXY = {
//   SERVICE_SOURCEING: "http://10.14.242.177:9114",
//   SERVICE_FLOW: "http://10.242.98.119:8017",
//   SERVICE_MASTER_DATA: "http://23mk369v.dnat.tech", // 冯春杰
//   SERVICE_RULE: " http://10.14.242.192:9114",
// };
// //本地联调时，使用的proxyConfig
// const PROXY_CONFIG = {
//   "/api/workFlow": {
//     target: LOCAL_PROXY.SERVICE_FLOW,
//     changeOrigin: true,
//     pathRewrite: {
//       "^/api/workFlow": "",
//     },
//   },
//   "/api/masterDataManagement": {
//     target: LOCAL_PROXY.SERVICE_MASTER_DATA,
//     changeOrigin: true,
//     pathRewrite: {
//       "^/api/masterDataManagement": "",
//     },
//   },
//   "/api/sourcing": {
//     target: LOCAL_PROXY.SERVICE_SOURCEING,
//     changeOrigin: true,
//     pathRewrite: {
//       "^/api/sourcing": "",
//     },
//   },
//   "/api/ruleConfig": {
//     target: LOCAL_PROXY.SERVICE_RULE,
//     changeOrigin: true,
//     pathRewrite: {
//       "^/api/ruleConfig": "",
//     },
//   },
// };
// 本地联调的时候，在浏览只访问子应用
const PROXY_CONFIG = {
  // "/api/sourcing": {
  //   target: "http://10.14.241.13:9114",
  //   changeOrigin: true,
  //   pathRewrite: {
  //     "^/api/sourcing": "",
  //   },
  // },
  '/api': {
    // https://cli.vuejs.org/zh/guide/mode-and-env.html
    target: process.env.VUE_PROXY_TARGET || 'http://srm-uat-gw.eads.tcl.com',
    changeOrigin: true,
    pathRewrite: {
      // "^/api/": "",
    }
  }
}

const PROXY_FILE = '/file'
const PROXY_WORK_FLOW = '/flow'
const PROXY_FORM_DESIGN = '/lowcodeWeb'
const PROXY_MASTER_DATA = '/masterDataManagement'
const PROXY_SOURCING = '/sourcing'
const PROXY_PRICEING = '/price'
const PROXY_IAM = '/iam'
const PROXY_ANALYSIS = '/analysis'
const PROXY_BASE = {
  PROXY_FILE,
  PROXY_WORK_FLOW,
  PROXY_FORM_DESIGN,
  PROXY_MASTER_DATA,
  PROXY_SOURCING,
  PROXY_PRICEING,
  PROXY_IAM,
  PROXY_ANALYSIS
}

module.exports = {
  PROXY_CONFIG,
  PROXY_BASE,
  PROXY_FILE,
  PROXY_WORK_FLOW,
  PROXY_FORM_DESIGN,
  PROXY_MASTER_DATA,
  PROXY_SOURCING,
  PROXY_PRICEING,
  PROXY_IAM,
  PROXY_ANALYSIS
}
