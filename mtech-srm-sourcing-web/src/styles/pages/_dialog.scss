// label样式
.e-dialog {
  height: 600px;
  .source-label {
    color: #2F353C;
    font-size: 14px;
    font-weight: 500;
    &.label-left {
      margin-right: 10px;
    }
  }
  &.small-dialog {
    width: 500px!important;
    height: 300px!important;
    .e-dlg-header-content {
      // background: #fff;
      .e-icon-btn .e-btn-icon {
        color: #9DAABF;
      }
      .e-dlg-header {
        // color: #292929;
        display: flex;
        align-items: center;
        i {
          color: #EDA133;
        }
        .header-text {
          margin-left: 10px;
          color: #292929;
        }
      }
    }
  }
  &.ratio-dialog {
    width: 500px!important;
    height: 500px!important;
    .e-dlg-header-content {
      // background: #fff;
      .e-icon-btn .e-btn-icon {
        color: #9DAABF;
      }
      .e-dlg-header {
        // color: #292929;
        display: flex;
        align-items: center;
        i {
          color: #EDA133;
        }
        .header-text {
          margin-left: 10px;
          color: #292929;
        }
      }
    }
  }
}
// 寻源需求 - 需求汇总管理 - 需求汇总明细 - 历史价格记录弹窗
.history-price-dialog .e-dlg-content {
  .pin-box {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 20px;
  }
}
// // 寻源需求 - 我的需求管理 - 我的列表主单 - 创建寻源项目
// .create-proj-dialog .e-dlg-content {
//  padding-top: 40px !important;
//  padding:40px;
//  .mt-form {
//    display: flex;
//    flex-wrap: wrap;
//    justify-content: space-between;
//    .mt-form-item {
//      width: 390px;
//      .mt-form-item__content {
//        width: 100%;
//      }
//    }
//  }
// }
// 寻源需求 - 我的需求管理 - 我的列表主单 - 创建寻源项目
.create-proj-dialog {
  .e-dlg-content {
     padding: 40px !important;
     .mt-form {
       display: flex;
       flex-wrap: wrap;
       justify-content: space-between;
       .mt-form-item {
         width: 390px;
         .mt-form-item__content {
           width: 100%;
         }
       }
       &.assistantForm {
        .mt-form-item {
          width: 290px;
       }
      }
     }
   }

   .e-footer-content {
     line-height: 40px;

     .e-btn {
       vertical-align: initial;
       margin-left: 0px !important;
       margin-right: 36px;

       &:last-child {
         margin-right: 44px;
       }
     }
   }
 }
// 招标大厅 - 候选供方 - 资质审查
.candidate-supplier-qualification.create-proj-dialog {
  height: 900px !important;
}

// dialog的header与footer -- 2023.06.20 - lbj
.e-dialog .e-dlg-header-content {
  padding: 8px 16px !important;
}
.e-dialog .e-dlg-content {
  padding-bottom: 0 !important;
}
