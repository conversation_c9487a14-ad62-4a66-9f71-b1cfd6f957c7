.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}

.backgroundRed {
  background-color: rgb(245, 245, 245);
}

.full-height {
  height: 100%;
}

.mt-pagertemplate.custom-page {
  height: auto;
  margin: 0;
  padding: 10px 0;
  box-sizing: border-box;
  background: var(--plugin-dg-bg-ff);
  border-top: 1px solid var(--plugin-dg-border-color);
}

//active状态，左侧线条
.list-item-active {
  border-left: none;
  position: relative;
  &:before {
    content: '';
    height: 100%;
    width: 0;
    position: absolute;
    border-left: 2px solid #00469c;
    left: 0;
    top: 0;
    animation: list-item-active-animation 0.2s ease;
  }
  @keyframes list-item-active-animation {
    0% {
      top: 50%;
      height: 0;
    }
    100% {
      top: 0;
      height: 100%;
    }
  }
}

//左上角角标
.top-left-arrow-tag {
  padding-left: 5px;
  position: relative;
  &:before {
    content: '';
    height: 0;
    width: 0;
    border-right: 6px solid transparent;
    border-top: 6px solid #eda133;
    position: absolute;
    left: 0;
    top: 0;
  }
}

.svg-option-item {
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  position: relative;
  color: #4f5b6d;
  .mt-icons {
    margin-right: 6px;
  }
  span {
    word-break: keep-all;
    font-size: 14px;
  }
}

//侧拉框-分组规则、分配策略
.slider-panel-container {
  background: rgba(0, 0, 0, 0.2);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1001;

  .slider-modal {
    width: 800px;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    background: #fafafa;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 0 0 8px;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);

    // &:before {
    //   content: "";
    //   cursor: pointer;
    //   width: 12px;
    //   height: 60px;
    //   background: #f3f3f3;
    //   border-radius: 8px 0 0 8px;
    //   position: absolute;
    //   left: -12px;
    //   top: calc(50% - 30px);
    // }

    // &:after {
    //   content: "";
    //   width: 0;
    //   height: 0;
    //   cursor: pointer;
    //   border-width: 4px 0px 4px 4px;
    //   border-style: solid;
    //   border-color: transparent transparent transparent #6c7a8f;
    //   position: absolute;
    //   left: -8px;
    //   top: calc(50% - 4px);
    // }

    .slider-header {
      height: 60px;
      background: #f3f3f3;
      padding: 10px 20px 10px 30px;
      justify-content: space-between;
      align-items: center;

      .slider-title {
        font-size: 16px;
        font-weight: 500;
        color: #292929;
      }

      .slider-close {
        cursor: pointer;
        font-size: 30px;
        color: #4d5b6f;
        transform: rotate(45deg);
      }
    }

    .slider-content {
      flex: 1;
      padding: 20px;
      .rule-group {
        margin-top: 30px;

        &:first-of-type {
          margin-top: 0;
        }

        .group-header {
          font-size: 14px;
          color: #292929;
          display: inline-block;
          padding-left: 10px;
          position: relative;

          &:before {
            content: '';
            position: absolute;
            width: 3px;
            height: 12px;
            background: #eda133;
            border-radius: 2px 0 0 2px;
            left: 0;
            top: 1px;
          }
        }

        .group-description {
          padding-left: 10px;
          font-size: 12px;
          display: block;
          color: #9a9a9a;
          margin-top: 6px;
        }

        .group-content {
          padding: 20px 10px 0 10px;
          display: flex;
          .mt-form {
            flex: 1;
            display: flex;
            .mt-form-item {
              flex: 1;
              margin: 0 10px;
              &:last-of-type {
                margin-right: 0;
              }
              &:first-of-type {
                margin-left: 0;
              }
            }
          }
        }
      }
    }

    .slider-footer {
      height: 60px;
      background: #ffffff;
      border-radius: 0 0 0 8px;
      box-shadow: inset 0 1px 0 0 #e8e8e8;
      align-items: center;
      flex-direction: row-reverse;

      span {
        cursor: pointer;
        margin: 0 30px;
        font-size: 14px;
        color: #0043a8;
        font-weight: 500;
        padding: 5px 10px;
        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

.page-grid-container {
  overflow-y: auto !important;
  .grid-container {
    flex: 1;
    height: auto;
    .mt-data-grid {
      display: flex;
      flex-direction: column;
      position: relative;
      .e-grid {
        flex: 1;
      }
    }
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 采购执行 - 带行内编辑的表格的样式
.pe-edit-grid {
  .e-grid {
    // 去掉第一个单元格选中时的左边框样式
    tr td:first-child::before {
      display: none;
    }
    // 去掉 单元格的选中背景色
    td.e-active {
      background-color: transparent !important;
    }
    // 去掉行上 悬浮时的单元格背景色
    tr:hover td {
      background-color: transparent !important;
    }
    // 禁用的单元格 样式,,优先级第二高，更新了的还是会变成蓝色
    tr td {
      &.e-rowcell.bg-grey,
      &.e-rowcell.bg-grey.e-updatedtd {
        background-color: #f5f5f5 !important;
        color: #9a9a9a !important;
        cursor: not-allowed;
        &.e-gridchkbox {
          background-color: transparent !important;
          cursor: pointer;
        }
      }
    }
    // 去掉冻结列 悬浮时，改变背景色问题
    &.e-gridhover .e-frozenhover,
    .e-detailcell,
    .e-detailindentcell,
    .e-detailrowcollapse,
    .e-detailrowexpand,
    .e-groupcaption,
    .e-indentcell,
    .e-recordpluscollapse,
    .e-recordplusexpand,
    .e-rowcell {
      background-color: transparent;
    }
    // 待保存的单元格，背景色改变,,优先级最高
    tr td.e-rowcell.e-updatedtd {
      background-color: rgba(0, 70, 156, 0.1) !important;
    }
    // 去掉冻结列 右侧的蓝色边框
    .e-frozenheader .e-table,
    .e-frozencontent .e-table {
      border-right: 0;
    }

    // 编辑时禁用的单元格样式
    .e-editedrow,
    .e-addedrow {
      .e-rowcell .e-control-wrapper {
        &.e-disabled,
        &.cell-disabled,
        .e-input[readonly]:not(.e-dropdownlist) {
          background: #f5f5f5 !important;
          cursor: not-allowed;
        }
      }
    }
  }
}
// data-grid toolbar 按钮样式，使其与 mt-template-page toolbar 按钮一致
.custom-toolbar-grid {
  .e-toolbar .e-tbar-btn:hover,
  .e-toolbar .e-tbar-btn:focus {
    background: transparent !important;
    box-shadow: none !important;
  }

  .e-tbar-btn:hover span.e-tbar-btn-text,
  .e-tbar-btn:hover .mt-icons {
    color: #707b8b !important;
  }

  .e-btn-icon.mt-icons {
    font-family: element-icons, e-icons;
    vertical-align: baseline;
    color: #4f5b6d;
  }

  .e-tbar-btn span.e-tbar-btn-text {
    vertical-align: baseline;
  }
}

/deep/li.tab-item2.tab-item2--rectangle.active {
  background: #f1f4f9 !important;
  color: #00469c !important;
  font-weight: bold !important;
  border-radius: 5px !important;
}
/deep/li.tab-item2.tab-item2--rectangle {
  background: #fff !important;
  color: #9a9a9a !important;
  font-weight: bold !important;
  border-radius: 5px !important;
  border-bottom: 1px solid #e8e8e8 !important;
  border: 1px solid #e8e8e8 !important;
}
.mt-input-number input[type='number'] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  padding-right: 30px !important;
}

// 自定义样式
.custom {
  &--color::before {
    content: '●';
    margin-right: 6px;
  }
  &-ED5633 {
    color: #ed5633;
  }
  &-6386C1 {
    color: #6386c1;
  }
  &-EDA133 {
    color: #eda133;
  }
  &-9A9A9A {
    color: #9a9a9a;
  }
}
.e-detailcell {
  .hierarchy-template {
    .toolbar-container {
      height: 0px !important;
    }
    .e-grid.e-horizontallines .e-rowcell {
      border-bottom-width: 1px;
    }
  }
}

/**--------------增加form-item的label文字解释-----lbj-2023.06.27-----------*/
.label-badge-content {
  display: block;
  position: absolute;
  left: 70px;
  top: 0px;
  height: 16px;
  line-height: 16px;
  width: 16px;
  text-align: center;
  background: rgba(0, 0, 0, 0.87);
  color: #fff;
  border-radius: 50%;
  &:after {
    content: attr(badge-msg);
    display: none;
    position: absolute;
    z-index: 9999;
    width: 300px;
    left: -90px;
    top: 18px;
    padding: 6px 8px;
    background: rgba(0, 0, 0, 0.7);
    color: #eee;
    border-radius: 4px;
    text-align: left;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
  }
}
.label-badge-content:hover::after {
  display: block;
}
/**--------------增加form-item的label文字解释-----lbj-2023.06.27-----------*/

/**--------------aggrid 表格相关样式 :start-----------*/
.flex-full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.ag-theme-alpine .ag-row-odd {
  background: #ffffff;
}
.ag-theme-alpine .ag-pinned-left-header {
  border-right-color: #dde2eb !important;
}
.ag-theme-alpine .ag-pinned-right-header,
.ag-theme-alpine
  .ag-cell.ag-cell-first-right-pinned:not(.ag-cell-range-left):not(.ag-cell-range-single-cell) {
  border-left-color: #dde2eb !important;
}
.cell-span {
  background-color: #fefefe !important;
  display: flex;
  align-items: center;
  border-right: 1px solid #dde2eb !important;
  border-bottom: 1px solid #dde2eb !important;
  z-index: 999 !important;
}
.mergeCell,
.mergeCellDisable {
  background-color: #fefefe !important;
  display: flex;
  align-items: center;
  border-right: 1px solid #dde2eb !important;
  border-bottom: 1px solid #dde2eb !important;
  z-index: 999 !important;
}
.mergeCellDisable,
.singleCellDisable {
  background: #f2f2f2 !important;
}
.cellEditable {
  background: rgba(77, 168, 243, 0.5) !important;
}

.ag-cell {
  border-right: 1px solid #dde2eb !important;
}
.ag-theme-alpine .ag-cell-inline-editing {
  height: 30px;
}
.ag-theme-alpine .ag-ltr .ag-cell-focus {
  border-right: 1px solid #2196f3 !important;
  border-bottom: 1px solid #2196f3 !important;
}
.ag-cell .mt-input-number .step-box {
  height: 36px !important;
}
.ag-theme-alpine.ag-popup {
  position: fixed;
  width: 100%;
}
.operate-btns .operate-btn{
  font-size: 14px;
  font-weight: 400;
  color: #3979F9;
  cursor: pointer;
  margin-right:16px;
  &:last-child{
    margin-right:0;
  }
  &:hover{
    color: #6194FA;
  }
  &:focus{
    color: #1154DC;
  }
  &.is-disabled{
    color:#D0DFFE ;
    cursor:not-allowed;
  }
}
/**--------------aggrid 表格相关样式 :end-----------*/
.template_checkbox {
  text-align: left;
  input[type='checkbox'] {
    visibility: visible;
    width: 16px;
    height: 16px;
  }
}

/**--------------tempalte-page 表格相关样式 :start-----------*/
.custom-bg-yellow {
  font-size: 12px;
  padding: 4px;
  border-radius: 2px;
  color: #a7a700;
  background: rgba(255, 255, 0, 0.5);
}
// 此段为npm包改写（后续待删除）

.toolbar-container {
  .toolbar-item {
    background: none !important;
    padding: 0 !important;
    box-shadow: 1px 1px 4px 0 #aaa;
    .mt-icons {
      display: none;
    }
    > span:not(.e-ddl-icon) {
      height: 28px;
      line-height: 26px;
      padding: 0 16px;
      background: #fff;
      border: 1px solid #2783fe;
      color: #4a556b !important;
      border-radius: 4px;
    }
    > span[type='primary'] {
      color: #ffffff !important;
    }
    // 此处样式结构有差异
    .mt-icon-icon_solid_Configuration + span:nth-child(2) {
      padding-right: 2px;
      border-radius: 0;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      border: 1px solid #2783fe;
      border-right: none;
    }
    span.e-ddl-icon {
      height: 28px;
      padding-right: 16px;
      background: #ffffff !important;
      border-radius: 0;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border: 1px solid #2783fe;
      border-left: none;
      &::before {
        top: 6px !important;
        color: #000000;
      }
      &.up-arrow::before {
        top: -7px !important;
        color: #000000;
      }
    }
    &.icon-disabled > span {
      border: 1px solid #959aaa !important;
      color: #bfbfbb !important;
      cursor: disabled;
    }
  }
  & > div:nth-child(2) {
    .toolbar-item {
      > span:not(.e-ddl-icon) {
        border: 1px solid #4a556b;
        color: #4a556b;
      }
      .mt-icon-icon_solid_Settingup + span {
        background: #4a556b;
        border: 1px solid #4a556b;
        color: #fff !important;
      }
    }
  }
}
.ext-toolbar-container .toolbar-item {
  box-shadow: none;
}

/**--------------tempalte-page 表格相关样式 :end  -----------*/
