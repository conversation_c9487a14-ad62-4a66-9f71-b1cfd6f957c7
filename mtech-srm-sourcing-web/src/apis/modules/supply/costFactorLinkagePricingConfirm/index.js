/********** 成本因子联动定价 ***********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'costFactorLinkagePricingConfirm'
const APIS = {
  // 查询列表-查询
  queryCflpcList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/pageQuery`, data)
  },
  // 头部-查询
  queryCflpcHeaderInfo: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/queryHeader`, data)
  },
  // 头部-确认联动定价
  confirmCflpc: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/commit`, data)
  },
  // 头部-驳回联动定价
  rejectCflpc: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/reject`, data)
  },
  // 头部-导出
  exportCflpc: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/export`, data, {
      responseType: 'blob'
    })
  },

  // 成本因子tab-查询
  queryCflpcCfDetailList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/rfxFactorItemPageQuery`,
      data
    )
  },
  // 成本因子tab-导出
  exportCflpcCfDetailList: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/exportRfxFactorItem`,
      data,
      {
        responseType: 'blob'
      }
    ),

  // 物料明细tab-查询
  queryCflpcMdDetailList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/rfxMaterialItemPageQuery`,
      data
    )
  },
  // 物料明细tab-导出
  exportCflpcMdDetailList: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/exportRfxMaterialItem`,
      data,
      {
        responseType: 'blob'
      }
    ),
  // 物料明细tab-成本分析表头
  queryCflpcCostAnalysisColumns: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/queryColumn`, data),
  // 物料明细tab-成本分析数据
  queryCflpcCostAnalysisData: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/costModelPrice`, data),
  // 物料明细tab-成本分析导出
  exportCflpcCostAnalysis: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/exportCostModelPrice`,
      data,
      {
        responseType: 'blob'
      }
    ),

  // 附件tab--根据rfxId查询文件节点信息
  getFileNodeList: (data = {}) =>
    API.get(
      `${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/queryFileNodeByRfxId`,
      data
    ),
  // 附件tab--根据rfxId查询文件列表
  getFileList: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/queryFileByRfxId`, data),
  // 附件tab--保存附件
  saveFile: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/rfx/supplier/factorMaterialLinkage/saveFile`, data)
}

export default {
  NAME,
  APIS
}
