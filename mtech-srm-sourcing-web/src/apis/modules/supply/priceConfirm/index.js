import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'priceConfirm'
const APIS = {
  /**
   * 上传
   */
  uploadFile: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/share/uploadFile`, data, {
      responseType: 'blob'
    }),

  viewDetailById: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/supplier/viewDetailById`, data),

  //供方-驳回-定价单
  reject: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/whitePoint/supplier/reject`, data),
  //供方-确认-定价单
  accept: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/whitePoint/supplier/accept`, data),
  //供方-保存-定价物料行的回复信息
  saveResponse: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/supplier/saveResponse`, data),
  //供方附件-保存文件信息
  saveFile: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/whitePoint/supplier/saveFile`, data),
  //供方附件-删除文件信息
  deleteFile: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/whitePoint/share/deleteFile`, data),

  //供方-导出-物料行(根据pointId)
  exportItemSup: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/supplier/exportItemSup`, data, {
      responseType: 'blob'
    }),
  //供方-查询-定价单分页列表
  queryHeaderByPage: `${PROXY_SOURCING}/tenant/whitePoint/supplier/queryHeaderByPage?isConfirm=0`,
  //供方-查询-定价单分页已确认列表
  queryHeaderByPageIsConfirm: `${PROXY_SOURCING}/tenant/whitePoint/supplier/queryHeaderByPage?isConfirm=1`,
  //供方-查询-定价物料行页列表
  queryItemByPage: `${PROXY_SOURCING}/tenant/whitePoint/supplier/queryItemByPage`
}
export default {
  NAME,
  APIS
}
