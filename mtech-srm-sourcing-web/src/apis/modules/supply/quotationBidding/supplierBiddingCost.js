import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
import qs from 'qs'
const NAME = 'supplierBiddingCost'

const APIS = {
  //供方-递归查询成本模型明细
  saveItemData: (params = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/supplier/biddingCostModel/saveItemData`, params),
  //供方-递归查询成本模型明细
  queryLeafNodeData: (params = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/supplier/biddingCostModel/queryLeafNodeData`, params),
  //供方-成本模型明细-保存末级节点未启用明细数据价格
  saveLeafNodePrice: (params = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/supplier/biddingCostModel/saveLeafNodePrice`, params),
  //供方-成本模型明细-导出
  exportSupList: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/biddingCostModel/export`, qs.stringify(params), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    }),
  //采方-成本模型明细-导出
  exportPurList: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBiddingItem/export`, qs.stringify(params), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    }),

  // 供方- 查询成本模型表头
  queryColumn: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/queryColumn`, params),
  // 供方- 查询报价明细成本模型
  queryBidItemCostModel: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/queryBidItemCostModel`, params),
  // 供方-保存供方成本模型分析
  saveCostModelPrice: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/saveCostModelPrice`, params),
  // 供方- 获取本单成本分析报价
  queryCostModelPriceList: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/rfxCostModelPriceList`, params),
  // 供方- 获取历史成本分析报价
  queryHistoryCostModelPriceList: (params = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/historyCostModelPriceList`,
      params
    ),
  // 供方- 成本模型分析
  costEstimate: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/estimate`, params),
  // 供方- 导入成本模型分析
  importCostModel: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/importItem?id=${data.id}`,
      data.data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-导出成本模型分析
  exportCostModel: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/exportItem`, data, {
      responseType: 'blob'
    }),
  // 供方-导出成本模型对比
  exportCostModelContrast: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/exportCostPriceItemContrast`,
      data,
      { responseType: 'blob' }
    ),

  // 供方-单位列表
  queryUnitList: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/unitPagedQuery`, params)
}

export default {
  NAME,
  APIS
}
