import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'supQBFiles'

const APIS = {
  /*
    寻源文件接口
  */
  //根据rfxId查询所有文件节点信息
  querySupFileNodeByRfxId: (params = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/supplier/bid/queryFileNodeByRfxId`, params),

  //根据rfxId查询所有文件信息
  querySupFileByRfxId: `${PROXY_SOURCING}/tenant/supplier/bid/queryFileByRfxId`,
  querySupFileByRfxIdFunc: (data = {}) => {
    API.get(`${PROXY_SOURCING}/tenant/supplier/bid/queryFileByRfxId`, data)
  }
}

export default {
  NAME,
  APIS
}
