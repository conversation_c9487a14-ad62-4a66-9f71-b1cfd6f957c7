import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'quotationBidding'
const APIS = {
  // 寻源项目 - 报价投标管理 - 获取列表 - 列表
  getOfferBidList: `${PROXY_SOURCING}/tenant/supplier/tender/list`,
  // 寻源项目 - 报价投标管理 - 获取列表 - 参与报价
  tenderJoin: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/tender/join`, data),
  // 寻源项目 - 报价投标管理 - 获取列表 - 详情
  getOfferBidDetails: (data = '') =>
    API.get(`${PROXY_SOURCING}/tenant/supplier/tender/detail?tenderId=${data}`),
  // 获取tabs
  getTabs: (data = '') =>
    API.get(`${PROXY_SOURCING}/tenant/supplier/rfx/module/config?tenderId=${data}`),
  // 寻源项目 - 报价投标管理 - 获取列表 - 详情 -报价
  // 轮次物料
  getBidItems: (roundId = '', biddingId = '') =>
    API.get(
      `${PROXY_SOURCING}/tenant/supplier/bid/items?roundId=${roundId}&biddingId=${biddingId}`
    ),
  // 寻源项目 - 报价投标管理 - 获取列表 - 详情 -相关文件
  getTenderFiles: (data) => API.post(`${PROXY_SOURCING}/tenant/supplier/tender/files`, data),
  // 寻源项目 - 报价投标管理 - 获取列表 - 详情 -报价
  // 轮次报价详情
  getLatestDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/price/latest-detail`, data),
  // 寻源项目 - 报价投标管理 - 获取列表 - 详情 -报价
  // 保存提交报价信息
  bidPriceSave: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/bid/price/save`, data),
  // 实时排名
  getRFXPriceRank: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/sup/price/rank`, data),
  // 实时竞价
  getRFXPriceInfo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/sup/price/info`, data),

  //保存文件信息
  saveSupplierHeaderFile: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/supplier/bid/saveFile`, data)
}

export default {
  NAME,
  APIS
}
