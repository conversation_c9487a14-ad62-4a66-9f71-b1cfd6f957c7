import { API } from '@mtech-common/http'
const NAME = 'assessFeedback'
const PROXY_BASE = '/analysis/tenant'
const APIS = {
  /*
    考核单接口
  */
  // 保存并提交考核单反馈数据
  saveAndSubmit: (data) => {
    return API.post(`${PROXY_BASE}/supplierClaim/saveAndSubmitClaimAppeal`, data)
  },
  // 保存考核单反馈数据
  saveClaimAppeal: (data) => {
    return API.post(`${PROXY_BASE}/supplierClaim/saveClaimAppeal`, data)
  },
  // 考核单详情（待反馈）
  detailClaim: (data) => {
    return API.post(`${PROXY_BASE}/supplierClaim/detailClaim`, data)
  },
  // 打印考核单
  printClaim: (data) => {
    return API.post(`${PROXY_BASE}/supplierClaim/printClaim`, data)
  }
}

export default {
  NAME,
  APIS
}
