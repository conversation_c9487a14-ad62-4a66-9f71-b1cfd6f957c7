import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'supplyQdetail'
const APIS = {
  //详情数据
  tenderDetail: (rfxId = '', biddingMode = '') =>
    API.get(
      `${PROXY_SOURCING}/tenant/supplier/tender/detail?rfxId=${rfxId}&biddingMode=${biddingMode}`
    ),

  // 参与报价
  tenderJoin: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/tender/join`, data),

  // 采购明细
  tenderItemsUrl: `${PROXY_SOURCING}/tenant/supplier/tender/items`,
  // 采购明细
  tenderItems: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/tender/items`, data),
  // listQueryFile: `${PROXY_SOURCING}/tenant/supplier/tender/list`,

  // 相关文件
  tenderFiles: `${PROXY_SOURCING}/tenant/supplier/tender/files`,

  // 获取tabs
  getTabs: (rfxId = '', biddingMode = '') =>
    API.get(
      `${PROXY_SOURCING}/tenant/supplier/rfx/module/config?rfxId=${rfxId}&biddingMode=${biddingMode}`
    ),

  // 根据询价单id 获取倒计时信息
  getRfxCountdown: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/supplier/tender/rfx-countdown`, data),

  // 保存报价信息
  priceSave: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/bid/price/save`, data),

  // 计算价格
  caluatePrice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/caluate-price`, data, {
      useNotify: false
    }),
  // 获取单据金额信息
  getRfxBidAmount: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/rfx-amount`, data),
  // 获取价格提示信息
  getPriceFloatMsg: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/price/quote/float`, data),
  // 获取配置文件
  getLogisticsConfig: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/logistics/detail/field`, data),
  // 计算物流汇总价格
  getLogisticsPrice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/logistics/calculate`, data),
  // 供方报价留言
  supplierSaveMessageApi: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/SupplierQuotationMessageBoard/supplierSaveMessage`, data),
  // 供方查看自己的留言板
  getMessageBoardMyselfApi: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/SupplierQuotationMessageBoard/getMessageBoardMyself/${data.rfxId}`
    ),
  // 采方查询留言板详情
  getByRfxIdApi: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/BuyerQuotationMessageBoard/getByRfxId/${data.rfxId}`),
  // 采方查询留言的供应商-弹框
  getMessageSupplierInfoApi: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/BuyerQuotationMessageBoard/getMessageSupplierInfo/${data.rfxId}`
    ),
  // 按降幅报价
  declineQuotation: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/price/decline/quotation`, data)
}

export default {
  NAME,
  APIS
}
