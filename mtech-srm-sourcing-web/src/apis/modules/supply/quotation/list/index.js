import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'quotationList'
const APIS = {
  /*
    报价
   */

  //列表
  //根据rfxId查询所有文件信息
  listQueryFile: `${PROXY_SOURCING}/tenant/supplier/tender/list`,

  // 同步数据
  tenderSync: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/tender/sync`, data),

  // 获取信息补充记录数据
  fetchInformation: `${PROXY_SOURCING}/tenant/supplier/bid/queryAppendInfoPage`,

  // 提交信息补充记录数据
  submitInformation: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/submitAppendInfo`, data),
  // 获取信息补充校验数据
  validAppendInfoList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/bid/validAppendInfoList`, data)
}

export default {
  NAME,
  APIS
}
