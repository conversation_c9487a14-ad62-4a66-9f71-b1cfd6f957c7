import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'pur'

const APIS = {
  /**
   * 获取采方税率List
   */
  getRates: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/pur-rates`, data),

  /**
   * 获取采方币种
   */
  getCurrencys: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/pur-currencys`, data),

  /**
   * 获取采方采购单位
   */
  getPurUnits: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/pur-units`, data),
  /**
   * 获取品牌
   */
  getBrands: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/hand/getPurSecondBrand`, data)
}

export default {
  NAME,
  APIS
}
