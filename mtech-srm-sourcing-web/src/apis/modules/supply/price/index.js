import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'

const NAME = 'supplierPrice'
const APIS = {
  //queryBuilder查询供应商清单
  getSupplierRecords: `/price/tenant/pricerecord/supplier/query/item`,
  getSupplierRecordsNew: `/price/tenant/priceRecord/new/supplier/pageQuery`,
  getSupplierlogis: `/price/tenant/priceRecord/new/supplier/logisticsy`,
  getHistoryRecords: `/price/tenant/pricerecord/supplier/query`,

  // 价格记录 - 动态列导出
  exportPriceRecord: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/priceRecord/new/supplier/record/export`, data, {
      responseType: 'blob'
    })
  }
}
export default {
  NAME,
  APIS
}
