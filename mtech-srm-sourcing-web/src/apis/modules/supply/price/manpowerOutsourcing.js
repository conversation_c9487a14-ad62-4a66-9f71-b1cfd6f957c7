import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'supManpowerOutsourcing'
const APIS = {
  /*********** 人力外包定价-供方 **********/
  // 查询列表
  queryManOutPricingList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/supplier/query`, data)
  },
  // 接受
  acceptManOutPricing: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/hro/point/supplier/feedback/receive`, data)
  },
  // 拒绝
  rejectManOutPricing: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/hro/point/supplier/feedback/reject`, data)
  }
}

export default {
  NAME,
  APIS
}
