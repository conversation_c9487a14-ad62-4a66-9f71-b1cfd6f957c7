import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'RfxSupRelConfig'
const APIS = {
  // 寻源项目 -我的投标-资质审查
  getSupRelBidList: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/examine/queryBuilder`, data),
  // 寻源项目 - 报价投标管理 - 获取列表 - 详情 -报价
  // 轮次物料
  // 寻源项目 - 报价投标管理 -资质审查保存
  getTenderFiles: (data) => API.put(`${PROXY_SOURCING}/tenant/supplier/examine/saveItemRel`, data),
  //供方资质审查列表
  // getrfxCainder: `${PROXY_SOURCING}/tenant/supplier/examine/queryItem`,
  // getrfxCaindesr: `${PROXY_SOURCING}/tenant/supplier/examine/queryItemRel`,
  //提交
  getRfxSave: (data) => API.put(`${PROXY_SOURCING}/tenant/supplier/examine/submitExamine`, data),
  //上传附件
  getTenderFileAdd: (data) =>
    API.put(`${PROXY_SOURCING}/tenant/supplier/examine/saveHeaderRel`, data),

  //上传技术附件
  uploadTechFiles: (data) => API.put(`${PROXY_SOURCING}/tenant/supplier/bid/saveTecFile`, data),

  //获取技术附件列表
  queryTecFileByRfxId: (data) =>
    API.get(`${PROXY_SOURCING}/tenant/supplier/bid/queryTecFileByRfxId`, data)
}

export default {
  NAME,
  APIS
}
