import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'candiDate'

const APIS = {
  /*
    寻源文件供应商接口
  */

  //queryBuilder查询供应商清单
  candiDateByRfxId: `${PROXY_SOURCING}/tenant/rfxRoundSupplier/queryBuilder`, //所有供应商
  //更新供应商短名单状态
  updateShortListBySupplierId: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/updateShortListBySupplierId`, data),
  //queryBuilder查询货源清单
  candiDateByRfxIds: `${PROXY_SOURCING}/tenant/rfxItemRoundSupplier/queryBuilder`, //非所有供应商
  //所有供应商删除
  candiDateDelete: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/deleteById`, data),
  //非所有供应商删除
  candiDateDeleteNo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItemRoundSupplier/deleteById`, data),
  //启用供应商--参数supplierId
  candiDateEnable: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxSupplier/enableBySupplierId`, data),
  //保存供应商
  candiDateSave: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/save`, data), //所有供应
  //保存货源
  rfxItemRoundSupplierSave: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/rfxItemRoundSupplier/save`, data), //非所有供应商
  //更新供应商评审状态
  candiDateUpdate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxSupplier/updateApproveBySupplierId`, data),
  //更新供应商标书购买状态
  candiDatePurchase: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxSupplier/updateBidPurchaseBySupplierId`, data),
  //更新供应商缴纳状态
  candiDateDeposit: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxSupplier/updateDepositBySupplierId`, data),
  //删除货源
  candiDateDeletes: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/deleteById`, data),
  //禁用货源
  candiDatedisable: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/disableById`, data),
  //启用货源
  candiDateEanble: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/enableById`, data),

  //根据品类查询rfx单据下货源清单--弹窗使用
  candiDateSite: `${PROXY_SOURCING}/tenant/rfxItemRoundSupplier/queryItemSiteSource`,
  //根据品类查询rfx单据下货源清单--弹窗使用
  queryItemSiteSource: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItemRoundSupplier/queryItemSiteSource`, data),
  //保存货源
  //保存货源
  candiDateSaves: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/save`, data)
}
export default {
  NAME,
  APIS
}
