/**
 * rfx/statistic/sup
 */
import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'rfxStatisticSup'
const APIS = {
  /**
   * 供方大厅上方表格接口
   */
  priceTable: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/sup/price/table`, data),

  /**
   * 供方大厅上方表格表头获取排名
   */
  totalRanking: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/sup/price/totalRanking`, data),

  /**
   * 统计供应商实时竞价（供方使用）
   */
  priceInfo: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/sup/price/info`, data),

  /**
   * 统计供应商报价排名（供方使用）
   */
  priceRank: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/sup/price/rank`, data),
  /**
   * 定时轮询统计供应商实时竞价
   */
  priceRankInfoSup: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/sup/pricebidding/statistic/price/real-rank`, data),
  /**
   * 定时轮询统计供应商实时排名 - 物流 - 供方
   */
  priceLogisticRankInfoSup: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/sup/pricebidding/statistic/logistics/price/real-rank`, data),
  /**
   * 定时轮询统计供应商实时排名 - 物流 - 采方
   */
  priceLogisticRankInfoPur: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/pur/pricebidding/statistic/logistics/price/real-rank`, data),
  /**
   * 定时轮询采方实时竞价
   */
  priceRankInfoPur: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/pur/pricebidding/statistic/price/real-rank
      `,
      data
    ),
  /**
   * 快速报价
   */
  fastSubmit: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/supplier/bid/price/fast/submit
     `,
      data
    ),
  /**
   * rsa秘钥
   */
  rsaKey: (data = {}) =>
    API.get(
      `${PROXY_SOURCING}/tenant/sup/pricebidding/statistic/price/key?rfxId=${data}
   `
    )
}

export default {
  NAME,
  APIS
}
