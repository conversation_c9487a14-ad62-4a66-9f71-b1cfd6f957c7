/**
 * /tenant/supplier/tender/*
 */

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'supplierTender'
const APIS = {
  /**
   * 导出报价单pdf
   */
  export: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/tender/export`, data, {
      responseType: 'blob'
    }),
  /**
   * 导出报价单pdf - 物流
   */
  logisticsExport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/tender/logistics/export`, data, {
      responseType: 'blob'
    }),
  /**
   * 获取货源关系
   */
  getSourceInfo: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/supplier/source-info`, data),

  /**
   * 供方策略获取
   */
  getStrategys: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/supplier/tender/strategys`, data)
}
export default {
  NAME,
  APIS
}
