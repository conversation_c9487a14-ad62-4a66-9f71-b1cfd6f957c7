import { API } from '@mtech-common/http'
import { PROXY_IAM } from 'CONFIG/proxy.config'
const NAME = 'iamService'
const APIS = {
  // 查询用户信息
  queryUserInfo: (params = {}) => {
    return API.get(`${PROXY_IAM}/common/account/userinfo`, params)
  },

  //获取当前登录人信息
  getUserDetail: (data) => API.get(`${PROXY_IAM}/tenant/account/user-detail`, data),

  // 选择的其他角色 查询账号接口
  getAccoutAccessIam: (data = {}) => API.post(`${PROXY_IAM}/tenant/account/listByTenantId`, data)
}

export default {
  NAME,
  APIS
}
