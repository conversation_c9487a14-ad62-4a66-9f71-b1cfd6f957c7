import { API } from '@mtech-common/http'
import { PROXY_FORM_DESIGN } from 'CONFIG/proxy.config'
const NAME = 'formDesignService'
const APIS = {
  // 新增表单
  addFormDesign: (data = {}) => {
    return API.post(`${PROXY_FORM_DESIGN}/tenant/form-design/add`, data)
  },
  // 更新表单
  updateFormDesign: (data = {}) => {
    return API.put(`${PROXY_FORM_DESIGN}/tenant/form-design/update`, data)
  },
  // 获取表单
  getFormDesignInfo: (data = {}) => {
    return API.get(`${PROXY_FORM_DESIGN}/tenant/form-design/get`, data)
  }
}

export default {
  NAME,
  APIS
}
