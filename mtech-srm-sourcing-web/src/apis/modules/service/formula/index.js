import { API } from '@mtech-common/http'
import { PROXY_ANALYSIS } from 'CONFIG/proxy.config'
const NAME = 'analysis'
const APIS = {
  /** 获取预定义函数列表 */
  getFormulaList() {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/expression/func/list`)
  },
  /** 校验用户输入函数
   * @param {string} expression - 用户输入的公式组合
   * @param {Object} paramMap - 公式用到的因子map
   */
  checkFormula(expression, paramMap) {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/expression/func/value`, {
      expression,
      paramMap
    })
  }
}

export default {
  NAME,
  APIS
}
