import { API } from '@mtech-common/http'
import { PROXY_WORK_FLOW } from 'CONFIG/proxy.config'
const NAME = 'workFlowService'
const APIS = {
  /*
    [3-租户级]-1.流程模型&模板模型
  */
  getTemplatePage: (params = {}) => {
    //分页查询-流程模型
    return API.get(`${PROXY_WORK_FLOW}/tenant/template/page`, params)
  },
  addTemplatePage: (params = {}) => {
    //添加新的模板
    return API.post(`${PROXY_WORK_FLOW}/tenant/template/add`, params)
  },
  publishTemplatePage: (params = {}) => {
    //发布模型
    return API.post(`${PROXY_WORK_FLOW}/tenant/template/publish`, params)
  },
  /*
    [3-租户级]-2.租户自定义分类
  */
  getTenantCategoryTree: (params = { group: 'processModel' }) => {
    //工作流-获取分类树
    return API.get(`${PROXY_WORK_FLOW}/tenant/category/tree`, params)
  },

  /*
    [管理级]工作流任务
  */
  deleteProcess: (data = {}) => {
    //工作流-关闭流程
    return API.delete(`${PROXY_WORK_FLOW}/admin/task/delete-process`, data)
  },
  finishFlow: (data = {}) => {
    //工作流-完成任务
    return API.post(`${PROXY_WORK_FLOW}/admin/task/finish`, data)
  },
  getHistoryTaskList: (params = {}) => {
    //工作流-历史流程
    return API.get(`${PROXY_WORK_FLOW}/admin/task/history-process`, params)
  },
  historyVar: (params = {}) => {
    //工作流-历史变量
    return API.get(`${PROXY_WORK_FLOW}/admin/task/history-var`, params)
  },
  listTask: (params = {}) => {
    //工作流-获取列表
    return API.get(`${PROXY_WORK_FLOW}/admin/task/list`, params)
  },
  taskLog: (params = {}, config = {}) => {
    //工作流-日志
    return API.get(`${PROXY_WORK_FLOW}/admin/task/log`, params, config)
  },
  getNowProcess: (params = {}) => {
    //工作流-当前活动流程
    return API.get(`${PROXY_WORK_FLOW}/admin/task/now-process`, params)
  },
  processImg: '/admin/task/process-img', //查看流程图
  getProcessImg: (params = {}) => {
    //查看流程图
    return API.get(`${PROXY_WORK_FLOW}/admin/task/process-img`, params)
  },
  updateTaskUser: (data = {}) => {
    //工作流-更新审批人信息
    return API.post(`${PROXY_WORK_FLOW}/admin/task/update-user`, data)
  },

  /*
      [管理级]工作流
    */
  startFlow: (data = {}) => {
    //激活流程
    return API.post(`${PROXY_WORK_FLOW}/admin/flow/start-flow`, data)
  },
  deleteFlow: (data = {}) => {
    //删除流程
    return API.delete(`${PROXY_WORK_FLOW}/admin/flow/deleter`, data)
  },
  getFlowDetail: (params = {}) => {
    //查询该模型下的流程
    return API.get(`${PROXY_WORK_FLOW}/admin/flow/detail`, params)
  },
  getFlow: (params = {}) => {
    //获取流程详情
    return API.get(`${PROXY_WORK_FLOW}/admin/flow/get`, params)
  },
  getModelList: (params = {}) => {
    //查询模型列表
    return API.get(`${PROXY_WORK_FLOW}/admin/flow/list-model`, params)
  },
  modelImg: '/admin/flow/model-img', //查看流程模型图
  getModelImg: (params = {}) => {
    //查询模型列表
    return API.get(`${PROXY_WORK_FLOW}/admin/flow/model-img`, params)
  },
  addModel: (data = {}) => {
    //添加模型
    return API.post(`${PROXY_WORK_FLOW}/admin/flow/add-model`, data)
  },
  recModel: (params = {}) => {
    //恢复模型
    return API.get(`${PROXY_WORK_FLOW}/admin/flow/rec-model`, params)
  },
  publisModel: (data = {}) => {
    //publish Model
    return API.post(`${PROXY_WORK_FLOW}/admin/flow/publish-model`, data)
  },

  /*
      [管理级]表单模板
    */
  getFormTemplate: (params = {}) => {
    //查看表单模板
    return API.get(`${PROXY_WORK_FLOW}/admin/form-template/get`, params)
  },
  getFormTemplateList: (params = {}) => {
    //获取表单模板列表
    return API.get(`${PROXY_WORK_FLOW}/admin/form-template/list`, params)
  },
  addFormTemplate: (data = {}) => {
    //添加表单模板
    return API.post(`${PROXY_WORK_FLOW}/admin/form-template/add`, data)
  },
  updateFormTemplateList: (data = {}) => {
    //更新表单模板
    return API.put(`${PROXY_WORK_FLOW}/admin/form-template/update-template`, data)
  },

  /*
      [管理级]表单值
    */
  getFormValue: (params = {}) => {
    //获取form-value详情
    return API.get(`${PROXY_WORK_FLOW}/admin/form-value/get`, params)
  },
  getFormValueList: (params = {}) => {
    //获取form-value列表
    return API.get(`${PROXY_WORK_FLOW}/admin/form-value/list`, params)
  },
  addFormValue: (data = {}) => {
    //添加表单值
    return API.post(`${PROXY_WORK_FLOW}/admin/form-value/add`, data)
  },
  updateFormValueList: (data = {}) => {
    //更新表单数据
    return API.put(`${PROXY_WORK_FLOW}/admin/form-value/update-data`, data)
  },

  /*
      [管理级]代理人
    */
  getFlowProxyList: (params = {}) => {
    //工作流-代理列表
    return API.get(`${PROXY_WORK_FLOW}/admin/flow-proxy/list`, params)
  },
  addFlowProxy: (data = {}) => {
    //工作流-新增代理
    return API.post(`${PROXY_WORK_FLOW}/admin/flow-proxy/add`, data)
  },

  /*
      flow-controller
    */
  getFlowList: (params = {}) => {
    //工作流-列表
    return API.get(`${PROXY_WORK_FLOW}/user/flow/list`, params)
  },
  getFlowIndex: (params = {}) => {
    //工作流-index
    return API.get(`${PROXY_WORK_FLOW}/user/flow/index`, params)
  },

  /*
      flow-form-feign-controller
*/
  queryFeignFormValue: (params = {}) => {
    //查询表单值列表
    return API.get(`${PROXY_WORK_FLOW}/feign/form/queryFormValue`, params)
  },
  getFeignFormValue: (params = {}) => {
    //获取表单内容值
    return API.get(`${PROXY_WORK_FLOW}/feign/form/getFormValue`, params)
  },
  updateFeignFormTemplate: (data = {}) => {
    //更新 表单模板
    return API.post(`${PROXY_WORK_FLOW}/feign/form/updateFormTemplate`, data)
  },
  getFeignFormTemplate: (data = {}) => {
    //获取 表单模板
    return API.post(`${PROXY_WORK_FLOW}/feign/form/getFormTemplate`, data)
  },
  addFeignFormTemplate: (data = {}) => {
    //添加form 表单模板
    return API.post(`${PROXY_WORK_FLOW}/feign/form/addFormTemplate`, data)
  },

  /*
      flow-feign-controller
  */
  closeFlow: (data = {}) => {
    //工作流-关闭任务
    return API.post(`${PROXY_WORK_FLOW}/feign/flow/closeFlow`, data)
  },
  finishTask: (data = {}) => {
    //工作流-完成任务
    return API.post(`${PROXY_WORK_FLOW}/feign/flow/finishTask`, data)
  },
  getFlowDefImage: (params = {}) => {
    //getFlowDefImage
    return API.get(`${PROXY_WORK_FLOW}/feign/flow/getFlowDefImage`, params)
  },
  getFlowImage: (params = {}) => {
    //getFlowImage
    return API.get(`${PROXY_WORK_FLOW}/feign/flow/getFlowImage`, params)
  },
  getTaskFormValueData: (params = {}) => {
    //getTaskFormValueData
    return API.get(`${PROXY_WORK_FLOW}/feign/flow/getTaskFormValueData`, params)
  },
  getTaskFormValueInfo: (params = {}) => {
    //getTaskFormValueInfo
    return API.get(`${PROXY_WORK_FLOW}/feign/flow/getTaskFormValueInfo`, params)
  },
  queryTaskByUser: (params = {}) => {
    //queryTaskByUser
    return API.get(`${PROXY_WORK_FLOW}/feign/flow/query-task-byuser`, params)
  },
  queryFlow: (data = {}) => {
    //queryFlow
    return API.post(`${PROXY_WORK_FLOW}/feign/flow/queryFlow`, data)
  },
  queryFlowByBusinessKey: (params = {}) => {
    //queryFlowByBusinessKey
    return API.get(`${PROXY_WORK_FLOW}/feign/flow/queryFlowByBusinessKey`, params)
  },
  queryNowTask: (data = {}) => {
    //queryNowTask
    return API.post(`${PROXY_WORK_FLOW}/feign/flow/queryNowTask`, data)
  },
  flowStart: (data = {}) => {
    //flowStart
    return API.post(`${PROXY_WORK_FLOW}/feign/flow/start`, data)
  },
  updateTaskAssignee: (data = {}) => {
    //updateTaskAssignee
    return API.post(`${PROXY_WORK_FLOW}/feign/flow/updateTaskAssignee`, data)
  },
  getModelListByQuery: (data = {}) => {
    //获取模板列表
    return API.get(`${PROXY_WORK_FLOW}/admin/model/page`, data)
  }
}

export default {
  NAME,
  APIS
}
