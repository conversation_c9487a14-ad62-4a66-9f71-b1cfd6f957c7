import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'porMine'
const APIS = {
  // 寻源需求项目 - 我的需求管理 - 我的列表主单tab - 列表
  queryMyHeader: `${PROXY_SOURCING}/tenant/porHeader/queryMyHeader`,

  // 寻源需求项目 - 我的需求管理 - 明细列表
  getMineDetailList: `${PROXY_SOURCING}/tenant/porItem/queryMyItem`,

  // 寻源需求项目 - 我的需求管理 - 我创建的tab - 列表
  queryCreate: `${PROXY_SOURCING}/tenant/porHeader/queryCreate`,

  // 寻源需求项目 - 我的需求管理 - 待审核tab - 列表
  queryNoApprove: `${PROXY_SOURCING}/tenant/porHeader/queryNoApprove`,

  // 寻源需求项目 - 我的需求管理 - 单据详情
  findPorHeaderDetailById: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/porHeader/findDetailById`, data),

  // 寻源需求项目 - 我的需求管理 - 转发弹窗
  forwardById: (data) => API.put(`${PROXY_SOURCING}/tenant/porHeader/forwardById`, data),

  // 寻源需求项目 - 我的需求管理 - 取消认领弹窗
  cancelClaimById: (data) => API.put(`${PROXY_SOURCING}/tenant/porHeader/cancelClaimById`, data),

  // 寻源需求项目 - 我的需求管理 - 顶部按钮 关闭
  closeById: (data) => API.put(`${PROXY_SOURCING}/tenant/porHeader/closeById`, data),

  // 寻源需求项目 - 我的需求管理 - 顶部按钮 取消关闭
  cancelCloseById: (data) => API.put(`${PROXY_SOURCING}/tenant/porHeader/cancelCloseById`, data),

  // 寻源需求项目 - 我的需求管理 - 按钮 发布
  publishById: (data) => API.put(`${PROXY_SOURCING}/tenant/porHeader/publishById`, data),

  // 寻源需求项目 - 我的需求管理 - 按钮 取消
  cancelApproveById: (data) =>
    API.put(`${PROXY_SOURCING}/tenant/porHeader/cancelApproveById`, data),

  // 寻源需求项目 - 我的需求管理 - 删除 取消
  sourceDeleteById: (data) => API.put(`${PROXY_SOURCING}/tenant/porHeader/deleteById`, data),

  // 寻源需求项目 - 我的需求管理 - 我创建的 - 顶部按钮：创建寻源需求
  saveSource: (data) => API.post(`${PROXY_SOURCING}/tenant/porHeader/save`, data),
  //创建寻源需求 --表单校验
  saveSourceValid: (data) => API.get(`${PROXY_SOURCING}/tenant/porHeader/save-valid`, data),

  // 寻源需求项目 - 我的需求管理 - 明细 - 转发
  forwardByIdDetail: (data) => API.put(`${PROXY_SOURCING}/tenant/porItem/forwardById`, data),

  // 寻源需求项目 - 我的需求管理 - 明细 - 取消认领
  cancelClaimByIdDetail: (data) =>
    API.put(`${PROXY_SOURCING}/tenant/porItem/cancelClaimById`, data),

  // 寻源需求项目 - 我的需求管理 - 明细 - 关闭
  closeByIdDetail: (data) => API.put(`${PROXY_SOURCING}/tenant/porItem/closeById`, data),

  // 寻源需求项目 - 我的需求管理 - 明细 - 取消关闭
  cancelCloseByIdDetail: (data) =>
    API.put(`${PROXY_SOURCING}/tenant/porItem/cancelCloseById`, data),

  // 寻源需求项目 - 我的需求管理 - 单据详情 - 删除 (需求明细)
  deleteDetailPorItemById: (data) => API.put(`${PROXY_SOURCING}/tenant/porItem/deleteById`, data),

  // 寻源需求项目 - 我的需求管理 - 单据详情 - 删除（供应商）
  deleteDetailPorSupplierById: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/porSupplier/deleteById`, data),

  // 新增 RFX  通过需求新增询价单
  saveRFXPor: (data) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/saveByPor`, data),
  // 新增 RFX  通过需求新增询价单
  saveRFXPorvalid: (data) => API.get(`${PROXY_SOURCING}/tenant/rfxHeader/saveByPor-valid`, data),
  // 新增 RFX  通过需求明细新增询价单
  saveRFXDetailPor: (data) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/saveByPorItem`, data),
  //获取当前业务类型信息
  getUserbusinessType: (data) => API.post(`${PROXY_SOURCING}/tenant/porHeader/queryLate`, data)
}

export default {
  NAME,
  APIS
}
