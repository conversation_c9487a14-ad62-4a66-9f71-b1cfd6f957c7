import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'porHeader'
const APIS = {
  // 寻源需求项目 - 需求汇总管理 - 需求汇总主单tab - 列表
  getRequireSummary: `${PROXY_SOURCING}/tenant/porHeader/queryBuilder`,

  // 寻源需求项目 - 需求汇总管理 - 明细列表
  getSummaryDetailList: `${PROXY_SOURCING}/tenant/porItem/queryBuilder`,

  // 寻源需求项目 - 需求汇总管理 - 认领弹窗 根据ID认领
  claimById: (data) => API.put(`${PROXY_SOURCING}/tenant/porHeader/claimById`, data),

  // 寻源需求项目 - 需求汇总管理 - 分配弹窗 根据ID分配
  distributeById: (data) => API.put(`${PROXY_SOURCING}/tenant/porHeader/distributeById`, data),

  // 寻源需求项目 - 需求汇总管理 - 明细 - 认领
  claimByIdDetail: (data) => API.put(`${PROXY_SOURCING}/tenant/porItem/claimById`, data),

  // 寻源需求项目 - 需求汇总管理 - 明细 - 分配
  distributeByIdDetail: (data) => API.put(`${PROXY_SOURCING}/tenant/porItem/distributeById`, data)
}

export default {
  NAME,
  APIS
}
