import { API } from '@mtech-common/http'
import { PROXY_SOURCING, PROXY_MASTER_DATA } from 'CONFIG/proxy.config'
const NAME = 'comparativePrice'
const APIS = {
  /*
    比价助手
  */
  //查询比价信息
  queryComp: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/comp/queryComp`, data),
  //查询比价信息
  queryCompExport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/comp/queryComp/export`, data, {
      responseType: 'blob'
    }),
  //查询最高报价和最低报价
  queryTotalPrice: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/comp/queryTotalPrice`, data),
  //查询最高报价和最低报价和排名
  queryTotalPriceRanking: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/comp/queryTotalPriceRanking`, data),
  // 根据rfxId查询供应商清单
  queryDistinctSupplier: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/queryDistinctSupplier`, data),
  // 获取表头
  queryRfxConfig: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfx/module/config`, data),
  // 根据rfxId查询明细清单
  queryDistinctItem: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxItem/queryDistinctItem`, data),
  // 根据rfxId查询直送地
  queryDeliveryPlaces: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxItem/queryDeliveryPlaces`, data),
  //待选物料（是否有价格记录）--分页查询
  queryItems: `${PROXY_SOURCING}/tenant/rfxItem/queryItems?BU_CODE=${localStorage.getItem('currentBu')}`,
  queryItemsData: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxItem/queryItems?BU_CODE=${localStorage.getItem('currentBu')}`, data),
  //待选物料（成本因子）--分页查询
  costFactor: `${PROXY_MASTER_DATA}/tenant/item-cost-factor/paged-query`,
  querycostFactor: `${PROXY_MASTER_DATA}/tenant/item-cost-factor/queryList`,
  queryItemsTwo: `${PROXY_SOURCING}/tenant/rfxItem/queryBuilderSerialItem`,
  queryItemsTwoData: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/queryBuilderSerialItem`, data),
  // //查询本次报价过程
  // queryCurPrice: (data = {}) =>
  //   API.post(`${PROXY_SOURCING}/tenant/comp/queryCurPrice`, data),
  //查询本次报价过程)(图表)
  queryCurPriceTable: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/comp/queryCurPriceTable`, data),
  // 获取历史报价信息列表
  queryCurPrice: `${PROXY_SOURCING}/tenant/comp/queryCurPrice`,
  //查询历史价格分析
  queryHisPrice: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/comp/queryHisPrice`, data),
  // 获取价格信息字段
  priceInfo: (data = {}) => API.get(`${PROXY_SOURCING}/common/base/priceInfo`, data),
  // 议价
  abate: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxBidding/abate`, data),
  // 接受报价
  accept: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/accept`, data),
  // 转定价
  toPricing: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfx/pricing/toPricing`, data),
  // 发起下一轮
  startNewRound: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxRound/startNewRound`, data),

  //提交定点推荐
  commitNewPointById: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxHeader/commitNewPointById`, data),
  // 批量VMI标识
  batchSetVmi: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/batchSetVmi`, data)
}

export default {
  NAME,
  APIS
}
