import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'scoringTemplateGradingRules'
const APIS = {
  /*
    评分模板细项
  */

  //获取评分细项列表
  queryScoreDetails: `${PROXY_SOURCING}/tenant/rfx/score/detail/queryBuilder`,
  //删除评分细项
  deleteScoringItems: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/score/detail/delete`, data),
  //启用评分细项
  enableScoringItems: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/score/detail/enable`, data),
  //禁用评分细项
  disableScoringItems: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/score/detail/disable`, data),
  //新增评分细项
  scoreDetailSave: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfx/score/detail/save`, data)
}

export default {
  NAME,
  APIS
}
