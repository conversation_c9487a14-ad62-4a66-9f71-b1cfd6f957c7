import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'scoringTemplateDetails'
const APIS = {
  /*
    评分模板明细
  */

  //查询评分模板详情
  ItemQuery: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfx/score/item/query`, data),
  //新增-更新评分详情(明细)
  AddUpdateScoringDetails: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/rfx/score/item/save`, data),
  //新增-更新评分详情(明细)
  queryScoreDetails: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/score/detail/queryBuilder`, data),
  //条件查询评分细项
  queryByType: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/score/detail/queryByType/${data}`)
}

export default {
  NAME,
  APIS
}
