import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'scoringTemplateList'
const APIS = {
  /*
    评分模板列表
  */
  //获取评分模板列表
  queryScore: `${PROXY_SOURCING}/tenant/rfx/score/model/queryBuilder`,
  //删除评分模板
  modelDelete: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/score/model/delete`, data),
  //禁用评分模板
  modelDisable: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/score/model/disable`, data),
  //禁用评分模板
  modelEnable: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/score/model/enable`, data)
}

export default {
  NAME,
  APIS
}
