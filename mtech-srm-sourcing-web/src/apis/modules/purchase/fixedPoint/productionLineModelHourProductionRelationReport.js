/********** 生产线机型时产关系表 ***********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'plmhpRelationReport'
const APIS = {
  // 查询列表
  queryPlmhprrList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/beltlineModel_yieldRel/query`, data)
  },
  // 保存
  savePlmhprrList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/beltlineModel_yieldRel/update`, data)
  },
  // 下载导入模板
  downloadPlmhprrTemplate: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/beltlineModel_yieldRel/downloadTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导入
  importPlmhprr: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/hro/beltlineModel_yieldRel/strengthenImport`,
      data.data,
      {
        responseType: 'blob'
      }
    )
  },
  //导出
  exportPlmhprr: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/beltlineModel_yieldRel/exportResult`, data, {
      responseType: 'blob'
    })
  },
  // 更新
  updatePlmhprrList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/hro/beltlineModel_yieldRel/supplementProdLineCode`,
      data
    )
  }
}

export default {
  NAME,
  APIS
}
