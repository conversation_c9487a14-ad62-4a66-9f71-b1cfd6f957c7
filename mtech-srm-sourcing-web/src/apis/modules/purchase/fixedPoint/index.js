import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
import utils from '@/utils/utils'
const NAME = 'fixedPoint'
const APIS = {
  /*
    寻源项目-定点推荐-菜单
  */
  /**
   * @description: 定点推荐-query查询定点推荐  OK
   * @param {*}
   * @return {*}
   */
  getPointRecommendList: `${PROXY_SOURCING}/tenant/recommend/query`,
  /**
   * @description: 定点推荐-退回比价  OK
   * @param {*}
   * @return {*}
   */
  doBackPointRecommend: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/recommend/back`, data)
  },
  //退回比价，表单验证   报500
  doBackPointRecommendValid: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/recommend/back-valid`, data)
  },

  /*
    寻源项目-定点管理-菜单
  */
  /**
   * @description: 定点管理-新建定点单详情
   * @param {*}
   * @return {*}
   */
  addPointDetail: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/addDetail`, data)
  },
  /**
   * @description: 定点管理-退回定点推荐  ?
   * @param {*}
   * @return {*}
   */
  doBackPoint: (data = {}) => {
    const query = utils.toQueryParams(data)
    return API.post(`${PROXY_SOURCING}/tenant/point/back?${query}`)
  },
  //定点管理-退回定点推荐  表单验证  500
  doBackPointValid: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/point/back-valid`, data)
  },
  /**
   * @description: 定点管理-关闭定点单
   * @param {*}
   * @return {*}
   */
  doClosePoint: (data = {}) => {
    const query = utils.toQueryParams(data)
    return API.post(`${PROXY_SOURCING}/tenant/point/close?${query}`)
  },
  //定点管理-关闭定点单-表单验证   404
  doClosePointValid: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/point/close-valid`, data)
  },
  /**
   * @description: 定点管理-查询定点单详情
   * @param {*}
   * @return {*}
   */
  getPointDetail: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/point/detail`, data)
  },
  /**
   * @description: 定点管理-获取定点模块  OK
   * @param {*}
   * @return {*}
   */
  getPointModule: (data = {}) => {
    const query = utils.toQueryParams(data)
    return API.post(`${PROXY_SOURCING}/tenant/point/module?${query}`)
  },
  /**
   * @description: 定点管理-query查询定点  OK
   * @param {*}
   * @return {*}
   */
  getPointList: `${PROXY_SOURCING}/tenant/point/query`,
  /**
   * @description: 定点管理-组成定点单
   * @param {*}
   * @return {*}
   */
  saveFixedPoint: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/saveFixedPoint`, data)
  },
  //定点管理-组成定点单  顶部表单验证   返回数据字段，均为空对象
  saveFixedPointValid: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/point/saveFixedPoint-valid`, data)
  },
  /**
   * @description: 定点管理-提交定点单审批  OK
   * @param {*}
   * @return {*}
   */
  doSubmitPoint: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/submit`, data)
  },
  /**
   * @description: 定点管理-取消定点单
   * @param {*}
   * @return {*}
   */
  cancelFixedPoint: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/cancelFixedPoint`, data)
  },
  /**
   * @description: 定点管理-修改定点单并提交
   * @param {*}
   * @return {*}
   */
  doUpdatePoint: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/update`, data)
  },
  /**
   * @description: 定点-获取数据    old
   * @param {*}
   * @return {*}
   */
  pointListGet: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/priceItem/query`, data)
  },
  /**
   * @description: 定点-提交审批    old
   * @param {*}
   * @return {*}
   */
  pointSubmit: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/priceItem/submitApproval`, data)
  }
}

export default {
  NAME,
  APIS
}
