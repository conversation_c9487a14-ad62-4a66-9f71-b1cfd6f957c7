import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'commonConfig'
const APIS = {
  /*
    公共接口
  */
  //获取基础模块
  getBaseModules: (params) => API.get(`${PROXY_SOURCING}/common/base/modules`, params),

  //获取基础任务结点
  getBaseTasks: (params) => API.get(`${PROXY_SOURCING}/common/flow/base/tasks`, params),

  //获取审批流结点类型列表
  getFlowOperationTypes: (params = {}) => {
    return API.get(`${PROXY_SOURCING}/common/flow/operation-types`, params)
  },
  //列表页数据导出
  exportBiddingData: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/exportResult`, params, {
      // return API.post(`/masterDataManagement/analysisVirtualOrg/export`, params, {
      responseType: 'blob'
    })
  },
  // 导出 - 定价
  exportPricing: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/exportPricing`, params, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
