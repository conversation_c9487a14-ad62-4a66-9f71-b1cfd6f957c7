/**
 * 公示接口 (common)
 */
import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'commonRfxNotice'

const APIS = {
  /**
   * 公示规则详情
   * @returns
   */
  getNotice: (data = {}) => API.post(`${PROXY_SOURCING}/common/rfxNotice/getNotice`, data),

  /**
   * 列出所有的公告
   * @returns
   */
  getListNotice: (data = {}) => API.post(`${PROXY_SOURCING}/common/rfxNotice/listNotice`, data)
}

export default {
  NAME,
  APIS
}
