/********** 成本因子联动定价 ***********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'logistics'
const APIS = {
  // 查询列表-查询
  queryLogisticsList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/header/pageQuery`, data)
  },
  // 查询列表-导出
  exportLogisticsList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/header/export`, data, {
      responseType: 'blob'
    })
  },
  // 查询列表-批量删除
  deleteLogistics: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/header/delete`, data)
  },
  // 详情页 - 查询 - 头部
  queryHeader: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/getHeaderById`, data)
  },
  // 详情页 - 保存 - 头部
  saveLogistics: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/header/save`, data)
  },
  // 详情页 - 查询 - 明细
  priceLogisticsItemPageQuery: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/item/pageQuery`, data)
  },
  // 详情页 - 保存 - 明细
  saveLogisticsItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/item/save`, data)
  },
  // 详情页 - 提交 - 头部
  submitLogistics: (data = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant/point/logistics/submitPoint`, data)
  },
  deleteLogistisItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/item/delete`, data)
  },
  // 详情页 - 导出 - 明细
  exportPriceLogisticsItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/item/export`, data, {
      responseType: 'blob'
    })
  },
  // 详情页 - 导出 - 明细
  exportTempaltePriceLogisticsItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point/logistics/item/export/template`, data, {
      responseType: 'blob'
    })
  },
  // 详情页 - 导入 - 明细
  importPriceLogisticsItem: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point/logistics/item/import`, data, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
