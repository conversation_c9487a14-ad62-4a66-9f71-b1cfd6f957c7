import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'applicationForm'

const APIS = {
  /*
    寻源文件-----制造审批表
  */

  //模具制造审批表详情查询
  detailQueryBuilder: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/mod/detailQueryBuilder`, data),
  //导入SAP
  modSyncSap: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/mod/modSyncSap`, data),
  //编辑保存明细行
  saveBatchMoldDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/mod/saveBatchMoldDetail`, data),
  //获取明细行数据
  queryMoldDetailList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/mod/queryMoldDetailList`, data),
  //获取附件明细行数据
  queryMoldDetailFilesList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/mod/queryMoldDetailFilesList`, data),
  //保存
  modSave: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/mod/save`, data),
  //提交
  modModCommit: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/mod/modCommit`, data),
  //附件上传
  modFileUpload: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/mod/modFileUpload`, data),
  //附件批量上传
  modBatchFileUpload: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/mod/modBatchFileUpload`, data)
}
export default {
  NAME,
  APIS
}
