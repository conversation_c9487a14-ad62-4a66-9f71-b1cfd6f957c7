/*
 * @Author: wenjie20.wang <EMAIL>
 * @Date: 2022-09-27 14:51:45
 * @LastEditors: wenjie20.wang <EMAIL>
 * @LastEditTime: 2022-09-30 10:44:57
 * @FilePath: \mtech-srm-sourcing-web\src\apis\modules\purchase\quotaCalc\itemQuotaCommit.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'itemQuotaCommit'
const APIS = {
  /*
    配额管理接口
  */

  // 协议配额明细查询-列表查询
  getItemQuotaCommitList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommitDetail/queryBuilder`, data),

  // 获取允许操作的公司信息
  getPermissionCompanyList: (data) =>
    API.post(`/masterDataManagement/tenant/permission/company`, data),

  // 根据权限获取有权限的工厂
  getPermissionSiteList: (data) =>
    API.post(
      `/masterDataManagement/tenant/permission/sites?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    ),

  // 确认
  batchConfirmItemQuotaCommit: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommitDetail/confirm`, data),

  // 取消
  batchCancelItemQuotaCommit: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommitDetail/cancel`, data),

  // 提交确认
  batchSubimtItemQuotaCommit: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommitDetail/submit`, data),

  // 导入
  excelimport: (data = {}) => {
    return API.post(
      `${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommitDetail/importExcel`,
      data,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
        responseType: 'blob'
      }
    )
  },
  // 导入模板
  exportTpl: (data = {}) => {
    return API.post(
      `${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommitDetail/exportExcel`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 导出
  excelExport: (data = {}) => {
    return API.post(
      `${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommitDetail/exportExcel`,
      data,
      {
        responseType: 'blob'
      }
    )
  }
}

export default {
  NAME,
  APIS
}
