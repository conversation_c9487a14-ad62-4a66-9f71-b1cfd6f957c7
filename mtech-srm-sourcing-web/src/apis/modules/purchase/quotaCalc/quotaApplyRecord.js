import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
import qs from 'qs'
const NAME = 'quotaApplyRecord'
const APIS = {
  /*
    配额管理接口
  */

  /*
    配额调整申请单据
  */
  // 配额申请单据明细页面，增加导出功能
  getQuotaCodeList: (data) =>
    API.post(
      `${PROXY_PRICEING}/tenant/quota/apply/item/export/quotaCode?quotaCode=${data.quotaCode}`,
      data,
      {
        responseType: 'blob'
      }
    ),

  // 配额调整备案头数据分页查询
  getRecordList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/headerPageQuery`, data),

  // 配额调整备案头数据导出
  exportRecordList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/exportHeader`, data, {
      responseType: 'blob'
    }),

  /*
    配额调整申请单据明细查询
  */
  // 配额调整备案行数据分页查询
  getRecordRowList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/itemPageQuery`, data),

  // 配额调整备案行数据导入
  importRecordRowList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/importItemExcel`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    }),

  // 配额调整备案行数据导出
  getImportTemplate: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/getImportTemplate`, data, {
      responseType: 'blob'
    }),

  // 配额调整备案行数据导出
  exportRecordRowList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/exportItem`, data, {
      responseType: 'blob'
    }),

  // 配额调整备案新增
  addRecordRow: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/add`, data),

  // 配额调整备案提交
  submitQuotaApply: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/approval`, data),

  // 配额调整备案更新
  updateRecordRow: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/update`, data),

  // 配额调整备案详情
  getRecordDetail: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/detail`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    }),

  // 配额调整备案删除
  deleteRecordRow: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/del`, data),

  // 配额调整备案删除行
  deleteRecordItem: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/delItem`, data),

  // 配额调整备案删除头附件
  deleteFileByIds: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjustmentFiling/deleteFileByIds`, data),

  // 所有人员
  usersPage: (data = {}) => {
    return API.post(`/iam/tenant/granted-subject/auth/users/page`, data)
  },
  // 采购开发下拉列表
  getBuyerList: (data = {}) => {
    return API.get(`/masterDataManagement/tenant/employee/currentTenantEmployees`, data)
  }
}

export default {
  NAME,
  APIS
}
