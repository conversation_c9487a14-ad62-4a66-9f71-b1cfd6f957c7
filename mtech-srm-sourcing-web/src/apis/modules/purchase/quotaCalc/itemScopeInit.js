import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'itemScopeInit'
const APIS = {
  /*
    配额管理接口
  */

  // 协议配额明细查询-列表查询
  getItemScopeInitList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/itemScopeInit/queryBuilder`, data),

  // 获取允许操作的公司信息
  getPermissionCompanyList: (data) =>
    API.post(`/masterDataManagement/tenant/permission/company`, data),

  // 根据权限获取有权限的工厂
  getPermissionSiteList: (data) =>
    API.post(
      `/masterDataManagement/tenant/permission/sites?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    ),

  // 导出
  excelExport: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/itemScopeInit/export`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
