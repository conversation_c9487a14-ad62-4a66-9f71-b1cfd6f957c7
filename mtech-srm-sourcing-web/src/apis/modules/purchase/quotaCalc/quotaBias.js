import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'quotaBias'
const APIS = {
  /*
    配额管理接口
  */

  // 配额偏差-清单列表查询
  getQuotaBiasList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/queryBuilder`, data),

  // 配额偏差-反馈列表查询
  getQuotaBiasFeedBackList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/feedBack/queryBuilder`, data),

  // 配额偏差-审核列表查询
  getQuotaBiasReviewList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/review/queryBuilder`, data),

  // 配额偏差-调整导入
  importQuotaBias: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/adjustImport`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    }),

  // 配额偏差-调整导入模板下载
  getQuotaBiasTemplate: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/adjustImportTemplate`, data, {
      responseType: 'blob'
    }),

  // 配额偏差-导出
  exportQuotaBias: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/adjustImportTemplate`, data, {
      responseType: 'blob'
    }),

  // 配额偏差-发布
  publishQuotaBias: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/publish`, data),

  // 配额偏差-撤回
  recallQuotaBias: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/recall`, data),

  // 配额偏差-反馈导入
  importQuotaBiasFeedback: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/feedBackImport`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    }),

  // 配额偏差-反馈导入模板下载
  getQuotaBiasFeedbackTemplate: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/feedBackTemplateImport`, data, {
      responseType: 'blob'
    }),

  // 配额偏差-反馈导出
  exportQuotaBiasFeedback: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/feedBackExport`, data, {
      responseType: 'blob'
    }),

  // 配额偏差-反馈导出
  exportQuotaBiasReview: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/reviewExport`, data, {
      responseType: 'blob'
    }),

  // 配额偏差-提交
  commitQuotaBias: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/confirmCommit`, data),

  // 配额偏差-审核  -通过  -驳回
  approvalQuotaBias: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaBias/review`, data)
}

export default {
  NAME,
  APIS
}
