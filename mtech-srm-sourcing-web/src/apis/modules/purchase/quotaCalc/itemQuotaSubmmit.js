import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'itemQuotaSubmmit'
const APIS = {
  /*
    配额管理接口
  */

  // 列表查询
  getItemList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommit/queryBuilder`, data),

  // 提交
  getDetailList: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommit/batchSubmit`, data)
  },
  // 保存
  saveList: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaAdjust/quotaItemSubmit/save`, data),
  //提交审批
  submitList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAdjust/quotaItemSubmit/submit`, data),
  //制定审批提交明细页面-获取物料配额分页列表
  getQueryBuild: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaAllocation/quotaItemCommitDetail/queryBuilder`, data),
  //获取审批头信息
  getTitleQueryBuild: (data) =>
    API.get(`${PROXY_PRICEING}/tenant/quotaAdjust/quotaItemSubmit/getByCode/` + data)
}

export default {
  NAME,
  APIS
}
