/********** 成本因子联动定价 ***********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'publicSourcing'
const APIS = {
  // 查询列表-查询
  queryInitiatedList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/queryPage`, data),
  // 查询列表 - 获取分享海报
  getSharePicture: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/getSharePicture`, data),
  // 查询列表-批量删除
  deleteInitiatedList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/baseInfo/delete`, data),
  // 查询列表-批量提交
  submitInitiatedList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/submitOa/apply`, data),
  // 查询列表-作废
  voidInitiatedList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/submitOa/abandon`, data),
  // 查询列表-修改截止时间
  updateDeclineDate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/deadlineDate/update`, data),
  // 付款条件
  queryPaymentTerms: (data = {}) =>
    API.post(`/masterDataManagement/tenant/payment-terms/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data),
  // 详情页 - 查询 - 详细信息
  queryBaseInfo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/details`, data),
  // 详情页 - 保存 - 基础信息
  saveBaseInfo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/baseInfo/save`, data),
  //详情页 - 寻源要求 - 查询
  queryRequireList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcing/requireConfig/queryList`, data),
  //详情页 - 寻源要求 - 保存
  saveRequireList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcing/requireConfig/batchSave`, data),
  // 详情页 - 根据rfxid查询所有文件节点信息
  queryFileNodeByRfxId: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/publicSourcingHeader/queryFileNodeByRfxId`, data),
  // 详情页 - 根据rfxid查询所有文件信息
  queryFileByRfxId: `${PROXY_SOURCING}/tenant/publicSourcingHeader/queryFileByRfxId`,
  // 详情页 - 保存文件信息
  saveFile: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/publicSourcingHeader/saveFile`, data),
  // 详情页 - 入围评定 - 查询页面
  queryEvaluationList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/evaluation/list`, data),
  // 详情页 - 入围评定 - 淘汰
  obsoleteEvaluationList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/evaluation/obsolete`, data),
  // 详情页 - 入围评定 - 通过
  passEvaluationList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/evaluation/pass`, data),
  // 详情页 - 入围评定 - 驳回
  rejectEvaluationList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/evaluation/reject`, data),
  // 详情页 - 入围评定 - 提交OA审批
  submitOaEvaluationList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/submitOa/select`, data),
  // 详情页 - 入围评定 - 提交OA审批校验
  checkEvaluationList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/submitOa/selectCheck`, data),
  // 详情页 - 入围评分 - 合作承诺
  queryCommitments: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/queryCommitments`, data),
  // 详情页 - 入围评分 - 企业信息
  queryEnterprise: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/queryEnterprise`, data),
  // 详情页 - 入围评分 - 供应商报价信息
  queryBiddingItem: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/queryBiddingItem`, data),
  // 详情页 - 入围评分 - 查询页面
  queryScoreList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/queryScoreList`, data),
  // 详情页 - 入围评分 - 保存页面
  saveScoreInfo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/saveScoreInfo`, data),
  // 详情页 - 入围评分 - 比较页面
  querySupplierQuoteList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcingHeader/querySupplierQuoteList`, data),
  // 详情页 - 获取oa审批链接
  getOaLink: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/publicSourcingHeader/getOaLink`, data),
  // 详情页 - 入围评分 - 审核保存
  reviewSave: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcing/requireConfig/review/save`, data),
  /** ----------------------------------------供应商接口-------------------------------------------- */
  // 查询列表-查询
  queryPublicList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/queryPage`, data),
  // 查询列表-参与
  joinPublicList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/join`, data),
  // 详情页 - 供方- 明细信息查询
  queryPublicDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/details`, data),
  //详情页 - 供方 - 寻源要求 - 查询
  querySupplierRequireList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/publicSourcing/requireConfig/supplier/queryList`, data),
  //详情页 - 供方 - 信息保存
  saveSupplierInfo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/baseInfo/save`, data),
  //详情页 - 供方 - 信息提交
  submitSupplierInfo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/submit`, data),
  //详情页 - 供方 - 企业信息查询
  querySupplierEnterprise: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/queryEnterprise`, data),
  // 详情页 - 供方 - 合作承诺页签查询
  querySupplierCommitments: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/queryCommitments`, data),
  // 详情页 - 根据rfxid查询所有文件节点信息
  querySupplierFileNodeByRfxId: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/queryFileNodeByRfxId`, data),
  // 详情页 - 根据rfxid查询所有文件信息
  querySupplierFileByRfxId: `${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/queryFileByRfxId`,
  // 详情页 - 保存文件信息
  saveSuppllierFile: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/saveFile`, data),
  // 入围结果 - 供方- 入围结果查询
  querySourcingResultList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/publicSourcingHeader/querySourcingResultList`, data)
}

export default {
  NAME,
  APIS
}
