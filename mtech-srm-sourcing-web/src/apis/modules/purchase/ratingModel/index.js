import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'ratingModel'

const APIS = {
  // 评分指标-分页查询
  pageRatingIndexApi(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/publicSourcingScoringIndicators/queryPage`, data)
  },
  // 评分指标-保存
  saveRatingIndexApi(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/publicSourcingScoringIndicators/saveBatch`, data)
  },
  // 评分指标-更新状态
  updateStatusRatingIndexApi(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/publicSourcingScoringIndicators/updateStatus`, data)
  },
  // 评分指标明细-查询
  detailRatingIndexApi(data = {}) {
    return API.post(
      `${PROXY_SOURCING}/tenant/publicSourcingScoringIndicatorsItem/queryByIndexId`,
      data
    )
  },
  // 评分指标明细-保存
  saveDetailRatingIndexApi(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/publicSourcingScoringIndicatorsItem/saveBatch`, data)
  },
  // 评分指标明细-删除
  deleteDetailRatingIndexApi(data = {}) {
    return API.post(
      `${PROXY_SOURCING}/tenant/publicSourcingScoringIndicatorsItem/deleteByIdList`,
      data
    )
  },

  // 评分模型-分页查询
  pageRatingModelApi(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/publicSourcingRatingModel/queryPage`, data)
  },
  // 评分模型-更新状态
  updateStatusRatingModelApi(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/publicSourcingRatingModel/updateStatus`, data)
  },
  // 评分模型-保存
  saveRatingModelApi(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/publicSourcingRatingModel/saveModel`, data)
  },
  // 评分模型-详情
  detailRatingModelApi(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/publicSourcingRatingModel/detail/${data.id}`)
  },
  // 评分模型-根据指标+纬度+状态查询指标详情
  queryItemDetailApi(data = {}) {
    return API.post(
      `${PROXY_SOURCING}/tenant/publicSourcingScoringIndicators/queryItemDetail`,
      data
    )
  }
}

export default {
  NAME,
  APIS
}
