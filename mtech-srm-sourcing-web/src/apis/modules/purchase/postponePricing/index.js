/********** 成本因子联动定价 ***********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'postponePricing'
const APIS = {
  // 查询列表-查询
  queryPostponeList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/pageQuery`, data)
  },
  // 查询列表-导出
  exportPostponeList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/export`, data, {
      responseType: 'blob'
    })
  },
  // 查询列表-批量删除
  deletePostpone: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/batchDel`, data)
  },
  // 查询列表-批量提交
  batchSubmitPostpone: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/batchCommit`, data)
  },

  // 详情页 - 查询 - 头部
  queryHeader: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/queryHeader`, data)
  },
  // 详情页 - 保存 - 头部
  savePostpone: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/savePricePostpone`, data)
  },
  // 详情页 - 提交 - 头部
  submitPostpone: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/commit`, data)
  },
  // 详情页 - 查询 - 明细
  pricePostponeItemPageQuery: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/pricePostponeItemPageQuery`, data)
  },
  // 详情页 - 保存 - 明细
  savePostponeItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/savePricePostponeItem`, data)
  },
  // 详情页 - 删除 - 明细
  deletePostponeItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/delPricePostponeItem`, data)
  },
  // 详情页 - 根据rfxid查询所有文件节点信息
  queryFileNodeByRfxId: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/price/postpone/queryFileNodeByRfxId`, data)
  },
  // 详情页 - 根据rfxid查询所有文件信息
  queryFileByRfxId: `${PROXY_SOURCING}/tenant/price/postpone/queryFileByRfxId`,
  // 详情页 - 保存文件信息
  saveFile: (data = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant//price/postpone/saveFile`, data)
  },
  // 详情页 - 根据rfxid查询价格记录
  queryPriceRecordList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/queryPriceRecordPage`, data)
  },

  // 详情页 - 下载导入模板 - 明细
  downloadImportTemplate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/price/postpone/downloadImportTemplate`, data, {
      responseType: 'blob'
    }),
  // 详情页 - 导入 - 明细
  importPricePostponeItem: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/price/postpone/importPricePostponeItem`, data, {
      responseType: 'blob'
    }),

  // 详情页 - 导出 - 明细
  exportPricePostponeItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/price/postpone/exportPricePostponeItem`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
