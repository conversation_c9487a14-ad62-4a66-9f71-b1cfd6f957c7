import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
import { PROXY_MASTER_DATA } from 'CONFIG/proxy.config'
const NAME = 'seriesItem'
const APIS = {
  formulaQuery: (data = {}) => {
    //查询计算公式
    return API.post(`${PROXY_MASTER_DATA}/tenant/category-series-formula/criteria-query`, data)
  },

  itemRangeSave: (data = {}) => {
    //保存适用物料范围
    return API.post(`${PROXY_SOURCING}/tenant/series/item/range/save`, data)
  },

  itemRangeQuery: (data = {}) => {
    //查询适用物料范围
    return API.post(`${PROXY_SOURCING}/tenant/series/item/range/query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data)
  },
  //列表查询系列物料定价单
  itemApplyList: `${PROXY_SOURCING}/tenant/series/item/apply/list`,

  itemApplySave: (data = {}) => {
    //保存并进入系列物料定价单
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/save`, data)
  },

  itemApplyDetail: (data = {}) => {
    //查看系列物料定价单详情
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/detail`, data)
  },
  calcPrice: (data = {}) => {
    //查看系列物料定价单详情
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/price/calcPrice`, data)
  },

  //查看系列物料定价单定价物料-配额分配详情
  itemApplyDetailPrice: `${PROXY_SOURCING}/tenant/series/item/apply/detail/price`,
  queryBuilder: `${PROXY_SOURCING}/tenant/pointQuota/queryBuilder`,
  itemApplySeriesSave: (data = {}) => {
    //保存系列物料
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/series/save`, data)
  },
  itemApplyPriceQueryUrl: `${PROXY_SOURCING}/tenant/series/item/apply/price/pageQuery`,
  itemApplyPriceQuery: (data = {}) => {
    //查询系列物料关联的物料主数据
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/price/query`, data)
  },

  itemApplyPriceSave: (data = {}) => {
    //保存定价物料
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/price/save`, data)
  },
  //保存定价物料
  itemApplyPriceUpdate: (data = {}) => {
    //保存定价物料
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/price/update`, data)
  },
  //保存配额
  saveQuota: (data = {}) => {
    //保存定价物料
    return API.post(`${PROXY_SOURCING}/tenant/pointQuota/saveQuota`, data)
  },
  itemApplyAttrQuery: (data = {}) => {
    //查询属性值
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/attr/query`, data)
  },
  itemApplyAttrSave: (data = {}) => {
    //保存属性值
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/attr/save`, data)
  },
  itemApplyPriceCalculate: (data = {}) => {
    //根据公式计算价格
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/price/calculate`, data)
  },
  itemApplyPriceDelete: (data = {}) => {
    //删除定价物料
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/price/delete`, data)
  },

  itemApplySubmit: (data = {}) => {
    //提交系列物料定价单
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/submit`, data)
  },
  itemApplySeriesDelete: (data = {}) => {
    //删除系列物料
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/series/delete`, data)
  },
  itemApplyDelete: (data = {}) => {
    //删除系列物料定价单
    return API.post(`${PROXY_SOURCING}/tenant/series/item/apply/delete`, data)
  },
  // 下载导入模板
  downloadImportTemplate: (params = {}) =>
    API.get(`/price/tenant/pricerecord/excelTemplate`, params, {
      responseType: 'blob'
    }),
  /**
   * Excel导入
   * @returns
   */
  importItem: (params = {}) =>
    API.post(
      `price/tenant/pricerecord/excelImport?itemCode=${params.itemCode}&itemName=${params.itemName}`,
      params.data,
      {
        responseType: 'blob'
      }
    ),
  // 获取OA跳转链接
  getOaLink: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/series/item/getOaLink`, data),

  /** 附件 */
  // 获取文件节点信息
  getFileNodeList: (params = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/series/item/queryFileNodeByRfxId`, params),
  // 获取文件列表
  getFileList: `${PROXY_SOURCING}/tenant/series/item/queryFileBypointId`,
  getFileListFunc: (params = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/series/item/queryFileBypointId`, params),
  // 保存文件信息
  saveFileList: (params = {}) => API.put(`${PROXY_SOURCING}/tenant/series/item/tvSaveFile`, params)
}

export default {
  NAME,
  APIS
}
