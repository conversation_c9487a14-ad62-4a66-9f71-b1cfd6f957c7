/********** 信息记录确认 ***********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'infoRecordConfirm'
const APIS = {
  // 报价明细列表
  getBiddingInfoListUrl: `${PROXY_SOURCING}/tenant/rfx/pricing/biddingItemInfoList`,
  // 信息记录列表
  getInfoRecordListUrl: `${PROXY_SOURCING}/tenant/rfx/pricing/selectBiddingItemInfoAppend`,
  // 审核
  auditBiddingInfo: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/managerReviewInfoAppend`, data)
  }
}

export default {
  NAME,
  APIS
}
