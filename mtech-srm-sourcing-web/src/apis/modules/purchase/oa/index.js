/**
 * common/base
 */

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'purchaseOa'
const APIS = {
  /**
   * 导入excel
   * @returns
   */
  importExcel: (params) =>
    API.post(`${PROXY_SOURCING}/tenant/basic/price/importExcel`, params.data, {
      responseType: 'blob'
    }),
  /**
   * 导出excel
   * @returns
   */
  exportExcel: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/basic/price/export`, data, {
      responseType: 'blob'
    }),
  /**
   * 删除
   * @returns
   */
  deleteExcel: (data) => API.get(`${PROXY_SOURCING}/tenant/basic/price/delete`, data),
  /**
   * 新增/编辑
   * @returns
   */
  saveExcel: (data) => API.post(`${PROXY_SOURCING}/tenant/basic/price/save`, data)
}

export default {
  NAME,
  APIS
}
