import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'notice'
const APIS = {
  /**
   * 公告保存
   * @returns
   */
  noticeSave: (data) => API.post(`${PROXY_SOURCING}/tenant/portal/announce/save`, data),

  /**
   * 公告发布
   * @returns
   */
  noticePublish: (data) => API.post(`${PROXY_SOURCING}/tenant/portal/announce/publish`, data),
  /**
   * 公告撤回
   * @returns
   */
  noticeRevoke: (data) => API.post(`${PROXY_SOURCING}/tenant/portal/announce/revoke`, data),

  /**
   * 公告删除
   * @returns
   */
  noticeDel: (data) => API.post(`${PROXY_SOURCING}/tenant/portal/announce/delete`, data),

  /**
   * 获取公告数据
   * @returns
   */
  queryAnnounce: (data) => API.post(`${PROXY_SOURCING}/tenant/portal/announce/detail`, data),
  /**
   * 导入excel
   * @returns
   */
  importExcel: (params) =>
    API.post(`${PROXY_SOURCING}/tenant/basic/price/importExcel`, params.data, {
      responseType: 'blob'
    }),
  /**
   * 导出excel
   * @returns
   */
  exportExcel: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/basic/price/export`, data, {
      responseType: 'blob'
    }),
  /**
   * 删除
   * @returns
   */
  deleteExcel: (data) => API.get(`${PROXY_SOURCING}/tenant/basic/price/delete`, data)
}

export default {
  NAME,
  APIS
}
