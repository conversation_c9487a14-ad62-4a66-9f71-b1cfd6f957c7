import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'nuclearPrice'

const APIS = {
  /*
    寻源文件-----定价管理
  */

  //删除
  deleteHeader: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/setPriceHeader/deleteHeader`, data),
  //详情
  queryDetail: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/setPriceHeader/queryDetail`, data),
  //合同模板
  initContractPrint: (data = {}) =>
    API.post(`/srm-purchase-execute/tenant/contractPrintConfig/initContractPrint`, data),
  //根据物料查值
  queryQuotaPrice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/setPriceHeader/queryQuotaPrice`, data),
  //列表 --  提交
  submit: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/setPriceHeader/submit`, data),
  //详情  保存 --  提交
  saveSetPriceHeader: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/setPriceHeader/saveSetPriceHeader`, data),
  //采购组织
  getBusinessOrganizationByOrgId: (data = {}) =>
    API.get(
      `/masterDataManagement/tenant/business-organization/getBusinessOrganizationByOrgId?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    )
}
export default {
  NAME,
  APIS
}
