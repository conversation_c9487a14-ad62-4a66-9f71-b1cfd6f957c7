import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'constraintExclude'
const APIS = {
  /*
    配额管理接口
  */

  // 放行配额-列表查询
  getConstraintExcludeList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/queryBuilder`, data),

  // 放行配额-新增编辑
  saveConstraintExclude: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/save`, data),

  // 放行配额-提交
  submitConstraintExclude: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/commit`, data),

  // 放行配额-删除
  batchDeleteConstraintExclude: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/batchDelete`, data),

  // 放行配额-导出
  exportConstraintExclude: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/excelExport`, data, {
      responseType: 'blob'
    }),

  // 放行配额-导入
  importConstraintExclude: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/excelImport`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },

  // 放行配额-导入模板下载
  exportConstraintExcludeTemplate: (data) =>
    API.get(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/excelTemplate`, data, {
      responseType: 'blob'
    }),

  // 放行配额-查询详情
  getConstraintExcludeDetail: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/query`, data),

  // 放行配额-附件删除
  getConstraintExcludefileDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/fileDelete`, data),

  // 放行配额-附件查询
  getConstraintExcludefileQuery: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/fileQuery`, data),

  // 获取允许操作的公司信息
  getPermissionCompanyList: (data) =>
    API.post(`/masterDataManagement/tenant/permission/company`, data),

  // 查询明细分页
  getConstraintExcludeItemList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/detail/queryList`, data),

  // 批量删除明细行
  batchDeleteConstraintExcludeItemList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraintExclude/detail/batchDelete`, data)
}

export default {
  NAME,
  APIS
}
