import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'exclude'
const APIS = {
  /*
    配额管理接口
  */

  // 配额例外-列表查询
  getExcludeList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/queryBuilder`, data),

  // 配额例外-新增编辑
  saveExclude: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/save`, data),

  // 配额例外-提交
  submitExclude: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/commit`, data),

  // 配额例外-删除
  batchDeleteExclude: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/batchDelete`, data),

  // 配额例外-导出
  exportExclude: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/excelExport`, data, {
      responseType: 'blob'
    }),

  // 配额例外-导入
  importExclude: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/excelImport`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },

  // 配额例外-导入模板下载
  exportExcludeTemplate: (data) =>
    API.get(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/excelTemplate`, data, {
      responseType: 'blob'
    }),

  // 配额例外-查询详情
  getExcludeDetail: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/query`, data),
  // 绩效组织架构设置 - 获取公司层级下的架构tree数据
  // getStructureTreeOrgForSpecial: (data) =>
  //   API.post(`/masterDataManagement/analysisVirtualOrg/query/treeOrgForSpecial`, data),

  // 配额例外 - 查询明细分页
  getExcludeItemList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/detail/queryList`, data),

  // 配额例外 - 批量删除明细行
  batchDeleteExcludeItemList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/detail/batchDelete`, data),

  // 配额例外-生效
  batchEffectiveExclude: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/batchEffective`, data),

  // 配额例外-失效
  batchInvalidExclude: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/exclude/batchInvalid`, data)
}

export default {
  NAME,
  APIS
}
