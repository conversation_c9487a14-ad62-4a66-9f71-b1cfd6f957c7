import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'ranking'
const APIS = {
  /*
    配额管理接口
  */

  // 综合排名优先级配置查询-列表查询
  getCustomizationList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/ranking/queryBuilder`, data),
  // 根据权限获取有权限的工厂
  getPermissionSiteList: (data) =>
    API.post(`/masterDataManagement/tenant/permission/departments`, data),
  // 综合排名优先级配置查询-删除
  batchDeleteCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/ranking/batchDelete`, data),
  // 综合排名优先级配置查询-生效
  batchEffectiveCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/ranking/batchEffective`, data),

  // 综合排名优先级配置查询-失效
  batchInvalidCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/ranking/batchInvalid`, data),
  // 综合排名优先级配置查询-新增编辑
  saveCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/ranking/batchSave`, data),
  // 综合排名优先级配置查询-导出
  exportExcel: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/ranking/excelExport`, data, {
      responseType: 'blob'
    }),
  // 综合排名优先级配置查询-导入
  importExcel: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/ranking/excelExport`, data)
}

export default {
  NAME,
  APIS
}
