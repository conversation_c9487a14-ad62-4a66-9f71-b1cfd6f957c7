// import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
import { API } from '@mtech-common/http'
const NAME = 'quotaAgreement'
const APIS = {
  /*
    协议配额接口
  */
  //列表查询
  queryBuilder: `${PROXY_PRICEING}/tenant/quotaStrategy/agreement/queryBuilder`,

  // 批量删除
  batchDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/batchDelete`, data),

  // 导出
  excelExport: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/excelExport`, data, {
      responseType: 'blob'
    })
  },
  // 公司下拉数据
  getPermissionCompanyList: (data) =>
    API.post(`/masterDataManagement/tenant/permission/company`, data),

  // 详情查询
  getDetails: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/query`, data),

  // 查询明细分页
  queryList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/detail/queryList`, data),
  // 导入历史协议列表
  queryHistory: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreementDetail/queryHistory`, data),
  // 工厂下拉数据
  sites: (data) =>
    API.post(
      `/masterDataManagement/tenant/permission/sites?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    ),

  // 导入
  excelimport: (data = {}) => {
    return API.post(
      `${PROXY_PRICEING}/tenant/quotaStrategy/agreement/excelDetailImport?headerId=${data.get(
        'headerId'
      )}`,
      data,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
        responseType: 'blob'
      }
    )
  },
  // 导入模板
  exportTpl: (data = {}) => {
    return API.get(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/excelDetailTemplate`, data, {
      responseType: 'blob'
    })
  },

  // 导出明细
  itemExport: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/excelDetailExport`, data, {
      responseType: 'blob'
    })
  },
  // 批量删除明细行
  detailBatchDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/detail/batchDelete`, data),

  // 新增编辑的保存
  save: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/batchSave`, data),

  // 新增编辑的提交审批
  commit: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/commit`, data),
  // 协议配额-附件删除
  getFileDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/agreement/fileDelete`, data)
}

export default {
  NAME,
  APIS
}
