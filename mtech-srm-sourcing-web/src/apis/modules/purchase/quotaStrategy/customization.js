import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'customization'
const APIS = {
  /*
    配额管理接口
  */

  // 自制配额-列表查询
  getCustomizationList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/customization/queryBuilder`, data),

  // 自制配额-新增编辑
  saveCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/customization/save`, data),

  // 自制配额-删除
  batchDeleteCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/customization/batchDelete`, data),

  // 自制配额-导出
  exportCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/customization/excelExport`, data, {
      responseType: 'blob'
    }),

  // 自制配额-导入
  importCustomization: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/customization/excelImport`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },

  // 自制配额-导入模板下载
  exportCustomizationTemplate: (data) =>
    API.get(`${PROXY_PRICEING}/tenant/quotaStrategy/customization/excelTemplate`, data, {
      responseType: 'blob'
    }),

  // 自制配额-生效
  batchEffectiveCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/customization/batchEffective`, data),

  // 自制配额-失效
  batchInvalidCustomization: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/customization/batchInvalid`, data),

  // 根据权限获取有权限的工厂
  getPermissionSiteList: (data) =>
    API.post(
      `/masterDataManagement/tenant/permission/sites?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    ),

  // new-根据权限获取有权限的工厂
  queryUserPermission: (data) => API.post(`/iam/feign/permission-data/queryUserPermission`, data),

  // 根据权限获取有权限的工厂 -- 不允许直调feign接口因此替换为该接口
  getOrgListByCode: (params) =>
    API.get(`/analysis/tenant/buyer/assess/comprehensiveResult/getOrgListByCode`, params)
}

export default {
  NAME,
  APIS
}
