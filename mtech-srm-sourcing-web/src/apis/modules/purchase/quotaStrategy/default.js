import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'quotaDefault'
const APIS = {
  /*
    预设配额接口
  */
  //列表查询
  queryBuilder: `${PROXY_PRICEING}/tenant/quotaStrategy/default/queryBuilder`,

  // 导出
  excelExport(data = {}) {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/default/excelExport`, data, {
      responseType: 'blob'
    })
  },

  // 事业部查询
  queryOrg: (data) => API.post(`/masterDataManagement/tenant/permission/departments`, data),

  // 批量删除
  batchDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/default/batchDelete`, data),

  // 详情查询
  getDetails: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/default/query`, data),

  // 查询明细分页
  queryList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/default/detail/queryList`, data),

  // 批量删除明细行
  detailBatchDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/default/detail/batchDelete`, data),

  // 新增编辑的保存
  save: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/default/save`, data),

  // 新增编辑的提交审批
  commit: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/default/commit`, data)
}

export default {
  NAME,
  APIS
}
