import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'constraint'
const APIS = {
  /*
    限制配额接口
  */
  //列表查询
  queryBuilder: `${PROXY_PRICEING}/tenant/quotaStrategy/constraint/queryBuilder`,

  // 导出
  excelExport(data = {}) {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/excelExport`, data, {
      responseType: 'blob'
    })
  },

  // 事业部查询
  queryOrg: (data) => API.post(`/masterDataManagement/tenant/permission/departments`, data),

  // 品类主数据查询
  queryCategory: (data) =>
    API.post(`/masterDataManagement/tenant/permission/queryCategories`, data),

  // 批量删除
  batchDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/batchDelete`, data),

  // 详情查询
  getDetails: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/query`, data),

  // 查询明细分页
  queryList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/detail/queryList`, data),

  // 批量删除明细行
  detailBatchDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/detail/batchDelete`, data),

  // 品类明细查询
  queryCategoryList: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/category/queryList`, data),
  // 品类明细查询
  categoryBatchDelete: (data) =>
    API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/detail/category`, data),

  // 品类导入
  excelimport: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/excelCategoryImport`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 导入模板下载
  exportTpl: (data = {}) => {
    return API.get(
      `${PROXY_PRICEING}/tenant/quotaStrategy/constraint/excelCategoryTemplate`,
      data,
      {
        responseType: 'blob'
      }
    )
  },

  // 新增编辑的保存
  save: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/save`, data),

  // 新增编辑的提交审批
  commit: (data) => API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/constraint/commit`, data)
}

export default {
  NAME,
  APIS
}
