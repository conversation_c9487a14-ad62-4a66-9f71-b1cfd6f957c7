import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'quotaPriority'
const APIS = {
  /*
    限制配额优先级配置
  */
  // 事业部查询
  getOrgList: () => {
    return API.post(`/masterDataManagement/tenant/permission/departments`)
  },
  // 列表数据查
  getPriorityList: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/priority/queryBuilder`, data)
  },
  // 新增编辑保存
  savePriority: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/priority/batchSave`, data)
  },
  // 删除
  delPriority: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/priority/batchDelete`, data)
  },
  // 生效
  enablePriority: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/priority/batchEffective`, data)
  },
  // 失效
  disabledPriority: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/priority/batchInvalid`, data)
  },
  // 导入
  excelImport: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/priority/excelImport`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 导入模板下载
  excelTemplate: (data) => {
    return API.get(`${PROXY_PRICEING}/tenant/quotaStrategy/priority/excelTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导出
  excelExport: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quotaStrategy/priority/excelExport`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
