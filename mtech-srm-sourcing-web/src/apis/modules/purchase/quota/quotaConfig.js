import { API } from '@mtech-common/http'
import { PROXY_PRICEING, PROXY_MASTER_DATA } from 'CONFIG/proxy.config'
const NAME = 'quotaConfig'
const DEFAULTPARAM = {
  condition: '',
  page: {
    current: 1,
    size: 1000
  },
  pageFlag: false
}
const APIS = {
  /*
    配额管理接口
  */
  //配额管理申请列表
  getQuotaApplyList: `${PROXY_PRICEING}/tenant/quota/query/page`,
  // 添加配额管理申请
  saveQuotaApply: (data = {}) => API.post(`${PROXY_PRICEING}/tenant/quota/newSave`, data),
  // 查询配额
  addQuotaApply: (data = {}) => API.post(`${PROXY_PRICEING}/tenant/quota/add`, data),
  // 查询配额
  getQuotaApplyDetail: (data = {}) => API.get(`${PROXY_PRICEING}/tenant/quota/query`, data),
  // 查询配额
  getQuotaDetailHead: (data = {}) => API.get(`${PROXY_PRICEING}/tenant/quota/queryHead`, data),
  // 编辑
  modifyQuotaApply: (data = {}) => API.post(`${PROXY_PRICEING}/tenant/quota/modify`, data),
  // 提交
  submitQuotaApply: (data = {}) => API.post(`${PROXY_PRICEING}/tenant/quota/sync/oa`, data),
  // 删除配额申明细
  deleteQuotaDetail: (data = {}) => API.post(`${PROXY_PRICEING}/tenant/quota/deleteItem`, data),
  getAntdQuotaDetailList: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/quota/query/item/queryItemListPage`, data),
  getQuotaDetailList: `${PROXY_PRICEING}/tenant/quota/query/item/page`,
  // 提交
  queryQuotaDetailList: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/quota/query/item/page`, data),
  // 同步SAP
  pricerecordQuota: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/pricerecord/tcl/sync/quota`, data),
  // 同步SCM
  pricerecordQuotaSyncScm: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/pricerecord/tcl/sync/scm/quota`, data),
  // 取消
  quotaCance: (data = DEFAULTPARAM) =>
    API.post(`${PROXY_PRICEING}/tenant/quota/sync/oa/cancel`, data),
  // 查询供应商
  getSupplierList: (data = { supplierCode: '' }) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/supplier/criteria-query`, data),
  // 查询单位
  pagedQueryUnit: (data = DEFAULTPARAM) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/unit/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },
  // 导入
  excelimport: (data = DEFAULTPARAM) => {
    return API.post(`${PROXY_PRICEING}/tenant/quota/excel/import`, data.data, {
      responseType: 'blob'
    })
  },
  /**
   * 导入模板下载
   */
  exportTpl: () =>
    API.post(
      `${PROXY_PRICEING}/tenant/quota/export`,
      {},
      {
        responseType: 'blob'
      }
    ),

  /**
   * 导出配额明细
   * @param {*} data
   */
  itemExport: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quota/item/itemExport?BU_CODE=${localStorage.getItem('currentBu')}`, data, {
      responseType: 'blob'
    })
  },

  // 查询工厂
  getSiteList: (data = DEFAULTPARAM) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/site/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`, data),

  // 查询物料
  getItemListUrls: `${PROXY_MASTER_DATA}/tenant/supplier/paged-query`,

  //批量查询采购视图基础信息详情
  basicDetails: (data = DEFAULTPARAM) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/item-purchasing/basic-details`, data),

  //采购单位
  basicDetail: (data = DEFAULTPARAM) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/item-purchasing/basic-detail`, data),

  //配额申请明细分页查询
  applyItemPage: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/quota/query/apply/item/page`, data),
  //价格单位
  getByComCode: (data = {}) => API.get(`${PROXY_MASTER_DATA}//tenant/dict-item/getByComCode`, data),
  //配额申请明细导出
  applyItemExport: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/quota/apply/item/export`, data, {
      responseType: 'blob'
    }),
  // 删除配额申请单据
  delete: (data) => API.post(`${PROXY_PRICEING}/tenant/quota/delete`, data),
  //配额申请单据导出
  applyExport: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/quota/quota/export`, data, {
      responseType: 'blob'
    }),
  // 导入配额申请单据明细
  applyItemImport: (data = DEFAULTPARAM) => {
    return API.post(`${PROXY_PRICEING}/tenant/quota/apply/item/import`, data.data, {
      responseType: 'blob'
    })
  },
  //下载配额申请明细导入模板
  importTemplate: () =>
    API.post(
      `${PROXY_PRICEING}/tenant/quota/apply/item/importTemplate`,
      {},
      {
        responseType: 'blob'
      }
    ),
  // 导入
  excelimport2: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quota/apply/item/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 导入模板下载
  exportTpl2: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quota/apply/item/importTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 根据工厂+物料+供应商获取最新的包装量,起拆量，采购量
  getLastQuotaItem: (data = {}) => API.get(`${PROXY_PRICEING}/tenant/quota/getLastQuotaItem`, data),
  // 判断工厂是否属于空调事业部
  judgeIsKT: (data = {}) => API.get(`${PROXY_PRICEING}/tenant/quota/judgeIsKT`, data),
  // 历史配额明细数据查询
  getQuotaHistoryData: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quota/pageQueryHistoryQuotaList`, data)
  },
  // 保存配额历史数据
  postQuotaHistoryData: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/quota/saveQuotaItemDraft`, data)
  }
}

export default {
  NAME,
  APIS
}
