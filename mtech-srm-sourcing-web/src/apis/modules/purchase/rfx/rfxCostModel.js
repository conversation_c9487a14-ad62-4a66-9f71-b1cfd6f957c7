import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxCostModel'
const APIS = {
  /*
    询价-递归查询成本模型明细
  */
  queryCostModelItem: (params = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxCostModel/queryCostModelItem`, params)
  },
  /*
    询价-成本模型明细-新增行数据
  */
  saveItemData: (params = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant/rfxCostModel/saveItemData`, params)
  },
  /*
  询价-成本测算-查询末级节点数据
*/ queryLeafNodeData: (params = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxCostModel/queryLeafNodeData`, params)
  },
  /*
  询价-成本本测算明细-保存末级节点未启用明细数据价格
*/
  saveLeafNodePrice: (params = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant/rfxCostModel/saveLeafNodePrice`, params)
  },
  /*
  询价-成本本测算明细-查询成本模型供应商报价明细
*/
  querySupBidCostModelPrice: (params = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxCostModel/querySupBidCostModelPrice`, params)
  },
  /*
  询价-成本本测算明细-根据ID查询明细
*/
  findById: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/findById`, params)
  },

  // 采方-查询采购明细成本模型
  queryRfxItemCostModel: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/queryRfxItemCostModel`, params)
  },
  // 采方-保存采购明细成本模型
  saveRfxItemCostModel: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/saveRfxItemCostModel`, params)
  },

  // 采方-查询成本模型测算
  queryEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/queryEstimate`, params)
  },
  // 采方-查询历史成本模型测算
  queryHistoryEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/historyEstimate`, params)
  },
  // 采方-成本测算
  costEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/estimate`, params)
  },
  // 采方-保存成本测算
  saveEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/saveEstimate`, params)
  },
  // 采方-提交成本测算
  submitEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/commitEstimate`, params)
  },
  // 采方- 导入成本模型测算
  importEstimate: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfxItem/costModel/importItem?id=${data.id}`,
      data.data,
      {
        responseType: 'blob'
      }
    )
  },
  // 采方-导出成本模型测算
  exportEstimate: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxItem/costModel/exportItem`, data, {
      responseType: 'blob'
    }),
  // 采方-导出成本分析
  exportAnalysis: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxItem/costModel/exportCostPriceItem`, data, {
      responseType: 'blob'
    }),

  // 采方-查询默认历史成本分析
  queryDefaultCostModelAnalysis: (params = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfxItem/costModel/queryDefaultBiddingCostModelPrice`,
      params
    )
  },
  // 采方-查询默认历史成本测算
  queryDefaultCostModelEstimate: (params = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfxItem/costModel/queryDefaultCostModelEstimate`,
      params
    )
  },
  // 采方-分页查询历史成本分析报价
  queryHistoryCostModelPriceUrl: `${PROXY_SOURCING}/tenant/rfxItem/costModel/historyCostModelPricePage`,
  // 采方-分页查询历史成本测算价格
  queryHistoryCostModelEstimateUrl: `${PROXY_SOURCING}/tenant/rfxItem/costModel/historyCostModelEstimatePage`,
  // 采方-根据ID查询成本分析
  queryAnalysisById: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/queryCostModelPriceById`, params)
  },
  // 采方-根据ID查询成本测算
  queryEstimateById: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/queryEstimateById`, params)
  },
  // 采方-导出成本模型对比
  exportCostModelContrast: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/exportCostPriceItemContrast`, data, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
