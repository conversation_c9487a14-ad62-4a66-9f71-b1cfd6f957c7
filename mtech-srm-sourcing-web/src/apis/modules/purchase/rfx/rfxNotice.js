/**
 * 公示接口
 */
import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxNotice'

const APIS = {
  /**
   * 公示规则详情
   * @returns
   */
  getDetailRule: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxNotice/detailRule`, data),

  /**
   * 列出寻源中标数据列表
   * @returns
   */
  listRfxBiddingResult: `${PROXY_SOURCING}/tenant/rfxNotice/listRfxBiddingResult`,

  /**
   * 中标公示 - 导出
   * @returns
   */
  exportNotice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxNotice/export`, data, {
      responseType: 'blob'
    }),

  /**
   * 预览公示
   * @returns
   */
  previewNotice: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxNotice/previewBiddingNotice`, data, {
      responseType: 'blob'
    }),

  /**
   * 保存公示规则
   * @returns
   */
  saveRule: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxNotice/saveRule`, data),

  /**
   * 提交公示
   * @returns
   */
  submitNotice: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxNotice/submitNotice`, data)
}

export default {
  NAME,
  APIS
}
