import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxDetail'
const APIS = {
  // 根据ID查询询价单
  getRFXDetailById: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxHeader/findById`, data),

  // 询价大厅--对应的统计版块
  // 统计供应商
  getRFXSupInfo: `${PROXY_SOURCING}/tenant/rfx/statistic/sup/info`,

  // 实时竞价
  getRFXPriceInfo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/pur/price/info`, data),

  // 实时排名
  getRFXPriceRank: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/pur/price/rank`, data),

  // rfx轮次信息 --------------------------------------------------
  // 根据询价单id查询轮次信息
  getTurnList: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxRound/queryByRfxId`, data),

  /**
   * 查询当前发布中的轮次，如果不存在查询最后一轮
   */
  queryLastRoundByRfxId: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxRound/queryLastRoundByRfxId`, data),

  // 根据询价单id查询当前开起的轮次信息
  getCurrentTurn: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxRound/queryCurrentRoundByRfxId`, data),

  // 根据询价单id 获取倒计时信息
  getRfxCountdown: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxHeader/rfx-countdown`, data),

  // 单据模块接口-----------------------------------------------
  // 获取RFX模块定义
  getRFXModules: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfx/module/config`, data),

  // 获取物流动态配置字段
  getLogisticsDynamicConfig: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/dynamic/fields/config/query`, data),

  //获取扩展
  purOrgWithSite: (data = {}) => {
    return API.get(
      `${PROXY_SOURCING}/tenant/permission/purOrgWithSite?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    )
  },
  purAllOrgWithSite: (data = {}) => {
    return API.get(
      `${PROXY_SOURCING}/tenant/permission/getAllPurOrgWithSiteByCompanys?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    )
  },
  //根据rfxId判断是否需要推送
  checkPriceQuotaIsPush: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxHeader/checkPriceQuotaIsPush`, data)
  },

  //根据rfxId手动推送价格记录与配额
  syncPriceQuota: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxHeader/syncPriceQuota`, data)
  },
  //根据版本号获取组件
  queryItemBom: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/out/queryItemBom`, data)
  },
  //根据PCB获取物料行信息
  getMaterialInfoByPCB: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/getMaterialInfoByPCB`, data)
  },
  //非采设置商务投标时间
  settingTenderTime: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxHeader/settingTenderTime`, data)
  },
  //非采设置技术投标时间
  settingTecTime: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxHeader/settingTecTime`, data)
  },
  // 竞价大厅 - 查看脱敏数据
  checkDeliveryConfigInfo: (data = {}) =>
    API.get(`/srm-purchase-execute/common/dataDesensitize?desensitize=${data.key}`)
}

export default {
  NAME,
  APIS
}
