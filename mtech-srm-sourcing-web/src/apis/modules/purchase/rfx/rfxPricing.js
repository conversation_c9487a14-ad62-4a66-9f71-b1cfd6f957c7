import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxPricing'
const APIS = {
  // 报价明细 列表
  getRfxPricingList: `${PROXY_SOURCING}/tenant/rfx/pricing/queryBuilder`,
  getRfxPricingItemList: (url, data = {}) => API.post(`${url}`, data),
  checkPaymentCondition: (data = {}) =>
    API.get(`/masterDataManagement/tenant/supplier/detail`, data),
  //queryBuilder查询所有配额信息 列表
  getRfxItemQuotaList: `${PROXY_SOURCING}/tenant/rfxItemQuota/queryBuilder`,
  //历史报价列表
  getRfxPricingLists: `${PROXY_SOURCING}/tenant/rfx/pricing/queryHistoryBiddingItem`,
  //退回议价
  toEvaluationBid: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/pricing/toEvaluationBid`, data),
  //提交
  getRfxPricingSubmit: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/pricing/submitPricing`, data),
  // 保存定价
  getPricingSave: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/saveQuota`, data),
  //保存配额
  rfxItemQuotaSaveQuota: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItemQuota/saveQuota`, data),
  //根据询价大厅信息记录id弹框表单
  // getRFXDetailInformationById: `${PROXY_SOURCING}/tenant/rfx/pricing/selectBiddingItemInfoAppend`,
  getRFXDetailInformationById: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/selectBiddingItemInfoAppend`, data),

  //点击信息记录扩展button添加数据
  getRFXPricingextendById: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/viewBiddingItemForInfoAppend`, data),

  //点击信息记录保存数据
  getRFXPricingSave: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/saveInfoAppend`, data),

  //点击信息记录发布数据
  getRFXPricingPublish: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/publishInfoAppend`, data),

  //点击信息记录删除通过数据
  getRFXPricingEdit: (data = {}) =>
    API.post(`${PROXY_SOURCING}//tenant/rfx/pricing/deleteInfoAppend`, data),

  //点击信息记录审核通过数据
  getRFXPricingAudit: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/reviewInfoAppend`, data),

  //信息记录-导入sap-采方
  getRFXPricingSynchronous: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/syncInfoAppend`, data),

  //点击信息记录同步数据
  syncToSap: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/syncToSap`, data),

  // 开标一览表
  previewSign: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRound/openBiddingTable`, data, {
      responseType: 'blob'
    }),

  // 评分一览表
  previewGrade: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRound/scoreTable`, data, {
      responseType: 'blob'
    }),

  // 生成模具制造审批表
  createMoldBuildTable: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/pricing/applyProductDie`, data),

  // 提交定价校验
  submitPricingValid: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/pricing/submitPricingValid`, data),
  // 配额分配-导入
  quotaImport: (params = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfxItemQuota/importItemQuota?rfxId=${params.rfxId}`,
      params.data,
      {
        responseType: 'blob'
      }
    )
  },
  // 配额分配-导出
  quotaExport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItemQuota/exportItemQuota`, data, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
