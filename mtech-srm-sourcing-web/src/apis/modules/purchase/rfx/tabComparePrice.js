import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
import utils from '@/utils/utils'
const NAME = 'tabComparePrice'
const APIS = {
  /*
    询价大厅-比价Tab页-接口
  */
  /**
   * @description: 比价-queryBuilder查询比价信息
   * @param {*}
   * @return {*}
   */
  getComparePriceList: `${PROXY_SOURCING}/tenant/comp/query`,
  /**
   * @description: 比价-退回报价
   * @param {*}
   * @return {*}
   */
  doBackComparePrice: (data = {}) => {
    const query = utils.toQueryParams(data)
    return API.post(`${PROXY_SOURCING}/tenant/comp/back?${query}`)
  },
  //比价-退回报价-表单验证   404
  doBackComparePriceValid: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/comp/back-valid`, data)
  },
  /**
   * @description: 比价-提交审批
   * @param {*}
   * @return {*}
   */
  doSubmitComparePrice: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/comp/submitRecommend`, data)
  }
}

export default {
  NAME,
  APIS
}
