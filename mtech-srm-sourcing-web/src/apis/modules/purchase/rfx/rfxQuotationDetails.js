import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
import utils from '@/utils/utils'
const NAME = 'rfxQuotationDetails'
const APIS = {
  /*
    询价大厅-报价详情Tab页-接口
  */
  /**
   * @description: 比价-queryBuilder查询报价明细
   * @param {*}
   * @return {*}
   */
  queryBiddingItemDetail: `${PROXY_SOURCING}/tenant/rfxBidding/item/queryBiddingItemDetail`,
  queryBiddingItemDetailRequest(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/queryBiddingItemDetail`, data)
  },
  /**
   * @description: 报价详情导出
   * @param {*}
   * @return {*}
   */
  excelExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/export`, data, {
      responseType: 'blob'
    })
  },
  /**
   * @description: 比价-queryBuilder查询历史报价Url
   * @param {*}
   * @return {*}
   */
  queryHistoryBiddingItem: `${PROXY_SOURCING}/tenant/rfxBidding/item/queryHistoryBiddingItem`,
  /**
   * @description: 比价-queryBuilder查询历史报价
   * @param {*}
   * @return {*}
   */
  queryHistoryBiddingItemRequest: (data = {}) => {
    return API.post(`${PROXY_SOURCING}//tenant/rfxBidding/item/queryHistoryBiddingItem`, data)
  },
  /**
   * @description: 比价-退回报价
   * @param {*}
   * @return {*}
   */
  doBackComparePrice: (data = {}) => {
    const query = utils.toQueryParams(data)
    return API.post(`${PROXY_SOURCING}/tenant/comp/back?${query}`)
  },
  //比价-退回报价-表单验证   404
  doBackComparePriceValid: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/comp/back-valid`, data)
  },
  /**
   * @description: 比价-提交审批
   * @param {*}
   * @return {*}
   */
  doSubmitComparePrice: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/comp/submitRecommend`, data)
  },
  /**
   * @description: 比价-保存物流意见
   * @param {*}
   * @return {*}
   */
  saveSuggestion: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/rfxBidding/logistic/saveSuggestion`, data),
  /**
   * @description: 比价-同步TMS
   * @param {*}
   * @return {*}
   */
  syncTMS: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/logistics/tms/sync`, data),
  // 获取配置文件
  getLogisticsConfig: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBidding/detail/field`, data)
}

export default {
  NAME,
  APIS
}
