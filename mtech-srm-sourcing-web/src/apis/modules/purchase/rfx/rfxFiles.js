import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxFiles'

const APIS = {
  /*
    寻源文件接口
  */
  //根据rfxId查询所有文件节点信息
  queryPorFileNodeByRfxId: (params = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxHeader/queryFileNodeByRfxId`, params),

  //根据rfxId查询所有文件信息
  queryPorFileByRfxId: `${PROXY_SOURCING}/tenant/rfxHeader/queryFileByRfxId`,
  //根据ID删除
  deleteSourcingFileById: (data) =>
    API.put(`${PROXY_SOURCING}/tenant/sourcingFile/deleteById`, data),

  //保存文件信息
  saveRfxHeaderFile: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/saveFile`, data),

  /** 直接定价-附件相关接口 */
  // 获取文件节点信息
  getFileNodeListZjdj: (params = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/point2/queryFileNodeByRfxId`, params),
  // 获取文件列表
  getFileListZjdj: `${PROXY_SOURCING}/tenant/point2/queryFileBypointId`,
  // 保存文件信息
  saveFileListZjdj: (params = {}) => API.put(`${PROXY_SOURCING}/tenant/point2/tvSaveFile`, params)
}

export default {
  NAME,
  APIS
}
