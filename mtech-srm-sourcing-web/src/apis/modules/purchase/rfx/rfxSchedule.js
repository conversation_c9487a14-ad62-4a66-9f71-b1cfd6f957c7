import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxSchedule'
const APIS = {
  /*
    RFX主信息接口
  */
  /**
   * @description: 任务计划-获取数据   根据rfxId查询寻源阶段 use
   * @param {*}
   * @return {*}
   */
  queryPlanPhaseByRfxId: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxHeader/queryPlanPhaseByRfxId`, data)
  },
  /**
   * @description: 任务计划-获取数据   根据rfxId查询所有计划信息
   * @param {*}
   * @return {*}
   */
  queryPlanByRfxId: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxHeader/queryPlanByRfxId`, data)
  },
  backwardPushPlanByRfxId: (data = {}) => {
    //根据rfxId倒推寻源计划  use
    return API.put(`${PROXY_SOURCING}/tenant/rfxHeader/backwardPushPlanByRfxId`, data)
  },
  backwardPushPlanByRfxIdValid: (data = {}) => {
    //根据rfxId倒推寻源计划  表单验证
    return API.get(`${PROXY_SOURCING}/tenant/rfxHeader/backwardPushPlanByRfxId-valid`, data)
  },
  forwardPushPlanByRfxId: (data = {}) => {
    //根据rfxId正推寻源计划  use
    return API.put(`${PROXY_SOURCING}/tenant/rfxHeader/forwardPushPlanByRfxId`, data)
  },
  forwardPushPlanByRfxIdValid: (data = {}) => {
    //根据rfxId正推寻源计划  表单验证
    return API.get(`${PROXY_SOURCING}/tenant/rfxHeader/forwardPushPlanByRfxId-valid`, data)
  },
  saveRfxHeaderPlan: (data = {}) => {
    //保存计划信息  use
    return API.put(`${PROXY_SOURCING}/tenant/rfxHeader/savePlan`, data)
  },
  saveRfxHeaderPlanValid: (data = {}) => {
    //保存计划信息  表单验证
    return API.get(`${PROXY_SOURCING}/tenant/rfxHeader/savePlan-valid`, data)
  },
  savePlanInfoByTemplateId: (data = {}) => {
    //通过计划模板保存计划信息
    return API.put(`${PROXY_SOURCING}/tenant/rfxHeader/savePlanInfoByTemplateId`, data)
  },
  saveRfxHeaderPlanPhase: (data = {}) => {
    //保存计划阶段  use
    return API.put(`${PROXY_SOURCING}/tenant/rfxHeader/savePlanPhase`, data)
  },
  saveRfxHeaderPlanPhaseValid: (data = {}) => {
    //保存计划阶段  表单验证
    return API.get(`${PROXY_SOURCING}/tenant/rfxHeader/savePlanPhase-valid`, data)
  }
}

export default {
  NAME,
  APIS
}
