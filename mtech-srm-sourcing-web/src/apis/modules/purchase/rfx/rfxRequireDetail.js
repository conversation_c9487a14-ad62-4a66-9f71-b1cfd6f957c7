import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxRequireDetail'
const APIS = {
  // rfx明细信息 ------------------------------
  // 查询明细
  getRFXItemUrl: `${PROXY_SOURCING}/tenant/rfxItem/queryBuilder`,
  getRFXItem: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxItem/queryBuilder`, data),
  queryBuilder4ReviewPrice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBidding/queryBuilder4ReviewPrice`, data),
  // 核价导出
  exportCorePrice: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/export4ReviewPrice`, params, {
      responseType: 'blob'
    }),
  // 保存RFX单据模块信息
  saveRFXItem: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/module/save`, data),

  // 删除明细
  deleteRFXItem: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxItem/deleteById`, data),
  // 毛正文修改
  // 提交核价
  submitReviewPriceApi: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/submitReviewPrice`, data),

  // 保存核价
  saveReviewPriceApi: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/saveInfo4ReviewPrice`, data),

  // 选择明细
  saveREXItem: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxItem/saveByPorItem`, data),

  // 获取物料对应的图纸链接
  getDrawingUrl: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxItem/getDrawingUrl`, data),

  // 供方招标接口--------------------------------------------
  // 采购明细
  getSupplierTenderList: `${PROXY_SOURCING}/tenant/supplier/tender/items`,
  // 导入
  importRfxItem: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/importRfxItem?rfxId=${params.rfxId}`, params.data, {
      responseType: 'blob'
    }),
  // 导出
  exportRfxItem: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/exportRfxItem`, params, {
      responseType: 'blob'
    }),
  // 招标导出
  exportBiddingAndTenderRfxItem: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/exportBiddingAndTenderRfxItem`, params, {
      responseType: 'blob'
    }),
  // 招标导入
  importBiddingAndTenderRfxItem: (params = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/rfxItem/importBiddingAndTenderRfxItem?rfxId=${params.rfxId}`,
      params.data,
      {
        responseType: 'blob'
      }
    ),
  // 导出
  download: (params = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/series/item/template/download`, params, {
      responseType: 'blob'
    }),
  // 导入
  import: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/series/item/import?pointId=${params.pointId}`, params.data, {
      responseType: 'blob'
    }),
  // 供方采购明细导入
  supImportPriceTemplete: (params = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/supplier/tender/price/upload?rfxId=${params.rfxId}`,
      params.data,
      {
        responseType: 'blob'
      }
    ),
  // 供方采购明细导出模板
  supExportPriceTemplete: (params = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/supplier/tender/price/export-template?rfxId=${params.rfxId}&tabType=${params.tabType}`,
      params,
      {
        responseType: 'blob'
      }
    ),
  // 导出
  exportBuilder: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/exportBuilder`, params, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
