import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxSupplier'
const APIS = {
  // 查询所有品类 // 参数id
  getRFXCate: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxItem/queryAllCateByRfxId`, data),

  // rfx轮次供应商 ---------------------------------
  // 查询货源清单
  getRFXRoundSourceList: `${PROXY_SOURCING}/tenant/rfxRoundSupplier/queryBuilder`,

  // 查询供应商清单
  getRFXRoundDistincList: `${PROXY_SOURCING}/tenant/rfxRoundSupplier/distinctByQueryBuilder`,

  // 删除货源
  deleteRFXSource: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/deleteById`, data),

  // 删除供应商
  deleteRFXSupplier: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/deleteBySupplierId`, data),

  // 启用货源
  activeRFXSource: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/enableById`, data),

  // 禁用货源
  inActiveRFXSource: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/disableById`, data),

  // 启用供应商 (参数supplied)
  activeRFXSupplier: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/enableBySupplierId`, data),

  // 禁用供应商 (参数supplied)
  inActiveRFXSupplier: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/disableBySupplierId`, data),

  // 弹窗：货源清单
  chooseRFXSource: `${PROXY_SOURCING}/tenant/rfxRoundSupplier/queryItemSiteSource`,

  // 新增：保存货源
  saveRFXSource: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/save`, data),

  // 操作：更新供应商缴纳状态
  updateDeposit: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/updateDepositBySupplierId`, data),

  // 操作：更新供应商评审状态
  updateReview: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRoundSupplier/updateApproveBySupplierId`, data)
}

export default {
  NAME,
  APIS
}
