import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxSupRel'
const APIS = {
  // 资质审查查询审核规则
  getRfxSupRelListAdd: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/queryBuilder`, data),

  // 保证金缴纳
  earnestPass: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/earnestPass`, data),

  // 保证金退款
  earnestRefund: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/earnestRefund`, data),

  // 退款状态查询
  paymentStatus: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/examine/paymentStatus`, data),
  // 资质审查审批通过
  getPackageById: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/examinePass`, data),
  // 资质审核规则明细与供应商关联
  getRfxSupRelList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/queryExamineRel`, data),

  //资质审核规则明细
  // getRfxSupRelConfigs: `${PROXY_SOURCING}/tenant/examine/queryItem`,
  getRfxSupRelConfigs: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/examine/queryItem`, data),
  //资质审查明细与供方关联信息
  rfxSupRelList: `${PROXY_SOURCING}/tenant/examine/querySupRel`,
  // /资质审查审批拒绝
  rejectRfxSupRel: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/reject`, data),
  //  保存资质审查规则
  saveExamine: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/saveExamine`, data),
  //  保存采方审查意见
  editRfTeams: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/saveOpinion`, data),
  //采方审核通过
  examineReject: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/examine/examineReject`, data),
  getSupRelBidList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/examine/queryBuilder`, data),
  getrfxCainder: `${PROXY_SOURCING}/tenant/supplier/examine/queryItem`,
  getRfxSave: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/supplier/examine/saveHeaderRel`, data),

  getRfxSaveAdd: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/supplier/examine/saveItemRel`, data),
  getRfxSaveSubmit: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/supplier/examine/submitExamine`, data),

  /**
   * 开启-关闭资质审查
   */
  openExamine: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/examine/openExamine`, data),

  /**
   * 查询资质审查状态
   */
  queryExamineStatus: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/examine/queryExamineStatus`, data)
}

export default {
  NAME,
  APIS
}
