import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxDetailTabRatingRules'
const APIS = {
  // 查询评分规则
  ruleQuery: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfx/score/rule/query`, data),
  // 查询专家
  queryExpertInfo: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/score/rule/queryExpertInfo`, data),
  // 新增修改评分规则
  ruleSave: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/score/rule/save`, data),
  // 获取评分模板列表
  queryBuilder: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/score/model/rule/queryBuilder?rfxCode=${data.rfxCode}`),
  //查询评分模板详情
  ItemQuery: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfx/score/item/query`, data),
  //获取评分模板下拉
  getModeList: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/expert/score/mode-list`, data)
}
export default {
  NAME,
  APIS
}
