import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxTask'
const APIS = {
  // 结束技术投标
  closeTecBid: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/task/closeTecBid`, data),
  // 结束商务投标
  closeBusinessBid: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/task/closeBusinessBid`, data),
  // 结束供应商评审
  closeAudit: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/task/closeAudit`, data),
  // 结束供应商报名
  closeJoin: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfx/task/closeJoin`, data)
}

export default {
  NAME,
  APIS
}
