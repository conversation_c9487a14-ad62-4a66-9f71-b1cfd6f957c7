import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxExperttechnicalBid'
const APIS = {
  // rfx技术标管理评标明细
  getRfxExperttechnicalBid: `${PROXY_SOURCING}/tenant/rfx/bid/mgr/bid/skill/queryDetail`,
  // rfx技术标管理专家评分
  getRfxExpert: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/bid/mgr/expertScore/query`, data),

  // 投标明细专家明细
  getComputedStyle: `${PROXY_SOURCING}/tenant/rfx/bid/mgr/expertScore/queryDetail`,

  // 技术标查看评分
  getSaveRfxTexpert: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/bid/mgr/bid/skill/query/score`, data),
  //  技术标晋级提交按钮
  getRfxSubmitExpert: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/bid/mgr/bid/skill/promotion`, data),
  //商务标报价明细
  getrfxEcpertList: `${PROXY_SOURCING}/tenant/rfx/bid/mgr/bid/price/item
  `,
  //专家评分供应商维度
  getExpertScoreList: `${PROXY_SOURCING}/tenant/rfx/bid/mgr/expertScore/queryBySupplier`,
  // 供应商维度详情
  getExpertScoreAddList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/bid/mgr/expertScore/queryDetailBySupplier`, data),
  //专家评分供应商维度
  updatePromotion: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/bid/mgr/bid/update/promotion`, data),
  //商务标报价明细导出
  exportrfxEcpertList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/bid/mgr/bid/price/export`, data, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
