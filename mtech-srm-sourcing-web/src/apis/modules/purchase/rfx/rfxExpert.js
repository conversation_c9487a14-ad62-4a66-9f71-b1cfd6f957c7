/*
 * @Author: your name
 * @Date: 2022-03-11 15:48:35
 * @LastEditTime: 2022-03-12 13:23:09
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-sourcing-web\src\apis\modules\purchase\rfx\rfxExpert.js
 */
import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxExpert'
const APIS = {
  // rfx专家评分 列表
  getRfxExpertList: `${PROXY_SOURCING}/tenant/expert/score/scoring/list`,
  // rfx专家评分确认 列表
  getRfxExpertConfirmList: `${PROXY_SOURCING}/tenant/expert/score/confirm/list`,
  // rfx专家评分 列表
  exportRfxExpertList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/scoring/export`, data, {
      responseType: 'blob'
    }),
  // rfx专家评分商务标
  getRfxExpert: `${PROXY_SOURCING}/tenant/expert/score/query/list`,
  // 专家评分-专家评分汇总列表
  getExpertScoreSummaryQuery: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/summary/query`, data),
  getSupplierDimensionList: `${PROXY_SOURCING}/tenant/expert/score/summary/queryBySupplier`,
  // 商务标打分明细
  getSaveRfxTexpert: `${PROXY_SOURCING}/tenant/expert/score/query/score/detail`,
  //  商务标提交按钮
  getRfxSubmitExpert: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/expert/score/submit`, data),
  // 保存商务标
  getRfxAddSave: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/save/score/detail`, data),
  // 技术标附件接口
  getSkillFileListUrl: `${PROXY_SOURCING}/tenant/expert/score/query/skill/file/list`,
  // 专家评分附件 - 查询
  getScoreFileListUrl: `${PROXY_SOURCING}/tenant/expert/score/select/expert/scope/file/list`,
  // 专家评分附件 - 保存
  saveScoreFileList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/save/expert/scope/file`, data),
  // 专家评分附件 - 删除
  deleteScoreFileList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/delete/expert/scope/file`, data),
  getSkillFileList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/query/skill/file/list`, data),
  // 专家评分汇总确认评分
  getRfxAddSaveExpert: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/summary/confirm`, data),
  // 专家评分-专家评分汇总(明细)
  getExpertSummaryQueryDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/summary/query/detail`, data),
  //专家评分汇总重新评分
  expertScoreRescore: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/score/summary/rescore`, data),
  // 标的
  getExpertScore: `${PROXY_SOURCING}/tenant/expert/score/bid/item`,
  //开标
  getExpertAdd: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxRound/openBidding`, data),

  //校验供应商参与数量
  checkJoinQuantity: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfxHeader/checkJoinQuantity?rfxId=` + data),

  // 下发专家评分
  getExpertIssue: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/bid/mgr/expertScore/issueByRfxCode`, data),

  endExpertTechnicalIssue: ({ rfxId }) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRound/closeTecBid?rfxId=${rfxId}`),

  endExpertCommercialIssue: ({ rfxId }) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxRound/closeBusinessBid?rfxId=${rfxId}`),
  //投标技术标管理
  getExpertList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/bid/mgr/expertScore/query`, data),
  //专家申请管理接口    查询用户信息
  getExpertItemAdd: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/expert/apply/user/info/query`, data),

  findByRfxCode: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/expert/score/findByRfxCode`, data)
}

export default {
  NAME,
  APIS
}
