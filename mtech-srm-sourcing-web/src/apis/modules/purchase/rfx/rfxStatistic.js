/**
 * rfx/statistic
 */
import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'rfxStatistic'
const APIS = {
  /**
   * 采方大厅上方表格接口
   */
  purPriceTable: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/pur/price/table`, data),
  /**
   * 采方大厅上方表格排名接口
   */
  totalRanking: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/statistic/pur/price/totalRanking`, data),
  /**
   * 采方大厅明细排名接口
   */
  rank: (data = {}) =>
    API.post(`${PROXY_SOURCING}//tenant/rfx/statistic/pur/price/table/rank`, data)
}

export default {
  NAME,
  APIS
}
