import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'rfxList'

// 寻源项目 - 大厅
const APIS = {
  // rfx主信息接口  ------------------------------------
  // 查询询价单
  getRFXList: `${PROXY_SOURCING}/tenant/rfxHeader/queryBuilder?BU_CODE=${localStorage.getItem('currentBu')}`,

  // 导出 - 询价单
  exportRFXList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxHeader/export`, data, {
      responseType: 'blob'
    }),

  // 导出 - 直接定价
  exportRFXListZ: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/export?BU_CODE=TV`, data, {
      responseType: 'blob'
    }),

  // 导出 - 白电直接定价
  exportRFXListBD: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/export`, data, {
      responseType: 'blob'
    }),

  // 导出 - 系列物料定价
  exportRFXListX: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/series/item/apply/export`, data, {
      responseType: 'blob'
    }),

  // 获取用户配置字段
  getUserConfigFields: (data) => API.get(`${PROXY_SOURCING}/tenant/rfx/header/config`, data),
  // 获取询价对象合集
  getSourcingObjects: () => API.get(`${PROXY_SOURCING}/tenant/config/soucingobjs`),
  // 添加询价单
  addRfxHeader: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/save`, data),
  saveSort: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/strategyConfig/saveSort`, data),
  // 添加询价单--表单验证
  addRfxHeaderValid: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxHeader/save-valid`, data),
  // 光伏需求池-转寻源
  toSourcingApi: (data = {}) =>
    API.post(`srm-purchase-pv/tenant/pv/purchase/apply/toSourcing`, data),

  // 结束报价 即 结束当前轮次
  endNowTurn: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxRound/finishById`, data),

  // 提交定点
  submitRFXPoint: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxHeader/commitFixedPointById`, data),

  // 删除询价单
  deleteRFXHeader: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxHeader/deleteById`, data),

  // 关闭询价单
  closeRFXHeader: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxHeader/closeById`, data),
  // 管理员关闭询价单
  closeSourcing: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/adminOperation/closeByIdAdmin`, data),
  // 取消立项
  cancelProject: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxHeader/cancelById`, data),

  // 流标
  failBidById: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxHeader/failBidById`, data),

  // 复制新增
  copyAdd: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxHeader/copyAdd`, data),

  // 提交立项
  saveProject: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxHeader/projectCommit`, data),

  // 发布询价招标
  publishBid: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxHeader/publishById`, data),

  checkItemCodeSupplierCode: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/checkItemCodeSupplierCode`, data),

  // 激活询价单
  activeRFXHeader: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxHeader/activeById`, data),

  // 暂停询价单
  pauseRFXHeader: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxHeader/pauseById`, data),

  getStrategyConfigFields: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/strategy/detail`, data),

  //获取定价列表
  // getPagePoint: (data = {}) =>
  //   API.post(`${PROXY_SOURCING}/tenant/point2/pagePoint`, data),

  getPagePoint: `${PROXY_SOURCING}/tenant/point2/pagePoint`,
  getPagePointTv: `${PROXY_SOURCING}/tenant/point2/pagePoint?BU_CODE=TV`,

  // 查询-可参考的价格记录分页列表
  priceReferList: `${PROXY_SOURCING}/tenant/point2/price/refer/list`,
  getPriceReferList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/price/refer/list`, data),

  //获取定点推荐明细头部数据
  detailPoint: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/detailPoint`, data),

  //获取定点推荐明细头部数据
  detailPointTv: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/detailPoint?BU_CODE=TV`, data),

  //获取定点推荐明细表格数据
  pagePointItem: `${PROXY_SOURCING}/tenant/point2/pagePointItem`,

  //获取定点推荐明细表格数据
  pagePointItemTv: `${PROXY_SOURCING}/tenant/point2/pagePointItem?BU_CODE=TV`,

  // 获取定点配额分配明细表格数据
  pagePointQuota: `${PROXY_SOURCING}/tenant/pointQuota/queryBuilder?BU_CODE=TV`,

  //查询-基价均价明细分页列表
  pagePointDetailItem: `${PROXY_SOURCING}/tenant/point2/pageBasicAndAverage`,

  //删除定点主单列表接口
  deletePointList: (data = {}) =>
    API.delete(`${PROXY_SOURCING}/tenant/point2/deletePointList`, data),
  //删除定点主单列表接口
  deletePointListTV: (data = {}) =>
    API.delete(`${PROXY_SOURCING}/tenant/point2/deletePointList?BU_CODE=TV`, data),

  //提交定点主单接口
  submitPoint: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/point2/submitPoint`, data),
  //提交定点主单接口
  submitPointTv: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/point2/submitPoint?BU_CODE=TV`, data),

  //基价均价执行价的提交接口
  submitPricing: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/point2/submitPricing`, data),

  // 通过id校验是否可同步到sap
  preCheckSubmit: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/preCheckSubmit`, data),

  // 校验-未税价格与历史价格偏差
  preCheckUntaxedHistoricalPricesSubmit: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/point2/checkPrice`, data),

  // 校验-基价均价价差
  checkBasicAndAverage: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/checkBasicAndAverage`, data),

  //定点主单明细保存接口
  savePointItemList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/savePointItemList`, data),

  //定点主单明细保存接口
  savePointItemListTv: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/savePointItemList?BU_CODE=TV`, data),
  //定点配额保存接口
  savePointItemAllocationList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/savePointItemAllocationList`, data),
  // 配额分配保存接口
  savetvQuotaItemAllocationList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/pointQuota/savetvQuota?BU_CODE=TV`, data),
  // 定点配额导入
  pointBiddingImport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/pointQuota/pointQuotaImport`, data, {
      responseType: 'blob'
    }),
  // 定点配额导入
  pointBiddingImportTv: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/pointQuota/pointQuotaImport?BU_CODE=TV`, data, {
      responseType: 'blob'
    }),
  // 定点配额导出
  pointItemExport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/pointQuota/pointQuotaExport`, data, {
      responseType: 'blob'
    }),
  // 定点配额导出
  pointItemExportTv: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/pointQuota/pointQuotaExport?BU_CODE=TV`, data, {
      responseType: 'blob'
    }),
  // 定价物料导出
  pointItemDetailExport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/pointItemDetailExport`, data, {
      responseType: 'blob'
    }),
  // 定价物料导出
  pointItemDetailExportTv: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/pointItemDetailExport?BU_CODE=TV`, data, {
      responseType: 'blob'
    }),
  // 定价物料导入
  pointItemImport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/pointItemImport?BU_CODE=TV`, data, {
      responseType: 'blob'
    }),
  // 定价物料及报价导入模板下载
  pointItemImportTemplate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/pointItemImportTemplate?BU_CODE=TV`, data, {
      responseType: 'blob'
    }),
  // 电源板 - 定价物料导入
  powerPointItemImport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/powerPointItemImport?BU_CODE=TV`, data, {
      responseType: 'blob'
    }),
  // 电源板 - 定价物料及报价导入模板下载
  powerPointItemImportTemplate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/powerPointItemImportTemplate?BU_CODE=TV`, data, {
      responseType: 'blob'
    }),
  // 通用oa审批链接
  getSourceOaLink: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/rfxHeader/getSourceOaLink?BU_CODE=TV`, data),
  // 根据分组id查询定价物料和报价信息
  queryItemByGroupId: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/queryItemByGroupId?BU_CODE=TV`, data),
  //定点主单明细保存接口
  deletePointItemList: (data = {}) =>
    API.delete(`${PROXY_SOURCING}/tenant/point2/deletePointItemList`, data),
  //定点主单明细保存接口
  deletePointItemListTv: (data = {}) =>
    API.delete(`${PROXY_SOURCING}/tenant/point2/deletePointItemList?BU_CODE=TV`, data),

  //定点主单明细分页列表
  pageToBiddingItem: `${PROXY_SOURCING}/tenant/point2/pageToBiddingItem`,

  //查询阶梯报价列表定价
  ladderList: `${PROXY_SOURCING}/tenant/point2/item/stage/list`,

  //查询阶梯报价列表寻源
  ladderListV2: `${PROXY_SOURCING}/tenant/point2/item/stage/listV2`,

  //新增阶梯
  ladderListAdd: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/item/stage/add`, data),

  //删除阶梯
  ladderListdelete: (data = {}) =>
    API.delete(`${PROXY_SOURCING}/tenant/point2/item/stage/delete`, data),

  //保存定点推荐
  savePoint: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/savePoint`, data),

  //保存定点推荐
  savePointTv: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/savePoint?BU_CODE=TV`, data),

  //批量保存定点主单接口
  savePointBatch: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/savePointBatch`, data),

  // 生成RFX CODE
  getRfxCode: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxHeader/rfx-code`, data),

  //获取空调标识(废弃)
  getKtFlag: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/getKtFlag`, data),

  //根据公司编码获取空调标识
  supplierGetKtFlag: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/point2/supplierGetKtFlag`, data),

  // 获取定价详情页头部附件
  getFile: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/point2/getFile`, data),

  //保存定价详情页头部附件
  saveFiles: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/saveFiles`, data),

  //定点主单明细保存接口
  savePointExpand: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/savePointExpand`, data),

  //删除-定点扩展工厂明细
  deletePointExpandList: (data = {}) =>
    API.delete(`${PROXY_SOURCING}/tenant/point2/deletePointExpand`, data),
  //获取上次价格
  getLastPrice: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/getLastPrice`, data),
  //保存-执行价单据
  saveExercisePrice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/saveExercisePrice`, data),
  // 根据供应商编码获取币种税率
  getRfxCountdown: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/point2/rfx-countdown`, data),
  // 下载核价-均价模板
  getAverageTemplate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/averagePrice/exportTemplate`, data, {
      responseType: 'blob'
    }),
  //核价-均价Excel导入
  importAveragePrice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/averagePrice/import`, data, {
      responseType: 'blob'
    }),
  // 下载核价-基价模板
  getBaseTemplate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/basePrice/exportTemplate`, data, {
      responseType: 'blob'
    }),
  //核价-基价Excel导入
  importBasePrice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/basePrice/import`, data, {
      responseType: 'blob'
    }),
  // 下载核价-执行价模板
  getStrikeTemplate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/exercisePrice/exportTemplate`, data, {
      responseType: 'blob'
    }),
  //核价-执行价Excel导入
  importStrikePrice: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/exercisePrice/import`, data, {
      responseType: 'blob'
    }),
  // 导入sap
  syncSap: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/point2/syncSap`, data),
  //获取供应商报价属性报价方式
  getDefaultValue: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/getDefaultValue`, data),
  //获取供应商报价属性报价方式
  getDefaultValueTv: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/point2/getDefaultValue?BU_CODE=TV`, data),
  // 获取计划交货时间
  getPlanTime: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/point2/getPlanTime`, data),
  // 执行价成本模型
  queryCostModelItem: (params = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/point2/queryCostModelItem`, params)
  },

  // 价格合同导出
  excelExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/point2/price/export`, data, {
      responseType: 'blob'
    })
  },

  // 价格合同导入
  excelimport: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point2/priceContract/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 价格合同导入模板下载
  exportTpl: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/point2/priceContract/exportTemplate`, data, {
      responseType: 'blob'
    })
  },

  // 直接定价导出
  reFpExportTv(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/point2/price/export?BU_CODE=TV`, data, {
      responseType: 'blob'
    })
  },

  // 基价定价导出
  basePriceExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/point2/price/export`, data, {
      responseType: 'blob'
    })
  },

  // 均价定价导出
  averagePriceExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/point2/price/export`, data, {
      responseType: 'blob'
    })
  },

  // 导出基价均价明细列表
  basicAndAverageExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/point2/export/basicAndAverage`, data, {
      responseType: 'blob'
    })
  },

  // 执行定价导出
  strikePriceExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/point2/price/export`, data, {
      responseType: 'blob'
    })
  },

  // 执行定价错误信息导出
  exportErrorInfoList(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/point2/exercise/errorInfoList`, data, {
      responseType: 'blob'
    })
  },

  //价格合同获取价格属性
  getPropertyPrice: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/getPriceAttr`, data),

  // 入库未定价列表-导出
  exportNotPriced: (data = {}) =>
    API.post(`/srm-purchase-execute/tenant/po/in_out_record/exportNotPriced`, data, {
      responseType: 'blob'
    }),

  //校验采购组织与供应商关系
  checkSupAndPur: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/check/supAndPur`, data),

  // 直接定价-批量VMI标识
  batchSetVmi: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/batchSetVmi`, data),

  // 执行价 - 推送SAP
  sapUpload: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/point2/strikePriceSynSap`, data),

  // 白电非采 - 报价IP提示 - 首次进来
  noticeSameIpApi: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBidding/fc/noticeSameIp/${data.rfxId}`),

  // 白电非采 - 报价IP提示 - 轮询调度
  pollingNoticeSameIpApi: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBidding/fc/pollingNoticeSameIp/${data.rfxId}`),

  // 白电非采 - 报价IP提示 - 关闭弹窗
  cancelNoticeSameIpApi: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBidding/fc/cancelNoticeSameIp/${data.rfxId}`),
  // 淘汰供应商 - 更新
  updateEliminate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxBidding/eliminate/update`, data),
  // 淘汰供应商 - 淘汰提示校验
  checkEliminate: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxBidding/checkEliminate`, data)
}

export default {
  NAME,
  APIS
}
