import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
import utils from '@/utils/utils'
const NAME = 'inquiryBidding'
const APIS = {
  /*
    询价大厅-询价招标Tab页-接口
  */
  getRfxItemPackageByRfxId: (params = {}) => {
    //根据询价单id查询-品项/物料列表
    return API.get(`${PROXY_SOURCING}/tenant/rfxItemPackage/rfxId`, params)
  },
  getRfxItemSupplierList: (data = {}) => {
    //根据询价单id查询供应商列表
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/supplier`, data)
  },
  //RFX供方报价主信息接口
  doRfxBiddingAbate: (data = {}) => {
    //还价操作
    return API.put(`${PROXY_SOURCING}/tenant/rfxBidding/abate`, data)
  },
  doRfxBiddingAccept: (data = {}) => {
    //接收报价操作
    return API.put(`${PROXY_SOURCING}/tenant/rfxBidding/accept/new`, data)
  },
  setRfxBiddingBestPriceFlag: (rfxId = null, data = {}) => {
    //设置最优报价行
    return API.put(`${PROXY_SOURCING}/tenant/rfxBidding/bestPriceFlag?rfxId=${rfxId}`, data)
  },
  clearRfxBiddingBestPriceFlag: (rfxId = null, data = {}) => {
    //清理最优报价行
    return API.put(`${PROXY_SOURCING}/tenant/rfxBidding/clearBestPriceFlag?rfxId=${rfxId}`, data)
  },
  setRfxBiddingCompleteAllFlag: (data = {}) => {
    //完成全部标志   old  un-use
    return API.put(`${PROXY_SOURCING}/tenant/rfxBidding/completeAllFlag`, data)
  },
  getRfxBiddingItemHistory: (data = {}) => {
    const { page, ...queryObject } = data
    const query = utils.toQueryParams(queryObject)
    //按物料查询询价单历史价格明细项
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/history/queryall?${query}`, { page })
  },
  getRfxBiddingItemNewest: (data = {}) => {
    const { page, ...queryObject } = data
    const query = utils.toQueryParams(queryObject)
    //按物料查询询价单最新价格明细项
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/newest/query?${query}`, { page })
  },
  getRfxBiddingAcceptItem: `${PROXY_SOURCING}/tenant/rfxBidding/item/accept/query`,
  getRfxBiddingNewestItem: `${PROXY_SOURCING}/tenant/rfxBidding/item/newest/query`,
  getRfxBiddingItem: (data = {}) => {
    //queryBuilder查询询价单明细项   un-use
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/query`, data)
  },
  doRfxBiddingJoin: (data = {}) => {
    //供应商参与报价,供方调用   un-use
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/join`, data)
  },
  getRfxBiddingByQueryBuilder: (data = {}) => {
    //queryBuilder查询询价单   un-use
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/query`, data)
  },
  doRfxBiddingSubmit: (data = {}) => {
    //供应商参与报价,供方调用  un-use
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/submit`, data)
  },
  /**
   * @description: 结束当前轮次
   * @param {*}
   * @return {*}
   */
  currentTurnFinish: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxRound/finishById`, data)
  },

  //判断是否自动关闭
  checkIsCanAutoCloseRfx: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxBidding/checkIsCanAutoCloseRfx`, data)
  },
  /**
   * @description: 结束所有轮次  un-use
   * @param {*}
   * @return {*}
   */
  allTurnFinish: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxRound/finishAllRoundById`, data)
  },
  /**
   * @description: 开启下一轮
   * @param {*}
   * @return {*}
   */
  nextTurnStart: (data = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant/rfxRound/save`, data)
  },
  /**
   * @description: 发布
   * @param {*}
   * @return {*}
   */
  sendOut: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxRound/publishById`, data)
  },
  /**
   * @description: 退回报价
   * @param {*}
   * @return {*}
   */
  quoteReject: (data = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant/rfxBidding/reject`, data)
  },
  /**
   * @description: 结束议价
   * @param {*}
   * @return {*}
   */
  endAbate: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxRound/finishAbateById`, data)
  },
  /**
   * @description: 根据rfxId查询所有轮次   un-use
   * @param {*}
   * @return {*}
   */
  roundQueryAll: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfxRound/queryByRfxId`, data)
  },
  /**
   * @description: 开标
   * @param {*}
   * @return {*}
   */
  biddingOpen: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxRound/openBidding`, data)
  },
  /**
   * @description: 历史报价-查看历史
   * @param {*}
   * @return {*}
   */
  historyView: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxBidding/item/history/query`, data)
  },
  /*
    询价大厅-询价招标Tab页-接口
  */
  /**
   * @description: 询价招标-提交全部数据
   * @param {*}
   * @return {*}
   */
  doSubmitAllRfxFlag: (rfxId = null, roundId = null, data = null) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfxBidding/submitAllFlag?rfxId=${rfxId}&roundId=${roundId}`,
      data
    )
  }
}

export default {
  NAME,
  APIS
}
