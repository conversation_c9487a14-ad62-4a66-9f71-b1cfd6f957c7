/*
 * @Author: your name
 * @Date: 2022-03-11 15:48:35
 * @LastEditTime: 2022-03-12 15:35:20
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-sourcing-web\src\apis\modules\purchase\rfx\rfxExt.js
 */
import { API } from '@mtech-common/http'
import { PROXY_SOURCING, PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'rfxExt'
const APIS = {
  // 消息接口
  geRFXMessage: (params = {}) => API.get(`${PROXY_SOURCING}/tenant/rfx/msg`, params),

  // 查询操作日志
  getLogList: `${PROXY_SOURCING}/tenant/log/list`,

  // 根据rfxId查询说明信息
  getExplain: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxHeader/queryClobByRfxId`, data),

  // 提交说明信息
  submitExplain: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/saveClob`, data),

  // rfx打包询价 - 根据询价单id查询报价包  （物料列表）
  getPackageById: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/rfxItemPackage/rfxId`, data),
  // rfx寻源团队 列表
  getRfxTeamList: `${PROXY_SOURCING}/tenant/rfxHeader/queryTeamPage`,

  //根据账号查询人员信息
  findAccountInfo: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/sourcingTeam/findAccountInfo`, data),

  //  rfx寻源团队 保存提交
  saveRfxTeam: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/saveTeam`, data),
  //  rfx寻源团队 删除
  RfxTeamDelete: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/sourcingTeam/deleteById`, data),
  //  寻源团队专家抽取
  editRfTeam: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/samplingExpert`, data),

  // 选择的寻源专家 查询账号接口
  getAccoutAccessSor: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/expert/info/query/account`, data),
  // 选择的寻源专家 查询此类所有专家账号
  getAccountClassify: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/sourcingTeam/query/userInfoList`, data),
  // // 选择的寻源专家 查询此类所有专家账号
  // getAccountClassify: (data = {}) =>
  //   API.get(`${PROXY_SOURCING}/tenant/expert/info/query/accountClassify`, data),

  // 寻源团队--设置专家组长
  updateGroupLeader: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/sourcingTeam/groupLeader/update`, data),

  // 获取oa审批链接
  getOaLink: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/getOaLink`, data),

  //更新物料编码
  updateItemCodeByRfxItemId: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/updateItemCodeByRfxItemId`, data),

  //获取正式模具信息API
  queryRfxItemDie: `${PROXY_SOURCING}/tenant/rfxItem/die/query-record`,
  queryDiePriceRecord: `${PROXY_PRICEING}/tenant/pricerecord/queryDiePriceRecordPage`,

  //获取正式模具信息
  queryItemCodeByRfxItem: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/die/query-record`, data),
  //根据物料信息查询价格记录信息单条
  queryMoldByItemCode: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/pricerecord/queryDieHistoryPriceRecord`, data),
  //根据物料信息查询价格记录信息多条
  queryAllMoldByItemCode: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/pricerecord/queryDiePriceRecordPage`, data)
}

export default {
  NAME,
  APIS
}
