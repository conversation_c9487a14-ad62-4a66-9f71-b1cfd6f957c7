import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'configuration'

const APIS = {
  /*
    寻源文件-----核价配置
  */

  //保存 0 -- 提交 1
  saveQuotaHeader: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/saveQuotaHeader`, data),
  //列表--草稿状态提交
  submit: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/submit`, data),

  //详情
  queryDetail: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/queryDetail`, data),
  //删除
  deleteHeader: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/deleteHeader`, data),
  //公司
  findSpecifiedChildrenLevelOrgs: (data = {}) =>
    API.post(`/masterDataManagement/tenant/organization/findSpecifiedChildrenLevelOrgs`, data),
  //供应商
  criteriaQuery: (data = {}) =>
    API.post(`/masterDataManagement/tenant/supplier/criteria-query`, data),
  //工厂
  findSiteInfoByParentId: (data = {}) =>
    API.post(`/masterDataManagement/tenant/site/findSiteInfoByParentId?BU_CODE=${localStorage.getItem('currentBu')}`, data),
  // 物料
  itemfuzzyQuery: (data = {}) =>
    API.post(`/masterDataManagement/tenant/item-org-rel/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`, data),
  // 成本因子
  queryColumn: (data = {}) => API.post(`/sourcing/tenant/costModelItem/queryColumn`, data),
  // 列表表头信息
  queryColumnQuota: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/queryColumnQuota`, data),
  // 物料成本因子
  queryLeafNodeDataQuota: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/queryLeafNodeDataQuota`, data),
  // oa链接
  getQuotaOaLink: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/getQuotaOaLink`, data),

  // 查询-定额明细
  queryQuotaDetail: (data = {}) =>
    API.post(`/sourcing/tenant/quotaMaintenanceHeader/queryItem`, data),

  // 删除-定额明细
  deleteQuotaDetail: (data = {}) =>
    API.post(`/sourcing/tenant/quotaMaintenanceHeader/deleteItem`, data),

  // 保存-定额明细
  saveQuotaDetail: (data = {}) =>
    API.post(`/sourcing/tenant/quotaMaintenanceHeader/saveItem`, data),

  // 定额导出
  quotaCfgExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/export`, data, {
      responseType: 'blob'
    })
  },
  // 定额明细导出
  quotaCfgDetailExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/quotaMaintenanceItem/queryBuilder/export`, data, {
      responseType: 'blob'
    })
  },
  // 定额导入
  excelimport: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 定额导入模板下载
  exportTpl: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/quotaMaintenanceHeader/exportTemplate`, data, {
      responseType: 'blob'
    })
  },

  // 定价导出
  rulePriceCfgExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/point2/price/export`, data, {
      responseType: 'blob'
    })
  }
}
export default {
  NAME,
  APIS
}
