import { API } from '@mtech-common/http'
const NAME = 'assessManage'
const PROXY_BASE = '/analysis/tenant'
const PROXY_MASTER = '/masterDataManagement'
const APIS = {
  /*
    考核接口
  */
  // 保存考核类型
  saveClaimType: (data) => {
    return API.post(`${PROXY_BASE}/claimType/saveClaimType`, data)
  },
  listClaimAppealArbitrator: (data) => {
    return API.post(`${PROXY_BASE}/claimType/listClaimAppealArbitrator`, data)
  },
  // 工厂
  fuzzySiteQuery: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/site/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },
  // 启用-禁用考核类型
  changeStatus: (data) => {
    return API.patch(`${PROXY_BASE}/claimType/turnClaimTypeState`, data)
  },
  // 删除考核类型
  deleteRecord: (data) => {
    return API.delete(`${PROXY_BASE}/claimType/deleteClaimType`, data)
  },
  // 考核类型详情
  detailClaimType: (data) => {
    return API.post(`${PROXY_BASE}/claimType/detailClaimType`, data)
  },
  // 获取启用的考核类型列表
  getAvailableClaimType: () => API.get(`${PROXY_BASE}/claimType/listAvailableClaimType`),
  // 保存考核类型申诉仲裁人列表
  saveClaimAppealArbitrator: (data) => {
    return API.post(`${PROXY_BASE}/claimType/saveClaimAppealArbitrator`, data)
  },

  /*
    指标接口
  */
  // 保存指标
  saveClaimStand: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimStand/saveClaimStand`, data)
  },
  // 获取指标详情
  getIndexDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimStand/detailClaimStand`, data)
  },
  // 获取启用的考核指标列表
  listAvailableClaimStand: () => API.get(`${PROXY_BASE}/claimStand/listAvailableClaimStand`),
  // 启用-停用考核指标
  changeClaimStandStatus: (data) => {
    return API.patch(`${PROXY_BASE}/claimStand/turnClaimStandState`, data)
  },
  // 删除考核指标
  deleteClaimStand: (data) => {
    return API.delete(`${PROXY_BASE}/claimStand/deleteClaimStand`, data)
  },
  /*
    协议书模板接口
  */
  // 保存协议书模板
  saveClaimAgreeTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimAgreementTemplate/saveClaimAgreementTemplate`, data)
  },
  // 启用-停用协议书模板
  turnClaimAgreeTempState: (data) => {
    return API.patch(`${PROXY_BASE}/claimAgreementTemplate/turnClaimAgreementTemplateState`, data)
  },
  // 删除协议书模板
  deleteClaimAgreeTemp: (data) => {
    return API.delete(`${PROXY_BASE}/claimAgreementTemplate/deleteClaimAgreementTemplate`, data)
  },
  // 协议书模板详情
  detailClaimAgreeTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimAgreementTemplate/detailClaimAgreementTemplate`, data)
  },
  // 获取协议书模板列表
  listClaimAgreeTemp: () =>
    API.get(`${PROXY_BASE}/claimAgreementTemplate/listClaimAgreementTemplate`),
  /*
    考核单接口
  */
  // 考核单确认扣款
  deductAmount: (data) => {
    return API.patch(`${PROXY_BASE}/claim/deductAmount`, data)
  },
  // 新增考核单
  saveClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveClaim`, data)
  },
  // 考核单详情（分页列表编辑按钮）（待发布）
  detailSuperficialClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/detailSuperficialClaim`, data)
  },
  // 完善考核单（点击编号进入）
  completeClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/completeClaim`, data)
  },
  // 完善并提交考核单
  completeAndSubmitClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/completeAndSubmitClaim`, data)
  },
  // 删除考核单
  deleteClaim: (data) => {
    return API.delete(`${PROXY_BASE}/claim/deleteClaim`, data)
  },
  // 取消考核单
  cancelClaim: (data) => {
    return API.patch(`${PROXY_BASE}/claim/cancelClaim`, data)
  },
  // 考核单详情（待发布）
  detailClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/detailClaim`, data)
  },
  // 考核单申诉处理保存并提交审批
  saveAndSubmitClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveAndSubmitClaim`, data)
  },
  // 提交考核单
  submitClaim: (data) => {
    return API.patch(`${PROXY_BASE}/claim/submitClaim`, data)
  },
  // 考核单详情（考核单汇总-申诉处理）
  detailPublishedClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/detailPublishedClaim`, data)
  },
  // 获取有效的考核单列表
  listAvailableClaim: () => API.get(`${PROXY_BASE}/claim/listAvailableClaim`),
  /*------考核单申诉处理-------*/
  // 考核单申诉处理保存并提交审批
  saveAndSubmitClaimAppealDeal: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveAndSubmitClaimAppealDeal`, data)
  },
  // 考核单申诉处理保存
  saveClaimAppealDeal: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveClaimAppealDeal`, data)
  }
}

export default {
  NAME,
  APIS
}
