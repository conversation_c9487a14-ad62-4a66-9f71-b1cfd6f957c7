import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
// import utils from "@/utils/utils";
const NAME = 'whitePoint'
const APIS = {
  /*
    采方-保存-定价头信息
  */
  saveHeader: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/saveHeader`, data)
  },
  /*
    采方-查询-头信息分页列表
  */
  queryHeaderByPage: `${PROXY_SOURCING}/tenant/whitePoint/buyer/queryHeaderByPage`,

  /*
    queryBuilder查询定点
  */
  queryBuilder: `${PROXY_SOURCING}/tenant/pointQuota/queryBuilder`,
  /*
    采方-删除-头信息分页列表
  */
  deleteHeader: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/deleteHeader`, data)
  },
  /*
    采方-提交-下发确认(根据pointId)
  */
  sendToSupplier: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/sendToSupplier`, data)
  },
  /*
    采方-查看-头信息详情(根据pointId)
  */
  viewHeaderDetailById: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/viewHeaderDetailById`, data)
  },
  /*
    采方-查询-定价物料行分页列表(根据pointId)
  */
  queryItemByPage: `${PROXY_SOURCING}/tenant/whitePoint/buyer/queryItemByPage`,
  /*
    采方-保存-定价物料行
  */
  saveItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/saveItem`, data)
  },
  /*
    保存定点定价配额配额
  */
  saveItemQuota: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/pointQuota/saveQuota`, data)
  },
  /**
   * 采方-导出-物料行(根据pointId)
   * @returns
   */
  exportItem: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/exportItemPur`, data, {
      responseType: 'blob'
    }),
  /**
   * 采方-导出-配额(根据pointId)
   * @returns
   */
  exportItemQuota: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/exportItemQuota`, data, {
      responseType: 'blob'
    }),
  /**
   * 定价明细行Excel导入
   * @returns
   */
  importItem: (params = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/whitePoint/buyer/importItem?pointNo=${params.pointNo}`,
      params.data,
      {
        responseType: 'blob'
      }
    ),
  /**
   * 定价明细行配额Excel导入
   * @returns
   */
  importItemQuota: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/importItemQuota`, params.data, {
      responseType: 'blob'
    }),
  /**
   * 采方-提交OA审批(根据pointId)
   * @returns
   */
  submitOA: (params = {}) => API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/submitOA`, params),
  getOaLink: (params = {}) => API.get(`${PROXY_SOURCING}//tenant/whitePoint/getOaLink`, params),
  /**
   * 采方-删除-定价物料行
   * @returns
   */
  deleteItem: (params = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/deleteItem`, params),
  // 下载导入模板
  downloadImportTemplate: (params = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/whitePoint/downloadImportTemplate`, params, {
      responseType: 'blob'
    }),

  /**
   * 采方-根据docId查询所有文件节点信息
   * @returns
   */
  queryFileNodeByDocId: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/whitePoint/buyer/queryFileNodeByDocId`, data)
  },

  //采方文件-查询-附件分页列表(根据pointId)
  queryFileByPointId: `${PROXY_SOURCING}/tenant/whitePoint/buyer/queryFileByPointId`,

  /*
      采方-附件上传
    */
  saveFile: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/saveFile`, data)
  },
  /*
      采方-附件上删除
    */
  deleteFile: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/share/deleteFile`, data)
  },
  /*
      查询定价物料默认值
    */
  defaultValue: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/apply/price/defaultValue`, data)
  },
  /*
      查询供应商
    */
  supplierDropdownList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/supplierDropdownList`, data)
  },
  /*
      采方-保存-定价头工厂扩展信息
    */
  saveHeaderExpand: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/saveHeaderExpand`, data)
  },
  /*
      采方-重新推送价格
    */
  manualPushPriceAndQuota: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/manualPushPriceAndQuotaNew`, data)
  },
  // 采方 - 白电直接定价 - 关闭
  directPricing: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/whitePoint/buyer/closeHeader`, data)
  },
  // 采方 - 询价大厅 - 白电直接定价
  getOutSourceMethodList: () =>
    API.get(`${PROXY_SOURCING}/tenant/whitePoint/getOutsourceMethodList`)
}

export default {
  NAME,
  APIS
}
