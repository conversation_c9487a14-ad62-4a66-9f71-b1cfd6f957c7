import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'manpowerOutsourcing'
const APIS = {
  /*********** 人力外包定价-采方 **********/
  // 查询列表
  queryManOutPricingList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/buyer/query`, data)
  },
  // 新增
  addManOutPricing: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/buyer/save`, data)
  },
  // 删除
  deleteManOutPricing: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/buyer/delete`, data)
  },
  // 提交
  submitManOutPricing: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/buyer/submit`, data)
  },
  // 发布
  publishManOutPricing: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/buyer/publish`, data)
  },
  // 查看OA审批
  getOaLink: (data = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant/rfxHeader/getSourceOaLink`, data)
  },
  // 明细-保存
  saveManOutPricing: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/update/remark`, data)
  },
  // 人力工时单价tab-查询列表
  queryManHourUnitPriceList: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/hro/price/record/rel/query`, data)
  },
  // 人力工时单价tab-新增
  addManHourUnitPrice: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/price/record/rel/save`, data)
  },
  // 人力工时单价tab-删除
  deleteManHourUnitPrice: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/price/record/rel/delete`, data)
  },
  // 定价结算机型tab-查询列表
  queryPricingClearModelList: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/hro/point/settle/model/query`, data)
  },
  // 定价结算机型tab-新增
  addPricingClearModel: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/settle/model/save`, data)
  },
  // 定价结算机型tab-保存
  savePricingClearModel: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/settle/model/update/valid/time`, data)
  },
  // 定价结算机型tab-删除
  deletePricingClearModel: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/point/settle/model/delete`, data)
  },
  // 附件tab--根据rfxId查询文件节点信息
  getFileNodeList: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/hro/file/queryFileNodeByRfxId`, data),
  // 附件tab--根据rfxId查询文件列表
  getFileList: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/hro/file/queryFileBypointId`, data),
  // 附件tab--保存附件
  saveFile: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/hro/file/tvSaveFile`, data),

  /************** 价格记录-人力工时单价-适用机型范围 ************/
  // 查询列表
  queryModelRangeList: (data) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/suit/model/range/query`, data)
  },
  // 查询计算工时列表
  queryFormulaList: (data) => {
    return API.get(`${PROXY_SOURCING}/tenant/hro/calc/formula/query/all`, data)
  },
  // 保存
  saveModelRangeList: (data) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/suit/model/range/batch/save`, data)
  },
  // 删除
  deleteModelRangeList: (data) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/suit/model/range/batch/delete`, data)
  },
  // 下载导入模板
  downloadModelRangeTemplate: (data) => {
    return API.get(`${PROXY_SOURCING}/tenant/hro/suit/model/range/excel/export/template`, data, {
      responseType: 'blob'
    })
  },
  // 导入
  importModelRangeList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/hro/suit/model/range/excel/import`, data.data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
