import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'examineBaseItem'

const APIS = {
  /**
   * 查看-资质审查项详情
   */
  queryBuilder: `${PROXY_SOURCING}/tenant/examineBaseItem/queryBuilder`,

  /**
   * 新增或修改-资质审查项
   */
  save: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examineBaseItem/save`, data),

  /**
   * 删除-资质审查项目
   */
  delete: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examineBaseItem/delete`, data),

  /**
   * 查看-资质审查项详情
   */
  view: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/examineBaseItem/view`, data)
}

export default {
  NAME,
  APIS
}
