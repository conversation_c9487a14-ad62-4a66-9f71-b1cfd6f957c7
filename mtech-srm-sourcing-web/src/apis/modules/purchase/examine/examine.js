import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'examine'

const APIS = {
  /**
   * 查看-审查项目信息(根据供应商和审查项检索)
   */
  viewItemListV1: `${PROXY_SOURCING}/tenant/examine/viewItemListV1`,

  /**
   * 查看-审查项目信息(根据供应商和审查项检索)
   */
  postViewItemListV1: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/viewItemListV1`, data),

  /**
   * 查看-保证金缴纳信息列表
   */
  viewMoneyStatusListV1: `${PROXY_SOURCING}/tenant/examine/viewMoneyStatusListV1`,

  /**
   * 查看-保证金缴纳信息列表
   */
  postViewMoneyStatusListV1: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/viewMoneyStatusListV1`, data),

  /**
   * 保存采方审查意见
   */
  saveOpinion: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/saveOpinion`, data),

  /**
   * 保证金退款
   */
  earnestRefund: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/earnestRefund`, data),

  /**
   * 退款状态查询
   */
  paymentStatus: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/examine/paymentStatus`, data),

  /**
   * 审核通过
   */
  examinePass: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/examinePass`, data),

  /**
   * 审核不通过
   */
  examineReject: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/examine/examineReject`, data),

  /**
   * 审批-保证金缴纳明细
   */
  earnestPass: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/examine/earnestPass`, data),
  /**
   * 确认-保证金缴纳明细
   */
  earnestConfirm: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/examine/manualEarnestPass`, data),
  /**
   * 确认-保证金驳回接口
   */
  earnestReject: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/rejectEarnestMoney`, data),
  /**
   * 确认-保证金退回接口
   */
  earnestReturn: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/sendBackEarnestMoney`, data),
  /**
   * 保存-全量保存指定询原单下各供应商资质审查所有相关信息
   */
  bigSaveExamineForAllSupplier: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/bigSaveExamineForAllSupplier`, data),

  /**
   * 查看-资质审查详情
   * @param {*} data
   * @returns
   */
  viewItemListByExamineId: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/viewItemListByExamineId`, data),

  /**
   * 查看-资质审查列表信息
   * @param {*} data
   * @returns
   */
  pageQueryBuilder: `${PROXY_SOURCING}/tenant/examine/pageQueryBuilder`,
  /**
   * 导出-资质审查列表信息
   */
  exportQueryBuilder: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/examine/export`, data, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
