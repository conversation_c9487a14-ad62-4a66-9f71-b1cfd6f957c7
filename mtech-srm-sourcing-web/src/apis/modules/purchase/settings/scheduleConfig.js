import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'scheduleConfig'
const APIS = {
  /*
    寻源计划接口
  */
  deleteSourcingPlanById: (data = {}) => {
    //根据ID删除  use
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlan/deleteById`, data)
  },
  finishSourcingPlanById: (data = {}) => {
    //根据ID结束计划
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlan/finishPlanById`, data)
  },
  deleteSourcingPlanPhaseById: (data = {}) => {
    //根据ID删除阶段  use
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlan/phase/deleteById`, data)
  },
  getSourcingPlanByQueryBuilder: (data = {}) => {
    //queryBuilder查询寻源计划
    return API.post(`${PROXY_SOURCING}/tenant/sourcingPlan/queryBuilder`, data)
  },

  /*
    寻源计划模板接口
  */
  deleteSourcingPlanTemplateById: (data = {}) => {
    //任务计划模板--根据ID删除
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlanTemplate/deleteById`, data)
  },
  disableSourcingPlanTemplateById: (data = {}) => {
    //任务计划模板--禁用
    return API.post(`${PROXY_SOURCING}/tenant/sourcingPlanTemplate/disableById`, data)
  },
  enableSourcingPlanTemplateById: (data = {}) => {
    //任务计划模板--启用
    return API.post(`${PROXY_SOURCING}/tenant/sourcingPlanTemplate/enableById`, data)
  },
  deleteSourcingPlanTemplatePhaseById: (data = {}) => {
    //任务计划模板--根据ID删除阶段
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlanTemplate/phase/deleteById`, data)
  },
  saveSourcingPlanTemplatePhase: (data = {}) => {
    //任务计划模板--保存寻源计划模板阶段
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlanTemplate/phase/save`, data)
  },
  getSourcingPlanTemplateList: `${PROXY_SOURCING}/tenant/sourcingPlanTemplate/queryBuilder`,
  getSourcingPlanTemplateByQueryBuilder: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/sourcingPlanTemplate/queryBuilder`, data)
  },
  saveSourcingPlanTemplate: (data = {}) => {
    //任务计划模板--保存寻源计划模板
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlanTemplate/save`, data)
  },
  saveSourcingPlanTemplateValid: (data = {}) => {
    //任务计划模板--保存寻源计划模板-验证
    return API.get(`${PROXY_SOURCING}/tenant/sourcingPlanTemplate/save-valid`, data)
  },

  /*
    寻源计划模板明细接口
  */
  deleteSourcingPlanTemplateItemById: (data = {}) => {
    //根据ID删除
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlanTemplateItem/deleteById`, data)
  },
  getSourcingPlanTemplateItemByQueryBuilder: (data = {}) => {
    //queryBuilder查询计划模板明细
    return API.post(`${PROXY_SOURCING}/tenant/sourcingPlanTemplateItem/queryBuilder`, data)
  },
  querySourcingPlanTemplateItemByTemplateId: (data = {}) => {
    //根据templateId查询计划模板明细  use
    return API.post(`${PROXY_SOURCING}/tenant/sourcingPlanTemplateItem/queryByTemplateId`, data)
  },
  saveSourcingPlanTemplateItem: (data = {}) => {
    //保存计划模板明细
    return API.put(`${PROXY_SOURCING}/tenant/sourcingPlanTemplateItem/save`, data)
  },
  querySourcingPlanTemplateTemplateId: (data = {}) => {
    // 根据计划模板保存计划信息
    return API.put(`${PROXY_SOURCING}/tenant/rfxHeader/savePlanByTemplateId`, data)
  }
}

export default {
  NAME,
  APIS
}
