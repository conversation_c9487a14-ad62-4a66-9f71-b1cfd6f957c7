import { API } from '@mtech-common/http'
const NAME = 'Jig'
const APIS = {
  // 物料治具关系表-分页查询
  pageJigMaterialRelationshipApi: (data = {}) =>
    API.post(`/contract/tenant/fixture/buyer/pageQuery`, data),

  // 物料治具关系表-保存
  saveJigMaterialRelationshipApi: (data = {}) =>
    API.post(`/contract/tenant/fixture/buyer/save`, data),

  // 物料治具关系表-删除
  deleteJigMaterialRelationshipApi: (data = {}) =>
    API.post(`/contract/tenant/fixture/buyer/delete`, data),

  // 物料治具关系表-启用
  enableJigMaterialRelationshipApi: (data = {}) =>
    API.post(`/contract/tenant/fixture/buyer/enable`, data),

  // 物料治具关系表-停用
  disableJigMaterialRelationshipApi: (data = {}) =>
    API.post(`/contract/tenant/fixture/buyer/disable`, data),

  // 物料治具关系表-导入
  importJigMaterialRelationshipApi: (data = {}) =>
    API.post(`/contract/tenant/fixture/buyer/excelImport`, data, { responseType: 'blob' }),

  // 物料治具关系表-模板下载
  importDownloadJigMaterialRelationshipApi: (data = {}) =>
    API.get(`/contract/tenant/fixture/buyer/excelTemplate`, data, { responseType: 'blob' }),

  // 物料治具关系表-导出
  exportJigMaterialRelationshipApi: (data = {}) =>
    API.post(`/contract/tenant/fixture/buyer/excelExport`, data, { responseType: 'blob' }),
}

export default {
  NAME,
  APIS
}