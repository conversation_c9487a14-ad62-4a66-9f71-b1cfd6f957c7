import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'smtComponentItem'
const APIS = {
  /*
    smt组件物料
  */
  // 查询列表接口
  querySmtList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/smtComponentItemRel/pageQuery`, data)
  },

  // 保存接口
  saveSmtList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/smtComponentItemRel/commit`, data)
  },

  // 删除
  batchDelList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/smtComponentItemRel/batchDel`, data)
  },
  // 删除
  batchUpdateList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/smtComponentItemRel/batchDel`, data)
  },
  // 查询接口
  querySmtComponent: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/smtComponentItemRel/querySmtComponentItemRel`, data)
  }
}

export default {
  NAME,
  APIS
}
