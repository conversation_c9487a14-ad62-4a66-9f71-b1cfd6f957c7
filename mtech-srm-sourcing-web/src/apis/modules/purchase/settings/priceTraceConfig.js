import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'

const NAME = 'priceTraceConfig'
const APIS = {
  /********** 价格追溯配置 ***********/
  // 价格追溯配置-编辑/保存
  savePriceTraceConfig: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/priceRecord/retrospect/config/save`, data)
  },
  // 价格追溯配置-删除
  deletePriceTraceConfig: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/priceRecord/retrospect/config/delete`, data)
  },
  // 价格追溯配置-启用
  enablePriceTraceConfig: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/priceRecord/retrospect/config/enable`, data)
  },
  // 价格追溯配置-停用
  disablePriceTraceConfig: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/priceRecord/retrospect/config/disable`, data)
  },
  // 价格追溯配置-详情
  getPriceTraceConfigDetailById: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/priceRecord/retrospect/config/detail`, data)
  }
}

export default {
  NAME,
  APIS
}
