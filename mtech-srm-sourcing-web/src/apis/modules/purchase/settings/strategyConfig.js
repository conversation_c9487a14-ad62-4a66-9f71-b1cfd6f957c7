import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'strategyConfig'
const APIS = {
  /*
    策略地图接口
  */
  getRfxStrategyList: `${PROXY_SOURCING}/tenant/rfx/strategy/list`,
  saveRfxStrategyData: (data = {}) => {
    //策略地图-保存策略地图
    return API.post(`${PROXY_SOURCING}/tenant/rfx/strategy/data`, data)
  },
  saveRfxStrategyDataValid: (data = {}) => {
    //策略地图-保存策略地图-表单验证
    return API.get(`${PROXY_SOURCING}/tenant/rfx/strategy/data-valid`, data)
  },
  getRfxStrategyDetailData: (params = {}) => {
    //策略地图详情
    return API.get(`${PROXY_SOURCING}/tenant/rfx/strategy/data/detail`, params)
  },
  //策略地图明细
  getRfxStrategyDetailItems: (params = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/rfx/strategy/detail/items`, params)
  },
  deleteRfxStrategyDataByIds: (data = {}) => {
    //策略地图-删除策略
    return API.post(`${PROXY_SOURCING}/tenant/rfx/strategy/data/ids`, data)
  },
  updateRfxStrategyDataStatus: (data = {}) => {
    //策略地图-更新策略状态
    return API.put(`${PROXY_SOURCING}/tenant/rfx/strategy/status`, data)
  },
  getRfxStrategySelectData: (params = {}) => {
    //策略地图--下拉框、选择框数据汇总
    return API.get(`${PROXY_SOURCING}/tenant/rfx/strategy/data/select`, params)
  },
  getRfxStrategyEnableList: (params = {}) => {
    //策略地图--所有启用的策略地图数据
    return API.get(`${PROXY_SOURCING}/tenant/rfx/strategy/list/enable`, params)
  },

  /*
    询价大厅-策略报告Tab使用
  */
  queryByRfxIdAndRoundId: (params = {}) => {
    //根据询价单id和轮次id查询策略信息
    // const _query = utils.toQueryParams(query);
    return API.post(`${PROXY_SOURCING}/tenant/rfxRoundStrategy/queryByRfxIdAndRoundId`, params)
  },
  /**
   * 根据询价单id和轮次id查询策略信息
   */
  findByRfxId: ({ sourcingMode, rfxId } = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/strategyConfig/findByRfxId/${sourcingMode}/${rfxId}`)
  },

  /**
   * 保存策略
   */
  save: (params = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant/strategyConfig/save`, params)
  },

  queryStrategyMapByRfxId: (params = {}) => {
    //根据询价单id查询可选策略地图
    return API.post(`${PROXY_SOURCING}/tenant/rfxRoundStrategy/queryStrategyMapByRfxId`, params)
  },
  saveRfxRoundStrategy: (data = {}) => {
    //保存策略
    return API.put(`${PROXY_SOURCING}/tenant/rfxRoundStrategy/save`, data)
  }
}

export default {
  NAME,
  APIS
}
