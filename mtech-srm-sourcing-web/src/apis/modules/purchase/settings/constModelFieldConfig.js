/** 采方: 成本模型字段配置 **/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'constModelFieldConfig'
const APIS = {
  // 查询列表
  queryCmffListUrl: `${PROXY_SOURCING}/tenant/costModel/baseField/queryBuilder`,
  // 保存
  saveCmff: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/costModel/baseField/batchSave`, data)
  },
  // 删除
  deleteCmff: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/costModel/baseField/batchDelete`, data)
  },
  // 启用
  enableCmff: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/costModel/baseField/batchEnable`, data)
  },
  // 禁用
  disableCmff: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/costModel/baseField/batchDisable`, data)
  }
}

export default {
  NAME,
  APIS
}
