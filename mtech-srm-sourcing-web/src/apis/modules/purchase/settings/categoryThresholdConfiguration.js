import { API } from '@mtech-common/http'
const NAME = 'CategoryThresholdConfiguration'
const APIS = {
  /*
    品类价格阈值配置
  */
  // 查询
  searchList: (data = {}) => API.post(`/price/tenant/categoryPrice/threshold/query`, data),

  // 新增
  addCategoryThresholdConfigurationInfo: (data = {}) =>
    API.post(`/price/tenant/categoryPrice/threshold`, data),

  // 编辑
  editCategoryThresholdConfigurationInfo: (data = {}) =>
    API.put(`/price/tenant/categoryPrice/threshold`, data),

  // 生效
  activeCategoryThresholdConfigurationInfo: (data = {}) =>
    API.get(`/price/tenant/categoryPrice/threshold?ids=${data.ids?.join()}&status=${data.status}`),

  // 失效
  invalidCategoryThresholdConfigurationInfo: (data = {}) =>
    API.get(`/price/tenant/categoryPrice/threshold?ids=${data.ids?.join()}&status=${data.status}`),

  // 删除
  deleteCategoryThresholdConfigurationInfo: (data = {}) =>
    API.delete(`/price/tenant/categoryPrice/threshold?ids=${data.join()}`),

  // 导入
  importCategoryThresholdConfigurationInfo: (data = {}) =>
    API.post(`/price/tenant/categoryPrice/threshold/upload`, data, {
      responseType: 'blob'
    }),

  // 模板下载
  importDownlaodCategoryThresholdConfiguration: (data = {}) =>
    API.post(`/price/tenant/categoryPrice/threshold/export?isTemplate=true`, data, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
