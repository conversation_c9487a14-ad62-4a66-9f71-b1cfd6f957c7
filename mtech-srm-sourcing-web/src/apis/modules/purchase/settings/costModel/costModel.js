import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'costModel'
const APIS = {
  /*
    成本模型配置接口
  */
  //查询-成本模型明细/*
  // 成本模型主表接口
  //queryBuilder查询-成本模型信息
  getModelList: `${PROXY_SOURCING}/tenant/costModelHeader/queryBuilder`,
  getAuthCompanyList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costModelHeader/queryCompany`, data),
  //保存成本模型-同步保存相关关联公司与关联项
  saveModel: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/costModelHeader/save`, data),
  //保存成本模型--表单校验
  saveModelValid: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/costModelHeader/save-valid`, data),
  //根据ID提交
  commitById: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/costModelHeader/commitById`, data),
  //根据ID停止使用
  disableById: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/costModelHeader/disableById`, data),
  //根据id查询成本模型信息
  queryById: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/costModelHeader/queryById`, data),
  //根据ID删除
  deleteById: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/costModelHeader/deleteById`, data),

  getCostModelItem: `${PROXY_SOURCING}/tenant/costModelItem/queryBuilder`,

  // 成本模型明细-递归查询成本模型明细
  queryCostModelItem: (data) =>
    API.get(`${PROXY_SOURCING}/tenant/costModelItem/queryCostModelItem`, data),
  // 成本模型明细-新增节点
  saveItem: (data) => API.put(`${PROXY_SOURCING}/tenant/costModelItem/saveItem`, data),
  // 成本模型明细-删除节点
  deleteItem: (data) => API.put(`${PROXY_SOURCING}/tenant/costModelItem/deleteItem`, data),
  // 成本模型明细-末级节点查询
  queryLeafNodeData: (data) =>
    API.get(`${PROXY_SOURCING}/tenant/costModelItem/queryLeafNodeData`, data),
  // 成本模型明细-末级列
  queryColumn: (data) => API.get(`${PROXY_SOURCING}/tenant/costModelItem/queryColumn`, data),
  // 成本模型明细-新增列
  saveColumn: (data) => API.put(`${PROXY_SOURCING}/tenant/costModelItem/saveColumn`, data),
  // 成本模型明细-删除列
  deleteColumn: (data) => API.put(`${PROXY_SOURCING}/tenant/costModelItem/deleteColumn`, data),
  // 成本模型明细-新增行数据
  saveItemData: (data) => API.put(`${PROXY_SOURCING}/tenant/costModelItem/saveItemData`, data),
  // 成本模型明细-删除行数据
  deleteRowData: (data) => API.put(`${PROXY_SOURCING}/tenant/costModelItem/deleteRowData`, data),
  // 根据公司和关联项查询-成本模型信息
  queryByCompanyAndRel: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/costModelHeader/queryByCompanyAndRel`, data),
  // 根据公司和关联项查询-成本模型信息
  queryRel: (data) => API.post(`${PROXY_SOURCING}/tenant/costModelHeader/queryRel`, data),
  queryRelUrl: `${PROXY_SOURCING}/tenant/costModelHeader/queryRel`,
  // 根据品类、公司关联成本模型
  queryRelCostModel: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/costModelHeader/queryRelCostModel`, data),
  queryRelKtCostModel: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/costModelHeader/kt/queryRelCostModel`, data),
  // 保存品类-物料-成本因子关联
  saveRel: (data) => API.post(`${PROXY_SOURCING}/tenant/costModelHeader/saveRel`, data),
  // 删除品类-物料-成本因子关联
  deleteRel: (data) => API.post(`${PROXY_SOURCING}/tenant/costModelHeader/deleteRel`, data),
  // 批量复制成本模型
  batchCopy: (data) => API.put(`${PROXY_SOURCING}/tenant/costModelHeader/batchCopy`, data),

  /** 成本模型-明细页面相关API */
  // 查询动态表单、列信息
  getCostModelDetailColumns: (data) =>
    API.get(`${PROXY_SOURCING}/tenant/costModelHeader/queryColumn`, data),
  // 查询列表
  getCostModelDetailList: (data) =>
    API.get(`${PROXY_SOURCING}/tenant/costModelHeader/queryItem`, data),
  // 保存
  saveDetailItemList: (data) => API.post(`${PROXY_SOURCING}/tenant/costModelHeader/saveItem`, data),
  // 提交
  submitDetailItemList: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/costModelHeader/commitItem`, data),
  // 删除
  deleteDetailItemList: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/costModelHeader/deleteItem`, data),
  // 导入
  importItem: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/costModelHeader/importItem?costModelId=${data.costModelId}`,
      data.data,
      {
        // headers: { 'Content-Type': 'multipart/form-data' },
        responseType: 'blob'
      }
    )
  },
  // 导出
  exportItem: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/costModelHeader/exportItem`, data, {
      responseType: 'blob'
    }),
  // 模拟成本测算
  costCalculate: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/costModelHeader/estimate`, data),
  // 获取成本构成公式变量
  getFormulaVarList: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/costModelHeader/queryItemVarList`, data),
  // 重载成本模型动态表单、列信息
  reloadCostModelDetailColumns: (data) =>
    API.get(`${PROXY_SOURCING}/tenant/costModelHeader/reloadColumn`, data)
}

export default {
  NAME,
  APIS
}
