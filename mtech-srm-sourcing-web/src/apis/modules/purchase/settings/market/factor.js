import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'marketFactor'
const APIS = {
  /*
    行情因子接口
  */
  //行情因子列表
  getMarketFactorList: `${PROXY_SOURCING}/tenant/factor/queryFactor`,
  // 添加行情因子
  saveMarketFactor: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/factor/save`, data),
  // 添加行情因子--表单验证
  saveMarketFactorValid: (data = {}) => API.get(`${PROXY_SOURCING}/tenant/factor/save-valid`, data),
  //禁用行情因子
  disableFactor: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/factor/disableById`, data),
  // 启用行情因子
  enableFactor: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/factor/enableById`, data),
  // 查询行情因子授权公司
  getAuthCompanyList: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/factor/queryCompany`, data),
  //操作记录
  getOperatorList: `${PROXY_SOURCING}/tenant/factor/list`,

  // 采方查询成本因子
  getCostFactorListUrl: `${PROXY_SOURCING}/tenant/costModelHeader/costFactorPageQuery`
}

export default {
  NAME,
  APIS
}
