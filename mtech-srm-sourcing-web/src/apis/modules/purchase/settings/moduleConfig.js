import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'moduleConfig'
const APIS = {
  /*
    策略配置接口
  */
  getStrategyConfigCondition: (params = {}) => {
    //获取条件下拉框数据  ok
    return API.get(`${PROXY_SOURCING}/tenant/strategy/config/condition`, params)
  },
  deleteStrategyConfig: (data = {}) => {
    //删除配置，多个逗号分隔
    return API.post(`${PROXY_SOURCING}/tenant/strategy/config/delete`, data)
  },
  updateStrategyConfigDistribute: (data = {}) => {
    //更新分配策略   ok
    return API.put(`${PROXY_SOURCING}/tenant/strategy/config/distribute`, data)
  },
  getStrategyConfigDistributeDetail: (params = {}) => {
    //分配策略配置详情  ok
    return API.get(`${PROXY_SOURCING}/tenant/strategy/config/distribute/detail`, params)
  },
  executeStrategyConfigDistribute: (data = {}) => {
    //执行分配策略   un-use
    return API.post(`${PROXY_SOURCING}/tenant/strategy/config/distribute/execute`, data)
  },
  saveStrategyConfigDistribute: (data = {}) => {
    //保存分配策略  ok
    return API.post(`${PROXY_SOURCING}/tenant/strategy/config/distribute/save`, data)
  },
  saveStrategyConfigDistributeValid: (data = {}) => {
    //保存分配策略  ok -验证
    return API.get(`${PROXY_SOURCING}/tenant/strategy/config/distribute/save-valid`, data)
  },
  updateStrategyConfigEnable: (data = {}) => {
    //更新状态  ok
    return API.put(`${PROXY_SOURCING}/tenant/strategy/config/enable`, data)
  },
  updateStrategyConfigGroup: (data = {}) => {
    //更新分组策略   ok
    return API.put(`${PROXY_SOURCING}/tenant/strategy/config/group`, data)
  },
  getStrategyConfigGroupDetail: (params = {}) => {
    //分组策略配置详情  ok
    return API.get(`${PROXY_SOURCING}/tenant/strategy/config/group/detail`, params)
  },
  getStrategyConfigGroupList: (params = {}) => {
    //分组规则列表  ok
    return API.get(`${PROXY_SOURCING}/tenant/strategy/config/group/list`, params)
  },
  getStrategyConfigGroupProposalList: (data = {}) => {
    //寻源需求建议
    return API.post(`${PROXY_SOURCING}/tenant/strategy/config/group/proposal/list`, data)
  },
  saveStrategyConfigGroup: (data = {}) => {
    //保存分组策略配置  ok
    return API.post(`${PROXY_SOURCING}/tenant/strategy/config/group/save`, data)
  },
  saveStrategyConfigGroupValid: (data = {}) => {
    //保存分组策略配置  ok-验证
    return API.get(`${PROXY_SOURCING}/tenant/strategy/config/group/save-valid`, data)
  },
  getStrategyConfigs: `${PROXY_SOURCING}/tenant/strategy/configs`,

  /*
    寻源策略配置接口
  */
  batchSaveSourcingGlobalStrategy: (params = {}) => {
    //批量保存寻源策略配置
    return API.put(`${PROXY_SOURCING}/tenant/sourcingGlobalStrategy/batchSave`, params)
  },
  deleteSourcingGlobalStrategyById: (data = {}) => {
    //根据ID删除
    return API.put(`${PROXY_SOURCING}/tenant/sourcingGlobalStrategy/deleteById`, data)
  },
  queryBuilderSourcingGlobalStrategy: (data = {}) => {
    //queryBuilder查询寻源策略配置
    return API.post(`${PROXY_SOURCING}/tenant/sourcingGlobalStrategy/queryBuilder`, data)
  }
}

export default {
  NAME,
  APIS
}
