import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'approvalConfig'
const APIS = {
  /*
    审批流配置接口
  */
  //获取审批流列表列表
  getApprovalConfigList: `${PROXY_SOURCING}/tenant/process/configs`,
  //保存审批流配置
  saveApprovalConfig: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/process/config/save`, data),
  //保存审批流配置-验证
  saveApprovalConfigValid: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/process/config/save-valid`, data),
  //删除审批流配置
  deleteApprovalConfig: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/process/config/delete`, data),
  //更新审批流状态
  updateApprovalConfig: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/process/config/enable`, data)
}

export default {
  NAME,
  APIS
}
