import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
import utils from '@/utils/utils'
const NAME = 'businessConfig'
const APIS = {
  /*
    业务类型字段配置--old
  */
  //配置详情
  getBusinessConfigDetail: (data) =>
    API.get(`${PROXY_SOURCING}/tenant/por/business/config/detail`, data),

  //保存配置
  saveBusinessConfig: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/por/business/config/save`, data),

  //配置列表
  getBusinessConfigs: `${PROXY_SOURCING}/tenant/por/business/configs`,

  /*
    寻源业务配置接口--new
  */
  //配置列表
  getConfigList: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/business/configs`, data),
  getConfigListUrl: `${PROXY_SOURCING}/tenant/business/configs`,
  getPostponeConfigList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/business/configs/PricePostpone`, data),
  //保存业务配置
  saveConfig: (data) => API.post(`${PROXY_SOURCING}/tenant/business/config/save`, data),

  //启用禁用
  enableConfig: (data) => API.post(`${PROXY_SOURCING}/tenant/business/config/enable`, data),

  //删除配置
  deleteConfig: (data) => API.post(`${PROXY_SOURCING}/tenant/business/config/delete`, data),

  //业务配置详情
  configDetail: (data) => API.get(`${PROXY_SOURCING}/tenant/business/config/detail`, data),

  //保存业务配置详情
  saveConfigDetail: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/business/config/detail/save`, data),

  //获取配置阶段
  getStageList: (data) => API.get(`${PROXY_SOURCING}/tenant/business/config/stages`, data),

  // 导入JSON
  businessCfgImportJson: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/business/import-config-json`, data),
  // 导出JSON
  businessCfgExportJson: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/business/export-config-json`, data),

  /*
    寻源需求公共接口
  */
  //单据详情页-获取模块定义
  getModuleConfig: (params) => API.get(`${PROXY_SOURCING}/tenant/por/module/config`, params),

  //单据详情页-获取单据模块数据
  getSingleModuleData: `${PROXY_SOURCING}/tenant/por/module/data`,
  getModuleData: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/por/module/data`, data),

  //单据详情页-保存单据模块信息
  saveModuleData: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/por/module/save`, data),
  // 保存单据模块信息--表单校验
  saveModuleDataValid: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/por/module/save-valid`, data),

  //单据详情页-导出单据模块信息
  exportModuleData: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/por/module/export`, data),

  /*
    寻源文件配置接口
  */
  //根据ID删除
  deleteFileConfig: (data) =>
    API.put(`${PROXY_SOURCING}/tenant/sourcingFileConfig/deleteById`, data),

  //获取文件配置
  getFileConfig: (data = {}) => {
    const query = utils.toQueryParams(data)
    return API.get(`${PROXY_SOURCING}/tenant/sourcingFileConfig/queryByConfigId?${query}`)
  },

  //寻源文件配置保存
  saveFileConfig: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/sourcingFileConfig/save`, data),

  //业务明细字段保存
  saveItemFileConfig: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/business/config/files/save`, data),

  //业务明细字段保存
  queryFieldGroup: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/business/base/fieldGroup`, data),

  //业务明细字段删除
  deleteBaseField: (data) => API.post(`${PROXY_SOURCING}/tenant/business/baseField/delete`, data),
  //业务明细字段导入
  importBaseField: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/business/import-baseField-json`, data),
  //业务明细字段导出
  exportBaseField: (data) =>
    API.post(`${PROXY_SOURCING}/tenant/business/export-baseField-json`, data)
}

export default {
  NAME,
  APIS
}
