import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'expert'
const APIS = {
  /*
    专家管理主表接口
  */
  //专家管理查询-专家管理信息
  expertList: `${PROXY_SOURCING}/tenant/expert/apply/query`,
  // 管家管理列表删除
  expertDelete: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/expert/apply/delete`, data),
  // 专家管理保存
  expertsave: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/expert/apply/save`, data),
  // 专家管理列表详情
  expertsaveDetail: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/expert/apply/queryDetail`, data),
  //专家信息管理列表
  expertLists: `${PROXY_SOURCING}/tenant/expert/info/query`,
  //专家信息管理详情列表
  expertsaveDetails: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/expert/info/queryDetail`, data),
  //专家信息管理删除
  expertDeletes: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/expert/info/delete`, data),
  //专家信息管理保存
  expertsaves: (data = {}) => API.put(`${PROXY_SOURCING}/tenant/expert/info/save`, data),
  // 专家申请提交
  expertSubmit: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/expert/apply/apply`, data),
  // 根据账号查询专家信息详情
  queryDetail: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/expert/info/delete/queryDetail`, data)
}

export default {
  NAME,
  APIS
}
