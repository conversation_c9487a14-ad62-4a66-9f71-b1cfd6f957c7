/********** 成本因子联动定价 ***********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'costFactorLinkagePricing'
const APIS = {
  // 查询列表-查询
  queryCflpList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/pageQuery`, data)
  },
  // 查询列表-导出
  exportCflpList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/export`, data, {
      responseType: 'blob'
    })
  },
  // 查询列表-批量删除
  deleteCflp: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/batchDel`, data)
  },
  // 查询列表-作废
  abandonCflp: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/abandonRfx`, data)
  },
  // 查询列表-批量发布
  publishCflp: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/batchPublish`, data)
  },

  // 表头-查询
  queryCflpHeaderInfo: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/queryHeader`, data)
  },
  // 表头-保存(整单)
  saveCflpAll: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/saveRfx`, data)
  },
  // 表头-发布(整单)
  publishCflpAll: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/publishRfx`, data)
  },
  // 头部-转定价
  trasnformToPricingCflp: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/toPricing`, data)
  },
  // 头部-提交OA审批
  submitOACflp: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/submitOa`, data)
  },
  // 头部-查询OA链接
  queryOALinkCflp: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/oaApproveLink`, data)
  },

  // 成本因子、物料明细-保存
  saveCflpDetail: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/saveRfxItem`, data)
  },

  // 成本因子tab-查询供应商因子价格
  querySupplierFactorPriceList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/supplierFactorPricePageQuery`,
      data
    )
  },
  // 成本因子tab-查询
  queryCflpCfDetailList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/rfxFactorItemPageQuery`,
      data
    )
  },
  // 成本因子tab-删除
  deleteCflpCfDetail: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/delRfxFactorItem`, data)
  },
  // 成本因子tab-导出
  exportCflpCfDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/exportRfxFactorItem`, data, {
      responseType: 'blob'
    }),

  // 物料明细tab-查询供应商物料价格
  querySupplierMaterialPriceList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/supplierMaterialPricePageQuery`,
      data
    )
  },
  // 物料明细tab-查询
  queryCflpMdDetailList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/rfxMaterialItemPageQuery`,
      data
    )
  },
  // 物料明细tab-删除
  deleteCflpMdDetailList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/delRfxMaterialItem`, data)
  },
  // 物料明细tab-驳回定价
  callbackCflpMdDetailList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/callbackPricing`, data)
  },
  // 物料明细tab-导出
  exportCflpMdDetailList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/exportRfxMaterialItem`, data, {
      responseType: 'blob'
    }),
  // 物料明细tab-成本分析列表查询
  queryCflpCostAnalysisColumns: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/queryColumn`, data)
  },
  // 物料明细tab-成本分析列表查询
  queryCflpCostAnalysisData: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/costModelPrice`, data)
  },
  // 物料明细tab-成本分析导出
  exportCflpCostAnalysis: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/exportCostModelPrice`, data, {
      responseType: 'blob'
    }),

  // 配额分配tab-查询
  queryCflpQuotaDetailList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/rfxQuotaItemPageQuery`,
      data
    )
  },
  // 配额分配tab-保存
  saveCflpQuotaDetailList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/saveRfxQuotaItem`, data)
  },
  // 配额分配tab-下载导入模板
  downloadCflpQuotaDetailListTemplate: (data = {}) =>
    API.post(
      `${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/downloadRfxQuotaItemTemplate`,
      data,
      {
        responseType: 'blob'
      }
    ),
  // 配额分配tab-导入
  importCflpQuotaDetailList: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/importRfxQuotaItem?rfxId=${data.rfxId}`,
      data.data,
      { responseType: 'blob' }
    )
  },
  // 配额分配tab-导出
  exportCflpQuotaDetailList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/exportRfxQuotaItem`, data, {
      responseType: 'blob'
    }),

  // 附件tab--根据rfxId查询文件节点信息
  getFileNodeList: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/queryFileNodeByRfxId`, data),
  // 附件tab--根据rfxId查询文件列表
  getFileList: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/queryFileByRfxId`, data),
  // 附件tab--保存附件
  saveFile: (data = {}) =>
    API.put(`${PROXY_SOURCING}/tenant/rfx/factorMaterialLinkage/saveFile`, data)
}

export default {
  NAME,
  APIS
}
