/******* 联动物料维护 **********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'linkageMaterialMaintenance'

const APIS = {
  // 联动定价物料维护查询
  getLmmList: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/cost/model/material/list`, data),
  // 新增
  addLmm: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/cost/model/material/add`, data),
  // 批量保存
  batchSaveLmm: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/cost/model/material/saveBatch`, data),
  // 启用、停用
  updateStatusLmm: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/cost/model/material/updateStatus`, data),
  // 批量删除
  batchDeleteLmm: (data = {}) =>
    API.delete(`${PROXY_SOURCING}/tenant/cost/model/material/delete`, data),
  // 下载导入模板
  downloadLmmImportTemplate: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/cost/model/material/getTemplate`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    }),
  // 导入
  importLmm: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/cost/model/material/import`, data.data, {
      responseType: 'blob'
    })
  },
  // 导出
  exportLmm: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/cost/model/material/export`, data, {
      responseType: 'blob'
    })
}

export default {
  NAME,
  APIS
}
