/******* 成本因子内部定价 **********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'costFactorInternalPricing'

const APIS = {
  /********  采方 ********/
  // 查询成本因子内部价格列表
  getCfipList: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/costFactor/point/pageQuery`, data),
  // 删除成本因子内部价格
  batchDeleteCfip: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/batchDel`, data),
  // 批量提交成本因子内部价格
  bacthSubmitCfip: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/batchCommit`, data),

  // 查询成本因子详情表头信息
  getCfipDetailHeaderInfo: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/queryHeader`, data),
  // 保存成本因子内部价格表头(整单保存)
  saveCfipAllDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/savePoint`, data),
  // 提交成本因子内部价格表头(整单保存)
  submitCfipAllDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/commitPoint`, data),

  // 查询定价明细列表
  getCfipDetailList: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/pointItemPageQuery`, data),
  // 保存定价明细
  saveCfipDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/savePointItem`, data),
  // 删除定价明细
  deleteCfipDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/delPointItem`, data),
  // 导入定价明细模板
  downloadCfipDetailImportTemplate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/downloadImportTemplate`, data, {
      responseType: 'blob'
    }),
  // 导入定价明细
  importCfipDetail: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/costFactor/point/importPointItem?rfxId=${data.rfxId}`,
      data.data,
      { responseType: 'blob' }
    )
  },
  // 导出定价明细
  exportCfipDetail: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/costFactor/point/exportPointItem`, data, {
      responseType: 'blob'
    }),

  // 附件-根据rfxId查询文件节点信息
  getFileNodeList: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/costFactor/point/queryFileNodeByRfxId`, data),

  // 查询成本因子价格-成本因子列表
  getCfPriceListPurchase: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/costFactorPricePageQuery`, data),

  // 查询成本因子价格-成本因子列表
  getCfPriceListCost: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/costFactorPricePageQuery`, data),
  /********  供方 ********/
  // 查询成本因子价格-成本因子列表
  getCfPriceListSupplier: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/supplier/rfxItem/costModel/costFactorPricePageQuery`, data)
}

export default {
  NAME,
  APIS
}
