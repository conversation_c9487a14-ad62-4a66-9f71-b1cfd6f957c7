/********* 成本分析报表 **********/
import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'costAnalysisReport'

const APIS = {
  // 查询列表
  getList: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/cost/analysis/report/query`, data),
  // 导出
  exportCostAnalysisReport: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/cost/analysis/report/export`, data, { responseType: 'blob' })
}

export default {
  NAME,
  APIS
}
