/******* 成本因子价格记录 **********/

import { API } from '@mtech-common/http'
import { PROXY_PRICEING } from 'CONFIG/proxy.config'
const NAME = 'costFactorPriceRecord'

const APIS = {
  // 查询成本因子价格记录列表
  getCfPriceRecords: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/costFactor/priceRecord/pageQuery`, data),
  // 导出成本因子价格记录
  exportCfPriceRecords: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/costFactor/priceRecord/exportPriceRecord`, data, {
      responseType: 'blob'
    }),
  // 查询成本因子价格历史记录
  getCfHistoryRecordsUrl: `${PROXY_PRICEING}/tenant/costFactor/priceRecord/historyPriceRecordPage`,
  // 查询成本因子价格历史趋势
  getCfHistoryTrend: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/costFactor/priceRecord/historyPriceRecordTrend`, data),
  // 导入模板下载
  outPriceImportTemplate: (data = {}) =>
    API.get(`${PROXY_PRICEING}/tenant/costFactor/priceRecord/getOuterPriceImportTemplate`, data, {
      responseType: 'blob'
    }),
  // 导入
  outPriceImport: (data = {}) => {
    return API.post(`${PROXY_PRICEING}/tenant/costFactor/priceRecord/outerPriceImport`, data.data, {
      responseType: 'blob'
    })
  }
}
export default {
  NAME,
  APIS
}
