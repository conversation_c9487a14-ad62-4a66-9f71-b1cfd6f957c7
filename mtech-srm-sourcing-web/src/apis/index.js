/*
 * @Author: your name
 * @Date: 2021-07-26 11:17:04
 * @LastEditTime: 2021-08-11 17:23:45
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-sourcing-web\src\apis\index.js
 */
const APIMap = {}

// require.context  webpack的api 通过执行require.context函数获取一个特定的上下文,
//主要用来实现自动化导入模块,在前端工程中,如果遇到从一个文件夹引入很多模块的情况,可以使用这个api,
//它会遍历文件夹中的指定文件,然后自动导入,使得不需要每次显式的调用import导入模块
// 但是这样做编辑器无法推断命名空间

// let imports = "";
// let exports = "";
const requireComponent = require.context('./modules', true, /\.js$/)
requireComponent.keys().forEach((path) => {
  const config = requireComponent(path)
  let { NAME, APIS } = config.default
  // imports += `import {APIS as ${NAME} } from ${path}\n`;
  // exports += `${NAME}, \n`;
  if (NAME) {
    APIMap[NAME] = APIS
  }
})
// console.log(imports, `export default {${exports} }`);

//暴露APIMap

export default APIMap
