import Vue from 'vue'

function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

// function removeClass(ele, cls) {
//   if (hasClass(ele, cls)) {
//     const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
//     ele.className = ele.className.replace(reg, ' ')
//   }
// }

Vue.directive('expend-search', {
  inserted: function (el) {
    addClass(el, 'hide-search-bar')
  }
})
