import Vue from 'vue'
import axios from 'axios'
const ERR_MSG = {
  302: '未登录',
  400: '账号异常，登录超时，请重新登录',
  401: '无权查看',
  500: '系统异常，请稍后再试',
  501: '表单验证未通过',
  503: '数据异常，操作失败',
  505: 'excel导入异常',
  default: '系统异常，请稍后再试'
}
let $http = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest'
  },
  timeout: 1000 * 30
})

// $http.interceptors.response.use(
//   (response) => {
//     // if (response.status === 200) {
//     //   return response.data;
//     // }
//     return response;
//   },
//   (error) => {
//     return Promise.reject(error.response);
//   }
// );

const handleRequest = (req) => {
  return new Promise((resolve, reject) => {
    req
      .then((res) => {
        console.error('数据返回,', res)
        const { code, msg } = res
        if (code === 200) {
          resolve(res)
        } else {
          console.error(`${code}:${msg}`)
          handleWarnMessage(res)
          reject(res)
        }
      })
      .catch((err) => {
        handleWarnMessage(ERR_MSG.default)
        reject(err)
      })
  })
}

function handleWarnMessage(res) {
  const { msg, code, data } = res
  let _message = msg || ERR_MSG[`${code}`] || ERR_MSG.default
  if (data && data.errorLabels && data.errorLabels.length) {
    Vue.prototype.$toast({
      type: 'warning',
      content: data.errorLabels[0]['label']
    })
  } else {
    let _content = code ? `${code}:${_message}` : `${_message}`
    Vue.prototype.$toast({ type: 'warning', content: _content })
  }
}

const transformRequest = (data) => {
  let ret = ''
  for (let it in data) {
    ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
  }
  return ret
}

$http.get = (url, params = {}, config = {}) => {
  let _query = {
    method: 'get',
    url: url,
    params: params
  }

  if (config) {
    if (config.header) {
      _query['headers'] = config.header
    }
  }
  return handleRequest($http(_query))
}

$http.post = (url, data = {}, config = {}) => {
  let _query = {
    method: 'post',
    url: url,
    data: data
  }
  if (config) {
    if (typeof config?.serialize === 'boolean' && config?.serialize) {
      _query['transformRequest'] = [transformRequest]
    }
    if (config.header) {
      _query['headers'] = config.header
    }
  }
  // return handleRequest($http(_query));
  return $http(_query)
}

$http.put = (url, data = {}, config = {}) => {
  let _query = {
    method: 'put',
    url: url,
    data: data
  }
  if (config) {
    if (typeof config?.serialize === 'boolean' && config?.serialize) {
      _query['transformRequest'] = [transformRequest]
    }
    if (config.header) {
      _query['headers'] = config.header
    }
  }
  return handleRequest($http(_query))
}

$http.delete = (url, data = {}, config = {}) => {
  let _query = {
    method: 'delete',
    url: url,
    data: data,
    transformRequest: [transformRequest],
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    }
  }
  if (config) {
    if (typeof config?.serialize === 'boolean' && config?.serialize) {
      _query['transformRequest'] = [transformRequest]
    }
    if (config.header) {
      _query['headers'] = config.header
    }
  }
  return handleRequest($http(_query))
}

export { $http }
