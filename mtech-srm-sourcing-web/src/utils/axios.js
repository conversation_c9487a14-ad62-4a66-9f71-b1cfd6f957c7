import axios from 'axios'
import indexDB from '@digis/internationalization'

const StatusCode = {
  Success: 200
}

const methodUseData = ['post', 'POST', 'put', 'PUT', 'patch', 'PATCH', 'delete', 'DELETE']
let instance
const API = {}
let notify = {}
const notifyDefault = {
  success: function () {
    // Vue.prototype.$toast({
    //   content: '操作成功',
    //   type: 'success'
    // })
  },
  error: function () {
    // Vue.prototype.$toast({
    //   content: '操作成功',
    //   type: 'success'
    // })
  }
}

export const baseConfig = {
  setDefault(config) {
    instance = axios.create(config)
    baseConfig.addRequestTransform()
    baseConfig.addResponseTransform()
    baseConfig.addNotify(notifyDefault)
  },
  addRequestTransform(callback) {
    instance.interceptors.request.use(
      (config) => {
        config = callback?.(config) ?? config

        if (config.headers) {
          config.headers['digis-lang'] = indexDB.getLanguage()
        }

        return config
      },
      async (error) => {
        return await Promise.reject(error)
      }
    )
  },
  addResponseTransform() {
    instance.interceptors.response.use(
      (response) => {
        return response
      },
      async (error) => {
        // Request failed with status code 为非2xx
        return await Promise.reject(error)
      }
    )
  },
  addNotify(config) {
    notify = config
  }
}

/**
 * @description: 实例方法
 * @param {object} config
 * @return {*}
 */
const handle = async (config) => {
  const { useNotify, data, ...AxiosConfig } = config
  const { responseType } = AxiosConfig
  let RequestConfig = {}

  RequestConfig[methodUseData.includes(AxiosConfig.method) ? 'data' : 'params'] = data
  RequestConfig = Object.assign(RequestConfig, AxiosConfig)

  return await new Promise((resolve, reject) => {
    instance(RequestConfig).then(
      (response) => {
        const responseData = response.data
        if (responseType === 'blob' || StatusCode.Success === responseData.code) {
          if (useNotify) {
            notify?.success(responseData.msg || '操作成功')
          }
          resolve(responseType === 'blob' ? response : responseData)
        } else {
          const errMsg =
            responseData.data?.errorLabels?.[0]?.message || responseData.msg || '系统异常'
          console.log('1222212112', notify)
          notify?.error(errMsg)
          reject(responseData)
        }
      },
      (error) => {
        notify?.error(error?.msg)
        reject(error)
      }
    )
  })
}

API.get = function (url, data, config) {
  const params = {
    method: 'get',
    url,
    data,
    ...config
  }
  return handle(params)
}

API.post = function (url, data, config) {
  const params = {
    method: 'post',
    url,
    data,
    ...config
  }
  return handle(params)
}

API.delete = function (url, data, config) {
  const params = {
    method: 'delete',
    url,
    data,
    ...config
  }
  return handle(params)
}

API.put = function (url, data, config) {
  const params = {
    method: 'put',
    url,
    data,
    ...config
  }
  return handle(params)
}

API.patch = function (url, data, config) {
  const params = {
    method: 'patch',
    url,
    data,
    ...config
  }
  return handle(params)
}

export { API }
