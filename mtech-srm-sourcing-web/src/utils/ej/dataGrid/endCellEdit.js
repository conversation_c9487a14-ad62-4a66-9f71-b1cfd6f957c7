let currentCell
let targetPopperEls = []
let cureentClickRange
let cancleEdit = true
cureentClickRange = document.querySelector('.mainMicroContainer') || document.body

export const setCancelRange = (el) => {
  if (!el) return
  el.onclick = null
  el.onclick = cancelEditinghandler
}
export const setCurrentCell = (td) => {
  currentCell = td
}
export const setPopperElm = (els) => {
  // targetPopperEls = targetPopperEls.concat(els)
  targetPopperEls = els
}
export const getCancelEditState = () => {
  return cancleEdit
}

export const getPopupContainer = (triggerNode) => {
  return triggerNode?.closest('td') || document.body
}

const cancelEditinghandler = (e) => {
  if (!currentCell) {
    cancleEdit = false
    return
  }
  targetPopperEls = targetPopperEls || document.querySelector('.coustom-editor-pop')
  if (
    currentCell.contains(e.target) ||
    currentCell.contains(e.target) ||
    currentCell === e.target ||
    (targetPopperEls &&
      Array.isArray(targetPopperEls) &&
      targetPopperEls.some((item) => item.contains(e.target)))
  ) {
    cancleEdit = true
  } else {
    cancleEdit = false
  }
}

// cureentClickRange.removeEventListener('click', cancelEditinghandler)
// cureentClickRange.addEventListener('click', cancelEditinghandler)
cureentClickRange.onclick = null
cureentClickRange.onclick = cancelEditinghandler
