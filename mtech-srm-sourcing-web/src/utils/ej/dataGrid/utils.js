/**
 * 字段联动更新
 * @param {object} ctx
 * @param {string} field 当前字段
 * @param {object} linkFields 需要更新的字段
 * @param {object} targetRow 选中行数据
 * @returns
 */
export function fieldLinkUpdate(ctx, field, linkFields = {}, targetRow) {
  const value = ctx.getValueByField(field)
  const { dataSource, fields } = ctx.getOptions(field)
  if (!dataSource) {
    return
  }
  if (!targetRow) {
    let pk = 'value'
    if (fields && fields.value) {
      pk = fields.value
    }
    targetRow = dataSource.find((e) => e[pk] === value)
  }

  for (const linkField of Object.keys(linkFields)) {
    const val = targetRow ? targetRow[linkFields[linkField]] : null
    ctx.setValueByField(linkField, val)
  }
}

/**
 * 合并 select 组件的 dataSource
 * 主要用于 select 在使用 filtering 动态插入 dataSource 时, 上面的 fieldLinkUpdate 方法会找不到新的 dataSource
 * @param {Object} ctx
 * @param {String} field
 * @param {Array} newDataSource
 * @param {String} pk 唯一主键
 */
export function mergeDataSource(ctx, field, newDataSource, pk) {
  const options = ctx.getOptions(field)
  const dataSource = options.dataSource || []
  if (!pk) {
    pk = options?.fields?.value || 'value'
  }
  for (const record of newDataSource) {
    if (!dataSource.find((e) => e[pk] === record[pk])) {
      dataSource.push(record)
    }
  }
  ctx.setOptions({ dataSource })
  return dataSource
}
