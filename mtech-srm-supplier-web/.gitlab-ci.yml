image: poleaxe-registry.tcl.com.cn/library/front-ci:1.2


stages:
  - build
  - docker-build

build:
  stage: build
  script:
    - npm install --unsafe-perm=true --allow-root --registry http://************:8081/repository/jd-npm-pub/
    - npm run build
    - echo "build complete..."
  cache:
    paths:
      - node_modules/ 
  artifacts:
    name: $CI_PROJECT_NAME
    expire_in: 1 day
    paths:
      - dist/*


#构建docker镜像
docker-build:
  stage: docker-build
  script:
    - docker_build
    - chart_build


.auto_devops: &auto_devops |
  curl -o .auto_devops.sh \
      "${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"
  if [ $? -ne 0 ];then
    cat .auto_devops.sh
    exit 1
  fi
  sed -i  "s,response_upload_to_devops.*$,##,g" .auto_devops.sh
  sed -i  "s,rm \"$.*$,ls -al,g" .auto_devops.sh
  source .auto_devops.sh
  function docker_build(){
    docker build --pull -t ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG} -f docker/Dockerfile .
    docker push ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}
  }

before_script:
  - *auto_devops
  - export
