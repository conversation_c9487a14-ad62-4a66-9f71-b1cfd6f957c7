const Router = [
  {
    path: 'sup/info-survey', // 信息调查管理（供方）
    name: 'sup-info-survey',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/infoSurveyManage/index')
  },
  {
    path: 'sup/info-survey-detail', // 信息调查管理详情（供方）
    name: 'sup-info-survey-detail',
    component: () => import('@/views/Buyer/infoSurveyManage/detail')
  }

  // {
  //   path: "pur/info-survey", // 供应商信息调查管理
  //   name: "pur-info-survey",
  //   component: () => import("@/views/Buyer/infoSurveyManage/index"),
  // },
  // {
  //   path: "pur/info-survey-detail", // 供应商信息调查管理详情
  //   name: "pur-info-survey-detail",
  //   component: () => import("@/views/Buyer/infoSurveyManage/detail"),
  // },
]

export default Router
