/** 采方：考核管理**/

const Router = [
  {
    path: 'sup/purchase-assessmanage-assessFeedback', // 菜单：考核单管理
    name: 'purchase-assessmanage-assessFeedback',
    meta: { keepAlive: true },
    component: () => import('@/views/Supplier/assessFeedback/index.vue')
  },
  {
    path: 'sup/purchase-assessmanage-assessFeedbackDetail', // 菜单：考核单详情
    name: 'purchase-assessmanage-assessFeedbackDetail',
    component: () => import('@/views/Supplier/assessFeedback/detail.vue')
  },
  {
    path: 'sup/purchase-assessmanage-assessFeedback-idleMaterialDetail', // 菜单：考核单-呆料索赔单详情
    name: 'purchase-assessmanage-assessFeedback-idleMaterialDetail',
    component: () => import('@/views/Supplier/assessFeedback/idleMaterial.vue')
  }
]

export default Router
