const Router = [
  // 返利管理
  {
    path: 'rebate-feedback',
    name: 'rebate-feedback',
    component: () => import('@/views/public.vue'),
    meta: {
      title: '返利反馈',
      keepAlive: true
    },
    children: [
      // 返利协议确认
      {
        path: 'rebate-agreement-confirm',
        name: 'rebate-agreement-confirm',
        meta: { keepAlive: true },
        component: () => import('@/views/Supplier/rebateFeedback/rebateAgreementConfirm/index.vue')
      },
      // 返利协议确认-明细
      {
        path: 'rebate-agreement-confirm-detail',
        name: 'rebate-agreement-confirm-detail',
        meta: { keepAlive: true },
        component: () => import('@/views/Supplier/rebateFeedback/rebateAgreementConfirm/detail.vue')
      },

      // 返利金额计算
      {
        path: 'rebate-calculat-amount-sup',
        name: 'rebate-calculat-amount-sup',
        meta: { keepAlive: true },
        component: () => import('@/views/Supplier/rebateFeedback/rebateCalculatAmount/index.vue')
      },
      // 返利金额计算-明细
      {
        path: 'rebate-calculat-amount-detail-sup',
        name: 'rebate-calculat-amount-detail-sup',
        meta: { keepAlive: true },
        component: () => import('@/views/Supplier/rebateFeedback/rebateCalculatAmount/detail.vue')
      }
    ]
  }
]

export default Router
