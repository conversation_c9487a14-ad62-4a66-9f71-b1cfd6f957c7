const Router = [
  {
    path: 'dataPrepare',
    name: 'data-preparation',

    component: () => import('@/views/Supplier/AnalysisModule/DataPreparation/index'),
    meta: {
      title: '数据准备',
      keepAlive: true
    }
  },
  {
    path: 'dataPrepareDetail',
    name: 'data-preparation-detail',
    component: () =>
      import('@/views/Supplier/AnalysisModule/DataPreparation/components/detail/index.vue'),
    meta: {
      title: '数据准备详情'
    }
  },
  {
    path: 'supplier/performanceManage',
    name: 'supplier-performance-management',
    component: () => import('@/views/Supplier/AnalysisModule/PerformanceManagement/index'),
    meta: {
      title: '供应商绩效管理',
      keepAlive: true
    }
  },
  {
    path: 'dataPresent',
    name: 'data-presentation',
    component: () =>
      import('@/views/Supplier/AnalysisModule/DataPresentation/components/charts/index'),
    meta: {
      title: '数据展现',
      keepAlive: true
    }
  }
]
export default Router
