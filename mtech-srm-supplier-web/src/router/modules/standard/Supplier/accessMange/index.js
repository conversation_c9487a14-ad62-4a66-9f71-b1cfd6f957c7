const Router = [
  {
    path: 'sup/access', // 准入管理（供方）
    name: 'sup-acces',
    meta: { keepAlive: true },
    component: () => import('@/views/Supplier/accessManage/index')
  },
  {
    path: 'sup/preReviewInvestigation', // 预审调查表（供方）
    name: 'sup-preReviewInvestigation',
    component: () => import('@/views/Supplier/accessManage/components/preReviewInvestigation.vue')
  },
  {
    path: 'sup/preReviewInvestigationDetail', // 预审调查表-详情（供方）
    name: 'sup-pre-detail',
    component: () =>
      import('@/views/Supplier/accessManage/components/preReviewInvestigationDetail.vue')
  }
]

export default Router
