const Router = [
  // 半导体风险反馈
  {
    path: 'semiconductor-risk-feedback',
    name: 'semiconductor-risk-feedback',
    component: () => import('@/views/Supplier/semiconductorRiskFeedback/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 半导体风险反馈-详情
  {
    path: 'semiconductor-risk-feedback-detail',
    name: 'semiconductor-risk-feedback-detail',
    meta: {},
    component: () => import('@/views/Supplier/semiconductorRiskFeedback/detail.vue')
  }
]
export default Router
