const Router = [
  {
    path: 'sup/review',
    name: 'sup/review',
    meta: { keepAlive: true },
    component: () => import('@/views/Supplier/purReview/index.vue')
  },
  {
    path: 'sup/review-detail',
    name: 'sup/review-detail',
    component: () => import('@/views/Supplier/purReview/reviewDetail.vue')
  }
  // 我的评分详情
  // {
  //   path: "pur/review-scoring-details",
  //   name: "pur-review-scoring-details", //切记用 / 要用-
  //   component: () =>
  //     import(
  //       "@/views/Buyer/purReview/scoreSetting/components/myscore/components/index.vue"
  //     ),
  // },
  // {
  //   path: "pur/review-detail",
  //   name: "review-detail",
  //   component: () =>
  //     import(
  //       "@/views/Buyer/purReview/scoreSetting/components/template/previewDetail.vue"
  //     ),
  // },
]
export default Router
