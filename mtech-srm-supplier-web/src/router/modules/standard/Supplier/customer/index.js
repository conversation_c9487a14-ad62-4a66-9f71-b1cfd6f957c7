const Router = [
  {
    path: 'customer-invitation',
    name: 'customer-invitation',
    component: () => import('@/views/Supplier/Customer/InvitationManage/index'),
    meta: {
      title: 'customerinvitation',
      keepAlive: true
    }
  },
  {
    path: 'customer-invitation-info/:type/:id',
    name: 'customer-invitation-info',
    component: () => import('@/views/Supplier/Customer/InvitationManage/Info'),
    meta: {
      title: 'customerInvitationInfo'
    }
  },
  {
    path: 'customer-profile',
    name: 'customer-profile',
    component: () => import('@/views/Supplier/Customer/customerProfile/index'),
    meta: {
      title: 'customerinvitation',
      keepAlive: true
    }
  },
  {
    path: 'customer-detail',
    name: 'customer-detail',
    component: () => import('@/views/Supplier/Customer/customerProfile/index'),
    meta: {
      title: 'customerinvitation'
    }
  }
]

export default Router
