const Router = [
  {
    path: 'pur/info-change-apply',
    name: 'info-change-apply-pur',
    component: () => import('@/views/Buyer/infoChange/index'),
    meta: { title: '信息变更管理', keepAlive: true }
  },
  {
    path: 'pur/infoChangePurDetail',
    name: 'infoChangePurDetail',
    component: () => import('@/views/Buyer/infoChange/children/lifeCycle'),
    meta: { title: '采方信息变更详情' }
  }
]
export default Router
