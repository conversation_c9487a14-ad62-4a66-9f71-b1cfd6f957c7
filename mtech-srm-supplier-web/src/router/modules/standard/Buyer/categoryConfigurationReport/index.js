const Router = [
  {
    path: 'category/configuration-report', // 品类配置表
    name: 'categoryConfigurationReport',

    component: () => import('@/views/Buyer/categoryConfigurationReport/index.vue')
  },
  {
    path: 'category/supplier-relation-management', // 供应商关系管理
    name: 'supplier-relation-management',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/supplierRelationManagement/index.vue')
  },
  {
    path: 'category/supplier-relation-management/multilevel-supplier-detail', // 供应商关系管理-多级供应商明细
    name: 'multilevel-supplier-detail',
    meta: { keepAlive: true },
    component: () =>
      import('@/views/Buyer/supplierRelationManagement/tabsPage/multilevelSupplier/detail.vue')
  }
]

export default Router
