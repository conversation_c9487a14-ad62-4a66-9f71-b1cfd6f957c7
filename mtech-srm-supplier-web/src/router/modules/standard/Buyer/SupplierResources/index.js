const Router = [
  {
    path: 'resources',
    name: 'supplier-resources',
    component: () => import('@/views/Buyer/supplierResources/index'),
    meta: {
      title: '供应商资源库管理',
      keepAlive: true
    }
  },
  {
    path: 'dataScreen',
    name: 'supplier-resources-dataScreen',
    component: () => import('@/views/Buyer/supplierResources/children/DataScreen'),
    meta: {
      title: '大屏',
      keepAlive: true
    }
  },
  {
    path: 'dataScreenSupplier',
    name: 'supplier-resources-dataScreenSupplier',
    component: () => import('@/views/Buyer/supplierResources/children/DataScreenSupplier'),
    meta: {
      title: '供应商驾驶舱',
      keepAlive: true
    }
  }
]

export default Router
