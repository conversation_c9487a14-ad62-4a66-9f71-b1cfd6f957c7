const Router = [
  {
    path: 'pur/category-certification', // 品类认证管理
    name: 'pur-category-certification',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/categoryCertifiManage/index')
  },
  {
    path: 'pur/category-certification-detail', // 品类认证管理详情
    name: 'pur-category-certification-detail',
    component: () => import('@/views/Buyer/categoryCertifiManage/detail')
  },
  {
    path: 'pur/category-certification-detail/scene-selfcheck-add', // 品类认证管理详情-现场审查新增
    name: 'scene-selfcheck-add',
    component: () =>
      import(
        '@/views/Buyer/purReview/scoreSetting/components/template/pages/sceneSelfcheckAdd/index.vue'
      )
  },
  {
    path: 'pur/preReviewInvestigationDetail', // 采方-预审调查表详情
    name: 'pur-pre-detail',
    component: () =>
      import('@/views/Buyer/categoryCertifiManage/components/preReviewInvestigationDetail.vue')
  }
]

export default Router
