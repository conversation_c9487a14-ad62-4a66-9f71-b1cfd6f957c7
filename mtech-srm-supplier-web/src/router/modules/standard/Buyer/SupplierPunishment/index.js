const Router = [
  {
    path: 'punishment',
    name: 'supplierpunishment',
    component: () => import('@/views/Buyer/SupplierPunishment/index'),
    meta: {
      title: '供应商惩罚',
      keepAlive: true
    },
    children: [
      {
        path: 'invitationDetail',
        name: 'invitationDetail2',
        component: () => import('@/views/Buyer/SupplierPunishment/children/invitationDetail'),
        meta: {
          title: '共享编辑',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: 'punishmentdetail',
    name: 'supplierpunishmentdetail',
    component: () => import('@/views/Buyer/SupplierPunishment/supplierDetall'),
    meta: {
      title: '供应商共享详情'
    }
  },
  {
    path: 'supplierzidong',
    name: 'supplierzidongdetail',
    component: () => import('@/views/Buyer/SupplierPunishment/supplierzidong'),
    meta: {
      title: '供应商自动分级详情'
    }
  },

  {
    path: 'classifDetall',
    name: 'supplierclassifDetall',
    component: () => import('@/views/Buyer/SupplierPunishment/supplierclassifDetall'),
    meta: {
      title: '供应商分级详情'
    }
  },
  {
    path: 'fenDetall',
    name: 'supplierfenDetall',
    component: () => import('@/views/Buyer/SupplierPunishment/supplierfenDetall'),
    meta: {
      title: '供应商分级详情'
    }
  }
]

export default Router
