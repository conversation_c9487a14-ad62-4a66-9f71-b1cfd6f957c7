const router = [
  {
    path: 'pur/freeze',
    name: 'freeze',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierFreeze/index'),
    meta: { title: '供应商冻结管理', keepAlive: true }
  },
  {
    path: 'pur/punish-detail',
    name: 'punish-detail',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierFreeze/detail'),
    meta: { title: '供应商惩罚详情' }
  },
  {
    path: 'pur/freeze-batch-detail',
    name: 'freeze-batch-detail',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierFreeze/batchDetail'),
    meta: { title: '供应商冻结详情' }
  },
  {
    path: 'pur/disuse',
    name: 'disuse',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierExit/index'),
    meta: { title: '供应商退出管理', keepAlive: true }
  },
  {
    path: 'pur/punish-detail-exit',
    name: 'punishDetailExit',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierExit/detail'),
    meta: { title: '供应商惩罚详情' }
  },
  {
    path: 'pur/exit-batch-detail',
    name: 'exit-batch-detail',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierExit/batchDetail'),
    meta: { title: '供应商退出详情' }
  },
  {
    path: 'pur/blacklist',
    name: 'blacklist',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierBlackList/index'),
    meta: { title: '供应商黑名单管理', keepAlive: true }
  },
  {
    path: 'pur/punish-detail-black',
    name: 'punishDetailBlack',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierBlackList/detail'),
    meta: { title: '供应商惩罚详情' }
  },
  {
    path: 'pur/classify',
    name: 'classify',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierClassify/index'),
    meta: { title: '供应商分级管理', keepAlive: true }
  },
  {
    path: 'pur/classify-detail',
    name: 'classifyDetail',
    component: () => import('@/views/Buyer/supplierDevelopment/supplierClassify/detail'),
    meta: { title: '供应商分级管理' }
  },
  {
    path: 'pur/change-request',
    name: 'changeRequest',
    component: () => import('@/views/Buyer/supplierDevelopment/changeRequest/index'),
    meta: { title: '4M1变更申请', keepAlive: true }
  },
  {
    path: 'pur/change-request-detail',
    name: 'changeRequestDetail',
    component: () => import('@/views/Buyer/supplierDevelopment/changeRequest/detail'),
    meta: { title: '4M1变更申请详情' }
  }
]
export default router
