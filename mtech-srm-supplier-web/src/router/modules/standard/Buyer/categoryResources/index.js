const Router = [
  // 品类资源
  {
    path: 'category-resources',
    name: 'category-resources',
    component: () => import('@/views/public.vue'),
    meta: {
      title: '品类资源',
      keepAlive: true
    },
    children: [
      // 供应能力模板设置
      {
        path: 'provision-capacity-tem',
        name: 'provision-capacity-tem',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/categoryResources/setProvisionCapacityTem/index.vue')
      },
      // 供应能力模板设置 - 模板明细
      {
        path: 'provision-capacity-template-detail',
        name: 'provision-capacity-template-detail',
        // meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Buyer/categoryResources/setProvisionCapacityTem/templateDetail/detail.vue'
          )
      },
      // 能力地图打点任务
      {
        path: 'capability-map-mark-quest',
        name: 'capability-map-mark-quest',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/categoryResources/capabilityMapMarkQuest/index.vue')
      },
      // 能力地图打点任务 - 任务明细
      {
        path: 'capability-map-mark-quest-detail',
        name: 'capability-map-mark-quest-detail',
        meta: { keepAlive: true },
        component: () =>
          import('@/views/Buyer/categoryResources/capabilityMapMarkQuest/templateDetail/detail.vue')
      },

      // 选型沙盘打点任务
      {
        path: 'select-sandbox-mark-quest',
        name: 'select-sandbox-mark-quest',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/categoryResources/selectSandboxMarkQuest/index.vue')
      },
      // 选型沙盘打点任务 - 任务明细
      {
        path: 'select-sandbox-mark-quest-detail',
        name: 'select-sandbox-mark-quest-detail',
        meta: { keepAlive: true },
        component: () =>
          import('@/views/Buyer/categoryResources/selectSandboxMarkQuest/templateDetail/detail.vue')
      },

      // 品类资源库 - 查询页
      {
        path: 'category-resource-library',
        name: 'category-resource-library',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/categoryResources/categoryResourceLibrary/index.vue')
      },
      // 品类资源库 - 详情页
      {
        path: 'category-resource-library-detail',
        name: 'category-resource-library-detail',
        component: () =>
          import('@/views/Buyer/categoryResources/categoryResourceLibrary/detail.vue')
      }
    ]
  }
]

export default Router
