const Router = [
  {
    path: 'pur/policy-setting',
    name: 'policySetting',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/moduleConfig/PolicySettings/index.vue')
  },
  {
    path: 'pur/parameter-config', // 参数设置
    name: 'parameter-config',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/moduleConfig/parameterConfig/index')
  },
  {
    path: 'pur/template-config', // 模板设置
    name: 'template-config',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/moduleConfig/templateConfig/index')
  },
  {
    path: 'questionnaire-template-detail', //调查表模板详情
    name: 'questionnaire-template-detail',
    component: () =>
      import('@/views/Buyer/moduleConfig/templateConfig/pages/questionnaireDetail.vue')
  },
  {
    path: 'review-template-detail', //评审模板详情
    name: 'review-template-detail',
    component: () => import('@/views/Buyer/moduleConfig/templateConfig/pages/reviewTempDetail.vue')
  },
  {
    path: 'threshold-template-detail', //门槛模板详情
    name: 'threshold-template-detail',
    component: () =>
      import('@/views/Buyer/moduleConfig/templateConfig/pages/thresholdTempDetail.vue')
  },
  {
    path: 'qualificationTempDetail', //资质模板详情
    name: 'qualificationTempDetail',
    component: () =>
      import('@/views/Buyer/moduleConfig/templateConfig/pages/qualificationTempDetail.vue')
  },
  {
    path: 'pur/expert-manage', //专家库列表
    name: 'expert-manage',
    component: () => import('@/views/Buyer/moduleConfig/expertManage/index.vue')
  },
  {
    path: 'expert-detail', //专家库详情
    name: 'expert-detail',
    component: () => import('@/views/Buyer/moduleConfig/expertManage/detail/index.vue')
  }
]

export default Router
