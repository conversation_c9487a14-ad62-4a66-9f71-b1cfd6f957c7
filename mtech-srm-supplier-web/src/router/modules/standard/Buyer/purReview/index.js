const Router = [
  {
    path: 'pur/review',
    name: 'pur-review',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/purReview/scoreSetting/index.vue')
  },
  // 我的评分详情
  {
    path: 'pur/review-scoring-details',
    name: 'pur-review-scoring-details', //切记用 / 要用-
    component: () =>
      import('@/views/Buyer/purReview/scoreSetting/components/myscore/components/index.vue')
  },
  {
    path: 'pur/review-detail',
    name: 'review-detail',
    component: () =>
      import('@/views/Buyer/purReview/scoreSetting/components/template/previewDetail.vue')
  },
  // 我的评分详情页
  {
    path: 'pur/myscore-detail',
    name: 'myscore-detail',
    component: () => import('@/views/Buyer/purReview/scoreSetting/components/myscore/detail.vue')
  }
]
export default Router
