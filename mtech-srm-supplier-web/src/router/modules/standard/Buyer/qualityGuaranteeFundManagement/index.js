const Router = [
  // 质保金管理
  {
    path: 'quality-guarantee-fund-management',
    name: 'quality-guarantee-fund-management',
    component: () => import('@/views/public.vue'),
    meta: {
      keepAlive: true
    },
    children: [
      // 质保金台账
      {
        path: 'standing-book',
        name: 'quality-guarantee-fund-standing-book',
        meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Buyer/qualityGuaranteeFundManagement/qualityGuaranteeFundStandingBook/index.vue'
          )
      },
      // 质保金申请
      {
        path: 'apply',
        name: 'quality-guarantee-fund-apply',
        meta: {},
        component: () =>
          import('@/views/Buyer/qualityGuaranteeFundManagement/qualityGuaranteeFundApply/index.vue')
      },
      // 质保金申请详情
      {
        path: 'apply-detail',
        name: 'quality-guarantee-fund-apply-detail',
        meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Buyer/qualityGuaranteeFundManagement/qualityGuaranteeFundApply/detail.vue'
          )
      }
    ]
  }
]
export default Router
