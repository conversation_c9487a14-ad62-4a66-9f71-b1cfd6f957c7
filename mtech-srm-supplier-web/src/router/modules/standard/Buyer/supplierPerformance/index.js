const Router = [
  {
    path: 'supplier-performance',
    name: 'supplier-performance',
    component: () => import('@/views/public.vue'),
    meta: {
      title: '供应商绩效管理',
      keepAlive: true
    },
    children: [
      {
        path: 'score-setting',
        name: 'score-setting',
        component: () => import('@/views/Buyer/supplierPerformance/scoreSetting'),
        meta: {
          title: '评分设置',
          keepAlive: true
        }
      },
      {
        path: 'score-result',
        name: 'score-result',
        component: () => import('@/views/Buyer/supplierPerformance/scoreResult'),
        meta: {
          title: '评分结果',
          keepAlive: true
        }
      },
      {
        path: 'score-plan',
        name: 'score-plan',
        component: () => import('@/views/Buyer/supplierPerformance/scorePlan'),
        meta: {
          title: '评分计划',
          keepAlive: true
        }
      },
      {
        path: 'score-plan-details',
        name: 'score-plan-details',
        component: () => import('@/views/Buyer/supplierPerformance/scorePlan/details'),
        meta: {
          title: '评分计划详情'
        }
      },
      {
        path: 'score-my',
        name: 'score-my',
        component: () => import('@/views/Buyer/supplierPerformance/scoreMy'),
        meta: {
          title: '我的评分',
          keepAlive: true
        }
      },
      {
        path: 'score-my-details',
        name: 'score-my-details',
        component: () => import('@/views/Buyer/supplierPerformance/scoreMy/details'),
        meta: {
          title: '我的评分详情'
        }
      },
      {
        path: 'graded-recommendation',
        name: 'graded-recommendation',
        component: () => import('@/views/Buyer/supplierPerformance/gradedRecommendation'),
        meta: {
          title: '分级推荐',
          keepAlive: true
        }
      }
    ]
  }
]

export default Router
