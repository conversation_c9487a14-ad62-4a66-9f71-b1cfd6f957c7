const Router = [
  // 返利管理
  {
    path: 'rebate-management',
    name: 'rebate-management',
    component: () => import('@/views/public.vue'),
    meta: {
      title: '返利管理',
      keepAlive: true
    },
    children: [
      // 返利模板
      {
        path: 'rebate-agreement-tem',
        name: 'rebate-agreement-tem',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/rebateManagement/rebateAgreementTem/index.vue')
      },
      // 返利模板详情编辑
      {
        path: 'rebate-agreement-tem-detail',
        name: 'rebate-agreement-tem-detail',
        component: () =>
          import('@/views/Buyer/rebateManagement/rebateAgreementTem/detail/detail.vue')
      },

      // 返利协议创建
      {
        path: 'rebate-agreement-create',
        name: 'rebate-agreement-create',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/rebateManagement/rebateAgreementCreate/index.vue')
      },
      // 返利协议创建-明细
      {
        path: 'rebate-agreement-create-detail',
        name: 'rebate-agreement-create-detail',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/rebateManagement/rebateAgreementCreate/detail.vue')
      },

      // 返利金额计算 - 查询
      {
        path: 'rebate-calculat-amount',
        name: 'rebate-calculat-amount',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/rebateManagement/rebateCalculatAmount/index.vue')
      },
      // 返利金额计算 - 明细
      {
        path: 'rebate-calculat-amount-detail',
        name: 'rebate-calculat-amount-detail',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/rebateManagement/rebateCalculatAmount/detail.vue')
      },

      // 返利报表查询 - 查询
      {
        path: 'rebate-report-query',
        name: 'rebate-report-query',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/rebateManagement/rebateReportQuery/index.vue')
      },
      // 返利基础设置 - 查询
      {
        path: 'rebate-base-setup',
        name: 'rebate-base-setup',
        meta: { keepAlive: true },
        component: () => import('@/views/Buyer/rebateManagement/rebateBaseSetup/index.vue')
      }
    ]
  }
]

export default Router
