const Router = [
  {
    path: 'access',
    name: 'supplieraccess',
    component: () => import('@/views/Buyer/supplierAccess/index'),
    meta: {
      title: '供应商准入管理',
      keepAlive: true
    },
    children: [
      {
        path: 'lifecycledetailsimple',
        name: 'lifecycledetailsimple',
        component: () => import('@/views/Buyer/supplierAccess/lifeCycle.vue'),
        meta: {
          title: '供应商准入管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: 'lifecycle',
    name: 'supplierlifecycle',
    component: () => import('@/views/Buyer/supplierLifecycle/index'),
    meta: {
      title: '供应商生命周期管理',
      keepAlive: true
    }
  },
  {
    path: 'lifecycledetail',
    name: 'lifecycledetail',
    component: () => import('@/views/Buyer/supplierLifecycle/lifeCycle.vue'),
    meta: {
      title: '供应商生命周期管理'
    }
  },
  {
    path: 'lifecycledetailsupplier',
    name: 'lifecycledetailsupplier',
    component: () => import('@/views/Supplier/Customer/customerProfile/components/lifeCycle.vue'),
    meta: {
      title: '供应商生命周期管理',
      keepAlive: true
    }
  },
  {
    path: 'lifecycledetail2',
    name: 'lifecycledetail2',
    component: () => import('@/views/Buyer/supplierLifecycle/children/lifeCycle.vue'),
    meta: {
      title: '供应商生命周期管理'
    }
  },
  {
    path: 'pur/access', // 供应商准入管理
    name: 'pur-access',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/supplierAccessManagement/index')
  },
  {
    path: 'pur/access-detail', // 供应商准入管理详情
    name: 'pur-access-detail',
    component: () => import('@/views/Buyer/supplierAccessManagement/pages/detail.vue')
  },
  {
    path: 'pur/access-threshold-results', // 供应商准入管理门槛结果查看
    name: 'pur-access-threshold-results',
    component: () => import('@/views/Buyer/supplierAccessManagement/pages/thresholdResults.vue')
  }
]

export default Router
