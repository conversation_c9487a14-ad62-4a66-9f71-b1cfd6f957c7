const Router = [
  {
    path: 'infochange',
    name: 'supplierInfoChange',
    component: () => import('@/views/Buyer/supplierInfoChange/index'),
    meta: {
      title: '供应商信息变更',
      keepAlive: true
    }
  },
  {
    path: 'infochange-detail',
    name: 'infoChangeDetail',
    component: () => import('@/views/Buyer/supplierInfoChange/children/InfoChangeDetail.vue'),
    meta: {
      title: '供应商生命周期管理'
    },
    children: [
      {
        path: 'change-task',
        name: 'supplierChangeTask',
        component: () => import('@/views/Buyer/supplierInfoChange/components/taskCenter.vue'),
        meta: {
          title: '供应商生命周期管理',
          keepAlive: true
        }
      }
    ]
  }
]

export default Router
