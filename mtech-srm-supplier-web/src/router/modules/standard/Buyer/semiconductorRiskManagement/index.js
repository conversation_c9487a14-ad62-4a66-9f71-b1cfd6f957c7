const Router = [
  // 半导体风险管理
  {
    path: 'semiconductor-risk-management',
    name: 'semiconductor-risk-management',
    component: () => import('@/views/public.vue'),
    meta: {
      keepAlive: true
    },
    children: [
      // 半导体品类清单维护
      {
        path: 'category-list-maintain',
        name: 'semiconductor-category-list-maintain',
        meta: { keepAlive: true },
        component: () =>
          import('@/views/Buyer/semiconductorRiskManagement/semiconductorCategoryList/index.vue')
      },
      // 半导体风险管理列表
      {
        path: 'list',
        name: 'semiconductor-risk-management-list',
        meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Buyer/semiconductorRiskManagement/semiconductorRiskManagementList/index.vue'
          )
      },
      // 半导体风险管理列表详情
      {
        path: 'list-detail',
        name: 'semiconductor-risk-management-list-detail',
        meta: {},
        component: () =>
          import(
            '@/views/Buyer/semiconductorRiskManagement/semiconductorRiskManagementList/detail.vue'
          )
      }
    ]
  }
]
export default Router
