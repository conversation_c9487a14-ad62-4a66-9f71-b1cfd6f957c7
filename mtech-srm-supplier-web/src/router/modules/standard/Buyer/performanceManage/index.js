const Router = [
  {
    path: 'template-manage',
    name: 'template-manage',
    component: () => import('@/views/public.vue'),
    meta: {
      title: '绩效模板管理',
      keepAlive: true
    },
    children: [
      // 维度清单
      {
        path: 'dimension-list',
        name: 'performance-dimension-list',
        component: () => import('@/views/Buyer/performanceManage/dimensionList/index.vue'),
        meta: {
          title: '维度清单',
          keepAlive: true
        }
      },
      // 指标清单
      {
        path: 'index-list',
        name: 'performance-index-list',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/indexList/index.vue'),
        meta: {
          title: '指标清单',
          keepAlive: true
        }
      },
      // 指标清单 - 编辑
      {
        path: 'index-edit',
        name: 'index-edit',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/indexList/detail.vue')
      },
      // 指标清单 - 新增
      {
        path: 'index-add',
        name: 'index-add',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/indexList/detail.vue')
      },
      // 指标清单 - 详情
      {
        path: 'index-detail',
        name: 'index-detail',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/indexList/detail.vue')
      },
      // 模板清单
      {
        path: 'template-list',
        name: 'template-manage-list',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/templateList/index.vue'),
        meta: {
          title: '模板清单',
          keepAlive: true
        }
      },
      // 模板清单详情
      {
        path: 'template-detail',
        name: 'template-manage-detail', //切记用 / 要用-
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/templateList/detail/index.vue'),
        meta: {
          title: '模板清单详情'
        }
      },
      // 品类模板关系
      {
        path: 'category-relation',
        name: 'category-relation',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/categoryRelation/index.vue'),
        meta: {
          title: '品类模板关系',
          keepAlive: true
        }
      },
      // 绩效组织架构设置
      {
        path: 'org-structure-setting',
        name: 'org-structure-setting',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/orgStructureSetting/index.vue'),
        meta: {
          title: '绩效组织架构设置',
          keepAlive: true
        }
      },
      // 绩效通知函模板
      {
        path: 'performance-letter',
        name: 'performance-letter',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/performanceLetter/index.vue'),
        meta: {
          title: '绩效通知函模板',
          keepAlive: true
        }
      },
      // 绩效通知函模板-详情
      {
        path: 'performance-letter-detail',
        name: 'performance-letter-detail',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/performanceLetter/detail.vue'),
        meta: {
          title: '绩效通知函模板详情'
        }
      },
      // 通知函抄送人设置
      {
        path: 'cc-setting',
        name: 'cc-setting',
        component: () =>
          import('@/views/Buyer/performanceManage/templateManage/ccSetting/index.vue'),
        meta: {
          title: '通知函抄送人设置',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: 'calculation-data',
    name: 'calculation-data',
    component: () => import('@/views/public.vue'),
    meta: {
      title: '绩效计算数据',
      keepAlive: true
    },
    children: [
      // 原材料接收数据
      {
        path: 'raw-material',
        name: 'raw-material',
        component: () =>
          import('@/views/Buyer/performanceManage/calculationData/rawMaterial/index.vue'),
        meta: {
          title: '原材料接收数据',
          keepAlive: true
        }
      },
      //例外供应商
      {
        path: 'except-supplier',
        name: 'calculation-except-supplier',
        meta: { keepAlive: true },
        component: () =>
          import('@/views/Buyer/performanceManage/calculationData/exceptSupplier/index.vue')
      },
      //绩效计算范围
      {
        path: 'calculation-range',
        name: 'calculation-data-range',
        meta: { keepAlive: true },
        component: () =>
          import('@/views/Buyer/performanceManage/calculationData/calculationRange/index.vue')
      },
      {
        path: 'appraiser-setting',
        name: 'appraiser-setting',
        component: () =>
          import('@/views/Buyer/performanceManage/calculationData/appraiserCfg/index.vue'),
        meta: {
          title: '考评人设置-品类',
          keepAlive: true
        }
      },
      {
        path: 'scoring-detail',
        name: 'scoring-detail',
        component: () =>
          import('@/views/Buyer/performanceManage/calculationData/scoringDetail/index.vue'),
        meta: {
          title: '绩效计算打分明细',
          keepAlive: true
        }
      },
      {
        path: 'plan-owner-setting',
        name: 'plan-owner-setting',
        component: () =>
          import('@/views/Buyer/performanceManage/calculationData/planOwnerSetting/index.vue'),
        meta: {
          title: '考评人设置-供应商',
          keepAlive: true
        }
      },
      // 现场评审得分报表
      {
        path: 'review-score-report',
        name: 'review-score-report',
        component: () =>
          import('@/views/Buyer/performanceManage/calculationData/reviewScoreReport/index.vue'),
        meta: {
          title: '现场评审得分报表',
          keepAlive: true
        }
      },
      // 任务中心
      {
        path: 'task-center',
        name: 'task-center',
        component: () =>
          import('@/views/Buyer/performanceManage/calculationData/taskCenter/index.vue'),
        meta: {
          title: '任务中心'
        }
      }
    ]
  },
  {
    path: 'performance-result',
    name: 'performance-result-query',
    component: () => import('@/views/public.vue'),
    meta: {
      title: '绩效结果',
      keepAlive: true
    },
    children: [
      // 综合绩效结果查询-采方
      {
        path: 'pur/comprehensive-query',
        name: 'pur-comprehensive-query',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/comprehensiveQuery/index.vue'),
        meta: {
          title: '综合绩效结果查询',
          keepAlive: true
        }
      },
      // 综合绩效结果查询-采方
      {
        path: 'sup/comprehensive-query',
        name: 'sup-comprehensive-query',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/comprehensiveQuery/index.vue'),
        meta: {
          title: '综合绩效结果查询',
          keepAlive: true
        }
      },
      // 综合绩效结果详情
      {
        path: 'comprehensive-query-detail',
        name: 'comprehensive-query-detail', //切记用 / 要用-
        component: () =>
          import(
            '@/views/Buyer/performanceManage/performanceResult/comprehensiveQuery/detail/index.vue'
          ),
        meta: {
          title: '综合绩效结果详情'
        }
      },
      // 绩效等级规则配置 - 列表
      {
        path: 'classification-rule',
        name: 'classification-rule',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/classificationRule/index.vue'),
        meta: {
          title: '绩效等级规则配置',
          keepAlive: true
        }
      },
      // 绩效等级规则配置 - 编辑
      {
        path: 'rule-edit',
        name: 'rule-edit',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/classificationRule/detail.vue')
      },
      // 绩效等级规则配置 - 新增
      {
        path: 'rule-add',
        name: 'rule-add',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/classificationRule/detail.vue')
      },
      // 绩效等级规则配置 - 详情
      {
        path: 'rule-detail',
        name: 'rule-detail',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/classificationRule/detail.vue')
      },
      //分类绩效查询
      {
        path: 'classification-query',
        name: 'classification-query',
        meta: { keepAlive: true },
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/classificationQuery/index.vue')
      },
      // 综合绩效分析图表 - 全量
      {
        path: 'pur-analysis-chart',
        name: 'pur-analysis-chart',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/analysisChart/index.vue'),
        meta: {
          title: '综合绩效分析图表',
          keepAlive: true
        }
      },
      // 综合绩效分析图表 - 供应商
      {
        path: 'sup-analysis-chart',
        name: 'sup-analysis-chart',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/analysisChart/index.vue'),
        meta: {
          title: '综合绩效分析图表',
          keepAlive: true
        }
      },
      // 黄牌-采方
      {
        path: 'yellow-card',
        name: 'yellow-card',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/yellowCard/index.vue'),
        meta: {
          title: '黄牌',
          keepAlive: true
        }
      },
      // 黄牌-供方
      {
        path: 'yellow-card-sup',
        name: 'yellow-card-sup',
        component: () =>
          import('@/views/Buyer/performanceManage/performanceResult/yellowCard/index.vue'),
        meta: {
          title: '黄牌',
          keepAlive: true
        }
      },
      // 绩效结果汇总 - 采方
      {
        path: 'performance-result-summary',
        name: 'performance-result-summary',
        component: () =>
          import(
            '@/views/Buyer/performanceManage/performanceResult/performanceResultSummary/index.vue'
          ),
        meta: {
          title: '绩效结果汇总',
          keepAlive: true
        }
      }
    ]
  },
  // {
  //   path: 'calculation-data',
  //   name: 'calculation-data',
  //   component: () => import('@/views/public.vue'),
  //   meta: {
  //     title: '绩效计算结果',
  //     keepAlive: true
  //   },
  //   children: [
  //     //例外供应商
  //     {
  //       path: 'except-supplier',
  //       name: 'calculation-except-supplier',
  //       meta: { keepAlive: true },
  //       component: () =>
  //         import('@/views/Buyer/performanceManage/calculationData/exceptSupplier/index.vue')
  //     },
  //     //绩效计算范围
  //     {
  //       path: 'calculation-range',
  //       name: 'calculation-data-range',
  //       meta: { keepAlive: true },
  //       component: () =>
  //         import('@/views/Buyer/performanceManage/calculationData/calculationRange/index.vue')
  //     }
  //   ]
  // },
  {
    path: 'layered-and-graded-management',
    name: 'layered-and-graded-management',
    component: () => import('@/views/public.vue'),
    meta: {
      title: '分层分级管理',
      keepAlive: true
    },
    children: [
      // 分层分级待打分明细
      {
        path: 'scoring-details',
        name: 'scoring-details',
        meta: { keepAlive: true },
        component: () =>
          import('@/views/Buyer/performanceManage/layeredAndGradedManagement/toBeScore/index.vue')
      },
      // 分层分级规则配置
      {
        path: 'rule-configuration',
        name: 'rule-configuration',
        meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Buyer/performanceManage/layeredAndGradedManagement/ruleConfiguration/index.vue'
          )
      },
      // 分层分级规则配置新增详情页面
      {
        path: 'rule-configuration-detail',
        name: 'rule-configuration-detail',
        component: () =>
          import(
            '@/views/Buyer/performanceManage/layeredAndGradedManagement/ruleConfiguration/detail.vue'
          )
      },
      // 自动计算指标配置
      {
        path: 'calculation-indicator-configuration',
        name: 'calculation-indicator-configuration',
        meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Buyer/performanceManage/layeredAndGradedManagement/calculationIndicatorConfiguration/index.vue'
          )
      },
      // 分级分层计算范围
      {
        path: 'calculation-range',
        name: 'calculation-range',
        meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Buyer/performanceManage/layeredAndGradedManagement/calculationRange/index.vue'
          )
      },
      // 分级分层结果查询 - 采方
      {
        path: 'result-query',
        name: 'result-query',
        meta: { keepAlive: true },
        component: () =>
          import('@/views/Buyer/performanceManage/layeredAndGradedManagement/resultQuery/index.vue')
      },
      // 分级分层结果得分详情页面 - 采方
      {
        path: 'result-query-detail',
        name: 'result-query-detail',
        component: () =>
          import(
            '@/views/Buyer/performanceManage/layeredAndGradedManagement/resultQuery/tabsPage/categoryGrade/detail/resultCategoryDetail.vue'
          )
      },
      // 分层分级结果汇总审批页面 - 采方
      {
        path: 'result-summary-approval',
        name: 'result-summary-approval',
        component: () =>
          import(
            '@/views/Buyer/performanceManage/layeredAndGradedManagement/resultSummaryApproval/index.vue'
          )
      },
      // 分层分级结果汇总审批-详情页面 - 采方
      {
        path: 'grade-result-sum-detail',
        name: 'grade-result-sum-detail',
        meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Buyer/performanceManage/layeredAndGradedManagement/resultQuery/tabsPage/gradeResultSummary/detail/gradeResultSumDetail.vue'
          )
      },
      // 分级分层结果查询 - 供方
      {
        path: 'result-query-sup',
        name: 'result-query-sup',
        meta: { keepAlive: true },
        component: () =>
          import(
            '@/views/Supplier/performanceManage/layeredAndGradedManagement/resultQuery/index.vue'
          )
      },
      // 分级分层结果详情页面 - 供方
      {
        path: 'result-query-detail-sup',
        name: 'result-query-detail-sup',
        component: () =>
          import(
            '@/views/Supplier/performanceManage/layeredAndGradedManagement/resultQuery/tabsPage/strategicSupplierScreening/detail/resultCategoryDetail.vue'
          )
      }
    ]
  }
]
export default Router
