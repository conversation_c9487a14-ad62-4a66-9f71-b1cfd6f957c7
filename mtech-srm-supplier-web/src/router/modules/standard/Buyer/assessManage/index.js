/** 采方：考核管理**/

const Router = [
  {
    path: 'pur/purchase-assessmanage-type', // 菜单：考核类型设置
    name: 'purchase-assessmanage-type',
    component: () => import('@/views/Buyer/assessManage/assessType/index.vue')
  },
  {
    path: 'pur/purchase-assessmanage-index', // 菜单：考核指标设置
    name: 'purchase-assessmanage-index',
    component: () => import('@/views/Buyer/assessManage/assessIndex/index.vue')
  },
  {
    path: 'pur/purchase-assessmanage-dataPrepare', // 菜单：数据准备
    name: 'purchase-assessmanage-dataPrepare',
    component: () => import('@/views/Buyer/assessManage/dataPrepare/index.vue')
  },
  {
    path: 'pur/purchase-assessmanage-agreementTemplate', // 菜单：协议书模板
    name: 'purchase-assessmanage-agreementTemplate',
    component: () => import('@/views/Buyer/assessManage/agreementTemplate/index.vue')
  },
  {
    path: 'pur/purchase-assessmanage-assessList', // 菜单：考核单管理
    name: 'purchase-assessmanage-assessList',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/assessManage/assessList/index.vue')
  },
  {
    path: 'pur/purchase-assessmanage-assessListDetail', // 菜单：考核单详情
    name: 'purchase-assessmanage-assessListDetail',
    component: () => import('@/views/Buyer/assessManage/assessList/detail.vue')
  },
  {
    path: 'pur/purchase-assessmanage-appealDeal', // 菜单：申诉处理
    name: 'purchase-assessmanage-appealDeal',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/assessManage/appealDeal/index.vue')
  },
  {
    path: 'pur/purchase-assessmanage-appealDealDetail', // 菜单：考核单详情
    name: 'purchase-assessmanage-appealDealDetail',
    component: () => import('@/views/Buyer/assessManage/appealDeal/detail.vue')
  },
  {
    path: 'pur/dead-materials-claims', // 菜单：呆料索赔清单查询-uat
    name: 'idle-material-claims',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/assessManage/deadMaterials/index.vue')
  },
  {
    path: 'pur/idle-material-claims', // 菜单：呆料索赔清单查询-sit
    name: 'idle-material-claims',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/assessManage/deadMaterials/index.vue')
  },
  {
    path: 'pur/idle-material-claimsDetail', // 菜单：呆料索赔清单详情页
    name: 'idle-material-claimsDetail',
    component: () => import('@/views/Buyer/assessManage/deadMaterials/detail.vue')
  },
  {
    path: 'pur/idle-material-claimsList', // 菜单：呆料索赔单管理
    name: 'idle-material-claimsList',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/assessManage/idleMaterialList/index.vue')
  },
  {
    path: 'pur/idle-material-claimsListDetail', // 菜单：呆料索赔单详情
    name: 'idle-material-claimsListDetail',
    component: () => import('@/views/Buyer/assessManage/idleMaterialList/detail.vue')
  },
  {
    path: 'assess-analysis-report', // 菜单：考核分析报表
    name: 'assess-analysis-report',
    component: () => import('@/views/Buyer/assessManage/assessAnalysisReport/index.vue')
  }
]

export default Router
