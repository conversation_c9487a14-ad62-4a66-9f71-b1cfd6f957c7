const Router = [
  {
    path: 'information-change',
    name: 'informationChange',

    component: () => import('@/views/Buyer/informationChange/index'),
    meta: {
      title: '信息变更管理',
      keepAlive: true
    }
  },
  {
    path: 'informationchange-detail',
    name: 'informationChangeDetail',
    component: () => import('@/views/Buyer/informationChange/children/InfoChangeDetail.vue'),
    meta: {
      title: '供应商生命周期管理'
    },
    children: [
      {
        path: 'change-task',
        name: 'changeTask',
        component: () => import('@/views/Buyer/informationChange/components/taskCenter.vue'),
        meta: {
          title: '供应商生命周期管理',
          keepAlive: true
        }
      }
    ]
  }
]

export default Router
