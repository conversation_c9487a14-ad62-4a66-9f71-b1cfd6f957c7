const Router = [
  {
    path: 'hazardous-substances/info-manage',
    name: 'hazardous-substances/info-manage',
    component: () => import('@/views/Buyer/hazardousSubstances/infoManage/index.vue')
  },
  // 单据详情
  {
    path: 'hazardous-substances/info-manage-detail',
    name: 'hazardous-substances/info-manage-detail',
    component: () => import('@/views/Buyer/hazardousSubstances/infoManage/detail.vue')
  },
  {
    path: 'hazardous-substances/info-config',
    name: 'hazardous-substances/info-config',
    component: () => import('@/views/Buyer/hazardousSubstances/infoConfig/index.vue')
  },
  {
    path: 'hazardous-substances/supplier-category-head',
    name: 'hazardous-substances/supplier-category-head',
    component: () => import('@/views/Buyer/hazardousSubstances/supplierCategoryHead/index.vue')
  },
  {
    path: 'hazardous-substances/sup-info-manage',
    name: 'hazardous-substances/sup-info-manage',
    component: () => import('@/views/Supplier/hazardousSubstances/infoManage/index.vue')
  },
  // 单据详情
  {
    path: 'hazardous-substances/sup-info-manage-detail',
    name: 'hazardous-substances/sup-info-manage-detail',
    component: () => import('@/views/Supplier/hazardousSubstances/infoManage/detail.vue')
  },
  // 有害物质多维度统计报表
  {
    path: 'hazardous-substances/multi-report',
    name: 'hazardous-substances/multi-report',
    component: () => import('@/views/Buyer/hazardousSubstances/multiReport/index.vue')
  }
]
export default Router
