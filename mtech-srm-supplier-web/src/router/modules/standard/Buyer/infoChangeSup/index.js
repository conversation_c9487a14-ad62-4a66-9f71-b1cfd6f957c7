const Router = [
  {
    path: 'sup/info-change-apply',
    name: 'info-change-apply-sup',
    // meta: { keepAlive: true },
    component: () => import('@/views/Buyer/infoChangeSup/index'),
    meta: { title: '信息变更管理' }
  },
  {
    path: 'sup/infoChangeSupDetail',
    name: 'infoChangeSupDetail',
    // meta: { keepAlive: true },//会引起页面异常
    component: () => import('@/views/Buyer/infoChangeSup/children/lifeCycle'),
    meta: { title: '供方信息变更详情' }
  }
]
export default Router
