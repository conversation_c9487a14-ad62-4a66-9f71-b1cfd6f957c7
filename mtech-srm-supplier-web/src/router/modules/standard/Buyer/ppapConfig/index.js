const Router = [
  {
    path: 'pur/ppap-template', // PPAP模板
    name: 'pur-ppap-template',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/ppapFile/ppapMoudle/index.vue')
  },
  {
    path: 'pur/ppap-detail-template', // PPAP模板详情页
    name: 'ppap-detail-template',
    component: () => import('@/views/Buyer/ppapFile/ppapMoudleDetail/index.vue')
  },
  {
    path: 'pur/ppap-doc-manage', // PPAP单据管理
    name: 'pur-ppap-doc-manage',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/ppapFile/ppapManage/index.vue')
  },
  {
    path: 'pur/ppap-doc-manage-detail', // PPAP单据管理详情页
    name: 'pur-ppap-doc-manage-detail',
    component: () => import('@/views/Buyer/ppapFile/ppapManageDetail/index.vue')
  }
]

export default Router
