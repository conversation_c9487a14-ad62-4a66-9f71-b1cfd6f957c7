const Router = [
  {
    path: 'performanceManage',
    name: 'performance-management',
    component: () => import('@/views/Buyer/analysisModule/PerformanceManagement/index'),
    meta: {
      title: '供应商绩效管理',
      keepAlive: true
    }
  },
  // 绩效分析
  {
    path: 'performance-analysis',
    name: 'performance-analysis',
    meta: { keepAlive: true },
    component: () => import('@/views/Buyer/analysisModule/performanceAnalysis/index.vue')
  },
  // 绩效分析-计划清单
  {
    path: 'performance-analysis-plan-detail',
    name: 'performance-analysis-plan-detail',
    component: () => import('@/views/Buyer/analysisModule/performanceAnalysis/planDetail/index.vue')
  },
  // 绩效分析-档案清单
  {
    path: 'performance-analysis-archives-detail',
    name: 'performance-analysis-archives-detail',
    component: () =>
      import('@/views/Buyer/analysisModule/performanceAnalysis/archivesDetail/index.vue')
  }
]

export default Router
