/* Router Modules */
const reqStandard = require.context(`./modules/standard`, true, /\.js$/)

// const requireAll = (requireContext) =>
//   requireContext.keys().map(requireContext);

// const modulesInstance = requireAll(req);
// let SupplierRouteDisplay = [];

// for (let i = 0; i < modulesInstance.length; i++) {
//   if (
//     !modulesInstance[i] ||
//     (!!modulesInstance[i].default && modulesInstance[i].default.length === 0)
//   ) {
//     continue;
//   }
//   SupplierRouteDisplay = [
//     ...SupplierRouteDisplay,
//     ...modulesInstance[i].default,
//   ];
// }

const requireAll = (requireContext) => requireContext.keys().map(requireContext)
// 动态获取路游
const getRoutes = (request) => {
  const modulesInstance = requireAll(request)
  let SupplierRouteDisplay = []

  for (let i = 0; i < modulesInstance.length; i++) {
    if (
      !modulesInstance[i] ||
      (!!modulesInstance[i].default && modulesInstance[i].default.length === 0)
    ) {
      continue
    }
    SupplierRouteDisplay = [...SupplierRouteDisplay, ...modulesInstance[i].default]
  }
  return SupplierRouteDisplay
}

let standardRoutes = getRoutes(reqStandard)
//  ;
//todo 根据业务、菜单，配置子路由
const routes = [
  {
    path: '/'
  },
  {
    path: '/supplier',
    component: () => import('@/views/public.vue'),
    children: [].concat(standardRoutes)
  }
]
console.log('所有路由', routes)

export default routes
