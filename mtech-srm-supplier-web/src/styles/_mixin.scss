@charset "UTF-8";
@function _unpack-shorthand($shorthand) {
  @if length($shorthand) == 1 {
    @return nth($shorthand, 1) nth($shorthand, 1) nth($shorthand, 1) nth($shorthand, 1);
  } @else if length($shorthand) == 2 {
    @return nth($shorthand, 1) nth($shorthand, 2) nth($shorthand, 1) nth($shorthand, 2);
  } @else if length($shorthand) == 3 {
    @return nth($shorthand, 1) nth($shorthand, 2) nth($shorthand, 3) nth($shorthand, 2);
  } @else {
    @return $shorthand;
  }
}
@mixin position(
  $position,
  $box-edge-values
) {
  $box-edge-values: _unpack-shorthand($box-edge-values);
  $offsets: (
    top:    nth($box-edge-values, 1),
    right:  nth($box-edge-values, 2),
    bottom: nth($box-edge-values, 3),
    left:   nth($box-edge-values, 4),
  );

  position: $position;

  @each $offset, $value in $offsets {
    @if _is-length($value) {
      #{$offset}: $value;
    }
  }
}

@mixin col-solid-point {
  &::before {
    content: "";
    width: 8px;
    height: 8px;
    display: inline-block;
    border-radius: 50%;
    margin-right: 10px;
  }
}

@mixin col-mark {
  font-size: 12px;
  padding: 2px 6px;
  line-height: 1;
  border-radius: 2px;
  overflow: hidden;
}

@mixin sticky-col {
  position: sticky !important;
  left: 0px;
  z-index: 1;
  border-right: 1px solid var(--plugin-dg-shadow-color);
  background-color: #f6f7fb;
}
