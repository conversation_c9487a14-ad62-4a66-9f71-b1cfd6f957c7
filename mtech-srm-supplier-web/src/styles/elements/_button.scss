button {
    font: {
        family: $primary-font-family;
    }
}

.button {
    outline: none;
    cursor: pointer;
    border: 1px solid transparent;
    white-space: nowrap;
    background-color: transparent;
    @include control-size($button-padding-default-vertical, $button-padding-default-horizontal, auto, $button-font-size-default, $button-border-radius-default);
    &[disabled],
    &.disabled {
        @include button-variant($button-disabled-color, $button-disabled-bg, $button-disabled-border);
        cursor: not-allowed;
        &:hover {
            background: $button-disabled-bg;
            color: $button-disabled-color;
            border-color: $button-disabled-border;
        }
    }
}

//fullWidth
.button-fullWidth {
    display: block;
    width: 100%;
}

// Button color
// --------------------------------------------------
// default appears gray
.button-default {
    @include button-variant($button-default-color, $button-default-bg, $button-default-border);
}

// primary appears light-blue
.button-primary {
    @include button-variant($button-primary-color, $button-primary-bg, $button-primary-border);
}

// Success appears as green
.button-success {
    @include button-variant($button-success-color, $button-success-bg, $button-success-border);
}

// Danger and error appear as red
.button-danger {
    @include button-variant($button-default-color, $button-default-bg, $button-default-border);
}

//
.button-border {
    @include button-border($button-border-width, $button-border-color, $white, $button-font-color);
}

// Button Sizes
// --------------------------------------------------
.button-small {
    @include control-size($button-padding-small-vertical, $button-padding-small-horizontal, auto, $button-font-size-small, $button-border-radius-small);
}

.button-medium {
    @include control-size($button-padding-medium-vertical, $button-padding-medium-horizontal, auto, $button-font-size-medium, $button-border-radius-medium);
}

.button-large {
    @include control-size($button-padding-large-vertical, $button-padding-large-horizontal, auto, $button-font-size-large, $button-border-radius-large);
}