input, select, textarea {
  font: {
    size: $font-size-default;
    family: $primary-font-family;
  }
  border: 1px solid #c3c3c3;
  background-color: $input-bg;
  color: $primary-font-color;
}

input {
  &[type=checkbox] {
    visibility: hidden;
  }
  &[type=radio] {
    display: none;
  }
  &[type=number] {
    padding-right: 0;
  }

}
::-webkit-input-placeholder { /* WebKit, Blink, Edge */
  color: $input-color-placeholder;
}
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
  color: $input-color-placeholder;
}
::-moz-placeholder { /* Mozilla Firefox 19+ */
  color: $input-color-placeholder;
}
:-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: $input-color-placeholder;
}

input[disabled], select[disabled], textarea[disabled] {
  cursor: not-allowed;
  background-color: $input-bg-disabled;
}

select {
  appearance: none;
}

.form-content {
  padding: 5px 0;
}

//label and input  horizontal centering control
.form-inline {
  .form-label {
    display: inline-block;
    min-width: 60px;
    text-align: right;
    &.form-label-textArea {
      vertical-align: top;
    }
  }
  .form-group {
    display: inline-block;
  }
  .form-control {
    display: inline-block;
    width: auto;
  }
}
.select-group {
  .select-control {
    display: inline-block;
  }
}
// responsive form input width ,select width
.form-responsive {
  .form-group {
    width: 100%;
  }
  .form-control {
    width: calc(100% - 104px);
    .select {
      width: 100%;
    }
  }
}

// input,select  default
.input, .select {
  @include control-size($input-padding-default-vertical, $input-padding-default-horizontal, $input-height-default, $input-font-size-default, $input-border-radius-default);
}

// input,select  small
.input-small, .select-small {
  @include control-size($input-padding-small-vertical, $input-padding-small-horizontal, $input-height-small, $input-font-size-small, $input-border-radius-small);
}

// input,select  medium
.input-medium, .select-medium {
  @include control-size($input-padding-medium-vertical, $input-padding-medium-horizontal, $input-height-medium, $input-font-size-medium, $input-border-radius-medium);
}

// input,select  large
.input-large, .select-large {
  @include control-size($input-padding-large-vertical, $input-padding-large-horizontal, $input-height-large, $input-font-size-large, $input-border-radius-large);
}

//input  has icon
.has-icon {
  position: relative;
  .form-control {
    padding-left: 30px;
  }
  .icon {
    position: absolute;
    left: 0;
    padding: $input-padding-default-vertical $input-padding-default-horizontal;
    text-align: center;
    border-right: 1px solid $border-color;
    white-space: nowrap;
    vertical-align: middle;
  }
}

.radioControl-gray {
  .radioContainer {
    display: inline-block;
    margin-right: 5px;
    input + label {
      position: relative;
      display: block;
      cursor: pointer;
      vertical-align: middle;
      left: 1px;
      &:before {
        position: relative;
        top: 2px;
        margin-right: 5px;
        display: inline-block;
        width: 18px;
        height: 18px;
        content: '';
        border: 1px solid #c0c0c0;
        border-radius: 50%;
      }
    }
    input:checked + label {
      &:after {
        background: $light-blue;
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        top: 7px;
        position: absolute;
        left: 5px;
      }
    }
  }

}

.checkboxGroup {
  .checkboxContainer {
    display: inline-block;
    margin-right: 5px;
  }

}
