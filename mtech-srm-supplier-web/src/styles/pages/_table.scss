// 表格高度问题
.template-height {
  .mt-data-grid {
    height: 100%;

    > .e-grid {
      height: calc(100% - 40px);
    }
  }
}

.template-height.has-page {
  .repeat-template .mt-data-grid > .e-control {
    height: calc(100% - 40px)!important;
  }
}

// 高度有问题的表格
.self-set-table {
  .repeat-template {
    height: calc(100% - 50px);

    .template-wrap {
      height: 100%;
      .grid-container {
        height: calc(100% - 50px);

        .mt-data-grid {
          height: 100%;

          > .e-control {
            height: calc(100% - 40px)!important;

            .e-gridcontent {
              height: calc(100% - 45px);
              overflow: auto;

              > .e-content {
                height: auto;
              }
            }

            // 如果没有数据
            .hasFrozenColumn + .e-gridcontent {
              height: 0;
            }
          }
        }
      }
    }
  }
}


// 详情页里的要求：tab固定，表格头固定
// 最外层的detail-fix-wrap；
.detail-fix-wrap {
  display: flex;
  flex-direction: column;

  // 底部几个tab外层的bottom-tables；
  .bottom-tables {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    // overflow-x: hidden;

    .mt-tabs {
      flex-shrink: 0;
      .mt-tabs-container {
        width: 100%;
      }
    }

    // 明细表格的bottom-box
    .bottom-box {
      flex: 1;
      display: flex;
      overflow: auto;

      // 使用mt-page时造成 样式问题
      .mt-pagertemplate {
        margin: 0;
        padding: 10px 0;
      }

      // 包裹data-grid或列模板的
      .grid-wrap {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        // 情况1： 原生的daga-grid
        .mt-data-grid {
          height: 100%;
          overflow: auto;

          .e-grid {
            height: 100%;
            display: flex;
            flex-direction: column;

            .e-headerchkcelldiv {
              padding-left: 0;
            }

            .e-gridcontent {
              flex: 1;
              display: flex;

              .e-content {
                flex-direction: row !important;
                overflow: auto;
              }
            }
          }
        }

        // 情况2：不分页的列模板
        .repeat-template,
        .template-wrap {
          height: 100%;

          .grid-container {
            flex: 1;
            height: auto;
            overflow: auto;

            .e-content {
              flex: 1;
            }
          }
        }

        // 情况3：带分页的列模板
        &.grid-wrap-page {
          .e-grid {
            height: calc(100% - 40px);
          }
          .mt-pagertemplate .mt-select-index {
            float: left;
          }
        }

      }

    }
  }
}

// 不分页的列模板 解决高度自适应和滚动条问题
.grid-wrap-not-paging {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .repeat-template,
  .template-wrap {
    height: 100%;

    .grid-container {
      flex: 1;
      height: auto;
      overflow: hidden;
      .mt-data-grid {
        height: 100%;
        .e-control.e-grid {
          height: 100%;
          display: flex;
          flex-direction: column;
          .e-gridheader {
            flex-shrink: 0;
            padding: 0 !important;
          }
          .e-gridcontent {
            flex: 1;
            overflow: auto;
            display: flex;
            flex-direction: column;

            .e-content {
              flex: 1;
              display: block !important;

              .e-movablecontent {
                overflow: auto !important;
              }
            }
          }

          .e-scrollbar {
            display: flex;
          }
        }
      }
    }
  }
}

// 使列模板组件首列固定
.frozenFistColumns {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
      }

      & tbody td:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
    }
  }
}


// tamplate-page样式设置（边框、行高）
.e-grid td.e-rowcell {
  height: 32px !important;
}
.grid-edit-column {
  height: 32px !important;
}
.e-grid td {
  border-right: 1px solid #e6e9ed !important;
}
.e-grid th.e-headercell:first-of-type {
  border-left-width: 0 !important;
}
.e-grid {
  border: 1px solid #e6e9ed !important;
  border-top: none !important;
}

// 解决自定义多选，多选框框未居中问题
.e-grid.e-responsive .e-headercelldiv {
  padding: 0 8px !important;
}
