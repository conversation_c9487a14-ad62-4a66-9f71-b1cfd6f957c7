.e-input-group input.e-input, .e-input-group.e-control-wrapper input.e-input {
  font-size: 12px !important;
}

.mt-form input.e-input, .mt-form .e-input-group input.e-input, .mt-form .e-input-group.e-control-wrapper input.e-input, 
.mt-form textarea.e-input, .mt-form .e-input-group textarea.e-input, .mt-form .e-input-group.e-control-wrapper textarea.e-input {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.mt-form .e-input-group input.e-input:focus, .mt-form .e-input-group.e-control-wrapper input.e-input:focus, 
.mt-form .e-input-group textarea.e-input:focus, .mt-form .e-input-group.e-control-wrapper textarea.e-input:focus, 
.mt-form .e-input-group.e-input-focus input.e-input, .e-input-group.e-control-wrapper.e-input-focus input.e-input {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.mt-form .e-ddt.e-input-group.e-control-wrapper .e-input-group-icon.e-ddt-icon, .mt-form .e-ddt.e-float-input.e-control-wrapper .e-input-group-icon.e-ddt-icon {
  background-color: transparent !important;
}

.mt-form .e-multi-select-wrapper {
  height: 22px !important;
  min-height: 22px !important;
}
.mt-form .e-multi-select-wrapper input[type='text'] {
  background: none !important;
  border: 0 !important;
  font-family: inherit;
  font-size: 12px !important;
  font-weight: 400 !important;
  height: 22px !important;
  min-height: 22px !important;
  outline: none !important;
  padding: 0 !important;
}
.mt-form .e-multi-select-wrapper .e-delim-values {
  font-size: 12px !important;
  line-height: 22px !important;
  display: inline-block;
  height: 22px !important;
}

/**--------ant-select----start-----*/
.search-area .ant-select-selection, .search-area .ant-select-selection--multiple {
  height: 23px;
  min-height: 22px;
}
.ant-select-selection--multiple > ul > li, .ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
  height: 20px;
  margin-top: 3px;
  line-height: 20px;
  font-size: 12px;
}
.ant-select-selection--multiple .ant-select-selection__clear, .ant-select-selection--multiple .ant-select-arrow {
  top: 12px;
}
/**--------ant-select----end-----*/

/**--------mt-select组件总的input框----start-----*/
.mt-form input.e-input, .mt-form .e-input-group input.e-input, .mt-form .e-input-group.e-control-wrapper input.e-input,
.mt-form textarea.e-input, .mt-form .e-input-group textarea.e-input, .mt-form .e-input-group.e-control-wrapper textarea.e-input {
  height: 22px !important;
}
.mt-form .e-input-group .e-input-group-icon, .mt-form .e-input-group.e-control-wrapper .e-input-group-icon {
  margin-bottom: 0px !important;
}
/**--------mt-select组件总的input框----end-----*/

/**--------校验信息----start-----*/ 
.mt-form-item .error-label-label {
  padding-top: 0px !important;
}
/**--------校验信息----end-----*/ 

.mt-form-item-topLabel .mtSelect[data-v-731999d0] .e-input-group {
  height: 22px !important;
  .text-ellipsis {
    font-size: 12px;
  }
  
}

/*--------------下拉选择的icon-------------*/
.e-ddl.e-input-group.e-control-wrapper .e-ddl-icon::before {
  content: '\e969' !important;
  font-size: 12px !important;
}

/*---------------disabled状态---------------*/
.mt-form .e-filled.e-input-group:not(.e-float-input) input.e-input, .mt-form .e-filled.e-input-group:not(.e-float-input).e-control-wrapper input.e-input,
.mt-form .e-disabled .e-multi-select-wrapper, .mt-form .e-disabled .e-dropdownbase {
  height: 22px !important;
  min-height: 22px !important;
}

/*--------------详情页-from---------------*/
.top-info .main-bottom .mt-form-item {
  margin-bottom: 10px;
}
