.button-group {
  height: 100%;
  border-radius: 0 0 8px 8px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 12px;
  span {
    display: block;
    height: 28px;
    line-height: 26px;
    padding: 0 16px;
    border-radius: 4px;
    margin-left: 12px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    border: 1px solid #4a556b;
    box-sizing: border-box;
  }
  span[type='info'] {
    background: #fff;
    color: #4a556b;
  }
  span[type='primary'] {
    background: #4a556b;
    color: #fff;
  }
}
.detail-top{
  position: sticky;
  top: 0px;
  z-index: 99;
  background-color: rgba(255, 255, 255, 1);
}
.accordion-title {
  height: 25px;
  line-height: 25px;
  border-left: 5px solid #4a556b;
  padding-left: 7px;
  border-radius: 4px 0 0 4px;
  font-weight: bold;
  font-size: 16px;
}
