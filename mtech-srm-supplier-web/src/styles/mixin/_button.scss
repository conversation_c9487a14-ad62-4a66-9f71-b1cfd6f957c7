//button-bg-color
@mixin button-variant($button-color, $button-bg, $button-border-color) {
  color: $button-color;
  border-color: $button-border-color;
  background-color: $button-bg;
  &:hover {
    background: darken($button-bg, 8%);
    transition: all 0.3s ease;
  }
  &:active {
    background: darken($button-bg, 25%);
  }

}
//button-border-color
@mixin button-border($button-border-width, $button-border-color, $white, $button-font-color) {
  border: $button-border-width solid $button-border-color;
  //background: $white;
  color: $button-font-color;
  &:hover {
    color: $button-border-font-color-hover;
    border-color: $button-border-font-color-hover;
  }
  &:active {
    color: $button-border-font-color-active;
    border-color: $button-border-font-color-active;

  }
}

