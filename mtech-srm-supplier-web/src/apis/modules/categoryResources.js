import { API } from '@mtech-common/http'
const NAME = 'categoryResources'
// const PROXY_BASE = '/analysis'
// import qs from 'qs'
const APIS = {
  // 能力地图打点明细页 - 获取详情数据（有id
  getMarkDetailList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/dot/queryDetailById`, data)
  },
  // 能力地图打点明细页 - 新增详情数据（无id
  addMarkDetailList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/dot/queryDetailByTemplateId`, data)
  },
  // 设置能力地图 - 生效
  temEffective: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/template/effective`, data)
  },
  // 设置能力地图 - 失效
  temInvalid: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/template/invalid`, data)
  },
  // 设置能力地图 - 删除
  temDeleteByIds: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/template/deleteByIds`, data)
  },
  // 设置能力地图 - 详情页
  getTemDetail: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/template/queryDetailById`, data)
  },
  // 设置能力地图 - 详情页- 维度参数定义提交
  dimParamSubmit: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/dim/param/submit`, data)
  },
  // 设置能力地图 - 详情页- 能力明细导入
  dimParamImport: (data = {}) => {
    return API.post('/analysis/tenant/buyer/supplier/ability/dim/param/detail/import', data, {
      responseType: 'blob'
    })
  },
  // 设置能力地图 - 详情页- 能力明细模板下载
  dimParamExport: (data = {}) => {
    return API.post('/analysis/tenant/buyer/supplier/ability/dim/param/detail/export', data, {
      responseType: 'blob'
    })
  },
  // 设置能力地图 - 详情页- 保存
  abilitySaveOrUpdate: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/template/saveOrUpdate`, data)
  },
  // 品类列表
  getCategoryListAll: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/category/paged-query`, data)
  },
  // 能力明细打点 - 详情页- 保存更新
  markSaveOrUpdate: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/dot/saveOrUpdate`, data)
  },
  // 能力地图打点 - 删除
  markDeleteByIds: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/dot/delete`, data)
  },
  // 能力地图打点 - 详情页- 导出
  markDetailExport: (data = {}) => {
    return API.post('/analysis/tenant/buyer/supplier/ability/dot/export', data, {
      responseType: 'blob'
    })
  },
  // 能力地图打点 - 详情页- 导入
  markDetailImport: (data = {}) => {
    return API.post('/analysis/tenant/buyer/supplier/ability/dot/import', data, {
      responseType: 'blob'
    })
  },
  // 能力地图打点 - 提交审批
  markSubmitApprove: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/dot/submitApprove`, data)
  },
  // 能力地图打点 - 发布
  markPublish: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/supplier/ability/dot/publish`, data)
  },
  // 能力地图打点详情 - 获取审批人列表（新增时 没有打点任务的时候，只有模板id）
  getUserConfigByTemplateId: (data = {}) => {
    return API.post(
      `analysis/tenant/buyer/supplier/ability/template/queryUserConfigByTemplateId`,
      data
    )
  },
  // 能力地图打点详情 - 获取审批人列表（有打点任务的时候）
  getUserConfigByDotId: (data = {}) => {
    return API.post(`analysis/tenant/buyer/supplier/ability/dot/queryUserConfigByDotId`, data)
  },
  // 能力地图打点详情 - 获取供应商列表
  getSupplierList: (data = {}) => {
    return API.post(`supplier/tenant/buyer/partner/archive/query/queryBySupplierInfo`, data)
  },

  // 品类资源库 - 查询
  getCategoryQueryDimParam: (data = {}) => {
    return API.get(`/analysis/tenant/buyer/category/resource/board/queryDimParam`, data)
  },

  // 品类资源库 - 查询供应商详情
  getCategoryQueryByCondition: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/category/resource/board/queryByCondition`, data)
  },
  // 品类资源库 - 详情页- 导出
  resourceBoardExport: (data = {}) => {
    return API.post('/analysis/tenant/buyer/category/resource/board/export', data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
