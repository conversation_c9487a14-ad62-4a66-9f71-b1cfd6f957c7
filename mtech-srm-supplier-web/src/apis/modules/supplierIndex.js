import { API } from '@mtech-common/http'
const NAME = 'supplierIndex'
const APIS = {
  // 改变供应商指标状态
  changeStatus: (data = {}) => {
    return API.put(`/analysis/tenant/buyer/assess/index/updateStatus`, data)
  },
  // 删除供应商指标
  delIndex: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/del`, data)
  },
  // 获取指标维度下拉列表
  getDimensionList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/dimension/listQuery`, data)
  },
  // 获取数据集下拉列表
  getTable: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/data/table/queryAll`, data)
  },
  // 获取数据集下拉列表
  queryByDimensionType: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/data/table/queryByDimensionType`, data)
  },
  // 新增指标
  addIndex: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/add`, data)
  },
  // 编辑指标
  editIndex: (data = {}) => {
    return API.put(`/analysis/tenant/buyer/assess/index/update`, data)
  },
  // 获取指标详情
  getIndexDetail: (id = '') => {
    return API.get(`/analysis/tenant/buyer/assess/index/detail?id=` + id)
  },
  getOrgTree: (data = {}) => {
    return API.post(`masterDataManagement/tenant/organization/getStatedLimitTree`, data)
  },
  currentTenantEmployees: (data = {}) => {
    return API.get(`masterDataManagement/tenant/employee/currentTenantEmployees`, data)
  }
}
export default {
  NAME,
  APIS
}
