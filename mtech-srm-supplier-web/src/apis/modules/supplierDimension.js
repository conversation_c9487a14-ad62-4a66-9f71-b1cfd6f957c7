import { API } from '@mtech-common/http'
const NAME = 'supplierDimension'
const APIS = {
  // 改变供应商维度状态
  changeStatus: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/dimension/updateStatus`, data)
  },
  // 删除维度
  del: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/dimension/del`, data)
  },
  // 新增维度
  add: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/dimension/add`, data)
  },
  // 修改维度
  update: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/dimension/update`, data)
  }
}
export default {
  NAME,
  APIS
}
