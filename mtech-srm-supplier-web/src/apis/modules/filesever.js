import { API } from '@mtech-common/http'
const NAME = 'fileService'
const APIS = {
  //文件上传-私密类型
  uploadPrivateFile: (data = {}) => API.post(`/file/user/file/uploadPrivate?useType=2`, data),

  //文件下载-私密文件
  downloadPrivateFile: (data = {}) =>
    API.get(`/file/user/file/downloadPrivateFile?useType=2`, data, {
      responseType: 'blob'
    }),

  //文件上传-私密类型(useType=1)
  uploadPrivateFileTypeOne: (data = {}) =>
    API.post(`/file/user/file/uploadPrivate?useType=1`, data),
  //文件下载-私密文件(useType=1)
  downloadPrivateFileTypeOne: (data = {}) =>
    API.get(`/file/user/file/downloadPrivateFile?useType=1&id=${data}`, '', {
      responseType: 'blob'
    }),

  // 文件下载--公开文件
  downloadPublicFile: (data = {}) =>
    API.get(`/file/user/file/downloadPublicFile`, data, {
      responseType: 'blob'
    }),

  // 文件预览
  getMtPreview: (data = {}) => API.get(`/file/user/file/previewUrl`, data),
  filePreview: (data = {}) => API.get(`/file/user/file/mtPreview`, data),
  // 文件上传
  uploadPublicFile: (data = []) => {
    return API.post(`/file/user/file/uploadPublic`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}
export default {
  NAME,
  APIS
}
