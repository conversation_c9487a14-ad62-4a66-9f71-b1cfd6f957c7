// 供应商绩效管理
import { API } from '@mtech-common/http'
const NAME = 'performanceManagement' // 接口前缀名（接口导出对象，用于.出里面的方法，不能重复，最好用自己的文件名）
const BUYER_PROXY_ANALYSIS = '/analysis/tenant/buyer/assess/archive/result'
const SUPPLIER_PROXY_ANALYSIS = '/analysis/tenant/supplier/assess/archive/result'
const APIS = {
  // 供方-绩效管理列表
  //   categoryStrategy: `${PROXY_ANALYSIS}/pageQuery`,
  // 采方-得分明细（弹窗）- 雷达图
  buyerDetailIndexScoreList: (data = []) => {
    return API.get(`${BUYER_PROXY_ANALYSIS}/detail/indexScoreList`, data)
  },
  // 供方-得分明细（弹窗）- 雷达图
  supplierDetailIndexScoreList: (data = []) => {
    return API.get(`${SUPPLIER_PROXY_ANALYSIS}/detail/indexScoreList`, data)
  },
  //绩效档案日志分页列表 根据清单类型查询
  pageQuery: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/board/pageQuery`, data)
  },
  // /analysis/tenant/buyer/assess/board/pageQuery
  buyerPublishList: (data = []) => {
    return API.post(`${BUYER_PROXY_ANALYSIS}/publish`, data)
  }
}
export default {
  NAME,
  APIS
}
