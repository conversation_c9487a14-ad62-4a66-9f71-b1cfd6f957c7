// 供应商-绩效管理-评分设置
import { API } from '@mtech-common/http'
const NAME = 'performanceScoreSetting'
const PROXY_ANALYSIS = '/analysis'
const PROXY_ANALYSISAS = '/supplier'
const PROXY_MASTER = '/masterDataManagement'
const PROXY_IAM = '/iam'
const DEFAULTPARAM = {
  condition: '',
  page: {
    current: 1,
    size: 1000
  },
  pageFlag: false
}
const APIS = {
  /*
    维度设置
  */
  //维度列表
  dimensionList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/list`,
  dimensionSelectList: (data = {}) => {
    //维度下拉列表
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/select`, data)
  },
  addDimension: (data = {}) => {
    //增加维度
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/data`, data)
  },
  editDimension: (data = {}) => {
    //修改维度
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/data`, data)
  },
  deleteDimension: (data = {}) => {
    //删除维度
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/data/ids`, data)
  },
  updateDimension: (data = {}) => {
    //更改维度状态
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/status`, data)
  },
  dimensionDetail: (data = {}) => {
    //维度信息-详情
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/detail`, data)
  },

  /*
    指标设置
  */
  //指标列表
  indexList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/index/list`,
  indexSelectList: (data = {}) => {
    //指标下拉列表
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/index/select`, data)
  },
  addIndex: (data = {}) => {
    //增加指标
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/index/data`, data)
  },
  editIndex: (data = {}) => {
    //修改指标
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/config/index/data`, data)
  },
  deleteIndex: (data = {}) => {
    //删除指标
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/index/data/ids`, data)
  },
  updateIndex: (data = {}) => {
    //更改指标状态
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/config/index/status`, data)
  },
  indexDetail: (data = {}) => {
    //指标-详情
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/config/index/detail`, data)
  },

  /*
    绩效考评模板
  */
  //模板列表
  templateList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/list`,
  templateSelectList: (data = {}) => {
    //模板下拉列表
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/config/template/select`, data)
  },
  addTemplate: (data = {}) => {
    //增加模板
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data`, data)
  },
  editTemplate: (data = {}) => {
    //修改模板
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data`, data)
  },
  deleteTemplate: (data = {}) => {
    //删除模板
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data/ids`, data)
  },
  updateTemplate: (data = {}) => {
    //更改模板状态
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/config/template/status`, data)
  },
  templateDetail: (data = {}) => {
    //模板信息-详情
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/config/template/info`, data)
  },
  // saveTemplateDetal: (data = {}) => {
  //   //模板信息-保存
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/info`,
  //     data
  //   );
  // },
  saveTemplate: (data = {}) => {
    //模板信息-保存
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/template/review/item/save`, data)
  },

  /*
    公司默认评分人
  */
  //评分人列表
  raterList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/list`,
  addRater: (data = {}) => {
    //增加评分人
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data`, data)
  },
  editRater: (data = {}) => {
    //修改评分人
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data`, data)
  },
  deleteRater: (data = {}) => {
    //删除评分人
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data/ids`, data)
  },
  updateRater: (data = {}) => {
    //更改评分人
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/status`, data)
  },

  //评分进度-列表
  getScoreProcess: `${PROXY_ANALYSIS}/tenant/buyer/process/query`,

  //评分计划-列表
  getPlanList: (data = DEFAULTPARAM) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/list`, data)
  },

  /*
    主数据
  */
  getStatedLimitTree: (data = {}) => {
    //租户级-组织结构接口-获取指定范围树结构数据
    //公司：ORG02，部门：ORG03，岗位：ORG04，员工：ORG05
    return API.post(`${PROXY_MASTER}/tenant/organization/getStatedLimitTree`, data)
  },

  /**
   * @description: 获取指定组织下指定组织层级节点列表
   * @param {
   *    organizationLevelCodes: [],//指定层级编码列表 公司：ORG02，部门：ORG03，岗位：ORG04，工厂：ORG06
   *    organzationIds: []//指定组织id列表
   * }
   * @return {}
   */
  findSpecifiedChildrenLevelOrgs: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
  },

  /**
   * @description: 获取指定部门下员工列表
   * @param {}
   * @return {}
   */
  findEmployeesInDeparments: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/findEmployeesInDeparments`, data)
  },

  // 根据字典类型编码获取字典详情
  dictionaryGetList: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/dict-item/dict-code`, data)
  },

  /*
    租户级-部门接口
  */
  // 模糊查询    获取部门列表
  getDepartmentList: (data = {}) => API.put(`${PROXY_MASTER}/tenant/department/fuzzy-query`, data),

  /*
    租户级-雇员接口
  */
  // 根据姓名模糊查询
  getEmployeeList: (data) => API.get(`${PROXY_MASTER}/tenant/employee/fuzzy-query`, data),

  /*
    IAM   租户级-账户表接口
  */
  //获取当前登录人信息
  getUserDetail: (data) => API.get(`${PROXY_IAM}/tenant/account/user-detail`, data)
}

export default {
  NAME,
  APIS
}
