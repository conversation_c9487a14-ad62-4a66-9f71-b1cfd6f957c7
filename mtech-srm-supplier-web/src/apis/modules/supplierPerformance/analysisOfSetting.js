// 供应商-绩效管理-分析设置
import { API } from '@mtech-common/http'
const NAME = 'analysisOfSetting' // 接口前缀名
const PROXY_ANALYSIS = '/analysis'
const PROXY_ANALYSISAS = '/supplier'
const PROXY_MASTER = '/masterDataManagement' // 主数据取公司接口前缀名
const APIS = {
  //品类策略列表
  categoryStrategy: `${PROXY_ANALYSIS}/tenant/buyer/assess/setting/range/pageQuery`,

  //例外供应商分页查询
  cateexceptpageQuery: `${PROXY_ANALYSIS}/tenant/buyer/assess/setting/exclude/pageQuery`,

  //评分人分页查询
  catassessQuery: `${PROXY_ANALYSIS}/tenant/buyer/assess/setting/rater/pageQuery`,
  //品类策略新增
  categoryStrategyAdd: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/range/add`, data)
  },
  //品类策略 新增 公司列表
  TreeByAccount: (data = []) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/getFuzzyCompanyTree`, data)
  },
  // sit特性：获取公司列表
  TreeByAccountInSit: (data = []) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/getStatedLimitTree`, data)
  },
  //品类策略 新增 根据公司取计划模板接口
  queryPlanByCompany: (data = []) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/assess/plan/queryPlanByCompanyId`, data)
  },
  //评分人设置 新增 根据计划获取的品类接口
  PlanTemplateRangeCat: (data = []) => {
    return API.get(
      `${PROXY_ANALYSIS}/tenant/buyer/category/getPlanCategoryRangeCatTreeByPlanId`,
      data
    )
  },
  //品类策略 新增 根据计划获取的品类接口
  TreeByPlanIdangeCat: (data = []) => {
    return API.get(
      `${PROXY_ANALYSIS}/tenant/buyer/category/getPlanTemplateRangeCatTreeByPlanId`,
      data
    )
  },
  //更改计划分析范围状态(品类策略)  启用 停用
  categorySupdateStatus: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/range/updateStatus`, data)
  },
  //编辑计划分析范围(品类策略)
  categoryStupdate: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/range/update`, data)
  },
  //删除计划分析范围(品类策略)
  categorydel: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/range/del`, data)
  },
  //计划分析范围详情(品类策略)
  categoryStdetail: (data = []) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/range/detail`, data)
  },
  //导入计划分析范围(品类策略)
  categoryimportData: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/range/importData`, data)
  },
  //计划分析范围下拉列表查询(品类策略)
  categoryStlistQuery: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/range/listQuery`, data)
  },
  //新增例外供应商
  cateexceptionAdd: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/exclude/add`, data)
  },
  //供应商列表
  cateexquery: (data = []) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/process/manager/query`, data)
  },
  // 导出例外供应商
  fileExportSupplier: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/exclude/exportExcel`, data, {
      responseType: 'blob'
    })
  },
  //删除例外供应商
  cateexceptiondel: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/exclude/del`, data)
  },
  //例外供应商详情
  cateexceptiondetail: (data = []) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/exclude/detail`, data)
  },
  //例外供应商导入
  cateexceptlistQuery: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/exclude/listQuery`, data)
  },

  //编辑例外供应商
  cateexcepupdate: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/exclude/update`, data)
  },
  //手动评分人新增
  cateexaddpoper: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/rater/add`, data)
  },
  //手动评分人编辑
  raterUpdate: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/rater/update`, data)
  },
  //手动评分人新增
  cateexadellse: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/setting/rater/del`, data)
  },
  //手动评分人新增 获取当前组织下部门列表
  getChildrenDepartmentOrganization: (data = []) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/getChildrenDepartmentOrganization`, data)
  },
  //手动评分人新增 获取当前组织下部门下的人员列表
  getOrganizationEmployees: (data = []) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/getOrganizationEmployees`, data)
  },
  //手动评分人新增 公司下的指标
  selectIndexByPlanId: (data = []) => {
    return API.post(
      `${PROXY_ANALYSIS}/tenant/buyer/assess/plan/selectIndexByPlanId?planId=` + data.planId,
      data
    )
  },
  // 提交评审申请
  submitReviewApply: (data = []) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/review/task/submit/apply`, data)
  },
  // 提交评审结果
  submitReviewResult: (data = []) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/review/task/submit/result`, data)
  }
}
export default {
  NAME,
  APIS
}
