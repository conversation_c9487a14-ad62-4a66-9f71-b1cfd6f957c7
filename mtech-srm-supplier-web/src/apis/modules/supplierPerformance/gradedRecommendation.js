// 供应商-绩效管理-我的评分
import { API } from '@mtech-common/http'
const NAME = 'performanceScoreGrade'
const PROXY_ANALYSIS = '/analysis'
// const PROXY_MASTER = "/masterDataManagement";
const APIS = {
  planList: `${PROXY_ANALYSIS}/tenant/buyer/auto/level/query`,
  queryHistory: `${PROXY_ANALYSIS}/tenant/buyer/auto/level/queryHistory`,
  dimensionSelectList: (data = {}) => {
    //维度下拉列表
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/select`, data)
  },
  orgList: (data = {}) => {
    //维度下拉列表
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/auto/level/orgList`, data)
  },
  adopt: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/auto/level/approve`, data)
  },
  close: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/auto/level/close`, data)
  }
}
export default {
  NAME,
  APIS
}
