// 供应商-绩效管理-绩效分析
import { API } from '@mtech-common/http'
const NAME = 'application' // 接口前缀名
const PROXY_ANALYSIS = '/analysis'
// const PROXY_ANALYSISAS = "/supplier";
const PROXY_MASTER = '/masterDataManagement' // 主数据取公司接口前缀名
const APIS = {
  //绩效计划分页查询列表
  catepageQuery: `${PROXY_ANALYSIS}/tenant/buyer/assess/plan/pageQuery`,

  //新增绩效计划
  carategyAdd: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/plan/add`, data)
  },
  // 新增 公司列表
  TreeByAccount: (data = []) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/getFuzzyCompanyTree`, data)
  },
  // 删除绩效计划
  carategyDel: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/plan/del`, data)
  },
  // 编辑绩效计划
  carategyEdit: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/plan/update`, data)
  },
  // 根据组织查询模板
  templateListQuery: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/template/listQuery`, data)
  },
  // 根据组织查询模板
  carategySubmit: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/plan/submit`, data)
  },
  // 审批
  carategyExamine: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/plan/submitAll`, data)
  },
  // 审批
  carategyUpdateStatus: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/plan/updateStatus`, data)
  }
}
export default {
  NAME,
  APIS
}
