// 供应商-绩效管理-评分设置
import { API } from '@mtech-common/http'
const NAME = 'performanceScorePlan'
const PROXY_ANALYSIS = '/analysis'
const PROXY_SUPPLIER = '/supplier'
const PROXY_MASTER = '/masterDataManagement'
const APIS = {
  /*
    评分计划
  */
  //计划列表
  planList: `${PROXY_ANALYSIS}/tenant/buyer/score/plan/list`,
  // dimensionSelectList: (data = {}) => {
  //   //维度下拉列表
  //   return API.get(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/select`,
  //     data
  //   );
  // },
  addPlanDatavalid: (data = {}) => {
    //增加维度
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/data-valid`, data)
  },
  addPlanData: (data = {}) => {
    //增加维度
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/data`, data)
  },
  relationListByOrgId: (data = {}) => {
    //增加维度
    return API.post(`${PROXY_SUPPLIER}/tenant/buyer/process/manager/relationListByOrgId`, data)
  },
  //
  editPlanDataValid: (data = {}) => {
    //增加维度
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/data-valid`, data)
  },
  editPlanData: (data = {}) => {
    //修改维度
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/data`, data)
  },
  deletePlan: (data = {}) => {
    //删除维度
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/data/ids`, data)
  },
  updatePlan: (data = {}) => {
    //更改维度状态
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/status`, data)
  },
  planDetail: (data = {}) => {
    //维度信息-详情
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/detail`, data)
  },
  /*
   * 详情
   * */
  planDetailInfo: (data = {}) => {
    //模板信息-详情
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/info`, data)
  },
  //
  // /*
  //   参评范围
  // */
  // //指标列表
  planSelect: (data = {}) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/supplier/select`, data)
  },
  addIndex: (data = {}) => {
    //增加考评供应商
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/range/data`, data)
  },
  // addIndexValid: (data = {}) => {
  //   //增加考评供应商
  //   return API.get(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/plan/range/data-valid`,
  //     data
  //   );
  // },
  deleteIndex: (data = {}) => {
    //删除指标
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/range/ids`, data)
  },
  deleteRater: (data = {}) => {
    //删除评分人
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/rater/ids`, data)
  },
  addRaterx: (data = []) => {
    //增加评分人
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/rater/data`, data)
  },
  addRaterxValid: (data = []) => {
    //增加评分人
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/rater/data-valid`, data)
  },
  updateIndex: (data = {}) => {
    //更改指标状态
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/rater/status`, data)
  },
  templateSelect: (data = {}) => {
    //模板所属指标列表
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/config/index/template/select`, data)
  },
  raterSelect: (data = {}) => {
    //模板所属指标列表
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/specific/rater/select`, data)
  },
  generate: (data = {}) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/process/generate`, data)
  },
  addRaterData: (data = {}) => {
    //增加评分人
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/specific/rater/data`, data)
  },
  specificRater: (data = {}) => {
    //增加评分人
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/specific/rater/ids`, data)
  },
  //score/plan/specific/rater/ids
  /*
    租户级-组织员工信息接口
  */
  getOrganizationEmployees: (data = { orgId: '' }) =>
    API.post(`${PROXY_MASTER}/tenant/organization/getOrganizationEmployees`, data),
  /*
    租户级-供应商信息接口
  */
  /**
   * @description:条件查询
   * @param {*}
   * @return {supplierEnterpriseCode、supplierEnterpriseName、id}
   */
  pagedQuery: `${PROXY_MASTER}/tenant/supplier/paged-query`,
  getSupplierData: `${PROXY_SUPPLIER}/tenant/buyer/process/manager/query`,
  getSupplierList: (data = { orgId: '' }) =>
    API.get(`${PROXY_SUPPLIER}/tenant/buyer/process/manager/listByCompanyId`, data),

  //定义集合
  getDefineList: (data = {}) =>
    API.post(`${PROXY_SUPPLIER}/tenant/buyer/label/define/defineList`, data)
  // indexDetail: (data = {}) => {
  //   //指标-详情
  //   return API.get(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/index/detail`,
  //     data
  //   );
  // },
  //
  // /*
  //   绩效考评模板
  // */
  // //模板列表
  // templateList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/list`,
  // addTemplate: (data = {}) => {
  //   //增加模板
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data`,
  //     data
  //   );
  // },
  // editTemplate: (data = {}) => {
  //   //修改模板
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data`,
  //     data
  //   );
  // },
  // deleteTemplate: (data = {}) => {
  //   //删除模板
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data/ids`,
  //     data
  //   );
  // },
  // updateTemplate: (data = {}) => {
  //   //更改模板状态
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/status`,
  //     data
  //   );
  // },

  // saveTemplateDetal: (data = {}) => {
  //   //模板信息-保存
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/info`,
  //     data
  //   );
  // },
  //
  // /*
  //   公司默认评分人
  // */
  // //评分人列表
  // raterList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/list`,
  // addRater: (data = {}) => {
  //   //增加评分人
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data`,
  //     data
  //   );
  // },
  // editRater: (data = {}) => {
  //   //修改评分人
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data`,
  //     data
  //   );
  // },
  // deleteRater: (data = {}) => {
  //   //删除评分人
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data/ids`,
  //     data
  //   );
  // },
  // updateRater: (data = {}) => {
  //   //更改评分人
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/status`,
  //     data
  //   );
  // },
  //
  // /*
  //   主数据
  // */

  //
  // // 根据字典类型编码获取字典详情
  // dictionaryGetList: (data = {}) => {
  //   return API.post(`${PROXY_MASTER}/tenant/dict-item/dict-code`, data);
  // },
}

export default {
  NAME,
  APIS
}
