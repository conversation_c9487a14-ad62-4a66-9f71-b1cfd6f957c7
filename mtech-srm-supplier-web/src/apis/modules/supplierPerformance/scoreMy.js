// 供应商-绩效管理-我的评分
import { API } from '@mtech-common/http'
const NAME = 'performanceScoreMy'
const PROXY_ANALYSIS = '/analysis'
// const PROXY_MASTER = "/masterDataManagement";
const APIS = {
  /*
    待处理
  */
  //评分列表
  myList: `${PROXY_ANALYSIS}/tenant/buyer/score/my/query`,
  myQueryHistory: `${PROXY_ANALYSIS}/tenant/buyer/score/my/queryHistory`,
  // dimensionSelectList: (data = {}) => {
  //   //维度下拉列表
  //   return API.get(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/select`,
  //     data
  //   );
  // },
  // addPlanDatavalid: (data = {}) => {
  //   //增加维度
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/dimension/data-valid`,
  //     data
  //   );
  // },
  addMyData: (data) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/my/submit`, data)
  },
  myDoscore: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/my/doscore`, data)
  },
  myDoscoreAndSubmit: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/my/doscoreAndSubmit`, data)
  },

  // editPlanData: (data = {}) => {
  //   //修改维度
  //   return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/data`, data);
  // },
  // deletePlan: (data = {}) => {
  //   //删除维度
  //   return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/data/ids`, data);
  // },
  // updatePlan: (data = {}) => {
  //   //更改维度状态
  //   return API.put(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/status`, data);
  // },
  myDetail: (data = {}) => {
    //维度信息-详情
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/my/detail`, data)
  },
  /*
   * 详情
   * */
  planDetailInfo: (data = {}) => {
    //模板信息-详情
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/info`, data)
  },
  //
  // /*
  //   参评范围
  // */
  // //指标列表
  // indexList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/index/list`,
  indexSelectList: (data = {}) => {
    //指标下拉列表
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/config/index/select`, data)
  },
  addIndex: (data = {}) => {
    //增加考评供应商
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/plan/range/data`, data)
  },

  // 分页获取详情的计划信息
  indexResponseList: (data = {}) => {
    //增加考评供应商
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/my/detail/indexResponseList`, data)
  }
  // editIndex: (data = {}) => {
  //   //修改指标
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/index/data`,
  //     data
  //   );
  // },
  // deleteIndex: (data = {}) => {
  //   //删除指标
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/index/data/ids`,
  //     data
  //   );
  // },
  // updateIndex: (data = {}) => {
  //   //更改指标状态
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/index/status`,
  //     data
  //   );
  // },
  // indexDetail: (data = {}) => {
  //   //指标-详情
  //   return API.get(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/index/detail`,
  //     data
  //   );
  // },
  //
  // /*
  //   绩效考评模板
  // */
  // //模板列表
  // templateList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/list`,
  // addTemplate: (data = {}) => {
  //   //增加模板
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data`,
  //     data
  //   );
  // },
  // editTemplate: (data = {}) => {
  //   //修改模板
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data`,
  //     data
  //   );
  // },
  // deleteTemplate: (data = {}) => {
  //   //删除模板
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/data/ids`,
  //     data
  //   );
  // },
  // updateTemplate: (data = {}) => {
  //   //更改模板状态
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/status`,
  //     data
  //   );
  // },

  // saveTemplateDetal: (data = {}) => {
  //   //模板信息-保存
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/template/info`,
  //     data
  //   );
  // },
  //
  // /*
  //   公司默认评分人
  // */
  // //评分人列表
  // raterList: `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/list`,
  // addRater: (data = {}) => {
  //   //增加评分人
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data`,
  //     data
  //   );
  // },
  // editRater: (data = {}) => {
  //   //修改评分人
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data`,
  //     data
  //   );
  // },
  // deleteRater: (data = {}) => {
  //   //删除评分人
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/data/ids`,
  //     data
  //   );
  // },
  // updateRater: (data = {}) => {
  //   //更改评分人
  //   return API.put(
  //     `${PROXY_ANALYSIS}/tenant/buyer/score/config/rater/status`,
  //     data
  //   );
  // },
  //
  // /*
  //   主数据
  // */

  // // 根据字典类型编码获取字典详情
  // dictionaryGetList: (data = {}) => {
  //   return API.post(`${PROXY_MASTER}/tenant/dict-item/dict-code`, data);
  // },
}

export default {
  NAME,
  APIS
}
