// 阶段配置 接口
import { API } from '@mtech-common/http'
const NAME = 'planDetail'
const PROXY_SU = '/supplier'
const PROXY_AN = '/analysis'
const PROXY_AP = '/approveCenter'

// const PROXY_MAIN = '/masterDataManagement';
// 主数据的
// const PROXY_Master = "/masterDataManagement";
const APIS = {
  // 绩效分析-计划清单列表-计划详情-查询模板详情
  planTplDetail: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/plan/queryTemplate?id=${data.id}`)
  },
  // 绩效分析-计划清单列表-计划详情-模板详情/模板维护tab-新增维度（弹窗）- 维度下拉列表
  dimensionListQuery: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/dimension/listQuery`, data)
  },
  // 绩效分析-计划清单列表-计划详情-模板详情/模板维护tab-新增维度（弹窗）- 新增维度（新增模板详情）
  templateAddDetail: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/addDetail`, data)
  },
  // 绩效分析-计划清单列表-计划详情-计划适用范围tab-引用模板指定品类-品类全量树
  queryTemplateCategoryAllTree: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/plan/queryTemplateCategoryTree?id=${data.id}`)
  },
  // 绩效分析-计划清单列表-计划详情-计划适用范围tab-引用模板指定供应商-供应商全量树
  queryTemplateManagerAllTree: (data = {}) => {
    return API.post(`${PROXY_SU}/tenant/buyer/process/manager/query`, data)
  },
  // 品类全量树
  queryAllTree: (data = {}) => {
    return API.get(`${PROXY_AN}/tenant/buyer/category/queryAllTree`, data)
  },
  // 绩效分析-计划清单列表-计划详情（引用模板）-保存指定品类或指定供应商（新增模板适用范围）
  templateAddRange: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/plan/addRange`, data)
  },
  // 绩效分析-计划清单列表-计划详情（自定义模板）-新增模板详情和模板适用范围
  templateAddItemAndRange: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/addItemAndRange`, data)
  },

  // 保存并提交
  saveAndSubmitDetail: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/plan/saveAndSubmitDetail`, data)
  },
  saveRangeAndSubmit: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/plan/saveRangeAndSubmit`, data)
  },

  // 删除
  templateDel: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/del`, data)
  },
  // 编辑
  templateUpdate: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/update`, data)
  },
  // 启用停用
  templateUpdateStatus: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/updateStatus`, data)
  },
  // 删除模板详情
  templatedelDetail: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/delDetail?id=${data.id}`)
  },
  // 指标
  queryListByDimensionIdAndOrgId: (data) => {
    return API.post(
      `${PROXY_AN}/tenant/buyer/assess/index/queryListByDimensionIdAndOrgId?${data}`,
      { page: { current: 1, size: 999 } }
    )
  },

  queryAlreadyProcessUserInfo: (data = {}) => {
    return API.get(`${PROXY_AP}/approve/center/queryAlreadyProcessUserInfo`, data)
  }
}

export default {
  NAME,
  APIS
}
