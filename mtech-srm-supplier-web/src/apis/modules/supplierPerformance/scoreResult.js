// 供应商-绩效管理-评分结果
import { API } from '@mtech-common/http'
const NAME = 'performanceScoreResult'
const PROXY_ANALYSIS = '/analysis'
const APIS = {
  // 确认评分结果操作
  confirmScore: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/supplier`, data)
  },
  // 获取供应商下拉列表数据
  getSupplierSelectList: (params = {}) => {
    return API.post(
      `${PROXY_ANALYSIS}/tenant/buyer/assess/archive/result/queryAssessChangeTrend`,
      params
    )
  },
  // 获取供应商最新一期所有维度得分   雷达图
  getSupplierRodarData: (params = {}) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/supplier/current`, params)
  },
  //获取供应商维度变化   折线图
  getSupplierLineData: (params = {}) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/score/supplier/dimension`, params)
  },
  //参加评分供应商数量与评分问卷提交数
  queryJoinScoreNum: (params = {}) => {
    return API.post(
      `${PROXY_ANALYSIS}/tenant/buyer/assess/archive/result/queryJoinScoreNum`,
      params
    )
  },
  //
  // 分级
  getSupplierLevel: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/supplier/level`, data)
  },
  // 发布给供应商
  doPublishSupplierScore: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/supplier/publish`, data)
  },
  // 获取供应商列表数据
  getSupplierScoreResult: `${PROXY_ANALYSIS}/tenant/buyer/score/supplier/query`,
  // 获取供应商绩效排名(升序-降序)-绩效最低TOP10   柱状图
  queryAssessCompositeScoreLowTop: (params = {}) => {
    return API.post(
      `${PROXY_ANALYSIS}/tenant/buyer/assess/archive/result/queryAssessCompositeScoreLowTop10`,
      params
    )
  },
  // 获取供应商绩效排名(升序-降序)-绩效最高TOP10   柱状图
  queryAssessCompositeScoreUpTop: (params = {}) => {
    return API.post(
      `${PROXY_ANALYSIS}/tenant/buyer/assess/archive/result/queryAssessCompositeScoreUpTop10`,
      params
    )
  },
  // 获取供应商最新一期所有维度得分
  doShareSupplierScore: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/score/supplier/share`, data)
  },

  // 自动分级
  // 单个分级
  generateLevel: (data = {}) => {
    const toQueryParams = (params) => {
      const keys = Object.keys(params)
      let queryParams = []

      keys.forEach((k) => {
        queryParams.push(`${k}=${params[k]}`)
      })

      return queryParams.join('&')
    }
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/auto/level/generate`, toQueryParams(data))
  }
}

export default {
  NAME,
  APIS
}
