// 供应商-绩效分析-档案
import { API } from '@mtech-common/http'
const NAME = 'archives' // 接口前缀名
const PROXY_ANALYSIS = '/analysis'
const APIS = {
  //绩效分析-档案列表
  archiveQuery: `${PROXY_ANALYSIS}/tenant/buyer/assess/archive/list`,
  //绩效分析-档案详情
  archiveDetail: (data = []) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail`, data)
  },
  //档案详情-计算结果分析
  archiveDetailAnalyse: (data = []) => {
    return API.get(
      `${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail/analyse?archiveId=${data.archiveId}`
    )
  },
  //档案详情-结果应用情况
  // archiveDetailApply: (data = []) => {
  //   return API.post(
  //     `${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail/apply`,
  //     data
  //   );
  // },
  archiveDetailApply: `${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail/apply`,
  //档案详情-结果应用情况-详情
  archiveDetailApplyDetail: (data = []) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail/apply/detail`, data)
  },
  //档案详情-底层数据-点击指标
  archiveDetailData: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail/data`, data)
  },
  //档案详情-底层数据-模板详情信息
  archiveDetailDataTemplateInfo: (data = []) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail/dataTemplateInfo`, data)
  },
  //档案详情-头部档案信息
  archiveDetailHead: (data = []) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail/head`, data)
  },
  //档案详情-保存
  archiveDetailSave: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/detail/save`, data)
  },
  //打分人进度详情
  archiveProgressList: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/progress/list`, data)
  },
  //打分人进度提醒
  archiveProgressNotify: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/progress/notify`, data)
  },
  //重新计算
  archiveRecalculate: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/recalculate`, data)
  },
  //档案提交审批
  archiveSubmit: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/submit`, data)
  },
  //解冻
  archiveUnfreeze: (data = []) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/assess/archive/unfreeze`, data)
  }
}
export default {
  NAME,
  APIS
}
