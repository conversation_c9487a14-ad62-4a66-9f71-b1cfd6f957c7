import { API } from '@mtech-common/http'
const NAME = 'supplierInvitationAdd'
// 供应商
const PROXY_BASE = '/supplier'
// 主数据的
const PROXY_Master = '/masterDataManagement'
const APIS = {
  // 国家地区编码接口查询
  getCountrys: (data = {}) => {
    return API.post(`${PROXY_Master}/common/country/criteria-query`, data)
  },

  // 查询公司 模糊查询
  getCompanys: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/company/fuzzy-query`, data)
  },

  getCompanys2: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/company/criteria-query`, data)
  },

  // 查询客户 模糊查询
  getUsers: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/customer/criteria-query`, data)
  },

  //  查询地点 模糊
  getSite: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/site/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  // 查询客户 模糊 查询地点
  getCategory: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/category/getTree`, data)
  },

  // 供应商类型
  getSupplierType: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/setting/supplier_type/list`, data)
  },
  // 获取集团层面的主品类树
  getProdtreeInClient: (data = {}) => {
    return API.post(`${PROXY_Master}/common/category/find-platform-product-child-categorys`, data)
  },
  // 模糊查询获取公司树
  getFuzzyCompanyTree: (data = {}) => {
    return API.post(
      `${PROXY_Master}/tenant/organization/findOrganizationCompanyTreeByAccount`,
      data
    )
  },
  getFuzzyCompanyTree2: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
  },
  // 根据parentCode获取地址
  selectByParentCode: (data = {}) => {
    return API.post(`${PROXY_Master}/common/area/selectByParentCode`, data)
  }
}
export default {
  NAME,
  APIS
}
