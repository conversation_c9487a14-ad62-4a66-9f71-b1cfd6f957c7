// 供应商调查表管理 接口
import { API } from '@mtech-common/http'
const NAME = 'AccessProcess'
const PROXY_BASE = '/supplier'
const APIS = {
  // 准入模板列表查询
  queryAccess: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/access/query`, data)
  },
  // 查询阶段模板详情
  queryStageDetail: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/stage/queryDetail?stageTemplateId=${id}`)
  },
  // 准入模板适用范围查询
  queryRange: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/access/scope/query?accessTemplateId=${id}`)
  },
  // 准入模板总配置查询
  queryStageAll: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/access/config?accessTemplateId=${id}`)
  },
  // 新增流程
  addAccess: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/access/add`, data)
  },
  // 流程状态更改
  changeAccessStatus: (data = {}) => {
    return API.put(
      `${PROXY_BASE}/tenant/buyer/template/access/changeStatus
      `,
      data
    )
  },
  // 复制流程
  copyAccess: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/access/copy`, data)
  },
  // 修改流程
  updateAccess: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/template/access/update`, data)
  },
  // 删除流程
  deleteAccess: (data = {}) => {
    return API.delete(`${PROXY_BASE}/tenant/buyer/template/access/delete`, data)
  },

  // 新增阶段（公司？）
  addStage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/stage/add`, data)
  },
  // 更新阶段（公司？）
  updateStage: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/template/stage/update`, data)
  },
  // 删除阶段模板
  deleteStage: (data = {}) => {
    return API.delete(`${PROXY_BASE}/tenant/buyer/template/stage/delete`, data)
  },
  // 查询阶段列表
  queryStage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/define/stage/query`, data)
  },
  // 完成方式查询
  queryCompletion: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/stage/completion/type`, data)
  },
  // 阶段模板拖动顺序
  moveStage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/stage/move`, data)
  },

  // 根据字典类型编码获取字典详情
  queryDict: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/dict-item/dict-code`, data)
  },

  // 适用范围
  // 准入模板适用范围查询
  queryAccessScope: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/access/scope/query?accessTemplateId=${id}`)
  },
  // 准入模板适用范围维护
  updateAccessScope: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/access/scope/update`, data)
  },

  // 矩阵相关的接口
  // 查询场景任务矩阵
  querySceneDetail: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/scene/task/detail?accessTemplateId=${id}`)
  },
  // 编辑场景任务矩阵
  updateSceneTaskUpdate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/scene/task/update`, data)
  },
  // 新增场景
  addSceneStage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/scene/stage/add`, data)
  },
  // 删除场景
  deleteScene: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/scene/stage/deleteScene`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 删除矩阵
  deleteSceneStage: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/scene/stage/delete`, data)
  },
  // 查询场景阶段矩阵
  querySceneStageDetail: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/scene/stage/detail?accessTemplateId=${id}`)
  },
  // 查询场景阶段任务矩阵
  querySceneTaskDetail: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/scene/task/detail?accessTemplateId=${id}`)
  },
  // 编辑场景阶段矩阵
  querySceneStageUpdate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/scene/stage/update`, data)
  },

  // 分页查询认证场景定义
  querySceneDefine: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/scene/define/pageQuery`, data)
  },

  // 根据公司品类信息获取 符合范围的认证场景信息
  querySceDefByOrgCate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/scene/define/findByOrgCategory`, data)
  },

  // 新增+编辑场景阶段矩阵
  querySaveOrUpdate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/scene/stage/saveOrUpdate`, data)
  },

  // 新增+编辑场景阶段矩阵
  queryPackageListQuery: () => {
    return API.get(`${PROXY_BASE}/tenant/buyer/review/package/listQuery`)
  },

  bytemplateDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/scene/task/detail/bytemplate`, data)
  },

  // 新增+编辑场景阶段矩阵
  saveAll: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/scene/stage/saveAll`, data)
  }
}

export default {
  NAME,
  APIS
}
