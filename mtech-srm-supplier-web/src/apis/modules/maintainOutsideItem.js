// 物料维护外部物料组

import { API } from '@mtech-common/http'
const NAME = 'maintainOutsideItem'
const PROXY_BASE = '/supplier/tenant'

const APIS = {
  // 列表-失效
  queryIPage: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/material/maintain/queryIPage`, data)
  },
  // 品类接口
  criteriaQuery: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/category/fuzzy-query`, data)
  },
  //  编辑保存接口
  updateBasic: (data = {}) => {
    return API.put(`/masterDataManagement/tenant/item/update-basic`, data)
  },
  //  新增保存接口
  saveAndSync: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/category-item/saveAndSync`, data)
  },
  // 删除接口
  delete: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/material/maintain/delete`, data)
  },
  // 修改记录
  queryRecordInfo: (id = '') => {
    return API.get(`${PROXY_BASE}/buyer/material/maintain/queryRecordInfo/${id}`)
  },
  // 下发
  syncMaterialGroup: (id = {}) => {
    return API.get(`/masterDataManagement/tenant/item/syncMaterialGroup`, id)
  },
  // 导入
  import: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/category-item/import-data`, data)
  },
  // 导出
  export: (data = {}) => {
    return API.get(`/masterDataManagement/tenant/category-item/export-data`, data, {
      responseType: 'blob'
    })
  },
  // 外部物料组导出
  categoryItemExport: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/category-item/export`, data, {
      responseType: 'blob'
    })
  },
  // 同步sap
  batchSync: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/category-item/batch-sync`, data)
  },
  // 更新数据
  updateAndSync: (data = {}) => {
    return API.put(`/masterDataManagement/tenant/category-item/updateAndSync`, data)
  },
  // 物料接口
  fuzzyQuery: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/item/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  }
}

export default {
  APIS,
  NAME
}
