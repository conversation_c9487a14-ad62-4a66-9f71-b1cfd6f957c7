import { API } from '@mtech-common/http'
const NAME = 'supplierProfile'
const PROXY_BASE = '/supplier'
const APIS = {
  searchBase: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/archive/base`, data)
  },
  searchFinance: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/archive/finance`, data)
  },
  searchCertificate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/archive/certificate`, data)
  },
  searchDevice: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/archive/device`, data)
  },
  searchCycle: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/archive/cycle`, data)
  },
  searchCycleById: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/partner/archive/cycle/${data}`)
  },
  update: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/perspective/update`, data)
  },

  // 供方
  accessDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/access/detail`, data)
  },
  searchBaseSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/archive/base`, data)
  },
  searchFinanceSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/archive/finance`, data)
  },
  searchCertificateSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/archive/certificate`, data)
  },
  searchDeviceSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/archive/device`, data)
  },
  searchCycleSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/archive/cycle`, data)
  },
  searchCycleByIdSup: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/partner/archive/cycle/${data}`)
  },

  archiveExcelImport: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/archive/excel/import`, data)
  },

  // 由供应商内部编码查询
  queryCode: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/archive/query/code`, data)
  },

  // 由供应商编码查询所需参数（之前的路由参数）
  queryLinkInfo: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/manager/queryLinkInfo`, data)
  },

  // 查询供应商状态变化记录
  pageStatusRecordApi: (data = {}) => {
    return API.post(`/supplier/tenant/_partner_relation_record/categoryList`, data)
  },

  // 生成供应商注册链接失效KEY
  createKeyApi: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/register/address/create/record/createKey`, data)
  }
}
export default {
  NAME,
  APIS
}
