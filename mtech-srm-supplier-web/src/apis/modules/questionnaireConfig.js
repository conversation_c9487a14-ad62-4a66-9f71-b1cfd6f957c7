// 供应商调查表管理 接口
import { API } from '@mtech-common/http'
const NAME = 'QuestionnaireConfig'
const PROXY_BASE = '/supplier'
const APIS = {
  // 调查表列表
  queryFormTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/task/query`, data)
  },
  // 调查表状态更改
  changeStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/task/changeStatus`, data)
  },
  // 删除调查表
  delFormTemplate: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/task/deleteBatch`, data)
  },
  // 新增调查表
  addFormTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/task/add`, data)
  },
  // 编辑调查表
  editFormTemplate: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/template/task/update`, data)
  },
  // 调查表详情
  detailFormTemplate: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/task/query/${id}`)
  },

  // 动态表单
  // 动态表单模板表编辑
  updateFormTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/task/form/update`, data)
  },
  // 已配置的模板列表
  queryFormTask: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/task/query/${id}`)
  },
  // 获取动态表单列表
  queryFormDesignList: (data = {}) => {
    return API.post(`/lowcodeWeb/tenant/form-design/list`, data)
  },
  // 根据id获取动态表单模板
  getFormDesignTemplate: (data = '') => {
    return API.get(`/lowcodeWeb/tenant/form-design/get?${data}`)
  }
}

export default {
  NAME,
  APIS
}
