// 供应商调查表管理 接口
import { API } from '@mtech-common/http'
const NAME = 'GradeConfige'
const PROXY_BASE = '/supplier'
const PROXY_WORK_FLOW = '/flow'
const APIS = {
  // 分级列表查询
  queryGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/grade/define/list`, data)
  },

  // 新增分级定义
  addGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/approve/define/add`, data)
  },
  // 删除分级定义
  deleteGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/approve/define/deleteBatch`, data)
  },
  // 分级定义启用、禁用
  changeStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/approve/define/enable`, data)
  },
  // 编辑分级定义
  updateGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/approve/define/update`, data)
  },

  // 查询分类下的所有元素
  findEment: (data = {}) => {
    return API.get(`${PROXY_BASE}/admin/template/listByCategory`, data)
  },

  // 查询分类
  findadd: (data = {}) => {
    return API.get(`/flow/tenant/category/tree`, data)
  },

  // 查询分类
  getListByCategory: (data = {}) => {
    return API.get(`/flow/admin/template/listByCategory`, data)
  },
  getTemplatePage: (params = {}) => {
    //分页查询-流程模型
    return API.get(`${PROXY_WORK_FLOW}/tenant/template/page`, params)
  }
}

export default {
  NAME,
  APIS
}
