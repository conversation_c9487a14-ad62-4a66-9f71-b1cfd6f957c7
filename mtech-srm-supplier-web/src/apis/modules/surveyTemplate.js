// 供方-资质管理-资质审查项
import { API } from '@mtech-common/http'
const NAME = 'surveyTemplate'
const PROXY_BASE = '/supplier'
// 主数据的
const APIS = {
  // 供方-查询资质库-详细（包含提交的动态字段）
  getReview: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/supplier/qualification/manager/qualificationDatabaseDesc`,
      data
    )
  },
  //供方-资质管理-查询审查单动态字段列表
  Dynamicreview: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/supplier/qualification/manager/getQualificationItemList`,
      data
    )
  },
  //供方-资质管理-保存或提交资质审查单
  savesettings: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/qualification/manager/save`, data)
  }
}

export default {
  NAME,
  APIS
}
