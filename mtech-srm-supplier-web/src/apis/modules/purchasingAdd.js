import { API } from '@mtech-common/http'
const NAME = 'purChasing'
const PROXY_BASE = '/supplier'

const APIS = {
  // 添加供应商经理
  addPurchaser: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/manager/setup/add`, data)
  },
  // 更新供应商经理
  updatePurchaser: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/manager/setup/update`, data)
  },
  // 删除供应商经理
  deletePurchaser: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/manager/setup/delete`, data)
  },

  // 删除供应商经理
  queryPurchaserDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/manager/setup/queryDetail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  }
}

export default {
  NAME,
  APIS
}
