// 品类分工报表

/*
  客户准入管理
*/

import { API } from '@mtech-common/http'
const NAME = 'categoryDivisionReport'
const PROXY_BASE = '/supplier/tenant'

const APIS = {
  // 公司下拉框
  pagedQuery: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/entity/paged-query`, data)
  },
  // 品类下拉框
  fuzzyQuery: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/category/fuzzy-query`, data)
  },
  // 所有人员 -old
  currentTenantEmployees: (data = {}) => {
    return API.get(`/masterDataManagement/tenant/employee/currentTenantEmployees`, data)
  },
  // 所有人员 -new
  page: (data = {}) => {
    return API.post(`/iam/tenant/granted-subject/auth/users/page`, data)
  },
  // 导入
  import: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryDivision/import`, data)
  },
  // 下载标准模板
  exportTmp: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryDivision/exportTmp`, data, {
      responseType: 'blob'
    })
  },
  // 导出
  exportQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryDivision/exportQuery`, data, {
      responseType: 'blob'
    })
  },
  // 保存
  saveData: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryDivision/saveData`, data)
  },
  // 删除
  remove: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryDivision/remove`, data)
  }
}

export default {
  APIS,
  NAME
}
