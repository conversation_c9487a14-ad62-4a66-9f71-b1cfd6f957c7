import { API } from '@mtech-common/http'
const NAME = 'supplierCalculationRange'
const PROXY_BASE = '/analysis'
const APIS = {
  // 绩效计算范围 - 分页查询
  pageQuery: `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/pageQuery`,
  // 绩效计算范围 - 新增弹窗 - 新增校验绩效结果
  check: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/check`, data)
  },
  // 绩效计算范围 - 新增弹窗 -保存
  add: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/add`, data)
  },
  // 绩效计算范围 - 删除校验
  checkDelete: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/checkDelete?id=${data.id}`,
      data
    )
  },
  // 绩效计算范围 - 删除
  delete: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/delete?id=${data.id}`,
      data
    )
  },
  // 绩效计算范围 - 批量删除
  batchDeleteApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/batchDelete`,
      data
    )
  },

  // 绩效计算范围 - 导入模板下载
  downloadTemplate: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/downloadTemplate`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 导入模板
  importData: (data = []) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/importData`,
      data,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
        responseType: 'blob'
      }
    )
  },
  // 模板导出
  exportTemplate: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/exportTemplate`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 未在范围的供应商导出
  exportNotScope: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/exportNotScope`,
      data,
      {
        responseType: 'blob'
      }
    )
  }
}
export default {
  NAME,
  APIS
}
