import { API } from '@mtech-common/http'
const NAME = 'SupplyPunishment'
const PROXY_BASE = '/supplier'
// 主数据的
const APIS = {
  // 新增调查表
  addFormTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/share/add`, data)
  },
  //获取共享表详情
  getDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/share/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 供方 - 新增解除处罚申请
  addRelive: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/apply/relive/add`, data)
  },
  // 供方 - 申请提交工作流
  queryStartFlow: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/apply/info/startFlow`, data)
  },
  // 编辑解除处罚申请单
  queryReliveUpdate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/apply/relive/update`, data)
  },
  // 获取解除淘汰的详情
  queryReliveDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/apply/relive/detail/${data.id}`)
  },
  // 供方-黑名单列表(处罚单)
  queryPunishDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/partner/punish/detail/${data.id}`)
  },

  // 供方-删除解除处罚申请
  reliveDelete: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/apply/relive/delete`, data)
  }
}

export default {
  NAME,
  APIS
}
