import { API } from '@mtech-common/http'
const NAME = 'supplierChangeRequest'
const PROXY_BASE = '/supplier'
const PROXY_MASTER = '/masterDataManagement'
//供方-4M1E变更申请
const APIS = {
  // 根据字典类型编码获取字典详情
  queryDict: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/dict-item/dict-code`, data)
  },
  // 分页查询
  pageList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/change/4m/pageList`, data)
  },
  //明细
  detail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/change/4m/detail`, data)
  },
  //删除
  remove: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/change/4m/remove`, data)
  },
  //保存
  save: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/change/4m/save`, data)
  },
  //提交
  submit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/change/4m/submit`, data)
  },
  //供应商档案-联系人信息
  queryBaseContactInfo: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/archive/queryBaseContactInfo`, data)
  },
  //获取与供应商关系的公
  selectPartnerRelation: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/relation/selectPartnerRelation`, data)
  }
}
export default {
  NAME,
  APIS
}
