/********* 半导体风险管理 ***********/
import { API } from '@mtech-common/http'
import qs from 'qs'

const NAME = 'semiconductorRiskManagement'
const PROXY_BASE = '/supplier/tenant'

const APIS = {
  /********* 半导体品类清单维护 **********/
  // 查询列表
  querySemiCategoryList: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorCategory/pageQuery`, data),
  // 新增
  addSemiCategory: (data = {}) => API.post(`${PROXY_BASE}/semiconductorCategory/add`, data),
  // 删除
  deleteSemiCategory: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorCategory/delete`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    }),
  // 更新状态(启用/停用)
  updateSemiCategoryStatus: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorCategory/updateStatus`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    }),
  // 下载导入模板
  downloadSemiCategoryTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/semiconductorCategory/downloadImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导入
  importSemiCategory: (data = {}) => {
    return API.post(`${PROXY_BASE}/semiconductorCategory/importExcelData`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },

  /********* 半导风险管理列表 **********/
  // 查询列表
  querySemiRiskList: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorRiskManagement/pageQuery`, data),
  // 新增
  addSemiRisk: (data = {}) => API.post(`${PROXY_BASE}/semiconductorRiskManagement/addHeader`, data),
  // 更新列表
  updateSemiRiskList: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorRiskManagement/refreshHeader`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    }),
  // 明细-列表
  querySemiRiskDetailList: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorRiskManagement/queryItemListByHeader`, data),
  // 明细-保存
  saveSemiRiskDetailList: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorRiskManagement/saveItem`, data),
  // 提交-保存
  submitSemiRiskDetailList: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorRiskManagement/submitItem`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    }),
  // 供方：明细-列表Sup
  querySupSemiRiskList: (data = {}) =>
    API.post(`${PROXY_BASE}/semiconductorRiskManagement/supplierPageQuery`, data),
  // 导出
  exportSemiRiskDetailList: (data = {}) => {
    return API.post(`${PROXY_BASE}/semiconductorRiskManagement/exportItemListByHeader`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
