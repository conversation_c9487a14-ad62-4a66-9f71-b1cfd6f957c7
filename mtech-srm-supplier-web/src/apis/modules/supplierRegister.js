import { API } from '@mtech-common/http'
const NAME = 'supplierRegister'
const PROXY_BASE = '/supplier'
const PROXY_MASTER = '/masterDataManagement'
const APIS = {
  searchList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/list`, data)
  },
  addInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/addInvite`, data)
  },
  // 供应商邀请 新增(新)
  addInviteBatch: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/addInviteBatch`, data)
  },
  updateInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/update`, data)
  },

  deleteInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/deleteInvite`, data)
  },

  sendInviteExtList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/sendInviteExtList`, data)
  },

  sendInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/sendInvite`, data)
  },

  handleRegister: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/handleRegister`, data)
  },

  findOrgsByFuzzyOrgName: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/findOrgsByFuzzyOrgName`, data)
  },

  addSupplierInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/add`, data)
  },

  editSupplierInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/update`, data)
  },

  saveInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/addApplyRegister`, data)
  },
  editSaveInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/updateApplyRegister`, data)
  },

  delSupplierInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/del`, data)
  },

  applyRegister: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/applyRegister`, data)
  },

  withdrawRegister: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/withdrawRegister`, data)
  },

  confirmInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/confirmInvite`, data)
  },

  inviteDetail: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/invite/detail/${id}`)
  },

  fuzzyQueryTenantByName: (name) => {
    return API.get(`${PROXY_MASTER}/common/tenant/fuzzyQueryTenantByName/${name}`)
  },

  productList: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/category/product-list`, data)
  },

  supplierInviteDetail: (id) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/invite/detail/${id}`)
  },

  purTenantIdQuery: () => {
    return API.get(`${PROXY_BASE}/tenant/common/common/op/purTenantIdQuery`)
  },

  findSiteInfoByParentId: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/site/findSiteInfoByParentId?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  tenantProductList: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/category/tenant-product-list`, data)
  },

  categoryQuery: (data = {}) => {
    return API.post(`${PROXY_MASTER}/auth/category/paged-query`, data)
  },
  // 查询品类：可选品类值集根据供应商+公司排除已准入的品类
  searchCategory: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/searchCategory`, data)
  },

  withdrawInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/withdrawInvite`, data)
  },

  queryContactInfo: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/queryContactInfo`, data)
  },

  inviteImportData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/exclude/importData?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  queryDict: (data = {}) => {
    return API.post(`${PROXY_MASTER}/common/dict-item/item-tree`, data)
  },
  query: (data = {}) => {
    return API.post(`/supplier/tenant/supplier/process/access/query`, data)
  }
}
export default {
  NAME,
  APIS
}
