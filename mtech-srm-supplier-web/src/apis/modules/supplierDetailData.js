// 供应商画像 接口
import { API } from '@mtech-common/http'
const NAME = 'supplierLifecycleDetail'
const PROXY_BASE = '/supplier'

const APIS = {
  // 信息维护--编辑后保存
  LifecycleDetailInformation: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/perspective/update`, data)
  },

  // 信息维护--新增按钮
  LifecycleDetailInformationAdd: (data = {}) => {
    // console.log(data,'data');
    return API.post(`${PROXY_BASE}/tenant/buyer/process/perspective/addTrade`, data)
  },

  // 信息维护--编辑按钮
  LifecycleDetailInformationEdit: (data = {}) => {
    // console.log(data,'data');
    return API.post(`${PROXY_BASE}/tenant/buyer/process/perspective/updateTrade`, data)
  },

  // 信息维护--删除按钮
  LifecycleDetailInformationDelete: (data = {}) => {
    console.log(data, 'data')
    return API.post(`${PROXY_BASE}/tenant/buyer/process/perspective/deleteTrade`, data)
  }
}

export default {
  NAME,
  APIS
}
