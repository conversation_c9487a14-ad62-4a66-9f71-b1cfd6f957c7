import { API } from '@mtech-common/http'
const NAME = 'PPAPConfig'
const APIS = {
  // 获取公司列表
  getCompanyList: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/templateDetails/bu/query`, data)
  },
  // ppap模板详情保存
  saveSonMoudle: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/templateDetails/add`, data)
  },
  // ppap模板详情查询
  SonMoudleQuery: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/templateDetails/infoQuery`, data)
  },
  // ppap模板详情删除
  deleteDetail: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/templateDetails/delete`, data)
  },
  // ppap模板查询
  queryInfo: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/template/infoQuery`, data)
  },
  // ppap模板删除
  moudleDelete: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/template/delete`, data)
  },
  // ppap模板生效
  moudleOperation: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/template/operation`, data)
  },
  // ppap模板复制
  moudleCopy: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/template/copy`, data)
  },
  // ppap管理 模板获取公司
  moudleFindCompany: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/templateDetails/bu/find`, data)
  },
  // ppap管理 公司编码获取供应商
  companyFindSupplier: (data = {}) => {
    return API.get(`/masterDataManagement/tenant/supplier/queryByOrgCode`, data)
  },
  // ppap管理 公司和供应商获取品类
  companyAndSupplierFindCategory: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/supply/source/findBySupplierCode`, data)
  },
  // ppap单据管理详情 保存
  billSave: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/details/add`, data)
  },
  // ppap单据管理详情 详情查询
  billDetailQuery: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/details/infoQuery`, data)
  },
  // ppap单据管理详情 提交
  billSubmit: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/details/submit`, data)
  },
  // ppap单据管理详情 模板查询行信息
  templateFindDetail: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/templateDetails/dateilQuery`, data)
  },
  // ppap单据管理 查询
  billInfoQuery: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/infoQuery`, data)
  },
  // ppap单据管理 删除
  billDelete: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/delete`, data)
  },
  // ppap单据管理 提交
  billInfoSubmit: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/submit`, data)
  },
  // ppap单据管理 OA审批
  getBillOA: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/info/getOALink`, data)
  },
  // ppap单据反馈 查询 供方
  feedbackQuery: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/supplier-infoQuery`, data)
  },
  // ppap单据反馈 提交 供方
  feedbackSubmit: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/supplier-submit`, data)
  },
  // ppap单据反馈详情 查询 供方
  feedbackDetailQuery: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/details/supplier-infoQuery`, data)
  },
  // ppap单据反馈详情 保存 供方
  billSupplierSave: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/details/supplier-add`, data)
  },
  // ppap单据反馈详情 提交 供方
  billSupplierSubimit: (data = {}) => {
    return API.post(`/supplier/tenant/ppap/bill/details/supplier-submit`, data)
  }
}
export default {
  NAME,
  APIS
}
