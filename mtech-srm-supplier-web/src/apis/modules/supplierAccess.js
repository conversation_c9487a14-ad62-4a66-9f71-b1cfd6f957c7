import { API } from '@mtech-common/http'
const NAME = 'supplierAcc'
// 供应商
const PROXY_BASE = '/supplier'

const PROXY_BASE1 = '/masterDataManagement/tenant'
const PROXY_USER = '/masterDataManagement/user'

const APIS = {
  // 获取已定义供应商阶段   scopeId 0
  getStageList: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/setting/stage_define/list`, data)
  },

  // 供应商准入-公司关系查询
  getRelationShip: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/access/partner/relation/query`, data)
  },

  // 供应商准入阶段列表查询
  getProcess: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/access/query`, data)
  },

  // 供应商准入-阶段详情查询
  getProcessDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/access/${data.id}`)
  },

  // 供应商准入-任务保存
  saveTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/task/save`, data)
  },

  // 供应商准入-阶段任务查询
  getStages: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/form/task/stage/query`, data)
  },

  // 供应商准入-任务提交
  stageSubmit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/task/submit`, data)
  },

  // 供应商准入-任务详情查询
  getTaskDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/form/task/${data.id}`)
  },

  /****
   *
   * 1030 任务接口
   *
   */
  // 供应商准入阶段模板
  queryStageTempalte: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/manager/queryStageTempalte`, data)
  },

  // 新增晋级申请单
  stageAdd: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/stage/add`, data)
  },

  // 获取用户信息
  queryUserInfo: (data = {}) => {
    return API.get(`/iam/tenant/account/user-detail`, data)
  },

  getCurrentTenantUsers: (data = {}) => {
    return API.get(`${PROXY_BASE1}/user/findCurrentTenantUsers`, data)
  },

  getCompanysByUserId: (data = {}) => {
    return API.get(`${PROXY_USER}/user/findCompanysByUserId`, data)
  },

  getDepartmentsByUserId: (data = {}) => {
    return API.get(`${PROXY_USER}/user/findDepartmentsByUserId`, data)
  },

  // 供应商准入管理-导出
  exportSupplierAccessList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/manager/categoryCertificationExcel`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
