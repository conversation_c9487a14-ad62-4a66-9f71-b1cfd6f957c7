import { API } from '@mtech-common/http'
const NAME = 'performanceManage'
const PROXY_BASE = '/analysis'
import qs from 'qs'
const APIS = {
  // 获取建议层级枚举项
  getSuggestLevelOptions: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/buyer/category/grade/config/queryBasicItemsByCategCode`,
      data
    )
  },
  // 新增模板
  addTemplateData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/add`, data)
  },
  // 删除模板
  delTemplateData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/del`, data)
  },
  // 创建新模板
  createNewVersionTemplateData: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/assess/template/createNewVersion`, data)
  },
  // 修改模板
  updateTemplateDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/update`, data)
  },
  // 模板提交审批
  templateSubmitApply: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/submitApply`, data)
  },
  // 模板详情
  getTemplateDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/assess/template/detail`, data)
  },
  // 模板失效
  templateInvalid: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/invalid`, data)
  },
  // 修改模板状态
  updateTemplateStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/updateStatus`, data)
  },
  // 导出模板
  downloadTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/export`, data, {
      responseType: 'blob'
    })
  },
  // 获取虚拟组织树状结构列表
  queryOrgTree: (data = {}) => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/queryOrgTree`, data)
  },
  // 维度下拉列表
  getDimensionlistQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/dimension/listQuery`, data)
  },
  // 维度下拉列表 _ 新
  getDimensionlistQuery_new: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/index/listQueryForIndex`, data)
  },
  // 获取指标明细列表
  getDimensionIndexDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/index/detail`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 根据维度查询有效状态指标-指标类列表，不包含指标类下属指标
  queryEffectiveIndexList: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/index/queryEffectiveIndexList`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 新增品类模板关系
  categoryStrategyAdd: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/categoryTemplateRelation/add`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 修改品类模板关系状态
  categorySupdateStatus: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/categoryTemplateRelation/changeStatus`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 删除品类模板关系
  categorydel: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/categoryTemplateRelation/del`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 导入品类模板
  fileUploadCategory: (data = []) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/categoryTemplateRelation/importExcel`,
      data,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
        responseType: 'blob'
      }
    )
  },
  // 获取品类导入模板
  fileDownloadCategory: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/categoryTemplateRelation/getImportTemplate`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 导出品类模板关系
  fileExportCategory: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryTemplateRelation/exportExcel`, data, {
      responseType: 'blob'
    })
  },
  // 模板下拉查询列表
  templateListQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/listQuery`, data)
  },
  // 绩效组织架构设置 - 获取列表
  getStructureList: (data = {}) => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/queryTrueOrg`, data)
  },
  // 绩效组织架构设置 - 新增
  addStructureList: (data = {}) => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/addOrg`, data)
  },
  // 绩效组织架构设置 - 更新
  updateStructureList: (data = {}) => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/update`, data)
  },
  // 绩效组织架构设置 - 获取架构tree数据
  getStructureTreeList: () => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/queryOrgTree`)
  },
  // 绩效组织架构设置 - 获取公司层级下的架构tree数据
  getStructureTreeOrgForSpecial: () => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/query/treeOrgForSpecial`)
  },
  // 绩效组织架构设置 - 获取有权限的公司层级下的组织列表
  getListOrgForSpecial: () => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/template/getListOrg`)
  },
  // 绩效组织架构设置 - 获取可选择的绩效月份
  queryAchievementsMonth: () => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/achievementsCalculationRange/queryAchievementsMonth`
    )
  },
  // 绩效组织架构设置 - 获取有权限的公司层级下的组织列表1
  getListOrgForIndexSpecial: () => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/index/getListOrg`)
  },
  // 绩效组织架构设置 - 获取公司层级下的架构tree平铺list数据
  getStructureListOrgForSpecial: () => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/categoryTemplateRelation/getOrgTree`)
  },
  // 绩效组织架构设置 - 查询父组织列表
  getParentOrgList: (data = {}) => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/selectParentOrg`, data)
  },
  // 绩效组织架构设置 - 查询组织列表
  getOrgList: (data = {}) => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/queryTrueOrg`, data)
  },
  // 绩效组织架构设置 - 导出列表数据
  exportOrgList: (data = {}) => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/export`, data, {
      responseType: 'blob'
    })
  },
  // 绩效组织架构设置 - 失效
  updateStructureStatus: (data = {}) => {
    return API.get(`/masterDataManagement/analysisVirtualOrg/invalid`, data)
  },
  // 指标清单 - 新增
  addIndexList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/add`, data)
  },
  // 指标清单 - 编辑
  updateIndexList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/update`, data)
  },
  // 指标清单 - 新增
  delIndexList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/del`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 品类模板关系 - 分页查询（不传页码即返回全部数据），查模板，查品类都用此接口
  categoryTemplateRelation: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryTemplateRelation/pageQuery`, data)
  },
  // 指标清单 - 查询指标说明
  getIndexDescription: (data = {}) => {
    return API.post(
      `/analysis/tenant/buyer/assess/index/queryEffectiveIndexListWithoutCategoryByDimension`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 指标清单 - 获取详情页数据
  getIndexDetail: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/detail`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 指标清单 - 更新status状态
  updateIndexStatus: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/changeStatus`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 指标清单 - 导出数据
  exportIndexList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/export`, data, { responseType: 'blob' })
  },
  // 指标清单 - 导入数据
  ImportIndexList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 指标清单 - 下载模板
  ImportTemplateList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/downIndexTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 指标清单 - 指标维度
  getIndexDimension: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/dimension/listQuery`, data)
  },

  //评分人分页查询
  catassessQuery: `/analysis/tenant/buyer/assess/rater/pageQuery`,
  //评分人设置 新增 根据计划获取的品类接口
  planTemplateRangeCat: (data = []) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryTemplateRelation/pageQuery`, data)
  },
  //评分人新增
  raterAdd: (data = []) => {
    return API.post(`/analysis/tenant/buyer/assess/rater/add`, data)
  },
  //评分人编辑
  raterUpdate: (data = []) => {
    return API.post(`/analysis/tenant/buyer/assess/rater/update`, data)
  },
  //评分人删除
  cateexadelete: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/rater/del`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 导出考评人设置
  fileExportRater: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/rater/exportExcel`, data, {
      responseType: 'blob'
    })
  },
  //评分人新增 公司下的指标
  queryIndexByTemplateId: (data = {}) => {
    return API.get(`/analysis/tenant/buyer/assess/template/detail`, data)
  },
  // 导入评分人
  fileUploadRater: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/rater/importExcel`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 获取评分人导入模板
  fileDownloadRater: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/rater/getImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 绩效等级规则配置 - 新增
  addRuleList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/level/rule/add`, data)
  },
  // 绩效等级规则配置 - 删除
  delRuleList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/level/rule/delete`, data)
  },
  // 绩效等级规则配置 - 编辑
  updateRuleList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/level/rule/update`, data)
  },
  // 绩效等级规则配置 - 生效/失效
  updateRuleStatus: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/level/rule/effectiveOrInvalid`, data)
  },
  // 绩效等级规则配置 - 获取详情
  getRuleDetail: (data = {}) => {
    return API.get(`/analysis/tenant/buyer/assess/level/rule/detail`, data)
  },
  // 原材料接收数据列表查询
  getFieldConfig: (data = {}) => {
    return API.post(`/platform/tenant/task/fieldConfig`, data)
  },
  // 任务中心获取 弹框配置
  getRawMaterialInfo: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/rawMaterialInfo/pageQuery`, data)
  },
  // 任务中心  保存列表
  saveTask: (data = {}) => {
    return API.post(`/platform/tenant/task/save`, data)
  },
  // 任务中心  获取供应商列表数据
  getSupplierList: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/supplier/achievements/fuzzy-query`, data)
  },
  // 绩效计算数据  全选发布
  getSelectAllSubmit: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/publishTaskAll`, data)
  },
  // 绩效计算数据  导出
  exportScoringList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/export`, data, {
      responseType: 'blob'
    })
  },
  // 绩效计算数据 导入
  importScoringList: (data = []) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 绩效计算数据 任务发布
  publishScoring: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/publishTask`, data)
  },
  // 绩效计算打分明细 提交
  submitScoring: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/submitScoreDetails`, data)
  },
  // 绩效计算打分明细 全部提交
  submitScoringAll: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/submitScoreDetailsAll`, data)
  },
  // 绩效计算打分明细 撤回
  recallScoring: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/revokeScoreDetails`, data)
  },
  // 绩效计算打分明细 撤回
  recallScoringAll: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/revokeScoreDetailsAll`, data)
  },
  // 绩效计算数据 编辑打分
  saveScoring: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/save`, data)
  },
  // 绩效计算数据 编辑打分
  getScoreRange: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/computeScoreDetail/getScoreRange`, data)
  },

  // 综合绩效结果分页列表查询 -采方
  getComprehensiveResult: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/comprehensiveResult/pageQuery`, data)
  },
  // 综合绩效结果提交审批 -采方
  approvalComprehensiveResult: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/comprehensiveResult/approval`, data)
  },
  // 综合绩效结果发布 -采方
  publishComprehensiveResult: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/comprehensiveResult/publish`, data)
  },
  // 综合绩效结果根据查询条件导出查询结果 -采方
  exportComprehensiveResult: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/comprehensiveResult/export`, data, {
      responseType: 'blob'
    })
  },
  // 综合绩效结果根据查询条件导出明细 -采方
  exportDetailComprehensiveResult: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/comprehensiveResult/exportDetail`, data, {
      responseType: 'blob'
    })
  },
  // 综合绩效结果详情查询 -采方
  getComprehensiveDetail: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/comprehensiveResult/detail`, data, {
      headers: { 'Content-Type': 'application/json' }
    })
  },
  // 综合绩效结果获取有权限的组织列表 -采方
  getPermissionOrgList: (data = {}) => {
    return API.get(
      `/analysis/tenant/buyer/assess/comprehensiveResult/getOrgList`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 综合绩效结果获取有权限的模板列表 -采方
  getPermissionTemplateList: (data = {}) => {
    return API.get(`/analysis/tenant/buyer/assess/comprehensiveResult/getTemplateList`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 综合绩效结果分页列表查询 -供方
  getIndexList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/index/listIndex`, data)
  },
  // 综合绩效结果分页列表查询 -供方
  getSupComprehensiveResult: (data = {}) => {
    return API.post(`/analysis/tenant/supplier/assess/comprehensiveResult/pageQuery`, data)
  },
  // 综合绩效结果根据查询条件导出查询结果 -供方
  exportSupComprehensiveResult: (data = {}) => {
    return API.post(`/analysis/tenant/supplier/assess/comprehensiveResult/export`, data, {
      responseType: 'blob'
    })
  },
  // 综合绩效结果根据查询条件导出明细 -供方
  exportSupDetailComprehensiveResult: (data = {}) => {
    return API.post(`/analysis/tenant/supplier/assess/comprehensiveResult/exportDetail`, data, {
      responseType: 'blob'
    })
  },
  // 综合绩效结果详情查询 -供方
  getSupComprehensiveDetail: (data = {}) => {
    return API.post(`/analysis/tenant/supplier/assess/comprehensiveResult/detail`, data, {
      headers: { 'Content-Type': 'application/json' }
    })
  },
  // 综合绩效结果获取有权限的组织列表 -供方
  getSupPermissionOrgList: (data = {}) => {
    return API.get(
      `/analysis/tenant/supplier/assess/comprehensiveResult/getOrgList`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 综合绩效结果获取有权限的模板列表 -供方
  getSupPermissionTemplateList: (data = {}) => {
    return API.get(`/analysis/tenant/supplier/assess/comprehensiveResult/getTemplateList`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 综合绩效分析图表查询 -采方
  getPorAnalysisChart: (data = {}) => {
    return API.post(
      `/analysis/tenant/buyer/assess/comprehensiveResult/getChartData`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 综合绩效分析图表查询 -供方
  getSubAnalysisChart: (data = {}) => {
    return API.post(
      `/analysis/tenant/buyer/assess/comprehensiveResult/getSupplierChartData`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 获取用户有权限的品类列表
  getPermissionCategoryList: (data = {}) => {
    return API.post(
      `/analysis/tenant/buyer/assess/comprehensiveResult/getPermissionCategoryList`,
      data,
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 采购计划负责人设置 - 新增
  addBuyerList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/raterAddition/add`, data)
  },
  // 采购计划负责人设置 - 删除
  delBuyerList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/raterAddition/del`, data)
  },
  // 采购计划负责人设置 - 导出
  exBuyerList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/raterAddition/exportExcel`, data, {
      responseType: 'blob'
    })
  },
  // 采购计划负责人设置 - 获取导入模板
  getImBuyerList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/raterAddition/getImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 采购计划负责人设置 - 通过模板ID获取有效绩效模板下拉列表
  getSupTemplateList: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/raterAddition/getIndexListByTemplateId`,
      data,
      { headers: { 'Content-Type': 'application/json' } }
    )
  },
  // 采购计划负责人设置 - 导入
  imBuyerList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/raterAddition/importExcel`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 采购计划负责人设置 - 获取采购负责人
  getBuyerList: (data = {}) => {
    return API.get(`/masterDataManagement/tenant/employee/currentTenantEmployees`, data)
  },
  // 采购计划负责人设置 - 分页查询
  buyerListQuery: `${PROXY_BASE}/tenant/buyer/assess/raterAddition/pageQuery`,

  // 综合绩效供应商列表
  getAssessSupplierList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/comprehensiveResult/getSupplierList`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 综合绩效供应商列表
  queryPermissionCategories: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/permission/queryCategories`, data)
  },

  // 现场评审得分报表列表
  siteReviewScoreList: (data = {}) => {
    return API.post(`/statistics/tenant/buyer/siteReviewScore/pageQuery`, data)
  },
  // 现场评审得分导出
  getReviewScoreReportExportApi: (data = {}) => {
    return API.post(`/statistics/tenant/buyer/siteReviewScore/export`, data, {
      responseType: 'blob'
    })
  },
  // 现场评审得分表-公司列表
  corporationListList: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/template/getListOrgForCompany`, data)
  },
  // 分层分级
  // 品类分层分级列表查询
  getCategoryGrade: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryGrade/pageQuery`, data)
  },

  // 品类分层分级-导出
  categoryGradeExport: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryGrade/export`, data, {
      responseType: 'blob'
    })
  },

  // 分层分级结果 - 品类分层分级 - 导出
  ResultsCategoryGradeExport: (data = {}) => {
    return API.post('/analysis/tenant/buyer/assess/layered/level/export', data, {
      responseType: 'blob'
    })
  },
  // 分层分级结果 - 品类分层分级 - 导出明细
  ResultsCategoryGradeExportDetail: (data = {}) => {
    return API.post('/analysis/tenant/buyer/assess/layered/level/exportScoreDetail', data, {
      responseType: 'blob'
    })
  },

  // 分层分级结果 - 品类分层分级 - 导入 excel
  ResultsCategoryGradeImport: (data = {}) => {
    return API.post('/analysis/tenant/buyer/assess/layered/level/import', data, {
      responseType: 'blob'
    })
  },

  // 品类分层分级-导入 excel
  categoryGradeImport: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryGrade/import`, data, {
      responseType: 'blob'
    })
  },

  // 品类分层分级-批量撤回 ids
  revokeScoreDetails: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryGrade/revokeScoreDetails`, data)
  },
  // 品类分层分级-全部撤回 ids
  revokeScoreDetailsAll: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryGrade/revokeScoreDetailsAll`, data)
  },

  // 品类分层分级-编辑保存
  categoryGradeSave: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/layered/level/save`, data)
  },
  // 分层分级待打分明细 - 品类分层分级 - 编辑保存
  detailCategoryGradeSave: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryGrade/save`, data)
  },

  // 品类分层分级-提交 ids
  categoryGradeSubmit: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryGrade/submit`, data)
  },
  // 品类分层分级-全部提交 ids
  categoryGradeSubmitAll: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/categoryGrade/submitAll`, data)
  },
  // 待打分明细 - 战略供应商筛选 - 导入
  strategySupplierImport: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/strategy/supplier/import`, data, {
      responseType: 'blob'
    })
  },
  // 待打分明细 - 战略供应商筛选-导出
  strategySupplierExport: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/strategy/supplier/export`, data, {
      responseType: 'blob'
    })
  },
  // 待打分明细 - 战略供应商筛选-批量撤回 ids
  strategySupplierRecall: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/strategy/supplier/revokeScoreDetails`, data)
  },
  // 待打分明细 - 战略供应商筛选-批量撤回 ids
  strategySupplierRecallAll: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/strategy/supplier/revokeScoreDetailsAll`, data)
  },
  // 待打分明细 - 战略供应商筛选-编辑保存
  strategySupplierSave: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/strategy/supplier/save`, data)
  },

  // 待打分明细 - 战略供应商筛选-提交 ids
  strategySupplierSubmit: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/strategy/supplier/submit`, data)
  },
  // 待打分明细 - 战略供应商筛选- 全部提交 ids
  strategySupplierSubmitAll: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/strategy/supplier/submitAll`, data)
  },
  // 黄牌 查询
  getYellowCardList: `${PROXY_BASE}/tenant/buyer/assess/yellowCard/pageQuery`,
  // 黄牌 供方查询
  getSupYellowCardList: `${PROXY_BASE}/tenant/buyer/assess/yellowCard/supplierPageQuery`,

  // 黄牌 - 供方导出
  exSupYellowCardList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/supplierExportExcel`, data, {
      responseType: 'blob'
    })
  },
  // getYellowCardList: (data = {}) => {
  //   return API.post(`/analysis/tenant/buyer/assess/yellowCard/pageQuery`, data)
  // }
  // 黄牌 - 新增
  addYellowCardList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/add`, data)
  },
  // 黄牌 - 更新
  updateYellowCardList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/update`, data)
  },
  // 黄牌 - 提交审批
  submitYellowCardList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/submit`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 黄牌 - 详情查询
  getYellowCardDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/detail`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 黄牌 - 发布
  pubYellowCardList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/publish`, data)
  },
  // 黄牌 - 删除
  delYellowCardList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/del`, data)
  },
  // 黄牌 - 导出
  exYellowCardList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/exportExcel`, data, {
      responseType: 'blob'
    })
  },
  // 黄牌 - 获取新增导入模板
  getYellowCardTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/getImportTemplateNew`, data, {
      responseType: 'blob'
    })
  },
  // 黄牌 - 新增导入
  imYellowCardList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/importExcelNew`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 黄牌 - 获取修改导入模板
  getYellowCardChangeTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/getImportTemplateChange`, data, {
      responseType: 'blob'
    })
  },
  // 黄牌 - 修改导入
  imYellowCardChangeList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/importExcelChange`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 黄牌 - 获取原始单据号列表
  getYellowCardNumberList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/yellowCard/getRequestNumberList`, data)
  },
  // 绩效结果汇总 - 查询
  assessResultQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/result/total/pageQuery`, data)
  },
  // 绩效结果汇总 - 导出
  assessResultExport: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/result/total/export`, data, {
      responseType: 'blob'
    })
  },
  // 绩效结果汇总 - 发布
  publishApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/result/total/publish`, data)
  },

  /*********** 分层分级规则配置 ****************/
  // 分层分级规则配置- 类别列表
  getCategoryList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/grade/config/queryCategCode`, data)
  },
  // 分层分级规则配置-查询明细
  getLayerAndGradeConfigDataDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/category/grade/config/queryItemsByRuleId`, data)
  },
  // 分层分级规则配置-删除
  deleteLayerAndGradeConfigData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/grade/config/delete`, data)
  },
  // 分层分级规则配置-生效
  effectiveLayerAndGradeConfigData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/grade/config/effective`, data)
  },
  // 分层分级规则配置-失效
  expireLayerAndGradeConfigData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/grade/config/expire`, data)
  },
  // 分层分级规则配置-保存/创建新版本
  saveLayerAndGradeConfigData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/grade/config/add`, data)
  },
  // 分层分级规则配置-导出
  exportLayerAndGradeConfigData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/grade/config/export`, data, {
      responseType: 'blob'
    })
  },
  // 分层分级规则配置- 分层分级类型基础数据
  queryBasicItemsByCategCode: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/buyer/category/grade/config/queryBasicItemsByCategCode`,
      data
    )
  },

  /*********** 自动计算指标配置 ****************/
  // 自动计算指标配置-指标名称列表
  getIndicatorNameList: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/buyer/assess/template/queryIndexByAchievementAssessType`,
      data
    )
  },
  // 自动计算指标配置-保存
  saveComputedIndicatorConfig: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/category/grade/auto/compute/rule/config/save`, data)
  },
  // 自动计算指标配置-删除
  deleteComputedIndicatorConfig: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/category/grade/auto/compute/rule/config/delete`,
      data
    )
  },

  /*********** 分层分级结果 ****************/
  // 获取'层级调整'下拉，获取值集
  getAdjustLevelOption: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/buyer/category/grade/config/queryBasicItemsByCategCode`,
      data
    )
  },
  /*********** 待打分明细 ****************/
  // 非战略供应商-导出
  noStrategyExport: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/export`, data, {
      responseType: 'blob'
    })
  },

  // 非战略供应商-导入 excel
  noStrategyImport: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/import`, data, {
      responseType: 'blob'
    })
  },
  // 非战略供应商 -提交 ids
  noStrategySubmit: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/submit`, data)
  },
  // 非战略供应商 -全部提交 ids
  noStrategySubmitAll: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/submitAll`, data)
  },
  // 非战略供应商 -撤回 ids
  noStrategyRecall: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/revokeScoreDetails`, data)
  },
  // 非战略供应商 -全部撤回 ids
  noStrategyRecallAll: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/revokeScoreDetailsAll`, data)
  },
  // 非战略供应商 - 编辑保存
  noStrategySave: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/save`, data)
  },
  getResultQueryForm: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/layered/level/pageQuery`, data)
  },

  /*********** 分层分级计算范围 ****************/
  // 导出
  downloadCalculationRange: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/supplier/layered/level/calcScope/export`,
      data,
      {
        responseType: 'blob'
      }
    )
  },

  /*********** 分层分级结果 ****************/
  // 分层分级结果 - 提交审批
  submitApprovalData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/layered/level/submitApprove`, data)
  },
  // 分层分级结果 - 调整导入
  resultImportFn: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/strategy/supplier/result/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 分层分级结果 - 导出
  resultExportCate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/strategy/supplier/result/export`, data, {
      responseType: 'blob'
    })
  },
  // 分层分级结果 - 行编辑保存
  resultRowSave: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/strategy/supplier/result/save`, data)
  },
  // 分层分级结果 - 页面数据 - 采方
  getResultStrategicQueryForm: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/strategy/supplier/result/pageQuery`, data)
  },
  // 分层分级结果 - 发布
  submitResultStrategicQueryForm: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/strategy/supplier/result/publish`, data)
  },
  // 分层分级结果 - 页面数据 - 供方
  getResultStrategicQueryFormSUP: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/assess/strategy/supplier/result/pageQuery`, data)
  },
  // 分层分级结果 - 导出 - 供方
  resultExportCateSUP: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/assess/strategy/supplier/result/export`, data, {
      responseType: 'blob'
    })
  },
  // 分层分级结果 - 得分明细查询 - 采方
  resultQueryDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/assess/layered/level/queryScoreDetail`, data)
  },
  // 分层分级结果 - 得分明细查询 - 供方 - 战略供应商
  resultQueryDetailSUP: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/supplier/assess/strategy/supplier/result/queryScoreDetail`,
      data
    )
  },
  // 分层分级结果 - 得分明细查询 - 供方 - 非战略供应商
  NonStrategicQueryDetailSUP: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/supplier/assess/noStrategy/supplier/result/queryScoreDetail`,
      data
    )
  },
  // 分层分级结果 - 战略供用商 - 导出明细 - 供方
  ResultsSupplierExportDetail: (data = {}) => {
    return API.post(
      '/analysis/tenant/buyer/assess/strategy/supplier/result/exportScoreDetail',
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 分层分级结果 - 战略供用商 - 导出明细 - 供方
  ResultsSupplierExportDetailSPU: (data = {}) => {
    return API.post(
      '/analysis/tenant/supplier/assess/strategy/supplier/result/exportScoreDetail',
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 分层分级接口 - 非战略供应商 - 分页查询 - 采方
  getNonStrategicForm: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/noStrategy/supplier/result/pageQuery`, data)
  },
  // 分层分级结果 - 非战略供应商 - 提交审批 - 采方
  nonStrategicSubmit: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/noStrategy/supplier/result/submitApprove`,
      data
    )
  },
  // 分层分级结果- 非战略供应商 - 导出 - 采方
  nonStrategicExportCate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/noStrategy/supplier/result/export`, data, {
      responseType: 'blob'
    })
  },
  // 分层分级结果 - 非战略供应商 - 导出明细 - 采方
  nonStrategicExportDetail: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/noStrategy/supplier/result/exportScoreDetail`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 分层分级结果 - 非战略供应商 - 发布 - 采方
  submitNonStrategic: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/noStrategy/supplier/result/publish`, data)
  },
  // 待打分明细 - 战略供应商筛选 - 编辑保存 - 采方
  nonStrategicSave: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/result/save`, data)
  },
  // 非战略供应商-导入 excel
  noStrategyResultImport: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/noStrategy/supplier/result/import`, data, {
      responseType: 'blob'
    })
  },
  // 分层分级结果 - 非战略供应商 - 发布 - 采方
  gradeResultPublish: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/strategy/result/total/publish`, data)
  },
  // 分层分级结果- 非战略供应商 - 导出 - 采方
  gradeResultExportCate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/strategy/result/total/export`, data, {
      responseType: 'blob'
    })
  },
  // 层级结果 - 供方
  gradeResultExportCateSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/assess/strategy/result/total/export`, data, {
      responseType: 'blob'
    })
  },

  // 层级结果tab详情查询 - 采方
  getSubmitApproveBefore: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/strategy/result/total/submitApproveBefore`,
      data
    )
  },
  // 汇总详情页面详情查询 - 采方
  summaryQueryDetail: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/strategy/result/total/approve/queryDetail`,
      data
    )
  },
  // 层级结果详情保存 - 采方
  saveOrUpdate: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/strategy/result/total/approve/saveOrUpdate`,
      data
    )
  },
  // 层级结果详情 提交 - 采方
  submitApprove: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/strategy/result/total/approve/submitApprove`,
      data
    )
  },

  // 分层分级结果-非战略供应商-分页查询 -供方
  getNonStrategicFormSUP: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/assess/noStrategy/supplier/result/pageQuery`,
      data
    )
  },
  // 分层分级结果 -非战略供应商 - 导出 - 供方
  NonStrategicExportCateSUP: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/assess/noStrategy/supplier/result/export`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 分层分级结果 - 非战略供应商 - 导出明细 - 供方
  nonStrategicExportDetailSUP: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/assess/noStrategy/supplier/result/exportScoreDetail`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 获取供应商层级
  getQuerySupplierLevel: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/assess/strategy/result/total/querySupplierLevel`,
      data
    )
  },

  // 获取分层分级评价周期-供方
  queryDictItemByDictCode: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/assess/strategy/supplier/result/queryDictItemByDictCode`,
      data
    )
  },

  // 采方-绩效通知函模板-分页
  pageNotificationTemplateApi: (data = {}) => {
    return API.post(`/analysis/tenant/performance/notificationTemplate/pageQuery`, data)
  },
  // 采方-绩效通知函模板-保存
  saveNotificationTemplateApi: (data = {}) => {
    return API.post(`/analysis/tenant/performance/notificationTemplate/saveTemplate`, data)
  },
  // 采方-绩效通知函模板-删除
  deleteNotificationTemplateApi: (data = {}) => {
    return API.post(`/analysis/tenant/performance/notificationTemplate/deleteTemplate`, data)
  },
  // 采方-绩效通知函模板-启用/停用
  updateStatusNotificationTemplateApi: (data = {}) => {
    return API.post(`/analysis/tenant/performance/notificationTemplate/updateTemplateStatus`, data)
  },

  // 采方-通知函抄送人设置-分页
  pageCcSettingApi: (data = {}) => {
    return API.post(`/analysis/tenant/performance/emailConfig/pageQuery`, data)
  },
  // 采方-通知函抄送人设置-保存
  saveCcSettingApi: (data = {}) => {
    return API.post(`/analysis/tenant/performance/emailConfig/saveConfig`, data)
  },
  // 采方-通知函抄送人设置-删除
  deleteCcSettingApi: (data = {}) => {
    return API.post(`/analysis/tenant/performance/emailConfig/deleteByIds`, data)
  },
  // 采方-通知函抄送人设置-导出
  exportCcSettingApi: (data = {}) => {
    return API.post(`analysis/tenant/performance/emailConfig/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-通知函抄送人设置-导入
  importCcSettingApi: (data = {}) => {
    return API.post(`analysis/tenant/performance/emailConfig/import`, data, {
      responseType: 'blob'
    })
  },
  // 采方-通知函抄送人设置-导入模板下载
  downloadCcSettingApi: (data = {}) => {
    return API.post(`analysis/tenant/performance/emailConfig/downTemplate`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
