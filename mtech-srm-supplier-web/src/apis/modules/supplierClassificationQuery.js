import { API } from '@mtech-common/http'
import qs from 'qs'
const NAME = 'supplierClassificationQuery'
const PROXY_BASE = '/analysis'
const APIS = {
  // 分类绩效查询 - 查询表单 - 获取指标维度评价分类下拉值
  getDimensionNameList: () => {
    return API.get(`${PROXY_BASE}/tenant/buyer/assess/classificationResult/getDimensionNameList`)
  },
  // 分类绩效查询 - 获取组织列表
  getPermissionOrgList: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/buyer/assess/comprehensiveResult/getOrgList`,
      qs.stringify(data),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 分类绩效查询 - 分页列表查询
  pageQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/classificationResult/pageQuery`, data)
  },
  // 分类绩效查询 - 导出
  export: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/classificationResult/export`, data, {
      responseType: 'blob'
    })
  }
}
export default {
  NAME,
  APIS
}
