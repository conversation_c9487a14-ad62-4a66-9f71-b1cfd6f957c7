import { API } from '@mtech-common/http'
const NAME = 'assessManage'
const PROXY_BASE = '/analysis/tenant'
const PROXY_MASTER = '/masterDataManagement'
const APIS = {
  /*
    考核接口
  */

  // 保存考核类型
  saveClaimType: (data) => {
    return API.post(`${PROXY_BASE}/claimType/saveClaimType`, data)
  },
  listClaimAppealArbitrator: (data) => {
    return API.post(`${PROXY_BASE}/claimType/listClaimAppealArbitrator`, data)
  },
  // 工厂
  fuzzySiteQuery: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/site/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },
  // 启用-禁用考核类型
  changeStatus: (data) => {
    return API.patch(`${PROXY_BASE}/claimType/turnClaimTypeState`, data)
  },
  // 删除考核类型
  deleteRecord: (data) => {
    return API.delete(`${PROXY_BASE}/claimType/deleteClaimType`, data)
  },
  // 考核类型详情
  detailClaimType: (data) => {
    return API.post(`${PROXY_BASE}/claimType/detailClaimType`, data)
  },
  // 获取启用的考核类型列表
  getAvailableClaimType: () => API.get(`${PROXY_BASE}/claimType/listAvailableClaimType`),
  // 获取启用的考核类型列表（考核单）
  getAvailableClaimTypeForClaim: () =>
    API.get(`${PROXY_BASE}/claimType/listAvailableClaimTypeForClaim`),
  // 获取考核类型所属公司
  listCompanyByClaimType: (data) => {
    return API.post(`${PROXY_BASE}/claimType/listCompanyByClaimType`, data)
  },
  // 保存考核类型申诉仲裁人列表
  saveClaimAppealArbitrator: (data) => {
    return API.post(`${PROXY_BASE}/claimType/saveClaimAppealArbitrator`, data)
  },

  /*
    指标接口
  */
  // 保存指标
  saveClaimStand: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimStand/saveClaimStand`, data)
  },
  // 获取指标详情
  getIndexDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimStand/detailClaimStand`, data)
  },
  // 获取启用的考核指标列表
  listAvailableClaimStand: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimStand/listAvailableClaimStand`, data)
  },
  // 获取公司下启用的考核指标列表
  listAvailableClaimStandByCompany: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimStand/listAvailableClaimStandByCompany`, data)
  },
  // 启用-停用考核指标
  changeClaimStandStatus: (data) => {
    return API.patch(`${PROXY_BASE}/claimStand/turnClaimStandState`, data)
  },
  // 删除考核指标
  deleteClaimStand: (data) => {
    return API.delete(`${PROXY_BASE}/claimStand/deleteClaimStand`, data)
  },
  /*
    协议书模板接口
  */
  // 保存协议书模板
  saveClaimAgreeTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimAgreementTemplate/saveClaimAgreementTemplate`, data)
  },
  // 启用-停用协议书模板
  turnClaimAgreeTempState: (data) => {
    return API.patch(`${PROXY_BASE}/claimAgreementTemplate/turnClaimAgreementTemplateState`, data)
  },
  // 删除协议书模板
  deleteClaimAgreeTemp: (data) => {
    return API.delete(`${PROXY_BASE}/claimAgreementTemplate/deleteClaimAgreementTemplate`, data)
  },
  // 协议书模板详情
  detailClaimAgreeTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimAgreementTemplate/detailClaimAgreementTemplate`, data)
  },
  // 获取协议书模板列表
  listClaimAgreeTemp: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimAgreementTemplate/listClaimAgreementTemplate`, data)
  },
  // 获取协议书模板所属公司
  listCompanyByTemplate: (data) => {
    return API.post(`${PROXY_BASE}/claimAgreementTemplate/listCompanyByTemplate`, data)
  },
  /*
    考核单接口
  */
  // 考核单确认扣款
  deductAmount: (data) => {
    return API.patch(`${PROXY_BASE}/claim/deductAmount`, data)
  },
  // 新增考核单
  saveClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveClaim`, data)
  },
  // 考核单详情（分页列表编辑按钮）（待发布）
  detailSuperficialClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/detailSuperficialClaim`, data)
  },
  // 完善考核单（点击编号进入）
  completeClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/completeClaim`, data)
  },
  // 完善并提交考核单
  completeAndSubmitClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/completeAndSubmitClaim`, data)
  },
  // 删除考核单
  deleteClaim: (data) => {
    return API.delete(`${PROXY_BASE}/claim/deleteClaim`, data)
  },
  // 取消考核单
  cancelClaim: (data) => {
    return API.patch(`${PROXY_BASE}/claim/cancelClaim`, data)
  },
  // 考核单详情（待发布）
  detailClaim: (data = {}) => {
    return API.get(`${PROXY_BASE}/claim/detailClaim`, data)
  },
  // 考核单详情通过code查询
  detailClaimByCode: (data = {}) => {
    return API.get(`${PROXY_BASE}/claim/jumpDetail`, data)
  },
  // 考核单申诉处理保存并提交审批
  saveAndSubmitClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveAndSubmitClaim`, data)
  },
  // 提交考核单
  submitClaim: (data) => {
    return API.patch(`${PROXY_BASE}/claim/submitClaim`, data)
  },
  // 考核单详情（考核单汇总-申诉处理）
  detailPublishedClaim: (data = {}) => {
    return API.get(`${PROXY_BASE}/claim/detailPublishedClaim`, data)
  },
  // 获取有效的考核单列表
  listAvailableClaim: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/listAvailableClaim`, data)
  },
  // 考核单发票列表
  listClaimInvoice: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/listClaimInvoice`, data)
  },
  // 保存考核单发票
  saveClaimInvoice: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveClaimInvoice`, data)
  },
  // 强制扣款
  purchaseConfirmClaimApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/purchaseConfirmClaim`, data)
  },
  /*------考核单申诉处理-------*/
  // 考核单申诉处理保存并提交审批
  saveAndSubmitClaimAppealDeal: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveAndSubmitClaimAppealDeal`, data)
  },
  // 考核单申诉处理保存
  saveClaimAppealDeal: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/saveClaimAppealDeal`, data)
  },
  // 考核单申诉处理-申诉驳回
  rejectClaimAppealDealApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/rejectClaimAppealDeal/${data.id}`)
  },
  // 查看OA链接-old
  getOALink: (data = {}) => {
    return API.get(`${PROXY_BASE}/claimConfig/getOALink`, data)
  },
  // 查看OA链接 -new
  infoGetOALink: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/info/getOALink`, data)
  },
  //考核OA链接 -new
  claimConfigGetOALink: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/claimConfig/getOALink`, data)
  },
  //采供双方发票补录附件
  saveAdditionalAttachment: (data = {}) => {
    return API.post(`/analysis/tenant/claim/saveAdditionalAttachment`, data)
  },
  // 考核单申诉处理-转办
  claimAppealDealTransfer: (data = {}) => {
    return API.post(`/analysis/tenant/claim/claimAppealDealTransfer`, data)
  },

  /******* 考核分析报表 ********/
  // 查询列表
  queryAssessAnalysisReportList: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/claimAnalysisPageQuery`, data)
  },
  // 导出
  exportAssessAnalysisReport: (data = {}) => {
    return API.post(`${PROXY_BASE}/claim/exportClaimAnalysis`, data, {
      responseType: 'blob'
    })
  },

  // 供应商生效管理-导出
  exportSupEffectiveList: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/effective/exportHeaderList`, data, {
      responseType: 'blob'
    })
  },

  // 考核单汇总-作废
  duplicateClaimApi: (data) => {
    return API.post(`/analysis/tenant/claim/duplicateClaim/${data.id}`)
  },

  // 获取字典
  getDictCode: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/dict-item/dict-code`, data)
  },

  // 采方-考核单管理-附件上传
  saveClaimAttachmentsApi: (data = {}) => {
    return API.post(`/analysis/tenant/claim/saveClaimAttachments`, data)
  }
}

export default {
  NAME,
  APIS
}
