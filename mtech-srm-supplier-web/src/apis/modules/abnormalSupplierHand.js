import { API } from '@mtech-common/http'
const NAME = 'abnormalSupplierHand'
const PROXY_BASE = '/supplier/tenant'
const MASTER = '/masterDataManagement/tenant'
// 主数据的
const APIS = {
  // 公司
  company: (data = {}) => {
    return API.get(`${MASTER}/supply-source-list/company`, data)
  },
  // 供应商
  queryIntersectionSupplier: (data = {}) => {
    return API.post(`${MASTER}/supply-source-list/queryIntersectionSupplier`, data)
  },
  // 品类
  queryIntersectionCategory: (data = {}) => {
    return API.post(`${MASTER}/supply-source-list/queryIntersectionCategory`, data)
  },
  // 物料
  queryIntersectionItem: (data = {}) => {
    return API.post(`${MASTER}/supply-source-list/queryIntersectionItem`, data)
  },
  // 新增
  update: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/apply/abnormal/update`, data)
  },
  // 提交
  submit: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/apply/info/submit`, data)
  },
  // 删除
  del: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/apply/abnormal/del`, data)
  },
  // 详情
  detail: (data = '') => {
    return API.get(`${PROXY_BASE}/buyer/apply/abnormal/detail/${data}`)
  },
  // 下载
  downloadPrivateFile: (data = '') => {
    return API.get(`/file/user/file/downloadPrivateFile`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
