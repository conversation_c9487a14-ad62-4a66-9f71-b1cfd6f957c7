import { API } from '@mtech-common/http'
const NAME = 'SupSystemCollaborationConfig'
const BASE_TENANT = '/srm-purchase-execute'

const APIS = {
  // 新增协同库存
  addSupSystemCollaborationConfig: (data = {}) => API.put(`${BASE_TENANT}/tenant/config`, data),

  // 保存协同库存
  saveSupSystemCollaborationConfig: (data = {}) => API.post(`${BASE_TENANT}/tenant/config`, data),

  // 删除协同库存信息
  deleteSupSystemCollaborationConfig: (ids = '') =>
    API.delete(`${BASE_TENANT}/tenant/config?ids=${ids.join()}`),

  // 启用&停用 - 协同库存
  startAndStopSupSystemCollaborationConfig: (data = {}) =>
    API.post(`${BASE_TENANT}/tenant/config/onLineAndOffLine?id=${data?.id}&status=${data?.status}`),

  // 根据id 获取协同库存信息
  getSupSystemCollaborationConfigInfo: (data = { id: '' }) =>
    API.get(`${BASE_TENANT}/tenant/config/${data.id}`),

  // 协同库存信息 - 导出
  SupSystemCollaborationConfigExport: (data = {}) =>
    API.post(`${BASE_TENANT}/tenant/config/export`, data, {
      responseType: 'blob'
    }),
  // 协同库存信息 - 导入
  importHazardousSubstancesInfo: (data = {}) =>
    API.post(`${BASE_TENANT}/tenant/config/import`, data, {
      responseType: 'blob'
    }),

  // 协同库存信息 - 导入模板下载
  importTemplateDown: (data = {}) =>
    API.get(`${BASE_TENANT}/tenant/config/import/template`, data, {
      responseType: 'blob'
    })
}

export default { NAME, APIS }
