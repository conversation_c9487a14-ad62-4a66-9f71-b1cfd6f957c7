import { API } from '@mtech-common/http'
const NAME = 'supplierlifecycle'
// 供应商
const PROXY_BASE = '/supplier'
const PROXY_FILE = '/file'
const PROXY_MASTERDATA = '/masterDataManagement'
const PROXY_ANALYSIS = '/analysis'

const APIS = {
  // 供应商准入-任务详情查询
  getlist: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/perspective/query`, data)
  },

  // 供应商准入-任务详情查询
  getDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/perspective/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方-采方待办任务查询
  getPending: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/task/pending/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方-供方待办任务查询
  getBuyer: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/task/buyer/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--全部任务查询
  gethistory: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/task/history/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 供方-采方待办任务查询
  getPendingSup: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/pending/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 供方-供方待办任务查询
  getBuyerSup: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/supplier/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 供方--全部任务查询
  gethistorySup: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/history/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--全部任务查询
  getTaskDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/task/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--全部任务查询
  getTaskDetailBySup: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--表单定义查询
  getFormDefine: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/task/formDefine`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--表单定义查询
  queryFormData: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/task/queryFormData`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方 --all表单定义查询 supplier/tenant/buyer/process/task/allFormDefine
  getAllFormDefine: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/task/allFormDefine`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--all表单定义查询
  queryAllFormData: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/task/queryAllFormData`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--任务表单保存
  saveFormData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/task/saveTaskFormData`, data)
  },

  // 采方--任务提交
  submitTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/task/submit`, data)
  },

  // 采方-准入进展列表查询
  stageQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/stage/query`, data)
  },

  // 采方-准入进展列表查询
  stageDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/process/stage/stageDetail`, data)
  },

  /**
   *  供应商画像的接口
   */

  // 供应商档案详情、工商信息
  getRecordDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/partner/detail`, data)
  },

  // 中标信息、合作次数、金额
  getRecordBidding: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/portrait/bidding`, data)
  },

  // 获取相关组织列表
  getOrganization: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/relation/organization`, data)
  },

  // 合作履历
  getPartnerEvent: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/portrait/partnerEvent`, data)
  },

  // 企业图谱
  getEnterpriseShip: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/partner/portrait/enterprise/ship`, data)
  },

  // 绩效信息、级别、、绩效得分
  getPerformance: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/portrait/performance`, data)
  },

  // 企业评分
  getCreditScore: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/partner/portrait/creditScore`, data)
  },

  /**
   * 标签接口集合
   */

  // 我的 定义合集
  getDefineList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/defineList`, data)
  },

  // 总标签
  getLabelList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/label/labelList`, data)
  },

  // 全量更新
  updateLabel: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/label/update`, data)
  },

  // 删除
  deleteLabel: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/label/delete`, data)
  },

  /**
   * file
   */
  publicImageFileUrl: (data = {}) => {
    return API.get(`${PROXY_FILE}/user/file/publicImageFileUrl`, data)
  },

  /**
   * file
   */
  commonFilesPath: (data = {}) => {
    return API.post(`${PROXY_FILE}/common/files/path`, data)
  },

  // 本组织分级接口
  queryDefinList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/list`, data)
  },

  // 分级哪边的获取父级组织树的接口
  getParentsTreeById: (data = {}) => {
    return API.get(`${PROXY_MASTERDATA}/tenant/organization/getParentsTreeById`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 分级哪边的获取父级组织树的接口
  getAmountDetail: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/archive/data/amountDetail`, data)
  },

  // 档案列表导出
  perspectiveExport: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/archive/excel/export`, data, {
      headers: { responseType: 'blob' }
    })
  },
  // 查询供应商历史变更信息
  applyQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/apply/query`, data)
  },

  /********** 供应方准入管理 ************/
  // 采方--查看调查表--获取全部任务列表
  getTaskList(data = {}) {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/task/stage/query`, data)
  },
  // 采方--查看调查表--保存
  saveTaskStage(data = {}) {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/task/stage/save`, data)
  },
  // 采方--查看调查表--提交
  submitTaskStage(data = {}) {
    return API.post(`${PROXY_BASE}/tenant/buyer/process/task/stage/approve`, data)
  },
  // 采方--门槛匹配详情
  getThresholdDetail(id = '') {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/threshold/detail/${id}`)
  },
  // 采方--门槛匹配详情_新
  getThresholdDetail_new(data = {}) {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/threshold/acquireThreshold`, data)
  },
  // 采方--准入详情--供应商关联关系表
  getByInviteIdApi(data = {}) {
    return API.post(`${PROXY_BASE}/tenant/buyer/relation/path/getByInviteId`, data)
  },
}

export default {
  NAME,
  APIS
}
