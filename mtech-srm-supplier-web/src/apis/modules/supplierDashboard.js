import { API } from '@mtech-common/http'
const NAME = 'supplierDashboard'
// 供应商的
const PROXY_BASE = '/analysis'
const APIS = {
  // 获取供应商级别
  querySupplierLevel: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/querySupplierLevel`, data)
  },
  // 获取各品类中标金额
  getBar: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/biddingAmount/bar`, data)
  },
  // 获取品类供应商数
  queryCategorySupplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/queryCategorySupplier`, data)
  },
  // 获取供应商分布
  queryDistribution: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/cockpit/queryDistribution`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 获取供应商个数变化
  querySupplierChange: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/querySupplierChange`, data)
  },
  // 获取供应商生态
  queryEcology: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/cockpit/queryEcology`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 中标金额变化情况
  queryTrend: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/biddingAmount/trend`, data)
  },
  // 支出金额变化情况
  queryExpend: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/purchaseAmount/trend`, data)
  },
  // 获取综合数据统计
  queryTotalCount: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/cockpit/totalCount`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 获取各品类支出金额
  getPurchaseAmountBar: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/purchaseAmount/bar`, data)
  },
  // 平台活跃度
  supplierActivity: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/cockpit/supplierActivity`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 黑名单列表
  getBlackList: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/cockpit/black/list`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 绩效top10
  queryScoreTen: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/queryScore`, data)
  },
  // 各品类支出金额TOP10
  purchaseAmountTen: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/purchaseAmount/top`, data)
  },
  // 各品类中标金额top10
  biddingAmountTen: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/cockpit/biddingAmount/top`, data)
  },
  // 生态合作金额top10
  querySupplierAmountTop: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/cockpit/querySupplierAmountTop`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  }
}

export default {
  NAME,
  APIS
}
