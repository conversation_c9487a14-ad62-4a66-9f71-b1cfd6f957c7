// 供应商-复兴-采购数据管理
import { API } from '@mtech-common/http'
const NAME = 'supplierPurchaseData'
const PROXY_ANALYSIS = '/analysis'
const PROXY_SUPPLY = '/supplier'
const APIS = {
  // 添加单据数据
  addPurchaseData: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/purchase/data/add`, data)
  },
  // 删除单据数据  ok
  deletePurchaseData: (params = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/purchase/data/delete`, params)
  },
  // 查询单据详情   un-use
  getPurchaseDataDetail: (params = {}) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/purchase/data/detail`, params)
  },
  // 查询单据数据列表  ok
  getPurchaseDataList: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/purchase/data/list`, data)
  },
  getPurchaseData: `${PROXY_ANALYSIS}/tenant/buyer/purchase/data/list`,

  // 提交单据数据  ok
  submitPurchaseData: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/purchase/data/submit`, data)
  },
  // 更新单据数据  un-use
  updatePurchaseData: (data = {}) => {
    return API.put(`${PROXY_ANALYSIS}/tenant/buyer/purchase/data/update`, data)
  },
  // 上传供应商数据Excel表  ok
  uploadPurchaseData: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/purchase/data/upload`, data)
  },
  // 审批单据数据   ok
  verifyPurchaseData: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/purchase/data/verify`, data)
  },
  // 查询用户信息   ok
  queryUserInfo: (params = {}) => {
    return API.get(`/iam/common/account/userinfo`, params)
  },
  queryUserDetail: (params = {}) => {
    return API.get(`/iam/tenant/account/user-detail`, params)
  },

  // 租户级-通用-附件信息
  // 附件查询--用于查询‘采购数据上报模板’
  queryCommonFile: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/common/file/query`, data)
  }
}

export default {
  NAME,
  APIS
}
