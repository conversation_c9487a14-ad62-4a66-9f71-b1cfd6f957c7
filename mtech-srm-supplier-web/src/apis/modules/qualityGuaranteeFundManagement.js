/********* 质保金管理 ***********/
import { API } from '@mtech-common/http'
const NAME = 'QualityGuarFundfManagement'
const PROXY_STAT = '/statistics'

const APIS = {
  /************** 质保金台账-扣留标准 ************/
  // 查询列表
  queryDetainStandardList: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/supplierRetention/query`, data)
  },
  // 查询扣留标准明细数据
  queryDetainStandardDetailList: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/supplierRetention/query/detail`, data)
  },
  // 新增
  addDetainStandard: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/supplierRetention/save`, data)
  },
  // 修改
  updateDetainStandard: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/supplierRetention/update`, data)
  },
  // 删除
  deleteDetainStandard: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/supplierRetention/remove`, data)
  },
  // 启用、停用
  updateDetainStandardStatus: (data = {}) => {
    return API.post(
      `${PROXY_STAT}/tenant/supplierRetention/update/status?status=${data.status}`,
      data.idList
    )
  },
  // 明细-删除
  deleteDetainStandardDetail: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/supplierRetention/remove/detail`, data)
  },

  /************** 质保金台账-供应商质保台账 ************/
  // 查询列表
  queryDetainBookList: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retentionBook/query`, data)
  },
  // 刷新列表
  refreshDetainBookList: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retentionBook/flush`, data)
  },
  // 保存
  saveDetainBook: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retentionBook/save`, data)
  },
  // 删除
  deleteDetainBook: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retentionBook/remove`, data)
  },
  // 计算
  calculateDetainBook: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retentionBook/auto`, data)
  },
  // 查询启用版本下拉列表
  queryEnableVersionList: (data = {}) => {
    return API.get(`${PROXY_STAT}/tenant/retentionBook/findVersion`, data)
  },
  // 下载导入模板
  downloadDetainBookTemplate: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retentionBook/download/template`, data, {
      responseType: 'blob'
    })
  },
  // 导入
  importDetainBook: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retentionBook/importRetentionMoney`, data, {
      responseType: 'blob'
    })
  },
  // 导出
  exportDetainBook: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retentionBook/export`, data, {
      responseType: 'blob'
    })
  },

  /************** 质保金申请 ************/
  // 查询列表
  queryApplyList: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/query`, data)
  },
  // 列表-新增
  addApply: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/save`, data)
  },
  // 列表-删除
  deleteApply: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/delete`, data)
  },
  // 明细-列表查询
  queryApplyDetailById: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/findById?id=${data.id}`, {})
  },
  // 明细-列表查询
  queryApplyDetailList: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/detail/query?id=${data.id}`, data)
  },
  // 明细-供应商下拉列表查询
  querySupplierList: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/detail/query/supplierCode`, data)
  },
  // 明细-保存
  saveApplyDetail: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/batchSave`, data)
  },
  // 明细-提交
  submitApplyDetail: (data = {}) => {
    return API.put(`${PROXY_STAT}/tenant/retention/submitOa`, data)
  },
  // 明细-删除
  deleteApplyDetail: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/detail/remove`, data)
  },
  // 明细-计算区间
  calculateRangeApplyDetail: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/autoQuery`, data)
  },
  // 明细-下载导入模板
  downloadApplyDetailTemplate: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/detail/download/template`, data, {
      responseType: 'blob'
    })
  },
  // 明细-导入
  importApplyDetail: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/detail/importRetentionDetail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    })
  },
  // 明细-导出、导入模板
  exportApplyDetail: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/retention/detail/export?id=${data.id}`, data, {
      responseType: 'blob'
    })
  },
  // 附件-列表查询
  queryApplyAttachmentList: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/file/queryFileList`, data)
  },
  // 附件-保存
  saveApplyAttachment: (data = {}) => {
    return API.post(`${PROXY_STAT}/tenant/file/saveFile`, data)
  },
  // 附件-删除
  deleteApplyAttachment: (data = {}) => {
    return API.put(`${PROXY_STAT}/tenant/file/deleteById`, data)
  }
}
export default {
  NAME,
  APIS
}
