import { API } from '@mtech-common/http'

const NAME = 'supplierCategoryBuAssociation'
const BASE_PATH = '/supplier/tenant/supplier/category/bu/rel'

const APIS = {
  // 供应商品类BU关联表 - 分页查询
  pageQuery: (data = {}) => API.post(`${BASE_PATH}/pageList`, data),
  // 供应商品类BU关联表 - 新增
  add: (data = {}) => API.post(`${BASE_PATH}/add`, data),
  // 供应商品类BU关联表 - 修改
  update: (data = {}) => API.post(`${BASE_PATH}/edit`, data),
  // 供应商品类BU关联表 - 删除
  delete: (data = {}) => API.post(`${BASE_PATH}/deleteByIds`, data),
  // 供应商品类BU关联表 - 详情
  getDetail: (data = {}) => API.post(`${BASE_PATH}/detailById`, data),
  // 供应商品类BU关联表 - 提交OA
  submitOa: (data = {}) => API.post(`${BASE_PATH}/submitOA`, data),
  // 供应商品类BU关联表 - 查看OA
  checkOa: (data = {}) => API.post(`${BASE_PATH}/getOALink`, data),
  // 供应商品类BU关联表 - 导出
  export: (data = {}) => API.post(`${BASE_PATH}/export`, data, { responseType: 'blob' }),
  // 供应商品类BU关联表 - 根据公司+供应商+品类+采购组织查询唯一记录
  getSupplySourceResponseByOrgSuppCatePur: (data = {}) =>
    API.post(`/masterDataManagement/tenant/supply/source/getSupplySourceResponseByOrgSuppCatePur`, data),
  // 供应商品类BU关联表 - 根据公司+供应商查询品类列表
  getSupplySourceResponseByOrgSuppCate: (data = {}) =>
    API.post(`/masterDataManagement/tenant/supply/source/getSupplySourceResponseByOrgSuppCate`, data)
}

export { NAME, APIS }
export default { NAME, APIS }