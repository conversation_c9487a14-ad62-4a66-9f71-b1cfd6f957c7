// 供应商关系管理

import { API } from '@mtech-common/http'
import qs from 'qs'

const NAME = 'supplierRelationshipReport'
const PROXY_BASE = '/supplier/tenant'

const APIS = {
  // 新增
  addPingcaiSuplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/pingcai/group/add`, data)
  },
  // 更新
  updatePingcaiSuplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/pingcai/group/update`, data)
  },
  // 导入
  supplierImport: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/pingcai/group/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 下载标准模板
  supplierExportTmp: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/pingcai/group/download`, data, {
      responseType: 'blob'
    })
  },
  // 导出
  supplierExportQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/pingcai/group/export`, data, {
      responseType: 'blob'
    })
  },
  // 删除
  deletePingcaiSuplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/pingcai/group/delete`, data)
  },
  // 原厂及代理供应商
  // 新增
  addOriginalAgent: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/original/factory/agent/add`, data)
  },
  // 编辑
  updateOriginalAgent: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/original/factory/agent/update`, data)
  },
  // 导入
  originalImport: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/original/factory/agent/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 下载标准模板
  originalExportTmp: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/original/factory/agent/download`, data, {
      responseType: 'blob'
    })
  },
  // 导出
  originalExportQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/original/factory/agent/export`, data, {
      responseType: 'blob'
    })
  },
  // 删除
  deleteOriginal: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/original/factory/agent/delete`, data)
  },
  // 生效 / 失效
  operateOriginal: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/original/factory/agent/operate`, data)
  },

  // 海外直采供应商 - 导出
  exportOverseasApi: (data = {}) => {
    return API.post(`/srm-purchase-execute/tenant/distribution/supplier/export`, data, {
      responseType: 'blob'
    })
  },

  /********* 多级供应商 **********/
  // 新增
  addMultilevelSupplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/multiLevelSupplier/header/createHeader`, data)
  },
  // 更新状态（生效、失效）
  updateMultiSupStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/multiLevelSupplier/header/updateStatus`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 明细-单据信息
  getMultiSupDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/multiLevelSupplier/header/getDetail`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 明细-保存
  saveMultiSupDetail: (data = {}) => {
    return API.post(`${PROXY_BASE}/multiLevelSupplier/header/updateHeader`, data)
  },
  // 明细-导入
  importMultiSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/multiLevelSupplier/header/importItemList`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 明细-导出
  exportMultiSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/multiLevelSupplier/header/exportItemList`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    })
  }
}

export default {
  APIS,
  NAME
}
