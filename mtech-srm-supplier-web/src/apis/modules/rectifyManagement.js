// 品类认证管理
import { API } from '@mtech-common/http'
const NAME = 'rectifyManagement'
const PROXY_BASE = '/supplier'

const APIS = {
  // ----------采方---------------
  // 认证需求列表
  querySupplierList: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/partner/relation/getOrgPartnerRelationsByStatus`,
      data
    )
  },
  // 单据详情
  queryDetail: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/partner/reform/query/${id}`)
  },
  // 保存
  save: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/reform/save`, data)
  },
  // 发布
  release: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/reform/release`, data)
  },
  // 保存并发布
  saveAndRelease: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/reform/saveAndRelease`, data)
  },
  // 通过
  approve: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/reform/approve`, data)
  },
  // 驳回
  reject: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/reform/reject`, data)
  },
  // 删除
  delete: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/reform/delete`, data)
  },

  // ------------供方----------------
  // 保存
  supSave: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/reform/save`, data)
  },
  // 提交
  submit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/reform/submit`, data)
  },
  // 保存并提交
  saveAndSubmit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/partner/reform/saveAndSubmit`, data)
  },
  // 反馈
  querySupDetail: (id) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/partner/reform/feedback/${id}`)
  },

  // 供应商整改管理-导出
  export: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/partner/reform/export`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
