// 阶段配置 接口
import { API } from '@mtech-common/http'
const NAME = 'DataPreparation'
// const PROXY_BASE = "/supplier";
const PROXY_AN = '/analysis'
// 主数据的
// const PROXY_Master = "/masterDataManagement";
const APIS = {
  // 树
  queryAllCatalogue: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/data/catalogue/queryAllCatalogue`, data)
  },
  // 分页表格查询
  queryBasicDate: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/data/field/queryBasicDate`, data)
  },
  // 表字段查询
  query: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/data/field/query`, data)
  },
  //导入
  importBasicData: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/data/file/importBasicData`, data)
  },
  //手动打分详情
  manualDetail: (data = {}) => {
    return API.get(`${PROXY_AN}/tenant/buyer/assess/archive/manual/detail`, data)
  },
  //手动打分保存
  manualDetailsave: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/archive/manual/score`, data)
  },
  //手动打分保存并提交
  manualDetailsubmit: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/archive/manual/detail/submit`, data)
  },
  //导出
  exportFile: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/archive/manual/detail/export`, data, {
      responseType: 'blob'
    })
  },
  fileUpload: (data = []) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/archive/manual/detail/import`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
  // /tenant/buyer/assess/archive/manual/detail/save
}

export default {
  NAME,
  APIS
}
