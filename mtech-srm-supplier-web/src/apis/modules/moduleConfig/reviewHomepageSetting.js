// 添加评审策略
import { API } from '@mtech-common/http'
const NAME = 'policySetting'
// const PROXY_BASE = "/supplier"; 暂时不用
const PROXY_ANALYSIS = '/analysis'
// 主数据的
// const PROXY_Master = "/masterDataManagement";
const APIS = {
  // 查询策略设置列表
  getList: `${PROXY_ANALYSIS}/tenant/buyer/strategy/set/list`,
  // getList: (data = {}) => {
  //   return API.post(`${PROXY_BASE}/tenant/buyer/strategy/set/list`, data);
  // },

  // 添加策略设置计划列表
  addListQuery: (data = {}) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/assess/plan/queryPlanByCompanyId`, data)
  },
  // 添加策略设置计划列表下分类
  addByPlanuery: (data = {}) => {
    // return API.get(`${PROXY_ANALYSIS}/tenant/buyer/strategy/set/getPlanCategoryRangeCatTreeByPlanId`, data);
    return API.get(
      `${PROXY_ANALYSIS}/tenant/buyer/category/getPlanCategoryRangeCatTreeByPlanId`,
      data
    )
  },
  // 添加策略设置
  addPolicy: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/strategy/set/add`, data)
  },
  // 删除策略设置
  deletePolicy: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/strategy/set/delete`, data)
  },

  // 查询策略设置详情
  getDetail: (data = {}) => {
    return API.get(`${PROXY_ANALYSIS}/tenant/buyer/strategy/set/detail`, data)
  },
  // 启用、停用策略设置
  enable: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/strategy/set/enable`, data)
  },

  // 更新策略设置
  update: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/strategy/set/update`, data)
  },
  // 复制策略设置
  copy: (data = {}) => {
    return API.post(`${PROXY_ANALYSIS}/tenant/buyer/strategy/set/copy`, data)
  }
}
export default {
  NAME,
  APIS
}
