// 供应商-绩效管理-分析设置
import { API } from '@mtech-common/http'
const NAME = 'reviewPlan' // 接口前缀名
// const PROXY_ANALYSIS = "/analysis";
const PROXY_ANALYSISAS = '/supplier'
const PROXY_MASTER = '/masterDataManagement' // 主数据取公司接口前缀名
const APIS = {
  //评审计划列表 字母表
  cateplantegy: `${PROXY_ANALYSISAS}/tenant/buyer/review/plan/pageQuery`,
  //建议评审清单列表
  catesuggest: `${PROXY_ANALYSISAS}/tenant/buyer/review/suggest/pageQuery`,
  //评审策略列表
  strategygest: `${PROXY_ANALYSISAS}/tenant/buyer/review/strategy/pageQuery`,
  //评审策略选中后 更新列表
  relationgest: `${PROXY_ANALYSISAS}/tenant/buyer/partner/relation/search`,
  //创建评审计划中得新增接口
  Listfilter: `${PROXY_ANALYSISAS}/tenant/buyer/review/suggest/selectLittle`,
  // 公司接口
  findSpecif: (data = []) => {
    return API.post(`${PROXY_MASTER}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
  },
  // 品类 评审策略中 - 旧
  productecif: (data = []) => {
    return API.post(`${PROXY_MASTER}/tenant/category/product-list`, data)
  },
  // 品类 评审策略中 - 远程模糊匹配
  getCategoryData: (data = {}) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/category/cycle/find`, data)
  },
  // 我的评分批量提交
  submitScoreBatch: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/task/submitScoreBatch
    `,
      data
    )
  },
  // 删除建议评审清单
  suggestdel: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/suggest/del
      `,
      data
    )
  },
  // 删除评审计划
  strategydel: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/strategy/del
      `,
      data
    )
  },
  // 评审计划编辑
  strupdatel: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/plan/update
      `,
      data
    )
  },

  // 删除建议评审清单
  straplandel: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/plan/del
      `,
      data
    )
  },
  // 策略评审启用/停用
  strategyupdateStatus: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/strategy/updateStatus
      `,
      data
    )
  },
  // 添加建议评审清单
  suggestAdd: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/suggest/add 
      `,
      data
    )
  },
  // 添加评审策略
  strategyriveAdd: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/strategy/add
      `,
      data
    )
  },
  // 编辑评审策略
  updateriveAdd: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/strategy/update
      `,
      data
    )
  },
  // 评审策略详情
  detailriveAdd: (data = {}) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/strategy/detail?id=${data.id}
      `
    )
  },
  //  // 评审计划详情
  strudetaill: (data = {}) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/plan/detail?id=${data.id}
      `
    )
  },

  // 创建评审计划
  suplanAdd: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/plan/add
      `,
      data
    )
  },
  // 公司所选供应商
  getOrgPartnerRelations: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/partner/relation/getOrgPartnerRelations
      `,
      data
    )
  },
  // 公司所选供应商
  getCategoryPartnerRelations: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/partner/relation/getCategoryPartnerRelations
      `,
      data
    )
  },
  // 根据公司id查询评审策略名字
  queryAlltions: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/strategy/queryAll 
      `,
      data
    )
  },
  //  批量添加建议评审（依据评审策略）
  addByStrategy: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/suggest/addByStrategy
      `,
      data
    )
  },
  //  评审计划提交接口
  updatePlanStatus: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/plan/updatePlanStatus
      `,
      data
    )
  },
  //  评审计划提交接口
  updateStatus: (data = []) => {
    return API.post(
      `${PROXY_ANALYSISAS}/tenant/buyer/review/suggest/update 
      `,
      data
    )
  },

  taskuserType: (data = []) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/review/task/userType`, data)
  },

  taskTurn: (data = []) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/review/task/turn`, data)
  },

  // 建议评审清单-导出
  exportSuggestList: (data = {}) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/review/suggest/export`, data, {
      responseType: 'blob'
    })
  },
  // 评审计划-导出
  exportReviewPlan: (data = {}) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/review/plan/export`, data, {
      responseType: 'blob'
    })
  },
  // 评审清单-导出
  exportReviewList: (data = {}) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/review/task/task/export`, data, {
      responseType: 'blob'
    })
  },
  // 我的评分-导出
  exportMyScore: (data = {}) => {
    return API.post(`${PROXY_ANALYSISAS}/tenant/buyer/review/task/myScore_export`, data, {
      responseType: 'blob'
    })
  }
}
export default {
  NAME,
  APIS
}
