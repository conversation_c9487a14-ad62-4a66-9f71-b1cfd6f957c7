import { API } from '@mtech-common/http'
const NAME = 'accessangeMange'
const PROXY_BASE = '/supplier'
const PROXY_File = '/file'
// 主数据的
const APIS = {
  // 供方获取资质审查单
  getExamination: `${PROXY_BASE}/tenant/supplier/qualification/manager/listBySupplier`,
  //供方资质审查提交
  getsubmission: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/qualification/manager/supplierSubmit`, data)
  },
  //   供方查询资质库
  getqualification: `${PROXY_BASE}/tenant/supplier/qualification/manager/qualificationDatabasePage`,
  // 供方查询资质库(详细包含详细的动态字段)
  Dynamiclibrary: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/supplier/qualification/manager/qualificationDatabaseDesc`,
      data,
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },
  // 查询审查单的资质项列表（已用下面的接口，这个不知道别处用不用到）
  getDynamicFieldUnionList: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/supplier/qualification/manager/getDynamicFieldUnionList`,
      data
    )
  },
  // 考核指标代码选择
  listAvailableClaimStandByCompany: (data = {}) => {
    return API.post(`/analysis/tenant/claimStand/listAvailableClaimStandByCompany`, data)
  },
  // 查询供方待审核数据==详情页
  getQualificationDesc: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/qualification/manager/getQualificationDesc`, data)
  },
  // 查询审查单动态字段列表
  getQualificationItemList: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/supplier/qualification/manager/getQualificationItemList`,
      data
    )
  },
  saveQualification: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/qualification/manager/save`, data)
  },
  // 文件上传
  fileUpload: (data = []) => {
    return API.post(`${PROXY_File}/user/file/uploadPublic`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  // 文件上传
  export: (data = {}) => {
    return API.post(`/analysis/tenant/claim/export`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
