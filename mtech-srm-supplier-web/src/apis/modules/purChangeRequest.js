import { API } from '@mtech-common/http'
const NAME = 'purChangeRequest'
const PROXY_BASE = '/supplier'
const PROXY_MASTER = '/masterDataManagement'
//采方-4M1E变更申请
const APIS = {
  // 根据字典类型编码获取字典详情
  queryDict: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/dict-item/dict-code`, data)
  },
  // 分页查询
  pageList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/change/4m/pageList`, data)
  },
  //明细
  detail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/change/4m/detail`, data)
  },
  //驳回
  refuse: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/change/4m/refuse`, data)
  },
  //提交
  submitOA: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/change/4m/submitOA`, data)
  },
  // 所有人员 -new
  page: (data = {}) => {
    return API.post(`/iam/tenant/granted-subject/auth/users/page`, data)
  },
  // 所有人员 -new
  employee: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/employee/info-paged-query`, data)
  }
}
export default {
  NAME,
  APIS
}
