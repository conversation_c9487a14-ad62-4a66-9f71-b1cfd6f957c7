// 考核模板设置

/*
  客户准入管理
*/

import { API } from '@mtech-common/http'
const NAME = 'assessmentTemplateSetting'
const PROXY_BASE = '/analysis/tenant'

const APIS = {
  // 列表-失效
  invalidOrEffect: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimTemplate/invalidOrEffect`, data)
  },
  // 列表-删除
  delete: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimTemplate/delete`, data)
  },
  // 列表-提交
  submit: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimTemplate/submit`, data)
  },
  // 进入详情
  queryDetail: (id = '') => {
    return API.get(`${PROXY_BASE}/claimTemplate/queryDetail/${id}`)
  },
  // 详情-保存
  add: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimTemplate/add`, data)
  },
  // 详情-保存并提交
  addSubmit: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimTemplate/addSubmit`, data)
  },
  // 详情-更新
  updateStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimTemplate/updateStatus`, data)
  },

  // 详情-适用组织
  query: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimTemplate/query`, data)
  },
  // 详情-获取考核类型
  dictCode: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/dict-item/dict-code`, data)
  },
  // 详情-仲裁人
  currentTenantEmployees: (data = {}) => {
    return API.get(`/masterDataManagement/tenant/employee/currentTenantEmployees`, data)
  },
  // 详情-引用模板
  queryIPage: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimTemplate/queryIPage`, data)
  },
  // 详情-指标名称 --old
  pageClaimStand: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimStand/pageClaimStand`, data)
  },
  // 详情-指标名称  --new
  queryStandByType: (data = {}) => {
    return API.post(`${PROXY_BASE}/claimStand/queryStandByType`, data)
  },
  // 详情-登录人
  userinfo: (data = {}) => {
    return API.get(`/iam/common/account/userinfo`, data)
  }
}

export default {
  APIS,
  NAME
}
