/*
  客户准入管理
*/

import { API } from '@mtech-common/http'
const NAME = 'customerAccess'
const PROXY_BASE = '/supplier'

const APIS = {
  // 客户准入 准入列表
  getAccessList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/access/query`, data)
  },
  // 客户准入 准入详情
  getAccessInfo: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/access/${data.id}`)
  },
  // 客户准入 阶段任务查询
  getStageTaskList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/form/task/stage/query`, {}, {}, data)
  },
  // 客户准入 阶段任务详情查询
  getStageTaskInfo: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/form/task/${data.id}`)
  },
  // 客户准入 阶段任务提交
  submitStageTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/form/task/submit`, data)
  },
  // 客户准入 阶段任务保存
  saveStageTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/form/task/save`, data)
  },

  // new  供方-准入进展列表查询
  stageQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/process/stage/query`, data)
  },

  // 供应商准入-任务详情查询
  getDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/perspective/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方-采方待办任务查询
  getPending: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/pending/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方-供方待办任务查询
  getBuyer: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/supplier/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--全部任务查询
  gethistory: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/history/query`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 供方-调查表任务查询
  queryQuestionnaire: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/process/task/questionnaire/query`, data)
  },
  // 供方--任务全部提交
  submitAllTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/process/task/submit`, data)
  },

  // 供方--根据企业ID获取中国工商企业信息
  getBusinessEnterpriseInformationApi: (data = {}) => {
    return API.post(
      `/masterDataManagement/tenant/enterprise/getBusinessEnterpriseInformation/${data.id}`
    )
  },
  // 供方--根据企业ID获取中国工商企业纳税评级信息
  getTaxRatingApi: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/enterprise/getTaxRating/${data.id}`)
  },
  // 供方--根据企业ID获取企业基本信息
  getBaseInfoApi: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/enterprise/getBaseInfo/${data.id}`)
  },
  // 供方--根据企业ID获取企业简介
  getProfileApi: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/enterprise/getProfile/${data.id}`)
  },
  // 供方--根据企业ID获取企业股东信息
  getHolderApi: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/enterprise/getHolder/${data.id}`)
  },

  // 采方--全部任务查询
  getTaskDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--表单定义查询
  getFormDefine: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/formDefine`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--表单定义查询
  queryFormData: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/queryFormData`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--表单定义查询
  getAllFormDefine: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/allFormDefine`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--表单定义查询
  queryAllFormData: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/task/queryAllFormData`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--任务表单保存
  saveFormData: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/process/task/saveTaskFormData`, data)
  },

  // 采方--任务提交
  submitTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/process/task/submit`, data)
  },

  // 采方-准入进展列表查询
  stageDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/stage/stageDetail`, data)
  },

  // 采方-客户搜索
  queryCustomers: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/access/queryCustomers`, data)
  },

  // 供方-供应商自查
  getOdminSelfReviewApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/odmin/self/review/getOdminSelfReview`, data)
  },
  // 供方-获取附件模板id
  getOdminSelfReviewResultTemplateFileApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/odmin/self/review/getOdminSelfReviewResultTemplateFile`,
      data
    )
  },
  // 供方-供应商自查-查看结果附件
  getOdminSelfReviewResultFileSupApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/odmin/self/review/getOdminSelfReviewResultFile/${data.id}`
    )
  },
  // 供方-供应商自查-上传结果附件
  uploadOdminSelfReviewResultFileApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/odmin/self/review/uploadOdminSelfReviewResultFile`,
      data
    )
  },
  // 供方-供应商自查-删除结果附件
  deleteOdminSelfReviewResultFileApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/odmin/self/review/deleteOdminSelfReviewResultFile`,
      data
    )
  },
  // 供方-供应商自查-修改供方备注
  updateSupplierOdminSelfReviewApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/supplier/odmin/self/review/updateSupplierOdminSelfReview`,
      data
    )
  },
  // 采方-供应商自查-根据品类关系查看ODMIN自查单
  getByCategoryPartnerRelationIdApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/odmin/self/review/getByCategoryPartnerRelationId/${data.id}`
    )
  },
  // 供方-供应商自查-查看结果附件
  getOdminSelfReviewResultFileApi: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/odmin/self/review/getOdminSelfReviewResultFile/${data.id}`
    )
  },

  // 供方-预审调查表
  queryPreReviewInvestigationSupApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/preReviewSurvey/supplier/queryPage`, data)
  },
  // 供方-预审调查表-根据id获取详情
  detailPreReviewInvestigationSupApi: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/preReviewSurvey/detail`, data)
  },
  // 供方-预审调查表-保存
  savePreReviewInvestigationSupApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/preReviewSurvey/save`, data)
  }
}

export default {
  APIS,
  NAME
}
