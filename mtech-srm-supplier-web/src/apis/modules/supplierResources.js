import { API } from '@mtech-common/http'
const NAME = 'supplierResources'
const PROXY_BASE = '/supplier'
// 主数据的
const PROXY_Master = '/masterDataManagement'
const APIS = {
  // 新增共享调查表
  addFormTemplate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/share/add`, data)
  },
  // 新增分级调查表
  addFormTemplatea: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/grade/add`, data)
  },
  // 新增惩罚调查表
  addFormTemplateb: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/punish/add`, data)
  },
  //获取共享表详情
  getDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/share/detail`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 查询公司 模糊查询
  getCompanys: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/company/fuzzy-query`, data)
  },

  // 获取分级
  getclassify: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/defineList`, data)
  },
  // 获取公司 集团 板块 事业群列表
  getRelations: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/organization/criteria-query`, data)
  },

  // 获取资源传参 http://srm.dev.qeweb.com/api/search-service/supplier/base/queryPage
  queryResourcePage: (data = {}) => {
    return API.post(`/search-service/supplier/base/queryPage`, data)
  },

  // 根据字典类型代码获取字典明细树
  getDictItem: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/dict-item/item-tree`, data)
  },

  // 租户级-国内省市区县接口-根据怕parentCode获取地址
  selectByParentCode: (data = {}) => {
    return API.post(`${PROXY_Master}/tenant/area/selectByParentCode`, data)
  },
  // 获取当前组织的父级组织节点
  getfathernode: (data = {}) => {
    return API.get(`${PROXY_Master}/tenant/organization/getParentOrg`, data)
  },

  // 采方标签配置 定义集合
  getDefineList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/defineList`, data)
  }
}

export default {
  NAME,
  APIS
}
