import { API } from '@mtech-common/http'
const NAME = 'sourceLists'
const PROXY_MASTER = '/masterDataManagement'
const APIS = {
  // 子表的查询
  criteriaQuery: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/supply-source-list/criteria-query`, data)
  },

  // 根据父级ID查询下级的品类列表
  listByParent: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/category/list-by-parent`, data)
  },

  // 工厂
  fuzzyQuery: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/site/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },
  // 物料
  fuzzyPagedQuery: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/item/fuzzy-paged-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  // 新增
  batchAdd: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/supply-source-list/batch-add`, data)
  },

  // 模糊搜索品类
  queryForTree: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/category/query-for-tree`, data)
  },

  // 供应商
  pagedQuery: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/supplier/paged-query`, data)
  },

  // 采购组织
  queryByOrgcode: (data = {}) => {
    return API.get(`${PROXY_MASTER}/tenant/business-partner/list-purchasing-groups`, data)
  },

  // 同步SAP
  batchSyncOutward: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/supply-source-list/batch-sync-outward`, data)
  },

  // 编辑
  batchUpdate: (data = {}) => {
    return API.put(`${PROXY_MASTER}/tenant/supply-source-list/batch-update`, data)
  },
  // 保存并同步SAP 批量新增并同步
  batchAddAndSync: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/supply-source-list/batch-add-and-sync`, data)
  },
  // 保存并同步SAP 批量新增并同步
  batchUpdateAndSync: (data = {}) => {
    return API.put(`${PROXY_MASTER}/tenant/supply-source-list/batch-update-and-sync`, data)
  },

  // 供应商信息接口
  criteriaQuerySupplier: (data = {}) => {
    return API.post(`${PROXY_MASTER}/tenant/supplier/criteria-query`, data)
  }
}
export default {
  NAME,
  APIS
}
