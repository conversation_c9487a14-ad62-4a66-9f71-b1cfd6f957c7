import { API } from '@mtech-common/http'
const NAME = 'qualification'
const PROXY_BASE = '/supplier'

//========【采方==供应商资质管理】==========
const APIS = {
  // 审查单资质项==列表
  managerQueryPageList: () => {
    API.post(`${PROXY_BASE}/tenant/buyer/qualification/manager/queryPageList`)
  },
  // 审查单资质项详情==列表
  // managerQueryItemList: (data = {}) => {
  //   return API.get(`${PROXY_BASE}/tenant/buyer/qualification/manager/queryItemList`, data, { headers: {'Content-Type': 'application/x-www-form-urlencoded'}});
  // },

  // new 采方-供应商资质管理-查询审查单详细信息
  getQualificationDesc: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/qualification/manager/getQualificationDesc`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 审查单资质项==新建
  managerAdd: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/manager/add`, data)
  },
  // 资质库==新建(供应商数据)
  getOrgPartnerRelationsByStatus: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/partner/relation/getOrgPartnerRelationsByStatus`,
      data
    )
  },
  // 审查单资质项==批量发布
  managerBatchPublish: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/manager/batchPublish`, data)
  },
  // 审查单资质项==批量删除
  managerDel: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/manager/del`, data)
  },
  // 审查单资质项==审核
  managerAudit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/item/manager/audit`, data)
  },
  // 审查单资质项==综合提交审批
  managerSubmitComprehensiveApproval: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/qualification/manager/submitComprehensiveApproval`,
      data
    )
  },
  // 资质库==列表
  managerQualificationDatabasePage: () => {
    return API.get(
      `${PROXY_BASE}/tenant/buyer/qualification/item/manager/qualificationDatabasePage`
    )
  },
  // 资质库==详情列表(包含动态字段)
  qualificationDatabaseDesc: (data = {}) => {
    return API.get(
      `${PROXY_BASE}/tenant/buyer/qualification/item/manager/qualificationDatabaseDesc`,
      data,
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    )
  },

  checkQualifications: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/manager/checkQualifications`, data)
  },

  submitQualifications: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/auth/manager/submit`, data)
  },
  checkThreshold: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/threshold/checkThreshold`, data)
  },

  // 门槛校验-提交审批
  submitApprove: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/auth/threshold/redline/submit`, data)
  },
  // 门槛校验-查看OA审批
  viewOaApprove: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/apply/info/getOALink`, data)
  },

  // 资质审查-更新确认人信息
  updateConfirmInfo: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/item/manager/updateConfirmInfo`, data)
  },
  // 供应商资质管理-催办
  urgeQualifications: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/qualification/manager/remindRelevantConfirmUser`,
      data
    )
  },
  // 供应商资质管理-资质审查单-导出
  exportQualiExamineOrder: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/qualification/manager/exportHeaderList`, data, {
      responseType: 'blob'
    })
  },
  // 供应商资质管理-资质库-导出
  exportQualiDatabase: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/qualification/item/manager/exportHeaderList`,
      data,
      {
        responseType: 'blob'
      }
    )
  }
}

export default {
  NAME,
  APIS
}
