// 供应商调查表管理 接口
import { API } from '@mtech-common/http'
const NAME = 'AccessStage'
const PROXY_BASE = '/supplier'
const APIS = {
  // 调查表列表
  queryStage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/define/stage/query`, data)
  },
  // 调查表状态更改
  changeStatus: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/define/stage/changeStatus`, data)
  },
  // 删除调查表
  delStage: (data = []) => {
    return API.delete(`${PROXY_BASE}/tenant/buyer/define/stage/delete`, data)
  },
  // 新增调查表
  addStage: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/define/stage/add`, data)
  },
  // 编辑调查表
  editStage: (data = {}) => {
    return API.put(`${PROXY_BASE}/tenant/buyer/define/stage/update`, data)
  },
  // 调查表详情
  detailStage: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/form/query/${id}`)
  }
}

export default {
  NAME,
  APIS
}
