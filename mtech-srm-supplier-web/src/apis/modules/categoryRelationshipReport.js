// 品类关系报表

/*
  客户准入管理
*/

import { API } from '@mtech-common/http'
const NAME = 'categoryRelationshipReport'
const PROXY_BASE = '/supplier/tenant'

const APIS = {
  // 保存
  saveData: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/category/cycle/saveData`, data)
  },
  // 导入
  import: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/category/cycle/import`, data)
  },
  // 导出
  exportQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/category/cycle/exportQuery`, data, {
      responseType: 'blob'
    })
  },
  // 删除
  remove: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/category/cycle/remove`, data)
  },
  // 人员下拉列表
  getBuyerList: (data = {}) => {
    return API.get(`/masterDataManagement/tenant/employee/currentTenantEmployees`, data)
  }
}

export default {
  APIS,
  NAME
}
