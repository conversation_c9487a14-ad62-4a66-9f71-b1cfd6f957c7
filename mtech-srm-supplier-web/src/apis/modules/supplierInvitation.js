import { API } from '@mtech-common/http'
const NAME = 'supplierInvitation'
const PROXY_BASE = '/supplier'
const PROXY_MASTERDATA = '/masterDataManagement'
const PROXY_MessageDATA = '/message'
const PROXY_platform = '/platform'
const APIS = {
  /*
    供应商邀请管理
    http://10.14.243.159:9112/tenant/buyer/invite/list
  */

  // 供应商邀请 邀请列表
  inviteList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/list`, data)
  },

  // 供应商邀请 新增
  inviteAdd: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/addInvite`, data)
  },

  // 供应商邀请 关闭
  inviteClose: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/close`, data)
  },

  // 撤回
  withdrawInvite: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/withdrawInvite`, data)
  },

  // 供应商邀请 详情
  inviteDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/invite/detail/${data.id}`)
  },

  // 供应商邀请 发送邀请
  sendInvitation: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/sendInvite`, data)
  },

  // 供应商邀请 撤回邀请 ###废弃###
  withdrawInvitation: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/withdrawInvitation`, data)
  },

  // 供应商邀请 删除邀请
  deleteInvitation: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/deleteInvite`, data)
  },

  // 供应商邀请 编辑邀请信息
  updateInvitation: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/update`, data)
  },

  // 供应商邀请 根据公司查询已邀请记录
  listExtHistory: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/listExtHistory`, data)
  },

  // 供应商邀请 根据公司查询联系人信息等
  queryContactInfo: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/queryContactInfo`, data)
  },

  // 租户级-组织机构接口-根据账户id获取公司组织树
  findOrganizationCompanyTreeByAccount: (data = {}) => {
    return API.post(
      `${PROXY_MASTERDATA}/tenant/organization/findOrganizationCompanyTreeByAccount`,
      data
    )
  },
  findOrganizationCompanyTreeByAccount2: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
  },
  findOrganizationCompanyTreeByAccount3: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/organization/getStatedLimitTree`, data)
  },
  // 租户级-品类接口-获取生产组织树
  getProdcutTree: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/category/producttree-in-org`, data)
  },

  /*
    主数据
  */
  // 获取主品类树特定层级数据--认证场景定义
  getCategoryList: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/category/product-list`, data)
  },
  getCategoryListAll: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/category/paged-query`, data)
  },
  getCategoryLists: (data = {}) =>
    API.post(`${PROXY_MASTERDATA}/tenant/category/criteria-query`, data),
  // 邮件模板信息
  getMessageList: (data = {}) => {
    return API.post(`${PROXY_MessageDATA}/tenant/mail/template/getList`, data)
  },

  // 根据Id获取区域列表
  sendInviteExtList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/sendInviteExtList`, data)
  },

  // 根据字典类型编码 获取字典详情
  getDictCode: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/dict-item/dict-code`, data)
  },
  // 查询指定组织付款方式
  getPaymentMethodByOrgId: (data = {}) => {
    return API.get(`${PROXY_MASTERDATA}/tenant/paymethod-org-rel/find-by-orgid`, data)
  },

  // 获取公司信息
  TreeByAccount: (data = []) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/organization/getFuzzyCompanyTree`, data)
  },
  getChildrenCompanyOrganization: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/organization/getChildrenCompanyOrganization`, data)
  },
  // 暂用
  getChildrenCompanyOrganization2: (data = {}) => {
    return API.post(`${PROXY_MASTERDATA}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
  },
  // 查找可用(审批通过)的租户信息
  getCompanyList: (data = {}) => {
    return API.post(`${PROXY_platform}/common/tenant/list/all`, data)
  },

  // 新增引入单据
  addIntroduce: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/addIntroduce`, data)
  },

  // 引入
  queryIntroduce: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/invite/introduce`, data)
  }
}

export default {
  NAME,
  APIS
}
