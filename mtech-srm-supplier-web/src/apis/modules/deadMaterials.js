import { API } from '@mtech-common/http'
const NAME = 'deadMaterials'
const PROXY_BASE = '/analysis/tenant'
const APIS = {
  /*
    呆料索赔清单接口
  */

  // 删除呆料索赔清单头数据
  removeHeader: (id) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/removeHeader?id=${id}`)
  },
  // 添加呆料索赔清单头数据
  addHeader: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/addHeader`, data)
  },
  // 更新呆料索赔清单头数据
  updateHeader: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/updateHeader`, data)
  },
  // 添加呆料索赔清单明细
  addItem: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/addItem`, data)
  },
  // 更新呆料索赔清单明细
  updateItem: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/updateItem`, data)
  },
  // 删除呆料索赔清单明细
  removeItem: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/removeItem`, data)
  },
  // 获取导入呆料索赔清单明细导入模板
  getItemImportTemplate: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/getItemImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导入呆料索赔清单明细
  importItemExcel: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/importItemExcel`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 通过呆料索赔清单创建呆料索赔考核单
  createClaimVoucher: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/createClaimVoucher`, data)
  },
  // 删除呆料考核单头
  deleteClaimVoucher: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/deleteHeader?id=${data}`)
  },
  // 通过呆料考核单头ID获取OA审批链接
  getOALink: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/getOALink?id=${data}`)
  },
  // 通过呆料考核单头ID 实现确认扣款
  deduct: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/deduct?id=${data}`)
  },
  // 导出呆料考核单头
  exportClaimVoucher: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/exportHeader`, data, {
      responseType: 'blob'
    })
  },
  // 获取呆料索赔详情
  getDetail: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/getDetail?id=${data}`)
  },
  // 通过呆料考核单头ID驳回单据
  rejectHeader: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/rejectHeader
    ?id=${data}`)
  },
  // 呆料索赔单据通过头数据ID提交
  submitHeader: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/submit`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 呆料索赔单据详情刷新价格
  refreshItemPriceApi: (data) => {
    return API.get(`${PROXY_BASE}/idleMaterialClaimVoucher/refreshItemPrice/${data.id}`)
  },
  // 呆料索赔单据详情数据保存
  saveDetail: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/save`, data)
  },
  // 呆料索赔单据详情数据保存
  saveAndSubmit: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/saveAndSubmit`, data)
  },
  // 查询呆料索赔单据索赔指标行对应采购执行价和未清PO价
  queryPrice: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/queryPrice`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 获取品类导入模板
  exportQueryPrice: (data = {}) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/exportQueryPrice`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    })
  },
  // 打印呆料索赔单据协议书
  purchaserPrintClaim: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/agreementPrint`, data, {
      responseType: 'blob',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 导入品类模板
  importMaterialItem: (data = []) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/importMaterialItem`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 获取品类导入模板
  downloadImportMaterialItemTemplate: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/idleMaterialClaimVoucher/downloadImportMaterialItemTemplate`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 查看OA链接 -new
  deadMaterialsOALink: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/claimConfig/getOALink`, data)
  },
  // 获取呆料清单索赔详情
  getIdleMaterialDetail: (data) => {
    return API.post(`analysis/tenant/idleMaterialVoucher/detailQuery?id=${data}`)
  },

  // 呆料索赔单管理-手动创建
  createClaimVoucherManual: (data) => {
    return API.post(`${PROXY_BASE}/idleMaterialClaimVoucher/createClaimVoucherManual`, data)
  },
  // 呆料索赔清单查询 - 复制
  copyDeadMaterials: (id = {}) => {
    return API.post(`${PROXY_BASE}/idleMaterialVoucher/copyVoucher?id=${id}`, {})
  }
}

export default {
  NAME,
  APIS
}
