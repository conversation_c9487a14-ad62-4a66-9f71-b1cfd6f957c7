import { API } from '@mtech-common/http'
const NAME = 'supplierInfoChange'
// 供应商
const PROXY_BASE = '/supplier'

const APIS = {
  // 供应商类型
  getDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/formTask/detail/${data.id}`)
  },

  // 采方 --all表单定义查询 supplier/tenant/buyer/process/task/allFormDefine
  getAllFormDefine: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/form/allFormDefine`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--all表单定义查询
  queryAllFormData: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/form/queryAllFormData`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--调查表变更历史
  applyHistory: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/apply/formTask/applyHistory`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },

  // 采方--操作记录时间线
  operateRecord: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/common/operate/operateRecord`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  }
}
export default {
  NAME,
  APIS
}
