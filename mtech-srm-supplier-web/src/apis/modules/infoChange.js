import { API } from '@mtech-common/http'
const NAME = 'infoChange'
const PROXY_BASE = '/supplier'
const PROXY_MASTER = '/masterDataManagement'
import qs from 'qs'

const APIS = {
  // 新建申请单详情
  applyAdd: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/apply/add`, data)
  },
  getDetailById: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/info/change/apply/${data}`)
  },

  getDataFromArchive: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/from/archive`, data)
  },

  getDataFromArchiveSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/from/archive`, data)
  },

  searchBase: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/base`, data)
  },
  searchFinance: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/finance`, data)
  },
  searchCertificate: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/certificate`, data)
  },
  searchDevice: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/device`, data)
  },
  searchCycle: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/cycle`, data)
  },
  searchCycleById: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/cycle/sub`, data)
  },

  // 信息变更详情保存
  infoChangeSave: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/save`, data)
  },
  // 信息变更详情-重新下发SAP
  reissueSAP: (data = {}) => {
    return API.post(
      `${PROXY_BASE}/tenant/buyer/info/change/infoChangeReDistributingSAP`,
      qs.stringify(data),
      {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      }
    )
  },

  // 保存并提交
  infoChangeSaveAndCommit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/submitOne`, data)
  },

  // 提交
  infoChangeCommit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/submit`, data)
  },

  // 供方
  applyAddSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/apply/add`, data)
  },
  getDetailByIdSup: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/info/change/apply/${data}`)
  },

  accessDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/process/access/detail`, data)
  },
  searchBaseSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/base`, data)
  },
  searchFinanceSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/finance`, data)
  },
  searchCertificateSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/certificate`, data)
  },
  searchDeviceSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/device`, data)
  },
  searchCycleSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/cycle`, data)
  },
  searchCycleByIdSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/cycle/sub`, data)
  },

  // 信息变更详情保存
  infoChangeSaveSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/save`, data)
  },

  // 保存并提交
  infoChangeSaveAndCommitSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/submitOne`, data)
  },
  // 提交
  infoChangeCommitSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/submit`, data)
  },

  // 获取货币类型-主数据

  pagedQuery: () => {
    return API.get(`${PROXY_MASTER}/common/currency/queryActiveCurrency`)
  },

  // 付款条件
  criteriaQuery: (data = {}) => {
    return API.post(`${PROXY_MASTER}/common/payment-terms/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  // 国家地区
  countryQuery: () => {
    return API.post(`${PROXY_MASTER}/common/country/queryAll`)
  },

  // 根据字典类型编码获取字典详情
  queryDict: (data = {}) => {
    return API.post(`${PROXY_MASTER}/common/dict-item/item-tree`, data)
  },

  // 批量删除
  infoChangeDel: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/info/change/delete`, data)
  },

  // 批量删除 供方
  infoChangeDelSup: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/info/change/delete`, data)
  },
  // 驳回
  reject: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/info/change/reject`, data)
  },

  // 采方-获取供应商新合格的品类信息
  getSupplierQualifiedCategoryInfoApi: (data = {}) => {
    return API.post(
      `/supplier/tenant/buyer/partner/relation/getSupplierQualifiedCategoryInfo`,
      data
    )
  },
  // 供方-获取供应商新合格的品类信息
  getSupplierQualifiedCategoryInfoSupApi: (data = {}) => {
    return API.post(
      `/supplier/tenant/supplier/partner/category/getSupplierQualifiedCategoryInfo`,
      data
    )
  }
}
export default {
  NAME,
  APIS
}
