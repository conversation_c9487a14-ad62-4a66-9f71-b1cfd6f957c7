/*
  客户邀请管理
  http://10.14.243.159:9112/tenant/supplier/invite/list
*/

import { API } from '@mtech-common/http'
const NAME = 'customerInvitation'
const PROXY_BASE = '/supplier'

const APIS = {
  // 客户邀约 邀约列表
  getInviteList: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/list`, data)
  },
  // 客户邀约 邀约详情
  getInviteInfo: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/supplier/invite/detail/${data.id}`)
  },
  // 客户邀约 邀约处理
  handleInviteStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/supplier/invite/confirmInvite`, data)
  }
}

export default {
  APIS,
  NAME
}
