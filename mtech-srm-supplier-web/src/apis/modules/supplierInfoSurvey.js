import { API } from '@mtech-common/http'
import { checkIsPur } from '@/views/Buyer/infoSurveyManage/config/utils'

const NAME = 'supplierInfoSurvey'

const APIS = {
  // 获取供应商列表 /supplier/tenant/buyer/process/manager/query
  queryManagerSupplier(data) {
    return API.post(`/supplier/tenant/buyer/process/manager/query`, data)
  },

  // 修改供应商信息调查实例状态
  updateSurveyStatus(data) {
    const url = checkIsPur()
      ? '/supplier/tenant/buyer/survey/task/updateStatus'
      : '/supplier/tenant/supplier/survey/task/submit'
    return API.post(url, data)
  },

  // 查询供应商信息调查实例详情
  detailSurveySupplier(data) {
    // supplier/tenant/supplier/survey/task/queryDetail
    const url = checkIsPur()
      ? '/supplier/tenant/buyer/survey/task/queryDetail'
      : '/supplier/tenant/supplier/survey/task/detail'
    return API.get(url + '/' + data)
  },

  // 获取模板调查列表
  queryTemplateList(data) {
    return API.post(`/supplier/tenant/buyer/template/task/query`, data)
  },

  // 添加供应商信息调查实例
  addSurverTask(data) {
    return API.post(`/supplier/tenant/buyer/survey/task/add`, data)
  },

  // 添加并发布供应商信息调查实例
  submitSurverTask(data) {
    return API.post(`/supplier/tenant/buyer/survey/task/addIndustry`, data)
  },

  // 供方 - 保存供应商表单信息
  saveForm(data) {
    return API.post(`/supplier/tenant/supplier/survey/task/saveForm`, data)
  },

  // 供方 - 保存并提交供应商表单信息
  saveAndPublish(data) {
    return API.post(`/supplier/tenant/supplier/survey/task/saveAndSubmit`, data)
  }
}
export default {
  NAME,
  APIS
}
