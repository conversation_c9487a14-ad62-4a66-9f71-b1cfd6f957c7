// 有害物质管理 接口
import { API } from '@mtech-common/http'
const BASE_TENANT = '/supplier'
const NAME = 'hazardousSubstances'
const APIS = {
  /************************ 有害物质环保要求配置 - START ****************************/
  // 查询
  queryHazardousSubstancesList: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/harmful/category/config/query`, data)
  },
  // 新增
  addHazardousSubstancesInfo: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/harmful/category/config/save`, data)
  },
  // 失效
  expireHazardousSubstancesInfo: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/harmful/category/config/expire`, data)
  },
  // 生效
  effectiveHazardousSubstancesInfo: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/harmful/category/config/effective`, data)
  },
  // 删除
  deleteHazardousSubstancesInfo: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/harmful/category/config/delete`, data)
  },
  // 导出
  exportHazardousSubstancesList: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/harmful/category/config/excelExport`, data, {
      responseType: 'blob'
    })
  },
  // 导入模板下载
  importTemplateDown: (data = {}) => {
    return API.get(`${BASE_TENANT}/tenant/harmful/category/config/excelImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导入
  importHazardousSubstancesInfo: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/harmful/category/config/importData`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  /******************************* END ***********************************/
  /***************************** 供应商品类与负责人关系 *********************************/
  // 查询
  queryHazardousSubstancesSupplierCategoryList: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/supplier/category/director/query`, data)
  },
  // 新增
  addHazardousSubstancesSupplierCategory: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/supplier/category/director/save`, data)
  },
  // 失效
  expireHazardousSubstancesSupplierCategory: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/supplier/category/director/expire`, data)
  },
  // 生效
  effectiveHazardousSubstancesSupplierCategory: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/supplier/category/director/effective`, data)
  },
  // 删除
  deleteHazardousSubstancesSupplierCategory: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/supplier/category/director/delete`, data)
  },
  // 导出
  exportHazardousSubstancesSupplierCategoryList: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/supplier/category/director/excelExport`, data, {
      responseType: 'blob'
    })
  },
  // 导入模板下载
  importTemplateDownSupplierCategory: (data = {}) => {
    return API.get(`${BASE_TENANT}/tenant/supplier/category/director/excelImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导入
  importHazardousSubstancesSupplierCategory: (data = {}) => {
    return API.post(`${BASE_TENANT}/tenant/supplier/category/director/importData`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  /************************************* END ******************************************/

  /*********************************** 有害物质需求管理 - 采方 ***************************************/
  // 采方驳回供方 - 有害物质管理 - 采方
  hazardousSubstancesManageReject: (data = {}) => {
    return API.post(`/supplier/user/harmful/category/head/reject`, data)
  },
  // 确认并提交审批
  hazardousSubstancesConfirmApprovals: (data = {}) => {
    return API.post(`/supplier/user/harmful/category/head/submitOAs`, data)
  },
  // 详情 - 提交并提交审批
  hazardousSubstancesConfirmApproval: (data = {}) => {
    return API.post(`/supplier/user/harmful/category/head/submitOA`, data)
  },
  // 导出 - 单据导出(汇总导出)
  hazardousSubstancesSummaryExport(data = {}) {
    return API.post(`/supplier/user/harmful/category/head/queryExport`, data, {
      responseType: 'blob'
    })
  },
  // 导出 - 单据导出(汇总明细导出)
  hazardousSubstancesSummaryDetailExport(data = {}) {
    return API.post(`/supplier/user/harmful/category/head/exportDetail`, data, {
      responseType: 'blob'
    })
  },
  // 导出 - (已归档/超期)
  hazardousSubstancesOverdueExport(data = {}) {
    return API.post(`/supplier/user/harmful/category/head/getLastByExport`, data, {
      responseType: 'blob'
    })
  },
  // 导出 - RoHs
  hazardousSubstancesRoHsExport(data = {}) {
    return API.post(`/supplier/user/harmful/category/head/getRoHSByExport`, data, {
      responseType: 'blob'
    })
  },
  // 获取单据详情
  getHazardousSubstancesDocumentDetail(data = {}) {
    return API.post('/supplier/user/harmful/category/head/queryHeadBYId', data)
  },

  // 根据id查询附件信息
  getHazardousSubstancesDetailId(data = {}) {
    return API.post('/supplier/user/harmful/category/head/queryFileBYId', data)
  },

  // 根据id删除头附件信息
  deleteHazardousSubstancesHeaderInfo(data = {}) {
    return API.post('/supplier/user/harmful/category/head/deleteHead', data)
  },

  // 根据id删除行附件信息
  deleteHazardousSubstancesListInfo(data = {}) {
    return API.post('/supplier/user/harmful/category/head/deleteFile', data)
  },

  // 供应商品类与负责人关系-分页列表
  pageDirectorCategoryApi(data = {}) {
    return API.post('/supplier/tenant/supplier/category/director/query', data)
  },

  // 有害物质管理明细-分页列表
  queryLineHeadApi(data = {}) {
    return API.post('/supplier/user/harmful/category/head/queryLine', data)
  },

  // 采供-删除有害物质头信息
  deleteHeadApi(data = {}) {
    return API.post('/supplier/user/harmful/category/head/deleteHead', data)
  },
  // 采供-删除有害物质明细
  deleteLineApi(data = {}) {
    return API.post('/supplier/user/harmful/category/head/deleteLine', data)
  },
  // 采方-发布
  publishApi(data = {}) {
    return API.post('/supplier/user/harmful/category/head/publish', data)
  },
  // 根据品类编码获取测试类型
  queryTestTypeListApi(data = {}) {
    return API.get('/supplier/tenant/harmful/category/config/queryTestTypeList', data)
  },
  // 采方-上传承诺书模板
  uploadCommitmentTemplateApi(data = {}) {
    return API.post(`/supplier/user/harmful/category/head/uploadCommitmentTemplate`, data)
  },
  // 采方-下载承诺书模板
  queryCommitmentTemplateApi(data = {}) {
    return API.get(`/supplier/user/harmful/category/head/queryCommitmentTemplate`, data, {
      responseType: 'blob'
    })
  },
  /******************************************** end ************************************************/

  /***************************************** 供方-有害物质管理 ***********************************************/
  // 提交报告 - 创建单据
  hazardousSubstancesConfirm: (data = {}) => {
    return API.post(`/supplier/tenant/harmful/category/record/create`, data)
  },
  // 导出 - 提交历史
  hazardousSubstancesHistoryExport(data = {}) {
    return API.post(`/supplier/user/harmful/category/head/getRoHSByExport`, data, {
      responseType: 'blob'
    })
  },
  // 导出 - (已归档/超期)
  hazardousSubstancesArchivedExport(data = {}) {
    return API.post(`/supplier/user/harmful/category/head/getLastByExport`, data, {
      responseType: 'blob'
    })
  },
  // 保存 - 单据详情
  saveHazardousSubstancesInfo(data = {}) {
    return API.post('/supplier/user/harmful/category/head/save', data)
  },
  // 供方 - 提交 - 单据详情
  submitHazardousSubstancesInfo(data = {}) {
    return API.post('/supplier/user/harmful/category/head/submit', data)
  },
  // 供方 - 提交历史 - 删除
  deleteHazardousSubstancesHistoryInfo(data = {}) {
    return API.post('/supplier/user/harmful/category/head/deleteHead', data)
  },
  /*********************************************************************************************************/
  // 有害物质多维度统计报表 - 分页查询
  multiStatistReportPageApi: (data = {}) => {
    return API.post(`/statistics/tenant/harmful/multiStatistReportPage`, data)
  },
  // 有害物质多维度统计报表 - 导出
  multiStatistReportExportApi(data = {}) {
    return API.post(`/statistics/tenant/harmful/multiStatistReportExport`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
