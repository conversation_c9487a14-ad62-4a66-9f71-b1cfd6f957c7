// 主数据
import { API } from '@mtech-common/http'

const NAME = 'masterData'
const PROXY_BASE = '/masterDataManagement/tenant'
const PROXY_SOURCING = '/sourcing/tenant'
const DEFAULTPARAM = {
  condition: '',
  page: {
    current: 1,
    size: 1000
  },
  pageFlag: false
}
const APIS = {
  // 获取用户信息
  getUserInfo: (data = {}) => API.get(`/iam/common/account/userinfo`, data),

  // 租户级-雇员接口-当前租户下员工列表
  getCurrentTenantEmployees: (data = {}) =>
    API.get(`${PROXY_BASE}/employee/currentTenantEmployees`, data),

  // 租户级-组织机构接口-根据员工id获取公司级组织列表（包含公司-事业群-板块-集团）
  getOrgCompanysByEmpId: (data = {}) =>
    API.get(`${PROXY_BASE}/organization/company-level-orgs`, data),

  getOrgCompanysByEmpId2: (data = {}) => {
    return API.post(`${PROXY_BASE}/organization/findSpecifiedChildrenLevelOrgs`, data)
  },

  // 租户级-组织机构接口-根据员工id获取部门级组织列表
  getDepartmentsByEmpId: (data = {}) =>
    API.get(`${PROXY_BASE}/organization/department-level-orgs`, data),

  // 获取主品类树特定层级数据--认证场景定义 --old
  getCategoryList: (data = {}) => {
    return API.post(`${PROXY_BASE}/category/product-list`, data)
  },
  // 获取主品类树特定层级数据--认证场景定义 --new
  findBySupplierCode: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/supply/source/findBySupplierCode`, data)
  },

  // 推荐供应商（输入带出内部供应商，也可以不选内部，直接输入）【已有】租户级-供应商信息接口-条件查询
  getSupplier: (data = {}) => API.post(`${PROXY_BASE}/supplier/criteria-query`, data),

  // 获取指定组织下员工
  getOrganizationEmployees: (data = { orgId: '' }) =>
    API.post(`${PROXY_BASE}/organization/getOrganizationEmployees`, data),

  // 根据字典类型编码获取字典详情
  queryDict: (data = {}) => API.post(`/masterDataManagement/common/dict-item/item-tree`, data),
  findSpecifiedChildrenLevelOrgs: (data = {}) =>
    API.post(`${PROXY_BASE}/organization/findSpecifiedChildrenLevelOrgs`, data),

  // 物料模糊查询
  getItemFuzzyQueryUrl: `/masterDataManagement/tenant/item/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,

  // 采购组织接口
  getBusinessOrganizationUrl: `/masterDataManagement/tenant/business-organization/getByOrgIdAndBgOrgTypeCode?BU_CODE=${localStorage.getItem('currentBu')}`,

  // 采购组织接口
  getBusinessOrganizationListUrl: `/masterDataManagement/tenant/business-organization/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,

  /*
    租户级-货币接口
  */
  /**
   * @description: 查询所有 币种
   * @param {*}
   * @return {currencyCode、currencyName、id}
   */
  queryAllCurrency: (data = {}) => {
    return API.post(`${PROXY_BASE}/currency/queryAll`, data)
  },
  /*
    租户级-税目税率接口
  */
  /**
   * @description: 查询所有
   * @param {*}
   * @return {taxItemCode、taxItemName、id}
   */
  queryAllTaxItem: (data = DEFAULTPARAM) => {
    return API.post(`${PROXY_BASE}/tax-item/queryAll?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },
  // 租户级-成本中心接口-条件查询
  postCostCenterCriteriaQuery: (data = {}) =>
    API.post(`${PROXY_BASE}/cost-center/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data),

  // 租户级-成本中心接口-条件查询
  getCostCenterCriteriaQueryList: (data) => {
    return API.post(`/masterDataManagement/tenant/cost-center/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },
  // 成本中心
  getCostCenterUrl: `/masterDataManagement/tenant/cost-center/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
  /*
    租户级-地点接口
  */
  /**
   * @description:分页查询
   * @param {*}
   * @return {siteCode、siteName、id}
   */
  getSiteList: (data = DEFAULTPARAM) => API.post(`${PROXY_BASE}/site/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`, data),
  // 工厂
  getSiteListUrl: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
  // 地点模糊查询
  getSiteFuzzyUrl: `/masterDataManagement/tenant/site/auth-fuzzy?BU_CODE=${localStorage.getItem('currentBu')}`,

  // 方案组
  getSchemaGroupUrl: `/masterDataManagement/tenant/schema-group/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
  /*
    租户级-单位接口
  */
  /**
   * @description: 分页查询
   * @param {*}
   * @return {? ? ?}
   */
  pagedQueryUnit: (data = DEFAULTPARAM) => {
    return API.post(`${PROXY_BASE}/unit/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },
  /*
    租户级-字典相关接口
  */
  /**
   * @description: 根据字典类型编码获取字典详情
   * 推荐合同类型:contractType  业务类型:businessType 项目策略:policyType
   * @param {*}
   * @return {itemCode、itemName、id}
   */
  dictionaryGetList: (data = {}) => API.post(`${PROXY_BASE}/dict-item/dict-code`, data),
  /*
   租户级-组织机构接口
  */
  getStatedLimitTree: (data = {}) => {
    //获取指定范围树结构数据
    //公司：ORG02，部门：ORG03，岗位：ORG04，员工：ORG05
    return API.post(`${PROXY_BASE}/organization/getStatedLimitTree`, data)
  },

  //采购单位
  basicDetail: (data = DEFAULTPARAM) =>
    API.post(`${PROXY_BASE}/item-purchasing/basic-detail`, data),

  // 获取需要的字典存入sessionStorage
  getNumDictListAllByCode: (data = {}) => {
    return API.post(`${PROXY_BASE}/dict-item/getByDictCodes`, data)
  },

  /*
    租户级-用户接口   模糊查询
  */
  getUserList: (data) => API.post(`${PROXY_BASE}/user/criteria-query`, data),

  getDetail: (data) => API.post(`${PROXY_BASE}/expert/apply/query/detail`, data),

  getfuzzy_sourcing: (data) => API.post(`${PROXY_SOURCING}/expert/apply/query/fuzzy`, data),
  getfuzzy: (data) => API.post(`${PROXY_BASE}/expert/apply/query/fuzzy`, data),

  // 雇员接口 分页查询
  getUserPageList: (data) => API.post(`${PROXY_BASE}/employee/paged-query`, data),

  // 获取公司下工厂
  getCompanySite: (data = {}) => API.get(`${PROXY_BASE}/site/getSiteInfo?BU_CODE=${localStorage.getItem('currentBu')}`, data),

  // 租户级-公司接口
  getCompanyList: (data = {}) => API.post(`${PROXY_BASE}/company/fuzzy-query`, data)
}

export default {
  NAME,
  APIS
}
