import { API } from '@mtech-common/http'
const NAME = 'supplierEffective'

const APIS = {
  getSceneList: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/scene/define/listQuery`, data)
  },
  getSceneList2: (data = {}) => {
    // /tenant/buyer/scene/define/findByOrgCategory
    return API.post(`/supplier/tenant/buyer/scene/define/findByOrgCategory`, data)
  },

  // 选择供应商列表
  getOrgPartnerRelations(data = {}) {
    return API.post(`/supplier/tenant/buyer/partner/relation/getOrgPartnerRelations`, data)
  },
  // 选择供应商列表
  getOrgPartnerRelationsByStatus(data = {}) {
    return API.post(`/supplier/tenant/buyer/partner/relation/getOrgPartnerRelationsByStatus`, data)
  },

  // 选择品类接口
  getCategoryPartnerRelations(data = {}) {
    return API.post(`/supplier/tenant/buyer/partner/relation/getCategoryPartnerRelations`, data)
  },

  // 根据与供应商关系以及状态获取品类关系信息
  getCategoryPartnerRelationsByStatus(data = {}) {
    return API.post(
      `/supplier/tenant/buyer/partner/relation/getCategoryPartnerRelationsByStatus`,
      data
    )
  },

  // 新增生效申请
  addEffective: (data = {}) => {
    return API.post(`/supplier/tenant/buyer/apply/effective/add`, data)
  },
  // 更新生效申请 supplier/tenant/buyer/apply/effective/update
  updateEffective(data) {
    return API.post(`/supplier/tenant/buyer/apply/effective/update`, data)
  },

  // 判断认证项目
  findAuthProjectCode(data = {}) {
    return API.post(`/supplier/tenant/buyer/apply/effective/findAuthProjectCode`, data)
  },

  // 获取接口详情 /api/supplier/tenant/buyer/apply/effective/detail/{id}
  getDetail(id) {
    return API.get(`/supplier/tenant/buyer/apply/effective/detail/` + id)
  },

  // 获取通讯统驭科目
  getTxSubjectApi(data = {}) {
    return API.post(`/masterDataManagement/tenant/controlling/account/sj/listQuery`, data)
  },

  // 根据字典类型代码获取字典一级层级
  getDictItemByCode(dictCode) {
    return API.get(`/masterDataManagement/tenant/dict-item/getByDictCode/` + dictCode)
  },

  // 付款条件接口
  queryPaymentTerms(data) {
    return API.post(`/masterDataManagement/tenant/payment-terms/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  // 采购组织接口
  queryBusinessOrg(data) {
    return API.get(
      `/masterDataManagement/tenant/business-organization/getByOrgIdAndBgOrgTypeCode?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    )
  },

  // 方案组接口
  querySchemaGroup(data) {
    return API.post(`/masterDataManagement/tenant/schema-group/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  // 贸易伙伴接口 /masterDataManagement/tenant/trading-partner/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}
  queryTradePartner(data) {
    return API.post(`/masterDataManagement/tenant/trading-partner/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  // 货币种类列表 /masterDataManagement/tenant/currency/criteria-query
  queryCurrency(data) {
    return API.post(`/masterDataManagement/tenant/currency/criteria-query`, data)
  },

  // 税率
  queryTaxType(data) {
    return API.post(`/masterDataManagement/tenant/tax-item/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  // 删除
  delEffective(data) {
    return API.post(`/supplier/tenant/buyer/apply/effective/del`, data)
  },

  // 下发 /tenant/buyer/apply/effective/issued
  issuedEffective(data) {
    return API.post(`/supplier/tenant/buyer/apply/effective/issued`, data)
  },

  // 获取用户权限公司
  queryCompany(data = {}) {
    return API.post('/masterDataManagement/tenant/organization/specified-level-paged-query', data)
  },

  // 获取用户权限工厂
  queryFactory(data = {}) {
    return API.post(`/masterDataManagement/tenant/site/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  },

  // 获取物料列表数据
  getItemData(data = {}) {
    return API.post(`/supplier/tenant/buyer/partner/selectItemByCateAndCompany?BU_CODE=${localStorage.getItem('currentBu')}`, data)
  }
}
export default {
  NAME,
  APIS
}
