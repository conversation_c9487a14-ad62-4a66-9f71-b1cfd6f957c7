import { API } from '@mtech-common/http'
const NAME = 'supplierRiskMonitoring'
const PROXY_BASE = '/supplier'
const APIS = {
  // 外部识别风险-分页查询
  pageExternalRiskMonitoring: (data) => {
    return API.post(`${PROXY_BASE}/tenant/external/risk/monitoring/prompts/queryPage`, data)
  },
  // 外部识别风险-导出
  exportExternalRiskMonitoring: (data) => {
    return API.post(`${PROXY_BASE}/tenant/external/risk/monitoring/prompts/export`, data, {
      responseType: 'blob'
    })
  },
  // 外部识别风险-根据id查询跟进记录
  findExternalRiskMonitoringFollowRecord: (data) => {
    return API.post(`${PROXY_BASE}/tenant/external/risk/follow/record/queryByFllowId`, data)
  },
  // 外部识别风险-保存跟进记录
  saveExternalRiskMonitoringFollowRecord: (data) => {
    return API.post(`${PROXY_BASE}/tenant/external/risk/follow/record/add`, data)
  },
  // TCL内部违规失信监控-分页查询
  pageInternalRiskMonitoring: (data) => {
    return API.post(`${PROXY_BASE}/tenant/violation_dishonest/pageQuery`, data)
  },
  // TCL内部违规失信监控-导出
  exportInternalRiskMonitoring: (data) => {
    return API.post(`${PROXY_BASE}/tenant/violation_dishonest/excelExport`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
