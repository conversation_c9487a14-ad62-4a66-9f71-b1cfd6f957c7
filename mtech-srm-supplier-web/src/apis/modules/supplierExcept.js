import { API } from '@mtech-common/http'
const NAME = 'supplierExcept'
const PROXY_BASE = '/analysis'
const APIS = {
  // 例外供应商 - 新增弹窗 - 获取组织下拉数据
  treeOrgForSpecial: (data = {}) => {
    return API.post(`/masterDataManagement/analysisVirtualOrg/query/treeOrgForSpecial`, data)
  },
  // 例外供应商 - 新增弹窗 - 获取供应商下拉数据
  getFuzzy: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/supplier/achievements/fuzzy-query`, data)
  },
  // 例外供应商 - 新增弹窗 - 根据供应商获取品类下拉数据 与供应商二级联动
  getCategory: (data = {}) => {
    return API.post(`/supplier/tenant/supplier/partner/category/filter-query`, data)
  },
  // 例外供应商 - 新增弹窗 - 根据供应商获取品类下拉数据 无需供应商二级联动
  getCategoryList: (data = {}) => {
    return API.post(`/masterDataManagement/tenant/category/fuzzy-rel-query`, data)
  },
  // 例外供应商 - 导入模板下载
  downloadTemplate: (data = {}) => {
    return API.post(`/analysis/tenant/buyer/assess/setting/exclude/downloadTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导入品类模板
  fileUploadExceptSupplier: (data = []) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/setting/exclude/importData`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 获取品类导入模板
  fileDownloadExceptSupplier: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/assess/setting/exclude/downloadTemplate`, data, {
      responseType: 'blob'
    })
  }
}
export default {
  NAME,
  APIS
}
