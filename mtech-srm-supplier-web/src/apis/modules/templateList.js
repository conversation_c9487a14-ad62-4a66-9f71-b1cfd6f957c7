// 阶段配置 接口
import { API } from '@mtech-common/http'
const NAME = 'templateList'
// const PROXY_BASE = "/supplier";
const PROXY_AN = '/analysis'
// 主数据的
// const PROXY_Master = "/masterDataManagement";
const APIS = {
  // 新增
  templateAdd: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/add`, data)
  },
  //删除
  templateDel: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/del`, data)
  },
  //编辑
  templateUpdate: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/update`, data)
  },
  //启用停用
  templateUpdateStatus: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/updateStatus`, data)
  },
  //   详情
  templateDetail: (data = {}) => {
    return API.get(`${PROXY_AN}/tenant/buyer/assess/template/detail`, data)
  },
  //下拉维度
  dimensionListQuery: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/dimension/listQuery`, data)
  },
  //新增模板详情
  templateAddDetail: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/addDetail`, data)
  },
  //删除模板详情
  templatedelDetail: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/delDetail?id=${data.id}`)
  },
  //指标
  queryListByDimensionIdAndOrgId: (data) => {
    return API.post(
      `${PROXY_AN}/tenant/buyer/assess/index/queryListByDimensionIdAndOrgId?${data}`,
      { page: { current: 1, size: 999 } }
    )
  },
  //品类全量树
  queryAllTree: (data = {}) => {
    return API.get(`${PROXY_AN}/tenant/buyer/category/queryAllTree`, data)
  },
  //新增模板适用范围
  templateAddRange: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/addRange`, data)
  },
  //新增模板详情和适用范围
  templateAddItemAndRange: (data = {}) => {
    return API.post(`${PROXY_AN}/tenant/buyer/assess/template/addItemAndRange`, data)
  }
  // tenant/buyer/assess/template/addItemAndRange
}

export default {
  NAME,
  APIS
}
