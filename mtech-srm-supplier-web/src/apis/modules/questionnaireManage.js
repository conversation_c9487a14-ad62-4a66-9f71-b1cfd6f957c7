// 供应商调查表管理 接口
import { API } from '@mtech-common/http'
const NAME = 'QuestionnaireManage'
const PROXY_BASE = '/supplier'
const APIS = {
  // 调查表-待填写列表
  queryPendingTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/task/pending/query`, data)
  },
  // 调查表-总览列表
  queryHistoryTask: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/task/history/query`, data)
  },
  // 调查表-详情页
  taskDetail: (id) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/form/task/${id}`)
  },
  // 调查表-保存
  taskSave: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/task/save`, data)
  },
  // 调查表-提交
  taskSubmit: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/task/submit`, data)
  },

  // 调查表审批-待审批列表
  queryPendingApprove: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/task/approve/query`, data)
  },
  // 调查表审批-审批总览列表
  queryHistoryApprove: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/form/task/approveHistory/query`, data)
  },
  // 调查表审批
  approval: (data = {}) => {
    return API.post(`/flow/user/task/approval`, data)
  }
}

export default {
  NAME,
  APIS
}
