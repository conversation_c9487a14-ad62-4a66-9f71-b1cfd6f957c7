// 供应商品质状态分析报表 接口
import { API } from '@mtech-common/http'
const NAME = 'statusAnalysisReport'
const PROXY_BASE = '/supplier/tenant'
const APIS = {
  // 供应商品质状态分析报表--导出
  exportQualityStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/process/perspective/exportQualityStatus`, data, {
      responseType: 'blob'
    })
  },
  // 供应商品质状态分析报表
  listQualityStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/process/perspective/listQualityStatus`, data)
  }
}
export default {
  NAME,
  APIS
}
