import { API } from '@mtech-common/http'
const NAME = 'assessFeedback'
const PROXY_BASE = '/analysis/tenant'
const PROXY_File = '/file'
const APIS = {
  /*
    考核单接口
  */
  // 保存并提交考核单反馈数据
  saveAndSubmit: (data) => {
    return API.post(`${PROXY_BASE}/supplierClaim/saveAndSubmitClaimAppeal`, data)
  },
  // 保存考核单反馈数据
  saveClaimAppeal: (data) => {
    return API.post(`${PROXY_BASE}/supplierClaim/saveClaimAppeal`, data)
  },
  // 考核单详情（待反馈）
  detailClaim: (data) => {
    return API.get(`${PROXY_BASE}/supplierClaim/detailClaim`, data)
  },
  // 已反馈考核单详情
  detailPublishedClaim: (data = {}) => {
    return API.get(`${PROXY_BASE}/supplierClaim/detailPublishedClaim`, data)
  },
  // 打印考核单--供
  printClaim: (data) => {
    return API.get(`${PROXY_BASE}/supplierClaim/printClaim`, data, {
      responseType: 'blob'
    })
  },
  // 导出考核单--供
  exportClaim: (data) => {
    return API.post(`${PROXY_BASE}/supplierClaim/pageClaimExport`, data, {
      responseType: 'blob'
    })
  },
  // 打印考核单--采
  purchaserPrintClaim: (data) => {
    return API.get(`${PROXY_BASE}/claim/printClaim`, data, {
      responseType: 'blob'
    })
  },
  // 文件上传
  fileUpload: (data = []) => {
    return API.post(`${PROXY_File}/user/file/uploadPublic`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  // 提交考核单反馈数据
  submitClaimAppeal: (data) => {
    return API.patch(`${PROXY_BASE}/supplierClaim/submitClaimAppeal`, data)
  },
  // 考核单发票列表
  listClaimInvoice: (data) => {
    return API.post(`${PROXY_BASE}/supplierClaim/listClaimInvoice`, data)
  }
}

export default {
  NAME,
  APIS
}
