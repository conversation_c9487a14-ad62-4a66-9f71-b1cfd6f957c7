// 供应商调查表管理 接口
import { API } from '@mtech-common/http'
const NAME = 'AccessView'
const PROXY_BASE = '/supplier'
const APIS = {
  // 准入模板适用视图列表查询
  queryAccessRelation: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/template/access/relation/query`, data)
  },
  // 视图明细查询
  getAccessStageDetail: (id = '') => {
    return API.get(`${PROXY_BASE}/tenant/buyer/template/access/stage/query?accessTemplateId=${id}`)
  }
}

export default {
  NAME,
  APIS
}
