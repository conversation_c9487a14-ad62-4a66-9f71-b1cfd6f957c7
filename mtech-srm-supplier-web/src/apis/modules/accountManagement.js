import { API } from '@mtech-common/http'
const NAME = 'accountManagement'
// 主数据的
const APIS = {
  // 修改密码
  resetByAdmin: (data = {}) => {
    return API.post(`/iam/common/account/password/resetByAdmin`, data)
  },
  // 修改手机号邮箱
  updateInfo: (data = {}) => {
    return API.post(`/masterDataManagement/common/user/updateInfo`, data)
  },
  // 供应商账号管理 - 查看脱敏数据
  checkDeliveryConfigInfo: (data = {}) => {
    return API.get(`/srm-purchase-execute/common/dataDesensitize?desensitize=${data.key}`)
  }
}

export default {
  NAME,
  APIS
}
