// 品类配置表

import { API } from '@mtech-common/http'
const NAME = 'categoryConfigurationReport'
const PROXY_BASE = '/supplier/tenant'

const APIS = {
  // 保存
  getPageInfo: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryConfig/pageQuery`, data)
  },
  // 删除
  deletePagesInfo: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryConfig/delete`, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 导入
  import: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryConfig/importExcel`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 下载标准模板
  exportTmp: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryConfig/downloadExcelTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 导出
  exportQuery: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryConfig/exportExcel`, data, {
      responseType: 'blob'
    })
  },
  // 保存
  saveData: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryConfig/save`, data)
  },
  // 操作记录
  pageQueryRecordApi: (data = {}) => {
    return API.post(`${PROXY_BASE}/buyer/categoryConfigRecord/pageQuery`, data)
  }
}

export default {
  APIS,
  NAME
}
