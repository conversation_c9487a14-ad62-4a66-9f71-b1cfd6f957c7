/*
  客户档案管理--联系人信息管理
*/

import { API } from '@mtech-common/http'
const NAME = 'customerProfileContact'
const PROXY_SUPPLY = '/supplier'

const APIS = {
  /*
    租户级-供方-客户联系方式
  */
  // 联系人列表
  getSupplyContactList: `${PROXY_SUPPLY}/tenant/supplier/partner/contact/supplierList`,
  // 新增联系人
  addSupplyContact: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/supplier/partner/contact/add`, data)
  },
  // 删除联系人
  deleteSupplyContact: (params = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/partner/contact/delete`, params)
  },
  // 联系人详情
  supplyContactInfo: (params = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/partner/contact/detail`, params)
  },
  // 设置默认联系人
  setDefaultSupplyContact: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/supplier/partner/contact/setDefault`, data)
  },
  // 编辑联系人
  editSupplyContact: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/supplier/partner/contact/update`, data)
  },

  /*
    租户级-采方-供应商联系方式
  */
  // 联系人列表
  getPurchaseContactList: `${PROXY_SUPPLY}/tenant/supplier/partner/contact/buyerList`,
  // 新增联系人
  addPurchaseContact: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/buyer/partner/contact/add`, data)
  },
  // 删除联系人
  deletePurchaseContact: (params = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/buyer/partner/contact/delete`, params)
  },
  // 联系人详情
  purchaseContactInfo: (params = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/buyer/partner/contact/detail`, params)
  },
  // 设置默认联系人
  setDefaultPurchaseContact: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/buyer/partner/contact/setDefault`, data)
  },
  // 编辑联系人
  editPurchaseContact: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/buyer/partner/contact/update`, data)
  }
}

export default {
  APIS,
  NAME
}
