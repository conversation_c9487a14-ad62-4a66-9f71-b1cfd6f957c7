/*
  客户档案管理--信息变更
*/

import { API } from '@mtech-common/http'
const NAME = 'customerProfileChange'
const PROXY_SUPPLY = '/supplier'

const APIS = {
  /*
    租户级-供方-客户档案管理
  */
  // 客户档案变更历史
  getAllApplyHistory: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/process/access/allApplyHistory`, data)
  },

  // 客户详情
  getSupplierDetail: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/process/access/detail`, data)
  },
  /*
    租户级-供方-调查表变更申请
  */
  // 任务关联关系详情  ok
  getTaskRelation: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/apply/formTask/taskRelation`, data)
  },

  // 调查表变更历史
  getInfoApplyHistory: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/apply/formTask/applyHistory`, data)
  },
  // 新增变更申请  ok
  addApplyInfo: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/supplier/apply/formTask/add`, data)
  },
  // 删除变更申请  ok
  deleteApplyInfo: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/apply/formTask/delete`, data)
  },
  // 变更申请详情  ok
  applyInfoDetail: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/apply/formTask/detail/${data.id}`)
  },
  // 变更申请列表  ok
  getApplyInfoChangeList: `${PROXY_SUPPLY}/tenant/supplier/apply/formTask/list`,
  // 提交变更申请  ok
  submitApplyInfo: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/apply/formTask/submit`, data)
  },
  // 编辑变更申请  ok
  updateApplyInfo: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/supplier/apply/formTask/update`, data)
  },
  //客户档案所有已完成任务列表
  getAllCompletedTask: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/apply/formTask/allTask`, data)
  },

  /*
    租户级-供方-调查表变更申请编辑
  */
  // 任务表单定义查询(全部表单)
  getAllFormDefine: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/apply/form/allFormDefine`, data)
  },
  // 任务表单数据查询(全部表单)
  getAllFormData: (data = {}) => {
    return API.get(`${PROXY_SUPPLY}/tenant/supplier/apply/form/queryAllFormData`, data)
  },
  // 任务表单保存
  saveFormData: (data = {}) => {
    return API.post(`${PROXY_SUPPLY}/tenant/supplier/apply/form/saveTaskFormData`, data)
  }
}

export default {
  APIS,
  NAME
}
