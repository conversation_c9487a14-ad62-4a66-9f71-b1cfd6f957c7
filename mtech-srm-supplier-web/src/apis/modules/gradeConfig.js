// 供应商调查表管理 接口
import { API } from '@mtech-common/http'
const NAME = 'GradeConfig'
const PROXY_BASE = '/supplier'
const APIS = {
  // 分级列表查询
  queryGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/grade/define/list`, data)
  },

  // 新增分级定义
  addGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/add`, data)
  },
  // 删除分级定义
  deleteGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/delete`, data)
  },
  // 删除自动分级定义
  deleteGradea: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/automatic/grade/delete`, data)
  },
  // 分级定义启用、禁用
  changeStatus: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/enable`, data)
  },
  // 自动分级定义启用、禁用
  changeStatusa: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/automatic/grade/enable`, data)
  },
  // 编辑分级定义
  updateGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/update`, data)
  },

  // 自动分级获取分级
  grading: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/label/define/list`, data)
  },

  // 自动分级获取分级
  queryAddGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/automatic/grade/add`, data)
  },
  queryupdateGrade: (data = {}) => {
    return API.post(`${PROXY_BASE}/tenant/buyer/automatic/grade/update`, data)
  },
  // 分级详情
  queryGradeDetail: (data = {}) => {
    return API.get(`${PROXY_BASE}/tenant/buyer/automatic/grade/detail/${data.id}`)
  }
}

export default {
  NAME,
  APIS
}
