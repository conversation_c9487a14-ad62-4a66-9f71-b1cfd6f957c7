<template>
  <div class="permission-tab">
    <mt-tabs
      id="stage-config-tabs"
      css-class="tab-wrap"
      :height="40"
      :e-tab="true"
      ref="mtTabs"
      :selected-item="selectedItem"
      :data-source="renderSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import axios from 'axios'
export default {
  props: {
    dataSource: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      selectedItem: 0,
      renderSource: []
    }
  },
  methods: {
    handleSelectTab(selectItem) {
      let { selectedIndex } = selectItem
      console.log('handleSelectTab123', selectItem)
      this.$emit('handleSelectTab', selectedIndex)
    },
    // 获取权限列表
    getPermissionList() {
      console.log('handleSelectTab123')

      axios
        .get(
          `/api/iam/tenant/user-permission-query/user-element-permissions?appCode=${utils.getAppCode()}`,
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' } }
        )
        .then((result) => {
          let allPermissionList = []
          let renderSource = []
          let selectedItem = -1 // 默认已选得tab index

          !!result.data &&
            result.data.data.forEach((item) => {
              allPermissionList.push(item.permissionCode)
            })

          this.dataSource.forEach((item, index) => {
            if (Object.prototype.hasOwnProperty.call(item, 'permission')) {
              console.log('123')
              let disabled =
                !item.permission ||
                (allPermissionList.length > 0 && !allPermissionList.includes(item.permission))
              if (!disabled && selectedItem === -1) {
                selectedItem = index // 寻找默认tab
              }
              // 非禁用得推入数据 禁用的将删除
              !disabled &&
                renderSource.push({
                  ...item,
                  disabled
                })
            } else {
              console.log('45666666')
              if (selectedItem === -1) {
                selectedItem = index
              }
              renderSource.push({
                ...item,
                disabled: false
              })
            }
          })
          this.$emit('handleSelectTab', selectedItem)
          this.selectedItem = selectedItem
          this.renderSource = renderSource
          console.log('handleSelectTabhandleSelectTab', this.renderSource)
        })
    }
  },
  created() {
    this.getPermissionList()
  }
}
</script>

<style lang="scss">
.permission-tab {
  margin-top: 10px;

  .tab-wrap {
    height: 40px;
  }
}
</style>
