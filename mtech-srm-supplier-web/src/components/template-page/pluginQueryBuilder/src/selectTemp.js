import { DropDownList, MultiSelect } from '@syncfusion/ej2-dropdowns'
import { createElement, getComponent } from '@syncfusion/ej2-base'

const inOperators = ['in', 'notin']

export default (params) => {
  const template = {}
  template.create = () => {
    // const fields = params.fields || { text: 'label', value: 'value' }
    // const ds = params.ds || []
    return createElement('input', {
      attrs: { type: 'text', autocomplete: 'off' }
    })
  }
  template.write = (args) => {
    const $mainInstance = params.$mainInstance
    const fields = params.fields || { text: 'label', value: 'value' }
    const ds = params.ds || []
    if (inOperators.indexOf(args.operator) > -1) {
      const multiSelectObj = new MultiSelect({
        fields: fields,
        dataSource: ds,
        value: args.values,
        mode: 'CheckBox',
        placeholder: 'Select Transaction',
        change: (e) => {
          $mainInstance.$refs.queryBuilder.$refs.ejsRef.$el.ej2_instances[0].notifyChange(
            e.value,
            e.element
          )
        }
      })
      multiSelectObj.appendTo('#' + args.elements.id)
    } else {
      const dropDownObj = new DropDownList({
        fields: fields,
        dataSource: ds,
        value: args.values,
        change: (e) => {
          $mainInstance.$refs.queryBuilder.$refs.ejsRef.$el.ej2_instances[0].notifyChange(
            e.itemData[fields.value],
            e.element
          )
        }
      })
      dropDownObj.appendTo('#' + args.elements.id)
    }
  }
  template.destroy = (args) => {
    const multiselect = getComponent(document.getElementById(args.elementId), 'multiselect')
    if (multiselect) multiselect.destroy()
    const dropdownlist = getComponent(document.getElementById(args.elementId), 'dropdownlist')
    if (dropdownlist) dropdownlist.destroy()
  }
  return template
}
