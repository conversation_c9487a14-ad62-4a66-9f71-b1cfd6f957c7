<template>
  <div class="query-builder">
    <div class="query-btns">
      <mt-button @click="handleQuerySearch">
        <i class="mt-icons mt-icon-BT_Excelfilter"></i>
        {{ $t('过滤') }}
      </mt-button>
      <mt-button @click="handleQueryReset">
        <i class="mt-icons mt-icon-BT_Excelclear"></i> {{ $t('重置') }}
      </mt-button>
      <!-- <mt-button> <i class="mt-icons mt-icon-save-02"></i> 保存</mt-button>
        <mt-button>
          <i class="mt-icons mt-icon-FilterFields"></i> 模板</mt-button
        > -->
    </div>

    <mt-query-builder
      ref="queryBuilder"
      :show-buttons="showButtons"
      width="100%"
      :column-data="queryColumnData"
    >
    </mt-query-builder>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
// 下面三个列上的过滤用
import { DropDownList } from '@syncfusion/ej2-dropdowns'
import { createElement } from '@syncfusion/ej2-base'
import { DataManager } from '@syncfusion/ej2-data'
import getSelectTemp from './selectTemp'
import MtButton from '@mtech-ui/button'
import MtQueryBuilder from '@mtech-ui/query-builder'

export default {
  name: 'CustomQueryBuilder',
  props: {
    columnData: {
      type: Array,
      default: () => {
        return []
      }
    },
    ignoreFields: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  components: {
    MtButton,
    MtQueryBuilder
  },
  data() {
    return {
      queryColumnData: [],
      filterOptions: {
        type: 'Menu'
      },
      showButtons: {
        ruleDelete: true
      },
      // 条件验证
      validateRule: {
        isRequired: true
      }
    }
  },
  watch: {
    columnData: {
      handler(n) {
        if (n && Array.isArray(n) && n.length > 0) {
          const _columns = []
          const _columnsData = utils.cloneDeep(n)
          _columnsData.forEach((item) => {
            // checkbox不作为筛选列
            if (item.type !== 'checkbox') {
              if (item?.valueConverter && item?.valueConverter?.map) {
                this.serializeColumnFilter(item)
              }
              item.label = item.headerText
              item.type = item.queryType || 'string'
              item.values = item.queryValues || null
              item.operators = this.getOpt(item)
              item.template = item.queryTemplate ? this.getTemp(item.queryTemplate) : null
              let _ignoreField = false
              if (this.ignoreFields.indexOf(item.field) > -1) {
                // 在ignoreFields中，设置了忽略此字段
                _ignoreField = true
              }
              if (typeof item?.ignore === 'boolean' && item?.ignore) {
                // 在单列中中，设置了忽略此字段
                _ignoreField = true
              }
              if (!_ignoreField) {
                _columns.push(item)
              }
            }
          })
          this.queryColumnData = _columns
        } else {
          this.queryColumnData = []
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleQuerySearch() {
      this.$emit('handleQuerySearch', this.$refs.queryBuilder.$refs.ejsRef.getRules())
    },
    // 获取当前列使用哪种选择器
    getOpt(item) {
      const type = item?.type
      const temp = item?.queryTemplate
      var operators = {
        stringOperator: [
          { key: this.$t('包含'), value: 'contains' },
          { key: this.$t('等于'), value: 'equal' },
          { key: this.$t('不等于'), value: 'notequal' }
        ],
        numberOperator: [
          { key: this.$t('等于'), value: 'equal' },
          { key: this.$t('不等于'), value: 'notequal' },
          { key: this.$t('大于'), value: 'greaterthan' },
          { key: this.$t('大于等于'), value: 'greaterthanorequal' },
          { key: this.$t('小于'), value: 'lessthan' },
          { key: this.$t('小于等于'), value: 'lessthanorequal' }
        ],
        dateOperator: [
          { key: this.$t('包含'), value: 'contains' },
          { key: this.$t('不等于'), value: 'notequal' },
          { key: this.$t('大于'), value: 'greaterthan' },
          { key: this.$t('大于等于'), value: 'greaterthanorequal' },
          { key: this.$t('小于'), value: 'lessthan' },
          { key: this.$t('小于等于'), value: 'lessthanorequal' }
        ],
        booleanOperator: [{ key: this.$t('等于'), value: 'equal' }],
        selectOperator: [
          { key: this.$t('等于'), value: 'equal' },
          { key: this.$t('不等于'), value: 'notequal' }
        ]
      }
      if (temp) {
        const tempType = temp.type
        if (tempType == 'select') {
          return operators.selectOperator
        } else {
          return []
        }
      } else {
        if (type == 'string') {
          return operators.stringOperator
        } else if (type == 'number') {
          return operators.numberOperator
        } else if (type == 'boolean') {
          return operators.booleanOperator
        } else if (type == 'date') {
          return operators.dateOperator
        } else {
          return []
        }
      }
    },

    getTemp(args) {
      if (!args) return
      const type = args.type
      const options = args.options
      if (type == 'select') {
        return getSelectTemp({
          $mainInstance: this,
          fields: options.fields,
          ds: options.ds
        })
      } else if (type == 'mulitiSelect') {
      } else {
      }
    },

    // 处理列筛选
    serializeColumnFilter(_col) {
      const _map = _col.valueConverter.map
      if (Array.isArray(_map)) {
        const _querySeleteList = _map
        _col.type = 'string'
        _col.queryType = 'number'
        _col.querySeleteList = _querySeleteList
        _col.queryTemplate = {
          type: 'select',
          options: {
            ds: _querySeleteList
          }
        }
        _col.filter = this.colFilter(_col)
      } else if (Object.prototype.toString.call(_map) === '[object Object]') {
        const _querySeleteList = []
        const _querySeleteData = _map
        for (const i in _querySeleteData) {
          _querySeleteList.push({ label: _querySeleteData[i], text: _querySeleteData[i], value: i })
        }
        _col.type = 'string'
        _col.queryType = 'number'
        _col.querySeleteList = _querySeleteList
        _col.queryTemplate = {
          type: 'select',
          options: {
            ds: _querySeleteList
          }
        }
        _col.filter = this.colFilter(_col)
      }
    },
    // 处理列过滤(下拉框渲染)
    colFilter(col) {
      if (!col.querySeleteList || col.querySeleteList.length < 1) {
        return
      }
      let dropInstance = null
      const _fields = col?.valueConverter?.fields ?? { text: 'text', value: 'value' }
      const _seleteList = [...col.querySeleteList]
      _seleteList.forEach((e) => {
        e.label = e[_fields.text]
        e.value = e[_fields.value]
      })
      return {
        ui: {
          create: function (args) {
            const flValInput = createElement('input', { className: 'flm-input' })
            args.target.appendChild(flValInput)
            dropInstance = new DropDownList({
              dataSource: new DataManager(_seleteList),
              placeholder: col.palceHolder || this.$t('请选择'),
              popupHeight: '200px'
            })
            dropInstance.appendTo(flValInput)
          },
          write: function (args) {
            dropInstance.value = args.filteredValue
          },
          read: function (args) {
            args.fltrObj.filterByColumn(args.column.field, args.operator, dropInstance.value)
          }
        }
      }
    },

    handleQueryReset() {
      this.$refs.queryBuilder.$refs.ejsRef.reset()
      this.$emit('handleQueryReset')
    }
  }
}
</script>

<style lang="scss" scoped>
.query-builder {
  padding-top: 20px;
}
</style>
