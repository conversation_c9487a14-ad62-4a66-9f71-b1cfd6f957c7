<template>
  <div class="grid-container">
    <mt-data-grid
      v-bind="$attrs"
      v-on="customListeners"
      :class="[{ hasFrozen: hasFrozenColumn }]"
      :id="gridId"
      ref="gridRef"
      :height="gridHeightCfg.height"
      :data-source="gridData"
      :column-data="customColumnData"
      :grid-lines="gridLines"
      :allow-selection="allowSelection"
      :selection-settings="selectionSettings"
      :allow-reordering="allowReordering"
      :column-drop="columnDrop"
      :allow-resizing="allowResizing"
      :allow-paging="allowPaging"
      :allow-grouping="allowGrouping"
      :page-settings="pageSetting"
      :allow-sorting="allowSorting"
      :allow-filtering="allowFiltering"
      :filter-settings="serializeFilterSetting"
      :show-column-chooser="true"
      :frozen-columns="frozenColumns"
      :allow-excel-export="allowExcelExport"
      :enable-virtualization="dynamicVirtualization"
      @resizeStop="handleResizeStop"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
      @onNum="onNum"
    />
    <div
      v-if="isShowCustomLoading"
      class="loading-modal"
      :style="{ '--loading-modal-height': gridHeightCfg.height + 'px' }"
    >
      <div class="loading-spinner"></div>
    </div>
    <mt-dialog
      ref="toast"
      :header="$t('已选项')"
      :buttons="buttons"
      :show-close-icon="false"
      :open="onOpen"
    >
      <div style="height: 100%; paddingtop: 20px">
        <mt-data-grid
          v-bind="$attrs"
          v-on="$listeners"
          class="chosen-list-height"
          :id="gridId"
          :data-source="gridDataOnNum"
          :column-data="getGridColumnData2"
        />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
// import MtDataGrid from '@mtech-ui/data-grid'
import MtDataGrid from '@/components/template-page/ejsGrid/main.vue'
import MtDialog from '@mtech-ui/dialog'
import { tempRowData } from './components/tempData.js'

// import PluginGridMixin from '@mtech/plugin-grid-mixin'
import PluginGridMixin from '../../pluginGridMixin/src/index.js'

import ProvidedComponent from './components/providedCpnt.vue'
import FreeEditor from './components/freeEditor.js'
import cloneDeep from 'lodash/cloneDeep'
import debounce from 'lodash/debounce'

// 没有使用自定义editor的列
const defaultField = []

export default {
  inheritAttrs: false,
  name: 'PluginDataGrid',
  mixins: [PluginGridMixin],
  components: {
    MtDataGrid,
    MtDialog
  },
  inject: {
    searchFormModelList: {
      value: 'searchFormModelList',
      default: () => {
        return [{}]
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: () => {
            this.$dialog({
              data: {
                title: this.$t('警告'),
                message: this.$t('是否清空所有选中项?')
              },
              success: () => {
                this.$refs.gridRef.clearAll()
                this.gridDataOnNum = []
                this.hide()
              }
            })
          },
          buttonModel: { isPrimary: 'true', content: this.$t('清空') }
        }
      ],
      gridDataOnNum: [],
      refreshKey: new Date(),
      isShowCustomLoading: false
    }
  },
  computed: {
    customDataSource() {
      return cloneDeep(this.gridData || [])
    },
    getGridColumnData2() {
      let arr = [...this.getGridColumnData]
      arr.shift()
      if (this.ignoreColumns.length > 0) {
        arr = arr.filter((e) => {
          return !this.ignoreColumns.includes(e.field)
        })
      }
      return arr
    },
    newItem() {
      const newItem = {}
      this.getGridColumnData.forEach((item) => {
        newItem[item.field] = null
      })
      return newItem
    },
    newItemIndex() {
      const newRowPosition = this.$attrs.editSettings?.newRowPosition || 'Top'
      const len = this.gridData.length
      return newRowPosition === 'Top' ? 0 : len + 1
    },
    customColumnData() {
      // 如果不启用自定义editor直接返回getGridColumnData以减少关于后续遍历的程序开销
      if (!this.$attrs.isUseCustomEditor) return this.getGridColumnData
      /** ------------------------------------------------------------------ */
      const tempCols = this.getGridColumnData
      if (!Array.isArray(tempCols)) return []
      defaultField.splice(0, defaultField.length)
      const parentVm = this
      tempCols.forEach((item) => {
        if (item.providedEditor) {
          const template = {
            template: `<ProvidedComponent :scoped="scoped" :dataSource="dataSource" :fieldKey="field" :editorParams="editorParams" :ejsRef="ejsRef"></ProvidedComponent>`,
            components: {
              ProvidedComponent
            },
            data() {
              return {
                data: {},
                dataSource: parentVm.gridData,
                field: item.field,
                editorParams: item.editorParams,
                scoped: {},
                ejsRef: parentVm?.$refs?.gridRef?.ejsRef
              }
            },
            created() {
              // 定义customDataSource是为了让开发者能在freeEditor中拿到同一个对象的引用
              const idx = parentVm.currentEditRowIndex || 0
              const tempObj = parentVm.customDataSource[idx]
              tempObj.index = idx
              this.scoped = tempObj
            }
          }
          item.editTemplate = function () {
            return {
              template
            }
          }
        } else if (item.editorRender && typeof item.editorRender === 'function') {
          const template = {
            template: `<FreeEditor :scoped="scoped" :dataSource="dataSource" :fieldKey="field" :editorRender="editorRender"></FreeEditor>`,
            components: {
              FreeEditor
            },
            data() {
              return {
                data: {},
                dataSource: parentVm.gridData,
                field: item.field,
                editorRender: item.editorRender,
                scoped: {}
              }
            },
            created() {
              // 定义customDataSource是为了让开发者能在freeEditor中拿到同一个对象的引用
              const idx = parentVm.currentEditRowIndex || 0
              const tempObj = parentVm.customDataSource[idx]
              tempObj.index = idx
              this.scoped = tempObj
            }
          }
          item.editTemplate = function () {
            return {
              template
            }
          }
        } else {
          if (!defaultField.includes(item.field) && item.field) defaultField.push(item.field)
        }
      })
      return tempCols
    },
    customListeners() {
      return Object.assign({}, this.$listeners, {
        actionBegin: this.actionBegin,
        actionComplete: this.actionComplete,
        cellSave: this.cellSave
      })
    }
  },
  deactivated() {
    this.hide()
  },
  methods: {
    columnDrop() {
      this.$nextTick(() => {
        let _showColumns = this.$refs.gridRef.ejsRef.getVisibleColumns()
        if (Array.isArray(_showColumns) && _showColumns.length) {
          const _visible = []
          _showColumns.forEach((c) => {
            if (c.field && c.headerText) {
              _visible.push({
                field: c.field,
                headerText: c.headerText,
                ignore: !!c.ignore,
                sticky: !!c.sticky
              })
            }
          })
          this.$parent.$emit('handleSaveMemory', { visibleCols: _visible })
        }
      })
    },
    // 此处会覆盖掉mixin中的actionBegin，因为此处需要做大量数据双向绑定的处理，还没有兼容的tree-grid -- lbj.2023.04.24
    actionBegin(args) {
      if (
        args.requestType === 'sorting' &&
        args?.direction &&
        this.columnData.find((e) => e.field === args.columnName)?.allowGlobalSorting
      ) {
        const _columnqueryParams = {
          column: args.columnName.replace(/[A-Z]/g, '_$&').toLowerCase(),
          asc: args.direction === 'Ascending'
        }
        if (this.pageSetting) {
          this.pageSetting.orders = []
          this.pageSetting.orders.push(_columnqueryParams)
        }
        this.$emit('handleQuerySearch', this.queryBuilderRules)
      }
      if (args.requestType === 'add') {
        const _parent = document.getElementById(this.gridId)
        const _elements = _parent.getElementsByClassName('hasFrozenColumn')
        if (_elements.length > 0) {
          _parent.removeChild(_elements[0])
        }
        // 新增时args中没有rowIndex,只有index
        this.customDataSource.splice(args.index, 0, cloneDeep(this.newItem))
        this.currentEditRowIndex = args.index
      }
      if (args.action === 'filter') {
        // 列过滤数据
        const _columnqueryParams = []
        if (args.columns && args.columns.length > 0) {
          args.columns.forEach((item) => {
            if (item.value) {
              _columnqueryParams.push({
                field: item.field,
                operator: item.operator,
                value: item.value
              })
            }
          })
          this.queryBuilderRules = { rules: _columnqueryParams }
          // this.$refs.gridRef.ejsRef.clearFiltering()
          this.$emit('handleQuerySearch', { rules: _columnqueryParams })
        }
      } else if (args.action === 'clearFilter') {
        // 列过滤数据-重置
        const _columnqueryParams = this.queryBuilderRules?.rules ?? []
        _columnqueryParams.forEach((item, index) => {
          if (item.field === args.currentFilterColumn.field) {
            _columnqueryParams.splice(index, 1)
          }
        })
        this.queryBuilderRules = { rules: _columnqueryParams }
        this.$emit('handleQuerySearch', { rules: _columnqueryParams })
      } else if (args.requestType === 'refresh') {
        if (this.$refs?.gridRef?.ejsRef?.closeEdit) {
          this.$refs.gridRef.ejsRef.closeEdit()
        }
      } else if (args.requestType === 'beginEdit') {
        this.currentEditRowIndex = args.rowIndex
      } else if (args.requestType === 'save') {
        // 如果自定义editor的新增，要对gridData数据长度做适当调整，不然在数据回绑时index有问题
        // if (args.action === 'add' && this.$attrs.isUseCustomEditor) {
        //   this.gridData.unshift(tempRowData)
        // }
        if (Object.keys(tempRowData).length) {
          // 使用了自定义editor并改动了值
          defaultField.forEach((field) => {
            tempRowData[field] = args.data[field]
          })
          // this.bindDataToDataSource(tempRowData)
          args.data = cloneDeep(tempRowData)
        }
      }
      this.$emit('actionBegin', args)
    },
    actionComplete: debounce(function (args) {
      let { requestType } = args
      // const idx = rowIndex || args.index
      if (requestType === 'save') {
        // 通过 addRecord() 新增行是不会立即修改到girdData中，就会导致当前表格中行数与此刻gridData中的行数对应不上
        if (args.action === 'add' && this.$attrs.isUseCustomEditor) {
          const viewRecords = this.$refs.gridRef.ejsRef.getCurrentViewRecords()
          if (viewRecords.length > this.gridData.length) {
            this.gridData.unshift(cloneDeep(tempRowData))
          }
        }
        if (Object.keys(tempRowData).length) {
          // 使用了自定义editor并改动了值
          const tempRow = cloneDeep(tempRowData)
          this.bindDataToDataSource(tempRow)
          args.data = tempRow
          // 每次绑定完值都清空对象
          for (const key in tempRowData) {
            if (Object.hasOwnProperty.call(tempRowData, key)) {
              delete tempRowData[key]
            }
          }
        }
      }
      this.$emit('actionComplete', args)
    }, 20),
    bindDataToDataSource(row) {
      this.$set(this.gridData, row.index, row)
      if (this.asyncConfig?.url) {
        // 强制刷新数据
        const tempList = cloneDeep(this.gridData)
        this.asyncDataList = []
        this.$set(this, 'asyncDataList', tempList)
      }
    },
    deleteData(index) {
      this.gridData.splice(index, 1)
      if (this.asyncConfig?.url) {
        // 强制刷新数据
        const tempList = cloneDeep(this.gridData)
        this.asyncDataList = []
        this.$set(this, 'asyncDataList', tempList)
      }
    },
    cellSave(args) {
      // const gridCpn = this.$refs.gridRef.ejsRef.ej2Instances
      // const rowIdx = gridCpn.getRowIndexByPrimaryKey(args.rowData.id)
      // gridCpn.updateCell(rowIdx, args.columnName, args.value)
      this.$emit('cellSave', args)
    },
    onNum(e) {
      // this.$set(this,'gridDataOnNum',e)
      this.gridDataOnNum = []
      this.gridDataOnNum.push(...e)
      this.show()
      // this.gridDataOnNum = e
    },
    onOpen(args) {
      args.preventFocus = true
    },
    show() {
      this.$refs.toast.ejsRef.show()
    },
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
    showCustomLoading() {
      this.isShowCustomLoading = true
    },
    hideCustomLoading() {
      this.isShowCustomLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>
.grid-container {
  width: 100%;
  height: 100%;
  padding: 0 8px !important;
  background: var(--plugin-dg-bg-ff);
  .loading-modal {
    width: 100%;
    height: var(--loading-modal-height);
    position: absolute;
    z-index: 10;
    bottom: 0;
    right: 0;
    .loading-spinner {
      width: 40px;
      height: 40px;
      position: absolute;
      left: 50%;
      top: 50%;
      margin-top: -50px;
      margin-left: -20px;
      border: 4px solid #f3f3f3; /* 加载图案的边框颜色 */
      border-top: 4px solid #3498db; /* 加载图案的顶部颜色 */
      border-radius: 50%; /* 将边框变为一个圆 */
      animation: spin 1s linear infinite; /* 创建旋转动画 */
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      } /* 起始状态，不旋转 */
      100% {
        transform: rotate(360deg);
      } /* 最终状态，旋转一周 */
    }
  }
  ::v-deep .mt-data-grid {
    .e-grid {
      // border: none; // 注释这个可以去除grid外边框
      .e-gridcontent {
        border-top: 0;
        border-bottom: 0;
        .e-content {
          overflow-y: auto !important; //mt-data-grid中，overflow-y:scroll
        }
        .e-scrollbar {
          .e-frozenscrollbar {
            border-top: none;
          }
          .e-movablescrollbar {
            //mt-data-grid中，overflow-x:scroll
            overflow-x: auto;
            min-height: unset !important;
            max-height: unset !important;
            height: 10px !important;
            .e-movablechild {
              min-height: unset !important;
              max-height: unset !important;
              height: 10px !important;
            }
          }
        }
      }
      th.e-headercell {
        vertical-align: middle;
      }
      td.e-rowcell {
        // 注释这个可以去除grid内部cell的边框
        border-right: 1px solid rgba(224, 224, 224, 1);
      }
      // td.e-rowcell{
      //   border-left: 0 !important;
      //   border-right: 0 !important;
      // }
    }

    //底部mt-page相关样式
    .mt-pagertemplate {
      margin: 0;
      padding: 10px 0;
      box-sizing: border-box;
      border-top: 1px solid var(--plugin-dg-border-color);
    }
    //存在冻结列，相关样式
    &.hasFrozen {
      .e-frozen-left-header > .e-table,
      .e-frozen-left-content > .e-table {
        border-right: 0;
        box-shadow: 2px 0 2px 0 var(--plugin-dg-shadow-color);
        position: relative;
      }
      .e-frozen-right-header > .e-table,
      .e-frozen-right-content > .e-table {
        border-left: 0;
        box-shadow: -2px 0 2px 0 var(--plugin-dg-shadow-color);
        position: relative;
      }
      .e-emptyrow {
        display: none;
      }
      .e-movablecontent {
        td.e-active:first-of-type:before {
          display: none;
        }
      }
    }

    //修改谷歌内核浏览器滚动条样式
    ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }
    ::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: var(--scroll-bar-track-color);
    }

    ::-webkit-scrollbar-thumb {
      background-color: var(--scroll-bar-thumb-color);
      border-radius: 2px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: var(--scroll-bar-thumb-hover-color);
    }

    ::-webkit-scrollbar-thumb:active {
      background-color: var(--scroll-bar-thumb-active-color);
    }
  }
}
::v-deep .chosen-list-height {
  height: 100%;
  > .e-grid {
    height: 100%;
    .e-gridcontent {
      height: calc(100% - 40px);
      .e-content {
        height: 100% !important;
      }
    }
  }
}
</style>
