<template>
  <div
    v-if="isExternal"
    :style="styleExternalIcon"
    class="svg-external-icon svg-icon"
    v-on="$listeners"
  />
  <div
    v-else
    class="mt-tooltip-container"
    @mouseenter="svgMouseEnterEvent"
    @mouseleave="svgMouseLeaveEvent"
  >
    <mt-tooltip opens-on="Custom" :content="svgTooltip" ref="tooltipForSvgIcon">
      <mt-icon :name="icon" />
    </mt-tooltip>
  </div>
</template>

<script>
import MtTooltip from '@mtech-ui/tooltip'
import MtIcon from '@mtech-ui/icon'
export default {
  name: 'MtSvgIcon',
  props: {
    icon: {
      type: String,
      required: true
    },
    svgTooltip: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  components: {
    MtTooltip,
    MtIcon
  },
  computed: {
    isExternal() {
      return /^(https?:|mailto:|tel:)/.test(this.iconClass)
    },
    styleExternalIcon() {
      return {
        mask: `url(${this.iconClass}) no-repeat 50% 50%`,
        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`
      }
    }
  },
  methods: {
    svgMouseEnterEvent(args) {
      if (this.svgTooltip) {
        this.$refs.tooltipForSvgIcon.open(args.target)
        setTimeout(() => {
          this.$refs.tooltipForSvgIcon.close(args.target)
        }, 2000)
      }
    },
    svgMouseLeaveEvent(args) {
      if (this.svgTooltip) {
        this.$refs.tooltipForSvgIcon.close(args.target)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mt-tooltip-container {
  display: inline-block;
  .mt-tooptip {
    width: auto;
    .mt-icons {
      top: -1px;
      position: relative;
    }
  }
}

.svg-icon {
  cursor: pointer;
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  &:hover {
    color: #00469c;
  }
}

.svg-external-icon {
  background-color: currentColor;
  mask-size: cover !important;
  display: inline-block;
}
</style>
