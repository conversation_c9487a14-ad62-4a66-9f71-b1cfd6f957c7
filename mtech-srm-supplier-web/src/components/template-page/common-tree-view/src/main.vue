<template>
  <div class="mt-commom-tree-view" :id="mtCommonTreeViewId">
    <mt-treeView
      ref="commonTreeView"
      v-bind="$attrs"
      :node-template="Template"
      v-on="$listeners"
      :fields="fields"
      @nodeSelected="nodeSelected"
    ></mt-treeView>
  </div>
</template>

<script>
import Vue from 'vue'

export default {
  props: {
    unButton: {
      type: Boolean,
      default: false
    },
    fields: {
      type: Object,
      default: () => {
        return {}
      }
    },
    mtCommonTreeViewId: {
      type: String,
      default: 'mt-common-tree-view'
    }
  },
  data() {
    const _this = this
    return {
      active: 0,
      Template: function () {
        return {
          template: Vue.component('commonTree', {
            template: `<div class="action-boxs">
                            <div v-if='$template' class="name-box" >
                              <component :is='$template' :mtData='data'></component>
                            </div>
                            <div class="name-box" v-else>
                              <span :class="data.labelClass" v-if="data.label">{{data.label}}</span>
                              {{ data.name }}
                            </div>
                            
                            <div class="btn-box"
                                @click.stop="active(data)" v-if="$unButton && !data.setOperation">
                            </div>
                            <div class="btn-box"
                                  @click.stop="active(data)" v-else-if="data.setOperation">
                                <div class="minus">
                                    <svg t="1629947806998" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9821" width="16" height="16"><path d="M512 102.4m-102.4 0a102.4 102.4 0 1 0 204.8 0 102.4 102.4 0 1 0-204.8 0Z" fill="#98AAC3" p-id="9822"></path><path d="M512 512m-102.4 0a102.4 102.4 0 1 0 204.8 0 102.4 102.4 0 1 0-204.8 0Z" fill="#98AAC3" p-id="9823"></path><path d="M512 921.6m-102.4 0a102.4 102.4 0 1 0 204.8 0 102.4 102.4 0 1 0-204.8 0Z" fill="#98AAC3" p-id="9824"></path></svg>
                                </div>
                                <div class="more" v-if="activeId == data[$id]">
                                    <div v-for="(item, i) in data.setOperation" class="operation" :class="item.class" @click="onButton(data,item)">
                                      {{item.text}}
                                    </div>
                                </div>
                            </div>
                            <div class="btn-box"
                                        @click.stop="active(data)" v-else>
                                        <div class="minus">
                                    <svg t="1629947806998" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9821" width="16" height="16"><path d="M512 102.4m-102.4 0a102.4 102.4 0 1 0 204.8 0 102.4 102.4 0 1 0-204.8 0Z" fill="#98AAC3" p-id="9822"></path><path d="M512 512m-102.4 0a102.4 102.4 0 1 0 204.8 0 102.4 102.4 0 1 0-204.8 0Z" fill="#98AAC3" p-id="9823"></path><path d="M512 921.6m-102.4 0a102.4 102.4 0 1 0 204.8 0 102.4 102.4 0 1 0-204.8 0Z" fill="#98AAC3" p-id="9824"></path></svg>
                                </div>
                                <div class="more" v-if="activeId == data[$id]">
                                    <div v-for="(item, i) in commonBtn" class="operation" :class="item.class" @click="onButton(data,item)">
                                      {{item.text}}
                                    </div>
                                </div>
                            </div>
                        </div>`,
            data() {
              return {
                data: {},
                commonBtn: [{ text: this.$t('新增下级') }, { text: this.$t('删除') }]
              }
            },
            computed: {
              activeId() {
                return _this.active
              },
              $id() {
                return _this.fields.id
              },
              $unButton() {
                return _this.unButton
              },
              $template() {
                if (_this.fields.nodeTemplate) {
                  return _this.fields.nodeTemplate().template.extendOptions.name
                } else {
                  return false
                }
              }
            },
            methods: {
              active(data) {
                _this.active = data[this.$id]
              },
              onButton(data, item) {
                _this.onButton(data, item)
              }
            }
          })
        }
      }
    }
  },
  mounted() {
    document.onmousedown = (e) => {
      if (e.target.classList.value != 'operation') {
        this.active = 0
      }
    }
  },
  methods: {
    onButton(data, item) {
      data.onBtn = item
      this.$emit('onButton', data)
    },
    nodeSelected(e) {
      // this.$emit('nodeSelected', e)
      const _this = this
      function a(node) {
        if (node.className.indexOf('e-level-1') > -1) {
          _this.handleAddBg(node.dataset.uid)
        } else {
          a(node.offsetParent)
        }
      }
      a(e.node)
    },
    handleAddBg(uid) {
      const _levelEleList = document
        .getElementById(this.mtCommonTreeViewId)
        .getElementsByClassName('e-level-1')
      Array.prototype.forEach.call(_levelEleList, (item) => {
        if (item.getAttribute('data-uid') == uid) {
          item.classList.add('expandedLevel1')
        } else if (item.classList.contains('expandedLevel1')) {
          item.classList.remove('expandedLevel1')
        }
      })
    },
    getCommonMethods() {
      return this.$refs.commonTreeView.ejsRef
    }
  }
}
</script>
<style lang="scss">
.mt-commom-tree-view {
  .expandedLevel1 {
    background: var(--common-tw-bg);
    position: relative;
    .e-icons {
      &::before {
        // color: var(--common-tw-level-color) !important;
      }
    }
  }
  .mt-tree-view {
    width: 100%;
    .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
      font-weight: 500 !important;
    }
    #treeview > .e-ul {
      padding: 0;
    }
    .e-treeview {
      overflow: visible;
      .e-list-item.e-active > .e-fullrow {
        background-color: var(--common-tw-level-active-color);
        border-color: var(--common-tw-level-active-color);
      }
      .e-ul {
        overflow: visible;
      }
      .e-icon-collapsible {
        &::before {
          color: var(--common-tw-collapsible-color);
        }
      }
    }
  }
  .e-icon-wrapper {
    display: flex;
    .e-list-text {
      flex: 1;
      display: flex;
    }
  }
  .action-boxs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .name-box {
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      font-family: 'PingFangSC';
    }

    .btn-box {
      width: 30px;
      display: flex;
      position: relative;
      .more {
        width: 120px;
        position: absolute;
        background: var(--common-tw-bg-ff);
        border-radius: 2px;
        box-shadow: 0 3px 12px 0 var(--common-tw-action-boxs-shadow-color);
        text-align: center;
        top: 36px;
        z-index: 1;
        padding: 5px 0;
        left: -42px;
        .operation {
          // height: 14px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: var(--common-tw-action-boxs-operation-color);
          &:hover {
            background: var(--common-tw-level-active-color);
            cursor: pointer;
          }
        }
        &::before {
          content: '';
          position: absolute;
          top: -10px;
          left: 40px;
          width: 0;
          height: 0;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-bottom: 10px solid var(--common-tw-action-boxs-border-color);
          box-shadow: 30px 10px 30px 0px var(--common-tw-action-boxs-shadow-color);
        }
      }
      .minus {
        width: 16px;
        height: 16px;
        line-height: 16px;
        font-size: 16px;
        i {
          width: 16px;
          height: 16px;
          line-height: 16px;
          font-size: 16px;
          color: var(--common-tw-action-boxs-minus-color);
        }
      }
    }
  }
  .e-list-text {
    color: var(--common-tw-span-color);
    flex: 1;
  }
  .e-treeview.e-interaction.e-fullrow-wrap .e-text-content {
    display: flex;
    align-items: center;
  }
}
</style>
<style lang="scss" scoped>
::v-deep .mt-tree-view .e-treeview .e-list-item {
  padding: 2px 0px 2px 12px;
}
</style>
