<template>
  <mt-dialog
    ref="combinationSelectionRef"
    css-class="combination-selction"
    :height="500"
    :enable-resize="false"
    :header="$t('高级查询')"
    :target="getDialogTarget"
    :buttons="buttons"
    @close="cancel"
  >
    <div class="dialog-content">
      <div class="rule-item-container">
        <div
          class="rule-item"
          v-for="(item, index) in combinationData"
          :key="'combination-rule-' + index"
        >
          <div class="item condition" v-if="index > 0">
            <!-- <mt-input
              v-if="index < 1"
              cssClass="rule-element"
              :readonly="true"
              :show-clear-button="false"
              float-label-type="Never"
              v-model="conditionLabel"
            ></mt-input> -->
            <mt-select
              css-class="rule-element"
              v-model="conditionValue"
              :data-source="conditionList"
            ></mt-select>
          </div>
          <div
            :class="['item', 'data-source', index === 0 && 'data-source-first']"
            :ref="'field-' + index"
          >
            <mt-select
              css-class="rule-element"
              v-model="item.field"
              :data-source="getFieldList(index)"
              :fields="{ text: 'headerText', value: 'field' }"
            ></mt-select>
          </div>
          <div class="item operator" :ref="'operator-' + index">
            <mt-select
              css-class="rule-element"
              v-model="item.operator"
              :data-source="getOperatorList(index)"
              popup-width="90px"
            ></mt-select>
          </div>
          <div class="item data-value" :ref="'value-' + index">
            <mt-input
              v-if="showElementByType(index, 'text')"
              v-model="item.value"
              :maxlength="getCurrentParam(index, 'maxQueryValueLength')"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              :placeholder="getCurrentParam(index, 'placeholder')"
              type="text"
            />
            <mt-input
              v-if="showElementByType(index, 'number')"
              v-model.number="item.value"
              type="number"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              :placeholder="getCurrentParam(index, 'placeholder')"
            />
            <mt-select
              v-if="showElementByType(index, 'select')"
              v-model="item.value"
              :allow-filtering="checkAllowFiltering(item.field)"
              :filtering="onFiltering"
              :data-source="getElementDataSource(index)"
              :fields="getElementDataFields(index)"
              :placeholder="getCurrentParam(index, 'placeholder')"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              @focus="getFocousItem(index)"
            ></mt-select>
            <mt-multi-select
              v-if="showElementByType(index, 'multi-select')"
              v-model="item.value"
              :allow-filtering="checkAllowFiltering(item.field)"
              :filtering="onFiltering"
              :data-source="getElementDataSource(index)"
              :fields="getElementDataFields(index)"
              :placeholder="getCurrentParam(index, 'placeholder')"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              :show-select-all="getCurrentParam(index, 'showSelectAll')"
              :select-all-text="$t('全选')"
              :un-select-all-text="$t('全不选')"
              @focus="getFocousItem(index)"
            ></mt-multi-select>
            <mt-date-picker
              v-if="showElementByType(index, 'date')"
              v-model="item.value"
              :open-on-focus="true"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              :allow-edit="false"
              :placeholder="getCurrentParam(index, 'placeholder')"
            ></mt-date-picker>
            <mt-time-picker
              v-if="showElementByType(index, 'time')"
              v-model="item.value"
              :open-on-focus="true"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              :allow-edit="false"
              :placeholder="getCurrentParam(index, 'placeholder')"
            ></mt-time-picker>
            <mt-date-time-picker
              v-if="showElementByType(index, 'datetime')"
              v-model="item.value"
              :open-on-focus="true"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              :allow-edit="false"
              :placeholder="getCurrentParam(index, 'placeholder')"
            ></mt-date-time-picker>
            <mt-date-range-picker
              v-if="showElementByType(index, 'date-range')"
              v-model="item.value"
              :open-on-focus="true"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              :allow-edit="false"
              :placeholder="getCurrentParam(index, 'placeholder')"
            ></mt-date-range-picker>
            <mt-date-picker
              v-if="showElementByType(index, 'date-month')"
              start="Year"
              depth="Year"
              format="MMMM y"
              :open-on-focus="true"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
              v-model="item.value"
              :placeholder="getCurrentParam(index, 'placeholder')"
            ></mt-date-picker>
            <mt-switch
              v-if="showElementByType(index, 'switch')"
              v-model="item.value"
              :on-label="getSwitchLabel(index)['onLabel']"
              :off-label="getSwitchLabel(index)['offLabel']"
            ></mt-switch>
            <mt-checkbox
              v-if="showElementByType(index, 'checkbox')"
              @change="handleChangeCheckBox($event, index)"
            ></mt-checkbox>
            <mt-drop-down-tree
              v-if="showElementByType(index, 'drop-down-tree')"
              v-model="item.value"
              id="drop-down-tree"
              :placeholder="getCurrentParam(index, 'placeholder')"
              :fields="getElementTreeDataFields(index)"
              :show-clear-button="getCurrentParam(index, 'clearButton')"
            ></mt-drop-down-tree>
          </div>
          <div class="item remove" @click="removeCombination(index)" :ref="'remove-' + index">
            <mt-icon name="icon_input_clear"></mt-icon>
          </div>
        </div>
      </div>
      <div
        class="add-rule-item"
        @click="addCombination"
        ref="addCombinationRef"
        v-show="combinationData.length < columnData.length"
      >
        <mt-icon name="icon_card_plus"></mt-icon>
        <span class="label">{{ $t('添加查询条件') }}</span>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
// import { operaterConfig } from "./operators";
import MtDialog from '@mtech-ui/dialog'
import MtInput from '@mtech-ui/input'
import MtSelect from '@mtech-ui/select'
import MtMultiSelect from '@mtech-ui/multi-select'
import MtSwitch from '@mtech-ui/switch'
import MtCheckbox from '@mtech-ui/checkbox'
import MtDatePicker from '@mtech-ui/date-picker'
import MtTimePicker from '@mtech-ui/time-picker'
import MtDateTimePicker from '@mtech-ui/date-time-picker'
import MtDateRangePicker from '@mtech-ui/date-range-picker'
import MtDropDownTree from '@mtech-ui/drop-down-tree'
import MtIcon from '@mtech-ui/icon'
import * as ToolTip from './components/Tooltip'
import { API } from '@mtech-common/http'
import Vue from 'vue'
Vue.prototype[ToolTip.NAME] = ToolTip.COMPONENT
export default {
  name: 'PluginCombinationSelection',
  components: {
    MtDialog,
    MtSelect,
    MtSwitch,
    MtCheckbox,
    MtInput,
    MtDatePicker,
    MtTimePicker,
    MtDateTimePicker,
    MtDateRangePicker,
    MtMultiSelect,
    MtDropDownTree,
    MtIcon
  },
  props: {
    // 原始数据，列数据
    columnData: {
      type: Array,
      default: () => []
    },
    // 需要忽略的字段列表
    ignoreFields: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 支持搜索过滤的字段列表
    allowFilteringFields: {
      type: Array,
      default: () => {
        return []
      }
    },
    // dialog初始加载位置
    dialogPosition: {
      type: Object,
      default: () => {
        return {
          X: 0,
          Y: 0
        }
      }
    },
    dialogTarget: {
      type: String,
      default: '.common-template-page'
    },
    maxQueryValueLength: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.resetCombination,
          buttonModel: { isPrimary: 'true', content: this.$t('重置') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('查询') }
        }
      ],
      currentColumnField: null,
      currentColumnFields: null,
      columnDataSourceMap: {},
      currentColumnQueryMethod: null,
      currentColumnQueryUrl: null,
      currentColumnQueryParams: null,
      currentColumnQueryItem: null,
      combinationData: [{}],
      resourceFields: [],
      conditionLabel: this.$t('当'),
      conditionValue: 'and',
      conditionList: [
        { text: this.$t('或者'), value: 'or' },
        { text: this.$t('并且'), value: 'and' }
      ]
    }
  },
  watch: {
    columnData: {
      handler(n) {
        if (n) {
          const _resource = utils.cloneDeep(n)
          const _columns = []
          _resource.forEach((item) => {
            let _isIgnoreField = false
            if (this.ignoreFields.indexOf(item.field) > -1) {
              // 在ignoreFields中，设置了忽略此字段
              _isIgnoreField = true
            }
            if (typeof item?.ignore === 'boolean' && item?.ignore) {
              // 在单列中中，设置了忽略此字段
              _isIgnoreField = true
            }
            if (!_isIgnoreField) {
              _columns.push(item)
            }
          })
          this.serializeColumnData(_columns)
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    getDialogTarget() {
      return `.${this.dialogTarget}`
    },
    // 获取搜索字段的列表。由于字段只能配置一次，所以每一项的字段列表，都要排除其他已选择的字段
    getFieldList() {
      return (i) => {
        const _originData = utils.cloneDeep(this.resourceFields)
        const _combinationData = utils.cloneDeep(this.combinationData)
        const _usedFields = []
        const _currentDataSource = []
        if (Array.isArray(_combinationData) && _combinationData.length) {
          _combinationData.forEach((item, index) => {
            if (i !== index) {
              if (item?.field) {
                _usedFields.push(item.field)
              }
            }
          })
        }
        _originData.forEach((e) => {
          if (_usedFields.indexOf(e.field) < 0) {
            _currentDataSource.push(e)
          }
        })
        return _currentDataSource
      }
    },
    // 根据index获取当前element对象
    getCurrentElement() {
      return (i) => {
        const _originData = utils.cloneDeep(this.resourceFields)
        const _combinationData = utils.cloneDeep(this.combinationData)
        const _current = utils.cloneDeep(_combinationData[i])
        if (_current?.field) {
          const _find = _originData.filter((e) => e.field === _current.field)
          if (_find.length) {
            return _find[0]
          } else {
            return {}
          }
        } else {
          return {}
        }
      }
    },
    getCurrentParam() {
      return (i, param) => {
        const _originData = utils.cloneDeep(this.resourceFields)
        const _combinationData = utils.cloneDeep(this.combinationData)
        const _current = utils.cloneDeep(_combinationData[i])
        if (param === 'maxQueryValueLength') {
          if (_current?.field) {
            const _find = _originData.filter((e) => e.field === _current.field)
            if (_find.length) {
              return _find[0].searchOptions?.maxQueryValueLength
                ? _find[0].searchOptions?.maxQueryValueLength
                : this.maxQueryValueLength
            } else {
              return this.maxQueryValueLength
            }
          } else {
            return this.maxQueryValueLength
          }
        } else if (param === 'clearButton') {
          if (_current?.field) {
            const _find = _originData.filter((e) => e.field === _current.field)
            if (_find.length) {
              return _find[0].searchOptions?.clearButton ? _find[0].searchOptions.clearButton : true
            } else {
              return false
            }
          } else {
            return false
          }
        } else if (param === 'placeholder') {
          if (_current?.field) {
            const _find = _originData.filter((e) => e.field === _current.field)
            if (_find.length) {
              return _find[0].searchOptions?.placeholder
                ? _find[0].searchOptions.placeholder
                : _find[0].headerText
            } else {
              return ''
            }
          } else {
            return ''
          }
        } else if (param === 'showSelectAll') {
          if (_current?.field) {
            const _find = _originData.filter((e) => e.field === _current.field)
            if (_find.length) {
              return _find[0].searchOptions?.showSelectAll
                ? _find[0].searchOptions.showSelectAll
                : false
            } else {
              return false
            }
          } else {
            return false
          }
        }
      }
    },
    // 获取switch控件的label值
    getSwitchLabel() {
      return (i) => {
        const _currentColumn = utils.cloneDeep(this.getCurrentElement(i))
        return {
          onLabel: _currentColumn?.searchOptions?.onLabel ?? this.$t('打开'),
          offLabel: _currentColumn?.searchOptions?.offLabel ?? this.$t('关闭')
        }
      }
    },
    // 校验当前字段的下拉框，是否需要模糊搜索
    checkAllowFiltering() {
      return (field) => {
        return this.allowFilteringFields.indexOf(field) > -1
      }
    },
    // 根据当前元素、以及type判断当前使用的控件
    showElementByType() {
      return (i, type) => {
        const _currentColumn = utils.cloneDeep(this.getCurrentElement(i))
        if (_currentColumn?.field) {
          if (_currentColumn?.searchOptions?.elementType) {
            return _currentColumn?.searchOptions?.elementType === type
          } else {
            return type === 'text'
          }
        } else {
          return type === 'text'
        }
      }
    },
    // 获取select、multi-select控件的下拉选项
    getElementDataSource() {
      return (i) => {
        const _currentColumn = utils.cloneDeep(this.getCurrentElement(i))
        this.currentColumnFields = _currentColumn?.searchOptions?.fields || null
        this.currentColumnQueryMethod = _currentColumn?.searchOptions?.queryMethods || 'post'
        this.currentColumnQueryUrl = _currentColumn?.searchOptions?.queryUrl || null
        this.currentColumnQueryParams = _currentColumn?.searchOptions?.queryParams || null
        this.currentColumnQueryItem = _currentColumn?.searchOptions?.queryItem || 'fuzzyNameOrCode'
        if (!Object.prototype.hasOwnProperty.call(this.columnDataSourceMap, _currentColumn.field)) {
          this.columnDataSourceMap[_currentColumn.field] = []
        }
        if (_currentColumn?.searchOptions?.dataSource) {
          this.columnDataSourceMap[_currentColumn.field] = _currentColumn.searchOptions.dataSource
          return this.columnDataSourceMap[_currentColumn.field]
        } else if (this.currentColumnQueryUrl) {
          return this.columnDataSourceMap[_currentColumn.field]
        } else {
          return []
        }
      }
    },
    // 获取select、multi-select控件的下拉选项值，fields
    getElementDataFields() {
      return (i) => {
        const _currentColumn = utils.cloneDeep(this.getCurrentElement(i))
        if (_currentColumn?.searchOptions?.fields) {
          return _currentColumn?.searchOptions?.fields
        } else {
          return { text: 'text', value: 'value' }
        }
      }
    },
    // 获取tree-data控件的下拉选项值，fields
    getElementTreeDataFields() {
      return (i) => {
        const _currentColumn = utils.cloneDeep(this.getCurrentElement(i))
        if (_currentColumn?.searchOptions?.fields) {
          return _currentColumn?.searchOptions?.fields
        } else {
          return {
            value: 'value',
            text: 'text',
            dataSource: [],
            child: 'child'
          }
        }
      }
    },
    // 根据当前元素，使用的不同控件，选择匹配规则  todo
    getOperatorList() {
      const operaterConfig = {
        string: [
          { text: this.$t('包含'), value: 'contains' },
          { text: this.$t('等于'), value: 'equal' },
          { text: this.$t('不等于'), value: 'notequal' }
        ],
        number: [
          { text: this.$t('等于'), value: 'equal' },
          { text: this.$t('不等于'), value: 'notequal' },
          { text: this.$t('大于'), value: 'greaterthan' },
          { text: this.$t('大于等于'), value: 'greaterthanorequal' },
          { text: this.$t('小于'), value: 'lessthan' },
          { text: this.$t('小于等于'), value: 'lessthanorequal' }
        ],
        date: [
          { text: this.$t('包含'), value: 'contains' },
          { text: this.$t('等于'), value: 'equal' },
          { text: this.$t('不等于'), value: 'notequal' },
          { text: this.$t('大于'), value: 'greaterthan' },
          { text: this.$t('大于等于'), value: 'greaterthanorequal' },
          { text: this.$t('小于'), value: 'lessthan' },
          { text: this.$t('小于等于'), value: 'lessthanorequal' }
        ],
        boolean: [
          { text: this.$t('等于'), value: 'equal' },
          { text: this.$t('不等于'), value: 'notequal' }
        ],
        select: [
          { text: this.$t('等于'), value: 'equal' },
          { text: this.$t('不等于'), value: 'notequal' },
          { text: this.$t('包含'), value: 'in' }
        ],
        multiSelect: [{ text: this.$t('包含'), value: 'in' }],
        dateRange: [{ text: this.$t('区间'), value: 'between' }]
      }
      const operators = operaterConfig
      return (i) => {
        const _currentColumn = utils.cloneDeep(this.getCurrentElement(i))
        const _elementType = _currentColumn?.searchOptions?.elementType
        let _operatorList = []
        switch (_elementType) {
          case 'date':
          case 'time':
          case 'datetime':
            _operatorList = operators.date
            break
          case 'date-range':
            _operatorList = operators.dateRange
            break
          case 'switch':
          case 'checkbox':
            _operatorList = operators.boolean
            break
          case 'number':
            _operatorList = operators.number
            break
          case 'select':
          case 'drop-down-tree':
            _operatorList = operators.select
            break
          case 'multi-select':
          case 'remote-autocomplete':
            _operatorList = operators.multiSelect
            break
          default:
            _operatorList = operators.string
            break
        }
        const _operator = this.combinationData[i].operator
        const _findIndex = _operatorList.findIndex((e) => e.value === _operator)
        if (_findIndex < 0) {
          this.combinationData[i].operator = _operatorList[0].value
        }
        return _operatorList
      }
    }
  },
  mounted() {
    this.onFiltering = utils.debounce(this.onFiltering, 1000)
    this.$refs.combinationSelectionRef.ejsRef.show()
  },
  methods: {
    getFocousItem(e) {
      const _currentColumn = utils.cloneDeep(this.getCurrentElement(e))
      this.currentColumnField = _currentColumn.field
    },
    changeData(args, index, item) {
      // let _column = this.getCurrentElement(index);
      // if(_column.searchOptions.type==="select"){
      //   item.value = args.itemData[_column.searchOptions.fields.value];
      // }
    },
    onFiltering(e) {
      if (this.currentColumnQueryUrl) {
        if (!this.currentColumnQueryParams) {
          this.currentColumnQueryParams = {}
        }
        this.currentColumnQueryParams[this.currentColumnQueryItem] = e.text
        API[this.currentColumnQueryMethod](
          `${this.currentColumnQueryUrl}`,
          this.currentColumnQueryParams
        ).then((res) => {
          this.columnDataSourceMap[this.currentColumnField] = res.data
          this.$nextTick(() => {
            e.updateData(this.columnDataSourceMap[this.currentColumnField])
          })
        })
      } else {
        if (this.currentColumnFields === null) {
          e.updateData(
            this.columnDataSourceMap[this.currentColumnField].filter((x) => x.includes(e.text))
          )
        } else {
          e.updateData(
            this.columnDataSourceMap[this.currentColumnField].filter((x) =>
              x[this.currentColumnFields.text].includes(e.text)
            )
          )
        }
      }
    },
    // 序列化columnData,主要处理dataSource、elementType
    serializeColumnData(list) {
      const _columns = utils.cloneDeep(list)
      const _lastLength = (list.length ?? 0) % 4
      this.emptyItems = _lastLength ? 4 - _lastLength : 0
      _columns.forEach((e) => {
        const _options = e?.searchOptions ?? {}
        const _valueConverter = e?.valueConverter
        if (_valueConverter?.map) {
          if (Array.isArray(_valueConverter?.map)) {
            _options.dataSource = _valueConverter?.map
          } else {
            const _list = []
            const _converterData = _valueConverter?.map
            for (const i in _converterData) {
              _list.push({ text: _converterData[i], value: i })
            }
            _options.dataSource = _list
          }
          _options.fields = _valueConverter?.fields
            ? _valueConverter?.fields
            : { text: 'text', value: 'value' }
          _options.type = _options.type ?? 'string'
          _options.elementType = _options.elementType ?? 'select'
          _options.operator = _options.operator ?? 'equal'
        }
        let _elementType = 'text'
        let _type = _options.type ? _options.type : e.type ?? 'string'
        switch (_type) {
          case 'date':
            _elementType = 'date'
            _type = 'string'
            break
          case 'time':
            _elementType = 'time'
            _type = 'string'
            break
          case 'datetime':
            _elementType = 'datetime'
            _type = 'string'
            break
          case 'number':
            _elementType = 'number'
            _type = 'number'
            break
        }
        _options.type = _type
        _options.elementType = _options.elementType ? _options.elementType : _elementType ?? 'text'
        _options.operator = _options.operator
          ? _options.operator
          : _options.elementType === 'select'
          ? 'equal'
          : 'contains'
        e.searchOptions = _options
      })
      this.resourceFields = _columns
    },
    // 添加搜索条件
    addCombination() {
      const _combinationData = utils.cloneDeep(this.combinationData)
      const _checkDataSource = this.checkCombinationData(_combinationData)
      if (_checkDataSource) {
        const _originData = utils.cloneDeep(this.resourceFields)
        if (_combinationData.length < _originData.length) {
          _combinationData.push({
            field: '',
            operator: '',
            value: ''
          })
          this.combinationData = _combinationData
        } else {
          this.handleTooltip(this.$refs.addCombinationRef, this.$t('所有字段均已配置查询条件'))
        }
      }
    },
    // 移除搜索条件
    removeCombination(i) {
      const _combinationData = utils.cloneDeep(this.combinationData)
      if (i < 1 && _combinationData.length < 2) {
        this.handleRuleTooltip(i, 'remove', this.$t('至少保留一个搜索条件'))
        return
      }
      _combinationData.splice(i, 1)
      this.combinationData = _combinationData
    },
    // 校验当前已经存在的搜索规则，是否规范。当前主要是判空操作
    checkCombinationData(_rules) {
      // 校验当前值，包括dataSource、symbol、dataValue
      let _checkResult = true
      for (const i in _rules) {
        const _rule = _rules[i]
        const _field = _rule.field
        if (!_field || !_field.trim()) {
          // field为空，校验不通过
          _checkResult = false
          this.handleRuleTooltip(i, 'field', this.$t('未选择字段'))
          break
        }

        const _operator = _rule.operator
        if (!_operator || !_operator.trim()) {
          // operator为空，校验不通过
          _checkResult = false
          this.handleRuleTooltip(i, 'operator', this.$t('未设置搜索规则'))
          break
        }
        const _value = _rule.value
        if (_value === undefined || (typeof _value === 'string' && !_value.trim())) {
          // value为空，校验不通过
          _checkResult = false
          this.handleRuleTooltip(i, 'value', this.$t('未设置搜索值'))
          break
        }
        // else if (typeof _value === 'string' && _value.length >= this.maxQueryValueLength) {
        //   _checkResult = false
        //   this.handleRuleTooltip(i, 'value', `${this.$t('搜索值长度，不得超过')}${this.maxQueryValueLength}`)
        //   break
        // }
      }
      return _checkResult
    },
    // 处理checkbox选择数据，解构checked
    handleChangeCheckBox(e, index) {
      this.combinationData[index].value = e.checked
    },
    // 处理各个搜索条件，元素上的错误提示
    handleRuleTooltip(index, link, msg = this.$t('规则配置不规范')) {
      const _el = this.$refs[`${link}-${index}`][0]
      this.handleTooltip(_el, msg)
    },
    // 错误提示
    handleTooltip(_el, msg = this.$t('规则配置不规范')) {
      this.$combinationTooltip({
        target: _el,
        coordinate: {
          offsetTop: _el?.offsetTop,
          offsetLeft: _el?.offsetLeft,
          offsetWidth: _el?.offsetWidth,
          offsetHeight: _el?.offsetHeight,
          clientWidth: _el?.clientWidth,
          clientHeight: _el?.clientHeight
        },
        content: msg
      })
    },
    // 序列化，获取单个搜索规则
    getSingleRule(item, i) {
      let { field, operator, value } = item
      const _currentColumn = utils.cloneDeep(this.getCurrentElement(i))
      if (_currentColumn?.field) {
        const _options = _currentColumn?.searchOptions
        if (typeof _options?.customRule === 'function') {
          // 用户自定义规则
          return _options.customRule(value)
        }
        if (typeof _options?.serializeValue === 'function') {
          // 用户自定义，数据序列化
          value = _options.serializeValue(value)
        } else if (_options?.dateFormat) {
          // 用户自定义dateFormat
          if (
            _options?.elementType === 'date' ||
            _options?.elementType === 'time' ||
            _options?.elementType === 'datetime'
          ) {
            // type为date、datetime，按照用户自定义的dateFormat，序列化数据
            value = this.formatDate(value, _options.dateFormat)
          }
        } else {
          if (_options?.elementType === 'date') {
            // 默认的date序列化
            value = this.formatDate(value)
          }
          if (_options?.elementType === 'datetime' || _options?.elementType === 'time') {
            // 默认的datetime、time序列化
            value = this.formatDate(value, 'YYYY-mm-dd HH:MM:SS')
          }
        }
        return {
          label: _currentColumn?.headerText,
          field: _options?.renameField ? _options?.renameField : field,
          type: _options.type,
          operator,
          value
        }
      } else {
        return null
      }
    },
    // 序列化confirm中需要的规则
    serializeRules() {
      const _combinationData = utils.cloneDeep(this.combinationData)
      const rules = []
      _combinationData.forEach((item, i) => {
        const _rule = this.getSingleRule(item, i)
        if (_rule) {
          rules.push(_rule)
        }
      })
      return {
        condition: this.conditionValue,
        rules
      }
    },
    formatDate(date, fmt = 'Y-m-d') {
      if (!date) {
        return
      }
      date = new Date(date)
      let ret
      const opt = {
        'Y+': date.getFullYear().toString(),
        'm+': (date.getMonth() + 1).toString(),
        'd+': date.getDate().toString(),
        'H+': date.getHours().toString(),
        'M+': date.getMinutes().toString(),
        'S+': date.getSeconds().toString()
      }
      for (const k in opt) {
        ret = new RegExp('(' + k + ')').exec(fmt)
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0')
          )
        }
      }
      return fmt
    },
    resetCombination() {
      this.combinationData = [
        {
          field: '',
          operator: '',
          value: ''
        }
      ]
      // 点击重置按钮，执行
      this.$emit('cancelColumnsDialog')
      this.$emit('handleQuickReset')
    },
    // 抛出方法，供外部调用
    getSearchRules() {
      return {
        form: this.combinationData,
        rules: this.serializeRules()
      }
    },
    cancel() {
      this.$emit('cancelColumnsDialog')
    },
    confirm() {
      const _combinationData = utils.cloneDeep(this.combinationData)
      const _checkDataSource = this.checkCombinationData(_combinationData)
      if (_checkDataSource) {
        this.$emit('handleQuickSearch', this.getSearchRules())
        this.$emit('confirmColumnsDialog')
      }
    }
  }
}
</script>
