<template>
  <div class="text-editor-container">
    <mt-rich-text-editor
      :id="'editorRef-' + configIndex"
      :ref="'editorRef-' + configIndex"
      :height="600"
      :css-class="'grid-text-editor'"
      v-model="editorConfig.value"
      :readonly="editorReadOnly"
    >
    </mt-rich-text-editor>
  </div>
</template>

<script>
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
export default {
  name: 'RichTextEditor',
  props: {
    configIndex: {
      type: Number,
      default: 0
    },
    editorConfig: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  components: {
    MtRichTextEditor
  },
  computed: {
    editorReadOnly() {
      return Object.prototype.hasOwnProperty.call(this.editorConfig, 'readonly')
        ? this.editorConfig.readonly
        : false
    }
  },
  methods: {}
}
</script>

<style lang="scss">
.text-editor-container {
  display: inline-block;
  flex: 1;
  margin-bottom: 20px;

  .mt-rich-text-editor {
    height: 100%;
  }
  .grid-text-editor {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #e8e8e8 !important;
    border-radius: 8px 8px 0 0 !important;
    .e-toolbar-wrapper {
      height: 42px !important;
    }
    .e-extended-toolbar {
      background: #ffffff;
      border-radius: 8px 8px 0 0 !important;
      .e-toolbar-items {
        border-radius: 8px 0 0 0 !important;
        background: #ffffff;
        .e-tbar-btn {
          background: #ffffff;
        }
      }
      .e-expended-nav {
        border-radius: 0 8px 0 0 !important;
        background: #ffffff;
      }
    }
    .e-rte-content {
      height: auto;
      margin-top: 0px;
      flex: 1;
    }
  }
}
</style>
