<template>
  <div class="cell-dropdown-container">
    <mt-select
      v-model="data[data.editingParams.fields.value]"
      :data-source="data.editingParams.dataSource"
      :fields="data.editingParams.fields"
      :change="handleSelectChange"
      :no-records-template="$t('无选择数据')"
    ></mt-select>
  </div>
</template>
<script>
import MtSelect from '@mtech-ui/select'
export default {
  name: 'ColumnDropdownTemplate',
  components: {
    MtSelect
  },
  inject: ['parentVm'],
  data() {
    return {
      data: {}
    }
  },
  mounted() {},
  computed: {
    serializeCellId() {
      return `cell-${this.data.templateField}-${this.data.index}`
    }
  },
  methods: {
    handleSelectChange(e) {}
  }
}
</script>
<style lang="scss" scoped></style>
