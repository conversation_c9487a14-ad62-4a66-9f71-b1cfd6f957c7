<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="formData" :rules="dialogRules" autocomplete="off">
        <mt-form-item prop="name" :label="$t('模板名')">
          <mt-input
            maxlength="16"
            v-model="formData.name"
            :show-clear-button="true"
            :disabled="false"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import MtDialog from '@mtech-ui/dialog'
import { API } from '@mtech-common/http'
const saveUserMemory = '/lowcodeWeb/tenant/user-memory/save'
export default {
  components: {
    MtDialog
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: this.$t('保存搜索模板'),
      formData: { name: '' },
      dialogRules: {
        name: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveSearchTemplate,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    saveSearchTemplate() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.dialogData.templateNames.includes(this.formData.name)) {
            this.$toast({ content: this.$t('模板名不能重复'), type: 'warning' })
            return
          }
          let _gridMemory = JSON.parse(sessionStorage.getItem(this.dialogData.gridId))
          if (_gridMemory?.searchTemplates?.length > 0) {
            _gridMemory.searchTemplates.push({
              templateName: this.formData.name,
              searchRule: this.dialogData.searchRules
            })
          } else {
            _gridMemory.searchTemplates = [
              { templateName: this.formData.name, searchRule: this.dialogData.searchRules }
            ]
          }
          sessionStorage.setItem(this.dialogData.gridId, JSON.stringify(_gridMemory))
          API.post(saveUserMemory, {
            gridId: this.dialogData.gridId,
            gridMemory: _gridMemory
          }).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('confirmSuccess')
              this.handleClose()
            }
          })
        }
      })
    }
  }
}
</script>
