<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <!-- <mt-select
        ref="supplierRef"
        v-model="staffs"
        :disabled="disabled"
        float-label-type="Never"
        :data-source="dataSourceList"
        :fields="{ text: 'employeeName', value: 'externalCode' }"
        :placeholder="`点击下拉查看已选人员`"
      ></mt-select> -->
      <vxe-pulldown ref="pulldown" destroy-on-close>
        <template #default>
          <vxe-input
            v-model="staffs"
            placeholder="点击搜索人员"
            suffix-icon="vxe-icon-arrow-down"
            @suffix-click="togglePanel('pulldown')"
            readonly
          ></vxe-input>
        </template>
        <template #dropdown>
          <vxe-list class="my-dropdown2" :data="dataSourceList" auto-resize>
            <template #default="{ items }">
              <div class="list-item2" v-for="item in items" :key="item.externalCode">
                <span>{{ item.externalCode + '-' + item.employeeName }}</span>
              </div>
            </template>
          </vxe-list>
        </template>
      </vxe-pulldown>
      <!-- <mt-icon
        style="width: 20px; right: 18px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="!disabled"
      ></mt-icon> -->
      <mt-icon
        v-if="!disabled"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Input, Pulldown, List } from 'vxe-table'
Vue.use(Input).use(Pulldown).use(List)

export default {
  props: {
    defaultValue: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataSourceList: [], // 下拉列表
      staffs: '',
      isShowDialog: false
    }
  },
  mounted() {
    console.log('mountedmounted', this.disabled)
  },
  computed: {
    // title() {
    //   // 设置弹窗title
    //   let columnData = this.config.pageConfig[0]['grid']['columnData']
    //   let column = columnData.filter((i) => i.field === this.config.text)
    //   return column[0]['headerText']
    // }
  },
  watch: {
    defaultValue: {
      immediate: true,
      handler(value) {
        console.log('valuevalue', value)
        if (value) {
          this.dataSourceList = value.map((item) => {
            return { externalCode: item.split('-')[0], employeeName: item.split('-')[1] }
          })
          this.staffs = value
        } else {
          this.staffs = ''
          this.dataSourceList = []
        }
      }
    }
  },
  methods: {
    togglePanel(ref) {
      this.$refs[ref].togglePanel()
    },
    handleClear() {
      const obj = {}
      obj[this.config.text] = ''
      obj[this.config.value] = ''
      this.$emit('change', obj)
      this.text = ''
    },
    showDialog() {
      this.$dialog({
        modal: () => import('./coms/selectorDialog.vue'),
        data: {
          title: '地址簿',
          curStaffs: this.dataSourceList
        },
        success: (data) => {
          // 设置下拉框显示的值
          data.forEach((e) => {
            e.employeeName = e.externalCode + '-' + e.employeeName
          })
          this.dataSourceList = data
          this.staffs = data
          console.log('changechangechange', data)
          this.$emit(
            'change',
            data.map((item) => item.employeeName)
          )
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-input .vxe-input--suffix {
  right: 2.2em;
}
::v-deep .vxe-input.is--suffix .vxe-input--inner {
  padding-right: 2.8em;
}
.list-item2 {
  padding-left: 10px;
  line-height: 33px;
}
.list-item2:hover {
  background-color: #eeeeee;
}
.my-dropdown2 {
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
::v-deep .vxe-input--inner {
  width: 100%;
  height: 100%;
  border-radius: 0px;
  outline: 0;
  margin: 0;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  padding: 0 0.6em;
  color: #606266;
  border: 1px solid rgba(0, 0, 0, 0.6);
  border-top: none;
  border-left: none;
  border-right: none;
  background-color: #fff;
  box-shadow: none;
}
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    /deep/ .mt-select {
      .e-input-group-icon,
      .e-ddl-icon,
      .e-search-icon {
        margin-right: 40px;
      }
    }

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 0;
    }
  }
}
</style>
