<template>
  <div class="category-select-box">
    <div class="header-line">
      <!-- <mt-checkbox
        label="全选"
        @change="handleChange"
        v-model="checkboxVal"
        class="item1"
      ></mt-checkbox> -->
      <div class="title">选择人员</div>
      <input
        v-if="dataList.length > 0"
        type="text"
        placeholder="请输入编码或名称模糊查询"
        class="item2"
        @keyup.enter="searchData"
        v-model="searchValueCache"
      />
      <span v-if="dataList.length > 0" @click="searchData" class="search">
        <mt-icon name="icon_input_search" class="close-icon" />
      </span>
      <span v-if="dataList.length > 0" @click="clearAll">
        <mt-icon name="icon_list_refuse" class="close-icon" />
      </span>
    </div>
    <div v-if="!!dataList.length" class="nodata1">
      <vxe-list height="100%" :data="dataList" auto-resize id="data-list-content">
        <template #default="{ items }">
          <div v-for="item in items" :key="item.id">
            <mt-checkbox
              style="float: left"
              @change="(e) => handleChangeItem(e, item)"
              v-model="item.checkboxVal"
            ></mt-checkbox>
            <div class="list">
              <p>{{ item.externalCode }}</p>
              <p>{{ item.employeeName }}</p>
            </div>
          </div>
        </template>
      </vxe-list>
    </div>
    <div v-else class="nodata2">暂无数据</div>

    <div class="footer-line">
      <div class="num">共{{ total || 0 }}条</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tenantId: {
      require: true,
      type: String,
      default: null
    },
    value: {
      require: true,
      type: Array,
      default: () => []
    },
    isUseLocalSelectAll: {
      type: Number,
      default: null
    },
    curStaffs: {
      type: Array,
      default: () => []
    }
  },
  data: () => {
    return {
      selectKeys: [],
      checkboxVal: false,
      searchValue: '',
      searchValueCache: '',
      selectedInfo: [],
      dataList: [],
      total: null
    }
  },
  watch: {
    value: {
      handler(val) {
        this.searchValueCache = ''
        this.selectKeys = val ? [...val] : []
        this.dataList = val ? [...val] : []
        this.total = val.length
        this.changeStatus()
      }
      // deep: true
      // immediate: true
    }
  },
  computed: {},
  mounted() {},
  methods: {
    //校验勾选状态
    changeStatus() {
      let selectViewNumber = 0
      console.log(this.curStaffs, this.dataList, 'selectKeys')
      this.dataList.forEach((item) => {
        if (this.curStaffs.some((c) => c.externalCode === item.externalCode)) {
          item.checkboxVal = true
          selectViewNumber++
        } else {
          item.checkboxVal = false
        }
      })
      if (this.dataList.length > 0 && selectViewNumber === this.dataList.length) {
        this.checkboxVal = true
      } else {
        this.checkboxVal = false
      }
    },
    clearAll() {
      //清空全选
      this.searchValueCache = ''
      this.dataList = this.value
      this.changeStatus()
    },
    searchData() {
      this.searchValue = this.searchValueCache
      console.log('this.searchValueCache', this.searchValueCache, this.dataList)
      const searchValue = this.searchValueCache
      const filteredData = this.dataList.filter((item) => {
        const employeeName = item.employeeName.toLowerCase()
        const searchValueLowerCase = searchValue.toLowerCase()
        return employeeName.includes(searchValueLowerCase)
      })

      const resultArray = filteredData.map((item) => {
        return item
      })
      this.dataList = resultArray
      this.changeStatus()

      console.log('loadDataloadDataloadData', resultArray)
    },
    handleChange(value) {
      console.log(value.checked, '全选的那个')
      if (value.checked) {
        this.dataList.forEach((item) => {
          if (!item.checkboxVal) {
            this.selectKeys.push({ ...item })
          }
        })
      } else {
        this.dataList.forEach((item) => {
          if (item.checkboxVal) {
            this.selectKeys = this.selectKeys.filter((c) => c.categoryCode !== item.categoryCode)
          }
        })
      }
      if (this.isUseLocalSelectAll === 1) {
        value.checked ? (this.allChoose = 1) : (this.allChoose = 0)
        this.selectKeys.forEach((item) => {
          this.$set(item, 'allChoose', 1)
        })
        this.$emit('onlineSelectAllChange', this.allChoose)
      }
      this.checkboxVal = value.checked

      this.changeStatus()
    },
    // 勾选事件
    handleChangeItem(e, item) {
      console.log('handleChangeItemhandleChangeItem', this.curStaffs)
      if (this.selectedInfo.length === 0) {
        this.curStaffs.forEach((tar) => {
          this.selectedInfo.push(tar)
        })
      }
      if (e.checked) {
        item.checkboxVal = e.checked
        this.selectedInfo.push(item)
        console.log('employee-changeemployee-change', this.selectedInfo, item)
        this.$emit('employee-change', this.selectedInfo)
      } else {
        item.checkboxVal = e.checked
        this.selectedInfo = this.selectedInfo.filter((c) => c.externalCode !== item.externalCode)
        this.$emit('employee-change', this.selectedInfo)
      }
      // this.changeStatus()
    },
    loadData() {
      this.changeStatus()
    }
  }
}
</script>

<style scoped lang="scss">
.category-select-box {
  border: 1px solid #e4e4e4;
  color: #292929;
}

.close-icon {
  cursor: pointer;
  margin-top: 8px;

  &:hover {
    color: #00469c;
    zoom: 1.2;
  }
}

.mt-icons {
  cursor: pointer;
}

.header-line {
  display: flex;
  margin-bottom: 5px;
  padding: 2px;
  align-items: center;
  background: #f2f2f2;
  position: relative;
  justify-content: space-between;
  height: 30px;

  .item1 {
    padding-top: 3px;
    margin-right: 5px;
    zoom: 0.9;
  }

  input {
    border: none;
    padding-right: 10px;
  }

  .search {
    position: absolute;
    top: 5px;
    right: 25px;
  }

  .item2 {
    zoom: 0.9;
    margin-right: 5px;
    flex: 1;
    width: 200px;
  }
}

.footer-line {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border: 1px solid #f5f5f5;
  padding: 0 5px;

  .num {
    font-size: 14px;
    color: #292929;
  }
}

.footer-page {
  display: flex;
  align-items: center;
}

.nodata1 {
  height: 340px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 5px;

  .item {
    display: flex;
    align-items: center;
  }

  .list {
    width: 220px;
    margin-left: 15px;
    padding: 3px;

    p {
      font-size: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    p:first-child {
      zoom: 0.8;
    }
  }

  .item:nth-child(2n-1) {
    background: #f2f2f2;
  }
}

.nodata2 {
  height: 340px;
  width: 100%;
  text-align: center;
  padding-top: 40px;
  font-size: 12px;
  color: #666;
}
.title {
  display: flex;
  margin-bottom: 5px;
  padding: 2px;
  background: #f2f2f2;
  height: 30px;
  line-height: 30px;
  font-weight: bold;
  padding-left: 5px;
}
</style>
