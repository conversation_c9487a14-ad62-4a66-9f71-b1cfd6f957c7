<template>
  <mt-dialog ref="dialog" height="610" :buttons="buttons" :header="header" @beforeClose="cancel">
    <!-- 主要信息 -->
    <div class="content">
      <!-- 组织 -->
      <div class="orgList">
        <template>
          <div class="title">选择组织</div>
          <mt-treeView
            v-if="filedsIcon.dataSource.length > 0"
            ref="treeView"
            class="tree-view-container"
            :show-check-box="false"
            @nodeSelecting="orgNodeSelecting"
            :fields="filedsIcon"
          ></mt-treeView>
          <div v-if="filedsIcon.dataSource.length === 0" class="nodata2">暂无数据</div>
        </template>
      </div>
      <!-- 部门 -->
      <div class="comList">
        <div class="title">选择部门</div>
        <mt-treeView
          v-if="fieldsDepartment.dataSource.length > 0"
          ref="comTreeView"
          class="tree-view-container"
          :show-check-box="false"
          @nodeSelecting="comNodeSelecting"
          :fields="fieldsDepartment"
        ></mt-treeView>
        <div v-if="fieldsDepartment.dataSource.length === 0" class="nodata2">暂无数据</div>
      </div>
      <!-- 人员 -->
      <div class="personList">
        <CategoryList
          ref="viewPerson"
          v-model="selectPersonNodes"
          :cur-staffs="selectPersonList"
          @employee-change="employeeChange"
        />
      </div>
    </div>
    <!-- 已选人员统计 -->
    <div class="selectPersonList">
      <div class="title">已选人员</div>
      <div v-for="item in selectPersonList" :key="item.externalCode">
        <span class="every_person">{{ item.externalCode + '-' + item.employeeName }}</span>
        <mt-button
          icon-css="mt-icons mt-icon-icon_Close_2"
          type="text"
          :key="item.externalCode"
          @click="hDelete(item)"
        ></mt-button>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import CategoryList from './CategoryList.vue'

export default {
  components: { CategoryList },
  data() {
    return {
      // 组织
      filedsIcon: {
        dataSource: [],
        id: 'id',
        value: 'id',
        text: 'name',
        child: 'children',
        key: 1
      },
      // 公司
      fieldsDepartment: {
        dataSource: [],
        key: 2,
        value: 'id',
        text: 'name',
        child: 'children'
      },
      // 人员
      selectPersonNodes: [],
      // 已选人员
      selectPersonList: [],
      // 按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {},
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.curStaffs.length > 0) {
      this.selectPersonList = this.modalData.curStaffs
    }
    this.getStatedLimitTree() // 公司组织枚举
  },
  methods: {
    employeeChange(val) {
      console.log('selectPersonNodesselectPersonNodes', val)
      this.selectPersonList = val
    },
    // 获取组织树==公司接口
    async getStatedLimitTree() {
      let query = 'ORG02'
      await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        orgType: 'ORG001ADM' //ORG001ADM TCL用，勿动
      }).then((res) => {
        let filterObj = function (item) {
          if (item.orgLeveLTypeCode == query) return true
          if (Object.prototype.hasOwnProperty.call(item, 'children')) {
            item.children = item.children.filter(function (child) {
              if (child.orgLeveLTypeCode == query) {
                return child.orgLeveLTypeCode == query
              } else if (Object.prototype.hasOwnProperty.call(child, 'children')) {
                return filterObj(child)
              }
            })
            if (item.children.length > 0) {
              return true
            }
          } else {
            return item.orgLeveLTypeCode == query
          }
        }
        let filter = res.data.filter(function (item) {
          return filterObj(item)
        })

        this.filedsIcon.dataSource = cloneDeep(filter)
        this.filedsIcon.key = this.randomString()
      })
    },
    //获取公司
    getCompany(orgId) {
      this.$API.ModuleConfig.qualificationGetCompanyDepartmentTree({ organizationId: orgId })
        .then((res) => {
          if (res.code === 200) {
            this.fieldsDepartment.dataSource = res.data
            this.fieldsDepartment.key = this.randomString()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 获取人员
    getEmployees(deptId) {
      let params = {
        employeeName: '',
        orgId: deptId,
        tenantId: '10000'
      }
      this.$API.ModuleConfig.getOrganizationEmployees(params).then((res) => {
        this.selectPersonNodes = res.data
      })
    },
    // 获取随机数
    randomString(len) {
      len = len || 32
      var $chars =
        'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678' /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
      var maxPos = $chars.length
      var pwd = ''
      for (var i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
      }
      return pwd
    },
    // 组织公司点击事件
    orgNodeSelecting(e) {
      this.getCompany(e.nodeData.id)
    },
    // 部门点击事件
    comNodeSelecting(e) {
      this.getEmployees(e.nodeData.id)
    },
    // 已选人员删除
    hDelete(item) {
      console.log('hDeletehDelete', item, this.selectPersonList)
      this.selectPersonList = this.selectPersonList.filter(
        (tar) => tar.employeeName != item.employeeName || tar.externalCode != item.externalCode
      )
    },
    confirm() {
      console.log('selectPersonListselectPersonList', this.selectPersonList)
      this.$emit('confirm-function', this.selectPersonList)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.nodata2 {
  height: 340px;
  width: 100%;
  text-align: center;
  padding-top: 40px;
  font-size: 12px;
  color: #666;
}
.content {
  margin-top: 10px;
  display: flex;
  justify-content: space-evenly;
  align-items: flex-start;
}
.title {
  display: flex;
  margin-bottom: 5px;
  padding: 2px;
  background: #f2f2f2;
  height: 30px;
  line-height: 30px;
  font-weight: bold;
  padding-left: 5px;
}
.orgList {
  height: 400px;
  width: 30%;
  box-shadow: 1px 4px 5px #999;
  overflow: auto;
}
.comList {
  height: 400px;
  width: 30%;
  box-shadow: 1px 4px 5px #999;
  overflow: auto;
}
.personList {
  height: 400px;
  width: 30%;
  box-shadow: 1px 4px 5px #999;
  overflow: auto;
}
.selectPersonList {
  box-shadow: 1px 4px 5px #999;
  margin: 10px 0 0 0;
  height: 60px;
  display: flex;
  flex-wrap: wrap;
}
</style>
