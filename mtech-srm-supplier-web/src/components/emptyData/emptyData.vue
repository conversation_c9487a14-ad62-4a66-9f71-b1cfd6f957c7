<template functional>
  <div class="empty-wrap">
    <div class="empty-container">
      <img src="../../assets/emptyData.png" />
      <div class="empty-txt">{{ data.attrs.msg || '未获取到数据！' }}</div>
    </div>
  </div>
</template>

<script>
export default {}
</script>

<style lang="scss" scoped>
.empty-wrap {
  position: relative;
  height: 240px;
  margin-top: 15%;
}
.empty-container {
  height: 240px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate3d(-50%, -50%, 0);

  img {
    display: block;
    width: 300px;
    height: 200px;
  }

  .empty-txt {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
    color: #9a9a9a;
  }
}
</style>
