<template>
  <div class="card-box">
    <div :class="['card-title-bar', showBorder && 'title-border']">
      <span class="card-title">{{ title }}</span>
      <div class="card-operator">
        <slot name="operator"></slot>
      </div>
      <div class="tool-bar">
        <div
          v-for="(item, index) in toolbar"
          :key="index"
          class="tool-item"
          @click="toolClick(item)"
        >
          <i
            v-if="item.icon"
            :class="['mt-icons', 'mt-icon-' + item.icon, 'tool-icons', item.cssClass]"
          ></i>
          <span :class="[item.cssClass]">{{ item.title }}</span>
        </div>
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      require: false,
      default: ''
    },
    toolbar: {
      type: Array,
      default: () => []
    },
    showBorder: {
      type: Boolean,
      default: false
    }
  },
  mounted() {},
  methods: {
    toolClick(data) {
      this.$emit('toolClick', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.card-box {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  // min-width: 1180px;
  // overflow-x: scroll;
  .card-title-bar {
    padding: 20px;
    display: flex;
    align-items: center;
    .card-title {
      font-size: 14px;
      color: #292929;
      font-weight: 600;
      position: relative;
      padding-left: 8px;
      &::before {
        content: ' ';
        width: 2px;
        height: 10px;
        background: #00469c;
        border-radius: 1px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
      }
    }
    .card-operator {
      flex: 1;
    }
    .tool-bar {
      .tool-item {
        cursor: pointer;
        display: inline-block;
        margin-right: 20px;
        color: #4f5b6d;
        font-size: 14px;
        &:last-child {
          margin-right: 0;
        }
        .tool-icons {
          margin-right: 4px;
        }
      }
    }
  }
  .title-border {
    border-bottom: 1px solid #e8e8e8;
  }
}
</style>
