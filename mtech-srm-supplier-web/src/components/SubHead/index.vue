<template>
  <span :class="['sub-head', cssClass]">{{ title }}</span>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      require: false,
      default: ''
    },
    cssClass: {
      type: String,
      require: false,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.sub-head {
  display: inline-block;
  font-size: 14px;
  color: #292929;
  font-weight: 600;
  position: relative;
  padding-left: 8px;
  &::before {
    content: ' ';
    width: 2px;
    height: 10px;
    background: #00469c;
    border-radius: 1px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
  }
}
</style>
