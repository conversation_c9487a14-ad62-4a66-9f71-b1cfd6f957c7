<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true
    }
  },
  mounted() {
    var that = this
    this.fieldName = this.data.column.field
    this.$bus.$off(`${this.fieldName}Change`) // 为了解决textWrapper拿不到的问题
    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      console.log('￥emit的监听到了被展示的数据------', this.fieldName, txt)
      // that.data[_field] = txt;
      that.$set(that.data, this.fieldName, txt)
    })
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return
  },
  methods: {
    handleClear() {
      this.$set(this.data, 'itemCode', null)
      this.$parent.$parent.$parent.$parent.$parent.$parent.assessIndexSelectedChanged({
        itemInfo: {
          itemCode: null,
          itemId: null,
          itemName: null
        }
      })
      this.$bus.$emit('itemNameChange', null)
    },
    showDialog() {
      if (
        sessionStorage.getItem('organizationId') == '' ||
        sessionStorage.getItem('organizationId') == 'undefined' ||
        sessionStorage.getItem('organizationId') == null
      ) {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择物料'),
          organizationId: sessionStorage.getItem('organizationId')
        },
        success: (data) => {
          console.log('emit-itemCodeChange', data[0], this)
          this.$set(this.data, 'itemCode', data[0]['itemCode'])
          this.$parent.$parent.$parent.$parent.$parent.$parent.assessIndexSelectedChanged({
            itemInfo: {
              itemCode: data[0].itemCode,
              itemId: data[0].id,
              itemName: data[0].itemName
            }
          })
          this.$bus.$emit('itemNameChange', data[0].itemName)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
