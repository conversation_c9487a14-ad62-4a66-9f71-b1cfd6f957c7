<template>
  <div :class="classes">
    <slot></slot>
  </div>
</template>
<script>
const prefixCls = 'ivu-collapse'

export default {
  name: 'Collapse',
  props: {
    accordion: {
      type: Boolean,
      default: false
    },
    value: {
      type: [Array, String],
      default: () => {
        return []
      }
    },
    hasArr: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentValue: this.value
    }
  },
  computed: {
    classes() {
      return [`${prefixCls}`, 'collapse-box']
    }
  },
  mounted() {
    this.setActive()
  },
  methods: {
    setActive() {
      const activeKey = this.getActiveKey()
      this.$nextTick(() => {
        this.$children.forEach((child, index) => {
          const name = child.name || index.toString()

          child.isActive = activeKey.indexOf(name) > -1
          child.index = index
        })
      })
    },
    getActiveKey() {
      let activeKey = this.currentValue || []
      const accordion = this.accordion

      if (!Array.isArray(activeKey)) {
        activeKey = [activeKey]
      }

      if (accordion && activeKey.length > 1) {
        activeKey = [activeKey[0]]
      }

      for (let i = 0; i < activeKey.length; i++) {
        activeKey[i] = activeKey[i].toString()
      }

      return activeKey
    },
    toggle(data) {
      const name = data.name.toString()
      let newActiveKey = []

      if (this.accordion) {
        if (!data.isActive) {
          // newActiveKey.push(name);
        }
        newActiveKey = name
      } else {
        let activeKey = this.getActiveKey()
        const nameIndex = activeKey.indexOf(name)

        if (data.isActive) {
          if (nameIndex > -1) {
            activeKey.splice(nameIndex, 1)
          }
        } else {
          if (nameIndex < 0) {
            activeKey.push(name)
          }
        }

        newActiveKey = activeKey
      }

      this.currentValue = newActiveKey
      this.$emit('input', newActiveKey)
      this.$emit('on-change', newActiveKey)
    }
  },
  watch: {
    value(val) {
      this.currentValue = val
    },
    currentValue() {
      this.setActive()
    }
  }
}
</script>

<style lang="scss">
.collapse-transition {
  -webkit-transition: 0.2s height ease-in-out, 0.2s padding-top ease-in-out,
    0.2s padding-bottom ease-in-out;
  transition: 0.2s height ease-in-out, 0.2s padding-top ease-in-out, 0.2s padding-bottom ease-in-out;
}
.ivu-collapse {
  background-color: #f7f7f7;
  border-radius: 3px;
  border: 1px solid #dcdee2;
}
.ivu-collapse-simple {
  border-left: none;
  border-right: none;
  background-color: #fff;
  border-radius: 0;
}
.ivu-collapse > .ivu-collapse-item {
  border-top: 1px solid #dcdee2;
}
.ivu-collapse > .ivu-collapse-item:first-child {
  border-top: 0;
}
.ivu-collapse > .ivu-collapse-item > .ivu-collapse-header {
  min-height: 38px;
  padding-left: 20px;
  background: #fff;
  color: #666;
  cursor: pointer;
  position: relative;
  border-bottom: 1px solid transparent;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.ivu-collapse > .ivu-collapse-item > .ivu-collapse-header > i {
  -webkit-transition: -webkit-transform 0.2s ease-in-out;
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  margin-right: 14px;
}
.ivu-collapse > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header {
  border-bottom: 1px solid #dcdee2;
  background: #e8e8e8;
}
.ivu-collapse-simple > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header {
  border-bottom: 1px solid transparent;
}
.ivu-collapse > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header > i {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.ivu-collapse-content {
  color: #515a6e;
  padding: 0 16px;
  background-color: #fff;
}
.ivu-collapse-content > .ivu-collapse-content-box {
  padding: 0;
}
.ivu-collapse-simple > .ivu-collapse-item > .ivu-collapse-content > .ivu-collapse-content-box {
  padding-top: 0;
}
.ivu-collapse-item:last-child > .ivu-collapse-content {
  border-radius: 0 0 3px 3px;
}
.ivu-collapse {
  .arr-icon {
    display: flex;
    align-items: center;
    i {
      font-size: 12px;
      color: #9baac1;
    }
  }
}
</style>
