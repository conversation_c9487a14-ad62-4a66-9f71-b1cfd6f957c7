<template>
  <div :class="itemClasses">
    <div :class="headerClasses" @click="toggle">
      <template v-if="hasArr">
        <i class="mt-icons mt-icon-MT_Right_Arrow"></i>
      </template>
      <slot name="header"></slot>
    </div>
    <collapse-transition v-if="mounted">
      <div :class="contentClasses" v-show="isActive">
        <div :class="boxClasses"><slot name="content"></slot></div>
      </div>
    </collapse-transition>
  </div>
</template>
<script>
import CollapseTransition from './collapse-transition'
const prefixCls = 'ivu-collapse'

export default {
  name: 'Panel',
  components: { CollapseTransition },
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      index: 0, // use index for default when name is null
      isActive: false,
      mounted: false
    }
  },
  computed: {
    hasArr() {
      return this.$parent.hasArr
    },
    itemClasses() {
      return [
        `${prefixCls}-item`,
        {
          [`${prefixCls}-item-active`]: this.isActive
        }
      ]
    },
    headerClasses() {
      return [
        `${prefixCls}-header `,
        {
          'arr-icon': this.$parent.hasArr
        }
      ]
    },
    contentClasses() {
      return `${prefixCls}-content`
    },
    boxClasses() {
      return `${prefixCls}-content-box`
    }
  },
  methods: {
    toggle() {
      this.$parent.toggle({
        name: this.name || this.index,
        isActive: this.isActive
      })
    }
  },
  mounted() {
    this.mounted = true
  }
}
</script>
