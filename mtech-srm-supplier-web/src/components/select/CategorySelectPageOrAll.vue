<template>
  <div class="category-select-box">
    <div class="header-line">
      <mt-checkbox
        label="品类全选"
        @change="handleChange"
        v-model="checkboxVal"
        class="item1"
      ></mt-checkbox>
      <input
        type="text"
        placeholder="请输入编码或名称模糊查询"
        class="item2"
        @keyup.enter="searchData"
        v-model="searchValueCache"
      />
      <span @click="searchData" class="search">
        <mt-icon name="icon_input_search" class="close-icon" />
      </span>
      <span @click="clearAll">
        <mt-icon name="icon_list_refuse" class="close-icon" />
      </span>
    </div>
    <div v-if="!!dataList.length" class="nodata1">
      <div v-for="(item, index) in dataList" :key="item.id" class="item">
        <mt-checkbox
          @change="(e) => handleChangeItem(e, item, index)"
          v-model="item.checkboxVal"
        ></mt-checkbox>
        <div class="list">
          <p>{{ item.categoryCode }}</p>
          <p>{{ item.categoryName }}</p>
        </div>
      </div>
    </div>
    <div v-else class="nodata2">该搜索条件下暂无数据</div>

    <div class="footer-line">
      <div class="num">共{{ total }}条</div>
      <div class="footer-page">
        <span @click="prePage">
          <mt-icon name="icon_arrow_left" />
        </span>
        <input
          type="number"
          min="0"
          :max="pageNumber"
          :showClearButton="false"
          v-model="page.current"
          @change="changeData"
          width="60"
        />/{{ pageNumber }}
        <span @click="nextPage">
          <mt-icon name="icon_arrow_right" />
        </span>
      </div>
    </div>
  </div>
</template>

<script>
/*
tenantId：公司的id
selectCategoryNodes:当前已经选择的节点
disabledCategorNodes:禁选的数据  //todo
*/
export default {
  props: {
    tenantId: {
      type: String,
      require: true,
      default: null
    },
    value: {
      type: String,
      require: true,
      default: null
    },
    isUseLocalSelectAll: {
      type: Number,
      default: null
    }
  },
  data: () => {
    return {
      allChoose: null, // 是否使用远程全选
      selectKeys: [],
      checkboxVal: false,
      searchValue: '',
      searchValueCache: '',
      dataList: [],
      total: 0,
      page: {
        current: 1,
        size: 10
      }
    }
  },
  computed: {
    pageNumber: function () {
      return Math.ceil(this.total / this.page.size)
    }
  },
  mounted() {
    this.selectKeys = this.value ? [...this.value] : []
    this.loadData()
  },
  methods: {
    getAllCheckedNodes() {
      const arr = this.selectKeys.map((item) => item.id)
      return arr
    },
    changeStatus() {
      //校验勾选状态
      let selectViewNumber = 0
      console.log(this.selectKeys, this.dataList, 'selectKeys')
      this.dataList.forEach((item) => {
        if (this.selectKeys.some((c) => c.categoryCode === item.categoryCode)) {
          item.checkboxVal = true
          selectViewNumber++
        } else {
          item.checkboxVal = false
        }
      })
      // this.dataList = JSON.parse(JSON.stringify(this.dataList))
      this.$forceUpdate()
      if (this.dataList.length > 0 && selectViewNumber === this.dataList.length) {
        this.checkboxVal = true
      } else {
        this.checkboxVal = false
      }
      this.$emit('input', this.selectKeys)
      this.$emit('change', this.selectKeys)
    },
    clearAll() {
      //清空全选
      this.selectKeys = []
      this.changeStatus()
    },
    searchData() {
      this.searchValue = this.searchValueCache
      this.page.current = 1
      this.loadData()
    },
    prePage() {
      if (this.page.current <= 1) {
        return
      }
      this.page.current--
      this.loadData()
    },
    nextPage() {
      if (this.page.current >= this.pageNumber) {
        return
      }
      this.page.current++
      this.loadData()
    },
    handleChange(value) {
      console.log(value.checked, '全选的那个')
      if (value.checked) {
        this.dataList.forEach((item) => {
          if (!item.checkboxVal) {
            this.selectKeys.push({ ...item })
          }
        })
      } else {
        this.dataList.forEach((item) => {
          if (item.checkboxVal) {
            this.selectKeys = this.selectKeys.filter((c) => c.categoryCode !== item.categoryCode)
          }
        })
      }
      if (this.isUseLocalSelectAll === 1) {
        value.checked ? (this.allChoose = 1) : (this.allChoose = 0)
        this.selectKeys.forEach((item) => {
          this.$set(item, 'allChoose', 1)
        })
        this.$emit('onlineSelectAllChange', this.allChoose)
      }
      this.checkboxVal = value.checked
      this.changeStatus()
    },
    handleChangeItem(e, item) {
      if (e.checked) {
        item.checkboxVal = e.checked
        this.selectKeys.push({ ...item })
      } else {
        item.checkboxVal = e.checked
        this.selectKeys = this.selectKeys.filter((c) => c.categoryCode !== item.categoryCode)
      }
      this.changeStatus()
    },
    changeData(e) {
      const num = Number(e.target.value)
      console.log(num, '=====')
      if (!num) return
      if (num <= 1) {
        this.page.current = 1
      } else if (num >= this.pageNumber) {
        this.page.current = this.pageNumber
      } else {
        this.page.current = num
      }
      this.loadData()
    },
    loadData() {
      if (!this.tenantId) {
        return
      }
      let params = {
        page: this.page,
        pageFlag: false,
        condition: 'and',
        rules: [
          {
            field: 'statusId',
            type: 'int',
            operator: 'equal',
            value: 1
          },
          {
            field: 'tree_level',
            operator: 'equal',
            type: 'int',
            value: 1
          },
          {
            condition: 'and',
            rules: [
              // {
              //     "field": "organizationId",
              //     "type": "int",
              //     "operator": "equal",
              //     "value": this.tenantId
              // },
              {
                condition: 'and',
                rules: [
                  {
                    condition: 'or',
                    field: 'categoryCode',
                    type: 'string',
                    operator: 'contains',
                    value: this.searchValue
                  },
                  {
                    condition: 'or',
                    field: 'categoryName',
                    type: 'string',
                    operator: 'contains',
                    value: this.searchValue
                  }
                ]
              }
            ]
          }
        ]
      }
      this.$API.supplierInvitation.getCategoryListAll(params).then((res) => {
        this.dataList = res.data.records.map((item) => {
          item.name = item.categoryName
          return item
        })
        this.total = res.data.total
        this.changeStatus()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.category-select-box {
  border: 1px solid #e4e4e4;
  color: #292929;
}

.close-icon {
  cursor: pointer;
  margin-top: 8px;

  &:hover {
    color: #00469c;
    zoom: 1.2;
  }
}

.mt-icons {
  cursor: pointer;
}

.header-line {
  display: flex;
  margin-bottom: 5px;
  padding: 2px;
  align-items: center;
  background: #f2f2f2;
  position: relative;
  justify-content: space-between;
  height: 30px;

  .item1 {
    padding-top: 3px;
    margin-right: 5px;
    zoom: 0.9;
  }

  input {
    border: none;
    padding-right: 10px;
  }

  .search {
    position: absolute;
    top: 5px;
    right: 25px;
  }

  .item2 {
    zoom: 0.9;
    margin-right: 5px;
    flex: 1;
    width: 200px;
  }
}

.footer-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #f5f5f5;
  padding: 0 5px;

  .num {
    font-size: 14px;
    color: #292929;
  }
}

.footer-page {
  display: flex;
  align-items: center;
}

.nodata1 {
  min-height: 330px;
  overflow-y: hidden;
  overflow-x: hidden;
  padding: 0 5px;

  .item {
    display: flex;
    align-items: center;
  }

  .list {
    width: 220px;
    margin-left: 15px;
    padding: 3px;

    p {
      font-size: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    p:first-child {
      zoom: 0.8;
    }
  }

  .item:nth-child(2n-1) {
    background: #f2f2f2;
  }
}

.nodata2 {
  min-height: 250px;
  width: 100%;
  text-align: center;
  padding-top: 40px;
  font-size: 12px;
  color: #666;
}
</style>
