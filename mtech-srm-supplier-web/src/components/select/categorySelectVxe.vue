<template>
  <div>
    <ScTable
      ref="xTable"
      :columns="columns"
      :table-data="tableData"
      height="360"
      :loading="loading"
      :loading-config="{ icon: 'vxe-icon-indicator roll', text: '正在拼命加载中...' }"
      :row-config="{ isCurrent: true, keyField: 'id' }"
      :checkbox-config="{
        checkRowKeys: selectRowsId,
        reserve: true,
        trigger: 'row'
      }"
      :tree-config="null"
      :scroll-y="{ gt: 30, oSize: 10 }"
      @checkbox-all="selectAllEvent"
      @checkbox-change="selectChangeEvent"
    >
    </ScTable>
    <div style="margin-top: 20px">
      <span>共 {{ total }} 项 </span>
      <span>已 选 择 {{ quantity }} 项 </span>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import ScTable from '@/components/ScTable/src/index'

export default {
  components: {
    ScTable
  },
  props: {
    tenantId: {
      type: String,
      default: null
    },
    value: {
      require: true,
      type: String,
      default: null
    },
    isUseLocalSelectAll: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      quantity: 0,
      loading: false,
      total: 0,
      selectKeys: [],
      columns: [
        { type: 'checkbox', title: 'ALL', width: 80 },
        {
          field: 'categoryCode',
          title: i18n.t('品类编码'),
          width: 80
        },
        {
          field: 'categoryName',
          title: i18n.t('品类名称')
        }
      ],
      searchValue: '',
      tableData: [],

      page: {
        current: 1,
        size: 1500
      }
    }
  },
  computed: {
    selectRowsId() {
      if (!this.value) return []
      return this.value.map((item) => item.id)
    }
  },
  mounted() {
    console.log('初始化渲染数据', this.value)
    this.selectKeys = this.value ? [...this.value] : []
    this.loadData()
  },
  methods: {
    // 获取所有的节点
    getAllCheckedNodes() {
      const arr = this.selectKeys.map((item) => item.id)
      return arr
    },
    // 初始化渲染数据
    loadData() {
      if (!this.tenantId) {
        return
      }
      this.loading = true
      let params = {
        page: this.page,
        pageFlag: false,
        condition: 'and',
        rules: [
          {
            field: 'statusId',
            type: 'int',
            operator: 'equal',
            value: 1
          },
          {
            field: 'tree_level',
            operator: 'equal',
            type: 'int',
            value: 1
          },
          {
            condition: 'and',
            rules: [
              {
                condition: 'and',
                rules: [
                  {
                    condition: 'or',
                    field: 'categoryCode',
                    type: 'string',
                    operator: 'contains',
                    value: this.searchValue
                  },
                  {
                    condition: 'or',
                    field: 'categoryName',
                    type: 'string',
                    operator: 'contains',
                    value: this.searchValue
                  }
                ]
              }
            ]
          }
        ]
      }
      this.$API.supplierInvitation.getCategoryListAll(params).then((res) => {
        this.tableData = res.data.records.map((item) => {
          item.name = item.categoryName
          return item
        })
        this.total = res.data.total
        this.loading = false
        this.$emit('input', this.selectKeys)
        this.$emit('change', this.selectKeys)
      })
    },
    selectAllEvent(e) {
      this.selectKeys = e.records
      if (e.checked) {
        this.selectKeys = e.records
      } else {
        this.selectKeys = []
      }
      this.quantity = e.records.length
      this.$emit('change', this.selectKeys)
    },
    selectChangeEvent(e) {
      if (e.checked) {
        this.selectKeys.push({ ...e.row })
      } else {
        this.selectKeys = this.selectKeys.filter((c) => c.categoryCode !== e.row.categoryCode)
      }
      this.quantity = e.records.length
      this.$emit('change', this.selectKeys)
    }
  }
}
</script>

<style></style>
