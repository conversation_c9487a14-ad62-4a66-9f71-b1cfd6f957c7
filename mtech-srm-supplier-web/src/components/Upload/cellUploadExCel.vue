<template>
  <div class="uploader">
    <div class="to-upload" v-if="!isView && (!isSingleFile || (isSingleFile && files.length == 0))">
      <input type="file" class="upload-input" @change="chooseFiles" />
      <div class="upload-box">
        <mt-icon class="plus-icon" name="icon_Close_1"></mt-icon>
        <div class="right-state">
          <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
          <div class="warn-text">
            {{ $t('文件最大不可超过50M， 文件格式仅支持（.xls .xlsx）') }}
          </div>
        </div>
      </div>
    </div>

    <!-- 已上传 -->
    <div v-for="(item, index) in files" class="has-file" :key="index">
      <div class="left-info">
        <div class="file-box">
          <div class="file-info">
            <div class="file-name">{{ item.fileName }}</div>
            <div class="file-size">{{ item.fileSize | byteToKB }}KB</div>
          </div>
        </div>
      </div>
      <mt-icon
        v-if="!isView"
        name="icon_Close_1"
        class="close-icon"
        @click.native="handleRemove(item)"
      ></mt-icon>
    </div>

    <!-- 上传中 -->
    <div v-for="(item, index) in uploadFileList" class="has-file" :key="index">
      <div class="left-info">
        <div class="file-box">
          <div class="file-info">
            <div class="file-name">{{ item.name }}</div>
            <div class="file-size">{{ item.size | byteToKB }}KB</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    isView: {
      // 是只查看的状态，故没有上传和删除
      type: Boolean,
      default: false
    },
    viewFileData: {
      // 查看状态下传入文件数据
      type: Array,
      default: () => []
    },
    isSingleFile: {
      type: Boolean,
      default: false
    },
    requestUrls: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      uploadFileList: [], // 上传状态显示用数据
      files: [], // 上传后得到的数据
      progressValue: 0 // 上传进度
    }
  },
  watch: {
    viewFileData: {
      handler(value) {
        this.files = value
      },
      immediate: true
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100
  },
  methods: {
    init() {
      this.files = []
    },
    chooseFiles(event) {
      const _file = event.target.files[0]
      const params = {
        type: 'array',
        limit: 50 * 1024 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (!_file) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      let _data = new FormData()
      let isOutOfRange = false
      let _pramsFile = this.requestUrls.file ? this.requestUrls.file : 'file'
      _data.append(_pramsFile, _file)
      if (_file.size > params.limit) {
        isOutOfRange = true
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }
      if (
        _file.name.indexOf('.xls') != _file.name.length - 4 &&
        _file.name.indexOf('.xlsx') != _file.name.length - 5
      ) {
        this.$toast({
          content: this.$t('文件格式仅支持（.xls .xlsx）')
        })
        return
      }
      this.files = [{ fileName: _file.name, fileSize: _file.size }]
      this.$emit('change', this.files, _data)
    },

    // 移除文件
    handleRemove() {
      this.files = []
      this.$emit('change', this.files, null)
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader {
  padding: 4px;
  position: relative;

  .to-upload {
    height: 70px;
    position: relative;
  }

  .upload-input {
    cursor: pointer;
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
    left: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    .plus-icon {
      transform: rotate(45deg);
      color: #98aac3;
      margin: 0 12px 0 10px;
      opacity: 0.6;
      width: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 24px;
    }
    .right-state {
      .plus-txt {
        width: 200px;
        height: 20px;
        font-size: 20px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(152, 170, 195, 1);
        margin-bottom: 10px;
      }
      .warn-text {
        font-size: 10px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(241, 62, 62, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }
  }

  .has-file {
    height: 70px;
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px dashed rgba(232, 232, 232, 1);
    margin: 10px 0 10px 0;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: inherit;

      .file-box {
        height: inherit;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        overflow: auto;
        img {
          height: inherit;
          width: fit-content;
        }
        .file-info {
          padding-left: 10px;
          width: 100%;
          .file-name {
            color: #292929;
            font-size: 14px;
          }
          .file-size {
            color: #9a9a9a;
            font-size: 12px;
          }
        }
      }
    }

    .close-icon {
      font-size: 14px;
      padding-right: 30px;
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
