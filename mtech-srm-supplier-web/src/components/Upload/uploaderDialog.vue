<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="title"
    :buttons="buttons"
    :open="onOpen"
    :close="init"
    @beforeClose="cancel"
  >
    <div>
      <upload-file
        :is-view="isView"
        :view-file-data="viewFileData"
        :is-single-file="isSingleFile"
        ref="uploader"
        @change="fileChange"
      ></upload-file>
    </div>
  </mt-dialog>
</template>

<script>
import UploadFile from '@/components/NormalEdit/Upload/uploader.vue'
export default {
  components: {
    UploadFile
  },
  data() {
    return {
      title: this.$t('附件上传'),
      isView: false,
      required: true,
      uploadData: [], // 上传完成的文件数据
      viewFileData: [], // 查看状态的数据
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    isSingleFile() {
      let flag = false
      if (this.modalData && this.modalData.isSingleFile) {
        flag = true
      }
      return flag
    },
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    }
  },
  mounted() {
    if (this.modalData && this.modalData.fileData) {
      this.dialogInit(this.modalData)
    }
  },
  methods: {
    init() {
      this.$refs.uploader.init()
    },
    fileChange(data) {
      this.uploadData = data
      this.$emit('change', data)
    },
    /**
     * fileData: 文件信息;
     * isView：是否为查看状态;
     * required：是否必须;
     * title: 标题;
     */
    dialogInit(entryInfo) {
      const { fileData, isView, required, title } = entryInfo
      this.viewFileData = fileData || []
      this.isView = isView || false // 默认 上传状态
      this.required = required != null ? required : true // 默认 必须
      this.title = title || this.$t('附件上传') // 默认 "附件上传"
      console.log('测试：附件上传-2', entryInfo)
      this.$refs.dialog.ejsRef.show()
      this.fileChange(fileData)
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      if (
        !this.isView &&
        this.required &&
        this.uploadData.length == 0 &&
        this.viewFileData.length == 0
      ) {
        this.$toast({ content: this.$t('请上传附件'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', this.uploadData)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
