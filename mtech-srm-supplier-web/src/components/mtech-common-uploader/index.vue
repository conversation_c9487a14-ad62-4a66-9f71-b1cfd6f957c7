<template>
  <div class="mt-common-uploader">
    <div class="mt-common-uploader-main" :style="{ width: width }">
      <mt-input
        v-if="type === 'line'"
        readonly="true"
        v-model="fileValue"
        :placeholder="placeholder"
      >
      </mt-input>
      <CellUpload
        class="cell-uploader"
        v-bind="$attrs"
        v-on="$listeners"
        @setFiles="setFiles"
        :save-url="saveUrl"
        :file-list="value"
      ></CellUpload>
    </div>
  </div>
</template>
<script>
import CellUpload from './components/cellUpload'
export default {
  name: 'MtCommonUploader',
  components: { CellUpload },
  props: {
    value: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },
    placeholder: {
      type: String,
      required: false,
      default: ''
    },
    type: {
      type: String,
      required: false,
      default: 'default'
    },
    saveUrl: {
      type: String,
      required: false,
      default: '/api/file/user/file/uploadPrivate?useType=2'
    },
    width: {
      type: String,
      required: false,
      default: '100%'
    }
  },
  watch: {
    value: {
      handler(val) {
        console.log('value1', val)
        this.fileValue = ''
        this.changeFileValue(val)
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      inputPlace: this.$t('请上传文件'),
      fileValue: ''
    }
  },
  mounted() {
    console.log('attrs', this.$attrs)
  },
  methods: {
    changeFileValue(e) {
      if (e.length === 0) {
        this.fileValue = ''
      } else {
        e.forEach((e, i) => {
          if (i === 0) {
            this.fileValue = e.fileName
          } else {
            this.fileValue = `${this.fileValue}，${e.fileName}`
          }
        })
      }
    },
    setFiles(e) {
      this.changeFileValue(e)
      this.$emit('input', e)
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-common-uploader {
  width: 100%;
  .mt-common-uploader-main {
    position: relative;
    .cell-uploader {
      position: absolute;
      right: 0;
      top: 0;
      height: 30px;
      line-height: 30px;
    }
  }
}
::v-deep .e-input-group.e-control-wrapper .e-input[readonly] {
  padding-right: 140px;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  white-space: nowrap;
}
</style>
