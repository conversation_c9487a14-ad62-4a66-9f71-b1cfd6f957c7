import Vue from 'vue'
import '@digis/component-props-state'
import App from './App.vue'
import Router from 'vue-router'
import routes from './router'
import store from './store'
import API_PROMISE from '@/apis'
import $utils from '@/utils/utils'
import getDictionary from '@/utils/dictionary'
import '@mtech-micro-frontend/vue-cli-plugin-micro/public-path.js'
import { sso } from '@mtech-sso/single-sign-on'
import './mtechUi'
import { setLocal } from '@mtech-ui/base'
// import "./config/api.config";
import '@/main.scss'
import '@/icons'
import emptyBox from '@/components/emptyData/emptyData.vue'

import indexDB from '@digis/internationalization'
export const i18n = indexDB.digisI18n(Vue, 'supplier')
// indexDB.internationlization("en");
import { Input, Icon, Tree, Table, Select, InputNumber } from 'ant-design-vue'
import { Theme } from '@mtech-common/utils'
import '@/directive/debounce.js' // 防抖
import '@/directive/throttle.js' // 节流
import waves from '@/directive/waves'
import Bus from '@/utils/bus.js'
import { baseConfig } from '@mtech-common/http'

Vue.use(waves)
Vue.use(Router)
Vue.prototype.$bus = Bus
Vue.config.productionTip = false
Vue.prototype.$API = API_PROMISE
Vue.prototype.$utils = $utils
Vue.prototype.$store = store

Vue.component('a-input', Input)
Vue.component('a-icon', Icon)
Vue.component('a-tree', Tree)
Vue.component('a-table', Table)
Vue.component('a-select', Select)
const { Option } = Select

Vue.component('a-select-option', Option)
Vue.component('a-input-number', InputNumber)

Vue.component('emptyBox', emptyBox)

baseConfig.setDefault({
  baseURL: '/api'
})
baseConfig.addNotify({
  success: function (msg) {
    // 可以使用$toast组件，msg当前版本下默认为接口response.msg
    Vue.prototype.$toast({
      content: msg,
      type: 'success'
    })
  },
  error: function (msg) {
    console.log('应用层报错：', msg)
    Vue.prototype.$toast({
      content: msg,
      type: 'error'
    })
    Vue.prototype.$store.commit('endLoading')
    Vue.prototype.$hloading()
  }
})

// setLocal("zh-CN");
const internationlization = localStorage.getItem('internationlization')
if (internationlization === 'zh' || internationlization === 'zh-CH') {
  setLocal('zh-CN')
}

// 获取已配置的字典
getDictionary()

let router = null
let instance = null

function theme(props) {
  const { entry, container } = props

  const changeTheme = new Theme()

  const themeName = localStorage.getItem('mt-layout-theme') || 'default'

  changeTheme.add(themeName, container, entry)
}

function render(props = {}) {
  const { container, routerMode = 'hash', defaultPath } = props
  router = new Router({
    routes,
    mode: routerMode
    // base: window.__POWERED_BY_QIANKUN__ ? "/supplier" : "/",
  })

  router.beforeEach((to, from, next) => {
    // if (window.__POWERED_BY_QIANKUN__) {
    //     let _toInfo = JSON.parse(JSON.stringify(to));
    //     _toInfo.path = '/supplier' + _toInfo.path;
    //     Vue.prototype.addVisitedViews(_toInfo)
    // } else {
    //     next();
    // }
    next()
  })
  indexDB.layoutCreatLanguage().finally(() => {
    instance = new Vue({
      router,
      store,
      i18n,
      render: (h) => h(App)
    }).$mount(container ? container.querySelector('#supplierApp') : '#supplierApp')
  })

  if (defaultPath) {
    router.push(defaultPath)
  }
}

if (!window.__POWERED_BY_QIANKUN__) {
  sso()
  render()
  theme({
    entry: location.origin,
    container: document.head
  })
}

function storeTest(props) {
  props.onGlobalStateChange &&
    props.onGlobalStateChange(
      (value, prev) => console.log(`[onGlobalStateChange - ${props.name}]:`, value, prev),
      true
    )
  props.setGlobalState &&
    props.setGlobalState({
      ignore: props.name,
      user: {
        name: props.name
      }
    })
}

//single-spa的生命周期函数
export async function bootstrap({ fns = [] } = {}) {
  Array.isArray(fns) &&
    fns.map((i) => {
      Vue.prototype[i.name] = i
    })
  console.log('%c ', 'color: green;', 'app bootstraped')
}

export async function mount(props) {
  // 在这里挂载vue
  storeTest(props)
  render(props)
  theme(props)
  console.log('%c ', 'color: green;', 'app mount', props)
}

export async function unmount() {
  // 在这里unmount实例的vue
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
  router = null
  console.log('%c ', 'color: green;', 'app unmount')
}
