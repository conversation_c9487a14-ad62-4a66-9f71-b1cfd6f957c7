import Vue from 'vue'
import { baseConfig } from '@mtech-common/http'

baseConfig.setDefault({
  baseURL: '/api'
})

baseConfig.addNotify({
  success: function () {
    // 可以使用$toast组件，msg当前版本下默认为接口response.msg
    // Vue.prototype.$toast({
    //   content: msg,
    //   type: "success",
    // });
  },
  error: function () {
    // Vue.prototype.$toast({
    //   content: msg,
    //   type: "error",
    // });
    Vue.prototype.$store.commit('endLoading')
  }
})
