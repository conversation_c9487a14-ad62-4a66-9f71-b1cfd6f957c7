import { i18n } from '@/main.js'

// BU代码映射关系
export const BU_MAPPING = {
  TV: 'tv',
  KT: 'kt',
  BD: 'bd',
  GF: 'pv',
  TX: 'tx'
}

// BU选项列表
export const BU_OPTIONS = [
  { text: i18n.t('泛智屏'), value: 'TV' },
  { text: i18n.t('空调'), value: 'KT' },
  { text: i18n.t('白电'), value: 'BD' },
  { text: i18n.t('光伏'), value: 'GF' },
  { text: i18n.t('通讯'), value: 'TX' }
]

// 缓存当前BU值
let cachedBu = null

/**
 * 获取当前BU代码（小写）
 * @returns {string} 当前BU代码（小写）
 */
export const getCurrentBu = () => {
  if (cachedBu === null) {
    const currentBu = localStorage.getItem('currentBu')
    cachedBu = BU_MAPPING[currentBu] || ''
  }
  return cachedBu
}

/**
 * 获取当前BU代码（大写）
 * @returns {string} 当前BU代码（大写）
 */
export const getCurrentBuUpper = () => {
  return localStorage.getItem('currentBu') || ''
}

/**
 * 获取BU显示名称
 * @param {string} buCode BU代码
 * @returns {string} BU显示名称
 */
export const getBuDisplayName = (buCode) => {
  return BU_OPTIONS.find(item => item.value === buCode)?.text || buCode
}

/**
 * 清除BU缓存
 */
export const clearBuCache = () => {
  cachedBu = null
}
