import Vue from 'vue'

/**
 * 使用频率较低的组件，在相应模块按需引入，提升系统性能：
 * MtDataGrid、MtRadio、MtToast、MtSwitch、MtQueryBuilder、MtPage
 * MtTreeGrid、MtInfoParser、MtChart、MtProgress、MtSlider
 * MtUploader、MtCheckboxGroup、MtBpmnEditor
 * MtMicroLoading、emptyBox
 **/
import MtDialog from '@mtech-ui/dialog'
import MtButton from '@mtech-ui/button'
import MtForm from '@mtech-ui/form'
import MtRow from '@mtech-ui/row'
import MtCol from '@mtech-ui/col'
import MtFormItem from '@mtech-ui/form-item'
import MtInput from '@mtech-ui/input'
import MtSelect from '@mtech-ui/select'
import MtTabs from '@mtech-ui/tabs'
import MtTooltip from '@mtech-ui/tooltip'
import MtMultiSelect from '@mtech-ui/multi-select'
import MtInputNumber from '@mtech-ui/input-number'
import * as Dialog from '@/components/Dialog'
import * as Toast from '@/components/Toast'
import * as ToolTip from '@/components/Tooltip'
import MtCheckbox from '@mtech-ui/checkbox'
import MtDatePicker from '@mtech-ui/date-picker'
import MtDateTimePicker from '@mtech-ui/date-time-picker'
import MtDateRangePicker from '@mtech-ui/date-range-picker'
import MtRadio from '@mtech-ui/radio'
import MtTreeView from '@mtech-ui/tree-view'
import MtDropDownTree from '@mtech-ui/drop-down-tree'
import MtUploader from '@mtech-ui/uploader'
import MtProgress from '@mtech-ui/progress'
import MtPage from '@mtech-ui/page'
import MtDataGrid from '@mtech-ui/data-grid'
import MtTreeGrid from '@mtech-ui/tree-grid'
import MtTag from '@mtech-ui/tag'
import MtSwitch from '@mtech-ui/switch'

import Parser from '@mtech-form-design/form-parser'
Vue.component('mt-parser', Parser)

import loading from './components/loading'
Vue.use(loading)

import MtToast from '@mtech-ui/toast'
Vue.use(MtToast)

import commonPermission from '@mtech/common-permission'
Vue.use(commonPermission)

import MtIcon from '@mtech-ui/icon'
Vue.use(MtIcon)

Vue.use(MtUploader)
Vue.use(MtProgress)

import MtCommonUploader from '@/components/mtech-common-uploader'

Vue.component('mt-toast', MtToast)
Vue.component('mt-col', MtCol)
Vue.component('mt-row', MtRow)
Vue.component('mt-dialog', MtDialog)
Vue.component('mt-button', MtButton)
Vue.component('mt-form', MtForm)
Vue.component('mt-form-item', MtFormItem)
Vue.component('mt-input', MtInput)
Vue.component('mt-select', MtSelect)
Vue.component('mt-tabs', MtTabs)
Vue.component('mt-tooltip', MtTooltip)
Vue.component('mt-multi-select', MtMultiSelect)
Vue.component('mt-date-picker', MtDatePicker)
Vue.component('mt-date-time-picker', MtDateTimePicker)
Vue.component('mt-date-range-picker', MtDateRangePicker)
Vue.component('mt-radio', MtRadio)
Vue.component('mt-multi-select', MtMultiSelect)
Vue.component('mt-inputNumber', MtInputNumber)
Vue.component('mt-input-number', MtInputNumber)
Vue.component('mt-checkbox', MtCheckbox)
Vue.component('mt-tree-view', MtTreeView)
Vue.component('mt-treeView', MtTreeView)
Vue.component('mt-common-uploader', MtCommonUploader)
Vue.component('mt-toast', MtToast)
Vue.component('mt-uploader', MtUploader)
Vue.component('mt-tag', MtTag)
Vue.component('mt-switch', MtSwitch)

Vue.component('mt-icon', MtIcon)
// 引入
Vue.prototype[Dialog['NAME']] = Dialog['COMPONENT']
Vue.prototype[Toast['NAME']] = Toast['COMPONENT']
Vue.prototype[ToolTip['NAME']] = ToolTip['COMPONENT']

Vue.component('mt-DropDownTree', MtDropDownTree)
Vue.component('mt-drop-downTree', MtDropDownTree)
Vue.component('mt-page', MtPage)
Vue.component('mt-data-grid', MtDataGrid)
Vue.component('mt-DataGrid', MtDataGrid)
Vue.component('mt-dataGrid', MtDataGrid)
Vue.component('mt-tree-grid', MtTreeGrid)

import MtTemplatePage from '@/components/template-page'
Vue.component('mt-template-page', MtTemplatePage)
