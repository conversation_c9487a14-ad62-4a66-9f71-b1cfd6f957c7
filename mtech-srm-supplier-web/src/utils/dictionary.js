import Vue from 'vue'
const codeList = [
  { code: 'PER_GRADE_TYPE', type: 'string' }, // 打分类型
  { code: 'PER_SUPPLIER_TYPE', type: 'string' }, // 供应商维度
  { code: 'PER_APPLICABLE_BUSINESS', type: 'string' }, // 适用业务
  { code: 'PER_CLASS', type: 'string' }, // 类别
  { code: 'PER_REVIEW_TYPE', type: 'string' }, // 现场评审得分，评审类型
  { code: 'thresholdType', type: 'string' }, // 门槛类型
  { code: 'PER_EVALUATION', type: 'string' }, // 评价周期(分层分级)
  { code: 'KTComCode', type: 'string' }, // 空调公司
  { code: 'BDComCode', type: 'string' }, // 合肥公司
  { code: 'EVALUATION-PERIOD', type: 'string' }, // 评价周期
  { code: 'claimType', type: 'string' }, // 评价周期
  { code: 'TVComCode', type: 'string' }, // TV公司
  { code: 'SUPPLIER_ABILITY_TEMPLATE_TYPE', type: 'string' }, // TV公司
  { code: 'IDLE_MATERIAL_PROFIT_FACTORY_MAP', type: 'string' } // 呆料索赔利润中心与工厂对应关系
]
export default function () {
  Vue.prototype.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
    const { code, data } = res
    if (code === 200 && data) {
      // 给所有字典加一个cssClass的空值，避免template-page序列化的映射出现问题
      for (const key in data) {
        const arr = data[key]
        arr.forEach((item) => {
          item.cssClass = ''
        })
      }
      sessionStorage.setItem('supplierDictionary', JSON.stringify(data))
    }
  })
}
