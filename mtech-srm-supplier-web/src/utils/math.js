const isNum = (value) => {
  if (value !== null && value !== '') {
    return !isNaN(value)
  }
  return false
}

const getDecimalCount = (num) => {
  let count

  try {
    count = num.toString().split('.')[1].length
  } catch (e) {
    count = 0
  }

  return count
}

// 加法 - 修复精度
export const decimalAdd = (num1, num2) => {
  if (!isNum(num1) || !isNum(num2)) return

  const count1 = getDecimalCount(num1)
  const count2 = getDecimalCount(num2)
  const max = Math.pow(10, Math.max(count1, count2))

  if (count1 === count2) {
    num1 = (num1 + '').replace('.', '')
    num2 = (num2 + '').replace('.', '')
  } else if (count1 > count2) {
    const fillZero = '0'.repeat(count1 - count2)
    num1 = (num1 + '').replace('.', '')
    num2 = (num2 + '').replace('.', '') + fillZero
  } else {
    const fillZero = '0'.repeat(count2 - count1)
    num1 = (num1 + '').replace('.', '') + fillZero
    num2 = (num2 + '').replace('.', '')
  }

  return (Number(num1) + Number(num2)) / max
}

// 减法-修复精度
// 减法转成加法计算
export const decimalSub = (num1, num2) => {
  if (!isNum(num1) || !isNum(num2)) return

  return decimalAdd(num1, -num2)
}

// 乘法-修复精度
export const decimalMul = (num1, num2) => {
  if (!isNum(num1) || !isNum(num2)) return

  const sum = getDecimalCount(num1) + getDecimalCount(num2)
  num1 = (num1 + '').replace('.', '')
  num2 = (num2 + '').replace('.', '')

  return (Number(num1) * Number(num2)) / Math.pow(10, sum)
}

// 除法 - 修复精度
export const decimalDiv = (num1, num2) => {
  if (!isNum(num1) || !isNum(num2)) return

  const count1 = getDecimalCount(num1)
  const count2 = getDecimalCount(num2)
  const formatNum1 = Number((num1 + '').replace('.', ''))
  const formatNum2 = Number((num2 + '').replace('.', ''))

  if (count2 - count1 < 0) {
    // 由于 Math.pow(10, count2 - count1) 为小数，需要处理精度问题
    return decimalMul(formatNum1 / formatNum2, Math.pow(10, count2 - count1))
  } else {
    return (formatNum1 / formatNum2) * Math.pow(10, count2 - count1)
  }
}
