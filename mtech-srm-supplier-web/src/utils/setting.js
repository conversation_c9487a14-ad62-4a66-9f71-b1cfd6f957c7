export const taskTemplateTypeSetting = {
  0: '供应商准入阶段',
  1: '品类准入阶段',
  2: '品类认证阶段'
}

export const taskTemplateTypeSettingAccess = {
  // 0: "供应商准入阶段",  // 暂时去掉
  1: '品类准入阶段'
}

export const taskTemplateTypeSettingAuthor = {
  2: '品类认证阶段'
}

export const stageAccessType = {
  0: '企业准入',
  1: '品类准入',
  2: '品类认证'
}

export const taskTemplateStatusSetting = {
  1: '启用',
  2: '停用'
}

export const taskTemplateClassifySetting = {
  0: '企业信息调查表',
  1: '资料调查表任务',
  2: '品类调查表任务',
  3: '评分表任务',
  4: '现场考察表任务'
}

export const upgradeRuleTypeSetting = {
  1: '自动晋级',
  2: '手动晋级',
  3: '审批晋级'
}

export const taskOnwerSetting = {
  0: '采方',
  1: '供方'
}

export const taskSharedlift = {
  0: '共享升级',
  1: '共享降级'
}

export const applicationformtype = {
  freeze: '供应商冻结',
  disuse: '供应商淘汰',
  black: '供应商拉黑',
  removeFreeze: '解除供应商冻结',
  removeDisuse: '解除供应商淘汰',
  removeBlack: '解除供应商拉黑'
}

export const taskTemplateaccessStage = {
  stageUpgrade: '手动晋级',
  stageDownGradea: '手动降级',
  freeze: '供应商冻结',
  black: '供应商拉黑',
  disuse: '供应商淘汰',
  removeFreeze: '供应商解除冻结',
  removeBlack: '供应商解除黑名单',
  removeDisuse: '供应商启用',
  grade: '供应商分级',
  shareUpgrade: '供应商共享升级',
  shareDownGrade: '供应商共享降级',
  informationChange: '供应商信息变更'
}

export const taskStatusSetting = {
  1: '启用',
  2: '停用'
}

export const taskStatusSettinge = {
  0: '审批',
  1: '停用'
}

export const settings = {
  0: '是',
  1: '否'
}

export const statsea = {
  10: '待提交',
  20: '待审批',
  30: '已驳回',
  40: '已完成',
  50: '已关闭',
  60: '待处理'
}

export const blackState = {
  10: '处罚中',
  20: '解除中',
  30: '已失效'
}

export const statsePuniment = {
  10: '处罚中',
  20: '解除中',
  30: '已解除'
}
