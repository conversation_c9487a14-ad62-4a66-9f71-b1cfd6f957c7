import Vue from 'vue'
import axios from 'axios'
import { i18n } from '@/main.js'

const utils = {}

//通过selectRef获取数据，并赋值其他字段。常用场景：form提交时，使用下拉框数据的其他字段
utils.assignDataFromRef = (formObject, key, ref, fields) => {
  const assignData = (_select, _form, _fields) => {
    for (let i in _fields) {
      // formField: sourceField
      _form[i] = _select[fields[i]]
    }
  }
  if (formObject[key]) {
    //根据当前选择的key，获取整个key对应的Select对象
    let _select = ref.getDataByValue(formObject[key])
    if (typeof fields === 'string') {
      //只赋值一个字段
      formObject[fields] = _select[fields]
    } else if (Array.isArray(fields)) {
      fields.forEach((e) => {
        if (typeof e === 'string') {
          //formField与sourceField一致时
          formObject[e] = _select[e]
        } else {
          assignData(_select, formObject, e)
        }
      })
    } else {
      assignData(_select, formObject, fields)
    }
  }
}
//通过selectRef获取数据，并赋值其他字段。常用场景：form提交时，使用下拉框数据的其他字段
utils.assignDataFromRefs = (formObject, fields) => {
  if (Array.isArray(fields)) {
    fields.forEach((e) => {
      utils.assignDataFromRef(formObject, e['key'], e['ref'], e['fields'])
    })
  }
}

const newDate = (val) => (isString(val) ? new Date(val.replace(/-/g, '/')) : new Date(val))

// const addDate = (add = 1, date = new Date(), format = 'yyyy-MM-dd') => {
//   let copyDate, newDay

//   if (isString(date) || isNumber(date)) {
//     copyDate = newDate(date)
//   } else {
//     copyDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
//   }

//   newDay = new Date(copyDate.setDate(copyDate.getDate() + add))

//   return {
//     date: newDay,
//     day: formatDate(newDay, format)
//   }
// }

const formatDate = (date = new Date(), format = 'yyyy-MM-dd') => {
  if (isString(date) || isNumber(date)) {
    date = newDate(date)
  }

  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds()
  }
  let w = [
    ['日', '一', '二', '三', '四', '五', '六'],
    ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  ]
  let now = new Date()
  let today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  let start = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  let diff = (start - today) / 86400000
  let text

  switch (diff) {
    case 0:
      text = '今天'
      break
    // case 1:
    //     text = '明天'
    //     break
    // case 2:
    //     text = '后天'
    //     break
    default:
      text = ''
  }

  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }

  if (/(w+)/.test(format)) {
    if (text) {
      format = format.replace(RegExp.$1, text)
    } else {
      format = format.replace(RegExp.$1, w[RegExp.$1.length - 1][date.getDay()])
    }
  }

  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }

  return format
}
utils.formateTime = formatDate
utils.newDate = newDate

const isString = (obj) => Object.prototype.toString.call(obj) === '[object String]'

// const isPainObject = (o) =>
//   o && Object.prototype.toString.call(o) === '[object Object]' && 'isPrototypeOf' in o

const isEmpty = (obj) => {
  /* eslint-disable */
  if (obj == null) return true
  if (Array.isArray(obj) || isString(obj)) return obj.length === 0
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) return false
  }
  return true
}

utils.isEmpty = isEmpty

const isMobile = (text) => {
  let reg = /^(1[3-9])\d{9}$/
  return reg.test(text)
}
utils.isMobile = isMobile

const isEmail = (email) => {
  let reg = /^[\w\+\-]+(\.[\w\+\-]+)*@[a-z\d\-]+(\.[a-z\d\-]+)*\.([a-z]{2,4})$/i
  return reg.test(email)
}
utils.isEmail = isEmail

const numIntToChinese = (str) => {
  str = str + ''
  var len = str.length - 1
  var idxs = [
    '',
    '十',
    '百',
    '千',
    '万',
    '十',
    '百',
    '千',
    '亿',
    '十',
    '百',
    '千',
    '万',
    '十',
    '百',
    '千',
    '亿'
  ]
  var num = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  return str.replace(/([1-9]|0+)/g, function ($, $1, idx, full) {
    var pos = 0
    if ($1[0] != '0') {
      pos = len - idx
      if (idx == 0 && $1[0] == 1 && idxs[len - idx] == '十') {
        return idxs[len - idx]
      }
      return num[$1[0]] + idxs[len - idx]
    } else {
      var left = len - idx
      var right = len - idx + $1.length
      if (Math.floor(right / 4) - Math.floor(left / 4) > 0) {
        pos = left - (left % 4)
      }
      if (pos) {
        return idxs[pos] + num[$1[0]]
      } else if (idx + $1.length >= len) {
        return ''
      } else {
        return num[$1[0]]
      }
    }
  })
}
utils.numIntToChinese = numIntToChinese

let timeid = null
utils.debounce = (func, wait = 0) => {
  if (typeof func !== 'function') {
    throw new TypeError('need a function arguments')
  }
  let result

  return function () {
    let context = this
    let args = arguments

    if (timeid) {
      clearTimeout(timeid)
    }
    timeid = setTimeout(function () {
      result = func.apply(context, args)
    }, wait)

    return result
  }
}

const formatRules = (rules) => {
  if (Object.prototype.toString.call(rules) != '[object Object]') {
    return {}
  }
  let res = {}
  for (var i in rules) {
    let _oneRule = []
    for (var j in rules[i]) {
      if (typeof rules[i][j][0] == 'boolean' && j != 'required') {
        _oneRule.push({
          type: j,
          message: rules[i][j][1],
          trigger: 'blur'
        })
      } else {
        _oneRule.push({
          [j]: rules[i][j][0],
          message: rules[i][j][1],
          trigger: 'blur'
        })
      }
    }
    res[i] = _oneRule
  }
  return res
}
utils.formatRules = formatRules

function throttle(fn, wait) {
  var pre = Date.now()
  return function () {
    var context = this
    var args = arguments
    var now = Date.now()
    if (now - pre >= wait) {
      fn.apply(context, args)
      pre = Date.now()
    }
  }
}
utils.throttle = throttle

const isNumber = (num) => {
  return parseFloat(num).toString() !== 'NaN'
}

const getCookie = (key) =>
  new RegExp('[; ]' + key + '=([^;]*);?').test('; ' + document.cookie)
    ? decodeURIComponent(RegExp.$1)
    : ''

const setCookie = (key, value, path, domain, expiration, undecodes) => {
  let str = key + '=' + (undecodes ? value : encodeURIComponent(value))
  if (expiration) {
    let type = typeof expiration
    // 数字，单位 分钟
    if (type === 'number') {
      expiration = new Date().getTime() + expiration * 60 * 1000
    } else if (type === 'string') {
      expiration = new Date(
        expiration
          .replace(/-/g, '/')
          .replace(/T/, ' ')
          .replace(/\.\d*$/, '')
      )
    }
    str += '; expires=' + new Date(expiration).toGMTString()
  }
  if (path) {
    str += '; path=' + path
  }
  if (domain) {
    str += '; domain=' + domain
  }

  document.cookie = str
}

utils.getCookie = getCookie
utils.setCookie = setCookie

// 通过 a 标签下载文件
export const download = (data) => {
  const { fileName, blob } = data

  if (!blob) {
    return
  }

  if (blob?.type === 'application/json') {
    const reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = function () {
      const readerRes = reader.result
      const resObj = JSON.parse(readerRes)
      Vue.prototype.$toast({
        content: resObj.msg,
        type: 'error'
      })
    }

    return
  }

  const a = document.createElement('a')
  a.href = URL.createObjectURL(blob)
  a.style.display = 'none'
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

// 供应商通用下载链接 userType = 1
function downloadAttachment(data) {
  if (!!data && !!data.id) {
    let item = data
    Vue.prototype.$loading()
    axios
      .get('/api/file/user/file/downloadPrivateFile?useType=1&id=' + item.fileId, {
        responseType: 'blob' // 1.首先设置responseType对象格式为 blob:
      })
      .then((res) => {
        Vue.prototype.$hloading()
        let blob = new Blob([res.data], {
          type: 'application/x-msdownload'
        })
        // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
        let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

        // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = item.fileName
        a.click()
        // 5.释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
      .catch(() => {
        Vue.prototype.$hloading()
        Vue.prototype.$toast({ content: '导出失败，请重试!', type: 'warning' })
      })
  } else {
    Vue.prototype.$toast({
      content: '参数传入错误',
      type: 'error'
    })
  }
}

utils.downloadAttachment = downloadAttachment

/**
 * 获取请求头中的文件名称
 * @param data data: { headers: content-disposition: "<文件名信息>" }
 * @returns String
 */
export const getHeadersFileName = (data) => {
  const contentDisposition = data?.headers['content-disposition'] || ''
  const prefix = 'attachment;filename='
  const prefix1 = 'attachment; filename='
  const prefix2 = "attachment;filename*=utf-8'zh_cn'"
  const prefix3 = 'attachment;filename*='
  let fileName = contentDisposition

  if (contentDisposition.indexOf(prefix) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix.length))
  }
  // 兼容不同格式，上面那个是主数据的导出接口没有空格，这个是supplier的接口多了个空格
  else if (contentDisposition.indexOf(prefix1) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix1.length))
  } else if (contentDisposition.indexOf(prefix2) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix2.length))
  } else if (contentDisposition.indexOf(prefix3) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix3.length))
  }

  return fileName
}

// 时间戳转换成yyyy-MM-dd hh:mm:ss
export const timestampToDate = (timestamp, type) => {
  if (typeof timestamp === 'string') timestamp = Number(timestamp)
  let date = new Date(timestamp) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear() //年
  let M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1 // 月
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() // 日
  let h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  let m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  let strDate = ''
  switch (type) {
    case 'year':
      strDate = Y
      break
    case 'month':
      strDate = Y + '-' + M
      break
    case 'date':
      strDate = Y + '-' + M + '-' + D
      break
    default:
      strDate = Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s
      break
  }
  return strDate
}

export const getSupplierDict = (code) => {
  const dict = JSON.parse(sessionStorage.getItem('supplierDictionary')) || {}
  if (code && !isEmpty(dict)) {
    return dict[code]
  } else {
    return []
  }
}

// 获取网址url的参数，如果传入了key则返回key对应的值
// 定义一个函数，接受一个url字符串作为参数
export const getUrlParams = (key = '') => {
  // 创建一个空对象，用于存储参数
  const url = location.href
  const params = {}
  // 检查url是否包含问号，如果有，说明有参数
  if (url.indexOf('?') !== -1) {
    // 用问号分割url，取第二部分，即参数部分
    const paramString = url.split('?')[1]
    // 用&分割参数部分，得到一个数组，每个元素是一个键值对
    const paramArray = paramString.split('&')
    // 遍历数组，对每个键值对进行处理
    for (let i = 0; i < paramArray.length; i++) {
      // 用=分割键值对，得到一个数组，第一个元素是键，第二个元素是值
      const pair = paramArray[i].split('=')
      // 对键和值进行解码，去除可能的编码字符
      const key = decodeURIComponent(pair[0])
      const value = decodeURIComponent(pair[1])
      // 将键和值作为属性和值添加到params对象中
      params[key] = value
    }
  }
  // 返回params对象
  if (!!key) {
    return params[key]
  }
  return params
}

// 金额千分位显示，保留指定位小数
export const formatAmount = (amount, precision = 2) => {
  if (!amount && amount !== 0) {
    return ''
  }
  const newAmount = amount?.toFixed(+precision)
  const decimalPlace = precision ? newAmount?.split('.')[1] : ''
  let res = amount?.toLocaleString()
  if (precision || precision === 0) {
    const intNum = res?.split('.')[0]
    res = precision === 0 ? intNum : intNum + '.' + decimalPlace
  }
  return res
}

// 导出
export const exportData = (requstUrl, params) => {
  const exportParams = {
    page: { current: 1, size: 10000 },
    ...params
  }
  const { preName, urlName } = requstUrl

  Vue.prototype.$store.commit('startLoading')
  Vue.prototype.$API[preName][urlName](exportParams)
    .then((res) => {
      const fileName = getHeadersFileName(res)
      download({ fileName: `${fileName}`, blob: res.data })
      Vue.prototype.$toast({ content: i18n.t('导出成功'), type: 'success' })
      Vue.prototype.$store.commit('endLoading')
    })
    .catch(() => {
      Vue.prototype.$store.commit('endLoading')
    })
}

// 生成随机字符串
export const randomString = (len = 32) => {
  const $chars =
    'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678' /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  const maxPos = $chars.length
  let str = ''
  for (let i = 0; i < len; i++) {
    str += $chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return str
}

utils.getUrlParams = getUrlParams
utils.getSupplierDict = getSupplierDict
utils.download = download
utils.getHeadersFileName = getHeadersFileName
utils.timestampToDate = timestampToDate
utils.formatAmount = formatAmount
utils.exportData = exportData
utils.randomString = randomString

export default utils
