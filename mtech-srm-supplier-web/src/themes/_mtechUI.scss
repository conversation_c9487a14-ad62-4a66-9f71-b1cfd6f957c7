 /**
*	1. 自定义变量的名称要注意命名规范，做到见名知意。强制要求是前缀必须是所属项目的缩写,缩写的名字可有项目负责人确定，例如 mpw 表示 mtech-platform-web
*	node --max_old_space_size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js serve
*/ 

// @import '/node_modules/@mtech-ui/base/build/themes/default.scss'; 	// UI 库中的基础变量	
// @import '/node_modules/@mtech-ui/button/build/themes/default.scss'; 	// UI 库中Button组件的基础变量
// @import '/node_modules/@mtech/mtech-common-layout/build/themes/default.scss';  // @mtech/mtech-common-layout组件的基础变量

// // 引入mtechUI组件中的 default 主题样式 
// :root {
//   --accent: #00469c;	// --accent 为mtechUI组件中提供的变量，如果项目中需要对其进行覆盖，可以在此处重写
//   --msw-personal-address-border-color: var(--accent);	// 项目中自定义的变量
//   --msw-base-font-primary-color: #{rgba($base-font,0.87)};	// 混用 scss 变量的写法
// }

// :root {	 	// 重写 mtech-common-layout 中的变量 
//   --msw-left-nav-sidebar-bg-color: var(--grey-100);
// }