<template>
  <div id="supplierApp">
    <!-- <router-view></router-view> -->
    <!-- 加载状态 -->
    <mt-micro-loading v-show="loading" />
    <keep-alive>
      <router-view v-if="isKeepAlive($router)" :key="$route.fullPath"></router-view>
    </keep-alive>
    <router-view v-if="!isKeepAlive($router)"></router-view>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import MtMicroLoading from '@/components/micro-loading'
import '@mtech/common-loading/build/esm/bundle.css'

export default {
  name: 'App',
  components: { MtMicroLoading },
  data() {
    return {
      active: 0
    }
  },
  computed: {
    ...mapState(['loading'])
  },

  methods: {
    next() {
      if (this.active++ > 2) this.active = 0
    },
    isKeepAlive(route) {
      let flag = false
      if (route.currentRoute && route.currentRoute.meta && route.currentRoute.meta.keepAlive) {
        flag = true
      }
      return flag
    }
  }
}
</script>

<style lang="scss" scoped>
#supplierApp {
  width: 100%;
  height: 100%;
  position: relative;
}
/* ::-webkit-scrollbar {
  width: 10px!important;
  height: 10px!important;
}
::-webkit-scrollbar-thumb{
	border-radius: 4px;
	background-color: inherit;
} */
html,
body {
  height: 100%;
}

::v-deep .loadingImg {
  height: 100%;
}
</style>
