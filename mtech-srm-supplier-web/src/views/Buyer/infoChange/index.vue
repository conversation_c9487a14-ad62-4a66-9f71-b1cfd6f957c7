<template>
  <div class="lifeCycle-container">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnDataMain } from './config/index.js'
import axios from 'axios'
export default {
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$hloading()
    })
  },
  data() {
    return {
      componentConfig: [
        {
          gridId: 'e9c9b4ea-2ed0-4cab-8a03-38b70e975f56',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_table_new', title: this.$t('新建') },
                { id: 'commit', icon: 'icon_table_new', title: this.$t('提交') },
                { id: 'disAgree', icon: 'icon_card_invite', title: this.$t('驳回') },
                { id: 'batchDel', icon: 'icon_table_delete', title: this.$t('批量删除') },
                { id: 'audit', icon: 'icon_solid_editsvg', title: this.$t('查看OA审批') }
              ],
              [
                'Filter',
                { id: 'export', icon: 'icon_solid_export', title: this.$t('导出') },
                'Refresh',
                'Setting'
              ]
            ]
          },
          grid: {
            columnData: columnDataMain,
            asyncConfig: {
              url: '/supplier/tenant/buyer/info/change/apply/query'
            },
            frozenColumns: 3
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    handleClickToolBar(item) {
      if (item.toolbar.id == 'add') {
        this.$router.push({
          name: 'infoChangePurDetail',
          query: {
            type: 'add'
          }
        })
      } else if (item.toolbar.id == 'commit') {
        // 提交 applyStatus 10 30 的可以提交  供方documentSender=1 applystatus=10 待提交 可以提交
        let selected = item.gridRef.getMtechGridRecords()
        if (selected.length > 0) {
          let num = 0
          let applyCodeList = []
          if (item.documentSender == 1) {
            selected.forEach((item) => {
              if (
                item.applyStatus == 20 ||
                item.applyStatus == 30 ||
                item.applyStatus == 40 ||
                item.applyStatus == 50
              ) {
                num++
              } else {
                applyCodeList.push(item.applyCode)
              }
            })
          } else {
            selected.forEach((item) => {
              if (item.applyStatus == 20 || item.applyStatus == 40 || item.applyStatus == 50) {
                num++
              } else {
                applyCodeList.push(item.applyCode)
              }
            })
          }

          if (num > 0) {
            this.$toast({
              content: this.$t('有状态不能提交'),
              type: 'warning'
            })
          } else {
            this.$loading()
            this.$API.infoChange
              .infoChangeCommit({ applyCodeList })
              .then(() => {
                this.$hloading()
                this.$refs.templateRef.refreshCurrentGridData()
                this.$toast({
                  content: this.$t('提交成功'),
                  type: 'success'
                })
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          }
        } else {
          this.$toast({
            content: this.$t('请选择数据进行提交'),
            type: 'warning'
          })
        }
      } else if (item.toolbar.id == 'disAgree') {
        let selected = item.gridRef.getMtechGridRecords()
        this.handleClickToolBarDisAgree(selected)
      } else if (item.toolbar.id == 'export') {
        this.$loading()
        axios
          .post(
            '/api/supplier/tenant/buyer/info/change/apply/export',
            {
              page: {
                current: 1,
                size: 10000
              }
            },
            {
              responseType: 'blob' // 1.首先设置responseType对象格式为 blob:
            }
          )
          .then((res) => {
            this.$hloading()
            // console.log(res); //把response打出来，看下图
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
            let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

            // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = this.$t('信息变更（采方）.xlsx')
            a.click()
            // 5.释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$hloading()
            this.$toast({ content: this.$t('导出失败，请重试!'), type: 'warning' })
          })
      } else if (item.toolbar.id == 'batchDel') {
        let selected = item.gridRef.getMtechGridRecords()
        if (selected.length > 0) {
          let num = 0
          let applyCodeList = []
          selected.forEach((item) => {
            // 来源是供方的不能删除
            if (item.documentSender == 1) {
              num++
            } else {
              // 删除的话，只有待提交和驳回状态的能删哈
              if (item.applyStatus == 20 || item.applyStatus == 40 || item.applyStatus == 50) {
                num++
              } else {
                applyCodeList.push(item.applyCode)
              }
            }
          })
          if (num > 0) {
            this.$toast({
              content: this.$t('有状态不能删除'),
              type: 'warning'
            })
          } else {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                this.$loading()
                this.$API.infoChange
                  .infoChangeDel({ applyCodeList })
                  .then(() => {
                    this.$hloading()
                    this.$refs.templateRef.refreshCurrentGridData()
                    this.$toast({
                      content: this.$t('删除成功'),
                      type: 'success'
                    })
                  })
                  .catch((err) => {
                    this.$hloading()
                    this.$toast({
                      content: err.msg,
                      type: 'error'
                    })
                  })
              }
            })
          }
        } else {
          this.$toast({
            content: this.$t('请选择数据进行删除'),
            type: 'warning'
          })
        }
      } else if (item.toolbar.id === 'audit') {
        let selected = item.gridRef.getMtechGridRecords()
        this.audit(selected)
      }
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].applyId,
        businessType: sltList[0].applyType
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    handleClickToolBarDisAgree(selected) {
      // documentSender 来源 0/采方 1/供方
      //applyStatus 状态

      let flag = true
      for (let i = 0; i < selected.length; i++) {
        if (selected[i].documentSender == 0) {
          this.$toast({
            content: this.$t(`${selected[i].applyCode}非供方来源不能驳回`),
            type: 'warning'
          })
          flag = false
          break
        } else if (selected[i].applyStatus != '10') {
          this.$toast({
            content: this.$t(`${selected[i].applyCode}非待提交状态不能驳回`),
            type: 'warning'
          })
          flag = false
          break
        }
      }
      if (!flag) return
      let _applyCodeList = selected.map((item) => item.applyCode)
      this.$API.infoChange.reject({ applyCodeList: _applyCodeList }).then(() => {
        this.$toast({
          content: this.$t('驳回成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    handleClickCellTool() {},
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'infoChangePurDetail',
        query: {
          type: 'edit',
          id: data.applyId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lifeCycle-container {
  width: 100%;
  height: 100%;
}
</style>
