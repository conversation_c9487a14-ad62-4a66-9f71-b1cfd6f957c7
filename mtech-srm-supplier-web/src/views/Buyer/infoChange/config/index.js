import Vue from 'vue'
import utils from '@/utils/utils'
import { i18n } from '@/main.js'
import selectAll3 from '../components/selectAll3'
import Component from './columnComponent'
import Select from '../components/Select.vue'
export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '210',
    field: 'applyCode',
    headerText: i18n.t('申请单编码'),
    cellTools: []
  },
  {
    width: '130',
    field: 'applyName',
    headerText: i18n.t('申请单名称')
  },
  {
    width: '200',
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM')
  },
  {
    width: '200',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
  },
  {
    width: '130',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '130',
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'complianceScreeningResult',
    headerText: i18n.t('合规筛查结果'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('通过'),
        0: i18n.t('黑名单'),
        '': ''
      }
    }
  },
  {
    field: 'legalApproveResult',
    headerText: i18n.t('法务审核结果'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('通过'),
        0: i18n.t('不通过'),
        2: i18n.t('审核中'),
        '': ''
      }
    }
  },
  {
    field: 'infoChangeType',
    headerText: i18n.t('信息变更类型')
    // valueConverter: {
    //   type: 'map',
    //   map: {
    //     0: i18n.t('基础信息变更'),
    //     1: i18n.t('供应商名称变更'),
    //     2: i18n.t('财务信息变更'),
    //     4: i18n.t('法人信息变更')
    //   }
    // }
  },
  {
    width: '150',
    field: 'documentSender',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('采方'), 1: i18n.t('供方') }
    }
  },
  {
    width: '100',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { key: '10', value: i18n.t('待提交') },
        { key: '20', value: i18n.t('待审批') },
        { key: '30', value: i18n.t('已驳回') },
        { key: '40', value: i18n.t('已完成') },
        { key: '50', value: i18n.t('已关闭') },
        { key: '90', value: i18n.t('下发失败') },
        { key: '100', value: i18n.t('下发成功') }
      ],
      fields: { text: 'value', value: 'key' }
    },
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

            <span v-if="data.applyStatus == 10" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(99,134,193,0.1);
              color: #6386C1;
            " >{{ statusMap[data.applyStatus] }}</span>
            <span v-if="data.applyStatus == 20" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(99,134,193,0.1);
              color: #6386C1;
            " >
            {{ statusMap[data.applyStatus] }}
            </span>
            <span v-if="data.applyStatus == 30 || data.applyStatus == 90" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(237,86,51,.1);
              color: #ED5633;
            " >{{ statusMap[data.applyStatus] }}</span>
            <span v-if="data.applyStatus == 40 || data.applyStatus == 50 || data.applyStatus == 100" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(154,154,154,.1);
              color: #9A9A9A;
            " >{{ statusMap[data.applyStatus] }}</span>

            </div>`,
          data: function () {
            return {
              data: {},
              statusMap: {
                10: i18n.t('待提交'),
                20: i18n.t('待审批'),
                30: i18n.t('已驳回'),
                40: i18n.t('已完成'),
                50: i18n.t('已关闭'),
                90: i18n.t('下发失败'),
                100: i18n.t('下发成功')
              }
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'failReason',
    headerText: i18n.t('同步备注')
  },
  {
    width: '130',
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    }
  },
  {
    width: '150',
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注')
  }
]
export const forecastColumnData = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const shipStructureColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]

export const factoryAddressColumn = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'address',
    headerText: i18n.t('工厂生产地址')
  }
]
export const factoryAddressColumnEdit = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'address',
    headerText: i18n.t('工厂生产地址'),
    editTemplate: Component.input({
      dataKey: 'address'
    })
  }
]
export const shipStructureInfo = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    field: 'stockHolder',
    headerText: i18n.t('股东'),
    width: '100'
  },
  {
    field: 'shareholdingRatio',
    headerText: i18n.t('持股比例'),
    width: '130'
  },
  {
    field: 'beneficialShares',
    headerText: i18n.t('最终受益股份'),
    width: '150'
  },
  {
    field: 'capitalContributionMoney',
    headerText: i18n.t('认缴出资额'),
    width: '150'
  },
  {
    field: 'capitalContributionDate',
    headerText: i18n.t('认缴出资日期'),
    width: '170',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const contactsInfo = (postList, acceptList) => {
  return [
    {
      type: 'checkbox',
      showInColumnChooser: false,
      width: '50'
    },
    {
      field: 'originTableId',
      headerText: i18n.t('原表Id'),
      visible: false,
      width: '100'
    },
    {
      field: 'contactName',
      headerText: i18n.t('姓名'),
      width: '100'
    },
    {
      field: 'contactSex',
      headerText: i18n.t('性别'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: { 1: i18n.t('男'), 2: i18n.t('女'), null: '', '': '', 0: '' }
      }
    },
    {
      field: 'contactDept',
      headerText: i18n.t('部门'),
      width: '100'
    },
    {
      field: 'contactPost',
      headerText: i18n.t('职务'),
      width: '130',
      valueAccessor: function (field, data) {
        let dataSource = postList || []
        return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
      }
    },
    {
      field: 'contactTel',
      headerText: i18n.t('电话'),
      width: '100'
    },
    {
      field: 'contactMobile',
      headerText: i18n.t('手机'),
      width: '100'
    },
    {
      field: 'contactMail',
      headerText: i18n.t('电子邮箱'),
      width: '130'
    },
    {
      field: 'acceptMsg',
      headerText: i18n.t('接受信息类型'),
      width: '150',
      valueAccessor: function (field, data) {
        let value = []
        let dataSource = acceptList || []
        try {
          let res = []
          if (typeof data[field] === 'object') {
            value = data[field]
          } else {
            value = JSON.parse(data[field])
          }
          if (value && value.length > 0) {
            value.forEach((item) => {
              dataSource.forEach((c) => {
                if (item === c.itemCode) {
                  console.log(item)
                  res.push(c.itemName)
                }
              })
            })
          }
          return res.join(',')
        } catch (error) {
          console.error(error) //处理脏数据的情况啊，'[a,b,c]'  'cs123
          return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
        }
      },
      editTemplate: () => {
        return { template: selectAll3 }
      },
      edit: {
        acceptList
      }
    },
    {
      field: 'contractQq',
      headerText: i18n.t('QQ'),
      width: '100'
    },
    {
      field: 'contactWechat',
      headerText: i18n.t('微信'),
      width: '100'
    }
  ]
}
export const bankInfoColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const bankInfoOld = (provinceList) => [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'bankAccount',
    headerText: i18n.t('银行账号')
  },
  {
    width: '150',
    field: 'accountName',
    headerText: i18n.t('开户名称')
  },
  {
    width: '150',
    field: 'bankName',
    headerText: i18n.t('银行名称')
  },
  {
    width: '150',
    field: 'bankProvince',
    headerText: i18n.t('开户行省份'),
    valueAccessor: function (field, data) {
      let dataSource = provinceList || []
      return dataSource.filter((i) => i.areaCode == data[field])?.[0]?.areaName
    }
  },
  {
    width: '150',
    field: 'bankCity',
    headerText: i18n.t('开户行市')
  },
  {
    width: '150',
    field: 'bankOutlet',
    headerText: i18n.t('开户银行网点')
  },
  {
    width: '150',
    field: 'bankCode',
    headerText: i18n.t('开户行联行号')
  },
  {
    width: '150',
    field: 'swiftCode',
    headerText: i18n.t('SWIFT CODE')
  }
]
export const bankInfo = (provinceList) => {
  return [
    {
      type: 'checkbox',
      showInColumnChooser: false,
      width: '70'
    },
    {
      field: 'originTableId',
      headerText: i18n.t('原表Id'),
      visible: false,
      width: '100'
    },
    {
      width: '150',
      field: 'bankAccount',
      headerText: i18n.t('银行账号')
    },
    {
      width: '150',
      field: 'accountName',
      headerText: i18n.t('开户名称')
    },
    {
      width: '150',
      field: 'bankName',
      headerText: i18n.t('银行名称')
    },
    {
      width: '150',
      field: 'bankProvince',
      headerText: i18n.t('开户行省份'),
      valueAccessor: function (field, data) {
        let dataSource = provinceList || []
        return dataSource.filter((i) => i.areaCode == data[field])?.[0]?.areaName
      }
    },
    {
      width: '150',
      field: 'bankCity',
      headerText: i18n.t('开户行市')
    },
    {
      width: '150',
      field: 'bankOutlet',
      headerText: i18n.t('开户银行网点')
    },
    {
      width: '150',
      field: 'bankCode',
      headerText: i18n.t('开户行联行号')
    },
    {
      width: '150',
      field: 'swiftCode',
      headerText: i18n.t('SWIFT CODE')
    }
  ]
}
export const initCertificateInfoColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initPatentInfoColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initInnovationAwardsColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initTemplateColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const certificateInfoColumnOld = (certificateList) => [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'certificateType',
    headerText: i18n.t('证书类型'),
    valueAccessor: function (field, data) {
      let dataSource = certificateList || []
      return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
    }
  },
  {
    width: '150',
    field: 'certCode',
    headerText: i18n.t('证书编号')
  },
  {
    width: '150',
    field: 'certName',
    headerText: i18n.t('证书名称')
  },
  {
    width: '150',
    field: 'certificateBody',
    headerText: i18n.t('认证机构')
  },
  {
    width: '150',
    field: 'annualReviewDate',
    headerText: i18n.t('年审日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'effectiveDate',
    headerText: i18n.t('生效日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'expireDate',
    headerText: i18n.t('截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'approved',
    headerText: i18n.t('是否审核通过'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否'), null: '', '': '' }
    }
  },
  {
    width: '200',
    field: 'attachment',
    headerText: i18n.t('附件'),
    template: () => {
      return {
        template: Vue.component('attachmentTemplate', {
          template: `
              <div style="width:100%" >
                <span style="cursor: pointer;color: #00469c;margin: 0 5px;"
                 v-for="(item,index) in attachmentLists" :key="index"
                @click="downAttachment(item)">{{item.fileName}}</span>
              </div>
              `,
          data: function () {
            return {
              data: {},
              attachmentLists: []
            }
          },
          mounted() {
            this.attachmentLists = this.data.attachment
          },
          methods: {
            downAttachment(item) {
              this.$utils.downloadAttachment(item)
            }
          }
        })
      }
    }
  }
]
export const certificateInfoColumn = (certificateList) => {
  return [
    {
      type: 'checkbox',
      showInColumnChooser: false,
      width: '70'
    },
    {
      field: 'originTableId',
      headerText: i18n.t('原表Id'),
      visible: false,
      width: '100'
    },
    {
      width: '150',
      field: 'certificateType',
      headerText: i18n.t('证书类型'),
      valueAccessor: function (field, data) {
        let dataSource = certificateList || []
        return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
      }
    },
    {
      width: '150',
      field: 'certCode',
      headerText: i18n.t('证书编号')
    },
    {
      width: '150',
      field: 'certName',
      headerText: i18n.t('证书名称')
    },
    {
      width: '150',
      field: 'certificateBody',
      headerText: i18n.t('认证机构')
    },
    {
      width: '150',
      field: 'annualReviewDate',
      headerText: i18n.t('年审日期'),
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            if (e == 0) {
              return (e = '')
            } else if (typeof e == 'object') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else if (typeof e == 'string') {
              if (e.indexOf('T') != -1) {
                // return e.substr(0,10);
                return utils.formateTime(new Date(e), 'yyyy-MM-dd')
              } else {
                let val = parseInt(e)
                return utils.formateTime(val, 'yyyy-MM-dd')
              }
            } else if (typeof e == 'number') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else {
              return e
            }
          } else {
            return e
          }
        }
      }
    },
    {
      width: '150',
      field: 'effectiveDate',
      headerText: i18n.t('生效日期'),
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            if (e == 0) {
              return (e = '')
            } else if (typeof e == 'object') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else if (typeof e == 'string') {
              if (e.indexOf('T') != -1) {
                // return e.substr(0,10);
                return utils.formateTime(new Date(e), 'yyyy-MM-dd')
              } else {
                let val = parseInt(e)
                return utils.formateTime(val, 'yyyy-MM-dd')
              }
            } else if (typeof e == 'number') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else {
              return e
            }
          } else {
            return e
          }
        }
      }
    },
    {
      width: '150',
      field: 'expireDate',
      headerText: i18n.t('截止日期'),
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            if (e == 0) {
              return (e = '')
            } else if (typeof e == 'object') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else if (typeof e == 'string') {
              if (e.indexOf('T') != -1) {
                // return e.substr(0,10);
                return utils.formateTime(new Date(e), 'yyyy-MM-dd')
              } else {
                let val = parseInt(e)
                return utils.formateTime(val, 'yyyy-MM-dd')
              }
            } else if (typeof e == 'number') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else {
              return e
            }
          } else {
            return e
          }
        }
      }
    },
    {
      width: '150',
      field: 'approved',
      headerText: i18n.t('是否审核通过'),
      valueConverter: {
        type: 'map',
        map: { 1: i18n.t('是'), 0: i18n.t('否'), null: '', '': '' }
      }
    },
    {
      width: '200',
      field: 'attachment',
      headerText: i18n.t('附件'),
      valueConverter: {
        type: 'function',
        filter: (v) => {
          if (v) {
            if (typeof v == 'object') {
              let arr = []
              v.forEach((item) => {
                arr.push(item.fileName)
              })
              return arr.join(',')
            } else {
              return v
            }
          }
        }
      }
    }
  ]
}
export const patentInfoColumnOld = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'originFactoryName',
    headerText: i18n.t('原厂名（中文）')
  },
  {
    width: '150',
    field: 'originFactoryEnglishName',
    headerText: i18n.t('原厂名（英文）')
  },
  {
    width: '150',
    field: 'originContactName',
    headerText: i18n.t('原厂联系人')
  },
  {
    width: '150',
    field: 'originContactTel',
    headerText: i18n.t('原厂联系人电话')
  },
  {
    width: '150',
    field: 'proxyCertificate',
    headerText: i18n.t('代理证/授权书')
  },
  {
    width: '150',
    field: 'effectiveDate',
    headerText: i18n.t('生效日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'expireDate',
    headerText: i18n.t('截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'contactTel',
    headerText: i18n.t('电话')
  },
  {
    width: '150',
    field: 'contractMobile',
    headerText: i18n.t('手机')
  },
  {
    width: '150',
    field: 'agentProduct',
    headerText: i18n.t('代理产品')
  },
  {
    width: '200',
    field: 'attachment',
    headerText: i18n.t('附件'),
    template: () => {
      return {
        template: Vue.component('attachmentTemplate', {
          template: `
          <div style="width:100%" >
                <span style="cursor: pointer;color: #00469c;margin: 0 5px;"
                 v-for="(item,index) in attachmentLists" :key="index"
                @click="downAttachment(item)">{{item.fileName}}</span>
              </div>
              `,
          data: function () {
            return {
              data: {},
              attachmentLists: []
            }
          },
          mounted() {
            this.attachmentLists = this.data.attachment
          },
          methods: {
            downAttachment(item) {
              this.$utils.downloadAttachment(item)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'agentCategoryInfo',
    headerText: i18n.t('代理品类信息')
  },
  {
    width: '150',
    field: 'originUnifiedSocialCredit',
    headerText: i18n.t('原厂统一社会信用代码')
  },
  {
    width: '150',
    field: 'originAcceptMsg',
    headerText: i18n.t('原厂联系人类型')
  },
  {
    width: '150',
    field: 'brand',
    headerText: i18n.t('品牌')
  },
  {
    width: '150',
    field: 'manufacturerName',
    headerText: i18n.t('生产厂家名称')
  },
  {
    width: '150',
    field: 'productionAddress',
    headerText: i18n.t('生产地址')
  }
]

export const patentInfoColumn = (acceptMsgTypeOptions) => {
  return [
    {
      type: 'checkbox',
      showInColumnChooser: false,
      width: '70'
    },
    {
      field: 'originTableId',
      headerText: i18n.t('原表Id'),
      visible: false,
      width: '100'
    },
    {
      width: '150',
      field: 'originFactoryName',
      headerText: i18n.t('原厂名（中文）')
    },
    {
      width: '150',
      field: 'originFactoryEnglishName',
      headerText: i18n.t('原厂名（中文）')
    },
    {
      width: '150',
      field: 'originContactName',
      headerText: i18n.t('原厂联系人')
    },
    {
      width: '150',
      field: 'originContactTel',
      headerText: i18n.t('原厂联系人电话')
    },
    {
      width: '150',
      field: 'proxyCertificate',
      headerText: i18n.t('代理证/授权书')
    },
    {
      width: '150',
      field: 'effectiveDate',
      headerText: i18n.t('生效日期'),
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            if (e == 0) {
              return (e = '')
            } else if (typeof e == 'object') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else if (typeof e == 'string') {
              if (e.indexOf('T') != -1) {
                // return e.substr(0,10);
                return utils.formateTime(new Date(e), 'yyyy-MM-dd')
              } else {
                let val = parseInt(e)
                return utils.formateTime(val, 'yyyy-MM-dd')
              }
            } else if (typeof e == 'number') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else {
              return e
            }
          } else {
            return e
          }
        }
      }
    },
    {
      width: '150',
      field: 'expireDate',
      headerText: i18n.t('截止日期'),
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            if (e == 0) {
              return (e = '')
            } else if (typeof e == 'object') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else if (typeof e == 'string') {
              if (e.indexOf('T') != -1) {
                // return e.substr(0,10);
                return utils.formateTime(new Date(e), 'yyyy-MM-dd')
              } else {
                let val = parseInt(e)
                return utils.formateTime(val, 'yyyy-MM-dd')
              }
            } else if (typeof e == 'number') {
              return utils.formateTime(e, 'yyyy-MM-dd')
            } else {
              return e
            }
          } else {
            return e
          }
        }
      }
    },
    {
      width: '150',
      field: 'contactTel',
      headerText: i18n.t('电话')
    },
    {
      width: '150',
      field: 'contractMobile',
      headerText: i18n.t('手机')
    },
    {
      width: '150',
      field: 'agentProduct',
      headerText: i18n.t('代理产品')
    },
    {
      width: '200',
      field: 'attachment',
      headerText: i18n.t('附件'),
      valueConverter: {
        type: 'function',
        filter: (v) => {
          if (v) {
            if (typeof v == 'object') {
              let arr = []
              v.forEach((item) => {
                arr.push(item.fileName)
              })
              return arr.join(',')
            } else {
              return v
            }
          }
        }
      }
    },
    {
      width: '150',
      field: 'agentCategoryInfo',
      headerText: i18n.t('代理品类信息'),
      template: () => {
        return {
          template: Vue.component('agentCategoryInfo', {
            template: `<div>{{data.agentCategoryInfoText}}</div>`,
            data: function () {
              return { data: {} }
            }
          })
        }
      },
      editTemplate: () => ({ template: Select })
    },
    {
      width: '150',
      field: 'originUnifiedSocialCredit',
      headerText: i18n.t('原厂统一社会信用代码')
    },
    {
      width: '150',
      field: 'originAcceptMsg',
      headerText: i18n.t('原厂联系人类型'),
      valueAccessor: function (field, data) {
        let dataSource = acceptMsgTypeOptions || []
        let nameList = data[field]?.map((v) => {
          return dataSource.find((item) => item.itemCode == v)?.itemName
        })
        return nameList?.join(',')
      },
      editTemplate: () => ({ template: Select })
    },
    {
      width: '150',
      field: 'brand',
      headerText: i18n.t('品牌')
    },
    {
      width: '150',
      field: 'manufacturerName',
      headerText: i18n.t('生产厂家名称')
    },
    {
      width: '150',
      field: 'productionAddress',
      headerText: i18n.t('生产地址')
    }
  ]
}
export const InnovationAwardsColumnOld = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'certName',
    headerText: i18n.t('专利名称')
  },
  {
    width: '150',
    field: 'awardDate',
    headerText: i18n.t('专利获得日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'issuingAuthority',
    headerText: i18n.t('颁发机构')
  },
  {
    width: '150',
    field: 'attachment',
    headerText: i18n.t('附件'),
    template: () => {
      return {
        template: Vue.component('attachmentTemplate', {
          template: `
          <div style="width:100%" >
                <span style="cursor: pointer;color: #00469c;margin: 0 5px;"
                 v-for="(item,index) in attachmentLists" :key="index"
                @click="downAttachment(item)">{{item.fileName}}</span>
              </div>
          `,
          data: function () {
            return {
              data: {},
              attachmentLists: []
            }
          },
          mounted() {
            this.attachmentLists = this.data.attachment
          },
          methods: {
            downAttachment(item) {
              this.$utils.downloadAttachment(item)
            }
          }
        })
      }
    }
  }
]
export const InnovationAwardsColumn = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'certName',
    headerText: i18n.t('专利名称')
  },
  {
    width: '150',
    field: 'awardDate',
    headerText: i18n.t('专利获得日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'issuingAuthority',
    headerText: i18n.t('颁发机构')
  },
  {
    width: '150',
    field: 'attachment',
    headerText: i18n.t('附件上传'),
    valueConverter: {
      type: 'function',
      filter: (v) => {
        if (v) {
          if (typeof v == 'object') {
            let arr = []
            v.forEach((item) => {
              arr.push(item.fileName)
            })
            return arr.join(',')
          } else {
            return v
          }
        }
      }
    }
  }
]
export const templateColumnOld = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'certName',
    headerText: i18n.t('奖项名称')
  },
  {
    width: '150',
    field: 'awardDate',
    headerText: i18n.t('获奖日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'issuingAuthority',
    headerText: i18n.t('颁发机构')
  },
  {
    width: '150',
    field: 'attachment',
    headerText: i18n.t('附件'),
    template: () => {
      return {
        template: Vue.component('attachmentTemplate', {
          template: `
          <div style="width:100%" >
                <span style="cursor: pointer;color: #00469c;margin: 0 5px;"
                 v-for="(item,index) in attachmentLists" :key="index"
                @click="downAttachment(item)">{{item.fileName}}</span>
              </div>`,
          data: function () {
            return {
              data: {},
              attachmentLists: []
            }
          },
          mounted() {
            this.attachmentLists = this.data.attachment
          },
          methods: {
            downAttachment(item) {
              this.$utils.downloadAttachment(item)
            }
          }
        })
      }
    }
  }
]
export const templateColumn = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'certName',
    headerText: i18n.t('奖项名称')
  },
  {
    width: '150',
    field: 'awardDate',
    headerText: i18n.t('获奖日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'issuingAuthority',
    headerText: i18n.t('颁发机构')
  },
  {
    width: '150',
    field: 'attachment',
    headerText: i18n.t('附件上传'),
    valueConverter: {
      type: 'function',
      filter: (v) => {
        if (v) {
          if (typeof v == 'object') {
            let arr = []
            v.forEach((item) => {
              arr.push(item.fileName)
            })
            return arr.join(',')
          } else {
            return v
          }
        }
      }
    }
  }
]
export const initManagementInfomation = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initEquipment = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initSalesQuantity = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]

export const managementInfomation = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'equipmentName',
    headerText: i18n.t('系统名称')
  },
  {
    width: '150',
    field: 'product',
    headerText: i18n.t('产品')
  },
  {
    width: '150',
    field: 'implementer',
    headerText: i18n.t('实施方')
  },
  {
    width: '150',
    field: 'launchDate',
    headerText: i18n.t('上线日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const equipment = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'equipmentName',
    headerText: i18n.t('主要设备名称')
  },
  {
    width: '150',
    field: 'specifications',
    headerText: i18n.t('规格')
  },
  {
    width: '150',
    field: 'number',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'launchDate',
    headerText: i18n.t('制造出厂日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'dailyProductionNormal',
    headerText: i18n.t('日生产能力（正常）')
  },
  {
    width: '150',
    field: 'dailyProductionMax',
    headerText: i18n.t('日生产能力（最大）')
  },
  {
    width: '150',
    field: 'monthlyProductionNormal',
    headerText: i18n.t('月生产能力（正常）')
  },
  {
    width: '150',
    field: 'monthlyProductionMax',
    headerText: i18n.t('月生产能力（最大）')
  },
  {
    width: '150',
    field: 'bottleneck',
    headerText: i18n.t('是否瓶颈'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否'), null: '', '': '' }
    }
  },
  {
    width: '150',
    field: 'equipmentAdditionPlan',
    headerText: i18n.t('设备增加计划')
  }
]
export const initTestColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const testColumn = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'equipmentName',
    headerText: i18n.t('主要设备名称')
  },
  {
    width: '150',
    field: 'specifications',
    headerText: i18n.t('规格')
  },
  {
    width: '150',
    field: 'launchDate',
    headerText: i18n.t('制造出厂日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const salesQuantity = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'cooperatorRank',
    headerText: i18n.t('排序')
  },
  {
    width: '150',
    field: 'customerName',
    headerText: i18n.t('客户名称')
  },
  {
    width: '150',
    field: 'supplyProduct',
    headerText: i18n.t('供应产品')
  },
  {
    width: '150',
    field: 'salesVolume',
    headerText: i18n.t('销售额')
  },
  {
    width: '150',
    field: 'proportion',
    headerText: i18n.t('比例（%）')
  },
  {
    width: '150',
    field: 'currentMonthlySales',
    headerText: i18n.t('当前月销售额')
  },
  {
    width: '150',
    field: 'currentMonthlyProportion',
    headerText: i18n.t('当前月比例（%）')
  },
  {
    width: '150',
    field: 'previousOneMonthSales',
    headerText: i18n.t('前一月销售额')
  },
  {
    width: '150',
    field: 'previousOneMonthProportion',
    headerText: i18n.t('前一月比例（%）')
  },
  {
    width: '150',
    field: 'previousTwoMonthSales',
    headerText: i18n.t('前两月销售额')
  },
  {
    width: '150',
    field: 'previousTwoMonthProportion',
    headerText: i18n.t('前两月比例（%）')
  },
  {
    width: '150',
    field: 'cooperationStartDate',
    headerText: i18n.t('合作起始日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const initBaseInfoColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initProcurementCycle = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initProductionColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initSubcontractColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const initHaulageColumn = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]
export const baseInfoColumn = [
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    field: 'categoryRelationCode',
    headerText: i18n.t('code'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'supplyCategory',
    headerText: i18n.t('供货类别')
  },
  {
    width: '150',
    field: 'categoryDescription',
    headerText: i18n.t('类别描述')
  },
  {
    width: '150',
    field: 'normalProcureCycle',
    headerText: i18n.t('正常采购周期（天）')
  },
  {
    width: '150',
    field: 'minProcureCycle',
    headerText: i18n.t('最短采购周期（天）')
  },
  {
    width: '150',
    field: 'rawMaterialRemark',
    headerText: i18n.t('主要原材料备注')
  },
  {
    width: '150',
    field: 'normalProduceCycle',
    headerText: i18n.t('正常生产周期（天）')
  },
  {
    width: '150',
    field: 'minProduceCycle',
    headerText: i18n.t('最短生产周期（天）')
  },
  {
    width: '150',
    field: 'produceCycleRemark',
    headerText: i18n.t('生产周期备注')
  }
]
export const procurementCycle = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'cycleName',
    headerText: i18n.t('原材料名称')
  },
  {
    width: '150',
    field: 'vendorName',
    headerText: i18n.t('供应厂商')
  },
  {
    width: '150',
    field: 'purchaseCycle',
    headerText: i18n.t('采购周期（天）')
  },
  {
    width: '150',
    field: 'bottleneck',
    headerText: i18n.t('是否瓶颈'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否'), null: '', '': '' }
    }
  },
  {
    width: '150',
    field: 'safetyStock',
    headerText: i18n.t('安全库存量')
  },
  {
    width: '150',
    field: 'vendorContactMode',
    headerText: i18n.t('供货商联系方式')
  }
]
export const productionColumn = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'cycleName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '150',
    field: 'useEquipment',
    headerText: i18n.t('使用设备')
  },
  {
    width: '150',
    field: 'processTime',
    headerText: i18n.t('工序时间（小时）')
  },
  {
    width: '150',
    field: 'bottleneck',
    headerText: i18n.t('是否瓶颈'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否'), null: '', '': '' }
    }
  },
  {
    width: '150',
    field: 'finishedProductInventory',
    headerText: i18n.t('（半）成品库存量')
  }
]
export const subcontractColumn = [
  {
    type: 'checkbox',
    showInColumnChooser: false,
    width: '70'
  },
  {
    field: 'originTableId',
    headerText: i18n.t('原表Id'),
    visible: false,
    width: '100'
  },
  {
    width: '150',
    field: 'cycleName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '150',
    field: 'vendorName',
    headerText: i18n.t('供货商')
  },
  {
    width: '150',
    field: 'purchaseCycle',
    headerText: i18n.t('采购周期（天）')
  },
  {
    width: '150',
    field: 'bottleneck',
    headerText: i18n.t('是否瓶颈'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否'), null: '', '': '' }
    }
  },
  {
    width: '150',
    field: 'safetyStock',
    headerText: i18n.t('安全库存量')
  },
  {
    width: '150',
    field: 'vendorContactMode',
    headerText: i18n.t('供货商联系方式')
  }
]
export const haulageColumn = (deliveryList, transportList) => {
  return [
    {
      type: 'checkbox',
      showInColumnChooser: false,
      width: '70'
    },
    {
      field: 'originTableId',
      headerText: i18n.t('原表Id'),
      visible: false,
      width: '100'
    },
    {
      width: '150',
      field: 'shipmentPlace',
      headerText: i18n.t('发货地点')
    },
    {
      width: '150',
      field: 'deliveryPlace',
      headerText: i18n.t('交货地点'),
      valueAccessor: function (field, data) {
        let dataSource = deliveryList || []
        return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
      }
    },
    {
      width: '150',
      field: 'transmitType',
      headerText: i18n.t('运输方式'),
      valueAccessor: function (field, data) {
        let dataSource = transportList || []
        return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
      }
    },
    {
      width: '150',
      field: 'distance',
      headerText: i18n.t('路程（公里）')
    },
    {
      width: '150',
      field: 'transmitTime',
      headerText: i18n.t('运输时间（小时）')
    },
    {
      width: '150',
      field: 'normalProduceCycle',
      headerText: i18n.t('正常交货周期（天）')
    },
    {
      width: '150',
      field: 'minProduceCycle',
      headerText: i18n.t('最短交货周期（天）')
    },
    {
      width: '150',
      field: 'remark',
      headerText: i18n.t('备注')
    }
  ]
}

const columnData = [
  {
    width: 120,
    field: 'dimensionName',
    headerText: i18n.t('名称')
  },
  {
    field: 'rate',
    headerText: i18n.t('分配权重（%）')
  },
  {
    field: 'dimensionFullScore',
    headerText: i18n.t('指标满分')
  },
  {
    field: 'dimensionScore',
    headerText: i18n.t('分配分值')
  }
]

export const pageConfig = [
  {
    useToolTemplate: false,
    treeGrid: {
      allowPaging: false,
      columnData,
      childMapping: 'targetScores',
      dataSource: []
    }
  }
]
