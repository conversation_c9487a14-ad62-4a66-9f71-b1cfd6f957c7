<template>
  <div>
    <mt-dialog
      ref="dialog"
      :buttons="buttons"
      :header="header"
      width="500px"
      height="300px"
      :open="onOpen"
    >
      <div class="dialog-content">
        {{ message }}
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: i18n.t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    message() {
      return this.modalData.message
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('confirm-function')
    },
    onOpen: function (args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
