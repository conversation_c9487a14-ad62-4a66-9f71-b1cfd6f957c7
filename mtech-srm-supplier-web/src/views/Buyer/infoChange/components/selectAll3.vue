<template>
  <mt-multi-select
    :id="data.column.field"
    :data-source="dataSource"
    v-model="data[data.column.field]"
    :fields="fields"
  ></mt-multi-select>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      fields: { text: 'itemName', value: 'itemCode' },
      dataSource: []
    }
  },
  created() {
    this.dataSource = this.data.column.edit.acceptList || []
    const value = this.data[this.data.column.field]
    try {
      const arr = JSON.parse(value)
      this.data[this.data.column.field] = arr
    } catch (error) {
      if (typeof value === 'string') {
        //处理脏数据
        const a = [value]
        this.data[this.data.column.field] = a
      }
    }
  }
}
</script>

<style scoped>
.cell-operable-title {
  display: inline-block;
  padding: 10px;
}
</style>
