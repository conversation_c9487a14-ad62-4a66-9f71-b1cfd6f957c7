<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  mounted() {
    if (this.data.column.field == 'enterpriseCode') {
      this.$bus.$on('entityNamebus', (val) => {
        this.data.orgCode = val.entityCode
      })
    }
  },
  methods: {
    onInput(e) {
      console.log(e)
    }
  }
}
</script>
