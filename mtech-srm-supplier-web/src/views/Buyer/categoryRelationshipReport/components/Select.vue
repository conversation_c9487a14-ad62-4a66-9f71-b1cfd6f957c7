<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :filtering="filtering"
      :disabled="
        data.id && (data.column.field == 'enterpriseName' || data.column.field == 'categoryName')
      "
      :allow-filtering="true"
    ></mt-select>
  </div>
</template>

<script>
import utils from '@/utils/utils'

export default {
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: 'categoryName', value: 'categoryName' },

      dataSource: []
    }
  },

  mounted() {
    this.filtering = utils.debounce(this.filtering, 1000)
    if (this.data.column.field === 'enterpriseName') {
      let params = {
        page: { current: 1, size: 1000 }
      }
      this.$API.categoryDivisionReport.pagedQuery(params).then((res) => {
        this.dataSource = res.data.records
        this.fields = { text: 'entityName', value: 'entityName' }
      })
    }
    //品类
    if (this.data.column.field === 'categoryName') {
      let params = {
        fuzzyNameOrCode: this.data.categoryName ? this.data.categoryName : ''
      }
      this.$API.categoryDivisionReport.fuzzyQuery(params).then((res) => {
        this.dataSource = res.data
        this.fields = { text: 'categoryName', value: 'categoryName' }
      })
    }
    if (this.data.column.field === 'categoryGrade') {
      this.dataSource = [
        { text: this.$t('A类'), value: 'A' },
        { text: this.$t('B类'), value: 'B' },
        { text: this.$t('C类'), value: 'C' }
      ]
      this.fields = { text: 'text', value: 'value' }
    }
  },

  methods: {
    filtering(e) {
      if (this.data.column.field === 'categoryName') {
        let params = {
          fuzzyNameOrCode: e.text
        }
        this.$API.categoryDivisionReport.fuzzyQuery(params).then((res) => {
          this.dataSource = res.data
          this.fields = { text: 'categoryName', value: 'categoryName' }
        })
      }
    },

    startOpen() {
      console.log(this.data, '下拉打开时最新的行数据')
    },

    selectChange(e) {
      if (this.data.column.field === 'enterpriseName') {
        this.$bus.$emit('crrEnterpriseNamebus', e.itemData)
      }
      if (this.data.column.field === 'categoryName') {
        this.$bus.$emit('crrCategoryCodebus', e.itemData)
        this.$bus.$emit('crrCategoryTypebus', e.itemData)
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('crrEnterpriseNamebus')
    this.$bus.$off('crrCategoryCodebus')
    this.$bus.$off('crrCategoryTypebus')
  }
}
</script>
