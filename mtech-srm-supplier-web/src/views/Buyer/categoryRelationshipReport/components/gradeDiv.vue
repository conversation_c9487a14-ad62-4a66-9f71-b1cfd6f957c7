<template>
  <div>
    <div>{{ categoryGrades }}</div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  computed: {
    categoryGrades() {
      return this.data.categoryGrade == 'A'
        ? this.$t('A类')
        : this.data.categoryGrade == 'B'
        ? this.$t('B类')
        : this.data.categoryGrade == 'C'
        ? this.$t('C类')
        : this.$t('未匹配')
    }
  },
  methods: {}
}
</script>
