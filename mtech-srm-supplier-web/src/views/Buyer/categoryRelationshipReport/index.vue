<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    ></mt-template-page>
    <!-- 导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      :is-show-tips="false"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
import { toolbar, columnData } from './config/index'

export default {
  components: {
    UploadExcelDialog
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          grid: {
            columnData,
            commandClick: this.commandClick, //配置单击进入行编辑
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowEditing: true, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/tenant/buyer/category/cycle/list'
            }
          }
        }
      ],
      //------------------------>
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'categoryRelationshipReport',
        templateUrl: '', // 下载模板接口方法名
        uploadUrl: 'import', // 上传接口方法名
        file: 'multipartFile' //后端接收参数名
      }
    }
  },
  computed: {},
  mounted() {},
  methods: {
    commandClick(args) {
      if (args.commandColumn.type === 'Deletes') {
        // 如果不存在id,直接删除(获取不到rowIndex,暂定刷新)
        if (!args.rowData.id) {
          this.$refs.tepPage.refreshCurrentGridData()
          return
        }
        let params = {
          id: args.rowData.id
        }
        this.$API.categoryRelationshipReport.remove(params).then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
        })
      } else if (args.commandColumn.type === 'history') {
        this.$dialog({
          modal: () =>
            import(/* webpackChunkName: "components/upload" */ './components/history.vue'),
          data: {
            title: this.$t('历史记录'),
            id: args.rowData.id
          },
          success: () => {}
        })
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      } else if (e.toolbar.id === 'upload') {
        this.handleClickToolBarUpload()
      } else if (e.toolbar.id === 'Download') {
        this.handleClickToolBarDownload()
      }
    },
    handleClickToolBarAdd() {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // handleClickToolBarUpload() {
    //   this.$dialog({
    //     modal: () => import(/* webpackChunkName: "components/upload" */ './components/upload.vue'),
    //     data: {
    //       title: this.$t('上传')
    //     },
    //     success: () => {
    //       this.$refs.tepPage.refreshCurrentGridData()
    //       this.$toast({
    //         content: this.$t('操作成功'),
    //         type: 'success'
    //       })
    //     }
    //   })
    // },
    handleClickToolBarUpload() {
      this.showUploadExcel(true)
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      console.log(flag)
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm(data) {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t(data),
        type: 'success'
      })
      this.$refs.tepPage.refreshCurrentGridData()
    },
    handleClickToolBarDownload() {
      let params = {
        page: { current: 1, size: 20 }
      }
      this.$API.categoryRelationshipReport.exportQuery(params).then((res) => {
        let blob = new Blob([res.data], {
          type: 'application/x-msdownload'
        })
        // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
        let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

        // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = this.$t('品类关系报表.xlsx')
        a.click()
        // 5.释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
    },
    actionComplete(e) {
      if (e.requestType == 'save') {
        if (
          e.data.categoryGrade == e.rowData.categoryGrade &&
          e.data.qualifiedCycle == e.rowData.qualifiedCycle
        ) {
          return
        } else {
          let _data = e.data
          const { rowIndex } = e
          this.$API.categoryRelationshipReport
            .saveData([_data])
            .then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
            })
            .catch(() => {
              this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
              this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
            })
        }
      }
    },
    actionBegin(e) {
      if (e.requestType == 'save') {
        if (e.data.categoryGrade === null) {
          e.data.categoryGrade = e.rowData.categoryGrade
        }
        if (e.data.qualifiedCycle === null) {
          e.data.qualifiedCycle = e.rowData.qualifiedCycle
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100vh;
  background-color: #fff;
  .e-color {
    color: #000;
  }
}
</style>
