import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
import gradeDiv from '../components/gradeDiv.vue'
import inputShow from '../components/inputShow.vue'
import editQualifiedCycle from '../components/editQualifiedCycle.vue'
import editQualifiedCycleDiv from '../components/editQualifiedCycleDiv.vue'

export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'enterpriseName',
    headerText: i18n.t('公司名称'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'remote-autocomplete',
      renameField: 'enterpriseCode',
      selectType: 'administrativeCompany', // 业务公司
      fields: { text: 'orgName', value: 'orgCode' },
      params: {
        organizationLevelCodes: ['ORG01', 'ORG02']
      },
      multiple: true,
      operator: 'in',
      url: '/masterDataManagement/tenant/organization/specified-level-paged-query'
    }
  },
  {
    width: 100,
    field: 'enterpriseCode',
    headerText: i18n.t('公司编码'),
    allowEditing: true,
    editTemplate: () => {
      return { template: inputShow }
    }
    // ignore: true
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    allowEditing: false,
    editTemplate: () => {
      return { template: inputShow }
    }
  },
  {
    width: 100,
    field: 'categoryType',
    headerText: i18n.t('品类类型'),
    allowEditing: false,
    editTemplate: () => {
      return { template: inputShow }
    }
  },
  {
    width: 100,
    field: 'categoryGrade',
    headerText: i18n.t('品类等级'),
    allowEditing: true,
    template: () => {
      return { template: gradeDiv }
    },
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 100,
    field: 'qualifiedCycle',
    headerText: i18n.t('新合格周期'),
    type: 'number',
    allowEditing: true,
    template: () => {
      return { template: editQualifiedCycleDiv }
    },
    editTemplate: () => {
      return { template: editQualifiedCycle }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    allowEditing: false,
    ignore: true
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('更新时间'),
    allowEditing: false,
    ignore: true
  },
  {
    width: 280,
    field: 'operation',
    headerText: i18n.t('操作'),
    commands: [
      {
        type: 'Edit',
        buttonOption: {
          content: i18n.t('编辑'),
          iconCss: ' e-icons e-edit',
          cssClass: 'e-flat e-color'
        }
      },
      {
        type: 'Deletes',
        buttonOption: {
          content: i18n.t('删除'),
          iconCss: 'e-icons e-delete',
          cssClass: 'e-flat e-color'
        }
      }
      // {
      //   type: 'history',
      //   buttonOption: {
      //     content: i18n.t('历史'),
      //     iconCss: 'e-icons e-update',
      //     cssClass: 'e-flat e-color'
      //   }
      // }
    ],
    ignore: true
  }
]
