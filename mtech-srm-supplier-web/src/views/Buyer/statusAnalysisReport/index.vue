<!-- 7.4.3 供应商品质状态分析报表 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config/index'
// import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  computed: {},
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      // let selectList = e.gridRef.getMtechGridRecords()
      // if (selectList.length < 1 && e.toolbar.id === 'Download') {
      //   this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      //   return
      // }
      if (e.toolbar.id === 'Download') {
        this.handleClickToolBarDownload()
      }
    },

    handleClickToolBarDownload() {
      let rule = this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 20 },
        rules: rule.rules || []
      }
      this.$API.statusAnalysisReport.exportQualityStatus(params).then((res) => {
        // const fileName = getHeadersFileName(res)
        // download({ fileName: `${fileName}`, blob: res.data })
        let blob = new Blob([res.data], {
          type: 'application/x-msdownload'
        })
        // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
        let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

        // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = this.$t('数量分析报表.xlsx')
        a.click()
        // 5.释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  background-color: #fff;
}
</style>
