<template>
  <div class="perform-box">
    <mt-template-page
      ref="dimensionTable"
      :template-config="tabConfig"
      :hidden-tabs="true"
      @handleClickCellTool="handleClickCellTool"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <mt-dialog
      ref="dialog"
      v-if="dialogVisible"
      css-class="index-dialog"
      :header="dialogHeader"
      :buttons="buttons"
      :open="onOpen"
      :close="onClose"
      :position="{ X: 'right', Y: 'top' }"
      height="100%"
      width="40%"
    >
      <mt-form ref="calcRuleForm" :model="data" :rules="calcRules" style="margin-top: 15px">
        <mt-form-item prop="dimensionName" class="form-item dimension-name" :label="$t('维度名称')">
          <mt-input
            maxlength="20"
            v-model="data.dimensionName"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入维度名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="dimensionType" class="form-item dimension-type" :label="$t('QCDSI')">
          <mt-select
            v-model="data.dimensionType"
            :data-source="[
              { text: $t('质量') + '（Q）', value: '0' },
              { text: $t('成本') + '（C）', value: '1' },
              { text: $t('交付') + '（D）', value: '2' },
              { text: $t('服务') + '（S）', value: '3' },
              { text: $t('创新') + '（I）', value: '4' },
              { text: $t('其他'), value: '5' }
            ]"
            :show-clear-button="true"
            :placeholder="$t('请选择QCDSI')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
          <mt-input
            maxlength="100"
            v-model="data.remark"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { i18n } from '@/main.js'

const dimensionTypeList = [
  { value: '0', text: i18n.t('质量'), cssClass: '' },
  { value: '1', text: i18n.t('成本'), cssClass: '' },
  { value: '2', text: i18n.t('交付'), cssClass: '' },
  { value: '3', text: i18n.t('服务'), cssClass: '' },
  { value: '4', text: i18n.t('创新'), cssClass: '' },
  { value: '5', text: i18n.t('其他'), cssClass: '' }
]
export default {
  data() {
    return {
      tabConfig: [
        {
          gridId: 'e3fa5ef5-e69c-40a8-a90e-f2cef82bcc4f',
          title: '',
          toolbar: [
            'Add',
            'Delete',
            {
              id: 'Edit',
              icon: 'icon_solid_edit',
              title: this.$t('编辑')
            },
            {
              id: 'Enable',
              icon: 'icon_table_enable',
              title: this.$t('启用')
            },
            {
              id: 'Disable',
              icon: 'icon_table_disable',
              title: this.$t('停用')
            }
          ],
          grid: {
            columnData: [
              {
                width: '50',
                type: 'checkbox'
              },
              {
                field: 'dimensionCode',
                headerText: this.$t('维度编码'),
                cellTools: [
                  {
                    id: 'edit',
                    icon: 'icon_list_edit',
                    title: this.$t('编辑'),
                    visibleCondition: (data) => {
                      return data.status == 2
                    }
                  },
                  {
                    id: 'del',
                    icon: 'icon_list_delete',
                    title: this.$t('删除'),
                    visibleCondition: (data) => {
                      return data.status == 2
                    }
                  }
                ]
              },
              {
                field: 'dimensionName',
                headerText: this.$t('维度名称')
              },
              {
                field: 'dimensionType',
                headerText: 'QCDSI',
                valueConverter: {
                  type: 'map',
                  map: dimensionTypeList
                },
                searchOptions: {
                  elementType: 'select',
                  dataSource: dimensionTypeList
                }
              },
              {
                field: 'status',
                headerText: this.$t('状态'),
                valueConverter: {
                  type: 'map',
                  map: [
                    {
                      status: '2',
                      label: this.$t('停用'),
                      cssClass: ['status-label', 'status-enable']
                    },
                    {
                      status: '1',
                      label: this.$t('启用'),
                      cssClass: ['status-label', 'status-disable']
                    }
                  ],
                  fields: { text: 'label', value: 'status' }
                },
                fields: { text: 'label', value: 'status' },
                cellTools: [
                  {
                    id: 'Enable',
                    icon: 'icon_list_enable',
                    title: this.$t('启用'),
                    visibleCondition: (data) => {
                      return data['status'] == '2'
                    }
                  },
                  {
                    id: 'Disable',
                    icon: 'icon_list_disable',
                    title: this.$t('停用'),
                    visibleCondition: (data) => {
                      return data['status'] == '1'
                    }
                  }
                ]
              },
              {
                field: 'createUserName',
                headerText: this.$t('创建人')
              },
              {
                field: 'createDate',
                headerText: this.$t('创建日期'),
                searchOptions: {
                  type: 'date',
                  dateFormat: 'YYYY-mm-dd'
                },
                template: () => {
                  return {
                    template: Vue.component('time-tmp', {
                      template: `
                            <div class="time-box">
                              {{ data.createTime }}
                            </div>`
                    })
                  }
                }
              },
              {
                field: 'remark',
                headerText: this.$t('备注')
              }
            ],
            asyncConfig: {
              url: '/analysis/tenant/buyer/assess/dimension/pageQuery'
            }
          }
        }
      ],
      dialogVisible: false,
      dialogHeader: this.$t('新增维度'),
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: async () => {
            // this.$t("弹窗确定")
            let res
            let data = this.data
            if (this.dialogHeader == this.$t('新增维度')) {
              res = await this.$API.supplierDimension.add(data)
            } else if (this.dialogHeader == this.$t('编辑维度')) {
              res = await this.$API.supplierDimension.update(data)
            }
            if (res.code == 200) {
              this.hide()
              this.$toast({ content: res.msg || this.$t('操作成功'), type: 'success' })
              this.$refs.dimensionTable.refreshCurrentGridData()
            } else {
              this.$toast({ content: res.msg || this.$t('操作失败'), type: 'warning' })
            }
          },
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      data: {
        dimensionName: '',
        dimensionType: '',
        remark: '',
        id: undefined
      },
      calcRules: {
        dimensionName: [{ required: true, message: this.$t('请输入维度名称'), trigger: 'blur' }],
        dimensionType: [{ required: true, message: this.$t('请选择QCDSI'), trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleClickCellTool(e) {
      if (e.tool.id == 'edit') {
        // 编辑
        this.dialogHeader = this.$t('编辑维度')
        if (this.buttons.length == 3) {
          this.buttons.splice(-1, 1)
        }
        this.data = e.data
        this.show()
      } else if (e.tool.id == 'del') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.$API.supplierDimension
              .del({
                ids: [e.data.id]
              })
              .then((res) => {
                if (res.code == '200') {
                  this.$toast({
                    content: res.msg || this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$refs.dimensionTable.refreshCurrentGridData()
                } else {
                  this.$toast({
                    content: res.msg || this.$t('操作失败'),
                    type: 'warning'
                  })
                }
              })
          }
        })
      } else if (e.tool.id == 'Enable' || e.tool.id == 'Disable') {
        this.$API.supplierDimension
          .changeStatus({
            ids: [e.data.id],
            status: e.tool.id == 'Enable' ? 1 : 2
          })
          .then((res) => {
            if (res.code == '200') {
              this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.dimensionTable.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg || this.$t('操作失败'),
                type: 'warning'
              })
            }
          })
      }
    },
    handleClickToolBar(e) {
      if (e.gridRef.getMtechGridRecords().length <= 0 && e.toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        // this.$t("新增")
        this.dialogHeader = this.$t('新增维度')
        if (this.buttons.length == 2) {
          this.buttons.push({
            click: async () => {
              // this.$t("弹窗确定")
              let data = this.data
              data.status = 1
              let res = await this.$API.supplierDimension.add(data)
              if (res.code == 200) {
                this.hide()
                this.$toast({
                  content: res.msg || this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.dimensionTable.refreshCurrentGridData()
              } else {
                this.$toast({
                  content: res.msg || this.$t('操作失败'),
                  type: 'warning'
                })
              }
            },
            buttonModel: { isPrimary: 'true', content: this.$t('新增并启用') }
          })
        }
        this.data = {
          dimensionName: '',
          dimensionType: '',
          remark: ''
        }
        this.show()
      } else if (e.toolbar.id == 'Delete') {
        // this.$t("删除")
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.$API.supplierDimension
              .del({
                ids: e.gridRef.getMtechGridRecords().map((v) => {
                  return v.id
                })
              })
              .then((res) => {
                if (res.code == '200') {
                  this.$toast({
                    content: res.msg || this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$refs.dimensionTable.refreshCurrentGridData()
                } else {
                  this.$toast({
                    content: res.msg || this.$t('操作失败'),
                    type: 'warning'
                  })
                }
              })
          }
        })
      } else if (e.toolbar.id == 'Enable') {
        // this.$t("启用")
        this.$API.supplierDimension
          .changeStatus({
            ids: e.gridRef.getMtechGridRecords().map((v) => {
              return v.id
            }),
            status: 1
          })
          .then((res) => {
            if (res.code == '200') {
              this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.dimensionTable.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg || this.$t('操作失败'),
                type: 'warning'
              })
            }
          })
      } else if (e.toolbar.id == 'Disable') {
        // this.$t("停用")
        this.$API.supplierDimension
          .changeStatus({
            ids: e.gridRef.getMtechGridRecords().map((v) => {
              return v.id
            }),
            status: 2
          })
          .then((res) => {
            if (res.code == '200') {
              this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.dimensionTable.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg || this.$t('操作失败'),
                type: 'warning'
              })
            }
          })
      } else if (e.toolbar.id == 'Edit') {
        // this.$t("编辑")
        if (e.gridRef.getMtechGridRecords().length > 1) {
          this.$toast({ content: this.$t('请勾选单条数据进行编辑'), type: 'warning' })
          return
        }
        if (e.gridRef.getMtechGridRecords()[0].status == 1) {
          this.$toast({ content: this.$t('启用状态下不可进行编辑'), type: 'warning' })
          return
        }
        this.dialogHeader = this.$t('编辑维度')
        if (this.buttons.length == 3) {
          this.buttons.splice(-1, 1)
        }
        this.data = e.gridRef.getMtechGridRecords()[0]
        this.show()
      }
    },
    onOpen(args) {
      this.show()
      args.preventFocus = true
    },
    onClose() {
      this.hide()
    },
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.show()
      })
    },
    hide() {
      this.dialogVisible = false
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.hide()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
.perform-box {
  width: 100%;
  position: relative;
  height: 100%;
  .red {
    background-color: rgba(237, 86, 51, 1);
  }
  .green {
    background-color: rgba(138, 204, 64, 1);
  }
  .yellow {
    background-color: rgba(237, 161, 51, 1);
  }
  .yellowcolor {
    color: rgba(237, 161, 51, 1) !important;
  }
  .bluecolor {
    color: #6386c1 !important;
  }
  .blurbgcolor {
    background-color: #00469c;
  }
  ::v-deep .now-status {
    padding: 3px 5px;
    color: #6386c1;
    background-color: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
  }
  ::v-deep .now-status-stop {
    padding: 3px 5px;
    color: #9a9a9a;
    background-color: rgba(154, 154, 154, 0.1);
    border-radius: 2px;
  }
  ::v-deep .change-status {
    color: #6386c1;
    margin-top: 5px;
    cursor: pointer;
  }
}
::v-deep .dimension-name {
  width: calc((100% - 24px) / 2);
  display: inline-block;
}
::v-deep .dimension-type {
  width: calc((100% - 24px) / 2);
  margin-left: 24px;
  display: inline-block;
}
</style>
