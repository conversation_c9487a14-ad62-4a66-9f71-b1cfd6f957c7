<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="levelCode" :label="$t('评审等级：')">
          <mt-select
            :data-source="modalData.levelData"
            v-model="formObject.levelCode"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :show-clear-button="true"
            :placeholder="$t('请选择评审等级')"
            :allow-filtering="true"
            filter-type="Contains"
            @change="levelChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('综合得分>=')" prop="compositeScoreStart">
          <mt-input
            v-model="formObject.compositeScoreStart"
            :min="0"
            :precision="2"
            type="number"
            :placeholder="$t('请输入综合得分')"
            class="number-item"
            @change="numberChange(formObject, 'compositeScoreStart')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('综合得分<')" prop="compositeScoreEnd">
          <mt-input
            v-model="formObject.compositeScoreEnd"
            :min="formObject.compositeScoreStart"
            :precision="2"
            type="number"
            :placeholder="$t('请输入综合得分')"
            class="number-item"
            @change="numberChange(formObject, 'compositeScoreEnd')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('品质得分>=')" prop="qualityScoreStart">
          <mt-input
            v-model="formObject.qualityScoreStart"
            :min="0"
            :precision="2"
            type="number"
            :placeholder="$t('请输入品质得分')"
            class="number-item"
            @change="numberChange(formObject, 'qualityScoreStart')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('品质得分<')" prop="qualityScoreEnd">
          <mt-input
            v-model="formObject.qualityScoreEnd"
            :min="formObject.qualityScoreStart"
            :precision="2"
            type="number"
            :placeholder="$t('请输入品质得分')"
            class="number-item"
            @change="numberChange(formObject, 'qualityScoreEnd')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      formObject: {
        levelName: '',
        levelCode: '',
        compositeScoreStart: '',
        compositeScoreEnd: '',
        qualityScoreStart: '',
        qualityScoreEnd: ''
      },

      formRules: {
        levelCode: [{ required: true, message: this.$t('请选择评审等级'), trigger: 'blur' }],
        compositeScoreStart: [
          { required: true, message: this.$t('请输入综合得分'), trigger: 'blur' }
        ],
        compositeScoreEnd: [
          { required: true, message: this.$t('请输入综合得分'), trigger: 'blur' }
        ],
        qualityScoreStart: [
          { required: true, message: this.$t('请输入品质得分'), trigger: 'blur' }
        ],
        qualityScoreEnd: [{ required: true, message: this.$t('请输入品质得分'), trigger: 'blur' }]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.title == this.$t('编辑分类规则')) {
      this.formObject = JSON.parse(JSON.stringify(this.modalData.info))
    }
  },
  methods: {
    numberChange(item, type) {
      if (String(item[type]).length > 16) {
        this.$set(item, type, String(item[type]).slice(0, 16))
      }
      let arr = String(item[type]).split('.')
      if (arr.length == 2 && arr[1].length > 2) {
        this.$set(item, type, String(item[type]).slice(0, arr[0].length + 3))
      }
      if (
        item['compositeScoreStart'] &&
        item['compositeScoreEnd'] &&
        parseFloat(item['compositeScoreStart']) > parseFloat(item['compositeScoreEnd'])
      ) {
        item['compositeScoreEnd'] = item['compositeScoreStart']
      }
      if (
        item['qualityScoreStart'] &&
        item['qualityScoreEnd'] &&
        parseFloat(item['qualityScoreStart']) > parseFloat(item['qualityScoreEnd'])
      ) {
        item['qualityScoreEnd'] = item['qualityScoreStart']
      }
    },
    // 弹框确认
    confirm() {
      this.$refs.dialogRef.validate((val) => {
        if (val) {
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    levelChange(v) {
      this.formObject.levelName = v.itemData?.itemName
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 18px;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  .mt-form {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 46%;
    }
  }
  .table-content {
    flex: 1;
  }
  /deep/ {
    .mt-input-number-active .input--wrap::before {
      bottom: -2px;
    }
  }
}
</style>
