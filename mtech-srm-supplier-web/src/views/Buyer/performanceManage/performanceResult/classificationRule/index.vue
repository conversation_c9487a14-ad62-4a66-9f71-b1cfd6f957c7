<template>
  <!-- 模板清单 -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  methods: {
    // 工具栏点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let notStatus0Records = _selectGridRecords.filter((item) => item.status !== 0) // 非拟定状态数据
      let notStatus1Records = _selectGridRecords.filter((item) => item.status !== 1) // 非有效状态数据
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Enable' || e.toolbar.id == 'Delete' || e.toolbar.id == 'Disable')
      ) {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id === 'Add') {
        // 新增
        this.handleAdd()
      } else if (e.toolbar.id === 'Delete') {
        // 删除
        if (notStatus0Records.length > 0) {
          this.$toast({ content: this.$t('只能删除拟定状态数据'), type: 'warning' })
          return
        }
        this.handleDelete(_selectGridRecords)
      } else if (e.toolbar.id === 'Enable') {
        // 生效
        if (notStatus0Records.length > 0) {
          this.$toast({ content: this.$t('只能生效拟定状态数据'), type: 'warning' })
          return
        }
        this.handleStatus(_selectGridRecords, 1)
      } else if (e.toolbar.id === 'Disable') {
        // 失效
        if (notStatus1Records.length > 0) {
          this.$toast({ content: this.$t('只能失效有效状态数据'), type: 'warning' })
          return
        }
        this.handleStatus(_selectGridRecords, 2)
      }
    },
    // 单元格工具点击事件
    handleClickCellTool(e) {
      if (e.tool.id == 'Edit') {
        // 编辑
        this.handleEdit(e.data)
      } else if (e.tool.id == 'Delete') {
        // 删除
        this.handleDelete([e.data])
      } else if (e.tool.id == 'Enable') {
        // 生效
        this.handleStatus([e.data], 1)
      } else if (e.tool.id == 'Disable') {
        // 失效
        this.handleStatus([e.data], 2)
      }
    },
    // 单元格字段点击事件
    handleClickCellTitle(e) {
      if (e.field !== 'name') return
      this.$router.push({
        path: 'rule-detail',
        query: {
          id: e.data.id
        }
      })
    },
    // 指标新增
    handleAdd() {
      this.$router.push({
        path: 'rule-add'
      })
    },
    // 指标编辑
    handleEdit(data) {
      this.$router.push({
        path: 'rule-edit',
        query: {
          id: data.id
        }
      })
    },
    // 指标删除
    handleDelete(records) {
      let ids = records.map((v) => {
        return v.id
      })
      // ids = ids.join(',')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceManage.delRuleList(ids).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // 状态操作
    handleStatus(records, status) {
      let _ids = records.map((v) => {
        return v.id
      })
      // _ids = _ids.join(',')
      let _statusMap = {
        0: this.$t('拟定'),
        1: this.$t('生效'),
        2: this.$t('失效')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceManage.updateRuleStatus({ ids: _ids, status }).then((res) => {
            if (res.code === 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
/deep/ .mt-select-index {
  float: left;
}
</style>
