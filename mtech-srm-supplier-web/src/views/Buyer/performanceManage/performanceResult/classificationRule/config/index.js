//维度设置Tab
import { i18n } from '@/main.js'

const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder ',
    title: i18n.t('新增')
  },
  {
    id: 'Disable',
    icon: 'icon_table_enable ',
    title: i18n.t('失效')
  },
  {
    id: 'Enable',
    icon: 'icon_table_disable ',
    title: i18n.t('生效')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete ',
    title: i18n.t('删除')
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织')
  },
  {
    field: 'name',
    headerText: i18n.t('规则名称'),
    cssClass: 'field-content',
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] === 0
        }
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] === 0
        }
      }
    ]
  },
  {
    field: 'templateTypeName',
    headerText: i18n.t('模板类型')
  },
  {
    field: 'effectiveDate',
    headerText: i18n.t('生效日期')
  },
  {
    field: 'invalidDate',
    headerText: i18n.t('失效日期')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: 150,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('拟定'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('有效'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('失效'),
          cssClass: ['status-label', 'status-enable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Enable',
        icon: 'icon_list_enable',
        title: i18n.t('生效'),
        visibleCondition: (data) => {
          return data['status'] === 0
        }
      },
      {
        id: 'Disable',
        icon: 'icon_list_disable',
        title: i18n.t('失效'),
        visibleCondition: (data) => {
          return data['status'] === 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    }
  }
]

export const pageConfig = [
  {
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url: '/analysis/tenant/buyer/assess/level/rule/queryPage'
      }
    }
  }
]
