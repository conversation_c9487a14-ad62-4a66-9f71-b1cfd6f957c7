import { i18n } from '@/main.js'

export const toolbar = {
  tools: [
    [
      { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
      { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
      { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') }
    ],
    []
  ]
}

// index list column
export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'lineNo',
    headerText: i18n.t('序号')
  },
  {
    field: 'levelName',
    headerText: i18n.t('评审等级')
  },
  {
    field: 'compositeScoreStart',
    headerText: '综合得分>='
  },
  {
    field: 'compositeScoreEnd',
    headerText: '综合得分<'
  },
  {
    field: 'qualityScoreStart',
    headerText: '品质得分>='
  },
  {
    field: 'qualityScoreEnd',
    headerText: '品质得分<'
  }
]

// index list pageConfig
export const pageConfig = [
  {
    toolbar: toolbar,
    useToolTemplate: false,
    useBaseConfig: false,
    grid: {
      height: 'auto',
      columnData: columnData,
      dataSource: []
    }
  }
]
