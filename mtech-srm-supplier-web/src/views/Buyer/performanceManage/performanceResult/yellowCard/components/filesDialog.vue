<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <div class="file-row file-head">
        <p class="file-name">{{ $t('文件名') }}</p>
        <p class="file-download">{{ $t('操作') }}</p>
      </div>
      <div style="height: calc(100% - 26px); overflow: auto">
        <div v-for="item in files" :key="item.id" class="file-row">
          <p class="file-name">{{ item.fileName }}</p>
          <p class="file-download" @click="downloadFile(item)">{{ $t('下载') }}</p>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { download } from '@/utils/utils'
export default {
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      files: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.propsData.files && this.propsData.files.length) {
      this.files = this.propsData.files
    }
  },
  methods: {
    downloadFile(file) {
      this.$API.fileService.downloadPrivateFileTypeOne(file.id).then((res) => {
        download({ fileName: file.fileName, blob: res.data })
      })
    },
    confirm() {
      this.$emit('cancel-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  // display: flex;
  height: 100%;
  width: 100%;
  .file-row {
    display: flex;
    border-bottom: 1px solid rgb(224, 224, 224);
    p {
      height: 42px;
      line-height: 42px;
    }
    .file-name {
      width: 50%;
      text-align: center;
    }
    .file-download {
      width: 50%;
      color: #2783fe;
      cursor: pointer;
      text-align: center;
    }
  }
  .file-head {
    background: #fafafa;
    border: 1px solid rgb(224, 224, 224);
    .file-download {
      color: unset;
    }
  }
}
</style>
