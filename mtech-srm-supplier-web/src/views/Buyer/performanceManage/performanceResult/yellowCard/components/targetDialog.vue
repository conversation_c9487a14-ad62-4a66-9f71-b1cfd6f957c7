<template>
  <mt-dialog
    ref="dialog"
    :buttons="onlySee ? buttonsDetail : buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="voucherType" :label="$t('新增类型：')">
          <mt-select
            v-model="formObject.voucherType"
            :disabled="onlySee || editType === 'edit'"
            :placeholder="$t('请选择新增类型')"
            :fields="{
              text: 'text',
              value: 'value'
            }"
            :data-source="voucherTypeList"
            @change="changeAddType"
            filter-type="Contains"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="formObject.voucherType == 9"
          prop="originRequestNumber"
          :label="$t('原始单据：')"
        >
          <mt-select
            v-model="formObject.originRequestNumber"
            :placeholder="$t('请选择原始单据')"
            :fields="{
              text: 'originRequestNumber',
              value: 'originRequestNumber'
            }"
            :disabled="onlySee"
            :data-source="originRequestNumberList"
            @change="selectOrgin"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="orgCode" :label="$t('组织：')">
          <mt-select
            v-model="formObject.orgCode"
            :placeholder="$t('请选择组织')"
            :fields="{
              text: 'orgName',
              value: 'orgCode'
            }"
            :disabled="isChanged || onlySee || formObject.voucherType == 9"
            :data-source="orgArrList"
            @change="selectOrg"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商名称：')">
          <mt-select
            v-model="formObject.supplierCode"
            :disabled="isChanged || onlySee || formObject.voucherType == 9"
            :data-source="planeArrList"
            :fields="{
              text: 'codeAndName',
              value: 'supplierCode'
            }"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            @change="changePlanName"
            :placeholder="$t('请选择供应商名称')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商代码：')">
          <mt-input
            v-model="formObject.supplierCode"
            disabled
            :placeholder="$t('请选择供应商代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="startMonthTime" :label="$t('起始月份')">
          <mt-date-picker
            v-model="formObject.startMonthTime"
            :disabled="isChanged || onlySee || formObject.voucherType == 9"
            float-label-type="Never"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :placeholder="$t('请选择月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endMonthTime" :label="$t('终止月份')">
          <mt-date-picker
            v-model="formObject.endMonthTime"
            :disabled="onlySee"
            float-label-type="Never"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :placeholder="$t('请选择月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="categoryCode" class="form-item positive" :label="$t('品类')">
          <mt-select
            :allow-filtering="true"
            :disabled="isChanged || onlySee || formObject.voucherType == 9"
            v-model="formObject.categoryCode"
            :data-source="categoryListArrList"
            :fields="{
              text: 'codeAndName',
              value: 'categoryCode'
            }"
            :show-clear-button="true"
            @change="selectCategoryas"
            :placeholder="$t('请选择品类')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="warningType" :label="$t('警告类型')">
          <mt-select
            :allow-filtering="true"
            :disabled="isChanged || onlySee || formObject.voucherType == 9"
            :data-source="warningList"
            :fields="{
              text: 'text',
              value: 'value'
            }"
            :show-clear-button="true"
            :placeholder="$t('请选择警告类型')"
            v-model="formObject.warningType"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('品质附件')" prop="files" label-style="top">
          <!-- <mt-input
            disabled
            :show-clear-button="true"
            v-model="formObject.files"
            :placeholder="$t('请上传附件模板')"
          ></mt-input>
          <input
            ref="file"
            type="file"
            id="btn_file"
            style="display: none"
            :name="fileName"
            @change="changeInputBtn"
          />
          <mt-button class="mgn-left-10" :disabled="isChanged || onlySee" @click="browseBtn">{{
            $t('上传')
          }}</mt-button> -->
          <mt-common-uploader
            :disabled="isChanged || onlySee || formObject.voucherType == 9"
            :is-single-file="false"
            :save-url="saveUrl"
            :download-url="downloadUrl"
            type="line"
            v-model="formObject.files"
          ></mt-common-uploader>
        </mt-form-item>
        <mt-form-item prop="description" :label="$t('描述')">
          <mt-input
            v-model="formObject.description"
            :disabled="isChanged || onlySee"
            :show-clear-button="true"
            :placeholder="$t('请输入描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils.js'
// import { cloneDeep } from 'lodash'
// import dayjs from 'dayjs'
import commonData from '@/utils/constant'

export default {
  data() {
    return {
      // 表单验证
      rules: {
        orgCode: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        originRequestNumber: [
          {
            required: true,
            message: this.$t('请选择原始单据'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商代码'),
            trigger: 'blur'
          }
        ],
        categoryCode: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        warningType: [
          {
            required: true,
            message: this.$t('请选择警告类型'),
            trigger: 'blur'
          }
        ]
      },
      // 弹窗确认事件
      buttons: [
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.submit,
          buttonModel: { isPrimary: 'true', content: this.$t('提交审批') }
        },
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      buttonsDetail: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      voucherTypeList: [
        { text: this.$t('新增单据'), value: 8 },
        { text: this.$t('变更终止月份'), value: 9 }
      ],
      warningList: [
        // { text: this.$t('红牌'), value: 0 },
        { text: this.$t('黄牌'), value: 12 },
        { text: this.$t('一次质量警告'), value: 10 },
        { text: this.$t('二次质量警告'), value: 11 }
        // { text: this.$t('新合格'), value: 4 }
      ],
      saveUrl: commonData.privateFileUrl,
      downloadUrl: commonData.downloadUrl,
      categoryListArrList: [],
      originRequestNumberList: [],
      orgArrList: [],
      planeArrList: [], // 供应商下拉数组
      //表单数据
      formObject: {
        voucherType: 8,
        categoryId: '',
        categoryCode: '',
        description: '',
        endMonthTime: '',
        files: [],
        orgId: '',
        orgCode: '',
        originRequestNumber: '',
        startMonthTime: '',
        supplierId: '',
        supplierCode: '',
        tempRequestNumber: '',
        warningType: ''
      },
      orgObj: {},
      isAllowEdit: true,
      isChanged: false,
      onlySee: false,
      fileName: null,
      editType: 'add'
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getOrgList() //获取组织树 - 下拉数据
    this.planeArrList = []
    this.getSupplierList()
    this.getCategoryList()
    this.getOriginRequestNumberList() //获取原始数据单号 - 下拉数据
    this.$refs['dialog'].ejsRef.show() //显示弹窗
    // 编辑模式
    if (this.modalData && this.modalData.data) {
      if (this.modalData.data.type === 'detail') {
        this.onlySee = true
      }
      if (this.modalData.data.type === 'edit') {
        this.editType = 'edit'
      }
      this.isAllowEdit = false
      const formData = this.modalData.data
      console.log('formData', formData)
      this.formObject = {
        ...formData,
        files: JSON.parse(formData.files),
        startMonthTime: new Date(Number(formData.startMonthTime)),
        endMonthTime: new Date(Number(formData.endMonthTime))
      } //编辑数据回显
      setTimeout(() => {
        this.isAllowEdit = true
      }, 1000)
    }
  },
  methods: {
    //原始单据选择
    selectOrgin(e) {
      const { itemData } = e
      this.$API.performanceManage
        .getYellowCardDetail({ requestNumber: itemData.value })
        .then((res) => {
          const { code, data } = res
          if (code == 200) {
            this.formObject = {
              ...this.formObject,
              categoryId: data.categoryId,
              categoryCode: data.categoryCode,
              description: data.description,
              endMonthTime: new Date(Number(data.endMonthTime)),
              files: JSON.parse(data.files),
              orgId: data.orgId,
              orgCode: data.orgCode,
              startMonthTime: new Date(Number(data.startMonthTime)),
              supplierId: data.supplierId,
              supplierCode: data.supplierCode,
              tempRequestNumber: data.tempRequestNumber,
              warningType: data.warningType
            }
          }
        })
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.orgArrList = res.data
        }
      })
    },
    //获取原始单号列表
    getOriginRequestNumberList() {
      this.$API.performanceManage.getYellowCardNumberList().then((res) => {
        if (res.code == 200) {
          this.originRequestNumberList = res.data
        }
      })
    },
    // 选择新增类型
    changeAddType(e) {
      console.log(e)
      if (e.itemData.value === 1) {
        this.isChanged = true
      } else {
        this.isChanged = false
      }
    },
    //选择组织
    selectOrg(e) {
      const { itemData } = e
      if (itemData && itemData) {
        this.formObject.orgCode = itemData.orgCode // 组织编码
        this.formObject.orgId = itemData.id // 组织id
        this.formObject.orgName = itemData.orgName // 组织名称
      }
    },
    // 获取供应商列表
    getSupplierList() {
      let pamrsform = {}
      this.$API.supplierExcept.getFuzzy(pamrsform).then((result) => {
        if (result.code == 200) {
          this.planeArrList = result.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.supplierName}-${i.supplierCode}`
            }
          })
          // this.getCategoryList(result.data)
        }
      })
    },
    // 获取品类列表
    getCategoryList() {
      this.$API.supplierExcept
        .getCategoryList({
          fuzzyNameOrCode: ''
        })
        .then((result) => {
          this.categoryListArrList = result.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.categoryName}-${i.categoryCode}`
            }
          })
        })
    },
    //选择供应商名称
    changePlanName(e) {
      const { itemData } = e
      if (itemData) {
        this.formObject.supplierCode = itemData.supplierCode //供应商编码
        this.formObject.supplierId = itemData.id //供应商id
        this.formObject.supplierName = itemData.supplierName //供应商名称
      } else {
        // if (this.isAllowEdit) {
        //   this.formObject.supplierCode = '' //供应商编码
        //   this.formObject.supplierId = '' //供应商id
        //   this.formObject.supplierName = '' //供应商名称
        //   this.formObject.categoryId = '' //清空 品类的选中
        //   this.formObject.categoryCode = ''
        //   this.formObject.categoryId = ''
        //   this.formObject.categoryName = ''
        // }
      }
    },
    // 选择品类
    selectCategoryas(e) {
      this.formObject.categoryCode = e.itemData.categoryCode
      this.formObject.categoryId = e.itemData.id
      this.formObject.categoryName = e.itemData.categoryName
    },
    //文件改变
    changeInputBtn(data) {
      var btnFile = document.getElementById('btn_file').value.toLowerCase().split('.')
      this.$loading()
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // this.headerFlag = false;
        // 您未选择需要上传的文件
        return
      }
      if (
        !['xls', 'xlsx', 'doc', 'docx', 'pdf', 'ppt', 'pptx', 'png', 'jpg', 'zip', 'rar'].includes(
          btnFile[btnFile.length - 1]
        )
      ) {
        this.$toast({
          content:
            data.msg ||
            this.$t(`上传格式错误,正确格式为:xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar`),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      this.fileName = files[0].name
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('UploadFiles', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      // fileData = _data
      // this.uploadFile()
    },
    // 上传文件
    // uploadFile() {
    //   this.$refs.file.value = ''
    //   //useType=1	(1 admin 平台管理 2 tenant 租户id 3 user 用户id)
    //   this.$API.fileService
    //     .uploadPrivateFileTypeOne(fileData)
    //     .then((res) => {
    //       if (res.code == 200 && res.data) {
    //         const { data } = res
    //         // this.formObject.files = data.fileName
    //         // this.formObject.modelUrl = data.url
    //         this.$hloading()
    //       } else {
    //         const { data } = res
    //         this.$hloading()
    //         this.$toast({
    //           content: data.msg || this.$t(`上传格式错误,正确格式为:xlsx、docx、pptx、pdf`),
    //           type: 'warning'
    //         })
    //       }
    //     })
    //     .catch((error) => {
    //       this.$hloading()
    //       this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
    //     })
    // },
    //上传文件==按钮
    browseBtn() {
      let fileId = document.getElementById('btn_file')
      fileId.click()
    },
    // 保存按钮 - 新增
    confirm() {
      const editType = this.modalData.data && this.modalData.data.type ? 'edit' : ''
      this.saveAndSubmit(editType, 'save')
    },
    // 保存按钮 - 新增
    submit() {
      const editType = this.modalData.data && this.modalData.data.type ? 'edit' : ''
      this.saveAndSubmit(editType, 'submit')
    },
    saveAndSubmit(editType, operat) {
      //表单校验 后 再请求接口校验状态
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          const params = {
            ...this.formObject,
            endMonthTime: new Date(
              utils.formateTime(this.formObject.endMonthTime, 'yyyy-MM')
            ).getTime(),
            startMonthTime: new Date(
              utils.formateTime(this.formObject.startMonthTime, 'yyyy-MM')
            ).getTime(),
            files: encodeURIComponent(
              this.formObject.files && this.formObject.files.length
                ? JSON.stringify(this.formObject.files)
                : '[]'
            )
          }
          this.saveApi(editType)(params)
            .then((res) => {
              if (res.code == 200) {
                if (operat === 'submit') {
                  console.log('按时发发-', res)
                  this.$API.performanceManage
                    .submitYellowCardList({ id: res.data })
                    .then((resp) => {
                      if (resp.code == 200) {
                        this.$toast({ content: this.$t('提交成功'), type: 'success' })

                        this.$emit('confirm-function') //关闭弹窗
                      }
                    })
                    .catch(() => {
                      this.$emit('confirm-function') //关闭弹窗
                    })
                  return
                }
                this.$toast({ content: this.$t('保存成功'), type: 'success' })

                this.$emit('confirm-function') //关闭弹窗
              } else {
                this.$toast({ content: res.msg, type: 'warning' })
              }
            })
            .catch(() => {
              this.$emit('confirm-function') //关闭弹窗
            })
        }
      })
    },
    saveApi(editType) {
      if (editType === 'edit') {
        return this.$API.performanceManage.updateYellowCardList
      }
      return this.$API.performanceManage.addYellowCardList
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.mgn-left-10 {
  margin-left: 10px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
}
</style>
