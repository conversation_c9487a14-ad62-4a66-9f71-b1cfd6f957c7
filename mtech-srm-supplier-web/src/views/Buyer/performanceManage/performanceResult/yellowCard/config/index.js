// 绩效计算范围
import { i18n } from '@/main.js'
import utils from '@/utils/utils.js'
// import { dropRight } from 'lodash'
import Vue from 'vue'
// import { API } from '@mtech-common/http'
const toolbar = (isPurchase) => {
  if (isPurchase) {
    return [
      {
        id: 'Add',
        icon: 'icon_solid_Createorder',
        title: i18n.t('新增')
      },
      {
        id: 'Addimport',
        icon: 'icon_solid_Createorder',
        title: i18n.t('新增导入')
      },
      {
        id: 'EXimport',
        icon: 'icon_solid_upload',
        title: i18n.t('变更结束月份导入')
      },
      {
        id: 'EXdownload',
        icon: 'icon_solid_Download ',
        title: i18n.t('导出')
      },
      {
        id: 'Publish',
        icon: 'icon_solid_Createorder  ',
        title: i18n.t('发布')
      },
      {
        id: 'submit',
        icon: 'icon_solid_Download ',
        title: i18n.t('提交审批')
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete ',
        title: i18n.t('删除')
      }
    ]
  }
  return [
    {
      id: 'EXdownload',
      icon: 'icon_solid_Download ',
      title: i18n.t('导出')
    }
  ]
}
export const warningList = {
  // 0: i18n.t('红牌'),
  12: i18n.t('黄牌'),
  10: i18n.t('一次质量警告'),
  11: i18n.t('二次质量警告')
  // 4: i18n.t('新合格')
}
const columnData = (isPurchase) => {
  const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
  if (isPurchase) {
    return [
      {
        type: 'checkbox',
        width: 50
      },
      {
        field: 'requestNumber',
        width: 150,
        cellTools: [
          {
            id: 'id'
            // visibleCondition: (data) => {
            //   return data['status'] === 'DRAFT' || data['status'] === 'REJECT'
            // }
          }
        ],
        headerText: i18n.t('单据号')
      },
      // {
      //   field: 'orgId',
      //   headerText: i18n.t('组织'),
      //   valueConverter: {
      //     type: 'map',
      //     map: _list,
      //     fields: { text: 'orgName', value: 'orgId' }
      //   }
      // },
      {
        field: 'orgName',
        headerText: i18n.t('组织')
      },
      {
        width: 150,
        field: 'supplierCode',
        headerText: i18n.t('供应商编码')
      },
      {
        field: 'supplierName',
        headerText: i18n.t('供应商名称')
      },
      {
        field: 'categoryName',
        headerText: i18n.t('品类')
      },
      {
        width: 120,
        field: 'warningType',
        headerText: i18n.t('警告类型'),
        allowEditing: false,
        // ignore: true,
        valueConverter: {
          type: 'map',
          map: {
            12: i18n.t('黄牌'),
            10: i18n.t('一次质量警告'),
            11: i18n.t('二次质量警告')
          }
        }
      },
      {
        width: 120,
        field: 'startMonthTime',
        headerText: i18n.t('起始月份'),
        searchOptions: {
          elementType: 'date-month',
          // type: 'datetime',
          serializeValue: (e) => {
            //自定义搜索值，规则
            return new Date(utils.formateTime(e, 'yyyy-MM')).getTime()
          },
          dateFormat: 'YYYY-mm'
        },
        template: () => {
          return {
            template: Vue.component('dateStr', {
              template: `<span>{{ dateStr }}</span>`,
              data: function () {},
              computed: {
                dateStr() {
                  return utils.formateTime(Number(this.data.startMonthTime), 'yyyy-MM')
                }
              }
            })
          }
        }
      },
      {
        width: 120,
        field: 'endMonthTime',
        headerText: i18n.t('终止月份'),
        searchOptions: {
          elementType: 'date-month',
          serializeValue: (e) => {
            //自定义搜索值，规则
            return new Date(utils.formateTime(e, 'yyyy-MM')).getTime()
          },
          dateFormat: 'YYYY-mm'
        },
        template: () => {
          return {
            template: Vue.component('dateStr', {
              template: `<span>{{ dateStr }}</span>`,
              data: function () {},
              computed: {
                dateStr() {
                  return utils.formateTime(Number(this.data.endMonthTime), 'yyyy-MM')
                }
              }
            })
          }
        }
      },
      {
        field: 'description',
        headerText: i18n.t('描述')
      },
      {
        width: 120,
        field: 'voucherType',
        headerText: i18n.t('单据类型'),
        valueConverter: {
          type: 'map',
          map: {
            8: i18n.t('新增单据'),
            9: i18n.t('变更终止月份')
          }
        }
      },
      {
        width: 150,
        field: 'file',
        headerText: i18n.t('品质警告附件'),
        ignore: true, //忽略
        cellTools: [
          {
            id: 'view',
            icon: 'icon_input_search',
            title: i18n.t('查看')
            // visibleCondition: (data) => {
            //   return JSON.parse(data['files']).length >= 1
            // }
          }
        ]
      },
      {
        width: 120,
        field: 'approvalStatus',
        headerText: i18n.t('审批状态'),
        allowEditing: false,
        // ignore: true,
        valueConverter: {
          type: 'map',
          map: [
            { status: 1, label: i18n.t('拟定'), cssClass: 'title-#9baac1' },
            { status: 2, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
            { status: 3, label: i18n.t('审批通过'), cssClass: 'title-#6386c1' },
            { status: 4, label: i18n.t('已驳回'), cssClass: 'title-#ed5633' },
            { status: 5, label: i18n.t('失效'), cssClass: 'title-#9a9a9a' }
          ],
          fields: { text: 'label', value: 'status' }
        }
      },
      {
        field: 'originRequestNumber',
        headerText: i18n.t('原始单据')
      },
      {
        width: 120,
        field: 'publishStatus',
        headerText: i18n.t('发布状态'),
        allowEditing: false,
        valueConverter: {
          type: 'map',
          map: { 6: i18n.t('未发布'), 7: i18n.t('已发布') }
        }
      },
      {
        field: 'createUserName',
        headerText: i18n.t('创建人')
      },
      {
        width: 150,
        field: 'createDate',
        headerText: i18n.t('创建时间'),
        searchOptions: {
          type: 'date',
          dateFormat: 'YYYY-mm-dd'
        }
      },
      {
        width: 120,
        freeze: 'Right',
        field: 'operation',
        headerText: i18n.t('操作'),
        ignore: true, //忽略
        cellTools: [
          {
            id: 'edit',
            icon: 'icon_Editor',
            title: i18n.t('编辑'),
            visibleCondition: (data) => {
              return (
                (data['approvalStatus'] === 1 || data['approvalStatus'] === 4) &&
                userInfo.username === data['createUserName']
              )
            }
          },
          {
            id: 'delete',
            icon: 'icon_Delete',
            title: i18n.t('删除'),
            visibleCondition: (data) => {
              return (
                (data['approvalStatus'] === 1 || data['approvalStatus'] === 4) &&
                userInfo.username === data['createUserName']
              )
            }
          }
        ]
      }
    ]
  }
  return [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'requestNumber',
      width: 150,
      // cellTools: [
      //   {
      //     id: 'id'
      //     // visibleCondition: (data) => {
      //     //   return data['status'] === 'DRAFT' || data['status'] === 'REJECT'
      //     // }
      //   }
      // ],
      headerText: i18n.t('单据号')
    },
    // {
    //   field: 'orgId',
    //   headerText: i18n.t('组织'),
    //   valueConverter: {
    //     type: 'map',
    //     map: _list,
    //     fields: { text: 'orgName', value: 'orgId' }
    //   }
    // },
    {
      field: 'orgName',
      headerText: i18n.t('组织')
    },
    {
      width: 150,
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类')
    },
    {
      width: 120,
      field: 'warningType',
      headerText: i18n.t('警告类型'),
      allowEditing: false,
      // ignore: true,
      valueConverter: {
        type: 'map',
        map: {
          12: i18n.t('黄牌'),
          10: i18n.t('一次质量警告'),
          11: i18n.t('二次质量警告')
        }
      }
    },
    {
      width: 120,
      field: 'startMonthTime',
      headerText: i18n.t('起始月份'),
      searchOptions: {
        elementType: 'date-month',
        // type: 'datetime',
        serializeValue: (e) => {
          //自定义搜索值，规则
          return new Date(utils.formateTime(e, 'yyyy-MM')).getTime()
        },
        dateFormat: 'YYYY-mm'
      },
      template: () => {
        return {
          template: Vue.component('dateStr', {
            template: `<span>{{ dateStr }}</span>`,
            data: function () {},
            computed: {
              dateStr() {
                return utils.formateTime(Number(this.data.startMonthTime), 'yyyy-MM')
              }
            }
          })
        }
      }
    },
    {
      width: 120,
      field: 'endMonthTime',
      headerText: i18n.t('终止月份'),
      searchOptions: {
        elementType: 'date-month',
        serializeValue: (e) => {
          //自定义搜索值，规则
          return new Date(utils.formateTime(e, 'yyyy-MM')).getTime()
        },
        dateFormat: 'YYYY-mm'
      },
      template: () => {
        return {
          template: Vue.component('dateStr', {
            template: `<span>{{ dateStr }}</span>`,
            data: function () {},
            computed: {
              dateStr() {
                return utils.formateTime(Number(this.data.endMonthTime), 'yyyy-MM')
              }
            }
          })
        }
      }
    },
    {
      field: 'description',
      headerText: i18n.t('描述')
    },
    {
      width: 150,
      field: 'file',
      headerText: i18n.t('品质警告附件'),
      ignore: true, //忽略
      cellTools: [
        {
          id: 'view',
          icon: 'icon_input_search',
          title: i18n.t('查看')
          // visibleCondition: (data) => {
          //   return JSON.parse(data['files']).length >= 1
          // }
        }
      ]
    },
    {
      width: 120,
      field: 'approvalStatus',
      headerText: i18n.t('审批状态'),
      allowEditing: false,
      // ignore: true,
      valueConverter: {
        type: 'map',
        map: [
          { status: 1, label: i18n.t('拟定'), cssClass: 'title-#9baac1' },
          { status: 2, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
          { status: 3, label: i18n.t('审批通过'), cssClass: 'title-#6386c1' },
          { status: 4, label: i18n.t('已驳回'), cssClass: 'title-#ed5633' },
          { status: 5, label: i18n.t('失效'), cssClass: 'title-#9a9a9a' }
        ],
        fields: { text: 'label', value: 'status' }
      }
    },
    {
      field: 'originRequestNumber',
      headerText: i18n.t('原始单据')
    },
    {
      width: 120,
      field: 'publishStatus',
      headerText: i18n.t('发布状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: { 6: i18n.t('未发布'), 7: i18n.t('已发布') }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      width: 150,
      field: 'createDate',
      headerText: i18n.t('创建时间'),
      searchOptions: {
        type: 'date',
        dateFormat: 'YYYY-mm-dd'
      }
    }
  ]
}

export const pageConfig = (url, isPurchase) => {
  let _config = {
    gridId: isPurchase
      ? 'd7f56c74-256e-495d-b9b3-8aaaba8c884e'
      : '586768f3-31d9-4a36-b794-16a2a0666653',
    toolbar: toolbar(isPurchase),
    useToolTemplate: false,
    grid: {
      //关闭查询
      // allowFiltering: false,
      allowSorting: false,
      allowFiltering: true
    }
  }
  // if (Array.isArray(_list) && _list.length) {
  //   _config.grid.columnData = columnData(_list)
  // } else {
  //   _config.grid.columnData = columnData()
  // }
  _config.grid.columnData = columnData(isPurchase)
  if (url) {
    _config.grid.asyncConfig = { url }
  }
  return [_config]
}
