<template>
  <!-- 采购计划负责人设置 -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { getHeadersFileName, download } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  data() {
    return {
      isPurchase: false,
      pageConfig: null
    }
  },
  watch: {
    $route: {
      handler: 'setIsPurchase',
      immediate: true
    }
  },
  mounted() {},
  methods: {
    setIsPurchase() {
      // 根据当前路由判断是否更改采方页面判断开关
      this.isPurchase = this.$route.path === '/supplier/performance-result/yellow-card'
      const url = this.isPurchase
        ? this.$API.performanceManage.getYellowCardList
        : this.$API.performanceManage.getSupYellowCardList
      this.pageConfig = pageConfig(url, this.isPurchase)
    },
    getExportApi() {
      if (this.isPurchase) {
        return this.$API.performanceManage.exYellowCardList
      }
      return this.$API.performanceManage.exSupYellowCardList
    },
    //导出
    EXdownload() {
      //获取template-page组件查询的参数
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 9999 },
        rules: rule.rules || []
      }
      //调用接口
      this.getExportApi()(params).then((res) => {
        const fileName = getHeadersFileName(res)
        //下载文件
        download({
          fileName,
          blob: res.data
        })
      })
    },
    //新增导入
    addimport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('新增上传/导入'),
          paramsKey: 'file',
          importApi: this.$API.performanceManage.imYellowCardList,
          downloadTemplateApi: this.$API.performanceManage.getYellowCardTemplate
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 单元格字段点击事件
    handleClickCellTitle(e) {
      e.data.type = 'detail'
      if (e.field !== 'requestNumber') return
      this.dialogShow(e.data)
    },
    //行点击事件
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        data.type = 'edit'
        this.dialogShow(data)
      } else if (tool.id === 'delete') {
        this.rowDelete([data])
      } else if (tool.id === 'view') {
        const files = data.files ? JSON.parse(data.files) : []
        this.$dialog({
          modal: () => import('./components/filesDialog.vue'),
          data: {
            title: this.$t('查看附件'),
            files
          }
        })
      }
    },
    //工具栏点击事件
    handleClickToolBar(item) {
      //获取选中的数据
      let _selectGridRecords = this.$refs.templateRef
        .getCurrentTabRef()
        .gridRef.getMtechGridRecords()
      //获取按钮id
      let name = item.toolbar ? item.toolbar.id : item.tool.id
      //删除或编辑，判断是否有选中数据
      if (name == 'Add') {
        //新增
        this.dialogShow()
      } else if (name == 'EXimport') {
        // 导入
        this.importData() //变更结束月份导入
      } else if (name == 'EXdownload') {
        //导出
        this.EXdownload()
      } else if (name == 'Delete') {
        // 删除

        this.rowDelete(_selectGridRecords)
      } else if (name == 'Addimport') {
        this.addimport()
      } else if (name == 'Publish') {
        this.pubulishData(_selectGridRecords) //发布数据
      } else if (name == 'submit') {
        this.sumbit(_selectGridRecords)
      }
    },
    //提交审批
    sumbit(val) {
      if (val.length !== 1) {
        this.$toast({ content: this.$t('请仅选择一行提交审批'), type: 'warning' })
        return
      }
      const idsList = []
      for (let i = 0; i < val.length; i++) {
        const ele = val[i]
        if (ele.approvalStatus !== 1 && ele.approvalStatus !== 4) {
          this.$toast({ content: this.$t('只能提交拟定、驳回状态的数据'), type: 'warning' })
          return
        }
        idsList.push(ele.id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交审批数据？')
        },
        success: () => {
          this.$API.performanceManage.submitYellowCardList({ id: idsList[0] }).then((res) => {
            if (res.code == 200) {
              //删除成功后刷新表格
              this.$toast({
                content: this.$t('提交审批成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData() //刷新列表
            }
          })
        }
      })
    },
    //发布数据
    pubulishData(val) {
      if (val.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idsList = []
      for (let i = 0; i < val.length; i++) {
        const ele = val[i]
        if (ele.approvalStatus !== 3 || ele.publishStatus !== 6) {
          this.$toast({ content: this.$t('只能发布审批通过且未发布的的数据'), type: 'warning' })
          return
        }
        idsList.push(ele.id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认发布数据？')
        },
        success: () => {
          this.$API.performanceManage.pubYellowCardList(idsList).then((res) => {
            if (res.code == 200) {
              //删除成功后刷新表格
              this.$toast({
                content: this.$t('发布成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData() //刷新列表
            }
          })
        }
      })
    },
    //变更结束日期导入
    importData() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('变更结束日期上传/导入'),
          paramsKey: 'file',
          importApi: this.$API.performanceManage.imYellowCardChangeList,
          downloadTemplateApi: this.$API.performanceManage.getYellowCardChangeTemplate
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 弹窗
    dialogShow(row) {
      let data = {
        title:
          this.$t('黄牌') +
          (row ? (row.type === 'detail' ? this.$t('详情') : this.$t('编辑')) : this.$t('新增'))
      }
      if (row) {
        data.data = row
      }
      this.$dialog({
        modal: () => import('./components/targetDialog.vue'),
        data,
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //批量删除
    rowDelete(val) {
      //判断val数组里hide的值 1是手动创建 且创建人=系统当前登录账号时，可删除 ，0不可删除
      // if (val.hide == 0) {
      //   this.$toast({ content: this.$t('您不是当前单据的创建人，不能删除'), type: 'warning' })
      //   return
      // }
      //提示弹框
      if (val.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idsList = []
      for (let i = 0; i < val.length; i++) {
        const ele = val[i]
        if (ele.approvalStatus !== 1) {
          this.$toast({ content: this.$t('只能删除拟定状态的数据'), type: 'warning' })
          return
        }
        idsList.push(ele.id)
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceManage.delYellowCardList(idsList).then((res) => {
            if (res.code == 200) {
              //删除成功后刷新表格
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData() //刷新列表
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
/deep/ .e-grid .e-frozencontent.e-frozen-right-content > .e-table {
  border-left: none !important;
}
/deep/ .e-grid .e-frozenheader.e-frozen-right-header > .e-table {
  border-left: none !important;
}
</style>
