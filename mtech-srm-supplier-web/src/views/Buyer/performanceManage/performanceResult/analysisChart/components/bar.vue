<!--
 * @Author: wenjie20.wang <EMAIL>
 * @Date: 2022-10-13 13:45:29
 * @LastEditors: wenjie20.wang <EMAIL>
 * @LastEditTime: 2022-10-22 10:31:28
 * @FilePath: \mtech-srm-supplier-web\src\views\Buyer\performanceManage\performanceResult\analysisChart\components\bar.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 绩效最高Top10，柱状图 -->
  <div class="chart-panel mt-flex-direction-column">
    <!-- <div class="chart-title">图一</div> -->
    <div style="overflow: auto">
      <table class="mytable">
        <!-- <tr class="tr">
          <td>模板类型</td>
          <td v-for="item in barData" :key="item.type">{{ item.type }}</td>
        </tr>
        <tr class="tr">
          <td>品质</td>
          <td v-for="item in barData" :key="item.type">{{ item.pinzhi }}</td>
        </tr>
        <tr class="tr">
          <td>交付</td>
          <td v-for="item in barData" :key="item.type">{{ item.pay }}</td>
        </tr> -->
        <template v-for="(item, index) in dimensions">
          <tr class="tr" :key="index">
            <td>{{ index === 0 ? $t('模板类型') : getDimensionText(dimensions[index]) }}</td>
            <td v-for="(itm, idx) in barData" :key="idx">
              {{ index === 0 ? itm.supplierName : itm[dimensions[index]] }}
            </td>
          </tr>
        </template>
      </table>
    </div>
    <div class="content" id="barTu" style="height: 420px; width: 100%"></div>
  </div>
</template>

<script>
import { templateArrList } from './../config'
import * as echarts from 'echarts'
let barChartDom = null
let barChart = null
export default {
  props: {
    chartList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      barData: [],
      dimensions: [],
      barOption: {}
    }
  },
  mounted() {
    barChartDom = document.getElementById('barTu')
    barChart = echarts.init(barChartDom)
    this.initChart()
  },
  methods: {
    initChart() {
      setTimeout(() => {
        barChart.clear()
        barChart.setOption(this.barOption)
      }, 20)
    },
    getDimensionText(value) {
      for (let i = 0; i < templateArrList.length; i++) {
        const item = templateArrList[i]
        if (item.value === value) {
          return item.text
        }
      }
      return ''
    },
    getDimensionColor(value) {
      for (let i = 0; i < templateArrList.length; i++) {
        const item = templateArrList[i]
        if (item.value === value) {
          return item.color
        }
      }
      return ''
    }
  },
  watch: {
    chartList: {
      handler(data) {
        const sourceData = JSON.parse(JSON.stringify(data))
        let obj = {}
        sourceData.forEach((i) => {
          obj = { ...obj, ...i }
        })

        const keyList = Object.keys(obj)

        for (let i = 0; i < keyList.length; i++) {
          const key = keyList[i]
          for (let j = 0; j < sourceData.length; j++) {
            if (!sourceData[j][key]) {
              sourceData[j][key] = ''
            }
          }
        }
        const legendData = []
        const series = []
        if (Array.isArray(sourceData) && sourceData.length) {
          this.dimensions = Object.keys(sourceData[0])
          this.dimensions.forEach((i) => {
            if (i !== 'supplierName') {
              legendData.push(this.getDimensionText(i))
              series.push({
                name: this.getDimensionText(i),
                // data: this.pinzhi,
                type: 'bar',
                label: {
                  show: true,
                  position: 'top'
                },
                color: this.getDimensionColor(i)
              })
            }
          })
        }
        this.barData = sourceData
        // this.$set(this.barOption.dataset, 'source', sourceData)
        // this.$set(this.barOption.dataset, 'dimensions', this.dimensions)
        this.barOption = {
          grid: {
            containLabel: true,
            left: '1%',
            right: '1%',
            bottom: '10%'
          },
          legend: {
            data: legendData,
            left: '10%'
          },
          tooltip: {},
          dataset: {
            dimensions: this.dimensions,
            source: sourceData
          },
          xAxis: {
            type: 'category',
            boundaryGap: true,
            axisLabel: {
              color: '#333',
              //  让x轴文字方向为竖向
              interval: 0,
              // formatter: function (value) {
              //   return value.split('').join('\n')
              // }
              formatter: function (params) {
                var newParamsName = '' // 最终拼接成的字符串
                var paramsNameNumber = params.length // 实际标签的个数
                var provideNumber = 10 // 每行能显示的字的个数
                if (sourceData.length <= 6) {
                  return params
                } else if (sourceData.length > 6 && sourceData.length <= 10) {
                  provideNumber = 7
                } else if (sourceData.length > 10 && sourceData.length <= 16) {
                  provideNumber = 5
                } else if (sourceData.length > 16 && sourceData.length <= 26) {
                  provideNumber = 3
                } else if (sourceData.length > 26 && sourceData.length <= 38) {
                  provideNumber = 2
                } else {
                  provideNumber = 1
                }
                var rowNumber = Math.ceil(paramsNameNumber / provideNumber) // 换行的话，需要显示几行，向上取整
                /**
                 * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
                 */
                // 条件等同于rowNumber>1
                if (paramsNameNumber > provideNumber) {
                  /** 循环每一行,p表示行 */
                  for (var p = 0; p < rowNumber; p++) {
                    var tempStr = '' // 表示每一次截取的字符串
                    var start = p * provideNumber // 开始截取的位置
                    var end = start + provideNumber // 结束截取的位置
                    // 此处特殊处理最后一行的索引值
                    if (p == rowNumber - 1) {
                      // 最后一次不换行
                      tempStr = params.substring(start, paramsNameNumber)
                    } else {
                      // 每一次拼接字符串并换行
                      tempStr = params.substring(start, end) + '\n'
                    }
                    newParamsName += tempStr // 最终拼成的字符串
                  }
                } else {
                  // 将旧标签的值赋给新标签
                  newParamsName = params
                }
                //将最终的字符串返回
                return newParamsName
              }
            }
            // data: this.modType
          },
          yAxis: {
            type: 'value'
          },
          series: series
        }
        this.initChart()
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    barChartDom = null
    barChart = null
  }
}
</script>
<style scoped>
table,
td {
  border: 1px solid #000000;
  border-collapse: collapse;
}
.tr td {
  padding: 10px 20px;
}
</style>
