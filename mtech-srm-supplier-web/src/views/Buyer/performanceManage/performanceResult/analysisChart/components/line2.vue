<!--
 * @Author: wenjie20.wang <EMAIL>
 * @Date: 2022-10-13 13:45:29
 * @LastEditors: wenjie20.wang <EMAIL>
 * @LastEditTime: 2022-10-22 10:35:28
 * @FilePath: \mtech-srm-supplier-web\src\views\Buyer\performanceManage\performanceResult\analysisChart\components\line.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 绩效最高Top10，柱状图 -->
  <div class="chart-panel mt-flex-direction-column">
    <!-- <div class="chart-title">图三</div> -->
    <div style="overflow: auto">
      <table class="mytable">
        <!-- <tr class="tr">
          <td>供应商</td>
          <td v-for="item in lineData" :key="item.name">{{ item.name }}</td>
        </tr>
        <tr class="tr">
          <td>技术</td>
          <td v-for="item in lineData" :key="item.name">{{ item.jishu }}</td>
        </tr>
        <tr class="tr">
          <td>品质</td>
          <td v-for="item in lineData" :key="item.name">{{ item.pinzhi }}</td>
        </tr> -->
        <template v-for="(item, index) in dimensions">
          <tr class="tr" :key="index">
            <td>{{ index === 0 ? $t('模板类型') : getDimensionText(dimensions[index]) }}</td>
            <td v-for="(itm, idx) in lineData" :key="idx">
              {{ index === 0 ? getMonthText(itm.month) : itm[dimensions[index]] }}
            </td>
          </tr>
        </template>
      </table>
    </div>
    <div class="content" id="lineTu2" style="height: 420px; width: 100%"></div>
  </div>
</template>

<script>
import { templateArrList, monthArrList } from './../config'
import * as echarts from 'echarts'
let lineChartDom = null
let lineChart = null
export default {
  props: {
    chartList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      lineData: [],
      dimensions: [],
      lineOption: {
        legend: {
          data: [this.$t('交付绩效'), this.$t('品质绩效')],
          left: '10%'
        },
        dataset: {
          dimensions: ['name', 'A', 'B', 'C'],
          source: [
            // { name: '1月', jishu: 70, pinzhi: 78 },
            // { name: '2月', jishu: 71, pinzhi: 73 },
            // { name: '3月', jishu: 75, pinzhi: 90 }
          ]
        },
        xAxis: {
          type: 'category'
          // data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: this.$t('交付绩效'),
            type: 'line',
            label: {
              show: true,
              position: 'top'
            },
            color: '#ed7d31'
          },
          {
            name: this.$t('品质绩效'),
            type: 'line',
            label: {
              show: true,
              position: 'top'
            },
            color: '#5b9bd5'
          }
        ]
      }
    }
  },
  mounted() {
    lineChartDom = document.getElementById('lineTu2')
    lineChart = echarts.init(lineChartDom)
    this.initChart()
  },
  methods: {
    initChart() {
      setTimeout(() => {
        lineChart.clear()
        lineChart.setOption(this.lineOption)
      }, 20)
    },
    getDimensionText(value) {
      for (let i = 0; i < templateArrList.length; i++) {
        const item = templateArrList[i]
        if (item.value === value) {
          return item.text
        }
      }
      return ''
    },
    getMonthText(value) {
      let month = ''
      if (value) {
        month = value.substring(value.length - 2)
      }
      for (let i = 0; i < monthArrList.length; i++) {
        const item = monthArrList[i]
        if (item.value === month) {
          return item.text
        }
      }
      return ''
    },
    getDimensionColor(value) {
      for (let i = 0; i < templateArrList.length; i++) {
        const item = templateArrList[i]
        if (item.value === value) {
          return item.color
        }
      }
      return ''
    }
  },
  watch: {
    chartList: {
      handler(data) {
        const sourceData = JSON.parse(JSON.stringify(data))
        let obj = {}
        sourceData.forEach((i) => {
          obj = { ...obj, ...i }
        })

        const keyList = Object.keys(obj)

        for (let i = 0; i < keyList.length; i++) {
          const key = keyList[i]
          for (let j = 0; j < sourceData.length; j++) {
            if (!sourceData[j][key]) {
              sourceData[j][key] = ''
            }
          }
        }
        const legendData = []
        const series = []
        if (Array.isArray(sourceData) && sourceData.length) {
          this.dimensions = Object.keys(sourceData[0])
          this.dimensions.forEach((i) => {
            if (i !== 'month') {
              legendData.push(this.getDimensionText(i))
              series.push({
                name: this.getDimensionText(i),
                type: 'line',
                label: {
                  show: true,
                  position: 'top'
                },
                color: this.getDimensionColor(i)
              })
            }
          })
        }
        this.lineData = sourceData
        // this.$set(this.lineOption.dataset, 'source', n)
        // this.$set(this.lineOption.dataset, 'dimensions', this.dimensions)
        this.lineOption = {
          legend: {
            data: legendData,
            left: '10%'
          },
          dataset: {
            dimensions: this.dimensions,
            source: sourceData.map((i) => {
              return { ...i, month: this.getMonthText(i.month) }
            })
          },
          xAxis: {
            type: 'category'
            // data: this.modType
          },
          yAxis: {
            type: 'value'
          },
          series: series
        }
        this.initChart()
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    lineChartDom = null
    lineChart = null
  }
}
</script>
<style scoped>
table,
td {
  border: 1px solid #000000;
  border-collapse: collapse;
}
.tr td {
  padding: 10px 20px;
}
</style>
