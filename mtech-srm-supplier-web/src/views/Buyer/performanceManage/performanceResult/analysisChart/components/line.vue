<!--
 * @Author: wenjie20.wang <EMAIL>
 * @Date: 2022-10-13 13:45:29
 * @LastEditors: wenjie20.wang <EMAIL>
 * @LastEditTime: 2022-10-13 15:58:03
 * @FilePath: \mtech-srm-supplier-web\src\views\Buyer\performanceManage\performanceResult\analysisChart\components\line.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 绩效最高Top10，柱状图 -->
  <div class="chart-panel mt-flex-direction-column">
    <!-- <div class="chart-title">图二</div> -->
    <div style="overflow: auto">
      <table class="mytable">
        <!-- <tr class="tr">
          <td>供应商</td>
          <td v-for="item in lineData" :key="item.name">{{ item.name }}</td>
        </tr>
        <tr class="tr">
          <td>A</td>
          <td v-for="item in lineData" :key="item.name">{{ item.A }}</td>
        </tr>
        <tr class="tr">
          <td>B</td>
          <td v-for="item in lineData" :key="item.name">{{ item.B }}</td>
        </tr>
        <tr class="tr">
          <td>C</td>
          <td v-for="item in lineData" :key="item.name">{{ item.C }}</td>
        </tr> -->
        <template v-for="(item, index) in dimensions">
          <tr class="tr" :key="index">
            <td>{{ index === 0 ? $t('供应商') : dimensions[index] }}</td>
            <td v-for="(itm, idx) in lineData" :key="idx">
              {{ index === 0 ? getMonthText(itm.month) : itm[dimensions[index]] }}
            </td>
          </tr>
        </template>
      </table>
    </div>
    <div class="content" id="lineTu" style="height: 420px; width: 100%"></div>
  </div>
</template>

<script>
import { monthArrList } from './../config'
import * as echarts from 'echarts'
let lineChartDom = null
let lineChart = null
export default {
  props: {
    chartList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      lineData: [],
      dimensions: [],
      lineOption: {}
    }
  },
  mounted() {
    lineChartDom = document.getElementById('lineTu')
    lineChart = echarts.init(lineChartDom)
    this.initChart()
  },
  methods: {
    initChart() {
      setTimeout(() => {
        lineChart.clear()
        lineChart.setOption(this.lineOption)
      }, 20)
    },
    getMonthText(value) {
      let month = ''
      if (value) {
        month = value.substring(value.length - 2)
      }
      for (let i = 0; i < monthArrList.length; i++) {
        const item = monthArrList[i]
        if (item.value === month) {
          return item.text
        }
      }
      return ''
    },
    getMonthColor(value) {
      let month = ''
      if (value) {
        month = value.substring(value.length - 2)
      }
      for (let i = 0; i < monthArrList.length; i++) {
        const item = monthArrList[i]
        if (item.value === month) {
          return item.color
        }
      }
      return ''
    }
  },
  watch: {
    chartList: {
      handler(data) {
        const sourceData = JSON.parse(JSON.stringify(data))
        let obj = {}
        sourceData.forEach((i) => {
          obj = { ...obj, ...i }
        })

        const keyList = Object.keys(obj)

        for (let i = 0; i < keyList.length; i++) {
          const key = keyList[i]
          for (let j = 0; j < sourceData.length; j++) {
            if (!sourceData[j][key]) {
              sourceData[j][key] = ''
            }
          }
        }
        const legendData = []
        const series = []
        if (Array.isArray(sourceData) && sourceData.length) {
          this.dimensions = Object.keys(sourceData[0])
          this.dimensions.forEach((i) => {
            if (i !== 'month') {
              legendData.push(i)
              series.push({
                name: i,
                type: 'line',
                label: {
                  show: true,
                  position: 'top'
                },
                color: this.getMonthColor(i)
              })
            }
          })
        }
        this.lineData = sourceData
        // this.$set(this.lineOption.dataset, 'source', sourceData)
        // this.$set(this.lineOption.dataset, 'dimensions', this.dimensions)
        this.lineOption = {
          // legend: {
          //   data: legendData,
          //   left: '10%'
          // },
          // tooltip: {},
          dataset: {
            dimensions: this.dimensions,
            source: sourceData.map((i) => {
              return { ...i, month: this.getMonthText(i.month) }
            })
          },
          xAxis: {
            type: 'category'
            // data: this.modType
          },
          yAxis: {
            type: 'value'
          },
          series: series
        }
        this.initChart()
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    lineChartDom = null
    lineChart = null
  }
}
</script>
<style scoped>
table,
td {
  border: 1px solid #000000;
  border-collapse: collapse;
}
.tr td {
  padding: 10px 20px;
}
</style>
