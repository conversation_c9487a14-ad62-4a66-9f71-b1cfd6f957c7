<template>
  <div class="comprehensive-container">
    <!-- 搜索区域 -->
    <collapse-search @reset="reset" @search="search" :min-rows="2">
      <mt-form ref="ruleForm" :model="queryForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="orgCode" :label="$t('组织')" label-style="top">
          <!-- <mt-DropDownTree
            v-if="fieldsarr.dataSource.length"
            v-model="queryForm.orgCode"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择组织：')"
            :popup-height="500"
            :fields="fieldsarr"
            id="baseTreeSelect"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择组织')" :data-source="[]"></mt-select> -->
          <mt-select
            v-model="queryForm.orgCode"
            float-label-type="Never"
            :fields="{ text: 'codeAndName', value: 'dimensionCodeValue' }"
            filter-type="Contains"
            :data-source="orgList"
            :allow-filtering="true"
            :show-clear-button="true"
            :placeholder="$t('请选择组织')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="assessCycle" :label="$t('评价周期')" label-style="top">
          <mt-select
            v-model="queryForm.assessCycle"
            float-label-type="Never"
            :disabled="!isPurchase"
            :allow-filtering="true"
            :show-clear-button="true"
            :data-source="circleOptions"
            :placeholder="$t('评价周期')"
            @change="assessCycleChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类')" label-style="top">
          <mt-select
            v-model="queryForm.categoryId"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="categoryList"
            :fields="{
              text: 'codeAndName',
              value: 'dimensionIdValue'
            }"
            :placeholder="$t('品类')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="!isPurchase"
          prop="chartDimension"
          :label="$t('图表维度')"
          label-style="top"
        >
          <mt-select
            v-model="queryForm.chartDimension"
            float-label-type="Never"
            :allow-filtering="true"
            :show-clear-button="true"
            :data-source="chartDimensionList"
            :placeholder="$t('图表维度')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="templateType" :label="$t('模板类型')" label-style="top">
          <mt-select
            v-model="queryForm.templateType"
            float-label-type="Never"
            :allow-filtering="true"
            :show-clear-button="true"
            :data-source="templateArrList"
            :placeholder="$t('模板类型')"
            @change="templateChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="startMonthStr" :label="$t('绩效起始月份')" label-style="top">
          <mt-date-picker
            v-model="queryForm.startMonthStr"
            float-label-type="Never"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :placeholder="$t('请选择月份')"
            @change="startDateChange"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endMonthStr" :label="$t('绩效终止月份')" label-style="top">
          <mt-date-picker
            v-model="queryForm.endMonthStr"
            float-label-type="Never"
            start="Year"
            depth="Year"
            :disabled="isPurchase || queryForm.assessCycle !== 'MONTH'"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :placeholder="$t('请选择月份')"
          ></mt-date-picker>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
          <!-- <mt-input
            v-model="queryForm.supplierCode"
            :show-clear-button="false"
            :placeholder="$t('供应商')"
          ></mt-input> -->
          <mt-select
            v-model="queryForm.supplierCode"
            float-label-type="Never"
            :fields="{ text: 'codeAndName', value: 'supplierCode' }"
            filter-type="Contains"
            :data-source="supplierList"
            :allow-filtering="true"
            :show-clear-button="true"
            :placeholder="$t('供应商')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>

    <!-- 图一 -->
    <div class="line-panel">
      <chart-bar v-if="isPurchase && barData.length > 0" :chart-list="barData"></chart-bar>
    </div>
    <!-- 图二 -->
    <div v-if="lineData.length > 0" class="line-panel">
      <chart-line :chart-list="lineData"></chart-line>
    </div>
    <!-- 图三 -->
    <div v-if="lineData2.length > 0" class="line-panel">
      <chart-line2 :chart-list="lineData2"></chart-line2>
    </div>
  </div>
</template>

<script>
import collapseSearch from '@/components/collapseSearch'
import chartLine from './components/line.vue'
import chartLine2 from './components/line2.vue'
import chartBar from './components/bar.vue'
import { templateArrList, chartDimensionList } from './config'
import utils from '@/utils/utils'
// import iconButton from '@/components/iconButton/index.vue'
export default {
  components: {
    collapseSearch,
    chartLine,
    chartLine2,
    chartBar
  },
  data() {
    return {
      // fieldsarr: {
      //   dataSource: [], // 组织树下拉数组
      //   value: 'dimensionCodeValue',
      //   text: 'dimensionNameValue',
      //   child: 'childrenList'
      // },
      orgList: [],
      barData: [],
      lineData: [],
      lineData2: [],
      templateArrList, // 模板下来列表
      supplierList: [], // 供应商下来列表
      chartDimensionList, // 图表维度下来列表
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50]
      },
      isPurchase: false, // 判断是是否为采方页面
      queryForm: {
        orgCode: '', // 组织
        startMonthStr: '', // 起始月份
        endMonthStr: '', // 终止月份
        templateType: '', // 模板类型
        assessCycle: '', // 评价周期
        supplierCode: '', // 供应商编码
        categoryId: '', // 品类id
        chartDimension: '' // 图表维度
      },
      rules: {
        assessCycle: [{ required: true, message: this.$t('请选择评价周期'), trigger: 'blur' }],
        categoryId: [{ required: true, message: this.$t('请选择品类'), trigger: 'blur' }],
        chartDimension: [{ required: true, message: this.$t('请选择图表维度'), trigger: 'blur' }],
        // templateType: [{ required: true, message: this.$t('请选择模板类型'), trigger: 'blur' }],
        // endMonthStr: [{ required: true, message: this.$t('请选择绩效结束月份'), trigger: 'blur' }],
        orgCode: [{ required: true, message: this.$t('请选择组织'), trigger: 'blur' }],
        startMonthStr: [{ required: true, message: this.$t('请选择绩效开始月份'), trigger: 'blur' }]
      },
      circleOptions: [
        { text: this.$t('月度'), value: 'MONTH' },
        { text: this.$t('半年度'), value: 'HALF-YEAR' },
        { text: this.$t('年度'), value: 'YEAR' },
        { text: this.$t('季度'), value: 'SEASON' }
      ],
      categoryList: []
    }
  },
  watch: {
    $route: {
      handler: 'setIsPurchase',
      immediate: true
    }
  },
  mounted() {},
  created() {
    this.TreeByAccount()
    this.getPermissionCategoryList()
    // this.getSupplierList = utils.debounce(this.getSupplierList, 300)
    this.getSupplierList()
  },
  methods: {
    assessCycleChange() {
      this.queryForm.startMonthStr = null
      this.queryForm.endMonthStr = null
    },
    // 获取供应商列表数据
    async getSupplierList() {
      this.$API.performanceManage.getAssessSupplierList().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.supplierList = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.supplierCode}-${i.supplierName}`
            }
          })
        }
      })
    },
    getPermissionCategoryList() {
      this.$API.performanceManage.getPermissionCategoryList().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.categoryList = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.dimensionCodeValue}-${i.dimensionNameValue}`
            }
          })
        }
      })
    },
    startDateChange(date) {
      console.log(date)
      this.queryForm.endMonthStr = this.ctrlDateTime(date, 'dateStart')
      // this.queryForm.endMonth = this.ctrlDateTime(date, 'dateStart')
    },
    ctrlDateTime(date, type) {
      const _period = this.queryForm.assessCycle
      const _date = utils.formateTime(date, 'yyyy-MM')
      let _time = ''
      switch (_period) {
        case 'MONTH':
          _time = this.calculateDate(_date, 0, type)
          break
        case 'YEAR':
          _time = this.calculateDate(_date, 11, type)
          break
        case 'HALF-YEAR':
          _time = this.calculateDate(_date, 5, type)
          break
        case 'SEASON':
          _time = this.calculateDate(_date, 2, type)
          break
        default:
          _time = this.calculateDate(_date, 0, type)
      }
      return _time
    },
    calculateDate(date, num, type) {
      let arr = date.split('-')
      let year = parseInt(arr[0])
      let month = parseInt(arr[1])
      month = type === 'dateStart' ? month + num : month - num
      if (month > 12) {
        let yearNum = parseInt((month - 1) / 12)
        month = month % 12 == 0 ? 12 : month % 12
        year += yearNum
      } else if (month <= 0) {
        month = Math.abs(month)
        let yearNum = parseInt((month + 12) / 12)
        let n = month % 12
        if (n == 0) {
          year -= yearNum
          month = 12
        } else {
          year -= yearNum
          month = Math.abs(12 - n)
        }
      }
      month = month < 10 ? '0' + month : month
      return year + '-' + month
    },
    templateChange(e) {
      const { itemData } = e
      console.log(itemData)
      // this.queryForm.assessCycle = itemData?.evaluationCycleCode
      // this.queryForm.assessCycleName = itemData?.evaluationCycleName
      // this.queryForm.startDate = ''
      // this.queryForm.endDate = ''
    },
    // 初始化获取组织列表
    TreeByAccount() {
      this.getTreeByAccountApi()().then((res) => {
        if (res.code == 200) {
          // let value = 'orgId'
          // let text = 'orgName'
          // let value = 'dimensionCodeValue'
          // let text = 'dimensionNameValue'
          // if (this.isPurchase) {
          //   value = 'dimensionCodeValue'
          //   text = 'dimensionNameValue'
          // }
          // this.$set(this.fieldsarr, 'value', value)
          // this.$set(this.fieldsarr, 'text', text)
          // this.$set(this.fieldsarr, 'dataSource', [...res.data])
          this.orgList = res.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.dimensionCodeValue}-${i.dimensionNameValue}`
            }
          })
        }
      })
    },
    getTreeByAccountApi() {
      if (this.isPurchase) {
        return this.$API.performanceManage.getPermissionOrgList
      }
      return this.$API.performanceManage.getPermissionOrgList
    },
    setIsPurchase() {
      // 根据当前路由判断是否更改采方页面判断开关
      this.isPurchase = this.$route.path === '/supplier/performance-result/pur-analysis-chart'
      if (!this.isPurchase) {
        this.queryForm.assessCycle = 'MONTH'
      }
      // this.toolbarOptions = toolbarOptions(this.isPurchase)
    },
    reset() {
      this.queryForm = {}
      this.lineData = []
      this.lineData2 = []
      // this.TreeByAccount()
      // this.search()
    },
    getParams() {
      if (this.queryForm.chartDimension === 'ALL' && !this.queryForm.templateType) {
        this.$toast({
          content: this.$t('图表维度为所有供应商时模板类型必填'),
          type: 'warning'
        })
        return false
      }
      if (this.queryForm.chartDimension === 'SINGLE' && !this.queryForm.supplierCode) {
        this.$toast({
          content: this.$t('图表维度为单个供应商时供应商必填'),
          type: 'warning'
        })
        return false
      }
      const params = {
        ...this.queryForm,
        // endDate: utils.formateTime(this.queryForm.endDate, 'yyyy-MM') + '-01',
        // startDate: utils.formateTime(this.queryForm.startDate, 'yyyy-MM') + '-01',
        endMonthStr: new Date(utils.formateTime(this.queryForm.endMonthStr, 'yyyy-MM')).getTime(),
        startMonthStr: new Date(
          utils.formateTime(this.queryForm.startMonthStr, 'yyyy-MM')
        ).getTime()
      }
      // if (this.queryForm.orgCode && this.queryForm.orgCode.length) {
      //   params.orgCode = this.queryForm.orgCode[0]
      // } else {
      //   params.orgCode = ''
      // }
      // 获取起始日期月份的数字
      let month = new Date(this.queryForm.startMonthStr).getMonth() + 1
      // 获取评价周期
      let evaluationCycle = this.queryForm.assessCycle
      if (evaluationCycle === 'YEAR' && month != 1) {
        this.$toast({
          content: this.$t('评价周期为年度时，绩效起始月份只可选择1月'),
          type: 'warning'
        })
        return false
      } else if (evaluationCycle === 'HALF-YEAR' && month != 1 && month != 7) {
        this.$toast({
          content: this.$t('评价周期为半年度时，绩效起始月份只可选择1月或7月'),
          type: 'warning'
        })
        return false
      } else if (
        evaluationCycle === 'SEASON' &&
        month != 1 &&
        month != 4 &&
        month != 7 &&
        month != 10
      ) {
        this.$toast({
          content: this.$t('评价周期为季度时，绩效起始月份只可选择1月、4月、7月或10月'),
          type: 'warning'
        })
        return false
      }
      return params
    },
    search() {
      // const params = {
      //   orgCode: '1017', // 组织
      //   startMonthStr: '202209', // 起始月份
      //   endMonthStr: '202209', // 终止月份
      //   templateType: 'DELIVER', // 模板类型
      //   assessCycle: 'MONTH', // 评价周期
      //   supplierCode: '', // 供应商编码
      //   categoryId: '1485882617341386753', // 品类id
      //   chartDimension: 'ALL' // 图表维度
      // }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 请求表格数据
          if (!this.getParams()) {
            return false
          }
          let params = this.getParams()
          params.startMonthStr = utils.formateTime(params.startMonthStr, 'yyyy-MM').replace('-', '')
          params.endMonthStr = utils.formateTime(params.endMonthStr, 'yyyy-MM').replace('-', '')

          this.getSearchApi()(params).then((res) => {
            console.log(res, '图表数据')
            const { code, data } = res
            if (code === 200) {
              const supplierCodeList = Object.keys(data)
              const barSourceData = []
              const lineSourceData = []
              const lineTowSourceData = []
              for (let i = 0; i < supplierCodeList.length; i++) {
                const ele = supplierCodeList[i]
                const obj = {
                  supplierName: data[ele][0]['supplierName']
                }
                const objLine = {
                  month: data[ele][0]['assessMonth']
                }
                const objLineTwo = {
                  month: data[ele][0]['assessMonth']
                }
                for (let j = 0; j < data[ele].length; j++) {
                  const element = data[ele][j]
                  // 柱状图拼接{模板类型: 得分}
                  obj[element['templateType']] = element['comprehensiveScore']
                  // 折线图1拼接{供应商名称: 得分}
                  objLine[element['supplierName']] = element['comprehensiveScore']
                  // 折线图2拼接{类型: 得分}
                  objLineTwo[element['templateType']] = element['comprehensiveScore']
                }
                barSourceData.push(obj)
                lineSourceData.push(objLine)
                lineTowSourceData.push(objLineTwo)
              }
              if (this.isPurchase) {
                this.barData = barSourceData
              } else {
                if (this.queryForm.chartDimension === 'SINGLE') {
                  this.lineData = []
                  this.lineData2 = lineTowSourceData
                }
                if (this.queryForm.chartDimension === 'ALL') {
                  this.lineData = lineSourceData
                  this.lineData2 = []
                }
              }
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getSearchApi() {
      if (this.isPurchase) {
        return this.$API.performanceManage.getPorAnalysisChart
      }
      return this.$API.performanceManage.getSubAnalysisChart
    }
  }
}
</script>

<style lang="scss" scoped>
.comprehensive-container {
  margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  .button-group {
    display: flex;
    padding: 0 0 16px;
  }
}
table,
td {
  border: 1px solid #000000;
  border-collapse: collapse;
}
// table.mytable {
//   border: 2px solid #444;
//   border-spacing: 0;
// }
.tr td {
  padding: 10px 20px;
}
/deep/.chart-panel {
  .chart-title {
    font-size: 16px;
    color: #292929;
    display: inline-block;
    padding-left: 13px;
    position: relative;
    margin-bottom: 20px;

    &:before {
      content: '';
      position: absolute;
      width: 3px;
      height: 14px;
      background: #6386c1;
      border-radius: 0 2px 2px 0;
      left: 0;
      top: 2px;
    }
  }
}
</style>
