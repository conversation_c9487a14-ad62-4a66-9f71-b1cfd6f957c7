<template>
  <div class="fullHeight">
    <div class="formContent">
      <!-- 自定义查询条件 -->
      <collapse-search
        @reset="handleCustomReset"
        @search="handleCustomSearch"
        :is-grid-display="true"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="rules">
          <mt-form-item prop="evaluateYear" :label="$t('评价年份')" label-style="top">
            <mt-input v-model="searchFormModel.evaluateYear"></mt-input>
          </mt-form-item>
          <mt-form-item prop="publishStatus" :label="$t('发布状态')" label-style="top">
            <mt-select
              v-model="searchFormModel.publishStatus"
              :data-source="[
                { text: $t('未发布'), value: 0 },
                { text: $t('发布中'), value: 1 },
                { text: $t('发布成功'), value: 2 },
                { text: $t('发布失败'), value: 3 }
              ]"
              :fields="{ text: 'text', value: 'value' }"
              :allow-filtering="true"
              filter-type="Contains"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
            <RemoteAutocomplete
              v-model="searchFormModel.categCode"
              url="/masterDataManagement/tenant/category/paged-query"
              multiple
              :placeholder="$t('请选择品类')"
              :fields="{ text: 'categoryName', value: 'categoryCode' }"
              :search-fields="['categoryCode', 'categoryName']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
            <RemoteAutocomplete
              v-model="searchFormModel.supplierCode"
              url="/masterDataManagement/tenant/supplier/paged-query"
              multiple
              :placeholder="$t('请选择供应商')"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :search-fields="['supplierCode', 'supplierName']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="orgId" :label="$t('组织')" label-style="top">
            <!-- <mt-select
              v-model="searchFormModel.orgId"
              float-label-type="Never"
              :fields="{ text: 'codeAndName', value: 'dimensionCodeValue' }"
              filter-type="Contains"
              :data-source="orgList"
              :allow-filtering="true"
              :show-clear-button="true"
              @change="orgChange"
              :placeholder="$t('请选择组织')"
            ></mt-select> -->
            <mt-DropDownTree
              v-if="orgFields.dataSource.length > 0"
              v-model="orgIdArr"
              :placeholder="$t('请选择组织')"
              :popup-height="500"
              :fields="orgFields"
              :allow-filtering="true"
              filter-type="Contains"
              @change="selectOrg"
              id="baseTreeSelect"
            />
            <mt-select
              v-else
              v-model="searchFormModel.orgId"
              css-class="rule-element"
              :data-source="[]"
              :show-clear-button="true"
              :placeholder="$t('请选择组织')"
            />
          </mt-form-item>
          <mt-form-item prop="purchaserName" :label="$t('采购员')" label-style="top">
            <mt-input
              v-model="searchFormModel.purchaserName"
              @change="(e) => handleInputChange(e, 'purchaserName')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="qltyTypeName" :label="$t('质量分类')" label-style="top">
            <mt-input
              v-model="searchFormModel.qltyTypeName"
              @change="(e) => handleInputChange(e, 'qltyTypeName')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="techTypeName" :label="$t('技术分类')" label-style="top">
            <mt-input
              v-model="searchFormModel.techTypeName"
              @change="(e) => handleInputChange(e, 'techTypeName')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="createDate" :label="$t('创建日期')" label-style="top">
            <!-- <mt-date-picker
              v-model="searchFormModel.startCreateDate"
              :allow-edit="false"
              :open-on-focus="true"
              format="yyyy-MM-dd"
              :placeholder="$t('请选择创建日期')"
              @change="onChange"
            /> -->
            <mt-date-range-picker
              ref="DatePicker"
              v-model="searchFormModel.createDate"
              :placeholder="$t('请选择')"
              @change="(e) => handleDateTimeChange(e, 'CreateDate')"
            />
          </mt-form-item>
        </mt-form>
      </collapse-search>
    </div>
    <div class="listContent">
      <listContent
        :list-info="listInfo"
        :evaluate-year="curYear"
        :page="totalNum"
        :search-model="searchFormModel"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>
<script>
import CollapseSearch from '@/components/collapseSearch'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import listContent from './coms/list.vue'
import dayjs from 'dayjs'

// import utils from '@/utils/utils'
// import { cloneDeep } from 'lodash'
// import utils from '@/utils/utils'

export default {
  components: { RemoteAutocomplete, CollapseSearch, listContent },
  data() {
    return {
      orgIdArr: [],
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      rules: {
        evaluateYear: [{ required: true, message: this.$t('请填写'), trigger: 'blur' }]
      },
      // 搜索条件
      searchFormModel: {
        evaluateYear: null,
        startCreateDate: null,
        createDate: [],
        endCreateDate: null,
        purchaserName: null,
        qltyTypeName: null,
        techTypeName: null
      },
      // 分页条件
      pageConfig: {
        current: 1,
        size: 20
      },
      totalNum: 0,
      orgList: [], // 组织树下拉数组
      listInfo: [], // 查询表格数据
      curYear: '' // 当前查询的年份
    }
  },
  computed: {},
  created() {
    this.getOrgList() //获取组织下拉数据
  },
  mounted() {
    this.getEvaluationYear() //获取默认填充的 ‘评价年份’
    this.init() // 填充数据
  },
  methods: {
    onChange(e) {
      this.searchFormModel.startCreateDate = dayjs(e).format('YYYY-MM-DD')
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgFields, 'dataSource', [...res.data])
          console.log(
            'getStructureListOrgForSpecialgetStructureListOrgForSpecial',
            res.data,
            this.orgFields
          )
        }
      })
    },
    //选择组织
    selectOrg(e) {
      if (e.value.length === 0) {
        this.searchFormModel.orgCode = null
        this.searchFormModel.orgId = null
      }
      this.matchItem(this.orgFields.dataSource, e['value'][0])
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.searchFormModel.orgCode = [ele.orgCode]
          this.searchFormModel.orgId = ele.id
          this.searchFormModel.orgName = ele.name
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    orgChange(e) {
      if (e.value) {
        this.searchFormModel.orgCode = [e.itemData.dimensionCodeValue]
      }
    },

    // 重置
    handleCustomReset() {
      this.$nextTick(() => {
        for (const key in this.searchFormModel) {
          if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
            this.searchFormModel[key] = null
          }
        }
        this.getEvaluationYear()
        this.$forceUpdate()
      })

      console.log('searchFormModelsearchFormModelsearchFormModel', this.$refs.DatePicker)
      // this.searchFormModel.createDate = []
      // this.searchFormModel.startCreateDate = null
      // this.searchFormModel.endCreateDate = null
      this.orgIdArr = []
    },
    // 查询
    init(pageObj) {
      let _params = {
        ...this.searchFormModel,
        page: {
          size: pageObj?.size || 20,
          current: pageObj?.current || 1
        }
      }
      _params.startCreateDate = _params.startCreateDate
        ? dayjs(_params.startCreateDate).format('YYYY-MM-DD')
        : null
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          // this.forecastPageCurrent = 1 // 点击“查询”时重置页码 -- lbj - 2023.05.05
          this.$API.performanceManage.assessResultQuery(_params).then((res) => {
            if (res.code == 200) {
              console.log('重置页码重置页码重置页码', res)
              this.listInfo = res.data.records
              this.totalNum = Number(res.data.total)
              this.curYear = this.searchFormModel.evaluateYear
            }
          })
        }
      })
    },
    // 获取当前评价年份
    getEvaluationYear() {
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()
      const currentMonth = currentDate.getMonth() + 1
      let evaluationYear = ''
      if (currentMonth >= 1 && currentMonth <= 6) {
        evaluationYear = currentYear - 1 + 'H2'
      } else if (currentMonth >= 7 && currentMonth <= 12) {
        evaluationYear = currentYear + 'H1'
      }
      this.searchFormModel.evaluateYear = evaluationYear
    },
    // 输入框事件
    handleInputChange(e, modelName) {
      if (!e) {
        this.searchFormModel[modelName] = null
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        console.log('e.startDate', e.startDate)
        this.searchFormModel['start' + field] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    // 查询
    handleCustomSearch() {
      this.curYear = this.searchFormModel.evaluateYear
      const pageObj = {
        size: 20,
        current: 1
      }
      this.init(pageObj)
      // this.$refs.formInfo.validate((valid) => {
      //   if (valid) {
      //     const params = {
      //       ...this.formInfo
      //     }
      //     this.loading = true
      //     this.$API.categoryResources.getCategoryQueryDimParam(params).then((res) => {
      //       if (res.code === 200) {
      //         this.loading = false
      //         this.listInfo = res.data
      //         const {
      //           buyerSupplierAbilityDimParamResponseList,
      //           buyerSupplierAbilityDimParamDetailResponseList
      //         } = res.data
      //         if (
      //           buyerSupplierAbilityDimParamResponseList &&
      //           buyerSupplierAbilityDimParamDetailResponseList
      //         ) {
      //           this.isShowList = true
      //         }
      //       }
      //     })
      //   }
      // })
    },
    pageChange(pageObj) {
      this.init(pageObj)
    }
  }
}
</script>

<style lang="scss" scoped>
.fullHeight {
  height: 100%;
  background-color: #fff;
}
/deep/ .mt-form .mt-form-item .mt-form-item-label div:nth-child(2) {
  width: inherit;
}
/deep/ .collapse-search-area {
  padding: 16px 0 0 16px;
}
</style>
