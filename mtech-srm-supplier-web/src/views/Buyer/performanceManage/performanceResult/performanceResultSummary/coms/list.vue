<template>
  <div class="list-content">
    <div class="tableArea">
      <vxe-toolbar class="btn-tools">
        <template #buttons>
          <vxe-button @click="handleExport" status="primary" size="mini">{{
            $t('导出')
          }}</vxe-button>
          <vxe-button @click="handlePublish" status="primary" size="mini">{{
            $t('发布')
          }}</vxe-button>
        </template>
      </vxe-toolbar>
      <ScTable
        ref="capabilityXTable"
        :columns="performanceCols"
        :table-data="performanceTableData"
        header-align="center"
        :tree-config="null"
        :auto-height="true"
        :is-show-toolbar="false"
        :need-footer="true"
        align="center"
      >
      </ScTable>
      <vxe-pager
        background
        :current-page="pageConfig.current"
        :page-size="pageConfig.size"
        :total="totalNum"
        @page-change="handlePageChange"
        :layouts="['Total', 'PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes']"
      >
      </vxe-pager>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import initAutoLoad from './mixin'
import { getHeadersFileName, download } from '@/utils/utils.js'

export default {
  components: {
    ScTable
  },
  mixins: [initAutoLoad],

  props: {
    // 动态表的填充数据
    listInfo: {
      type: Array,
      default: () => []
    },
    evaluateYear: {
      type: String,
      default: null
    },
    page: {
      type: Number,
      default: 0
    },
    searchModel: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      performanceTableData: [],
      pageConfig: {
        current: 1,
        size: 20
      },
      loading: false,
      totalNum: 0
    }
  },
  watch: {
    evaluateYear: {
      handler(year) {
        const curYear = year.slice(-2)
        if (curYear === 'H1') {
          const qltIndex = this.performanceCols.findIndex(
            (item) => item.title === this.$t('质量得分')
          )
          const deliverIndex = this.performanceCols.findIndex(
            (item) => item.title === this.$t('交付得分')
          )
          this.performanceCols[qltIndex].children = this.qltyFirstHalfYear
          this.performanceCols[deliverIndex].children = this.deliverFirstHalfYear
        } else if (curYear === 'H2') {
          const qltIndex = this.performanceCols.findIndex(
            (item) => item.title === this.$t('质量得分')
          )
          const deliverIndex = this.performanceCols.findIndex(
            (item) => item.title === this.$t('交付得分')
          )
          this.$nextTick(() => {
            this.performanceCols[qltIndex].children = this.qltySecondHalfYear
            this.performanceCols[deliverIndex].children = this.deliverSecondHalfYear
          })
        }
      },
      deep: true,
      immediate: true
    },
    listInfo: {
      handler(info) {
        this.performanceTableData = this.getList(info)
      },
      deep: true,
      immediate: true
    },
    page: {
      handler(info) {
        this.totalNum = info
      },
      immediate: true
    }
  },
  computed: {},
  mounted() {},
  methods: {
    // 获取页面数据
    getList(info) {
      const newInfo = info.map((item) => {
        item.qltyScoreRow?.forEach((tar) => {
          let key = 'qltyMon' + tar.month
          item[key] = tar.score
          item.qltyAverage = tar.average
          item.qltyRank = tar.rank
        })
        item.deliverScoreRow?.forEach((tar) => {
          let key = 'deliverMon' + tar.month
          item[key] = tar.score
          item.deliverAverage = tar.average
          item.deliverRank = tar.rank
        })
        item.businessRank = item.businessScoreRow?.rank
        item.businessScore = item.businessScoreRow?.score
        item.techRank = item.techScoreRow?.rank
        item.techScore = item.techScoreRow?.score
        return item
      })
      return newInfo
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.pageConfig.current = currentPage
      this.pageConfig.size = pageSize
      this.$emit('pageChange', this.pageConfig)
    },
    // 导出
    handleExport() {
      const _rule = this.searchModel || {}
      let params = {
        page: { current: 1, size: 10000 },
        ..._rule
      }
      this.$API.performanceManage.assessResultExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 发布
    handlePublish() {
      let selectedRecords = this.$refs.capabilityXTable.$refs.xGrid.getCheckboxRecords()
      let ids = selectedRecords.map((v) => v.id)
      let params = {
        ids,
        page: { current: this.pageConfig.current, size: this.pageConfig.size },
        ...this.searchModel
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认发布？')
        },
        success: () => {
          this.$API.performanceManage.publishApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('发布成功'), type: 'success' })
              this.$parent.handleCustomSearch()
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list-content {
  margin: 24px;
  .buttons {
    margin-bottom: 24px;
  }
}
.ddxxys {
  background: #027db4;
  color: red;
}
::v-deep .vxe-table--render-default .vxe-cell {
  padding: 0;
}
::v-deep .vxe-table--render-default.size--small .vxe-header--column {
  padding: 0;
}
</style>
