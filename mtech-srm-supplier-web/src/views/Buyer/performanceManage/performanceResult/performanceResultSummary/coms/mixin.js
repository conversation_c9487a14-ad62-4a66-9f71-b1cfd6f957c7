import { i18n } from '@/main.js'

export default {
  data() {
    return {
      qltyFirstHalfYear: [
        {
          field: 'qltyMon1',
          title: i18n.t('1月'),
          width: 120,
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>1月</div>]
            }
          }
        },
        {
          field: 'qltyMon2',
          title: i18n.t('2月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>2月</div>]
            }
          }
        },
        {
          field: 'qltyMon3',
          title: i18n.t('3月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>3月</div>]
            }
          }
        },
        {
          field: 'qltyMon4',
          title: i18n.t('4月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>4月</div>]
            }
          }
        },
        {
          field: 'qltyMon5',
          title: i18n.t('5月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>5月</div>]
            }
          }
        },
        {
          field: 'qltyMon6',
          title: i18n.t('6月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>6月</div>]
            }
          }
        },
        {
          field: 'qltyAverage',
          title: i18n.t('平均分'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>平均分</div>]
            }
          }
        },
        {
          field: 'qltyRank',
          title: i18n.t('排名'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>排名</div>]
            }
          }
        }
      ],
      qltySecondHalfYear: [
        {
          field: 'qltyMon7',
          title: i18n.t('7月'),
          width: 120,
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>7月</div>]
            }
          }
        },
        {
          field: 'qltyMon8',
          title: i18n.t('8月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>8月</div>]
            }
          }
        },
        {
          field: 'qltyMon9',
          title: i18n.t('9月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>9月</div>]
            }
          }
        },
        {
          field: 'qltyMon10',
          title: i18n.t('10月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>10月</div>]
            }
          }
        },
        {
          field: 'qltyMon11',
          title: i18n.t('11月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>11月</div>]
            }
          }
        },
        {
          field: 'qltyMon12',
          title: i18n.t('12月'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>12月</div>]
            }
          }
        },
        {
          field: 'qltyAverage',
          title: i18n.t('平均分'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>平均分</div>]
            }
          }
        },
        {
          field: 'qltyRank',
          title: i18n.t('排名'),
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>排名</div>]
            }
          }
        }
      ],
      deliverFirstHalfYear: [
        {
          field: 'deliverMon1',
          title: i18n.t('1月'),
          width: 120,
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>1月</div>]
            }
          }
        },
        {
          field: 'deliverMon2',
          title: i18n.t('2月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>2月</div>]
            }
          }
        },
        {
          field: 'deliverMon3',
          title: i18n.t('3月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>3月</div>]
            }
          }
        },
        {
          field: 'deliverMon4',
          title: i18n.t('4月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>4月</div>]
            }
          }
        },
        {
          field: 'deliverMon5',
          title: i18n.t('5月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>5月</div>]
            }
          }
        },
        {
          field: 'deliverMon6',
          title: i18n.t('6月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>6月</div>]
            }
          }
        },
        {
          field: 'deliverAverage',
          title: i18n.t('平均分'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>平均分</div>]
            }
          }
        },
        {
          field: 'deliverRank',
          title: i18n.t('排名'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>排名</div>]
            }
          }
        }
      ],
      deliverSecondHalfYear: [
        {
          field: 'deliverMon7',
          title: i18n.t('7月'),
          width: 120,
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>7月</div>]
            }
          }
        },
        {
          field: 'deliverMon8',
          title: i18n.t('8月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>8月</div>]
            }
          }
        },
        {
          field: 'deliverMon9',
          title: i18n.t('9月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>9月</div>]
            }
          }
        },
        {
          field: 'deliverMon10',
          title: i18n.t('10月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>10月</div>]
            }
          }
        },
        {
          field: 'deliverMon11',
          title: i18n.t('11月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>11月</div>]
            }
          }
        },
        {
          field: 'deliverMon12',
          title: i18n.t('12月'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>12月</div>]
            }
          }
        },
        {
          field: 'deliverAverage',
          title: i18n.t('平均分'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>平均分</div>]
            }
          }
        },
        {
          field: 'deliverRank',
          title: i18n.t('排名'),
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>排名</div>]
            }
          }
        }
      ],
      performanceCols: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          type: 'seq',
          title: i18n.t('序号'),
          width: 70
        },
        {
          field: 'publishStatus',
          title: i18n.t('发布状态'),
          formatter: ({ cellValue }) => {
            let item = [
              { text: i18n.t('未发布'), value: 0 },
              { text: i18n.t('发布中'), value: 1 },
              { text: i18n.t('发布成功'), value: 2 },
              { text: i18n.t('发布失败'), value: 3 }
            ].find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'orgName',
          width: 120,
          title: i18n.t('组织')
        },
        {
          field: 'evaluateYear',
          title: i18n.t('评价年份')
        },
        {
          field: 'categCode',
          title: i18n.t('品类编码')
        },
        {
          field: 'categName',
          title: i18n.t('品类名称')
        },
        {
          field: 'supplierCode',
          title: i18n.t('供应商编码')
        },
        {
          field: 'supplierName',
          title: i18n.t('供应商名称')
        },
        {
          field: 'purchaserName',
          title: i18n.t('采购员')
        },
        {
          field: 'qltyTypeName',
          title: i18n.t('质量分类')
        },
        {
          field: 'techTypeName',
          title: i18n.t('技术分类')
        },
        {
          title: '质量得分',
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>质量得分</div>]
            }
          },
          children: []
        },
        {
          title: '交付得分',
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>交付得分</div>]
            }
          },
          children: []
        },
        {
          title: '技术得分',
          slots: {
            header: () => {
              return [<div style='background-color: #ebf1de'>技术得分</div>]
            }
          },
          children: [
            {
              field: 'techScore',
              title: i18n.t('绩效得分'),
              slots: {
                header: () => {
                  return [<div style='background-color: #ebf1de'>绩效得分</div>]
                }
              }
            },
            {
              field: 'techRank',
              title: i18n.t('排名'),
              slots: {
                header: () => {
                  return [<div style='background-color: #ebf1de'>排名</div>]
                }
              }
            }
          ]
        },
        {
          title: '商务得分',
          slots: {
            header: () => {
              return [<div style='background-color: #dce6f1'>商务得分</div>]
            }
          },
          children: [
            {
              field: 'businessScore',
              title: i18n.t('绩效得分'),
              slots: {
                header: () => {
                  return [<div style='background-color: #dce6f1'>绩效得分</div>]
                }
              }
            },
            {
              field: 'businessRank',
              title: i18n.t('排名'),
              slots: {
                header: () => {
                  return [<div style='background-color: #dce6f1'>排名</div>]
                }
              }
            }
          ]
        },
        {
          field: 'createDate',
          width: 120,
          title: i18n.t('创建时间')
        }
      ]
    }
  }
}
