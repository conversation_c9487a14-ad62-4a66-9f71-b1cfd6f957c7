<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @open="onOpen" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('组织：')">
          <mt-DropDownTree
            :key="fieldsOrg.dataSource.length"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择组织：')"
            :popup-height="500"
            :fields="fieldsOrg"
            @select="selectOrg"
            :allow-filtering="true"
            filter-type="Contains"
            id="baseTreeSelect"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商代码：')">
          <mt-select
            v-model="formObject.supplierCode"
            :data-source="planeArrList"
            :fields="{
              text: 'supplierCode',
              value: 'supplierCode'
            }"
            :allow-filtering="true"
            :show-clear-button="true"
            @change="changePlanCode"
            :placeholder="$t('请选择供应商代码')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称：')">
          <mt-select
            :allow-filtering="true"
            v-model="formObject.supplierName"
            :data-source="planeArrList"
            :fields="{
              text: 'supplierName',
              value: 'supplierName'
            }"
            :show-clear-button="true"
            @change="changePlanName"
            :placeholder="$t('请选择供应商名称：')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryId" class="form-item positive" :label="$t('品类')">
          <mt-DropDownTree
            :fields="categoryListArrList"
            v-model="formObject.categoryId"
            :allow-multi-selection="true"
            :auto-check="true"
            :show-check-box="true"
            filter-type="Contains"
            id="checkboxTreeSelect"
            :placeholder="$t('请选择品类')"
            @input="selectCategoryas"
            :key="categoryListArrList.key"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('新增原因：')">
          <mt-input
            :max-length="100"
            type="text"
            :placeholder="$t('请输入新增原因')"
            v-model="formObject.remark"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="performanceMonth" :label="$t('绩效月份')">
          <mt-date-picker
            v-model="formObject.performanceMonth"
            float-label-type="Never"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :placeholder="$t('请选择月份')"
          ></mt-date-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils.js'
export default {
  data() {
    return {
      editStatus: false,
      // 表单验证
      rules: {
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商代码'),
            trigger: 'blur'
          }
        ],
        supplierName: [
          {
            required: true,
            message: this.$t('请选择供应商名称'),
            trigger: 'blur'
          }
        ],
        categoryId: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: true,
            message: this.$t('请输入新增原因'),
            trigger: 'blur'
          }
        ],
        performanceMonth: [
          {
            required: true,
            message: this.$t('请选择绩效月份'),
            trigger: 'blur'
          }
        ]
      },
      // 弹窗确认事件
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      fieldsOrg: {
        // 组织树下拉数组
        dataSource: [], //下拉数据源
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      planeArrList: [], // 供应商下拉数组
      categoryListArrList: {
        dataSource: [], //品类树下拉数组
        value: 'id',
        text: 'categoryName',
        child: 'childrens',
        key: 'ebb0fe1a-3ecc-44bf-880b-cd2ad5117798'
      }, // 品类下拉数组
      //表单数据
      formObject: {
        orgCode: '', //组织机构编码
        orgId: '', //组织机构id
        orgLevel: '', //组织等级
        orgName: '', //组织机构名称

        orgIdArr: [], //组织机构id

        supplierCode: null, //供应商编码
        supplierId: null, //供应商id
        supplierName: null, //供应商名称

        categoryId: [], //品类

        remark: '', //新增原因
        performanceMonth: '' //绩效月份
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getOrgList() //获取组织树 - 下拉数据
    this.getSupplierList() //获取供应商 - 下拉数据

    this.$refs['dialog'].ejsRef.show() //显示弹窗
    //编辑模式 or 新增模式
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.editConfirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      //编辑数据回显

      // let _data = { ...this.modalData.data }
      // let _data = cloneDeep({

      // })
      // this.formObject = {

      // }
    }
  },
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$refs.dialogRef.validateField('orgIdArr')
      }
    }
  },
  methods: {
    //获取组织树
    getOrgList() {
      this.$API.supplierExcept.treeOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.fieldsOrg.dataSource = res.data
        }
      })
    },
    //选择组织
    selectOrg(e) {
      if (e.itemData) {
        //匹配当前选中的组织 设置到formObject
        this.fn(this.fieldsOrg.dataSource, e.itemData.id)
      }
    },
    // 递归 获取当前选中组织  设置到formObject
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formObject.orgId = ele.id
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgLevel = ele.orgLevel
          this.formObject.orgName = ele.orgName
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.fn(ele.childrenList, id)
        }
      })
    },
    // 获取供应商列表
    getSupplierList() {
      let pamrsform = {
        // fuzzyNameOrCode: 3000158
      }
      this.$API.supplierExcept.getFuzzy(pamrsform).then((result) => {
        if (result.code == 200) {
          this.planeArrList = result.data
        }
      })
    },
    //选择供应商代码
    changePlanCode(e) {
      // 编码和名称联动
      let { itemData } = e
      if (itemData) {
        this.formObject.supplierCode = itemData.supplierCode //供应商编码
        this.formObject.supplierId = itemData.id //供应商id
        this.formObject.supplierName = itemData.supplierName //供应商名称
      } else {
        this.$set(this.categoryListArrList, 'dataSource', [])
        this.$set(this.categoryListArrList, 'key', this.randomString())
        this.formObject.categoryId = [] //清空 品类的选中
      }
    },
    //选择供应商名称
    changePlanName(e) {
      let { itemData } = e
      if (itemData) {
        this.formObject.supplierCode = itemData.supplierCode //供应商编码
        this.formObject.supplierId = itemData.id //供应商id
        this.formObject.supplierName = itemData.supplierName //供应商名称
        this.$nextTick(() => {
          this.getCategoryList(itemData) //请求品类的下拉数据
        })
      } else {
        this.$set(this.categoryListArrList, 'dataSource', [])
        this.$set(this.categoryListArrList, 'key', this.randomString())
        this.formObject.categoryId = [] //清空 品类的选中
      }
    },
    // 选择品类
    selectCategoryas(e) {
      console.log(e)
    },
    // 获取品类列表
    getCategoryList(data) {
      this.$API.supplierExcept
        .getCategory({
          supplierCode: data.supplierCode, //供应商编码
          supplierId: data.id, //供应商id
          supplierName: data.supplierName //供应商名称
        })
        .then((result) => {
          if (result.code === 200 && !utils.isEmpty(result.data)) {
            this.$set(this.categoryListArrList, 'dataSource', [...result.data])
            this.$set(this.categoryListArrList, 'key', this.randomString())
          } else {
            this.$set(this.categoryListArrList, 'dataSource', [])
            this.$set(this.categoryListArrList, 'key', this.randomString())
          }
        })
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },

    // 确认按钮 - 新增
    confirm() {
      console.log(this.formObject)
      //气泡询问框 是否保存
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('XXX月份绩效已审批，新增后不能重新计算绩效!是否保存？')
        },
        success: () => {}
      })
    },
    // 确认按钮 - 编辑
    editConfirm() {
      console.log(this.formObject)
      console.log(JSON.stringify(this.formObject))
    },
    cancel() {
      this.$emit('cancel-function')
    },
    onOpen(args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
}
</style>
