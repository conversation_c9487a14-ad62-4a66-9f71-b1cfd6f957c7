// 分类绩效查询
import { i18n } from '@/main.js'
export const toolbar = [
  {
    text: i18n.t('导出'),
    id: 'export',
    prefixIcon: 'e-excelexport'
  }
]
export const columnData = [
  {
    field: 'assessMonth',
    headerText: i18n.t('绩效月份')
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },

  {
    field: 'templateName',
    headerText: i18n.t('模板类型')
  },
  {
    field: 'dimensionName',
    headerText: i18n.t('指标维度评价分类')
  },
  {
    field: 'score',
    headerText: i18n.t('绩效得分')
  },
  {
    field: 'rank',
    headerText: i18n.t('绩效排名')
  },
  {
    field: 'levelCode',
    headerText: i18n.t('分类结果')
  }
]
export const antdColumns = [
  {
    title: i18n.t('绩效月份'),
    dataIndex: 'assessMonth',
    align: 'left',
    key: 'assessMonth'
  },
  {
    dataIndex: 'orgName',
    key: 'orgName',
    align: 'left',
    title: i18n.t('组织')
  },
  {
    dataIndex: 'categoryName',
    key: 'categoryName',
    align: 'left',
    title: i18n.t('品类名称')
  },
  {
    dataIndex: 'supplierCode',
    key: 'supplierCode',
    align: 'left',
    title: i18n.t('供应商编码')
  },
  {
    dataIndex: 'supplierName',
    key: 'supplierName',
    align: 'left',
    title: i18n.t('供应商名称')
  },

  {
    dataIndex: 'templateName',
    key: 'templateName',
    align: 'left',
    title: i18n.t('模板类型')
  },
  {
    dataIndex: 'dimensionName',
    key: 'dimensionName',
    align: 'left',
    title: i18n.t('指标维度评价分类')
  },
  {
    dataIndex: 'comprehensiveScore',
    key: 'comprehensiveScore',
    align: 'left',
    title: i18n.t('综合得分')
  },
  {
    dataIndex: 'comprehensiveRank',
    key: 'comprehensiveRank',
    align: 'left',
    title: i18n.t('综合排名')
  },
  {
    dataIndex: 'score',
    key: 'score',
    align: 'left',
    title: i18n.t('指标维度评价得分')
  },
  {
    dataIndex: 'rank',
    key: 'rank',
    align: 'left',
    title: i18n.t('指标维度排名')
  },
  {
    dataIndex: 'levelCode',
    key: 'levelCode',
    align: 'left',
    title: i18n.t('分类结果')
  }
]
