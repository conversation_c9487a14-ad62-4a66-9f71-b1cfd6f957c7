<template>
  <div class="comprehensive-container">
    <!-- 搜索区域 -->
    <collapse-search :is-grid-display="true" @reset="handleClear" @search="handleSearch">
      <mt-form ref="listForm" :model="listForm" :rules="formRules">
        <mt-form-item prop="templateType" :label="$t('模板类型')" label-style="top">
          <mt-select
            :allow-filtering="true"
            v-model="listForm.templateType"
            :data-source="templateTypeList"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :show-clear-button="true"
            @change="selectPlan"
            :placeholder="$t('请选择模板类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="dimensionName" :label="$t('指标维度评价分类')" label-style="top">
          <mt-select
            :allow-filtering="true"
            v-model="listForm.dimensionName"
            :data-source="dimensionList"
            :show-clear-button="true"
            @change="selectPlan"
            :placeholder="$t('请选择指标维度评价分类')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="assessCycle" :label="$t('评价周期')" label-style="top">
          <mt-select
            v-model="listForm.assessCycle"
            :data-source="assessCycleData"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :show-clear-button="true"
            :placeholder="$t('请选择评价周期')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="startDate" :label="$t('起始月份')" label-style="top">
          <mt-date-picker
            v-model="listForm.startDate"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            @change="dateStartChange"
            :placeholder="$t('请选择起始月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDate" :label="$t('终止月份')" label-style="top">
          <mt-date-picker
            v-model="listForm.endDate"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :disabled="true"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :placeholder="$t('请选择终止月份')"
          ></mt-date-picker>
        </mt-form-item>

        <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
          <mt-DropDownTree
            :key="fieldsarr.dataSource.length"
            v-model="listForm.orgIdArr"
            :placeholder="$t('请选择组织')"
            :popup-height="500"
            :fields="fieldsarr"
            @select="selectOrg"
            :allow-filtering="true"
            filter-type="Contains"
            id="baseTreeSelect"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="listForm.supplierName"
            :max-length="32"
            :placeholder="$t('请输入供应商名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="listForm.supplierCode"
            :max-length="32"
            :placeholder="$t('请输入供应商编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类')" label-style="top">
          <mt-input
            v-model="listForm.categoryName"
            :max-length="32"
            :placeholder="$t('品类')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="button-group">
      <icon-button
        icon="icon_solid_export"
        :text="$t('导出')"
        @click="handleClickToolBar('export')"
      />
    </div>
    <!-- 表格 -->
    <!-- :toolbar="toolbar"
        @toolbarClick="handleClickToolBar" -->
    <!-- <mt-data-grid
      ref="dataGrid"
      :column-data="columnData"
      :selection-settings="{ checkboxOnly: true }"
      :data-source="dataSource"
      :allow-sorting="true"
      :allow-scrolling="true"
      :allow-paging="true"
      :page-settings="pageSettings"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    >
    </mt-data-grid> -->
    <div>
      <a-locale-provider :locale="locale">
        <a-table
          :columns="antdColumns"
          :data-source="dataSource"
          :pagination="pagination"
          @change="handleTableChange"
        />
      </a-locale-provider>
    </div>
  </div>
</template>

<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import collapseSearch from '@/components/collapseSearch'
import { toolbar, columnData, antdColumns } from './config'
import utils from '@/utils/utils'
import { getHeadersFileName, download } from '@/utils/utils'
import iconButton from '@/components/iconButton/index.vue'
import dayjs from 'dayjs'
export default {
  components: {
    collapseSearch,
    iconButton
  },
  data() {
    return {
      locale: zhCN,
      toolbar,
      columnData,
      antdColumns,
      dataSource: [], // 表格数据
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        pageSizeOptions: [10, 20, 50, 100, 200],
        showTotal: (total) => `共 ${total} 条数据`,
        onShowSizeChange: (current, pageSize) => (this.pageSize = pageSize),
        showSizeChanger: true,
        showQuickJumper: true
      },
      // pageSettings: {
      //   // 分页配置
      //   currentPage: 1,
      //   pageSize: 10,
      //   totalRecordsCount: 0,
      //   pageSizes: [10, 20, 50]
      // },
      //组织树下拉数组
      fieldsarr: {
        dataSource: [],
        value: 'dimensionIdValue',
        text: 'dimensionNameValue',
        child: 'childrenList'
      },
      //模板类型下拉数据
      templateTypeList: [
        { id: '1557921006855299073', itemCode: 'TOTAL', itemName: this.$t('综合') },
        { id: '1557921197138288641', itemCode: 'QUALITY', itemName: this.$t('质量') },
        { id: '1557921371545837570', itemCode: 'DELIVER', itemName: this.$t('交付') },
        { id: '1557921524440801281', itemCode: 'BUSINESS', itemName: this.$t('商务') },
        { id: '1557921703533387777', itemCode: 'TECH', itemName: this.$t('技术') }
      ],
      //评价周期下拉数据
      assessCycleData: [
        { id: '1557926315917221889', itemCode: 'MONTH', itemName: this.$t('月度') },
        { id: '1557926394157768706', itemCode: 'YEAR', itemName: this.$t('年度') },
        { id: '1557926510608424962', itemCode: 'HALF-YEAR', itemName: this.$t('半年度') },
        { id: '1558293629280321538', itemCode: 'SEASON', itemName: this.$t('季度') }
      ], //评价周期
      dimensionList: [], //指标维度评价分类数据
      listForm: {
        orgIdArr: null,
        orgId: null, //组织Id
        orgCode: null, //组织Code
        orgName: null, //组织名称
        startDate: null, //起始月份
        endDate: null, //终止月份
        supplierName: null, //供应商名称
        supplierCode: null, //供应商编码
        categoryName: null, //品类
        assessCycle: null, //评价周期
        templateType: null, //模板类型
        dimensionName: null //指标维度评价分类
      },
      formRules: {
        startDate: [
          {
            required: true,
            message: this.$t('请选择绩效起始月份'),
            trigger: 'blur'
          }
        ],
        templateType: [
          {
            required: true,
            message: this.$t('请选择模板类型'),
            trigger: 'blur'
          }
        ],
        dimensionName: [
          {
            required: true,
            message: this.$t('请选择指标维度评价分类'),
            trigger: 'blur'
          }
        ],
        assessCycle: [
          {
            required: true,
            message: this.$t('请选择评价周期'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    'listForm.assessCycle': {
      handler() {
        if (!this.listForm.startDate) return
        this.listForm.endDate = this.ctrlDateTime(this.listForm.startDate, 'dateStart')
      }
    }
  },
  async mounted() {
    this.getOrgList() //获取组织下拉数据
    this.getDimensionList() //获取指标维度评价分类
  },
  methods: {
    handleTableChange(pagination) {
      console.log(pagination)
      const pager = { ...this.pagination }
      pager.current = pagination.current
      pager.pageSize = pagination.pageSize
      this.pagination = pager
      this.handleSearch()
    },
    //获取指标维度评价分类下拉值
    getDimensionList() {
      this.$API.supplierClassificationQuery.getDimensionNameList().then((res) => {
        this.dimensionList = res.data
      })
    },
    //获取组织树
    getOrgList() {
      this.$API.supplierClassificationQuery.getPermissionOrgList().then((res) => {
        if (res.code == 200) {
          this.fieldsarr.dataSource = res.data
        }
      })
    },
    //选择组织
    selectOrg(e) {
      if (e.itemData) {
        //匹配当前选中的组织 设置到formObject
        this.fn(this.fieldsarr.dataSource, e.itemData.id)
      }
    },
    // 递归 获取当前选中组织  设置到formObject
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.dimensionIdValue == id) {
          this.listForm.orgId = ele.dimensionIdValue
          this.listForm.orgCode = ele.dimensionCodeValue
          this.listForm.orgName = ele.dimensionNameValue
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.fn(ele.childrenList, id)
        }
      })
    },
    selectPlan() {},
    //查询参数处理
    getParams() {
      // 获取起始日期月份的数字
      let month = new Date(this.listForm.startDate).getMonth() + 1
      // 获取评价周期
      let evaluationCycle = this.listForm.assessCycle
      if (evaluationCycle === 'YEAR' && month != 1) {
        this.$toast({
          content: this.$t('评价周期为年度时，绩效起始月份只可选择1月'),
          type: 'warning'
        })
        return false
      } else if (evaluationCycle === 'HALF-YEAR' && month != 1 && month != 7) {
        this.$toast({
          content: this.$t('评价周期为半年度时，绩效起始月份只可选择1月或7月'),
          type: 'warning'
        })
        return false
      } else if (
        evaluationCycle === 'SEASON' &&
        month != 1 &&
        month != 4 &&
        month != 7 &&
        month != 10
      ) {
        this.$toast({
          content: this.$t('评价周期为季度时，绩效起始月份只可选择1月、4月、7月或10月'),
          type: 'warning'
        })
        return false
      }
      //开始时间 结束时间 转换为毫秒时间戳
      const params = {
        ...this.listForm,
        endDate: dayjs(dayjs(this.listForm.endDate).format('YYYY-MM')).valueOf(), //结束月份 转为毫秒时间戳
        startDate: dayjs(dayjs(this.listForm.startDate).format('YYYY-MM')).valueOf(), //起始月份
        page: {
          current: this.pagination.current,
          size: this.pagination.pageSize
        }
      }
      //删除orgIdArr
      delete params.orgIdArr
      return params
    },
    //查询
    handleSearch() {
      this.$refs.listForm.validate((valid) => {
        if (valid) {
          // 请求表格数据
          let params = this.getParams()
          this.$API.supplierClassificationQuery.pageQuery(params).then((res) => {
            if (res.code === 200) {
              this.dataSource = res.data.records
              // this.pageSettings = {
              //   ...this.pageSettings,
              //   currentPage: res.data.current,
              //   pageSize: res.data.size,
              //   totalRecordsCount: res.data.total
              // }
              this.pagination = {
                ...this.pagination,
                current: Number(res.data.current),
                pageSize: Number(res.data.size),
                total: Number(res.data.total)
              }
            }
          })
        }
      })
    },
    //重置查询参数
    handleClear() {
      this.listForm = {
        orgIdArr: null,
        orgId: null, //组织Id
        orgCode: null, //组织Code
        orgName: null, //组织名称
        startDate: null, //起始月份
        endDate: null, //终止月份
        supplierName: null, //供应商名称
        supplierCode: null, //供应商编码
        categoryName: null, //品类
        assessCycle: null, //评价周期
        templateType: null, //模板类型
        dimensionName: null //指标维度评价分类
      }
      this.dataSource = []
      // this.pageSettings = {
      //   ...this.pageSettings,
      //   currentPage: 1,
      //   pageSize: 10,
      //   totalRecordsCount: 0
      // }
      this.pagination = {
        ...this.pagination,
        current: 1,
        pageSize: 10,
        total: 0
      }
    },
    //导出
    EXdownload() {
      this.$refs.listForm.validate((valid) => {
        if (valid) {
          // 请求表格数据
          let params = this.getParams()
          this.$API.supplierClassificationQuery.export(params).then((res) => {
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
            this.$toast({
              content: this.$t('导出成功'),
              type: 'success'
            })
          })
        }
      })
    },

    // // 分页的两个方法
    // handleCurrentChange(currentPage) {
    //   this.pageSettings.currentPage = currentPage
    //   this.handleSearch()
    // },
    // handleSizeChange(pageSize) {
    //   this.pageSettings.pageSize = pageSize
    //   this.handleSearch()
    // },
    //工具栏点击事件
    handleClickToolBar(id) {
      if (id === 'export') {
        //导出
        this.EXdownload()
      }
    },
    //起始月份 change
    dateStartChange(date) {
      this.listForm.endDate = this.ctrlDateTime(date, 'dateStart')
    },
    ctrlDateTime(date, type) {
      const _period = this.listForm.assessCycle
      const _date = utils.formateTime(date, 'yyyy-MM')
      let _time = ''

      switch (_period) {
        case 'MONTH':
          _time = this.calculateDate(_date, 0, type)
          break
        case 'YEAR':
          _time = this.calculateDate(_date, 11, type)
          break
        case 'HALF-YEAR':
          _time = this.calculateDate(_date, 5, type)
          break
        case 'SEASON':
          _time = this.calculateDate(_date, 2, type)
          break
        default:
          _time = this.calculateDate(_date, 0, type)
      }
      return _time
    },
    calculateDate(date, num, type) {
      let arr = date.split('-')
      let year = parseInt(arr[0])
      let month = parseInt(arr[1])
      month = type === 'dateStart' ? month + num : month - num
      if (month > 12) {
        let yearNum = parseInt((month - 1) / 12)
        month = month % 12 == 0 ? 12 : month % 12
        year += yearNum
      } else if (month <= 0) {
        month = Math.abs(month)
        let yearNum = parseInt((month + 12) / 12)
        let n = month % 12
        if (n == 0) {
          year -= yearNum
          month = 12
        } else {
          year -= yearNum
          month = Math.abs(12 - n)
        }
      }
      month = month < 10 ? '0' + month : month
      return year + '-' + month
    }
  }
}
</script>

<style lang="scss" scoped>
.index-box {
  padding: 5px;
  background-color: #fff;
  border-radius: 4px;
  // box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  // margin-bottom: 20px;
  height: 100%;
}
.main-info {
  padding-top: 10px;
}
.operate-bars {
  width: 100%;
  height: 190px;
  margin: 5px 16px 0 16px;
  padding-bottom: 8px;
  background: #f5f5f5;
  border-radius: 8px;
  .operate-bar {
    // height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #4f5b6d;
    .op-item {
      cursor: pointer;
      align-items: center;
      margin-right: 20px;
      align-items: center;
      background-color: rgba(0, 0, 0, 0);
      border-color: rgba(0, 0, 0, 0);
      color: #00469c;
    }
  }
}
.operation-btn-wrap {
  display: inline-block;
  width: 15%;
  text-align: right;
}
/deep/ {
  .mt-select-index {
    float: left;
  }
  .mt-input-number-active .input--wrap::before {
    bottom: -2px;
  }
  .mt-input {
    width: 100%;
  }
  .mt-template-page {
    /deep/ .repeat-template {
      padding: 20px;
    }
  }
  .mt-input-number input {
    height: 30px;
    width: 100%;
    border: unset;
    border-bottom: 1px solid rgba(0, 0, 0, 0.42);
    outline: unset !important;
    background: transparent;
    margin-bottom: 4px;
  }
  .mt-input-number input:disabled {
    background: #fafafa;
    background-image: none;
    background-position: initial;
    background-repeat: no-repeat;
    background-size: 0;
    border-color: rgba(0, 0, 0, 0.06);
    color: rgba(0, 0, 0, 0.38);
  }
}
.score-setting {
  height: calc(100% - 200px);
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
.comprehensive-container {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  /deep/.ant-table {
    overflow-x: auto !important;
  }
}
.button-group {
  display: flex;
  justify-content: flex-start;
  padding: 0;
  margin: 15px 0 10px 0;
}
.search-input {
  padding: 10px 0 16px;
  .toggle-tag {
    padding-right: 15px;
    color: #2783fe;
    display: inline-block;
    font-size: 12px;
    position: relative;
    cursor: pointer;
    user-select: none;
    .mt-icons {
      font-size: 12px;
      position: absolute;
      transform: scale(0.4);
      top: -2px;
      left: 26px;
      &:nth-child(2) {
        top: 2px;
      }
    }
  }
  .search-area {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 16px 24px 12px;
    margin-top: 12px;
    .button-group {
      padding-top: 10px;
      span {
        margin-right: 16px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #2783fe;
        cursor: pointer;
        user-select: none;
      }
    }
  }
}
::v-deep {
  .button-group span {
    height: auto;
    padding: 0;
    margin-left: 0;
    border: none;
  }
}
</style>
