<template>
  <div>
    <div class="comprehensive-container">
      <!-- 搜索区域 -->
      <collapse-search @reset="reset" @search="search" :is-grid-display="true">
        <mt-form ref="ruleForm" :model="queryForm" :rules="rules" :validate-on-rule-change="false">
          <mt-form-item prop="templateId" :label="$t('绩效模板')" label-style="top">
            <mt-select
              v-model="queryForm.templateId"
              float-label-type="Never"
              :allow-filtering="true"
              :filtering="queryTemplateList"
              :data-source="templateArrList"
              :fields="selectFields"
              :open-dispatch-change="false"
              :placeholder="$t('绩效模板')"
              @change="templateChange"
              @open="queryTemplateList"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="startDate" :label="$t('起始月份')" label-style="top">
            <mt-date-picker
              v-model="queryForm.startDate"
              float-label-type="Never"
              start="Year"
              depth="Year"
              format="yyyy-MM"
              :allow-edit="false"
              :open-on-focus="true"
              :show-today-button="false"
              :placeholder="$t('请选择月份')"
              @change="startDateChange"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item prop="endDate" :label="$t('终止月份')" label-style="top">
            <mt-date-picker
              v-model="queryForm.endDate"
              float-label-type="Never"
              start="Year"
              depth="Year"
              format="yyyy-MM"
              disabled
              :allow-edit="false"
              :open-on-focus="true"
              :show-today-button="false"
              :placeholder="$t('请选择月份')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item prop="orgCode" :label="$t('组织')" label-style="top">
            <mt-DropDownTree
              v-if="fieldsarr.dataSource.length"
              v-model="queryForm.orgCode"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择组织：')"
              :popup-height="500"
              :fields="fieldsarr"
              @select="selectCompany"
              id="baseTreeSelect"
            ></mt-DropDownTree>
            <mt-select v-else :placeholder="$t('请选择组织')" :data-source="[]"></mt-select>
          </mt-form-item>
          <mt-form-item prop="assessCycleName" :label="$t('评价周期')" label-style="top">
            <mt-input
              v-model="queryForm.assessCycleName"
              :show-clear-button="false"
              disabled
              :placeholder="$t('评价周期')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            v-if="isPurchase"
            prop="supplierName"
            :label="$t('供应商名称')"
            label-style="top"
          >
            <mt-input
              v-model="queryForm.supplierName"
              :show-clear-button="false"
              :placeholder="$t('供应商名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            v-if="isPurchase"
            prop="supplierCode"
            :label="$t('供应商编码')"
            label-style="top"
          >
            <mt-input
              v-model="queryForm.supplierCode"
              :show-clear-button="false"
              :placeholder="$t('供应商编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
            <mt-input
              v-model="queryForm.categoryName"
              :show-clear-button="false"
              :placeholder="$t('品类名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            v-if="isPurchase"
            prop="publishStatus"
            :label="$t('发布状态')"
            label-style="top"
          >
            <mt-select
              v-model="queryForm.publishStatus"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="pubStatusOptions"
              :fields="{ text: 'label', value: 'status' }"
              :placeholder="$t('发布状态')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            v-if="isPurchase"
            prop="approvalStatus"
            :label="$t('审批状态')"
            label-style="top"
          >
            <mt-select
              v-model="queryForm.approvalStatus"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="statusOptions"
              :fields="{ text: 'label', value: 'status' }"
              :placeholder="$t('审批状态')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </collapse-search>
    </div>
    <!-- 详细表格区域 -->
    <div>
      <!-- 表格 -->
      <!-- :toolbar="toolbarOptions" -->
      <!-- @toolbarClick="toolbarClick" -->
      <div class="button-group">
        <icon-button icon="icon_solid_export" :text="$t('导出')" @click="toolbarClick('export')" />
        <icon-button
          v-if="isPurchase"
          icon="icon_solid_Createproject"
          :text="$t('发布')"
          @click="toolbarClick('publish')"
        />
        <icon-button
          v-if="isPurchase"
          icon="icon_solid_upload"
          :text="$t('提交审批')"
          @click="toolbarClick('submit')"
        />
        <icon-button
          icon="icon_solid_export"
          :text="$t('导出综合绩效得分明细')"
          @click="toolbarClick('exportDetail')"
        />
      </div>
      <mt-data-grid
        ref="dataGrid"
        :column-data="columnData"
        :selection-settings="{ checkboxOnly: true }"
        :data-source="dataSource"
        :allow-sorting="true"
        :allow-scrolling="true"
        :allow-paging="true"
        height="400"
        :page-settings="pageSettings"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      >
      </mt-data-grid>
    </div>
  </div>
</template>

<script>
import collapseSearch from '@/components/collapseSearch'
import { columnData, statusOptions, pubStatusOptions, toolbarOptions } from './config'
import utils from '@/utils/utils'
import { download } from '@/utils/utils'
import iconButton from '@/components/iconButton/index.vue'

export default {
  components: {
    collapseSearch,
    iconButton
  },
  data() {
    return {
      gridHeight: '30%',
      fieldsarr: {
        dataSource: [], // 组织树下拉数组
        value: 'dimensionCodeValue',
        text: 'dimensionNameValue',
        child: 'childrenList'
      },
      selectFields: { text: 'templateName', value: 'id' },
      templateArrList: [], // 模板下来列表
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50]
      },
      dataSource: [], // 表格数据
      toolbarOptions: [], // 表格上方工具栏
      isPurchase: false, // 判断是是否为采方页面
      columnData: [], // 表格列配置
      statusOptions,
      pubStatusOptions,
      queryForm: {
        orgCode: '', // 组织
        startDate: '', // 起始月份
        endDate: '', // 终止月份
        templateId: '', // 绩效模板
        assessCycle: '', // 评价周期
        assessCycleName: '', // 评价周期
        supplierName: '', // 供应商名称
        supplierCode: '', // 供应商编码
        categoryName: '', // 品类名称
        publishStatus: '', // 发布状态
        approvalStatus: '' // 审批状态
      },
      rules: {
        startDate: [{ required: true, message: this.$t('请选择起始月份'), trigger: 'blur' }],
        endDate: [{ required: true, message: this.$t('请选择终止月份'), trigger: 'blur' }],
        templateId: [{ required: true, message: this.$t('请选择绩效模板'), trigger: 'blur' }]
      },
      circleOptions: [
        { label: this.$t('月度'), value: '0' },
        { label: this.$t('半年度'), value: '1' },
        { label: this.$t('年度'), value: '2' }
      ]
    }
  },
  computed: {
    urlParams() {
      return utils.getUrlParams()
    }
  },
  watch: {
    $route: {
      handler: 'setIsPurchase',
      immediate: true
    }
  },
  created() {
    this.TreeByAccount()
    this.queryTemplateList = utils.debounce(this.queryTemplateList, 500)
    this.queryTemplateList()
  },
  mounted() {
    if (Object.keys(this.urlParams).length) {
      this.queryForm = { ...this.queryForm, ...this.urlParams }
      this.queryForm.startDate = utils.timestampToDate(this.queryForm.startDate, 'month')
      this.queryForm.endDate = utils.timestampToDate(this.queryForm.endDate, 'month')
      this.$nextTick(() => {
        this.search()
      })
    }
  },
  methods: {
    startDateChange(date) {
      this.queryForm.endDate = this.ctrlDateTime(date, 'dateStart')
    },
    ctrlDateTime(date, type) {
      const _period = this.queryForm.assessCycle
      const _date = utils.formateTime(date, 'yyyy-MM')
      let _time = ''

      switch (_period) {
        case 'MONTH':
          _time = this.calculateDate(_date, 0, type)
          break
        case 'YEAR':
          _time = this.calculateDate(_date, 11, type)
          break
        case 'HALF-YEAR':
          _time = this.calculateDate(_date, 5, type)
          break
        case 'SEASON':
          _time = this.calculateDate(_date, 2, type)
          break
        default:
          _time = this.calculateDate(_date, 0, type)
      }
      return _time
    },
    calculateDate(date, num, type) {
      let arr = date.split('-')
      let year = parseInt(arr[0])
      let month = parseInt(arr[1])
      month = type === 'dateStart' ? month + num : month - num
      if (month > 12) {
        let yearNum = parseInt((month - 1) / 12)
        month = month % 12 == 0 ? 12 : month % 12
        year += yearNum
      } else if (month <= 0) {
        month = Math.abs(month)
        let yearNum = parseInt((month + 12) / 12)
        let n = month % 12
        if (n == 0) {
          year -= yearNum
          month = 12
        } else {
          year -= yearNum
          month = Math.abs(12 - n)
        }
      }
      month = month < 10 ? '0' + month : month
      return year + '-' + month
    },
    templateChange(e) {
      const { itemData } = e
      this.queryForm.assessCycle = itemData?.evaluationCycleCode
      this.queryForm.assessCycleName = itemData?.evaluationCycleName
      this.queryForm.startDate = ''
      this.queryForm.endDate = ''
    },
    // 获取绩效模板列表
    queryTemplateList(e) {
      let params = {
        templateName: e.text || ''
      }
      this.getQueryTemplateListApi()(params).then((res) => {
        if (res.code == 200) {
          let value = 'templateId'
          if (this.isPurchase) {
            value = 'id'
          }
          this.selectFields = { text: 'templateName', value }
          this.templateArrList = res.data
          if (e?.updateData) {
            this.$nextTick(() => {
              e.updateData(this.templateArrList)
            })
          }
          if (this.queryForm.templateId) {
            const curItem = this.templateArrList.find(
              (item) => item.id === this.queryForm.templateId
            )
            if (curItem) {
              this.queryForm.assessCycle = curItem?.evaluationCycleCode
              this.queryForm.assessCycleName = curItem?.evaluationCycleName
            }
          }
        }
      })
    },
    getQueryTemplateListApi() {
      if (this.isPurchase) {
        return this.$API.performanceManage.getPermissionTemplateList
      }
      return this.$API.performanceManage.getSupPermissionTemplateList
    },
    // 初始化获取组织列表
    TreeByAccount() {
      this.getTreeByAccountApi()().then((res) => {
        if (res.code == 200) {
          let value = 'orgId'
          let text = 'orgName'
          if (this.isPurchase) {
            value = 'dimensionCodeValue'
            text = 'dimensionNameValue'
          }
          this.$set(this.fieldsarr, 'value', value)
          this.$set(this.fieldsarr, 'text', text)
          this.$set(this.fieldsarr, 'dataSource', [...res.data])
        }
      })
    },
    getTreeByAccountApi() {
      if (this.isPurchase) {
        return this.$API.performanceManage.getPermissionOrgList
      }
      return this.$API.performanceManage.getSupPermissionOrgList
    },
    setIsPurchase() {
      // 根据当前路由判断是否更改采方页面判断开关
      this.isPurchase = this.$route.path === '/supplier/performance-result/pur/comprehensive-query'
      this.toolbarOptions = toolbarOptions(this.isPurchase)
      let arr = []
      if (this.dataSource.length) {
        arr = this.dataSource[0]['dimensionScoreInfo']
      }
      this.columnData = columnData(this.isPurchase, arr)
    },
    reset() {
      this.queryForm = {
        orgCode: '', // 组织
        startDate: '', // 起始月份
        endDate: '', // 终止月份
        templateId: '', // 绩效模板
        assessCycle: '', // 评价周期
        assessCycleName: '', // 评价周期
        supplierName: '', // 供应商名称
        supplierCode: '', // 供应商编码
        categoryName: '', // 品类名称
        publishStatus: '', // 发布状态
        approvalStatus: '' // 审批状态
      }
      this.dataSource = []
      this.TreeByAccount()
      this.queryTemplateList()
      // this.search()
    },
    // 选择组织
    selectCompany(e) {
      if (e.itemData) {
        // let { itemData } = e
        // this.fn(this.fieldsarr.dataSource, itemData.id)
      }
    },
    getParams() {
      const params = {
        ...this.queryForm,
        // endDate: utils.formateTime(this.queryForm.endDate, 'yyyy-MM') + '-01',
        // startDate: utils.formateTime(this.queryForm.startDate, 'yyyy-MM') + '-01',
        endDate: new Date(utils.formateTime(this.queryForm.endDate, 'yyyy-MM')).getTime(),
        startDate: new Date(utils.formateTime(this.queryForm.startDate, 'yyyy-MM')).getTime(),
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      if (this.queryForm.orgCode && this.queryForm.orgCode.length) {
        params.orgCode = this.queryForm.orgCode[0]
      } else {
        params.orgCode = ''
      }
      // 获取起始日期月份的数字
      let month = new Date(this.queryForm.startDate).getMonth() + 1
      // 获取评价周期
      let evaluationCycle = this.queryForm.assessCycle
      if (evaluationCycle === 'YEAR' && month != 1) {
        this.$toast({
          content: this.$t('评价周期为年度时，绩效起始月份只可选择1月'),
          type: 'warning'
        })
        return false
      } else if (evaluationCycle === 'HALF-YEAR' && month != 1 && month != 7) {
        this.$toast({
          content: this.$t('评价周期为半年度时，绩效起始月份只可选择1月或7月'),
          type: 'warning'
        })
        return false
      } else if (
        evaluationCycle === 'SEASON' &&
        month != 1 &&
        month != 4 &&
        month != 7 &&
        month != 10
      ) {
        this.$toast({
          content: this.$t('评价周期为季度时，绩效起始月份只可选择1月、4月、7月或10月'),
          type: 'warning'
        })
        return false
      }
      return params
    },
    search() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 请求表格数据
          if (!this.getParams()) {
            return false
          }
          let params = this.getParams()
          this.getSearchApi()(params).then((res) => {
            if (res.code === 200) {
              if (res.data.total != '0') {
                // 根据请求的数据设置动态列
                this.columnData = columnData(
                  this.isPurchase,
                  res.data.records[0]['dimensionScoreInfo']
                )
              }
              this.dataSource = res.data.records
              this.pageSettings = {
                ...this.pageSettings,
                currentPage: Number(res.data.current || 0),
                pageSize: Number(res.data.size || 0),
                totalRecordsCount: Number(res.data.total || 0)
              }
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getSearchApi() {
      if (this.isPurchase) {
        return this.$API.performanceManage.getComprehensiveResult
      }
      return this.$API.performanceManage.getSupComprehensiveResult
    },
    // 分页的两个方法
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.search()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    // 表格顶部工具栏按钮
    toolbarClick(id) {
      const selectedRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      // if (args.item.id !== 'export' && selectedRecords.length > 1) {
      //   this.$toast({ content: this.$t('暂只支持操作单行'), type: 'warning' })
      //   return
      // }
      if ((id === 'publish' || id === 'submit') && selectedRecords.length === 0) {
        this.$toast({ content: this.$t('至少勾选一行数据进行操作'), type: 'warning' })
        return
      }
      if (id === 'export') {
        // 点击导出
        this.exportList()
      } else if (id === 'publish') {
        // 点击发布
        this.publish(selectedRecords)
      } else if (id === 'submit') {
        // 点击提交审批
        this.submitApprove(selectedRecords)
      } else if (id === 'exportDetail') {
        // 点击导出综合绩效得分明细
        this.exportDetail(selectedRecords)
      }
    },
    // 导出按钮
    exportList() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 请求表格数据
          let params = {}
          if (this.getParams()) {
            params = this.getParams()
          }
          this.getExportListApi()(params).then((res) => {
            const { data, headers } = res
            download({
              // fileName: '模板清单.xlsx',
              fileName: decodeURI(headers['content-disposition'].split('=')[1]),
              blob: data
            })
            this.$toast({
              content: this.$t('导出成功'),
              type: 'success'
            })
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getExportListApi() {
      if (this.isPurchase) {
        return this.$API.performanceManage.exportComprehensiveResult
      }
      return this.$API.performanceManage.exportSupComprehensiveResult
    },
    // 发布按钮
    publish(selectedRecords) {
      // 校验审批状态为已审批，发布状态为未发布的数据
      if (selectedRecords.some((i) => i.approvalStatus != 3 || i.publishStatus != 5)) {
        this.$toast({
          content: this.$t('发布状态非未发布，审批状态非已审批不能发布'),
          type: 'success'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认发布所选数据吗？')
        },
        success: () => {
          const ids = selectedRecords.map((item) => item.id)
          // 调发布接口
          this.$API.performanceManage.publishComprehensiveResult(ids).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.search()
            }
          })
        }
      })
    },
    // 提交审批按钮
    submitApprove(selectedRecords) {
      // 校验审批状态是未审批的数据，可以提交OA审批
      if (selectedRecords.some((i) => i.approvalStatus != 1 && i.approvalStatus != 4)) {
        this.$toast({
          content: this.$t('所选数据存在审批中状态或审批通过状态，不能提交审批'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交所选数据吗？')
        },
        success: () => {
          const ids = selectedRecords.map((item) => item.id)
          // 调提交接口
          this.$API.performanceManage.approvalComprehensiveResult(ids).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.search()
            }
          })
        }
      })
    },
    // 导出综合绩效得分明细按钮
    exportDetail() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 请求表格数据
          let params = {}
          if (this.getParams()) {
            params = this.getParams()
          }
          this.getExportDetailApi()(params).then((res) => {
            const { data, headers } = res
            download({
              // fileName: '模板清单.xlsx',
              fileName: decodeURI(headers['content-disposition'].split('=')[1]),
              blob: data
            })
            this.$toast({
              content: this.$t('导出成功'),
              type: 'success'
            })
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getExportDetailApi() {
      if (this.isPurchase) {
        return this.$API.performanceManage.exportDetailComprehensiveResult
      }
      return this.$API.performanceManage.exportSupDetailComprehensiveResult
    }
    // deleteTodoList(params) {
    //   if (this.isPurchase) {
    //     return this.$api.taskList.todoDelete(params)
    //   }
    //   return this.$api.taskList.supplierTodoDelete(params)
    // }
  }
}
</script>

<style lang="scss" scoped>
.mt-data-grid {
  flex: 1;
  /deep/ .e-grid {
    height: 100%;
    display: flex;
    flex-direction: column;

    .e-headerchkcelldiv {
      padding-left: 0;
    }

    .e-gridcontent {
      flex: 1;
      .e-content {
        // height: 100% !important;
        flex-direction: row !important;
      }
    }
  }
}
.comprehensive-container {
  // margin-top: 24px;
  background: #fff;
  padding: 10px;
  border-radius: 8px;
}
.button-group {
  display: flex;
  justify-content: flex-start;
  margin: 15px 0 10px 0;
}

.search-input {
  padding: 10px 0 16px;
  .toggle-tag {
    padding-right: 15px;
    color: #2783fe;
    display: inline-block;
    font-size: 12px;
    position: relative;
    cursor: pointer;
    user-select: none;
    .mt-icons {
      font-size: 12px;
      position: absolute;
      transform: scale(0.4);
      top: -2px;
      left: 26px;
      &:nth-child(2) {
        top: 2px;
      }
    }
  }
  .search-area {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 16px 24px 12px;
    margin-top: 12px;
    .main-form {
      /deep/ .mt-form-item {
        width: calc(25% - 20px);
        min-width: 200px;
        display: inline-flex;
        margin-top: 5px;
        margin-right: 20px;

        .full-width {
          width: calc(100% - 20px) !important;
        }
        .e-ddt .e-ddt-icon {
          background: unset;
          &::before {
            content: '\e36a';
            font-size: 16px;
          }
        }
      }
      .check-area {
        transform: translateY(10px);
      }
    }
    .button-group {
      padding-top: 10px;
      span {
        margin-right: 16px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #2783fe;
        cursor: pointer;
        user-select: none;
      }
    }
  }
  // .source-config-table-container {
  //   flex: 1;
  //   // overflow: auto;
  //   background: #fff;
  //   height: 100%;
  // }
}
::v-deep {
  .button-group span {
    height: auto;
    padding: 0;
    margin-left: 0;
    border: none;
  }
}
</style>
