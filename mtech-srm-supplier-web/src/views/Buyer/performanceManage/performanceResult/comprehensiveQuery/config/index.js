//维度设置Tab
import { i18n } from '@/main.js'
import Vue from 'vue'
export const toolbarOptions = (isPurchase) => {
  const toolbarOptions = [
    {
      text: i18n.t('导出'),
      id: 'export',
      prefixIcon: 'e-excelexport'
    },
    {
      text: i18n.t('发布'),
      id: 'publish',
      prefixIcon: 'e-update'
    },
    {
      text: i18n.t('提交审批'),
      id: 'submit',
      prefixIcon: 'e-add'
    },
    {
      text: i18n.t('导出综合绩效得分明细'),
      id: 'exportDetail',
      prefixIcon: 'e-excelexport'
    }
  ]
  if (!isPurchase) {
    toolbarOptions.splice(1, 2)
  }
  return toolbarOptions
}

export const statusOptions = [
  {
    status: 1,
    label: i18n.t('未审批'),
    cssClass: ['status-label', 'status-enable']
  },
  {
    status: 2,
    label: i18n.t('审批中'),
    cssClass: ['status-label', 'status-disable']
  },
  {
    status: 3,
    label: i18n.t('审批通过'),
    cssClass: ['status-label', 'status-disable']
  },
  {
    status: 4,
    label: i18n.t('审批驳回'),
    cssClass: ['status-label', 'status-disable']
  }
  // {
  //   status: '1',
  //   label: i18n.t('失效'),
  //   cssClass: ['status-label', 'status-enable']
  // },
]
export const pubStatusOptions = [
  {
    status: 5,
    label: i18n.t('未发布'),
    cssClass: ['status-label', 'status-enable']
  },
  {
    status: 6,
    label: i18n.t('已发布'),
    cssClass: ['status-label', 'status-disable']
  }
]
export const columnData = (isPurchase, dynamicDolumn) => {
  const assessCycleList = [
    {
      label: 'YEAR',
      value: i18n.t('年度')
    },
    {
      label: 'HALF-YEAR',
      value: i18n.t('半年度')
    },
    {
      label: 'SEASON',
      value: i18n.t('季度')
    },
    {
      label: 'MONTH',
      value: i18n.t('月度')
    }
  ]
  const columnHeader = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'assessMonth',
      headerText: i18n.t('绩效月份'),
      width: 100
    },
    {
      field: 'assessCycle',
      headerText: i18n.t('评价周期'),
      width: 100,
      template: () => {
        return {
          template: Vue.component('template-assess', {
            template: `
                <span>{{ getStatusLabel(data.assessCycle) }}</span>`,
            methods: {
              getStatusLabel(assessCycle) {
                let label = assessCycleList.filter((j) => j.label === assessCycle)
                return label[0]['value']
              }
            }
          })
        }
      }
    },
    {
      field: 'templateName',
      headerText: i18n.t('绩效模板'),
      width: 100
    },
    {
      field: 'orgName',
      headerText: i18n.t('组织'),
      width: 150
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: 150
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: 150
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      width: 100
    },
    {
      field: 'comprehensiveScore',
      headerText: i18n.t('综合绩效得分'),
      width: 150
    },
    {
      field: 'comprehensiveRank',
      headerText: i18n.t('综合绩效排名'),
      width: 150
    },
    {
      field: 'id',
      headerText: i18n.t('得分明细'),
      width: 100,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span style="color: #6386c1; cursor: pointer;" @click="toDetail(data)">{{ $t('明细') }}</span>`,
            methods: {
              toDetail(data) {
                console.log(i18n.t('跳转明细'), data)
                this.$router.push({
                  path: '/supplier/performance-result/comprehensive-query-detail',
                  query: {
                    type: 'Detail',
                    id: data.id,
                    isPur: isPurchase
                  }
                })
              }
            }
          })
        }
      }
    }
  ]
  const columnFooter = [
    {
      field: 'comprehensiveRankFlag',
      headerText: i18n.t('标识'),
      width: 80,
      visible: isPurchase,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `<span v-if="data.comprehensiveRankFlag === 'first'" style="color:green;font-weight:bold;">⚑</span>
            <span v-else-if="data.comprehensiveRankFlag === 'last'" style="color:red;font-weight:bold;">⚑</span>
            <span></span>`
          })
        }
      }
    },
    {
      field: 'approvalStatus',
      headerText: i18n.t('审批状态'),
      width: 150,
      visible: isPurchase,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span>{{ getStatusLabel(data.approvalStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = statusOptions.filter((j) => j.status === status)
                return label[0]['label']
              }
            }
          })
        }
      }
    },
    {
      field: 'publishStatus',
      headerText: i18n.t('发布状态'),
      width: 150,
      visible: isPurchase,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span>{{ getStatusLabel(data.publishStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = pubStatusOptions.filter((j) => j.status === status)
                return label[0]['label']
              }
            }
          })
        }
      }
    }
  ]
  for (let i = 0; i < dynamicDolumn.length; i++) {
    const item = dynamicDolumn[i]
    columnHeader.push({
      field: 'dimensionScoreInfo',
      headerText: i18n.t(`${item.dimensionName}得分`),
      width: 150,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span>{{getDimensionTypeScore(data)}}</span>`,
            methods: {
              getDimensionTypeScore(data) {
                let score = ''
                for (let j = 0; j < data.dimensionScoreInfo.length; j++) {
                  const itm = data.dimensionScoreInfo[j]
                  if (itm.dimensionName === item.dimensionName) {
                    score = itm.dimensionTypeScore
                  }
                }
                return score
              }
            }
          })
        }
      }
    })
  }
  columnHeader.push(...columnFooter)
  return columnHeader
}
