import { i18n } from '@/main.js'
export const columns = (indexType) => {
  // indexType判断是指标还是指标类
  let isIndex = indexType == 1 ? true : false
  return [
    { headerText: i18n.t('指标名称'), field: 'indexName' },
    { headerText: i18n.t('指标类型'), visible: isIndex, field: 'indexType' },
    { headerText: i18n.t('评分标准'), field: 'scoreStandard' },
    {
      headerText: i18n.t('得分'),
      field: 'dimensionCode'
    },
    {
      headerText: i18n.t('评分数据来源'),
      field: 'dataSource',
      valueConverter: {
        type: 'map',
        map: [
          {
            dataSource: 1,
            label: i18n.t('系统自动'),
            cssClass: ['status-label', 'status-enable']
          },
          {
            dataSource: 2,
            label: i18n.t('手动录入'),
            cssClass: ['status-label', 'status-disable']
          }
        ],
        fields: {
          text: 'label',
          value: 'dataSource'
        }
      }
    },
    { headerText: i18n.t('满分'), visible: !isIndex, field: 'originScore' },
    { headerText: i18n.t('加/扣分上限'), visible: !isIndex, field: 'maxDeductScore' },
    { headerText: i18n.t('备注'), field: 'remark' }
  ]
}
