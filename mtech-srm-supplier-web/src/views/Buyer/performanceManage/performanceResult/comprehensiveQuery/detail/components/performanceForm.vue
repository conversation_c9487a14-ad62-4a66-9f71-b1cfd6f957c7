<template>
  <div>
    <div class="header-box">
      <span class="header-title">{{ $t('综合绩效得分明细') }}</span>
      <div>
        <mt-button css-class="e-flat" :is-primary="true" @click="backTo">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-form">
      <mt-form ref="ruleForm" :model="performanceForm" :validate-on-rule-change="false">
        <mt-form-item prop="orgName" :label="$t('组织')">
          <mt-input
            v-model="performanceForm.orgName"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('组织')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="templateCode" :label="$t('绩效模板编码')">
          <mt-input
            v-model="performanceForm.templateCode"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('绩效模板编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
          <mt-input
            v-model="performanceForm.supplierCode"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('供应商编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类')">
          <mt-input
            v-model="performanceForm.categoryName"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('品类')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="comprehensiveScore" :label="$t('综合绩效得分')">
          <mt-input
            v-model="performanceForm.comprehensiveScore"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('综合绩效得分')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')">
          <mt-input
            v-model="performanceForm.supplierName"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('供应商名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="assessMonth" :label="$t('绩效月份')">
          <mt-input
            v-model="performanceForm.assessMonth"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('绩效月份')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="comprehensiveRank" :label="$t('综合绩效排名')">
          <mt-input
            v-model="performanceForm.comprehensiveRank"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('综合绩效排名')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="calcDate" :label="$t('计算时间')">
          <mt-input
            v-model="performanceForm.calcDate"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('计算时间')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    formConfig: {
      type: Object,
      require: true,
      default: () => {
        return {
          orgName: '', // 组织
          templateCode: '', // 绩效模板编码
          supplierCode: '', // 供应商编码
          categoryName: '', // 品类
          comprehensiveScore: '', // 综合绩效得分
          supplierName: '', // 供应商名称
          assessMonth: '', // 绩效月份
          comprehensiveRank: '', // 绩效排名
          calcDate: '' // 计算时间
        }
      }
    }
  },
  data() {
    return {
      performanceForm: {
        orgName: '', // 组织
        templateCode: '', // 绩效模板编码
        supplierCode: '', // 供应商编码
        categoryName: '', // 品类
        comprehensiveScore: '', // 综合绩效得分
        supplierName: '', // 供应商名称
        assessMonth: '', // 绩效月份
        comprehensiveRank: '', // 绩效排名
        calcDate: '' // 计算时间
      }
    }
  },
  created() {
    // 请求组织列表的接口并赋值给templateOrgOptions
  },
  methods: {
    backTo() {
      // this.$router.push({
      //   path: '/supplier/performance-result/sup/comprehensive-query'
      // })
      this.$router.go(-1)
    }
  },
  watch: {
    /* 监听传进来得内容 */
    formConfig: {
      handler(newVal) {
        this.performanceForm = { ...this.performanceForm, ...newVal }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.header-box {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  .header-title {
    color: #292929;
    font-family: PingFangSC;
    font-size: 20px;
    font-weight: 600;
  }
}
.main-form {
  /deep/ .mt-form-item {
    width: calc(33.33% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-top: 5px;
    margin-right: 20px;

    .full-width {
      width: calc(100% - 20px) !important;
    }
    .e-ddt .e-ddt-icon {
      background: #fff;
      &::before {
        content: '\e36a';
        font-size: 16px;
      }
    }
  }
}
</style>
