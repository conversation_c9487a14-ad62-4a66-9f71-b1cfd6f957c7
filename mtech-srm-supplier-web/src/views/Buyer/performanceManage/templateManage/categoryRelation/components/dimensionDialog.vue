<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgId" :label="$t('组织')">
          <mt-DropDownTree
            v-if="fieldsarr.dataSource.length"
            v-model="formObject.orgId"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择组织')"
            :popup-height="500"
            :fields="fieldsarr"
            @select="selectCompany"
            id="baseTreeSelect"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择组织')" :data-source="[]"></mt-select>
        </mt-form-item>
        <mt-form-item prop="templateId" :label="$t('绩效模板')">
          <mt-select
            v-model="formObject.templateId"
            :data-source="planeArrList"
            :fields="{ text: 'templateName', value: 'id' }"
            :show-clear-button="true"
            :allow-filtering="true"
            @select="selectTemplate"
            :placeholder="$t('请选择绩效模板')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="calcRule" :label="$t('绩效计算规则')">
          <mt-select
            v-model="formObject.calcRule"
            :data-source="calcRuleList"
            :fields="{ text: 'ruleName', value: 'id' }"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择绩效计算规则')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryIdList" :label="$t('适用品类')">
          <mt-DropDownTree
            @input="selectCategoryas"
            v-model="formObject.categoryIdList"
            v-if="fields.dataSource.length"
            :fields="fields"
            :allow-filtering="true"
            filter-type="Contains"
            :show-check-box="true"
            id="checkboxTreeSelect"
            :placeholder="$t('请选择适用品类')"
            :allow-multi-selection="true"
            :auto-check="true"
            :key="fields.key"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择品类')" :data-source="[]"></mt-select>
        </mt-form-item>
      </mt-form>
      <div class="rule-desc">
        <p>{{ $t('绩效计算规则说明') }}：</p>
        <li>{{ $t('系统自动是指根据设定的权重，系统自动从下级组织的绩效卷积计算上层的绩效') }}</li>
        <li>{{ $t('手动录入是指绩效计算的原始数据由用户手动录入，非卷积计算') }}</li>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      // 表单验证
      rules: {
        orgId: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        categoryIdList: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        templateId: [
          {
            required: true,
            message: this.$t('请选择绩效模板'),
            trigger: 'blur'
          }
        ],
        calcRule: [
          {
            required: true,
            message: this.$t('请选择绩效计算规则'),
            trigger: 'blur'
          }
        ]
      },
      fieldsarr: {
        dataSource: [], //公司树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList'
        // code: 'orgCode'
      },
      categoryList: [], // 品类树下拉数组
      fields: {
        dataSource: [], //品类树下拉数组
        value: 'id',
        text: 'codeAndName',
        child: 'childrens',
        key: 1
        // parentValue: "pid",
        // hasChildren: "hasChild",
      },
      formInfo: {
        orgId: '', //公司id
        companyId: '', //计划id 取计划模板
        templateId: '', //计划id 取品类
        categoryIdList: [] //品类树下拉数组
      },
      // 弹窗底部按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      // 新增表单内容
      formObject: {
        categoryInfoRequests: [
          {
            categoryCode: '', //品类编码
            categoryId: '', //品类ID
            categoryName: '', //品类名称
            parentId: '' //父级ID
          }
        ],
        orgCode: '', //组织机构编码
        orgId: '', //组织机构id
        orgName: '', //组织机构名称
        planCode: '', //计划编码
        templateId: '', //计划id
        planName: '', //计划名称
        calcRule: '' // 计算规则
      },
      editStatus: false,
      planeArrList: [], //计划下拉数组
      calcRuleList: [
        // 计算规则下拉数组
        { ruleName: this.$t('手动录入'), id: 2 },
        { ruleName: this.$t('系统自动'), id: 1 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    }
  },
  watch: {
    'formObject.orgId': {
      handler() {
        this.$refs.dialogRef.validateField('orgId')
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        // this.$set(this.buttons[1].buttonModel, "content", "保存");
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      let _data = { ...this.modalData.data }
      this.formObject = {
        id: _data.id, //配置Id
        dimensionName: _data.dimensionName, //名称
        remark: _data.remark //备注
      }
    }
  },
  async created() {
    // 初始化获取公司列表
    this.TreeByAccount()
    // 获取品类列表
    this.getCategorys('')
    // 编辑
    if (this.modalData.isEdit) {
      let emitData = this.modalData.policyData //行内传值数据
      this.queryPlanByCompany() //调用计划接口
      this.formObject = emitData[0]
      this.formObject.orgId = '1475802394828308482' // 先写死 后期有公司对应 id  就可以了
    }
  },
  methods: {
    // 品类下拉框事件
    getCategorys(val) {
      this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          // this.categoryList = res.data.records
          this.$set(
            this.fields,
            'dataSource',
            res.data.records.map((i) => {
              return { ...i, codeAndName: `${i.categoryCode} - ${i.categoryName}` }
            })
          )
        })
    },
    // 选取公司递归
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formInfo.orgId = ele.id
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgId = ele.id
          this.formObject.orgName = ele.name
          return
        }
        if (ele.children && ele.children.length > 0) {
          this.fn(ele.children, id)
        }
      })
    },

    // 选择公司
    selectCompany(e) {
      if (e.itemData) {
        let { itemData } = e
        this.fn(this.fieldsarr.dataSource, itemData.id)
        this.queryPlanByCompany(itemData.id) // 获取绩效模板列表
      }
    },
    // 品类数组递归去重
    fcin(data, e, arr) {
      data.forEach((item) => {
        if (e.includes(item.categoryCode)) {
          arr.push({
            categoryCode: item.categoryCode,
            categoryId: item.id,
            categoryName: item.categoryName,
            parentId: item.parentId
          })
        }
        if (Array.isArray(item.childrens)) {
          this.fcin(item.childrens, e, arr)
        }
      })
    },
    // 品类选中事件
    selectCategoryas(e) {
      this.formObject.categoryInfoRequests = []
      this.fcin(this.fields.dataSource, e, this.formObject.categoryInfoRequests)
      this.$refs.dialogRef.validateField('categoryIdList')
    },
    // 选择绩效模板点击事件
    selectTemplate(e) {
      let { itemData } = e
      this.formInfo.templateId = itemData.id //获取模板传值id
      this.formObject.templateId = [itemData.id] //获取模板传值id
      this.formObject.planCode = itemData.planCode
      this.formObject.planName = itemData.planName
      // this.getCategorys('')
    },

    // 获取绩效模板列表
    queryPlanByCompany(id) {
      this.$API.performanceManage
        .templateListQuery({
          condition: 'and',
          rules: [
            {
              label: this.$t('组织ID'),
              field: 'orgId',
              type: 'string',
              operator: 'contains',
              value: id
            }
          ],
          page: { current: 1, size: 10000 }
        })
        .then((res) => {
          if (res.code == 200) {
            this.planeArrList = res.data
          }
        })
    },
    // 初始化获取公司列表
    TreeByAccount() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.fieldsarr, 'dataSource', [...res.data])
        }
      })
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = {
            calcRule: this.formObject.calcRule,
            orgId: this.formObject.orgId[0],
            templateId: this.formObject.templateId,
            categoryIdList: this.formObject.categoryIdList.join(',')
          }
          this.$API.performanceManage.categoryStrategyAdd(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function') //关闭弹窗
              // this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/* .dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
} */
.e-ddt .e-ddt-icon::before {
  content: '\e36a';
  font-size: 16px;
}
.rule-desc {
  color: #f44336;
  p {
    padding-left: 20px;
    &:first-child {
      padding-left: 0;
    }
  }
}
</style>
