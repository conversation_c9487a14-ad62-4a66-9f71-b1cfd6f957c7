//维度设置Tab
import { i18n } from '@/main.js'
import Vue from 'vue'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder ',
    title: i18n.t('新增')
    // permission: ["O_02_0039"],
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete ',
    title: i18n.t('删除')
    // permission: ["O_02_0039"],
  },
  // {
  //   id: "Edit",
  //   icon: "icon_solid_edit ",
  //   title: i18n.t("编辑"),
  //   // permission: ["O_02_0039"],
  // },
  {
    id: 'Enable',
    icon: 'icon_table_disable ',
    title: i18n.t('启用')
    // permission: ["O_02_0041"],
  },
  {
    id: 'Disable',
    icon: 'icon_table_enable ',
    title: i18n.t('停用')
    // permission: ["O_02_0040"],
  },
  {
    id: 'EXimport',
    icon: 'icon_solid_upload',
    title: i18n.t('导入')
    // permission: ["O_02_0042"],
  },
  {
    id: 'EXdownload',
    icon: 'icon_solid_Download',
    title: i18n.t('导出')
    // permission: ["O_02_0042"],
  }
  // {
  //   id: "EXdownload",
  //   icon: "icon_solid_Download ",
  //   title: i18n.t("导入模板下载"),
  //   // permission: ["O_02_0043"],
  // },
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    cssClass: ''
  },
  {
    field: 'templateCode',
    headerText: i18n.t('模板编码'),
    cssClass: 'field-content'
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板名称'),
    cssClass: 'field-content'
  },
  {
    field: 'calcRule', // planRule
    headerText: i18n.t('绩效计算规则'),
    valueConverter: {
      type: 'map',
      map: [
        // {
        //   status: 0,
        //   label: i18n.t("已创建"),
        //   cssClass: ["status-label", "status-enable"],
        // },
        {
          calcRule: 1,
          label: i18n.t('系统自动'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          calcRule: 2,
          label: i18n.t('手动录入'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: {
        text: 'label',
        value: 'calcRule'
      }
    },
    fields: { text: 'label', value: 'calcRule' }
  },

  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: 80,
    valueConverter: {
      type: 'map',
      map: [
        // {
        //   status: 0,
        //   label: i18n.t("已创建"),
        //   cssClass: ["status-label", "status-enable"],
        // },
        {
          status: 2,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: {
        text: 'label',
        value: 'status'
      }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Start',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      },
      {
        id: 'Stop',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      }
    ]
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织')
  },

  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('createTime', {
          template: `
              <div class="time-box">
                {{ data.createTime }}
              </div>`
        })
      }
    }
    // queryType: "date",
    // format: "yyyy-MM-dd <br/> hh:mm:ss",
  }
]
// 调用列表
export const pageConfig = (url) => [
  {
    gridId: 'aa43d887-b5b0-4aa7-bf93-d67b92238dd3',
    toolbar,
    grid: {
      // allowTextWrap: true,
      columnData,
      useToolTemplate: false,
      asyncConfig: {
        url
      }
    }
  }
]
