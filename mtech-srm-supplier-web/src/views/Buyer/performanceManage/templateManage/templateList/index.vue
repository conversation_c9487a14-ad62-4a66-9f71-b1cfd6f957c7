<template>
  <!-- 模板清单 -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
  <!--

    -->
</template>

<script>
import { download } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig('/analysis/tenant/buyer/assess/template/pageQuery')
    }
  },
  methods: {
    handleClickCellTool(e) {
      console.log('handleClickCellTool-', e)
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.$router.push({
          path: '/supplier/template-manage/template-detail',
          query: {
            type: 'Edit',
            id: data.id
          }
        })
      } else if (tool.id === 'delete') {
        this.templateDelete([data])
      } else if (tool.id === 'detail') {
        this.$router.push({
          path: '/supplier/template-manage/template-detail',
          query: {
            type: 'Detail',
            id: data.id
          }
        })
      }
    },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Submit' ||
          e.toolbar.id == 'Version' ||
          e.toolbar.id == 'Delete' ||
          e.toolbar.id == 'Disable')
      ) {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id == 'Add') {
        this.linkToDetail(e.toolbar.id)
      } // 创建新版本
      else if (e.toolbar.id == 'Version') {
        if (_selectGridRecords.length > 1) {
          this.$toast({ content: this.$t('暂只支持单行创建新版本'), type: 'warning' })
          return
        }
        if (_selectGridRecords[0].status != 'EFFECTIVE') {
          this.$toast({ content: this.$t('暂只支持有效状态进行创建新版本'), type: 'warning' })
          return
        }
        this.linkToDetail(e.toolbar.id, _selectGridRecords[0].id)
      } // 删除
      else if (e.toolbar.id == 'Delete') {
        if (_selectGridRecords.some((i) => i.status != 'DRAFT')) {
          this.$toast({ content: this.$t('暂只支持拟定状态进行删除'), type: 'warning' })
          return
        }
        this.templateDelete(_selectGridRecords)
      } // 提交审批
      else if (e.toolbar.id == 'Submit') {
        if (_selectGridRecords[0].status != 'DRAFT' && _selectGridRecords[0].status != 'REJECT') {
          this.$toast({ content: this.$t('暂只支持拟定或驳回状态进行审批'), type: 'warning' })
          return
        }
        this.templateSubmit(_selectGridRecords)
      } // 失效
      else if (e.toolbar.id == 'Disable') {
        if (_selectGridRecords.some((i) => i.status != 'EFFECTIVE')) {
          this.$toast({ content: this.$t('暂只支持有效状态进行失效操作'), type: 'warning' })
          return
        }
        this.templateDisable(_selectGridRecords)
      } // 导出
      else if (e.toolbar.id == 'EXdownload') {
        this.templateExport(_selectGridRecords)
      }
    },
    // 跳转新增 || 创建新版本
    linkToDetail(type, id) {
      const query = { type }
      if (id) {
        query.id = id
      }
      this.$router.push({
        path: '/supplier/template-manage/template-detail',
        query
      })
    },
    // 删除
    templateDelete(rows) {
      // 调对应接口后刷新列表
      let ids = rows.map((v) => {
        return v.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceManage.delTemplateData({ ids }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // 审批
    templateSubmit(rows) {
      let ids = rows.map((v) => {
        return v.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交审批？')
        },
        success: () => {
          this.$API.performanceManage.templateSubmitApply(ids).then(() => {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // 失效
    templateDisable(rows) {
      let ids = rows.map((v) => {
        return v.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认失效数据？')
        },
        success: () => {
          this.$API.performanceManage.templateInvalid(ids).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // 导出
    templateExport() {
      let params = this.$refs.templateRef.getAsyncParams()
      // let ids = rows.map((v) => {
      //   return v.id
      // })
      this.$API.performanceManage.downloadTemplate(params).then((res) => {
        const { data, headers } = res
        download({
          // fileName: '模板清单.xlsx',
          fileName: decodeURI(headers['content-disposition'].split('=')[1]),
          blob: data
        })
        // this.$toast({
        //   content: this.$t('导出成功'),
        //   type: 'success'
        // })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
  .mt-select-index {
    float: left;
  }
}
</style>
