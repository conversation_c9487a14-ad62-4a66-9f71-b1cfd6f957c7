// import { is } from "core-js/core/object";
import { i18n } from '@/main.js'
export const EditInputCell = {
  template: `
      <div class="editable-cell">
        <div v-if="pageType !== 'Detail'" class="editable-cell-input-wrapper">
          <a-input-number :value="value" :max="100" :min="0" :step="0.01" :precision="2" @change="handleChange" style="width: 96%" />
        </div>
        <div v-else class="editable-cell-text-wrapper">
          {{ value }}
        </div>
      </div>
    `,
  props: {
    text: {
      // type: Number,
      require: false,
      default: null
    },
    pageType: {
      // 判断当前页面的类型，Add;Edit;Detail;Version
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      value: this.text
    }
  },
  methods: {
    handleChange(e) {
      const value = e
      this.value = value
      this.$emit('change', this.value)
    }
  }
}
// export const EditInputCell = {
//   template: `
//       <div class="editable-cell">
//         <div v-if="pageType !== 'Detail'" class="editable-cell-input-wrapper">
//           <a-input :value="value" @change="handleChange" style="width: 96%" />
//         </div>
//         <div v-else class="editable-cell-text-wrapper">
//           {{ value || ' ' }}
//         </div>
//       </div>
//     `,
//   props: {
//     text: String,
//     pageType: {
//       // 判断当前页面的类型，Add;Edit;Detail;Version
//       type: String,
//       require: true,
//       default: ''
//     }
//   },
//   data() {
//     return {
//       value: this.text
//     }
//   },
//   methods: {
//     handleChange(e) {
//       const value = e.target.value
//       this.value = value
//       const reg =
//         /^\d\.([1-9]{1,2}|[0-9][1-9])$|^[1-9]\d{0,1}(\.\d{1,2}){0,1}$|^100(\.0{1,2}){0,1}$/ // 校验输入值为0-100
//       if (reg.test(this.value)) {
//         // this.value = value;
//       } else {
//         let num = this.value
//         if (isNaN(num)) {
//           this.value = '0'
//         }
//         if (this.value > 100) {
//           this.value = '100'
//         } else if (this.value < 0) {
//           this.value = '0'
//         } else {
//           num = Math.floor(num * 100) / 100
//           this.value = num.toFixed(2)
//         }
//       }
//       this.$emit('change', this.value)
//     }
//   }
// }

const dialogColumn = [
  {
    width: '60',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标/指标类名称')
  },
  {
    field: 'dataSource',
    ignore: true,
    headerText: i18n.t('评分数据来源'),
    valueConverter: {
      type: 'map',
      map: [
        // {
        //   status: 0,
        //   label: i18n.t("已创建"),
        //   cssClass: ["status-label", "status-enable"],
        // },
        {
          dataSource: 1,
          label: i18n.t('系统自动'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          dataSource: 2,
          label: i18n.t('手动录入'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: {
        text: 'label',
        value: 'dataSource'
      }
    }
  },
  {
    field: 'description',
    headerText: i18n.t('评分逻辑描述')
  },
  {
    field: 'originScore',
    ignore: true,
    headerText: i18n.t('原始得分')
  },
  {
    field: 'maxDeductScore',
    ignore: true,
    headerText: i18n.t('扣分上限')
  },
  {
    field: 'additional',
    ignore: true,
    headerText: i18n.t('是否附加项'),
    valueConverter: {
      type: 'map',
      map: [
        {
          additional: false,
          label: i18n.t('否'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          additional: true,
          label: i18n.t('是'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: {
        text: 'label',
        value: 'additional'
      }
    }
  }
]
export const dialogPageConfig = () => [
  {
    useToolTemplate: false,
    toolbar: {},
    gridId: '82dd9f98-ede0-4516-a289-95144f2da47c',
    grid: {
      // height: 'calc(100vh - 460px)',
      height: 'auto',
      lineIndex: true,
      allowFiltering: false,
      columnData: dialogColumn,
      allowSorting: false,
      allowPaging: false,
      allowSelection: true,
      // asyncConfig: {
      //   url: `/analysis/tenant/buyer/assess/index/queryEffectiveIndexList`,
      //   params: { dimensionId: '' },
      //   axiosConfig: {
      //     headers: { "Content-Type": "application/x-www-form-urlencoded" },
      //   },
      // },
      dataSource: []
    }
  }
]

export const detailDialogColumn = (indexLineType) => {
  return [
    // {
    //   field: "index",
    //   headerText: "序号",
    //   template: function () {
    //     return {
    //       template: Vue.component("indexSort", {
    //         template: `<div>{{ 1 + data.index }}</div>`,
    //         data() {
    //           return { data: {} };
    //         },
    //       }),
    //     };
    //   },
    // },
    {
      field: 'description',
      headerText: i18n.t('指标说明')
    },
    {
      field: 'minPercentScore',
      headerText: '评分值>=%',
      visible: indexLineType == 3
    },
    {
      field: 'maxPercentScore',
      headerText: '评分值<%',
      visible: indexLineType == 3
    },
    {
      field: 'minNumberScore',
      headerText: '评分值>=',
      visible: indexLineType == 2
    },
    {
      field: 'maxNumberScore',
      headerText: '评分值<',
      visible: indexLineType == 2
    },
    // {
    //   field: 'coefficient',
    //   headerText: i18n.t('系数')
    // },
    {
      field: 'score',
      headerText: i18n.t('得分')
    }
  ]
}
export const dialogDetailPageConfig = () => [
  {
    useToolTemplate: false,
    toolbar: {},
    grid: {
      height: 'calc(100vh - 460px)',
      lineIndex: true,
      allowFiltering: false,
      columnData: detailDialogColumn(),
      allowSorting: false,
      allowPaging: false,
      allowSelection: true,
      // asyncConfig: {
      //   url: url,
      // }
      dataSource: []
    }
  }
]
