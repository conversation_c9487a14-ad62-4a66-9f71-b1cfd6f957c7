import { i18n } from '@/main.js'

export const dimensionColumns = function (pageType) {
  const columns = [
    {
      title: i18n.t('指标维度'),
      dataIndex: 'dimensionId',
      align: 'left',
      scopedSlots: { customRender: 'dimensionType' }
    },
    // {
    //   title: '*权重(%)',
    //   align: 'left',
    //   dataIndex: 'weight',
    //   scopedSlots: { customRender: 'weight' }
    // },
    {
      title: i18n.t('得分'),
      align: 'left',
      dataIndex: 'weight',
      scopedSlots: { customRender: 'weight' }
    },
    {
      title: i18n.t('操作'),
      align: 'left',
      dataIndex: 'operation',
      scopedSlots: { customRender: 'operation' }
    }
  ]
  if (pageType === 'Detail') {
    columns.pop()
  }
  return columns
}

{
  /* <a-input :value="value" @change="handleChange" @pressEnter="check" style="width: 96%" /><a-icon
  type="check"
  class="editable-cell-icon-check"
  @click="check"
/> */
}
export const EditSelectCell = {
  template: `
      <div class="editable-cell" style="width: 96%">
        <div v-if="pageType !== 'Detail'" class="editable-cell-input-wrapper">
          <a-select
            :placeholder="$t('请选择')"
            :value="value"
            style="width: 100%"
            @change="handleChange"
            :show-search="true"
            :filter-option="
              (value, option) => option.componentOptions.children[0].text.indexOf(value) >= 0
            "
          >
            <a-select-option v-for="item in filteredOptions" :key="item.id" :value="item.id">
              {{ item.dimensionName }}
            </a-select-option>
          </a-select>
        </div>
        <div v-else class="editable-cell-text-wrapper">
          {{ filteredOptions.filter(i => i.id === value )[0] ? filteredOptions.filter(i => i.id === value )[0]['dimensionName'] : ' ' }}
        </div>
      </div>
    `,
  props: {
    text: String,
    configData: Array,
    pageType: {
      // 判断当前页面的类型，Add;Edit;Detail;Version
      type: String,
      require: true,
      default: ''
    },
    filteredOptions: {
      // 判断当前页面的类型，Add;Edit;Detail;Version
      type: Array,
      require: true,
      default: () => []
    }
  },
  data() {
    return {
      value: this.text
    }
  },
  methods: {
    handleChange(e) {
      const value = e
      this.value = value
      this.$emit('change', this.value)
    }
  }
}

export const columns = function (pageType) {
  const columns = [
    {
      title: i18n.t('指标名称'),
      dataIndex: 'indexName',
      ellipsis: true,
      key: 'indexName',
      align: 'left'
    },
    {
      title: i18n.t('指标类型'),
      dataIndex: 'indexType',
      key: 'indexType',
      align: 'left',
      scopedSlots: { customRender: 'indexType' }
    },
    {
      title: i18n.t('评分逻辑描述'),
      dataIndex: 'description',
      ellipsis: true,
      key: 'description',
      align: 'left',
      width: 120
    },
    {
      title: i18n.t('评分数据来源'),
      dataIndex: 'dataSource',
      key: 'dataSource',
      align: 'left',
      scopedSlots: { customRender: 'dataSource' }
    },
    { title: i18n.t('原始得分'), dataIndex: 'originScore', key: 'originScore', align: 'left' },
    {
      title: i18n.t('默认得分'),
      dataIndex: 'defaultScore',
      key: 'defaultScore',
      align: 'left',
      scopedSlots: { customRender: 'defaultScore' }
    },
    {
      title: i18n.t('扣分上限'),
      dataIndex: 'maxDeductScore',
      key: 'maxDeductScore',
      align: 'left'
    },
    {
      title: i18n.t('是否附加项'),
      dataIndex: 'additional',
      key: 'additional',
      align: 'left',
      scopedSlots: { customRender: 'additional' }
    },
    {
      title: i18n.t('指标行'),
      dataIndex: 'id',
      key: 'id',
      align: 'left',
      scopedSlots: { customRender: 'dimensionRow' }
    },
    // {
    //   title: '权重(%)',
    //   dataIndex: 'weight',
    //   key: 'weight',
    //   align: 'left',
    //   scopedSlots: { customRender: 'weight' }
    // },
    {
      title: i18n.t('操作'),
      key: 'operation',
      scopedSlots: { customRender: 'operation' },
      align: 'left'
    }
  ]
  if (pageType === 'Detail') {
    columns.pop()
  }
  return columns
}

export const innerColumns = function (pageType) {
  const columns = [
    { title: i18n.t('序号'), key: 'index', scopedSlots: { customRender: 'index' }, align: 'left' },
    {
      title: i18n.t('指标名称'),
      dataIndex: 'indexName',
      ellipsis: true,
      key: 'name',
      align: 'left'
    },
    {
      title: i18n.t('评分逻辑描述'),
      dataIndex: 'description',
      ellipsis: true,
      key: 'version',
      align: 'left',
      width: 120
    },
    {
      title: i18n.t('评分数据来源'),
      dataIndex: 'dataSource',
      key: 'dataSource',
      align: 'left',
      scopedSlots: { customRender: 'dataSource' }
    },
    { title: i18n.t('原始得分'), dataIndex: 'originScore', key: 'originScore', align: 'left' },
    {
      title: i18n.t('扣分上限'),
      dataIndex: 'maxDeductScore',
      key: 'maxDeductScore',
      align: 'left'
    },
    {
      title: i18n.t('是否附加项'),
      dataIndex: 'additional',
      key: 'additional',
      align: 'left',
      scopedSlots: { customRender: 'additional' }
    },
    {
      title: i18n.t('指标行'),
      dataIndex: 'id',
      key: 'id',
      align: 'left',
      scopedSlots: { customRender: 'dimensionRow' }
    },
    // {
    //   title: '权重(%)',
    //   dataIndex: 'weight',
    //   key: 'weight',
    //   align: 'left',
    //   scopedSlots: { customRender: 'weight' }
    // },
    {
      title: i18n.t('操作'),
      key: 'operation',
      scopedSlots: { customRender: 'operation' },
      align: 'left'
    }
  ]
  if (pageType === 'Detail') {
    columns.pop()
  }
  return columns
}
