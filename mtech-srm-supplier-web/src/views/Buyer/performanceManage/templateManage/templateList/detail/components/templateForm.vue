<template>
  <div>
    <div class="header-box">
      <!-- 右侧各种操作按钮 -->
      <span class="header-title">{{ getTitle() }}</span>
      <div>
        <mt-button
          v-if="pageType !== 'Detail'"
          css-class="e-flat"
          :is-primary="true"
          @click="save()"
          >{{ $t('保存') }}</mt-button
        >
        <mt-button
          v-if="pageType !== 'Detail'"
          css-class="e-flat"
          :is-primary="true"
          @click="submit()"
          >{{ $t('提交') }}</mt-button
        >
        <mt-button css-class="e-flat" :is-primary="true" @click="backTo">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-form">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item v-if="pageType !== 'Add'" prop="templateCode" :label="$t('模板编码')">
          <mt-input
            v-model="addForm.templateCode"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('模板编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="templateType" :label="$t('模板类型')">
          <mt-select
            v-model="addForm.templateType"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="pageType === 'Detail'"
            :data-source="templateTypeOptions"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="orgId" :label="$t('组织')">
          <!-- <mt-select
            v-model="addForm.orgId"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="pageType === 'Detail'"
            :data-source="templateOrgOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
            @select="selectOrg"
          ></mt-select> -->
          <mt-DropDownTree
            :placeholder="$t('请选择组织')"
            :popup-height="500"
            :show-clear-button="false"
            :enabled="pageType !== 'Detail' && pageType !== 'Version'"
            :fields="orgFields"
            v-model="addForm.orgId"
            :allow-filtering="true"
            filter-type="Contains"
            id="baseTreeSelect"
            @select="selectOrg"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="templateName" :label="$t('模板名称')">
          <mt-input
            v-model="addForm.templateName"
            :show-clear-button="false"
            :disabled="pageType === 'Detail'"
            :placeholder="$t('模板名称')"
            :max-length="32"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('模板状态')">
          <mt-select
            v-model="addForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="true"
            :data-source="statusOptions"
            :fields="{ text: 'label', value: 'status' }"
            :placeholder="$t('模板状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="templateDesc" :label="$t('模板描述')">
          <mt-input
            v-model="addForm.templateDesc"
            :show-clear-button="false"
            :disabled="pageType === 'Detail'"
            type="textarea"
            :placeholder="$t('模板描述')"
            :max-length="200"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="evaluationCycleCode" :label="$t('评价周期')">
          <mt-select
            v-model="addForm.evaluationCycleCode"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="pageType === 'Detail'"
            :data-source="circleOptions"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :placeholder="$t('评价周期')"
            @change="evaluationCycleChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="achievementAssessSupplierType" :label="$t('供应商维度')">
          <mt-select
            v-model="addForm.achievementAssessSupplierType"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="pageType === 'Detail'"
            :data-source="supplierDimensionOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('供应商维度')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="achievementAssessBiz" :label="$t('适用业务')">
          <mt-select
            v-model="addForm.achievementAssessBiz"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="pageType === 'Detail'"
            :data-source="applicableBusinessOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('适用业务')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="achievementAssessType" :label="$t('类别')">
          <mt-select
            v-model="addForm.achievementAssessType"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="pageType === 'Detail'"
            :data-source="perClassOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('类别')"
          ></mt-select>
        </mt-form-item>
        <div class="mt-form-item check-area">
          <mt-checkbox
            :disabled="pageType === 'Detail'"
            :checked="addForm.excludeSupplier"
            @change="(e) => (addForm.excludeSupplier = e.checked)"
          />
          <!-- <span class="e-error">*</span> -->
          <span class="label" style="margin-left: -10px">{{ $t('排除内部供应商') }}</span>
        </div>
      </mt-form>
    </div>
  </div>
</template>

<script>
import MtDropDownTree from '@mtech-ui/drop-down-tree'
import {
  statusOptions,
  supplierDimensionOptions,
  applicableBusinessOptions,
  perClassOptions
} from './../../config/index'
import utils from '@/utils/utils.js'
export default {
  props: {
    pageType: {
      // 判断当前页面的类型，Add;Edit;Detail;Version
      type: String,
      require: true,
      default: ''
    },
    formConfig: {
      // 判断当前页面的类型，Add;Edit;Detail;Version
      type: Object,
      require: true,
      default: () => {
        return {
          templateName: '', // 模板名称
          templateCode: '', // 模板编码
          templateType: '', // 模板类型
          orgId: '', // 组织
          status: 'DRAFT', // 模板状态
          templateDesc: '', // 模板描述
          evaluationCycleCode: '', // 评价周期
          achievementAssessSupplierType: '', // 供应商维度
          achievementAssessBiz: '', // 适用业务
          achievementAssessType: '', // 类别
          evaluationCycleName: '', // 评价周期名称
          excludeSupplier: false // 排除内部供应商
        }
      }
    }
  },
  components: {
    MtDropDownTree
  },
  data() {
    return {
      orgFields: {}, // 组织下拉列表的配置信息
      treeSettings: { autoCheck: true },
      templateTypeOptions: [], // 模板类型
      circleOptions: [], // 评价周期列表
      statusOptions,
      supplierDimensionOptions,
      applicableBusinessOptions,
      perClassOptions,
      addForm: {
        templateName: '', // 模板名称
        templateCode: '', // 模板编码
        templateType: '', // 模板类型
        orgId: '', // 组织
        status: 'DRAFT', // 模板状态
        templateDesc: '', // 模板描述
        evaluationCycleCode: '', // 评价周期
        achievementAssessSupplierType: '', // 供应商维度
        achievementAssessBiz: '', // 适用业务
        achievementAssessType: '', // 类别
        evaluationCycleName: '', // 评价周期名称
        excludeSupplier: false // 排除内部供应商
      },
      rules: {
        templateType: [{ required: true, message: this.$t('请选择模板类型'), trigger: 'blur' }],
        orgId: [{ required: true, message: this.$t('请选择组织'), trigger: 'blur' }],
        templateName: [
          {
            required: true,
            message: this.$t('请输入模板名称'),
            trigger: 'blur'
          }
        ],
        // templateDesc: [
        //   {
        //     required: true,
        //     message: this.$t('请输入模板描述'),
        //     trigger: 'blur'
        //   }
        // ],
        evaluationCycleCode: [
          { required: true, message: this.$t('请选择评价周期'), trigger: 'blur' }
        ],
        achievementAssessSupplierType: [
          { required: true, message: this.$t('请选择供应商维度'), trigger: 'blur' }
        ],
        achievementAssessBiz: [
          { required: true, message: this.$t('请选择适用业务'), trigger: 'blur' }
        ],
        achievementAssessType: [{ required: true, message: this.$t('请选择类别'), trigger: 'blur' }]
      }
    }
  },
  created() {
    // 获取字典类型列表
    this.getTemplateTypeList()
    // 获取评价周期列表
    this.getCircleOptionsList()
    // 组织下拉列表
    this.queryOrgTree()
    // 请求组织列表的接口并赋值给templateOrgOptions
  },
  methods: {
    getTemplateTypeList() {
      // 模板类型列表 - templateTypeList
      this.$API.performanceScoreSetting
        .dictionaryGetList({
          dictCode: 'MB-TYPE'
        })
        .then((res) => {
          if (res.code == 200) {
            this.templateTypeOptions = res.data
          }
        })
    },
    getCircleOptionsList() {
      // 评价周期列表 - circleOptions
      this.$API.performanceScoreSetting
        .dictionaryGetList({
          dictCode: 'EVALUATION-PERIOD'
        })
        .then((res) => {
          if (res.code == 200) {
            this.circleOptions = res.data
          }
        })
    },
    queryOrgTree() {
      // 组织下拉列表 - orgFields
      this.$API.performanceManage.getListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.orgFields = {
            dataSource: res.data,
            value: 'id',
            text: 'orgName',
            child: 'childrenList'
          }
        }
      })
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let result = utils.debounce(this.saveFn, 1000)
          result()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveFn() {
      this.$emit('save', this.addForm)
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let result = utils.debounce(this.submitFn, 1000)
          result()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    submitFn() {
      this.$emit('submit', this.addForm)
    },
    getTitle() {
      let title = this.$t('绩效模板')
      // 判断当前页面的类型，Add;Edit;Detail;Version
      switch (this.pageType) {
        case 'Version':
          return this.$t('复制') + title
        case 'Edit':
          return this.$t('编辑') + title
        case 'Detail':
          return this.$t('查看') + title
        default:
          return this.$t('新增') + title
      }
    },
    backTo() {
      this.$router.push({
        path: '/supplier/template-manage/template-list'
      })
    },
    evaluationCycleChange(e) {
      // 评价周期需要保存名称
      const { itemData } = e
      this.addForm.evaluationCycleName = itemData.itemName
    },
    selectOrg(e) {
      // 组织需要保存组织名称 和组织编码
      const { itemData } = e
      // this.addForm.orgName = itemData.text
      this.selectOrgCircle(this.orgFields.dataSource, itemData.id)
      this.$emit('updateOrgId', itemData.id)
      this.$emit('getDimensionTypeOption', itemData.id)
    },
    // 递归公司组织
    selectOrgCircle(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.addForm.orgCode = ele.orgCode
          this.addForm.orgName = ele.orgName
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.selectOrgCircle(ele.childrenList, id)
        }
      })
    }
  },
  watch: {
    /* 监听传进来得内容 */
    formConfig: {
      handler(newVal) {
        setTimeout(() => {
          // 使用定时器是为了解决组织下拉树状表回显异常的问题
          this.addForm = { ...this.addForm, ...newVal }
          if (this.pageType === 'Version') {
            this.addForm.status = 'DRAFT'
          }
        }, 800)
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.header-box {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  .header-title {
    color: #292929;
    font-family: PingFangSC;
    font-size: 20px;
    font-weight: 600;
  }
}
.main-form {
  /deep/ .mt-form-item {
    width: calc(25% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-top: 5px;
    margin-right: 20px;

    .full-width {
      width: calc(100% - 20px) !important;
    }
    .e-ddt .e-ddt-icon::before {
      content: '\e36a';
      font-size: 16px;
    }
  }
  .check-area {
    transform: translateY(10px);
  }
}
</style>
