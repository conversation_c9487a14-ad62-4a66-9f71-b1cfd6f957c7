<template>
  <div class="template-container">
    <template-form
      :form-config="formConfig"
      :page-type="pageType"
      @save="save"
      @submit="submit"
      @updateOrgId="updateOrgId"
      @getDimensionTypeOption="getDimensionTypeOption"
    />
    <!-- 折叠框通过设置id关联内容 -->
    <mt-accordion class="accordion-area" :data-source="dimensionArea"></mt-accordion>
    <div class="full-height" id="PLATFORM-ENTERPRISE-DETAIL-dimension-CONTENT">
      <mt-button
        v-if="pageType !== 'Detail'"
        css-class="e-flat"
        :is-primary="true"
        @click="addDimension"
        >{{ $t('新增') }}</mt-button
      >
      <a-table
        :row-selection="rowSelection"
        :columns="dimensionColumns"
        :data-source="dimensionData"
        :pagination="false"
        row-key="addId"
        size="small"
      >
        <template slot="dimensionType" slot-scope="text, record">
          <edit-select-cell
            :text="text"
            :page-type="pageType"
            :filtered-options="filteredOptions"
            @change="onCellChange(record.addId, 'dimensionId', $event)"
          />
        </template>
        <template slot="weight" slot-scope="text, record">
          <edit-input-cell
            :text="text"
            :page-type="pageType"
            @change="onCellInputChange(record.addId, 'weight', $event)"
          />
        </template>
        <span
          v-if="dimensionData.length"
          slot="operation"
          slot-scope="text, record, index"
          style="cursor: pointer; color: #6386ce"
          @click="() => deleteDimension(record.addId, index)"
          >{{ $t('删除') }}</span
        >
      </a-table>
    </div>
    <mt-accordion class="accordion-area" :data-source="detailArea"></mt-accordion>
    <div class="full-height" id="PLATFORM-ENTERPRISE-DETAIL-detail-CONTENT">
      <tree-table
        v-if="selectedRowKeys && selectedRowKeys.length"
        v-model="pageConfig"
        :page-type="pageType"
      ></tree-table>
    </div>
  </div>
</template>

<script>
// import Vue from 'vue'
// import { Select } from 'ant-design-vue'
// const { Option } = Select
// Vue.component('a-select-option', Option)
import { dimensionColumns, EditSelectCell, columns, innerColumns } from './config/index.js'
import { EditInputCell } from './components/config/index.js'
import MtAccordion from '@mtech-ui/accordion'

export default {
  components: {
    templateForm: require('./components/templateForm.vue').default,
    treeTable: require('./components/treeTable.vue').default,
    EditSelectCell,
    EditInputCell,
    MtAccordion
  },
  data() {
    return {
      dimensionData: [],
      dimensionColumns: dimensionColumns(this.$route.query.type),
      filteredOptions: [], // 指标维度下拉数据列表
      dimensionArea: [
        {
          header: this.$t('指标维度'),
          content: '#PLATFORM-ENTERPRISE-DETAIL-dimension-CONTENT',
          expanded: true
        }
      ],
      detailArea: [
        {
          header: this.$t('指标明细'),
          content: '#PLATFORM-ENTERPRISE-DETAIL-detail-CONTENT',
          expanded: true
        }
      ],
      selectedRowKeys: [],
      pageConfig: {
        data: [],
        dimensionId: '', // 当前选择行的维度id
        orgId: '', // 当前选择行的维度id
        columns: columns(this.$route.query.type),
        innerColumns: innerColumns(this.$route.query.type)
      },
      formConfig: {} // 表单部分回显
    }
  },
  methods: {
    updateOrgId(id) {
      this.pageConfig.orgId = id
    },
    // 获取指标清单下拉选项
    getDimensionTypeOption(e) {
      console.log('getDimensionTypeOption', e)
      this.$API.performanceManage.getDimensionlistQuery_new({ id: e }).then((res) => {
        const { data } = res
        this.filteredOptions = data
      })
    },
    // 由于指标跟模板的字段没有对齐，导致需要转换字段名
    changeFieldName(dataList, newName) {
      const list = JSON.parse(JSON.stringify(dataList))
      let oldName = 'buyerAssessTemplateIndexDTOList'
      if (newName === 'buyerAssessTemplateIndexDTOList') {
        oldName = 'childrenList'
      }
      for (let data of list) {
        data[newName] = data[oldName]
        data['addId'] = data['dimensionId']
        delete data[oldName]
        if (data[newName] && data[newName].length) {
          for (let child of data[newName]) {
            child[newName] = child[oldName]
            if (!child['indexId']) {
              child['indexId'] = child['id']
            }
            delete child[oldName]
            if (child[newName] && child[newName].length) {
              for (let grandChild of child[newName]) {
                if (!grandChild['indexId']) {
                  grandChild['indexId'] = grandChild['id']
                }
              }
            } else {
              child[newName] = []
            }
          }
        } else {
          data[newName] = []
        }
      }
      return list
    },
    save(addForm) {
      const params = {
        ...addForm,
        orgId: addForm.orgId[0],
        buyerAssessTemplateDimensionDTOList: this.changeFieldName(
          this.dimensionData,
          'buyerAssessTemplateIndexDTOList'
        )
      }
      // 模板编码不可输入，新增的时候为空不传值
      if (params.templateCode === '') {
        delete params.templateCode
      }
      this.$API.performanceManage.addTemplateData(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          // 保存成功跳转列表页
          // this.$router.push({
          //   path: '/supplier/template-manage/template-list'
          // })
          if (this.pageType === 'Add' || this.pageType === 'Version') {
            this.$router.push({
              // path: '/supplier/template-manage/template-detail',
              query: {
                type: 'Edit',
                id: res.data.id
              }
            })
            this.getDetailData()
          }
        }
      })
    },
    submit(addForm) {
      // 校验维度，指标类，指标的权重相加为100%
      // 遍历维度层数据
      // let dimensionWeight = 0
      // for (let i = 0; i < this.dimensionData.length; i++) {
      //   const element = this.dimensionData[i]
      //   dimensionWeight += parseInt(element.weight)
      //   // 遍历指标类层数据
      //   let indexClassWeight = 0
      //   for (let j = 0; j < element.childrenList.length; j++) {
      //     const item = element.childrenList[j]
      //     indexClassWeight += parseInt(item.weight)
      //     // 遍历指标层数据
      //     let indexWeight = 0
      //     for (let k = 0; k < item.childrenList.length; k++) {
      //       const itm = item.childrenList[k]
      //       indexWeight += parseInt(itm.weight)
      //     }
      //     if (indexWeight != 100) {
      //       this.$toast({
      //         content: this.$t('请检查指标明细下的子类指标的权重相加是否为100'),
      //         type: 'warning'
      //       })
      //       return false
      //     }
      //   }
      //   if (indexClassWeight != 100) {
      //     this.$toast({ content: this.$t('请检查指标明细的权重相加是否为100'), type: 'warning' })
      //     return false
      //   }
      // }
      // if (dimensionWeight != 100) {
      //   this.$toast({ content: this.$t('请检查指标维度的权重相加是否为100'), type: 'warning' })
      //   return false
      // }
      const params = {
        ...addForm,
        orgId: addForm.orgId[0],
        buyerAssessTemplateDimensionDTOList: this.changeFieldName(
          this.dimensionData,
          'buyerAssessTemplateIndexDTOList'
        )
      }
      // 模板编码不可输入，新增的时候为空不传值
      if (params.templateCode === '') {
        delete params.templateCode
      }
      // 函数柯里化--根据不同页面类型获取不同接口
      this.$API.performanceManage.addTemplateData(params).then((res) => {
        // 提交即保存后立即调用提交审批接口
        if (this.pageType === 'Add' || this.pageType === 'Version') {
          this.$router.push({
            // path: '/supplier/template-manage/template-detail',
            query: {
              type: 'Edit',
              id: res.data.id
            }
          })
          this.getDetailData()
        }
        this.$API.performanceManage.templateSubmitApply([res.data.id]).then((resp) => {
          if (resp.code == 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            // 保存并提交成功跳转列表页
            this.$router.push({
              path: '/supplier/template-manage/template-list'
            })
          }
        })
      })
    },
    // 新增指标维度的数据
    addDimension() {
      this.dimensionData.push({
        addId: 'add' + Math.random().toString(36).substr(3, 8),
        dimensionId: '',
        weight: 0,
        childrenList: []
      })
    },
    // 删除指标维度的数据
    deleteDimension(id) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          const dataSource = [...this.dimensionData]
          this.dimensionData = dataSource.filter((item) => item.addId !== id)
          this.selectedRowKeys = []
          this.pageConfig.data = []
          this.$toast({
            content: this.$t('删除成功'),
            type: 'success'
          })
        }
      })
    },
    // 指标维度下拉框值更改
    onCellChange(key, dataIndex, value) {
      const dataSource = [...this.dimensionData]
      const target = dataSource.find((item) => item.addId === key)
      const filteredItem = [...this.filteredOptions].find((item) => item.id === value)
      if (target && target[dataIndex] !== value) {
        target[dataIndex] = value
        target['dimensionName'] = filteredItem['dimensionName']
        // target['dimensionId'] = filteredItem['dimensionId']
        target['childrenList'] = []
        this.dimensionData = dataSource
        // 清空已选行
        this.selectedRowKeys = []
      }
    },
    // 父表单元格编辑事件
    onCellInputChange(key, dataIndex, value) {
      const dataSource = [...this.dimensionData]
      const target = dataSource.find((item) => item.addId === key)
      if (target) {
        target[dataIndex] = value
        this.dimensionData = dataSource
      }
    },
    getDetailApi(type) {
      if (type === 'Version') {
        return this.$API.performanceManage.createNewVersionTemplateData
      }
      return this.$API.performanceManage.getTemplateDetail
    },
    getDetailData() {
      // 函数柯里化---区分创建新版本以及其他页面类型下获取详情数据的接口
      this.getDetailApi(this.pageType)({ id: this.detailId })
        .then((res) => {
          const { data } = res
          let childrenList = data.buyerAssessTemplateDimensionDTOList || []
          // 指标和模板的接口字段没对齐，导致需要转换字段名
          this.dimensionData = this.changeFieldName(childrenList, 'childrenList')
          this.formConfig = { ...data }
          this.pageConfig.orgId = this.formConfig.orgId
          this.formConfig.orgId = [this.formConfig.orgId]
          this.selectedRowKeys = [] // fix新增后跳转编辑子表数据存在缓存的问题
        })
        .catch(() => {
          if (this.pageType === 'Version') {
            // 请求失败跳转列表页
            this.$router.push({
              path: '/supplier/template-manage/template-list'
            })
          }
        })
      // this.dimensionData = dimensionData
    }
  },
  computed: {
    pageType() {
      // 判断当前页面的类型，Add;Edit;Detail;Version
      return this.$route.query.type || ''
    },
    detailId() {
      // 非新增页面获取详情id
      return this.$route.query.id || ''
    },
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          if (selectedRows[0] && selectedRows[0]['dimensionId']) {
            // 表格单选触发的事件
            this.selectedRowKeys = selectedRowKeys
            // 保存当前选择行的维度id，新增维度明细时需要此查询
            this.pageConfig.dimensionId = selectedRows[0]['dimensionId']
            this.pageConfig.data = selectedRows[0]['childrenList']
          } else {
            this.$toast({
              content: this.$t('请勾选当前行指标维度'),
              type: 'warning'
            })
          }
        },
        type: 'radio'
      }
    }
  },
  created() {
    // 获取维度下拉列表
    this.$API.performanceManage.getDimensionlistQuery({ id: this.detailId }).then((res) => {
      const { data } = res
      this.filteredOptions = data
    })
    // 非新增页面获取详情
    if (this.pageType !== 'Add') {
      this.getDetailData()
    }
  }
}
</script>

<style lang="scss" scoped>
.template-container {
  background: #fff;
  height: auto;
  overflow: hidden;
  margin-top: 20px;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;
  ::v-deep.e-btn.e-flat.e-primary {
    color: #6386ce !important;
    font: inherit !important;
  }
}
.accordion-area {
  padding-right: 20px;
  margin-top: 20px;
  ::v-deep.e-acrdn-content {
    padding: 0 !important;
  }
  // & + .accordion-area {
  //   margin-top: 20px;
  // }
}
.full-height {
  height: 100%;
  overflow-x: auto;
  ::v-deep.e-content {
    height: unset !important;
  }
  ::v-deep.ant-table-body {
    overflow-x: auto !important;
  }
}
</style>
