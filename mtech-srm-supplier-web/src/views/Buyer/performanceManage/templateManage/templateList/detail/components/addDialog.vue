<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div style="padding-top: 16px">
      <collapse-search @reset="reset" @search="search">
        <mt-form ref="ruleForm">
          <mt-form-item :label="$t('指标/指标类名称')" label-style="top">
            <mt-input
              v-model="indexName"
              :show-clear-button="false"
              :placeholder="$t('指标/指标类名称')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <mt-template-page ref="templateRef" :template-config="dialogPageConfig" />
    </div>
  </mt-dialog>
</template>
<script>
import collapseSearch from '@/components/collapseSearch'
import { dialogPageConfig } from './config/index.js'
export default {
  components: {
    collapseSearch
  },
  props: {
    // 父组件传值 数据集合
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      header: this.$t('新增指标明细'),
      buttons: [
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        },
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      indexNameOptions: [
        {
          label: this.$t('质量'),
          value: '0'
        },
        {
          label: this.$t('交付'),
          value: '1'
        },
        {
          label: this.$t('市场'),
          value: '2'
        },
        {
          label: this.$t('采购'),
          value: '3'
        },
        {
          label: this.$t('开发'),
          value: '4'
        }
      ],
      dialogPageConfig: dialogPageConfig(),
      sourceData: [],
      indexName: '' // 查询条件
    }
  },
  methods: {
    search() {
      if (this.indexName) {
        const arr = []
        this.sourceData.forEach((i) => {
          if (i.indexName.includes(this.indexName)) {
            arr.push(i)
          }
        })
        this.$set(this.dialogPageConfig[0].grid, 'dataSource', arr)
      } else {
        this.queryEffectiveIndexList()
      }
    },
    reset() {
      this.indexName = ''
      this.sourceData = []
      this.queryEffectiveIndexList()
    },
    queryEffectiveIndexList() {
      this.$API.performanceManage
        .queryEffectiveIndexList({
          // dimensionId: '1507176787244679170'
          orgId: this.dialogData.orgId,
          dimensionId: this.dialogData.dimensionId
        })
        .then((res) => {
          if (res.code === 200) {
            this.sourceData = res.data
            this.$set(this.dialogPageConfig[0].grid, 'dataSource', res.data)
          }
        })
    },
    save() {
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      this.$emit('confirmSuccess', _records) // 确认
    },
    cancel() {
      this.$emit('handleAddDialogShow', false)
    }
  },
  created() {
    this.queryEffectiveIndexList()
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  }
}
</script>
