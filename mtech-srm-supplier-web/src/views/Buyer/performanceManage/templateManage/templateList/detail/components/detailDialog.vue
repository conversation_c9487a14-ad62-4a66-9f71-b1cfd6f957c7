<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div>
      <mt-template-page ref="templateRef" :template-config="dialogDetailPageConfig" />
    </div>
  </mt-dialog>
</template>
<script>
import { dialogDetailPageConfig, detailDialogColumn } from './config/index.js'
export default {
  props: {
    // 父组件传值 数据集合
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      header: this.$t('指标行详细信息'),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      dialogDetailPageConfig: dialogDetailPageConfig()
    }
  },
  methods: {
    cancel() {
      this.$emit('handleDetailDialogShow', false)
    }
  },
  created() {
    this.$API.performanceManage
      .getDimensionIndexDetail({
        id: this.dialogData.id
      })
      .then((res) => {
        const { code, data } = res
        if (code === 200) {
          let _columnData = detailDialogColumn(data.header.indexLineType)
          this.$set(this.dialogDetailPageConfig[0].grid, 'columnData', _columnData)
          this.$set(this.dialogDetailPageConfig[0].grid, 'dataSource', data.indexItems)
        }
      })
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  }
}
</script>
<style lang="scss">
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
/deep/ .mt-date-picker > .e-input-group {
  line-height: 32px;
}
/deep/ .icon-style {
  margin: -14px 0 0 42px;
  position: absolute;
}
/deep/ #describe {
  width: 100%;
}
</style>
