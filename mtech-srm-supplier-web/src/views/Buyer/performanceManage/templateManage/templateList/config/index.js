//维度设置Tab
import { i18n } from '@/main.js'
import Vue from 'vue'
import utils from '@/utils/utils'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
    // permission: ["O_02_0039"],
  },
  {
    id: 'Version',
    icon: 'icon_solid_edit',
    title: i18n.t('创建新版本')
    // permission: ["O_02_0039"],
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')

    // permission: ["O_02_0039"],
  },
  {
    id: 'Submit',
    icon: 'icon_table_submit',
    title: i18n.t('提交审批')
    // permission: ["O_02_0041"],
  },
  {
    id: 'Disable',
    icon: 'icon_table_enable',
    title: i18n.t('失效')
    // permission: ["O_02_0040"],
  },
  {
    id: 'EXdownload',
    icon: 'icon_solid_Download',
    title: i18n.t('导出')
    // permission: ["O_02_0043"],
  }
]
export const supplierDimensionOptions = utils.getSupplierDict('PER_SUPPLIER_TYPE') || []
export const applicableBusinessOptions = utils.getSupplierDict('PER_APPLICABLE_BUSINESS') || []
export const perClassOptions = utils.getSupplierDict('PER_CLASS') || []
export const perevaluationOptions = utils.getSupplierDict('EVALUATION-PERIOD') || []

export const statusOptions = [
  {
    status: 'DRAFT',
    label: i18n.t('拟定'),
    cssClass: ['status-label', 'status-enable']
  },
  {
    status: 'INVALID',
    label: i18n.t('失效'),
    cssClass: ['status-label', 'status-enable']
  },
  {
    status: 'EFFECTIVE',
    label: i18n.t('有效'),
    cssClass: ['status-label', 'status-disable']
  },
  {
    status: 'APPROVING',
    label: i18n.t('审批中'),
    cssClass: ['status-label', 'status-disable']
  },
  {
    status: 'REJECT',
    label: i18n.t('驳回'),
    cssClass: ['status-label', 'status-disable']
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'templateCode',
    headerText: i18n.t('模板编码'),
    // ignore: true,
    cssClass: 'field-content',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] === 'DRAFT' || data['status'] === 'REJECT'
        }
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] === 'DRAFT'
        }
      },
      {
        id: 'detail',
        icon: 'icon_solid_Submit',
        title: i18n.t('明细')
      }
    ]
  },
  {
    field: 'evaluationCycleCode',
    headerText: i18n.t('评价周期'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: perevaluationOptions,
      fields: { text: 'dictName', value: 'dictCode' }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: perevaluationOptions,
      fields: { text: 'dictName', value: 'dictCode' }
    },
    editTemplate: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.evaluationCycleCode) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = perevaluationOptions.filter((j) => j.dictCode === status)
              return label[0]['dictName']
            }
          }
        })
      }
    }
    // ignore: true
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织')
    // ignore: true
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板名称')
    // ignore: true
  },
  // {
  //   field: "version",
  //   headerText: i18n.t("版本号"),
  //   ignore: true,
  // },
  {
    field: 'templateDesc',
    headerText: i18n.t('模板描述')
  },
  {
    field: 'status',
    headerText: i18n.t('模板状态'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: statusOptions,
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'achievementAssessSupplierType',
    headerText: i18n.t('供应商维度'),
    width: 120,
    valueConverter: {
      type: 'map',
      map: supplierDimensionOptions,
      fields: { text: 'dictName', value: 'dictCode' }
    }
  },
  {
    field: 'achievementAssessBiz',
    headerText: i18n.t('适用业务'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: applicableBusinessOptions,
      fields: { text: 'dictName', value: 'dictCode' }
    }
  },
  {
    field: 'achievementAssessType',
    headerText: i18n.t('类别'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: perClassOptions,
      fields: { text: 'dictName', value: 'dictCode' }
    }
  },
  {
    field: 'effectiveDate',
    headerText: i18n.t('生效日期'),
    width: 100,
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('effectiveDate', {
          template: `
              <div class="time-box">
                {{ data.effectiveDate }}
              </div>`
        })
      }
    }
  },
  {
    field: 'invalidDate',
    headerText: i18n.t('失效日期'),
    width: 100,
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ data.invalidDate }}
              </div>`
        })
      }
    }
    // type: "date",
    // format: "yyyy-MM-dd",
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: 80
    // ignore: true
  }
  // {
  //   field: 'id',
  //   headerText: i18n.t('明细'),
  //   width: 80,
  //   ignore: true,
  //   template: () => {
  //     return {
  //       template: Vue.component('template-detail', {
  //         template: `
  //             <span style="color: #6386c1; cursor: pointer;" @click="toDetail(data)">明细</span>`,
  //         methods: {
  //           toDetail(data) {
  //             this.$router.push({
  //               path: '/supplier/template-manage/template-detail',
  //               query: {
  //                 type: 'Detail',
  //                 id: data.id
  //               }
  //             })
  //           }
  //         }
  //       })
  //     }
  //   }
  // }
  // {
  //   field: 'operator',
  //   headerText: i18n.t('操作'),
  //   width: 80,
  //   ignore: true,
  //   template: () => {
  //     return {
  //       template: Vue.component('operator', {
  //         template: `
  //             <span v-if="data.status == 'DRAFT'" style="color: #6386c1; cursor: pointer;" @click="toEdit(data)">编辑</span>`,
  //         methods: {
  //           toEdit(data) {
  //             this.$router.push({
  //               path: '/supplier/template-manage/template-detail',
  //               query: {
  //                 type: 'Edit',
  //                 id: data.id
  //               }
  //             })
  //           }
  //         }
  //       })
  //     }
  //   }
  // }
]

export const pageConfig = (url) => [
  {
    gridId: 'e5bfb4f9-e323-44db-9f28-959301e7af4b',
    toolbar,
    grid: {
      columnData,
      useToolTemplate: false,
      asyncConfig: {
        url: url
      }
    }
  }
]
