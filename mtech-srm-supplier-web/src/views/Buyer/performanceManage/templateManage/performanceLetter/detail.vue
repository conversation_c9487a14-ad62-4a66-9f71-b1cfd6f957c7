<!-- 绩效通知函模板-详情 -->
<template>
  <div class="detail">
    <div class="header">
      <span class="header-title">{{ getTitle() }}</span>
      <div>
        <mt-button css-class="e-flat" :is-primary="true" @click="handleSave">
          {{ $t('保存') }}
        </mt-button>
        <mt-button css-class="e-flat" :is-primary="true" @click="handleBack">
          {{ $t('返回') }}
        </mt-button>
      </div>
    </div>
    <div class="content">
      <mt-form ref="formRef" :model="modelForm" :rules="rules">
        <mt-form-item prop="orgCode" :label="$t('组织')">
          <mt-DropDownTree
            :placeholder="$t('请选择组织')"
            :popup-height="500"
            :show-clear-button="false"
            :enabled="true"
            :fields="orgFields"
            v-model="modelForm.orgId"
            :allow-filtering="true"
            filter-type="Contains"
            id="baseTreeSelect"
            @select="selectOrg"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="notificationType" :label="$t('模板类型')">
          <mt-select
            v-model="modelForm.notificationType"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="false"
            :data-source="notificationTypeOptions"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :placeholder="$t('请选择')"
            @change="notificationTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="notificationName" :label="$t('模板名称')">
          <mt-input
            v-model="modelForm.notificationName"
            :show-clear-button="false"
            :disabled="false"
            :placeholder="$t('模板名称')"
            :max-length="32"
          ></mt-input>
        </mt-form-item>
      </mt-form>

      <div class="edit-box">
        <rich-text-editor
          ref="editorRef"
          v-model.trim="templateText"
          :toolbar="clientToolbar"
          :disabled="false"
          :height="600"
        >
        </rich-text-editor>
      </div>
    </div>
  </div>
</template>

<script>
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
import { defaultTemp, defaultTemp1 } from './config'
export default {
  components: {
    RichTextEditor
  },
  data() {
    return {
      modelForm: {
        orgCode: null
      },
      rules: {
        orgCode: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        notificationType: [
          {
            required: true,
            message: this.$t('请选择模板类型'),
            trigger: 'blur'
          }
        ],
        notificationName: [
          {
            required: true,
            message: this.$t('请输入模板名称'),
            trigger: 'blur'
          }
        ]
      },

      orgFields: {}, // 组织下拉列表的配置信息

      variableList: [],
      clientToolbar: `bold italic underline | formatselect alignleft aligncenter alignright alignjustify bullist numlist | table link image backcolor | code undo redo paste | lineheight| parmarsSetButton | fullscreen`,
      templateText: '',

      notificationTypeOptions: [],
      firstRender: true
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    }
  },
  created() {
    this.queryOrgTree()
    this.getNotificationTypeOptions()
    if (['edit', 'detail'].includes(this.$route.query?.type)) {
      let row = JSON.parse(sessionStorage.getItem('tempRow'))
      this.templateText = row.templateText
      setTimeout(() => {
        this.modelForm = {
          ...row
        }
        this.modelForm.orgId = [row.orgId]
      }, 600)
    }
  },
  methods: {
    notificationTypeChange(e) {
      if (!this.firstRender || this.pageType === 'add') {
        if (e) {
          if (e.itemData?.itemCode === 'JX') {
            this.templateText = defaultTemp1
          } else if (e.itemData?.itemCode === 'ZH') {
            this.templateText = defaultTemp
          }
        }
      } else {
        this.firstRender = false
      }
    },
    getTitle() {
      let title = this.$t('绩效通知函模板')
      switch (this.pageType) {
        case 'edit':
          return this.$t('编辑') + title
        case 'detail':
          return this.$t('查看') + title
        default:
          return this.$t('新增') + title
      }
    },
    getNotificationTypeOptions() {
      this.$API.assessManage
        .getDictCode({
          dictCode: 'PERFORMANCE_NOTIFICATION_TEMPLATE_TYPE'
        })
        .then((res) => {
          if (res.code === 200) {
            this.notificationTypeOptions = res.data
          }
        })
    },
    queryOrgTree() {
      this.$API.performanceManage.getListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.orgFields = {
            dataSource: res.data,
            value: 'id',
            text: 'orgName',
            child: 'childrenList'
          }
        }
      })
    },
    selectOrg(e) {
      // 组织需要保存组织名称 和组织编码
      const { itemData } = e
      this.selectOrgCircle(this.orgFields.dataSource, itemData.id)
    },
    selectOrgCircle(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.modelForm.orgId = [ele.id]
          this.modelForm.orgCode = ele.orgCode
          this.modelForm.orgName = ele.orgName
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.selectOrgCircle(ele.childrenList, id)
        }
      })
    },
    handleSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let params = {
            ...this.modelForm,
            templateText: this.templateText
          }
          params.orgId = this.modelForm.orgId[0]
          this.$API.performanceManage.saveNotificationTemplateApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$router.push({
                name: 'performance-letter',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            }
          })
        }
      })
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  height: auto;
  padding: 20px;
  background: #fff;
  overflow: hidden;
}
.header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  .header-title {
    color: #292929;
    font-family: PingFangSC;
    font-size: 20px;
    font-weight: 600;
  }
  ::v-deep.e-btn.e-flat.e-primary {
    color: #6386ce !important;
    font: inherit !important;
  }
}
.content {
  /deep/ .mt-form-item {
    width: calc(25% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-top: 5px;
    margin-right: 20px;

    .full-width {
      width: calc(100% - 20px) !important;
    }
    .e-ddt .e-ddt-icon::before {
      content: '\e36a';
      font-size: 16px;
    }
  }
  .check-area {
    transform: translateY(10px);
  }
}
</style>
