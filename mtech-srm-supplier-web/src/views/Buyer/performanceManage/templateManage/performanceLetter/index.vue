<!-- 绩效通知函模板 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('通知函编码')" prop="notificationCode">
          <mt-input
            v-model="searchFormModel.notificationCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('通知函名称')" prop="notificationName">
          <mt-input
            v-model="searchFormModel.notificationName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('组织')" prop="orgId">
          <mt-DropDownTree
            v-model="searchFormModel.orgId"
            :placeholder="$t('请选择组织')"
            :popup-height="500"
            :show-clear-button="false"
            :enabled="true"
            :fields="orgFields"
            :allow-filtering="true"
            filter-type="Contains"
            id="baseTreeSelect"
            @select="selectOrg"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item :label="$t('模板类型')" prop="notificationType">
          <mt-select
            v-model="searchFormModel.notificationType"
            :data-source="notificationTypeOptions"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'createTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="6c4f742d-8ad5-462e-92a8-2e610d00d5c9"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #notificationCode="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleEdit(row)">
          {{ row.notificationCode }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions } from './config'
import dayjs from 'dayjs'
export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        {
          code: 'add',
          name: this.$t('新增'),
          status: 'info',
          loading: false
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: 'info',
          loading: false
        },
        {
          code: 'enable',
          name: this.$t('启用'),
          status: 'info',
          loading: false
        },
        {
          code: 'disable',
          name: this.$t('停用'),
          status: 'info',
          loading: false
        }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      notificationTypeOptions: [],

      orgFields: {} // 组织下拉列表的配置信息
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.queryOrgTree()
    this.getNotificationTypeOptions()
    this.getTableData()
  },
  methods: {
    queryOrgTree() {
      this.$API.performanceManage.getListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.orgFields = {
            dataSource: res.data,
            value: 'id',
            text: 'orgName',
            child: 'childrenList'
          }
        }
      })
    },
    selectOrg(e) {
      // 组织需要保存组织名称 和组织编码
      const { itemData } = e
      this.selectOrgCircle(this.orgFields.dataSource, itemData.id)
    },
    selectOrgCircle(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.searchFormModel.orgCode = ele.orgCode
          this.searchFormModel.orgName = ele.orgName
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.selectOrgCircle(ele.childrenList, id)
        }
      })
    },
    getNotificationTypeOptions() {
      this.$API.assessManage
        .getDictCode({
          dictCode: 'PERFORMANCE_NOTIFICATION_TEMPLATE_TYPE'
        })
        .then((res) => {
          if (res.code === 200) {
            this.notificationTypeOptions = res.data
          }
        })
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.performanceManage
        .pageNotificationTemplateApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = selectedRecords.map((v) => v.id)
      const commonToolbar = ['delete', 'enable', 'disable']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(ids)
            }
          })
          break
        case 'enable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认启用？')
            },
            success: () => {
              this.handleEnable(ids)
            }
          })
          break
        case 'disable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认停用？')
            },
            success: () => {
              this.handleDisable(ids)
            }
          })
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$router.push({
        name: 'performance-letter-detail',
        query: {
          type: 'add',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleEdit(row) {
      sessionStorage.setItem('tempRow', JSON.stringify(row))
      this.$router.push({
        name: 'performance-letter-detail',
        query: {
          type: row.status !== 1 ? 'edit' : 'detail',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleDelete(ids) {
      this.$API.performanceManage.deleteNotificationTemplateApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleEnable(ids) {
      this.$API.performanceManage
        .updateStatusNotificationTemplateApi({ ids, status: 1 })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('启用成功'), type: 'success' })
            this.handleSearch()
          }
        })
    },
    handleDisable(ids) {
      this.$API.performanceManage
        .updateStatusNotificationTemplateApi({ ids, status: -1 })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('停用成功'), type: 'success' })
            this.handleSearch()
          }
        })
    }
  }
}
</script>
