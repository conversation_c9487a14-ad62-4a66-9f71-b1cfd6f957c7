<!-- 采方-通知函抄送人设置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="400"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item v-if="reRender" prop="orgCode" :label="$t('组织')">
        <mt-DropDownTree
          v-if="actionType === 'add'"
          v-model="formData.orgId"
          :placeholder="$t('请选择组织')"
          :popup-height="500"
          :show-clear-button="false"
          :enabled="true"
          :fields="orgFields"
          :allow-filtering="true"
          filter-type="Contains"
          id="baseTreeSelect"
          @select="selectOrg"
        ></mt-DropDownTree>
        <mt-input v-else v-model="formData.orgName" disabled />
      </mt-form-item>
      <mt-form-item prop="categoryCode" :label="$t('品类')">
        <RemoteAutocomplete
          v-if="reRender"
          v-model="formData.categoryCode"
          url="/masterDataManagement/tenant/category/paged-query"
          :multiple="false"
          :placeholder="$t('请选择')"
          :fields="{ text: 'categoryName', value: 'categoryCode' }"
          :search-fields="['categoryName', 'categoryCode']"
          :disabled="actionType === 'edit'"
          @change="categoryChange"
        />
      </mt-form-item>
      <mt-form-item prop="notificationType" :label="$t('模板类型')">
        <mt-select
          v-model="formData.notificationType"
          :data-source="notificationTypeOptions"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :disabled="actionType === 'edit'"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="cc" :label="$t('抄送人')">
        <RemoteAutocomplete
          v-if="reRender"
          v-model="formData.cc"
          url="/masterDataManagement/tenant/employee/paged-query"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'employeeName', value: 'externalCode' }"
          :search-fields="['employeeName', 'employeeCode']"
          @multiChange="ccMultiChange"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { cloneDeep } from 'lodash'
export default {
  components: { RemoteAutocomplete },
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        orgCode: null
      },
      rules: {
        orgCode: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        categoryCode: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        notificationType: [
          {
            required: true,
            message: this.$t('请选择模板类型'),
            trigger: 'blur'
          }
        ],
        cc: [
          {
            required: true,
            message: this.$t('请选择抄送人'),
            trigger: 'blur'
          }
        ]
      },

      notificationTypeOptions: [],

      reRender: false
    }
  },
  created() {
    this.queryOrgTree()
    this.getNotificationTypeOptions()
  },
  methods: {
    queryOrgTree() {
      this.$API.performanceManage.getListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.orgFields = {
            dataSource: res.data,
            value: 'id',
            text: 'orgName',
            child: 'childrenList'
          }
        }
      })
    },
    selectOrg(e) {
      // 组织需要保存组织名称 和组织编码
      const { itemData } = e
      this.selectOrgCircle(this.orgFields.dataSource, itemData.id)
    },
    selectOrgCircle(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formData.orgCode = ele.orgCode
          this.formData.orgName = ele.orgName
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.selectOrgCircle(ele.childrenList, id)
        }
      })
    },
    getNotificationTypeOptions() {
      this.$API.assessManage
        .getDictCode({
          dictCode: 'PERFORMANCE_NOTIFICATION_TEMPLATE_TYPE'
        })
        .then((res) => {
          if (res.code === 200) {
            this.notificationTypeOptions = res.data
          }
        })
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType

      if (actionType === 'edit') {
        this.formData = cloneDeep(row)
        this.formData.cc = this.formData.accountCode.split(',')
      }
      this.reRender = true
    },
    beforeOpen() {
      this.formData = {
        orgCode: null
      }
      this.$refs.ruleForm.clearValidate()
      this.reRender = false
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {
        orgCode: null
      }
      this.reRender = false
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {
        orgCode: null
      }
      this.reRender = false
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = {
        id: this.formData?.id,
        orgCode: this.formData?.orgCode,
        orgName: this.formData?.orgName,
        categoryCode: this.formData?.categoryCode,
        categoryName: this.formData?.categoryName,
        notificationType: this.formData?.notificationType,
        emailAddressList: this.formData?.emailAddressList
      }
      const api = this.$API.performanceManage.saveCcSettingApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    categoryChange(e) {
      this.formData.categoryName = e.itemData?.categoryName
    },
    ccMultiChange(e) {
      let list = e.map((item) => {
        return {
          accountCode: item.externalCode,
          accountName: item.employeeName,
          emailAddress: item.email
        }
      })
      this.formData.emailAddressList = list
    }
  }
}
</script>
