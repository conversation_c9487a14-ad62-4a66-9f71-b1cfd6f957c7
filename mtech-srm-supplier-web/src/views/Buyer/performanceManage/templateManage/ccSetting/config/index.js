import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'orgName',
    title: i18n.t('组织')
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称')
  },
  {
    field: 'notificationTypeDesc',
    title: i18n.t('模板类型')
  },
  {
    field: 'accountName',
    title: i18n.t('抄送人')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
