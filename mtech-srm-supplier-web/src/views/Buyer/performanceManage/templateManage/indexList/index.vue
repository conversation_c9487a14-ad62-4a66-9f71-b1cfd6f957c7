<template>
  <!-- 模板清单 -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  methods: {
    // 工具栏点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let notStatus0Records = _selectGridRecords.filter((item) => item.status === 2) // 非拟定状态数据
      let notStatus1Records = _selectGridRecords.filter((item) => item.status !== 2) // 非有效状态数据
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Enable' || e.toolbar.id == 'Delete' || e.toolbar.id == 'Disable')
      ) {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id === 'Add') {
        // 新增
        this.handleAdd()
      } else if (e.toolbar.id === 'Delete') {
        // 删除
        if (notStatus0Records.length > 0) {
          this.$toast({ content: this.$t('只能删除拟定状态数据'), type: 'warning' })
          return
        }
        this.handleDelete(_selectGridRecords)
      } else if (e.toolbar.id === 'Enable') {
        // 生效
        if (notStatus0Records.length > 0) {
          this.$toast({ content: this.$t('只能生效拟定状态数据'), type: 'warning' })
          return
        }
        this.handleStatus(_selectGridRecords, 2)
      } else if (e.toolbar.id === 'Disable') {
        // 失效
        if (notStatus1Records.length > 0) {
          this.$toast({ content: this.$t('只能失效有效状态数据'), type: 'warning' })
          return
        }
        this.handleStatus(_selectGridRecords, 3)
      } else if (e.toolbar.id === 'IndexExport') {
        // 导出
        this.handleExport()
      } else if (e.toolbar.id === 'IndexImport') {
        // 导入
        this.templateGradeImportFn()
      }
    },
    // 单元格工具点击事件
    handleClickCellTool(e) {
      if (e.tool.id == 'Edit') {
        // 编辑
        this.handleEdit(e.data)
      } else if (e.tool.id == 'Delete') {
        // 删除
        this.handleDelete([e.data])
      } else if (e.tool.id == 'Enable') {
        // 生效
        this.handleStatus([e.data], 2)
      } else if (e.tool.id == 'Disable') {
        // 失效
        this.handleStatus([e.data], 3)
      }
    },
    // 单元格字段点击事件
    handleClickCellTitle(e) {
      if (e.field !== 'dimensionName') return
      this.$router.push({
        path: 'index-detail',
        query: {
          id: e.data.id
        }
      })
    },
    // 指标新增
    handleAdd() {
      this.$router.push({
        path: 'index-add'
      })
    },
    // 指标编辑
    handleEdit(data) {
      this.$router.push({
        path: 'index-edit',
        query: {
          id: data.id
        }
      })
    },
    // 指标删除
    handleDelete(records) {
      let ids = records.map((v) => {
        return v.id
      })
      ids = ids.join(',')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceManage.delIndexList({ ids }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // 状态操作
    handleStatus(records, status) {
      let _ids = records.map((v) => {
        return v.id
      })
      _ids = _ids.join(',')
      let _statusMap = {
        1: this.$t('拟定'),
        2: this.$t('生效'),
        3: this.$t('失效')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceManage.updateIndexStatus({ ids: _ids, status }).then((res) => {
            if (res.code === 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 20 },
        rules: rule.rules || []
      }
      this.$API.performanceManage.exportIndexList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 导入
    templateGradeImportFn() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.performanceManage.ImportIndexList,
          downloadTemplateApi: this.$API.performanceManage.ImportTemplateList
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
/deep/ {
  .mt-select-index {
    float: left;
  }
}
</style>
