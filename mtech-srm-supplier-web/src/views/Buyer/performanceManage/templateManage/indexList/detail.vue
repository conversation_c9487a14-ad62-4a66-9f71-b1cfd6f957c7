<template>
  <div class="index-box mt-flex">
    <div class="main-container mt-flex-direction-column" style="width: 100%">
      <div class="operate-bars">
        <div class="operate-bar">
          <div class="op-item mt-flex" @click="handleback">
            {{ $t('返回') }}
          </div>
          <div class="op-item mt-flex" v-throttle="[handleSave, 'click', 1200]" v-if="!isDetail">
            {{ $t('保存') }}
          </div>
        </div>
        <!-- 顶部主要信息 -->
        <div class="main-info">
          <mt-form ref="listForm" :model="listForm" :rules="formRules">
            <mt-form-item prop="status" :label="$t('状态')">
              <!-- <mt-input v-model="status" float-label-type="Never" disabled></mt-input> -->
              <mt-select
                :data-source="statusData"
                v-model="status"
                :show-clear-button="true"
                :disabled="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="orgCode" :label="$t('事业部')">
              <mt-select
                :data-source="departmentData"
                v-model="listForm.orgCode"
                :fields="orgFields"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择事业部')"
                id="baseTreeSelect"
                :disabled="isDetail"
                @select="selectOrg"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="dimensionId" :label="$t('指标维度')">
              <mt-select
                :data-source="dimensionData"
                v-model="listForm.dimensionId"
                :fields="{ text: 'dimensionName', value: 'id' }"
                :show-clear-button="true"
                :placeholder="$t('请选择指标维度')"
                :disabled="isDetail"
                :allow-filtering="true"
                :filtering="serchText"
                @change="dimensionChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="indexType" :label="$t('指标类型')">
              <mt-select
                :data-source="indexTypeData"
                v-model="listForm.indexType"
                :show-clear-button="true"
                :disabled="isDetail"
                :placeholder="$t('请选择指标类型')"
                @change="indexTypeChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="indexName" :label="$t('指标') + '/' + $t('指标类名称')">
              <mt-input
                v-model="listForm.indexName"
                float-label-type="Never"
                :disabled="isDetail"
                :max-length="32"
                :placeholder="$t('请输入') + $t('指标') + '/' + $t('指标类名称')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="indexItemType" :label="$t('指标细项类型')">
              <mt-select
                :data-source="indexItemTypeData"
                v-model="listForm.indexItemType"
                :readonly="true"
                :placeholder="$t('请选择指标细项类型')"
                :disabled="isDetail"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              prop="indexLineType"
              v-if="listForm.indexType === 2"
              :label="$t('指标行类型')"
            >
              <mt-select
                :data-source="indexLineTypeData"
                v-model="listForm.indexLineType"
                :show-clear-button="true"
                :placeholder="$t('请选择指标行类型')"
                :disabled="isDetail"
                @change="indexLineTypeChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="dataSource" v-if="listForm.indexType === 2" :label="$t('数据来源')">
              <mt-select
                :data-source="dataSourceData"
                v-model="listForm.dataSource"
                :show-clear-button="true"
                :placeholder="$t('请选择数据来源')"
                :disabled="isDetail"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              prop="scoreDimension"
              v-if="listForm.indexType === 2"
              :label="$t('打分维度')"
            >
              <mt-select
                :data-source="scoreDimensionData"
                v-model="listForm.scoreDimension"
                :show-clear-button="true"
                :placeholder="$t('请选择打分维度')"
                :disabled="isDetail"
                @change="scoreDimensionChange"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              prop="achievementAssessScoreType"
              v-if="listForm.indexType === 2"
              :label="$t('打分类型')"
            >
              <mt-select
                :data-source="scoreTypeData"
                v-model="listForm.achievementAssessScoreType"
                :placeholder="$t('请选择打分类型')"
                :disabled="isDetail || listForm.scoreDimension === 2"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              prop="originScore"
              v-if="listForm.indexType === 2"
              :label="$t('原始分数')"
            >
              <mt-input
                v-model="listForm.originScore"
                :placeholder="$t('请输入原始分数')"
                :disabled="isDetail"
                type="number"
                :min="0.01"
                :precision="2"
                class="number-item"
                @change="numberChange(listForm, 'originScore')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="maxDeductScore"
              v-if="listForm.indexType === 2"
              :label="$t('加/扣分上限')"
            >
              <mt-input
                v-model="listForm.maxDeductScore"
                :placeholder="$t('提示：加分为正数，扣分为负数')"
                type="number"
                :disabled="isDetail"
                :precision="2"
                class="number-item"
                @change="numberChange(listForm, 'maxDeductScore')"
              ></mt-input>
            </mt-form-item>
            <div class="mt-form-item check-area" v-if="listForm.indexType === 2">
              <mt-checkbox
                :disabled="isDetail"
                :checked="listForm.additional"
                @change="(e) => (listForm.additional = e.checked)"
              />
              <span class="label" style="margin-left: -10px">{{ $t('是否附加项') }}</span>
            </div>
            <mt-form-item prop="description" :label="$t('评分逻辑描述')" class="full-width">
              <mt-input
                v-model="listForm.description"
                :multiline="true"
                :maxlength="200"
                :disabled="isDetail"
              ></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="index-list">
        <!-- 指标类型的判断 -->
        <mt-template-page
          v-if="listForm.indexType === 2"
          ref="indexTemplateRef"
          :template-config="indexPageConfig"
          @handleClickToolBar="handleClickToolBar"
          @cellEdit="rowCellEdit"
        />
        <mt-template-page
          v-show="listForm.indexType === 1"
          ref="indexCategoryTemplateRef"
          :template-config="indexCategoryPageConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </div>
</template>
<script>
import {
  toolbar,
  indexPageConfig,
  indexCategoryPageConfig,
  indexCategoryColumnData,
  defaultIndexColumnData,
  perIndexColumnData,
  numIndexColumnData
} from './config/detail'
import utils from '@/utils/utils'

export default {
  data() {
    return {
      status: 1,
      listForm: {
        orgId: '',
        orgCode: '',
        orgName: '',
        dimensionId: null,
        indexType: null,
        indexName: '',
        indexItemType: null,
        indexLineType: null,
        dataSource: null,
        originScore: null,
        maxDeductScore: null,
        description: '',
        indexItemDTOList: [],
        scoreDimension: [],
        achievementAssessScoreType: null,
        additional: false
      },
      orgFields: { text: 'orgName', value: 'orgCode' }, // 组织下拉列表的配置信息
      dimensionId: '',
      disabled: false,
      indexPageConfig: indexPageConfig,
      indexCategoryPageConfig: indexCategoryPageConfig,
      indexCategoryColumnData: indexCategoryColumnData,
      // formRules: {
      //   orgCode: [
      //     {
      //       required: true,
      //       message: this.$t('请选择事业部'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   indexType: [
      //     {
      //       required: true,
      //       message: this.$t('请选择指标类型'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   indexName: [
      //     {
      //       required: true,
      //       message: this.$t('请输入指标/指标名称'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   indexItemType: [
      //     {
      //       required: true,
      //       message: this.$t('请选择指标细项类型'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   indexLineType: [
      //     {
      //       required: true,
      //       message: this.$t('请选择指标行类型'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   dataSource: [
      //     {
      //       required: true,
      //       message: this.$t('请选择数据来源'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   scoreDimension: [
      //     {
      //       required: true,
      //       message: this.$t('请选择打分维度'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   achievementAssessScoreType: [
      //     {
      //       required: true,
      //       message: this.$t('请选择打分类型'),
      //       trigger: 'blur'
      //     }
      //   ],
      //   originScore: [
      //     {
      //       required: true,
      //       message: this.$t('请输入正确的原始分数'),
      //       trigger: 'blur',
      //       pattern: /^([1-9][0-9]*|0)(\.[0-9]*[1-9])?$/
      //     }
      //   ],
      //   // maxDeductScore: [
      //   //   {
      //   //     required: true,
      //   //     message: this.$t('请输入正确的扣分上限'),
      //   //     trigger: 'blur'
      //   //     // pattern: /^[+]{0,1}(\d+)$/
      //   //   }
      //   // ],
      //   dimensionId: [
      //     {
      //       required: true,
      //       message: this.$t('请选择指标维度'),
      //       trigger: 'blur'
      //     }
      //   ]
      // },
      departmentData: [],
      dimensionData: [],
      statusData: [
        { text: this.$t('拟定'), value: 1 },
        { text: this.$t('有效'), value: 2 },
        { text: this.$t('失效'), value: 3 }
      ],
      indexTypeData: [
        { text: this.$t('指标类'), value: 1 },
        { text: this.$t('指标'), value: 2 }
      ],
      indexItemTypeData: [
        { text: this.$t('指标细项'), value: 1 },
        { text: this.$t('得分项'), value: 2 }
      ],
      indexLineTypeData: [
        { text: this.$t('文本'), value: 1 },
        { text: this.$t('数字'), value: 2 },
        { text: this.$t('百分比'), value: 3 }
      ],
      dataSourceData: [
        { text: this.$t('系统自动'), value: 1 },
        { text: this.$t('手工录入'), value: 2 }
      ],
      scoreDimensionData: [
        { text: this.$t('指标名称'), value: 1 },
        { text: this.$t('指标说明'), value: 2 }
      ],
      // scoreTypeData: utils.getSupplierDict('PER_GRADE_TYPE') || [],
      scoreTypeData: [
        { text: this.$t('得分'), value: '1' },
        { text: this.$t('评分'), value: '2' }
      ],
      postId: '', //保存后记录id
      flag: 0
    }
  },
  computed: {
    isDetail() {
      return this.$route.name === 'index-detail'
    },
    isAdd() {
      return this.$route.name === 'index-add'
    },
    isEdit() {
      return this.$route.name === 'index-edit'
    },
    formRules() {
      const rules = {
        orgCode: [
          {
            required: true,
            message: this.$t('请选择事业部'),
            trigger: 'blur'
          }
        ],
        indexType: [
          {
            required: true,
            message: this.$t('请选择指标类型'),
            trigger: 'blur'
          }
        ],
        indexName: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('指标') + '/' + this.$t('指标名称'),
            trigger: 'blur'
          }
        ],
        indexItemType: [
          {
            required: true,
            message: this.$t('请选择指标细项类型'),
            trigger: 'blur'
          }
        ],
        indexLineType: [
          {
            required: true,
            message: this.$t('请选择指标行类型'),
            trigger: 'blur'
          }
        ],
        dataSource: [
          {
            required: true,
            message: this.$t('请选择数据来源'),
            trigger: 'blur'
          }
        ],
        scoreDimension: [
          {
            required: true,
            message: this.$t('请选择打分维度'),
            trigger: 'blur'
          }
        ],
        // achievementAssessScoreType: [
        //   {
        //     required: true,
        //     message: this.$t('请选择打分类型'),
        //     trigger: 'blur'
        //   }
        // ],
        originScore: [
          {
            required: true,
            message: this.$t('请输入正确的原始分数'),
            trigger: 'blur',
            pattern: /^([1-9][0-9]*|0)(\.[0-9]*[1-9])?$/
          }
        ],
        dimensionId: [
          {
            required: true,
            message: this.$t('请选择指标维度'),
            trigger: 'blur'
          }
        ]
      }
      // if (this.flag == 0) {
      //   rules.achievementAssessScoreType = [
      //     {
      //       required: true,
      //       message: this.$t('请选择打分类型'),
      //       trigger: 'blur'
      //     }
      //   ]
      // } else if (this.flag == 1) {
      //   if (rules.achievementAssessScoreType) delete rules.achievementAssessScoreType
      // }
      return rules
    }
  },
  watch: {
    'listForm.indexLineType': {
      handler(v) {
        // 指标行类型的判断
        let _columnData =
          v === 3 ? perIndexColumnData : v === 2 ? numIndexColumnData : defaultIndexColumnData
        if (this.isDetail) {
          _columnData = _columnData.filter((item) => item.type !== 'checkbox')
        }
        const scoreDimensionIndex = _columnData.findIndex((item) => {
          return item.field === 'scoreWeight'
        })
        if (
          (this.listForm.scoreDimension === 2 ||
            this.listForm.achievementAssessScoreType === '1') &&
          scoreDimensionIndex > -1
        ) {
          // 打分维度的判断
          _columnData = _columnData.filter((item) => item.field !== 'scoreWeight')
        }
        this.$set(this.indexPageConfig[0].grid, 'columnData', _columnData)
        // this.$refs.indexTemplateRef.getCurrentTabRef().grid.refreshColumns()
      },
      immediate: true
    },
    'listForm.scoreDimension': {
      handler(v) {
        // 指标行类型的判断
        let _columnData =
          this.listForm.indexLineType === 3
            ? perIndexColumnData
            : this.listForm.indexLineType === 2
            ? numIndexColumnData
            : defaultIndexColumnData
        const scoreDimensionIndex = _columnData.findIndex((item) => {
          return item.field === 'scoreWeight'
        })
        if (v === 2 && scoreDimensionIndex > -1) {
          // 打分维度的判断
          _columnData = _columnData.filter((item) => item.field !== 'scoreWeight')
        }
        this.$set(this.indexPageConfig[0].grid, 'columnData', _columnData)
        // this.$refs.indexTemplateRef.getCurrentTabRef().grid.refreshColumns()
      },
      immediate: true
    },
    'listForm.achievementAssessScoreType': {
      handler(v) {
        console.log(
          'achievementAssessScoreTypeachievementAssessScoreTypeachievementAssessScoreType',
          v
        )
        // 打分类型的判断
        let _columnData =
          this.listForm.indexLineType === 3
            ? perIndexColumnData
            : this.listForm.indexLineType === 2
            ? numIndexColumnData
            : defaultIndexColumnData
        const scoreDimensionIndex = _columnData.findIndex((item) => {
          return item.field === 'scoreWeight'
        })
        if (v === '1' && scoreDimensionIndex > -1) {
          // 打分维度的判断
          _columnData = _columnData.filter((item) => item.field !== 'scoreWeight')
        }
        this.$set(this.indexPageConfig[0].grid, 'columnData', _columnData)
        // this.$refs.indexTemplateRef.getCurrentTabRef().grid.refreshColumns()
      },
      immediate: true
    }
  },
  mounted() {
    this.serchText = utils.debounce(this.serchText, 1000)
    this.getIndexDimension()
    // 组织下拉列表
    this.queryOrgTree()
    // 请求组织列表的接口并赋值给templateOrgOptions
    //如果由编辑页进入 初始化渲染数据
    const _columnData = this.isDetail
      ? this.indexCategoryColumnData.filter((item) => item.type !== 'checkbox')
      : this.indexCategoryColumnData
    const _toolbar = this.isDetail ? [] : toolbar
    this.$set(this.indexCategoryPageConfig[0], 'toolbar', _toolbar)
    this.$set(this.indexCategoryPageConfig[0].grid, 'columnData', _columnData)
    this.$set(this.indexPageConfig[0], 'toolbar', _toolbar)

    if (!this.isAdd) {
      this.getEditData()
    }
  },
  methods: {
    serchText(e) {
      e.updateData(this.dimensionData.filter((f) => f?.dimensionName.indexOf(e.text) > -1))
    },
    selectOrg(e) {
      const { itemData } = e
      // 组织需要保存组织名称 和组织编码
      this.listForm.orgId = itemData.id
      this.listForm.orgName = itemData.orgName
    },
    queryOrgTree() {
      // 组织下拉列表 - orgFields
      this.$API.performanceManage.getListOrgForIndexSpecial().then((res) => {
        console.log(res)
        if (res.code == 200) {
          this.departmentData = res.data
          if (this.isAdd && this.departmentData.length === 1) {
            this.listForm.orgId = this.departmentData[0]['id']
            this.listForm.orgCode = this.departmentData[0]['orgCode']
            this.listForm.orgName = this.departmentData[0]['orgName']
          }
        }
      })
    },
    numberChange(item, type) {
      if (String(item[type]).length > 16) {
        this.$set(item, type, String(item[type]).slice(0, 16))
      }
      let arr = String(item[type]).split('.')
      if (arr.length == 2 && arr[1].length > 2) {
        this.$set(item, type, String(item[type]).slice(0, arr[0].length + 3))
      }
    },
    arr2Obj(data) {
      const obj = {}
      data.map((item) => {
        obj[item.value] = item.text
      })
      return obj
    },

    handleback() {
      this.$router.go(-1)
    },
    handleSave() {
      console.log(111)
      this.$refs.listForm.validate((val) => {
        if (!val) return
        if (this.listForm.indexItemDTOList?.length <= 0) {
          this.$toast({
            content: this.$t('请填写指标列表'),
            type: 'warning'
          })
          return
        }
        const postData = { ...this.listForm }
        const indexList = [...postData.indexItemDTOList]
        if (postData.indexType === 1) {
          delete postData.indexItemDTOList
          delete postData.maxDeductScore
          delete postData.originScore
          delete postData.dataSource
          delete postData.scoreDimension
          delete postData.indexLineType
          delete postData.additional
          postData.indexRelationDTOList = indexList.map((item) => {
            return {
              indexName: item.indexName,
              indexId: item.indexId
            }
          })
        } else {
          postData.indexItemDTOList = indexList.map((item) => {
            console.log('itemitem', item)
            return {
              maxNumberScore: item.maxNumberScore,
              maxPercentScore: item.maxPercentScore,
              minNumberScore: item.minNumberScore,
              minPercentScore: item.minPercentScore,
              score: item.score,
              description: item.description,
              scoreWeight: item.scoreWeight
            }
          })
        }
        if (val) {
          const obj = this.postId ? { id: this.postId } : {}
          this.$API.performanceManage[this.postId ? 'updateIndexList' : 'addIndexList']({
            ...postData,
            ...obj
          }).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.postId = res.data
              // this.$router.go(-1)
            }
          })
        }
      })
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length < 1 && (e.toolbar.id == 'Del' || e.toolbar.id == 'Edit')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAddIndex()
      } else if (e.toolbar.id == 'Edit') {
        this.handleEditIndex(_selectGridRecords[0])
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelIndex(_selectGridRecords)
      }
    },
    // 添加指标
    handleAddIndex() {
      this.$dialog({
        modal: () =>
          import(
            `./components/detailDialog/${
              this.listForm.indexType === 2 ? 'index.vue' : 'indexCategory.vue'
            }`
          ),
        data: {
          title: this.listForm.indexType === 2 ? this.$t('新增指标') : this.$t('新增指标类'),
          indexDescribeData: this.indexDescribeData,
          indexLineType: this.listForm.indexLineType, // 指标行类型
          scoreDimension: this.listForm.scoreDimension // 打分维度
        },
        success: (val) => {
          let obj = {
            ...val
          }
          // 设置序号
          let index = 0
          this.listForm.indexItemDTOList.forEach((item) => {
            if (item.lineNo > index) {
              index = item.lineNo
            }
          })
          obj.lineNo = index + 1
          this.listForm.indexItemDTOList.push(obj)
          this.setIndexDataSource(this.listForm.indexType, this.listForm.indexItemDTOList)
        }
      })
    },
    // 删除指标
    handleDelIndex(arr) {
      let info = arr.map((item) => {
        return item.lineNo
      })
      let list = this.listForm.indexItemDTOList
      for (let i = 0; i < list.length; i++) {
        if (info.indexOf(list[i].lineNo) != -1) {
          list.splice(i, 1)
          i--
        }
      }
    },
    // 编辑指标
    handleEditIndex(val) {
      this.$dialog({
        modal: () =>
          import(
            `./components/detailDialog/${
              this.listForm.indexType === 2 ? 'index.vue' : 'indexCategory.vue'
            }`
          ),
        data: {
          title: this.listForm.indexType === 2 ? this.$t('编辑指标') : this.$t('编辑指标类'),
          info: val,
          indexDescribeData: this.indexDescribeData,
          indexLineType: this.listForm.indexLineType, // 指标行类型
          scoreDimension: this.listForm.scoreDimension // 打分维度
        },
        success: (val) => {
          let obj = {
            ...val
          }
          let index = -1
          this.listForm.indexItemDTOList.forEach((e, i) => {
            if (e.lineNo == val.lineNo) {
              index = i
            }
          })
          this.listForm.indexItemDTOList.splice(index, 1, obj)
        }
      })
    },
    // 获取详细数据
    getEditData() {
      this.$API.performanceManage.getIndexDetail({ id: this.$route.query.id }).then((res) => {
        if (res.code === 200) {
          const _res = res.data
          const _indexList = _res.header.indexType === 1 ? _res.indexs : _res.indexItems
          this.cur_indexList = _res.header.indexType === 1 ? _res.indexs : _res.indexItems
          this.listForm = Object.assign({}, this.listForm, _res.header)
          _indexList.map((item, index) => {
            item.lineNo = index + 1
            item.indexId = _res.header.indexType === 1 ? item.id : item.indexId // 手动添加id
          })
          //指标和指标类返回的字段不同
          this.$set(this.listForm, 'indexItemDTOList', _indexList)
          //手动设置
          this.setIndexDataSource(_res.header.indexType, _indexList)
          //设置状态
          this.status = _res.header.status
          this.postId = _res.header.id
        }
      })
    },
    // 手动设置列表数据源
    setIndexDataSource(v, data) {
      if (v === 2) {
        this.$set(this.indexPageConfig[0].grid, 'dataSource', data)
      } else if (v === 1) {
        this.$set(this.indexCategoryPageConfig[0].grid, 'dataSource', data)
      }
    },
    // 获取指标维度列表
    getIndexDimension() {
      this.$API.performanceManage
        .getIndexDimension({ page: { current: 1, size: 20 } })
        .then((res) => {
          if (res.code === 200) {
            this.dimensionData = res.data
          }
        })
    },
    // 获取指标说明
    getIndexDescription(v) {
      this.$API.performanceManage
        .getIndexDescription({
          dimensionId: v
        })
        .then((res) => {
          if (res.code === 200) {
            this.indexDescribeData = res.data
          }
        })
    },
    // 指标维度切换
    dimensionChange(v) {
      this.getIndexDescription(v.value)
    },
    indexTypeChange(v) {
      if (v.e) {
        this.listForm.indexItemDTOList = []
        this.setIndexDataSource(v.value, this.listForm.indexItemDTOList)
        this.listForm.indexItemType = v.value
      }
    },
    indexLineTypeChange(v) {
      if (v.e) {
        this.listForm.indexItemDTOList = []
        this.setIndexDataSource(this.listForm.indexType, this.listForm.indexItemDTOList)
      }
    },
    scoreDimensionChange(args) {
      const { value } = args
      // value ? (this.flag = 1) : (this.flag = 0)
      if (value === 1) {
        this.listForm.achievementAssessScoreType = '2'
      } else {
        this.listForm.achievementAssessScoreType = '1'
      }
    },
    rowCellEdit(list, type) {
      this.cur_indexList[list.index][type] = list[type]
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .mt-data-grid {
  .e-grid {
    .e-gridcontent {
      min-height: 100px;
    }
  }
}
.full-width {
  width: 100% !important;
}
/deep/ {
  .mt-select-index {
    float: left;
  }
  .mt-input-number-active .input--wrap::before {
    bottom: -2px;
  }
  .mt-template-page {
    /deep/ .repeat-template {
      padding: 20px;
    }
  }
  .mt-input-number input {
    height: 30px;
    width: 100%;
    border: unset;
    border-bottom: 1px solid rgba(0, 0, 0, 0.42);
    outline: unset !important;
    background: transparent;
    margin-bottom: 4px;
  }
  .mt-input-number input:disabled {
    background: #fafafa;
    background-image: none;
    background-position: initial;
    background-repeat: no-repeat;
    background-size: 0;
    border-color: rgba(0, 0, 0, 0.06);
    color: rgba(0, 0, 0, 0.38);
  }
  .mt-form-item {
    width: calc(20% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-bottom: 20px;
    .label {
      margin-bottom: 6px;
    }
  }
}

.index-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .main-container {
    flex: 1;
    background: #fff;
    padding: 0 20px;

    .operate-bars {
      border-radius: 8px 8px 0 0;
      .operate-bar {
        height: 60px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        color: #4f5b6d;
        .op-item {
          cursor: pointer;
          align-items: center;
          margin-right: 20px;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          color: #00469c;
        }
      }
    }

    .index-list {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
    }
  }
}
</style>
