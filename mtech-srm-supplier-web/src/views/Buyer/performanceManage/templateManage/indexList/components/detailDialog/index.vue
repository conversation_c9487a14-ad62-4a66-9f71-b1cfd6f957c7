<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item :label="$t('指标说明')" prop="description">
          <mt-input
            type="text"
            v-model="formObject.description"
            :show-clear-button="false"
            :max-length="200"
            float-label-type="Never"
            :placeholder="$t('请输入指标说明')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('系数')" prop="coefficient">
          <mt-inputNumber
            v-model="formObject.coefficient"
            :min="0"
            :precision="2"
            :placeholder="$t('请输入系数')"
            class="number-item"
            @change="numberChange(formObject, 'coefficient')"
          ></mt-inputNumber>
        </mt-form-item> -->
        <mt-form-item
          v-if="modalData.indexLineType === 3"
          :label="$t('评分值>=%')"
          prop="minPercentScore"
        >
          <mt-input
            v-model="formObject.minPercentScore"
            :min="0"
            :precision="2"
            type="number"
            :placeholder="$t('请输入评分值')"
            class="number-item"
            @change="numberChange(formObject, 'minPercentScore')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="modalData.indexLineType === 3"
          :label="$t('评分值<%')"
          prop="maxPercentScore"
        >
          <mt-input
            v-model="formObject.maxPercentScore"
            :min="0"
            :precision="2"
            type="number"
            :placeholder="$t('请输入评分值')"
            class="number-item"
            @change="numberChange(formObject, 'maxPercentScore')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="modalData.indexLineType === 2"
          :label="$t('评分值>=')"
          prop="minNumberScore"
        >
          <mt-input
            v-model="formObject.minNumberScore"
            :min="0"
            :precision="2"
            type="number"
            :placeholder="$t('请输入评分值')"
            class="number-item"
            @change="numberChange(formObject, 'minNumberScore')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="modalData.indexLineType === 2"
          :label="$t('评分值<')"
          prop="maxNumberScore"
        >
          <mt-input
            v-model="formObject.maxNumberScore"
            :min="formObject.minNumberScore"
            :precision="2"
            type="number"
            :placeholder="$t('请输入评分值')"
            class="number-item"
            @change="numberChange(formObject, 'minNumberScore')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('得分')" prop="score">
          <!-- <mt-inputNumber
            v-model="formObject.score"
            :precision="2"
            :placeholder="$t('请输入得分')"
            class="number-item"
            @change="numberChange(formObject, 'score')"
          ></mt-inputNumber> -->
          <mt-input
            v-model="formObject.score"
            type="number"
            :placeholder="$t('请输入得分')"
            class="number-item"
            @change="numberChange(formObject, 'score')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="
            (modalData.indexLineType === 2 || modalData.indexLineType === 3) &&
            modalData.scoreDimension === 1
          "
          :label="$t('是否按权重计算得分')"
          prop="scoreWeight"
        >
          <mt-select
            v-model="formObject.scoreWeight"
            :data-source="scoreDimensionOptions"
            :placeholder="$t('请选择')"
            @change="scoreWeightChange(formObject, 'scoreWeight')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { i18n } from '@/main.js'

export default {
  data() {
    return {
      scoreDimensionOptions: [
        // 是否按权重计算得分
        { text: i18n.t('是'), value: true },
        { text: i18n.t('否'), value: false }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      formObject: {
        description: '', //指标说明
        coefficient: '', //系数
        minNumberScore: '', //评分值  >
        maxNumberScore: '', //评分值  <
        minPercentScore: '', //评分值  < %
        maxPercentScore: '', //评分值  < %
        score: '', //得分
        scoreWeight: '' // 是否按权重计算得分
      },
      defaultFormRules: {
        description: [{ required: true, message: this.$t('请输入指标说明'), trigger: 'blur' }],
        coefficient: [{ required: true, message: this.$t('请输入系数'), trigger: 'blur' }],
        score: [{ required: true, message: this.$t('得分'), trigger: 'blur' }]
      },
      scoreFormRules: {
        // minNumberScore: [{ required: true, message: this.$t('请输入评分值'), trigger: 'blur' }],
        // maxNumberScore: [{ required: true, message: this.$t('请输入评分值'), trigger: 'blur' }]
      },
      editStatus: false,
      siteArr: [],
      selectedMaterialItem: {}
    }
  },
  watch: {},
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    formRules() {
      return this.modalData.indexLineType === 3
        ? { ...this.defaultFormRules }
        : { ...this.defaultFormRules, ...this.scoreFormRules }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.title == this.$t('编辑指标')) {
      this.formObject = JSON.parse(JSON.stringify(this.modalData.info))
    }
  },
  methods: {
    numberChange(item, type) {
      if (String(item[type]).length > 16) {
        this.$set(item, type, String(item[type]).slice(0, 16))
      }
      let arr = String(item[type]).split('.')
      if (arr.length == 2 && arr[1].length > 2) {
        this.$set(item, type, String(item[type]).slice(0, arr[0].length + 3))
      }
    },
    // 弹框确认
    confirm() {
      this.$refs.dialogRef.validate((val) => {
        if (val) {
          // 如果是百分比 校验评分值范围必填其一
          if (this.modalData.indexLineType === 3 && !this.validPerData()) {
            return
          }
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    // 校验百分比弹框数据
    validPerData() {
      if (!this.formObject.minPercentScore && !this.formObject.maxPercentScore) {
        this.$toast({
          content: this.$t('请填写评分值'),
          type: 'warning'
        })
        return false
      }
      if (
        this.formObject.maxPercentScore &&
        this.formObject.maxPercentScore < this.formObject.minPercentScore
      ) {
        this.$toast({
          content: this.$t('评分值<%不能小于评分制>=%'),
          type: 'warning'
        })
        return false
      }
      return true
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 18px;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  .mt-form {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 46%;
    }
  }
  .table-content {
    flex: 1;
  }
  /deep/ {
    .mt-input-number-active .input--wrap::before {
      bottom: -2px;
    }
  }
}
</style>
