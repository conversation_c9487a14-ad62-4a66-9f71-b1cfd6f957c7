<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item :label="$t('指标说明')" prop="indexId">
          <mt-select
            v-model="formObject.indexId"
            float-label-type="Never"
            :data-source="modalData.indexDescribeData || []"
            :fields="{ text: 'indexName', value: 'id' }"
            :placeholder="$t('请选择指标说明')"
            @change="indexChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="formObject.status"
            float-label-type="Never"
            :data-source="statusData"
            :placeholder="$t('请选择状态')"
            :readonly="true"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      formObject: {
        indexId: '', //指标说明id
        indexName: '', //指标说明
        status: 2 //状态 默认有效
      },
      formRules: {
        indexId: [{ required: true, message: this.$t('请输入指标说明'), trigger: 'blur' }]
      },
      editStatus: false,
      siteArr: [],
      selectedMaterialItem: {},
      statusData: [{ text: this.$t('有效'), value: 2 }]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.title == this.$t('编辑指标类')) {
      this.formObject = JSON.parse(JSON.stringify(this.modalData.info))
    }
  },
  methods: {
    numberChange(item, type) {
      if (String(item[type]).length > 16) {
        this.$set(item, type, String(item[type]).slice(0, 16))
      }
      let arr = String(item[type]).split('.')
      if (arr.length == 2 && arr[1].length > 2) {
        this.$set(item, type, String(item[type]).slice(0, arr[0].length + 3))
      }
    },
    indexChange(v) {
      this.formObject.indexName = v.itemData.indexName
    },
    // 弹框确认
    confirm() {
      this.$refs.dialogRef.validate((val) => {
        if (val) {
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 18px;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  .mt-form {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 46%;
    }
  }
  .table-content {
    flex: 1;
  }
}
</style>
