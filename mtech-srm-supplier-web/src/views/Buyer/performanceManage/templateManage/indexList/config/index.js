//维度设置Tab
import { i18n } from '@/main.js'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder ',
    title: i18n.t('新增')
  },
  {
    id: 'Disable',
    icon: 'icon_table_enable ',
    title: i18n.t('失效')
  },
  {
    id: 'Enable',
    icon: 'icon_table_disable ',
    title: i18n.t('生效')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete ',
    title: i18n.t('删除')
  },
  {
    id: 'IndexExport',
    icon: 'icon_solid_pushorder ',
    title: i18n.t('导出')
  },
  {
    id: 'IndexImport',
    icon: 'icon_solid_pushorder ',
    title: i18n.t('导入')
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'orgName',
    headerText: i18n.t('事业部')
  },
  {
    field: 'dimensionName',
    headerText: i18n.t('指标维度'),
    cssClass: 'field-content',
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] === 1 || data['status'] === 3
        }
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] === 1
        }
      }
    ]
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标') + '/' + i18n.t('指标名称'),
    cssClass: ''
  },
  {
    field: 'indexType',
    headerText: i18n.t('指标类型'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('指标类'), 2: i18n.t('指标') }
    }
  },
  {
    field: 'description',
    headerText: i18n.t('评分逻辑描述'),
    cssClass: ''
  },
  {
    field: 'effectiveDate',
    headerText: i18n.t('生效日期')
  },
  {
    field: 'invalidDate',
    headerText: i18n.t('失效日期')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: 150,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 1,
          label: i18n.t('拟定'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('有效'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 3,
          label: i18n.t('失效'),
          cssClass: ['status-label', 'status-enable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Enable',
        icon: 'icon_list_enable',
        title: i18n.t('生效'),
        visibleCondition: (data) => {
          return data['status'] === 1 || data['status'] === 3
        }
      },
      {
        id: 'Disable',
        icon: 'icon_list_disable',
        title: i18n.t('失效'),
        visibleCondition: (data) => {
          return data['status'] === 2
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    }
  }
]

export const pageConfig = [
  {
    gridId: 'db60d5d1-8280-41b8-9fa2-0e8b552e8faf',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url: '/analysis/tenant/buyer/assess/index/pageQuery'
      }
    }
  }
]
