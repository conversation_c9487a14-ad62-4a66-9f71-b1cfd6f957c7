import { i18n } from '@/main.js'

export const toolbar = {
  tools: [
    [
      { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
      { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
      { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') }
    ],
    []
  ]
}
export const scoreDimensionOptions = [
  // 是否按权重计算得分
  { text: i18n.t('是'), value: true },
  { text: i18n.t('否'), value: false }
]

// index list column
export const defaultIndexColumnData = [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false
  },
  {
    field: 'lineNo',
    headerText: i18n.t('序号'),
    allowEditing: false
  },
  {
    field: 'description',
    headerText: i18n.t('指标说明'),
    allowEditing: false
  },
  // {
  //   field: 'coefficient',
  //   headerText: i18n.t('系数'),
  // },
  {
    field: 'score',
    headerText: i18n.t('得分'),
    allowEditing: false
  }
]
export const perIndexColumnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'lineNo',
    headerText: i18n.t('序号')
  },
  {
    field: 'description',
    headerText: i18n.t('指标说明')
  },
  // {
  //   field: 'coefficient',
  //   headerText: i18n.t('系数'),
  // },
  {
    field: 'minPercentScore',
    headerText: i18n.t('评分值') + '>=%'
  },
  {
    field: 'maxPercentScore',
    headerText: i18n.t('评分值') + '<%'
  },
  {
    field: 'score',
    headerText: i18n.t('得分')
  },
  {
    field: 'scoreWeight',
    headerText: i18n.t('是否按权重计算得分'),
    valueConverter: {
      type: 'map',
      map: {
        true: i18n.t('是'),
        false: i18n.t('否'),
        '': i18n.t('否')
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component('scoreWeight', {
    //       template: `<div>
    //       <mt-select ref="input" :id="data.id + Date.parse(new Date())" :disabled="enable" :dataSource="dataSource"
    //         v-model="data.scoreWeight" @change="dataChange"></mt-select>
    //       </div>`,
    //       data() {
    //         return {
    //           i18n: i18n,
    //           dataSource: [
    //             { text: i18n.t('是'), value: true },
    //             { text: i18n.t('否'), value: false }
    //           ]
    //         }
    //       },
    //       methods: {
    //         dataChange(e) {
    //           this.data.scoreWeight = e.itemData.value
    //           this.$parent.$emit('cellEdit', this.data, 'scoreWeight')
    //         }
    //       }
    //     })
    //   }
    // }
  }
]
export const numIndexColumnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'lineNo',
    headerText: i18n.t('序号')
  },
  {
    field: 'description',
    headerText: i18n.t('指标说明')
  },
  // {
  //   field: 'coefficient',
  //   headerText: i18n.t('系数'),
  // },
  {
    field: 'minNumberScore',
    headerText: i18n.t('评分值') + '>='
  },
  {
    field: 'maxNumberScore',
    headerText: i18n.t('评分值') + '<'
  },
  {
    field: 'score',
    headerText: i18n.t('得分')
  },
  {
    field: 'scoreWeight',
    headerText: i18n.t('是否按权重计算得分'),
    valueConverter: {
      type: 'map',
      map: {
        true: i18n.t('是'),
        false: i18n.t('否'),
        '': i18n.t('否')
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component('scoreWeight', {
    //       template: `<div>
    //       <mt-select ref="input" :id="data.id + Date.parse(new Date())" :disabled="enable" :dataSource="dataSource"
    //         v-model="data.scoreWeight" @change="dataChange"></mt-select>
    //       </div>`,
    //       data() {
    //         return {
    //           i18n: i18n,
    //           dataSource: [
    //             { text: i18n.t('是'), value: true },
    //             { text: i18n.t('否'), value: false }
    //           ]
    //         }
    //       },
    //       methods: {
    //         dataChange(e) {
    //           this.data.scoreWeight = e.itemData.value
    //           this.$parent.$emit('cellEdit', this.data, 'scoreWeight')
    //         }
    //       }
    //     })
    //   }
    // }
  }
]

// index category list column
export const indexCategoryColumnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'lineNo',
    headerText: i18n.t('序号')
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标说明')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('拟定'),
        2: i18n.t('有效'),
        3: i18n.t('失效')
      }
    }
  }
]

// index list pageConfig
export const indexPageConfig = [
  {
    toolbar: toolbar,
    useToolTemplate: false,
    useBaseConfig: false,
    // isUseCustomEditor: true,
    grid: {
      // editSettings: {
      //   allowEditing: true,
      //   allowAdding: true,
      //   allowDeleting: true,
      //   mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
      //   showConfirmDialog: false,
      //   showDeleteConfirmDialog: false,
      //   newRowPosition: 'Top'
      // },
      height: 'auto',
      columnData: defaultIndexColumnData,
      dataSource: []
    }
  }
]

// index category list pageConfig
export const indexCategoryPageConfig = [
  {
    toolbar: toolbar,
    useToolTemplate: false,
    useBaseConfig: false,
    grid: {
      height: 'auto',
      columnData: indexCategoryColumnData,
      dataSource: []
    }
  }
]
