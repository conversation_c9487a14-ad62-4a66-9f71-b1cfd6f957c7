import { i18n } from '@/main.js'
// list column
const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'orgTypeName',
    headerText: i18n.t('组织层级')
  },
  {
    field: 'orgCode',
    headerText: i18n.t('组织编码')
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织名称')
  },
  {
    field: 'parentOrgName',
    headerText: i18n.t('父组织名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'effectiveDate',
    headerText: i18n.t('生效日期')
  },
  {
    field: 'invalidDate',
    headerText: i18n.t('失效日期')
  }
]

export const pageConfig = [
  {
    gridId: 'eaa9b100-6f47-4589-9401-f0ef5660d6e7',
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: [
      { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
      { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
      { id: 'Enable', icon: 'icon_table_disable', title: i18n.t('失效') },
      { id: 'ListExport', icon: 'icon_solid_pushorder', title: i18n.t('导出') }
    ],
    grid: {
      columnData: columnData,
      asyncConfig: {
        url: '/masterDataManagement/analysisVirtualOrg/queryList'
      }
    }
  }
]
