<template>
  <div class="structure-setting">
    <div class="setting" slot="slot-0">
      <div class="setting-content">
        <div class="setting-left">
          <div class="setting-top">
            <mt-common-tree
              id="MtCommonTree"
              :fields="fileds"
              :allow-editing="false"
              :un-button="true"
            ></mt-common-tree>
          </div>
        </div>
        <div class="setting-right">
          <mt-template-page
            ref="templateRef"
            :hidden-tabs="false"
            :padding-top="true"
            :template-config="pageConfig"
            @handleClickToolBar="handleClickToolBar"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      :dialog-type="dialogType"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>
<script>
import { pageConfig } from './config'
import utils from '@/utils/utils'
import MtCommonTree from '@mtech/common-tree-view'
import '@mtech/common-tree-view/build/esm/bundle.css'

export default {
  components: {
    addDialog: require('./components/addDialog.vue').default,
    MtCommonTree
  },
  data() {
    return {
      pageConfig: pageConfig,
      addDialogShow: false,
      dialogData: null,
      dialogType: 'add',
      fileds: {
        dataSource: [],
        id: 'id',
        parentID: 'parentOrgId',
        text: 'catalogueName',
        child: 'childrenList'
      },
      modalData: {
        title: '',
        isEdit: false,
        fileInfo: null,
        applyInfo: null
      }
    }
  },
  computed: {},
  created() {
    this.init()
  },
  methods: {
    init() {
      // 获取架构数据
      this.$API.performanceManage.getStructureTreeList().then((res) => {
        const _resData = this.handleTreeData(res.data)
        this.$set(this.fileds, 'dataSource', _resData)
      })
    },
    // 点击表格工具栏
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      // Add Edit ListExport
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id === 'Edit') {
        if (_selectRows.length <= 0 || _selectRows.length > 1) {
          this.$toast({ content: this.$t('请选择一行'), type: 'warning' })
          return
        }
        const _invalidDate = new Date(_selectRows[0].invalidDate).getTime()
        const _now = new Date(utils.formateTime()).getTime()
        if (_now > _invalidDate) {
          this.$toast({ content: this.$t('该组织已经失效，不能编辑'), type: 'warning' })
          return
        }
        this.handleEdit(_selectRows[0])
      } else if (e.toolbar.id === 'Enable') {
        if (_selectRows.length <= 0 || _selectRows.length > 1) {
          this.$toast({ content: this.$t('请选择一行'), type: 'warning' })
          return
        }
        this.handleEnable(_selectRows[0])
      } else if (e.toolbar.id === 'ListExport') {
        this.handleListExport()
      }
    },
    // 添加弹框操作
    handleAdd() {
      this.addDialogShow = true
      this.dialogType = 'add'
    },
    // 编辑弹框操作
    handleEdit(row) {
      this.addDialogShow = true
      this.dialogType = 'edit'
      this.dialogData = row
    },
    // 失效操作
    handleEnable(records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认更新状态为') + this.$t('失效') + '？'
        },
        success: () => {
          this.$API.performanceManage.updateStructureStatus({ id: records.id }).then((res) => {
            if (res.code === 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    },
    // get AddDialogShow
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },
    // confirmSuccess back function
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
      this.init()
    },
    // tree 数据添加name
    handleTreeData(data) {
      data.forEach((ele) => {
        ele['name'] = ele.orgName
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.handleTreeData(ele.childrenList)
        }
      })
      return data
    },
    handleListExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 20 },
        rules: rule.rules || []
      }
      this.$API.performanceManage.exportOrgList(params).then((res) => {
        const fileName = utils.getHeadersFileName(res)
        utils.download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import 'components/css/bundle.css';
/deep/ .mt-pagertemplate .mt-select-index {
  float: left;
}
.structure-setting {
  height: 100%;
  .setting {
    height: 100%;
    .setting-content {
      width: 100%;
      height: 100%;
      display: flex;
      .setting-left {
        width: 30%;
        height: calc(100% - 20px);
        background: rgba(255, 255, 255, 1);
        margin-top: 20px;
        margin-right: 16px;
        padding-top: 16px;
        .setting-top {
          width: 90%;
          height: 40px;
          line-height: 40px;
          margin: auto;
        }
        .setting-left {
          margin-top: 10px;
          width: 100%;
          height: calc(100% - 50px);
          border: 1px solid rgba(232, 232, 232, 1);
          border-radius: 6px;
          font-size: 14px;
          color: #4d5b6f;
          .left-title {
            height: 50px;
            line-height: 50px;
            padding-left: 20px;
          }
        }
      }

      .setting-right {
        width: 70%;
      }
    }
  }
}
</style>
