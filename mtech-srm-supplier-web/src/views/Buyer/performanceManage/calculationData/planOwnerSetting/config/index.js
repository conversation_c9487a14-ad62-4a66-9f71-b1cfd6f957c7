// 绩效计算范围
import { i18n } from '@/main.js'
// import { dropRight } from 'lodash'
// import Vue from 'vue'
// import { API } from '@mtech-common/http'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  {
    id: 'EXimport',
    icon: 'icon_solid_upload',
    title: i18n.t('导入')
  },
  {
    id: 'EXdownload',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete ',
    title: i18n.t('删除')
  }
]

const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织')
  },
  {
    width: 100,
    field: 'supplierCode',
    headerText: i18n.t('供应商代码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    width: 150,
    field: 'employeeName',
    headerText: i18n.t('考评人')
  },
  {
    field: 'templateName',
    headerText: i18n.t('绩效模板')
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    }
  },
  {
    width: 75,
    freeze: 'Right',
    field: 'operation',
    headerText: i18n.t('操作'),
    ignore: true, //忽略
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑')
        // visibleCondition: (data) => {
        //   return data['status'] === 'DRAFT' || data['status'] === 'REJECT'
        // }
      }
    ]
  }
]

export const pageConfig = (url) => [
  {
    gridId: '94ddef9b-beb9-483e-9722-c94402ae379a',
    toolbar,
    useToolTemplate: false,
    grid: {
      //关闭查询
      // allowFiltering: false,
      allowSorting: false,
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
