<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('组织：')">
          <!-- <mt-select
            v-model="formObject.orgId"
            :placeholder="$t('请选择组织：')"
            :fields="{
              text: 'orgName',
              value: 'id'
            }"
            :data-source="orgArrList"
            @change="selectOrg"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          ></mt-select> -->

          <!-- :enabled="editType !== 'edit'"
          @change="selectOrganization" -->
          <!-- :disabled="editType === 'edit'" -->
          <mt-DropDownTree
            v-if="orgFields.dataSource.length > 0"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择组织：')"
            :popup-height="500"
            :fields="orgFields"
            :allow-filtering="true"
            filter-type="Contains"
            @change="selectOrg"
            id="baseTreeSelect"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择组织：')" :data-source="[]"></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierId" :label="$t('供应商名称：')">
          <mt-select
            v-model="formObject.supplierId"
            :data-source="planeArrList"
            :fields="{
              text: 'supplierName',
              value: 'id'
            }"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            @change="changePlanName"
            :placeholder="$t('请选择供应商名称：')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商代码：')">
          <mt-input
            v-model="formObject.supplierCode"
            disabled
            :placeholder="$t('请选择供应商代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="categoryId" class="form-item positive" :label="$t('品类名称')">
          <!-- <mt-select
            :allow-filtering="true"
            v-model="formObject.categoryId"
            :data-source="categoryListArrList"
            :fields="{
              text: 'categoryName',
              value: 'id'
            }"
            :show-clear-button="true"
            @change="selectCategoryas"
            :placeholder="$t('请选择品类名称:')"
          ></mt-select> -->
          <mt-select
            v-model="formObject.categoryId"
            :allow-filtering="true"
            float-label-type="Never"
            filter-type="Contains"
            :data-source="categoryListArrList"
            :filter-bar-placeholder="$t('请输入品类名称或编码进行搜索')"
            :fields="{ text: 'textAndValue', value: 'id' }"
            @change="selectCategoryas"
            :filtering="filteringList"
            :placeholder="$t('请选择品类名称:')"
            :no-records-template="noCategoryasRecordsTemplate"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="uid" :label="$t('采购负责人')">
          <mt-select
            v-model="formObject.uid"
            id="dropDownTreeCom"
            style="width: 100%"
            :fields="{ text: 'nameAndPhone', value: 'uid' }"
            :data-source="employeeListArrList"
            filter-type="Contains"
            :allow-filtering="true"
            :filtering="getBuyerList"
            :placeholder="$t('请输入采购负责人')"
            :filter-bar-placeholder="$t('请输入用户名称进行搜索')"
            @select="selectBuyer"
            :no-records-template="noRecordsTemplate"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="templateId" :label="$t('绩效模板')">
          <mt-select
            :allow-filtering="true"
            :data-source="templateListArrList"
            :show-clear-button="true"
            filter-type="Contains"
            @change="selectTemplate"
            :placeholder="$t('请输入绩效模板')"
            v-model="formObject.templateId"
            :fields="{
              text: 'templateName',
              value: 'id'
            }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="pageType === 'add'" prop="indexIdList" :label="$t('指标名称')">
          <mt-multi-select
            :allow-filtering="true"
            v-model="formObject.indexIdList"
            :data-source="indexListArrList"
            :fields="{
              text: 'indexName',
              value: 'id'
            }"
            :show-clear-button="true"
            @change="selectIndex"
            :placeholder="$t('指标名称')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item v-else prop="indexId" :label="$t('指标名称')">
          <mt-select
            :allow-filtering="true"
            v-model="formObject.indexId"
            filter-type="Contains"
            :data-source="indexListArrList"
            :fields="{
              text: 'indexName',
              value: 'id'
            }"
            :show-clear-button="true"
            :placeholder="$t('指标名称')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils.js'
import { throttle, cloneDeep } from 'lodash'
// import dayjs from 'dayjs'
export default {
  data() {
    return {
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      // 表单验证
      rules: {
        // orgId: [
        //   {
        //     required: true,
        //     message: this.$t('请选择组织'),
        //     trigger: 'blur'
        //   }
        // ],
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商代码'),
            trigger: 'blur'
          }
        ],
        supplierId: [
          {
            required: true,
            message: this.$t('请选择供应商名称'),
            trigger: 'blur'
          }
        ],
        categoryId: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        uid: [
          {
            required: true,
            message: this.$t('请输入采购负责人'),
            trigger: 'blur'
          }
        ],
        templateId: [
          {
            required: true,
            message: this.$t('请选择绩效模板'),
            trigger: 'blur'
          }
        ],
        indexIdList: [
          {
            required: true,
            message: this.$t('请选择指标名称'),
            trigger: 'blur'
          }
        ],
        indexId: [
          {
            required: true,
            message: this.$t('请选择指标名称'),
            trigger: 'blur'
          }
        ]
      },
      // 弹窗确认事件
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      categoryListArrList: [],
      orgArrList: [],
      indexListArrList: [],
      templateListArrList: [],
      employeeListArrList: [], //采购负责人下拉数组
      planeArrList: [], // 供应商下拉数组
      //表单数据
      formObject: {
        orgIdArr: [],
        orgCode: '', //组织机构编码
        orgId: '', //组织机构id
        orgName: '', //组织机构名称
        supplierCode: '', //供应商编码
        supplierId: '', //供应商id
        supplierName: '', //供应商名称
        categoryId: '', //品类
        categoryCode: '',
        uid: '', // 采购计划负责人
        templateId: '', //绩效模板
        templateCode: '',
        indexIdList: [],
        id: ''
      },
      orgObj: {},
      isAllowEdit: true,
      pageType: 'add',
      noRecordsTemplate: this.$t('请输入用户名称进行搜索'),
      noCategoryasRecordsTemplate: this.$t('请输入品类名称或编码进行搜索')
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getBuyerList = utils.debounce(this.getBuyerList, 300)
    this.getOrgList() //获取组织树 - 下拉数据
    this.getSupplierList()
    this.$refs['dialog'].ejsRef.show() //显示弹窗
    // 编辑模式
    if (this.modalData && this.modalData.data) {
      this.pageType = 'edit'
      this.isAllowEdit = false
      this.getTemplateList({
        orgId: this.modalData.data.orgId
      })
      this.filteringList({ text: this.modalData.data.categoryCode })
      this.getIndexList(this.modalData.data.templateId)
      this.formObject = { ...this.modalData.data, uid: this.modalData.data.employeeId } //编辑数据回显
      this.getBuyerList({
        text: this.modalData.data.employeeName
      }) //采购负责人
      setTimeout(() => {
        this.isAllowEdit = true
      }, 1000)
    }
  },
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$nextTick(() => {
          this.$refs.dialogRef.validateField('orgIdArr')
        })
      }
    }
  },
  methods: {
    filteringList: throttle(function (e) {
      let { text } = e
      if (text) {
        let params = {
          page: {
            current: 1,
            size: 9999
          }
        }
        const rules = []
        const columnData = ['categoryCode', 'categoryName']
        for (let i = 0; i < columnData.length; i++) {
          const field = columnData[i]
          let obj = {
            field,
            label: '',
            operator: field.includes('Code') ? 'equal' : 'contains',
            type: 'string',
            value: text
          }
          rules.push(obj)
        }
        if (rules.length) {
          params.condition = 'or'
          params.rules = rules
        }
        this.$API.performanceManage.queryPermissionCategories(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.categoryListArrList = data.records.map((i) => {
              return {
                ...i,
                textAndValue: `${i.categoryCode}-${i.categoryName}`
              }
            })
            this.noRecordsTemplate = this.$t('没有找到记录')
          }
        })
      }
    }, 300),
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          // this.orgArrList = res.data
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgId = ele.id
          this.formObject.orgName = ele.name
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    //选择组织
    selectOrg(e) {
      // const { itemData } = e
      // if (itemData && itemData) {
      //   this.formObject.orgCode = itemData.orgCode // 组织编码
      //   this.formObject.orgId = itemData.id // 组织id
      //   this.formObject.orgName = itemData.orgName // 组织名称
      // }
      const { value } = e
      // this.planeArrList = []
      // this.getSupplierList({
      //   orgCode: itemData.orgCode,
      //   orgId: itemData.id,
      //   orgName: itemData.orgName,
      //   orgType: itemData.orgType
      // })
      this.matchItem(this.orgFields.dataSource, e['value'][0])

      this.getTemplateList({
        orgId: value[0]
      })
      if (this.isAllowEdit) {
        // 清空供应商选中和品类选中
        // this.formObject.supplierCode = '' //供应商编码
        // this.formObject.supplierId = '' //供应商id
        // this.formObject.supplierName = '' //供应商名称
        this.formObject.templateId = '' //绩效模板
        this.formObject.uid = '' // 采购计划负责人
        this.formObject.indexIdList = [] //指标
        this.formObject.indexId = '' //指标
      }
    },
    // 获取供应商列表
    getSupplierList() {
      let pamrsform = {
        // organizationCode: form.orgCode,
        // organizationId: form.orgId,
        // organizationName: form.orgName,
        // organizationType: form.orgType
        // organizationParentId: form.parentOrgId
      }
      // this.orgObj = {
      //   orgId: form.orgId,
      //   // parentOrgId: form.parentOrgId,
      //   orgType: form.orgType,
      //   orgCode: form.orgCode
      // }
      this.$API.supplierExcept.getFuzzy(pamrsform).then((result) => {
        if (result.code == 200) {
          this.planeArrList = result.data
          // this.getCategoryList(result.data)
        }
      })
    },
    // 获取品类列表
    getCategoryList(data) {
      this.$API.supplierExcept
        .getCategory({
          supplierCode: data.supplierCode, //供应商编码
          supplierId: data.id, //供应商id
          supplierName: data.supplierName, //供应商名称
          supplierTenantId: data.supplierTenantId, //供应商租户id
          ...this.orgObj //组织信息
        })
        .then((result) => {
          this.categoryListArrList = result.data
        })
    },
    //获取采购负责人列表
    getBuyerList(val) {
      // let params = { orgLevelCode: 'ORG05', orgType: 'ORG001ADM' }
      // if (this.formObject.employeeName) {
      //   params.fuzzyName = this.formObject.employeeName
      // }
      // this.$API.performanceManage.getBuyerList(params).then((result) => {
      //   this.employeeListArrList = result.data
      // })
      let params = {
        fuzzyName: val.text,
        // organizationId: '1485965381538336769' // this.formObject.orgId
        orgLevelCode: 'ORG05',
        orgType: 'ORG001ADM'
      }
      this.$API.supplierIndex.currentTenantEmployees(params).then((res) => {
        this.employeeListArrList = []
        if (res.code == 200 && res.data != null) {
          // this.ownerList = res.data.map((item) => {
          //   item.ownerName = item.employeeName + item.phoneNum
          //   return item
          // })
          const records = JSON.parse(JSON.stringify(res.data))
          this.employeeListArrList = records.map((i) => {
            return {
              ...i,
              nameAndPhone: `${i.employeeName}-${i.phoneNum}`
            }
          })
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },
    //获取绩效模板
    getTemplateList(para) {
      let params = {
        page: { current: 1, size: 9999 }
      }
      params = {
        ...params,
        ...para
      }
      this.$API.performanceManage.templateListQuery(params).then((result) => {
        this.templateListArrList = result.data
      })
    },
    // 选择绩效模板
    selectTemplate(e) {
      //清空供应商选中和品类选中
      let { itemData } = e
      if (itemData) {
        this.formObject.templateId = itemData.id //模板id
        this.formObject.templateCode = itemData.templateCode
        this.$nextTick(() => {
          this.getIndexList(itemData.id) //请求s=指标的下拉数据
        })
      } else {
        if (this.isAllowEdit) {
          this.formObject.templateId = '' //模板id
          this.formObject.templateCode = ''
          this.formObject.indexIdList = [] //指标
          this.formObject.indexId = '' //指标
        }
      }
    },

    // 选择采购负责人
    selectBuyer(e) {
      let { itemData } = e
      if (itemData) {
        this.formObject.uid = itemData.uid
        this.formObject.employeeId = itemData.employeeId //模板id
        this.formObject.employeeEmail = itemData.email
        this.formObject.employeeExternalCode = itemData.employeeCode
        this.formObject.employeeName = itemData.employeeName
      } else {
        if (this.isAllowEdit) {
          this.formObject.indexIdList = null //清空指标的选中
          this.formObject.indexId = '' //指标
        }
      }
    },
    // 选择指标
    selectIndex(e) {
      if (e) {
        this.formObject.indexIdList = e.value
      } else {
        this.formObject.indexIdList = [] //清空指标的选中
        this.formObject.indexId = '' //指标
      }
    },
    //获取指标
    getIndexList(para) {
      this.$API.performanceManage.getSupTemplateList(para).then((result) => {
        this.indexListArrList = result.data
      })
    },
    //选择供应商名称
    changePlanName(e) {
      const { itemData } = e
      if (itemData) {
        this.formObject.supplierCode = itemData.supplierCode //供应商编码
        this.formObject.supplierId = itemData.id //供应商id
        this.formObject.supplierName = itemData.supplierName //供应商名称
        // this.getCategoryList(itemData) //请求品类的下拉数据
      } else {
        if (this.isAllowEdit) {
          this.formObject.supplierCode = '' //供应商编码
          this.formObject.supplierId = '' //供应商id
          this.formObject.supplierName = '' //供应商名称
          // this.formObject.categoryId = '' //清空 品类的选中
          // this.formObject.categoryCode = ''
          // this.formObject.categoryName = ''
        }
      }
    },
    // 选择品类
    selectCategoryas(e) {
      this.formObject.categoryCode = e.itemData.categoryCode
      this.formObject.categoryId = e.itemData.id
      this.formObject.categoryName = e.itemData.categoryName
    },
    // 确认按钮 - 新增
    confirm() {
      //表单校验 后 再请求接口校验状态
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          const params = cloneDeep(this.formObject)
          delete params.orgIdArr

          // params.employeeId = params.uid
          if (this.pageType === 'edit') {
            params.indexIdList = [params.indexId]
            this.employeeListArrList.forEach((i) => {
              if (params.employeeId === i.uid) {
                params.employeeId = i.employeeId
              }
            })
          }
          this.$API.performanceManage.addBuyerList(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('新增成功'), type: 'success' })
              this.$emit('confirm-function') //关闭弹窗
            } else {
              this.$toast({ content: res.msg, type: 'warning' })
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
}
</style>
