<template>
  <!-- 采购计划负责人设置 -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { getHeadersFileName, download } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceManage.buyerListQuery)
      // pageConfig: pageConfig(this.$API.supplierCalculationRange.pageQuery)
    }
  },
  mounted() {},
  methods: {
    //导出
    EXdownload() {
      //获取template-page组件查询的参数
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 9999 },
        rules: rule.rules || []
      }
      //调用接口
      this.$API.performanceManage.exBuyerList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        //下载文件
        download({
          fileName,
          blob: res.data
        })
      })
    },
    //行点击事件
    handleClickCellTool(e) {
      const { data } = e
      this.dialogShow(data)
    },
    //工具栏点击事件
    handleClickToolBar(item) {
      //获取选中的数据
      let _selectGridRecords = this.$refs.templateRef
        .getCurrentTabRef()
        .gridRef.getMtechGridRecords()
      //获取按钮id
      let name = item.toolbar ? item.toolbar.id : item.tool.id
      //删除或编辑，判断是否有选中数据
      if (name == 'Add') {
        //新增
        this.dialogShow()
      } else if (name == 'EXimport') {
        // 导入
        this.importData()
      } else if (name == 'EXdownload') {
        //导出
        this.EXdownload()
      } else if (name == 'Delete') {
        // 删除
        //提示弹框
        if (_selectGridRecords.length === 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }

        this.rowDelete(_selectGridRecords)
      }
    },
    //Excel导入 - 绩效计算范围
    importData() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('上传/导入'),
          paramsKey: 'file',
          importApi: this.$API.performanceManage.imBuyerList,
          downloadTemplateApi: this.$API.performanceManage.getImBuyerList
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 弹窗
    dialogShow(row) {
      let data = {
        title: (row ? this.$t('编辑') : this.$t('新增')) + this.$t('采购计划负责人')
      }
      if (row) {
        data.data = row
      }
      this.$dialog({
        modal: () => import('./components/targetDialog.vue'),
        data,
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //行 删除
    rowDelete(val) {
      //判断val数组里hide的值 1是手动创建 且创建人=系统当前登录账号时，可删除 ，0不可删除
      // if (val.hide == 0) {
      //   this.$toast({ content: this.$t('您不是当前单据的创建人，不能删除'), type: 'warning' })
      //   return
      // }
      const idsList = []
      val.forEach((ele) => {
        idsList.push(ele.id)
      })

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.sumbitDelete(idsList)
        }
      })
    },
    //请求删除接口
    sumbitDelete(form) {
      this.$API.performanceManage.delBuyerList(form).then((res) => {
        if (res.code == 200) {
          //删除成功后刷新表格
          this.$toast({
            content: this.$t('删除成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData() //刷新列表
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
/deep/ .e-grid .e-frozencontent.e-frozen-right-content > .e-table {
  border-left: none !important;
}
/deep/ .e-grid .e-frozenheader.e-frozen-right-header > .e-table {
  border-left: none !important;
}
</style>
