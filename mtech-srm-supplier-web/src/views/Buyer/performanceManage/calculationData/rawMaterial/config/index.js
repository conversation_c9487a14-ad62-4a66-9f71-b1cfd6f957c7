import { i18n } from '@/main.js'
import utils from '@/utils/utils.js'

// list column
const columnData = [
  {
    field: 'siteCode',
    width: '180',
    headerText: '工厂',
    formatter: (column, row) => {
      return row.siteCode ? row.siteCode + '-' + row.siteName : ''
    },
    searchOptions: {
      elementType: 'remote-autocomplete',
      renameField: 'siteCode',
      fields: { text: 'siteName', value: 'siteCode' },
      params: {},
      searchFields: ['siteName', 'siteCode'],
      operator: 'in',
      multiple: true,
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`
    }
  },
  {
    field: 'supplierCode',
    width: '150',
    headerText: i18n.t('供应商代码')
  },
  {
    field: 'supplierName',
    width: '150',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'itemCode',
    width: '150',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    width: '150',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    width: '150',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'quantity',
    width: '150',
    headerText: i18n.t('收货数量')
  },
  {
    field: 'stockSite',
    width: '150',
    headerText: i18n.t('仓库')
  },
  {
    field: 'receiveDate',
    width: '150',
    headerText: i18n.t('收货月份'),
    type: 'dateTime',
    format: 'yyyy-MM',
    searchOptions: {
      clearButton: true,
      elementType: 'date-month',
      dateFormat: 'YYYY-mm',
      serializeValue: (e) => {
        return utils.formateTime(e, 'yyyy-MM')
      }
    }
  }
]

// list pageConfig
export const pageConfig = [
  {
    gridId: '36a04cac-4847-4ae7-ae1b-85e8ed40db78',
    toolbar: [],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      asyncConfig: {
        url: '/analysis/tenant/buyer/assess/rawMaterialInfo/pageQuery',
        params: {},
        ignoreDefaultSearch: true,
        ignoreSearchValid: true
      }
    }
  }
]
