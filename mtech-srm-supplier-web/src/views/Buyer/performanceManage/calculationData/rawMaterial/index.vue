<template>
  <div class="raw-material">
    <mt-template-page ref="templateRef" :template-config="pageConfig" />
  </div>
</template>
<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  methods: {}
}
</script>
<style lang="scss">
.raw-material {
  height: 100%;
  .mt-select-index {
    float: left;
  }
  // .e-content {
  //   height: calc(100vh - 230px) !important;
  // }
}
</style>
