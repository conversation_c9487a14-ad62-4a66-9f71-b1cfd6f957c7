<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="taskTypeId" :label="$t('任务名称')">
        <mt-select
          v-model="addForm.taskTypeId"
          :data-source="taskTypeData"
          :fields="{ text: 'itemName', value: 'id' }"
          :show-clear-button="true"
          :placeholder="$t('请选择任务名称')"
          @change="taskTypeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="orgIdList" :label="$t('组织')">
        <mt-DropDownTree
          :popup-height="500"
          :fields="orgFields"
          v-model="addForm.orgIdList"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择组织')"
          :filter-bar-placeholder="$t('请输入关键字')"
          :show-check-box="true"
          id="orgTreeSelectID"
        ></mt-DropDownTree>
      </mt-form-item>
      <mt-form-item v-if="isShowSupplier" prop="supplierCodeList" :label="$t('供应商')">
        <mt-multi-select
          v-model="addForm.supplierCodeList"
          :data-source="supplierData"
          :show-clear-button="true"
          :allow-filtering="true"
          :filtering="getSupplierList"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :enable-group-check-box="true"
          mode="CheckBox"
          :placeholder="$t('请选择供应商')"
        ></mt-multi-select>
      </mt-form-item>
      <mt-form-item prop="templateType" :label="$t('模板类型')" v-if="isShowTemplate">
        <mt-select
          v-model="addForm.templateType"
          :data-source="templateTypeData"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :show-clear-button="true"
          :placeholder="$t('请选择模板类型')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="evaluationCycle" :label="$t('评价周期')">
        <mt-select
          v-model="addForm.evaluationCycle"
          :data-source="evaluationCycleData"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :show-clear-button="true"
          :placeholder="$t('请选择评价周期')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="achievementsStartMonth" :label="$t('绩效月份起')">
        <mt-date-picker
          v-model="addForm.achievementsStartMonth"
          format="yyyy-MM"
          start="Year"
          depth="Year"
          :disabled="isEdit"
          :show-clear-button="true"
          :allow-edit="false"
          @change="dateStartChange"
          :placeholder="$t('选择绩效月份起')"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="achievementsEndMonth" :label="$t('绩效月份止')">
        <mt-date-picker
          v-model="addForm.achievementsEndMonth"
          format="yyyy-MM"
          start="Year"
          depth="Year"
          :disabled="true"
          :show-clear-button="true"
          :allow-edit="false"
          @change="dateEndChange"
          :placeholder="$t('选择绩效月份止')"
        ></mt-date-picker>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import { flattenDeep } from 'lodash'
import utils from '@/utils/utils'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: this.$t('任务新增'),
      orgFields: null,
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      taskTypeCode: '',
      addForm: {
        taskTypeId: '',
        orgIdList: [],
        supplierCodeList: [],
        templateType: '',
        evaluationCycle: '',
        achievementsStartMonth: '',
        achievementsEndMonth: ''
      },
      rules: {
        taskTypeId: [
          {
            required: true,
            message: this.$t('请选择任务名称'),
            trigger: 'blur'
          }
        ],
        orgIdList: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        templateType: [
          {
            required: true,
            message: this.$t('请选择模板类型'),
            trigger: 'blur'
          }
        ],
        evaluationCycle: [
          {
            required: true,
            message: this.$t('请选择评价周期'),
            trigger: 'blur'
          }
        ],
        achievementsStartMonth: [
          {
            required: true,
            message: this.$t('请选择绩效月份起'),
            trigger: 'blur'
          }
        ],
        achievementsEndMonth: [
          {
            required: true,
            message: this.$t(''),
            trigger: 'blur'
          }
        ]
      },
      taskTypeData: [],
      orgData: [],
      supplierData: [],
      templateTypeData: [],
      evaluationCycleData: [],
      isEdit: false
    }
  },
  computed: {
    isShowTemplate() {
      return (
        this.taskTypeCode === 'JSDF' ||
        this.taskTypeCode === 'DDFMX' ||
        this.taskTypeCode === 'ZHJXJS'
      )
    },
    isShowSupplier() {
      return this.taskTypeCode !== 'JSFW'
    },
    taskList() {
      const { id, itemCode, itemName } = this.taskTypeData?.find(
        (item) => item.id === this.addForm.taskTypeId
      )

      return {
        taskTypeCode: itemCode,
        taskTypeId: id,
        taskTypeName: itemName
      }
    },
    orgList() {
      // 处理提交的orgList数据
      const _map = []
      const _orgData = this.flatTreeData(this.orgData)
      _orgData?.map((item) => {
        if (this.addForm.orgIdList?.includes(item.id)) {
          const { orgCode, id, orgLevel, orgName, orgType } = item
          _map.push({
            orgCode: orgCode,
            orgId: id,
            orgLevel: orgLevel,
            orgName: orgName,
            orgType: orgType
          })
        }
      })
      return _map
    },
    supplierList() {
      // 处理提交的orgList数据
      const _map = []
      this.supplierData?.map((item) => {
        if (this.addForm.supplierCodeList?.includes(item.supplierCode)) {
          const { supplierCode, id, supplierName } = item
          _map.push({
            supplierCode: supplierCode || '',
            supplierId: id || '',
            supplierName: supplierName || ''
          })
        }
      })
      return _map
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    this.getSelectDictList()
    this.getOrgList()
    this.getSupplierList = utils.debounce(this.getSupplierList, 300)
  },
  watch: {
    'addForm.orgIdList': {
      handler() {
        this.$nextTick(() => {
          this.$refs.ruleForm.validateField('orgIdList')
        })
      }
    },
    'addForm.evaluationCycle': {
      handler() {
        if (!this.addForm.achievementsStartMonth) return
        this.addForm.achievementsEndMonth = this.ctrlDateTime(
          this.addForm.achievementsStartMonth,
          'dateStart'
        )
      }
    },
    'addForm.orgIdArr': {
      handler() {
        this.$nextTick(() => {
          this.$refs.dialogRef.validateField('orgIdArr')
        })
      }
    }
  },
  methods: {
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'childrenList') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    // 弹框 - 开启
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    // 弹框 - 关闭
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    // 弹框 - 确认
    handleConfirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return
        let month = new Date(this.addForm.achievementsStartMonth).getMonth() + 1
        let evaluationCycle = this.addForm.evaluationCycle
        if (evaluationCycle === 'YEAR' && month != 1) {
          this.$toast({
            content: this.$t('评价周期为年度时，绩效起始月份只可选择1月'),
            type: 'warning'
          })
          return
        } else if (evaluationCycle === 'HALF-YEAR' && month != 1 && month != 7) {
          this.$toast({
            content: this.$t('评价周期为半年度时，绩效起始月份只可选择1月或7月'),
            type: 'warning'
          })
          return
        } else if (
          evaluationCycle === 'SEASON' &&
          month != 1 &&
          month != 4 &&
          month != 7 &&
          month != 10
        ) {
          this.$toast({
            content: this.$t('评价周期为季度时，绩效起始月份只可选择1月、4月、7月或10月'),
            type: 'warning'
          })
          return
        }
        const _addForm = {}
        for (var i in this.addForm) {
          if (i !== 'taskTypeId') {
            if (i === 'orgIdList') {
              _addForm['orgList'] = this.orgList
            } else if (i === 'supplierCodeList') {
              _addForm['supplierList'] = this.supplierList
            } else {
              _addForm[i] = this.addForm[i]
            }
          }
        }

        const postData = {
          ...this.taskList,
          parameter: {
            ..._addForm,
            achievementsEndMonth: this.addForm.achievementsEndMonth
              ? utils.formateTime(new Date(this.addForm.achievementsEndMonth), 'yyyy-MM') + '-01'
              : '',
            achievementsStartMonth: this.addForm.achievementsStartMonth
              ? utils.formateTime(new Date(this.addForm.achievementsStartMonth), 'yyyy-MM') + '-01'
              : ''
          }
        }
        this.$API.performanceManage
          .saveTask(postData)
          .then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
          .catch(() => {
            this.$emit('handleAddDialogShow', false)
          })
      })
    },
    // 任务名称切换
    taskTypeChange(v) {
      if (v.e) {
        this.taskTypeCode = v.itemData?.itemCode
      }
    },
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.orgData = res.data
          this.orgFields = {
            dataSource: res.data,
            value: 'id',
            text: 'orgName',
            child: 'childrenList'
          }
        }
      })
    },

    // 获取任务名称
    async getSelectDictList() {
      this.taskTypeData = await this.getDictItems('TASK')
      this.templateTypeData = await this.getDictItems('MB-TYPE')
      this.evaluationCycleData = await this.getDictItems('EVALUATION-PERIOD')
      // this.addForm.taskTypeId = this.taskTypeData[0]?.id
    },
    dateStartChange(date) {
      this.addForm.achievementsEndMonth = this.ctrlDateTime(date, 'dateStart')
    },
    dateEndChange() {
      // this.addForm.startDate = this.ctrlDateTime(date, 'dateEnd')
    },
    ctrlDateTime(date, type) {
      const _period = this.addForm.evaluationCycle
      const _date = utils.formateTime(date, 'yyyy-MM')
      let _time = ''

      switch (_period) {
        case 'MONTH':
          _time = this.calculateDate(_date, 0, type)
          break
        case 'YEAR':
          _time = this.calculateDate(_date, 11, type)
          break
        case 'HALF-YEAR':
          _time = this.calculateDate(_date, 5, type)
          break
        case 'SEASON':
          _time = this.calculateDate(_date, 2, type)
          break
        default:
          _time = this.calculateDate(_date, 0, type)
      }
      return _time
    },
    calculateDate(date, num, type) {
      let arr = date.split('-')
      let year = parseInt(arr[0])
      let month = parseInt(arr[1])
      month = type === 'dateStart' ? month + num : month - num
      if (month > 12) {
        let yearNum = parseInt((month - 1) / 12)
        month = month % 12 == 0 ? 12 : month % 12
        year += yearNum
      } else if (month <= 0) {
        month = Math.abs(month)
        let yearNum = parseInt((month + 12) / 12)
        let n = month % 12
        if (n == 0) {
          year -= yearNum
          month = 12
        } else {
          year -= yearNum
          month = Math.abs(12 - n)
        }
      }
      month = month < 10 ? '0' + month : month
      return year + '-' + month
    },

    // 根据名称获取字典数据
    getDictItems(key) {
      return this.$API.supplierInvitation
        .getDictCode({
          dictCode: key,
          nameLike: ''
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            return res.data
          }
        })
    },
    // 获取供应商列表数据
    async getSupplierList(e) {
      this.$API.performanceManage.getSupplierList({ fuzzyNameOrCode: e.text }).then((res) => {
        this.supplierData = res.data
      })
    }
  }
}
</script>

<style>
.create-proj-dialog .e-dlg-content {
  padding-top: 20px !important;
}
</style>
