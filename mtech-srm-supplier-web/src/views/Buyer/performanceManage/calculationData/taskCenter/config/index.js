import { i18n } from '@/main.js'
// list toolbar
export const toolbar = [{ id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') }]
// list column
const columnData = [
  {
    field: 'taskCode',
    headerText: i18n.t('任务ID')
  },
  {
    field: 'taskName',
    headerText: i18n.t('任务名称')
  },
  {
    field: 'displayParameter',
    headerText: i18n.t('参数')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('进行中'), cssClass: '' },
        { value: 1, text: i18n.t('已完成'), cssClass: '' },
        { value: 2, text: i18n.t('失败'), cssClass: '' }
      ]
    }
  },
  {
    field: 'responseContent',
    headerText: i18n.t('返回消息')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
// list pageConfig
export const pageConfig = [
  {
    toolbar: toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      height: 'auto',
      columnData,
      asyncConfig: {
        url: 'platform/tenant/task/queryBuilder',
        params: {}
      }
    }
  }
]
