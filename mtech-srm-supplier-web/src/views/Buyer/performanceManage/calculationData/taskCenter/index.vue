<template>
  <div class="task-center">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>

    <add-dialog
      class="task-dialog"
      v-if="addDialogShow"
      :dialog-data="dialogData"
      :type="this.type"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import { pageConfig } from './config'
import addDialog from './components/addDialog.vue'
export default {
  components: {
    addDialog: addDialog
  },
  data() {
    return {
      pageConfig: pageConfig,
      type: '0',
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      if (e.gridRef.getMtechGridRecords().length <= 0 && !(e.toolbar.id == 'Add')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = [],
        _status = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)
        _status.push(item.status)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      }
    },

    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    }
  }
}
</script>
<style lang="scss">
.task-center {
  // height: 100%;
  .mt-select-index {
    float: left;
  }
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
