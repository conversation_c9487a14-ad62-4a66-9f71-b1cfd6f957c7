<template>
  <!-- 模板清单 -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleQuickSearch="handleQuickSearch"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
  </div>
</template>

<script>
import { rowDataTemp } from './config/variable'
import { pageConfig } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [],
      templateList: [], // 绩效模板下拉枚举
      fields: {
        // 品类下拉
        dataSource: []
      },
      planeArrList: [], // 供应商下拉
      indexList: [] // 指标下拉
    }
  },
  created() {},
  mounted() {
    this.getSelectList()
  },
  methods: {
    handleQuickSearch() {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.clearAll()
    },
    getSelectList() {
      const templateList = this.getTemplateList()
      // const indexList = this.getIndexList()
      Promise.all([templateList]).then((result) => {
        this.templateList = result[0]
        this.indexList = result[1]
        this.pageConfig = pageConfig(this)
        // this.planeArrList = result[1].map((i) => {
        //   return {
        //     ...i,
        //     codeAndName: `${i.supplierCode}-${i.supplierName}`
        //   }
        // })
        // this.$set(
        //   this.fields,
        //   'dataSource',
        //   result[1].map((i) => {
        //     return { ...i, codeAndName: `${i.categoryCode} - ${i.categoryName}` }
        //   })
        // )
      })
    },
    // 获取模板下拉 枚举
    getTemplateList() {
      return new Promise((resolve) => {
        this.$API.performanceManage
          .getPermissionTemplateList({
            templateName: ''
          })
          .then((res) => {
            resolve(res.data)
          })
          .catch(() => {
            resolve([])
          })
      })
      // this.$API.performanceManage.getPermissionTemplateList().then((res) => {
      //   this.templateList = res.data
      //   this.pageConfig = pageConfig(this)
      // })
    },
    // 获取模板下拉 枚举
    getIndexList() {
      return new Promise((resolve) => {
        this.$API.performanceManage
          .getIndexList()
          .then((res) => {
            resolve(res.data)
          })
          .catch(() => {
            resolve([])
          })
      })
      // this.$API.performanceManage.getPermissionTemplateList().then((res) => {
      //   this.templateList = res.data
      //   this.pageConfig = pageConfig(this)
      // })
    },
    // 获取品类下拉 枚举
    getCategorys(val) {
      return new Promise((resolve) => {
        this.$API.supplierInvitation
          .getCategoryListAll({
            condition: 'and',
            page: {
              current: 1,
              size: 10000
            },
            pageFlag: false,
            rules: [
              {
                field: 'tree_level',
                operator: 'equal',
                type: 'int',
                value: 1
              },
              {
                condition: 'and',
                rules: [
                  {
                    field: 'categoryCode',
                    type: 'string',
                    operator: 'contains',
                    value: val
                  },
                  {
                    condition: 'or',
                    field: 'categoryName',
                    type: 'string',
                    operator: 'contains',
                    value: val
                  }
                ]
              }
            ]
          })
          .then((res) => {
            resolve(res.data.records)
          })
          .catch(() => {
            resolve([])
          })
      })
    },
    // 获取供应商下拉枚举
    getSupplierList() {
      return new Promise((resolve) => {
        this.$API.supplierExcept
          .getFuzzy()
          .then((res) => {
            resolve(res.data)
          })
          .catch(() => {
            resolve([])
          })
      })
    },
    // 工具栏点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      // if (_selectGridRecords.length <= 0 && e.toolbar.id == 'Publish') {
      //   //提示弹框
      //   this.$toast({ content: this.$t('请选择需要发布的数据'), type: 'warning' })
      //   return
      // }
      // if (
      //   _selectGridRecords.length <= 0 &&
      //   (e.toolbar.id == 'Submit' || e.toolbar.id == 'Recall')
      // ) {
      //   //提示弹框
      //   this.$toast({ content: this.$t('请选择需要操作的数据'), type: 'warning' })
      //   return
      // }
      if (e.toolbar.id === 'Publish') {
        // 发布任务
        this.handlePublish(_selectGridRecords)
      } else if (e.toolbar.id === 'IndexImport') {
        this.handleImport()
      } else if (e.toolbar.id === 'IndexExport') {
        // 导出
        this.handleExport()
      } else if (e.toolbar.id == 'Cancel') {
        e.grid.closeEdit()
      } else if (e.toolbar.id == 'Submit') {
        // 提交
        this.handleSubmit(_selectGridRecords)
      } else if (e.toolbar.id == 'Recall') {
        // 撤回
        this.handleRecall(_selectGridRecords)
      }
      //  else if (e.toolbar.id == 'selectAll') {
      //   // 全选
      //   this.handleSelectAllSubmit()
      // }
    },
    // 提交
    handleSubmit(_selectGridRecords) {
      if (_selectGridRecords.length === 0) this.submitRecordAll()
      if (_selectGridRecords.length > 0) {
        const ids = []
        for (let i = 0; i < _selectGridRecords.length; i++) {
          const item = _selectGridRecords[i]
          if (!item.result && item.result !== 0) {
            this.$toast({
              content: this.$t('勾选的指标得分数据不能为空'),
              type: 'warning'
            })
            return
          }
          if (item.submitStatus == 1) {
            this.$toast({
              content: this.$t('存在已提交的评分，不可重复提交'),
              type: 'warning'
            })
            return
          }
          ids.push(item.id)
        }
        this.$API.performanceManage.submitScoring(ids).then((res) => {
          if (res.code === 200) {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          }
        })
      }
    },
    // 全部提交
    submitRecordAll() {
      const currentViewData =
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.ej2Instances.currentViewData
      if (currentViewData.length === 0)
        return this.$toast({ content: this.$t('暂无数据可提交'), type: 'warning' })
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('请确认是否全部提交')
        },
        success: () => {
          this.$API.performanceManage
            .submitScoringAll({
              rules: rule.rules || []
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('全部提交成功'), type: 'success' })
                this.$refs.templateRef.refreshCurrentGridData()
              }
              this.getList()
            })
        }
      })
    },
    // 撤回
    handleRecall(_selectGridRecords) {
      if (_selectGridRecords.length === 0) this.revokeScoreDetailsFnAll()
      if (_selectGridRecords.length > 0) {
        const ids = []
        for (let i = 0; i < _selectGridRecords.length; i++) {
          const item = _selectGridRecords[i]
          if (![1, 4].includes(item.approveStatus)) {
            this.$toast({
              content: this.$t('勾选的指标对应绩效得分不为【未审批、审批驳回】，不可撤回'),
              type: 'warning'
            })
            return
          }
          if (item.submitStatus == 0) {
            this.$toast({
              content: this.$t('存在未提交的评分，不可撤回'),
              type: 'warning'
            })
            return
          }
          ids.push(item.id)
        }
        this.$API.performanceManage.recallScoring(ids).then((res) => {
          if (res.code === 200) {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          }
        })
      }
    },
    // 全部撤回
    revokeScoreDetailsFnAll() {
      const currentViewData =
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.ej2Instances.currentViewData
      if (currentViewData.length === 0)
        return this.$toast({ content: this.$t('暂无数据可撤回'), type: 'warning' })
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('请确认是否全部撤回')
        },
        success: () => {
          this.$API.performanceManage
            .recallScoringAll({
              rules: rule.rules || []
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('全部撤回成功'), type: 'success' })
                this.$refs.templateRef.refreshCurrentGridData()
              }
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    },
    // 发布任务
    handlePublish(records) {
      if (records.length === 0) this.handleSelectAllSubmit()
      if (records.length > 0) {
        let _ids = records.map((v) => {
          return v.id
        })
        this.$API.performanceManage.publishScoring(_ids).then((res) => {
          if (res.code === 200) {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          }
        })
      }
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 20 },
        rules: rule.rules || []
      }
      this.$API.performanceManage.exportScoringList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 导入
    handleImport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let downloadTemplateParams = null
      if (rule && rule.rules && rule.rules.length) {
        downloadTemplateParams = {
          condition: rule.condition || '',
          page: { current: 1, size: 20 },
          rules: rule.rules || []
        }
      }
      this.$dialog({
        modal: () => import('@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('上传/导入'),
          paramsKey: 'excel',
          importApi: this.$API.performanceManage.importScoringList,
          downloadTemplateApi: this.$API.performanceManage.exportScoringList,
          // asyncParams,
          downloadTemplateParams
        },
        success: () => {
          // 修复导入接口异步导致表格数据未实时刷新
          setTimeout(() => {
            this.$refs.templateRef.refreshCurrentGridData()
          }, 600)
          // this.$nextTick(() => {
          //   this.$refs.templateRef.refreshCurrentGridData()
          // })
        }
      })
    },
    // 全选发布
    handleSelectAllSubmit() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        rules: rule.rules || []
      }
      this.$API.performanceManage.getSelectAllSubmit(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    saveRow(e) {
      this.$API.performanceManage
        .saveScoring(e.data)
        .then(() => {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        })
        .catch((e) => {
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({
            content: e?.errorLabels[0]?.message,
            type: 'error'
          })
        })
    },
    actionBegin(args) {
      const { requestType, rowData } = args
      if (requestType === 'save') {
        // 提交前合并自定义组件数据到数据源
        args.data = rowData
        // console.log('rowDatarowDatarowData', args)
      } else if (requestType === 'beginEdit') {
        // 单据状态处于未提交状态（0）时才能进入编辑
        if (rowData.submitStatus == 1) {
          args.cancel = true
        }
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    actionComplete(e) {
      const { requestType, rowIndex } = e
      // 判断逻辑太多，加一下注释；此处操作类型为保存
      if (requestType === 'save') {
        if (
          e.data.achievementAssessScoreType === 1 &&
          !/^([+-]?)([0-9]|[1-9]\d+)(\.\d{1,2})?$/.test(e.data.result)
        ) {
          this.$toast({
            content: this.$t('得分请输入正确的数字！'),
            type: 'error'
          })
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          return
        }
        if (e.data.achievementAssessScoreType === 1 && !e.data.result) {
          this.$toast({
            content: this.$t('请选择得分！'),
            type: 'error'
          })
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          return
        }
        if (
          e.data.achievementAssessScoreType === 2 &&
          e.data.indexLineType !== 1 &&
          !/^([+-]?)([0-9]|[1-9]\d+)(\.\d{1,2})?$/.test(e.data.score)
        ) {
          this.$toast({
            content: this.$t('评分值请输入正确的数字！！'),
            type: 'error'
          })
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          return
        }
        // if (e.data.remark && !/^.{0,200}$/.test(e.data.remark)) {
        //   this.$toast({
        //     content: '备注不能超过200个字符！',
        //     type: 'error'
        //   })
        //   this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
        //   this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        //   return
        // }
        // 审批状态为未审批的
        if (!e.data.approveStatus || [1, 4].includes(e.data.approveStatus)) {
          // 手动录入分数，根据加/扣分上限是否有值，如果没有值，则得分的值不控制，如果有值，则根据正负数控制得分列的值大小，如下，
          // 如果加/扣分上限的值为正数，则得分列的值必须小于等于加/扣分上限的值，大于等于0
          // 如果加/扣分上限的值为负数，则得分列的值必须大于等于原始分数-[加/扣分上限]绝对值，小于等于原始分数
          // 如果录入的分数不在此区间，则报错并保存不成功，报错信息：得分值不在可填写的区间范围之内，请确认修改！
          if (
            // 指标的打分类型为“评分”，指标行类型为“文本” 不校验
            !(e.data.achievementAssessScoreType == 2 && e.data.indexLineType == 1) &&
            e.data.maxDeductScore &&
            ((e.data.maxDeductScore > 0 &&
              (Number(e.data.result) > e.data.maxDeductScore || Number(e.data.result) < 0)) ||
              (e.data.maxDeductScore < 0 &&
                (Number(e.data.result) < e.data.originScore - Math.abs(e.data.maxDeductScore) ||
                  e.data.result > e.data.originScore)))
          ) {
            this.$toast({
              content: this.$t('保存失败，得分值不在可填写的区间范围之内，请确认修改！'),
              type: 'error'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            // 评价周期为月度才执行一下逻辑
            if (e.data.evaluationCycle === 'MONTH') {
              const params = {
                categoryCode: e.data.categoryCode,
                evaluationCycle: e.data.evaluationCycle,
                indexRowId: e.data.indexRowId,
                orgCode: e.data.orgCode,
                supplierCode: e.data.supplierCode
              }
              // 同一打分维度计分高于或低于历史值1.3倍的，提示确认后才保存
              this.$API.performanceManage.getScoreRange(params).then((res) => {
                const { max, min } = res.data
                if ((!!max && e.data.result > max) || (!!min && e.data.result < min)) {
                  this.$dialog({
                    data: {
                      title: this.$t('提示'),
                      message: this.$t('超出历史6个月最高/最低值的1.3倍，是否确认录入？')
                    },
                    success: () => {
                      this.saveRow(e)
                    },
                    failed: () => {
                      this.$refs.templateRef.refreshCurrentGridData()
                    }
                  })
                } else {
                  this.saveRow(e)
                }
              })
            } else {
              this.saveRow(e)
            }
          }
        } else {
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({
            content: this.$t('保存失败，非未审批状态的数据不可提交'),
            type: 'error'
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
/deep/ {
  .grid-container {
    overflow-y: scroll !important;
  }
  .mt-select-index {
    float: left;
  }
  .e-rowcell .score-standard-cell {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
  }
}
</style>
