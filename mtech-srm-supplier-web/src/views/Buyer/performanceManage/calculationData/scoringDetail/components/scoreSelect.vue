<template>
  <div>
    <mt-select
      v-if="data.indexLineType === 1 && data.achievementAssessScoreType === 2"
      v-model="data[data.column.field]"
      :disabled="data.achievementAssessScoreType === 1"
      :data-source="dataSource"
      popup-width="450px"
      :placeholder="scorePlaceholder"
      :fields="{ text: 'description', value: 'description' }"
      @change="change"
    ></mt-select>
    <mt-inputNumber
      v-else
      :placeholder="scorePlaceholder"
      :disabled="data.achievementAssessScoreType === 1"
      v-model="data[data.column.field]"
      @input="numberInput"
    ></mt-inputNumber>
  </div>
</template>
<script>
/**
 * 打分类型(achievementAssessScoreType) 2 = 评分（得分值不可编辑）， 1 = 得分 （评分值不可编辑）
 * 指标行类型(indexLineType) 1 = 文本（下拉）， 2 = 数字（数字匹配），  3 = 百分比（数字匹配）
 *
 */
import { rowDataTemp } from '../config/variable.js'
export default {
  data() {
    return {
      data: {},
      dataSource: [],
      scorePlaceholder: this.$t('请输入')
    }
  },

  mounted() {
    this.dataSource = [...this.data['buyerAssessIndexItemDTOList']]
    // console.log('this.datathis.data', this.data.indexLineType)
  },
  destroyed() {
    this.$bus.$off('scoreChange')
  },
  methods: {
    change(e) {
      if (this.data.column.field == 'score') {
        this.$bus.$emit('scoreChange', e?.itemData?.score)
        rowDataTemp[rowDataTemp.length - 1]['score'] = e?.value
        rowDataTemp[rowDataTemp.length - 1]['result'] = e?.itemData?.score
      }
    },
    numberInput(e) {
      // 判断当前输入的值在哪个区间
      let _score = null
      for (var i = 0; i < this.dataSource.length; i++) {
        let _min =
          this.data.indexLineType === 2
            ? this.dataSource[i].minNumberScore
            : this.dataSource[i].minPercentScore
        let _max =
          this.data.indexLineType === 2
            ? this.dataSource[i].maxNumberScore
            : this.dataSource[i].maxPercentScore
        if (
          (!_min && e < _max) ||
          (!_max && e >= _min) ||
          (_min && _max && e >= _min && e < _max)
        ) {
          if (this.dataSource[i].scoreWeight) {
            _score = ((e * this.data?.originScore) / 100).toFixed(2)
          } else {
            _score = this.dataSource[i].score
          }
          break
        }
      }
      console.log('scoreChangescoreChangescoreChange', e, _score, this.data)
      this.$bus.$emit('scoreChange', _score)
      rowDataTemp[rowDataTemp.length - 1]['score'] = e
      rowDataTemp[rowDataTemp.length - 1]['result'] = _score
    }
  }
}
</script>
