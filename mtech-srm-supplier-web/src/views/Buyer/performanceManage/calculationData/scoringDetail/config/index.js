//维度设置Tab
import utils from '@/utils/utils'
import { i18n } from '@/main.js'
import Vue from 'vue'
import scoreSelect from '../components/scoreSelect.vue'
import { rowDataTemp } from './variable'
export const scoreDimensionOptions = [
  // 打分维度
  { text: i18n.t('指标名称'), value: 1 },
  { text: i18n.t('指标说明'), value: 2 }
]
export const statusOptions = [
  // 发布状态
  { text: i18n.t('未发布'), value: 0 },
  { text: i18n.t('已发布'), value: 1 }
]
export const submitStatusOptions = [
  // 提交状态
  { text: i18n.t('未提交'), value: 0 },
  { text: i18n.t('已提交'), value: 1 }
]
export const approveStatusOptions = [
  // 审批状态
  { text: i18n.t('未审批'), value: null },
  { text: i18n.t('未审批'), value: 1 },
  { text: i18n.t('审批中'), value: 2 },
  { text: i18n.t('审批通过'), value: 3 },
  { text: i18n.t('审批驳回'), value: 4 }
]
export const scoreTypeData = [
  // 打分类型
  { text: i18n.t('得分'), value: 1 },
  { text: i18n.t('评分'), value: 2 }
]
export const indexLineTypeOption = [
  // 指标行类型
  { text: i18n.t('文本'), value: 1 },
  { text: i18n.t('数字'), value: 2 },
  { text: i18n.t('百分比'), value: 3 }
]
export const perevaluationOptions = utils.getSupplierDict('EVALUATION-PERIOD') || [] // 评价周期

const toolbar = [
  {
    id: 'Cancel',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消编辑')
  },
  {
    id: 'Publish',
    icon: 'icon_solid_Createorder ',
    title: i18n.t('发布任务'),
    permission: ['O_02_1309']
  },
  {
    id: 'IndexImport',
    icon: 'icon_solid_upload ',
    title: i18n.t('导入')
  },
  {
    id: 'IndexExport',
    icon: 'icon_solid_pushorder ',
    title: i18n.t('导出')
  },
  {
    id: 'Submit',
    icon: 'icon_solid_Createorder',
    title: i18n.t('提交')
  },
  {
    id: 'Recall',
    icon: 'a-icon_solid_revocation',
    title: i18n.t('撤回')
  }
  // {
  //   id: 'selectAll',
  //   icon: 'a-icon_solid_revocation',
  //   title: i18n.t('全部发布')
  // }
]

const columnData = (that) => {
  const column = [
    {
      type: 'checkbox',
      width: 50,
      allowEditing: false
    },
    {
      field: 'achievementsMonth',
      headerText: i18n.t('绩效月份'),
      width: 120,
      searchOptions: {
        clearButton: true,
        type: 'date',
        dateFormat: 'YYYY-mm'
      },
      ignore: true,
      allowEditing: false
    },
    {
      field: 'categoryCode',
      width: 120,
      headerText: i18n.t('品类编码'),
      allowEditing: false,
      ignore: true
    },
    {
      field: 'categoryName',
      width: 120,
      headerText: i18n.t('品类名称'),
      allowEditing: false,
      searchOptions: {
        elementType: 'remote-autocomplete',
        renameField: 'categoryCode',
        fields: { text: 'categoryName', value: 'categoryCode' },
        params: {},
        multiple: true,
        operator: 'in',
        url: '/masterDataManagement/tenant/category/paged-query',
        placeholder: ' ',
        searchFields: ['categoryCode', 'categoryName']
      }
    },
    {
      field: 'supplierCode',
      width: 120,
      headerText: i18n.t('供应商编码'),
      allowEditing: false,
      ignore: true
    },
    {
      field: 'supplierName',
      width: 200,
      headerText: i18n.t('供应商名称'),
      allowTextWrap: false,
      allowEditing: false,
      searchOptions: {
        renameField: 'supplierCode',
        elementType: 'remote-autocomplete',
        fields: { text: 'supplierName', value: 'supplierCode' },
        multiple: true,
        operator: 'in',
        url: '/masterDataManagement/tenant/supplier/paged-query',
        searchFields: ['id', 'supplierCode', 'supplierDescription', 'supplierName']
      }
    },
    {
      field: 'indexName',
      width: 120,
      headerText: i18n.t('指标名称'),
      allowEditing: false,
      // searchOptions: {
      //   elementType: 'remote-autocomplete',
      //   renameField: 'indexId',
      //   fields: { text: 'indexName', value: 'id' },
      //   params: {},
      //   multiple: true,
      //   operator: 'in',
      //   url: '/analysis/tenant/buyer/assess/index/listIndex',
      //   placeholder: ' ',
      //   supplierSearchFields: ['indexName']
      // },
      template: () => {
        return {
          template: Vue.component('indexNameStandard', {
            template: `
              <div class="score-standard-cell">
                {{ data.indexName}}
              </div>`
          })
        }
      },
      editTemplate: function () {
        return {
          template: Vue.component('indexNameOption', {
            template: `<div>
          <mt-input
            disabled
            v-model="data.indexName"
          ></mt-input>
          </div>`,
            data() {}
          })
        }
      }
    },
    {
      field: 'scoreStandard',
      width: 200,
      headerText: i18n.t('评分标准'),
      allowEditing: false,
      clipMode: 'Ellipsis',
      template: () => {
        return {
          template: Vue.component('scoreStandard', {
            template: `
            <span id="spanSelector" v-html="getBrText(data.scoreStandard)" :title="data.scoreStandard"  style="display: block;height: 2.6em;overflow: hidden;line-height: 1.3em" />`,
            mounted() {
              // // const spanElement = document.querySelector('#spanSelector')
              // // if (this.data.scoreStandard && !this.data.scoreStandard.includes('\n')) {
              // //   console.log('scoreStandardscoreStandard')
              // //   spanElement.removeAttribute('title')
              // // }
            },
            methods: {
              getBrText(response) {
                let resText = response.replace(/\n/g, '<br/>')
                return resText
              }
            }
          })
        }
      }
    },
    {
      field: 'originScore',
      width: 120,
      headerText: i18n.t('满分'),
      allowEditing: false
    },
    {
      field: 'achievementAssessScoreType',
      width: 120,
      headerText: i18n.t('打分类型'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: { 1: i18n.t('得分'), 2: i18n.t('评分') }
      },
      template: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.achievementAssessScoreType) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let arr = scoreTypeData.filter((j) => j.value === status)
                return arr[0]?.text
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.achievementAssessScoreType) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = scoreTypeData.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },
    {
      field: 'indexLineType',
      headerText: i18n.t('指标行类型'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('文本'),
          2: i18n.t('数字'),
          3: i18n.t('百分比')
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.indexLineType) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = indexLineTypeOption.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },
    {
      field: 'result',
      width: 120,
      headerText: i18n.t('得分'),
      editTemplate: function () {
        return {
          template: Vue.component('resultInput', {
            // PRIMITIVE_SCORE 2 = 评分(得分不可编辑)  1 SCORE = 得分
            template: `
              <div>
                <mt-input :disabled="data.achievementAssessScoreType === 2" :value="data.result" @input="input" />
              </div>
            `,
            data() {
              return {
                data: {}
              }
            },
            mounted() {
              this.$bus.$on('scoreChange', (e) => {
                this.data.result = e
              })
            },
            methods: {
              input(e) {
                rowDataTemp[rowDataTemp.length - 1]['result'] = e
              }
            }
          })
        }
      }
    },
    {
      field: 'score',
      width: 120,
      headerText: i18n.t('评分值'),
      editTemplate: function () {
        return { template: scoreSelect }
      }
      // editType: 'numericEdit'
    },
    {
      field: 'remark',
      width: 120,
      headerText: i18n.t('备注'),
      editTemplate: function () {
        return {
          template: Vue.component('remark', {
            template: `
              <div>
                <mt-input :value="data.remark" @change="remarkChange" />
              </div>
            `,
            methods: {
              remarkChange(e) {
                rowDataTemp[rowDataTemp.length - 1]['remark'] = e
              }
            }
          })
        }
      }
    },
    {
      field: 'scoreLogicDesc',
      width: 200,
      headerText: i18n.t('评分逻辑描述'),
      allowTextWrap: false,
      allowEditing: false,
      ignore: true
    },
    {
      field: 'achievementsStartMonth',
      headerText: i18n.t('绩效起始月份'),
      width: 0.1,
      searchOptions: {
        clearButton: true,
        elementType: 'date-month',
        // type: 'date',
        // dateFormat: 'YYYYmm'
        serializeValue: (e) => {
          //自定义搜索值，规则
          return utils.formateTime(new Date(e), 'yyyyMM')
        }
      },
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'achievementsEndMonth',
      headerText: i18n.t('绩效截止月份'),
      width: 0.1,
      searchOptions: {
        clearButton: true,
        elementType: 'date-month',
        // type: 'date',
        // dateFormat: 'YYYYmm'
        serializeValue: (e) => {
          //自定义搜索值，规则
          return utils.formateTime(new Date(e), 'yyyyMM')
        }
      },
      allowEditing: false,
      allowResizing: false
    },

    {
      field: 'evaluationCycle',
      width: 120,
      headerText: i18n.t('评价周期'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: perevaluationOptions,
        fields: { text: 'dictName', value: 'dictCode' }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.evaluationCycle) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = perevaluationOptions.filter((j) => j.dictCode === status)
                return label[0]['dictName']
              }
            }
          })
        }
      }
    },
    {
      field: 'orgName',
      width: 200,
      headerText: i18n.t('组织'),
      allowTextWrap: false,
      allowEditing: false
    },

    {
      field: 'templateName',
      width: 120,
      headerText: i18n.t('绩效模板'),
      allowEditing: false,
      searchOptions: {
        elementType: 'select',
        allowFilter: true,
        dataSource: that.templateList,
        fields: { text: 'templateName', value: 'templateName' },
        filterType: 'Contains',
        placeholder: i18n.t('请选择绩效模板')
      }
      // editType: 'numericEdit'
    },

    {
      field: 'originScore',
      width: 120,
      headerText: i18n.t('满分'),
      allowEditing: false
    },
    {
      field: 'maxDeductScore',
      width: 120,
      headerText: i18n.t('加/扣分上限'),
      allowEditing: false
    },
    {
      field: 'scoreDimension',
      width: 120,
      headerText: i18n.t('打分维度'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('指标名称'),
          2: i18n.t('指标说明')
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.scoreDimension) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = scoreDimensionOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },

    {
      field: 'status',
      headerText: i18n.t('发布状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: { 0: i18n.t('未发布'), 1: i18n.t('已发布') }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.status) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = statusOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },
    {
      field: 'submitStatus',
      headerText: i18n.t('提交状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: { 0: i18n.t('未提交'), 1: i18n.t('已提交') }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.submitStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = submitStatusOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },
    {
      field: 'approveStatus',
      headerText: i18n.t('审批状态'),
      allowEditing: false,
      // ignore: true,
      valueConverter: {
        type: 'map',
        map: {
          null: i18n.t('未审批'),
          1: i18n.t('未审批'),
          2: i18n.t('审批中'),
          3: i18n.t('审批通过'),
          4: i18n.t('审批驳回')
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.approveStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = approveStatusOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },
    {
      field: 'scoreUserName',
      headerText: i18n.t('评分人'),
      allowEditing: false
      // allowEditing: JSON.parse(sessionStorage.getItem('userInfo')).roleList.some(
      //   (i) => i === 'TENANT_ADMIN'
      // )
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建日期'),
      searchOptions: {
        clearButton: true,
        type: 'date',
        dateFormat: 'YYYY-mm-dd'
      },
      allowEditing: false
    },
    {
      field: 'updateUserName',
      headerText: i18n.t('更新人'),
      allowEditing: false
    },
    {
      field: 'updateTime',
      headerText: i18n.t('更新时间'),
      searchOptions: {
        clearButton: true,
        type: 'date',
        dateFormat: 'YYYY-mm-dd'
      },
      allowEditing: false
    },
    {
      field: 'resultNull',
      headerText: i18n.t('得分是否为空'),
      allowEditing: false,
      width: 0.1,
      allowResizing: false,
      valueConverter: {
        type: 'map',
        map: { 1: i18n.t('是'), 0: i18n.t('否') }
      }
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      gridId: '9ac22bc5-6211-493c-b161-0e0574babdba',
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      toolbar,
      grid: {
        allowEditing: true, //开启表格编辑操作
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal' // 选择默认模式，双击整行可以进行编辑
        },
        // editSettings: {
        //   allowAdding: true,
        //   allowEditing: true,
        //   allowDeleting: true,
        //   mode: 'Normal',
        //   allowEditOnDblClick: true
        // },
        // height: '100%',
        allowTextWrap: false,
        allowFilteringFields: ['templateName', 'categoryName', 'supplierName', 'indexName'],
        allowFiltering: true,
        allowSorting: false,
        columnData: columnData(that),
        asyncConfig: {
          url: '/analysis/tenant/buyer/assess/computeScoreDetail/pageQuery'
        }
      }
    }
  ]
  return config
}
