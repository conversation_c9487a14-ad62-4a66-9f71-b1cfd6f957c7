//绩效考评模板Tab
import { i18n } from '@/main.js'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
    // permission: ["O_02_0049"],
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
    // permission: ["O_02_0052"],
  },
  {
    id: 'Edit',
    icon: 'icon_solid_Edit',
    title: i18n.t('编辑')
    // permission: ["O_02_0052"],
  },
  // {
  //   id: "EXdownload",
  //   icon: "icon_solid_Download ",
  //   title: "下载Excel模板",
  //   // permission: ["O_02_0050"],
  // },
  {
    id: 'EXimport',
    icon: 'icon_solid_upload   ',
    title: i18n.t('Excel导入')
    // permission: ["O_02_0051"],
  },
  {
    id: 'EXdownload',
    icon: 'icon_solid_Download',
    title: i18n.t('导出')
    // permission: ["O_02_0042"],
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'employeeName',
    headerText: i18n.t('姓名')
  },
  {
    field: 'orgCode',
    headerText: i18n.t('组织编码')
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织名称')
  },
  {
    field: 'categoryNames',
    headerText: i18n.t('负责品类'),
    searchOptions: {
      elementType: 'remote-autocomplete',
      renameField: 'categoryCodes',
      fields: { text: 'categoryName', value: 'categoryCode' },
      params: {},
      multiple: true,
      operator: 'in',
      url: '/masterDataManagement/tenant/category/paged-query',
      placeholder: ' ',
      searchFields: ['categoryCode', 'categoryName']
    }
  },
  {
    field: 'categoryCodes',
    headerText: i18n.t('负责品类编码'),
    ignore: true
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板')
  },
  {
    field: 'indexNames',
    headerText: i18n.t('指标')
  },

  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    ignore: true
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    ignore: true
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('更新日期'),
    ignore: true
  }
]

export const pageConfig = (url) => [
  {
    gridId: '69470502-0802-4077-a126-e00dc5a4f1ec',
    toolbar,
    grid: {
      // allowTextWrap: true,
      // height: 'auto',
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
