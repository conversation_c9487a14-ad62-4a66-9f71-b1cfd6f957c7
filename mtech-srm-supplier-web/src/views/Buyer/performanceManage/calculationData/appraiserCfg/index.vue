<template>
  <!-- 指标定义Tab -->
  <div class="appraiser-config-container">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { download } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  data() {
    return {
      delBuyerAssessRequest: {
        ids: []
      },
      //列表调用
      pageConfig: pageConfig(this.$API.performanceManage.catassessQuery)
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'Delete') {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id == 'Add') {
        this.analysisOfSettingAdd()
      } //编辑
      else if (e.toolbar.id == 'Edit') {
        this.analysisOfSettingEdit(_selectGridRecords)
      } //删除
      else if (e.toolbar.id == 'Delete') {
        this.analysisOfSettingDelete(_selectGridRecords)
      } //Excel导入
      else if (e.toolbar.id == 'EXimport') {
        this.analysisOfSettingEXimport()
      } //Excel导出
      else if (e.toolbar.id == 'EXdownload') {
        this.analysisOfSettingEXDownload()
      }
    },
    //例外供应商 导出
    analysisOfSettingEXDownload() {
      let params = this.$refs.templateRef.getAsyncParams()
      // let ids = rows.map((v) => {
      //   return v.id
      // })
      params.page.size = 99999
      this.$API.performanceManage.fileExportRater(params).then((res) => {
        const { data, headers } = res
        download({
          // fileName: '模板清单.xlsx',
          fileName: decodeURI(headers['content-disposition'].split('=')[1]),
          blob: data
        })
      })
    },
    //编辑配置
    analysisOfSettingEdit(_selectGridRecords) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (_selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('暂只支持单行编辑'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () => import('./components/templateDialog.vue'),
        data: {
          title: this.$t('编辑评分人'),
          data: _selectGridRecords[0],
          editType: 'edit'
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //新增配置
    analysisOfSettingAdd() {
      this.$dialog({
        modal: () => import('./components/templateDialog.vue'),
        data: {
          title: this.$t('新增评分人'),
          ownerOrg: this.ownerOrg
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //删除配置
    analysisOfSettingDelete(val) {
      val.forEach((item) => {
        this.delBuyerAssessRequest.ids.push(item.id)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          const ids = this.delBuyerAssessRequest.ids.toString()
          this.$API.performanceManage.cateexadelete({ ids: ids }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
          this.delBuyerAssessRequest.ids = []
        }
      })
    },
    //Excel导入
    analysisOfSettingEXimport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.performanceManage.fileUploadRater,
          downloadTemplateApi: this.$API.performanceManage.fileDownloadRater
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //单元格icons，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.appraiser-config-container {
  height: 100%;
  ::v-deep .mt-select-index {
    float: left;
  }
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
