<template>
  <!-- 例外供应商页面 -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleQuickSearch="handleQuickSearch"
    ></mt-template-page>
  </div>
</template>

<script>
import { download } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  data() {
    return {
      delBuyerAssessRequest: {
        ids: []
      },
      // index: 0,
      pageConfig: pageConfig(this.$API.analysisOfSetting.cateexceptpageQuery),
      isEdit: 1 // 当前是否编辑状态
    }
  },
  mounted() {},
  methods: {
    //快捷查询回调
    handleQuickSearch(data) {
      const { rules } = data
      //将data.rules里的生效月份和失效月份转换成 月份的时间戳
      rules.rules.forEach((item) => {
        //生效月份
        if (item.field === 'effectiveStartMonth') {
          let yearmonth = this.$utils.formateTime(new Date(item.value), 'yyyy/MM')
          item.value = new Date(yearmonth).getTime()
        }
        //失效月份
        if (item.field === 'effectiveEndMonth') {
          let yearmonth = this.$utils.formateTime(new Date(item.value), 'yyyy/MM')
          item.value = new Date(yearmonth).getTime()
        }
      })
    },
    //Excel导入 - 例外供应商
    exceptSupplierEXimport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('上传/导入'),
          paramsKey: 'multipartFile',
          importApi: this.$API.supplierExcept.fileUploadExceptSupplier,
          downloadTemplateApi: this.$API.supplierExcept.fileDownloadExceptSupplier
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //点击单元格回调
    handleClickCellTitle(e) {
      console.log(e)
      // 点击供应商编码回调
      if (e.field == 'supplierCode') {
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            supplierCode: e.data.supplierCode,
            orgCode: e.data.orgCode
          }
        })
      }
    },
    //工具栏点击事件
    handleClickToolBar(item) {
      //获取选中的数据
      let _selectGridRecords = item.data ? [item.data] : item.gridRef.getMtechGridRecords()
      //获取按钮id
      let name = item.toolbar ? item.toolbar.id : item.tool.id
      //删除时，判断是否有选中数据
      if (_selectGridRecords.length <= 0 && (name == 'Delete' || name === 'Edit')) {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (name == 'Add') {
        //新增
        this.exceptSupplierAdd()
      } else if (name == 'Delete') {
        //删除
        this.exceptSupplierDelete(_selectGridRecords)
      } else if (name == 'Calculation') {
        //计算供应商绩效范围
        console.log('计算')
      } else if (name == 'EXimport') {
        //导入
        this.exceptSupplierEXimport()
      } //Excel导出
      else if (name == 'EXdownload') {
        this.exceptSupplierEXDownload()
      } // 编辑
      else if (name == 'Edit') {
        if (_selectGridRecords.length > 1)
          return this.$toast({
            content: this.$t('只能对一条数据进行编辑'),
            type: 'warning'
          })
        this.handleEdit(_selectGridRecords)
      }
    },
    //例外供应商 编辑
    handleEdit(row) {
      this.$dialog({
        modal: () => import('./components/targetDialog.vue'),
        data: {
          title: this.$t('例外供应商'),
          ownerOrg: this.ownerOrg,
          flag: this.isEdit,
          rowData: row[0]
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //例外供应商 导出
    exceptSupplierEXDownload() {
      let params = this.$refs.templateRef.getAsyncParams()
      // let ids = rows.map((v) => {
      //   return v.id
      // })
      params.page.size = 99999
      this.$API.analysisOfSetting.fileExportSupplier(params).then((res) => {
        const { data, headers } = res
        download({
          // fileName: '模板清单.xlsx',
          fileName: decodeURI(headers['content-disposition'].split('=')[1]),
          blob: data
        })
      })
    },
    //例外供应商 新增
    exceptSupplierAdd() {
      this.$dialog({
        modal: () => import('./components/targetDialog.vue'),
        data: {
          title: this.$t('例外供应商'),
          ownerOrg: this.ownerOrg
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //例外供应商 删除
    exceptSupplierDelete(val) {
      console.log(val)
      val.forEach((item) => {
        this.delBuyerAssessRequest.ids.push(item.id)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.analysisOfSetting.cateexceptiondel(this.delBuyerAssessRequest).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
          this.delBuyerAssessRequest.ids = []
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
