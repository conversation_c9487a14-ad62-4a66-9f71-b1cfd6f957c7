//指标定义Tab
import { i18n } from '@/main.js'
import Vue from 'vue'

const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
    // permission: ["O_02_0044"],
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
    // permission: ["O_02_0045"],
  },
  {
    id: 'EXimport',
    icon: 'icon_solid_upload',
    title: i18n.t('导入')
    // permission: ["O_02_0046"],
  },
  {
    id: 'EXdownload',
    icon: 'icon_solid_Download',
    title: i18n.t('导出')
    // permission: ["O_02_0042"],
  },
  {
    id: 'Edit',
    icon: 'icon_solid_Createorder',
    title: i18n.t('编辑')
    // permission: ["O_02_0042"],
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
    // cellTools:[] //单元格里面的工具栏操作按钮
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织')
  },
  {
    field: 'remark',
    headerText: i18n.t('原因')
  },
  {
    field: 'effectiveStartMonth',
    headerText: i18n.t('生效月份'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm'
    },
    template: () => {
      return {
        template: Vue.component('effectiveStartMonth', {
          template: `
              <div class="time-box">
                {{ timeStr}}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = this.data.effectiveStartMonth.split('-')
              return timeStr[0] + '-' + timeStr[1]
            }
          }
        })
      }
    }
  },
  {
    field: 'effectiveEndMonth',
    headerText: i18n.t('失效月份'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm'
    },
    template: () => {
      return {
        template: Vue.component('effectiveEndMonth', {
          template: `
              <div class="time-box">
                {{ timeStr}}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = this.data.effectiveEndMonth.split('-')
              return timeStr[0] + '-' + timeStr[1]
            }
          }
        })
      }
    }
  },
  // {
  //   field: 'buyerAssessExcludeCategoryRelResponseList',
  //   headerText: i18n.t('品类'),
  //   // ignore: true, //‘忽略’此字段
  //   template: () => {
  //     return {
  //       template: Vue.component('buyerAssessExcludeCategoryRelResponseList', {
  //         template: `
  //             <div class="time-box" v-if="data.buyerAssessExcludeCategoryRelResponseList && data.buyerAssessExcludeCategoryRelResponseList.length>0">
  //               {{ data.buyerAssessExcludeCategoryRelResponseList.map(item=>item.categoryName).join(",") }}
  //             </div>
  //             <div class="time-box" v-else>
  //             </div>
  //             `
  //       })
  //     }
  //   },
  //   searchOptions: {
  //     renameField: 'categoryName'
  //   }
  // },
  {
    field: 'categoryCodes',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryNames',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('createTime', {
          template: `
              <div class="time-box">
                {{ data.createDate }}
              </div>`
        })
      }
    }
  }
]

export const pageConfig = (url) => [
  {
    gridId: 'a4160e55-6208-4b96-9d69-ed9ce2740155',
    toolbar,
    grid: {
      columnData,
      allowFilteringFields: ['buyerAssessExcludeCategoryRelResponseList'], // 高级搜索忽略
      ignoreFields: ['buyerAssessExcludeCategoryRelResponseList'], //忽略查询字段
      asyncConfig: {
        url
      }
    }
  }
]
