//现场评审得分设置Tab
import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'

export const reviewTypeOptions = utils.getSupplierDict('PER_REVIEW_TYPE') || []
export const toolbarOptions = () => {
  const toolbarOptions = [
    {
      text: i18n.t('导出'),
      id: 'export',
      prefixIcon: 'e-excelexport'
    }
  ]
  return toolbarOptions
}

export const columnData = () => {
  const columnHeader = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'orgName',
      headerText: i18n.t('公司'),
      width: 220
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: 120,
      cssClass: ''
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: 150,
      cssClass: ''
    },
    {
      field: 'categCode',
      headerText: i18n.t('品类编码'),
      width: 120
    },
    {
      field: 'categName',
      headerText: i18n.t('品类名称'),
      width: 180
    },
    {
      field: 'reviewType',
      headerText: i18n.t('评审类型'),
      width: 120,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span>{{ getStatusLabel(data.reviewType) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = reviewTypeOptions.filter((j) => j.dictCode === status)
                return label[0]['dictName']
              }
            }
          })
        }
      }
    },
    {
      field: 'score',
      headerText: i18n.t('评审得分'),
      width: 120
    },
    {
      field: 'reviewDate',
      headerText: i18n.t('评审日期'),
      width: 150,
      searchOptions: {
        type: 'date',
        dateFormat: 'YYYY-mm-dd'
      }
    },
    {
      field: 'reviewDocNo',
      headerText: i18n.t('评审单号'),
      width: 180
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建时间'),
      width: 150,
      searchOptions: {
        type: 'date',
        dateFormat: 'YYYY-mm-dd'
      }
    }
  ]
  return columnHeader
}
