<template>
  <div>
    <div class="comprehensive-container">
      <!-- 搜索区域 -->
      <collapse-search
        @reset="reset"
        @search="search"
        :is-grid-display="true"
        :default-max-height="75"
      >
        <mt-form ref="ruleForm" :model="queryForm" :validate-on-rule-change="false">
          <mt-form-item prop="assessCycleName" :label="$t('公司')" label-style="top">
            <mt-select
              v-model="queryForm.orgName"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="corporationArrList"
              :show-clear-button="true"
              :fields="{ text: 'orgName', value: 'orgName' }"
              :placeholder="$t('公司')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
            <mt-input
              v-model="queryForm.supplierCode"
              :show-clear-button="false"
              :placeholder="$t('供应商编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
            <mt-input
              v-model="queryForm.supplierName"
              :show-clear-button="false"
              :placeholder="$t('供应商名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="categCode" :label="$t('品类编码')" label-style="top">
            <mt-input
              v-model="queryForm.categCode"
              :show-clear-button="false"
              :placeholder="$t('品类编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="categName" :label="$t('品类名称')" label-style="top">
            <mt-input
              v-model="queryForm.categName"
              :show-clear-button="false"
              :placeholder="$t('品类名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="reviewType" :label="$t('评审类型')" label-style="top">
            <mt-select
              v-model="queryForm.reviewType"
              :data-source="reviewTypeOptions"
              :show-clear-button="true"
              :placeholder="$t('评审类型')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="score" :label="$t('评审得分')" label-style="top">
            <mt-input
              v-model="queryForm.score"
              :show-clear-button="false"
              :placeholder="$t('评审得分')"
            ></mt-input>
          </mt-form-item>

          <mt-form-item prop="reviewDate" :label="$t('评审日期')" label-style="top">
            <mt-date-picker
              v-model="queryForm.reviewDate"
              format="yyyy-MM-dd"
              :placeholder="$t('请选择评审日期')"
            ></mt-date-picker>
          </mt-form-item>

          <mt-form-item prop="reviewDocNo" :label="$t('评审单号')" label-style="top">
            <mt-input
              v-model="queryForm.reviewDocNo"
              :show-clear-button="true"
              :placeholder="$t('评审单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
            <mt-date-picker
              v-model="queryForm.createDate"
              format="yyyy-MM-dd"
              :placeholder="$t('请选择创建时间')"
            ></mt-date-picker>
          </mt-form-item>
        </mt-form>
      </collapse-search>
    </div>
    <div>
      <!-- 按钮 -->
      <div class="button-group">
        <icon-button icon="icon_solid_export" :text="$t('导出')" @click="toolbarClick('export')" />
      </div>
      <!-- 表格 -->
      <mt-data-grid
        ref="dataGrid"
        :column-data="columnData"
        :selection-settings="{ checkboxOnly: true }"
        :data-source="dataSource"
        :allow-sorting="true"
        :allow-scrolling="true"
        :allow-paging="true"
        height="405"
        :page-settings="pageSettings"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      >
      </mt-data-grid>
    </div>
  </div>
</template>

<script>
import collapseSearch from '@/components/collapseSearch'
import { columnData, reviewTypeOptions } from './config'
import { download } from '@/utils/utils'
import iconButton from '@/components/iconButton/index.vue'
import utils from '@/utils/utils'
import { cloneDeep, debounce } from 'lodash'
export default {
  components: { collapseSearch, iconButton },
  data() {
    return {
      corporationArrList: [],
      columnData: columnData(),
      dataSource: [], // 表格数据
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50]
      },
      queryForm: {
        orgName: '', // 公司
        supplierCode: '', // 供应商编码
        supplierName: '', // 供应商名称
        categCode: '', // 品类编码
        categName: '', // 品类名称
        reviewType: '', // 评审类型
        score: '', // 评审得分
        reviewDate: '', // 评审日期
        reviewDocNo: '', // 评审单号
        createDate: '' // 创建日期
      }
    }
  },
  computed: {
    reviewTypeOptions() {
      const options = reviewTypeOptions.map((item) => {
        const obj = {
          text: item.dictName,
          value: item.dictCode
        }
        return obj
      })
      return options
    }
  },
  created() {
    this.getList()
    this.getCorporationListList()
  },
  methods: {
    // 获取公司列表
    getCorporationListList() {
      this.$API.performanceManage.corporationListList({}).then((res) => {
        if (res.code == 200) {
          this.corporationArrList = res.data
        }
      })
    },

    formatDate(date, fmt = 'Y-m-d') {
      if (!date) {
        return
      }
      date = new Date(date)
      let ret
      const opt = {
        'Y+': date.getFullYear().toString(),
        'm+': (date.getMonth() + 1).toString(),
        'd+': date.getDate().toString(),
        'H+': date.getHours().toString(),
        'M+': date.getMinutes().toString(),
        'S+': date.getSeconds().toString()
      }
      for (const k in opt) {
        ret = new RegExp('(' + k + ')').exec(fmt)
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0')
          )
        }
      }
      return fmt
    },

    paramsFn() {
      function fn(data) {
        data = cloneDeep(data)
        if (!Object.keys(data).length) return
        Object.keys(data).forEach((item) => {
          const key = data[item]
          if (key === '' || key === null || key === undefined) {
            delete data[item]
          }
        })
        return data || {}
      }

      const query = {
        ...this.queryForm,
        reviewDate: this.queryForm.reviewDate
          ? utils.formateTime(new Date(this.queryForm.reviewDate))
          : '', // 评审日期
        createDate: this.queryForm.createDate
          ? utils.formateTime(new Date(this.queryForm.createDate))
          : '' // 创建日期
      }

      const data = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...fn(query)
      }

      return data
    },
    // 获取列表
    getList: debounce(function () {
      const data = this.paramsFn()
      this.$API.performanceManage.siteReviewScoreList(data).then((res) => {
        if (res.code == 200) {
          this.dataSource = res.data.records.map((item) => {
            item.reviewDate = item.reviewDate ? this.formatDate(item.reviewDate, 'YYYY-mm-dd') : ''
            return item
          })
          this.pageSettings = {
            ...this.pageSettings,
            currentPage: Number(res.data.current || 0),
            pageSize: Number(res.data.size || 0),
            totalRecordsCount: Number(res.data.total || 0)
          }
        }
      })
    }, 200),

    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },

    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.search()
    },

    search() {
      this.pageSettings = {
        currentPage: Number(this.pageSettings.currentPage),
        pageSize: Number(this.pageSettings.pageSize),
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50]
      }
      this.getList()
    },

    reset() {
      this.pageSettings = {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50]
      }
      this.queryForm = {
        orgName: '', // 公司
        supplierCode: '', // 供应商编码
        supplierName: '', // 供应商名称
        categCode: '', // 品类编码
        categName: '', // 品类名称
        reviewType: '', // 评审类型
        score: '', // 评审得分
        reviewDate: '', // 评审日期
        reviewDocNo: '', // 评审单号
        createDate: '' // 创建日期
      }
      this.getList()
    },

    // 表格顶部工具栏按钮
    toolbarClick(id) {
      // 点击导出
      if (id === 'export') {
        this.exportList()
      }
    },

    // 导出按钮
    exportList() {
      const data = this.paramsFn()
      this.$API.performanceManage.getReviewScoreReportExportApi(data).then((res) => {
        const { data, headers } = res
        download({
          // fileName: '现场评审得分.xlsx',
          fileName: decodeURI(headers['content-disposition'].split("'zh_cn'")[1]),
          blob: data
        })
        this.$toast({
          content: this.$t('导出成功'),
          type: 'success'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.comprehensive-container {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  .button-group {
    display: flex;
    padding: 0 0 16px;
  }
  .toggle-tag {
    margin-left: 4px;
  }
}
.search-input {
  padding: 10px 0 16px;
  .toggle-tag {
    padding-right: 15px;
    color: #2783fe;
    display: inline-block;
    font-size: 12px;
    position: relative;
    cursor: pointer;
    user-select: none;
    .mt-icons {
      font-size: 12px;
      position: absolute;
      transform: scale(0.4);
      top: -2px;
      left: 26px;
      &:nth-child(2) {
        top: 2px;
      }
    }
  }
  .search-area {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 16px 24px 12px;
    margin-top: 12px;
    .main-form {
      /deep/ .mt-form-item {
        width: calc(25% - 20px);
        min-width: 200px;
        display: inline-flex;
        margin-top: 5px;
        margin-right: 20px;

        .full-width {
          width: calc(100% - 20px) !important;
        }
        .e-ddt .e-ddt-icon {
          background: unset;
          &::before {
            content: '\e36a';
            font-size: 16px;
          }
        }
      }
      .check-area {
        transform: translateY(10px);
      }
    }
    .button-group {
      padding-top: 10px;
      span {
        margin-right: 16px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #2783fe;
        cursor: pointer;
        user-select: none;
      }
    }
  }
}
.button-group {
  display: flex;
  padding: 0 0 16px;
}
</style>
