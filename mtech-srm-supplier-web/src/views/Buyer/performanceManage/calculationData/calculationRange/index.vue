<template>
  <!-- 绩效计算范围页面 -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { getHeadersFileName, download } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  data() {
    return {
      delBuyerAssessRequest: {
        ids: []
      },
      pageConfig: pageConfig(this.$API.supplierCalculationRange.pageQuery)
    }
  },
  mounted() {},
  methods: {
    //Excel导入
    EXimport() {},
    //导出
    EXdownload() {
      //获取template-page组件查询的参数
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 9999 },
        rules: rule.rules || []
      }
      //调用接口
      this.$API.supplierCalculationRange.exportTemplate(params).then((res) => {
        const fileName = getHeadersFileName(res)
        //下载文件
        download({
          fileName,
          blob: res.data
        })
      })
    },
    //点击单元格回调
    handleClickCellTitle(e) {
      // 点击供应商编码回调
      if (e.field == 'supplierCode') {
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            supplierCode: e.data.supplierCode,
            orgCode: e.data.orgCode
          }
        })
      }
    },
    //
    handleClickCellTool(e) {
      const { data } = e
      this.rowDelete(data)
    },
    //工具栏点击事件
    handleClickToolBar(item) {
      //获取选中的数据
      let _selectGridRecords = this.$refs.templateRef
        .getCurrentTabRef()
        .gridRef.getMtechGridRecords()
      //获取按钮id
      let name = item.toolbar ? item.toolbar.id : item.tool.id
      //删除，判断是否有选中数据
      if (_selectGridRecords.length < 1 && name == 'delete') {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (name == 'Add') {
        //新增
        this.dialogAdd()
      } else if (name == 'delete') {
        //删除
        this.handleDelete(_selectGridRecords)
      } else if (name == 'EXimport') {
        this.importData()
      } else if (name == 'EXdownload') {
        //导出
        this.EXdownload()
      } else if (name == 'List') {
        // 未在范围的供应商清单
        this.viewList()
      }
    },
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除？')
        },
        success: () => {
          this.$API.supplierCalculationRange.batchDeleteApi(ids).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    //Excel导入 - 绩效计算范围
    importData() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('上传/导入'),
          paramsKey: 'multipartFile',
          importApi: this.$API.supplierCalculationRange.importData,
          downloadTemplateApi: this.$API.supplierCalculationRange.downloadTemplate
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //弹窗 新增
    dialogAdd() {
      this.$dialog({
        modal: () => import('./components/targetDialog.vue'),
        data: {
          title: this.$t('绩效计算范围')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 弹窗 未在范围的供应商清单
    viewList() {
      this.$dialog({
        modal: () => import('./components/supplierListDialog.vue'),
        data: {
          title: this.$t('未在范围的供应商清单')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // //弹窗编辑
    // dialogEdit(data) {
    //   console.log('data', data)
    //   this.$dialog({
    //     modal: () => import('./components/targetDialog.vue'),
    //     data: {
    //       title: this.$t('绩效计算范围'),
    //       data: data[0]
    //     },
    //     success: () => {
    //       this.$refs.templateRef.refreshCurrentGridData()
    //     }
    //   })
    // },
    //行 删除
    rowDelete(val) {
      //判断val数组里hide的值 1是手动创建 且创建人=系统当前登录账号时，可删除 ，0不可删除
      if (val.hide == 0) {
        this.$toast({ content: this.$t('您不是当前单据的创建人，不能删除'), type: 'warning' })
        return
      }
      const form = { id: val.id }
      //校验当前行的状态 errorCode=1可删除，则弹出确认删除提示框，确认后直接删除，errorCode=0 绩效结果已审批/审批中，！弹窗询问errorMessage后确认删除
      this.$API.supplierCalculationRange.checkDelete(form).then((res) => {
        if (res.data?.errorCode == 1) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除数据？')
            },
            success: () => {
              this.sumbitDelete(form)
            }
          })
        } else {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t(res.data.errorMessage)
            },
            success: () => {
              this.sumbitDelete(form)
            }
          })
        }
      })
    },
    //请求删除接口
    sumbitDelete(form) {
      this.$API.supplierCalculationRange.delete(form).then((res) => {
        if (res.code == 200) {
          //删除成功后刷新表格
          this.$toast({
            content: this.$t('删除成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData() //刷新列表
        }
      })
      this.delBuyerAssessRequest.ids = []
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
