<template>
  <mt-dialog
    ref="dialog"
    :width="1200"
    :height="711"
    :buttons="buttons"
    :header="header"
    @open="onOpen"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-template-page
        ref="templateDialogRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { getHeadersFileName, download } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  data() {
    return {
      // 弹窗确认事件
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      pageConfig: [],
      orgList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    //获取组织树
    this.$API.performanceManage.getListOrgForSpecial().then((res) => {
      if (res.code == 200) {
        this.orgList = res.data || []
        // 获取可选择的绩效月份
        this.$API.performanceManage.queryAchievementsMonth().then((resp) => {
          if (resp.code == 200) {
            this.pageConfig = pageConfig(this.orgList, resp.data)
            this.$refs['dialog'].ejsRef.show() //显示弹窗
          }
        })
      }
    })
  },
  methods: {
    //工具栏点击事件
    handleClickToolBar(item) {
      //获取选中的数据
      let _selectGridRecords = item.data ? [item.data] : item.gridRef.getMtechGridRecords()
      //获取按钮id
      let name = item.toolbar ? item.toolbar.id : item.tool.id
      const _achievementsMonth = this.validAchievementsMonth()
      //删除或编辑，判断是否有选中数据
      if (_selectGridRecords.length < 1 && name == 'Add') {
        // 校验月份查询条件必填
        if (!_achievementsMonth) {
          this.$toast({
            content: this.$t('请按照绩效月份查询'),
            type: 'warning'
          })
          return
        }
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (name == 'Add') {
        //新增
        this.dialogAdd(_selectGridRecords, _achievementsMonth)
      } else if (name == 'EXdownload') {
        //导出
        this.EXdownload()
      }
    },
    dialogAdd(records, achievementsMonth) {
      const params = this.mergeData(records, achievementsMonth)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认添加所选数据吗？')
        },
        success: () => {
          this.$API.supplierCalculationRange.add(params).then((res) => {
            if (res.code == 200) {
              //删除成功后刷新表格
              this.$toast({
                content: this.$t('添加成功'),
                type: 'success'
              })
              this.$refs.templateDialogRef.refreshCurrentGridData() //刷新列表
              this.$emit('confirm-function') //关闭弹窗
            }
          })
        }
      })
    },
    validAchievementsMonth() {
      // 获取查询条件数据
      const _rules = this.$refs.templateDialogRef.getCurrentUsefulRef().pluginRef.queryBuilderRules
      if (!_rules || !_rules?.rules.length) {
        return false
      }
      const _rulesField = _rules.rules.map((item) => item.field)
      if (!_rulesField.includes('achievementsMonth')) {
        return false
      }
      const _achievements = _rules.rules.find((item) => item.field === 'achievementsMonth')
      return _achievements.value
    },
    // 组装数据
    mergeData(data, achievementsMonth) {
      if (!data || !data?.length) return []
      return data.map((item) => {
        return {
          categoryCode: item.categoryCode,
          categoryId: item.categoryId,
          categoryName: item.categoryName,
          orgCode: item.orgCode,
          orgId: item.orgId,
          orgName: item.orgName,
          remark: '',
          supplierCode: item.supplierCode,
          supplierId: item.supplierId,
          supplierName: item.supplierName,
          achievementsMonth: achievementsMonth
        }
      })
    },
    EXdownload() {
      //获取template-page组件查询的参数
      let rule =
        this.$refs.templateDialogRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 9999 },
        rules: rule.rules || []
      }
      //调用接口
      this.$API.supplierCalculationRange.exportNotScope(params).then((res) => {
        const fileName = getHeadersFileName(res)
        //下载文件
        download({
          fileName,
          blob: res.data
        })
      })
    },
    confirm() {
      this.$emit('confirm-function') //关闭弹窗
    },
    cancel() {
      this.$emit('cancel-function')
    },
    onOpen(args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  // padding-top: 18px;
  font-size: 16px;
  height: auto;
  width: 100%;
}
// 特殊button处理
/deep/.toolbar-container .mt-flex:first-child .toolbar-item:last-child {
  cursor: text;
  background: none;
  span {
    color: red;
  }
}
/deep/ .quick-search .mt-form .mt-form-item {
  width: 100% !important;
}
</style>
