<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @open="onOpen" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('组织：')">
          <mt-DropDownTree
            :key="fieldsOrg.dataSource.length"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择组织：')"
            :popup-height="500"
            :fields="fieldsOrg"
            @input="selectOrg"
            :allow-filtering="true"
            filter-type="Contains"
            id="baseTreeSelect"
          ></mt-DropDownTree>
        </mt-form-item>
        <!-- <mt-form-item prop="supplierCode" :label="$t('供应商代码：')">
          <mt-select
            v-model="formObject.supplierCode"
            :data-source="planeArrList"
            :fields="{
              text: 'supplierCode',
              value: 'supplierCode'
            }"
            :allow-filtering="true"
            :show-clear-button="true"
            @change="changePlanCode"
            :placeholder="$t('请选择供应商代码')"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="supplierName" :label="$t('供应商名称：')">
          <mt-select
            :allow-filtering="true"
            v-model="formObject.supplierName"
            :data-source="planeArrList"
            :fields="{
              text: 'codeAndName',
              value: 'supplierName'
            }"
            :show-clear-button="true"
            @change="changePlanName"
            :placeholder="$t('请选择供应商名称：')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryId" class="form-item positive" :label="$t('品类')">
          <!-- <mt-select
            :allow-filtering="true"
            v-model="formObject.categoryId"
            :data-source="categoryListArrList"
            :fields="{
              text: 'categoryName',
              value: 'id'
            }"
            :show-clear-button="true"
            @change="selectCategoryas"
            :placeholder="$t('请选择品类:')"
          ></mt-select> -->
          <mt-DropDownTree
            :fields="categoryListArrList"
            v-model="formObject.categoryId"
            :auto-check="true"
            :allow-filtering="true"
            filter-type="Contains"
            id="checkboxTreeSelect"
            :placeholder="$t('请选择品类')"
            @input="selectCategoryas"
            :key="categoryListArrList.key"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('新增原因：')">
          <mt-input
            :max-length="100"
            type="text"
            :placeholder="$t('请输入新增原因')"
            v-model="formObject.remark"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="achievementsMonth" :label="$t('绩效月份')">
          <mt-date-picker
            v-model="formObject.achievementsMonth"
            float-label-type="Never"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :open-on-focus="true"
            :show-today-button="false"
            :placeholder="$t('请选择月份')"
          ></mt-date-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils.js'
// import { cloneDeep } from 'lodash'
import dayjs from 'dayjs'
export default {
  data() {
    return {
      editStatus: false,
      // 表单验证
      rules: {
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商代码'),
            trigger: 'blur'
          }
        ],
        supplierName: [
          {
            required: true,
            message: this.$t('请选择供应商名称'),
            trigger: 'blur'
          }
        ],
        categoryId: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: true,
            message: this.$t('请输入新增原因'),
            trigger: 'blur'
          }
        ]
        // achievementsMonth: [
        //   {
        //     required: true,
        //     message: this.$t('请选择绩效月份'),
        //     trigger: 'blur'
        //   }
        // ]
      },
      // 弹窗确认事件
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      fieldsOrg: {
        // 组织树下拉数组
        dataSource: [], //下拉数据源
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      planeArrList: [], // 供应商下拉数组
      categoryListArrList: {
        dataSource: [], //品类树下拉数组
        value: 'id',
        text: 'codeAndName',
        child: 'childrens',
        key: 'E6BPdKfEj5Ky2NwbH2F6Nkrhj6W8MamD'
      }, // 品类下拉数组
      //表单数据
      formObject: {
        orgCode: '', //组织机构编码
        orgId: '', //组织机构id
        orgName: '', //组织机构名称

        orgIdArr: [], //组织机构id

        supplierCode: null, //供应商编码
        supplierId: null, //供应商id
        supplierName: null, //供应商名称

        categoryId: null, //品类

        remark: '', //新增原因
        achievementsMonth: '' //绩效月份
      },
      orgObj: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getOrgList() //获取组织树 - 下拉数据
    this.getCategoryList()
    // this.getSupplierList() //获取供应商 - 下拉数据

    this.$refs['dialog'].ejsRef.show() //显示弹窗
    //编辑模式 or 新增模式
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.editConfirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      //编辑数据回显
    }
  },
  watch: {
    'formObject.orgIdArr': {
      handler() {
        this.$refs.dialogRef.validateField('orgIdArr')
      }
    }
  },
  methods: {
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.fieldsOrg.dataSource = res.data
        }
      })
    },
    //选择组织
    selectOrg(e) {
      //清空供应商选中和品类选中
      this.formObject.supplierCode = '' //供应商编码
      this.formObject.supplierId = '' //供应商id
      this.formObject.supplierName = '' //供应商名称
      this.planeArrList = []
      //品类
      // this.$set(this.categoryListArrList, 'dataSource', [])
      // this.$set(this.categoryListArrList, 'key', this.randomString())
      // this.formObject.categoryId = [] //清空 品类的选中
      if (e.length > 0) {
        //匹配当前选中的组织 设置到formObject
        this.fn(this.fieldsOrg.dataSource, e[0])
      }
    },
    // 递归 获取当前选中组织  设置到formObject
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formObject.orgId = ele.id
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgName = ele.orgName
          this.getSupplierList({
            orgCode: ele.orgCode,
            orgId: ele.id,
            orgName: ele.orgName,
            orgType: ele.orgType,
            parentOrgId: ele.parentOrgId
          }) //获取供应商 - 下拉数据
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.fn(ele.childrenList, id)
        }
      })
    },
    // 获取供应商列表
    getSupplierList(form) {
      let pamrsform = {
        organizationCode: form.orgCode,
        organizationId: form.orgId,
        organizationName: form.orgName,
        organizationType: form.orgType,
        organizationParentId: form.parentOrgId
      }
      this.orgObj = { orgId: form.orgId, parentOrgId: form.parentOrgId, orgType: form.orgType }
      this.$API.supplierExcept.getFuzzy(pamrsform).then((result) => {
        if (result.code == 200) {
          this.planeArrList = result.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.supplierName}-${i.supplierCode}`
            }
          })
        }
      })
    },
    //选择供应商名称
    changePlanName(e) {
      let { itemData } = e
      if (itemData) {
        this.formObject.supplierCode = itemData.supplierCode //供应商编码
        this.formObject.supplierId = itemData.id //供应商id
        this.formObject.supplierName = itemData.supplierName //供应商名称
        // this.$set(this.categoryListArrList, 'dataSource', [])
        // this.$nextTick(() => {
        //   this.getCategoryList(itemData) //请求品类的下拉数据
        // })
      } else {
        // this.$set(this.categoryListArrList, 'dataSource', [])
        // this.$set(this.categoryListArrList, 'key', this.randomString())
        // this.formObject.categoryId = null //清空 品类的选中
      }
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 选择品类
    selectCategoryas(e) {
      if (e.length > 0) {
        //根据id匹配到当前选中的品类
        let category = this.categoryListArrList.dataSource.filter((ele) => {
          return ele.id == e[0]
        })
        console.log(category)
        //品类代码 品类id 品类名称
        this.formObject.categoryCode = category[0].categoryCode
        this.formObject.categoryId = category[0].id
        this.formObject.categoryName = category[0].categoryName
      }
    },
    // 获取品类列表
    getCategoryList() {
      this.$API.supplierExcept
        .getCategoryList({
          fuzzyNameOrCode: ''
        })
        .then((result) => {
          if (result.code === 200 && !utils.isEmpty(result.data)) {
            const list = result.data.map((i) => {
              return {
                ...i,
                codeAndName: `${i.categoryName}-${i.categoryCode}`
              }
            })
            this.$set(this.categoryListArrList, 'dataSource', list)
            this.$set(this.categoryListArrList, 'key', this.randomString())
          } else {
            this.$set(this.categoryListArrList, 'dataSource', [])
            this.$set(this.categoryListArrList, 'key', this.randomString())
          }
        })
    },
    // 确认按钮 - 新增
    confirm() {
      //表单校验 后 再请求接口校验状态
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          //数据处理
          let addForm = JSON.parse(JSON.stringify(this.formObject))
          delete addForm.orgIdArr //删除
          if (addForm.achievementsMonth) {
            //绩效月份achievementsMonth 校验只能选择上个月之后的月份 dayjs
            let prevMonth = dayjs().subtract(1, 'month').format('YYYY-MM') //上个月

            if (dayjs(prevMonth).isAfter(dayjs(addForm.achievementsMonth))) {
              this.$toast({
                content: this.$t('绩效月份不能选择当前月份之前的月份'),
                type: 'warning'
              })
              return
            }
            let achievementsMonth = this.$utils.formateTime(
              new Date(this.formObject.achievementsMonth),
              'yyyy-MM'
            )

            addForm.achievementsMonth = achievementsMonth + '-01' //转换为年月+01
          }

          //校验状态

          this.$API.supplierCalculationRange.check(addForm).then((res) => {
            if (res.code == 200) {
              //errorCode =1 校验通过   直接保存 ||errorCode =0 校验已存在 询问errorMessage
              if (res.data.errorCode == 1) {
                this.sumbitForm(addForm)
              } else {
                this.$dialog({
                  data: {
                    title: this.$t('提示'),
                    message: this.$t(res.data.errorMessage)
                  },
                  success: () => {
                    this.sumbitForm(addForm)
                  }
                })
              }
            }
          })
        }
      })
    },
    //提交表单
    sumbitForm(addForm) {
      this.$API.supplierCalculationRange.add([addForm]).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('新增成功'), type: 'success' })
          this.$emit('confirm-function') //关闭弹窗
          this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
        } else {
          this.$toast({ content: res.msg, type: 'warning' })
        }
      })
    },
    // 确认按钮 - 编辑
    editConfirm() {
      console.log(this.formObject)
      console.log(JSON.stringify(this.formObject))
    },
    cancel() {
      this.$emit('cancel-function')
    },
    onOpen(args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
}
</style>
