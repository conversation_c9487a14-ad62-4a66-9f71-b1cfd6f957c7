// 绩效计算范围
import { i18n } from '@/main.js'
// import { API } from '@mtech-common/http'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('添加')
  },
  {
    id: 'EXdownload',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  },
  {
    id: 'tips', // 通过button添加tips
    icon: '',
    title: i18n.t('“绩效月份”、组织为必填项')
  }
]

const columnData = (orgList, monthList) => [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 0.1,
    field: 'achievementsMonth',
    headerText: i18n.t('*绩效月份'),
    searchOptions: {
      elementType: 'select',
      dataSource: monthList
    },
    // searchOptions: {
    //   clearButton: true,
    //   elementType: 'date-month',
    //   // type: 'date',
    //   // dateFormat: 'YYYYmm',
    //   serializeValue: (e) => {
    //     //自定义搜索值，规则
    //     return utils.formateTime(new Date(e), 'yyyyMM')
    //   }
    // },
    allowResizing: false
  },
  {
    width: 0.1,
    field: 'orgName1',
    headerText: i18n.t('*组织'),
    searchOptions: {
      elementType: 'drop-down-tree',
      // dataSource: orgList,
      fields: {
        dataSource: orgList, //组织树下拉数组
        value: 'orgCode',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.join()
      },
      renameField: 'orgCode'
    }
  },
  {
    width: 100,
    field: 'orgName',
    headerText: i18n.t('组织'),
    // searchOptions: {
    //   elementType: 'select',
    //   dataSource: orgList,
    //   fields: { text: 'orgName', value: 'orgCode' },
    //   renameField: 'orgCode'
    // },
    searchOptions: {
      elementType: 'drop-down-tree',
      // dataSource: orgList,
      fields: {
        dataSource: orgList, //组织树下拉数组
        value: 'orgCode',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.join()
      },
      renameField: 'orgCode'
    },
    ignore: true
  },
  {
    width: 100,
    field: 'groupSupplierCode',
    headerText: i18n.t('集团供应商编码')
  },
  {
    width: 100,
    field: 'groupSupplierName',
    headerText: i18n.t('集团供应商名称')
  },
  {
    width: 80,
    field: 'supplierCode',
    headerText: i18n.t('供应商代码')
  },
  {
    width: 80,
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 70,
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: 70,
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

export const pageConfig = (orgList, monthList) => [
  {
    gridId: '9bf03486-5818-40e5-9467-a041f5d27535',
    toolbar,
    useToolTemplate: false,
    grid: {
      // 关闭查询
      // allowFiltering: true,
      allowFilteringFields: ['orgName'],
      height: '250px',
      allowSorting: false,
      columnData: columnData(orgList, monthList),
      asyncConfig: {
        ignoreDefaultSearch: true,
        url: '/analysis/tenant/buyer/assess/achievementsCalculationRange/pageQueryNotScope'
      }
    }
  }
]
