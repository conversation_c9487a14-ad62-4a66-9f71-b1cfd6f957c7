// 绩效计算范围
import { i18n } from '@/main.js'
// import Vue from 'vue'
// import { API } from '@mtech-common/http'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  {
    id: 'delete',
    icon: ' ',
    title: i18n.t('删除')
  },
  {
    id: 'EXimport',
    icon: 'icon_solid_upload',
    title: i18n.t('导入')
  },
  {
    id: 'EXdownload',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  },
  {
    id: 'List',
    icon: 'icon_solid_Createproject ',
    title: i18n.t('未在范围的供应商清单')
  }
]

const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织')
  },
  {
    width: 200,
    field: 'groupSupplierCode',
    headerText: i18n.t('集团供应商编码')
  },
  {
    field: 'groupSupplierName',
    headerText: i18n.t('集团供应商名称')
  },
  {
    width: 200,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'remark',
    headerText: i18n.t('新增原因')
  },
  {
    field: 'achievementsMonth',
    headerText: i18n.t('绩效月份')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('最后更新时间'),
    allowEditing: false,
    ignore: true
  }
  // {
  //   field: 'operation',
  //   headerText: i18n.t('操作'),
  //   width: 80,
  //   ignore: true, //忽略
  //   cellTools: [
  //     {
  //       id: 'Delete',
  //       icon: 'icon_solid_Delete',
  //       title: i18n.t('删除'),
  //       visibleCondition: (data) => {
  //         return data['hide'] == 1
  //       }
  //     }
  //   ]
  // }
]

export const pageConfig = (url) => [
  {
    gridId: '567a9ea8-4fc1-4051-8daa-94a32b5c2ef4',
    toolbar,
    useToolTemplate: false,
    grid: {
      //关闭查询
      // allowFiltering: false,
      allowSorting: false,
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
