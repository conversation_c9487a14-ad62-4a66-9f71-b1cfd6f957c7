import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'
// import bus from '@/utils/bus'

export const perGradeTypeOptions = [
  { text: i18n.t('得分'), value: 1 },
  { text: i18n.t('评分'), value: 2 }
]
export const levelAdjustOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const submitStatusOptions = [
  { text: i18n.t('未提交'), value: 0 },
  { text: i18n.t('已提交'), value: 1 }
]

export const approveStatusOptions = [
  { text: i18n.t('未审批'), value: 1 },
  { text: i18n.t('审批中'), value: 2 },
  { text: i18n.t('审批通过'), value: 3 },
  { text: i18n.t('审批驳回'), value: 4 }
]
export const pubStatusOptions = [
  { value: 5, text: i18n.t('未发布') },
  { value: 6, text: i18n.t('已发布') }
]

export const perEvaluationOptions = utils.getSupplierDict('PER_EVALUATION') || []
// 获取建议层级枚举项
export const perSuggestLevelOptions = utils.getSupplierDict('STRATEGIC_CATEGORY') || []
// 品类分层分级
const columnData = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织'),
    allowEditing: false
  },
  {
    field: 'approveDocNo',
    headerText: i18n.t('审批单号'),
    allowEditing: false,
    cssClass: 'field-content'
  },
  {
    field: 'docTitle',
    headerText: i18n.t('单据标题'),
    allowEditing: false
  },
  {
    field: 'evaluateCycleName',
    headerText: i18n.t('评价周期'),
    width: 100,
    allowEditing: false
  },
  {
    field: 'evaluateYear',
    headerText: i18n.t('评价年份'),
    width: 100,
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('描述'),
    width: 100,
    allowEditing: false
  },

  {
    field: 'approveStatus',
    headerText: i18n.t('审批状态'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.approveStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = approveStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    }
  },

  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建时间'),
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    allowEditing: false
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('最后更新时间'),
    allowEditing: false
  }
]

export const pageConfig = [
  {
    gridId: '9c0ae65d-5885-4ccd-8a21-c2d85d143a34',
    title: i18n.t(''),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    isUseCustomEditor: true,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [[], ['Filter', 'Refresh', 'Setting']]
    },
    useToolTemplate: false,
    grid: {
      columnData: columnData,
      // height: 500,
      frozenColumns: 1,
      asyncConfig: {
        url: 'analysis/tenant/buyer/assess/strategy/result/total/approve/pageQuery'
      }
    }
  }
]
