<template>
  <div>
    <div class="full-height">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        :use-tool-template="true"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleCustomReset"
        @handleClickCellTitle="handleClickCellTitle"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
                <mt-DropDownTree
                  v-if="orgFields.dataSource.length > 0"
                  v-model="orgIdArr"
                  :placeholder="$t('请选择组织')"
                  :popup-height="500"
                  :fields="orgFields"
                  :allow-filtering="true"
                  filter-type="Contains"
                  @change="selectOrg"
                  id="baseTreeSelect"
                />
                <mt-select
                  v-else
                  v-model="searchFormModel.orgId"
                  css-class="rule-element"
                  :data-source="[]"
                  :show-clear-button="true"
                  :placeholder="$t('请选择组织')"
                />
              </mt-form-item>

              <mt-form-item prop="approveDocNo" :label="$t('审批单号')" label-style="top">
                <mt-input v-model="searchFormModel.approveDocNo"></mt-input>
              </mt-form-item>
              <mt-form-item prop="docTitle" :label="$t('单据标题')" label-style="top">
                <mt-input v-model="searchFormModel.docTitle"></mt-input>
              </mt-form-item>

              <mt-form-item prop="evaluateCycle" :label="$t('评价周期')" label-style="top">
                <mt-select
                  v-model="searchFormModel.evaluateCycle"
                  css-class="rule-element"
                  :data-source="perEvaluationOptions"
                  :show-clear-button="true"
                  :fields="{ text: 'dictName', value: 'dictCode' }"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="evaluateYear" :label="$t('评价年份')" label-style="top">
                <mt-input v-model="searchFormModel.evaluateYear"></mt-input>
              </mt-form-item>

              <mt-form-item prop="remark" :label="$t('描述')" label-style="top">
                <mt-input v-model="searchFormModel.remark"></mt-input>
              </mt-form-item>

              <mt-form-item prop="approveStatus" :label="$t('审批状态')" label-style="top">
                <mt-select
                  v-model="searchFormModel.approveStatus"
                  css-class="rule-element"
                  :data-source="approveStatusOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>

              <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
                <mt-input v-model="searchFormModel.createUserName"></mt-input>
              </mt-form-item>
              <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.createDate"
                  :placeholder="$t('请选择创建时间')"
                  @change="(e) => handleDateTimeChange(e, 'CreateDate')"
                ></mt-date-range-picker>
              </mt-form-item>
              <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
                <mt-input v-model="searchFormModel.updateUserName"></mt-input>
              </mt-form-item>
              <mt-form-item prop="modifyDate" :label="$t('最后更新时间')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.modifyDate"
                  :placeholder="$t('请选择最后更新时间')"
                  @change="(e) => handleDateTimeChange(e, 'ModifyDate')"
                ></mt-date-range-picker>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
  </div>
</template>

<script>
import {
  pageConfig,
  perGradeTypeOptions,
  perEvaluationOptions,
  submitStatusOptions,
  approveStatusOptions,
  levelAdjustOptions
} from './config/index.js'
import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils.js'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      orgIdArr: [],
      templateListArrList: [], // 绩效模板
      searchFormModel: {},
      // 得分是否为空
      scoreNullOptions: [
        { text: this.$t('是'), value: true },
        { text: this.$t('否'), value: false }
      ],
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      //分页数据
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 表格 当前页码
      pageConfig,
      isLoading: false,
      perGradeTypeOptions,
      perEvaluationOptions,
      perSuggestLevelOptions: [],
      submitStatusOptions,
      approveStatusOptions,
      levelAdjustOptions,
      editrowdata: {},
      adjustLevelOption: [], //层级调整下拉值集
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'performanceManage',
        templateUrl: 'ResultsCategoryGradeExport', // 下载模板接口方法名
        uploadUrl: 'ResultsCategoryGradeImport', // 上传接口方法名
        file: 'excel' //后端接收参数名
      },
      resHeaderText: []
    }
  },

  created() {
    this.getOrgList() //获取组织下拉数据
    this.getSuggestLevelOptions() //获取建议层级数据
  },
  mounted() {},
  activated() {
    this.getOrgList()
  },
  deactivated() {
    this.orgFields.dataSource = []
  },
  computed: {},
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        console.log('e.startDate', e.startDate)
        this.searchFormModel['start' + field] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    // 重置
    handleCustomReset() {
      this.templateListArrList = []
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 选择绩效模板
    selectTemplate(e) {
      //清空供应商选中和品类选中
      let { itemData } = e
      if (itemData) {
        this.searchFormModel.templateId = itemData.id //模板id
        this.searchFormModel.templateCode = itemData.templateCode
      }
    },
    //获取绩效模板
    getTemplateList(para) {
      let params = {
        page: { current: 1, size: 9999 }
      }
      params = {
        ...params,
        ...para
      }
      this.$API.performanceManage.templateListQuery(params).then((result) => {
        this.templateListArrList = result.data
      })
    },
    // 获取建议层级数据
    getSuggestLevelOptions() {
      let params = {
        categCode: 'STRATEGIC_CATEGORY',
        layeredLevel: 1
      }
      this.$API.performanceManage.getSuggestLevelOptions(params).then((res) => {
        if (res.code === 200) {
          this.perSuggestLevelOptions = res.data.map((item) => ({
            text: item.layeredLevelTypeName,
            value: item.layeredLevelType
          }))
        }
      })
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    selectOrg(e) {
      const { value } = e
      if (e.value.length === 0) {
        this.searchFormModel.orgCode = null
        this.searchFormModel.orgId = null
      }
      this.matchItem(this.orgFields.dataSource, e['value'][0])

      this.getTemplateList({
        orgId: value[0]
      })
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.searchFormModel.orgCode = ele.orgCode
          this.searchFormModel.orgId = ele.id
          this.searchFormModel.orgName = ele.name
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    // 点击工具栏
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (
        records.length <= 0 &&
        (item.toolbar.id == 'submitApproval' || item.toolbar.id == 'publish')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'submitApproval') {
        let _idList = records.map((e) => e.id)
        let approveStatus = records.map((e) => e.approveStatus)
        if (approveStatus.some((e) => e === 2 || e === 3))
          return this.$toast({
            content: this.$t('存在审批中或者审批通过的单据，无法提交'),
            type: 'warning'
          })
        this.submitRecord(_idList)
      } else if (item.toolbar.id == 'publish') {
        let _idList = records.map((e) => e.id)
        this.publishFn(_idList)
      } else if (item.toolbar.id == 'ImportCate') {
        this.categoryGradeImportFn()
      } else if (item.toolbar.id == 'ExportCate') {
        this.handleClickDownload()
      }
    },
    // 发布
    publishFn(ids) {
      this.$API.performanceManage
        .gradeResultPublish({
          ids: ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('发布成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
    },
    //Excel导入 - 品类分层分级
    categoryGradeImportFn() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.performanceManage.ResultsCategoryGradeImport,
          downloadTemplateApi: this.$API.performanceManage.ResultsCategoryGradeExport
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //导出
    handleClickDownload() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.performanceManage.gradeResultExportCate(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 提交
    submitRecord(records) {
      this.$router.push({
        path: '/supplier/layered-and-graded-management/grade-result-sum-detail',
        query: {
          id: records
        }
      })
    },
    // 撤回
    revokeScoreDetailsFn(ids) {
      this.$API.performanceManage
        .revokeScoreDetails({
          ids: ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('撤回成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
            this.getList()
          }
        })
    },
    actionBegin(args) {
      // rowData是编辑前的行数据， data是编辑后的数据
      console.log('actionBeginactionBeginactionBeginactionBegin', args)
      const { requestType, rowData, data } = args
      if (requestType === 'save') {
        if (data.achievementAssessScoreType === '1' && !data.score) {
          this.$toast({ content: this.$t('打分类型为得分时“得分”项必填'), type: 'warning' })
          args.cancel = true
        }
        if (data.achievementAssessScoreType === '2' && !data.achievementAssessScoreValue) {
          this.$toast({ content: this.$t('打分类型为评分时得“评分值”项必填'), type: 'warning' })
          args.cancel = true
        }
      } else if (requestType === 'beginEdit') {
        const query = {
          categCode: rowData.achievementAssessType,
          layeredLevel: 1
        }
        this.$API.performanceManage.getAdjustLevelOption(query).then((res) => {
          if (res.code == 200) {
            this.adjustLevelOption = res.data
          }
        })
      }
    },

    actionComplete(args) {
      console.log('flag')
      const { requestType, data } = args
      if (data && data.suggestLevelName !== data.adjustLevelName && !data.adjustCause) {
        data.adjustCause = null
      }
      if (requestType === 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    async save(data) {
      const res = await this.$API.performanceManage.categoryGradeSave(data)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
      }
    },
    // // 行内编辑触发事件
    // parentEditrowdata(e) {
    //   console.log('行内编辑', e)
    //   if (e.text == 'orgName') {
    //     this.editrowdata.orgCode = e.data.dictCode
    //     this.editrowdata.orgName = e.data.dictName
    //   }
    //   console.log('this.editrowdata', this.editrowdata)
    // },
    // 单元格字段点击事件,跳转页面
    handleClickCellTitle(e) {
      if (e.field === 'approveDocNo') {
        this.$router.push({
          path: '/supplier/layered-and-graded-management/grade-result-sum-detail',
          query: {
            id: e.data.id,
            pageFlag: 1, // 1 ：代表当前页面是汇总审批页面进去的，要走另外的接口请求详情数据
            approveStatus: e.data.approveStatus,
            totalIds: e.data.totalIds || []
            // score: JSON.stringify(e.data.templateTypeScores),
            // selectIndex: 2
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  // height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
