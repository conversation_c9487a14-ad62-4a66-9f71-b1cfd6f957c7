<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="docNo" :label="$t('单据编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.docNo"
                :show-clear-button="true"
                :placeholder="$t('请输入单据编码')"
              />
            </mt-form-item>
            <mt-form-item prop="origDocNo" :label="$t('原单据编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.origDocNo"
                :show-clear-button="true"
                :placeholder="$t('请输入原单据编码')"
              />
            </mt-form-item>
            <mt-form-item prop="categCode" :label="$t('类别')" label-style="top">
              <mt-select
                v-model="searchFormModel.categCode"
                css-class="rule-element"
                :data-source="categoryList"
                :fields="{ text: 'categName', value: 'categCode' }"
                :show-clear-button="true"
                :placeholder="$t('请选择类别')"
              />
            </mt-form-item>
            <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
              <mt-DropDownTree
                v-if="orgList.dataSource.length > 0"
                v-model="orgIdArr"
                :placeholder="$t('请选择组织')"
                :popup-height="500"
                :fields="orgList"
                :allow-filtering="true"
                filter-type="Contains"
                @change="handleOrgChange"
                id="baseTreeSelect"
              />
              <mt-select
                v-else
                v-model="searchFormModel.orgId"
                css-class="rule-element"
                :data-source="[]"
                :show-clear-button="true"
                :placeholder="$t('请选择组织')"
              />
            </mt-form-item>
            <mt-form-item prop="ruleName" :label="$t('规则名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.ruleName"
                :show-clear-button="true"
                :placeholder="$t('请输入规则名称')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                css-class="rule-element"
                :data-source="statusList"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="effectiveTime" :label="$t('生效时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.effectiveTime"
                :allow-edit="false"
                @change="(e) => handleDateTimeChange(e, 'EffectiveTime')"
                :placeholder="$t('请选择生效时间')"
              />
            </mt-form-item>
            <mt-form-item prop="expireTime" :label="$t('失效时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.expireTime"
                @change="(e) => handleDateTimeChange(e, 'ExpireTime')"
                :placeholder="$t('请选择失效时间')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                @change="(e) => handleDateTimeChange(e, 'CreateDate')"
                :placeholder="$t('请选择创建时间')"
              />
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
              <mt-input
                v-model="searchFormModel.updateUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入最后更新人')"
              />
            </mt-form-item>
            <mt-form-item prop="modifyDate" :label="$t('最后更新时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.modifyDate"
                @change="(e) => handleDateTimeChange(e, 'ModifyDate')"
                :placeholder="$t('请选择最后更新时间')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig, statusList } from './config'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      orgIdArr: [],
      categoryList: [],
      orgList: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      statusList,
      pageConfig
    }
  },
  computed: {},
  created() {
    this.getSelectList()
  },
  methods: {
    // 获取下拉列表值（类别、组织）
    async getSelectList() {
      this.getCategoryList()
      this.getOrgList()
    },
    // 获取类别列表
    getCategoryList() {
      this.$API.performanceManage.getCategoryList().then((res) => {
        if (res.code === 200) {
          this.categoryList = res.data
        }
      })
    },

    // 获取组织列表
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgList, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    handleOrgChange(item) {
      if (item.value?.length) {
        this.matchItem(this.orgList.dataSource, item['value'][0])
      } else {
        this.orgIdArr = []
        // 对组织列表进行置空再赋值，清除掉选中数据，解决清空后不能继续选择上次选中数据项问题
        const dataSource = [...this.orgList.dataSource]
        this.$set(this.orgList, 'dataSource', [])
        setTimeout(() => {
          this.$set(this.orgList, 'dataSource', dataSource)
        }, 10)
        this.searchFormModel.orgId = null
        this.searchFormModel.orgCode = null
        this.searchFormModel.orgName = null
      }
    },
    // 递归取组织信息
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          const { orgCode, orgName } = ele
          this.searchFormModel.orgId = id
          this.searchFormModel.orgCode = orgCode
          this.searchFormModel.orgName = orgName
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel['start' + field] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 点击工具栏
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      if (
        selectedRecords.length <= 0 &&
        ['Delete', 'Effective', 'Expire', 'Create'].includes(toolbar.id)
      ) {
        this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        return
      }
      switch (toolbar.id) {
        case 'Add':
          this.handleAdd()
          break
        case 'Delete':
        case 'Effective':
        case 'Expire':
          this.handleDeleteOrEffectiveOrExpire(selectedRecords, toolbar.id.toLowerCase())
          break
        case 'Create':
          if (selectedRecords.length > 1) {
            this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
            return
          }
          if (selectedRecords[0].status !== 1) {
            this.$toast({ content: this.$t('仅可操作【有效】状态单据'), type: 'warning' })
            return
          }
          this.handleCreateNewVersion(selectedRecords[0].id)
          break
        case 'Download':
          this.handleExport()
          break
        default:
          break
      }
    },
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field == 'docNo') {
        this.handleEdit(data.id)
      } else if (field == 'origDocNo') {
        // this.handleEdit(data.id)
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        path: '/supplier/layered-and-graded-management/rule-configuration-detail',
        query: {
          action: 'add',
          ruleId: ''
        }
      })
    },
    // 编辑
    handleEdit(id) {
      this.$router.push({
        path: '/supplier/layered-and-graded-management/rule-configuration-detail',
        query: {
          action: 'edit',
          ruleId: id
        }
      })
    },
    // 删除、生效、失效
    handleDeleteOrEffectiveOrExpire(list, type) {
      const ids = []
      list.forEach((item) => {
        ids.push(item.id)
      })
      const titleMap = {
        delete: this.$t('删除'),
        effective: this.$t('生效'),
        expire: this.$t('失效')
      }
      const index = list.findIndex((item) => item.status !== 0)
      if (type !== 'expire' && index > -1) {
        this.$toast({
          content: this.$t('仅可操作【草稿】状态单据！'),
          type: 'warning'
        })
        return
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${titleMap[type]}数据？`)
        },
        success: () => {
          this.$loading()
          this.$API.performanceManage[type + 'LayerAndGradeConfigData']({ ids })
            .then(() => {
              this.$toast({
                content: this.$t(`${titleMap[type]}成功！`),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    // 创建新版本
    handleCreateNewVersion(id) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认创建新版本？`)
        },
        success: () => {
          this.$router.push({
            path: '/supplier/layered-and-graded-management/rule-configuration-detail',
            query: {
              action: 'create',
              ruleId: id
            }
          })
        }
      })
    },
    // 导出
    handleExport() {
      const page = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.pageSettings
      const params = {
        ...this.searchFormModel,
        page: {
          current: page.currentPage,
          size: page.pageSize
        }
      }
      this.$loading()
      this.$API.performanceManage
        .exportLayerAndGradeConfigData(params)
        .then((res) => {
          const blob = new Blob([res.data], { type: 'application/x-msdownload' })
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = this.$t('分层分级规则配置.xlsx')
          a.click()
          window.URL.revokeObjectURL(url)
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
