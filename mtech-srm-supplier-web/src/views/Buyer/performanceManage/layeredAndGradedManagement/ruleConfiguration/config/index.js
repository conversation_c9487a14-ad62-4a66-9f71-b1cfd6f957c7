//现场评审得分设置Tab
import { i18n } from '@/main.js'

// 状态值集
export const statusList = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('有效'), value: 1 },
  { text: i18n.t('失效'), value: 2 }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'docNo',
    headerText: i18n.t('单据编码'),
    width: 220,
    cssClass: 'field-content'
  },
  {
    field: 'origDocNo',
    headerText: i18n.t('原单据编码'),
    width: 120,
    cssClass: ''
  },
  {
    field: 'categName',
    headerText: i18n.t('类别'),
    width: 120
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织'),
    width: 150
  },
  {
    field: 'ruleName',
    headerText: i18n.t('规则名称'),
    width: 120
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: 120,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('有效'),
        2: i18n.t('失效')
      }
    }
  },
  {
    field: 'effectiveTime',
    headerText: i18n.t('生效时间'),
    width: 150
  },
  {
    field: 'expireTime',
    headerText: i18n.t('失效时间'),
    width: 150
  },

  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: 120
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建时间'),
    width: 150
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    width: 120
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('最后更新时间'),
    width: 150
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: 120
  }
]
export const pageConfig = [
  {
    gridId: '9ab79cae-acca-4bf6-9c9b-27eba0e4f9bf',
    title: i18n.t('分层分级规则设置'),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          'Add',
          'Delete',
          { id: 'Effective', icon: 'icon_solid_upload', title: i18n.t('生效') },
          { id: 'Expire', icon: 'icon_solid_submit', title: i18n.t('失效') },
          { id: 'Create', icon: 'a-icon_solid_revocation', title: i18n.t('创建新版本') },
          { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      columnData,
      frozenColumns: 1,
      asyncConfig: {
        url: '/analysis/tenant/buyer/category/grade/config/pageQuery'
      }
    }
  }
]
