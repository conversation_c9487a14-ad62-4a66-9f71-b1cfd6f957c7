<template>
  <div style="height: 100%">
    <div class="header-container">
      <div class="btns-wrap">
        <mt-button class="float-right" @click="goback">{{ $t('返回') }}</mt-button>
        <mt-button
          class="float-right"
          :disabled="headerFormModel.status > 0"
          @click="
            () => {
              this.headerFormModel.id && $route.query.action !== 'create'
                ? this.handeleActive()
                : this.handleSave('active')
            }
          "
          >{{ $t('生效') }}</mt-button
        >
        <mt-button
          :disabled="headerFormModel.status > 0"
          class="float-right"
          @click="handleSave('save')"
          >{{ $t('保存') }}</mt-button
        >
      </div>
      <mt-form ref="ruleForm" :model="headerFormModel" :rules="rules">
        <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
          <mt-DropDownTree
            v-if="orgFields.dataSource.length > 0"
            v-model="headerFormModel.orgIdArr"
            :placeholder="$t('请选择组织：')"
            :popup-height="500"
            :fields="orgFields"
            :allow-filtering="true"
            :show-clear-button="false"
            :enabled="headerFormModel.status === 0 && actionType !== 'create'"
            filter-type="Contains"
            id="baseTreeSelect"
            @change="selectOrg"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="ruleName" :label="$t('规则名称')" label-style="top">
          <mt-input v-model="headerFormModel.ruleName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="categCode" :label="$t('类别')" label-style="top">
          <mt-select
            v-model="headerFormModel.categCode"
            css-class="rule-element"
            :data-source="ruletypeMap"
            :fields="{ value: 'categCode', text: 'categName' }"
            :show-clear-button="true"
            :disabled="actionType === 'create'"
            :open-dispatch-change="false"
            @change="changeCategry"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="docNo" :label="$t('单据编码')" label-style="top">
          <mt-input v-model="headerFormModel.docNo" disabled></mt-input>
        </mt-form-item>
        <mt-form-item prop="origDocNo" :label="$t('原单据编码')" label-style="top">
          <mt-input v-model="headerFormModel.origDocNo" disabled></mt-input>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="headerFormModel.status"
            css-class="rule-element"
            :data-source="statusMap"
            :show-clear-button="true"
            disabled
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
          <mt-input v-model="headerFormModel.remark"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
    <div class="table-bar" style="height: 66%">
      <div class="btns-wrap">
        <mt-button class="float-left" @click="handleAdd">{{ $t('新增') }}</mt-button>
        <mt-button class="float-left" @click="handleDelete">{{ $t('删除') }}</mt-button>
      </div>
      <ScTable
        :columns="columns"
        :table-data="tableData"
        height="auto"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="checkboxChangeEvent"
      ></ScTable>
    </div>
  </div>
</template>
<script>
import cloneDeep from 'lodash/cloneDeep'
import ScTable from '@/components/ScTable/src/index.js'
import { relationType, statusMap } from './config/detailConfig.js'
import debounce from 'lodash/debounce'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      headerFormModel: {
        orgIdArr: [],
        status: 0
      },
      tableData: [],
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      ruletypeMap: [],
      layeredLevelMap: [],
      templateMap: [],
      statusMap,
      selectedRow: [],
      rules: {}
    }
  },
  computed: {
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50
        },
        {
          field: 'layeredLevelType',
          title: this.$t('分层分级类型'),
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <vxe-select
                    v-model={row.layeredLevelType}
                    options={this.layeredLevelMap}
                    option-props={{ value: 'layeredLevelType', label: 'layeredLevelTypeName' }}
                    placeholder={this.$t('请选择')}
                    transfer
                    size='mini'
                    onChange={(item) => {
                      this.changeGradeType(item, row)
                    }}></vxe-select>
                </div>
              ]
            }
          }
        },
        {
          field: 'templateType',
          title: this.$t('模板类型'),
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <vxe-select
                    v-model={row.templateType}
                    options={this.templateMap}
                    option-props={{ value: 'templateType', label: 'templateTypeName' }}
                    placeholder={this.$t('请选择')}
                    transfer
                    size='mini'></vxe-select>
                </div>
              ]
            }
          }
        },
        {
          field: 'minScore',
          title: this.$t('得分>='),
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <vxe-input
                    v-model={row.minScore}
                    placeholder='输入0-100数值'
                    type='integer'
                    min='0'
                    max='100'
                    clearable></vxe-input>
                </div>
              ]
            }
          }
        },
        {
          field: 'highestScore',
          title: this.$t('得分<'),
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <vxe-input
                    v-model={row.highestScore}
                    placeholder='输入0-100数值'
                    type='integer'
                    min='0'
                    max='100'
                    clearable></vxe-input>
                </div>
              ]
            }
          }
        },
        {
          field: 'sort',
          title: this.$t('编号')
        },
        {
          field: 'relationAttr',
          title: this.$t('编号关系'),
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <vxe-select
                    v-model={row.relationAttr}
                    options={relationType}
                    placeholder={this.$t('请选择')}
                    disabled={row.isDisabled}
                    clearable
                    transfer
                    size='mini'></vxe-select>
                </div>
              ]
            }
          }
        }
      ]
    },
    actionType() {
      return this.$route.query.action
    },
    ruleId() {
      return this.$route.query.ruleId
    }
  },
  watch: {
    'headerFormModel.categCode': {
      handler(val) {
        if (val) {
          this.queryBasicItems('layeredLevel')
          this.queryBasicItems('templateType')
        }
      },
      immediate: true
    }
  },
  created() {
    this.getCategoryList() // 类别下拉列表
    this.getOrgList() //获取组织下拉数据
    // 查询明细
    if (this.actionType !== 'add') {
      this.getDetail()
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.rules = {
        orgIdArr: [{ required: true, message: this.$t('组织必填') }],
        ruleName: [{ required: true, message: this.$t('规则必填') }],
        categCode: [{ required: true, message: this.$t('类别必填') }]
      }
    })
  },
  methods: {
    async getDetail() {
      if (this.ruleId) {
        const params = {
          ruleId: this.ruleId
        }
        const res = await this.$API.performanceManage.getLayerAndGradeConfigDataDetail(params)
        if (res.code === 200) {
          const { buyerCategoryGradeRuleItemConfigResponseList, ...headerInfo } = res.data
          this.tableData = buyerCategoryGradeRuleItemConfigResponseList || []
          this.setCustomId() // 删除时需要用到customId来查找数据
          this.headerFormModel = headerInfo || {}
          this.headerFormModel.orgIdArr = [headerInfo.orgId]
          // 创建新版本时
          if (this.actionType === 'create') {
            this.headerFormModel.origDocNo = this.headerFormModel.docNo
            this.headerFormModel.docNo = null
            this.headerFormModel.status = 0
          }
          this.setNum()
        }
      }
    },
    setCustomId() {
      this.tableData.forEach((item) => {
        item.customId = Math.floor(Math.random() * 1000000)
      })
    },
    // 获取类别列表
    getCategoryList() {
      this.$API.performanceManage.getCategoryList().then((res) => {
        if (res.code === 200) {
          this.ruletypeMap = res.data
        }
      })
    },
    async queryBasicItems(type) {
      if (!this.headerFormModel.categCode) return
      const params = {
        categCode: this.headerFormModel.categCode,
        layeredLevel: type === 'layeredLevel' ? 1 : null,
        templateType: type === 'templateType' ? 1 : null
      }
      const res = await this.$API.performanceManage.queryBasicItemsByCategCode(params)
      if (res.code === 200) {
        if (type === 'layeredLevel') {
          this.layeredLevelMap = res.data || []
        } else {
          this.templateMap = res.data || []
        }
      }
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          // this.orgArrList = res.data
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    selectOrg(e) {
      // const { value } = e
      this.matchItem(this.orgFields.dataSource, e['value'][0])
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.headerFormModel.orgCode = ele.orgCode
          this.headerFormModel.orgId = ele.id
          this.headerFormModel.orgName = ele.orgName
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    changeCategry(e) {
      this.headerFormModel.categName = e.itemData?.categName
      this.tableData = []
    },
    handleAdd() {
      if (!this.headerFormModel.categCode) {
        this.$toast({
          content: this.$t('请先选择类别'),
          type: 'warning'
        })
        return
      }
      const curLayeredLevelType = this.layeredLevelMap[0]['layeredLevelType']
      const exitSameGirdeArr = this.tableData.filter(
        (item) => item.layeredLevelType === curLayeredLevelType
      )
      exitSameGirdeArr.forEach((item) => {
        item.isDisabled = false
      })
      let sort = 1
      if (Array.isArray(exitSameGirdeArr)) {
        sort = exitSameGirdeArr.length + 1
      }
      const item = {
        customId: Math.floor(Math.random() * 1000000),
        layeredLevelType: this.layeredLevelMap[0]?.layeredLevelType,
        templateType: this.templateMap[0]?.templateType,
        highestScore: null,
        minScore: null,
        sort,
        relationAttr: null,
        // isDisabled: true
        isDisabled: false
      }
      this.tableData.push(item)
    },
    handleDelete() {
      if (!this.selectedRow?.length) {
        this.$toast({
          content: this.$t('请勾选要删除的数据'),
          type: 'warning'
        })
        return
      }
      this.selectedRow.forEach((item) => {
        this.tableData.forEach((el, index) => {
          if (item.customId === el.customId) {
            this.tableData.splice(index, 1)
          }
        })
      })
      this.setNum()
    },
    changeGradeType() {
      this.setNum()
    },
    setNum() {
      const typeArr = new Set(this.tableData.map((el) => el.layeredLevelType))
      const data = cloneDeep(this.tableData)
      this.tableData = []
      typeArr.forEach((el) => {
        const exitSameGirdeArr = data.filter((item) => item.layeredLevelType === el)
        exitSameGirdeArr.forEach((item, index) => {
          item.sort = index + 1
          item.isDisabled = false
          // if (index === exitSameGirdeArr.length - 1) {
          //   item.isDisabled = true
          //   item.relationAttr = null
          // }
        })
        this.tableData = this.tableData.concat(exitSameGirdeArr)
      })
    },
    async handeleActive() {
      const params = {
        ids: [this.headerFormModel.id]
      }
      const res = await this.$API.performanceManage.effectiveLayerAndGradeConfigData(params)
      if (res.code === 200) {
        this.$toast({
          content: this.$t(`生效成功！`),
          type: 'success'
        })
        this.goback()
      }
    },
    getName(val, list, valKey, labelKey) {
      const item = list.find((item) => item[valKey] === val)
      return item ? item[labelKey] : null
    },
    handleSave: debounce(function (type) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.tableData.some((e) => !e.minScore && !e.highestScore)) {
            this.$toast({
              content: this.$t('最高分和最低分不能同时为空'),
              type: 'warning'
            })
            return
          }
          this.tableData.forEach((item) => {
            item.layeredLevelTypeName = this.getName(
              item.layeredLevelType,
              this.layeredLevelMap,
              'layeredLevelType',
              'layeredLevelTypeName'
            )
            item.templateTypeName = this.getName(
              item.templateType,
              this.templateMap,
              'templateType',
              'templateTypeName'
            )
          })
          const params = {
            ...this.headerFormModel,
            itemConfigRequestList: this.tableData
          }
          // 创建新版本时主动清除掉id
          if (this.actionType === 'create') params.id = null
          this.$loading()
          this.$API.performanceManage
            .saveLayerAndGradeConfigData(params)
            .then((res) => {
              this.$hloading()
              if (res.code === 200) {
                if (type === 'save') {
                  this.$toast({
                    content: this.$t(`操作成功！`),
                    type: 'success'
                  })
                }
                if (res.data) {
                  this.headerFormModel.docNo = res.data.docNo
                  this.headerFormModel.id = res.data.id
                }
                // 创建新规则保存后，直接点击生效，提示重复的问题
                if (type === 'save') {
                  this.$router.replace({
                    path: '/supplier/layered-and-graded-management/rule-configuration-detail',
                    query: {
                      action: 'edit',
                      ruleId: res.data.id
                    }
                  })
                }

                // 串行拼接上生效
                if (type === 'active') {
                  this.handeleActive()
                }
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    }, 1000),
    // checkbox
    selectChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 全选
    checkboxChangeEvent(row) {
      this.selectedRow = row.records
    },
    goback() {
      this.$router.push('/supplier/layered-and-graded-management/rule-configuration')
      // this.$router.push({
      //   path: `/supplier/layered-and-graded-management/rule-configuration`,
      //   query: {
      //     refreshKey: new Date().getTime()
      //   }
      // })
    }
  }
}
</script>
<style lang="scss" scoped>
.header-container {
  background: #fff;
  padding: 16px;
  ::v-deep .mt-form {
    width: 100%;
    padding: 5px 10px 0 11px;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, 300px);
    grid-gap: 20px;
  }
}
.header-container .btns-wrap,
.table-bar .btns-wrap {
  height: 32px;
  line-height: 32px;
  margin-right: 0;
  background: #fff;
  /deep/ .mt-button {
    margin-right: 0;
    button {
      background: transparent;
      //border: 1px solid rgba(0, 70, 156, 0.1);
      border-radius: 4px;
      box-shadow: unset;
      padding: 6px 12px 4px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
  }
}
::v-deep .e-btn:disabled,
.e-css.e-btn:disabled {
  color: rgba(0, 0, 0, 0.26) !important;
}
.float-right {
  float: right;
}
.float-left {
  float: left;
}
</style>
