import { i18n } from '@/main.js'

const categoryCols = [
  {
    field: 'indexName',
    headerText: i18n.t('指标名称'),
    allowEditing: false
  },
  {
    field: 'scoreLogicDesc',
    headerText: i18n.t('评分逻辑描述'),
    allowEditing: false
  },
  {
    field: 'dataSource',
    headerText: i18n.t('评分数据来源'),
    width: '220',
    allowEditing: false,
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: {
        1: i18n.t('系统自动'),
        2: i18n.t('手工录入')
      }
    }
  },
  {
    field: 'fullMark',
    headerText: i18n.t('满分'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'score',
    headerText: i18n.t('得分'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'maxDeductPointsUpperLimit',
    headerText: i18n.t('加/扣分上限'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '220',
    allowEditing: false
  }
]

export const pageConfig = () => {
  const config = [
    {
      toolbar: {
        useBaseConfig: false
      },
      useToolTemplate: false,
      grid: {
        columnData: categoryCols,
        height: 'auto',
        dataSource: []
        // asyncConfig: {
        //   url: url
        // }
      }
    }
  ]
  return config
}
