<template>
  <div class="rule-box mt-flex">
    <div class="main-container mt-flex-direction-column" style="width: 100%">
      <div class="operate-bars">
        <div class="operate-bar">
          <span
            class="op-item"
            v-if="$route.query.approveStatus === '1' || $route.query.approveStatus === '4'"
            @click="handleSave"
          >
            {{ $t('保存') }}
          </span>
          <span
            class="op-item"
            v-if="$route.query.approveStatus === '1' || $route.query.approveStatus === '4'"
            @click="handleSubmit"
          >
            {{ $t('提交审批') }}
          </span>
          <span class="op-item" @click="handleback">
            {{ $t('返回') }}
          </span>
        </div>
        <!-- 顶部主要信息 -->
        <div class="main-info">
          <mt-form ref="listForm" :model="listForm" :rules="formRules">
            <mt-form-item prop="orgName" :label="$t('组织')">
              <mt-input v-model="listForm.orgName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="approveDocNo" :label="$t('审批单号')">
              <mt-input
                v-model="listForm.approveDocNo"
                disabled
                :placeholder="$t('提交单据时自动生成')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="docTitle" :label="$t('单据标题')">
              <mt-input
                v-model="listForm.docTitle"
                :disabled="$route.query.approveStatus === '2' || $route.query.approveStatus === '3'"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="evaluateCycleName" :label="$t('评价周期')">
              <mt-input v-model="listForm.evaluateCycleName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="evaluateYear" :label="$t('评价年份')">
              <mt-input v-model="listForm.evaluateYear" disabled></mt-input>
            </mt-form-item>

            <mt-form-item prop="approveStatus" :label="$t('审批状态')">
              <mt-select
                v-model="listForm.approveStatus"
                css-class="rule-element"
                :data-source="approveStatusOptions"
                disabled
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="createUserName" :label="$t('创建人')">
              <mt-input v-model="listForm.createUserName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('描述')">
              <mt-input
                v-model="listForm.remark"
                :disabled="$route.query.approveStatus === '2' || $route.query.approveStatus === '3'"
              ></mt-input>
            </mt-form-item>
            <template v-for="item in prePageScores">
              <mt-form-item
                :key="item.templateType"
                :prop="item.templateType"
                :label="$t(item.templateTypeName)"
              >
                <mt-input v-model="item.score" disabled></mt-input>
              </mt-form-item>
            </template>
          </mt-form>
        </div>
      </div>
      <div class="rule-list">
        <!-- <div>
          <detailInfoList ref="detailInfo" :list="detailList" />
        </div> -->
        <div class="main_class">
          <!-- 上传附件区 -->
          <div class="file_class">
            <span style="font-weight: bold">{{ $t('附件') }}</span>
            <mt-button
              :disabled="$route.query.approveStatus === '2' || $route.query.approveStatus === '3'"
              @click="hImport"
              >{{ $t('点击上传') }}</mt-button
            >
            <template v-for="item in fileList">
              <span class="every_file" :key="item.title" @click="hPreviewFile(item)"
                >{{ item.fileName }}
              </span>
              <mt-button
                icon-css="mt-icons mt-icon-icon_Close_2"
                type="text"
                :key="item.title"
                @click="hDelete(item)"
              ></mt-button>
              <mt-button type="text" :key="item.title" @click="handleDownloadFile(item)"
                >↓下载</mt-button
              >
            </template>
          </div>
          <div v-for="item in infoList" :key="item.title">
            <!-- title区 -->
            <div class="bar_title">
              <span>{{ item.title }}</span>
            </div>

            <!-- 详细数据区 -->
            <div>
              <mt-template-page
                ref="templateRef"
                :template-config="item.pageConfig"
                :hidden-tabs="true"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import { pageConfig, supplierPageConfig } from './coms/config/index'
import { download } from '@/utils/utils'
import debounce from 'lodash/debounce'

export default {
  components: {
    // detailInfoList: () => import('./coms/detailInfoList.vue')
  },
  data() {
    return {
      formRules: {
        docTitle: [
          {
            required: true,
            message: this.$t('请输入单据标题'),
            trigger: 'blur'
          }
        ]
        // remark: [{ required: true, message: this.$t('请输入描述'), trigger: 'blur' }]
      },
      listForm: {
        docTitle: null,
        orgName: null,
        approveDocNo: null,
        evaluateCycleName: null,
        evaluateYear: null,
        createUserName: null,
        remark: null
      },
      orgFields: null,
      selectIndex: 0,
      dataSource: [],
      prePageId: null,
      prePageScores: [],
      detailList: [],
      approveStatusOptions: [
        { text: i18n.t('未审批'), value: 1 },
        { text: i18n.t('审批中'), value: 2 },
        { text: i18n.t('审批通过'), value: 3 },
        { text: i18n.t('审批驳回'), value: 4 }
      ],

      fileList: [],
      infoList: [
        { id: 0, title: this.$t('品类分层分级'), pageConfig: pageConfig(this) },
        { id: 1, title: this.$t('供应商分层分级'), pageConfig: supplierPageConfig(this) }
      ]
    }
  },
  activated() {
    this.prePageId = this.$route.query.id

    // this.prePageScores = JSON.parse(this.$route.query.score)
    console.log('routerrouterrouter', this.$route)
    if (this.$route.query.pageFlag === '0') {
      this.prePageId = Array.isArray(this.prePageId) ? this.prePageId : this.prePageId.split(',')
      this.init()
    } else if (this.$route.query.pageFlag === '1') {
      this.summaryInit()
    }
  },
  methods: {
    // 层级结果tab页面的明细请求数据
    init() {
      const resultSummary = JSON.parse(sessionStorage.getItem('resultData'))
      this.listForm = resultSummary

      this.fileList = resultSummary?.buyerAssessFileAttachmentResponse
        ? resultSummary?.buyerAssessFileAttachmentResponse
        : []
      // pageConfig[0].grid.dataSource = resultSummary?.categoryTotalApproveDetailList
      // supplierPageConfig[0].grid.dataSource = resultSummary?.supplierTotalApproveDetailList
    },
    // 汇总结果页面的明细请求数据
    summaryInit() {
      const param = {
        id: this.prePageId
      }
      this.$API.performanceManage.summaryQueryDetail(param).then((res) => {
        console.log('this.prePageId123', res)
        this.listForm = res.data
        this.fileList = res.data?.buyerAssessFileAttachmentResponse
          ? res.data?.buyerAssessFileAttachmentResponse
          : []
        // pageConfig[0].grid.dataSource = res.data?.categoryTotalApproveDetailList
        // supplierPageConfig[0].grid.dataSource = res.data?.supplierTotalApproveDetailList
      })
    },
    handleSave() {
      this.$refs.listForm.validate((val) => {
        if (val) {
          const fileListArr = []
          this.fileList.forEach((e) => {
            fileListArr.push({
              fileId: e.fileId,
              fileName: e.fileName,
              fileSize: e.fileSize,
              fileType: e.fileType,
              id: e.id,
              url: e.fileUrl
            })
          })
          const params = {
            actionStatus: 'add',
            addBuyerAssessFileAttachment: fileListArr,
            docTitle: this.listForm.docTitle,
            evaluateCycle: this.listForm.evaluateCycle,
            evaluateCycleName: this.listForm.evaluateCycleName,
            evaluateYear: this.listForm.evaluateYear,
            orgCode: this.listForm.orgCode,
            orgId: this.listForm.orgId,
            orgName: this.listForm.orgName,
            remark: this.listForm.remark,
            tenantId: this.listForm.tenantId,
            totalIds: this.listForm.totalIds
          }
          this.$API.performanceManage.saveOrUpdate(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: res.msg || this.$t('保存成功'), type: 'success' })
              this.listForm = res.data
            }
          })
        }
      })
    },
    handleSubmit: debounce(function () {
      this.$refs.listForm.validate((val) => {
        if (val) {
          const fileListArr = []
          this.fileList.forEach((e) => {
            fileListArr.push({
              fileId: e.fileId,
              fileName: e.fileName,
              fileSize: e.fileSize,
              fileType: e.fileType,
              id: e.id,
              url: e.fileUrl
            })
          })
          const params = {
            actionStatus: 'submit',
            addBuyerAssessFileAttachment: fileListArr,
            docTitle: this.listForm.docTitle,
            evaluateCycle: this.listForm.evaluateCycle,
            evaluateCycleName: this.listForm.evaluateCycleName,
            evaluateYear: this.listForm.evaluateYear,
            orgCode: this.listForm.orgCode,
            orgId: this.listForm.orgId,
            orgName: this.listForm.orgName,
            remark: this.listForm.remark,
            tenantId: this.listForm.tenantId,
            totalIds: this.listForm.totalIds
          }
          this.$loading()
          this.$API.performanceManage
            .submitApprove(params)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: res.msg || this.$t('提交成功'), type: 'success' })
                this.$hloading()
                this.$router.push({
                  path: '/supplier/layered-and-graded-management/result-summary-approval'
                })
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    }, 1000),
    handleback() {
      // 0 是tab页面进来的； 1 是汇总审批页面进来的
      // this.$router.push({
      //   path: '/supplier/layered-and-graded-management/result-summary-approval'
      // })
      this.$router.go(-1)
    },
    // 点击上传附件按钮事件
    hImport() {
      this.$dialog({
        modal: () => import('./coms/upload.vue'),
        data: {
          title: this.$t('附件上传')
        },
        success: (res) => {
          this.fileList.push(res)
        }
      })
    },
    // 附件打开预览功能
    preview(item) {
      const params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 附件删除
    hDelete(item) {
      console.log('itemitemitem', item)
      const index = this.fileList.findIndex((file) => file.fileId === item.fileId)
      this.fileList.splice(index, 1)
    },
    // 附件预览打开
    hPreviewFile(item) {
      const params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.fileId
        })
        .then((res) => {
          download({
            fileName: data.fileName,
            blob: res.data
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-width {
  width: 100% !important;
}
/deep/ {
  .mt-drop-down-tree .e-input-group.e-disabled,
  .mt-drop-down-tree .e-input-group.e-disabled .e-input-group-icon.e-icons {
    background: #fafafa !important;
  }
  .mt-select-index {
    float: left;
  }

  .mt-template-page {
    /deep/ .repeat-template {
      padding: 20px;
    }
  }
  .mt-form-item {
    width: calc(33% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-bottom: 20px;
    .label {
      margin-bottom: 6px;
    }
  }
}

.rule-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .main-container {
    flex: 1;
    background: #fafafa;
    padding: 0 20px;

    .operate-bars {
      border-radius: 8px 8px 0 0;
      .main-info {
        background-color: #fff;
        padding: 10px;
      }
      .operate-bar {
        height: 60px;
        display: flex;
        justify-content: end;
        align-items: center;
        color: #4f5b6d;
        .title-left {
          font-weight: 700;
          font-size: 18px;
        }
        .op-item {
          cursor: pointer;
          align-items: center;
          margin-right: 20px;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          color: #00469c;
        }
      }
    }

    .rule-list {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
    }
  }
}
::v-deep .tab-wrap2 {
  margin: 0 0 10px 0;
}
::v-deep .mt-pagertemplate .mt-select-index {
  display: none;
}

::v-deep .toolbar-container {
  height: 0;
  padding: 5px;
}
.file_class {
  padding: 10px;
  background-color: #fafafa;
  margin-bottom: 10px;
  .every_file {
    padding: 0 10px;
    text-decoration: underline;
    cursor: pointer;
  }
  // &:hover .every_file {
  //   color: #6386c1;
  // }
}
::v-deep .e-btn {
  color: #40a2eb;
  margin-left: 10px;
}
.bar_title {
  height: 35px;
  background-color: #f2f2f2;
  border: 1px solid #ede8e8;
  padding: 10px;
  font-weight: bold;
}
.score_sum {
  padding: 10px 0 0 0;
}
</style>
