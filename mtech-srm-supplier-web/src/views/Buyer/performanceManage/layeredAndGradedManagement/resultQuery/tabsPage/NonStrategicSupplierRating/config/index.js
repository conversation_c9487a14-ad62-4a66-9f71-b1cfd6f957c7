import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'
// import bus from '@/utils/bus'

export const perGradeTypeOptions = [
  { text: i18n.t('得分'), value: 1 },
  { text: i18n.t('评分'), value: 2 }
]
export const levelAdjustOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const acceptOptions = [
  { text: i18n.t('是'), value: 'Y' },
  { text: i18n.t('否'), value: 'N' }
]

export const submitStatusOptions = [
  { text: i18n.t('未提交'), value: 0 },
  { text: i18n.t('已提交'), value: 1 }
]

export const approveStatusOptions = [
  { text: i18n.t('未审批'), value: 1 },
  { text: i18n.t('审批中'), value: 2 },
  { text: i18n.t('审批通过'), value: 3 },
  { text: i18n.t('审批驳回'), value: 4 }
]
export const pubStatusOptions = [
  { value: 5, text: i18n.t('未发布') },
  { value: 6, text: i18n.t('已发布') }
]
// 供应商状态
export const supplierStatusOptions = [
  { value: '3', text: i18n.t('新合格') },
  { value: '10', text: i18n.t('合格') }
]

export const perEvaluationOptions = utils.getSupplierDict('PER_EVALUATION') || []
// 获取建议层级枚举项
export const perSuggestLevelOptions = utils.getSupplierDict('STRATEGIC_CATEGORY') || []
// 品类分层分级
export const columnData = (that) => {
  const categoryCols = [
    {
      width: '50',
      type: 'checkbox',
      allowEditing: false
    },
    {
      field: 'evaluateYear',
      headerText: i18n.t('评价年份'),
      width: 100,
      allowEditing: false
    },
    {
      field: 'categCode',
      headerText: i18n.t('品类编码'),
      width: 100,
      allowEditing: false
    },
    {
      field: 'categName',
      headerText: i18n.t('品类名称'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'suggestLevelName',
      headerText: i18n.t('建议层级'),
      width: 100,
      allowEditing: false
    },
    {
      field: 'levelAdjust',
      headerText: i18n.t('是否调整层级'),
      width: 120,
      allowEditing: true,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span>{{ getStatusLabel(data.levelAdjust) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = levelAdjustOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <span>{scoped.levelAdjust === 1 ? i18n.t('是') : i18n.t('否')}</span>
          </div>
        )
      }
    },
    {
      field: 'adjustLevelName',
      headerText: i18n.t('调整层级'),
      width: 100,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span>{{ data.adjustLevelName }}</span>`
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.adjustLevel}
              data-source={that.adjustLevelOption}
              show-clear-button={true}
              placeholder={i18n.t('请选择')}
              fields={{
                text: 'layeredLevelTypeName',
                value: 'layeredLevelType'
              }}
              onChange={(e) => {
                const { layeredLevelType, layeredLevelTypeName } = e.itemData || {}
                // 当建议层级与调整层级不一致时，是否调整层级则为 "是"
                if (layeredLevelTypeName !== scoped.suggestLevelName) {
                  scoped.levelAdjust = 1
                } else {
                  scoped.levelAdjust = 0
                }
                scoped.adjustLevel = layeredLevelType
                scoped.adjustLevelName = layeredLevelTypeName
              }}></mt-select>
          </div>
        )
      },
      searchOptions: {
        elementType: 'select',
        type: 'select', // 下拉选择
        dataSource: that.adjustLevelOption,
        fields: { text: 'dictName', value: 'dictCode' },
        multiple: false,
        operator: 'in',
        placeholder: i18n.t('请选择')
      }
    },
    {
      field: 'adjustCause',
      headerText: i18n.t('调整原因')
    },
    {
      field: 'scoreDtl',
      headerText: i18n.t('得分明细'),
      width: 100,
      allowEditing: false,
      cssClass: 'field-content'
    },
    {
      field: 'acceptInstock',
      headerText: i18n.t('是否有入库记录'),
      width: '140',
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span>{{ getStatusLabel(data.acceptInstock) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = acceptOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <span>{scoped.acceptInstock === 'Y' ? i18n.t('是') : i18n.t('否')}</span>
          </div>
        )
      }
    },
    {
      field: 'orgName',
      headerText: i18n.t('组织'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'evaluateCycleName',
      headerText: i18n.t('评价周期'),
      width: 100,
      allowEditing: false
    },
    {
      field: 'categRespUserName',
      headerText: i18n.t('品类责任人'),
      width: 100,
      allowEditing: false
    },
    {
      field: 'approveStatus',
      headerText: i18n.t('审批状态'),
      width: 100,
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('template-span', {
            template: `
                <span>{{ getStatusLabel(data.approveStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = approveStatusOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
                <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.approveStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = approveStatusOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },
    {
      field: 'publishStatus',
      headerText: i18n.t('发布状态'),
      width: 100,
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.publishStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = pubStatusOptions.filter((j) => j.value === status)
                return label[0]?.text
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.approveStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = pubStatusOptions.filter((j) => j.value === status)
                return label[0]?.text
              }
            }
          })
        }
      }
    },
    {
      field: 'limitCause',
      headerText: i18n.t('限选原因'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'supplierStatusCode',
      headerText: i18n.t('供应商状态'),
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('template-span', {
            template: `
                <span>{{ getStatusLabel(data.supplierStatusCode) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = supplierStatusOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('template-span', {
            template: `
                <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.supplierStatusCode) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = supplierStatusOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建时间'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'updateUserName',
      headerText: i18n.t('最后更新人'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'modifyDate',
      headerText: i18n.t('最后更新时间'),
      width: '120',
      allowEditing: false
    }
  ]
  return categoryCols
}

// const editSettings = {
//   allowEditing: true,
//   mode: 'Normal', // 默认normal模式
//   allowEditOnDblClick: true,
//   showConfirmDialog: false,
//   showDeleteConfirmDialog: false
// }

// export const pageConfig = (that) => {
//   // 品类分层分级
//   const config = [
//     {
//       gridId: 'd2a0ef91-a9df-44dc-b831-3d3b156b20c3',
//       isUseCustomEditor: true,
//       isUseCustomSearch: true,
//       isCustomSearchRules: true,
//       title: i18n.t('品类分层分级'),
//       toolbar: {
//         useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
//         tools: [
//           [
//             { id: 'submitApproval', icon: 'icon_solid_upload', title: i18n.t('提交审批') },
//             { id: 'ImportCate', icon: 'icon_solid_upload', title: i18n.t('调整导入') },
//             { id: 'ExportCate', icon: 'icon_solid_Download', title: i18n.t('导出') },
//             {
//               id: 'exportScoreDetail',
//               icon: 'a-icon_solid_revocation',
//               title: i18n.t('导出得分明细')
//             }
//           ],
//           ['Filter', 'Refresh', 'Setting']
//         ]
//       },
//       useToolTemplate: false,
//       grid: {
//         columnData: columnData(that),
//         allowPaging: false, // 不分页
//         editSettings,
//         height: 400,
//         frozenColumns: 1,
//         dataSource: []
//         // asyncConfig: {
//         //   url: 'analysis/tenant/buyer/assess/layered/level/pageQuery',
//         //   serializeList: (list) => {
//         //     let k = 0
//         //     for (let j = 0; j < list.length; j++) {
//         //       for (let i = 0; i < list[j].templateTypeScores.length; i++) {
//         //         const gridCofig = {
//         //           field: list[0].templateTypeScores[i].templateType
//         //             ? list[0].templateTypeScores[i].templateType
//         //             : 'null',
//         //           headerText: i18n.t(list[0].templateTypeScores[i].templateTypeName),
//         //           width: '220',
//         //           allowEditing: false
//         //         }
//         //         if (!columnData(that).some((item) => item.field === gridCofig.field)) {
//         //           columnData(that).splice(6 + k, 0, gridCofig)
//         //           k += 1
//         //         }
//         //         const templateType = list[j].templateTypeScores[i].templateType
//         //           ? list[j].templateTypeScores[i].templateType
//         //           : 'null'
//         //         list[j][templateType] = list[j].templateTypeScores[i].score
//         //       }
//         //     }
//         //     console.log('请求回来的数据123', list)
//         //     return list
//         //   }
//         // }
//       }
//     }
//   ]
//   return config
// }
