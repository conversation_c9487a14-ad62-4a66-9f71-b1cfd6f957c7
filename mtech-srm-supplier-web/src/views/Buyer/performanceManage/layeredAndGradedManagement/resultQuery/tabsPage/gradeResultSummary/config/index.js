import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'
// import bus from '@/utils/bus'

export const perGradeTypeOptions = [
  { text: i18n.t('得分'), value: 1 },
  { text: i18n.t('评分'), value: 2 }
]
export const levelAdjustOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const submitStatusOptions = [
  { text: i18n.t('未提交'), value: 0 },
  { text: i18n.t('已提交'), value: 1 }
]

export const approveStatusOptions = [
  { text: i18n.t('未审批'), value: 1 },
  { text: i18n.t('审批中'), value: 2 },
  { text: i18n.t('审批通过'), value: 3 },
  { text: i18n.t('审批驳回'), value: 4 }
]
export const pubStatusOptions = [
  { value: 5, text: i18n.t('未发布') },
  { value: 6, text: i18n.t('已发布') }
]

export const perEvaluationOptions = utils.getSupplierDict('PER_EVALUATION') || []
// 获取建议层级枚举项
export const perSuggestLevelOptions = utils.getSupplierDict('STRATEGIC_CATEGORY') || []

const columnData = (that) => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      allowEditing: false
    },
    {
      field: 'orgName',
      headerText: i18n.t('组织'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'evaluateCycleName',
      headerText: i18n.t('评价周期'),
      width: 100,
      allowEditing: false
    },
    {
      field: 'evaluateYear',
      headerText: i18n.t('评价年份'),
      width: 100,
      allowEditing: false
    },
    {
      field: 'categCode',
      headerText: i18n.t('品类编码'),
      width: 100,
      allowEditing: false
    },
    {
      field: 'categName',
      headerText: i18n.t('品类名称'),
      allowEditing: false
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'categRespUserName',
      headerText: i18n.t('品类责任人'),
      allowEditing: false
    },
    {
      field: 'approveStatus',
      headerText: i18n.t('分层分级审批状态'),
      width: '180',
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('template-span', {
            template: `
              <span>{{ getStatusLabel(data.approveStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = approveStatusOptions.filter((j) => j.value === status)
                return label[0]['text']
              }
            }
          })
        }
      }
    },

    {
      field: 'publishStatus',
      headerText: i18n.t('发布状态'),
      width: '100',
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('template-span', {
            template: `
                <span>{{ getStatusLabel(data.publishStatus) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = pubStatusOptions.filter((j) => j.value === status)
                return label[0]?.text
              }
            }
          })
        }
      }
    },
    {
      field: 'categLevel',
      headerText: i18n.t('品类层级'),
      width: '100',
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('template-span', {
            template: `
                <span>{{ getStatusLabel(data.categLevel) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = that.perSuggestLevelOptions.filter((j) => j.value === status)
                return label[0]?.text
              }
            }
          })
        }
      }
    },
    {
      field: 'supplierLevel',
      headerText: i18n.t('供应商层级'),
      width: '100',
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('template-span', {
            template: `
                <span>{{ getStatusLabel(data.supplierLevel) }}</span>`,
            methods: {
              getStatusLabel(status) {
                let label = that.perSupplierLevelOptions.filter((j) => j.value === status)
                return label[0]?.text
              }
            }
          })
        }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      width: '100',
      allowEditing: false
    },

    {
      field: 'createDate',
      headerText: i18n.t('创建时间'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'updateUserName',
      headerText: i18n.t('最后更新人'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'modifyDate',
      headerText: i18n.t('最后更新时间'),
      width: '120',
      allowEditing: false
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      gridId: '9c0ae65d-5885-4ccd-8a21-c2d85d143a45',
      title: i18n.t(''),
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        tools: [
          [
            {
              id: 'submitApproval',
              icon: 'icon_solid_upload',
              title: i18n.t('提交审批'),
              permission: ['O_02_1398']
            },
            {
              id: 'publish',
              icon: 'icon_solid_submit',
              title: i18n.t('发布')
            },
            { id: 'ExportCate', icon: 'icon_solid_Download', title: i18n.t('导出') }
          ],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      grid: {
        columnData: columnData(that),
        // height: 500,
        // frozenColumns: 1,
        asyncConfig: {
          url: '/analysis/tenant/buyer/assess/strategy/result/total/pageQuery'
        }
      }
    }
  ]
  return config
}
