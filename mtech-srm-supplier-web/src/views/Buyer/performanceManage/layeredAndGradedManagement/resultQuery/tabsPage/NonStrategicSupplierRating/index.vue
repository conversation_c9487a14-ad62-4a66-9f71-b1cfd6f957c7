<template>
  <div>
    <div class="full-height">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        :use-tool-template="true"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleCustomReset"
        @handleClickCellTitle="handleClickCellTitle"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
                <mt-DropDownTree
                  v-if="orgFields.dataSource.length > 0"
                  v-model="orgIdArr"
                  :placeholder="$t('请选择组织')"
                  :popup-height="500"
                  :fields="orgFields"
                  :allow-filtering="true"
                  filter-type="Contains"
                  @change="selectOrg"
                  id="baseTreeSelect"
                />
                <mt-select
                  v-else
                  v-model="searchFormModel.orgId"
                  css-class="rule-element"
                  :data-source="[]"
                  :show-clear-button="true"
                  :placeholder="$t('请选择组织')"
                />
              </mt-form-item>

              <mt-form-item prop="evaluateCycle" :label="$t('评价周期')" label-style="top">
                <mt-select
                  v-model="searchFormModel.evaluateCycle"
                  css-class="rule-element"
                  :data-source="perEvaluationOptions"
                  :show-clear-button="true"
                  :fields="{ text: 'dictName', value: 'dictCode' }"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="evaluateYear" :label="$t('评价年份')" label-style="top">
                <mt-input v-model="searchFormModel.evaluateYear"></mt-input>
              </mt-form-item>

              <mt-form-item prop="categCode" :label="$t('品类名称')" label-style="top">
                <RemoteAutocomplete
                  v-model="searchFormModel.categCode"
                  url="/masterDataManagement/tenant/category/paged-query"
                  multiple
                  :placeholder="$t('请选择品类')"
                  :fields="{ text: 'categoryName', value: 'categoryCode' }"
                  :search-fields="supplierSearchFields"
                ></RemoteAutocomplete>
              </mt-form-item>
              <mt-form-item prop="supplierCode" :label="$t('供应商名称')" label-style="top">
                <RemoteAutocomplete
                  v-model="searchFormModel.supplierCode"
                  url="/masterDataManagement/tenant/supplier/paged-query"
                  multiple
                  :placeholder="$t('请选择供应商')"
                  :fields="{ text: 'supplierName', value: 'supplierCode' }"
                  :search-fields="SearchFields"
                ></RemoteAutocomplete>
              </mt-form-item>
              <mt-form-item prop="suggestLevel" :label="$t('建议层级')" label-style="top">
                <mt-select
                  v-model="searchFormModel.suggestLevel"
                  css-class="rule-element"
                  :data-source="perSuggestLevelOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="adjustLevel" :label="$t('调整层级')" label-style="top">
                <mt-select
                  v-model="searchFormModel.adjustLevel"
                  css-class="rule-element"
                  :data-source="perSuggestLevelOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>

              <mt-form-item prop="adjustCause" :label="$t('调整原因')" label-style="top">
                <mt-input v-model="searchFormModel.adjustCause"></mt-input>
              </mt-form-item>
              <mt-form-item prop="levelAdjust" :label="$t('是否层级调整')" label-style="top">
                <mt-select
                  v-model="searchFormModel.levelAdjust"
                  css-class="rule-element"
                  :data-source="levelAdjustOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="acceptInstock" :label="$t('是否有入库记录')" label-style="top">
                <mt-select
                  v-model="searchFormModel.acceptInstock"
                  css-class="rule-element"
                  :data-source="acceptOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="approveStatus" :label="$t('审批状态')" label-style="top">
                <mt-select
                  v-model="searchFormModel.approveStatus"
                  css-class="rule-element"
                  :data-source="approveStatusOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="publishStatus" :label="$t('发布状态')" label-style="top">
                <mt-select
                  v-model="searchFormModel.publishStatus"
                  css-class="rule-element"
                  :data-source="pubStatusOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>

              <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.createDate"
                  :placeholder="$t('请选择创建时间')"
                  @change="(e) => handleDateTimeChange(e, 'CreateDate')"
                ></mt-date-range-picker>
              </mt-form-item>
              <!-- <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
                <mt-input v-model="searchFormModel.createUserName"></mt-input>
              </mt-form-item> -->
              <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
                <mt-input v-model="searchFormModel.updateUserName"></mt-input>
              </mt-form-item>
              <mt-form-item prop="modifyDate" :label="$t('最后更新时间')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.modifyDate"
                  :placeholder="$t('请选择最后更新时间')"
                  @change="(e) => handleDateTimeChange(e, 'ModifyDate')"
                ></mt-date-range-picker>
              </mt-form-item>
              <mt-form-item prop="categRespUserName" :label="$t('品类责任人')" label-style="top">
                <mt-input v-model="searchFormModel.categRespUserName"></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import {
  columnData,
  perGradeTypeOptions,
  perEvaluationOptions,
  submitStatusOptions,
  approveStatusOptions,
  levelAdjustOptions,
  acceptOptions,
  pubStatusOptions
} from './config/index.js'
import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils.js'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import debounce from 'lodash/debounce'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    search: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      supplierSearchFields: ['categoryCode', 'categoryName'],
      SearchFields: ['supplierCode', 'supplierName'],
      orgIdArr: [],
      templateListArrList: [], // 绩效模板
      searchFormModel: {},
      // 得分是否为空
      scoreNullOptions: [
        { text: this.$t('是'), value: true },
        { text: this.$t('否'), value: false }
      ],
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      //分页数据
      forecastPageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100, 200]
      },
      forecastPageCurrent: 1, //  表格 当前页码
      pageConfig: [
        {
          // gridId: 'd2a0ef91-a9df-44dc-b831-3d3b156b20c3',
          isUseCustomEditor: true,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          title: this.$t('非战略供应商评级'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'submitApproval',
                  icon: 'icon_solid_upload',
                  title: this.$t('提交审批'),
                  permission: ['O_02_1441']
                },
                { id: 'ImportCate', icon: 'icon_solid_upload', title: this.$t('调整导入') },
                { id: 'nonStrategicPublish', icon: 'publish', title: this.$t('发布') },
                { id: 'ExportCate', icon: 'icon_solid_Download', title: this.$t('导出') },
                {
                  id: 'exportScoreDetail',
                  icon: 'a-icon_solid_revocation',
                  title: this.$t('导出得分明细')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: columnData(this),
            allowPaging: false, // 不分页
            editSettings: {
              allowEditing: true,
              mode: 'Normal', // 默认normal模式
              allowEditOnDblClick: true,
              showConfirmDialog: false,
              showDeleteConfirmDialog: false
            },
            // height: 400,
            frozenColumns: 1,
            dataSource: []
          }
        }
      ],
      isLoading: false,
      perGradeTypeOptions,
      perEvaluationOptions,
      perSuggestLevelOptions: [],
      submitStatusOptions,
      pubStatusOptions,
      approveStatusOptions,
      levelAdjustOptions,
      acceptOptions,
      editrowdata: {},
      adjustLevelOption: [], //层级调整下拉值集
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'performanceManage',
        templateUrl: 'ResultsCategoryGradeExport', // 下载模板接口方法名
        uploadUrl: 'ResultsCategoryGradeImport', // 上传接口方法名
        file: 'excel' //后端接收参数名
      },
      resHeaderText: []
    }
  },

  created() {
    this.getOrgList() //获取组织下拉数据
    this.getSuggestLevelOptions() //获取建议层级数据
  },
  mounted() {
    delete this.search.selectIndex
    for (let key in this.search) {
      this.searchFormModel[key] = this.search[key]
    }

    this.init()
  },
  activated() {
    this.getOrgList()
  },
  deactivated() {
    this.orgFields.dataSource = []
  },
  computed: {},
  methods: {
    // 接收数据,处理表头
    async init(currentPage) {
      this.pageConfig[0].grid.dataSource = []
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: currentPage ? currentPage : 1
        },
        ...this.searchFormModel
      }
      let res = await this.$API.performanceManage.getNonStrategicForm(params)
      if (res?.code == 200) {
        const total = res?.data?.total || 0
        this.forecastPageSettings.totalPages = Math.ceil(
          Number(total) / this.forecastPageSettings.pageSize
        )
        this.$set(this.forecastPageSettings, 'totalRecordsCount', Number(total))

        let k = 0
        for (let j = 0; res.data.records && j < res.data.records.length; j++) {
          res.data.records[j].scoreDtl = this.$t('明细')
          for (
            let i = 0;
            res.data.records[j].templateTypeScores &&
            i < res.data.records[j].templateTypeScores.length;
            i++
          ) {
            const gridCofig = {
              field: res.data.records[0].templateTypeScores[i].templateType
                ? res.data.records[0].templateTypeScores[i].templateType
                : 'null',
              headerText: this.$t(res.data.records[0].templateTypeScores[i].templateTypeName),
              width: '160',
              allowEditing: false
            }
            if (
              !this.pageConfig[0].grid.columnData.some((item) => item.field === gridCofig.field)
            ) {
              this.pageConfig[0].grid.columnData.splice(10 + k, 0, gridCofig)
              k += 1
            }

            const templateType = res.data.records[j].templateTypeScores[i].templateType
              ? res.data.records[j].templateTypeScores[i].templateType
              : 'null'
            res.data.records[j][templateType] = res.data.records[j].templateTypeScores[i].score
          }
        }
        this.$nextTick(() => {
          this.pageConfig[0].grid.dataSource = res.data.records
        })
      }
    },
    // 分页 页码
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.init(this.forecastPageCurrent)
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.init()
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        console.log('e.startDate', e.startDate)
        this.searchFormModel['start' + field] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    // 重置
    handleCustomReset() {
      this.templateListArrList = []
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 选择绩效模板
    selectTemplate(e) {
      //清空供应商选中和品类选中
      let { itemData } = e
      if (itemData) {
        this.searchFormModel.templateId = itemData.id //模板id
        this.searchFormModel.templateCode = itemData.templateCode
      }
    },
    //获取绩效模板
    getTemplateList(para) {
      let params = {
        page: { current: 1, size: 9999 }
      }
      params = {
        ...params,
        ...para
      }
      this.$API.performanceManage.templateListQuery(params).then((result) => {
        this.templateListArrList = result.data
      })
    },
    // 获取建议层级数据
    getSuggestLevelOptions() {
      let params = {
        categCode: 'NOT_STRATEGIC_SUPPLIER',
        layeredLevel: 1
      }
      this.$API.performanceManage.getSuggestLevelOptions(params).then((res) => {
        if (res.code === 200) {
          this.perSuggestLevelOptions = res.data.map((item) => ({
            text: item.layeredLevelTypeName,
            value: item.layeredLevelType
          }))
        }
      })
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    selectOrg(e) {
      const { value } = e
      if (e.value.length === 0) {
        this.searchFormModel.orgCode = null
        this.searchFormModel.orgId = null
      }
      this.matchItem(this.orgFields.dataSource, e['value'][0])

      this.getTemplateList({
        orgId: value[0]
      })
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.searchFormModel.orgCode = ele.orgCode
          this.searchFormModel.orgId = ele.id
          this.searchFormModel.orgName = ele.name
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    // 点击工具栏
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (
        records.length <= 0 &&
        (item.toolbar.id == 'submitApproval' || item.toolbar.id == 'nonStrategicPublish')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'submitApproval') {
        let _idList = records.map((e) => e.id)
        this.submitRecord(_idList)
      } else if (item.toolbar.id == 'nonStrategicPublish') {
        let _idList = records.map((e) => e.id)
        this.nonStrategicPublishFn(_idList)
      } else if (item.toolbar.id == 'ImportCate') {
        this.nonStrategicImportFn()
      } else if (item.toolbar.id == 'ExportCate') {
        this.handleClickDownload()
      } else if (item.toolbar.id == 'filterDataByLocal') {
        this.init()
      } else if (item.toolbar.id == 'resetDataByLocal') {
        this.init()
      } else if (item.toolbar.id == 'refreshDataByLocal') {
        this.init()
      } else if (item.toolbar.id == 'exportScoreDetail') {
        this.handleClickDownloadDetail()
      }
    },
    //Excel导入 -
    nonStrategicImportFn() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.performanceManage.noStrategyResultImport,
          downloadTemplateApi: this.$API.performanceManage.nonStrategicExportCate,
          downloadTemplateParams: this.searchFormModel
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //导出
    handleClickDownload() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.performanceManage.nonStrategicExportCate(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 导出明细
    handleClickDownloadDetail() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.performanceManage.nonStrategicExportDetail(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 提交
    submitRecord: debounce(function (ids) {
      this.$loading()
      this.$API.performanceManage
        .nonStrategicSubmit({
          ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          this.getList()
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
    }, 1000),
    // 发布
    nonStrategicPublishFn(ids) {
      this.$API.performanceManage
        .submitNonStrategic({
          ids: ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('发布成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
            this.getList()
          }
        })
    },
    actionBegin(args) {
      // rowData是编辑前的行数据， data是编辑后的数据
      console.log('actionBeginactionBeginactionBeginactionBegin', args)
      const { requestType, rowData, data } = args
      if (requestType === 'save') {
        if (data.achievementAssessScoreType === '1' && !data.score) {
          this.$toast({ content: this.$t('打分类型为得分时“得分”项必填'), type: 'warning' })
          args.cancel = true
        }
        if (data.achievementAssessScoreType === '2' && !data.achievementAssessScoreValue) {
          this.$toast({ content: this.$t('打分类型为评分时得“评分值”项必填'), type: 'warning' })
          args.cancel = true
        }
      } else if (requestType === 'beginEdit') {
        const query = {
          categCode: rowData.achievementAssessType,
          layeredLevel: 1
        }
        this.$API.performanceManage.getAdjustLevelOption(query).then((res) => {
          if (res.code == 200) {
            this.adjustLevelOption = res.data
          }
        })
      }
    },

    actionComplete(args) {
      console.log('flag')
      const { requestType, data } = args
      if (data && data.suggestLevelName !== data.adjustLevelName && !data.adjustCause) {
        data.adjustCause = null
      }
      if (requestType === 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    async save(data) {
      const res = await this.$API.performanceManage.nonStrategicSave(data)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.init(this.forecastPageCurrent)
      }
    },
    // 行内编辑触发事件
    parentEditrowdata(e) {
      if (e.text == 'orgName') {
        this.editrowdata.orgCode = e.data.dictCode
        this.editrowdata.orgName = e.data.dictName
      }
      console.log('this.editrowdata', this.editrowdata)
    },
    // 单元格字段点击事件,跳转页面
    handleClickCellTitle(e) {
      if (e.field === 'scoreDtl') {
        this.$router.push({
          path: '/supplier/layered-and-graded-management/result-query-detail',
          query: {
            id: e.data.id,
            score: JSON.stringify(e.data.templateTypeScores),
            selectIndex: 2
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
//底部mt-page相关样式
.mt-pagertemplate {
  margin: 0;
  padding: 10px 0;
  box-sizing: border-box;
  border-top: 1px solid #e8e8e8;
}
</style>
