<template>
  <div>
    <mt-tabs
      :e-tab="false"
      overflow-mode="Popup"
      :data-source="renderSource"
      :selected-item="selectIndex"
      v-permission="permissionObj"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div v-if="selectIndex == indexObj['T_02_0174']">
      <categoryGrade :search="$route.query" />
    </div>
    <div v-if="selectIndex == indexObj['T_02_0175']">
      <strategicSupplierScreening :search="$route.query" />
    </div>
    <div v-if="selectIndex == indexObj['T_02_0176']">
      <nonStrategicSupplierRating :search="$route.query" />
    </div>
    <div v-if="selectIndex == indexObj['T_02_0177']">
      <gradeResultSummary :search="$route.query" />
    </div>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
import axios from 'axios'

export default {
  components: {
    categoryGrade: () => import('./tabsPage/categoryGrade/index.vue'),
    strategicSupplierScreening: () => import('./tabsPage/strategicSupplierScreening/index.vue'),
    nonStrategicSupplierRating: () => import('./tabsPage/NonStrategicSupplierRating/index.vue'),
    gradeResultSummary: () => import('./tabsPage/gradeResultSummary/index.vue')
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      selectIndex: 0,
      indexObj: {},
      // dataSource: [
      //   { title: this.$t('品类分层分级'), 'data-permission': 'categoryGradePermission' },
      //   { title: this.$t('战略供应商筛选'), 'data-permission': 'strategicSupplierPermission' },
      //   { title: this.$t('非战略供应商评级'), 'data-permission': 'nonStrategicSupplierPermission' },
      //   { title: this.$t('层级结果汇总'), 'data-permission': 'gradeResultSummaryPermission' }
      // ],
      renderSource: [],
      dataSource: [
        {
          title: this.$t('品类分层分级'),
          permission: 'T_02_0174' // 权限字段
          // disabled: false
        },
        {
          title: this.$t('战略供应商筛选'),
          permission: 'T_02_0175' // 权限字段
          // disabled: false
        },
        {
          title: this.$t('非战略供应商评级'),
          permission: 'T_02_0176' // 权限字段
          // disabled: false
        },
        {
          title: this.$t('层级结果汇总'),
          permission: 'T_02_0177' // 权限字段
          // disabled: false
        }
      ],
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'categoryGradePermission', permissionCode: 'T_02_0174' },
          { dataPermission: 'strategicSupplierPermission', permissionCode: 'T_02_0175' },
          { dataPermission: 'nonStrategicSupplierPermission', permissionCode: 'T_02_0176' },
          { dataPermission: 'gradeResultSummaryPermission', permissionCode: 'T_02_0177' }
        ]
      }
    }
  },
  mounted() {
    console.log('层级结果汇总层级结果汇总', this.$route.query)
    this.handleRouter()
    this.getPermissionList()
  },
  methods: {
    // 切换Tab
    handleSelectTab(e) {
      this.selectIndex = e
    },
    // 路由跳转
    handleRouter() {
      // console.log('routerrouterrouter', this.$route)
      if (this.$route.query.selectIndex != null) {
        this.selectIndex = this.$route.query.selectIndex
      }
    },
    // 获取权限列表
    getPermissionList() {
      console.log('handleSelectTab123')

      axios
        .get(
          `/api/iam/tenant/user-permission-query/user-element-permissions?appCode=${utils.getAppCode()}`,
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' } }
        )
        .then((result) => {
          let allPermissionList = []
          let renderSource = []
          let selectedItem = -1 // 默认已选得tab index

          !!result.data &&
            result.data.data.forEach((item) => {
              allPermissionList.push(item.permissionCode)
            })
          let objIndex = 0
          this.dataSource.forEach((item) => {
            if (Object.prototype.hasOwnProperty.call(item, 'permission')) {
              console.log('123')
              let disabled =
                !item.permission ||
                (allPermissionList.length > 0 && !allPermissionList.includes(item.permission))
              if (!disabled) {
                console.log('sdsdsdsdsd', item)
                selectedItem = 0 // 寻找默认tab
                this.indexObj[item.permission] = objIndex
                objIndex += 1
              }
              // 非禁用得推入数据 禁用的将删除
              !disabled &&
                renderSource.push({
                  ...item,
                  disabled
                })
            } else {
              console.log('45666666')
              if (selectedItem === -1) {
                selectedItem = 0
              }
              renderSource.push({
                ...item,
                disabled: false
              })
            }
          })
          this.selectIndex = selectedItem
          this.renderSource = renderSource
          console.log('handleSelectTabhandleSelectTab', this.indexObj, selectedItem)
        })
    }
  }
}
</script>
