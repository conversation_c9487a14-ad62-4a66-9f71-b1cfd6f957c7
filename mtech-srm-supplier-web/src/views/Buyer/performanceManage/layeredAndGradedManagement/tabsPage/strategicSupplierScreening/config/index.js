import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'
export const perGradeTypeOptions = [
  { text: i18n.t('得分'), value: 1 },
  { text: i18n.t('评分'), value: 2 }
]

export const submitStatusOptions = [
  { text: i18n.t('未提交'), value: 0 },
  { text: i18n.t('已提交'), value: 1 }
]

export const approveStatusOptions = [
  { text: i18n.t('未审批'), value: 1 },
  { text: i18n.t('审批中'), value: 2 },
  { text: i18n.t('审批通过'), value: 3 },
  { text: i18n.t('审批驳回'), value: 4 }
]

export const perEvaluationOptions = utils.getSupplierDict('PER_EVALUATION') || []
// 品类分层分级
const categoryCols = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织'),
    allowEditing: false
  },
  {
    field: 'evaluateCycleName',
    headerText: i18n.t('评价周期'),
    allowEditing: false
  },
  {
    field: 'evaluateYear',
    headerText: i18n.t('评价年份'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标名称'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'indexName12',
    headerText: i18n.t('供应商编码'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'indexName23',
    headerText: i18n.t('供应商名称'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'categCode',
    headerText: i18n.t('品类编码'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'categName',
    headerText: i18n.t('品类名称'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'templateName',
    headerText: i18n.t('绩效模板'),
    allowEditing: false
  },
  {
    field: 'scoreStandard',
    headerText: i18n.t('评分标准'),
    allowEditing: false
  },
  {
    field: 'achievementAssessScoreType',
    headerText: i18n.t('打分类型'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.achievementAssessScoreType) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = perGradeTypeOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.achievementAssessScoreType) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = perGradeTypeOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    }
  },
  {
    field: 'score',
    headerText: i18n.t('得分'),
    allowEditing: false,
    ignore: true
  },
  {
    field: 'achievementAssessScoreValue',
    headerText: i18n.t('评分值'),
    allowEditing: false,
    ignore: true
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    allowEditing: false
  },
  {
    field: 'scoreUserName',
    headerText: i18n.t('评分人'),
    allowEditing: false
  },
  {
    field: 'fullMark',
    headerText: i18n.t('满分'),
    allowEditing: false
  },
  {
    field: 'maxDeductPointsUpperLimit',
    headerText: i18n.t('加/扣分上限'),
    allowEditing: false
  },
  {
    field: 'submitStatus',
    headerText: i18n.t('提交状态'),
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.submitStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = submitStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.submitStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = submitStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    allowEditing: false
  },
  {
    field: 'approveStatus',
    headerText: i18n.t('审批状态'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.approveStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = approveStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.approveStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = approveStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建时间'),
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    allowEditing: false
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('最后更新时间'),
    allowEditing: false
  }
]

const editSettings = {
  allowEditing: false,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false
}

export const pageConfig = [
  // 品类分层分级
  {
    gridId: '9c0ae65d-5885-4ccd-8a21-c2d85d143a98',
    title: i18n.t('品类分层分级'),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    isUseCustomEditor: true,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          { id: 'ImportCate', icon: 'icon_solid_upload', title: i18n.t('导入') },
          {
            id: 'Submit',
            icon: 'icon_solid_submit',
            title: i18n.t('提交')
          },
          {
            id: 'Recall',
            icon: 'a-icon_solid_revocation',
            title: i18n.t('撤回')
          },
          { id: 'ExportCate', icon: 'icon_solid_Download', title: i18n.t('导出') }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      columnData: categoryCols,
      editSettings,
      // height: 500,
      frozenColumns: 1,
      asyncConfig: {
        url: '/analysis/tenant/buyer/assess/categoryGrade/pageQuery'
      }
    }
  }
]
