import Vue from 'vue'
import { i18n } from '@/main.js'
import utils from '@/utils/utils'

export const evaluateCycleMap = utils.getSupplierDict('PER_EVALUATION')

export const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织')
  },
  {
    field: 'evaluateCycleName',
    headerText: i18n.t('评价周期')
  },
  {
    field: 'evaluateYear',
    headerText: i18n.t('评价年份'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
                <div class="time-box">
                  {{ data.evaluateYear }}
                </div>`
        })
      }
    }
  },
  {
    field: 'achievementAssessType',
    headerText: i18n.t('类别'),
    valueConverter: {
      type: 'map',
      map: {
        STRATEGIC_CATEGORY: i18n.t('品类分层分级'),
        STRATEGIC_SUPPLIER: i18n.t('战略供应商筛选'),
        NOT_STRATEGIC_SUPPLIER: i18n.t('非战略供应商评级')
      },
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建时间'),
    ignore: true,
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
                <div class="time-box">
                  {{ data.createDate }}
                </div>`
        })
      }
    }
  }
]
