<template>
  <div class="perform-box">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
              <mt-DropDownTree
                v-if="orgList.dataSource.length > 0"
                v-model="orgIdArr"
                :placeholder="$t('请选择组织')"
                :popup-height="500"
                :fields="orgList"
                :allow-filtering="true"
                filter-type="Contains"
                @change="handleOrgChange"
                id="baseTreeSelect"
              />
              <mt-select
                v-else
                v-model="searchFormModel.orgId"
                css-class="rule-element"
                :data-source="[]"
                :show-clear-button="true"
                :placeholder="$t('请选择组织')"
              />
            </mt-form-item>
            <mt-form-item prop="evaluateCycle" :label="$t('评价周期')" label-style="top">
              <mt-select
                v-model="searchFormModel.evaluateCycle"
                css-class="rule-element"
                :data-source="evaluateCycleMap"
                :fields="{ text: 'dictName', value: 'dictCode' }"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
            <mt-form-item prop="evaluateYear" :label="$t('评价年份')" label-style="top">
              <mt-input
                v-model="searchFormModel.evaluateYear"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="categCode" :label="$t('品类编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.categCode"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="categName" :label="$t('品类名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.categName"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierCode"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierName"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="achievementAssessType" :label="$t('类别')" label-style="top">
              <mt-select
                v-model="searchFormModel.achievementAssessType"
                css-class="rule-element"
                :data-source="ruletypeMap"
                :show-clear-button="true"
                :fields="{ value: 'categCode', text: 'categName' }"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                @change="(e) => handleDateTimeChange(e, 'CreateDate')"
                :placeholder="$t('请选择创建时间')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import { columns, evaluateCycleMap } from './config.js'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [
        {
          gridId: '6dee1a20-29ac-4fb8-afb3-f31f0a20a470',
          title: '',
          toolbar: [{ id: 'ExportCate', icon: 'icon_solid_Download', title: i18n.t('导出') }],
          useToolTemplate: false,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            columnData: columns,
            asyncConfig: {
              url: 'analysis/tenant/buyer/assess/supplier/layered/level/calcScope/pageQuery'
            }
          }
        }
      ],

      data: {
        dimensionName: '',
        dimensionType: '',
        remark: '',
        id: undefined
      },
      calcRules: {
        dimensionName: [{ required: true, message: this.$t('请输入维度名称'), trigger: 'blur' }],
        dimensionType: [{ required: true, message: this.$t('请选择QCDSI'), trigger: 'blur' }]
      },
      orgList: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      evaluateCycleMap,
      ruletypeMap: [],
      orgIdArr: []
    }
  },
  created() {
    this.getCategoryList()
    this.getOrgList()
  },
  activated() {
    this.getOrgList()
  },
  deactivated() {
    // console.log('deactivateddeactivated8888')
    this.orgList.dataSource = []
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 获取类别列表
    getCategoryList() {
      this.$API.performanceManage.getCategoryList().then((res) => {
        if (res.code === 200) {
          this.ruletypeMap = res.data
        }
      })
    },
    // 获取组织列表
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgList, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    handleOrgChange(item) {
      if (item.value?.length) {
        this.matchItem(this.orgList.dataSource, item['value'][0])
      } else {
        this.orgIdArr = []
        // 对组织列表进行置空再赋值，清除掉选中数据，解决清空后不能继续选择上次选中数据项问题
        const dataSource = [...this.orgList.dataSource]
        this.$set(this.orgList, 'dataSource', [])
        setTimeout(() => {
          this.$set(this.orgList, 'dataSource', dataSource)
        }, 10)
        this.searchFormModel.orgId = null
        this.searchFormModel.orgCode = null
        this.searchFormModel.orgName = null
      }
    },
    // 递归取组织信息
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          const { orgCode, orgName } = ele
          this.searchFormModel.orgId = id
          this.searchFormModel.orgCode = orgCode
          this.searchFormModel.orgName = orgName
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    // 选择时间
    handleDateTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['startCreateDate'] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['endCreateDate'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['startCreateDate'] = null
        this.searchFormModel['endCreateDate'] = null
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'ExportCate') {
        this.handleClickDownload()
      }
    },
    //导出
    handleClickDownload() {
      let params = {
        page: { current: 1, size: 10000 }
      }
      this.$API.performanceManage
        .downloadCalculationRange(params)
        .then((res) => {
          const blob = new Blob([res.data], { type: 'application/x-msdownload' })
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = this.$t('分层分级计算范围.xlsx')
          a.click()
          window.URL.revokeObjectURL(url)
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
.perform-box {
  width: 100%;
  position: relative;
  height: 100%;
  .red {
    background-color: rgba(237, 86, 51, 1);
  }
  .green {
    background-color: rgba(138, 204, 64, 1);
  }
  .yellow {
    background-color: rgba(237, 161, 51, 1);
  }
  .yellowcolor {
    color: rgba(237, 161, 51, 1) !important;
  }
  .bluecolor {
    color: #6386c1 !important;
  }
  .blurbgcolor {
    background-color: #00469c;
  }
  ::v-deep .now-status {
    padding: 3px 5px;
    color: #6386c1;
    background-color: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
  }
  ::v-deep .now-status-stop {
    padding: 3px 5px;
    color: #9a9a9a;
    background-color: rgba(154, 154, 154, 0.1);
    border-radius: 2px;
  }
  ::v-deep .change-status {
    color: #6386c1;
    margin-top: 5px;
    cursor: pointer;
  }
}
::v-deep .dimension-name {
  width: calc((100% - 24px) / 2);
  display: inline-block;
}
::v-deep .dimension-type {
  width: calc((100% - 24px) / 2);
  margin-left: 24px;
  display: inline-block;
}
</style>
