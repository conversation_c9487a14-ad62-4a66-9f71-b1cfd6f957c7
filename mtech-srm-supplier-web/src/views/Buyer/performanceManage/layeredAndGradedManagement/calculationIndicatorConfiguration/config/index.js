//现场评审得分设置Tab
import { i18n } from '@/main.js'
import Vue from 'vue'

const columnData = (that) => {
  const column = [
    {
      type: 'checkbox',
      width: 50,
      allowEditing: false,
      allowResizing: false,
      customAttributes: {
        class: 'sticky-col-0'
      }
    },
    {
      field: 'autoCalcIndexTypeName',
      headerText: i18n.t('自动计算指标类型'),
      headerTemplate: () => {
        return {
          template: Vue.component('autoCalcIndexTypeTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('自动计算指标类型')}}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.autoCalcIndexType}
              fields={{ text: 'itemName', value: 'itemCode' }}
              dataSource={that.indicatorTypeList}
              allow-filtering={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择自动计算指标类型')}
              onChange={(e) => {
                const val = e.value || ''
                const exitTitem = that.indicatorTypeList.find((item) => item.itemCode === val)
                scoped.autoCalcIndexTypeName = exitTitem ? exitTitem.itemName : i18n.t('未匹配')
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'categName',
      headerText: i18n.t('类别'),
      headerTemplate: () => {
        return {
          template: Vue.component('categoryTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('类别')}}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        if (scoped.categCode) {
          that.getIndicatorNameList(scoped, scoped.categCode)
        }
        return (
          <div>
            <mt-select
              v-model={scoped.categCode}
              fields={{ text: 'itemName', value: 'itemCode' }}
              dataSource={that.categoryList}
              allow-filtering={true}
              clearable
              filter-type='Contains'
              placeholder={i18n.t('请选择类别')}
              onChange={(e) => {
                const val = e.value || ''
                scoped.indexId = null
                scoped.indexName = null
                that.getIndicatorNameList(scoped, val)
                const exitTitem = that.categoryList.find((item) => item.itemCode === val)
                scoped.categName = exitTitem ? exitTitem.itemName : i18n.t('未匹配')
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'indexName',
      headerText: i18n.t('指标名称'),
      headerTemplate: () => {
        return {
          template: Vue.component('indexTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('指标名称')}}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.indexId}
              fields={{ text: 'indexName', value: 'indexId' }}
              dataSource={scoped.templateIndexMap || []}
              allow-filtering={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择指标名称')}
              disabled={!scoped.categCode}
              onChange={(e) => {
                const { indexId, indexName } = e.itemData || {}
                scoped.indexId = indexId
                scoped.indexName = indexName
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      allowEditing: false
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建时间'),
      allowEditing: false
    },
    {
      field: 'updateUserName',
      headerText: i18n.t('最后更新人'),
      allowEditing: false
    },
    {
      field: 'modifyDate',
      headerText: i18n.t('最后更新时间'),
      allowEditing: false
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      gridId: '651c121d-8759-49cc-a435-42825b4171eb',
      title: i18n.t('自动计算指标配置'),
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          ['Add', 'Delete'],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      grid: {
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        // frozenColumns: 1,
        asyncConfig: {
          url: '/analysis/tenant/buyer/category/grade/auto/compute/rule/config/pageQuery'
        }
      }
    }
  ]
  return config
}
