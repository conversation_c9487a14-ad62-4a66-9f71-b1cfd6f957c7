<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item
              prop="autoCalcIndexType"
              :label="$t('自动计算指标类型')"
              label-style="top"
            >
              <mt-select
                v-model="searchFormModel.autoCalcIndexType"
                css-class="rule-element"
                :data-source="indicatorTypeList"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择自动计算指标类型')"
              />
            </mt-form-item>
            <mt-form-item prop="categCode" :label="$t('类别')" label-style="top">
              <mt-select
                v-model="searchFormModel.categCode"
                css-class="rule-element"
                :data-source="categoryList"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择类别')"
                @change="(e) => getIndicatorNameList(null, e.itemData.itemCode)"
              />
            </mt-form-item>
            <mt-form-item prop="indexId" :label="$t('指标名称')" label-style="top">
              <mt-select
                v-model="searchFormModel.indexId"
                css-class="rule-element"
                :data-source="templateIndexMap"
                :fields="{ text: 'indexName', value: 'indexId' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择类别')"
                :disabled="!searchFormModel.categCode"
                @change="handleIndexIdChange"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                @change="(e) => handleDateTimeChange(e, 'CreateDate')"
                :placeholder="$t('请选择创建时间')"
              />
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
              <mt-input
                v-model="searchFormModel.updateUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入最后更新人')"
              />
            </mt-form-item>
            <mt-form-item prop="modifyDate" :label="$t('最后更新时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.modifyDate"
                @change="(e) => handleDateTimeChange(e, 'ModifyDate')"
                :placeholder="$t('请选择最后更新时间')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig } from './config'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [],
      indicatorTypeList: [], // 自动发计算指标类型列表
      categoryList: [], // 类别列表
      templateIndexMap: [] // 指标名称列表
    }
  },
  computed: {},
  created() {
    this.getSelectList()
  },
  methods: {
    // 指标名称下拉变化
    handleIndexIdChange(e) {
      if (!e.itemData) {
        this.searchFormModel.indexName = null
        return
      }
      this.searchFormModel.indexName = e.itemData.indexName
    },
    // 获取下拉列表值（自动计算指标类型、类别）
    getSelectList() {
      // 自动计算指标类型
      const getIndicatorTypeList = this.getDictItems('PER_INDEX_AUT')
      // 类别
      const getCategoryList = this.getDictItems('PER_CLASS')
      Promise.all([getIndicatorTypeList, getCategoryList]).then((result) => {
        this.indicatorTypeList = result[0]
        this.categoryList = result[1]
        this.pageConfig = pageConfig(this)
      })
    },
    // 根据名称获取字典数据
    getDictItems(key) {
      return new Promise((resolve) => {
        this.$API.supplierInvitation
          .getDictCode({
            dictCode: key,
            nameLike: ''
          })
          .then((res) => {
            resolve(res.data)
          })
          .catch(() => {
            resolve([])
          })
      })
    },
    // 获取指标名称列表
    async getIndicatorNameList(scoped, val) {
      const params = {
        achievementAssessType: val
      }
      const res = await this.$API.performanceManage.getIndicatorNameList(params)
      if (res.code === 200) {
        const data = res.data || []
        scoped ? this.$set(scoped, 'templateIndexMap', data) : (this.templateIndexMap = data)
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel['start' + field] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()

      if (toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'Delete') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        this.handleDelete(selectedRecords)
      }
    },
    handleClickCellTitle() {},
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        const validateMap = {
          autoCalcIndexType: {
            value: data.autoCalcIndexType,
            msg: this.$t('请选择自动计算指标类别')
          },
          categCode: {
            value: data.categCode,
            msg: this.$t('请选择类别')
          },
          indexId: {
            value: data.indexId,
            msg: this.$t('请选择指标名称')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.handleSave(data, rowIndex)
      }
    },
    // 保存
    handleSave(rowData, rowIndex = 0) {
      this.$API.performanceManage
        .saveComputedIndicatorConfig(rowData)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          }
        })
        .catch(() => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 删除
    handleDelete(list) {
      const ids = []
      list.forEach((item) => {
        ids.push(item.id)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$loading()
          this.$API.performanceManage
            .deleteComputedIndicatorConfig({ ids })
            .then(() => {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
  /deep/.e-rowcell.sticky-col-0,
  /deep/.e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  /deep/.e-rowcell.sticky-col-1,
  /deep/.e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
}
</style>
