import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'
export const perGradeTypeOptions = [
  { text: i18n.t('得分'), value: 1 },
  { text: i18n.t('评分'), value: 2 }
]

export const submitStatusOptions = [
  { text: i18n.t('未提交'), value: 0 },
  { text: i18n.t('已提交'), value: 1 }
]

export const approveStatusOptions = [
  { text: i18n.t('未审批'), value: 1 },
  { text: i18n.t('审批中'), value: 2 },
  { text: i18n.t('审批通过'), value: 3 },
  { text: i18n.t('审批驳回'), value: 4 }
]
export const indexLineTypeOption = [
  // 指标行类型
  { text: i18n.t('文本'), value: 1 },
  { text: i18n.t('数字'), value: 2 },
  { text: i18n.t('百分比'), value: 3 }
]

export const perEvaluationOptions = utils.getSupplierDict('PER_EVALUATION') || []

// 根据评分值划定区间得到得分值
const getRangeScore = (row, e) => {
  if (!Array.isArray(row.buyerAssessIndexItemDTOList)) return null
  let res = null
  const scoreKey = row.indexItemType === 2 ? 'NumberScore' : 'PercentScore'
  row.buyerAssessIndexItemDTOList.forEach((item) => {
    let minVal = item['min' + scoreKey] || -100
    let maxVal = item['max' + scoreKey] || 10000
    let curVal = Number(e)
    if (minVal <= curVal && curVal < maxVal) {
      if (item.scoreWeight) {
        res = ((e * row?.fullMark) / 100).toFixed(2)
      } else {
        res = item.score
      }
    }
  })
  return res
}

// 品类分层分级
const categoryCols = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'evaluateYear',
    headerText: i18n.t('评价年份'),
    width: '100',
    allowEditing: false
  },
  {
    field: 'categCode',
    headerText: i18n.t('品类编码'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'categName',
    headerText: i18n.t('品类名称'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标名称'),
    allowEditing: false
  },
  {
    field: 'scoreStandard',
    headerText: i18n.t('评分标准'),
    allowEditing: false,
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('scoreStandard', {
          template: `
          <div class="titleStyle">
            <span id="spanSelector" v-html="getBrText(data.scoreStandard)" :title="data.scoreStandard"  style="display: block;height: 2.6em;overflow: hidden;line-height: 1.3em;" />
          </div>`,
          mounted() {},
          methods: {
            getBrText(response) {
              let resText = response.replace(/\n/g, '<br/>')
              return resText
            }
          }
        })
      }
    }
  },
  {
    field: 'achievementAssessScoreType',
    headerText: i18n.t('打分类型'),
    width: '100',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.achievementAssessScoreType) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = perGradeTypeOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.achievementAssessScoreType) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = perGradeTypeOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    }
  },
  {
    field: 'indexItemType',
    headerText: i18n.t('指标行类型'),
    width: '120',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('文本'),
        2: i18n.t('数字'),
        3: i18n.t('百分比')
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('template-span', {
          template: `
            <span>{{ getStatusLabel(data.indexItemType) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = indexLineTypeOption.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    }
  },
  {
    field: 'fullMark',
    headerText: i18n.t('满分'),
    width: '70',
    allowEditing: false
  },
  {
    field: 'score',
    headerText: i18n.t('得分'),
    width: '70',
    editorRender(h, scoped) {
      return (
        <div>
          <mt-input
            v-model={scoped.score}
            disabled={scoped.achievementAssessScoreType === 2}></mt-input>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'achievementAssessScoreValue',
    headerText: i18n.t('评分值'),
    editorRender(h, scoped) {
      return (
        <div>
          <mt-select
            vShow={scoped.indexItemType == 1}
            v-model={scoped.achievementAssessScoreValue}
            allow-filtering={true}
            data-source={scoped.buyerAssessIndexItemDTOList || []}
            show-clear-button={true}
            filter-type='Contains'
            popup-width='300px'
            placeholder={i18n.t('请选择评分值')}
            disabled={scoped.achievementAssessScoreType === 1}
            fields={{
              text: 'description',
              value: 'description'
            }}
            onChange={(e) => {
              // 值为0的时候需要注意
              if (e.itemData.score || e.itemData.score === 0 || e.value == null) {
                scoped.score = e.itemData?.score || 0
                scoped.achievementAssessScoreValue = e.itemData?.description
              } else {
                // 清空
                scoped.score = null
                scoped.achievementAssessScoreValue = null
              }
            }}></mt-select>
          <mt-input-number
            vShow={scoped.indexItemType != 1}
            v-model={scoped.achievementAssessScoreValue}
            disabled={scoped.achievementAssessScoreType === 1}
            show-clear-button
            onInput={(val) => {
              // 通过退格键清空输入框是val为0，可能存在问题, 需要点击清空按钮以做清空
              if (!val && val !== 0) {
                scoped.score = null
                return
              }
              const res = getRangeScore(scoped, val)
              scoped.score = res
            }}></mt-input-number>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '80'
  },
  {
    field: 'scoreLogicDesc',
    headerText: i18n.t('评分逻辑描述'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织'),
    width: '100',
    allowEditing: false
  },
  {
    field: 'evaluateCycleName',
    headerText: i18n.t('评价周期'),
    width: '100',
    allowEditing: false
  },

  {
    field: 'templateName',
    headerText: i18n.t('绩效模板'),
    allowEditing: false
  },
  {
    field: 'submitStatus',
    headerText: i18n.t('提交状态'),
    width: '100',
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.submitStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = submitStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.submitStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = submitStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    allowEditing: false
  },
  {
    field: 'approveStatus',
    headerText: i18n.t('审批状态'),
    width: '100',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.approveStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = approveStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span style = 'color:rgba(0, 0, 0, 0.42);'>{{ getStatusLabel(data.approveStatus) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = approveStatusOptions.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    }
  },

  {
    field: 'scoreUserName',
    headerText: i18n.t('评分人'),
    width: '80',
    allowEditing: false
  },

  {
    field: 'maxDeductPointsUpperLimit',
    headerText: i18n.t('扣分上限'),
    width: '100',
    allowEditing: false
  },

  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '100',
    allowEditing: false
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建时间'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('最后更新时间'),
    width: '120',
    allowEditing: false
  }
]

const editSettings = {
  allowEditing: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false
}

export const pageConfig = [
  {
    gridId: '5a3bd9e8-7bb0-4e98-9dbf-349f30f8d9ec',
    title: i18n.t(''),
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    isUseCustomEditor: true,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          { id: 'ImportCate', icon: 'icon_solid_upload', title: i18n.t('导入') },
          {
            id: 'Submit',
            icon: 'icon_solid_submit',
            title: i18n.t('提交')
          },
          {
            id: 'Recall',
            icon: 'a-icon_solid_revocation',
            title: i18n.t('撤回')
          },
          { id: 'ExportCate', icon: 'icon_solid_Download', title: i18n.t('导出') }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      columnData: categoryCols,
      editSettings,
      // height: 500,
      allowSorting: false,
      frozenColumns: 1,
      asyncConfig: {
        url: 'analysis/tenant/buyer/assess/noStrategy/supplier/pageQuery'
      }
    }
  }
]
