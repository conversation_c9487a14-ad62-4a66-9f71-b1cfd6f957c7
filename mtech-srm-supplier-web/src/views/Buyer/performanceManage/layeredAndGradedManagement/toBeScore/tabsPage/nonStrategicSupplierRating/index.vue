<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="categCode" :label="$t('品类名称')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.categCode"
                url="/masterDataManagement/tenant/category/paged-query"
                multiple
                :placeholder="$t('请选择品类')"
                :fields="{ text: 'categoryName', value: 'categoryCode' }"
                :search-fields="supplierSearchFields"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商名称')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="SearchFields"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="templateId" :label="$t('绩效模板')" label-style="top">
              <mt-select
                :allow-filtering="true"
                :data-source="templateListArrList"
                :show-clear-button="true"
                filter-type="Contains"
                @change="selectTemplate"
                :placeholder="$t('请输入绩效模板')"
                v-model="searchFormModel.templateId"
                :fields="{
                  text: 'templateName',
                  value: 'id'
                }"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="indexName" :label="$t('指标名称')" label-style="top">
              <mt-input v-model="searchFormModel.indexName"></mt-input>
            </mt-form-item>

            <mt-form-item prop="scoreNull" :label="$t('得分是否为空')" label-style="top">
              <mt-select
                v-model="searchFormModel.scoreNull"
                css-class="rule-element"
                :data-source="scoreNullOptions"
                :show-clear-button="true"
                @change="selectScoreNull"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="submitStatus" :label="$t('提交状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.submitStatus"
                css-class="rule-element"
                :data-source="submitStatusOptions"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
              <mt-DropDownTree
                v-if="orgFields.dataSource.length > 0"
                v-model="orgIdArr"
                :placeholder="$t('请选择组织')"
                :popup-height="500"
                :fields="orgFields"
                :allow-filtering="true"
                filter-type="Contains"
                @change="selectOrg"
                id="baseTreeSelect"
              />
              <mt-select
                v-else
                v-model="searchFormModel.orgId"
                css-class="rule-element"
                :data-source="[]"
                :show-clear-button="true"
                :placeholder="$t('请选择组织')"
              />
            </mt-form-item>
            <mt-form-item prop="evaluateCycle" :label="$t('评价周期')" label-style="top">
              <mt-select
                v-model="searchFormModel.evaluateCycle"
                css-class="rule-element"
                :data-source="perEvaluationOptions"
                :show-clear-button="true"
                :fields="{ text: 'dictName', value: 'dictCode' }"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="evaluateYear" :label="$t('评价年份')" label-style="top">
              <mt-input v-model="searchFormModel.evaluateYear"></mt-input>
            </mt-form-item>

            <mt-form-item prop="scoreUserName" :label="$t('评分人')" label-style="top">
              <mt-input
                v-model="searchFormModel.scoreUserName"
                @change="(e) => handleInputChange(e, 'scoreUserName')"
              ></mt-input>
            </mt-form-item>
            <!-- <mt-form-item prop="categCode" :label="$t('品类编码')" label-style="top">
              <mt-input v-model="searchFormModel.categCode"></mt-input>
            </mt-form-item> -->
            <!-- <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <mt-input v-model="searchFormModel.supplierCode"></mt-input>
            </mt-form-item> -->

            <mt-form-item
              prop="achievementAssessScoreType"
              :label="$t('打分类型')"
              label-style="top"
            >
              <mt-select
                v-model="searchFormModel.achievementAssessScoreType"
                css-class="rule-element"
                :data-source="perGradeTypeOptions"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="scoreStandard" :label="$t('评分标准')" label-style="top">
              <mt-input v-model="searchFormModel.scoreStandard"></mt-input>
            </mt-form-item>
            <mt-form-item prop="score" :label="$t('得分')" label-style="top">
              <mt-input v-model="searchFormModel.score" type="number"></mt-input>
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.remark"
                @change="(e) => handleInputChange(e, 'remark')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="approveStatus" :label="$t('审批状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.approveStatus"
                css-class="rule-element"
                :data-source="approveStatusOptions"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input v-model="searchFormModel.createUserName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                :placeholder="$t('请选择创建时间')"
                @change="(e) => handleDateTimeChange(e, 'CreateDate')"
              ></mt-date-range-picker>
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
              <mt-input v-model="searchFormModel.updateUserName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="modifyDate" :label="$t('最后更新时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.modifyDate"
                :placeholder="$t('请选择最后更新时间')"
                @change="(e) => handleDateTimeChange(e, 'ModifyDate')"
              ></mt-date-range-picker>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import {
  pageConfig,
  perGradeTypeOptions,
  perEvaluationOptions,
  submitStatusOptions,
  approveStatusOptions
} from './config/index.js'
import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils.js'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      supplierSearchFields: ['categoryCode', 'categoryName'],
      SearchFields: ['supplierCode', 'supplierName'],
      orgIdArr: [],
      templateListArrList: [], // 绩效模板
      searchFormModel: {},
      // 得分是否为空
      scoreNullOptions: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 2 }
      ],
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      pageConfig,
      isLoading: false,
      perGradeTypeOptions,
      perEvaluationOptions,
      submitStatusOptions,
      approveStatusOptions
    }
  },

  created() {
    this.getOrgList() //获取组织下拉数据
    this.getLatestData()
    this.getTemplateList()
  },
  activated() {
    this.getOrgList() //获取组织下拉数据
    this.getTemplateList()
  },
  deactivated() {
    this.orgFields.dataSource = []
  },
  computed: {},
  methods: {
    // 默认展示 最新年份 和 未提交状态的数据
    getLatestData() {
      // const currentYear = new Date().getFullYear()
      const uncommittedState = 0
      // this.searchFormModel.evaluateYear = currentYear
      this.searchFormModel.submitStatus = uncommittedState
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        console.log('e.startDate', e.startDate)
        this.searchFormModel['start' + field] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    // 重置
    handleCustomReset() {
      // this.templateListArrList = []
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 输入框事件
    handleInputChange(e, modelName) {
      if (!e) {
        this.searchFormModel[modelName] = null
      }
    },
    // 选择绩效模板
    selectTemplate(e) {
      //清空供应商选中和品类选中
      let { itemData } = e
      if (itemData) {
        this.searchFormModel.templateId = itemData.id //模板id
        this.searchFormModel.templateCode = itemData.templateCode
      } else {
        this.searchFormModel.templateId = null
        this.searchFormModel.templateCode = null
      }
    },
    //获取绩效模板
    getTemplateList() {
      let params = {
        page: { current: 1, size: 9999 }
      }
      params = {
        ...params
        // ...para
      }
      this.$API.performanceManage.templateListQuery(params).then((result) => {
        this.templateListArrList = result.data
        console.log(
          'result.dataresult.dataresult.dataresult.dataresult.dataresult.data',
          result.data
        )
      })
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    selectOrg(e) {
      console.log('2341234123412345134252345234', e)
      // const { value } = e
      if (e.value.length === 0) {
        this.searchFormModel.orgCode = null
        this.searchFormModel.orgId = null
      }
      this.matchItem(this.orgFields.dataSource, e['value'][0])

      // this.getTemplateList({
      //   orgId: value[0]
      // })
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.searchFormModel.orgCode = ele.orgCode
          this.searchFormModel.orgId = ele.id
          this.searchFormModel.orgName = ele.name
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    // 点击工具栏
    handleClickToolBar(item) {
      let records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      // if (records.length <= 0 && (item.toolbar.id == 'Submit' || item.toolbar.id == 'Recall')) {
      //   this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      //   return
      // }
      if (item.toolbar.id == 'Submit') {
        let _idList = records.map((e) => e.id)
        this.submitRecord(_idList)
      } else if (item.toolbar.id == 'Recall') {
        let _idList = records.map((e) => e.id)
        this.RecallFn(_idList)
      } else if (item.toolbar.id == 'ImportCate') {
        this.ImportFn()
      } else if (item.toolbar.id == 'ExportCate') {
        this.handleClickDownload()
      }
    },

    //Excel导入 - 非战略供应商
    ImportFn() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.performanceManage.noStrategyImport,
          downloadTemplateApi: this.$API.performanceManage.noStrategyExport,
          downloadTemplateParams: this.searchFormModel
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //导出
    handleClickDownload() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.performanceManage.noStrategyExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 提交
    submitRecord(ids) {
      if (ids.length === 0) this.submitRecordAll()
      if (ids.length > 0) {
        this.$API.performanceManage
          .noStrategySubmit({
            ids
          })
          .then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
            this.getList()
          })
      }
    },
    // 全部提交
    submitRecordAll() {
      const currentViewData =
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.ej2Instances.currentViewData
      if (currentViewData.length === 0)
        return this.$toast({ content: this.$t('暂无数据可提交'), type: 'warning' })
      // const params = { ...this.searchFormModel }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('请确认是否全部提交')
        },
        success: () => {
          this.$API.performanceManage
            .noStrategySubmitAll({
              ...this.searchFormModel
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('全部提交成功'), type: 'success' })
                this.$refs.templateRef.refreshCurrentGridData()
              }
              this.getList()
            })
        }
      })
    },
    // 撤回
    RecallFn(ids) {
      if (ids.length === 0) this.revokeScoreDetailsFnAll()
      if (ids.length > 0) {
        this.$API.performanceManage
          .noStrategyRecall({
            ids: ids
          })
          .then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('撤回成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
              this.getList()
            }
          })
      }
    },
    // 全部撤回
    revokeScoreDetailsFnAll() {
      const currentViewData =
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.ej2Instances.currentViewData
      if (currentViewData.length === 0)
        return this.$toast({ content: this.$t('暂无数据可撤回'), type: 'warning' })
      // const params = { ...this.searchFormModel }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('请确认是否全部撤回')
        },
        success: () => {
          this.$API.performanceManage
            .noStrategyRecallAll({
              ...this.searchFormModel
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('全部撤回成功'), type: 'success' })
                this.$refs.templateRef.refreshCurrentGridData()
              }
              this.getList()
            })
        }
      })
    },
    actionBegin(args) {
      // rowData是编辑前的行数据， data是编辑后的数据
      const { requestType, rowData, data } = args
      if (requestType === 'save') {
        if (data.achievementAssessScoreType === '1' && !data.score) {
          this.$toast({ content: this.$t('打分类型为得分时“得分”项必填'), type: 'warning' })
          args.cancel = true
        }
        if (data.achievementAssessScoreType === '2' && !data.achievementAssessScoreValue) {
          this.$toast({ content: this.$t('打分类型为评分时得“评分值”项必填'), type: 'warning' })
          args.cancel = true
        }
      } else if (requestType === 'beginEdit') {
        // 单据状态处于未提交状态（0）时才能进入编辑
        if (rowData.submitStatus == 1) {
          args.cancel = true
        }
      }
    },
    actionComplete(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    save(data) {
      this.$API.performanceManage
        .noStrategySave(data)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch((err) => {
          console.log(err)
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    selectScoreNull(e) {
      console.log('143234123412341234', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
/deep/ .quick-search .custom-search-container .mt-form {
  display: grid;
  grid-template-columns: repeat(auto-fill, 250px);
}
</style>
