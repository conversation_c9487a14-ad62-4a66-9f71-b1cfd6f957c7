<template>
  <div>
    <mt-tabs
      :e-tab="false"
      overflow-mode="Popup"
      :data-source="renderSource"
      :selected-item="selectIndex"
      v-permission="permissionObj"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- <p-tab :data-source="dataSource" @handleSelectTab="handleSelectTab"></p-tab> -->
    <div v-if="selectIndex == indexObj['T_02_0178']">
      <categoryGrade />
    </div>
    <div v-if="selectIndex == indexObj['T_02_0179']">
      <strategicSupplierScreening />
    </div>
    <div v-if="selectIndex == indexObj['T_02_0180']">
      <nonStrategicSupplierRating />
    </div>
  </div>
</template>
<script>
// import pTab from '@/components/PermissionTab/pTab.vue'
import { utils } from '@mtech-common/utils'
import axios from 'axios'

export default {
  components: {
    // pTab,
    categoryGrade: () => import('./tabsPage/categoryGrade/index.vue'),
    strategicSupplierScreening: () => import('./tabsPage/strategicSupplierScreening/index.vue'),
    nonStrategicSupplierRating: () => import('./tabsPage/nonStrategicSupplierRating/index.vue')
  },
  data() {
    return {
      selectIndex: 0,
      indexObj: {},
      // dataSource: [
      //   { title: this.$t('品类分层分级'), 'data-permission': 'categoryGradePermission' },
      //   { title: this.$t('战略供应商筛选'), 'data-permission': 'strategicSupplierPermission' },
      //   { title: this.$t('非战略供应商评级'), 'data-permission': 'nonStrategicSupplierPermission' }
      // ],
      renderSource: [],
      dataSource: [
        {
          title: this.$t('品类分层分级'),
          permission: 'T_02_0178' // 权限字段
          // disabled: false
        },
        {
          title: this.$t('战略供应商筛选'),
          permission: 'T_02_0179' // 权限字段
          // disabled: false
        },
        {
          title: this.$t('非战略供应商评级'),
          permission: 'T_02_0180' // 权限字段
          // disabled: false
        }
      ],
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'categoryGradePermission', permissionCode: 'T_02_0178' },
          { dataPermission: 'strategicSupplierPermission', permissionCode: 'T_02_0179' },
          { dataPermission: 'nonStrategicSupplierPermission', permissionCode: 'T_02_0180' }
        ]
      }
    }
  },
  mounted() {
    this.handleRouter()
    this.getPermissionList()
  },
  methods: {
    // 切换Tab
    handleSelectTab(e) {
      console.log('切换Tab切换Tab切换Tab', e)
      this.selectIndex = e
    },
    // 路由跳转
    handleRouter() {
      // console.log('routerrouterrouter', this.$route)
      if (this.$route.query.selectIndex != null) {
        this.selectIndex = this.$route.query.selectIndex
      }
    },
    // 获取权限列表
    getPermissionList() {
      console.log('handleSelectTab123')

      axios
        .get(
          `/api/iam/tenant/user-permission-query/user-element-permissions?appCode=${utils.getAppCode()}`,
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' } }
        )
        .then((result) => {
          let allPermissionList = []
          let renderSource = []
          let selectedItem = -1 // 默认已选得tab index

          !!result.data &&
            result.data.data.forEach((item) => {
              allPermissionList.push(item.permissionCode)
            })
          let objIndex = 0
          this.dataSource.forEach((item) => {
            if (Object.prototype.hasOwnProperty.call(item, 'permission')) {
              console.log('123')
              let disabled =
                !item.permission ||
                (allPermissionList.length > 0 && !allPermissionList.includes(item.permission))
              if (!disabled) {
                console.log('sdsdsdsdsd', item)
                selectedItem = 0 // 寻找默认tab
                this.indexObj[item.permission] = objIndex
                objIndex += 1
              }
              // 非禁用得推入数据 禁用的将删除
              !disabled &&
                renderSource.push({
                  ...item,
                  disabled
                })
            } else {
              console.log('45666666')
              if (selectedItem === -1) {
                selectedItem = 0
              }
              renderSource.push({
                ...item,
                disabled: false
              })
            }
          })
          this.selectIndex = selectedItem
          this.renderSource = renderSource
          console.log('handleSelectTabhandleSelectTab', this.indexObj, selectedItem)
        })
    }
  }
}
</script>
