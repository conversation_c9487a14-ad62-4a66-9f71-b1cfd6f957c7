<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    css-class="create-proj-dialog"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="formObject" :rules="rules">
        <mt-form-item prop="orgCode" :label="$t('组织')">
          <mt-select
            v-model="formObject.orgCode"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="orgList"
            :placeholder="$t('请选择计划生效组织')"
            @change="orgChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="analysisType" :label="$t('分析类型')">
          <mt-select
            v-model="formObject.analysisType"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="analysisList"
            :placeholder="$t('请选择分析类型')"
            @change="analysisTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="dataRange" :label="$t('数据范围')">
          <mt-select
            v-model="formObject.dataRange"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="dataRangeList"
            :placeholder="$t('请选择数据范围')"
            @change="dataRangeChange"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      dialogTitle: '',
      orgList: [], // 计划策略
      analysisList: [], // 计划周期
      dataRangeList: [],
      formObject: {
        planName: null,
        orgCode: null,
        analysisType: null,
        dataRange: null,
        planStrgy: null,
        planCycle: null
      },
      rules: {
        dataRange: [
          {
            required: true,
            message: this.$t('请选择数据范围'),
            trigger: 'blur'
          }
        ],
        planStrgy: [
          {
            required: true,
            message: this.$t('请选择计划策略'),
            trigger: 'blur'
          }
        ],
        planCycle: [
          {
            required: true,
            message: this.$t('请选择计划周期'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    if (this.dialogData.dialogType === 'add') {
      this.dialogTitle = this.$t('新增计划')
    } else {
      this.dialogTitle = this.$t('编辑计划')
    }
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    orgChange() {},
    analysisTypeChange() {},
    dataRangeChange() {}
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
</style>
