<template>
  <div style="background-color: #fff">
    <sc-table
      ref="sctableRef"
      grid-id="c6eed7fc-d91e-429d-a0a2-43c29810a004"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template #fileDefault="{ row }">
        <div style="color: #409eff; cursor: pointer" @click="handleUpload(row)">
          {{ $t('查看附件') }}
        </div>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      loading: false,
      tableData: [],

      resultOptions: [
        { text: this.$t('待提交'), value: 0 },
        { text: this.$t('待审批'), value: 1 },
        { text: this.$t('通过'), value: 2 },
        { text: this.$t('驳回'), value: 3 }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'selfReviewCode',
          title: this.$t('评审任务编码')
        },
        {
          field: 'selfReviewName',
          title: this.$t('评审任务名称')
        },
        {
          field: 'orgCode',
          title: this.$t('公司'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.orgName : ''
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.categoryName : ''
          }
        },
        {
          field: 'categoryRelationCode',
          title: this.$t('品类采供关系编码')
        },
        {
          field: 'supplierEnterpriseName',
          title: this.$t('供应商企业名称')
        },
        {
          field: 'file',
          title: this.$t('结果附件'),
          slots: {
            default: 'fileDefault'
          }
        },
        {
          field: 'reviewResult',
          title: this.$t('自查结果'),
          formatter: ({ cellValue }) => {
            let item = this.resultOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'rejectReason',
          title: this.$t('驳回原因')
        },
        {
          field: 'approverUserName',
          title: this.$t('审批人')
        },
        {
          field: 'supplierReason',
          title: this.$t('供方备注')
        }
      ]
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleUpload(row) {
      this.$dialog({
        modal: () => import('./FileManage.vue'),
        data: {
          title: this.$t('查看附件'),
          type: 'download',
          id: row.id,
          selfReviewCode: row.selfReviewCode,
          supplierEnterpriseId: row.supplierEnterpriseId
        }
      })
    },
    handleSearch() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        id: this.$route.query?.categoryPartnerRelationId
      }
      let api = this.$API.customerAccess.getByCategoryPartnerRelationIdApi
      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        if (res.data) {
          const records = [res.data]
          this.tableData = records
        }
      }
    }
  }
}
</script>
