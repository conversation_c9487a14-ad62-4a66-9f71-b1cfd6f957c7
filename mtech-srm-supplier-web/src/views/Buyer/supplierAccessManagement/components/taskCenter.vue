<template>
  <div class="task-center fbox">
    <div class="task-sidebar">
      <div class="search-box"></div>
      <div class="task-collapse">
        <div class="collapse-content">
          <template>
            <mt-collapse :accordion="false" :has-arr="true" v-model="activeCompanyId">
              <mt-panel :name="taskData.customerEnterpriseId">
                <div slot="header" class="accordion-header">
                  <span
                    class="company-name"
                    :alt="taskData.customerEnterpriseName"
                    :title="taskData.customerEnterpriseName"
                    >{{ taskData.customerEnterpriseName }}</span
                  >
                  <span class="header-number">({{ taskData.supplierTaskList.length || 0 }})</span>
                </div>
                <div slot="content">
                  <div
                    class="sub-item"
                    v-for="childItem of taskData.supplierTaskList"
                    :key="childItem.id"
                    @click="selectTask(childItem)"
                    :class="{
                      active: activeIndexId === childItem.id,
                      grey:
                        childItem.status === 30 ||
                        childItem.status === 40 ||
                        childItem.status === 50
                    }"
                  >
                    <div
                      class="task-name ellipsis"
                      :alt="childItem.taskName"
                      :title="childItem.taskName"
                    >
                      {{ childItem.taskName }}
                    </div>
                    <div class="sub-tips">
                      {{ mapState[childItem.status] }}
                    </div>
                  </div>
                </div>
              </mt-panel>
            </mt-collapse>
          </template>
        </div>
      </div>
    </div>
    <div class="task-content flex1">
      <div class="task-info fbox">
        <div class="mian-info flex1">
          <div class="task-list fbox">
            <span>{{ $t('调查表名称：') }}{{ buyerTaskInstance.taskName }}</span>
            <span
              >{{ $t('调查表填写方：') }}
              {{
                buyerTaskInstance && buyerTaskInstance.taskOwner === 1
                  ? $t('供方')
                  : buyerTaskInstance && buyerTaskInstance.taskOwner === 0
                  ? $t('采方')
                  : '--'
              }}</span
            >
            <span>{{ $t('邀请人：') }}{{ buyerTaskInstance.inviterName }}</span>
            <span>{{ $t('邀请时间：') }}{{ buyerTaskInstance.createTime }}</span>
            <!-- 待审批 的驳回通过放入 审批中心去做了 此处不展示 -->
            <span class="reject-reason" v-if="formInfo.status === 20">{{
              $t('该调查表已发送至相关人员的审批中心等待审批')
            }}</span>
          </div>
          <div class="categroy-list fbox" v-if="buyerPartnerFactoryRelationList.length > 0">
            <div
              class="factory-item fbox"
              v-for="item of buyerPartnerFactoryRelationList"
              :key="item.id"
            >
              <span class="factory-name">{{ item.factoryName }}</span>
              <template
                v-if="
                  !!item.buyerPartnerCategoryRelationList &&
                  item.buyerPartnerCategoryRelationList.length > 0
                "
              >
                <span
                  class="factory-category"
                  v-for="cItem of item.buyerPartnerCategoryRelationList"
                  :key="cItem.id"
                  >{{ cItem.categoryName }}</span
                >
              </template>
            </div>
          </div>
        </div>
        <div class="btn-box fbox">
          <template v-if="formInfo.status === 30 && !!buyerTaskInstance.auditRemark">
            <mt-tooltip :content="buyerTaskInstance.auditRemark" target="#box">
              <div class="err-tips" id="box">
                <i class="mt-icons mt-icon-MT_info"></i>{{ $t('驳回原因') }}
              </div>
            </mt-tooltip>
          </template>

          <!-- 是否展示按钮 只有采方待办 && 待填写 已驳回 -->
          <template v-if="(formInfo.status === 10 || formInfo.status === 30) && displayOperation">
            <div class="normal-btn" @click="saveTask()">{{ $t('保存') }}</div>
            <div class="normal-btn" @click="proSubmitTask()">
              <template v-if="formInfo.status === 10">{{ $t('提交') }}</template>
              <template v-if="formInfo.status === 30">{{ $t('重新提交') }}</template>
            </div>
          </template>
        </div>
      </div>
      <div class="task-form fbox">
        <div class="side-bar">
          <div
            class="side-item ellipsis"
            :alt="item.formName"
            :title="item.formName"
            :class="{ active: activeForm.id === item.id }"
            v-for="item of buyerFormInstanceList"
            :key="item.id"
            @click="scrollInto(item.id)"
          >
            {{ item.formName }}
          </div>
          <!--@click="selectFromItem(item)"-->
        </div>

        <div class="form-content flex1" ref="formContent">
          <template v-if="formTemplateArr.length > 0">
            <div
              v-for="item of formTemplateArr"
              :key="item.id"
              class="display-item"
              :ref="'formItem_' + item.id"
              :data-id="item.id"
            >
              <!--class="none" :class="{'display-item': activeForm.id === item.id}"-->
              <div class="parse-title">{{ item.name }}</div>
              <mt-parser :ref="`parser_${item.id}`" :form-conf="item.value" />
            </div>
          </template>
          <template v-else-if="!!emptyMsg">
            <div class="empty-container">
              <img src="../../../../assets/emptyData.png" />
              <div class="empty-txt">{{ emptyMsg }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mtCollapse from '../../../../components/collapse/collapse.vue'
import mtPanel from '../../../../components/collapse/panel.vue'
import Parser from '@mtech-form-design/form-parser'
import utils from '@/utils/utils.js'

// polyfill for scrollIntoView
import smoothscroll from 'smoothscroll-polyfill'
// kick off the polyfill!
smoothscroll.polyfill()

// 表单tab后面的数据
let FORMLISTDATA = new Map()

export default {
  components: {
    'mt-collapse': mtCollapse,
    'mt-panel': mtPanel,
    'mt-parser': Parser
  },
  filters: {
    // 遍历二级数组下 status为10 待填写 的数量
    filterStatus10(dataArr) {
      let dataNum = 0
      dataArr.forEach((dItem) => {
        let filterData =
          !!dItem.buyerTaskList && dItem.buyerTaskList.filter((cv) => cv.status === 10).length
        dataNum = dataNum + filterData
      })
      return dataNum
    },
    noFilterStatus10(dataArr) {
      console.log('=dataArr=', dataArr)
      let dataNum = 0
      dataArr.forEach((dItem) => {
        let filterData = !!dItem.buyerTaskList && dItem.buyerTaskList.length
        dataNum = dataNum + filterData
      })
      return dataNum
    },
    filterStatus102(dataArr) {
      let dataNum = !!dataArr && dataArr.filter((cv) => cv.status === 10).length
      return dataNum
    },
    noFilterStatus102(dataArr) {
      let dataNum = !!dataArr && dataArr.length
      return dataNum
    },
    filterStatus(value) {
      let statusTxt = ''
      switch (value) {
        case 10:
          statusTxt = this.$t('待填写')
          break
        case 20:
          statusTxt = this.$t('待审批')
          break
        case 30:
          statusTxt = this.$t('已驳回')
          break
        case 40:
          statusTxt = this.$t('已完成')
          break
        case 50:
          statusTxt = this.$t('已关闭')
          break
        default:
          statusTxt = '--'
      }
      return statusTxt
    }
  },
  props: {
    id: {
      type: String || Number,
      default: ''
    },
    orgId: {
      type: String || Number,
      default: ''
    },
    partnerRelationId: {
      type: String || Number,
      default: ''
    },
    categoryPartnerRelationId: {
      type: String || Number,
      default: ''
    },
    inviteId: {
      type: String || Number,
      default: ''
    }
  },
  data() {
    return {
      emptyMsg: '',
      displayOperation: false, // 是否展示操作按钮 只有采方待办才行
      mapState: {
        10: this.$t('待填写'),
        20: this.$t('待审批'),
        30: this.$t('已驳回'),
        40: this.$t('已完成'),
        50: this.$t('已关闭')
      },
      collapseValue: '',
      activeIndexId: -1,
      activeCompanyId: -1,
      activeForm: {},
      // 表单的详情
      formInfo: {},

      // 任务详情
      buyerTaskInstance: {},
      //表单详情
      buyerFormInstanceList: {},
      //公司准入关系
      buyerPartnerFactoryRelationList: {},
      // 表单模板
      formTemplateArr: [],
      // 任务数据
      taskData: {}
    }
  },
  created() {
    this.getTaskList()
  },
  methods: {
    // 获取任务列表
    getTaskList() {
      this.$loading()
      this.$API.supplierlifecycle
        .getTaskList({
          categoryRelationId: this.categoryPartnerRelationId,
          inviteId: this.inviteId
        })
        .then((res) => {
          const { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            this.taskData = data

            if (!utils.isEmpty(data?.supplierTaskList || [])) {
              // 任务列表中包含待审批的任务，显示保存、保存并提交按钮
              const exitIndex = data.supplierTaskList?.findIndex((item) => item.status === 20)
              this.$emit('handleShowBtn', exitIndex >= 0)

              let index = 0
              if (this.formInfo.id) {
                const i = data.supplierTaskList.findIndex((item) => item.id === this.formInfo.id)
                i > 0 ? (index = i) : (index = 0)
              }
              this.formInfo = data.supplierTaskList[index]
            }
            // 默认展开
            if (!utils.isEmpty(this.formInfo)) {
              this.activeIndexId = this.formInfo.id
              this.activeCompanyId = data?.customerEnterpriseId
              this.$emit('curTaskChange', this.formInfo)
              this.selectTask(this.formInfo)
            } else {
              this.$hloading()
            }
          }
        })
    },
    // 选择对应的调查表任务 任务id
    async selectTask(formInfo, index) {
      console.log('formInfo', formInfo)
      await this.getAllDate()
      // let realTimeData = await this.getAllDate()
      // if (
      //   this.formTemplateArr.length &&
      //   JSON.stringify(this.initData) !== JSON.stringify(realTimeData)
      // ) {
      //   this.$dialog({
      //     data: {
      //       title: this.$t('提示'),
      //       message: this.$t('填写的内容还未保存，切换后会丢失，确认切换吗？')
      //     },
      //     success: () => {
      //       this.switchTask(formInfo, index)
      //     }
      //   })
      // } else {
      this.switchTask(formInfo, index)
      // }
    },
    switchTask(formInfo, index) {
      this.editFlag = formInfo.status == 40

      // 是否展示操作按钮  采方待办 也就是第一个下标的 展示
      if (Number(index) === 0) {
        this.displayOperation = true
      } else {
        this.displayOperation = false
      }

      this.formInfo = formInfo
      let { id } = formInfo

      // 默认展开
      this.activeIndexId = formInfo.id

      // 重置掉表单
      this.formTemplateArr.length = 0
      FORMLISTDATA.clear()

      this.$loading()
      // 兜底去除loading
      setTimeout(() => {
        this.$hloading()
      }, 1000)
      this.$API.supplierlifecycle
        .getTaskDetail({ taskInstanceId: id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            // 任务详情   ---           表单详情 ---            公司准入关系
            const { buyerTaskInstance, buyerFormInstanceList, buyerPartnerFactoryRelationList } =
              data

            this.buyerTaskInstance = buyerTaskInstance
            this.buyerFormInstanceList = buyerFormInstanceList
            this.buyerPartnerFactoryRelationList = buyerPartnerFactoryRelationList

            this.$emit('approveInfoChange', buyerTaskInstance)
          } else {
            this.$hloading()
            this.$toast({
              content: this.$t('获取任务详情失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('获取任务详情失败，请重试!'),
            type: 'warning'
          })
        })

      this.getAllFormDefine()
    },
    getAllDate() {
      return new Promise((resolve, reject) => {
        let formDataArr = []
        this.$nextTick(function () {
          if (this.formTemplateArr.length) {
            this.$refs[`parser_${this.formTemplateArr[0].id}`][0].getFormData()
            // “行内编辑在不脱离编辑状态的情况下直接点右上角保存”
            // “循环调用组件方法脱离编辑状态拿到编辑中的数据”
            this.formTemplateArr.forEach((e) => {
              let parseName = `parser_${e.id}`
              let _formData = this.getData(parseName)
              _formData
                .then((res) => {
                  formDataArr.push({
                    formData: res,
                    formInstanceId: e.id
                  })
                  // “if条件是用来判断有没有遍历完动态表单的，满足if条件代表遍历了，else一定会走，不用管”
                  if (formDataArr.length === this.formTemplateArr.length) {
                    resolve(formDataArr)
                  }
                })
                .catch(() => {
                  reject({})
                })
            })
          } else {
            resolve({})
          }
        })
      })
    },
    async saveOldData() {
      this.initData = JSON.parse(JSON.stringify(await this.getAllDate()))
    },
    async getAllFormDefine() {
      // 重置表单空提示
      this.emptyMsg = ''

      let { id } = this.formInfo
      const formAllDetail = await this.queryAllFormData()
      if (FORMLISTDATA.has(id)) {
        // 存入表单数据
        this.$nextTick(() => {
          if (!utils.isEmpty(formAllDetail) && formAllDetail.length > 0) {
            formAllDetail.forEach((detail) => {
              if (!utils.isEmpty(detail) && !utils.isEmpty(detail.formData)) {
                setTimeout(() => {
                  this.$refs[`parser_${detail.formInstanceId}`][0].setFormData(detail.formData)
                }, 1000)
              }
            })
          }
        })
        this.$hloading()
        return
      }

      this.$API.supplierlifecycle.getAllFormDefine({ taskInstanceId: id }).then((res) => {
        let { code, data } = res
        if (code === 200 && !utils.isEmpty(data)) {
          // 非可编辑的表单 循环disabled
          if (data.length > 0) {
            data.forEach((tmpItem) => {
              if (
                !utils.isEmpty(tmpItem) &&
                !utils.isEmpty(tmpItem.formDefineResponse) &&
                !utils.isEmpty(tmpItem.formDefineResponse.template)
              ) {
                let template = this.disableTemplate(tmpItem.formDefineResponse.template)
                this.formTemplateArr.push({
                  id: tmpItem.id,
                  value: template,
                  name: tmpItem.formName
                })
              }
            })

            // 处理滚动条到容器顶部
            if (this.formTemplateArr) {
              document.querySelector('.display-item') &&
                document
                  .querySelector('.display-item')
                  .scroll({ top: 0, left: 0, behavior: 'auto' })
            }

            // 缓存当前表单结构数据
            FORMLISTDATA.set(id, this.formTemplateArr)

            this.$nextTick(() => {
              // 存入表单数据
              if (!utils.isEmpty(formAllDetail) && formAllDetail.length > 0) {
                formAllDetail.forEach((detail) => {
                  if (!utils.isEmpty(detail) && !utils.isEmpty(detail.formData)) {
                    setTimeout(() => {
                      this.$refs[`parser_${detail.formInstanceId}`][0].setFormData(detail.formData)
                    }, 1000)
                  }
                })
              }
              this.$hloading()
            })
            this.saveOldData()
          }
        } else {
          this.emptyMsg = this.$t('获取表单定义模板失败，请重试!')
          this.$hloading()
          this.$toast({
            content: this.$t('获取表单定义模板失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    // 获取默认表单实例
    async getDefaultFormInstance(defaultForm) {
      this.$loading()
      // 当前表单id
      this.activeForm = defaultForm

      // 实时获取表单内部数据
      const formDetail = await this.queryFormDataDetail(defaultForm)

      if (FORMLISTDATA.has(this.activeForm.id)) {
        this.$nextTick(() => {
          // 存入表单数据
          if (!utils.isEmpty(formDetail) && !utils.isEmpty(formDetail.formData)) {
            this.$nextTick(() => {
              this.$refs[`parser_${defaultForm.id}`][0].setFormData(formDetail.formData)
            })
          }
          this.$hloading()
        })
        return
      }

      // 获取定义表结构
      this.$API.supplierlifecycle
        .getFormDefine({ formInstanceId: defaultForm.id })
        .then((res) => {
          let { code, data } = res
          if (
            code === 200 &&
            !utils.isEmpty(data) &&
            !utils.isEmpty(data.formDefineResponse) &&
            !utils.isEmpty(data.formDefineResponse.template)
          ) {
            // 非可编辑的表单 循环disabled
            let template = this.disableTemplate(data.formDefineResponse.template)

            this.formTemplateArr.push({
              id: this.activeForm.id,
              value: template,
              name: this.activeForm.formName
            })
            // 缓存当前表单结构数据
            FORMLISTDATA.set(this.activeForm.id, template)

            this.$nextTick(() => {
              // 存入表单数据
              if (!utils.isEmpty(formDetail) && !utils.isEmpty(formDetail.formData)) {
                this.$refs[`parser_${this.activeForm.id}`][0].setFormData(formDetail.formData)
              }
              this.$hloading()
            })
          } else {
            this.$hloading()
            this.$toast({
              content: this.$t('获取表单定义模板失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('获取表单定义模板失败，请重试!'),
            type: 'warning'
          })
        })
    },

    /**
     * (formInfo.status === 10 || formInfo.status === 30) && displayOperation
     * disable不能操作的表单结构
     */
    disableTemplate(template) {
      if ((this.formInfo.status === 10 || this.formInfo.status === 30) && this.displayOperation) {
        return template
      } else {
        !!template.fields &&
          template.fields.forEach((tItem) => {
            tItem.disabled = true
          })
        return template
      }
    },

    // 获取表单生成器数据
    queryFormDataDetail(defaultForm) {
      return this.$API.supplierlifecycle
        .queryFormData({ formInstanceId: defaultForm.id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            return data
          } else {
            return {}
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('获取表单定义模板失败，请重试!'),
            type: 'warning'
          })
          this.$hloading()
        })
    },

    // 获取all 表单生成器数据
    queryAllFormData() {
      let { id } = this.formInfo
      return this.$API.supplierlifecycle
        .queryAllFormData({ taskInstanceId: id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            return data
          } else {
            return {}
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'warning'
          })
          this.$hloading()
        })
    },

    // 表单滚动
    scrollInto(id) {
      console.log('999999', id)
      this.$refs['formItem_' + id][0].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    },

    // 选择form表单栏目
    selectFromItem(item) {
      this.getDefaultFormInstance(item)
    },

    // 保存的接口请求
    saveTaskAjax() {
      // 校验表单的promise
      let promiseValidataArr = []
      for (let va of this.formTemplateArr) {
        let parseName = `parser_${va.id}`
        promiseValidataArr.push(this.$refs[parseName][0].valiteFormData())
      }
      return (
        Promise.all(promiseValidataArr)
          // .then((result) => {
          .then(() => {
            return new Promise((resolve, reject) => {
              let formDataArr = []
              // for (let va of this.formTemplateArr) {
              //   let parseName = `parser_${va.id}`;
              //   formDataArr.push({
              //     formData: this.$refs[parseName][0].getFormData(),
              //     formInstanceId: va.id,
              //   });
              // }

              // return this.$API.supplierlifecycle.saveFormData({
              //   buyerFormDataRequestList: formDataArr,
              //   taskInstanceId: this.formInfo.id,
              // });
              this.$refs[`parser_${this.formTemplateArr[0].id}`][0].getFormData()
              this.formTemplateArr.forEach((e) => {
                let parseName = `parser_${e.id}`
                let _formData = this.getData(parseName)
                _formData.then((res) => {
                  formDataArr.push({
                    formData: res,
                    formInstanceId: e.id
                  })
                  if (formDataArr.length === this.formTemplateArr.length) {
                    return this.$API.supplierlifecycle
                      .saveFormData({
                        buyerFormDataRequestList: formDataArr,
                        taskInstanceId: this.formInfo.id
                      })
                      .then((res) => {
                        resolve(res)
                      })
                      .catch((err) => {
                        resolve([])
                        reject(err)
                      })
                  }
                })
              })
            })
          })
          .catch((reason) => {
            return Promise.resolve({
              msg: reason.msg || this.$t('表单验证不通过，请查看表单是否有漏填!')
            })
          })
      )
    },
    saveTaskWithoutCheckAjax() {
      return new Promise((resolve) => {
        let formDataArr = []
        this.$refs[`parser_${this.formTemplateArr[0].id}`][0].getFormData()
        // for (let va of this.formTemplateArr) {
        //   let parseName = `parser_${va.id}`;
        //   formDataArr.push({
        //     formData: this.$refs[parseName][0].getFormData(),
        //     formInstanceId: va.id,
        //   });
        // }

        // resolve(this.$API.supplierlifecycle.saveFormData({
        //   buyerFormDataRequestList: formDataArr,
        //   taskInstanceId: this.formInfo.id,
        // }));

        // “行内编辑在不脱离编辑状态的情况下直接点右上角保存”
        // “循环调用组件方法脱离编辑状态拿到编辑中的数据”
        this.formTemplateArr.forEach((e) => {
          let parseName = `parser_${e.id}`
          let _formData = this.getData(parseName)
          _formData
            .then((res) => {
              formDataArr.push({
                formData: res,
                formInstanceId: e.id
              })
              // “if条件是用来判断有没有遍历完动态表单的，满足if条件代表遍历了，else一定会走，不用管”
              if (formDataArr.length === this.formTemplateArr.length) {
                resolve(
                  this.$API.supplierlifecycle.saveFormData({
                    buyerFormDataRequestList: formDataArr,
                    taskInstanceId: this.formInfo.id
                  })
                )
              }
            })
            .catch((error) => {
              resolve({
                msg: error.msg
              })
            })
        })
      })
    },
    getData(parseName) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(this.$refs[parseName][0].getFormData())
        }, 100)
      })
    },
    // 保存任务
    async saveTask() {
      this.$loading()
      const ajaxSaveTask = await this.saveTaskWithoutCheckAjax()
      this.$hloading()

      // let { code, data } = ajaxSaveTask;
      let { code } = ajaxSaveTask
      if (code && code === 200) {
        this.$toast({
          content: ajaxSaveTask.msg || this.$t('保存成功!'),
          type: 'success'
        })
        this.saveOldData()
      } else {
        this.$toast({
          content: ajaxSaveTask.msg || this.$t('获取表单定义模板数据失败，请重试!'),
          type: 'warning'
        })
      }
    },

    // 预提交
    proSubmitTask() {
      if (this.formInfo.status === 10) {
        // 正常提交
        this.submitTask()
      }
      if (this.formInfo.status === 30) {
        // 驳回的重新提交 要弹框填 重新提交原因
        this.$dialog({
          modal: () => import('../components/addResubmitReason.vue'),
          data: {
            title: this.$t('重新提交'),
            rejectReason: this.supplierTaskInstance.auditRemark
          },
          success: (data) => {
            let submitReason = data
            this.submitTask(submitReason)
          }
        })
      }
    },

    // 提交任务
    async submitTask(submitReason = '') {
      this.$loading()
      setTimeout(() => {
        this.$hloading()
      }, 2000)
      const ajaxSaveTask = await this.saveTaskAjax()

      // let { code, data } = ajaxSaveTask;
      let { code } = ajaxSaveTask
      if (code === 200) {
        let query = {
          taskInstanceId: this.formInfo.id
        }
        if (this.formInfo.status === 30) {
          query.submitReason = submitReason
        }
        // 提交接口
        this.$API.supplierlifecycle.submitTask(query).then((res) => {
          this.$hloading()
          // let { code, data } = res;
          let { code } = res
          if (code === 200) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            setTimeout(() => {
              this.$router.go(-1)
            }, 600)
            this.saveOldData()
          } else {
            this.$toast({
              content: res.msg || this.$t('提交失败，请重试!'),
              type: 'warning'
            })
          }
        })
      } else {
        this.$hloading()
        setTimeout(() => {
          this.$toast({
            content: ajaxSaveTask.msg || this.$t('获取表单定义模板数据，请重试!'),
            type: 'error'
          })
        }, 100)
      }
    },

    // 通过任务 不搞了
    aggressiveTask() {},

    // 拒绝 不搞了
    refuseTask() {}
  },
  destroyed() {
    this.$hloading()
    FORMLISTDATA.clear()
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../node_modules/@mtech-form-design/form-parser/build/esm/bundle.css';
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.task-center {
  margin-top: 20px;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  border-right: 2px solid rgba(232, 232, 232, 1);
  background: #fff;
  margin-bottom: 20px;
  height: calc(100% - 20px);

  .task-sidebar {
    width: 240px;
    height: 100%;
    background: #fff;
    padding-bottom: 20px;
  }

  .task-collapse {
    .collapse-header {
      height: 60px;
      line-height: 60px;
      padding-left: 30px;
      font-size: 16px;

      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      border-top: 1px solid rgba(232, 232, 232, 1);

      -moz-user-select: none;
      -ms-user-select: none;
      -webkit-user-select: none;
      user-select: none;
    }

    .collapse-header:nth-child(1) {
      border-top: none;
    }

    .active {
      color: rgba(0, 70, 156, 1);
      // border-bottom: 1px solid rgba(232,232,232,1);
    }
  }

  .task-content {
    border-left: 1px solid rgba(232, 232, 232, 1);
    display: flex;
    flex-direction: column;
    overflow-x: auto;

    .task-info {
      padding: 24px 30px 24px 20px;
      background: transparent;
      justify-content: space-between;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      .mian-info {
        .task-list {
          .reject-reason {
            color: #eda133;
          }
          flex-wrap: wrap;
          span {
            margin-right: 30px;
            font-size: 12px;

            font-weight: normal;
            color: rgba(100, 100, 100, 1);
          }
        }

        .categroy-list {
          margin-top: 14px;

          .factory-item {
            margin-right: 30px;
            flex-wrap: wrap;
            .factory-name {
              height: 20px;
              line-height: 20px;
              font-size: 12px;

              font-weight: 500;
              color: rgba(41, 41, 41, 1);
            }
            .factory-category {
              margin-left: 10px;
              height: 20px;
              line-height: 20px;
              padding: 0 4px;
              background: rgba(99, 134, 193, 0.1);
              border-radius: 2px;
              font-size: 12px;

              font-weight: 500;
              color: rgba(99, 134, 193, 1);
            }
          }
        }
      }
      .btn-box {
        white-space: nowrap;
        // width: calc( 48px + 48px + 30px);
        .normal-btn {
          padding: 0 10px;
          font-size: 14px;

          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
        .normal-btn:nth-child(1) {
          margin-right: 30px;
        }

        .err-tips {
          display: flex;
          i {
            position: relative;
            top: 3px;
            margin-right: 4px;
          }
        }
      }
    }

    .task-form {
      flex: 1;
      .side-bar {
        width: 160px;
        border-right: 1px solid rgba(232, 232, 232, 1);
        padding-bottom: 20px;
        .side-item {
          width: 100%;
          height: 50px;
          line-height: 50px;
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          padding-left: 20px;
          cursor: pointer;
          -moz-user-select: none;
          -ms-user-select: none;
          -webkit-user-select: none;
          user-select: none;
        }
        .active {
          color: #00469c;
          background: #f5f6f9;
        }
      }

      .form-content {
        padding: 30px 20px;
        height: calc(100vh - 360px);
        overflow: auto;
        -webkit-overflow-scrolling: auto;
        transform: rotate(1);
        position: relative;

        .parse-title {
          color: #292929;
          position: relative;
          display: flex;
          padding-left: 12px;
          margin-bottom: 20px;
          font-size: 14px;
          font-weight: 500;

          &::before {
            content: ' ';
            display: inline-block;
            position: absolute;
            width: 2px;
            height: 14px;
            background: #00469c;
            left: 0;
          }
        }

        .empty-box {
          margin: 0 auto;
          margin-top: 40px;
          font-size: 16px;
          color: #333;
          text-align: center;
        }

        .empty-container {
          height: 240px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate3d(-50%, -50%, 0);

          img {
            display: block;
            width: 200px;
            height: 160px;
          }

          .empty-txt {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #9a9a9a;
          }
        }

        .none {
          display: none;
        }
        .display-item {
          display: block;
          .mt-row {
            // min-height: 300px;
            .mt-form-item {
              min-height: 300px;
              .e-grid {
                min-height: 200px !important;
              }
              .e-content {
                min-height: 200px !important;
              }
            }
            /deep/.mt-data-grid {
              min-height: 200px !important;
              .e-grid {
                min-height: 200px !important;
              }
              .e-content {
                min-height: 200px !important;
              }
            }
          }
        }
      }
    }
  }
}
::v-deep .mt-form {
  .mt-col > div {
    pointer-events: auto !important;
  }
}
</style>

<style lang="scss">
@import '../../../../../node_modules/@mtech-form-design/form-parser/build/esm/bundle.css';
.task-center {
  flex: 1;

  .mt-tooptip {
    margin-right: 40px;
    line-height: 16px;
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .ivu-collapse {
    border: none !important;

    // normal
    .ivu-collapse-header {
      height: 50px;
      line-height: 50px;
      // header
      .accordion-header {
        width: 140px;
        font-size: 14px;

        color: rgba(41, 41, 41, 1);
        display: flex;

        .company-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .header-number {
          // width: ;
          color: #00469c;
        }
      }
    }

    .ivu-collapse-content {
      // children
      .sub-item {
        height: 50px;
        line-height: 50px;
        padding-left: 50px;
        background: #fff;
        font-size: 14px;
        border-bottom: 1px solid #e8e8e8;
        font-weight: 500;
        color: #292929;
        display: flex;
        align-items: center;
        cursor: pointer;

        .task-name {
          width: 130px;
        }

        .sub-tips {
          display: inline-block;
          padding: 0 4px;
          height: 20px;
          line-height: 20px;
          background: rgba(237, 161, 51, 0.1);
          border-radius: 2px;
          color: #eda133;
        }
      }

      .sub-item:last-child {
        border-bottom: none;
      }

      .active {
        background: rgba(245, 246, 249, 1);
        color: rgba(0, 70, 156, 1);
      }

      .grey {
        color: rgba(154, 154, 154, 1);
        .sub-tips {
          display: inline-block;
          padding: 0 4px;
          height: 20px;
          line-height: 20px;
          background: rgba(154, 154, 154, 0.1);
          border-radius: 2px;
          color: #9a9a9a;
        }
      }
    }

    // active
    .ivu-collapse-item-active {
      .header-wrap {
      }
    }
  }
  .ivu-collapse > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header {
    background: rgba(245, 246, 249, 1);
  }
  .ivu-collapse > .ivu-collapse-item > .ivu-collapse-header {
    padding-left: 30px;
  }
  .ivu-collapse-content {
    padding: 0;
  }
}
</style>
