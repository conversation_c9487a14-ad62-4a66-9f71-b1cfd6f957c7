<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="formObject">
        <mt-form-item prop="orgCode" :label="$t('处理意见')">
          <textarea
            v-model="formObject.approvalText"
            :multiline="true"
            :rows="6"
            style="width: 100%; background: #fff"
            maxlength="800"
            float-label-type="Never"
            :placeholder="$t('请输入驳回意见（800字以内）')"
          ></textarea>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      dialogTitle: this.$t('审批意见'),
      formObject: {
        approvalText: null
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    save() {
      // 调驳回接口，成功回调之后，调用下方的方法关闭弹窗
      this.$emit('confirm-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
</style>
