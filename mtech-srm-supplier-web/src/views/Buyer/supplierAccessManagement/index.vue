<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>
<script>
import { listColumnData } from './config/index'
import { exportData } from '@/utils/utils.js'

export default {
  components: {
    addDialog: require('./components/addDialog.vue').default
  },
  data() {
    return {
      addDialogShow: false,
      pageConfig: [
        {
          gridId: '78a00a47-eac0-4233-b090-40a624b8412c',
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                // {
                //   id: "Add",
                //   icon: "icon_solid_edit",
                //   title: "新增",
                // },
                // {
                //   id: 'change',
                //   icon: 'icon_solid_edit',
                //   title: this.$t('阶段调整')
                {
                  id: 'agree',
                  icon: 'icon_card_invite',
                  title: this.$t('批准')
                },
                {
                  id: 'disAgree',
                  icon: 'icon_card_invite',
                  title: this.$t('驳回')
                },
                {
                  id: 'export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            // allowPaging: false,
            columnData: listColumnData,
            asyncConfig: {
              // url: '/supplier/tenant/buyer/process/manager/categoryList'
              url: '/supplier/tenant/buyer/process/manager/category-certification',
              serializeList: (list) => {
                list.forEach((item) => {
                  console.log(item)
                  if (item.currentBuAuthProjectStage && item.currentBuAuthProjectStage.sceneName) {
                    item.sceneName = item.currentBuAuthProjectStage.sceneName
                  } else {
                    item.sceneName = ''
                  }
                })
                return list
              }
            },
            frozenColumns: 2
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      let records = e.data ? [e.data] : e.gridRef.getMtechGridRecords()
      const barName = e.toolbar.id
      if (barName == 'agree' || barName == 'disAgree') {
        if (records.length == 0) {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
          return
        }
        const idList = []
        for (let i = 0; i < records.length; i++) {
          const item = records[i]
          if (item.taskStageInstanceStatus != 20) {
            // 替换
            this.$toast({
              content: this.$t('仅可操作状态为待审核的数据'),
              type: 'warning'
            })
            return
          }
          idList.push(i.id)
        }
        if (barName == 'disAgree') {
          // 驳回弹窗填写驳回原因
          this.$dialog({
            modal: () => import('./components/disagreeDialog.vue'),
            data: {
              // processInstanceId:this.planDetail.processInstanceId,
            },
            success: () => {}
          })
          return
        }
        // 同意调同意接口
      }
      if (barName == 'Add') {
        this.handleAdd()
      }
      if (barName == 'export') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
    },
    handleClickCellTitle(e) {
      if (e.field == 'supplierInternalCode') {
        this.$router.push({
          path: '/supplier/pur/access-detail',
          query: {
            id: e.data.partnerArchiveId,
            orgId: e.data.orgId,
            partnerRelationId: e.data.partnerRelationId,
            categoryPartnerRelationId: e.data.categoryPartnerRelationId,
            inviteId: e.data.inviteId
          }
        })
      }
      if (e.field == 'authProjectCode') {
        this.$router.push({
          path: '/supplier/pur/category-certification-detail',
          query: {
            id: e.data.authProjectId,
            type: '0'
          }
        })
      }
    },
    // 切换新增弹窗显示状态
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },
    confirmSuccess() {},
    handleAdd() {
      console.log('点击新增')
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },
    // 导出
    handleExport() {
      const rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: rule.condition || '',
        rules: rule.rules || []
      }
      const requestUrl = {
        preName: 'supplierAcc',
        urlName: 'exportSupplierAccessList'
      }
      exportData(requestUrl, params)
    }
  }
}
</script>
<style lang="scss">
.full-height {
  height: 100%;
  .complete {
    width: 100%;
    height: 100%;
    // background: #845ec2;
    // background: #2C73D2;
    background: rgb(29, 112, 68);
    // background: #008e9b;
    line-height: 40px;
    text-align: center;
    color: #fff;
    // background: #008F7A;
  }
  .optional {
    width: 100%;
    height: 100%;
    // background: #845ec2;
    // background: #2C73D2;
    background: rgb(255, 227, 127);
    // background: #008e9b;
    line-height: 40px;
    text-align: center;
    // color: #fff;
    // background: #008F7A;
  }
  .ongoing {
    width: 100%;
    height: 100%;
    // background: #845ec2;
    background: #2c73d2;
    // background: #0089ba;
    line-height: 40px;
    text-align: center;
    color: #fff;
    // background: #008E9B;
    // background: #008F7A;
  }
  .noStart {
    width: 100%;
    height: 100%;
    // background: #845ec2;
    // background: #2C73D2;
    // background: #0089BA;
    // background: #008E9B;
    background: rgb(230, 230, 230);
    line-height: 40px;
    text-align: center;
    // color: #fff;
  }
}
</style>
