<template>
  <div class="task-center fbox flex1" :class="{ 'change-inside': !!changeId }">
    <div class="task-content flex1">
      <div class="task-info fbox">
        <div class="mian-info flex1">
          <div class="task-list fbox">
            <span class="back-btn" @click="goBack()" v-if="changeId">{{ $t('返回') }}</span>
            <span v-if="formTaskDTO.taskInstanceName"
              >{{ $t('调查表名称') }}：{{ formTaskDTO.taskInstanceName }}</span
            >
            <span>{{ $t('调查表类型') }}：{{ formTaskDTO.taskType | filterType }}</span>
          </div>
        </div>
      </div>
      <div class="task-form fbox">
        <div class="side-bar">
          <div
            class="side-item ellipsis active"
            v-for="item of formTemplateArr"
            :key="item.id"
            @click="scrollInto(item.id)"
          >
            {{ item.name }}
          </div>
        </div>

        <div class="form-content flex1 fbox" ref="formContent">
          <div class="template-box flex1">
            <template v-if="formTemplateArr.length > 0">
              <div
                v-for="item of formTemplateArr"
                :key="item.id"
                class="display-item"
                :ref="'formItem_' + item.id"
                :data-id="item.id"
              >
                <div class="parse-title">{{ item.name }}</div>
                <mt-parser :ref="`parser_${item.id}`" :form-conf="item.value" />
              </div>
            </template>
            <template v-else>
              <empty-data :msg="msg"></empty-data>
            </template>
          </div>
          <div class="change-box">
            <div class="title">{{ $t('变更内容') }}：</div>

            <div class="change-desc">
              <div v-for="(changeitem, index) of changeContent" :key="index">
                <div class="change-title">{{ changeitem.formName }}：</div>
                <div class="change-info" v-for="(item, index) of changeitem.changes" :key="index">
                  <div
                    class="change-item"
                    :class="{
                      'change-before': cindex === 0,
                      'change-after': cindex === 1
                    }"
                    v-for="(citem, cindex) of item"
                    :key="cindex"
                  >
                    <div class="c-title">{{ citem.key }}：</div>
                    <div class="c-detail">{{ citem.value }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EmptyData from '@/components/emptyData/emptyData.vue'
import Parser from '@mtech-form-design/form-parser'
import utils from '@/utils/utils.js'
// polyfill for scrollIntoView
import smoothscroll from 'smoothscroll-polyfill'
// kick off the polyfill!
smoothscroll.polyfill()
// 表单tab后面的数据
let FORMLISTDATA = new Map()
let FORMLISTDATA2 = new Map()

export default {
  components: {
    'mt-parser': Parser,
    EmptyData
  },
  filters: {
    filterType: function (taskType) {
      let resultText = '--'
      if (taskType === 0) {
        resultText = this.$t('企业准入')
      }
      if (taskType === 1) {
        resultText = this.$t('品类准入')
      }
      return resultText
    }
  },
  data() {
    return {
      msg: this.$t('暂无表单定义数据'),
      changeId: 0, // 是否是 调查表历史查看详情跳转过来的
      mapState: {
        10: this.$t('待填写'),
        20: this.$t('待审批'),
        30: this.$t('已驳回'),
        40: this.$t('已完成'),
        50: this.$t('已关闭')
      },
      // 表单模板
      formTemplateArr: [],
      changeContent: [],

      applyId2formName: {}, // id name 键值对
      formTaskDTO: {}
    }
  },
  props: {
    formTaskDTOTP: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {},
  watch: {
    formTaskDTOTP(newValue) {
      if (!!newValue && !utils.isEmpty(newValue)) {
        this.formTaskDTO = newValue
        this.initTaskCenter()
      }
    }
  },
  created() {
    console.log('info init')
    // 根据id判断是不是 调查表历史点击跳转过来的 query传值
    let changeId =
      !!this.$route.query && !!this.$route.query.changeid ? this.$route.query.changeid : ''
    if (changeId) {
      this.changeId = changeId
      this.formTaskDTO = Object.assign({}, this.$route.query, {
        applyId: this.$route.query.id
      }) // 触发watch initTaskCenter
    }
  },
  mounted() {
    this.formTaskDTO = this.formTaskDTOTP
    if (!!this.formTaskDTO && !utils.isEmpty(this.formTaskDTO)) {
      this.initTaskCenter()
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },

    // 重置表单
    resetFormData() {
      FORMLISTDATA.clear()
      this.formTemplateArr = []
    },

    // 通过ref初始化
    initTaskCenter() {
      // this.resetFormData()
      this.getAllFormDefine()
    },

    // 表单滚动
    scrollInto(id) {
      this.$refs['formItem_' + id][0].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    },

    isEmpty(value) {
      return utils.isEmpty(value)
    },

    async getAllFormDefine() {
      this.$loading()
      let { applyId: id } = this.formTaskDTO
      const formAllDetail = await this.queryAllFormData()
      let changeContent = []
      if (FORMLISTDATA.has(id)) {
        // 缓存获取数据
        this.formTemplateArr = FORMLISTDATA.get(id)
        this.applyId2formName = FORMLISTDATA2.get(id)
        // 存入表单数据
        this.$nextTick(() => {
          if (!utils.isEmpty(formAllDetail) && formAllDetail.length > 0) {
            formAllDetail.forEach((detail) => {
              if (!utils.isEmpty(detail) && !utils.isEmpty(detail.formData)) {
                this.$refs[`parser_${detail.applyFormId}`][0].setFormData(detail.formData)

                // 单个表的所有修改的stringify数据
                let changeContentString = detail.changeContent
                let parseChangeContent = {}
                try {
                  parseChangeContent = JSON.parse(changeContentString)
                  changeContent.push({
                    id: detail.applyFormId,
                    value: parseChangeContent.changeContent
                  })
                } catch (error) {
                  console.log(error)
                }
              }
            })
          }
        })
        this.$hloading()
        this.renderChangeContent(changeContent)
        return
      }

      this.$API.supplierInfoChange.getAllFormDefine({ applyId: id }).then((res) => {
        let { code, data } = res
        if (code === 200 && !utils.isEmpty(data)) {
          // 非可编辑的表单 循环disabled
          if (data.length > 0) {
            data.forEach((tmpItem) => {
              if (
                !utils.isEmpty(tmpItem) &&
                !utils.isEmpty(tmpItem.formDefineResponse) &&
                !utils.isEmpty(tmpItem.formDefineResponse.template)
              ) {
                let template = this.disableTemplate(tmpItem.formDefineResponse.template)
                this.formTemplateArr.push({
                  id: tmpItem.id,
                  value: template,
                  name: tmpItem.formName
                })
                this.applyId2formName[tmpItem.id] = tmpItem.formName
              }
            })
            // 缓存当前表单结构数据
            FORMLISTDATA.set(id, this.formTemplateArr)
            FORMLISTDATA2.set(id, this.applyId2formName)

            this.$nextTick(() => {
              // 存入表单数据
              if (!utils.isEmpty(formAllDetail) && formAllDetail.length > 0) {
                formAllDetail.forEach((detail) => {
                  if (!utils.isEmpty(detail) && !utils.isEmpty(detail.formData)) {
                    this.$refs[`parser_${detail.applyFormId}`][0].setFormData(detail.formData)

                    // 单个表的所有修改的stringify数据
                    let changeContentString = detail.changeContent
                    let parseChangeContent = {}
                    try {
                      parseChangeContent = JSON.parse(changeContentString)
                      changeContent.push({
                        id: detail.applyFormId,
                        value: parseChangeContent.changeContent
                      })
                    } catch (error) {
                      console.log(error)
                    }
                  }
                })
              }
              this.$hloading()
              this.renderChangeContent(changeContent)
            })
          }
        } else {
          this.$hloading()
          this.$toast({
            content: this.$t('获取表单定义模板失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    /**
     * 渲染右侧变化的内容 @todo 传值方式还不确定 需要后期确定
     *
     */
    renderChangeContent(changeContent) {
      let ccItemArr = []
      changeContent.forEach((item) => {
        let { id, value } = item
        let ccItem = []
        value.forEach((cv) => {
          ccItem.push([
            { key: cv.key, value: cv.oldValue },
            { key: cv.key, value: cv.newValue }
          ])
        })

        let formName = this.applyId2formName[id]
        ccItemArr.push({ formName, changes: ccItem })
      })
      this.changeContent = ccItemArr
      // console.log(ccItemArr)
    },

    // 获取all 表单生成器数据
    queryAllFormData() {
      let { applyId: id } = this.formTaskDTO
      return this.$API.supplierInfoChange
        .queryAllFormData({ applyId: id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            return data
          } else {
            return {}
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'warning'
          })
          this.$hloading()
        })
    },

    /**
     * disable不能操作的表单结构
     */
    disableTemplate(template) {
      !!template.fields &&
        template.fields.forEach((tItem) => {
          tItem.disabled = true
        })
      return template
    }
  },
  destroyed() {
    this.$hloading()
    FORMLISTDATA.clear()
    FORMLISTDATA2.clear()
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.change-inside {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  top: 0;
  height: 100%;
  z-index: 99;
}

.task-center {
  margin-top: 10px;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  border-right: 2px solid rgba(232, 232, 232, 1);
  // overflow: hidden;
  background: #fff;

  .task-collapse {
    .collapse-header {
      height: 60px;
      line-height: 60px;
      padding-left: 30px;
      font-size: 16px;

      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      border-top: 1px solid rgba(232, 232, 232, 1);

      -moz-user-select: none;
      -ms-user-select: none;
      -webkit-user-select: none;
      user-select: none;
    }

    .collapse-header:nth-child(1) {
      border-top: none;
    }

    .active {
      color: rgba(0, 70, 156, 1);
      // border-bottom: 1px solid rgba(232,232,232,1);
    }
  }

  .task-content {
    border-left: 1px solid rgba(232, 232, 232, 1);
    display: flex;
    flex-direction: column;

    .task-info {
      padding: 24px 30px 24px 20px;
      background: transparent;
      justify-content: space-between;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      background: #fafafa;

      .mian-info {
        .task-list {
          .back-btn {
            width: 48px;
            text-align: center;
            color: #00469c;
            margin-right: 30px;
            cursor: pointer;
            font-weight: 600;
          }

          .reject-reason {
            color: #eda133;
          }

          flex-wrap: wrap;
          span {
            margin-right: 30px;
            font-size: 12px;

            font-weight: normal;
            color: rgba(100, 100, 100, 1);
          }
        }
      }

      .btn-box {
        white-space: nowrap;

        .normal-btn {
          padding: 0 10px;
          font-size: 14px;

          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
        .normal-btn:nth-child(1) {
          margin-right: 30px;
        }

        .err-tips {
          display: flex;
          i {
            position: relative;
            top: 3px;
            margin-right: 4px;
          }
        }
      }
    }

    .task-form {
      flex: 1;
      background: #fff;

      .side-bar {
        width: 180px;
        border-right: 1px solid rgba(232, 232, 232, 1);
        padding: 20px 0 0 20px;

        .side-item {
          width: 100%;
          height: 50px;
          line-height: 50px;
          font-size: 14px;

          font-weight: normal;
          color: #9a9a9a;
          padding-left: 20px;

          cursor: pointer;
          -moz-user-select: none;
          -ms-user-select: none;
          -webkit-user-select: none;
          user-select: none;
        }
        .active {
          color: #292929;
          position: relative;

          &::before {
            content: ' ';
            display: inline-block;
            position: absolute;
            width: 2px;
            height: 14px;
            background: #00469c;
            top: 18px;
            left: 10px;
          }
        }
      }

      .form-content {
        overflow: auto;
        -webkit-overflow-scrolling: auto;
        transform: rotate(1);

        .template-box {
          padding: 30px 20px;
          position: relative;

          .parse-title {
            color: #292929;
            position: relative;
            display: flex;
            padding-left: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;

            &::before {
              content: ' ';
              display: inline-block;
              position: absolute;
              width: 2px;
              height: 14px;
              background: #00469c;
              left: 0;
            }
          }
        }

        .change-box {
          width: 218px;
          border-left: 1px solid #e8e8e8;
          padding: 20px;

          .title {
            font-size: 14px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .change-desc {
            margin-top: 20px;

            .change-title {
              font-size: 14px;
              font-weight: normal;
              color: rgba(41, 41, 41, 1);
              margin-bottom: 10px;
              margin-top: 20px;
            }

            .change-info {
              background: rgba(245, 245, 245, 1);
              border-radius: 8px;
              padding: 10px;
              margin-bottom: 10px;

              .change-item {
                margin-bottom: 20px;

                .c-title {
                  font-size: 14px;
                  font-weight: normal;
                  color: rgba(41, 41, 41, 1);
                  margin-bottom: 10px;
                }

                .c-detail {
                  font-size: 14px;
                  font-weight: normal;
                  color: #ed5633;
                }
              }

              .change-before {
                .c-detail {
                  color: #ed5633;
                }
              }

              .change-after {
                .c-detail {
                  color: #00469c;
                }
              }
            }
          }
        }

        .none {
          display: none;
        }
        .display-item {
          display: block;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.task-center {
  .mt-tooptip {
    margin-right: 40px;
    line-height: 16px;
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .ivu-collapse {
    border: none !important;

    // normal
    .ivu-collapse-header {
      height: 50px;
      line-height: 50px;
      // header
      .accordion-header {
        width: 140px;
        font-size: 14px;

        color: rgba(41, 41, 41, 1);
        display: flex;

        .company-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .header-number {
          // width: ;
          color: #00469c;
        }
      }
    }

    .ivu-collapse-content {
      // children
      .sub-item {
        height: 50px;
        line-height: 50px;
        padding-left: 50px;
        background: #fff;
        font-size: 14px;
        border-bottom: 1px solid #e8e8e8;
        font-weight: 500;
        color: #292929;
        display: flex;
        align-items: center;
        cursor: pointer;

        .task-name {
          width: 130px;
        }

        .sub-tips {
          display: inline-block;
          padding: 0 4px;
          height: 20px;
          line-height: 20px;
          background: rgba(237, 161, 51, 0.1);
          border-radius: 2px;
          color: #eda133;
        }
      }

      .sub-item:last-child {
        border-bottom: none;
      }

      .active {
        background: rgba(245, 246, 249, 1);
        color: rgba(0, 70, 156, 1);
      }

      .grey {
        color: rgba(154, 154, 154, 1);
        .sub-tips {
          display: inline-block;
          padding: 0 4px;
          height: 20px;
          line-height: 20px;
          background: rgba(154, 154, 154, 0.1);
          border-radius: 2px;
          color: #9a9a9a;
        }
      }
    }

    // active
    .ivu-collapse-item-active {
      .header-wrap {
        // color: rgba(0,70,156,1);
      }
    }
  }
  .ivu-collapse > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header {
    background: rgba(245, 246, 249, 1);
    // border-bottom: none!important;
  }
  .ivu-collapse > .ivu-collapse-item > .ivu-collapse-header {
    padding-left: 30px;
  }
  .ivu-collapse-content {
    padding: 0;
  }
}
</style>
