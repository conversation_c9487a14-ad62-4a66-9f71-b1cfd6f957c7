<template>
  <div class="questionaire-history flex1">
    <template v-if="historyList.length > 0">
      <div class="period-wrap">
        <!-- 阶段块 start-->
        <div
          class="period-item"
          :class="{ active: index === 0 }"
          v-for="(item, index) of historyList"
          :key="item.id"
        >
          <div class="pot-box"></div>
          <div class="period-time">{{ item.applyFinishTime | fliterTime }}</div>
          <div class="period-des fbox">
            <div class="des-name">{{ item.applyName }}</div>
            <div class="des-detail" @click="changeTask(item)">{{ $t('查看详情') }}</div>
            <div class="des-changer flex1">{{ item.applyerName || '--' }}</div>
          </div>
        </div>
        <!-- 阶段块 end-->
      </div>
    </template>
    <template v-if="historyList.length === 0 || !historyList">
      <empty-data :msg="msg"></empty-data>
    </template>

    <router-view></router-view>
  </div>
</template>

<script>
import EmptyData from '@/components/emptyData/emptyData.vue'
import utils from '@/utils/utils.js'
export default {
  components: {
    EmptyData
  },
  data() {
    return {
      historyList: [],
      msg: this.$t('获取调查表历史失败，请重试！')
    }
  },
  props: {
    formTaskDTO: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    formTaskDTO(newValue) {
      if (!!newValue && !utils.isEmpty(newValue)) {
        this.initTaskCenter(newValue)
      }
    }
  },
  filters: {
    fliterTime: function (value) {
      let formatData = ''
      if (value) {
        let applyFinishTime = value.replace(/T/g, ' ')
        formatData = utils.formateTime(applyFinishTime, this.$t('yyyy年MM月dd日 hh:mm:ss'))
      }
      return formatData
    }
  },
  created() {},
  mounted() {
    this.initTaskCenter(this.formTaskDTO)
  },
  methods: {
    changeTask(item) {
      this.$router.push({
        name: 'changeTask',
        query: {
          changeid: item.id,
          ...this.formTaskDTO,
          ...item
        }
      })
    },

    // 通过ref初始化
    initTaskCenter(formTaskDTO) {
      this.formTaskDTO = formTaskDTO
      this.applyHistory()
    },

    // 获取调查表历史
    applyHistory() {
      this.$loading()
      let { taskInstanceId } = this.formTaskDTO
      this.$API.supplierInfoChange
        .applyHistory({ taskInstanceId: taskInstanceId })
        .then((res) => {
          this.$hloading()
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            this.historyList = data
          } else {
            this.historyList = []
            this.msg = res.msg
            // this.$toast({
            //   content: "获取调查表历史为空，请重试！",
            //   type: "warning"
            // })
          }
        })
        .catch((err) => {
          this.historyList = []
          this.msg = err.msg
          this.$toast({
            content: err.msg,
            type: 'warning'
          })
          this.$hloading()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.questionaire-history {
  padding: 40px;
  width: 100%;
  background: #fff;
  position: relative;

  .period-wrap {
    position: relative;
  }

  .period-item {
    padding-left: 24px;
    position: relative;

    &::before {
      content: '';
      display: block;
      width: 1px;
      height: 100%;
      background: #e8e8e8;
      position: absolute;
      left: 5px;
      top: 0;
    }

    .pot-box {
      width: 12px;
      height: 14px;
      position: absolute;
      left: 0;
      top: 0;
      background: #fff;

      &::before {
        content: '';
        display: inline-block;
        width: 7px;
        height: 7px;
        border: 1px solid #9a9a9a;
        box-sizing: border-box;
        position: absolute;
        border-radius: 100%;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%, -50%, 0);
        z-index: 2;
      }

      &::after {
        content: '';
        width: 12px;
        height: 1px;
        background: #9a9a9a;
        position: absolute;
        top: 7px;
        left: 0;
        z-index: 1;
      }
    }

    .period-time {
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      font-weight: normal;
      color: #9a9a9a;
    }

    .period-des {
      padding: 30px 0;
      max-width: 400px;

      .des-name {
        font-size: 16px;
        font-weight: normal;
        color: #9a9a9a;
      }

      .des-detail {
        font-size: 14px;
        font-weight: normal;
        color: rgba(0, 70, 156, 1);
        margin-left: 10px;
        cursor: pointer;
      }

      .des-changer {
        font-size: 16px;
        font-weight: normal;
        color: #9a9a9a;
        text-align: right;
      }
    }
  }

  // 顶部的
  .active {
    padding-left: 24px;
    position: relative;

    &::before {
      content: '';
      display: block;
      width: 1px;
      height: 100%;
      background: #e8e8e8;
      position: absolute;
      left: 5px;
      top: 0;
    }

    .pot-box {
      width: 12px;
      height: 14px;
      position: absolute;
      left: 0;
      top: 0;
      background: #fff;

      &::before {
        content: '';
        display: inline-block;
        width: 7px;
        height: 7px;
        background: #00469c;
        border: none;
        box-sizing: border-box;
        position: absolute;
        border-radius: 100%;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%, -50%, 0);
        z-index: 2;
      }

      &::after {
        content: '';
        width: 12px;
        height: 1px;
        background: #00469c;
        position: absolute;
        top: 7px;
        left: 0;
        z-index: 1;
      }
    }

    .period-time {
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      font-weight: normal;
      color: #292929;
    }

    .period-des {
      padding: 30px 0;
      max-width: 400px;

      .des-name {
        font-size: 16px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
      }

      .des-detail {
        font-size: 14px;
        font-weight: normal;
        color: rgba(0, 70, 156, 1);
        margin-left: 10px;
      }

      .des-changer {
        font-size: 16px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        text-align: right;
      }
    }
  }
}
</style>
