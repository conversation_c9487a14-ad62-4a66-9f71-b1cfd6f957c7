// 认证需求详情描述
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <rich-text-editor
        ref="editor"
        :height="500"
        :disabled="isView"
        css-class="rich-editor"
        v-model.trim="richValue"
      ></rich-text-editor>
    </div>
  </mt-dialog>
</template>
<script>
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
export default {
  components: {
    RichTextEditor
  },
  data() {
    return {
      isView: true,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    richValue() {
      return this.modalData.info
    }
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    handleClose() {
      this.isOpen = false
    },

    cancel() {
      this.$emit('cancel-function')
    }
  },
  mounted() {
    this.show()
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-rich-text-editor {
  .e-richtexteditor {
    border: 0px;
  }
  .e-toolbar-wrapper {
    // height: 42px !important;
    display: none;
  }
  .e-rte-content {
    border: 0px;
    height: 464px !important;
    padding: 0px 7px;
    em {
      font: revert;
    }
    ol,
    ul {
      list-style: revert;
    }
  }
}
/deep/.dialog-content {
  .setting-banner {
    width: 100%;
    height: 50px;
    cursor: pointer;
    padding: 0 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(79, 91, 109, 1);

    i {
      font-size: 16px;
      line-height: 14px;
      display: inline-block;
      height: 16px;
      width: 16px;
      cursor: pointer;
    }

    span {
      display: inline-block;
      margin-left: 6px;
      cursor: pointer;
    }
  }

  .edit-box {
    padding: 0 20px;
    width: 100%;
    height: 100%;
    background: #fff;
  }
}
</style>
