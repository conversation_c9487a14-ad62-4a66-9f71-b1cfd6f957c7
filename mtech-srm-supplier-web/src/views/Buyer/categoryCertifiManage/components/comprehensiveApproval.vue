<!-- 综合审批 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      v-if="this.pageConfig[0].grid.columnData.length > 0"
      @cellEdit="cellEdit"
    ></mt-template-page>
  </div>
</template>
<script>
import { approvalColumn } from '../config/index.js'
import utils from '@/utils/utils'
import { download } from '@/utils/utils'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    isView() {
      return this.info.status != 10 && this.info.status != 30
    }
  },
  data() {
    return {
      arrData: [],
      pageConfig: [
        {
          // gridId: "d00d60e1-235a-4941-9573-7e95fd7caa4a",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'Submit',
                  icon: 'icon_table_submit',
                  title: this.$t('提交审批'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                }
              ]
            ]
          },
          grid: {
            lineSelection: true,
            columnData: [],
            asyncConfig: {
              url: '/supplier/tenant/buyer/auth/approve/pageQuery',
              params: {
                authProjectCode: this.info.projectCode
              },
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.fileInfos == null) {
                    item.fileInfos = []
                  }
                })
                this.arrData = JSON.parse(JSON.stringify(list))
                return list
              }
            },
            frozenColumns: 3
          }
        }
      ]
    }
  },
  mounted() {
    console.log('mounted==', this.info)
    this.initData()
  },
  methods: {
    cellEdit(data, type) {
      let obj = {}
      this.arrData.forEach((item) => {
        if (data.id == item.id) {
          obj = item
        }
      })
      if (typeof data[type] == 'string') {
        if (obj[type] !== data[type]) {
          obj[type] = data[type]
          let params = JSON.parse(JSON.stringify(obj))
          if (params.qualificationApproveResult == '') {
            this.$toast({ content: this.$t('请先选择资质审查结果'), type: 'warning' })
            return
          }
          if (JSON.stringify(params.fileInfos) == '[]') {
            this.$toast({ content: this.$t('请先上传附件'), type: 'warning' })
            return
          }
          delete params.column
          this.$API.CategoryCertification.updateApprove(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      } else {
        if (JSON.stringify(obj[type]) !== data[type]) {
          obj[type] = JSON.parse(JSON.stringify(data[type]))
          let params = JSON.parse(JSON.stringify(obj))
          if (params.qualificationApproveResult == '') {
            this.$toast({ content: this.$t('请先选择资质审查结果'), type: 'warning' })
            return
          }
          if (JSON.stringify(params.fileInfos) == '[]') {
            this.$toast({ content: this.$t('请先上传附件'), type: 'warning' })
            return
          }
          delete params.column
          this.$API.CategoryCertification.updateApprove(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      }
    },
    initData() {
      this.pageConfig[0].grid.columnData = approvalColumn(this.isView)
    },
    handleClickToolBar(e) {
      let _this = this
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (
        (!sltList || sltList.length <= 0) &&
        toolbar.id != 'Add' &&
        toolbar.id != 'Submit' &&
        toolbar.id != 'audit'
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id == 'Add') {
        _this.$API.CategoryCertification.getFinishedSupList({
          authProjectCode: _this.info.projectCode,
          page: { current: 1, size: 10000 }
        }).then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.$dialog({
              modal: () => import('./dialog/approveTempAddDialog.vue'),
              data: {
                title: this.$t('新增综合审批'),
                info: { supplierList: res.data }
              },
              success: () => {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          } else {
            this.$toast({
              content: this.$t('认证项目未引入供应商!'),
              type: 'warning'
            })
            return
          }
        })
      } else if (toolbar.id == 'Delete') {
        let ids = sltList.map((e) => e.id)
        this.delSupplier(ids)
      } else if (toolbar.id == 'Submit') {
        // let _status = sltList.map((e) => Number(e.status));
        // if (_status.includes(20) || _status.includes(40)) {
        //   this.$toast({
        //     content: this.$t("已提交的数据不能重复提交"),
        //     type: "warning",
        //   });
        //   return;
        // }
        // let ids = sltList.map((e) => e.id);z
        this.$loading()
        this.$API.CategoryCertification.checkQualification({
          projectCode: this.info.projectCode
        })
          .then((res) => {
            if (res.code == 200 && res.data == null) {
              this.submitApprove({
                projectCode: this.info.projectCode,
                toContinue: true
              })
            } else {
              this.$hloading()
              this.$dialog({
                data: {
                  title: this.$t('提示'),
                  message:
                    res.data +
                    this.$t('供应商已进行资质审查，但综合审批中缺少该供应商，是否继续提交审批？')
                },
                success: () => {
                  this.submitApprove({
                    projectCode: this.info.projectCode,
                    toContinue: true
                  })
                }
              })
            }
          })
          .catch(() => {
            this.$hloading()
          })
      } else if (toolbar.id === 'audit') {
        this.audit(sltList)
      }
    },
    submitApprove(param) {
      this.$loading()
      this.$API.CategoryCertification.submitApprove(param)
        .then((res) => {
          this.$hloading()
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].id,
        businessType: '',
        businessKey: sltList[0].approveCode
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'delete') {
        this.delSupplier([data.id])
      } else if (id == 'edit') {
        let obj = {}
        this.arrData.forEach((item) => {
          if (data.id == item.id) {
            obj = item
          }
        })
        this.$dialog({
          modal: () => import('./dialog/approveTempAddDialog.vue'),
          data: {
            title: this.$t('新增综合审批'),
            info: obj,
            isEdit: true
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (id == 'download') {
        this.handleDownloadFile(data)
      }
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPublicFile({
          id: data.fileId
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: data.fileName, blob: res.data })
        })
    },
    delSupplier(param) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选供应商？'),
          confirm: () => this.$API.CategoryCertification.delApprove({ ids: param })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
