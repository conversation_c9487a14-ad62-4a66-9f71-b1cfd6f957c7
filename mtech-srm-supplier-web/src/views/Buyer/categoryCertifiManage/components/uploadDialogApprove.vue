<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="file" class="form-item">
              <div class="cell-upload">
                <div class="to-upload">
                  <!-- multiple="multiple" -->
                  <input
                    ref="file"
                    type="file"
                    class="upload-input"
                    multiple="multiple"
                    @change="chooseFiles"
                    accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
                  />
                  <div class="upload-box">
                    <div class="plus-icon"></div>
                    <div class="right-state">
                      <div class="plus-txt">
                        {{ $t('请拖拽文件或点击上传') }}
                      </div>
                      <div class="warn-text">
                        {{
                          $t(
                            '注：文件最大不可超过50M， 文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="has-file" v-for="(item, index) in uploadInfo" :key="'a' + index">
                  <div class="left-info">
                    <div class="file-title">
                      <span class="text-ellipsis">
                        {{ item.fileName }}
                      </span>
                      <span>{{ (item.fileSize / 1024).toFixed(2) }} kb</span>
                    </div>
                  </div>
                  <mt-icon
                    name="icon_Close_2"
                    class="close-icon"
                    @click.native="handleRemove(index)"
                  ></mt-icon>
                </div>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
export default {
  data() {
    return {
      allowFileType: [
        'xls',
        'xlsx',
        'doc',
        'docx',
        'pdf',
        'ppt',
        'pptx',
        'png',
        'jpg',
        'zip',
        'rar'
      ],
      formInfo: {},
      rules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      uploadInfo: [] // 上传后信息
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.uploadInfo = this.modalData.info || []
    this.show()
  },
  methods: {
    chooseFiles(data) {
      this.$loading()
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      } else if (files.length > 5) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
      })
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      bol = files.some((item) => {
        return item.size > params.limit * 1024
      })
      if (bol) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      files.forEach((item, index) => {
        let _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
        this.uploadFile(_data, index == files.length - 1)
      })
    },
    // 上传图片
    uploadFile(data, bol) {
      this.$refs.file.value = ''
      this.$API.SupplierPunishment.fileUploadSit(data)
        .then((res) => {
          const { code, data } = res
          if (bol) {
            this.$hloading()
          }
          if (code == 200 && !utils.isEmpty(data)) {
            this.uploadInfo.push({
              ...data
            })
            if (bol) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          } else {
            this.$toast({
              content: data.msg || this.$t('上传失败'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          if (bol) {
            this.$hloading()
          }
          this.$toast({
            content: error.msg || this.$t('上传失败'),
            type: 'warning'
          })
        })
    },

    // 移除文件
    handleRemove(index) {
      this.uploadInfo.splice(index, 1)
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$emit('confirm-function', this.uploadInfo)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.uploader-box {
  .mt-form-item__content {
    flex: 1;
  }
}
</style>
<style lang="scss" scoped>
.uploader-box {
  padding: 40px 20px 0;
}

.cell-upload {
  margin-top: 15px;
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          width: 100%;
          display: inline-block;
          vertical-align: middle;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
