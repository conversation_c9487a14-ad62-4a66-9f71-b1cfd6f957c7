// 品类认证项目
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel" :open="onOpen">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('人员')" label-style="top" prop="userId">
          <!-- <mt-select
            v-model="formInfo.userId"
            :data-source="employeeList"
            :fields="{ text: 'employeeName', value: 'id' }"
            @change="userChange"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
          ></mt-select> -->
          <!-- <RemoteAutocomplete
            ref="staffRef"
            :params="{
              organizationId: formInfo.deptId
            }"
            v-model="formInfo.userId"
            :fields="{ text: 'employeeName', value: 'employeeCode' }"
            url="/masterDataManagement/tenant/employee/paged-query"
            @change="userChange"
            :title-switch="false"
            :width="414"
            :placeholder="$t('请选择')"
            select-type="staff"
          ></RemoteAutocomplete> -->
          <mt-select
            v-model="formInfo.userId"
            :data-source="auditManList"
            :fields="{
              text: 'employeeName',
              value: 'id'
            }"
            :placeholder="$t('请输入关键字搜索选择人员')"
            popup-width="370"
            :allow-filtering="true"
            :filtering="getAuditMan"
            :open-dispatch-change="true"
            :width="414"
            @change="userChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('所属部门')" label-style="top" prop="deptCode">
          <!-- <mt-select
            v-model="formInfo.deptCode"
            :data-source="deptList"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
            :allowFiltering="true"
            :filtering="onFiltering"
            @change="deptChange"
          ></mt-select> -->
          <!-- <RemoteAutocomplete
            :params="{
              organizationIds,
              organizationLevelCodes: ['ORG03'],
              orgType: 'ORG001ADM',
              includeItself: true
            }"
            v-model="formInfo.deptCode"
            :fields="{ text: 'departmentName', value: 'departmentCode' }"
            url="/masterDataManagement/tenant/department/paged-query"
            @change="deptChange"
            :title-switch="false"
            :width="414"
            :placeholder="$t('请选择')"
            select-type="administrativeCompany"
          ></RemoteAutocomplete> -->
          <mt-input
            :disabled="true"
            v-model="formInfo.deptName"
            float-label-type="Never"
            width="414"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('邮箱')" label-style="top" prop="email">
          <mt-input
            :disabled="true"
            v-model="formInfo.email"
            :placeholder="$t('请输入')"
            float-label-type="Never"
            width="414"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('电话')" label-style="top" prop="mobile">
          <mt-input
            :disabled="true"
            v-model="formInfo.mobile"
            :placeholder="$t('请输入')"
            float-label-type="Never"
            width="414"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('角色')" label-style="top" prop="roleId">
          <mt-select
            v-model="formInfo.roleId"
            :data-source="roleList"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            @change="roleChange"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('职责描述：')"
          label-style="top"
          prop="dutyDesc"
        >
          <mt-input
            v-model="formInfo.dutyDesc"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            :multiline="true"
            maxlength="200"
            :rows="1"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
// import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import debounce from 'lodash.debounce'

export default {
  components: {
    // RemoteAutocomplete
  },
  data() {
    return {
      auditManList: [], // 人员列表
      organizationIds: [],
      rules: {
        deptCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        userId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        roleId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      formInfo: {
        authProjectCode: this.modalData.info.projectCode,
        authProjectId: this.modalData.info.projectId
      },
      deptList: [],
      // employeeList: [],
      roleList: [],
      employeeId: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      loadCompanyData: false,
      loadStaffData: false, // :loadData会监听去控制每次置为true就会重新发请求下拉组件数据

      flag: false,
      userflag: false,
      isUserChange: false,
      isDeptChange: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  async created() {
    await this.getDepartments()
    this.getAuditMan = debounce(this.getAuditMan, 1000)
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 获取人员列表
    getAuditMan(val) {
      let params = {
        page: {
          current: 1,
          size: 20
        },
        subjectType: 0
      }
      if (val) {
        params['condition'] = 'or'
        params['rules'] = [
          {
            label: this.$t('账号'),
            field: 'externalCode',
            type: 'string',
            operator: 'contains',
            value: val.text
          },
          {
            label: this.$t('姓名'),
            field: 'employeeName',
            type: 'string',
            operator: 'contains',
            value: val.text
          }
        ]
      }
      this.$API.purChangeRequest.employee(params).then((res) => {
        if (res.code == 200 && res.data.records != null) {
          const auditManList = res.data.records.map((item) => {
            item.employeeName = item.externalCode + '-' + item.employeeName
            return item
          })
          const newArr = auditManList.concat(this.auditManList)
          let map = new Map()
          for (let item of newArr) {
            map.set(item.externalCode, item)
          }
          this.auditManList = [...map.values()]
        }
      })
    },
    onOpen: function (args) {
      args.preventFocus = true
    },
    onFiltering(e) {
      e.updateData(
        this.deptList.filter((x) => x.orgName.toUpperCase().includes(e.text.toUpperCase()))
      )
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    async initData() {
      console.log('getRoleList', this.info)
      // await this.getDepartments();
      await this.getRoleList()
      if (this.isEdit) {
        this.$nextTick(() => {
          this.auditManList = [
            {
              employeeName: this.info.userName,
              id: this.info.userId
            }
          ]
          this.formInfo = {
            ...this.info
          }
        })
      }
      await this.show()
    },
    async getRoleList() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'groupRoleType'
      }).then((res) => {
        if (res.code == 200) {
          this.roleList = res.data
        }
      })
    },
    async getDepartments() {
      await this.$API.masterData.getUserInfo().then((res) => {
        if (res.code == 200) {
          this.employeeId = res.data.employeeId
          let arr = res.data.orgList
            .filter((item) => {
              return item.orgLevelTypeCode == 'ORG02'
            })
            .map((item) => {
              return item.id
            })
          this.organizationIds = arr
          this.loadCompanyData = true
          // this.$API.masterData
          //   .findSpecifiedChildrenLevelOrgs({
          //     includeItself: true,
          //     orgType: "ORG001ADM",
          //     organizationIds: arr,
          //     organizationLevelCodes: ["ORG03"]
          //   })
          //   .then((res) => {
          //     this.deptList = res.data;
          //   });
        }
      })
    },
    deptChange(e) {
      if (this.isEdit && !this.flag) {
        this.flag = true
        return
      }
      this.formInfo.deptId = e.itemData.id
      this.formInfo.deptName = e.itemData.orgName
      // this.getEmployee(this.formInfo.deptId);
      // 切换部门时，先清空人员
      this.isDeptChange = true
      if (!this.isUserChange) {
        // this.$refs.staffRef.clear()
        this.formInfo.userName = null
        this.formInfo.userId = null
        this.formInfo.email = null
        this.formInfo.mobile = null
      } else {
        // this.isUserChange = false
      }
      // :loadData会监听去控制每次置为true就会重新发请求下拉组件数据
      this.loadStaffData = true
      // 加延迟将:loadData置为false，否则组件无法watch监听变化
      setTimeout(() => {
        this.loadStaffData = false
      }, 500)
    },
    // getEmployee(id) {
    //   this.$API.masterData
    //     .getOrganizationEmployees({
    //       orgId: id,
    //     })
    //     .then((res) => {
    //       this.employeeList = res.data;
    //     });
    // },
    userChange(e) {
      if (this.isEdit && !this.userflag) {
        this.userflag = true
        return
      }
      this.formInfo.userName = e.itemData.employeeName
      this.formInfo.email = e.itemData.email
      this.formInfo.mobile = e.itemData.phoneNum
      this.formInfo.deptName = e.itemData.departmentName
      this.formInfo.deptCode = e.itemData.departmentCode

      // if (!this.isDeptChange) {
      //   this.isUserChange = true
      //   this.formInfo.deptCode = e.itemData.departmentCode
      //   this.formInfo.deptId = e.itemData.departmentOrganizationId
      //   this.formInfo.deptName = e.itemData.departmentName
      // } else {
      //   this.isDeptChange = false
      // }

      if (
        this.formInfo.deptId !== e.itemData.departmentOrganizationId &&
        e.itemData.departmentOrganizationId
      ) {
        this.isUserChange = true
        this.formInfo.deptCode = e.itemData.departmentCode
        this.formInfo.deptId = e.itemData.departmentOrganizationId
        this.formInfo.deptName = e.itemData.departmentName
      }
      this.isDeptChange = false
      setTimeout(() => {
        this.isUserChange = false
      }, 1500)
    },
    roleChange(e) {
      // 新增时 或 编辑时切换角色 显示角色对应的默认职责描述
      if (!this.isEdit || (this.isEdit && e.itemData.itemName !== this.formInfo.roleName)) {
        if (e.itemData.itemCode == '1') {
          // 决策人
          this.formInfo.dutyDesc = this.$t('对认证小组输出的策略共识结果进行决策')
        } else if (e.itemData.itemCode == '2') {
          // 执行人
          this.formInfo.dutyDesc = this.$t(
            '分析认证需求，根据现有供应商资源池及未来资源池规划，讨论并输出认证策略'
          )
        }
      }
      this.formInfo.roleName = e.itemData.itemName
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'updateGroup' : 'addGroup'
          this.$API.CategoryCertification[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.form-item-spec {
  width: 414px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .full-width {
  width: 100% !important;
}
</style>
