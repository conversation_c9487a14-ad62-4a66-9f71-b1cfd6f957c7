// 引入供应商
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      v-if="this.pageConfig[0].grid.columnData.length > 0"
    ></mt-template-page>
  </div>
</template>
<script>
import { sceneDefineList, supplierViewColumn } from '../config/index.js'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      supplierList: [],
      pageConfig: [
        {
          // gridId:"7f9533cf-63c1-4f0c-9c29-e89338a72a64",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  visibleCondition: () => !this.isView
                }
              ]
            ]
          },

          grid: {
            lineSelection: true,
            columnData: [],
            dataSource: []
            // asyncConfig: {
            //   url: '/supplier/tenant/buyer/auth/supplier/query',
            //   params: {
            //     authProjectCode: this.info.projectCode
            //   }
            // }
          }
        }
      ]
    }
  },
  computed: {
    isView() {
      return this.info.status != 10 && this.info.status != 30
    }
  },
  mounted() {
    this.initData()
  },

  methods: {
    async initData() {
      // 获取场景定义枚举
      await this.querySceDefByOrgCate()
      this.pageConfig[0].grid.columnData = supplierViewColumn(this.isView)
      this.handleSearch()
    },
    // 分页查询场景定义
    async querySceDefByOrgCate() {
      sceneDefineList.length = 0
      await this.$API.AccessProcess.querySceDefByOrgCate({
        categoryId: this.info.categoryId,
        orgId: this.info.orgId,
        sourceNo: this.info.sourceNo
      })
        .then((res) => {
          if (res.code == 200) {
            res.data.forEach((e) => sceneDefineList.push(e))
          } else {
            this.$toast({
              content: res.msg || this.$t('系统异常'),
              type: 'error'
            })
            return []
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
          return []
        })
    },
    handleSearch() {
      this.$loading()
      this.$API.CategoryCertification.importedSupplier({
        authProjectCode: this.info.projectCode,
        page: {
          current: 1,
          size: 20
        }
      })
        .then((res) => {
          if (res.code == 200) {
            let records = res.data.records
            if (records?.length > 0) {
              this.queryOtherField(records[0].id, records)
            } else {
              this.$set(this.pageConfig[0].grid, 'dataSource', [])
            }
          }
        })
        .finally(() => {
          this.$hloading()
        })
    },
    async queryOtherField(id, records) {
      let params = {
        authProjectSupplierId: id
      }
      this.$API.CategoryCertification.queryByAuthProjectSupplierIdApi(params).then((res) => {
        if (res.code === 200) {
          if (res.data.length > 0) {
            records.forEach((e) => {
              let exitSupplier = 1
              let exitSupplierCodeStr = ''
              let exitOrgCodeStr = ''
              let exitReasonStr = ''
              res.data.forEach((item) => {
                exitSupplier = item.exitSupplier
                exitSupplierCodeStr += item?.exitSupplierCode + '/'
                exitOrgCodeStr += item?.exitOrgCode + '/'
                exitReasonStr += item?.exitReason + '/'
              })
              e.exitSupplier = exitSupplier
              e.exitSupplierCode = exitSupplierCodeStr.replace(/\//g, '')
              e.exitOrgCode = exitOrgCodeStr.replace(/\//g, '')
              e.exitReason = exitReasonStr.replace(/\//g, '')
            })
            this.$set(this.pageConfig[0].grid, 'dataSource', records)
          } else {
            records.forEach((e) => {
              let exitSupplier = 0
              let exitSupplierCodeStr = ''
              let exitOrgCodeStr = ''
              let exitReasonStr = ''
              e.exitSupplier = exitSupplier
              e.exitSupplierCode = exitSupplierCodeStr
              e.exitOrgCode = exitOrgCodeStr
              e.exitReason = exitReasonStr
            })
            this.$set(this.pageConfig[0].grid, 'dataSource', records)
          }
        }
      })
    },
    // 获取供应商下拉
    async getSupplier(param) {
      console.log('param', param)
    },
    handleClickToolBar(e) {
      console.log('===', e)
      let _this = this
      const { toolbar, grid, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Add') {
        this.$API.CategoryCertification.getSupListByCategory(_this.info).then((res) => {
          _this.supplierList.length = 0
          let _tempSup = grid.getCurrentViewRecords().map((e) => e.supplierInternalCode)
          res.data.forEach((e) => {
            if (!_tempSup.includes(e.supplierInternalCode)) {
              let supplierStatusDesc = ''
              if (e.supplierStatus == 1) {
                supplierStatusDesc = this.$t('注册')
              } else if (e.supplierStatus == 2) {
                supplierStatusDesc = this.$t('潜在')
              } else if (e.supplierStatus == 10) {
                supplierStatusDesc = this.$t('合格')
              } else if (e.supplierStatus == 20) {
                supplierStatusDesc = this.$t('冻结')
              } else if (e.supplierStatus == 30) {
                supplierStatusDesc = this.$t('黑名单')
              } else if (e.supplierStatus == 40) {
                supplierStatusDesc = this.$t('退出')
              }
              this.supplierList.push({
                ...e,
                authProjectCode: _this.info.projectCode,
                authProjectId: _this.info.projectId,
                value: e.id,
                text: e.supplierCode + ' ' + e.supplierName + ' ' + supplierStatusDesc
              })
            }
          })
          console.log('_this.supplierList', _this.supplierList, sltList, _tempSup)
          if (_this.supplierList.length > 0) {
            this.$dialog({
              modal: () => import('./addSupplierDialog.vue'),
              data: {
                title: this.$t('新增供应商'),
                info: { ..._this.info },
                supplierList: _this.supplierList
              },
              success: () => {
                // _this.$refs.templateRef.refreshCurrentGridData()
                _this.handleSearch()
              }
            })
          } else {
            this.$toast({
              content: this.$t('没有可新增的供应商'),
              type: 'warning'
            })
            return
          }
        })
      } else if (toolbar.id == 'Delete') {
        let ids = sltList.map((e) => e.id)
        this.delSupplier(ids.toString())
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'delete') {
        this.delSupplier(data.id)
      }
    },
    delSupplier(param) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选供应商？'),
          confirm: () => this.$API.CategoryCertification.deleteSupplier({ ids: param })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          // this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
          this.handleSearch()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
