// 品类认证项目
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('名称')" label-style="top" prop="projectName">
          <mt-input
            v-model="formInfo.projectName"
            :maxlength="20"
            :placeholder="$t('请输入')"
            float-label-type="Never"
            width="414"
            :disabled="this.isEdit"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('认证来源')"
          label-style="top"
          prop="demandCode"
        >
          <mt-select
            :show-clear-button="true"
            :allow-filtering="true"
            v-model="formInfo.demandCode"
            v-if="isSourceShow"
            :data-source="demandList"
            :fields="{ text: 'text', value: 'demandCode' }"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
            @change="demandCodeChange"
            :disabled="this.isEdit || JSON.stringify(info) != '{}'"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('需求品类')"
          label-style="top"
          prop="categoryCode"
        >
          <mt-select
            v-if="isCateShow"
            v-model="formInfo.categoryCode"
            :data-source="categoryList"
            :allow-filtering="true"
            :filtering="(e) => filteringResource(e, categoryList, 'categoryName')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
            @change="categoryCodeChange"
            :disabled="formInfo.demandCode != '-99' || this.isEdit"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('需求公司')"
          label-style="top"
          prop="orgCode"
        >
          <!-- <mt-select
            v-model="formInfo.orgId"
            v-if="isOrgShow"
            :data-source="orgList"
            :fields="{ text: 'orgName', value: 'id' }"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
            @change="orgChange"
            :disabled="formInfo.demandCode != '-99' || this.isEdit"
          ></mt-select> -->
          <RemoteAutocomplete
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            v-model="formInfo.orgCode"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            @change="orgChange"
            :disabled="formInfo.demandCode != '-99' || this.isEdit"
            :title-switch="false"
            :width="414"
            :placeholder="$t('请选择')"
            select-type="administrativeCompany"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('采购负责人')"
          label-style="top"
          prop="purchaseUserId"
        >
          <mt-select
            v-model="formInfo.purchaseUserId"
            v-if="isUserShow"
            :data-source="userList"
            :fields="{ text: 'text', value: 'employeeId' }"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
            @change="purchaseUserChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('需求人')"
          label-style="top"
          prop="demandUserId"
        >
          <mt-select
            v-model="formInfo.demandUserId"
            v-if="isUserShow"
            :data-source="userList"
            :fields="{ text: 'text', value: 'employeeId' }"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            width="414"
            @change="demandUserChange"
            :disabled="this.isEdit || formInfo.demandCode != '-99'"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import { cloneDeep, debounce } from 'lodash'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  data() {
    return {
      isCateShow: true,
      isSourceShow: true,
      isUserShow: true,
      // isOrgShow: true,
      demandList: [],
      userList: [],
      categoryList: [],
      // orgList: [],
      rules: {
        projectName: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        demandCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orgCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        purchaseUserId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        demandUserId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      formInfo: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    totalDemendCode() {
      return this.modalData.totalDemendCode
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.show()
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    async initData() {
      this.$loading()
      if (this.isEdit) {
        this.demandList.length = 0
        this.demandList.push({ demandCode: '-99', text: this.$t('手动创建') })
        this.$nextTick(() => {
          this.formInfo = {
            ...this.info
          }
        })
      } else {
        let res = await this.getDemands()
        this.isSourceShow = false
        this.demandList.length = 0
        this.demandList.push({ demandCode: '-99', text: this.$t('手动创建') })
        if (this.info) {
          this.formInfo = {
            ...this.info
          }
        }
        res.data.records.forEach((item) => {
          if (this.totalDemendCode.indexOf(item.demandCode) < 0) {
            this.demandList.push({
              ...item,
              text: this.$t(`${item.demandName}：${item.demandCode}`)
            })
          }
        })
        setTimeout(() => {
          this.isSourceShow = true
        }, 10)
      }
      const categoryName = this.formInfo.categoryName
      const len = categoryName?.split('-').length
      const orgCategoryName = len > 1 ? categoryName?.split('-')[len - 1] : ''
      await this.getCategorys(orgCategoryName)
      // this.getOrgCompanys();
      await this.getEmployees()
    },
    getDemands() {
      return this.$API.CategoryCertification.demandQuery({
        page: { current: 1, size: 10000 }
      })
    },
    async getCategorys(val) {
      await this.$API.supplierInvitation
        .getCategoryLists({
          dataLimit: 20,
          patternKeyword: val,
          permissionFlag: 1,
          permissionUserId: JSON.parse(sessionStorage.getItem('userInfo')).uid
        })
        .then((res) => {
          this.isCateShow = false
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.categoryName = item.categoryCode + '-' + item.categoryName
          })
          this.categoryList = _data
          if (this.formInfo.categoryCode) {
            this.categoryList.forEach((item) => {
              if (item.categoryCode == this.formInfo.categoryCode) {
                this.formInfo.categoryId = item.id
                this.formInfo.categoryName = item.categoryName
              }
            })
          }
          setTimeout(() => {
            this.isCateShow = true
          }, 10)
        })
    },
    // getOrgCompanys() {
    //   this.$API.masterData.getUserInfo().then((res) => {
    //     if (res.code == 200) {
    //       // this.employeeId = res.data.employeeId;
    //       // this.$API.masterData
    //       //   .getOrgCompanysByEmpId({ employeeId: this.employeeId })
    //       // this.$API.masterData
    //       //   .getOrgCompanysByEmpId2({
    //       //     organizationLevelCodes: ["ORG02", "ORG01"],
    //       //     orgType: "ORG001PRO",
    //       //     includeItself: true,
    //       //   })
    //       //   .then((res) => {
    //       //     this.isOrgShow = false;
    //       //     this.orgList = res.data;
    //       //     setTimeout(() => {
    //       //       this.isOrgShow = true;
    //       //     }, 10);
    //       //   });
    //     }
    //   });
    // },
    // 获取 员工列表
    async getEmployees() {
      await this.$API.masterData.getCurrentTenantEmployees().then((res) => {
        res.data.forEach((item) => {
          this.isUserShow = false
          this.userList.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        if (this.userList.length > 0 && !this.isEdit) {
          this.$set(this.formInfo, 'purchaseUserId', this.userList[0].employeeId)
          this.formInfo.purchaseUserName = this.userList[0].employeeName
        }
        setTimeout(() => {
          this.isUserShow = true
        }, 10)
        this.$hloading()
      })
    },
    demandCodeChange(e) {
      if (e.value != '-99') {
        // this.formInfo.demandCreateTime = e.itemData.createTime;
        this.formInfo.demandId = e.itemData.id
        this.formInfo.demandName = e.itemData.demandName
        this.formInfo.source = '1'
        this.formInfo.demandUserId = e.itemData.demandUserId
        this.$set(this.formInfo, 'categoryCode', e.itemData.categoryCode)
        this.$set(this.formInfo, 'orgId', e.itemData.orgId)
        const categoryName = e.itemData.categoryName
        const len = categoryName?.split('-').length
        const orgCategoryName = len > 1 ? categoryName?.split('-')[len - 1] : ''
        this.getCategorys(orgCategoryName)
      } else if (!this.isEdit) {
        delete this.formInfo.demandId
        delete this.formInfo.demandName
        this.$set(this.formInfo, 'demandUserId', this.userList[0].employeeId)
        this.$set(this.formInfo, 'categoryCode', null)
        this.$set(this.formInfo, 'orgId', null)
        this.formInfo.source = '2'
      }
    },
    categoryCodeChange(e) {
      this.formInfo.categoryId = e.itemData?.id
      this.formInfo.categoryName = e.itemData?.categoryName
    },
    purchaseUserChange(e) {
      if (e.itemData) {
        this.formInfo.purchaseUserName = e.itemData.employeeName
      }
    },
    demandUserChange(e) {
      this.formInfo.demandUserName = e.itemData.employeeName
    },
    orgChange(e) {
      this.formInfo.orgCode = e.itemData.orgCode
      this.formInfo.orgName = e.itemData.orgName
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$loading()
          const methodName = this.isEdit ? 'updateProject' : 'addProject'
          this.$API.CategoryCertification[methodName](this.formInfo)
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .cathc(() => {
              this.$hloading()
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 模糊搜索，不区分大小写模糊搜索
    filteringResource: debounce(async function (e, dataSource) {
      if (typeof e.text === 'string' && e.text) {
        const res = await this.$API.supplierInvitation.getCategoryLists({
          dataLimit: 20,
          patternKeyword: e.text,
          permissionFlag: 1,
          permissionUserId: JSON.parse(sessionStorage.getItem('userInfo')).uid
        })
        if (res.code === 200) {
          let _categoryList = cloneDeep(res.data)
          _categoryList.map((item) => {
            item.categoryName = item.categoryCode + '-' + item.categoryName
          })
          e.updateData(_categoryList)
        }
        // e.updateData(
        //   dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        // )
      } else {
        e.updateData(dataSource)
      }
    }, 300)
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.form-item-spec {
  width: 414px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
</style>
