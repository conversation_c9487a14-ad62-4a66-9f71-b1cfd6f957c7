// 供应商公示
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>
<script>
import { supplierPublicityColumn } from '../config/index.js'
export default {
  props: {
    info: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          // gridId:"ffeef521-4f81-436f-87d8-b790e81fd884",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  visibleCondition: () => !this.isView
                }
                // {
                //   id: "Publicity",
                //   icon: "icon_table_submit",
                //   title: this.$t("提交"),
                //   visibleCondition: () => !this.isView,
                // },
              ]
            ]
          },
          grid: {
            lineSelection: true,
            columnData: [],
            asyncConfig: {
              url: '/supplier/tenant/buyer/after/effective/publicity/pageQuery',
              serializeList: (list) => {
                list.forEach((e, i) => {
                  e.indexData = i + 1
                })
                return list
              },
              defaultRules: [
                {
                  label: this.$t('品类认证项目编号'),
                  field: 'authProjectCode',
                  type: 'string',
                  operator: 'equal',
                  value: this.info.projectCode
                }
              ]
            }
          }
        }
      ]
    }
  },
  computed: {
    isView() {
      return this.info.status != 10 && this.info.status != 30
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.pageConfig[0].grid.columnData = supplierPublicityColumn(this.isView)
    },
    handleClickCellTool(e) {
      let _this = this
      let id = e.tool.id
      if (id == 'edit') {
        this.$dialog({
          modal: () => import('./publicityDialog.vue'),
          data: {
            title: this.$t('编辑公示信息'),
            type: 'edit',
            info: this.info,
            infoData: e.data
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (id == 'delete') {
        this.deleteFile([e.data])
      }
    },
    handleClickToolBar(e) {
      let _this = this
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (toolbar.id === 'Add') {
        this.$dialog({
          modal: () => import('./publicityDialog.vue'),
          data: {
            title: this.$t('新增公示信息'),
            type: 'add',
            info: this.info
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (toolbar.id == 'Delete') {
        this.deleteFile(sltList)
      } else if (toolbar.id == 'Publicity') {
        this.publicity(sltList)
      }
    },
    publicity(arr) {
      if (arr.length == 0) {
        this.$toast({
          content: this.$t('请选择数据'),
          type: 'warning'
        })
        return
      }
      let params = {
        ids: arr.map((item) => item.id)
      }
      this.$API.CategoryCertification.publicitySubmit(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteFile(arr) {
      if (arr.length == 0) {
        this.$toast({
          content: this.$t('请选择数据'),
          type: 'warning'
        })
        return
      }
      let params = {
        ids: arr.map((item) => item.id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.CategoryCertification.publicityDelete(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
