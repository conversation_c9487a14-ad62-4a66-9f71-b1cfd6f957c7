<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item
              class="form-item"
              :label="$t('发布时间')"
              label-style="top"
              prop="publishTime"
            >
              <mt-date-time-picker
                v-model="formInfo.publishTime"
                :placeholder="$t('选择日期和时间')"
              ></mt-date-time-picker>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="24">
            <!-- <mt-form-item class="form-item" :label="$t('上传文件')" prop="uploadInfo">
              <div class="cell-upload">
                <div class="to-upload">
                  <input
                    ref="file"
                    type="file"
                    class="upload-input"
                    @change="chooseFiles"
                    multiple="multiple"
                    accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
                  />
                  <div class="upload-box">
                    <div class="plus-icon"></div>
                    <div class="right-state">
                      <div class="plus-txt">
                        {{ $t('请拖拽文件或点击上传') }}
                      </div>
                      <div class="warn-text">
                        {{
                          $t(
                            '注：文件最大不可超过50M， 文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="has-file"
                  v-for="(item, index) in formInfo.uploadInfo"
                  :key="'a' + index"
                >
                  <div class="left-info">
                    <div class="file-title">
                      <span class="text-ellipsis">
                        {{ item.fileName }}
                      </span>
                      <span class="text-ellipsis">{{ item.fileSize }} kb</span>
                    </div>
                  </div>
                  <mt-icon
                    name="icon_Close_2"
                    class="close-icon"
                    @click.native="handleRemove(index)"
                  ></mt-icon>
                </div>
              </div>
            </mt-form-item> -->
            <mt-form-item class="form-item" :label="$t('附件')" prop="uploadInfo" label-style="top">
              <mt-common-uploader
                :is-single-file="false"
                :save-url="saveUrl"
                :download-url="downloadUrl"
                type="line"
                v-model="formInfo.uploadInfo"
              ></mt-common-uploader>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="file" class="form-item" :label="$t('通知人员')">
              <div class="tabs">
                <p @click="handleSelectTab(0)" :class="{ active: currentTab == 0 }">
                  {{ $t('公司人员') }}
                </p>
                <p @click="handleSelectTab(1)" :class="{ active: currentTab == 1 }">
                  {{ $t('供应商') }}
                </p>
              </div>
              <mt-template-page
                ref="templateRef"
                v-show="currentTab == 0"
                :template-config="pageConfig"
                @handleClickToolBar="handleClickToolBar"
              ></mt-template-page>
              <mt-template-page
                ref="templateRef1"
                v-show="currentTab == 1"
                :template-config="pageConfig1"
              ></mt-template-page>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import commonData from '@/utils/constant'
import utils from '@/utils/utils'
import onlyShowInput from './onlyShowInput.vue'
import selectedOrder from './selectedOrder.vue'
import Vue from 'vue'
export default {
  data() {
    return {
      downloadUrl: commonData.downloadUrl,
      saveUrl: commonData.publicFileUrl,
      supplierList: [],
      allowFileType: [
        'xls',
        'xlsx',
        'doc',
        'docx',
        'pdf',
        'ppt',
        'pptx',
        'png',
        'jpg',
        'zip',
        'rar'
      ],
      formInfo: {
        publishTime: '',
        uploadInfo: []
      },
      rules: {
        publishTime: [{ required: true, message: this.$t('请输入发布时间'), trigger: 'blur' }],
        uploadInfo: [{ required: true, message: this.$t('请上传文件'), trigger: 'blur' }]
      },
      currentTab: 0,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: {},
          grid: {
            height: '300px',
            columnData: [
              {
                width: '50',
                type: 'checkbox'
              },
              {
                width: '280',
                field: 'accountName',
                headerText: this.$t('姓名')
              },
              {
                width: '180',
                field: 'mailAccount',
                headerText: this.$t('邮箱')
              },
              {
                width: '180',
                field: 'noticeMail',
                headerText: this.$t('是否邮件通知'),
                template: function () {
                  return {
                    template: Vue.component('actionOption', {
                      template: `<div>
                      <mt-checkbox
                        disabled
                        v-model="checkboxVal"
                      ></mt-checkbox>
                      </div>`,
                      data() {
                        return { data: {}, sceneName: '' }
                      },
                      computed: {
                        checkboxVal() {
                          return this.data.noticeMail == 1 ? true : false
                        }
                      }
                    })
                  }
                }
              }
            ],
            allowPaging: false, // 不分页
            dataSource: [],
            checkBoxChange: this.checkBoxChange
            // rowDeselected:this.rowDeselected,
          }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('添加行')
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除行')
                }
              ]
            ]
          },
          grid: {
            height: '300px',
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                allowEditing: false
              },
              {
                width: '280',
                field: 'accountName',
                headerText: this.$t('姓名'),
                editTemplate: () => {
                  return { template: selectedOrder }
                }
              },
              {
                width: '180',
                field: 'mailAccount',
                headerText: this.$t('邮箱'),
                editTemplate: () => {
                  return { template: onlyShowInput }
                }
              },
              {
                width: '180',
                field: 'accountDepartment',
                headerText: this.$t('部门'),
                editTemplate: () => {
                  return { template: onlyShowInput }
                }
              },
              {
                width: '180',
                field: 'noticeMail',
                headerText: this.$t('是否邮件通知'),
                allowEditing: false,
                template: function () {
                  return {
                    template: Vue.component('actionOption', {
                      template: `<div>
                      <mt-checkbox
                        disabled
                        v-model="checkboxVal"
                      ></mt-checkbox>
                      </div>`,
                      data() {
                        return { data: {}, sceneName: '' }
                      },
                      computed: {
                        checkboxVal() {
                          return this.data.noticeMail == 1 ? true : false
                        }
                      }
                    })
                  }
                },
                editTemplate: function () {
                  return {
                    template: Vue.component('actionOption', {
                      template: `<div>
                      <mt-checkbox
                        disabled
                        v-model="checkboxVal"
                      ></mt-checkbox>
                      </div>`,
                      data() {
                        return { data: {}, sceneName: '' }
                      },
                      computed: {
                        checkboxVal() {
                          return this.data.noticeMail == 1 ? true : false
                        }
                      }
                    })
                  }
                }
              }
            ],
            allowEditing: true, //开启表格编辑操作
            allowPaging: false, // 不分页
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              mode: 'Normal'
            },
            dataSource: []
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    info() {
      return this.modalData.info
    },
    infoData() {
      return this.modalData.infoData
    }
  },

  mounted() {
    if (this.type == 'edit') {
      this.$API.CategoryCertification.publishDetail({
        id: this.infoData.id
      }).then((res) => {
        this.formInfo.publishTime = new Date(Number(res.data.publishTime))
        this.formInfo.uploadInfo = res.data.fileInfoResponses
        let arr = []
        this.supplierList = []
        res.data.messageNoticeInfoDTOS.forEach((item) => {
          if (item.accountType == 1) {
            arr.push(item)
          } else {
            this.supplierList.push(item)
          }
        })
        this.pageConfig[0].grid.dataSource = arr
      })
    }
    this.$API.CategoryCertification.supplierList({
      authProjectCode: this.info.projectCode
    }).then((res) => {
      res.data.forEach((item) => {
        item.accountName = item.contactPerson
        item.mailAccount = item.contactEmail
        item.noticeMail = 1
        item.accountOriginId = item.supplierEnterpriseId
      })
      this.pageConfig1[0].grid.dataSource = res.data
    })
    this.show()
  },
  methods: {
    handleSelectTab(e) {
      this.currentTab = e
      if (e == 1) {
        let arr = []
        this.pageConfig1[0].grid.dataSource.forEach((item, index) => {
          let bol = this.supplierList.some((e) => {
            return e.accountOriginId == item.accountOriginId
          })
          if (bol) {
            arr.push(index)
          }
        })
        this.$nextTick(() => {
          setTimeout(() => {
            this.$refs.templateRef1.getCurrentTabRef().grid.selectRows(arr, true)
          }, 300)
        })
      }
    },
    checkBoxChange() {
      this.supplierList = this.$refs.templateRef1.getCurrentTabRef().grid.getSelectedRecords()
    },
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentTabRef().grid.endEdit()
        this.pageConfig[0].grid.dataSource = this.$refs.templateRef
          .getCurrentTabRef()
          .grid.getCurrentViewRecords()
        this.pageConfig[0].grid.dataSource.unshift({
          noticeMail: 1,
          id: this.randomString()
        })
        setTimeout(() => {
          this.$refs.templateRef.getCurrentTabRef().grid.selectRow(0)
          this.$refs.templateRef.getCurrentTabRef().grid.startEdit()
        }, 100)
      } else if (toolbar.id == 'Delete') {
        this.$refs.templateRef.getCurrentTabRef().grid.endEdit()
        this.pageConfig[0].grid.dataSource = this.$refs.templateRef
          .getCurrentTabRef()
          .grid.getCurrentViewRecords()
        let dataSource = this.pageConfig[0].grid.dataSource
        for (let i = 0; i < dataSource.length; i++) {
          let bol = sltList.some((item) => {
            return item.id == dataSource[i].id
          })
          if (bol) {
            dataSource.splice(i, 1)
            i--
          }
        }
      }
    },
    chooseFiles(data) {
      this.$loading()
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      } else if (files.length > 5) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
      })
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      bol = files.some((item) => {
        return item.size > params.limit * 1024
      })
      if (bol) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      files.forEach((item, index) => {
        let _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
        this.uploadFile(_data, index == files.length - 1)
      })
    },
    // 上传图片
    uploadFile(data, bol) {
      this.$refs.file.value = ''
      this.$API.SupplierPunishment.fileUploadSit(data)
        .then((res) => {
          const { code, data } = res
          if (bol) {
            this.$hloading()
          }
          if (code == 200 && !utils.isEmpty(data)) {
            this.formInfo.uploadInfo.push({
              ...data
            })
            if (bol) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          } else {
            this.$toast({
              content: data.msg || this.$t('上传失败'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          if (bol) {
            this.$hloading()
          }
          this.$toast({
            content: error.msg || this.$t('上传失败'),
            type: 'warning'
          })
        })
    },

    // 移除文件
    handleRemove(index) {
      this.formInfo.uploadInfo.splice(index, 1)
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$refs.templateRef.getCurrentTabRef().grid.endEdit()
          this.pageConfig[0].grid.dataSource = this.$refs.templateRef
            .getCurrentTabRef()
            .grid.getCurrentViewRecords()
          let bol = false
          let dataSource = this.pageConfig[0].grid.dataSource
          dataSource.forEach((item) => {
            if (!item.accountName || !item.mailAccount) {
              bol = true
            }
          })
          if (bol) {
            this.$toast({
              content: this.$t('请填写维护行内信息'),
              type: 'warning'
            })
            return
          }
          var myReg = /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/
          let index = -1
          dataSource.forEach((item, i) => {
            if (!myReg.test(item.mailAccount)) {
              bol = true
              if (index == -1) {
                index = i
              }
            }
          })
          if (bol) {
            this.$toast({
              content: this.$t(`第${index + 1}条数据邮箱格式不正确`),
              type: 'warning'
            })
            return
          }
          for (let i = 0; i < dataSource.length; i++) {
            for (let j = i + 1; j < dataSource.length; j++) {
              if (
                dataSource[i].accountName == dataSource[j].accountName &&
                dataSource[i].mailAccount == dataSource[j].mailAccount &&
                dataSource[i].accountDepartment == dataSource[j].accountDepartment
              ) {
                bol = true
              }
            }
          }
          if (bol) {
            this.$toast({
              content: this.$t('不能添加重复的公司成员'),
              type: 'warning'
            })
            return
          }
          let query = {
            publishTime: new Date(this.formInfo.publishTime).getTime(),
            bizType: 'publish',
            authProjectId: this.info.projectId,
            authProjectCode: this.info.projectCode,
            fileInfoRequests: [],
            messageNoticeInfoDTOS: []
          }
          this.formInfo.uploadInfo.forEach((item) => {
            query.fileInfoRequests.push({
              bizType: 'publish',
              fileId: item.id,
              fileName: item.fileName,
              fileSize: item.fileSize,
              fileType: item.fileType,
              fileUrl: item.url
            })
          })
          let arr = this.pageConfig[0].grid.dataSource
          arr.forEach((item) => {
            query.messageNoticeInfoDTOS.push({
              accountName: item.accountName,
              accountType: 1,
              accountTypeName: this.$t('员工'),
              bizType: 'publish',
              mailAccount: item.mailAccount,
              noticeMail: item.noticeMail,
              noticeSystem: 1,
              accountDepartment: item.accountDepartment
            })
          })
          this.supplierList.forEach((item) => {
            query.messageNoticeInfoDTOS.push({
              accountName: item.accountName,
              accountType: 2,
              accountTypeName: this.$t('供应商'),
              bizType: 'publish',
              mailAccount: item.mailAccount,
              noticeMail: 1,
              noticeSystem: 1,
              accountDepartment: '',
              accountOriginId: item.accountOriginId
            })
          })
          if (this.type == 'add') {
            this.$API.CategoryCertification.publishAdd(query).then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
          } else {
            query.id = this.infoData.id
            this.$API.CategoryCertification.publishUpdate(query).then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('编辑成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.uploader-box {
  padding: 40px 20px 0;
}

.cell-upload {
  margin-top: 15px;
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          font-size: 14px;
          display: inline-block;
          vertical-align: middle;
          margin: 0 10px;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
.tabs {
  display: flex;
  p {
    width: 100px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    cursor: pointer;
  }
  .active {
    border-bottom: 2px solid #00469c;
  }
}
</style>
