<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item
              class="form-item"
              :label="$t('供应商')"
              label-style="top"
              prop="supplierId"
            >
              <mt-select
                v-model="formInfo.supplierId"
                :data-source="info.supplierList"
                :fields="{ text: 'supplierName', value: 'id' }"
                :placeholder="$t('请选择')"
                float-label-type="Never"
                width="414"
                @select="supplierSelect"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item
              class="form-item"
              :label="sampleQtyLabel[type]"
              label-style="top"
              prop="sampleQty"
            >
              <mt-input
                v-model="formInfo.sampleQty"
                type="number"
                :min="0"
                :show-clear-button="true"
                :show-spin-button="false"
                height="40"
                :placeholder="$t('请输入')"
                @change="handleSampleQtyChange"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item
              class="form-item"
              :label="$t('认证轮次')"
              prop="approveRound"
              label-style="top"
            >
              <mt-input
                :max="100"
                type="number"
                :show-clear-button="true"
                :show-spin-button="false"
                v-model="formInfo.approveRound"
                oninput="if(value>100){value=100}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
                height="40"
                :placeholder="$t('非负整数')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item
              class="form-item"
              :label="$t('认证结果')"
              label-style="top"
              prop="approveResult"
            >
              <mt-select
                v-model="formInfo.approveResult"
                :data-source="certificationList"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :placeholder="$t('请选择')"
                float-label-type="Never"
                width="414"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="24">
            <mt-form-item class="form-item" :label="$t('附件')" prop="uploadInfo" label-style="top">
              <mt-common-uploader
                :is-single-file="false"
                :save-url="saveUrl"
                :download-url="downloadUrl"
                type="line"
                v-model="formInfo.uploadInfo"
              ></mt-common-uploader>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import commonData from '@/utils/constant'

export default {
  data() {
    return {
      allowFileType: [
        'xls',
        'xlsx',
        'doc',
        'docx',
        'pdf',
        'ppt',
        'pptx',
        'png',
        'jpg',
        'zip',
        'rar'
      ],
      saveUrl: commonData.publicFileUrl,
      downloadUrl: commonData.downloadUrl,
      supplierList: [],
      certificationList: [], //认证结果字典
      formInfo: {
        supplierId: '',
        approveRound: '',
        approveResult: '',
        uploadInfo: []
      },
      rules: {
        uploadInfo: [
          {
            required: true,
            message: this.$t('请选择附件'),
            trigger: 'blur'
          }
        ],
        supplierId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        sampleQty: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        approveRound: [
          {
            required: true,
            message: this.$t('请输入认证轮次'),
            trigger: 'blur'
          }
        ],
        approveResult: [
          {
            required: true,
            message: this.$t('请选择认证结果'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      uploadInfo: [], // 上传后信息
      sampleQtyLabel: {
        sample: this.$t('送样数量'),
        smallBatch: this.$t('试用数量')
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    info() {
      return this.modalData.info
    }
  },

  mounted() {
    this.show()
    this.certificationdict()
  },
  methods: {
    // 取整
    handleSampleQtyChange(val) {
      this.formInfo = {
        ...this.formInfo,
        sampleQty: val < 0 ? 0 : Math.round(val)
      }
    },
    certificationdict() {
      this.$API.CategoryCertification.approvedict({
        dictCode: 'approveResultType'
      }).then((res) => {
        this.certificationList = res.data
      })
    },
    chooseFiles(data) {
      this.$loading()
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      } else if (files.length > 5) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
      })
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      bol = files.some((item) => {
        return item.size > params.limit * 1024
      })
      if (bol) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      files.forEach((item, index) => {
        let _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
        this.uploadFile(_data, index == files.length - 1)
      })
    },
    // 上传图片
    uploadFile(data, bol) {
      this.$refs.file.value = ''
      this.$API.SupplierPunishment.fileUploadSit(data)
        .then((res) => {
          const { code, data } = res
          if (bol) {
            this.$hloading()
          }
          if (code == 200 && !utils.isEmpty(data)) {
            this.formInfo.uploadInfo.push({
              ...data
            })
            if (bol) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          } else {
            this.$toast({
              content: data.msg || this.$t('上传失败'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          if (bol) {
            this.$hloading()
          }
          this.$toast({
            content: error.msg || this.$t('上传失败'),
            type: 'warning'
          })
        })
    },

    // 移除文件
    handleRemove(index) {
      this.formInfo.uploadInfo.splice(index, 1)
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    supplierSelect(e) {
      let { itemData } = e
      this.formInfo.authProjectId = itemData.authProjectId
      this.formInfo.authProjectCode = itemData.authProjectCode
      this.formInfo.partnerArchiveId = itemData.partnerArchiveId
      this.formInfo.partnerRelationId = itemData.partnerRelationId
      this.formInfo.categoryRelationId = itemData.categoryRelationId
      this.formInfo.supplierCode = itemData.supplierCode
      this.formInfo.supplierName = itemData.supplierName
      this.formInfo.supplierInternalCode = itemData.supplierInternalCode
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          // if (this.uploadInfo.length == 0) {
          //   this.$toast({
          //     content: this.$t('请选择文件上传！'),
          //     type: 'warning'
          //   })
          //   return
          // }

          let query = {
            bizId: this.formInfo.supplierId,
            sampleQty: this.formInfo.sampleQty,
            approveRound: this.formInfo.approveRound,
            approveResult: this.formInfo.approveResult,
            bizType: this.type,
            authProjectId: this.formInfo.authProjectId,
            authProjectCode: this.formInfo.authProjectCode,
            partnerArchiveId: this.formInfo.partnerArchiveId,
            partnerRelationId: this.formInfo.partnerRelationId,
            categoryRelationId: this.formInfo.categoryRelationId,
            supplierCode: this.formInfo.supplierCode,
            supplierName: this.formInfo.supplierName,
            supplierInternalCode: this.formInfo.supplierInternalCode,
            fileInfoRequests: []
          }

          this.formInfo.uploadInfo.forEach((item) => {
            query.fileInfoRequests.push({
              bizType: this.type,
              bizId: this.formInfo.supplierId,
              fileId: item.id,
              fileName: item.fileName,
              fileSize: item.fileSize,
              fileType: item.fileType,
              fileUrl: item.url
            })
          })

          this.$API.CategoryCertification.addUploader(query)
            .then((res) => {
              const { code, data } = res
              if (code == 200 && !utils.isEmpty(data)) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function', 'reload')
              } else {
                this.$toast({
                  content: data.msg || this.$t('上传失败'),
                  type: 'warning'
                })
              }
            })
            .catch((error) => {
              this.$toast({
                content: error.msg || this.$t('上传失败'),
                type: 'warning'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.uploader-box {
  .mt-form-item__content {
    flex: 1;
  }
}
</style>
<style lang="scss" scoped>
.uploader-box {
  padding: 40px 20px 0;
}

.cell-upload {
  margin-top: 15px;
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          font-size: 14px;
          display: inline-block;
          vertical-align: middle;
          margin: 0 10px;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
