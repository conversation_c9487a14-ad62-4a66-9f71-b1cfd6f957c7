// 综合审批新增弹窗
<template>
  <mt-dialog ref="thresholdItemDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('供应商')"
          label-style="top"
          prop="authProjectSupplierId"
        >
          <mt-select
            v-model="formInfo.authProjectSupplierId"
            :fields="{ text: 'supplierName', value: 'authProjectSupplierId' }"
            :data-source="supplierList"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            @change="supplierCodeChange"
            v-if="!isEdit"
          ></mt-select>
          <mt-input
            v-model="formInfo.supplierName"
            float-label-type="Never"
            :disabled="true"
            v-if="isEdit"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('品类')" label-style="top" prop="categoryName">
          <mt-input
            v-model="formInfo.categoryName"
            float-label-type="Never"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('场景')" label-style="top" prop="sceneName">
          <mt-input
            v-model="formInfo.sceneName"
            float-label-type="Never"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item class="form-item" :label="$t('附件')" label-style="top" prop="fileName">
          <div style="display: flex">
            <mt-input
              v-model="formInfo.fileName"
              float-label-type="Never"
              :disabled="true"
              width="314"
              :placeholder="$t('请上传供应商认证评审报告&供应商准入评审报告')"
            ></mt-input>
            <mt-button @click="uploading">{{ $t('点击上传') }}</mt-button>
          </div>
        </mt-form-item> -->

        <mt-form-item class="form-item" :label="$t('附件')" prop="fileName" label-style="top">
          <mt-common-uploader
            :is-single-file="false"
            :save-url="saveUrl"
            :download-url="downloadUrl"
            type="line"
            v-model="formInfo.fileInfoDTO"
          ></mt-common-uploader>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('生效方式')"
          label-style="top"
          prop="effectiveMethod"
        >
          <mt-select
            v-model="formInfo.effectiveMethod"
            float-label-type="Never"
            :data-source="effectiveMethodList"
            :placeholder="$t('请选择生效方式')"
          />
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('备注')"
          label-style="top"
          prop="remark"
        >
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            float-label-type="Never"
            :max-length="100"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import commonData from '@/utils/constant'
export default {
  data() {
    return {
      downloadUrl: commonData.downloadUrl,
      isShow: false,
      saveUrl: commonData.publicFileUrl,
      formInfo: {
        fileInfoDTO: [],
        qualificationApproveResult: ''
      },
      supplierList: [],

      rules: {
        authProjectSupplierId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        fileInfoDTO: [{ required: true, message: this.$t('请上传文件'), trigger: 'blur' }],
        qualificationApproveResult: [
          { required: true, message: this.$t('请选择资质审查结果'), trigger: 'blur' }
        ],
        effectiveMethod: [{ required: true, message: this.$t('请选择生效方式'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],

      effectiveMethodList: [
        { text: this.$t('线下采委会审批'), value: 10 },
        { text: this.$t('线上审批'), value: 20 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.show()
    this.isShow = true
  },
  methods: {
    uploading() {
      this.$dialog({
        modal: () => import('../uploadDialogApprove.vue'),
        data: {
          title: this.$t('上传'),
          info: this.formInfo.fileInfoDTO
        },
        success: (val) => {
          this.formInfo.fileInfoDTO = val
          this.formInfo.fileName = ''
          this.formInfo.fileInfoDTO.forEach((item) => {
            this.formInfo.fileName += item.fileName
          })
          this.formInfo = JSON.parse(JSON.stringify(this.formInfo))
          this.$refs.formInfo.validateField('fileName')
        }
      })
    },
    initData() {
      this.supplierList = this.info.supplierList
      if (this.isEdit && this.info && Object.keys(this.info).length) {
        this.formInfo = {
          ...this.info,
          fileInfoDTO: this.info.fileInfos
        }
        this.formInfo.fileName = ''
        this.formInfo.fileInfoDTO.forEach((item) => {
          this.formInfo.fileName += item.fileName
        })
      }
    },

    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    supplierCodeChange(e) {
      this.formInfo.categoryName = e.itemData.categoryName
      this.formInfo.sceneName = e.itemData.sceneName
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'updateApprove' : 'addApprove'
          let obj = {
            ...this.formInfo,
            categoryName: this.formInfo.categoryName,
            sceneName: this.formInfo.sceneName,
            authProjectSupplierId: this.formInfo.authProjectSupplierId,
            remark: this.formInfo.remark,
            fileInfos: []
          }
          this.formInfo.fileInfoDTO.forEach((item) => {
            item.fileId = item.fileId ? item.fileId : item.id
            item.fileUrl = item.fileUrl ? item.fileUrl : item.url
            obj.fileInfos.push({ ...item })
          })
          this.$API.CategoryCertification[methodName](obj)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
}
</style>
