// 门槛匹配项目明细弹窗
<template>
  <mt-dialog
    ref="thresholdMatchDetail-dialog"
    css-class="thresholdMatchDetail-dialog"
    :buttons="buttons"
    :header="modalData.title"
  >
    <mt-data-grid
      class="thresholdMatchDetail-dialog"
      :data-source="modalData.dataList"
      :column-data="modalData.columnData"
      ref="dataGrid"
      locale="zh"
    ></mt-data-grid>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('返回') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.$refs['thresholdMatchDetail-dialog'].ejsRef.show()
  },
  methods: {
    cancel() {
      this.$refs['thresholdMatchDetail-dialog'].ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.thresholdMatchDetail-dialog {
  margin: 40px 22px 0 22px;
}
</style>
