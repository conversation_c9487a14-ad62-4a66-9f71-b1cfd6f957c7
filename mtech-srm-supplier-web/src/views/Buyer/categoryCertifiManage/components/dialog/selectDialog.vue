<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('选择部门')" label-style="top" prop="supplierId">
          <mt-select
            :data-source="personneloplist"
            :fields="{ text: 'orgName', value: 'id' }"
            :show-clear-button="true"
            :placeholder="$t('请选择所属部门')"
            @change="stationGet"
            class="responsible-children"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('选择人员')" label-style="top">
          <mt-template-page
            ref="templateRef"
            :template-config="pageConfig"
            @handleClickToolBar="handleClickToolBar"
          ></mt-template-page>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      personneloplist: [],
      formInfo: {
        deptId: ''
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      rules: {
        deptId: [
          {
            required: true,
            message: this.$t('请选择部门'),
            trigger: 'blur'
          }
        ]
      },
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false,
          grid: {
            height: '300px',
            columnData: [
              {
                width: '50',
                type: 'checkbox'
              },
              {
                width: '180',
                field: 'employeeName',
                headerText: this.$t('姓名')
              },
              {
                width: '180',
                field: 'email',
                headerText: this.$t('邮箱')
              },
              {
                width: '180',
                field: 'departmentName',
                headerText: this.$t('部门')
              }
            ],
            dataSource: [],
            allowPaging: false // 不分页
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    orgId() {
      return this.modalData.orgId
    }
  },

  mounted() {
    this.getChildrenDepartmentOrganization()
    this.show()
  },
  methods: {
    getChildrenDepartmentOrganization() {
      let parms = {
        onlyCurrentLevel: 0,
        organizationId: this.orgId
      }
      this.$API.analysisOfSetting['getChildrenDepartmentOrganization'](parms).then((result) => {
        this.personneloplist = result.data
      })
    },
    handleClickToolBar(e) {
      const { toolbar } = e
      // let sltList = gridRef.getMtechGridRecords()
      if (toolbar.id === 'Add') {
        this.pageConfig[0].grid.dataSource.push({
          noticeMail: 1
        })
      } else if (toolbar.id == 'Delete') {
        // let ids = sltList.map((e) => e.id)
      }
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          let arr = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()
          if (arr.length == 0) {
            this.$toast({
              content: this.$t('请选择成员'),
              type: 'warning'
            })
          }
          this.$emit('confirm-function', arr)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    stationGet(e) {
      if (e.itemData) {
        let { itemData } = e
        this.getOrganizationEmployees(itemData.id) //调用部门下评分人接口
      }
    },
    getOrganizationEmployees(id) {
      let parms = {
        orgId: id
      }
      this.$API.analysisOfSetting['getOrganizationEmployees'](parms).then((result) => {
        this.pageConfig[0].grid.dataSource = result.data
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.uploader-box {
  padding: 40px 20px 0;
}
</style>
