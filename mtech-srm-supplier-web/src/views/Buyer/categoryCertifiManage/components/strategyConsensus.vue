// 策略共识
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      v-if="this.pageConfig[0].grid.columnData.length > 0"
    ></mt-template-page>
    <input
      ref="file"
      type="file"
      style="display: none"
      @change="chooseFiles"
      multiple="multiple"
      accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
    />
  </div>
</template>
<script>
import { strategyConsensusColumn } from '../config/index.js'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    isView() {
      return this.info.status != 10 && this.info.status != 30
    }
  },
  data() {
    return {
      allowFileType: [
        'xls',
        'xlsx',
        'doc',
        'docx',
        'pdf',
        'ppt',
        'pptx',
        'png',
        'jpg',
        'zip',
        'rar'
      ],
      pageConfig: [
        {
          // gridId: "91f503da-41e8-4342-bbfc-26a9304b250b",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  visibleCondition: () => !this.isView
                }
              ]
            ]
          },
          grid: {
            lineSelection: true,
            columnData: [],
            asyncConfig: {
              url: '/supplier/tenant/buyer/auth/view/queryView',
              params: {
                authProjectCode: this.info.projectCode
              }
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.pageConfig[0].grid.columnData = strategyConsensusColumn(this.isView)
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Add') {
        // this.$dialog({
        //   modal: () => import("./uploadDialog.vue"),
        //   data: {
        //     title: this.$t("上传"),
        //     info: this.info,
        //   },
        //   success: () => {
        //     this.$refs.templateRef.refreshCurrentGridData();
        //   },
        // });
        this.$refs.file.click()
      } else if (toolbar.id == 'Delete') {
        let ids = sltList.map((e) => e.id)
        this.deleteView(ids.toString())
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e.data.fileId
          // useType: 2,
        }
        this.$API.SupplierPunishment.filepreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'delete') {
        this.deleteView(data.id)
      }
    },
    deleteView(param) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选策略共识？'),
          confirm: () => this.$API.CategoryCertification.deleteView({ ids: param })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    chooseFiles(data) {
      this.$loading()
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      } else if (files.length > 5) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
      })
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      bol = files.some((item) => {
        return item.size > params.limit * 1024
      })
      if (bol) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$refs.file.value = ''
      this.uploadFile(files)
    },
    uploadFile(files) {
      let arr = []
      files.forEach((item) => {
        let _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
        arr.push(this.$API.SupplierPunishment.fileUpload(_data))
      })
      Promise.all(arr).then((res) => {
        let params = {
          authProjectCode: this.info.projectCode,
          authProjectId: this.info.projectId,
          fileInfos: []
        }
        res.forEach((item) => {
          if (item.code == 200) {
            params.fileInfos.push({
              ...item.data,
              fileId: item.data.id,
              fileUrl: item.data.url
            })
          }
        })
        this.$API.CategoryCertification.addView(params).then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({
              content: data.msg || this.$t('上传失败'),
              type: 'warning'
            })
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
