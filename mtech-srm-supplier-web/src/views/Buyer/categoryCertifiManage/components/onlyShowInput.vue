<template>
  <mt-input
    :id="data.column.field"
    v-model="data[data.column.field]"
    :disabled="data.column.field == 'accountDepartment'"
  />
</template>

<script>
export default {
  props: {},
  data() {
    return { data: {}, value: '' }
  },
  mounted() {
    const that = this
    this.fieldName = this.data.column.field
    this.$bus.$on(`${this.fieldName}Change`, (value) => {
      that.$set(that.data, this.fieldName, value)
    })
  },
  methods: {}
}
</script>

<style scoped lang="scss"></style>
