<template>
  <div>
    <mt-input :id="data.column.field" v-model="data[data.column.field]" style="display: none" />
    <mt-select
      v-model="value"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="$t('请选择员工')"
      :allow-filtering="true"
      :filtering="onFiltering"
      @change="selectChange"
    >
    </mt-select>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  props: {},
  data() {
    return {
      data: {},
      fields: { text: 'employeeName', value: 'userId' },
      dataSource: [],
      dataSourceCopy: [],
      value: ''
    }
  },
  mounted() {
    this.purOrderQueryOrder()
    this.onFiltering = utils.debounce(this.onFiltering, 300)
  },
  methods: {
    purOrderQueryOrder() {
      let params = {
        page: {
          current: 1,
          pages: 0,
          size: 100
        }
      }
      this.$API.CategoryCertification.queryEmployee(params).then((res) => {
        let _records = res.data.records.map((item) => {
          return {
            ...item,
            employeeName: item.employeeName + '-' + item.phoneNum + '-' + item.employeeDescription
          }
        })
        this.dataSource = _records
        this.dataSourceCopy = res.data.records
      })
    },
    onFiltering(e) {
      if (typeof e.text === 'string' && e.text) {
        let params = {
          page: {
            current: 1,
            pages: 0,
            size: 200
          },
          rules: [
            {
              field: 'employeeName',
              label: this.$t('品类认证编号'),
              operator: 'contains',
              type: 'string',
              value: e.text
            }
          ]
        }
        this.$API.CategoryCertification.queryEmployee(params).then((res) => {
          let _records = res.data.records.map((item) => {
            return {
              ...item,
              employeeName: item.employeeName + '-' + item.phoneNum + '-' + item.employeeDescription
            }
          })
          e.updateData(_records)
        })
      } else {
        e.updateData(this.dataSourceCopy)
      }
    },
    selectChange(e) {
      let { itemData } = e
      this.$bus.$emit('mailAccountChange', itemData.email.trim())
      this.$bus.$emit('accountDepartmentChange', itemData.departmentName)
      this.data[this.data.column.field] = itemData.employeeName
    }
  }
}
</script>

<style scoped lang="scss"></style>
