<!-- 预审调查表 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @cellEdit="cellEdit"
    ></mt-template-page>
  </div>
</template>

<script>
import { preColumn } from '../config/index.js'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'publish',
                  title: this.$t('发布'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'approve',
                  title: this.$t('批准'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'reject',
                  title: this.$t('驳回'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'submit',
                  title: this.$t('提交OA审批'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                }
              ]
            ]
          },
          gridId: '68f55a9b-81d1-44a4-a0aa-a575f2462eb1',
          grid: {
            columnData: [],
            dataSource: []
          }
        }
      ],
      currentList: []
    }
  },
  computed: {
    isView() {
      return this.info.status != 10 && this.info.status != 30
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.pageConfig[0].grid.columnData = preColumn(this.isView)
      this.handleSearch()
    },
    handleSearch() {
      this.$loading()
      this.$API.CategoryCertification.queryPreReviewSurveyApi({
        authProjectCode: this.info.projectCode
      })
        .then((res) => {
          if (res.code == 200) {
            this.currentList = res.data
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data)
          }
        })
        .finally(() => {
          this.$hloading()
        })
    },
    cellEdit(args) {
      const { key, value } = args

      this.currentList.forEach((item) => {
        if (item.id === value.id) {
          item[key] = value[key]
        }
      })
      this.$set(this.pageConfig[0].grid, 'dataSource', this.currentList)
    },
    handleClickCellTitle(e) {
      const { data } = e
      if (e.field == 'code') {
        this.$router.push({
          path: '/supplier/pur/preReviewInvestigationDetail',
          query: {
            id: data.id,
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const selectedRecords = gridRef.getMtechGridRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1) {
        this.$toast({ content: this.$t('只能选择一行进行操作'), type: 'warning' })
        return
      }
      switch (toolbar.id) {
        case 'publish':
          this.handlePublish(selectedRecords[0])
          break
        case 'approve':
          this.handleApprove(selectedRecords[0])
          break
        case 'reject':
          this.handleReject(selectedRecords[0])
          break
        case 'submit':
          this.handleSubmit(selectedRecords[0])
          break
        case 'audit':
          this.handleAudit(selectedRecords[0])
          break
        default:
          break
      }
    },
    handlePublish(row) {
      let params = {
        id: row.id,
        purchaseAmount: row.purchaseAmount
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message:
            this.$t('预估年采购金额（万元）') +
            '：' +
            row.purchaseAmount +
            '，' +
            this.$t('确认发布？')
        },
        success: () => {
          this.$loading()
          this.$API.CategoryCertification.publishPreReviewSurveyApi(params)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('发布成功'), type: 'success' })
                this.handleSearch()
              }
            })
            .finally(() => {
              this.$hloading()
            })
        }
      })
    },
    handleApprove(row) {
      let params = {
        id: row.id
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认批准？')
        },
        success: () => {
          this.$loading()
          this.$API.CategoryCertification.passPreReviewSurveyApi(params)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('批准成功'), type: 'success' })
                this.handleSearch()
              }
            })
            .finally(() => {
              this.$hloading()
            })
        }
      })
    },
    handleReject(row) {
      let params = {
        id: row.id
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认驳回？')
        },
        success: () => {
          this.$loading()
          this.$API.CategoryCertification.rejectPreReviewSurveyApi(params)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('驳回成功'), type: 'success' })
                this.handleSearch()
              }
            })
            .finally(() => {
              this.$hloading()
            })
        }
      })
    },
    handleSubmit(row) {
      let params = {
        id: row.id
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交OA审批？')
        },
        success: () => {
          this.$loading()
          this.$API.CategoryCertification.submitOAPreReviewSurveyApi(params)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('提交成功'), type: 'success' })
                this.handleSearch()
              }
            })
            .finally(() => {
              this.$hloading()
            })
        }
      })
    },
    handleAudit(row) {
      let params = {
        applyId: row.id
      }
      this.$API.CategoryCertification.checkOAPreReviewSurveyApi(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
