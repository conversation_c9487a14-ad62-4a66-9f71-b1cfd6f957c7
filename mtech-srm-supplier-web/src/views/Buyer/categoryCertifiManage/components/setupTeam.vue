// 成立小组
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      v-if="this.pageConfig[0].grid.columnData.length > 0"
    ></mt-template-page>
  </div>
</template>
<script>
import { teamColumn } from '../config/index.js'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          // gridId:"8ddcf526-9133-4f5c-93cc-259dd10b829d",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  visibleCondition: () => !this.isView
                }
              ]
            ]
          },
          grid: {
            lineSelection: true,
            // allowPaging: false,
            columnData: [],
            asyncConfig: {
              url: '/supplier/tenant/buyer/auth/group/queryGroup',
              params: {
                authProjectCode: this.info.projectCode
              }
            }
          }
        }
      ]
    }
  },
  mounted() {
    console.log('this.projectCode', this.info)
    this.pageConfig[0].grid.columnData = teamColumn(this.isView)
  },
  computed: {
    isView() {
      return this.info.status != 10 && this.info.status != 30
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Add') {
        this.$dialog({
          modal: () => import('./addTeamMemberDialog.vue'),
          data: {
            title: this.$t('新增人员'),
            info: this.info
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (toolbar.id == 'Delete') {
        let ids = sltList.map((e) => e.id)
        this.deleteUser(ids.toString())
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'edit') {
        this.$dialog({
          modal: () => import('./addTeamMemberDialog.vue'),
          data: {
            title: this.$t('编辑人员'),
            isEdit: true,
            info: data
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (id == 'delete') {
        this.deleteUser(data.id)
      }
    },
    deleteUser(param) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选人员？'),
          confirm: () => this.$API.CategoryCertification.deleteGroup({ ids: param })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
