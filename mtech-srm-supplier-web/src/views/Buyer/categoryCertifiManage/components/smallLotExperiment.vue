// 小批量实验
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @updateFileList="updateFileList"
      v-if="this.pageConfig[0].grid.columnData.length > 0"
    ></mt-template-page>
  </div>
</template>
<script>
import { smallLotExperimentColumn } from '../config/index.js'
import utils from '@/utils/utils'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    isView() {
      return this.info.status != 10 && this.info.status != 30
    }
  },
  data() {
    return {
      pageConfig: [
        {
          // gridId:"f77c5e39-d5a1-45ef-8e11-9f417ddda628",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增'),
                  visibleCondition: () => !this.isView
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  visibleCondition: () => !this.isView
                }
              ]
            ]
          },
          grid: {
            lineSelection: true,
            columnData: [],
            asyncConfig: {
              url: '/supplier/tenant/buyer/category/approve/list',
              params: {
                authProjectCode: this.info.projectCode,
                bizType: 'smallBatch'
              },
              methods: 'get',
              recordsPosition: 'data'
            },
            frozenColumns: 3
          }
        }
      ]
    }
  },
  mounted() {
    this.pageConfig[0].grid.columnData = smallLotExperimentColumn(this.isView)
  },
  methods: {
    handleClickToolBar(e) {
      let _this = this
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Add') {
        _this.$API.CategoryCertification.getSupListByAuthCode({
          authProjectCode: _this.info.projectCode
        }).then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.$dialog({
              modal: () => import('./productCertificationDialog.vue'),
              data: {
                title: this.$t('上传'),
                info: { supplierList: res.data },
                type: 'smallBatch'
              },
              success: () => {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          } else {
            this.$toast({
              content: this.$t('认证项目未引入供应商!'),
              type: 'warning'
            })
            return
          }
        })
      } else if (toolbar.id == 'Delete') {
        let ids = sltList.map((e) => e.id)
        this.deleteFile(ids.toString())
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e.data.fileId
          // useType: 2,
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(`${res.data}/onlinePreview?url=${e.data.fileUrl}`)
        })
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'delete') {
        this.deleteFile(data.id)
      }
    },
    // 更新附件列表
    updateFileList(data) {
      const params = {
        bizId: data.id,
        bizType: 'smallBatch',
        fileInfoRequests: data.buyerAuthProjectFileResponses
      }
      this.$API.CategoryCertification.updateFileList(params).then((res) => {
        if (res.code === 200) {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteFile(param) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选记录？'),
          confirm: () => this.$API.CategoryCertification.delUploader({ ids: param })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
