<template>
  <div class="intro-box">
    <div class="edit-box" v-if="isShow">
      <rich-text-editor
        ref="editor"
        :height="500"
        :disabled="isView"
        class="rich-editor"
        v-model.trim="richValue"
      >
      </rich-text-editor>
      <div class="buttons-box" v-if="queryType == 1 && info.source == '2'">
        <span class="btn" @click="saveTxt">{{ $t('保存') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'

export default {
  components: {
    RichTextEditor
  },
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    }
  },
  props: {
    moduleKey: {
      type: String,
      default: ''
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isShow: false,
      isView: false,
      richValue: '',
      toolbarSettings: {
        enable: true,
        enableFloating: true,
        type: 'Expand',
        items: [
          'Bold',
          'Italic',
          'Underline',
          '|',
          'Formats',
          'Alignments',
          'OrderedList',
          'UnorderedList',
          '|',
          'CreateLink',
          'Image',
          'backgroundColor',
          '|',
          'SourceCode',
          'Undo',
          'Redo'
        ],
        itemConfigs: {}
      },
      backgroundColor: {
        columns: 10,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      }
    }
  },
  mounted() {
    // 校验是否是查看
    if (this.info.source == '1') {
      this.toolbarSettings.enable = false
      this.toolbarSettings.enableFloating = false
      this.richValue = this.info.buyerAuthDemandResponse.demandDesc
      this.isView = true
    } else {
      this.richValue = this.info.demandDesc
    }

    this.isShow = true
  },
  methods: {
    saveTxt() {
      if (!this.richValue || this.richValue.length <= 0) {
        this.$toast({ content: this.$t('请先添加描述说明'), type: 'warning' })
        return
      }

      this.$API.CategoryCertification.updateProject({
        ...this.info,
        demandDesc: this.richValue
      }).then((res) => {
        if (res.code == 200) {
          this.info.demandDesc = this.richValue
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        }
      })
    },
    changeText(value) {
      console.log(value)
    },
    createdTextEditor(value) {
      console.log('created', value)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-rich-text-editor {
  padding-top: 50px;

  .e-toolbar-wrapper {
    height: 42px !important;
  }

  .e-rte-content {
    height: 500px !important;
    padding: 0px 7px;

    em {
      font: revert;
    }

    ol,
    ul {
      list-style: revert;
    }
  }
}

.intro-box {
  width: 100%;
  display: flex;
  flex: 1;
  background: #fff;

  .setting-banner {
    width: 100%;
    height: 50px;
    cursor: pointer;
    padding: 0 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(79, 91, 109, 1);

    i {
      font-size: 16px;
      line-height: 14px;
      display: inline-block;
      height: 16px;
      width: 16px;
      cursor: pointer;
    }

    span {
      display: inline-block;
      margin-left: 6px;
      cursor: pointer;
    }
  }

  .edit-box {
    padding: 0 20px;
    width: 100%;
    height: 100%;
    background: #fff;

    .detail-list {
      margin-left: 50px;
      float: left;
      width: calc(100% - 500px);
    }
  }
}

/deep/ .rich-editor {
  width: 960px;
  // height: 100%;
  margin: 0 auto;
}

.buttons-box {
  width: 100%;
  text-align: center;
  line-height: 30px;
  margin-top: 40px;

  .btn {
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    color: #00469c;
    margin-left: 50px;
    cursor: pointer;
  }
}
</style>
