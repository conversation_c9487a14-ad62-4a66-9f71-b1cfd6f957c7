<template>
  <div class="full-height category-certiry-container">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
      @handleSelectTab="handleSelectTab"
      :current-tab="currentTab"
    />
    <require-detail-describe
      ref="requireDetailDescribeRef"
      v-if="isShowRelativeState"
    ></require-detail-describe>
  </div>
</template>
<script>
import { certificationColumnData, handleColumnData } from './config/index'
import { exportData } from '@/utils/utils.js'

export default {
  components: {
    requireDetailDescribe: require('./components/requireDetailDescribe.vue').default
  },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex'))
      ? JSON.parse(localStorage.getItem('tabIndex'))
      : 0
    localStorage.removeItem('tabIndex')
    return {
      currentTab,
      totalDemendCode: [],
      tabSltIndex: 0,
      isShowRelativeState: false,
      pageConfig: [
        {
          gridId: '24722964-ff37-4dae-b084-2f5b1caccd04',
          title: this.$t('认证需求'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'Create',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建品类认证项目')
                },
                {
                  id: 'export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            lineSelection: true,
            columnData: certificationColumnData,
            asyncConfig: {
              url: '/supplier/tenant/buyer/auth/manager/demandQuery'
            },
            frozenColumns: 3
          }
        },
        {
          gridId: 'cb59d4d9-2b97-4759-adf3-247ff5b59b1a',
          title: this.$t('品类认证项目'),
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'Discontinue',
                  icon: 'icon_table_discontinue',
                  title: this.$t('终止认证')
                },
                {
                  id: 'Restart',
                  icon: 'icon_table_restart',
                  title: this.$t('重新认证')
                },
                // {
                //   id: "Add",
                //   icon: "icon_table_appoint",
                //   title: this.$t("推荐他人处理"),
                // },
                {
                  id: 'export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            lineSelection: true,
            columnData: handleColumnData,
            asyncConfig: {
              url: '/supplier/tenant/buyer/auth/manager/projectQuery',
              afterAsyncData: this.afterAsyncData
            }
          },
          frozenColumns: 3
        }
      ]
    }
  },
  methods: {
    afterAsyncData(res) {
      this.totalDemendCode = res.data.records
        .filter((e) => e.source === '1')
        .map((x) => x.demandCode)
    },
    handleSelectTab(e) {
      this.tabSltIndex = e
    },
    handleClickToolBar(e) {
      const { toolbar, gridRef, tabIndex } = e
      let sltList = gridRef.getMtechGridRecords()
      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add' && toolbar.id !== 'export') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id == 'Add') {
        if (tabIndex === 0) {
          this.$dialog({
            modal: () => import('./components/certificationDialog.vue'),
            data: {
              title: this.$t('新增认证需求')
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (tabIndex === 1) {
          this.$dialog({
            modal: () => import('./components/categoryDialog.vue'),
            data: {
              title: this.$t('新增品类认证项目'),
              totalDemendCode: this.totalDemendCode,
              info: {}
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      } else if (toolbar.id == 'Create') {
        if (tabIndex === 0) {
          if (sltList.length > 1) {
            this.$toast({ content: this.$t('请选择一行'), type: 'warning' })
            return
          }
          let data = sltList[0]
          if (data.authProjectCode && data.authProjectCode != '') {
            this.$toast({
              content: this.$t('认证需求已生成品类认证项目'),
              type: 'warning'
            })
            return
          }
          this.$dialog({
            modal: () => import('./components/categoryDialog.vue'),
            data: {
              title: this.$t('新增品类认证项目'),
              totalDemendCode: [],
              info: {
                projectName: data.demandName,
                demandId: data.id,
                demandName: data.demandName,
                demandCode: data.demandCode,
                source: '1',
                demandUserId: data.demandUserId,
                demandUserName: data.demandUserName,
                categoryCode: data.categoryCode,
                categoryName: data.categoryName,
                orgId: data.orgId,
                orgCode: data.orgCode,
                orgName: data.orgName
              }
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
              // this.currentTab = 1;
            }
          })
        }
      } else if (toolbar.id == 'Delete') {
        if (tabIndex === 0) {
          let bol = sltList.some((item) => {
            return item.authProjectCode
          })
          if (bol) {
            this.$toast({
              content: this.$t('请选择没有认证项目的数据！'),
              type: 'warning'
            })
            return
          }
          // 中止寻源的单据不可编辑、不可删除。（估计没啥用，加上保险点）
          if (sltList.some((item) => item.status == 3)) {
            this.$toast({
              content: this.$t('中止寻源的数据无法删除'),
              type: 'warning'
            })
            return
          }
          let ids = sltList.map((e) => e.id)
          this.deleteDemends(ids.toString())
        } else if (tabIndex === 1) {
          let ids = sltList.map((e) => e.id)
          if (sltList.some((item) => item.status == 2)) {
            this.$toast({
              content: this.$t('认证完成的数据无法删除'),
              type: 'warning'
            })
            return
          }
          if (sltList.some((item) => item.status == 3)) {
            this.$toast({
              content: this.$t('中止寻源的数据无法删除'),
              type: 'warning'
            })
            return
          }
          this.deleteCategorys(ids.toString())
        }
      } else if (toolbar.id == 'Discontinue') {
        // 终止认证
        if (tabIndex === 1) {
          let ids = sltList.map((e) => e.id)
          this.stopProjects(ids)
        }
      } else if (toolbar.id == 'Restart') {
        // 重新认证
        if (tabIndex === 1) {
          let ids = sltList.map((e) => e.id)
          this.restartProjects(ids)
        }
      } else if (toolbar.id == 'export') {
        // 导出
        this.handleExport(tabIndex)
      }
    },
    deleteDemends(param) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选认证需求？'),
          confirm: () => this.$API.CategoryCertification.deleteDemand({ ids: param })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    deleteCategorys(param) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选品类认项目？'),
          confirm: () => this.$API.CategoryCertification.deleteProject({ ids: param })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    // 终止认证
    stopProjects(param) {
      this.$dialog({
        data: {
          title: this.$t('终止认证'),
          message: this.$t('是否确认对所选品类认项目终止认证？'),
          confirm: () => this.$API.CategoryCertification.stopProjects(JSON.stringify(param))
        },
        success: () => {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    // 重新认证
    restartProjects(param) {
      this.$dialog({
        data: {
          title: this.$t('重新认证'),
          message: this.$t('是否确认对所选品类认项目重新寻源？'),
          confirm: () => this.$API.CategoryCertification.restartProjects(JSON.stringify(param))
        },
        success: () => {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    handleClickCellTitle(e) {
      let _id
      if (e.field == 'authProjectCode') {
        _id = e.data.authProjectId
        this.$router.push({
          name: 'pur-category-certification-detail',
          query: {
            id: _id,
            type: e.tabIndex
          }
        })
      } else if (e.field == 'projectCode' && e.tabIndex === 1) {
        _id = e.data.id
        this.$router.push({
          name: 'pur-category-certification-detail',
          query: {
            id: _id,
            type: e.tabIndex
          }
        })
      } else if (e.field == 'demandCode' && e.tabIndex === 0) {
        this.$dialog({
          modal: () => import('./components/certificationDialog.vue'),
          data: {
            title: this.$t('编辑认证需求'),
            isEdit: true,
            info: e.data,
            disable: e.data.authProjectCode ? true : false
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    handleClickCellTool(e) {
      const { tool, data, tabIndex } = e
      console.log(data)
      const { id } = tool || {}
      if (id == 'edit') {
        if (tabIndex === 0) {
          this.$dialog({
            modal: () => import('./components/certificationDialog.vue'),
            data: {
              title: this.$t('编辑认证需求'),
              isEdit: true,
              info: data
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (tabIndex === 1) {
          this.$dialog({
            modal: () => import('./components/categoryDialog.vue'),
            data: {
              title: this.$t('编辑品类认证项目'),
              isEdit: true,
              info: data
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      } else if (id == 'delete') {
        if (tabIndex === 0) {
          this.deleteDemends(data.id)
        } else if (tabIndex === 1) {
          this.deleteCategorys(data.id)
        }
      }
    },
    // 导出
    handleExport(tabIndex) {
      const rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: rule.condition || '',
        rules: rule.rules || []
      }
      const requestUrl = {
        preName: 'CategoryCertification',
        urlName: tabIndex === 0 ? 'exportCertifyDemand' : 'exportCertifyProject'
      }
      exportData(requestUrl, params)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
<style lang="scss">
.category-certiry-container {
  .mt-tabs.mt-tabs--height {
    height: 46px !important;
  }
}
.mt-tabs ul.tab-container li.tab-item2 {
  color: #6a6767 !important;
}
</style>
