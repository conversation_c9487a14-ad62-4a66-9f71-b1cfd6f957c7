<template>
  <div class="add-supplier-area">
    <mt-dialog
      ref="dialogArea"
      css-class="bule-bg add-area"
      :width="1200"
      min-height="600"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div class="dialog-content addCompany-dialog">
        <div class="form-box">
          <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" :label-width="1200">
            <mt-row>
              <mt-col :span="24">
                <mt-form-item prop="companyId" class="form-item" :label="$t('公司')">
                  <mt-select
                    v-model="ruleForm.companyId"
                    :value="ruleForm.companyId"
                    :allow-filtering="true"
                    :data-source="companyArrList"
                    :placeholder="$t('请选择供应商企业全称')"
                    @select="selectCompany"
                    :fields="{ text: 'orgName', value: 'id' }"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
            </mt-row>

            <div class="dialog-content-h4 fbox" v-if="!!currentCompany.id">
              <div class="flex1">
                <h4>{{ $t('待选项') }}</h4>
                <div class="all-list-box">
                  <div class="company-tree-box flex1">
                    <template v-if="factoryArray.length > 0">
                      <div
                        class="factory-item"
                        :class="{ active: item.id === factoryId }"
                        v-for="item of factoryArray"
                        :key="item.id"
                        @click="selectFactory(item)"
                      >
                        {{ item.siteName }}
                      </div>
                    </template>
                    <template v-else>
                      <p class="category-no-data">{{ $t('暂无数据') }}</p>
                    </template>
                  </div>
                  <div class="category-tree-box flex1">
                    <template v-if="categoryTree.dataSource.length > 0">
                      <mt-tree-view
                        key="category-tree"
                        class="category-tree"
                        v-if="showCategoryTree"
                        ref="treeViewCategory"
                        :fields="categoryTree"
                        :show-check-box="true"
                        :checked-nodes="checkedNodes"
                        @nodeChecked="nodeCheckedCategory"
                      ></mt-tree-view>
                    </template>
                    <p v-if="categoryTree.dataSource.length === 0" class="category-no-data">
                      {{ $t('暂无数据') }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="select-list-box flex1">
                <h4>{{ $t('已选项') }}</h4>
                <div class="select-list">
                  <mt-tree-view
                    key="select-tree"
                    class="select-tree"
                    v-if="showSelectTree"
                    ref="treeViewSelect"
                    :fields="selectTree"
                  ></mt-tree-view>
                  <p v-if="selectTree.dataSource.length === 0" class="category-no-data">
                    {{ $t('请先选择公司') }}
                  </p>
                  <p
                    v-else-if="selectTree.dataSource.length > 0"
                    class="category-delete-data"
                    @click="clearSelect"
                  >
                    {{ $t('清空选择') }}
                  </p>
                </div>
              </div>
            </div>
          </mt-form>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import utils from '@/utils/utils.js'
let factoryCategoryMap = new Map()

export default {
  data() {
    return {
      companyArrList: [],
      ruleForm: {
        companyId: ''
      },
      rules: {
        companyId: [
          {
            required: true,
            message: this.$t('请选择企业全称'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('新增') }
        }
      ],
      factoryArray: [],
      categoryTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      // 已选品类
      selectCateGroyArr: {},
      // 已勾选的ID

      checkedNodes: [],
      checkedNodesObj: [],

      deepArr: [],

      parentNodeArr: [],
      parentNode: [],

      sltCompanyList: [],

      currentFactory: {},
      factoryId: -1,
      // 已选的公司对象 单选
      currentCompany: {
        id: ''
      },
      showCategoryTree: false, // treeview数据不更新。。
      showSelectTree: false, // treeview数据不更新。。
      selectTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      }
    }
  },

  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  computed: {
    header() {
      return this.modalData.title
    },
    dataArr() {
      return this.modalData.distributeArr || []
    }
  },

  async created() {
    let companyOrg = await this.getUserDetail()
    if (!companyOrg || !companyOrg.id) {
      this.$hloading()
      this.$toast({ content: this.$t('获取当前组织信息失败，请重试'), type: 'error' })
      return
    }
    this.getChildrenCompanyOrganization(companyOrg.id)
  },

  mounted() {
    this.$refs['dialogArea'].ejsRef.show()
  },

  methods: {
    // 获取当前用户信息
    // getUserDetail() {
    //   this.$loading();
    //   setTimeout(() => {
    //     this.$hloading();
    //   }, 1000);
    //   return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
    //     let { data } = res;
    //     let { companyOrg } = data;
    //     return companyOrg;
    //   });
    // },

    beforeClose() {
      this.$refs['dialogArea'].ejsRef.hide()
      this.$emit('cancel-function')
    },

    // 获取ORG02公司 数组格式
    getChildrenCompanyOrganization() {
      // this.$API.supplierInvitation["getChildrenCompanyOrganization"]({
      //   organizationId,
      // }).then((result) => {
      this.$API.supplierInvitation['getChildrenCompanyOrganization2']({
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true
      }).then((result) => {
        this.$hloading()
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          let companyArrList = result.data.filter((item) => {
            return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
          })
          this.companyArrList = companyArrList
        } else {
          this.companyArrList = []
        }
      })
    },

    // 选择公司
    async selectCompany(e) {
      // 有级联
      let { itemData } = e
      itemData.name = itemData.orgName
      console.log('selectCompany', itemData)
      this.ruleForm.companyId = itemData.id
      this.currentCompany = itemData
      // 新增
      let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)
      index === -1 && this.sltCompanyList.push(this.currentCompany)

      // 渲染 公司名称 已选树 start
      this.showSelectTree = false
      if (index === -1) {
        // 新增
        let dataSource = this.selectTree.dataSource
        dataSource = [
          ...dataSource,
          {
            id: itemData.id,
            name: itemData.name,
            expanded: true,
            children: []
          }
        ]
        this.$set(this.selectTree, 'dataSource', dataSource)
      }
      setTimeout(() => {
        this.showSelectTree = true
      }, 200)
      // 渲染已选树 end

      // 获取公司对应的工厂 todo 接口缺失
      this.factoryArray = await this.getSite()
      if (this.factoryArray.length === 0) {
        // this.$toast({ content: "工厂数据为空！", type: "error" });
        // 重置 品类
        this.categoryTree = {
          dataSource: [],
          id: 'id',
          text: 'name',
          child: 'children'
        }
        return
      }

      this.currentFactory =
        !!this.factoryArray[0] && !utils.isEmpty(this.factoryArray[0]) ? this.factoryArray[0] : {}

      // 根据公司获取品类树 接口还没有 暂时用账户获取品类数 getProdcutTree
      this.getCateGoryTree(this.currentCompany, this.factoryArray[0].id, true)

      let cindex = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)

      this.sltCompanyList[cindex].children = [this.factoryArray[0]]
    },

    /**
     * 根据公司获取工厂
     */
    getSite() {
      let query = {
        parentId: this.currentCompany.id || ''
      }

      return this.$API.supplierInvitationAdd['getSite'](query).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result)) {
          return result.data
        } else {
          return []
        }
      })
    },

    /**
     * 获取品类树 (假接口 传参未传)
     * @currentCompany 公司信息
     * @factoryObject 工厂信息
     * @isInit 是否是第一个 要展开
     */
    getCateGoryTree(currentCompany, factoryObjectId, isInit = false) {
      this.showCategoryTree = false
      // 缓存品类树
      if (factoryCategoryMap.has(factoryObjectId)) {
        this.categoryTree = factoryCategoryMap.get(factoryObjectId)

        this.$nextTick(() => {
          this.initSelectTree()
        })
        setTimeout(() => {
          this.showCategoryTree = true
        }, 500)
        return
      }

      this.$API.supplierInvitation
        .getProdcutTree({
          id: currentCompany.id
        })
        .then((res) => {
          setTimeout(() => {
            this.showCategoryTree = true
          }, 500)

          this.$nextTick(() => {
            this.initSelectTree()
          })

          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            this.categoryTree = Object.assign(
              {},
              {
                dataSource: [],
                id: 'id',
                text: 'name',
                child: 'children'
              },
              {
                dataSource: data
              }
            )
            if (isInit) {
              this.factoryId = factoryObjectId
            }
            // 缓存品类树
            factoryCategoryMap.set(factoryObjectId, JSON.parse(JSON.stringify(this.categoryTree)))
          } else {
            // 空数据 重置
            this.categoryTree = {
              dataSource: [],
              id: 'id',
              text: 'name',
              child: 'children'
            }
          }
        })
    },

    // 初始化品类的勾选
    initSelectTree() {
      const { id: currentCompanyId } = this.currentCompany
      let index = this.checkedNodesObj.findIndex(
        (item) => item.id === currentCompanyId + '_' + this.factoryId
      )
      this.checkedNodes = index > -1 ? this.checkedNodesObj[index].nodes : []
    },

    // 选择工厂
    selectFactory(item) {
      let factoryId = item.id
      this.factoryId = factoryId
      this.currentFactory = item

      this.showCategoryTree = false

      // 设置工厂
      let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)
      let factoryList = this.sltCompanyList[index].children
      let cIndex = factoryList.findIndex((item) => item.id === factoryId)
      if (cIndex === -1) {
        this.sltCompanyList[index].children.push(item)
      }

      if (factoryCategoryMap.has(factoryId)) {
        this.categoryTree = factoryCategoryMap.get(factoryId)

        this.$nextTick(() => {
          this.initSelectTree()
        })
        setTimeout(() => {
          this.showCategoryTree = true
        }, 500)

        return
      }

      // 根据公司获取品类树
      this.getCateGoryTree(this.currentCompany, factoryId)
    },

    nodeCheckedCategory(args) {
      /**
       * 返回的data数组中，第一个应该是当前点击的currentNode
       * 如果当前动作是check,如果currentNode的hasChildren是true，找到他下面的叶子节点，禁用
       * 如果currentNode的hasChildren是false，，选中的nodes里如果有hasChildren是true的，禁用他的子节点
       * 如果当前动作是uncheck：取消所有的禁用，...
       */
      this.showSelectTree = false
      const treeViewRef = this.$refs.treeViewCategory.ejsRef
      const { action, data } = args
      let currentNode = data && data.length && data[0]
      let disabledNodes = treeViewRef.getDisabledNodes()
      let allCheckedNodes = treeViewRef.getAllCheckedNodes()
      // 保存当前公司的品类选择
      let index = this.checkedNodesObj.findIndex(
        (item) => item.id === this.currentCompany.id + '_' + this.factoryId
      )

      // 叶子节点的禁用逻辑
      if (action === 'check' && currentNode.hasChildren) {
        let currentTree = treeViewRef.getTreeData(currentNode.id)
        let leafs = this.getLeaf(currentTree)
        treeViewRef.disableNodes(leafs)

        index === -1 &&
          this.checkedNodesObj.push({
            id: this.currentCompany.id + '_' + this.factoryId,
            nodes: allCheckedNodes
          })
      } else {
        action === 'uncheck' && treeViewRef.enableNodes(disabledNodes)
        let sltNodes = allCheckedNodes.map((id) => {
          return treeViewRef.getNode(id)
        })
        sltNodes.forEach((node) => {
          if (node.hasChildren) {
            let currentTree = treeViewRef.getTreeData(node.id)
            let leafs = this.getLeaf(currentTree)
            treeViewRef.disableNodes(leafs)
          }
        })

        index > -1 && this.checkedNodesObj.splice(index, 1)
      }

      let nodes = this.getSltNodes()
      if (!!nodes && nodes.length > 0) {
        let factoryCategroy = {
          ...this.currentFactory,
          id: this.currentFactory.id,
          name: this.currentFactory.siteName,
          children: nodes
        }

        let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)

        let cIndex = this.sltCompanyList[index].children.findIndex(
          (item) => item.id === this.factoryId
        )

        this.sltCompanyList[index].children[cIndex] = {
          ...factoryCategroy
        }

        let sltTreeData = this.sltCompanyList.map((node) => {
          let children = node.children
          if (node.children) {
            children = children
              .filter((item) => item.children && item.children.length)
              .map((vNode) => {
                return {
                  ...vNode,
                  expanded: true
                }
              })
          }
          return {
            id: node.id,
            name: node.name,
            expanded: true,
            children
          }
        })
        this.$set(this.selectTree, 'dataSource', sltTreeData)
        setTimeout(() => {
          this.showSelectTree = true
        }, 200)
      } else {
        // 当前的工厂下没选择品类 清空当前工厂下的品类数组
        let factoryCategroy = {
          ...this.currentFactory,
          id: this.currentFactory.id,
          name: this.currentFactory.siteName,
          children: []
        }

        let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)

        let cIndex = this.sltCompanyList[index].children.findIndex(
          (item) => item.id === this.factoryId
        )

        this.sltCompanyList[index].children[cIndex] = {
          ...factoryCategroy
        }

        let sltTreeData = this.sltCompanyList.map((node) => {
          let children = node.children
          if (node.children) {
            children = children
              .filter((item) => item.children && item.children.length) // 只收集 工厂下面有品类得
              .map((vNode) => {
                return {
                  ...vNode,
                  expanded: true
                }
              })
          }
          return {
            id: node.id,
            name: node.name,
            expanded: true,
            children
          }
        })
        this.$set(this.selectTree, 'dataSource', sltTreeData)
        setTimeout(() => {
          this.showSelectTree = true
        }, 200)
      }
    },

    // 清除选择
    clearSelect() {
      // 清除选择树
      this.selectTree = {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      }
      // 清除勾选的状态 start
      this.checkedNodesObj = []
      this.checkedNodes = []
      this.sltCompanyList = []
      // 清除勾选的状态 end

      // 恢复到当前公司选择
      let currentCompany = this.currentCompany
      this.sltCompanyList.push(this.currentCompany)

      // 渲染 公司名称 已选树 start
      this.showSelectTree = false

      this.$set(this.selectTree, 'dataSource', [
        {
          id: currentCompany.id,
          name: currentCompany.name,
          expanded: true,
          children: []
        }
      ])
      setTimeout(() => {
        this.showSelectTree = true
      }, 200)
      // 渲染已选树 end
    },

    /**
     * 选中的节点，保存为树形
     * 非叶子节点的isChecked表示该节点是否选中（他的叶子节点被选中了部分，则是false）
     */
    getSltNodes() {
      const treeViewRef = this.$refs.treeViewCategory.ejsRef
      let checkedId = treeViewRef.getAllCheckedNodes()
      let sltParentNode = []
      if (checkedId && checkedId.length) {
        // const { dataSource } = this.categoryTree;
        let dataSource = JSON.parse(JSON.stringify(this.categoryTree.dataSource))
        let tree = this.setCheckStatus(dataSource, checkedId)

        const getSltParentNode = (tree) => {
          if (tree && tree.length) {
            tree.forEach((node) => {
              if (node.isChecked) {
                delete node.children // 只收集父节点
                sltParentNode.push(node)
              } else if (node.children && node.children.length) {
                getSltParentNode(node.children)
              }
            })
          }
        }
        getSltParentNode(tree)
      }
      return sltParentNode || []
    },

    // 加状态 isChecked
    setCheckStatus(tree, checkedId) {
      if (tree && tree.length) {
        tree.forEach((node) => {
          node.isChecked = false
          if (node.children && node.children.length) {
            let childrenIds = node.children.map((child) => {
              return child.id
            })
            node.isChecked = this.isContainArr(checkedId, childrenIds)
            this.setCheckStatus(node.children, checkedId)
          } else {
            let index = checkedId.findIndex((id) => id === node.id)
            index > -1 && (node.isChecked = true)
          }
        })
      }
      return tree
    },

    isContainArr(parent, child) {
      return child.every((item) => {
        return parent.some((v) => {
          return item == v
        })
      })
    },

    // 获取叶子节点的id
    getLeaf(tree) {
      const leaf = []
      const getChild = (tree) => {
        tree &&
          tree.forEach((item) => {
            if (item.children && item.children.length) {
              getChild(item.children)
            } else {
              let index = leaf.findIndex((leafItem) => leafItem === item.id)
              index === -1 && leaf.push(item.id)
            }
          })
      }
      getChild(tree)
      return leaf
    },

    confirm() {
      if (this.sltCompanyList.length === 0) {
        this.$toast({ content: this.$t('请选择公司'), type: 'error' })
        return
      }
      if (!!this.sltCompanyList && this.sltCompanyList.length > 0) {
        let floatData = this.floatCompanyList(this.sltCompanyList)
        this.$emit('confirm-function', floatData)
        return
      }
    },

    // 扁平化树
    floatCompanyList(treeData) {
      let finalData = []
      let companyData = {}
      let factoryData = {}
      let categroyData = {}
      treeData.forEach((item) => {
        companyData = {
          orgId: item.id,
          orgCode: item.orgCode,
          orgName: item.name
        }
        if (!!item.children && item.children.length > 0) {
          let categroyArray = item.children.filter(
            (CCItem) => !!CCItem.children && !!CCItem.children.length > 0
          )
          if (categroyArray.length > 0) {
            categroyArray.forEach((cItem) => {
              factoryData = {
                factoryId: cItem.id,
                factoryName: cItem.siteName,
                factoryCode: cItem.siteCode
              }
              cItem.children.forEach((CCItem) => {
                categroyData = {
                  categoryCode: CCItem.categoryCode,
                  categoryId: CCItem.id,
                  categoryName: CCItem.name
                }

                finalData.push({
                  ...companyData,
                  ...factoryData,
                  ...categroyData,
                  // 类型 0:公司 1:品类
                  relatedType: 1
                })
              })
            })
          } else {
            finalData.push({
              ...companyData,
              factoryId: '',
              factoryName: '',
              factoryCode: '',

              categoryCode: '',
              categoryId: '',
              categoryName: '',

              relatedType: 0
            })
          }
        } else {
          finalData.push({
            ...companyData,
            factoryId: '',
            factoryName: '',
            factoryCode: '',

            categoryCode: '',
            categoryId: '',
            categoryName: '',

            relatedType: 0
          })
        }
      })

      return finalData
    },

    cancel() {
      this.$emit('cancel-function')
    }
  },

  beforeDestroy() {
    factoryCategoryMap.clear()
  }
}
</script>

<style lang="scss">
.fbox {
  display: flex;
}
.flex1 {
  flex: 1;
}
.add-area {
  .e-dlg-content {
    padding: 0 !important;
  }
  .addCompany-dialog {
    padding: 40px;
  }

  .accordion-header {
    padding: 10px 0;
    .flex-title {
      font-size: 14px;

      font-weight: 500;
      color: rgba(99, 134, 193, 1);
    }

    .flex-select-list {
      display: flex;
      margin-top: 10px;
      .category-item {
        display: flex;
        align-items: center;
        padding: 0 4px;
        height: 22px;
        line-height: 22px;
        background: rgba(245, 245, 245, 1);
        border-radius: 2px;
        margin-right: 14px;
        font-size: 14px;

        font-weight: normal;
        color: rgba(41, 41, 41, 1);

        .mt-icons {
          display: inline-block;
          width: 10px;
          height: 10px;
          margin-left: 4px;
          color: #9baac1;
          font-size: 10px;
        }
      }

      .clear-item {
        height: 22px;
        line-height: 22px;
        font-size: 14px;

        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        margin-left: 50px;
      }
    }
  }

  .e-acrdn-content {
    padding: 0 20px !important;
  }

  .e-acrdn-content {
    padding: 0 20px !important;
  }

  #treeview > .e-list-parent {
    padding: 0 !important;
  }

  .dialog-content-h4 {
    h4 {
      font-size: 14px;
      color: #292929;
      font-weight: bold;
      margin: 20px 0 10px 0;
    }
    .all-list-box {
      display: flex;
      background: #fff;
      border: 1px solid #e8e8e8;
      min-height: 300px;
      border-radius: 4px;
    }
    .company-tree-box {
      width: 300px;
      min-height: 300px;

      .factory-item {
        padding-left: 40px;
        width: 100%;
        height: 30px;
        line-height: 30px;
        cursor: pointer;
      }

      .active {
        background: #f5f6f9;
      }
    }

    .category-tree-box {
      flex: 1;
      width: 200px;
      min-height: 300px;
      border-left: 1px solid #e8e8e8;
      overflow: scroll;
    }
    .select-list-box {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-left: 20px;
      .select-list {
        flex: 1;
        padding: 10px 20px;
        min-height: 300px;
        background: #fff;
        border: 1px solid #e8e8e8;
        width: 100%;
        border-radius: 4px;

        .select-tree {
          width: 100%;
        }
      }
    }
    .category-no-data {
      font-size: 14px;
      color: #9a9a9a;
      padding-top: 20px;
      text-align: center;
    }
    .category-delete-data {
      width: 100%;
      text-align: center;
      font-size: 14px;

      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      margin-top: 38px;
      cursor: pointer;
    }
  }
}
</style>
