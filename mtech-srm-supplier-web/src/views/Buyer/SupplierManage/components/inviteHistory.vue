<template>
  <div class="history-box">
    <mt-template-page
      :padding-top="false"
      :use-tool-template="false"
      :template-config="componentConfig"
    ></mt-template-page>
  </div>
</template>

<script>
import { historyColumnData } from '../config/index'
import utils from '@/utils/utils.js'
export default {
  props: {
    supplierEnterpriseId: {
      type: String,
      default: ''
    }
  },
  watch: {
    supplierEnterpriseId: {
      handler(nv) {
        if (nv) {
          this.getHistoryInvite(nv)
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      componentConfig: [
        {
          gridId: '899bf233-fd51-475c-bf87-7e96b80e5bb1',
          toolbar: [],
          grid: {
            allowPaging: false,
            columnData: historyColumnData,
            dataSource: [],
            page: {
              current: 1,
              size: 20000
            }
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    /**
     * 获取邀请历史
     */
    getHistoryInvite(supplierEnterpriseId) {
      if (!supplierEnterpriseId) {
        this.$set(this.componentConfig[0].grid, 'dataSource', [])
        return false
      }

      this.$API.supplierInvitation
        .listExtHistory({
          supplierEnterpriseId: supplierEnterpriseId || ''
        })
        .then((res) => {
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            this.$set(this.componentConfig[0].grid, 'dataSource', data)
          } else {
            // 空数据
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
<style lang="scss">
.history-box {
  height: 266px;
}
// 公共样式，其他页面会用
.supplier-table {
  height: 266px;
}
</style>
