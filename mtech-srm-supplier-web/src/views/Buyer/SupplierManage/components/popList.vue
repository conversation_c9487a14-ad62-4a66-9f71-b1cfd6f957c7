<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content addsupplier-dialog">
      <div class="pop-list">
        <mt-template-page
          ref="gridlist"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="componentConfig"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
// const pageParams = {
//   condition: '',
//   page: {
//     current: 1,
//     size: 10
//   },
//   rules: [
//     {
//       field: 'inviteStatus',
//       label: '',
//       operator: 'equal',
//       type: 'number',
//       value: 1
//     }
//   ]
// }
import { columnDataMain3 } from '../config'
import utils from '@/utils/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmAndInvite,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并邀请') }
        }
      ],
      componentConfig: [
        {
          gridId: 'cc35fc2e-abcb-47f2-a3e2-d8e56ee2ce8e',
          title: this.$t('已认领'),
          toolbar: [],
          grid: {
            columnData: columnDataMain3(),
            asyncConfig: {
              url: '/supplier/tenant/buyer/invite/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  field: 'inviteStatus',
                  label: '',
                  operator: 'equal',
                  type: 'number',
                  value: 1
                }
              ]
            }
          }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    inviteArr() {
      return this.modalData.inviteArr || []
    }
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },

    confirm() {
      // 获取区域 并判断对应的邀请是否重复添加（id 或者 内容一直）
      this.sendInviteExtList('confirm')
    },

    confirmAndInvite() {
      // 获取区域 并判断对应的邀请是否重复添加（id 或者 内容一直）
      this.sendInviteExtList('confirmAndInvite')
    },

    // 获取区域 并判断对应的邀请是否重复添加（id 或者 内容一直）
    sendInviteExtList(mode) {
      let selectRows =
        !!this.$refs.gridlist.getCurrentUsefulRef() &&
        !!this.$refs.gridlist.getCurrentUsefulRef().gridRef &&
        !!this.$refs.gridlist.getCurrentUsefulRef().gridRef.getMtechGridRecords()
          ? this.$refs.gridlist.getCurrentUsefulRef().gridRef.getMtechGridRecords()
          : []

      if (selectRows.length === 0) {
        this.$emit('confirm-function', {
          inviteData: [],
          areaData: [],
          mode: ''
        })
        return
      }

      let idArr = selectRows.map((v) => v.id)
      let selectedData = this.modalData.inviteArr.map((v) => v.id)
      let resultData = [].concat(idArr, selectedData)

      this.$API.supplierInvitation['sendInviteExtList']({
        inviteIdList: resultData
      }).then((result) => {
        console.log(result)
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          this.$emit('confirm-function', {
            inviteData: selectRows,
            areaData: result.data,
            mode
          })
        } else {
          this.$toast({
            content: result.msg || '获取数据失败，请重试！',
            type: 'warning'
          })
        }
      })
    }
  },
  created() {},
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  }
}
</script>

<style lang="scss" scoped></style>
