<template>
  <div class="add-supplier-area">
    <mt-dialog
      ref="dialog"
      css-class="bule-bg add-area"
      :width="1200"
      min-height="600"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div class="dialog-content addCompany-dialog">
        <div class="form-box">
          <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" :label-width="1200">
            <mt-row>
              <mt-col :span="24">
                <mt-form-item prop="orgName" class="form-item" :label="$t('公司')">
                  <mt-DropDownTree
                    :fields="companyFields"
                    :show-check-box="false"
                    id="checkboxTreeSelect"
                    :placeholder="$t('组织树状图，可选至任意层级、单选')"
                    :allow-multi-selection="false"
                    :auto-check="false"
                    @select="companyChange"
                  ></mt-DropDownTree>
                </mt-form-item>
              </mt-col>
            </mt-row>

            <template v-if="!!selectCompanyObject.id">
              <mt-collapse :accordion="true" v-model="collapseValue">
                <template v-for="item of factoryArray">
                  <mt-panel :name="item.id" :data-id="item.id" :key="item.id">
                    <div slot="header" class="accordion-header">
                      <div class="flex-title">{{ item.siteName }}</div>
                      <div
                        class="flex-select-list fbox"
                        @click="categoryClick"
                        v-if="!!selectCateGroyArr[item.id] && selectCateGroyArr[item.id].length > 0"
                      >
                        <div
                          class="category-item"
                          v-for="child of selectCateGroyArr[item.id]"
                          :key="child.id"
                          :data-id="child.id"
                        >
                          {{ child.name }}
                          <i
                            class="mt-icons mt-icon-grp-del"
                            :data-deleteId="child.id"
                            @click.stop="deleteCategory(item.id, child.id)"
                          ></i>
                        </div>

                        <div class="clear-item" @click="clearCategroy(item.id)">
                          {{ $t('清空品类') }}
                        </div>
                      </div>
                    </div>
                    <div slot="content">
                      <!-- :checkedNodes='checkedNodes'   :disabled="true" -->
                      <mt-treeView
                        :ref="'cateTree' + item.id"
                        :fields="categoryFileds"
                        :show-check-box="true"
                        @nodeOnCheck="nodeChecked"
                        @nodeChecked="nodeChecked2"
                        @nodeChecking="nodeChecking"
                      ></mt-treeView>
                    </div>
                  </mt-panel>
                </template>
              </mt-collapse>
            </template>
          </mt-form>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import mtCollapse from '../../../../components/collapse/collapse.vue'
import mtPanel from '../../../../components/collapse/panel.vue'
import MtTreeView from '@mtech-ui/tree-view'
import utils from '@/utils/utils.js'

let factoryCategoryMap = new Map()

export default {
  components: {
    'mt-collapse': mtCollapse,
    'mt-panel': mtPanel,
    'mt-treeView': MtTreeView
  },
  watch: {
    collapseValue(nv) {
      // 合并操作
      if (!nv || nv.length === 0) return
      let factoryId = nv

      if (factoryCategoryMap.has(factoryId)) {
        this.categoryFileds = factoryCategoryMap.get(factoryId)
        // 已勾选id 用于勾选
        this.checkedNodes =
          !!this.selectCateGroyArr[factoryId] && this.selectCateGroyArr[factoryId].length > 0
            ? this.selectCateGroyArr[factoryId].map((v) => v.id)
            : []
        return
      }

      // 根据公司获取品类树 接口还没有 暂时用账户获取品类数 getProdcutTree
      this.getCateGoryTree(this.selectCompanyObject, factoryId)
    }
  },
  data() {
    return {
      collapseValue: '',

      ruleForm: {
        orgName: ''
      },
      rules: {
        orgName: [
          {
            required: true,
            message: this.$t('请输入企业全称'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('新增') }
        }
      ],

      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },

      // 已选的公司对象 单选
      selectCompanyObject: {},

      factoryArray: [],

      categoryFileds: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      // 已选品类
      selectCateGroyArr: {},
      // 已勾选的ID

      checkedNodes: [],
      checkedNodesObj: [],

      deepArr: [],

      parentNodeArr: [],
      parentNode: [],

      disAbleArr: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    dataArr() {
      return this.modalData.distributeArr || []
    }
  },
  created() {
    this.getCompanyTree()
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    /**
     * 选哪个 传哪个
     * 选父节点的话 传父节点 自己点置灰
     */
    nodeChecked(args) {
      console.log('nodeChecked ', args)
    },
    // 获取叶子节点的id
    getLeaf(tree) {
      const leaf = []
      const getChild = (tree) => {
        tree &&
          tree.forEach((item) => {
            if (item.children && item.children.length) {
              getChild(item.children)
            } else {
              let index = leaf.findIndex((leafItem) => leafItem === item.id)
              index === -1 && leaf.push(item.id)
            }
          })
      }
      getChild(tree)
      return leaf
    },

    /**
     * 返回的data数组中，第一个应该是当前点击的currentNode
     * 如果当前动作是check,如果currentNode的hasChildren是true，找到他下面的叶子节点，禁用
     * 如果currentNode的hasChildren是false，，选中的nodes里如果有hasChildren是true的，禁用他的子节点
     * 如果当前动作是uncheck：取消所有的禁用，...
     */
    nodeChecked2(args) {
      console.log('nodeChecked2 ', args)
      const treeViewRef = this.$refs[`cateTree${this.collapseValue}`][0].ejsRef
      const { action, data } = args
      let currentNode = data && data.length && data[0]
      let disabledNodes = treeViewRef.getDisabledNodes()
      let allCheckedNodes = treeViewRef.getAllCheckedNodes()
      console.log(currentNode, disabledNodes, allCheckedNodes)

      // 保存当前公司的品类选择
      let index = this.checkedNodesObj.findIndex((item) => item.id === this.collapseValue)
      index === -1 &&
        this.checkedNodesObj.push({
          id: this.collapseValue,
          nodes: allCheckedNodes
        })

      // 叶子节点的禁用逻辑
      if (action === 'check' && currentNode.hasChildren) {
        let currentTree = treeViewRef.getTreeData(currentNode.id)
        let leafs = this.getLeaf(currentTree)
        treeViewRef.disableNodes(leafs)
      } else {
        action === 'uncheck' && treeViewRef.enableNodes(disabledNodes)
        let sltNodes = allCheckedNodes.map((id) => {
          return treeViewRef.getNode(id)
        })
        sltNodes.forEach((node) => {
          if (node.hasChildren) {
            let currentTree = treeViewRef.getTreeData(node.id)
            let leafs = this.getLeaf(currentTree)
            treeViewRef.disableNodes(leafs)
          }
        })
      }
    },

    // 搞子节点禁用逻辑
    nodeChecking(args) {
      let parentId = args.node.dataset.uid
      if (args.action === 'check') {
        this.getParentNode([parentId])
      } else {
        // 取消勾选
        let tmpSelectCateGroyArr = this.selectCateGroyArr[this.collapseValue]
        let index = tmpSelectCateGroyArr.findIndex((v) => v.id === parentId)
        tmpSelectCateGroyArr.splice(index, 1)
        // 已勾选的对象数组
        this.$set(
          this.selectCateGroyArr,
          this.collapseValue,
          JSON.parse(JSON.stringify(tmpSelectCateGroyArr))
        )
        this.checkedNodes = tmpSelectCateGroyArr.map((v) => v.id)
      }
    },

    renderDisabled(allFileds, parentId) {
      allFileds.forEach((dataItem) => {
        if (dataItem.id === parentId) {
          let children = dataItem.children
          children.forEach((v) => {
            v.disabled = true
            this.disAbleArr.push(v.id)
          })
        } else {
          if (!!dataItem.children && dataItem.children.length > 0) {
            this.renderDisabled(dataItem.children, parentId)
          }
        }
      })
    },

    // 加disbale false
    renderAbled(allFileds, parentId) {
      allFileds.forEach((dataItem) => {
        if (dataItem.id === parentId) {
          let children = dataItem.children
          children.forEach((v) => {
            v.disabled = false
          })
        } else {
          // 全部加disabled false
          if (!parentId) {
            dataItem.disabled = false
            dataItem.cssClass = 'test'
            if (!!dataItem.children && dataItem.children.length > 0) {
              this.renderAbled(dataItem.children)
            }
          }
          if (!!dataItem.children && dataItem.children.length > 0) {
            this.renderDisabled(dataItem.children, parentId)
          }
        }
      })
      // return allFileds
    },

    /**
     * 根据节点数组 获取最底层节点
     * @allNodes 全部节点
     */
    getChildNode(allNodes) {
      let categoryFileds = factoryCategoryMap.get(this.collapseValue)
      let { dataSource } = categoryFileds

      this.deepArr = []
      dataSource.forEach((v) => {
        this.deepLoopTree(v, allNodes)
      })

      // 根据 parentId 拿最底层的
      let parentIdArr = this.deepArr.map((v) => v.parentId)
      let selectDeepArr = []
      this.deepArr.forEach((node) => {
        if (!parentIdArr.includes(node.id)) {
          selectDeepArr.push({
            categoryCode: node.categoryCode,
            id: node.id,
            name: node.name,
            parentId: node.parentId
          })
        }
      })

      // 已勾选的对象数组
      this.$set(
        this.selectCateGroyArr,
        this.collapseValue,
        JSON.parse(JSON.stringify(selectDeepArr))
      )
      this.checkedNodes = allNodes
      console.log('底层的对象', selectDeepArr, this.checkedNodes)
    },

    // 获取当前点击的node
    getParentNode(allNodes) {
      let categoryFileds = factoryCategoryMap.get(this.collapseValue)
      let { dataSource } = categoryFileds

      this.parentNode = []
      dataSource.forEach((v) => {
        this.deepLoopTree2(v, allNodes)
      })

      let TmpparentNodeArr = [
        ...(this.selectCateGroyArr[this.collapseValue]
          ? this.selectCateGroyArr[this.collapseValue]
          : []),
        ...this.parentNode
      ]
      // 已勾选的对象数组
      this.$set(
        this.selectCateGroyArr,
        this.collapseValue,
        JSON.parse(JSON.stringify(TmpparentNodeArr))
      )
      this.checkedNodes = this.parentNodeArr.map((v) => v.id)
      console.log('parentnode Ids : ', this.parentNode, this.parentNodeArr)
    },

    /**
     * 根据组建返回的所有的节点id  递归出节点详细对象信息
     */
    deepLoopTree(node, allNodes) {
      if (allNodes.includes(node.id)) {
        this.deepArr.push(node)

        if (!!node.children && node.children.length > 0) {
          node.children.forEach((v) => {
            this.deepLoopTree(v, allNodes)
          })
        }
      }
    },
    deepLoopTree2(node, allNodes) {
      if (allNodes.includes(node.id)) {
        this.parentNode.push(node)

        if (!!node.children && node.children.length > 0) {
          node.children.forEach((v) => {
            this.deepLoopTree2(v, allNodes)
          })
        }
      } else {
        if (!!node.children && node.children.length > 0) {
          node.children.forEach((v) => {
            this.deepLoopTree2(v, allNodes)
          })
        }
      }
    },

    confirm() {
      let resultArr = []
      //  this.selectCompanyObject  // this.factoryArray  // this. this.selectCateGroyArr
      for (let [factoryId, categoryArrays] of Object.entries(this.selectCateGroyArr)) {
        // 获取工厂的对象全部字段
        let factoryFullMsg = this.factoryArray.filter((v) => v.id === factoryId)[0]
        let cateResultArr = categoryArrays.map((categoryItem) => {
          return {
            orgId: this.selectCompanyObject.id,
            orgCode: this.selectCompanyObject.orgCode,
            orgName: this.selectCompanyObject.name,

            factoryId: factoryFullMsg.id,
            factoryName: factoryFullMsg.siteName,
            factoryCode: factoryFullMsg.siteCode,

            categoryCode: categoryItem.categoryCode,
            categoryId: categoryItem.id,
            categoryName: categoryItem.name,

            // 类型 0:公司 1:品类
            relatedType: 1
          }
        })
        resultArr = [...resultArr, ...cateResultArr]
      }

      // 如果是空的 那就区公司的试试
      if (utils.isEmpty(this.selectCateGroyArr) && !utils.isEmpty(this.selectCompanyObject)) {
        resultArr = [
          {
            orgId: this.selectCompanyObject.id,
            orgCode: this.selectCompanyObject.orgCode,
            orgName: this.selectCompanyObject.name,

            factoryId: '',
            factoryName: '',
            factoryCode: '',

            categoryCode: '',
            categoryId: '',
            categoryName: '',

            relatedType: 0
          }
        ]
      }

      this.$emit('confirm-function', resultArr)
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleSelectTab(index) {
      console.log(index)
      this.selectIndex = index
    },

    /**
     * 根据公司 去获取对应的工厂  根据工厂 =》 对应的品类
     */
    filterNode(arr, id) {
      if (arr.filter((v) => v.id === id).length > 0) {
        let arrTmp = arr.filter((v) => v.id === id)
        this.selectCompanyObject = arrTmp[0]
      } else {
        arr.forEach((vc) => {
          if (!!vc.children && vc.children.length > 0) {
            this.filterNode(vc.children, id)
          }
        })
      }
    },
    async companyChange(node) {
      let value = node.id
      // let text = node.text
      if (!value || this.selectCompanyObject.id === value) {
        return
      }

      // 清缓存
      factoryCategoryMap.clear()

      this.filterNode(this.companyFields.dataSource, node.id)

      // 已选公司信息
      // this.selectCompanyObject = {
      //   id: value,
      //   name: text
      // }

      // 获取公司对应的工厂 todo 接口缺失
      this.factoryArray = await this.getSite()

      // 根据公司获取品类树 接口还没有 暂时用账户获取品类数 getProdcutTree
      this.getCateGoryTree(this.selectCompanyObject, this.factoryArray[0].id, true)
    },

    /**
     * 根据公司获取工厂 (假数据)
     */
    getSite() {
      let query = {
        tenantId: '17706479458443265'
      }

      return this.$API.supplierInvitationAdd['getSite'](query).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result)) {
          return result.data
        } else {
          return []
        }
      })
    },

    /**
     * 获取品类树 (假接口 传参未传)
     * @selectCompanyObject 公司信息
     * @factoryObject 工厂信息
     * @isInit 是否是第一个 要展开
     */
    getCateGoryTree(selectCompanyObject, factoryObjectId, isInit = false) {
      console.log('品类请求的公司， 工厂：', selectCompanyObject, factoryObjectId)

      // 缓存品类树
      if (factoryCategoryMap.has(factoryObjectId)) {
        let categoryFileds = factoryCategoryMap.get(factoryObjectId)
        this.categoryFileds = categoryFileds
        this.checkedNodes =
          !!this.selectCateGroyArr[factoryObjectId] &&
          this.selectCateGroyArr[factoryObjectId].length > 0
            ? this.selectCateGroyArr[factoryObjectId].map((v) => v.id)
            : []
        return
      }

      this.$API.supplierInvitation
        .getProdcutTree({
          id: selectCompanyObject.id
        })
        .then((res) => {
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            // 加disabled false
            // this.renderAbled(data)
            this.categoryFileds = Object.assign({}, this.categoryFileds, { dataSource: data })
            if (isInit) {
              this.collapseValue = factoryObjectId
            }
            // 缓存品类树
            factoryCategoryMap.set(factoryObjectId, this.categoryFileds)
          } else {
            // 空数据 重置
            this.categoryFileds = {
              dataSource: [],
              id: 'id',
              text: 'name',
              child: 'children',
              iconCss: 'icon',
              imageUrl: 'image'
            }
          }
        })
    },

    /**
     * 品类树点击
     */
    categoryClick(e) {
      console.log(e.target.dataset)
      e.preventDefault()
      e.stopPropagation()
    },
    // 删除all品类
    clearCategroy(factoryObjectId) {
      this.$set(this.selectCateGroyArr, factoryObjectId, JSON.parse(JSON.stringify([])))
    },

    deleteCategory(factoryId, categroyId) {
      let categroyArr = this.selectCateGroyArr[factoryId]
      let index = categroyArr.findIndex((v) => v.id === categroyId)
      categroyArr.splice(index, 1)
      this.$set(this.selectCateGroyArr, factoryId, JSON.parse(JSON.stringify(categroyArr)))

      this.$nextTick(() => {
        this.initSelectTree()
      })
    },

    initSelectTree() {
      const { id: currentCompanyId } = this.currentCompany
      let index = this.checkedNodesObj.findIndex((item) => item.id === currentCompanyId)
      this.checkedNodes = index > -1 ? this.checkedNodesObj[index].nodes : []
    },

    getCompanyTree() {
      // 根据用户id来获取公司树 不能按照需求来完成 后端暂时没有接口 包括品类也是根据账户信息来获取
      // this.$API.supplierInvitation
      //   .findOrganizationCompanyTreeByAccount({
      //     accountId: 0,
      //   })
      this.$API.supplierInvitation
        .findOrganizationCompanyTreeByAccount2({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        })
        .then((res) => {
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            this.companyFields = Object.assign({}, this.companyFields, { dataSource: data })
          } else {
            // 空数据 重置
            this.companyFields = {
              dataSource: [],
              value: 'id',
              test: 'orgCode',
              text: 'name',
              child: 'children'
            }
          }
        })
    }
  },
  beforeDestroy() {
    // this.$refs.cateTree.destory();
    factoryCategoryMap.clear()
  }
}
</script>

<style lang="scss">
.add-area {
  .e-dlg-content {
    padding: 0 !important;
  }
  .addCompany-dialog {
    padding: 40px;
  }

  .accordion-header {
    padding: 10px 0;
    .flex-title {
      font-size: 14px;

      font-weight: 500;
      color: rgba(99, 134, 193, 1);
    }

    .flex-select-list {
      display: flex;
      margin-top: 10px;
      .category-item {
        display: flex;
        align-items: center;
        padding: 0 4px;
        height: 22px;
        line-height: 22px;
        background: rgba(245, 245, 245, 1);
        border-radius: 2px;
        margin-right: 14px;
        font-size: 14px;

        font-weight: normal;
        color: rgba(41, 41, 41, 1);

        .mt-icons {
          display: inline-block;
          width: 10px;
          height: 10px;
          margin-left: 4px;
          color: #9baac1;
          font-size: 10px;
        }
      }

      .clear-item {
        height: 22px;
        line-height: 22px;
        font-size: 14px;

        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        margin-left: 50px;
      }
    }
  }

  .e-acrdn-content {
    padding: 0 20px !important;
  }

  .e-acrdn-content {
    padding: 0 20px !important;
  }
  // .e-list-parent {
  //   padding: 0!important;
  // }
  // .e-treeview .e-ul {
  //   padding: 0!important;
  // }
  #treeview > .e-list-parent {
    padding: 0 !important;
  }
}
</style>
