<template>
  <div class="supplier-container">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :padding-top="true"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>

    <router-view></router-view>
  </div>
</template>

<script>
import { columnDataMain, toolsAll, toolsFosun } from './config'

export default {
  watch: {
    '$route.name'(nv, ov) {
      // 回退刷新逻辑
      if (ov === 'invitationDetail') {
        let reload = ''
        try {
          reload = sessionStorage.getItem('reload')
          sessionStorage.removeItem('reload')
        } catch (error) {
          console.log(error)
        }
        reload === 'reload' && this.$refs.templateRef.refreshCurrentGridData()
      }
    }
  },
  data() {
    return {
      componentConfig: [],
      inviteDatas: []
    }
  },
  mounted() {},
  created() {
    let isFosun = location.host.indexOf('onelinkplus')
    let toolBar = toolsAll
    if (isFosun >= 0) {
      toolBar = toolsFosun
    }

    this.componentConfig = [
      {
        gridId: 'b7c78ec0-6902-4f65-89d1-a144b1c142c5',
        title: this.$t('供应商开发管理'),
        toolbar: {
          tools: [toolBar, ['Filter', 'Refresh', 'Setting']]
        },
        grid: {
          columnData: columnDataMain,
          asyncConfig: {
            url: '/supplier/tenant/buyer/invite/list'
          }
        }
      }
    ]
  },
  methods: {
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      let { data } = e
      let dealData = this.renderData(data)
      delete data.cellTools
      if (e.tool.id == 'edit') {
        //编辑操作
        this.inviteEdit(dealData)
      } else if (e.tool.id == 'delete') {
        //删除操作
        this.deleteInvitation([dealData])
      } else if (e.tool.id == 'invite') {
        this.inviteDialog([dealData])
      } else if (e.tool.id == 'inviteClose') {
        this.inviteClose([dealData])
      }
    },

    renderData(data) {
      return {
        ...data,
        abolished: data.abolished,
        buyerPartnerInviteExtDTO: null,
        contactEmail: data.contactEmail,
        contactMobile: data.contactMobile,
        contactName: data.contactName,
        countryCode: '',
        countryName: '',
        createTime: data.createTime,
        createUserName: data.createUserName,
        id: data.id,
        invitationName: data.invitationName,
        invitationReason: data.invitationReason,
        inviteCode: data.inviteCode,
        inviteNo: data.inviteNo,
        inviteStatus: data.inviteStatus,
        inviteUrl: '',
        remark: data.remark,
        sourceType: data.sourceType,
        supplierEnterpriseCode: data.supplierEnterpriseCode,
        supplierEnterpriseId: data.supplierEnterpriseId,
        supplierEnterpriseName: data.supplierEnterpriseName,
        supplierTenantId: data.supplierTenantId,
        supplierType: data.supplierType,
        tenantId: data.tenantId,
        unifiedCode: data.unifiedCode,
        unifiedCodeType: data.unifiedCodeType
      }
    },

    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field === 'inviteStatus') return
      let { data } = e
      this.$dialog({
        modal: () => import('./components/addSupplierDialog.vue'),
        data: {
          title: this.$t('邀约详情'),
          mode: 'display',
          lineDate: data || []
        },
        success: (data) => {
          console.log(data)
        }
      })
    },

    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      // console.log(e, e.gridRef.getMtechGridRecords());
      const { toolbar, gridRef } = e

      // 新增
      if (toolbar.id === 'addNew') {
        this.newInviteDialog()
        return
      }

      // 快速 新增
      if (toolbar.id === 'firstAdd') {
        this.firstInviteDialog()
        return
      }

      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请先选择一行数据'), type: 'warning' })
        return
      }

      // 快速准入
      if (toolbar.id === 'firstAccess') {
        let dataArr = e.gridRef.getMtechGridRecords()
        if (
          dataArr.filter(
            (v) => v.inviteStatus === 1 || v.inviteStatus === 4 || v.inviteStatus === 5
          ).length !== dataArr.length
        ) {
          this.$toast({ content: this.$t('请选择待邀请状态的数据！'), type: 'warning' })
          return
        }
        let ids = dataArr.map((v) => v.id)
        this.firstAccess(ids)
        return
      }

      // 邀请
      if (toolbar.id === 'inviteList') {
        let dataArr = e.gridRef.getMtechGridRecords()
        // if (
        //   dataArr.filter(
        //     (v) =>
        //       v.inviteStatus === 1 ||
        //       v.inviteStatus === 4 ||
        //       v.inviteStatus === 5
        //   ).length !== dataArr.length
        // ) {
        //   this.$toast({ content: "请先选择可邀请状态的行", type: "warning" });
        //   return;
        // }

        let ids = dataArr.map((v) => v.id).join(',')
        this.inviteDatas = dataArr

        this.$router.push({
          name: 'invitationDetail',
          query: {
            ids
          }
        })
        return
      }

      let recordsList = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id == 'inviteList') {
        this.inviteDialog(recordsList)
      } else if (e.toolbar.id == 'closeList') {
        this.inviteClose(recordsList)
      } else if (e.toolbar.id == 'deleteList') {
        this.deleteInvitation(recordsList)
      }
    },

    // 快速准入
    firstAccess(idList) {
      this.$API.supplierInvitation
        .queryIntroduce({
          idList
        })
        .then((result) => {
          if (result.code === 200) {
            this.$toast({
              content: result.msg || this.$t('快速准入成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({
              content: result.msg || this.$t('快速准入失败'),
              type: 'warning'
            })
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg, type: 'warning' })
        })
    },

    // 邀请按钮
    inviteDialog(data) {
      let ids = data.map((v) => v.id) || []
      ids = ids + ''

      this.inviteDatas = data

      this.$router.push({
        name: 'invitationDetail',
        query: {
          ids
        }
      })
    },

    // 关闭申请
    inviteClose(data) {
      if (data.length > 0 && data.filter((v) => v.inviteStatus != 2).length > 0) {
        this.$toast({
          content: this.$t('单据状态为待确认（已邀请）,才可以撤回'),
          type: 'warning'
        })
        return
      }
      let ids = data.map((v) => v.id) || []
      this.$dialog({
        data: {
          title: this.$t('关闭'),
          message: this.$t('是否确认撤回所选邀请单？'),
          confirm: () => this.$API.supplierInvitation['withdrawInvite']({ idList: ids })
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 删除申请 145 可以删除
    deleteInvitation(data) {
      if (
        data.length > 0 &&
        data.filter((v) => v.inviteStatus === 2 || v.inviteStatus === 3).length > 0
      ) {
        this.$toast({
          content: this.$t('单据状态不正确，无法删除'),
          type: 'warning'
        })
        return
      }
      let ids = data.map((v) => v.id) || []
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选邀请单？'),
          confirm: () => this.$API.supplierInvitation['deleteInvitation']({ idList: ids })
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 编辑 1：邀请，2：准入
    inviteEdit(data) {
      if (data.businessType === 1) {
        this.newInviteDialog(data, 'edit')
      } else if (data.businessType === 2) {
        this.firstInviteDialog(data, 'edit')
      }
    },

    // 刷新页面
    handleRefresh() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 新增供应商弹框
    /**
     * mode: add edit reWrite
     * edit 带入之前的数据
     */
    newInviteDialog(data = [], mode = 'add') {
      let title = mode === 'add' ? this.$t('新增邀请') : this.$t('编辑')
      this.$dialog({
        modal: () => import('./components/addSupplierDialog.vue'),
        data: {
          title: title,
          mode,
          lineDate: data || []
        },
        success: (result) => {
          let { mode, data } = result
          if (mode === 'goInvite') {
            let ids = data.id || []
            ids = ids + ''

            this.inviteDatas = [data]

            this.$router.push({
              name: 'invitationDetail',
              query: {
                ids
              }
            })
            return
          }
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 快速新增
    firstInviteDialog(data = [], mode = 'add') {
      let title = mode === 'add' ? this.$t('新增邀请') : this.$t('编辑')
      this.$dialog({
        modal: () => import('./components/firstAddSupplierDialog'),
        data: {
          title: title,
          mode,
          lineDate: data || []
        },
        success: (result) => {
          let { mode, data } = result
          if (mode === 'goInvite') {
            let ids = data.id || []
            ids = ids + ''

            this.inviteDatas = [data]

            this.$router.push({
              name: 'invitationDetail',
              query: {
                ids
              }
            })
            return
          }
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.supplier-container {
  height: 100%;
  position: relative;

  .status-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
  }

  .normal-status {
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
    border-radius: 2px;
    opacity: 10;
    font-size: 12px;
    font-weight: 500;
    color: #6386c1;
  }
  .operate-btn {
    display: flex;
    line-height: 20px;
    height: 20px;
    margin-top: 4px;
    font-size: 12px;
    color: rgba(99, 134, 193, 1);
    cursor: pointer;
    i {
      display: inline-block;
      line-height: 20px;
      margin-right: 4px;
    }
  }
  .disabled {
    background: rgba(154, 154, 154, 0.1);
    color: rgba(154, 154, 154, 1);
  }
}
</style>

<style lang="scss" scoped>
.supplier-container {
  height: 100%;
  position: relative;

  .operate-box {
    color: red;
  }
}
.supplier-container /deep/ .operate-box {
  justify-content: flex-start;
}

.supplier-container /deep/ .operate-box .operator-btn {
  margin: 0 10px;
}
.supplier-container /deep/ .operate-box .active {
  color: #6386c1;
  cursor: pointer;
}
.supplier-container /deep/ .operate-box .grey {
  color: #b1b1b1;
  display: none;
}

.supplier-container /deep/.mt-data-grid {
  display: flex;
  flex-direction: column;
}
</style>
