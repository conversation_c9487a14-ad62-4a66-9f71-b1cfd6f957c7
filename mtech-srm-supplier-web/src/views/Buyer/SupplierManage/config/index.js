import utils from '../../../../utils/utils'
import { i18n } from '@/main.js'

export const toolsAll = [
  {
    id: 'addNew',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('新增邀请')
  },
  {
    id: 'inviteList',
    icon: 'icon_solid_invitation',
    title: i18n.t('发送邀请')
  },
  {
    id: 'closeList',
    icon: 'a-icon_solid_revocation',
    title: i18n.t('撤回邀请')
  },
  {
    id: 'firstAdd',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('新增准入')
  },
  {
    id: 'firstAccess',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('快速准入')
  },
  {
    id: 'deleteList',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]
export const toolsFosun = [
  {
    id: 'firstAdd',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('新增')
  },
  {
    id: 'firstAccess',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('快速准入')
  },
  {
    id: 'deleteList',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]

export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'inviteNo',
    headerText: i18n.t('邀请单编码'),
    headerTextAlign: 'center',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.inviteStatus === 1 || data.inviteStatus === 4 || data.inviteStatus === 5
        }
      },
      {
        id: 'delete',
        icon: 'table-delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data.inviteStatus === 1 || data.inviteStatus === 4 || data.inviteStatus === 5
        }
      }
    ]
  },
  {
    width: '210',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商企业名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'contactName',
    headerText: i18n.t('联系人'),
    headerTextAlign: 'center'
  },
  {
    field: 'contactMobile',
    headerText: i18n.t('联系方式'),
    headerTextAlign: 'center'
  },
  {
    field: 'contactEmail',
    headerText: i18n.t('联系邮箱'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'inviteStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    type: 'string',
    cssClass: 'normal-status ',
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('待邀请'),
        2: i18n.t('待确认'),
        3: i18n.t('已确认'),
        4: i18n.t('已拒绝'),
        5: i18n.t('已撤回')
      }
    }
    // cellTools: [
    //   {
    //     id: "invite",
    //     icon: "icon_solid_Newinvitation",
    //     title: i18n.t("邀请"),
    //     visibleCondition: (data) => {
    //       return data.inviteStatus === 1 || data.inviteStatus === 4 || data.inviteStatus === 5;
    //     },
    //   },
    //   {
    //     id: "inviteClose",
    //     icon: "a-icon_solid_revocation",
    //     title: i18n.t("撤回"),
    //     visibleCondition: (data) => {
    //        // 1、待邀请 2、待确认 （可以撤回，）3、已确认 4、已拒绝（可以重新邀请，编辑 ，删除） 5、已撤回（等于关闭，可以再邀请 编辑 删除 ）
    //       return data.inviteStatus === 2;
    //     },
    //   },
    // ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    headerTextAlign: 'center'
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    headerTextAlign: 'center',
    queryType: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '190',
    field: 'remark',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]
export const columnDataMain3 = () => {
  return [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      field: 'inviteNo',
      headerText: i18n.t('邀请单编码')
    },
    {
      field: 'supplierEnterpriseName',
      headerText: i18n.t('供应商企业名称')
    },
    {
      field: 'contactName',
      headerText: i18n.t('联系人')
    },
    {
      field: 'contactMobile',
      headerText: i18n.t('联系方式')
    },
    {
      field: 'contactEmail',
      headerText: i18n.t('联系邮箱')
    },
    {
      width: '110',
      field: 'inviteStatus',
      headerText: i18n.t('状态'),
      type: 'string',
      cssClass: 'normal-status ',
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('待邀请'),
          2: i18n.t('待确认'),
          3: i18n.t('已确认'),
          4: i18n.t('已拒绝'),
          5: i18n.t('已撤回')
        }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建日期'),
      queryType: 'date',
      format: 'yyyy-MM-dd hh:mm:ss'
    },
    {
      width: '190',
      field: 'remarks',
      headerText: i18n.t('备注')
    }
    // {
    //   width: "240",
    //   field: "remark",
    //   headerText: i18n.t("操作"),
    //   template: () => {
    //     return {
    //       template: Vue.component("operate-template", {
    //         /**
    //          *  inviteStatus
    //          *  0  草稿 （待邀请）   ===  邀请 编辑 删除
    //          *  1  已邀请  ===  关闭
    //          *  2  已确认 重新建邀请 带入传参
    //          *  3  已拒绝 重新建邀请 带入传参
    //          *  4  已关闭 重新建邀请 带入传参
    //          */
    //         template: `
    //                 <div class="operate-box mt-flex">
    //                   <div class="operator-btn" :class="{'active': statusTmp === 0, 'grey': statusTmp != 0}" @click.stop="invite">邀请</div>
    //                   <div class="operator-btn" :class="{'active': statusTmp === 1, 'grey': statusTmp != 1}" @click.stop="inviteClose">关闭</div>
    //                   <div class="operator-btn" :class="{'active': statusTmp === 0, 'grey': statusTmp != 0}" @click.stop="inviteEdit">编辑</div>
    //                   <div class="operator-btn" :class="{'active': statusTmp === 0, 'grey': statusTmp != 0}" @click.stop="inviteDelete">删除</div>
    //                   <div class="operator-btn" :class="{'active': (statusTmp === 2 || statusTmp === 3 || statusTmp === 4), 'grey': (statusTmp === 0 || statusTmp === 1 ) }" @click.stop="inviteReWrite">重填</div>
    //                 </div>`,
    //         data: function () {
    //           return { data: {} };
    //         },
    //         computed: {
    //           statusTmp() {
    //             return this.data.inviteStatus
    //           }
    //         },
    //         methods: {
    //           // 邀请 接口端来判断邀请保持情景
    //           invite() {
    //               let { data } = this
    //               if (data.inviteStatus != 0 ) return
    //               inviteDialog([data])
    //           },
    //           // 关闭
    //           inviteClose() {
    //               let { data } = this
    //               if (data.inviteStatus != 1 ) return
    //               inviteClose([data])
    //           },
    //           // 编辑
    //           inviteEdit() {
    //               let { data } = this
    //               if (data.inviteStatus != 0 ) return
    //               inviteEdit(data)
    //           },
    //           // 删除
    //           inviteDelete() {
    //             let { data } = this
    //             if (data.inviteStatus != 0) return
    //             deleteInvitation([data])
    //           },

    //           // 重填
    //           inviteReWrite() {
    //               let { data } = this
    //               if (data.inviteStatus === 0 || data.inviteStatus === 1 ) return
    //               inviteReWrite(data)
    //           }
    //         }
    //       }),
    //     };
    //   },
    // },
  ]
}
export const columnDataMain2 = () => {
  return [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '210',
      field: 'supplierEnterpriseName',
      headerText: i18n.t('供应商企业名称'),
      cellTools: [
        {
          id: 'delete',
          icon: 'table-delete',
          title: i18n.t('删除')
        }
      ]
    },
    {
      width: '130',
      field: 'contactName',
      headerText: i18n.t('联系人')
    },
    {
      width: '210',
      field: 'contactMobile',
      headerText: i18n.t('联系方式')
    },
    {
      width: '210',
      field: 'contactEmail',
      headerText: i18n.t('联系邮箱')
    },
    {
      width: '160',
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('创建日期'),
      queryType: 'date',
      format: 'yyyy-MM-dd hh:mm:ss'
    },
    {
      width: '190',
      field: 'remarks',
      headerText: i18n.t('备注')
    }
  ]
}

export const invitationToolbar = [
  [
    {
      id: 'addNew',
      icon: 'icon_solid_Newinvitation',
      title: i18n.t('新增')
    },
    {
      id: 'deleteList',
      icon: 'icon_solid_Delete1',
      title: i18n.t('删除')
    },
    {
      id: 'inviteList',
      icon: 'icon_solid_invitation',
      title: i18n.t('邀请')
    },
    {
      id: 'closeList',
      icon: 'a-icon_solid_revocation',
      title: i18n.t('撤回')
    }
  ],
  ['Filter', 'Refresh', 'Setting']
]
export const invitationToolbar2 = [
  [
    {
      id: 'addNew',
      icon: 'icon_solid_Newinvitation',
      title: i18n.t('新增')
    },
    {
      id: 'deleteList',
      icon: 'icon_solid_Delete1',
      title: i18n.t('删除')
    }
  ],
  ['Filter', 'Refresh', 'Setting']
]

export const supplyAreaToolbar = [
  {
    id: 'addNew',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('新增')
  },
  {
    id: 'deleteList',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]

const validateMobile = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入联系方式'))
  } else if (!utils.isMobile(value)) {
    callback(new Error('请输入正确的联系方式'))
  } else {
    callback()
  }
}

const validateMEmail = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入联系人邮箱'))
  } else if (!utils.isEmail(value)) {
    callback(new Error('请输入正确的联系人邮箱'))
  } else {
    callback()
  }
}

export const inviteRule = {
  supplierEnterpriseId: [
    {
      required: true,
      message: i18n.t('请输入供应商企业全称'),
      trigger: 'submit'
    }
  ],
  contactName: [
    {
      required: true,
      message: i18n.t('请输入联系人'),
      trigger: 'submit'
    },
    { min: 2, max: 5, message: '长度在 2 到 5 个字符', trigger: 'submit' }
  ],
  contactMobile: [{ required: true, validator: validateMobile, trigger: 'submit' }],
  contactEmail: [{ required: true, validator: validateMEmail, trigger: 'submit' }],
  supplierType: [
    {
      required: true,
      message: i18n.t('请输入调查表标识'),
      trigger: 'submit'
    }
  ],
  remark: [{ min: 0, max: 200, message: '长度在 200 个字符以内', trigger: 'blur' }]
}

export const areaColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '100',
    field: 'orgCode',
    cssClass: 'field-content',
    headerText: i18n.t('公司编码'),
    cellTools: [
      //用户单元格按钮，详见celltools备注。
      'Delete'
    ]
  },
  {
    width: '100',
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '100',
    field: 'factoryCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '100',
    field: 'factoryName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '100',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '100',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

export const historyColumnData = [
  // {
  //   width: "50",
  //   type: "checkbox",
  //   showInColumnChooser: false,
  // },
  {
    width: '100',
    field: 'orgCode',
    cssClass: 'field-content',
    headerText: i18n.t('公司编码')
  },
  {
    width: '100',
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '100',
    field: 'factoryCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '100',
    field: 'factoryName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '100',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '100',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

export const historyMockData = [
  {
    orgCode: '10000P',
    orgName: '测试公司1',
    categoryCode: '1000C',
    categoryName: '',
    factoryCode: '1000F',
    factoryName: ''
  }
]
