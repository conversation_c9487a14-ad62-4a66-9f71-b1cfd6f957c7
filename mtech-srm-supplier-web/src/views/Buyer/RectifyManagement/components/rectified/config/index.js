//维度设置Tab
import Vue from 'vue'
import { i18n } from '@/main'
import utils from '@/utils/utils'
import { download } from '@/utils/utils'

/**
 * 时间戳转日期显示
 * @param data: { formatString: <"YYYY-MM-dd"/"HH:MM:SS">, value: "时间戳" }
 * @returns String
 */
export const timeNumberToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

const rectificationTypeMap = JSON.parse(sessionStorage.getItem('rectificationTypeMap'))
const documentSourceMap = {
  0: i18n.t('手工创建'),
  10: i18n.t('现场评审单'),
  20: i18n.t('QMS报检单'),
  30: i18n.t('限期整改'),
  40: i18n.t('质量绩效')
}

// const toolbar = [
//   { id: "upGrade", icon: "icon_solid_Newinvitation", title: i18n.t("驳回") },
//   { id: "upGrade", icon: "icon_solid_edit", title: i18n.t("通过") },
// ];
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'rectificationCode',
    headerText: i18n.t('整改单号'),
    cellTools: []
  },
  {
    field: 'rectificationType',
    headerText: i18n.t('整改类型'),
    valueConverter: {
      type: 'map',
      map: rectificationTypeMap
    }
  },
  {
    field: 'purchaserCode',
    width: 200,
    headerText: i18n.t('公司编码')
  },
  {
    field: 'purchaserName',
    width: 200,
    headerText: i18n.t('公司名称')
  },
  {
    width: '200',
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM')
  },
  {
    width: '200',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
  },
  {
    field: 'supplierName',
    width: 200,
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'documentSource',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: documentSourceMap
    }
  },
  {
    field: 'sourceApplyCode',
    headerText: i18n.t('来源单据号')
  },
  {
    field: 'feedbackAttachment',
    width: '180',
    ignore: true,
    headerText: i18n.t('供应商整改反馈'),
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="download">{{templateName}}</a>`,
          data() {
            return { data: {}, templateName: '' }
          },
          mounted() {
            this.templateName = this.data.feedbackAttachment[0].fileName
          },
          methods: {
            download() {
              this.$store.commit('startLoading')
              this.$API.fileService
                .downloadPublicFile({
                  id: this.data.feedbackAttachment[0].fileId
                })
                .then((res) => {
                  this.$store.commit('endLoading')
                  download({
                    fileName: this.data.feedbackAttachment[0].fileName,
                    blob: res.data
                  })
                })
            }
          }
        })
      }
    }
  },
  {
    field: 'template',
    headerText: i18n.t('整改模板'),
    ignore: true,
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="download">{{templateName}}</a>`,
          data() {
            return { data: {}, templateName: '' }
          },
          mounted() {
            this.templateName =
              this.data.templateType == '1' ? i18n.t('8D电子版') : this.data.template[0].fileName
          },
          methods: {
            download() {
              if (this.data.templateType == '1') {
                const a = document.createElement('a')
                a.href = 'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/supplier/8D电子版.xlsx'
                a.style.display = 'none'
                a.download = i18n.t('8D电子版')
                document.body.appendChild(a)
                a.click()
                document.body.removeChild(a)
              } else {
                this.$store.commit('startLoading')
                this.$API.fileService
                  .downloadPublicFile({
                    id: this.data.template[0].fileId
                  })
                  .then((res) => {
                    this.$store.commit('endLoading')
                    download({
                      fileName: this.data.template[0].fileName,
                      blob: res.data
                    })
                  })
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'source',
    headerText: i18n.t('不合格项'),
    ignore: true,
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="showDetail">${i18n.t(
            '查看详情'
          )}</a>`,
          data() {
            return { data: {} }
          },
          methods: {
            showDetail() {
              this.$dialog({
                modal: () => import('../../unqualifiedList.vue'),
                data: {
                  title: i18n.t('查看详情'),
                  id: this.data.id
                }
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'deadline',
    headerText: i18n.t('时限要求'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    field: 'status',
    headerText: i18n.t('整改状态'),
    valueConverter: {
      type: 'map',
      map: { 40: i18n.t('完成') }
    }
  },
  {
    field: 'rectificationCount',
    headerText: i18n.t('整改次数'),
    type: 'number'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  }
]

export const pageConfig = [
  {
    gridId: '4897b099-15d7-43ae-98c6-cf1b8ae3ca5e',
    toolbar: [
      {
        id: 'export',
        title: i18n.t('导出')
      }
    ],
    useToolTemplate: false,
    grid: {
      columnData,
      asyncConfig: {
        url: '/supplier/tenant/buyer/partner/reform/complete/list',
        serializeList: (list) => {
          list.forEach((e) => {
            let nameList = []
            let codeList = []
            e.purchaserCompanyInfo.forEach((item) => {
              nameList.push(item.purchaserName)
              codeList.push(item.purchaserCode)
            })
            e.purchaserName = nameList.join()
            e.purchaserCode = codeList.join()
          })
          return list
        }
      },
      frozenColumns: 3
    }
  }
]
