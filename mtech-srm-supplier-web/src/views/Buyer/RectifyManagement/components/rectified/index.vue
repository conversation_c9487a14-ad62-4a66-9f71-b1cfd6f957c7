<template>
  <div class="rectified-content">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { exportData } from '@/utils/utils.js'

export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'rectificationCode') {
        this.$router.push({
          name: `pur-rectification-detail`,
          query: {
            type: 'edit',
            id: e.data['id'],
            refreshKey: new Date().getTime()
          }
        })
      }
    },
    handleClickToolBar(e) {
      const { toolbar } = e
      if (toolbar.id === 'export') {
        // 导出
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      const rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        exportStatus: 3,
        condition: rule.condition || '',
        rules: rule.rules || []
      }
      const requestUrl = {
        preName: 'rectifyManagement',
        urlName: 'export'
      }
      exportData(requestUrl, params)
    }
  }
}
</script>

<style lang="scss" scoped>
.rectified-content {
  height: 100%;
}
</style>
