<template>
  <div class="slider-panel-container" ref="slider-dialog">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="slider-content">
        <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
          <mt-form-item
            class="form-item"
            :label="$t('整改类型')"
            label-style="top"
            prop="rectificationType"
          >
            <mt-select
              v-model="formInfo.rectificationType"
              :data-source="rectificationTypeList"
              :fields="{ text: 'name', value: 'itemCode' }"
              :placeholder="$t('请选择')"
              float-label-type="Never"
              width="390"
              @change="typeChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item class="form-item" prop="buyerOrgId" :label="$t('公司')">
            <!-- <mt-select
              v-model="formInfo.buyerOrgId"
              float-label-type="Never"
              :data-source="orgList"
              :fields="{ text: 'orgName', value: 'id' }"
              :placeholder="$t('请选择公司')"
              @change="orgChange"
              width="390"
            ></mt-select> -->
            <RemoteAutocomplete
              :params="{
                organizationLevelCodes: ['ORG02', 'ORG01'],
                orgType: 'ORG001PRO',
                includeItself: true
              }"
              v-model="formInfo.buyerOrgId"
              :width="390"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              url="/masterDataManagement/tenant/organization/specified-level-paged-query"
              :title-switch="false"
              :placeholder="$t('请选择')"
              select-type="administrativeCompany"
              @change="orgChange"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('供应商')"
            label-style="top"
            prop="supplierInternalCode"
          >
            <mt-select
              v-model="formInfo.supplierInternalCode"
              :data-source="supplierList"
              :fields="{ text: 'supplierName', value: 'supplierInternalCode' }"
              :placeholder="$t('请选择')"
              float-label-type="Never"
              width="390"
              @change="supplierChange"
              :allow-filtering="true"
              :filtering="onFiltering"
              css-class="d72562-c40d-4933-9a24-98c3298365ac"
              :item-template="iTemplate"
            ></mt-select>
          </mt-form-item>
          <mt-form-item class="form-item" :label="$t('期限')" label-style="top" prop="deadline">
            <mt-date-picker
              v-model="formInfo.deadline"
              :min="new Date()"
              format="yyyy-MM-dd"
              :placeholder="$t('请选择')"
              width="390"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item
            class="form-item radio-item"
            :label="$t('整改模板')"
            label-style="top"
            prop="templateType"
          >
            <mt-select
              v-model="formInfo.templateType"
              :data-source="templateTypeData"
              :placeholder="$t('请选择')"
              float-label-type="Never"
              width="390"
              @change="planTempChange"
            ></mt-select>
          </mt-form-item>

          <mt-form-item
            class="form-item radio-item"
            :label="$t('整改模板')"
            label-style="top"
            prop="template"
            v-if="formInfo.templateType == 2"
          >
            <mt-common-uploader
              :is-single-file="true"
              :accept="acceptType"
              :save-url="saveUrl"
              :download-url="downloadUrl"
              type="line"
              v-model="formInfo.template"
            ></mt-common-uploader>
          </mt-form-item>

          <mt-form-item class="form-item full-width" :label="$t('不合格项')"> </mt-form-item>
        </mt-form>

        <div class="slider-content-list">
          <mt-template-page
            ref="templateRef"
            :template-config="pageConfig"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTool="handleClickCellTool"
          >
          </mt-template-page>
        </div>
      </div>
      <div class="slider-footer mt-flex">
        <span @click="cancel">{{ $t('取消') }}</span>
        <span @click="confirm('save')">{{ $t('保存') }}</span>
        <span @click="confirm('saveAndRelease')">{{ $t('保存并发布') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { dialogColumns } from '../config/index'
import { cloneDeep } from 'lodash'
import utils from '@/utils/utils'
import commonData from '@/utils/constant'
import itemVue from './itemVue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete,
    itemVue
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  data() {
    return {
      // orgList: [], // 公司列表
      iTemplate: function () {
        return {
          template: itemVue
        }
      },
      saveUrl: commonData.publicFileUrl,
      downloadUrl: commonData.downloadUrl,
      acceptType: ['.xls', '.xlsx'],

      supplierList: [],
      rectificationTypeList: [],
      templateTypeData: [
        {
          text: this.$t('8D电子版'),
          value: '1'
        },
        {
          text: this.$t('自定义附件'),
          value: '2'
        }
      ],
      formInfo: {
        templateType: '1',
        template: [],
        supplierInternalCode: '',
        supplierName: ''
      },
      rules: {
        rectificationType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        supplierInternalCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        deadline: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        templateType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        template: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ]
      },
      pageConfig: [
        {
          gridId: '3dc78e1f-3a17-41a3-aeaf-9f8ee2ae14ed',
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [['Add', 'Edit', 'Delete']]
          },
          grid: {
            height: '280',
            allowPaging: false,
            columnData: dialogColumns,
            lineIndex: 1,
            dataSource: []
          }
        }
      ]
    }
  },
  created() {
    this.initData()
  },
  mounted() {},
  methods: {
    // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
    handleCompanyData(resData = []) {
      resData = resData.filter((item) => {
        return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      })
      return resData
    },
    onFiltering(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.supplierList.filter((item) => {
            if (
              item?.supplierName.indexOf(e.text) > -1 ||
              item?.supplierInternalCode.indexOf(e.text) > -1 ||
              item?.supplierCode.indexOf(e.text) > -1
            ) {
              return true
            } else {
              return false
            }
          })
        )
      } else {
        e.updateData(this.supplierList)
      }
    },
    // getChildrenCompanyOrganization() {
    //   this.$API.supplierInvitation["getChildrenCompanyOrganization2"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((result) => {
    //     if (result.code === 200 && !utils.isEmpty(result.data)) {
    //       this.orgList = result.data.filter((item) => {
    //         return (
    //           item.orgLevelTypeCode === "ORG02" ||
    //           item.orgLevelTypeCode === "ORG01"
    //         );
    //       });
    //     } else {
    //       this.orgList = [];
    //     }
    //   });
    // },
    orgChange(event) {
      // 有级联
      const { itemData = {} } = event
      this.formInfo.buyerOrgId = itemData?.id
      this.formInfo.buyerCode = itemData?.orgCode
      this.formInfo.buyerName = itemData?.orgName
      this.formInfo.supplierCode = ''
      this.formInfo.supplierName = ''
      if (itemData?.id) {
        this.getOrgPartnerRelations(itemData.id)
      } else {
        // 清空供应商绑定值 和 dataSource
        this.formInfo.supplierInternalCode = ''
        this.supplierList = []
      }
    },
    // STATUS_DRAFT(1, "注册"),
    // STATUS_POTENTIAL(2, "潜在"),
    // STATUS_NORMAL(10, "合格"),
    // STATUS_FROZEN(20, "冻结"),
    // STATUS_BLACK(30, "黑名单"),
    // Integer 类型 这些状态可以惩罚
    getOrgPartnerRelations(orgId) {
      this.$API.supplierEffective
        .getOrgPartnerRelationsByStatus({
          orgId,
          status: [1, 2, 10, 20, 30]
        })
        .then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.supplierList = res.data
          } else {
            this.supplierList = []
          }
        })
    },
    initData() {
      if (this.isEdit) {
        this.$API.rectifyManagement.queryDetail(this.info.id).then((res) => {
          this.formInfo = {
            ...res.data,
            deadline: utils.formateTime(new Date(Number(res.data.deadline)))
          }
          if (this.formInfo.templateType == 2) {
            this.formInfo.template[0].id = this.formInfo.template[0].fileId
          }

          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.buyerRectificationExtDTOList)
        })
      }
      this.getTypelist()
      // this.getChildrenCompanyOrganization();
      // this.getSupplierList();
    },
    getTypelist() {
      this.$API.masterData
        .queryDict({
          dictCode: 'rectificationType'
        })
        .then((res) => {
          this.rectificationTypeList = res.data
        })
    },
    // getSupplierList() {
    //   this.$API.masterData.getUserInfo().then((res) => {
    //     if (res.code == 200) {
    //       let _org = res.data.orgList.find(
    //         (e) => e.orgLevelTypeCode === "ORG02"
    //       );
    //       this.$API.rectifyManagement
    //         .querySupplierList({
    //           orgId: _org.id,
    //           status: ["10"],
    //         })
    //         .then((res) => {
    //           this.supplierList = res.data;
    //         });
    //     }
    //   });
    // },
    planTempChange(e) {
      if (e.value == '1') {
        this.formInfo.template.length = 0
      }
    },
    typeChange() {},
    supplierChange(e) {
      this.formInfo.supplierEnterpriseId = e.itemData.supplierEnterpriseId
      this.formInfo.supplierName = e.itemData.supplierName
      this.formInfo.supplierCode = e.itemData.supplierCode
    },
    handleClickToolBar(e) {
      let _this = this
      const { toolbar, grid, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      let selectedRowIndexes = grid.getSelectedRowIndexes()
      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Add') {
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            title: this.$t('新增不合格项')
          },
          success: (data) => {
            // _this.formInfo.buyerRectificationExtDTOList.push(data);
            // _this.pageConfig[0].grid.dataSource.push(data);
            // console.log(
            //   "_this.pageConfig[0].grid.dataSource",
            //   _this.pageConfig[0].grid.dataSource
            // );

            let _tempData = cloneDeep(_this.pageConfig[0].grid.dataSource)
            _this.pageConfig[0].grid.dataSource.length = 0
            _tempData.push(data)
            this.$set(_this.pageConfig[0].grid, 'dataSource', _tempData)
          }
        })
      } else if (toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            title: this.$t('编辑不合格项'),
            isEdit: true,
            info: sltList[0],
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            let _tempData = cloneDeep(_this.pageConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.pageConfig[0].grid, 'dataSource', _tempData)
          }
        })
      } else if (toolbar.id == 'Delete') {
        let _tempData = cloneDeep(_this.pageConfig[0].grid.dataSource)
        let _fileIds = sltList.map((x) => x.attachment[0].id)
        let _newData = _tempData.filter((element) => !_fileIds.includes(element.attachment[0].id))
        this.$set(this.pageConfig[0].grid, 'dataSource', _newData)
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data, componentData } = e
      const { id } = tool || {}
      if (id == 'edit') {
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            title: this.$t('编辑不合格项'),
            isEdit: true,
            info: data,
            index: componentData.index
          },
          success: (data) => {
            let _tempData = cloneDeep(this.pageConfig[0].grid.dataSource)
            _tempData[componentData.index] = data
            this.$set(this.pageConfig[0].grid, 'dataSource', _tempData)
          }
        })
      } else if (id == 'delete') {
        this.pageConfig[0].grid.dataSource.splice(componentData.index, 1)
      }
    },
    confirm(type) {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (this.pageConfig[0].grid.dataSource.length < 1) {
            this.$toast({
              content: this.$t('不符合项不能为空'),
              type: 'warning'
            })
            return
          }
          if (this.formInfo.templateType == 2) {
            this.formInfo.template[0].fileUrl = this.formInfo.template[0].url
          }
          let param = {
            ...this.formInfo,
            deadline: Math.round(new Date(this.formInfo.deadline)),
            buyerRectificationExtDTOList: this.pageConfig[0].grid.dataSource
          }
          this.$API.rectifyManagement[type](param)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
              }
              this.$emit('confirm-function')
            })
            .catch((err) => {
              this.$toast({
                content: err.msg,
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../../../../../../../node_modules/@mtech/mtech-common-uploader/build/esm/bundle.css';
/deep/ .mt-common-uploader {
  .main {
    margin: 0;
    min-height: 60px;
  }
}
.slider-panel-container {
  .slider-modal {
    width: 850px;
    border: none;
    .slider-header {
      height: 58px;
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }

    .slider-content {
      display: flex;
      flex-direction: column;
      &-list {
        flex: 1;
      }
    }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
  z-index: 999;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.radio-item {
  width: 390px;
  .mt-radio {
    line-height: 32px;
  }
}
/deep/.full-width {
  width: 100%;
}
/deep/.mt-form-item {
  margin-bottom: 18px;
}
</style>
<style lang="scss">
.d72562-c40d-4933-9a24-98c3298365ac {
  li {
    display: flex;
    align-items: center;
    & > div {
      flex-shrink: 0;
    }
    & > div:first-child {
      margin-left: 20px;
    }
  }
}
</style>
