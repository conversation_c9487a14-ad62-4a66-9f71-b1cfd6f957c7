<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <!-- <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="file"> </mt-form-item>
          </mt-col>
        </mt-row> -->
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <mt-select
            v-model="formInfo.siteCode"
            :data-source="factoryData"
            :show-clear-button="true"
            :allow-filtering="true"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :placeholder="$t('请选择工厂')"
            @change="changeSite"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-select
            v-model="formInfo.itemCode"
            :data-source="itemData"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="remoteItemList"
            :disabled="isQMSdata"
            :fields="{ text: 'itemCode', value: 'itemCode' }"
            :placeholder="$t('请选择物料')"
            @change="changeItem"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" label-style="top" prop="itemName">
          <mt-input v-model="formInfo.itemName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('评审项目说明')" label-style="top" prop="reviewProjectDescription">
          <mt-input
            v-model="formInfo.reviewProjectDescription"
            :multiline="false"
            maxlength="200"
            :placeholder="$t('请输入')"
            float-label-type="Never"
            width="820"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('缺陷描述')" label-style="top" prop="unqualifiedDesc">
          <mt-input
            v-model="formInfo.unqualifiedDesc"
            :multiline="false"
            maxlength="200"
            :placeholder="$t('请输入')"
            float-label-type="Never"
            width="820"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('附件')" label-style="top" prop="attachment">
          <mt-common-uploader
            :is-single-file="true"
            :accept="acceptType"
            :save-url="saveUrl"
            :download-url="downloadUrl"
            type="line"
            v-model="formInfo.attachment"
          ></mt-common-uploader>
        </mt-form-item>
      </mt-form>
      <!-- <div class="cell-upload">
        <div class="to-upload">
          <input type="file" ref="file" class="upload-input" @change="chooseFiles" />
          <div class="upload-box" v-show="!uploadInfo.fileName">
            <div class="plus-icon"></div>
            <div class="right-state">
              <div class="plus-txt">
                {{ $t('请拖拽文件或点击上传') }}
              </div>
              <div class="warn-text">
                {{
                  $t(
                    '注：文件大小不超过50M，文件格式支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'
                  )
                }}
              </div>
            </div>
          </div>
        </div>

        <div class="has-file" v-if="!!uploadInfo.fileName">
          <div class="left-info">
            <div class="file-title">
              <span class="text-ellipsis">{{ uploadInfo.fileName }}</span>
              <span>{{ uploadInfo.fileSize }} kb</span>
            </div>
          </div>
          <mt-icon name="icon_Close_2" class="close-icon" @click.native="handleRemove"></mt-icon>
        </div>
      </div> -->
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import commonData from '@/utils/constant'

let fileData = null
export default {
  data() {
    return {
      allowFileType: [
        'xls',
        'xlsx',
        'doc',
        'docx',
        'pdf',
        'ppt',
        'pptx',
        'png',
        'jpg',
        'zip',
        'rar'
      ],
      saveUrl: commonData.publicFileUrl,
      downloadUrl: commonData.downloadUrl,
      acceptType: ['.xls', '.xlsx'],
      formInfo: {
        siteCode: null,
        itemCode: null,
        itemName: null,
        reviewProjectDescription: null,
        unqualifiedDesc: '',
        attachment: []
      },
      rules: {
        siteCode: [{ required: true, message: this.$t('工厂不能为空'), trigger: 'blur' }],
        unqualifiedDesc: [{ required: true, message: this.$t('缺陷描述不能为空'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      uploadInfo: {}, // 上传后信息
      itemData: [] // 物料数据
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    index() {
      return this.modalData.index
    },
    factoryData() {
      return this.modalData.factoryData || []
    },
    isQMSdata() {
      return this.modalData.documentSource == 20
    },
    addFormModel() {
      return this.modalData.addFormModel || {}
    }
  },
  created() {
    this.remoteItemList = utils.debounce(this.getItemData, 300)
    this.isRealChange = false
    setTimeout(() => {
      this.isRealChange = true
    }, 500)
  },
  mounted() {
    console.log('this.modalData', this.modalData)
    this.initData()
    this.show()
  },
  methods: {
    initData() {
      if (this.isEdit) {
        this.formInfo = { ...this.info }
        // this.uploadInfo = { ...this.info.attachment[0] }
        this.itemData = [{ itemCode: this.info.itemCode, itemName: this.info.itemName }]
      } else {
        // 新增
        this.formInfo.buyerCode = this.addFormModel.buyerCode
        this.formInfo.supplierCode = this.addFormModel.supplierCode
        this.formInfo.partnerArchiveId = this.addFormModel.partnerArchiveId
      }
    },
    // 获取物料列表数据
    getItemData(e) {
      if (
        !this.formInfo.organizationId ||
        !this.formInfo.buyerCode ||
        !this.formInfo.supplierCode
      ) {
        this.$toast({ content: this.$t('请确定公司,供应商和工厂都已经选择了!'), type: 'warning' })
        return
      }
      const params = {
        organizationId: this.formInfo.organizationId, // 工厂id
        partnerArchiveId: this.formInfo.partnerArchiveId, // 供应商id
        itemName: e.text
      }
      this.$API.supplierEffective.getItemData(params).then((res) => {
        if (res.code === 200) {
          this.itemData = res.data
        }
      })
    },
    changeItem(e) {
      this.formInfo.itemName = e.itemData?.itemName
    },
    changeSite(e) {
      this.formInfo.siteName = e.itemData?.siteName
      this.formInfo.organizationId = e.itemData?.organizationId
      if (this.isRealChange) {
        this.formInfo.itemCode = null
        this.formInfo.itemName = null
      }
    },
    chooseFiles(data) {
      this.$loading()
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      }
      // console.log("files", files);
      let _tempInfo = files[0].name.split('.')
      if (_tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])) {
        this.$toast({
          content: this.$t('文件格式仅支持' + this.allowFileType.join('/')),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('UploadFiles', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      fileData = _data
      this.uploadFile()
    },

    // 上传图片
    uploadFile() {
      this.$refs.file.value = ''
      this.$API.SupplierPunishment.fileUpload(fileData)
        .then((res) => {
          const { code, data } = res
          this.$hloading()
          if (code == 200 && !utils.isEmpty(data)) {
            this.uploadInfo = {
              ...data,
              fileId: data.id
            }
            // this.$toast({ content: this.$t("操作成功"), type: "success" });
          } else {
            this.uploadInfo = {}
            this.$toast({
              content: data.msg || this.$t('上传失败'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.uploadInfo = {}
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('上传失败'),
            type: 'warning'
          })
        })
    },

    // 移除文件
    handleRemove() {
      this.uploadInfo = {}
      fileData = null
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          // if (utils.isEmpty(this.uploadInfo)) {
          //   this.$toast({
          //     content: this.$t('请选择文件上传！'),
          //     type: 'warning'
          //   })
          //   return
          // }
          // console.log('this.uploadInfo', this.uploadInfo, this.formInfo)
          this.$emit('confirm-function', {
            ...this.formInfo,
            // attachment: [{ ...this.uploadInfo, fileUrl: this.uploadInfo.url }],
            // uploadTime: this.uploadInfo.createTime
            //   ? Number(new Date(this.uploadInfo.createTime)).toString()
            //   : this.formInfo.uploadTime
            uploadTime: Number(new Date()).toString()
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.uploader-box {
  .mt-form-item__content {
    flex: 1;
  }
}
</style>
<style lang="scss" scoped>
.uploader-box {
  padding: 40px 20px 0;
}

.cell-upload {
  margin-top: 15px;
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          width: 100%;
          display: inline-block;
          vertical-align: middle;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
