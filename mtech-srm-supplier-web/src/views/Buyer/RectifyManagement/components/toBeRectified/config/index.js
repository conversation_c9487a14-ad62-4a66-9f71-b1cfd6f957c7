//维度设置Tab
import Vue from 'vue'
import { i18n } from '@/main'
import utils from '@/utils/utils'
import { download } from '@/utils/utils'

/**
 * 时间戳转日期显示
 * @param data: { formatString: <"YYYY-MM-dd"/"HH:MM:SS">, value: "时间戳" }
 * @returns String
 */
export const timeNumberToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

export const statusMap = {
  0: i18n.t('草稿'),
  10: i18n.t('待整改'),
  19: i18n.t('整改中'),
  30: i18n.t('驳回'),
  50: i18n.t('逾期')
}

export const documentSourceMap = {
  0: i18n.t('手工创建'),
  10: i18n.t('现场评审单'),
  20: i18n.t('QMS报检单'),
  30: i18n.t('限期整改'),
  40: i18n.t('质量绩效')
}

const rectificationTypeMap = JSON.parse(sessionStorage.getItem('rectificationTypeMap'))

const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'rectificationCode',
    headerText: i18n.t('整改单号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] == 0 && data.documentSource !== 40
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] == 0 && data.documentSource !== 40
        }
      }
    ]
    // cssClass: ''
  },
  {
    field: 'rectificationType',
    headerText: i18n.t('整改类型'),
    valueConverter: {
      type: 'map',
      map: rectificationTypeMap
    }
  },
  {
    field: 'purchaserCode',
    width: 200,
    headerText: i18n.t('公司编码')
  },
  {
    field: 'purchaserName',
    width: 200,
    headerText: i18n.t('公司名称')
  },
  {
    width: '200',
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM')
  },
  {
    width: '200',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
  },
  {
    field: 'supplierName',
    width: 200,
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'documentSource',
    headerText: i18n.t('整改来源'),
    valueConverter: {
      type: 'map',
      map: documentSourceMap
    }
  },
  {
    field: 'sourceApplyCode',
    headerText: i18n.t('来源单据号')
  },
  {
    field: 'template',
    headerText: i18n.t('整改模板'),
    ignore: true,
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="download">{{templateName}}</a>`,
          data() {
            return { data: {}, templateName: '' }
          },
          mounted() {
            this.templateName =
              this.data.templateType == '1' ? i18n.t('8D电子版') : this.data.template[0]?.fileName
          },
          methods: {
            download() {
              if (this.data.templateType == '1') {
                const a = document.createElement('a')
                a.href = 'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/supplier/8D电子版.xlsx'
                a.style.display = 'none'
                a.download = i18n.t('8D电子版')
                document.body.appendChild(a)
                a.click()
                document.body.removeChild(a)
              } else {
                this.$store.commit('startLoading')
                this.$API.fileService
                  .downloadPublicFile({
                    id: this.data.template[0].fileId
                  })
                  .then((res) => {
                    this.$store.commit('endLoading')
                    download({
                      fileName: this.data.template[0]?.fileName,
                      blob: res.data
                    })
                  })
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'source',
    headerText: i18n.t('不合格项'),
    ignore: true,
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="showDetail">${i18n.t(
            '查看详情'
          )}</a>`,
          data() {
            return { data: {} }
          },
          methods: {
            showDetail() {
              this.$dialog({
                modal: () => import('../../unqualifiedList.vue'),
                data: {
                  title: i18n.t('查看详情'),
                  id: this.data.id
                }
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'deadline',
    headerText: i18n.t('整改截止时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    field: 'status',
    headerText: i18n.t('单据状态'),
    valueConverter: {
      type: 'map',
      map: statusMap
    },
    cellTools: [
      {
        id: 'release',
        icon: 'icon_card_invite',
        title: i18n.t('发布'),
        visibleCondition: (data) => {
          return data['status'] == 0
        }
      }
    ]
  },
  {
    field: 'rectificationCount',
    headerText: i18n.t('整改次数'),
    type: 'number'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  }
]

export const pageConfig = [
  {
    gridId: '86ea5602-610d-43c3-9350-2d6c57d910e9',
    toolbar: [
      'Add',
      'Edit',
      'Delete',
      {
        id: 'release',
        title: i18n.t('发布'),
        icon: 'icon_solid_Newinvitation'
      },
      {
        id: 'export',
        title: i18n.t('导出')
      }
    ],
    grid: {
      columnData,
      asyncConfig: {
        url: '/supplier/tenant/buyer/partner/reform/wait/list',
        serializeList: (list) => {
          list.forEach((e) => {
            let nameList = []
            let codeList = []
            e.purchaserCompanyInfo.forEach((item) => {
              nameList.push(item.purchaserName)
              codeList.push(item.purchaserCode)
            })
            e.purchaserName = nameList.join()
            e.purchaserCode = codeList.join()
          })
          return list
        }
      },
      frozenColumns: 3
    }
  }
]

export const dialogTools = [['Add', 'Edit', 'Delete']]
export const dialogColumns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 160,
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    cssClass: '',
    cellTools: ['edit', 'delete']
  },
  {
    width: 160,
    field: 'unqualifiedDesc',
    headerText: i18n.t('缺陷描述')
  },
  {
    width: 160,
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: 160,
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: 160,
    field: 'reviewProjectDescription',
    headerText: i18n.t('项目评审说明')
  },
  {
    field: 'attachment',
    headerText: i18n.t('附件'),
    cellTools: [
      {
        id: 'upload',
        icon: 'icon_solid_upload',
        title: i18n.t('上传')
      }
    ],
    cssClass: '',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e[0]?.fileName || '暂无附件'
      }
    }
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  }
]

export const dynamicTools = [['Edit', 'Delete']]
export const dynamicCols = [
  {
    width: 160,
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: 160,
    field: 'unqualifiedDesc',
    headerText: i18n.t('缺陷描述')
  },
  {
    width: 160,
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: 160,
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: 160,
    field: 'reviewProjectDescription',
    headerText: i18n.t('项目评审说明')
  },
  {
    field: 'attachment',
    headerText: i18n.t('附件'),
    ignore: true,
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="downloadAttechment">{{flieName}}</a>`,
          data() {
            return { data: {}, flieName: '' }
          },
          mounted() {
            this.flieName = this.data.attachment[0]?.fileName
          },
          methods: {
            downloadAttechment() {
              this.$store.commit('startLoading')
              this.$API.fileService
                .downloadPublicFile({
                  id: this.data.attachment[0].fileId
                })
                .then((res) => {
                  this.$store.commit('endLoading')
                  download({
                    fileName: this.data.attachment[0]?.fileName,
                    blob: res.data
                  })
                })
            }
          }
        })
      }
    }
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: 160,
    field: 'reasonAnalysis',
    headerText: i18n.t('原因分析')
  },
  {
    field: 'improveMeasures',
    headerText: i18n.t('改善措施')
  }
]
