<template>
  <div class="detail-container">
    <div class="header">
      <div class="title">{{ $t('供应商管理维护') }}</div>
      <div class="operate-bar">
        <div v-if="isEnableStatus || isAddPage" class="op-item mt-flex" @click="save">
          {{ $t('保存') }}
        </div>
        <div v-if="isEnableStatus" class="op-item mt-flex" @click="submit">
          {{ $t('保存并发布') }}
        </div>
        <div class="op-item mt-flex" @click="backToBusinessConfig">
          {{ $t('返回') }}
        </div>
      </div>
    </div>
    <div class="form-warp">
      <mt-form ref="formInstance" class="form-box" :model="formModel" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('整改单号')"
          label-style="top"
          prop="rectificationCode"
        >
          <mt-input v-model="formModel.rectificationCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('单据状态')" label-style="top" prop="status">
          <mt-input :value="statusMap[formModel.status]" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('整改类型')" label-style="top" prop="rectificationType">
          <mt-select
            v-model="formModel.rectificationType"
            :data-source="rectificationTypeList"
            :fields="{ text: 'name', value: 'itemCode' }"
            :placeholder="$t('请选择')"
            :disabled="formDisabled"
            float-label-type="Never"
            @change="typeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" prop="purchaserCode" :label="$t('公司')">
          <mt-input
            v-if="formModel.documentSource == '40'"
            :value="formModel.purchaserName"
            :disabled="true"
          ></mt-input>
          <mt-select
            v-else
            v-model="formModel.purchaserCode"
            float-label-type="Never"
            :data-source="orgList"
            allow-filtering="true"
            filter-type="Contains"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :placeholder="$t('请选择公司')"
            :disabled="formDisabled"
            @change="orgChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" label-style="top" prop="supplierCode">
          <mt-select
            v-model="formModel.supplierCode"
            :data-source="supplierList"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :placeholder="$t('请选择')"
            float-label-type="Never"
            @change="supplierChange"
            :allow-filtering="true"
            filter-type="Contains"
            :filtering="onFiltering"
            :disabled="formDisabled"
            css-class="d72562-c40d-4933-9a24-98c3298365ac"
            :item-template="iTemplate"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('单据来源')" label-style="top" prop="documentSource">
          <mt-input
            :value="documentSourceMap[formModel.documentSource]"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('来源单据号')"
          label-style="top"
          prop="sourceApplyCode"
        >
          <mt-input :value="formModel.sourceApplyCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('期限')" label-style="top" prop="deadline">
          <mt-date-picker
            v-model="formModel.deadline"
            :min="new Date()"
            format="yyyy-MM-dd"
            :placeholder="$t('请选择')"
            :disabled="formDisabled"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item :label="$t('整改模板')" label-style="top" prop="templateType">
          <mt-select
            v-model="formModel.templateType"
            :data-source="templateTypeData"
            :placeholder="$t('请选择')"
            :disabled="formDisabled"
            float-label-type="Never"
            @change="planTempChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item
          :label="$t('自定义模板附件')"
          label-style="top"
          prop="template"
          v-if="formModel.templateType == 2"
        >
          <mt-common-uploader
            :is-single-file="true"
            :accept="acceptType"
            :save-url="saveUrl"
            :download-url="downloadUrl"
            :disabled="formDisabled"
            type="line"
            v-model="formModel.template"
          ></mt-common-uploader>
        </mt-form-item>
        <mt-form-item :label="$t('缺陷附件')" label-style="top" prop="defect">
          <mt-common-uploader
            :is-single-file="true"
            :accept="acceptType"
            :save-url="saveUrl"
            :download-url="downloadUrl"
            :disabled="formDisabled"
            type="line"
            v-model="formModel.defect"
          ></mt-common-uploader>
        </mt-form-item>
        <!-- 19是整改中, 当单据处于19以后的状态都显示整改结果附件 -->
        <mt-form-item
          v-if="formDisabled"
          :label="$t('整改结果附件')"
          label-style="top"
          prop="feedbackAttachment"
        >
          <mt-common-uploader
            :is-single-file="true"
            :accept="acceptType"
            :save-url="saveUrl"
            :download-url="downloadUrl"
            type="line"
            :disabled="true"
            v-model="formModel.feedbackAttachment"
          ></mt-common-uploader>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('报检数量')"
          label-style="top"
          prop="sendQuantity"
        >
          <mt-input :value="formModel.sendQuantity" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('不良率')" label-style="top" prop="rejectRatio">
          <mt-input :value="formModel.rejectRatio" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('不良大类')"
          label-style="top"
          prop="defectBigCateg"
        >
          <mt-input :value="formModel.defectBigCateg" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-col :span="24">
          <mt-form-item :label="$t('备注')" label-style="top" prop="remark">
            <mt-input
              v-model="formModel.remark"
              :multiline="false"
              maxlength="200"
              :placeholder="$t('请输入')"
              :disabled="formDisabled"
              float-label-type="Never"
              width="300%"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-form>
    </div>
    <div class="table-content">
      <div class="table-title">{{ $t('缺陷明细') }}</div>
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>
    </div>
  </div>
</template>
<script>
import {
  dialogColumns,
  dynamicCols,
  statusMap,
  documentSourceMap,
  dynamicTools,
  dialogTools
} from './config/index'
import { cloneDeep } from 'lodash'
import utils from '@/utils/utils'
import commonData from '@/utils/constant'
import itemVue from './components/itemVue'

export default {
  components: {
    itemVue
  },
  data() {
    return {
      disabled: false, //禁用
      formModel: {
        //表单数据
        id: null,
        rectificationCode: null, // 整改单号
        purchaserCode: null, // 公司
        rectificationType: null, // 整改类型
        status: 0, //单据状态
        documentSource: null, // 单据来源
        sourceApplyCode: null, // 来源单据号
        deadline: null, // 截止时间
        remark: null, // 备注
        templateType: '1',
        template: [], // 模板
        defect: [], // 缺陷附件
        feedbackAttachment: [], // 整改结果附件
        supplierCode: '',
        supplierName: '',
        purchaserCompanyInfo: [] // 公司信息
      },
      iTemplate: function () {
        return {
          template: itemVue
        }
      },
      saveUrl: commonData.publicFileUrl,
      downloadUrl: commonData.downloadUrl,
      acceptType: ['.xls', '.xlsx'],

      orgList: [], // 公司列表
      supplierList: [], // 供应商列表
      rectificationTypeList: [],
      templateTypeData: [
        {
          text: this.$t('8D电子版'),
          value: '1'
        },
        {
          text: this.$t('自定义附件'),
          value: '2'
        }
      ],
      rules: {
        purchaserCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        rectificationType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        templateType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        template: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        defect: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ]
      },
      pageConfig: [
        {
          gridId: '3dc78e1f-3a17-41a3-aeaf-9f8ee2ae14ed',
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: this.$route.query?.tabType != 'tobe' ? dynamicTools : dialogTools
          },
          grid: {
            height: 'calc(100vh - 620px)',
            allowPaging: false,
            columnData: this.$route.query?.tabType != 'tobe' ? dynamicCols : dialogColumns,
            lineIndex: 1,
            dataSource: []
          }
        }
      ],
      statusMap,
      documentSourceMap,
      factoryData: []
    }
  },
  computed: {
    pageType() {
      return this.$route?.query?.type || 'add'
    },
    isEnableStatus() {
      return (
        (this.formModel.status == 0 || this.formModel.status == 2) && this.pageType !== 'detail'
      )
    },
    isAddPage() {
      return this.pageType === 'add'
    },
    isEdit() {
      return this.pageType === 'edit' || this.pageType === 'detail'
    },
    formId() {
      return this.$route?.query?.id
    },
    formDisabled() {
      return this.formModel.status > '19' || this.pageType === 'detail'
    }
  },
  created() {
    this.initData()
    this.isRealChange = false
    setTimeout(() => {
      this.isRealChange = true
    }, 2000)
  },
  methods: {
    // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
    handleCompanyData(resData = []) {
      resData = resData.filter((item) => {
        return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      })
      return resData
    },
    onFiltering(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.supplierList.filter((item) => {
            if (
              item?.supplierName.indexOf(e.text) > -1 ||
              item?.supplierCode.indexOf(e.text) > -1
            ) {
              return true
            } else {
              return false
            }
          })
        )
      } else {
        e.updateData(this.supplierList)
      }
    },
    getCompanyData() {
      const params = {
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        page: { current: 1, pages: 0, size: 1000 }
      }
      this.$API.supplierEffective['queryCompany'](params).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          this.orgList = result.data.records.filter((item) => {
            // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
            return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
          })
        } else {
          this.orgList = []
        }

        // 绩效整改，根据公司获取供应商列表
        if (this.formModel.documentSource === 40) {
          const codeList = this.formModel.purchaserCode.split(',')
          const arr = this.orgList.filter((item) => codeList.includes(item.orgCode))
          this.supplierList = []
          arr.map((item) => {
            this.$API.supplierEffective
              .getOrgPartnerRelationsByStatus({
                orgId: item.id,
                status: [10]
              })
              .then((res) => {
                if (res.code == 200 && !utils.isEmpty(res.data)) {
                  this.supplierList = [...this.supplierList, ...res.data]
                }
              })
          })
        }
      })
    },
    orgChange(event) {
      // 有级联
      const { itemData = {} } = event
      const { orgCode, orgName } = itemData
      this.formModel.purchaserCompanyInfo = [
        {
          id: this.formModel.purchaserCompanyInfo[0]?.id,
          purchaserCode: orgCode,
          purchaserName: orgName,
          rectificationId: this.formId
        }
      ]
      this.formModel.supplierName = ''
      // 清空供应商绑定值 和 dataSource
      if (this.isRealChange) {
        this.supplierList = []
        this.formModel.supplierCode = ''
        // 清空工厂数据
        this.factoryData = []
      }
      this.getOrgPartnerRelations(itemData.id)
      this.getFactoryData(itemData.id)
    },
    // STATUS_DRAFT(1, "注册"),
    // STATUS_POTENTIAL(2, "潜在"),
    // STATUS_NORMAL(10, "合格"),
    // STATUS_FROZEN(20, "冻结"),
    // STATUS_BLACK(30, "黑名单"),
    // Integer 类型 这些状态可以惩罚
    getOrgPartnerRelations(orgId) {
      this.$API.supplierEffective
        .getOrgPartnerRelationsByStatus({
          orgId,
          status: [10]
        })
        .then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.supplierList = res.data
          } else {
            this.supplierList = []
          }
        })
    },
    initData() {
      if (this.isEdit) {
        this.$API.rectifyManagement.queryDetail(this.formId).then((res) => {
          let nameList = []
          let codeList = []
          res.data.purchaserCompanyInfo.forEach((item) => {
            nameList.push(item.purchaserName)
            codeList.push(item.purchaserCode)
          })
          this.formModel = {
            ...res.data,
            purchaserCode: codeList.join(),
            purchaserName: nameList.join(),
            deadline: utils.formateTime(new Date(Number(res.data.deadline)))
          }
          if (this.formModel.templateType == 2) {
            this.formModel.template[0].id = this.formModel.template[0].fileId
          }
          if (this.formModel.defect.length > 0) {
            this.formModel.defect.map((item) => {
              item.id = item.fileId
            })
          }
          this.formModel.defect = this.formModel.defect || []
          if (this.formModel.feedbackAttachment.length > 0) {
            this.formModel.feedbackAttachment.map((item) => {
              item.id = item.fileId
            })
          }
          this.formModel.feedbackAttachment = this.formModel.feedbackAttachment || []
          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.buyerRectificationExtDTOList)
        })
      }
      this.getCompanyData()
      this.getTypelist()
      // this.getFactoryData()
      // this.getSupplierList();
    },
    getTypelist() {
      this.$API.masterData
        .queryDict({
          dictCode: 'rectificationType'
        })
        .then((res) => {
          this.rectificationTypeList = res.data
        })
    },
    // 获取工厂列表数据
    getFactoryData(orgId) {
      const params = {
        parentId: orgId
      }
      this.$API.supplierEffective.queryFactory(params).then((res) => {
        if (res.code === 200) {
          this.factoryData = res.data
        }
      })
    },
    // getSupplierList() {
    //   this.$API.masterData.getUserInfo().then((res) => {
    //     if (res.code == 200) {
    //       let _org = res.data.orgList.find(
    //         (e) => e.orgLevelTypeCode === "ORG02"
    //       );
    //       this.$API.rectifyManagement
    //         .querySupplierList({
    //           orgId: _org.id,
    //           status: ["10"],
    //         })
    //         .then((res) => {
    //           this.supplierList = res.data;
    //         });
    //     }
    //   });
    // },
    planTempChange(e) {
      if (e.value == '1') {
        this.formModel.template.length = 0
      }
    },
    typeChange() {},
    supplierChange(e) {
      this.formModel.supplierEnterpriseId = e.itemData.supplierEnterpriseId
      this.formModel.supplierName = e.itemData.supplierName
      this.formModel.supplierCode = e.itemData.supplierCode
    },
    getPartnerArchiveId() {
      let res = null
      if (Array.isArray(this.supplierList) && this.supplierList.length) {
        const targetItem = this.supplierList.find(
          (item) => item.supplierCode === this.formModel.supplierCode
        )
        res = targetItem ? targetItem.partnerArchiveId : null
      }
      return res
    },
    handleClickToolBar(e) {
      let _this = this
      const { toolbar, grid } = e
      let sltList = grid.getSelectedRecords()
      let selectedRowIndexes = grid.getSelectedRowIndexes()
      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Add') {
        this.$dialog({
          modal: () => import('./components/uploadDialog.vue'),
          data: {
            title: this.$t('新增缺陷'),
            factoryData: this.factoryData,
            addFormModel: { ...this.formModel, partnerArchiveId: this.getPartnerArchiveId() }
          },
          success: (data) => {
            let _tempData = cloneDeep(_this.pageConfig[0].grid.dataSource)
            _this.pageConfig[0].grid.dataSource.length = 0
            _tempData.push({
              ...data,
              deleteId: Date.now() // 删除时候用于查找到对应的数据
            })
            this.$set(_this.pageConfig[0].grid, 'dataSource', _tempData)
          }
        })
      } else if (toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./components/uploadDialog.vue'),
          data: {
            title: this.$t('编辑缺陷'),
            isEdit: true,
            info: { ...sltList[0], partnerArchiveId: this.getPartnerArchiveId() },
            index: selectedRowIndexes[0],
            factoryData: this.factoryData
          },
          success: (data) => {
            let _tempData = cloneDeep(_this.pageConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.pageConfig[0].grid, 'dataSource', _tempData)
          }
        })
      } else if (toolbar.id == 'Delete') {
        let _tempData = cloneDeep(_this.pageConfig[0].grid.dataSource)
        sltList.forEach((item) => {
          const index = _tempData.findIndex(
            (row) => (item.id && row.id === item.id) || row.deleteId === item.deleteId
          )
          _tempData.splice(index, 1)
        })
        this.$set(this.pageConfig[0].grid, 'dataSource', _tempData)
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data, componentData } = e
      const { id } = tool || {}
      if (id === 'edit' || id === 'upload') {
        this.$dialog({
          modal: () => import('./components/uploadDialog.vue'),
          data: {
            title: this.$t('编辑缺陷'),
            isEdit: true,
            info: { ...data, partnerArchiveId: this.getPartnerArchiveId() },
            index: componentData.index,
            factoryData: this.factoryData
          },
          success: (data) => {
            let _tempData = cloneDeep(this.pageConfig[0].grid.dataSource)
            _tempData[componentData.index] = data
            this.$set(this.pageConfig[0].grid, 'dataSource', _tempData)
          }
        })
      } else if (id === 'delete') {
        this.pageConfig[0].grid.dataSource.splice(Number(componentData.index), 1)
      }
    },
    confirm(type) {
      this.$refs.formInstance.validate((valid) => {
        if (valid) {
          if (
            this.pageConfig[0].grid.dataSource.length < 1 &&
            this.formModel.documentSource != '40'
          ) {
            this.$toast({
              content: this.$t('缺陷明细不能为空'),
              type: 'warning'
            })
            return
          }
          // 判断是否存在缺陷明细表中有的工厂不属于当前选择的公司的情况
          const containsRes = this.pageConfig[0].grid.dataSource.some((item) => {
            return !this.factoryData.find((el) => el.siteCode === item.siteCode)
          })
          if (containsRes) {
            this.$toast({
              content: this.$t('缺陷明细中存在不属于当前所选公司的工厂！'),
              type: 'warning'
            })
            return
          }
          if (this.formModel.templateType == 2) {
            this.formModel.template[0].fileUrl = this.formModel.template[0].url
          }
          let param = {
            ...this.formModel,
            deadline: Math.round(new Date(this.formModel.deadline)),
            buyerRectificationExtDTOList: this.pageConfig[0].grid.dataSource
          }
          this.$API.rectifyManagement[type](param)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
              }
              if (type === 'saveAndRelease' || (type === 'save' && this.pageType === 'add'))
                this.$router.replace({ name: 'pur-rectification' })
            })
            .catch((err) => {
              this.$toast({
                content: err.msg,
                type: 'error'
              })
            })
        }
      })
    },
    save() {
      this.confirm('save')
    },
    submit() {
      this.confirm('saveAndRelease')
    },
    //返回列表页
    backToBusinessConfig() {
      this.$emit('cancel-function')
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.mt-form-item {
  width: calc(33% - 30px);
  min-width: 200px;
  display: inline-flex;
  margin-left: 10px;
  margin-right: 20px;
  .mt-input {
    width: 100%;
  }
}
.detail-container {
  background: #fff;
  height: 100%;
  .header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      height: 100%;
      line-height: 40px;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      padding: 5px 10px;
    }
    .operate-bar {
      height: 100%;
      float: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;

      .op-item {
        cursor: pointer;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }
  }
  .table-content {
    padding: 10px;
    .table-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
