<template>
  <div class="rectified-content">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { exportData } from '@/utils/utils.js'

export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if ((!sltList || sltList.length <= 0) && ['Edit', 'Delete', 'release'].includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let ids = sltList.map((e) => e.id)
      if (toolbar.id === 'Add') {
        this.handleAdd()
      } else if (toolbar.id === 'Edit' || toolbar.id === 'Delete' || toolbar.id === 'release') {
        let _status = sltList.map((e) => Number(e.status))
        if (_status.includes(10) || _status.includes(30)) {
          this.$toast({
            content: this.$t('只能操作草稿状态的数据'),
            type: 'warning'
          })
          return
        }
        if (toolbar.id === 'release') {
          this.releaseRows(ids)
        } else if (toolbar.id === 'Edit') {
          if (sltList.length > 1) {
            this.$toast({
              content: this.$t('不能同时编辑多行'),
              type: 'warning'
            })
            return
          }
          if (sltList[0].documentSource === 40) {
            this.$toast({
              content: this.$t('不能编辑整改来源为绩效整改的单'),
              type: 'warning'
            })
            return
          }
          this.$router.push({
            name: `pur-rectification-detail`,
            query: {
              type: 'edit',
              id: sltList[0]['id'],
              tabType: 'tobe',
              refreshKey: new Date().getTime()
            }
          })
        } else {
          const documentSourceList = sltList.map((item) => item.documentSource)
          if (documentSourceList.includes(40)) {
            this.$toast({
              content: this.$t('不能删除整改来源为绩效整改的单'),
              type: 'warning'
            })
            return
          }
          this.deleteRows(ids)
        }
      } else {
        // 导出
        this.handleExport()
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'rectificationCode') {
        this.$router.push({
          name: `pur-rectification-detail`,
          query: {
            type: 'detail',
            id: e.data['id'],
            tabType: 'tobe',
            refreshKey: new Date().getTime()
          }
        })
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'edit') {
        this.$router.push({
          name: `pur-rectification-detail`,
          query: {
            type: 'edit',
            id: data.id,
            tabType: 'tobe',
            refreshKey: new Date().getTime()
          }
        })
      } else if (id == 'delete') {
        this.deleteRows([data.id])
      } else if (id == 'release') {
        this.releaseRows([data.id])
      }
    },
    //新增
    handleAdd() {
      this.$router.push({
        name: `pur-rectification-detail`,
        query: {
          type: 'add',
          tabType: 'tobe',
          refreshKey: new Date().getTime()
        }
      })
    },
    deleteRows(idList) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？'),
          confirm: () => _this.$API.rectifyManagement.delete({ ids: idList })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    releaseRows(idList) {
      this.$API.rectifyManagement.release({ ids: idList }).then((res) => {
        console.log('res', res.data)
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 导出
    handleExport() {
      const rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        exportStatus: 1,
        condition: rule.condition || '',
        rules: rule.rules || []
      }
      const requestUrl = {
        preName: 'rectifyManagement',
        urlName: 'export'
      }
      exportData(requestUrl, params)
    }
  }
}
</script>

<style lang="scss" scoped>
.rectified-content {
  height: 100%;
}
</style>
