<template>
  <div class="rectified-content">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { exportData } from '@/utils/utils.js'

export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()

      if ((!sltList || sltList.length <= 0) && toolbar.id !== 'export') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let ids = sltList.map((e) => e.id)
      if (toolbar.id === 'approve') {
        this.approveRows(ids)
      } else if (toolbar.id === 'reject') {
        this.rejectRows(ids)
      } else {
        // 导出
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'approve') {
        this.approveRows([data.id])
      } else if (id == 'reject') {
        this.rejectRows([data.id])
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'rectificationCode') {
        this.$router.push({
          name: `pur-rectification-detail`,
          query: {
            type: 'edit',
            id: e.data['id'],
            refreshKey: new Date().getTime()
          }
        })
      }
    },
    approveRows(idList) {
      this.$API.rectifyManagement.approve({ ids: idList }).then((res) => {
        console.log('res', res.data)
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    rejectRows(idList) {
      this.$API.rectifyManagement.reject({ ids: idList }).then((res) => {
        console.log('res', res.data)
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    // 导出
    handleExport() {
      const rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        exportStatus: 2,
        condition: rule.condition || '',
        rules: rule.rules || []
      }
      const requestUrl = {
        preName: 'rectifyManagement',
        urlName: 'export'
      }
      exportData(requestUrl, params)
    }
  }
}
</script>

<style lang="scss" scoped>
.rectified-content {
  height: 100%;
}
</style>
