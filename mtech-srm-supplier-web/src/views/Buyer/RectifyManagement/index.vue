<!--
  供应商整改管理
-->
<template>
  <div class="rectify-content" v-if="rectificationTypeMap">
    <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig">
      <!-- 待整改 -->
      <to-be-rectified slot="slot-0" index="0"></to-be-rectified>
      <!-- 已整改 -->
      <to-be-Confirm slot="slot-1" index="1"></to-be-Confirm>
      <!-- 已整改 -->
      <rectified slot="slot-2" index="2"></rectified>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //待整改
    toBeRectified: () =>
      import(/* webpackChunkName: "toBeRectified" */ './components/toBeRectified'),
    // 待确认
    toBeConfirm: () => import(/* webpackChunkName: "toBeConfirm" */ './components/toBeConfirm'),
    // 整改完成
    rectified: () => import(/* webpackChunkName: "rectified" */ './components/rectified')
  },
  data() {
    return {
      rectificationTypeMap: null,
      pageConfig: [
        {
          gridId: '6dd9d565-6602-4eaa-81c9-aeeea2bc42f6',
          title: this.$t('待整改')
        },
        {
          gridId: '0ac7d509-98b0-47a3-93b1-fbec35e270f1',
          title: this.$t('待确认')
        },
        {
          gridId: '992f4d14-0137-4cad-aed1-7ac2c87fc3dc',
          title: this.$t('整改完成')
        }
      ]
    }
  },
  created() {
    this.getTypelist()
  },
  methods: {
    getTypelist() {
      this.$API.masterData
        .queryDict({
          dictCode: 'rectificationType'
        })
        .then((res) => {
          if (res.code === 200) {
            let _rectificationTypeMap = {}
            res.data.forEach((e) => {
              _rectificationTypeMap[e.itemCode] = e.name
            })
            this.rectificationTypeMap = _rectificationTypeMap
            sessionStorage.setItem('rectificationTypeMap', JSON.stringify(_rectificationTypeMap))
            console.log(' this.rectificationTypeMap', _rectificationTypeMap)
          }
        })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('rectificationTypeMap')
  }
}
</script>

<style lang="scss" scoped>
.rectify-content {
  width: 100%;
  height: 100%;
}
</style>
