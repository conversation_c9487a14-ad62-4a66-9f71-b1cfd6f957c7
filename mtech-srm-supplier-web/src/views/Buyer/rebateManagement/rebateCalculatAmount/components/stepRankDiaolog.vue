<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <ScTable
        ref="sctableRef"
        :columns="columns"
        :table-data="tableData"
        :is-show-right-btn="false"
        :edit-config="editConfig"
        :sort-config="sortConfig"
      >
      </ScTable>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'

export default {
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('关闭') }
        }
      ],
      tableData: [],
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    sortConfig() {
      return {
        defaultSort: {
          field: 'stageAmount',
          order: 'asc'
        },
        showIcon: false
      }
    },
    columns() {
      const dedaultColumns = [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'rebateLadderType',
          title: this.$t('阶梯类型'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              const selectedItem = this.stepTypeList.find(
                (item) => item.value === row.rebateLadderType
              )
              const rebateLadderTypeName = selectedItem?.text || null
              return [<span>{rebateLadderTypeName}</span>]
            },
            edit: ({ row }) => {
              // 阶梯类型只能选择其一，不能混合选择
              const stepTypeListTemp = [...this.stepTypeList]
              const stList = []
              this.tableData.forEach((r) => r.rebateLadderType && stList.push(r))
              if (this.tableData.length > 1 && stList.length >= 1) {
                stepTypeListTemp.forEach((item) => {
                  item.value !== stList[0].rebateLadderType
                    ? (item.disabled = true)
                    : (item.disabled = false)
                })
              } else {
                stepTypeListTemp.forEach((item) => (item.disabled = false))
              }
              return [
                <vxe-select
                  v-model={row.rebateLadderType}
                  clearable
                  options={stepTypeListTemp}
                  option-props={{ label: 'text', value: 'value' }}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'stageAmount',
          title: this.$t('截止数值'),
          sortable: true,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.stageAmount} clearable type='integer' min='0' transfer />
              ]
            }
          }
        },
        {
          field: 'rebateRatio',
          title: this.$t('返利比例（%）'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-show={row.rebateLadderType === 1}
                  v-model={row.rebateRatio}
                  clearable
                  type='number'
                  min='0'
                  max='100'
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'rebateAmount',
          title: this.$t('返利金额（元）'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-show={row.rebateLadderType === 2}
                  v-model={row.rebateAmount}
                  clearable
                  type='number'
                  min='0'
                  transfer
                />
              ]
            }
          }
        }
      ]

      if (this.modalData.type === 7) {
        const col = {
          field: 'rebatePriceDiff',
          title: this.$t('返利差价'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-show={row.rebateLadderType === 3}
                  v-model={row.rebatePriceDiff}
                  clearable
                  type='number'
                  min='0'
                  transfer
                />
              ]
            }
          }
        }
        dedaultColumns.push(col)
      }

      return dedaultColumns
    },
    stepTypeList() {
      const defaultList = [
        { text: this.$t('返利比例'), value: 1 },
        { text: this.$t('返利金额'), value: 2 }
      ]
      if (this.modalData.type === 7) {
        defaultList.push({ text: this.$t('单片差价'), value: 3 })
      }
      return defaultList
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show() //显示弹窗
    this.tableData = this.modalData.list
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
