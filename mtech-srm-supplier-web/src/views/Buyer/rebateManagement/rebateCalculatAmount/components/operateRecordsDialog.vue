<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="confirm"
  >
    <div class="dialog-content">
      <mt-template-page
        ref="templateDialogRef"
        :use-tool-template="false"
        :hidden-tabs="true"
        :template-config="pageConfig"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { operateRecordsPageConfig } from './config'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: operateRecordsPageConfig
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show() //显示弹窗
    this.getTableData()
  },
  methods: {
    async getTableData() {
      const res = await this.$API.rebateManagement.queryOperateRecords({ id: this.modalData.id })
      if (res.code === 200) {
        this.$set(this.pageConfig[0].grid, 'dataSource', res.data || [{ id: '001' }])
      }
    },
    confirm() {
      this.$emit('confirm-function') //关闭弹窗
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
