<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="rebateCode" :label="$t('返利协议单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.rebateCode"
                :show-clear-button="true"
                :placeholder="$t('请输入返利协议单号')"
              />
            </mt-form-item>
            <mt-form-item prop="rebateName" :label="$t('返利协议名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.rebateName"
                :show-clear-button="true"
                :placeholder="$t('请输入返利协议名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="searchFormModel.companyCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCodeList" :label="$t('供应商')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.supplierCodeList"
                :multiple="true"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="supplier"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.statusList"
                css-class="rule-element"
                :data-source="statusList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="rebateHeaderCreator" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.rebateHeaderCreator"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="totalRound" :label="$t('返利总轮次')" label-style="top">
              <mt-input
                v-model="searchFormModel.totalRound"
                :show-clear-button="true"
                :placeholder="$t('请输入返利总轮次')"
              />
            </mt-form-item>
            <mt-form-item prop="residualRound" :label="$t('剩余轮次')" label-style="top">
              <mt-input
                v-model="searchFormModel.residualRound"
                :show-clear-button="true"
                :placeholder="$t('请输入返利总轮次')"
              />
            </mt-form-item>
            <mt-form-item prop="currentRound" :label="$t('返利当前轮次')" label-style="top">
              <mt-input
                v-model="searchFormModel.currentRound"
                :show-clear-button="true"
                :placeholder="$t('请输入返利总轮次')"
              />
            </mt-form-item>
            <mt-form-item prop="rebateType" :label="$t('返利类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.rebateType"
                css-class="rule-element"
                :data-source="rebateTypeList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择返利计算频次')"
              />
            </mt-form-item>
            <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.categoryCode"
                url="/masterDataManagement/tenant/category/paged-query"
                :placeholder="$t('请选择品类')"
                :fields="{ text: 'categoryName', value: 'categoryCode' }"
                :search-fields="['categoryName', 'categoryCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.factoryCode"
                :remote-search="true"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
                :url="$API.masterData.getSiteListUrl"
                :title-switch="false"
                :placeholder="$t('请选择工厂')"
                select-type="factoryAddress"
              />
            </mt-form-item>
            <mt-form-item prop="startYear" :label="$t('返利起始年')" label-style="top">
              <mt-input
                v-model="searchFormModel.startYear"
                :show-clear-button="true"
                :placeholder="$t('请输入返利起始年')"
              />
            </mt-form-item>
            <mt-form-item prop="startDate" :label="$t('返利起始日')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.startDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择返利起始日')"
                @change="(e) => handleDateTimeChange(e, 'startDate')"
              />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('返利结束日')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.endDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择返利结束日')"
                @change="(e) => handleDateTimeChange(e, 'endDate')"
              />
            </mt-form-item>
            <mt-form-item prop="actualStartDate" :label="$t('实际返利起始时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.actualStartDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择实际返利起始时间')"
                @change="(e) => handleDateTimeChange(e, 'actualStartDate')"
              />
            </mt-form-item>
            <mt-form-item prop="sourceCreatedDate" :label="$t('协议创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.sourceCreatedDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择协议创建日期')"
                @change="(e) => handleDateTimeChange(e, 'sourceCreatedDate')"
              />
            </mt-form-item>
            <mt-form-item prop="amountItemCreateDate" :label="$t('金额创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.amountItemCreateDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择金额创建日期')"
                @change="(e) => handleDateTimeChange(e, 'amountItemCreateDate')"
              />
            </mt-form-item>
            <mt-form-item prop="rebateFreq" :label="$t('返利计算频次')" label-style="top">
              <mt-select
                v-model="searchFormModel.rebateFreq"
                css-class="rule-element"
                :data-source="rebateFreqOptions"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择返利计算频次')"
              />
            </mt-form-item>
            <mt-form-item prop="amountItemStatus" :label="$t('金额行状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.amountItemStatus"
                css-class="rule-element"
                :data-source="amountItemStatusList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择金额行状态')"
              />
            </mt-form-item>
            <mt-form-item prop="currency" :label="$t('币种')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.currency"
                url="/masterDataManagement/tenant/currency/queryAll"
                :placeholder="$t('请选择币种')"
                :fields="{ text: 'currencyName', value: 'currencyCode' }"
                :search-fields="['currencyCode', 'currencyName']"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="profitCenter" :label="$t('利润中心')" label-style="top">
              <mt-input
                v-model="searchFormModel.profitCenter"
                :show-clear-button="true"
                :placeholder="$t('请输入利润中心')"
              />
            </mt-form-item>
            <mt-form-item prop="fsscVoucherNumber" :label="$t('共享单据号')" label-style="top">
              <mt-input
                v-model="searchFormModel.fsscVoucherNumber"
                :show-clear-button="true"
                :placeholder="$t('请输入共享单据号')"
              />
            </mt-form-item>
            <mt-form-item prop="pushFsscYear" :label="$t('推送共享年')" label-style="top">
              <mt-input
                v-model="searchFormModel.pushFsscYear"
                :show-clear-button="true"
                :placeholder="$t('请输入推送共享年')"
              />
            </mt-form-item>
            <mt-form-item prop="pushShareTime" :label="$t('推送共享时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.pushShareTime"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择推送共享时间')"
                @change="(e) => handleDateTimeChange(e, 'pushShareTime')"
              />
            </mt-form-item>
            <mt-form-item prop="groupingName" :label="$t('组别')" label-style="top">
              <mt-input
                v-model="searchFormModel.groupingName"
                :show-clear-button="true"
                :placeholder="$t('请输入组别')"
              />
            </mt-form-item>
            <mt-form-item prop="medCateg" :label="$t('重分类中类')" label-style="top">
              <mt-input
                v-model="searchFormModel.medCateg"
                :show-clear-button="true"
                :placeholder="$t('请输入重分类中类')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import {
  pageConfig,
  statusList,
  amountItemStatusList,
  rebateFreqOptions,
  rebateTypeList
} from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getCurrentBu } from '@/constants/bu'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        startDateRange: [],
        endDateRange: [],
        feedbackDateRange: [],
        creatDateRange: []
      },
      pageConfig,
      statusList,
      amountItemStatusList,
      rebateFreqOptions,
      rebateTypeList
    }
  },
  computed: {},
  mounted() {
    this.$refs.templateRef.refreshCurrentGridData()
  },
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      const { startDate, endDate } = e
      if (startDate) {
        this.searchFormModel[field + 'Start'] = startDate.valueOf()
        this.searchFormModel[field + 'End'] = endDate.valueOf() + 86400000
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let _selectGridRecords = gridRef.getMtechGridRecords()
      if (['viewOA', 'reconApply'].includes(toolbar.id) && !_selectGridRecords.length) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (['viewOA'].includes(toolbar.id) && _selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      switch (toolbar.id) {
        case 'viewOA':
          this.handleViewOA(_selectGridRecords[0])
          break
        case 'reconApply':
          this.handleApply(_selectGridRecords)
          break
        case 'Download':
          this.handleDownload()
          break
        default:
          break
      }
    },
    // 点击单元格标题
    handleClickCellTitle(e) {
      const { field, data } = e
      switch (field) {
        case 'operateRecords':
          this.hanldeViewOperateRecors(data)
          break
        default:
          break
      }
    },
    // 导出
    async handleDownload() {
      const page = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.pageSettings
      const params = {
        ...this.searchFormModel,
        page,
        bu: getCurrentBu()
      }
      const res = await this.$API.rebateManagement.exportrRebateRecord(params)
      if (res.data) {
        download({ fileName: getHeadersFileName(res), blob: res.data })
        this.$toast({ type: 'success', content: this.$t('导出成功') })
      }
    },
    // 查看操作记录
    hanldeViewOperateRecors(row) {
      this.$dialog({
        modal: () => import('./components/operateRecordsDialog.vue'),
        data: {
          title: this.$t('返利执行明细'),
          id: row.id
        },
        success: () => {}
      })
    },
    // 查看OA审批
    async handleViewOA(row) {
      if (row.amountItemStatus !== 8) {
        this.$toast({
          content: this.$t('【金额行状态】非【对账申请审批中】，无法查看！'),
          type: 'warning'
        })
        return
      }
      const params = {
        applyId: row.rebateAmountItemId,
        businessType: 'REBATE_RECONCILIATION_APPLY'
      }
      const res = await this.$API.rebateManagement.getOALink(params)
      if (res.code === 200) {
        window.open(res.data)
      }
    },
    // 对账申请
    async handleApply(rows) {
      const idList = []
      for (let row of rows) {
        if (row.amountItemStatus !== 7) {
          this.$toast({
            content: this.$t('包含【金额行状态】非【供应商待对账】的数据行，请检查！'),
            type: 'warning'
          })
          return
        }
        idList.push(row.rebateAmountItemId)
      }
      const res = await this.$API.rebateManagement.reconciliationApply(idList)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

.full-height {
  height: 100%;
}
::v-deep {
  .j-select.ant-select .ant-select-selection {
    background-color: #f5f5f5;
    border-color: rgba(0, 0, 0, 0.42);
  }

  .e-rowcell.sticky-col-0,
  .e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
  .e-rowcell.sticky-col-1,
  .e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
}
</style>
