<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    height="653"
    @beforeClose="confirm"
  >
    <div class="dialog-content">
      <ScTable
        ref="sctableRef"
        :columns="operateRecordsColumnData"
        :table-data="tableData"
        :auto-height="false"
        show-footer
        :is-show-right-btn="false"
        :footer-method="footerMethod"
      >
      </ScTable>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'

export default {
  components: {
    ScTable
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      operateRecordsColumnData: [
        {
          field: 'id',
          title: this.$t('行号'),
          width: 50
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          minWidth: 140
        },
        {
          field: 'materialName',
          title: this.$t('物料名称'),
          minWidth: 140
        },
        {
          field: 'factoryName',
          title: this.$t('工厂'),
          minWidth: 140
        },
        {
          field: 'quantity',
          title: this.$t('当前结算数量'),
          minWidth: 160
        },
        {
          field: 'recoTotalAmt',
          title: this.$t('当前结算金额'),
          minWidth: 160
        }
      ],
      tableData: [],
      buttons: [
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('关闭') }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show() //显示弹窗
    this.getTableData()
  },
  methods: {
    async getTableData() {
      const res = await this.$API.rebateManagement.getByRecordId({ recordId: this.modalData.id })
      if (res.code === 200) {
        this.tableData = res.data.rebateRecordItemResponseList
      }
    },
    confirm() {
      this.$emit('confirm-function') //关闭弹窗
    },
    footerMethod({ columns, data }) {
      const sums = []
      columns.forEach((column) => {
        let sumCell = null
        switch (column.property) {
          case 'quantity':
          case 'recoTotalAmt':
            sumCell = `${this.$t('总计')}：${this.sumNum(data, column.property)}`
            break
        }
        sums.push(sumCell)
      })
      // 返回一个二维数组的表尾合计
      return [sums]
    },
    // 求和
    sumNum(list, field) {
      let count = 0
      list.forEach((item) => {
        count += parseFloat(item[field])
      })
      return count.toFixed(3)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
