import { i18n } from '@/main.js'

const operateRecordsColumnData = [
  {
    field: 'createUserName',
    headerText: i18n.t('操作人'),
    formatter: (column, row) => {
      return row.remark ? row.remark : row.createUserName
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('操作时间')
  },
  {
    field: 'operateDescription',
    headerText: i18n.t('操作')
  }
]
export const operateRecordsPageConfig = [
  {
    title: i18n.t('操作记录'),
    toolbar: {
      useBaseConfig: false,
      tools: [[], ['Refresh', 'Setting']]
    },
    useToolTemplate: false,
    grid: {
      allowPaging: false,
      columnData: operateRecordsColumnData,
      dataSource: []
    }
  }
]

// 返利类型列表
export const rebateTypeList = [
  { text: i18n.t('物料类别采购金额*返利比例'), value: 1 },
  { text: i18n.t('物料类别采购金额*阶梯返利（金额、比例)'), value: 2 },
  { text: i18n.t('物料类别采购数量*阶梯返利(金额、比例)'), value: 3 },
  { text: i18n.t('物编采购金额*返利比例'), value: 4 },
  { text: i18n.t('物编采购数量*单位数量返利金额'), value: 5 },
  { text: i18n.t('物编采购金额*阶梯返利(金额、比例)'), value: 6 },
  { text: i18n.t('物编采购数量*阶梯返利(金额、比例、差价）'), value: 7 },
  { text: i18n.t('其他-一次执行'), value: 8 },
  { text: i18n.t('其他-分频次执行'), value: 9 },
  { text: i18n.t('付现返利'), value: 10 }
]

// 光伏返利类型列表
export const rebateTypePVList = [
  { text: i18n.t('物编采购金额*阶梯返利(金额、比例)'), value: 6 },
  { text: i18n.t('其他-一次执行'), value: 8 },
  { text: i18n.t('其他-分频次执行'), value: 9 }
]

// 返利计算频次列表
export const rebateCalculationFrequencyList = [
  { text: i18n.t('月度'), value: 1 },
  { text: i18n.t('双月'), value: 2 },
  { text: i18n.t('季度'), value: 3 },
  { text: i18n.t('半年'), value: 4 },
  { text: i18n.t('年度'), value: 5 },
  { text: i18n.t('一次返利'), value: 6 }
]

// 光伏返利计算频次列表
export const pvRebateCalculationFrequencyList = [
  { text: i18n.t('年度'), value: 5 },
  { text: i18n.t('一次返利'), value: 6 }
]

// 阶梯类型
export const stepTypeList = [
  { text: i18n.t('返利比例'), value: 1 },
  { text: i18n.t('返利金额'), value: 2 },
  { text: i18n.t('单片差价'), value: 3 }
]

// 根据返利类型获取对应的返利计算频次列表
export const getRebateCalculationFrequencyList = () => {
  const currentBu = localStorage.getItem('currentBu')
  // 光伏业务单元使用光伏专用频次列表
  if (currentBu === 'GF') {
    return pvRebateCalculationFrequencyList
  }

  // 其他情况使用普通返利计算频次列表
  return rebateCalculationFrequencyList
}
