<template>
  <!-- 明细table -->
  <div class="detail-table">
    <ScTable
      ref="sctableRef"
      grid-id="166fff5d-1024-447e-a2fe-2be50d6e108e"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      :edit-config="editConfig"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </ScTable>

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      :upload-params="{
        headerId: $route.query.id
      }"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    ScTable,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [mixin],
  data() {
    return {
      type: 'detail',
      downTemplateName: this.$t('返利协议创建明细-导入模板'),
      downTemplateParams: {},
      requestUrls: {}
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    dataList() {
      const dataList = []
      this.tableData.forEach((item, index) => {
        if (item.isAdd) {
          item.id = null
        } else if (this.tableRef.isUpdateByRow(item)) {
          item.optType = 'modify'
        }
        dataList.push({
          ...item,
          lineNumber: index + 1,
          actualStartDate: Number(item.actualStartDate)
            ? Number(item.actualStartDate)
            : new Date(item.actualStartDate).getTime()
        })
      })
      return dataList
    }
  },
  mounted() {},
  methods: {
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'stepRank':
          this.$dialog({
            modal: () => import('./stepRankDiaolog.vue'),
            data: {
              title: this.$t('阶梯返利等级设置'),
              id: row.id,
              type: row.rebateType,
              list: row.ladderInfo ? JSON.parse(row.ladderInfo) : []
            },
            success: (list) => {
              row.ladderInfo = JSON.stringify(list)
              const index = this.tableData.findIndex((r) => r.id === row.id)
              this.$set(this.tableData, index, row)
            }
          })
          break
        default:
          break
      }
    },
    // 新增
    async handleAdd() {
      const row = this.tableData[0]
      if (row && [8, 9].includes(row.rebateType)) {
        this.$toast({
          content: this.$t('返利类型为【其他-一次执行、其他-分频次执行】， 只能有一条返利明细！'),
          type: 'warning'
        })
        return
      }
      const newRowData = {
        isAdd: true,
        optType: 'add'
      }
      const { row: newRow } = await this.tableRef.insertAt(newRowData)
      this.tableRef.setEditRow(newRow)
      this.tableData.unshift(newRow)
    },
    // 删除
    handleDelete() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          await this.tableRef.removeCheckboxRow()
          this.tableData = this.tableRef.getTableData().fullData
        }
      })
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'rebateManagement',
        templateUrl: 'downloadRebateAgreementDetailTemplate',
        uploadUrl: 'importRebateAgreementDetailList'
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const includeColumnFiledNames = []
      const tableColumns = this.tableRef.getColumns()
      tableColumns.forEach((col) => col.field && includeColumnFiledNames.push(col.field))

      const params = {
        id: this.$route.query.id,
        includeColumnFiledNames
      }
      const res = await this.$API.rebateManagement.exportRebateAgreementDetailList(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    async upExcelConfirm() {
      this.showUploadExcel(false)
      const res = await this.$API.rebateManagement.getRebateAgreementDetailById({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        this.tableData = this.serializeList(res.data.itemList || [])
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .vxe-input {
    height: 32px;
    line-height: 32px;
  }
}
</style>
