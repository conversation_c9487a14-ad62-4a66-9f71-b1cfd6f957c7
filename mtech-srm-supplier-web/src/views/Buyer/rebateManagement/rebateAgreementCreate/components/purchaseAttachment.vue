<template>
  <!-- 采方附件 -->
  <div class="detail-table">
    <ScTable
      ref="sctableRef"
      :columns="columns"
      :table-data="tableData"
      height="180"
      :auto-height="false"
      :loading="loading"
      :row-config="{ height: 45 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </ScTable>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'
import { download } from '@/utils/utils'

export default {
  components: { ScTable },
  mixins: [mixin],
  data() {
    return {
      type: 'purcahseAttachment'
    }
  },
  computed: {
    dataList() {
      const dataList = []
      this.tableData.forEach((item) => {
        item.id = this.$route.query.id || null
        dataList.push(item)
      })
      return dataList
    }
  },
  mounted() {},
  methods: {
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const _selectRows = this.tableRef.getCheckboxRecords()
      if (['download', 'delete'].includes(e.code) && !_selectRows.length) {
        this.$toast({ content: this.$t('请至少先选择一行数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'upload':
          this.handleUpload()
          break
        case 'download':
          _selectRows.forEach((row) => this.handleDownload(row))
          break
        case 'delete':
          this.handleDelete(_selectRows)
          break
        default:
          break
      }
    },
    // 单元格按钮点击
    handleClickCellTool(code, row) {
      switch (code) {
        case 'download':
          this.handleDownload(row)
          break
        case 'delete':
          this.handleDelete([row])
          break
        default:
          break
      }
    },
    // 单元格title文字点击
    handleClickCellTitle(row, column) {
      if (column.field === 'attachmentName') {
        let params = {
          id: row?.attachmentId,
          useType: 2
        }
        this.$API.fileService.filePreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    // 上传文件
    handleUpload() {
      this.$dialog({
        modal: () => import('@/components/Upload/index.vue'),
        data: {
          title: this.$t('上传')
        },
        success: (data) => {
          let { id, fileName, fileSize, fileType, url, createTime } = data
          const userInfo = JSON.parse(sessionStorage.getItem('userInfo')) || {}
          let newRow = {
            id: this.$route.query.id,
            attachmentId: id,
            attachmentName: fileName,
            attachmentSize: fileSize,
            attachmentType: fileType,
            attachmentUrl: url,
            uploadUserName: userInfo?.username,
            uploadTime: createTime ? new Date(createTime).getTime() : null
          }
          this.handleUpdateAttachmentList([...this.tableData, newRow])
        }
      })
    },
    //删除
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const attachmentList = []
          this.tableData.forEach((item) => {
            const t = rows.find((r) => r.id === item.id)
            !t && attachmentList.push(item)
          })
          this.handleUpdateAttachmentList(attachmentList)
        }
      })
    },
    // 下载
    handleDownload(row) {
      this.$API.fileService.downloadPublicFile({ id: row.attachmentId }).then((res) => {
        download({ fileName: row.attachmentName, blob: new Blob([res.data]) })
      })
    },
    // 更新附件列表
    async handleUpdateAttachmentList(attachmentList) {
      const params = {
        id: this.$route.query.id,
        attachmentList
      }
      const res = await this.$API.rebateManagement.updatePurAttachmentsList(params)
      if (res.code === 200) {
        // 查询附件列表
        const res2 = await this.$API.rebateManagement.getRebateAgreementDetailById({
          id: this.$route.query.id
        })
        if (res2.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.tableData = res2.data?.purchaseFileList || []
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.cell-btn {
  color: #409eff;
  font-size: 12px;
  font-weight: 400;
  margin-right: 10px;
}
</style>
