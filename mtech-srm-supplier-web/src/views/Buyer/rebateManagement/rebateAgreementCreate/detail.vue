<template>
  <div class="full-height">
    <!-- 头部表单 -->
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-btns">
          <vxe-button
            v-for="item in toolbar"
            v-show="!item.isHidden"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :disabled="item.disabled"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="rebateCode" :label="$t('返利协议单号')" label-style="top">
              <mt-input v-model="dataForm.rebateCode" disabled />
            </mt-form-item>
            <mt-form-item prop="rebateName" :label="$t('返利协议名称')" label-style="top">
              <mt-input
                v-model="dataForm.rebateName"
                type="text"
                maxlength="50"
                :disabled="!editable"
                :show-clear-button="true"
                :placeholder="$t('请输入返利协议名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                :disabled="!editable"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="dataForm.companyCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
                @change="(e) => handleValueChange('companyCode', e)"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="agreementTemplateCode" :label="$t('协议书模板')" label-style="top">
              <mt-select
                v-model="dataForm.agreementTemplateCode"
                :data-source="agreementTemplateList"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'text', value: 'agreementCode' }"
                :disabled="!editable || !dataForm.companyCode"
                :placeholder="$t('请选择协议书模板')"
                @change="(e) => handleValueChange('agreementTemplateCode', e)"
              />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="extendCompanyCodes"
              :label="$t('拓展公司')"
              label-style="top"
            >
              <RemoteAutocomplete
                v-model="dataForm.extendCompanyCodes"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                multiple
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                :disabled="!editable || !dataForm.agreementTemplateCode"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
                @multiChange="handleExtendCompanyChange"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="factoryCodeList" :label="$t('工厂')" label-style="top">
              <mt-multi-select
                v-model="dataForm.factoryCodeList"
                :show-clear-button="true"
                :data-source="factoryList"
                :disabled="!editable || !dataForm.companyCode"
                :fields="{ text: 'text', value: 'siteCode' }"
                :show-select-all="true"
                :placeholder="$t('请选择工厂')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <RemoteAutocomplete
                v-model="dataForm.supplierCode"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                :disabled="!editable"
                select-type="supplier"
                @change="(e) => handleValueChange('supplierCode', e)"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="agreementType"
              :label="$t('协议类型')"
              label-style="top"
            >
              <mt-select
                v-model="dataForm.agreementType"
                :data-source="agreementTypeList"
                :disabled="!editable"
                :placeholder="$t('请选择协议类型')"
                @change="handleAgreementTypeChange"
              />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="specialRebateAmount"
              :label="$t('专案返利金额')"
              label-style="top"
            >
              <mt-input-number
                v-model="dataForm.specialRebateAmount"
                :show-clear-button="true"
                :show-spin-button="false"
                :disabled="!editable"
                :placeholder="$t('请输入专案返利金额')"
                :precision="2"
                :min="0"
                :max="*********"
                :step="0.01"
              />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="taxNumber"
              :label="$t('购买方税号')"
              label-style="top"
            >
              <mt-input
                v-model="dataForm.taxNumber"
                :show-clear-button="true"
                :disabled="true"
              />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="rebatePaymentType"
              :label="$t('返利支付方式')"
              label-style="top"
            >
              <mt-select
                v-model="dataForm.rebatePaymentType"
                :data-source="filteredRebatePaymentMethodList"
                :disabled="!editable"
                :placeholder="$t('请选择返利支付方式')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="dataForm.status"
                :data-source="statusList"
                disabled
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="currency" :label="$t('原币编码')" label-style="top">
              <mt-select
                v-model="dataForm.currency"
                :data-source="currencyList"
                :show-clear-button="true"
                :allow-filtering="true"
                :disabled="!editable"
                filter-type="Contains"
                :fields="{ text: 'text', value: 'currencyCode' }"
                :placeholder="$t('请选择币种')"
              />
            </mt-form-item>
            <mt-form-item prop="costCurrency" :label="$t('本位币编码')" label-style="top">
              <mt-select
                v-model="dataForm.costCurrency"
                :data-source="currencyList"
                :show-clear-button="true"
                :allow-filtering="true"
                :disabled="!editable"
                filter-type="Contains"
                :fields="{ text: 'text', value: 'currencyCode' }"
                :placeholder="$t('请选择本位币编码')"
              />
            </mt-form-item>
            <mt-form-item prop="startDate" :label="$t('返利起始日')" label-style="top">
              <mt-date-picker
                v-model="dataForm.startDate"
                :show-clear-button="true"
                :disabled="!editable"
                :allow-edit="false"
                :open-on-focus="true"
                :render-day-cell="(args) => handleRenderDayCell('start', args)"
                :placeholder="$t('请选择返利起始日')"
              />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('返利结束日')" label-style="top">
              <mt-date-picker
                v-model="dataForm.endDate"
                :show-clear-button="true"
                :disabled="!editable"
                :allow-edit="false"
                :open-on-focus="true"
                :render-day-cell="(args) => handleRenderDayCell('end', args)"
                :placeholder="$t('请选择返利结束日')"
              />
            </mt-form-item>
            <mt-form-item prop="feedbackDate" :label="$t('要求反馈日期')" label-style="top">
              <mt-date-picker
                v-model="dataForm.feedbackDate"
                :show-clear-button="true"
                :disabled="!editable"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择要求反馈日期')"
              />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <mt-input
                v-model="dataForm.remark"
                :show-clear-button="true"
                :disabled="!editable"
                :multiline="true"
                :rows="1"
                type="text"
                maxlength="200"
                :placeholder="$t('请输入备注')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierHandleRemark" :label="$t('供方处理意见')" label-style="top">
              <mt-select
                v-model="dataForm.supplierHandleRemark"
                :data-source="handleRemarkList"
                disabled
              />
            </mt-form-item>
            <mt-form-item
              prop="supplierFeedbackRemark"
              :label="$t('供方反馈意见')"
              label-style="top"
            >
              <mt-input
                v-model="dataForm.supplierFeedbackRemark"
                disabled
                :multiline="true"
                :rows="1"
                type="text"
              />
            </mt-form-item>

            <mt-form-item
              v-if="showNeedOa"
              prop="needOa"
              :label="$t('是否需要OA线上审核')"
              label-style="top"
            >
              <mt-select
                v-model="dataForm.needOa"
                :data-source="yesOrNoList"
                :placeholder="$t('请选择')"
                :disabled="!editable"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <!-- 内容部分table -->
    <div class="body-container" v-show="$route.query.id">
      <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab" />
      <div v-if="currentTabIndex === 0" class="table-item">
        <detail-table ref="detailRef" :detail-info="dataForm" :list="detailList" />
      </div>
      <!-- 附件列表 -->
      <div v-if="currentTabIndex === 1">
        <!-- 采方附件 -->
        <div class="table-item">
          <div class="item-top">
            <div class="item-title">
              <div class="title-prefix" />
              {{ $t('附件上传') }}
            </div>
            <div
              class="item-icon"
              :class="[!purIsExpand && 'item-icon-hidden']"
              @click="purIsExpand = !purIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <purchase-attachment
            v-show="purIsExpand"
            ref="purAttachmentRef"
            :detail-info="dataForm"
            :list="purAttachmentList"
          />
        </div>
        <!-- 供方附件 -->
        <div class="table-item">
          <div class="item-top">
            <div class="item-title">
              <div class="title-prefix" />
              {{ $t('供方附件') }}
            </div>
            <div
              class="item-icon"
              :class="[!supIsExpand && 'item-icon-hidden']"
              @click="supIsExpand = !supIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <supplier-attachment
            v-show="supIsExpand"
            ref="supAttachmentRef"
            :list="supAttachmentList"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { statusList, handleRemarkList, yesOrNoList } from './config/index'
import DetailTable from './components/detailTable.vue'
import PurchaseAttachment from './components/purchaseAttachment.vue'
import SupplierAttachment from './components/supplierAttachment.vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getCurrentBuUpper } from '@/constants/bu'

export default {
  components: { DetailTable, PurchaseAttachment, SupplierAttachment, RemoteAutocomplete },
  data() {
    return {
      type: 'detail',
      isExpand: true,
      dataForm: {
        factoryCodeList: [],
        status: 1,
        extendCompanyNames: [] // 拓展公司名称数组
      },
      statusList,
      handleRemarkList,
      yesOrNoList,
      agreementTemplateList: [],
      factoryList: [],
      currencyList: [],
      purIsExpand: true,
      supIsExpand: true,
      detailList: [], //明细列表
      purAttachmentList: [], //采方附件
      supAttachmentList: [], // 供方附件
      isInit: true,
      currentTabIndex: 0,
      tabList: [
        {
          title: this.$t('明细')
        },
        {
          title: this.$t('附件')
        }
      ],

      agreementTypeList: [
        {
          value: 1,
          text: this.$t('专案返利')
        },
        {
          value: 2,
          text: this.$t('框架返利')
        }
      ],

      // 返利支付方式选项
      rebatePaymentMethodList: [
        {
          value: 1,
          text: this.$t('红票')
        },
        {
          value: 2,
          text: this.$t('低开')
        },
        {
          value: 3,
          text: this.$t('高开')
        }
      ]
    }
  },
  computed: {
    editable() {
      return [1, 3, 6].includes(this.dataForm.status)
    },
    toolbar() {
      const { id } = this.$route.query
      const toolbar = [
        {
          code: 'print',
          name: this.$t('协议打印'),
          isHidden: !id
        },
        {
          code: 'save',
          name: this.$t('保存'),
          isHidden: !this.editable
        },
        {
          code: 'publish',
          name: this.$t('保存并发布'),
          isHidden: !this.editable
        },
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        }
      ]
      return toolbar
    },
    formRules() {
      return {
        rebateCode: [
          {
            required: this.$route.query.type === 'edit',
            message: this.$t('请输入定价单名称'),
            trigger: 'blur'
          }
        ],
        rebateName: [{ required: true, message: this.$t('请输入返利协议名称'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        agreementTemplateCode: [
          { required: true, message: this.$t('请选择协议书模板'), trigger: 'blur' }
        ],
        factoryCodeList: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        currency: [{ required: true, message: this.$t('请选择币种'), trigger: 'blur' }],
        costCurrency: [{ required: true, message: this.$t('请选择本位币编码'), trigger: 'blur' }],
        startDate: [{ required: true, message: this.$t('请选择返利起始日'), trigger: 'blur' }],
        endDate: [{ required: true, message: this.$t('请选择返利结束日'), trigger: 'blur' }],
        needOa: [{ required: true, message: this.$t('请选择是否需要OA线上审核'), trigger: 'blur' }],
        // KT事业部专用字段验证规则
        agreementType: [{ required: this.showKtField, message: this.$t('请选择协议类型'), trigger: 'blur' }],
        rebatePaymentType: [{ required: this.showKtField, message: this.$t('请选择返利支付方式'), trigger: 'blur' }],
        // 专案返利金额：专案时为必填，框架时不必填
        specialRebateAmount: [
          { required: this.isSpecialAgreementType, message: this.$t('请输入专案返利金额'), trigger: 'blur' },
          {
            validator: this.validatespecialRebateAmount,
            trigger: 'blur'
          }
        ],
      }
    },
    showNeedOa() {
      return ['KT', 'GF'].includes(getCurrentBuUpper())
    },
    showKtField() {
      return ['KT'].includes(getCurrentBuUpper())
    },
    // 判断是否为专案协议类型（专案返利金额必填）
    isSpecialAgreementType() {
      return this.showKtField && this.dataForm.agreementType === 1
    },
    // 根据协议类型过滤返利支付方式选项
    filteredRebatePaymentMethodList() {
      if (!this.showKtField || !this.dataForm.agreementType) {
        return this.rebatePaymentMethodList
      }

      // 专案：显示所有3种方式
      if (this.dataForm.agreementType === 1) {
        return this.rebatePaymentMethodList
      }

      // 框架：只显示红票和低开
      if (this.dataForm.agreementType === 2) {
        return this.rebatePaymentMethodList.filter(item => [1, 2].includes(item.value))
      }

      return this.rebatePaymentMethodList
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getCurrencyList()
      if (this.$route.query.type !== 'create') {
        this.getDetailInfo()
      }
    },
    // 获取明细信息
    async getDetailInfo() {
      this.isInit = true
      const res = await this.$API.rebateManagement.getRebateAgreementDetailById({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        const { header, itemList, purchaseFileList, supplierFileList } = res.data

        const { siteInfo, startDate, endDate, feedbackDate } = header
        const factoryCodeList = []
        const factoryList = siteInfo ? JSON.parse(siteInfo) : []
        factoryList.forEach((item) => factoryCodeList.push(item.code))
        this.dataForm = {
          ...header,
          factoryCodeList,
          startDate: startDate ? dayjs(Number(startDate)).format('YYYY-MM-DD') : null,
          endDate: endDate ? dayjs(Number(endDate)).format('YYYY-MM-DD') : null,
          feedbackDate: feedbackDate ? dayjs(Number(feedbackDate)).format('YYYY-MM-DD') : null,
          // 将拓展公司相关字符串转换为数组
          extendCompanyCodes: this.convertStringToArray(header.extendCompanyCodes),
          extendCompanyNames: this.convertStringToArray(header.extendCompanyNames)
        }
        this.detailList = itemList
        this.purAttachmentList = purchaseFileList
        this.supAttachmentList = supplierFileList

        this.getAgreementTemplateList(header.companyCode)
        this.getFactoryList(header.companyCode)
      }
    },
    // 协议模板下拉列表
    async getAgreementTemplateList(companyCode) {
      const params = {
        status: 1,
        companyCode
      }
      const res = await this.$API.rebateManagement.queryAgreemenTempalteList(params)
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.agreementCode + '-' + item.agreementName
        })
        this.agreementTemplateList = res.data || []
      }
    },
    // 工厂下拉列表
    async getFactoryList(companyCode) {
      const params = {
        condition: '',
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          {
            label: this.$t('公司'),
            field: 'parentCode',
            type: 'string',
            operator: 'equal',
            value: companyCode
          }
        ],
        pageFlag: false
      }

      const res = await this.$API.masterData.getSiteList(params)
      if (res.code === 200) {
        res.data.records?.forEach((item) => (item.text = item.siteCode + '-' + item.siteName))
        this.factoryList = res.data.records
      }
    },
    // 获取币种下拉列表
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.currencyCode + '-' + item.currencyName
        })
        this.currencyList = res.data
      }
    },
    // 限制日期选择器选择
    handleRenderDayCell(type, args) {
      const day = dayjs(args.date).get('date')
      const lastDay = dayjs(args.date).endOf('month').$D
      if (type === 'end' && day !== lastDay) {
        args.isDisabled = true
        return
      }
      if (type === 'start' && day !== 1) {
        args.isDisabled = true
        return
      }
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    },
    // 专案返利金额验证方法
    validatespecialRebateAmount(_, value, callback) {
      // 如果不是专案协议类型或者值为空，跳过验证
      if (!this.isSpecialAgreementType || value === null || value === undefined || value === '') {
        callback()
        return
      }

      const numValue = Number(value)

      // 检查是否为有效数字
      if (isNaN(numValue)) {
        callback(new Error(this.$t('请输入有效的数字')))
        return
      }

      // 检查是否为负数
      if (numValue < 0) {
        callback(new Error(this.$t('专案返利金额不能为负数')))
        return
      }

      // 检查最大值（1亿 = 100,000,000）
      if (numValue > *********) {
        callback(new Error(this.$t('专案返利金额不得超过1亿')))
        return
      }

      // 检查小数位数（最多2位）
      const decimalPart = value.toString().split('.')[1]
      if (decimalPart && decimalPart.length > 2) {
        callback(new Error(this.$t('专案返利金额最多保留2位小数')))
        return
      }

      callback()
    },

    // 协议类型变更处理
    handleAgreementTypeChange(e) {
      const { value } = e

      // 当协议类型变更时，清空返利支付方式的值（如果当前值在新类型下不可用）
      if (this.dataForm.rebatePaymentType) {
        const availableOptions = this.filteredRebatePaymentMethodList.map(item => item.value)
        if (!availableOptions.includes(this.dataForm.rebatePaymentType)) {
          this.$set(this.dataForm, 'rebatePaymentType', null)
        }
      }

      // 如果从专案切换到框架，清空专案返利金额
      if (value === 2 && this.dataForm.specialRebateAmount) {
        this.$set(this.dataForm, 'specialRebateAmount', null)
      }

      // 当协议类型变更时，清空明细列表中不符合要求的返利类型
      if (value === 1) {
        // 专案返利：只能选择其他-一次执行（值为8）
        this.detailList.forEach(item => {
          if (item.rebateType && item.rebateType !== 8) {
            item.rebateType = null
            item.rebateFreq = null
          }
        })
      }
    },

    // 字符串转数组的通用方法
    convertStringToArray(value) {
      if (!value) return []
      if (Array.isArray(value)) return value
      if (typeof value === 'string') {
        return value.split(',').map(item => item.trim()).filter(item => item)
      }
      return []
    },
    // 拓展公司多选变化处理
    handleExtendCompanyChange(checkedList) {
      // 更新拓展公司名称数组
      this.dataForm.extendCompanyNames = checkedList.map(item => item.orgName)
    },
    // 下拉列表选中值修改
    handleValueChange(prefix, e) {
      const { value, itemData } = e
      switch (prefix) {
        // 选择公司
        case 'companyCode':
          this.dataForm.companyName = itemData?.orgName

          if (value) {
            this.getAgreementTemplateList(value)
            this.getFactoryList(value)
          } else {
            this.agreementTemplateList = []
            this.factoryList = []
          }

          if (!this.isInit) {
            this.$set(this.dataForm, 'agreementTemplateCode', null)
            this.$set(this.dataForm, 'agreementTemplateName', null)

            this.$set(this.dataForm, 'factoryCodeList', [])
          }
          break
        case 'agreementTemplateCode':
          this.$set(this.dataForm, 'agreementTemplateName', itemData?.agreementName || null)
          break
        case 'supplierCode':
          this.$set(this.dataForm, 'supplierName', itemData?.supplierName || null)
          break
        default:
          break
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.$router.go(-1)
          break
        case 'print':
          this.handlePrint()
          break
        case 'save':
          this.handleSave()
          break
        case 'publish':
          this.handleSave('publish')
          break
        default:
          break
      }
    },
    // 保存、保存后发布
    handleSave(type) {
      this.$refs.dataFormRef.validate(async (valid) => {
        if (valid) {
          const itemList =
            this.currentTabIndex === 0 ? this.$refs.detailRef?.dataList : this.detailList
          if (!this.validateDeatilList(itemList)) {
            return
          }
          const { factoryCodeList, startDate, endDate, feedbackDate } = this.dataForm
          const siteInfo = []
          factoryCodeList?.forEach((code) => {
            const item = this.factoryList.find((f) => f.siteCode === code)
            siteInfo.push({
              code: item.siteCode,
              name: item.siteName
            })
          })
          const params = {
            ...this.dataForm,
            startDate: startDate ? new Date(startDate).getTime() : null,
            endDate: endDate ? new Date(endDate).getTime() : null,
            feedbackDate: feedbackDate ? new Date(feedbackDate).getTime() : null,
            siteInfo: JSON.stringify(siteInfo),
            itemList,
            attachmentList:
              this.currentTabIndex === 1
                ? this.$refs.purAttachmentRef?.dataList
                : this.purAttachmentList,
            // 将拓展公司相关数组转换为逗号分隔的字符串
            extendCompanyCodes: Array.isArray(this.dataForm.extendCompanyCodes)
              ? this.dataForm.extendCompanyCodes.join(',')
              : this.dataForm.extendCompanyCodes,
            extendCompanyNames: Array.isArray(this.dataForm.extendCompanyNames)
              ? this.dataForm.extendCompanyNames.join(',')
              : this.dataForm.extendCompanyNames
          }
          delete params.factoryCodeList

          const prefix = this.$route.query.type === 'create' ? 'create' : 'save'
          const res = await this.$API.rebateManagement[prefix + 'RebateAgreement'](params)
          if (res.code === 200) {
            !type && this.$toast({ content: this.$t('保存成功'), type: 'success' })

            this.$router.replace({
              name: 'rebate-agreement-create-detail',
              query: {
                type: 'edit',
                id: res.data,
                refreshId: Date.now()
              }
            })
            type === 'publish' && this.handlePublish(res.data)
          }
        }
      })
    },
    // 发布
    async handlePublish(id) {
      const res = await this.$API.rebateManagement.publishRebateAgreement([id])
      if (res.code === 200) {
        this.$toast({ content: this.$t('发布成功'), type: 'success' })
        this.getDetailInfo()
      }
    },
    // 打印
    async handlePrint() {
      let buffer = await this.$API.rebateManagement
        .printRebateAgreement({ id: this.$route.query.id })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    // 校验明细行数据是否合法
    validateDeatilList(list) {
      for (let index = 0; index < list.length; index++) {
        const {
          categoryCode,
          materialCode,
          rebateType,
          rebateRatio,
          untaxedAmt,
          priceDiff,
          rebateFreq,
          actualStartDate,
          ladderInfo
        } = list[index]

        if (!rebateType) {
          this.$toast({
            content: this.getMsg(index, 'rebateType'),
            type: 'warning'
          })
          return false
        }

        if ([1, 2, 3].includes(rebateType)) {
          if (!categoryCode) {
            this.$toast({
              content: this.getMsg(index, 'categoryCode'),
              type: 'warning'
            })
            return false
          }
        }

        if ([4, 5, 6, 7].includes(rebateType)) {
          if (!materialCode) {
            this.$toast({
              content: this.getMsg(index, 'materialCode'),
              type: 'warning'
            })
            return false
          }
        }

        if ([1, 4].includes(rebateType)) {
          if (!rebateRatio && rebateRatio !== 0) {
            this.$toast({
              content: this.getMsg(index, 'rebateRatio'),
              type: 'warning'
            })
            return false
          }
        }

        if (rebateType === 5) {
          if (!priceDiff && priceDiff !== 0) {
            this.$toast({
              content: this.getMsg(index, 'priceDiff'),
              type: 'warning'
            })
            return false
          }
        }

        if ([8, 9].includes(rebateType)) {
          if (!untaxedAmt && untaxedAmt !== 0) {
            this.$toast({
              content: this.getMsg(index, 'untaxedAmt'),
              type: 'warning'
            })
            return false
          }
        }

        if (!rebateFreq) {
          this.$toast({
            content: this.getMsg(index, 'rebateFreq'),
            type: 'warning'
          })
          return false
        }

        if (!actualStartDate) {
          this.$toast({
            content: this.getMsg(index, 'actualStartDate'),
            type: 'warning'
          })
          return false
        }

        const stepRankList = ladderInfo ? JSON.parse(ladderInfo) : []
        if ([2, 3, 6, 7].includes(rebateType) && stepRankList.length === 0) {
          this.$toast({
            content: this.getMsg(index, 'stepRankList'),
            type: 'warning'
          })
        }
      }
      return true
    },
    handleSelectTab(index) {
      index === 0 && (this.purAttachmentList = this.$refs.purAttachmentRef?.dataList)
      index === 1 && (this.detailList = this.$refs.detailRef?.dataList)
      this.currentTabIndex = index
    },
    getMsg(index, key) {
      const keyMap = {
        rebateType: this.$t('返利类型'),
        categoryCode: this.$t('物料类别'),
        materialCode: this.$t('物料编码'),
        rebateRatio: this.$t('返利比例'),
        priceDiff: this.$t('单片差价'),
        untaxedAmt: this.$t('返利金额未税'),
        rebateFreq: this.$t('返利计算频次'),
        actualStartDate: this.$t('实际返利起始时间'),
        stepRankList: this.$t('阶梯等级明细列表')
      }
      const msg =
        this.$t('第') + (index + 1) + this.$t('行') + ', ' + keyMap[key] + this.$t('不能为空')
      return msg
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  width: 100%;
  height: auto;
  padding: 8px;
  background: #fff;
  overflow: scroll;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-btns {
      text-align: right;
      margin-bottom: 10px;
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 8px 8px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 8px 8px 0 0;
    }
  }
}

.body-container {
  .table-item {
    width: 100%;
    margin-top: 10px;
    .item-top {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .item-title {
        display: flex;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-weight: 500;
        .title-prefix {
          width: 5px;
          height: 18px;
          background-color: #409eff;
          margin: 7px 15px 0 0;
        }
      }

      .item-icon {
        color: #409eff;
        transform: rotate(270deg);
      }
      .item-icon-hidden {
        transform: rotate(90deg);
        margin-right: 16px;
      }
    }
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
  }

  .j-select.ant-select .ant-select-selection {
    background-color: #f5f5f5;
    border-color: rgba(0, 0, 0, 0.42);
  }
}
::v-deep .e-searcher {
  width: 100% !important;
}
::v-deep .e-input-group.e-control-wrapper.e-multi-line-input {
  height: 31px !important;
}
</style>
