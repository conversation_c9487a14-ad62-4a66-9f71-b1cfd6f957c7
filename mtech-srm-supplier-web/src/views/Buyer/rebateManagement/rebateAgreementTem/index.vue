<template>
  <div class="perform-box">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="agreementCode" :label="$t('协议书编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.agreementCode"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="agreementName" :label="$t('协议书名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.agreementName"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="searchFormModel.companyCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
                @change="(e) => handleValueChange('companyCode', e)"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="agreementType" :label="$t('协议类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.agreementType"
                css-class="rule-element"
                :data-source="agreementTypeOption"
                :fields="{ text: 'label', value: 'value' }"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import { columns } from './config.js'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      orgIdArr: [],
      searchFormModel: {},
      pageConfig: [
        {
          gridId: '6dee1a20-29ac-4fb8-afb3-f31f0a20a471',
          title: '',
          toolbar: [
            { id: 'templateAdd', title: i18n.t('新增') },
            { id: 'delete', title: i18n.t('删除') },
            { id: 'effect', title: i18n.t('启用') },
            { id: 'loseEffectiveness', title: i18n.t('停用') }
          ],
          useToolTemplate: false,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            // virtualPageSize: 30,
            // enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            showSelected: false,
            columnData: columns,
            asyncConfig: {
              url: 'analysis/tenant/rebateAgreementTemplate/pageQuery'
            }
          }
        }
      ],
      agreementTypeOption: [
        { label: i18n.t('返利协议'), value: 1 },
        { label: i18n.t('付现协议'), value: 2 }
      ],
      companyIdKey: 1
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 工具栏
    handleClickToolBar(item) {
      // let records = item.data ? [item.data] : item.grid.getSelectedRecords()

      let records = item.gridRef.getCustomSelectedRows()
      if (
        records.length <= 0 &&
        (item.toolbar.id == 'effect' ||
          item.toolbar.id == 'loseEffectiveness' ||
          item.toolbar.id == 'delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (records.length > 1) {
        this.$toast({ content: this.$t('只允许对一条数据进行操作'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'effect') {
        // 启用
        const id = records.map((e) => e.id)
        this.handleTemplateEffect(id)
      } else if (item.toolbar.id == 'loseEffectiveness') {
        // 停用
        const ids = records.map((e) => e.id)
        this.handleTemLoseEffectiveness(ids)
      } else if (item.toolbar.id == 'delete') {
        // 删除
        // const ids = records.map((e) => e.id)
        this.handleTemDelete(records)
      } else if (item.toolbar.id == 'templateAdd') {
        // 新增
        this.handleTemplateAdd()
      }
    },
    // 新增
    handleTemplateAdd() {
      this.$router.push({
        path: `/supplier/rebate-management/rebate-agreement-tem-detail`
      })
    },
    // 单元格字段点击事件,跳转页面
    handleClickCellTitle(e) {
      if (e.field === 'agreementCode') {
        this.handleTemplateDetail(e.data)
      }
    },
    // 模板明细跳转
    handleTemplateDetail(e) {
      sessionStorage.setItem('agreementTem', JSON.stringify(e))
      this.$router.push({
        path: `/supplier/rebate-management/rebate-agreement-tem-detail`,
        query: {
          id: e.id
        }
      })
    },
    // 启用
    handleTemplateEffect(arr) {
      const params = {
        id: arr.toString(),
        status: 1
      }
      this.$API.rebateManagement.updateRebateTemplateStatus(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 停用
    handleTemLoseEffectiveness(arr) {
      const params = {
        id: arr.toString(),
        status: -1
      }
      this.$API.rebateManagement.updateRebateTemplateStatus(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除 只有新建可以删除
    handleTemDelete(arr) {
      if (arr.length > 1) {
        this.$toast({ content: this.$t('只允许对一条数据进行操作'), type: 'warning' })
        return
      }
      if (arr.some((e) => e.status === 1))
        return this.$toast({ content: this.$t('启用状态的数据不可删除'), type: 'warning' })
      const params = {
        id: arr.map((e) => e.id).toString()
      }
      this.$API.rebateManagement.deleteRebateTemplate(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
.perform-box {
  width: 100%;
  position: relative;
  height: 100%;
  /deep/.e-rowcell.sticky-col-0,
  /deep/.e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  /deep/.e-rowcell.sticky-col-1,
  /deep/.e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
  .red {
    background-color: rgba(237, 86, 51, 1);
  }
  .green {
    background-color: rgba(138, 204, 64, 1);
  }
  .yellow {
    background-color: rgba(237, 161, 51, 1);
  }
  .yellowcolor {
    color: rgba(237, 161, 51, 1) !important;
  }
  .bluecolor {
    color: #6386c1 !important;
  }
  .blurbgcolor {
    background-color: #00469c;
  }
  ::v-deep .now-status {
    padding: 3px 5px;
    color: #6386c1;
    background-color: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
  }
  ::v-deep .now-status-stop {
    padding: 3px 5px;
    color: #9a9a9a;
    background-color: rgba(154, 154, 154, 0.1);
    border-radius: 2px;
  }
  ::v-deep .change-status {
    color: #6386c1;
    margin-top: 5px;
    cursor: pointer;
  }
}
::v-deep .dimension-name {
  width: calc((100% - 24px) / 2);
  display: inline-block;
}
::v-deep .dimension-type {
  width: calc((100% - 24px) / 2);
  margin-left: 24px;
  display: inline-block;
}
::v-deep {
  .template_checkbox {
    text-align: center;
    input[type='checkbox'] {
      visibility: visible;
    }
  }
}
</style>
