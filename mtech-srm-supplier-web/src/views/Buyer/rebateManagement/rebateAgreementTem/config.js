import { i18n } from '@/main.js'

export const columns = [
  // {
  //   width: '30',
  //   type: 'checkbox',
  //   allowEditing: false,
  //   allowResizing: false,
  //   customAttributes: {
  //     class: 'sticky-col-0'
  //   }
  // },
  {
    field: 'agreementCode',
    headerText: i18n.t('协议书编码'),
    width: '120',
    cssClass: 'field-content'
  },
  {
    field: 'agreementName',
    headerText: i18n.t('协议书名称'),
    width: '120'
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '100'
  },
  {
    field: 'agreementType',
    headerText: i18n.t('协议类型'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('返利协议'),
        2: i18n.t('付现协议')
      },
      fields: { text: 'label', value: 'status' }
    },
    width: '100'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('启用'),
        '-1': i18n.t('停用')
      },
      fields: { text: 'label', value: 'status' }
    },
    width: '70'
  },
  {
    field: 'version',
    headerText: i18n.t('版本')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '80'
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    ignore: true,
    // searchOptions: {
    //   type: 'date',
    //   dateFormat: 'YYYY-mm-dd'
    // },
    // template: () => {
    //   return {
    //     template: Vue.component('time-tmp', {
    //       template: `
    //             <div class="time-box">
    //               {{ data.createDate }}
    //             </div>`
    //     })
    //   }
    // },
    width: '140'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '80'
  }
]
