<template>
  <div class="perform-box">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="templateCode" :label="$t('模板编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.templateCode"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="templateName" :label="$t('模板名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.templateName"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="templateType" :label="$t('模板类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.templateType"
                css-class="rule-element"
                :data-source="ABILITY_TEMPLATE_TYPE"
                :fields="{ text: 'dictName', value: 'dictCode' }"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
            <mt-form-item prop="categCode" :label="$t('品类')" label-style="top">
              <mt-DropDownTree
                :fields="categoryListArrList"
                v-model="searchFormModel.categCode"
                :allow-multi-selection="true"
                :auto-check="true"
                :show-check-box="true"
                :allow-filtering="true"
                filter-type="Contains"
                id="checkboxTreeSelect"
                :placeholder="$t('请输入品类名称或编码进行搜索')"
                :key="categoryListArrList.key"
              ></mt-DropDownTree>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                css-class="rule-element"
                :data-source="statusMap"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
    <!-- 新增弹窗 -->
    <mt-dialog
      ref="dialog"
      v-if="dialogVisible"
      css-class="index-dialog"
      :header="dialogHeader"
      :buttons="buttons"
      :open="onOpen"
      :close="onClose"
      height="40%"
      width="40%"
    >
      <mt-form ref="ruleForm" :model="Adddata" :rules="calcRules" style="margin-top: 15px">
        <mt-form-item prop="templateName" class="form-item dimension-name" :label="$t('模板名称')">
          <mt-input
            maxlength="20"
            v-model="Adddata.templateName"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入模板名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="templateType" class="form-item dimension-type" :label="$t('模板类型')">
          <mt-select
            v-model="Adddata.templateType"
            :data-source="ABILITY_TEMPLATE_TYPE"
            :show-clear-button="true"
            :placeholder="$t('请选择模板类型')"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            @change="templateTypeSelect"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
          <mt-DropDownTree
            :key="orgFields.dataSource.length"
            v-model="Adddata.orgIdArr"
            :placeholder="$t('请选择组织：')"
            :popup-height="500"
            :fields="orgFields"
            @input="selectOrg"
            :allow-filtering="true"
            filter-type="Contains"
            id="baseTreeSelect"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
          <mt-input
            maxlength="100"
            v-model="Adddata.remark"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
// import { getHeadersFileName, download } from '@/utils/utils.js'
import { columns, ABILITY_TEMPLATE_TYPE } from './config.js'
// import dayjs from 'dayjs'
// import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    // RemoteAutocomplete
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      orgIdArr: [],

      dialogVisible: false,
      dialogHeader: this.$t('新增'),
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('新增') }
        }
      ],
      Adddata: {
        templateName: '',
        templateType: '',
        remark: '',
        orgName: '',
        orgCode: '',
        orgId: '',
        orgIdArr: [],
        templateTypeName: ''
      },
      //品类下拉
      categoryListArrList: {
        dataSource: [], //品类树下拉数组
        value: 'categoryCode',
        text: 'codeAndName',
        child: 'childrens',
        key: 'E6BPdKfEj5Ky2NwbH2F6Nkrhj6W8MamE'
      },
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      searchFormModel: {},
      pageConfig: [
        {
          gridId: '6dee1a20-29ac-4fb8-afb3-f31f0a20D567',
          title: '',
          toolbar: [
            // { id: 'templateDetail', title: i18n.t('模板明细') },
            { id: 'templateAdd', title: i18n.t('新增') },
            { id: 'effect', title: i18n.t('生效') },
            { id: 'loseEffectiveness', title: i18n.t('失效') },
            { id: 'delete', title: i18n.t('删除') }
          ],
          useToolTemplate: false,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            columnData: columns,
            asyncConfig: {
              url: 'analysis/tenant/buyer/supplier/ability/template/pageQuery'
            }
          }
        }
      ],
      ABILITY_TEMPLATE_TYPE,
      calcRules: {
        templateName: [{ required: true, message: this.$t('请输入模板名称'), trigger: 'blur' }],
        templateType: [{ required: true, message: this.$t('请输入模板类型'), trigger: 'blur' }],
        orgIdArr: [{ required: true, message: this.$t('请选择组织范围'), trigger: 'blur' }]
      },
      orgList: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      statusMap: [
        { text: this.$t('新建'), value: 0 },
        { text: this.$t('生效'), value: 1 },
        { text: this.$t('失效'), value: 2 }
      ],
      ruletypeMap: [],
      categorySearchFields: ['categCode', 'categName']
    }
  },
  watch: {
    'Adddata.orgIdArr': {
      handler() {
        this.$refs.ruleForm.validateField('orgIdArr')
      }
    }
  },
  created() {},
  mounted() {
    this.getCategorys('')
    this.getOrgList()
  },
  methods: {
    // 品类下拉框事件
    getCategorys(val) {
      this.$API.categoryResources
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          const list = res.data.records.map((i) => {
            return {
              ...i,
              codeAndName: `${i.categoryName}-${i.categoryCode}`
            }
          })
          this.$set(this.categoryListArrList, 'dataSource', list)
          this.$set(this.categoryListArrList, 'key', this.randomString())
        })
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.show()
      })
    },
    hide() {
      this.dialogVisible = false
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.hide()
      })
    },
    onOpen(args) {
      this.show()
      args.preventFocus = true
    },
    onClose() {
      this.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增
          const params = {
            ...this.Adddata
          }
          delete params.orgIdArr
          this.$API.categoryResources.abilitySaveOrUpdate(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('模板数据保存成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
              this.hide()
              this.Adddata = {
                templateName: '',
                templateType: '',
                remark: '',
                orgName: '',
                orgCode: '',
                orgId: '',
                orgIdArr: [],
                templateTypeName: ''
              }
            }
          })
        }
      })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          // const OrgresData = []
          // res.data.forEach((item) => {
          //   item.childrenList?.forEach((tar) => {
          //     OrgresData.push(tar)
          //   })
          // })
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    selectOrg(e) {
      if (e?.length === 0) {
        this.Adddata.orgCode = null
        this.Adddata.orgId = null
        this.Adddata.orgName = null
      }
      console.log('选择组织选择组织选择组织选择组织', e)
      this.matchItem(this.orgFields.dataSource, e[0])
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.Adddata.orgCode = ele.orgCode
          this.Adddata.orgId = ele.id
          this.Adddata.orgName = ele.orgName
          return
        }
        if (ele.childrenList && ele.childrenList?.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    templateTypeSelect(e) {
      const { itemData } = e
      this.Adddata.templateTypeName = itemData.dictName
    },
    // 工具栏
    handleClickToolBar(item) {
      // let records = item.data ? [item.data] : item.grid.getSelectedRecords()

      let records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (
        records.length <= 0 &&
        (item.toolbar.id == 'effect' ||
          item.toolbar.id == 'loseEffectiveness' ||
          item.toolbar.id == 'delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // if (item.toolbar.id == 'templateDetail') {
      //   // 模板明细
      //   const ids = records.map((e) => e.id)
      //   this.handleTemplateDetail(ids)
      // } else
      if (item.toolbar.id == 'effect') {
        // 生效
        const ids = records.map((e) => e.id)
        this.handleTemplateEffect(ids)
      } else if (item.toolbar.id == 'loseEffectiveness') {
        // 失效
        const ids = records.map((e) => e.id)
        this.handleTemLoseEffectiveness(ids)
      } else if (item.toolbar.id == 'delete') {
        // 删除
        // const ids = records.map((e) => e.id)
        this.handleTemDelete(records)
      } else if (item.toolbar.id == 'templateAdd') {
        // 新增
        this.handleTemplateAdd()
      }
    },
    // 新增
    handleTemplateAdd() {
      // this.$router.push({
      //   path: `/supplier/category-resources/provision-capacity-template-detail`
      // })
      this.show()
    },
    // 模板明细跳转
    handleTemplateDetail(e) {
      this.$router.push({
        path: `/supplier/category-resources/provision-capacity-template-detail`,
        query: {
          id: e.id,
          status: e.status
        }
      })
    },
    // 生效
    handleTemplateEffect(arr) {
      if (arr.length > 1) {
        this.$toast({ content: this.$t('只允许对一条数据进行操作'), type: 'warning' })
        return
      }
      const params = {
        id: arr.toString()
      }
      this.$API.categoryResources.temEffective(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 失效
    handleTemLoseEffectiveness(arr) {
      if (arr.length > 1) {
        this.$toast({ content: this.$t('只允许对一条数据进行操作'), type: 'warning' })
        return
      }
      const params = {
        id: arr.toString()
      }
      this.$API.categoryResources.temInvalid(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除 只有新建可以删除
    handleTemDelete(arr) {
      if (arr.some((e) => e.status === 1 || e.status === 2))
        return this.$toast({ content: this.$t('只有新建状态的数据才可以删除'), type: 'warning' })
      const params = {
        ids: arr.map((e) => e.id)
      }
      this.$API.categoryResources.temDeleteByIds(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 单元格字段点击事件,跳转页面
    handleClickCellTitle(e) {
      if (e.field === 'templateName') {
        this.handleTemplateDetail(e.data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
.perform-box {
  width: 100%;
  position: relative;
  height: 100%;
  /deep/.e-rowcell.sticky-col-0,
  /deep/.e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  /deep/.e-rowcell.sticky-col-1,
  /deep/.e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
  .red {
    background-color: rgba(237, 86, 51, 1);
  }
  .green {
    background-color: rgba(138, 204, 64, 1);
  }
  .yellow {
    background-color: rgba(237, 161, 51, 1);
  }
  .yellowcolor {
    color: rgba(237, 161, 51, 1) !important;
  }
  .bluecolor {
    color: #6386c1 !important;
  }
  .blurbgcolor {
    background-color: #00469c;
  }
  ::v-deep .now-status {
    padding: 3px 5px;
    color: #6386c1;
    background-color: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
  }
  ::v-deep .now-status-stop {
    padding: 3px 5px;
    color: #9a9a9a;
    background-color: rgba(154, 154, 154, 0.1);
    border-radius: 2px;
  }
  ::v-deep .change-status {
    color: #6386c1;
    margin-top: 5px;
    cursor: pointer;
  }
}
::v-deep .dimension-name {
  width: calc((100% - 24px) / 2);
  display: inline-block;
}
::v-deep .dimension-type {
  width: calc((100% - 24px) / 2);
  margin-left: 24px;
  display: inline-block;
}
</style>
