import { i18n } from '@/main.js'
import { ABILITY_TEMPLATE_TYPE } from '../../config'

export const headerColumns = [
  {
    field: 'templateCode',
    title: i18n.t('模板编码'),
    width: 80
  },
  {
    field: 'templateName',
    title: i18n.t('模板名称'),
    editRender: {},
    slots: {
      edit: 'templateNameEdit'
    }
  },
  {
    field: 'templateType',
    title: i18n.t('模板类型'),
    // editRender: {},
    // slots: {
    //   edit: 'templateTypeSelect'
    // },
    formatter: ({ cellValue }) => {
      let item = ABILITY_TEMPLATE_TYPE.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    field: 'dimLevel',
    title: i18n.t('维度层级'),
    width: 80
  },
  // {
  //   field: 'status',
  //   title: i18n.t('状态'),
  //   formatter: (data) => {
  //     if (data.row.status == 0) {
  //       return '新建'
  //     } else if (data.row.status == 1) {
  //       return '生效'
  //     } else if (data.row.status == 2) {
  //       return '失效'
  //     }
  //   }
  // },
  {
    field: 'orgName',
    title: i18n.t('组织范围'),
    editRender: {},
    slots: {
      edit: 'orgSelect'
    },
    width: 200
  },
  // {
  //   field: 'categCodes',
  //   title: i18n.t('应用品类'),
  //   editRender: {},
  //   slots: {
  //     edit: 'categorysSelect'
  //   }
  // },
  // {
  //   field: 'categNames',
  //   title: i18n.t('品类名称')
  // },
  {
    field: 'remark',
    title: i18n.t('备注'),
    editRender: { name: 'input', attrs: { placeholder: '备注' } },
    width: 80
  }
  // {
  //   field: 'createDate',
  //   title: i18n.t('创建日期')
  // },
  // {
  //   field: 'createUserName',
  //   title: i18n.t('创建人')
  // },
  // {
  //   field: 'modifyDate',
  //   title: i18n.t('最后更新日期')
  // }
]

export const dimensionColumns = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    title: i18n.t('层级'),
    type: 'seq',
    width: 50
  },
  // {
  //   field: 'evaluateCycleName',
  //   title: i18n.t('层级'),
  //   editRender: { name: 'input', attrs: { placeholder: '层级' } }
  // },
  {
    field: 'dimensionParam',
    title: i18n.t('维度参数'),
    editRender: {},
    slots: {
      edit: 'dimensionEdit'
    }
  },
  {
    field: 'inputFormat',
    title: i18n.t('输入格式'),
    editRender: {},
    slots: {
      edit: 'inputFormatEdit'
    },
    formatter: (data) => {
      if (data.row.inputFormat == 0) {
        return '字符'
      } else if (data.row.inputFormat == 1) {
        return '文本'
      } else if (data.row.inputFormat == 3) {
        return '日期'
      } else if (data.row.inputFormat == 4) {
        return '数字'
      }
    }
  },
  {
    field: 'inputLength',
    title: i18n.t('输入长度'),
    editRender: {
      name: '$input',
      placeholder: '输入长度',
      immediate: true,
      props: {
        type: 'integer',
        min: 1
      }
    }
  },
  {
    field: 'remark',
    title: i18n.t('备注'),
    editRender: { name: 'input', attrs: { placeholder: '输入备注' } }
  }
]

export const capabilityColumns = [
  {
    type: 'seq',
    width: 50
  },
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'abc',
    title: i18n.t('能力（一阶）'),
    editRender: { name: 'input', attrs: { placeholder: '请输入' } }
  },
  {
    field: 'evaluateYear',
    title: i18n.t('能力（二阶）'),
    editRender: { name: 'input', attrs: { placeholder: '请输入' } }
  },
  {
    field: 'achievementAssessType',
    title: i18n.t('加工特征'),
    editRender: { name: 'input', attrs: { placeholder: '请输入' } }
  },
  {
    field: 'supplierCode',
    title: i18n.t('技术能力规则与标准'),
    editRender: { name: 'input', attrs: { placeholder: '请输入' } }
  },
  {
    field: 'supplierName',
    title: i18n.t('备注')
  }
]

export const categoryColumns = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'categCode',
    title: i18n.t('品类编码'),
    width: 100,
    editRender: {},
    slots: {
      edit: 'categorySelect'
    }
  },
  {
    field: 'categName',
    title: i18n.t('品类名称')
  }
]
export const gradeColumns = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'level',
    title: i18n.t('等级'),
    editRender: {},
    slots: {
      edit: 'levelSelect'
    }
  },
  {
    field: 'definition',
    title: i18n.t('定义'),
    editRender: { name: 'input', attrs: { placeholder: '定义' } }
  },
  {
    field: 'remark',
    title: i18n.t('备注'),
    editRender: { name: 'input', attrs: { placeholder: '输入备注' } }
  }
]
