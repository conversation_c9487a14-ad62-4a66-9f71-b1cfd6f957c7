<template>
  <div>
    <template class="capability">
      <div style="margin-top: 24px" />
      <div style="font-weight: bold">{{ $t('适用品类') }}</div>
      <div class="buttons">
        <vxe-toolbar ref="xToolbar">
          <template #buttons>
            <vxe-button
              size="mini"
              :content="$t('新增')"
              @click="InsertEvent('categoryXTable')"
            ></vxe-button>
            <vxe-button size="mini" :content="$t('删除')" @click="dimensionRemove"></vxe-button>
          </template>
        </vxe-toolbar>
      </div>
      <ScTable
        ref="categoryXTable"
        max-height="300"
        :columns="categoryColumns"
        :table-data="categoryData"
        header-align="center"
        :tree-config="{}"
        align="center"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showUpdateStatus: true,
          showInsertStatus: true
        }"
      >
        <template #categorySelect="{ row }">
          <vxe-select
            ref="select"
            v-model="row.categCode"
            :placeholder="$t('请选择品类')"
            :options="categoryOptions"
            transfer
            filterable
            @change="(value) => categoryChange(value, row)"
          ></vxe-select>
        </template>
      </ScTable>
    </template>
    <div style="margin-top: 24px" />
    <template class="capability">
      <div style="font-weight: bold">{{ $t('等级定义') }}</div>
      <vxe-toolbar ref="xToolbar">
        <template #buttons>
          <vxe-button
            size="mini"
            :content="$t('新增')"
            @click="InsertEvent('levelXTable')"
          ></vxe-button>
          <vxe-button size="mini" :content="$t('删除')" @click="dimensionRemove"></vxe-button>
        </template>
      </vxe-toolbar>
      <ScTable
        ref="levelXTable"
        max-height="300"
        :columns="gradeColumns"
        :table-data="gradeData"
        header-align="center"
        :tree-config="{}"
        align="center"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showUpdateStatus: true,
          showInsertStatus: true
        }"
      >
        <template #levelSelect="{ row }">
          <vxe-select
            v-model="row.level"
            :placeholder="$t('请选择等级')"
            :options="levelOptions"
            transfer
          ></vxe-select>
        </template>
      </ScTable>
    </template>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { categoryColumns, gradeColumns } from './config/detail'
import utils from '@/utils/utils.js'

export default {
  components: {
    ScTable
  },
  props: {
    list: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      categoryColumns,
      categoryData: [],
      gradeColumns,
      gradeData: [],
      categoryOptions: [],
      categoryList: [],
      levelOptions: [
        { label: 'A', value: 'A' },
        { label: 'B', value: 'B' },
        { label: 'C', value: 'C' },
        { label: 'D', value: 'D' }
      ]
    }
  },
  mounted() {
    this.init()
    this.getCategoryList()
  },
  methods: {
    // 处理数据
    init() {
      this.categoryData = this.list.Category
      this.gradeData = this.list.Level
    },
    // 新增
    async InsertEvent(refTem) {
      const newRecord = {}
      await this.$refs[refTem].$refs.xGrid.insert(newRecord)
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.$refs[refTem].$refs.xGrid.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.$refs[refTem].$refs.xGrid.setEditRow(currentViewRecords[0])
      })
    },
    // 临时删除
    async dimensionRemove() {
      await this.$refs.dimensionXTable.$refs.xGrid.removeCheckboxRow()
    },
    // 获取品类列表
    getCategoryList() {
      this.$API.supplierExcept
        .getCategoryList({
          fuzzyNameOrCode: ''
        })
        .then((result) => {
          if (result.code === 200 && !utils.isEmpty(result.data)) {
            this.categoryList = result?.data || []
            this.categoryOptions = this.categoryList.map((i) => {
              return {
                ...i,
                label: `${i.categoryCode}-${i.categoryName}`,
                value: i.categoryCode
              }
            })
          }
        })
    },
    // 选择品类编码 联动品类名称
    categoryChange(val, row) {
      const selectedLabel = this.categoryList.find(
        (option) => option.categoryCode === val.value
      ).categoryName
      this.$set(row, 'categName', selectedLabel)
    }
  }
}
</script>

<style lang="scss" scoped></style>
