<template>
  <div>
    <div class="operate-bar">
      <!-- <span class="op-item" @click="show()">
        {{ $t('点击查看适用品类&定义等级') }}
      </span> -->
      <!-- <span v-if="isNonEffective" class="op-item" @click="hSave">
        {{ $t('保存') }}
      </span> -->
      <vxe-button
        style="margin: 5px 10px 0 0"
        v-if="isNonEffective"
        :loading="saveLoading"
        :content="$t('保存')"
        @click="hSave"
      ></vxe-button>
      <!-- <span class="op-item" @click="hBack">
        {{ $t('返回') }}
      </span> -->
      <vxe-button style="margin: 5px 10px 0 0" :content="$t('返回')" @click="hBack"></vxe-button>
    </div>
    <template class="header">
      <div style="font-weight: bold">{{ $t('模板概要') }}</div>
      <ScTable
        ref="xTable"
        :columns="headerColumns"
        :table-data="headerTableData"
        :is-show-toolbar="false"
        :is-show-right-btn="false"
        header-align="center"
        :auto-height="false"
        :min-height="false"
        height="80"
        :tree-config="null"
        align="center"
        :edit-rules="temValidRules"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showUpdateStatus: true,
          showInsertStatus: true,
          activeMethod: this.activeRowMethod
        }"
        @edit-disabled="editDisabledEvent"
      >
        <template #templateNameEdit="{ row }">
          <vxe-input
            type="text"
            v-model="row.templateName"
            :placeholder="$t('请输入模板名称')"
            @input="validateInput(row, 'templateName')"
          ></vxe-input>
        </template>
        <!-- <template #statusSelect="{ row }">
          <vxe-select
            ref="select"
            v-model="row.status"
            placeholder="请选择状态"
            :options="statusOptions"
            transfer
          ></vxe-select>
        </template> -->
        <template #templateTypeSelect="{ row }">
          <vxe-select
            ref="select"
            v-model="row.templateType"
            :placeholder="$t('请选择模板类型')"
            :options="formattedOptions"
            transfer
            @change="(value) => templateTypeChange(value, row)"
          ></vxe-select>
        </template>
        <template #orgSelect="{ row }">
          <vxe-select
            ref="select"
            v-model="row.orgName"
            :placeholder="$t('请选择组织')"
            :options="orgOptions"
            transfer
            filterable
            @change="(value) => selectOrg(value, row)"
          ></vxe-select>
        </template>
      </ScTable>
    </template>
    <div style="margin-top: 24px" />
    <div style="display: flex">
      <div class="tableArea" style="width: 70%">
        <div class="buttons">
          <span style="font-weight: bold; background-color: #fff; padding: 10px 10px 10px 0">{{
            $t('维度参数定义')
          }}</span>
          <vxe-toolbar ref="xToolbar">
            <template #buttons>
              <vxe-button
                v-if="isEffective"
                size="mini"
                status="primary"
                :content="$t('新增')"
                @click="(row) => InsertEvent(-1, 'dimensionXTable')"
              ></vxe-button>
              <vxe-button
                v-if="isEffective"
                size="mini"
                :content="$t('删除')"
                @click="rowRemove('dimensionXTable')"
              ></vxe-button>
              <vxe-button
                v-if="isEffective"
                size="mini"
                :loading="loading"
                :content="$t('提交')"
                @click="dimensionSubmit"
              ></vxe-button>
            </template>
          </vxe-toolbar>
        </div>
        <ScTable
          ref="dimensionXTable"
          max-height="250"
          :columns="dimensionColumns"
          :table-data="dimensionTableData"
          header-align="center"
          :is-show-toolbar="false"
          :is-show-right-btn="false"
          :tree-config="null"
          align="center"
          :edit-rules="validRules"
          :auto-height="false"
          keep-source
          :edit-config="{
            trigger: 'dblclick',
            mode: 'row',
            showUpdateStatus: true,
            showInsertStatus: true,
            activeMethod: this.activeRowMethod
          }"
          @edit-disabled="editDisabledEvent"
        >
          <template #dimensionEdit="{ row }">
            <vxe-input
              type="text"
              v-model="row.dimensionParam"
              :placeholder="$t('请输入维度参数')"
              @input="validateInput(row, 'dimensionParam')"
            ></vxe-input>
          </template>
          <template #inputFormatEdit="{ row }">
            <vxe-select
              v-model="row.inputFormat"
              :placeholder="$t('请选择维度参数')"
              :options="inputFormatOptions"
              transfer
            ></vxe-select>
          </template>
        </ScTable>
      </div>
      <div class="category" style="width: 30%; margin-left: 24px">
        <div class="buttons">
          <span style="font-weight: bold; background-color: #fff; padding: 10px 10px 10px 0">{{
            $t('适用品类')
          }}</span>
          <vxe-toolbar ref="xToolbar">
            <template #buttons>
              <vxe-button
                v-if="isNonEffective"
                size="mini"
                status="primary"
                :content="$t('新增')"
                @click="(row) => InsertEvent(0, 'categoryXTable')"
              ></vxe-button>
              <vxe-button
                v-if="isNonEffective"
                size="mini"
                :content="$t('删除')"
                @click="rowRemove('categoryXTable')"
              ></vxe-button>
            </template>
          </vxe-toolbar>
        </div>
        <ScTable
          ref="categoryXTable"
          max-height="250"
          :columns="categoryColumns"
          :table-data="categoryData"
          header-align="center"
          :is-show-toolbar="false"
          :is-show-right-btn="false"
          :auto-height="false"
          :tree-config="{}"
          align="center"
          :edit-config="{
            trigger: 'dblclick',
            mode: 'row',
            showUpdateStatus: true,
            showInsertStatus: true,
            activeMethod: this.activeRowMethod
          }"
          @edit-disabled="editDisabledEvent"
        >
          <template #categorySelect="{ row }">
            <vxe-select
              ref="select"
              v-model="row.categCode"
              :placeholder="$t('请选择品类')"
              :options="categoryOptions"
              transfer
              filterable
              @change="(value) => categoryChange(value, row)"
            ></vxe-select>
          </template>
        </ScTable>
      </div>
    </div>
    <div style="margin-top: 24px" />
    <div style="display: flex">
      <div v-if="capabilityIsShow" class="tableArea" style="width: 70%">
        <div class="buttons">
          <span style="font-weight: bold; background-color: #fff; padding: 10px 10px 10px 0">{{
            $t('能力明细')
          }}</span>
          <vxe-toolbar ref="xToolbar">
            <template #buttons>
              <vxe-button
                v-if="isNonEffective"
                size="mini"
                status="info"
                :content="$t('导入')"
                @click="capabilityImport"
              ></vxe-button>
              <vxe-button
                v-if="isNonEffective"
                size="mini"
                status="primary"
                :content="$t('新增')"
                @click="(row) => InsertEvent(0, 'capabilityXTable')"
              ></vxe-button>
              <vxe-button
                v-if="isNonEffective"
                size="mini"
                :content="$t('删除')"
                @click="rowRemove('capabilityXTable')"
              ></vxe-button>
            </template>
          </vxe-toolbar>
        </div>
        <ScTable
          ref="capabilityXTable"
          max-height="250"
          :columns="capabilityColumns"
          :table-data="capabilityTableData"
          header-align="center"
          :tree-config="null"
          :auto-height="false"
          :is-show-toolbar="false"
          :is-show-right-btn="false"
          align="center"
          :row-config="{ isCurrent: true, isHover: true }"
          keep-source
          :edit-config="{
            trigger: 'dblclick',
            mode: 'row',
            showUpdateStatus: true,
            showInsertStatus: true,
            activeMethod: this.activeRowMethod
          }"
          @edit-disabled="editDisabledEvent"
        >
        </ScTable>
      </div>
      <div v-else-if="!capabilityIsShow" class="tableArea" style="width: 70%"></div>
      <div class="level" style="width: 30%; margin-left: 24px">
        <div class="buttons">
          <span style="font-weight: bold; background-color: #fff; padding: 10px 10px 10px 0">{{
            $t('等级定义')
          }}</span>
          <vxe-toolbar ref="xToolbar">
            <template #buttons>
              <vxe-button
                v-if="isNonEffective"
                size="mini"
                status="primary"
                :content="$t('新增')"
                @click="(row) => InsertEvent(0, 'levelXTable')"
              ></vxe-button>
              <vxe-button
                v-if="isNonEffective"
                size="mini"
                :content="$t('删除')"
                @click="rowRemove('levelXTable')"
              ></vxe-button>
            </template>
          </vxe-toolbar>
        </div>
        <ScTable
          ref="levelXTable"
          max-height="250"
          :columns="gradeColumns"
          :table-data="gradeData"
          header-align="center"
          :auto-height="false"
          :is-show-toolbar="false"
          :is-show-right-btn="false"
          :tree-config="{}"
          align="center"
          :edit-config="{
            trigger: 'dblclick',
            mode: 'row',
            showUpdateStatus: true,
            showInsertStatus: true,
            activeMethod: this.activeRowMethod
          }"
          @edit-disabled="editDisabledEvent"
        >
          <template #levelSelect="{ row }">
            <vxe-select
              v-model="row.level"
              :placeholder="$t('请选择等级')"
              :options="levelOptions"
              transfer
            ></vxe-select>
          </template>
        </ScTable>
      </div>
    </div>
    <!-- 查看适用品类&定义等级弹窗 -->
    <!-- <mt-dialog
      ref="dialog"
      v-if="dialogVisible"
      css-class="index-dialog"
      header="查看"
      :buttons="buttons"
      @close="hide"
      :open="onOpen"
      :position="{ X: 'right', Y: 'top' }"
      height="100%"
      width="60%"
    >
      <categoryGrade ref="categoryGrade" :list="CategoryLevelList"></categoryGrade>
    </mt-dialog> -->
  </div>
</template>

<script>
import Vue from 'vue'
import ScTable from '@/components/ScTable/src/index'
import { headerColumns, dimensionColumns, categoryColumns, gradeColumns } from './config/detail'
// import categoryGrade from './categoryGrade.vue'
import { cloneDeep } from 'lodash'
import utils from '@/utils/utils.js'
import { ABILITY_TEMPLATE_TYPE } from '../config'
import { Pulldown } from 'vxe-table'
Vue.use(Pulldown)

export default {
  components: {
    ScTable
    // categoryGrade
  },
  data() {
    return {
      saveLoading: false,
      loading: false,
      isNonEffective: true, // 当前是否失效数据
      isEffective: true, // 当前是否生效数据
      orgOptions: [],
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      treeConfig: {
        transform: true,
        rowField: 'id',
        parentField: 'parentOrgId'
      },
      columns: [
        {
          field: 'orgName',
          title: this.$t('组织'),
          width: 360,
          treeNode: true
        }
      ],
      tableData: [],
      tableColumn: [],
      ABILITY_TEMPLATE_TYPE,
      headerColumns, // 头部模板概要 - 表头
      dimensionColumns,
      capabilityColumns: [],
      siteOptions: [],
      headerTableData: [], // 头部模板概要 - 数据
      dimensionTableData: [], // 头部模板概要 - 数据
      capabilityTableData: [], // 头部模板概要 - 数据
      dialogVisible: false, // 弹窗控制数据
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      // 弹窗所需数据
      CategoryLevelList: {
        Category: [],
        Level: []
      },
      statusOptions: [
        { value: 0, label: this.$t('新建') },
        { value: 1, label: this.$t('生效') },
        { value: 2, label: this.$t('失效') }
      ],
      // 输入格式枚举
      inputFormatOptions: [
        { value: 0, label: this.$t('字符') },
        { value: 1, label: this.$t('文本') },
        { value: 3, label: this.$t('日期') },
        { value: 4, label: this.$t('数字') }
      ],
      categoryColumns,
      categoryData: [],
      gradeColumns,
      gradeData: [],
      categoryOptions: [],
      categoryList: [],
      levelOptions: [
        { label: 'A', value: 'A' },
        { label: 'B', value: 'B' },
        { label: 'C', value: 'C' },
        { label: 'D', value: 'D' }
      ],
      capabilityIsShow: false,
      validRules: {
        dimensionParam: [{ required: true, message: this.$t('维度参数') }],
        inputFormat: [{ required: true, message: this.$t('输入格式填写') }],
        inputLength: [
          { required: true, message: this.$t('输入长度填写') },
          { min: 0, message: this.$t('长度须大于0') }
        ]
      },
      temValidRules: {
        templateName: [{ required: true, message: this.$t('模板名称必填') }],
        templateType: [{ required: true, message: this.$t('模板类型必填') }],
        status: [{ required: true, message: this.$t('状态必填') }],
        orgName: [{ required: true, message: this.$t('组织范围必填') }],
        categCodes: [{ required: true, message: this.$t('应用品类必填') }]
      }
    }
  },
  computed: {
    formattedOptions() {
      // 通过对选项数组进行映射，将指定字段设置为 label
      return this.ABILITY_TEMPLATE_TYPE.map((option) => ({
        value: option.dictCode,
        label: option.dictName
      }))
    }
  },
  created() {},
  mounted() {
    if (this.$route.query.id) {
      // 判断当前模板类型为选型沙盘or能力地图
      this.detailInit()
      const detailShowRows = [
        {
          field: 'status',
          title: this.$t('状态'),
          width: 70,
          formatter: (data) => {
            if (data.row.status == 0) {
              return this.$t('新建')
            } else if (data.row.status == 1) {
              return this.$t('生效')
            } else if (data.row.status == 2) {
              return this.$t('失效')
            }
          }
        },
        {
          field: 'createDate',
          title: this.$t('创建日期')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人'),
          width: 80
        },
        {
          field: 'modifyDate',
          title: this.$t('最后更新日期')
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人'),
          width: 80
        }
      ]
      if (
        this.headerColumns.every(
          (item) =>
            item.field !== 'status' &&
            item.field !== 'createDate' &&
            item.field !== 'createUserName' &&
            item.field !== 'modifyDate'
        )
      ) {
        this.headerColumns.push(...detailShowRows)
      }
      if (this.$route.query.status === '2') {
        // 当前是失效的数据
        this.isNonEffective = false
      }
      if (this.$route.query.status === '2' || this.$route.query.status === '1') {
        this.isEffective = false
      }
    } else {
      this.headerTableData = [{ templateCode: null, templateName: null }]
    }
    this.getCategoryList()
    this.getOrgList() //获取组织下拉数据
  },
  methods: {
    // 输入空格校验
    validateInput(row, value) {
      row[value] = row[value].replace(/\s+/g, '')
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.OrgresData = []
          res.data.forEach((item) => {
            this.OrgresData = this.recursionItem(item, this.OrgresData)
          })
          this.orgOptions = this.OrgresData.map((option) => ({
            value: option.orgCode,
            label: `${option.orgCode}-${option.orgName}`
          }))
          // this.tableData = this.flatTableData(res.data || [])
        }
      })
    },
    flatTableData(list) {
      let arr = []
      list.forEach((item) => {
        arr = arr.concat(item.childrenList || [])
      })
      const res = arr.concat(list)
      res.forEach((item) => {
        item.codeAndName = item.orgCode + ' ' + item.orgName
      })
      return res
    },
    cellClickEvent({ row }) {
      const $pulldown = this.$refs.pulldownRef
      if ($pulldown) {
        this.searchName = row.orgName
        $pulldown.hidePanel()
      }
    },
    // 递归组织
    recursionItem(item, resData) {
      if (item) {
        resData.push(item)
        item.childrenList?.forEach((tar) => {
          resData = this.recursionItem(tar, resData)
        })
      }
      return resData
    },
    selectOrg(val, row) {
      const selectedCode = this.OrgresData.find((option) => option.orgCode === val.value).orgName
      const selectedId = this.OrgresData.find((option) => option.orgCode === val.value).id

      this.$set(row, 'orgCode', val.value)
      this.$set(row, 'orgName', selectedCode)
      this.$set(row, 'orgId', selectedId)
    },
    // 明细页面 - 各个表格数据渲染
    detailInit() {
      const param = {
        id: this.$route.query.id
      }
      this.$API.categoryResources.getTemDetail(param).then((res) => {
        if (res.code == 200) {
          const temProfile = {
            categCodes: res.data.categCodes,
            categNames: res.data.categNames,
            createDate: res.data.createDate,
            createUserName: res.data.createUserName,
            dimLevel: res.data.dimLevel,
            id: res.data.id,
            modifyDate: res.data.modifyDate,
            orgCode: res.data.orgCode,
            orgId: res.data.orgId,
            orgName: res.data.orgName,
            remark: res.data.remark,
            status: res.data.status,
            templateCode: res.data.templateCode,
            templateName: res.data.templateName,
            templateType: res.data.templateType,
            templateTypeName: res.data.templateTypeName,
            updateUserName: res.data.updateUserName
          }
          // 判断当前模板类型
          this.judgeCurTemplateType(
            res.data.templateType,
            res.data.buyerSupplierAbilityDimParamList
          )
          // 模板概要数据
          this.headerTableData.push(temProfile)
          this.headerTableData.forEach((item) => {
            item.dimLevel =
              res.data.templateType === 'SELECT_SANDBOX'
                ? '1'
                : res.data.buyerSupplierAbilityDimParamList.length
          })
          // 维度参数定义数据
          this.dimensionColumnsData = res.data.buyerSupplierAbilityDimParamList
          this.dimensionTableData = res.data.buyerSupplierAbilityDimParamList
          // 能力明细
          this.temCapabilityColumnsData = cloneDeep(
            res.data.buyerSupplierAbilityDimParamDetailResponseList
          )
          this.getCapabilityColumns(res.data.buyerSupplierAbilityDimParamDetailResponseList)
          if (res.data.buyerSupplierAbilityDimParamDetailResponseList?.length) {
            this.capabilityIsShow = true
          }

          // 适用品类、等级定义
          this.categoryData = res.data.buyerSupplierAbilityCategoryList
          this.gradeData = res.data.buyerSupplierAbilityLevelDefinitionList
        }
      })
    },
    // 判断当前模板类型
    judgeCurTemplateType(tempType, dimTableData) {
      // SELECT_SANDBOX 选型沙盘
      if (tempType === 'SELECT_SANDBOX') {
        this.dimensionColumns = this.dimensionColumns.filter((item) => item?.type !== 'seq')
        this.dimensionColumns.splice(1, 0, {
          field: 'indexLine',
          title: this.$t('层级')
        })
        console.log('this.dimensionTableDatathis.dimensionTableData', dimTableData)
        dimTableData = dimTableData.map((e) => {
          e.indexLine = 1
          return e
        })
      }
    },
    // 能力明细表头渲染
    getCapabilityColumns(columns) {
      if (columns && columns.length > 0) {
        this.capabilityColumns.push({
          type: 'checkbox',
          width: '60'
        })
        columns[0]?.arrays?.forEach((tar) => {
          let b = {}
          b.field = tar.fieldValue
          b.title = this.$t(tar.fieldName)
          b.editRender = { name: 'input', attrs: { placeholder: this.$t('请输入') } }
          this.capabilityColumns.push(b)
        })
        this.capabilityColumns.push({
          field: 'remark',
          title: this.$t('备注'),
          editRender: { name: 'input', attrs: { placeholder: this.$t('备注') } },
          width: 80
        })
      }
      columns?.forEach((item) => {
        let a = {}
        item.arrays?.forEach((tar) => {
          a[tar.fieldValue] = tar.paramValue
        })
        a.remark = item.remark
        this.capabilityTableData.push(a)
      })
    },
    onOpen(args) {
      args.preventFocus = true
    },
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.show()
      })
    },
    hide() {
      this.dialogVisible = false
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.hide()
      })
    },
    // 禁用失效的数据
    activeRowMethod() {
      if (this.$route.query.status === '2') {
        return false
      }
      return true
    },
    // 禁用编辑行的提示
    editDisabledEvent() {
      // const status = this.$route.query.status
      this.$toast({
        content: this.$t('失效单据的数据不可修改'),
        type: 'warning'
      })
    },
    // 新增
    async InsertEvent(row, refTem) {
      const newRecord = {}
      await this.$refs[refTem].$refs.xGrid.insertAt(newRecord, row)
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.$refs[refTem].$refs.xGrid.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        if (row === -1) {
          this.$refs[refTem].$refs.xGrid.setEditRow(
            currentViewRecords[currentViewRecords.length - 1]
          )
        } else if (row === 0) {
          this.$refs[refTem].$refs.xGrid.setEditRow(currentViewRecords[0])
        }

        if (refTem === 'dimensionXTable') {
          currentViewRecords[currentViewRecords.length - 1].indexLine = 1
        }
      })
    },
    // 临时删除
    async rowRemove(ref) {
      await this.$refs[ref].$refs.xGrid.removeCheckboxRow()
    },
    // 提交
    async dimensionSubmit() {
      const $table = this.$refs.dimensionXTable.$refs.xGrid
      const { insertRecords, removeRecords } = $table.getRecordset()
      // 获取维度参数当前表格数据
      const dimParamListData = this.$refs?.dimensionXTable?.$refs?.xGrid.getTableData().tableData
      if (dimParamListData.length <= 0)
        return this.$toast({
          content: this.$t('请确保维度参数表格内至少有一条数据，再提交！'),
          type: 'warning'
        })
      // 数据必填校验
      const errMap = await $table.validate().catch((errMap) => errMap)
      if (errMap) {
        this.$toast({ content: this.$t('维度参数校验必填项不通过！'), type: 'warning' })
        return
      }

      if (this.dimensionTableData) {
        const dimParamRequests = []
        const fieldValueNameObj = {}
        let levelTp = 1
        this.dimensionTableData.forEach((item) => {
          if (removeRecords.every((tar) => item.fieldName !== tar.fieldName)) {
            const tar = {}
            tar.dimParamDetailRequestList = []
            const pp = {}
            pp.arrays = []
            fieldValueNameObj['fld' + levelTp] = item.dimensionParam
            pp.arrays.push({
              fieldName: item.dimensionParam,
              fieldValue: 'fld' + levelTp,
              paramValue: ''
            })
            pp.remark = item.remark
            pp.templateId = item.templateId
            this.dimensionTemplateId = item.templateId
            tar.dimParamDetailRequestList.push(pp)
            tar.dimensionParam = item.dimensionParam
            tar.fieldName = 'fld' + levelTp
            tar.inputFormat = item.inputFormat
            tar.inputLength = item.inputLength
            tar.level = levelTp
            levelTp += 1
            tar.remark = item.remark
            tar.templateId = item.templateId
            dimParamRequests.push(tar)
          }
        })
        for (let i = insertRecords.length - 1; i >= 0; i--) {
          const item = insertRecords[i]
          const tar = {}
          tar.dimParamDetailRequestList = []
          const pp = {}
          pp.arrays = []
          pp.arrays.push({
            fieldName: item.dimensionParam,
            fieldValue: 'fld' + levelTp,
            paramValue: ''
          })
          pp.remark = item.remark
          // pp.templateId = this.dimensionTemplateId
          pp.templateId = this.headerTableData[0].id
          tar.dimParamDetailRequestList.push(pp)
          tar.dimensionParam = item.dimensionParam
          tar.fieldName = 'fld' + levelTp
          tar.inputFormat = item.inputFormat
          tar.inputLength = item.inputLength
          tar.level = levelTp
          levelTp += 1
          tar.remark = item.remark
          // tar.templateId = this.dimensionTemplateId
          tar.templateId = this.headerTableData[0].id

          dimParamRequests.push(tar)
        }

        const dimParamDetailRequests = this.$refs?.capabilityXTable?.$refs.xGrid
          .getTableData()
          .fullData.map((item) => {
            levelTp = 0
            let newItem = {}
            newItem.arrays = []
            for (let key in item) {
              if (
                key !== 'id' &&
                key !== 'remark' &&
                removeRecords.every((att) => att.fieldName !== key)
              ) {
                levelTp += 1
                newItem.arrays.push({
                  fieldName: fieldValueNameObj['fld' + levelTp],
                  fieldValue: 'fld' + levelTp,
                  paramValue: item[key]
                })
                newItem.remark = item.remark
              }
            }
            for (let i = insertRecords.length - 1; i >= 0; i--) {
              const arr = insertRecords[i]
              levelTp += 1
              newItem.arrays.push({
                fieldName: arr.dimensionParam,
                fieldValue: 'fld' + levelTp,
                paramValue: ''
              })
            }
            return newItem
          })
        const params = {
          dimParamDetailRequests,
          dimParamRequests
        }
        this.loading = true
        this.$API.categoryResources
          .dimParamSubmit(params)
          .then((res) => {
            if (res.code === 200) {
              this.capabilityColumns = []
              this.capabilityTableData = []
              this.dimensionTableData = []
              this.capabilityIsShow = true
              // 重新渲染‘能力明细’表格的表头
              this.getCapabilityColumns(res.data?.dimParamDetailRequests)
              this.dimensionTableData = res.data.dimParamRequests
              // 维度参数‘层级’字段添加默认值(选型沙盘才需要)
              if (this.headerTableData[0].templateType === 'SELECT_SANDBOX') {
                this.dimensionTableData = this.dimensionTableData.map((e) => {
                  e.indexLine = 1
                  return e
                })
              }
              console.log('this.dimensionTableDatathis.dimensionTableData', this.dimensionTableData)
              // this.$set(this.headerTableData, 'dimLevel', res.data.dimParamRequests.length)
              this.headerTableData?.forEach((item) => {
                item.dimLevel =
                  this.headerTableData[0].templateType === 'SELECT_SANDBOX'
                    ? '1'
                    : res.data.dimParamRequests.length
              })
              this.loading = false
            }
          })
          .catch((err) => {
            console.log(err)
            this.loading = false
          })
      }
    },

    // 能力明细导入
    capabilityImport() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          asyncParams: {
            templateId: this.dimensionTableData[0].templateId
          },
          downloadTemplateParams: { id: this.$route.query.id },
          importApi: this.$API.categoryResources.dimParamImport,
          downloadTemplateApi: this.$API.categoryResources.dimParamExport
        },
        success: (data) => {
          // 重新渲染‘能力明细’表格的表头
          this.capabilityColumns = []
          this.capabilityTableData = []
          this.getCapabilityColumns(data)
        }
      })
    },
    // 获取品类列表
    getCategoryList() {
      this.$API.supplierExcept
        .getCategoryList({
          fuzzyNameOrCode: ''
        })
        .then((result) => {
          if (result.code === 200 && !utils.isEmpty(result.data)) {
            this.categoryList = result?.data || []
            this.categoryOptions = this.categoryList.map((i) => {
              return {
                ...i,
                label: `${i.categoryCode}-${i.categoryName}`,
                value: i.categoryCode
              }
            })
          }
        })
    },
    // 选择品类编码 联动品类名称
    categoryChange(val, row) {
      const selectedLabel = this.categoryList.find(
        (option) => option.categoryCode === val.value
      ).categoryName
      this.$set(row, 'categName', selectedLabel)
    },
    // 模板品类选择
    temCategorysChange(val, row) {
      const selectedLabel = this.categoryList.find(
        (option) => option.categoryCode === val.value
      ).categoryName
      this.$set(row, 'categNames', selectedLabel)
    },
    templateTypeChange(val, row) {
      const selectedLabel = this.ABILITY_TEMPLATE_TYPE.find(
        (option) => option.dictCode === val.value
      ).dictName
      this.$set(row, 'templateTypeName', selectedLabel)
    },
    // 保存
    hSave() {
      const $table = this.$refs.dimensionXTable.$refs.xGrid
      const { insertRecords, removeRecords, updateRecords } = $table.getRecordset()
      if (insertRecords.length > 0 || removeRecords.length > 0 || updateRecords.length > 0) {
        this.$toast({ content: this.$t('请先提交维度参数的数据，再保存'), type: 'warning' })
        return
      }
      // 判断 品类 & 等级 table的空值
      const $categoryTable = this.$refs?.categoryXTable?.$refs?.xGrid.getTableData().tableData // 品类
      const $levelTable = this.$refs?.levelXTable?.$refs?.xGrid.getTableData().tableData // 等级

      const categoryRequests = $categoryTable.filter((item) => {
        return item?.categCode !== null || item?.categName !== null
      })
      const levelDefinitionRequests = $levelTable.filter((item) => {
        return item?.definition !== null || item?.level !== null || item?.remark !== null
      })

      const abilityDimParamRequests =
        this.$refs?.dimensionXTable?.$refs?.xGrid.getTableData().tableData // 维度参数
      const templateRequests = this.$refs?.xTable.$refs.xGrid.getTableData().tableData // 模板

      const dimParamDetailRequests = [] // 能力明细数据
      const capabilityColumns = {}

      this.$refs?.capabilityXTable?.$refs.xGrid.columns.forEach((item) => {
        capabilityColumns[item.field] = item.title
      })
      this.$refs?.capabilityXTable?.$refs.xGrid.getTableData().fullData.forEach((item) => {
        const dimParamDetailRequestsData = {}
        dimParamDetailRequestsData.arrays = []
        for (let key in item) {
          if (key !== 'id' && key !== 'remark') {
            dimParamDetailRequestsData.arrays.push({
              fieldName: capabilityColumns[key],
              fieldValue: key,
              paramValue: item[key] ? item[key] : ''
            })
          }
        }
        dimParamDetailRequestsData.remark = item.remark
        dimParamDetailRequestsData.templateId = this.dimensionTableData[0].templateId
        if (dimParamDetailRequestsData.arrays.some((item) => item.paramValue !== '')) {
          dimParamDetailRequests.push(dimParamDetailRequestsData)
        }
        // dimParamDetailRequests.filter((item) => {
        //   return item.arrays.some((tar) => {
        //     return tar.paramValue !== ''
        //   })
        // })
      })
      const params = {
        abilityDimParamRequests,
        categoryRequests,
        levelDefinitionRequests,
        dimParamDetailRequests,
        ...templateRequests[0]
      }
      params.dimLevel =
        templateRequests[0].templateType === 'SELECT_SANDBOX' ? '1' : abilityDimParamRequests.length
      if (!this.$route.query.id) delete params.id
      this.saveLoading = true

      this.$API.categoryResources
        .abilitySaveOrUpdate(params)
        .then((res) => {
          console.log('dimParamSubmitdimParamSubmit5678', res)
          if (res.code === 200) {
            this.$toast({ content: this.$t('数据保存成功'), type: 'success' })
            this.saveLoading = false
            this.$router.go(-1)
          }
        })
        .catch((err) => {
          console.log(err)
          this.saveLoading = false
        })
    },
    // 返回
    hBack() {
      this.$router.go(-1)
    },
    focusEvent() {
      const $pulldown = this.$refs.pulldownRef
      if ($pulldown) {
        $pulldown.showPanel()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.operate-bar {
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: end;
  align-items: center;
  color: #4f5b6d;
  .title-left {
    font-weight: 700;
    font-size: 18px;
  }
  .op-item {
    cursor: pointer;
    align-items: center;
    margin-right: 20px;
    align-items: center;
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: #00469c;
  }
}
/deep/ .e-dialog .e-dlg-header-content + .e-dlg-content {
  padding-top: 20px;
}
.tableArea {
  background-color: #fff;
}

.buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  background-color: #fff;
}
.title {
  display: inline-block;
  font-weight: bold;
  background-color: #fff;
}

.my-dropdown4 {
  width: 370px;
  height: auto;
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}
</style>
