// 认证需求
<template>
  <mt-dialog ref="dialog" height="650" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <ScTable
        ref="detailXTable"
        :columns="detailCols"
        :table-data="detailTableData"
        header-align="center"
        :tree-config="null"
        :auto-height="false"
        :is-show-toolbar="false"
        align="center"
        :row-config="{ isCurrent: true, isHover: true, keyField: 'dtlId' }"
        :checkbox-config="{
          checkRowKeys: selectRowsId,
          reserve: true
        }"
      >
      </ScTable>
    </div>
  </mt-dialog>
</template>
<script>
// import utils from '@/utils/utils'
// import { cloneDeep } from 'lodash'
import ScTable from '@/components/ScTable/src/index'

export default {
  components: { ScTable },
  data() {
    return {
      detailCols: [],
      detailTableData: [],
      detailResponseList: JSON.parse(sessionStorage.getItem('detailResponseList'))
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    detailResponseList: {
      handler(info) {
        this.getDetailCols(info)
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    tableRef() {
      return this.$refs.detailXTable.$refs.xGrid
    },
    buttons() {
      return [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    },
    selectRowsId() {
      let sameIdList = []
      this.modalData.dataInfo.forEach((item) => {
        if (this.detailTableData.some((tar) => tar.dtlId === item.dtlId)) {
          sameIdList.push(item.dtlId)
        }
      })
      return sameIdList
    }
  },
  async created() {},
  mounted() {
    this.show()
  },
  methods: {
    // 填充详情表信息
    getDetailCols(columns) {
      if (columns && columns.length > 0) {
        this.detailCols.push({
          type: 'checkbox',
          width: '60'
        })
        columns[0]?.arrays.forEach((tar) => {
          let b = {}
          b.field = tar.fieldValue
          b.title = this.$t(tar.fieldName)
          this.detailCols.push(b)
        })
      }
      columns?.forEach((item) => {
        let a = {}
        item.arrays?.forEach((tar) => {
          a[tar.fieldValue] = tar.paramValue
          a.dtlId = tar.id
        })
        this.detailTableData.push(a)
      })
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      let selectRecords = this.tableRef.getCheckboxRecords()
      if (selectRecords.length === 0)
        return this.$toast({
          content: this.$t('请至少选择一条数据'),
          type: 'warning'
        })
      console.log('selectRecordsselectRecords', selectRecords)
      this.$emit('confirm-function', selectRecords)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-rich-text-editor {
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    height: 170px !important;
    padding: 0px 7px;
    em {
      font: revert;
    }
    ol,
    ul {
      list-style: revert;
    }
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.form-item-spec {
  width: 414px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .full-width {
  width: 100% !important;
}
</style>
<style lang="scss">
.d72562-c40d-4933-9a24-98c3298365ac {
  li {
    display: flex;
    align-items: center;
    & > div {
      flex-shrink: 0;
    }
    & > div:first-child {
      margin-left: 20px;
    }
  }
}
</style>
