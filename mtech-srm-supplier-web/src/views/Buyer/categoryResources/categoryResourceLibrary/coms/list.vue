<template>
  <div class="list-content">
    <div v-if="capabilityIsShow" class="tableArea">
      <div class="buttons">
        <vxe-button
          size="mini"
          status="primary"
          :content="$t('增加')"
          @click="handleAddInfo"
        ></vxe-button>
        <vxe-button size="mini" :content="$t('清除')" @click="handleDelete"></vxe-button>
        <vxe-button
          style="float: right"
          size="mini"
          status="primary"
          :content="$t('查询')"
          :loading="loading"
          @click="handleListSearch"
        ></vxe-button>
      </div>
      <ScTable
        ref="capabilityXTable"
        height="100%"
        :columns="categoryCols"
        :table-data="categoryTableData"
        header-align="center"
        :tree-config="null"
        :auto-height="false"
        :is-show-toolbar="false"
        align="center"
        :row-config="{ isCurrent: true, isHover: true }"
        keep-source
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          showUpdateStatus: true,
          showInsertStatus: true
        }"
      >
      </ScTable>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'

export default {
  components: {
    ScTable
  },
  props: {
    // 动态表的填充数据
    listInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      capabilityIsShow: true,
      categoryCols: [],
      categoryTableData: []
    }
  },
  watch: {
    listInfo: {
      handler(info) {
        this.categoryCols = []
        this.categoryTableData = []
        const {
          buyerSupplierAbilityDimParamResponseList,
          buyerSupplierAbilityDimParamDetailResponseList
        } = info
        if (
          buyerSupplierAbilityDimParamResponseList &&
          buyerSupplierAbilityDimParamDetailResponseList
        ) {
          sessionStorage.setItem(
            'detailResponseList',
            JSON.stringify(buyerSupplierAbilityDimParamDetailResponseList)
          )
          // 填充页面展示表头
          this.getcategoryCols(buyerSupplierAbilityDimParamResponseList)
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    tableRef() {
      return this.$refs.capabilityXTable.$refs.xGrid
    },
    markOptions() {
      const { buyerSupplierAbilityLevelDefinitionResponses } = this.listInfo
      buyerSupplierAbilityLevelDefinitionResponses.forEach((item) => {
        item['label'] = item.level
        item['value'] = item.level
      })
      return buyerSupplierAbilityLevelDefinitionResponses
    }
  },
  mounted() {},
  methods: {
    // 能力明细/ 选型沙盘表头渲染
    getcategoryCols(columns) {
      if (columns && columns.length > 0) {
        columns?.forEach((tar) => {
          let b = {}
          b.field = tar.fieldName
          b.title = this.$t(tar.dimensionParam)
          this.categoryCols.push(b)
        })
      }
    },
    async handleDelete() {
      await this.tableRef.removeCheckboxRow()
    },
    handleAddInfo() {
      const curCategoryTableData = this.tableRef.getTableData().fullData
      this.$dialog({
        modal: () => import('./addListInfo'),
        data: {
          title: this.$t('选择能力参数'),
          dataInfo: curCategoryTableData || null
        },
        success: (data) => {
          this.categoryTableData = data
          const checkbox = {
            type: 'checkbox',
            width: '60'
          }
          // const screenLevel = {
          //   field: 'level',
          //   title: this.$t('筛选等级'),
          //   editRender: {},
          //   slots: {
          //     edit: ({ row }) => {
          //       return [
          //         <div>
          //           <vxe-select
          //             v-model={row.level}
          //             placeholder={`请选择等级`}
          //             options={this.markOptions}
          //             option-props={{ label: 'label', value: 'value' }}
          //             transfer></vxe-select>
          //         </div>
          //       ]
          //     }
          //   }
          // }
          const screenLevel = {
            field: 'level',
            title: this.$t('筛选等级'),
            editRender: {
              name: 'select',
              options: this.markOptions,
              optionProps: { label: 'label', value: 'value' },
              transfer: true
            },
            formatter: ({ cellValue }) => {
              const selectedOption = this.markOptions.find((option) => option.value === cellValue)
              return selectedOption ? selectedOption.label : ''
            }
          }
          const includesObj1 = this.categoryCols.some(
            (item) => item.type === checkbox.type && item.width === checkbox.width
          )
          const includesObj2 = this.categoryCols.some(
            (item) => item.field === screenLevel.field && item.title === screenLevel.title
          )
          if (!includesObj1) {
            this.categoryCols.unshift(checkbox)
          }
          if (!includesObj2) {
            this.categoryCols.push(screenLevel)
          }
          let levelChar = 'A'
          this.markOptions.forEach((item) => {
            if (item?.level <= levelChar) {
              levelChar = item?.level
            }
          })
          console.log('asdasd', this.categoryTableData)
          this.categoryTableData.forEach((e) => {
            e.level = levelChar
          })
        }
      })
    },
    // 主页搜索
    handleListSearch() {
      const dataInfo = this.tableRef.getTableData().fullData
      if (dataInfo.length === 0)
        return this.$toast({
          content: this.$t('请先设置搜索条件'),
          type: 'warning'
        })
      for (let i = 0; i < dataInfo.length; i++) {
        if (!dataInfo[i].level) {
          this.$toast({
            content: this.$t(`请填写第${i + 1}行等级`),
            type: 'warning'
          })
          return
        }
      }
      const params = {
        baseConditionList: dataInfo,
        categCode: this.$parent.formInfo.categCode,
        categName: this.$parent.formInfo.categName
      }
      sessionStorage.setItem('CategoryQueryByCondition', JSON.stringify(params))
      this.loading = true
      this.$API.categoryResources
        .getCategoryQueryByCondition(params)
        .then((res) => {
          this.loading = false
          if (res.code === 200) {
            // 检查是否所有返回的值都为 null
            const isAllNull = Object.values(res.data).every((value) => value === null)
            if (isAllNull) {
              this.$toast({
                content: this.$t('暂无符合条件的供应商'),
                type: 'warning'
              })
            } else {
              sessionStorage.setItem('categoryResourceDetail', JSON.stringify(res.data))
              this.$router.push({ name: 'category-resource-library-detail' })
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.list-content {
  margin: 24px;
  .buttons {
    margin-bottom: 24px;
  }
}
</style>
