<template>
  <div>
    <div class="operate-bar">
      <vxe-button
        style="margin: 5px 10px 0 0"
        status="info"
        :content="$t('导出')"
        size="mini"
        @click="hExport"
      ></vxe-button>
      <vxe-button
        style="margin: 5px 10px 0 0"
        status="primary"
        :content="$t('查看明细')"
        size="mini"
        @click="handleReadMore"
      ></vxe-button>
      <vxe-button
        size="mini"
        style="margin: 5px 10px 0 0"
        :content="$t('返回')"
        @click="hBack"
      ></vxe-button>
    </div>
    <div style="margin-top: 10px" />
    <template class="header">
      <ScTable
        ref="xTable"
        :columns="headerColumns"
        :table-data="headerTableData"
        :is-show-right-btn="false"
        :is-show-toolbar="false"
        :auto-height="false"
        :min-height="false"
        height="80"
        header-align="center"
        :tree-config="null"
        align="center"
      >
      </ScTable>
    </template>
    <div style="margin-top: 24px" />
    <div class="tableArea">
      <ScTable
        ref="markXTable"
        :columns="supplierColumns"
        :table-data="supplierTableData"
        header-align="center"
        :tree-config="null"
        :auto-height="false"
        height="300"
        align="center"
        show-header-overflow
        :is-show-right-btn="false"
        :is-show-toolbar="false"
      >
      </ScTable>
    </div>
    <div style="margin-top: 24px" />
    <div class="detailInfo">
      <ScTable
        v-if="isShowAbilityScore"
        ref="moreXTable"
        :columns="abilityScoreColumns"
        :table-data="abilityScoreTableData"
        header-align="center"
        :tree-config="null"
        :auto-height="false"
        height="300"
        align="center"
        show-header-overflow
        :is-show-right-btn="false"
        :is-show-toolbar="false"
      >
      </ScTable>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { headerColumns, supplierColumns } from './config/detail'
import utils from '@/utils/utils'
import { getHeadersFileName, download } from '@/utils/utils.js'

export default {
  components: {
    ScTable
  },
  data() {
    const ABILITY_TEMPLATE_TYPE = utils.getSupplierDict('SUPPLIER_ABILITY_TEMPLATE_TYPE')
    return {
      isShowAbilityScore: false,
      headerColumns, // 头部模板概要
      // headerTableData: [],
      supplierColumns,
      abilityScoreColumns: [],
      abilityScoreTableData: [], // 能力模板概要 - 数据
      ABILITY_TEMPLATE_TYPE // 模板类型
    }
  },
  watch: {},
  computed: {
    tableRef() {
      return this.$refs.capabilityXTable.$refs.xGrid
    },
    supplierTableData() {
      const { buyerResourceBoardSupplierResponseList: supplierInfo } = JSON.parse(
        sessionStorage.getItem('categoryResourceDetail')
      )
      return supplierInfo
    },
    headerTableData() {
      return [JSON.parse(sessionStorage.getItem('categoryResourceDetail'))]
    }
  },
  created() {},
  mounted() {
    const { buyerSupplierAbilityDotDetailResponses: abilityInfo } = JSON.parse(
      sessionStorage.getItem('categoryResourceDetail')
    )
    if (abilityInfo) {
      this.getAbilityDotClos(abilityInfo)
    }
  },
  methods: {
    // 获取能力明细 / 选项沙盘表格
    getAbilityDotClos(info) {
      let i = 0
      info?.forEach((item) => {
        const abilityScoreTableDataObj = {}
        item.dimParamArrayResponses?.forEach((tar) => {
          if (i === 0) {
            this.abilityScoreColumns.push({
              field: tar.fieldValue,
              title: this.$t(tar.fieldName),
              key: i
            })
          }
          abilityScoreTableDataObj[tar.fieldValue] = tar.paramValue
        })
        item?.dotSupplierResponses?.forEach((tar) => {
          if (i === 0) {
            const column = {
              field: tar.supplierCode,
              title: this.$t(tar.supplierName)
            }
            this.abilityScoreColumns.push(column)
          }
          abilityScoreTableDataObj[tar.supplierCode] = tar.level
        })
        this.abilityScoreTableData.push(abilityScoreTableDataObj)
        i += 1
      })
    },
    handleReadMore() {
      this.isShowAbilityScore = true
      setTimeout(() => {
        let readMoreDOM = document.querySelector('.detailInfo')
        readMoreDOM.scrollIntoView({ behavior: 'smooth' })
      }, 500)
    },
    hExport() {
      let params = {
        ...JSON.parse(sessionStorage.getItem('CategoryQueryByCondition'))
      }
      this.$API.categoryResources.resourceBoardExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    hBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.operate-bar {
  // height: 60px;
  display: flex;
  justify-content: end;
  align-items: center;
  color: #4f5b6d;
  .title-left {
    font-weight: 700;
    font-size: 18px;
  }
  .op-item {
    cursor: pointer;
    align-items: center;
    margin-right: 20px;
    align-items: center;
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: #00469c;
  }
}
/deep/ .e-dialog .e-dlg-header-content + .e-dlg-content {
  padding-top: 20px;
}
.tableArea {
  background-color: #fff;
  // height: 100%;
}

.buttons {
  display: flex;
  justify-content: end;
  padding: 0 10px;
}
/deep/ .vxe-cell--title {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
/deep/ .vxe-button .size--mini .type--button {
  height: 20px;
}
</style>
