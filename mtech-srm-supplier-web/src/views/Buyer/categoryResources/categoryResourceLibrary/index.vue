<template>
  <div class="fullHeight">
    <div class="formContent">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('类型')" label-style="top" prop="templateType">
          <mt-select
            v-model="formInfo.templateType"
            css-class="rule-element"
            :data-source="ABILITY_TEMPLATE_TYPE"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="categCode" :label="$t('品类名称')" label-style="top">
          <RemoteAutocomplete
            v-model="formInfo.categCode"
            url="/masterDataManagement/tenant/category/paged-query"
            :placeholder="$t('请选择品类')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
            @change="categChange"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('')" label-style="top" prop="">
          <vxe-button
            status="primary"
            :loading="loading"
            icon="vxe-icon-search"
            round
            @click="handleSearch"
            >{{ $t('筛选') }}</vxe-button
          >
          <!-- <div class="triangleSearch" @click="handleSearch" /> -->
        </mt-form-item>
      </mt-form>
    </div>
    <div class="listContent">
      <listContent v-if="isShowList" :list-info="listInfo" />
      <div v-else class="emptyInfo">{{ this.$t('暂无数据') }}</div>
    </div>
  </div>
</template>
<script>
// import utils from '@/utils/utils'
// import { cloneDeep } from 'lodash'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import listContent from './coms/list.vue'
import utils from '@/utils/utils'

export default {
  components: { RemoteAutocomplete, listContent },
  data() {
    const ABILITY_TEMPLATE_TYPE = utils.getSupplierDict('SUPPLIER_ABILITY_TEMPLATE_TYPE')
    return {
      loading: false,
      ABILITY_TEMPLATE_TYPE,
      demandDesc: '',
      imgUrl: '',

      isCateShow: true,
      isEmpShow: true,
      isSupShow: true,
      employeeId: null,
      // orgList: [],
      deptList: [],
      categoryList: [],
      supplierList: [],
      userList: [],
      rules: {
        templateType: [{ required: true, message: this.$t('请选择类型'), trigger: 'blur' }],
        categCode: [{ required: true, message: this.$t('请选择品类'), trigger: 'blur' }]
      },
      formInfo: {
        templateType: '',
        categCode: '',
        categName: ''
      },
      listInfo: {},
      isShowList: false
    }
  },
  computed: {},
  async created() {},
  mounted() {},
  methods: {
    categChange(e) {
      console.log('categChangecategChange', e)
      const { itemData } = e
      this.formInfo.categName = itemData.categoryName
    },
    // 头部查询
    handleSearch() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const params = {
            ...this.formInfo
          }
          this.loading = true
          this.$API.categoryResources.getCategoryQueryDimParam(params).then((res) => {
            if (res.code === 200) {
              this.loading = false
              this.listInfo = res.data
              const {
                buyerSupplierAbilityDimParamResponseList,
                buyerSupplierAbilityDimParamDetailResponseList
              } = res.data
              if (
                buyerSupplierAbilityDimParamResponseList &&
                buyerSupplierAbilityDimParamDetailResponseList
              ) {
                this.isShowList = true
              } else {
                this.$toast({
                  content: this.$t('该类型品类下暂无数据'),
                  type: 'warning'
                })
                this.isShowList = false
              }
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.fullHeight {
  height: 100%;
  background-color: #fff;
}
.formContent {
  margin: 10px 10px 0 10px;
  padding-bottom: 8px;
  background: #f5f5f5;
  border-radius: 8px;
}
.triangleSearch {
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 30px solid #409eff;
  border-right: 50px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  padding-left: 8px;
}

.mt-form-item {
  width: 414px;
  margin-right: 20px;
}
/deep/ .form-box {
  padding: 10px 0 0 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.emptyInfo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 80px;
}
</style>
