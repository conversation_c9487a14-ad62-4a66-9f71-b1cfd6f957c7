import { i18n } from '@/main.js'
import utils from '@/utils/utils'

const ABILITY_TEMPLATE_TYPE = utils.getSupplierDict('SUPPLIER_ABILITY_TEMPLATE_TYPE')

export const headerColumns = [
  {
    field: 'categCode',
    title: i18n.t('品类编号'),
    width: '100'
  },
  {
    field: 'categName',
    title: i18n.t('品类名称'),
    width: '120'
  },
  {
    field: 'templateType',
    title: i18n.t('类型'),
    formatter: ({ cellValue }) => {
      let item = ABILITY_TEMPLATE_TYPE.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    field: 'supplierQty',
    title: i18n.t('供应商总数')
  },
  {
    field: 'screenResult',
    title: i18n.t('筛选结果')
  },
  {
    field: 'taskNo',
    title: i18n.t('打点任务编号')
  },
  {
    field: 'effectiveTime',
    title: i18n.t('生效时间')
  },
  {
    field: 'createUserName',
    title: i18n.t('发起人')
  },
  {
    field: 'remark',
    title: i18n.t('备注')
  }
]

export const supplierColumns = [
  {
    type: 'seq',
    title: i18n.t('序号')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编号'),
    width: '100'
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    width: '180'
  },
  {
    field: 'supplierAbbr',
    title: i18n.t('供应商简称'),
    width: '100'
  },
  {
    field: 'condition',
    title: i18n.t('条件')
  },
  {
    field: 'level',
    title: i18n.t('等级')
  },
  {
    field: 'status',
    title: i18n.t('状态')
  },
  {
    field: 'supplyScope',
    title: i18n.t('供应范围')
  },
  {
    field: 'businessContact',
    title: i18n.t('业务联系人')
  },
  {
    field: 'contactPhoneNumber',
    title: i18n.t('联系方式')
  },
  {
    field: 'contactEmail',
    title: i18n.t('邮箱')
  },
  {
    field: 'documentDate',
    title: i18n.t('建档时间')
  }
]
