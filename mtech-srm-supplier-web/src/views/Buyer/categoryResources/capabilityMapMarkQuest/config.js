import { i18n } from '@/main.js'
import utils from '@/utils/utils'

export const evaluateCycleMap = utils.getSupplierDict('PER_EVALUATION')

export const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'taskNo',
    headerText: i18n.t('任务编码'),
    width: '100',
    cssClass: 'field-content'
  },
  {
    field: 'taskName',
    headerText: i18n.t('任务名称'),
    width: '120'
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板'),
    width: '100'
  },
  {
    field: 'status',
    headerText: i18n.t('任务状态'),
    width: '120',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('审批中'),
        2: i18n.t('批准'),
        3: i18n.t('退回'),
        4: i18n.t('已发布')
      }
    }
  },
  {
    field: 'createDate',
    headerText: i18n.t('发起时间'),
    width: '140'
  },

  {
    field: 'effectiveTime',
    headerText: i18n.t('生效时间'),
    width: '140'
  },

  {
    field: 'categNames',
    headerText: i18n.t('品类'),
    width: '140'
  },
  {
    field: 'supplierQty',
    headerText: i18n.t('供应商数'),
    width: '120'
  },
  {
    field: 'processInstanceId',
    headerText: i18n.t('OA流程'),
    width: '120'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('发起人'),
    width: '100'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '100'
  }
]
