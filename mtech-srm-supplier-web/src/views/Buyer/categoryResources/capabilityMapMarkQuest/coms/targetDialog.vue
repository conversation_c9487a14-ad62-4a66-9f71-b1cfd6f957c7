<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    height="650"
    :header="header"
    @open="onOpen"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        :use-tool-template="false"
        :hidden-tabs="true"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="templateCode" :label="$t('模板编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.templateCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                />
              </mt-form-item>
              <mt-form-item prop="templateName" :label="$t('模板名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.templateName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                />
              </mt-form-item>
              <!-- <mt-form-item prop="templateType" :label="$t('模板类型')" label-style="top">
                <mt-select
                  v-model="searchFormModel.templateType"
                  css-class="rule-element"
                  :data-source="ABILITY_TEMPLATE_TYPE"
                  :fields="{ text: 'dictName', value: 'dictCode' }"
                  :show-clear-button="true"
                  :placeholder="$t('请选择')"
                />
              </mt-form-item> -->
              <mt-form-item prop="categCode" :label="$t('品类')" label-style="top">
                <mt-DropDownTree
                  :fields="categoryListArrList"
                  v-model="searchFormModel.categCode"
                  :allow-multi-selection="true"
                  :auto-check="true"
                  :show-check-box="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  id="checkboxTreeSelect"
                  popup-width="300"
                  :placeholder="$t('请输入品类搜索')"
                  :key="categoryListArrList.key"
                ></mt-DropDownTree>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { columns, evaluateCycleMap, ABILITY_TEMPLATE_TYPE } from './config.js'

export default {
  components: {
    // RemoteAutocomplete
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      orgIdArr: [],

      dialogVisible: false,
      dialogHeader: this.$t('新增'),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消返回') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('发起打点任务') }
        }
      ],
      Adddata: {
        templateName: '',
        templateType: '',
        remark: '',
        orgName: '',
        orgCode: '',
        orgId: '',
        orgIdArr: [],
        templateTypeName: ''
      },
      //品类下拉
      categoryListArrList: {
        dataSource: [], //品类树下拉数组
        value: 'categoryCode',
        text: 'codeAndName',
        child: 'childrens',
        key: 'E6BPdKfEj5Ky2NwbH2F6Nkrhj6W8MamE'
      },
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      searchFormModel: {},
      pageConfig: [
        {
          gridId: '6dee1a20-29ac-4fb8-afb3-f31f0a20a475',
          title: '',
          toolbar: [],
          useToolTemplate: false,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            columnData: columns,
            asyncConfig: {
              url: 'analysis/tenant/buyer/supplier/ability/template/pageQuery',
              params: { status: 1, templateType: 'ABILITY_MAP' }
            }
          }
        }
      ],
      ABILITY_TEMPLATE_TYPE,
      calcRules: {
        templateName: [{ required: true, message: this.$t('请输入模板名称'), trigger: 'blur' }],
        templateType: [{ required: true, message: this.$t('请输入模板类型'), trigger: 'blur' }],
        orgIdArr: [{ required: true, message: this.$t('请选择组织范围'), trigger: 'blur' }]
      },
      orgList: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      evaluateCycleMap,
      ruletypeMap: [],
      categorySearchFields: ['categCode', 'categName']
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  created() {},
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getCategorys('')
    // this.getOrgList()
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 品类下拉框事件
    getCategorys(val) {
      this.$API.categoryResources
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          const list = res.data.records.map((i) => {
            return {
              ...i,
              codeAndName: `${i.categoryName}-${i.categoryCode}`
            }
          })
          this.$set(this.categoryListArrList, 'dataSource', list)
          this.$set(this.categoryListArrList, 'key', this.randomString())
        })
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.fieldsarr.dataSource = res.data
        }
      })
    },
    //选择组织
    selectOrg(e) {
      if (e.length > 0) {
        //匹配当前选中的组织 设置到formObject
        this.fn(this.fieldsarr.dataSource, e[0])
      }
    },
    // 递归 获取当前选中组织  设置到formObject
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formObject.orgId = ele.id
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgLevel = ele.orgLevel
          this.formObject.orgName = ele.orgName
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.fn(ele.childrenList, id)
        }
      })
    },

    // 确认按钮
    confirm() {
      let records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行发起任务打点'), type: 'warning' })
        return
      }
      if (records.length > 1)
        return this.$toast({
          content: this.$t('只能对一条数据进行任务打点'),
          type: 'warning'
        })
      this.$emit('confirm-function', records) //关闭弹窗
    },
    cancel() {
      this.$emit('cancel-function')
    },
    onOpen(args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.browse {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
  .show-input {
    width: 90%;
    height: 80%;
    background: transparent;
  }
}
::v-deep .quick-search .custom-search-container .mt-form {
  display: grid;
  grid-template-columns: repeat(auto-fill, 150px);
}
</style>
