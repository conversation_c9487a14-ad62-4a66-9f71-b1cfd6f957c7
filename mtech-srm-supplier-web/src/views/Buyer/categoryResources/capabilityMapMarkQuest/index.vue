<template>
  <div class="perform-box">
    <!-- 查询页 -->
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="taskNo" :label="$t('任务编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.taskNo"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="templateName" :label="$t('模板名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.templateName"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="categCode" :label="$t('品类')" label-style="top">
              <mt-DropDownTree
                :fields="categoryListArrList"
                v-model="searchFormModel.categCode"
                :allow-multi-selection="true"
                :auto-check="true"
                :show-check-box="true"
                :allow-filtering="true"
                filter-type="Contains"
                id="checkboxTreeSelect"
                :placeholder="$t('请输入品类名称或编码进行搜索')"
                :key="categoryListArrList.key"
              ></mt-DropDownTree>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                css-class="rule-element"
                :data-source="statusMap"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
// import { getHeadersFileName, download } from '@/utils/utils.js'
import { columns } from './config.js'
import dayjs from 'dayjs'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      orgIdArr: [],
      searchFormModel: {},
      pageConfig: [
        {
          gridId: '6dee1a20-29ac-4fb8-afb3-f31f0a20a567',
          title: '',
          toolbar: [
            { id: 'markAdd', title: i18n.t('新增打点任务') },
            { id: 'submit', title: i18n.t('提交') },
            { id: 'deleteMark', title: i18n.t('删除') },
            { id: 'publish', title: i18n.t('发布') }
          ],
          useToolTemplate: false,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            columnData: columns,
            asyncConfig: {
              url: 'analysis/tenant/buyer/supplier/ability/dot/pageQuery',
              params: { templateType: 'ABILITY_MAP' }
            }
          }
        }
      ],
      //品类下拉
      categoryListArrList: {
        dataSource: [], //品类树下拉数组
        value: 'categoryCode',
        text: 'codeAndName',
        child: 'childrens',
        key: 'E6BPdKfEj5Ky2NwbH2F6Nkrhj6W8MamE'
      },
      data: {
        dimensionName: '',
        dimensionType: '',
        remark: '',
        id: undefined
      },
      calcRules: {
        dimensionName: [{ required: true, message: this.$t('请输入维度名称'), trigger: 'blur' }],
        dimensionType: [{ required: true, message: this.$t('请选择QCDSI'), trigger: 'blur' }]
      },
      orgList: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      statusMap: [
        { text: this.$t('新建'), value: 0 },
        { text: this.$t('审批中'), value: 1 },
        { text: this.$t('批准'), value: 2 },
        { text: this.$t('退回'), value: 3 },
        { text: this.$t('已发布'), value: 4 }
      ],
      ruletypeMap: [],
      supplierSearchFields: ['supplierCode', 'supplierName']
    }
  },
  created() {},
  mounted() {
    this.getCategorys('')
  },

  methods: {
    // 品类下拉框事件
    getCategorys(val) {
      this.$API.categoryResources
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          const list = res.data.records.map((i) => {
            return {
              ...i,
              codeAndName: `${i.categoryName}-${i.categoryCode}`
            }
          })
          this.$set(this.categoryListArrList, 'dataSource', list)
          this.$set(this.categoryListArrList, 'key', this.randomString())
        })
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 单元格字段点击事件,跳转页面
    handleClickCellTitle(e) {
      if (e.field === 'taskNo') {
        this.handleTemplateDetail(e.data)
      }
    },
    // 选择时间
    handleDateTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['startCreateDate'] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['endCreateDate'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['startCreateDate'] = null
        this.searchFormModel['endCreateDate'] = null
      }
    },
    // 工具栏
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (item.toolbar.id == 'markAdd') {
        this.$dialog({
          modal: () => import('./coms/targetDialog.vue'),
          data: {
            title: this.$t('新增打点任务')
          },
          success: (data) => {
            this.$router.push({
              path: `/supplier/category-resources/capability-map-mark-quest-detail`,
              query: {
                addTemplate: data[0],
                isDetail: 'add'
              }
            })
          }
        })
      } else if (item.toolbar.id == 'deleteMark') {
        if (records.length == 0)
          return this.$toast({
            content: this.$t('请先选择一条数据'),
            type: 'warning'
          })
        if (records.length > 1)
          return this.$toast({
            content: this.$t('只能对一条数据进行编辑'),
            type: 'warning'
          })
        this.handleTemDelete(records)
      } else if (item.toolbar.id == 'publish') {
        if (records.length == 0)
          return this.$toast({
            content: this.$t('请先选择一条数据'),
            type: 'warning'
          })
        this.handlePublish(records)
      } else if (item.toolbar.id == 'submit') {
        if (records.length == 0)
          return this.$toast({
            content: this.$t('请先选择一条数据'),
            type: 'warning'
          })
        if (records.length > 1)
          return this.$toast({
            content: this.$t('只能对一条数据进行编辑'),
            type: 'warning'
          })
        this.handleSubmit(records)
      }
    },
    // 提交
    handleSubmit(arr) {
      if (arr.some((e) => e.status !== 0 && e.status !== 3))
        return this.$toast({
          content: this.$t('只有新建和退回状态的数据可以提交'),
          type: 'warning'
        })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交选中的数据吗？')
        },
        success: () => {
          this.$loading()

          this.$API.categoryResources
            .markSubmitApprove({ id: arr.map((e) => e.id)[0] })
            .then((res) => {
              if (res.code === 200) {
                this.$hloading()
                this.$toast({ content: this.$t('数据提交成功'), type: 'success' })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    //  发布 只有状态： 批准 可以发布
    handlePublish(arr) {
      if (arr.some((e) => e.status !== 2))
        return this.$toast({
          content: this.$t('只有批准状态的数据可以发布'),
          type: 'warning'
        })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认发布选中的数据吗？')
        },
        success: () => {
          const params = {
            ids: arr.map((e) => e.id)
          }
          this.$loading()
          this.$API.categoryResources
            .markPublish(params)
            .then((res) => {
              if (res.code == 200) {
                this.$hloading()
                this.$toast({ content: this.$t('发布成功'), type: 'success' })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    // 模板明细跳转
    handleTemplateDetail(e) {
      // if (e.status !== 0 && e.status !== 3)
      //   return this.$toast({
      //     content: this.$t('请检查当前单据状态，只有新建和退回状态可查看详情'),
      //     type: 'warning'
      //   })
      sessionStorage.setItem('markdetailData', JSON.stringify(e))
      this.$router.push({
        path: `/supplier/category-resources/capability-map-mark-quest-detail`,
        query: {
          id: e.id,
          isDetail: e.status !== 0 && e.status !== 3 ? 'detail' : 'edit'
        }
      })
    },
    // 删除 只有新建 退回可以删除
    handleTemDelete(arr) {
      if (arr.some((e) => e.status !== 0 && e.status !== 3))
        return this.$toast({
          content: this.$t('只有新建和退回状态的数据可以删除'),
          type: 'warning'
        })

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据吗？')
        },
        success: () => {
          const params = {
            id: arr.map((e) => e.id).toString()
          }
          this.$API.categoryResources.markDeleteByIds(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
.perform-box {
  width: 100%;
  position: relative;
  height: 100%;
  .red {
    background-color: rgba(237, 86, 51, 1);
  }
  .green {
    background-color: rgba(138, 204, 64, 1);
  }
  .yellow {
    background-color: rgba(237, 161, 51, 1);
  }
  .yellowcolor {
    color: rgba(237, 161, 51, 1) !important;
  }
  .bluecolor {
    color: #6386c1 !important;
  }
  .blurbgcolor {
    background-color: #00469c;
  }
  ::v-deep .now-status {
    padding: 3px 5px;
    color: #6386c1;
    background-color: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
  }
  ::v-deep .now-status-stop {
    padding: 3px 5px;
    color: #9a9a9a;
    background-color: rgba(154, 154, 154, 0.1);
    border-radius: 2px;
  }
  ::v-deep .change-status {
    color: #6386c1;
    margin-top: 5px;
    cursor: pointer;
  }
}
::v-deep .dimension-name {
  width: calc((100% - 24px) / 2);
  display: inline-block;
}
::v-deep .dimension-type {
  width: calc((100% - 24px) / 2);
  margin-left: 24px;
  display: inline-block;
}
</style>
