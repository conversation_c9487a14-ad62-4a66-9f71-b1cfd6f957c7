import { i18n } from '@/main.js'

export const headerColumns = [
  {
    field: 'taskNo',
    title: i18n.t('任务编码')
  },
  {
    field: 'templateName',
    title: i18n.t('模板'),
    width: '100'
  },
  {
    field: 'status',
    title: i18n.t('任务状态'),
    formatter: (data) => {
      if (data.row.status == 0) {
        return '新建'
      } else if (data.row.status == 1) {
        return '审批中'
      } else if (data.row.status == 2) {
        return '批准'
      } else if (data.row.status == 3) {
        return '退回'
      } else if (data.row.status == 4) {
        return '已发布'
      }
    }
  },
  {
    field: 'createDate',
    title: i18n.t('发起时间')
  },
  {
    field: 'effectiveTime',
    title: i18n.t('生效时间')
  },
  {
    field: 'categNames',
    title: i18n.t('品类')
  },
  {
    field: 'supplierQty',
    title: i18n.t('供应商数')
  },
  {
    field: 'categCode',
    title: i18n.t('OA审批流程'),
    slots: {
      default: 'OA'
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('发起人')
  },
  {
    field: 'createUserName',
    title: i18n.t('等级定义'),
    slots: {
      default: 'rankDefinition'
    }
  },
  {
    field: 'remark',
    title: i18n.t('备注'),
    editRender: { name: 'input', attrs: { placeholder: '备注' } }
  }
]

export const level = [
  {
    field: 'level',
    title: i18n.t('等级')
  },
  {
    field: 'definition',
    title: i18n.t('定义')
  },
  {
    field: 'remark',
    title: i18n.t('备注')
  }
]

export const userConfig = [
  {
    type: 'seq',
    title: i18n.t('序号')
  },
  {
    field: 'roleName',
    title: i18n.t('分工')
  },
  {
    field: 'respUserName',
    title: i18n.t('责任人'),
    formatter: (data) => {
      return data.row.respUser + '-' + data.row.respUserName
    }
  },
  {
    field: 'approverName',
    title: i18n.t('审批人'),
    formatter: (data) => {
      return data.row.approverAcct + '-' + data.row.approverName
    }
  }
]
