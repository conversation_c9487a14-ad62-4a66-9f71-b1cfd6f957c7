<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :header="header"
    width="700"
    height="400"
    @beforeClose="cancel"
  >
    <div>
      <template class="capability">
        <div style="margin-top: 24px" />
        <div style="font-weight: bold">{{ $t('审批人列表') }}</div>
        <ScTable
          ref="sctableRef"
          :auto-height="false"
          height="220"
          :columns="columns"
          :table-data="userConfigData"
          header-align="center"
          :is-show-right-btn="false"
          :tree-config="{}"
          align="center"
          :edit-config="{
            trigger: 'dblclick',
            mode: 'row',
            showUpdateStatus: true,
            showInsertStatus: true,
            activeMethod: this.activeRowMethod
          }"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              v-show="isShowEditBtn"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </template>
        </ScTable>
      </template>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
// import { userConfig } from './config/detail'
import mixin from './config/mixin'

export default {
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      userConfigData: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      roleList: [
        { label: this.$t('质量'), value: 'QUALITY' },
        { label: this.$t('采购'), value: 'PURCHASE' },
        { label: this.$t('研发'), value: 'RD' }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    userConfigId() {
      return this.modalData.data.id
    },
    markTaskType() {
      return this.modalData.data.markTaskType
    },
    isChangeData() {
      return this.modalData.data.isChangeData
    },
    isShowEditBtn() {
      const status =
        JSON.parse(sessionStorage.getItem('markdetailData'))?.status === 0 ||
        JSON.parse(sessionStorage.getItem('markdetailData'))?.status === 3
      return status || this.markTaskType === 'add' || this.markTaskType === 'edit'
    }
  },
  mounted() {
    console.log('dialogdialogdialog', this.isShowEditBtn, this.markTaskType)
    // 获取审批人数据
    if (!this.isChangeData) {
      this.getUserConfigList()
    } else {
      this.userConfigData = this.modalData.data.curUserConfig
    }
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    cancel() {
      const userConfigCurList = {
        isChangeData: true,
        userConfigCurArr: this.tableRef.getTableData().tableData
      }
      this.$emit('confirm-function', userConfigCurList)
    },
    // 获取审批人数据
    getUserConfigList() {
      let paramId = {
        id: this.userConfigId
      }
      if (this.markTaskType === 'add') {
        this.$API.categoryResources.getUserConfigByTemplateId(paramId).then((res) => {
          this.userConfigData = res.data
        })
      } else if (this.markTaskType === 'edit' || this.markTaskType === 'detail') {
        this.$API.categoryResources.getUserConfigByDotId(paramId).then((res) => {
          this.userConfigData = res.data
        })
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },
    // 新增
    async handleAdd() {
      const newRecord = {}
      await this.tableRef.insert(newRecord)
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    // 删除
    handleDelete() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          // 临时删除
          await this.tableRef.removeCheckboxRow()
        }
      })
    },
    // isDetail的状态为detail的 不允许编辑
    activeRowMethod() {
      if (!this.isShowEditBtn) {
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.buttons {
  display: flex;
  justify-content: end;
  background-color: #fff;
}
</style>
