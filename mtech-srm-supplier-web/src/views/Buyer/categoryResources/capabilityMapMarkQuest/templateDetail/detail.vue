<template>
  <div>
    <div class="operate-bar">
      <vxe-button
        v-if="$route.query.isDetail === 'edit'"
        style="margin: 5px 10px 0 0"
        :content="$t('导入')"
        size="mini"
        @click="handleImport"
      />
      <vxe-button
        style="margin: 5px 10px 0 0"
        :content="$t('导出')"
        size="mini"
        :disabled="isUseExport"
        @click="hExport"
      />
      <vxe-button
        v-if="$route.query.isDetail === 'edit' || $route.query.isDetail === 'add'"
        style="margin: 5px 10px 0 0"
        :loading="saveLoading"
        :content="$t('保存')"
        size="mini"
        @click="hSave"
      ></vxe-button>
      <vxe-button
        style="margin: 5px 10px 0 0"
        size="mini"
        v-if="$route.query.isDetail === 'edit'"
        :loading="submitLoading"
        :content="$t('提交审批')"
        @click="hSubmit"
      ></vxe-button>
      <vxe-button
        size="mini"
        style="margin: 5px 10px 0 0"
        status="primary"
        :content="$t('返回')"
        @click="hBack"
      />
    </div>
    <div style="margin-top: 15px" />
    <template class="header">
      <ScTable
        ref="xTable"
        :columns="headerColumns"
        :table-data="headerTableData"
        :is-show-right-btn="false"
        :is-show-toolbar="false"
        :auto-height="false"
        :min-height="false"
        height="100"
        header-align="center"
        :tree-config="null"
        align="center"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          showUpdateStatus: true,
          showInsertStatus: true
        }"
      >
        <template #OA>
          <vxe-button size="mini" status="primary" @click="hUserConfig">{{
            $t('审批人列表')
          }}</vxe-button>
        </template>
        <template #rankDefinition>
          <vxe-button size="mini" status="primary" @click="hCheckLevel">查看等级定义</vxe-button>
        </template>
      </ScTable>
    </template>
    <div style="margin-top: 24px" />
    <div class="tableArea">
      <ScTable
        ref="markXTable"
        :auto-height="false"
        height="600"
        :columns="dimensionColumns"
        :table-data="dimensionTableData"
        header-align="center"
        :tree-config="null"
        align="center"
        :row-config="{ isCurrent: false, isHover: false }"
        keep-source
        show-header-overflow
        :is-show-right-btn="false"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          showUpdateStatus: true,
          showInsertStatus: true
        }"
        :span-method="rowspanMethod"
        :loading="tableDataLoading"
        :loading-config="{ icon: 'vxe-icon-indicator roll', text: '数据加载中...' }"
      >
        <template #header="{ column }">
          <div style="display: block">
            <span>{{ column.title }}</span>
            <div>
              <vxe-button size="mini" style="margin-left: 5px" @click="addColumn()">+</vxe-button>
              <vxe-button size="mini" @click="handleHeaderClick(column)">x</vxe-button>
            </div>
          </div>
        </template>
        <template #newHeader="{ column }">
          <vxe-select
            v-model="column.field"
            placeholder="请选择供应商"
            :options="supplierOptions"
            @change="(value) => supplierChange(value, column)"
            transfer
          ></vxe-select>
          <div style="display: flex; flex-direction: column-reverse; align-items: flex-end">
            <vxe-button size="mini" style="margin-left: 5px" @click="addColumn()">+</vxe-button>
            <vxe-button size="mini" @click="handleHeaderClick(column)">x</vxe-button>
          </div>
        </template>
        <template #newColumn="{ row, column }">
          <vxe-select
            v-model="row[column.field]"
            placeholder="请评分"
            :options="markOptions"
            @change="(value) => levelChange(value, row, column)"
            transfer
          />
        </template>
      </ScTable>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="index-dialog"
      :header="dialogHeader"
      :buttons="buttons"
      :open="onOpen"
      :close="onClose"
      height="40%"
      width="40%"
    >
      <mt-form ref="ruleForm" :model="Adddata" :rules="calcRules" style="margin-top: 15px">
        <mt-form-item prop="supplierId" :label="$t('供应商简称')">
          <mt-select
            v-model="Adddata.supplierId"
            :data-source="planeArrList"
            :fields="{ text: 'text', value: 'supplierEnterpriseId' }"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            @change="changePlanName"
            :placeholder="$t('请选择供应商简称：')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
          <mt-input v-model="Adddata.supplierCode" disabled :placeholder="$t('请选择供应商编码')" />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商名称')">
          <mt-input v-model="Adddata.supplierName" disabled :placeholder="$t('请选择供应商名称')" />
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <!-- 导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      :is-show-tips="false"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
import { headerColumns } from './config/detail'
import { getHeadersFileName, download } from '@/utils/utils.js'
import utils from '@/utils/utils'

export default {
  components: {
    ScTable,
    UploadExcelDialog
  },
  data() {
    const ABILITY_TEMPLATE_TYPE = utils.getSupplierDict('SUPPLIER_ABILITY_TEMPLATE_TYPE')
    // const fields = this.dimensionColumns
    //   .map((item) => item.field)
    //   .filter((item) => item?.indexOf('fld') === 0)
    return {
      // tooltipConfig: {
      //   contentMethod: ({ type, column, row, items, _columnIndex }) => {
      //     // 重写默认的提示内容
      //     if (fields.field === 'date') {
      //       if (type === 'header') {
      //         return '自定义标题提示内容：' + column.title
      //       } else if (type === 'footer') {
      //         return '自定义表尾提示内容：' + items[_columnIndex]
      //       }
      //       return '自定义提示内容：' + row[column.field]
      //     }
      //   },
      //   enterable: true
      // },
      tableDataLoading: false,
      planeArrList: [],
      calcRules: {
        templateName: [{ required: true, message: this.$t('请输入模板名称'), trigger: 'blur' }],
        templateType: [{ required: true, message: this.$t('请输入模板类型'), trigger: 'blur' }],
        orgIdArr: [{ required: true, message: this.$t('请选择组织范围'), trigger: 'blur' }]
      },
      dialogHeader: this.$t('新增'),
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('新增') }
        }
      ],
      Adddata: {
        supplierCode: '',
        supplierId: '',
        supplierName: ''
      },
      submitLoading: false,
      saveLoading: false,
      headerColumns, // 头部模板概要 - 表头
      dimensionColumns: [],
      siteOptions: [],
      supplierOptions: [],
      markOptions: [],
      levelDetailInfo: [],
      headerTableData: [
        {
          evaluateYear: 1,
          achievementAssessType: 2,
          taskName: ''
        }
      ], // 头部模板概要 - 数据
      dimensionTableData: [], // 头部模板概要 - 数据
      capabilityTableData: [], // 头部模板概要 - 数据
      markdetailData: {},
      detailCapId: {},
      dotSupplierResponses: {},
      ABILITY_TEMPLATE_TYPE, // 模板类型
      templateType: '', // 当前模板类型
      isChangeData: false, // 审批人列表是否发送请求渲染列表flag
      curUserConfig: [],
      isFirstEntry: true, // 是否第一次进入页面
      uploadParams: {}, // 导入文件参数
      requestUrls: {}
    }
  },
  watch: {
    '$route.query.addTemplate.templateType': {
      handler(val) {
        if (val) {
          this.templateType = val
        }
      },
      immediate: true
    }
  },
  computed: {
    isUseExport() {
      return !!this.$route.query?.addTemplate?.id || !this.$route.query.id
    },
    lastItem() {
      let levelChar = 'A'
      this.levelDetailInfo.forEach((item) => {
        if (item?.level >= levelChar) {
          levelChar = item?.level
        }
      })
      return levelChar
    }
  },
  activated() {
    if (this.isFirstEntry) {
      this.getSupplierList()
      // 查看详情，有id的情况
      if (this.$route.query.id) {
        this.getCapabilityDetailData()
        this.markdetailData = JSON.parse(sessionStorage.getItem('markdetailData'))
        this.headerTableData = [JSON.parse(sessionStorage.getItem('markdetailData'))]
      } else if (this.$route.query.addTemplate.id) {
        // 新增情况，没有id
        this.headerTableData = [this.$route.query.addTemplate]
        this.headerTableData[0].status = 0
        this.headerTableData[0].taskName = this.$route.query.addTemplate.templateName
        this.addCapabilityDetailData()
      }
      if (this.$route.query.isDetail === 'add') {
        if (this.headerColumns.some((item) => item.field === 'taskName')) {
          this.headerColumns[
            this.headerColumns.findIndex((item) => item.field === 'taskName')
          ].editRender = { name: 'input', attrs: { placeholder: this.$t('任务名称') } }
        } else {
          this.headerColumns.unshift({
            field: 'taskName',
            title: this.$t('任务名称'),
            editRender: { name: 'input', attrs: { placeholder: this.$t('任务名称') } }
          })
        }
      } else if (this.$route.query.isDetail === 'edit' || this.$route.query.isDetail === 'detail') {
        if (this.headerColumns.some((item) => item.field === 'taskName')) {
          delete this.headerColumns[
            this.headerColumns.findIndex((item) => item.field === 'taskName')
          ].editRender
        } else {
          this.headerColumns.unshift({
            field: 'taskName',
            title: this.$t('任务名称')
          })
        }
      }
      this.isFirstEntry = false
    }
  },
  // deactivated() {
  //   this.isFirstEntry = true
  // },
  mounted() {},
  methods: {
    // 获取供应商列表
    getSupplierList() {
      this.$API.categoryResources.getSupplierList().then((result) => {
        if (result.code == 200) {
          this.planeArrList = result.data.map((item) => {
            item.text = item.supplierCode + '-' + item.supplierShortName + '-' + item.supplierName
            return item
          })
        }
      })
    },
    //选择供应商名称
    changePlanName(e) {
      if (e.itemData) {
        const { supplierEnterpriseId, supplierCode, supplierName, supplierShortName } = e.itemData
        this.Adddata = {
          supplierId: supplierEnterpriseId,
          supplierCode,
          supplierName,
          supplierAbbr: supplierShortName
        }
      }
    },
    // 弹窗确认事件
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.dimensionColumns.some((item) => item.field === this.Adddata.supplierCode))
            return this.$toast({
              content: this.$t('该供应商已存在，请勿重复选择打分'),
              type: 'warning'
            })
          const isEdit = 'edit'
          this.addNewColumn(this.Adddata.supplierCode, this.Adddata.supplierAbbr, isEdit)
          if (this.dimensionColumns.findIndex((item) => item.field === null) >= 0) {
            this.dimensionColumns.splice(
              this.dimensionColumns.findIndex((item) => item.field === null),
              1
            )
          }
          this.hide()
        }
      })
    },
    // 有任务id 查看详情
    async getCapabilityDetailData() {
      const params = {
        id: this.$route.query.id
      }
      this.tableDataLoading = true
      const res = await this.$API.categoryResources.getMarkDetailList(params)
      this.tableDataLoading = false
      let i = 0
      this.dimensionColumns = []
      this.dimensionTableData = []
      res.data.forEach((item, index) => {
        this.detailCapId[index] =
          item?.dimParamArrayResponses?.length > 0 ? item?.dimParamArrayResponses[0].id : null
        this.templateType = item.templateType
      })
      this.templateId = res.data[0].templateId
      res.data.forEach((item, index) => {
        item.dotSupplierResponses.forEach((tar) => {
          const key = tar.supplierCode + '-' + index
          this.dotSupplierResponses[key] = tar.id
        })
      })
      this.levelDetailInfo = res.data[0].buyerSupplierAbilityLevelDefinitionResponses
      this.markOptions = res.data[0].buyerSupplierAbilityLevelDefinitionResponses.map((option) => ({
        value: option.level,
        label: option.level,
        abolished: option.abolished,
        id: option.id
      }))
      res.data?.forEach((item) => {
        const dimensionTableDataObj = {}
        item.dimParamArrayResponses?.forEach((tar) => {
          if (i === 0) {
            this.dimensionColumns.push({
              field: tar.fieldValue,
              title: this.$t(tar.fieldName),
              key: i,
              minWidth: '200'
              // fixed: 'left'
            })
          }
          dimensionTableDataObj[tar.fieldValue] = tar.paramValue
        })
        item?.dotSupplierResponses?.forEach((tar) => {
          if (i === 0) {
            const column = {
              field: tar.supplierCode,
              title: this.$t(tar.supplierAbbr || tar.supplierName),
              editRender: {},
              slots: {
                header: '',
                edit: ''
              },
              minWidth: '200'
            }
            const edit = ({ row, column }) => {
              return [
                <div>
                  <vxe-select
                    v-model={row[column.field]}
                    placeholder='请评分'
                    options={this.markOptions}
                    onChange={(value) => this.levelChange(value, row, column)}
                    transfer></vxe-select>
                </div>
              ]
            }
            const header = ({ column }) => {
              return [
                <div style='display: block'>
                  <div>
                    <vxe-button
                      type='text'
                      size='mini'
                      style='marginLeft: 5px'
                      onClick={() => this.addColumn()}>
                      +
                    </vxe-button>
                    <vxe-button
                      type='text'
                      size='mini'
                      onClick={() => this.handleHeaderClick(column)}>
                      x
                    </vxe-button>
                  </div>
                  <span>{column.title}</span>
                </div>
              ]
            }
            if (this.$route.query.isDetail === 'edit') {
              this.$set(column.slots, 'header', header)
              this.$set(column.slots, 'edit', edit)
            }
            this.dimensionColumns.push(column)
          }
          dimensionTableDataObj[tar.supplierCode] = tar.level
        })
        this.dimensionTableData.push(dimensionTableDataObj)
        i += 1
      })
    },
    // 有模板id 新增的打点数据
    addCapabilityDetailData() {
      const params = {
        id: this.$route.query.addTemplate.id
      }
      this.tableDataLoading = true

      this.$API.categoryResources
        .addMarkDetailList(params)
        .then((res) => {
          this.tableDataLoading = false
          this.levelDetailInfo = res.data[0]?.buyerSupplierAbilityLevelDefinitionResponses
          this.markOptions = res.data[0]?.buyerSupplierAbilityLevelDefinitionResponses?.map(
            (option) => ({
              value: option.level,
              label: option.level,
              abolished: option.abolished,
              id: option.id
            })
          )
          res.data.forEach((item, index) => {
            this.detailCapId[index] =
              item?.dimParamArrayResponses?.length > 0 ? item?.dimParamArrayResponses[0].id : null
          })
          let i = 0
          res.data.forEach((item) => {
            const dimensionTableDataObj = {}
            item.dimParamArrayResponses.forEach((tar) => {
              if (i === 0) {
                this.dimensionColumns.push({
                  field: tar.fieldValue,
                  title: this.$t(tar.fieldName),
                  minWidth: '200'
                  // fixed: 'left'
                })
              }
              dimensionTableDataObj[tar.fieldValue] = tar.paramValue
            })
            // 如果当前存在合适的供应商
            if (item?.dotSupplierResponses.length > 0) {
              this.renderCurrentSupplier(item?.dotSupplierResponses, dimensionTableDataObj, i)
            }
            this.dimensionTableData.push(dimensionTableDataObj)
            i += 1
          })
          // 如果存在合适的供应商则不需要添加一列空的
          if (res.data[0].dotSupplierResponses.length === 0) {
            const newField = null
            const newTitle = this.$t('添加供应商')
            const isEdit = 'add'
            this.addNewColumn(newField, newTitle, isEdit)
          }
        })
        .catch(() => {
          this.tableDataLoading = false
        })
    },
    // 如果存在合适的供应商 则直接渲染供应商头信息
    renderCurrentSupplier(supplierList, dimensionTableDataObj, i) {
      supplierList.forEach((tar) => {
        if (i === 0) {
          const column = {
            field: tar.supplierCode,
            title: this.$t(tar.supplierAbbr || tar.supplierName),
            editRender: {},
            slots: {
              header: '',
              edit: ''
            },
            minWidth: '200'
          }
          const edit = ({ row, column }) => {
            return [
              <div>
                <vxe-select
                  v-model={row[column.field]}
                  placeholder='请评分'
                  options={this.markOptions}
                  onChange={(value) => this.levelChange(value, row, column)}
                  transfer></vxe-select>
              </div>
            ]
          }
          const header = ({ column }) => {
            return [
              <div style='display: block'>
                <div>
                  <vxe-button
                    type='text'
                    size='mini'
                    style='marginLeft: 5px'
                    onClick={() => this.addColumn()}>
                    +
                  </vxe-button>
                  <vxe-button
                    type='text'
                    size='mini'
                    onClick={() => this.handleHeaderClick(column)}>
                    x
                  </vxe-button>
                </div>
                <span>{column.title}</span>
              </div>
            ]
          }
          this.$set(column.slots, 'header', header)
          this.$set(column.slots, 'edit', edit)
          this.dimensionColumns.push(column)
        }
        dimensionTableDataObj[tar.supplierCode] = tar.level
      })
    },
    // 渲染新的一列
    addNewColumn(newField, newTitle, isEdit) {
      const newColumn = {
        key: this.dimensionColumns.length + 1,
        field: newField,
        title: newTitle,
        editRender: {},
        minWidth: '200',
        slots: {
          header: ({ column }) => {
            return [
              <div style='display: block'>
                <div>
                  <vxe-button
                    type='text'
                    size='mini'
                    style='marginLeft: 5px'
                    onClick={() => this.addColumn()}>
                    +
                  </vxe-button>
                  <vxe-button
                    type='text'
                    size='mini'
                    onClick={() => this.handleHeaderClick(column)}>
                    x
                  </vxe-button>
                </div>
                <span>{column.title}</span>
              </div>
            ]
          }
        }
      }
      if (isEdit === 'edit') {
        const a = ({ row, column }) => {
          return [
            <div>
              <vxe-select
                v-model={row[column.field]}
                placeholder='请评分'
                options={this.markOptions}
                onChange={(value) => this.levelChange(value, row, column)}
                transfer></vxe-select>
            </div>
          ]
        }
        this.$set(newColumn.slots, 'edit', a)
      }
      this.dimensionColumns.push(newColumn)
    },
    onOpen(args) {
      this.show()
      args.preventFocus = true
    },
    onClose() {
      this.hide()
    },
    show() {
      this.$refs.dialog?.ejsRef.show()
    },
    hide() {
      this.Adddata = {}
      this.$refs.dialog?.ejsRef.hide()
    },
    // 提交
    dimensionSubmit() {
      const $table = this.$refs.markXTable.$refs.xGrid
      const { insertRecords, removeRecords, updateRecords } = $table.getRecordset()
      if (insertRecords.length <= 0 && removeRecords.length <= 0 && updateRecords.length <= 0) {
        this.$toast({ content: this.$t('请先修改数据'), type: 'warning' })
        return
      }
    },
    // 保存
    hSave() {
      const markData = this.$refs.markXTable.$refs.xGrid.getTableData().fullData // 每一行的数据
      const markColumns = this.$refs.markXTable.$refs.xGrid.columns // 列头数据
      if (
        markColumns.every(
          (item) => item.field === 'id' || item.field?.indexOf('fld') === 0 || item.field === null
        )
      )
        return this.$toast({ content: this.$t('请先添加供应商，再保存'), type: 'warning' })
      if (!this.headerTableData[0].taskName)
        return this.$toast({ content: this.$t('请填写任务名称'), type: 'warning' })
      const columnsSortObj = {}
      const columnsSupObj = {}

      this.planeArrList.forEach((item) => {
        columnsSupObj[item.supplierCode] = item
      })
      let j = 1
      this.$refs.markXTable.$refs.xGrid.columns.forEach((item) => {
        if (item.field !== 'id' && item.field?.indexOf('fld') !== 0) {
          columnsSortObj[item.field] = j
          j += 1
        }
      })
      const addBuyerSupplierAbilityDotSupplierRequests = []
      markData.forEach((item, index) => {
        markColumns.forEach((tar) => {
          const key = tar.field
          const value = item[key] || this.lastItem
          if (key !== 'id' && key?.indexOf('fld') !== 0) {
            addBuyerSupplierAbilityDotSupplierRequests.push({
              dotId: this.$route.query.id ? this.$route.query.id : null, // 打点的任务id，前一个页面点击进来的id
              dtlId: this.detailCapId[index], // 每行的能力明细id
              id:
                Object.keys(this.dotSupplierResponses).length === 0
                  ? null
                  : this.dotSupplierResponses[key + '-' + index], // 对应列的接口返回dotSupplierResponses的lsit的里面的id，如果有就传，新增的列 没有的话，就传null
              levelDefineId:
                this.markOptions[this.markOptions.findIndex((tar) => tar.label === value)]?.id,
              level: value,
              supplierId:
                columnsSupObj[key]?.supplierId || columnsSupObj[key]?.supplierEnterpriseId,
              supplierCode: key,
              supplierName: columnsSupObj[key]?.supplierName,
              supplierAbbr: columnsSupObj[key]?.supplierShortName,
              templateId: this.templateId, // 模板的id 详情接口会有
              sort: columnsSortObj[key] //字段列头的排序
            })
          }
        })
      })
      console.log('this.curUserConfigthis.curUserConfig', this.$route.query.id)
      this.curUserConfig.forEach((e) => {
        e.dotId = this.$route.query.id ? this.$route.query.id : null
        delete e.id
      })
      console.log('paramparam', addBuyerSupplierAbilityDotSupplierRequests)

      // debugger
      const param = {
        addBuyerSupplierAbilityUserConfigRequests: this.curUserConfig,
        addBuyerSupplierAbilityDotSupplierRequests, // 当前页面数据
        remark: this.$refs.xTable.$refs.xGrid.getTableData().fullData[0].remark,
        id: this.$route.query.id ? this.$route.query.id : null, // 主表查询的id
        taskName: this.markdetailData.taskName
          ? this.markdetailData.taskName
          : this.headerTableData[0].taskName,
        taskNo: this.markdetailData.taskNo,
        templateId: this.templateId ? this.templateId : this.$route.query.addTemplate?.id || null,
        templateName:
          this.markdetailData.templateName ||
          this.$refs.xTable.$refs.xGrid.getTableData().fullData[0].templateName,
        templateType: this.templateType,
        continueFlag: false
      }
      this.saveLoading = true
      this.$API.categoryResources
        .markSaveOrUpdate(param)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('数据保存成功'), type: 'success' })
            this.saveLoading = false
            this.$router.go(-1)
            this.dimensionColumns = []
            this.dimensionTableData = []
            this.isFirstEntry = true
          }
        })
        .catch((err) => {
          console.log(err)
          this.saveLoading = false
          if (err.code === 201) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t(err.msg + ',是否确定选择继续？')
              },
              success: () => {
                param.continueFlag = true
                this.$API.categoryResources.markSaveOrUpdate(param).then(() => {
                  this.$toast({
                    content: this.$t('数据保存成功'),
                    type: 'success'
                  })
                  this.$router.go(-1)
                  this.dimensionColumns = []
                  this.dimensionTableData = []
                  this.isFirstEntry = true
                })
              }
            })
          }
        })
    },
    // 合并单元格方法
    rowspanMethod({ row, _rowIndex, column, visibleData }) {
      const fields = this.dimensionColumns
        .map((item) => item.field)
        .filter((item) => item?.indexOf('fld') === 0)

      if (fields.length == 0) {
        return
      }
      const cellValue = row[column.property]
      if (cellValue != undefined && cellValue != null && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && this.checkMergeFields(row, prevRow, column.property)) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && this.checkMergeFields(row, nextRow, column.property)) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    //循环判断前面的列，如果值不一样的话就不合并 (上一级不相同的，下一级及时相同也不能合并)
    checkMergeFields(row, nextRow, property) {
      const fields = this.dimensionColumns
        .map((item) => item.field)
        .filter((item) => item?.indexOf('fld') === 0)
      var ret = true
      for (var i = 0; i < fields.length; i++) {
        var field = fields[i]
        if (nextRow[field] != row[field]) {
          ret = false
          break
        }
        if (field == property) {
          break
        }
      }
      return ret
    },
    // 新增一列
    addColumn() {
      this.show()
    },
    // 删除当前列
    handleHeaderClick(column) {
      if (this.dimensionColumns.filter((item) => item.field?.indexOf('fld') !== 0)?.length === 1) {
        this.$toast({ content: this.$t('请至少保留一条供应商'), type: 'warning' })
        return
      }
      const columnIndex = this.dimensionColumns.findIndex((item) => item.field === column.field)
      this.dimensionTableData = this.dimensionTableData.map((item) => {
        delete item[column.field]
        return item
      })
      if (columnIndex !== -1) {
        this.dimensionColumns.splice(columnIndex, 1)
      }
    },
    // 选择表头
    supplierChange(value, column) {
      column.property = value.value
      column.field = value.value
    },
    levelChange(val, row, column) {
      const { value } = val
      row[column.field] =
        this.markOptions[this.markOptions.findIndex((item) => item.value === value)].label
    },
    // 返回
    hBack() {
      if (this.$route.query.isDetail === 'add' || this.$route.query.isDetail === 'edit') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认是否放弃修改，不保存退出？')
          },
          success: () => {
            this.$router.go(-1)
            this.dimensionColumns = []
            this.dimensionTableData = []
            this.isFirstEntry = true
            this.isChangeData = false // 审批人列表重新进入发送请求，请求最新数据
          }
        })
      } else if (this.$route.query.isDetail === 'detail') {
        this.$router.go(-1)
        this.dimensionColumns = []
        this.dimensionTableData = []
        this.isFirstEntry = true
        this.isChangeData = false // 审批人列表重新进入发送请求，请求最新数据
      }
    },
    hCheckLevel() {
      console.log('34876')
      this.$dialog({
        modal: () => import('./levelDialog.vue'),
        data: {
          title: this.$t('查看等级定义'),
          data: this.levelDetailInfo
        },
        success: () => {}
      })
    },
    // 审批人列表dialog
    hUserConfig() {
      const userConfigId =
        this.$route.query.isDetail === 'add'
          ? this.$route.query?.addTemplate?.id
          : this.$route.query.id
      this.$dialog({
        modal: () => import('./userConfigDialog.vue'),
        data: {
          title: this.$t('查看审批人列表'),
          data: {
            id: userConfigId,
            markTaskType: this.$route.query.isDetail,
            isChangeData: this.isChangeData,
            curUserConfig: this.isChangeData && this.curUserConfig
          }
        },
        success: (data) => {
          this.isChangeData = data.isChangeData
          this.curUserConfig = data.userConfigCurArr
        }
      })
    },
    // 导出
    hExport() {
      if (this.$route.query.isDetail === 'edit') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('请确保当前页面新增数据已保存，未保存数据无法导出')
          },
          success: () => {
            let params = {
              id: this.$route.query.id ? this.$route.query.id : this.$route.query.addTemplate.id
            }
            this.$API.categoryResources.markDetailExport(params).then((res) => {
              const fileName = getHeadersFileName(res)
              download({ fileName: `${fileName}`, blob: res.data })
            })
          }
        })
      } else if (this.$route.query.isDetail === 'detail') {
        let params = {
          id: this.$route.query.id ? this.$route.query.id : this.$route.query.addTemplate.id
        }
        this.$API.categoryResources.markDetailExport(params).then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    },
    // 提交审批
    hSubmit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('请确保当前页面变更的数据已保存，否则可能导致数据提交出错')
        },
        success: () => {
          this.submitLoading = true
          this.$API.categoryResources
            .markSubmitApprove({ id: this.$route.query.id })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('数据提交成功'), type: 'success' })
                this.submitLoading = false
                this.$router.go(-1)
              }
            })
            .catch(() => {
              this.submitLoading = false
            })
        }
      })
    },
    // 导入
    handleImport() {
      this.uploadParams = {
        dotId: this.$route.query.id
      }
      this.requestUrls = {
        templateUrlPre: 'categoryResources',
        uploadUrl: 'markDetailImport',
        file: 'excel'
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({ content: this.$t('导入成功'), type: 'success' })
      this.getCapabilityDetailData()
    }
  }
}
</script>

<style lang="scss" scoped>
.operate-bar {
  // height: 60px;
  display: flex;
  justify-content: end;
  align-items: center;
  color: #4f5b6d;
  .title-left {
    font-weight: 700;
    font-size: 18px;
  }
  .op-item {
    cursor: pointer;
    align-items: center;
    margin-right: 20px;
    align-items: center;
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: #00469c;
  }
}
/deep/ .e-dialog .e-dlg-header-content + .e-dlg-content {
  padding-top: 20px;
}
.tableArea {
  background-color: #fff;
  // height: 100%;
}

.buttons {
  display: flex;
  justify-content: end;
  padding: 0 10px;
}
/deep/ .vxe-cell--title {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
/deep/ .vxe-button .size--mini .type--button {
  height: 20px;
}
</style>
