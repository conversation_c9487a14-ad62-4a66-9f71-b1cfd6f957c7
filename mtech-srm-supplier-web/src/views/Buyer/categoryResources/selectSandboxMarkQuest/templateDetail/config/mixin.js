import { i18n } from '@/main.js'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'

export default {
  components: { VxeRemoteSearch },
  props: {},
  data() {
    return {}
  },
  watch: {},
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      return [
        {
          code: 'add',
          name: this.$t('新增'),
          // icon: 'vxe-icon-square-plus',
          status: 'primary'
          // isHidden: !this.editable
        },
        {
          code: 'delete',
          name: this.$t('删除')
          // icon: 'vxe-icon-delete',
          // isHidden: !this.editable
        }
      ]
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        // {
        //   type: 'seq',
        //   title: i18n.t('序号')
        // },
        {
          field: 'roleName',
          title: i18n.t('分工'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              const selectedItem = this.roleList.find((item) => item.value === row.roleCode)
              const roleName = selectedItem?.label || null
              return [<span>{roleName}</span>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.roleName}
                  options={this.roleList}
                  option-props={{ label: 'label', value: 'value' }}
                  transfer
                  on-change={(item) => {
                    const selectedItem = this.roleList.find((e) => e.value === item.value)
                    row.roleName = selectedItem.label
                    row.roleCode = item.value
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'respUserName',
          title: i18n.t('责任人'),
          // formatter: (data) => {
          //   return data.row.respUser + '-' + data.row.respUserName
          // },
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.respUserName}
                  fileds={{ value: 'accountName', text: 'employeeName' }}
                  request-info={{
                    urlPre: 'categoryRelationshipReport',
                    url: 'getBuyerList',
                    searchKey: 'fuzzyName',
                    recordsPosition: 'data'
                  }}
                  on-change={(item) => {
                    row.respUserName = item?.employeeName || null
                    row.respUser = item.accountName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'approverName',
          title: i18n.t('审批人'),
          // formatter: (data) => {
          //   return data.row.approverAcct + '-' + data.row.approverName
          // }
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.approverName}
                  fileds={{ value: 'accountName', text: 'employeeName' }}
                  request-info={{
                    urlPre: 'categoryRelationshipReport',
                    url: 'getBuyerList',
                    searchKey: 'fuzzyName',
                    recordsPosition: 'data'
                  }}
                  on-change={(item) => {
                    row.approverName = item?.employeeName || null
                    row.approverAcct = item?.accountName || null
                  }}
                />
              ]
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
