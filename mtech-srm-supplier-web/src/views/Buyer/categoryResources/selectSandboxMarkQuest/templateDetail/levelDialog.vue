<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :header="header"
    width="500"
    height="400"
    @beforeClose="cancel"
  >
    <div>
      <template class="capability">
        <div style="margin-top: 24px" />
        <div style="font-weight: bold">{{ $t('等级定义') }}</div>
        <ScTable
          ref="categoryXTable"
          :columns="level"
          :table-data="levelData"
          header-align="center"
          :is-show-right-btn="false"
          :is-show-toolbar="false"
          :auto-height="false"
          height="220"
          :tree-config="{}"
          align="center"
          :edit-config="{
            trigger: 'dblclick',
            mode: 'row',
            showUpdateStatus: true,
            showInsertStatus: true
          }"
        >
        </ScTable>
      </template>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { level } from './config/detail'

export default {
  components: {
    ScTable
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      level,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    levelData() {
      return this.modalData.data
    }
  },
  mounted() {
    console.log('dialogdialogdialog', this.modalData.data)
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped></style>
