import Vue from 'vue'
import { i18n } from '@/main.js'
import utils from '@/utils/utils'

export const ABILITY_TEMPLATE_TYPE = utils.getSupplierDict('SUPPLIER_ABILITY_TEMPLATE_TYPE')

export const columns = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    allowResizing: false,
    customAttributes: {
      class: 'sticky-col-0'
    }
  },
  {
    field: 'templateCode',
    headerText: i18n.t('模板编码'),
    width: '100'
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板名称'),
    width: '120'
  },
  {
    field: 'templateType',
    headerText: i18n.t('模板类型'),
    valueConverter: {
      type: 'map',
      map: ABILITY_TEMPLATE_TYPE,
      fields: { text: 'dictName', value: 'dictCode' }
    },
    width: '100'
  },
  {
    field: 'dimLevel',
    headerText: i18n.t('维度层级'),
    width: '100',
    ignore: true
    // valueConverter: {
    //   type: 'map',
    //   map: {
    //     STRATEGIC_CATEGORY: i18n.t('品类分层分级'),
    //     STRATEGIC_SUPPLIER: i18n.t('战略供应商筛选'),
    //     NOT_STRATEGIC_SUPPLIER: i18n.t('非战略供应商评级')
    //   },
    //   fields: { text: 'label', value: 'status' }
    // }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('生效'),
        2: i18n.t('失效')
      },
      fields: { text: 'label', value: 'status' }
    },
    width: '70',
    ignore: true
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织范围'),
    ignore: true
  },
  {
    field: 'categCodes',
    headerText: i18n.t('应用品类'),
    width: '100',
    ignore: true
  },
  {
    field: 'categNames',
    headerText: i18n.t('品类名称'),
    width: '100',
    searchOptions: {
      elementType: 'remote-autocomplete',
      renameField: 'categoryCode',
      fields: { text: 'categoryName', value: 'categoryCode' },
      params: {},
      multiple: true,
      operator: 'in',
      popupWidth: '300px',
      url: '/masterDataManagement/tenant/category/paged-query',
      placeholder: ' ',
      supplierSearchFields: ['categoryCode', 'categoryName']
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '80',
    ignore: true
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    ignore: true,
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
                <div class="time-box">
                  {{ data.createDate }}
                </div>`
        })
      }
    },
    width: '120'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '80',
    ignore: true
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('最后更新日期'),
    ignore: true,
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
                <div class="time-box">
                  {{ data.modifyDate }}
                </div>`
        })
      }
    },
    width: '120'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '100',
    ignore: true
  }
]
