//评分计划Tab
import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增'), permission: ['O_02_0015'] },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除'), permission: ['O_02_0016'] },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑'), permission: ['O_02_0017'] },
  { id: 'Start', icon: 'icon_table_enable', title: i18n.t('启用'), permission: ['O_02_0018'] },
  { id: 'Stop', icon: 'icon_table_disable', title: i18n.t('停用'), permission: ['O_02_0019'] },
  {
    id: 'Generate',
    icon: 'icon_table_disable',
    title: i18n.t('生成进度'),
    permission: ['O_02_0071']
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'planCode',
    headerText: i18n.t('绩效考评计划编码'),
    cellTools: [
      { id: 'edit', icon: 'icon_Editor', title: i18n.t('编辑'), permission: ['O_02_0017'] },
      { id: 'delete', icon: 'icon_Delete', title: i18n.t('删除'), permission: ['O_02_0016'] }
    ]
  },
  {
    field: 'planName',
    headerText: i18n.t('绩效考评计划名称')
  },
  // {
  //   field: "dimensionName",
  //   headerText: i18n.t("参评供应商清单"),
  // },
  {
    field: 'period',
    headerText: i18n.t('考评周期'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('年度'),
        1: i18n.t('半年度'),
        2: i18n.t('季度'),
        3: i18n.t('月度'),
        4: i18n.t('单日')
      }
    }
  },
  {
    field: 'orgName',
    headerText: i18n.t('发布组织')
  },
  {
    field: 'templateName',
    headerText: i18n.t('绩效模板名称')
  },
  {
    field: 'deadline',
    headerText: i18n.t('有效期限')
  },
  {
    field: 'enabled',
    headerText: i18n.t('状态'),
    width: 200,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Start',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        permission: ['O_02_0018'],
        visibleCondition: (data) => {
          return data['enabled'] == 1
        }
      },
      {
        id: 'Stop',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        permission: ['O_02_0019'],
        visibleCondition: (data) => {
          return data['enabled'] == 0
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const pageConfig = (url) => {
  console.log('url ::', url)
  return [
    {
      gridId: '3d0ccd53-01ba-446d-b2e7-31a648ea5179',
      toolbar,
      grid: {
        columnData,
        asyncConfig: {
          url
        }
      }
    }
  ]
}
