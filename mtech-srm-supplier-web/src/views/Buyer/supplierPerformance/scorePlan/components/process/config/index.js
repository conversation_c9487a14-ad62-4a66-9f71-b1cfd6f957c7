//评分进度Tab
import { i18n } from '@/main.js'
const toolbar = []
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'planCode',
    headerText: i18n.t('评分计划编码'),
    cssClass: ''
  },
  {
    field: 'planName',
    headerText: i18n.t('评分计划名称')
  },
  {
    field: 'period', //考评周期 0：年度 1：半年度 2：季度 3：月度 4：单日（需再选日期）
    headerText: i18n.t('计划周期'),
    valueConverter: {
      type: 'map',
      // map: [i18n.t("年度"), i18n.t("半年度"), i18n.t("季度"), i18n.t("月度"), i18n.t("单日")],
      map: {
        0: i18n.t('年度'),
        1: i18n.t('半年度'),
        2: i18n.t('季度'),
        3: i18n.t('月度'),
        4: i18n.t('单日')
      }
    }
  },
  {
    field: 'periodNo',
    headerText: i18n.t('期号')
  },
  {
    field: 'deadline',
    headerText: i18n.t('提交截至日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'submitFact',
    headerText: i18n.t('已提交份数')
  },
  {
    field: 'submitPlan',
    headerText: i18n.t('计划提交份数')
  },
  {
    field: 'createTime',
    headerText: i18n.t('任务生成时间'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'orgName',
    headerText: i18n.t('发布组织')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('计划创建人')
  }
]

export const pageConfig = (url) => [
  {
    gridId: 'f97cef50-9d0b-406e-8ffc-605b6b219609',
    useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
    toolbar,
    grid: {
      columnData,
      // dataSource: [
      //   {
      //     companyId: 0, //发布公司id
      //     orgName: "公司001", //	发布公司名称
      //     createTime: "2021-09-01", //创建日期
      //     createUserName: "lcj", //创建人
      //     deadline: "2022-09-11", //提交截至日期
      //     id: 0,
      //     period: 1, //考评周期 0：年度 1：半年度 2：季度 3：月度 4：单日（需再选日期）
      //     periodNo: "XD2", //期号
      //     planCode: "CODE001", //计划编码
      //     planId: 0, //	评分计划id
      //     planName: "计划001", //	计划名称
      //     submitFact: 30, //	已提交份数
      //     submitPlan: 60, //计划提交份数
      //     tenantId: 0, //	租户id
      //   },
      // ],
      asyncConfig: {
        url
      }
    }
  }
]
