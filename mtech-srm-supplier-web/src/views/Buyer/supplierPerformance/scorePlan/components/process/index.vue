<template>
  <!-- 评分进度Tab -->
  <div class="score-plan-process">
    <mt-template-page ref="templateRef" :template-config="pageConfig" />
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceScoreSetting.getScoreProcess)
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.score-plan-process {
  height: 100%;
}
</style>
