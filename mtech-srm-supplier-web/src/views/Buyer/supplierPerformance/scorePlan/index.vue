<template>
  <!-- 评分计划页面 -->
  <div class="score-plan">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :use-tool-template="false"
      :template-config="pageConfig"
    >
      <!-- 评分计划 -->
      <tab-dimension slot="slot-0"></tab-dimension>
      <!-- 评分进度 -->
      <tab-process slot="slot-1"></tab-process>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //评分计划
    tabDimension: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/plan" */ './components/plan'
      ),
    //评分进度
    tabProcess: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/process" */ './components/process'
      )
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: '66616753-9e55-4460-b35f-e05bed4f5378',
          title: this.$t('评分计划')
        },
        {
          gridId: '9610e0a3-06cd-462c-a680-1a7a8012af2d',
          title: this.$t('评分进度')
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.score-plan {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
