//参评范围设置Tab
import { i18n } from '@/main.js'
const toolbar = [[], ['Filter', 'Refresh', 'Setting']]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  }
]

export const pageConfig = (url, orgId, defineId) => [
  {
    gridId: 'e219be1b-c4f2-480d-a6ee-04e949f15c89',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        loading: false,
        url,
        params: {
          orgId: orgId
        },
        defaultRules: [
          {
            label: i18n.t('分级'),
            field: 'supplierGradeId',
            type: 'string',
            operator: defineId === '-1' ? 'contains' : 'equal',
            value: defineId === '-1' ? '' : defineId
          }
        ]
      }
    }
  }
]
