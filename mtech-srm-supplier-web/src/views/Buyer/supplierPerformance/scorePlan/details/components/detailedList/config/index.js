//指标定义Tab
import { i18n } from '@/main.js'
const toolbar = [
  [
    'Add',
    { id: 'Stop', icon: 'icon_table_disable', title: i18n.t('禁用') },
    { id: 'Start', icon: 'icon_table_enable', title: i18n.t('启用') },
    'Delete'
  ],
  []
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'orgCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'raterName',
    headerText: i18n.t('评分人姓名')
  },
  // {
  //   field: "raterId",
  //   headerText: i18n.t("评分人账号"),
  // },
  {
    field: 'deptName',
    headerText: i18n.t('评分人部门')
  },
  // {
  //   field: "downOrgName", //
  //   headerText: i18n.t("评分人岗位"),
  // },
  {
    field: 'source',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('默认'),
        1: i18n.t('手动增加')
      }
    }
  },
  {
    field: 'enabled',
    headerText: i18n.t('状态'),
    width: 200,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('禁用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (dataSource) => [
  {
    gridId: '20f6f9d4-1d14-4646-9119-879e15a3d8a0',
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: toolbar
    },
    grid: {
      allowPaging: false,
      columnData,
      dataSource
      // asyncConfig: {
      //   url,
      // },
    }
  }
]
