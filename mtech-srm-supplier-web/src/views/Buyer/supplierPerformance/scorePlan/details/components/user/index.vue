<template>
  <!-- 维度设置Tab -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  props: {
    form: {
      type: Object,
      default: () => {
        return {}
      }
    },
    disabled: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: []
    }
  },
  computed: {
    appointRater() {
      return this.form.appointRater || []
    }
  },
  created() {
    console.log(this.form.appointRater)
    this.pageConfig = pageConfig(this.appointRater)
  },
  watch: {
    appointRater(val) {
      // 可以监听对象的单个属性
      console.log('变化》>>>>>>》', val)
      this.pageConfig = pageConfig(this.appointRater)
    }
  },
  methods: {
    //新增配置
    handleAddConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension/dimensionDialog" */ './components/planDialog.vue'
          ),
        data: {
          title: this.$t('新增特定评分人'),
          id: this.$route.query.id,
          templateId: this.form.templateId
        },
        success: () => {
          this.$emit('init', 'appointRater')
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      //状态 0: this.$t("启用"), 1: this.$t("停用")
      let _params = {
        idList: ids,
        enabled: status
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为') + _statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceScoreSetting.updateDimension(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$emit('init', 'appointRater')
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除配置
    handleDeleteConfig(ids) {
      let _params = {
        ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceScorePlan.specificRater(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$emit('init', 'appointRater')
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
}
</style>
