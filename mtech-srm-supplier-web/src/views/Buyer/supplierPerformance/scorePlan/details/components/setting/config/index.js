//参评范围设置Tab
import { i18n } from '@/main.js'
const toolbar = [['Add', 'Delete'], []]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    cellTools: ['delete']
  },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  }
]

export const pageConfig = (dataSource) => [
  {
    gridId: 'b4e18922-a8d9-40f1-842e-dd0046e06d7e',
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: toolbar
    },
    grid: {
      allowPaging: false,
      columnData,
      dataSource
      // asyncConfig: {
      //   url,
      // },
    }
  }
]
