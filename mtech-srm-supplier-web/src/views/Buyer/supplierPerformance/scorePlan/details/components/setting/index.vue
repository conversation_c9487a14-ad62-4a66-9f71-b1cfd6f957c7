<template>
  <!-- 参评范围 -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  props: {
    form: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: []
    }
  },
  computed: {
    rangeConfig() {
      return this.form.rangeConfig || []
    }
  },
  created() {
    console.log(this.form.rangeConfig)
    this.pageConfig = pageConfig(this.rangeConfig)
  },
  watch: {
    rangeConfig(val) {
      // 可以监听对象的单个属性
      console.log('变化》>>>>>>', val)
      this.pageConfig = pageConfig(this.rangeConfig)
    }
  },
  methods: {
    //新增配置
    handleAddConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension/dimensionDialog" */ './components/planDialog.vue'
          ),
        data: {
          title: this.$t('新增考评供应商'),
          id: this.$route.query.id,
          orgId: this.form.orgId
        },
        success: () => {
          this.$emit('init', 'rangeConfig')
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //删除配置
    handleDeleteConfig(ids) {
      let _params = {
        ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceScorePlan.deleteIndex(_params).then(() => {
            this.$emit('init', 'rangeConfig')
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
}
</style>
