//维度设置Tab
import { i18n } from '@/main.js'
const toolbar = [['Add', 'Delete'], []]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'indexCode',
    headerText: i18n.t('指标编码')
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标名称')
  },
  {
    field: 'dimensionName',
    headerText: i18n.t('所属维度')
  },
  {
    field: 'raterName',
    headerText: i18n.t('评分人姓名')
  },
  // {
  //   field: "raterName",
  //   headerText: i18n.t("评分人账号"),
  // },
  // {
  //   field: "dimensionName",
  //   headerText: i18n.t("评分人岗位"),
  // },
  {
    field: 'type',
    headerText: i18n.t('特定类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('仅限'),
        1: i18n.t('排除')
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const pageConfig = (dataSource) => [
  {
    gridId: '0c8263ad-e2b4-4738-bbd4-70baafc0efb7',
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: toolbar
    },
    grid: {
      allowPaging: false,
      columnData,
      dataSource
      // asyncConfig: {
      //   url,
      // },
    }
  }
]
