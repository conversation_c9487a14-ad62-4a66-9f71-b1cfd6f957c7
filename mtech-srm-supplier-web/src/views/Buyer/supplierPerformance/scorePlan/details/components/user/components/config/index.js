//维度设置Tab
import { i18n } from '@/main.js'
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'orgCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'raterName',
    headerText: i18n.t('评分人姓名')
  }
]

export const pageConfig = (dataSource) => [
  {
    gridId: 'e256e574-9004-4baf-91fc-0fe87be0aa99',
    grid: {
      columnData,
      dataSource
      // asyncConfig: {
      //   url,
      // },
    }
  }
]
