<template>
  <!-- 指标定义Tab -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  props: {
    form: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: []
    }
  },
  computed: {
    raterList() {
      return this.form.raterList || []
    }
  },
  created() {
    console.log(this.form.raterList)
    this.pageConfig = pageConfig(this.raterList)
  },
  watch: {
    raterList(val) {
      // 可以监听对象的单个属性
      console.log('变化》>>>>>>', val)
      this.pageConfig = pageConfig(this.raterList)
    }
  },
  methods: {
    //新增配置
    handleAddConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/target/targetDialog" */ './components/targetDialog.vue'
          ),
        data: {
          title: this.$t('评分人设置'),
          id: this.$route.query.id,
          orgId: this.form.orgId
        },
        success: () => {
          this.$emit('init', 'raterList')
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      //状态 0: this.$t("启用"), 1: this.$t("停用")
      let _params = {
        ids,
        enabled: status
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为') + _statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceScorePlan.updateIndex(_params).then(() => {
            this.$emit('init', 'raterList')
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除配置
    handleDeleteConfig(ids) {
      let _params = {
        ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceScorePlan.deleteRater(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$emit('init', 'raterList')
            // this.$refs.templateRef.refreshCurrentGridData();
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
}
</style>
