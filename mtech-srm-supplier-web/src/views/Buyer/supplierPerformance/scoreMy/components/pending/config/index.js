//维度设置Tab
import Vue from 'vue'
import { i18n } from '@/main.js'
const toolbar = [{ id: 'Start', icon: 'icon_solid_submit', title: i18n.t('提交') }]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'planCode',
    headerText: i18n.t('评分计划编码'),
    cellTools: []
  },
  {
    field: 'planName',
    headerText: i18n.t('绩效考评计划名称')
  },
  {
    field: 'period',
    headerText: i18n.t('考评周期'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('年度'),
        1: i18n.t('半年度'),
        2: i18n.t('季度'),
        3: i18n.t('月度'),
        4: i18n.t('单日')
      }
    }
  },
  {
    field: 'periodNo',
    headerText: i18n.t('期号')
  },
  {
    field: 'deadline',
    headerText: i18n.t('提交截至日期')
  },
  {
    field: 'finishedCount', //totalCount
    headerText: i18n.t('完成度'),
    template: () => {
      return {
        template: Vue.component('maxScore', {
          template: `
              <div>
                {{data.finishedCount}}/{{data.totalCount}}
              </div>`,
          data: function () {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'roleName',
    headerText: i18n.t('角色')
  },
  {
    field: 'orgName',
    headerText: i18n.t('代表组织')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('计划创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('任务创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    gridId: 'f9a2e7a6-b735-48cc-9527-4e69c10da9b9',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
