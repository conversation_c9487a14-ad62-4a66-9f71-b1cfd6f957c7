<template>
  <!-- 维度设置Tab -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      index: 0,
      pageConfig: pageConfig(this.$API.performanceScoreMy.myList)
    }
  },
  methods: {
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids) {
      let _params = ids
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认提交？`)
        },
        success: () => {
          this.$API.performanceScoreMy.addMyData(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
}
</style>
