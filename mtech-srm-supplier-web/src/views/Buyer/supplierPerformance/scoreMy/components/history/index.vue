<template>
  <!-- 维度设置Tab -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      index: 1,
      pageConfig: pageConfig(this.$API.performanceScoreMy.myQueryHistory)
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
}
</style>
