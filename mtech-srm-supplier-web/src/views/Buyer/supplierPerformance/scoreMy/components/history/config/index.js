//维度设置Tab
import { i18n } from '@/main.js'
const toolbar = []
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'planCode',
    headerText: i18n.t('评分计划编码'),
    cellTools: []
  },
  {
    field: 'planName',
    headerText: i18n.t('评分计划名称')
  },
  {
    field: 'period',
    headerText: i18n.t('考评周期'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('年度'),
        1: i18n.t('半年度'),
        2: i18n.t('季度'),
        3: i18n.t('月度'),
        4: i18n.t('单日')
      }
    }
  },
  {
    field: 'periodNo',
    headerText: i18n.t('期号')
  },
  {
    field: 'deadline',
    headerText: i18n.t('提交截至日期')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未完成'),
        1: i18n.t('已完成'),
        2: i18n.t('已提交'),
        3: i18n.t('过期未提交')
      }
    }
    // template: () => {
    //   return {
    //     template: Vue.component("maxScore", {
    //       template: `
    //           <div>
    //           <mt-tag>{{arr[data.status]}}</mt-tag>
    //           </div>`,
    //       data: function () {
    //         return {
    //           data: {},
    //           arr: {
    //             0: i18n.t("未完成"),
    //             1: i18n.t("已完成"),
    //             2: i18n.t("已提交"),
    //             3: i18n.t("过期未提交"),
    //           },
    //         };
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'roleName',
    headerText: i18n.t('角色')
  },
  {
    field: 'orgName',
    headerText: i18n.t('代表组织')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('计划创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('任务创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    gridId: '52c653d2-4178-446d-831e-b75cc32e5a00',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
