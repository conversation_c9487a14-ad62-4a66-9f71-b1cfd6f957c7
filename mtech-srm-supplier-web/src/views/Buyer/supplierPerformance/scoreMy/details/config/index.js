//维度设置Tab
import Vue from 'vue'
import { i18n } from '@/main.js'
const toolbar = [{ id: 'Start', icon: 'icon_Editor', title: i18n.t('批量打分') }]
const columnData = (canEdit) => {
  return [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'indexName',
      headerText: i18n.t('指标名称')
    },
    {
      field: 'dimensionName',
      headerText: i18n.t('维度')
    },
    {
      field: 'description',
      headerText: i18n.t('评分标准说明')
    },
    {
      field: 'supplierEnterpriseCode',
      headerText: i18n.t('供应商编码')
    },
    {
      field: 'supplierEnterpriseName',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'maxScore', //minScore
      headerText: i18n.t('打分范围'),
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div>
                  {{data.minScore}}-{{data.maxScore}}
                </div>`,
            data: function () {
              return { data: {} }
            }
          })
        }
      }
    },
    {
      field: 'weight',
      headerText: i18n.t('指标权重')
    },
    {
      width: '180',
      field: 'num',
      headerText: i18n.t('打分'),
      template: () => {
        return {
          template: Vue.component('range', {
            template: `
                <div>
                  <div v-if="canEdit">{{data.score}}</div>
                  <mt-inputNumber v-else
                      ref="inputNumber"
                      v-model.number="data.score"
                      cssClass="e-outline"
                      :max="data.maxScore"
                      :min="data.minScore"
                      @change="change"
                  ></mt-inputNumber>
                </div>`,
            data: function () {
              return { data: {}, canEdit }
            },
            methods: {
              change(val) {
                this.data.score = val
                this.$parent.$emit('change', this.data)
              }
            }
          })
        }
      }
    }
  ]
}

export const pageConfig = (canEdit) => [
  {
    gridId: '09698df9-43ad-4e19-aec0-93abf591c062',
    toolbar,
    grid: {
      columnData: columnData(canEdit),
      dataSource: []
      // asyncConfig: {
      //   url: '/analysis/tenant/buyer/score/my/detail/indexResponseList',
      //   defaultRules: [
      //     {
      //       "operator": "equal",
      //       "field": "instanceId",
      //       "type": "Long",
      //       "value": instanceId,
      //       "label": i18n.t("实例id")
      //     }
      //   ],
      //   page: {
      //     current: 1,
      //     size: 200000,
      //   },
      //   afterAsyncData: updateData
      // },
    }
  }
]
