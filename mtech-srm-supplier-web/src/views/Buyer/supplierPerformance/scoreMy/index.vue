<template>
  <!-- 评分配置页面 -->
  <div class="score-setting">
    <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig">
      <!-- 待处理 -->
      <tab-pending slot="slot-0" index="0"></tab-pending>
      <!-- 历史考评 -->
      <tab-history slot="slot-1" index="1"></tab-history>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //待处理
    tabPending: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension" */ './components/pending'
      ),
    //历史考评
    tabHistory: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/target" */ './components/history'
      )
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: '89309458-b7fd-432c-ac33-0ccd77712a85',
          title: this.$t('待处理')
        },
        {
          gridId: '9d631b03-37dc-4cdd-8f29-ce2de6bb6306',
          title: this.$t('历史考评')
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
