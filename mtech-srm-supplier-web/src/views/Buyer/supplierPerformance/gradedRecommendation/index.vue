<template>
  <!-- 评分配置页面 -->
  <div class="score-setting">
    <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig">
      <!-- 待处理推荐 -->
      <tab-pending slot="slot-0" index="0"></tab-pending>
      <!-- 处理历史 -->
      <tab-history slot="slot-1" index="1"></tab-history>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //待处理推荐
    tabPending: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension" */ './components/pending'
      ),
    //历史考评
    tabHistory: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/target" */ './components/history'
      )
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: 'e68f7f72-2f3d-4e50-a5a9-e08544da7473',
          title: this.$t('待处理推荐')
        },
        {
          gridId: 'd7481444-af71-4eef-a853-ffc1ebe0fe28',
          title: this.$t('历史考评')
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
