export default {
  methods: {
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (_selectRecords.length < 1) {
        this.$toast({ content: '请先选择一行', type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Start') {
        this.handleBatchStart(_selectRecords, 0)
      } else if (e.toolbar.id === 'Stop') {
        this.handleBatchStart(_selectRecords, 1)
      }
    },
    //批量提交操作
    handleBatchStart(_selectRecords, value) {
      //状态 0: "启用", 1: "停用"
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleUpdateConfigStatus(_selectIds, value)
    }
  }
}
