<template>
  <!-- 维度设置Tab -->
  <div class="score-setting">
    <div style="margin: 20px">
      <mt-select
        width="300"
        id="checkboxTreeSelect"
        :fields="{
          text: 'orgName',
          value: 'orgId'
        }"
        :data-source="orgList"
        :show-clear-button="true"
        :placeholder="$t('请选择发布组织')"
        @change="companyList"
      ></mt-select>
    </div>
    <mt-template-page ref="templateRef" :template-config="pageConfig" />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      index: 1,
      organization: '',
      orgList: [],
      pageConfig: pageConfig(this.$API.performanceScoreGrade.queryHistory)
    }
  },
  created() {
    this.$API.performanceScoreGrade.orgList().then((res) => {
      console.log(res)
      this.orgList = res.data
    })
  },
  methods: {
    companyList(e) {
      console.log(e)
      this.$set(
        this.pageConfig[0].grid.asyncConfig,
        'defaultRules',
        e.itemData
          ? [
              {
                condition: 'equal',
                field: 'orgId',
                label: this.$t('公司ID'),
                operator: 'equal',
                type: 'string',
                value: e.itemData.orgId //设置数据
              }
            ]
          : []
      )
      // this.$refs.templateRef.refreshCurrentGridData();
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  background: white;
  height: 100%;
  overflow: hidden;
  /deep/.cssClass {
    border: 1px solid rgba(0, 0, 0, 0.42);
  }
}
</style>
