//维度设置Tab
import { i18n } from '@/main.js'
const toolbar = []
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'orgCode',
    headerText: i18n.t('组织编码')
  },
  {
    field: 'orgName',
    headerText: i18n.t('组织名称')
  },
  {
    field: 'scoreSource',
    headerText: i18n.t('考评源')
  },
  {
    field: 'score',
    headerText: i18n.t('综合得分')
  },
  {
    field: 'gradeName',
    headerText: i18n.t('建议级别')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('待处理'),
        1: i18n.t('采纳'),
        2: i18n.t('关闭'),
        3: i18n.t('被覆盖')
      }
    }
  },
  {
    field: 'approveUserName',
    headerText: i18n.t('处理人')
  },
  {
    field: 'approveTime',
    headerText: i18n.t('处理日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    gridId: 'dfde06c2-5cad-4b2f-8714-44e3df645586',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url: url
      }
    }
  }
]
