<template>
  <!-- 维度设置Tab -->
  <div class="score-setting">
    <div style="margin: 20px">
      <mt-select
        width="300"
        id="checkboxTreeSelect"
        :fields="{
          text: 'orgName',
          value: 'orgId'
        }"
        :data-source="orgList"
        :show-clear-button="true"
        :placeholder="$t('请选择发布组织')"
        @change="companyList"
      ></mt-select>
    </div>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      index: 0,
      orgList: [],
      pageConfig: pageConfig(this.$API.performanceScoreGrade.planList)
    }
  },
  created() {
    this.$API.performanceScoreGrade.orgList().then((res) => {
      console.log(res)
      this.orgList = res.data
    })
    // this.pageConfig = pageConfig(this.$API.performanceScoreGrade.planList);
  },
  methods: {
    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.id === 'Start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.id], 0)
      } else if (e.tool.id === 'Stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.id], 1)
      }
    },
    companyList(e) {
      console.log(e)
      this.$set(
        this.pageConfig[0].grid.asyncConfig,
        'defaultRules',
        e.itemData
          ? [
              {
                condition: 'equal',
                field: 'orgId',
                label: this.$t('公司ID'),
                operator: 'equal',
                type: 'string',
                value: e.itemData.orgId //设置数据
              }
            ]
          : []
      )
      // this.$refs.templateRef.refreshCurrentGridData();
    },
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      let _params = ids
      let _statusMap = [this.$t('采纳'), this.$t('关闭')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `确认${_statusMap[status]}？`
        },
        success: () => {
          let str = 'adopt'
          if (status) str = 'close'
          console.log(str, status)
          this.$API.performanceScoreGrade[str](_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  background: white;
  height: 100%;
  overflow: hidden;
  /deep/.cssClass {
    border: 1px solid rgba(0, 0, 0, 0.42);
  }
}
</style>
