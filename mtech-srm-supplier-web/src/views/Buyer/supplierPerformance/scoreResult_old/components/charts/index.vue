<template>
  <!-- 图表视图Tab -->
  <div class="score-result-charts">
    <slot-filter :filter-params="filterParams" @changeFilterData="changeFilterData"></slot-filter>
    <div class="charts-container mt-flex">
      <div class="left-panel mt-flex-direction-column">
        <div class="top-panel">
          <chart-top :chart-list="top10Data"></chart-top>
        </div>
        <div class="bottom-panel">
          <chart-bottom :chart-list="bottom10Data"></chart-bottom>
        </div>
      </div>
      <div class="right-panel mt-flex-direction-column">
        <div class="chart-filter mt-flex">
          <mt-form>
            <mt-form-item :label="$t('选择供应商')">
              <mt-select
                ref="supplierRef"
                float-label-type="Never"
                v-model="filterParams.selectSupplier"
                :data-source="supplierList"
                :placeholder="$t('请选择供应商')"
                @select="selectSupplier"
                :fields="{
                  text: 'supplierEnterpriseName',
                  value: 'supplierEnterpriseId'
                }"
              >
              </mt-select>
            </mt-form-item>
          </mt-form>
          <div class="option-panel mt-flex">
            <div class="option-item" @click="doGenerateLevel">
              <mt-icon name="icon_table_setLevel" />
              <span>{{ $t('分级') }}</span>
            </div>
            <!-- <div class="option-item">
              <mt-icon name="icon_solid_submit" />
              <span>{{ $t('发送给供应商') }}</span>
            </div> -->
          </div>
        </div>
        <div class="line-panel">
          <chart-line :chart-list="lineData"></chart-line>
        </div>
        <div class="radar-panel">
          <chart-radar :chart-list="radarData"></chart-radar>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    //顶部扩展搜索部分
    slotFilter: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/components/filter" */ '../filter.vue'
      ),
    chartTop: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/charts/components/top" */ './components/top.vue'
      ),
    chartBottom: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/charts/components/bottom" */ './components/bottom.vue'
      ),
    chartLine: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/charts/components/line" */ './components/line.vue'
      ),
    chartRadar: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/charts/components/radar" */ './components/radar.vue'
      )
  },
  data() {
    return {
      filterParams: {
        // orgId: "1399202858620522497",
        // planId: 1,
        orgId: null,
        planId: null,
        selectSupplier: null, //当前选中的供应商
        supplierCount: 0,
        instanceCount: 0
      },
      top10Data: [],
      bottom10Data: [],
      lineData: [],
      radarData: [],
      supplierList: []
    }
  },
  mounted() {
    // this.getResultRankData(); //获取top-10,bottom-10列表
    // this.getSupplierSelectList(); //获取供应商下拉框数据
    // this.getSupplierLineDataMock();
    // this.getSupplierRodarDataMock();
  },
  methods: {
    changeFilterData(data) {
      let _org = data.org
      let _plan = data.plan
      if (
        _org &&
        _org.ownerOrgId &&
        Array.isArray(_org.ownerOrgId) &&
        _org.ownerOrgId.length === 1
      ) {
        this.filterParams.orgId = _org.ownerOrgId[0]
      } else {
        this.filterParams.orgId = null
      }
      if (_plan && _plan.id) {
        this.filterParams.planId = _plan.id
      } else {
        this.filterParams.planId = null
      }
      console.log(this.$t('当前筛选数据'), this.filterParams)
      if (this.filterParams.orgId && this.filterParams.planId) {
        this.getResultRankData() //获取top-10,bottom-10列表
        this.getSupplierSelectList() //获取供应商下拉框数据
      }
    },
    //供应商下拉列表
    getSupplierSelectList() {
      this.$API.performanceScoreResult
        .getSupplierSelectList({
          orgId: this.filterParams.orgId,
          planId: this.filterParams.planId
        })
        .then((res) => {
          console.log('测试数据--下拉列表', res)
          if (Array.isArray(res.data)) {
            this.supplierList = res.data
            if (this.supplierList.length) {
              this.filterParams.selectSupplier = this.supplierList[0]['supplierEnterpriseId']
              this.getSupplierLineData() //获取折线图数据
              this.getSupplierRodarData() //获取雷达数据
            }
          } else {
            this.supplierList = []
            this.lineData = []
            this.radarData = []
          }
        })
    },
    //切换供应商
    selectSupplier(e) {
      console.log('handleSelectChange--', e)
      let _data = e.itemData
      this.$set(this.filterParams, 'selectSupplier', _data['supplierEnterpriseId'])
      console.log('切换供应商--', this.filterParams)
      this.getSupplierLineData() //获取折线图数据
      this.getSupplierRodarData() //获取雷达数据
    },
    //分级操作
    doGenerateLevel() {
      if (this.filterParams.orgId && this.filterParams.selectSupplier) {
        console.log('执行分级操作')
        this.$API.performanceScoreResult
          .generateLevel({
            supplierEnterpriseId: this.filterParams.selectSupplier,
            orgId: this.filterParams.orgId
          })
          .then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          })
      } else if (!this.filterParams.orgId) {
        this.$toast({
          content: this.$t('当前未选择"所属组织"'),
          type: 'warning'
        })
        return
      } else {
        this.$toast({
          content: this.$t('当前未选择"供应商"'),
          type: 'warning'
        })
        return
      }
    },
    //获取top-10,bottom-10列表
    getResultRankData() {
      //top-10 - top10Data
      this.$API.performanceScoreResult
        .getSupplierRanks({
          orderBy: 'desc',
          orgId: this.filterParams.orgId,
          planId: this.filterParams.planId
        })
        .then((res) => {
          console.log('top-10', res)
          let _data = res.data
          this.filterParams.instanceCount = _data.instanceCount
          this.filterParams.supplierCount = _data.supplierCount
          this.top10Data = _data.supplierRankDTOList
          // this.getSupplierLineDataMock();
          // this.getSupplierRodarDataMock();
        })
      //bottom-10 - bottom10Data
      this.$API.performanceScoreResult
        .getSupplierRanks({
          orderBy: 'asc',
          orgId: this.filterParams.orgId,
          planId: this.filterParams.planId
        })
        .then((res) => {
          console.log('bottom-10', res)
          let _data = res.data
          this.filterParams.instanceCount = _data.instanceCount
          this.filterParams.supplierCount = _data.supplierCount
          this.bottom10Data = _data.supplierRankDTOList
        })
    },
    //获取折线图数据
    getSupplierLineData() {
      this.$API.performanceScoreResult
        .getSupplierLineData({
          // orgId: this.filterParams.orgId,
          // planId: this.filterParams.planId,
          supplierEnterpriseId: this.filterParams.selectSupplier
        })
        .then((res) => {
          console.log('测试数据--折线图', res)
          if (res.data && Array.isArray(res.data.supplierDimensionDTOList)) {
            this.lineData = res.data.supplierDimensionDTOList
          } else {
            this.lineData = []
          }
        })
    },
    //获取雷达数据
    getSupplierRodarData() {
      this.$API.performanceScoreResult
        .getSupplierRodarData({
          // orgId: this.filterParams.orgId,
          // planId: this.filterParams.planId,
          supplierEnterpriseId: this.filterParams.selectSupplier
        })
        .then((res) => {
          console.log('测试数据--雷达图', res)
          if (res.data && Array.isArray(res.data.supplierDimensionDTOList)) {
            this.radarData = res.data.supplierDimensionDTOList
          } else {
            this.radarData = []
          }
        })
    }
    // getSupplierLineDataMock() {
    //   let res = {
    //     code: 0,
    //     data: {
    //       supplierDimensionDTOList: [
    //         [
    //           {
    //             dimensionName: this.$t("质量"),
    //             score: 30,
    //           },
    //           {
    //             dimensionName: this.$t("质量"),
    //             score: 344,
    //           },
    //           {
    //             dimensionName: this.$t("质量"),
    //             score: 258,
    //           },
    //           {
    //             dimensionName: this.$t("质量"),
    //             score: 17.4,
    //           },
    //         ],
    //         [
    //           {
    //             dimensionName: this.$t("数量"),
    //             score: 18.8,
    //           },
    //           {
    //             dimensionName: this.$t("数量"),
    //             score: 151,
    //           },
    //           {
    //             dimensionName: this.$t("数量"),
    //             score: 143.75,
    //           },
    //           {
    //             dimensionName: this.$t("数量"),
    //             score: 127.45,
    //           },
    //         ],
    //       ],
    //       scoreList: [48.8, 495, 401.75, 144.85],
    //     },
    //     errorStackTrace: "",
    //     msg: "",
    //   };
    //   if (res.data && Array.isArray(res.data.supplierDimensionDTOList)) {
    //     this.lineData = res.data.supplierDimensionDTOList;
    //   } else {
    //     this.lineData = [];
    //   }
    // },
    // getSupplierRodarDataMock() {
    //   let res = {
    //     code: 0,
    //     data: {
    //       scoreList: [], //	供应商总得分List
    //       supplierDimensionDTOList: [
    //         [
    //           {
    //             dimensionName: this.$t("质量"),
    //             score: 30,
    //             total: 50,
    //           },
    //         ],
    //         [
    //           {
    //             dimensionName: this.$t("数量"),
    //             score: 18.8,
    //             total: 100,
    //           },
    //         ],
    //       ],
    //     },
    //     errorStackTrace: "",
    //     msg: "",
    //   };
    //   if (res.data && Array.isArray(res.data.supplierDimensionDTOList)) {
    //     this.radarData = res.data.supplierDimensionDTOList;
    //   } else {
    //     this.radarData = [];
    //   }
    //   console.log("页面数据----", this.radarData);
    // },
  }
}
</script>

<style lang="scss" scoped>
.score-result-charts {
  height: 100%;
  width: 100%;
  // background: rgba(255, 255, 255, 1);
  // border: 1px solid rgba(232, 232, 232, 1);
  // border-radius: 8px 8px 0 0;
  // box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .charts-container {
    background: #fff;
    justify-content: space-around;
    .left-panel,
    .right-panel {
      flex: 1;
      padding: 10px;
    }
    .top-panel,
    .bottom-panel,
    .line-panel,
    .radar-panel {
      // min-width: 300px;
      // min-height: 300px;
      flex: 1;
    }
    .right-panel {
      .chart-filter {
        padding: 0 10px;
        justify-content: space-between;
        align-items: center;
        .option-panel {
          .option-item {
            cursor: pointer;
            padding: 0;
            display: flex;
            align-items: center;
            position: relative;
            color: #4f5b6d;
            .mt-icons {
              margin-right: 6px;
            }
            span {
              word-break: keep-all;
              font-size: 14px;
            }

            &:not(:first-of-type) {
              margin-left: 20px;
            }
          }
        }
      }
    }

    /deep/.chart-panel {
      .chart-title {
        font-size: 16px;
        color: #292929;
        display: inline-block;
        padding-left: 13px;
        position: relative;
        margin-bottom: 20px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 2px;
        }
      }
    }
  }
}
</style>
