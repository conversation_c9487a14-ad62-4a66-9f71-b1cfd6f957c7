<template>
  <!-- 绩效变化，折线图 -->
  <div class="chart-panel mt-flex-direction-column">
    <div class="chart-title">{{ $t('绩效变化') }}</div>
    <div class="content" id="lineList" style="height: 420px"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
let chartDom = null
let myChart = null
export default {
  props: {
    chartList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chartOptions: {
        color: [
          '#00469C',
          '#42A2F1',
          '#8ACC40',
          '#EDA133',
          '#ED5633',
          '#4D7EBA',
          '#7BBEF5',
          '#ADDB7A',
          '#F2BD71',
          '#F28971',
          '#85A7D0',
          '#A5D3F9',
          '#C7E7A4',
          '#F7D29D',
          '#F7AE9D',
          '#B4C9E2',
          '#C8E4FB',
          '#DDF0C7',
          '#FAE4C3',
          '#FACDC3'
        ],
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          bottom: 5
        },
        grid: {
          left: 20,
          right: 20,
          bottom: 30,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#333333',
              width: 1,
              type: 'solid',
              opacity: 0.75
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          max: 100,
          min: 0,
          interval: 20,
          axisLabel: {
            color: '#9A9A9A'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#333333',
              width: 1,
              type: 'dashed',
              opacity: 0.25
            }
          }
        },
        series: [
          // {
          //   name: "",
          //   type: "line",
          //   data: [20],
          // },
        ]
      }
    }
  },
  mounted() {
    chartDom = document.getElementById('lineList')
    myChart = echarts.init(chartDom)
    this.initChart()
  },
  methods: {
    initChart() {
      setTimeout(() => {
        myChart.clear()
        myChart.setOption(this.chartOptions)
      }, 20)
    }
  },
  watch: {
    chartList: {
      handler(n) {
        console.log('todo---折线图数据', n)
        let _series = [],
          _xData = []
        if (Array.isArray(n) && n.length) {
          n.forEach((e, i) => {
            if (Array.isArray(e) && e.length) {
              let _data = []
              e.forEach((d) => {
                _data.push(d.score)
              })
              if (i < 1) {
                e.forEach((d) => {
                  _xData.push(d.periodNo)
                })
              }
              _series.push({
                name: `${e[0].dimensionName}`,
                type: 'line',
                data: _data
              })
            }
          })
        }
        this.$set(this.chartOptions, 'series', _series)
        this.$set(this.chartOptions.xAxis, 'data', _xData)
        this.initChart()
      },
      deep: true
    }
  },
  beforeDestroy() {
    chartDom = null
    myChart = null
  }
}
</script>
