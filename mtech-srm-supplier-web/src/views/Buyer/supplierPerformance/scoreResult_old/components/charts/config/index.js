// import { i18n } from '@/main.js'
//指标定义Tab
// const toolbar = [
//   { id: 'Check', icon: 'icon_solid_edit', title: i18n.t('分级') },
//   { id: 'Share', icon: 'icon_solid_edit', title: i18n.t('分享') },
//   { id: 'Send', icon: 'icon_solid_edit', title: i18n.t('发布给供应商') }
// ]
// const columnData = [
//   {
//     type: 'checkbox',
//     width: 50
//   },
//   {
//     field: 'supplierEnterpriseCode',
//     headerText: i18n.t('供应商编码'),
//     cssClass: ''
//   },
//   {
//     field: 'supplierEnterpriseName',
//     headerText: i18n.t('供应商名称')
//   },
//   {
//     field: 'planCode',
//     headerText: i18n.t('考评计划编码'),
//     width: 200
//   },
//   {
//     field: 'planName',
//     headerText: i18n.t('考评计划名称')
//   },
//   {
//     field: 'field1',
//     headerText: i18n.t('考评公布时间')
//   },
//   {
//     field: 'field1',
//     headerText: i18n.t('考评得分')
//   },
//   {
//     field: 'field1',
//     headerText: '考评得分/满分'
//   },
//   {
//     field: 'field1',
//     headerText: i18n.t('参评公司数')
//   },
//   {
//     field: 'field1',
//     headerText: i18n.t('参评问卷数')
//   }
// ]

export const pageConfig = () => [
  {
    // toolbar,
    // grid: {
    //   columnData,
    //   dataSource: [],
    //   // asyncConfig: {
    //   //   url,
    //   // },
    // },
  }
]
