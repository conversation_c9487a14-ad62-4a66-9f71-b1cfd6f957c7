<template>
  <!-- 列表视图Tab -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
      <slot-filter
        slot="slot-filter"
        :filter-params="filterParams"
        @changeFilterData="changeFilterData"
      ></slot-filter>
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  components: {
    //顶部扩展搜索部分
    slotFilter: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/components/filter" */ '../filter.vue'
      )
  },
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceScoreResult.getSupplierScoreResult),
      filterParams: {
        orgId: null,
        planId: null,
        supplierCount: 0,
        instanceCount: 0
      }
    }
  },
  mounted() {
    // this.getResultColumnData();
  },
  methods: {
    changeFilterData(data) {
      let _rules = []
      let _org = data.org
      let _plan = data.plan
      if (
        _org &&
        _org.ownerOrgId &&
        Array.isArray(_org.ownerOrgId) &&
        _org.ownerOrgId.length === 1
      ) {
        _rules.push({
          condition: 'and',
          field: 'orgId',
          operator: 'equal',
          type: 'string',
          value: _org.ownerOrgId[0] //设置数据
        })
        this.filterParams.orgId = _org.ownerOrgId[0]
      } else {
        this.filterParams.orgId = null
      }
      if (_plan && _plan.id) {
        _rules.push({
          condition: 'and',
          field: 'planId',
          operator: 'equal',
          type: 'string',
          value: _plan.id //设置数据
        })
        this.filterParams.planId = _plan.id
      } else {
        this.filterParams.planId = null
      }
      //更新组织ID，刷新列表数据
      this.$set(this.pageConfig[0].grid.asyncConfig, 'defaultRules', _rules)
      this.getResultColumnData()
    },
    getResultColumnData() {
      //top-10 - top10List
      if (this.filterParams.planId && this.filterParams.orgId) {
        this.$API.performanceScoreResult
          .getSupplierRanks({
            orderBy: 'asc',
            orgId: this.filterParams.orgId,
            planId: this.filterParams.planId
          })
          .then((res) => {
            let _data = res.data
            this.filterParams.instanceCount = _data.instanceCount
            this.filterParams.supplierCount = _data.supplierCount
          })
      }
    },
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (e.toolbar.id == 'Check') {
        if (_selectRecords.length < 1) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else if (_selectRecords.length > 1) {
          this.$toast({ content: this.$t('只支持单条数据的分级操作'), type: 'warning' })
        } else {
          //分级操作
          this.doGenerateLevel(_selectRecords[0])
        }
      }
    },
    //分级操作
    doGenerateLevel(record) {
      if (record.orgId && record.supplierEnterpriseId) {
        console.log('执行分级操作')
        this.$API.performanceScoreResult
          .generateLevel({
            supplierEnterpriseId: record.supplierEnterpriseId,
            orgId: record.orgId
          })
          .then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          })
      } else if (!this.filterParams.orgId) {
        this.$toast({
          content: this.$t('当前未选择"所属组织"'),
          type: 'warning'
        })
        return
      } else {
        this.$toast({
          content: this.$t('当前未选择"供应商"'),
          type: 'warning'
        })
        return
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
}
</style>
