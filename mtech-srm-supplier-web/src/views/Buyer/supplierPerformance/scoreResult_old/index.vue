<template>
  <!-- 评分结果页面 -->
  <div class="score-setting">
    <mt-template-page ref="templateRef" :template-config="pageConfig">
      <!-- 图表视图 -->
      <tab-charts slot="slot-0"></tab-charts>
      <!-- 列表视图 -->
      <tab-list slot="slot-1"></tab-list>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //图表视图
    tabCharts: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/charts" */ './components/charts'
      ),
    //列表视图
    tabList: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreResult/list" */ './components/list'
      )
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: 'd8c6bf95-a07f-482c-ab9c-2f420f3bcde6',
          title: this.$t('图表视图')
        },
        {
          gridId: 'b4bd5a3f-551b-496b-ac51-6e4ff95e7b0e',
          title: this.$t('列表视图')
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
}
</style>
