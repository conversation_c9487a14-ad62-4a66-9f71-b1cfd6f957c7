//指标定义Tab
import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Check', icon: 'icon_solid_edit', title: i18n.t('结果确认'), permission: ['O_02_0020'] }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    cssClass: ''
  },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'planCode',
    headerText: i18n.t('考评计划编码'),
    width: 200
  },
  {
    field: 'planName',
    headerText: i18n.t('考评计划名称')
  },
  {
    field: 'publishTime',
    headerText: i18n.t('考评公布时间')
  },
  {
    field: 'score',
    headerText: i18n.t('考评得分')
  },
  {
    field: 'maxScore',
    headerText: '考评得分/满分'
  },
  {
    field: 'companyCount',
    headerText: i18n.t('参评公司数')
  },
  {
    field: 'instanceCount',
    headerText: i18n.t('参评问卷数')
  }
]

export const pageConfig = (url, orgId) => {
  if (url && orgId) {
    return [
      {
        gridId: '4c16e337-db68-4aef-88d9-c7b89bf39f9f',
        title: i18n.t('待处理'),
        useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
        toolbar,
        grid: {
          columnData,
          asyncConfig: {
            url,
            defaultRules: [
              {
                condition: 'and',
                field: 'orgId',
                operator: 'equal',
                type: 'string',
                value: orgId //设置数据
              },
              {
                condition: 'and',
                field: 'status',
                operator: 'equal',
                type: 'integer',
                value: 0 //是否已确认 0否1是
              }
            ]
          }
        }
      },
      {
        gridId: '9ae6c584-670b-4c75-b400-9e62c725fbea',
        title: i18n.t('已确认'),
        useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
        toolbar: [],
        grid: {
          columnData,
          asyncConfig: {
            url,
            defaultRules: [
              {
                condition: 'and',
                field: 'orgId',
                operator: 'equal',
                type: 'string',
                value: orgId //设置数据
              },
              {
                condition: 'and',
                field: 'status',
                operator: 'equal',
                type: 'integer',
                value: 1 //是否已确认 0否1是
              }
            ]
          }
        }
      }
    ]
  } else {
    return [
      {
        gridId: '3a5a0d50-945b-413a-ae46-d128988d0140',
        title: i18n.t('待处理'),
        useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
        toolbar,
        grid: {
          columnData,
          dataSoure: []
        }
      },
      {
        gridId: '13831ac4-f9ff-4fb8-a3f3-f6947594ca53',
        title: i18n.t('已确认'),
        useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
        toolbar: [],
        grid: {
          columnData,
          dataSoure: []
        }
      }
    ]
  }
}
