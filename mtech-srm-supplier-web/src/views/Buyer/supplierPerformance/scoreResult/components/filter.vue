<template>
  <!-- 顶部-公用数据过滤 -->
  <div v-if="companyTreeFields.dataSource.length" class="filter-panel">
    <mt-DropDownTree
      class="filter-item"
      :id="'org_' + new Date().getTime()"
      ref="ownerOrgNameRef"
      :placeholder="$t('请选择所属组织')"
      v-model="ownerOrg.ownerOrgId"
      :fields="companyTreeFields"
      :show-clear-button="false"
      @select="selectOwnerOrg"
    ></mt-DropDownTree>
  </div>
</template>

<script>
export default {
  props: {
    filterParams: {
      type: Object,
      default: () => {
        return {
          orgId: null,
          planId: null,
          supplierCount: 0,
          instanceCount: 0
        }
      }
    }
  },
  data() {
    return {
      ownerOrg: { ownerOrgId: null, ownerOrgName: null },
      companyTreeFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      companyTreeList: [], //当前组织列表--Tree
      flatCompanyTreeList: [] //当前组织列表--Array
    }
  },
  mounted() {
    this.getCompanyTree()
  },
  methods: {
    getCompanyTree() {
      this.$API.performanceScoreSetting
        .getStatedLimitTree({
          orgLevelCode: 'ORG02',
          orgType: 'ORG001PRO'
        })
        .then((res) => {
          let _data = []
          if (res && res.data && Array.isArray(res.data)) {
            _data = res.data
          }
          this.companyTreeList = _data
          this.flatCompanyTreeList = this.flatTreeData(_data)
          this.companyTreeFields.dataSource = []
          this.$nextTick(() => {
            this.$set(this.companyTreeFields, 'dataSource', _data)
            console.log(
              '当前组织树-数据',
              this.companyTreeFields,
              this.companyTreeList,
              this.flatCompanyTreeList
            )
          })
        })
    },
    //选取组织数据
    selectOwnerOrg(e) {
      if (e.action !== 'select') return
      if (e && e.itemData) {
        this.ownerOrg = {
          ownerOrgId: [e.itemData.id],
          ownerOrgName: e.itemData.text
        }
      } else {
        this.ownerOrg = {
          ownerOrgId: null,
          ownerOrgName: null
        }
      }
      this.$emit('changeFilterData', {
        org: this.ownerOrg
      })
    },
    /**
     * 拆解树为数组，并且不会对原始对象进行修改，原始对象子集列表也不会进行删除。
     * @param tree          {Array}          树形数组
     * @param children_key  {String}         子集对象 'key'
     * @return {[{}]} 树形被拆解后的数组
     */
    flatTreeData(tree, children_key = 'children') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-panel {
  flex-shrink: 0;
  background: #fff;
  padding: 10px;
  display: flex;
  align-items: center;
  .filter-item {
    width: 250px;
    &:not(:first-child) {
      margin-left: 10px;
    }
  }
}
</style>
