<template>
  <!-- 评分结果-确认页 -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  data() {
    return {
      pageConfig: pageConfig()
    }
  },
  mounted() {
    this.getUserInfoDetail()
  },
  methods: {
    getUserInfoDetail() {
      this.$API.performanceScoreSetting.getUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        this.pageConfig = pageConfig(
          this.$API.performanceScoreResult.getSupplierScoreResult,
          companyOrg.id
        )
      })
    },
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (e.toolbar.id == 'Check') {
        if (_selectRecords.length < 1) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          this.doBatchConfirmScore(_selectRecords)
        }
      }
    },
    //批量执行i18n.t('确认评分')
    doBatchConfirmScore(_selectRecords) {
      //是否已确认 0否1是
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.processScoreId)
      })
      this.handleConfirmRecords(_selectIds)
    },
    //执行上报操作
    handleConfirmRecords(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认评分结果？`)
        },
        success: () => {
          this.$API.performanceScoreResult.confirmScore({ ids, status: 1 }).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
}
</style>
