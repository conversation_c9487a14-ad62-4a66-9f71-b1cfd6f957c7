<template>
  <!-- 评分配置页面 -->
  <div class="score-setting">
    <mt-template-page ref="templateRef" :use-tool-template="false" :template-config="pageConfig">
      <!-- 维度设置 -->
      <tab-dimension slot="slot-0"></tab-dimension>
      <!-- 指标定义 -->
      <tab-target slot="slot-1"></tab-target>
      <!-- 绩效考评模板 -->
      <tab-template slot="slot-2" :template-type-list="templateTypeList"></tab-template>
      <!-- 公司默认评分人 -->
      <tab-rater slot="slot-3" :template-type-list="templateTypeList"></tab-rater>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //维度设置
    tabDimension: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension" */ './components/dimension'
      ),
    //指标定义
    tabTarget: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/target" */ './components/target'
      ),
    //绩效考评模板
    tabTemplate: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/template" */ './components/template'
      ),
    //公司默认评分人
    tabRater: () =>
      import(
        /* webpackChunkName: "router/supplierPerformance/scoreSetting/rater" */ './components/rater'
      )
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: 'ff31c5ac-dbaf-4df9-9a96-1e0d7b6826ac',
          title: this.$t('维度设置')
        },
        {
          gridId: '423fc37d-0320-4b4c-8e0b-bc77220cb758',
          title: this.$t('指标定义')
        },
        {
          gridId: '8987b97c-39de-4fbf-8bc6-692a6e368ae5',
          title: this.$t('绩效考评模板')
        },
        {
          gridId: 'b9e03c2e-8a62-493d-a190-96b5bc3c65ed',
          title: this.$t('公司默认评分人')
        }
      ],
      templateTypeList: [] //模板类型列表
    }
  },
  mounted() {
    this.getTemplateTypeList()
  },
  methods: {
    getTemplateTypeList() {
      //模板类型列表 - templateTypeList
      this.$API.performanceScoreSetting
        .dictionaryGetList({
          dictCode: 'PERFORMANCE_TEMPLATE_TYPE'
        })
        .then((res) => {
          let _list = [...res.data]
          _list.forEach((e) => {
            e.cssClass = 'user-column'
          })
          this.templateTypeList = _list
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
