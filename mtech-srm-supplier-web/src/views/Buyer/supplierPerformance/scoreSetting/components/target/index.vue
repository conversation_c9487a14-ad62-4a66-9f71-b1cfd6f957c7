<template>
  <!-- 指标定义Tab -->
  <div class="score-setting-target">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceScoreSetting.indexList),
      ownerOrg: { ownerOrgId: null, ownerOrgName: null }
    }
  },
  mounted() {
    this.getUserInfoDetail()
  },
  methods: {
    getUserInfoDetail() {
      this.$API.performanceScoreSetting.getUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        this.ownerOrg.ownerOrgId = companyOrg.id
        this.ownerOrg.ownerOrgName = companyOrg.orgName
        this.$set(this.pageConfig[0].grid.asyncConfig, 'defaultRules', [
          {
            condition: 'and',
            field: 'ownerOrgId',
            operator: 'equal',
            type: 'string',
            value: this.ownerOrg.ownerOrgId //设置数据
          }
        ])
      })
    },
    //新增配置
    handleAddConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/target/targetDialog" */ './components/targetDialog.vue'
          ),
        data: {
          title: this.$t('新增指标'),
          ownerOrg: this.ownerOrg
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditConfig(data) {
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/supplierPerformance/scoreSetting/target/targetDialog" */ './components/targetDialog.vue'
            ),
          data: {
            title: this.$t('编辑指标'),
            data,
            ownerOrg: this.ownerOrg
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      //状态 0: this.$t("启用"), 1: this.$t("停用")
      let _params = {
        ids,
        enabled: status
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为') + _statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceScoreSetting.updateIndex(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除配置
    handleDeleteConfig(ids) {
      let _params = {
        ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceScoreSetting.deleteIndex(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-target {
  height: 100%;
  .filter-org {
    background: #fff;
    padding: 10px;
    /deep/.owner-org-tree {
      width: 300px;
    }
  }
}
</style>
