//指标定义Tab
import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增'), permission: ['O_02_0044'] },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除'), permission: ['O_02_0045'] },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑'), permission: ['O_02_0046'] },
  { id: 'Start', icon: 'icon_table_enable', title: i18n.t('启用'), permission: ['O_02_0047'] },
  { id: 'Stop', icon: 'icon_table_disable', title: i18n.t('停用'), permission: ['O_02_0048'] }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'indexCode',
    headerText: i18n.t('指标编码'),
    cssClass: '',
    cellTools: [
      { id: 'edit', icon: 'icon_Editor', title: i18n.t('编辑'), permission: ['O_02_0046'] },
      { id: 'delete', icon: 'icon_Delete', title: i18n.t('删除'), permission: ['O_02_0045'] }
    ]
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标名称')
  },
  {
    field: 'dimensionName',
    headerText: i18n.t('所属维度')
  },
  {
    field: 'ownerOrgName',
    headerText: i18n.t('所属组织')
  },
  {
    field: 'description',
    headerText: i18n.t('评价标准简介')
  },
  // {
  //   field: "downOrgName",
  //   headerText: i18n.t("派发组织清单"),
  // },
  {
    field: 'enabled',
    headerText: i18n.t('状态'),
    width: 200,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Start',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        permission: ['O_02_0047'],
        visibleCondition: (data) => {
          return data['enabled'] == 1
        }
      },
      {
        id: 'Stop',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        permission: ['O_02_0048'],
        visibleCondition: (data) => {
          return data['enabled'] == 0
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    gridId: '088ddbc1-cdb0-4c9b-8c4d-3247f74ecdf9',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
