<template>
  <!-- 绩效考评模板Tab -->
  <div class="score-setting-template">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  props: {
    templateTypeList: {
      //模板类型列表
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceScoreSetting.templateList)
    }
  },
  methods: {
    // 单元格title，点击
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field === 'templateCode') {
        this.redirectTemplateDetail(e)
      }
    },
    //跳转模板详情页
    redirectTemplateDetail(e) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/template/components/detail" */ './components/detail'
          ),
        data: {
          data: e.data
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //新增配置
    handleAddConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/template/templateDialog" */ './components/templateDialog.vue'
          ),
        data: {
          title: this.$t('新增绩效考评模板'),
          flatTreeData: this.flatTreeData,
          templateTypeList: this.templateTypeList
        },
        success: (res) => {
          if (res.type === 'detail') {
            this.redirectTemplateDetail(res)
          }
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditConfig(data) {
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/supplierPerformance/scoreSetting/template/templateDialog" */ './components/templateDialog.vue'
            ),
          data: {
            title: this.$t('编辑绩效考评模板'),
            flatTreeData: this.flatTreeData,
            templateTypeList: this.templateTypeList,
            data
          },
          success: (res) => {
            if (res.type === 'detail') {
              this.redirectTemplateDetail(res)
            }
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      //状态 0: this.$t("启用"), 1: this.$t("停用")
      let _params = {
        ids,
        enabled: status
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为') + _statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceScoreSetting.updateTemplate(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除配置
    handleDeleteConfig(ids) {
      let _params = {
        ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceScoreSetting.deleteTemplate(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-template {
  height: 100%;
}
</style>
