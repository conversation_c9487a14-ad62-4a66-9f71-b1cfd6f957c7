import { i18n } from '@/main.js'
//绩效考评模板Tab
const toolbar = (enabled) => {
  return {
    useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
    tools: [
      [
        {
          id: 'Add',
          icon: 'icon_solid_edit',
          title: i18n.t('新增维度'),
          visibleCondition: () => {
            console.log('enabled:', enabled)
            return enabled !== 0
          }
        }
      ]
    ]
  }
}
const columnData = (enabled) => {
  return [
    {
      width: 200,
      field: 'name',
      headerText: i18n.t('名称'),
      cssClass: '',
      cellTools: [
        {
          id: 'Add',
          icon: 'icon_card_plus',
          title: i18n.t('添加指标'),
          visibleCondition: (data) => {
            return data['dimensionId'] && data['dimensionName'] && enabled !== 0
          }
        },
        {
          id: 'Edit',
          icon: 'icon_list_edit',
          title: i18n.t('编辑'),
          visibleCondition: () => {
            return enabled !== 0
          }
        },
        {
          id: 'Delete',
          icon: 'icon_list_delete',
          title: i18n.t('删除'),
          visibleCondition: (data) => {
            return data['showDeleteIcon'] && enabled !== 0
          }
        }
      ]
    },
    {
      field: 'weight',
      headerText: i18n.t('分配权重（%）')
    },
    {
      field: 'indexFullScore',
      headerText: i18n.t('指标满分')
    },
    {
      field: 'allocateScore',
      headerText: i18n.t('分配分值')
    },
    {
      field: 'indexFromSource',
      headerText: i18n.t('指标来源')
    },
    {
      field: 'scoreStandard',
      headerText: i18n.t('评分标准')
    }
  ]
}

export const pageConfig = (enabled) => {
  return [
    {
      gridId: '8e7bf6a0-d23f-43f2-90d6-01ad41ac3b1c',
      useToolTemplate: false,
      toolbar: toolbar(enabled),
      treeGrid: {
        allowPaging: false,
        columnData: columnData(enabled),
        childMapping: 'indexList',
        dataSource: []
        // dataSource: [
        //   {
        //     indexList: [
        //       {
        //         indexId: "1448552109902594050",
        //         indexName: i18n.t("合同指标"),
        //         weight: null,
        //         allocateScore: null,
        //         indexFullScore: 5,
        //         indexFromSource: i18n.t("京东集团"),
        //         scoreStandard: i18n.t("合同指标说明"),
        //       },
        //     ],
        //     weight: null,
        //     allocateScore: null,
        //     dimensionName: i18n.t("质量"),
        //     dimensionId: "1442780060260077569",
        //   },
        // ],
      }
    }
  ]
}
