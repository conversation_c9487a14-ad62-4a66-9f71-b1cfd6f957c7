import { i18n } from '@/main.js'
//绩效考评模板Tab
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增'), permission: ['O_02_0049'] },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除'), permission: ['O_02_0052'] },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑'), permission: ['O_02_0050'] },
  { id: 'Start', icon: 'icon_table_enable', title: i18n.t('启用'), permission: ['O_02_0051'] },
  { id: 'Stop', icon: 'icon_table_disable', title: i18n.t('停用'), permission: ['O_02_0053'] }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'templateCode',
    headerText: i18n.t('模板编码'),
    cellTools: [
      { id: 'edit', icon: 'icon_Editor', title: i18n.t('编辑'), permission: ['O_02_0050'] },
      { id: 'delete', icon: 'icon_Delete', title: i18n.t('删除'), permission: ['O_02_0052'] }
    ]
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'templateTypeName',
    headerText: i18n.t('模板类型')
  },
  {
    field: 'enabled',
    headerText: i18n.t('状态'),
    width: 200,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Start',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        permission: ['O_02_0051'],
        visibleCondition: (data) => {
          return data['enabled'] == 1
        }
      },
      {
        id: 'Stop',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        permission: ['O_02_0053'],
        visibleCondition: (data) => {
          return data['enabled'] == 0
        }
      }
    ]
  },
  {
    field: 'score',
    headerText: i18n.t('满分')
  },
  {
    field: 'ownerOrgName',
    headerText: i18n.t('所属组织')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    gridId: 'a3ca9807-3a43-40a0-9a2f-4674fde511c1',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
