<template>
  <!-- 指标定义Tab -->
  <div class="score-setting-target">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <div slot="slot-filter" v-if="companyTreeFields.dataSource.length" class="filter-org">
        <mt-DropDownTree
          class="owner-org-tree"
          :id="'org_' + new Date().getTime()"
          ref="ownerOrgNameRef"
          :placeholder="$t('请选择所属组织')"
          v-model="ownerOrg.ownerOrgId"
          :fields="companyTreeFields"
          :show-clear-button="false"
          @select="selectOwnerOrg"
        ></mt-DropDownTree>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceScoreSetting.indexList),
      ownerOrg: { ownerOrgId: null, ownerOrgName: null },
      companyTreeFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      companyTreeList: [], //当前组织列表--Tree
      flatCompanyTreeList: [] //当前组织列表--Array
    }
  },
  mounted() {
    this.getCompanyTree()
  },
  methods: {
    getCompanyTree() {
      this.$API.performanceScoreSetting
        .getStatedLimitTree({
          orgLevelCode: 'ORG02',
          orgType: 'ORG001PRO'
        })
        .then((res) => {
          let _data = []
          if (res && res.data && Array.isArray(res.data)) {
            _data = res.data
          }
          this.companyTreeList = _data
          this.flatCompanyTreeList = this.flatTreeData(_data)
          this.companyTreeFields.dataSource = []
          this.$nextTick(() => {
            this.$set(this.companyTreeFields, 'dataSource', _data)
          })
        })
    },
    //选取组织数据
    selectOwnerOrg(e) {
      if (e.action !== 'select') return
      if (e && e.itemData) {
        this.ownerOrg = {
          ownerOrgId: [e.itemData.id],
          ownerOrgName: e.itemData.text
        }
      } else {
        this.ownerOrg = {
          ownerOrgId: null,
          ownerOrgName: null
        }
      }
      //更新组织ID，刷新列表数据
      this.$set(this.pageConfig[0].grid.asyncConfig, 'defaultRules', [
        {
          condition: 'and',
          field: 'ownerOrgId',
          operator: 'equal',
          type: 'string',
          value: this.ownerOrg.ownerOrgId[0] //设置数据
        }
      ])
    },
    //新增配置
    handleAddConfig() {
      if (!this.ownerOrg.ownerOrgId || !this.ownerOrg.ownerOrgName) {
        this.$toast({
          content: this.$t('当前未选择"所属组织"'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/target/targetDialog" */ './components/targetDialog.vue'
          ),
        data: {
          title: this.$t('新增指标'),
          ownerOrg: this.ownerOrg,
          flatTreeData: this.flatTreeData,
          flatCompanyTreeList: this.flatCompanyTreeList
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditConfig(data) {
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/supplierPerformance/scoreSetting/target/targetDialog" */ './components/targetDialog.vue'
            ),
          data: {
            title: this.$t('编辑指标'),
            data,
            ownerOrg: this.ownerOrg,
            flatTreeData: this.flatTreeData,
            flatCompanyTreeList: this.flatCompanyTreeList
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      //状态 0: this.$t("启用"), 1: this.$t("停用")
      let _params = {
        ids,
        enabled: status
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `确认更新状态为${_statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceScoreSetting.updateIndex(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除配置
    handleDeleteConfig(ids) {
      let _params = {
        ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceScoreSetting.deleteIndex(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-target {
  height: 100%;
  .filter-org {
    background: #fff;
    padding: 10px;
    /deep/.owner-org-tree {
      width: 300px;
    }
  }
}
</style>
