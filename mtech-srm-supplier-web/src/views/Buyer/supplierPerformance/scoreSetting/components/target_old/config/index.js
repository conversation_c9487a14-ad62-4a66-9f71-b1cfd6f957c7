//指标定义Tab
import { i18n } from '@/main.js'
const toolbar = [
  'Add',
  'Delete',
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'Start', icon: 'icon_table_enable', title: i18n.t('启用') },
  { id: 'Stop', icon: 'icon_table_disable', title: i18n.t('停用') }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'indexCode',
    headerText: i18n.t('指标编码'),
    cssClass: '',
    cellTools: ['edit', 'delete']
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标名称')
  },
  {
    field: 'dimensionName',
    headerText: i18n.t('所属维度')
  },
  {
    field: 'ownerOrgName',
    headerText: i18n.t('所属组织')
  },
  {
    field: 'description',
    headerText: i18n.t('评价标准简介')
  },
  // {
  //   field: "downOrgName",
  //   headerText: i18n.t("派发组织清单"),
  // },
  {
    field: 'enabled',
    headerText: i18n.t('状态'),
    width: 200,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Start',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['enabled'] == 1
        }
      },
      {
        id: 'Stop',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['enabled'] == 0
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    gridId: '3065b2ab-93c1-4a6f-9126-28e1cabf3d4e',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
