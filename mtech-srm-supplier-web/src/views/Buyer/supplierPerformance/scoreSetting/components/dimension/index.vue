<template>
  <!-- 维度设置Tab -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceScoreSetting.dimensionList)
    }
  },
  methods: {
    //新增配置
    handleAddConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension/dimensionDialog" */ './components/dimensionDialog.vue'
          ),
        data: {
          title: this.$t('新增维度')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditConfig(data) {
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/supplierPerformance/scoreSetting/dimension/dimensionDialog" */ './components/dimensionDialog.vue'
            ),
          data: {
            title: this.$t('编辑维度'),
            data
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      //状态 0: this.$t("启用"), 1: this.$t("停用")
      let _params = {
        ids,
        enabled: status
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为') + _statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceScoreSetting.updateDimension(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除配置
    handleDeleteConfig(ids) {
      let _params = {
        ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceScoreSetting.deleteDimension(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
</style>
