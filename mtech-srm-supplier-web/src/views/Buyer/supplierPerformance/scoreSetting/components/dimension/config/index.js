//维度设置Tab
import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增'), permission: ['O_02_0039'] },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除'), permission: ['O_02_0040'] },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑'), permission: ['O_02_0041'] },
  { id: 'Start', icon: 'icon_table_enable', title: i18n.t('启用'), permission: ['O_02_0042'] },
  { id: 'Stop', icon: 'icon_table_disable', title: i18n.t('停用'), permission: ['O_02_0043'] }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'dimensionCode',
    headerText: i18n.t('维度编码'),
    cssClass: '',
    cellTools: [
      { id: 'edit', icon: 'icon_Editor', title: i18n.t('编辑'), permission: ['O_02_0041'] },
      { id: 'delete', icon: 'icon_Delete', title: i18n.t('删除'), permission: ['O_02_0040'] }
    ]
  },
  {
    field: 'dimensionName',
    headerText: i18n.t('维度名称')
  },
  {
    field: 'enabled',
    headerText: i18n.t('状态'),
    width: 80,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Start',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        permission: ['O_02_0042'],
        visibleCondition: (data) => {
          return data['enabled'] == 1
        }
      },
      {
        id: 'Stop',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        permission: ['O_02_0043'],
        visibleCondition: (data) => {
          return data['enabled'] == 0
        }
      }
    ]
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    gridId: '491ee2b4-1868-4c18-9b5c-c232d44db002',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
