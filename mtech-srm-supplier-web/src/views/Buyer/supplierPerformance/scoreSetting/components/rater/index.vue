<template>
  <!-- 公司默认评分人Tab -->
  <div class="score-setting-rater">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  props: {
    templateTypeList: {
      //模板类型列表
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceScoreSetting.raterList)
    }
  },
  methods: {
    //新增配置
    handleAddConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supplierPerformance/scoreSetting/rater/raterDialog" */ './components/raterDialog.vue'
          ),
        data: {
          title: this.$t('评分人设置'),
          templateTypeList: this.templateTypeList,
          flatTreeData: this.flatTreeData
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditConfig(data) {
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/supplierPerformance/scoreSetting/rater/raterDialog" */ './components/raterDialog.vue'
            ),
          data: {
            title: this.$t('评分人编辑'),
            configId: data.id,
            templateTypeList: this.templateTypeList
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      //状态 0: this.$t("启用"), 1: this.$t("停用")
      let _params = {
        ids,
        enabled: status
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: ` ${this.$t('确认更新状态为') + _statusMap[status]}？`
        },
        success: () => {
          this.$API.performanceScoreSetting.updateRater(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除配置
    handleDeleteConfig(ids) {
      let _params = {
        ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.performanceScoreSetting.deleteRater(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-rater {
  height: 100%;
}
</style>
