//公司默认评分人Tab
import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增'), permission: ['O_02_0054'] },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除'), permission: ['O_02_0055'] }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'orgCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'raterName',
    headerText: i18n.t('评分人姓名')
  },
  // {
  //   field: "remark",
  //   headerText: i18n.t("评分人账号"),
  // },
  // {
  //   field: "deptName",
  //   headerText: i18n.t("评分人部门"),
  // },
  // {
  //   field: "remark",
  //   headerText: i18n.t("评分人岗位"),
  // },
  {
    field: 'templateTypeName',
    headerText: i18n.t('适用评分模板类型')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    gridId: '4d7cc394-eec4-4248-9dde-b24799d77d44',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
