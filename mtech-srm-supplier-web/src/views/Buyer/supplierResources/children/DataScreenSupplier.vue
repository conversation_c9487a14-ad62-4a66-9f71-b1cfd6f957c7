<template>
  <div class="DataScreen-div" ref="DataScreenSupplier">
    <div class="top">
      <div class="top-1">
        <SupplierAnalysis
          :organization-id="organizationId"
          :info-setting="jxInfoSetting"
        ></SupplierAnalysis>
      </div>
      <div class="top-2-1">
        <SupplierRightTitle
          :organization-id="organizationId"
          :is-supplier-show="true"
        ></SupplierRightTitle>
      </div>
    </div>
    <div class="top-2">
      <CategorySuppliers></CategorySuppliers>
    </div>
    <div class="top-2">
      <ResourceBuy></ResourceBuy>
    </div>
    <div :class="classState ? 'middle3' : 'middle'">
      <div class="middle-1">
        <!-- 供应商数量变化 -->
        <QuantityChange :info-setting="jxInfoSetting"></QuantityChange>
      </div>
      <div class="middle-1">
        <SupplierLevel :info-setting="jxInfoSetting"></SupplierLevel>
      </div>
      <div class="middle-1">
        <!-- 支出金额变化情况 -->
        <SupplierExpend :info-setting="jxInfoSetting"></SupplierExpend>
      </div>
      <div class="middle-1">
        <InfoListForm :info-setting="jxInfoSetting" @getDwonTreeVal="getDwonTreeVal"></InfoListForm>
      </div>
      <div class="middle-1">
        <InfoListForm
          :info-setting="zcInfoSetting"
          @getDwonTreeVal="getDwonTreeValZC"
          @getDateVal="getDateVal"
        ></InfoListForm>
      </div>
      <div class="middle-1">
        <ListTable></ListTable>
      </div>
    </div>
  </div>
</template>
<script>
import SupplierRightTitle from './components/SupplierRightTitle.vue'
// import RightTitle from './components/RightTitle.vue'
import ResourceBuy from './components/ResourceBuy.vue'
import SupplierExpend from './components/SupplierExpend.vue'
import SupplierAnalysis from './components/SupplierAnalysis.vue'
// import ExpenditureAmount from './components/ExpenditureAmount.vue'
import SupplierLevel from './components/SupplierLevel.vue'
// import PlatformActivity from './components/PlatformActivity.vue' // 平台活跃度
// import EcologicalAnalysis from './components/EcologicalAnalysis.vue' // 平台生态分析
import QuantityChange from './components/QuantityChange.vue' // 供应商数量变化
import CategorySuppliers from './components/CategorySuppliers.vue' // 各品类供应商数
import ListTable from './components/ListTable.vue' //黑名单清单
import InfoListForm from './components/InfoListForm.vue'
import bus from '@/utils/bus'
export default {
  components: {
    SupplierRightTitle,
    ResourceBuy,
    SupplierAnalysis,
    SupplierExpend,
    SupplierLevel,
    QuantityChange,
    CategorySuppliers,
    ListTable,
    InfoListForm
  },
  created() {
    let { organizationid } = this.$route.query
    this.organizationId = organizationid
  },
  data() {
    return {
      classState: true,
      titleNamere: this.$t('供应商数量变化'),
      organizationId: '',
      jxInfoSetting: {
        titleName: this.$t('绩效') + ' Top 10',
        id: '0',
        mockList: [],
        isShowScore: true,
        isShowPL: true,
        isShowPercent: false,
        isShowPLName: true,
        isShowXM: false,
        isShowTime: false,
        type: 'Supplier',
        dataArrPL: []
      },
      zcInfoSetting: {
        titleName: this.$t('支出金额') + ' Top 10',
        id: '1',
        mockList: [],
        isShowScore: false,
        isShowPL: true,
        isShowPercent: true,
        isShowPLName: true,
        isShowXM: false,
        isShowTime: true,
        type: 'Supplier',
        dataArrPL: []
      },
      projectVal: '',
      dateVal: '',
      orgId: '',
      areaCode: '',
      timer: false
    }
  },
  mounted() {
    this.getProdtreeInClient()
    // this.getDictItems() 项目分类接口，目前供应商没有用到
    bus.$on('companyChange', (a) => {
      this.orgId = a
      this.queryScore()
      this.purchaseAmountTop()
    })
    bus.$on('areaChange', (a) => {
      this.areaCode = a
      this.queryScore()
      this.purchaseAmountTop()
    })
    // this.queryScore();
    // this.purchaseAmountTop();
    // 根据外层宽度变化切换样式
    window.addEventListener('scroll', this.checkScroll, true)
    window.addEventListener('resize', this.checkScroll, true)
  },
  methods: {
    checkScroll() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        // 最大值1199，最小值1200  根据外层宽度变化切换样式
        if (this.$refs.DataScreenSupplier.clientWidth >= 1200) {
          this.classState = true
        } else if (this.$refs.DataScreenSupplier.clientWidth <= 1199) {
          this.classState = false
        }
      }, 300)
    },
    // 绩效TOP10
    queryScore(categoryId) {
      this.$API.supplierDashboard
        .queryScoreTen({
          areaCode: this.areaCode,
          orgId: this.orgId, //集团ID
          categoryId: categoryId ? categoryId : '' //品类ID
        })
        .then((res) => {
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.map((item) => {
            item.itemName = item.supplierEnterpriseName
            item.itemScore = item.score
          })
          this.jxInfoSetting.mockList = arr
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 支出金额TOP10
    purchaseAmountTop() {
      let tdate = new Date()
      let tyear = tdate.getFullYear()
      this.$API.supplierDashboard
        .purchaseAmountTen({
          categoryId: this.projectVal,
          groupOrgId: this.areaCode,
          purOrgId: this.orgId,
          year: this.dateVal || this.dateVal !== '' ? this.dateVal : tyear
        })
        .then((res) => {
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.map((item) => {
            item.itemName = item.purOrgName
            item.itemScore = item.purchaseAmount
          })
          this.zcInfoSetting.mockList = arr
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    //品类分类
    getProdtreeInClient() {
      this.$API.supplierInvitationAdd
        .getProdtreeInClient({ id: 0 })
        .then((res) => {
          // 把品类第一级名字改成不限品类
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.forEach((item) => {
            item.categoryName = this.$t('不限品类')
          })
          this.jxInfoSetting.dataArrPL = arr
          this.zcInfoSetting.dataArrPL = arr
        })
        .catch((err) => {
          this.$toast({
            content: err.msg ? err.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    // 项目分类
    getDictItems() {
      let dictCode = 'projectType'
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          let arr = []
          res.data.forEach((item) => {
            let uip = {
              stageName: item.name,
              id: item.id,
              code: item.itemCode
            }
            arr.push(uip)
          })
          // 供应商用不到这个值所以不用传
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
        })
    },
    getDwonTreeVal(val) {
      let value = val ? val : undefined
      this.queryScore(value)
    },
    getDwonTreeValZC(val) {
      this.projectVal = val
      this.purchaseAmountTop()
    },
    getDateVal(val) {
      this.dateVal = val.getFullYear()
      this.purchaseAmountTop()
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.checkScroll, true)
    window.removeEventListener('resize', this.checkScroll, true)
  }
}
</script>
<style>
.transfer-icon {
  transform: rotate(90deg);
  display: inline-block;
  margin: 0 2px 0 10px;
}
</style>
<style lang="scss" scoped>
/deep/.mt-tree-view .e-treeview .e-level-1 {
  padding: 0 !important;
}
/deep/.mt-tree-view .e-treeview > .e-ul {
  padding: 0 0 0 10px !important;
}

.DataScreen-div {
  background: #fafafa;
  margin: 20px 0;
  min-width: 992px;
  .top {
    width: 100%;
    height: 300px;
    display: flex;

    .top-1 {
      width: 35%;
      min-height: 300px;
      margin: 0 20px 0 0;
    }
    .top-2-1 {
      width: 65%;
      height: 300px;
    }
  }
  .top-2 {
    width: 100%;
    // height: 300px;
    display: flex;
    // flex-direction: row;
    // flex-wrap: nowrap;
    margin-top: 20px;
  }
  .middle {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    .middle-1 {
      width: 49%;
      height: 332px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-right: 2%;
      margin-bottom: 20px;
    }
    .middle-1:nth-child(2n) {
      margin-right: 0px;
    }
  }
  .middle3 {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    .middle-1 {
      width: 32%;
      height: 332px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-right: 2%;
      margin-bottom: 20px;
    }
    .middle-1:nth-child(3n) {
      margin-right: 0px;
    }
  }
  .bottom {
    width: 100%;
    display: flex;
    margin-top: 20px;
    .bottom-1 {
      height: 480px;
      flex: 1;
      width: 33%;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    }
  }
  .top-1,
  .top-2,
  .top-2-1,
  .top-2-2 {
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  }
}
</style>
