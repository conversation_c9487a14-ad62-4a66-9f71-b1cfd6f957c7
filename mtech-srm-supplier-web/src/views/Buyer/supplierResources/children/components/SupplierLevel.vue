<template>
  <div class="SupplierLevel">
    <div class="data-screen-title" style="display: flex; justify-content: space-between">
      <div>
        {{ $t('供应商级别') }}
        <span class="change-list" @click="isShow = !isShow"
          ><mt-icon class="transfer-icon" name="icon_card_transfer"></mt-icon
          >{{ $t('切换列表') }}</span
        >
      </div>
      <div>
        <div
          class="data-screen-select"
          id="supplierLevel"
          style="position: relative"
          @click="displayTreeView"
        >
          <span>{{ plId }}</span>
          <MtIcon
            name="icon_table_arrow"
            style="font-size: 12px; margin-left: auto; margin-right: 5px"
          />
        </div>
        <div v-show="showTree">
          <div slot="content" id="treeViewslevel" class="select-content" style="margin-top: 0px">
            <mt-treeView
              class="tree-view--template"
              ref="treeView"
              :fields="fields"
              :auto-check="true"
              :show-check-box="false"
              @nodeSelecting="nodeSelecting"
            ></mt-treeView>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!showBox" class="mh225">
      <span v-show="!isShow" class="data-screen-chart-span tableClass">
        <mt-template-page
          style="margin-top: 10px"
          ref="templateRef"
          :hidden-tabs="true"
          :template-config="pageConfig"
        ></mt-template-page
      ></span>
      <span v-show="isShow">
        <div class="SupplierLevel-charts">
          <div ref="charts" :style="{ height: '225px', width: '100%', margin: '0 auto' }"></div>
          <div class="tips-box">
            <div class="tips" v-show="getData.length >= 4">
              <div class="pop-tip">
                {{ getData.length >= 4 ? getData[3].value : 0 }}
              </div>
              <div class="num" style="color: #eda133">
                {{ getData.length >= 4 ? getData[3].value : 0 }}
              </div>
              <div class="word">
                {{ getData.length >= 4 ? getData[3].name : '' }}
              </div>
            </div>
            <div class="tips" v-show="getData.length >= 5">
              <div class="pop-tip">
                {{ getData.length >= 5 ? getData[4].value : 0 }}
              </div>
              <div class="num" style="color: #ed5633">
                {{ getData.length >= 5 ? getData[4].value : 0 }}
              </div>
              <div class="word">
                {{ getData.length >= 5 ? getData[4].name : '' }}
              </div>
            </div>
          </div>
          <div
            class="tips-box2"
            :style="getData.length > 3 ? 'right: 90px !important;' : 'right: 35px !important;'"
          >
            <div class="tips" v-show="getData.length >= 1">
              <div class="pop-tip">
                {{ getData.length >= 1 ? getData[0].value : '' }}
              </div>
              <div class="num" style="color: #8acc40">
                {{ getData.length >= 1 ? getData[0].value : '' }}
              </div>
              <div class="word">
                {{ getData.length >= 1 ? getData[0].name : '' }}
              </div>
            </div>
            <div class="tips" v-show="getData.length >= 2">
              <div class="pop-tip">
                {{ getData.length >= 2 ? getData[1].value : 0 }}
              </div>
              <div class="num" style="color: #6386c1">
                {{ getData.length >= 2 ? getData[1].value : 0 }}
              </div>
              <div class="word">
                {{ getData.length >= 2 ? getData[1].name : '' }}
              </div>
            </div>
            <div class="tips" v-show="getData.length >= 3">
              <div class="pop-tip">
                {{ getData.length >= 3 ? getData[2].value : 0 }}
              </div>
              <div class="num" style="color: #00469c">
                {{ getData.length >= 3 ? getData[2].value : 0 }}
              </div>
              <div class="word">
                {{ getData.length >= 3 ? getData[2].name : '' }}
              </div>
            </div>
          </div>
        </div>
      </span>
    </div>
    <div style="height: 205px" v-else>
      <emptyBox></emptyBox>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts' //   npm install echarts@4.9.0
import { supplierLevelColumn } from '../../config'
import bus from '@/utils/bus'
import emptyBox from '@/components/emptyData/emptyData.vue'

let isInitHideTree = false
let containsArr = []
let containsSelect = []
export default {
  name: '',
  components: { emptyBox },
  props: {
    organizationId: {
      type: Object,
      default: () => {}
    },
    infoSetting: {
      type: Object,
      default: () => {
        return {
          isShowScore: true, // 是否显示右侧的数字
          dataArrPL: [] // 品类接口返回的数据 （因为好多处用这个接口所以写到外面了）
        }
      }
    }
  },
  data() {
    return {
      showBox: false,
      showTree: false,
      plId: '',
      getData: [],
      isShow: true,
      fields: {
        dataSource: [],
        id: 'id',
        text: 'categoryName',
        parentID: 'parentId'
      },
      value: '',
      pageConfig: [
        {
          gridId: '53ddbb5b-b6ea-41b7-a454-75d114771fa8',
          title: this.$t('审批流配置'),
          grid: {
            allowReordering: false,
            allowResizing: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: supplierLevelColumn,
            allowPaging: false,
            dataSource: []
            // asyncConfig: {
            //   methods: "post",
            //   params: {
            //     areaCode: "",
            //     categoryId: "",
            //     orgId: "1431073794369822721",
            //   },
            //   url: "/analysis/tenant/cockpit/querySupplierLevel",
            //   recordsPosition: "data",
            // },
          }
        }
      ],
      orgId: '',
      areaCode: ''
    }
  },
  watch: {
    infoSetting: {
      handler() {
        if (this.infoSetting.dataArrPL.length > 0) {
          const ref = this.$refs.treeView
          if (ref.ejsInstances.groupedData.length > 0) {
            return
          }
          ref.ejsInstances.addNodes(this.infoSetting.dataArrPL)
          this.plId = this.infoSetting.dataArrPL[0].categoryName
        }
      },
      deep: true
    },
    isShow(n) {
      if (n) {
        this.$nextTick(() => {
          // 切换列表后移动窗口 图表都挤在了一起
          let myevent = new Event('resize')
          window.dispatchEvent(myevent)
        })
      }
    }
  },
  created() {
    let id = sessionStorage.getItem('cockUserId')
    this.orgId = id
    // this.pageConfig[0].grid.asyncConfig.params.orgId = id;
    this.querySupplierLevel()
    // this.getProdtreeInClient();
  },
  mounted() {
    bus.$on('companyChange', (a) => {
      if (this.orgId !== a) {
        this.orgId = a
        this.querySupplierLevel()
        // this.pageConfig[0].grid.asyncConfig.params.orgId = a;
      }
    })
    bus.$on('areaChange', (a) => {
      if (this.areaCode !== a) {
        this.areaCode = a
        this.querySupplierLevel()
        // this.pageConfig[0].grid.asyncConfig.params.areaCode = a;
      }
    })
    this.initDownTree()
    this.$nextTick(() => {
      // echarts容器的宽度根据父元素宽度变化进行自适应
      const resizeob = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (echarts.getInstanceByDom(entry.target)) {
            echarts.getInstanceByDom(entry.target).resize()
          }
        }
      })
      resizeob.observe(this.$refs.charts)
    })

    // this.mycharts();
    // this.$set(this.fields, "dataSource", this.infoSetting.dataArrPL);
  },
  methods: {
    // 展示按钮
    displayTreeView() {
      this.showTree = !this.showTree
      !isInitHideTree && this.showTree && this.initDownTree()
    },
    // 初始化点击周边 关闭tree 点击外部隐藏 内部没是
    clickInsideFun(e) {
      containsArr = document.getElementById('treeViewslevel')
      containsSelect = document.getElementById('supplierLevel')
      if (
        (!!containsArr && containsArr.contains(e.target)) ||
        (!!containsSelect && containsSelect.contains(e.target))
      ) {
        // console.log("在内");
      } else {
        this.showTree = false
      }
    },
    initDownTree() {
      isInitHideTree = true
      document.addEventListener('click', this.clickInsideFun)
    },
    getProdtreeInClient(item = 0) {
      this.$API.supplierInvitationAdd
        .getProdtreeInClient({ id: item })
        .then((res) => {
          if (res.data.length > 0) {
            const ref = this.$refs.treeView
            ref.ejsInstances.addNodes(res.data)
            if (res.data[0].parentId == '0') {
              this.plId = res.data[0].categoryName
            }
          } else {
            this.displayTreeView()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg ? err.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    nodeSelecting(e) {
      this.plId = e.nodeData.text
      this.value = e.nodeData.text == this.$t('不限品类') ? '' : e.nodeData.id
      if (this.value || this.value == '') {
        this.getProdtreeInClient(e.nodeData.id)
        this.querySupplierLevel()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    mycharts(e) {
      let myChart = echarts.init(this.$refs.charts, 'macarons')
      let option = {
        color: ['#8ACC40', '#6386C1', '#00469C', '#EDA133', '#ED5633'],
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: '' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        series: [
          {
            name: '',
            type: 'funnel',
            left: 0,
            top: 20,
            bottom: 0,
            width: '50%',
            sort: 'none',
            gap: 2,
            max: e.max,
            label: {
              show: false,
              position: 'inside',
              formatter: '{b}: {c}',
              color: '#fff',
              itemStyle: {
                textBorderColor: ''
              }
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 0,
                type: 'solid'
              }
            },
            data: e.series
          }
        ]
      }
      myChart.setOption(option, true)
      //让图表自适应+防抖
      window.addEventListener('resize', resizeDebounce(resizeFn, 200))
      function resizeFn() {
        myChart.resize()
      }
      function resizeDebounce(fn, delay) {
        let timer = null
        return function () {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(fn, delay)
        }
      }
    },

    change(e) {
      this.value = e.id
      if (this.value) {
        this.querySupplierLevel()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    querySupplierLevel() {
      this.$API.supplierDashboard
        .querySupplierLevel({
          areaCode: this.areaCode,
          categoryId: this.value,
          orgId: this.orgId
        })
        .then((res) => {
          if (res.data && res.data.length > 0) {
            this.showBox = false
            let a = { xAxis: [], series: [], max: 0 }
            let arr = []
            res.data.forEach((e) => {
              a.xAxis.push(e.supplierGradeName)
              arr.push({ value: e.supplierCount, name: e.supplierGradeName })
              if (
                e.supplierGradeName !== this.$t('未分级供应商') &&
                e.supplierGradeName !== this.$t('未分级') &&
                e.supplierCount != 0
              ) {
                let obj = { value: e.supplierCount, name: e.supplierGradeName }
                a.series.push(obj)
                if (parseInt(e.supplierCount) > a.max) {
                  a.max = e.supplierCount
                }
              }
              this.getData = JSON.parse(JSON.stringify(arr))
            })
            this.$nextTick(() => {
              this.mycharts(a)
            })
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data)
          } else {
            this.showBox = true
          }
        })
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickInsideFun)
    containsArr = null
    containsSelect = null
  }
}
</script>

<style lang="scss" scoped>
/deep/.e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon {
  padding-top: 0 !important;
}
.SupplierLevel {
  width: 100%;
  padding: 20px 30px;
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .tableClass {
    height: 225px;
    overflow-y: scroll;
  }
  .SupplierLevel-charts {
    width: 100%;
    height: 235px;
    // display: flex;
    // align-items: center;
    position: relative;
    .tips-box2 {
      position: absolute;
      right: 95px !important;
      top: 18px !important;
    }
    .tips-box,
    .tips-box2 {
      position: absolute;
      right: 0;
      top: 55px;
      .tips {
        position: relative;
        margin-bottom: 28px;
        cursor: pointer;
        .pop-tip {
          display: none;
          position: absolute;
          top: -14px;
          left: 0;
          z-index: 9;
          padding: 5px 10px;
          color: #fff;
          background-color: rgb(113, 113, 113);
          border-radius: 2px;
          text-align: center;
          font-size: 12px;
        }
        .num {
          width: 84px;
          height: 25px;
          font-size: 24px;
          font-family: DINAlternate;
          font-weight: bold;
          margin-bottom: 5px;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .word {
          width: 84px;
          height: 14px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .tips:hover {
        .pop-tip {
          display: block;
        }
      }
    }
  }
  .choose-div {
    // margin: 20px;
    display: flex;
  }
  .choose-un {
    cursor: pointer;
    width: 60px;
    height: 20px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    font-size: 12px;
    text-align: center;
    line-height: 20px;
    box-sizing: content-box;
  }
  .choose-un:nth-of-type(1) {
    border-radius: 4px 0 0 4px;
    border-right: none;
  }
  .choose-un:nth-of-type(2) {
    border-radius: 0 4px 4px 0;
    border-left: none;
  }
  .choose-active {
    color: rgba(255, 255, 255, 1);
    background: rgba(0, 70, 156, 1);
  }
}
</style>
