<template>
  <div class="SupplierRightTitle">
    <div class="select-title">
      <div class="select">
        <template
          v-if="
            !!ownerOrgId &&
            ownerOrgId.length > 0 &&
            !!fieldDatas.dataSource &&
            fieldDatas.dataSource.length > 0
          "
        >
          <mt-DropDownTree
            css-class="selectClass"
            v-model="ownerOrgId"
            :id="'company_' + new Date().getTime()"
            :width="250"
            :popup-height="400"
            :fields="fieldDatas"
            :show-clear-button="false"
            @select="selectData"
          ></mt-DropDownTree>
        </template>
      </div>
      <div class="select">
        <mt-DropDownTree
          v-if="
            !!areaId && areaId.length > 0 && !!fields.dataSource && fields.dataSource.length > 0
          "
          css-class="selectClass"
          v-model="areaId"
          :id="'world_' + new Date().getTime()"
          :popup-height="400"
          :width="200"
          :fields="fields"
          :show-clear-button="false"
          @select="change"
        ></mt-DropDownTree>
      </div>
    </div>
    <div style="display: flex">
      <div class="icon-class">
        <div class="iconPromptClass">
          <div class="pop-tip1">{{ $t('最近12个月有支出金额的供应商视为活跃供应商') }}</div>
          <mt-icon name="icon_outline_prompt"></mt-icon>
        </div>
      </div>
      <div class="up-item">
        <div class="text-black">{{ $t('活跃供应商') }}</div>
        <div
          class="pop-tip"
          v-show="!!total.activityCount"
          :alt="total.activityCount"
          :title="total.activityCount"
        >
          {{ total.activityCount || 0 }}
        </div>
        <div class="text-1 blue typeThree" :alt="total.activityCount" :title="total.activityCount">
          {{ total.activityCount || 0 }}
        </div>
      </div>
      <div class="up-item">
        <div class="text-black">{{ $t('供应商总数') }}</div>
        <div
          class="pop-tip"
          v-show="!!total.supplierCount"
          :alt="total.supplierCount"
          :title="total.supplierCount"
        >
          {{ total.supplierCount || 0 }}
        </div>
        <div
          class="text-1 lightblue typeThree"
          :alt="total.supplierCount"
          :title="total.supplierCount"
        >
          {{ total.supplierCount || 0 }}
        </div>
      </div>
      <div class="up-item">
        <div class="text-black">{{ $t('年度总支出金额') }}</div>
        <div
          class="pop-tip"
          v-show="!!total.yearExpend"
          :alt="total.yearExpend"
          :title="total.yearExpend"
        >
          {{ total.yearExpend || 0 }}
        </div>
        <div class="text-1 blue typeThree" :alt="total.yearExpend" :title="total.yearExpend">
          {{ total.yearExpend || 0 }}
        </div>
      </div>
      <div class="up-item">
        <div class="text-black">{{ $t('年度战略和集采采购占比') }}</div>
        <div
          class="text-1 blue typeThree"
          :alt="total.yearStrategyPurchasePoint"
          :title="total.yearStrategyPurchasePoint"
        >
          {{ total.yearStrategyPurchasePoint || 0 }}%
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import bus from '@/utils/bus'
export default {
  data() {
    return {
      areaId: [],
      areaIdBat: [],
      value: '',
      fieldDatas: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      fields: {
        dataSource: [{ areaCode: '' }],
        value: 'areaCode',
        text: 'areaName',
        child: ''
      },
      total: {},
      ownerOrgId: [],
      ownerOrgIdBat: []
    }
  },
  mounted() {
    this.getUserInfo()
    // this.getFuzzyCompanyTree();
    this.selectByParentCode()
  },
  methods: {
    queryTotalCount() {
      this.$API.supplierDashboard
        .queryTotalCount({
          orgId: this.ownerOrgId[0], //公司
          supplierProvinceCode: this.value //地区
        })
        .then((res) => {
          res.data.yearExpend = this.get_thousand_num(res.data.yearExpend)
          res.data.yearStrategyPurchasePoint = res.data.yearStrategyPurchasePoint.toFixed(0)
          this.total = { ...res.data }
        })
    },
    // 数据千分位显示
    get_thousand_num(num) {
      return num.toString().replace(/\d+/, function (n) {
        // 先提取整数部分
        return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
          // 对整数部分添加分隔符
          return $1 + ','
        })
      })
    },
    // 获取当前登录人所在组织
    getUserInfo() {
      this.$API.performanceScoreSetting.getUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        if (companyOrg) {
          this.ownerOrgId = [companyOrg.id]
          sessionStorage.setItem('cockUserId', companyOrg.id)
          this.ownerOrgIdBat = JSON.parse(JSON.stringify([companyOrg.id]))
          !!companyOrg.id && bus.$emit('companyChange', companyOrg.id)
          this.getFuzzyCompanyTree(companyOrg.id)
          this.queryTotalCount() //需要ownerorgid的值
        }
      })
    },
    // 获取集团数据
    getFuzzyCompanyTree() {
      // this.$API.supplierInvitationAdd
      //   .getFuzzyCompanyTree({
      //     accountId: id,
      //   })
      this.$API.supplierInvitationAdd
        .getFuzzyCompanyTree2({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        })
        .then((res) => {
          this.fieldDatas = Object.assign({}, this.fieldDatas, {
            dataSource: res.data
          })
        })
    },
    // 获取城市数据
    selectByParentCode() {
      this.$API.supplierInvitationAdd
        .selectByParentCode({
          parentCode: ''
        })
        .then((res) => {
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.unshift({ areaCode: '', areaName: this.$t('全部地区') })
          this.fields = Object.assign({}, this.fields, { dataSource: arr })
          this.areaId = [arr[0].areaCode]

          this.areaIdBat = JSON.parse(JSON.stringify([arr[0].areaCode]))
          !!arr[0].areaCode && bus.$emit('areaChange', arr[0].areaCode)
        })
    },
    isEqualArr(arrA, arrB) {
      if (arrA.length !== arrB.length) {
        return false
      }

      if (
        arrA.filter((item) => arrB.filter((citem) => citem === item).length === 1).length ===
        arrA.length
      ) {
        return true
      } else {
        return false
      }
    },
    selectData(e) {
      if (e.id) {
        this.ownerOrgIdBat = JSON.parse(JSON.stringify([e.id]))
        if (!this.isEqualArr(this.ownerOrgId, this.ownerOrgIdBat)) {
          this.ownerOrgId = [e.id]
          this.queryTotalCount()
          bus.$emit('companyChange', e.id)
        }
      }
    },
    change(e) {
      this.value = e.id
      if (this.value || this.value == '') {
        this.areaIdBat = JSON.parse(JSON.stringify([e.id]))
        if (!this.isEqualArr(this.areaId, this.areaIdBat)) {
          this.queryTotalCount()
          bus.$emit('areaChange', e.id)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.SupplierRightTitle {
  width: 100%;
  padding: 30px 0 0 30px;
  .select-title {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 50px;
  }
  .icon-class {
    position: relative;
    cursor: pointer;
    .iconPromptClass {
      position: absolute;
      right: -100px;
      top: -10px;
      font-size: 18px;
      cursor: pointer;
      color: #4d5b6f;
      .pop-tip1 {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        display: none;
        position: absolute;
        top: -44px;
        left: -124px;
        z-index: 9;
        width: 336px;
        height: 42px;
        background: #fff;
        border-radius: 2px;
        text-align: center;
        line-height: 42px;
        font-size: 14px;
      }
    }
  }

  .iconPromptClass:hover {
    .pop-tip1 {
      display: block;
    }
  }
  .text-black {
    font-size: 16px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin: 0 0 10px 0;
  }
  .typeOne {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .typeTwo {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .typeThree {
    // max-width: 140px;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
  .vtas {
    vertical-align: super;
    margin-left: 20px;
  }
  .text-1,
  .text-2 {
    font-weight: bold;
  }
  .text-1 {
    font-size: 30px;
  }
  .text-2 {
    font-size: 24px;
    margin-top: 20px;
  }
  .select {
    background: rgba(250, 250, 250, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    margin-right: 20px;
    height: 40px;
  }
  .text {
    width: 84px;
    height: 14px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
  }
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .blue {
    color: #00469c;
  }
  .lightblue {
    color: #6386c1;
  }
  .red {
    color: #ed5633;
  }
  .green {
    color: #8acc40;
  }
}

.mt20 {
  margin-top: 20px;
}

.SupplierRightTitle .up-item {
  position: relative;
  min-width: 120px;
  height: 80px;
  text-align: center;
  margin-bottom: 20px;
  padding: 10px 40px 8px 0px;
  cursor: pointer;
  .pop-tip {
    display: none;
    position: absolute;
    top: 10px;
    left: 0;
    z-index: 9;
    padding: 5px 10px;
    color: #fff;
    background-color: rgb(113, 113, 113);
    border-radius: 2px;
    text-align: center;
    font-size: 12px;
  }
}
.up-item:hover {
  .pop-tip {
    display: block;
  }
}
.up-item:last-child {
  padding-right: 0px;
}
.SupplierRightTitle .bt-item {
  min-width: 60px;
  height: 80px;
  text-align: left;
  margin-bottom: 20px;
  padding: 10px 90px 8px 0px;
}
</style>
