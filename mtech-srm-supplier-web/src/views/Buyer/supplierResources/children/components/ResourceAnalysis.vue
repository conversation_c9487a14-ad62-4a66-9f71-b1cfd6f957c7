<template>
  <div class="ResourceAnalysis">
    <div class="data-screen-title" style="display: flex">
      {{ $t('一级品类中标金额') }}
      <span style="margin: 0 10px; line-height: 18px" class="change-list" @click="isShow = !isShow"
        ><mt-icon class="transfer-icon" name="icon_card_transfer"></mt-icon
        >{{ $t('切换列表') }}</span
      >
      <div class="data-screen-select">
        <mt-select
          :data-source="dataArr"
          :value="value"
          :change="change"
          placeholder=""
        ></mt-select>
      </div>
    </div>
    <div v-if="!showBox" class="mh225">
      <span v-show="!isShow" class="data-screen-chart-span tableClass">
        <mt-template-page
          ref="templateRef"
          :hidden-tabs="true"
          :use-tool-template="false"
          :template-config="pageConfig"
        ></mt-template-page
      ></span>
      <span v-show="isShow">
        <div class="ResourceAnalysis-charts">
          <div class="righttop" ref="charts"></div></div
      ></span>
    </div>
    <div style="height: 180px" v-else>
      <emptyBox></emptyBox>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts' //   npm install echarts@4.9.0
import { resourceAnalysisColumn } from '../../config'
import bus from '@/utils/bus'
import emptyBox from '@/components/emptyData/emptyData.vue'

export default {
  components: { emptyBox },
  props: {
    organizationId: {
      type: String,
      default: ''
    },
    titleName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showBox: false,
      isShow: true,
      listArr: [],
      dataArr: [
        { text: '2021', value: 2021 },
        { text: '2020', value: 2020 }
      ],
      value: 2021,
      pageConfig: [
        {
          gridId: '676302d9-b557-4618-a338-bbda00650103',
          title: this.$t('审批流配置'),
          grid: {
            allowReordering: false,
            allowResizing: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: resourceAnalysisColumn,
            allowPaging: false,
            dataSource: []
            // asyncConfig: {
            //   params: { year: 2021,supplierProvinceCode:'' },
            //   url: "/analysis/tenant/cockpit/biddingAmount/bar",
            //   recordsPosition: "data",
            // },
          }
        }
      ],
      getData: {
        commercialSupplierNumber: 0,
        unCommercialSupplierNumber: 0,
        sum: 0
      },
      areaCode: ''
    }
  },
  watch: {
    isShow(n) {
      if (n) {
        this.$nextTick(() => {
          // 切换列表后移动窗口 图表都挤在了一起
          let myevent = new Event('resize')
          window.dispatchEvent(myevent)
        })
      }
    }
  },
  created() {
    this.getBar()
  },
  mounted() {
    bus.$on('areaChange', (a) => {
      if (this.areaCode !== a) {
        this.areaCode = a
        this.getBar()
        // this.pageConfig[0].grid.asyncConfig.params.supplierProvinceCode = a;
      }
    })
    this.$nextTick(() => {
      // echarts容器的宽度根据父元素宽度变化进行自适应
      const resizeob = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (echarts.getInstanceByDom(entry.target)) {
            echarts.getInstanceByDom(entry.target).resize()
          }
        }
      })
      resizeob.observe(this.$refs.charts)
    })
    // this.mycharts();
    // this.drawLine();
  },
  methods: {
    mycharts(e) {
      let myChart = echarts.init(this.$refs.charts, 'macarons')
      myChart.setOption(
        {
          color: ['#6386C1'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
              crossStyle: {
                color: '#999'
              }
            },

            position: function (pt) {
              //提示框的位置
              return [pt[0], 30]
            }
          },
          grid: {
            //图表和父盒子之间的距离
            left: 0,
            right: 0,
            bottom: '5%',
            top: '15%',
            containLabel: true,
            width: '100%'
          },
          // dataZoom: [
          //   {
          //     type: "slider",
          //     show: true,
          //     xAxisIndex: [0],
          //     start: 0,
          //     end: 100,
          //     bottom: 15,
          //     textStyle: {
          //       color: "#ccd7d7",
          //     },
          //     height: 15,
          //   },
          // ],
          xAxis: {
            //x轴
            type: 'category',
            // boundaryGap: false,
            data: e.xAxis || [
              this.$t('品类一'),
              this.$t('品类二'),
              this.$t('品类三'),
              this.$t('品类四'),
              this.$t('品类五')
            ],
            axisLabel: {
              interval: 0,
              rotate: 30,
              textStyle: {
                color: '#292929',
                fontSize: 12
              },
              margin: 8
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: ''
              }
            },
            axisTick: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                //x轴的坐标文字
                show: true,
                textStyle: {
                  color: '#9A9A9A' //文字的颜色
                }
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#333333'
                }
              },
              axisTick: {
                show: false
              },
              splitLine: {
                //坐标在grid区域的分割线
                lineStyle: {
                  //设置分割线的样式(图表横线颜色)
                  color: ['#cccccc'],
                  type: 'dashed'
                }
              }
            },
            {
              type: 'value',
              show: false,
              max: 100,
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          series: [
            {
              itemStyle: {
                // 图形的形状
                barBorderRadius: [4, 4, 4, 4]
              },
              name: this.$t('金额'),
              type: 'bar', //柱状图
              data: e.series,
              barWidth: 20 //柱状体的宽度
            },
            {
              name: this.$t('占比'),
              type: 'line', //线状图
              yAxisIndex: 1,
              data: e.lineSeries
            }
          ]
        },
        true
      )

      //让图表自适应+防抖
      window.addEventListener('resize', resizeDebounce(resizeFn, 200))
      function resizeFn() {
        myChart.resize()
      }
      function resizeDebounce(fn, delay) {
        let timer = null
        return function () {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(fn, delay)
        }
      }
    },
    getBar() {
      this.$API.supplierDashboard
        .getBar({
          supplierProvinceCode: this.areaCode,
          // purOrgId: 1,
          year: this.value
        })
        .then((res) => {
          if (res && res.data.length > 0) {
            let a = { xAxis: [], series: [], lineSeries: [] }
            res.data.forEach((e) => {
              a.xAxis.push(e.categoryName)
              a.series.push(e.biddingAmount)
              a.lineSeries.push(e.biddingAmountRate)
            })
            this.$nextTick(() => {
              this.mycharts(a)
            })
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data)
          } else {
            this.showBox = true
          }
        })
    },
    change(e) {
      console.log(e)
      this.value = e.value
      this.getBar()
      // this.pageConfig[0].grid.asyncConfig.params.year = this.value;
    }
  }
}
</script>

<style lang="scss" scoped>
.ResourceAnalysis {
  flex: 1;
  padding: 20px 30px;
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .tableClass {
    height: 225px;
    overflow-y: scroll;
  }
  .ResourceAnalysis-charts {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    .righttop {
      width: 100%;
      height: 225px;
      margin-right: 10px;
    }
  }
}
</style>
