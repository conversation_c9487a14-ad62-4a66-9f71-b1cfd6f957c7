<template>
  <div class="CategorySuppliers">
    <div class="data-screen-title">
      {{ $t('一级品类供应商数') }}
      <span class="change-list" @click="isShowChart = !isShowChart">
        <mt-icon class="transfer-icon" name="icon_card_transfer"></mt-icon
        >{{ $t('切换列表') }}</span
      >
    </div>
    <div v-if="!showBox" class="mh225">
      <div v-show="isShowChart" class="CategorySuppliers-charts">
        <div class="righttop" ref="charts"></div>
      </div>
      <div v-show="!isShowChart" class="data-screen-chart-span tableClass">
        <mt-template-page
          ref="templateRef"
          :hidden-tabs="true"
          :template-config="pageConfig"
        ></mt-template-page>
      </div>
    </div>
    <div style="height: 180px" v-else>
      <emptyBox></emptyBox>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts' //   npm install echarts@4.9.0
import { supplierQuotient } from '../../config'
import bus from '@/utils/bus'
import emptyBox from '@/components/emptyData/emptyData.vue'

export default {
  components: { emptyBox },
  props: {
    organizationId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showBox: false,
      isShowChart: true,
      listArr: [],
      getData: {
        commercialSupplierNumber: 0,
        unCommercialSupplierNumber: 0,
        sum: 0
      },
      pageConfig: [
        {
          gridId: '0c244493-74b5-4ec6-96c9-64645564bae9',
          title: this.$t('审批流配置'),
          grid: {
            allowReordering: false,
            allowResizing: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: supplierQuotient,
            allowPaging: false,
            dataSource: []
            // asyncConfig: {
            //   methods: "post",
            //   params: {
            //     // categoryId: "",
            //     orgId: "1431073794369822721",
            //     areaCode: "",
            //   },
            //   url: "/analysis/tenant/cockpit/queryCategorySupplier",
            //   recordsPosition: "data.list",
            // },
          }
        }
      ],
      orgId: '1431073794369822721',
      areaCode: ''
    }
  },
  watch: {
    isShowChart(n) {
      if (n) {
        this.$nextTick(() => {
          // 切换列表后移动窗口 图表都挤在了一起
          let myevent = new Event('resize')
          window.dispatchEvent(myevent)
        })
      }
    }
  },
  created() {
    let id = sessionStorage.getItem('cockUserId')
    this.orgId = id
    // this.pageConfig[0].grid.asyncConfig.params.orgId = id;
    this.queryCategorySupplier()
  },
  mounted() {
    // this.mycharts();
    bus.$on('companyChange', (a) => {
      if (this.orgId !== a) {
        this.orgId = a
        // this.pageConfig[0].grid.asyncConfig.params.orgId = a;
        this.queryCategorySupplier()
      }
    })
    bus.$on('areaChange', (a) => {
      if (this.areaCode !== a) {
        this.areaCode = a
        // this.pageConfig[0].grid.asyncConfig.params.areaCode = a;
        this.queryCategorySupplier()
      }
    })
    // echarts容器的宽度根据父元素宽度变化进行自适应
    this.$nextTick(() => {
      const resizeob = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (echarts.getInstanceByDom(entry.target)) {
            echarts.getInstanceByDom(entry.target).resize()
          }
        }
      })
      resizeob.observe(this.$refs.charts)
    })
  },
  methods: {
    mycharts(e) {
      let myChart = echarts.init(this.$refs.charts, 'macarons')
      myChart.setOption(
        {
          color: ['#8ACC40', '#6386C1', '#00469C', '#EDA133', '#ED5633'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
              crossStyle: {
                color: '#999'
              }
            },
            position: function (pt) {
              //提示框的位置
              return [pt[0], 30]
            }
          },

          legend: {
            top: 0,
            right: 10
          },

          grid: {
            //图表和父盒子之间的距离
            left: '0%',
            right: '0%',
            bottom: '5%',
            top: '15%',
            containLabel: true
          },
          // dataZoom: [
          //   {
          //     type: "slider",
          //     show: true,
          //     xAxisIndex: [0],
          //     start: 0,
          //     end: 100,
          //     bottom: 15,
          //     textStyle: {
          //       color: "#ccd7d7",
          //     },
          //     height: 15,
          //   },
          // ],
          xAxis: {
            //x轴
            type: 'category',
            // boundaryGap: false,
            data: e.xAxis,
            axisLabel: {
              interval: 0,
              rotate: 30,
              textStyle: {
                color: '#292929',
                fontSize: 12
              }
              // margin: 8,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: ''
              }
            },
            axisTick: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                textStyle: {
                  color: '#9A9A9A' //文字的颜色
                }
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#333333'
                }
              },
              axisTick: {
                show: false
              },
              splitLine: {
                //坐标在grid区域的分割线
                lineStyle: {
                  //设置分割线的样式(图表横线颜色)
                  color: ['#cccccc'],
                  type: 'dashed'
                }
              }
            },
            {
              type: 'value',
              show: false,
              max: 100,
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          series: e.series
        },
        true
      )

      //让图表自适应+防抖
      window.addEventListener('resize', resizeDebounce(resizeFn, 200))
      function resizeFn() {
        myChart.resize()
      }
      function resizeDebounce(fn, delay) {
        let timer = null
        return function () {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(fn, delay)
        }
      }
    },
    getOneArr(arr, key) {
      var newobj = {},
        newArr = []
      if (arr) {
        for (var i = 0; i < arr.length; i++) {
          var item = arr[i]
          if (!newobj[item[key]]) {
            newobj[item[key]] = newArr.push(item)
          }
        }
      }
      return newArr
    },
    queryCategorySupplier() {
      this.$loading()
      this.$API.supplierDashboard
        .queryCategorySupplier({
          // categoryId: "",
          orgId: this.orgId,
          areaCode: this.areaCode
        })
        .then((res) => {
          if (
            res.data.labelDTOSet !== null &&
            res.data.list !== null &&
            res.data.labelDTOSet.length > 0 &&
            res.data.list.length > 0
          ) {
            this.showBox = false
            let a = { xAxis: [], series: [[], [], [], [], []] }
            let labelDTOSet = []
            let b = this.getOneArr(res.data.list, 'categoryCode')
            let lineArr = [] //折线图
            let xAxis = b.map((e) => {
              return e.categoryName
            })
            // 分级
            res.data.labelDTOSet.forEach((item) => {
              let x = [] // 各分级的data数组
              res.data.list.forEach((listItem) => {
                if (!!listItem.gradeDTOList && listItem.gradeDTOList.length > 0) {
                  listItem.gradeDTOList.forEach((gItem) => {
                    if (item.labelName == gItem.labelName) {
                      x.push(gItem.supplierCount)
                    }
                  })
                }
              })
              labelDTOSet.push({
                name: item.labelName,
                type: 'bar',
                stack: 'total',
                emphasis: {
                  focus: 'series'
                },
                data: x,
                barWidth: '20px'
              })
            })
            res.data.list.forEach((item) => {
              lineArr.push(item.supplierCountRate)
            })
            labelDTOSet.push({
              name: this.$t('占比'),
              type: 'line',
              yAxisIndex: 1,
              data: lineArr
            })
            a.xAxis = xAxis
            a.series = labelDTOSet
            this.$nextTick(() => {
              this.mycharts(a)
            })
            // 组成列表所需的结构
            let array = []
            res.data.list.forEach((item) => {
              if (!!item.gradeDTOList && item.gradeDTOList.length > 0) {
                item.gradeDTOList.forEach((gItem) => {
                  let obj = {
                    labelName: gItem.labelName,
                    supplierCount: gItem.supplierCount,
                    categoryName: item.categoryName
                  }
                  array.push(obj)
                })
              }
            })
            this.$set(this.pageConfig[0].grid, 'dataSource', array)
            this.$hloading()
          } else {
            this.showBox = true
            this.$hloading()
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.CategorySuppliers {
  width: 100%;
  padding: 20px 30px;
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .tableClass {
    max-height: 240px;
    overflow-y: scroll;
  }
  .CategorySuppliers-charts {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    .righttop {
      width: 100%;
      height: 225px;
      margin-right: 10px;
    }
  }
}
</style>
