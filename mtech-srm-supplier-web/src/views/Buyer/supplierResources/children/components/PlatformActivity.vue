<template>
  <div class="PlatformActivity">
    <div class="data-screen-title">
      {{ $t('平台活跃度') }}
      <div class="choose-div">
        <div
          class="choose-un"
          :class="activeId === item.id ? 'choose-active' : ''"
          v-for="(item, i) in chooseArr"
          :key="i"
          @click="clickChoose(item)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <!-- <div class="data-screen-select">
      <mt-select
        :data-source="dataArr"
        :value="value"
        placeholder=""
      ></mt-select>
    </div> -->
    <div class="PlatformActivity-s" v-if="!showBox">
      <div ref="myEchart"></div>
      <div :style="{ height: '250px', width: '100%', margin: '0 auto' }" ref="myEchart2"></div>
    </div>
    <div style="height: 235px" v-else>
      <emptyBox></emptyBox>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import bus from '@/utils/bus'
import emptyBox from '@/components/emptyData/emptyData.vue'

export default {
  components: { emptyBox },
  props: {
    organizationId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showBox: false,
      chart: null,
      activeId: 1,
      value: 2021,
      dataArr: [
        { text: '2021', value: 2021 },
        { text: '2020', value: 2020 }
      ],
      chooseArr: [
        { id: 1, text: this.$t('日') },
        { id: 2, text: this.$t('月') },
        { id: 3, text: this.$t('季') },
        { id: 4, text: this.$t('年') }
      ],
      areaCode: ''
    }
  },
  created() {
    this.supplierActivity()
  },
  mounted() {
    bus.$on('areaChange', (a) => {
      if (this.areaCode !== a) {
        this.areaCode = a
        this.supplierActivity()
      }
    })
    this.$nextTick(() => {
      // echarts容器的宽度根据父元素宽度变化进行自适应
      const resizeob = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (echarts.getInstanceByDom(entry.target)) {
            echarts.getInstanceByDom(entry.target).resize()
          }
        }
      })
      resizeob.observe(this.$refs.myEchart2)
    })

    // this.muCharts2();
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    muCharts2(data) {
      let myChart = echarts.init(this.$refs.myEchart2) //这里是为了获得容器所在位置
      myChart.setOption(
        {
          tooltip: {
            trigger: 'item'
          },
          color: ['rgba(138,204,64,0.5)'],
          xAxis: {
            axisLabel: {
              interval: 0,
              rotate: 20
            },
            type: 'category',
            boundaryGap: false,
            data: data.xData || ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
          },
          yAxis: {
            type: 'value'
          },
          grid: {
            containLabel: true,
            left: '5%',
            bottom: 0
          },
          series: [
            {
              data: data.series0 || [820, 932, 901, 934, 1290, 1330, 1320],
              type: 'line',
              areaStyle: {}
            }
          ]
        },
        true
      )
      //让图表自适应+防抖
      window.addEventListener('resize', resizeDebounce(resizeFn, 200))
      function resizeFn() {
        myChart.resize()
      }
      function resizeDebounce(fn, delay) {
        let timer = null
        return function () {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(fn, delay)
        }
      }
    },
    clickChoose(item) {
      this.activeId = item.id
      this.supplierActivity()
    },
    supplierActivity() {
      this.$API.supplierDashboard
        .supplierActivity({
          supplierProvinceCode: this.areaCode,
          // categoryId: 1,
          dataType: this.activeId
        })
        .then((res) => {
          if (res.data && res.data.length > 0) {
            this.showBox = false
            let data2 = { xData: [], series0: [] }
            res.data.forEach((e) => {
              data2.xData.push(e.dataCycle)
              data2.series0.push(e.activityCount)
            })
            this.$nextTick(() => {
              this.muCharts2(data2)
            })
          } else {
            this.showBox = true
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.PlatformActivity {
  padding: 20px 30px;
  position: relative;
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .PlatformActivity-s {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .bottom-title {
    width: 70px;
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
  }
  .powerDiv {
    width: 20px;
    height: 98px;
    background: linear-gradient(180deg, rgba(232, 232, 232, 1) 0%, rgba(51, 107, 176, 1) 100%);
    position: absolute;
    bottom: 40px;
    left: 30px;
  }
}
</style>
