<template>
  <div class="RightTitle">
    <div class="select-title">
      <div class="select">
        <!-- 平台驾驶仓，默认全球,不用选地区 -->
        <mt-select
          css-class="selectClass"
          :data-source="dataArr"
          :show-clear-button="true"
          :disabled="true"
          :placeholder="$t('全球')"
        ></mt-select>
        <!-- <mt-DropDownTree v-if="!!areaId && areaId.length > 0 && !!fields.dataSource && fields.dataSource.length >0"
          css-class="selectClass"
          v-model="areaId"
          :id="'areamore_'+new Date().getTime()"
          :popup-height="300"
          :popup-width="300"
          :fields="fields"
          :show-clear-button="false"
          @select="change"
        ></mt-DropDownTree> -->
      </div>
    </div>
    <div style="display: flex">
      <div class="up-item">
        <div class="text-black">{{ $t('供应商总数') }}</div>
        <div class="pop-tip" v-show="!!total.supplierCount">
          {{ total.supplierCount || 0 }}
        </div>
        <div class="text-2 lightBlue">{{ total.supplierCount || 0 }}</div>
      </div>
      <div class="up-item">
        <div class="text-black">{{ $t('年度环比') }}</div>
        <div class="text-2 green">{{ total.yearOnYear || 0 }}%</div>
      </div>
      <div class="up-item">
        <div class="text-black">{{ $t('季度环比') }}</div>
        <div class="text-2 red">{{ total.yearOnYear || 0 }}%</div>
      </div>
      <div class="up-item">
        <div class="text-black">{{ $t('月度环比') }}</div>
        <div class="text-2 green">{{ total.monthOnMonth || 0 }}%</div>
      </div>
    </div>
  </div>
</template>
<script>
import bus from '@/utils/bus'
export default {
  props: {
    isSupplierShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      value: '',
      chooseArr: [
        { id: 0, text: this.$t('平台') },
        { id: 1, text: this.$t('集团') }
      ],
      fields: {
        dataSource: [{ id: '' }],
        value: 'id',
        text: 'areaName',
        child: ''
      },
      dataArr: [],
      total: {},
      areaId: []
    }
  },
  created() {
    sessionStorage.setItem('cockUserId', '')
    this.queryTotalCount()
    this.selectByParentCode()
  },
  methods: {
    change(e) {
      this.value = e.id
      // this.value = e.value[0] || 1;
      if (this.value) {
        this.queryTotalCount()
        bus.$emit('areaChange', e.id)
      }
    },
    // 综合数据
    queryTotalCount() {
      this.$API.supplierDashboard
        .queryTotalCount({
          supplierProvinceCode: this.value,
          orgId: ''
        })
        .then((res) => {
          this.total = { ...res.data }
        })
    },
    // 获取城市数据
    selectByParentCode() {
      this.$API.supplierInvitationAdd
        .selectByParentCode({
          parentCode: ''
        })
        .then((res) => {
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.unshift({ id: '', areaName: this.$t('全部地区') })
          this.fields = Object.assign({}, this.fields, { dataSource: arr })
          this.areaId = [arr[0].id]
        })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-ddt.e-input-group.e-control-wrapper .e-input-group-icon.e-ddt-icon {
  background-color: transparent !important;
}
.RightTitle {
  width: 100%;
  padding: 30px 0 0 30px;
  .select-title {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 50px;
  }
  .text-black {
    font-size: 16px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin: 0 0 10px 0;
  }
  .text-1 {
    width: 178px;
    height: 36px;
    font-size: 36px;
    font-family: DINAlternate;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .text-2 {
    width: 78px;
    height: 36px;
    font-size: 24px;
    font-family: DINAlternate;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .select {
    width: 210px;
    height: 50px;
    background: rgba(250, 250, 250, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    margin-right: 20px;
    display: flex;
    flex-direction: row;
    align-content: center;
    flex-wrap: wrap;
    height: 40px;
  }
  .text {
    width: 84px;
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
  }
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .blue {
    color: #00469c;
  }
  .lightblue {
    color: #6386c1;
  }
  .red {
    color: #ed5633;
  }
  .green {
    color: #8acc40;
  }
}
.RightTitle .up-item {
  min-width: 60px;
  height: 80px;
  text-align: left;
  margin-bottom: 20px;
  padding: 10px 60px 8px 0px;
  position: relative;
  cursor: pointer;
  .pop-tip {
    display: none;
    position: absolute;
    top: 13px;
    left: 0;
    z-index: 9;
    padding: 5px 10px;
    color: #fff;
    background-color: rgb(113, 113, 113);
    border-radius: 2px;
    text-align: center;
    font-size: 12px;
  }
}
.up-item:hover {
  .pop-tip {
    display: block;
  }
}
</style>
