<template>
  <div class="QuantityChange">
    <div class="data-screen-title">
      {{ $t('供应商数量变化') }}
      <span class="change-list" @click="isShow = !isShow"
        ><mt-icon class="transfer-icon" name="icon_card_transfer"></mt-icon
        >{{ $t('切换列表') }}</span
      >
      <!-- <span class="linkRelative">环比 +</span> -->
    </div>
    <div class="data-screen-title" :style="!showBox ? 'position:relative' : ''">
      <!-- <div class="data-screen-select">
        <mt-select
          :data-source="dataArr"
          :value="year"
          placeholder=""
          :change="yearChange"
        ></mt-select>
      </div>  style="left: 115px"-->
      <div>
        <div class="data-screen-select" id="quantityChange" @click="displayTreeView">
          <span>{{ plId }}</span>
          <MtIcon
            name="icon_table_arrow"
            style="font-size: 12px; margin-left: auto; margin-right: 5px"
          />
        </div>
        <div v-show="showTree">
          <div slot="content" class="select-content" id="treeViewqc">
            <mt-treeView
              :id="'qc' + new Date().getTime()"
              class="tree-view--template"
              ref="treeView"
              :fields="fields"
              :auto-check="true"
              :show-check-box="false"
              @nodeSelecting="nodeSelecting"
            ></mt-treeView>
          </div>
        </div>
      </div>
      <div class="choose-div">
        <div
          class="choose-un"
          :class="activeId === item.id ? 'choose-active' : ''"
          v-for="(item, i) in chooseArr"
          :key="i"
          @click="clickChoose(item)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <div v-if="!showBox" class="mh225">
      <div v-show="!isShow" class="data-screen-chart-span tableClass" style="margin-top: 0px">
        <mt-template-page
          ref="templateRef"
          :hidden-tabs="true"
          :template-config="pageConfig"
        ></mt-template-page>
      </div>
      <div class="QuantityChange-s" v-show="isShow">
        <div :style="{ height: '225px', width: '100%' }" ref="myEchart2"></div>
      </div>
    </div>
    <div style="height: 205px" v-else>
      <emptyBox></emptyBox>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { quantitySupplier } from '../../config'
import bus from '@/utils/bus'
import emptyBox from '@/components/emptyData/emptyData.vue'

let isInitHideTree = false
let containsArr = []
let containsSelect = []
export default {
  components: { emptyBox },
  props: {
    organizationId: {
      type: Object,
      default: () => {}
    },
    infoSetting: {
      type: Object,
      default: () => {
        return {
          isShowScore: true, // 是否显示右侧的数字
          dataArrPL: [] // 品类接口返回的数据 （因为好多处用这个接口所以写到外面了）
        }
      }
    }
  },
  data() {
    return {
      showTree: false,
      showBox: false,
      plId: '',
      labelName: '',
      isShow: true,
      chart: null,
      activeId: 3,
      chooseArr: [
        { id: 3, text: this.$t('年') },
        { id: 2, text: this.$t('季') },
        { id: 1, text: this.$t('月') }
      ],
      dataArr: [
        { text: this.$t('增量'), value: 2021 },
        { text: this.$t('减量'), value: 2020 }
      ],
      value: '',
      year: 2021,
      fields: {
        dataSource: [],
        id: 'id',
        text: 'categoryName',
        parentID: 'parentId'
      },
      pageConfig: [
        {
          gridId: 'b7a2a294-715f-4419-b9fb-a46ce2b61ff8',
          title: this.$t('审批流配置'),
          grid: {
            allowReordering: false,
            allowResizing: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: quantitySupplier,
            allowPaging: false,
            dataSource: []
            // asyncConfig: {
            //   methods: "post",
            //   params: {
            //     categoryId:"",
            //     orgId:"",
            //     dateType:3,
            //     areaCode:""
            //   },
            //   url: "/analysis/tenant/cockpit/querySupplierChange",
            //   recordsPosition: "data.list",
            // },
          }
        }
      ],
      orgId: '1399246009909403649',
      areaCode: '',
      timer: false
    }
  },
  watch: {
    infoSetting: {
      handler() {
        if (this.infoSetting.dataArrPL.length > 0) {
          const ref = this.$refs.treeView
          if (ref.ejsInstances.groupedData.length > 0) {
            return
          }
          ref.ejsInstances.addNodes(this.infoSetting.dataArrPL)
          this.plId = this.infoSetting.dataArrPL[0].categoryName
        }
      },
      deep: true
    },
    isShow(n) {
      if (n) {
        this.$nextTick(() => {
          // 切换列表后移动窗口 图表都挤在了一起
          let myevent = new Event('resize')
          window.dispatchEvent(myevent)
        })
      }
    }
  },
  created() {
    // 获取当前公司id
    let id = sessionStorage.getItem('cockUserId')
    this.orgId = id
    // this.pageConfig[0].grid.asyncConfig.params.orgId = id;
    this.querySupplierChange()
    // this.getProdtreeInClient();
  },
  mounted() {
    bus.$on('companyChange', (a) => {
      if (a !== this.orgId) {
        this.orgId = a
        this.querySupplierChange()
        // this.pageConfig[0].grid.asyncConfig.params.orgId = a;
      }
    })
    bus.$on('areaChange', (a) => {
      if (this.areaCode !== a) {
        this.areaCode = a
        // this.pageConfig[0].grid.asyncConfig.params.areaCode = a;
        this.querySupplierChange()
      }
    })
    this.initDownTree()
    this.$nextTick(() => {
      // echarts容器的宽度根据父元素宽度变化进行自适应
      const resizeob = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (echarts.getInstanceByDom(entry.target)) {
            echarts.getInstanceByDom(entry.target).resize()
          }
        }
      })
      resizeob.observe(this.$refs.myEchart2)
    })
  },
  // beforeDestroy() {
  //   if (!this.chart) {
  //     return
  //   }
  //   this.chart.dispose()
  //   this.chart = null
  // },
  methods: {
    // 展示按钮
    displayTreeView() {
      this.showTree = !this.showTree
      !isInitHideTree && this.showTree && this.initDownTree()
    },
    // 初始化点击周边 关闭tree 点击外部隐藏 内部没是
    clickInsideFun(e) {
      containsArr = document.getElementById('treeViewqc')
      containsSelect = document.getElementById('quantityChange')
      if (
        (!!containsArr && containsArr.contains(e.target)) ||
        (!!containsSelect && containsSelect.contains(e.target))
      ) {
        // console.log("在内");
      } else {
        this.showTree = false
      }
    },
    initDownTree() {
      isInitHideTree = true
      document.addEventListener('click', this.clickInsideFun)
    },
    //品类分类
    getProdtreeInClient(item = 0) {
      this.$API.supplierInvitationAdd
        .getProdtreeInClient({ id: item })
        .then((res) => {
          if (res.data.length > 0) {
            const ref = this.$refs.treeView
            ref.ejsInstances.addNodes(res.data)
            if (res.data[0].parentId == '0') {
              this.plId = res.data[0].categoryName
            }
          } else {
            this.displayTreeView()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg ? err.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    nodeSelecting(e) {
      this.plId = e.nodeData.text
      this.value = e.nodeData.text == this.$t('不限品类') ? '' : e.nodeData.id
      if (this.value || this.value == '') {
        this.getProdtreeInClient(e.nodeData.id)
        this.querySupplierChange()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    muCharts2(data) {
      let myChart = echarts.init(this.$refs.myEchart2) //这里是为了获得容器所在位置
      myChart.setOption(
        {
          color: ['#8ACC40', '#6386C1', '#00469C', '#EDA133', '#ED5633'],
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            axisLabel: {
              interval: 0,
              rotate: 30
            },
            type: 'category',
            boundaryGap: false,
            data: data.xData
          },
          legend: {
            icon: 'circle',
            top: 10
          },
          grid: {
            containLabel: true,
            bottom: 0
          },
          yAxis: {
            type: 'value'
          },
          series: data.series0
        },
        true
      )

      //让图表自适应+防抖
      window.addEventListener('resize', resizeDebounce(resizeFn, 200))
      function resizeFn() {
        myChart.resize()
      }
      function resizeDebounce(fn, delay) {
        let timer = null
        return function () {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(fn, delay)
        }
      }
    },
    yearChange(e) {
      this.year = e.value
      this.querySupplierChange()
    },
    change(e) {
      this.value = e.id
      if (this.value) {
        this.querySupplierChange()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    clickChoose(item) {
      this.activeId = item.id
      this.querySupplierChange()
      // this.pageConfig[0].grid.asyncConfig.params.dateType = this.activeId;
    },
    querySupplierChange() {
      this.$API.supplierDashboard
        .querySupplierChange({
          categoryId: this.value,
          orgId: this.orgId,
          dateType: this.activeId,
          areaCode: this.areaCode
        })
        .then((res) => {
          if (
            res.data.labelDTOSet !== null &&
            res.data.list !== null &&
            res.data.labelDTOSet.length > 0 &&
            res.data.list.length > 0
          ) {
            this.showBox = false
            let data2 = { xData: [], series0: [] }
            let dataArr = []
            let dataName = []
            for (let i = 0; i < res.data.labelDTOSet.length; i++) {
              let labelName = res.data.labelDTOSet[i].labelName
              let obj = { name: labelName, type: 'line', data: [] }
              dataArr.push(obj) //组成图表的series数据
            }
            res.data.list.forEach((item) => {
              dataName.push(item.month)
              dataArr.forEach((a, b) => {
                if (item.labelName == a.name) {
                  dataArr[b].data.push(item.supplierCount)
                }
              })
            })
            data2.xData = Array.from(new Set(dataName))
            data2.series0 = dataArr
            this.$nextTick(() => {
              this.muCharts2(data2)
            })
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data.list)
          } else {
            this.showBox = true
          }
        })
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickInsideFun)
    containsArr = null
    containsSelect = null
  }
}
</script>
<style lang="scss" scoped>
/deep/.e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon {
  padding-top: 0 !important;
}
.QuantityChange {
  width: 100%;
  padding: 20px 30px;
  .tableClass {
    max-height: 225px;
    overflow-y: scroll;
  }
  position: relative;
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .echar-wrap-imag {
    margin: 20px;
  }
  .tspan {
    position: relative;
    height: 30px;
    display: flex;
  }
  .QuantityChange-s {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .powerDiv {
    width: 20px;
    height: 98px;
    background: linear-gradient(180deg, rgba(232, 232, 232, 1) 0%, rgba(51, 107, 176, 1) 100%);
    position: absolute;
    bottom: 40px;
    left: 30px;
  }
}
</style>
