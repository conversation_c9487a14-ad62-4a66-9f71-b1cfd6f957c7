<template>
  <div class="TagCloud">
    <div class="hot-tag">{{ $t('热门标签') }}</div>
    <div style="display: flex; justify-content: center">
      <word-cloud
        :data="defaultWords"
        name-key="name"
        value-key="value"
        :rotate="{ from: 0, to: 0 }"
        :color="myColors"
        :show-tooltip="false"
        :word-click="wordClickHandler"
      >
      </word-cloud>
    </div>
    <div class="hot-tag">{{ $t('各品类中标额最高供应商') }}</div>
    <div class="hot-list">
      <div class="hot-list-1" v-for="(item, i) in hotListArr" :key="i">
        <span>{{ item.itemName }}</span>
        <span>{{ item.supplierEnterpriseName }}</span>
        <span>￥{{ item.biddingTotalPrice }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import wordCloud from 'vue-wordcloud'

export default {
  name: 'App',
  props: {
    organizationId: {
      type: String,
      default: ''
    }
  },
  components: {
    wordCloud
  },
  data() {
    return {
      myColors: ['#9BAAC1', '#A2B0C6'],
      defaultWords: [],
      hotListArr: []
    }
  },
  created() {
    this.getHotLabels()
    this.getBidding()
  },
  methods: {
    wordClickHandler(name, value, vm) {
      console.log('wordClickHandler', name, value, vm)
    },
    getHotLabels() {
      this.$API.supplierDashboard
        .getHotLabels({ organizationId: this.organizationId })
        .then((res) => {
          // this.info = res.data;
          let a = []
          res.data.forEach((e) => {
            a.push({ name: e.itemName, value: e.itemCount })
          })
          this.defaultWords = a
        })
    },
    getBidding() {
      this.$API.supplierDashboard
        .getBidding({
          organizationId: this.organizationId
        })
        .then((res) => {
          this.hotListArr = res.data.biddingStatistics
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.TagCloud {
  /deep/ .wordCloud {
    width: 450px;
    height: 320px;
  }
  .hot-tag {
    margin: 20px 30px;
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }
  .hot-list {
    height: 292px;
    overflow: auto;
    .hot-list-1 {
      display: flex;
      padding: 10px 30px;
      font-size: 13px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      flex-wrap: nowrap;
      justify-content: space-between;
    }
  }
}
</style>
