<template>
  <div class="listTable">
    <div class="table-top">
      <div>{{ $t('黑名单清单') }}</div>
      <div style="display: none"><img src="../image/icon_card_download.svg" />{{ $t('下载') }}</div>
    </div>
    <div class="table-content">
      <mt-template-page
        class="tableStyle"
        ref="blackTemplatePage"
        :template-config="pageConfigBlack"
        :hidden-tabs="true"
        :padding-top="false"
      >
      </mt-template-page>
    </div>
  </div>
</template>
<script>
import { blackListColumn } from '../../config/index.js'
import utils from '@/utils/utils'
export default {
  data() {
    return {
      pageConfigBlack: [
        {
          gridId: 'fae93b5e-3f64-4775-8b94-44bbed5dacf1',
          grid: {
            allowReordering: false,
            allowResizing: false,
            allowSorting: true, //允许表格排序操作(表头)
            allowFiltering: false,
            dataSource: [],
            columnData: blackListColumn,
            allowPaging: false
          }
        }
      ]
    }
  },
  mounted() {
    this.blackList()
    this.getDictItem()
  },
  methods: {
    blackList() {
      let that = this
      that.$API.supplierDashboard
        .getBlackList()
        .then((res) => {
          if (res.code === 200 && Array.isArray(res.data) && res.data.length > 0) {
            let dataSource = res.data.map((v) => {
              v.supplierEnterpriseName = v.supplierEnterpriseName
                ? v.supplierEnterpriseName.substr(0, 1) + '**' + v.supplierEnterpriseName.substr(3)
                : '--'
              return v
            })
            that.pageConfigBlack[0].grid.dataSource = dataSource
          }
        })
        .catch((err) => {
          that.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 获取 惩罚原因枚举
    getDictItem(dictCode = 'violationType') {
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            let violationTypeList = {}
            res.data.forEach((v) => {
              violationTypeList[v.itemCode] = v.name
            })
            this.$set(
              this.pageConfigBlack[0].grid,
              'columnData',
              blackListColumn(violationTypeList)
            )
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg,
            type: 'warning'
          })
        })
    }
  }
}
</script>
<style lang="scss">
.listTable {
  .table-top {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    font-size: 14px;
    padding: 0 20px;
    line-height: 40px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(255, 255, 255, 1);
    border-radius: 8px 8px 0 0;
    background: rgba(155, 170, 193, 1);
    img {
      width: 14px;
      height: 14px;
      vertical-align: middle;
      margin-right: 6px;
    }
  }
  .table-content {
    width: 100%;
    height: 280px;
    background-color: white;
    border: 1px solid transparent;
    .tableStyle {
      margin: 10px;
      border-bottom: 1px solid #e8e8e8 !important;
      height: 270px;
      overflow-y: scroll;
    }
    .common-template-page {
      width: auto !important;
      .grid-container {
        border-radius: 8px 8px 0 0 !important;
      }
    }
  }
}
</style>
