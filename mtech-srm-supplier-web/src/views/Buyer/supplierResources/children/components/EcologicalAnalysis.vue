<template>
  <div class="PlatformActivity">
    <div class="data-screen-title">
      {{ $t('供应商生态分析') }}
      <span class="change-list" @click="isShow = !isShow"
        ><mt-icon class="transfer-icon" name="icon_card_transfer"></mt-icon
        >{{ $t('切换列表') }}</span
      >
      <!-- <div class="choose-div">
        <div
          class="choose-un"
          :class="activeId === item.id ? 'choose-active' : ''"
          v-for="(item, i) in chooseArr"
          :key="i"
          @click="clickChoose(item)"
        >
          {{ item.text }}
        </div>
      </div> -->
    </div>
    <span class="tspan" :style="!showBox ? '' : 'position:relative'">
      <div class="data-screen-select">
        <mt-select
          :data-source="dataArr"
          :value="year"
          placeholder=""
          :change="yearChange"
        ></mt-select>
      </div>
      <div style="margin-left: 20px; display: none">
        <div class="data-screen-select" id="ecologicalAna" @click="displayTreeView">
          <span>{{ plId }}</span>
          <MtIcon
            name="icon_table_arrow"
            style="font-size: 12px; margin-left: auto; margin-right: 5px"
          />
        </div>
        <div v-show="showTree">
          <div slot="content" id="treeViewEc" class="select-content">
            <mt-treeView
              class="tree-view--template"
              ref="treeView"
              :id="'ecological' + new Date().getTime()"
              :fields="fields"
              :auto-check="true"
              :show-check-box="false"
              @nodeSelecting="nodeSelecting"
            ></mt-treeView>
          </div>
        </div>
      </div>
    </span>
    <div v-if="!showBox" class="mh225">
      <span v-show="!isShow" class="data-screen-chart-span">
        <mt-template-page
          ref="templateRef"
          :hidden-tabs="true"
          :template-config="pageConfig"
        ></mt-template-page
      ></span>
      <span v-show="isShow">
        <div class="PlatformActivity-charts">
          <div class="drowCircle-parent">
            <div :style="{ height: '225px', width: '100%', margin: '0 auto' }" ref="drawLine"></div>
            <div class="tips-box">
              <div class="tips">
                <div class="pop-tip">{{ getData.purAmount || 0 }}</div>
                <div class="num" style="color: #00469c">
                  {{ getData.purAmount || 0 }}
                </div>
                <div class="word">{{ $t('采购金额') }}</div>
              </div>
              <div class="tips">
                <div class="pop-tip">{{ getData.saleAmount || 0 }}</div>
                <div class="num" style="color: #eda133">
                  {{ getData.saleAmount || 0 }}
                </div>
                <div class="word">{{ $t('销售金额') }}</div>
              </div>
            </div>
          </div>
        </div>
      </span>
    </div>
    <div style="height: 205px" v-else>
      <emptyBox></emptyBox>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts' //   npm install echarts@4.9.0
import { ecologicalAnalysisColumn } from '../../config'
import emptyBox from '@/components/emptyData/emptyData.vue'

let isInitHideTree = false
let containsArr = []
let containsSelect = []
export default {
  components: { emptyBox },
  props: {
    organizationId: {
      type: Object,
      default: () => {}
    },
    infoSetting: {
      type: Object,
      default: () => {
        return {
          isShowScore: true, // 是否显示右侧的数字
          dataArrPL: [] // 品类接口返回的数据 （因为好多处用这个接口所以写到外面了）
        }
      }
    }
  },
  data() {
    return {
      showTree: false,
      plId: '',
      showBox: false,
      isShow: true,
      listArr: [],
      getData: {
        purAmount: 1048,
        saleAmount: 735,
        sum: 0
      },
      value: '',
      year: 2021,
      fields: {
        dataSource: [],
        id: 'id',
        text: 'categoryName',
        parentID: 'parentId'
      },
      activeId: 0,
      dataArr: [
        { text: '2021', value: 2021 },
        { text: '2020', value: 2020 }
      ],
      chooseArr: [
        { id: 0, text: this.$t('月') },
        { id: 1, text: this.$t('季') },
        { id: 2, text: this.$t('年') }
      ],
      pageConfig: [
        {
          gridId: 'f52a5d44-69c6-415d-8161-07422c58d2a6',
          title: this.$t('审批流配置'),
          grid: {
            allowReordering: false,
            allowResizing: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: ecologicalAnalysisColumn,
            allowPaging: false,
            dataSource: []
            // asyncConfig: {
            //   methods: "get",
            //   params: { categoryId: '', year: 2021 },
            //   url: "/analysis/tenant/cockpit/queryEcology",
            //   recordsPosition: "data",
            // },
          }
        }
      ]
    }
  },
  watch: {
    infoSetting: {
      handler() {
        if (this.infoSetting.dataArrPL.length > 0) {
          const ref = this.$refs.treeView
          if (ref.ejsInstances.groupedData.length > 0) {
            return
          }
          ref.ejsInstances.addNodes(this.infoSetting.dataArrPL)
          this.plId = this.infoSetting.dataArrPL[0].categoryName
        }
      },
      deep: true
    },
    isShow(n) {
      if (n) {
        this.$nextTick(() => {
          // 切换列表后移动窗口 图表都挤在了一起
          let myevent = new Event('resize')
          window.dispatchEvent(myevent)
        })
      }
    }
  },
  created() {
    // this.getProdtreeInClient();
    this.queryEcology()
  },
  mounted() {
    this.initDownTree()
    this.$nextTick(() => {
      // echarts容器的宽度根据父元素宽度变化进行自适应
      const resizeob = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (echarts.getInstanceByDom(entry.target)) {
            echarts.getInstanceByDom(entry.target).resize()
          }
        }
      })
      resizeob.observe(this.$refs.drawLine)
    })

    // this.$set(this.fields, "dataSource", this.infoSetting.dataArrPL);
  },
  methods: {
    getProdtreeInClient(item = 0) {
      this.$API.supplierInvitationAdd
        .getProdtreeInClient({ id: item })
        .then((res) => {
          if (res.data.length > 0) {
            const ref = this.$refs.treeView
            ref.ejsInstances.addNodes(res.data)
            if (res.data[0].parentId == '0') {
              this.plId = res.data[0].categoryName
            }
          } else {
            this.displayTreeView()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg ? err.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    nodeSelecting(e) {
      this.plId = e.nodeData.text
      this.value = e.nodeData.text == this.$t('不限品类') ? '' : e.nodeData.id
      if (this.value || this.value == '') {
        this.getProdtreeInClient(e.nodeData.id)
        this.queryEcology()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    drawLine(e) {
      let myChart = echarts.init(this.$refs.drawLine, 'macarons')
      let option = {
        tooltip: {
          trigger: 'item',
          position: ['0%', '40%']
        },
        color: ['#00469C', '#EDA133'],
        series: [
          {
            name: this.$t('供应商'),
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            right: '40%',
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: '25',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: e.data || [
              { value: 1048, name: this.$t('采购金额') },
              { value: 735, name: this.$t('销售金额') }
            ]
          }
        ]
      }
      myChart.setOption(option, true)

      //让图表自适应+防抖
      window.addEventListener('resize', resizeDebounce(resizeFn, 200))
      function resizeFn() {
        myChart.resize()
      }
      function resizeDebounce(fn, delay) {
        let timer = null
        return function () {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(fn, delay)
        }
      }
    },
    yearChange(e) {
      this.year = e.value
      this.queryEcology()
      // this.pageConfig[0].grid.asyncConfig.params.year = this.year;
    },
    change(e) {
      this.value = e.value[0] || 1
      this.queryEcology()
      // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
    },
    clickChoose(item) {
      this.activeId = item.id
      // this.getQuantity();
    },
    queryEcology() {
      this.$API.supplierDashboard
        .queryEcology({
          // categoryId: this.value, 和产品确认，去除品类选择器
          categoryId: '',
          year: this.year
        })
        .then((res) => {
          if (!!res.data[0].purAmount || !!res.data[0].saleAmount) {
            this.showBox = false
            let b = { data: [] }
            b.data = [
              { value: res.data[0].purAmount, name: this.$t('采购金额') },
              { value: res.data[0].saleAmount, name: this.$t('销售金额') }
            ]
            this.$nextTick(() => {
              this.drawLine(b)
            })
            this.getData.purAmount = res.data[0].purAmount
            this.getData.saleAmount = res.data[0].saleAmount
            this.getData.sum = res.data[0].saleAmount + res.data[0].purAmount
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data)
          } else {
            this.showBox = true
          }
        })
    },

    // 展示按钮
    displayTreeView() {
      this.showTree = !this.showTree
      !isInitHideTree && this.showTree && this.initDownTree()
    },
    // 初始化点击周边 关闭tree 点击外部隐藏 内部没是
    clickInsideFun(e) {
      containsArr = document.getElementById('treeViewEc')
      containsSelect = document.getElementById('ecologicalAna')
      if (
        (!!containsArr && containsArr.contains(e.target)) ||
        (!!containsSelect && containsSelect.contains(e.target))
      ) {
        // console.log("在内");
      } else {
        this.showTree = false
      }
    },
    initDownTree() {
      isInitHideTree = true
      document.addEventListener('click', this.clickInsideFun)
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickInsideFun)
    containsArr = null
    containsSelect = null
  }
}
</script>

<style lang="scss" scoped>
/deep/.e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon {
  padding-top: 0 !important;
}
.PlatformActivity {
  width: 100%;
  padding: 20px 30px;
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .tspan {
    position: relative;
    height: 30px;
    display: flex;
  }
  .PlatformActivity-charts {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    .drowCircle-parent {
      width: 100%;
      position: relative;
      .tips-box {
        position: absolute;
        right: 0;
        top: 60px;
        .tips {
          cursor: pointer;
          margin-bottom: 28px;
          position: relative;
          .pop-tip {
            display: none;
            position: absolute;
            font-size: 12px;
            color: #fff;
            background-color: rgb(113, 113, 113);
            top: -20px;
            left: 0;
            z-index: 9;
            padding: 5px 10px;
            border-radius: 2px;
            text-align: center;
          }
          // .pop-tip::before{
          //   content:" ";
          //   position: absolute;
          //   left:-10px;
          //   top:18px;
          //   border-top:10px solid #6a6;
          //   border-left: 10px solid transparent;
          //   border-right:10px solid transparent;
          //   border-bottom:10px solid transparent;
          // }
          .num {
            width: 150px;
            height: 25px;
            font-size: 24px;
            font-family: DINAlternate;
            font-weight: bold;
            margin-bottom: 5px;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .word {
            width: 150px;
            height: 14px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .tips:hover {
          .pop-tip {
            display: block;
          }
        }
      }
      .text {
        position: absolute;
        top: 40%;
        left: 150px;
        font-size: 40px;
        color: #292929;
        text-align: center;
        width: 100px;
        span {
          display: block;
        }
        .span:nth-of-type(2) {
          font-size: 14px;
          margin-top: 10px;
        }
      }
    }
  }
}
</style>
