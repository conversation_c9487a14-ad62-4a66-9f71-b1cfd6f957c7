<template>
  <div class="ScreenMap">
    <div>
      <div style="margin: 20px; color: #292929; font-weight: 600">
        {{ $t('供应商分布') }}
      </div>
      <div class="choose-div">
        <div
          class="choose-un"
          :class="activeId == item.id ? 'choose-active' : ''"
          v-for="(item, i) in chooseArr"
          :key="i"
          @click="clickChoose(item)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <div :style="{ height: '260px', width: '80%', margin: '0 auto' }" ref="myEchart"></div>
    <div class="powerDiv">
      <span style="position: absolute; top: 0; left: 30px; color: #292929">{{ $t('少') }}</span>
      <span style="position: absolute; bottom: 0; left: 30px; color: #292929">{{ $t('多') }}</span>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import './china.js' // 引入中国地图数据
export default {
  props: {
    organizationId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null,
      activeId: -1,
      chooseArr: [
        { id: -1, text: this.$t('全部') },
        { id: 1, text: this.$t('经营性') },
        { id: 0, text: this.$t('非经营性') }
      ]
    }
  },
  created() {
    this.getRegisterMap()
  },
  mounted() {
    // this.chinaConfigure();
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    chinaConfigure(e) {
      console.log(this.userJson)
      let myChart = echarts.init(this.$refs.myEchart) //这里是为了获得容器所在位置
      window.onresize = myChart.resize
      myChart.setOption({
        // 进行相关配置
        backgroundColor: '#FFFFFF',
        tooltip: {}, // 鼠标移到图里面的浮动提示框
        dataRange: {
          show: false,
          min: 0,
          max: 1000,
          text: [this.$t('多'), this.$t('少')],
          realtime: true,
          calculable: true,
          color: ['#00469C', '#336BB0', '#85A7D0', '#C2D3E8']
        },
        geo: {
          // 这个是重点配置区
          map: 'china', // 表示中国地图
          roam: true,
          label: {
            normal: {
              show: false, // 是否显示对应地名
              textStyle: {
                color: 'rgba(0,0,0,0.4)'
              }
            }
          },
          itemStyle: {
            normal: {
              borderColor: 'rgba(0, 0, 0, 0.2)'
            },
            emphasis: {
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              shadowBlur: 20,
              borderWidth: 0,
              areaColor: null, // 地图高亮颜色
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        },
        series: [
          {
            type: 'scatter',
            coordinateSystem: 'geo' // 对应上方配置
          },
          {
            name: this.$t('供应商'), // 浮动框的标题
            type: 'map',
            geoIndex: 0,
            data: e
          }
        ]
      })
    },
    clickChoose(item) {
      this.activeId = item.id
      // this.$forceUpdate();
      this.getRegisterMap()
    },
    getRegisterMap() {
      this.$API.supplierDashboard
        .getRegisterMap({
          organizationId: this.organizationId,
          commercialType: this.activeId
        })
        .then((res) => {
          let a = []
          res.data.provinceRegisterDTOS.forEach((e) => {
            a.push({ name: e.itemName, value: e.itemCount })
          })
          this.chinaConfigure(a)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.ScreenMap {
  position: relative;
  height: 360px;
  width: 100%;
  .choose-div {
    margin: 20px;
    display: flex;
  }
  .choose-un {
    cursor: pointer;
    width: 60px;
    height: 20px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    font-size: 12px;
    text-align: center;
    line-height: 20px;
    box-sizing: content-box;
  }
  .choose-un:nth-of-type(1) {
    border-radius: 4px 0 0 4px;
    border-right: none;
  }
  .choose-un:nth-of-type(2) {
    border-radius: none;
  }
  .choose-un:nth-of-type(3) {
    border-radius: 0 4px 4px 0;
    border-left: none;
  }
  .choose-active {
    color: rgba(255, 255, 255, 1);
    background: rgba(0, 70, 156, 1);
  }
  .powerDiv {
    width: 20px;
    height: 98px;
    background: linear-gradient(180deg, rgba(232, 232, 232, 1) 0%, rgba(51, 107, 176, 1) 100%);
    position: absolute;
    bottom: 40px;
    left: 30px;
  }
}
</style>
