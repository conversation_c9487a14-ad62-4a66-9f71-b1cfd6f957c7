<template>
  <div class="ExpenditureAmount">
    <div class="title">
      <div>
        {{ $t('供应商分布') }}
        <span
          ><mt-icon class="transfer-icon" name="icon_card_transfer"></mt-icon
          >{{ $t('切换列表') }}</span
        >
      </div>
      <div class="choose-div">
        <div
          class="choose-un"
          :class="activeId == item.id ? 'choose-active' : ''"
          v-for="(item, i) in chooseArr"
          :key="i"
          @click="clickChoose(item)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <div class="ExpenditureAmount-charts">
      <div class="righttop" ref="charts"></div>
      <div class="drowCircle-parent">
        <div class="drowCircle" ref="drawLine"></div>
        <div class="text">
          <span class="span">{{ $t('供应商分布') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts' //   npm install echarts@4.9.0
export default {
  name: '',
  props: {
    organizationId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeId: -1,
      chooseArr: [
        { id: -1, text: this.$t('大陆') },
        { id: 1, text: this.$t('世界') }
      ]
    }
  },
  created() {
    // this.getStatistics();
  },
  mounted() {
    this.mycharts()
    // this.drawLine();
  },
  methods: {
    mycharts(e) {
      let myChart = echarts.init(this.$refs.charts, 'macarons')
      myChart.setOption({
        color: ['#8ACC40', '#00469C', '#6386C1'],
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}'
        },
        legend: {},
        series: [
          {
            name: this.$t('供应商'),
            type: 'funnel',
            left: '10%',
            x: '50%',
            top: 30,
            bottom: 40,
            width: '100%',
            min: 0,
            max: 100,
            minSize: '0%',
            maxSize: '100%',
            sort: 'ascending',
            gap: 2,
            label: {
              show: false,
              position: 'inside'
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid'
              }
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            emphasis: {
              label: {
                fontSize: 20
              }
            },
            data: e || [
              { value: 10, name: this.$t('战略供应商') },
              { value: 40, name: this.$t('优推供应商') },
              { value: 50, name: this.$t('合格供应商') }
            ]
          }
        ]
      })
    },
    drawLine(e) {
      let myChart = echarts.init(this.$refs.drawLine, 'macarons')
      let option = {
        tooltip: {
          trigger: 'item'
        },
        color: ['#00469C', '#ED5633', '#EDA133', '#8ACC40', '#6690C4', '#F49A85'],
        legend: {
          top: 'center',
          // left: "right",
          right: 50,
          orient: 'vertical',
          itemWidth: 10,
          itemHeight: 10,
          itemStyle: {},
          align: 'left'
        },
        series: [
          {
            name: this.$t('供应商'),
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: '25',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: e || [
              { value: 1048, name: this.$t('采矿') },
              { value: 735, name: this.$t('能源') },
              { value: 735, name: this.$t('电讯') },
              { value: 735, name: this.$t('服务业') },
              { value: 735, name: this.$t('物流') },
              { value: 735, name: this.$t('其他') }
            ]
          }
        ]
      }
      myChart.setOption(option)
    },
    clickChoose(item) {
      this.activeId = item.id
      // this.$forceUpdate();
      // this.getRegisterMap();
    },
    getStatistics() {
      this.$API.supplierDashboard
        .getStatistics({
          organizationId: this.organizationId
        })
        .then((res) => {
          let a = []
          let b = []
          res.data.serviceStatisticsDTOS.forEach((e) => {
            a.push({ value: e.itemCount, name: e.itemName })
          })
          this.drawLine(a)
          res.data.gradeStatisticsDTOS.forEach((e) => {
            b.push({ value: e.itemCount, name: e.itemName })
          })
          this.mycharts(b)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.ExpenditureAmount {
  .ExpenditureAmount-charts {
    display: flex;
    align-items: center;
    position: relative;
    .tips-box {
      position: absolute;
      left: 310px;
      top: 63px;
      .tips {
        margin-bottom: 28px;
        .num {
          width: 90px;
          height: 25px;
          font-size: 24px;
          font-family: DINAlternate;
          font-weight: bold;
          margin-bottom: 5px;
          text-align: center;
        }
        .word {
          width: 90px;
          height: 14px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          text-align: center;
        }
      }
    }
    .righttop {
      width: 660px;
      height: 300px;
      margin-right: 10px;
    }
    .drowCircle {
      width: 400px;
      height: 300px;
    }
    .drowCircle-parent {
      position: relative;
      .text {
        position: absolute;
        top: 42%;
        left: 115px;
        font-size: 25px;
        color: #292929;
        text-align: center;
        width: 97px;
        span {
          display: block;
        }
        .span:nth-of-type(2) {
          font-size: 14px;
          margin-top: 10px;
        }
      }
    }
  }
  .choose-div {
    // margin: 20px;
    display: flex;
  }
  .choose-un {
    cursor: pointer;
    width: 60px;
    height: 20px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    font-size: 12px;
    text-align: center;
    line-height: 20px;
    box-sizing: content-box;
  }
  .choose-un:nth-of-type(1) {
    border-radius: 4px 0 0 4px;
    border-right: none;
  }
  .choose-un:nth-of-type(2) {
    border-radius: 0 4px 4px 0;
    border-left: none;
  }
  .choose-active {
    color: rgba(255, 255, 255, 1);
    background: rgba(0, 70, 156, 1);
  }
  .title {
    margin: 16px 30px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: #292929;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }
}
</style>
