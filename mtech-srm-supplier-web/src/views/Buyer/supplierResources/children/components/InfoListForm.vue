<template>
  <div class="infoList">
    <div class="infoTop">
      <div>{{ infoSetting.titleName }}</div>
      <div style="display: flex">
        <div v-show="infoSetting.isShowPL">
          <div
            class="data-screen-select"
            :id="'infoListForms' + infoSetting.id"
            style="position: relative; margin-top: 8px; margin-left: 0px"
            @click="displayTreeView"
          >
            <span>{{ plId }}</span>
            <MtIcon
              name="icon_table_arrow"
              style="font-size: 12px; margin-left: auto; margin-right: 5px; line-height: 42px"
            />
          </div>
          <div v-show="showTree">
            <div
              slot="content"
              :id="'treeViewInfo2' + infoSetting.id"
              class="select-content"
              style="margin-top: 0px"
            >
              <mt-treeView
                :id="'infolist_' + new Date().getTime()"
                class="tree-view--template"
                ref="treeView"
                :fields="fields"
                :auto-check="true"
                :show-check-box="false"
                @nodeSelecting="nodeSelecting"
              ></mt-treeView>
            </div>
          </div>
        </div>
        <mt-select
          v-show="infoSetting.isShowXM"
          css-class="selectClass"
          :show-clear-button="true"
          :width="100"
          :data-source="infoSetting.dataArrXM"
          :fields="{ text: 'stageName', value: 'code' }"
          :placeholder="$t('项目分类')"
          @change="changeVal"
        ></mt-select>
        <span style="margin-left: 10px" v-show="infoSetting.isShowTime">
          <mt-date-picker
            :id="'infodate_' + new Date().getTime()"
            css-class="selectClass"
            v-model="val"
            :width="100"
            :open-on-focus="true"
            :show-clear-button="false"
            start="Decade"
            depth="Decade"
            format="yyyy"
            :allow-edit="false"
            :placeholder="$t('选择日期')"
            @change="changeDate"
          ></mt-date-picker>
        </span>
      </div>
    </div>
    <div class="table-content" v-if="!showBox">
      <div v-for="(item, i) in infoSetting.mockList" :key="i" class="list">
        <span class="listName">
          <span class="list-x" v-if="i > 2">{{ i + 1 }}</span>
          <span class="list-x" v-else>
            <img v-if="i == 0" src="../image/first.svg" alt="" />
            <img v-if="i == 1" src="../image/second.svg" alt="" />
            <img v-if="i == 2" src="../image/third.svg" alt="" />
          </span>
          <span class="pop-tip" v-show="!!item.itemName">{{ item.itemName }}</span>
          <span
            :class="
              infoSetting.isShowPercent &&
              infoSetting.isShowPLName &&
              item.firstCategoryName !== null
                ? 'itemName126'
                : 'itemName230'
            "
            >{{ item.itemName }}</span
          >
        </span>
        <span class="spend" v-show="infoSetting.isShowPercent">{{ item.amountSingleRate }}%</span>
        <span class="cumul-prop" v-show="infoSetting.isShowPercent">{{ item.amountSumRate }}%</span>
        <span class="listName">
          <span class="pop-tip" v-show="!!item.firstCategoryName || !!item.categoryName">{{
            !!item.firstCategoryName ? item.firstCategoryName : item.categoryName
          }}</span>
          <span class="categrayName" v-show="infoSetting.isShowPLName">{{
            !!item.firstCategoryName ? item.firstCategoryName : item.categoryName
          }}</span>
        </span>
        <span v-show="infoSetting.isShowScore" style="color: #00469c">{{ item.itemScore }}</span>
      </div>
    </div>
    <div style="height: calc(100% - 40px)" v-else>
      <emptyBox></emptyBox>
    </div>
  </div>
</template>
<script>
import emptyBox from '@/components/emptyData/emptyData.vue'

let isInitHideTree = false
let containsArr = []
let containsSelect = []
export default {
  components: { emptyBox },
  props: {
    infoSetting: {
      type: Object,
      default: () => {
        return {
          titleName: '', // 标题
          mockList: [], // 查询的内容
          isShowScore: true, // 是否显示右侧的数字
          isShowPL: true, // 是否显示品类的下拉选择框
          isShowPercent: true, // 是否占比
          isShowPLName: true,
          isShowXM: true, // 是否显示时间选择框
          isShowTime: true, // 是否显示时间选择框
          dataArrPL: [], // 品类接口返回的数据 （因为好多处用这个接口所以写到外面了）
          dataArrXM: [] // 项目分类数据
        }
      }
    }
  },
  watch: {
    infoSetting: {
      handler() {
        if (this.infoSetting.mockList !== null && this.infoSetting.mockList.length > 0) {
          this.showBox = false
        } else {
          this.showBox = true
        }
        if (this.infoSetting.dataArrPL && this.infoSetting.dataArrPL.length > 0) {
          const ref = this.$refs.treeView
          if (ref.ejsInstances.groupedData.length > 0) {
            return
          }
          ref.ejsInstances.addNodes(this.infoSetting.dataArrPL)
          this.plId = this.infoSetting.dataArrPL[0].categoryName
        }
        if (this.infoSetting.type == 'Supplier') {
          this.type = true
        } else {
          this.type = false
        }
      },
      deep: true
    }
  },
  data() {
    return {
      type: false,
      showTree: false,
      showBox: false,
      plId: '',
      val: new Date().getFullYear(),
      fields: {
        dataSource: [],
        id: 'id',
        text: 'categoryName',
        parentID: 'parentId'
      }
    }
  },
  // computed: {
  //   'fields.dataSource': function (a) {
  //
  //   }
  // },
  mounted() {
    this.initDownTree()
    // this.getProdtreeInClient();
  },
  methods: {
    // 展示按钮
    displayTreeView() {
      console.log(666)
      this.showTree = !this.showTree
      !isInitHideTree && this.showTree && this.initDownTree()
    },
    // 初始化点击周边 关闭tree 点击外部隐藏 内部没是
    clickInsideFun(e) {
      containsArr = document.getElementById(`treeViewInfo2${this.infoSetting.id}`)
      containsSelect = document.getElementById(`infoListForms${this.infoSetting.id}`)
      if (
        (!!containsArr && containsArr.contains(e.target)) ||
        (!!containsSelect && containsSelect.contains(e.target))
      ) {
        // console.log("在内");
      } else {
        this.showTree = false
      }
    },
    initDownTree() {
      isInitHideTree = true
      document.addEventListener('click', this.clickInsideFun)
    },
    getProdtreeInClient(item = 0) {
      this.$API.supplierInvitationAdd
        .getProdtreeInClient({ id: item })
        .then((res) => {
          if (res.data.length > 0) {
            const ref = this.$refs.treeView
            ref.ejsInstances.addNodes(res.data)
            if (res.data[0].parentId == '0') {
              this.plId = res.data[0].categoryName
            }
          } else {
            this.displayTreeView()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg ? err.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    nodeSelecting(e) {
      this.plId = e.nodeData.text
      this.value = e.nodeData.text == this.$t('不限品类') ? '' : e.nodeData.id
      if (this.value || this.value == '') {
        this.getProdtreeInClient(e.nodeData.id) //查询品类
        this.selectData(this.value) //查询展示数据
      }
    },

    selectData(val) {
      let value = val
      // let value = val.value[0] || 0;
      if (value || value == '') {
        this.$emit('getDwonTreeVal', value)
      }
    },
    changeDate(val) {
      this.$emit('getDateVal', val)
    },
    changeVal(val) {
      this.$emit('getSelectVal', val)
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickInsideFun)
    containsArr = null
    containsSelect = null
  }
}
</script>
<style lang="scss">
.infoList {
  height: 100%;
  .infoTop {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    background: rgba(155, 170, 193, 1);
    font-size: 14px;
    padding: 0 20px;
    line-height: 40px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(255, 255, 255, 1);
    border-radius: 8px 8px 0 0;
    .select-content {
      .mt-tree-view {
        margin-left: 0px !important;
      }
    }
    .selectClass {
      height: 22px;
      line-height: 22px;
      font-size: 12px;
      color: #292929;
      background: white;
      border: none;
      border-radius: 4px;
      .e-control {
        min-height: 16px !important;
      }
      .e-input-group-icon {
        margin: 0 !important;
        border-radius: 0 8px 8px 0 !important;
      }
      .e-clear-icon {
        padding: 0 !important;
        margin: 0 !important;
      }
    }
  }
  .table-content {
    width: 100%;
    height: 280px;
    overflow-y: scroll;
    background-color: white;
    border-top: 1px solid transparent;
    margin-top: 5px;
    .list {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
    }
    .list-x {
      width: 13px;
      display: inline-block;
      text-align: center;
      margin-right: 9px;
      img {
        position: relative;
        top: 2px;
      }
    }
    .listName {
      position: relative;
      cursor: pointer;
      .pop-tip {
        display: none;
        position: absolute;
        top: -15px;
        left: 0;
        z-index: 9;
        padding: 5px 10px;
        color: #fff;
        background-color: rgb(113, 113, 113);
        border-radius: 2px;
        text-align: center;
        font-size: 12px;
      }
    }
    .listName:hover {
      .pop-tip {
        display: block;
      }
    }

    .itemName230 {
      display: inline-block;
      width: 230px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .itemName126 {
      display: inline-block;
      width: 126px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .categrayName {
      display: inline-block;
      width: 100px;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      position: relative;
    }
    .spend,
    .cumul-prop {
      display: inline-block;
      width: 40px;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
