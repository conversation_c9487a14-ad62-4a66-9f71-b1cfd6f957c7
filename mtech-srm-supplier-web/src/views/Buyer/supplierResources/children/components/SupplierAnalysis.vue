<template>
  <div class="SupplierAnalysis">
    <div class="title">
      <div>
        {{ $t('供应商分布') }}
        <span class="change-list" @click="isShowChart = !isShowChart"
          ><mt-icon class="transfer-icon" name="icon_card_transfer"></mt-icon
          >{{ $t('切换列表') }}</span
        >
      </div>
      <div class="choose-div">
        <div
          class="choose-un"
          :class="activeId == item.id ? 'choose-active' : ''"
          v-for="(item, i) in chooseArr"
          :key="i"
          @click="clickChoose(item)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <div
      class="data-screen-select mb10"
      id="supplierAnalysis"
      :style="!showBox ? '' : 'position:relative'"
      @click="displayTreeView"
    >
      <span>{{ plId }}</span>
      <MtIcon
        name="icon_table_arrow"
        style="font-size: 12px; margin-left: auto; margin-right: 5px"
      />
    </div>
    <div v-show="showTree">
      <div slot="content" class="select-content" id="treeViewAn">
        <mt-treeView
          :id="'SupplierAnalysis_' + new Date().getTime()"
          class="tree-view--template"
          ref="treeViewAn"
          :fields="fields"
          :auto-check="true"
          :show-check-box="false"
          @nodeSelecting="nodeSelecting"
        ></mt-treeView>
      </div>
    </div>
    <div v-if="!showBox">
      <div v-show="isShowChart" class="SupplierAnalysis-charts">
        <div class="drowCircle-parent">
          <div
            class="drowCircle"
            ref="drawLine"
            :style="{ height: '215px', width: '100%', margin: '0 auto' }"
          ></div>
          <div class="text">
            <span class="span">{{ $t('供应商分布') }}</span>
          </div>
        </div>
      </div>
      <div v-show="!isShowChart" class="data-screen-chart-span tableClass mh2">
        <mt-template-page
          ref="templateRef"
          :hidden-tabs="true"
          :template-config="pageConfig"
        ></mt-template-page>
      </div>
    </div>
    <div v-else class="mh2">
      <emptyBox></emptyBox>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts' //   npm install echarts@4.9.0
import { supplierDistribution } from '../../config'
import MtTreeView from '@mtech-ui/tree-view'
import emptyBox from '@/components/emptyData/emptyData.vue'

let isInitHideTree = false
let containsArr = []
let containsSelect = []
let selectedProvinces = [] // 供应商关系 已选得省份
let fullDataSource = [] // 全部数据得备份
export default {
  components: {
    'mt-treeView': MtTreeView,
    emptyBox
  },
  props: {
    organizationId: {
      type: Object,
      default: () => {}
    },
    infoSetting: {
      type: Object,
      default: () => {
        return {
          isShowScore: true, // 是否显示右侧的数字
          dataArrPL: [] // 品类接口返回的数据 （因为好多处用这个接口所以写到外面了）
        }
      }
    }
  },
  name: '',
  data() {
    return {
      showTree: false,
      showBox: false,
      plId: '',
      isSupplier: false,
      isShowChart: true,
      activeId: 0,
      chooseArr: [
        { id: 0, text: this.$t('大陆') },
        { id: 1, text: this.$t('全球') }
      ],
      value: '',
      fields: {
        dataSource: [],
        id: 'id',
        text: 'categoryName',
        parentID: 'parentId'
      },
      pageConfig: [
        {
          gridId: 'b0ae34ee-189f-46f9-97ed-e999afe80a82',
          title: this.$t('审批流配置'),
          grid: {
            allowReordering: false,
            allowResizing: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: supplierDistribution,
            allowPaging: false,
            dataSource: []
            // asyncConfig: {
            //   methods: "get",
            //   params: {
            //     categoryId: '',
            //     areaType: 0,
            //   },
            //   url: "/analysis/tenant/cockpit/queryDistribution",
            //   recordsPosition: "data",
            // },
          }
        }
      ]
    }
  },
  created() {
    this.queryDistribution()
    // this.getProdtreeInClient();
  },
  mounted() {
    this.initDownTree()
    // echarts容器的宽度根据父元素宽度变化进行自适应
    this.$nextTick(() => {
      const resizeob = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (echarts.getInstanceByDom(entry.target)) {
            echarts.getInstanceByDom(entry.target).resize()
          }
        }
      })
      resizeob.observe(this.$refs.drawLine)
    })
  },
  watch: {
    isShowChart(n) {
      if (!n) {
        let dataSource = JSON.parse(JSON.stringify(fullDataSource))
        let filterResult = []
        dataSource.forEach((item) => {
          if (
            selectedProvinces.find((cItem) => {
              let proviceItem = !!cItem && cItem.length > 5 ? cItem.substr(0, 5) : cItem
              return (
                (!item.area && item.area === proviceItem) ||
                (!!item.area && item.area.indexOf(proviceItem) >= 0)
              )
            })
          ) {
            filterResult.push(item)
          }
        })
        this.$set(this.pageConfig[0].grid, 'dataSource', filterResult)
      } else {
        // 切换列表后移动窗口 图表都挤在了一起
        this.$nextTick(() => {
          let myevent = new Event('resize')
          window.dispatchEvent(myevent)
        })
      }
    },
    infoSetting: {
      handler() {
        // 控制图表高度
        if (this.infoSetting.type == 'Supplier') {
          this.isSupplier = true
        } else if (this.infoSetting.type == 'Platform') {
          this.isSupplier = false
        }
        // 获取品类数据
        if (this.infoSetting.dataArrPL.length > 0) {
          const ref = this.$refs.treeViewAn
          if (ref.ejsInstances.groupedData.length > 0) {
            return
          }
          ref.ejsInstances.addNodes(this.infoSetting.dataArrPL)
          this.plId = this.infoSetting.dataArrPL[0].categoryName
        }
      },
      deep: true
    }
  },
  methods: {
    //品类分类
    getProdtreeInClient(item = 0) {
      this.$API.supplierInvitationAdd
        .getProdtreeInClient({ id: item })
        .then((res) => {
          if (res.data.length > 0) {
            const ref = this.$refs.treeViewAn
            ref.ejsInstances.addNodes(res.data)
            if (res.data[0].parentId == '0') {
              this.plId = res.data[0].categoryName
            }
          } else {
            this.displayTreeView()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg ? err.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    nodeSelecting(e) {
      this.plId = e.nodeData.text
      this.value = e.nodeData.text == this.$t('不限品类') ? '' : e.nodeData.id
      if (this.value || this.value == '') {
        this.getProdtreeInClient(e.nodeData.id)
        this.queryDistribution()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    drawLine(e) {
      let myChart = echarts.init(this.$refs.drawLine, 'macarons')
      let option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a}<br />{b}：{d}%',
          position: ['0%', '40%']
        },
        color: [
          '#00469C',
          '#ED5633',
          '#EDA133',
          '#8ACC40',
          '#6690C4',
          '#F49A85',
          '#f00606',
          '#a241b3',
          '#31b578',
          '#CCCCCC'
        ],
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 0,
          top: 0,
          bottom: 0,
          itemWidth: 10,
          itemHeight: 10,
          itemStyle: {
            borderRadius: '100%'
          },
          textStyle: {
            width: 100,
            overflow: 'break'
          }
          // align: "left",
        },
        series: [
          {
            name: this.$t('供应商'),
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['28%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: e
          }
        ]
      }
      myChart.setOption(option, true)

      // legend 点击事件
      myChart.on('legendselectchanged', function (params) {
        // console.log(params)
        if (params.selected) {
          selectedProvinces = []
          for (let item in params.selected) {
            if (params.selected[item]) {
              selectedProvinces.push(item)
            }
          }

          // 再次将legend的 selected 设为 false（置灰）
          // myChart.setOption({
          //     legend:{selected:{[params.name]: false}}
          // })
        }
      })
      //让图表自适应+防抖
      window.addEventListener('resize', resizeDebounce(resizeFn, 200))
      function resizeFn() {
        myChart.resize()
      }
      function resizeDebounce(fn, delay) {
        let timer = null
        return function () {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(fn, delay)
        }
      }
    },
    change(e) {
      console.log(e)
      this.value = e.id
      if (this.value) {
        this.queryDistribution()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    clickChoose(item) {
      this.activeId = item.id
      // this.$forceUpdate();
      this.queryDistribution()
      // this.pageConfig[0].grid.asyncConfig.params.areaType = this.activeId;
    },
    queryDistribution() {
      this.$API.supplierDashboard
        .queryDistribution({
          categoryId: this.value,
          areaType: this.activeId
        })
        .then((res) => {
          let a = []
          res.data = [{ registerAddressProvinceName: this.$t('北京'), percent: 100 }]
          if (res.data && res.data.length > 0) {
            //后台会返回null，所以加判断
            this.showBox = false
            if (this.activeId == 0) {
              //0 大陆 1 全球
              res.data.forEach((item) => {
                item.area = item.registerAddressProvinceName
              })
            } else if (this.activeId == 1) {
              res.data.forEach((item) => {
                item.area = item.registerAddressCountryName
              })
            }
            res.data.forEach((e) => {
              let percent = e.percent * 100
              a.push({ value: percent, name: e.area })
              selectedProvinces.push(e.area)
            })
            this.$nextTick(() => {
              this.drawLine(a)
            })
            fullDataSource = res.data // 全部数据得备份
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data)
          } else {
            this.showBox = true
          }
        })
    },
    // 展示按钮
    displayTreeView() {
      this.showTree = !this.showTree
      !isInitHideTree && this.showTree && this.initDownTree()
    },
    // 初始化点击周边 关闭tree 点击外部隐藏 内部没是
    clickInsideFun(e) {
      containsArr = document.getElementById('treeViewAn')
      containsSelect = document.getElementById('supplierAnalysis')
      if (
        (!!containsArr && containsArr.contains(e.target)) ||
        (!!containsSelect && containsSelect.contains(e.target))
      ) {
        // console.log("在内");
      } else {
        this.showTree = false
      }
    },
    initDownTree() {
      isInitHideTree = true
      document.addEventListener('click', this.clickInsideFun)
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickInsideFun)
    containsArr = null
    containsSelect = null
  }
}
</script>

<style lang="scss" scoped>
/deep/.e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon {
  padding-top: 0 !important;
}

.mb10 {
  margin-bottom: 10px;
}

.SupplierAnalysis {
  width: 100%;
  padding: 20px 30px;

  .tableClass {
    margin-top: 10px;
    max-height: 345px;
    overflow-y: scroll;
  }
  .mh2 {
    height: 180px !important;
  }
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .SupplierAnalysis-charts {
    display: flex;
    align-items: center;
    // position: relative;
    // top: 20px;

    .tips-box {
      position: absolute;
      left: 310px;
      top: 63px;
      .tips {
        margin-bottom: 28px;
        .num {
          width: 90px;
          height: 25px;
          font-size: 24px;
          font-family: DINAlternate;
          font-weight: bold;
          margin-bottom: 5px;
          text-align: center;
        }
        .word {
          width: 90px;
          height: 14px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          text-align: center;
        }
      }
    }
    .righttop {
      width: 660px;
      height: 360px;
      margin-right: 10px;
    }
    .drowCircle-parent {
      width: 100%;
      position: relative;
      .text {
        position: absolute;
        top: 0;
        left: 0;
        color: #292929;
        text-align: center;
        width: 55%;
        height: 215px;
        font-size: 14px;
        font-weight: bold;

        span {
          display: block;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 50px;
        }
        .span:nth-of-type(2) {
          font-size: 14px;
          margin-top: 10px;
        }
      }
    }
  }
  .choose-div {
    // margin: 20px;
    display: flex;
  }
  .choose-un {
    cursor: pointer;
    width: 60px;
    height: 20px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    font-size: 12px;
    text-align: center;
    line-height: 20px;
    box-sizing: content-box;
  }
  .choose-un:nth-of-type(1) {
    border-radius: 4px 0 0 4px;
    border-right: none;
  }
  .choose-un:nth-of-type(2) {
    border-radius: 0 4px 4px 0;
    border-left: none;
  }
  .choose-active {
    color: rgba(255, 255, 255, 1);
    background: rgba(0, 70, 156, 1);
  }
  .title {
    margin-bottom: 10px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: #292929;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }
}
/deep/ .tree-view-container {
  box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
  width: 100%;
  position: absolute;
  left: 0;
  top: 50px;
  z-index: 2;
  background: #fff;
  height: 200px;
  overflow: auto;
}
</style>
<style lang="scss">
.SupplierAnalysis {
  .select-content .mt-tree-view {
    margin-left: 0;
  }
}
</style>
