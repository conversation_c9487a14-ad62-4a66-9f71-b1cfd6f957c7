<template>
  <div class="RightList">
    <div class="title">{{ $t('绩效') }}Top10</div>
    <div v-for="(item, i) in mockList" :key="i" class="list">
      <span
        ><span class="list-x" v-if="i > 2">{{ i + 1 }}</span
        ><span class="list-x" v-else>
          <img v-if="i == 0" src="../image/first.svg" alt="" />
          <img v-if="i == 1" src="../image/second.svg" alt="" />
          <img v-if="i == 2" src="../image/third.svg" alt="" />
        </span>
        {{ item.itemName }}</span
      >
      <span style="color: #00469c">{{ item.itemScore }}</span>
    </div>
    <div class="title">{{ $t('中标金额') }}Top10</div>
    <div v-for="(item, i) in mockList1" :key="i + 20" class="list">
      <span
        ><span class="list-x" v-if="i > 2">{{ i + 1 }}</span
        ><span class="list-x" v-else>
          <img v-if="i == 0" src="../image/first.svg" alt="" />
          <img v-if="i == 1" src="../image/second.svg" alt="" />
          <img v-if="i == 2" src="../image/third.svg" alt="" />
        </span>
        {{ item.supplierEnterpriseName }}</span
      >
      <span style="color: #00469c">￥{{ item.biddingTotalPrice }}</span>
    </div>
    <div class="title">{{ $t('中标项目个数') }}Top10</div>
    <div v-for="(item, i) in mockList2" :key="i + 40" class="list">
      <span
        ><span class="list-x" v-if="i > 2">{{ i + 1 }}</span
        ><span class="list-x" v-else>
          <img v-if="i == 0" src="../image/first.svg" alt="" />
          <img v-if="i == 1" src="../image/second.svg" alt="" />
          <img v-if="i == 2" src="../image/third.svg" alt="" />
        </span>
        {{ item.supplierEnterpriseName }}</span
      >
      <span style="color: #00469c">{{ item.biddingTotalPrice }}</span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    organizationId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      mockList: [],
      mockList1: [],
      mockList2: []
    }
  },
  created() {
    this.getPerformance()
    this.getBidding()
  },
  methods: {
    getPerformance() {
      this.$API.supplierDashboard
        .getPerformance({
          organizationId: this.organizationId
        })
        .then((res) => {
          this.mockList = res.data.increaseStatisticsDTOS
        })
    },
    getBidding() {
      this.$API.supplierDashboard
        .getBidding({
          organizationId: this.organizationId
        })
        .then((res) => {
          this.mockList1 = res.data.biddingAmountStatistics
          this.mockList2 = res.data.biddingQuantityStatistics
        })
    }
  }
}
</script>
<style lang="scss">
.RightList {
  .title {
    width: 360px;
    height: 40px;
    background: rgba(155, 170, 193, 1);
    font-size: 14px;
    padding: 0 20px;
    line-height: 40px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(255, 255, 255, 1);
  }
  .list-x {
    width: 13px;
    display: inline-block;
    text-align: center;
    img {
      position: relative;
      top: 2px;
    }
  }
  .title:nth-of-type(1) {
    border-radius: 4px 4px 0 0;
  }
  .list {
    font-size: 13px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin: 27px 20px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
