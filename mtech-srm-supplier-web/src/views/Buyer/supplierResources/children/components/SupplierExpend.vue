<template>
  <div class="SupplierIncrease">
    <div class="data-screen-title">
      {{ $t('支出金额变化情况') }}
      <span class="change-list" @click="isShow = !isShow"
        ><mt-icon class="transfer-icon" name="icon_card_transfer"></mt-icon
        >{{ $t('切换列表') }}</span
      >
    </div>
    <div class="data-screen-title">
      <div
        class="data-screen-select"
        style="position: relative"
        id="supplierExpend"
        @click="displayTreeView"
      >
        <span>{{ plId }}</span>
        <MtIcon
          name="icon_table_arrow"
          style="font-size: 12px; margin-left: auto; margin-right: 5px"
        />
      </div>
      <div v-show="showTree">
        <div slot="content" id="treeViewse" class="select-content" style="margin-top: 0px">
          <mt-treeView
            class="tree-view--template"
            ref="treeView"
            :fields="fields"
            :auto-check="true"
            :show-check-box="false"
            @nodeSelecting="nodeSelecting"
          ></mt-treeView>
        </div>
      </div>
      <div class="choose-div">
        <div
          class="choose-un"
          :class="activeId === item.id ? 'choose-active' : ''"
          v-for="(item, i) in chooseArr"
          :key="i"
          @click="clickChoose(item)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <div v-if="!showBox" class="mh225">
      <span
        v-show="!isShow"
        class="data-screen-chart-span"
        style="height: 235px; overflow-y: scroll"
      >
        <mt-template-page
          style="margin-top: 10px"
          ref="templateRef"
          :hidden-tabs="true"
          :template-config="pageConfig"
        ></mt-template-page
      ></span>
      <span v-show="isShow">
        <div class="SupplierIncrease-s">
          <!-- <div ref="myEchart"></div> -->
          <div :style="{ height: '225px', width: '90%', margin: '0 auto' }" ref="myEchart2"></div>
        </div>
      </span>
    </div>
    <div v-else style="height: 210px">
      <emptyBox></emptyBox>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { supplierExpendColumn } from '../../config'
import bus from '@/utils/bus'
import emptyBox from '@/components/emptyData/emptyData.vue'

let isInitHideTree = false
let containsArr = []
let containsSelect = []
export default {
  components: { emptyBox },
  props: {
    organizationId: {
      type: Object,
      default: () => {}
    },
    infoSetting: {
      type: Object,
      default: () => {
        return {
          isShowScore: true, // 是否显示右侧的数字
          dataArrPL: [] // 品类接口返回的数据 （因为好多处用这个接口所以写到外面了）
        }
      }
    }
  },
  data() {
    return {
      type: true,
      showTree: false,
      showBox: false,
      plId: '',
      chart: null,
      activeId: 1,
      value: '',
      isShow: true,
      fields: {
        dataSource: [],
        id: 'id',
        text: 'categoryName',
        parentID: 'parentId'
      },
      chooseArr: [
        { id: 1, text: this.$t('月') },
        { id: 2, text: this.$t('季') },
        { id: 3, text: this.$t('年') }
      ],
      pageConfig: [
        {
          gridId: 'b737a5ba-ca77-4059-9056-d7ab265d8243',
          title: this.$t('审批流配置'),
          grid: {
            allowReordering: false,
            allowResizing: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: supplierExpendColumn,
            allowPaging: false,
            dataSource: []
            // asyncConfig: {
            //   params: {
            //     categoryId: '',
            //     type: 1,
            //     purOrgId:'1431073794369822721',
            //     supplierProvinceCode:''
            //   },
            //   url: "/analysis/tenant/cockpit/purchaseAmount/trend",
            //   recordsPosition: "data",
            // },
          }
        }
      ],
      orgId: '1431073794369822721',
      areaCode: ''
    }
  },
  watch: {
    infoSetting: {
      handler() {
        if (this.infoSetting.dataArrPL.length > 0) {
          const ref = this.$refs.treeView
          if (ref.ejsInstances.groupedData.length > 0) {
            return
          }
          ref.ejsInstances.addNodes(this.infoSetting.dataArrPL)
          this.plId = this.infoSetting.dataArrPL[0].categoryName
        }
        if (this.infoSetting.type == 'Supplier') {
          this.type = true
        } else {
          this.type = false
        }
      },
      deep: true
    },
    isShow(n) {
      if (n) {
        this.$nextTick(() => {
          // 切换列表后移动窗口 图表都挤在了一起
          let myevent = new Event('resize')
          window.dispatchEvent(myevent)
        })
      }
    }
  },
  created() {
    let id = sessionStorage.getItem('cockUserId')
    this.orgId = id
    // this.pageConfig[0].grid.asyncConfig.params.purOrgId = id;
    this.queryExpend()
    // this.getProdtreeInClient()
  },
  mounted() {
    bus.$on('companyChange', (a) => {
      if (this.orgId !== a) {
        this.orgId = a
        this.queryExpend()
        // this.pageConfig[0].grid.asyncConfig.params.purOrgId = a;
      }
    })
    bus.$on('areaChange', (a) => {
      if (this.areaCode !== a) {
        this.areaCode = a
        this.queryExpend()
        // this.pageConfig[0].grid.asyncConfig.params.supplierProvinceCode = a;
      }
    })
    this.initDownTree()
    this.$nextTick(() => {
      if (echarts) {
        // echarts容器的宽度根据父元素宽度变化进行自适应
        const resizeob = new ResizeObserver((entries) => {
          for (const entry of entries) {
            if (echarts.getInstanceByDom(entry.target)) {
              echarts.getInstanceByDom(entry.target).resize()
            }
          }
        })
        resizeob.observe(this.$refs.myEchart2)
      }
    })

    // this.$set(this.fields, "dataSource", this.infoSetting.dataArrPL);
  },
  // beforeDestroy() {
  //   if (!this.chart) {
  //     return
  //   }
  //   this.chart.dispose()
  //   this.chart = null
  // },
  methods: {
    // 展示按钮
    displayTreeView() {
      this.showTree = !this.showTree
      !isInitHideTree && this.showTree && this.initDownTree()
    },
    // 初始化点击周边 关闭tree 点击外部隐藏 内部没是
    clickInsideFun(e) {
      containsArr = document.getElementById('treeViewse')
      containsSelect = document.getElementById('supplierExpend')
      if (
        (!!containsArr && containsArr.contains(e.target)) ||
        (!!containsSelect && containsSelect.contains(e.target))
      ) {
        // console.log("在内");
      } else {
        this.showTree = false
      }
    },
    initDownTree() {
      isInitHideTree = true
      document.addEventListener('click', this.clickInsideFun)
    },
    getProdtreeInClient(item = 0) {
      this.$API.supplierInvitationAdd
        .getProdtreeInClient({ id: item })
        .then((res) => {
          if (res.data.length > 0) {
            const ref = this.$refs.treeView
            ref.ejsInstances.addNodes(res.data)
            if (res.data[0].parentId == '0') {
              this.plId = res.data[0].categoryName
            }
          } else {
            this.displayTreeView()
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg ? err.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    nodeSelecting(e) {
      this.plId = e.nodeData.text
      this.value = e.nodeData.text == this.$t('不限品类') ? '' : e.nodeData.id
      if (this.value || this.value == '') {
        this.getProdtreeInClient(e.nodeData.id)
        this.queryExpend()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    muCharts2(e) {
      let myChart = echarts.init(this.$refs.myEchart2) //这里是为了获得容器所在位置
      myChart.setOption(
        {
          color: ['rgba(0,70,156,0.5)'],
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: e.xData,
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value'
          },
          grid: {
            containLabel: true,
            left: 0,
            bottom: 0
          },
          series: [
            {
              data: e.series || [820, 932, 901, 934, 1290, 1330, 1320],
              type: 'line',
              areaStyle: {}
            }
          ]
        },
        true
      )

      //让图表自适应+防抖
      window.addEventListener('resize', resizeDebounce(resizeFn, 200))
      function resizeFn() {
        myChart.resize()
      }
      function resizeDebounce(fn, delay) {
        let timer = null
        return function () {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(fn, delay)
        }
      }
    },
    change(e) {
      this.value = e.id
      if (this.value) {
        this.queryExpend()
        // this.pageConfig[0].grid.asyncConfig.params.categoryId = this.value;
      }
    },
    clickChoose(item) {
      this.activeId = item.id
      this.queryExpend()
      // this.pageConfig[0].grid.asyncConfig.params.type = item.id;
    },
    queryExpend() {
      this.$API.supplierDashboard
        .queryExpend({
          categoryId: this.value,
          type: this.activeId,
          purOrgId: this.orgId,
          supplierProvinceCode: this.areaCode
        })
        .then((res) => {
          if (res.data && res.data.length > 0) {
            this.showBox = false
            let data = { xData: [], series: [] }
            res.data.forEach((e) => {
              data.xData.push(e.date)
              data.series.push(e.purchaseAmount)
            })
            this.$nextTick(() => {
              this.muCharts2(data)
            })
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data)
          } else {
            this.showBox = true
          }
        })
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickInsideFun)
    containsArr = null
    containsSelect = null
  }
}
</script>
<style lang="scss" scoped>
/deep/.e-input-group .e-clear-icon,
.e-input-group.e-control-wrapper .e-clear-icon {
  padding-top: 0 !important;
}
.SupplierIncrease {
  padding: 20px 30px;
  position: relative;
  /deep/ .e-input-group.e-control-wrapper:not(.e-float-icon-left) {
    border: none !important;
    margin: 0 !important;
  }
  .SupplierIncrease-s {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .bottom-title {
    width: 70px;
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
  }
  .choose-un {
    width: 60px;
    height: 20px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    font-size: 12px;
    text-align: center;
    line-height: 20px;
    box-sizing: content-box;
  }
  .choose-un:nth-of-type(1) {
    border-radius: 4px 0 0 4px;
    border-right: none;
  }
  .choose-un:nth-of-type(2) {
    border-radius: none;
  }
  .choose-un:nth-of-type(3) {
    border-radius: 0 4px 4px 0;
    border-left: none;
  }
  .choose-active {
    color: rgba(255, 255, 255, 1);
    background: rgba(0, 70, 156, 1);
  }
  .powerDiv {
    width: 20px;
    height: 98px;
    background: linear-gradient(180deg, rgba(232, 232, 232, 1) 0%, rgba(51, 107, 176, 1) 100%);
    position: absolute;
    bottom: 40px;
    left: 30px;
  }
}
</style>
