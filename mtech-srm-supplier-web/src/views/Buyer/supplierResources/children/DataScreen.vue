<template>
  <div class="DataScreen-div" ref="DataScreen">
    <div class="top">
      <div class="top-1">
        <SupplierAnalysis
          :organization-id="organizationId"
          :info-setting="jxInfoSetting"
        ></SupplierAnalysis>
      </div>
      <div class="top-2-1">
        <RightTitle :organization-id="organizationId"></RightTitle>
      </div>
    </div>
    <div class="top-2">
      <CategorySuppliers></CategorySuppliers>
    </div>
    <div class="top-2">
      <ResourceAnalysis></ResourceAnalysis>
    </div>
    <div :class="classState ? 'middle3' : 'middle'">
      <div class="middle-1">
        <!-- 数量变化 -->
        <QuantityChange :info-setting="jxInfoSetting"></QuantityChange>
      </div>
      <div class="middle-1">
        <SupplierLevel :info-setting="jxInfoSetting"></SupplierLevel>
      </div>
      <div class="middle-1">
        <!-- 中标金额情况 -->
        <SupplierIncrease :info-setting="jxInfoSetting"></SupplierIncrease>
      </div>
      <div class="middle-1">
        <PlatformActivity></PlatformActivity>
      </div>
      <div class="middle-1">
        <EcologicalAnalysis :info-setting="jxInfoSetting"></EcologicalAnalysis>
      </div>
      <div class="middle-1">
        <ListTable></ListTable>
      </div>
      <div class="middle-1">
        <InfoListForm :info-setting="jxInfoSetting" @getDwonTreeVal="getDwonTreeVal"></InfoListForm>
      </div>
      <div class="middle-1">
        <InfoListForm
          :info-setting="zcInfoSetting"
          @getSelectVal="getSelectVal"
          @getDateVal="getDateVal"
        ></InfoListForm>
      </div>
      <div class="middle-1">
        <InfoListForm :info-setting="hzInfoSetting" @getDateVal="getDateValHZ"></InfoListForm>
      </div>
    </div>
  </div>
</template>
<script>
// import ScreenMap from './components/ScreenMap.vue'
// import TagCloud from './components/TagCloud.vue'
// import RightList from './components/RightList.vue'
import RightTitle from './components/RightTitle.vue'
import ResourceAnalysis from './components/ResourceAnalysis.vue'
import SupplierIncrease from './components/SupplierIncrease.vue'
import SupplierAnalysis from './components/SupplierAnalysis.vue'
// import ExpenditureAmount from './components/ExpenditureAmount.vue'
import SupplierLevel from './components/SupplierLevel.vue'
import PlatformActivity from './components/PlatformActivity.vue' // 平台活跃度
import EcologicalAnalysis from './components/EcologicalAnalysis.vue' // 平台生态分析
import QuantityChange from './components/QuantityChange.vue' // 供应商数量变化
import CategorySuppliers from './components/CategorySuppliers.vue' // 各品类供应商数
import ListTable from './components/ListTable.vue' //黑名单清单
import InfoListForm from './components/InfoListForm.vue'
import bus from '@/utils/bus'
export default {
  components: {
    RightTitle,
    ResourceAnalysis,
    SupplierAnalysis,
    SupplierIncrease,
    SupplierLevel,
    PlatformActivity,
    EcologicalAnalysis,
    QuantityChange,
    CategorySuppliers,
    ListTable,
    InfoListForm
  },
  created() {
    let { organizationid } = this.$route.query
    this.organizationId = organizationid
  },
  data() {
    return {
      classState: true,
      organizationId: '',
      jxInfoSetting: {
        titleName: this.$t('集团绩效') + ' Top 10',
        mockList: [],
        isShowScore: true,
        isShowPL: true,
        isShowPLName: false,
        isShowXM: false,
        isShowTime: false,
        type: 'Platform',
        dataArrPL: []
      },
      zcInfoSetting: {
        titleName: this.$t('中标金额') + ' Top 10',
        mockList: [],
        isShowScore: false,
        isShowPL: false,
        isShowPercent: false,
        isShowPLName: false,
        type: 'Platform',
        isShowXM: true,
        isShowTime: true,
        dataArrXM: []
      },
      hzInfoSetting: {
        titleName: this.$t('生态合作金额') + ' Top 10',
        mockList: [],
        isShowScore: false,
        isShowPL: false,
        isShowPercent: false,
        isShowPLName: false,
        isShowXM: false,
        type: 'Platform',
        isShowTime: true
      },
      projectVal: '',
      dateVal: '',
      areaCode: '',
      orgId: '',
      value: '',
      parentIdPl: 0,
      timer: false
    }
  },
  mounted() {
    this.getProdtreeInClient()
    this.getDictItems()
    bus.$on('areaChange', (a) => {
      this.areaCode = a
      this.queryScore()
      this.purchaseAmountTop()
    })
    this.queryScore()
    this.purchaseAmountTop()
    this.querySupplierAmountTop()
    // 根据外层宽度变化切换样式
    window.addEventListener('scroll', this.checkScroll, true)
    window.addEventListener('resize', this.checkScroll, true)
  },
  methods: {
    checkScroll() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        // 最大值1199，最小值1200  根据外层宽度变化切换样式
        if (this.$refs.DataScreen.clientWidth >= 1200) {
          this.classState = true
        } else if (this.$refs.DataScreen.clientWidth <= 1199) {
          this.classState = false
        }
      }, 300)
    },
    // checkResize(){
    //   if (this.timer) {
    //     clearTimeout(this.timer);
    //   }
    //   this.timer = setTimeout(() => {
    //     console.log("222222222222222222");
    //     // 最大值1199，最小值1200  根据外层宽度变化切换样式
    //     if (this.$refs.DataScreen.clientWidth >= 1200) {
    //       this.classState = true;
    //     } else if (this.$refs.DataScreen.clientWidth <= 1199) {
    //       this.classState = false;
    //     }
    //   }, 300);
    // },
    // 绩效TOP10
    queryScore() {
      this.$API.supplierDashboard
        .queryScoreTen({
          areaCode: this.areaCode,
          // orgId: this.orgId, //集团ID
          categoryId: this.value ? this.value : '' //品类ID
        })
        .then((res) => {
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.map((item) => {
            item.itemName = item.supplierEnterpriseName
            item.itemScore = item.score
          })
          this.jxInfoSetting.mockList = arr
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 中标金额TOP10 val:项目分类  date:日期
    purchaseAmountTop() {
      let tdate = new Date()
      let tyear = tdate.getFullYear()
      this.$API.supplierDashboard
        .biddingAmountTen({
          biddingProjectType: this.projectVal,
          supplierProvinceCode: this.areaCode,
          // "categoryId": 0,
          // "groupOrgId": 0,
          // "purOrgId": 0,
          year: this.dateVal || this.dateVal !== '' ? this.dateVal : tyear
        })
        .then((res) => {
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.map((item) => {
            item.itemName = item.purOrgName
            item.itemScore = item.biddingAmount
          })
          this.zcInfoSetting.mockList = arr
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 生态合作金额top10
    querySupplierAmountTop(val) {
      let tdate = new Date()
      let tyear = tdate.getFullYear()
      this.$API.supplierDashboard
        .querySupplierAmountTop({
          year: val ? val : tyear
        })
        .then((res) => {
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.map((item) => {
            item.itemName = item.supplierName
            item.itemScore = item.purchaseAmount
          })
          this.hzInfoSetting.mockList = arr
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    //品类分类
    getProdtreeInClient() {
      this.$API.supplierInvitationAdd
        .getProdtreeInClient({ id: 0 })
        .then((res) => {
          let arr = JSON.parse(JSON.stringify(res.data))
          arr.forEach((item) => {
            item.categoryName = this.$t('不限品类')
          })
          this.jxInfoSetting.dataArrPL = arr
        })
        .catch((err) => {
          this.$toast({
            content: err.msg ? err.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    // 项目分类
    getDictItems() {
      let dictCode = 'projectType'
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          let arr = []
          res.data.forEach((item) => {
            let uip = {
              stageName: item.name,
              id: item.id,
              code: item.itemCode
            }
            arr.push(uip)
          })
          this.zcInfoSetting.dataArrXM = arr
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
        })
    },
    // 品类树形下拉框
    getDwonTreeVal(val) {
      this.value = val
      this.queryScore()
    },
    // 项目分类
    getSelectVal(val) {
      this.projectVal = val.value
      this.purchaseAmountTop()
    },
    getDateVal(val) {
      this.dateVal = val.getFullYear()
      this.purchaseAmountTop()
    },
    getDateValHZ(val) {
      let value = val.getFullYear()
      this.querySupplierAmountTop(value)
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.checkScroll, true)
    window.removeEventListener('resize', this.checkScroll, true)
  }
}
</script>
<style lang="scss" scoped>
/deep/.mt-tree-view .e-treeview .e-level-1 {
  padding: 0 !important;
}
/deep/.mt-tree-view .e-treeview > .e-ul {
  padding: 0 0 0 10px !important;
}
.DataScreen-div {
  background: #fafafa;
  margin: 20px 0;
  min-width: 992px;
  // display: flex;
  .top-2 {
    width: 100%;
    height: 300px;
    margin-bottom: 20px;
  }
  .top {
    width: 100%;
    display: flex;
    margin-bottom: 20px;
    .top-1 {
      width: 35%;
      height: 300px;
      margin: 0 20px 0 0;
    }
    .top-2-1 {
      width: 65%;
      height: 300px;
    }
  }
  .middle {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    .middle-1 {
      height: 332px;
      width: 49%;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      position: relative;
      margin-right: 2%;
      margin-bottom: 20px;
    }
    .middle-1:nth-child(2n) {
      margin-right: 0px;
    }
  }
  .middle3 {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    .middle-1 {
      height: 332px;
      width: 32%;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      position: relative;
      margin-right: 2%;
      margin-bottom: 20px;
    }
    .middle-1:nth-child(3n) {
      margin-right: 0px;
    }
  }
  .bottom {
    width: 100%;
    display: flex;
    margin-top: 20px;
    .bottom-1 {
      height: 480px;
      flex: 1;
      width: 33%;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      position: relative;
    }
  }
  .top-1,
  .top-2-1,
  .top-2 {
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  }
}
</style>
