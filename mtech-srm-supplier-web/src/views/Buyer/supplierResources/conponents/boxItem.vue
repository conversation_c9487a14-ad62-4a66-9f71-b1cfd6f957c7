<template>
  <div class="item-wrap fbox">
    <div class="display-item" @click="selectItem(item)" v-for="item in searchList" :key="item.id">
      <div class="display-item-inner">
        <div class="mian-title fbox">
          <!-- enterpriseLogoFileId -->
          <div class="title-pic">
            <img
              :src="
                '/api/file/user/file/viewPublicImageFile?id=' +
                item.enterpriseResponseVo.enterpriseLogoFileId
              "
            />
          </div>
          <div
            class="title-desc flex1"
            :data-id="item.id"
            :data-partnerarchiveid="item.partnerArchiveId"
            :data-supplierEnterpriseId="item.supplierEnterpriseId"
            @click="jumpSp"
          >
            {{ item.supplierEnterpriseName }}
          </div>
        </div>
        <div class="item-tips">
          <template
            v-if="
              !!item.enterpriseResponseVo &&
              !!item.enterpriseResponseVo.labelList &&
              item.enterpriseResponseVo.labelList.length > 0
            "
          >
            <div
              class="tips-item"
              v-for="child in item.enterpriseResponseVo.labelList"
              :key="child.labelCode"
            >
              {{ child.labelName }}
            </div>
          </template>
        </div>

        <template
          v-if="
            !!item.enterpriseResponseVo &&
            !!item.enterpriseResponseVo.industryList &&
            item.enterpriseResponseVo.industryList.length > 0
          "
        >
          <div class="normal-line">
            行业：<template v-for="child in item.enterpriseResponseVo.industryList"
              ><span :key="child.industryName">{{ child.industryName }}</span></template
            >
          </div>
        </template>
        <template v-else>
          <div class="normal-line"></div>
        </template>

        <template
          v-if="
            !!item.enterpriseResponseVo &&
            !!item.enterpriseResponseVo.categoryList &&
            item.enterpriseResponseVo.categoryList.length > 0
          "
        >
          <div class="normal-line eplips-2">
            产品服务：<template v-for="child in item.enterpriseResponseVo.categoryList"
              ><span :key="child.categoryCode">{{ child.categoryName }}</span></template
            >
          </div>
        </template>

        <div class="push-tip">
          <template
            v-if="
              !!item.enterpriseResponseVo &&
              !!item.enterpriseResponseVo.labelList &&
              item.enterpriseResponseVo.labelList.length > 0
            "
          >
            <span
              class="tips-item"
              v-for="child in item.enterpriseResponseVo.labelList"
              :key="child.labelCode"
              >{{ child.labelName }}</span
            >
          </template>
        </div>

        <div class="company-info fbox">
          <div class="cp-money">
            <template
              v-if="!!item.enterpriseResponseVo && item.enterpriseResponseVo.registerCapital"
            >
              注册资金：{{ item.enterpriseResponseVo.registerCapital }}
            </template>
          </div>
          <div class="cp-address">
            <template
              v-if="
                !!item.enterpriseResponseVo &&
                (item.enterpriseResponseVo.registerAddressCountryName ||
                  item.enterpriseResponseVo.registerAddressProvinceName ||
                  item.enterpriseResponseVo.registerAddressCityName)
              "
            >
              地区：{{ item.enterpriseResponseVo.registerAddressCountryName }}
              {{ item.enterpriseResponseVo.registerAddressProvinceName }}
              {{ item.enterpriseResponseVo.registerAddressCityName }}
            </template>
          </div>
        </div>

        <div class="operator-line fbox">
          <div class="checkbox">
            <template v-if="isInArray(item)">
              <mt-icon name="a-icon_MultipleChoice_on" />
            </template>
            <template v-else>
              <mt-icon name="a-icon_MultipleChoice_off" />
            </template>
          </div>
          <div class="btn-box fbox">
            <template
              v-if="
                orgLevelTypeCode === 'ORG02-1' ||
                orgLevelTypeCode === 'ORG01-1' ||
                orgLevelTypeCode === 'ORG01'
              "
            >
              <!-- 置灰 -->
              <template v-if="item.relationType === 1">
                <div class="operator-item fbox grey">
                  <div class="op-icon"><mt-icon name="icon_card_invite" /></div>
                  <div class="op-txt">{{ $t('已准入') }}</div>
                </div>
              </template>
              <template v-if="item.relationType === 2">
                <div class="operator-item fbox" :data-id="item.id" @click="popyinruDialog">
                  <div class="op-icon"><mt-icon name="icon_card_invite" /></div>
                  <div class="op-txt">{{ $t('引入') }}</div>
                </div>
              </template>
            </template>
            <template
              v-if="
                orgLevelTypeCode === 'ORG02' ||
                orgLevelTypeCode === 'ORG02-1' ||
                orgLevelTypeCode === 'ORG01-1'
              "
            >
              <div class="operator-item fbox" :data-id="item.id" @click="popShareDialog">
                <div class="op-icon"><mt-icon name="icon_table_share" /></div>
                <div class="op-txt">{{ $t('共享') }}</div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      checkedList: []
    }
  },
  props: {
    searchList: {
      type: Array,
      default: () => {
        return []
      }
    },
    orgLevelTypeCode: {
      type: String,
      default: ''
    }
  },
  methods: {
    // 新增引入
    popyinruDialog(e) {
      e.stopPropagation()
      let { dataset } = e.currentTarget
      let id = dataset.id
      let selectItem = this.searchList.filter((item) => item.id === id)[0]
      this.$dialog({
        modal: () => import('./addyinru.vue'),
        data: {
          title: this.$t('新增引入'),
          mode: 'edit',
          lineDate: selectItem
        },
        success: (result) => {
          let { mode, data } = result
          if (mode === 'goInvite') {
            let ids = data.id || []
            ids = ids + ''

            this.inviteDatas = [data]

            this.$router.push({
              name: 'invitationDetail',
              query: {
                ids
              }
            })
            return
          }
        },
        close: () => {}
      })
    },

    popShareDialog(e) {
      e.stopPropagation()
      let { dataset } = e.currentTarget
      let id = dataset.id
      this.$emit('popShareDialog', id)
      return
    },

    jumpSp(event) {
      event.stopPropagation()
      // 跳转到供应商画像的页面 供应商档案的详情页面
      this.$router.push({
        name: 'lifecycledetail2',
        query: {
          id: event.target.dataset.id,
          supplierEnterpriseId: event.target.dataset.supplierenterpriseid
        }
      })
    },

    selectItem(item) {
      // checked 双击删除
      console.log('checked', item)
      if (
        this.checkedList.length > 0 &&
        this.checkedList.filter((child) => item.id === child.id).length > 0
      ) {
        let index = this.checkedList.findIndex((child) => child.id === item.id)
        this.checkedList.splice(index, 1)
        this.$emit('setCheckList', this.checkedList)
        return
      }
      this.checkedList.push(item)
      this.$emit('setCheckList', this.checkedList)
    },

    isInArray(item) {
      if (
        this.checkedList.length > 0 &&
        this.checkedList.filter((child) => item.id === child.id).length > 0
      ) {
        return true
      } else {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: center;
}

.flex1 {
  flex: 1;
}

.item-wrap {
  display: flex;
  align-items: stretch;
  width: 100%;
  flex-wrap: wrap;
}

.display-item {
  min-width: 16.666%;
  margin-bottom: 20px;
  box-sizing: border-box;
  cursor: pointer;

  .display-item-inner {
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 4px;
    margin-right: 20px;
    padding: 20px 20px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .mian-title {
    .title-pic {
      width: 48px;
      height: 48px;
      background: rgba(216, 216, 216, 1);
      margin-right: 10px;
    }

    .title-desc {
      font-size: 14px;
      line-height: 16px;

      font-weight: 600;
      color: rgba(41, 41, 41, 1);

      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .item-tips {
    margin-top: 10px;

    .tips-item {
      display: inline-block;
      height: 20px;
      line-height: 20px;
      padding: 0 4px;
      color: rgba(99, 134, 193, 1);
      background: rgba(99, 134, 193, 0.1);
      border-radius: 2px;
      margin-right: 5px;
    }
  }

  .normal-line {
    margin-top: 10px;
    font-size: 12px;
    line-height: 18px;

    font-weight: normal;
    color: rgba(41, 41, 41, 1);
  }

  .eplips-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .push-tip {
    font-size: 12px;
    margin-top: 10px;
    height: 12px;
    line-height: 12px;
    font-weight: 600;
    color: rgba(0, 70, 156, 1);
  }

  .company-info {
    margin-top: 10px;
    justify-content: space-between;
    font-size: 12px;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }

  .operator-line {
    justify-content: space-between;
    height: 36px;
    line-height: 36px;
    border-top: 1px solid #e8e8e8;
    margin-top: 10px;

    .checkbox {
      i {
        font-size: 16px;
        color: #00469c;
      }
    }

    .btn-box {
      cursor: pointer;
      user-select: none;
      justify-content: flex-end;
      color: #00469c;

      .operator-item {
        margin-right: 20px;
        cursor: pointer;
        white-space: nowrap;
        cursor: pointer;

        .op-icon {
          display: flex;
          align-items: center;
          margin-right: 8px;
          position: relative;
          top: 2px;
        }

        i {
          display: inline-block;
          width: 14px;
          height: 16px;
          font-size: 14px;
        }
      }

      .grey {
        color: #9a9a9a;
      }

      .operator-item:last-child {
        margin-right: 0;
      }
    }
  }
}

@media only screen and (max-width: 1290px) {
  .display-item {
    flex: 0 0 20%;
  }
  .display-item:nth-child(5n) {
    .display-item-inner {
      margin-right: 0;
    }
  }
}

@media only screen and (min-width: 1290px) {
  .display-item {
    flex: 0 0 16.666%;
  }
  .display-item:nth-child(6n) {
    .display-item-inner {
      margin-right: 0;
    }
  }
}
</style>
