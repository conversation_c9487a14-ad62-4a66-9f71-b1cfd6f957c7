<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('新增标签')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                css-class="search-input"
                :show-clear-button="true"
                type="text"
                @input="changeInput"
                :placeholder="$t('请输入新增标签')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('违规原因')">
              <mt-input
                v-model="formInfo.orgName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入违规原因')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :hidden-tabs="true"
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, areaColumnData } from '../config/index.js'
export default {
  data() {
    return {
      dictCode: 'violationType',
      vala: '',
      valb: '',
      orgLevelTypeCode: 'ORG02',
      applyUserIdData: [],
      stageList: [],
      stagei: [],
      stageInfo: {},
      stagTypeL: [
        {
          text: this.$t('供应商拉黑'),
          value: 'black'
        },
        {
          text: this.$t('供应商冻结'),
          value: 'freeze'
        },
        {
          text: this.$t('供应商淘汰'),
          value: 'disuse'
        }
      ],
      TenantUserList: [], // 当前租户下的用户
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        supplierEnterpriseName: '',
        orgName: '',
        supplierStage: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        supplierTypel: '',
        applyerOrgName: '',
        applyerDeptName: '',
        applyerName: ''
      },
      rules: {
        supplierTypel: [{ required: true, message: this.$t('请输入申请单类型'), trigger: 'blur' }],
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        supplierStage: [{ required: true, message: this.$t('请选择目标阶段'), trigger: 'blur' }],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      componentConfig: [
        {
          gridId: '242a56f2-651b-4c09-9f95-894fdcaa822f',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: areaColumnData,
            dataSource: [{}]
          }
        }
      ]
    }
  },
  created() {},
  mounted() {
    this.show()
    this.getCurrentTenantUsers()
    // 出发change
    this.$set(this.componentConfig[0].grid, 'dataSource', this.dataInfo.sltList)
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    // 提交
    confirm() {
      let formInfo = this.formInfo
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }

      let infoDTO = {
        applyName: formInfo.supplierEnterpriseName,
        applyerOrgId: formInfo.companyId,
        applyerOrgName: formInfo.applyerOrgName,
        applyerDeptId: formInfo.applyDepId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyUserId,
        applyerName: formInfo.applyUser,
        applyType: formInfo.supplierTypel,
        remark: formInfo.remark
      }
      let punishDTO = {
        violationType: formInfo.supplierStage,
        penaltyPeriodEnd: this.valb,
        penaltyPeriodStart: this.vala,
        businessType: formInfo.supplierTypel,
        sourceOrgCode: 'TDi',
        sourceOrgDimension: 'ORG02',
        sourceOrgId: '123',
        sourceOrgName: this.$t('京东物流科技有限公司')
      }

      // let relationDTOList = this.componentConfig[0].grid.dataSource.map((item) => {
      //   return { partnerRelationId: item.id };
      // });

      let relationDTOList = [
        {
          partnerRelationId: '1439072419756183554',
          supplierEnterpriseCode: this.stageInfo.code,
          supplierEnterpriseId: this.stageInfo.id,
          supplierEnterpriseName: this.stageInfo.stageName
        }
      ]

      if (relationDTOList.length === 0) {
        this.$toast({
          content: this.$t('新增失败，请至少有一个勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        infoDTO,
        punishDTO,
        relationDTOList
      }
      this.$loading()

      this.$API.supplierResources
        .addFormTemplateb(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({ content: this.$t('新增成功'), type: 'warning' })
            this.$emit('confirm-function', 'reload')
          } else {
            this.$toast({
              content: res.msg || this.$t('新增失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('新增失败，请重试'),
            type: 'warning'
          })
        })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (sltList.filter((citem) => citem.id === item.id).length === 0) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },
    handleClickCellTool() {},
    handleClickCellTitle() {},

    handleUserChange(data) {
      let { itemData } = data
      console.log(itemData)
      this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.userName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      console.log(itemData)
      this.stageInfo = itemData
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      let dictCode = this.dictCode
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          console.log(res)
          this.stagei = res.data
          this.stagei.forEach((item) => {
            let uip = {
              stageName: item.name,
              id: item.id,
              code: item.itemCode
            }
            this.stageList.push(uip)
          })
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
          console.log(this.applyUserIdData)
        })
      })
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            console.log(res.data)
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    handleCompanyChange(data) {
      let { itemData } = data
      console.log(data, itemData)
      // this.formInfo.companyId = itemData.id
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
.upgrade-dialog {
  padding: 40px 20px;

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
.search-input-wrap {
  height: 50px;

  &::before {
    content: ' ';
    display: inline-block;
    width: 1px;
    height: 30px;
    position: absolute;
    top: 10px;
    left: 590px;
    background: rgba(232, 232, 232, 1);
  }

  .search-input {
    width: 100%;
    height: 50px;
    border-bottom: none;
    padding-left: 20px;
    input {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      border-bottom: none;
    }
  }
}
</style>
