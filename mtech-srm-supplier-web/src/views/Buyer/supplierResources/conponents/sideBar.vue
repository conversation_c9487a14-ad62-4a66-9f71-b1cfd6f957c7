<template>
  <div class="side-bar-box">
    <div class="side-content">
      <div class="title-banner mt-flex">
        <div class="title-txt">{{ $t('新增/编辑') }}</div>
        <div class="close" @click="closeSideBar()">
          <mt-icon name="close" />
        </div>
      </div>

      <div class="mian-form">
        <div class="mian-title">{{ $t('筛选条件') }}</div>
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <mt-form-item prop="service" css-class="form-item-wrap" :label="$t('主要产品/服务')">
            <mt-multi-select
              :show-drop-down-icon="true"
              :data-source="companyDataArr"
              :show-clear-button="true"
              :allow-filtering="true"
              v-model="ruleForm.service"
              :placeholder="$t('请选择主要产品/服务')"
            ></mt-multi-select>
          </mt-form-item>

          <mt-form-item prop="companyTags" css-class="form-item-wrap" :label="$t('企业标签')">
            <mt-multi-select
              :show-drop-down-icon="true"
              :data-source="companyDataArr"
              :show-clear-button="true"
              :allow-filtering="true"
              v-model="ruleForm.companyTags"
              :placeholder="$t('请选择企业标签')"
            ></mt-multi-select>
          </mt-form-item>

          <mt-form-item prop="totalPrice" css-class="form-item-wrap" :label="$t('与我成交金额')">
            <mt-multi-select
              :show-drop-down-icon="true"
              :data-source="companyDataArr"
              :show-clear-button="true"
              :allow-filtering="true"
              v-model="ruleForm.totalPrice"
              :placeholder="$t('请选择与我成交金额')"
            ></mt-multi-select>
          </mt-form-item>

          <mt-form-item prop="area" css-class="form-item-wrap" :label="$t('区域')">
            <mt-multi-select
              :show-drop-down-icon="true"
              :data-source="companyDataArr"
              :show-clear-button="true"
              :allow-filtering="true"
              v-model="ruleForm.area"
              :placeholder="$t('请选择区域')"
            ></mt-multi-select>
          </mt-form-item>

          <mt-form-item prop="trade" css-class="form-item-wrap" :label="$t('所属行业')">
            <mt-multi-select
              :show-drop-down-icon="true"
              :data-source="companyDataArr"
              :show-clear-button="true"
              :allow-filtering="true"
              v-model="ruleForm.trade"
              :placeholder="$t('请选择所属行业')"
            ></mt-multi-select>
          </mt-form-item>
        </mt-form>

        <!-- 预设条件 -->
        <div class="prset-condition">
          <div class="prset-title">{{ $t('预设条件') }}</div>

          <!-- 预设的行 -->
          <div class="preset-condition-line">
            <div class="title-wrap fbox">
              <div class="prset-title">{{ $t('预设条件') }}1</div>
              <div class="prset-operator fbox">
                <mt-icon name="icon_list_edit" />
                <mt-icon name="icon_list_delete" />
              </div>
            </div>

            <div class="filter-result fbox">
              <div class="filter-item fbox">
                <div class="filter-txt flex1">{{ $t('工业器件') }}</div>
                <div class="filter-icon"><mt-icon name="icon_input_clear" /></div>
              </div>

              <div class="filter-item fbox">
                <div class="filter-txt flex1">{{ $t('工业器件') }}</div>
                <div class="filter-icon"><mt-icon name="icon_input_clear" /></div>
              </div>
            </div>
          </div>

          <!-- 未命名筛选条件 -->
          <div class="preset-condition-line">
            <div class="title-wrap fbox">
              <div
                class="prset-title"
                contenteditable="true"
                ref="editLine"
                @change="changeEditLine"
                @input="inputEditLie"
              >
                {{ $t('未命名筛选条件') }}
              </div>
              <div class="prset-operator fbox">
                <mt-icon name="icon_list_edit" />
                <mt-icon name="icon_list_delete" />
              </div>
            </div>

            <div class="filter-result fbox">
              <div class="filter-item fbox">
                <div class="filter-txt flex1">{{ $t('工业器件') }}</div>
                <div class="filter-icon"><mt-icon name="icon_input_clear" /></div>
              </div>

              <div class="filter-item fbox">
                <div class="filter-txt flex1">{{ $t('工业器件') }}</div>
                <div class="filter-icon"><mt-icon name="icon_input_clear" /></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部关闭按钮 -->
      <div class="btn-box">
        <div class="btn-item" @click="closeSideBar">{{ $t('取消') }}</div>
        <div class="btn-item" @click="submitRole">{{ $t('确定') }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      companyDataArr: [
        { text: this.$t('区域1'), value: 0 },
        { text: this.$t('区域2'), value: 1 }
      ],
      ruleForm: {
        service: '',
        companyTags: '',
        totalPrice: '',
        area: ''
      },
      rules: {
        taskDefineClassify: [{ required: false }]
      }
    }
  },
  methods: {
    changeEditLine(e) {
      console.log(e)
    },
    inputEditLie(e) {
      console.log(e)
    },
    closeSideBar() {
      this.$emit('cancel-function')
    },
    submitRole() {
      this.$emit('confirm-function', {})
    }
  }
}
</script>

<style lang="scss">
.side-bar-box {
  background: rgba(0, 0, 0, 0.2);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1001;

  .side-content {
    width: 800px;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px 0 0 8px;

    .title-banner {
      width: 800px;
      height: 58px;
      line-height: 60px;
      padding: 0 20px 0 30px;
      background: #00469c;
      border-radius: 8px 0 0 0;
      justify-content: space-between;
      align-items: center;
      position: fixed;
      top: 0;
      right: 0;
      z-index: 999;

      .title-txt {
        font-size: 18px;
        color: #fff;
        font-weight: 500;
      }

      .close {
        cursor: pointer;
        color: #fff;
      }
    }

    .mian-form {
      margin-top: 58px;
      padding: 30px 40px;

      .mian-title {
        font-size: 16px;
        font-weight: 500;
        color: #292929;
        margin-bottom: 20px;
      }

      .prset-condition {
        font-size: 16px;
        color: #292929;
        font-weight: 500;
        margin-top: 40px;

        .prset-title {
          font-size: 16px;
          font-weight: 500;
          color: #292929;
        }

        .preset-condition-line {
          .title-wrap {
            margin-top: 20px;

            .prset-title {
              font-size: 14px;
              font-weight: normal;
              color: #292929;
            }

            .prset-operator {
              font-size: 12px;
              color: #6386c1;

              i {
                margin-left: 10px;
              }
            }
          }

          .filter-result {
            margin-top: 10px;
            flex-wrap: wrap;

            .filter-item {
              padding: 0 4px;
              height: 22px;
              line-height: 22px;
              font-size: 14px;
              font-weight: normal;
              color: rgba(41, 41, 41, 1);
              background: rgba(245, 245, 245, 1);
              border-radius: 2px;
              margin-right: 10px;

              .filter-txt {
                margin-right: 4px;
                white-space: nowrap;
                color: #292929;
                user-select: none;
              }

              .filter-icon {
                display: inline-block;
                width: 10px;
                height: 10px;
                font-size: 10px;
                display: flex;
                align-items: center;
                cursor: pointer;

                i {
                  cursor: pointer;
                  width: 10px;
                  height: 10px;
                  font-size: 10px;
                  color: #9baac1;
                }
              }
            }
          }
        }
      }
    }

    .btn-box {
      position: fixed;
      bottom: 0;
      right: 0;
      width: 800px;
      height: 60px;
      line-height: 60px;
      border-radius: 0 0 0 8px;
      box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
      display: inline-flex;
      justify-content: flex-end;
      background: #fff;

      .btn-item {
        cursor: pointer;
        height: 60px;
        line-height: 60px;
        padding-left: 30px;
        padding-right: 30px;
        font-size: 14px;
        color: #0043a8;
      }
    }
  }
}
</style>

<style lang="scss"></style>
