<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="orgName " class="form-item" :label="$t('源组织')">
              <!-- :disabled="true" -->
              <mt-input
                v-model="formInfo.orgName"
                :readonly="true"
                :disabled="true"
                css-class="grey-input"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入源组织')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierTypel" class="form-item" :label="$t('申请类型')">
              <mt-select
                v-model="formInfo.supplierTypel"
                :width="400"
                :disabled="true"
                :data-source="stagTypeL"
                :show-clear-button="true"
                @change="handleChangeStage"
                :placeholder="$t('请选择申请类型')"
              ></mt-select>
            </mt-form-item>

            <mt-form-item class="form-item" prop="workflowName" :label="$t('目标组织')">
              <mt-select
                v-model="formInfo.workflowName"
                :width="400"
                :disabled="true"
                :data-source="parentOrgList"
                :fields="{ text: 'orgName', value: 'id' }"
                :show-clear-button="true"
                @change="handleChangeParent"
                :placeholder="$t('请选择目标组织')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyUserId" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.applyUserId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="companyId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.companyId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyDepId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyDepId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                :multiline="true"
                maxlength="200"
                :rows="2"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, companyColumnData } from '../config/index.js'
export default {
  data() {
    return {
      applyUserIdData: [],
      parentOrgList: [],
      parentInfo: {},
      stageInfo: {},
      stagTypeL: [],
      TenantUserList: [], // 当前租户下的用户
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        workflowName: '',
        supplierEnterpriseName: '',
        orgName: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        supplierTypel: '',
        applyerOrgName: '',
        applyerDeptName: '',
        applyerName: ''
      },
      rules: {
        supplierTypel: [{ required: true, message: this.$t('请输入申请单类型'), trigger: 'blur' }],
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        workflowName: [{ required: true, message: this.$t('请选择目标组织'), trigger: 'blur' }],
        orgName: [{ required: true, message: this.$t('请选择原组织'), trigger: 'blur' }],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'subChild'
      },
      componentConfig: [
        {
          gridId: '747bca01-4644-45a4-acba-92bc917f0b96',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: companyColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  created() {
    this.getRelationList()
  },
  mounted() {
    // 渲染数据
    this.getDictItem()
    this.renderData()
    this.show()
    this.getCurrentTenantUsers()
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.info
    },
    methodInfo() {
      return this.modalData.methodInfo
    }
  },
  methods: {
    // 副组织
    handleChangeParent(data) {
      let { itemData } = data
      this.parentInfo = itemData
      this.formInfo.workflowName = itemData.id
      console.log(this.parentInfo)
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    // 渲染默认传参
    renderData() {
      let { buyerOrgName } = this.info[0]
      this.formInfo = {
        ...this.formInfo,
        orgName: buyerOrgName // 源组织信息
      }
      let dataSource = this.info.map((v) => {
        return {
          supplierEnterpriseCode: v.supplierEnterpriseCode,
          supplierEnterpriseName: v.supplierEnterpriseName
        }
      })
      this.$set(this.componentConfig[0].grid, 'dataSource', dataSource)
    },
    // 申请类型
    getDictItem() {
      let dictCode = 'shareType'
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.stagTypeL = res.data.map((item) => {
              return {
                ...item,
                text: item.name,
                value: item.itemCode
              }
            })
            this.formInfo.supplierTypel =
              this.methodInfo === 'up' ? res.data[0].itemCode : res.data[1].itemCode // 默认共享升级
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
        })
    },
    // 获取目标组织
    getRelationList() {
      let organizationId = this.modalData.info[0].buyerOrgId
      this.$API.SupplierPunishment.getParentOrg({
        organizationId
      })
        .then((res) => {
          console.log(res)
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            this.parentOrgList = [res.data]
            this.parentInfo = res.data
            this.formInfo.workflowName = res.data.id
          } else {
            this.parentOrgList = []
            this.$toast({
              content: this.$t('获取目标组织数据失败！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
        })
    },

    // 提交
    confirm() {
      let formInfo = this.formInfo
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }

      let applyInfo = {
        applyName: formInfo.supplierEnterpriseName,
        applyerOrgId: formInfo.companyId,
        applyerOrgName: formInfo.applyerOrgName,
        applyerDeptId: formInfo.applyDepId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyUserId,
        applyerName: formInfo.applyUser,
        applyType: formInfo.supplierTypel,
        remark: formInfo.remark
      }
      let applyShare = {
        businessType: this.stageInfo.value,
        destinationOrgId: this.parentInfo.id,
        destinationOrgName: this.parentInfo.orgName,
        destinationOrgCode: this.parentInfo.orgCode,
        destinationOrgDimension: this.parentInfo.orgLevelTypeCode,
        sourceOrgCode: this.info[0].buyerOrgCode,
        sourceOrgDimension: this.parentInfo.orgLevelTypeCode, //this.info[0].buyerOrgDimension,
        sourceOrgId: this.info[0].buyerOrgId,
        sourceOrgName: this.info[0].buyerOrgName
      }

      let relationList = this.info.map((v) => {
        return {
          partnerArchiveId: v.partnerArchiveId,
          partnerRelationId: v.id,
          remark: '',
          supplierEnterpriseCode: v.supplierEnterpriseCode,
          supplierEnterpriseId: v.supplierEnterpriseId,
          supplierEnterpriseName: v.supplierEnterpriseName,
          tenantId: v.tenantId
        }
      })

      if (relationList.length === 0) {
        this.$toast({
          content: this.$t('新增失败，请至少有一个勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        applyInfo,
        applyShare,
        relationList
      }
      this.$loading()

      this.$API.supplierResources
        .addFormTemplate(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({ content: this.$t('新增成功'), type: 'success' })
            this.$emit('confirm-function', 'reload')
          } else {
            this.$toast({
              content: res.msg || this.$t('新增失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('新增失败，请重试'),
            type: 'warning'
          })
        })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    filterNode(arr, id) {
      console.log(id)
      console.log(arr)
      if (arr.filter((v) => v.id === id).length > 0) {
        let arrTmp = arr.filter((v) => v.id === id)
        this.selectCompanyObject = arrTmp[0]
      } else {
        arr.forEach((vc) => {
          if (!!vc.subChild && vc.subChild.length > 0) {
            this.filterNode(vc.subChild, id)
          }
        })
      }
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (
            sltList.filter((citem) => citem.supplierEnterpriseCode === item.supplierEnterpriseCode)
              .length === 0
          ) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },

    handleUserChange(data) {
      let { itemData } = data
      console.log(itemData)
      this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.userName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      console.log(itemData)
      this.stageInfo = itemData
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
        })
      })
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            console.log(res.data)
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    handleCompanyChange(data) {
      let { itemData } = data
      console.log(data, itemData)
      // this.formInfo.companyId = itemData.id
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
.upgrade-dialog {
  padding: 20px; //--*

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
</style>
