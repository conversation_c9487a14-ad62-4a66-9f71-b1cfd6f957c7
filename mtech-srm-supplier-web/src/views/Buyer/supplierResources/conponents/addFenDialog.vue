<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="24">
          <mt-col :span="24">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="orgName" class="form-item" :label="$t('分级组织')">
              <mt-input
                v-model="formInfo.orgName"
                :disabled="true"
                :width="400"
                :height="300"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierStage" class="form-item" :label="$t('级别')">
              <mt-select
                v-model="formInfo.supplierStage"
                :width="400"
                :show-clear-button="true"
                :data-source="stageList"
                :fields="{ text: 'labelName', value: 'id' }"
                @change="handleChangeStage"
                :placeholder="$t('请选择级别')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyUserId" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.applyUserId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="companyId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.companyId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyDepId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyDepId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                maxlength="200"
                :multiline="true"
                :rows="2"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :hidden-tabs="true"
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, companyColumnData } from '../config/index.js'
export default {
  data() {
    return {
      labelDefineType: 1,
      applyUserIdData: [],
      stageList: [],
      stagei: [],
      stageInfo: {},
      TenantUserList: [], // 当前租户下的用户
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        supplierEnterpriseName: '',
        orgName: '',
        supplierStage: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        supplierTypel: '',
        applyerOrgName: '',
        applyerDeptName: '',
        applyerName: ''
      },
      rules: {
        supplierTypel: [{ required: true, message: this.$t('请输入申请单类型'), trigger: 'blur' }],
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        supplierStage: [{ required: true, message: this.$t('请选择目标阶段'), trigger: 'blur' }],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmSubmit,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      componentConfig: [
        {
          gridId: '01983c50-82d8-49e2-a897-b6d03fdb40c1',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: companyColumnData,
            dataSource: [{}]
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.info
    }
  },
  created() {},
  mounted() {
    this.getclassify()
    this.show()
    this.getCurrentTenantUsers()
    this.renderData()
    // 出发change
    // this.$set(
    //   this.componentConfig[0].grid,
    //   "dataSource",
    //   this.dataInfo.sltList
    // );
  },
  methods: {
    // 渲染默认传参
    renderData() {
      let { buyerOrgName } = this.info[0]
      this.formInfo = {
        ...this.formInfo,
        orgName: buyerOrgName // 源组织信息
      }
      let dataSource = this.info.map((v) => {
        return {
          partnerRelationId: v.id,
          supplierEnterpriseCode: v.supplierEnterpriseCode,
          supplierEnterpriseName: v.supplierEnterpriseName,
          supplierEnterpriseId: v.supplierEnterpriseId,
          supplierType: v.supplierType,
          partnerArchiveId: v.partnerArchiveId
        }
      })
      this.$set(this.componentConfig[0].grid, 'dataSource', dataSource)
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    // 提交
    confirm(fn) {
      let formInfo = this.formInfo
      let validRs = false
      let levelObj = {}
      this.stageList.forEach((e) => {
        if (e.id === this.formInfo.supplierStage) {
          levelObj.gradeId = e.id
          levelObj.gradeName = e.labelName
          levelObj.gradeType = e.labelType
        }
      })
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }
      let gradeDTO = {
        ...levelObj,
        businessType: formInfo.supplierTypel,
        orgId: this.info[0].buyerOrgId,
        orgName: this.info[0].buyerOrgName,
        orgCode: this.info[0].buyerOrgCode,
        orgDimension: this.info[0].buyerOrgDimension,
        applyType: formInfo.supplierTypel,
        remark: formInfo.remark
      }
      let infoDTO = {
        applyerDeptId: formInfo.applyDepId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyUserId,
        applyerName: formInfo.applyUser,
        applyName: formInfo.supplierEnterpriseName,
        applyerOrgId: formInfo.companyId,
        applyerOrgName: formInfo.applyerOrgName
      }

      let relationDTOList = this.componentConfig[0].grid.dataSource

      if (relationDTOList.length === 0) {
        this.$toast({
          content: this.$t('新增失败，请至少有一个供应商勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        gradeDTO,
        infoDTO,
        relationDTOList
      }
      this.$loading()

      this.$API.supplierResources
        .addFormTemplatea(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({ content: this.$t('新增成功'), type: 'success' })
            if (!!fn && typeof fn === 'function') {
              fn(res.data)
            } else {
              this.$emit('confirm-function', 'reload')
            }
          } else {
            this.$toast({
              content: res.msg || this.$t('新增失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('新增失败，请重试'),
            type: 'warning'
          })
        })
    },

    confirmSubmit() {
      this.confirm(() => {
        this.$emit('confirm-function', { type: 'jump' })
      })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (
            sltList.filter((citem) => citem.partnerRelationId === item.partnerRelationId).length ===
            0
          ) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },
    handleClickCellTool() {},
    handleClickCellTitle() {},

    handleUserChange(data) {
      let { itemData } = data
      console.log(itemData)
      this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.userName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      console.log(itemData)
      this.stageInfo = itemData
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
          console.log(this.applyUserIdData)
        })
      })
    },

    getclassify() {
      let labelDefineType = this.labelDefineType
      this.$API.supplierResources.getclassify({ labelDefineType }).then((res) => {
        this.stagei = res.data
        console.log(res)
        this.stagei.forEach((item) => {
          let uip = {
            labelName: item.labelName,
            id: item.id,
            labelType: item.labelType
          }
          this.stageList.push(uip)
        })
      })
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            console.log(res.data)
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    handleCompanyChange(data) {
      let { itemData } = data
      console.log(data, itemData)
      // this.formInfo.companyId = itemData.id
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
.upgrade-dialog {
  padding: 20px; //--*

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
</style>
