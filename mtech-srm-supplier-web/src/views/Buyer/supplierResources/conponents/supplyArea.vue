<template>
  <div class="supply-area">
    <mt-template-page
      :padding-top="true"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { areaColumnDatab, supplyAreaToolbara } from '../config/index'

export default {
  props: {
    extList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    extList(newValue) {
      if (!!newValue && newValue.length > 0) {
        this.$set(this.componentConfig[0].grid, 'dataSource', newValue)
      }
    }
  },
  data() {
    return {
      resultAreaData: [],
      componentConfig: [
        {
          gridId: 'b727df2e-91a3-47cc-abb7-6e78948f9a05',
          toolbar: supplyAreaToolbara,
          grid: {
            columnData: areaColumnDatab,
            dataSource: []
          }
        }
      ]
    }
  },
  methods: {
    diff(oldArr, newArr) {
      let result = oldArr
      newArr.forEach((element) => {
        //  orgId: item.id,
        //   orgCode: item.orgCode,
        //   orgName: item.name,
        //   factoryId: "",
        //   factoryName: "",
        //   factoryCode: "",

        //   categoryCode: "",
        //   categoryId: "",
        //   categoryName: "",
        if (
          oldArr.filter(
            (item) =>
              item.orgId === element.orgId &&
              item.factoryId === element.factoryId &&
              item.categoryId === element.categoryId
          ).length === 0
        ) {
          result.push(element)
        }
      })
      return result
    },
    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      console.log(e, e.gridRef.getMtechGridRecords())
      let lineData = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id === 'addNew') {
        this.$dialog({
          modal: () => import('./addSArea.vue'),
          data: {
            title: this.$t('新增供应范围'),
            distributeArr: []
          },
          success: (data) => {
            console.log(data)
            let resultAreaData = this.componentConfig[0].grid.dataSource
            let difResultAreaData = this.diff(resultAreaData, data)

            this.$set(this.componentConfig[0].grid, 'dataSource', difResultAreaData)
            // 同步数据到组建外
            this.$emit('pushData', difResultAreaData)
          }
        })
      }
      if (e.toolbar.id === 'deleteList' || e.toolbar.id === 'Delete') {
        if (e.gridRef.getMtechGridRecords().length === 0) {
          this.$toast({ content: this.$t('请勾选数据！'), type: 'error' })
          return
        }
        let resultAreaData = this.componentConfig[0].grid.dataSource
        let filterRsData = []
        resultAreaData.forEach((item) => {
          if (
            lineData.filter(
              (v) =>
                v.categoryId === item.categoryId &&
                v.orgId === item.orgId &&
                v.factoryId === item.factoryId
            ).length === 0
          ) {
            filterRsData.push(item)
          }
        })
        // 同步数据到组建外
        this.$emit('pushData', filterRsData)

        this.$set(this.componentConfig[0].grid, 'dataSource', filterRsData)
      }
    },

    // cell tool
    handleClickCellTool(e) {
      console.log(e)
      let { data, tool } = e
      if (tool.id === 'delete') {
        let resultAreaData = this.componentConfig[0].grid.dataSource
        let index = resultAreaData.findIndex(
          (v) =>
            v.categoryId === data.categoryId &&
            v.orgId === data.orgId &&
            v.factoryId === data.factoryId
        )
        resultAreaData.splice(index, 1)
        // 同步数据到组建外
        this.$emit('pushData', resultAreaData)

        this.$set(this.componentConfig[0].grid, 'dataSource', resultAreaData)
      }
    },

    handleClickCellTitle(e) {
      let { data } = e
      console.log(data)
    }
  },
  created() {
    if (!!this.extList && this.extList.length > 0) {
      this.$set(this.componentConfig[0].grid, 'dataSource', this.extList)
    }
  },
  mounted() {}
}
</script>

<style lang="scss" scoped></style>
<style lang="scss">
.supply-area {
  min-height: 266px;
}
</style>
