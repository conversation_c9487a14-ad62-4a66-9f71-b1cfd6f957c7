<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content addsupplier-dialog">
      <div class="form-box">
        <mt-form ref="addInfoForm" :model="addInfo" :rules="inviteRules" :label-width="900">
          <mt-row>
            <mt-col :span="24">
              <mt-form-item
                prop="supplierEnterpriseId"
                class="form-item positive"
                :label="$t('供应商企业全称')"
              >
                <mt-select
                  v-model="addInfo.supplierEnterpriseId"
                  :value="addInfo.supplierEnterpriseId"
                  :allow-filtering="true"
                  :disabled="true"
                  :filtering="filteringCompany"
                  :data-source="companyArrList"
                  :placeholder="$t('请选择供应商企业全称')"
                  @select="selectCompany"
                  :fields="{ text: 'name', value: 'enterpriseId' }"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>

          <mt-row :gutter="20">
            <mt-col :span="12">
              <mt-form-item prop="contactName" class="form-item" :label="$t('联系人')">
                <mt-input
                  v-model="addInfo.contactName"
                  :disabled="false"
                  :width="400"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入联系人')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item prop="contactMobile" class="form-item" :label="$t('联系方式')">
                <mt-input
                  v-model="addInfo.contactMobile"
                  :disabled="false"
                  :width="400"
                  :show-clear-button="true"
                  type="text"
                  maxlength="11"
                  :placeholder="$t('请输入联系方式')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>

          <mt-row :gutter="20">
            <mt-col :span="12">
              <mt-form-item prop="contactEmail" class="form-item" :label="$t('联系人邮箱')">
                <mt-input
                  v-model="addInfo.contactEmail"
                  :disabled="false"
                  :width="400"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入联系人邮箱')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item
                prop="supplierType"
                class="form-item"
                :label="$t('调查表标识')"
                v-if="claimArr.length > 0"
              >
                <mt-select
                  v-model="addInfo.supplierType"
                  :width="400"
                  :data-source="claimArr"
                  :show-clear-button="true"
                  :placeholder="$t('请输入调查表标识')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>

          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
                <mt-input
                  css-class="e-outline"
                  v-model="addInfo.remark"
                  :disabled="false"
                  :show-clear-button="true"
                  maxlength="200"
                  :multiline="true"
                  :rows="3"
                  type="text"
                  :placeholder="$t('请输入备注')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>

      <div class="table-box">
        <mt-tabs
          :height="34"
          tabs-width="820px"
          :e-tab="false"
          :data-source="tabSource"
          @handleSelectTab="handleSelectTab"
        ></mt-tabs>

        <template v-if="selectIndex === 0">
          <invite-history :supplier-enterprise-id="addInfo.supplierEnterpriseId"></invite-history>
        </template>
        <template v-if="selectIndex === 1">
          <supply-area :ext-list="extList" @pushData="pushData"></supply-area>
        </template>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import inviteHistory from './inviteHistory'
import supplyArea from './supplyArea.vue'
import utils from '@/utils/utils.js'

let filterCompanyTxt = ''
const validateMobile = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入联系方式'))
  } else if (!utils.isMobile(value)) {
    callback(new Error('请输入正确的联系方式'))
  } else {
    callback()
  }
}

const validateMEmail = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入联系人邮箱'))
  } else if (!utils.isEmail(value)) {
    callback(new Error('请输入正确的联系人邮箱'))
  } else {
    callback()
  }
}
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.submitForm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmAndInvite,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并邀请') }
        }
      ],
      addInfo: {
        supplierEnterpriseName: '',
        supplierEnterpriseId: '',
        supplierEnterpriseCode: '',
        supplierTenantId: '',

        contactName: '',
        contactMobile: '',
        contactEmail: '',
        supplierType: '',
        remark: ''
      },
      companyArrList: [],

      claimArr: [],
      selectIndex: 0,
      tabSource: [
        {
          title: this.$t('邀请历史记录')
        },
        {
          title: this.$t('供应范围')
        }
      ],
      mode: '',
      extList: [],
      inviteRules: {
        supplierEnterpriseId: [
          {
            required: true,
            message: this.$t('请输入供应商企业全称'),
            trigger: 'blur'
          }
        ],
        contactName: [
          {
            required: true,
            message: this.$t('请输入联系人'),
            trigger: 'blur'
          },
          { min: 2, max: 5, message: '长度在 2 到 5 个字符', trigger: 'blur' }
        ],
        contactMobile: [{ required: true, validator: validateMobile, trigger: 'blur' }],
        contactEmail: [{ required: true, validator: validateMEmail, trigger: 'blur' }],
        supplierType: [
          {
            required: true,
            message: this.$t('请输入调查表标识'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  components: {
    inviteHistory,
    supplyArea
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    lineDate() {
      return this.modalData.lineDate
    },
    header() {
      return this.modalData.title
    }
  },
  async created() {
    await this.getDictCode()

    if (this.modalData.mode === 'edit') {
      this.addInfo = Object.assign({}, this.addInfo, {
        supplierEnterpriseName: this.lineDate.supplierEnterpriseName,
        supplierEnterpriseId: this.lineDate.supplierEnterpriseId,
        supplierEnterpriseCode: this.lineDate.supplierEnterpriseCode,
        supplierTenantId: this.lineDate.tenantId
      })

      // 预设公司名称
      filterCompanyTxt = this.lineDate.supplierEnterpriseName
      this.getCompanyList()
      this.searchCompay()
      // this.pushData([
      //   {
      //     companyId: this.lineDate.supplierEnterpriseId,
      //     orgCode: this.lineDate.supplierEnterpriseCode,
      //     orgName: this.lineDate.supplierEnterpriseName,
      //     factoryId: "",
      //     factoryName: "",
      //     factoryCode: "",

      //     categoryCode: "",
      //     categoryId: "",
      //     categoryName: "",

      //     relatedType: 0,
      //   },
      // ]);
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 输入的时候
    filteringCompany(e) {
      console.log('filteringCompany', e)
      let { text } = e
      filterCompanyTxt = text
      let getCompanyList = this.getCompanyList
      let result = utils.debounce(getCompanyList, 1000)
      result()
    },

    // 选择公司
    selectCompany(e) {
      let { itemData } = e
      console.log('selectCompany', itemData)
      this.addInfo.supplierEnterpriseId = itemData.enterpriseId
      this.addInfo.supplierEnterpriseName = itemData.name
      this.addInfo.supplierEnterpriseCode = itemData.code
      this.addInfo.supplierTenantId = itemData.id
      if (itemData.enterpriseId) {
        this.searchCompay()
      }
    },

    // 获取平台那边全部公司名称 id code
    getCompanyList() {
      this.$loading()
      this.$API.supplierInvitation
        .getCompanyList({
          name: filterCompanyTxt
        })
        .then((res) => {
          this.$hloading()
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            this.companyArrList = data
          } else {
            this.companyArrList = []
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('获取公司信息失败'),
            type: 'warning'
          })
        })
    },

    // 供应范围回传数据
    pushData(resultAreaData) {
      this.extList = this.renderExtList(resultAreaData)
    },

    renderExtList(resultAreaData) {
      let extList = resultAreaData.map((vc) => {
        let {
          orgId,
          orgCode,
          orgName,
          factoryCode,
          factoryName,
          factoryId,
          categoryCode,
          categoryId,
          categoryName,
          relatedType
        } = vc

        return {
          orgId,
          orgCode,
          orgName,
          factoryCode,
          factoryName,
          factoryId,
          categoryCode,
          categoryId,
          categoryName,
          relatedType
        }
      })

      return extList || []
    },
    // 提交邀请
    submitForm(fn) {
      console.log(this.addInfo)
      let addInfo = this.addInfo
      this.$refs.addInfoForm.validate((valid) => {
        if (valid) {
          let query = {
            contactEmail: addInfo.contactEmail,
            contactMobile: addInfo.contactMobile,
            contactName: addInfo.contactName,
            remark: addInfo.remark,
            supplierEnterpriseId: addInfo.supplierEnterpriseId,
            supplierEnterpriseName: addInfo.supplierEnterpriseName,
            supplierEnterpriseCode: addInfo.supplierEnterpriseCode,
            supplierTenantId: addInfo.supplierTenantId,
            supplierType: addInfo.supplierType,
            extList: this.extList
          }

          if (this.modalData.mode === 'edit') {
            query = {
              ...addInfo,
              ...query
            }
          }

          let url = 'inviteAdd'
          this.$API.supplierInvitation[url](query)
            .then((result) => {
              if (result.code === 200) {
                this.$toast({
                  content: result.msg || this.$t('新增邀请成功'),
                  type: 'success'
                })
                if (typeof fn === 'function') {
                  fn(result.data)
                } else {
                  this.$emit('confirm-function', { mode: 'reload' })
                }
              } else {
                this.$toast({ content: result.msg, type: 'warning' })
              }
            })
            .catch((err) => {
              this.$toast({ content: err.msg, type: 'warning' })
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    confirmAndInvite() {
      this.submitForm((data) => {
        this.$emit('confirm-function', { mode: 'goInvite', data })
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    handleSelectTab(index) {
      this.selectIndex = index
    },

    /**
     * 重置公司信息：电话 联系人 邮箱
     */
    resetCompanyInfo() {
      this.addInfo = Object.assign({}, this.addInfo, {
        contactEmail: '',
        contactMobile: '',
        contactName: '',
        supplierType: !!this.claimArr[0] && !!this.claimArr[0].value ? this.claimArr[0].value : ''
      })
    },
    /**
     *  根据供应商公司名称获取公司信息
     */
    searchCompay() {
      if (!this.addInfo.supplierEnterpriseId) {
        // 重置公司信息
        this.resetCompanyInfo()
        this.$toast({
          content: this.$t('请选择供应商企业全称！'),
          type: 'warning'
        })
        return false
      }

      this.$loading()
      this.$API.supplierInvitation
        .queryContactInfo({
          supplierEnterpriseId: this.addInfo.supplierEnterpriseId || ''
        })
        .then((res) => {
          this.$hloading()
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data) && !utils.isEmpty(data)) {
            // 填充对应的信息
            this.addInfo = Object.assign({}, this.addInfo, data)
          } else {
            // 重置公司信息
            this.resetCompanyInfo()
          }
        })
        .catch((error) => {
          // 重置公司信息
          this.resetCompanyInfo()
          this.$toast({
            content: error.msg || this.$t('获取公司信息失败，请检查企业全称是否正确！'),
            type: 'warning'
          })
        })
    },

    // getDetail(id) {
    //   this.$loading()
    //   this.$API.supplierInvitation['inviteDetail']({"id": id}).then((result) => {
    //     this.$hloading()
    //     if (result.code === 200 && !utils.isEmpty(result.data)) {
    //       let { extList } =  result.data
    //       this.extList = extList
    //     }
    //   }).catch(error=> {
    //     this.$hloading()
    //     this.$toast({
    //         content: error.msg || "获取公司信息失败，请检查企业全称是否正确！",
    //         type: "warning",
    //       });
    //   })
    // },

    getDictCode() {
      this.$API.supplierInvitation
        .getDictCode({
          dictCode: 'formTaskMark'
        })
        .then((res) => {
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            this.claimArr = data.map((v) => {
              return {
                ...v,
                text: v.itemName,
                value: v.itemCode
              }
            })
            this.addInfo.supplierType = this.claimArr[0].value
          } else {
            this.$toast({
              content: this.$t('获取调查表标识信息失败！'),
              type: 'warning'
            })
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 820px;
  margin: 0 auto;
  padding-top: 40px;
  padding-bottom: 20px;
  font-size: 16px;
}
</style>
<style lang="scss">
.addsupplier-dialog {
  .mt-form-item-topLabel {
    .label {
      color: #292929;
      font-weight: 400;
    }
  }

  .positive {
    position: relative;

    .search-company {
      position: absolute;
      width: 48px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      font-size: 14px;
      color: rgba(0, 70, 156, 1);
      right: 20px;
      bottom: 12px;
      transition: all 0.4s ease-in-out;
      cursor: pointer;
    }
    .search-company:hover {
      font-weight: 600;
    }
  }

  .table-box {
    .mt-tabs-container {
      background: transparent !important;
    }
  }
}
</style>
