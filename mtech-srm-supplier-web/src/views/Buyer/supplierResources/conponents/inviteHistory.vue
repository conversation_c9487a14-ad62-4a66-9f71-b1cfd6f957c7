<template>
  <div class="history-box">
    <mt-template-page
      :padding-top="true"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { historyColumnData } from '../config/index'
import utils from '@/utils/utils.js'
export default {
  props: {
    supplierEnterpriseId: {
      type: String,
      default: ''
    }
  },
  watch: {
    supplierEnterpriseId: {
      handler(nv) {
        if (nv) {
          this.getHistoryInvite(nv)
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      componentConfig: [
        {
          gridId: '9835ec0e-31d9-4911-8830-9ad59866240e',
          toolbar: [],
          grid: {
            columnData: historyColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    handleClickCellTool() {},
    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      console.log(e, e.gridRef.getMtechGridRecords())
    },

    handleClickCellTitle() {},

    /**
     * 获取邀请历史
     */
    getHistoryInvite(supplierEnterpriseId) {
      if (!supplierEnterpriseId) {
        this.$set(this.componentConfig[0].grid, 'dataSource', [])
        return false
      }

      this.$API.supplierInvitation
        .listExtHistory({
          supplierEnterpriseId: supplierEnterpriseId || ''
        })
        .then((res) => {
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            this.$set(this.componentConfig[0].grid, 'dataSource', data)
          } else {
            // 空数据
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
<style lang="scss">
.history-box {
  min-height: 266px;
}
</style>
