<template>
  <div class="supplier-resources" ref="scrollbar">
    <!-- 顶部搜索 -->
    <div class="search-wrap">
      <div class="categroy-tab fbox">
        <div
          class="category-item"
          :class="{ active: orgLevelTypeCode === 'ORG02' }"
          @click="selectDataBase('ORG02')"
        >
          {{ $t('公司资源库') }}
        </div>
        <div
          class="category-item"
          :class="{ active: orgLevelTypeCode === 'ORG02-1' }"
          @click="selectDataBase('ORG02-1')"
        >
          {{ $t('事业部资源库') }}
        </div>
        <!-- 板块无数据 后端未开发 disabled-->
        <div
          class="category-item"
          :class="{ active: orgLevelTypeCode === 'ORG01-1' }"
          @click="selectDataBase('ORG01-1')"
        >
          {{ $t('板块资源库') }}
        </div>
        <div
          class="category-item"
          :class="{ active: orgLevelTypeCode === 'ORG01' }"
          @click="selectDataBase('ORG01')"
        >
          {{ $t('集团资源库') }}
        </div>
      </div>
      <!-- 顶部搜索 -->
      <div class="search-box fbox">
        <div class="search-select-wrap-left">
          <mt-select
            class="update-select"
            :width="110"
            css-class="search-select"
            :data-source="selectTypeArray"
            v-model="typeValue"
            disabled
            @change="changeSelectType"
            :show-clear-button="false"
            :placeholder="$t('请选择区域')"
          ></mt-select>
        </div>
        <div class="search-input-wrap flex1">
          <mt-input
            type="text"
            :width="470"
            css-class="search-input"
            @input="changeInput"
            :placeholder="placeholder"
          ></mt-input>
        </div>
        <div class="search-select-wrap">
          <template v-if="!refresh">
            <mt-select
              :width="210"
              css-class="search-select"
              :data-source="relationList"
              v-model="selectedOrgObj.id"
              @change="changeSelect"
              :show-clear-button="false"
              :placeholder="relationPlaceHolder"
            ></mt-select>
          </template>
        </div>

        <div class="data-dashboard" @click="jumpDashboard">{{ $t('供应商数据看板') }}</div>
      </div>

      <div class="more-change" @click="jumpSource">
        {{ $t('寻求更多商机？前往平台资源库') }}
      </div>
    </div>

    <div class="mian-section">
      <div class="section-active">
        <div v-show="isShow" @click="isShow = false">
          {{ $t('收起')
          }}<span class="active-icons"
            ><mt-icon name="icon_input_arrow" style="transform: rotate(180deg)"
          /></span>
        </div>
        <div v-show="!isShow" @click="isShow = true">
          {{ $t('展开') }}<span><mt-icon name="icon_input_arrow" /></span>
        </div>
      </div>
      <div class="query-box" v-show="isShow">
        <!-- 单行start -->
        <template v-for="(item, index) in filterLineData">
          <div class="categroy-line fbox" :key="item.name">
            <div class="categroy-name">{{ item.name }}：</div>
            <div
              class="categroy-list fbox flex1"
              :class="{
                isEplps: overEplispsArray[index] && !expandLineArray[index]
              }"
              @click="selectFilters"
            >
              <!-- 冒泡绑定点击事件 -->
              <div
                class="expand-icon"
                v-if="overEplispsArray[index]"
                :class="{ rotate: expandLineArray[index] }"
                @click="expandLine(index)"
              >
                <mt-icon name="icon_input_arrow" />
              </div>
              <!-- <template v-if="item.name === '服务区域'">
                <div
                  class="categroy-item"
                  v-for="child in item.value"
                  :key="child.id"
                  :class="{ active: isInArray(child) }"
                  data-dictname="服务区域"
                  :data-itemcode="child.areaCode"
                  :data-name="child.simpleName"
                >
                  {{ child.simpleName }}
                </div>
              </template>
              <template v-else> -->
              <div
                class="categroy-item"
                v-for="child in item.value"
                :key="child.id"
                :class="{ active: isInArray(child) }"
                :data-dictname="child.dictName"
                :data-itemcode="child.itemcode"
                :data-id="child.id"
                :data-name="child.name"
              >
                {{ child.name }}
              </div>
              <!-- </template> -->
            </div>
          </div>
        </template>
        <!-- 单行end -->
      </div>

      <!-- 筛选的展示行 -->
      <div class="filter-wrap fbox" v-show="isShow">
        <!-- 左侧的筛选项目 -->
        <div class="filter-box flex1 fbox">
          <template v-if="filterSelectArray.length > 0"></template>
          <div class="filter-item fbox" v-for="item in filterSelectArray" :key="item.name">
            <div class="filter-txt flex1">{{ item.name }}</div>
            <div class="filter-icon" @click="clearSelect(item)">
              <mt-icon name="icon_input_clear" />
            </div>
          </div>

          <div class="clear-filter" v-if="filterSelectArray.length > 0" @click="clearAllSelect">
            {{ $t('清除') }}
          </div>
        </div>
      </div>

      <!-- 筛选行 -->
      <div class="query-builder fbox">
        <!-- 左侧的筛选 -->
        <div class="filter-left fbox">
          <div
            class="filter-item fbox"
            :class="{ active: filterLeftIndex === 0 && isDownSort !== '-1' }"
            @click="selectLeftFilter(0)"
          >
            <div class="filter-text">{{ $t('默认') }}</div>
            <div
              class="filter-icon"
              :class="{
                'down-sort': isDownSort === '1' && filterLeftIndex === 0,
                'up-sort': isDownSort === '0' && filterLeftIndex === 0
              }"
            >
              <mt-icon name="icon_card_sortArrow" />
            </div>
          </div>

          <div
            class="filter-item fbox"
            :class="{ active: filterLeftIndex === 1 && isDownSort !== '-1' }"
            @click="selectLeftFilter(1)"
          >
            <div class="filter-text">{{ $t('合作次数') }}</div>
            <div
              class="filter-icon"
              :class="{
                'down-sort': isDownSort === '1' && filterLeftIndex === 1,
                'up-sort': isDownSort === '0' && filterLeftIndex === 1
              }"
            >
              <mt-icon name="icon_card_sortArrow" />
            </div>
          </div>

          <div
            class="filter-item fbox"
            :class="{ active: filterLeftIndex === 2 && isDownSort !== '-1' }"
            @click="selectLeftFilter(2)"
          >
            <div class="filter-text">{{ $t('合作金额') }}</div>
            <div
              class="filter-icon"
              :class="{
                'down-sort': isDownSort === '1' && filterLeftIndex === 2,
                'up-sort': isDownSort === '0' && filterLeftIndex === 2
              }"
            >
              <mt-icon name="icon_card_sortArrow" />
            </div>
          </div>

          <div
            class="filter-item fbox"
            :class="{ active: filterLeftIndex === 3 && isDownSort !== '-1' }"
            @click="selectLeftFilter(3)"
          >
            <div class="filter-text">{{ $t('注册资金') }}</div>
            <div
              class="filter-icon"
              :class="{
                'down-sort': isDownSort === '1' && filterLeftIndex === 3,
                'up-sort': isDownSort === '0' && filterLeftIndex === 3
              }"
            >
              <mt-icon name="icon_card_sortArrow" />
            </div>
          </div>

          <div
            class="filter-item fbox"
            :class="{ active: filterLeftIndex === 4 && isDownSort !== '-1' }"
            @click="selectLeftFilter(4)"
          >
            <div class="filter-text">{{ $t('入库时间') }}</div>
            <div
              class="filter-icon"
              :class="{
                'down-sort': isDownSort === '1' && filterLeftIndex === 4,
                'up-sort': isDownSort === '0' && filterLeftIndex === 4
              }"
            >
              <mt-icon name="icon_card_sortArrow" />
            </div>
          </div>
        </div>

        <!-- 右侧的筛选 -->
        <div class="filter-right flex1 fbox">
          <div class="operator-box fbox" v-if="!!searchList && searchList.length >= 0">
            {{ $t('共查询到') }} {{ searchList.length }} {{ $t('家相关企业') }}
          </div>
          <div class="style-box fbox">
            <div
              class="style-item"
              :class="{ active: filterRightIndex === 0 }"
              @click="selectRightFilter(0)"
            >
              <mt-icon name="icon_card_view_list_inactive" />
            </div>
            <div
              class="style-item"
              :class="{ active: filterRightIndex === 1 }"
              @click="selectRightFilter(1)"
            >
              <mt-icon name="icon_card_view_grid_active" />
            </div>
            <!-- <div class="style-item" :class="{'active': filterRightIndex === 2}" @click="selectFilter(2)"><mt-icon name="icon_solid_Filter"/></div> -->
          </div>
        </div>
      </div>

      <div class="operator-line fbox" v-if="!!searchList && searchList.length > 0">
        <!-- 假数据 -->
        <div class="has-select flex1 fbox">
          {{ $t('已选择') }}
          <span>{{ checkedList.length }}</span>
          {{ $t('项') }}
        </div>
        <!--
          公司：共享（升级）、分级、惩罚、标签
          板块/事业部：共享（升级）、引入、分级、标签、下架
          集团：优推派发、引入、下架、分级、标签

          公司：ORG02
          事业群 ORG02-1
          板块 ORG01-1
          集团：ORG01
        -->
        <div class="operator-box flex1 fbox">
          <!-- <template v-if="orgLevelTypeCode === 'ORG01'">
            <div class="operator-item fbox" @click="popShareDialog">
              <div class="op-icon"><mt-icon name="icon_solid_pushorder" /></div>
              <div class="op-txt">优推派发</div>
            </div>
          </template> -->
          <!-- 引入放底下 只能单个引入 -->
          <!-- <template v-if=" orgLevelTypeCode==='ORG02-1' || orgLevelTypeCode==='ORG01-1' || orgLevelTypeCode==='ORG01'">
              <div class="operator-item fbox" @click="popShareDialog">
                <div class="op-icon"><mt-icon name="icon_card_invite"/></div>
                <div class="op-txt">引入</div>
              </div>
            </template> -->
          <template
            v-if="
              orgLevelTypeCode === 'ORG02' ||
              orgLevelTypeCode === 'ORG02-1' ||
              orgLevelTypeCode === 'ORG01-1'
            "
          >
            <div class="operator-item fbox" @click="popShareDialog('up')">
              <div class="op-icon"><mt-icon name="icon_table_share" /></div>
              <div class="op-txt">{{ $t('共享') }}</div>
            </div>
          </template>
          <template
            v-if="
              orgLevelTypeCode === 'ORG02' ||
              orgLevelTypeCode === 'ORG02-1' ||
              orgLevelTypeCode === 'ORG01-1' ||
              orgLevelTypeCode === 'ORG01'
            "
          >
            <div class="operator-item fbox" @click="popClassification">
              <div class="op-icon"><mt-icon name="icon_table_setLevel" /></div>
              <div class="op-txt">{{ $t('分级') }}</div>
            </div>
          </template>
          <template v-if="orgLevelTypeCode === 'ORG02'">
            <div class="operator-item fbox" @click="popPunishDialog">
              <div class="op-icon"><mt-icon name="icon_table_punish" /></div>
              <div class="op-txt">{{ $t('惩罚') }}</div>
            </div>
          </template>
          <!-- <template
            v-if="
              orgLevelTypeCode === 'ORG02' ||
              orgLevelTypeCode === 'ORG02-1' ||
              orgLevelTypeCode === 'ORG01-1' ||
              orgLevelTypeCode === 'ORG01'
            "
          >
            <div class="operator-item fbox" @click="popEliminate">
              <div class="op-icon"><mt-icon name="icon_card_label" /></div>
              <div class="op-txt">标签</div>
            </div>
          </template> -->
          <template
            v-if="
              orgLevelTypeCode === 'ORG02-1' ||
              orgLevelTypeCode === 'ORG01-1' ||
              orgLevelTypeCode === 'ORG01'
            "
          >
            <div class="operator-item fbox" @click="popShareDownDialog">
              <div class="op-icon"><mt-icon name="icon_solid_demotion" /></div>
              <div class="op-txt">{{ $t('下架') }}</div>
            </div>
          </template>
        </div>
      </div>

      <!-- 底部展示的box  with-filter-display-box 块状展示-->
      <div class="display-box fbox with-filter-display-box" v-show="filterRightIndex === 1">
        <box-item
          :search-list="searchList"
          :org-level-type-code="orgLevelTypeCode"
          @setCheckList="setCheckList"
          @popShareDialog="emitPopShareDialog"
        ></box-item>
      </div>

      <!-- 产品展示 横状展示 -->
      <div class="display-line" v-show="filterRightIndex === 0">
        <line-item
          :search-list="searchList"
          :org-level-type-code="orgLevelTypeCode"
          @setCheckList="setCheckList"
          @popShareDialog="emitPopShareDialog"
        ></line-item>
      </div>

      <div class="empty-box" v-if="searchList.length === 0 || !searchList">
        <empty-data :msg="emptyMsg"></empty-data>
      </div>

      <!-- <div class="page-wrap">
        <mt-page
          :page-settings="pageSettings"
          :total-pages="totalPages"
          @currentChange="goToPage"
          @sizeChange="changePageSize"
        ></mt-page>
      </div> -->
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import boxItem from './conponents/boxItem.vue'
import lineItem from './conponents/lineItem.vue'
import emptyData from '@/components/emptyData/emptyData.vue'
import axios from 'axios'
export default {
  name: 'SupplierResources',
  components: {
    boxItem,
    lineItem,
    emptyData
  },
  data() {
    return {
      placeholder: this.$t('搜索供应商'),
      searchValue: '',

      // 左侧写死的类型 目前只能选择供应商 品类暂时不做 置灰
      selectTypeArray: [
        { text: this.$t('供应商'), value: 0 },
        { text: this.$t('品类'), value: 1, disabled: true }
      ],
      typeValue: 0,

      relationPlaceHolder: this.$t('请选择公司'),
      // 资源库类型
      orgLevelTypeCode: 'ORG02',
      relationList: [], // 顶部接口返回的公司集团等列表
      selectedOrgObj: {},

      filterSelectArray: [],

      filterLineData: [],

      // 左侧 筛选已选id
      filterLeftIndex: -1,
      // 是否是 降序 -1: 未选 0 升序 1 降序
      isDownSort: '-1',
      // 右侧 筛选已选id
      filterRightIndex: 0,
      // 已勾选的栏目
      checkedList: [],

      overEplispsArray: [],
      expandLineArray: [],
      // 接口返回的数据数量
      searchList: [],

      pageParams: {
        condition: '',
        page: {
          current: 1,
          size: 10
        }
      },
      pageSettings: { pageSize: 10, pageCount: 10, pageSizes: [10, 15, 20] },
      totalPages: 0,
      emptyMsg: this.$t('暂无数据'),

      refresh: false,
      isShow: true,
      timeLoadMore: false,
      pageNum: 0
    }
  },
  mounted() {
    this.initConpanyData() // 获取公司事业群板块集团的接口数据
    this.getDict() // 获取筛选行的 字典接口
    // 鼠标滚动分页
    if (this.$router.path == '/supplier/resources') {
      window.addEventListener('scroll', this.handleScroll, true)
    } else {
      window.removeEventListener('scroll', this.handleScroll, true)
    }
  },
  methods: {
    handleScroll(e) {
      let that = this
      if (that.timeLoadMore) {
        clearTimeout(that.timeLoadMore)
      }
      that.timeLoadMore = setTimeout(() => {
        let sh = that.$refs.scrollbar ? that.$refs.scrollbar.scrollHeight : 0
        let st = e.target.defaultView ? e.target.defaultView.pageYOffset : 0
        let ch = e.target.body ? e.target.body.clientHeight : 0
        if (st + ch >= sh) {
          // 到底了：查询下一页
          if (that.totalPages > 0 && that.pageNum < that.totalPages) {
            that.pageParams.page.current++
            that.getListData()
          }
        }
      }, 300)
    },
    // 公司：ORG02
    // 事业群 ORG02-1
    // 板块 ORG01-1
    // 集团：ORG01
    selectDataBase(id) {
      this.orgLevelTypeCode = id
      this.initConpanyData()
      this.relationPlaceHolder =
        id === 'ORG02'
          ? this.$t('请选择公司')
          : id === 'ORG02-1'
          ? this.$t('请选择事业部')
          : id === 'ORG01-1'
          ? this.$t('请选择板块')
          : id === 'ORG01-1'
          ? this.$t('请选择集团')
          : this.$t('请选择')
    },

    // 根据树
    async initConpanyData() {
      this.$loading()
      // 默认获取公司树
      let relationList = await this.getRelationList()
      if (relationList.length === 0) {
        this.searchList = []
        return
      }
      // 默认选择第一个
      this.refresh = true
      this.relationList = relationList.map((v) => ({
        ...v,
        text: v.orgName,
        value: v.id
      }))
      this.selectedOrgObj = this.relationList[0] ? this.relationList[0] : {}
      this.$nextTick(() => {
        this.refresh = false
      })
      // 空的话 触发 ，非空的话 走changeselect事件触发
      this.getListData() // 获取列表数据
    },

    /*
      获取公司关系数据
      orgLevelTypeCode
      公司：ORG02
      事业群 ORG02-1
      板块  ORG01-1
      集团：ORG01
    */
    getRelationList() {
      let orgLevelTypeCode = this.orgLevelTypeCode
      return this.$API.supplierResources['getRelations']({
        orgLevelTypeCode
      })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200 && !utils.isEmpty(data)) {
            return data
          } else {
            return []
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取数据失败！'),
            type: 'warning'
          })
          return []
        })
    },

    getDict() {
      // 字典需要自己配置
      // ProductService 产品服务
      let AJAXProductService = this.$API.supplierResources['getDictItem']({
        dictCode: 'ProductService'
      })
      // EnterpriseLabel 企业标签
      // let AJAXEnterpriseLabel = this.$API.supplierResources["getDictItem"]({
      //   dictCode: "EnterpriseLabel",
      // });
      let AJAXEnterpriseLabel = this.$API.supplierResources['getDefineList']({
        labelDefineType: 3,
        labelName: ''
      })
      // 分级
      let AJAXLevelSet = this.$API.supplierResources['getDefineList']({
        labelDefineType: 1,
        labelName: ''
      })
      // EnterpriseType 企业类型
      let AJAXEnterpriseType = this.$API.supplierResources['getDictItem']({
        dictCode: 'EnterpriseType'
      })
      // 获取省市信息
      let AJAXGetArea = this.$API.supplierResources['selectByParentCode']({
        parentCode: ''
      })

      Promise.allSettled([
        AJAXProductService,
        AJAXEnterpriseLabel,
        AJAXEnterpriseType,
        AJAXGetArea,
        AJAXLevelSet
      ]).then((result) => {
        let filterLineData = []
        for (let i = 0; i < result.length; i++) {
          let dataItem = result[i]
          if (
            dataItem.status === 'fulfilled' &&
            !utils.isEmpty(dataItem.value) &&
            dataItem.value.code === 200 &&
            !utils.isEmpty(dataItem.value.data)
          ) {
            let name = ''
            let value = []
            if (i === 3) {
              name = this.$t('服务区域')
              value = dataItem.value.data.map((v) => {
                return {
                  ...v,
                  itemcode: v.areaCode,
                  dictName: name,
                  id: v.id,
                  name: v.simpleName
                }
              })
            } else if (i === 1) {
              name = this.$t('企业标签')
              value = dataItem.value.data.map((v) => {
                return {
                  ...v,
                  itemcode: v.labelCode,
                  dictName: name,
                  id: v.id,
                  name: v.labelName
                }
              })
            } else if (i === 4) {
              name = this.$t('级别')
              value = dataItem.value.data.map((v) => {
                return {
                  ...v,
                  itemcode: v.labelCode,
                  dictName: name,
                  id: v.id,
                  name: v.labelName
                }
              })
            } else {
              name = dataItem.value.data[0].dictName
              value = value = dataItem.value.data.map((v) => {
                return {
                  ...v,
                  itemcode: v.itemCode
                }
              })
            }

            filterLineData.push({
              name,
              value: value
            })
          } else {
            continue
          }
        }
        console.log(filterLineData)
        this.filterLineData = filterLineData
        // 根据渲染长度 展示下拉展开箭头
        this.$nextTick(() => {
          this.overEplips()
        })
      })
    },

    // 获取列表数据
    getListData() {
      // 重置选择
      this.resetSearch()

      let filterSelectArray = this.filterSelectArray
      // categoryIds 对应产品服务 分类用ID  剩下得用itemCode
      // labelCodes 对应企业标签
      // industryCodes 所属行业
      // regionCodes 区域
      // supplierGradeIds 级别
      let termVos = {
        categoryIds: [],
        labelCodes: [],
        industryCodes: [],
        regionCodes: [],
        supplierGradeIds: []
      }
      filterSelectArray.forEach((item) => {
        if (item.dictname === this.$t('服务区域')) {
          termVos.regionCodes.push(item.itemcode)
        }
        if (item.dictname === this.$t('产品服务')) {
          termVos.categoryIds.push(item.id)
        }
        if (item.dictname === this.$t('企业标签')) {
          termVos.labelCodes.push(item.itemcode)
        }
        if (item.dictname === this.$t('所属行业')) {
          termVos.industryCodes.push(item.itemcode)
        }
        if (item.dictname === this.$t('级别')) {
          termVos.supplierGradeIds.push(item.itemcode)
        }
      })

      let query = {
        pageNum: this.pageParams.page.current,
        pageSize: this.pageParams.page.size,
        keyword: this.searchValue,
        buyerOrgCode: this.selectedOrgObj.orgCode,
        ...termVos
      }
      if (this.filterLeftIndex !== -1) {
        query.orderField = this.filterLeftIndex === 0 ? '' : this.filterLeftIndex + '' // 1，2，3，4 合作次数、合作金额、注册资金、入库时间 不传值 就是默认
        query.orderType = this.isDownSort !== '-1' ? (this.isDownSort === '0' ? 'asc' : 'desc') : ''
      }
      this.$loading()
      axios
        .post('/api/search-service/supplier/base/queryPage', query)
        .then((res) => {
          this.$hloading()
          let { data } = res
          if (
            data.code === '200' &&
            !!data.data &&
            !!data.data.result &&
            !utils.isEmpty(data.data.result)
          ) {
            let { result, totalCount } = data.data
            this.searchList = utils.isEmpty(result) ? [] : result

            // 分页相关
            this.pageNum += result.length
            this.totalPages = totalCount

            // (this.pageSettings = {
            //   pageSize,
            //   pageCount: totalPageCount,
            //   pageSizes: [10, 15, 20],
            // }),
            //   (this.totalPages = totalPageCount);
            // this.$set(this.pageParams, "page", {
            //   current: pageNum,
            //   size: pageSize,
            // });
          } else {
            this.searchList = []
            this.pageNum = 0
            this.totalPages = 0
          }
        })
        .catch((error) => {
          this.$hloading()
          this.emptyMsg = error.msg
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
        })
    },

    goToPage(num) {
      this.$set(this.pageParams.page, 'current', num)

      // 请求列表数据
      this.getListData()
    },
    changePageSize(size) {
      this.$set(this.pageParams.page, 'size', size)

      // 请求列表数据
      this.getListData()
    },

    // 品类选择
    selectFilters(event) {
      let dataset = !!event.target && !!event.target.dataset ? event.target.dataset : {}
      if (utils.isEmpty(dataset) || !dataset.itemcode) {
        // this.$toast({
        //   content: "获取筛选值失败！",
        //   type: "warning",
        // });
        return
      }
      // 双击取消
      if (this.filterSelectArray.filter((v) => v.itemcode === dataset.itemcode).length > 0) {
        let item = this.filterSelectArray.filter((v) => v.itemcode === dataset.itemcode)[0]
        this.clearSelect(item)
        return
      }
      // 添加
      this.filterSelectArray.push({
        itemcode: dataset.itemcode,
        dictname: dataset.dictname,
        name: dataset.name,
        id: dataset.id
      })
      /**
       * @todo 防抖去请求列表数据
       */

      // 请求列表数据
      let getListData = this.getListData
      let request = utils.debounce(getListData, 600)
      request()
    },

    isInArray(item) {
      if (this.filterSelectArray.filter((v) => v.itemcode === item.itemcode).length > 0) {
        return true
      } else {
        return false
      }
    },

    // 删除单个筛选
    clearSelect(item) {
      let index = this.filterSelectArray.findIndex((child) => child.itemcode === item.itemcode)
      this.filterSelectArray.splice(index, 1)

      // 请求列表数据
      this.getListData()
    },

    // 清空筛选
    clearAllSelect() {
      this.filterSelectArray = []

      // 请求列表数据
      this.getListData()
    },

    // 跳转供应商大屏
    jumpDashboard() {
      if (!this.selectedOrgObj.id) {
        this.$toast({ content: this.$t('请选择一个组织机构'), type: 'warning' })
        return
      }
      this.$router.push({
        path: 'dataScreen',
        query: {
          organizationid: this.selectedOrgObj.id
        }
      })
    },

    // 跳转平台资源库
    jumpSource() {
      return
    },

    // 品类超出一行的下拉箭头展示
    overEplips() {
      let domItem = Array.from(document.querySelectorAll('.categroy-list'))
      let overEplispsArray = []
      let expandLineArray = []
      domItem.forEach((item) => {
        expandLineArray.push(false)
        if (item.clientHeight > 30) {
          overEplispsArray.push(true)
        } else {
          overEplispsArray.push(false)
        }
      })

      this.overEplispsArray = overEplispsArray
      this.expandLineArray = expandLineArray
    },

    expandLine(index) {
      let expandLineItem = !this.expandLineArray[index]
      this.$set(this.expandLineArray, index, expandLineItem)
      // console.log(this.expandLineArray);
    },

    changeInput(e) {
      console.log(e)
      this.searchValue = e
      // 防抖处理搜索  // 请求列表数据
      let getListData = this.getListData
      let request = utils.debounce(getListData, 1000)
      request()
    },

    changeSelectType(e) {
      console.log(e)
    },

    // 选择公司集团等信息
    changeSelect(e) {
      console.log(e)
      let { itemData } = e
      if (!utils.isEmpty(itemData)) {
        this.selectedOrgObj = this.relationList.filter((item) => item.id === itemData.id)[0]
      }

      // 请求列表数据
      this.getListData()
    },

    resetSearch() {
      // 已选值空
      this.checkedList = []
      // 已搜索值空
      this.searchList = []
    },

    // 切换平台
    changePlateForm(e) {
      console.log(e, itemData)
      let { itemData } = e
      if (!!itemData && !utils.isEmpty(itemData)) {
        this.plateFormResult = itemData.value
      } else {
        this.$toast({ content: this.$t('请选择平台'), type: 'warning' })
      }
    },

    selectPlateFormPop() {
      this.showPlateFormDrop = !this.showPlateFormDrop
    },

    selectPlateForm(item) {
      this.plateFormResult = item
      this.selectPlateFormPop()
    },

    // 左侧筛选的id
    // 0 - up / 1 - down / -1 无状态
    selectLeftFilter(id) {
      this.filterLeftIndex = id
      switch (this.isDownSort) {
        case '-1':
          this.isDownSort = '1'
          break
        case '0':
          this.isDownSort = '-1'
          break
        case '1':
          this.isDownSort = '0'
          break
      }

      // 请求列表数据
      this.getListData()
    },

    // 右筛选的id
    selectRightFilter(id) {
      this.filterRightIndex = id
    },

    // 展开右侧的筛选 （暂时不搞）
    selectFilter() {
      this.$dialog({
        modal: () => import('./conponents/sideBar'),
        data: {
          isShow: true
        },
        success: () => {},
        close: () => {}
      })
    },

    handleChange(e) {
      console.log(e)
    },
    // 新增引入
    popyinruDialog() {
      if (this.checkedList.length) {
        const _this = this
        this.$dialog({
          modal: () => import('./conponents/addyinru.vue'),
          data: {
            title: this.$t('新增引入')
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          },
          close: () => {}
        })
      } else {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      }
    },
    // item 上的共享
    emitPopShareDialog(id) {
      let selectItem = this.searchList.filter((item) => item.id === id)[0]
      this.$dialog({
        modal: () => import('./conponents/addShareDialog'),
        data: {
          title: this.$t('新增共享申请单'),
          info: [selectItem],
          methodInfo: 'up'
        },
        success: () => {
          setTimeout(() => {
            this.$router.push({
              name: 'supplierpunishment'
            })
          }, 600)
        },
        close: () => {}
      })
    },
    // 新增共享单
    popShareDialog(method = 'up') {
      if (this.checkedList.length > 0) {
        this.$dialog({
          modal: () => import('./conponents/addShareDialog'),
          data: {
            title: this.$t('新增共享申请单'),
            info: this.checkedList,
            methodInfo: method
          },
          success: () => {
            setTimeout(() => {
              this.$router.push({
                name: 'supplierpunishment'
              })
            }, 600)
          },
          close: () => {}
        })
      } else {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      }
    },
    // 共享下架
    popShareDownDialog() {
      this.popShareDialog('down')
    },

    // 新增分级
    popClassification() {
      if (this.checkedList.length) {
        this.$dialog({
          modal: () => import('./conponents/addFenDialog'),
          data: {
            title: this.$t('新增分级单'),
            info: this.checkedList
          },
          success: (data) => {
            if (data.type === 'jump') {
              this.$router.push({
                name: 'supplierpunishment',
                query: {
                  tabId: 1
                }
              })
            }
          },
          close: () => {}
        })
      } else {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      }
    },
    // 新增惩罚单
    popPunishDialog() {
      if (this.checkedList.length) {
        this.$dialog({
          modal: () => import('./conponents/addPunishDialog'),
          data: {
            title: this.$t('新增惩罚单'),
            info: this.checkedList
          },
          success: (data) => {
            if (data.type === 'jump') {
              this.$router.push({
                name: 'supplierpunishment',
                query: {
                  tabId: 2
                }
              })
            }
          },
          close: () => {}
        })
      } else {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      }
    },
    popEliminate() {
      if (this.checkedList.length) {
        this.$dialog({
          modal: () => import('./conponents/addhtmlform.vue'),
          data: {
            title: this.$t('新增惩罚单')
          },
          success: () => {},
          close: () => {}
        })
      } else {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      }
    },

    // 回调勾选的栏目
    setCheckList(CheckList) {
      this.checkedList = CheckList
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll, true)
  }
}
</script>

<style lang="scss">
.fbox {
  display: flex;
  align-items: center;
}

.flex1 {
  flex: 1;
}

.empty-box {
  position: relative;
  width: 100%;
  min-height: 500px;
}

.supplier-resources {
  min-width: 1200px;
  .search-wrap {
    height: 140px;
    background: url(../../../assets/resources/<EMAIL>) 100% 100% no-repeat;
    background-size: 100% 100%;
    padding: 20px 0;

    .categroy-tab {
      width: 100%;
      height: 14px;
      font-size: 14px;
      justify-content: center;

      .category-item {
        cursor: pointer;
        padding: 0 20px;
        color: #292929;
        border-right: 1px solid #9a9a9a;
      }

      .category-item:nth-child(1) {
        border-left: 1px solid #9a9a9a;
      }

      .category-item:hover,
      .active {
        font-weight: 600;
        color: #00469c;
      }
      .disabled {
        font-weight: normal !important;
        color: #9a9a9a !important;
        cursor: not-allowed !important;
      }
    }
  }

  .search-box {
    width: 800px;
    height: 50px;
    background: rgba(250, 250, 250, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    margin: 0 auto;
    position: relative;
    margin-top: 10px;

    .data-dashboard {
      cursor: pointer;
      position: absolute;
      left: 100%;
      padding-left: 20px;
      height: 50px;
      line-height: 50px;
      font-size: 14px;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      white-space: nowrap;
      user-select: none;
    }

    .data-dashboard:hover {
      font-weight: 600;
    }

    .search-select-wrap-left {
      width: 110px;
      height: 50px;
      line-height: 50px;
      box-sizing: border-box;
      position: relative;
      color: #292929;

      &::after {
        content: ' ';
        display: inline-block;
        width: 1px;
        height: 30px;
        background: #e8e8e8;
        position: absolute;
        right: 0;
        top: 10px;
      }

      .select-container {
        height: 50px;
        line-height: 50px;
        box-sizing: border-box;
      }

      .search-select {
        width: 100%;
        height: 50px;
        line-height: 50px;
        box-sizing: border-box;
        border-bottom: none;
        padding-right: 20px;
        padding-left: 20px;
        color: #292929;

        input {
          width: 100%;
          height: 0;
          box-sizing: border-box;
          border-bottom: none;
          color: #292929 !important;
          padding-top: 20px;
        }
      }
    }

    .search-input-wrap {
      height: 50px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 1px;
        height: 30px;
        position: absolute;
        top: 10px;
        left: 590px;
        background: rgba(232, 232, 232, 1);
      }

      .search-input {
        width: 100%;
        height: 50px;
        border-bottom: none;
        padding-left: 20px;
        input {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          border-bottom: none;
        }
      }
    }

    .search-select-wrap {
      width: 210px;
      height: 50px;
      line-height: 50px;
      box-sizing: border-box;
      color: #292929;

      .select-container {
        height: 50px;
        line-height: 50px;
        box-sizing: border-box;
      }

      .search-select {
        width: 100%;
        height: 50px;
        line-height: 50px;
        box-sizing: border-box;
        border-bottom: none;
        padding-right: 20px;
        padding-left: 20px;
        color: #292929;

        input {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          border-bottom: none;
          color: #292929;
        }
      }
    }
  }

  .more-change {
    // height: 30px;
    line-height: 14px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
    margin-top: 10px;
    text-align: center;
    cursor: pointer;
  }

  .page-wrap {
    display: flex;
    justify-content: center;

    // .mt-pagertemplate {
    //   display: inline-flex!important;
    //   justify-content: center!important;
    // }
  }
}
</style>
<style lang="scss" scoped>
.supplier-resources {
  height: 100%;
  margin: 20px 0;
  position: relative;

  .search-box {
    width: 800px;
    height: 50px;
    background: rgba(250, 250, 250, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    margin: 0 auto;
    position: relative;
    margin-top: 10px;
  }

  .mian-section {
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 8px 0 0;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    margin-top: 20px;
    padding: 16px 20px;
    .section-active {
      height: 20px;
      line-height: 20px;
      text-align: right;
      margin-left: 20px;
      color: #00469c;
      font-size: 14px;
      cursor: pointer;
      .active-icons {
        display: inline-block;
        transform: rotate(180deg);
      }
      span {
        margin-left: 5px;
      }
    }
  }

  .query-box {
    .categroy-line {
      padding: 15px 0;
      border-bottom: 1px solid #e8e8e8;
      align-items: flex-start;

      .categroy-name {
        width: 145px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }

      .categroy-list {
        font-size: 14px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        flex-wrap: wrap;
        line-height: 20px;
        padding-right: 22px;
        position: relative;
        transition: all 0.2s linear;

        .expand-icon {
          position: absolute;
          width: 12px;
          height: 12px;
          line-height: 12px;
          right: 10px;
          top: 1px;
          font-size: 12px;
          color: #9baac1;
          cursor: pointer;
          transition: all 0.2s linear;
        }

        .rotate {
          transform: rotate(180deg);
        }

        .categroy-item {
          height: 22px;
          line-height: 22px;
          margin-right: 36px;
          padding: 0 4px;
          margin-bottom: 5px;
          cursor: pointer;
          user-select: none;
        }

        .categroy-item:hover {
          color: rgba(99, 134, 193, 1);
        }

        .active {
          height: 14px;
          line-height: 14px;
          font-size: 14px;
          padding: 0 4px;
          background: rgba(99, 134, 193, 0.1);
          font-weight: 500;
          color: rgba(99, 134, 193, 1);
        }
      }

      .isEplps {
        height: 20px;
        overflow: hidden;
      }
    }
  }

  .query-builder {
    margin-top: 15px;
    height: 34px;
    height: 34px;
    justify-content: space-between;

    .operator-box {
      font-size: 14px;
      justify-content: flex-end;
      font-weight: normal;
      color: #00469c;
      padding-right: 10px;

      .operator-item {
        margin-right: 20px;
        cursor: pointer;
        white-space: nowrap;

        .op-icon {
          display: flex;
          align-items: center;
          margin-right: 8px;
          position: relative;
          top: 2px;
        }

        i {
          display: inline-block;
          width: 14px;
          height: 16px;
          font-size: 14px;
        }
      }
    }

    .filter-left {
      height: 34px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 4px;
      font-size: 14px;

      font-weight: normal;
      color: rgba(154, 154, 154, 1);
      overflow: hidden;

      .filter-item {
        height: 34px;
        line-height: 34px;
        border-right: 1px solid rgba(232, 232, 232, 1);
        padding: 0 20px;
        cursor: pointer;
        white-space: nowrap;

        .filter-icon {
          margin-left: 4px;
        }

        .up-sort {
          transform: rotate(180deg);
        }

        i {
          font-size: 8px;
          line-height: 34px;
          color: #9a9a9a;
          transform: scale(0.7);
          display: inline-block;
          width: 12px;
          height: 12px;
          font-weight: 600;
          position: relative;
          top: 3px;
        }
      }

      .filter-item:last-child {
        border-right: none !important;
      }

      .active {
        background: #00469c;
        color: #fff;

        i {
          color: #fff;
        }
      }
    }

    .filter-right {
      justify-content: flex-end;
      height: 40px;
      line-height: 40px;

      .style-box {
        height: 34px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        overflow: hidden;

        .style-item {
          cursor: pointer;
          overflow: hidden;
          padding: 0 20px;
          color: #9baac1;
          border-right: 1px solid rgba(232, 232, 232, 1);

          i {
            font-size: 18px;
            display: inline-block;
            width: 20px;
            height: 20px;
            vertical-align: middle;
          }
        }

        .style-item:last-child {
          border-right: none !important;
        }

        .active {
          color: #00469c;
        }
      }
    }
  }

  .operator-line {
    margin-top: 15px;
    height: 24px;
    line-height: 24px;

    .has-select {
      font-size: 14px;
      color: #292929;

      span {
        display: inline-block;
        margin: 0 10px;
        height: 24px;
        line-height: 24px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 2px;
        padding: 0 20px;
        font-size: 14px;
        font-weight: bold;
        color: rgba(99, 134, 193, 1);
      }
    }

    .operator-box {
      font-size: 14px;
      justify-content: flex-end;
      font-weight: normal;
      color: #00469c;
      padding-right: 10px;

      .operator-item {
        margin-right: 20px;
        cursor: pointer;
        white-space: nowrap;

        .op-icon {
          display: flex;
          align-items: center;
          margin-right: 8px;
          position: relative;
          top: 2px;
        }

        i {
          display: inline-block;
          width: 14px;
          height: 16px;
          font-size: 14px;
        }
      }

      .operator-item:last-child {
        margin-right: 0;
      }
    }
  }

  .filter-wrap {
    margin-top: 15px;

    .filter-box {
      height: 22px;
      line-height: 22px;

      .filter-item {
        padding: 0 4px;
        height: 22px;
        line-height: 22px;
        font-size: 14px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        background: rgba(245, 245, 245, 1);
        border-radius: 2px;
        margin-right: 10px;

        .filter-txt {
          margin-right: 4px;
          white-space: nowrap;
          color: #292929;
          user-select: none;
        }

        .filter-icon {
          display: inline-block;
          width: 10px;
          height: 10px;
          font-size: 10px;
          display: flex;
          align-items: center;
          cursor: pointer;

          i {
            cursor: pointer;
            width: 10px;
            height: 10px;
            font-size: 10px;
            color: #9baac1;
          }
        }
      }

      .clear-filter {
        margin-left: 20px;
        font-size: 14px;
        font-weight: normal;
        color: rgba(0, 70, 156, 1);
        cursor: pointer;
      }
    }

    .filter-result {
      text-align: right;
      height: 22px;
      line-height: 22px;
      font-size: 14px;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
    }
  }

  .display-box {
    margin-top: 20px;
    flex-wrap: wrap;

    @media only screen and (max-width: 1290px) {
      .display-item {
        flex: 0 0 25%;
      }
      .display-item:nth-child(5n) {
        .display-item-inner {
          margin-right: 0;
        }
      }
    }

    @media only screen and (min-width: 1290px) {
      .display-item {
        flex: 0 0 16.666%;
      }
      .display-item:nth-child(6n) {
        .display-item-inner {
          margin-right: 0;
        }
      }
    }
  }

  .display-line {
    margin-top: 20px;
  }

  .with-filter-display-box {
    margin-top: 10px;
  }
}
/deep/.update-select {
  .e-filled.e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left).e-disabled,
  .e-filled.e-input-group:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left).e-disabled {
    border-radius: 8px;
    height: 49px;
    padding: 5px 10px 0 20px !important;
  }
}
/deep/.display-item {
  .mian-title .title-pic {
    width: 40px;
    height: 40px;
  }
  .item-tips .tips-item {
    font-size: 12px;
    font-weight: 500;
  }
}
</style>
