import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export const accessToolbarA = [
  { id: 'upGrade', icon: 'icon_table_upgrade', title: i18n.t('手动升级') }
]

export const accessToolbarB = [
  { id: 'upGrade', icon: 'icon_table_upgrade', title: i18n.t('手动升级') },
  { id: 'demotion', icon: 'icon_table_downgrade', title: i18n.t('手动降级') }
]

const validateMobile = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入联系方式'))
  } else if (!utils.isMobile(value)) {
    callback(new Error('请输入正确的联系方式'))
  } else {
    callback()
  }
}

const validateMEmail = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入联系人邮箱'))
  } else if (!utils.isEmail(value)) {
    callback(new Error('请输入正确的联系人邮箱'))
  } else {
    callback()
  }
}

export const areaColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '100',
    field: 'supplierEnterpriseCode',
    cssClass: 'field-content',
    headerText: i18n.t('公司编码')
  },
  {
    width: '100',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('公司名称')
  }
]

export const companyColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '100',
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '100',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  }
]

export const historyColumnData = [
  // {
  //   width: "50",
  //   type: "checkbox",
  //   showInColumnChooser: false,
  // },
  {
    width: '100',
    field: 'orgCode',
    cssClass: 'field-content',
    headerText: i18n.t('公司编码')
  },
  {
    width: '100',
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '100',
    field: 'factoryCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '100',
    field: 'factoryName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '100',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '100',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
export const supplyAreaToolbara = [
  {
    id: 'addNew',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('新增')
  },
  {
    id: 'deleteList',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]
export const inviteRule = {
  supplierEnterpriseId: [
    {
      required: true,
      message: i18n.t('请输入供应商企业全称'),
      trigger: 'blur'
    }
  ],
  contactName: [
    {
      required: true,
      message: i18n.t('请输入联系人'),
      trigger: 'blur'
    },
    { min: 2, max: 5, message: '长度在 2 到 5 个字符', trigger: 'blur' }
  ],
  contactMobile: [{ required: true, validator: validateMobile, trigger: 'blur' }],
  contactEmail: [{ required: true, validator: validateMEmail, trigger: 'blur' }],
  supplierType: [
    {
      required: true,
      message: i18n.t('请输入调查表标识'),
      trigger: 'blur'
    }
  ]
}

export const accessToolbarC = [
  { id: 'demotion', icon: 'icon_table_downgrade', title: i18n.t('手动降级') }
]
import Vue from 'vue'
export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '210',
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    width: '130',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '210',
    field: 'contactName',
    headerText: i18n.t('阶段进度'),
    template: () => {
      return {
        template: Vue.component('operate-template', {
          template: `
            <div class="tp-box" style="color: #00469C">{{ countRate }}</div>
          `,
          data: function () {
            return { data: {} }
          },
          computed: {
            countRate() {
              return this.data.successCount + '/' + this.data.totalCount
            }
          }
        })
      }
    }
  },
  {
    width: '210',
    field: 'contactPhone',
    headerText: i18n.t('待处理任务'),
    template: () => {
      return {
        template: Vue.component('operate-template', {
          template: `
            <div class="tp-box" style="color: #00469C">{{ countRate }}</div>
          `,
          data: function () {
            return { data: {} }
          },
          computed: {
            countRate() {
              return this.data.totalCount - this.data.successCount
            }
          }
        })
      }
    }
  },
  {
    width: '160',
    field: 'upgradeDate',
    headerText: i18n.t('晋级本阶段时间'),
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  },
  {
    width: '160',
    field: 'supplierGradeName',
    headerText: i18n.t('分级')
  },
  {
    width: '160',
    field: 'shareDetails',
    headerText: i18n.t('共享情况')
  },
  {
    width: '160',
    field: 'createUserName',
    headerText: i18n.t('邀请人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('邀请时间'),
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  }
]

export const supplyAreaToolbar = [
  // {
  //   id: "addNew",
  //   icon: "icon_solid_Newinvitation",
  //   title: i18n.t("新增"),
  // },
  {
    id: 'deleteList',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]

export const areaColumnDatab = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '100',
    field: 'orgCode',
    cssClass: 'field-content',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '100',
    field: 'orgName',
    headerText: i18n.t('供应商名称')
  }
]

// 黑名单清单
export const blackListColumn = (violationTypeList = {}) => [
  {
    field: 'number',
    headerText: i18n.t('序号'),
    width: '70',
    textAlign: 'Center',
    template: () => {
      return {
        template: Vue.component('operate-template', {
          template: `
            <div class="tp-box">{{ countRate }}</div>
          `,
          data: function () {
            return { data: {} }
          },
          computed: {
            countRate() {
              let index = parseInt(this.data.index) + 1
              return index
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierEnterpriseName',
    width: '140',
    headerText: i18n.t('供应商企业名称'),
    textAlign: 'Center'
  },
  {
    field: 'categoryName',
    width: '70',
    headerText: i18n.t('品类'),
    textAlign: 'Center'
  },
  {
    field: 'violationType',
    width: '100',
    headerText: i18n.t('违规类型'),
    textAlign: 'Center',
    valueConverter: {
      type: 'map',
      map: violationTypeList
    }
  },
  {
    field: 'penaltyPeriodStart',
    width: '140',
    headerText: i18n.t('进入黑名单时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.substr(0, 10)
        } else {
          return '--'
        }
      }
    }
  }
]
// 供应商级别
export const supplierLevelColumn = [
  {
    field: 'supplierGradeName',
    width: '70',
    headerText: i18n.t('级别')
  },
  {
    field: 'supplierCount',
    width: '50',
    headerText: i18n.t('个数')
  },
  {
    field: 'categoryName',
    width: '90',
    headerText: i18n.t('品类'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == null) {
          return i18n.t('不限品类')
        } else {
          return e
        }
      }
    }
  }
]
// 一级品类中标金额
export const resourceAnalysisColumn = [
  {
    field: 'biddingAmount',
    width: '50',
    headerText: i18n.t('金额')
  },
  {
    field: 'categoryName',
    width: '140',
    headerText: i18n.t('品类'),
    cssClass: '',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == null) {
          return i18n.t('不限品类')
        } else {
          return e
        }
      }
    }
  }
]
// 一级品类支出金额
export const resourceBuyColumn = [
  {
    field: 'purchaseAmount',
    width: '50',
    headerText: i18n.t('金额')
  },
  {
    field: 'firstCategoryName',
    width: '140',
    headerText: i18n.t('品类'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == null) {
          return i18n.t('不限品类')
        } else {
          return e
        }
      }
    }
  }
]
// 中标金额情况
export const supplierIncreaseColumn = [
  {
    field: 'biddingAmount',
    width: '140',
    headerText: i18n.t('金额')
  },
  {
    field: 'date',
    width: '70',
    headerText: i18n.t('时间')
  }
]
// 支出金額變化情況
export const supplierExpendColumn = [
  {
    field: 'purchaseAmount',
    width: '140',
    headerText: i18n.t('金额')
  },
  {
    field: 'date',
    width: '70',
    headerText: i18n.t('时间')
  }
]
// 供应商生态分析
export const ecologicalAnalysisColumn = [
  {
    field: 'year',
    width: '70',
    headerText: i18n.t('数据年份')
  },
  {
    field: 'saleAmount',
    width: '70',
    headerText: i18n.t('销售金额')
  },
  {
    field: 'purAmount',
    width: '70',
    headerText: i18n.t('采购金额')
  }
]
// 供应商分布
export const supplierDistribution = [
  {
    field: 'area',
    width: '70',
    headerText: i18n.t('分布地区')
  },
  {
    field: 'total',
    width: '50',
    headerText: i18n.t('分布数量')
  },
  {
    field: 'categoryName',
    width: '90',
    headerText: i18n.t('品类'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == null) {
          return i18n.t('不限品类')
        } else {
          return e
        }
      }
    }
  }
]

// 一级品类供应商数
export const supplierQuotient = [
  {
    field: 'labelName',
    width: '70',
    headerText: i18n.t('数据项目')
  },
  {
    field: 'supplierCount',
    width: '50',
    headerText: i18n.t('供应商个数')
  },
  {
    field: 'categoryName',
    width: '100',
    headerText: i18n.t('品类'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == null) {
          return i18n.t('不限品类')
        } else {
          return e
        }
      }
    }
  }
]
export const quantitySupplier = [
  {
    field: 'month',
    width: '60',
    headerText: i18n.t('时间')
  },
  {
    field: 'labelName',
    width: '80',
    headerText: i18n.t('数据项目')
  },
  {
    field: 'supplierCount',
    width: '80',
    headerText: i18n.t('供应商个数')
  },
  {
    field: 'firstCategoryName',
    width: '100',
    headerText: i18n.t('品类'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == null) {
          return i18n.t('不限品类')
        } else {
          return e
        }
      }
    }
  }
]
