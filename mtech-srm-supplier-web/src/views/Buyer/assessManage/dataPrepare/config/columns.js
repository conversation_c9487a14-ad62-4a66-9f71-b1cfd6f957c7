import { i18n } from '@/main.js'
import Vue from 'vue'
import MtProgress from '@mtech-ui/progress'

// 供应商共享
export const columnShare = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'archiveCode',
    headerText: i18n.t('档案编码'),
    cssClass: 'field-content',
    width: '150'
  },
  {
    field: 'planName',
    width: '120',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'planCycle',
    width: '300',
    headerText: i18n.t('计划策略'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('年度'),
        1: i18n.t('半年度'),
        2: i18n.t('季度'),
        3: i18n.t('月度')
      }
    }
  },
  {
    field: 'issueNum',
    width: '120',
    headerText: i18n.t('期号')
  },
  {
    field: 'submitDeadline',
    width: '120',
    headerText: i18n.t('提交截止日期')
  },
  {
    field: 'finishNum', //countNum
    width: '150',
    headerText: i18n.t('完成进度'),
    cssClass: '',
    template: () => {
      return {
        template: Vue.component('finishNum', {
          components: {
            MtProgress
          },
          template: `
              <div v-if='text' style='
              display: flex;
              align-items: center;
              flex-direction: row;
          ' >
              <mt-progress style='width:auto'
                ref="vertical"
                id="vertical-container"
                type="Circular"
                :startAngle="startAngle1"
                :endAngle="endAngle1"
                :minimum="minimum"
                width="50px"
                height="50px"
                :maximum="data.countNum"
                :value="data.finishNum"
                cornerRadius="Round"
                :trackThickness="trackThickness"
                :progressThickness="progressThickness"

              >
              </mt-progress>
              <span>{{text}}</span>
              </div>`,
          data: function () {
            return {
              startAngle1: 0,
              endAngle1: 0,
              minimum: 0,
              maximum: 100,
              trackThickness: 2,
              progressThickness: 2,
              data: {}
            }
          },
          computed: {
            text() {
              console.log(this.data)
              return this.data.finishNum + '/' + this.data.countNum
            }
          },
          methods: {}
        })
      }
    }
  },
  {
    field: 'orgName',
    width: '150',
    headerText: i18n.t('公司')
  },
  {
    width: '110',
    field: 'raterName',
    headerText: i18n.t('评分人')
  },
  {
    field: 'taskCreateDate',
    width: '150',
    headerText: i18n.t('任务创建日期')
  },
  {
    field: 'status',
    width: '180',
    headerText: i18n.t('状态')
  }
]
const obj = {
  digis_q_quality_information: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'purchasingManager',
      width: '150',
      headerText: i18n.t('采购责任人')
    },
    {
      field: 'partsManager',
      width: '150',
      headerText: i18n.t('部品责任人')
    },
    {
      field: 'exemptInspectionYn',
      width: '150',
      headerText: i18n.t('是否免检')
    },
    {
      field: 'incomingBatches',
      width: '150',
      headerText: i18n.t('IQC-来料批数')
    },
    {
      field: 'qualifiedBatches',
      width: '150',
      headerText: i18n.t('IQC-合格批数')
    },
    {
      field: 'unqualifiedBatches',
      width: '150',
      headerText: i18n.t('IQC-不合格批数')
    },
    {
      field: 'inspectionPassRate',
      width: '150',
      headerText: i18n.t('IQC-进检合格率')
    },
    {
      field: 'iqcScore',
      width: '150',
      headerText: i18n.t('IQC-评分')
    },
    {
      field: 'weightingFactor',
      width: '150',
      headerText: i18n.t('IQC-加权系数')
    },
    {
      field: 'actualScore',
      width: '150',
      headerText: i18n.t('实际IQC得分')
    },
    {
      field: 'forActualSituation',
      width: '150',
      headerText: i18n.t('FOR-实际情况')
    },
    {
      field: 'forScore',
      width: '150',
      headerText: i18n.t('FOR-评分')
    },
    {
      field: 'oqcActualSituation',
      width: '150',
      headerText: i18n.t('OQC-实际情况')
    },
    {
      field: 'oqcScore',
      width: '150',
      headerText: i18n.t('OQC-评分')
    },
    {
      field: 'fcrActualSituation',
      width: '150',
      headerText: i18n.t('FCR-实际情况')
    },
    {
      field: 'fcrScore',
      width: '150',
      headerText: i18n.t('FCR-评分')
    },
    {
      field: 'mqcActualSituation',
      width: '150',
      headerText: i18n.t('MQC-实际情况')
    },
    {
      field: 'mqcScore',
      width: '150',
      headerText: i18n.t('MQC-评分')
    },
    {
      field: 'deductionDesc',
      width: '150',
      headerText: i18n.t('扣分详细说明')
    },
    {
      field: 'evaluationMonthly',
      width: '150',
      headerText: i18n.t('考评月度')
    }
  ],
  digis_c_drop_data_maintenance: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'performanceCategory',
      width: '150',
      headerText: i18n.t('绩效类别')
    },
    {
      field: 'secondaryIndicator',
      width: '150',
      headerText: i18n.t('次级指标')
    },
    {
      field: 'indicatorDetails',
      width: '150',
      headerText: i18n.t('指标细项')
    },
    {
      field: 'factory',
      width: '150',
      headerText: i18n.t('工厂')
    },
    {
      field: 'materialCode',
      width: '150',
      headerText: i18n.t('物料代码')
    },
    {
      field: 'materialName',
      width: '150',
      headerText: i18n.t('物料名称')
    },
    {
      field: 'purchasePrices',
      width: '150',
      headerText: i18n.t('采购单价')
    },
    {
      field: 'purchaseQuantity',
      width: '150',
      headerText: i18n.t('采购数量')
    },
    {
      field: 'basePrice',
      width: '150',
      headerText: i18n.t('基准价格')
    },
    {
      field: 'categoryDropMonth',
      width: '150',
      headerText: i18n.t('当月品类降幅')
    },
    {
      field: 'level',
      width: '150',
      headerText: i18n.t('级别')
    },
    {
      field: 'ratingRules',
      width: '150',
      headerText: i18n.t('评分规则')
    },
    {
      field: 'performanceScore',
      width: '150',
      headerText: i18n.t('绩效得分')
    },
    {
      field: 'statementDate',
      width: '150',
      headerText: i18n.t('对账单日期'),
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.statementDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ],
  digis_c_price_x1_base_points: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'xoneBaseScore',
      width: '150',
      headerText: i18n.t('X1基础分')
    },
    {
      field: 'effectiveStartTime',
      width: '150',
      headerText: i18n.t('有效起始时间'),
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.effectiveStartTime.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'failureTime',
      width: '150',
      headerText: i18n.t('失效时间'),
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.failureTime.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ],
  digis_c_base_price_maintenance: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'state',
      width: '150',
      headerText: i18n.t('状态')
    },
    {
      field: 'factoryCode',
      width: '150',
      headerText: i18n.t('工厂代码')
    },
    {
      field: 'materialCode',
      width: '150',
      headerText: i18n.t('物料代码')
    },
    {
      field: 'materialName',
      width: '150',
      headerText: i18n.t('物料名称')
    },
    {
      field: 'basePrice',
      width: '150',
      headerText: i18n.t('基准价格')
    },
    {
      field: 'currencyCode',
      width: '150',
      headerText: i18n.t('币种代码')
    },
    {
      field: 'effectiveStartTime',
      width: '150',
      headerText: i18n.t('有效起始时间'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.effectiveStartTime.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'failureTime',
      width: '150',
      headerText: i18n.t('失效时间'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.failureTime.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'founder',
      width: '150',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createDate',
      width: '150',
      headerText: i18n.t('创建日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.createDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'auditDate',
      width: '150',
      headerText: i18n.t('审核日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.auditDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ],
  digis_c_dtc_basic_data: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'rawMaterial',
      width: '150',
      headerText: i18n.t('原物料')
    },
    {
      field: 'newMaterial',
      width: '150',
      headerText: i18n.t('新物料')
    },
    {
      field: 'materialCode',
      width: '150',
      headerText: i18n.t('物料代码')
    },
    {
      field: 'materialName',
      width: '150',
      headerText: i18n.t('物料名称')
    },
    {
      field: 'singleMachineSavesCosts',
      width: '150',
      headerText: i18n.t('单机节省成本')
    },
    {
      field: 'bomNumber',
      width: '150',
      headerText: i18n.t('BOM号')
    },
    {
      field: 'allBomYn',
      width: '150',
      headerText: i18n.t('是否适用于所有BOM')
    },
    {
      field: 'effectiveStartTime',
      width: '150',
      headerText: i18n.t('有效起始时间'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.effectiveStartTime.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'failureTime',
      width: '150',
      headerText: i18n.t('失效时间'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.failureTime.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'founder',
      width: '150',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createDate',
      width: '150',
      headerText: i18n.t('创建日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.createDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ],
  digis_c_cost_improvement_data: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'state',
      width: '150',
      headerText: i18n.t('状态')
    },
    {
      field: 'factory',
      width: '150',
      headerText: i18n.t('工厂')
    },
    {
      field: 'originalMaterialComponent',
      width: '150',
      headerText: i18n.t('原物编/组件')
    },
    {
      field: 'newMaterialComponent',
      width: '150',
      headerText: i18n.t('新物编/组件')
    },
    {
      field: 'singleMachineSavesCosts',
      width: '150',
      headerText: i18n.t('单机节省成本')
    },
    {
      field: 'productionQuantity',
      width: '150',
      headerText: i18n.t('当月机型生产台数')
    },
    {
      field: 'dtcAffiliatedSuppliers',
      width: '150',
      headerText: i18n.t('DTC所属供应商')
    },
    {
      field: 'dtcReductionAmount',
      width: '150',
      headerText: i18n.t('DTC降本金额')
    },
    {
      field: 'dtcReductionRate',
      width: '150',
      headerText: i18n.t('DTC降本率')
    },
    {
      field: 'quota',
      width: '150',
      headerText: i18n.t('配额')
    },
    {
      field: 'originalAmount',
      width: '150',
      headerText: i18n.t('原始金额')
    },
    {
      field: 'ratingRules',
      width: '150',
      headerText: i18n.t('评分规则')
    },
    {
      field: 'performanceScore',
      width: '150',
      headerText: i18n.t('绩效得分')
    },
    {
      field: 'updateStaff',
      width: '150',
      headerText: i18n.t('更新人员')
    },
    {
      field: 'updateDate',
      width: '150',
      headerText: i18n.t('更新日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.updateDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ],
  digis_d_no_work_hours: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'indicatorDetails',
      width: '150',
      headerText: i18n.t('指标细项')
    },
    {
      field: 'factoryCode',
      width: '150',
      headerText: i18n.t('工厂代码')
    },
    {
      field: 'noWorkHours',
      width: '150',
      headerText: i18n.t('无作业工时')
    },
    {
      field: 'eventDescription',
      width: '150',
      headerText: i18n.t('事件说明')
    }
  ],
  digis_d_urgent_order_coordination: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'state',
      width: '150',
      headerText: i18n.t('状态')
    },
    {
      field: 'performanceCategory',
      width: '150',
      headerText: i18n.t('绩效类别')
    },
    {
      field: 'secondaryIndicator',
      width: '150',
      headerText: i18n.t('次级指标')
    },
    {
      field: 'indicatorDetails',
      width: '150',
      headerText: i18n.t('指标细项')
    },
    {
      field: 'factory',
      width: '150',
      headerText: i18n.t('工厂')
    },
    {
      field: 'rushOrderNumber',
      width: '150',
      headerText: i18n.t('紧急订单号')
    },
    {
      field: 'eventDescription',
      width: '150',
      headerText: i18n.t('事件说明')
    },
    {
      field: 'ratingRules',
      width: '150',
      headerText: i18n.t('评分规则')
    },
    {
      field: 'addPoints',
      width: '150',
      headerText: i18n.t('加分')
    },
    {
      field: 'urgentOrdersNumber',
      width: '150',
      headerText: i18n.t('当月紧急订单个数')
    },
    {
      field: 'categoryUrgentOrderPoints',
      width: '150',
      headerText: i18n.t('品类-紧急订单加分')
    },
    {
      field: 'entryDepartment',
      width: '150',
      headerText: i18n.t('录入部门')
    },
    {
      field: 'entryStaff',
      width: '150',
      headerText: i18n.t('录入人员')
    },
    {
      field: 'entryDate',
      width: '150',
      headerText: i18n.t('录入日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.entryDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'auditDate',
      width: '150',
      headerText: i18n.t('审核日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.auditDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ],
  digis_d_delivery_exception_data: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'indicatorDetails',
      width: '150',
      headerText: i18n.t('指标细项')
    },
    {
      field: 'factoryCode',
      width: '150',
      headerText: i18n.t('工厂代码')
    },
    {
      field: 'materialCode',
      width: '150',
      headerText: i18n.t('物料代码')
    },
    {
      field: 'deliveryType',
      width: '150',
      headerText: i18n.t('交货类型(PO/JIT/排期)')
    },
    {
      field: 'deliveryExceptionDescription',
      width: '150',
      headerText: i18n.t('交货异常说明')
    },
    {
      field: 'lotOrderNumber',
      width: '150',
      headerText: i18n.t('批次号/订单号')
    },
    {
      field: 'batchesNumber',
      width: '150',
      headerText: i18n.t('当月该物编的批次数')
    }
  ],
  digis_s_service_score_basic_data: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'evaluationIndicators',
      width: '150',
      headerText: i18n.t('考评指标')
    },
    {
      field: 'indicatorDetails',
      width: '150',
      headerText: i18n.t('指标细项')
    },
    {
      field: 'eventDescription',
      width: '150',
      headerText: i18n.t('事件说明')
    },
    {
      field: 'deductionRules',
      width: '150',
      headerText: i18n.t('扣分规则')
    },
    {
      field: 'deductPoints',
      width: '150',
      headerText: i18n.t('该行扣分')
    },
    {
      field: 'entryDepartment',
      width: '150',
      headerText: i18n.t('录入部门')
    },
    {
      field: 'entryStaff',
      width: '150',
      headerText: i18n.t('录入人员')
    },
    {
      field: 'entryDate',
      width: '150',
      headerText: i18n.t('录入日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.entryDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'auditDate',
      width: '150',
      headerText: i18n.t('审核日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.auditDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ],
  digis_c_new_pro_dev_prop_data_maintenance: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'performanceCategory',
      width: '150',
      headerText: i18n.t('绩效类别')
    },
    {
      field: 'secondaryIndicator',
      width: '150',
      headerText: i18n.t('次级指标')
    },
    {
      field: 'indicatorDetails',
      width: '150',
      headerText: i18n.t('指标细项')
    },
    {
      field: 'materialCode',
      width: '150',
      headerText: i18n.t('物料代码')
    },
    {
      field: 'materialName',
      width: '150',
      headerText: i18n.t('物料名称')
    },
    {
      field: 'winTheBidYn',
      width: '150',
      headerText: i18n.t('当月供应商新品开发中标')
    },
    {
      field: 'sourceDocumentNumber',
      width: '150',
      headerText: i18n.t('来源单据号')
    },
    {
      field: 'proportion',
      width: '150',
      headerText: i18n.t('新产品开发占比')
    },
    {
      field: 'ratingRules',
      width: '150',
      headerText: i18n.t('评分规则')
    },
    {
      field: 'performanceScore',
      width: '150',
      headerText: i18n.t('绩效得分')
    },
    {
      field: 'entryDate',
      width: '150',
      headerText: i18n.t('对账单日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.entryDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ],
  digis_c_open_cost_data: [
    {
      field: 'purchaseEnterpriseId',
      width: '150',
      headerText: i18n.t('采方公司代码')
    },
    {
      field: 'purchaseEnterpriseName',
      width: '150',
      headerText: i18n.t('采方公司名称')
    },
    {
      field: 'supplierCode',
      width: '150',
      headerText: i18n.t('供应商代码')
    },
    {
      field: 'supplierName',
      width: '150',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'categoryCode',
      width: '150',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      width: '150',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'eventDate',
      width: '150',
      headerText: i18n.t('事件日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.eventDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    },
    {
      field: 'performanceCategory',
      width: '150',
      headerText: i18n.t('绩效类别')
    },
    {
      field: 'secondaryIndicator',
      width: '150',
      headerText: i18n.t('次级指标')
    },
    {
      field: 'indicatorDetails',
      width: '150',
      headerText: i18n.t('指标细项')
    },
    {
      field: 'materialCode',
      width: '150',
      headerText: i18n.t('物料代码')
    },
    {
      field: 'materialName',
      width: '150',
      headerText: i18n.t('物料名称')
    },
    {
      field: 'finalOffer',
      width: '150',
      headerText: i18n.t('最终报价')
    },
    {
      field: 'initialOffer',
      width: '150',
      headerText: i18n.t('最初报价')
    },
    {
      field: 'costDeviationRate',
      width: '150',
      headerText: i18n.t('成本偏差率')
    },
    {
      field: 'ratingRules',
      width: '150',
      headerText: i18n.t('评分规则')
    },
    {
      field: 'performanceScore',
      width: '150',
      headerText: i18n.t('绩效得分')
    },
    {
      field: 'pricingDate',
      width: '150',
      headerText: i18n.t('定价日期'),
      // 创建组件
      template: () => {
        return {
          template: Vue.component('maxScore', {
            template: `
                <div v-if='text'>
                  {{text}}
                </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            computed: {
              text() {
                let text = []
                text = this.data.pricingDate.split(' ')
                //  console.log(text)
                return text[0] || ''
              }
            },
            methods: {}
          })
        }
      }
    }
  ]
}

//进货记录表
export function recodeColumn(value, type) {
  console.log(type)
  if (type == 2) {
    return [
      {
        field: 'fieldType',
        headerText: i18n.t('字段类型')
      },
      {
        field: 'fieldName',
        headerText: i18n.t('字段名')
      }
    ]
  }
  if (obj[value]) {
    return obj[value]
  } else {
    return obj['digis_c_drop_data_maintenance']
  }
}
