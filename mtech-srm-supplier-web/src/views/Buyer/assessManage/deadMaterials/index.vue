<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { deadmaterialCols } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '6931300e-ecf2-4267-83eb-c5c3a8fa7359',
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder ',
                  title: this.$t('新增')
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete ',
                  title: this.$t('删除')
                },
                {
                  id: 'copy',
                  title: this.$t('复制')
                },
                // {
                //   id: 'Submit',
                //   icon: 'icon_solid_Createproject ',
                //   title: this.$t('提交')
                // },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            showSelected: false,
            columnData: deadmaterialCols,
            asyncConfig: {
              url: '/analysis//tenant/idleMaterialVoucher/headerPageQuery'
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      let records = e.gridRef.getCustomSelectedRows()
      if (records.length <= 0 && e.toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickCellTitle()
        return
      }
      if (e.toolbar.id === 'Delete') {
        if (records.length !== 1) {
          this.$toast({ content: this.$t('仅可选择一行进行删除操作'), type: 'warning' })
          return
        }
        this.handleDelete(records[0]['id'])
        return
      }
      if (e.toolbar.id === 'Create') {
        if (records.length !== 1) {
          this.$toast({ content: this.$t('仅可选择一行进行创建操作'), type: 'warning' })
          return
        }
        this.handleCreate(records)
        return
      }
      if (e.toolbar.id === 'audit') {
        const list = e.gridRef.getCustomSelectedRows()
        this.audit(list)
      }
      if (e.toolbar.id === 'copy') {
        this.handleCopy(records)
      }
    },
    // 查看OA审批按钮
    audit(sltList) {
      // if (sltList.length < 1) {
      //   this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
      //   return
      // }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].id,
        businessType: 'MATERIAL_VOUCHER'
      }
      this.$API.deadMaterials.deadMaterialsOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    handleDelete(id) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.deadMaterials.removeHeader(id).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })

            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleCreate(records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认创建数据？')
        },
        success: () => {
          const params = {
            createType: 1,
            headerId: records[0]['id']
          }
          this.$API.deadMaterials.createClaimVoucher(params).then(() => {
            this.$toast({
              content: this.$t('创建成功'),
              type: 'success'
            })

            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleClickCellTitle(e) {
      if (e && e.field == 'code') {
        let { data } = e
        sessionStorage.setItem(
          'deadMaterialsListInfo',
          JSON.stringify({
            id: data.id,
            // approvalNo: data.approvalNo,
            // companyCode: data.companyCode,
            // companyName: data.companyName
            ...data
          })
        )
        this.$router.push({
          path: '/supplier/pur/idle-material-claimsDetail',
          query: {
            id: data.id,
            timeStamp: new Date().getTime()
          }
        })
      } else {
        this.$router.push({
          name: 'idle-material-claimsDetail'
        })
      }
    },
    // 复制
    async handleCopy(list) {
      if (list.length > 1) {
        this.$toast({ content: '只能选择一条数据', type: 'warning' })
        return
      }
      const res = await this.$API.deadMaterials.copyDeadMaterials(list[0].id)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
// .mt-form-item {
//   width: 25.5%;
// }

// /deep/ {
//   .mt-form-item {
//     width: calc(20% - 20px);
//     min-width: 200px;
//     display: inline-flex;
//     margin-right: 20px;
//     margin-bottom: 20px;
//     .label {
//       margin-bottom: 6px;
//     }
//   }
// }
::v-deep {
  .template_checkbox {
    text-align: center;
    input[type='checkbox'] {
      visibility: visible;
    }
  }
}
</style>
