import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import selectedItemCode from '../coms/selectItemCode'
import cellChanged from '../coms/cellChanged.vue'

export default {
  components: {
    RemoteAutocomplete
  }
}

const isCall = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const deadmaterialCols = [
  // {
  //   width: '50',
  //   type: 'checkbox'
  // },
  {
    field: 'code',
    headerText: i18n.t('清单编码'),
    cellTools: []
  },
  {
    field: 'approvalNo',
    headerText: i18n.t('OA审批号')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司代码'),
    searchOptions: {
      elementType: 'remote-autocomplete',
      selectType: 'administrativeCompany', // 业务公司
      fields: { text: 'orgName', value: 'orgCode' },
      params: {
        organizationLevelCodes: ['ORG01', 'ORG02']
      },
      width: '256',
      multiple: false,
      operator: 'in',
      url: '/masterDataManagement/tenant/organization/specified-level-paged-query'
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    searchOptions: {}
  },
  {
    field: 'totalAmt',
    headerText: i18n.t('呆滞料总金额/元'),
    ignore: true
  },
  {
    field: 'claimTotalAmt',
    headerText: i18n.t('索赔总额/元'),
    ignore: true
  },
  {
    field: 'typeName',
    headerText: i18n.t('类型')
  },
  {
    field: 'categName',
    headerText: i18n.t('类别')
  },
  {
    field: 'reason',
    headerText: i18n.t('报呆原因')
  },
  // {
  //   field: 'purchaserTotalAmt',
  //   headerText: i18n.t('TCL承担损失总额/元'),
  //   ignore: true,
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       if (typeof e === 'number') {
  //         return e.toFixed(2)
  //       } else {
  //         return e
  //       }
  //     }
  //   }
  // },
  // {
  //   field: 'purchaserActualTotalAmt',
  //   headerText: i18n.t('TCL实际承担损失总额/元'),
  //   ignore: true,
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       if (typeof e === 'number') {
  //         return e.toFixed(2)
  //       } else {
  //         return e
  //       }
  //     }
  //   }
  // },
  // {
  //   field: 'supplierTotalAmt',
  //   headerText: i18n.t('供应商承担损失总额/元'),
  //   ignore: true,
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       if (typeof e === 'number') {
  //         return e.toFixed(2)
  //       } else {
  //         return e
  //       }
  //     }
  //   }
  // },
  // {
  //   field: 'supplierActualTotalAmt',
  //   headerText: i18n.t('供应商实际承担损失总额/元'),
  //   ignore: true,
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       if (typeof e === 'number') {
  //         return e.toFixed(2)
  //       } else {
  //         return e
  //       }
  //     }
  //   }
  // },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x, i) => {
          if (i === 1) {
            return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
          }
          return Number(new Date(x.toString()))
        })
      }
    },
    ignore: true
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     if (e) {
    //       if (e == 0) {
    //         return (e = '')
    //       } else if (typeof e == 'object') {
    //         return utils.formateTime(e, 'yyyy-MM-dd hh:mm:ss')
    //       } else if (typeof e == 'string') {
    //         if (e.indexOf('T') != -1) {
    //           return e.substr(0, 10)
    //         } else {
    //           let val = parseInt(e)
    //           return utils.formateTime(new Date(val), 'yyyy-MM-dd hh:mm:ss')
    //         }
    //       } else if (typeof e == 'number') {
    //         return utils.formateTime(new Date(e), 'yyyy-MM-dd hh:mm:ss')
    //       } else {
    //         return e
    //       }
    //     } else {
    //       return e
    //     }
    //   }
    // }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    ignore: true
  },
  {
    field: 'status',
    headerText: i18n.t('清单状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('新建'),
        2: i18n.t('待审批'),
        3: i18n.t('已驳回'),
        4: i18n.t('已通过'),
        5: i18n.t('已废弃')
      }
    }
  }
]

export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}

export const deadmaterialDetailCols = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false
  },
  // {
  //   field: 'batchNo',
  //   headerText: i18n.t('批次号'),
  //   allowEditing: false,
  //   ignore: true
  // },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂代码'),
    headerTemplate: () => {
      return {
        template: Vue.component('factoryCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂代码')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            remoteSearch={true}
            v-model={scoped.factoryCode}
            operator='in'
            url={`/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
              'currentBu'
            )}`}
            fields={{ text: 'siteName', value: 'siteCode' }}
            searchFields={['siteName', 'siteCode']}
            disabled={scoped.voucherStatus == 2}
            multiple={false}
            placeholder={i18n.t('请选择工厂')}
            selectType='factoryAddress'
            getPopupContainer={() => document.body}
            onChange={(e) => {
              if (e.itemData.siteCode || e.value == null) {
                scoped.factoryCode = e.itemData.siteCode
                scoped.factoryName = e.itemData.siteName
              }
            }}></RemoteAutocomplete>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'factoryName',
    headerText: i18n.t('工厂名称'),
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component('supplierCodeTemplate', {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('供应商编码')}}</span>
    //           </div>
    //         `
    //     })
    //   }
    // },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            remoteSearch={true}
            v-model={scoped.supplierCode}
            operator='in'
            fields={{ text: 'supplierName', value: 'supplierCode' }}
            searchFields={['supplierName', 'supplierCode']}
            url={'/masterDataManagement/tenant/supplier/paged-query'}
            disabled={scoped.voucherStatus == 2}
            multiple={false}
            placeholder={i18n.t('请选择供应商')}
            selectType='supplier'
            getPopupContainer={() => document.body}
            onChange={(e) => {
              if (e.itemData.supplierName || e.value == null) {
                scoped.supplierName = e.itemData?.supplierName
              }
            }}></RemoteAutocomplete>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  {
    field: 'plannerName',
    headerText: i18n.t('计划员'),
    headerTemplate: () => {
      return {
        template: Vue.component('plannerNameTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('计划员')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            remoteSearch={true}
            v-model={scoped.plannerName}
            operator='in'
            fields={{ text: 'employeeName', value: 'employeeCode' }}
            searchFields={['employeeName', 'employeeCode']}
            url={'/masterDataManagement/tenant/employee/paged-query'}
            disabled={scoped.voucherStatus == 2}
            titleSwitch={false}
            multiple={false}
            placeholder={i18n.t('请选择计划员')}
            selectType='staff'
            getPopupContainer={() => document.body}
            onChange={(e) => {
              if (e.itemData.externalCode || e.value == null) {
                scoped.plannerExternalCode = e.itemData?.externalCode
                scoped.userId = e.itemData?.userId
              } else {
                scoped.plannerExternalCode = e.itemData?.externalCode
                scoped.userId = e.itemData?.userId
              }
            }}></RemoteAutocomplete>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'plannerExternalCode',
    headerText: i18n.t('计划员编码'),
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('categoryCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('品类编码')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            v-model={scoped.categoryCode}
            url='/masterDataManagement/tenant/category/paged-query'
            multiple={false}
            placeholder={i18n.t('请选择品类编码')}
            popup-width='250'
            fields={{ text: 'categoryName', value: 'categoryCode' }}
            search-fields={['categoryCode', 'categoryName']}
            onChange={(e) => {
              scoped.categoryCode = e.itemData?.categoryCode
              scoped.categoryName = e.itemData?.categoryName
              // if (e.itemData.externalCode || e.value == null) {
              //   scoped.plannerExternalCode = e.itemData?.externalCode
              // }
            }}></RemoteAutocomplete>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('materialCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span>{{$t('物料编码')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return {
        template: selectedItemCode
      }
    },
    ignore: true
  },
  {
    field: 'materialDesc',
    headerText: i18n.t('物料描述'),
    editTemplate: () => {
      return { template: cellChanged }
    },
    ignore: true
  },
  {
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织代码'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaseOrgCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购组织代码')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      console.log('scopedscopedscopedscopedscoped', scoped)
      return (
        <div>
          <RemoteAutocomplete
            remoteSearch={false}
            v-model={scoped.purchaseOrgCode}
            url={this.$API.masterData.getBusinessOrganizationUrl}
            multiple={false}
            placeholder={i18n.t('请选择采购组织')}
            popup-width='250'
            method='get'
            params={{
              businessOrganizationTypeCode: 'BUORG002ADM',
              organizationId: scoped.companyCode
                ? scoped.companyCode
                : sessionStorage.getItem('companyId'),
              id: '',
              organizationCode: '',
              organizationName: ''
            }}
            records-position='data'
            data-limit={999}
            fields={{ text: 'organizationName', value: 'organizationCode' }}
            onChange={(e) => {
              if (e.itemData.organizationName || e.value == null) {
                scoped.purchaseOrgName = e.itemData.organizationName
              }
            }}></RemoteAutocomplete>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织名称'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaseOrgNameTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购组织名称')}}</span>
              </div>
            `
        })
      }
    },
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  {
    field: 'invQtyText',
    headerText: i18n.t('在库数量'),
    headerTemplate: () => {
      return {
        template: Vue.component('invQtyTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('在库数量')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <mt-inputNumber
            v-model={scoped.invQtyText}
            min='0'
            placeholder={i18n.t('请输入在库数量')}
            step='1'
            show-clear-button={true}
            onChange={(e) => {
              scoped.invQty = e
              scoped.invQtyText = e
              // 数量 = 在库数量 + 在途数量
              scoped.qtyText = scoped.transitQtyText + e
              scoped.qty = scoped.qtyText
              // 金额 = 数量 * 单价
              scoped.actualAmt = (scoped.qty * scoped.unitPrice).toFixed(2)
              scoped.actualAmtText = scoped.actualAmt
            }}></mt-inputNumber>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'transitQtyText',
    headerText: i18n.t('在途数量'),
    headerTemplate: () => {
      return {
        template: Vue.component('transitQtyTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('在途数量')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <mt-inputNumber
            v-model={scoped.transitQtyText}
            min='0'
            placeholder={i18n.t('请输入在途数量')}
            step='1'
            show-clear-button={true}
            onChange={(e) => {
              scoped.transitQty = e
              scoped.transitQtyText = e
              // 数量 = 在库数量 + 在途数量
              scoped.qtyText = scoped.invQtyText + e
              scoped.qty = scoped.qtyText
              // 金额 = 数量 * 单价
              scoped.actualAmt = (scoped.qty * scoped.unitPrice).toFixed(2)
              scoped.actualAmtText = scoped.actualAmt
            }}></mt-inputNumber>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'qtyText',
    headerText: i18n.t('数量'),
    headerTemplate: () => {
      return {
        template: Vue.component('qtyTextTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('数量')}}</span>
              </div>
            `
        })
      }
    },
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  {
    field: 'currency',
    headerText: i18n.t('币种'),
    width: '150',
    headerTemplate: () => {
      return {
        template: Vue.component('currencyTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('币种')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            v-model={scoped.currency}
            url='/masterDataManagement/tenant/currency/queryAll'
            multiple={false}
            placeholder={i18n.t('请选择币种')}
            popup-width='250'
            fields={{ text: 'currencyName', value: 'currencyCode' }}
            search-fields={['currencyCode', 'currencyName']}
            records-position='data'></RemoteAutocomplete>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'unitPriceText',
    headerText: i18n.t('单价'),
    headerTemplate: () => {
      return {
        template: Vue.component('unitPriceTextTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('单价')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <mt-inputNumber
            v-model={scoped.unitPriceText}
            min='0'
            placeholder={i18n.t('请输入单价')}
            show-clear-button={true}
            disabled={!scoped.qtyText}
            onChange={(e) => {
              scoped.unitPrice = e
              // 金额 = 数量 * 单价
              scoped.actualAmt = (scoped?.qtyText * e).toFixed(2)
              scoped.actualAmtText = scoped.actualAmt
            }}></mt-inputNumber>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'actualAmt',
    headerText: i18n.t('预估金额'),
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  // {
  //   field: 'actualAmtText',
  //   headerText: i18n.t('实际损失金额'),
  //   editorRender(h, scoped) {
  //     return (
  //       <div>
  //         <mt-inputNumber
  //           v-model={scoped.actualAmtText}
  //           min='0'
  //           placeholder=i18n.t('请输入实际损失金额')
  //           show-clear-button={true}></mt-inputNumber>
  //       </div>
  //     )
  //   },
  //   ignore: true
  // },
  // {
  //   field: 'purchaserPercent',
  //   headerText: i18n.t('TCL承担占比'),
  //   editorRender(h, scoped) {
  //     return (
  //       <div>
  //         <mt-inputNumber
  //           v-model={scoped.purchaserPercent}
  //           min='0'
  //           placeholder='请输入TCL承担占比'
  //           show-clear-button={true}></mt-inputNumber>
  //       </div>
  //     )
  //   },
  //   ignore: true
  // },
  // {
  //   field: 'purchaseAmt',
  //   headerText: i18n.t('TCL承担损失'),
  //   allowEditing: false,
  //   ignore: true
  // },
  // {
  //   field: 'supplierPercent',
  //   headerText: i18n.t('供方承担占比'),
  //   editorRender(h, scoped) {
  //     return (
  //       <div>
  //         <mt-inputNumber
  //           v-model={scoped.supplierPercent}
  //           min='0'
  //           placeholder=i18n.t('请输入供方承担占比')
  //           show-clear-button={true}></mt-inputNumber>
  //       </div>
  //     )
  //   },
  //   ignore: true
  // },
  // {
  //   field: 'supplierAmt',
  //   headerText: i18n.t('供方承担损失'),
  //   allowEditing: false,
  //   ignore: true
  // },
  {
    field: 'costCenter',
    headerText: i18n.t('成本中心'),
    headerTemplate: () => {
      return {
        template: Vue.component('costCenterTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('成本中心')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            v-model={scoped.costCenter}
            url={`/masterDataManagement/tenant/cost-center/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`}
            multiple={false}
            placeholder={i18n.t('请选择成本中心')}
            popup-width='250'
            fields={{ text: 'costCenterDesc', value: 'costCenterCode' }}
            search-fields={['costCenterCode', 'costCenterDesc']}
            onChange={(e) => {
              scoped.costCenter = e.itemData?.costCenterCode
              scoped.costCenterDesc = e.itemData?.costCenterDesc
            }}></RemoteAutocomplete>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'costCenterDesc',
    headerText: i18n.t('成本中心描述'),
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  {
    field: 'purchaserRemark',
    headerText: i18n.t('采购方备注'),
    providedEditor: true,
    editorParams: {
      type: 'input'
    },
    ignore: true
  },
  // {
  //   field: 'voucherStatus',
  //   headerText: i18n.t('状态'),
  //   allowEditing: false,
  //   valueConverter: {
  //     type: 'map',
  //     map: {
  //       1: i18n.t('新增'),
  //       2: i18n.t('已创建'),
  //       3: i18n.t('已驳回')
  //     }
  //   },
  //   ignore: true
  // },
  {
    field: 'isLaunchProcess',
    headerText: i18n.t('是否发起索赔'),
    headerTemplate: () => {
      return {
        template: Vue.component('isLaunchProcessTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('是否发起索赔')}}</span>
              </div>
            `
        })
      }
    },
    ignore: true,
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('是'),
        0: i18n.t('否')
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <mt-select
            v-model={scoped.isLaunchProcess}
            fields={{ text: 'text', value: 'value' }}
            dataSource={isCall}
          />
        </div>
      )
    }
  },
  {
    field: 'claimNo',
    headerText: i18n.t('索赔单号'),
    allowEditing: false,
    ignore: true
  },
  {
    field: 'purchaseAmt',
    headerText: i18n.t('TCL承担金额'),
    allowEditing: false,
    ignore: true
  },
  {
    field: 'purchaserPercent',
    headerText: i18n.t('TCL承担比例'),
    allowEditing: false,
    ignore: true
  }
]

export const attachmentColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'attachmentName',
    headerText: i18n.t('附件名称'),
    cellTools: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  },
  {
    field: 'attachmentSize',
    headerText: i18n.t('附件大小'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return `${Math.floor((e / 1024) * 100) / 100}KB`
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('上传时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (typeof e == 'number') {
            return utils.formateTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('描述')
  }
]
