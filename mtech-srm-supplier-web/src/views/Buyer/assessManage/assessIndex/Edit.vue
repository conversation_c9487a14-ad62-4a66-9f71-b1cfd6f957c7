<template>
  <div>
    <mt-form ref="ruleForm" :model="dataEdit" :rules="rules" style="margin: 10px 0">
      <mt-form-item prop="standName" class="form-item" :label="$t('考核指标名称')">
        <mt-input
          v-model="dataEdit.standName"
          :disabled="disabled"
          :show-clear-button="true"
          type="text"
          maxlength="20"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
      <!-- <mt-form-item
        prop="dimensionId"
        class="form-item"
        :label="$t('指标维度')"
      >
        <mt-select
          v-model="dataEdit.dimensionId"
          @change="dimensionChange"
          :disabled="disabled"
          :dataSource="dimensionList"
          :showClearButton="true"
          :placeholder="$t('请选择指标维度')"
        ></mt-select>
      </mt-form-item> -->
      <mt-form-item prop="claimTypeCode" class="form-item" :label="$t('考核类型')">
        <mt-select
          v-model="dataEdit.claimTypeCode"
          :disabled="disabled"
          :data-source="claimTypeList"
          :show-clear-button="true"
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          @change="claimTypeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
        <mt-input
          v-model="dataEdit.remark"
          :disabled="disabled"
          :show-clear-button="true"
          :multiline="true"
          :rows="5"
          type="text"
          maxlength="500"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
    </mt-form>

    <div class="content">
      <mt-tabs
        :selected-item="0"
        :tab-id="'tabId'"
        :e-tab="false"
        :data-source="[{ title: $t('计算规则') }]"
        @handleSelectTab="handleSelectTab"
      >
        <!-- :data-source="[{ title: $t('计算规则') }, { title: $t('适用组织') }]" -->
      </mt-tabs>

      <div v-show="tabId == 0" class="content-out">
        <div v-for="(item, index) in claimStandCalcRuleList" :key="item.id">
          <div class="content-body">
            {{ $t('规则') }}{{ index + 1 }}
            <!-- <div v-if="!disabled" class="del-rule" @click="delRule(index)">
              {{ $t('删除此规则') }}
            </div> -->
            <mt-form ref="calcRuleForm" :model="item" :rules="calcRules" style="margin-top: 15px">
              <mt-form-item prop="ruleType" class="form-item" :label="$t('取值方式')">
                <mt-select
                  v-model="item.ruleType"
                  :disabled="disabled"
                  :data-source="ruleTypeList"
                  :show-clear-button="true"
                  :placeholder="$t('请选择取')"
                ></mt-select>
              </mt-form-item>

              <mt-form-item
                v-show="item.ruleType == 1"
                prop="dataSets"
                class="form-item"
                :label="$t('数据集')"
              >
                <mt-multi-select
                  mode="Box"
                  :maximum-selection-length="2"
                  :disabled="disabled"
                  v-model="item.dataSets"
                  :data-source="tableList"
                  :show-clear-button="true"
                  :placeholder="$t('请选择数据集')"
                ></mt-multi-select>
              </mt-form-item>

              <mt-form-item prop="standDesc" class="form-item" :label="$t('考核指标描述')">
                <mt-input
                  maxlength="100"
                  v-model="item.standDesc"
                  :disabled="disabled"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入')"
                ></mt-input>
              </mt-form-item>

              <!-- 待平台层提供接口，不同租户接口不同（from王磊） -->
              <!-- <mt-form-item
                      prop=""
                      class="form-item"
                      :label="$t('API')"
                      v-if="item.ruleType == 3"
                  >

                  </mt-form-item> -->
            </mt-form>

            <div v-if="item.ruleType == 1">
              <div class="content-out-put" v-for="(v, index) in item.outputRuleDTOList" :key="v.id">
                {{ $t('输出') }}{{ index + 1 }}:
                <div class="del-out-put-rule" @click="delOutPutRule(item, index)">
                  {{ $t('删除此输出') }}
                </div>
                <div
                  class="content-out-put-condition"
                  v-for="(j, _index) in v.conditionDTOList"
                  :key="j.id"
                >
                  <mt-select
                    :key="j.conditionType"
                    v-if="_index === 0"
                    v-model="j.conditionType"
                    :disabled="disabled"
                    :data-source="[{ text: $t('当'), value: 0 }]"
                  ></mt-select>

                  <mt-select
                    :key="j.conditionType"
                    v-if="_index !== 0"
                    :disabled="disabled"
                    v-model="j.conditionType"
                    :data-source="[
                      { text: $t('且'), value: 1 },
                      { text: $t('或'), value: 2 }
                    ]"
                  ></mt-select>

                  <mt-select
                    v-model="j.conditionParam"
                    :disabled="disabled"
                    class="content-out-put-param"
                    :data-source="[{ text: $t('公式'), value: '1' }]"
                  ></mt-select>

                  <mt-select
                    v-model="j.conditionSymbol"
                    :disabled="disabled"
                    :data-source="[
                      { text: '=', value: '0' },
                      { text: '>=', value: '1' },
                      { text: '>', value: '2' },
                      { text: '<', value: '3' },
                      { text: '<=', value: '4' },
                      { text: $t('空'), value: '5' },
                      { text: $t('非空'), value: '6' },
                      { text: $t('包含'), value: '7' },
                      { text: $t('不包含'), value: '8' },
                      { text: $t('等于'), value: '9' },
                      { text: $t('不等于'), value: '10' }
                    ]"
                  ></mt-select>

                  <mt-input
                    style="display: inline-block"
                    v-model="j.conditionValue"
                    :disabled="disabled"
                    type="number"
                  ></mt-input>

                  <mt-icon
                    name="icon_Close_2"
                    class="content-condition-icon"
                    @click.native="delCondition(v, _index)"
                  ></mt-icon>
                </div>
                <div v-if="!disabled" class="add-condition" @click="addCondition(v)">
                  <mt-icon name="icon_card_plus"></mt-icon>
                  {{ $t('添加输出条件') }}
                </div>
                <mt-form
                  ref="outputRuleForm"
                  :model="v"
                  :rules="outputRules"
                  style="margin-top: 15px"
                >
                  <mt-form-item prop="outputValue" class="form-item" :label="$t('输出')">
                    <mt-input
                      v-model="v.outputValue"
                      :disabled="disabled"
                      type="number"
                      :placeholder="$t('请输入输出数值')"
                    ></mt-input>
                  </mt-form-item>
                </mt-form>
              </div>
              <div v-if="!disabled" class="add-out-put-rule" @click="addOutPutRule(item)">
                {{ $t('添加输出内容') }}
              </div>
            </div>

            <div class="content-num">
              <mt-form ref="calcRuleForm" :model="item" :rules="numRules" style="margin-top: 15px">
                <mt-form-item
                  v-if="item.ruleType == 1"
                  prop="keepDecimal"
                  class="form-item"
                  :label="$t('保留小数')"
                >
                  <mt-select
                    v-model="item.keepDecimal"
                    :disabled="disabled"
                    :placeholder="$t('请选择保留小数')"
                    :data-source="[
                      { text: '0', value: 0 },
                      { text: '1', value: 1 },
                      { text: '2', value: 2 },
                      { text: '3', value: 3 }
                    ]"
                  ></mt-select>
                </mt-form-item>

                <mt-form-item prop="defaultValue" class="form-item" :label="$t('默认考核金额')">
                  <mt-inputNumber
                    v-model="item.defaultValue"
                    :disabled="disabled"
                    :show-clear-button="true"
                    type="number"
                    :min="0"
                    :placeholder="$t('请输入')"
                    @change="referChange(item)"
                  ></mt-inputNumber>
                </mt-form-item>

                <mt-form-item prop="minValue" class="form-item" :label="$t('下限值')">
                  <mt-inputNumber
                    v-model="item.minValue"
                    :disabled="disabled"
                    :show-clear-button="true"
                    type="number"
                    :min="0"
                    :placeholder="$t('请输入')"
                    oninput="if(value.length > 9) value = value.slice(0,9)"
                    @change="minChange(item)"
                  ></mt-inputNumber>
                </mt-form-item>

                <mt-form-item
                  style="margin-right: 0"
                  prop="maxValue"
                  class="form-item"
                  :label="$t('上限值')"
                >
                  <mt-inputNumber
                    v-model="item.maxValue"
                    :disabled="disabled"
                    :min="0"
                    :show-clear-button="true"
                    onkeyup="this.value= Number(String(this.value).substring(0,9))"
                    @change="maxChange(item)"
                    type="number"
                    :placeholder="$t('请输入')"
                  ></mt-inputNumber>
                </mt-form-item>
              </mt-form>
            </div>
          </div>
          <div class="line"></div>
        </div>
      </div>

      <!-- <div v-if="tabId == 0 && !disabled" class="add-rule" @click="addRule">
        {{ $t('添加计算规则') }}
      </div> -->

      <div v-if="tabId == 1">
        <range-org
          :org-tree="orgTree"
          :disabled="disabled"
          :rule-list="
            claimStandCalcRuleList.map((v, index) => {
              return $t('规则') + (index + 1)
            })
          "
          :claim-stand-range-list.sync="claimStandRangeList"
        ></range-org>
      </div>
    </div>
  </div>
</template>

<script>
import rangeOrg from './RangeOrg.vue'
export default {
  name: 'SupplyIndexEdit',
  components: {
    rangeOrg
  },
  props: {
    // 数据源
    disabled: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      ruleTypeList: [
        // { text: this.$t("规则"), value: 0 },
        // { text: this.$t("公式 "), value: 1 },
        { text: this.$t('手动填写'), value: 2 }
        // { text: this.$t("默认金额"), value: 3 },
      ],
      rules: {
        standName: [
          {
            required: true,
            message: this.$t('请输入指标名称'),
            trigger: 'blur'
          }
        ],
        dimensionId: [
          {
            required: true,
            message: this.$t('请选择指标维度'),
            trigger: 'blur'
          }
        ],
        claimTypeCode: [
          {
            required: true,
            message: this.$t('请选择指标类型'),
            trigger: 'blur'
          }
        ]
      },
      calcRules: {
        ruleType: [
          {
            required: true,
            message: this.$t('请选择取值方式'),
            trigger: 'blur'
          }
        ],
        dataSets: [{ required: true, message: this.$t('请选择数据集'), trigger: 'blur' }]
      },
      outputRules: {
        outputValue: [
          {
            required: true,
            message: this.$t('请输入输出数值'),
            trigger: 'blur'
          }
        ]
      },
      numRules: {
        keepDecimal: [
          {
            required: true,
            message: this.$t('请输入保留小数'),
            trigger: 'blur'
          }
        ],
        maxValue: [{ required: true, message: this.$t('请输入满分值'), trigger: 'blur' }],
        minValue: [{ required: true, message: this.$t('请输入最小值'), trigger: 'blur' }]
        // defaultValue: [
        //   { required: true, message: this.$t("请输入基准值"), trigger: "blur" },
        // ],
      },
      dataEdit: {},
      tabId: '',
      // 计算规则list
      claimStandCalcRuleList: [
        {
          // 计算规则名称
          ruleName: '',
          // 取值方式
          ruleType: '2',
          // 描述
          standDesc: '',
          // 数据集
          dataSets: null,
          // 公式编辑器内容
          calcRuleFormula: {},
          // 公式编辑器表、字段list
          calcRuleDataDTOList: [],
          // 保留小数
          keepDecimal: '',
          // 基准值
          defaultValue: '',
          // 最小值
          minValue: '',
          // 最大值
          maxValue: '',
          // 输出规则
          outputRuleDTOList: [
            {
              outputValue: undefined,
              conditionDTOList: [
                {
                  // 条件下拉  1公式
                  conditionParam: '1',
                  // 条件符号  0：=,1:>=,2:>,3:<,4:<=,5:空,6:非空,7:包含,8:不包含,9:等于,10:不等于
                  conditionSymbol: '0',
                  // 条件类型  0:无 1:且 2:或
                  conditionType: 0,
                  conditionValue: undefined
                }
              ]
            }
          ],
          sort: 0
        }
      ],
      // 适用组织list（后端传参）
      claimStandRangeList: [],
      // 数据集下拉
      tableList: [],
      // 指标维度下拉
      dimensionList: [],
      // 适用组织树
      orgTree: [],
      // 考核类型
      claimTypeList: [],
      // 编辑时，不清空数据集
      editability: true
    }
  },
  created() {
    window.vm = this
    this.dataEdit = this.data
    if (this.dataEdit.id) {
      this.editability = false
    }
    if (this.data.claimStandCalcRule && Object.keys(this.data.claimStandCalcRule).length > 0) {
      this.claimStandCalcRuleList = [this.data.claimStandCalcRule]
    }
    if (this.data.claimStandRangeList && this.data.claimStandRangeList.length > 0) {
      this.claimStandRangeList = this.data.claimStandRangeList
    }
    // 获取指标维度下拉列表
    // this.getDimensionList();
    // 获取适用组织树（目前为：公司TCL）
    if (this?.data?.claimTypeCode) {
      this.getOrgTree({ claimTypeCode: this.data.claimTypeCode })
    }
    this.getClaimTypeList()
  },
  methods: {
    getClaimTypeList() {
      // this.$API.assessManage.getAvailableClaimType().then((res) => {
      //   this.claimTypeList = res.data
      // })
      this.$API.assessmentTemplateSetting.dictCode({ dictCode: 'claimType' }).then((res) => {
        this.claimTypeList = res.data
      })
    },
    claimTypeChange(e) {
      if (e.value) {
        this.dataEdit.claimTypeId = e.itemData.id
        this.dataEdit.claimTypeName = e.itemData.itemName
        this.getOrgTree({ claimTypeCode: e.value })
      } else {
        this.dataEdit.claimTypeId = null
        this.dataEdit.claimTypeName = null
      }
    },
    // 获取适用组织树
    getOrgTree(param) {
      this.$API.assessManage.listCompanyByClaimType(param).then((res) => {
        if (res.code == 200) {
          if (res.data && res.data.length > 0) {
            this.orgTree = res.data
          }
        } else {
          this.$toast({
            content: res.msg || this.$t('获取组织信息失败'),
            type: 'warning'
          })
        }
      })
    },
    // 获取指标维度下拉列表
    getDimensionList() {
      this.$API.supplierIndex.getDimensionList().then((res) => {
        if (res.code == 200) {
          if (res.data && res.data.length > 0) {
            const data = res.data
            data.forEach((v) => {
              v.dimensionId = v.id
              v.text = v.dimensionName
              v.value = v.id
            })
            this.dimensionList = data
          }
        } else {
          this.$toast({
            content: res.msg || this.$t('获取指标维度信息失败'),
            type: 'warning'
          })
        }
      })
    },

    getTable(val) {
      this.$API.supplierIndex
        .queryByDimensionType({
          dimensionType: val
        })
        .then((res) => {
          if (res.code == 200) {
            if (res.data && res.data.length > 0) {
              const data = res.data
              data.forEach((v) => {
                v.text = v.tableName
                v.value = v.id
              })
              this.tableList = data
            }
          } else {
            this.$toast({
              content: res.msg || this.$t('获取指标维度信息失败'),
              type: 'warning'
            })
          }
        })
    },
    dimensionChange(v) {
      this.dataEdit = Object.assign({}, this.dataEdit, {
        dimensionId: v.itemData.dimensionId,
        dimensionCode: v.itemData.dimensionCode,
        dimensionName: v.itemData.dimensionName
      })
      this.getTable(v.itemData.dimensionType)
      if (this.editability) {
        this.claimStandCalcRuleList.forEach((item) => {
          item.dataSets = []
        })
      }
      this.editability = true
    },
    handleSelectTab(v) {
      this.tabId = v
    },
    // 添加输出条件
    addCondition(val) {
      val.conditionDTOList.push({
        // 条件下拉  1公式
        conditionParam: '1',
        // 条件符号  0：=,1:>=,2:>,3:<,4:<=,5:空,6:非空,7:包含,8:不包含,9:等于,10:不等于
        conditionSymbol: '0',
        // 条件类型  0:无 1:且 2:或
        conditionType: 0,
        conditionValue: undefined
      })
    },
    // 删除输出条件
    delCondition(val, index) {
      if (val.conditionDTOList.length > 1) {
        val.conditionDTOList.splice(index, 1)
      }
    },
    // 添加输出内容
    addOutPutRule(val) {
      if (val.outputRuleDTOList.length === 5) {
        this.$toast({
          content: this.$t('输出内容最多可添加5条'),
          type: 'warning'
        })
        return
      }
      val.outputRuleDTOList.push({
        outputValue: undefined,
        conditionDTOList: [
          {
            // 条件下拉  1公式
            conditionParam: '1',
            // 条件符号  0：=,1:>=,2:>,3:<,4:<=,5:空,6:非空,7:包含,8:不包含,9:等于,10:不等于
            conditionSymbol: '0',
            // 条件类型  0:无 1:且 2:或
            conditionType: 0,
            conditionValue: undefined
          }
        ]
      })
    },
    // 删除此输出
    delOutPutRule(val, index) {
      if (val.outputRuleDTOList.length > 1) {
        val.outputRuleDTOList.splice(index, 1)
      }
    },
    // 添加计算规则
    addRule() {
      if (this.claimStandCalcRuleList.length === 5) {
        this.$toast({
          content: this.$t('计算规则最大条数为5条'),
          type: 'warning'
        })
        return
      }
      this.claimStandCalcRuleList.push({
        // 取值方式
        ruleType: '',
        // 描述
        standDesc: '',
        // 数据集
        dataSets: [],
        // 公式编辑器内容
        calcRuleFormula: {},
        // 公式编辑器表、字段list
        calcRuleDataDTOList: [],
        // 输出规则
        outputRuleDTOList: [
          {
            outputValue: undefined,
            conditionDTOList: [
              {
                // 条件下拉  1公式
                conditionParam: '1',
                // 条件符号  0：=,1:>=,2:>,3:<,4:<=,5:空,6:非空,7:包含,8:不包含,9:等于,10:不等于
                conditionSymbol: '0',
                // 条件类型  0:无 1:且 2:或
                conditionType: 0,
                conditionValue: undefined
              }
            ]
          }
        ],
        sort: this.claimStandCalcRuleList.length
      })
    },
    // 删除此规则
    delRule(index) {
      this.claimStandCalcRuleList.splice(index, 1)
    },
    // 基准值变化
    referChange(item) {
      if (!item.defaultValue) {
        return
      }
      if (item.minValue && Number(item.minValue) > Number(item.defaultValue)) {
        item.defaultValue = null
        this.$toast({
          content: this.$t('基准值不得小于最小值'),
          type: 'warning'
        })
        return
      }
      if (item.maxValue && Number(item.maxValue) < Number(item.defaultValue)) {
        item.defaultValue = null
        this.$toast({
          content: this.$t('基准值不得大于满分值'),
          type: 'warning'
        })
        return
      }
    },
    // 最小变化
    minChange(item) {
      if (!item.minValue) {
        return
      }
      // item.defaultValue=item.minValue
      if (item.defaultValue && Number(item.minValue) > Number(item.defaultValue)) {
        item.minValue = null
        this.$toast({
          content: this.$t('最小值不得大于基准值'),
          type: 'warning'
        })
        return
      }
      if (item.maxValue && Number(item.minValue) > Number(item.maxValue)) {
        item.minValue = null
        this.$toast({
          content: this.$t('最小值不得大于满分值'),
          type: 'warning'
        })
        return
      }
    },
    // 满分值变化
    maxChange(item) {
      if (!item.maxValue) {
        return
      }
      if (item.defaultValue && Number(item.maxValue) < Number(item.defaultValue)) {
        item.maxValue = null
        this.$toast({
          content: this.$t('满分值不得小于基准值'),
          type: 'warning'
        })
        return
      }
      if (item.minValue && Number(item.minValue) > Number(item.maxValue)) {
        item.maxValue = null
        this.$toast({
          content: this.$t('满分值不得小于最小值'),
          type: 'warning'
        })
        return
      }
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  margin-top: 15px;
  font-size: 14px;
  .content-out {
    padding-bottom: 20px;
    > div {
      margin-top: 20px;
    }
    .content-body {
      background-color: #f5f6f9;
      border-radius: 6px;
      padding: 10px 10px 20px 10px;
      font-size: 14px;
      .content-num {
        .mt-form > div {
          width: calc((100% - 60px) / 4);
          margin-right: 20px;
          display: inline-block;
        }
      }
      .del-rule {
        float: right;
        color: #ed5633;
        cursor: pointer;
      }
    }
    .line {
      margin-top: 24px;
      width: 100%;
      height: 1px;
      background-color: #e8e8e8;
    }
  }
  .add-rule {
    color: #00469c;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
  }
  .content-out-put {
    margin-top: 24px;
    .content-out-put-condition > div {
      width: calc((65% - 100px) / 3);
      margin-right: 20px;
      margin-top: 10px;
    }
    .content-condition-icon {
      color: #9baac1;
      cursor: pointer;
    }
    .content-out-put-param {
      width: 35% !important;
    }
    .add-condition {
      color: #6386c1;
      margin-top: 10px;
      cursor: pointer;
      display: inline-block;
    }
    .del-out-put-rule {
      float: right;
      color: #ed5633;
      cursor: pointer;
    }
  }
  .add-out-put-rule {
    color: #00469c;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
  }
}
::v-deep .mt-tabs {
  .tab-container {
    background-color: white;
  }
  .tabs-arrow {
    display: none;
  }
}
</style>
