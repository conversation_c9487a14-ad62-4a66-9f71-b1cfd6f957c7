<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { idleMaterialCols } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: 'e8e3fcd7-de74-4a16-9767-3ae151651958',
          title: this.$t('待提交'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                'Add',
                'Edit',
                {
                  id: 'publish',
                  icon: 'icon_table_new',
                  title: this.$t('提交')
                },
                'Delete',
                // {
                //   id: 'audit',
                //   title: this.$t('查看OA审批'),
                //   icon: 'icon_solid_editsvg'
                // },
                { id: 'Download', icon: 'icon_solid_Download', title: this.$t('导出') },
                {
                  id: 'print',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            showSelected: false,
            columnData: idleMaterialCols,
            asyncConfig: {
              url: '/analysis/tenant/idleMaterialClaimVoucher/headerPageQuery',
              params: { type: 1 }
            }
          }
        },
        {
          gridId: 'be999567-871f-48e4-8e90-3f2b99d154c1',
          title: this.$t('索赔单汇总'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                { id: 'Download1', icon: 'icon_solid_Download', title: this.$t('导出') },
                {
                  id: 'print1',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                },
                {
                  id: 'deduct',
                  title: this.$t('确认扣款')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: idleMaterialCols,
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            showSelected: false,
            asyncConfig: {
              url: '/analysis/tenant/idleMaterialClaimVoucher/headerPageQuery',
              params: { type: 2 }
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(item) {
      // let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      let records = item.gridRef.getCustomSelectedRows()

      if (
        records.length <= 0 &&
        !(
          item.toolbar.id == 'Add' ||
          item.toolbar.id == 'Filter' ||
          item.toolbar.id == 'Refresh' ||
          item.toolbar.id == 'Setting' ||
          item.toolbar.id == 'audit' ||
          item.toolbar.id == 'Download' ||
          item.toolbar.id == 'Download1' ||
          item.toolbar.id == 'reject'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          data: {
            title: this.$t('新建索赔单')
          },
          success: (res) => {
            if (res.type === 'confirm') {
              this.$refs.templateRef.refreshCurrentGridData()
            } else {
              this.$router.push({
                name: 'idle-material-claimsListDetail',
                query: {
                  type: 1,
                  id: res.id
                }
              })
            }
          }
        })
      } else if (item.toolbar.id == 'Edit') {
        if (
          records.some(
            (item) =>
              item.status != 1 &&
              item.status != 4 &&
              item.status != 6 &&
              item.status != 14 &&
              item.status != 15
          )
        ) {
          this.$toast({
            content: this.$t('非新建、已盖章或审批拒绝的数据不可编辑'),
            type: 'warning'
          })
          return
        }
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
          return
        } else if (records.some((item) => item.createType == 1)) {
          this.$toast({
            content: this.$t('自动创建的数据无法编辑'),
            type: 'warning'
          })
          return
        } else {
          // this.$dialog({
          //   modal: () => import('./components/addDialog.vue'),
          //   data: {
          //     title: this.$t('编辑索赔单'),
          //     isEdit: true,
          //     info: records[0]
          //   },
          //   success: (res) => {
          //     if (res.type === 'confirm') {
          //       this.$refs.templateRef.refreshCurrentGridData()
          //     } else {
          //       this.$router.push({
          //         name: 'idle-material-claimsListDetail',
          //         query: {
          //           type: res.data.status,
          //           id: res.data.id
          //         }
          //       })
          //     }
          //   }
          // })
          let data = records[0]
          this.$router.push({
            name: 'idle-material-claimsListDetail',
            query: {
              type: data.status,
              id: data.id
            }
          })
        }
      } else if (item.toolbar.id == 'publish') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能提交一行'), type: 'warning' })
          return
        }
        if (
          records.some(
            (item) =>
              item.status != 1 &&
              item.status != 4 &&
              item.status != 6 &&
              item.status != 14 &&
              item.status != 15
          )
        ) {
          this.$toast({
            content: this.$t('非新建、已盖章或审批拒绝的数据无法提交'),
            type: 'warning'
          })
          return
        }
        let _idList = records.map((e) => e.id)
        this.submitRecord(_idList)
      } else if (item.toolbar.id == 'Delete') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能删除一行'), type: 'warning' })
          return
        }
        if (records.some((item) => ![1, 3, 6, 7, 14].includes(item.status))) {
          this.$toast({
            content: this.$t('非新建、或审批拒绝的数据无法删除'),
            type: 'warning'
          })
          return
        }
        let _idList = records.map((e) => e.id)
        this.deleteRecord(_idList)
      } else if (item.toolbar.id == 'audit') {
        this.audit(records)
      } else if (item.toolbar.id == 'deduct') {
        this.deduct(records)
      } else if (item.toolbar.id == 'print') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能打印单行'), type: 'warning' })
          return
        }
        // if (records[0]['status'] == 0 || records[0]['status'] == 1 || records[0]['status'] == 3) {
        //   this.$toast({ content: this.$t('新建、已提交、审批拒绝状态不能打印'), type: 'warning' })
        //   return
        // }
        this.printPdf(records[0].id)
      } else if (item.toolbar.id == 'print1') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能打印单行'), type: 'warning' })
          return
        }
        // if (records[0]['status'] == 0 || records[0]['status'] == 1 || records[0]['status'] == 3) {
        //   this.$toast({ content: this.$t('新建、已提交、审批拒绝状态不能打印'), type: 'warning' })
        //   return
        // }
        this.printPdf(records[0].id)
      } else if (item.toolbar.id == 'Download') {
        this.handleClickDownload(1)
      } else if (item.toolbar.id == 'Download1') {
        this.handleClickDownload(2)
      } else if (item.toolbar.id == 'reject') {
        this.handleClickReject(records)
      }
    },
    handleClickReject(records) {
      if (records.length > 1) {
        this.$toast({ content: this.$t('只能驳回一行'), type: 'warning' })
        return
      }
      console.log('驳回-----', records)
    },
    //导出
    handleClickDownload(number) {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        type: number,
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        rules: rule.rules || []
      }
      this.$API.deadMaterials.exportClaimVoucher(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (['edit', 'delete'].includes(tool.id)) {
        const { status } = data
        if (status != 1 && status != 4 && status != 6 && status != 14 && status != 15) {
          this.$toast({
            content: this.$t('非新建、已盖章或审批拒绝的数据不可操作'),
            type: 'warning'
          })
          return
        }
        if (tool.id === 'edit') {
          // this.$dialog({
          //   modal: () => import('./components/addDialog.vue'),
          //   data: {
          //     title: this.$t('编辑索赔单'),
          //     isEdit: true,
          //     info: data
          //   },
          //   success: (res) => {
          //     if (res.type === 'confirm') {
          //       this.$refs.templateRef.refreshCurrentGridData()
          //     } else {
          //       this.$router.push({
          //         name: 'idle-material-claimsListDetail',
          //         query: {
          //           type: res.data.status,
          //           id: res.data.id
          //         }
          //       })
          //     }
          //   }
          // })
          // let data = records[0]
          this.$router.push({
            name: 'idle-material-claimsListDetail',
            query: {
              type: data.status,
              id: data.id
            }
          })
        }
        tool.id === 'delete' && this.deleteRecord([data.id])
      }
    },
    async printPdf(data) {
      const formdata = new FormData()
      formdata.append('id', data)
      let buffer = await this.$API.deadMaterials.purchaserPrintClaim(formdata).catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    submitRecord(ids) {
      const formdata = new FormData()
      formdata.append('id', ids[0])
      this.$API.deadMaterials.submitHeader(formdata).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteRecord(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？'),
          confirm: () => this.$API.deadMaterials.deleteClaimVoucher(ids[0])
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    audit(ids) {
      if (ids.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (ids.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let businessType = ''
      if (ids[0].status === 1)
        return this.$toast({ content: this.$t('当前状态还未提交OA审批'), type: 'warning' })
      if (ids[0].status === 2 || ids[0].status === 3 || ids[0].status === 4) {
        businessType = 'IDLE_MATERIAL_CLAIM_COMPENSATION_SUBMIT'
      } else if (
        ids[0].status === 5 ||
        ids[0].status === 6 ||
        ids[0].status === 7 ||
        ids[0].status === 8 ||
        ids[0].status === 9 ||
        ids[0].status === 10
      ) {
        businessType = 'IDLE_MATERIAL_CLAIM_SUBMIT'
      } else if (ids[0].status === 13 || ids[0].status === 14 || ids[0].status === 15) {
        businessType = 'idle_claim_auto_stamp'
      }
      const params = {
        businessType,
        applyId: ids[0].id
      }
      this.$API.deadMaterials.deadMaterialsOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    deduct(ids) {
      if (ids.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (ids.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      this.$API.deadMaterials.deduct(ids[0].id).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('扣款成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      if (e.field == 'claimCode') {
        let { data } = e
        this.$router.push({
          name: 'idle-material-claimsListDetail',
          query: {
            type: data.status,
            id: data.id
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
.dialog-content {
  margin-top: 20px;
}
::v-deep {
  .template_checkbox {
    text-align: center;
    input[type='checkbox'] {
      visibility: visible;
    }
  }
}
</style>
