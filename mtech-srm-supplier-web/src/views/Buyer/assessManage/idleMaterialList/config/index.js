import { i18n } from '@/main.js'
// import Vue from 'vue'
// import utils from '@/utils/utils'
import dayjs from 'dayjs'

export const idleMaterialCols = [
  // {
  //   width: '50',
  //   type: 'checkbox'
  // },
  {
    field: 'sourceNo',
    headerText: i18n.t('索赔清单编码'),
    width: '150',
    formatter: (column, data) => {
      return data?.sourceNo || '-'
    }
  },
  {
    field: 'claimCode',
    headerText: i18n.t('索赔单编码'),
    cellTools: [
      {
        id: 'edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          // return data.status == 1 || data.status == 4 || data.status == 7
          return [1, 3, 4, 6, 11, 14, 15].includes(data.status)
        }
      },
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          // return data.status == 1 || data.status == 4 || data.status == 7
          return [1, 3, 6, 7, 14].includes(data.status)
        }
      }
    ]
  },
  {
    field: 'claimDimension',
    headerText: i18n.t('索赔维度'),
    valueConverter: {
      type: 'map',
      map: {
        S01: i18n.t('质量'),
        S02: i18n.t('服务'),
        S03: i18n.t('返利'),
        S04: i18n.t('呆料索赔'),
        S05: i18n.t('冲销'),
        S06: i18n.t('品质回用'),
        S08: i18n.t('交货考核'),
        S09: i18n.t('停产索赔'),
        S10: i18n.t('生产索赔'),
        S11: i18n.t('违规索赔')
      }
    }
  },
  // {
  //   field: 'reason',
  //   headerText: i18n.t('索赔指标')
  // },
  // {
  //   field: 'claimDimension',
  //   headerText: i18n.t('考核类型'),
  //   template: function () {
  //     return {
  //       template: Vue.component('claimDimension', {
  //         template: `<span>考核类型</span>`
  //       })
  //     }
  //   },
  //   ignore: true
  // },
  // {
  //   field: 'claimDimension',
  //   headerText: i18n.t('索赔指标'),
  //   ignore: true
  // },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司'),
    width: '220'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '220'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '220'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('新建'),
        2: i18n.t('赔付比例审批中'),
        3: i18n.t('赔付比例审批拒绝'),
        4: i18n.t('赔付比例审批通过'),
        5: i18n.t('索赔单待审批'),
        6: i18n.t('已废弃'),
        7: i18n.t('索赔单审批拒绝'),
        8: i18n.t('扣款失败'),
        9: i18n.t('已完成'),
        10: i18n.t('已入账'),
        11: i18n.t('赔付比例维护中'),
        13: i18n.t('索赔用章审批中'),
        14: i18n.t('索赔用章审批拒绝'),
        15: i18n.t('索赔用章审批通过')
      }
    }
  },
  {
    field: 'source',
    headerText: i18n.t('单据来源'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('呆料清单创建'),
        1: i18n.t('手动创建')
      }
    }
  },
  {
    field: 'purchaseAmt',
    headerText: i18n.t('TCL承担损失'),
    ignore: true,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (typeof e === 'number') {
          return e.toFixed(2)
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'supplierAmt',
    headerText: i18n.t('供应商承担损失'),
    ignore: true,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (typeof e === 'number') {
          return e.toFixed(2)
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'bookkeepingDate',
    headerText: i18n.t('记账日期'),
    valueConverter: {
      type: 'function', //filter可不传，如果未传，则原数据返回
      filter: (data) => {
        let formatDate = ''
        if (data) {
          formatDate = dayjs(data).format('YYYY-MM-DD')
        } else if (!data) {
          formatDate = '-'
        }
        return formatDate
      }
    },
    width: '220',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x) => {
          return dayjs(x).format('YYYY-MM-DD')
        })
      }
    }
  },
  {
    field: 'deductVoucherNo',
    headerText: i18n.t('扣款凭证号'),
    width: '220'
  },
  {
    field: 'accountDate',
    headerText: i18n.t('过账月份'),
    width: '220'
  },
  {
    field: 'sapDeductVoucherNo',
    headerText: i18n.t('过账凭证'),
    width: '220'
  },

  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '220'
  },
  {
    width: '130',
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(******** - 1440000)

        //自定义搜索值，规则
        return obj
      },
      renameField: 'createTime'
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '220'
  }
]
