<template>
  <mt-dialog
    ref="addDialog"
    :buttons="buttons"
    :header="modalData.title"
    width="500"
    height="300"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="formDataRef" :model="formData" :rules="rules" :auto-complete="false">
        <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
          <RemoteAutocomplete
            v-model="formData.companyCode"
            :remote-search="true"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            select-type="administrativeCompany"
            :placeholder="$t('请选择所属公司')"
            @change="(e) => handleChange('company', e)"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
          <RemoteAutocomplete
            v-model="formData.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierCode', 'supplierName']"
            :placeholder="$t('请选择供应商')"
            @change="(e) => handleChange('supplier', e)"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {},
      rules: {
        companyCode: [{ required: true, message: this.$t('请选择所属公司'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmAndEnter,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ]
    }
  },
  mounted() {
    this.$refs.addDialog?.ejsRef?.show()
  },
  methods: {
    handleChange(key, e) {
      const { orgName, supplierName } = e?.itemData || {}
      switch (key) {
        case 'company':
          this.formData.companyName = orgName
          break
        case 'supplier':
          this.formData.supplierName = supplierName
          break
        default:
          break
      }
    },
    // 取消
    cancel() {
      this.$emit('cancel-function')
    },
    // 保存
    confirm() {
      this.handleSave('confirm')
    },
    // 保存并进入
    confirmAndEnter() {
      this.handleSave('confirmAndEnter')
    },
    // 保存
    handleSave(type) {
      this.$refs.formDataRef.validate(async (valid) => {
        if (valid) {
          const res = await this.$API.deadMaterials.createClaimVoucherManual(this.formData)
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$emit('confirm-function', { type, id: res.data })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  font-size: 16px;
}
</style>
