import { i18n } from '@/main.js'
import Select from '../editComponents/Select.vue'
import Input from '../editComponents/Input.vue'
import cellChanged from '../editComponents/cellChanged.vue'
import priceCellChange from '../editComponents/priceCellChange.vue'
import percentCellChange from '../editComponents/percentCellChange.vue'
import debounce from 'lodash.debounce'

import bus from '@/utils/bus'
import Vue from 'vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  }
}

export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}

export const assessmentItemColumn = (that) => [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'addId',
    headerText: 'addId',
    width: '0',
    allowEditing: false,
    visible: false
  },
  {
    field: 'siteAndItemCode',
    headerText: i18n.t('工厂+ 物料+ 采购组织'),
    editTemplate: () => {
      return {
        template: Vue.component('siteAndItemCode', {
          template: `
              <mt-select
              :id="data.column.field"
              v-model="data[data.column.field]"
              :data-source="dataSource"
              :fields="fields"
              :popup-width="750"
              :placeholder="placeholder"
              @change="selectChange"
              :open-dispatch-change="false"
              :disabled="isDisabled"
              :allow-filtering="true"
              :filtering="serchText"
            ></mt-select>
          `,
          data() {
            return {
              data: {},
              placeholder: this.$t('请选择'),
              fields: { text: 'siteAndItemName', value: 'siteAndItemCode' },
              dataSource: [],
              isDisabled: false
            }
          },
          mounted() {
            const gridRef = that.$refs.assessIndexTemplateRef?.getCurrentUsefulRef().gridRef.ejsRef
            const indexList = gridRef?.getCurrentViewRecords() || []
            this.dataSource = []
            if (indexList && indexList.length) {
              indexList.map((i) => {
                const siteAndItemCode = `${i.factoryCode} - ${i.materialCode} - ${i.purchaseOrgCode}`
                const siteAndItemName = `${i.factoryName} - ${i.materialDesc} - ${i.purchaseOrgName}`
                this.dataSource.push({
                  ...i,
                  indexItemId: i.id,
                  siteAndItemCode,
                  siteAndItemName
                })
              })
            }
          },
          methods: {
            serchText(val) {
              val.updateData(
                this.dataSource.filter(
                  (e) =>
                    e[this.fields.value].includes(val.text) ||
                    e[this.fields.text].includes(val.text)
                )
              )
            },
            selectChange(val) {
              if (val.itemData.unitPrice) {
                bus.$emit('isHavaUnitPriceChange', 2)
              } else {
                bus.$emit('isHavaUnitPriceChange', 1)
              }
              console.log('12323', val)
              this.$set(this.data, 'siteAndItemCode', val.value)
              bus.$emit('indexItemLineNumberChange', val.itemData?.lineNumber)
              bus.$emit('indexItemIdChange', val.itemData.indexItemId)
              bus.$emit('factoryNameChange', val.itemData.factoryName)
              bus.$emit('factoryCodeChange', val.itemData.factoryCode)
              bus.$emit('materialCodeChange', val.itemData.materialCode)
              bus.$emit('materialDescChange', val.itemData.materialDesc)
              bus.$emit('categoryNameChange', val.itemData.categoryName)
              bus.$emit('categoryCodeChange', val.itemData.categoryCode)
              bus.$emit('purchaseOrgNameChange', val.itemData.purchaseOrgName)
              bus.$emit('purchaseOrgCodeChange', val.itemData.purchaseOrgCode)
              bus.$emit('currencyChange', val.itemData.currency)
              bus.$emit('costCenterChange', val.itemData.costCenter)
              bus.$emit('unitPriceChange', val.itemData.unitPrice)
            }
          }
        })
      }
    }
  },
  {
    field: 'isHavaUnitPrice',
    headerText: i18n.t('是否可编辑单价'),
    width: 0.1,
    // editTemplate: () => {
    //   return { template: Select }
    // }
    editTemplate: () => {
      return { template: priceCellChange }
    }
  },
  {
    field: 'indexItemId',
    headerText: i18n.t('指标行id'),
    width: 0.1,
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'indexItemLineNumber',
    headerText: i18n.t('索赔指标行号'),
    width: 0,
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂编码'),
    // editTemplate: () => {
    //   return { template: Select }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'factoryName',
    headerText: i18n.t('工厂名称'),
    // editTemplate: () => {
    //   return { template: Select }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    editTemplate: () => {
      return { template: cellChanged }
    },
    ignore: true
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    editTemplate: () => {
      return { template: cellChanged }
    },
    ignore: true
  },
  {
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织编码'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  // {
  //   field: 'factoryCode',
  //   width: 0.1,
  //   headerText: i18n.t('物料编码'),
  //   // editTemplate: () => {
  //   //   return {
  //   //     template: selectedItemCodeNew
  //   //   }
  //   // }
  //   editTemplate: () => {
  //     return { template: cellChanged }
  //   }
  // },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码'),
    // editTemplate: () => {
    //   return {
    //     template: selectedItemCodeNew
    //   }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'materialDesc',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'materialStatus',
    headerText: i18n.t('物料状态'),
    headerTemplate: () => {
      return {
        template: Vue.component('qtyTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料状态')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('siteAndItemCode', {
          template: `
            <mt-input :id="data.column.field" v-model="data.materialStatus" :disabled="isMaterialEditable"></mt-input>
          `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            isMaterialEditable() {
              const status = this.$route.query.type
              if (status === 11) {
                return true
              } else {
                return false
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'transferPlanCode',
    headerText: i18n.t('采购员编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('qtyTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购员编码')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('siteAndItemCode', {
          template: `
              <mt-select
              :id="data.column.field"
              v-model="data.transferPlanCode"
              :data-source="auditManList"
              :fields="{
                text: 'employeeNameCode',
                value: 'externalCode'
              }"
              :placeholder="$t('请选择采购员')"
              :allow-filtering="true"
              :filtering="getAuditMan"
              :open-dispatch-change="true"
              :disabled="isEditable"
              @change="selectChange"
            ></mt-select>
          `,
          data() {
            return {
              data: {},
              placeholder: this.$t('请选择'),
              auditManList: []
            }
          },
          computed: {
            isEditable() {
              const status = this.$route.query.type
              if (status === 11) {
                return true
              } else {
                return false
              }
            }
          },
          created() {
            this.getAuditMan = debounce(this.getAuditMan, 1000)
          },
          mounted() {
            this.getAuditMan(this.data.transferPlanCode)
            // this.auditManList = [
            //   {
            //     employeeNameCode: this.data.transferPlanCode + '-' + this.data.transferPlanName
            //   }
            // ]
          },
          methods: {
            getAuditMan(val) {
              let params = {
                page: {
                  current: 1,
                  size: 20
                },
                subjectType: 0
              }
              if (val) {
                params['condition'] = 'or'
                params['rules'] = [
                  {
                    label: '账号',
                    field: 'externalCode',
                    type: 'string',
                    operator: 'contains',
                    value: val.text || val
                  },
                  {
                    label: '姓名',
                    field: 'employeeName',
                    type: 'string',
                    operator: 'contains',
                    value: val.text || val
                  }
                ]
              }
              this.$API.purChangeRequest.employee(params).then((res) => {
                if (res.code == 200 && res.data.records != null) {
                  const auditManList = res.data.records.map((item) => {
                    item.employeeNameCode = item.externalCode + '-' + item.employeeName
                    return item
                  })
                  this.auditManList = auditManList
                  this.noRecordsTemplate = '没有找到记录'
                }
              })
            },
            selectChange(val) {
              bus.$emit('transferPlanNameChange', val.itemData.employeeName)

              // this.$set(this.data, 'transferPlanCode', val.value.split('-')[0])
              // this.$set(this.data, 'transferPlanName', val.value.split('-')[1])
            }
          }
        })
      }
    }
  },
  {
    field: 'transferPlanName',
    headerText: i18n.t('采购员名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  // {
  //   field: 'claimDate',
  //   headerText: i18n.t('索赔日期'),
  //   template: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `<span>{{ getTimeFmt() }}</span>`,
  //         methods: {
  //           getTimeFmt() {
  //             if (this.data[this.data.column.field]) {
  //               return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
  //             }
  //             return ''
  //           }
  //         }
  //       })
  //     }
  //   },
  //   editTemplate: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `
  //             <mt-date-picker
  //               :id="data.column.field"
  //               v-model="data[data.column.field]"
  //               :open-on-focus="true"
  //               :allow-edit="false"
  //               :placeholder="$t('请选择索赔日期')"
  //             ></mt-date-picker>
  //           `
  //       })
  //     }
  //   }
  // },
  {
    field: 'qty',
    headerText: i18n.t('实际盘点数量'),
    headerTemplate: () => {
      return {
        template: Vue.component('qtyTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('实际盘点数量')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('siteAndItemCode', {
          template: `
            <mt-input :id="data.column.field" v-model="data.qty" type="number" :disabled="isQtyEditable"></mt-input>
          `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            isQtyEditable() {
              const status = this.$route.query.type
              if (status === 11) {
                return true
              } else {
                return false
              }
            }
          }
        })
      }
    }
    // editType: 'numericedit',
    // edit: {
    //   params: {
    //     min: 0,
    //     decimals: 3,
    //     format: '###',
    //     validateDecimalOnType: true,
    //     htmlAttributes: { type: 'number' },
    //     showSpinButton: false
    //   }
    // }
  },
  {
    field: 'currency',
    headerText: i18n.t('币种'),
    // editTemplate: () => {
    //   return { template: Select }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'unitPrice',
    headerText: i18n.t('单价'),
    editTemplate: () => {
      return { template: Input }
    }
    // editTemplate: () => {
    //   return { template: priceCellChange }
    // },
    // editType: 'numericedit',
    // edit: {
    //   params: {
    //     min: 0,
    //     decimals: 4,
    //     format: '###.##',
    //     validateDecimalOnType: true,
    //     htmlAttributes: { type: 'number' },
    //     showSpinButton: false
    //   }
    // }
  },
  {
    field: 'actualAmt',
    headerText: i18n.t('实际损失金额'),
    allowEditing: false
  },
  {
    field: 'purchaserPercent',
    headerText: i18n.t('TCL承担比例'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaserPercentTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('TCL承担比例')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: percentCellChange }
    }
  },
  {
    field: 'purchaseAmt',
    headerText: i18n.t('TCL承担损失'),
    allowEditing: false
  },
  {
    field: 'supplierPercent',
    headerText: i18n.t('供应商承担比例'),
    headerTemplate: () => {
      return {
        template: Vue.component('supplierPercentTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商承担比例')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: percentCellChange }
    }
  },
  {
    field: 'supplierAmt',
    headerText: i18n.t('供应商承担损失'),
    allowEditing: false
  },
  {
    field: 'costCenter',
    headerText: i18n.t('成本中心代码'),
    editTemplate: () => {
      return { template: cellChanged }
    },
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('采购备注')
  },
  {
    field: 'purchaserRemark',
    headerText: i18n.t('物控备注')
  }
]
export const assessmentIndexColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'addId',
    headerText: 'addId',
    width: '0',
    allowEditing: false,
    visible: false
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂代码'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'factoryName',
    headerText: i18n.t('工厂名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'materialDesc',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织编码'),
    editTemplate: () => {
      return { template: Select, params: { companyId: 'companyId' } }
    }
  },
  {
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'qty',
    headerText: i18n.t('在途数量'),
    editType: 'numericedit',
    edit: {
      params: {
        min: 0,
        decimals: 3,
        format: '###',
        validateDecimalOnType: true,
        htmlAttributes: { type: 'number' },
        showSpinButton: false
      }
    }
  },
  {
    field: 'currency',
    headerText: i18n.t('币种'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'unitPrice',
    headerText: i18n.t('单价'),
    editTemplate: () => {
      return { template: Input }
    }
    // editType: 'numericedit',
    // edit: {
    //   params: {
    //     min: 0,
    //     decimals: 3,
    //     format: '###.###',
    //     validateDecimalOnType: true,
    //     htmlAttributes: { type: 'number' },
    //     showSpinButton: false
    //   }
    // }
  },
  {
    field: 'costCenter',
    headerText: i18n.t('成本中心代码'),
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('成本中心代码')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const attachmentColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'attachmentName',
    headerText: i18n.t('附件名称'),
    cellTools: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  },
  {
    field: 'attachmentSize',
    headerText: i18n.t('附件大小'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return `${Math.floor((e / 1024) * 100) / 100}KB`
      }
    },
    allowEditing: false
  },
  {
    field: 'uploadUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
