<template>
  <div>
    <!-- 品类编码 -->
    <RemoteAutocomplete
      v-if="data.column.field === 'categoryCode'"
      v-model="data[data.column.field]"
      id="categoryCode"
      url="/masterDataManagement/tenant/category/paged-query"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :fields="{ value: 'categoryCode', text: 'categoryName' }"
      :search-fields="['categoryCode', 'categoryName']"
      :title-switch="false"
      :placeholder="$t('请选择品类')"
      popup-width="300"
      @change="selectChange"
    />
    <!-- 物料编码 -->
    <RemoteAutocomplete
      v-else-if="data.column.field === 'materialCode'"
      v-model="data[data.column.field]"
      id="materialCode"
      records-position="data"
      :url="$API.masterData.getItemFuzzyQueryUrl"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :fields="{ value: 'itemCode', text: 'itemName' }"
      params-key="fuzzyNameOrCode"
      popup-width="300"
      :title-switch="false"
      :placeholder="$t('请选择物料')"
      @change="selectChange"
    />
    <!-- 采购组织编码 -->
    <RemoteAutocomplete
      v-else-if="data.column.field === 'purchaseOrgCode'"
      id="purchaseOrgCode"
      method="get"
      records-position="data"
      :url="$API.masterData.getBusinessOrganizationUrl"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :remote-search="false"
      v-model="data[data.column.field]"
      :params="{
        organizationId: data.companyId
      }"
      :fields="{ value: 'organizationCode', text: 'organizationName' }"
      :title-switch="false"
      :placeholder="$t('请选择采购组织')"
      select-type="businessOrganization"
      popup-width="300"
      @change="selectChange"
    />
    <!-- 成本中心 -->
    <RemoteAutocomplete
      v-else-if="data.column.field === 'costCenter'"
      v-model="data[data.column.field]"
      id="costCenter"
      :url="$API.masterData.getCostCenterUrl"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :fields="{ value: 'costCenterCode', text: 'costCenterDesc' }"
      :search-fields="['costCenterCode', 'costCenterDesc']"
      :title-switch="false"
      :placeholder="$t('请选择成本中心')"
      popup-width="300"
      @change="selectChange"
    />
    <mt-select
      v-else
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
      popup-width="300"
    />
  </div>
</template>
<script>
import bus from '@/utils/bus'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      costCenter: '',
      isDisabled: false,
      factoryCode: ''
    }
  },
  async mounted() {
    if (this.data.column.field === 'currency') {
      // 币种
      this.getCurrencyList()
      this.fields = { text: 'text', value: 'currencyCode' }
    }
    if (this.data.column.field === 'taxInclusiveName') {
      // bus.$emit("taxInclusiveNameChange", this.data.taxInclusiveName);
      this.dataSource = [
        { label: this.$t('否'), value: false },
        { label: this.$t('是'), value: true }
      ]
      this.fields = { text: 'label', value: 'label' }
    }
    if (this.data.column.field === 'unitName' || this.data.column.field === 'refResUnitName') {
      this.getUnitList()
      this.fields = { text: 'unitName', value: 'unitName' }
    }
    if (this.data.column.field === 'taxTypeName') {
      console.log('this.data.taxInclusiveName', this.data)
      if (!this.data.taxInclusive) {
        this.isDisabled = true
      }
      this.$bus.$on('taxInclusiveNameChange', (val) => {
        console.log('taxInclusiveNameChange=', val)
        this.isDisabled = !val.itemData.value
        if (!val.itemData.value) {
          this.data.taxTypeName = null
        }
      })
      this.getTaxItemList()
      this.fields = { text: 'taxItemName', value: 'taxItemName' }
    }
    if (this.data.column.field === 'standName') {
      this.getAssessIndexList()
      this.fields = { text: 'standName', value: 'standName' }
    }
    if (this.data.column.field === 'factoryCode') {
      if (this?.data?.siteId) {
        sessionStorage.setItem('organizationId', this.data.siteId)
      }
      this.getFactoryList()
      this.fields = { text: 'text', value: 'siteCode' }
    }
  },
  methods: {
    getFactoryList() {
      let _company = JSON.parse(sessionStorage.getItem('inspectionSheetCompany'))
        ? JSON.parse(sessionStorage.getItem('inspectionSheetCompany'))
        : ''
      this.$API.masterData
        .getSiteList({
          condition: '',
          page: {
            current: 1,
            size: 1000
          },
          defaultRules: [
            {
              label: this.$t('公司'),
              field: 'parentCode',
              type: 'string',
              operator: 'equal',
              value: _company.companyCode ? _company.companyCode : ''
            }
          ],
          pageFlag: false
        })
        .then((res) => {
          res.data.records?.forEach((item) => {
            const { siteCode, siteName } = item
            item.text = siteCode + '-' + siteName
          })
          this.dataSource = res.data.records
        })
    },
    getAssessIndexList() {
      let _claimTypeCode = sessionStorage.getItem('claimTypeCode')
      let _claimCompanyCode = sessionStorage.getItem('claimCompanyCode')
      this.$API.assessManage
        .listAvailableClaimStandByCompany({
          companyCode: _claimCompanyCode,
          typeCode: _claimTypeCode
        })
        .then((res) => {
          this.dataSource = res.data
        })
    },
    getTaxItemList() {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        this.dataSource = res.data
      })
    },
    getUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.dataSource = res.data.records
      })
    },
    async getCurrencyList() {
      await this.$API.masterData
        .queryAllCurrency()
        .then((res) => {
          res.data?.forEach((item) => {
            const { currencyCode, currencyName } = item
            item.text = currencyCode + '-' + currencyName
          })
          this.dataSource = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    serchText(val) {
      val.updateData(
        this.dataSource.filter(
          (e) => e[this.fields.value].includes(val.text) || e[this.fields.text].includes(val.text)
        )
      )
    },
    startOpen() {},
    selectChange(val) {
      if (this.data.column.field === 'costCenter') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'costCenter',
          itemInfo: {
            costCenter: val.itemData.costCenterCode,
            costCenterDesc: val.itemData.costCenterDesc
          }
        })
        bus.$emit('costCenterDescChange', val.itemData.costCenterDesc)
      }
      if (this.data.column.field === 'currencyCode') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'currencyCode',
          itemInfo: {
            currencyName: val.itemData.currencyName
          }
        })
        bus.$emit('currencyNameChange', val.itemData.currencyName)
      }
      if (this.data.column.field === 'standName') {
        // bus.$emit('standCodeChange', val) //传给描述
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'standCode',
          itemInfo: {
            standDesc: val.itemData.claimStandCalcRule.standDesc,
            standName: val.itemData.standName,
            standCode: val.itemData.standCode
          }
        })
        bus.$emit('standCodeChange', val.itemData.standCode)
        bus.$emit('changeUntaxedPricebus', val.itemData)
        bus.$emit('standDescChange', val.itemData.claimStandCalcRule.standDesc)
      }
      if (this.data.column.field === 'taxInclusiveName') {
        bus.$emit('taxInclusiveNameChange', val)
        if (!val.itemData.value) {
          bus.$emit('taxAmountChange', null)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxInclusiveName',
            itemInfo: {
              taxInclusiveName: val.itemData.label,
              taxInclusive: val.itemData.value,
              taxAmount: null,
              taxedPrice: null,
              taxTypeName: null
            }
          })
        } else {
          bus.$emit('taxAmountChange', null)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxInclusiveName',
            itemInfo: {
              taxInclusiveName: val.itemData.label,
              taxInclusive: val.itemData.value,
              taxAmount: null,
              untaxedPrice: null,
              taxTypeName: null
            }
          })
        }
      }
      if (this.data.column.field === 'taxTypeName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'taxTypeName',
          itemInfo: {
            taxTypeName: val.itemData.taxItemName,
            taxTypeId: val.itemData.id,
            taxTypeCode: val.itemData.taxItemCode,
            taxRate: val.itemData.taxRate
          }
        })
        bus.$emit('taxedPriceChange', val)
      }
      if (this.data.column.field === 'factoryCode') {
        sessionStorage.setItem('organizationId', val.itemData.organizationId)
        bus.$emit('itemCodeChange', null)
        bus.$emit('itemNameChange', null)
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'factoryCode',
          itemInfo: {
            factoryId: val.itemData.organizationId,
            factoryCode: val.itemData.siteCode,
            factoryName: val.itemData.siteName,
            itemId: null,
            itemCode: null,
            itemName: null
          }
        })
        bus.$emit('factoryNameChange', val.itemData?.siteName || null)
      }
      if (this.data.column.field === 'unitName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'unitName',
          itemInfo: {
            unitCode: val.itemData.unitCode,
            unitName: val.itemData.unitName
          }
        })
      }
      if (this.data.column.field === 'refResUnitName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'refResUnitName',
          itemInfo: {
            refResUnitCode: val.itemData.unitCode,
            refResUnitName: val.itemData.unitName
          }
        })
      }
      if (this.data.column.field === 'categoryCode') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'categoryCode',
          itemInfo: {
            categoryCode: val.itemData?.categoryCode || null,
            categoryName: val.itemData?.categoryName || null
          }
        })
        bus.$emit('categoryNameChange', val.itemData?.categoryName || null)
      } else if (this.data.column.field === 'materialCode') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'materialCode',
          itemInfo: {
            materialCode: val.itemData?.itemCode || null,
            materialName: val.itemData?.itemName || null,
            materialDesc: val.itemData?.itemName || null
          }
        })
        bus.$emit('materialNameChange', val.itemData?.itemName || null)
        bus.$emit('materialDescChange', val.itemData?.itemName || null)
      } else if (this.data.column.field === 'purchaseOrgCode') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'purchaseOrgCode',
          itemInfo: {
            purchaseOrgCode: val.itemData?.organizationCode || null,
            purchaseOrgName: val.itemData?.organizationName || null
          }
        })
        bus.$emit('purchaseOrgNameChange', val.itemData?.organizationName || null)
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('changeUntaxedPricebus')
  }
}
</script>
