<template>
  <div
    :class="['cell-changed', 'cell-changed-disabled' && !data.column.allowEditing]"
    id="cell-changed"
  >
    <mt-input
      :id="data.column.field"
      style="display: none"
      :value="data[data.column.field]"
    ></mt-input>
    <mt-input-number
      v-model="data[data.column.field]"
      :disabled="isEditable"
      min="0"
      max="100"
      @input="handleInputChange"
    ></mt-input-number>
  </div>
</template>

<script>
import bus from '@/utils/bus'

export default {
  data() {
    return {
      data: {},
      fieldName: ''
    }
  },
  computed: {
    isEditable() {
      console.log('thisthishitshitshit', this.$route.query.type)
      const status = this.$route.query.type
      if (status === 1) {
        return true
      } else {
        return false
      }
    }
  },
  created() {},
  mounted() {
    var that = this
    const fieldName = this.data.column.field
    this.$bus.$off(`${fieldName}Change`) // 为了解决textWrapper拿不到的问题
    this.$bus.$on(`${fieldName}Change`, (txt) => {
      that.$set(that.data, fieldName, txt)
    })
  },
  methods: {
    handleInputChange(val) {
      const { field } = this.data.column

      // TCL承担比例 + 供应商承担比例总 = 100，填写其中一个自动计算另外一个
      if (field === 'purchaserPercent') {
        bus.$emit('supplierPercentChange', 100 - val)
      } else if (field === 'supplierPercent') {
        bus.$emit('purchaserPercentChange', 100 - val)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#cell-changed /deep/ .e-input.e-disabled {
  height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
  padding-left: 10px !important;
  background: #f5f5f5 !important;
}
</style>
