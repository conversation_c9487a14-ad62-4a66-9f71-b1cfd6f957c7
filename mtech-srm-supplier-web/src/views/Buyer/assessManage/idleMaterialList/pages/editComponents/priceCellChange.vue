<template>
  <div
    :class="['cell-changed', 'cell-changed-disabled' && !data.column.allowEditing]"
    id="cell-changed"
  >
    <mt-input
      :id="data.column.field"
      style="display: none"
      :value="data[data.column.field]"
    ></mt-input>
    <mt-input v-model="data[data.column.field]" :disabled="isEditable"></mt-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      fieldName: '',
      isEditable: true
    }
  },
  watch: {
    // 'data.unitPrice': {
    //   handler(val) {
    //     if (this.data.column.field === 'unitPrice') {
    //       this.isEditable = val ? true : false
    //     }
    //   },
    //   deep: true,
    //   immediate: true
    // }
  },
  created() {},
  mounted() {
    const status = this.$route.query.type
    const indexItemList = JSON.parse(sessionStorage.getItem('setIdleMateriaItem'))
    let isFalg = false
    const dataCode = `${this.data.factoryCode} - ${this.data.materialCode} - ${this.data.purchaseOrgCode}`
    indexItemList.forEach((e) => {
      e.siteAndItemCode = `${e.factoryCode} - ${e.materialCode} - ${e.purchaseOrgCode}`
      if (e.siteAndItemCode === dataCode && !e.unitPrice) {
        isFalg = true
      }
    })
    if (this.data.column.field === 'unitPrice' && isFalg && status === 11) {
      this.isEditable = false
    }
    console.log('thisssssss', this.data)
    var that = this
    // console.log("进入cellChange。vue", this.data);
    this.fieldName = this.data.column.field
    this.$bus.$off(`${this.fieldName}Change`) // 为了解决textWrapper拿不到的问题
    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      console.log(
        '￥emit的监听到了被展示的数据------',
        this.fieldName,
        txt,
        this.data,
        this.data.isHavaUnitPrice
      )
      // that.data[_field] = txt;
      if (txt) {
        console.log('unitPriceunitPrice45')

        this.isEditable = true
      } else if (!txt && status === 11) {
        console.log('unitPriceunitPrice123')

        this.isEditable = false
      }
      console.log('isEditableisEditable', this.isEditable)
      that.$set(that.data, this.fieldName, txt)
    })
  }
}
</script>

<style lang="scss" scoped>
#cell-changed /deep/ .e-input.e-disabled {
  height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
  padding-left: 10px !important;
  background: #f5f5f5 !important;
}
</style>
