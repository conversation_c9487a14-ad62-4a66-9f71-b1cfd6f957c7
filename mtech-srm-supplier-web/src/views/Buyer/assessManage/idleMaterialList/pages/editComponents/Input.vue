<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :placeholder="$t('')"
      @input="onInput"
    ></mt-input>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data() {
    return {
      data: {}
    }
  },
  computed: {
    dateValue() {
      return this.data[this.data.column.field]
    }
  },
  mounted() {},
  methods: {
    onInput: debounce(function (e) {
      let _val = e
      if (this.data.column.field === 'unitPrice') {
        let reg = /^\d+(\.\d{1,5})?$/g
        if (!reg.test(_val)) {
          this.$toast({
            type: 'warning',
            content: this.$t('只能输入数字或小数点后五位')
          })
          _val = ''
        }
        setTimeout(() => {
          this.data[this.data.column.field] = _val

          this.$parent.$emit('selectedChanged', {
            fieldCode: 'unitPrice',
            itemInfo: {
              unitPrice: _val
            }
          })
        }, 100)
      }
    }, 1000)
  },
  beforeD<PERSON>roy() {}
}
</script>
