<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="confirm"
    height="380"
    width="420"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" class="" :model="formData" :rules="rules">
        <mt-form-item prop="appealArbitratorId" :label="$t('审核人')" label-style="top">
          <mt-select
            v-model="formData.appealArbitratorId"
            :data-source="auditManList"
            :fields="{
              text: 'employeeName',
              value: 'userId'
            }"
            :placeholder="$t('点击搜索审核人')"
            :allow-filtering="true"
            :filtering="getAuditMan"
            :open-dispatch-change="true"
            :disabled="isAllowEdit"
            @change="hChangAppealArbitrator"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import debounce from 'lodash.debounce'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      formData: {
        appealArbitratorId: '',
        appealArbitratorName: ''
      },
      auditManList: [], // 人员列表
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    infoId() {
      return this.modalData.dataId
    }
  },
  created() {
    this.getAuditMan = debounce(this.getAuditMan, 1000)
  },
  mounted() {
    this.$refs.dialog.ejsRef.show() //显示弹窗
  },
  methods: {
    // 获取人员列表
    getAuditMan(val) {
      let params = {
        page: {
          current: 1,
          size: 20
        },
        subjectType: 0
      }
      if (val) {
        params['condition'] = 'or'
        params['rules'] = [
          {
            label: this.$t('账号'),
            field: 'externalCode',
            type: 'string',
            operator: 'contains',
            value: val.text
          },
          {
            label: this.$t('姓名'),
            field: 'employeeName',
            type: 'string',
            operator: 'contains',
            value: val.text
          }
        ]
      }
      this.$API.purChangeRequest.employee(params).then((res) => {
        if (res.code == 200 && res.data.records != null) {
          const auditManList = res.data.records.map((item) => {
            item.employeeName = item.externalCode + '-' + item.employeeName
            return item
          })
          const newArr = auditManList.concat(this.auditManList)
          let map = new Map()
          for (let item of newArr) {
            map.set(item.externalCode, item)
          }
          this.auditManList = [...map.values()]
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },
    hChangAppealArbitrator(e) {
      console.log('hChangAppealArbitrator', e)
      const { itemData } = e
      this.formData.appealArbitratorName = itemData.employeeName.substring(
        itemData.employeeName.indexOf('-') + 1
      )
    },
    confirm() {
      const params = {
        appealArbitratorId: this.formData.appealArbitratorId,
        appealArbitratorName: this.formData.appealArbitratorName,
        claimId: this.infoId
      }
      this.$API.assessManage.claimAppealDealTransfer(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('添加成功'),
            type: 'success'
          })
          this.$emit('confirm-function') //关闭弹窗
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
// .dialog-content {
//   height: 100%;
//   width: 100%;
// }
/deep/ .mt-form-item {
  margin-top: 20px;
}
</style>
