<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { appealDealCols } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '41bfa451-9168-4de8-8868-59c13a5c2214',
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                // {
                //   id: 'deal',
                //   icon: 'icon_table_new',
                //   title: this.$t('申诉处理')
                // },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                },
                {
                  id: 'turn',
                  title: this.$t('转办'),
                  icon: 'icon_solid_editsvg'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: appealDealCols,
            frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claim/pageClaim',
              // params: { type: 2 }
              params: { type: 6 }
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'audit') {
        this.audit(records)
      } else if (e.toolbar.id == 'turn') {
        this.turn(records)
      }
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].id,
        businessType: sltList[0].businessType ? sltList[0].businessType : ''
      }
      this.$API.assessManage.claimConfigGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    // 转办
    turn(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () => import('./coms/trunDialog.vue'),
        data: {
          title: this.$t('转办'),
          dataId: sltList[0].id
        },
        success: () => {}
      })
    },
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field == 'claimCode') {
        let { data } = e
        this.$router.push({
          name: 'purchase-assessmanage-appealDealDetail',
          query: {
            type: data.status,
            id: data.id
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
