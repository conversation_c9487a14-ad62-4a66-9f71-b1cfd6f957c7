import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Vue from 'vue'
export const appealDealCols = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'claimCode',
    headerText: i18n.t('考核单编码'),
    cellTools: []
  },
  {
    field: 'claimTypeName',
    headerText: i18n.t('考核维度')
  },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'supplierCode',
    width: 0.5,
    headerText: i18n.t('供应商代码')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已取消'),
        '-2': i18n.t('已作废'),
        0: i18n.t('新建'),
        1: i18n.t('已提交'),
        3: i18n.t('审批拒绝'),
        10: i18n.t('待反馈'),
        11: i18n.t('已反馈'),
        12: i18n.t('已确认'),
        13: i18n.t('申诉处理审批中'),
        14: i18n.t('重新改判'),
        15: i18n.t('已改判'),
        16: i18n.t('不改判'),
        17: i18n.t('已付款'),
        19: i18n.t('申诉驳回')
      }
    }
  },

  {
    field: 'appealArbitratorName',
    headerText: i18n.t('申诉处理人'),
    width: '120'
    // ignore: true
  },
  {
    field: 'createType',
    headerText: i18n.t('单据来源'),
    width: '100',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('手动创建'), 1: i18n.t('自动创建') }
    }
    // ignore: true
  },
  {
    field: 'referenceClaim',
    headerText: i18n.t('关联单据号'),
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="showDetail">{{referenceClaimCode}}</a>`,
          data() {
            return { data: {}, referenceClaimCode: '' }
          },
          mounted() {
            if (this?.data?.referenceClaim?.claimCode) {
              this.referenceClaimCode = this.data.referenceClaim.claimCode
            }
          },
          methods: {
            showDetail() {
              window.open(this.data.referenceClaim.redirectUrl)
            }
          }
        })
      }
    },
    ignore: true
  },
  {
    field: 'withholdCode',
    headerText: i18n.t('预扣凭证号')
    // ignore: true
  },
  {
    field: 'deductCode',
    headerText: i18n.t('扣款凭证号'),
    ignore: true
  },
  {
    field: 'withholdWriteOffCode',
    headerText: i18n.t('预扣冲销凭证号'),
    ignore: true
  },
  {
    field: 'offlineEnsure',
    headerText: i18n.t('供应商是否签字确认'),
    valueConverter: {
      type: 'map',
      map: { false: i18n.t('否'), true: i18n.t('是') }
    },
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        let _flag = null
        if (e == 'false') {
          _flag = false
        } else if (e == 'true') {
          _flag = true
        }
        return _flag
      }
    },
    ignore: true
  },
  {
    field: 'claimTotalAmount',
    headerText: i18n.t('索赔总额'),
    ignore: true
  },
  {
    field: 'claimMonth',
    headerText: i18n.t('考核月份'),
    ignore: true
  },
  {
    field: 'itemName',
    headerText: i18n.t('考核品类')
    // ignore: true
  },
  {
    field: 'feedbackEndTime',
    headerText: i18n.t('要求反馈日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formateTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    },
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000 - 1440000)

        //自定义搜索值，规则
        return obj
      }
    }
    // ignore: true
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    searchOptions: {
      renameField: 'mc.createUserName'
    }
    // ignore: true
  },
  {
    field: 'createUserDepartment',
    headerText: i18n.t('创建人部门')
  }
]
