<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('申诉处理') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="basicInfo" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('币种')"
            label-style="top"
            prop="claimTypeCode"
          >
            <mt-input :disabled="true" v-model="basicInfo.currencyName" width="300"></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('索赔总额')"
            label-style="top"
            prop="claimTotalAmount"
          >
            <mt-input
              :disabled="true"
              v-model="basicInfo.claimTotalAmount"
              :placeholder="$t('请输入')"
              width="300"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('反馈截止时间')"
            label-style="top"
            prop="feedbackEndTime"
          >
            <mt-date-time-picker
              :disabled="true"
              :width="300"
              v-model="basicInfo.feedbackEndTime"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('实际反馈时间')"
            label-style="top"
            prop="claimTypeCode"
            v-if="queryType != 10"
          >
            <mt-date-time-picker
              :disabled="true"
              :width="300"
              v-model="actualFeedbackTime"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('申诉处理截止时间')"
            label-style="top"
            prop="claimTypeCode"
            v-if="queryType != 10"
          >
            <mt-date-time-picker
              :disabled="true"
              :width="300"
              v-model="appealDealEndTime"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('申诉处理')"
            label-style="top"
            prop="appealDeal"
            v-if="queryType != 10"
          >
            <mt-select
              v-model="basicInfo.appealDeal"
              :data-source="handleClaimList"
              :placeholder="$t('请选择')"
              :disabled="isDisabled"
              @change="changeHandleClaim"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item full-width"
            :label="$t('决议说明')"
            label-style="top"
            prop="appealDealDesc"
            v-if="queryType != 10"
          >
            <mt-input
              v-model="basicInfo.appealDealDesc"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :disabled="isDisabled"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>

    <div
      slot="slot-filter"
      :class="['top-filter', !assessmentIndexExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('考核指标详情') }}</div>
      <div class="sort-box" @click="assessmentIndexExpand = !assessmentIndexExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="assessmentIndexExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="assessIndexTemplateRef"
          :template-config="assessmentIndexConfig"
          @handleClickToolBar="clickAssessmentIndexToolBar"
        ></mt-template-page>
      </div>
    </div>
    <!-- v-if="1 == 2" 隐藏成本中心 -->
    <div
      v-if="isShowCostCenter"
      slot="slot-filter"
      :class="['top-filter', !costCenterExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('成本中心分摊') }}</div>
      <div class="sort-box" @click="costCenterExpand = !costCenterExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="costCenterExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="costCenterTemplateRef"
          :template-config="costCenterConfig"
          @actionComplete="costCenterActionComplete"
          @handleClickToolBar="handleClickCostCenterToolBar"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !reasonExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('原因说明') }}</div>
      <div class="sort-box" @click="reasonExpand = !reasonExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="reasonExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-input
          ref="editorRef"
          v-model="templateText"
          :multiline="true"
          :rows="2"
          :disabled="true"
        ></mt-input>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !purAttachmentExpand && 'top-filter-small', 'mb-30']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('采方附件') }}</div>
      <div class="sort-box" @click="purAttachmentExpand = !purAttachmentExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="purAttachmentExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="attachmentRef"
          :template-config="purchaseAttachmentConfig"
          @handleClickToolBar="handleClickPurchaseAttachmentToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { download } from '@/utils/utils'
import util from '@/utils/utils'

import {
  costCenterColumn,
  assessmentIndexColumn,
  attachmentColumn,
  editSettings,
  claimTypeList,
  taxItemList,
  assessmentIndexData,
  costCenterData,
  editFlag,
  QMS
} from './config/index'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    },
    actualFeedbackTime() {
      let _tempTime = null
      if (this?.info?.claimAppeal?.createTime) {
        _tempTime = utils.formateTime(
          new Date(Number(this.info.claimAppeal.createTime)),
          'yyyy-MM-dd hh:mm'
        )
      }
      return _tempTime
    },
    appealDealEndTime() {
      let _tempTime = null
      if (this?.info?.claimAppeal?.appealDealEndTime) {
        _tempTime = utils.formateTime(
          new Date(Number(this.info.claimAppeal.appealDealEndTime)),
          'yyyy-MM-dd hh:mm'
        )
      }
      return _tempTime
    },
    isDisabled() {
      if (this?.info?.status == 13) {
        return true
      } else {
        return false
      }
    },
    costCenterConfig() {
      let config = []
      if (this.$route.query.type == 10) {
        config = [
          {
            useToolTemplate: false,
            grid: {
              allowPaging: false,
              lineIndex: 1,
              columnData: costCenterColumn,
              height: 350,
              dataSource: costCenterData
            }
          }
        ]
      } else {
        config = [
          {
            toolbar: {
              useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
              tools: [['Add', 'Delete'], []]
            },
            useToolTemplate: false,
            grid: {
              editSettings: {
                allowEditing: true,
                allowAdding: true,
                allowDeleting: true,
                mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
                showConfirmDialog: false,
                showDeleteConfirmDialog: false,
                newRowPosition: 'Top'
              },
              allowPaging: false,
              lineIndex: 1,
              columnData: costCenterColumn,
              height: 'auto',
              dataSource: costCenterData
            }
          }
        ]
      }
      return config
    }
  },
  async created() {
    if (this.queryType == 10) {
      delete this.assessmentIndexConfig[0].toolbar
      delete this.purchaseAttachmentConfig[0].toolbar
    }
    await this.getAssessIndexList()
    await this.getTaxItemList()
  },
  mounted() {
    console.log('mounted=', this.info)
    if (this?.info?.createType == 1) {
      this.handleClaimList.splice(2, 1)
    }
    if (this.info.status == 13) {
      editFlag.isDisabled = true
    }
    this.basicInfo = {
      ...this.info,
      feedbackEndTime: new Date(Number(this.info.feedbackEndTime)),
      appealDealDesc: this?.info?.claimAppealDeal?.appealDealDesc,
      id: this?.info?.claimAppealDeal?.id,
      appealDeal: this?.info?.claimAppealDeal?.appealDeal
    }
    if (!this?.info?.claimAppealDeal?.id) {
      delete this.basicInfo.id
    }

    if (this?.info?.claimAppealDeal?.appealDeal == 2) {
      this.assessmentIndexConfig[0].toolbar.tools[0] = ['Delete']
    }

    this.info.standDetailList.forEach(
      (e) => (e.happenTime = utils.formateTime(new Date(Number(e.happenTime)), 'yyyy-MM-dd hh:mm'))
    )
    this.info.costCenterList.forEach((e) => (e.costCenterAddId = this.costCenterAddId++))
    this.costCenterListOrigin = cloneDeep(this.info.costCenterList)
    this.standDetailListOrigin = cloneDeep(this.info.standDetailList)
    costCenterData.length = 0
    assessmentIndexData.length = 0
    let _costCenterList = cloneDeep(this.info.costCenterList)
    let _standDetailList = cloneDeep(this.info.standDetailList)
    _costCenterList.forEach((e) => costCenterData.push(e))
    _standDetailList.forEach((e) => assessmentIndexData.push(e))
    setTimeout(() => {
      this.purchaseAttachmentConfig[0].grid.dataSource = this.info.attachmentList
    }, 0.17)
    this.templateText = this.info.reasonDesc
    this.$bus.$on('tempClaimTotalAmountChange', (val) => {
      this.basicInfo.claimTotalAmount = val
    })
    this.getCompanyCode()
  },
  data() {
    return {
      isShowCostCenter: false,

      standDetailListOrigin: [],
      costCenterListOrigin: [],
      templateText: null,
      currencyList: [],
      referenceList: [],
      basicExpand: true,
      costCenterExpand: true,
      assessmentIndexExpand: true,
      reasonExpand: true,
      purAttachmentExpand: true,
      basicInfo: {},
      appealDealForm: {},
      rules: {
        appealDeal: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        appealDealDesc: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      handleClaimList: [
        {
          text: this.$t('维持原判'),
          value: 0
        },
        {
          text: this.$t('减免金额'),
          value: 1
        },
        {
          text: this.$t('改判指标'),
          value: 2
        },
        {
          text: this.$t('取消考核'),
          value: 3
        }
      ],
      assessmentIndexConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], []]
          },
          useToolTemplate: false,
          grid: {
            allowSorting: false,
            allowPaging: false,
            lineIndex: 1,
            columnData:
              this?.info?.createType == 1
                ? [...assessmentIndexColumn(this.info), ...QMS]
                : assessmentIndexColumn(this.info),
            height: 350,
            dataSource: assessmentIndexData
          }
        }
      ],
      purchaseAttachmentConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Edit', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 350,
            dataSource: []
          }
        }
      ]
    }
  },
  methods: {
    handleClickCostCenterToolBar(args) {
      const { toolbar, grid } = args
      if (toolbar.id == 'Add') {
        // 新增
        this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'Delete') {
        // 选中的数据
        let selectedRecords = grid.getSelectedRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
            }
          })
        } else {
          this.$toast({
            content: this.$t('请先选择一行'),
            type: 'warning'
          })
        }
      }
    },
    costCenterActionComplete(args) {
      let { data, requestType, rowIndex } = args
      if (requestType == 'save') {
        // 验证必输
        if (!data.costCenterCode) {
          this.$toast({
            content: this.$t('有字段未输入'),
            type: 'warning'
          })
          this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.costCenterTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        }
      }
    },
    // 获取公司代码（TV）
    getCompanyCode() {
      const TVCodeArr = util.getSupplierDict('TVComCode') || []
      TVCodeArr.forEach((e) => {
        if (this.info.companyCode === e.dictCode) {
          this.isShowCostCenter = true
        }
      })
    },
    handleClickCellTitle(e) {
      if (e.field == 'attachmentName') {
        this.preview(e.data)
      }
    },
    preview(item) {
      let params = {
        id: item.attachmentId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.attachmentId
        })
        .then((res) => {
          download({
            fileName: data.attachmentName,
            blob: res.data
          })
        })
    },
    // 获取税率下拉
    getTaxItemList() {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        taxItemList.length = 0
        res.data.forEach((e) => taxItemList.push(e))
      })
    },
    getAssessIndexList() {
      console.log(this.info)
      this.$API.assessManage
        .listAvailableClaimStandByCompany({
          companyCode: this.info.companyCode,
          typeCode: this.info.claimTypeCode
        })
        .then((res) => {
          claimTypeList.length = 0
          res.data.forEach((e) => claimTypeList.push(e))
        })
    },
    changeHandleClaim(e) {
      console.log('-e-', e)
      editFlag.appealDeal = e.value
      if (e.value == 2) {
        this.$set(this.assessmentIndexConfig[0].toolbar, 'tools', [['Delete'], []])
      } else {
        this.$set(this.assessmentIndexConfig[0].toolbar, 'tools', [[], []])
      }
      // if (e.previousItem) {
      costCenterData.length = 0
      assessmentIndexData.length = 0
      this.$set(this.costCenterConfig[0].grid, 'dataSource', [])
      this.$set(this.assessmentIndexConfig[0].grid, 'dataSource', [])

      let _costCenterData = cloneDeep(this.costCenterListOrigin)
      let _assessmentIndexData = cloneDeep(this.standDetailListOrigin)
      _costCenterData.forEach((e) => costCenterData.push(e))
      _assessmentIndexData.forEach((e) => assessmentIndexData.push(e))
      setTimeout(() => {
        this.$set(this.costCenterConfig[0].grid, 'dataSource', costCenterData)

        this.$set(this.assessmentIndexConfig[0].grid, 'dataSource', assessmentIndexData)
      }, 10)
      // }
    },
    changeCurrency() {},
    clickAssessmentIndexToolBar(args) {
      const { toolbar, grid } = args
      console.log('handleClickAssessToolBar=', this, args)
      if (toolbar.id == 'Delete') {
        // 选中的数据
        let selectedRecords = grid.getSelectedRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              let ids = selectedRecords.map((item) => item.id)
              let _tempData = this.assessmentIndexConfig[0].grid.dataSource.filter(
                (e) => !ids.includes(e.id)
              )
              this.assessmentIndexConfig[0].grid.dataSource = _tempData
              console.log('Delete=', this.$refs.assessIndexTemplateRef.getCurrentUsefulRef())
              this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      }
    },
    handleClickPurchaseAttachmentToolBar(e) {
      let sltList = e.grid.getSelectedRecords()
      let selectedRowIndexes = e.grid.getSelectedRowIndexes()
      if (
        (!sltList || sltList.length <= 0) &&
        (e.toolbar.id == 'Edit' || e.toolbar.id == 'Delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      console.log('handleClickPurchaseAttachmentToolBar=', e)
      if (e.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            fileData: [],
            isView: false, // 是否为预览
            required: false, // 是否必须
            title: this.$t('新增采方附件')
          },
          success: (res) => {
            this.handleUploadFiles(res)
          }
        })
      } else if (e.toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            title: this.$t('编辑采方附件'),
            isEdit: true,
            info: {
              ...sltList[0],
              fileName: sltList[0].attachmentName,
              fileSize: sltList[0].attachmentSize,
              url: sltList[0].attachmentUrl
            },
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _tempData)
            this.$refs.attachmentRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id == 'Delete') {
        let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
        let _fileIds = sltList.map((x) => x.id)
        let _newData = _tempData.filter((element) => !_fileIds.includes(element.id))
        this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _newData)
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'download') {
        this.handleDownloadFile(data)
      }
    },
    handleUploadFiles(data) {
      let _tempData = {
        ...data,
        attachmentId: data.id,
        attachmentName: data.fileName,
        attachmentSize: data.fileSize,
        attachmentUrl: data.url
      }
      this.purchaseAttachmentConfig[0].grid.dataSource.push(_tempData)
    }
  }
}
</script>
<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
// .accordion-title {
//   float: left;
//   font-size: 14px;
//   margin-left: 20px;
//   font-family: PingFangSC;
//   font-weight: 500;
//   color: rgba(41, 41, 41, 1);
//   text-indent: 10px;
//   border-left: 5px solid #00469c;
//   margin-bottom: 20px;
//   border-radius: 2px 0 0 2px;
// }

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
/deep/ .mt-rich-text-editor {
  margin: 30px 10px 0 10px;
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-toolbar-items {
    margin-left: -59px;
  }
  .e-rte-content {
    height: 300px !important;
  }
}
.mb-30 {
  margin-bottom: 30px;
}
/deep/ .mt-form-item {
  margin-bottom: 16px;
}
</style>
