<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
import bus from '@/utils/bus'
import { utils } from '@mtech-common/utils'
import util from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      costCenterCode: '', // 成本中心
      isDisabled: false,
      siteName: ''
    }
  },
  async mounted() {
    this.serchText = utils.debounce(this.serchText, 300)
    if (this.data.column.field === 'costCenterCode') {
      //成本中心下拉
      this.getCostCenter(this.data.costCenterCode)
      this.fields = { text: 'costCenterCode', value: 'costCenterCode' }
      let _company = JSON.parse(sessionStorage.getItem('inspectionSheetCompany'))
      const KtBdCompany = this.getCompanyCode()
      console.log('costCenterCode', KtBdCompany)
      KtBdCompany.forEach((e) => {
        if (e.dictCode === _company.companyCode) {
          this.isDisabled = true
          return
        }
      })
    }

    if (this.data.column.field === 'taxInclusiveName') {
      // bus.$emit("taxInclusiveNameChange", this.data.taxInclusiveName);
      this.dataSource = [
        { label: this.$t('否'), value: false },
        { label: this.$t('是'), value: true }
      ]
      this.fields = { text: 'label', value: 'label' }
      if (this.data.isPv) {
        this.isDisabled = true
      }
    }
    if (this.data.column.field === 'unitName' || this.data.column.field === 'refResUnitName') {
      this.getUnitList()
      this.fields = { text: 'unitName', value: 'unitName' }
    }
    if (this.data.column.field === 'taxTypeName') {
      console.log('this.data.taxInclusiveName', this.data)
      if (!this.data.taxInclusive) {
        this.isDisabled = true
      }
      this.$bus.$on('taxInclusiveNameChange', (val) => {
        console.log('taxInclusiveNameChange=', val)
        this.isDisabled = !val.itemData.value
        if (!val.itemData.value) {
          this.data.taxTypeName = null
        }
      })
      this.getTaxItemList()
      this.fields = { text: 'taxItemName', value: 'taxItemName' }
    }
    if (this.data.column.field === 'standName') {
      this.getAssessIndexList()
      this.fields = { text: 'standName', value: 'standName' }
    }
    if (this.data.column.field === 'siteName') {
      if (this?.data?.siteId) {
        sessionStorage.setItem('organizationId', this.data.siteId)
      }
      this.getFactoryList()
      this.fields = { text: 'siteName', value: 'siteName' }
    }
  },
  methods: {
    // 获取公司代码（空调/白电）
    getCompanyCode() {
      const KT = util.getSupplierDict('KTComCode') || []
      // const BD = util.getSupplierDict('BDComCode') || []
      // const companyArr = KT.concat(BD)
      const companyArr = KT
      return companyArr
    },
    getFactoryList() {
      let _company = JSON.parse(sessionStorage.getItem('inspectionSheetCompany'))
        ? JSON.parse(sessionStorage.getItem('inspectionSheetCompany'))
        : ''
      this.$API.masterData
        .getSiteList({
          condition: '',
          page: {
            current: 1,
            size: 1000
          },
          defaultRules: [
            {
              label: this.$t('公司'),
              field: 'parentCode',
              type: 'string',
              operator: 'equal',
              value: _company.companyCode ? _company.companyCode : ''
            }
          ],
          pageFlag: false
        })
        .then((res) => {
          this.dataSource = res.data.records
        })
    },
    getAssessIndexList() {
      let _claimTypeCode = sessionStorage.getItem('claimTypeCode')
      let _claimCompanyCode = sessionStorage.getItem('claimCompanyCode')
      this.$API.assessManage
        .listAvailableClaimStandByCompany({
          companyCode: _claimCompanyCode,
          typeCode: _claimTypeCode
        })
        .then((res) => {
          this.dataSource = res.data
        })
    },
    getTaxItemList() {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        // 过滤税率下拉列表，仅展示taxItemCode以'X'或'x'开头的
        this.dataSource = res.data?.filter(
          (item) => item.taxItemCode?.startsWith('X') || item.taxItemCode?.startsWith('x')
        )
      })
    },
    getUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.dataSource = res.data.records
      })
    },
    async getCostCenter(val) {
      const param = {
        page: {
          current: 1,
          size: 999
        },
        rules: [
          {
            label: this.$t('有效截至日期'),
            field: 'endTime',
            type: 'string',
            operator: 'greaterthanorequal',
            value: dayjs(Date.now()).format('YYYY-MM-DD')
          },
          {
            label: this.$t('成本中心编码'),
            field: 'costCenterCode',
            type: 'string',
            operator: 'contains',
            value: val
          }
        ]
      }
      // // 成本中心代码 下拉取值接口
      // await this.$API.masterData
      //   .postCostCenterCriteriaQuery()
      //   .then((res) => {
      //     this.dataSource = res.data
      //   })
      //   .catch((err) => {
      //     this.$toast({
      //       content: err.msg,
      //       type: 'error'
      //     })
      //   })
      // 成本中心代码 下拉取值接口
      await this.$API.masterData
        .getCostCenterCriteriaQueryList(param)
        .then((res) => {
          // console.log('res.data.records', res.data.records)
          this.dataSource = res.data.records
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    serchText(val) {
      if (this.data.column.field === 'costCenterCode') {
        // console.log('搜索值', val, this.data.column.field, dayjs(Date.now()).format('YYYY-MM-DD'))
        this.getCostCenter(val.text)
        this.fields = { text: 'costCenterCode', value: 'costCenterCode' }
      } else {
        val.updateData(this.dataSource.filter((e) => e[this.fields.value].includes(val.text)))
      }
    },
    startOpen() {
      // // 成本中心
      // if (this.data.column.field === "costCenterCode") {
      //   console.log("startOpen=", this);
      //   if (!!this.data.companyId) {
      //     this.getCostCenter();
      //   }
      // }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log('valthis.data', val, this.data)
      if (this.data.column.field === 'costCenterCode') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'costCenterCode',
          itemInfo: {
            costCenterDesc: val.itemData.costCenterDesc
          }
        })
        bus.$emit('costCenterDescChange', val.itemData.costCenterDesc)
      }
      if (this.data.column.field === 'standName') {
        // bus.$emit('standCodeChange', val) //传给描述
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'standCode',
          itemInfo: {
            standDesc: val.itemData.claimStandCalcRule.standDesc,
            standName: val.itemData.standName,
            standCode: val.itemData.standCode
          }
        })
        bus.$emit('standCodeChange', val.itemData.standCode)
        bus.$emit('changeUntaxedPricebus', val.itemData)
        bus.$emit('standDescChange', val.itemData.claimStandCalcRule.standDesc)
      }
      if (this.data.column.field === 'taxInclusiveName') {
        bus.$emit('taxInclusiveChange', val.itemData.value)
        bus.$emit('taxInclusiveNameChange', val)
        if (!val.itemData.value) {
          bus.$emit('taxAmountChange', null)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxInclusiveName',
            itemInfo: {
              taxInclusiveName: val.itemData.label,
              taxInclusive: val.itemData.value,
              taxAmount: null,
              taxedPrice: null,
              taxTypeName: null
            }
          })
        } else {
          bus.$emit('taxAmountChange', null)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxInclusiveName',
            itemInfo: {
              taxInclusiveName: val.itemData.label,
              taxInclusive: val.itemData.value,
              taxAmount: null,
              untaxedPrice: null,
              taxTypeName: null
            }
          })
        }
        this.data.taxInclusive = val.itemData.value
      }
      if (this.data.column.field === 'taxTypeName') {
        bus.$emit('taxRateChange', val.itemData.taxRate)
        bus.$emit('taxTypeCodeChange', val.itemData.taxItemCode)
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'taxTypeName',
          itemInfo: {
            taxTypeName: val.itemData.taxItemName,
            taxTypeId: val.itemData.id,
            taxTypeCode: val.itemData.taxItemCode,
            taxRate: val.itemData.taxRate
          }
        })
        this.$set(this.data, 'taxRate', val.itemData.taxRate)
      }
      if (this.data.column.field === 'siteName') {
        sessionStorage.setItem('organizationId', val.itemData.organizationId)
        bus.$emit('itemCodeChange', null)
        bus.$emit('itemNameChange', null)
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'siteName',
          itemInfo: {
            siteId: val.itemData.organizationId,
            siteCode: val.itemData.siteCode,
            siteName: val.itemData.siteName,
            itemId: null,
            itemCode: null,
            itemName: null
          }
        })
      }
      if (this.data.column.field === 'unitName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'unitName',
          itemInfo: {
            unitCode: val.itemData.unitCode,
            unitName: val.itemData.unitName
          }
        })
      }
      if (this.data.column.field === 'refResUnitName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'refResUnitName',
          itemInfo: {
            refResUnitCode: val.itemData.unitCode,
            refResUnitName: val.itemData.unitName
          }
        })
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('changeUntaxedPricebus')
  }
}
</script>
