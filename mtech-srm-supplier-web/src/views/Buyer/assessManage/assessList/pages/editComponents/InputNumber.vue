<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <mt-inputNumber
      v-model="data[data.column.field]"
      :min="min"
      :max="max"
      :disabled="isDisabled"
      :step="1"
      :precision="2"
      :show-clear-button="false"
      @input="numberChange"
    ></mt-inputNumber>
  </div>
</template>
<script>
// var bigDecimal = require("js-big-decimal");
export default {
  inject: ['assessIndexInfo'],
  data() {
    return {
      data: {},
      min: 0,
      max: null,
      stockQuantity: 0,
      isDisabled: false
    }
  },
  created() {
    // 确保data对象包含所有必要的字段
    this.$set(this.data, 'quantity', this.data.quantity || 0)
    this.$set(this.data, 'unitPrice', this.data.unitPrice || 0)
    this.$set(this.data, 'days', this.data.days || 0)
    this.$set(this.data, 'untaxedPrice', this.data.untaxedPrice || 0)
    this.$set(this.data, 'taxedPrice', this.data.taxedPrice || 0)
    this.$set(this.data, 'taxAmount', this.data.taxAmount || 0)
  },
  mounted() {
    if (this.data.column.field === 'shareRatio') {
      this.max = 100
    }
    if (this.data.column.field === 'taxedPrice') {
      this.isDisabled = true
      this.$bus.$on('taxedPriceChange', (val) => {
        this.data.taxedPrice = val
      })
    }
    if (this.data.column.field === 'untaxedPrice') {
      if (this.data.isPv) {
        this.isDisabled = true
      } else {
        this.$bus.$on('changeUntaxedPricebus', (val) => {
          this.data.untaxedPrice = val.claimStandCalcRule.defaultValue
        })
        if (this.data.taxInclusive) {
          this.isDisabled = true
        }
        this.$bus.$on('taxInclusiveNameChange', (val) => {
          this.isDisabled = val.itemData.value
          if (val.itemData.value) {
            this.data.untaxedPrice = null
          }
          console.log('接受的单位', this.data, val)
        })
        this.$bus.$on('taxedPriceChange', (val) => {
          if (
            this.assessIndexInfo.taxInclusive &&
            this.assessIndexInfo.taxRate &&
            this.assessIndexInfo.taxedPrice
          ) {
            console.log('taxedPriceChange=', this.data, this.assessIndexInfo, val)

            let _taxedPrice = this.assessIndexInfo.taxedPrice
            let _taxRate = this.assessIndexInfo.taxRate
            let _untaxedPrice = _taxedPrice / (1 + Number(_taxRate))
            this.data.untaxedPrice = _untaxedPrice.toFixed(3)
            this.data.taxAmount = (_taxedPrice - this.data.untaxedPrice).toFixed(3)
            this.$bus.$emit('taxAmountChange', this.data.taxAmount)
            this.$parent.$emit('selectedChanged', {
              fieldCode: 'taxedPrice',
              itemInfo: {
                taxedPrice: _taxedPrice,
                untaxedPrice: _untaxedPrice,
                taxAmount: this.data.taxAmount
              }
            })
          }
        })
      }
    }
    // 统一添加所有字段的监听
    this.$bus.$on('quantityChange', (val) => {
      this.data.quantity = val
    })
    this.$bus.$on('unitPriceChange', (val) => {
      this.data.unitPrice = val
    })
    this.$bus.$on('daysChange', (val) => {
      this.data.days = val
    })
    this.$bus.$on('taxInclusiveChange', (val) => {
      this.data.taxInclusive = val
    })
    this.$bus.$on('taxRateChange', (val) => {
      this.data.taxRate = val
      this.calculatePrices()
    })
    this.$bus.$on('untaxedPriceChange', (val) => {
      this.data.untaxedPrice = val
      // 注意：这里不需要重新计算，因为不含税金额是通过含税金额计算出来的
    })
  },
  methods: {
    numberChange(val) {
      if (this.data.column.field === 'shareRatio') {
        console.log('numberChange-shareRatio=', val, this.assessIndexInfo)
        let _tempNum = ((this.assessIndexInfo.totalUntaxedPrice * val) / 100).toFixed(2)
        this.$bus.$emit('untaxedPriceChange', _tempNum) // 触发cellChange文件的$on方法
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'shareRatio',
          itemInfo: {
            untaxedPrice: _tempNum
          }
        })
      }
      if (this.data.column.field === 'untaxedPrice') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'untaxedPrice',
          itemInfo: {
            untaxedPrice: val
          }
        })
        // 当 this.data.isPv 为 true 且 taxInclusive 为 true 时，重新计算价格
        if (this.data.isPv && this.data.taxInclusive && this.data.taxRate) {
          this.calculatePrices()
        }
      }
      if (this.data.column.field === 'taxedPrice') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'taxedPrice',
          itemInfo: {
            taxedPrice: val
          }
        })
        this.$bus.$emit('taxedPriceChange', val)
      }
      if (
        this.data.column.field === 'quantity' ||
        this.data.column.field === 'unitPrice' ||
        this.data.column.field === 'days'
      ) {
        // 使用bus.$emit通知数据变化
        this.$bus.$emit(`${this.data.column.field}Change`, val)

        // 当 this.data.isPv 为 true 时，重新计算价格
        if (this.data.isPv) {
          this.calculatePrices()
        }
      }
    },
    // 计算含税金额和不含税金额
    // 根据公式：含税金额 = 数量 * 单价 * 天数，不含税金额 = 含税金额 / (1 + 税率)
    calculatePrices() {
      if (this.data.isPv) {
        let quantity = Number(this.data.quantity) || 0
        let unitPrice = Number(this.data.unitPrice) || 0
        let days = Number(this.data.days) || 0
        let taxRate = Number(this.data.taxRate) || 0

        // 计算含税金额：含税金额 = 数量 * 单价 * 天数
        let taxedPrice = quantity * unitPrice * days

        // 计算不含税金额：不含税金额 = 含税金额 / (1 + 税率)
        let untaxedPrice = taxedPrice / (1 + taxRate)

        // 计算税额
        let taxAmount = taxedPrice - untaxedPrice

        // 更新数据
        this.data.taxedPrice = taxedPrice.toFixed(3)
        this.data.untaxedPrice = untaxedPrice.toFixed(3)
        this.data.taxAmount = taxAmount.toFixed(3)

        // 发送事件通知
        this.$bus.$emit('taxedPriceChange', this.data.taxedPrice)
        this.$bus.$emit('untaxedPriceChange', this.data.untaxedPrice)
        this.$bus.$emit('taxAmountChange', this.data.taxAmount)

        this.$parent.$emit('selectedChanged', {
          fieldCode: 'priceCalculation',
          itemInfo: {
            taxedPrice: this.data.taxedPrice,
            untaxedPrice: this.data.untaxedPrice,
            taxAmount: this.data.taxAmount
          }
        })
      }
    },

    // 保留原有的计算不含税金额方法（向后兼容）
    calculateUntaxedPrice() {
      this.calculatePrices()
    },

    // 保留原有的计算含税金额方法（向后兼容）
    calculateTaxedPrice() {
      this.calculatePrices()
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.mt-input-number {
  margin-bottom: 0px;
}
</style>
