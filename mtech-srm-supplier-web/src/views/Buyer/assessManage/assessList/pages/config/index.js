import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import Select from '../editComponents/Select.vue'
import InputNumber from '../editComponents/InputNumber.vue'
import cellChanged from '../editComponents/cellChanged.vue'
import selectedItemCode from '../selectItemCode' // 物料
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import Vue from 'vue'

export default {
  components: {
    RemoteAutocomplete
  }
}
export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}

export const costCenterColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'costCenterCode',
    headerText: i18n.t('成本中心代码'),
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('成本中心代码')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'costCenterDesc',
    headerText: i18n.t('成本中心描述'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  // {
  //   field: 'shareRatio',
  //   headerText: i18n.t('分摊比例(%)'),
  //   editTemplate: () => {
  //     return { template: InputNumber }
  //   }
  //   // editType: "numericedit",
  //   // edit: {
  //   //   params: {
  //   //     min: 0,
  //   //     max: 100,
  //   //     decimals: 0,
  //   //     format: "###",
  //   //     validateDecimalOnType: true,
  //   //     htmlAttributes: { type: "number" },
  //   //     showSpinButton: false,
  //   //   },
  //   // },
  //   // headerTemplate: () => {
  //   //   return {
  //   //     template: Vue.component('headers', {
  //   //       template: `
  //   //           <div class="headers">
  //   //             <span style="color: red">*</span>
  //   //             <span class="e-headertext">{{$t('分摊比例(%)')}}</span>
  //   //           </div>
  //   //         `
  //   //     })
  //   //   }
  //   // }
  // },
  {
    field: 'untaxedPrice',
    headerText: i18n.t('不含税金额'),
    editTemplate: () => {
      return { template: InputNumber }
    }
    // editTemplate: () => {
    //   return { template: cellChanged }
    // }
  }
]

export const assessmentIndexColumn = ({ claimTypeCode }) => {
  let currentBu = localStorage.getItem('currentBu')
  let showPvField = currentBu === 'GF' && ['S01', 'S08'].includes(claimTypeCode)

  let columns = [
    {
      width: '50',
      type: 'checkbox',
      allowEditing: false
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂'),
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'standName',
      headerText: i18n.t('考核指标描述'),
      editTemplate: () => {
        return { template: Select }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('考核指标描述')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'standCode',
      headerText: i18n.t('考核指标代码'),
      width: 120,
      editTemplate: () => {
        return { template: cellChanged }
      }
    },
    {
      field: 'standDesc',
      headerText: i18n.t('考核指标说明'),
      width: 120,
      editTemplate: () => {
        return { template: cellChanged }
      }
    },
    {
      field: 'happenTime',
      headerText: i18n.t('发生时间'),
      width: 200,
      editType: 'dateTimePickerEdit',
      edit: {
        params: {
          max: new Date()
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('发生时间')}}</span>
                </div>
              `
          })
        }
      },
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            if (e == 0) {
              return (e = '')
            } else if (typeof e == 'object') {
              return utils.formateTime(e, 'yyyy-MM-dd hh:mm')
            } else if (typeof e == 'string') {
              if (e.indexOf(':') != -1) {
                return e
              } else {
                let val = parseInt(e)
                return utils.formateTime(new Date(val), 'yyyy-MM-dd hh:mm')
              }
            } else if (typeof e == 'number') {
              return utils.formateTime(new Date(e), 'yyyy-MM-dd hh:mm')
            } else {
              return e
            }
          } else {
            return e
          }
        }
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      editTemplate: () => {
        return {
          template: selectedItemCode
        }
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: 120,
      editTemplate: () => {
        return { template: cellChanged }
      }
    },
    {
      field: 'unitName',
      headerText: i18n.t('单位'),
      width: 100,
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'quantity',
      headerText: i18n.t('数量'),
      width: 100,
      editTemplate: () => {
        return { template: InputNumber }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('quantity', {
            template: `
                <div class="headers">
                  <span v-if="showPvField" style="color: red">*</span>
                  <span class="e-headertext">{{$t('数量')}}</span>
                </div>
              `,
            props: {
              showPvField: {
                type: Boolean,
                default: showPvField
              }
            }
          })
        }
      }
    },
    {
      field: 'unitPrice',
      headerText: i18n.t('单价'),
      width: 100,
      editTemplate: () => {
        return { template: InputNumber }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('unitPrice', {
            template: `
                <div class="headers">
                  <span v-if="showPvField" style="color: red">*</span>
                  <span class="e-headertext">{{$t('单价')}}</span>
                </div>
              `,
            props: {
              showPvField: {
                type: Boolean,
                default: showPvField
              }
            }
          })
        }
      }
    },
    {
      field: 'days',
      headerText: i18n.t('天数'),
      width: 100,
      editTemplate: () => {
        return { template: InputNumber }
      },
      isHide: !showPvField,
      headerTemplate: () => {
        return {
          template: Vue.component('days', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('天数')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'isPv',
      headerText: i18n.t('是否光伏'),
      visible: false,
      allowEditing: false
    },
    {
      field: 'claimDesc',
      headerText: i18n.t('考核说明'),
      width: 120,
      headerTemplate: () => {
        return {
          template: Vue.component('claimDesc', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('考核说明')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'taxInclusiveName',
      headerText: i18n.t('是否含税'),
      width: 100,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('是否含税')}}</span>
                </div>
              `
          })
        }
      },
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'taxInclusive',
      headerText: i18n.t('是否含税'),
      visible: false,
      allowEditing: false
    },
    {
      field: 'taxTypeName',
      headerText: i18n.t('税率'),
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'taxTypeCode',
      headerText: i18n.t('税率'),
      width: 1,
      editTemplate: () => {
        return { template: cellChanged }
      }
    },
    {
      field: 'taxRate',
      headerText: i18n.t('税率'),
      width: 1,
      editTemplate: () => {
        return { template: cellChanged }
      }
    },
    {
      field: 'taxAmount',
      headerText: i18n.t('税额'),
      width: 100,
      editTemplate: () => {
        return { template: cellChanged }
      }
    },
    {
      field: 'untaxedPrice',
      headerText: i18n.t('不含税金额'),
      width: 120,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('不含税金额')}}</span>
                </div>
              `
          })
        }
      },
      editTemplate: () => {
        return { template: InputNumber }
      }
    },
    {
      field: 'taxedPrice',
      headerText: i18n.t('含税金额'),
      width: 120,
      editTemplate: () => {
        return { template: InputNumber }
      }
    },
    {
      field: 'refRes',
      headerText: i18n.t('联带物品'),
      width: 100
    },
    {
      field: 'refResUnitName',
      headerText: i18n.t('联带单位'),
      width: 100,
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'refResQuantity',
      headerText: i18n.t('联带数量'),
      width: 100,
      editType: 'numericedit',
      edit: {
        params: {
          min: 0,
          decimals: 3,
          format: '###',
          validateDecimalOnType: true,
          htmlAttributes: { type: 'number' },
          showSpinButton: false
        }
      }
    },
    {
      field: 'refAmount',
      headerText: i18n.t('联带金额'),
      width: 100,
      editType: 'numericedit',
      edit: {
        params: {
          min: 0,
          decimals: 3,
          format: '###.###',
          validateDecimalOnType: true,
          htmlAttributes: { type: 'number' },
          showSpinButton: false
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      width: 100
    }
  ]

  return columns.filter((v) => !v.isHide)
}
export const costCenterBDCol = [
  {
    field: 'costCenterCode',
    headerText: i18n.t('成本中心代码'),
    isPrimaryKey: true,
    // editTemplate: () => {
    //   return { template: Select }
    // },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            remoteSearch={true}
            v-model={scoped.costCenterCode}
            operator='in'
            url={this.$API.masterData.getCostCenterUrl}
            fields={{ text: 'costCenterDesc', value: 'costCenterCode' }}
            searchFields={['costCenterCode', 'costCenterDesc']}
            multiple={false}
            placeholder='请选择'
            popup-width='300'
            getPopupContainer={() => document.body}
            onChange={(e) => {
              scoped.costCenterCode = e.costCenterCode
            }}></RemoteAutocomplete>
        </div>
      )
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('成本中心代码')}}</span>
              </div>
            `
        })
      }
    }
    // editTemplate: () => {
    //   return {
    //     template: Vue.component('siteAndItemCode', {
    //       template: `
    //             <RemoteAutocomplete
    //               :id="data.column.field"
    //               v-model="data.costCenterCode"
    //               :url="$API.masterData.getCostCenterUrl"
    //               :fields="{ text: 'costCenterDesc', value: 'costCenterCode' }"
    //               :search-fields="['costCenterCode', 'costCenterDesc']"
    //               :multiple="false"
    //               placeholder='请选择'
    //               popup-width='300'
    //               ></RemoteAutocomplete>
    //       `,
    //       components: {
    //         RemoteAutocomplete
    //       },
    //       data() {
    //         return {
    //           data: {},
    //           placeholder: this.$t('请选择'),
    //           auditManList: []
    //         }
    //       },
    //       computed: {},
    //       created() {},
    //       mounted() {},
    //       methods: {
    //         selectChange() {
    //           // bus.$emit('transferPlanNameChange', val.itemData.employeeName)
    //           // this.$set(this.data, 'transferPlanCode', val.value.split('-')[0])
    //           // this.$set(this.data, 'transferPlanName', val.value.split('-')[1])
    //         }
    //       }
    //     })
    //   }
    // }
  },
  {
    field: 'costCenterDesc',
    headerText: i18n.t('成本中心描述'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  }
]
export const QMS = [
  {
    field: 'defectLevel',
    headerText: i18n.t('缺陷等级'),
    allowEditing: false
  },
  {
    field: 'processRatio',
    headerText: i18n.t('加工费比例'),
    allowEditing: false
  },
  {
    field: 'deductRatio',
    headerText: i18n.t('扣款比例'),
    allowEditing: false
  },
  {
    field: 'receiveCode',
    headerText: i18n.t('物料凭证'),
    allowEditing: false
  },
  {
    width: 150,
    field: 'receiveItemNo',
    headerText: i18n.t('物料凭证行号'),
    allowEditing: false
  }
]
export const attachmentColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'attachmentName',
    headerText: i18n.t('附件名称'),
    cellTools: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  },
  {
    field: 'attachmentSize',
    headerText: i18n.t('附件大小'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return `${Math.floor((e / 1024) * 100) / 100}KB`
      }
    }
  },
  {
    field: 'uploadUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formateTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('描述')
  }
]
