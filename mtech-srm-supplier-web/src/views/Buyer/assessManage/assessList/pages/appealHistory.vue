<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('基本信息') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="basicInfo">
          <mt-form-item class="form-item" :label="$t('币种')" label-style="top" prop="currencyName">
            <mt-input :disabled="true" v-model="basicInfo.currencyName" width="300"></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('反馈截止时间')"
            label-style="top"
            prop="feedbackEndTime"
          >
            <mt-date-time-picker
              :disabled="true"
              :width="300"
              v-model="basicInfo.feedbackEndTime"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('申诉处理')"
            label-style="top"
            prop="appealDeal"
          >
            <mt-select
              :disabled="true"
              v-model="basicInfo.appealDeal"
              :data-source="handleClaimList"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('索赔总额')"
            label-style="top"
            prop="claimTotalAmount"
          >
            <mt-input
              :disabled="true"
              :width="300"
              v-model="basicInfo.claimTotalAmount"
              :min="0"
            ></mt-input>
          </mt-form-item>
          <mt-form-item class="form-item full-width" :label="$t('决议说明')" label-style="top">
            <mt-input
              :disabled="true"
              v-model="basicInfo.appealDealDesc"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !costCenterExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('成本中心分摊') }}</div>
      <div class="sort-box" @click="costCenterExpand = !costCenterExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="costCenterExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="costCenterTemplateRef"
          :template-config="costCenterConfig"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !assessmentIndexExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('考核指标详情') }}</div>
      <div class="sort-box" @click="assessmentIndexExpand = !assessmentIndexExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="assessmentIndexExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="assessIndexTemplateRef"
          :template-config="assessmentIndexConfig"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import { costCenterColumn, assessmentIndexColumn } from './config/index'
export default {
  computed: {},
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      basicInfo: {},
      basicExpand: true,
      costCenterExpand: true,
      assessmentIndexExpand: true,
      handleClaimList: [
        {
          text: this.$t('维持原判'),
          value: 0
        },
        {
          text: this.$t('减免金额'),
          value: 1
        },
        {
          text: this.$t('改判指标'),
          value: 2
        },
        {
          text: this.$t('取消考核'),
          value: 3
        }
      ],
      costCenterConfig: [
        {
          grid: {
            allowPaging: false,
            lineIndex: 1,
            columnData: costCenterColumn,
            height: 350,
            dataSource: []
          }
        }
      ],
      assessmentIndexConfig: [
        {
          grid: {
            allowPaging: false,
            lineIndex: 1,
            columnData: assessmentIndexColumn,
            height: 350,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    this.basicInfo = {
      ...this.info.claimAppealDealHistory,
      feedbackEndTime: new Date(Number(this.info.claimAppealDealHistory.feedbackEndTime)),
      appealDealDesc: this?.info?.claimAppealDealHistory?.claimAppealDeal?.appealDealDesc,
      appealDeal: this?.info?.claimAppealDealHistory?.claimAppealDeal?.appealDeal
    }
    this.info.claimAppealDealHistory.standDetailList.forEach((e) => {
      e.happenTime = utils.formateTime(new Date(Number(e.happenTime)), 'yyyy-MM-dd hh:mm')
    })
    this.costCenterConfig[0].grid.dataSource = this.info.claimAppealDealHistory.costCenterList
    this.assessmentIndexConfig[0].grid.dataSource = this.info.claimAppealDealHistory.standDetailList
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
      /deep/ textarea {
        color: #000000de !important;
        -webkit-text-fill-color: #000000de;
      }
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
</style>
