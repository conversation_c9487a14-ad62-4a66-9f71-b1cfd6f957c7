<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <mt-dialog
      ref="toast"
      :header="$t('提示')"
      :buttons="buttons"
      :open="onOpen"
      size="small"
      :show-close-icon="false"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <div class="dialog-content">{{ $t('是否开票？') }}</div>
        <mt-form-item prop="invoiceFlag">
          <mt-radio v-model="ruleForm.invoiceFlag" :data-source="radioData"></mt-radio>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import MtRadio from '@mtech-ui/radio'
import { perPublishCols, assessColumn } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import { getCurrentBu } from '@/constants/bu'

export default {
  components: { MtRadio },
  data() {
    return {
      assessColumn,
      ruleForm: {
        invoiceFlag: 'false'
      },
      radioData: [
        {
          label: this.$t('是'),
          value: 'true'
        },
        {
          label: this.$t('否'),
          value: 'false'
        }
      ],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.deductAmount,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        // {
        //   gridId: '9ff7b082-ac3a-429f-986e-81b946efc382',
        //   title: this.$t('待发布'),
        //   toolbar: {
        //     useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        //     tools: [
        //       [
        //         'Add',
        //         'Edit',
        //         {
        //           id: 'publish',
        //           icon: 'icon_table_new',
        //           title: this.$t('提交')
        //         },
        //         'Delete',
        //         {
        //           id: 'audit',
        //           title: this.$t('查看OA审批'),
        //           icon: 'icon_solid_editsvg'
        //         },
        //         { id: 'Download', icon: 'icon_solid_Download', title: this.$t('导出') },
        //         {
        //           id: 'print',
        //           icon: 'icon_table_new',
        //           title: this.$t('打印')
        //         }
        //       ],
        //       ['Filter', 'Refresh', 'Setting']
        //     ]
        //   },
        //   useToolTemplate: false,
        //   grid: {
        //     columnData: perPublishCols,
        //     frozenColumns: 1,
        //     asyncConfig: {
        //       url: '/analysis/tenant/claim/pageClaim',
        //       params: { type: 0 }
        //     }
        //   }
        // },
        // {
        //   gridId: '7fa603ed-f6db-474f-a06d-1ce2af30cfe3',
        //   title: this.$t('考核单汇总'),
        //   toolbar: {
        //     useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        //     tools: [
        //       [
        //         {
        //           id: 'deductAmount',
        //           icon: 'icon_table_new',
        //           title: this.$t('确认扣款')
        //         },
        //         {
        //           id: 'print1',
        //           icon: 'icon_table_new',
        //           title: this.$t('打印')
        //         },
        //         { id: 'Download1', icon: 'icon_solid_Download', title: this.$t('导出') }
        //       ],
        //       ['Filter', 'Refresh', 'Setting']
        //     ]
        //   },
        //   useToolTemplate: false,
        //   grid: {
        //     columnData: assessColumn,
        //     frozenColumns: 1,
        //     asyncConfig: {
        //       url: '/analysis/tenant/claim/pageClaim',
        //       params: { type: 1 }
        //     }
        //   }
        // }
        {
          gridId: '9ff7b082-ac3a-429f-986e-81b946efc382',
          title: this.$t('待提交'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                'Add',
                'Edit',
                {
                  id: 'publish',
                  icon: 'icon_table_new',
                  title: this.$t('提交')
                },
                'Delete',
                // {
                //   id: 'audit',
                //   title: this.$t('查看OA审批'),
                //   icon: 'icon_solid_editsvg'
                // },
                { id: 'Download', icon: 'icon_solid_Download', title: this.$t('导出') },
                {
                  id: 'print',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: perPublishCols,
            // frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claim/pageClaim',
              params: { type: 9, bu: getCurrentBu() }
            }
          }
        },
        {
          gridId: 'ed9f7c29-7c94-4144-a696-171e2e450493',
          title: this.$t('待扣款'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'deductAmount',
                  icon: 'icon_table_new',
                  title: this.$t('确认扣款')
                },
                {
                  id: 'print1',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                },
                { id: 'Download1', icon: 'icon_solid_Download', title: this.$t('导出') }
                // { id: 'reject', icon: 'icon_solid_Delete', title: this.$t('驳回') }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: assessColumn,
            // frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claim/pageClaim',
              params: { type: 8, bu: getCurrentBu() }
            }
          }
        },
        {
          gridId: '7fa603ed-f6db-474f-a06d-1ce2af30cfe3',
          title: this.$t('考核单汇总'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                // {
                //   id: 'deductAmount',
                //   icon: 'icon_table_new',
                //   title: this.$t('确认扣款')
                // },
                { id: 'Download2', icon: 'icon_solid_Download', title: this.$t('导出') },
                {
                  id: 'print1',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                }
                // {
                //   id: 'cancel',
                //   title: this.$t('作废')
                // }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: assessColumn,
            // frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claim/pageClaim',
              params: { type: 7, bu: getCurrentBu() }
            }
          }
        }
      ],
      isLoading: false
    }
  },
  methods: {
    handleSelectTab(e) {
      if (e === 2 && this.assessColumn.every((item) => item.field !== 'confirmTime')) {
        const confirmTime = {
          field: 'confirmTime',
          headerText: this.$t('扣款时间'),
          ignore: true
        }
        this.assessColumn.splice(12, 0, confirmTime)
      } else if (e === 1 && this.assessColumn.some((item) => item.field === 'confirmTime')) {
        this.assessColumn.splice(
          this.assessColumn.findIndex((item) => item.field === 'confirmTime'),
          1
        )
      }
    },
    show() {
      this.ruleForm.invoiceFlag = 'false'
      this.$refs.toast.ejsRef.show()
    },
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
    onOpen: function (args) {
      args.preventFocus = true
    },
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (
        records.length <= 0 &&
        !(
          item.toolbar.id == 'Add' ||
          item.toolbar.id == 'Filter' ||
          item.toolbar.id == 'Refresh' ||
          item.toolbar.id == 'Setting' ||
          item.toolbar.id == 'audit' ||
          item.toolbar.id == 'Download' ||
          item.toolbar.id == 'Download1' ||
          item.toolbar.id == 'Download2' ||
          item.toolbar.id == 'reject' ||
          item.toolbar.id == 'cancel'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'cancel') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能选择一行进行作废操作'), type: 'warning' })
          return
        }
        this.handleCancel(records[0].id)
      }
      if (item.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          data: {
            title: this.$t('新建考核单')
          },
          success: (res) => {
            if (res.type === 'confirm') {
              this.$refs.templateRef.refreshCurrentGridData()
            } else {
              this.$router.push({
                name: 'purchase-assessmanage-assessListDetail',
                query: {
                  type: res.data.status,
                  id: res.data.id
                }
              })
            }
          }
        })
      } else if (item.toolbar.id == 'Edit') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
          return
        } else if (records.some((item) => item.status == 1)) {
          this.$toast({
            content: this.$t('已提交的数据无法编辑'),
            type: 'warning'
          })
          return
        } else if (records.some((item) => item.createType == 1)) {
          this.$toast({
            content: this.$t('自动创建的数据无法编辑'),
            type: 'warning'
          })
          return
        } else {
          this.$dialog({
            modal: () => import('./components/addDialog.vue'),
            data: {
              title: this.$t('编辑考核单'),
              isEdit: true,
              info: records[0]
            },
            success: (res) => {
              if (res.type === 'confirm') {
                this.$refs.templateRef.refreshCurrentGridData()
              } else {
                this.$router.push({
                  name: 'purchase-assessmanage-assessListDetail',
                  query: {
                    type: res.data.status,
                    id: res.data.id
                  }
                })
              }
            }
          })
        }
      } else if (item.toolbar.id == 'publish') {
        let _idList = records.map((e) => e.id)
        this.submitRecord(_idList)
      } else if (item.toolbar.id == 'Delete') {
        if (records.some((item) => item.status != 0 && item.status != 3)) {
          this.$toast({
            content: this.$t('非新增或审批拒绝的数据无法删除'),
            type: 'warning'
          })
          return
        }
        let _idList = records.map((e) => e.id)
        this.deleteRecord(_idList)
      } else if (item.toolbar.id == 'deductAmount') {
        if (records.some((item) => item.status != 12 && item.status != 15 && item.status != 16)) {
          this.$toast({
            content: this.$t('已确认、已改判、不改判以外的单据无法扣款'),
            type: 'warning'
          })
          return
        }
        this.ruleForm.idList = records.map((e) => e.id)
        // this.show() // 通过弹窗二次确认发请求
        this.deductAmount() // 跳过弹窗
      } else if (item.toolbar.id == 'audit') {
        this.audit(records)
      } else if (item.toolbar.id == 'print') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能打印单行'), type: 'warning' })
          return
        }
        // if (records[0]['status'] == 0 || records[0]['status'] == 1 || records[0]['status'] == 3) {
        //   this.$toast({ content: this.$t('新建、已提交、审批拒绝状态不能打印'), type: 'warning' })
        //   return
        // }
        this.printPdf(records[0].id)
      } else if (item.toolbar.id == 'print1') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能打印单行'), type: 'warning' })
          return
        }
        // if (records[0]['status'] == 0 || records[0]['status'] == 1 || records[0]['status'] == 3) {
        //   this.$toast({ content: this.$t('新建、已提交、审批拒绝状态不能打印'), type: 'warning' })
        //   return
        // }
        this.printPdf(records[0].id)
      } else if (item.toolbar.id == 'Download') {
        this.handleClickDownload(9)
      } else if (item.toolbar.id == 'Download1') {
        this.handleClickDownload(8)
      } else if (item.toolbar.id == 'Download2') {
        this.handleClickDownload(7)
      } else if (item.toolbar.id == 'reject') {
        this.handleClickReject(records)
      }
    },
    handleCancel(id) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认作废？')
        },
        success: () => {
          this.$API.assessManage.duplicateClaimApi({ id }).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('作废成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    handleClickReject(records) {
      if (records.length > 1) {
        this.$toast({ content: this.$t('只能驳回一行'), type: 'warning' })
        return
      } else if (records.some((item) => item.claimTotalAmount < 50000)) {
        this.$toast({
          content: this.$t('索赔总额小于五万无法驳回'),
          type: 'warning'
        })
        return
      }
      console.log('驳回-----', records)
    },
    //导出
    handleClickDownload(number) {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        type: number,
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        rules: rule.rules || []
      }
      this.$API.accessangeMange.export(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (['edit', 'delete'].includes(tool.id)) {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        if (tool.id === 'edit') {
          this.$dialog({
            modal: () => import('./components/addDialog.vue'),
            data: {
              title: this.$t('编辑考核单'),
              isEdit: true,
              info: data
            },
            success: (res) => {
              if (res.type === 'confirm') {
                this.$refs.templateRef.refreshCurrentGridData()
              } else {
                this.$router.push({
                  name: 'purchase-assessmanage-assessListDetail',
                  query: {
                    type: res.data.status,
                    id: res.data.id
                  }
                })
              }
            }
          })
        }
        tool.id === 'delete' && this.deleteRecord([data.id])
      }
    },
    deductAmount() {
      if (this.isLoading) {
        this.$toast({ content: this.$t('操作频繁，请稍等'), type: 'warning' })
        return
      }
      this.isLoading = true
      this.$API.assessManage
        .deductAmount({
          ...this.ruleForm,
          invoiceFlag: this.ruleForm.invoiceFlag === 'false' ? false : true
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('扣款成功'), type: 'success' })
            delete this.ruleForm.idList
            this.hide()
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    async printPdf(data) {
      let buffer = await this.$API.assessFeedback.purchaserPrintClaim({ id: data }).catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    submitRecord(ids) {
      this.$API.assessManage
        .submitClaim({
          idList: ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
    },
    deleteRecord(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？'),
          confirm: () =>
            this.$API.assessManage.deleteClaim({
              idList: ids
            })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].id,
        businessType: sltList[0].businessType ? sltList[0].businessType : ''
      }
      this.$API.assessManage.claimConfigGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field == 'claimCode') {
        let { data } = e
        this.$router.push({
          name: 'purchase-assessmanage-assessListDetail',
          query: {
            type: data.status,
            id: data.id
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
.dialog-content {
  margin-top: 20px;
}
</style>
