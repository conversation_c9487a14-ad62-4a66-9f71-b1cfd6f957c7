import { i18n } from '@/main'

// 状态列表
export const statusList = [
  { value: 0, text: i18n.t('新建') },
  { value: 1, text: i18n.t('已提交') },
  { value: 3, text: i18n.t('审批拒绝') },
  { value: 10, text: i18n.t('待反馈') },
  { value: 11, text: i18n.t('已反馈') },
  { value: 12, text: i18n.t('已确认') },
  { value: 13, text: i18n.t('申诉处理审批中') },
  { value: 14, text: i18n.t('重新改判') },
  { value: 15, text: i18n.t('已改判') },
  { value: 16, text: i18n.t('不改判') },
  { value: 17, text: i18n.t('已付款') },
  { value: -1, text: i18n.t('已取消') }
]
// 统计维度列表
export const statisticDimensionList = [
  { value: 'MONTH', text: i18n.t('月度') },
  { value: 'SEASON', text: i18n.t('季度') },
  { value: 'HALF-YEAR', text: i18n.t('半年度') },
  { value: 'YEAR', text: i18n.t('年度') }
]
// 品类维度列表
export const categoryDimensionList = [
  { value: 'bk', text: i18n.t('背光组') },
  { value: 'st', text: i18n.t('结构组') },
  { value: 'et', text: i18n.t('电子组') }
]
// 索赔金额列表
export const claimAmountList = [
  { value: 1, text: '>=50000' },
  { value: 2, text: '<50000' }
]
// 排名列表
export const rankList = [{ value: true, text: 'Top 10' }]
// 是否列表
export const isNotList = [
  { value: false, text: i18n.t('否') },
  { value: true, text: i18n.t('是') }
]
