export default {
  data() {
    return {
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info' }]
    }
  },
  computed: {
    // 统计年份
    statisticYearList() {
      const curYear = new Date().getFullYear()
      return [
        { value: curYear - 1, text: curYear - 1 },
        { value: curYear, text: curYear }
      ]
    },
    // 统计维度
    statisticDimensionMap() {
      // 月份
      const monthList = []
      for (let index = 1; index < 13; index++) {
        monthList.push({
          value: index,
          text: index + this.$t('月')
        })
      }

      return {
        MONTH: {
          label: this.$t('月份'),
          key: 'MONTH',
          list: monthList
        },
        SEASON: {
          label: this.$t('季度'),
          key: 'SEASON',
          list: [
            { value: '1,2,3', text: this.$t('第1季度') },
            { value: '4,5,6', text: this.$t('第2季度') },
            { value: '7,8,9', text: this.$t('第3季度') },
            { value: '10,11,12', text: this.$t('第4季度') }
          ]
        },
        'HALF-YEAR': {
          label: this.$t('半年度'),
          key: 'HALF-YEAR',
          list: [
            { value: '1,2,3,4,5,6', text: this.$t('上半年度') },
            { value: '7,8,9,10,11,12', text: this.$t('下半年度') }
          ]
        },
        YEAR: {
          label: this.$t('年度'),
          key: 'YEAR',
          list: [{ value: 'all', text: this.$t('全年度') }]
        }
      }
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50
        },
        {
          field: 'claimCode',
          title: this.$t('考核单编码'),
          minWidth: 180
        },
        {
          field: 'claimTypeName',
          title: this.$t('考核类型')
        },
        {
          field: 'claimTypeCode',
          title: this.$t('考核指标')
        },
        {
          field: 'companyCode',
          title: this.$t('所属公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.supplierCode + '-' + row.supplierName}</span>]
            }
          }
        },
        {
          field: 'statusDesc',
          title: this.$t('状态')
        },
        {
          field: 'createTypeDesc',
          title: this.$t('单据来源')
        },
        {
          field: 'offlineEnsureDesc',
          title: this.$t('供应商是否已经线下确认'),
          minWidth: 180
        },
        {
          field: 'claimTotalAmount',
          title: this.$t('索赔总额')
        },
        {
          field: 'claimMonth',
          title: this.$t('考核月份')
        },
        {
          field: 'itemCode',
          title: this.$t('考核品类'),
          slots: {
            default: ({ row }) => {
              return [<span>{row.itemCode + '-' + row.itemName}</span>]
            }
          }
        },
        {
          field: 'feedbackEndTimeDesc',
          title: this.$t('要求反馈日期')
        },
        {
          field: 'createUserDepartment',
          title: this.$t('创建人部门')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'confirmTime',
          title: this.$t('扣款时间')
        },
        {
          field: 'overdue',
          title: this.$t('是否逾期未反馈'),
          minWidth: 150
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
