<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="yearList" :label="$t('统计年份')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.yearList"
            :data-source="statisticYearList"
            :show-clear-button="true"
            :show-select-all="true"
            :select-all-text="$t('全选')"
            :un-select-all-text="$t('全不选')"
            :placeholder="$t('请选择统计年份')"
          />
        </mt-form-item>
        <mt-form-item prop="analysisDimension" :label="$t('统计维度')" label-style="top">
          <mt-select
            v-model="searchFormModel.analysisDimension"
            css-class="rule-element"
            :data-source="statisticDimensionList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择统计维度')"
            @change="handleStatisticDimensionChange"
          />
        </mt-form-item>
        <mt-form-item
          v-if="searchFormModel.analysisDimension"
          :prop="statisticDimensionMap[searchFormModel.analysisDimension].key"
          :label="statisticDimensionMap[searchFormModel.analysisDimension].label"
          label-style="top"
        >
          <mt-multi-select
            v-if="statisticDimensionMap[searchFormModel.analysisDimension].key !== 'YEAR'"
            v-model="searchFormModel[searchFormModel.analysisDimension]"
            :data-source="statisticDimensionMap[searchFormModel.analysisDimension].list"
            :show-clear-button="true"
            :show-select-all="true"
            :select-all-text="$t('全选')"
            :un-select-all-text="$t('全不选')"
            :placeholder="
              $t('请选择') + statisticDimensionMap[searchFormModel.analysisDimension].label
            "
          />
          <mt-select
            v-else
            v-model="searchFormModel.YEAR"
            :data-source="statisticDimensionMap.YEAR.list"
            :show-clear-button="true"
            :placeholder="$t('请选择年度')"
          />
        </mt-form-item>
        <mt-form-item prop="groupTypeList" :label="$t('品类维度')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.groupTypeList"
            :data-source="categoryDimensionList"
            :show-clear-button="true"
            :show-select-all="true"
            :select-all-text="$t('全选')"
            :un-select-all-text="$t('全不选')"
            :placeholder="$t('请选择品类维度')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCodeList" :label="$t('公司')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCodeList"
            :multiple="true"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :title-switch="false"
            select-type="administrativeCompany"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
        <mt-form-item prop="amountLimit" :label="$t('索赔金额')" label-style="top">
          <mt-select
            v-model="searchFormModel.amountLimit"
            :data-source="claimAmountList"
            :show-clear-button="true"
            :placeholder="$t('请选择索赔金额')"
          />
        </mt-form-item>
        <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            :data-source="statusList"
            :show-clear-button="true"
            :show-select-all="true"
            :select-all-text="$t('全选')"
            :un-select-all-text="$t('全不选')"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="claimTotalCountLimit" :label="$t('索赔总次数')" label-style="top">
          <mt-select
            v-model="searchFormModel.claimTotalCountLimit"
            :data-source="rankList"
            :show-clear-button="true"
            :placeholder="$t('请选择索赔总次数')"
          />
        </mt-form-item>
        <mt-form-item prop="claimTotalAmountLimit" :label="$t('索赔总额')" label-style="top">
          <mt-select
            v-model="searchFormModel.claimTotalAmountLimit"
            :data-source="rankList"
            :show-clear-button="true"
            :placeholder="$t('请选择索赔总额')"
          />
        </mt-form-item>
        <mt-form-item prop="overdue" :label="$t('是否逾期未反馈')" label-style="top">
          <mt-select
            v-model="searchFormModel.overdue"
            :data-source="isNotList"
            :show-clear-button="true"
            :placeholder="$t('请选择是否逾期未反馈')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="52b9937c-e732-4ee3-86ee-8f7efbc5588e"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      show-footer
      :footer-method="footerMethod"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import {
  statusList,
  statisticDimensionList,
  categoryDimensionList,
  claimAmountList,
  rankList,
  isNotList
} from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: { ScTable, CollapseSearch, RemoteAutocomplete },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      tableData: [],
      loading: false,
      statisticDimensionList,
      categoryDimensionList,
      statusList,
      claimAmountList,
      rankList,
      isNotList,
      sumClaimTotalAmount: 0
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    queryParams() {
      const { analysisDimension, claimTotalCountLimit, claimTotalAmountLimit } =
        this.searchFormModel
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel,
        dimensionValue:
          analysisDimension === 'YEAR' ? null : this.searchFormModel[analysisDimension]?.join(','),
        claimTotalCountLimit: claimTotalCountLimit ? true : false,
        claimTotalAmountLimit: claimTotalAmountLimit ? true : false
      }
      return params
    }
  },
  methods: {
    // table表汇总
    footerMethod(e) {
      const { columns } = e
      const resList = []
      columns.forEach((col) => {
        if (col.field === 'claimTotalAmount') {
          resList.push(this.$t('汇总') + '：' + this.sumClaimTotalAmount)
        } else {
          resList.push('-')
        }
      })
      return [resList]
    },
    // 选择统计维度
    handleStatisticDimensionChange(e) {
      const keyList = ['MONTH', 'SEASON', 'HALF-YEAR', 'YEAR']
      for (let key in keyList) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      e.value === 'YEAR' && this.$set(this.searchFormModel, 'YEAR', 'all')
    },
    // 获取table数据
    async getTableData() {
      this.loading = true
      const res = await this.$API.assessManage
        .queryAssessAnalysisReportList(this.queryParams)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.sumClaimTotalAmount = res.data?.sum
        this.tableData = res.data?.pageData?.records || []
        this.total = res.data?.pageData?.total || 0
      }
    },

    // 点击按钮栏
    async handleClickToolBar(e) {
      if (e.code === 'export') {
        const params = {
          ...this.queryParams,
          page: { current: 1, pageSize: 100000 }
        }
        const res = await this.$API.assessManage.exportAssessAnalysisReport(params)
        if (res.data) {
          this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
          download({ fileName: getHeadersFileName(res), blob: res.data })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
