<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '30e0c949-d9e3-434d-a465-f864a96467db',
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                'Add',
                'Edit',
                {
                  id: 'enable',
                  icon: 'icon_table_new',
                  title: this.$t('启用')
                },
                {
                  id: 'disable',
                  icon: 'icon_table_new',
                  title: this.$t('停用')
                },
                'Delete'
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: columnData,
            frozenColumns: 1,
            asyncConfig: {
              url: '/analysis/tenant/claimType/pageClaimType'
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(item) {
      console.log('handleClickToolBar=', item)
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (
        records.length <= 0 &&
        !(
          item.toolbar.id == 'Add' ||
          item.toolbar.id == 'Filter' ||
          item.toolbar.id == 'Refresh' ||
          item.toolbar.id == 'Setting'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          data: {
            title: this.$t('新增考核类型')
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (item.toolbar.id == 'Edit') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
          return
        } else if (records[0].status === 1) {
          this.$toast({
            content: this.$t('启用状态的数据无法编辑'),
            type: 'warning'
          })
          return
        } else {
          this.editRecord(records[0])
        }
      }
      if (item.toolbar.id == 'Delete') {
        if (records.some((item) => item.status === 1)) {
          this.$toast({
            content: this.$t('存在处于启用状态的数据，不可删除'),
            type: 'warning'
          })
          return
        } else {
          let _idList = records.map((e) => e.id)
          this.deleteRecord(_idList)
        }
      }
      if (item.toolbar.id === 'enable' || item.toolbar.id === 'disable') {
        const toStatus = item.toolbar.id === 'enable' ? 1 : -1
        if (records && records.length) {
          let len = records.length
          let ids = []
          for (let i = 0; i < len; i++) {
            const { id, status } = records[i]
            if (status === toStatus) {
              this.$toast({
                content: `${this.$t('当前已有处于')}${
                  item.toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')
                }${this.$t('状态')}`,
                type: 'warning'
              })
              return
            }
            ids.push(id)
          }

          let params = {
            operationType: toStatus,
            idList: ids
          }
          this.updateStatus(params)
        }
      }
    },
    updateStatus(params) {
      this.$API.assessManage.changeStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    changeFieldName(e) {
      console.log('changeFieldName=', e)
    },
    deleteRecord(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？'),
          confirm: () =>
            this.$API.assessManage.deleteRecord({
              idList: ids
            })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editRecord(data) {
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('编辑考核类型'),
          isEdit: true,
          info: data
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (['edit', 'delete'].includes(tool.id)) {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        if (tool.id === 'edit') {
          this.editRecord(data)
        }
        tool.id === 'delete' && this.deleteRecord([data.id])
      }
      if (['enable', 'disable'].includes(tool.id)) {
        const toStatus = tool.id === 'enable' ? 1 : -1
        let params = {
          operationType: toStatus,
          idList: [data.id]
        }
        this.updateStatus(params)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
