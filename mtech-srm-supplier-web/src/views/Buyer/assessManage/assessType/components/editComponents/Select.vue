<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
import bus from '@/utils/bus'
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      companyName: '',
      isDisabled: false,
      siteName: '',
      admOrgName: ''
    }
  },
  async mounted() {
    if (this.data.column.field === 'companyName') {
      bus.$emit('companyChange', this.data.companyName)
      let _typeCode = sessionStorage.getItem('assessTypeCode')
      //业务公司下拉
      this.listCompanyByClaimType({ claimTypeCode: _typeCode })
    }
    if (this.data.column.field === 'siteName') {
      //监听isDump变化
      bus.$on('companyChange', (val) => {
        console.log('=isCompany变=', val, this)
        if (val?.value) {
          this.data.companyName = val.value
          this.data.companyId = val.itemData.companyId
          this.data.siteName = null
          this.data.siteId = null
        } else {
          this.data.siteName = null
          this.data.siteId = null
        }
      })
      if (this.data.companyId) {
        this.getSite()
      }
      this.fields = { text: 'siteName', value: 'siteName' }
    }
    if (this.data.column.field === 'admOrgName') {
      bus.$emit('busCompChange', this.data.admOrgName)
      //公司下拉
      this.getOrgs({
        organizationLevelCodes: ['ORG02'],
        orgType: 'ORG001ADM',
        includeItself: true,
        organizationIds: []
      })
    }
    if (this.data.column.field === 'employeeName') {
      //监听isDump变化
      bus.$on('busCompChange', (val) => {
        console.log('=isbusComp变=', val, this)
        if (val?.value) {
          this.data.admOrgName = val.value
          this.data.admOrgId = val.itemData.id
          this.data.employeeId = null
          this.data.employeeCode = null
          this.data.employeeName = null
        } else {
          this.data.employeeId = null
          this.data.employeeCode = null
          this.data.employeeName = null
        }
      })
      if (this.data.admOrgId) {
        this.getEmployee()
      }
      this.fields = { text: 'employeeName', value: 'employeeName' }
    }
  },
  methods: {
    //获取公司
    listCompanyByClaimType(param) {
      this.$API.assessManage.listCompanyByClaimType(param).then((res) => {
        this.dataSource = res.data
      })
      this.fields = { text: 'companyName', value: 'companyName' }
    },
    async getEmployee() {
      await this.$API.masterData
        .getOrganizationEmployees({
          fuzzyParam: this.inputName,
          orgId: this.data.admOrgId
        })
        .then((res) => {
          this.dataSource = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async getSite() {
      await this.$API.assessManage
        .fuzzySiteQuery({
          fuzzyParam: this.inputName,
          organizationId: this.data.companyId
        })
        .then((res) => {
          this.dataSource = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    serchText(val) {
      console.log('搜索值', val, this.data.column.field)
      val.updateData(this.dataSource.filter((e) => e[this.fields.value].includes(val.text)))
    },
    getOrgs(data) {
      this.$API.masterData.findSpecifiedChildrenLevelOrgs(data).then((res) => {
        this.dataSource = res.data
      })
      this.fields = { text: 'orgName', value: 'orgName' }
    },
    startOpen() {
      // 公司与工厂联动
      if (this.data.column.field === 'siteName') {
        console.log('startOpen=', this)
        if (this.data.companyId) {
          this.getSite()
        }
      }
      if (this.data.column.field === 'employeeName') {
        console.log('startOpen=', this)
        if (this.data.admOrgId) {
          this.getEmployee()
        }
      }
      console.log(this.data, '下拉打开时最新的行数据', this)
      // if (this.data.column.field === "companyName") {
      //   if (!this.dataSource.length) {
      //     this.getOrgs();
      //   }
      // }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log('val', val, this.data)
      if (this.data.column.field === 'companyName') {
        bus.$emit('companyChange', val) //传给物料描述
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'companyName',
          itemInfo: {
            companyId: val.itemData.companyId,
            companyCode: val.itemData.companyCode
          }
        })
      }
      if (this.data.column.field === 'siteName') {
        // isInner字段，判断是否内部供应商 ,内部供应商时，“转存类货源”字段可选择，否则不可选默认否
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'siteName',
          itemInfo: {
            siteId: val.itemData.id,
            siteCode: val.itemData.siteCode
          }
        })
      }
      if (this.data.column.field === 'admOrgName') {
        bus.$emit('busCompChange', val) //传给仲裁人
        // isInner字段，判断是否内部供应商 ,内部供应商时，“转存类货源”字段可选择，否则不可选默认否
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'admOrgName',
          itemInfo: {
            admOrgId: val.itemData.id,
            admOrgCode: val.itemData.orgCode
          }
        })
      }
      if (this.data.column.field === 'employeeName') {
        // isInner字段，判断是否内部供应商 ,内部供应商时，“转存类货源”字段可选择，否则不可选默认否
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'employeeName',
          itemInfo: {
            employeeNameId: val.itemData.id,
            employeeNameCode: val.itemData.employeeCode,
            employeeId: val.itemData.userId
          }
        })
      }
    }
  }
}
</script>
