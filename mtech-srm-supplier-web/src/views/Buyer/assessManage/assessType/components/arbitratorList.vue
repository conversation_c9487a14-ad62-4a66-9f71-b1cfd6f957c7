<template>
  <mt-dialog ref="arbitratorDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="full-height">
      <mt-template-page
        ref="templateRefEdit"
        :key="templateKey"
        :use-tool-template="false"
        :template-config="templateConfigEdit"
        @handleClickToolBar="handleClickToolBarEdit"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @selectedChanged="selectedChanged"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>
<script>
import { arbitratorColumn } from '../config/index'
import { cloneDeep } from 'lodash'
import bus from '@/utils/bus'
export default {
  data() {
    return {
      selectedOtherInfo: {},
      isInner: '',
      templateConfigEdit: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            lineIndex: 1,
            columnData: arbitratorColumn,
            allowPaging: false,
            dataSource: []
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    claimTypeId() {
      return this.modalData.id
    },
    typeCode() {
      return this.modalData.typeCode
    }
  },
  mounted() {
    bus.$on(`companyChange`, (txt) => {
      console.log('在双重单元格中，监听到了公司变化了', this.fieldName, txt)
      this.nowRowHasItemCode = txt ? true : false
    })
    bus.$on(`busCompChange`, (txt) => {
      console.log('在双重单元格中，监听到了行政公司变化了', this.fieldName, txt)
      this.nowRowHasItemCode = txt ? true : false
    })
    console.log('modalData=', this.modalData)
    this.getList()
    this.show()
  },
  methods: {
    selectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedOtherInfo, val.itemInfo || {})
      console.log(this.selectedOtherInfo, '最新的额外数据导入的')
    },
    actionBegin(args) {
      let { data, requestType } = args
      // 行内数据新增
      if (requestType == 'Add') {
        data.isDumpDesc = this.$t('否') //默认为否
        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      } else if (requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
      } else if (requestType == 'save') {
        data.isInner = this.isInner
      }
    },
    actionComplete(item) {
      let { data, requestType } = item
      if (requestType == 'save') {
        // 验证必输
        if (!data.admOrgName || !data.siteName || !data.admOrgName || !data.employeeName) {
          this.$toast({
            content: this.$t('有字段未输入'),
            type: 'warning'
          })
          this.inputState = false
        }
      }
      let row = this.getRow()
      console.log('actionComplete', item, row)
      this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRefEdit
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    handleClickToolBarEdit(args) {
      const { toolbar, grid } = args
      console.log('handleClickToolBarEdit', this, args)
      if (toolbar.id == 'Add') {
        // 新增
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'Delete') {
        if (this.type == 'edit') {
          this.$toast({
            content: this.$t('编辑状态下不可删除行数据'),
            type: 'warning'
          })
        } else {
          // 选中的数据
          let selectedRecords = grid.getSelectedRecords()
          if (selectedRecords.length > 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                // let allData = this.$refs.templateRefEdit
                //   .getCurrentUsefulRef()
                //   .gridRef.ejsRef.getCurrentViewRecords();
                // selectedRecords.forEach((item, index) => {
                //   allData.forEach((sitem, sindex) => {
                //     if (item.cid == sitem.cid) {
                //       allData.splice(sindex, 1);
                //     }
                //   });
                // });
                // this.$set(
                //   this.templateConfigEdit[0].grid,
                //   "dataSource",
                //   allData
                // );
                this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
              }
            })
          } else {
            this.$toast({
              content: this.$t('请选择数据'),
              type: 'warning'
            })
          }
        }
      } else if (toolbar.id == 'endEdit') {
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    getList() {
      this.$API.assessManage
        .listClaimAppealArbitrator({ claimTypeId: this.claimTypeId })
        .then((res) => {
          if (res.code === 200) {
            this.templateConfigEdit[0].grid.dataSource = res.data
          }
        })
      // this.templateConfigEdit[0].grid.dataSource = [
      //   {
      //     companyName: "TCL家用电器（合肥）有限公司",
      //     companyId: "1512620484903219202",
      //     siteName: "冰洗BU工厂",
      //     siteId: "1512620490330648577",
      //     admOrgName: "奥马电器",
      //     admOrgId: "1512441132042387458",
      //     employeeName: "周子涵",
      //     companyCode: "0530",
      //     siteCode: "1200",
      //     admOrgCode: "dz90010354",
      //     employeeNameId: "1512610400542306306",
      //     employeeNameCode: "00015349",
      //   },
      // ];
    },
    show() {
      this.$refs['arbitratorDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['arbitratorDialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      setTimeout(() => {
        let _tempData = this.$refs.templateRefEdit
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
        console.log('confirm=', _tempData)
        this.$API.assessManage
          .saveClaimAppealArbitrator({
            claimTypeAppealArbitratorList: _tempData,
            claimTypeId: this.claimTypeId
          })
          .then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
          .catch((err) => {
            this.$toast({
              content: err.msg || this.$t('系统异常'),
              type: 'error'
            })
          })
      }, 100)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('assessTypeCode')
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
