<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>

      <div class="slider-content">
        <mt-form
          ref="ruleForm"
          class="ruleForm"
          :model="ruleForm"
          :rules="rules"
          :auto-complete="false"
        >
          <mt-form-item :label="$t('协议书名称')" prop="agreementName">
            <mt-input v-model="ruleForm.agreementName" :disabled="ruleForm.status == 1"></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('考核类型')" prop="claimTypeCode">
            <mt-select
              v-model="ruleForm.claimTypeCode"
              :data-source="claimTypeList"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
              :fields="{ text: 'dictName', value: 'dictCode' }"
              @change="changeclaimType"
              :disabled="ruleForm.status == 1"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="company" class="full-width" :label="$t('所属公司')">
            <mt-multi-select
              :disabled="ruleForm.status == 1"
              v-model="ruleForm.company"
              :show-clear-button="true"
              :data-source="companyList"
              :placeholder="$t('请选择')"
              @change="companyChange"
              :fields="{ text: 'companyName', value: 'companyCode' }"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item class="full-width" :label="$t('备注')" prop="remark">
            <mt-input
              :disabled="ruleForm.status == 1"
              v-model="ruleForm.remark"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item class="full-width" :label="$t('变量库')" prop="variableLibrary">
            <mt-select
              v-if="ruleForm.status != 1"
              v-model="ruleForm.variableLibrary"
              :data-source="variableLibraryList"
              :placeholder="$t('请选择')"
              :fields="{ text: 'text', value: 'value' }"
              @change="changeVariableLibrary"
            ></mt-select>
          </mt-form-item>
        </mt-form>

        <div class="edit-box">
          <!-- <mt-rich-text-editor
            :disabled="ruleForm.status == 1"
            :created="onCreate"
            ref="editorRef"
            :height="600"
            css-class="rich-editor"
            v-model.trim="templateText"
            :toolbar-settings="toolbarSettings"
            :quick-toolbar-settings="quickToolbarSettings"
            :insert-image-settings="insertImageSettings"
            @change="changeText"
            :enable-xhtml="true"
          >
          </mt-rich-text-editor> -->
          <rich-text-editor
            v-if="variableList.length"
            :disabled="ruleForm.status == 1"
            ref="editorRef"
            :height="600"
            v-model.trim="templateText"
            :toolbar="clientToolbar"
          >
          </rich-text-editor>
        </div>
      </div>
      <div class="slider-footer mt-flex">
        <span @click="confirm" v-if="ruleForm.status == 0 || ruleForm.status == -1">{{
          $t('保存')
        }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { defaultTemp3 } from '../config/column'
import Vue from 'vue'
import { getSupplierDict } from '@/utils/utils'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'

let proxy = undefined

export default {
  components: {
    RichTextEditor
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      variableDictCode: '',
      variableLibraryList: [
        { text: this.$t('质量索赔'), value: 1 },
        { text: this.$t('呆料索赔'), value: 2 }
      ],
      clientToolbar: `bold italic underline | formatselect alignleft aligncenter alignright alignjustify bullist numlist | table link image backcolor | code undo redo paste | lineheight| parmarsSetButton | fullscreen`,
      templateText: '',
      ruleForm: {
        agreementName: '',
        company: '',
        companyList: [],
        remark: ''
      },
      claimTypeList: [],
      companyList: [],
      rules: {
        variableLibrary: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        agreementName: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        company: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        claimTypeCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      insertImageSettings: { saveFormat: 'Base64' },
      toolbarSettings: {
        items: [
          {
            tooltipText: 'Insert Symbol',
            undo: true,
            click: this.onClick.bind(this),
            template: function () {
              return {
                template: Vue.component('actionOption', {
                  template: `<div><span style="color:red">*</span><span>变量库：</span><mt-select v-model="varValue" :dataSource="variableList" id="custom_tbar" :fields="{ value: 'itemCode', text: 'itemName' }" placeholder="请选择"  @select="insertVar" style="width:auto"></mt-select></div>`,
                  data() {
                    return {
                      data: {},
                      variableList: [],
                      varValue: ''
                    }
                  },
                  mounted() {
                    this.$API.masterData
                      .dictionaryGetList({
                        dictCode: 'ClaimAgreementTemplateVariable'
                      })
                      .then((res) => {
                        this.variableList = res.data
                      })
                  },
                  computed: {},
                  methods: {
                    insertVar(e) {
                      console.log('insertVar==', e, this.data, proxy)
                      proxy.$refs.editorRef.ejsRef.executeCommand(
                        'insertText',
                        '${' + e.itemData.itemCode + '}',
                        {
                          undo: true
                        }
                      )
                    }
                  }
                })
              }
            }
          },
          'insertCode',
          'Bold',
          'Italic',
          'Underline',
          'StrikeThrough',
          'FontName',
          'FontSize',
          'FontColor',
          'BackgroundColor',
          'LowerCase',
          'UpperCase',
          'SuperScript',
          'SubScript',
          '|',
          'Formats',
          'Alignments',
          'NumberFormatList',
          'BulletFormatList',
          'Outdent',
          'Indent',
          '|',
          'CreateTable',
          'CreateLink',
          'Image',
          'FileManager',
          '|',
          'ClearFormat',
          'Print',
          'SourceCode',
          'FullScreen',
          '|',
          'Undo',
          'Redo'
        ]
      },
      quickToolbarSettings: {
        table: [
          'TableHeader',
          'TableRows',
          'TableColumns',
          'TableCell',
          '-',
          'BackgroundColor',
          'TableRemove',
          'TableCellVerticalAlign',
          'Styles'
        ]
      },
      variableList: []
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    this.getVariableList('ClaimAgreementTemplateVariable') // 获取富文本变量库枚举
  },
  mounted() {
    if (this.modalData.type == 'add') {
      this.ruleForm.status = 0
    }
    // this.ruleForm.variableLibrary = 1
    this.initData()
  },
  methods: {
    changeVariableLibrary(e) {
      const { itemData } = e
      if (itemData.value === 2) {
        this.variableList = []
        this.getVariableList('IdleMaterialClaimAgreementTemplateVariable')
        this.variableDictCode = 'IdleMaterialClaimAgreementTemplateVariable'
      } else if (itemData.value === 1) {
        this.variableList = []
        this.getVariableList('ClaimAgreementTemplateVariable')
        this.variableDictCode = 'ClaimAgreementTemplateVariable'
      }
    },
    getVariableList(dictCode) {
      this.$API.masterData
        .dictionaryGetList({
          dictCode
        })
        .then((res) => {
          this.variableList = res.data.map(function (item) {
            return {
              type: 'choiceitem',
              text: item.itemName,
              value: item.itemCode
            }
          })
          sessionStorage.setItem('variableList', JSON.stringify(this.variableList))
        })
    },
    onCreate() {
      console.log('onCreate===')
      proxy = this
    },
    onClick() {
      console.log('onClick==', this.$refs.editorRef)
    },
    async initData() {
      await this.getAvailableClaimType()
      if (this.isEdit) {
        this.$API.assessManage
          .detailClaimAgreeTemp({
            id: this.info.id
          })
          .then((res) => {
            if (res.code == 200) {
              this.getOrgs({ claimTypeCode: res.data.claimTypeCode })
              this.$nextTick(() => {
                this.ruleForm = {
                  ...res.data,
                  company: res.data.companyList.map((e) => e.companyCode)
                }
              })
              this.templateText = res.data.templateText
            }
          })
      } else {
        this.templateText = defaultTemp3 //带值, defaultTemp==不带值
      }
    },
    //获取公司
    async getOrgs(param) {
      await this.$API.assessManage.listCompanyByClaimType(param).then((res) => {
        this.companyList = res.data
      })
    },
    getAvailableClaimType() {
      this.claimTypeList = getSupplierDict('claimType')
    },
    changeclaimType(e) {
      if (e.value) {
        // this.ruleForm.claimTypeId = e.itemData.dictCode
        this.ruleForm.claimTypeName = e.itemData.dictName
        if (e.e != null) {
          this.ruleForm.companyList = []
          this.ruleForm.company = ''
        }
        this.getOrgs({ claimTypeCode: e.value })
      }
    },
    companyChange(e) {
      this.ruleForm.companyList.length = 0
      if (e.value.length > 0) {
        this.companyList.forEach((e) => {
          if (this.ruleForm.company.includes(e.companyCode)) {
            this.ruleForm.companyList.push({
              companyCode: e.companyCode,
              companyName: e.companyName,
              companyId: e.companyId
            })
          }
        })
        console.log('companyChange=', e, this.ruleForm.companyList)
      }
    },
    changeText(e) {
      console.log('changeText=', e)
      // this.ruleForm.templateText = e.value;
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      // console.log(
      //   'confirm',
      //   this.templateText,
      //   this.$refs.editorRef.ejsRef.getContent(),
      //   this.$refs.editorRef.ejsRef.getHtml()
      // )
      if (this.templateText.length === 0 || this.templateText === '<div><p><br/></p></div>') {
        this.$toast({ content: this.$t('请先添加模板'), type: 'warning' })
        return
      } else {
        this.ruleForm.templateText = this.templateText
        this.ruleForm['variableDictCode'] = this.variableDictCode
        // this.ruleForm.templateText =  this.templateText.replaceAll('<br>','<br/>');
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$API.assessManage
            .saveClaimAgreeTemp(this.ruleForm)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px 10px 10px;
}
/deep/ .select-container {
  height: 46px;
  padding: 5px;
}
/deep/ .e-input-group {
  padding-left: 5px;
}
.search-box {
  display: flex;
  border-bottom: 1px solid #e8e8e8;

  .searchInput {
    width: calc(100% - 34px);
    padding-right: 10px;
    /deep/ .e-input-group {
      border: none;
    }
  }
}

.slider-panel-container {
  .slider-content {
    overflow-y: auto;
  }
  .slider-modal {
    width: 850px;
    border: none;
    .slider-header {
      height: 58px;
      background: #31374e;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }

    .add-rule-group {
      margin-top: 30px;
      height: 14px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      width: 100%;
      text-align: center;
      cursor: pointer;
    }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
.ruleForm {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .fbox {
    display: flex;
  }
  .mt-form-item {
    width: 365px;
    margin-bottom: 16px;
  }
  .full-width {
    width: 810px;
  }
}
.edit-box {
  width: 100%;
  background: #fff;
  .setting-banner {
    width: 100%;
    height: 50px;
    cursor: pointer;
    padding: 0 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(79, 91, 109, 1);

    i {
      font-size: 16px;
      line-height: 14px;
      display: inline-block;
      height: 16px;
      width: 16px;
      cursor: pointer;
    }

    span {
      display: inline-block;
      margin-left: 6px;
      cursor: pointer;
    }
  }
  .rich-editor {
    height: 100%;
  }
  .mt-rich-text-editor {
    height: 100%;
  }
}
/deep/ .mt-rich-text-editor {
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    height: 600px !important;
  }
}
</style>
