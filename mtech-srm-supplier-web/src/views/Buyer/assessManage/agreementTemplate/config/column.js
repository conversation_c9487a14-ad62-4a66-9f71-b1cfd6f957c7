import { i18n } from '@/main'
import utils from '@/utils/utils'
export const registColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'agreementCode',
    headerText: i18n.t('协议书编码'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.status != 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    width: '200',
    field: 'agreementName',
    headerText: i18n.t('协议书名称')
  },
  {
    width: '130',
    field: 'claimTypeName',
    headerText: i18n.t('考核类型')
  },
  {
    width: '250',
    field: 'companyNameStr',
    headerText: i18n.t('公司')
  },
  {
    width: '130',
    field: 'version',
    headerText: i18n.t('版本'),
    ignore: true
  },
  {
    width: '130',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('新建'), 1: i18n.t('启用'), '-1': i18n.t('停用') }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data.status != 1
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data.status == 1
        }
      }
    ]
  },

  {
    width: '100',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    ignore: true
  },
  {
    width: '130',
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formateTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    },
    ignore: true
  },
  {
    width: '100',
    field: 'remark',
    headerText: i18n.t('备注'),
    ignore: true
  }
]

export const defaultTemp =
  '<div style="text-align: left;"><span style="font-size: 12pt;"><sup>TCL王牌电器(惠州)有限公司</sup></span><span style="font-size: 12pt;"><br/></span></div><div style="text-align: center;"><span style="font-size: 18pt;">索赔协议</span></div><p style="text-align: center;">&nbsp; &nbsp; &nbsp; 索赔单号：&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 日期：&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<br/></p><div style="width: 100%;margin:0 auto;"><table class="e-rte-table" style=" background-color: transparent; margin: 0px auto; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; width: 100%; min-width: 0px;"><tbody style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 45px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供方</p></td><td colspan="4" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 417.438px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><p style="text-align: center;">索赔类型</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 162px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">T<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">C<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">L<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">索<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">赔<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">原<br/></p><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">因</p></td><td colspan="6" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 704.094px;" class=""><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td rowspan="4" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;">索赔</p><span style=" margin: 0px; padding: 0px; border: 0px; line-height: inherit; font-family: Roboto, &quot;Segoe UI&quot;, GeezaPro, &quot;DejaVu Serif&quot;, &quot;sans-serif&quot;, -apple-system, BlinkMacSystemFont; vertical-align: baseline; color: rgb(51, 51, 51);">项目</span></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">索赔项目描述</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><p style="text-align: center;">币种</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><p style="text-align: center;">税率</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><span style=" margin: 0px; padding: 0px; border: 0px; line-height: inherit; font-family: Roboto, &quot;Segoe UI&quot;, GeezaPro, &quot;DejaVu Serif&quot;, &quot;sans-serif&quot;, -apple-system, BlinkMacSystemFont; vertical-align: baseline; color: rgb(51, 51, 51);"><p style="text-align: center;">税额</p></span></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><p style="text-align: center;">不含税金额</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><p style="text-align: center;">含税金额</p></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 55px;"><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 54px;"><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><br/></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: bottom; margin: 0px; font: inherit; width: 101.859px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: right;">合计：<br/></p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;"><br/></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 59px;"><td colspan="3" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: bottom; margin: 0px; font: inherit; width: 310.578px; text-align: right;">索赔总额：<br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;"><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 52px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供应商申诉情况</p></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">申诉内容</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;"><br/></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;"><br/></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 53px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><br/></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">申诉意见</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;"><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td rowspan="4" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;">申诉处理</td><td rowspan="3" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">处理动作</td><td colspan="5" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;">不改判</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">改判金额</td><td colspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 213.719px;">改判前金额：</td><td colspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 286.656px;">改判后金额：</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td class="" colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;">改判项目</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">决议说明</td><td class="" colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;"><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 58px;"><td colspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 172.766px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供应商确认：</p></td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class=""><p style="text-align: center;">盖公章<br/></p></td></tr></tbody></table></div><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><br/>\n\n</p>'

export const defaultTemp1 =
  '<div><div style="text-align: left;"><span style="font-size: 12pt;"><sup>${companyName}</sup></span><span style="font-size: 12pt;"><br/></span></div><div style="text-align: center;"><span style="font-size: 18pt;">索赔协议</span></div><p style="text-align: center;">索赔单号：${claimCode}日期：${printDate}<br/></p><div style="width: 100%;margin:0 auto;"><table class="e-rte-table" style=" background-color: transparent; margin: 0px auto; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; width: 100%; min-width: 0px;"><tbody style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 45px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供方</p></td><td colspan="4" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 417.438px;" class="">${supplierName}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><p style="text-align: center;">索赔类型</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class="">${claimTypeName}</td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 162px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">T<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">C<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">L<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">索<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">赔<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">原<br/></p><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">因</p></td><td colspan="6" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 704.094px;" class="">${reasonDesc!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td rowspan="3" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;">索赔</p><span style=" margin: 0px; padding: 0px; border: 0px; line-height: inherit; font-family: Roboto, &quot;Segoe UI&quot;, GeezaPro, &quot;DejaVu Serif&quot;, &quot;sans-serif&quot;, -apple-system, BlinkMacSystemFont; vertical-align: baseline; color: rgb(51, 51, 51);">项目</span></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">索赔项目描述</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><p style="text-align: center;">币种</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><p style="text-align: center;">税率</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">税额<span style=" margin: 0px; padding: 0px; border: 0px; line-height: inherit; font-family: Roboto, &quot;Segoe UI&quot;, GeezaPro, &quot;DejaVu Serif&quot;, &quot;sans-serif&quot;, -apple-system, BlinkMacSystemFont; vertical-align: baseline; color: rgb(51, 51, 51);"></span></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><p style="text-align: center;">不含税金额</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><p style="text-align: center;">含税金额</p></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 55px;"><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${[standDetailList.standDesc]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${currencyName}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${[standDetailList.taxRate]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${[standDetailList.taxAmount]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class="">${[standDetailList.untaxedPrice]}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class="">${[standDetailList.taxedPrice]!""}</td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 59px;"><td colspan="3" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: bottom; margin: 0px; font: inherit; width: 310.578px; text-align: right;" class="">索赔总额：<br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${claimTotalAmount}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 52px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供应商申诉情况</p></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">申诉内容</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${claimAppeal.appealContextName!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 53px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><br/></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">申诉意见</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${claimAppeal.appealSuggestion!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td rowspan="3" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;">申诉处理</td><td rowspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">处理动作</td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class="">${claimAppealDeal.appealDealName!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><br/></td><td colspan="2" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 213.719px;" class="">改判前金额：${preClaimTotalAmount!""}</td><td colspan="2" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 286.656px;" class="">改判后金额：</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">决议说明</td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class="">${claimAppealDeal.appealDealDesc!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 58px;"><td colspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 172.766px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供应商确认：</p></td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class=""><p style="text-align: center;">盖公章<br/></p></td></tr></tbody></table></div><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><br/></p></div>'

export const defaultTemp2 =
  '<div><div style="text-align: left;"><span style="font-size: 12pt;"><sup>${companyName}</sup></span><span style="font-size: 12pt;"><br/></span></div><div style="text-align: center;"><span style="font-size: 18pt;">索赔协议</span></div><p style="text-align: center;">索赔单号：${claimCode}日期：${printDate}<br/></p><div style="width: 100%;margin:0 auto;"><table class="e-rte-table" style=" background-color: transparent; margin: 0px auto; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; width: 100%; min-width: 0px;"><tbody style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 45px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供方</p></td><td colspan="4" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 417.438px;" class="">${supplierName}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><p style="text-align: center;">索赔类型</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class="">${claimTypeName}</td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 162px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">T<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">C<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">L<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">索<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">赔<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">原<br/></p><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">因</p></td><td colspan="6" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 704.094px;" class="">${reasonDesc!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td rowspan="3" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;">索赔</p><span style=" margin: 0px; padding: 0px; border: 0px; line-height: inherit; font-family: Roboto, &quot;Segoe UI&quot;, GeezaPro, &quot;DejaVu Serif&quot;, &quot;sans-serif&quot;, -apple-system, BlinkMacSystemFont; vertical-align: baseline; color: rgb(51, 51, 51);">项目</span></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">索赔项目描述</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><p style="text-align: center;">币种</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><p style="text-align: center;">税率</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">税额<span style=" margin: 0px; padding: 0px; border: 0px; line-height: inherit; font-family: Roboto, &quot;Segoe UI&quot;, GeezaPro, &quot;DejaVu Serif&quot;, &quot;sans-serif&quot;, -apple-system, BlinkMacSystemFont; vertical-align: baseline; color: rgb(51, 51, 51);"></span></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><p style="text-align: center;">不含税金额</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><p style="text-align: center;">含税金额</p></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 55px;"><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${[standDetailList.standDesc]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${currencyName!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${[standDetailList.taxRate]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${[standDetailList.taxAmount]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class="">${[standDetailList.untaxedPrice]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class="">${[standDetailList.taxedPrice]!""}</td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 59px;"><td colspan="3" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: bottom; margin: 0px; font: inherit; width: 310.578px; text-align: right;" class="">索赔总额：<br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${claimTotalAmount!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 52px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供应商申诉情况</p></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">申诉内容</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${claimAppeal.appealContextName!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;"><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 53px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><br/></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">申诉意见</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${claimAppeal.appealSuggestion!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td rowspan="3" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;">申诉处理</td><td rowspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">处理动作</td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class="">${claimAppealDeal.appealDealName!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><br/></td><td colspan="2" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 213.719px;" class="">改判前金额：${preClaimTotalAmount!""}</td><td colspan="2" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 286.656px;" class="">改判后金额：</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">决议说明</td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class="">${claimAppealDeal.appealDealDesc!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 58px;"><td colspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 172.766px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供应商确认：</p></td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class=""><p style="text-align: center;">盖公章<br/></p></td></tr></tbody></table></div><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><br/></p></div>'

export const defaultTemp3 =
  '<div><div style="text-align: left;"><span style="font-size: 12pt;"><sup>${companyName}</sup></span><span style="font-size: 12pt;"><br/></span></div><div style="text-align: center;"><span style="font-size: 18pt;">索赔协议</span></div><p style="text-align: center;">索赔单号：${claimCode}日期：${printDate}<br/></p><div style="width: 100%;margin:0 auto;"><table class="e-rte-table" style=" background-color: transparent; margin: 0px auto; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; width: 100%; min-width: 0px; border-collapse: collapse;"><tbody style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 45px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供方</p></td><td colspan="4" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 417.438px;" class="">${supplierName}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><p style="text-align: center;">索赔类型</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class="">${claimTypeName}</td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 162px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">T<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">C<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">L<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">索<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">赔<br/></p><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">原<br/></p><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">因</p></td><td colspan="6" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 704.094px;" class="">${reasonDesc!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td rowspan="3" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px 0px 10px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;">索赔</p><span style=" margin: 0px; padding: 0px; border: 0px; line-height: inherit; font-family: Roboto, &quot;Segoe UI&quot;, GeezaPro, &quot;DejaVu Serif&quot;, &quot;sans-serif&quot;, -apple-system, BlinkMacSystemFont; vertical-align: baseline; color: rgb(51, 51, 51);">项目</span></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">索赔项目描述</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><p style="text-align: center;">币种</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class=""><p style="text-align: center;">税率</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">税额<span style=" margin: 0px; padding: 0px; border: 0px; line-height: inherit; font-family: Roboto, &quot;Segoe UI&quot;, GeezaPro, &quot;DejaVu Serif&quot;, &quot;sans-serif&quot;, -apple-system, BlinkMacSystemFont; vertical-align: baseline; color: rgb(51, 51, 51);"></span></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><p style="text-align: center;">不含税金额</p></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><p style="text-align: center;">含税金额</p></td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 55px;"><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${[standDetailList.standDesc]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${currencyName!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${[standDetailList.taxRate]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${[standDetailList.taxAmount]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class="">${[standDetailList.untaxedPrice]!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class="">${[standDetailList.taxedPrice]!""}</td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 59px;"><td colspan="3" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: bottom; margin: 0px; font: inherit; width: 310.578px; text-align: right;" class="">索赔总额：<br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 106.859px;" class="">${claimTotalAmount!""}</td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 143.812px;" class=""><br/></td><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 142.844px;" class=""><br/></td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 52px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供应商申诉情况</p></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">申诉内容</td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${claimAppeal.appealContextName!""}</td></tr><tr style="margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 53px;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;"><br/></td><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">申诉意见</td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class="">${claimAppeal.appealSuggestion!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td rowspan="3" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 70.9062px;">申诉处理</td><td rowspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">处理动作</td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class="">${claimAppealDeal.appealDealName!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;" class=""><br/></td><td colspan="2" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 213.719px;" class="">改判前金额：${preClaimTotalAmount!""}</td><td colspan="2" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 286.656px;" class="">改判后金额：</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><td class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 101.859px;">决议说明</td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class="">${claimAppealDeal.appealDealDesc!""}</td></tr><tr style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; height: 58px;"><td colspan="2" class="" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 172.766px;"><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline; text-align: center;">供应商确认：</p></td><td colspan="5" style=" padding: 2px 5px; border: 1px solid rgb(189, 189, 189); height: 20px; min-width: 20px; vertical-align: middle; margin: 0px; font: inherit; width: 602.234px;" class=""><p style="text-align: center;">盖公章<br/></p></td></tr></tbody></table></div><p style=" margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;"><br/></p></div>'
