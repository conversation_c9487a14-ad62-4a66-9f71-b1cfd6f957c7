<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index'
// import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '98f7ae0a-1bc5-4199-92b6-1b373b323611',
          toolbar: [],
          useToolTemplate: false,
          grid: {
            columnData: columnData,
            frozenColumns: 3,
            asyncConfig: {
              url: '/masterDataManagement/common/user/listSupplierAccount'
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickCellTool(e) {
      console.log(e)
      let { data, tool } = e
      if (tool.id === 'edit') {
        this.handleClickCellToolEdit(data)
      } else if (tool.id === 'reset') {
        this.handleClickCellToolReset(data)
      }
    },
    handleClickCellToolEdit(data) {
      this.$dialog({
        modal: () => import('../components/editDialog.vue'),
        data: {
          title: this.$t('编辑'),
          data: data
        },
        success: () => {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleClickCellToolReset(data) {
      this.$dialog({
        modal: () => import('../components/resetDialog.vue'),
        data: {
          title: this.$t('重置密码'),
          data: data
        },
        success: () => {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
.dialog-content {
  margin-top: 20px;
}
</style>
