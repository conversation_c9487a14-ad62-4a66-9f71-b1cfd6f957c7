import { i18n } from '@/main.js'
import inputView from '../components/inputView.vue'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'employeeCode',
    headerText: i18n.t('登录账户')
  },
  {
    field: 'userCode',
    headerText: i18n.t('用户编码')
  },
  {
    field: 'employeeName',
    headerText: i18n.t('用户名称')
  },
  {
    field: 'tenantCode',
    headerText: i18n.t('租户编码')
  },
  {
    field: 'tenantName',
    headerText: i18n.t('租户名称')
  },
  {
    field: 'email',
    headerText: i18n.t('注册邮箱'),
    template: () => ({ template: inputView })
  },
  {
    field: 'telephone',
    headerText: i18n.t('注册手机号'),
    template: () => ({ template: inputView })
  },
  {
    field: 'operation',
    headerText: i18n.t('操作'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_solid_edit',
        title: i18n.t('编辑')
      },
      {
        id: 'reset',
        icon: 'icon_solid_edit',
        title: i18n.t('重置密码')
      }
    ],
    ignore: true //忽略
  }
]
// export const columnDataSupplier = [
//   {
//     width: '50',
//     type: 'checkbox'
//   },
//   {
//     field: 'employeeCode',
//     headerText: i18n.t('登录账户')
//   },
//   {
//     field: 'employeeName',
//     headerText: i18n.t('用户名称')
//   },
//   {
//     field: 'email',
//     headerText: i18n.t('注册邮箱')
//   },
//   {
//     field: 'telephone',
//     headerText: i18n.t('注册手机号')
//   },
//   {
//     field: '',
//     headerText: i18n.t('操作'),
//     cellTools: [
//       {
//         id: 'edit',
//         icon: 'icon_solid_edit',
//         title: i18n.t('编辑')
//       },
//       {
//         id: 'reset',
//         icon: 'icon_solid_edit',
//         title: i18n.t('重置密码')
//       }
//     ],
//     ignore: true //忽略
//   }
// ]
