<!-- 重置密码 -->
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('手机号')" label-style="top" prop="telephone">
          <mt-input
            id:input
            v-model="formInfo.telephone"
            :placeholder="$t('手机号')"
            float-label-type="Never"
            width="400"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('邮箱')" label-style="top" prop="email">
          <mt-input
            id:input
            v-model="formInfo.email"
            :placeholder="$t('邮箱')"
            float-label-type="Never"
            width="400"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    const validateTelephone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入手机号'))
      } else {
        let regexp = new RegExp(/^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/)
        if (regexp.test(value)) {
          callback()
        } else {
          callback(new Error('手机号码格式错误'))
        }
      }
    }
    const validateEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入邮箱'))
      } else {
        let regexp = new RegExp(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)
        if (regexp.test(value)) {
          callback()
        } else {
          callback(new Error('邮箱格式错误'))
        }
      }
    }
    return {
      formInfo: {
        telephone: '', // 密码
        email: ''
      },
      rules: {
        telephone: [
          {
            required: true,
            validator: validateTelephone,
            trigger: 'blur'
          }
        ],
        email: [
          {
            required: true,
            validator: validateEmail,
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      promptNumber: 0
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.data
    }
  },
  mounted() {
    this.formInfo = this.info
    if (this.info.email && this.info.encryptMap) {
      this.$API.accountManagement
        .checkDeliveryConfigInfo({ key: this.info.encryptMap?.email || '' })
        .then((res) => {
          if (res && res.code === 200) {
            this.info.email = res.data || ''
          }
        })
    }
    if (this.info.telephone && this.info.encryptMap) {
      this.$API.accountManagement
        .checkDeliveryConfigInfo({ key: this.info.encryptMap?.telephone || '' })
        .then((res) => {
          if (res && res.code === 200) {
            this.info.telephone = res.data || ''
          }
        })
    }
    this.show()
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          let params = {
            userId: this.info.userId,
            telephone: this.formInfo.telephone,
            email: this.formInfo.email
          }
          this.$API.accountManagement.updateInfo(params).then(() => {
            this.$emit('confirm-function')
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/.mt-input-number input {
  width: 100%;
}
</style>
