<!-- 编辑 -->
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('密码')" label-style="top" prop="password">
          <mt-input
            id:input
            v-model="formInfo.password"
            :placeholder="$t('密码')"
            float-label-type="Never"
            width="400"
            :maxlength="30"
          ></mt-input>
          <!--
             @input="input"
            @change="change"
           -->
        </mt-form-item>
        <div class="clickButton">
          <div class="add-custom-btn" @click="refresh">
            <i class="mt-icons mt-icon-icon_table_fresh"></i>
            <span>{{ $t('刷新') }}</span>
          </div>
          <div class="add-custom-btn" @click="copy">
            <i class="mt-icons mt-icon-icon_solid_Save1"></i>
            <span>{{ $t('复制') }}</span>
          </div>
        </div>
      </mt-form>
      <!-- <div class="prompt">
        <p>{{ '* 请输入密码' }}</p>
        <p>{{ '* 密码必须包含大小写字母,数字,和特殊符号,且长度不少于6位' }}</p>
      </div> -->
    </div>
  </mt-dialog>
</template>

<script>
// import { copyFile } from 'fs'

export default {
  data() {
    const validateEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('请输入密码或点击刷新自动生成')))
      } else {
        let regexp = new RegExp(
          /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*-+.?])[\da-zA-Z~!@#$%^&*-+.?]{6,}$/
        )
        if (regexp.test(value)) {
          callback()
        } else {
          callback(new Error(this.$t('密码必须包含大小写字母,数字,和特殊符号,且长度不少于6位')))
        }
      }
    }
    return {
      formInfo: {
        password: '' // 密码
      },
      rules: {
        password: [
          // {
          //   required: true,
          //   message: this.$t('请输入密码或点击刷新自动生成'),
          //   trigger: 'blur'
          // }
          {
            required: true,
            validator: validateEmail,
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      promptNumber: 0
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.data
    }
  },
  mounted() {
    this.show()
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    refresh() {
      this.formInfo.password = this.randomString()
    },
    copy() {
      if (!this.formInfo.password) {
        this.$toast({ content: this.$t('文本框为空'), type: 'warning' })
        return
      }
      // text是复制文本
      // 创建input元素
      const el = document.createElement('input')
      // 给input元素赋值需要复制的文本
      el.setAttribute('value', this.formInfo.password)
      // 将input元素插入页面
      document.body.appendChild(el)
      // 选中input元素的文本
      el.select()
      // 复制内容到剪贴板
      document.execCommand('copy')
      // 删除input元素
      document.body.removeChild(el)
    },
    // input(e) {
    //   this.regularCheck(e)
    // },
    // change(e) {
    //   this.regularCheck(e)
    // },
    // regularCheck(e) {
    //   // let check = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*-+.?])[\da-zA-Z~!@#$%^&*-+.?]{6,}$/
    //   // console.log(check.test(e), e)
    //   // if (!e) {
    //   //   this.rules.password[0].message = '请输入密码或点击刷新自动生成'
    //   //   alert('1')
    //   // } else if (!check.test(e)) {
    //   //   this.rules.password[0].required = true
    //   //   this.rules.password[0].message = '密码必须包含大小写字母,数字,和特殊符号,且长度不少于6位'
    //   //   alert('2')
    //   // } else {
    //   //   this.rules.password[0].required = false
    //   //   this.rules.password[0].message = ''
    //   //   alert('3')
    //   // }
    // },

    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          let params = { userId: this.info.userId, password: this.formInfo.password }
          this.$API.accountManagement.resetByAdmin(params).then(() => {
            this.$emit('confirm-function')
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    randomString(e) {
      e = e || 8
      let password = [],
        n = 0
      let passwordArr = [
        'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
        'abcdefghijklmnoprstuvwxyz',
        '*********',
        '!@#$&*^%'
      ]

      for (let i = 0; i < e; i++) {
        if (password.length < e - 4) {
          let arrayRandom = Math.floor(Math.random() * 4)
          let passwordItem = passwordArr[arrayRandom]
          let item = passwordItem[Math.floor(Math.random() * passwordItem.length)]
          password.push(item)
        } else {
          let newItem = passwordArr[n]
          let lastItem = newItem[Math.floor(Math.random() * newItem.length)]
          let spliceIndex = Math.floor(Math.random() * password.length)
          password.splice(spliceIndex, 0, lastItem)
          n++
        }
      }
      return password.join('')

      // for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      // return n
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/.mt-input-number input {
  width: 100%;
}
.clickButton {
  width: 400px;
  .add-custom-btn {
    border: 1px solid var(--plugin-ct-cell-icon-color);
    width: 65px;
    display: inline-block;
    padding: 4px 6px;
    border-radius: 5px;
    // background-color: var(--plugin-ct-cell-icon-color);
    color: var(--plugin-ct-cell-icon-color);
    /* vertical-align: middle; */
    margin: 15px;
  }
  .add-custom-btn:hover {
    cursor: pointer;
  }
  .add-custom-btn:active {
    color: #fff;
    background-color: var(--plugin-ct-cell-icon-color);
  }
  // .prompt {
  //   p {
  //     font-size: 12px;
  //     color: red;
  //   }
  // }
}
</style>
