<!-- 考核类型 -->
<template>
  <div class="full-height">
    <div class="hande">{{ $t('考核类型') }}</div>
    <!--
      rowSelecting 复选框事件
      changeRowdata 监听行数据变化
      rowclickOperation 行内操作按钮--删除
      clickCheckbox  自定义复选框
      topInfoPageConfig 头部引用模板选择
     -->
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      @rowSelected="rowSelected"
      :template-config="pageConfig"
      @changeRowdata="changeRowdata"
      @changeRowdataNumber="changeRowdataNumber"
      @topInfoPageConfig="topInfoPageConfig"
      @clickCheckbox="clickCheckbox"
      @rowclickOperation="rowclickOperation"
      @handleClickToolBar="handleClickToolBar"
      @rowSelecting="rowSelecting"
    ></mt-template-page>
  </div>
</template>

<script>
import { pageConfig1 } from './config/index'
import { cloneDeep } from 'lodash'
export default {
  props: {
    typeRectification: {
      type: Array,
      default: () => {
        return []
      }
    },
    assessmentType: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      pageConfig: pageConfig1(this.typeRectification),
      listDataSource: [],
      rowTypeId: 0
    }
  },
  mounted() {
    console.log(this.typeRectification)
    if (this.assessmentType instanceof Array && this.assessmentType.length > 0) {
      let _assessmentType = cloneDeep(this.assessmentType)
      _assessmentType.map((item) => {
        item.checkbox = false
        item.typeId = this.randomString(10)
        item.standIdList.map((val) => {
          val.standId = this.randomString(10)
        })
      })

      this.$set(this, 'listDataSource', _assessmentType)
      this.$set(this.pageConfig[0].grid, 'dataSource', _assessmentType)
      let params = {
        checkbox: true,
        data: _assessmentType[0]
      }
      this.$nextTick(() => {
        this.clickCheckbox(params)
      })
    }
    console.log(this.pageConfig[0].grid.dataSource)
  },
  methods: {
    handleClickToolBar(e) {
      let selectList = e.gridRef.getMtechGridRecords()
      if (selectList.length < 1 && (e.toolbar.id === 'delete1' || e.toolbar.id === 'delete2')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add1') {
        // console.log(this.$parent.organization)
        let organization = this.$parent.organization
        if (organization.length > 0) {
          this.handleClickToolBarAdd()
        } else {
          this.$toast({ content: this.$t('请选择头部组织'), type: 'warning' })
        }
      } else if (e.toolbar.id === 'delete1') {
        this.handleClickToolBarDelete(selectList)
      }
    },
    handleClickToolBarAdd() {
      let rowData = {
        checkbox: false, //自定义复选框
        typeName: '', // 类型名称,
        autoEnsure: '0', // 是否超期自动确认
        feedbackDays: '0', //默认反馈时长/天
        allowAppeal: '0', // 是否允许申诉
        appealDays: '0', // 申诉天数
        allowReverse: '0', // 是否允许冲销
        allowWithhold: '0', //是否允许预扣
        // remark: '', //申诉处理仲裁人
        standIdList: [], //考核指标
        typeId: this.randomString(10) //随机id
      }
      let flag = true
      let _dataSource = cloneDeep(this.listDataSource)
      _dataSource.map((item, index) => {
        if (!item.typeName) {
          this.$toast({ content: this.$t(`第${index + 1}行未填写类型名称`), type: 'warning' })
          flag = false
        }
        if (item.standIdList.length < 1) {
          this.$toast({ content: this.$t(`第${index + 1}行未选择考核指标`), type: 'warning' })
          flag = false
        }
      })
      if (!flag) return
      _dataSource.push(rowData)
      this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
      this.$set(this, 'listDataSource', _dataSource)
      this.$emit('assessTypeAdd', this.listDataSource)
      this.$refs.tepPage.getCurrentUsefulRef().ejsRef.selectRows([])
    },
    handleClickToolBarDelete(selectList) {
      let _dataSource = cloneDeep(this.pageConfig[0].grid.dataSource)
      _dataSource.map((item, index) => {
        if (item.typeId == selectList[0].typeId) {
          this.listDataSource.splice(index, 1)
          this.$set(this.pageConfig[0].grid, 'dataSource', this.listDataSource)
        }
      })
      this.$nextTick(() => {
        this.$set(this.$parent, 'assessmentType', this.listDataSource)
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        // console.log(this.$parent.assessmentType)
      })
    },
    changeRowdata(data) {
      let listDataSource = cloneDeep(this.listDataSource)
      listDataSource.map((item, index) => {
        if (item.typeId == data.data.typeId) {
          switch (data.text) {
            case 'typeName':
              item.typeName = data.itemData.itemName
              item.typeCode = data.itemData.itemCode
              if (data.e.e !== null) {
                let _filterArr = this.listDataSource.filter((item) => {
                  return item.typeId == data.data.typeId
                })
                if (_filterArr[0].checkbox) {
                  sessionStorage.setItem('assessTypeSelectStatus', JSON.stringify(item.typeCode))
                }
                item.standIdList = []
                this.$set(this.pageConfig[0].grid.dataSource[index], 'standIdList', [])
                this.$parent.$refs.assessIndicators.listDataSource = []
                this.$parent.$refs.assessIndicators.pageConfig[0].grid.dataSource = []
              }
              break
            // case 'remark':
            //   item.remark = data.itemData.employeeName
            //   item.arbitrator = data.itemData.employeeCode
            //   break
            case 'autoEnsure':
              item.autoEnsure = data.itemData.value
              break
            case 'allowAppeal':
              item.allowAppeal = data.itemData.value
              this.$bus.$emit('changeAllowAppeal', item)
              break
            case 'allowReverse':
              item.allowReverse = data.itemData.value
              break
            case 'allowWithhold':
              item.allowWithhold = data.itemData.value
              break
            case 'allowReverse1':
              item.allowReverse1 = data.itemData.value
              break
          }
          item.index = data.data.index
        }
      })

      this.$set(this, 'listDataSource', listDataSource)
      this.$set(this.$parent, 'assessmentType', listDataSource)
      console.log(this.$parent.assessmentType, this.listDataSource)
    },
    changeRowdataNumber(data) {
      let listDataSource = cloneDeep(this.listDataSource)
      listDataSource.map((item) => {
        if (item.typeId == data.data.typeId) {
          switch (data.text) {
            case 'feedbackDays':
              item.feedbackDays = data.value
              break
            case 'appealDays':
              item.appealDays = data.value
              break
          }
          item.index = data.data.index
        }
      })
      this.$set(this, 'listDataSource', listDataSource)
      this.$set(this.$parent, 'assessmentType', listDataSource)
      // console.log(this.$parent.assessmentType)
    },
    rowclickOperation(data) {
      let _dataSource = cloneDeep(this.pageConfig[0].grid.dataSource)
      _dataSource.map((item, index) => {
        if (item.typeId == data.typeId) {
          this.listDataSource.splice(index, 1)
          this.$set(this.pageConfig[0].grid, 'dataSource', this.listDataSource)
        }
      })
      this.$nextTick(() => {
        this.$set(this.$parent, 'assessmentType', this.listDataSource)
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    clickCheckbox(e) {
      this.listDataSource.map((item) => {
        if (item.typeId !== e.data.typeId) {
          item.checkbox = false
          console.log(item)
        } else {
          console.log(item)
          item.checkbox = e.checkbox

          if (!e.checkbox) {
            sessionStorage.setItem('assessTypeSelectStatus', JSON.stringify(''))
            this.$set(this.$parent.$refs.assessIndicators.pageConfig[0].grid, 'dataSource', [])
            return
          }
          this.$set(this.$parent.$refs.assessIndicators, 'listDataSource', e.data.standIdList)
          this.$set(
            this.$parent.$refs.assessIndicators.pageConfig[0].grid,
            'dataSource',
            e.data.standIdList
          )
          let _filterArr = this.listDataSource.filter((item) => {
            return item.typeId == e.data.typeId
          })
          sessionStorage.setItem('assessTypeSelectStatus', JSON.stringify(_filterArr[0].typeCode))
        }
      })
      console.log(this.listDataSource)
      this.$set(this.pageConfig[0].grid, 'dataSource', this.listDataSource)
    },
    rowSelected() {
      // let arr = []
      // this.listDataSource.map((item, index) => {
      //   arr.push(index)
      // })
      // let a = this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRows(arr)
      // let b = this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      // console.log(a, b)
    },
    rowSelecting() {
      // console.log(e)
      // if (e?.rowIndexes?.length < 1) return
      // if (e.data instanceof Array && e.data.length !== 1) return
      // console.log(e)
      // let currentSelect = [e.data]
      // let _index = ''
      // this.listDataSource.map((item, index) => {
      //   if (item.typeId == currentSelect[0].typeId) {
      //     _index = index
      //   }
      // })
      // let mapArr = [_index]
      // console.log(mapArr)
      // // this.rowTypeId = e.data.typeId
      // // let mapArr = [e.rowIndex]
      // // console.log(e, mapArr)
      // // e.rowIndexes = mapArr
      // if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length == 1) {
      //   this.$nextTick(() => {
      //     this.$refs.tepPage.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      //     let _assessIndicators = this.$parent.assessmentType.filter((item) => {
      //       return item.typeId == e.data.typeId
      //     })
      //     sessionStorage.setItem(
      //       'assessTypeSelectStatus',
      //       JSON.stringify(_assessIndicators[0].typeName)
      //     )
      //     console.log(this.listDataSource, this.pageConfig[0].grid.dataSource, _assessIndicators)
      //     this.$set(
      //       this.$parent.$refs.assessIndicators,
      //       'listDataSource',
      //       _assessIndicators[0].standIdList
      //     )
      //     this.$set(
      //       this.$parent.$refs.assessIndicators.pageConfig[0].grid,
      //       'dataSource',
      //       _assessIndicators[0].standIdList
      //     )
      //   })
      // }
    },
    topInfoPageConfig() {
      this.$set(this.$parent.$refs.assessType.pageConfig[0].grid, 'dataSource', this.listDataSource)
      console.log(this.listDataSource)

      this.$nextTick(() => {
        let params = {
          checkbox: true,
          data: this.listDataSource[0]
        }
        this.clickCheckbox(params)
      })
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnoprstuvwxyz123456789',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 50vh;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  /deep/.common-template-page {
    height: 100%;
    .mt-data-grid {
      height: 100%;
      .e-control {
        height: 100%;
        .e-content {
          height: 100%;
        }
      }
    }
  }
  /deep/.forbidCheck {
    background-color: #eee;
  }
  .hande {
    vertical-align: middle;
    display: inline-block;
  }
  .hande::before {
    content: '*';
    display: inline-block;
    vertical-align: middle;
    color: red;
  }
  .operation {
    display: flex;
    justify-content: space-around;
    align-items: center;
    span {
      color: #2783fe;
      text-decoration: underline;
    }
  }
}
</style>
