<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap"></div>
        <div class="btns-wrap">
          <mt-button
            class="e-flat"
            @click="$router.push('/supplier/assessment/template-setting')"
            >{{ $t('返回') }}</mt-button
          >
          <mt-button class="e-flat" @click="clickButtonSave">{{ $t('保存') }}</mt-button>
          <!-- <mt-button class="e-flat" @click="clickButtonSubmit">{{ $t('保存并提交') }}</mt-button> -->
        </div>
      </div>
      <div class="formInput">
        <mt-form ref="dialogRef" :model="formObject" :rules="rules">
          <mt-form-item prop="claimTemplateCode" :label="$t('模板编号')">
            <mt-input
              v-model="formObject.claimTemplateCode"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('模板编号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="claimTemplateName" :label="$t('模板名称')">
            <mt-input
              v-model="formObject.claimTemplateName"
              float-label-type="Never"
              :disabled="detailsObj != null && queryType === 'edit'"
              :placeholder="$t('模板名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="statusText" :label="$t('模板状态')">
            <mt-input
              v-model="statusText"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('模板状态')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="templateName" :label="$t('引用模板')">
            <mt-select
              v-model="formObject.templateCode"
              float-label-type="Never"
              :show-clear-button="true"
              :allow-filtering="true"
              :disabled="detailsObj !== null && queryType === 'edit'"
              :data-source="referenceTemplateArr"
              :fields="{ text: 'templateName', value: 'templateCode' }"
              @change="changeReferenceTemplate"
              :placeholder="$t('引用模板')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item class="applyOrganization" prop="applyOrganization" :label="$t('适用组织')">
            <mt-input
              v-model="formObject.applyOrganization"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('适用组织')"
            ></mt-input>
            <div class="hover" v-if="formObject.applyOrganization">
              <span
                style="margin: 0 5px; padding: 2px 0; box-sizing: border-box"
                v-for="(item, index) in formObject.rowOrganization"
                :key="item.id"
                >{{ index + 1 }}.{{ $t(item.orgName || item.companyName) }}</span
              >
            </div>
            <!-- <div><span v-for="(item) in formObject.rowOrganization" :key="item.nodeId">{{item.item}}</span></div> -->
            <mt-button
              :disabled="detailsObj !== null && queryType == 'edit' && detailsObj.status != '0'"
              @click="clickApply"
              >{{ $t('选择组织') }}</mt-button
            >
          </mt-form-item>
        </mt-form>
        <mt-form ref="dialogRef1" :model="formObject" :rules="rules">
          <mt-form-item prop="remark" :label="$t('模板描述')">
            <mt-input
              v-model="formObject.remark"
              float-label-type="Never"
              :placeholder="$t('模板描述')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </nav>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
// import { pageConfig } from '../../../supplierLifecycle/config'
export default {
  props: {
    detailsObj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    queryType: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      formObject: {
        claimTemplateCode: '', //模板编码
        claimTemplateName: '', //模板名称
        status: '0', //状态
        applyOrganization: '', //适用组织
        rowOrganization: [], //组织arr
        remark: '', //描述
        templateName: '', //引用模板
        templateCode: '' //引用模板
      },
      referenceTemplateArr: [], //引用模板
      rules: {
        claimTemplateName: [
          { required: true, message: this.$t('请输入模板名称'), trigger: 'blur' }
        ],
        applyOrganization: [{ required: true, message: this.$t('请选择适用组织'), trigger: 'blur' }]
        // remark: [{ required: true, message: this.$t('请输入模板描述'), trigger: 'blur' }]
      },
      userInfo: null,
      organization: []
    }
  },
  computed: {
    statusText() {
      return this.formObject.status == '-1'
        ? this.$t('已取消')
        : this.formObject.status == '0'
        ? this.$t('新建')
        : this.formObject.status == '1'
        ? this.$t('已提交')
        : this.formObject.status == '3'
        ? this.$t('审批拒绝')
        : this.formObject.status == '4'
        ? this.$t('生效')
        : this.formObject.status == '5'
        ? this.$t('失效')
        : this.formObject.status == '10'
        ? this.$t('待反馈')
        : this.$t('未匹配')
    }
  },
  mounted() {
    this.queryDetail()
    this.initialCall()
  },
  methods: {
    queryDetail() {
      if (this.detailsObj) {
        let { rangeList, status, templateCode, templateName, remark } = this.detailsObj
        let text = ''
        rangeList.map((item) => {
          text += `${item.companyName},`
        })
        this.formObject.applyOrganization = text
        this.formObject = {
          claimTemplateCode: templateCode, //模板编码
          claimTemplateName: templateName, //模板名称
          status: status, //状态
          applyOrganization: text, //适用组织
          rowOrganization: rangeList, //组织arr
          remark: remark, //描述
          templateName: '', //引用模板
          templateCode: '' //引用模板
        }
      }
    },
    async initialCall() {
      await this.$API.assessmentTemplateSetting.userinfo().then((res) => {
        this.userInfo = res.data
      })
      // console.log(this.userInfo)
      let params = {
        page: {
          current: 1,
          size: 9999
        },
        condition: 'and',
        defaultRules: [
          {
            field: 'status', // 模板状态
            type: 'Array',
            operator: 'in',
            value: ['4', '5']
          },
          {
            field: 'createUserId ', // 登录人
            type: 'string',
            operator: 'equal',
            value: this.userInfo.uid
          }
        ]
      }
      await this.$API.assessmentTemplateSetting.query().then((res) => {
        this.organization = res.data
      })
      await this.$API.assessmentTemplateSetting.queryIPage(params).then((res) => {
        this.referenceTemplateArr = res.data.records
      })
    },
    //change 引用模板
    async changeReferenceTemplate(e) {
      if (!e.value) {
        this.formObject = {
          claimTemplateCode: '', //模板编码
          claimTemplateName: '', //模板名称
          status: '0', //状态
          applyOrganization: '', //适用组织
          rowOrganization: [], //组织arr
          remark: '', //描述
          templateName: '', //引用模板
          templateCode: '' //引用模板
        }
        this.$emit('clickTopInfoApply', [])
        this.$set(this.$parent.$refs.assessType, 'listDataSource', [])
        this.$set(this.$parent.$refs.assessIndicators, 'listDataSource', [])
        this.$set(this.$parent.$refs.assessType.pageConfig[0].grid, 'dataSource', [])
        this.$set(this.$parent.$refs.assessIndicators.pageConfig[0].grid, 'dataSource', [])
        // this.$parent.$refs.assessType.topInfoPageConfig()
        this.$set(this.$parent, 'assessmentType', [])
        return
      }
      await this.$API.assessmentTemplateSetting.queryDetail(e.itemData.id).then((res) => {
        let _detailsObj = cloneDeep(res.data)
        _detailsObj.claimTypeList.map((item) => {
          item.standIdList = item.claimStandList
          item.standIdList.map((val) => {
            val.claimCalcTypeStr = val.claimStandCalcRule?.ruleType
            val.defaultValue = val.claimStandCalcRule?.defaultValue
            val.maxValue = val.claimStandCalcRule?.maxValue
            val.minValue = val.claimStandCalcRule?.minValue
            val.standId = this.randomString(10)
          })
          item.checkbox = false
          // item.standIdList.map((val) => {
          // })
          item.typeId = this.randomString(10)
          delete item.claimStandList
          delete item.id
        })

        console.log(_detailsObj)
        let { rangeList } = _detailsObj
        let text = ''
        rangeList.map((item) => {
          text += `${item.companyName},`
        })
        this.formObject.applyOrganization = text
        this.formObject.rowOrganization = rangeList //组织arr
        this.$emit('clickTopInfoApply', rangeList)

        this.$set(this.$parent.$refs.assessType, 'listDataSource', _detailsObj.claimTypeList)
        this.$parent.$refs.assessType.topInfoPageConfig()
        this.$set(this.$parent, 'assessmentType', _detailsObj.claimTypeList)
      })
    },
    //保存
    clickButtonSave() {
      this.$emit('topSubmit', '0')
    },
    //提交
    // clickButtonSubmit() {
    //   this.$emit('topSubmit', '1')
    // },
    //选择组织
    clickApply() {
      this.$dialog({
        modal: () => import('./components/organization.vue'),
        data: {
          title: this.$t('选择组织'),
          organization: this.organization
        },
        success: (val) => {
          let text = '',
            _val = []
          val.map((item) => {
            if (item.orgType == 'company') {
              text += `${item.orgName},`
              _val.push(item)
            }
          })
          this.formObject.applyOrganization = text
          this.formObject.rowOrganization = _val
          this.$emit('clickTopInfoApply', _val)
        }
      })
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnoprstuvwxyz123456789',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    }
    // //OA审批记录
    // clickButtonOA() {
    //   window.location.href = this.formObject.oaUrl
    // }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  nav {
    //按钮
    .detail-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      .mt-form {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
        .mt-form-item:nth-last-child(1) {
          width: 405px;
          .mt-input {
            display: inline-block;
            width: 320px;
          }
          /deep/.e-btn {
            padding: 6px 8px 4px;
            margin-left: 10px;
          }
        }
        &::after {
          content: '';
          width: 400px;
        }
      }
      .mt-form:nth-of-type(2) {
        width: 100%;
        .mt-form-item {
          width: 100%;
          .mt-input {
            width: 100%;
          }
        }
      }
      .applyOrganization {
        position: relative;
        .mt-input:hover + .hover {
          display: block;
        }
      }
      .hover {
        display: none;
        position: absolute;
        top: -110px;
        left: 0;
        z-index: 1;
        width: 400px;
        height: 100px;
        background: #fff;
        border: 1px solid #fee;
        border-radius: 5px;
      }
    }
  }
}
</style>
