import { i18n } from '@/main.js'
import Vue from 'vue'
import { cloneDeep } from 'lodash'
const toolbar1 = [
  { id: 'Add1', icon: 'icon_solid_Createorder', title: i18n.t('新增') }
  // { id: 'delete1', icon: 'icon_solid_Delete1', title: i18n.t('删除') }
]
const toolbar2 = [
  { id: 'Add2', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete2', icon: 'icon_solid_Delete1', title: i18n.t('删除') }
]

export const pageConfig1 = (typeRectification) => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [toolbar1, []]
    },
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      allowPaging: false,
      columnData: [
        {
          width: '50',
          // field: '',
          // type: 'checkbox'
          field: 'checkbox',
          headerText: i18n.t(''),
          headerTemplate: () => {
            return {
              template: Vue.component('headers', {
                template: '<div></div>'
              })
            }
          },
          template: () => {
            return {
              template: Vue.component('checkbox', {
                template: `<div>
                <mt-checkbox
                v-model="data.checkbox"
                @change="certificateNochange"
              ></mt-checkbox>
                </div>`,
                data: function () {
                  return {
                    // checkboxVal: this.data.checkbox
                  }
                },
                methods: {
                  certificateNochange(val) {
                    this.data.checkbox = val.checked
                    let params = {
                      checkbox: val.checked,
                      data: this.data
                    }
                    this.$parent.$emit('clickCheckbox', params)
                    console.log(params)
                  }
                }
              })
            }
          }
          // template: () => {
          //   return {
          //     template: Vue.component('checkbox', {
          //       template: `<div><mt-checkbox
          //         v-model="checkboxVal"
          //         @click="checkboxVal = !checkboxVal"
          //       ></mt-checkbox></div>`,
          //       data: function () {
          //         return {
          //           checkboxVal: false
          //         }
          //       },
          //       methods: {}
          //     })
          //   }
          // }
        },
        {
          field: 'typeName',
          headerText: i18n.t('类型名称'),
          template: () => {
            return {
              template: Vue.component('typeName', {
                template: `<div>
                <mt-select
                v-model="data.typeCode"
                float-label-type="Never"
                :data-source="typeNameArr"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                @change="changeReferenceTemplate"
                :placeholder="$t('请选择类型名称')"
              ></mt-select>
                </div>`,
                data: function () {
                  return {
                    typeNameArr: []
                  }
                },
                mounted() {
                  this.$set(this, 'typeNameArr', typeRectification)
                  console.log(this.typeNameArr)
                },
                methods: {
                  changeReferenceTemplate(e) {
                    // console.log(e)
                    // :open-dispatch-change="false"
                    // :open-dispatch-change="false" //初始进来不触发change事件
                    // if (e.e !== null) {
                    let params = {
                      data: this.data,
                      text: 'typeName',
                      itemData: e.itemData,
                      e: e
                    }
                    this.$parent.$emit('changeRowdata', params)
                    // }
                  }
                }
              })
            }
          }
        },
        {
          width: 150,
          field: 'autoEnsure',
          headerText: i18n.t('是否超期自动确认'),
          template: () => {
            return {
              template: Vue.component('autoEnsure', {
                template: `<div>
                <mt-select
                v-model="data.autoEnsure"
                float-label-type="Never"
                :data-source="autoEnsureArr"
                @change="changeautoEnsure"
                :placeholder="$t('是否超期自动确认')"
              ></mt-select>
                </div>`,
                data: function () {
                  return {
                    autoEnsureArr: [
                      { text: i18n.t('是'), value: '1' },
                      { text: i18n.t('否'), value: '0' }
                    ]
                  }
                },
                methods: {
                  changeautoEnsure(e) {
                    let params = {
                      data: this.data,
                      text: 'autoEnsure',
                      itemData: e.itemData
                    }
                    this.$parent.$emit('changeRowdata', params)
                  }
                }
              })
            }
          }
        },
        {
          width: 150,
          field: 'feedbackDays',
          headerText: i18n.t('默认反馈时长/天'),
          template: () => {
            return {
              template: Vue.component('feedbackDays', {
                template: `<div>
                <mt-input
                v-model="data.feedbackDays"
                :min="0"
                type="number"
                @change="changenumber"
              ></mt-input>
                </div>`,
                data: function () {
                  return {}
                },
                methods: {
                  changenumber(e) {
                    let params = {
                      data: this.data,
                      text: 'feedbackDays',
                      value: e
                    }
                    this.$parent.$emit('changeRowdataNumber', params)
                  }
                }
              })
            }
          }
        },
        {
          width: 150,
          field: 'allowAppeal',
          headerText: i18n.t('是否允许申诉'),
          template: () => {
            return {
              template: Vue.component('allowAppeal', {
                template: `<div>
                <mt-select
                v-model="data.allowAppeal"
                float-label-type="Never"
                :data-source="allowAppealArr"
                @change="changallowAppeal"
                :placeholder="$t('是否允许申诉')"
              ></mt-select>
                </div>`,
                data: function () {
                  return {
                    allowAppealArr: [
                      { text: i18n.t('是'), value: '1' },
                      { text: i18n.t('否'), value: '0' }
                    ]
                  }
                },
                methods: {
                  changallowAppeal(e) {
                    let params = {
                      data: this.data,
                      text: 'allowAppeal',
                      itemData: e.itemData
                    }
                    this.$parent.$emit('changeRowdata', params)
                  }
                }
              })
            }
          }
        },
        {
          width: 150,
          field: 'appealDays',
          headerText: i18n.t('申诉处理时长/天'),
          template: () => {
            return {
              template: Vue.component('appealDays', {
                template: `<div>
                <mt-input
                v-model="data.appealDays"
                type="number"
                :min="0"
                :disabled='disabled'
                @change="changenumber"
              ></mt-input>
                </div>`,
                data: function () {
                  return {
                    disabled: ''
                  }
                },
                mounted() {
                  if (this.data.allowAppeal == '0') {
                    this.disabled = true
                  } else if (this.data.allowAppeal == '1') {
                    this.disabled = false
                  }
                  this.$bus.$on('changeAllowAppeal', (item) => {
                    if (item.allowAppeal == '0') {
                      this.data.appealDays = 0
                      this.disabled = true
                    } else if (item.allowAppeal == '1') {
                      this.disabled = false
                    }
                  })
                },
                methods: {
                  changenumber(e) {
                    let params = {
                      data: this.data,
                      text: 'appealDays',
                      value: e
                    }
                    this.$parent.$emit('changeRowdataNumber', params)
                  }
                }
              })
            }
          }
        },
        {
          width: 150,
          field: 'allowReverse',
          headerText: i18n.t('是否允许冲销'),
          template: () => {
            return {
              template: Vue.component('allowReverse', {
                template: `<div>
                <mt-select
                v-model="data.allowReverse"
                float-label-type="Never"
                :data-source="allowReverseArr"
                @change="changallowReverse"
                :placeholder="$t('是否允许冲销')"
              ></mt-select>
                </div>`,
                data: function () {
                  return {
                    allowReverseArr: [
                      { text: i18n.t('是'), value: '1' },
                      { text: i18n.t('否'), value: '0' }
                    ]
                  }
                },
                methods: {
                  changallowReverse(e) {
                    let params = {
                      data: this.data,
                      text: 'allowReverse',
                      itemData: e.itemData
                    }
                    this.$parent.$emit('changeRowdata', params)
                  }
                }
              })
            }
          }
        },
        {
          width: 150,
          field: 'allowWithhold',
          headerText: i18n.t('是否允许预扣'),
          template: () => {
            return {
              template: Vue.component('allowWithhold', {
                template: `<div>
                <mt-select
                v-model="data.allowWithhold"
                float-label-type="Never"
                :data-source="allowWithholdArr"
                @change="changallowWithhold"
                :placeholder="$t('是否允许冲销')"
              ></mt-select>
                </div>`,
                data: function () {
                  return {
                    allowWithholdArr: [
                      { text: i18n.t('是'), value: '1' },
                      { text: i18n.t('否'), value: '0' }
                    ]
                  }
                },
                methods: {
                  changallowWithhold(e) {
                    let params = {
                      data: this.data,
                      text: 'allowWithhold',
                      itemData: e.itemData
                    }
                    this.$parent.$emit('changeRowdata', params)
                  }
                }
              })
            }
          }
        },
        // {
        //   width: 200,
        //   field: 'remark',
        //   headerText: i18n.t('申诉处理仲裁人'),
        //   template: () => {
        //     return {
        //       template: Vue.component('remark', {
        //         template: `<div>
        //         <mt-select
        //         v-model="data.arbitrator"
        //         float-label-type="Never"
        //         :allow-filtering="true"
        //         :data-source="employeeArr"
        //         :fields="{ text: 'employeeName', value: 'employeeCode' }"
        //         :filtering="inputPersonnelitem"
        //         @change="changeEmployee"
        //         :placeholder="$t('请选择申诉处理仲裁人')"
        //       ></mt-select>
        //         </div>`,
        //         data: function () {
        //           return {
        //             employeeArr: []
        //           }
        //         },
        //         mounted() {
        //           this.inputPersonnelitem = utils.debounce(this.inputPersonnelitem, 500)
        //           this.f()
        //         },
        //         methods: {
        //           // remark  名字
        //           // arbitrator  Code
        //           f() {
        //             let value = ''
        //             if (this.data.remark && this.data.remark.indexOf('-') != -1) {
        //               value = this.data.remark.split('-')[0]
        //             } else if (this.data.remark) {
        //               value = this.data.remark
        //             }
        //             let params = {
        //               fuzzyName: value
        //             }
        //             this.$API.assessmentTemplateSetting
        //               .currentTenantEmployees(params)
        //               .then((res) => {
        //                 let data = cloneDeep(res.data)
        //                 data.map((item) => {
        //                   item.employeeName = item.employeeName + '-' + item.employeeCode
        //                 })
        //                 this.$set(this, 'employeeArr', data)
        //               })
        //           },
        //           inputPersonnelitem(e) {
        //             let params = {
        //               fuzzyName: e.text
        //             }
        //             this.$API.assessmentTemplateSetting
        //               .currentTenantEmployees(params)
        //               .then((res) => {
        //                 let data = cloneDeep(res.data)
        //                 data.map((item) => {
        //                   item.employeeName = item.employeeName + '-' + item.employeeCode
        //                 })
        //                 this.$set(this, 'employeeArr', data)
        //               })
        //           },
        //           changeEmployee(e) {
        //             let params = {
        //               data: this.data,
        //               text: 'remark',
        //               itemData: e.itemData
        //             }
        //             this.$parent.$emit('changeRowdata', params)
        //           }
        //         }
        //       })
        //     }
        //   }
        // },
        {
          width: 150,
          field: 'allowReverse',
          headerText: i18n.t('仲裁结果供方确认'),
          template: () => {
            return {
              template: Vue.component('allowReverse', {
                template: `<div>
                <mt-select
                v-model="data.allowReverse"
                float-label-type="Never"
                :data-source="allowReverseArr"
                @change="changallowReverse"
                :placeholder="$t('是否允许冲销')"
              ></mt-select>
                </div>`,
                data: function () {
                  return {
                    allowReverseArr: [
                      { text: i18n.t('是'), value: '1' },
                      { text: i18n.t('否'), value: '0' }
                    ]
                  }
                },
                methods: {
                  changallowReverse(e) {
                    let params = {
                      data: this.data,
                      texe: 'allowReverse',
                      itemData: e.itemData
                    }
                    this.$parent.$emit('changeRowdata', params)
                  }
                }
              })
            }
          }
        },
        {
          width: 200,
          field: 'operation',
          headerText: i18n.t('操作'),
          template: () => {
            return {
              template: Vue.component('operation', {
                template: `<div class="operation">
                <span @click="clickOperation">{{ $t('删除') }}</span></div>`,
                data: function () {
                  return {}
                },
                methods: {
                  clickOperation() {
                    this.$parent.$emit('rowclickOperation', this.data)
                  }
                }
              })
            }
          }
        }
      ],
      // asyncConfig: {
      //   url: '/supplier/tenant/claim/template/query'
      // }
      rowDataBound: (args) => {
        if (args.data.checkbox) {
          args.row.classList.add('forbidCheck')
        }
      },
      dataSource: []
    }
  }
]
export const pageConfig2 = () => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [toolbar2, []]
    },
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'standName',
          headerText: i18n.t('指标名称'),
          template: () => {
            return {
              template: Vue.component('standName', {
                template: `<div>
                <mt-select
                v-model="data.standCode"
                float-label-type="Never"
                :data-source="standArr"
                :fields="{ text: 'standName', value: 'standCode' }"
                @change="changestand"
                :placeholder="$t('请选择类型名称')"
              ></mt-select>
                </div>`,
                data: function () {
                  return {
                    standArr: []
                  }
                },
                mounted() {
                  this.f()
                },
                methods: {
                  f() {
                    let assessTypeSelectStatus = JSON.parse(
                      sessionStorage.getItem('assessTypeSelectStatus')
                    )
                    let params = {
                      typeCode: assessTypeSelectStatus
                      // page: {
                      //   current: 1,
                      //   size: 20
                      // },
                      // condition: 'and',
                      // defaultRules: [
                      //   {
                      //     field: 'claimTypeName',
                      //     label: i18n.t('考核类型'),
                      //     operator: 'contains',
                      //     type: 'string',
                      //     value: assessTypeSelectStatus
                      //   }
                      // ]
                    }
                    console.log(params)
                    this.$API.assessmentTemplateSetting.queryStandByType(params).then((res) => {
                      let _data = cloneDeep(res.data)
                      console.log(_data)
                      _data.map((item) => {
                        item.standName = item.standName + '-' + item.standCode
                      })
                      this.$set(this, 'standArr', _data)
                    })
                  },
                  changestand(e) {
                    if (
                      e.itemData.standName != this.data.standName &&
                      this.data.standCode != e.itemData.standCode
                    ) {
                      this.data.standName = e.itemData.standName
                      this.data.standCode = e.itemData.standCode
                      let params = {
                        data: this.data,
                        text: 'standName',
                        itemData: e.itemData
                      }
                      this.$parent.$emit('changeRowdata', params)
                    }
                  }
                }
              })
            }
          }
        },
        {
          field: 'standDesc',
          headerText: i18n.t('指标描述')
          // template: () => {
          //   return {
          //     template: Vue.component('standDesc', {
          //       template: `<div>
          //       <mt-input
          //       v-model="data.standDesc"
          //     ></mt-input>
          //       </div>`,
          //       data: function () {
          //         return {
          //           data: {}
          //         }
          //       },
          //       methods: {}
          //     })
          //   }
          // }
        },
        {
          field: 'claimCalcTypeStr',
          headerText: i18n.t('取值方式')
          // template: () => {
          //   return {
          //     template: Vue.component('claimCalcTypeStr', {
          //       template: `<div>
          //       <mt-select
          //       v-model="data.claimCalcTypeStr"
          //       float-label-type="Never"
          //       :data-source="claimCalcTypeStrArr"
          //       @change="changeClaimCalcTypeStr"
          //       :placeholder="$t('请选择类型名称')"
          //     ></mt-select>
          //       </div>`,
          //       data: function () {
          //         return {
          //           data: {
          //             claimCalcTypeStrArr: []
          //           }
          //         }
          //       },
          //       methods: {
          //         changeClaimCalcTypeStr() {}
          //       }
          //     })
          //   }
          // }
        },
        {
          field: 'defaultValue',
          headerText: i18n.t('默认考核金额')
          // template: () => {
          //   return {
          //     template: Vue.component('defaultValue', {
          //       template: `<div>
          //       <mt-input
          //       v-model="data.defaultValue"
          //       type="number"
          //     ></mt-input>
          //       </div>`,
          //       data: function () {
          //         return {
          //           data: {}
          //         }
          //       },
          //       methods: {}
          //     })
          //   }
          // }
        },
        {
          field: 'maxValue',
          headerText: i18n.t('上限金额')
          // template: () => {
          //   return {
          //     template: Vue.component('maxValue', {
          //       template: `<div>
          //       <mt-input
          //       v-model="data.maxValue"
          //       type="number"
          //     ></mt-input>
          //       </div>`,
          //       data: function () {
          //         return {
          //           data: {}
          //         }
          //       },
          //       methods: {}
          //     })
          //   }
          // }
        },
        {
          field: 'minValue',
          headerText: i18n.t('下限金额')
          // template: () => {
          //   return {
          //     template: Vue.component('minValue', {
          //       template: `<div>
          //       <mt-input
          //       v-model="data.minValue"
          //       type="number"
          //     ></mt-input>
          //       </div>`,
          //       data: function () {
          //         return {
          //           data: {}
          //         }
          //       },
          //       methods: {}
          //     })
          //   }
          // }
        },
        {
          field: 'operation',
          headerText: i18n.t('操作'),
          template: () => {
            return {
              template: Vue.component('operation', {
                template: `<div class="operation">
                <span @click="clickDelete">{{ $t('删除') }}</span></div>`,
                data: function () {
                  return {
                    data: {}
                  }
                },
                methods: {
                  clickDelete() {
                    this.$parent.$emit('rowclickDelete', this.data)
                  }
                }
              })
            }
          }
        }
      ],
      // asyncConfig: {
      //   url: '/supplier/tenant/claim/template/query'
      // }
      dataSource: []
    }
  }
]
