<!-- 考核指标 -->
<template>
  <div class="full-height">
    <div class="hande">{{ $t('考核指标') }}</div>
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @changeRowdata="changeRowdata"
      @rowclickDelete="rowclickDelete"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { pageConfig2 } from './config/index'
import { cloneDeep } from 'lodash'

export default {
  // props: {
  //   assessIndicatorsStandName: {
  //     type: Array,
  //     default: () => {
  //       return []
  //     }
  //   }
  // },
  data() {
    return {
      pageConfig: pageConfig2(),
      listDataSource: []
    }
  },
  computed: {},
  mounted() {
    // console.log(this.$parent)
  },
  methods: {
    handleClickToolBar(e) {
      let selectList = e.gridRef.getMtechGridRecords()
      if (selectList.length < 1 && e.toolbar.id === 'delete2') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add2') {
        let typelist = cloneDeep(this.$parent.$refs.assessType.listDataSource)
        if (typelist.length == 0) {
          this.$toast({ content: this.$t('请添加一条考核类型'), type: 'warning' })
          return
        }
        let filterlist = typelist.filter((item) => {
          return item.checkbox == true
        })

        if (filterlist.length < 1) {
          this.$toast({ content: this.$t('请勾选一条考核类型'), type: 'warning' })
          return
        }
        if (!filterlist[0].typeName) {
          this.$toast({ content: this.$t('勾选数据考核类型不能为空'), type: 'warning' })
          return
        }
        let flag = true
        let _dataSource = cloneDeep(this.listDataSource)
        _dataSource.map((item, index) => {
          if (!item.standName) {
            this.$toast({ content: this.$t(`第${index + 1}行未填写指标名称`), type: 'warning' })
            flag = false
          }
        })
        if (!flag) return
        this.handleClickToolBarAdd(filterlist)
      } else if (e.toolbar.id === 'delete2') {
        this.handleClickToolBarDelete(selectList)
      }
    },
    handleClickToolBarAdd(filterlist) {
      let rowData = {
        standName: '', // 指标名称,
        standDesc: '', // 指标描述
        claimCalcTypeStr: '', //取值方式
        defaultValue: '0', // 默认考核金额
        maxValue: '0', // 上限金额
        minValue: '0', // 下限金额
        standId: this.randomString(10) //随机id
      }
      let _dataSource = cloneDeep(this.listDataSource)
      _dataSource.push(rowData)
      this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
      this.$set(this, 'listDataSource', _dataSource)
      let typeListDataSource = cloneDeep(this.$parent.$refs.assessType.listDataSource)
      typeListDataSource.map((item) => {
        if (item.typeId == filterlist[0].typeId) {
          item.standIdList.push(rowData)
        }
      })
      this.$set(this.$parent.$refs.assessType, 'listDataSource', typeListDataSource)
      this.$set(this.$parent, 'assessmentType', typeListDataSource)
      console.log(this.$parent.$refs.assessType.listDataSource, this.$parent.assessmentType)
    },
    handleClickToolBarDelete(selectList) {
      let ids = selectList.map((item) => item.standId)
      let _listDataSource = cloneDeep(this.listDataSource)
      _listDataSource = _listDataSource.filter((v) => !ids.includes(v.standId))
      this.$set(this.pageConfig[0].grid, 'dataSource', _listDataSource)
      this.$set(this, 'listDataSource', _listDataSource)

      this.$nextTick(() => {
        let typelist = cloneDeep(this.$parent.$refs.assessType.listDataSource)
        let filterlist = typelist.filter((item) => {
          return item.checkbox == true
        })
        typelist.map((item) => {
          if (item.typeId == filterlist[0].typeId) {
            this.$set(item, 'standIdList', this.listDataSource)
          }
        })
        this.$set(this.$parent.$refs.assessType, 'listDataSource', typelist)
        this.$set(this.$parent, 'assessmentType', typelist)
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        // console.log(
        //   this.$parent.$refs.assessType.listDataSource,
        //   this.$parent.assessmentType,
        //   this.listDataSource,
        //   this.pageConfig[0].grid.dataSource
        // )
      })
    },
    rowclickDelete(data) {
      let _dataSource = cloneDeep(this.listDataSource)
      _dataSource = _dataSource.filter((item) => {
        return item.standId != data.standId
      })
      this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
      this.$set(this, 'listDataSource', _dataSource)

      this.$nextTick(() => {
        let typelist = cloneDeep(this.$parent.$refs.assessType.listDataSource)
        let filterlist = typelist.filter((item) => {
          return item.checkbox == true
        })
        typelist.map((item) => {
          if (item.typeId == filterlist[0].typeId) {
            this.$set(item, 'standIdList', this.listDataSource)
          }
        })
        this.$set(this.$parent.$refs.assessType, 'listDataSource', typelist)
        this.$set(this.$parent, 'assessmentType', typelist)
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        // console.log(
        //   this.$parent.$refs.assessType.listDataSource,
        //   this.$parent.assessmentType,
        //   this.listDataSource,
        //   this.pageConfig[0].grid.dataSource
        // )
      })
    },
    changeRowdata(data) {
      let listDataSource = cloneDeep(this.listDataSource)
      listDataSource.map((item) => {
        if (item.standId == data.data.standId) {
          switch (data.text) {
            case 'standName':
              item.standName = data.itemData.standName
              item.standCode = data.itemData.standCode
              item.id = data.itemData.id
              item.standDesc = data.itemData.remark
              item.claimCalcTypeStr = data.itemData?.claimStandCalcRule?.ruleType
                ? data.itemData?.claimStandCalcRule?.ruleType
                : '' //取值方式  0 规则  1公式 2手动填写
              item.defaultValue = data.itemData?.claimStandCalcRule?.defaultValue
                ? data.itemData?.claimStandCalcRule?.defaultValue
                : '' //默认考核金额
              item.maxValue = data.itemData?.claimStandCalcRule?.maxValue
                ? data.itemData?.claimStandCalcRule?.maxValue
                : ''
              item.minValue = data.itemData?.claimStandCalcRule?.minValue
                ? data.itemData?.claimStandCalcRule?.minValue
                : ''
              break
          }
          item.index = data.data.index
        }
      })

      this.$set(this, 'listDataSource', listDataSource)
      this.$set(this.pageConfig[0].grid, 'dataSource', listDataSource)

      this.$nextTick(() => {
        let typelist = cloneDeep(this.$parent.$refs.assessType.listDataSource)
        let filterlist = typelist.filter((item) => {
          return item.checkbox == true
        })
        typelist.map((item) => {
          if (item.typeId == filterlist[0].typeId) {
            this.$set(item, 'standIdList', this.listDataSource)
          }
        })
        this.$set(this.$parent.$refs.assessType, 'listDataSource', typelist)
        this.$set(this.$parent, 'assessmentType', typelist)
        // this.$toast({ content: this.$t('操作成功'), type: 'success' })
        console.log(
          this.$parent.$refs.assessType.listDataSource,
          this.$parent.assessmentType,
          this.listDataSource,
          this.pageConfig[0].grid.dataSource
        )
      })
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnoprstuvwxyz123456789',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 50vh;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  .hande {
    vertical-align: middle;
    display: inline-block;
  }
  .hande::before {
    content: '*';
    display: inline-block;
    vertical-align: middle;
    color: red;
  }
  .operation {
    display: flex;
    justify-content: space-around;
    align-items: center;
    span {
      color: #2783fe;
      text-decoration: underline;
    }
  }
}
</style>
