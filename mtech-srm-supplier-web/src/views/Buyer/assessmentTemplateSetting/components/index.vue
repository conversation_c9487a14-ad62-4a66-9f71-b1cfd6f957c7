<template>
  <div class="inventory">
    <!-- 头部 -->
    <top-info
      ref="topInfo"
      v-if="
        queryType === 'Add' ||
        (queryType === 'edit' && detailsObj) ||
        (queryType === 'copy' && detailsObj)
      "
      :details-obj="detailsObj"
      :query-type="queryType"
      @topSubmit="topSubmit"
      @clickTopInfoApply="clickTopInfoApply"
    ></top-info>
    <!-- 考核类型 -->
    <assessType
      ref="assessType"
      v-if="
        (queryType === 'Add' && typeRectification.length > 0) ||
        (queryType === 'edit' && detailsObj !== null && typeRectification.length > 0) ||
        (queryType === 'copy' && detailsObj !== null && typeRectification.length > 0)
      "
      :type-rectification="typeRectification"
      :assessment-type="assessmentType"
      @assessTypeAdd="assessTypeAdd"
    >
    </assessType>
    <!-- 考核指标 -->
    <assessIndicators
      ref="assessIndicators"
      v-if="
        queryType === 'Add' ||
        (queryType === 'edit' && detailsObj) ||
        (queryType === 'copy' && detailsObj)
      "
    >
    </assessIndicators>
  </div>
</template>
<script>
import topInfo from './page/topInfo.vue'
import assessType from './page/assessType.vue'
import assessIndicators from './page/assessIndicators.vue'
import { cloneDeep } from 'lodash'
export default {
  components: {
    topInfo,
    assessType,
    assessIndicators
  },
  data() {
    return {
      detailsObj: null,
      typeRectification: [], //类型整改
      organization: [], //选择组织
      assessmentType: [] //考核类型
    }
  },
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    },
    queryData() {
      return this.$route.query.data
    }
  },
  async mounted() {
    if (this.queryId) {
      await this.queryDetail(this.queryId)
      await this.assessTypeName()
    } else {
      await this.assessTypeName()
    }
  },
  methods: {
    queryDetail(queryId) {
      this.$API.assessmentTemplateSetting.queryDetail(queryId).then((res) => {
        this.detailsObj = cloneDeep(res.data)
        this.detailsObj.claimTypeList.map((item) => {
          item.standIdList = item.claimStandList
          item.standIdList.map((val) => {
            val.claimCalcTypeStr = val.claimStandCalcRule?.ruleType
            val.defaultValue = val.claimStandCalcRule?.defaultValue
            val.maxValue = val.claimStandCalcRule?.maxValue
            val.minValue = val.claimStandCalcRule?.minValue
          })

          delete item.claimStandList
        })
        this.assessmentType = this.detailsObj.claimTypeList
        this.organization = this.detailsObj.rangeList
      })
    },
    assessTypeName() {
      this.$API.assessmentTemplateSetting.dictCode({ dictCode: 'claimType' }).then((res) => {
        this.typeRectification = res.data
        // console.log(this.typeRectification)
      })
      if (this.queryType === 'copy') {
        this.detailsObj = cloneDeep(this.queryData)
        delete this.detailsObj.id
        this.detailsObj.templateCode = ''
        this.detailsObj.status = '0'
        this.detailsObj.claimTypeList.map((item) => {
          item.standIdList = item.claimStandList
          item.standIdList.map((val) => {
            val.claimCalcTypeStr = val.claimStandCalcRule?.ruleType
            val.defaultValue = val.claimStandCalcRule?.defaultValue
            val.maxValue = val.claimStandCalcRule?.maxValue
            val.minValue = val.claimStandCalcRule?.minValue
          })
          delete item.claimStandList
          delete item.id
        })
        console.log(this.detailsObj)
        this.assessmentType = this.detailsObj.claimTypeList
        this.organization = this.detailsObj.rangeList
      }
    },
    topSubmit(e) {
      let _formObject = cloneDeep(this.$refs.topInfo.formObject)
      let _assessmentType = cloneDeep(this.assessmentType)
      if (!_formObject.claimTemplateName) {
        this.$toast({ content: this.$t('模板名称不能为空'), type: 'warning' })
        return
      }
      if (this.organization.length < 1) {
        this.$toast({ content: this.$t('组织不能为空'), type: 'warning' })
        return
      }
      if (_assessmentType.length < 1) {
        this.$toast({ content: this.$t('考核类型不能为空'), type: 'warning' })
        return
      }
      let companyList = []
      let _organization = this.organization
      _organization.map((item) => {
        // if (item.orgType == 'company') {
        companyList.push({
          companyCode: item.orgCode || item.companyCode,
          companyName: item.orgName || item.companyName,
          companyId: item.id || item.companyId
        })
        // }
      })
      let params
      if (this.queryType == 'edit') {
        _assessmentType.map((item) => {
          item.rangeList = companyList
          item.standIdList = item.standIdList.map((val) => val.id)
          delete item.typeId
          delete item.checkbox
          delete item.index
        })
        params = {
          claimTypeList: _assessmentType,
          rangeList: companyList,
          endTime: '',
          startTime: '',
          templateName: _formObject.claimTemplateName,
          remark: _formObject.remark
        }
      } else {
        _assessmentType.map((item) => {
          item.companyList = companyList
          item.standIdList = item.standIdList.map((val) => val.id)
          delete item.typeId
          delete item.checkbox
          delete item.index
        })
        params = {
          claimTypeList: _assessmentType,
          companyList: companyList,
          endTime: '',
          startTime: '',
          templateName: _formObject.claimTemplateName,
          remark: _formObject.remark
        }
      }

      if (e == '0' && !this.detailsObj) {
        this.$API.assessmentTemplateSetting.add(params).then(() => {
          this.$router.push({
            path: '/supplier/assessment/template-setting'
          })
        })
      } else if (e == '0' && Object.hasOwnProperty.call(this.detailsObj, 'id')) {
        params.id = this.detailsObj.id
        this.$API.assessmentTemplateSetting.updateStatus(params).then(() => {
          this.$router.push({
            path: '/supplier/assessment/template-setting'
          })
        })
      } else if (e == '0' && this.queryType === 'copy') {
        this.$API.assessmentTemplateSetting.add(params).then(() => {
          this.$router.push({
            path: '/supplier/assessment/template-setting'
          })
        })
      }

      //  else if (e == '1') {
      //   this.$API.assessmentTemplateSetting.addSubmit(params).then(() => {
      //     this.$router.push({
      //       path: '/supplier/assessment/template-setting'
      //     })
      //   })
      // }
    },
    clickTopInfoApply(val) {
      this.organization = val
      // this.$API.assessmentTemplateSetting
      //   .pageClaimStand({ page: { current: 1, size: 10000 } })
      //   .then((res) => {
      //     this.assessIndicatorsStandName = res.data
      //     // sessionStorage.setItem('assessIndicatorsStandName', JSON.stringify(res.data))
      //   })
    },
    assessTypeAdd(data) {
      this.$set(this, 'assessmentType', data)
    }
  }
}
</script>
<style lang="scss">
.inventory {
  height: 100vh;
  .operation {
    span {
      color: #2783fe;
      text-decoration: underline;
      margin: 0 10px;
    }
  }
  .operation:hover {
    cursor: pointer;
  }
}
</style>
