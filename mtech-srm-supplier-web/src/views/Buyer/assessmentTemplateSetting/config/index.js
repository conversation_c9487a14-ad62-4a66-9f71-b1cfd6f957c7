import { i18n } from '@/main.js'
import Vue from 'vue'

const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'enable', icon: 'icon_table_enable', title: i18n.t('生效') },
  { id: 'disable', icon: 'icon_table_disable', title: i18n.t('失效') },
  { id: 'delete', icon: 'icon_solid_Delete1', title: i18n.t('删除') },
  { id: 'copy', icon: 'icon_table_copy', title: i18n.t('复制') }
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'templateCode',
    headerText: i18n.t('模板编码'),
    cssClass: 'field-content'
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'scopeOrgName',
    headerText: i18n.t('所属组织')
  },
  {
    field: 'status',
    headerText: i18n.t('模板状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已取消'),
        0: i18n.t('新建'),
        1: i18n.t('已提交'),
        3: i18n.t('审批拒绝'),
        4: i18n.t('生效'),
        5: i18n.t('失效'),
        10: i18n.t('待反馈')
      }
    }
  },
  {
    field: 'startTime',
    headerText: i18n.t('生效日期'),
    ignore: true
  },
  {
    field: 'endTime',
    headerText: i18n.t('失效日期'),
    ignore: true
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    ignore: true
  },
  {
    field: 'operation',
    headerText: i18n.t('操作'),
    template: () => {
      return {
        template: Vue.component('operation', {
          template: `<div class="operation">
          <span @click="clickEffect">{{ $t('生效') }}</span><span @click="clickdisable">{{ $t('失效') }}</span></div>`,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {
            clickEffect() {
              this.$parent.$emit('clickOperation', { data: this.data, value: 'effect' })
            },
            clickdisable() {
              this.$parent.$emit('clickOperation', { data: this.data, value: 'disble' })
            }
          }
        })
      }
    },
    ignore: true
  }
]
export const pageConfig = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      asyncConfig: {
        url: '/analysis/tenant/claimTemplate/queryIPage',
        serializeList: (list) => {
          return list.map((item) => {
            let scopeOrgName = ''
            item.rangeList.map((val) => {
              scopeOrgName += `${val.companyName},`
            })
            return {
              ...item,
              scopeOrgName
            }
          })
        }
      }
    }
  }
]
