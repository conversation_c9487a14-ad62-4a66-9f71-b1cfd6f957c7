<!-- 考核模板设置 索赔 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @clickOperation="clickOperation"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config/index'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      formObject: {},
      companySelect: [],
      supplierSelect: [],
      itemSelect: []
    }
  },
  computed: {},
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      let selectList = e.gridRef.getMtechGridRecords()
      if (selectList.length < 1 && e.toolbar.id !== 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      } else if (e.toolbar.id === 'enable') {
        this.handleClickToolBarEnable(selectList)
      } else if (e.toolbar.id === 'disable') {
        this.handleClickToolBarDisable(selectList)
      } else if (e.toolbar.id === 'delete') {
        this.handleClickToolBarDelete(selectList)
      } else if (e.toolbar.id === 'copy') {
        this.handleClickToolBarCopy(selectList)
      }
    },
    handleClickToolBarAdd() {
      this.$router.push({
        path: '/supplier/assessment/template-detail',
        query: { type: 'Add' }
      })
    },
    handleClickToolBarEnable(selectList) {
      let ids = selectList.map((item) => item.id)
      let params = {
        ids: ids,
        status: '1'
      }
      this.$API.assessmentTemplateSetting.invalidOrEffect(params).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    handleClickToolBarDisable(selectList) {
      let ids = selectList.map((item) => item.id)
      let params = {
        ids: ids,
        status: '0'
      }
      this.$API.assessmentTemplateSetting.invalidOrEffect(params).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    handleClickToolBarDelete(selectList) {
      let ids = selectList.map((item) => item.id)
      let params = {
        ids: ids
      }
      this.$API.assessmentTemplateSetting.delete(params).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    async handleClickToolBarCopy(selectList) {
      if (selectList.length > 1) {
        this.$toast({ content: this.$t('只能复制一行数据'), type: 'warning' })
        return
      }
      let id = selectList[0].id
      let paramter = null
      await this.$API.assessmentTemplateSetting.queryDetail(id).then((res) => {
        paramter = cloneDeep(res.data)
      })
      this.$router.push({
        path: '/supplier/assessment/template-detail',
        query: { type: 'copy', data: paramter }
      })
      // let companyList = paramter.rangeList

      // paramter.claimTypeList.map((item) => {
      //   item.companyList = companyList
      //   item.standIdList = item.claimStandList.map((val) => val.id)
      //   delete item.claimStandList
      //   delete item.id
      // })
      // let params = {
      //   claimTypeList: paramter.claimTypeList,
      //   companyList: companyList,
      //   endTime: '',
      //   startTime: '',
      //   templateName: paramter.templateName,
      //   remark: paramter.remark
      // }

      // await this.$API.assessmentTemplateSetting.add(params).then(() => {
      //   this.$refs.tepPage.refreshCurrentGridData()
      //   this.$toast({ content: this.$t('操作成功'), type: 'success' })
      // })
    },
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'templateCode') {
        this.$router.push({
          path: '/supplier/assessment/template-detail',
          query: {
            type: 'edit',
            id: data.id,
            keys: this.randomString(10)
          }
        })
      }
    },
    clickOperation(e) {
      let ids = [e.data.id]
      let params = {
        ids: ids
      }
      if (e.value === 'disble') {
        // 5 失效
        if (e.data.status == 5) {
          this.$toast({ content: this.$t('已失效数据不能重复操作'), type: 'warning' })
          return
        }
        params.status = '0'
        this.$API.assessmentTemplateSetting.invalidOrEffect(params).then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        })
      } else if (e.value === 'effect') {
        // 4  生效
        if (e.data.status == 4) {
          this.$toast({ content: this.$t('已生效数据不能重复操作'), type: 'warning' })
          return
        }
        params.status = '1'
        this.$API.assessmentTemplateSetting.invalidOrEffect(params).then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        })
      }
    },
    // 随机数创造
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnoprstuvwxyz123456789',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    }
  }
}
</script>

<style lang="scss">
.full-height {
  height: 100vh;
  background-color: #fff;
  .operation {
    span {
      color: #2783fe;
      text-decoration: underline;
      margin: 0 10px;
    }
  }
  .operation:hover {
    cursor: pointer;
  }
}
</style>
