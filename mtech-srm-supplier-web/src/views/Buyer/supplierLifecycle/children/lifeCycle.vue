<template>
  <div class="lifeCycle-container">
    <!-- 顶部信息 start -->
    <div class="header-status">
      <div class="cp-name-line fbox">
        <div class="cp-name epls">
          <template v-if="logoUrl">
            <img :src="logoUrl" alt="" />
          </template>
          <template v-else>
            <i class="mt-icons mt-icon-icon_card_company"></i>
          </template>
          <span> {{ headerInfo.supplierEnterpriseName }}</span>
        </div>
        <div class="cp-tips fbox flex1">
          <template v-for="(item, index) in myLabelList">
            <div
              class="tip-item"
              v-if="index < 3"
              :key="item.labelId"
              :title="item.labelName"
              :alter="item.labelName"
            >
              <div class="txt-wrap">{{ item.labelName }}</div>
              <div class="close-btn" v-if="item.del === 1" @click="deleteLabel(item)">
                <i class="mt-icons mt-icon-icon_input_clear"></i>
              </div>
            </div>
          </template>
          <div class="tip-item" v-if="myLabelList.length > 3" @click="showMore">...</div>
          <div class="edit-tips" v-if="labelList.length > 0" @click="popEditTipDialog()">
            {{ $t('编辑标签') }}
          </div>
        </div>
        <div class="cp-select fbox">
          <mt-select
            :width="220"
            :data-source="organizationList"
            css-class="e-outline cp-select-input"
            v-model="organizationId"
            :show-clear-button="false"
            :placeholder="$t('请选择公司')"
          ></mt-select>
          <div class="right-btn" @click="pdfPrint">{{ $t('导出PDF') }}</div>
          <div class="right-btn" @click="onBack">{{ $t('返回') }}</div>
        </div>
      </div>

      <!-- 评分行 -->
      <div class="rate-line fbox">
        <div class="rate-item fbox">
          <div class="rate-name">{{ $t('市场表现') }}</div>
          <div class="rate-score fbox">
            <div class="bg-width" :style="{ width: scoreArray[0] + '%' }"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
          </div>
          <div class="rate-num">{{ headerInfo.marketScore || '--' }}</div>
        </div>

        <div class="rate-item fbox">
          <div class="rate-name">{{ $t('企业信用') }}</div>
          <div class="rate-score fbox">
            <div class="bg-width" :style="{ width: scoreArray[1] + '%' }"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
          </div>
          <div class="rate-num">{{ headerInfo.creditScore || '--' }}</div>
        </div>

        <div class="rate-item fbox">
          <div class="rate-name">{{ $t('产品质量') }}</div>
          <div class="rate-score fbox">
            <div class="bg-width" :style="{ width: scoreArray[2] + '%' }"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
          </div>
          <div class="rate-num">
            {{ headerInfo.productQualityScore || '--' }}
          </div>
        </div>

        <div class="rate-item fbox">
          <div class="rate-name">{{ $t('履约能力') }}</div>
          <div class="rate-score fbox">
            <div class="bg-width" :style="{ width: scoreArray[3] + '%' }"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
          </div>
          <div class="rate-num">{{ headerInfo.promiseScore || '--' }}</div>
        </div>

        <div class="rate-item fbox">
          <div class="rate-name">{{ $t('财务表现') }}</div>
          <div class="rate-score fbox">
            <div class="bg-width yellow-bg" :style="{ width: scoreArray[4] + '%' }"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
            <div class="star-bg"></div>
          </div>
          <div class="rate-num yellow-num">
            {{ headerInfo.financeScore || '--' }}
          </div>
        </div>
      </div>

      <div class="supplier-info fbox">
        <div class="info-item flex1">
          <div class="mian-title green">
            {{ performanceData.supplierGradeName || '--' }}
          </div>
          <div class="sub-title">{{ $t('供应商级别') }}</div>
        </div>
        <div class="info-item flex1">
          <div class="mian-title orange">
            <template v-if="!!scoreInfo.creditScore || !!scoreInfo.level"
              >{{ scoreInfo.level || '--' }} ({{ scoreInfo.creditScore }})</template
            >
            <template v-else>{{ $t('未购买') }}</template>
          </div>
          <div class="sub-title fbox risk">
            {{ $t('征信风险') }}
            <div class="pop-icon">
              <mt-icon name="icon_outline_prompt"></mt-icon>

              <div class="pop-box">
                <div class="list-box fbox">
                  <div class="it-list" v-for="item of riskList" :key="item.name">
                    <div class="tp-txt">{{ item.name }}</div>
                    <div class="bt-color" :style="{ background: item.color }"></div>
                  </div>
                </div>
                <div class="tip-lf fbox">
                  <div>{{ $t('风险较低') }}</div>
                  <div>{{ $t('风险较高') }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="bt-tips">
            <span>{{ $t('更新日期') }}：{{ scoreInfo.lastUpdateTime | filterTime }}</span>
            <span class="sec-btn" @click="updataScore">{{ $t('更新') }}</span>
          </div>
        </div>
        <div class="info-item flex1">
          <div class="mian-title blue">
            {{ biddingData.cooperationTimes || '--' }} <span>{{ $t('次') }}</span>
          </div>
          <div class="sub-title">{{ $t('与我累计合作') }}</div>
        </div>
        <div class="info-item flex1">
          <div class="mian-title red">
            <span>¥ </span>{{ biddingData.cooperationMoney || '--' }}
          </div>
          <div class="sub-title">{{ $t('累计合作金额') }}</div>
        </div>
        <div class="info-item flex1">
          <div class="mian-title blue">
            {{ performanceData.performanceScore || '--' }}
          </div>
          <div class="sub-title">{{ $t('绩效得分') }}</div>
        </div>
      </div>
    </div>

    <!-- 顶部信息 end -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>

    <keep-alive include="supplierPortrait">
      <supplier-portrait
        ref="portrait"
        v-if="selectIndex === 0"
        :business-d-t-o="businessDTO"
        :partner-archive-id="partnerArchiveId"
        :enterprise-ship-list="enterpriseShipList"
        :organization-id="organizationId"
        :supplier-enterprise-id="supplierEnterpriseId"
        :file-url-arr="fileUrlArr"
        @refresh="refreshShipList"
        @setBiddingData="setBiddingData"
        @setPerformanceScore="setPerformanceScore"
      >
        <access-process :id="partnerArchiveId" :organization-id="organizationId"></access-process>
      </supplier-portrait>
      <task-center
        v-if="selectIndex === 1"
        :id="partnerArchiveId"
        :organization-id="organizationId"
      ></task-center>
      <access-process
        v-if="selectIndex === 2"
        :id="partnerArchiveId"
        :organization-id="organizationId"
      ></access-process>
    </keep-alive>
  </div>
</template>

<script>
import TaskCenter from '../components/taskCenter.vue'
import AccessProcess from '../components/accessProcess.vue'
import SupplierPortrait from '../components/supplierPortrait.vue'
import html2canvas from 'html2canvas'
import jspdf from 'jspdf'
import utils from '../../../../utils/utils'
export default {
  components: {
    TaskCenter,
    AccessProcess,
    SupplierPortrait
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('供应商画像')
        },
        {
          title: this.$t('任务总览')
        },
        {
          title: this.$t('阶段总览')
        }
      ],
      partnerArchiveId: '',
      supplierEnterpriseId: '',
      info: {},
      // 顶部信息
      headerInfo: {},
      scoreArray: [],
      organizationList: [], // 组织结构列表
      organizationId: 0,
      enterpriseShipList: {}, // 公司关系图
      scoreInfo: {}, // 企业评分
      biddingData: {}, // 合作次数 合作金额
      performanceData: {
        performanceScore: '--', // 绩效分数
        supplierGradeName: '--' // 级别
      },

      labelList: [],
      myLabelList: [],

      fileUrlArr: [], // 认证信息文件url
      logoUrl: '',
      riskList: [
        {
          name: 'R1',
          color: '#19371B'
        },
        {
          name: 'R2',
          color: '#488550'
        },
        {
          name: 'R3',
          color: '#7EC685'
        },
        {
          name: 'R4',
          color: '#AAF745'
        },
        {
          name: 'R5',
          color: '#FEFA01'
        },
        {
          name: 'R6',
          color: '#FFE601'
        },
        {
          name: 'R7',
          color: '#E3C0A1'
        },
        {
          name: 'R8',
          color: '#FAC091'
        },
        {
          name: 'R9',
          color: '#F6A915'
        },
        {
          name: 'R10',
          color: '#E16C0C'
        },
        {
          name: 'R11',
          color: '#D65409'
        },
        {
          name: 'R12',
          color: '#CF3C06'
        },
        {
          name: 'R13',
          color: '#C82C03'
        },
        {
          name: 'R14',
          color: '#C21900'
        },
        {
          name: 'R15',
          color: '#973430'
        }
      ]
    }
  },
  filters: {
    filterTime: function (value) {
      if (!value || utils.isEmpty(value)) {
        return '--'
      }
      return utils.formateTime(value, 'yyyy-MM-dd')
    }
  },
  computed: {
    businessDTO() {
      return this.headerInfo.businessDTO ? this.headerInfo.businessDTO : {}
    }
  },
  watch: {
    organizationId(nv) {
      if (nv) {
        this.getLabelList(nv, this.supplierEnterpriseId) // 获取我的标签
      }
    }
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },

    handleSelectTab(index) {
      this.selectIndex = index
    },

    // 获取任务详情
    getAccessDetail(id) {
      this.$API.supplierlifecycle.getDetail({ partnerArchiveId: id }).then((res) => {
        this.info = res.data
      })
    },

    /**
     * 获取组织结构树
     * 如果是列表页面过的 默认传 partnerArchiveId 档案id  supplierEnterpriseId 供应商id 默认取获取第一个返回值的 organizationId 组织id 去请求下面的几个接口
     * 如果是供应商资源页面点过来的 用url带过来的  partnerArchiveId supplierEnterpriseId organizationId
     */
    getOrganization(partnerArchiveId, supplierEnterpriseId, organizationId) {
      return this.$API.supplierlifecycle
        .getOrganization({
          partnerArchiveId,
          supplierEnterpriseId: supplierEnterpriseId
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            let organizationList = res.data.map((item) => {
              return {
                ...item,
                text: item.name,
                value: item.id
              }
            })
            this.organizationList = organizationList

            if (
              !!organizationId &&
              organizationList.filter((v) => v.id === organizationId).length > 0
            ) {
              // 预选第一个组织
              this.organizationId = organizationId
            } else if (!organizationId) {
              this.organizationId = organizationList[0].id
            } else if (
              !!organizationId &&
              organizationList.filter((v) => v.id === organizationId).length === 0
            ) {
              this.$toast({
                content: this.$t('未查询到当前公司信息，请重试!'),
                type: 'warning'
              })
            }
          } else {
            this.$toast({
              content: this.$t('获取组织机构失败，请重试!'),
              type: 'warning'
            })
          }
        })
    },

    // 供应商档案详情、工商信息
    getRecordDetail(id) {
      this.$API.supplierlifecycle.getRecordDetail({ partnerArchiveId: id }).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.headerInfo = res.data

          let scoreArray = [
            res.data.marketScore || 0,
            res.data.creditScore || 0,
            res.data.productQualityScore || 0,
            res.data.promiseScore || 0,
            res.data.financeScore || 0
          ]
          let widthArray = []
          for (let i = 0; i < 5; i++) {
            let ints = scoreArray[i].toString().split('.')

            let width = (ints[0] / 5) * 100
            let underWidth = 0 // 分子的宽度 因为加了一个margin 2px 即 12/14 所以要重新计算一下五角星的宽 (相对计算一个范围就行 最大 17%)
            let finalWith = 0
            if (ints[1]) {
              let tmpValue = ints[1][0] || 0
              if (tmpValue >= 0 && tmpValue <= 2) {
                underWidth = 3.4
              }
              if (tmpValue > 2 && tmpValue <= 4) {
                underWidth = 6.8
              }
              if (tmpValue > 4 && tmpValue <= 6) {
                underWidth = 10.2
              }
              if (tmpValue > 6 && tmpValue <= 8) {
                underWidth = 13.6
              }
              if (tmpValue > 8 && tmpValue <= 10) {
                underWidth = 16.5
              }
            }
            finalWith = width + underWidth
            widthArray.push(finalWith)
          }
          this.scoreArray = widthArray

          this.getFileLink(res.data.businessDTO)
        } else {
          this.$toast({
            content: this.$t('获取供应商信息失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    getFileLink(businessDTO) {
      if (utils.isEmpty(businessDTO)) {
        return
      }
      let { enterpriseLogoFileId, enterpriseProfileDTOs } = businessDTO

      let fileIdArray = [enterpriseLogoFileId]
      if (!!enterpriseProfileDTOs && enterpriseProfileDTOs.length > 0) {
        enterpriseProfileDTOs.forEach((item) => {
          fileIdArray.push(item.fileId)
        })
      }

      this.$API.supplierlifecycle.commonFilesPath({ ids: fileIdArray }).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          let fileUrlArr = []
          for (let index in res.data) {
            if (index === enterpriseLogoFileId) {
              this.logoUrl = res.data[index]
            } else {
              fileUrlArr.push(res.data[index])
            }
          }
          this.fileUrlArr = fileUrlArr
        }
      })
    },

    // 企业图谱
    getEnterpriseShip(partnerArchiveId, refresh = false) {
      this.$API.supplierlifecycle
        .getEnterpriseShip({
          partnerArchiveId,
          refresh
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.enterpriseShipList = res.data
          } else {
            this.$toast({
              content: this.$t('获取企业图谱失败，请重试!'),
              type: 'warning'
            })
          }
        })
    },

    // 企业评分
    getCreditScore(partnerArchiveId, refresh = false) {
      this.$API.supplierlifecycle
        .getCreditScore({
          partnerArchiveId,
          refresh
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.scoreInfo = res.data
          } else {
            this.$toast({
              content: this.$t('获取企业评分失败，请重试!'),
              type: 'warning'
            })
          }
        })
    },
    // 获取我的标签
    getDefineList() {
      this.$API.supplierlifecycle
        .getDefineList({
          labelDefineType: 3
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.labelList = res.data
          } else {
            this.labelList = []
          }
        })
    },
    // 获取我的标签
    getLabelList(companyId, supplierEnterpriseId) {
      this.$API.supplierlifecycle
        .getLabelList({
          orgId: companyId,
          supplierEnterpriseId: supplierEnterpriseId
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.myLabelList = res.data
          } else {
            this.myLabelList = []
          }
        })
    },
    // 刷新信用
    updataScore() {
      this.getCreditScore(this.partnerArchiveId, true)
    },

    // 刷新关系图
    refreshShipList() {
      // 防抖处理搜索
      let getListData = this.getEnterpriseShip
      let request = utils.debounce(getListData(this.partnerArchiveId, true), 1000)
      request()
    },

    // 设置合作次数 合作金额
    setBiddingData(data) {
      this.biddingData = data
    },

    // 设置合作次数 合作金额
    setPerformanceScore(data) {
      this.performanceData = data
    },

    // 弹框编辑标签
    popEditTipDialog(method = '') {
      let labelList = this.labelList
      let myLabelList = this.myLabelList
      let organizationId = this.organizationId
      let supplierEnterpriseId = this.supplierEnterpriseId
      if (labelList.length === 0) {
        this.$toast({
          content: this.$t('未获取到标签数据！'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('../components/addLabelDialog.vue'),
        data: {
          title: this.$t('编辑标签'),
          labelList: labelList || [],
          myLabelList: myLabelList || [],
          organizationId: organizationId,
          supplierEnterpriseId: supplierEnterpriseId,
          method
        },
        success: (result) => {
          if (result.updata) {
            // 更新成功 重新获取我的标签
            this.getLabelList(organizationId, supplierEnterpriseId) // 获取我的标签
          }
        }
      })
    },

    // 展示我的标签弹框 并且可以编辑
    showMore() {
      this.popEditTipDialog('my')
    },

    // 删除标签
    deleteLabel(item) {
      let { labelCode } = item
      let labelIdList = [item.labelId]
      this.$API.supplierlifecycle
        .deleteLabel({
          orgId: this.organizationId,
          labelIdList: labelIdList,
          supplierEnterpriseId: this.supplierEnterpriseId
        })
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 删除数据
            let index = this.myLabelList.findIndex((v) => v.labelCode === labelCode)
            this.myLabelList.splice(index, 1)

            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
          } else {
            this.$toast({
              content: this.$t('删除失败，请重试！'),
              type: 'warning'
            })
          }
        })
    },

    async pdfPrint() {
      var target = document.getElementById('supplierPublic')
      this.$loading()

      const canvas = await html2canvas(target, {
        useCORS: true,
        scale: 1, //设置放大的倍数
        height: target.scrollHeight,
        windowHeight: target.scrollHeight
      })
      this.$hloading()
      var contentWidth = canvas.width
      var contentHeight = canvas.height

      //一页pdf显示html页面生成的canvas高度;
      var pageHeight = (contentWidth / 592.28) * 841.89
      //未生成pdf的html页面高度
      var leftHeight = contentHeight
      //页面偏移
      var position = 0
      //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
      var imgWidth = 595.28
      var imgHeight = (592.28 / contentWidth) * contentHeight

      var pageData = canvas.toDataURL('image/jpeg', 1.0)

      var pdf = new jspdf('', 'pt', 'a4')

      //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
      //当内容未超过pdf一页显示的范围，无需分页
      if (leftHeight < pageHeight) {
        pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
      } else {
        while (leftHeight > 0) {
          pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
          leftHeight -= pageHeight
          position -= 841.89
          //避免添加空白页
          if (leftHeight > 0) {
            pdf.addPage()
          }
        }
      }

      pdf.save('供应商画像.pdf')
    }
  },
  async created() {
    let partnerArchiveId = this.$route.query.id
    let supplierEnterpriseId = this.$route.query.supplierEnterpriseId
    let organizationId = this.$route.query.organizationId || ''
    if (!partnerArchiveId || !supplierEnterpriseId) {
      this.$toast({
        content: '获取ID失败，请重试!',
        type: 'warning'
      })
      return
    }
    this.partnerArchiveId = partnerArchiveId
    this.supplierEnterpriseId = supplierEnterpriseId
    this.organizationId = organizationId // 供应商资源库跳转带公司id
    // this.getAccessDetail(id);
    this.getDefineList() // 获取总标签

    this.getCreditScore(partnerArchiveId)
    this.getRecordDetail(partnerArchiveId)
    this.getEnterpriseShip(partnerArchiveId)
    await this.getOrganization(partnerArchiveId, supplierEnterpriseId, organizationId) // 获取公司 并默认第一个公司的id为organizationId
    this.getLabelList(this.organizationId, supplierEnterpriseId) // 获取我的标签
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.epls {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.lifeCycle-container {
  margin-top: 20px;
  min-width: 1200px;
  overflow-x: scroll;

  .header-status {
    padding: 30px 40px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .cp-name-line {
      white-space: nowrap;

      .cp-name {
        img {
          display: inline-block;
          width: 30px;
          height: 30px;
          margin-right: 10px;
          vertical-align: middle;
        }

        i {
          display: inline-block;
          width: 30px;
          height: 30px;
          font-size: 30px;
          margin-right: 10px;
          color: #00469c;
          vertical-align: middle;
        }

        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          font-size: 24px;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
          vertical-align: middle;
        }
      }

      .cp-tips {
        margin-left: 40px;
        flex-wrap: wrap;

        .tip-item {
          height: 30px;
          line-height: 30px;
          padding: 0 10px;
          font-size: 16px;
          font-weight: 500;
          color: rgba(51, 166, 23, 1);
          background: rgba(51, 166, 23, 0.12);
          border-radius: 2px;
          margin-right: 10px;
          position: relative;
          user-select: none;
          max-width: 100px;
          margin-bottom: 10px;

          .txt-wrap {
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .close-btn {
            display: none;
          }

          &:hover .close-btn {
            display: block;
            width: 12px;
            height: 12px;
            font-size: 12px;
            position: absolute;
            right: -6px;
            top: -6px;
            color: #9baac1;
            line-height: 12px;
            cursor: pointer;
          }

          i.mt-icon-plus {
            line-height: 30px;
            font-size: 14px;
            cursor: pointer;
          }
        }

        .edit-tips {
          line-height: 30px;
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      .cp-select {
        width: 410px;
        height: 50px;

        .right-btn {
          font-size: 14px;
          line-height: 40px;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
          font-weight: 600;
        }

        .right-btn:nth-child(2) {
          margin-left: 50px;
        }

        .right-btn:nth-child(3) {
          margin-left: 40px;
        }
      }
    }

    .rate-line {
      margin-top: -8px;

      .rate-item {
        margin-right: 30px;

        .rate-name {
          font-size: 12px;
          line-height: 12px;
          color: rgba(41, 41, 41, 1);
        }

        .rate-score {
          margin-left: 5px;
          width: 70px;
          height: 12px;
          position: relative;

          .bg-width {
            width: 100%;
            height: 12px;
            position: absolute;
            left: 0;
            top: 0;
            background: #00469c;
          }

          .yellow-bg {
            background: #eda133;
          }

          .star-bg {
            width: 14px;
            background: rgba(203, 203, 203, 0.4) url(../../../../assets/star.png) 0 0 no-repeat;
            background-size: 12px 12px;
            position: relative;
            z-index: 1;

            &::after {
              content: ' ';
              width: 2px;
              height: 12px;
              position: absolute;
              right: 0;
              top: 0;
              background: #fff;
            }
          }
        }

        .rate-num {
          margin-left: 5px;
          font-size: 12px;
          font-weight: bold;
          color: rgba(0, 70, 156, 1);
        }

        .yellow-num {
          color: #eda133;
        }
      }
    }

    .supplier-info {
      padding: 0 5%;
      height: 100px;
      margin-top: 20px;
      text-align: center;

      .info-item {
        position: relative;
        padding: 20px 0px;
        display: flex;
        justify-content: space-between;
        flex-direction: column;

        .mian-title {
          height: 24px;
          line-height: 24px;
          font-size: 24px;
          font-weight: 600;
          color: rgba(138, 204, 64, 1);
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          position: relative;

          span {
            font-size: 24px;
          }
        }

        .green {
          color: #8acc40;
        }

        .orange {
          color: #eda133;
        }

        .blue {
          color: #00469c;
        }

        .red {
          color: #ed5633;
        }

        .sub-title {
          height: 16px;
          font-size: 16px;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }

        .risk {
          justify-content: center;

          i {
            font-size: 18px;
            color: #4d5b6f;
            margin-left: 10px;
            cursor: pointer;
          }

          .pop-icon {
            position: relative;

            .pop-box {
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
              display: none;
              position: absolute;
              z-index: 9;
              width: 500px;
              height: 72px;
              background: rgba(255, 255, 255, 1);
              border-radius: 2px;
              padding: 14px 18px 10px;
              top: 16px;
              left: -250px;
              transform: translate3d(10px, 0, 0);

              .it-list {
                width: 30px;
                text-align: center;
                margin-right: 1px;

                .tp-txt {
                  height: 12px;
                  font-size: 12px;
                  color: rgba(41, 41, 41, 1);
                  margin-bottom: 4px;
                }

                .bt-color {
                  width: 30px;
                  height: 10px;
                  border-radius: 2px;
                }
              }

              .tip-lf {
                height: 12px;
                justify-content: space-between;
                font-size: 12px;
                color: rgba(41, 41, 41, 1);
                margin-top: 10px;
              }
            }
          }

          .pop-icon:hover {
            .pop-box {
              display: block;
            }
          }
        }

        .bt-tips {
          width: 100%;
          text-align: center;
          font-size: 12px;
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;

          span:nth-child(1) {
            color: #9a9a9a;
          }

          span:nth-child(2) {
            color: #00469c;
            display: inline-block;
            margin-left: 10px;
            cursor: pointer;
          }
        }

        &::after {
          content: ' ';
          display: inline-block;
          position: absolute;
          width: 1px;
          height: 60px;
          background: #dddddd;
          right: 0;
          top: 20px;
        }
      }

      .info-item:last-child {
        &::after {
          display: none;
        }
      }
    }
  }
}
</style>
