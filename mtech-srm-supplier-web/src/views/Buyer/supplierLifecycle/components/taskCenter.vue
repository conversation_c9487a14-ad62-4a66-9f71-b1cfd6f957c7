<template>
  <div class="task-center fbox">
    <div class="task-sidebar">
      <div class="search-box"></div>

      <div class="task-collapse">
        <div v-for="(taskItem, index) of fullTaskIndexArr" :key="taskItem">
          <div class="collapse-header" :class="{ active: index === 0 }">
            {{ taskItem }} ({{ fullTaksArr[index] | filterStatus10 }})
          </div>
          <div class="collapse-content">
            <template v-for="item of fullTaksArr[index]">
              <mt-collapse
                :accordion="false"
                :has-arr="true"
                :key="item.orgId"
                v-model="activeCompanyId"
              >
                <mt-panel :name="item.orgId + '_' + index">
                  <div slot="header" class="accordion-header">
                    <span class="company-name" :alt="item.orgName" :title="item.orgName">{{
                      item.orgName
                    }}</span>
                    <span class="header-number">({{ item.buyerTaskList | filterStatus102 }})</span>
                  </div>
                  <div slot="content">
                    <div
                      class="sub-item"
                      v-for="childItem of item.buyerTaskList"
                      :key="childItem.id"
                      @click="selectTask(childItem, index)"
                      :class="{
                        active: activeIndexId === childItem.id,
                        grey:
                          childItem.status === 30 ||
                          childItem.status === 40 ||
                          childItem.status === 50
                      }"
                    >
                      <div
                        class="task-name ellipsis"
                        :alt="childItem.taskName"
                        :title="childItem.taskName"
                      >
                        {{ childItem.taskName }}
                      </div>
                      <div class="sub-tips">
                        {{ mapState[childItem.status] }}
                      </div>
                    </div>
                  </div>
                </mt-panel>
              </mt-collapse>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="task-content flex1">
      <div class="task-info fbox">
        <div class="mian-info flex1">
          <div class="task-list fbox">
            <span>{{ $t('调查表名称') }}：{{ buyerTaskInstance.taskName }}</span>
            <!-- <span>{{ $t("调查表类型") }}：{{ $t("企业信息调查表") }}</span> -->
            <span
              >{{ $t('调查表填写方') }}：{{
                buyerTaskInstance.taskOwner && buyerTaskInstance.taskOwner === 1
                  ? $t('供方')
                  : buyerTaskInstance.taskOwner && buyerTaskInstance.taskOwner === 0
                  ? $t('采方')
                  : '--'
              }}</span
            >
            <span>{{ $t('邀请人') }}：{{ buyerTaskInstance.createUserName }}</span>
            <span>{{ $t('邀请时间') }}：{{ buyerTaskInstance.createTime }}</span>
            <!-- 待审批 的驳回通过放入 审批中心去做了 此处不展示 -->
            <span class="reject-reason" v-if="formInfo.status === 20">{{
              $t('该调查表已发送至相关人员的审批中心等待审批')
            }}</span>
          </div>
          <div class="categroy-list fbox" v-if="buyerPartnerFactoryRelationList.length > 0">
            <div
              class="factory-item fbox"
              v-for="item of buyerPartnerFactoryRelationList"
              :key="item.id"
            >
              <span class="factory-name">{{ item.factoryName }}</span>
              <template
                v-if="
                  !!item.buyerPartnerCategoryRelationList &&
                  item.buyerPartnerCategoryRelationList.length > 0
                "
              >
                <span
                  class="factory-category"
                  v-for="cItem of item.buyerPartnerCategoryRelationList"
                  :key="cItem.id"
                  >{{ cItem.categoryName }}</span
                >
              </template>
            </div>
          </div>
        </div>
        <div class="btn-box fbox">
          <template v-if="formInfo.status === 30 && !!buyerTaskInstance.auditRemark">
            <mt-tooltip :content="buyerTaskInstance.auditRemark" target="#box">
              <div class="err-tips" id="box">
                <i class="mt-icons mt-icon-MT_info"></i>{{ $t('驳回原因') }}
              </div>
            </mt-tooltip>
          </template>

          <!-- 是否展示按钮 只有采方待办 && 待填写 已驳回 -->
          <template v-if="(formInfo.status === 10 || formInfo.status === 30) && displayOperation">
            <div class="normal-btn" @click="saveTask()">{{ $t('保存') }}</div>
            <div class="normal-btn" @click="proSubmitTask()">
              <template v-if="formInfo.status === 10">{{ $t('提交') }}</template>
              <template v-if="formInfo.status === 30">{{ $t('重新提交') }}</template>
            </div>
          </template>
        </div>
      </div>
      <div class="task-form fbox">
        <div class="side-bar">
          <div
            class="side-item ellipsis"
            :class="{ active: activeForm.id === item.id }"
            v-for="item of buyerFormInstanceList"
            :key="item.id"
            @click="scrollInto(item.id)"
          >
            {{ item.formName }}
          </div>
          <!--@click="selectFromItem(item)"-->
        </div>

        <div class="form-content flex1" ref="formContent">
          <template v-if="formTemplateArr.length > 0">
            <div
              v-for="item of formTemplateArr"
              :key="item.id"
              class="display-item"
              :ref="'formItem_' + item.id"
              :data-id="item.id"
            >
              <!--class="none" :class="{'display-item': activeForm.id === item.id}"-->
              <div class="parse-title">{{ item.name }}</div>
              <mt-parser :ref="`parser_${item.id}`" :form-conf="item.value" />
            </div>
          </template>
          <template v-else>
            <div class="empty-box">
              {{ $t('暂无表单定义数据') }}
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mtCollapse from '../../../../components/collapse/collapse.vue'
import mtPanel from '../../../../components/collapse/panel.vue'
import mtParser from '@mtech-form-design/form-parser'
import utils from '@/utils/utils.js'

// polyfill for scrollIntoView
import smoothscroll from 'smoothscroll-polyfill'
// kick off the polyfill!
smoothscroll.polyfill()

// 表单tab后面的数据
let FORMLISTDATA = new Map()

export default {
  components: {
    'mt-collapse': mtCollapse,
    'mt-panel': mtPanel,
    'mt-parser': mtParser
  },
  filters: {
    // 遍历二级数组下 status为10 待填写 的数量
    filterStatus10(dataArr) {
      let dataNum = 0
      dataArr.forEach((dItem) => {
        let filterData =
          !!dItem.buyerTaskList && dItem.buyerTaskList.filter((cv) => cv.status === 10).length
        dataNum = dataNum + filterData
      })
      return dataNum
    },
    filterStatus102(dataArr) {
      let dataNum = !!dataArr && dataArr.filter((cv) => cv.status === 10).length
      return dataNum
    },
    filterStatus(value) {
      let statusTxt = ''
      switch (value) {
        case 10:
          statusTxt = this.$t('待填写')
          break
        case 20:
          statusTxt = this.$t('待审批')
          break
        case 30:
          statusTxt = this.$t('已驳回')
          break
        case 40:
          statusTxt = this.$t('已完成')
          break
        case 50:
          statusTxt = this.$t('已关闭')
          break
        default:
          statusTxt = '--'
      }
      return statusTxt
    }
  },
  props: {
    id: {
      type: String || Number,
      default: ''
    },
    organizationId: {
      type: String || Number,
      default: ''
    }
  },
  data() {
    return {
      displayOperation: false, // 是否展示操作按钮 只有采方待办才行
      mapState: {
        10: this.$t('待填写'),
        20: this.$t('待审批'),
        30: this.$t('已驳回'),
        40: this.$t('已完成'),
        50: this.$t('已关闭')
      },
      collapseValue: '',
      activeIndexId: -1,
      activeCompanyId: -1,

      activeForm: {},
      // 全部的 待办数据
      fullTaskIndexArr: [this.$t('采方待办'), this.$t('供方待办'), this.$t('全部任务')],
      fullTaksArr: [[], [], []],
      // 表单的详情
      formInfo: {},

      // 我的待办
      pengingData: [],
      // 供方待办
      buyerTasks: [],
      // 全部待办
      historyData: [],
      // 任务详情
      taskDetail: {},

      // 任务详情
      buyerTaskInstance: {},
      //表单详情
      buyerFormInstanceList: {},
      //公司准入关系
      buyerPartnerFactoryRelationList: {},

      // 表单模板
      formTemplateArr: []
    }
  },
  created() {
    this.$loading()
    setTimeout(() => {
      this.$hloading()
    }, 1000)

    let id = this.id
    let organizationId = this.organizationId

    this.initTask(id, organizationId)
  },
  methods: {
    // 初始化公司任务
    initTask(id, organizationId = '') {
      // 采方待办任务查询
      const getPendingTasks = this.getPendingTasks(id, organizationId)
      // 供方待办任务查询
      const getBuyerTasks = this.getBuyerTasks(id, organizationId)
      // 全部任务查询
      const gethistory = this.gethistory(id, organizationId)

      Promise.all([getPendingTasks, getBuyerTasks, gethistory]).then((result) => {
        this.fullTaksArr = result
        this.pengingData = result[0]
        this.buyerTasks = result[1]
        this.historyData = result[2]

        // 获取默认展开项目 获取对应的详情

        let formInfo = {}
        let defaultCompanyId = -1
        let defaultId = -1
        let selectIndex = 0
        for (let index in result) {
          if (!utils.isEmpty(result[index]) && defaultId === -1) {
            for (let cItem of result[index]) {
              if (!utils.isEmpty(cItem.buyerTaskList)) {
                defaultCompanyId = cItem.orgId + '_' + index // 增加唯一性
                formInfo = cItem.buyerTaskList[0]
                defaultId = cItem.buyerTaskList[0].id
                selectIndex = index
                break
              } else {
                continue
              }
            }
          } else {
            continue
          }
        }
        // 默认展开
        if (!utils.isEmpty(formInfo)) {
          this.activeIndexId = formInfo.id
          this.activeCompanyId = defaultCompanyId

          this.selectTask(formInfo, selectIndex)
        } else {
          this.$hloading()
        }
      })
    },

    isEmpty(value) {
      return utils.isEmpty(value)
    },

    getPendingTasks(id, organizationId = '') {
      return new Promise((resolve) => {
        this.$API.supplierlifecycle
          .getPending({ partnerArchiveId: id, orgId: organizationId })
          .then((res) => {
            let { code, data } = res
            if (code === 200 && !utils.isEmpty(data)) {
              resolve(data)
            } else {
              resolve([])
            }
          })
          .catch(() => {
            resolve([])
          })
      })
    },

    getBuyerTasks(id, organizationId = '') {
      return new Promise((resolve) => {
        this.$API.supplierlifecycle
          .getBuyer({ partnerArchiveId: id, orgId: organizationId })
          .then((res) => {
            let { code, data } = res
            if (code === 200 && !utils.isEmpty(data)) {
              resolve(data)
            } else {
              resolve([])
            }
          })
          .catch(() => {
            resolve([])
          })
      })
    },

    gethistory(id, organizationId = '') {
      return new Promise((resolve) => {
        this.$API.supplierlifecycle
          .gethistory({ partnerArchiveId: id, orgId: organizationId })
          .then((res) => {
            let { code, data } = res
            if (code === 200 && !utils.isEmpty(data)) {
              resolve(data)
            } else {
              resolve([])
            }
          })
          .catch(() => {
            resolve([])
          })
      })
    },

    // 选择对应的调查表任务 任务id
    selectTask(formInfo, index) {
      // 是否展示操作按钮
      if (Number(index) === 0) {
        this.displayOperation = true
      } else {
        this.displayOperation = false
      }

      this.formInfo = formInfo
      let { id } = formInfo

      // 默认展开
      this.activeIndexId = formInfo.id

      // 重置掉表单
      this.formTemplateArr = []
      FORMLISTDATA.clear()

      this.$loading()
      // 兜底去除loading
      setTimeout(() => {
        this.$hloading()
      }, 1000)

      this.$API.supplierlifecycle
        .getTaskDetail({ taskInstanceId: id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            // 任务详情   ---           表单详情 ---            公司准入关系
            const { buyerTaskInstance, buyerFormInstanceList, buyerPartnerFactoryRelationList } =
              data

            this.buyerTaskInstance = buyerTaskInstance
            this.buyerFormInstanceList = buyerFormInstanceList
            this.buyerPartnerFactoryRelationList = buyerPartnerFactoryRelationList

            // 单个表单请求的逻辑 现在改成多个表单统一请求的逻辑
            // if (!!buyerFormInstanceList && buyerFormInstanceList.length > 0) {
            //   // 获取默认表单实例
            //   this.getDefaultFormInstance(buyerFormInstanceList[0])
            // }
          } else {
            this.$hloading()
            this.$toast({
              content: this.$t('获取任务详情失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('获取任务详情失败，请重试!'),
            type: 'warning'
          })
        })

      this.getAllFormDefine()
    },

    async getAllFormDefine() {
      let { id } = this.formInfo
      const formAllDetail = await this.queryAllFormData()
      if (FORMLISTDATA.has(id)) {
        // 存入表单数据
        this.$nextTick(() => {
          if (!utils.isEmpty(formAllDetail) && formAllDetail.length > 0) {
            formAllDetail.forEach((detail) => {
              if (!utils.isEmpty(detail) && !utils.isEmpty(detail.formData)) {
                this.$refs[`parser_${detail.formInstanceId}`][0].setFormData(detail.formData)
              }
            })
          }
        })
        this.$hloading()
        return
      }

      this.$API.supplierlifecycle.getAllFormDefine({ taskInstanceId: id }).then((res) => {
        let { code, data } = res
        if (code === 200 && !utils.isEmpty(data)) {
          // 非可编辑的表单 循环disabled
          if (data.length > 0) {
            data.forEach((tmpItem) => {
              if (
                !utils.isEmpty(tmpItem) &&
                !utils.isEmpty(tmpItem.formDefineResponse) &&
                !utils.isEmpty(tmpItem.formDefineResponse.template)
              ) {
                let template = this.disableTemplate(tmpItem.formDefineResponse.template)
                this.formTemplateArr.push({
                  id: tmpItem.id,
                  value: template,
                  name: tmpItem.formName
                })
              }
            })
            // 缓存当前表单结构数据
            FORMLISTDATA.set(id, this.formTemplateArr)

            this.$nextTick(() => {
              // 存入表单数据
              if (!utils.isEmpty(formAllDetail) && formAllDetail.length > 0) {
                formAllDetail.forEach((detail) => {
                  if (!utils.isEmpty(detail) && !utils.isEmpty(detail.formData)) {
                    this.$refs[`parser_${detail.formInstanceId}`][0].setFormData(detail.formData)
                  }
                })
              }
              this.$hloading()
            })
          }
        } else {
          this.$hloading()
          this.$toast({
            content: this.$t('获取表单定义模板失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    // 获取默认表单实例
    async getDefaultFormInstance(defaultForm) {
      this.$loading()
      // 当前表单id
      this.activeForm = defaultForm

      // 实时获取表单内部数据
      const formDetail = await this.queryFormDataDetail(defaultForm)

      if (FORMLISTDATA.has(this.activeForm.id)) {
        this.$nextTick(() => {
          // 存入表单数据
          if (!utils.isEmpty(formDetail) && !utils.isEmpty(formDetail.formData)) {
            this.$nextTick(() => {
              this.$refs[`parser_${defaultForm.id}`][0].setFormData(formDetail.formData)
            })
          }
          this.$hloading()
        })
        return
      }

      // 获取定义表结构
      this.$API.supplierlifecycle
        .getFormDefine({ formInstanceId: defaultForm.id })
        .then((res) => {
          let { code, data } = res
          if (
            code === 200 &&
            !utils.isEmpty(data) &&
            !utils.isEmpty(data.formDefineResponse) &&
            !utils.isEmpty(data.formDefineResponse.template)
          ) {
            // 非可编辑的表单 循环disabled
            let template = this.disableTemplate(data.formDefineResponse.template)

            this.formTemplateArr.push({
              id: this.activeForm.id,
              value: template
            })
            // 缓存当前表单结构数据
            FORMLISTDATA.set(this.activeForm.id, template)

            this.$nextTick(() => {
              // 存入表单数据
              if (!utils.isEmpty(formDetail) && !utils.isEmpty(formDetail.formData)) {
                this.$refs[`parser_${this.activeForm.id}`][0].setFormData(formDetail.formData)
              }
              this.$hloading()
            })
          } else {
            this.$hloading()
            this.$toast({
              content: this.$t('获取表单定义模板失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('获取表单定义模板失败，请重试!'),
            type: 'warning'
          })
        })
    },

    /**
     * (formInfo.status === 10 || formInfo.status === 30) && displayOperation
     * disable不能操作的表单结构
     */
    disableTemplate(template) {
      if ((this.formInfo.status === 10 || this.formInfo.status === 30) && this.displayOperation) {
        return template
      } else {
        !!template.fields &&
          template.fields.forEach((tItem) => {
            tItem.disabled = true
          })
        return template
      }
    },

    // 获取表单生成器数据
    queryFormDataDetail(defaultForm) {
      return this.$API.supplierlifecycle
        .queryFormData({ formInstanceId: defaultForm.id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            return data
          } else {
            return {}
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('获取表单定义模板失败，请重试!'),
            type: 'warning'
          })
          this.$hloading()
        })
    },

    // 获取all 表单生成器数据
    queryAllFormData() {
      let { id } = this.formInfo
      return this.$API.supplierlifecycle
        .queryAllFormData({ taskInstanceId: id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            return data
          } else {
            return {}
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'warning'
          })
          this.$hloading()
        })
    },

    // 表单滚动
    scrollInto(id) {
      this.$refs['formItem_' + id][0].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    },

    // 选择form表单栏目
    selectFromItem(item) {
      this.getDefaultFormInstance(item)
    },

    // 保存的接口请求
    saveTaskAjax() {
      // 校验表单的promise
      let promiseValidataArr = []
      for (let va of this.formTemplateArr) {
        let parseName = `parser_${va.id}`
        promiseValidataArr.push(this.$refs[parseName][0].valiteFormData())
      }
      return Promise.all(promiseValidataArr)
        .then(() => {
          let formDataArr = []
          for (let va of this.formTemplateArr) {
            let parseName = `parser_${va.id}`
            formDataArr.push({
              formData: this.$refs[parseName][0].getFormData(),
              formInstanceId: va.id
            })
          }

          return this.$API.supplierlifecycle.saveFormData({
            buyerFormDataRequestList: formDataArr,
            taskInstanceId: this.formInfo.id
          })
        })
        .catch((reason) => {
          return Promise.resolve({
            msg: reason.msg || this.$t('表单验证不通过，请查看表单是否有漏填!')
          })
        })
    },

    // 保存任务
    async saveTask() {
      this.$loading()
      const ajaxSaveTask = await this.saveTaskAjax()
      this.$hloading()

      let { code } = ajaxSaveTask
      if (code === 200) {
        this.$toast({
          content: ajaxSaveTask.msg || this.$t('保存成功!'),
          type: 'success'
        })
      } else {
        this.$toast({
          content: ajaxSaveTask.msg || this.$t('获取表单定义模板数据，请重试!'),
          type: 'warning'
        })
      }
    },

    // 预提交
    proSubmitTask() {
      console.log(123)
      if (this.formInfo.status === 10) {
        // 正常提交
        this.submitTask()
      }
      if (this.formInfo.status === 30) {
        // 驳回的重新提交 要弹框填 重新提交原因
        this.$dialog({
          modal: () => import('./addResubmitReason.vue'),
          data: {
            title: this.$t('重新提交'),
            rejectReason: this.supplierTaskInstance.auditRemark
          },
          success: (data) => {
            let submitReason = data
            this.submitTask(submitReason)
          }
        })
      }
    },

    // 提交任务
    async submitTask(submitReason = '') {
      this.$loading()
      setTimeout(() => {
        this.$hloading()
      }, 2000)
      const ajaxSaveTask = await this.saveTaskAjax()

      let { code } = ajaxSaveTask
      if (code === 200) {
        let query = {
          taskInstanceId: this.formInfo.id
        }
        if (this.formInfo.status === 30) {
          query.submitReason = submitReason
        }
        // 提交接口
        this.$API.supplierlifecycle.submitTask(query).then((res) => {
          this.$hloading()
          let { code } = res
          if (code === 200) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            setTimeout(() => {
              this.$router.go(-1)
            }, 600)
          } else {
            this.$toast({
              content: res.msg || this.$t('提交失败，请重试!'),
              type: 'warning'
            })
          }
        })
      } else {
        this.$hloading()
        this.$toast({
          content: ajaxSaveTask.msg || this.$t('获取表单定义模板数据，请重试!'),
          type: 'warning'
        })
      }
    },

    // 通过任务 不搞了
    aggressiveTask() {},

    // 拒绝 不搞了
    refuseTask() {}
  },
  destroyed() {
    this.$hloading()
    FORMLISTDATA.clear()
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../node_modules/@mtech-form-design/form-parser/build/esm/bundle.css';
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.task-center {
  margin-top: 20px;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  border-right: 2px solid rgba(232, 232, 232, 1);
  // overflow: hidden;

  .task-sidebar {
    width: 240px;
    height: 100%;
    background: #fff;
  }

  .task-collapse {
    .collapse-header {
      height: 60px;
      line-height: 60px;
      padding-left: 30px;
      font-size: 16px;

      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      border-top: 1px solid rgba(232, 232, 232, 1);

      -moz-user-select: none;
      -ms-user-select: none;
      -webkit-user-select: none;
      user-select: none;
    }

    .collapse-header:nth-child(1) {
      border-top: none;
    }

    .active {
      color: rgba(0, 70, 156, 1);
      // border-bottom: 1px solid rgba(232,232,232,1);
    }
  }

  .task-content {
    border-left: 1px solid rgba(232, 232, 232, 1);
    display: flex;
    flex-direction: column;

    .task-info {
      padding: 24px 30px 24px 20px;
      background: transparent;
      justify-content: space-between;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      .mian-info {
        .task-list {
          .reject-reason {
            color: #eda133;
          }
          flex-wrap: wrap;
          span {
            margin-right: 30px;
            font-size: 12px;

            font-weight: normal;
            color: rgba(100, 100, 100, 1);
          }
        }

        .categroy-list {
          margin-top: 14px;

          .factory-item {
            margin-right: 30px;
            .factory-name {
              height: 20px;
              line-height: 20px;
              font-size: 12px;

              font-weight: 500;
              color: rgba(41, 41, 41, 1);
            }
            .factory-category {
              margin-left: 10px;
              height: 20px;
              line-height: 20px;
              padding: 0 4px;
              background: rgba(99, 134, 193, 0.1);
              border-radius: 2px;
              font-size: 12px;

              font-weight: 500;
              color: rgba(99, 134, 193, 1);
            }
          }
        }
      }
      .btn-box {
        white-space: nowrap;
        // width: calc( 48px + 48px + 30px);
        .normal-btn {
          padding: 0 10px;
          font-size: 14px;

          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
        .normal-btn:nth-child(1) {
          margin-right: 30px;
        }

        .err-tips {
          display: flex;
          i {
            position: relative;
            top: 3px;
            margin-right: 4px;
          }
        }
      }
    }

    .task-form {
      flex: 1;
      .side-bar {
        width: 160px;
        border-right: 1px solid rgba(232, 232, 232, 1);
        .side-item {
          width: 100%;
          height: 50px;
          line-height: 50px;
          font-size: 14px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          padding-left: 20px;

          cursor: pointer;
          -moz-user-select: none;
          -ms-user-select: none;
          -webkit-user-select: none;
          user-select: none;
        }
        .active {
          color: #00469c;
          background: #f5f6f9;
        }
      }

      .form-content {
        padding: 30px 20px;
        overflow: auto;
        -webkit-overflow-scrolling: auto;
        transform: rotate(1);

        .empty-box {
          margin: 0 auto;
          margin-top: 40px;
          font-size: 16px;
          color: #333;
          text-align: center;
        }

        .none {
          display: none;
        }
        .display-item {
          display: block;

          .parse-title {
            color: #292929;
            position: relative;
            display: flex;
            padding-left: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;

            &::before {
              content: ' ';
              display: inline-block;
              position: absolute;
              width: 2px;
              height: 14px;
              background: #00469c;
              left: 0;
            }
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.task-center {
  .mt-tooptip {
    margin-right: 40px;
    line-height: 16px;
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .ivu-collapse {
    border: none !important;

    // normal
    .ivu-collapse-header {
      height: 50px;
      line-height: 50px;
      // header
      .accordion-header {
        width: 140px;
        font-size: 14px;

        color: rgba(41, 41, 41, 1);
        display: flex;

        .company-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .header-number {
          // width: ;
          color: #00469c;
        }
      }
    }

    .ivu-collapse-content {
      // children
      .sub-item {
        height: 50px;
        line-height: 50px;
        padding-left: 50px;
        background: #fff;
        font-size: 14px;
        border-bottom: 1px solid #e8e8e8;
        font-weight: 500;
        color: #292929;
        display: flex;
        align-items: center;
        cursor: pointer;

        .task-name {
          width: 130px;
        }

        .sub-tips {
          display: inline-block;
          padding: 0 4px;
          height: 20px;
          line-height: 20px;
          background: rgba(237, 161, 51, 0.1);
          border-radius: 2px;
          color: #eda133;
        }
      }

      .sub-item:last-child {
        border-bottom: none;
      }

      .active {
        background: rgba(245, 246, 249, 1);
        color: rgba(0, 70, 156, 1);
      }

      .grey {
        color: rgba(154, 154, 154, 1);
        .sub-tips {
          display: inline-block;
          padding: 0 4px;
          height: 20px;
          line-height: 20px;
          background: rgba(154, 154, 154, 0.1);
          border-radius: 2px;
          color: #9a9a9a;
        }
      }
    }

    // active
    .ivu-collapse-item-active {
      .header-wrap {
        // color: rgba(0,70,156,1);
      }
    }
  }
  .ivu-collapse > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header {
    background: rgba(245, 246, 249, 1);
    // border-bottom: none!important;
  }
  .ivu-collapse > .ivu-collapse-item > .ivu-collapse-header {
    padding-left: 30px;
  }
  .ivu-collapse-content {
    padding: 0;
  }
}
</style>
