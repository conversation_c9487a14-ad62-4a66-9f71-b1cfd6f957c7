<template>
  <div class="route-line fbox">
    <div
      class="route-item flex1"
      :class="{ 'up-item': index % 2 === 0, 'down-item': index % 2 !== 0 }"
      v-for="(item, index) of PartnerEventList"
      :key="item.eventName"
    >
      <div class="time">{{ item.filterTime }}</div>
      <div class="dotted"></div>
      <div class="route-txt">{{ item.eventName }}</div>
    </div>
  </div>
</template>

<script>
export default {
  // watch: {
  //   PartnerEventList(nv) {
  //     console.log(nv)
  //   }
  // },
  props: {
    partnerEventList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  filters: {
    filterTime: function (value) {
      return value
    }
  }
}
</script>

<style lang="scss" scoped>
.route-line {
  width: 100%;
  height: 126px;
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
  margin-top: 26px;

  &::before {
    content: ' ';
    display: inline-block;
    width: 100%;
    height: 1px;
    background: #e8e8e8;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  .route-item {
    position: relative;
    height: 100%;

    .route-txt {
      display: inline-block;
      height: 16px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #292929;
    }

    .dotted {
      width: 30px;
      height: 14px;
      background: #fff;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate3d(-50%, -50%, 0);

      &::before {
        position: absolute;
        left: 10px;
        top: 2px;
        content: ' ';
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #9baac1;
      }
    }
  }

  .down-item {
    &::before {
      content: ' ';
      display: block;
      width: 24px;
      height: 30px;
      margin: 0 auto;
      background: url(../../../../assets/resources/<EMAIL>) center no-repeat;
      background-size: 24px 30px;
      margin-top: 70px;
      transform: rotate(180deg);
    }

    .time {
      display: inline-block;
      font-size: 10px;
      height: 10px;
      line-height: 10px;
      color: #9a9a9a;
      text-align: center;
      position: absolute;
      left: 50%;
      top: 50px;
      transform: translate3d(-50%, -50%, 0);
    }

    .route-txt {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .up-item {
    .route-txt {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    .time {
      display: inline-block;
      font-size: 10px;
      height: 10px;
      line-height: 10px;
      color: #9a9a9a;
      text-align: center;
      position: absolute;
      left: 50%;
      top: 74px;
      transform: translate3d(-50%, -50%, 0);
    }

    &::before {
      content: ' ';
      display: block;
      width: 24px;
      height: 30px;
      margin: 0 auto;
      background: url(../../../../assets/resources/<EMAIL>) center no-repeat;
      background-size: 24px 30px;
      margin-top: 25px;
    }
  }
}
</style>
