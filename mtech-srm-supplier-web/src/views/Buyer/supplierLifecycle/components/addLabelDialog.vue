<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="beforeClose"
  >
    <div class="dialog-content addsupplier-dialog">
      <div class="form-box fbox">
        <template v-if="method === 'my'">
          <div class="cp-tips fbox flex1">
            <template v-for="item in myLabelListCopy">
              <div
                class="tip-item"
                :key="item.labelId"
                :title="item.labelName"
                :alter="item.labelName"
              >
                <div class="txt-wrap">{{ item.labelName }}</div>
                <div class="close-btn" v-if="item.del === 1" @click="deleteLabel(item)">
                  <i class="mt-icons mt-icon-icon_input_clear"></i>
                </div>
              </div>
            </template>
          </div>
        </template>
        <template v-else>
          <div class="checkbox-wrap" v-for="item in renderLabelList" :key="item.id">
            <mt-checkbox
              class="checkbox-item"
              :checked="item.isChecked"
              :label="item.labelName"
              :disabled="item.disabled"
              :fields="{ label: 'labelName', id: 'id' }"
              @change="handleChange(item)"
            ></mt-checkbox>
          </div>
        </template>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.submitForm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],

      checkboxVal: true,
      labelValueList: [],
      renderLabelList: [], // this.$t("展示型的")label list
      myLabelListCopy: []
    }
  },

  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return !!this.modalData.method && this.modalData.method === 'my'
        ? this.$t('我的标签')
        : this.modalData.title
    },
    labelList() {
      return this.modalData.labelList || []
    },
    myLabelList() {
      return this.modalData.myLabelList || []
    },
    organizationId() {
      return this.modalData.organizationId
    },
    supplierEnterpriseId() {
      return this.modalData.supplierEnterpriseId
    },
    method() {
      return this.modalData.method
    }
  },
  async created() {},
  mounted() {
    !this.method && this.renderLabelListFun()
    !!this.method && this.method === 'my' && this.renderBtn()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    renderBtn() {
      this.myLabelListCopy = this.myLabelList
    },
    renderLabelListFun() {
      let renderLabelList = []
      renderLabelList = this.labelList.map((v) => {
        let returnValue = {}
        let joinData = this.myLabelList.filter((cv) => cv.labelCode === v.labelCode)
        if (joinData.length > 0) {
          returnValue = {
            ...v,
            disabled: joinData[0].del === 0, // 已勾选 && 不能删除的标签 == 置灰
            isChecked: true
          }
        } else {
          returnValue = {
            ...v,
            disabled: false,
            isChecked: false
          }
        }

        return returnValue
      })

      this.renderLabelList = renderLabelList
    },

    // 删除标签
    deleteLabel(item) {
      let { labelCode } = item
      let labelIdList = [item.labelId]
      this.$API.supplierlifecycle
        .deleteLabel({
          orgId: this.organizationId,
          labelIdList: labelIdList,
          supplierEnterpriseId: this.supplierEnterpriseId
        })
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 删除数据
            let index = this.myLabelListCopy.findIndex((v) => v.labelCode === labelCode)
            this.myLabelListCopy.splice(index, 1)

            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
          } else {
            this.$toast({
              content: this.$t('删除失败，请重试！'),
              type: 'warning'
            })
          }
        })
    },

    handleChange(item) {
      let renderLabelList = this.renderLabelList
      let index = renderLabelList.findIndex((v) => v.labelCode === item.labelCode)
      this.$set(this.renderLabelList, index, {
        ...this.renderLabelList[index],
        isChecked: !this.renderLabelList[index].isChecked
      })
    },

    // 更新
    updateLabel() {
      let labelListDTO = []
      this.renderLabelList.forEach((v) => {
        if (v.isChecked && this.labelList.filter((cv) => cv.labelCode === v.labelCode).length > 0) {
          labelListDTO.push({
            labelCode: v.labelCode,
            labelId: v.id,
            labelName: v.labelName,
            labelType: v.labelType
          })
        }
      })
      let query = {
        orgId: this.organizationId,
        labelListDTO,
        remark: '',
        supplierEnterpriseId: this.supplierEnterpriseId
      }

      this.$API.supplierlifecycle.updateLabel(query).then((res) => {
        console.log(res)
        if (res.code === 200 && !!res.data) {
          console.log(res)
          this.$toast({
            content: this.$t('更新成功!'),
            type: 'success'
          })
          setTimeout(() => {
            this.beforeClose()
            this.$emit('confirm-function', { updata: true })
          }, 400)
        } else {
          this.$toast({
            content: this.$t('更新失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    beforeClose() {
      this.$emit('cancel-function')
    },

    submitForm() {
      if (!!this.method && this.method === 'my') {
        this.beforeClose()
        this.$emit('confirm-function', { updata: true })
        return
      }
      this.updateLabel()
    },

    cancel() {
      if (!!this.method && this.method === 'my') {
        this.beforeClose()
        this.$emit('confirm-function', { updata: true })
        return
      }
      this.$emit('cancel-function')
    },

    handleSelectTab(index) {
      this.selectIndex = index
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 820px;
  margin: 0 auto;
  padding-top: 40px;
  padding-bottom: 20px;
  font-size: 16px;

  .cp-tips {
    flex-wrap: wrap;
    .tip-item {
      height: 30px;
      line-height: 30px;
      padding: 0 10px;
      font-size: 16px;
      font-weight: 500;
      color: rgba(51, 166, 23, 1);
      background: rgba(51, 166, 23, 0.12);
      border-radius: 2px;
      margin-right: 10px;
      position: relative;
      user-select: none;
      margin-bottom: 10px;
      max-width: 100px;

      .txt-wrap {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .close-btn {
        display: none;
      }

      &:hover .close-btn {
        display: block;
        width: 12px;
        height: 12px;
        font-size: 12px;
        position: absolute;
        right: -6px;
        top: -6px;
        color: #9baac1;
        line-height: 12px;
        cursor: pointer;
      }

      i.mt-icon-plus {
        line-height: 30px;
        font-size: 14px;
        cursor: pointer;
      }
    }

    .edit-tips {
      line-height: 30px;
      font-size: 12px;
      color: rgba(0, 70, 156, 1);
      cursor: pointer;
    }
  }
}
</style>
<style lang="scss">
.addsupplier-dialog {
  .form-box {
    flex-wrap: wrap;
  }

  .checkbox-wrap {
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 2px;
    margin-bottom: 20px;
    margin-right: 20px;
    display: flex;
    align-items: center;
  }
}
</style>
