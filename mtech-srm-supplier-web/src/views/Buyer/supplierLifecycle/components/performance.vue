<template>
  <div class="performance-box" id="main"></div>
</template>

<script>
import * as echarts from 'echarts'
let chartDom = null
let myChart = null
export default {
  props: {
    performanceInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  watch: {
    performanceInfo: {
      handler() {
        this.$nextTick(() => {
          this.initChart()
        })
      },
      deep: true
    }
  },
  mounted() {
    chartDom = document.getElementById('main')
    myChart = echarts.init(chartDom)
  },
  methods: {
    initChart() {
      let option

      option = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            areaStyle: {}
          }
        ]
      }

      option && myChart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.performance-box {
}
</style>
