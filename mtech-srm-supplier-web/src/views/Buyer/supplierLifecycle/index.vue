<template>
  <div class="lifeCycle-container">
    <mt-template-page
      :padding-top="true"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { invitationToolbar, columnDataMain } from './config/index.js'
export default {
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$hloading()
    })
  },
  data() {
    return {
      componentConfig: [
        {
          gridId: '16b397af-4c53-4bfb-aa42-9a0fc89e032d',
          toolbar: invitationToolbar,
          grid: {
            columnData: columnDataMain,
            asyncConfig: {
              url: '/supplier/tenant/buyer/process/perspective/query'
            }
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    handleClickToolBar() {},
    handleClickCellTool() {},
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'lifecycledetail2',
        query: {
          id: data.id,
          supplierEnterpriseId: data.supplierEnterpriseId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lifeCycle-container {
  height: 100%;
}
</style>
