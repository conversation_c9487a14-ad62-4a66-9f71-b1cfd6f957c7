import { i18n } from '@/main.js'
export const invitationToolbar = [[], ['Filter', 'Export', 'Refresh', 'Setting']]

export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '210',
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    cellTools: []
  },
  {
    width: '130',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商企业名称')
  },
  {
    width: '210',
    field: 'establishTime',
    headerText: i18n.t('成立时间'),
    queryType: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '210',
    field: 'platformRegisterTime',
    headerText: i18n.t('平台注册时间'),
    queryType: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '160',
    field: 'legalPerson',
    headerText: i18n.t('法人代表')
  },
  {
    width: '160',
    field: 'serviceCount',
    headerText: i18n.t('服务公司个数')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('第一次准入时间'),
    queryType: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '160',
    field: 'address',
    headerText: i18n.t('注册地址')
  }
]

const columnData = [
  {
    width: 120,
    field: 'dimensionName',
    headerText: i18n.t('名称')
  },
  {
    field: 'rate',
    headerText: i18n.t('分配权重（%）')
  },
  {
    field: 'dimensionFullScore',
    headerText: i18n.t('指标满分')
  },
  {
    field: 'dimensionScore',
    headerText: i18n.t('分配分值')
  }
]

export const pageConfig = [
  {
    gridId: 'efcbeac0-3c45-4480-b8c2-71a7f53a59bd',
    useToolTemplate: false,
    treeGrid: {
      allowPaging: false,
      columnData,
      childMapping: 'targetScores',
      dataSource: []
    }
  }
]
