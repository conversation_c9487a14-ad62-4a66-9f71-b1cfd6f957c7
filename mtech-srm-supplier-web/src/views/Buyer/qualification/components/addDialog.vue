<template>
  <!-- 资质项自定义 -->
  <mt-dialog ref="thresholdItemDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('审查单名称')"
          label-style="top"
          prop="applyName"
        >
          <mt-input
            v-model="formInfo.applyName"
            :placeholder="$t('请输入审查单名称')"
            type="text"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('公司')" label-style="top" prop="organizationId">
          <!-- <mt-DropDownTree
            id="DropDownTree"
            :placeholder="$t('请选择公司')"
            v-model="formInfo.organizationId"
            :fields="fields"
            :key="fields.key"
            @input="organizationIdChange"
            v-if="!projectCode"
          ></mt-DropDownTree> -->

          <RemoteAutocomplete
            v-if="!projectCode"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            v-model="formInfo.organizationCode"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            @change="orgChange"
            :title-switch="false"
            :disabled="editorIdstatus"
            :width="414"
            :placeholder="$t('请选择')"
            select-type="administrativeCompany"
          ></RemoteAutocomplete>
          <mt-input
            v-model="formInfo.organizationName"
            v-if="projectCode"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('任务类型')" label-style="top" prop="bizType">
          <mt-select
            ref="bizTypeRef"
            v-model="formInfo.bizType"
            :data-source="bizTypeDataSource"
            :placeholder="$t('请选择任务类型')"
            @change="bizTypeChange"
            v-if="!projectCode"
          ></mt-select>
          <mt-select
            v-model="formInfo.bizType"
            :data-source="bizTypeDataSource"
            :placeholder="$t('请选择任务类型')"
            @change="bizTypeChange"
            v-if="projectCode"
            :disabled="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('品类')" label-style="top" prop="categoryName">
          <mt-select
            v-model="formInfo.categoryName"
            :data-source="categoryNameDataSource"
            :placeholder="$t('请选择品类')"
            :allow-filtering="true"
            :filtering="filteringResource"
            :fields="{ text: 'categoryName', value: 'categoryName' }"
            @change="categoryNameChange"
            v-if="!projectCode"
          ></mt-select>
          <mt-input v-model="formInfo.categoryName" v-if="projectCode" :disabled="true"> </mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="formInfo.bizType ? $t('模板类型') : $t('资质模板')"
          label-style="top"
          prop="qualificationTemplateTypeCode"
        >
          <mt-select
            v-if="formInfo.bizType && !projectCode"
            ref="thresholdFieldListRef"
            v-model="formInfo.qualificationTemplateTypeCode"
            :data-source="thresholdFieldList"
            :placeholder="$t('请选择模板类型')"
            :fields="{ text: 'qualificationTemplateTypeName', value: 'qualificationTemplateCode' }"
            @change="qualificationTemplateTypeCodeChange"
            @open="openSelect"
          ></mt-select>
          <mt-select
            v-if="!formInfo.bizType && !projectCode"
            ref="thresholdFieldListRefTwo"
            v-model="formInfo.qualificationTemplateTypeCode"
            :data-source="thresholdFieldListTwo"
            :placeholder="$t('请选择资质模板')"
            :fields="{ text: 'qualificationTemplateName', value: 'qualificationTemplateCode' }"
            @change="qualificationTemplateTypeCodeChange"
            @open="openSelect"
          ></mt-select>
          <mt-input
            v-model="formInfo.qualificationTemplateTypeName"
            v-if="projectCode"
            :disabled="true"
          >
          </mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('选择供应商')"
          label-style="top"
          prop="supplierName"
        >
          <mt-multi-select
            class="f-1"
            :disabled="false"
            v-model="formInfo.supplierName"
            :data-source="symbolSource"
            :show-clear-button="true"
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierInternalCode' }"
            @change="supplierSelectChange"
            @open="supplierSelectOpen"
          ></mt-multi-select>
        </mt-form-item>
        <div class="demo-block">
          <mt-DataGrid
            :data-source="dialogGridDataSource"
            :column-data="dialogGridColumnData"
            :height="180"
            ref="dataGrid"
            :progress-val="progressVal"
            @progressOpen="progressOpen"
            @progressChange="progressChange"
            @rowSelected="getSelectedRecords"
          ></mt-DataGrid>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import utils from '@/utils/utils'
import { dialogGridDataSource, dialogGridColumnData } from '../config/addDialog'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  data() {
    return {
      dialogGridDataSource,
      dialogGridColumnData,
      sourceLabel: this.$t('选择资质'),
      // organizationId: [],
      organizationId: '',
      // ======内容数据源======
      fields: {
        //公司
        dataSource: [],
        value: 'id',
        id: 'id',
        text: 'name',
        child: 'children',
        key: 1
      },
      bizTypeDataSource: [
        //任务类型
        {
          text: this.$t('品类认证'),
          value: 1
        },
        {
          text: this.$t('其它'),
          value: 0
        }
      ],
      categoryNameDataSource: [], //任务品类
      symbolSource: [], //供应商
      thresholdFieldList: [], //模板类型
      thresholdFieldListTwo: [], //资质模板
      qualificationTemplateTypeCodeVal: '', //资质模板==监听
      //=====表单属性=====
      formInfo: {
        applyName: '', //名称
        organizationId: '', //公司ID
        organizationCode: '', //公司编码
        organizationName: '', //公司名称
        categoryId: '', //一级类目ID
        categoryCode: '', //一级类目编码
        categoryName: '', //品类名称
        bizType: 1, //任务类型，0-其他，1-品类认证
        qualificationTemplateTypeCode: '', //资质模板类型编码
        supplierName: '', //供应商名称
        supplierList: [] //供应商列表
      },
      formInfoBack: {
        organizationId: '', //公司ID
        organizationCode: '', //公司编码
        organizationName: '' //公司名称
      },
      templateTypeParams: {
        //资质模板==字段单
        categoryCode: '', //类目编码
        organizationCode: '', //公司编码
        qualificationTemplateCode: '', //资质模板编码
        qualificationTemplateName: '', //资质模板名称
        qualificationTemplateTypeCode: '', //资质模板类型编码
        sceneCode: '', //场景编码
        status: '1' //状态 固定值:1为启用
      },
      rules: {
        //校验规则==表单
        applyName: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
        organizationId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        bizType: [{ required: true, message: this.$t('请选择任务类型'), trigger: 'blur' }],
        categoryName: [{ required: true, message: this.$t('请选择品类'), trigger: 'blur' }],
        qualificationTemplateTypeCode: [
          { required: true, message: this.$t('请选择模板'), trigger: 'blur' }
        ],
        supplierName: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }]
      },
      formTypeList: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      // dataCode: [],
      dataArr: [], //引用场景传递, props无法传递==待解决
      rowProgress: {},
      progressVal: ''
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    formInfoOrganization() {
      //选择供应商监听
      return this.formInfo.supplierName
    },
    projectCode() {
      return this.modalData.projectCode
    },
    taskCode() {
      return this.modalData.taskCode
    }
  },
  watch: {
    async formInfoOrganization(newVal) {
      //选择供应商监听
      let dataS = newVal
        ? this.symbolSource.filter((i) => {
            return newVal.includes(i.supplierInternalCode)
          })
        : []
      dataS.forEach((i) => {
        i['qualificationTemplateCode'] = this.formInfo.qualificationTemplateCode
        i['progressShow'] = !this.formInfo.bizType && dataS ? false : true
      })
      this.dialogGridDataSource = dataS
    }
  },
  mounted() {
    if (this.projectCode) {
      this.formInfo.organizationId = this.info.orgId
      this.formInfo.organizationCode = this.info.orgCode
      this.formInfo.organizationName = this.info.orgName
      this.formInfo.categoryId = this.info.categoryId
      this.formInfo.categoryCode = this.info.categoryCode
      this.formInfo.categoryName = this.info.categoryName
      this.formInfo.authProjectCode = this.info.projectCode
      this.formInfo.authProjectId = this.info.projectId
      this.formInfo.authProjectName = this.info.projectName
      if (this.taskCode == 'investigationTempType') {
        this.formInfo.qualificationTemplateTypeName = this.$t('资质审查')
        this.formInfo.qualificationTemplateTypeCode = 'investigation'
      } else {
        this.formInfo.qualificationTemplateTypeName = this.$t('签署协议')
        this.formInfo.qualificationTemplateTypeCode = 'sign'
      }
      this.getCateSupplierList()
    } else {
      this.initData()
    }
    this.show()
    this.filteringResource = utils.debounce(this.filteringResource, 300)
  },
  methods: {
    // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
    handleCompanyData(resData = []) {
      resData = resData.filter((item) => {
        return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      })
      return resData
    },
    // 品类认证页面获取供应商列表
    getCateSupplierList() {
      this.$API.CategoryCertification.getSupListByAuthCode({
        authProjectCode: this.projectCode
      }).then((res) => {
        if (res.code == 200 && !utils.isEmpty(res.data)) {
          this.symbolSource = res.data
        } else {
          this.symbolSource = []
        }
      })
    },
    // =========【接口】============
    async filteringResource(e) {
      await this.getCategoryListInterface(e.text)
      e.updateData(this.categoryNameDataSource)
      this.$forceUpdate()
    },
    async initData() {
      //初始化接口
      await this.getCategoryListInterface('') //品类
      // await this.getStatedLimitTree(); //公司
    },
    //品类==接口
    async getCategoryListInterface(val) {
      await this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'statusId',
              type: 'int',
              operator: 'equal',
              value: 1
            },
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          this.categoryNameDataSource = res.data.records
        })
    },
    //模板类型==接口
    qualificationTemplateConditionQueryInterface(params) {
      let pam = params || {}
      this.$API.ModuleConfig.qualificationTemplateConditionQuery(pam).then((res) => {
        //模板类型
        if (res.code == 200 && res.data) {
          let newData = res.data
          var hash = {}
          newData = newData.reduce(function (item, i) {
            if (!hash[i.qualificationTemplateTypeCode]) {
              hash[i.qualificationTemplateTypeCode] = true
              item.push(i)
            }
            return item
          }, [])
          this.thresholdFieldList = newData //模板管理
          this.thresholdFieldListTwo = res.data //资质模板
          this.$forceUpdate()
          // 资质模板编码...
        }
      })
    },
    //模板类型==接口(引用场景逻辑)
    async qualificationTemplateConditionQueryInterfaceReferToScene() {
      let params = this.templateTypeParams || {}
      let dataArrScene = () => {
        this.dialogGridDataSource.forEach((i) => {
          i['dataArr'] = this.dataArr
        })
        this.dialogGridDataSource = JSON.parse(JSON.stringify(this.dialogGridDataSource))
      }
      await this.$API.ModuleConfig.qualificationTemplateConditionQuery(params)
        .then((res) => {
          //模板类型
          if (res.code == 200 && res.data) {
            let newData = res.data.filter((i) => {
              if (this.qualificationTemplateTypeCodeVal) {
                return i.qualificationTemplateTypeCode == this.qualificationTemplateTypeCodeVal
              } else {
                return i.qualificationTemplateTypeCode
              }
            })
            this.dataArr = []
            newData.map((ic) => {
              ic.sceneInfoList.map((i) => {
                this.dataArr.push(i)
              })
            })
          }
          dataArrScene()
        })
        .catch(() => {
          dataArrScene()
        })
    },
    //供应商==接口
    getOrgPartnerRelationsByStatusInterface(params) {
      this.$API.qualification.getOrgPartnerRelationsByStatus(params).then((res) => {
        if (res.code === 200) {
          this.symbolSource = res.data
          this.symbolSource.map((i) => {
            //引入场景中使用
            // i["dataArr"] = this.dataArr;
            i['progressShow'] = this.formInfo.bizType == 1 ? true : false //任务类型==1.其它 0.品类认证
            i['qualificationTemplateCode'] = ''
          })
        }
      })
    },

    // definelistQueryInterface(){
    //   this.$API.ModuleConfig.definelistQuery().then(res=>{ //引入场景
    //     if(res.code == 200){
    //       this.dataArr = res.data;
    //     }
    //   })
    // },
    // 获取组织树==公司接口
    // async getStatedLimitTree() {
    //   let query = "ORG02";
    //   await this.$API.ModuleConfig.getStatedLimitTree({
    //     orgLevelCode: query,
    //     orgType: "ORG001PRO",
    //   }).then((res) => {
    //     let filterObj = function (item) {
    //       if (item.orgLeveLTypeCode == query) return true;
    //       if (Object.prototype.hasOwnProperty.call(item, "children")) {
    //         item.children = item.children.filter(function (child) {
    //           if (child.orgLeveLTypeCode == query) {
    //             return child.orgLeveLTypeCode == query;
    //           } else if (
    //             Object.prototype.hasOwnProperty.call(child, "children")
    //           ) {
    //             return filterObj(child);
    //           }
    //         });
    //         if (item.children.length > 0) {
    //           return true;
    //         }
    //       } else {
    //         return item.orgLeveLTypeCode == query;
    //       }
    //     };
    //     let filter = res.data.filter(function (item) {
    //       return filterObj(item);
    //     });

    //     this.fields.dataSource = cloneDeep(filter);
    //     this.fields.key = this.randomString();
    //   });
    // },
    // randomString(len) {
    //   len = len || 32;
    //   var $chars =
    //     "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678"; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
    //   var maxPos = $chars.length;
    //   var pwd = "";
    //   for (var i = 0; i < len; i++) {
    //     pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    //   }
    //   return pwd;
    // },

    // fn(data, arr) {
    //   //递归监听
    //   if (data)
    //     data.forEach((ele) => {
    //       if (arr.indexOf(ele.id) != -1) {
    //         this.formInfo["organizationCode"] = ele.orgCode;
    //         this.formInfo["organizationName"] = ele.name;
    //         this.formInfo.supplierList.push({
    //           organizationCode: ele.orgCode,
    //           organizationId: ele.id,
    //           organizationName: ele.name,
    //         });
    //       }
    //       if (ele.children && ele.children.length > 0) {
    //         this.fn(ele.children, arr);
    //       }
    //     });
    // },
    // 清空数据==通用
    clearData() {
      this.formInfo.qualificationTemplateTypeCode = ''
      this.thresholdFieldList = []
      this.thresholdFieldListTwo = []
      this.formInfo.supplierName = ''
      this.dialogGridDataSource = []
      this.progressVal = ''
      this.templateTypeParams['sceneCode'] = ''
    },
    // =========【内容改变时==选择监听】===========
    // 公司==选择监听
    // async organizationIdChange(e) {
    //   this.formInfo.supplierList = [];
    //   this.clearData(); //清空通用数据
    //   this.fn(this.fields.dataSource, e);
    //   this.formInfoBack["organizationCode"] = this.formInfo["organizationCode"];
    //   this.formInfoBack["organizationName"] = this.formInfo["organizationName"];
    //   this.formInfoBack["organizationId"] = this.formInfo.organizationId[0];
    //   let aParams = { orgId: e[0], status: [1, 2, 10, 20, 30] };
    //   this.templateTypeParams["organizationCode"] = e[0]
    //     ? this.formInfo.supplierList[0].organizationCode
    //     : "";
    //   if (e[0]) {
    //     this.getOrgPartnerRelationsByStatusInterface(aParams); //供应商
    //   } else {
    //     this.symbolSource = [];
    //     this.formInfo.supplierList = [];
    //   }
    // },
    // 公司==选择监听
    orgChange(e) {
      this.formInfo.supplierList = []
      this.clearData() //清空通用数据
      this.formInfo['organizationCode'] = e.itemData.orgCode
      this.formInfo['organizationId'] = e.itemData.id
      this.formInfo['organizationName'] = e.itemData.orgName
      this.formInfo.supplierList.push({
        organizationCode: e.itemData.orgCode,
        organizationId: e.itemData.id,
        organizationName: e.itemData.orgName
      })
      this.formInfoBack['organizationCode'] = this.formInfo['organizationCode']
      this.formInfoBack['organizationName'] = this.formInfo['organizationName']
      this.formInfoBack['organizationId'] = this.formInfo.organizationId
      let aParams = { orgId: e.itemData.id, status: [1, 2, 10, 20, 30] }
      this.templateTypeParams['organizationCode'] = e.itemData
        ? this.formInfo.supplierList[0].organizationCode
        : ''
      if (e.itemData?.id) {
        this.getOrgPartnerRelationsByStatusInterface(aParams) // 请求供应商
      } else {
        this.symbolSource = []
        this.formInfo.supplierList = []
      }
    },
    // 任务类型==选择监听
    async bizTypeChange(e) {
      this.formInfo.qualificationTemplateTypeCode = ''
      this.thresholdFieldList = []
      this.thresholdFieldListTwo = []
      this.qualificationTemplateTypeCodeVal = ''
      if (this.dialogGridDataSource) {
        this.dialogGridDataSource = JSON.parse(JSON.stringify(this.dialogGridDataSource))
        this.dialogGridDataSource.forEach((i) => {
          i['qualificationTemplateCode'] = ''
          i['sceneId'] = ''
          i['sceneCode'] = ''
        }) //清空所选供应商行内隐藏字段code;
        //TODO简易深拷贝
        if (e.itemData.value == 1) {
          //"品类认证"
          this.dialogGridDataSource.forEach((item) => {
            item.progressShow = true
          })
        } else if (e.itemData.value == 0) {
          //"其它"
          this.dialogGridDataSource.forEach((item) => {
            item.progressShow = false
          })
        }
        await this.qualificationTemplateConditionQueryInterfaceReferToScene()
      }
    },
    // 品类==选择监听
    categoryNameChange(e) {
      this.formInfo['categoryId'] = e.itemData.id
      this.formInfo['categoryCode'] = e.itemData.categoryCode
      let param = e.itemData.categoryCode || ''
      this.templateTypeParams['categoryCode'] = param
      this.clearData()
      // this.formInfo.categoryInfoList = [];
      // this.categoryList.forEach(item=>{
      //   if(e.value.indexOf(item.categoryCode) != -1){
      //     this.formInfo.categoryInfoList.push({
      //       categoryCode:item.categoryCode,
      //       categoryId:item.id,
      //       categoryName:item.categoryName,
      //     })
      //   }
      // })
    },
    // 模板类型/资质模板==选择监听
    async qualificationTemplateTypeCodeChange(e) {
      this.progressVal = ''
      let param = null
      if (this.formInfo.bizType)
        param = this.$refs.thresholdFieldListRef.ejsRef.getDataByValue(e.value)
      if (!this.formInfo.bizType)
        param = this.$refs.thresholdFieldListRefTwo.ejsRef.getDataByValue(e.value)
      this.qualificationTemplateTypeCodeVal = param.qualificationTemplateTypeCode
      this.dialogGridDataSource = JSON.parse(JSON.stringify(this.dialogGridDataSource))
      await this.qualificationTemplateConditionQueryInterfaceReferToScene()
    },
    // 模板类型/资质模板==打开时
    openSelect() {
      if (this.formInfo.organizationId && this.formInfo.categoryName) {
        this.qualificationTemplateConditionQueryInterface(this.templateTypeParams)
      } else {
        this.$toast({ content: this.$t('请选择公司及品类后再查询模板'), type: 'error' })
      }
    },
    // 选择供应商==选择监听
    supplierSelectChange() {
      // 放在watch中了,这里不好用
    },
    // 选择供应商==打开时
    supplierSelectOpen() {
      if (!this.formInfo.organizationId || this.formInfo.organizationId.length <= 0) {
        return this.$toast({
          content: this.$t('请选择公司后再查询供应商'),
          type: 'error'
        })
      }
      let params = this.templateTypeParams || {}
      this.$API.ModuleConfig.qualificationTemplateConditionQuery(params)
        .then((res) => {
          //模板类型
          if (res.code == 200 && res.data) {
            let newData = res.data.filter((i) => {
              if (this.qualificationTemplateTypeCodeVal) {
                return i.qualificationTemplateTypeCode == this.qualificationTemplateTypeCodeVal
              } else {
                return i.qualificationTemplateTypeCode
              }
            })
            this.dataArr = []
            newData.map((ic) => {
              if (ic.sceneInfoList)
                ic.sceneInfoList.map((i) => {
                  if (i) this.dataArr.push(i)
                })
            })
            this.symbolSource.map((i) => {
              //引入场景中使用
              i['dataArr'] = this.dataArr
            })
          }
          if (res.code == 500)
            this.symbolSource.map((i) => {
              i['dataArr'] = []
            })
        })
        .catch(() => {
          this.symbolSource.map((i) => {
            i['dataArr'] = []
          })
        })
    },
    //引用场景==选择监听
    progressChange(val, row) {
      this.rowProgress = row
      // this.templateTypeParams["sceneCode"] = val.itemData.sceneCode;
      if (row && this.formInfo.bizType) {
        //判断存不存在当前行 && 任务类型==1
        this.dialogGridDataSource.forEach((i) => {
          if (i.supplierName === row.supplierName) {
            i['qualificationTemplateCode'] = this.formInfo.qualificationTemplateTypeCode || ''
            i['sceneId'] = val.itemData.sceneId
            i['sceneCode'] = val.itemData.sceneCode
          }
        })
      }
    },
    // //引用场景==打开时
    progressOpen() {},
    getSelectedRecords() {},
    symbolChange(e) {
      if (e.value > 5) {
        this.formInfo.defaultValue = null
        this.rules.defaultValue[0].required = false
      } else {
        this.rules.defaultValue[0].required = true
      }
    },
    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    fieldIdChange(e) {
      if (e != null && e.value != null) {
        this.formInfo.fieldCode = e.itemData.fieldCode
        this.formInfo.fieldId = e.itemData.id
        this.formInfo.fieldName = e.itemData.fieldName
      } else {
        this.formInfo.fieldCode = null
        this.formInfo.fieldId = null
        this.formInfo.fieldName = null
      }
    },
    //=======【保存 取消】=======
    async confirm() {
      let flag = false
      let _this = this
      this.formInfo.supplierList = []
      if (this.projectCode && this.formInfo.supplierName.length > 0) {
        for (let i = 0; i < this.formInfo.supplierName.length; i++) {
          let _record = this.symbolSource.find(
            (element) => element.supplierInternalCode == this.formInfo.supplierName[i]
          )
          this.templateTypeParams = {
            categoryCode: _record.categoryCode,
            organizationCode: _record.orgCode,
            qualificationTemplateTypeCode: this.formInfo.qualificationTemplateTypeCode,
            sceneCode: _record.sceneCode
          }
          await this.$API.ModuleConfig.qualificationTemplateConditionQuery(this.templateTypeParams)
            .then((res) => {
              //模板类型
              if (res.code == 200) {
                this.dialogGridDataSource[i].qualificationTemplateCode =
                  res.data[0].qualificationTemplateCode
              }
            })
            .catch((err) => {
              flag = true
              this.$toast({
                content: _record.supplierName + err.msg,
                type: 'error'
              })
              return
            })
          if (flag) {
            return
          }
        }
      }
      if (!this.projectCode) {
        this.formInfo['organizationCode'] = this.formInfoBack['organizationCode']
        this.formInfo['organizationName'] = this.formInfoBack['organizationName']
        this.formInfo['organizationId'] = this.formInfoBack['organizationId']
      }
      const dialogGridDataSource = JSON.parse(JSON.stringify(this.dialogGridDataSource))
      dialogGridDataSource.forEach((i) => {
        _this.formInfo.supplierList.push({
          qualificationTemplateCode:
            i.qualificationTemplateCode || this.formInfo.qualificationTemplateTypeCode,
          sceneId: i.sceneId || '',
          sceneCode: i.sceneCode || '',
          supplierCode: i.supplierCode,
          supplierName: i.supplierName,
          supplierInternalCode: i.supplierInternalCode,
          supplierEnterpriseId: i.supplierEnterpriseId,
          partnerRelationId: this.projectCode ? i.partnerRelationId : i.id
        })
      })
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$loading()
          this.$API.qualification
            .managerAdd(this.formInfo)
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
                this.hide()
              }
            })
            .catch((err) => {
              this.$hloading()
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 选择场景
    sceneCodeChange() {}
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
  //当前页==通用样式
  .mgn-left-10 {
    margin-left: 10px;
  }
  .flex {
    display: flex;
  }
  .f-1 {
    flex: 1;
  }
}

//多选框默认文字样式==组件
/deep/ .e-multi-select-wrapper {
  width: 100%;
}
/deep/ .e-multi-select-wrapper .e-searcher {
  width: 100%;
}

/deep/ .mt-drop-down-tree .e-chips-wrapper {
  // width: 0% !important;
  position: absolute !important;
}
</style>
