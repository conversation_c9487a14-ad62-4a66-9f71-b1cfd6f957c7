<template>
  <!-- 选择部门 -->
  <div class="dept-container">
    <mt-input v-model="value" :placeholder="$t('请选择确认部门')" :readonly="true" />
    <mt-icon
      v-show="!disabled"
      class="serch-icon"
      name="icon_input_search"
      @click.native="handleShowDialog"
    />
  </div>
</template>

<script>
export default {
  model: {
    prop: 'modelVal',
    event: 'syncValue'
  },
  props: {
    modelVal: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    }
  },
  methods: {
    handleShowDialog() {
      this.$dialog({
        modal: () => import('./departmentDialog.vue'),
        data: {
          title: this.$t('确认部门')
        },
        success: (val) => {
          this.$emit('syncValue', val?.departmentName || null)
          this.$emit('change', val)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.dept-container {
  display: flex;
  .serch-icon {
    margin: auto 5px;
  }
}
</style>
