<template>
  <!-- 资质项自定义 -->
  <mt-dialog
    ref="thresholdItemDialog"
    :buttons="buttons"
    size="small"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item :label="$t('组织')" label-style="top" prop="organizationId">
          <mt-DropDownTree
            v-model="formInfo.organizationId"
            id="orgTreeSelect"
            :key="orgFields.key"
            :fields="orgFields"
            :placeholder="$t('请选择组织')"
            @input="handleOrgChange"
          />
        </mt-form-item>
        <mt-form-item :label="$t('部门')" label-style="top" prop="deptId">
          <mt-DropDownTree
            v-model="formInfo.deptId"
            id="departmentTreeSelect"
            :key="deptFields.key"
            :fields="deptFields"
            :disabled="!formInfo.organizationId"
            :placeholder="$t('请选择部门')"
            @input="handleDeptChange"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import { randomString } from '@/utils/utils.js'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  data() {
    return {
      formInfo: {},
      //组织树下拉数组
      orgFields: {
        key: 1,
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      deptFields: {
        key: 2,
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      rules: {
        organizationId: [{ required: true, message: this.$t('请选择组织'), trigger: 'blur' }],
        deptId: [{ required: true, message: this.$t('请选择部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.show()
    this.queryOrgTree()
  },
  methods: {
    // 获取组织树
    async queryOrgTree() {
      const queryKey = 'ORG02'
      const res = await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: queryKey,
        orgType: 'ORG001ADM'
      })
      if (res.code === 200) {
        this.orgFields.dataSource = res.data
        this.orgFields.key = randomString()
      }
    },
    // 获取部门树
    async queryDeptTree(organizationId) {
      const res = await this.$API.ModuleConfig.qualificationGetCompanyDepartmentTree({
        organizationId
      })
      if (res.code === 200) {
        this.deptFields.dataSource = res.data
        this.deptFields.key = randomString()
      }
    },
    handleOrgChange(e) {
      if (e.length) {
        this.queryDeptTree(e[0])
      } else {
        this.deptFields = {
          key: randomString(),
          dataSource: [],
          value: 'id',
          text: 'name',
          child: 'children'
        }
        this.handleDeptChange()
      }
    },
    handleDeptChange(e) {
      if (e?.length) {
        this.setUserInfo(this.deptFields.dataSource, e[0])
      } else {
        this.formInfo = {
          ...this.formInfo,
          departmentId: null,
          departmentCode: null,
          departmentName: null
        }
      }
    },
    setUserInfo(arr, targetId) {
      arr.forEach((item) => {
        if (item.id == targetId) {
          const { id, departmentCode, name } = item
          this.formInfo = {
            ...this.formInfo,
            departmentId: id,
            departmentCode,
            departmentName: name
          }
          return
        } else if (Array.isArray(item.children)) {
          this.setUserInfo(item.children, targetId)
        }
      })
    },
    // 显示
    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    // 隐藏
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    // 确认
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formInfo)
        }
      })
    },
    // 取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped></style>
