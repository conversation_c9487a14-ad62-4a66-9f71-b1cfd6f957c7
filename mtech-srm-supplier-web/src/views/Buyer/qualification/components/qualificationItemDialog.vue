<template>
  <!-- 资质项自定义 -->
  <mt-dialog
    ref="thresholdItemDialog"
    :buttons="buttons"
    :header="$t('驳回资质项')"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item
          prop="text1"
          class="form-item"
          :label="$t('驳回原因')"
          label-width="200"
          label-align="right"
          label-style="top"
        >
          <textarea
            v-model="ruleForm.text1"
            class="inputtext"
            :placeholder="$t('请输入驳回原因')"
            name="content"
            maxlength="200"
          ></textarea>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import Vue from 'vue'
// import { dialogGridColumnData } from "../config/index";
export default {
  props: {},
  data() {
    return {
      boolen1: false,
      list: [],
      // dialogGridDataSource,
      // dialogGridColumnData,
      // 表格的配置
      // pageConfig: [
      //   {
      //     useToolTemplate: false,
      //     useBaseConfig: false,
      //     toolbar: {
      //       tool: [],
      //     },

      //     grid: {
      //       // lineSelection: true,
      //       allowPaging: false,
      //       columnData: [],
      //       dataSource: [], //表格内容
      //       // asyncConfig: {
      //       //   url: this.$API.surveyTemplate.getReview,
      //       //   methods: "get",
      //       //   params: this.modalData,
      //       // },
      //       // frozenColumns: 1,
      //     },
      //   },
      // ],
      sourceLabel: this.$t('选择资质'),
      radioData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '2'
        }
      ],
      bizTypeList: [
        {
          text: this.$t('资质项'),
          value: 1
        },
        {
          text: this.$t('业务类型'),
          value: 2
        }
      ],
      symbolList: [
        {
          text: '>',
          value: 1
        },
        {
          text: '<',
          value: 2
        },
        {
          text: '≥',
          value: 3
        },
        {
          text: '≤',
          value: 4
        },
        {
          text: '=',
          value: 5
        },
        {
          text: this.$t('非空'),
          value: 6
        },
        {
          text: this.$t('为空'),
          value: 7
        }
      ],
      formInfo: {
        bizId: 0,
        bizType: 2,
        defaultValue: '',
        fieldCode: null,
        fieldId: null,
        fieldName: null,
        formType: 0,
        symbol: 0,
        thresholdName: '',
        source: '1'
      },
      ruleForm: {
        text1: ''
      },
      thresholdFieldList: [],
      formTypeList: [],
      rules: {
        text1: [
          {
            required: true,
            message: this.$t('请输入驳回原因'),
            trigger: 'blur'
          }
        ],
        thresholdName: [
          {
            required: true,
            message: this.$t('请输入门槛名称'),
            trigger: 'blur'
          },
          {
            whitespace: true,
            message: this.$t('请输入门槛名称'),
            trigger: 'blur'
          }
        ],
        formType: [
          {
            required: true,
            message: this.$t('请选择门槛类型'),
            trigger: 'blur'
          }
        ],
        bizType: [{ required: true, message: this.$t('请选择数据源'), trigger: 'blur' }],
        fieldCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        symbol: [
          {
            required: true,
            message: this.$t('请选择所属维度'),
            trigger: 'blur'
          }
        ],
        defaultValue: [
          {
            required: true,
            message: this.$t('请输入默认目标值'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.okbtn,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      uploadInfo: {} // 上传后信息
    }
  },
  mounted() {
    // this.initData();
    this.show()
    // this.getReview();
  },

  created() {},
  beforeMount() {
    // params()
  },
  methods: {
    async getReview() {
      const { data } = await this.$API.surveyTemplate.getReview(this.modalData)
      this.list = data
      this.getlist() //处理动态数据
    },
    getlist() {
      const { submitList } = this.list

      // let healder = [];
      let obj = {}
      submitList.forEach((item) => {
        let obj1 = {
          field: item.fieldCode,
          headerText: item.fieldName
        }
        if (item.fieldCode == 'file') {
          obj1.template = () => {
            return {
              template: Vue.component('modelName', {
                template: `<div>
                  <a v-if="data.file" style="color:#00469c" :href="data.file" download target="_blank">{{ $t("下载") }}</a>
                </div>`,
                data: function () {}
              })
            }
          }
        }
        this.pageConfig[0].grid.columnData.push(obj1)
        obj[item.fieldCode] = item.fieldValue
      })

      this.pageConfig[0].grid.dataSource.push(obj)
      if (this.pageConfig[0].grid.columnData.length > 0) {
        this.boolen1 = true
      }
    },

    // 失效前提醒（天）==触发时
    handleChange() {},
    // getSelectedRecords() {
    //   // let Obj = this.$refs.dataGrid.ejsRef.getSelectedRecords()
    // },
    onFiltering(e) {
      e.updateData(this.thresholdFieldList.filter((x) => x.fieldName.includes(e.text)))
    },
    symbolChange(e) {
      if (e.value > 5) {
        this.formInfo.defaultValue = null
        this.rules.defaultValue[0].required = false
      } else {
        this.rules.defaultValue[0].required = true
      }
    },
    async initData() {
      await this.getDict()
      // await this.getThresholdFieldList(this.info.bizType);
      await this.getThresholdFieldList()
      if (this.isEdit && this.info && Object.keys(this.info).length) {
        this.formInfo = {
          ...this.info,
          bizType: 2,
          formType: this.info.formType.toString()
        }
      }
    },
    async getDict() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdType'
      }).then((res) => {
        this.formTypeList = res.data
      })
    },
    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    // bizTypeChange(e) {
    //   if (e.value == 1) {
    //     this.sourceLabel = "选择资质";
    //   } else {
    //     this.sourceLabel = "选择信息";
    //   }
    //   this.getThresholdFieldList(e.value);
    // },
    async getThresholdFieldList() {
      await this.$API.ModuleConfig.queryThresholdFieldList()
        .then((res) => {
          if (res.code === 200) {
            this.thresholdFieldList = res.data
          }
        })
        .catch((e) => {
          this.thresholdFieldList = []
          this.$toast({ content: e.msg, type: 'warning' })
          return
        })
    },
    fieldIdChange(e) {
      if (e != null && e.value != null) {
        this.formInfo.fieldCode = e.itemData.fieldCode
        this.formInfo.fieldId = e.itemData.id
        this.formInfo.fieldName = e.itemData.fieldName
      } else {
        this.formInfo.fieldCode = null
        this.formInfo.fieldId = null
        this.formInfo.fieldName = null
      }
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (this.formInfo.defaultValue < 0) {
            this.$toast({
              content: this.$t('请输入非负整数'),
              type: 'warning'
            })
            return
          }
          const methodName = this.isEdit ? 'updateThresholdDef' : 'addThresholdDef'

          this.$API.ModuleConfig[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    okbtn() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.ruleForm.text1) //&&
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.inputtext {
  width: 852px;
  height: 200px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 6px;
  padding-left: 20px;
  padding-bottom: 20px;
  margin-top: 16px;
  resize: none;
}
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
  .demo-block1 {
    display: flex;
    justify-content: center;
    align-items: center;
    .xx {
      color: #9a9a9a;
      text-align: center;
      font-size: 14px;
    }
  }

  .bt {
    color: #f44336;
  }

  //当前页==通用样式
  .mgn-left-10 {
    margin-left: 10px;
  }
  .flex {
    display: flex;
  }
  .f-1 {
    flex: 1;
  }
}
/deep/ .mt-input-number {
  width: 100%;
}
/deep/ .mt-input-number .input--wrap {
  width: 100%;
}
/deep/ #mtInputNumber {
  width: 100% !important;
}
</style>
