<!-- 选择事业部 -->
<template>
  <mt-dialog
    ref="dialog"
    :header="$t('选择所属事业部')"
    :buttons="buttons"
    :height="400"
    width="30%"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <div style="margin-top: 18px">
      <mt-form ref="ruleForm" :model="formData" :rules="formRules">
        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="affiliatedOrgCode" :label="$t('所属事业部')">
              <mt-select
                v-model="formData.affiliatedOrgCode"
                :data-source="orgCodeOptions"
                float-label-type="Never"
                :placeholder="$t('请选择')"
                width="100%"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      formRules: {
        affiliatedOrgCode: [
          {
            required: true,
            message: this.$t('请选择所属事业部'),
            trigger: 'blur'
          }
        ]
      },
      orgCodeOptions: [
        { text: this.$t('商用事业部'), value: '1014' },
        { text: this.$t('泛智屏事业部'), value: '1503' }
      ]
    }
  },
  computed: {
    applyCodeList() {
      return this.modalData.ids
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title } = args
      this.dialogTitle = title
    },
    beforeOpen() {
      this.formData = {}
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.formData = {}
    },
    handleClose() {
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$dialog({
            data: {
              title: this.$t('再次确认'),
              message: this.$t('确定选择当前事业部吗？'),
              confirm: () =>
                this.$API.qualification.managerBatchPublish({
                  applyCodeList: this.applyCodeList,
                  affiliatedOrgCode: this.formData.affiliatedOrgCode
                })
            },
            success: () => {
              this.$emit('confirm', this.formData)
              this.handleClose()
            }
          })
        }
      })
    }
  }
}
</script>
