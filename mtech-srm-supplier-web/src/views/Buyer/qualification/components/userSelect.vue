<template>
  <!-- 选择部门 -->
  <div class="user-container">
    <mt-select
      v-model="value"
      :data-source="userList"
      :fields="{ text: 'employeeName', value: 'employeeId' }"
      :disabled="disabled"
      :allow-filtering="true"
      :filtering="handleFilter"
      :placeholder="$t('请选择')"
      @change="handleSelectChange"
    />
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'

export default {
  model: {
    prop: 'modelVal',
    event: 'syncValue'
  },
  props: {
    modelVal: {
      type: String,
      default: null
    },
    departmentId: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    userName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      userList: []
    }
  },
  computed: {
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    }
  },
  watch: {
    userName: {
      handler(val) {
        if (val) {
          let employeeName = val?.split('-')[0] || ''
          this.querUserList(employeeName)
        }
      },
      immediate: true
    }
  },
  mounted() {
    // this.querUserList('')
    this.handleFilter = utils.debounce(this.handleFilter, 1000)
  },
  methods: {
    async querUserList(employeeName) {
      // const params = {
      //   employeeName,
      //   orgId: this.departmentId,
      //   tenantId: '10000'
      // }
      // const res = await this.$API.ModuleConfig.getOrganizationEmployees(params)
      // if (res.code === 200) {
      //   res.data?.forEach((item) => {
      //     item.employeeName = item.employeeName + '-' + item.employeeCode
      //   })
      //   this.userList = res.data || []
      // }
      let params = {
        fuzzyName: employeeName
      }
      this.$API.supplierIndex.currentTenantEmployees(params).then((res) => {
        if (res.code == 200) {
          res.data?.forEach((item) => {
            item.employeeName = item.employeeName + '-' + item.employeeCode
          })
          this.userList = res.data || []
        }
      })
    },
    handleFilter(e) {
      this.querUserList(e.text || '')
    },
    handleSelectChange(e) {
      this.$emit('syncValue', e.itemData?.employeeId || null)
      this.$emit('change', e.itemData)
    }
  }
}
</script>

<style scoped lang="scss"></style>
