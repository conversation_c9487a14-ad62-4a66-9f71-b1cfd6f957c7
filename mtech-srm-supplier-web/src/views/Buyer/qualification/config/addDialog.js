// =====对话框表格数据源=====
import Vue from 'vue'
import { i18n } from '@/main.js'
export let dialogGridDataSource = []
// =====对话框表格列=====
export let dialogGridColumnData = [
  // { width: '60',
  //   type: 'checkbox',
  //   allowFiltering: true,
  //   allowSorting: 'false'
  // },
  {
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM'),
    textAlign: 'center'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP'),
    textAlign: 'center'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    textAlign: 'center'
  },
  {
    field: 'status',
    headerText: i18n.t('阶段'),
    // width: "80",
    textAlign: 'center',
    // valueConverter: {
    //   type: "map", //(map为key/value对象)：此时，fields可不传。
    //   map: {
    //     1: i18n.t("注册"),
    //     2: i18n.t("潜在"),
    //     10: i18n.t("合格"),
    //     20: i18n.t("冻结"),
    //     30: i18n.t("黑名单"),
    //   },
    // },
    template: function () {
      return {
        template: Vue.component('spanState', {
          template: `<span>{{ paramsName || data.supplierStageName }}</span>`,
          data() {
            return {
              data: {},
              paramsName: ''
            }
          },
          mounted() {
            if (this.data.status == 1) {
              this.paramsName = i18n.t('注册')
            } else if (this.data.status == 2) {
              this.paramsName = i18n.t('潜在')
            } else if (this.data.status == 10) {
              this.paramsName = i18n.t('合格')
            } else if (this.data.status == 20) {
              this.paramsName = i18n.t('冻结')
            } else if (this.data.status == 30) {
              this.paramsName = i18n.t('黑名单')
            } else {
              this.paramsName = ' '
            }
          },
          methods: {
            changeBtn(val) {
              this.$parent.$emit('scene', val, this.dataArr)
            }
            // this.orgName = this.data.rangeDTOList
            //   .filter((item) => {
            //     return item.rangeType == 2;
            //   })
            //   .map((e) => e.orgName)
            //   .toString();
          }
        })
      }
    }
  },
  {
    field: '',
    headerText: i18n.t('引入场景'),
    width: '300',
    textAlign: 'center',
    template: function () {
      return {
        template: Vue.component('progressCon', {
          template: `<mt-select
                          v-if="data.progressShow && !data.authProjectCode"
                          v-model="progressValNew"
                          :dataSource="data.dataArr"
                          width="250"
                          :showClearButton="true"
                          :placeholder="$t('请选择')"
                          :fields="{ text: 'sceneName', value: 'sceneCode' }"
                          @change="changeBtn"
                          @open="progressOpen">
                        </mt-select>
                        <span v-else>{{data.sceneName}}</span>
                      `,
          data() {
            return {
              data: {},
              progressValNew: ''
            }
          },
          props: {
            progressVal: {
              type: String,
              default: ''
            }
          },
          watch: {
            progressVal(newVal) {
              this.progressValNew = newVal
            }
          },
          methods: {
            changeBtn(val) {
              this.$parent.$emit('progressChange', val, this.data)
            },
            progressOpen() {
              this.$parent.$emit('progressOpen', this.data)
            }
          }
        })
      }
    }
  }
]
