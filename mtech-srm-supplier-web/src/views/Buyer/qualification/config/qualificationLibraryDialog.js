// import dayjs from "dayjs";
import { i18n } from '@/main.js'

//待处理
export const getListColumnData = function () {
  const listColumnData = [
    {
      width: '250',
      field: 'applyCode',
      headerText: i18n.t('审查单编号'),
      cellTools: []
    },
    {
      width: '160',
      field: 'applyName',
      headerText: i18n.t('申请单名称')
    },
    {
      width: '160',
      field: 'customerCode',
      headerText: i18n.t('客户编码')
    },
    {
      width: '160',
      field: 'customerName',
      headerText: i18n.t('客户名称')
    },
    {
      width: '160',
      field: 'categoryName',
      headerText: i18n.t('品类')
    },
    {
      width: '160',
      field: 'supplierStatus',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map', //(map为key/value对象)：此时，fields可不传。
        map: {
          0: i18n.t('待处理'),
          10: i18n.t('已保存,待提交'),
          20: i18n.t('已提交（待审批）'),
          30: i18n.t('被驳回')
        }
      }
      // 状态（0,草稿（保存操作）；1，待填写（发布操作） ；
      // 2， 待审批（供方提交操作）；3，已完成（确认操作）；4， 驳回（驳回操作）；5，已关闭（关闭操作））
      // 除了状态2是供方需要操作的状态其余均是采方
    },
    {
      width: '200',
      field: 'remark',
      headerText: i18n.t('驳回原因')
    },
    {
      width: '160',
      field: 'createTime',
      headerText: i18n.t('创建日期'),
      type: 'date',
      format: 'yyyy-MM-dd'
    }
  ]
  return listColumnData
}

// 资质库
export const Qualification = function () {
  // let conlumnD = [
  //   {

  //   }
  // ]
  const listColumnData = [
    {
      width: '230',
      field: 'qualificationCode',
      headerText: i18n.t('资质项编号'),
      cellTools: []
    },
    {
      width: '230',
      field: 'qualificationName',
      headerText: i18n.t('资质项')
    },
    {
      width: '230',
      field: 'customerName',
      headerText: i18n.t('客户')
    },
    {
      width: '230',
      field: 'categoryName',
      headerText: i18n.t('品类')
    },
    {
      width: '230',
      field: 'validityDate',
      headerText: i18n.t('有效期'),
      ignore: true
    },
    {
      width: '230',
      field: 'createTime',
      headerText: i18n.t('创建日期'),
      type: 'date',
      format: 'yyyy-MM-dd'
    }
  ]
  return listColumnData
}

// 动态数据==有用上面可删掉
export let dialogGridColumnData = function () {
  const listColumnData = [
    // {
    //   width: "150",
    //   field: "taskName",
    //   headerText: i18n.t("证书编号"),
    // },
    // {
    //   field: "taskID",
    //   headerText: i18n.t("生效日期"),
    //   width: "150",
    // },
    // {
    //   field: "efficacy",
    //   headerText: i18n.t("失效日期"),
    //   width: "150",
    // },
    // {
    //   field: "duration",
    //   headerText: i18n.t("获取日期"),
    //   width: "150",
    // },
    // {
    //   field: "progress",
    //   headerText: i18n.t("发证机关"),
    //   width: "150",
    // },
    // {
    //   field: "accessory",
    //   headerText: i18n.t("附件"),
    //   width: "150",
    //   cellTools: []
    // },
    // {
    //   field: "products",
    //   headerText: i18n.t("代理产品"),
    //   width: "150",
    // },
    // {
    //   field: "factory",
    //   headerText: i18n.t("代理工厂"),
    //   width: "150",
    // },
    // {
    //   field: "priority",
    //   headerText: i18n.t("联系人"),
    //   width: "150",
    // },
  ]
  return listColumnData
}
