import Vue from 'vue'
import { i18n } from '@/main.js'
//表格列==资质审查单
function getListColumnData() {
  const listColumnData = [
    {
      width: '140',
      field: 'applyCode',
      headerText: i18n.t('审查单编号'),
      cellTools: []
    },
    {
      width: '140',
      field: 'applyName',
      headerText: i18n.t('审查单名称')
    },
    {
      width: '140',
      field: 'supplierInternalCode',
      headerText: i18n.t('供应商编号-SRM'),
      cellTools: []
    },
    {
      width: '140',
      field: 'supplierCode',
      headerText: i18n.t('供应商编号-SAP')
    },
    {
      width: '140',
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      width: '140',
      field: 'categoryName',
      headerText: i18n.t('品类')
    },
    {
      width: '140',
      field: 'qualificationTemplateTypeName',
      headerText: i18n.t('审查模板类型')
    },
    {
      width: '140',
      field: 'tolink',
      headerText: i18n.t('跳转地址'),
      cellTools: []
      // template: () => {
      //   return {
      //     template: Vue.component('linkTemplate', {
      //       template: `
      //           <div class="time-box">
      //             {{ data.tolink || '--' }}
      //           </div>`,
      //       data() {
      //         return { data: {} }
      //       }
      //     })
      //   }
      // }
    },
    {
      width: '100',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map', //(map为key/value对象)：此时，fields可不传。
        map: {
          10: i18n.t('草稿'),
          20: i18n.t('待反馈'),
          30: i18n.t('待确认'),
          40: i18n.t('已确认'),
          50: i18n.t('不通过'),
          60: i18n.t('完成')
        }
      }
    },
    {
      width: '140',
      field: 'confirmProcess',
      headerText: i18n.t('确认进度'),
      ignore: true
    },
    {
      width: '140',
      field: 'organizationName',
      headerText: i18n.t('公司')
    },
    {
      width: '180',
      field: 'bizType',
      headerText: i18n.t('任务类型（来源）'),
      valueConverter: {
        type: 'map', //(map为key/value对象)：此时，fields可不传。
        map: {
          0: i18n.t('其他'),
          1: i18n.t('品类认证')
        }
      }
    },
    {
      width: '150',
      field: 'authProjectCode',
      // cellTools: [],
      headerText: i18n.t('品类认证编号'),
      template: function () {
        return {
          template: Vue.component('spanState', {
            template: `<span><span @click="goto" v-if="data.authProjectCode" style="color:#00469c;cursor: pointer;">{{data.authProjectCode}}</span><span v-else>-</span></span>`,
            data() {
              return {
                data: {}
              }
            },
            methods: {
              goto() {
                this.$router.push({
                  path: '/supplier/pur/category-certification-detail',
                  query: {
                    id: this.data.authProjectId,
                    type: 1
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      width: '140',
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      width: '140',
      field: 'createDate',
      headerText: i18n.t('创建日期')
      // type: "date",
      // format: "yyyy-MM-dd hh:mm:ss"
    },
    {
      width: '140',
      field: 'remark',
      headerText: i18n.t('备注')
    }
  ]
  return listColumnData
}
//表格列==资质库
function getListColumnDataTwo() {
  const listColumnData = [
    {
      width: '140',
      field: 'qualificationCode',
      headerText: i18n.t('资质项编码'),
      cellTools: []
    },
    {
      width: '140',
      field: 'qualificationName',
      headerText: i18n.t('资质项')
    },
    {
      width: '140',
      field: 'supplierInternalCode',
      headerText: i18n.t('供应商编号-SRM'),
      cellTools: []
    },
    {
      width: '140',
      field: 'supplierCode',
      headerText: i18n.t('供应商编号-SAP')
    },
    {
      width: '140',
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      width: '140',
      field: 'categoryName',
      headerText: i18n.t('品类')
    },
    {
      width: '140',
      field: 'qualificationTemplateTypeName',
      headerText: i18n.t('审查类型')
    },
    {
      width: '140',
      field: 'validityDate',
      headerText: i18n.t('有效期'),
      ignore: true
      // operator: "equal",
      // valueConverter: {
      //   type: "string",
      //   operator: "equal"
      // },
      // map必须是对象格式的
      // template: function () {
      //   return {
      //     template: Vue.component("spanState", {
      //       template: `<span>{{paramsName}}</span>`,
      //       data() {
      //         return {
      //           data: {},
      //           paramsName: "",
      //         };
      //       },
      //       mounted() {
      //         if (!this.data.validityDate) {
      //           this.paramsName = '-'
      //         } else {
      //           this.paramsName = this.data.validityDate
      //         }
      //       },
      //     }),
      //   };
      // },
    },
    {
      width: '100',
      field: 'databaseStatus',
      headerText: i18n.t('状态'),
      ignore: true
      // valueConverter: {
      //   type: "map", //(map为key/value对象)：此时，fields可不传。
      //   map: {
      //     0: i18n.t("已生效"),
      //     1: i18n.t("已失效"),
      //   },
      // },
      // 状态（0,草稿（保存操作）；1，待填写（发布操作） ；
      // 2， 待审批（供方提交操作）；3，已完成（确认操作）；4， 驳回（驳回操作）；5，已关闭（关闭操作））
      // 除了状态2是供方需要操作的状态其余均是采方
    },
    {
      width: '140',
      field: 'organizationName',
      headerText: i18n.t('公司')
    },
    {
      width: '140',
      field: 'createDate',
      headerText: i18n.t('创建日期')
      // valueConverter: {
      //   type: "function", //filter可不传，如果未传，则原数据返回
      //   filter: (data) => {
      //     return dayjs(+data).format("YYYY-MM-DD");
      //   },
      // },
    }
  ]
  return listColumnData
}

// type: pur 采方，sup 供方
//=====业务组件参数=====
export const defaultTools = [
  {
    id: 'Add',
    icon: 'icon_solid_edit',
    title: i18n.t('新增')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_edit',
    title: i18n.t('删除')
  },
  {
    id: 'Publish',
    icon: 'icon_solid_edit',
    title: i18n.t('发布')
  },
  {
    id: 'export',
    title: i18n.t('导出')
  }
  // {
  //   id: "Submit",
  //   icon: "icon_solid_edit",
  //   title: i18n.t("提交综合审批"),
  // },
  // {
  //   id: "Reject",
  //   icon: "icon_solid_edit",
  //   title: i18n.t("驳回"),
  // },
  // {
  //   id: "Confirm",
  //   icon: "icon_solid_edit",
  //   title: i18n.t("确认"),
  // },
]
export const pageConfig = () => {
  let tools = [[], ['Filter', 'refresh', 'setting']]
  return [
    {
      gridId: 'a129a584-6a05-432c-bd23-65785dbd927c',
      title: i18n.t('资质审查单'),
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        tools: tools
      },
      grid: {
        lineSelection: true,
        ignoreFields: ['confirmProcess'],
        columnData: getListColumnData(),
        asyncConfig: {
          url: '/supplier/tenant/buyer/qualification/manager/queryPageList'
        },
        frozenColumns: 3,
        dataSource: []
      }
    },
    {
      gridId: '94611586-5a9f-4de1-a513-ab56185edc61',
      title: i18n.t('资质库'),
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        tools: [
          [
            {
              id: 'export',
              title: i18n.t('导出')
            }
          ],
          ['Filter', 'refresh', 'setting']
        ]
      },
      grid: {
        lineSelection: false,
        columnData: getListColumnDataTwo(),
        asyncConfig: {
          url: '/supplier/tenant/buyer/qualification/item/manager/qualificationDatabasePage', //采方
          // query: {
          //   current: 1,
          //   size: 10,
          // },
          methods: 'post'
          // defaultRules: [
          //   {
          //     label: i18n.t("状态"),
          //     field: "status",
          //     type: "number",
          //     operator: "equal",
          //     value: 3,
          //   },
          // ],
        },
        frozenColumns: 3
        // dataSource: [],
      }
    }
  ]
}

export const pageConfigForCate = () => {
  let tools = [[], ['Filter', 'refresh', 'setting']]
  return [
    {
      gridId: 'c0f23ad4-fdf0-4331-9d6b-9a63675a6c00',
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        tools: tools
      },
      grid: {
        lineSelection: true,
        ignoreFields: ['confirmProcess'],
        columnData: getListColumnData(),
        asyncConfig: {
          url: '/supplier/tenant/buyer/qualification/manager/queryPageList'
        },
        frozenColumns: 3,
        dataSource: []
      }
    }
  ]
}
