import Vue from 'vue'
import { i18n } from '@/main.js'
// import DepartmentSelect from '../components/departmentSelect'
import UserSelect from '../components/userSelect'

const statusList = [
  { text: i18n.t('草稿'), value: '10', cssClass: '' },
  { text: i18n.t('待反馈'), value: '20', cssClass: '' },
  { text: i18n.t('待确认'), value: '30', cssClass: '' },
  { text: i18n.t('已确认'), value: '40', cssClass: '' },
  { text: i18n.t('已驳回'), value: '50', cssClass: '' }
]

export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'qualificationName',
    headerText: i18n.t('资质项名称'),
    allowEditing: false
  }
]
export const columnDataMainTwo = [
  {
    field: 'modelName',
    headerText: i18n.t('附件模板'),
    allowEditing: false,
    cellTools: []
  },
  {
    field: 'orgName',
    headerText: i18n.t('有效期'),
    allowEditing: false
  },
  {
    width: '100',
    field: 'status',
    headerText: i18n.t('状态'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: statusList
    },
    cellTools: [
      {
        id: 'confirm',
        title: i18n.t('确认'),
        visibleCondition: (data) => {
          return data['status'] == '30' && data['showFlag'] !== 0
        }
      },
      {
        id: 'reject',
        title: i18n.t('驳回'),
        visibleCondition: (data) => {
          return data['status'] == '30' && data['showFlag'] !== 0
        }
      }
    ],
    editorRender(h, scoped) {
      const selectedItem = statusList.find((item) => item.value == scoped.status)
      return (
        <div>
          <span>{selectedItem?.text}</span>
        </div>
      )
    }
  },
  // {
  //   width: '150',
  //   field: 'confirmDeptName',
  //   headerText: i18n.t('确认部门'),
  //   allowEditing: true,
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component('confirmDeptHeader', {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('确认部门')}}</span>
  //             </div>
  //           `
  //       })
  //     }
  //   },
  //   editorRender(h, scoped) {
  //     return (
  //       <div>
  //         <DepartmentSelect
  //           v-model={scoped.confirmDeptName}
  //           disabled={scoped.status != '10'}
  //           onchange={(item) => {
  //             const { departmentId, departmentCode } = item
  //             if (scoped.confirmDeptId != departmentId) {
  //               scoped.confirmDeptId = departmentId
  //               scoped.confirmDept = departmentCode

  //               scoped.confirmUserId = null
  //               scoped.confirmUserCode = null
  //               scoped.confirmUserName = null
  //             }
  //           }}
  //         />
  //       </div>
  //     )
  //   }
  // },
  {
    width: '150',
    field: 'confirmUserName',
    headerText: i18n.t('人员'),
    allowEditing: true,
    headerTemplate: () => {
      return {
        template: Vue.component('userNameHeader', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('人员')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <UserSelect
            v-model={scoped.confirmUserId}
            department-id={scoped.confirmDeptId}
            user-name={scoped.confirmUserName}
            disabled={scoped.status != '10'}
            onChange={(item) => {
              const { employeeCode, employeeName } = item
              scoped.confirmUserCode = employeeCode
              scoped.confirmUserName = employeeName
            }}
          />
        </div>
      )
    }
  },
  {
    field: 'remark',
    width: '150',
    headerText: i18n.t('驳回原因'),
    allowEditing: false
  },
  {
    field: 'auditRemark',
    width: '150',
    headerText: i18n.t('评审重点说明'),
    allowEditing: false
  }
]
