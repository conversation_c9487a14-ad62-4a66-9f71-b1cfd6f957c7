<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      v-if="pageConfig.length > 0"
    />
  </div>
</template>
<script>
import { pageConfig, defaultTools, pageConfigForCate } from './config/index'
import utils from '@/utils/utils'

export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    taskCode: {
      type: String,
      default: ''
    },
    taskTemplateId: {
      type: String,
      default: ''
    },
    initData: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    routeName() {
      return this.$route.name
    }
  },
  components: {},
  data() {
    return {
      emptyMsg: '',
      //公司准入关系
      buyerPartnerFactoryRelationList: {},
      //表单详情
      buyerFormInstanceList: {},
      // 表单模板
      formTemplateArr: [],
      pageConfig: []
    }
  },
  mounted() {
    let _pageConfig
    if (this.routeName === 'pur-category-certification-detail') {
      _pageConfig = pageConfigForCate()
      _pageConfig[0].grid.asyncConfig.defaultRules = []
      _pageConfig[0].grid.asyncConfig.defaultRules.push({
        label: this.$t('品类认证项目编号'),
        field: 'authProjectId',
        type: 'string',
        operator: 'equal',
        value: this.info.projectId
      })
      if (this.taskCode == 'investigationTempType') {
        _pageConfig[0].grid.asyncConfig.defaultRules.push({
          label: this.$t('模板类型'),
          field: 'qualificationTemplateTypeCode',
          operator: 'equal',
          value: 'investigation'
        })
      } else {
        _pageConfig[0].grid.asyncConfig.defaultRules.push({
          label: this.$t('模板类型'),
          field: 'qualificationTemplateTypeCode',
          operator: 'equal',
          value: 'sign'
        })
      }
      if (this.info.status != 10 && this.info.status != 30) {
        _pageConfig[0].toolbar.tools[0].length = 0
        // _pageConfig[0].useToolTemplate = true;
      } else {
        _pageConfig[0].toolbar.tools[0] = defaultTools
        // pageConfig[0].useToolTemplate = false;
      }
    } else {
      _pageConfig = pageConfig()
      _pageConfig[0].toolbar.tools[0] = defaultTools
    }
    this.pageConfig = _pageConfig
    if (this.taskCode == 'investigationTempType') {
      let params = {
        page: {
          current: 1,
          size: 10
        },
        rules: [
          {
            label: this.$t('品类认证项目编号'),
            field: 'authProjectId',
            type: 'string',
            operator: 'equal',
            value: this.info.projectId
          },
          {
            label: this.$t('模板类型'),
            field: 'qualificationTemplateTypeCode',
            operator: 'equal',
            value: 'investigation'
          }
        ]
      }

      this.$API.qualification.checkQualifications(params).then((res) => {
        if (res.data) {
          this.$dialog({
            data: {
              title: this.$t('提交'),
              message: this.$t('是否确认提交资质审查单？')
            },
            success: () => {
              this.$API.qualification
                .submitQualifications({
                  authProjectCode: this.info.projectCode,
                  taskTemplateId: this.taskTemplateId
                })
                .then(() => {
                  this.initData('investigationTempType')
                  this.$toast({ content: this.$t('操作成功'), type: 'success' })
                })
            }
          })
        }
      })
    }
  },
  methods: {
    handleClickToolBar(e) {
      const _this = this
      const { toolbar, gridRef, tabIndex } = e
      let sltList = gridRef.getMtechGridRecords()
      //======新增======
      if (toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          data: {
            title: _this.$t('新增资质审查单'),
            projectCode: _this.info.projectCode,
            taskCode: _this.taskCode,
            info: _this.info
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
      if ((!sltList || sltList.length <= 0) && ['Delete', 'Publish'].includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //=====删除======
      if (toolbar.id == 'Delete') {
        if (tabIndex === 0) {
          let ids = sltList.map((e) => {
            return e.applyCode
          })
          this.$dialog({
            data: {
              title: this.$t('删除'),
              message: this.$t('是否确认删除所选资质审查单？'),
              confirm: () => _this.$API.qualification.managerDel({ applyCodeList: ids })
            },
            success: () => {
              _this.$toast({ content: this.$t('删除成功'), type: 'success' })
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      }
      //======发布======
      else if (toolbar.id == 'Publish') {
        let ids = sltList.map((e) => {
          return e.applyCode
        })
        if (['1503', '1014'].includes(this.info.orgCode)) {
          this.$dialog({
            modal: () => import('./components/BuSelectDialog.vue'),
            data: {
              title: this.$t('选择所属事业部'),
              ids
            },
            success: () => {
              _this.$toast({ content: this.$t('发布成功'), type: 'success' })
            }
          })
        } else {
          this.$dialog({
            data: {
              title: this.$t('发布'),
              message: this.$t('是否发布所选资质审查单？'),
              confirm: () =>
                _this.$API.qualification.managerBatchPublish({
                  applyCodeList: ids
                })
            },
            success: () => {
              _this.$toast({ content: this.$t('发布成功'), type: 'success' })
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      }
      // 导出
      else if (toolbar.id === 'export') {
        this.handleExport(tabIndex)
      }
      // //==提交综合审批==
      // if(toolbar.id == 'Submit'){
      //   let ids = sltList.map((e) => e.qualificationCode);
      //   this.qualificationDefineDel(ids);
      // }
    },
    handleClickCellTitle(e) {
      const { data, tabIndex } = e
      // 资质审查单
      if (tabIndex == 0) {
        if (e.field == 'applyCode') {
          //审查单编号==跳转
          this.$router.push({
            path: '/supplier/pur/qualificationInfo',
            query: {
              applyCode: data.applyCode
            }
          })
        }
        if (e.field == 'supplierInternalCode') {
          let { data } = e
          this.$router.push({
            name: 'profileDetail',
            query: {
              partnerArchiveId: data.buyerPartnerRelationLinkResponse.partnerArchiveId,
              orgId: data.buyerPartnerRelationLinkResponse.orgId,
              supplierEnterpriseId: data.buyerPartnerRelationLinkResponse.supplierEnterpriseId,
              partnerRelationCode: data.buyerPartnerRelationLinkResponse.partnerRelationCode,
              status: data.buyerPartnerRelationLinkResponse.status
            }
          })
        }
        if (e.field == 'authProjectId') {
          let { data } = e
          if (data.authProjectId !== 0) {
            this.$router.push({
              name: 'pur-category-certification',
              query: {
                id: data.authProjectId,
                type: 1
              }
            })
          }
        }
      }
      // 资质库
      else if (tabIndex == 1) {
        if (e.field == 'supplierInternalCode') {
          let { data } = e
          this.$router.push({
            name: 'profileDetail',
            query: {
              partnerArchiveId: data.buyerPartnerRelationLinkResponse.partnerArchiveId,
              orgId: data.buyerPartnerRelationLinkResponse.orgId,
              supplierEnterpriseId: data.buyerPartnerRelationLinkResponse.supplierEnterpriseId,
              partnerRelationCode: data.buyerPartnerRelationLinkResponse.partnerRelationCode,
              status: data.buyerPartnerRelationLinkResponse.status
            }
          })
        }
        if (e.field == 'qualificationCode') {
          //审查单编号==跳转
          this.$dialog({
            modal: () => import('./components/qualificationLibraryInfoDialog.vue'),
            data: {
              title: this.$t('查看资质项'),
              info: data
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (e.field == 'customerCode') {
          let { data } = e
          this.$router.push({
            name: 'profileDetail',
            query: {
              partnerArchiveId: data.buyerPartnerRelationLinkResponse.partnerArchiveId,
              orgId: data.buyerPartnerRelationLinkResponse.orgId,
              supplierEnterpriseId: data.buyerPartnerRelationLinkResponse.supplierEnterpriseId,
              partnerRelationCode: data.buyerPartnerRelationLinkResponse.partnerRelationCode,
              status: data.buyerPartnerRelationLinkResponse.status
              // buyerPartnerRelationLinkResponse:{
              //   partnerArchiveId:data.buyerPartnerRelationLinkResponse.partnerArchiveId,
              //   orgId:data.buyerPartnerRelationLinkResponse.orgId,
              //   supplierEnterpriseId:data.buyerPartnerRelationLinkResponse.supplierEnterpriseId,
              //   partnerRelationCode:data.buyerPartnerRelationLinkResponse.partnerRelationCode,
              // }
            }
          })
        }
      }
    },
    saveData() {},
    submit(list) {
      // 供方 —— 只有待填写或驳回状态能提交
      const idList = list.filter((v) => v.status === 1 || v.status === 4).map((v) => v.id)

      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('只有待填写或驳回状态支持提交操作'),
          type: 'warning'
        })
        return
      }

      this.updateSurveyStatus({
        ids: idList
        // status: 2,
      })
    },
    publish(list) {
      // 只有草稿状态能发布
      const idList = list.filter((v) => v.status === 0).map((v) => v.id)

      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('只有草稿状态支持发布操作'),
          type: 'warning'
        })
        return
      }
      this.updateSurveyStatus({
        idList: idList,
        status: 1
      })
    },
    reject(list) {
      // 只有待审批状态能驳回
      const idList = list.filter((v) => v.status === 2).map((v) => v.id)
      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('只有待审批状态支持驳回操作'),
          type: 'warning'
        })
        return
      }
      this.updateSurveyStatus({
        idList: idList,
        status: 4
      })
    },
    confirm(list) {
      // 只有待审批状态能确认
      const idList = list.filter((v) => v.status === 2).map((v) => v.id)
      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('只有待审批状态支持确认操作'),
          type: 'warning'
        })
        return
      }
      this.updateSurveyStatus({
        idList: idList,
        status: 3
      })
    },
    updateSurveyStatus(params) {
      this.$API.supplierInfoSurvey.updateSurveyStatus(params).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          if (result.code === 200 && !utils.isEmpty(result.data)) {
            this.$toast({ content: result.msg, type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({ content: result.msg, type: 'error' })
          }
        }
      })
    },
    // 导出
    handleExport(tabIndex) {
      const rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: rule.condition || '',
        rules: rule.rules || []
      }
      const requestUrl = {
        preName: 'qualification',
        urlName: tabIndex === 0 ? 'exportQualiExamineOrder' : 'exportQualiDatabase'
      }
      utils.exportData(requestUrl, params)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
