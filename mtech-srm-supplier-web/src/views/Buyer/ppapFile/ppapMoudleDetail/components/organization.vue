<template>
  <!-- 资质项自定义 -->
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    size="small"
    :header="$t('选择部门')"
    @beforeClose="cancel"
  >
    <mt-treeView
      ref="treeView"
      class="tree-view-container"
      :show-check-box="true"
      :fields="filedsIcon"
    ></mt-treeView>
  </mt-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    organization() {
      return this.modalData.organization
    }
  },
  data() {
    return {
      filedsIcon: {
        dataSource: [],
        id: 'id',
        text: 'orgName',
        value: 'orgCode',
        child: 'childrenList'
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.show()
    this.$set(this.filedsIcon, 'dataSource', this.organization)
  },
  methods: {
    //确认
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    //隐藏
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    //确认==按钮
    confirm() {
      let treeData = cloneDeep(this.$refs.treeView.ejsRef.getTreeData())
      let checkedData = cloneDeep(this.$refs.treeView.ejsRef.getAllCheckedNodes())
      let listArr = []
      function recursion(arry) {
        for (let i = 0; i < arry.length; i++) {
          listArr.push(arry[i])
          if (arry[i].childrenList && arry[i].childrenList.length > 0) {
            recursion(arry[i].childrenList)
          }
        }
      }
      recursion(treeData)
      let filarr = listArr.filter((item) => checkedData.includes(item.id))
      this.$emit('confirm-function', filarr)
    },
    //取消==按钮
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
// .dialog-content {
//   padding: 20px 20px 0 20px;
//   font-size: 16px;
//   .mt-form {
//     display: flex;
//     flex-wrap: wrap;
//     justify-content: space-between;
//     .mt-form-item {
//       width: 390px;
//     }
//     .full-width {
//       width: 100%;
//     }
//   }
//   .demo-block {
//     width: 100%;
//   }
//   .demo-block1 {
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     .xx {
//       color: #9a9a9a;
//       text-align: center;
//       font-size: 14px;
//     }
//   }

//   //当前页==通用样式
//   .mgn-left-10 {
//     margin-left: 10px;
//   }
//   .flex {
//     display: flex;
//   }
//   .f-1 {
//     flex: 1;
//   }
// }
/deep/ .mt-input-number {
  width: 100%;
}
/deep/ .mt-input-number .input--wrap {
  width: 100%;
}
/deep/ #mtInputNumber {
  width: 100% !important;
}
</style>
