import Vue from 'vue'
import utils from '@/utils/utils'
import { i18n } from '@/main'
export const registColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'inviteNo',
    headerText: i18n.t('申请单编码'),
    headerTextAlign: 'center',
    cellTools: []
  },
  {
    width: '150',
    field: 'customerBizName',
    headerText: i18n.t('客户名称'),
    headerTextAlign: 'center'
  },
  // {
  //     width: "100",
  //     field:'businessType',
  //     headerText:i18n.t('来源'),
  //     headerTextAlign: "center",
  //     valueConverter: {
  //       type: "map",
  //       map: { 1: i18n.t("采方邀请注册"), 2: i18n.t("采方快速引入"), 3: i18n.t("供方自主注册") },
  //     },
  // },
  {
    width: '150',
    field: 'supplierType',
    headerText: i18n.t('供应商类型'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'map',
      map: {
        logisticsProvider: i18n.t('物流商'),
        commonPurchaseSupplier: i18n.t('通采'),
        noBiddingPurchaseSupplier: i18n.t('非采'),
        '': ''
      }
    }
  },
  {
    width: '150',
    field: 'extList',
    headerText: i18n.t('供应范围'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        let arr = []
        e.forEach((item) => {
          arr.push(item.customerCategoryName)
        })
        return arr.join()
      }
    }
  },
  {
    width: '110',
    field: 'inviteStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('待申请'),
        2: i18n.t('待确认'),
        3: i18n.t('已确认'),
        4: i18n.t('已拒绝'),
        5: i18n.t('已撤回')
      }
    }
  },
  {
    width: '100',
    field: 'approvalRemark',
    headerText: i18n.t('审批备注'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e || '--'
      }
    }
  },
  {
    width: '100',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    headerTextAlign: 'center'
  },
  {
    width: '130',
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    headerTextAlign: 'center',
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd hh:mm:ss')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd hh:mm:ss')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '100',
    field: 'remark',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]

export const inviteColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'inviteNo',
    headerText: i18n.t('邀请单ID'),
    headerTextAlign: 'center',
    cellTools: []
  },
  {
    width: '150',
    field: 'invitationName',
    headerText: i18n.t('邀请单名称'),
    headerTextAlign: 'center'
  },
  {
    width: '150',
    field: 'inviterName',
    headerText: i18n.t('邀请人'),
    headerTextAlign: 'center'
  },
  {
    width: '150',
    field: 'inviterMobile',
    headerText: i18n.t('邀请人联系方式'),
    headerTextAlign: 'center'
  },
  {
    width: '150',
    field: 'inviterEmail',
    headerText: i18n.t('邀请人邮箱'),
    headerTextAlign: 'center'
  },
  {
    width: '150',
    field: 'inviteStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('待申请'),
        2: i18n.t('待处理'),
        3: i18n.t('已确认'),
        4: i18n.t('已拒绝'),
        5: i18n.t('已撤回')
      }
    },
    cellTools: [
      {
        id: 'agree',
        title: i18n.t('接受'),
        visibleCondition: (data) => {
          return data['inviteStatus'] == 2
        }
      },
      {
        id: 'disAgree',
        title: i18n.t('拒绝'),
        visibleCondition: (data) => {
          return data['inviteStatus'] == 2
        }
      }
    ]
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('邀请时间'),
    headerTextAlign: 'center',
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd hh:mm:ss')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd hh:mm:ss')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
//我的申请新增弹出框==表格列
export const addColumnData = [
  {
    field: 'orgid',
    headerText: i18n.t('客户编码'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgName',
    headerText: i18n.t('客户名称'),
    headerTextAlign: 'center',
    textAlign: 'center'
  },
  {
    width: '280',
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    headerTextAlign: 'center',
    textAlign: 'center',
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('CategoryName', {
          template: `
          <mt-tooltip
          target="#title"
          cssClass="e-tooltip-css"
          ref="tooltipTitle"
          position="BottomCenter"
          :content="val"
        >
          <div id="container">
            <div class="content" >
              <div class="text" id="title"">{{categoryName}}</div>
            </div>
          </div>
        </mt-tooltip>
          `,
          data: function () {
            const _this = this
            return {
              val: function () {
                return {
                  template: Vue.component('detail', {
                    template: `
                  <div id="tooltip" ref="content" style="max-height:100px;">
                      {{value}}
                  </div>`,
                    data() {
                      return {
                        value: _this.categoryName
                      }
                    },
                    computed: {}
                  })
                }
              }
            }
          },
          computed: {
            categoryName() {
              return this.data.categoryName
            }
          }
        })
      }
    }
  },
  {
    field: 'username',
    headerText: i18n.t('供方联系人'),
    headerTextAlign: 'center',
    textAlign: 'center'
  },
  {
    field: 'mobile',
    headerText: i18n.t('供方联系电话'),
    headerTextAlign: 'center',
    textAlign: 'center'
  }
]
