<template>
  <div class="register-box">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { registColumn } from './config/column'
import axios from 'axios'
export default {
  data() {
    return {
      currentTab: 0,
      templateConfig: [
        {
          gridId: 'efffcce8-5347-4a27-8dcd-bfb11f838690',
          title: this.$t('我的申请'),
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_table_new', title: this.$t('新增') },
                { id: 'edit', icon: 'icon_table_edit', title: this.$t('编辑') },
                {
                  id: 'deleted',
                  icon: 'icon_table_delete',
                  title: this.$t('删除')
                },
                {
                  id: 'apply',
                  icon: 'icon_card_invite',
                  title: this.$t('提交')
                }
              ],
              [
                'Filter',
                {
                  id: 'applyExport',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          grid: {
            columnData: registColumn,
            asyncConfig: {
              url: '/supplier/tenant/supplier/invite/list',
              defaultRules: [
                {
                  label: this.$t('状态'),
                  field: 'businessType',
                  type: 'number',
                  operator: 'equal',
                  value: '3'
                }
              ]
            }
          }
        }
        // {
        //   title: this.$t("邀约管理"),
        //   useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
        //   toolbar: {
        //     useBaseConfig: false,
        //     tools: [
        //       [
        //         {
        //           id: "agree",
        //           icon: "icon_card_invite",
        //           title: this.$t("接受"),
        //         },
        //         {
        //           id: "disAgree",
        //           icon: "icon_card_invite",
        //           title: this.$t("拒绝"),
        //         },
        //       ],
        //       [
        //         "Filter",
        //         {
        //           id: "export",
        //           icon: "icon_solid_export",
        //           title: this.$t("导出"),
        //         },
        //         "Refresh",
        //         "Setting",
        //       ],
        //     ],
        //   },
        //   grid: {
        //     columnData: inviteColumn,
        //     asyncConfig: {
        //       url: "/supplier/tenant/supplier/invite/list",
        //       defaultRules: [
        //         {
        //           label: this.$t("状态"),
        //           field: "businessType",
        //           type: "number",
        //           operator: "notequal",
        //           value: "3",
        //         },
        //       ],
        //     },
        //   },
        // },
      ]
    }
  },
  created() {
    if (this.$route.query.tab) {
      this.currentTab = parseInt(this.$route.query.tab)
    }
  },
  methods: {
    //单元格Title点击
    handleClickCellTitle(e) {
      if (e.tabIndex == 0) {
        this.$dialog({
          modal: () => import('./components/addRegister.vue'),
          data: {
            title: this.$t('查看'),
            type: 'view',
            record: [e.data]
          },
          success: () => {},
          close: () => {}
        })
      } else if (e.tabIndex == 1) {
        this.$router.push({
          name: 'sup-customer-invitation-info',
          params: {
            type: 'view',
            id: e.data.id
          }
        })
      }
    },
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.gridRef.getMtechGridRecords()
      let name = item.toolbar ? item.toolbar.id : item.tool.id
      if (name == 'add') {
        //新增：打开弹窗
        this.$dialog({
          modal: () => import('./components/addRegister.vue'),
          data: {
            title: this.$t('新增'),
            type: 'add',
            record: []
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          },
          close: () => {}
        })
      } else if (name == 'edit') {
        // 供应商类型：物流商
        if (records[0].supplierType == 'logisticsProvider') {
          this.$toast({
            content: this.$t('物流商类型，不需要编辑，若想改变供应商类型，可删除重新添加'),
            type: 'error'
          })
          return
        }
        // 编辑
        if (records.length == 1) {
          if (
            records[0].inviteStatus == 1 ||
            records[0].inviteStatus == 4 ||
            records[0].inviteStatus == 5
          ) {
            this.$dialog({
              modal: () => import('./components/addRegister.vue'),
              data: {
                title: this.$t('编辑'),
                type: 'edit',
                record: records
              },
              success: () => {
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          } else {
            this.$toast({
              content: this.$t('此状态下不能编辑'),
              type: 'warning'
            })
          }
        } else {
          this.$toast({
            content: this.$t('请选择一条数据进行编辑'),
            type: 'warning'
          })
        }
      } else if (
        name == 'deleted' ||
        name == 'apply' ||
        name == 'recall' ||
        name == 'agree' ||
        name == 'disAgree'
      ) {
        if (records.length > 0) {
          let idList = []
          let num = 0
          records.forEach((item) => {
            idList.push(item.id)
            if (item.inviteStatus == 2 || item.inviteStatus == 3) {
              num++
            }
          })
          if (name == 'deleted') {
            if (num == 0) {
              this.$dialog({
                data: {
                  title: this.$t('提示'),
                  message: this.$t('确认删除数据？')
                },
                success: () => {
                  this.$loading()
                  this.$API.supplierRegister
                    .delSupplierInvite({ idList })
                    .then(() => {
                      this.$hloading()
                      this.$refs.templateRef.refreshCurrentGridData()
                      this.$toast({
                        content: this.$t('删除成功'),
                        type: 'success'
                      })
                    })
                    .catch((err) => {
                      this.$hloading()
                      this.$toast({
                        content: err.msg,
                        type: 'error'
                      })
                    })
                }
              })
            } else {
              this.$toast({
                content: this.$t('数据状态不能删除'),
                type: 'warning'
              })
            }
          } else if (name == 'apply') {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认申请数据？')
              },
              success: () => {
                this.$loading()
                this.$API.supplierRegister
                  .applyRegister({ idList })
                  .then(() => {
                    this.$hloading()
                    this.$refs.templateRef.refreshCurrentGridData()
                    this.$toast({
                      content: this.$t('申请成功'),
                      type: 'success'
                    })
                  })
                  .catch((err) => {
                    this.$hloading()
                    this.$toast({
                      content: err.msg,
                      type: 'error'
                    })
                  })
              }
            })
          } else if (name == 'recall') {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认撤回数据？')
              },
              success: () => {
                this.$loading()
                this.$API.supplierRegister
                  .withdrawRegister({ idList })
                  .then(() => {
                    this.$hloading()
                    this.$refs.templateRef.refreshCurrentGridData()
                    this.$toast({
                      content: this.$t('撤回成功'),
                      type: 'success'
                    })
                  })
                  .catch((err) => {
                    this.$hloading()
                    this.$toast({
                      content: err.msg,
                      type: 'error'
                    })
                  })
              }
            })
          } else if (name == 'agree' || name == 'disAgree') {
            let inviteStatus = name == 'agree' ? 3 : 4
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('是否确认执行此操作？')
              },
              success: () => {
                this.$loading()
                this.$API.supplierRegister
                  .confirmInvite({ idList, inviteStatus })
                  .then(() => {
                    this.$hloading()
                    this.$refs.templateRef.refreshCurrentGridData()
                    this.$toast({
                      content: this.$t('操作成功'),
                      type: 'success'
                    })
                  })
                  .catch((err) => {
                    this.$hloading()
                    this.$toast({
                      content: err.msg,
                      type: 'error'
                    })
                  })
              }
            })
          }
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (name == 'applyExport') {
        //我的申请
        let idList = []
        records.forEach((item) => {
          idList.push(item.id)
        })
        this.$loading()
        axios
          .post(
            '/api/supplier/tenant/supplier/invite/exportInvite',
            {
              page: {
                current: 1,
                size: 10000
              },
              defaultRules: [
                {
                  field: 'businessType',
                  label: this.$t('状态'),
                  operator: 'equal',
                  type: 'number',
                  value: 3
                }
              ],
              ids: idList
            },
            {
              responseType: 'blob' // 1.首先设置responseType对象格式为 blob:
            }
          )
          .then((res) => {
            this.$hloading()
            // console.log(res); //把response打出来，看下图
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
            let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

            // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = this.$t('我的申请.xlsx')
            a.click()
            // 5.释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$hloading()
            this.$toast({ content: this.$t('导出失败，请重试!'), type: 'warning' })
          })
      } else if (name == 'export') {
        //邀约管理
        let idList = []
        records.forEach((item) => {
          idList.push(item.id)
        })
        this.$loading()
        axios
          .post(
            '/api/supplier/tenant/supplier/invite/exportInvite',
            {
              page: {
                current: 1,
                size: 10000
              },
              defaultRules: [
                {
                  field: 'businessType',
                  label: this.$t('状态'),
                  operator: 'notequal',
                  type: 'number',
                  value: 3
                }
              ],
              ids: idList
            },
            {
              responseType: 'blob' // 1.首先设置responseType对象格式为 blob:
            }
          )
          .then((res) => {
            this.$hloading()
            // console.log(res); //把response打出来，看下图
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
            let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

            // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = this.$t('邀约管理.xlsx')
            a.click()
            // 5.释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$hloading()
            this.$toast({ content: this.$t('导出失败，请重试!'), type: 'warning' })
          })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.register-box {
  width: 100%;
  height: 100%;
}
</style>
