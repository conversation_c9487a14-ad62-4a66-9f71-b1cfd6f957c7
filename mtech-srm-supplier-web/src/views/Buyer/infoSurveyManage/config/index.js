import dayjs from 'dayjs'
import { i18n } from '@/main.js'
function getListColumnData(isPur) {
  const listColumnData = [
    {
      width: '150',
      field: 'surveyTaskNo',
      headerText: i18n.t('表单编码'),
      cellTools: []
    },
    {
      width: '150',
      field: 'surveyTaskName',
      headerText: i18n.t('表单名称')
    },
    {
      width: '150',
      field: 'surveySource',
      headerText: i18n.t('来源')
    },
    {
      width: '210',
      field: 'surveyTemplateType',
      headerText: i18n.t('调查表类型')
    }
  ]

  if (isPur) {
    listColumnData.push(
      {
        width: '210',
        field: 'orgSupplierRelationDTO.supplierEnterpriseCode',
        headerText: i18n.t('供应商企业编码'),
        formatter: (column, data) => {
          return data?.orgSupplierRelationDTO?.supplierEnterpriseCode || '--'
        }
      },
      {
        width: '150',
        field: 'orgSupplierRelationDTO.supplierEnterpriseName',
        headerText: i18n.t('供应商企业名称'),
        formatter: (column, data) => {
          return data?.orgSupplierRelationDTO?.supplierEnterpriseName || '--'
        }
      }
    )
  } else {
    listColumnData.push(
      {
        width: '210',
        field: 'supplierInfoDTO.customerEnterpriseCode',
        headerText: i18n.t('客户企业编码'),
        formatter: (column, data) => {
          return data?.supplierInfoDTO?.customerEnterpriseCode || '--'
        }
      },
      {
        width: '150',
        field: 'supplierInfoDTO.customerEnterpriseName',
        headerText: i18n.t('客户企业名称'),
        formatter: (column, data) => {
          return data?.supplierInfoDTO?.customerEnterpriseName || '--'
        }
      }
    )
  }

  listColumnData.push(
    {
      width: '150',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map', //(map为key/value对象)：此时，fields可不传。
        map: {
          0: i18n.t('草稿'),
          1: i18n.t('待填写'),
          2: i18n.t('待审批'),
          3: i18n.t('已完成'),
          4: i18n.t('驳回'),
          5: i18n.t('已关闭')
        }
      }

      // 状态（0,草稿（保存操作）；1，待填写（发布操作） ；
      // 2， 待审批（供方提交操作）；3，已完成（确认操作）；4， 驳回（驳回操作）；5，已关闭（关闭操作））
      // 除了状态2是供方需要操作的状态其余均是采方
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('创建日期'),
      valueConverter: {
        type: 'function', //filter可不传，如果未传，则原数据返回
        filter: (data) => {
          return dayjs(+data).format('YYYY-MM-DD')
        }
      }
    }
  )

  return listColumnData
}

// type: pur 采方，sup 供方
export const pageConfig = (isPur) => {
  console.log('isPur', isPur)

  let buttonTools = []

  if (isPur) {
    buttonTools = [
      {
        id: 'Add',
        icon: 'icon_solid_edit',
        title: i18n.t('新增')
      },
      {
        id: 'Publish',
        icon: 'icon_solid_edit',
        title: i18n.t('发布')
      },
      {
        id: 'Reject',
        icon: 'icon_solid_edit',
        title: i18n.t('驳回')
      },
      {
        id: 'Confirm',
        icon: 'icon_solid_edit',
        title: i18n.t('确认')
      }
    ]
  } else {
    buttonTools = [
      {
        id: 'Submit',
        icon: 'icon_solid_edit',
        title: i18n.t('提交')
      }
    ]
  }
  const tools = [buttonTools, ['Filter', 'refresh', 'setting']]
  return [
    {
      gridId: 'ed682af1-beac-4e3c-bb9e-421825050b63',
      title: i18n.t('处理中'),
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        tools: tools
      },
      grid: {
        lineSelection: true,
        columnData: getListColumnData(isPur),
        asyncConfig: {
          url: isPur
            ? '/supplier/tenant/buyer/survey/task/query' //采方
            : '/supplier/tenant/supplier/survey/task/query', //  供方
          // url: "/supplier/tenant/supplier/survey/task/query",  供方
          defaultRules: [
            {
              label: i18n.t('状态'),
              field: 'status',
              type: 'number',
              operator: 'notequal',
              value: 3
            }
          ]
        },
        frozenColumns: 3,
        dataSource: []
      }
    },
    {
      gridId: '9366a35f-3952-469c-9931-566569070512',
      title: i18n.t('已完成'),
      toolbar: [],
      // useToolTemplate: false,
      // toolbar: {
      // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      // tools: tools,
      // },
      grid: {
        lineSelection: true,
        columnData: getListColumnData(isPur),
        asyncConfig: {
          url: isPur
            ? '/supplier/tenant/buyer/survey/task/query' //采方
            : '/supplier/tenant/supplier/survey/task/query', //  供方
          defaultRules: [
            {
              label: i18n.t('状态'),
              field: 'status',
              type: 'number',
              operator: 'equal',
              value: 3
            }
          ]
        },
        frozenColumns: 3,
        dataSource: []
      }
    }
  ]
}
