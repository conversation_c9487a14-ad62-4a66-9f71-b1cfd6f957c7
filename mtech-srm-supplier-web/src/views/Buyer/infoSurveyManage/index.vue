<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config/index'
import utils from '@/utils/utils'
import { checkIsPur } from './config/utils'

export default {
  components: {},
  data() {
    return {
      emptyMsg: '',
      //公司准入关系
      buyerPartnerFactoryRelationList: {},
      //表单详情
      buyerFormInstanceList: {},
      // 表单模板
      formTemplateArr: [],
      pageConfig: pageConfig(checkIsPur())
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)

      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      }

      const list = e.gridRef.getMtechGridRecords()
      if (list.length === 0) return

      if (e.toolbar.id == 'Publish') {
        this.publish(list)
      } else if (e.toolbar.id == 'Reject') {
        this.reject(list)
      } else if (e.toolbar.id == 'Confirm') {
        this.confirm(list)
      } else if (e.toolbar.id == 'Submit') {
        // 供方提交按钮
        this.submit(list)
      }
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
      if (e.field == 'surveyTaskNo') {
        let path = ''
        if (checkIsPur()) {
          path = '/supplier/pur/info-survey-detail'
        } else {
          path = '/supplier/sup/info-survey-detail'
        }
        this.$router.push({
          path: path,
          query: {
            id: e.data.id
          }
        })
      }
    },
    handleAdd() {
      console.log('点击新增')
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    saveData() {},

    submit(list) {
      // 供方 —— 只有待填写或驳回状态能提交
      const idList = list.filter((v) => v.status === 1 || v.status === 4).map((v) => v.id)

      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('只有待填写或驳回状态支持提交操作'),
          type: 'warning'
        })
        return
      }

      this.updateSurveyStatus({
        ids: idList
        // status: 2,
      })
    },

    publish(list) {
      // this.$t("只有草稿状态能发布")
      const idList = list.filter((v) => v.status === 0).map((v) => v.id)

      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('只有草稿状态支持发布操作'),
          type: 'warning'
        })
        return
      }
      this.updateSurveyStatus({
        idList: idList,
        status: 1
      })
    },

    reject(list) {
      // this.$t("只有待审批状态能驳回")
      const idList = list.filter((v) => v.status === 2).map((v) => v.id)
      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('只有待审批状态支持驳回操作'),
          type: 'warning'
        })
        return
      }
      this.updateSurveyStatus({
        idList: idList,
        status: 4
      })
    },

    confirm(list) {
      // 只有待审批状态能确认
      const idList = list.filter((v) => v.status === 2).map((v) => v.id)
      if (idList.length < list.length) {
        this.$toast({
          content: this.$t('只有待审批状态支持确认操作'),
          type: 'warning'
        })
        return
      }
      this.updateSurveyStatus({
        idList: idList,
        status: 3
      })
    },

    updateSurveyStatus(params) {
      this.$API.supplierInfoSurvey.updateSurveyStatus(params).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          if (result.code === 200 && !utils.isEmpty(result.data)) {
            this.$toast({ content: result.msg, type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({ content: result.msg, type: 'error' })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
