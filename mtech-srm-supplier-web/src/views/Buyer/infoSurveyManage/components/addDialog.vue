<template>
  <mt-dialog ref="dialog" :header="dialogTitle" :buttons="buttons" @close="cancel" :open="onOpen">
    <mt-form class="form-dialog" ref="form" :model="formData" :rules="rules">
      <mt-form-item prop="surveyTaskName" :label="$t('名称')">
        <mt-input v-model="formData.surveyTaskName" maxlength="30" />
      </mt-form-item>

      <mt-form-item prop="orgId" :label="$t('公司')">
        <!-- <mt-select
          v-model="formData.orgId"
          float-label-type="Never"
          :data-source="orgList"
          :fields="{ text: 'orgName', value: 'id' }"
          :placeholder="$t('请选择公司')"
          @change="orgChange"
        ></mt-select> -->
        <!-- <MasterdataDropdownSelects 
          :remoteSearch="true"
          v-model="formData.orgId"
          :params="{
            organizationLevelCodes: ['ORG02', 'ORG01'],
            orgType: 'ORG001PRO',
            includeItself: true,
          }"
          :fields="{ text: 'title', value: 'id' }"
          :handleData="handleCompanyData"
          @change="orgChange"
          :titleSwitch="false"
          :placeholder="$t('请选择公司')"
          selectType="administrativeCompany"
        ></MasterdataDropdownSelects> -->

        <RemoteAutocomplete
          :params="{
            organizationLevelCodes: ['ORG02', 'ORG01'],
            orgType: 'ORG001PRO',
            includeItself: true
          }"
          v-model="formData.orgCode"
          :fields="{ text: 'orgName', value: 'orgCode' }"
          url="/masterDataManagement/tenant/organization/specified-level-paged-query"
          @change="orgChange"
          :title-switch="false"
          :width="414"
          :placeholder="$t('请选择')"
          select-type="administrativeCompany"
        ></RemoteAutocomplete>
      </mt-form-item>

      <mt-form-item prop="surveyTemplateId" :label="$t('调查表模板')">
        <mt-select
          v-model="formData.surveyTemplateId"
          float-label-type="Never"
          :data-source="surveyTemplateList"
          :fields="{ text: 'surveyTemplateName', value: 'id' }"
          :placeholder="$t('请选择调查表模板')"
          @change="surveyTemplateChange"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="supplierEnterpriseIds" :label="$t('供应商')">
        <mt-multi-select
          v-model="formData.supplierEnterpriseIds"
          :data-source="supplierEnterpriseList"
          :show-clear-button="true"
          :allow-filtering="true"
          :fields="{
            text: 'supplierEnterpriseName',
            value: 'supplierEnterpriseId'
          }"
          :placeholder="$t('请选择供应商')"
        ></mt-multi-select>

        <!-- <mt-select
          v-model="formData.supplierEnterpriseIds"
          float-label-type="Never"
          :fields="{
            text: 'supplierEnterpriseName',
            value: 'supplierEnterpriseId',
          }"
          :data-source="supplierEnterpriseList"
          :placeholder="$t('请选择供应商')"
        ></mt-select> -->
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>
<script>
import utils from '@/utils/utils'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: this.$t('新建'),
      formData: {},
      // orgList: [],
      surveyTemplateList: [],
      supplierEnterpriseList: [],
      rules: {
        surveyTaskName: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
        orgCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        surveyTemplateId: [
          {
            required: true,
            message: this.$t('请选择调查表模板'),
            trigger: 'blur'
          }
        ],
        supplierEnterpriseIds: [
          { required: true, message: this.$t('请选择供应商'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    buttons() {
      return [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.saveAndPublish,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并发布') }
        }
      ]
    },
    rowId() {
      return this.modalData.rowId || ''
    }
  },
  async created() {
    this.queryTemplateList()
    // const userDetail = await this.getUserDetail();

    // if (userDetail && userDetail.id) {
    //   this.getChildrenCompanyOrganization(userDetail.id);
    // }
    if (this.data && this.data.id) {
      this.headFormData = Object.assign({}, this.data)
    }
  },
  mounted() {
    document.body.style.overflow = 'hidden'
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
    handleCompanyData(resData = []) {
      resData = resData.filter((item) => {
        return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      })
      return resData
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    cancel() {
      this.$refs['dialog'].ejsRef.hide()
      document.body.style.overflow = 'scroll'
    },

    // getUserDetail() {
    //   return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
    //     let { data } = res;
    //     let { companyOrg } = data;
    //     return companyOrg;
    //   });
    // },

    // getChildrenCompanyOrganization() {
    //   // this.$API.supplierInvitation["getChildrenCompanyOrganization"]({
    //   //   organizationId: userId,
    //   // }).then((result) => {
    //   this.$API.supplierInvitation["getChildrenCompanyOrganization2"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((result) => {
    //     if (result.code === 200 && !utils.isEmpty(result.data)) {
    //       this.orgList = result.data.filter((item) => {
    //         return item.orgLevelTypeCode === "ORG02" ||
    //           item.orgLevelTypeCode === "ORG01";
    //       });
    //     } else {
    //       this.orgList = [];
    //     }
    //   });
    // },

    queryManagerSupplier(companyId) {
      this.$API.supplierInfoSurvey
        .queryManagerSupplier({
          page: { current: 1, size: 10 },
          orgId: companyId
        })
        .then((result) => {
          let { data } = result
          if (result.code === 200 && !utils.isEmpty(data)) {
            this.supplierEnterpriseList = data?.records || []
          }
        })
        .catch(() => {})
    },

    queryTemplateList() {
      this.$API.supplierInfoSurvey
        .queryTemplateList({
          condition: '',
          page: {
            current: 1,
            size: 10000
          },
          defaultRules: [
            {
              field: 'status',
              label: this.$t('状态'),
              operator: 'equal',
              type: 'number',
              value: 1
            }
          ]
        })
        .then((result) => {
          let { data } = result
          if (result.code === 200 && !utils.isEmpty(data)) {
            this.surveyTemplateList = data?.records || []
          }
        })
        .catch(() => {})
    },

    orgChange(event) {
      // 有级联
      const { itemData = {} } = event
      if (itemData?.id) {
        this.formData.orgId = itemData.id
        this.queryManagerSupplier(itemData?.id)
      } else {
        // 清空供应商绑定值 和 dataSource
        this.formData.supplierEnterpriseIds = []
        this.supplierEnterpriseList = []
      }
    },

    surveyTemplateChange(event) {
      const { itemData } = event
      this.formData.surveyTemplateType = itemData.surveyTemplateType
    },

    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const param = this.formatParams()
          this.$API.supplierInfoSurvey.addSurverTask(param).then((result) => {
            if (result.code === 200 && !utils.isEmpty(result.data)) {
              this.$toast({ content: result.msg, type: 'success' })
              // this.cancel();
              this.$emit('confirm-function')
            } else {
              this.$toast({ content: result.msg, type: 'error' })
            }
          })
        }
      })
    },

    saveAndPublish() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const param = this.formatParams()
          this.$API.supplierInfoSurvey.submitSurverTask(param).then((result) => {
            if (result.code === 200 && !utils.isEmpty(result.data)) {
              if (result.code === 200 && !utils.isEmpty(result.data)) {
                this.$toast({ content: result.msg, type: 'success' })
                // this.cancel();
                this.$emit('confirm-function')
              } else {
                this.$toast({ content: result.msg, type: 'error' })
              }
            }
          })
        }
      })
    },

    formatParams() {
      const param = { ...this.formData, surveySource: this.$t('手动创建') }
      param.supplierEnterpriseIds = param.supplierEnterpriseIds.join()

      return param
    }
  }
}
</script>
<style lang="scss" scoped></style>
