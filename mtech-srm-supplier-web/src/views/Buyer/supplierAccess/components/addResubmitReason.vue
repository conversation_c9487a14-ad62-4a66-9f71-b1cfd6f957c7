<template>
  <div class="addSubmitReson-dialog">
    <mt-dialog
      ref="dialog"
      css-class="bule-bg"
      :width="900"
      min-height="600"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div class="dialog-content addSubmit-dialog">
        <div class="reject-reason">{{ $t('驳回原因') }}：{{ rejectReason }}</div>

        <div class="form-box">
          <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
            <mt-form-item
              class="form-item"
              :label="$t('重新提交原因')"
              label-style="top"
              prop="submitReason"
            >
              <mt-input
                v-model="formInfo.submitReason"
                :placeholder="$t('请输入重新提交原因')"
                float-label-type="Never"
                :multiline="true"
                :rows="3"
                type="text"
              ></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formInfo: {
        submitReason: ''
      },
      rules: {
        submitReason: [
          { required: true, message: this.$t('请输入重新提交原因'), trigger: 'submit' }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.submit,
          buttonModel: { isPrimary: 'true', content: this.$t('重新提交') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    // inviteRules() {
    //   return inviteRule
    // },
    header() {
      return this.modalData.title
    },
    rejectReason() {
      return this.modalData.rejectReason || this.$t('无')
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },
    submit() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formInfo.submitReason)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.addSubmit-dialog {
  padding: 40px 20px;

  .reject-reason {
    font-size: 14px;

    font-weight: normal;
    color: #292929;
  }

  .form-box {
    margin-top: 30px;
    font-size: 14px;

    font-weight: normal;
    color: rgba(41, 41, 41, 1);
  }
}
</style>
