<template>
  <div class="access-process">
    <div class="access-banner fbox">
      <div class="search-box">
        <div class="e-input-group">
          <span class="e-input-group-icon mt-icons mt-icon-icon_search"></span>
          <input
            class="e-input"
            type="text"
            v-model="searchValue"
            :placeholder="$t('请输入公司名称')"
          />
        </div>
      </div>
      <div class="second-box flex1">{{ $t('流程名称') }}</div>
      <div class="process-box">{{ $t('对应阶段') }}</div>
      <!-- <div class="expand-all"><i class="mt-icons mt-icon-MT_FullScreen"></i> {{ $t("全部展开") }}</div> -->
    </div>
    <div class="access-content">
      <!-- 外层行 带底部横线的 内部包含树的 -->
      <div class="process-line2 fbox" v-for="(item, idx) of processData1" :key="idx">
        <div
          class="left-line"
          :class="{
            none: expandCompanyIdArr.includes(item[0].id) || item.length === 1
          }"
        ></div>
        <!-- 内层行 不带底部的横线的 -->

        <div
          class="process-inline fbox"
          :class="{
            'active-expend': expandCompanyIdArr.includes(cItem.id),
            none: expandCompanyIdArr.includes(item[0].id) && cIdx !== 0
          }"
          v-for="(cItem, cIdx) of item"
          :key="cItem.id"
        >
          <!-- 父节点 -->
          <template v-if="cIdx === 0">
            <div class="company-box parent-line" @click="checkNode(cItem.id)">
              <!-- 第一级父节点 -->
              <div class="arrow-wrap fbox">
                <i class="mt-icons mt-icon-icon_Packup"></i>
                <mt-tooltip
                  :content="cItem.name || '--'"
                  :target="'#arrow-txt-' + cIdx"
                  position="BottomCenter"
                >
                  <div :id="'arrow-txt-' + cIdx" class="arrow-txt" @click="selectCompany(cItem)">
                    {{ cItem.name || '--' }}
                  </div>
                </mt-tooltip>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="company-box children-line">
              <!-- 第二级子节点 -->
              <div class="arrow-wrap fbox">
                <mt-tooltip
                  :content="cItem.name || '--'"
                  :target="'#arrow-txt-' + cIdx"
                  position="BottomCenter"
                >
                  <div :id="'arrow-txt-' + cIdx" class="arrow-txt" @click="selectCompany(cItem)">
                    {{ cItem.name || '--' }}
                  </div>
                </mt-tooltip>
              </div>
            </div>
          </template>

          <div class="line-box flex1">
            <mt-tooltip
              :content="cItem.processName || '--'"
              :target="'#line-box-' + cIdx"
              position="BottomCenter"
            >
              <span :id="'line-box-' + cIdx">{{ cItem.processName || '--' }}</span>
            </mt-tooltip>
          </div>

          <div class="process-wrap" :class="{ 'max-height': isExpanded }">
            <div
              class="process-box fbox"
              :class="{
                'company-bg': cIdx === 0
              }"
            >
              <!-- 阶段按钮 -->
              <div
                class="process-item"
                v-for="(singleItem, singleIndex) of cItem.buyerStageInstanceList"
                :key="singleItem.id"
                :class="{
                  opacity: cItem.currentStageId > singleIndex,
                  grey: cItem.currentStageId < singleIndex
                }"
              >
                <!-- 主box -->
                <div
                  class="mian-wrap"
                  @click="stageClick(singleItem, cItem.currentStageId < singleIndex)"
                >
                  <template v-if="cItem.currentStageId > singleIndex">
                    <div class="icon-box">
                      <i class="mt-icons mt-icon-Ok"></i>
                    </div>
                  </template>
                  <template v-else>
                    <div class="number-box">{{ singleIndex + 1 }}</div>
                  </template>

                  <div class="process-name">
                    {{ singleItem.stageName }}
                  </div>

                  <template v-if="cItem.currentStageId > singleIndex">
                    <div class="process-dor">{{ $t('已完成') }}</div>
                  </template>
                  <template v-else-if="cItem.currentStageId === singleIndex">
                    <div class="process-dor">
                      <span class="blue-txt">{{ singleItem.successCount }}</span
                      >/<span>{{ singleItem.totalCount }}</span>
                    </div>
                  </template>
                  <template v-else>
                    <div class="process-dor"></div>
                  </template>
                </div>

                <!-- 下拉模块 -->
                <div
                  class="dropDown-wrap"
                  v-if="!isEmpty(expandeArr[singleItem.id])"
                  :style="{
                    height:
                      !isEmpty(expandeArr[singleItem.id]) && expandeArr[singleItem.id].isExpanded
                        ? 'auto'
                        : 0
                  }"
                  :class="{
                    'expand-item': !(
                      !isEmpty(expandeArr[singleItem.id]) && expandeArr[singleItem.id].isExpanded
                    )
                  }"
                >
                  <div class="mian-title">{{ $t('任务') }}：</div>
                  <template
                    v-for="expandItem of expandeArr[singleItem.id].buyerFormTaskInstanceList"
                  >
                    <div :key="expandItem.id">
                      <div
                        class="task-name"
                        :class="{
                          'grey-task':
                            expandItem.status === 30 ||
                            expandItem.status === 40 ||
                            expandItem.status === 50
                        }"
                      >
                        {{ expandItem.taskName }}
                      </div>
                      <div class="task-status">
                        {{ statusObj[expandItem.status] }}
                      </div>
                    </div>
                  </template>
                  <div class="mian-title mt-20">{{ $t('晋级前置条件') }}：</div>
                  <div class="task-name1">
                    {{ $t('任务完成大于等于') }}
                    {{ expandeArr[singleItem.id].upgradeRuleFront }}
                  </div>
                  <div class="mian-title mt-20">{{ $t('晋级方式') }}：</div>
                  <div class="task-name1">
                    {{ upMethod[expandeArr[singleItem.id].upgradeRuleType] }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import MtTreeView from '@mtech-ui/tree-view'
import utils from '@/utils/utils.js'
import treeitem from './treeitem.vue'
let buyerFormTaskInstanceListMap = new Map()
export default {
  // components: {
  //   'mt-treeView': MtTreeView
  // },
  props: {
    orgId: {
      type: [Number, String],
      default: ''
    }
  },
  watch: {
    searchValue(nv) {
      if (nv) {
        this.searchForCompany(nv)
      } else {
        this.processData1 = JSON.parse(JSON.stringify(this.originProcessData1))
      }
    }
  },
  data() {
    return {
      treeTemplate: function () {
        return {
          template: treeitem
        }
      },
      statusObj: {
        10: this.$t('待填写'),
        20: this.$t('待审批'),
        30: this.$t('已驳回'),
        40: this.$t('已完成'),
        50: this.$t('已关闭')
      },
      upMethod: {
        1: this.$t('自动晋级'),
        2: this.$t('手动晋级')
      },
      searchValue: '',
      processData: [],
      originProcessData: [],
      processData1: [],
      originProcessData1: [],
      isExpanded: false,
      expandeArr: {},

      expandCompanyIdArr: []
    }
  },
  created() {
    let partnerArchiveId = this.$route.query.id
    this.stageQuery(partnerArchiveId, this.orgId)
  },
  methods: {
    isEmpty(value) {
      return utils.isEmpty(value)
    },
    checkNode(id) {
      // 展开的公司id 放入数组 =》 根据数组有值 展开
      if (this.expandCompanyIdArr.includes(id)) {
        let index = this.expandCompanyIdArr.findIndex((v) => v.id === id)
        this.expandCompanyIdArr.splice(index, 1)
      } else {
        this.expandCompanyIdArr.push(id)
      }
    },

    // 搜索筛选
    searchForCompany(value) {
      let reg = new RegExp(value, 'g')
      let tmpProcessData = []
      this.originProcessData1.forEach((v) => {
        if (!!v[0].name && reg.test(v[0].name)) {
          tmpProcessData.push(v)
        }
      })
      this.processData1 = JSON.parse(JSON.stringify(tmpProcessData))
    },

    // 获取阶段列表
    stageQuery(partnerArchiveId, orgId = '') {
      this.$loading()
      // 获取对应的数据
      this.$API.supplierlifecycle
        .stageQuery({ partnerArchiveId: partnerArchiveId, orgId: orgId })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            // 渲染公司树数据
            // this.renderCompanyTree(data);
            this.renderCompanyTree1(data)
          } else {
            this.$toast({
              content: this.$t('获取阶段数据失败，请重试!'),
              type: 'warning'
            })
          }
        })
    },

    // 获取当前id 和 子数组内部id相同的对象的 index
    getChidArrEqualId(buyerStageInstanceList, currentStageId) {
      let index = -1
      for (let idx in buyerStageInstanceList) {
        if (buyerStageInstanceList[idx].id === currentStageId) {
          index = Number(idx)
          break
        }
      }
      return index
    },

    // 渲染公司树 生成一个列为单位的 数组
    renderCompanyTree1(data) {
      let tmpDataArr = []
      data.forEach((item) => {
        // 公司 =》 品类  没有三级菜单 不在继续递归
        // 一个公司下的 多行数组
        let processLineArr = []

        processLineArr.push({
          id: item.id,
          name: item.orgName,
          expanded: false, //展开
          processName: item.accessTemplateName,
          buyerStageInstanceList: item.buyerStageInstanceList,
          currentStageId: this.getChidArrEqualId(item.buyerStageInstanceList, item.currentStageId)
        })

        if (
          !utils.isEmpty(item.buyerPartnerCategoryList) &&
          item.buyerPartnerCategoryList.length > 0
        ) {
          // 树数据
          item.buyerPartnerCategoryList.forEach((cItem) => {
            processLineArr.push({
              id: cItem.id,
              name: cItem.categoryName,
              ...cItem,
              processName: cItem.accessTemplateName,
              buyerStageInstanceList: cItem.buyerStageInstanceList,
              currentStageId: this.getChidArrEqualId(
                cItem.buyerStageInstanceList,
                cItem.currentStageId
              )
            })
          })
        }
        tmpDataArr.push(processLineArr)
      })
      this.processData1 = tmpDataArr
      // 备份
      this.originProcessData1 = JSON.parse(JSON.stringify(tmpDataArr))
      this.$hloading()
    },

    // 点击阶段模块
    stageClick(stageItem, isGrey = false) {
      if (isGrey) {
        return
      }
      this.$loading()
      if (buyerFormTaskInstanceListMap.has(stageItem.id) && !!this.expandeArr[stageItem.id]) {
        let isExpanded = this.expandeArr[stageItem.id].isExpanded
        let buyerFormTaskInstanceList = buyerFormTaskInstanceListMap.get(stageItem.id)
        this.$set(this.expandeArr, stageItem.id, {
          ...buyerFormTaskInstanceList,
          isExpanded: !isExpanded
        })
        this.$hloading()
        return
      }
      this.$API.supplierlifecycle.stageDetail({ stageInstanceId: stageItem.id }).then((res) => {
        let { code, data } = res
        this.$hloading()
        if (code === 200 && !utils.isEmpty(data)) {
          // 渲染公司树数据
          !buyerFormTaskInstanceListMap.has(stageItem.id) &&
            buyerFormTaskInstanceListMap.set(stageItem.id, data)
          this.$set(this.expandeArr, stageItem.id, {
            ...data,
            isExpanded: true
          })
        } else {
          this.$toast({
            content: this.$t('暂无阶段详情!'),
            type: 'warning'
          })
        }
      })
    },

    nodeOnClick(e) {
      console.log(e)
    },

    // 展开
    nodeExpanding(e) {
      this.isExpanded = true
      console.log('nodeExpanded:', e)
    },

    // 缩起
    nodeCollapsing(e) {
      this.isExpanded = false
      console.log('nodeCollapsed:', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: center;
}
.flex1 {
  flex: 1;
}

.none {
  transition: all 0.4s ease-in-out;
  display: none !important;
}

.access-process {
  margin-top: 20px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  // overflow-x: auto;

  .access-banner {
    width: 100%;
    height: 54px;
    line-height: 54px;
    background: rgba(245, 245, 245, 1);
    font-size: 14px;
    position: relative;
    .search-box {
      padding-left: 20px;
      width: 400px;
    }

    .second-box {
      // width: 160px;
      padding-left: 20px;
      color: rgba(41, 41, 41, 1);
    }

    .process-wrap {
      width: 500px;
      display: flex;
      flex-direction: column;
    }

    .process-box {
      width: 500px;
      height: 30px;
      padding-left: 20px;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 20px;
      align-items: flex-start;
    }

    .expand-all {
      position: absolute;
      height: 100%;
      right: 40px;
    }
  }

  .company-bg {
    .mian-wrap {
      background: #fbebd3 !important;
    }
  }

  .access-content {
    background: #fff;
    border-bottom: 1px solid rgba(232, 232, 232, 1);

    .process-line {
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      align-items: flex-start;
      .company-box {
        width: 400px;
        // border-right: 1px solid rgba(232,232,232,1);
      }

      .line-box {
        padding: 20px 0;
        // width: 160px;
        padding-left: 20px;
        font-size: 14px;

        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-left: 1px solid rgba(232, 232, 232, 1);
        overflow: hidden;
        transition: all 0.6s linear;

        .normal-line {
          min-height: 30px;
          line-height: 30px;
          margin-top: 20px;

          .normal-line-item {
            height: 30px;
            line-height: 30px;
          }
        }

        .normal-line:nth-child(1) {
          margin-top: 0 !important;
        }
      }

      .limit-height {
        height: 70px;
        overflow: hidden;
      }
      .max-height {
        height: auto;
        overflow: hidden;
      }

      .process-wrap {
        width: 500px;
        padding: 20px 0;
        overflow: hidden;
        transition: all 0.6s linear;
      }

      .limit-height1 {
        height: 70px;
        overflow: hidden;
      }
      .max-height {
        height: auto;
      }

      .process-box {
        padding-left: 20px;
        margin-top: 20px;
        align-items: flex-start;

        .process-item {
          margin-right: 10px;
          // height: 30px;
          font-size: 14px;
          position: relative;

          .icon-box {
            font-size: 16px;
            margin-right: 5px;
            height: 30px;
            line-height: 30px;
            display: flex;
            align-items: center;
            i {
              line-height: 20px;
              width: 16px;
              height: 16px;
              color: #6386c1;
              opacity: 0.5;
            }
          }

          .process-name {
            // margin-right: 55px;
            min-width: 100px;
            padding: 0 0 0 5px;
          }
          .number-box {
            width: 16px;
            height: 16px;
            line-height: 16px;
            font-size: 14px;
            text-align: center;
            margin-right: 5px;
            background: rgba(0, 70, 156, 1);
            color: #fff;
            border-radius: 100%;
          }
          .process-dor {
            min-width: 38px;
            text-align: right;
            white-space: nowrap;
            .blue-txt {
              color: #00469c;
            }
          }
        }

        .mian-wrap {
          display: flex;
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          border-radius: 4px;
          justify-content: space-between;
          background: rgba(220, 235, 255, 1);
          align-items: center;
          cursor: pointer;
        }

        .dropDown-wrap {
          // height: 0;
          overflow: hidden;

          padding: 10px;
          background: #fafafa;
          border-left: 1px solid rgba(251, 235, 211, 1);
          border-right: 1px solid rgba(251, 235, 211, 1);
          border-bottom: 1px solid rgba(251, 235, 211, 1);

          .mian-title {
            font-size: 12px;

            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            margin-bottom: 10px;
          }

          .task-name {
            margin-bottom: 5px;
            font-size: 12px;

            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            margin-top: 5px;
          }

          .task-name1 {
            margin-bottom: 5px;
            font-size: 12px;

            font-weight: 500;
            color: #9a9a9a;
            margin-top: 5px;
          }

          .task-status {
            font-size: 10px;

            font-weight: normal;
            color: rgba(154, 154, 154, 1);
            transform: scale(1, 0.9);
          }

          .grey-task {
            color: #9a9a9a;
          }

          .mt-20 {
            margin-top: 20px;
          }
        }

        .expand-item {
          padding: 0 !important;
        }

        .opacity {
          background: rgba(220, 235, 255, 1);
          border-radius: 4px;
          color: #292929;
          .mian-wrap {
            opacity: 0.5;
          }
        }
        .grey {
          color: rgba(154, 154, 154, 1);
          background: rgba(245, 245, 245, 1) !important;
          .number-box {
            background: #9a9a9a;
            color: #fff;
          }
          .mian-wrap {
            background: rgba(245, 245, 245, 1);
          }
        }
      }
      .process-box:nth-child(1) {
        margin-top: 0 !important;
      }
    }

    // ***********
    // 新的样式下的
    // ***********
    // ***********
    // ***********

    .process-line2 {
      padding: 0px 0 20px 20px;
      flex-direction: column;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      align-items: flex-start;
      position: relative;
      transition: all 0.6s linear;
      overflow-x: auto;

      .left-line {
        display: inline-block;
        width: 1px;
        height: calc(100% - 60px);
        background: #9baac1;
        left: 32px;
        top: 40px;
        position: absolute;
      }

      &::before {
        content: '';
        display: inline-block;
        width: 1px;
        height: 100%;
        background: #e8e8e8;
        position: absolute;
        left: 400px;
        top: 0;
      }

      &::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 100%;
        background: #e8e8e8;
        position: absolute;
        right: 500px;
        top: 0;
      }

      .company-box {
        width: calc(400px - 20px);
        position: relative;
      }

      .children-line {
        &::before {
          content: ' ';
          display: inline-block;
          width: 12px;
          height: 1px;
          background: #9baac1;
          position: absolute;
          left: 12px;
          top: 50%;
        }
      }

      .line-box {
        height: 30px;
        line-height: 30px;
        // width: 160px;
        padding-left: 20px;
        font-size: 14px;

        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        // border-right: 1px solid rgba(232, 232, 232, 1);
        // border-left: 1px solid rgba(232, 232, 232, 1);
        overflow: hidden;
        transition: all 0.6s linear;

        .normal-line {
          min-height: 30px;
          line-height: 30px;
          margin-top: 20px;

          .normal-line-item {
            height: 30px;
            line-height: 30px;
          }
        }

        .normal-line:nth-child(1) {
          margin-top: 0 !important;
        }
      }

      .limit-height {
        height: 70px;
        overflow: hidden;
      }
      .max-height {
        height: auto;
        overflow: hidden;
      }

      .process-wrap {
        width: 500px;
        // height: 30px;
        overflow: hidden;
        transition: all 0.6s linear;
      }

      .limit-height1 {
        height: 70px;
        overflow: hidden;
      }
      .max-height {
        height: auto;
      }

      .process-box {
        padding-left: 20px;
        margin-top: 20px;
        align-items: flex-start;

        .process-item {
          margin-right: 10px;
          // height: 30px;
          font-size: 14px;
          position: relative;

          .icon-box {
            font-size: 16px;
            margin-right: 5px;
            height: 30px;
            line-height: 30px;
            display: flex;
            align-items: center;
            i {
              line-height: 20px;
              width: 16px;
              height: 16px;
              color: #6386c1;
              opacity: 0.5;
            }
          }

          .process-name {
            min-width: 100px;
            padding: 0 0 0 5px;
          }
          .number-box {
            width: 16px;
            height: 16px;
            line-height: 16px;
            font-size: 14px;
            text-align: center;
            margin-right: 5px;
            background: rgba(0, 70, 156, 1);
            color: #fff;
            border-radius: 100%;
          }
          .process-dor {
            min-width: 38px;
            text-align: right;
            white-space: nowrap;
            .blue-txt {
              color: #00469c;
            }
          }
        }

        .mian-wrap {
          display: flex;
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          border-radius: 4px;
          justify-content: space-between;
          background: rgba(220, 235, 255, 1);
          align-items: center;
          cursor: pointer;
        }

        .dropDown-wrap {
          overflow: hidden;

          padding: 10px;
          background: #fafafa;
          border-left: 1px solid rgba(251, 235, 211, 1);
          border-right: 1px solid rgba(251, 235, 211, 1);
          border-bottom: 1px solid rgba(251, 235, 211, 1);

          .mian-title {
            font-size: 12px;

            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            margin-bottom: 10px;
          }

          .task-name {
            margin-bottom: 5px;
            font-size: 12px;

            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            margin-top: 5px;
          }

          .task-name1 {
            margin-bottom: 5px;
            font-size: 12px;

            font-weight: 500;
            color: #9a9a9a;
            margin-top: 5px;
          }

          .task-status {
            font-size: 10px;

            font-weight: normal;
            color: rgba(154, 154, 154, 1);
            transform: scale(1, 0.9);
          }

          .grey-task {
            color: #9a9a9a;
          }

          .mt-20 {
            margin-top: 20px;
          }
        }

        .expand-item {
          padding: 0 !important;
        }

        .opacity {
          background: rgba(220, 235, 255, 1);
          border-radius: 4px;
          color: #292929;
          .mian-wrap {
            opacity: 0.5;
          }
        }
        .grey {
          color: rgba(154, 154, 154, 1);
          background: rgba(245, 245, 245, 1) !important;
          .number-box {
            background: #9a9a9a;
            color: #fff;
          }
          .mian-wrap {
            background: rgba(245, 245, 245, 1);
          }
        }
      }
      .process-box:nth-child(1) {
        margin-top: 0 !important;
      }
      .process-inline {
        width: 100%;
        margin-top: 20px;
        align-items: flex-start;

        .company-box,
        .parent-line {
          cursor: pointer;

          .arrow-wrap {
            height: 30px;
            line-height: 30px;

            i {
              transition: all 0.2s linear;
              display: inline-block;
              width: 24px;
              height: 24px;
              line-height: 24px;
              text-align: center;
              font-size: 12px;
              color: #9baac1;
              transform: rotate(90deg);
            }

            .arrow-txt {
              font-size: 14px;
              color: #292929;
              padding-left: 2px;
            }
          }
        }
        .children-line {
          padding-left: 30px;
        }
      }
      .active-expend {
        .arrow-wrap {
          .mt-icons {
            transform: rotate(0deg) !important;
          }
        }
      }
    }
  }
  .access-content:last-child {
    border-bottom: none;
  }
}
</style>
<style lang="scss">
.access-process {
  .ivu-collapse-header {
    border-bottom: 1px solid #e8e8e8;
  }
  .sub-item {
    border-bottom: 1px solid #e8e8e8;
  }
  .e-input-group {
    border: none;
  }

  .access-content {
    .process-line {
      .company-box {
        .e-treeview > .e-ul {
          padding: 20px 0 20px 20px;
          margin-top: 0 !important;
          .e-list-item {
            margin-top: 0;
          }
        }

        .e-treeview .e-ul {
          li {
            padding: 0;
          }
        }

        .e-treeview .e-list-text {
          font-size: 14px;
          color: #292929;
        }

        .e-treeview .e-level-2 {
          margin-top: 20px !important;
        }
      }
    }
  }
}
</style>
