<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierStage" class="form-item" :label="$t('目标阶段')">
              <mt-select
                v-model="formInfo.supplierStage"
                :width="400"
                :data-source="stageList"
                :fields="{ text: 'stageName', value: 'id' }"
                :show-clear-button="true"
                @change="handleChangeStage"
                :placeholder="$t('请选择目标阶段')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyUserId" class="form-item" :label="$t('申请人')">
              <mt-select
                ref="userRef"
                v-model="formInfo.applyUserId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="companyId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.companyId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyDepId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyDepId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                :multiline="true"
                :rows="2"
                maxlength="200"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, areaColumnData } from '../config/index.js'
export default {
  data() {
    return {
      stageInfo: {},
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        supplierEnterpriseName: '',
        supplierStage: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        applyerOrgName: '',
        applyerOrgCode: '',
        applyerDeptName: '',
        applyerName: ''
      },
      rules: {
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        supplierStage: [{ required: true, message: this.$t('请选择目标阶段'), trigger: 'blur' }],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmAndJump,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      componentConfig: [
        {
          gridId: 'f4d4b37c-0e18-4b92-8cba-bf760c40d692',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: areaColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    dataInfo() {
      return this.modalData.dataInfo
    },
    applyUserIdData() {
      return this.modalData.dataInfo.TenantUserList
    },
    stageList() {
      let stageList = []
      let currentTab = this.modalData.dataInfo.currentTab
      let oldStageList = this.modalData.dataInfo.stageList
      if (this.modalData.type === 'stageUpgrade') {
        stageList = oldStageList.slice(currentTab + 1)
      } else {
        stageList = oldStageList.slice(0, currentTab)
      }
      return stageList
    }
  },
  created() {
    // console.log(1111,this.modalData)
  },
  mounted() {
    this.show()
    console.log(this.dataInfo)
    this.formInfo.applyUserId = this.modalData.dataInfo.applyUserId
    this.formInfo.applyUser = this.modalData.dataInfo.applyUser
    // 出发change
    this.$set(this.componentConfig[0].grid, 'dataSource', this.dataInfo.sltList)
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    // 提交
    confirm(fn) {
      let formInfo = this.formInfo
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }

      let applyInfo = {
        applyName: formInfo.supplierEnterpriseName,
        applyerOrgId: formInfo.companyId,
        applyerOrgName: formInfo.applyerOrgName,
        applyerDeptId: formInfo.applyDepId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyUserId,
        applyerName: formInfo.applyUser,
        remark: formInfo.remark
      }

      let applyStage = {
        businessType: this.type,
        // 后加
        orgId: formInfo.companyId,
        orgCode: formInfo.applyerOrgCode,
        orgName: formInfo.applyerOrgName,
        destinationStageTemplateId: this.stageInfo.id,
        destinationStageTemplateName: this.stageInfo.stageName,
        sourceStageTemplateId:
          this.modalData.dataInfo.stageList[this.modalData.dataInfo.currentTab].id,
        sourceStageTemplateName:
          this.modalData.dataInfo.stageList[this.modalData.dataInfo.currentTab].stageName
      }

      let relationList = this.componentConfig[0].grid.dataSource.map((item) => {
        return { partnerRelationId: item.id }
      })

      if (relationList.length === 0) {
        this.$toast({
          content: this.$t('新增失败，请至少有一个勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        applyInfo,
        applyStage,
        relationList
      }
      this.$loading()

      this.$API.supplierAcc
        .stageAdd(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            if (!!fn && typeof fn === 'function') {
              this.$toast({ content: this.$t('新增成功，即将跳转'), type: 'warning' })
              setTimeout(() => {
                fn(res.data)
              }, 400)
            } else {
              this.$toast({ content: this.$t('新增成功'), type: 'warning' })
              this.$emit('confirm-function', 'reload')
            }
          } else {
            this.$toast({ content: this.$t('新增失败，请重试'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('新增失败，请重试'),
            type: 'warning'
          })
        })
    },

    confirmAndJump() {
      this.confirm((data) => {
        this.$emit('confirm-function', { type: 'jump', data })
      })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (sltList.filter((citem) => citem.id === item.id).length === 0) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },
    handleClickCellTool() {},
    handleClickCellTitle() {},

    handleUserChange(data) {
      let { itemData } = data
      // this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.fullName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      this.stageInfo = itemData
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            // this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    handleCompanyChange(data) {
      let { itemData } = data
      console.log(data, itemData)
      // this.formInfo.companyId = itemData.id
      this.formInfo.applyerOrgName = itemData.orgName
      this.formInfo.applyerOrgCode = itemData.orgCode
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
.upgrade-dialog {
  padding: 20px; //--*

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
</style>
