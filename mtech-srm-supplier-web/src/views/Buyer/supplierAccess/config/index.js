import { i18n } from '@/main.js'
export const accessToolbarA = [
  { id: 'upGrade', icon: 'icon_table_upgrade', title: i18n.t('手动升级') }
]

export const accessToolbarB = [
  { id: 'upGrade', icon: 'icon_table_upgrade', title: i18n.t('手动升级') },
  { id: 'demotion', icon: 'icon_table_downgrade', title: i18n.t('手动降级') }
]

export const accessToolbarC = [
  { id: 'demotion', icon: 'icon_table_downgrade', title: i18n.t('手动降级') }
]
import Vue from 'vue'
export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '210',
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    width: '130',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '210',
    field: 'contactName',
    headerText: i18n.t('阶段进度'),
    template: () => {
      return {
        template: Vue.component('operate-template', {
          template: `
            <div class="tp-box" style="color: #00469C">{{ countRate }}</div>
          `,
          data: function () {
            return { data: {} }
          },
          computed: {
            countRate() {
              return this.data.successCount + '/' + this.data.totalCount
            }
          }
        })
      }
    }
  },
  {
    width: '210',
    field: 'contactPhone',
    headerText: i18n.t('待处理任务'),
    template: () => {
      return {
        template: Vue.component('operate-template', {
          template: `
            <div class="tp-box" style="color: #00469C">{{ countRate }}</div>
          `,
          data: function () {
            return { data: {} }
          },
          computed: {
            countRate() {
              return this.data.totalCount - this.data.successCount
            }
          }
        })
      }
    }
  },
  {
    width: '160',
    field: 'upgradeDate',
    headerText: i18n.t('晋级本阶段时间'),
    queryType: 'date',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              if (
                this.data.upgradeDate == null ||
                this.data.upgradeDate == '' ||
                this.data.upgradeDate == undefined
              ) {
                return ''
              } else {
                let timeStr = this.data.upgradeDate.replace(/T/gi, ' ')
                timeStr = timeStr.replace(/\.000\+0000/gi, '')
                return timeStr
              }
            }
          }
        })
      }
    }
  },
  {
    width: '160',
    field: 'supplierGradeName',
    headerText: i18n.t('分级')
  },
  {
    width: '160',
    field: 'shareDetails',
    headerText: i18n.t('共享情况')
  },
  {
    width: '160',
    field: 'createUserName',
    headerText: i18n.t('邀请人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('邀请时间'),
    queryType: 'date',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              if (
                this.data.createTime == null ||
                this.data.createTime == '' ||
                this.data.createTime == undefined
              ) {
                return ''
              } else {
                let timeStr = this.data.createTime.replace(/T/gi, ' ')
                timeStr = timeStr.replace(/\.000\+0000/gi, '')
                return timeStr
              }
            }
          }
        })
      }
    }
  }
]

export const supplyAreaToolbar = [
  // {
  //   id: "addNew",
  //   icon: "icon_solid_Newinvitation",
  //   title: i18n.t("新增"),
  // },
  {
    id: 'deleteList',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]

export const areaColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '100',
    field: 'supplierEnterpriseCode',
    cssClass: 'field-content',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '100',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  }
]
