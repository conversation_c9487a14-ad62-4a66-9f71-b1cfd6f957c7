<template>
  <div class="supplier-access">
    <div class="company-right">
      <mt-DropDownTree
        :fields="companyFields"
        :show-check-box="false"
        class="mt-down-tree"
        :class="{ 'empty-color': !initCompanyValue }"
        id="checkboxTreeSelect"
        :placeholder="initCompanyValue || $t('组织树状图，可选至任意层级、单选')"
        :allow-multi-selection="false"
        :auto-check="false"
        v-model="selectCompanyId"
        @select="companyChange"
      ></mt-DropDownTree>
    </div>

    <template v-if="!emptyData">
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="false"
        :padding-top="false"
        :use-tool-template="false"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
        @handleSelectTab="handleSelectTab"
      >
      </mt-template-page>
    </template>
    <!-- 空的样式 -->
    <template v-else>
      <div class="empty-container">
        <img src="../../../assets/emptyData.png" />
        <div class="empty-txt">{{ emptyMsg }}</div>
      </div>
    </template>

    <router-view></router-view>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import { accessToolbarA, accessToolbarB, accessToolbarC, columnDataMain } from './config/index.js'
export default {
  data() {
    return {
      applyUserIdData: [],
      stageList: [], // 阶段数组
      emptyData: false, // 没有阶段模板导致的报错
      emptyMsg: this.$t('暂无数据'),
      selectCompanyId: '', //当前选择的公司id
      initCompanyValue: '',
      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      currentTab: 0,
      componentConfig: [],
      TenantUserList: [], // 当前租户下的用户
      applyUserId: '', // 当前用户的id
      applyerName: '', // 当前用户的name
      upDownTypeList: []
    }
  },
  beforeRouteEnter(to, from, next) {
    next(() => {
      console.log(to)
      if (from.name === 'lifecycledetailsimple') {
        // 回跳 回显的问题
        // let jumpCompanyMsg = {}
        try {
          // jumpCompanyMsg = window.sessionStorage.getItem('jumpCompanyMsg')
          // if (!!jumpCompanyMsg.orgId && !!companyId.stageTemplateId) {
          //   // vm.
          // }
        } catch (error) {
          console.log(error)
        }
      }
      console.log(from)
    })
  },
  created() {
    /**
     *  首先获取 getuserinfo id和 租户下的公司 companyid 来默认请求 阶段列表 queryStageTempalte （平台没有返回公司id 后续加）
     *  用户自主选择公司 请求阶段 =》 阶段请求 grid列表
     */

    // 获取当前租户下的用户 (判断登录用户是否在当前租户底下 （暂时去除）)
    let getCurrentTenantUsers = this.getCurrentTenantUsers()
    // 获取当前账户信息
    let queryUserInfo = this.queryUserInfo()
    // 租户下所有公司
    let getCompanyTree = this.getCompanyTree()

    Promise.all([getCurrentTenantUsers, queryUserInfo, getCompanyTree])
      .then((result) => {
        // console.log(result)
        let currentTenantUser = result[0]
        let userInfoInfo = result[1]
        let companyTree = result[2]

        let TenantUserList = []
        let userInfo = {}
        let companyTreeList = []

        if (
          !utils.isEmpty(currentTenantUser) &&
          currentTenantUser.code === 200 &&
          !utils.isEmpty(currentTenantUser.data)
        ) {
          TenantUserList = currentTenantUser.data
          this.TenantUserList = TenantUserList
        }
        if (
          !utils.isEmpty(userInfoInfo) &&
          userInfoInfo.code === 200 &&
          !utils.isEmpty(userInfoInfo.data)
        ) {
          userInfo = userInfoInfo.data
        }
        if (
          !utils.isEmpty(companyTree) &&
          companyTree.code === 200 &&
          !utils.isEmpty(companyTree.data)
        ) {
          companyTreeList = companyTree.data
          this.companyFields = Object.assign({}, this.companyFields, {
            dataSource: companyTreeList
          })
        } else {
          this.$toast({ content: this.$t('获取公司信息失败！'), type: 'warning' })
          this.emptyMsg = this.$t('获取公司信息失败！')
          this.emptyData = true
          // 空数据 重置
          this.companyFields = {
            dataSource: [],
            value: 'id',
            test: 'orgCode',
            text: 'name',
            child: 'children'
          }
        }

        // let applyUserObj = TenantUserList.find(
        //   (item) => item.id == userInfo.uid
        // )

        // this.applyUserId = applyUserObj.id
        // this.applyUser = applyUserObj.fullName
        this.applyUserId = userInfo.uid
        this.applyUser = userInfo.username

        // 根据用户id 获取当前用户所属的公司列表 = 获取默认已选公司
        this.getCompany(userInfo.uid)
      })
      .catch(() => {
        this.$toast({ content: this.$t('获取用户信息失败！请重试！'), type: 'warning' })
        this.emptyMsg = this.$t('获取用户信息失败！请重试！')
        this.emptyData = true
      })
  },

  methods: {
    // 获取当前用户信息
    queryUserInfo() {
      return new Promise((resolve) => {
        this.$API.supplierAcc
          .queryUserInfo()
          .then((result) => {
            resolve(result)
          })
          .catch(() => {
            resolve({})
          })
      })
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      return new Promise((resolve) => {
        this.$API.supplierAcc
          .getCurrentTenantUsers()
          .then((result) => {
            resolve(result)
          })
          .catch(() => {
            resolve({})
          })
      })
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany(applyUserId) {
      this.$API.supplierAcc.getCompanysByUserId({ userId: applyUserId }).then((res) => {
        if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
          // 获取默认选择的公司
          this.selectCompanyId = !!res.data[0] && !!res.data[0].id ? [res.data[0].id] : ''
          this.initCompanyValue = !!res.data[0] && !!res.data[0].id ? res.data[0].orgName : '' // 模拟初始化数据
          this.queryStageTempalte(this.selectCompanyId[0])
        }
      })
    },

    // 根据用户id来获取公司树 不能按照需求来完成 后端暂时没有接口 包括品类也是根据账户信息来获取
    getCompanyTree() {
      return new Promise((resolve) => {
        // this.$API.supplierInvitation
        //   .findOrganizationCompanyTreeByAccount({
        //     accountId: 0,
        //   })
        this.$API.supplierInvitation
          .findOrganizationCompanyTreeByAccount2({
            organizationLevelCodes: ['ORG02', 'ORG01'],
            orgType: 'ORG001PRO',
            includeItself: true
          })
          .then((result) => {
            resolve(result)
          })
          .catch(() => {
            resolve({})
          })
      })
    },

    // 根据用户id来获取公司树 不能按照需求来完成 后端暂时没有接口 包括品类也是根据账户信息来获取
    getCompanyTree1() {
      // this.$API.supplierInvitation
      //   .findOrganizationCompanyTreeByAccount({
      //     accountId: 0,
      //   })
      this.$API.supplierInvitation
        .findOrganizationCompanyTreeByAccount2({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        })
        .then((res) => {
          let { data } = res
          if (res.code === 200 && !utils.isEmpty(data)) {
            // data[0].selected = true
            this.companyFields = Object.assign({}, this.companyFields, {
              dataSource: data
            })
          } else {
            this.$toast({ content: this.$t('获取公司信息失败！'), type: 'warning' })
            this.emptyMsg = this.$t('获取公司信息失败！')
            // 空数据 重置
            this.companyFields = {
              dataSource: [],
              value: 'id',
              test: 'orgCode',
              text: 'name',
              child: 'children'
            }
          }
        })
    },

    companyChange(node) {
      if (node.id) {
        this.initCompanyValue = ''
        this.selectCompanyId = node.id
        this.queryStageTempalte(node.id)
      }
    },

    // 根据公司id 获取阶段模板
    queryStageTempalte(companyId) {
      this.emptyData = false
      this.$API.supplierAcc
        .queryStageTempalte({
          orgId: companyId
        })
        .then((result) => {
          let { data } = result
          if (result.code === 200 && !utils.isEmpty(data)) {
            this.stageList = data
            this.renderGridData(data, companyId)
          } else {
            // 值空假数据
            this.emptyData = true
            this.emptyMsg = this.$t('获取准入阶段失败！')
            this.$toast({
              content: result.msg || this.$t('获取准入阶段失败！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          // 值空假数据
          this.emptyData = true
          this.emptyMsg = this.$t('获取准入阶段失败！')
          this.$toast({
            content: error.msg || this.$t('获取准入阶段失败！'),
            type: 'warning'
          })
        })
    },

    // 渲染多tab grid 的数据传值
    renderGridData(processData, companyId) {
      let componentConfig = processData.map((item, index) => {
        let toolbar =
          index === 0
            ? accessToolbarA
            : index === processData.length - 1
            ? accessToolbarC
            : accessToolbarB
        return {
          title: item.stageName,
          toolbar: toolbar,
          grid: {
            columnData: columnDataMain,
            asyncConfig: {
              url: '/supplier/tenant/buyer/process/manager/query',
              params: {
                orgId: companyId,
                stageTemplateId: item.id
              }
            }
          }
        }
      })
      this.componentConfig = componentConfig
    },

    // 升级 降低 按钮
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      let dataInfo = {
        selectCompanyId:
          !!this.selectCompanyId && Array.isArray(this.selectCompanyId)
            ? this.selectCompanyId[0]
            : this.selectCompanyId,
        sltList: gridRef.getMtechGridRecords(),
        applyUserId: this.applyUserId,
        applyUser: this.applyUser,
        TenantUserList: this.TenantUserList,
        stageList: this.stageList,
        currentTab: this.currentTab
      }
      // 升级
      if (toolbar.id === 'upGrade') {
        this.$dialog({
          modal: () => import('./components/addUpgradeDialog'),
          data: {
            title: this.$t('新增手动升级申请单'),
            type: 'stageUpgrade',
            dataInfo
          },
          success: (data) => {
            if (!utils.isEmpty(data)) {
              let { type, data: dataChild } = data
              if (type === 'jump') {
                this.$router.push({
                  path: '/supplier/supplierzidong',
                  query: {
                    id: dataChild.id
                  }
                })
              }
            }
            this.$refs.templateRef.refreshCurrentGridData()
          },
          close: () => {}
        })
        // this.$t("降级")
      } else if (toolbar.id === 'demotion') {
        this.$dialog({
          modal: () => import('./components/addUpgradeDialog'),
          data: {
            title: this.$t('新增手动降级申请单'),
            type: 'stageDownGrade',
            dataInfo
          },
          success: (data) => {
            if (!utils.isEmpty(data)) {
              let { type, data: dataChild } = data
              if (type === 'jump') {
                this.$router.push({
                  path: '/supplier/supplierzidong',
                  query: {
                    id: dataChild.id
                  }
                })
              }
            }
            this.$refs.templateRef.refreshCurrentGridData()
          },
          close: () => {}
        })
      }
    },
    handleClickCellTool() {},
    handleSelectTab(e) {
      this.currentTab = e
    },
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      try {
        window.sessionStorage.setItem(
          'jumpCompanyMsg',
          JSON.stringify({
            orgId: data.orgId,
            stageTemplateId: this.currentTab
          })
        )
      } catch (error) {
        console.log(error)
      }
      let { data } = e
      this.$router.push({
        name: 'lifecycledetailsimple',
        query: {
          id: data.partnerArchiveId, // 档案id
          orgId: data.orgId // 公司id
        }
      })
    }
  }
}
</script>

<style lang="scss">
.supplier-access {
  height: 100%;
  .mt-down-tree {
    color: #000;
    input::placeholder {
      color: #000 !important;
    }
    /deep/input.e-input::placeholder {
      color: #000 !important;
    }
  }
  .empty-color {
    color: #999 !important;
    input::placeholder {
      color: #ddd !important;
    }
    /deep/input.e-input::placeholder {
      color: #ddd !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.supplier-access {
  width: 100%;

  .company-right {
    position: absolute;
    top: 12px;
    right: 20px;
    width: 300px;
    height: 30px;
    z-index: 9;
  }

  .empty-container {
    height: 240px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);

    img {
      display: block;
      width: 300px;
      height: 200px;
    }

    .empty-txt {
      text-align: center;
      margin-top: 20px;
      font-size: 14px;
      color: #9a9a9a;
    }
  }
}
</style>
