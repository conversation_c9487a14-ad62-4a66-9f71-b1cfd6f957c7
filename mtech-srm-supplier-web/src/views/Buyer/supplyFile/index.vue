<template>
  <div class="lifeCycle-container">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnDataMain } from './config/index.js'
import * as XLSX from 'xlsx'
export default {
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$hloading()
    })
  },
  data() {
    return {
      sampleData: [],
      statusMap: {
        1: this.$t('注册'),
        2: this.$t('潜在'),
        10: this.$t('合格'),
        20: this.$t('冻结'),
        30: this.$t('黑名单'),
        40: this.$t('退出')
      },
      componentConfig: [
        {
          gridId: '965ab2bd-50ba-4efe-baa1-b632edf632b8',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'export1', icon: 'icon_table_new', title: this.$t('导出') }
                // { id: "edit", icon: "icon_table_new", title: this.$t("品类扩充") },
              ],
              // { id: "importData", icon: "icon_solid_Import", title: "导入" },
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnDataMain,
            asyncConfig: {
              url: '/supplier/tenant/supplier/process/access/query',
              serializeList: (list) => {
                this.sampleData = list
                return list
              }
            }
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    handleClickToolBar(e) {
      let record = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id == 'edit') {
        // 编辑
        if (record.length == 1) {
          let title = ''
          let type = ''
          let row = record[0].categoryRelationDTOList
          // 如果当前数据供应品类有值，则调用编辑弹窗，如果没有值则新增弹窗
          if (row.length > 0) {
            title = this.$t('编辑')
            type = 'archivEdit'
          } else {
            title = this.$t('新增')
            type = 'add'
          }
          this.$dialog({
            modal: () => import('../registrationManagement/components/addRegister.vue'),
            data: {
              title: title,
              type: type,
              record: row
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择一条数据进行品类扩充'),
            type: 'warning'
          })
        }
      }
      if (e.toolbar.id == 'export1') {
        let data = []
        if (record.length == 0) {
          data = this.sampleData
        } else {
          data = record
        }
        let tableData = [
          [
            this.$t('客户编码'),
            this.$t('客户名称'),
            this.$t('供应品类'),
            this.$t('注册时间'),
            this.$t('供应商状态')
          ]
        ]
        data.forEach((item) => {
          let arr = []
          arr.push(
            item.customerCode,
            item.customerName,
            item.categoryRelationDTOList.map((e) => e.customerCategoryName).join(),
            item.createDate,
            this.statusMap[item.status]
          )
          tableData.push(arr)
        })
        let ws = XLSX.utils.aoa_to_sheet(tableData)
        let wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
        XLSX.writeFile(wb, this.$t(`导出表格数据.xlsx`))
      }
    },
    fn() {},
    handleClickCellTool() {},
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'fileDetail',
        query: {
          partnerArchiveId: data.id,
          orgId: data.customerOrgId,
          orgName: escape(data.customerName),
          orgCode: data.customerCode,
          supplierEnterpriseId: data.customerEnterpriseId,
          partnerRelationCode: data.partnerRelationCode,
          status: data.status
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lifeCycle-container {
  width: 100%;
  height: 100%;
}
</style>
