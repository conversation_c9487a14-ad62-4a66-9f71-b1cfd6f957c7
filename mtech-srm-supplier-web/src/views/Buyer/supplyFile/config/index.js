import Vue from 'vue'
import utils from '@/utils/utils'
import { i18n } from '../../../../main'
export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '210',
    field: 'customerCode',
    headerText: i18n.t('客户编码'),
    cellTools: []
  },
  {
    width: '130',
    field: 'customerName',
    headerText: i18n.t('客户名称')
  },
  {
    width: '200',
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM')
  },
  {
    width: '200',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
  },
  {
    width: '150',
    field: 'categoryRelationDTOList',
    headerText: i18n.t('供应品类'),
    ignore: true,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        let arr = []
        e.forEach((item) => {
          arr.push(item.customerCategoryName)
        })
        return arr.join()
      }
    }
  },
  {
    width: '100',
    field: 'createDate',
    headerText: i18n.t('注册时间'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    }
  },
  {
    width: '100',
    field: 'status',
    headerText: i18n.t('供应商状态'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { key: '1', value: '注册' },
        { key: '2', value: '潜在' },
        { key: '10', value: '合格' },
        { key: '20', value: '冻结' },
        { key: '30', value: '黑名单' },
        { key: '40', value: '退出' }
      ],
      fields: { text: 'value', value: 'key' }
    },
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

            <span v-if="data.status == 1 || data.status == 10" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(99,134,193,0.1);
              color: #6386C1;
            " >{{ statusMap[data.status] }}</span>
            <span v-if="data.status == 2" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(237,86,51,.1);
              color: #ED5633;
            " >{{ statusMap[data.status] }}</span>
            <span v-if="data.status == 20 || data.status == 30 || data.status == 40" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(154,154,154,.1);
              color: #9A9A9A;
            " >{{ statusMap[data.status] }}</span>

            </div>`,
          data: function () {
            return {
              data: {},
              statusMap: {
                1: i18n.t('注册'),
                2: i18n.t('潜在'),
                10: i18n.t('合格'),
                20: i18n.t('冻结'),
                30: i18n.t('黑名单'),
                40: i18n.t('退出')
              }
            }
          }
        })
      }
    }
  }
]
export const shipStructureInfo = [
  {
    field: 'stockHolder',
    headerText: i18n.t('股东')
  },
  {
    field: 'shareholdingRatio',
    headerText: i18n.t('持股比例')
  },
  {
    field: 'beneficialShares',
    headerText: i18n.t('最终受益股份')
  },
  {
    field: 'capitalContributionMoney',
    headerText: i18n.t('认缴出资额')
  },
  {
    field: 'capitalContributionDate',
    headerText: i18n.t('认缴出资日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const contactsInfo = (postList, acceptList) => [
  {
    width: '100',
    field: 'contactName',
    headerText: i18n.t('姓名')
  },
  {
    width: '100',
    field: 'contactSex',
    headerText: i18n.t('性别'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('男'), 2: i18n.t('女') }
    }
  },
  {
    width: '100',
    field: 'contactDept',
    headerText: i18n.t('部门')
  },
  {
    width: '100',
    field: 'contactPost',
    headerText: i18n.t('职务'),
    valueAccessor: function (field, data) {
      let dataSource = postList || []
      return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
    }
  },
  {
    width: '100',
    field: 'contactTel',
    headerText: i18n.t('电话')
  },
  {
    width: '100',
    field: 'contactMobile',
    headerText: i18n.t('手机')
  },
  {
    width: '150',
    field: 'contactMail',
    headerText: i18n.t('电子邮箱')
  },
  {
    width: '200',
    field: 'acceptMsg',
    headerText: i18n.t('接受信息类型'),
    valueAccessor: function (field, data) {
      let value = []
      let dataSource = acceptList || []
      try {
        let res = []
        value = JSON.parse(data[field])
        if (value && value.length > 0) {
          value.forEach((item) => {
            dataSource.forEach((c) => {
              if (item === c.itemCode) {
                console.log(item)
                res.push(c.itemName)
              }
            })
          })
        }
        return res.join(',')
      } catch (error) {
        console.error(error) //处理脏数据的情况
        return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
      }
    }
  },
  {
    width: '100',
    field: 'contractQq',
    headerText: i18n.t('QQ')
  },
  {
    width: '100',
    field: 'contactWechat',
    headerText: i18n.t('微信')
  }
]
export const bankInfo = (provinceList) => [
  {
    width: '150',
    field: 'bankAccount',
    headerText: i18n.t('银行账号')
  },
  {
    width: '150',
    field: 'accountName',
    headerText: i18n.t('开户名称')
  },
  {
    width: '150',
    field: 'bankName',
    headerText: i18n.t('银行名称')
  },
  {
    width: '150',
    field: 'bankProvince',
    headerText: i18n.t('开户行省份'),
    valueAccessor: function (field, data) {
      let dataSource = provinceList || []
      return dataSource.filter((i) => i.areaCode == data[field])?.[0]?.areaName
    }
  },
  {
    width: '150',
    field: 'bankCity',
    headerText: i18n.t('开户行市')
  },
  {
    width: '150',
    field: 'bankOutlet',
    headerText: i18n.t('开户银行网点')
  },
  {
    width: '150',
    field: 'bankCode',
    headerText: i18n.t('开户行联行号')
  }
]
export const certificateInfoColumn = (certificateList) => [
  {
    width: '150',
    field: 'certificateType',
    headerText: i18n.t('证书类型'),
    valueAccessor: function (field, data) {
      let dataSource = certificateList || []
      return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
    }
  },
  {
    width: '150',
    field: 'certCode',
    headerText: i18n.t('证书编号')
  },
  {
    width: '150',
    field: 'certName',
    headerText: i18n.t('证书名称')
  },
  {
    width: '150',
    field: 'certificateBody',
    headerText: i18n.t('认证机构')
  },
  {
    width: '150',
    field: 'annualReviewDate',
    headerText: i18n.t('年审日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'effectiveDate',
    headerText: i18n.t('生效日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'expireDate',
    headerText: i18n.t('截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'approved',
    headerText: i18n.t('是否审核通过'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    width: '150',
    field: 'attachment',
    headerText: i18n.t('附件'),
    template: () => {
      return {
        template: Vue.component('attachmentTemplate', {
          template: `
              <div style="width:100%" >
                <span class="downStyle" v-for="(item,index) in attachmentLists" :key="index"
                @click="downAttachment(item)">{{item.fileName}}</span>
              </div>
              `,
          data: function () {
            return {
              data: {},
              attachmentLists: []
            }
          },
          mounted() {
            this.attachmentLists = this.data.attachment
          },
          methods: {
            downAttachment(item) {
              this.$utils.downloadAttachment(item)
            }
          }
        })
      }
    }
  }
]
export const patentInfoColumn = [
  {
    width: '150',
    field: 'originFactoryName',
    headerText: i18n.t('原厂名（中文）')
  },
  {
    width: '150',
    field: 'originFactoryEnglishName',
    headerText: i18n.t('原厂名（英文）')
  },
  {
    width: '150',
    field: 'originContactName',
    headerText: i18n.t('原厂联系人')
  },
  {
    width: '150',
    field: 'originContactTel',
    headerText: i18n.t('原厂联系人电话')
  },
  {
    width: '150',
    field: 'proxyCertificate',
    headerText: i18n.t('代理证/授权书')
  },
  {
    width: '150',
    field: 'effectiveDate',
    headerText: i18n.t('生效日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'expireDate',
    headerText: i18n.t('截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'contactTel',
    headerText: i18n.t('电话')
  },
  {
    width: '150',
    field: 'contractMobile',
    headerText: i18n.t('手机')
  },
  {
    width: '150',
    field: 'agentProduct',
    headerText: i18n.t('代理产品')
  },
  {
    width: '150',
    field: 'attachment',
    headerText: i18n.t('附件'),
    template: () => {
      return {
        template: Vue.component('attachmentTemplate', {
          template: `
              <div style="width:100%" >
                <span class="downStyle" v-for="(item,index) in attachmentLists" :key="index"
                @click="downAttachment(item)">{{item.fileName}}</span>
              </div>
              `,
          data: function () {
            return {
              data: {},
              attachmentLists: []
            }
          },
          mounted() {
            this.attachmentLists = this.data.attachment
          },
          methods: {
            downAttachment(item) {
              this.$utils.downloadAttachment(item)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'agentCategoryInfo',
    headerText: i18n.t('代理品类信息')
  },
  {
    width: '150',
    field: 'originUnifiedSocialCredit',
    headerText: i18n.t('原厂统一社会信用代码')
  },
  {
    width: '150',
    field: 'originAcceptMsg',
    headerText: i18n.t('原厂联系人类型')
  },
  {
    width: '150',
    field: 'brand',
    headerText: i18n.t('品牌')
  },
  {
    width: '150',
    field: 'manufacturerName',
    headerText: i18n.t('生产厂家名称')
  },
  {
    width: '150',
    field: 'productionAddress',
    headerText: i18n.t('生产地址')
  }
]
export const InnovationAwardsColumn = [
  {
    width: '150',
    field: 'certName',
    headerText: i18n.t('专利名称')
  },
  {
    width: '150',
    field: 'awardDate',
    headerText: i18n.t('专利获得日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'issuingAuthority',
    headerText: i18n.t('颁发机构')
  },
  {
    width: '150',
    field: 'attachment',
    headerText: i18n.t('附件'),
    template: () => {
      return {
        template: Vue.component('attachmentTemplate', {
          template: `
              <div style="width:100%" >
                <span class="downStyle" v-for="(item,index) in attachmentLists" :key="index"
                @click="downAttachment(item)">{{item.fileName}}</span>
              </div>
              `,
          data: function () {
            return {
              data: {},
              attachmentLists: []
            }
          },
          mounted() {
            this.attachmentLists = this.data.attachment
          },
          methods: {
            downAttachment(item) {
              this.$utils.downloadAttachment(item)
            }
          }
        })
      }
    }
  }
]
export const templateColumn = [
  {
    width: '150',
    field: 'certName',
    headerText: i18n.t('奖项名称')
  },
  {
    width: '150',
    field: 'awardDate',
    headerText: i18n.t('获奖日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'issuingAuthority',
    headerText: i18n.t('颁发机构')
  },
  {
    width: '150',
    field: 'attachment',
    headerText: i18n.t('附件'),
    template: () => {
      return {
        template: Vue.component('attachmentTemplate', {
          template: `
              <div style="width:100%" >
                <span class="downStyle" v-for="(item,index) in attachmentLists" :key="index"
                @click="downAttachment(item)">{{item.fileName}}</span>
              </div>
              `,
          data: function () {
            return {
              data: {},
              attachmentLists: []
            }
          },
          mounted() {
            this.attachmentLists = this.data.attachment
          },
          methods: {
            downAttachment(item) {
              this.$utils.downloadAttachment(item)
            }
          }
        })
      }
    }
  }
]
export const managementInfomation = [
  {
    width: '150',
    field: 'equipmentName',
    headerText: i18n.t('系统名称')
  },
  {
    width: '150',
    field: 'product',
    headerText: i18n.t('产品')
  },
  {
    width: '150',
    field: 'implementer',
    headerText: i18n.t('实施方')
  },
  {
    width: '150',
    field: 'launchDate',
    headerText: i18n.t('上线日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const equipment = [
  {
    width: '150',
    field: 'equipmentName',
    headerText: i18n.t('主要设备名称')
  },
  {
    width: '150',
    field: 'specifications',
    headerText: i18n.t('规格')
  },
  {
    width: '150',
    field: 'number',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'launchDate',
    headerText: i18n.t('制造出厂日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '150',
    field: 'dailyProductionNormal',
    headerText: i18n.t('日生产能力（正常）')
  },
  {
    width: '150',
    field: 'dailyProductionMax',
    headerText: i18n.t('日生产能力（最大）')
  },
  {
    width: '150',
    field: 'monthlyProductionNormal',
    headerText: i18n.t('月生产能力（正常）')
  },
  {
    width: '150',
    field: 'monthlyProductionMax',
    headerText: i18n.t('月生产能力（最大）')
  },
  {
    width: '150',
    field: 'bottleneck',
    headerText: i18n.t('是否瓶颈'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    width: '150',
    field: 'equipmentAdditionPlan',
    headerText: i18n.t('设备增加计划')
  }
]
export const testColumn = [
  {
    width: '150',
    field: 'equipmentName',
    headerText: i18n.t('主要设备名称')
  },
  {
    width: '150',
    field: 'specifications',
    headerText: i18n.t('规格')
  },
  {
    width: '150',
    field: 'launchDate',
    headerText: i18n.t('制造出厂日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const salesQuantity = [
  {
    width: '150',
    field: 'cooperatorRank',
    headerText: i18n.t('排序')
  },
  {
    width: '150',
    field: 'customerName',
    headerText: i18n.t('客户名称')
  },
  {
    width: '150',
    field: 'supplyProduct',
    headerText: i18n.t('供应产品')
  },
  {
    width: '150',
    field: 'salesVolume',
    headerText: i18n.t('销售额')
  },
  {
    width: '150',
    field: 'proportion',
    headerText: i18n.t('比例（%）')
  },
  {
    width: '150',
    field: 'currentMonthlySales',
    headerText: i18n.t('当前月销售额')
  },
  {
    width: '150',
    field: 'currentMonthlyProportion',
    headerText: i18n.t('当前月比例（%）')
  },
  {
    width: '150',
    field: 'previousOneMonthSales',
    headerText: i18n.t('前一月销售额')
  },
  {
    width: '150',
    field: 'previousOneMonthProportion',
    headerText: i18n.t('前一月比例（%）')
  },
  {
    width: '150',
    field: 'previousTwoMonthSales',
    headerText: i18n.t('前两月销售额')
  },
  {
    width: '150',
    field: 'previousTwoMonthProportion',
    headerText: i18n.t('前两月比例（%）')
  },
  {
    width: '150',
    field: 'cooperationStartDate',
    headerText: i18n.t('合作起始日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const baseInfoColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'supplyCategory',
    headerText: i18n.t('供货类别')
  },
  {
    width: '150',
    field: 'categoryDescription',
    headerText: i18n.t('类别描述')
  },
  {
    width: '150',
    field: 'normalProcureCycle',
    headerText: i18n.t('正常采购周期（天）')
  },
  {
    width: '150',
    field: 'minProcureCycle',
    headerText: i18n.t('最短采购周期（天）')
  },
  {
    width: '150',
    field: 'rawMaterialRemark',
    headerText: i18n.t('主要原材料备注')
  },
  {
    width: '150',
    field: 'normalProduceCycle',
    headerText: i18n.t('正常生产周期（天）')
  },
  {
    width: '150',
    field: 'minProduceCycle',
    headerText: i18n.t('最短生产周期（天）')
  },
  {
    width: '150',
    field: 'produceCycleRemark',
    headerText: i18n.t('生产周期备注')
  }
]
export const procurementCycle = [
  {
    width: '150',
    field: 'cycleName',
    headerText: i18n.t('原材料名称')
  },
  {
    width: '150',
    field: 'vendorName',
    headerText: i18n.t('供应厂商')
  },
  {
    width: '150',
    field: 'purchaseCycle',
    headerText: i18n.t('采购周期（天）')
  },
  {
    width: '150',
    field: 'bottleneck',
    headerText: i18n.t('是否瓶颈'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    width: '150',
    field: 'safetyStock',
    headerText: i18n.t('安全库存量')
  },
  {
    width: '150',
    field: 'vendorContactMode',
    headerText: i18n.t('供货商联系方式')
  }
]
export const productionColumn = [
  {
    width: '150',
    field: 'cycleName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '150',
    field: 'useEquipment',
    headerText: i18n.t('使用设备')
  },
  {
    width: '150',
    field: 'processTime',
    headerText: i18n.t('工序时间（小时）')
  },
  {
    width: '150',
    field: 'bottleneck',
    headerText: i18n.t('是否瓶颈'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    width: '150',
    field: 'finishedProductInventory',
    headerText: i18n.t('（半）成品库存量')
  }
]
export const subcontractColumn = [
  {
    width: '150',
    field: 'cycleName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '150',
    field: 'vendorName',
    headerText: i18n.t('供货商')
  },
  {
    width: '150',
    field: 'purchaseCycle',
    headerText: i18n.t('采购周期（天）')
  },
  {
    width: '150',
    field: 'bottleneck',
    headerText: i18n.t('是否瓶颈'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    width: '150',
    field: 'safetyStock',
    headerText: i18n.t('安全库存量')
  },
  {
    width: '150',
    field: 'vendorContactMode',
    headerText: i18n.t('供货商联系方式')
  }
]
export const haulageColumn = (deliveryList, transportList) => [
  {
    width: '150',
    field: 'shipmentPlace',
    headerText: i18n.t('发货地点')
  },
  {
    width: '150',
    field: 'deliveryPlace',
    headerText: i18n.t('交货地点'),
    valueAccessor: function (field, data) {
      let dataSource = deliveryList || []
      return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
    }
  },
  {
    width: '150',
    field: 'transmitType',
    headerText: i18n.t('运输方式'),
    valueAccessor: function (field, data) {
      let dataSource = transportList || []
      return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.name
    }
  },
  {
    width: '150',
    field: 'distance',
    headerText: i18n.t('路程（公里）')
  },
  {
    width: '150',
    field: 'transmitTime',
    headerText: i18n.t('运输时间（小时）')
  },
  {
    width: '150',
    field: 'normalProduceCycle',
    headerText: i18n.t('正常交货周期（天）')
  },
  {
    width: '150',
    field: 'minProduceCycle',
    headerText: i18n.t('最短交货周期（天）')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

const columnData = [
  {
    width: 120,
    field: 'dimensionName',
    headerText: i18n.t('名称')
  },
  {
    field: 'rate',
    headerText: i18n.t('分配权重（%）')
  },
  {
    field: 'dimensionFullScore',
    headerText: i18n.t('指标满分')
  },
  {
    field: 'dimensionScore',
    headerText: i18n.t('分配分值')
  }
]

export const pageConfig = [
  {
    useToolTemplate: false,
    treeGrid: {
      allowPaging: false,
      columnData,
      childMapping: 'targetScores',
      dataSource: []
    }
  }
]
