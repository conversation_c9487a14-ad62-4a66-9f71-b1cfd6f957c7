<template>
  <div class="supplier-portrait">
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('基本信息') }}</div>
        <div class="b-info-content">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('供应商名称') }}</div>
            <div class="info-content-left">
              {{ baseInfo.supplierName || '--' }}
            </div>
            <div class="info-title epls">{{ $t('供应商英文名称') }}</div>
            <div class="info-content-right">
              {{ baseInfo.supplierNameEn || '--' }}
            </div>
            <div class="info-title epls">{{ $t('供应商简称') }}</div>
            <div class="info-content-right">
              {{ baseInfo.supplierShortName || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('供应商类型') }}</div>
            <div class="info-content-right">
              <mt-select
                :disabled="true"
                v-model="baseInfo.supplierType"
                :data-source="supplierList"
                :fields="stageTypeFields"
              ></mt-select>
            </div>
            <div class="info-title">{{ $t('企业性质') }}</div>
            <div class="info-content-left">
              <mt-select
                :disabled="true"
                v-model="baseInfo.enterpriseNature"
                :data-source="enterpriseList"
                :fields="stageTypeFields"
              ></mt-select>
            </div>
            <div class="info-title epls">{{ $t('所属行业') }}</div>
            <div class="info-content-right">
              <mt-select
                :disabled="true"
                v-model="baseInfo.industryBelong"
                :data-source="industryList"
                :fields="stageTypeFields"
              ></mt-select>
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('行业排名') }}</div>
            <div class="info-content-right">
              <mt-select
                :disabled="true"
                v-model="baseInfo.industryRank"
                :data-source="rankList"
                :fields="stageTypeFields"
              ></mt-select>
            </div>
            <div class="info-title">{{ $t('行业经验（年）') }}</div>
            <div class="info-content-left">
              {{ baseInfo.industryEnterprise || '--' }}
            </div>
            <div class="info-title epls">{{ $t('企业电话') }}</div>
            <div class="info-content-right">
              {{ baseInfo.enterprisePhone || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('电子邮箱') }}</div>
            <div class="info-content-right">
              {{ baseInfo.enterpriseEmail || '--' }}
            </div>
            <div class="info-title">{{ $t('邮编') }}</div>
            <div class="info-content-left">
              {{ baseInfo.zipCode || '--' }}
            </div>
            <div class="info-title epls">{{ $t('传真') }}</div>
            <div class="info-content-right">
              {{ baseInfo.fax || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('法人代表') }}</div>
            <div class="info-content-right">
              {{ baseInfo.legalRepresentative || '--' }}
            </div>
            <div class="info-title">{{ $t('企业网站') }}</div>
            <div class="info-content-left">
              {{ baseInfo.website || '--' }}
            </div>
            <div class="info-title epls">{{ $t('公司成立时间') }}</div>
            <div class="info-content-right">
              {{ checkDate(baseInfo.establishmentTime) || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('国家/地区') }}</div>
            <div class="info-content-right">
              <!-- <mt-select
                  :disabled="true"
                    v-model="baseInfo.countryRegion"
                    :dataSource="countryLists"
                    :fields="countryFields"
                  ></mt-select> -->
              <RemoteAutocomplete
                v-model="baseInfo.countryRegion"
                :disabled="true"
                :fields="{ text: 'shortName', value: 'countryCode' }"
                url="/masterDataManagement/tenant/country/paged-query"
                :rule-params="ruleParams"
                :load-data="loadCountryData"
                :search-fields="[
                  'id',
                  'alpha2Code',
                  'countryCode',
                  'englishShortName',
                  'numericCode',
                  'shortName',
                  'statusDescription'
                ]"
                :title-switch="false"
                select-type="country"
              >
              </RemoteAutocomplete>
            </div>
            <div class="info-title">{{ $t('省份') }}</div>
            <div class="info-content-left">
              <mt-select
                :disabled="true"
                v-model="baseInfo.province"
                :data-source="provinceList"
                :fields="provinceFields"
              ></mt-select>
            </div>
            <div class="info-title epls">{{ $t('市/区') }}</div>
            <div class="info-content-right">
              {{ baseInfo.city || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('工厂/仓库距离TCL（公里）') }}</div>
            <div class="info-content-right">
              {{ baseInfoInsideDTO.supplyDistance || '--' }}
            </div>
            <div class="info-title">{{ $t('工厂面积') }}</div>
            <div class="info-content-left">
              {{ baseInfo.factoryArea || '--' }}
            </div>
            <div class="info-title epls">{{ $t('同行业主力客户数量') }}</div>
            <div class="info-content-right">
              {{ baseInfoExtDTO.mainCustomerNumber || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('同行业主力客户名称') }}</div>
            <div class="info-content-right">
              {{ baseInfoExtDTO.mainCustomerName || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('控股股东是否有TV整机品牌背景') }}</div>
            <div class="info-content-left">
              <mt-select
                :disabled="true"
                v-model="baseInfoExtDTO.stockHolderHasBackground"
                :data-source="stockDataArr"
              ></mt-select>
            </div>
            <div class="info-title epls">{{ $t('公司法人、关键高层有无TCL3年内离职人员') }}</div>
            <div class="info-content-right">
              <mt-select
                :disabled="true"
                v-model="baseInfoExtDTO.hasResignedEmployee"
                :data-source="dataArrEmployee"
              ></mt-select>
            </div>
            <div class="info-title epls">{{ $t('是否TCL竞争对手控股') }}</div>
            <div class="info-content-right">
              <mt-select
                :disabled="true"
                v-model="baseInfoExtDTO.controlledByCompetitors"
                :data-source="controlledDataArr"
              ></mt-select>
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('纳税人识别号') }}</div>
            <div class="textareaClass">
              {{ baseInfo.taxNumber || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('注册地址') }}</div>
            <div class="textareaClass">
              {{ baseInfo.detailAddress || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('企业简介') }}</div>
            <div class="textareaClass">
              {{ baseInfoExtDTO.enterpriseProfile || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('英文地址') }}</div>
            <div class="textareaClass">
              {{ baseInfo.supplierAddrEn || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('经营范围') }}</div>
            <div class="textareaClass">
              {{ baseInfoExtDTO.businessScope || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('行业优势') }}</div>
            <div class="textareaClass">
              {{ baseInfoExtDTO.industryAdvantage || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('生产能力简介') }}</div>
            <div class="textareaClass">
              {{ baseInfoExtDTO.productionProfile || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('产品性能/价格/质量情况') }}</div>
            <div class="textareaClass">
              {{ baseInfoExtDTO.productProfile || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('供货方及供货方便性简介') }}</div>
            <div class="textareaClass">
              {{ baseInfoExtDTO.supplierProfile || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('供货地区') }}</div>
            <div class="textareaClass">
              {{ baseInfo.area || '--' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('工厂生产地址') }}</div>
        <div class="b-info-content" v-for="(item, key) in baseInfo.factoryAddress" :key="key">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('工厂生产地址') + (key + 1) }}</div>
            <div class="textareaClass">
              {{ item.address || '--' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('供货类别') }}</div>
        <div class="b-info-content">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('供货类别') }}</div>
            <div class="textareaClass">
              {{ baseInfoInsideDTO.supplyCategory || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('供货补充说明') }}</div>
            <div class="textareaClass">
              {{ baseInfoInsideDTO.categoryDescription || '--' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('股权结构') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="shipTemplateConfig"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('联系人信息') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="templateConfig"
          ></mt-template-page>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { shipStructureInfo, contactsInfo } from '../config/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  props: {
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ruleParams: [{ field: 'statusId', operator: 'equal', type: 'int', value: 1 }],

      stockDataArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      dataArrEmployee: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      controlledDataArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      baseInfo: {},
      baseInfoExtDTO: {},
      baseInfoInsideDTO: {},
      shipTemplateConfig: [
        {
          gridId: 'b3a2b543-c926-4724-adb7-8e40563ed841',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            columnData: shipStructureInfo,
            dataSource: []
          }
        }
      ],
      templateConfig: [
        {
          gridId: '86fb4a39-132c-401c-bd6a-c885e10ad6c9',
          grid: {
            allowPaging: false,
            columnData: [],
            dataSource: []
          }
        }
      ],
      // 所有下拉列表框数据
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      supplierList: [],
      enterpriseList: [],
      industryList: [],
      rankList: [],
      postList: [],
      acceptList: [],
      supplyAddressList: [],
      // countryLists:[],
      // countryFields: {
      //   text: "shortName",
      //   value: "countryCode",
      // },
      countryFields: {
        text: 'title',
        value: 'countryCode'
      },
      provinceList: [],
      provinceFields: {
        text: 'areaName',
        value: 'areaCode'
      }
    }
  },
  mounted() {
    console.log(this.supplierEnterpriseId)
    // 查询接口
    this.getAllSelectList()
    this.searchBase()
    this.selectByParentCode()
  },
  methods: {
    selectByParentCode() {
      this.$loading()
      this.$API.supplierInvitationAdd
        .selectByParentCode({
          parentCode: ''
        })
        .then((res) => {
          this.$hloading()
          let arr = JSON.parse(JSON.stringify(res.data))
          // arr.unshift({ id: "", areaName: "全部地区" });
          this.provinceList = arr
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async getAllSelectList() {
      // 供货地区
      await this.getEnterpriseNature('supply_area')
      // 国家地区
      // this.countryQuery();
      // 供应商类型
      this.getEnterpriseNature('supplierType')
      // 企业性质
      this.getEnterpriseNature('EnterpriseType')
      // 所属行业
      this.getEnterpriseNature('Industry')
      // 行业排名
      this.getEnterpriseNature('industryRank')
      // 职务
      await this.getEnterpriseNature('post')
      // 接受信息类型
      await this.getEnterpriseNature('acceptMsgType')
      this.nextThen()
    },
    // countryQuery(){
    //   this.$loading();
    //   this.$API.infoChange.countryQuery().then((res) => {
    //     this.$hloading();
    //     this.countryLists = res.data;
    //   }).catch(err=>{
    //     this.$hloading();
    //     this.$toast({
    //       content:err.msg,
    //       type:"error"
    //     })
    //   });
    // },
    async getEnterpriseNature(dictCode) {
      this.$loading()
      await this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'EnterpriseType') {
              this.enterpriseList = data
            } else if (dictCode == 'Industry') {
              this.industryList = data
            } else if (dictCode == 'industryRank') {
              this.rankList = data
            } else if (dictCode == 'post') {
              this.postList = data
            } else if (dictCode == 'supplierType') {
              this.supplierList = data
            } else if (dictCode == 'acceptMsgType') {
              this.acceptList = data
            } else if (dictCode == 'supply_area') {
              this.supplyAddressList = data
            }
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    nextThen() {
      this.templateConfig[0].grid.columnData = contactsInfo(this.postList, this.acceptList)
    },
    checkDate(e) {
      if (e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd')
        } else if (typeof e == 'string') {
          let val = parseInt(e)
          return this.$utils.formateTime(val, 'yyyy-MM-dd')
        } else {
          return e
        }
      } else {
        return e
      }
    },
    searchBase() {
      let obj = {
        partnerArchiveId: this.partnerArchiveId,
        orgId: this.orgId,
        partnerRelationCode: this.partnerRelationCode,
        supplierEnterpriseId: this.supplierEnterpriseId
      }
      this.$loading()
      this.$API.supplierProfile
        .searchBaseSup(obj)
        .then((res) => {
          this.$hloading()
          this.baseInfo = res.data.baseInfoDTO
          this.baseInfo.factoryAddress =
            res.data.baseInfoDTO.factoryAddress && res.data.baseInfoDTO.factoryAddress !== 'null'
              ? JSON.parse(res.data.baseInfoDTO.factoryAddress).map((item) => {
                  return JSON.parse(item)
                })
              : []
          this.baseInfo.area =
            res.data.baseInfoDTO.supplyAddress && res.data.baseInfoDTO.supplyAddress !== 'null'
              ? JSON.parse(res.data.baseInfoDTO.supplyAddress)
                  .map((item) => {
                    return this.supplyAddressList.find((v) => v.itemCode === item)?.itemName
                  })
                  .join('，')
              : ''
          this.baseInfoExtDTO = res.data.baseInfoExtDTO
          this.baseInfoInsideDTO = res.data.baseInfoInsideDTO
          this.templateConfig[0].grid = Object.assign({}, this.templateConfig[0].grid, {
            dataSource: res.data.contactInsideDTOList
          })
          this.shipTemplateConfig[0].grid = Object.assign({}, this.shipTemplateConfig[0].grid, {
            dataSource: res.data.ownershipStructureDTOList
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            word-break: break-all;
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 85%;
            padding: 5px 10px;
            word-break: break-word;
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
