<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="false"
    ></mt-select>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      isDisabled: false
    }
  },
  async mounted() {
    if (this.data.column.field === 'endProductStatus') {
      this.getEndProductStatus()
    }
  },
  beforeDestroy() {},
  methods: {
    getEndProductStatus() {
      this.dataSource = [
        { label: this.$t('否'), value: 0 },
        { label: this.$t('是'), value: 1 }
      ]
      this.fields = { text: 'label', value: 'value' }
    },
    selectChange() {}
  }
}
</script>
