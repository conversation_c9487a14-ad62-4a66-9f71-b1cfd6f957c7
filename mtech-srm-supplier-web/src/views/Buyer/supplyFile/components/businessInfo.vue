<template>
  <div class="supplier-portrait">
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('基本信息') }}</div>
        <div class="b-info-content">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('总资产（万元）') }}</div>
            <div class="info-content-left">
              {{ financialBaseInfo.totalAssets || '--' }}
            </div>
            <div class="info-title epls">{{ $t('注册资本（万元）') }}</div>
            <div class="info-content-right">
              {{ financialBaseInfo.registeredCapital || '--' }}
            </div>
            <div class="info-title epls">{{ $t('实缴资本（万元）') }}</div>
            <div class="info-content-right">
              {{ financialBaseInfo.paidInCapital || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('流动资金（万元）') }}</div>
            <div class="info-content-left">
              {{ financialBaseInfo.workingCapital || '--' }}
            </div>
            <div class="info-title epls">{{ $t('固定资产（万元）') }}</div>
            <div class="info-content-right">
              {{ financialBaseInfo.fixedAssets || '--' }}
            </div>
            <div class="info-title epls">{{ $t('年度销售总额/万元（近三年）-第一年') }}</div>
            <div class="info-content-right">
              {{ financialBaseInfo.totalAnnualSalesOne || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('年度销售总额/万元（近三年）-第二年') }}</div>
            <div class="info-content-left">
              {{ financialBaseInfo.totalAnnualSalesTwo || '--' }}
            </div>
            <div class="info-title epls">{{ $t('年度销售总额/万元（近三年）-第三年') }}</div>
            <div class="info-content-right">
              {{ financialBaseInfo.totalAnnualSalesThree || '--' }}
            </div>
            <div class="info-title epls">{{ $t('近三年利润率（%）') }}</div>
            <div class="info-content-right">
              {{ financialBaseInfo.profitMargin || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('年产值（万元）') }}</div>
            <div class="info-content-left">
              {{ financialBaseInfo.annualOutputValue || '--' }}
            </div>
            <div class="info-title epls">{{ $t('银行信用等级') }}</div>
            <div class="info-content-right">
              {{ financialBaseInfo.bankCredit || '--' }}
            </div>
            <div class="info-title epls">{{ $t('纳税信用最新评价') }}</div>
            <div class="info-content-right">
              {{ financialBaseInfo.taxCreditEvaluation || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('币种') }}</div>
            <div class="info-content-left">
              <!-- <mt-select
                :disabled="true"
                    v-model="baseInfoInsideDTO.currency"
                    :fields="currencyFields"
                    :dataSource="pagedList"
                  ></mt-select> -->
              <RemoteAutocomplete
                v-model="baseInfoInsideDTO.currency"
                :fields="{ text: 'currencyName', value: 'currencyCode' }"
                :search-fields="['currencyCode', 'currencyName']"
                url="/masterDataManagement/tenant/currency/queryAll"
                records-position="data"
                select-type="money"
                :disabled="true"
              >
              </RemoteAutocomplete>
            </div>
            <div class="info-title epls">{{ $t('结算类型') }}</div>
            <div class="info-content-right">
              <mt-select
                :disabled="true"
                v-model="baseInfoInsideDTO.settlementType"
                :data-source="settlementList"
                :fields="stageTypeFields"
              ></mt-select>
            </div>
            <div class="info-title epls">{{ $t('能否接受TCL金单') }}</div>
            <div class="info-content-right">
              <mt-select
                :disabled="true"
                v-model="baseInfoInsideDTO.receiveGoldOrder"
                :data-source="receiveDataArr"
              ></mt-select>
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('付款条件') }}</div>
            <div class="info-content-left">
              <mt-select
                :disabled="true"
                v-model="baseInfoInsideDTO.paymentTerm"
                :data-source="payList"
                :fields="payFields"
              ></mt-select>
            </div>
            <div class="info-title epls">{{ $t('附件') }}</div>
            <div class="info-content-right" style="width: 52%s">
              <div style="width: 100%">
                <span
                  class="downStyle"
                  v-for="(item, index) in baseInfoInsideDTO.financeAttachment"
                  :key="index"
                  @click="downAttachment(item)"
                  >{{ item.fileName }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('银行账户信息') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="templateConfig"
          ></mt-template-page>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { bankInfo } from '../config/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  props: {
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      receiveDataArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      settleDataArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      financialBaseInfo: {},
      baseInfoInsideDTO: {},
      templateConfig: [
        {
          gridId: '45e7b801-4649-42c5-ac8c-efd2b722b898',
          // toolbar: {
          //   useBaseConfig: false,
          //   tools: [
          //     [
          //       { id: "add", icon: "icon_table_new", title: "新增" },
          //       { id: "edit", icon: "icon_table_edit", title: "编辑" },
          //       { id: "deleted", icon: "icon_table_delete", title: "删除" },
          //     ],
          //     ["Filter", "export", "Refresh", "Setting"],
          //   ],
          // },
          grid: {
            allowPaging: false,
            columnData: bankInfo,
            dataSource: []
          }
        }
      ],
      // currencyFields: {
      //   text: "currencyName",
      //   value: "currencyCode",
      // },
      currencyFields: {
        text: 'title',
        value: 'currencyCode'
      },
      settlementList: [],
      provinceList: [],
      payList: [],
      payFields: {
        text: 'paymentTermsName',
        value: 'paymentTermsCode'
      },
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      // pagedList:[],
      attachmentList: []
    }
  },
  mounted() {
    this.searchFinance()
    this.getAllSelectList()
  },
  methods: {
    // 下载附件
    downAttachment(item) {
      this.$utils.downloadAttachment(item)
    },
    getAllSelectList() {
      // 币种
      // this.getPagedQuery();
      // 结算类型
      this.getEnterpriseNature('settlementType')
      // 付款条件
      this.getCriteriaQuery()
      // 开户行省份
      this.selectByParentCode()
      // 证书类型
      this.getEnterpriseNature('certificateType')
    },
    // getPagedQuery(){
    //   this.$loading();
    //   this.$API.infoChange.pagedQuery().then(res=>{
    //     this.$hloading();
    //     this.pagedList = res.data;
    //   }).catch(err=>{
    //     this.$hloading();
    //     this.$toast({
    //       content:err.msg,
    //       type:"error"
    //     })
    //   })
    // },
    // 付款条件
    getCriteriaQuery() {
      this.$loading()
      this.$API.infoChange
        .criteriaQuery({})
        .then((res) => {
          this.$hloading()
          this.payList = res.data
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    getEnterpriseNature(dictCode) {
      this.$loading()
      this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'settlementType') {
              this.settlementList = data
            }
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 获取城市数据
    selectByParentCode() {
      this.$loading()
      this.$API.supplierInvitationAdd
        .selectByParentCode({
          parentCode: ''
        })
        .then((res) => {
          this.$hloading()
          let arr = JSON.parse(JSON.stringify(res.data))
          // arr.unshift({ id: "", areaName: "全部地区" });
          this.provinceList = arr
          this.$set(this.templateConfig[0].grid, 'columnData', bankInfo(this.provinceList))
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    searchFinance() {
      let obj = {
        partnerArchiveId: this.partnerArchiveId,
        orgId: this.orgId,
        partnerRelationCode: this.partnerRelationCode,
        supplierEnterpriseId: this.supplierEnterpriseId
      }
      this.$loading()
      this.$API.supplierProfile
        .searchFinanceSup(obj)
        .then((res) => {
          this.$hloading()
          this.financialBaseInfo = res.data.financeInfoDTO
          this.baseInfoInsideDTO = res.data.baseInfoInsideDTO
          this.templateConfig[0].grid = Object.assign({}, this.templateConfig[0].grid, {
            dataSource: res.data.bankInsideDTOList
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 85%;
            padding: 5px 10px;
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            .downStyle {
              cursor: pointer;
              color: #00469c;
              margin: 0 5px;
            }
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
