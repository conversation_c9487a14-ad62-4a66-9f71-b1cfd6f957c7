<template>
  <div class="lifeCycle-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="header-content flex1 fbox">
        <div class="titles-box flex1">
          <div class="mian-title" v-if="!!applyInfoDTO.applyName">
            {{ applyInfoDTO.applyName || '--' }}
          </div>
          <div class="sub-title fbox">
            <div class="normal-title" v-if="!!applyInfoDTO.applyCode">
              {{ $t('申请单编码') }}：{{ applyInfoDTO.applyCode }}
            </div>
            <div class="normal-title" v-if="!!applyInfoDTO.applyerName">
              {{ $t('申请人') }}：{{ applyInfoDTO.applyerName }}
            </div>
            <div class="normal-title" v-if="!!applyInfoDTO.applyerDeptName">
              {{ $t('申请部门') }}：{{ applyInfoDTO.applyerDeptName }}
            </div>
            <div class="normal-title" v-if="!!applyInfoDTO.createTime">
              {{ $t('申请时间') }}：{{ applyInfoDTO.createTime }}
            </div>
            <div class="normal-title" v-if="!!applyInfoDTO.applyReason">
              {{ $t('申请原因') }}：{{ applyInfoDTO.applyReason }}
            </div>
          </div>
          <div class="sub-title2 fbox">
            <div class="normal-line" v-for="item in relationDTOList" :key="item.id">
              {{ $t('供应商') }}：{{ item.customerEnterpriseName }}
              <span>{{ item.customerEnterpriseCode || '--' }}</span>
            </div>
          </div>
        </div>
        <div class="btns-box fbox">
          <!-- 待提交 驳回 -->
          <div
            class="invite-btn"
            v-if="applyInfoDTO.applyStatus === 10 || applyInfoDTO.applyStatus === 30"
            @click="onSubmit"
          >
            {{ $t('提交') }}
          </div>
          <div class="invite-btn" @click="onBack">{{ $t('返回') }}</div>
        </div>
      </div>
    </div>
    <!-- 顶部信息 end -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>

    <task-center
      v-if="selectIndex === 0"
      :form-task-d-t-o-t-p="formTaskDTO"
      ref="taskCenter"
    ></task-center>
    <questionnaire-history
      v-if="selectIndex === 1"
      :form-task-d-t-o="formTaskDTO"
      :id="id"
      ref="questionnaireHistory"
    ></questionnaire-history>
    <operator-history v-if="selectIndex === 2" :id="id"></operator-history>
  </div>
</template>

<script>
import TaskCenter from '../components/taskCenter.vue'
import QuestionnaireHistory from '../components/questionnaireHistory.vue'
import OperatorHistory from '../components/operatorHistory.vue'
import utils from '@/utils/utils.js'
export default {
  beforeRouteEnter(to, from, next) {
    // 子子页面 重定向会详情页面
    if (!from.name && to.name === 'changeTask') {
      next({
        name: 'infoChangeDetail',
        query: {
          id: to.query.applyId
        }
      })
    } else {
      next()
    }
  },
  components: {
    TaskCenter,
    QuestionnaireHistory,
    OperatorHistory
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('调查表')
        },
        {
          title: this.$t('调查表历史')
        },
        {
          title: this.$t('操作历史')
        }
      ],
      id: '',

      applyInfoDTO: {},
      formTaskDTO: {},
      relationDTOList: []
    }
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    // 提交
    onSubmit() {
      this.$loading()
      this.$API.SupplierPunishment.applySubmit({
        applyIdList: [this.id]
      }).then((res) => {
        this.$hloading()
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.$toast({
            content: this.$t('提交成功!即将跳回'),
            type: 'success'
          })

          setTimeout(() => {
            this.onBack()
          }, 600)
        } else {
          this.$toast({
            content: this.$t('提交失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    handleSelectTab(index) {
      this.selectIndex = index
    },

    // 获取任务详情
    getAccessDetail(id) {
      this.$API.supplierInfoChange
        .getDetail({ id: id })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            let { applyInfoDTO, formTaskDTO, relationDTOList } = res.data
            this.applyInfoDTO = applyInfoDTO
            this.formTaskDTO = formTaskDTO
            this.relationDTOList = relationDTOList

            // 初始化任务中心
            // this.$refs.taskCenter.initTaskCenter(formTaskDTO)
            // this.$refs.questionnaireHistory.initTaskCenter(formTaskDTO)
          } else {
            this.$toast({
              content: this.$t('获取申请详情失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg || this.$t('获取申请详情失败，请重试!'),
            type: 'warning'
          })
        })
    }
  },
  created() {
    let id = this.$route.query.id
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getAccessDetail(id)
  }
}
</script>

<style lang="scss">
.lifeCycle-container {
  display: flex;
  flex-direction: column;

  .mt-tabs-container {
    background: transparent;
  }
  .mt-tabs-container ul.tab-container li.tab-item2 {
    color: #292929;
  }
}
</style>

<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.lifeCycle-container {
  height: 100%;
  margin-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .header-logo {
      border-radius: 100%;
      width: 60px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 70, 156, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      font-size: 40px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      margin-right: 20px;
    }
    .header-content {
      justify-content: space-between;
      .titles-box {
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;

          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .sub-title {
          font-size: 12px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-top: 16px;
          .normal-title {
            font-size: 12px;

            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }
        }
        .sub-title2 {
          margin-top: 20px;
          font-size: 14px;
          font-weight: normal;
          color: #292929;

          .normal-line {
            font-size: 14px;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;

            span {
              color: #00469c;
            }
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        max-width: 300px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        .invite-btn {
          cursor: pointer;
          margin-right: 40px;
        }

        .invite-btn:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
