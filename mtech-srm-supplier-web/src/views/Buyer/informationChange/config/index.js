import Vue from 'vue'
import { i18n } from '@/main.js'
export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '210',
    field: 'applyCode',
    headerText: i18n.t('申请单编码'),
    cssClass: 'field-content'
  },
  {
    width: '210',
    field: 'applyName',
    headerText: i18n.t('申请单名称')
  },
  {
    width: '210',
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '210',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '160',
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '160',
    field: 'taskInstanceName',
    headerText: i18n.t('调查表名称')
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500; 
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.applyStatus] }}</span>
          
          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: {
                10: i18n.t('待提交'),
                20: i18n.t('待审批'),
                30: i18n.t('已驳回'),
                40: i18n.t('已完成'),
                50: i18n.t('已关闭')
              }
            }
          }
        })
      }
    }
  },
  {
    width: '160',
    field: 'applyerDeptName',
    headerText: i18n.t('申请人部门')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('申请日期'),
    queryType: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '160',
    field: 'remark',
    headerText: i18n.t('供应商备注')
  }
]

const toolbar = [
  { id: 'upGrade', icon: 'icon_solid_Newinvitation', title: i18n.t('新建') },
  { id: 'upGrade', icon: 'icon_solid_edit', title: i18n.t('批准') },
  { id: 'upGrade', icon: 'icon_solid_Delete', title: i18n.t('驳回') }
]

export const pageConfig = (url) => {
  return [
    {
      gridId: 'bc7fd0d2-0138-4765-a53f-d7c0999a3d22',
      toolbar: toolbar,
      grid: {
        columnData: columnDataMain,
        asyncConfig: {
          url
        }
      }
    }
  ]
}

export const supplyAreaToolbar = []
export const areaColumnData = []
