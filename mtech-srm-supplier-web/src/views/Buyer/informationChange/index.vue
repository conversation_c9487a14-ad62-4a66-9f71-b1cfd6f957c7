<!-- 供应商信息变更管理 20220119 0301版本 -->
<template>
  <div class="supplier-info-change">
    <mt-template-page
      :padding-top="false"
      ref="templateRef"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config/index.js'
export default {
  data() {
    return {
      componentConfig: pageConfig('/supplier/tenant/buyer/apply/formTask/list')
    }
  },
  created() {},
  methods: {
    handleClickToolBar(e) {
      console.log(e)
      let { toolbar } = e
      if (toolbar.id === 'upGrade') {
        this.$dialog({
          modal: () => import('./components/addInfoChange'),
          data: {
            title: this.$t('新增')
          },
          success: () => {
            this.$refs.templateRef?.refreshCurrentGridData()
          },
          close: () => {}
        })
      }
    },
    handleClickCellTool() {},
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      let { field, data } = e
      if (field === 'applyCode') {
        this.$router.push({
          name: 'infoChangeDetail',
          query: {
            id: data.id
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
.supplier-info-change {
  width: 100%;
  height: 100%;

  .normal-status {
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
    border-radius: 2px;
    opacity: 10;
    font-size: 12px;
    font-weight: 500;
    color: #6386c1;
  }
}
</style>

<style lang="scss" scoped>
.supplier-info-change {
  width: 100%;
}
</style>
