import { i18n } from '@/main.js'

// 模板枚举
const templateENUM = [
  { text: i18n.t('绩效模板'), value: 18 },
  { text: i18n.t('商务审查模板'), value: 19 },
  { text: i18n.t('CSR审查模板'), value: 20 },
  { text: i18n.t('研发审查模板'), value: 21 },
  { text: i18n.t('环保审查模板'), value: 22 },
  { text: i18n.t('QSA模板'), value: 23 },
  { text: i18n.t('QPA模板'), value: 24 }
]

// 层级枚举
const levelENUM = [{ text: i18n.t('实业'), value: 1 }]

// 是否生效枚举
const validENUM = [
  { text: i18n.t('生效'), value: 2 },
  { text: i18n.t('不生效'), value: 3 }
]

// 归属领域枚举
const domainENUM = [{ text: i18n.t('结构'), value: 4 }]

// 询报价模式
const quoteMethodENUM = [
  { text: i18n.t('招投标'), value: 5 },
  { text: i18n.t('议价'), value: 6 }
]

// 能力地图和选型沙盘
const mapSelectionENUM = [
  { text: i18n.t('启用'), value: 7 },
  { text: i18n.t('未启用'), value: 8 }
]

// 成本模型
const costMouldENUM = [
  { text: i18n.t('启用'), value: 9 },
  { text: i18n.t('未启用'), value: 10 }
]

// 供应商引入
const effectiveMethodENUM = [
  { text: i18n.t('线上签批'), value: 11 },
  { text: i18n.t('采委会决策'), value: 12 }
]

// 交货模式
const deliveryMethodENUM = [{ text: i18n.t('JIT'), value: 13 }]

// 事业部
const bussinessUnitENUM = [
  { text: i18n.t('空调事业部'), value: 53 },
  { text: i18n.t('白电事业部'), value: 54 },
  { text: i18n.t('泛智屏事业部'), value: 55 }
]

// 分层分级结果
const gradingResultENUM = [
  { text: i18n.t('战略'), value: 14 },
  { text: i18n.t('杠杆'), value: 15 },
  { text: i18n.t('常规'), value: 16 },
  { text: i18n.t('瓶颈'), value: 17 }
]

// 组别
const groupingNameENUM = [
  { text: i18n.t('电子组'), value: '电子组' },
  { text: i18n.t('结构组'), value: '结构组' },
  { text: i18n.t('背光组'), value: '背光组' },
  { text: i18n.t('海外组'), value: '海外组' },
  { text: i18n.t('战略组'), value: '战略组' },
  { text: i18n.t('非采'), value: '非采' },
  { text: i18n.t('ODM/OEM'), value: 'ODM/OEM' },
  { text: i18n.t('包辅'), value: '包辅' }
]

export {
  templateENUM,
  levelENUM,
  validENUM,
  domainENUM,
  quoteMethodENUM,
  mapSelectionENUM,
  costMouldENUM,
  effectiveMethodENUM,
  deliveryMethodENUM,
  bussinessUnitENUM,
  gradingResultENUM,
  groupingNameENUM
}
