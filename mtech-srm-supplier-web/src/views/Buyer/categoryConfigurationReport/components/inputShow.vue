<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  mounted() {
    if (this.data.column.field == 'enterpriseCode') {
      this.$bus.$on('crrEnterpriseNamebus', (val) => {
        this.data.enterpriseCode = val.entityCode
      })
    }
    if (this.data.column.field == 'categoryCode') {
      this.$bus.$on('crrCategoryCodebus', (val) => {
        this.data.categoryCode = val.categoryCode
      })
    }
    if (this.data.column.field == 'categoryType') {
      this.$bus.$on('crrCategoryTypebus', (val) => {
        this.data.categoryType = val.categoryTypeName
      })
    }
  },
  methods: {
    onInput(e) {
      console.log(e)
    }
  }
}
</script>
