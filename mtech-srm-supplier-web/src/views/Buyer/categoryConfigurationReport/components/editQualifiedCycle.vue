<template>
  <div>
    <div class="editQualifiedCycle">
      <mt-input type="Number" :id="data.column.field" v-model="data[data.column.field]"></mt-input>
      <div class="text">{{ $t('个月') }}</div>
    </div>
  </div>
</template>

<script>
// import utils from '@/utils/utils'

export default {
  data() {
    return {}
  }

  // mounted() {
  //   this.filtering = utils.debounce(this.filtering, 500)
  //   if (this.data.column.field === 'categoryGrade') {
  //     this.dataSource = [
  //       { text: this.$t('A类'), value: 'A' },
  //       { text: this.$t('B类'), value: 'B' },
  //       { text: this.$t('C类'), value: 'C' }
  //     ]
  //     this.fields = { text: 'text', value: 'value' }
  //   }
  // },

  // methods: {
  //   filtering(e) {
  //     let params = {
  //       fuzzyNameOrCode: e.text
  //     }
  //     this.$API.maintainOutsideItem.criteriaQuery(params).then((res) => {
  //       res.data.map((item) => {
  //         item.categoryName = item.categoryCode + '-' + item.categoryName
  //       })
  //       this.dataSource = res.data
  //     })
  //     // var searchData = this.dataSource
  //     // // this.purOrderQueryOrder(e.text);
  //     // // load overall data when search key empty.
  //     // if (e.text == '') e.updateData(searchData)
  //     // else {
  //     //   let query = new Query().select(['categoryCode', 'categoryCode'])
  //     //   // change the type of filtering
  //     //   query = e.text !== '' ? query.where('categoryCode', 'contains', e.text, true) : query
  //     //   e.updateData(searchData, query)
  //     // }
  //   },

  //   // getCategoryItem() {

  //   //   //物料下拉

  //   //   this.$API.masterData.getCategoryItem().then((res) => {

  //   //     this.dataSource = res.data || [];

  //   //   });

  //   //   this.fields = { text: "itemCode", value: "itemCode" };

  //   // },

  //   startOpen() {
  //     console.log(this.data, '下拉打开时最新的行数据')

  //     // if (this.data.column.field === 'siteName') {
  //     //   //工厂下拉

  //     //   if (!this.data.itemCode && !this.itemCode) {
  //     //     this.$toast({
  //     //       content: this.$t('请先选择物料信息'),

  //     //       type: 'warning'
  //     //     })
  //     //   }
  //     // }

  //     // if (this.data.column.field === 'batch') {
  //     //   console.log(this.entryData)

  //     //   // console.log(this.plan);

  //     //   // console.log(this.dataSource);

  //     //   // console.log(this.data);

  //     //   // this.$bus.$on("batchChange", (val) => {

  //     //   //   console.log("......", this.data, val);

  //     //   //   val.forEach((item) => {

  //     //   //     item.name = item.batch; //   客户名称

  //     //   //     item.value = item.batch; //  客户名称

  //     //   //   });

  //     //   //   this.dataSource = val || [];

  //     //   //   this.fields = { text: "name", value: "value" };

  //     //   //   console.log(this.dataSource);

  //     //   // }); //接受的单位

  //     //   // console.log(this.data.column.entryData);

  //     //   // if (this.dataSource.length === 0) {

  //     //   //   let params = {

  //     //   //     buyerEnterpriseId: this.entryData.buyerEnterpriseId,

  //     //   //     orderQuantity: this.entryData.quantity,

  //     //   //     quantity: this.data.itemData.quantity,

  //     //   //     siteCode: this.entryData.siteCode,

  //     //   //     supplierCode: this.entryData.supplierCode,

  //     //   //     nature: "1",

  //     //   //     itemCode: this.data.itemData.itemCode,

  //     //   //   };

  //     //   //   this.$API.outsourcing.queryStock(params).then((r) => {

  //     //   //     r.data.forEach((item) => {

  //     //   //       item.name = item.batch; //   客户名称

  //     //   //       item.value = item.batch; //  客户名称

  //     //   //     });

  //     //   //     this.dataSource = r.data || [];

  //     //   //     this.fields = { text: "name", value: "value" };

  //     //   //   });

  //     //   // }
  //     // }
  //   },

  //   selectChange(val) {
  //     console.log(val.itemData, '下拉数据的信息')

  //     if (this.data.column.field === 'categoryName') {
  //       // this.data.categoryName = val.categoryName
  //       this.$bus.$emit('categoryCode', val.itemData)
  //     }
  //     // if (this.data.column.field === 'batch') {

  //     //   // console.log("---batch", this.data);

  //     //   this.$bus.$emit('batchChange2', val.itemData)
  //     // }

  //     // if (this.data.column.field === 'itemCode') {
  //     //   //物料下拉 //传给物料名称

  //     //   this.data.quantity = val.itemData.quantity

  //     //   console.log(this.data)

  //     //   val.itemData.warehouseName = this.$bus.$emit(
  //     //     'itemNameChange',

  //     //     val.itemData
  //     //   )

  //     //   // console.log("this.entryData", this.entryData);

  //     //   let params = {
  //     //     buyerEnterpriseId: this.entryData.buyerEnterpriseId,

  //     //     orderQuantity: this.entryData.quantity,

  //     //     quantity: val.itemData.quantity,

  //     //     siteCode: this.entryData.siteCode,

  //     //     supplierCode: this.entryData.supplierCode,

  //     //     nature: '1',

  //     //     buyerOrgCode: this.entryData.buyerOrgCode,

  //     //     id: this.data.id,

  //     //     itemCode: val.itemData.itemCode
  //     //   }

  //     //   // this.$API.outsourcing.queryStock(params).then((r) => {

  //     //   //   this.$bus.$emit("batchChange", r.data);

  //     //   // });
  //     //   console.log(this.entryData)
  //     //   this.$API.outsourcing.supplierQueryWmsStock(params).then((r) => {
  //     //     this.$bus.$emit('warehouseChange', r.data)
  //     //     if (r.data.length === 0) {
  //     //       this.$API.outsourcing.supplierQueryMaxDemand(params).then((res) => {
  //     //         this.$bus.$emit('maxDemandQuantityChange', res.data)
  //     //       })
  //     //     }
  //     //   })
  //     // }

  //     // if (this.data.column.field === 'warehouseName') {
  //     //   console.log(val)

  //     //   this.$bus.$emit('batchChange', val.itemData)
  //     // }
  //   }
  // },
  // beforeDestroy() {
  //   this.$bus.$off('categoryCode')
  //   // this.$bus.$off('batchChange')
  //   // this.$bus.$off('maxDemandQuantityChange')
  //   // this.$bus.$off('warehouseChange')
  //   // this.$bus.$off('itemNameChange')
  //   // this.$bus.$off('warehouseChange2')
  // }
}
</script>
<style lang="scss" scoped>
.editQualifiedCycle {
  width: 100%;
  .mt-input {
    width: 49%;
    display: inline-block;
    vertical-align: middle;
  }
  .text {
    width: 49%;
    display: inline-block;
    vertical-align: middle;
  }
}
</style>
