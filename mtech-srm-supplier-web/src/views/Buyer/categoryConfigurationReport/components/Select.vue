<template>
  <div>
    <!-- 采购组织编码 -->
    <RemoteAutocomplete
      v-if="data.column.field === 'purchaseOrgCode'"
      id="purchaseOrgCode"
      method="post"
      records-position="data.records"
      :url="$API.masterData.getBusinessOrganizationListUrl"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :remote-search="true"
      :search-fields="['organizationCode', 'organizationName']"
      v-model="data[data.column.field]"
      :fields="{ value: 'organizationCode', text: 'organizationName' }"
      :title-switch="false"
      :placeholder="$t('请选择采购组织')"
      select-type="businessOrganization"
      popup-width="300"
      @change="selectChange"
    />
    <RemoteAutocomplete
      v-else-if="
        [
          'PURCHASER',
          'PURCHASING_MANAGER',
          'PMC',
          'MC_TEAM_LEADER',
          'MC_MANAGER',
          'QUALITY_VQA',
          'PET',
          'QUALITY_MINISTER',
          'CEG_RD_MEMBER',
          'CEG_RD_LEADER',
          'CEG_QUALITY_MEMBER',
          'CEG_QUALITY_LEADER',
          'CEG_COST_MEMBER',
          'CEG_COST_LEADER',
          'CEG_PURCHASING_MEMBER',
          'CEG_PURCHASING_LEADER',
          'RD_REVIEW_LEADER',
          'QUALITY_REVIEW_LEADER',
          'COST_REVIEW_LEADER',
          'PURCHASING_REVIEW_LEADER',
          'SUPPLIER_OPERATION_OFFICER',
          'PURCHASING_DIRECTOR',
          'SUPPLY_CHAIN_GENERAL_MANAGER',
          'PRICING_OFFICER',
          'PRICING_MANAGER',
          'RD_GENERAL_MANAGER',
          'QUALITY_DIRECTOR',
          'FINANCIAL_DIRECTOR'
        ].includes(data.column.field)
      "
      :id="data.column.field"
      url="/masterDataManagement/tenant/employee/paged-query"
      :get-popup-container="() => $refs.templateRef.parentElement.parentElement.parentElement"
      :remote-search="true"
      v-model="data[data.column.field]"
      :fields="{ value: 'externalCode', text: 'employeeName' }"
      :search-fields="['employeeName', 'externalCode']"
      :title-switch="false"
      :multiple="true"
      :placeholder="$t('请选择')"
      popup-width="300"
      @multiChange="multiChange"
    />
    <mt-select
      v-else
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :filtering="filtering"
      :disabled="
        data.id && (data.column.field == 'enterpriseName' || data.column.field == 'categoryName')
      "
      :allow-filtering="true"
    ></mt-select>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import {
  templateENUM,
  levelENUM,
  validENUM,
  domainENUM,
  quoteMethodENUM,
  mapSelectionENUM,
  costMouldENUM,
  effectiveMethodENUM,
  deliveryMethodENUM,
  bussinessUnitENUM,
  gradingResultENUM,
  groupingNameENUM
} from './enum'
import cloneDeep from 'lodash/cloneDeep'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: '', value: '' },

      dataSource: [],

      userArrList: []
    }
  },

  mounted() {
    this.filtering = utils.debounce(this.filtering, 1000)

    //品类编码
    if (this.data.column.field === 'categoryCode') {
      this.$loading()
      let params = {
        fuzzyNameOrCode: this.data.categoryCode
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.dataSource = _data
          this.fields = { text: 'text', value: 'categoryCode' }
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    }

    if (this.data.column.field === 'levelCode') {
      this.dataSource = levelENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'enableCostMould') {
      this.dataSource = costMouldENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'mapSelectionEnable') {
      this.dataSource = mapSelectionENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'orgCode') {
      this.dataSource = bussinessUnitENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'valid') {
      this.dataSource = validENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'groupingName') {
      this.dataSource = groupingNameENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'domainCode') {
      this.dataSource = domainENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'quoteMethod') {
      this.dataSource = quoteMethodENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'effectiveMethod') {
      this.dataSource = effectiveMethodENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'deliveryMethod') {
      this.dataSource = deliveryMethodENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'gradingResult') {
      this.dataSource = gradingResultENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'ASSESS') {
      this.dataSource = templateENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'DEVELOP') {
      this.dataSource = templateENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'QSA') {
      this.dataSource = templateENUM
      this.fields = { text: 'text', value: 'value' }
    }
    if (this.data.column.field === 'QPA') {
      this.dataSource = templateENUM
      this.fields = { text: 'text', value: 'value' }
    }
  },

  methods: {
    // 模糊搜索
    filtering(e) {
      if (this.data.column.field === 'categoryCode') {
        this.$loading()
        let params = {
          fuzzyNameOrCode: e.text
        }
        this.$API.maintainOutsideItem
          .criteriaQuery(params)
          .then((res) => {
            res.data.map((item) => {
              item.text = item.categoryCode + '-' + item.categoryName
            })
            e.updateData(res.data)
            this.$hloading()
          })
          .catch((error) => {
            this.$hloading()
            this.$toast({
              content: error.msg || this.$t('查询失败，请重试'),
              type: 'error'
            })
          })
      }
    },

    startOpen() {
      console.log(this.data, '下拉打开时最新的行数据')
    },

    selectChange(e) {
      if (this.data.column.field === 'enterpriseName') {
        this.$bus.$emit('crrEnterpriseNamebus', e.itemData)
      }
      if (this.data.column.field === 'categoryCode') {
        let params = {
          fieldCode: 'categoryName',
          itemInfo: {
            categoryId: e.itemData.id,
            categoryCode: e.itemData.categoryCode,
            categoryName: e.itemData.categoryName
          }
        }
        this.$parent.$emit('parentcategoryCode', params)
        this.$bus.$emit('maintainOutsideItemcategoryCode', e.itemData)
      }
      if (this.data.column.field === 'purchaseOrgCode') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'purchaseOrgCode',
          itemInfo: {
            purchaseOrgCode: e.itemData?.organizationCode || null,
            purchaseOrgName: e.itemData?.organizationName || null
          }
        })
        this.$bus.$emit('purchaseOrgNameChange', { purchaseOrgName: e.itemData?.organizationName })
      }
    },
    multiChange(e) {
      this.userArrList = e
      const curFieldKeyCode = this.data.column.field + '_CODE'
      const curFieldKeyName = this.data.column.field + '_NAME'
      let codeList = e.map((v) => v.externalCode)
      let nameList = e.map((v) => v.employeeName)
      let params = {
        fieldCode: this.data.column.field,
        itemInfo: {
          [curFieldKeyCode]: codeList.join(','),
          [curFieldKeyName]: nameList.join(',')
        }
      }
      this.$parent.$emit('parentcategoryCode', params)
    }
  },
  beforeDestroy() {
    this.$bus.$off('crrEnterpriseNamebus')
    this.$bus.$off('crrCategoryCodebus')
    this.$bus.$off('crrCategoryTypebus')
    this.$bus.$off('maintainOutsideItemcategoryCode')
  }
}
</script>
