<!-- 操作记录 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :height="750"
    width="75%"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <sc-table
        ref="sctableRef"
        grid-id="8af78fe9-bba7-432f-8881-42483dafa0f8"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        keep-source
        :sortable="false"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
      </sc-table>
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: { ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],

      loading: false,
      tableData: [],
      toolbar: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },

      orgOptions: [
        { text: this.$t('空调事业部'), value: 53 },
        { text: this.$t('白电事业部'), value: 54 },
        { text: this.$t('泛智屏事业部'), value: 55 }
      ],

      validOptions: [
        { text: this.$t('生效'), value: 2 },
        { text: this.$t('失效'), value: 3 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          field: 'categoryCode',
          title: this.$t('品类编码')
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称')
        },
        {
          field: 'orgCode',
          title: this.$t('事业部'),
          formatter: ({ cellValue }) => {
            const item = this.orgOptions.find((v) => v.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织编码')
        },
        {
          field: 'purchaseOrgName',
          title: this.$t('采购组织名称')
        },
        {
          field: 'PURCHASER',
          title: this.$t('采购员'),
          formatter: ({ row }) => {
            return row['PURCHASER_CODE'] ? row['PURCHASER_CODE'] + '-' + row['PURCHASER_NAME'] : ''
          }
        },
        {
          field: 'PRICING_OFFICER',
          title: this.$t('核价员'),
          formatter: ({ row }) => {
            return row['PURCHASING_MANAGER_CODE']
              ? row['PURCHASING_MANAGER_CODE'] + '-' + row['PURCHASING_MANAGER_NAME']
              : ''
          }
        },
        {
          field: 'PMC',
          title: this.$t('PMC'),
          formatter: ({ row }) => {
            return row['PMC_CODE'] ? row['PMC_CODE'] + '-' + row['PMC_NAME'] : ''
          }
        },
        {
          field: 'valid',
          title: this.$t('是否生效'),
          formatter: ({ cellValue }) => {
            const item = this.validOptions.find((v) => v.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'operationUserName',
          title: this.$t('操作人')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        }
      ]
    }
  },
  mounted() {
    this.getTableData()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        categoryConfigId: this.modalData.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      const res = await this.$API.categoryConfigurationReport
        .pageQueryRecordApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = this.formatData(res.data?.records)
        this.tableData = records
      }
    },
    formatData(arr) {
      let data = []
      arr.forEach((item) => {
        let obj = JSON.parse(item.originalData)
        let personConfig = JSON.parse(obj.personConfig)
        for (let key in personConfig) {
          obj[key] = personConfig[key]
        }
        data.push({
          operationUserName: item.operationUserName,
          ...obj
        })
      })
      return data
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        id: this.modalData.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.moldManagement
        .exportLogFactoryAuditManagementApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    .dialog-content {
      padding: 10px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
