import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
// import gradeDiv from '../components/gradeDiv.vue'
// import inputShow from '../components/inputShow.vue'
import InputView from '../components/inputView.vue'
import Vue from 'vue'
import {
  // templateENUM,
  levelENUM,
  validENUM,
  domainENUM,
  quoteMethodENUM,
  mapSelectionENUM,
  costMouldENUM,
  effectiveMethodENUM,
  deliveryMethodENUM,
  bussinessUnitENUM,
  gradingResultENUM,
  // getCurPerson,
  groupingNameENUM
} from '../components/enum.js'

// import editQualifiedCycle from '../components/editQualifiedCycle.vue'
// import editQualifiedCycleDiv from '../components/editQualifiedCycleDiv.vue'
// async function list(user) {
//   let params = {
//     fuzzyName: user,
//     orgLevelCode: 'ORG05',
//     orgType: 'ORG001ADM'
//   }

//   const res = await this.$API.categoryRelationshipReport.getBuyerList(params)
//   const result = {}
//   result[res.data[0].accountName] = res.data[0].employeeName
//   return result
// }

export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Delete', icon: 'icon_solid_Createorder', title: i18n.t('删除') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') },
  { id: 'Record', icon: '', title: i18n.t('操作记录') }
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: 250,
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('categoryCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('品类编码')}}</span>
              </div>
            `
        })
      }
    },

    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    headerTemplate: () => {
      return {
        template: Vue.component('categoryNameTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('品类名称')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: InputView }
    },
    allowEditing: false
  },

  {
    field: 'levelCode',
    headerText: i18n.t('层级'),
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.levelCode) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = levelENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: levelENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'orgCode',
    headerText: i18n.t('事业部'),
    headerTemplate: () => {
      return {
        template: Vue.component('orgCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('事业部')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.orgCode) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = bussinessUnitENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: bussinessUnitENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    width: 250,
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaseOrgCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购组织编码')}}</span>
              </div>
            `
        })
      }
    },

    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织名称'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaseOrgNameTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购组织名称')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: InputView }
    },
    allowEditing: false
  },
  {
    field: 'valid',
    headerText: i18n.t('是否生效'),
    headerTemplate: () => {
      return {
        template: Vue.component('categoryCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('是否生效')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.valid) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = validENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: validENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'groupingName',
    headerText: i18n.t('组别'),
    headerTemplate: () => {
      return {
        template: Vue.component('categoryCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('组别')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.groupingName) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = groupingNameENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    }
    // searchOptions: {
    //   elementType: 'select',
    //   dataSource: groupingNameENUM,
    //   fields: { text: 'text', value: 'value' }
    // }
  },
  {
    field: 'medCateg',
    headerText: i18n.t('重分类中类'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'enableCostMould',
    headerText: i18n.t('成本模型'),
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.enableCostMould) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = costMouldENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: costMouldENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'mapSelectionEnable',
    headerText: i18n.t('能力地图和选型沙盘'),
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.mapSelectionEnable) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = mapSelectionENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: mapSelectionENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'domainCode',
    headerText: i18n.t('归属领域'),
    headerTemplate: () => {
      return {
        template: Vue.component('categoryCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('归属领域')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.domainCode) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = domainENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: domainENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMethod',
    headerText: i18n.t('询报价模式'),
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.quoteMethod) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = quoteMethodENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: quoteMethodENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'effectiveMethod',
    headerText: i18n.t('供应商引入'),
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.effectiveMethod) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = effectiveMethodENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: effectiveMethodENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'minSupplierQuality',
    headerText: i18n.t('最少供应商数量'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'deliveryMethod',
    headerText: i18n.t('交货模式'),
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.deliveryMethod) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = deliveryMethodENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: deliveryMethodENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'gradingResult',
    headerText: i18n.t('分层分级结果'),
    template: () => {
      return {
        template: Vue.component('template-span', {
          template: `
              <span>{{ getStatusLabel(data.gradingResult) }}</span>`,
          methods: {
            getStatusLabel(status) {
              let label = gradingResultENUM.filter((j) => j.value === status)
              return label[0]['text']
            }
          }
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: gradingResultENUM,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'ASSESS',
    headerText: i18n.t('绩效模板'),
    // template: () => {
    //   return {
    //     template: Vue.component('template-span', {
    //       template: `
    //           <span>{{ getStatusLabel(data.ASSESS) }}</span>`,
    //       methods: {
    //         getStatusLabel(status) {
    //           let label = templateENUM.filter((j) => j.value === status)
    //           return label[0]['text']
    //         }
    //       }
    //     })
    //   }
    // },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'DEVELOP',
    headerText: i18n.t('研发审查模板'),
    // template: () => {
    //   return {
    //     template: Vue.component('template-span', {
    //       template: `
    //           <span>{{ getStatusLabel(data.DEVELOP) }}</span>`,
    //       methods: {
    //         getStatusLabel(status) {
    //           let label = templateENUM.filter((j) => j.value === status)
    //           return label[0]['text']
    //         }
    //       }
    //     })
    //   }
    // },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'QSA',
    headerText: i18n.t('QSA模板'),
    // template: () => {
    //   return {
    //     template: Vue.component('template-span', {
    //       template: `
    //           <span>{{ getStatusLabel(data.QSA) }}</span>`,
    //       methods: {
    //         getStatusLabel(status) {
    //           let label = templateENUM.filter((j) => j.value === status)
    //           return label[0]['text']
    //         }
    //       }
    //     })
    //   }
    // },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'QPA',
    headerText: i18n.t('QPA模板'),
    // template: () => {
    //   return {
    //     template: Vue.component('template-span', {
    //       template: `
    //           <span>{{ getStatusLabel(data.QPA) }}</span>`,
    //       methods: {
    //         getStatusLabel(status) {
    //           let label = templateENUM.filter((j) => j.value === status)
    //           return label[0]['text']
    //         }
    //       }
    //     })
    //   }
    // },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'PURCHASER',
    headerText: i18n.t('采购员'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaserTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购员')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.PURCHASER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'PURCHASING_MANAGER',
    headerText: i18n.t('采购经理'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaserManagerTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购经理')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.PURCHASING_MANAGER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'PRICING_OFFICER',
    headerText: i18n.t('核价员'),
    allowEditing: true,
    headerTemplate: () => {
      return {
        template: Vue.component('pricingOfficerTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('核价员')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.PRICING_OFFICER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'PRICING_MANAGER',
    headerText: i18n.t('核价经理'),
    allowEditing: true,
    headerTemplate: () => {
      return {
        template: Vue.component('PRICING_MANAGER', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('核价经理')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.PRICING_MANAGER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'PMC',
    headerText: i18n.t('PMC'),
    headerTemplate: () => {
      return {
        template: Vue.component('PMC', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('PMC')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.PMC_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'MC_TEAM_LEADER',
    headerText: i18n.t('MC组长'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.MC_TEAM_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'MC_MANAGER',
    headerText: i18n.t('MC经理'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.MC_MANAGER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'QUALITY_VQA',
    headerText: i18n.t('质量VQA'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.QUALITY_VQA_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'PET',
    headerText: i18n.t('PET'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.PET_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'QUALITY_MINISTER',
    headerText: i18n.t('质量部长'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.QUALITY_MINISTER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'CEG_RD_MEMBER',
    headerText: i18n.t('研发-组员'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.CEG_RD_MEMBER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'CEG_RD_LEADER',
    headerText: i18n.t('研发-组长'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.CEG_RD_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'CEG_QUALITY_MEMBER',
    headerText: i18n.t('质量-组员'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.CEG_QUALITY_MEMBER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'CEG_QUALITY_LEADER',
    headerText: i18n.t('质量-组长'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.CEG_QUALITY_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'CEG_COST_MEMBER',
    headerText: i18n.t('成本-组员'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.CEG_COST_MEMBER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'CEG_COST_LEADER',
    headerText: i18n.t('成本-组长'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.CEG_COST_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'CEG_PURCHASING_MEMBER',
    headerText: i18n.t('采购-组员'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.CEG_PURCHASING_MEMBER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'CEG_PURCHASING_LEADER',
    headerText: i18n.t('采购-组长'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.CEG_PURCHASING_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'RD_REVIEW_LEADER',
    headerText: i18n.t('研发评审组长'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.RD_REVIEW_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'QUALITY_REVIEW_LEADER',
    headerText: i18n.t('质量评审组长'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.QUALITY_REVIEW_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'COST_REVIEW_LEADER',
    headerText: i18n.t('成本评审组长'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.COST_REVIEW_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'PURCHASING_REVIEW_LEADER',
    headerText: i18n.t('采购评审组长'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.PURCHASING_REVIEW_LEADER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'SUPPLIER_OPERATION_OFFICER',
    headerText: i18n.t('供应商运营专员'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.SUPPLIER_OPERATION_OFFICER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'RD_GENERAL_MANAGER',
    headerText: i18n.t('研发总经理'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.RD_GENERAL_MANAGER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'QUALITY_DIRECTOR',
    headerText: i18n.t('质量总监'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.QUALITY_DIRECTOR_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'FINANCIAL_DIRECTOR',
    headerText: i18n.t('财务总监'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.FINANCIAL_DIRECTOR_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },

  {
    field: 'PURCHASING_DIRECTOR',
    headerText: i18n.t('采购总监'),
    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.PURCHASING_DIRECTOR_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'SUPPLY_CHAIN_GENERAL_MANAGER',
    headerText: i18n.t('供应链总经理'),
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('purchaser', {
          template: `
              <div>
                <span>{{data.SUPPLY_CHAIN_GENERAL_MANAGER_TEXT}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('更新日期'),
    ignore: true,
    allowEditing: false
  }
]
