<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <!-- 导入弹框 -->
    <!-- <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog> -->
  </div>
</template>

<script>
import { columnData } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: [
            { id: 'Add', icon: 'icon_solid_Createorder', title: this.$t('暂停/恢复供货') },
            {
              id: 'edit',
              icon: 'icon_table_edit',
              title: this.$t('编辑')
            },
            'Delete',
            {
              id: 'submit',
              icon: 'icon_solid_submit',
              title: this.$t('提交')
            },
            {
              id: 'audit',
              icon: 'icon_solid_editsvg',
              title: this.$t('查看OA审批')
            },
            {
              id: 'download',
              icon: 'icon_solid_export',
              title: this.$t('下载处理报告')
            }
          ],

          useToolTemplate: false,
          grid: {
            columnData: columnData,
            frozenColumns: 3,
            asyncConfig: {
              url: '/supplier/tenant/buyer/apply/abnormal/query'
            }
          }
        }
      ]
      // downTemplateParams: {
      //   pageFlag: false
      // }, // 导入下载模板参数
      // uploadParams: {}, // 导入文件参数
      // // 导入请求接口配置
      // requestUrls: {
      //   templateUrlPre: 'sourceFiles',
      //   templateUrl: 'exportData', // 下载模板接口方法名
      //   uploadUrl: 'importData' // 上传接口方法名
      // }
    }
  },
  methods: {
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (records.length <= 0 && !(item.toolbar.id == 'Add' || item.toolbar.id == 'upload')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'Add') {
        this.handleClickToolBarAdd()
      }
      if (item.toolbar.id == 'Delete') {
        this.handleClickToolBarDelete(records)
      }
      if (item.toolbar.id == 'submit') {
        this.handleClickToolBarSubmit(records)
      }
      if (item.toolbar.id == 'edit') {
        this.handleClickToolBarEdit(records)
      }
      if (item.toolbar.id == 'audit') {
        this.handleClickToolBarAudit(records)
      }
    },
    handleClickToolBarAdd() {
      this.$router.push({
        path: '/supplier/abnormal-supplier-handDetails',
        query: {
          type: 'Add'
        }
      })
    },
    handleClickToolBarDelete(records) {
      let _this = this
      let _applyIdList = records.map((item) => item.id)
      this.$API.abnormalSupplierHand.del({ applyIdList: _applyIdList }).then(() => {
        _this.$refs.templateRef.refreshCurrentGridData()
        _this.$toast({ content: this.$t('删除成功'), type: 'success' })
      })
    },
    handleClickToolBarSubmit(records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交数据？')
        },
        success: () => {
          let _this = this
          let _applyIdList = records.map((item) => item.id)
          this.$API.abnormalSupplierHand.submit({ applyIdList: _applyIdList }).then(() => {
            _this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          })
        }
      })
    },
    handleClickToolBarEdit(records) {
      if (records.length > 1) {
        this.$toast({ content: this.$t('只能选择一行'), type: 'warning' })
        return
      }
      if (records[0].applyStatus != 10 && records[0].applyStatus != 30) {
        this.$toast({ content: this.$t('待审批、已完成状态不可编辑'), type: 'warning' })
        return
      }
      this.$router.push({
        path: '/supplier/abnormal-supplier-handDetails',
        query: {
          type: 'edit',
          id: records[0].id
        }
      })
      console.log(records)
    },
    handleClickToolBarAudit(records) {
      if (records.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (records.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: records[0].id,
        businessType: ''
        // businessKey: records[0].approveCode
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field == 'abnormalReportFile') {
        this.abnormalReportFile(data)
      } else if (field == 'rectificationCode') {
        this.rectificationCode(data)
      }
    },
    abnormalReportFile(_data) {
      let data = _data.abnormalReportFile
      if (!data) {
        return
      } else {
        let params = {
          useType: 2,
          id: data.fileId
        }
        this.$API.abnormalSupplierHand.downloadPrivateFile(params).then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    },
    rectificationCode(data) {
      let _rectificationUrl = `/${data.rectificationUrl.split('/#/')[1]}-detail`
      this.$router.push({
        path: _rectificationUrl,
        query: {
          type: 'detail',
          id: data.rectificationId
        }
      })
    }
    // 显示隐藏上传弹框
    // showUploadExcel(flag) {
    //   console.log(flag)
    //   if (flag) {
    //     this.$refs.uploadExcelRef.uploadData = null // 清空数据
    //     this.$refs.uploadExcelRef.fileLength = 0
    //     this.$refs.uploadExcelRef.$refs.uploader.files = []
    //     this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
    //   } else {
    //     this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
    //   }
    // },
    // // 上传成功后
    // upExcelConfirm() {
    //   this.showUploadExcel(false)
    //   this.$toast({
    //     content: this.$t('导入成功'),
    //     type: 'success'
    //   })
    //   this.$refs.templateRef.refreshCurrentGridData()
    // },
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
.dialog-content {
  margin-top: 20px;
}
</style>
