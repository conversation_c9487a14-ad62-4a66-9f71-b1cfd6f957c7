import { i18n } from '@/main.js'
import utils from '@/utils/utils'
// import Vue from 'vue'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('单据编号')
  },
  {
    field: 'rectificationCode',
    headerText: i18n.t('整改单号'),
    cssClass: 'field-content'
  },
  {
    field: 'applyName',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('待提交'),
        20: i18n.t('待审批'),
        30: i18n.t('已驳回'),
        40: i18n.t('已完成')
      }
    }
  },
  {
    width: 100,
    field: 'suspendType',
    headerText: i18n.t('暂停类型'),
    valueConverter: {
      type: 'map',
      map: {
        supplier: i18n.t('供应商'),
        category: i18n.t('品类'),
        material: i18n.t('物料')
      }
    }
  },
  {
    width: 150,
    field: 'updateTime',
    headerText: i18n.t('单据日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    },
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000 - 1440000)

        //自定义搜索值，规则
        return obj
      },
      renameField: 'updateTime'
    }
  },
  {
    width: 150,
    field: 'businessType',
    headerText: i18n.t('申请类别'),
    valueConverter: {
      type: 'map',
      map: {
        rectification: i18n.t('期限整改'),
        suspend: i18n.t('暂停供方资格'),
        restore: i18n.t('恢复供货资格')
      }
    }
  },
  {
    field: 'orgNames',
    headerText: i18n.t('公司')
  },
  {
    field: 'supplierNames',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryNames',
    headerText: i18n.t('品类名称')
  },
  {
    width: 150,
    field: 'itemCodes',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemNames',
    headerText: i18n.t('物料名称')
  },
  {
    width: 100,
    field: 'abnormalType',
    headerText: i18n.t('异常来源'),
    valueConverter: {
      type: 'map',
      map: {
        complaint: i18n.t('客诉'),
        process: i18n.t('制程'),
        supplied: i18n.t('来料'),
        other: i18n.t('其他')
      }
    }
  },
  {
    width: 150,
    field: 'abnormalReportFile',
    headerText: i18n.t('异常报告'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (!e) {
          return ''
        } else {
          return i18n.t('点击下载')
        }
      }
    }
  },
  {
    field: 'abnormalDescribe',
    headerText: i18n.t('异常描述')
  },
  {
    width: 150,
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: 150,
    field: 'rectificationExpireTime',
    headerText: i18n.t('整改截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              // return e.substr(0,10);
              return utils.formateTime(new Date(e), 'yyyy-MM-dd')
            } else {
              let val = parseInt(e)
              return utils.formateTime(val, 'yyyy-MM-dd')
            }
          } else if (typeof e == 'number') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    },
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000 - 1440000)

        //自定义搜索值，规则
        return obj
      },
      renameField: 'updateTime'
    }
  }
]
