<template>
  <div class="supplierAbnormal">
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="navbutton"
    >
      <div class="btns-wrap">
        <mt-button class="e-flat" @click="$router.push('/supplier/abnormal-supplier-hand')">{{
          $t('返回')
        }}</mt-button>
        <mt-button class="e-flat" @click="clickButtonSave">{{ $t('保存') }}</mt-button>
        <mt-button class="e-flat" @click="clickButtonSubmit">{{ $t('提交') }}</mt-button>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('基本信息') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="formObject" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('单据编号')"
            label-style="top"
            prop="applyCode"
          >
            <mt-input
              :disabled="true"
              v-model="formObject.applyCode"
              :placeholder="$t('')"
              width="300"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('单据名称')"
            label-style="top"
            prop="applyName"
          >
            <mt-input
              :disabled="false"
              v-model="formObject.applyName"
              :placeholder="$t('请输入')"
              width="300"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('异常来源')"
            label-style="top"
            prop="abnormalType"
          >
            <mt-select
              :disabled="false"
              v-model="formObject.abnormalType"
              :data-source="abnormalSource"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
              @change="change($event, 'abnormalType')"
              width="300"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="abnormal">
        <mt-form ref="formInfo1" :model="formObject" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="abnormal-description"
            :label="$t('异常描述')"
            label-style="top"
            prop="abnormalDescribe"
          >
            <mt-input
              ref="editorRef"
              v-model="formObject.abnormalDescribe"
              :multiline="true"
              :rows="2"
              maxlength="2000"
              float-label-type="Never"
              :placeholder="$t('字数不超过2000字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('异常详情') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo2" :model="formObject" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('申请类别')"
            label-style="top"
            prop="businessType"
          >
            <mt-select
              :disabled="false"
              v-model="formObject.businessType"
              :data-source="applicationCategoryArr"
              :placeholder="$t('请选择')"
              @change="change($event, 'businessType')"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('反馈截止时间')"
            label-style="top"
            prop="rectificationExpireTime"
          >
            <mt-date-picker
              :width="300"
              v-model="formObject.rectificationExpireTime"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('暂停类型')"
            label-style="top"
            prop="suspendType"
          >
            <mt-select
              :disabled="false"
              :width="300"
              :data-source="suspensionTypeArr"
              v-model="formObject.suspendType"
              :placeholder="$t('请选择')"
              @change="change($event, 'suspendType')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="orgCodes" :label="$t('公司')">
            <mt-multi-select
              v-model="formObject.orgCodes"
              float-label-type="Never"
              :disabled="false"
              :data-source="orgList"
              :show-clear-button="true"
              :allow-filtering="true"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              :filtering="(e) => filteringResource(e, orgList, 'orgName')"
              @change="change($event, 'orgCodes')"
              :placeholder="$t('请选择公司')"
              width="300"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item prop="supplierCodes" :label="$t('供应商')">
            <mt-multi-select
              v-model="formObject.supplierCodes"
              float-label-type="Never"
              :disabled="false"
              :data-source="supplierList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'supplierNames', value: 'supplierCode' }"
              :filtering="(e) => filteringResource(e, supplierList, 'supplierNames')"
              @change="change($event, 'supplierCodes')"
              :placeholder="$t('请选择供应商')"
              width="300"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item
            v-if="formObject.suspendType && formObject.suspendType != 'supplier'"
            prop="categoryCodes"
            :label="$t('品类')"
          >
            <mt-multi-select
              v-model="formObject.categoryCodes"
              float-label-type="Never"
              :disabled="false"
              :data-source="categoryList"
              :show-clear-button="true"
              :allow-filtering="true"
              :fields="{ text: 'categoryNames', value: 'categoryCode' }"
              :filtering="(e) => filteringResource(e, categoryList, 'categoryNames')"
              @change="change($event, 'categoryCodes')"
              :placeholder="$t('请选择工厂')"
              width="300"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item
            v-if="
              formObject.suspendType &&
              formObject.suspendType != 'supplier' &&
              formObject.suspendType != 'category'
            "
            prop="itemCodes"
            :label="$t('物料')"
          >
            <mt-multi-select
              v-model="formObject.itemCodes"
              float-label-type="Never"
              :disabled="false"
              :data-source="itemList"
              :show-clear-button="true"
              :allow-filtering="true"
              :fields="{ text: 'itemNames', value: 'itemCode' }"
              :filtering="(e) => filteringResource(e, itemList, 'itemNames')"
              @change="change($event, 'itemCodes')"
              :placeholder="$t('请选择工厂')"
              width="300"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            prop="abnormalReportFile"
            :label="$t('异常报告')"
            label-style="top"
          >
            <mt-common-uploader
              :is-single-file="true"
              :save-url="saveUrl"
              type="line"
              v-model="formObject.abnormalReportFile"
            ></mt-common-uploader>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import commonData from '@/utils/constant'
export default {
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    }
  },
  mounted() {},
  data() {
    return {
      //新值
      //异常来源
      basicExpand: true,
      saveUrl: commonData.publicFileUrl,
      abnormalSource: [
        {
          text: this.$t('客诉'),
          value: 'complaint'
        },
        {
          text: this.$t('制程'),
          value: 'process'
        },
        {
          text: this.$t('来料'),
          value: 'supplied'
        },
        {
          text: this.$t('其他'),
          value: 'other'
        }
      ],
      applicationCategoryArr: [
        {
          text: this.$t('期限整改'),
          value: 'rectification'
        },
        {
          text: this.$t('暂停供方资格'),
          value: 'suspend'
        },
        {
          text: this.$t('恢复供货资格'),
          value: 'restore'
        }
      ],
      suspensionTypeArr: [
        { text: this.$t('供应商'), value: 'supplier' },
        { text: this.$t('品类'), value: 'category' },
        { text: this.$t('物料'), value: 'material' }
      ],
      orgList: [],
      supplierList: [],
      categoryList: [],
      itemList: [],
      formObject: {
        applyCode: '', //单据编号
        applyName: '', //单据名称
        abnormalType: '', //异常来源
        abnormalDescribe: '', //异常描述
        businessType: '', //申请类别
        rectificationExpireTime: '', //反馈截止时间
        suspendType: '', //暂停类型
        orgCodes: [], //公司
        orgList: [],
        supplierCodes: [], //供应商
        supplierList: [],
        categoryCodes: [], //品类
        categoryList: [],
        itemCodes: [], //物料
        itemList: [],
        abnormalReportFile: [] //异常报告
      },
      rules: {
        applyName: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        abnormalType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        businessType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        abnormalReportFile: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        rectificationExpireTime: [{ required: false, message: this.$t('请选择'), trigger: 'blur' }],
        suspendType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orgCodes: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCodes: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryCodes: [{ required: false, message: this.$t('请选择'), trigger: 'blur' }],
        itemCodes: [{ required: false, message: this.$t('请选择'), trigger: 'blur' }]
      },
      editFormObject: {},
      submitQueryId: null
    }
  },
  async created() {
    if (this.queryId) {
      await this.editDetail()
      await this.initialCall()
    } else {
      await this.initialCall()
    }
  },
  methods: {
    async editDetail() {
      await this.$API.abnormalSupplierHand.detail(this.queryId).then((res) => {
        this.editFormObject = cloneDeep(res.data)
        let _abnormalReportFile = this.editFormObject.applyAbnormalDTO.abnormalReportFile
        _abnormalReportFile.id = _abnormalReportFile.fileId
        this.formObject = {
          applyCode: this.editFormObject.infoDTO.applyCode, //单据编号
          applyName: this.editFormObject.infoDTO.applyName, //单据名称
          abnormalType: this.editFormObject.applyAbnormalDTO.abnormalType, //异常来源
          abnormalDescribe: this.editFormObject.applyAbnormalDTO.abnormalDescribe, //异常描述
          businessType: this.editFormObject.applyAbnormalDTO.businessType, //申请类别
          rectificationExpireTime: utils.formateTime(
            new Date(Number(this.editFormObject.applyAbnormalDTO.rectificationExpireTime)),
            'yyyy-MM-dd'
          ), //反馈截止时间
          suspendType: this.editFormObject.applyAbnormalDTO.suspendType, //暂停类型
          orgCodes: [], //公司
          orgList: [],
          supplierCodes: [], //供应商
          supplierList: [],
          categoryCodes: [], //品类
          categoryList: [],
          itemCodes: [], //物料
          itemList: [],
          abnormalReportFile: [_abnormalReportFile] //异常报告
        }
      })
    },
    async initialCall() {
      let _this = this
      this.$loading()
      await this.$API.abnormalSupplierHand
        .company({})
        .then((res) => {
          this.$hloading()
          this.orgList = cloneDeep(res.data)
          if (_this.queryId) {
            let _orgCodes = _this.editFormObject.applyAbnormalDTO.orgCodes.split(',')
            _this.formObject.orgCodes = _orgCodes
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    change(e, text) {
      let _this = this
      //申请类别
      if (text === 'businessType') {
        if (e.value === 'rectification') {
          this.rules.rectificationExpireTime[0].required = true
        } else {
          this.rules.rectificationExpireTime[0].required = false
        }
        if (this.formObject.categoryCodes.length > 0) {
          this.itemList = []
          this.formObject.itemList = []
          this.formObject.itemCodes = []
          this.filterMaterial(e.value)
        }
      }
      //暂停类型
      if (text === 'suspendType') {
        if (e.value === 'supplier') {
          this.rules.categoryCodes[0].required = false
          this.rules.itemCodes[0].required = false
        } else if (e.value === 'category') {
          this.rules.categoryCodes[0].required = true
        } else if (e.value === 'material') {
          this.rules.categoryCodes[0].required = true
          this.rules.itemCodes[0].required = true
        }
        if (e.value !== 1 && this.formObject.orgCodes.length > 0) {
          this.categoryList = []
          this.itemList = []
          this.formObject.supplierList = []
          this.formObject.supplierCodes = []
          this.formObject.categoryList = []
          this.formObject.categoryCodes = []
          this.formObject.itemList = []
          this.formObject.itemCodes = []
        }
      }
      //公司
      if (text === 'orgCodes') {
        this.supplierList = []
        this.categoryList = []
        this.itemList = []
        this.formObject.supplierList = []
        this.formObject.supplierCodes = []
        this.formObject.categoryList = []
        this.formObject.categoryCodes = []
        this.formObject.itemList = []
        this.formObject.itemCodes = []
        if (e.value.length > 0) {
          this.formObject.orgList.length = 0
          this.formObject.orgCodes = e.value
          this.orgList.forEach((item) => {
            if (e.value.indexOf(item.orgCode) != -1) {
              this.formObject.orgList.push({
                orgCode: item.orgCode,
                orgName: item.orgName,
                orgId: item.id
              })
            }
          })
        }
        // 供应商
        let obj = {
          organizationCodeList: this.formObject.orgCodes
        }
        this.$API.abnormalSupplierHand
          .queryIntersectionSupplier(obj)
          .then((res) => {
            let _data = res.data
            _data.map((item) => {
              item.supplierNames = item.supplierCode + '-' + item.supplierName
            })
            _this.supplierList = _data
            if (_this.queryId) {
              let _supplierCodes = _this.editFormObject.applyAbnormalDTO.supplierCodes.split(',')
              _this.formObject.supplierCodes = _supplierCodes
            }
          })
          .catch((err) => {
            this.$toast({
              content: err.msg,
              type: 'error'
            })
          })
      }
      //供应商
      if (text === 'supplierCodes') {
        this.categoryList = []
        this.itemList = []
        this.formObject.categoryList = []
        this.formObject.categoryCodes = []
        this.formObject.itemList = []
        this.formObject.itemCodes = []
        if (e.value.length > 0) {
          this.formObject.supplierList.length = 0
          this.formObject.supplierCodes = e.value
          this.supplierList.forEach((item) => {
            if (e.value.indexOf(item.supplierCode) != -1) {
              this.formObject.supplierList.push({
                supplierCode: item.supplierCode,
                supplierName: item.supplierName,
                partnerCode: item.partnerCode
              })
            }
          })
        }
        //品类
        this.$API.abnormalSupplierHand
          .queryIntersectionCategory({
            organizationCodeList: this.formObject.orgCodes,
            supplierCodeList: this.formObject.supplierCodes
          })
          .then((res) => {
            let _data = res.data
            _data.map((item) => {
              item.categoryNames = item.categoryCode + '-' + item.categoryName
            })
            _this.categoryList = _data
            if (_this.queryId) {
              let _categoryCodes = _this.editFormObject.applyAbnormalDTO.categoryCodes.split(',')
              _this.formObject.categoryCodes = _categoryCodes
            }
          })
      }
      //品类
      if (text === 'categoryCodes') {
        let _statusId = this.formObject.businessType
        if (!_statusId) {
          this.$toast({ content: this.$t('请先选择申请类别'), type: 'warning' })
          this.formObject.categoryList.length = 0
          this.formObject.categoryCodes = []
          return
        }
        this.itemList = []
        this.formObject.itemList = []
        this.formObject.itemCodes = []
        this.formObject.categoryList.length = 0
        this.formObject.categoryCodes = e.value
        this.categoryList.forEach((item) => {
          if (e.value.indexOf(item.categoryCode) != -1) {
            this.formObject.categoryList.push({
              categoryCode: item.categoryCode,
              categoryName: item.categoryName,
              categoryId: item.id
            })
          }
        })
        //物料
        this.filterMaterial(_statusId)
      }
      //物料
      if (text === 'itemCodes') {
        if (e.value.length > 0) {
          this.formObject.itemList.length = 0
          this.formObject.itemCodes = e.value
          this.itemList.forEach((item) => {
            if (e.value.indexOf(item.itemCode) != -1) {
              this.formObject.itemList.push({
                itemCode: item.itemCode,
                itemName: item.itemName,
                itemId: item.id
              })
            }
          })
        }
      }
    },
    clickButtonSave() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$refs.formInfo1.validate((valid1) => {
            if (valid1) {
              this.$refs.formInfo2.validate((valid2) => {
                if (valid2) {
                  let _orgNames = this.formObject.orgList.map((item) => item.orgName)
                  let _supplierNames = this.formObject.supplierList.map((item) => item.supplierName)
                  let _categoryNames =
                    this.formObject.categoryList.length > 0
                      ? this.formObject.categoryList.map((item) => item.categoryName)
                      : []
                  let _itemNames =
                    this.formObject.itemList.length > 0
                      ? this.formObject.itemList.map((item) => item.itemName)
                      : []

                  let params = {
                    infoDTO: {
                      // applyCode: this.formObject.applyCode ? this.formObject.applyCode : '', //单据编号
                      applyName: this.formObject.applyName //单据名称
                    },
                    applyAbnormalDTO: {
                      abnormalType: this.formObject.abnormalType, //异常来源
                      abnormalDescribe: this.formObject.abnormalDescribe
                        ? this.formObject.abnormalDescribe
                        : '', //异常描述
                      businessType: this.formObject.businessType, //申请类别
                      rectificationExpireTime: this.formObject.rectificationExpireTime
                        ? new Date(this.formObject.rectificationExpireTime).getTime()
                        : '', //反馈截止时间
                      suspendType: this.formObject.suspendType, //暂停类型
                      orgCodes: this.formObject.orgCodes.toString(),
                      orgNames: _orgNames.toString(),
                      supplierCodes: this.formObject.supplierCodes.toString(),
                      supplierNames: _supplierNames.toString(),
                      categoryCodes: this.formObject.categoryCodes.toString(),
                      categoryNames: _categoryNames.toString(),
                      itemCodes: this.formObject.itemCodes.toString(),
                      itemNames: _itemNames.toString(),
                      abnormalReportFile:
                        this.formObject.abnormalReportFile.length > 0
                          ? this.formObject.abnormalReportFile[0]
                          : {} //异常报告
                    }
                  }
                  if (this.queryId) {
                    params.infoDTO.id = this.editFormObject.infoDTO.id
                  }
                  this.$API.abnormalSupplierHand.update(params).then(() => {
                    this.$router.push({
                      path: '/supplier/abnormal-supplier-hand'
                    })
                    this.$toast({
                      content: this.$t('保存成功'),
                      type: 'success'
                    })
                  })
                }
              })
            }
          })
        }
      })
    },
    clickButtonSubmit() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$refs.formInfo1.validate((valid1) => {
            if (valid1) {
              this.$refs.formInfo2.validate((valid2) => {
                if (valid2) {
                  this.$loading()
                  let _orgNames = this.formObject.orgList.map((item) => item.orgName)
                  let _supplierNames = this.formObject.supplierList.map((item) => item.supplierName)
                  let _categoryNames =
                    this.formObject.categoryList.length > 0
                      ? this.formObject.categoryList.map((item) => item.categoryName)
                      : []
                  let _itemNames =
                    this.formObject.itemList.length > 0
                      ? this.formObject.itemList.map((item) => item.itemName)
                      : []

                  let params = {
                    infoDTO: {
                      // applyCode: this.formObject.applyCode ? this.formObject.applyCode : '', //单据编号
                      applyName: this.formObject.applyName //单据名称
                    },
                    applyAbnormalDTO: {
                      abnormalType: this.formObject.abnormalType, //异常来源
                      abnormalDescribe: this.formObject.abnormalDescribe
                        ? this.formObject.abnormalDescribe
                        : '', //异常描述
                      businessType: this.formObject.businessType, //申请类别
                      rectificationExpireTime: this.formObject.rectificationExpireTime
                        ? new Date(this.formObject.rectificationExpireTime).getTime()
                        : '', //反馈截止时间
                      suspendType: this.formObject.suspendType, //暂停类型
                      orgCodes: this.formObject.orgCodes.toString(),
                      orgNames: _orgNames.toString(),
                      supplierCodes: this.formObject.supplierCodes.toString(),
                      supplierNames: _supplierNames.toString(),
                      categoryCodes: this.formObject.categoryCodes.toString(),
                      categoryNames: _categoryNames.toString(),
                      itemCodes: this.formObject.itemCodes.toString(),
                      itemNames: _itemNames.toString(),
                      abnormalReportFile:
                        this.formObject.abnormalReportFile.length > 0
                          ? this.formObject.abnormalReportFile[0]
                          : {} //异常报告
                    }
                  }
                  // if (this.queryId || this.submitQueryId) {
                  //   params.infoDTO.id = this.editFormObject.infoDTO.id
                  // }
                  params.infoDTO.id = this.queryId
                    ? this.editFormObject.infoDTO.id
                    : this.submitQueryId
                    ? this.submitQueryId
                    : ''
                  this.$API.abnormalSupplierHand
                    .update(params)
                    .then((res) => {
                      if (res.code == 200 && res.data.infoDTO.id) {
                        this.submitQueryId = res.data.infoDTO.id
                        this.submit(res.data.infoDTO.id)
                      }
                    })
                    .catch((error) => {
                      this.$hloading()
                      this.$toast({
                        content: error.msg,
                        type: 'error'
                      })
                    })
                }
              })
            }
          })
        }
      })
    },
    submit(id) {
      this.$API.abnormalSupplierHand
        .submit({ applyIdList: [id] })
        .then(() => {
          this.$hloading()
          this.$router.push({
            path: '/supplier/abnormal-supplier-hand'
          })
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg,
            type: 'error'
          })
        })
    },
    filterMaterial(_statusId) {
      let _this = this
      this.$API.abnormalSupplierHand
        .queryIntersectionItem({
          organizationCodeList: this.formObject.orgCodes,
          supplierCodeList: this.formObject.supplierCodes,
          categoryCodeList: this.formObject.categoryCodes,
          statusIdList: [
            _statusId == 'rectification'
              ? 1
              : _statusId == 'suspend'
              ? 1
              : _statusId == 'restore'
              ? 3
              : ''
          ]
        })
        .then((res) => {
          let _data = res.data
          _data.map((item) => {
            item.itemNames = item.itemCode + '-' + item.itemName
          })
          _this.itemList = _data
          if (_this.queryId) {
            let _itemCodes = _this.editFormObject.applyAbnormalDTO.itemCodes.split(',')
            _this.formObject.itemCodes = _itemCodes
          }
        })
    },
    // 模糊搜索，不区分大小写模糊搜索
    filteringResource(e, dataSource, key) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        )
      } else {
        e.updateData(dataSource)
      }
    }

    // ---------------------------------------------------->
  }
}
</script>
<style lang="scss" scoped>
.supplierAbnormal {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
.navbutton {
  width: 100%;
  .btns-wrap {
    margin-right: 0;
    /deep/ .mt-button {
      margin-right: 0;
      float: right;
      button {
        background: transparent;
        //border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        box-shadow: unset;
        padding: 6px 12px 4px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
    }
  }
}

.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
    }
  }
}
.abnormal {
  width: 100%;

  .mt-form {
    margin-left: 30px;
    .mt-input {
      margin-top: 20px;
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
// /deep/ .mt-rich-text-editor {
//   margin: 30px 10px 0 10px;
//   .e-toolbar-wrapper {
//     height: 42px !important;
//   }
//   .e-toolbar-items {
//     margin-left: -59px;
//   }
//   .e-rte-content {
//     height: 300px !important;
//   }
// }
.mb-30 {
  // margin-bottom: 30px;
}
</style>
