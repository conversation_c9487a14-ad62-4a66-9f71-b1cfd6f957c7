<template>
  <mt-dialog
    ref="dialogRef"
    :buttons="buttons"
    :header="modalData.title"
    width="550"
    height="350"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="formDataRef" class="form-box" :model="formData" :rules="rules">
        <mt-form-item prop="categoryCode" :label="$t('外部物料组编码')" label-style="top">
          <RemoteAutocomplete
            v-model="formData.categoryCode"
            url="/masterDataManagement/tenant/category/paged-query"
            :fields="{ value: 'categoryCode', text: 'categoryName' }"
            :search-fields="['categoryCode', 'categoryName']"
            :title-switch="false"
            :placeholder="$t('请选择外部物料组')"
            @change="
              ({ itemData }) => {
                formData.categoryName = itemData ? itemData.categoryName : null
              }
            "
          />
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('外部物料组名称')" label-style="top">
          <mt-input
            v-model="formData.categoryName"
            disabled
            :placeholder="$t('请输入外部物料组名称')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        enableFlag: true // 默认启用
      },
      rules: {
        categoryCode: [
          { required: true, message: this.$t('请选择外部物料组编码'), trigger: 'blur' }
        ],
        categoryName: [
          { required: true, message: this.$t('请输入外部物料组名称'), trigger: 'blur' }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.$refs.dialogRef?.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.formDataRef.validate(async (valid) => {
        if (valid) {
          const res = await this.$API.semiconductorRiskManagement.addSemiCategory(this.formData)
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function')
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
