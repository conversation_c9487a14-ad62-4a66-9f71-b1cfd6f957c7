<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="categoryCode" :label="$t('外部物料组')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCode"
            url="/masterDataManagement/tenant/category/paged-query"
            :fields="{ value: 'categoryCode', text: 'categoryName' }"
            :search-fields="['categoryCode', 'categoryName']"
            :title-switch="false"
            :placeholder="$t('请选择外部物料组')"
          />
        </mt-form-item>
        <mt-form-item prop="enable" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.enable"
            :data-source="statusList"
            :show-clear-button="true"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="80fb3728-fb5a-4943-90d4-1e76501e41d3"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      :upload-params="uploadParams"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog.vue'
import pagingMixin from '@/mixins/paging.js'

export default {
  components: { ScTable, CollapseSearch, RemoteAutocomplete, UploadExcelDialog },
  mixins: [pagingMixin],
  data() {
    return {
      tableData: [],
      loading: false,
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' },
        { code: 'enable', name: this.$t('启用'), status: 'info' },
        { code: 'disable', name: this.$t('停用'), status: 'info' },
        { code: 'import', name: this.$t('导入'), status: 'info' }
      ],
      statusList: [
        { value: false, text: this.$t('停用') },
        { value: true, text: this.$t('启用') }
      ],
      downTemplateName: '',
      downTemplateParams: {},
      requestUrls: {},
      uploadParams: {}
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50
        },
        {
          field: 'categoryCode',
          title: this.$t('外部物料组编码')
        },
        {
          field: 'categoryName',
          title: this.$t('外部物料组名称')
        },
        {
          field: 'enableFlag',
          title: this.$t('状态'),
          slots: {
            default: ({ row }) => {
              const selectItem = this.statusList.find((item) => item.value == row.enableFlag)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        }
      ]
    }
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.semiconductorRiskManagement
        .querySemiCategoryList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data.records || []
        this.total = res.data?.total || 0
      }
    },

    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete', 'enable', 'disable'].includes(e.code) && !selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.hanldeAdd()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'enable':
        case 'disable':
          this.handleOperate(e.code, selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        default:
          break
      }
    },
    // 新增
    hanldeAdd() {
      this.$dialog({
        modal: () => import('./dialog/addDialog.vue'),
        data: {
          title: this.$t('新增')
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    // 删除
    handleDelete(list) {
      const idList = []
      for (let index = 0; index < list.length; index++) {
        const { enableFlag } = list[index]
        if (enableFlag) {
          this.$toast({ content: this.$t(`仅可删除状态为【停用】的数据`), type: 'warning' })
          return
        }
        idList.push(list[index].id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(this.$t('确定删除选中的数据?'))
        },
        success: async () => {
          const res = await this.$API.semiconductorRiskManagement.deleteSemiCategory({
            idList: idList.join(',')
          })
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.getTableData()
          }
        }
      })
    },
    // 启用、停用
    handleOperate(type, list) {
      const tipMap = {
        enable: this.$t('启用'),
        disable: this.$t('停用')
      }
      const idList = []
      for (let index = 0; index < list.length; index++) {
        const { enableFlag } = list[index]
        if (type === 'enable' && enableFlag) {
          this.$toast({ content: this.$t(`仅可启用状态为【停用】的数据`), type: 'warning' })
          return
        }
        if (type === 'disable' && !enableFlag) {
          this.$toast({ content: this.$t(`仅可停用状态为【启用】的数据`), type: 'warning' })
          return
        }
        idList.push(list[index].id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`${this.$t('确认') + tipMap[type] + this.$t('选中的数据')}？`)
        },
        success: async () => {
          const params = {
            idList: idList.join(','),
            enable: type === 'enable' ? true : false
          }
          const res = await this.$API.semiconductorRiskManagement.updateSemiCategoryStatus(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.getTableData()
          }
        }
      })
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'semiconductorRiskManagement',
        templateUrl: 'downloadSemiCategoryTemplate',
        uploadUrl: 'importSemiCategory'
      }
      this.showUploadExcel(true)
    },
    // 展示/隐藏上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = []
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.$toast({ type: 'success', content: this.$t('导入成功') })
      this.showUploadExcel(false)
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
