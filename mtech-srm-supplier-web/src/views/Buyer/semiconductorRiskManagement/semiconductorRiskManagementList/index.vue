<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="code" :label="$t('档案编号')" label-style="top">
          <mt-input
            v-model="searchFormModel.code"
            :show-clear-button="true"
            :placeholder="$t('请输入档案编号')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <!-- 暂时只能选择1503 -->
          <mt-select
            v-model="searchFormModel.companyCode"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'companyCode' }"
            :show-clear-button="true"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="51dc6a53-6a1a-4e13-b846-6331a1bd286b"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
// import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'

export default {
  components: { ScTable, CollapseSearch },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      type: 'list',
      tableData: [],
      loading: false
    }
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.semiconductorRiskManagement
        .querySemiRiskList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data.records || []
        this.total = res.data?.total || 0
      }
    },

    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (e.code === 'update' && !selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.hanldeAdd()
          break
        case 'update':
          this.handleUpdate(selectedRecords)
          break
        default:
          break
      }
    },
    // 点击单元格标题
    handleClickCellTitle(row, column) {
      if (column.field === 'code') {
        this.handleViewDetail(row)
      }
    },
    // 新增
    hanldeAdd() {
      this.$dialog({
        modal: () => import('./dialog/addDialog.vue'),
        data: {
          title: this.$t('新增')
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    // 更新
    async handleUpdate(list) {
      const idList = []
      list.forEach((item) => idList.push(item.id))
      const res = await this.$API.semiconductorRiskManagement.updateSemiRiskList({
        idList: idList.join(',')
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t('更新成功'), type: 'success' })
        this.handleSearch()
      }
    },
    // 查看详情
    handleViewDetail(row) {
      sessionStorage.setItem('rowInfo', JSON.stringify(row))
      this.$router.push({
        name: 'semiconductor-risk-management-list-detail',
        query: {
          id: row.id,
          refreshId: Date.now()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
