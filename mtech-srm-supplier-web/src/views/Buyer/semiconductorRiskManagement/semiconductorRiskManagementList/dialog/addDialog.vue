<template>
  <mt-dialog
    ref="dialogRef"
    :buttons="buttons"
    :header="modalData.title"
    width="550"
    height="350"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="formDataRef" class="form-box" :model="formData" :rules="rules">
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <!-- 暂时只能选择1503 -->
          <mt-select
            v-model="formData.companyCode"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'companyCode' }"
            :show-clear-button="true"
            :placeholder="$t('请选择公司')"
            @change="
              ({ itemData }) => {
                formData.companyName = itemData ? itemData.companyName : null
              }
            "
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import mixin from '../config/mixin'

export default {
  mixins: [mixin],
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {},
      rules: {
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  computed: {},
  async created() {},
  mounted() {
    this.$refs.dialogRef?.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.formDataRef.validate(async (valid) => {
        if (valid) {
          const res = await this.$API.semiconductorRiskManagement.addSemiRisk(this.formData)
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function')
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
