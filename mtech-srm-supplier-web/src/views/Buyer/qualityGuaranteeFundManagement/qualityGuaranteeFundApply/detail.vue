<template>
  <div class="full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div v-show="!isExpand">
              <span>{{ dataForm.requestCode }}</span>
              <span class="sub-title">{{ dataForm.requestName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="requestCode" :label="$t('申请单编码')" label-style="top">
              <vxe-input v-model="dataForm.requestCode" disabled />
            </mt-form-item>
            <mt-form-item prop="requestName" :label="$t('申请单标题')" label-style="top">
              <vxe-input
                v-model="dataForm.requestName"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入申请单标题')"
              />
            </mt-form-item>
            <mt-form-item prop="type" :label="$t('申请单类型')" label-style="top">
              <vxe-select
                v-model="dataForm.type"
                :options="typeList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                :disabled="!!$route.query.id"
                :placeholder="$t('请选择申请单类型')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <vxe-input v-model="dataForm.createUserName" disabled />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建日期')" label-style="top">
              <vxe-input v-model="dataForm.createTime" disabled />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <vxe-select
                v-model="dataForm.status"
                :options="statusList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="topRemark" :label="$t('备注')" label-style="top">
              <vxe-textarea
                v-model="dataForm.topRemark"
                clearable
                :disabled="!editable"
                :rows="1"
                :placeholder="$t('请输入备注')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container" v-show="!!$route.query.id">
      <mt-tabs
        ref="mtTabsRef"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive>
        <component ref="mainContent" :is="activeComponent" :data-info="dataForm" />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea
} from 'vxe-table'
import mixin from './config/mixin'
import { statusList, typeList } from './config/index'

export default {
  name: 'ExpertRating',
  components: { VxeInput, VxeButton, VxeSelect, VxeTextarea },
  mixins: [mixin],
  data() {
    return {
      isExpand: true,
      dataForm: {},
      statusList,
      typeList,
      tabList: [{ title: this.$t('明细') }, { title: this.$t('附件') }],
      activeTabIndex: 0,
      detailList: []
    }
  },
  computed: {
    editable() {
      return this.$route.query.type === 'add' || [0, 2, 4].includes(this.dataForm.status)
    },
    detailToolbar() {
      return [
        { code: 'save', name: this.$t('保存'), status: '', isHidden: !this.editable },
        { code: 'submit', name: this.$t('提交'), status: '', isHidden: !this.editable },
        {
          code: 'viewOA',
          name: this.$t('查看OA审批进度'),
          status: '',
          isHidden: ![1, 3].includes(this.dataForm.status)
        },
        { code: 'back', name: this.$t('返回'), status: 'primary' }
      ]
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          comp = () => import('./components/detailTab.vue')
          break
        case 1:
          comp = () => import('./components/attachmentTab.vue')
          break
        default:
          return
      }
      return comp
    },
    formRules() {
      return {
        requestCode: [
          {
            required: this.$route.query.type === 'edit',
            message: this.$t('请输入申请单编码'),
            trigger: 'blur'
          }
        ],
        requestName: [{ required: true, message: this.$t('请输入申请单标题'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('请选择申请类型'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.getDetailById()
  },
  methods: {
    async getDetailById() {
      if (!this.$route.query.id) {
        return
      }
      const res = await this.$API.QualityGuarFundfManagement.queryApplyDetailById({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        this.dataForm = { ...res.data }
      }
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'submit':
          this.handleSubmit()
          break
        case 'viewOA':
          this.handleViewOA()
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      index === 1 && (this.detailList = this.$refs.mainContent?.dataList)
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 返回
    handleBack() {
      this.$router.replace({ name: 'quality-guarantee-fund-apply' })
    },
    // 保存
    async handleSave() {
      this.$refs.dataFormRef.validate(async (valid) => {
        if (valid) {
          this.$route.query.type === 'add' ? this.handleAdd() : this.handleUpdate()
        }
      })
    },
    // 提交
    async handleSubmit() {
      const res = await this.$API.QualityGuarFundfManagement.submitApplyDetail({
        id: this.dataForm.id
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t('提交成功'), type: 'success' })
        this.$router.replace({
          name: 'quality-guarantee-fund-apply-detail',
          query: {
            type: 'edit',
            id: this.dataForm.id,
            refreshId: Date.now()
          }
        })
      }
    },
    // 新增
    async handleAdd() {
      const res = await this.$API.QualityGuarFundfManagement.addApply(this.dataForm)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.$router.replace({
          name: 'quality-guarantee-fund-apply-detail',
          query: {
            type: 'edit',
            id: res.data.id,
            refreshId: Date.now()
          }
        })
      }
    },
    // 更新
    async handleUpdate() {
      const data = this.activeTabIndex === 0 ? this.$refs.mainContent?.dataList : this.detailList
      if ([1, 2].includes(this.dataForm.type)) {
        data?.forEach((item) => {
          const { dateFrom, dateTo } = item
          const arr = dateTo?.split('-')
          item.dateFrom = dateFrom ? new Date(dateFrom + '-01 00:00:00').valueOf() : null
          item.dateTo = dateTo ? new Date(arr[0], arr[1], 1).valueOf() - 1000 : null
        })
      }
      const res = await this.$API.QualityGuarFundfManagement.saveApplyDetail({
        ...this.dataForm,
        retentionId: this.dataForm.id,
        data
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.$router.replace({
          name: 'quality-guarantee-fund-apply-detail',
          query: {
            type: 'edit',
            id: this.dataForm.id,
            refreshId: Date.now()
          }
        })
      }
    },
    // 查看OA审批进度
    handleViewOA() {
      const { oaLink } = this.dataForm
      oaLink ? window.open(oaLink) : this.$toast({ content: this.$t('暂无申请单'), type: 'error' })
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
  padding: 8px;
  background: #fff;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;

    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }

    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select {
        width: 100%;
        height: 32px;
        line-height: 32px;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 10px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
