import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import cloneDeep from 'lodash/cloneDeep'

export default {
  components: { VxeRemoteSearch },
  data() {
    return {
      tableData: [],
      loading: false,
      supplierList: []
    }
  },
  computed: {
    editable() {
      return [0, 2, 4].includes(this.dataInfo.status)
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      return [
        {
          code: 'calculate',
          name: this.$t('计算区间'),
          status: 'info',
          isHidden: !this.editable || [0, 3, 4].includes(this.dataInfo.type)
        },
        { code: 'add', name: this.$t('新增'), status: 'info', isHidden: !this.editable },
        { code: 'delete', name: this.$t('删除'), status: 'info', isHidden: !this.editable },
        { code: 'import', name: this.$t('导入'), status: 'info', isHidden: !this.editable },
        { code: 'export', name: this.$t('导出'), status: 'info' }
      ]
    },
    columns() {
      const supplierColumns = [
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.supplierCode}
                  clearable
                  filterable={true}
                  options={this.supplierList}
                  option-props={{ label: 'text', value: 'supplierCode' }}
                  transfer
                  on-change={(e) => {
                    const selectItem = this.supplierList.find(
                      (item) => item.supplierCode === e.value
                    )
                    row.supplierName = selectItem?.supplierName || null
                    row.factWarrantAmount = selectItem?.factWarrantAmount || 0
                    // 调整后扣留金额 = 现扣留金额 + 确认调整金额
                    row.resetWarrantAmount = Number(row.factWarrantAmount) + Number(row.resetAmount)
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'supplierName',
          title: this.$t('供应商描述'),
          minWidth: 140
        }
      ]
      const amountColumns = [
        {
          field: 'supplierAmount',
          title: this.$t('供货金额（万元）'),
          minWidth: 160
        },
        {
          field: 'warrantAmount',
          title: this.$t('扣留金额计算（万元）'),
          minWidth: 160
        },
        {
          field: 'factWarrantAmount',
          title: this.$t('现扣留金额（万元）'),
          minWidth: 160
        },
        {
          field: 'resetAmount',
          title: this.$t('确认调整金额（万元）'),
          minWidth: 180,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.resetAmount}
                  type='number'
                  clearable
                  on-change={(e) => {
                    // 调整后扣留金额 = 现扣留金额 + 确认调整金额
                    row.resetWarrantAmount = Number(row.factWarrantAmount) + Number(e.value)
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'resetWarrantAmount',
          title: this.$t('调整后扣留金额（万元）'),
          minWidth: 180
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} clearable />]
            }
          }
        }
      ]
      switch (this.dataInfo.type) {
        case 0:
        case 4:
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            ...supplierColumns,
            ...amountColumns.splice(2, amountColumns.length)
          ]
        case 1:
        case 2:
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'date',
              title: this.$t('年度'),
              minWidth: 120,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.date}
                      type='year'
                      valueFormat='yyyy'
                      clearable
                      editable={false}
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'dateFrom',
              title: this.$t('年月从'),
              minWidth: 120,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.dateFrom}
                      type='month'
                      valueFormat='yyyy-MM'
                      clearable
                      editable={false}
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'dateTo',
              title: this.$t('年月至'),
              minWidth: 120,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.dateTo}
                      type='month'
                      valueFormat='yyyy-MM'
                      clearable
                      editable={false}
                      transfer
                    />
                  ]
                }
              }
            },
            ...supplierColumns,
            {
              field: 'version',
              title: this.$t('协议版本')
            },
            ...amountColumns,
            {
              field: 'reasonDesc',
              title: this.$t('计算失败原因'),
              minWidth: 180
            }
          ]
        case 3:
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            ...supplierColumns,
            ...amountColumns
          ]
        default:
          return []
      }
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    dataList() {
      const list = cloneDeep(this.tableData)
      const dataList = []
      list.forEach((item) => {
        !item.id
          ? dataList.push(item)
          : this.tableRef.isUpdateByRow(item) && (this.editObj[item.id] = item)
      })
      for (const id in this.editObj) {
        dataList.push(this.editObj[id])
      }
      return dataList
    }
  },
  watch: {
    dataInfo: {
      handler(val) {
        this.getSupplierList(val.type)
      }
    }
  },
  methods: {
    async getSupplierList(type) {
      if (!type && type !== 0) {
        return
      }
      const res = await this.$API.QualityGuarFundfManagement.querySupplierList({
        type,
        retentionId: this.dataInfo.id
      })
      if (res.code === 200) {
        res.data?.forEach((item) => (item.text = item.supplierCode + '-' + item.supplierName))
        this.supplierList = res.data
      }
    }
  }
}
