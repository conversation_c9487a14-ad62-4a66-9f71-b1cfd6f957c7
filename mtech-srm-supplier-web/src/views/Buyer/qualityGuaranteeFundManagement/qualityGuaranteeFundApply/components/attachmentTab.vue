<template>
  <div>
    <ScTable
      ref="sctableRef"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      :row-config="{ height: 45 }"
      :is-show-refresh-bth="true"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </ScTable>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import { download } from '@/utils/utils'
import { fileTypeIconSetting } from './config/attachment.js'

export default {
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [],
      loading: false
    }
  },
  computed: {
    editable() {
      return [0, 2, 4].includes(this.dataInfo.status)
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      return [
        { code: 'upload', name: this.$t('上传'), status: 'info', isHidden: !this.editable },
        { code: 'delete', name: this.$t('删除'), status: 'info', isHidden: !this.editable },
        { code: 'download', name: this.$t('下载'), status: 'info' }
      ]
    },
    columns() {
      const defaultColumns = [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'fileName',
          title: this.$t('文件名称'),
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a style='display: block' on-click={() => this.handleClickCellTitle(row, column)}>
                    {row.fileName}
                  </a>
                  <a class='cell-btn' on-click={() => this.handleClickCellTool('download', row)}>
                    <i class='vxe-icon-download' />
                    {this.$t('下载')}
                  </a>
                  <a
                    class='cell-btn'
                    v-show={this.editable}
                    on-click={() => this.handleDelete([row])}>
                    <i class='vxe-icon-delete' />
                    {this.$t('删除')}
                  </a>
                </div>
              ]
            }
          }
        },
        {
          field: 'fileSize',
          title: this.$t('文件大小'),
          slots: {
            default: ({ row }) => {
              return [<span>{Number(((row.fileSize / 1024) * 100) / 100).toFixed(2)}KB</span>]
            }
          }
        },
        {
          field: 'fileType',
          title: this.$t('文件类型'),
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <i class={fileTypeIconSetting[row.fileType]} />
                  <span>{row.fileType}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        }
      ]
      return defaultColumns
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    // 获取table数据
    async getTableData() {
      if (!this.$route.query.id) return
      const params = {
        docId: this.$route.query.id
      }
      this.loading = true
      const res = await this.$API.QualityGuarFundfManagement.queryApplyAttachmentList(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const _selectRows = this.tableRef.getCheckboxRecords()
      if (['download', 'delete'].includes(e.code) && !_selectRows.length) {
        this.$toast({ content: this.$t('请至少先选择一行数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'upload':
          this.handleUpload()
          break
        case 'download':
          _selectRows.forEach((row) => this.handleDownload(row))
          break
        case 'delete':
          this.handleDelete(_selectRows)
          break
        default:
          break
      }
    },
    // 单元格按钮点击
    handleClickCellTool(code, row) {
      switch (code) {
        case 'download':
          this.handleDownload(row)
          break
        case 'delete':
          this.handleDelete([row])
          break
        default:
          break
      }
    },
    // 单元格title文字点击
    handleClickCellTitle(row, column) {
      if (column.field === 'fileName') {
        let params = {
          id: row?.sysFileId,
          useType: 2
        }
        this.$API.fileService.filePreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    // 上传文件
    handleUpload() {
      this.$dialog({
        modal: () => import('@/components/Upload/index.vue'),
        data: {
          title: this.$t('上传')
        },
        success: async (data) => {
          const { id, fileName, fileSize, fileType, url } = data
          const params = {
            docId: this.$route.query.id,
            docType: 'retention',
            sysFileId: id,
            fileName,
            fileSize,
            fileType,
            url
          }
          const res = await this.$API.QualityGuarFundfManagement.saveApplyAttachment(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('上传成功'), type: 'success' })
            this.getTableData()
          }
        }
      })
    },
    //删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const ids = []
          list.forEach((row) => ids.push(row.id))
          const res = await this.$API.QualityGuarFundfManagement.deleteApplyAttachment({ ids })
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.getTableData()
          }
        }
      })
    },
    // 下载
    handleDownload(row) {
      this.$API.fileService.downloadPublicFile({ id: row.sysFileId }).then((res) => {
        download({ fileName: row.fileName, blob: new Blob([res.data]) })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.cell-btn {
  color: #409eff;
  font-size: 12px;
  font-weight: 400;
  margin-right: 10px;
}
</style>
