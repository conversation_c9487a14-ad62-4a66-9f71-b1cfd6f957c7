<template>
  <div>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      row-id="customId"
      grid-id="c3f33636-576b-4201-a126-eb5f765d00be"
      keep-source
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      :upload-params="uploadParams"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
// import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog.vue'
import detailMixin from './config/detailMixin'
import pagingMixin from '@/mixins/paging.js'
import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: { ScTable, UploadExcelDialog },
  mixins: [detailMixin, pagingMixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      downTemplateName: '',
      downTemplateParams: {},
      requestUrls: {},
      uploadParams: {},
      editObj: {}
    }
  },
  methods: {
    // 获取table数据
    async getTableData() {
      if (!this.$route.query.id) return
      const params = {
        page: this.pageInfo,
        id: this.$route.query.id
      }
      this.loading = true
      const res = await this.$API.QualityGuarFundfManagement.queryApplyDetailList(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        const dataList = res.data?.records || []
        if ([1, 2].includes(this.dataInfo.type)) {
          dataList.forEach((row) => {
            const { date, dateFrom, dateTo } = row
            row.date = date ? date + '' : null
            row.dateFrom = Number(dateFrom) ? dayjs(Number(dateFrom)).format('YYYY-MM') : null
            row.dateTo = Number(dateTo) ? dayjs(Number(dateTo)).format('YYYY-MM') : null
          })
        }
        this.tableData = dataList
        this.total = res.data?.total || 0
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete'].includes(e.code) && !selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'calculate':
          this.handleCalculate()
          break
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 新增
    async handleAdd() {
      // 记录编辑的行
      const list = this.tableRef.getUpdateRecords()
      list.forEach((item) => {
        item.id && (this.editObj[item.id] = item)
      })

      let newRowData = { optType: 'add' }
      // 设置默认值
      if ([1, 2].includes(this.dataInfo.type)) {
        const curYear = new Date().getFullYear()
        newRowData = {
          optType: 'add',
          date: curYear + '',
          dateFrom: curYear - 1 + '-09',
          dateTo: curYear + '-08'
        }
      }
      const { row: newRow } = await this.tableRef.insertAt(newRowData)
      this.tableData.unshift(newRow)
      this.tableRef.setEditRow(newRow)
    },
    // 删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const idList = []
          list.forEach(async (item) => {
            if (item.optType === 'add') {
              await this.tableRef.remove(item)
            } else {
              idList.push(item.id)
            }
          })
          if (idList.length === 0) {
            this.tableData = this.tableRef.getTableData().fullData
            return
          }
          const res = await this.$API.QualityGuarFundfManagement.deleteApplyDetail(idList)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.getTableData()
          }
        }
      })
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'QualityGuarFundfManagement',
        templateUrl: 'exportApplyDetail',
        uploadUrl: 'importApplyDetail'
      }
      const { id, requestCode, requestName, type, status, createUserName } = this.dataInfo
      this.uploadParams = { id, type }
      this.downTemplateParams = {
        id,
        requestCode,
        requestName,
        type,
        status,
        createUserName,
        isTemplate: 1,
        page: { current: 1, size: 100000 }
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const { id, requestCode, requestName, type, status, createUserName } = this.dataInfo
      const params = {
        id,
        requestCode,
        requestName,
        type,
        status,
        createUserName,
        page: { current: 1, size: 100000 }
      }
      const res = await this.$API.QualityGuarFundfManagement.exportApplyDetail(params)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 计算
    handleCalculate() {
      this.$dialog({
        modal: () => import('./dialog/calculateDialog.vue'),
        data: {
          title: this.$t('计算区间设置'),
          data: {
            id: this.$route.query.id
          }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    // 展示/隐藏上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = []
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    async upExcelConfirm() {
      this.$toast({ type: 'success', content: this.$t('导入成功') })
      this.showUploadExcel(false)
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .vxe-input {
    height: 32px;
    line-height: 32px;
  }
}
</style>
