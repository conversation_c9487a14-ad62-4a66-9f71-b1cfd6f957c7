<template>
  <vxe-modal
    ref="modalRef"
    :title="title"
    width="500"
    height="350"
    :loading="loading"
    show-footer
    resize
    transfer
  >
    <template #default>
      <div class="dialog-content">
        <mt-form class="form-box" ref="dataFormRef" :model="formData" :rules="formRules">
          <mt-form-item prop="date" :label="$t('计算年')" label-style="top">
            <vxe-input
              v-model="formData.date"
              type="year"
              value-format="yyyy"
              clearable
              :editable="false"
              transfer
              :placeholder="$t('请选择计算年')"
            />
          </mt-form-item>
          <mt-form-item prop="dateFrom" :label="$t('年月从')" label-style="top">
            <vxe-input
              v-model="formData.dateFrom"
              type="month"
              value-format="yyyy-MM"
              clearable
              :editable="false"
              transfer
              :placeholder="$t('请选择年月从')"
            />
          </mt-form-item>
          <mt-form-item prop="dateTo" :label="$t('年月至')" label-style="top">
            <vxe-input
              v-model="formData.dateTo"
              value-format="yyyy-MM"
              type="month"
              clearable
              :editable="false"
              transfer
              :placeholder="$t('请选择年月至')"
            />
          </mt-form-item>
        </mt-form>
      </div>
    </template>
    <template #footer>
      <vxe-button type="text" @click="cancel">{{ $t('取消') }}</vxe-button>
      <vxe-button type="text" @click="confirm">{{ $t('执行') }}</vxe-button>
    </template>
  </vxe-modal>
</template>

<script>
import { Modal as VxeModal } from 'vxe-table'

export default {
  components: { VxeModal },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('执行') }
        }
      ],
      formData: {},
      formRules: {
        date: [{ required: true, message: this.$t('请选择计算年'), trigger: 'blur' }],
        dateFrom: [{ required: true, message: this.$t('请选择年月从'), trigger: 'blur' }],
        dateTo: [{ required: true, message: this.$t('请选择年月至'), trigger: 'blur' }]
      },
      loading: false
    }
  },
  computed: {
    title() {
      return this.modalData.title
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.$refs.modalRef.open()
      // 设置默认值
      const curYear = new Date().getFullYear()
      this.formData = {
        date: curYear + '',
        dateFrom: curYear - 1 + '-09',
        dateTo: curYear + '-08'
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.dataFormRef.validate(async (valid) => {
        if (valid) {
          const { date, dateFrom, dateTo } = this.formData
          const arr = dateTo?.split('-')
          const params = {
            retentionId: this.modalData.data.id,
            date,
            dateFrom: dateFrom ? new Date(dateFrom + '-01 00:00:00').valueOf() : null,
            dateTo: dateTo ? new Date(arr[0], arr[1], 1).valueOf() - 1000 : null
          }

          // 设置loading的提示信息
          const eleList = document.getElementsByClassName('vxe-loading--text')
          const ele = eleList?.length ? eleList[eleList.length - 1] : null
          ele.innerText = this.$t('计算中') + '...'

          this.loading = true
          const res = await this.$API.QualityGuarFundfManagement.calculateRangeApplyDetail(
            params
          ).catch(() => (this.loading = false))
          this.loading = false
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function')
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .vxe-modal--box {
    border: none !important;
  }
  .vxe-modal--header {
    height: 60px;
    line-height: 60px;
    background-color: #31374e !important;
    color: #fff !important;
  }
  .vxe-modal--header-title,
  .vxe-modal--header-right {
    padding: 0 0.6em;
  }
  .vxe-input {
    width: 100%;
    height: 32px;
    line-height: 32px;
  }
}
</style>
