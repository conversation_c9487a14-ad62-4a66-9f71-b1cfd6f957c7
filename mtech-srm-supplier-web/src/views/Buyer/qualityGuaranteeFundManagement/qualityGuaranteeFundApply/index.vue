<template>
  <div>
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="requestCode" :label="$t('申请单编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.requestCode"
            :show-clear-button="true"
            :placeholder="$t('请输入申请单编码')"
          />
        </mt-form-item>
        <mt-form-item prop="requestName" :label="$t('申请单标题')" label-style="top">
          <mt-input
            v-model="searchFormModel.requestName"
            :show-clear-button="true"
            :placeholder="$t('请输入申请单标题')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :show-clear-button="true"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('类型')" label-style="top">
          <mt-select
            v-model="searchFormModel.type"
            css-class="rule-element"
            :data-source="typeList"
            :show-clear-button="true"
            :placeholder="$t('请选择类型')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="2e254bd6-5e4a-423d-9c97-32c7c67eaeec"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { statusList, typeList } from './config/index'

export default {
  components: { ScTable, CollapseSearch },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      statusList,
      typeList
    }
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.QualityGuarFundfManagement.queryApplyList(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.total = res.data?.total
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete'].includes(e.code) && !selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    // 点击单元格标题
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'requestCode':
          this.handleEdit(row)
          break
        case 'oaLink':
          window.open(row.oaLink)
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        name: 'quality-guarantee-fund-apply-detail',
        query: {
          type: 'add',
          refreshId: Date.now()
        }
      })
    },
    // 编辑
    handleEdit(row) {
      this.$router.push({
        name: 'quality-guarantee-fund-apply-detail',
        query: {
          type: 'edit',
          id: row.id,
          refreshId: Date.now()
        }
      })
    },
    // 删除
    handleDelete(list) {
      const idList = []
      for (let i = 0; i < list.length; i++) {
        if (![0, 2].includes(list[i].status) || list[i].detailStatus !== 0) {
          this.$toast({
            content: this.$t('仅可删除状态为【草稿、审批驳回】且计算状态为【计算完成】的单据'),
            type: 'success'
          })
          return
        }
        idList.push(list[i].id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const res = await this.$API.QualityGuarFundfManagement.deleteApply(idList)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
