import { i18n } from '@/main.js'

// 状态列表
export const statusList = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('审批中'), value: 1 },
  { text: i18n.t('审批驳回'), value: 2 },
  { text: i18n.t('已完成'), value: 3 },
  { text: i18n.t('审核废弃'), value: 4 }
]

// 计算状态列表
export const calculateStatusList = [
  { text: i18n.t('计算完成'), value: 0 },
  { text: i18n.t('计算中'), value: 1 },
  { text: i18n.t('计算失败'), value: 2 }
]

// 类型列表
export const typeList = [
  { text: i18n.t('新供方质保金扣留'), value: 0 },
  { text: i18n.t('合作供方年度保证金扣留'), value: 1 },
  { text: i18n.t('合作供方保证金扣留减免'), value: 2 },
  { text: i18n.t('退出供方质保金扣留'), value: 3 },
  { text: i18n.t('退出供方质保金返还'), value: 4 }
]
