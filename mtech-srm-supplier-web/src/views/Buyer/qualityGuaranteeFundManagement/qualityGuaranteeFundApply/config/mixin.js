import { statusList, calculateStatusList, typeList } from './index'

export default {
  data() {
    return {
      tableData: [],
      loading: false,
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'requestCode',
          title: this.$t('申请单编码'),
          slots: {
            default: ({ row, column }) => {
              return [
                <a style='color: #409eff;' on-click={() => this.handleClickCellTitle(row, column)}>
                  {row.requestCode}
                </a>
              ]
            }
          }
        },
        {
          field: 'requestName',
          title: this.$t('申请单标题')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              return [<div>{selectItem?.text || ''}</div>]
            }
          }
        },
        {
          field: 'detailStatus',
          title: this.$t('计算状态'),
          slots: {
            default: ({ row }) => {
              const selectItem = calculateStatusList.find((item) => item.value === row.detailStatus)
              return [<div>{selectItem?.text || ''}</div>]
            }
          }
        },
        {
          field: 'type',
          title: this.$t('类型'),
          slots: {
            default: ({ row }) => {
              const selectItem = typeList.find((item) => item.value === row.type)
              return [<div>{selectItem?.text || ''}</div>]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        },
        {
          field: 'oaLink',
          title: this.$t('OA申请单查看'),
          minWidth: 120,
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a
                    v-show={row.oaLink}
                    style='color: #409eff;'
                    on-click={() => this.handleClickCellTitle(row, column)}>
                    {this.$t('查看')}
                  </a>
                  <span v-show={!row.oaLink}>-</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'submitTime',
          title: this.$t('提交时间')
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
