<template>
  <div class="full-height">
    <mt-tabs
      ref="mtTabsRef"
      :e-tab="false"
      :data-source="tabList"
      :halt-select="false"
      @handleSelectTab="(index, item) => handleTabChange(index)"
      style="background-color: #fff"
    />
    <keep-alive>
      <component ref="mainContent" :is="activeComponent" />
    </keep-alive>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabList: [{ title: this.$t('供应商质保台账') }, { title: this.$t('扣留标准') }],
      activeTabIndex: 0
    }
  },
  computed: {
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          comp = () => import('./tabs/supplierQualityGuaranteeStandingBook.vue')
          break
        case 1:
          comp = () => import('./tabs/detainStandard.vue')
          break
        default:
          return
      }
      return comp
    }
  },
  methods: {
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
