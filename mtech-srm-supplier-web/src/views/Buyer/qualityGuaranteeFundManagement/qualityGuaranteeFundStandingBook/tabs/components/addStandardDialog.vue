<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- 表单 -->
      <mt-form class="form-box" ref="dataFormRef" :model="formData" :rules="formRules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="version" :label="$t('版本号')" label-style="top">
              <vxe-input v-model="formData.version" clearable :placeholder="$t('请输入版本号')" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="versionDescription" :label="$t('版本描述')" label-style="top">
              <vxe-input
                v-model="formData.versionDescription"
                clearable
                :placeholder="$t('请输入版本描述')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
      <!-- table表 -->
      <ScTable
        ref="sctableRef"
        row-id="customId"
        :columns="columns"
        :table-data="tableData"
        :edit-config="editConfig"
        :loading="loading"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            v-show="!item.isHidden"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :disabled="item.disabled"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </template>
      </ScTable>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'

export default {
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        status: 1
      },
      formRules: {
        versionCode: [{ required: true, message: this.$t('请输入版本号'), trigger: 'blur' }]
      },
      tableData: [],
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      loading: false
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      return [
        {
          code: 'add',
          name: this.$t('新增'),
          status: 'info'
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: 'info'
        }
      ]
    },
    columns() {
      const dedaultColumns = [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'lastSupplyAmountStart',
          title: this.$t('上年年度供货金额从（万元）'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.lastSupplyAmountStart}
                  clearable
                  type='number'
                  min='0'
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'lastSupplyAmountEnd',
          title: this.$t('上年年度供货金额至（万元）'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.lastSupplyAmountEnd}
                  clearable
                  type='number'
                  min='0'
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'guarantee',
          title: this.$t('质量风险保障金（万元）'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.guarantee} clearable type='number' min='0' transfer />
              ]
            }
          }
        }
      ]
      return dedaultColumns
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.$refs.dialog.ejsRef.show()
      if (this.modalData.type === 'update') {
        const { id, version, versionDescription, status } = this.modalData.data
        this.formData = {
          id,
          version,
          versionDescription,
          status
        }
        this.getTableData()
      }
    },
    async getTableData() {
      const params = { criterionId: this.formData.id }
      this.loading = false
      const res = await this.$API.QualityGuarFundfManagement.queryDetainStandardDetailList(
        params
      ).catch(() => (this.loading = false))
      if (res.code === 200) {
        this.loading = false
        this.tableData = res.data || []
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.dataFormRef.validate(async (valid) => {
        if (valid) {
          const params = {
            ...this.formData,
            data: this.tableData
          }
          const res = await this.$API.QualityGuarFundfManagement[
            this.modalData.type + 'DetainStandard'
          ](params)
          if (res.code === 200) {
            this.$emit('confirm-function')
          }
        }
      })
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
      }
    },
    // 新增
    async handleAdd() {
      const newRowData = {
        optType: 'add',
        criterionId: this.formData.id || null
      }
      const { row: newRow } = await this.tableRef.insertAt(newRowData)
      this.tableRef.setEditRow(newRow)
      this.tableData.unshift(newRow)
    },
    // 删除
    handleDelete() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const itemIdList = []
          selectedRecords.forEach(async (item) => {
            if (item.optType === 'add') {
              await this.tableRef.remove(item)
            } else {
              itemIdList.push(item.id)
            }
          })

          if (itemIdList.length === 0) {
            this.tableData = this.tableRef.getTableData().fullData
            return
          }
          const res = await this.$API.QualityGuarFundfManagement.deleteDetainStandardDetail(
            itemIdList
          )
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.getTableData()
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
  .form-box {
    padding-top: 10px;
  }
}
::v-deep {
  .vxe-input {
    width: 100%;
    height: 32px;
    line-height: 32px;
  }
}
</style>
