<template>
  <div>
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="supplierCodeList" :label="$t('供应商')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierCode', 'supplierName']"
            :placeholder="$t('请选择供应商')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierStatusList" :label="$t('供应商属性')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.supplierStatusList"
            css-class="rule-element"
            :data-source="supplierAttributeList"
            :show-clear-button="true"
            :fields="{ text: 'text', value: 'text' }"
            :placeholder="$t('请选择供应商属性')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCodeList" :label="$t('供应品类')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCodeList"
            url="/masterDataManagement/tenant/category/paged-query"
            multiple
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryCode', 'categoryName']"
            :placeholder="$t('请选择供应品类')"
          />
        </mt-form-item>
        <mt-form-item prop="version" :label="$t('协议签订版本')" label-style="top">
          <mt-input
            v-model="searchFormModel.version"
            :show-clear-button="true"
            :placeholder="$t('请输入协议签订版本')"
          />
        </mt-form-item>
        <mt-form-item prop="actualVersion" :label="$t('实际执行版本')" label-style="top">
          <mt-input
            v-model="searchFormModel.actualVersion"
            :show-clear-button="true"
            :placeholder="$t('请输入实际执行版本')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      row-id="customId"
      grid-id="dd7f1964-e25c-4812-917e-b5f535607598"
      keep-source
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      @refresh="handleRefresh"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog.vue'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { supplierAttributeList } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: { ScTable, CollapseSearch, RemoteAutocomplete, UploadExcelDialog },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      type: 'standingBook',
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      supplierAttributeList,
      downTemplateName: '',
      downTemplateParams: {},
      requestUrls: {},
      editObj: {}
    }
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.QualityGuarFundfManagement.queryDetainBookList(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        res.data.records?.forEach((item) => {
          const { categoryCode, supplierQuitDate } = item
          item.categoryCode = categoryCode ? categoryCode.split(',') : []
          item.supplierQuitDate =
            Number(supplierQuitDate) === 0
              ? ''
              : dayjs(Number(supplierQuitDate)).format('YYYY-MM-DD')
        })
        this.tableData = res.data?.records || []
        this.total = res.data?.total
      }
    },
    // 刷新
    async handleRefresh() {
      const flushDtoList = []
      this.tableData.forEach((item) => {
        const {
          id,
          supplierCode,
          supplierName,
          trueFrozenAmount,
          actualFrozenAmount,
          needFrozenAmount
        } = item
        flushDtoList.push({
          id,
          supplierCode,
          supplierName,
          trueFrozenAmount,
          actualFrozenAmount,
          needFrozenAmount
        })
      })
      this.loading = true
      const res = await this.$API.QualityGuarFundfManagement.refreshDetainBookList({
        flushDtoList
      }).catch(() => (this.loading = false))
      if (res.code === 200) {
        this.getTableData()
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete'].includes(e.code) && !selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        case 'calculate':
          this.handleCalculate()
          break
        default:
          break
      }
    },
    // 新增
    async handleAdd() {
      // 记录编辑的行
      const list = this.tableRef.getUpdateRecords()
      list.forEach((item) => {
        item.id && (this.editObj[item.id] = item)
      })

      const newRowData = { optType: 'add' }
      const { row: newRow } = await this.tableRef.insertAt(newRowData)
      this.tableData.unshift(newRow)
      this.tableRef.setEditRow(newRow)
    },
    // 保存
    async handleSave() {
      const dataList = [...this.dataList]
      dataList.forEach((item) => {
        const { categoryCode, supplierQuitDate } = item
        item.supplierQuitDate = Number(supplierQuitDate)
          ? supplierQuitDate
          : new Date(supplierQuitDate).valueOf()
        item.categoryCode = categoryCode?.join(',') || null
      })
      const res = await this.$API.QualityGuarFundfManagement.saveDetainBook(dataList)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.getTableData()
      }
    },
    // 删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const idList = []
          list.forEach(async (item) => {
            if (item.optType === 'add') {
              await this.tableRef.remove(item)
            } else {
              idList.push(item.id)
            }
          })
          if (idList.length === 0) {
            this.tableData = this.tableRef.getTableData().fullData
            return
          }
          const res = await this.$API.QualityGuarFundfManagement.deleteDetainBook(idList)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'QualityGuarFundfManagement',
        templateUrl: 'downloadDetainBookTemplate',
        uploadUrl: 'importDetainBook'
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const params = {
        ...this.searchFormModel,
        page: { current: 1, size: 100000 }
      }
      const res = await this.$API.QualityGuarFundfManagement.exportDetainBook(params)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 计算
    handleCalculate() {
      this.$dialog({
        modal: () => import('./components/calculateDialog.vue'),
        data: {
          title: this.$t('计算区间设置')
        },
        success: () => {
          this.getTableData()
        }
      })
    },
    // 展示/隐藏上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = []
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    async upExcelConfirm() {
      this.$toast({ type: 'success', content: this.$t('导入成功') })
      this.showUploadExcel(false)
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .vxe-input {
    height: 32px;
    line-height: 32px;
  }
}
</style>
