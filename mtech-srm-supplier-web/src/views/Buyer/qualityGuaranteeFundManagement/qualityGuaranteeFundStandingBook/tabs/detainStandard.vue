<template>
  <div>
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="version" :label="$t('版本号')" label-style="top">
          <mt-input
            v-model="searchFormModel.version"
            :show-clear-button="true"
            :placeholder="$t('请输入版本号')"
          />
        </mt-form-item>
        <mt-form-item prop="versionDescription" :label="$t('版本描述')" label-style="top">
          <mt-input
            v-model="searchFormModel.versionDescription"
            :show-clear-button="true"
            :placeholder="$t('请输入版本描述')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="dd7f1964-e25c-4812-917e-b5f535607598"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'

export default {
  components: { ScTable, CollapseSearch },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      type: 'standard'
    }
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.QualityGuarFundfManagement.queryDetainStandardList(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.total = res.data?.total
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (e.code) {
        case 'add':
          this.handleEditData('add')
          break
        case 'delete':
        case 'enable':
        case 'disable':
          if (!selectedRecords.length) {
            this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
            return
          }
          this.handleOperate(e.code, selectedRecords)
          break
        default:
          break
      }
    },
    // 点击单元格标题
    handleClickCellTitle(row, column) {
      if (column.field === 'version') {
        this.handleEditData('update', row)
      }
    },
    // 新增、编辑
    handleEditData(type, row) {
      this.$dialog({
        modal: () => import('./components/addStandardDialog.vue'),
        data: {
          title: this.$t('质保金扣留标准'),
          type,
          data: row
        },
        success: () => {
          this.getTableData()
        }
      })
    },

    // 删除、启用、禁用
    handleOperate(type, list) {
      const tipMap = {
        delete: this.$t('删除'),
        enable: this.$t('启用'),
        disable: this.$t('停用')
      }
      const idList = []
      for (let index = 0; index < list.length; index++) {
        const { status } = list[index]
        if (type === 'delete' && status === 1) {
          this.$toast({ content: this.$t(`仅可删除状态为【禁用】的数据`), type: 'warning' })
          return
        }
        if (type === 'enable' && status === 1) {
          this.$toast({ content: this.$t(`仅可启用状态为【禁用】的数据`), type: 'warning' })
          return
        }
        if (type === 'disable' && status === 0) {
          this.$toast({ content: this.$t(`仅可停用状态为【启用】的数据`), type: 'warning' })
          return
        }
        idList.push(list[index].id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`${this.$t('确认') + tipMap[type] + this.$t('选中的数据')}？`)
        },
        success: async () => {
          let res = {}
          if (type === 'delete') {
            res = await this.$API.QualityGuarFundfManagement.deleteDetainStandard(idList)
          } else {
            const params = {
              status: type === 'enable' ? 1 : 0,
              idList
            }
            res = await this.$API.QualityGuarFundfManagement.updateDetainStandardStatus(params)
          }
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.getTableData()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
