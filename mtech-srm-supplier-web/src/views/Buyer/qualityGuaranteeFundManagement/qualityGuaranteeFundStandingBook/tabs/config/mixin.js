import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { statusList, supplierAttributeList } from './index'
import cloneDeep from 'lodash/cloneDeep'

export default {
  components: { VxeRemoteSearch },
  data() {
    return {
      tableData: [],
      loading: false,
      versionList: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      switch (this.type) {
        case 'standard':
          return [
            { code: 'add', name: this.$t('新增'), status: 'info' },
            { code: 'delete', name: this.$t('删除'), status: 'info' },
            { code: 'enable', name: this.$t('启用'), status: 'info' },
            { code: 'disable', name: this.$t('停用'), status: 'info' }
          ]
        case 'standingBook':
          return [
            { code: 'add', name: this.$t('新增'), status: 'info' },
            { code: 'save', name: this.$t('保存'), status: 'info' },
            { code: 'delete', name: this.$t('删除'), status: 'info' },
            { code: 'import', name: this.$t('导入'), status: 'info' },
            { code: 'export', name: this.$t('导出'), status: 'info' },
            { code: 'calculate', name: this.$t('计算'), status: 'info' }
          ]
        default:
          return []
      }
    },
    columns() {
      switch (this.type) {
        case 'standard':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'version',
              title: this.$t('版本号'),
              slots: {
                default: ({ row, column }) => {
                  return [
                    <a
                      style='color: #409eff;'
                      on-click={() => this.handleClickCellTitle(row, column)}>
                      {row.version}
                    </a>
                  ]
                }
              }
            },
            {
              field: 'versionDescription',
              title: this.$t('版本描述')
            },
            {
              field: 'status',
              title: this.$t('状态'),
              slots: {
                default: ({ row }) => {
                  const selectItem = statusList.find((item) => item.value === row.status)
                  return [<div>{selectItem?.text || ''}</div>]
                }
              }
            },
            {
              field: 'createUserName',
              title: this.$t('创建人')
            },
            {
              field: 'createTime',
              title: this.$t('创建时间')
            },
            {
              field: 'updateUserName',
              title: this.$t('修改人')
            },
            {
              field: 'updateTime',
              title: this.$t('修改时间')
            }
          ]
        case 'standingBook':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码'),
              minWidth: 140,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <VxeRemoteSearch
                      v-model={row.supplierCode}
                      fileds={{ value: 'supplierCode', text: 'supplierName' }}
                      request-info={{
                        urlPre: 'masterData',
                        url: 'getSupplier',
                        searchKey: 'fuzzyNameOrCode',
                        recordsPosition: 'data'
                      }}
                      on-change={(item) => {
                        row.supplierName = item?.supplierName || null
                      }}
                    />
                  ]
                }
              }
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称'),
              minWidth: 140
            },
            {
              field: 'version',
              title: this.$t('协议签订版本'),
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-select
                      v-model={row.version}
                      clearable
                      options={this.versionList}
                      option-props={{ label: 'version', value: 'version' }}
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'actualVersion',
              title: this.$t('实际执行版本'),
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-select
                      v-model={row.actualVersion}
                      clearable
                      options={this.versionList}
                      option-props={{ label: 'version', value: 'version' }}
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'supplierStatus',
              title: this.$t('供应商属性'),
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-select
                      v-model={row.supplierStatus}
                      clearable
                      options={supplierAttributeList}
                      option-props={{ label: 'text', value: 'text' }}
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'categoryCode',
              title: this.$t('供应品类编码'),
              minWidth: 140,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <VxeRemoteSearch
                      v-model={row.categoryCode}
                      fileds={{ value: 'categoryCode', text: 'categoryName' }}
                      multiple={true}
                      request-info={{
                        urlPre: 'maintainOutsideItem',
                        url: 'criteriaQuery',
                        searchKey: 'fuzzyNameOrCode',
                        recordsPosition: 'data'
                      }}
                      on-change={(list) => {
                        const categoryNameList = []
                        list.forEach((item) => categoryNameList.push(item.categoryName))
                        row.categoryName = categoryNameList.join(',')
                      }}
                    />
                  ]
                }
              }
            },
            {
              field: 'categoryName',
              title: this.$t('供应品类名称'),
              minWidth: 140
            },
            {
              field: 'lastSupplierAmount',
              title: this.$t('上一年供货额（万元）'),
              minWidth: 160
            },
            {
              field: 'supplierAmount',
              title: this.$t('本年供货额（万元）'),
              minWidth: 160
            },
            {
              field: 'versionFrozenAmount',
              title: this.$t('协议应冻结金额（万元）'),
              minWidth: 170
            },
            {
              field: 'actualFrozenAmount',
              title: this.$t('实际应冻结金额（万元）'),
              minWidth: 170
            },
            {
              field: 'trueFrozenAmount',
              title: this.$t('已冻结金额（万元）'),
              minWidth: 160,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.trueFrozenAmount}
                      type='number'
                      min='0'
                      clearable
                      on-change={() => {
                        const needFreezeAmount =
                          Number(row.actualFreezeAmount) - Number(row.frozenAmount)
                        row.needFreezeAmount = needFreezeAmount >= 0 ? needFreezeAmount : '-'
                      }}
                    />
                  ]
                }
              }
            },
            {
              field: 'needFrozenAmount',
              title: this.$t('仍需冻结金额（万元）'),
              minWidth: 160
            },
            {
              field: 'supplierQuitDate',
              title: this.$t('供应商退出供货时间'),
              minWidth: 160,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.supplierQuitDate}
                      clearable
                      type='date'
                      editable={false}
                      transfer
                    />
                  ]
                }
              }
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 160,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [<vxe-input v-model={row.remark} clearable />]
                }
              }
            },
            {
              field: 'createUserName',
              title: this.$t('创建人')
            },
            {
              field: 'createTime',
              title: this.$t('创建时间')
            },
            {
              field: 'updateUserName',
              title: this.$t('修改人')
            },
            {
              field: 'updateTime',
              title: this.$t('修改时间')
            }
          ]
        default:
          return []
      }
    },
    dataList() {
      const list = cloneDeep(this.tableData)
      const dataList = []
      list.forEach((item) => {
        !item.id
          ? dataList.push(item)
          : this.tableRef.isUpdateByRow(item) && (this.editObj[item.id] = item)
      })
      for (const id in this.editObj) {
        dataList.push(this.editObj[id])
      }
      return dataList
    }
  },
  mounted() {
    this.type === 'standingBook' && this.getVersionList()
  },
  methods: {
    async getVersionList() {
      const res = await this.$API.QualityGuarFundfManagement.queryEnableVersionList()
      if (res.code === 200) {
        this.versionList = [
          ...res.data,
          { version: this.$t('未签订'), versionDescription: this.$t('未签订') }
        ]
      }
    }
  }
}
