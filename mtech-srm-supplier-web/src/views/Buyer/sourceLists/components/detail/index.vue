// 计划清单详情
<template>
  <div class="full-height">
    <div class="top-info">
      <div class="header">
        <!-- 左侧的信息 -->
        <div class="plan-title">{{ topInfo.planName || '' }}</div>
        <div :class="['status-box', 'status-box' + '-' + topInfo.status]">
          <span v-if="topInfo.status == 0">{{ $t('待提交') }}</span>
          <span v-if="topInfo.status == 1">{{ $t('停用') }}</span>
          <span v-if="topInfo.status == 2">{{ $t('启用') }}</span>
        </div>
        <div class="infos">{{ $t('审批完成') }}</div>

        <div class="middle-blank"></div>

        <!-- 右侧操作按钮 -->
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack(0)">{{
          $t('返回')
        }}</mt-button>
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack(1)">{{
          $t('保存')
        }}</mt-button>
        <!-- <mt-button css-class="e-flat" :is-primary="true" @click="goBack(2)"
          >{{ $t("保存并提交") }}</mt-button> -->
      </div>
      <div class="middle">
        <span>{{ $t('项目编码') }}：{{ topInfo.templateCode }}</span>
        <span>{{ $t('创建人') }}：{{ topInfo.createUserName }}{{ topInfo.createTime }}</span>
        <!-- <span>{{ $t("项目类型") }}：{{analysisList[topInfo.templateType]}}</span> -->
      </div>
      <div class="bottom">
        <span>{{ $t('期号') }}：{{ topInfo.issueNum }}</span>
        <span>{{ $t('提交截止日期') }}{{ topInfo.submitDeadline }}</span>
        <span>{{ $t('所属组织') }}：{{ topInfo.orgName }}{{ topInfo.orgCode }} </span>
      </div>
    </div>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @input="input"
    />
    <mt-dialog
      ref="dialog"
      :header="$t('批量打分')"
      css-class="bule-bg"
      :width="900"
      min-height="600"
      :buttons="buttons"
    >
      <div class="uploader-box">
        <mt-form ref="formInfo" :model="formInfo">
          <mt-form-item prop="score" :label="$t('批量设置分值')">
            <mt-input
              v-model="formInfo.score"
              type="number"
              :placeholder="$t('请输入分值')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="reason" :label="$t('原因')">
            <mt-input
              v-model="formInfo.reason"
              type="text"
              :placeholder="$t('请输入原因')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
        <!--  -->
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { pageConfig } from './config/columns'
export default {
  // props: {
  //   topInfo: {
  //     type: Object,
  //     default: () => {},
  //   },
  // },
  data() {
    return {
      arr: [],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.piliang,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formInfo: {
        score: 0,
        reason: ''
      },
      pageConfig: [],

      topInfo: {},
      id: ''
    }
  },
  created() {
    this.id = this.$route.query.id
    this.init()
  },
  mounted() {
    // if (this.modalData && this.modalData.data && this.modalData.data.id) {
    //   this.getFormDetail(this.modalData.data.id);
    // }
    // this.getDimensionSelectList();
  },
  methods: {
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    piliang() {
      this.topInfo.scoreDetailList.forEach((ele) => {
        if (this.arr.includes(ele.id)) {
          ele.reason = this.formInfo.reason
          ele.score = this.formInfo.score
        }
      })
      this.$refs['dialog'].ejsRef.hide()
    },
    input(index, key, value) {
      this.$set(this.topInfo.scoreDetailList[index], key, value)
    },
    handleClickToolBar(e) {
      //this.$t("勾选的每一列的数据")
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id == 'dafen') {
        if (_selectGridRecords.length <= 0) {
          this.$toast({ content: this.$t('请选中需批量打分得数据'), type: 'warning' })
          return
        } else {
          _selectGridRecords.forEach((ele) => {
            if (ele.indexType != '2') {
              this.$toast({ content: this.$t('只可选中指标'), type: 'warning' })
              return
            }
          })
          this.arr = _selectGridRecords.map((item) => {
            return item.id
          })
          this.$refs['dialog'].ejsRef.show()
        }
      }
    },
    arrFn(arr = []) {
      arr.forEach((ele) => {
        if (ele.indexLevel == '1') {
          ele['name'] = ele.dimensionName
        } else {
          ele['name'] = ele.indexName
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.arrFn(ele.childrenList)
        }
      })
    },
    init() {
      this.$API.DataPreparation.manualDetail({ archiveId: this.id }).then((res) => {
        this.topInfo = null
        let arr = { ...res.data }
        if (!arr.scoreDetailList) arr.scoreDetailList = []
        // if(arr.scoreDetailList.length>0){
        //   this.arrFn(arr.scoreDetailList)
        // }
        this.topInfo = JSON.parse(JSON.stringify(arr))
        this.topInfo.scoreDetailList = this.topInfo.scoreDetailList.map((item, i) => {
          return { ...item, i: i, keepDecimal: this.topInfo.keepDecimal }
        })
        this.pageConfig = pageConfig(this.topInfo.scoreDetailList)
        // this.topInfo=arr
        // this.$refs.template1.init(this.topInfo.itemDTOList)

        // this.currentTabIndex=0
      })
    },
    // 返回按钮
    goBack(num) {
      if (num == 1 || num == 2) {
        this.$loading()
        let list = []
        this.topInfo.scoreDetailList.forEach((item) => {
          if (item.indexType == '2' && item.score) {
            let obj = {
              archiveId: item.archiveId,
              categoryCode: item.categoryCode,
              categoryId: item.categoryId,
              manualId: item.manualId,
              parentTemplateItemId: item.parentTemplateItemId,
              raterId: item.raterId,
              reason: item.reason,
              score: item.score,
              scoreDetailId: item.scoreDetailId,
              supplierEnterpriseId: item.supplierEnterpriseId,
              templateItemId: item.templateItemId
            }
            list.push(obj)
          }
        })
        this.$API.DataPreparation.manualDetailsave({
          indexScoreDTOList: list
        }).then((res) => {
          this.$hloading()
          if (res && res.code === 200) {
            this.init()
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          } else {
            this.$toast({
              content: res.msg || this.$t('保存失败，请重试'),
              type: 'error'
            })
          }
        })
      } else {
        this.back()
      }
    },
    digui(arr, data) {
      data.forEach((ele) => {
        if (ele.childrens && ele.childrens.length > 0) {
          this.digui(arr, ele.childrens)
        } else {
          let obj = {
            categoryCode: ele.categoryCode,
            categoryId: ele.id,
            categoryName: ele.categoryName,
            // "id": 0,
            rangeType: this.$refs.template2.rangeType,
            // "remark": "",
            templateId: this.topInfo.id
            // "tenantId": 0
          }
          arr.push(obj)
        }
      })
    },
    back() {
      // this.$router.go(-1)
      this.$router.push({
        path: '/supplier/dataPrepare',
        query: {
          tab: 1
        }
      })
    },
    // 切换Tab页
    handleSelectTab(e) {
      this.currentTabIndex = e
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: calc(100% - 29px);
  display: flex;
  flex-direction: column;
  padding: 0 20px;
}
.mt-tabs {
  width: 100%;
  background-color: #fafafa;
}
.plan-title {
  font-size: 20px;
  font-family: PingFangSC;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
}
.middle-blank {
  flex: 1;
}
.top-info {
  width: 100%;
  height: 132px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  display: flex;
  flex-direction: column;
  padding: 26px 0 28px 30px;
  margin: 20px 0 25px 0;
  .status-box {
    line-height: 12px;
    padding: 4px;
    border-radius: 2px;
    margin: 0 28px 0 16px;
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    &-0 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
    &-1 {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
    &-2 {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
  }
  .header {
    width: 100%;
    height: 20px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .middle {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin: 10px 0 20px 0;
    span {
      margin-right: 20px;
    }
  }
  .bottom {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    span {
      margin-right: 24px;
    }
  }
}
</style>
