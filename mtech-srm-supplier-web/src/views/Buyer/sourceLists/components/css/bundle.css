.mt-commom-tree-view .expandedLevel1 {
  background: #fafafa;
  position: relative; }

.mt-commom-tree-view .expandedLevel1 .e-icons::before {
  color: #6386c1 !important; }

.mt-commom-tree-view .mt-tree-view {
  width: 100%; }

.mt-commom-tree-view .mt-tree-view .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  font-weight: 500 !important; }

.mt-commom-tree-view .mt-tree-view #treeview > .e-ul {
  padding: 0; }

.mt-commom-tree-view .mt-tree-view .e-treeview {
  overflow: visible; }

.mt-commom-tree-view .mt-tree-view .e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #f5f6f9;
  border-color: #f5f6f9; }

.mt-commom-tree-view .mt-tree-view .e-treeview .e-ul {
  overflow: visible; }

.mt-commom-tree-view .mt-tree-view .e-treeview .e-icon-collapsible::before {
  color: #6386c1; }

.mt-commom-tree-view .e-icon-wrapper {
  display: flex; }

.mt-commom-tree-view .e-icon-wrapper .e-list-text {
  flex: 1;
  display: flex; }

.mt-commom-tree-view .action-boxs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%; }

.mt-commom-tree-view .action-boxs .name-box {
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  font-family: PingFangSC; }

.mt-commom-tree-view .action-boxs .btn-box {
  width: 30px;
  display: flex;
  position: relative; }

.mt-commom-tree-view .action-boxs .btn-box .more {
  width: 100px;
  position: absolute;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
  top: 36px;
  z-index: 1;
  padding: 5px 0;
  left: -42px; }

.mt-commom-tree-view .action-boxs .btn-box .more .operation {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #292929; }

.mt-commom-tree-view .action-boxs .btn-box .more .operation:hover {
  background: #f5f6f9;
  cursor: pointer; }

.mt-commom-tree-view .action-boxs .btn-box .more::before {
  content: "";
  position: absolute;
  top: -10px;
  left: 40px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #fff;
  box-shadow: 30px 10px 30px 0 rgba(0, 0, 0, 0.1); }

.mt-commom-tree-view .action-boxs .btn-box .minus {
  width: 16px;
  height: 16px;
  line-height: 16px;
  font-size: 16px; }

.mt-commom-tree-view .action-boxs .btn-box .minus i {
  width: 16px;
  height: 16px;
  line-height: 16px;
  font-size: 16px;
  color: #6386c1; }

.mt-commom-tree-view .e-list-text {
  color: #292929;
  flex: 1; }

.mt-commom-tree-view .e-treeview.e-interaction.e-fullrow-wrap .e-text-content {
  display: flex;
  align-items: center; }
