import Vue from 'vue'
import { i18n } from '@/main.js'
// 供应商绩效管理列表
export const columnList = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '150',
    cellTools: []
  },
  {
    field: 'supplierEnterpriseName',
    width: '150',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryName',
    width: '100',
    headerText: i18n.t('品类')
  },
  {
    field: 'compositeScore',
    width: '100',
    headerText: i18n.t('得分'),
    template: () => {
      return {
        template: Vue.component('score', {
          template: `<div style="text-align:left">{{compositeScore}}
          <p style="cursor:pointer;color:rgba(99,134,193,1)" @click='scoringDetailsClicked()'>{{i18nDetail}}</p></div>`,
          data: function () {},
          methods: {
            scoringDetailsClicked() {
              this.$parent.$emit('handleClickCellTitle', {
                field: 'scoringDetails',
                data: this.data
              })
            }
          },
          computed: {
            compositeScore() {
              return this.data.compositeScore
            },
            i18nDetail() {
              return i18n.t('得分明细')
            }
          }
        })
      }
    }
  },
  {
    field: 'scoreRank',
    width: '100',
    headerText: i18n.t('品类排名')
  },
  {
    field: 'comprehensiveLight',
    width: '100',
    headerText: i18n.t('综合灯'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { key: '0', value: i18n.t('红灯') },
        { key: '1', value: i18n.t('黄灯') },
        { key: '2', value: i18n.t('绿灯') }
      ],
      fields: { text: 'value', value: 'key' }
    },
    template: () => {
      return {
        template: Vue.component('comprehensiveLight', {
          template: `
          <div>
          <div style="width:14px;height:14px;border-radius:50%;" :class="{green:data.comprehensiveLight=='2',red:data.comprehensiveLight=='0',yellow:data.comprehensiveLight=='1'}"></div>
          </div>`,
          data: function () {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'qualityLight',
    width: '100',
    headerText: i18n.t('质量灯'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { key: '3', value: i18n.t('红灯') },
        { key: '4', value: i18n.t('黄灯') },
        { key: '5', value: i18n.t('绿灯') }
      ],
      fields: { text: 'value', value: 'key' }
    },
    template: () => {
      return {
        template: Vue.component('quality', {
          template: `
          <div>
            <div style="width:14px;height:14px;border-radius:50%;" :class="{green:data.qualityLight=='5',yellow:data.qualityLight=='4',red:data.qualityLight=='3'}"></div>
          </div>`,
          data: function () {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'isNotice',
    width: '150',
    headerText: i18n.t('是否观察期'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { key: '6', value: i18n.t('是') },
        { key: '7', value: i18n.t('否') }
      ],
      fields: { text: 'value', value: 'key' }
    },
    template: () => {
      return {
        template: Vue.component('isNotice', {
          template: `<div>{{data.isNotice==6? i18nYes: i18nNo}}</div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            i18nYes() {
              return i18n.t('是')
            },
            i18nNo() {
              return i18n.t('否')
            }
          }
        })
      }
    }
  },
  {
    field: 'redLightCount',
    width: '200',
    headerText: i18n.t('三个月内红灯次数')
  },
  // 处理建议: 8推荐评审 9推荐整改 10推荐淘汰 11推荐拉黑 12推荐停用 13推荐供货比例降低 14处置单 15警告书
  {
    field: 'handlingSuggestions',
    width: '150',
    headerText: i18n.t('处理建议'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { key: '8', value: i18n.t('推荐评审') },
        { key: '9', value: i18n.t('推荐整改') },
        { key: '10', value: i18n.t('推荐淘汰') },
        { key: '11', value: i18n.t('推荐拉黑') },
        { key: '12', value: i18n.t('推荐停用') },
        { key: '13', value: i18n.t('推荐供货比例降低') },
        { key: '14', value: i18n.t('处置单') },
        { key: '15', value: i18n.t('警告书') }
      ],
      fields: { text: 'value', value: 'key' }
    },
    template: () => {
      return {
        template: Vue.component('handlingSuggestions', {
          template: `<div>{{handlingSuggestions}}</div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            handlingSuggestions() {
              let handlingSuggestions = this.data.handlingSuggestionsView || '-'
              return handlingSuggestions
            }
          }
        })
      }
    }
  },
  {
    field: 'publishStatus',
    width: '130',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: { 10: i18n.t('未发布'), 20: i18n.t('已发布') }
    }
  },
  {
    field: 'publishTime',
    width: '150',
    headerText: i18n.t('发布时间'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    valueConverter: {
      type: 'function', //filter可不传，如果未传，则原数据返回
      filter: (data) => {
        if (data == null) {
          return ''
        } else {
          let date = new Date(+data) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
          let year = date.getFullYear(),
            month = ('0' + (date.getMonth() + 1)).slice(-2),
            sdate = ('0' + date.getDate()).slice(-2),
            hour = ('0' + date.getHours()).slice(-2),
            minute = ('0' + date.getMinutes()).slice(-2),
            second = ('0' + date.getSeconds()).slice(-2)
          // 拼接
          let result = year + '-' + month + '-' + sdate + '\n' + hour + ':' + minute + ':' + second
          // 返回
          return result
        }
      }
    }
  },
  {
    field: 'templateName',
    width: '130',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'issueNum',
    width: '100',
    headerText: i18n.t('期号')
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('结果生成日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                <p>{{ timeStr[0] }}</p>
                <p>{{ timeStr[1] }}</p>
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = this.data.createTime.replace(/T/gi, ' ')
              timeStr = timeStr.replace(/\.000\+0000/gi, '')
              let arr = timeStr.split(' ')
              return arr
            }
          }
        })
      }
    }
  }
]

// 日志列表
export const logColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '100',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cellTools: []
  },
  {
    width: '100',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '100',
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    width: '100',
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '100',
    field: 'disposalResult',
    headerText: i18n.t('系统处置结果'),
    //处置结果: （disposalResult） (0综合红灯 1综合黄灯 2综合绿灯 3质量红灯 4质量黄灯 5质量绿灯 6移入观察期 7移出观察期 8推荐评审 9推荐整改 10推荐淘汰 11推荐拉黑 12推荐停用 13推荐供货比例降低 14处置单 15警告书）
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { key: '0', value: i18n.t('综合红灯') },
        { key: '1', value: i18n.t('综合黄灯') },
        { key: '2', value: i18n.t('综合绿灯') },
        { key: '3', value: i18n.t('质量红灯') },
        { key: '4', value: i18n.t('质量黄灯') },
        { key: '5', value: i18n.t('质量绿灯') },
        { key: '6', value: i18n.t('移入观察期') },
        { key: '7', value: i18n.t('移出观察期') },
        { key: '8', value: i18n.t('推荐评审') },
        { key: '9', value: i18n.t('推荐整改') },
        { key: '10', value: i18n.t('推荐淘汰') },
        { key: '11', value: i18n.t('推荐拉黑') },
        { key: '12', value: i18n.t('推荐停用') },
        { key: '13', value: i18n.t('推荐供货比例降低') },
        { key: '14', value: i18n.t('处置单') },
        { key: '15', value: i18n.t('警告书') }
      ],
      fields: { text: 'value', value: 'key' }
    },
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ data.disposalResultView }}
              </div>`
        })
      }
    }
  },
  {
    width: '100',
    field: 'strategyName',
    headerText: i18n.t('触发策略名称')
  },
  {
    width: '100',
    field: 'strategyRuleDescribe',
    headerText: i18n.t('系统规则描述')
  },
  {
    width: '100',
    field: 'createDate',
    headerText: i18n.t('系统创建时间'),
    searchOptions: {
      type: 'date',
      dataFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('createTime', {
          template: `
              <div class="time-box">
                <p>{{ timeStr[0] }}</p>
                <p>{{ timeStr[1] }}</p>

              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = this.data.createTime.replace(/T/gi, ' ')
              timeStr = timeStr.replace(/\.000\+0000/gi, '')
              let arr = timeStr.split(' ')
              return arr
            }
          }
        })
      }
    }
  }
]

// 得分详情
export const detailColumn = [
  {
    width: '100',
    field: 'indexName',
    headerText: i18n.t('维度/指标'),
    cssClass: (data) => {
      if (data.parentTemplateItemId == 0) {
        return 'tree-title-bold'
      }
      return ''
    }
  },
  {
    width: '100',
    field: 'score',
    headerText: i18n.t('指标得分'),
    template: () => {
      return {
        template: Vue.component('score', {
          template: `<span><span style="font-weight:bold;" v-if="parentTemplateItemId==0">{{score}}</span><span style="font-weight:normal;" v-else>{{score}}</span></span>`,
          data: function () {},
          computed: {
            parentTemplateItemId() {
              return this.data.parentTemplateItemId
            },
            score() {
              return this.data.score
            }
          }
        })
      }
    }
  },
  {
    width: '70',
    field: 'fullScore',
    headerText: i18n.t('满分'),
    template: () => {
      return {
        template: Vue.component('fullScore', {
          template: `<span><span style="font-weight:bold;" v-if="parentTemplateItemId==0">{{fullScore}}</span><span style="font-weight:normal;" v-else>{{fullScore}}</span></span>`,
          data: function () {},
          computed: {
            parentTemplateItemId() {
              return this.data.parentTemplateItemId
            },
            fullScore() {
              return this.data.fullScore
            }
          }
        })
      }
    }
  },
  {
    width: '70',
    field: 'scoreRank',
    headerText: i18n.t('排名'),
    template: () => {
      return {
        template: Vue.component('scoreRank', {
          template: `<span><span style="font-weight:bold;" v-if="parentTemplateItemId==0">{{scoreRank}}</span><span style="font-weight:normal;" v-else>{{scoreRank}}</span></span>`,
          data: function () {},
          computed: {
            parentTemplateItemId() {
              return this.data.parentTemplateItemId
            },
            scoreRank() {
              return this.data.scoreRank
            }
          }
        })
      }
    }
  }
]
