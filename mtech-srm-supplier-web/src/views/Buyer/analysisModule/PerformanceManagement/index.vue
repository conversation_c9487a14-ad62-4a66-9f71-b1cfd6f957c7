<template>
  <div class="perform-box" :style="setStyle ? 'width:1633px' : 'width:100%'">
    <mt-template-page
      ref="templateRef"
      :template-config="tabConfig"
      @handleSelectTab="handleSelectTab"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    >
      <!-- 看板内容 -->
      <div class="board" slot="slot-1" ref="scrollbar">
        <div class="list" id="redList">
          <div class="listTop">
            <div>
              <span>●</span>
              {{ $t('红灯清单') }}
            </div>
            <div>{{ redList.tool }}</div>
          </div>
          <div class="listCon">
            <div class="listDetails" v-for="(i, j) in redList.data" :key="j">
              <div>{{ i.supplierEnterpriseName }}</div>
              <div class="detailCon">
                <div>{{ i.categoryName }}</div>
                <div style="color: 9a9a9a">{{ i.createTime }}</div>
              </div>
              <div class="detailTip">{{ $t('触发规则描述') }}：{{ i.strategyRuleDescribe }}</div>
            </div>
          </div>
        </div>
        <div class="list" id="yellowList">
          <div class="listTop">
            <div><span class="yellowcolor">●</span>{{ $t('黄灯清单') }}</div>
            <div>{{ yellowList.tool }}</div>
          </div>
          <div class="listCon">
            <div class="listDetails" v-for="(i, j) in yellowList.data" :key="j">
              <div>{{ i.supplierEnterpriseName }}</div>
              <div class="detailCon">
                <div>{{ i.categoryName }}</div>
                <div style="color: #9a9a9a">{{ i.createTime }}</div>
              </div>
              <div class="detailTip yellowcolor">
                {{ $t('触发规则描述') }}：{{ i.strategyRuleDescribe }}
              </div>
            </div>
          </div>
        </div>
        <div class="list" id="seeList">
          <div class="listTop">
            <div>
              <span class>●</span>
              {{ $t('观察期') }}
            </div>
            <div>{{ seeList.tool }}</div>
          </div>
          <div class="listCon">
            <div class="listDetails" v-for="(i, j) in seeList.data" :key="j">
              <div>{{ i.supplierEnterpriseName }}</div>
              <div class="detailCon">
                <div>{{ i.categoryName }}</div>
                <div style="color: #9a9a9a">{{ i.createTime }}</div>
              </div>
              <div class="detailTip bluecolor">
                {{ $t('触发规则描述') }}：{{ i.strategyRuleDescribe }}
              </div>
            </div>
          </div>
        </div>
        <div class="list" id="srList">
          <div class="listTop">
            <div>
              <span class>●</span>
              {{ $t('系统推荐') }}
            </div>
            <div>{{ srList.tool }}</div>
          </div>
          <div class="listCon">
            <div class="listDetails" v-for="(i, j) in srList.data" :key="j">
              <div class="detailtitle">
                <div class="">{{ i.supplierEnterpriseName }}</div>
                <div class="yellowcolor">{{ i.recommendName }}</div>
              </div>
              <div class="detailCon">
                <div>{{ i.categoryName }}</div>
                <div style="color: #9a9a9a">{{ i.createTime }}</div>
              </div>
              <div class="detailTip bluecolor">
                {{ $t('触发规则描述') }}：{{ i.strategyRuleDescribe }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </mt-template-page>
    <!-- 列表得分明细弹窗 -->
    <div
      style="position: absolute; top: 0; left: 0; right: 0; bottom: 0"
      @click="isShow = false"
      v-if="isShow"
    >
      <div @click.stop class="scoreDetail" id="scoreId">
        <div>
          <span>{{ scoreDetail.data.supplierEnterpriseName }}</span>
          <span style="margin-left: 20px">{{ scoreDetail.data.categoryName }}</span>
        </div>
        <div class="perEcharts">
          <div ref="myRadar"></div>
          <div ref="myLine"></div>
        </div>
        <div class="score-detail-title">
          <div>{{ $t('得分明细') }}</div>
          <div class="section-active">
            <div v-show="scoreDetail.isOpen" @click="toggleScoreDetailTreeTable(true)">
              {{ $t('收起') }}<span class="active-icons"><mt-icon name="MT_DownArrow" /></span>
            </div>
            <div v-show="!scoreDetail.isOpen" @click="toggleScoreDetailTreeTable(false)">
              {{ $t('展开') }}<span class="expand-icons"><mt-icon name="MT_DownArrow" /></span>
            </div>
          </div>
        </div>
        <div style="margin-top: 15px">
          <mt-template-page ref="detailtable" :template-config="detailConfig"></mt-template-page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { columnList, logColumn, detailColumn } from './config/columns'
import utils from '@/utils/utils'
import * as echarts from 'echarts'

export default {
  data() {
    return {
      redList: {
        page: { current: 1, size: 10 },
        tool: 0,
        data: []
      }, //1
      yellowList: {
        page: { current: 1, size: 10 },
        tool: 0,
        data: []
      }, //2
      seeList: {
        page: { current: 1, size: 10 },
        tool: 0,
        data: []
      }, //3
      srList: {
        tool: 0,
        page: { current: 1, size: 10 },
        data: []
      }, //4
      isShow: false,
      setStyle: false,
      scoreDetail: {
        data: null, // 点击行数据
        isOpen: true // 标记treeTable是否展开，默认是展开的
      },
      timeLoadMore: false,
      tabConfig: [
        {
          gridId: '41910e5e-d3b0-45a8-b6e1-5046dec20233',
          title: this.$t('列表'),
          useToolTemplate: false,
          toolbar: [
            {
              id: 'release',
              icon: 'icon_solid_submit ',
              title: this.$t('发布')
            }
            // {
            //   id: "Print",
            //   icon: "icon_solid_edit ",
            //   title: this.$t("打印"),
            // },
          ],
          grid: {
            asyncConfig: {
              url: '/analysis/tenant/buyer/assess/archive/result/pageQuery'
            },
            allowSorting: false,
            columnData: columnList
          }
        },
        {
          gridId: 'd265471a-ffe1-4024-b420-ecb357ea6bf2',
          title: this.$t('看板')
        },
        {
          gridId: 'f4ad561e-e5cf-471e-b9d9-bc9c28e154ef',
          title: this.$t('日志'),
          useToolTemplate: false,
          toolbar: [],
          grid: {
            columnData: logColumn,
            asyncConfig: {
              url: '/analysis/tenant/buyer/assess/archivelog/pageQuery'
            }
          }
        }
      ],
      detailConfig: [
        {
          gridId: 'e0a7cebe-4af8-419a-aa48-dc407041b839',
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
          },
          treeGrid: {
            allowPaging: false, //关闭/隐藏表格底部，翻页操作
            childMapping: 'subtasks', //配置属性数据的子节点标记为'children'，默认配置为'subtasks'
            columnData: detailColumn,
            asyncConfig: {
              url: '/analysis/tenant/buyer/assess/archive/result/detail/indexScoreDetailList',
              methods: 'get',
              params: {
                resultId: null
              },
              recordsPosition: 'data', // 默认值为"data.records",可以根据实际情况传值，如果数据位置在response.data,则传值'data'
              serializeList: (list) => {
                // 一级根据“scoreRank”排名排序
                list.forEach((e) => {
                  e.id = e.templateItemId //这里面默认子树的标识为id！！！
                })
                return list
              }, // 用于数据序列化，API返回数据，二次处理。
              transform: true, // 设置为true后，执行Array向Tree结构的转化
              parentId: 'parentTemplateItemId', // 默认值为 "parentId",可根据实际情况传值，如parentId:'pid'、parentId:'categoryId'
              rootTag: '0' // 这个参数如果传值，代表第一层级数据的标记，根据实际情况传值,如果parentId='-1'代表第一层级数据，则rootTag='-1'
            }
          }
        }
      ]
    }
  },
  created() {
    //   获取弹窗的id，判断点击弹窗以外的部分则关闭弹窗
    // document.addEventListener("click", (e) => {
    //   console.log(e)
    //   var scoreId = document.getElementById("scoreId");
    //   if (scoreId.contains(e.target)) {
    //   } else {
    //     this.isShow = false;
    //   }
    // });
    // this.init()
  },
  mounted() {
    // 表格切换会触发2次，加一个防抖节流    掉一次接口
    this.init = utils.debounce(this.init, 300)
    window.addEventListener('scroll', this.handleScroll, true)
  },
  methods: {
    init() {
      let arr = [
        {
          str: 'redList',
          num: 1
        },
        {
          str: 'yellowList',
          num: 2
        },
        {
          str: 'seeList',
          num: 3
        },
        {
          str: 'srList',
          num: 4
        }
      ]
      arr.forEach((ele) => {
        this[ele.str].data = []
        this[ele.str].page = {
          current: 1,
          size: 10
        }
        this.jiekou(ele.str, ele.num)
      })
    },
    jiekou(str, num) {
      this.$API.performanceManagement
        .pageQuery({ page: this[str].page, inventoryType: num })
        .then((res) => {
          this[str].data = this[str].data.concat(res.data.records)
          ;(this[str].tool = res.data.total), console.log(this[str])
        })
    },
    handleScroll(e) {
      // 如果x轴滑动，e.bubbles 为true，y轴滑动为false
      if (e.bubbles) {
        return
      }
      if (this.timeLoadMore) {
        clearTimeout(this.timeLoadMore)
      }
      this.timeLoadMore = setTimeout(() => {
        let sh = e.target ? e.target.scrollHeight : 0
        let st = e.target ? e.target.scrollTop : 0
        let ch = e.target ? e.target.clientHeight : 0
        st = parseInt(st.toFixed(0))
        // 通过id确定四个区域哪个在滑动
        console.log(e.target.parentElement.id)
        let str = e.target.parentElement.id
        if (st + ch >= sh) {
          console.log('到底了：查询下一页')
          if (this[str].tool > this[str].data.length) {
            this[str].page.current++
            let obj = {
              redList: 1,
              yellowList: 2,
              seeList: 3,
              srList: 4
            }
            this.jiekou(str, obj[str])
          }
          // if(this.totalPages>0 && this.pageNum < this.totalPages){
          //   this.pageParams.page.current ++;
          //   this.getListData();
          // }
        }
      }, 300)
    },
    // tab框点击,返回当前索引
    handleSelectTab(e) {
      console.log(e)
      if (e == 1) {
        this.setStyle = true
        this.init()
      } else {
        this.setStyle = false
      }
    },
    handleClickToolBar(e) {
      const _this = this
      let { tabIndex, toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (tabIndex == 0) {
        if (toolbar.id == 'release') {
          let resultIds = []
          sltList.map((item) => {
            if (item.publishStatus == 10) {
              resultIds.push(item.id)
            }
          })
          if (resultIds.length == 0) {
            this.$toast({ content: this.$t('所选全为已发布数据，无需再次发布！'), type: 'warning' })
            return
          }
          this.$dialog({
            data: {
              title: this.$t('发布'),
              message: this.$t('是否确认发布所选档案结果？'),
              confirm: () =>
                _this.$API.performanceManagement.buyerPublishList({ resultIds: resultIds })
            },
            success: () => {
              _this.$toast({ content: this.$t('发布成功'), type: 'success' })
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'scoringDetails') {
        // 得分明细
        this.scoreDetail.data = e.data
        let resultId = e.data.id
        this.$set(this.detailConfig[0].treeGrid.asyncConfig.params, 'resultId', resultId)
        this.isShow = true
        this.$nextTick(() => {
          let params = { resultId: resultId }
          this.$API.performanceManagement.buyerDetailIndexScoreList(params).then((res) => {
            if (res.code == 200) {
              this.initRadar(res.data)
            }
          })
          // this.initLine();
        })
      }
      if (e.field == 'supplierCode') {
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            supplierCode: e.data.supplierCode,
            orgCode: e.data.orgCode
          }
        })
      }
      // field == 得分字段名，得分详情弹窗isShow为true；
    },
    // 得分明细-雷达图
    initRadar(data) {
      let indicator = data.map((item) => {
        let newItem = {}
        newItem.text = item.indexName
        return newItem
      })
      let series = data.map((item) => {
        return item.score
      })
      let myechart = echarts.init(this.$refs.myRadar)
      myechart.setOption({
        color: ['#6386C1'],
        radar: [
          {
            // indicator: [
            //   { text: "产品质量" },
            //   { text: "财务表现" },
            //   { text: "市场表现" },
            //   { text: "履约能力" },
            //   { text: "企业信用" },
            // ],
            indicator: indicator,
            center: ['50%', '50%'],
            radius: 100,
            shape: 'circle'
          }
        ],
        series: [
          {
            type: 'radar',
            data: [
              {
                // value: [84, 36, 43, 78, 76],
                value: series,
                areaStyle: {
                  color: '#E8EEF6'
                }
              }
            ]
          }
        ]
      })
    },
    // 得分明细-折线图
    initLine() {
      let myLine = echarts.init(this.$refs.myLine)
      myLine.setOption({
        color: '#00469C',
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#BCCFE5' },
                  { offset: 0.5, color: '#D4E0EE' },
                  { offset: 1, color: '#F7F9FC' }
                ])
              }
            }
          }
        ]
      })
    },
    // 得分明细（弹窗）-得分明细table切换展开/收起
    toggleScoreDetailTreeTable(isOpen) {
      // 组件没把方法抛出，只能这样逐级去找。。。
      let ser1 = this.$refs.detailtable.$children[this.$refs.detailtable.$children.length - 1]
      let ser2 = ser1.$children[0]
      let ser3 = ser2.$children[0]
      let ser4 = ser3.$children[0]
      // console.log(123,ser4);
      this.scoreDetail.isOpen = !this.scoreDetail.isOpen
      if (isOpen) {
        ser4.collapseAll() // 收起全部
      } else {
        ser4.expandAll() // 展开全部
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll, true)
    document.removeEventListener('click')
  }
}
</script>

<style lang="scss" scoped>
/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
.perform-box {
  width: 100%;
  position: relative;
  height: 100%;
  /deep/ .e-rowcell {
    .red {
      background-color: rgba(237, 86, 51, 1);
    }
    .green {
      background-color: rgba(138, 204, 64, 1);
    }
    .yellow {
      background-color: rgba(237, 161, 51, 1);
    }
  }
  .yellowcolor {
    color: rgba(237, 161, 51, 1) !important;
  }
  .bluecolor {
    color: #6386c1 !important;
  }
  .blurbgcolor {
    background-color: #00469c;
  }
  .scoreDetail {
    overflow: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 840px;
    height: 600px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
    padding: 15px;
    font-size: 14px;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    .score-detail-title {
      display: flex;
      align-items: center;
    }
    .section-active {
      margin-top: 2px;
      margin-left: 16px;
      color: #6386c1;
      font-size: 12px;
      font-weight: normal;
      cursor: pointer;
      .active-icons {
        display: inline-block;
      }
      .expand-icons {
        display: inline-block;
        transform: rotate(180deg);
        vertical-align: text-top;
      }
      span {
        margin-left: 5px;
      }
    }
    /deep/ .e-rowcell {
      text-align: left;
    }
  }
  .perEcharts {
    margin-top: 15px;
    height: 274px;
    display: flex;
    div {
      width: 50%;
      height: 100%;
      margin-right: 8px;
    }
  }
  .board {
    height: 100%;
    display: flex;
    .list {
      width: 388px;
      height: 100%;
      border-radius: 8px;
      background-color: #f5f5f5;
      margin: 0 8px;
      padding: 16px;
      .listTop {
        height: 30px;
        line-height: 25px;
        display: flex;
        justify-content: space-between;
        color: #292929;
        font-size: 18px;
        margin-bottom: 8px;
        span {
          color: #ed5633;
          font-size: 28px;
          margin-right: 16px;
        }
      }
      .listCon {
        height: calc(100% - 30px);
        overflow-y: scroll;
        .listDetails {
          width: 340px;
          height: 106px;
          border-radius: 8px;
          background-color: #fff;
          margin-bottom: 16px;
          padding: 16px 24px;
          font-size: 14px;
          color: #292929;
          font-weight: 600;
          .detailtitle {
            font-weight: 600;
            display: flex;
            justify-content: space-between;
          }
          .detailCon {
            display: flex;
            justify-content: space-between;
            font-weight: normal;
            margin: 16px 0;
          }
          .detailTip {
            color: #ed5633;
            font-weight: normal;
          }
        }
      }
    }
  }
}
/deep/ .e-treecolumn-container {
  display: flex !important;
  align-items: center;
  align-content: center;
  overflow: hidden;
}
/deep/.e-grid {
  .e-rowcell {
    text-align: left !important;
    .grid-edit-column {
      span {
        display: block !important;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .icon-title {
        display: inline-block !important;
      }
    }
  }
}
/deep/.tree-title-bold {
  font-weight: bold;
}
</style>
