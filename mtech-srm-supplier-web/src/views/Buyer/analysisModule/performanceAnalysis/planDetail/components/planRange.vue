// 计划清单详情-计划适用范围
<template>
  <div class="full-height">
    <mt-select
      :width="360"
      v-model="rangeType"
      :fields="{ text: 'label', value: 'value' }"
      :data-source="radioData"
      :disabled="form.status == 20 || form.status == 40"
      class="range-type"
      @change="onChange"
    ></mt-select>
    <!-- 指定品类 || 指定供应商 -->
    <div v-if="rangeType == 1 || rangeType == 2" class="transfer">
      <!-- 左框 -->
      <div class="list left">
        <div class="accordion-title">
          <span>{{ rangeType == 1 ? $t('待选分类') : $t('待选供应商') }}</span>
        </div>
        <!-- <mt-tree-grid ref="templateFormLeft"
          :dataSource="left_data"
          :columns="planRangeColumnDataCate"
          :allowSelection='true'
          :autoCheckHierarchy="true"
          childMapping="childrens"
          :treeColumnIndex="1"
          :height="500"
        ></mt-tree-grid> -->
        <mt-template-page
          ref="templateFormLeft"
          :template-config="pageConfigLeft"
          grid-lines="Both"
          height="500px"
        ></mt-template-page>
      </div>
      <div class="content-arrow">
        <div class="content-arrow--btn" @click="push(1)">
          <i class="mt-icons mt-icon-MT_Right_Arrow"></i>
        </div>
        <div class="content-arrow--btn" @click="push(2)">
          <i class="mt-icons mt-icon-MT_Left_Arrow"></i>
        </div>
      </div>
      <!-- 右框 -->
      <div class="list right">
        <div class="accordion-title">
          <span>{{ rangeType == 1 ? $t('已选分类') : $t('已选供应商') }}</span>
        </div>
        <!-- categoryRangeDTOList || supplierRangeDTOList -->
        <mt-template-page
          ref="templateFormRight"
          :template-config="pageConfigRight"
          grid-lines="Both"
          height="500px"
        ></mt-template-page>
        <!-- categoryRangeDTOList || supplierRangeDTOList -->
      </div>
    </div>
  </div>
</template>
<script>
import { planRangeColumnDataCate, planRangeColumnDataSupplier } from '../config/index'
export default {
  props: {
    form: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfigLeft: [
        {
          gridId: 'a0748298-b0ea-9362-5514-9720c3e0370b',
          useToolTemplate: false,
          treeGrid: {
            // allowGrouping: true, // tree不能分组
            allowPaging: false,
            columnData: planRangeColumnDataCate,
            childMapping: 'childrens',
            dataSource: [],
            autoCheckHierarchy: true
          }
        }
      ],
      planRangeColumnDataCate: planRangeColumnDataCate,
      planRangeColumnDataSupplier: planRangeColumnDataSupplier,
      pageConfigRight: [
        {
          gridId: '77f761b1-b39f-3e4c-2291-1ee56e8d96e6',
          useToolTemplate: false,
          treeGrid: {
            // allowGrouping: true, // tree不能分组
            allowPaging: false,
            columnData: planRangeColumnDataCate,
            childMapping: 'childrens',
            dataSource: [],
            autoCheckHierarchy: true
          }
        }
      ],
      rangeType: 1, // rangeType 0哪种也不是 1指定品类 2指定供应商
      radioData: [
        {
          label: this.$t('指定品类'),
          value: 1
        },
        {
          label: this.$t('指定供应商'),
          value: 2
        }
      ],
      // 模拟初始数据
      idStr: 'id',
      allCategory: [], // 全量品类（记录下来，请求过一次后切换select时就不用重新请求了）
      allSupplier: [], // 全量供应商（记录下来，请求过一次后切换select时就不用重新请求了）
      all_data: [], // 全量树
      all_data1: [], // 全量树
      left_data: [], // 左框数据
      right_data: [], // 右框数据
      rightIdArr: [], // 右侧数据id集合,
      categoryRangeDTOList: []
    }
  },
  mounted() {
    // designatedCategory是否指定品类 Y N; designatedSupplier是否指定供应商 Y N
    // rangeType 0哪种也不是 1指定品类 2指定供应商
    this.rangeType = this.form
      ? this.form.designatedCategory == 'Y'
        ? 1
        : this.form.designatedSupplier == 'Y'
        ? 2
        : 1
      : 1
    // this.rangeType = this.form.rangeType || 0;

    // 数据处理（请求全量树，只请求一次，过滤出左右穿梭框数据，筛选出“待选”和“已选”）
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      let columnData =
        this.rangeType == 1
          ? this.planRangeColumnDataCate
          : this.rangeType == 2
          ? this.planRangeColumnDataSupplier
          : []
      // status计划状态  10 未提交 20已提交 30已驳回  40启用  50停用  60已过期
      if (this.form.status != 20 && this.form.status != 40) {
        // planDetailColumnData 20已提交和40启用时不展示复选框不可操作，其它状态添加复选框可操作

        if (!columnData[0].showCheckbox) {
          columnData = [{ width: '150', showCheckbox: true }, ...columnData]
        }
      }

      this.$set(this.pageConfigLeft[0].treeGrid, 'columnData', columnData)
      this.$set(this.pageConfigRight[0].treeGrid, 'columnData', columnData)
      // rangeType 0哪种也不是 1指定品类 2指定供应商
      if (this.rangeType == 1) {
        // 全量品类（记录下来，请求过一次后切换select时就不用重新请求了）
        if (!this.allCategory.length) {
          // 查询品类全量树
          this.$API.planDetail.queryTemplateCategoryAllTree({ id: this.form.id }).then((res) => {
            this.allCategory = res.data || [] // 全量品类
            // 初始品类赋值
            this.initCateData()
          })
        } else {
          // 初始品类赋值
          this.initCateData()
        }
      } else {
        // 全量供应商（记录下来，请求过一次后切换select时就不用重新请求了）
        if (!this.allSupplier.length) {
          // 查询供应商全量树
          this.$API.planDetail
            .queryTemplateManagerAllTree({
              orgId: this.form.orgId,
              page: { current: '1', size: '100' }
            })
            .then((res) => {
              this.allSupplier = res.data.records || []
              // 初始供应商赋值
              this.initSupplierData()
            })
        } else {
          // 初始供应商赋值
          this.initSupplierData()
        }
      }
    },
    recursion1(data, arr) {
      data.forEach((item) => {
        item.parent = [...arr, item[this.idStr]]
        if (item.childrens && item.childrens.length > 0) {
          this.recursion1(item.childrens, item.parent)
        }
      })
    },
    recursion2(data, arr) {
      data.forEach((item) => {
        if (!item.childrens) item.childrens = []
        if (item.childrens.length == 0) {
          arr.push(item)
        } else {
          if (item.childrens && item.childrens.length > 0) {
            this.recursion2(item.childrens, arr)
          }
        }
      })
    },
    // 初始品类赋值
    initCateData() {
      this.idStr = 'id'
      this.all_data = this.allCategory // 全量树
      this.recursion1(this.all_data, [])
      // 已选品类（引用模板计划适用范围 指定品类）
      this.right_data = JSON.parse(JSON.stringify(this.form.categoryRangeDTOList)) || []
      this.recursion1(this.right_data, [])
      // // 筛选出右侧数据id集合
      // this.filterRightIds(this.right_data, this.rightIdArr);
      // // 过滤出左右穿梭框数据
      this.dataFilter()
    },
    // 初始供应商赋值
    initSupplierData() {
      this.idStr = 'supplierEnterpriseId'
      this.all_data = this.allSupplier // 全量树

      this.recursion1(this.all_data, [])
      // 已选供应商（引用模板计划适用范围 指定供应商）
      this.right_data = JSON.parse(JSON.stringify(this.form.supplierRangeDTOList)) || []

      this.recursion1(this.right_data, [])
      // 筛选出右侧数据id集合
      // this.filterRightIds(this.right_data, this.rightIdArr);
      // 过滤出左右穿梭框数据
      this.dataFilter()
    },
    // 过滤出左右穿梭框数据
    dataFilter() {
      this.all_data1 = JSON.parse(JSON.stringify(this.all_data)) // 全量树
      let allArr = []
      let selectArr = []
      this.recursion2(this.all_data1, allArr)
      this.recursion2(this.right_data, selectArr)
      for (let i = 0; i < allArr.length; i++) {
        let bol = selectArr.some((e) => {
          return e[this.idStr] == allArr[i][this.idStr]
        })
        if (bol) {
          allArr.splice(i, 1)
          i--
        }
      }
      let arr = []
      allArr.forEach((item) => {
        arr.push(...item.parent)
      })
      let arr1 = JSON.parse(JSON.stringify(this.all_data1))
      this.remove(arr1, arr)
      // 赋值加延迟异步，用于解决组件loading不消失的bug
      setTimeout(() => {
        this.$set(this.pageConfigLeft[0].treeGrid, 'dataSource', arr1)
        this.$set(this.pageConfigRight[0].treeGrid, 'dataSource', this.right_data)
      }, 100)
    },
    // 筛选出右侧数据id集合
    filterRightIds(data, selectedIds) {
      data.forEach((ele) => {
        selectedIds.push(ele[this.idStr])
        if (ele.childrens && ele.childrens.length > 0) {
          this.filterRightIds(ele.childrens, selectedIds)
        }
      })
    },
    // 给全量树数据添加字段标记左右侧数据（只给最末级加 dataType 左0 右1）
    processFullTreeData(data) {
      data.forEach((ele) => {
        if (ele.childrens && ele.childrens.length > 0) {
          this.processFullTreeData(ele.childrens)
        } else {
          if (this.rightIdArr.indexOf(ele[this.idStr]) != -1) {
            ele.dataType = 1
          } else {
            ele.dataType = 0
          }
        }
      })
    },
    // 重组树形结构数据
    regroupTreeData(arr = [], dataType, idx) {
      arr.forEach((ele, index) => {
        if (!idx) {
          idx = index
        }
        if (ele) {
          if (ele.childrens && ele.childrens.length > 0) {
            this.regroupTreeData(ele.childrens, dataType, index)
          } else {
            if (ele.dataType == dataType) {
              // arr.splice(index, 1); // 这样子会改变数组长度
              arr[index] = null
            }
          }
        }
      })
    },
    // 移除tree里子集为null的父级
    removeNullParentTreeNode(arr = []) {
      return arr
        .filter((item) => {
          if (item && item.childrens && item.childrens.length > 0) {
            let notNullArr = item.childrens.filter((obj) => {
              return obj != null
            })
            return notNullArr.length // 过滤条件
          }
          return item != null // 过滤条件
        })
        .map((item) => {
          item = Object.assign({}, item)
          if (item.childrens && item.childrens.length > 0) {
            item.childrens = this.removeNullParentTreeNode(item.childrens)
          }
          return item
        })
    },
    // 移除tree里除末级外子集为[]的父级
    diguiLeft(arr = []) {
      return arr.filter((item) => {
        if (item && item.childrens) {
          if (item.childrens.length > 0) {
            this.diguiLeft(item.childrens)
          }
          if (this.rightIdArr.includes(item[this.idStr])) {
            return item.childrens.length // 过滤条件
          } else {
            return true
          }
        }
        return item != null // 过滤条件
      })
    },
    diguiRight(arr = []) {
      for (let i = 0; i < arr.length; i++) {
        //  const element = array[i];
        if (arr[i].childrens.length > 0) {
          this.diguiRight(arr[i].childrens)
        } else {
          if (!this.rightIdArr.includes(arr[i].id)) {
            arr.splice(i, 1)
          }
        }
      }
      // return arr.filter(item => {
      //   if (item && item.childrens) {
      //     if (item.childrens.length > 0) {
      //       this.diguiRight(item.childrens);
      //     }
      //     if(this.rightIdArr.includes(item.id)){
      //       return item.childrens.length;// 过滤条件
      //     }else{
      //       return true
      //     }

      //   }
      //   return item != null; // 过滤条件
      // });
    },
    add(data, arr) {
      data.forEach((item) => {
        arr.push(...item.parent)
        this.add(item.childrens, arr)
      })
    },
    remove(data, arr) {
      for (let i = 0; i < data.length; i++) {
        this.remove(data[i].childrens, arr)
        if (arr.indexOf(data[i][this.idStr]) == -1) {
          data.splice(i, 1)
          i--
        }
      }
    },
    remove1(data, arr) {
      for (let i = 0; i < data.length; i++) {
        this.remove1(data[i].childrens, arr)
        if (arr.indexOf(data[i][this.idStr]) != -1) {
          data.splice(i, 1)
          i--
        }
      }
    },
    // 添加数据
    push(str) {
      let selectGridRecords = []
      if (str == 1) {
        // 左 -> 右
        selectGridRecords = this.$refs.templateFormLeft
          .getCurrentTabRef()
          .treeGrid.getCheckedRecords()
        let selectarr = []
        let removeArr = []
        selectGridRecords.forEach((item) => {
          selectarr.push(...item.parent)
          removeArr.push(item[this.idStr])
        })
        this.add(this.pageConfigRight[0].treeGrid.dataSource, selectarr)
        this.right_data = JSON.parse(JSON.stringify(this.all_data1))
        this.remove(this.right_data, selectarr)

        console.log(JSON.stringify(this.right_data))
        let leftArr = this.pageConfigLeft[0].treeGrid.dataSource
        this.remove1(leftArr, removeArr)
        setTimeout(() => {
          this.$set(this.pageConfigRight[0].treeGrid, 'dataSource', this.right_data)
          this.$set(this.pageConfigLeft[0].treeGrid, 'dataSource', leftArr)
        }, 100)
      } else {
        selectGridRecords = this.$refs.templateFormRight
          .getCurrentTabRef()
          .treeGrid.getCheckedRecords()
        let selectarr = []
        let removeArr = []
        selectGridRecords.forEach((item) => {
          selectarr.push(...item.parent)
          removeArr.push(item[this.idStr])
        })
        this.add(this.pageConfigLeft[0].treeGrid.dataSource, selectarr)

        let leftArr = JSON.parse(JSON.stringify(this.all_data1))
        this.remove(leftArr, selectarr)

        this.right_data = this.pageConfigRight[0].treeGrid.dataSource
        this.remove1(this.right_data, removeArr)

        // 赋值加延迟异步，用于解决组件loading不消失的bug
        setTimeout(() => {
          this.$set(this.pageConfigLeft[0].treeGrid, 'dataSource', leftArr)
          this.$set(this.pageConfigRight[0].treeGrid, 'dataSource', this.right_data)
        }, 100)
      }
    },
    // 切换select选项（指定品类、指定供应商）时
    onChange(e) {
      if (e.itemData) {
        this.rangeType = e.itemData.value
        // 清空所有数据
        this.all_data = []
        this.left_data = []
        this.right_data = []
        this.rightIdArr = []
        this.initData()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  flex: 1;
}
.content-arrow {
  &--btn {
    height: 30px;
    width: 30px;
    border: 2px solid #98aac3;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    color: #98aac3;
    margin: 10px;
    cursor: pointer;
  }
}
.transfer {
  display: flex;
  justify-content: center;
  align-items: center;
}
.transfer > .list {
  width: 709px;
  height: 640px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
  display: flex;
  flex-direction: column;
  .mt-data-grid {
    margin: 0 auto;
  }
  /deep/ .e-rowcell {
    text-align: left;
  }
}
.range-type {
  margin: 24px 0 16px 30px;
}
.accordion-title {
  float: left;
  margin-bottom: 16px;
  padding: 18px 0 18px 0;
  width: 100%;
  border-bottom: 1px solid rgba(232, 232, 232, 1);
  span {
    display: inline-block;
    border-left: 2px solid #00469c;
    text-indent: 10px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin-left: 10px;
  }
}
</style>
