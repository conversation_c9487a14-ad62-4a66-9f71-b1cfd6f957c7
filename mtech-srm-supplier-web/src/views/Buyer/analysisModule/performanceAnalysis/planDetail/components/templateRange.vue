// 计划清单详情-模板适用范围
<template>
  <div class="full-height">
    <mt-select
      :width="360"
      v-model="rangeType"
      :fields="{ text: 'label', value: 'value' }"
      :data-source="radioData"
      :disabled="form.status == 2"
      class="range-type"
      @change="onchang"
    ></mt-select>
    <div v-if="rangeType === 1 || rangeType === 2" class="transfer">
      <!-- 左框 -->
      <div class="list left">
        <div class="accordion-title">
          <span>{{ $t('待选品类') }}</span>
        </div>
        <!-- <mt-tree-grid ref="templateFormLeft"
          :dataSource="old_info"
          :columns="planRangeColumnDataCate"
          :allowSelection='true'
          :autoCheckHierarchy="true"
          childMapping="childrens"
          :treeColumnIndex="1"
          :height="500"
        ></mt-tree-grid> -->
        <mt-template-page
          style="height: 500px"
          ref="templateFormLeft"
          :template-config="pageConfigLeft"
          grid-lines="Both"
        ></mt-template-page>
      </div>
      <div class="content-arrow">
        <div class="content-arrow--btn" @click="push(1)">
          <i class="mt-icons mt-icon-MT_Right_Arrow"></i>
        </div>
        <div class="content-arrow--btn" @click="push(2)">
          <i class="mt-icons mt-icon-MT_Left_Arrow"></i>
        </div>
      </div>
      <!-- {{ $t("右框") }} -->
      <div class="list right">
        <div class="accordion-title">
          <span>{{ $t('已选品类') }}</span>
        </div>
        <!-- rangeDTOList -->
        <mt-template-page
          style="height: 500px"
          ref="templateFormRight"
          :template-config="pageConfig"
          grid-lines="Both"
        ></mt-template-page>
        <!-- rangeDTOList -->
      </div>
    </div>
  </div>
</template>
<script>
import { planRangeColumnDataCate } from '../config/index'
export default {
  props: {
    form: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfigLeft: [
        {
          gridId: '63abc4bf-96cd-05fe-f2d1-4b79d859d394',
          useToolTemplate: false,
          treeGrid: {
            height: 500,
            allowPaging: false,
            columnData: planRangeColumnDataCate,
            // enableVirtualization: true,
            childMapping: 'childrens',
            autoCheckHierarchy: true,
            // dataSource: [],
            dataSource: []
          }
        }
      ],
      planRangeColumnDataCate: planRangeColumnDataCate,
      pageConfig: [
        {
          gridId: '39b13d17-d5f9-a691-563d-60d607499eac',
          useToolTemplate: false,
          treeGrid: {
            allowPaging: false,
            height: 500,
            autoCheckHierarchy: true,
            columnData: planRangeColumnDataCate,
            // enableVirtualization: true,
            childMapping: 'childrens',
            // dataSource: [],
            dataSource: []
          }
        }
      ],
      rangeType: this.form.rangeType,
      radioData: [
        {
          label: this.$t('通用'),
          value: 0
        },
        {
          label: this.$t('部分品类'),
          value: 1
        },
        {
          label: this.$t('排除以下品类'),
          value: 2
        }
      ],
      // 模拟初始数据
      info: [],
      old_info: [], // 左框数据
      new_info: [], // 右框数据
      rightIdArr: [] //id集合
    }
  },
  mounted() {
    // this.rangeType=this.form.rangeType
    console.log(planRangeColumnDataCate)
    if (this.form.status != 2) {
      // planRangeColumnDataCate
      if (!this.planRangeColumnDataCate[0].showCheckbox) {
        this.planRangeColumnDataCate.unshift({ width: '150', showCheckbox: true })
      }

      this.pageConfigLeft[0].treeGrid.columnData = this.planRangeColumnDataCate
      this.pageConfig[0].treeGrid.columnData = this.planRangeColumnDataCate
    }
    console.log(this.pageConfigLeft)
    console.log(this.rangeType)
    this.dataFilterinit()
  },
  methods: {
    dataFilter() {
      console.log(this.rangeType, this.form.rangeType)
      if (this.rangeType == this.form.rangeType) {
        this.new_info = JSON.parse(JSON.stringify(this.form.rangeDTOList))
      } else {
        this.new_info = []
      }
      this.pushParent(this.info, [])
      this.pushParent(this.new_info, [])
      // this.processFullTreeData(this.info);

      console.log(this.info)
      // 根据标记的类型字段（dataType 左0 右1）筛选出左右数据
      this.old_info = JSON.parse(JSON.stringify(this.info))
      let allArr = []
      let selectArr = []
      this.digui(this.info, allArr)
      this.digui(this.new_info, selectArr)
      // this.regroupTreeData(this.old_info, 1);
      // this.old_info = this.removeNullParentTreeNode(this.old_info);
      // this.old_info = this.digui(this.old_info);
      for (let i = 0; i < allArr.length; i++) {
        let bol = selectArr.some((e) => {
          return e.id == allArr[i].id
        })
        if (bol) {
          allArr.splice(i, 1)
          i--
        }
      }
      let arr = []
      allArr.forEach((item) => {
        arr.push(...item.parent)
      })
      let arr1 = JSON.parse(JSON.stringify(this.info))
      this.remove(arr1, arr)

      // 赋值加延迟异步，用于解决组件loading不消失的bug
      setTimeout(() => {
        // 给左右穿梭框赋值
        this.$set(this.pageConfigLeft[0].treeGrid, 'dataSource', arr1)
        this.$set(this.pageConfig[0].treeGrid, 'dataSource', this.new_info)
      }, 100)
    },
    // 过滤出左右穿梭框数据
    dataFilterinit() {
      let str = 'common'
      if (this.form.templateBusiness == 2) {
        str = 'product'
      }
      this.$API.templateList.queryAllTree({ code: str }).then((res) => {
        console.log(res)
        this.info = res.data
        // this.old_info=this.info
        // let arr=[]
        if (!this.form.rangeDTOList) {
          this.form.rangeDTOList = []
        }
        this.dataFilter()
      })
    },
    add(data, arr) {
      data.forEach((item) => {
        arr.push(...item.parent)
        this.add(item.childrens, arr)
      })
    },
    remove(data, arr) {
      for (let i = 0; i < data.length; i++) {
        this.remove(data[i].childrens, arr)
        if (arr.indexOf(data[i].id) == -1) {
          data.splice(i, 1)
          i--
        }
      }
    },
    remove1(data, arr) {
      for (let i = 0; i < data.length; i++) {
        this.remove1(data[i].childrens, arr)
        if (arr.indexOf(data[i].id) != -1) {
          data.splice(i, 1)
          i--
        }
      }
    },

    pushParent(data, arr) {
      data.forEach((item) => {
        item.parent = [...arr, item.id]
        this.pushParent(item.childrens, item.parent)
      })
    },
    digui(data, arr) {
      data.forEach((item) => {
        if (item.childrens.length == 0) {
          arr.push(item)
        } else {
          this.digui(item.childrens, arr)
        }
      })
    },

    onchang(e) {
      console.log(e)
      if (e.itemData) {
        this.rangeType = e.itemData.value
        // this.form.rangeDTOList=[]
        this.rightIdArr = []
        console.log('onchang', this.rangeType, e)
        // this.dataFilterinit()
        this.dataFilter()
        console.log(this.pageConfigLeft, this.pageConfig)
      }

      // this.dataFilter()
    },
    push(str) {
      let selectGridRecords = []
      if (str == 1) {
        // 左 -> 右
        selectGridRecords = this.$refs.templateFormLeft
          .getCurrentTabRef()
          .treeGrid.getCheckedRecords()
        let selectarr = []
        let removeArr = []
        selectGridRecords.forEach((item) => {
          selectarr.push(...item.parent)
          removeArr.push(item.id)
        })
        this.add(this.pageConfig[0].treeGrid.dataSource, selectarr)
        this.new_info = JSON.parse(JSON.stringify(this.info))
        this.remove(this.new_info, selectarr)

        console.log(JSON.stringify(this.new_info))
        let leftArr = this.pageConfigLeft[0].treeGrid.dataSource
        this.remove1(leftArr, removeArr)

        // 赋值加延迟异步，用于解决组件loading不消失的bug
        setTimeout(() => {
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', this.new_info)
          this.$set(this.pageConfigLeft[0].treeGrid, 'dataSource', leftArr)
        }, 100)
      } else {
        selectGridRecords = this.$refs.templateFormRight
          .getCurrentTabRef()
          .treeGrid.getCheckedRecords()
        let selectarr = []
        let removeArr = []
        selectGridRecords.forEach((item) => {
          selectarr.push(...item.parent)
          removeArr.push(item.id)
        })
        this.add(this.pageConfigLeft[0].treeGrid.dataSource, selectarr)
        let leftArr = JSON.parse(JSON.stringify(this.info))
        this.remove(leftArr, selectarr)

        this.new_info = this.pageConfig[0].treeGrid.dataSource
        this.remove1(this.new_info, removeArr)

        // 赋值加延迟异步，用于解决组件loading不消失的bug
        setTimeout(() => {
          this.$set(this.pageConfigLeft[0].treeGrid, 'dataSource', leftArr)
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', this.new_info)
        }, 100)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  flex: 1;
}
.content-arrow {
  &--btn {
    height: 30px;
    width: 30px;
    border: 2px solid #98aac3;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    color: #98aac3;
    margin: 10px;
    cursor: pointer;
  }
}
.transfer {
  display: flex;
  justify-content: center;
  align-items: center;
}
.transfer > .list {
  width: 709px;
  height: 640px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
  display: flex;
  flex-direction: column;
  .mt-data-grid {
    margin: 0 auto;
  }
}
.range-type {
  margin: 24px 0 16px 30px;
}
.accordion-title {
  float: left;
  margin-bottom: 16px;
  padding: 18px 0 18px 0;
  width: 100%;
  border-bottom: 1px solid rgba(232, 232, 232, 1);
  span {
    display: inline-block;
    border-left: 2px solid #00469c;
    text-indent: 10px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin-left: 10px;
  }
}
</style>
