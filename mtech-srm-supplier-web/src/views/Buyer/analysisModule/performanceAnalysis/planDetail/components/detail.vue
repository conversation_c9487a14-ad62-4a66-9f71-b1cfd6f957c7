<!--
 * @Author: your name
 * @Date: 2021-10-28 13:19:58
 * @LastEditTime: 2021-11-02 17:35:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalCenter\components\detail.vue
-->
<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ $t('审批详情') }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="slider-content">
        <div class="rule-group">
          <div class="group-header">{{ $t('审批结果') }}</div>
          <div class="group-description"></div>
          <div class="group-content">
            <mt-form>
              <mt-form-item class="block">
                <mt-input
                  :value="
                    data.result == 'pass'
                      ? $t('通过')
                      : data.result == 'reject'
                      ? $t('驳回')
                      : $t('待审批')
                  "
                  :show-clear-button="false"
                  :readonly="true"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </div>
        <div class="rule-group">
          <div class="group-header">{{ $t('审批时间') }}</div>
          <div class="group-description"></div>
          <div class="group-content">
            <mt-form>
              <mt-form-item class="block">
                <mt-input v-model="data.endTime" :show-clear-button="false" :readonly="true" />
              </mt-form-item>
            </mt-form>
          </div>
        </div>
        <div class="rule-group">
          <div class="group-header">{{ $t('审批意见') }}</div>
          <div class="group-description"></div>
          <div class="group-content">
            <mt-form>
              <mt-form-item class="block">
                <mt-input
                  v-model="data.msg"
                  :multiline="true"
                  :rows="5"
                  :show-clear-button="false"
                  :readonly="true"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </div>
      </div>
      <div class="slider-footer mt-flex">
        <span @click="cancel">{{ $t('确定') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Detail',
  components: {},
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      strategyRules: [
        {
          groupLeft: '',
          dataSource: '',
          dataValue: '',
          symbol: '',
          groupRight: '',
          groupLink: 'or'
        }
      ],
      queryFields: { text: 'headerText', value: 'field' },
      queryDataSource: [],
      strategyDetail: {
        configId: null,
        strategyName: '',
        sortValue: 1,
        targetType: 0,
        targetId: 0
      },
      targetTypeList: [
        // 选择分配到的类型
        { text: this.$t('部门'), value: 2 },
        { text: this.$t('采购组'), value: 1 },
        { text: this.$t('人'), value: 0 }
      ]
    }
  },
  mounted() {},
  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    },
    data() {
      return this.modalData.data
    }
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.block {
  display: block;
}
</style>
