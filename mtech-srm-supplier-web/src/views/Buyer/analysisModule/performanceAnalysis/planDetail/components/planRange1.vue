// 计划清单详情-计划适用范围
<template>
  <div class="full-height">
    <mt-select
      :width="360"
      v-model="rangeType"
      :fields="{ text: 'label', value: 'value' }"
      :data-source="radioData"
      :disabled="form.status == 20 || form.status == 40"
      class="range-type"
      @change="onChange"
    ></mt-select>
    <!-- 指定品类 || 指定供应商 -->
    <div v-if="rangeType == 1 || rangeType == 2" class="transfer">
      <!-- 左框 -->
      <div class="list left">
        <div class="accordion-title">
          <span>{{ rangeType == 1 ? $t('待选分类') : $t('待选供应商') }}</span>
        </div>
        <!-- <mt-tree-grid ref="templateFormLeft"
          :dataSource="left_data"
          :columns="planRangeColumnDataCate"
          :allowSelection='true'
          :autoCheckHierarchy="true"
          childMapping="childrens"
          :treeColumnIndex="1"
          :height="500"
        ></mt-tree-grid> -->
        <mt-template-page
          ref="templateFormLeft"
          :template-config="pageConfigLeft"
          grid-lines="Both"
          height="500px"
        ></mt-template-page>
      </div>
      <div class="content-arrow">
        <div class="content-arrow--btn" @click="push(1)">
          <i class="mt-icons mt-icon-MT_Right_Arrow"></i>
        </div>
        <div class="content-arrow--btn" @click="push(2)">
          <i class="mt-icons mt-icon-MT_Left_Arrow"></i>
        </div>
      </div>
      <!-- 右框 -->
      <div class="list right">
        <div class="accordion-title">
          <span>{{ rangeType == 1 ? $t('已选分类') : $t('已选供应商') }}</span>
        </div>
        <!-- categoryRangeDTOList || supplierRangeDTOList -->
        <mt-template-page
          ref="templateFormRight"
          :template-config="pageConfigRight"
          grid-lines="Both"
          height="500px"
        ></mt-template-page>
        <!-- categoryRangeDTOList || supplierRangeDTOList -->
      </div>
    </div>
  </div>
</template>
<script>
import { planRangeColumnDataCate, planRangeColumnDataSupplier } from '../config/index'
export default {
  props: {
    form: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfigLeft: [
        {
          gridId: '096b3b88-1d84-8a1d-915f-3be60f52dec4',
          useToolTemplate: false,
          treeGrid: {
            // allowGrouping: true, // tree不能分组
            allowPaging: false,
            columnData: planRangeColumnDataCate,
            childMapping: 'childrens',
            autoCheckHierarchy: true, // 父子关联（半选）
            dataSource: []
          }
        }
      ],
      planRangeColumnDataCate: planRangeColumnDataCate,
      planRangeColumnDataSupplier: planRangeColumnDataSupplier,
      pageConfigRight: [
        {
          gridId: 'bff480e0-af6f-04a8-48f0-cf92d237b7ec',
          useToolTemplate: false,
          treeGrid: {
            // allowGrouping: true, // tree不能分组
            allowPaging: false,
            columnData: planRangeColumnDataCate,
            childMapping: 'childrens',
            autoCheckHierarchy: true, // 父子关联（半选）
            dataSource: []
          }
        }
      ],
      rangeType: 1, // rangeType 0哪种也不是 1指定品类 2指定供应商
      radioData: [
        {
          label: this.$t('指定品类'),
          value: 1
        },
        {
          label: this.$t('指定供应商'),
          value: 2
        }
      ],
      // 模拟初始数据
      idStr: 'id',
      allCategory: [], // 全量品类（记录下来，请求过一次后切换select时就不用重新请求了）
      allSupplier: [], // 全量供应商（记录下来，请求过一次后切换select时就不用重新请求了）
      all_data: [], // 全量树
      left_data: [], // 左框数据
      right_data: [], // 右框数据
      rightIdArr: [], // 右侧数据id集合,
      firstTreeLevelTreePath: '', // 一级的treePath，所有都一样，所以最后要排除出去
      rightTreePathArr: [] // 右侧数据treePath集合（排除一级的treePath之后，剩下的对应选中项的id，含半选）
    }
  },
  mounted() {
    // designatedCategory是否指定品类 Y N; designatedSupplier是否指定供应商 Y N
    // rangeType 0哪种也不是 1指定品类 2指定供应商
    this.rangeType = this.form
      ? this.form.designatedCategory == 'Y'
        ? 1
        : this.form.designatedSupplier == 'Y'
        ? 2
        : 1
      : 1
    // this.rangeType = this.form.rangeType || 0;

    // 数据处理（请求全量树，只请求一次，过滤出左右穿梭框数据，筛选出“待选”和“已选”）
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      let columnData =
        this.rangeType == 1
          ? this.planRangeColumnDataCate
          : this.rangeType == 2
          ? this.planRangeColumnDataSupplier
          : []
      // status计划状态  10 未提交 20已提交 30已驳回  40启用  50停用  60已过期
      if (this.form.status != 20 && this.form.status != 40) {
        // planDetailColumnData 20已提交和40启用时不展示复选框不可操作，其它状态添加复选框可操作
        if (!columnData[0].showCheckbox) {
          columnData.unshift({ width: '150', showCheckbox: true })
        }
      }
      this.$set(this.pageConfigLeft[0].treeGrid, 'columnData', columnData)
      this.$set(this.pageConfigRight[0].treeGrid, 'columnData', columnData)

      // rangeType 0哪种也不是 1指定品类 2指定供应商
      if (this.rangeType == 1) {
        // 全量品类（记录下来，请求过一次后切换select时就不用重新请求了）
        if (!this.allCategory.length) {
          // 查询品类全量树
          this.$API.planDetail.queryTemplateCategoryAllTree({ id: this.form.id }).then((res) => {
            this.allCategory = res.data || [] // 全量品类
            // 初始品类赋值
            this.initCateData()
          })
        } else {
          // 初始品类赋值
          this.initCateData()
        }
      } else {
        // 全量供应商（记录下来，请求过一次后切换select时就不用重新请求了）
        if (!this.allSupplier.length) {
          // 查询供应商全量树
          this.$API.planDetail
            .queryTemplateManagerAllTree({
              orgId: this.form.orgId,
              page: { current: '1', size: '100' }
            })
            .then((res) => {
              this.allSupplier = res.data.records || []
              // 初始供应商赋值
              this.initSupplierData()
            })
        } else {
          // 初始供应商赋值
          this.initSupplierData()
        }
      }
    },
    // 初始品类赋值
    initCateData() {
      this.idStr = 'id'
      this.all_data = this.allCategory // 全量树
      // 已选品类（引用模板计划适用范围 指定品类）
      this.right_data = JSON.parse(JSON.stringify(this.form.categoryRangeDTOList)) || []
      // 筛选出右侧数据id和treePath集合（品类树形数据才有）
      this.filterRightIdsAndTreePaths(this.right_data, this.rightIdArr, this.rightTreePathArr)
      // rangeType 0哪种也不是 1指定品类 2指定供应商
      if (this.rangeType == 1) {
        // this.rightTreePathArr去重
        this.rightTreePathArr = this.distinctSelectedTreePaths(this.rightTreePathArr)
      }
      // 过滤出左右穿梭框数据
      this.dataFilter()
    },
    // 初始供应商赋值
    initSupplierData() {
      this.idStr = 'supplierEnterpriseId'
      this.all_data = this.allSupplier // 全量树
      // 已选供应商（引用模板计划适用范围 指定供应商）
      this.right_data = JSON.parse(JSON.stringify(this.form.supplierRangeDTOList)) || []
      // 筛选出右侧数据id和treePath集合（品类树形数据才有）
      this.filterRightIdsAndTreePaths(this.right_data, this.rightIdArr, this.rightTreePathArr)
      // rangeType 0哪种也不是 1指定品类 2指定供应商
      if (this.rangeType == 1) {
        // this.rightTreePathArr去重
        this.rightTreePathArr = this.distinctSelectedTreePaths(this.rightTreePathArr)
      }
      // 过滤出左右穿梭框数据
      this.dataFilter()
    },
    // 过滤出左右穿梭框数据
    dataFilter() {
      let columnData =
        this.rangeType == 1
          ? this.planRangeColumnDataCate
          : this.rangeType == 2
          ? this.planRangeColumnDataSupplier
          : null
      this.$set(this.pageConfigLeft[0].treeGrid, 'columnData', columnData)
      this.$set(this.pageConfigRight[0].treeGrid, 'columnData', columnData)

      // 一级的treePath，所有都一样，所以最后要排除出去
      if (this.firstTreeLevelTreePath == '' && this.all_data.length) {
        this.firstTreeLevelTreePath = this.all_data[0].treePath
      }
      // 给全量树数据添加字段标记左右侧数据（dataType 左0 右1 半选2）
      this.processFullTreeData(this.all_data)
      // 根据标记的类型字段（dataType 左0 右1）筛选出左右数据
      this.left_data = JSON.parse(JSON.stringify(this.all_data))
      // 递归筛选左侧树（含半选）

      this.left_data = this.recursionFilterTree(this.left_data, 0)

      // 右侧有值才递归筛选处理
      if (this.rightIdArr.length) {
        this.right_data = JSON.parse(JSON.stringify(this.all_data))
        // 递归筛选右侧树（含半选）
        this.right_data = this.recursionFilterTree(this.right_data, 1)
      } else {
        this.right_data = []
      }
      // 给左右穿梭框赋值
      this.$set(this.pageConfigLeft[0].treeGrid, 'dataSource', this.left_data)
      this.$set(this.pageConfigRight[0].treeGrid, 'dataSource', this.right_data)
    },
    // 筛选出右侧数据id和treePath集合（品类树形数据才有）
    filterRightIdsAndTreePaths(data, selectedIds, selectedTreePaths) {
      data.forEach((ele) => {
        selectedIds.push(ele[this.idStr])
        // rangeType 0哪种也不是 1指定品类 2指定供应商
        if (this.rangeType == 1) {
          let treePaths = ele.treePath.split(',')
          selectedTreePaths.push(...treePaths)
        }
        if (ele.childrens && ele.childrens.length > 0) {
          this.filterRightIdsAndTreePaths(ele.childrens, selectedIds, selectedTreePaths)
        }
      })
    },
    // selectedTreePaths去重
    distinctSelectedTreePaths(arr) {
      let newArr = Array.from(new Set(arr))
      // 一级的treePath，所有都一样，所以最后要排除出去
      newArr = newArr.filter((item) => {
        return item != this.firstTreeLevelTreePath
      })
      return newArr
    },
    // 给全量树数据添加字段标记左右侧数据（dataType 左0 右1 半选2）
    processFullTreeData(data) {
      data.forEach((ele) => {
        if (this.rightIdArr.indexOf(ele[this.idStr]) == -1) {
          // rangeType 0哪种也不是 1指定品类 2指定供应商
          if (this.rangeType == 1) {
            if (this.rightTreePathArr.indexOf(ele[this.idStr]) != -1) {
              ele.dataType = 2 //半选
            } else {
              ele.dataType = 0 //左
            }
          } else {
            ele.dataType = 0 //左
          }
        } else {
          // rangeType 0哪种也不是 1指定品类 2指定供应商
          if (this.rangeType == 1) {
            if (this.rightTreePathArr.indexOf(ele[this.idStr]) != -1 && ele.childrens) {
              ele.dataType = 2 //半选
            } else {
              ele.dataType = 1 //右
            }
          } else {
            ele.dataType = 1 //右
          }
        }
        if (ele.childrens && ele.childrens.length > 0) {
          this.processFullTreeData(ele.childrens)
        }
      })
    },
    // 递归筛选出左右穿梭框tree数据（含半选）（dataType 0左 1右 2半选）
    recursionFilterTree(arr = [], dataType) {
      return arr
        .filter((item) => {
          return item.dataType == dataType || item.dataType == 2 // 过滤条件
        })
        .map((item) => {
          item = Object.assign({}, item)
          if (item.childrens && item.childrens.length > 0) {
            item.childrens = this.recursionFilterTree(item.childrens, dataType)
          }
          return item
        })
    },
    // 添加数据
    push(str) {
      let selectGridRecords = []
      if (str == 1) {
        // 左 -> 右
        selectGridRecords = this.$refs.templateFormLeft
          .getCurrentTabRef()
          .treeGrid.getCheckedRecords()
      } else {
        // 左 ← 右
        selectGridRecords = this.$refs.templateFormRight
          .getCurrentTabRef()
          .treeGrid.getCheckedRecords()
      }
      // 筛选出选中数据id集合
      let selectedIdArr = selectGridRecords.map((item) => {
        return item[this.idStr]
      })
      // rangeType 0哪种也不是 1指定品类 2指定供应商
      let selectedTreePathArr = []
      if (this.rangeType == 1) {
        // 筛选出选中数据treePath集合
        selectGridRecords.forEach((ele) => {
          let treePaths = ele.treePath.split(',')
          selectedTreePathArr.push(...treePaths)
        })
      }

      if (str == 1) {
        // 左 -> 右
        this.rightIdArr.push(...selectedIdArr)
        // rangeType 0哪种也不是 1指定品类 2指定供应商
        if (this.rangeType == 1) {
          this.rightTreePathArr.push(...selectedTreePathArr)
          // selectedTreePaths去重
          this.rightTreePathArr = this.distinctSelectedTreePaths(this.rightTreePathArr)
        }
      } else {
        // 左 ← 右
        this.rightIdArr = this.rightIdArr.filter((item) => {
          return selectedIdArr.indexOf(item) == -1
        })
        // rangeType 0哪种也不是 1指定品类 2指定供应商
        if (this.rangeType == 1) {
          // selectedTreePaths去重
          selectedTreePathArr = this.distinctSelectedTreePaths(selectedTreePathArr)
          this.rightTreePathArr = this.rightTreePathArr.filter((item) => {
            return selectedTreePathArr.indexOf(item) == -1
          })
        }
      }
      // 过滤出左右穿梭框数据
      this.dataFilter()
    },
    // 切换select选项（指定品类、指定供应商）时
    onChange(e) {
      if (e.itemData) {
        this.rangeType = e.itemData.value
        // 清空所有数据
        this.all_data = []
        this.left_data = []
        this.right_data = []
        this.rightIdArr = []
        this.firstTreeLevelTreePath = ''
        this.rightTreePathArr = []
        this.initData()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  flex: 1;
}
.content-arrow {
  &--btn {
    height: 30px;
    width: 30px;
    border: 2px solid #98aac3;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    color: #98aac3;
    margin: 10px;
    cursor: pointer;
  }
}
.transfer {
  display: flex;
  justify-content: center;
  align-items: center;
}
.transfer > .list {
  width: 709px;
  height: 640px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
  display: flex;
  flex-direction: column;
  .mt-data-grid {
    margin: 0 auto;
  }
  /deep/ .e-rowcell {
    text-align: left;
  }
}
.range-type {
  margin: 24px 0 16px 30px;
}
.accordion-title {
  float: left;
  margin-bottom: 16px;
  padding: 18px 0 18px 0;
  width: 100%;
  border-bottom: 1px solid rgba(232, 232, 232, 1);
  span {
    display: inline-block;
    border-left: 2px solid #00469c;
    text-indent: 10px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin-left: 10px;
  }
}
</style>
