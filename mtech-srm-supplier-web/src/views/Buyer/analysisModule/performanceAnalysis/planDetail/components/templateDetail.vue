// 计划清单详情-模板详情/模板维护
<template>
  <div class="detail-content">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { templateDetailColumnData } from '../config/index'
export default {
  props: {
    planDetail: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          // gridId:"610cc378-df3e-564e-01b5-826ad8bbac6f",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_edit',
                  title: this.$t('添加维度'),
                  visibleCondition: () => {
                    // planCreateType: 创建方式（1引用模板，2自定义模板）
                    // status计划状态  10 未提交 20已提交 30已驳回  40启用  50停用  60已过期
                    // console.log("planCreateType:", this.planDetail.planCreateType);
                    return (
                      this.planDetail.planCreateType == 2 &&
                      this.planDetail.status != 20 &&
                      this.planDetail.status != 40
                    )
                  }
                }
              ]
            ]
          },
          treeGrid: {
            height: 'calc(100vh - 460px)',
            allowPaging: false,
            clipMode: 'EllipsisWithTooltip',
            columnData: templateDetailColumnData,
            childMapping: 'childrenList',
            // dataSource: [],
            dataSource: []
          }
        }
      ],
      dimensionIdArr: [], // 维度id集合（同级唯一）
      indexNameArr: [], // 指标类名称集合（该维度下唯一）
      indexCodeArr: [] // 指标编码集合（唯一）
    }
  },
  created() {},
  mounted() {
    // console.log(11111,this.list)
    // 模板详情数据集合
    if (!this.planDetail.itemDTOList) this.planDetail.itemDTOList = []
    if (this.planDetail.itemDTOList.length > 0) {
      // 重组树形结构数据
      this.dataToTree(this.planDetail.itemDTOList, this.planDetail)
    }
    this.$nextTick(() => {
      this.pageConfig[0].treeGrid.dataSource = this.planDetail.itemDTOList
    })
  },
  methods: {
    // 重组树形结构数据
    dataToTree(arr = [], item) {
      arr.forEach((ele) => {
        ele['scoreTypes'] = item.scoreType || item.calcType
        ele['indexNames'] = item.indexName || ''
        ele['indexNameId'] = ele.dimensionName + ele.indexName || ''
        ele['planCreateType'] = item.planCreateType
        ele['assignPointss'] = item.assignPoints || 1
        ele['status'] = item.status
        if (ele.indexLevel == '1') {
          ele['name'] = ele.dimensionName
        } else {
          ele['name'] = ele.indexName
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.dataToTree(ele.childrenList, ele)
        }
      })
    },
    // 校验新添加的指标类名称是否重复（同级，指标类只会存在于第2级，判断该维度下的所有指标类）
    checkNewAddIndexNameIsRepeated(newIndexName) {
      return (
        this.indexNameArr.findIndex((indexName) => {
          return indexName === newIndexName
        }) != -1
      )
    },

    // 点击单元格文字
    handleClickCellTitle() {},
    // 表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.treeGrid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (e.toolbar.id == 'Add') {
        // 添加维度操作
        this.handleAddIndex()
        return
      }
    },
    // 点击单元格内按钮
    handleClickCellTool(e) {
      // console.log("use-handleClickCellTool", e);
      if (e.tool.id == 'Add' || e.tool.id == 'Add1') {
        // 添加指标（类）操作
        this.handleAddIndex(e)
      } else if (e.tool.id == 'Edit') {
        // 编辑维度|指标（类）操作
        this.handleEditDimension(e)
      } else if (e.tool.id == 'Delete') {
        // 删除维度|指标（类）操作
        this.handleDelIndex(e.data)
      }
    },
    handleEditDimension(e) {
      console.log('handleAddIndex---', e)
      let title = this.$t('编辑')
      let num = 1
      let showNum = 1
      let data = null
      let weightShow = false

      if (e) {
        num = Number(e.data.indexLevel)
        data = e.data
      }
      if (num == 1) {
        showNum = 1
      } else if (num == 2 && data.indexType == '1') {
        showNum = 2
      } else {
        showNum = 3
      }
      let list = {}
      let arr = []
      if (showNum == 1) {
        if (this.planDetail.calcType == 'weight') {
          if (this.planDetail.itemDTOList && this.planDetail.itemDTOList.length > 0) {
            arr = this.planDetail.itemDTOList
              .filter((item) => {
                return item.indexKind != 2
              })
              .map((item) => {
                return Number(item.scoreWeight) || 0
              })
          }
          weightShow = true
        } else {
          if (this.planDetail.itemDTOList && this.planDetail.itemDTOList.length > 0) {
            arr = this.planDetail.itemDTOList
              .filter((item) => {
                return item.indexKind != 2
              })
              .map((item) => {
                return Number(item.maxValue)
              })
          }
          weightShow = false
        }
      } else {
        this.planDetail.itemDTOList.forEach((ele) => {
          if (ele.dimensionId == data.dimensionId) {
            // let obj=ele
            if (data.indexLevel == 2) {
              list = ele
            } else {
              ele.childrenList.forEach((item) => {
                if (item.indexName == data.indexNames) {
                  list = item
                }
              })
            }
          }
        })
        if (data.scoreTypes == 'weight') {
          if (list.childrenList && list.childrenList.length > 0) {
            arr = list.childrenList
              .filter((item) => {
                return item.indexKind != 2
              })
              .map((item) => {
                return Number(item.scoreWeight)
              })
          }

          weightShow = true
        } else {
          if (list.childrenList && list.childrenList.length > 0) {
            arr = list.childrenList
              .filter((item) => {
                return item.indexKind != 2
              })
              .map((item) => {
                return Number(item.maxValue)
              })
          }
          weightShow = false
        }
      }
      let minweight = 0
      arr.forEach((ele) => {
        minweight = minweight + ele
      })
      if (!weightShow) {
        if (showNum == 1) {
          minweight = this.planDetail.fullScore - minweight + data.maxValue
        } else {
          minweight = list.maxValue - minweight + data.maxValue
        }
      } else {
        minweight = minweight - data.scoreWeight
      }
      if (data.scoreWeight) {
        data.scoreWeight = this.floatMul(data.scoreWeight, 100)
      }
      // if(data&&num!=1) {
      //   data['parentId']=data.id
      // }
      this.$dialog({
        modal: () => import('./targetDialog.vue'),
        data: {
          edit: true,
          title: title,
          num: num,
          showNum: showNum,
          data: data,
          planDetail: this.planDetail,
          weightShow: weightShow,
          minweight: minweight,
          orgId: this.planDetail.orgId
        },
        success: (query) => {
          let _query = { ...query, templateId: this.planDetail.templateId, indexLevel: num }
          _query.indexDescribe = _query.remark
          if (!_query.indexRange || _query.indexKind == 2) {
            _query.indexRange = _query.minValue + '~' + _query.maxValue
          }
          if (showNum != 3) {
            _query.indexRange = _query.minValue + '~' + _query.maxValue
          }
          if (!_query.indexKind) {
            _query.indexKind = 1
          }
          if (weightShow) {
            if (num == 1) {
              _query.assignPoints = Number(_query.scoreWeight) * Number(this.planDetail.fullScore)
            } else {
              if (_query.indexKind == 2) {
                _query.assignPoints = 0
              } else {
                _query.assignPoints = Number(_query.scoreWeight) * Number(data.assignPointss)
              }
            }
            if (_query.assignPoints) {
              _query.assignPoints = this.floatDiv(_query.assignPoints, 100)
            }
          } else {
            _query.assignPoints = Number(_query.maxValue)
          }
          if (_query.scoreWeight) {
            _query.scoreWeight = this.floatDiv(_query.scoreWeight, 100)
          }
          _query.assignPoints = Number(_query.assignPoints).toFixed(
            this.planDetail.keepDecimal ? this.planDetail.keepDecimal : 0
          )
          delete _query.childrenList
          delete _query.id
          this.$API.templateList.templateAddDetail(_query).then((res) => {
            // let lists=res.data
            let resData = res.data
            resData.childrenList = query.childrenList
            let indexNameId = resData.dimensionName + resData.indexName || ''
            this.diguiInit(this.planDetail.itemDTOList, indexNameId, resData)

            if (this.planDetail.itemDTOList.length > 0) {
              this.dataToTree(this.planDetail.itemDTOList, this.planDetail)
            }

            // this.init(res.data)
            // this.init()
            //
          })
        }
      })
    },
    diguiInit(arr, id, data) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].indexNameId == id) {
          this.$set(arr, i, data)
          // arr[i]=data
        } else {
          if (arr[i].childrenList && arr[i].childrenList.length > 0) {
            this.diguiInit(arr[i].childrenList, id, data)
          }
        }
      }
    },
    floatMul(a, b) {
      var c = 0,
        d = a.toString(),
        e = b.toString()
      try {
        c += d.split('.')[1].length
      } catch (f) {}
      try {
        c += e.split('.')[1].length
      } catch (f) {}
      return (Number(d.replace('.', '')) * Number(e.replace('.', ''))) / Math.pow(10, c)
    },
    floatDiv(a, b) {
      var c,
        d,
        e = 0,
        f = 0
      try {
        e = a.toString().split('.')[1].length
      } catch (g) {}
      try {
        f = b.toString().split('.')[1].length
      } catch (g) {}
      c = Number(a.toString().replace('.', ''))
      d = Number(b.toString().replace('.', ''))
      return this.floatMul(c / d, Math.pow(10, f - e))
    },
    // 添加维度|添加指标（类）操作
    handleAddIndex(e) {
      // console.log("handleAddIndex---", e);
      let title = this.$t('新增维度')
      let num = 1
      let showNum = 1
      let data = null
      let weightShow = false
      let parentIndexName = '' // 指标类下添加指标时，用于记录指标类名称，判断添加的位置

      if (e) {
        title = e.tool.title
        num = Number(e.data.indexLevel) + 1
        data = e.data
      } else {
        num = 1
        data = this.planDetail
      }
      if (title == this.$t('新增维度')) {
        showNum = 1
      } else if (title == this.$t('添加指标类')) {
        showNum = 2
      } else {
        showNum = 3
        // indexLevel	层级（1一级，2二级 3三级）
        if (e.data.indexLevel == 2) {
          // 指标类下添加指标时，记录指标类名称，判断添加的位置
          parentIndexName = e.data.indexName
        }
      }
      let arr = []
      if (showNum == 1) {
        if (this.planDetail.calcType == 'weight') {
          if (this.planDetail.itemDTOList && this.planDetail.itemDTOList.length > 0) {
            arr = this.planDetail.itemDTOList
              .filter((item) => {
                return item.indexKind != 2
              })
              .map((item) => {
                return Number(item.scoreWeight)
              })
          }
          weightShow = true
        } else {
          if (this.planDetail.itemDTOList && this.planDetail.itemDTOList.length > 0) {
            arr = this.planDetail.itemDTOList
              .filter((item) => {
                return item.indexKind != 2
              })
              .map((item) => {
                return Number(item.maxValue)
              })
          }
          weightShow = false
        }
      } else {
        if (data.scoreType == 'weight') {
          if (data.childrenList && data.childrenList.length > 0) {
            arr = data.childrenList
              .filter((item) => {
                return item.indexKind != 2
              })
              .map((item) => {
                return Number(item.scoreWeight)
              })
          }

          weightShow = true
        } else {
          if (data.childrenList && data.childrenList.length > 0) {
            arr = data.childrenList
              .filter((item) => {
                return item.indexKind != 2
              })
              .map((item) => {
                return Number(item.maxValue)
              })
          }
          weightShow = false
        }
      }
      let minweight = 0
      arr.forEach((ele) => {
        minweight = minweight + ele
      })
      if (!weightShow) {
        if (showNum == 1) {
          minweight = this.planDetail.fullScore - minweight
        } else {
          minweight = data.maxValue - minweight
        }
      }
      if (data) {
        data['parentId'] = data.id
      }
      console.log(showNum)

      this.$dialog({
        modal: () => import('./targetDialog.vue'),
        data: {
          planDetail: this.planDetail,
          title: title,
          num: num,
          showNum: showNum,
          data: data,
          weightShow: weightShow,
          minweight: minweight,
          orgId: this.planDetail.orgId
        },
        success: (query) => {
          let _query = { ...query, templateId: this.planDetail.templateId, indexLevel: num }
          _query.indexDescribe = _query.remark
          if (!_query.indexKind) {
            _query.indexKind = 1
          }
          if (!_query.indexRange || _query.indexKind == 2) {
            _query.indexRange = _query.minValue + '~' + _query.maxValue
          }
          if (weightShow) {
            if (num == 1) {
              _query.assignPoints = Number(_query.scoreWeight) * Number(this.planDetail.fullScore)
            } else {
              if (_query.indexKind == 2) {
                _query.assignPoints = 0
              } else {
                _query.assignPoints = Number(_query.scoreWeight) * Number(data.assignPoints)
              }
            }
            if (_query.assignPoints) {
              _query.assignPoints = this.floatDiv(_query.assignPoints, 100)
            }
          } else {
            _query.assignPoints = Number(_query.maxValue)
          }
          _query.assignPoints = Number(_query.assignPoints).toFixed(
            this.planDetail.keepDecimal ? this.planDetail.keepDecimal : 0
          )
          if (_query.scoreWeight) {
            _query.scoreWeight = this.floatDiv(_query.scoreWeight, 100)
          }
          // _query.assignPoints = _query.assignPoints / 100
          delete _query.childrenList
          delete _query.id
          console.log(_query)
          this.$API.planDetail.templateAddDetail(_query).then((res) => {
            // console.log(res)
            // showNum: 1新增|删除维度 2添加|删除指标类 2添加|删除指标
            // data：当前要添加的数据
            let data = res.data
            if (showNum == 1) {
              // 新增维度
              if (!data['childrenList']) {
                data['childrenList'] = []
              }
              this.planDetail.itemDTOList.push(data)
            } else if (showNum == 2) {
              // 添加指标类（添加到维度下）
              if (!data['childrenList']) {
                data['childrenList'] = []
              }
              for (let i = 0; i < this.planDetail.itemDTOList.length; i++) {
                const item = this.planDetail.itemDTOList[i]
                if (item.dimensionId == data.dimensionId) {
                  if (!this.planDetail.itemDTOList[i].childrenList) {
                    this.planDetail.itemDTOList[i].childrenList = []
                  }
                  this.planDetail.itemDTOList[i].childrenList.push(data)
                  // 找到满足条件的目标后就结束循环（内外）
                  // return;
                  // return跳出for循环后，之后的代码也不会执行了，所以换break
                  break
                }
              }
            } else {
              // 添加指标（判断是添加到维度下，还是添加到指标类下）
              // indexLevel	层级（1一级，2二级 3三级）
              if (data.indexLevel == 2) {
                // 添加到维度下
                for (let i = 0; i < this.planDetail.itemDTOList.length; i++) {
                  const item = this.planDetail.itemDTOList[i]
                  if (item.dimensionId == data.dimensionId) {
                    this.planDetail.itemDTOList[i].childrenList.push(data)
                    // 找到满足条件的目标后就结束循环（内外）
                    // return;
                    // return跳出for循环后，之后的代码也不会执行了，所以换break
                    break
                  }
                }
              } else {
                // 添加到指标类下
                for (let i = 0; i < this.planDetail.itemDTOList.length; i++) {
                  const item = this.planDetail.itemDTOList[i]
                  if (item.dimensionId == data.dimensionId) {
                    for (let j = 0; j < item.childrenList.length; j++) {
                      const obj = item.childrenList[j]
                      if (obj.indexName == parentIndexName) {
                        this.planDetail.itemDTOList[i].childrenList[j].childrenList.push(data)
                        // 找到满足条件的目标后就结束循环（内外）
                        // return;
                        // return跳出for循环后，之后的代码也不会执行了，所以换break
                        break
                      }
                    }
                  }
                }
              }
            }
            if (this.planDetail.itemDTOList.length > 0) {
              this.dataToTree(this.planDetail.itemDTOList, this.planDetail)
            }
          })
        }
      })
    },
    // 删除维度|指标（类）操作
    handleDelIndex(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          // indexLevel	层级（1一级，2二级 3三级）
          if (data.indexLevel == 1) {
            // 删除维度
            // this.planDetail.itemDTOList = this.planDetail.itemDTOList.filter(item => {
            //   return item.dimensionId != data.dimensionId;
            // });
            // filter会改变指针空间地址，push,splice是在原本的上面改变
            for (let i = 0; i < this.planDetail.itemDTOList.length; i++) {
              const item = this.planDetail.itemDTOList[i]
              if (item.dimensionId == data.dimensionId) {
                this.planDetail.itemDTOList.splice(i, 1)
                // 找到满足条件的目标后就结束循环（内外）
                // return;
                // return跳出for循环后，之后的代码也不会执行了，所以换break
                break
              }
            }
          } else if (data.indexLevel == 2) {
            // 删除指标（类）
            for (let i = 0; i < this.planDetail.itemDTOList.length; i++) {
              const item = this.planDetail.itemDTOList[i]
              if (item.dimensionId == data.dimensionId) {
                for (let j = 0; j < item.childrenList.length; j++) {
                  const obj = item.childrenList[j]
                  // indexType	指标类型（1指标类, 2指标）
                  var str = data.indexType == 1 ? 'indexName' : 'indexCode'
                  if (obj[str] == data[str]) {
                    this.planDetail.itemDTOList[i].childrenList.splice(j, 1)
                    // 找到满足条件的目标后就结束循环（内外）
                    // return;
                    // return跳出for循环后，之后的代码也不会执行了，所以换break
                    break
                  }
                }
              }
            }
          } else {
            // 删除指标（指标类下，三级）
            for (let i = 0; i < this.planDetail.itemDTOList.length; i++) {
              const item = this.planDetail.itemDTOList[i]
              if (item.dimensionId == data.dimensionId) {
                for (let j = 0; j < item.childrenList.length; j++) {
                  const obj = item.childrenList[j]
                  // indexType	指标类型（1指标类, 2指标）
                  if (obj.indexType == 1) {
                    for (let k = 0; k < obj.childrenList.length; k++) {
                      const ele = obj.childrenList[k]
                      if (ele.indexCode == data.indexCode) {
                        this.planDetail.itemDTOList[i].childrenList[j].childrenList.splice(k, 1)
                        // 找到满足条件的目标后就结束循环（内外）
                        // return;
                        // return跳出for循环后，之后的代码也不会执行了，所以换break
                        break
                      }
                    }
                  }
                }
              }
            }
          }

          this.$toast({
            content: this.$t('删除成功'),
            type: 'success'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-content {
  background: #e8e8e8;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  ::v-deep .common-template-page .repeat-template {
    height: 100%;
  }
  ::v-deep .grid-container {
    height: calc(100% - 50px);
  }
  /deep/ .e-treecolumn-container {
    display: flex !important;
    align-items: center;
    align-content: center;
    overflow: hidden;
  }
  /deep/.e-grid {
    .e-rowcell {
      text-align: left !important;
      .grid-edit-column {
        display: block !important;
        span {
          display: block !important;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .icon-title {
          display: inline-block !important;
        }
      }
    }
  }
  /deep/.tree-title-bold {
    font-weight: bold;
  }
}
</style>
