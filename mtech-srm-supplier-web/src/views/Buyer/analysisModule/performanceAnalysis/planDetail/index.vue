// 计划清单详情
<template>
  <div class="full-height" :style="currentTabIndex === 0 && planDetail ? 'overflow:hidden;' : ''">
    <div class="top-info" v-if="planDetail">
      <div class="header">
        <!-- 左侧的信息 -->
        <div class="plan-title">{{ planDetail.planName || '' }}</div>
        <!-- status计划状态  10 未提交 20已提交 30已驳回  40启用  50停用  60已过期 -->
        <div :class="['status-box', 'status-box' + '-' + planDetail.status]">
          <span v-if="planDetail.status == 10">{{ $t('未提交') }}</span>
          <span v-if="planDetail.status == 20">{{ $t('已提交') }}</span>
          <span v-if="planDetail.status == 30">{{ $t('已驳回') }}</span>
          <span v-if="planDetail.status == 40">{{ $t('启用') }}</span>
          <span v-if="planDetail.status == 50">{{ $t('停用') }}</span>
          <span v-if="planDetail.status == 60">{{ $t('已过期') }}</span>
        </div>
        <!-- approveStatus计划审批状态 10 未提交  20 待审核 30 已审批  40 审核中 -->
        <div class="approve-status-box">
          <!-- <span
            :class="[
              'approve-status-dot',
              'approve-status-dot' + '-' + planDetail.approveStatus,
            ]"
          ></span> -->
          <span v-if="planDetail.approveStatus == 10">{{ $t('未提交') }}</span>
          <span v-if="planDetail.approveStatus == 20" @click="examine">{{ $t('待审核') }}</span>
          <span v-if="planDetail.approveStatus == 30" @click="examine">{{ $t('已审批') }}</span>
          <span v-if="planDetail.approveStatus == 40" @click="examine">{{ $t('审核中') }}</span>
        </div>

        <div class="middle-blank"></div>

        <!-- 右侧操作按钮 -->
        <mt-button v-if="!type" css-class="e-flat" :is-primary="true" @click="goBack(0)">{{
          $t('返回')
        }}</mt-button>
        <mt-button
          v-if="planDetail.status != 20 && planDetail.status != 40"
          css-class="e-flat"
          :is-primary="true"
          @click="goBack(1)"
          >{{ $t('保存') }}</mt-button
        >
        <mt-button
          v-if="planDetail.status != 20 && planDetail.status != 40 && planDetail.status != 50"
          css-class="e-flat"
          :is-primary="true"
          @click="goBack(2)"
          >{{ $t('保存并提交') }}</mt-button
        >
      </div>
      <div class="middle">
        <span>{{ $t('计划编码') }}：{{ planDetail.planCode }}</span>
        <span
          >{{ $t('创建人') }}：{{ planDetail.createUserName
          }}<span style="margin-left: 8px">{{ planDetail.createTime }}</span></span
        >
        <span>{{ $t('分析类型') }}：{{ analysisList[planDetail.planType] }}</span>
      </div>
      <div class="bottom">
        <span>{{ $t('计划策略') }}：{{ planCycleList[planDetail.planCycle] }}</span>
        <!-- <span>{{ $t("适用类型") }}：{{ applicationType }}</span> -->
        <!-- <span
          >{{ $t("有效期") }}：{{ planDetail.planStartTime || "--"
          }}<span style="margin: 0 8px">{{ $t("至") }}</span
          >{{ planDetail.planEndTime || "--" }}</span
        > -->
        <span
          >{{ $t('所属公司') }}：{{ planDetail.orgName
          }}<span class="orgCode" @click="goOrgDetail()">
            {{ planDetail.orgCode }}
          </span>
        </span>
      </div>
    </div>
    <mt-tabs
      v-if="planDetail"
      :tab-id="tabId"
      :e-tab="false"
      :data-source="pageConfig"
      @handleSelectTab="handleSelectTab"
    >
    </mt-tabs>
    <!-- 模板详情/模板维护 -->
    <div :style="currentTabIndex === 0 && planDetail ? 'height: calc(100% - 230px)' : ''">
      <template-detail
        ref="template1"
        v-if="planDetail && currentTabIndex === 0"
        :plan-detail.sync="planDetail"
      ></template-detail>
      <!-- 模板适用范围 -->
      <template-range
        ref="template2"
        v-if="planDetail && planCreateType == 2 && currentTabIndex === 1"
        :form="planDetail"
      ></template-range>
      <!-- 计划适用范围 -->
      <plan-range
        ref="template3"
        v-if="planDetail && planCreateType == 1 && currentTabIndex === 1"
        :form="planDetail"
      ></plan-range>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    templateDetail: require('./components/templateDetail.vue').default,
    templateRange: require('./components/templateRange.vue').default,
    planRange: require('./components/planRange.vue').default
  },
  // props: {
  //   planDetail: {
  //     type: Object,
  //     default: () => {},
  //   },
  // },
  data() {
    return {
      analysisList: {
        1: this.$t('绩效分析'),
        2: this.$t('风险分析')
      },
      planCycleList: {
        0: this.$t('年度'),
        1: this.$t('半年度'),
        2: this.$t('季度'),
        3: this.$t('月度')
      },
      currentTabIndex: 0, //当前Tab索引
      planDetail: null, // 计划详情
      tabId: 'planDetail', // 用于区分多个自适应tabs
      planCreateType: 0, // 创建方式（1引用模板，2自定义模板）
      pageConfig: [
        { title: this.$t('模板详情/模板维护') }
        // { title: i18n.t("模板适用范围") },
        // { title: i18n.t("计划适用范围") },
      ],
      id: '',
      // 用于保存tab切换数据
      rangeType: 0,
      rangeData: [],
      type: false
    }
  },
  mounted() {
    // console.log(this.$route);
    this.id = this.$route.query.id
    this.type = this.$route.query.type == 1 ? true : false
    this.init()
  },
  computed: {
    applicationType() {
      // planCreateType 创建方式（1引用模板，2自定义模板）
      /* 1. 引用模板（产品场景）: 隐藏模板适用范围tab。维护计划适用范围;
          2. 自定义模板（TCL场景）：维护模板适用范围。隐藏计划适用范围tab，计划适用范围取自分析设置/品类策略； */
      if (this.planCreateType == 1) {
        // rangeType 0哪种也不是 1指定品类 2指定供应商
        if (this.rangeType == 2) {
          return this.$t('指定供应商')
        } else {
          return this.$t('指定品类')
        }
      } else if (this.planCreateType == 2) {
        // rangeType 适用品类类型（0通用，1部分品类，2排除品类）
        if (this.rangeType == 1) {
          return this.$t('部分品类')
        } else if (this.rangeType == 2) {
          return this.$t('排除品类')
        } else {
          return this.$t('通用')
        }
      }
      return this.$t('通用')
    }
  },
  methods: {
    examine() {
      this.$dialog({
        modal: () => import('./components/approvalDialog.vue'),
        data: {
          processInstanceId: this.planDetail.processInstanceId
        },
        success: () => {}
      })
    },
    init() {
      // 绩效分析-查询模板详情
      this.$API.planDetail.planTplDetail({ id: this.id }).then((res) => {
        // console.log(res);
        let { data } = res
        // 计划详情
        this.planDetail = data
        if (data.planStartTime) {
          this.planDetail.planStartTime = data.planStartTime.split(' ')[0]
        }
        if (data.planEndTime) {
          this.planDetail.planEndTime = data.planEndTime.split(' ')[0]
        }
        // planCreateType 创建方式（1引用模板，2自定义模板）
        this.planCreateType = this.planDetail.planCreateType
        /* 1. 引用模板（产品场景）: 隐藏模板适用范围tab。维护计划适用范围;
           2. 自定义模板（TCL场景）：维护模板适用范围。隐藏计划适用范围tab，计划适用范围取自分析设置/品类策略； */
        if (this.planCreateType == 1) {
          this.pageConfig = [
            { title: this.$t('模板详情/模板维护') },
            { title: this.$t('计划适用范围') }
          ]
        } else if (this.planCreateType == 2) {
          this.pageConfig = [
            { title: this.$t('模板详情/模板维护') },
            { title: this.$t('模板适用范围') }
          ]
        }

        // 初始化其它tab数据
        this.handleTabData()
      })
    },
    // 保存|保存并提交|返回按钮
    goBack(num) {
      // 1保存（暂存）| 2保存并提交
      if (num == 1 || num == 2) {
        this.$loading()
        // 处理tab切换数据
        this.handleTabData()
        let arr = []
        let query = {}
        // planCreateType: 创建方式（1引用模板，2自定义模板）
        if (this.planCreateType == 2) {
          query['id'] = this.planDetail.templateId // 模板id

          this.digui(arr, this.rangeData, this.rangeType)
          if (!arr.length && this.rangeType != 0) {
            this.$toast({
              content: this.$t('请选择适用品类'),
              type: 'error'
            })
            this.$hloading()
            return
          }
          query['rangeDTOList'] = arr
          query['rangeType'] = this.rangeType
        } else if (this.planCreateType == 1) {
          query['id'] = this.planDetail.id // 计划id

          this.digui(arr, this.rangeData, this.rangeType)
          // rangeType 0哪种也不是 1指定品类 2指定供应商
          if (this.rangeType == 1) {
            if (!arr.length) {
              this.$toast({
                content: this.$t('请选择适用品类'),
                type: 'error'
              })
              this.$hloading()
              return
            }
            query['categoryRangeDTOList'] = arr
          } else {
            if (!arr.length) {
              this.$toast({
                content: this.$t('请选择适用供应商'),
                type: 'error'
              })
              this.$hloading()
              return
            }
            query['supplierRangeDTOList'] = arr
          }
        } else {
          this.$hloading()
          return
        }

        // 模板详情
        query['templateItemList'] = this.planDetail.itemDTOList

        // planCreateType: 创建方式（1引用模板，2自定义模板）
        let url = ''
        if (num == 1) {
          url =
            this.planCreateType == 1
              ? this.$API.planDetail.templateAddRange(query)
              : this.$API.planDetail.templateAddItemAndRange(query)
        } else if (num == 2) {
          query.planId = this.planDetail.id
          url =
            this.planCreateType == 1
              ? this.$API.planDetail.saveRangeAndSubmit(query)
              : this.$API.planDetail.saveAndSubmitDetail(query)
        }
        url
          .then((res) => {
            this.$hloading()
            if (res && res.code === 200) {
              this.$toast({
                content: this.$t('保存成功'),
                type: 'success'
              })
              if (num == 1) {
                // 刷新
                // this.init();
              } else if (num == 2) {
                // 返回
                this.back()
              }
            } else {
              this.$toast({
                content: res.msg || this.$t('保存失败，请重试'),
                type: 'error'
              })
            }
          })
          .catch((error) => {
            this.$hloading()
            this.$toast({
              content: error.msg || this.$t('保存失败，请重试'),
              type: 'error'
            })
          })
      } else {
        this.back()
      }
    },
    digui(arr, data, rangeType) {
      data.forEach((ele) => {
        // console.log(ele);
        if (ele.childrens && ele.childrens.length > 0) {
          this.digui(arr, ele.childrens, rangeType)
        } else {
          let obj = {}
          // planCreateType: 创建方式（1引用模板，2自定义模板）
          if (this.planCreateType == 1) {
            // rangeType 0哪种也不是 1指定品类 2指定供应商
            if (rangeType == 1) {
              obj = {
                categoryCode: ele.categoryCode,
                id: ele.id,
                categoryName: ele.categoryName
              }
            } else {
              obj = {
                supplierEnterpriseId: ele.supplierEnterpriseId,
                supplierEnterpriseCode: ele.supplierEnterpriseCode,
                supplierCode: ele.supplierCode,
                supplierEnterpriseName: ele.supplierEnterpriseName
              }
            }
          } else {
            obj = {
              categoryCode: ele.categoryCode,
              categoryId: ele.id,
              categoryName: ele.categoryName,
              // "id": 0,
              rangeType: rangeType,
              // "remark": "",
              templateId: this.planDetail.templateId
              // "tenantId": 0
            }
          }

          arr.push(obj)
        }
      })
    },
    // 顶部-返回
    back() {
      this.$router.push({
        path: '/supplier/performance-analysis'
      })
    },
    // 切换Tab页
    handleSelectTab(e) {
      // console.log("handleSelectTab", e);
      // 处理tab切换数据
      this.handleTabData()
      this.currentTabIndex = e
    },
    // 处理tab切换数据
    handleTabData() {
      if (this.planCreateType == 2) {
        if (this.$refs.template2) {
          this.rangeType = this.$refs.template2.rangeType
          this.rangeData = JSON.parse(JSON.stringify(this.$refs.template2.new_info))
        } else {
          this.rangeType = this.planDetail.rangeType
          this.rangeData = JSON.parse(JSON.stringify(this.planDetail.rangeDTOList || []))
        }
      } else if (this.planCreateType == 1) {
        if (this.$refs.template3) {
          this.rangeType = this.$refs.template3.rangeType
          this.rangeData = JSON.parse(JSON.stringify(this.$refs.template3.right_data))
        } else {
          // designatedCategory是否指定品类 Y N; designatedSupplier是否指定供应商 Y N
          // rangeType 0哪种也不是 1指定品类 2指定供应商
          this.rangeType = this.planDetail
            ? this.planDetail.designatedCategory == 'Y'
              ? 1
              : this.planDetail.designatedSupplier == 'Y'
              ? '2'
              : 1
            : 1
          if (this.rangeType == 1) {
            this.rangeData = JSON.parse(JSON.stringify(this.planDetail.categoryRangeDTOList || []))
          } else {
            this.rangeData = JSON.parse(JSON.stringify(this.planDetail.supplierRangeDTOList || []))
          }
        }
      }
    },
    // 跳转所属组织详情页面
    goOrgDetail() {
      // 跳转所属组织详情页面
      // this.$router.push({
      //   path: "/supplier/performance-analysis-plan-detail",
      //   query: {
      //     // id: data.id
      //     id: '1493770941088468993'
      //   },
      // });
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: calc(100% - 29px);
  display: flex;
  flex-direction: column;
  padding: 0 20px;
}
.mt-tabs {
  width: 100%;
  background-color: #fafafa;
  flex-shrink: 0;
}
.plan-title {
  font-size: 20px;
  font-family: PingFangSC;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
}
.middle-blank {
  flex: 1;
}
.top-info {
  width: 100%;
  height: 132px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  display: flex;
  flex-direction: column;
  padding: 26px 0 28px 30px;
  margin: 20px 0 25px 0;
  .status-box {
    line-height: 12px;
    padding: 4px;
    border-radius: 2px;
    margin: 0 28px 0 16px;
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: #6386c1;
    background: rgba(99, 134, 193, 0.1);
    &-10,
    &-20,
    &-40 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
    &-30,
    &-50,
    &-60 {
      color: #9a9a9a;
      background: rgba(154, 154, 154, 0.1);
    }
  }
  .approve-status-box {
    cursor: pointer;
    color: #00469c;
    .approve-status-dot {
      display: inline-block;
      margin-right: 4px;
      margin-bottom: 1px;
      width: 8px;
      height: 8px;
      border-radius: 4px;
      &-10 {
        background: #eda133;
      }
      &-20 {
        background: #6386c1;
      }
      &-30 {
        background: #e8e8e8;
      }
      &-40 {
        background: #8acc40;
      }
    }
  }
  .header {
    width: 100%;
    height: 20px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .middle {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin: 10px 0 20px 0;
    span {
      margin-right: 20px;
    }
  }
  .bottom {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    span {
      margin-right: 24px;
    }
    .orgCode {
      // margin-left: 8px;
      color: #00469c;
      cursor: pointer;
    }
  }
}
</style>
