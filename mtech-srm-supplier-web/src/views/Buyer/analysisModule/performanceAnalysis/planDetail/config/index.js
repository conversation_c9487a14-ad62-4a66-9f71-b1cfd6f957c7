import { i18n } from '@/main.js'
// 计划清单详情-模板详情/模板维护
import Vue from 'vue'
export const templateDetailColumnData = [
  {
    width: 270,
    field: 'name',
    headerText: i18n.t('维度/指标'),
    cssClass: (data) => {
      //自定义Title显示样式，根据逻辑，控制不同的样式
      // indexLevel	层级（1一级，2二级 3三级）
      if (data.indexLevel == 1) {
        return 'tree-title-bold'
      }
      return ''
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_card_plus',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.planCreateType == 2 && data.status != 20 && data.status != 40
        }
      },
      {
        id: 'Add',
        icon: 'icon_card_plus',
        title: i18n.t('添加指标类'),
        visibleCondition: (data) => {
          // planCreateType: 创建方式（1引用模板，2自定义模板）
          // status计划状态  10 未提交 20已提交 30已驳回  40启用  50停用  60已过期
          return (
            data['indexLevel'] == '1' &&
            data.planCreateType == 2 &&
            data.status != 20 &&
            data.status != 40
          )
        }
      },
      {
        id: 'Add1',
        icon: 'icon_card_plus',
        title: i18n.t('添加指标'),
        visibleCondition: (data) => {
          // indexType	指标类型（1指标类, 2指标）
          return (
            ['1', '2'].includes(data['indexLevel']) &&
            data['indexType'] != 2 &&
            data.planCreateType == 2 &&
            data.status != 20 &&
            data.status != 40
          )
        }
      },
      {
        id: 'Delete',
        icon: 'icon_list_delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data.planCreateType == 2 && data.status != 20 && data.status != 40
        }
      }
    ]
  },
  {
    field: 'indexType',
    headerText: i18n.t('类型'),
    template: () => {
      return {
        template: Vue.component('indexType', {
          template: `<span><span style="font-weight:bold;" v-if="indexLevel==1">{{indexType}}</span><span style="font-weight:normal;" v-else>{{indexType}}</span></span>`,
          data: function () {},
          computed: {
            // indexLevel	层级（1一级，2二级 3三级）
            indexLevel() {
              return this.data.indexLevel
            },
            indexType() {
              // indexType	指标类型（1指标类, 2指标）
              if (this.data.indexType) {
                if (this.data.indexType == 2) {
                  return i18n.t('指标')
                } else {
                  return i18n.t('指标类')
                }
              } else {
                return i18n.t('维度')
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'scoreType',
    headerText: i18n.t('取值方式'),
    template: () => {
      return {
        template: Vue.component('scoreType', {
          template: `<span><span style="font-weight:bold;" v-if="indexLevel==1">{{scoreType}}</span><span style="font-weight:normal;" v-else>{{scoreType}}</span></span>`,
          data: function () {
            return {
              map: {
                sum: i18n.t('子项求和'),
                weight: i18n.t('权重式计算'),
                max: i18n.t('子项最高值'),
                min: i18n.t('子项最低值'),
                average: i18n.t('子项平均值'),
                1: i18n.t('公式'),
                2: i18n.t('手动'),
                3: i18n.t('接口调用')
              }
            }
          },
          computed: {
            // indexLevel	层级（1一级，2二级 3三级）
            indexLevel() {
              return this.data.indexLevel
            },
            scoreType() {
              return this.map[this.data.scoreType]
            }
          }
        })
      }
    }
  },
  {
    field: 'oneVeto',
    headerText: i18n.t('最低分一票否决'),
    template: () => {
      return {
        template: Vue.component('oneVeto', {
          template: `<span><span style="font-weight:bold;" v-if="indexLevel==1">{{oneVeto}}</span><span style="font-weight:normal;" v-else>{{oneVeto}}</span></span>`,
          data: function () {
            return {
              map: {
                0: i18n.t('否'),
                1: i18n.t('是')
              }
            }
          },
          computed: {
            // indexLevel	层级（1一级，2二级 3三级）
            indexLevel() {
              return this.data.indexLevel
            },
            oneVeto() {
              return this.map[this.data.oneVeto]
            }
          }
        })
      }
    }
  },
  {
    field: 'scoreWeight',
    headerText: i18n.t('权重值'),
    template: () => {
      return {
        template: Vue.component('scoreWeight', {
          template: `<div >
              <span v-if="data.indexLevel==1" style="font-weight:bold;">{{data.scoreWeight||'-'}}</span>
              <span v-else >{{data.scoreWeight||'-'}}</span>
          </div>`,
          data: function () {
            return {}
          }
        })
      }
    }
  },
  {
    field: 'maxValue',
    headerText: i18n.t('满分值'),
    template: () => {
      return {
        template: Vue.component('maxValue', {
          template: `<div >
              <span v-if="data.indexLevel==1" style="font-weight:bold;">{{data.maxValue}}</span>
              <span v-else >{{data.maxValue}}</span>
          </div>`,
          data: function () {
            return {}
          }
        })
      }
    }
  },
  {
    field: 'indexRange',
    headerText: i18n.t('评分范围'),
    template: () => {
      return {
        template: Vue.component('indexRange', {
          template: `<span><span style="font-weight:bold;" v-if="indexLevel==1">{{indexRange}}</span><span style="font-weight:normal;" v-else>{{indexRange}}</span></span>`,
          data: function () {},
          computed: {
            // indexLevel	层级（1一级，2二级 3三级）
            indexLevel() {
              return this.data.indexLevel
            },
            indexRange() {
              return this.data.indexRange
            }
          }
        })
      }
    }
  },
  {
    field: 'assignPoints',
    headerText: i18n.t('分配分值'),
    template: () => {
      return {
        template: Vue.component('assignPoints', {
          template: `<span><span style="font-weight:bold;" v-if="indexLevel==1">{{assignPoints}}</span><span style="font-weight:normal;" v-else>{{assignPoints}}</span></span>`,
          data: function () {},
          computed: {
            // indexLevel	层级（1一级，2二级 3三级）
            indexLevel() {
              return this.data.indexLevel
            },
            assignPoints() {
              return this.data.assignPoints
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('描述'),
    template: () => {
      return {
        template: Vue.component('remark', {
          template: `<span><span style="font-weight:bold;" v-if="indexLevel==1">{{remark}}</span><span style="font-weight:normal;" v-else>{{remark}}</span></span>`,
          data: function () {},
          computed: {
            // indexLevel	层级（1一级，2二级 3三级）
            indexLevel() {
              return this.data.indexLevel
            },
            remark() {
              return this.data.remark
            }
          }
        })
      }
    }
  }
]

// 计划清单详情-模板适用范围 || 计划清单详情-计划适用范围-指定品类
export const planRangeColumnDataCate = [
  // {
  //   width: "100",
  //   type: "checkbox",
  //   // showCheckbox:true
  // },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

// 计划清单详情-计划适用范围-指定供应商
export const planRangeColumnDataSupplier = [
  // {
  //   width: "100",
  //   type: "checkbox",
  //   // showCheckbox:true
  // },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  }
]
