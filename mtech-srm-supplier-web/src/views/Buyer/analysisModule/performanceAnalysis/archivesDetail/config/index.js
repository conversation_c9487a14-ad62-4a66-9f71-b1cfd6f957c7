import Vue from 'vue'
import { i18n } from '@/main.js'
// 档案清单详情-结果应用情况
export const resultApplyStatusColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cellTools: []
  },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'compositeScore',
    headerText: i18n.t('得分'),
    template: () => {
      return {
        template: Vue.component('score', {
          template: `<div style="text-align:left">{{sourceOrgCode}}
          <p class="cell-operable-title" @click="showScoreDetail">{{ i18nTitle }}</p></div>`,
          data: function () {},
          computed: {
            sourceOrgCode() {
              return this.data.compositeScore
            },
            i18nTitle() {
              return i18n.t('得分明细')
            }
          },
          methods: {
            showScoreDetail() {
              this.$dialog({
                modal: () =>
                  import(
                    '@/views/Buyer/analysisModule/performanceAnalysis/archivesDetail/components/scoreDetailDialog.vue'
                  ),
                data: {
                  lineDate: this.data
                }
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'scoreRank',
    headerText: i18n.t('品类排名')
  },
  {
    field: 'comprehensiveLight',
    width: '100',
    headerText: i18n.t('综合灯'),
    template: () => {
      return {
        template: Vue.component('comprehensiveLight', {
          template: `
          <div>
          <div style="width:14px;height:14px;border-radius:50%;" :class="{green:data.comprehensiveLight==='2',red:data.comprehensiveLight==='0',yellow:data.comprehensiveLight==='1'}"></div>
          </div>`,
          data: function () {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'qualityLight',
    width: '100',
    headerText: i18n.t('质量灯'),
    template: () => {
      return {
        template: Vue.component('quality', {
          template: `
          <div>
            <div style="width:14px;height:14px;border-radius:50%;" :class="{green:data.qualityLight==='5',red:data.qualityLight==='3',yellow:data.qualityLight==='4'}"></div>
          </div>`,
          data: function () {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'isNotice',
    headerText: i18n.t('是否观察期'),
    template: () => {
      return {
        template: Vue.component('isNotice', {
          template: `<div>{{data.isNotice==6? i18nYes: i18nNo}}</div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            i18nYes() {
              return i18n.t('是')
            },
            i18nNo() {
              return i18n.t('否')
            }
          }
        })
      }
    }
  },
  {
    field: 'redLightCount',
    headerText: i18n.t('三个月内红灯次数')
  },
  {
    field: 'handlingSuggestionsView',
    headerText: i18n.t('处理建议'),
    template: () => {
      return {
        template: Vue.component('handlingSuggestions', {
          template: `<div>{{handlingSuggestions}}</div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            handlingSuggestions() {
              let handlingSuggestions = this.data.handlingSuggestionsView || '-'
              return handlingSuggestions
            }
          }
        })
      }
    }
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'issueNum',
    headerText: i18n.t('期号')
  },
  {
    field: 'createTime',
    headerText: i18n.t('结果生成日期'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>{{data.createTime.substring(0,10)}}<br>{{data.createTime.substring(11,19)}}</div>`,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  }
  // {
  //   field: "idfour",
  //   headerText: i18n.t("详情"),
  // },
]

// 档案清单详情-结果应用情况-详情弹窗
export const scoreDialogColumnData = [
  {
    width: 100,
    field: 'indexName',
    headerText: i18n.t('维度/指标'),
    clipMode: 'EllipsisWithTooltip',
    cssClass: (data) => {
      if (data.parentTemplateItemId == 0) {
        return 'tree-title-bold'
      }
      return ''
    }
  },
  {
    width: '100',
    field: 'score',
    headerText: i18n.t('指标得分'),
    template: () => {
      return {
        template: Vue.component('score', {
          template: `<span><span style="font-weight:bold;" v-if="parentTemplateItemId==0">{{Number(score).toFixed(2)}}</span><span style="font-weight:normal;" v-else>{{Number(score).toFixed(2)}}</span></span>`,
          data: function () {},
          computed: {
            parentTemplateItemId() {
              return this.data.parentTemplateItemId
            },
            score() {
              return this.data.score
            }
          }
        })
      }
    }
  },
  {
    width: '70',
    field: 'fullScore',
    headerText: i18n.t('满分'),
    template: () => {
      return {
        template: Vue.component('fullScore', {
          template: `<span><span style="font-weight:bold;" v-if="parentTemplateItemId==0">{{fullScore}}</span><span style="font-weight:normal;" v-else>{{fullScore}}</span></span>`,
          data: function () {},
          computed: {
            parentTemplateItemId() {
              return this.data.parentTemplateItemId
            },
            fullScore() {
              return this.data.fullScore
            }
          }
        })
      }
    }
  },
  {
    width: '70',
    field: 'scoreRank',
    headerText: i18n.t('排名'),
    template: () => {
      return {
        template: Vue.component('scoreRank', {
          template: `<span><span style="font-weight:bold;" v-if="parentTemplateItemId==0">{{scoreRank}}</span><span style="font-weight:normal;" v-else>{{scoreRank}}</span></span>`,
          data: function () {},
          computed: {
            parentTemplateItemId() {
              return this.data.parentTemplateItemId
            },
            scoreRank() {
              return this.data.scoreRank
            }
          }
        })
      }
    }
  }
]

// 档案清单详情-底层数据
export const basementDataColumn = [
  {
    width: 270,
    field: 'name',
    headerText: i18n.t('维度/指标'),
    cssClass: (data) => {
      if (data.indexLevel == 1) {
        return 'tree-title-bold'
      }
      return ''
    },
    cellTools: [
      {
        id: 'Add',
        title: i18n.t('查看数据'),
        visibleCondition: (data) => {
          return data['indexType'] == '2' && data.type != '1'
        }
      }
    ]
  },
  {
    field: 'indexType',
    headerText: i18n.t('类型'),
    template: () => {
      return {
        template: Vue.component('indexType1', {
          template: `<div>{{format(data.indexType)}}</div>`,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {
            format(val) {
              switch (val) {
                case '':
                  return i18n.t('维度')
                case '1':
                  return i18n.t('指标类')
                case '2':
                  return i18n.t('指标')
                default:
                  return ''
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'scoreType',
    headerText: i18n.t('取值方式'),
    template: () => {
      return {
        template: Vue.component('scoreType1', {
          template: `<div>{{format(data.scoreType)}}</div>`,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {
            format(val) {
              switch (val) {
                case 'sum':
                  return i18n.t('子项求和')
                case 'weight':
                  return i18n.t('权重式计算')
                case 'max':
                  return i18n.t('子项最高值')
                case 'min':
                  return i18n.t('子项最低值')
                default:
                  return ''
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'oneVeto',
    headerText: i18n.t('最低分一票否决'),
    template: () => {
      return {
        template: Vue.component('oneVeto1', {
          template: `<div>{{format(data.oneVeto)}}</div>`,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {
            format(val) {
              switch (val) {
                case '0':
                  return i18n.t('否')
                case '1':
                  return i18n.t('是')
                default:
                  return ''
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'indexRange',
    headerText: i18n.t('评分范围')
  },
  {
    field: 'scoreWeight',
    headerText: i18n.t('权重'),
    template: () => {
      return {
        template: Vue.component('scoreWeight', {
          template: `<div>{{data.scoreWeight||'-'}}
          </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {}
        })
      }
    }
  },
  {
    field: 'assignPoints',
    headerText: i18n.t('分配分值')
  },
  {
    field: 'remark',
    headerText: i18n.t('描述')
  }
]

// 档案清单详情-底层数据-数据视图
export const dataColumn = [
  {
    width: '150',
    field: 'id',
    headerText: i18n.t('计划编码')
  },
  {
    width: '150',
    field: 'id',
    headerText: i18n.t('计划名称')
  },
  {
    width: '230',
    field: 'id',
    headerText: i18n.t('组织')
  }
]

// 档案清单详情-底层数据-手动打分视图
export const manualGradingColumn = [
  {
    field: 'name',
    headerText: i18n.t('维度/指标')
  },
  {
    field: 'id',
    headerText: i18n.t('类型')
  },
  {
    field: 'id',
    headerText: i18n.t('取值方式')
  },
  {
    field: 'id',
    headerText: i18n.t('最低分一票否决'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: '0',
          text: i18n.t('否')
        },
        {
          value: '1',
          text: i18n.t('是')
        }
      ]
    }
  },
  {
    field: 'id',
    headerText: i18n.t('基准分')
  },
  {
    field: 'id',
    headerText: i18n.t('评分范围')
  },
  {
    field: 'id',
    headerText: i18n.t('权重')
  },
  {
    field: 'id',
    headerText: i18n.t('分配分值')
  },
  {
    field: 'id',
    headerText: i18n.t('描述')
  }
]

// 档案清单详情-计算结果分析
export const calcResultColumn = [
  { field: 'supplierCode', headerText: i18n.t('供应商编码'), cellTools: [] },
  { field: 'supplierEnterpriseName', headerText: i18n.t('供应商企业名称') },
  // { field: "dimensionName", headerText: i18n.t("维度") },
  // { field: "name", headerText: i18n.t("指标类") },
  { field: 'categoryName', headerText: i18n.t('品类') },
  { field: 'indexName', headerText: i18n.t('名称') }, //todo 缺少字段
  {
    field: 'indexType',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: { '': i18n.t('维度'), 1: i18n.t('指标类'), 2: i18n.t('指标') }
    }
  }, //todo 缺少字段
  { field: 'indexDescribe', headerText: i18n.t('描述') },
  { field: 'indexRange', headerText: i18n.t('评分范围') },
  {
    field: 'scoreWeight',
    headerText: i18n.t('权重值'),
    template: () => {
      return {
        template: Vue.component('scoreWeight', {
          template: `<div>{{data.scoreWeight||'-'}}
        </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {}
        })
      }
    }
  },
  { field: 'maxValue', headerText: i18n.t('满分值') },
  { field: 'assignPoints', headerText: i18n.t('分配分值') },
  {
    field: 'score',
    headerText: i18n.t('得分')
  },
  { field: 'reason', headerText: i18n.t('原因') }
]
