// 档案清单详情-结果应用情况
<template>
  <div class="result-apply-content">
    <mt-template-page
      slot="slot-0"
      :use-tool-template="false"
      ref="templateForm"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { resultApplyStatusColumnData } from '../config/index.js'
export default {
  data() {
    return {
      id: '',
      pageConfig: [
        {
          // gridId:"7866b873-707e-ba16-a719-f359c2757610",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
          },
          grid: {
            columnData: resultApplyStatusColumnData,
            dataSource: [],
            frozenColumns: 1
          }
        }
      ]
    }
  },
  created() {
    this.id = this.$route.query.id
    this.resetAsyncConfigParams()
  },
  methods: {
    //列表参数重新赋值
    resetAsyncConfigParams() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.archives.archiveDetailApply,
        params: { archiveId: this.id }
      })
    },
    // 点击表头上方按钮
    handleClickToolBar(e) {
      console.log(e)
    },
    // 点击单元格内按钮
    handleClickCellTool(e) {
      console.log(e)
    },
    // 表格内点击事件
    handleClickCellTitle(e) {
      console.log('e', e)
      // 跳转供应商档案详情
      if (e.field == 'supplierCode') {
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            supplierCode: e.data.supplierCode,
            orgCode: e.data.orgCode
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.result-apply-content {
  display: flex;
  flex: 1;
  height: 100%;
}
/deep/.red {
  background-color: rgba(237, 86, 51, 1);
}
/deep/.green {
  background-color: rgba(138, 204, 64, 1);
}
/deep/.yellow {
  background-color: rgba(237, 161, 51, 1);
}
</style>
