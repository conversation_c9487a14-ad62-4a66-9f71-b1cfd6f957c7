// 档案清单详情-计算结果分析
<template>
  <div class="calc-result-content">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import Vue from 'vue'
import { calcResultColumn } from '../config/index'
export default {
  props: {
    topInfo: {
      type: Array,
      default() {
        return []
      }
    },
    orgCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      id: null,
      pageConfig: [
        {
          // gridId:"530b4c50-ca1c-ca27-b972-f09eb0512314",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
          },
          grid: {
            height: 'calc(100vh - 510px)',
            columnData: calcResultColumn,
            allowGrouping: true,
            allowPaging: false,
            dataSource: [],
            groupSettings: {
              captionTemplate: function () {
                return {
                  template: Vue.component('columnTemplate', {
                    template: `<div>{{ value }}</div>`,
                    data: function () {
                      return {
                        data: {}
                      }
                    },
                    computed: {
                      value: (val) => {
                        const _column = calcResultColumn.filter((v) => {
                          return v.field === val.data.field
                        })
                        if (_column?.[0]?.valueConverter) {
                          return (
                            _column[0].valueConverter.map[val.data.key] +
                            ' - ' +
                            val.data.count +
                            ' ' +
                            val.$t('项目')
                          )
                        } else {
                          return val.data.key + ' - ' + val.data.count + ' ' + val.$t('项目')
                        }
                      }
                    }
                  })
                }
              }
            }
          }
          // treeGrid: {
          //   allowGrouping:true,
          //   height: "auto",
          //   allowPaging: false,
          //   columnData: calcResultColumn,
          //   childMapping: "indexList",
          //   dataSource: [

          //   ],
          // },
        }
      ]
    }
  },
  watch: {
    topInfo: {
      handler(val) {
        this.$set(this.pageConfig[0].grid, 'dataSource', val)
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {},
  methods: {
    // 点击表头上方按钮
    handleClickToolBar() {},
    handleClickCellTitle(e) {
      if (e.field == 'supplierCode') {
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            supplierCode: e.data.supplierCode,
            orgCode: this.orgCode
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.calc-result-content {
  // display: flex;
  // flex: 1;
  height: 100%;
  // ::v-deep .mt-data-grid{
  //   height: 100%;
  // }
  // ::v-deep .common-template-page .repeat-template{
  //   height: 100%;
  // }
  // ::v-deep .grid-container{
  //   height: calc(100% - 50px)
  // }
  // ::v-deep .e-gridcontent{
  //   height: calc(100% - 90px);
  // }
  // ::v-deep .e-content{
  //   height: 100% !important;
  // }
  // ::v-deep .e-grid{
  //   height: 100%;
  // }
}
</style>
