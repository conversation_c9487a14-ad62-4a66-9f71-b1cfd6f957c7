// 档案清单详情-底层数据
<template>
  <div class="flex-display">
    <div :class="[isViewData || isManualGrading ? 'all-list-left' : 'warp']">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
      />
    </div>
    <div class="all-list-right" v-if="isViewData">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
    <div class="all-list-right" v-if="isManualGrading">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig2"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
  </div>
</template>
<script>
import {
  basementDataColumn,
  // dataColumn,
  // manualGradingColumn,
  calcResultColumn
} from '../config/index'

export default {
  data() {
    return {
      isViewData: false, //数据视图
      isManualGrading: false, // 手动打分
      id: null,
      pageConfig: [
        {
          // gridId:"dee369a5-db52-1f5d-66f1-ee26676300aa",
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
          },
          treeGrid: {
            height: 'calc(100vh - 460px)',
            allowPaging: false,
            clipMode: 'EllipsisWithTooltip',
            columnData: basementDataColumn,
            childMapping: 'childrenList',
            dataSource: []
          }
        }
      ],
      pageConfig1: [],
      pageConfig2: []
    }
  },
  created() {
    this.id = this.$route.query.id
    this.$API.archives
      .archiveDetailDataTemplateInfo({
        archiveId: this.id
      })
      .then((res) => {
        if (res.code == 200) {
          this.processData(res.data)
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', res.data)
        }
      })
  },
  methods: {
    processData(arr) {
      arr.forEach((item) => {
        if (item.indexType == '1' || item.indexType == '2') {
          item.name = item.indexName
        }
        if (item.indexType == '') {
          item.name = item.dimensionName
        }
        item.type = this.$route.query.type
        if (Array.isArray(item.childrenList)) {
          this.processData(item.childrenList)
        }
      })
    },
    look(value) {
      this.$API.archives
        .archiveDetailData({
          archiveId: this.id,
          indexCode: value.indexCode,
          indexVersion: value.indexVersion
        })
        .then((res) => {
          if (res.code == 200) {
            if (res.data.flag == 2) {
              this.isManualGrading = true
              this.isViewData = false
              this.pageConfig2 = [
                {
                  // gridId:"a72f11e4-cd13-94f4-fb53-c3bda58279ee",
                  useToolTemplate: false,
                  toolbar: {
                    useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
                    tools: [
                      [
                        {
                          id: 'close',
                          icon: 'icon_solid_export',
                          title: this.$t('关闭此数据视图')
                        }
                      ]
                    ]
                  },
                  grid: {
                    height: 'calc(100vh - 510px)',
                    columnData: calcResultColumn,
                    // columnData: manualGradingColumn,
                    allowGrouping: true,
                    allowPaging: false,
                    dataSource: []
                  }
                }
              ]
              this.$nextTick(() => {
                this.$set(this.pageConfig2[0].grid, 'dataSource', res.data.manualList)
              })
            }
            if (res.data.flag == 1) {
              let arr = []
              let dataAar = []
              res.data.dataInfoList.forEach((item) => {
                let obj = {
                  title: item.tableName,
                  useToolTemplate: false,
                  toolbar: {
                    useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
                    tools: [
                      [
                        {
                          id: 'close',
                          icon: 'icon_solid_export',
                          title: this.$t('关闭此数据视图')
                        }
                      ]
                    ]
                  },
                  grid: {
                    height: 'calc(100vh - 510px)',
                    allowPaging: false,
                    columnData: [],
                    dataSource: []
                  }
                }
                item.tableFieldList.forEach((e) => {
                  obj.grid.columnData.push({
                    field: e.fieldCode,
                    headerText: e.fieldName
                  })
                })
                arr.push(obj)
                dataAar.push(item.valueList)
              })
              this.pageConfig1 = arr
              this.isManualGrading = false
              this.isViewData = true
              this.$nextTick(() => {
                this.pageConfig1.forEach((item, index) => {
                  item.grid.dataSource = dataAar[index]
                })
              })
              // res.data.dataInfoList.tableFieldList.forEach(item => {
              //   arr.push({
              //     field:item.fieldCode,
              //     headerText:item.fieldName,
              //   })
              // });
              // this.$set(
              //   this.pageConfig2[0].grid,
              //   "dataSource",
              //   res.data.manualList
              // );
            }
          }
        })
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'Add') {
        this.look(e.data)
      }
    },
    // 点击表头上方按钮
    handleClickToolBar(e) {
      if (e.toolbar.id === 'close') {
        this.isViewData = false
        this.isManualGrading = false
      }
      // else if (e.toolbar.id === "allData") {
      //   this.$set(this.pageConfig2[0].toolbar.tools[0][0], "id", "currentData");
      //   this.$set(
      //     this.pageConfig2[0].toolbar.tools[0][0],
      //     "title",
      //     "查看所选手动数据"
      //   );
      // } else if (e.toolbar.id === "currentData") {
      //   this.$set(this.pageConfig2[0].toolbar.tools[0][0], "id", "allData");
      //   this.$set(
      //     this.pageConfig2[0].toolbar.tools[0][0],
      //     "title",
      //     "查看全部手动数据"
      //   );
      // } else if (e.toolbar.id === "closeManualGrading") {
      //   this.isManualGrading = false;
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
.flex-display {
  display: flex;
  justify-content: space-between;
  height: 100%;
}
.warp {
  width: 100%;
}
.all-list-left {
  width: calc(50% - 12px);
}
.all-list-right {
  width: calc(50% - 12px);
}
/deep/ .e-treecolumn-container {
  display: flex !important;
  align-items: center;
  align-content: center;
  overflow: hidden;
}
/deep/.e-grid {
  .e-rowcell {
    text-align: left !important;
    .grid-edit-column {
      display: block !important;
      span {
        display: block !important;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .icon-title {
        display: inline-block !important;
      }
    }
  }
}
/deep/.tree-title-bold {
  font-weight: bold;
}
</style>
