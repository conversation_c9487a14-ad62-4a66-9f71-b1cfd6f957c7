// 档案清单详情-结果应用情况-详情弹窗
<template>
  <div ref="dialog" v-if="isOpen">
    <div class="full" @click="handleClose"></div>
    <div class="scoreDetail">
      <div>
        <span>{{ header }}</span>
        <span style="margin-left: 20px">{{ categoryName }}</span>
      </div>
      <div>
        <div style="width: 400px; height: 300px; float: left" id="radar"></div>
        <div style="width: 380px; height: 250px; float: left" id="line"></div>
      </div>
      <div class="score-detail-title">
        <div>{{ $t('得分明细') }}</div>
        <div class="section-active">
          <div v-show="flag" @click="toggleScoreDetailTreeTable(true)">
            {{ $t('收起') }}
            <span class="expand-icons"><mt-icon name="MT_DownArrow" /></span>
          </div>
          <div v-show="!flag" @click="toggleScoreDetailTreeTable(false)">
            {{ $t('展开') }}
            <span class="active-icons"><mt-icon name="MT_DownArrow" /></span>
          </div>
        </div>
      </div>
      <div class="list-content">
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { scoreDialogColumnData } from '../config/index'
import * as echarts from 'echarts'

let radarChartDom,
  radarChart,
  radarOption = null

export default {
  watch: {
    // performanceInfo: {
    //   handler(nv) {
    //     this.$nextTick(() => {
    //       this.initChart();
    //     });
    //   },
    //   deep: true,
    // },
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isOpen: true,
      flag: true,
      header: '',
      categoryName: '',
      radarData: [],
      radarLabel: [],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
          },
          treeGrid: {
            allowPaging: false,
            columnData: scoreDialogColumnData,
            parentIdMapping: 'parentTemplateItemId',
            idMapping: 'templateItemId',
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    this.$API.archives
      .archiveDetailApplyDetail({
        resultId: this.modalData.lineDate.id
      })
      .then((res) => {
        if (res.code == 200) {
          this.header = res.data.supplierEnterPriseName
          this.categoryName = res.data.categoryName
          this.$set(this.pageConfig[0].treeGrid, 'dataSource', [
            ...res.data.indexScoreDetailList,
            { templateItemId: '0', parentTemplateItemId: '-1' }
          ])
          res.data.indexScoreList.forEach((item) => {
            this.radarLabel.push({
              text: item.indexName
            })
            this.radarData.push(item.score)
          })
          radarChartDom = document.getElementById('radar')
          radarChart = echarts.init(radarChartDom)
          this.initChart()
        }
      })
  },
  methods: {
    toggleScoreDetailTreeTable(isOpen) {
      // 组件没把方法抛出，只能这样逐级去找。。。
      let ser1 = this.$refs.templateRef.$children[this.$refs.templateRef.$children.length - 1]
      let ser2 = ser1.$children[0]
      let ser3 = ser2.$children[0]
      let ser4 = ser3.$children[0]
      console.log(this.$refs.templateRef)
      this.flag = !this.flag
      if (isOpen) {
        ser4.collapseAll() // 收起全部
      } else {
        ser4.expandAll() // 展开全部
      }
    },
    handleClose() {
      this.isOpen = false
    },
    initChart() {
      // option = {
      //   xAxis: {
      //     type: "category",
      //     boundaryGap: false,
      //     data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      //     // axisLine: {
      //     //   symbol: "triangle",
      //     // },
      //     triggerEvent: true,
      //   },
      //   yAxis: {
      //     type: "value",
      //   },
      //   series: [
      //     {
      //       label: {
      //         show: true,
      //         position: "top",
      //       },
      //       data: [820, 932, 901, 934, 1290, 1330, 1320],
      //       type: "line",
      //     },
      //   ],
      // };
      // option && myChart.setOption(option);
      radarOption = {
        color: ['#6386C1'],
        legend: {},
        tooltip: {
          trigger: 'item'
        },
        radar: [
          {
            indicator: this.radarLabel,
            center: ['44%', '55%'],
            radius: 100,
            startAngle: 90,
            splitNumber: 4,
            shape: 'circle',
            axisName: {
              formatter: '{value}',
              color: '#292929'
            }
          }
        ],
        series: [
          {
            type: 'radar',
            emphasis: {
              lineStyle: {
                width: 4
              }
            },
            data: [
              {
                value: this.radarData,
                areaStyle: {
                  color: '#E8EEF6'
                }
              }
            ]
          }
        ]
      }
      radarOption && radarChart.setOption(radarOption)
    },
    handleClickToolBar() {}
  }
}
</script>
<style lang="scss" scoped>
.full {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 998;
}
.scoreDetail {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 840px;
  height: 670px;
  background: rgba(255, 255, 255, 1);
  border-radius: 8px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
  padding: 15px;
  font-size: 14px;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
  z-index: 999;
  /deep/ .e-rowcell {
    text-align: left;
  }
}
.list-content {
  height: 300px;
  display: flex;
  flex: 1 1 auto;
}
.score-detail-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.section-active {
  margin-top: 2px;
  margin-left: 16px;
  color: #6386c1;
  font-size: 12px;
  font-weight: normal;
  cursor: pointer;
  .active-icons {
    display: inline-block;
  }
  .expand-icons {
    display: inline-block;
    transform: rotate(180deg);
    vertical-align: text-top;
  }
  span {
    margin-left: 5px;
  }
}
/deep/ .e-treecolumn-container {
  display: flex !important;
  align-items: center;
  align-content: center;
  overflow: hidden;
}
/deep/.e-grid {
  .e-rowcell {
    text-align: left !important;
    .grid-edit-column {
      span {
        display: block !important;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .icon-title {
        display: inline-block !important;
      }
    }
  }
}
/deep/.tree-title-bold {
  font-weight: bold;
}
</style>
