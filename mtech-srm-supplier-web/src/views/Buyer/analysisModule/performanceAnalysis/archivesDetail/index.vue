<template>
  <div class="full-height">
    <div class="top-info">
      <div class="header">
        <!-- 左侧的信息 -->
        <div class="archives-title">{{ topInfo.planName }}</div>
        <div :class="['status-box', 'status-box' + '-' + topInfo.status]">
          <span v-if="topInfo.status == 0">{{ $t('未发布') }}</span>
          <span v-if="topInfo.status == 1">{{ $t('待反馈') }}</span>
        </div>
        <div class="infos">{{ topInfo.approvalResult }}</div>

        <div class="middle-blank"></div>

        <!-- 右侧操作按钮 -->
        <mt-button v-if="type != '1'" css-class="e-flat" :is-primary="true" @click="goBack">{{
          $t('返回')
        }}</mt-button>
        <!-- <mt-button css-class="e-flat" :is-primary="true" @click="goBack"
          >{{ $t("保存") }}</mt-button
        >
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack"
          >{{ $t("保存并提交") }}</mt-button
        > -->
      </div>
      <div class="middle">
        <span>{{ $t('计划编码') }}：{{ topInfo.planCode }}</span>
        <span>{{ $t('创建人') }}：{{ topInfo.createUserName }} {{ topInfo.time }}</span>
        <span>{{ $t('分析类型') }}：{{ analysisList[topInfo.planType] }}</span>
      </div>
      <div class="bottom">
        <span>{{ $t('期号') }}：{{ topInfo.issueNum }}</span>
        <span>{{ $t('提交截止日期') }}：{{ topInfo.submitDeadline }}</span>
        <span>{{ $t('所属公司') }}：{{ topInfo.orgName }} </span>
      </div>
    </div>
    <mt-tabs
      style="flex-shrink: 0"
      :tab-id="tabId"
      :e-tab="false"
      :data-source="pageConfig"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div style="height: calc(100% - 230px)">
      <calc-result-analysis
        :top-info="resultList"
        :org-code="topInfo.orgCode"
        v-if="currentTabIndex === 0"
      ></calc-result-analysis>
      <basement-data v-if="currentTabIndex === 1"></basement-data>
      <result-apply-status v-if="currentTabIndex === 2"></result-apply-status>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
export default {
  components: {
    basementData: require('./components/basementData.vue').default,
    calcResultAnalysis: require('./components/calcResultAnalysis.vue').default,
    resultApplyStatus: require('./components/resultApplyStatus.vue').default
  },
  data() {
    return {
      topInfo: {
        status: 0,
        createUserName: 'userName',
        createTime: 'createTime'
      },
      resultList: [],
      currentTabIndex: 0,
      tabId: 'archivesDetail', //用于区分多个自适应tabs
      pageConfig: [
        { title: this.$t('计算结果分析') },
        { title: this.$t('底层数据') },
        { title: this.$t('结果应用情况') }
      ],
      id: null,
      type: 0,
      analysisList: {
        1: this.$t('绩效分析'),
        2: this.$t('风险分析')
      }
    }
  },
  created() {
    this.id = this.$route.query.id
    this.type = this.$route.query.type || 0
    if (this.type == '1') {
      this.pageConfig = [{ title: this.$t('计算结果分析') }, { title: this.$t('底层数据') }]
    } else {
      this.pageConfig = [
        { title: this.$t('计算结果分析') },
        { title: this.$t('底层数据') },
        { title: this.$t('结果应用情况') }
      ]
    }
    this.init()
  },
  methods: {
    // 初始化查询详情
    init() {
      this.$API.archives.archiveDetail({ archiveId: this.id }).then((res) => {
        if (res.code == 200) {
          this.topInfo = res.data
          this.topInfo.time = utils.formateTime(
            Number(res.data.generateTime, 'yyyy-MM-dd hh:mm:ss')
          )
          this.resultList = res.data.scoreDetailList
        }
      })
    },
    // 返回按钮
    goBack() {
      this.$router.push({
        path: '/supplier/performance-analysis',
        query: {
          currentTab: 1
        }
      })
    },
    // 切换Tab
    handleSelectTab(e) {
      console.log('handleSelectTab', e)
      this.currentTabIndex = e
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-tabs {
  width: 100%;
  background-color: #fafafa;
}
.full-height {
  height: calc(100% - 29px);
  display: flex;
  flex-direction: column;
  padding: 0 20px;
  overflow: hidden;
}
.archives-title {
  font-size: 20px;
  font-family: PingFangSC;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
}
.middle-blank {
  flex: 1;
}
.top-info {
  width: 100%;
  height: 132px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  display: flex;
  flex-direction: column;
  padding: 26px 0 28px 30px;
  margin: 20px 0 25px 0;
  .status-box {
    line-height: 12px;
    padding: 4px;
    border-radius: 2px;
    margin: 0 28px 0 16px;
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    &-0 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
    &-1 {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
  }
  .header {
    width: 100%;
    height: 20px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .middle {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin: 10px 0 20px 0;
    span {
      margin-right: 20px;
    }
  }
  .bottom {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    span {
      margin-right: 24px;
    }
  }
}
</style>
