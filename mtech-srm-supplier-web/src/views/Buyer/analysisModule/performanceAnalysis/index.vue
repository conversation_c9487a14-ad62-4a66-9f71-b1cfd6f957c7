<template>
  <div class="full-height">
    <mt-template-page
      slot="slot-0"
      ref="templateForm"
      :template-config="pageConfig"
      :current-tab="currentTab"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>
<script>
import { planListColumnData, archivesColumnData } from './config/index.js'
export default {
  components: {
    addDialog: require('./components/addDialog.vue').default
  },
  data() {
    return {
      currentTab: 0,
      //列表调用
      // pageConfig: pageConfig(this.$API.analysisOfSetting.catassessQuery),
      addDialogShow: false, // 是否显示弹窗
      pageConfig: [
        {
          gridId: '3249fdd7-6a64-79ee-55b7-8cc313c3bd92',
          title: this.$t('计划清单'),
          toolbar: [
            {
              id: 'addNew',
              icon: 'icon_solid_Newinvitation',
              title: this.$t('新增')
            },
            {
              id: 'delete',
              icon: 'icon_solid_Delete1',
              title: this.$t('删除')
            },
            {
              id: 'editPolicy',
              icon: 'icon_Editor',
              title: this.$t('编辑')
            },
            {
              id: 'Enable',
              icon: 'icon_table_enable',
              title: this.$t('启用')
            },
            {
              id: 'disable',
              icon: 'icon_table_disable',
              title: this.$t('停用')
            },
            {
              id: 'Commit',
              icon: 'icon_solid_Createorder',
              title: this.$t('提交审批')
            }
          ],
          grid: {
            ignoreFields: ['planEndTime'],
            lineSelection: true,
            columnData: planListColumnData,
            asyncConfig: {
              url: this.$API.application.catepageQuery
            },
            frozenColumns: 1
          }
        },
        {
          gridId: '6b2916be-64f2-338b-92de-56056bf2a464',
          title: this.$t('档案清单'),
          useToolTemplate: false,
          toolbar: [
            {
              id: 'Disable',
              icon: 'icon_solid_Createorder',
              title: this.$t('提交审批')
            }
            // {
            //   id: "Enable",
            //   icon: "icon_solid_Createorder",
            //   title: this.$t("解冻"),
            // },
            // {
            //   id: "Commit",
            //   icon: "icon_solid_Createorder",
            //   title: this.$t("重新运算"),
            // },
          ],
          grid: {
            lineSelection: true,
            columnData: archivesColumnData,
            asyncConfig: {
              url: this.$API.archives.archiveQuery
              // serializeList: (list) =>{
              //   list.forEach(e=>{
              //     e.generateTime = Number(e.createTime)
              //     e.time = this.$utils.formateTime(e.createTime,"yyyy-MM-dd hh:mm:ss")
              //   });
              //   return list;
              // },
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  created() {
    this.currentTab = this.$route.query.currentTab ? this.$route.query.currentTab : 0
  },
  methods: {
    // 点击工具栏按钮
    handleClickToolBar(e) {
      if (e.tabIndex == 0) {
        if (e.toolbar.id == 'addNew') {
          this.handleAdd()
          return
        }
        if (e.gridRef.getMtechGridRecords().length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        if (e.gridRef.getMtechGridRecords().length > 1 && e.toolbar.id == 'editPolicy') {
          this.$toast({ content: this.$t('暂只支持单行编辑'), type: 'warning' })
          return
        }
        if (e.toolbar.id == 'editPolicy') {
          this.handleEdit(e.gridRef.getMtechGridRecords()[0])
        }
        if (e.toolbar.id == 'delete') {
          this.handleDelete(e.gridRef.getMtechGridRecords())
        }
        if (e.toolbar.id == 'Commit') {
          this.planSubmit(e.gridRef.getMtechGridRecords())
        }
        if (e.toolbar.id == 'Reject') {
          this.planExamine(e.gridRef.getMtechGridRecords(), 30)
        }
        if (e.toolbar.id == 'Pass') {
          this.planExamine(e.gridRef.getMtechGridRecords(), 40)
        }
        if (e.toolbar.id == 'Enable') {
          this.planOperate(e.gridRef.getMtechGridRecords(), 40)
        }
        if (e.toolbar.id == 'disable') {
          this.planOperate(e.gridRef.getMtechGridRecords(), 50)
        }
      }
      if (e.tabIndex == 1 && e.toolbar.id == 'Disable') {
        if (e.gridRef.getMtechGridRecords().length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        if (e.gridRef.getMtechGridRecords().length > 1) {
          this.$toast({ content: this.$t('只可单选'), type: 'warning' })
          return
        }
        this.handleSubmit(e.gridRef.getMtechGridRecords())
      }
    },
    // 审批
    planExamine(arr, val) {
      let bol = arr.every((item) => {
        return item.approveStatus == 20
      })
      if (!bol) {
        this.$toast({ content: this.$t('请选择待审核的数据'), type: 'warning' })
        return
      }
      let ids = arr.map((item) => {
        return item.id
      })
      let params = {
        ids: ids,
        approveStatus: 30,
        status: val
      }
      this.$API.application.carategyExamine(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('审批成功'),
            type: 'success'
          })
          this.$refs.templateForm.refreshCurrentGridData()
        }
      })
    },
    // 启用停用
    planOperate(arr, val) {
      if (val == 40) {
        let bol = arr.every((item) => {
          return item.status == 50
        })
        if (!bol) {
          this.$toast({ content: this.$t('请选择停用的数据'), type: 'warning' })
          return
        }
      }
      if (val == 50) {
        let bol = arr.every((item) => {
          return item.status == 40
        })
        if (!bol) {
          this.$toast({
            content: this.$t('提示请选择状态为启用的计划清单'),
            type: 'warning'
          })
          return
        }
      }
      let ids = arr.map((item) => {
        return item.id
      })
      let params = {
        ids: ids,
        status: val
      }
      this.$API.application.carategyUpdateStatus(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('修改成功'),
            type: 'success'
          })
          this.$refs.templateForm.refreshCurrentGridData()
        }
      })
    },
    // 档案提交审批
    handleSubmit(arr) {
      let params = {
        archiveIds: []
      }
      arr.forEach((item) => {
        params.archiveIds.push(item.archiveId)
      })
      this.$API.archives.archiveSubmit(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateForm.refreshCurrentGridData()
        }
      })
    },
    // this.$t("计划删除")
    handleDelete(arr) {
      let bol = arr.some((item) => {
        return item.status == 20 || item.status == 40
      })
      if (bol) {
        this.$toast({
          content: this.$t('状态为启用\已提交，不可删除'),
          type: 'warning'
        })
        return
      }
      let ids = arr.map((item) => {
        return item.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.application.carategyDel({ ids: ids }).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateForm.refreshCurrentGridData()
            }
          })
        }
      })
    },
    // /计划提交
    planSubmit(arr) {
      let bol = arr.every((item) => {
        return item.status == 10 || item.status == 30
      })
      if (!bol) {
        this.$toast({ content: this.$t('待提交或者驳回状态才能提交审批'), type: 'warning' })
        return
      }
      let ids = arr.map((item) => {
        return item.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否确认提交审批')
        },
        success: () => {
          this.$API.application.carategySubmit({ ids: ids }).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.$refs.templateForm.refreshCurrentGridData()
            }
          })
        }
      })
    },
    // 点击单元格按钮
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data])
      } else if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      } else if (e.tool.id == 'enable') {
        this.planOperate([e.data], 40)
      } else if (e.tool.id == 'block') {
        this.planOperate([e.data], 50)
      } else if (e.tool.id == 'submit') {
        this.planSubmit([e.data])
      }
    },
    // 点击单元格文字
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'planCode') {
        // 跳转计划清单详情页面
        this.$router.push({
          path: '/supplier/performance-analysis-plan-detail',
          query: { id: data.id }
        })
      } else if (field === 'archiveCode') {
        // 跳转档案清单详情页面
        this.$router.push({
          path: '/supplier/performance-analysis-archives-detail',
          query: { id: data.archiveId }
        })
      }
    },
    // 弹框关闭刷新页面
    confirmSuccess(val) {
      if (val != undefined) {
        this.$router.push({
          path: '/supplier/performance-analysis-plan-detail',
          query: { id: val }
        })
      } else {
        this.$refs.templateForm.refreshCurrentGridData() //刷新表格统一方法
      }
    },
    // 新增
    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },
    // 编辑。
    handleEdit(val) {
      if (val.status == 20 || val.status == 40) {
        this.$toast({
          content: this.$t('状态为启用\已提交，不可编辑'),
          type: 'warning'
        })
        return
      }
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        data: val
      }
    },
    // 切换新增弹窗显示状态
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
}
/deep/ .mt-progress {
  width: auto;
}
/deep/.mt-icon-a-icon_Singlechoice_on {
  margin: auto 9px;
  color: #e8e8e8;
  font-size: 20px;
}
/deep/.progress-number {
  display: inline-block;
  line-height: 45px;
  color: #00469c;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 600;
}
</style>
