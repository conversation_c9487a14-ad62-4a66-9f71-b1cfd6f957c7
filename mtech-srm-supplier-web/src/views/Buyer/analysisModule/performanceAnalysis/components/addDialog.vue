// 计划清单-新增计划弹窗
<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    css-class="create-proj-dialog"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="formObject" :rules="rules">
        <mt-form-item prop="planName" :label="$t('计划名称')">
          <mt-input
            v-model="formObject.planName"
            :show-clear-button="true"
            :multiline="false"
            :placeholder="$t('请输入计划名称')"
            :max-length="50"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="orgIdArr" :label="$t('公司')">
          <mt-DropDownTree
            v-if="fieldsarr.dataSource.length > 0"
            v-model="formObject.orgIdArr"
            :placeholder="$t('请选择计划生效公司')"
            :popup-height="500"
            :fields="fieldsarr"
            @input="selectCompany"
            id="baseTreeSelect"
          ></mt-DropDownTree>
          <mt-select v-else :placeholder="$t('请选择计划生效公司')" :data-source="[]"></mt-select>
        </mt-form-item>
        <mt-form-item prop="planType" :label="$t('分析类型')">
          <mt-select
            :fields="{ text: 'name', value: 'type' }"
            v-model="formObject.planType"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="analysisList"
            :placeholder="$t('请选择分析类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="planStrategy" :label="$t('计划策略')">
          <mt-select
            :fields="{ text: 'name', value: 'type' }"
            v-model="formObject.planStrategy"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="planList"
            :placeholder="$t('请选择计划策略')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="planCycle" :label="$t('周期')">
          <mt-select
            v-model="formObject.planCycle"
            :fields="{ text: 'name', value: 'type' }"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="planLoopList"
            :placeholder="$t('请选择计划周期')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="planRange" :label="$t('数据范围')">
          <mt-select
            :fields="{ text: 'name', value: 'type' }"
            v-model="formObject.planRange"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="dataRangeList"
            :placeholder="$t('请选择数据范围')"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('有效期')">
          <mt-date-picker
            style="width: 49%;display: inline-block"
            v-model="formObject.planStartTime"
            format="yyyy-MM-dd"
            :placeholder="$t('请选择生效时间')"
          ></mt-date-picker
          >&nbsp;
          <mt-date-picker
            style="width: 49%;display: inline-block"
            v-model="formObject.planEndTime"
            format="yyyy-MM-dd"
            :placeholder="$t('请选择截止时间')"
          ></mt-date-picker>
        </mt-form-item> -->
        <mt-form-item prop="planDeadLine" :label="$t('截止日')">
          <mt-inputNumber
            ref="planDeadLine"
            :max="999"
            :min="0"
            v-model="formObject.planDeadLine"
            @input="(v) => planDeadLineChange(v, 'planDeadLine')"
            :placeholder="$t('请输入截止日')"
            :precision="0"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item prop="planCreateType" :label="$t('创建方式')">
          <mt-radio
            v-model="formObject.planCreateType"
            :data-source="radioData"
            @change="planCreateTypeChange"
            :disabled="dialogData.dialogType !== 'add'"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item prop="templateCode" :label="$t('模板')" v-if="formObject.planCreateType == 1">
          <mt-select
            :fields="{ text: 'templateName', value: 'templateCode' }"
            v-model="formObject.templateCode"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="templateList"
            :placeholder="$t('请选择计划模板')"
            @select="templateCodeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="fullScore" :label="$t('满分')" v-if="formObject.planCreateType == 2">
          <mt-inputNumber
            ref="fullScore"
            :max="999"
            :min="0"
            v-model="formObject.fullScore"
            @input="(v) => planDeadLineChange(v, 'fullScore')"
            :placeholder="$t('请输入满分')"
            :precision="0"
            key="1"
            :disabled="dialogData.dialogType !== 'add'"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item prop="calcType" :label="$t('计算方式')" v-if="formObject.planCreateType == 2">
          <mt-select
            key="2"
            v-model="formObject.calcType"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="approachesToList"
            :placeholder="$t('请选择计算方式')"
            :disabled="dialogData.dialogType !== 'add'"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="templateBusiness"
          :label="$t('业务场景')"
          v-if="formObject.planCreateType == 2"
        >
          <mt-select
            key="3"
            v-model="formObject.templateBusiness"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="templateBusinessList"
            :placeholder="$t('业务场景')"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item prop="repeatScoreYn" :label="$t('重复评分')">
          <mt-select
            v-model="formObject.repeatScoreYn"
            float-label-type="Never"
            :allow-filtering="true"
            :fields="{ text: 'name', value: 'type' }"
            :data-source="supplierTypeList"
            :placeholder="$t('请选择重复评分')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="repeatAssessYn" :label="$t('重复分析')">
          <mt-select
            v-model="formObject.repeatAssessYn"
            float-label-type="Never"
            :allow-filtering="true"
            :fields="{ text: 'name', value: 'type' }"
            :data-source="supplietwoList"
            :placeholder="$t('请选择重复分析')"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item id="describe" :label="$t('描述')">
          <mt-input
            v-model="formObject.planDesc"
            :multiline="false"
            :placeholder="$t('请输入描述')"
            :max-length="200"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
// import {
//   equalType, //绩效分析
// } from "../config/index";
import MtRadio from '@mtech-ui/radio'

export default {
  components: { MtRadio },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmEnter,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      dialogTitle: '',
      analysisList: [
        {
          type: '1',
          name: this.$t('绩效分析')
        }
      ], // 分析类型
      dataRangeList: [
        {
          type: '1',
          name: this.$t('跟随周期')
        }
      ], // this.$t("数据范围")
      planList: [
        {
          type: '1',
          name: this.$t('周期')
        }
      ], // 计划策略
      planLoopList: [
        {
          type: '0',
          name: this.$t('年度')
        },
        {
          type: '1',
          name: this.$t('半年度')
        },
        {
          type: '2',
          name: this.$t('季度')
        },
        {
          type: '3',
          name: this.$t('月度')
        }
      ],
      // 重复评分
      supplierTypeList: [
        {
          type: '1',
          name: this.$t('允许')
        },
        {
          type: '2',
          name: this.$t('不允许')
        }
      ],
      // 重复分析
      supplietwoList: [
        {
          type: '1',
          name: this.$t('允许')
        },
        {
          type: '2',
          name: this.$t('不允许')
        }
      ],
      // 创建方式
      radioData: [
        // {
        //   label: this.$t("引用模板"),
        //   value: "1",
        // },
        {
          label: this.$t('自定义模板'),
          value: '2'
        }
      ],
      // 计算方式
      approachesToList: [
        {
          text: this.$t('子项求和'),
          value: 'sum'
        },
        {
          text: this.$t('权重式计算'),
          value: 'weight'
        },
        {
          text: this.$t('子项最高值'),
          value: 'max'
        },
        {
          text: this.$t('子项最低值'),
          value: 'min'
        },
        {
          text: this.$t('子项平均值'),
          value: 'average'
        }
      ],
      // this.$t("业务场景")
      templateBusinessList: [
        {
          text: this.$t('非生产采购'),
          value: 1
        },
        {
          text: this.$t('生产采购'),
          value: 2
        }
      ],

      // 模板列表
      templateList: [],
      formObject: {
        planName: '', //计划名称  0
        planCode: '', //计划编码 0
        planId: '', //计划id  0

        orgIdArr: [],
        orgCode: '', //组织机构编码   0
        orgId: '', //组织机构id   0
        orgName: '', //组织机构名称  0

        planType: '', // 分析类型
        planStrategy: '', // 计划策略
        planCycle: '', //计划周期
        planRange: '', //		数据范围
        planStartTime: '', //		有效期起
        planEndTime: '', //		有效期止
        planDeadLine: '', //截止日
        planCreateType: '2', //创建方式（引用模板，自定义模板） 1:引用 2：自定义创建方式（引用模板，自定义模板） 1:引用 2：自定义
        repeatAssessYn: '2', //	重复分析
        repeatScoreYn: '2', //		重复评分
        planDesc: '', //	描述

        // 引用模板
        templateCode: '', //		模板code
        templateName: '', //		模板名称
        templateVersion: '', //		模板版本
        rangeType: '',

        // 自定义模板
        fullScore: '', //	满分
        calcType: '', //计算方式
        templateBusiness: '', //		模板对应的业务场景 1非采 2通采
        keepDecimal: 2 //保留几位小数
      },
      // 组织
      fieldsarr: {
        dataSource: [], //公司树下拉数组
        value: 'id',
        text: 'name',
        child: 'children',
        code: 'orgCode'
      },
      fieldsarr1: {
        dataSource: [], //公司树下拉数组
        value: 'id',
        text: 'name',
        child: 'children',
        code: 'orgCode'
      },
      // 校验规则
      rules: {
        planName: [{ required: true, message: this.$t('请输入计划名称'), trigger: 'blur' }],
        orgIdArr: [{ required: true, message: this.$t('请选择组织'), trigger: 'blur' }],
        planType: [{ required: true, message: this.$t('请选择分析类型'), trigger: 'blur' }],
        planStrategy: [{ required: true, message: this.$t('请选择计划策略'), trigger: 'blur' }],
        planRange: [{ required: true, message: this.$t('请选择数据范围'), trigger: 'blur' }],
        planCycle: [{ required: true, message: this.$t('请选择计划周期'), trigger: 'blur' }],
        planDeadLine: [{ required: true, message: this.$t('请输入截止日'), trigger: 'blur' }],
        planCreateType: [{ required: true, message: this.$t('请选择创建方式'), trigger: 'blur' }],
        templateCode: [{ required: true, message: this.$t('请选择计划模板'), trigger: 'blur' }],
        fullScore: [{ required: true, message: this.$t('请输入满分'), trigger: 'blur' }],
        calcType: [{ required: true, message: this.$t('请选择计算方式'), trigger: 'blur' }],
        templateBusiness: [{ required: true, message: this.$t('请选择业务场景'), trigger: 'blur' }],
        repeatScoreYn: [{ required: true, message: this.$t('请选择重复评分'), trigger: 'blur' }],
        repeatAssessYn: [{ required: true, message: this.$t('请选择重复评分'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    if (this.dialogData.dialogType === 'add') {
      this.dialogTitle = this.$t('新增计划')
    } else {
      this.dialogTitle = this.$t('编辑计划')
      this.formObject = JSON.parse(JSON.stringify(this.dialogData.data))
      this.formObject.orgIdArr = [this.formObject.orgId]
      this.templateListQuery(this.formObject.orgId)
    }
    this.$refs['dialog'].ejsRef.show()
  },
  created() {
    // 初始化获取公司列表
    this.TreeByAccount()
  },
  methods: {
    // 初始化获取公司列表
    TreeByAccount() {
      this.$API.supplierIndex
        .getOrgTree({
          orgLevelCode: 'ORG02'
        })
        .then((res) => {
          if (res.code == 200) {
            this.$set(this.fieldsarr, 'dataSource', [...res.data])
          }
        })
      // this.$API.application["TreeByAccount"]({}).then((res) => {
      //   this.$set(this.fieldsarr, "dataSource", [...res.data]);
      // });
    },
    // 选取公司递归
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formObject.orgCode = ele.orgCode
          this.formObject.orgId = ele.id
          this.formObject.orgName = ele.name
          return
        }
        if (ele.children && ele.children.length > 0) {
          this.fn(ele.children, id)
        }
      })
    },
    // 选择公司
    selectCompany(e) {
      this.fn(this.fieldsarr.dataSource, e[0])
      if (this.formObject.planCreateType == 1) {
        this.formObject.templateCode = ''
        this.formObject.templateName = ''
        this.formObject.templateVersion = ''
        this.formObject.rangeType = ''
      }

      this.templateListQuery(e[0])
    },
    // 请求模板
    templateListQuery(id) {
      this.$API.application
        .templateListQuery({
          orgId: id
        })
        .then((res) => {
          if (res.code == 200) {
            this.templateList = res.data
          }
        })
    },
    templateCodeChange(e) {
      if (e.itemData) {
        let { itemData } = e
        this.formObject.templateName = itemData.templateName
        this.formObject.templateVersion = itemData.version
        this.formObject.rangeType = itemData.rangeType
      }
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    save(val) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let addBuyerAssessPlanRaterRequest = JSON.parse(JSON.stringify(this.formObject))
          addBuyerAssessPlanRaterRequest.keepDecimal = 2
          delete addBuyerAssessPlanRaterRequest.orgIdArr
          if (addBuyerAssessPlanRaterRequest.planStartTime) {
            addBuyerAssessPlanRaterRequest.planStartTime =
              addBuyerAssessPlanRaterRequest.planStartTime.substring(0, 10) + ' 00:00:00'
          }
          if (addBuyerAssessPlanRaterRequest.planEndTime) {
            addBuyerAssessPlanRaterRequest.planEndTime =
              addBuyerAssessPlanRaterRequest.planEndTime.substring(0, 10) + ' 23:59:59'
          }
          if (
            addBuyerAssessPlanRaterRequest.planEndTime &&
            addBuyerAssessPlanRaterRequest.planStartTime
          ) {
            if (
              new Date(addBuyerAssessPlanRaterRequest.planEndTime).getTime() <
              new Date(addBuyerAssessPlanRaterRequest.planStartTime).getTime()
            ) {
              this.$toast({ content: this.$t('生效日期不能大于截止日期'), type: 'warning' })
              return
            }
          }
          if (this.dialogData.dialogType === 'add') {
            this.carategyAdd(addBuyerAssessPlanRaterRequest, val)
          } else {
            this.carategyEdit(addBuyerAssessPlanRaterRequest, val)
          }
        }
      })
    },
    // 计划新增
    carategyAdd(params, val) {
      this.$API.application.carategyAdd(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('新增成功'),
            type: 'success'
          })
          this.$emit('handleAddDialogShow', false) //关闭弹窗
          if (val == 1) {
            this.$emit('confirmSuccess', res.data.id) //刷新列表
          } else {
            this.$emit('confirmSuccess') //刷新列表
          }
        }
      })
    },
    carategyEdit(params, val) {
      this.$API.application.carategyEdit(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('编辑成功'),
            type: 'success'
          })
          this.$emit('handleAddDialogShow', false) //关闭弹窗
          if (val == 1) {
            this.$emit('confirmSuccess', params.id) //刷新列表
          } else {
            this.$emit('confirmSuccess') //刷新列表
          }
        }
      })
    },
    confirmEnter() {
      this.save(1)
    },
    planDeadLineChange(val, type) {
      console.log(type)
      if (val != null) {
        this.formObject[type] = Number(String(val).substring(0, 3))
        this.$refs[type].ejsValue = this.formObject[type]
      }
    },
    planCreateTypeChange(val) {
      this.formObject.templateCode = ''
      this.formObject.templateName = ''
      this.formObject.templateVersion = ''
      this.formObject.fullScore = ''
      this.formObject.calcType = ''
      this.formObject.templateBusiness = ''
      this.formObject.rangeType = ''
      if (val == 1) {
        this.formObject.keepDecimal = ''
      } else {
        this.formObject.keepDecimal = 2
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
/deep/ .mt-date-picker > .e-input-group {
  line-height: 32px;
}
/deep/ .icon-style {
  margin: -14px 0 0 42px;
  position: absolute;
}
/deep/ #describe {
  width: 100%;
}
</style>
