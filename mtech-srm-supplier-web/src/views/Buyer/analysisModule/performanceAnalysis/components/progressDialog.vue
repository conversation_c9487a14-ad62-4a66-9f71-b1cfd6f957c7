// 档案清单-手动打分人进度弹窗
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :header="dialogTitle"
      css-class="create-proj-dialog"
      :buttons="buttons"
      @close="handleClose"
      :open="onOpen"
    >
      <mt-template-page ref="template-0" :template-config="pageConfig"> </mt-template-page>
    </mt-dialog>
  </div>
</template>
<script>
import { progressColumn } from '../config/index'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      dialogTitle: '',
      pageConfig: [
        {
          gridId: 'db01e85a-5b00-7988-82d3-477f9916001c',
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: []
          },
          grid: {
            allowPaging: false,
            lineSelection: true,
            columnData: progressColumn,
            asyncConfig: {
              url: '/analysis/tenant/buyer/assess/archive/progress/list',
              methods: 'get',
              params: {
                archiveId: this.modalData.archiveId
              },
              recordsPosition: 'data.manualScoreProgressDTOList'
            }
            // dataSource: [],
          }
        }
      ]
    }
  },
  mounted() {
    this.dialogTitle = this.$t('打分进度')
    this.$refs['dialog'].ejsRef.show()
    console.log(this.modalData)
    // this.pageConfig=
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    handleClose() {
      this.$refs['dialog'].ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped></style>
