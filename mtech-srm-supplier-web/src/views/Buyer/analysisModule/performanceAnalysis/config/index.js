import Vue from 'vue'
import MtProgress from '@mtech-ui/progress'
import { i18n } from '@/main.js'
// const toolbar = [
//   {
//     id: 'addNew',
//     icon: 'icon_solid_Newinvitation',
//     title: i18n.t('新增')
//   },
//   {
//     id: 'editPolicy',
//     icon: 'icon_Editor',
//     title: i18n.t('编辑')
//   },
//   {
//     id: 'delete',
//     icon: 'icon_solid_Delete1',
//     title: i18n.t('删除')
//   },
//   {
//     id: 'Enable',
//     icon: 'icon_table_enable',
//     title: i18n.t('启用')
//   },
//   {
//     id: 'disable',
//     icon: 'icon_table_disable',
//     title: i18n.t('停用')
//   },
//   {
//     id: 'copy',
//     icon: 'icon_table_copy',
//     title: i18n.t('复制')
//   }
// ]
// 计划清单
export const planListColumnData = [
  {
    width: '180',
    field: 'planCode',
    headerText: i18n.t('计划编码'),
    cellTools: [
      {
        id: 'edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.status != 20 && data.status != 40
        }
      },
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data.status != 20 && data.status != 40
        }
      }
    ]
  },
  {
    width: '180',
    field: 'planName',
    headerText: i18n.t('计划名称')
  },
  {
    width: '180',
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    width: '180',
    field: 'planCycle',
    headerText: i18n.t('计划策略'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: '0',
          text: i18n.t('年度'),
          cssClass: 'col-inactive'
        },
        {
          value: '1',
          text: i18n.t('半年度'),
          cssClass: 'col-inactive'
        },
        {
          value: '2',
          text: i18n.t('季度'),
          cssClass: 'col-inactive'
        },
        {
          value: '3',
          text: i18n.t('月度'),
          cssClass: 'col-inactive'
        }
      ]
    }
  },
  {
    width: '180',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '10', text: i18n.t('未提交'), cssClass: 'col-inactive' },
        { value: '20', text: i18n.t('已提交'), cssClass: 'col-inactive' },
        {
          value: '30',
          text: i18n.t('已驳回'),
          cssClass: 'col-active'
        },
        {
          value: '40',
          text: i18n.t('启用'),
          cssClass: 'col-active'
        },
        {
          value: '50',
          text: i18n.t('停用'),
          cssClass: 'col-active'
        },
        {
          value: '60',
          text: i18n.t('已过期'),
          cssClass: 'col-active'
        }
      ]
    },
    cellTools: [
      {
        id: 'enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data.status == 50
        }
      },
      {
        id: 'block',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data.status == 40
        }
      },
      {
        id: 'submit',
        title: i18n.t('提交审批'),
        visibleCondition: (data) => {
          return data.status == 10
        }
      }
    ]
  },
  {
    width: '180',
    field: 'planType',
    headerText: i18n.t('分析类型'),
    valueConverter: {
      type: 'map',
      map: [{ value: '1', text: i18n.t('绩效分析'), cssClass: 'col-inactive' }]
    }
  },
  {
    width: '180',
    field: 'approveStatus',
    headerText: i18n.t('审批状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: '10',
          text: i18n.t('未提交'),
          cssClass: 'col-unCommit'
        },
        {
          value: '20',
          text: i18n.t('待审核'),
          cssClass: 'col-notApprove'
        },
        {
          value: '30',
          text: i18n.t('已审批'),
          cssClass: 'col-approved'
        }
      ]
    }
  },
  {
    width: '100',
    field: 'planEndTime',
    headerText: i18n.t('有效期'),
    template: () => {
      return {
        template: Vue.component('planEndTime', {
          template: `<div style="text-align:left">
            <p>{{data.planStartTime?data.planStartTime.substring(0,10):''}}</p>
            <p>{{data.planEndTime?data.planEndTime.substring(0,10):''}}</p>
          </div>`,
          data: function () {}
        })
      }
    }
  },
  {
    width: '180',
    field: 'planDesc',
    headerText: i18n.t('描述')
  },
  {
    width: '180',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '180',
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>{{data.createTime.substring(0,10)}}<br>{{data.createTime.substring(11,19)}}</div>`,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  }
]

// 档案清单
export const archivesColumnData = [
  {
    width: '180',
    field: 'archiveCode',
    headerText: i18n.t('档案编码'),
    cellTools: []
  },
  {
    width: '180',
    field: 'templateName',
    headerText: i18n.t('模板名称')
  },
  {
    width: '250',
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    width: '180',
    field: 'planCycle',
    headerText: i18n.t('计划策略'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: '0',
          text: i18n.t('年度'),
          cssClass: 'col-inactive'
        },
        {
          value: '1',
          text: i18n.t('半年度'),
          cssClass: 'col-inactive'
        },
        {
          value: '2',
          text: i18n.t('季度'),
          cssClass: 'col-inactive'
        },
        {
          value: '3',
          text: i18n.t('月度'),
          cssClass: 'col-inactive'
        }
      ]
    }
  },
  {
    width: '180',
    field: 'issueNum',
    headerText: i18n.t('期号')
  },
  {
    width: '180',
    field: 'createDate',
    headerText: i18n.t('生成时间'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>{{data.createTime.substring(0,10)}}<br>{{data.createTime.substring(11,19)}}</div>`,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: '180',
    field: 'submitDeadline',
    headerText: i18n.t('提交截止日期'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>{{data.submitDeadline.substring(0,10)}}</div>`,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: '180',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      //0-未发布，1. 待反馈，2. 反馈正常，3. 反馈异常，-1. 已关闭
      map: [
        { value: '10', text: i18n.t('进行中'), cssClass: 'col-inactive' },
        { value: '15', text: i18n.t('待提交'), cssClass: 'col-inactive' },
        { value: '20', text: i18n.t('已提交'), cssClass: 'col-active' },

        { value: '30', text: i18n.t('已驳回'), cssClass: 'col-active' },
        { value: '40', text: i18n.t('已完成'), cssClass: 'col-active' },
        { value: '50', text: i18n.t('已解冻'), cssClass: 'col-active' },
        { value: '60', text: i18n.t('已过期'), cssClass: 'col-active' }
      ],
      fields: { text: 'text', value: 'value' }
    }
    // cellTools: [
    //   {
    //     id: "close",
    //     icon: "icon_list_close",
    //     title: i18n.t("关闭"),
    //   },
    //   {
    //     id: "publish",
    //     icon: "icon_list_issue",
    //     title: i18n.t("发布"),
    //   },
    //   {
    //     id: "cancelPublish",
    //     icon: "icon_list_Unpublish",
    //     title: i18n.t("取消发布"),
    //     visibleCondition: (data) => {
    //       return data.status == 1;
    //     },
    //   },
    // ],
  },
  {
    width: '180',
    field: 'approvalResult',
    headerText: i18n.t('审批状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: '10',
          text: i18n.t('未提交'),
          cssClass: 'col-notApprove'
        },
        {
          value: '20',
          text: i18n.t('待审批'),
          cssClass: 'col-unCommit'
        },
        {
          value: '30',
          text: i18n.t('已审批'),
          cssClass: 'col-approved'
        }
      ],
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    width: '180',
    field: 'businessType',
    ignore: true,
    headerText: i18n.t('手动打分人进度'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          components: {
            MtProgress
          },
          template: `<div style="display: flex;" class="cell-operable-title" @click="showProgress"><mt-progress
          ref="indeterminate"
          id="successcontainer"
          type="Circular"
          width="40px"
          height="40px"
          :value="value2"
          :progressThickness="progressThickness"
          v-if='data.finishedCount != data.totalCount'
        >
        </mt-progress>
        <MtIcon  v-if="data.finishedCount == data.totalCount" name="a-icon_Singlechoice_on" />
        <span class="progress-number">{{data.finishedCount}}/{{data.totalCount}}</span></div>`,
          data() {
            return {
              data: {},
              value2: null,
              progressThickness: 2
            }
          },
          mounted() {
            console.log('this.data', this.data)
            this.value2 = (this.data.finishedCount / this.data.totalCount) * 100
          },
          methods: {
            showProgress() {
              this.$dialog({
                modal: () =>
                  import(
                    '@/views/Buyer/analysisModule/performanceAnalysis/components/progressDialog.vue'
                  ),
                data: {
                  archiveId: this.data.archiveId
                }
              })
            }
          }
        })
      }
    }
  }
]

// 手动打分人进度弹窗表头
export const progressColumn = [
  {
    field: 'raterName',
    headerText: i18n.t('手动评分人')
  },
  {
    field: 'phone',
    headerText: i18n.t('手机号')
  },
  {
    field: 'email',
    headerText: i18n.t('邮箱')
  },
  {
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    field: 'deptName',
    headerText: i18n.t('部门')
  },
  {
    field: 'indexName',
    headerText: i18n.t('指标')
  }
]
