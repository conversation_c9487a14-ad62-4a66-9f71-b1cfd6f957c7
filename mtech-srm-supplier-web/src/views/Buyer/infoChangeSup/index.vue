<template>
  <div class="lifeCycle-container">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnDataMain } from './config/index.js'
import axios from 'axios'
export default {
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$hloading()
    })
  },
  data() {
    return {
      componentConfig: [
        {
          gridId: 'dc6976d0-a5af-4ef7-8561-483fb5b4a03a',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'launch', icon: 'icon_table_new', title: this.$t('发起信息变更') }
                // { id: "commit", icon: "icon_table_new", title: this.$t("提交") },
              ],
              [
                'Filter',
                { id: 'export', icon: 'icon_solid_export', title: this.$t('导出') },
                'Refresh',
                'Setting'
              ]
            ]
          },
          grid: {
            columnData: columnDataMain,
            asyncConfig: {
              url: '/supplier/tenant/supplier/info/change/apply/query'
            }
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    handleClickToolBar(item) {
      if (item.toolbar.id == 'add') {
        this.$router.push({
          name: 'infoChangeSupDetail',
          query: { type: 'add' }
        })
      } else if (item.toolbar.id == 'commit') {
        // 提交 applyStatus 10 30 的可以提交
        let selected = item.gridRef.getMtechGridRecords()
        if (selected.length > 0) {
          let num = 0
          let applyCodeList = []
          selected.forEach((item) => {
            if (item.applyStatus == 20 || item.applyStatus == 40 || item.applyStatus == 50) {
              num++
            } else {
              applyCodeList.push(item.applyCode)
            }
          })
          if (num > 0) {
            this.$toast({
              content: this.$t('有状态不能提交'),
              type: 'warning'
            })
          } else {
            this.$loading()
            this.$API.infoChange
              .infoChangeCommitSup({ applyCodeList })
              .then(() => {
                this.$hloading()
                this.$refs.templateRef.refreshCurrentGridData()
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          }
        } else {
          this.$toast({
            content: this.$t('请选择数据进行提交'),
            type: 'warning'
          })
        }
      } else if (item.toolbar.id == 'export') {
        this.$loading()
        axios
          .post(
            '/api/supplier/tenant/supplier/info/change/apply/export',
            {
              page: {
                current: 1,
                size: 10000
              }
            },
            {
              responseType: 'blob' // 1.首先设置responseType对象格式为 blob:
            }
          )
          .then((res) => {
            this.$hloading()
            // console.log(res); //把response打出来，看下图
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
            let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

            // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = this.$t('信息变更（供方）.xlsx')
            a.click()
            // 5.释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$hloading()
            this.$toast({ content: this.$t('导出失败，请重试!'), type: 'warning' })
          })
      } else if (item.toolbar.id == 'launch') {
        this.$router.push({
          name: 'infoChangeSupDetail',
          query: {
            type: 'launch'
          }
        })
      }
    },
    handleClickCellTool(item) {
      let { tool, data } = item
      if (tool.id == 'deleted') {
        // 待提交，驳回状态的单据可以删除
        if (data.applyStatus == 10 || data.applyStatus == 30) {
          let applyCodeList = []
          applyCodeList.push(data.applyCode)
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              this.$loading()
              this.$API.infoChange
                .infoChangeDelSup({ applyCodeList })
                .then(() => {
                  this.$hloading()
                  this.$refs.templateRef.refreshCurrentGridData()
                  this.$toast({
                    content: this.$t('删除成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            }
          })
        } else {
          this.$toast({
            content: this.$t('单据状态不可以删除'),
            type: 'error'
          })
        }
      }
    },
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'infoChangeSupDetail',
        query: {
          type: 'edit',
          id: data.applyId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lifeCycle-container {
  width: 100%;
  height: 100%;
}
</style>
