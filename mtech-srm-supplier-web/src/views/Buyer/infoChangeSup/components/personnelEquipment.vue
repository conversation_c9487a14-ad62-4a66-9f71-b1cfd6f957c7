<template>
  <div class="supplier-portrait">
    <!-- 变更前：不能修改 -->
    <div class="supplier-content">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('基本信息') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('研发人员（数量）') }}</div>
              <div class="info-content-left">
                {{ staffBaseInfo.researcherNumber || '--' }}
              </div>
              <div class="info-title epls">
                {{ $t('从业5年以上研发人员（数量）') }}
              </div>
              <div class="info-content-right">
                {{ staffBaseInfo.oldResearcherNumber || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">
                {{ $t('专职供应链管理人员（数量）') }}
              </div>
              <div class="info-content-left">
                {{ staffBaseInfo.supplyManagerNumber || '--' }}
              </div>
              <div class="info-title">{{ $t('公司员工总数（数量）') }}</div>
              <div class="info-content-right">
                {{ staffBaseInfo.totalEmployeesNumber || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('技术和专业人员（数量）') }}</div>
              <div class="info-content-right">
                {{ staffBaseInfo.technologyNumber || '--' }}
              </div>
              <div class="info-title epls">
                {{ $t('技术和专业人员占总人数比率（%）') }}
              </div>
              <div class="info-content-left">
                {{ staffBaseInfo.technologyScale || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('实施、售后、运维人员数量') }}</div>
              <div class="info-content-right">
                {{ staffBaseInfo.maintenanceNumber || '--' }}
              </div>
              <div class="info-title epls">{{ $t('质量管理人员（数量）') }}</div>
              <div class="info-content-left">
                {{ staffBaseInfo.qualityManageNumber || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('管理人员（数量）') }}</div>
              <div class="info-content-right">
                {{ staffBaseInfo.managerNumber || '--' }}
              </div>
              <div class="info-title epls">{{ $t('生产人员（数量）') }}</div>
              <div class="info-content-left">
                {{ staffBaseInfo.producerNumber || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('FAE人员（数量）') }}</div>
              <div class="info-content-right">
                {{ staffBaseInfo.faePersonnelNumber || '--' }}
              </div>
              <div class="info-title epls">
                {{ $t('从业8年以上的FAE人员（数量）') }}
              </div>
              <div class="info-content-left">
                {{ staffBaseInfo.eightFaeNumber || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('产品执行标准') }}</div>
              <div class="info-content-right">
                <mt-select
                  :disabled="true"
                  v-model="staffBaseInfo.productExecutiveStandard"
                  :data-source="productList"
                  :fields="stageTypeFields"
                ></mt-select>
              </div>
              <div class="info-title epls">{{ $t('工程人员（数量）') }}</div>
              <div class="info-content-left">
                {{ staffBaseInfo.engineerNumber || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('本科学历以上（%）') }}</div>
              <div class="info-content-right">
                {{ staffBaseInfo.bachelorDegreeProportion || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('产品认证状况') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="staffBaseInfo.productCertificationStatus"
                  :multiline="true"
                  :rows="2"
                  :maxlength="1000"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('备注') }}</div>
              <div class="textareaClass">
                <mt-input
                  v-model="staffBaseInfo.remark"
                  type="text"
                  :multiline="true"
                  :rows="2"
                  :maxlength="1000"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('附件') }}</div>
              <div class="textareaClass" style="width: 75%">
                <div style="width: 100%">
                  <span
                    class="downStyle"
                    v-for="(item, index) in staffBaseInfo.staffAttachment"
                    :key="index"
                    @click="downAttachment(item)"
                    >{{ item.fileName }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('管理信息系统') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="certificateInfo"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('生产设备') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="patentInfo"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('检测/试验设备') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="testInfo"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('主要客户') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="InnovationAwards"
            ></mt-template-page>
          </div>
        </div>
      </div>
    </div>
    <!-- 变更后：修改提交 -->
    <div class="supplier-content" style="margin-left: 2%">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('基本信息') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('研发人员（数量）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.researcherNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.researcherNumber"
                    @input="changeVal('researcherNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">
                {{ $t('从业5年以上研发人员（数量）') }}
              </div>
              <div class="info-content-right">
                <div :class="[styleObj.oldResearcherNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.oldResearcherNumber"
                    @input="changeVal('oldResearcherNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">
                {{ $t('专职供应链管理人员（数量）') }}
              </div>
              <div class="info-content-left">
                <div :class="[styleObj.supplyManagerNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.supplyManagerNumber"
                    @input="changeVal('supplyManagerNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title">{{ $t('公司员工总数（数量）') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.totalEmployeesNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.totalEmployeesNumber"
                    @input="changeVal('totalEmployeesNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('技术和专业人员（数量）') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.technologyNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.technologyNumber"
                    @input="changeVal('technologyNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">
                {{ $t('技术和专业人员占总人数比率（%）') }}
              </div>
              <div class="info-content-left">
                <div :class="[styleObj.technologyScale ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    v-model="staffBaseInfoEdit.technologyScale"
                    @input="changeVal('technologyScale')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('实施、售后、运维人员数量') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.maintenanceNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.maintenanceNumber"
                    @input="changeVal('maintenanceNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">{{ $t('质量管理人员（数量）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.qualityManageNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.qualityManageNumber"
                    @input="changeVal('qualityManageNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('管理人员（数量）') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.managerNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.managerNumber"
                    @input="changeVal('managerNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">{{ $t('生产人员（数量）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.producerNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.producerNumber"
                    @input="changeVal('producerNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('FAE人员（数量）') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.faePersonnelNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.faePersonnelNumber"
                    @input="changeVal('faePersonnelNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">
                {{ $t('从业8年以上的FAE人员（数量）') }}
              </div>
              <div class="info-content-left">
                <div :class="[styleObj.eightFaeNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.eightFaeNumber"
                    @input="changeVal('eightFaeNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('产品执行标准') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.productExecutiveStandard ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="staffBaseInfoEdit.productExecutiveStandard"
                    :data-source="productList"
                    :fields="stageTypeFields"
                    @change="changeVal('productExecutiveStandard', $event)"
                  ></mt-select>
                </div>
              </div>
              <div class="info-title epls">{{ $t('工程人员（数量）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.engineerNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="staffBaseInfoEdit.engineerNumber"
                    @input="changeVal('engineerNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('本科学历以上（%）') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.bachelorDegreeProportion ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    v-model="staffBaseInfoEdit.bachelorDegreeProportion"
                    @input="changeVal('bachelorDegreeProportion')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('产品认证状况') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.productCertificationStatus ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="staffBaseInfoEdit.productCertificationStatus"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1000"
                    @input="changeVal('productCertificationStatus')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('备注') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.remark ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="staffBaseInfoEdit.remark"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1000"
                    @input="changeVal('remark')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('附件') }}</div>
              <div class="textareaClass" style="width: 75%">
                <!-- <div style="width:100%" >
                  <span class="downStyle" v-for="(item,index) in staffBaseInfoEdit.staffAttachment" :key="index"
                  @click="downAttachment(item)">{{item.fileName}}</span>
                </div> -->
                <mt-common-uploader
                  :is-single-file="false"
                  :save-url="saveUrl"
                  :download-url="downloadUrl"
                  type="line"
                  v-model="staffBaseInfoEdit.staffAttachment"
                  :key="fileListKey"
                ></mt-common-uploader>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('管理信息系统') }}</div>
          <div class="infos">
            <mt-template-page
              ref="certificateInfoEditRef"
              :use-tool-template="false"
              :template-config="certificateInfoEdit"
              @handleClickToolBar="handleClickCertificateEdit"
              @actionComplete="CertificateComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('生产设备') }}</div>
          <div class="infos">
            <mt-template-page
              ref="patentInfoEditRef"
              :use-tool-template="false"
              :template-config="patentInfoEdit"
              @handleClickToolBar="handleClickPatentEdit"
              @actionComplete="patentComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('检测/试验设备') }}</div>
          <div class="infos">
            <mt-template-page
              ref="testRef"
              :use-tool-template="false"
              :template-config="testInfoEdit"
              @handleClickToolBar="handleClickTestEdit"
              @actionComplete="testComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('主要客户') }}</div>
          <div class="infos">
            <mt-template-page
              ref="InnovationAwardsEditRef"
              :use-tool-template="false"
              :template-config="InnovationAwardsEdit"
              @handleClickToolBar="handleClickAwardsEdit"
              @actionComplete="AwardsComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  managementInfomation,
  equipment,
  salesQuantity,
  initManagementInfomation,
  initEquipment,
  initSalesQuantity,
  testColumn,
  initTestColumn
} from '../config/index'
import Component from '../config/columnComponent'
import commonData from '@/utils/constant'
export default {
  props: {
    routerType: {
      type: String,
      default: ''
    },
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    },
    applyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      saveUrl: commonData.privateFileUrl,
      downloadUrl: commonData.downloadUrl,
      fileListKey: 0,
      certificateAllData: [],
      patentAllData: [],
      AwardsAllData: [],
      testAllData: [],
      dataArr: [],
      staffBaseInfo: {},
      staffBaseInfoEdit: {},
      certificateInfo: [
        {
          gridId: '977aa5d3-1170-4476-a3f2-165c6a2adb6b',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: managementInfomation,
            dataSource: []
          }
        }
      ],
      patentInfo: [
        {
          gridId: '66da4ab1-2047-405f-8b8c-c1520b56b7ff',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: equipment,
            dataSource: []
          }
        }
      ],
      testInfo: [
        {
          gridId: '88a8ce6b-5c06-428a-bc2c-b6f96b0e72a1',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: testColumn,
            dataSource: []
          }
        }
      ],
      InnovationAwards: [
        {
          gridId: 'f42b4c8a-5dd6-40bb-8ed6-95172de624ff',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: salesQuantity,
            dataSource: []
          }
        }
      ],
      // 变更后
      certificateInfoEdit: [
        {
          gridId: 'c017cabc-d53f-4e81-8965-4027034aff1c',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initManagementInfomation,
            dataSource: [],
            rowDataBound: this.rowDataBound
          }
        }
      ],
      patentInfoEdit: [
        {
          gridId: 'b1e0c059-7330-4491-b1b4-420ad0610daa',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initEquipment,
            dataSource: [],
            rowDataBound: this.rowDataBoundPatent
          }
        }
      ],
      testInfoEdit: [
        {
          gridId: 'c3197bf5-af48-4fed-ae44-f1ab529f7742',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initTestColumn,
            dataSource: [],
            rowDataBound: this.rowDataBoundTest
          }
        }
      ],
      InnovationAwardsEdit: [
        {
          gridId: '38c66475-d9ef-43ce-abc8-64ab59877ed7',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initSalesQuantity,
            dataSource: [],
            rowDataBound: this.rowDataBoundAwards
          }
        }
      ],
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      productList: [],
      styleObj: {
        researcherNumber: true,
        oldResearcherNumber: true,
        supplyManagerNumber: true,
        technologyNumber: true,
        technologyScale: true,
        maintenanceNumber: true,
        qualityManageNumber: true,
        managerNumber: true,
        producerNumber: true,
        faePersonnelNumber: true,
        eightFaeNumber: true,
        productExecutiveStandard: true,
        engineerNumber: true,
        bachelorDegreeProportion: true,
        productCertificationStatus: true,
        remark: true
      }
    }
  },
  async mounted() {
    this.$loading()
    this.searchDevice()
    // 所有下拉框接口
    await this.getAllSelectList()

    this.initCertificateInfo()
    this.initPatentInfo()
    this.initTestInfo()
    this.initAwardsInfo()
    this.$nextTick(() => {
      this.$hloading()
    })
  },
  methods: {
    // 高亮显示修改的内容
    changeVal(item, e) {
      if (e) {
        if (e.isInteracted) {
          this.styleObj[item] = false
        }
      } else {
        this.styleObj[item] = false
      }
    },

    downAttachment(item) {
      this.$utils.downloadAttachment(item)
    },
    async getAllSelectList() {
      // 产品执行标准
      await this.getEnterpriseNature('productStandard')
    },
    async getEnterpriseNature(dictCode) {
      // this.$loading();
      await this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          // this.$hloading();
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'productStandard') {
              this.productList = data
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    searchDevice() {
      let obj = {
        applyCode: this.applyCode,
        queryArchiveRequest: {
          partnerArchiveId: this.partnerArchiveId,
          orgId: this.orgId,
          partnerRelationCode: this.partnerRelationCode,
          supplierEnterpriseId: this.supplierEnterpriseId
        }
      }
      // this.$loading();
      this.$API.infoChange
        .searchDeviceSup(obj)
        .then((res) => {
          // this.$hloading();
          let { newOne, oldOne, infoChangeRecordDTOList } = res.data
          this.staffBaseInfo = oldOne.staffInfoDTO

          let systemList = []
          let prodDeviceList = []
          let testList = []
          // 1=系统，2=设备
          oldOne.deviceDTOList.forEach((item) => {
            if (item.deviceType == 1) {
              systemList.push(item)
            } else if (item.deviceType == 2) {
              prodDeviceList.push(item)
            } else if (item.deviceType == 3) {
              testList.push(item)
            }
          })
          this.certificateInfo[0].grid = Object.assign({}, this.certificateInfo[0].grid, {
            dataSource: systemList
          })
          this.patentInfo[0].grid = Object.assign({}, this.patentInfo[0].grid, {
            dataSource: prodDeviceList
          })
          this.testInfo[0].grid = Object.assign({}, this.testInfo[0].grid, {
            dataSource: testList
          })
          this.InnovationAwards[0].grid = Object.assign({}, this.InnovationAwards[0].grid, {
            dataSource: oldOne.cooperatorDTOList
          })
          let changeEquipInfo = JSON.parse(sessionStorage.getItem('changeEquipInfoSup'))
          if (changeEquipInfo) {
            this.staffBaseInfoEdit = changeEquipInfo.staffBaseInfoEdit
            if (Array.isArray(this.staffBaseInfoEdit?.staffAttachment)) {
              this.staffBaseInfoEdit?.staffAttachment.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            this.AwardsAllData = JSON.parse(changeEquipInfo.equipAwardsAllData)
            this.certificateAllData = JSON.parse(changeEquipInfo.equipAllData)
            this.patentAllData = JSON.parse(changeEquipInfo.equipPatentAllData)
            this.testAllData = JSON.parse(changeEquipInfo.testAllData)
            this.$set(this.certificateInfoEdit[0].grid, 'dataSource', this.certificateAllData)
            this.$set(this.patentInfoEdit[0].grid, 'dataSource', this.patentAllData)
            this.$set(this.testInfoEdit[0].grid, 'dataSource', this.testAllData)
            this.$set(this.InnovationAwardsEdit[0].grid, 'dataSource', this.AwardsAllData)
          } else {
            let systemListEdit = []
            let prodDeviceListEdit = []
            let testListEdit = []
            // 1=系统，2=设备
            newOne.deviceDTOList.forEach((item) => {
              if (item.deviceType == 1) {
                systemListEdit.push(item)
              } else if (item.deviceType == 2) {
                prodDeviceListEdit.push(item)
              } else if (item.deviceType == 3) {
                testListEdit.push(item)
              }
            })
            this.staffBaseInfoEdit = JSON.parse(JSON.stringify(newOne.staffInfoDTO))
            if (Array.isArray(this.staffBaseInfoEdit?.staffAttachment)) {
              this.staffBaseInfoEdit?.staffAttachment.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            this.certificateInfoEdit[0].grid = Object.assign({}, this.certificateInfoEdit[0].grid, {
              dataSource: systemListEdit
            })
            this.patentInfoEdit[0].grid = Object.assign({}, this.patentInfoEdit[0].grid, {
              dataSource: prodDeviceListEdit
            })
            this.testInfoEdit[0].grid = Object.assign({}, this.testInfoEdit[0].grid, {
              dataSource: testListEdit
            })
            this.InnovationAwardsEdit[0].grid = Object.assign(
              {},
              this.InnovationAwardsEdit[0].grid,
              { dataSource: newOne.cooperatorDTOList }
            )
          }
          this.fileListKey++
          // 变更字段:有缓存拿缓存，无缓存，拿infoChangeRecordDTOList
          // 高亮显示修改的内容
          let baseStyle = JSON.parse(sessionStorage.getItem('equipStyleSup'))
          if (baseStyle) {
            this.styleObj = baseStyle
          } else {
            if (infoChangeRecordDTOList.length > 0) {
              infoChangeRecordDTOList.forEach((item) => {
                if (!item.listType) {
                  // 变更动作（0:更新 1:删除 2:列表新增）
                  if (item.changeFlag == 1 || item.changeFlag == 2) {
                    let str = item.changeField
                    this.styleObj[str] = false
                  }
                }
              })
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 管理
    initCertificateInfo() {
      initManagementInfomation.length = 0
      const column = this.formatTableColumnCertificate(managementInfomation)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initManagementInfomation.push(item)
      })
    },
    formatTableColumnCertificate(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'systemName') {
          // 系统名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'product') {
          // 产品
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'implementer') {
          // 实施方
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'launchDate') {
          // 上线日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBound(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    CertificateComplete(args) {
      this.certificateAllData = this.getCertificateRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(managementInfomation))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.certificateInfoEditRef
              .getCurrentUsefulRef()
              .gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.certificateAllData = this.certificateAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.certificateInfoEdit[0].grid, 'dataSource', this.certificateAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.certificateInfoEdit[0].grid.dataSource = this.certificateAllData
      }
    },
    getCertificateRow() {
      let rows = this.$refs.certificateInfoEditRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        item.deviceType = 1
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'certifi' + num
      })
      return rows
    },
    handleClickCertificateEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.certificateInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.certificateAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.certificateAllData.splice(sindex, 1)
                  }
                })
              })

              this.$set(this.certificateInfoEdit[0].grid, 'dataSource', this.certificateAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.certificateInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // 测试设备
    initTestInfo() {
      initTestColumn.length = 0
      const column = this.formatTableColumnTest(testColumn)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initTestColumn.push(item)
      })
    },
    formatTableColumnTest(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'equipmentName') {
          // 主要设备名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'specifications') {
          // 规格
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'launchDate') {
          // 制造出厂日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundTest(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    testComplete(args) {
      this.testAllData = this.getTestRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(testColumn))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.testRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.testAllData = this.testAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.testInfoEdit[0].grid, 'dataSource', this.testAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.testInfoEdit[0].grid.dataSource = this.testAllData
      }
    },
    getTestRow() {
      let rows = this.$refs.testRef.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        item.deviceType = 3
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'test' + num
      })
      return rows
    },
    handleClickTestEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.testRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.testAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.testAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.testInfoEdit[0].grid, 'dataSource', this.testAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.testRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // 生产设备
    initPatentInfo() {
      initEquipment.length = 0
      const column = this.formatTableColumnPatent(equipment)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initEquipment.push(item)
      })
    },
    formatTableColumnPatent(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'equipmentName') {
          // 主要设备名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'specifications') {
          // 规格
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'number') {
          // 数量
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'launchDate') {
          // 制造出厂日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'dailyProductionNormal') {
          // 日生产能力(正常)
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'dailyProductionMax') {
          // 日生产能力(最大)
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'monthlyProductionNormal') {
          // 月生产能力(正常)
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'monthlyProductionMax') {
          // 月生产能力(最大)
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'bottleneck') {
          // 是否瓶颈
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            dataList: [
              { text: this.$t('是'), value: 1 },
              { text: this.$t('否'), value: 0 }
            ]
          })
        } else if (col.field === 'equipmentAdditionPlan') {
          // 设备增加计划
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundPatent(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    patentComplete(args) {
      this.patentAllData = this.getPatentRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(equipment))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.patentInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.patentAllData = this.patentAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.patentInfoEdit[0].grid, 'dataSource', this.patentAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.patentInfoEdit[0].grid.dataSource = this.patentAllData
      }
    },
    getPatentRow() {
      let rows = this.$refs.patentInfoEditRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        item.deviceType = 2
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'patent' + num
      })
      return rows
    },
    handleClickPatentEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.patentInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.patentAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.patentAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.patentInfoEdit[0].grid, 'dataSource', this.patentAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.patentInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // 主要客户
    initAwardsInfo() {
      initSalesQuantity.length = 0
      const column = this.formatTableColumnAwards(salesQuantity)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initSalesQuantity.push(item)
      })
    },
    formatTableColumnAwards(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'cooperatorRank') {
          // 排序
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'customerName') {
          // 客户名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'supplyProduct') {
          // 供应产品
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'salesVolume') {
          // 销售额
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'proportion') {
          // 比例（%）
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'currentMonthlySales') {
          // 当前月销售额
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'currentMonthlyProportion') {
          // 当前月比例
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'previousOneMonthSales') {
          // 前一月销售额
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'previousOneMonthProportion') {
          // 前一月比例
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'previousTwoMonthSales') {
          // 前两月销售额
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'previousTwoMonthProportion') {
          // 前两月比例
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'cooperationStartDate') {
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundAwards(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    AwardsComplete(args) {
      this.AwardsAllData = this.getAwardsRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(salesQuantity))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(
              rowIndex
            )
          } else {
            this.AwardsAllData = this.AwardsAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.InnovationAwardsEdit[0].grid, 'dataSource', this.AwardsAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.InnovationAwardsEdit[0].grid.dataSource = this.AwardsAllData
      }
    },
    getAwardsRow() {
      let rows =
        this.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'Awards' + num
      })
      return rows
    },
    handleClickAwardsEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.AwardsAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.AwardsAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.InnovationAwardsEdit[0].grid, 'dataSource', this.AwardsAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-filled.e-input-group.e-control-wrapper textarea.e-input {
  padding-bottom: 8px;
}
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;
  display: flex;

  .supplier-content {
    width: 49%;
  }

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            word-break: break-all;
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 75%;
            padding: 5px 10px;
            .downStyle {
              cursor: pointer;
              color: #00469c;
              margin: 0 5px;
            }
            /deep/ .mt-input {
              width: 100%;
            }
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
          .bgColor {
            background: rgba(245, 252, 255, 1);
            width: 100%;
            padding: 0 5px;
            /deep/.mt-input-number input {
              width: 100%;
            }
          }
          .highlightColor {
            background: #e8f0fe;
            width: 100%;
            padding: 0 5px;
            /deep/.e-input-group {
              color: red !important;
            }
            /deep/.mt-input-number input {
              width: 100%;
            }
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
