<template>
  <div class="supplier-portrait">
    <!-- 变更前：不能修改 -->
    <div class="supplier-content">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('基本信息') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRefLeft"
              :use-tool-template="false"
              :template-config="baseInfo"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShow">
        <div class="b-info">
          <div class="b-info-title">{{ $t('主要原材料采购周期') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef1"
              :use-tool-template="false"
              :template-config="procurementInfo"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShow">
        <div class="b-info">
          <div class="b-info-title">{{ $t('生产周期') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef2"
              :use-tool-template="false"
              :template-config="productionPhase"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShow">
        <div class="b-info">
          <div class="b-info-title">{{ $t('委外加工') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef3"
              :use-tool-template="false"
              :template-config="subcontract"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShow">
        <div class="b-info">
          <div class="b-info-title">{{ $t('运输时间') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef4"
              :use-tool-template="false"
              :template-config="haulageTime"
              :key="haulageKey"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShow">
        <div class="b-info">
          <div class="b-info-title">{{ $t('分析结论') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('原材料采购周期方面') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="analyse.rawMaterialsAspect"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('生产周期方面') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="analyse.produceCycleAspect"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('运输时间方面') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="analyse.transportTimeAspect"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 变更后：修改提交 -->
    <div class="supplier-content" style="margin-left: 2%">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('基本信息') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateBaseRef"
              :use-tool-template="false"
              :template-config="baseInfoEdit"
              @handleClickToolBar="handleClickBaseEdit"
              @actionComplete="baseComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShowEdit">
        <div class="b-info">
          <div class="b-info-title">{{ $t('主要原材料采购周期') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateProcureRef"
              :use-tool-template="false"
              :template-config="procurementInfoEdit"
              @handleClickToolBar="handleClickProcureEdit"
              @actionComplete="procureComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShowEdit">
        <div class="b-info">
          <div class="b-info-title">{{ $t('生产周期') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templatePhaseRef"
              :use-tool-template="false"
              :template-config="productionPhaseEdit"
              @handleClickToolBar="handleClickPhaseEdit"
              @actionComplete="phaseComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShowEdit">
        <div class="b-info">
          <div class="b-info-title">{{ $t('委外加工') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateSubconRef"
              :use-tool-template="false"
              :template-config="subcontractEdit"
              @handleClickToolBar="handleClickSubconEdit"
              @actionComplete="subconComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShowEdit">
        <div class="b-info">
          <div class="b-info-title">{{ $t('运输时间') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateTimeRef"
              :use-tool-template="false"
              :template-config="haulageTimeEdit"
              @handleClickToolBar="handleClickTimeEdit"
              @actionComplete="timeComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox" v-show="isShowEdit">
        <div class="b-info">
          <div class="b-info-title">{{ $t('分析结论') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('原材料采购周期方面') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.rawMaterialsAspect ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="analyseEdit.rawMaterialsAspect"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1000"
                    @input="changeVal('rawMaterialsAspect')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('生产周期方面') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.produceCycleAspect ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="analyseEdit.produceCycleAspect"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1000"
                    @input="changeVal('produceCycleAspect')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('运输时间方面') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.transportTimeAspect ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="analyseEdit.transportTimeAspect"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1000"
                    @input="changeVal('transportTimeAspect')"
                  ></mt-input>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  baseInfoColumn,
  procurementCycle,
  productionColumn,
  subcontractColumn,
  haulageColumn,
  initBaseInfoColumn,
  initProcurementCycle,
  initProductionColumn,
  initSubcontractColumn,
  initHaulageColumn
} from '../config/index'
import Component from '../config/columnComponent'
export default {
  props: {
    routerType: {
      type: String,
      default: ''
    },
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    },
    applyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      categoryRelationCode: '',
      rightItem: {},
      isShow: false,
      isShowEdit: false,
      mainId: '',
      mainIdEdit: '',
      infoForm: {},
      analyse: {},
      analyseEdit: {},
      baseInfo: [
        {
          gridId: '2c12f937-bf5b-4186-a927-7d3b8c31c3c3',
          toolbar: {
            useBaseConfig: false,
            tools: [[]]
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: baseInfoColumn,
            rowSelected: this.rowSelected,
            selectionSettings: {
              type: 'Single',
              mode: 'Row',
              enableToggle: false,
              checkboxMode: 'ResetOnRowClick'
            },
            dataSource: []
          }
        }
      ],
      procurementInfo: [
        {
          gridId: 'ddbd991f-f08e-4414-814a-0905bcc42a5e',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: procurementCycle,
            dataSource: []
          }
        }
      ],
      productionPhase: [
        {
          gridId: '1b593e23-ef1c-42fd-bcf5-b70f83e74fd2',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: productionColumn,
            dataSource: []
          }
        }
      ],
      subcontract: [
        {
          gridId: 'ae18b21b-0fe5-4316-8b29-a943f00f4cb9',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: subcontractColumn,
            dataSource: []
          }
        }
      ],
      haulageKey: 1,
      haulageTime: [
        {
          gridId: '2d9d5baf-f024-4c6a-9d4d-75c2a745201f',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: [],
            dataSource: []
          }
        }
      ],

      // 变更
      baseInfoEdit: [
        {
          gridId: '1fec0ecc-f4a3-4702-ad73-fbe873c54202',
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [[]]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            // selectionSettings: { type: "Single", mode: "Row", enableToggle: false, checkboxMode:'ResetOnRowClick' },
            columnData: initBaseInfoColumn,
            dataSource: [],
            rowDataBound: this.rowDataBound
          }
        }
      ],
      procurementInfoEdit: [
        {
          gridId: '776a6673-9180-4332-a4fe-a1cb1e23e10a',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initProcurementCycle,
            dataSource: [],
            rowDataBound: this.rowDataBoundProcurement
          }
        }
      ],
      productionPhaseEdit: [
        {
          gridId: '554c7c23-9e91-4209-9e34-79dc19084856',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initProductionColumn,
            dataSource: [],
            rowDataBound: this.rowDataBoundProduction
          }
        }
      ],
      subcontractEdit: [
        {
          gridId: '78e87ad9-d880-42af-8699-e4f0818019c2',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initSubcontractColumn,
            dataSource: [],
            rowDataBound: this.rowDataBoundContract
          }
        }
      ],
      haulageTimeEdit: [
        {
          gridId: '785b6391-6e3b-436a-a018-00951dbef958',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initHaulageColumn,
            dataSource: [],
            rowDataBound: this.rowDataBoundTime
          }
        }
      ],
      baseAllData: [],
      procureAllData: [],
      productionAllData: [],
      subconAllData: [],
      hauAllData: [],

      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      deliveryList: [],
      transportList: [],
      styleObj: {
        rawMaterialsAspect: true,
        produceCycleAspect: true,
        transportTimeAspect: true
      }
    }
  },
  async mounted() {
    this.$loading()
    this.searchCycle()
    // 所有下拉框接口
    await this.getAllSelectList()

    this.initBaseColumn()
    this.initProcureColumn()
    this.initProductColumn()
    this.initSubconColumn()
    this.initHauColumn()
    this.$nextTick(() => {
      this.$hloading()
    })
  },
  methods: {
    // 高亮显示修改的内容
    changeVal(item, e) {
      //select框初始化首次触发change
      if (e) {
        if (e.isInteracted) {
          this.styleObj[item] = false
        }
      } else {
        this.styleObj[item] = false
      }
    },
    async getAllSelectList() {
      // 交货地点
      await this.getEnterpriseNature('deliveryPlace')
      // 运输方式
      await this.getEnterpriseNature('transportType')
      this.nextThen()
    },
    async getEnterpriseNature(dictCode) {
      // this.$loading();
      await this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          // this.$hloading();
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'deliveryPlace') {
              this.deliveryList = data
            } else if (dictCode == 'transportType') {
              this.transportList = data
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    nextThen() {
      this.$set(
        this.haulageTime[0].grid,
        'columnData',
        haulageColumn(this.deliveryList, this.transportList)
      )
      this.haulageKey++
    },
    // 查询第一个表格
    searchCycle() {
      let obj = {
        applyCode: this.applyCode,
        queryArchiveRequest: {
          partnerArchiveId: this.partnerArchiveId,
          orgId: this.orgId,
          partnerRelationCode: this.partnerRelationCode,
          supplierEnterpriseId: this.supplierEnterpriseId
        }
      }
      // this.$loading();
      this.$API.infoChange
        .searchCycleSup(obj)
        .then((res) => {
          // this.$hloading();
          let { newOne, oldOne, infoChangeRecordDTOList } = res.data
          // 添加字段存储子表的数据以及点击次数
          oldOne.deliveryInfoInsideDTOList.forEach((item) => {
            item.childrenDataSource = {}
            item.clickNum = 0
          })
          this.baseInfo[0].grid = Object.assign({}, this.baseInfo[0].grid, {
            dataSource: oldOne.deliveryInfoInsideDTOList
          })
          let changeCycleInfo = JSON.parse(sessionStorage.getItem('changeCycleInfoSup'))
          if (changeCycleInfo) {
            this.baseAllData = changeCycleInfo
            this.$set(this.baseInfoEdit[0].grid, 'dataSource', this.baseAllData)
            if (this.baseAllData.length > 0) {
              this.baseInfoEdit[0].toolbar.tools = [
                [
                  {
                    id: 'edit',
                    icon: 'icon_list_delete',
                    title: this.$t('保存')
                  }
                ]
              ]
            }
          } else {
            newOne.deliveryInfoInsideDTOList.forEach((item) => {
              item.childrenDataSource = {}
              item.clickNum = 0
            })
            this.baseInfoEdit[0].grid = Object.assign({}, this.baseInfoEdit[0].grid, {
              dataSource: newOne.deliveryInfoInsideDTOList
            })
            if (newOne.deliveryInfoInsideDTOList.length > 0) {
              this.baseInfoEdit[0].toolbar.tools = [
                [
                  {
                    id: 'edit',
                    icon: 'icon_list_delete',
                    title: this.$t('保存')
                  }
                ]
              ]
            }
          }

          // 变更字段:有缓存拿缓存，无缓存，拿infoChangeRecordDTOList
          // 高亮显示修改的内容
          let baseStyle = JSON.parse(sessionStorage.getItem('cycleStyleSup'))
          if (baseStyle) {
            this.styleObj = baseStyle
          } else {
            if (infoChangeRecordDTOList.length > 0) {
              infoChangeRecordDTOList.forEach((item) => {
                if (!item.listType) {
                  if (item.changeFlag == 1 || item.changeFlag == 2) {
                    let str = item.changeField
                    this.styleObj[str] = false
                  }
                }
              })
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 点击第一个表格的数据，拿到当前行id，查询其他表格
    // supplyCategory 唯一，找到另一侧的数据选中
    async rowSelected(filed) {
      let { data } = filed
      this.mainId = data.id
      // 对右侧表格进行操作，切换时保存右侧子表

      // 进行备份:this.categoryRelationCode 还没有被赋值，仍然是前一条数据的值，保存前一条数据
      //右侧主表
      let allData = this.$refs.templateBaseRef.getCurrentTabRef().grid.ej2Instances.currentViewData
      if (this.categoryRelationCode != '') {
        allData.forEach((item) => {
          if (this.categoryRelationCode == item.categoryRelationCode) {
            let obj = {}
            this.analyseEdit.id = this.mainIdEdit
            obj.deliveryInfoInsideDTO = this.analyseEdit
            obj.cycleInsideDTOList = []
              .concat(this.procureAllData)
              .concat(this.productionAllData)
              .concat(this.subconAllData)
              .concat(this.hauAllData)
            item.childrenDataSource = obj
          }
        })
      }

      this.categoryRelationCode = data.categoryRelationCode

      // 联动，点左侧右侧相关选中   只做一边，右侧不让点
      let supplyCategory = data.supplyCategory
      allData.forEach((item, index) => {
        if (item.supplyCategory == supplyCategory) {
          this.rightItem = item
          this.mainIdEdit = item.id //右侧主表id
          this.$refs.templateBaseRef.getCurrentUsefulRef().gridRef.ejsRef.selectRows([index])
        }
      })

      // 拿到主表所有数据，是第一次点击请求接口，之后点击从备份里拿
      if (this.rightItem.clickNum == 0) {
        await this.searchCycleById()
        this.rightItem.clickNum++ //记录点击次数
      } else {
        await this.searchCycleById()
        // 不切换tab的情况下：拿到右侧最新信息赋值
        this.getData(this.rightItem)
      }
      this.isShow = true
      this.isShowEdit = true
    },

    // 给子表赋值
    getData(allData) {
      let materialListEdit = []
      let productionListEdit = []
      let extListEdit = []
      let transmitListEdit = []
      let cycleInsideDTOList = allData.childrenDataSource.cycleInsideDTOList
      cycleInsideDTOList.forEach((item) => {
        if (item.cycleType == 1) {
          materialListEdit.push(item)
        } else if (item.cycleType == 2) {
          productionListEdit.push(item)
        } else if (item.cycleType == 3) {
          extListEdit.push(item)
        } else if (item.cycleType == 4) {
          transmitListEdit.push(item)
        }
      })

      this.procurementInfoEdit[0].grid = Object.assign({}, this.procurementInfoEdit[0].grid, {
        dataSource: materialListEdit
      })
      this.productionPhaseEdit[0].grid = Object.assign({}, this.productionPhaseEdit[0].grid, {
        dataSource: productionListEdit
      })
      this.subcontractEdit[0].grid = Object.assign({}, this.subcontractEdit[0].grid, {
        dataSource: extListEdit
      })
      this.haulageTimeEdit[0].grid = Object.assign({}, this.haulageTimeEdit[0].grid, {
        dataSource: transmitListEdit
      })
      this.analyseEdit = JSON.parse(
        JSON.stringify(allData.childrenDataSource.deliveryInfoInsideDTO)
      )
    },

    async searchCycleById() {
      this.$loading()
      await this.$API.infoChange
        .searchCycleByIdSup({
          newId: this.mainIdEdit,
          oldId: this.mainId
        })
        .then((res) => {
          this.$hloading()
          let { newOne, oldOne } = res.data
          //  if(type == 'left'){
          let materialList = []
          let productionList = []
          let extList = []
          let transmitList = []
          // 1=原材料，2=生产周期，3=委外加工 4=运输
          oldOne.cycleInsideDTOList.forEach((item) => {
            if (item.cycleType == 1) {
              materialList.push(item)
            } else if (item.cycleType == 2) {
              productionList.push(item)
            } else if (item.cycleType == 3) {
              extList.push(item)
            } else if (item.cycleType == 4) {
              transmitList.push(item)
            }
          })
          this.procurementInfo[0].grid = Object.assign({}, this.procurementInfo[0].grid, {
            dataSource: materialList
          })
          this.productionPhase[0].grid = Object.assign({}, this.productionPhase[0].grid, {
            dataSource: productionList
          })
          this.subcontract[0].grid = Object.assign({}, this.subcontract[0].grid, {
            dataSource: extList
          })
          this.haulageTime[0].grid = Object.assign({}, this.haulageTime[0].grid, {
            dataSource: transmitList
          })
          this.analyse = oldOne.deliveryInfoInsideDTO
          //  }else if(type == 'right'){
          let changeCycleInfo = JSON.parse(sessionStorage.getItem('changeCycleInfo'))
          if (changeCycleInfo) {
            changeCycleInfo.forEach((item) => {
              if (item.categoryRelationCode == this.categoryRelationCode) {
                this.getData(item)
              }
            })
          } else if (this.rightItem.clickNum == 0) {
            let materialListEdit = []
            let productionListEdit = []
            let extListEdit = []
            let transmitListEdit = []
            newOne.cycleInsideDTOList.forEach((item) => {
              if (item.cycleType == 1) {
                materialListEdit.push(item)
              } else if (item.cycleType == 2) {
                productionListEdit.push(item)
              } else if (item.cycleType == 3) {
                extListEdit.push(item)
              } else if (item.cycleType == 4) {
                transmitListEdit.push(item)
              }
            })

            this.procurementInfoEdit[0].grid = Object.assign({}, this.procurementInfoEdit[0].grid, {
              dataSource: materialListEdit
            })
            this.productionPhaseEdit[0].grid = Object.assign({}, this.productionPhaseEdit[0].grid, {
              dataSource: productionListEdit
            })
            this.subcontractEdit[0].grid = Object.assign({}, this.subcontractEdit[0].grid, {
              dataSource: extListEdit
            })
            this.haulageTimeEdit[0].grid = Object.assign({}, this.haulageTimeEdit[0].grid, {
              dataSource: transmitListEdit
            })
            this.analyseEdit = JSON.parse(JSON.stringify(newOne.deliveryInfoInsideDTO))
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    // 基本信息
    initBaseColumn() {
      initBaseInfoColumn.length = 0
      const column = this.formatTableColumnBase(baseInfoColumn)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initBaseInfoColumn.push(item)
      })
    },
    formatTableColumnBase(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'supplyCategory') {
          // 供货类别
          defaultCol.editTemplate = Component.notEdit({
            dataKey: col.field
          })
        } else if (col.field === 'categoryDescription') {
          // 类别描述
          defaultCol.editTemplate = Component.notEdit({
            dataKey: col.field
          })
        } else if (col.field === 'normalProcureCycle') {
          // 正常采购周期
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'minProcureCycle') {
          // 最短采购周期
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'rawMaterialRemark') {
          // 主要原材料备注
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'normalProduceCycle') {
          // 正常生产周期
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'minProduceCycle') {
          // 最短生产周期
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'produceCycleRemark') {
          // 生产周期备注
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBound(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    baseComplete(args) {
      this.baseAllData = this.getBaseRow()
      if (args.requestType == 'save') {
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.baseInfoEdit[0].grid.dataSource = this.baseAllData
      }
    },
    getBaseRow() {
      let rows = this.$refs.templateBaseRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'base' + num
      })
      return rows
    },
    handleClickBaseEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templateBaseRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.baseAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.baseAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.baseInfoEdit[0].grid, 'dataSource', this.baseAllData)
              this.isShowEdit = false
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.templateBaseRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },

    // 主要原材料
    initProcureColumn() {
      initProcurementCycle.length = 0
      const column = this.formatTableColumnProcure(procurementCycle)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initProcurementCycle.push(item)
      })
    },
    formatTableColumnProcure(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'rawMaterialName') {
          // 原材料名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'supplier') {
          // 供应厂商
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'purchaseCycle') {
          // 采购周期
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'bottleneck') {
          // 是否瓶颈
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            dataList: [
              { text: this.$t('是'), value: 1 },
              { text: this.$t('否'), value: 0 }
            ]
          })
        } else if (col.field === 'safetyStock') {
          // 安全库存量
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'supplierContactMode') {
          // 供货商联系方式
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundProcurement(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    procureComplete(args) {
      this.procureAllData = this.getProcureRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(procurementCycle))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.templateProcureRef
              .getCurrentUsefulRef()
              .gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.procureAllData = this.procureAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.procurementInfoEdit[0].grid, 'dataSource', this.procureAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.procurementInfoEdit[0].grid.dataSource = this.procureAllData
      }
    },
    getProcureRow() {
      let rows = this.$refs.templateProcureRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        item.cycleType = 1
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'procure' + num
        item.categoryRelationCode = this.categoryRelationCode
      })
      return rows
    },
    handleClickProcureEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templateProcureRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.procureAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.procureAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.procurementInfoEdit[0].grid, 'dataSource', this.procureAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        //
        this.$refs.templateProcureRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },

    // 生产周期
    initProductColumn() {
      initProductionColumn.length = 0
      const column = this.formatTableColumnProduct(productionColumn)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initProductionColumn.push(item)
      })
    },
    formatTableColumnProduct(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'processName') {
          // 工序名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'useEquipment') {
          // 使用设备
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'processTime') {
          // 工序时间
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'bottleneck') {
          // 是否瓶颈
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            dataList: [
              { text: this.$t('是'), value: 1 },
              { text: this.$t('否'), value: 0 }
            ]
          })
        } else if (col.field === 'finishedProductInventory') {
          // 成品库存量
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundProduction(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    phaseComplete(args) {
      this.productionAllData = this.getPhaseRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(productionColumn))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.templatePhaseRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.productionAllData = this.productionAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.productionPhaseEdit[0].grid, 'dataSource', this.productionAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.productionPhaseEdit[0].grid.dataSource = this.productionAllData
      }
    },
    getPhaseRow() {
      let rows = this.$refs.templatePhaseRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        item.cycleType = 2
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'phase' + num
        item.categoryRelationCode = this.categoryRelationCode
      })
      return rows
    },
    handleClickPhaseEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templatePhaseRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.productionAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.productionAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.productionPhaseEdit[0].grid, 'dataSource', this.productionAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.templatePhaseRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },

    // 委外加工
    initSubconColumn() {
      initSubcontractColumn.length = 0
      const column = this.formatTableColumnSub(subcontractColumn)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initSubcontractColumn.push(item)
      })
    },
    formatTableColumnSub(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'processName') {
          // 工序名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'supplier') {
          // 供货商
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'purchaseCycle') {
          // 采购周期（天）
          defaultCol.editTemplate = Component.input({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'bottleneck') {
          // 是否瓶颈
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            dataList: [
              { text: this.$t('是'), value: 1 },
              { text: this.$t('否'), value: 0 }
            ]
          })
        } else if (col.field === 'safetyStock') {
          // 安全库存量
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'supplierContactMode') {
          // 供货商联系方式
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundContract(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    subconComplete(args) {
      this.subconAllData = this.getSubconRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(subcontractColumn))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.templateSubconRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.subconAllData = this.subconAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.subcontractEdit[0].grid, 'dataSource', this.subconAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.subcontractEdit[0].grid.dataSource = this.subconAllData
      }
    },
    getSubconRow() {
      let rows = this.$refs.templateSubconRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        item.cycleType = 3
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'sub' + num
        item.categoryRelationCode = this.categoryRelationCode
      })
      return rows
    },
    handleClickSubconEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templateSubconRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.subconAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.subconAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.subcontractEdit[0].grid, 'dataSource', this.subconAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.templateSubconRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },

    // 运输时间
    initHauColumn() {
      initHaulageColumn.length = 0
      const column = this.formatTableColumnTime(
        haulageColumn(this.deliveryList, this.transportList)
      )
      const columns = [].concat(column)
      columns.forEach((item) => {
        initHaulageColumn.push(item)
      })
    },
    formatTableColumnTime(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'shipmentPlace') {
          // 发货地点
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'deliveryPlace') {
          // 交货地点
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            fields: this.stageTypeFields,
            dataList: this.deliveryList
          })
        } else if (col.field === 'transmitType') {
          // 运输方式
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            fields: this.stageTypeFields,
            dataList: this.transportList
          })
        } else if (col.field === 'distance') {
          // 路程
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'transmitTime') {
          // 运输时间
          defaultCol.editTemplate = Component.number({
            dataKey: col.field
          })
        } else if (col.field === 'normalProduceCycle') {
          // 正常交货周期
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'minProduceCycle') {
          // 最短交货周期
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'transmitRemark') {
          // 备注
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundTime(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    timeComplete(args) {
      this.hauAllData = this.getTimeRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = haulageColumn()
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.templateTimeRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.hauAllData = this.hauAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.haulageTimeEdit[0].grid, 'dataSource', this.hauAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.haulageTimeEdit[0].grid.dataSource = this.hauAllData
      }
    },
    getTimeRow() {
      let rows = this.$refs.templateTimeRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        item.cycleType = 4
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'time' + num
        item.categoryRelationCode = this.categoryRelationCode
      })
      return rows
    },
    handleClickTimeEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templateTimeRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.hauAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.hauAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.haulageTimeEdit[0].grid, 'dataSource', this.hauAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.templateTimeRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-filled.e-input-group.e-control-wrapper textarea.e-input {
  padding-bottom: 8px;
}
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;
  display: flex;

  .supplier-content {
    width: 49%;
  }

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            word-break: break-all;
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 85%;
            padding: 5px 10px;
            /deep/ .mt-input {
              width: 100%;
            }
          }
          .bgColor {
            background: rgba(245, 252, 255, 1);
            width: 100%;
            padding: 0 5px;
            /deep/.mt-input-number input {
              width: 100%;
            }
          }
          .highlightColor {
            background: #e8f0fe;
            width: 100%;
            padding: 0 5px;

            /deep/.e-input-group {
              color: red !important;
            }
            /deep/.mt-input-number input {
              width: 100%;
            }
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
        /deep/.e-gridcontent {
          height: 162px;
          .e-content {
            height: 100% !important;
          }
        }
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
