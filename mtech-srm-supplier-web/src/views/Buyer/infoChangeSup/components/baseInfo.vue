<template>
  <div class="supplier-portrait">
    <!-- 变更前：不能修改 -->
    <div class="supplier-content" style="box-shadow: 0 0 20px 0px #999">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('基本信息') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供应商名称') }}</div>
              <div class="info-content-left">
                {{ baseInfo.supplierName || '--' }}
              </div>
              <div class="info-title">{{ $t('供应商英文名称') }}</div>
              <div class="info-content-left">
                {{ baseInfo.supplierNameEn || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('供应商简称') }}</div>
              <div class="info-content-right">
                {{ baseInfo.supplierShortName || '--' }}
              </div>
              <div class="info-title">{{ $t('供应商类型') }}</div>
              <div class="info-content-left">
                <mt-select
                  :disabled="true"
                  v-model="baseInfo.supplierType"
                  :data-source="supplierList"
                  :fields="stageTypeFields"
                ></mt-select>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('企业性质') }}</div>
              <div class="info-content-right">
                <mt-select
                  :disabled="true"
                  v-model="baseInfo.enterpriseNature"
                  :data-source="enterpriseList"
                  :fields="stageTypeFields"
                ></mt-select>
              </div>
              <div class="info-title">{{ $t('所属行业') }}</div>
              <div class="info-content-left">
                <mt-select
                  :disabled="true"
                  v-model="baseInfo.industryBelong"
                  :data-source="industryList"
                  :fields="stageTypeFields"
                ></mt-select>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('行业排名') }}</div>
              <div class="info-content-right">
                <mt-select
                  :disabled="true"
                  v-model="baseInfo.industryRank"
                  :data-source="rankList"
                  :fields="stageTypeFields"
                ></mt-select>
              </div>
              <div class="info-title">{{ $t('行业经验（年）') }}</div>
              <div class="info-content-left">
                {{ baseInfo.industryEnterprise || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('企业电话') }}</div>
              <div class="info-content-right">
                {{ baseInfo.enterprisePhone || '--' }}
              </div>
              <div class="info-title">{{ $t('电子邮箱') }}</div>
              <div class="info-content-left">
                {{ baseInfo.enterpriseEmail || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('邮编') }}</div>
              <div class="info-content-right">
                {{ baseInfo.zipCode || '--' }}
              </div>
              <div class="info-title">{{ $t('传真') }}</div>
              <div class="info-content-left">
                {{ baseInfo.fax || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('法人代表') }}</div>
              <div class="info-content-right">
                {{ baseInfo.legalRepresentative || '--' }}
              </div>
              <div class="info-title">{{ $t('企业网站') }}</div>
              <div class="info-content-left">
                {{ baseInfo.website || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('公司成立时间') }}</div>
              <div class="info-content-right">
                {{ checkDate(baseInfo.establishmentTime) }}
              </div>
              <div class="info-title epls">{{ $t('国家/地区') }}</div>
              <div class="info-content-left">
                <!-- <mt-select
                  :disabled="true"
                    v-model="baseInfo.countryRegion"
                    :dataSource="countryLists"
                    :fields="countryFields"
                  ></mt-select> -->
                <!-- <MasterdataDropdownSelects
                  :remote-search="true"
                  v-model="baseInfo.countryRegion"
                  :fields="countryFields"
                  :load-data="loadCountryData"
                  :disabled="true"
                  :title-switch="false"
                  select-type="country"
                ></MasterdataDropdownSelects> -->
                <RemoteAutocomplete
                  v-model="baseInfo.countryRegion"
                  :disabled="true"
                  :fields="{ text: 'shortName', value: 'countryCode' }"
                  url="/masterDataManagement/tenant/country/paged-query"
                  :rule-params="ruleParams"
                  :load-data="loadCountryData"
                  :search-fields="[
                    'id',
                    'alpha2Code',
                    'countryCode',
                    'englishShortName',
                    'numericCode',
                    'shortName',
                    'statusDescription'
                  ]"
                  :title-switch="false"
                  select-type="country"
                >
                </RemoteAutocomplete>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('省份') }}</div>
              <div class="info-content-right">
                <mt-select
                  :disabled="true"
                  v-model="baseInfo.province"
                  :data-source="provinceList"
                  :fields="provinceFields"
                ></mt-select>
              </div>
              <div class="info-title epls">{{ $t('市/区') }}</div>
              <div class="info-content-left">
                {{ baseInfo.city || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">
                {{ $t('工厂/仓库距离TCL（公里）') }}
              </div>
              <div class="info-content-right">
                {{ baseInfoInsideDTO.supplyDistance || '--' }}
              </div>
              <div class="info-title">{{ $t('工厂面积') }}</div>
              <div class="info-content-left">
                {{ baseInfo.factoryArea || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('同行业主力客户数量') }}</div>
              <div class="info-content-right">
                {{ baseInfoExtDTO.mainCustomerNumber || '--' }}
              </div>
              <div class="info-title epls">{{ $t('同行业主力客户名称') }}</div>
              <div class="info-content-left">
                {{ baseInfoExtDTO.mainCustomerName || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">
                {{ $t('控股股东是否有TV整机品牌背景') }}
              </div>
              <div class="info-content-right">
                <mt-select
                  :disabled="true"
                  v-model="baseInfoExtDTO.stockHolderHasBackground"
                  :data-source="stockDataArr"
                ></mt-select>
              </div>
              <div class="info-title epls">
                {{ $t('公司法人、关键高层有无TCL3年内离职人员') }}
              </div>
              <div class="info-content-left">
                <mt-select
                  :disabled="true"
                  v-model="baseInfoExtDTO.hasResignedEmployee"
                  :data-source="dataArrEmployee"
                ></mt-select>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('是否TCL竞争对手控股') }}</div>
              <div class="info-content-right">
                <mt-select
                  :disabled="true"
                  v-model="baseInfoExtDTO.controlledByCompetitors"
                  :data-source="controlledDataArr"
                ></mt-select>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('纳税人识别号') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfo.taxNumber"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('注册地址') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfo.detailAddress"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('英文地址') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfo.supplierAddrEn"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('企业简介') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfoExtDTO.enterpriseProfile"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('经营范围') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfoExtDTO.businessScope"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('行业优势') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfoExtDTO.industryAdvantage"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('生产能力简介') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfoExtDTO.productionProfile"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('产品性能/价格/质量情况') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfoExtDTO.productProfile"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供货方及供货方便性简介') }}</div>
              <div class="textareaClass">
                <mt-input
                  type="text"
                  v-model="baseInfoExtDTO.supplierProfile"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供货地区') }}</div>
              <div class="textareaClass">
                {{ baseInfo.area || '--' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('工厂生产地址') }}</div>
          <div class="infos">
            <mt-template-page
              ref="factoryAddressRef"
              :use-tool-template="false"
              :template-config="factoryAddressTemplateConfig"
            >
            </mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('供货类别') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供货类别') }}</div>
              <div class="textareaClass">
                {{ baseInfoInsideDTO.supplyCategory || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供货补充说明') }}</div>
              <div class="textareaClass">
                {{ baseInfoInsideDTO.categoryDescription || '--' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('股权结构') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRefShip"
              :use-tool-template="false"
              :template-config="shipTemplateConfig"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('联系人信息') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="templateConfig"
              :key="templateKey"
              @actionComplete="actionComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
    </div>
    <!-- 变更后：修改提交 -->
    <div class="supplier-content" style="box-shadow: 0 0 20px 0px #999">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('基本信息') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供应商名称') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.supplierName ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="100"
                    v-model="baseInfoEdit.supplierName"
                    @change="changeVal('supplierName')"
                  ></mt-input>
                </div>
              </div>
              <div class="info-title">
                <span style="color: red">*</span>{{ $t('供应商英文名称') }}
              </div>
              <div class="info-content-left">
                <div :class="[styleObj.supplierNameEn ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="70"
                    v-model="baseInfoEdit.supplierNameEn"
                    @change="changeVal('supplierNameEn')"
                    @blur="handleSupplierNameEnBlur"
                  />
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('供应商简称') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.supplierShortName ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="baseInfoEdit.supplierShortName"
                    @change="changeVal('supplierShortName')"
                  ></mt-input>
                </div>
              </div>
              <div class="info-title">{{ $t('供应商类型') }}</div>
              <div class="info-content-left">
                <mt-select
                  :disabled="true"
                  v-model="baseInfoEdit.supplierType"
                  :data-source="supplierList"
                  :fields="stageTypeFields"
                ></mt-select>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('企业性质') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.enterpriseNature ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoEdit.enterpriseNature"
                    :data-source="enterpriseList"
                    :fields="stageTypeFields"
                    @change="changeVal('enterpriseNature')"
                  ></mt-select>
                </div>
              </div>
              <div class="info-title">{{ $t('所属行业') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.industryBelong ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoEdit.industryBelong"
                    :data-source="industryList"
                    :fields="stageTypeFields"
                    @change="changeVal('industryBelong')"
                  ></mt-select>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('行业排名') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.industryRank ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoEdit.industryRank"
                    :data-source="rankList"
                    :fields="stageTypeFields"
                    @change="changeVal('industryRank')"
                  ></mt-select>
                </div>
              </div>
              <div class="info-title">{{ $t('行业经验（年）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.industryEnterprise ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :min="0"
                    v-model="baseInfoEdit.industryEnterprise"
                    @change="changeVal('industryEnterprise')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('企业电话') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.enterprisePhone ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="baseInfoEdit.enterprisePhone"
                    @change="changeVal('enterprisePhone')"
                  ></mt-input>
                </div>
              </div>
              <div class="info-title">{{ $t('电子邮箱') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.enterpriseEmail ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="baseInfoEdit.enterpriseEmail"
                    @change="changeVal('enterpriseEmail')"
                    @blur="checkEmail"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('邮编') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.zipCode ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="baseInfoEdit.zipCode"
                    @change="changeVal('zipCode')"
                  ></mt-input>
                </div>
              </div>
              <div class="info-title">{{ $t('传真') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.fax ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="baseInfoEdit.fax"
                    @change="changeVal('fax')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('法人代表') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.legalRepresentative ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="baseInfoEdit.legalRepresentative"
                    @change="changeVal('legalRepresentative')"
                  ></mt-input>
                </div>
              </div>
              <div class="info-title">{{ $t('企业网站') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.website ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="baseInfoEdit.website"
                    @change="changeVal('website')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('公司成立时间') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.establishmentTime ? 'bgColor' : 'highlightColor']">
                  <span class="red">{{ checkDate(baseInfoEdit.establishmentTime) }}</span>
                </div>
              </div>
              <div class="info-title epls">{{ $t('国家/地区') }}</div>
              <div class="info-content-left">
                <!-- <mt-select
                  :disabled="true"
                  v-model="baseInfoEdit.countryRegion"
                  :dataSource="countryLists"
                  :fields="countryFields"
                ></mt-select> -->

                <RemoteAutocomplete
                  v-model="baseInfo.countryRegion"
                  :disabled="true"
                  :fields="{ text: 'shortName', value: 'countryCode' }"
                  url="/masterDataManagement/tenant/country/paged-query"
                  :rule-params="ruleParams"
                  :load-data="loadCountryData"
                  :search-fields="[
                    'id',
                    'alpha2Code',
                    'countryCode',
                    'englishShortName',
                    'numericCode',
                    'shortName',
                    'statusDescription'
                  ]"
                  :title-switch="false"
                  select-type="country"
                  @change="changeVal('countryRegion')"
                >
                </RemoteAutocomplete>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('省份') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.province ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    :disabled="true"
                    v-model="baseInfoEdit.province"
                    :data-source="provinceList"
                    :fields="provinceFields"
                    @change="changeVal('province')"
                  ></mt-select>
                </div>
              </div>
              <div class="info-title epls">{{ $t('市/区') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.city ? 'bgColor' : 'highlightColor']">
                  <span class="red">{{ baseInfoEdit.city || '--' }}</span>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">
                {{ $t('工厂/仓库距离TCL（公里）') }}
              </div>
              <div class="info-content-right">
                <div :class="[styleObj.supplyDistance ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    max-length="30"
                    v-model="baseInfoInsideDTOEdit.supplyDistance"
                    @change="changeVal('supplyDistance')"
                  ></mt-input>
                </div>
              </div>
              <div class="info-title">{{ $t('工厂面积') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.factoryArea ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    max-length="30"
                    v-model="baseInfoEdit.factoryArea"
                    @change="changeVal('factoryArea')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('同行业主力客户数量') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.mainCustomerNumber ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="0"
                    :min="0"
                    :max="127"
                    v-model="baseInfoExtDTOEdit.mainCustomerNumber"
                    @change="changeVal('mainCustomerNumber')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">{{ $t('同行业主力客户名称') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.mainCustomerName ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="baseInfoExtDTOEdit.mainCustomerName"
                    @change="changeVal('mainCustomerName')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">
                {{ $t('控股股东是否有TV整机品牌背景') }}
              </div>
              <div class="info-content-right">
                <div :class="[styleObj.stockHolderHasBackground ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoExtDTOEdit.stockHolderHasBackground"
                    :data-source="stockDataArr"
                    @change="changeVal('stockHolderHasBackground')"
                  ></mt-select>
                </div>
              </div>
              <div class="info-title epls">
                {{ $t('公司法人、关键高层有无TCL3年内离职人员') }}
              </div>
              <div class="info-content-left">
                <div :class="[styleObj.hasResignedEmployee ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoExtDTOEdit.hasResignedEmployee"
                    :data-source="dataArrEmployee"
                    @change="changeVal('hasResignedEmployee')"
                  ></mt-select>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('是否TCL竞争对手控股') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.controlledByCompetitors ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoExtDTOEdit.controlledByCompetitors"
                    :data-source="controlledDataArr"
                    @change="changeVal('controlledByCompetitors')"
                  ></mt-select>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('纳税人识别号') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.taxNumber ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="baseInfoEdit.taxNumber"
                    :multiline="true"
                    :rows="2"
                    :disabled="false"
                    @change="changeVal('taxNumber')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('注册地址') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.detailAddress ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="baseInfoEdit.detailAddress"
                    :multiline="true"
                    :rows="2"
                    :disabled="true"
                    @change="changeVal('detailAddress')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('英文地址') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.supplierAddrEn ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="baseInfoEdit.supplierAddrEn"
                    :multiline="true"
                    :rows="2"
                    @change="changeVal('supplierAddrEn')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('企业简介') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.enterpriseProfile ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="baseInfoExtDTOEdit.enterpriseProfile"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1200"
                    @change="changeVal('enterpriseProfile')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('经营范围') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.businessScope ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    v-model="baseInfoExtDTOEdit.businessScope"
                    type="text"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1200"
                    @change="changeVal('businessScope')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('行业优势') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.industryAdvantage ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="baseInfoExtDTOEdit.industryAdvantage"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1200"
                    @change="changeVal('industryAdvantage')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('生产能力简介') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.productionProfile ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="baseInfoExtDTOEdit.productionProfile"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1200"
                    @change="changeVal('productionProfile')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('产品性能/价格/质量情况') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.productProfile ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="baseInfoExtDTOEdit.productProfile"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1200"
                    @change="changeVal('productProfile')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供货方及供货方便性简介') }}</div>
              <div class="textareaClass">
                <div :class="[styleObj.supplierProfile ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    v-model="baseInfoExtDTOEdit.supplierProfile"
                    :multiline="true"
                    :rows="2"
                    :maxlength="1200"
                    @change="changeVal('supplierProfile')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('供货地区') }}</div>
              <div class="info-content-left2">
                <div :class="[styleObj.area ? 'bgColor' : 'highlightColor']">
                  <mt-multi-select
                    v-model="baseInfoEdit.area"
                    :data-source="areaOptions"
                    :fields="{ text: 'itemName', value: 'itemCode' }"
                    @change="changeVal('area', $event)"
                  ></mt-multi-select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('工厂生产地址') }}</div>
          <div class="infos">
            <mt-template-page
              ref="factoryAddressEditRef"
              :use-tool-template="false"
              :template-config="factoryAddressTemplateConfigEdit"
              @handleClickToolBar="handleClickFactoryAddressEdit"
              @actionComplete="factoryAddressComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('供货类别') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供货类别') }}</div>
              <div class="textareaClass">
                {{ baseInfoInsideDTOEdit.supplyCategory || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('供货补充说明') }}</div>
              <div class="textareaClass">
                {{ baseInfoInsideDTOEdit.categoryDescription || '--' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('股权结构') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRefShipEdit"
              :use-tool-template="false"
              :template-config="shipTemplateConfigEdit"
              @handleClickToolBar="handleClickShipEdit"
              @actionComplete="shipComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('联系人信息') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRefEdit"
              :use-tool-template="false"
              :template-config="templateConfigEdit"
              @handleClickToolBar="handleClickToolBarEdit"
              @actionBegin="actionBegin"
              @actionComplete="actionComplete"
            ></mt-template-page>
            <!-- <mt-template-page
            ref="templateRef"
            :template-config="componentConfig"
            :hidden-tabs="true"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTool="handleClickCellTool"
            @actionBegin="actionBegin"
            @actionComplete="actionComplete"
            @dataBound="handleDataBound"
          /> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  factoryAddressColumn,
  factoryAddressColumnEdit,
  shipStructureInfo,
  contactsInfo,
  forecastColumnData,
  shipStructureColumn
} from '../config/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import Component from '../config/columnComponent'
export default {
  components: {
    RemoteAutocomplete
  },
  props: {
    routerType: {
      type: String,
      default: ''
    },
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    },
    applyCode: {
      type: String,
      default: ''
    },
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ruleParams: [{ field: 'statusId', operator: 'equal', type: 'int', value: 1 }],
      styleObj: {
        supplierName: true,
        supplierNameEn: true,
        supplierShortName: true,
        enterpriseNature: true,
        industryBelong: true,
        industryRank: true,
        industryEnterprise: true,
        enterprisePhone: true,
        enterpriseEmail: true,
        zipCode: true,
        fax: true,
        legalRepresentative: true,
        website: true,
        establishmentTime: true,
        countryRegion: true,
        province: true,
        city: true,
        supplyDistance: true,
        factoryArea: true,
        mainCustomerNumber: true,
        mainCustomerName: true,
        stockHolderHasBackground: true,
        hasResignedEmployee: true,
        controlledByCompetitors: true,
        taxNumber: true,
        detailAddress: true,
        supplierAddrEn: true,
        enterpriseProfile: true,
        businessScope: true,
        industryAdvantage: true,
        productionProfile: true,
        productProfile: true,
        supplierProfile: true,
        area: true
      },
      isShowDialog: false,
      firstShow: true,
      allData: [], // 表格所有的数据
      shipAllData: [], //股权表格所有数据
      //变更时，下拉框的数据
      dataArr: [],
      stockDataArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      dataArrEmployee: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      controlledDataArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      baseInfo: {},
      baseInfoExtDTO: {},
      baseInfoInsideDTO: {},
      baseInfoEdit: {},
      baseInfoExtDTOEdit: {},
      baseInfoInsideDTOEdit: {},
      factoryAddressTemplateConfig: [
        {
          gridId: '63e7d078-cee5-45d1-b394-fff835a368b8',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: factoryAddressColumn,
            dataSource: []
          }
        }
      ],
      factoryAddressTemplateConfigEdit: [
        {
          gridId: '60d9e2cc-f18e-4d73-9979-0c58869120f8',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: factoryAddressColumnEdit,
            dataSource: [],
            rowDataBound: this.rowDataBound
          }
        }
      ],
      factoryAddressData: [], // 工厂生产地址表格数据
      areaOptions: [],
      shipTemplateConfig: [
        {
          gridId: '73ddcf14-c2b2-4bab-bd98-b5fcc732a2e3',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: shipStructureInfo,
            dataSource: []
          }
        }
      ],
      shipTemplateConfigEdit: [
        {
          gridId: '98540607-69d5-4bb7-aa13-e2ff93718dc9',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: shipStructureColumn,
            dataSource: [],
            rowDataBound: this.rowDataBoundShip
          }
        }
      ],
      templateKey: 1,
      templateConfig: [
        {
          gridId: '4229e38a-8b75-457c-8064-d334ece35bce',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: [],
            dataSource: []
          }
        }
      ],
      templateConfigEdit: [
        {
          gridId: '4189129e-e49a-4f87-926d-f0f58cecef6b',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: forecastColumnData,
            dataSource: [],
            rowDataBound: (args) => {
              if (args?.data['through']) {
                args.row.classList.add('line-through')
              }
            }
          }
        }
      ],
      itemNameOptions: [],
      dataList: [],
      // 所有下拉列表框数据
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      supplierList: [],
      enterpriseList: [],
      industryList: [],
      rankList: [],
      postList: [],
      acceptList: [],
      // countryLists:[],
      // countryFields: {
      //   text: "shortName",
      //   value: "countryCode",
      // },
      countryFields: {
        text: 'title',
        value: 'countryCode'
      },
      provinceList: [],
      provinceFields: {
        text: 'areaName',
        value: 'areaCode'
      },

      loadCountryData: false, // :loadData会监听去控制每次置为true就会重新发请求下拉组件数据
      pushArr: [], //如果双击的是删除的数据--则不可编辑
      isValid: true,
      originSupplierNameEn: '' // 供应商英文名称
    }
  },
  async mounted() {
    if (this.partnerArchiveId) {
      this.$loading()
      // 查询接口
      this.getAreaOptions()
      this.searchBase()
      this.selectByParentCode()
      // 所有下拉框接口
      await this.getAllSelectList()

      // 初始化表头数据
      this.initShipColumn()
      this.handleColumnsInfo()
      this.$nextTick(() => {
        this.$hloading()
      })
    }
  },
  methods: {
    getAreaOptions() {
      this.$API.infoChange['queryDict']({
        dictCode: 'supply_area'
      }).then((res) => {
        if (res.code) {
          this.areaOptions = res.data
        }
      })
    },
    // 校验供应商英文名称是否合法
    handleSupplierNameEnBlur(val) {
      const reg = /^[a-zA-Z0-9,.\s\-_@!#$%^&*()+={}[\]|\\:;"'<>.?/`~]+$/
      const isValid = reg.test(val)
      this.isValid = true
      if (!val || !isValid) {
        this.isValid = false
        this.baseInfoEdit.supplierNameEn = this.originSupplierNameEn
        !val
          ? this.$toast({ content: this.$t('供应商英文名称不能为空'), type: 'warning' })
          : this.$toast({
              content: this.$t('供应商英文名称不合法，请输入合法的英文名称'),
              type: 'warning'
            })
      }
    },
    // 高亮显示修改的内容
    changeVal(item) {
      // 下拉选择赋值延迟，需要setTimeout延时判断
      setTimeout(() => {
        if (['supplyDistance'].includes(item)) {
          if (this.baseInfoInsideDTO[item] != this.baseInfoInsideDTOEdit[item]) {
            this.styleObj[item] = false
            this.isShowDialog = true
          } else {
            this.styleObj[item] = true
          }
        } else if (
          [
            'mainCustomerNumber',
            'mainCustomerName',
            'stockHolderHasBackground',
            'hasResignedEmployee',
            'controlledByCompetitors',
            'enterpriseProfile',
            'businessScope',
            'industryAdvantage',
            'productionProfile',
            'productProfile',
            'supplierProfile'
          ].includes(item)
        ) {
          if (this.baseInfoExtDTO[item] != this.baseInfoExtDTOEdit[item]) {
            this.styleObj[item] = false
            this.isShowDialog = true
          } else {
            this.styleObj[item] = true
          }
        } else {
          if (['add', 'archive', 'launch'].includes(this.routerType) && !this.$parent.isSave) {
            if (item === 'supplierName' && this.baseInfo[item] != this.baseInfoEdit[item]) {
              this.confirmDialog()
              return
            }
          }
          if (this.baseInfo[item] != this.baseInfoEdit[item]) {
            this.styleObj[item] = false
            this.isShowDialog = true
          } else {
            this.styleObj[item] = true
          }
        }
        if (
          ['add', 'archive', 'launch'].includes(this.routerType) &&
          this.isShowDialog &&
          this.firstShow &&
          !this.$parent.isSave
        ) {
          this.showDialog()
        }
      })
    },
    showDialog() {
      this.firstShow = false
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('当前信息与天眼查不一致的内容，已按照天眼查覆盖并用红色字体标注')
        },
        modal: () => import('./showMsg.vue'),
        success: () => {}
      })
    },
    confirmDialog() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('变更后供应商名称与变更前不一致，请确认是否更改供应商名称')
        },
        modal: () => import('./showMsg.vue'),
        success: () => {
          this.styleObj['supplierName'] = false
        }
      })
    },

    checkDate(e) {
      if (e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd')
        } else if (typeof e == 'string') {
          if (e.indexOf('T') != -1) {
            return e.substr(0, 10)
          } else {
            let val = parseInt(e)
            return this.$utils.formateTime(val, 'yyyy-MM-dd')
          }
        } else if (typeof e == 'number') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd')
        } else {
          return e
        }
      } else {
        return e
      }
    },
    async getAllSelectList() {
      // 国家地区
      // this.countryQuery();
      // :loadData会监听去控制每次置为true就会重新发请求下拉组件数据
      this.loadCountryData = true
      // 加延迟将:loadData置为false，否则组件无法watch监听变化
      setTimeout(() => {
        this.loadCountryData = false
      }, 500)
      // 供应商类型
      this.getEnterpriseNature('supplierType')
      // 企业性质
      this.getEnterpriseNature('EnterpriseType')
      // 所属行业
      this.getEnterpriseNature('Industry')
      // 行业排名
      this.getEnterpriseNature('industryRank')
      // 职务
      await this.getEnterpriseNature('post')
      await this.getEnterpriseNature('acceptMsgType')
      this.nextThen()
    },
    async selectByParentCode() {
      // this.$loading();
      await this.$API.supplierInvitationAdd
        .selectByParentCode({
          parentCode: ''
        })
        .then((res) => {
          // this.$hloading();
          let arr = JSON.parse(JSON.stringify(res.data))
          this.provinceList = arr
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // countryQuery(){
    //   this.$loading();
    //   this.$API.infoChange.countryQuery().then((res) => {
    //     this.$hloading();
    //     this.countryLists = res.data;
    //   }).catch(err=>{
    //     this.$hloading();
    //     this.$toast({
    //       content:err.msg,
    //       type:"error"
    //     })
    //   });
    // },
    async getEnterpriseNature(dictCode) {
      // this.$loading();
      await this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          // this.$hloading();
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'EnterpriseType') {
              this.enterpriseList = data
            } else if (dictCode == 'Industry') {
              this.industryList = data
            } else if (dictCode == 'industryRank') {
              this.rankList = data
            } else if (dictCode == 'post') {
              this.postList = data
            } else if (dictCode == 'supplierType') {
              this.supplierList = data
            } else if (dictCode == 'acceptMsgType') {
              this.acceptList = data
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    nextThen() {
      this.templateConfig[0].grid.columnData = contactsInfo(this.postList, this.acceptList)
      this.templateKey++
    },
    checkEmail(value) {
      if (value == '' || value == null || value == undefined) {
        return
      } else if (!this.$utils.isEmail(value)) {
        this.$toast({
          content: this.$t('请输入正确的联系人邮箱'),
          type: 'warning'
        })
      }
    },
    async searchBase() {
      // 信息内容展示
      let obj = {
        applyCode: this.applyCode,
        queryArchiveRequest: {
          partnerArchiveId: this.partnerArchiveId,
          orgId: this.orgId,
          partnerRelationCode: this.partnerRelationCode,
          supplierEnterpriseId: this.supplierEnterpriseId
        }
      }

      // this.$loading();
      await this.$API.infoChange
        .searchBaseSup(obj)
        .then((res) => {
          // this.$hloading();
          let { newOne, oldOne, infoChangeRecordDTOList } = res.data
          this.baseInfo = oldOne.baseInfoDTO
          console.log(oldOne.baseInfoDTO, 'oldOne.baseInfoDTOoldOne.baseInfoDTOoldOne.baseInfoDTO')
          window.countryCode = oldOne.baseInfoDTO?.countryRegion
          this.baseInfoExtDTO = oldOne.baseInfoExtDTO
          this.baseInfoInsideDTO = oldOne.baseInfoInsideDTO
          setTimeout(() => {
            this.templateConfig[0].grid = Object.assign({}, this.templateConfig[0].grid, {
              dataSource: oldOne.contactInsideDTOList
            })
            this.shipTemplateConfig[0].grid = Object.assign({}, this.shipTemplateConfig[0].grid, {
              dataSource: oldOne.ownershipStructureDTOList
            })
            this.factoryAddressTemplateConfig[0].grid.dataSource =
              oldOne.baseInfoDTO.factoryAddress && oldOne.baseInfoDTO.factoryAddress !== 'null'
                ? JSON.parse(oldOne.baseInfoDTO.factoryAddress).map((item) => {
                    return JSON.parse(item)
                  })
                : []

            this.baseInfo.area =
              oldOne.baseInfoDTO.supplyAddress && oldOne.baseInfoDTO.supplyAddress !== 'null'
                ? JSON.parse(oldOne.baseInfoDTO.supplyAddress)
                    .map((item) => {
                      return this.areaOptions.find((v) => v.itemCode === item)?.itemName
                    })
                    .join('，')
                : ''
          })

          // 变更
          // 如果已缓存，展示最新缓存的数据，否则就从接口拿数据
          let changeBaseInfo = JSON.parse(sessionStorage.getItem('changeBaseInfoSup'))
          if (changeBaseInfo) {
            this.baseInfoEdit = changeBaseInfo.baseInfoEdit
            this.originSupplierNameEn = this.baseInfoEdit.supplierNameEn

            let baseInfoExtDTO = changeBaseInfo.baseInfoExtDTOEdit
            this.baseInfoExtDTOEdit = baseInfoExtDTO
            // this.baseInfoExtDTOEdit = {
            //   mainCustomerNumber: baseInfoExtDTO.mainCustomerNumber,
            //   mainCustomerName: baseInfoExtDTO.mainCustomerName,
            //   stockHolderHasBackground: baseInfoExtDTO.stockHolderHasBackground,
            //   hasResignedEmployee: baseInfoExtDTO.hasResignedEmployee,
            //   controlledByCompetitors: baseInfoExtDTO.controlledByCompetitors,
            //   enterpriseProfile: baseInfoExtDTO.enterpriseProfile,
            //   businessScope: baseInfoExtDTO.businessScope,
            //   industryAdvantage: baseInfoExtDTO.industryAdvantage,
            //   productionProfile: baseInfoExtDTO.productionProfile,
            //   productProfile: baseInfoExtDTO.productProfile,
            //   supplierProfile: baseInfoExtDTO.supplierProfile,
            //   originTableId:baseInfoExtDTO.originTableId,
            //   id:baseInfoExtDTO.id,
            // };
            let baseInfoInsideDTO = changeBaseInfo.baseInfoInsideDTOEdit
            this.baseInfoInsideDTOEdit = baseInfoInsideDTO
            // this.baseInfoInsideDTOEdit = {
            //   supplyDistance: baseInfoInsideDTO.supplyDistance,
            //   supplyCategory: baseInfoInsideDTO.supplyCategory,
            //   categoryDescription: baseInfoInsideDTO.categoryDescription,
            //   originTableId:baseInfoInsideDTO.originTableId,
            //   id:baseInfoInsideDTO.id,
            // };
            this.shipAllData = JSON.parse(changeBaseInfo.shipAllData)
            this.allData = JSON.parse(changeBaseInfo.allData)
            this.factoryAddressData = JSON.parse(changeBaseInfo.factoryAddressData)
            setTimeout(() => {
              this.$set(this.shipTemplateConfigEdit[0].grid, 'dataSource', this.shipAllData)
              this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.allData)
              this.$set(
                this.factoryAddressTemplateConfigEdit[0].grid,
                'dataSource',
                this.factoryAddressData
              )
            }, 100)
          } else {
            this.baseInfoEdit = JSON.parse(JSON.stringify(newOne.baseInfoDTO))
            this.originSupplierNameEn = this.baseInfoEdit.supplierNameEn

            let baseInfoExtDTO = JSON.parse(JSON.stringify(newOne.baseInfoExtDTO))
            this.baseInfoExtDTOEdit = baseInfoExtDTO
            // this.baseInfoExtDTOEdit = {
            //   mainCustomerNumber: baseInfoExtDTO.mainCustomerNumber,
            //   mainCustomerName: baseInfoExtDTO.mainCustomerName,
            //   stockHolderHasBackground: baseInfoExtDTO.stockHolderHasBackground,
            //   hasResignedEmployee: baseInfoExtDTO.hasResignedEmployee,
            //   controlledByCompetitors: baseInfoExtDTO.controlledByCompetitors,
            //   enterpriseProfile: baseInfoExtDTO.enterpriseProfile,
            //   businessScope: baseInfoExtDTO.businessScope,
            //   industryAdvantage: baseInfoExtDTO.industryAdvantage,
            //   productionProfile: baseInfoExtDTO.productionProfile,
            //   productProfile: baseInfoExtDTO.productProfile,
            //   supplierProfile: baseInfoExtDTO.supplierProfile,
            //   originTableId:baseInfoExtDTO.originTableId,
            //   id:baseInfoExtDTO.id,
            // };
            let baseInfoInsideDTO = JSON.parse(JSON.stringify(newOne.baseInfoInsideDTO))
            this.baseInfoInsideDTOEdit = baseInfoInsideDTO
            // this.baseInfoInsideDTOEdit = {
            //   supplyDistance: baseInfoInsideDTO.supplyDistance,
            //   supplyCategory: baseInfoInsideDTO.supplyCategory,
            //   categoryDescription: baseInfoInsideDTO.categoryDescription,
            //   originTableId:baseInfoInsideDTO.originTableId,
            //   id:baseInfoInsideDTO.id,
            // };
            // this.templateConfigEdit[0].grid = Object.assign({}, this.templateConfigEdit[0].grid, {
            //   dataSource: newOne.contactInsideDTOList
            // })
            // console.log(
            //   'this.templateConfigEdit[0].grid',
            //   newOne.contactInsideDTOList,
            //   this.templateConfig[0].grid
            // )
            if (this.isShow) {
              this.templateConfigEdit[0].grid = Object.assign({}, this.templateConfigEdit[0].grid, {
                dataSource: newOne.contactInsideDTOList
              })
            } else {
              let arr = newOne.contactInsideDTOList.map((item) => item.originTableId)
              let pusharr = this.templateConfig[0].grid.dataSource.filter(
                (v) => !arr.includes(v.id)
              )
              this.pushArr = pusharr
              if (pusharr.length > 0) {
                pusharr.map((item) => {
                  item.through = true
                })
                newOne.contactInsideDTOList.push(...pusharr)
              }
              this.templateConfigEdit[0].grid = Object.assign({}, this.templateConfigEdit[0].grid, {
                dataSource: newOne.contactInsideDTOList
              })
            }

            this.factoryAddressData =
              newOne.baseInfoDTO.factoryAddress && newOne.baseInfoDTO.factoryAddress !== 'null'
                ? JSON.parse(newOne.baseInfoDTO.factoryAddress).map((item) => {
                    return {
                      cid: 'factory' + Math.floor(Math.random() * (1 - 100) + 100),
                      ...JSON.parse(item)
                    }
                  })
                : []
            this.$set(
              this.baseInfoEdit,
              'area',
              newOne.baseInfoDTO.supplyAddress && newOne.baseInfoDTO.supplyAddress !== 'null'
                ? JSON.parse(newOne.baseInfoDTO.supplyAddress)
                : []
            )

            this.shipTemplateConfigEdit[0].grid = Object.assign(
              {},
              this.shipTemplateConfigEdit[0].grid,
              { dataSource: newOne.ownershipStructureDTOList }
            )
            this.factoryAddressTemplateConfigEdit[0].grid = Object.assign(
              {},
              this.factoryAddressTemplateConfigEdit[0].grid,
              {
                dataSource: this.factoryAddressData
              }
            )
          }

          // 变更字段:有缓存拿缓存，无缓存，拿infoChangeRecordDTOList
          // 高亮显示修改的内容
          let baseStyle = JSON.parse(sessionStorage.getItem('baseStyleSup'))
          if (baseStyle) {
            this.styleObj = baseStyle
          } else {
            if (infoChangeRecordDTOList.length > 0) {
              infoChangeRecordDTOList.forEach((item) => {
                if (!item.listType) {
                  // 变更动作（0:更新 1:删除 2:列表新增）
                  if (item.changeFlag == 1 || item.changeFlag == 2) {
                    let str = item.changeField
                    this.styleObj[str] = false
                  }
                }
              })
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 股权结构行内编辑
    initShipColumn() {
      shipStructureColumn.length = 0
      const column = this.formatTableColumnShip(shipStructureInfo)
      const columns = [].concat(column)
      columns.forEach((item) => {
        shipStructureColumn.push(item)
      })
    },
    formatTableColumnShip(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'stockHolder') {
          // 股东
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'shareholdingRatio') {
          // 持股比例
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'beneficialShares') {
          // 最终受益股份
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'capitalContributionMoney') {
          // 认缴出资额
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'capitalContributionDate') {
          // 认缴出资日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundShip(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
      if (args?.data['through']) {
        args.row.classList.add('line-through')
      }
    },
    shipComplete(args) {
      this.shipAllData = this.getShipRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(shipStructureInfo))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.templateRefShipEdit
              .getCurrentUsefulRef()
              .gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.shipAllData = this.shipAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.shipTemplateConfigEdit[0].grid, 'dataSource', this.shipAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.shipTemplateConfigEdit[0].grid.dataSource = this.shipAllData
      }
    },
    getShipRow() {
      let rows = this.$refs.templateRefShipEdit
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'ship' + num
        item.supplierEnterpriseId = this.baseInfo.supplierEnterpriseId
        item.supplierTenantId = this.baseInfo.supplierTenantId
        item.tenantId = this.baseInfo.tenantId
      })
      return rows
    },
    handleClickFactoryAddressEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.factoryAddressEditRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.factoryAddressData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.factoryAddressData.splice(sindex, 1)
                  }
                })
              })
              this.$set(
                this.factoryAddressTemplateConfigEdit[0].grid,
                'dataSource',
                this.factoryAddressData
              )
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: this.$t('warning')
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.factoryAddressEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    factoryAddressComplete(args) {
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        const allNull = ['address'].every((ditem) => {
          const value = data[ditem]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.factoryAddressEditRef
              .getCurrentUsefulRef()
              .gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.factoryAddressData = this.factoryAddressData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(
              this.factoryAddressTemplateConfigEdit[0].grid,
              'dataSource',
              this.factoryAddressData
            )
          }
          return
        }
      }
      this.factoryAddressData = this.getFactoryAddressRow()
      if (args.requestType == 'save') {
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.factoryAddressTemplateConfigEdit[0].grid.dataSource = this.factoryAddressData
      }
    },
    getFactoryAddressRow() {
      let rows = this.$refs.factoryAddressEditRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'factory' + num
      })
      return rows
    },
    handleClickShipEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templateRefShipEdit.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.shipAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.shipAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.shipTemplateConfigEdit[0].grid, 'dataSource', this.shipAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.templateRefShipEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // 联系人信息行内编辑
    // 处理表头数据
    handleColumnsInfo() {
      forecastColumnData.length = 0 // 清空表头数据
      // 固定的表头
      const forecastColumns = this.formatTableColumnData(
        contactsInfo(this.postList, this.acceptList)
      )
      const columns = [].concat(forecastColumns)
      columns.forEach((item) => {
        forecastColumnData.push(item)
      })
    },
    // 行内样式
    formatTableColumnData(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'contactName') {
          // 姓名  下拉
          defaultCol.editTemplate = Component.input({
            dataKey: col.field,
            dataList: []
          })
        } else if (col.field === 'contactSex') {
          // 性别 下拉框
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            dataList: [
              { text: this.$t('男'), value: '1' },
              { text: this.$t('女'), value: '2' }
            ]
          })
        } else if (col.field === 'contactDept') {
          // 部门  文本框
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'contactPost') {
          // 职务  下拉
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            fields: this.stageTypeFields,
            dataList: this.postList
          })
        } else if (col.field === 'contactTel') {
          // 电话  文本框
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'contactMobile') {
          // 手机  文本框
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'contactMail') {
          // 邮箱  文本框
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'acceptMsg') {
          // 接受信息类型
          // defaultCol.editTemplate = Component.select({
          //   dataKey: col.field,
          //   fields: this.stageTypeFields,
          //   dataList: this.acceptList
          // })
        } else if (col.field === 'contractQq') {
          // QQ  文本框
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'contactWechat') {
          // 微信  文本框
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    actionBegin(args) {
      const { requestType, rowData } = args
      if (requestType == 'beginEdit' && rowData?.through) {
        args.cancel = true
        this.$toast({ content: this.$t('此数据不可编辑'), type: 'warning' })
      }
    },
    rowDataBound(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    actionComplete(args) {
      const reg_tel = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
      const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      const reg_mobel = /0\d{2,3}-[1-9]\d{6,7}/
      const reg_mobel400 = /^400[0-9]{7}/
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = contactsInfo()
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.allData = this.allData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.allData)
          }
          return
        }
        if (data.acceptMsg != null) {
          data.acceptMsg = JSON.stringify(data.acceptMsg)
        }
        if (
          this.baseInfo.countryRegion === 'CN' &&
          data.contactMobile &&
          !reg_tel.test(data.contactMobile)
        ) {
          this.$toast({
            content: this.$t('请输入格式正确的手机号'),
            type: 'error'
          })
          this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          return
        }
        if (data.contactMail && !emailReg.test(data.contactMail)) {
          this.$toast({
            content: this.$t('请输入格式正确的邮箱'),
            type: 'error'
          })
          this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          return
        }
        console.log(!reg_mobel.test(data.contactTel) && !reg_mobel400.test(data.contactTel))
        console.log(reg_mobel.test(data.contactTel), reg_mobel400.test(data.contactTel))

        if (
          this.baseInfo.countryRegion === 'CN' &&
          data.contactTel &&
          !reg_mobel.test(data.contactTel) &&
          !reg_mobel400.test(data.contactTel) &&
          !reg_tel.test(data.contactTel)
        ) {
          this.$toast({
            content: '请输入格式正确的电话号码，座机区号和号码之间加-,400电话直接输号码即可',
            type: 'error'
          })
          this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          return
        }
      }
      this.allData = this.getRow()
      if (args.requestType == 'save') {
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.templateConfigEdit[0].grid.dataSource = this.allData
      }
      // if (args.rowData && args.rowData.through) {
      //   // 结束编辑状态
      //   // this.$nextTick(() => {
      //     let _current = this.$refs.templateRefEdit.getCurrentTabRef()
      //     _current?.grid.endEdit()
      //   // })
      //   this.$toast({ content: this.$t('此数据不可编辑'), type: 'warning' })
      // }
    },
    getRow() {
      let rows = this.$refs.templateRefEdit
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'infoBase' + num
      })
      return rows
    },
    handleClickToolBarEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.allData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.allData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.allData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-filled.e-input-group.e-control-wrapper textarea.e-input {
  padding-bottom: 8px;
}
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;
  display: flex;
  justify-content: space-between;
  .border {
    width: 1px;
    height: auto;
    background: #000 !important;
  }
  .supplier-content {
    width: 49%;
  }
  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            word-break: break-all;
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 75%;
            padding: 5px 10px;
            /deep/ .mt-input {
              width: 100%;
            }
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
          .info-content-left2 {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 75%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
          .bgColor {
            background: rgba(245, 252, 255, 1);
            width: 100%;
            padding: 0 5px;
            /deep/.mt-input-number input {
              width: 100%;
            }
          }
          .highlightColor {
            background: #e8f0fe;
            width: 100%;
            padding: 0 5px;
            /deep/.e-input-group {
              color: red !important;
            }
            /deep/.e-multi-select-wrapper {
              color: red !important;
            }
            /deep/.mt-input-number input {
              width: 100%;
              color: red !important;
            }
            /deep/.red {
              color: red !important;
            }
          }
          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
/deep/.line-through {
  text-decoration: line-through;
}
</style>
