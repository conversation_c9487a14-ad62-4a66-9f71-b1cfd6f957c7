<template>
  <div class="supplier-portrait">
    <!-- 变更前：不用修改 -->
    <div class="supplier-content">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('基本信息') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('总资产（万元）') }}</div>
              <div class="info-content-left">
                {{ financialBaseInfo.totalAssets || '--' }}
              </div>
              <div class="info-title epls">{{ $t('注册资本（万元）') }}</div>
              <div class="info-content-right">
                {{ financialBaseInfo.registeredCapital || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('实缴资本（万元）') }}</div>
              <div class="info-content-left">
                {{ financialBaseInfo.paidInCapital || '--' }}
              </div>
              <div class="info-title epls">{{ $t('流动资金（万元）') }}</div>
              <div class="info-content-right">
                {{ financialBaseInfo.workingCapital || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('固定资产（万元）') }}</div>
              <div class="info-content-left">
                {{ financialBaseInfo.fixedAssets || '--' }}
              </div>
              <div class="info-title epls">
                {{ $t('年度销售总额/万元（近三年）-第一年') }}
              </div>
              <div class="info-content-right">
                {{ financialBaseInfo.totalAnnualSalesOne || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">
                {{ $t('年度销售总额/万元（近三年）-第二年') }}
              </div>
              <div class="info-content-left">
                {{ financialBaseInfo.totalAnnualSalesTwo || '--' }}
              </div>
              <div class="info-title epls">
                {{ $t('年度销售总额/万元（近三年）-第三年') }}
              </div>
              <div class="info-content-right">
                {{ financialBaseInfo.totalAnnualSalesThree || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('近三年利润率（%）') }}</div>
              <div class="info-content-left">
                {{ financialBaseInfo.profitMargin || '--' }}
              </div>
              <div class="info-title epls">{{ $t('年产值（万元）') }}</div>
              <div class="info-content-right">
                {{ financialBaseInfo.annualOutputValue || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('银行信用等级') }}</div>
              <div class="info-content-left">
                {{ financialBaseInfo.bankCredit || '--' }}
              </div>
              <div class="info-title epls">{{ $t('纳税信用最新评价') }}</div>
              <div class="info-content-right">
                {{ financialBaseInfo.taxCreditEvaluation || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('币种') }}</div>
              <div class="info-content-left">
                <!-- <mt-select
                :disabled="true"
                    v-model="baseInfoInsideDTO.currency"
                    :fields="currencyFields"
                    :dataSource="pagedList"
                  ></mt-select> -->
                <RemoteAutocomplete
                  v-model="baseInfoInsideDTO.currency"
                  :fields="{ text: 'currencyName', value: 'currencyCode' }"
                  :search-fields="['currencyCode', 'currencyName']"
                  url="/masterDataManagement/tenant/currency/queryAll"
                  records-position="data"
                  select-type="money"
                  :disabled="true"
                >
                </RemoteAutocomplete>
              </div>
              <div class="info-title epls">{{ $t('结算类型') }}</div>
              <div class="info-content-right">
                <mt-select
                  :disabled="true"
                  v-model="baseInfoInsideDTO.settlementType"
                  :data-source="settlementList"
                  :fields="stageTypeFields"
                ></mt-select>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('能否接受TCL金单') }}</div>
              <div class="info-content-left">
                <mt-select
                  :disabled="true"
                  v-model="baseInfoInsideDTO.receiveGoldOrder"
                  :data-source="receiveDataArr"
                ></mt-select>
              </div>
              <div class="info-title epls">{{ $t('付款条件') }}</div>
              <div class="info-content-right">
                <mt-select
                  :disabled="true"
                  v-model="baseInfoInsideDTO.paymentTerm"
                  :data-source="payList"
                  :fields="payFields"
                ></mt-select>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('附件') }}</div>
              <div class="info-content-left" style="width: 75%">
                <div style="width: 100%">
                  <span
                    class="downStyle"
                    v-for="(item, index) in baseInfoInsideDTO.financeAttachment"
                    :key="index"
                    @click="downAttachment(item)"
                    >{{ item.fileName }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('银行账户信息') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="templateConfig"
              :key="templateKey"
            ></mt-template-page>
          </div>
        </div>
      </div>
    </div>
    <!-- 变更 -->
    <div class="supplier-content" style="margin-left: 2%">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('基本信息') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('总资产（万元）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.totalAssets ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.totalAssets"
                    @change="changeVal('totalAssets')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">{{ $t('注册资本（万元）') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.registeredCapital ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.registeredCapital"
                    @change="changeVal('registeredCapital')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('实缴资本（万元）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.paidInCapital ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.paidInCapital"
                    @change="changeVal('paidInCapital')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">{{ $t('流动资金（万元）') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.workingCapital ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.workingCapital"
                    @change="changeVal('workingCapital')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('固定资产（万元）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.fixedAssets ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.fixedAssets"
                    @change="changeVal('fixedAssets')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">
                {{ $t('年度销售总额/万元（近三年）-第一年') }}
              </div>
              <div class="info-content-right">
                <div :class="[styleObj.totalAnnualSalesOne ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.totalAnnualSalesOne"
                    @change="changeVal('totalAnnualSalesOne')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">
                {{ $t('年度销售总额/万元（近三年）-第二年') }}
              </div>
              <div class="info-content-left">
                <div :class="[styleObj.totalAnnualSalesTwo ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.totalAnnualSalesTwo"
                    @change="changeVal('totalAnnualSalesTwo')"
                  ></mt-inputNumber>
                </div>
              </div>
              <div class="info-title epls">
                {{ $t('年度销售总额/万元（近三年）-第三年') }}
              </div>
              <div class="info-content-right">
                <div :class="[styleObj.totalAnnualSalesThree ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.totalAnnualSalesThree"
                    @change="changeVal('totalAnnualSalesThree')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('近三年利润率（%）') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.isProfit ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    v-model="financialBaseInfoEdit.profitMargin"
                    @change="changeVal('isProfit')"
                  ></mt-input>
                </div>
              </div>
              <div class="info-title epls">{{ $t('年产值（万元）') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.annualOutputValue ? 'bgColor' : 'highlightColor']">
                  <mt-inputNumber
                    :precision="2"
                    :min="0"
                    v-model="financialBaseInfoEdit.annualOutputValue"
                    @change="changeVal('annualOutputValue')"
                  ></mt-inputNumber>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('银行信用等级') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.bankCredit ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    max-length="30"
                    v-model="financialBaseInfoEdit.bankCredit"
                    @change="changeVal('bankCredit')"
                  ></mt-input>
                </div>
              </div>
              <div class="info-title epls">{{ $t('纳税信用最新评价') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.taxCreditEvaluation ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="financialBaseInfoEdit.taxCreditEvaluation"
                    @change="changeVal('taxCreditEvaluation')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('币种') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.currency ? 'bgColor' : 'highlightColor']">
                  <!-- <mt-select
                    v-model="baseInfoInsideDTOEdit.currency"
                    :fields="currencyFields"
                    :dataSource="pagedList"
                    @change="changeVal('currency',$event)"
                  ></mt-select> -->
                  <RemoteAutocomplete
                    v-model="baseInfoInsideDTOEdit.currency"
                    :fields="{ text: 'currencyName', value: 'currencyCode' }"
                    :search-fields="['currencyCode', 'currencyName']"
                    url="/masterDataManagement/tenant/currency/queryAll"
                    records-position="data"
                    select-type="money"
                    @change="changeVal('currency')"
                  >
                  </RemoteAutocomplete>
                </div>
              </div>
              <div class="info-title epls">{{ $t('结算类型') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.settlementType ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoInsideDTOEdit.settlementType"
                    :data-source="settlementList"
                    :fields="stageTypeFields"
                    @change="changeVal('settlementType')"
                  ></mt-select>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('能否接受TCL金单') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.receiveGoldOrder ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoInsideDTOEdit.receiveGoldOrder"
                    :data-source="receiveDataArr"
                    @change="changeVal('receiveGoldOrder')"
                  ></mt-select>
                </div>
              </div>
              <div class="info-title epls">{{ $t('付款条件') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.paymentTerm ? 'bgColor' : 'highlightColor']">
                  <mt-select
                    v-model="baseInfoInsideDTOEdit.paymentTerm"
                    :data-source="payList"
                    :fields="payFields"
                    @change="changeVal('paymentTerm')"
                  ></mt-select>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title epls">{{ $t('附件') }}</div>
              <div class="info-content-left" style="width: 75%">
                <!-- <div style="width:100%" >
                  <span class="downStyle" v-for="(item,index) in baseInfoInsideDTOEdit.financeAttachment" :key="index"
                  @click="downAttachment(item)">{{item.fileName}}</span>
                </div> -->
                <mt-common-uploader
                  :is-single-file="false"
                  :save-url="saveUrl"
                  :download-url="downloadUrl"
                  type="line"
                  v-model="baseInfoInsideDTOEdit.financeAttachment"
                  :key="fileListKey"
                ></mt-common-uploader>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('银行账户信息') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRefEdit"
              :use-tool-template="false"
              :template-config="templateConfigEdit"
              @handleClickToolBar="handleClickShipEdit"
              @actionComplete="shipComplete"
            ></mt-template-page>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { bankInfo, bankInfoOld, bankInfoColumn } from '../config/index'
import Component from '../config/columnComponent'
import commonData from '@/utils/constant'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  props: {
    routerType: {
      type: String,
      default: ''
    },
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    },
    applyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      saveUrl: commonData.privateFileUrl,
      downloadUrl: commonData.downloadUrl,
      fileListKey: 0,
      styleObj: {
        totalAssets: true,
        registeredCapital: true,
        paidInCapital: true,
        workingCapital: true,
        fixedAssets: true,
        totalAnnualSalesOne: true,
        totalAnnualSalesTwo: true,
        totalAnnualSalesThree: true,
        annualOutputValue: true,
        bankCredit: true,
        taxCreditEvaluation: true,
        currency: true,
        settlementType: true,
        receiveGoldOrder: true,
        paymentTerm: true,
        isProfit: true
      },
      dataArr: [],
      receiveDataArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      settleDataArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      financialBaseInfo: {},
      baseInfoInsideDTO: {},
      financialBaseInfoEdit: {},
      baseInfoInsideDTOEdit: {},
      templateKey: 1,
      templateConfig: [
        {
          gridId: 'f0e4912b-2c6b-47d1-b285-25df4ff21eb6',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: [],
            dataSource: []
          }
        }
      ],
      templateConfigEdit: [
        {
          gridId: 'b125a82e-9c6b-4ad5-930c-01d5178d01e6',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: bankInfoColumn,
            dataSource: [],
            rowDataBound: this.rowDataBound
          }
        }
      ],
      shipAllData: [],
      // currencyFields: {
      //   text: "currencyName",
      //   value: "currencyCode",
      // },
      currencyFields: {
        text: 'title',
        value: 'currencyCode'
      },
      settlementList: [],
      provinceList: [],
      certificateList: [],
      payList: [],
      payFields: {
        text: 'paymentTermsName',
        value: 'paymentTermsCode'
      },
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      }
      // pagedList:[]
    }
  },
  async mounted() {
    this.$loading()
    this.searchFinance()
    // 所有下拉框接口
    await this.getAllSelectList()

    this.initColumn()
    this.$nextTick(() => {
      this.$hloading()
    })
  },
  methods: {
    // 高亮显示修改的内容
    changeVal(item) {
      setTimeout(() => {
        if (['currency', 'settlementType', 'receiveGoldOrder', 'paymentTerm'].includes(item)) {
          if (this.baseInfoInsideDTO[item] != this.baseInfoInsideDTOEdit[item]) {
            this.styleObj[item] = false
          } else {
            this.styleObj[item] = true
          }
        } else {
          if (this.financialBaseInfo[item] != this.financialBaseInfoEdit[item]) {
            this.styleObj[item] = false
          } else {
            this.styleObj[item] = true
          }
        }
      })
    },
    rowDataBound(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },

    downAttachment(item) {
      this.$utils.downloadAttachment(item)
    },
    async getAllSelectList() {
      // 币种
      // this.getPagedQuery();
      // 结算类型
      this.getEnterpriseNature('settlementType')
      // 付款条件
      this.getCriteriaQuery()
      // 银行名称
      // await this.getEnterpriseNature('');
      // 开户行省份
      await this.selectByParentCode()
      // 证书类型
      await this.getEnterpriseNature('certificateType')
    },
    // getPagedQuery(){
    //   this.$loading();
    //   this.$API.infoChange.pagedQuery().then(res=>{
    //     this.$hloading();
    //     this.pagedList = res.data;
    //   }).catch(err=>{
    //     this.$hloading();
    //     this.$toast({
    //       content:err.msg,
    //       type:"error"
    //     })
    //   })
    // },
    // 付款条件
    getCriteriaQuery() {
      // this.$loading();
      this.$API.infoChange
        .criteriaQuery({})
        .then((res) => {
          // this.$hloading();
          this.payList = res.data
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async getEnterpriseNature(dictCode) {
      // this.$loading();
      await this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          // this.$hloading();
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'settlementType') {
              this.settlementList = data
            } else if (dictCode == 'certificateType') {
              this.certificateList = data
            }
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 获取城市数据
    async selectByParentCode() {
      // this.$loading();
      await this.$API.supplierInvitationAdd
        .selectByParentCode({
          parentCode: '',
          countryCode: window.countryCode || ''
        })
        .then((res) => {
          // this.$hloading();
          let arr = JSON.parse(JSON.stringify(res.data))
          // arr.unshift({ id: "", areaName: "全部地区" });
          this.provinceList = arr
          this.$set(this.templateConfig[0].grid, 'columnData', bankInfoOld(this.provinceList))
          this.templateKey++
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    searchFinance() {
      let obj = {
        applyCode: this.applyCode,
        queryArchiveRequest: {
          partnerArchiveId: this.partnerArchiveId,
          orgId: this.orgId,
          partnerRelationCode: this.partnerRelationCode,
          supplierEnterpriseId: this.supplierEnterpriseId
        }
      }
      // this.$loading();
      this.$API.infoChange
        .searchFinanceSup(obj)
        .then((res) => {
          // this.$hloading();
          let { newOne, oldOne, infoChangeRecordDTOList } = res.data
          this.financialBaseInfo = oldOne.financeInfoDTO
          this.baseInfoInsideDTO = oldOne.baseInfoInsideDTO
          this.templateConfig[0].grid = Object.assign({}, this.templateConfig[0].grid, {
            dataSource: oldOne.bankInsideDTOList
          })

          // 变更
          let changeBusinessInfo = JSON.parse(sessionStorage.getItem('changeBusinessInfoSup'))
          if (changeBusinessInfo) {
            this.financialBaseInfoEdit = changeBusinessInfo.financialBaseInfoEdit
            let baseInfoInsideDTO = changeBusinessInfo.businessInfoInsideDTOEdit
            if (baseInfoInsideDTO) {
              this.baseInfoInsideDTOEdit = {
                currency: baseInfoInsideDTO.currency,
                settlementType: baseInfoInsideDTO.settlementType,
                receiveGoldOrder: baseInfoInsideDTO.receiveGoldOrder,
                paymentTerm: baseInfoInsideDTO.paymentTerm,
                financeAttachment: baseInfoInsideDTO.financeAttachment || []
              }
            }
            if (Array.isArray(this.baseInfoInsideDTOEdit?.financeAttachment)) {
              this.baseInfoInsideDTOEdit?.financeAttachment.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }

            this.shipAllData = JSON.parse(changeBusinessInfo.businessData)
            this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.shipAllData)
          } else {
            this.financialBaseInfoEdit = JSON.parse(JSON.stringify(newOne.financeInfoDTO))
            let baseInfoInsideDTO = JSON.parse(JSON.stringify(newOne.baseInfoInsideDTO))
            this.baseInfoInsideDTOEdit = {
              currency: baseInfoInsideDTO.currency,
              settlementType: baseInfoInsideDTO.settlementType,
              receiveGoldOrder: baseInfoInsideDTO.receiveGoldOrder,
              paymentTerm: baseInfoInsideDTO.paymentTerm,
              financeAttachment: baseInfoInsideDTO.financeAttachment || []
            }
            if (Array.isArray(this.baseInfoInsideDTOEdit?.financeAttachment)) {
              this.baseInfoInsideDTOEdit?.financeAttachment.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            this.templateConfigEdit[0].grid = Object.assign({}, this.templateConfigEdit[0].grid, {
              dataSource: newOne.bankInsideDTOList
            })
          }
          this.fileListKey++
          // 变更字段:有缓存拿缓存，无缓存，拿infoChangeRecordDTOList
          // 高亮显示修改的内容
          let baseStyle = JSON.parse(sessionStorage.getItem('businessStyleSup'))
          if (baseStyle) {
            this.styleObj = baseStyle
          } else {
            if (infoChangeRecordDTOList.length > 0) {
              infoChangeRecordDTOList.forEach((item) => {
                if (!item.listType) {
                  // 变更动作（0:更新 1:删除 2:列表新增）
                  if (item.changeFlag == 1 || item.changeFlag == 2) {
                    let str = item.changeField
                    this.styleObj[str] = false
                  }
                }
              })
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 行内编辑
    initColumn() {
      bankInfoColumn.length = 0
      const column = this.formatTableColumnShip(bankInfo(this.provinceList))
      const columns = [].concat(column)
      columns.forEach((item) => {
        bankInfoColumn.push(item)
      })
    },
    formatTableColumnShip(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'bankAccount') {
          // 银行账号
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'accountName') {
          // 开户名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'bankName') {
          // 银行名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'bankProvince') {
          // 开户行省份
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            fields: {
              text: 'areaName',
              value: 'areaCode'
            },
            dataList: this.provinceList
          })
        } else if (col.field === 'bankCity') {
          // 开户行市
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'bankOutlet') {
          // 开户银行网点
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'bankCode') {
          // 开户行联行号
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    shipComplete(args) {
      this.shipAllData = this.getShipRow()
      if (args.requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = bankInfo()
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            this.shipAllData = this.shipAllData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.shipAllData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        this.templateConfigEdit[0].grid.dataSource = this.shipAllData
      }
    },
    getShipRow() {
      let rows = this.$refs.templateRefEdit
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      rows.forEach((item) => {
        var num = Math.floor(Math.random() * (1 - 100) + 100)
        item.cid = 'business' + num
      })
      return rows
    },
    handleClickShipEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.shipAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.shipAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.shipAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-filled.e-input-group.e-control-wrapper textarea.e-input {
  padding-bottom: 8px;
}
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;
  display: flex;

  .supplier-content {
    width: 49%;
  }

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            word-break: break-all;
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 85%;
            padding: 5px 10px;
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
            .downStyle {
              cursor: pointer;
              color: #00469c;
              margin: 0 5px;
            }
          }

          .bgColor {
            background: rgba(245, 252, 255, 1);
            width: 100%;
            padding: 0 5px;
            /deep/.mt-input-number input {
              width: 100%;
            }
          }
          .highlightColor {
            background: #e8f0fe;
            width: 100%;
            padding: 0 5px;
            /deep/.e-input-group {
              color: red !important;
            }
            /deep/.e-multi-select-wrapper {
              color: red !important;
            }
            /deep/.mt-input-number input {
              width: 100%;
              color: red !important;
            }
            /deep/.red {
              color: red !important;
            }
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
