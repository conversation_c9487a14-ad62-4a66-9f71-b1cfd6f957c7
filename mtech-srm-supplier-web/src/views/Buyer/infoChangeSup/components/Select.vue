<template>
  <div>
    <mt-multi-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="false"
    ></mt-multi-select>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'text', value: 'value' },
      dataSource: [],
      isDisabled: false
    }
  },
  async mounted() {
    if (this.data.column.field === 'originAcceptMsg') {
      this.getAcceptMsgTypeOptions()
    }
    if (this.data.column.field === 'agentCategoryInfo') {
      this.getAgentCategoryInfoOptions()
    }
  },
  beforeDestroy() {},
  methods: {
    getAcceptMsgTypeOptions() {
      this.$API.infoChange['queryDict']({
        dictCode: 'acceptMsgType'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.dataSource = data?.map((item) => {
            return {
              text: item.itemName,
              value: item.itemCode
            }
          })
        }
      })
    },
    getAgentCategoryInfoOptions() {
      let orgId = this.$route.query?.orgId
        ? this.$route.query?.orgId
        : sessionStorage.getItem('orgId')
      let params = {
        categeryInfo: '',
        orgId,
        page: {
          current: 1,
          size: 100
        }
      }
      this.$API.infoChange['getSupplierQualifiedCategoryInfoSupApi'](params).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.dataSource = data?.records.map((item) => {
            return {
              text: item,
              value: item
            }
          })
        }
      })
    },
    selectChange() {}
  }
}
</script>
