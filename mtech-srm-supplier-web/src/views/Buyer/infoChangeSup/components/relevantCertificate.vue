<template>
  <div class="supplier-portrait">
    <!-- 变更前：不能修改 -->
    <div class="supplier-content">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('三证信息') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('统一社会信用代码') }}</div>
              <div class="info-content-left">
                {{ certBaseInfo.creditCode || '--' }}
              </div>
              <div class="info-title epls">{{ $t('相关经营证书') }}</div>
              <div class="info-content-right">
                {{ certBaseInfo.businessCertificate || '--' }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('营业执照附件') }}</div>
              <div class="info-content-left">
                <div style="width: 100%">
                  <span
                    class="downStyle"
                    v-for="(item, index) in certBaseInfo.licenseAttachment"
                    :key="index"
                    @click="downAttachment(item)"
                    >{{ item.fileName }}</span
                  >
                </div>
              </div>
              <div class="info-title epls">{{ $t('截止日期') }}</div>
              <div class="info-content-right">
                {{ checkDate(certBaseInfo.expireDate) }}
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('附件') }}</div>
              <div class="info-content-left" style="width: 75%">
                <div style="width: 100%">
                  <span
                    class="downStyle"
                    v-for="(item, index) in certBaseInfo.certificateAttachment"
                    :key="index"
                    @click="downAttachment(item)"
                    >{{ item.fileName }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">
            {{ $t('质量保证体系、代理证书、行业资质证书、从业资质证书') }}
          </div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('应标相关行业资质证书') }}</div>
              <div class="info-content-left">
                <div style="width: 100%">
                  <span
                    class="downStyle"
                    v-for="(item, index) in certBaseInfo.industryCertificate"
                    :key="index"
                    @click="downAttachment(item)"
                    >{{ item.fileName }}</span
                  >
                </div>
              </div>
              <div class="info-title epls">
                {{ $t('应标相关行业人员从业资质证书') }}
              </div>
              <div class="info-content-right">
                <div style="width: 100%">
                  <span
                    class="downStyle"
                    v-for="(item, index) in certBaseInfo.industryStaffCertificate"
                    :key="index"
                    @click="downAttachment(item)"
                    >{{ item.fileName }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">
            {{ $t('质量保证体系及代理认证情况-其他') }}
          </div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="certificateInfo"
              :key="certificateKey"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('代理商信息维护') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="patentInfo"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('专利信息') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="InnovationAwards"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('创新奖项') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="templateConfig"
            ></mt-template-page>
          </div>
        </div>
      </div>
    </div>
    <!-- 变更后：修改提交 -->
    <div class="supplier-content" style="margin-left: 2%">
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('三证信息') }}</div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('统一社会信用代码') }}</div>
              <div class="info-content-left">
                {{ certBaseInfoEdit.creditCode || '--' }}
              </div>
              <div class="info-title epls">{{ $t('相关经营证书') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.businessCertificate ? 'bgColor' : 'highlightColor']">
                  <mt-input
                    type="text"
                    max-length="30"
                    v-model="certBaseInfoEdit.businessCertificate"
                    @input="changeVal('businessCertificate')"
                  ></mt-input>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('营业执照附件') }}</div>
              <div class="info-content-left">
                <div :class="[styleObj.licenseAttachment ? 'bgColor' : 'highlightColor']">
                  <mt-common-uploader
                    :is-single-file="false"
                    :save-url="saveUrl"
                    type="line"
                    v-model="certBaseInfoEdit.licenseAttachment"
                    :key="fileListKey"
                    @change="changeVal('licenseAttachment')"
                    :download-url="downloadUrl"
                  ></mt-common-uploader>
                </div>
              </div>
              <div class="info-title epls">{{ $t('截止日期') }}</div>
              <div class="info-content-right">
                <div :class="[styleObj.expireDate ? 'bgColor' : 'highlightColor']">
                  <mt-date-picker
                    v-model="certBaseInfoEdit.expireDate"
                    format="yyyy-MM-dd"
                    placeholder=""
                    @input="changeVal('expireDate')"
                  ></mt-date-picker>
                </div>
              </div>
            </div>
            <div class="info-line fbox">
              <div class="info-title">{{ $t('附件') }}</div>
              <div class="info-content-left" style="width: 75%">
                <!-- <div style="width:100%" >
                  <span class="downStyle" v-for="(item,index) in certBaseInfoEdit.certificateAttachment" :key="index"
                  @click="downAttachment(item)">{{item.fileName}}</span>
                </div> -->
                <mt-common-uploader
                  :is-single-file="false"
                  :save-url="saveUrl"
                  :download-url="downloadUrl"
                  type="line"
                  v-model="certBaseInfoEdit.certificateAttachment"
                  :key="certificateAttachmentKey"
                ></mt-common-uploader>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">
            {{ $t('质量保证体系、代理证书、行业资质证书、从业资质证书') }}
          </div>
          <div class="b-info-content">
            <div class="info-line fbox">
              <div class="info-title">{{ $t('应标相关行业资质证书') }}</div>
              <div class="info-content-left">
                <!-- <div style="width:100%" >
                  <span class="downStyle" v-for="(item,index) in certBaseInfoEdit.industryCertificate" :key="index"
                  @click="downAttachment(item)">{{item.fileName}}</span>
                </div> -->
                <mt-common-uploader
                  :is-single-file="false"
                  :save-url="saveUrl"
                  :download-url="downloadUrl"
                  type="line"
                  v-model="certBaseInfoEdit.industryCertificate"
                  :key="industryCertificateKey"
                ></mt-common-uploader>
              </div>
              <div class="info-title epls">
                {{ $t('应标相关行业人员从业资质证书') }}
              </div>
              <div class="info-content-right">
                <!-- <div style="width:100%" >
                  <span class="downStyle" v-for="(item,index) in certBaseInfoEdit.industryStaffCertificate" :key="index"
                  @click="downAttachment(item)">{{item.fileName}}</span>
                </div> -->
                <mt-common-uploader
                  :is-single-file="false"
                  :save-url="saveUrl"
                  :download-url="downloadUrl"
                  type="line"
                  v-model="certBaseInfoEdit.industryStaffCertificate"
                  :key="industryStaffCertificateKey"
                ></mt-common-uploader>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">
            {{ $t('质量保证体系及代理认证情况-其他') }}
          </div>
          <div class="infos">
            <mt-template-page
              ref="certificateInfoEditRef"
              :use-tool-template="false"
              :template-config="certificateInfoEdit"
              @handleClickToolBar="handleClickCertificateEdit"
              @actionComplete="CertificateComplete"
              @cellEdit="certifiCellEdit"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('代理商信息维护') }}</div>
          <div class="infos">
            <mt-template-page
              ref="patentInfoEditRef"
              :use-tool-template="false"
              :template-config="patentInfoEdit"
              @handleClickToolBar="handleClickPatentEdit"
              @actionComplete="patentComplete"
              @cellEdit="patentCellEdit"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('专利信息') }}</div>
          <div class="infos">
            <mt-template-page
              ref="InnovationAwardsEditRef"
              :use-tool-template="false"
              :template-config="InnovationAwardsEdit"
              @handleClickToolBar="handleClickAwardsEdit"
              @actionComplete="AwardsComplete"
              @cellEdit="AwardsCellEdit"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div class="business-information fbox">
        <div class="b-info">
          <div class="b-info-title">{{ $t('创新奖项') }}</div>
          <div class="infos">
            <mt-template-page
              ref="templateRefEdit"
              :use-tool-template="false"
              :template-config="templateConfigEdit"
              @handleClickToolBar="handleClicktemplateEdit"
              @actionComplete="templateComplete"
              @cellEdit="templateCellEdit"
            ></mt-template-page>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  certificateInfoColumnOld,
  certificateInfoColumn,
  patentInfoColumnOld,
  patentInfoColumn,
  InnovationAwardsColumnOld,
  InnovationAwardsColumn,
  templateColumnOld,
  templateColumn,
  initCertificateInfoColumn,
  initPatentInfoColumn,
  initInnovationAwardsColumn,
  initTemplateColumn
} from '../config/index'
import Component from '../config/columnComponent'
import commonData from '@/utils/constant'
export default {
  props: {
    routerType: {
      type: String,
      default: ''
    },
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    },
    applyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      saveUrl: commonData.privateFileUrl,
      downloadUrl: commonData.downloadUrl,
      fileListKey: 1,
      certificateAttachmentKey: 0,
      industryCertificateKey: 0,
      industryStaffCertificateKey: 0,
      certifiAttach: [], //附件上传的信息
      patentAttach: [],
      awardsAttach: [],
      templateAttach: [],

      certificateAllData: [],
      patentAllData: [],
      AwardsAllData: [],
      templateData: [],
      certBaseInfo: {}, //三证信息
      certBaseInfoEdit: {},
      dataArr: [],
      certificateKey: 1,
      certificateInfo: [
        {
          gridId: '3e5fbfeb-eea5-4555-a1f3-be704cae4011',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: [],
            dataSource: []
          }
        }
      ],
      patentInfo: [
        {
          gridId: 'e6e3ff4d-452a-41a3-b05a-73a9196e8f9b',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: patentInfoColumnOld,
            dataSource: []
          }
        }
      ],
      InnovationAwards: [
        {
          gridId: '52ed86e3-8f67-4d5d-aed4-ab97778a9147',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: InnovationAwardsColumnOld,
            dataSource: []
          }
        }
      ],
      templateConfig: [
        {
          gridId: '6e766414-42c6-48ae-a71a-8feb33623ac2',
          toolbar: {
            useBaseConfig: false,
            tools: []
          },
          grid: {
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: templateColumnOld,
            dataSource: []
          }
        }
      ],
      // 变更后
      certificateInfoEdit: [
        {
          gridId: '2ca32597-72fb-4095-bc34-078d1e59c974',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initCertificateInfoColumn,
            dataSource: [],
            rowDataBound: this.rowDataBound
          }
        }
      ],
      patentInfoEdit: [
        {
          gridId: '0700c240-0f99-4e3c-a3b0-f661beb9891c',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initPatentInfoColumn,
            dataSource: [],
            rowDataBound: this.rowDataBoundPatent
          }
        }
      ],
      InnovationAwardsEdit: [
        {
          gridId: 'ee906e0c-cb6d-40a7-90c1-fc399361c2eb',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initInnovationAwardsColumn,
            dataSource: [],
            rowDataBound: this.rowDataBoundAwards
          }
        }
      ],
      templateConfigEdit: [
        {
          gridId: '0226af16-082a-4f99-a3b6-8c089cbce29a',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_card_plus', title: this.$t('新增') },
                { id: 'del', icon: 'icon_list_delete', title: this.$t('删除') },
                { id: 'edit', icon: 'icon_list_delete', title: this.$t('保存') }
              ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            allowSorting: false,
            allowFiltering: false,
            columnData: initTemplateColumn,
            dataSource: [],
            rowDataBound: this.rowDataBoundInfo
          }
        }
      ],
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      certificateList: [],
      styleObj: {
        businessCertificate: true,
        expireDate: true,
        licenseAttachment: true
      },

      acceptMsgTypeOptions: []
    }
  },
  async mounted() {
    this.$loading()
    this.searchCertificate()
    await this.getAcceptMsgTypeOptions()
    // 所有下拉框接口
    await this.getAllSelectList()

    this.initCertificateInfo()
    this.initPatentInfo()
    this.initAwardsInfo()
    this.initTemplateInfo()
    this.$nextTick(() => {
      this.$hloading()
    })
  },
  methods: {
    async getAcceptMsgTypeOptions() {
      await this.$API.infoChange['queryDict']({
        dictCode: 'acceptMsgType'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.acceptMsgTypeOptions = data
        }
      })
    },
    // 高亮显示修改的内容
    changeVal(item, e) {
      if (item == 'expireDate') {
        if (this.changeFlag) {
          this.styleObj[item] = false
        } else {
          this.changeFlag = true
        }
      } else {
        if (e) {
          if (e.isInteracted) {
            this.styleObj[item] = false
          }
        } else {
          this.styleObj[item] = false
        }
      }
    },

    downAttachment(item) {
      this.$utils.downloadAttachment(item)
    },
    checkDate(e) {
      if (e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd')
        } else if (typeof e == 'string') {
          if (e.indexOf('T') != -1) {
            return e.substr(0, 10)
          } else {
            let val = parseInt(e)
            return this.$utils.formateTime(val, 'yyyy-MM-dd')
          }
        } else if (typeof e == 'number') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd')
        } else {
          return e
        }
      } else {
        return e
      }
    },
    async getAllSelectList() {
      // 证书类型
      await this.getEnterpriseNature('certificateType')
    },
    async getEnterpriseNature(dictCode) {
      // this.$loading();
      await this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          // this.$hloading();
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'certificateType') {
              this.certificateList = data
              this.certificateInfo[0].grid.columnData = certificateInfoColumnOld(
                this.certificateList
              )
              this.certificateKey++
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    searchCertificate() {
      let obj = {
        applyCode: this.applyCode,
        queryArchiveRequest: {
          partnerArchiveId: this.partnerArchiveId,
          orgId: this.orgId,
          partnerRelationCode: this.partnerRelationCode,
          supplierEnterpriseId: this.supplierEnterpriseId
        }
      }
      // this.$loading();
      this.$API.infoChange
        .searchCertificateSup(obj)
        .then((res) => {
          // this.$hloading();
          let { newOne, oldOne, infoChangeRecordDTOList } = res.data
          this.certBaseInfo = oldOne.baseInfoExtDTO

          let qualityList = [] //质量保证
          let agentList = [] //代理
          let patentList = [] //专利
          let awardList = [] //创新
          oldOne.certificateDTOList.forEach((item) => {
            // 3专利，2创新奖项，1质量证书，4代理商
            if (item.certType == 1) {
              qualityList.push(item)
            } else if (item.certType == 4) {
              agentList.push(item)
            } else if (item.certType == 3) {
              patentList.push(item)
            } else if (item.certType == 2) {
              awardList.push(item)
            }
          })
          agentList.forEach((item) => {
            item.originAcceptMsg = item.originAcceptMsg ? JSON.parse(item.originAcceptMsg) : []
            item.originAcceptMsg = item.originAcceptMsg.map((v) => {
              return this.acceptMsgTypeOptions.find((ele) => ele.itemCode == v)?.itemName
            })
          })
          this.certificateInfo[0].grid = Object.assign({}, this.certificateInfo[0].grid, {
            dataSource: qualityList
          })
          this.patentInfo[0].grid = Object.assign({}, this.patentInfo[0].grid, {
            dataSource: agentList.map((item) => {
              return {
                ...item,
                agentCategoryInfo: JSON.parse(item.agentCategoryInfo)?.join(','),
                originAcceptMsg: item.originAcceptMsg.join(',')
              }
            })
          })
          this.InnovationAwards[0].grid = Object.assign({}, this.InnovationAwards[0].grid, {
            dataSource: patentList
          })
          this.templateConfig[0].grid = Object.assign({}, this.templateConfig[0].grid, {
            dataSource: awardList
          })

          let changeCertificateInfo = JSON.parse(sessionStorage.getItem('changeCertificateInfoSup'))
          if (changeCertificateInfo) {
            let baseInfoExtDTO = changeCertificateInfo.certBaseInfoEdit
            let expireDate = this.checkDate(baseInfoExtDTO.expireDate)
            this.certBaseInfoEdit = {
              creditCode: baseInfoExtDTO.creditCode,
              businessCertificate: baseInfoExtDTO.businessCertificate,
              licenseAttachment: baseInfoExtDTO.licenseAttachment || [],
              expireDate: expireDate,
              certificateAttachment: baseInfoExtDTO.certificateAttachment || [],
              industryCertificate: baseInfoExtDTO.industryCertificate || [],
              industryStaffCertificate: baseInfoExtDTO.industryStaffCertificate || []
            }
            // 组件下载用的是id   需要把fileId赋值给id
            if (Array.isArray(this.certBaseInfoEdit?.licenseAttachment)) {
              this.certBaseInfoEdit?.licenseAttachment.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            if (Array.isArray(this.certBaseInfoEdit?.certificateAttachment)) {
              this.certBaseInfoEdit?.certificateAttachment.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            if (Array.isArray(this.certBaseInfoEdit?.industryCertificate)) {
              this.certBaseInfoEdit?.industryCertificate.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            if (Array.isArray(this.certBaseInfoEdit?.industryStaffCertificate)) {
              this.certBaseInfoEdit?.industryStaffCertificate.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            this.fileListKey++
            this.certificateAttachmentKey++
            this.industryCertificateKey++
            this.industryStaffCertificateKey++
            this.certificateAllData = JSON.parse(changeCertificateInfo.certificateAllData)
            this.patentAllData = JSON.parse(changeCertificateInfo.patentAllData)
            this.AwardsAllData = JSON.parse(changeCertificateInfo.AwardsAllData)
            this.templateData = JSON.parse(changeCertificateInfo.templateData)
            this.$set(this.certificateInfoEdit[0].grid, 'dataSource', this.certificateAllData)
            this.$set(this.patentInfoEdit[0].grid, 'dataSource', this.patentAllData)
            this.$set(this.InnovationAwardsEdit[0].grid, 'dataSource', this.AwardsAllData)
            this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.templateData)
          } else {
            let qualityListEdit = [] //质量保证
            let agentListEdit = [] //代理
            let patentListEdit = [] //专利
            let awardListEdit = [] //创新
            newOne.certificateDTOList.forEach((item) => {
              // 1专利，2创新奖项，3质量证书，4代理商
              if (item.certType == 1) {
                qualityListEdit.push(item)
              } else if (item.certType == 4) {
                agentListEdit.push(item)
              } else if (item.certType == 3) {
                patentListEdit.push(item)
              } else if (item.certType == 2) {
                awardListEdit.push(item)
              }
            })
            agentListEdit.forEach((item) => {
              item.originAcceptMsg = item.originAcceptMsg ? JSON.parse(item.originAcceptMsg) : []
            })
            let baseInfoExtDTO = JSON.parse(JSON.stringify(newOne.baseInfoExtDTO))
            let expireDate = this.checkDate(baseInfoExtDTO.expireDate)
            this.certBaseInfoEdit = {
              creditCode: baseInfoExtDTO.creditCode,
              businessCertificate: baseInfoExtDTO.businessCertificate,
              licenseAttachment: baseInfoExtDTO.licenseAttachment || [],
              expireDate: expireDate,
              certificateAttachment: baseInfoExtDTO.certificateAttachment || [],
              industryCertificate: baseInfoExtDTO.industryCertificate || [],
              industryStaffCertificate: baseInfoExtDTO.industryStaffCertificate || []
            }
            if (Array.isArray(this.certBaseInfoEdit?.licenseAttachment)) {
              this.certBaseInfoEdit?.licenseAttachment.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            if (Array.isArray(this.certBaseInfoEdit?.certificateAttachment)) {
              this.certBaseInfoEdit?.certificateAttachment.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            if (Array.isArray(this.certBaseInfoEdit?.industryCertificate)) {
              this.certBaseInfoEdit?.industryCertificate.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            if (Array.isArray(this.certBaseInfoEdit?.industryStaffCertificate)) {
              this.certBaseInfoEdit?.industryStaffCertificate.forEach((item) => {
                if (item.fileId) {
                  item.id = item.fileId
                }
              })
            }
            this.fileListKey++

            this.certificateInfoEdit[0].grid = Object.assign({}, this.certificateInfoEdit[0].grid, {
              dataSource: qualityListEdit
            })
            this.patentInfoEdit[0].grid = Object.assign({}, this.patentInfoEdit[0].grid, {
              dataSource: agentListEdit.map((item) => {
                return {
                  ...item,
                  agentCategoryInfo: JSON.parse(item.agentCategoryInfo),
                  agentCategoryInfoText: JSON.parse(item.agentCategoryInfo)?.join(',')
                }
              })
            })
            this.InnovationAwardsEdit[0].grid = Object.assign(
              {},
              this.InnovationAwardsEdit[0].grid,
              { dataSource: patentListEdit }
            )
            this.templateConfigEdit[0].grid = Object.assign({}, this.templateConfigEdit[0].grid, {
              dataSource: awardListEdit
            })
          }

          // 变更字段:有缓存拿缓存，无缓存，拿infoChangeRecordDTOList
          // 高亮显示修改的内容
          let baseStyle = JSON.parse(sessionStorage.getItem('certifiStyleSup'))
          if (baseStyle) {
            this.styleObj = baseStyle
          } else {
            if (infoChangeRecordDTOList.length > 0) {
              infoChangeRecordDTOList.forEach((item) => {
                if (!item.listType) {
                  // 变更动作（0:更新 1:删除 2:列表新增）
                  if (item.changeFlag == 1 || item.changeFlag == 2) {
                    let str = item.changeField
                    this.styleObj[str] = false
                  }
                }
              })
            }
          }
        })
        .catch((err) => {
          // this.$hloading();
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 质量
    initCertificateInfo() {
      initCertificateInfoColumn.length = 0
      const column = this.formatTableColumnCertificate(certificateInfoColumn(this.certificateList))
      const columns = [].concat(column)
      columns.forEach((item) => {
        initCertificateInfoColumn.push(item)
      })
    },
    formatTableColumnCertificate(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'certificateType') {
          // 证书类型
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            fields: this.stageTypeFields,
            dataList: this.certificateList
          })
        } else if (col.field === 'certificateCode') {
          // 证书编号
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'certificateName') {
          // 证书名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'certificateBody') {
          // 认证机构
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'annualReviewDate') {
          // 年审日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'effectiveDate') {
          // 生效日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'expireDate') {
          // 截止日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'approved') {
          // 是否审核通过
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            dataList: [
              { text: this.$t('是'), value: 1 },
              { text: this.$t('否'), value: 0 }
            ]
          })
        } else if (col.field === 'attachment') {
          // 附件
          defaultCol.editTemplate = Component.mtComUpload({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBound(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    // 附件上传后抛出来的钩子
    certifiCellEdit(list) {
      // 所上传的文件集合
      this.certifiAttach = list.fileList
    },
    CertificateComplete(args) {
      const { requestType, action } = args
      let allData = this.getCertificateRow(action)
      if (requestType == 'add') {
        this.certifiAttach = [] //新增一条新数据，附件集合清空；
      } else if (requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = certificateInfoColumn()
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.certificateInfoEditRef
              .getCurrentUsefulRef()
              .gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            allData = allData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.certificateInfoEdit[0].grid, 'dataSource', allData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        allData.forEach((item) => {
          if (typeof item.attachment == 'string' && item.attachment != '') {
            let arr = item.attachment.split(',')
            arr.forEach((aitem, index) => {
              this.certifiAttach.forEach((citem) => {
                citem.fileUrl = citem.url
                if (aitem == citem.fileName) {
                  arr[index] = citem
                }
              })
            })
            item.attachment = arr
          } else if (item.attachment == '') {
            item.attachment = []
          }
        })
      }

      this.certificateAllData = allData
      if (requestType == 'save') {
        this.certificateInfoEdit[0].grid.dataSource = this.certificateAllData
      }
    },
    getCertificateRow(action) {
      let rows = this.$refs.certificateInfoEditRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      if (action == 'add') {
        rows.forEach((item) => {
          item.certType = 1
          var num = Math.floor(Math.random() * (1 - 100) + 100)
          item.cid = 'certifi' + num
        })
      }

      return rows
    },
    handleClickCertificateEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.certificateInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.certificateAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.certificateAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.certificateInfoEdit[0].grid, 'dataSource', this.certificateAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.certificateInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // 代理商
    initPatentInfo() {
      initPatentInfoColumn.length = 0
      const column = this.formatTableColumnPatent(patentInfoColumn(this.acceptMsgTypeOptions))
      const columns = [].concat(column)
      columns.forEach((item) => {
        initPatentInfoColumn.push(item)
      })
    },
    formatTableColumnPatent(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'originFactoryName') {
          // 原厂名
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'originFactoryEnglishName') {
          // 原厂名e
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'contactName') {
          // 原厂联系人
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'contactTel') {
          // 原厂联系人电话
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'proxyCertificate') {
          // 代理证
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'effectiveDate') {
          // 生效日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'expireDate') {
          // 截止日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'contactTel') {
          // 电话
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'contractMobile') {
          // 手机
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'agentProduct') {
          // 代理产品
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'attachment') {
          // 附件
          defaultCol.editTemplate = Component.mtComUpload({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundPatent(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    // 附件上传后抛出来的钩子
    patentCellEdit(list) {
      // 所上传的文件集合
      this.patentAttach = list.fileList
    },
    patentComplete(args) {
      const { requestType, action } = args
      let allData = this.getPatentRow(action)
      if (requestType == 'add') {
        this.patentAttach = [] //新增一条新数据，附件集合清空；
      } else if (requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(patentInfoColumn))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.patentInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            allData = allData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.patentInfoEdit[0].grid, 'dataSource', allData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        allData.forEach((item) => {
          if (typeof item.attachment == 'string' && item.attachment != '') {
            let arr = item.attachment.split(',')
            arr.forEach((aitem, index) => {
              this.patentAttach.forEach((citem) => {
                citem.fileUrl = citem.url
                if (aitem == citem.fileName) {
                  arr[index] = citem
                }
              })
            })
            item.attachment = arr
          } else if (item.attachment == '') {
            item.attachment = []
          }
        })
      }
      this.patentAllData = allData
      if (requestType == 'save') {
        this.patentInfoEdit[0].grid.dataSource = this.patentAllData
      }
    },
    getPatentRow(action) {
      let rows = this.$refs.patentInfoEditRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      if (action == 'add') {
        rows.forEach((item) => {
          item.certType = 4
          var num = Math.floor(Math.random() * (1 - 100) + 100)
          item.cid = 'patent' + num
        })
      }

      return rows
    },
    handleClickPatentEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.patentInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.patentAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.patentAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.patentInfoEdit[0].grid, 'dataSource', this.patentAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.patentInfoEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // 专利
    initAwardsInfo() {
      initInnovationAwardsColumn.length = 0
      const column = this.formatTableColumnAwards(InnovationAwardsColumn)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initInnovationAwardsColumn.push(item)
      })
    },
    formatTableColumnAwards(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'patentName') {
          // 专利名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'awardDate') {
          // 专利获得日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'issuingAuthority') {
          // 颁发机构
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'attachment') {
          // 附件上传
          defaultCol.editTemplate = Component.mtComUpload({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundAwards(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    // 附件上传后抛出来的钩子
    AwardsCellEdit(list) {
      // 所上传的文件集合
      this.awardsAttach = list.fileList
    },
    AwardsComplete(args) {
      const { requestType, action } = args
      let allData = this.getAwardsRow(action)
      if (requestType == 'add') {
        this.awardsAttach = [] //新增一条新数据，附件集合清空；
      } else if (requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(InnovationAwardsColumn))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(
              rowIndex
            )
          } else {
            allData = allData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.InnovationAwardsEdit[0].grid, 'dataSource', allData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        allData.forEach((item) => {
          if (typeof item.attachment == 'string' && item.attachment != '') {
            let arr = item.attachment.split(',')
            arr.forEach((aitem, index) => {
              this.awardsAttach.forEach((citem) => {
                citem.fileUrl = citem.url
                if (aitem == citem.fileName) {
                  arr[index] = citem
                }
              })
            })
            item.attachment = arr
          } else if (item.attachment == '') {
            item.attachment = []
          }
        })
      }
      this.AwardsAllData = allData
      if (requestType == 'save') {
        this.InnovationAwardsEdit[0].grid.dataSource = this.AwardsAllData
      }
    },
    getAwardsRow(action) {
      let rows =
        this.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
      if (action == 'add') {
        rows.forEach((item) => {
          item.certType = 3
          var num = Math.floor(Math.random() * (1 - 100) + 100)
          item.cid = 'Awards' + num
        })
      }

      return rows
    },
    handleClickAwardsEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.AwardsAllData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.AwardsAllData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.InnovationAwardsEdit[0].grid, 'dataSource', this.AwardsAllData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.InnovationAwardsEditRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // 创新
    initTemplateInfo() {
      initTemplateColumn.length = 0
      const column = this.formatTableColumn(templateColumn)
      const columns = [].concat(column)
      columns.forEach((item) => {
        initTemplateColumn.push(item)
      })
    },
    formatTableColumn(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'awardName') {
          // 奖项名称
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'awardDate') {
          // 获奖日期
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'issuingAuthority') {
          // 颁发机构
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'attachment') {
          // 附件上传
          defaultCol.editTemplate = Component.mtComUpload({
            dataKey: col.field
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    rowDataBoundInfo(args) {
      // 执行数据渲染后，调整数据样式
      // changeFlag = 1，2 是行有变更， 默认为0
      if (args.data.changeFlag == 1 || args.data.changeFlag == 2) {
        args.row.classList.add('bg-blue')
      }
    },
    // 附件上传后抛出来的钩子
    templateCellEdit(list) {
      // 所上传的文件集合
      this.templateAttach = list.fileList
    },
    templateComplete(args) {
      const { requestType, action } = args
      let allData = this.getTemplateRow(action)
      if (requestType == 'add') {
        this.templateAttach = [] //新增一条新数据，附件集合清空；
      } else if (requestType == 'save') {
        const { data, rowIndex, action } = args
        let forecastColumnsCopy = JSON.parse(JSON.stringify(templateColumn))
        forecastColumnsCopy.splice(0, 2)
        const allNull = forecastColumnsCopy.every((ditem) => {
          const value = data[ditem.field]
          return (
            value === 'null' ||
            value === null ||
            value === undefined ||
            value === '[]' ||
            value === '' ||
            value === ' '
          )
        })
        if (allNull) {
          if (action == 'add') {
            this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord(rowIndex)
          } else {
            allData = allData.filter((sitem) => {
              return sitem.cid !== data.cid
            })
            this.$set(this.templateConfigEdit[0].grid, 'dataSource', allData)
          }
          return
        }
        if (args.action == 'add' || args.action == 'edit') {
          args.data.changeFlag = 1
        }
        allData.forEach((item) => {
          if (typeof item.attachment == 'string' && item.attachment != '') {
            let arr = item.attachment.split(',')
            arr.forEach((aitem, index) => {
              this.templateAttach.forEach((citem) => {
                citem.fileUrl = citem.url
                if (aitem == citem.fileName) {
                  arr[index] = citem
                }
              })
            })
            item.attachment = arr
          } else if (item.attachment == '') {
            item.attachment = []
          }
        })
      }
      this.templateData = allData
      if (requestType == 'save') {
        this.templateConfigEdit[0].grid.dataSource = this.templateData
      }
    },
    getTemplateRow(action) {
      let rows = this.$refs.templateRefEdit
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      if (action == 'add') {
        rows.forEach((item) => {
          item.certType = 2
          var num = Math.floor(Math.random() * (1 - 100) + 100)
          item.cid = 'template' + num
        })
      }

      return rows
    },
    handleClicktemplateEdit(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'del') {
        // 选中的数据
        let selectedRecords = gridRef.getMtechGridRecords()
        if (selectedRecords.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              selectedRecords.forEach((item) => {
                this.templateData.forEach((sitem, sindex) => {
                  if (item.cid == sitem.cid) {
                    this.templateData.splice(sindex, 1)
                  }
                })
              })
              this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.templateData)
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id == 'edit') {
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-filled.e-input-group.e-control-wrapper textarea.e-input {
  padding-bottom: 8px;
}
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;
  display: flex;
  .supplier-content {
    width: 49%;
  }
  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            word-break: break-all;
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 85%;
            padding: 5px 10px;
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
            .downStyle {
              cursor: pointer;
              color: #00469c;
              margin: 0 5px;
            }
          }

          .bgColor {
            background: rgba(245, 252, 255, 1);
            width: 100%;
            padding: 0 5px;
            /deep/.mt-input-number input {
              width: 100%;
            }
            /deep/.mt-common-uploader .main {
              min-width: 125px;
              .cell-operable-title {
                white-space: nowrap;
              }
            }
          }
          .highlightColor {
            background: #e8f0fe;
            width: 100%;
            padding: 0 5px;

            /deep/.e-input-group {
              color: red !important;
            }
            /deep/.mt-input-number input {
              width: 100%;
            }
            /deep/.mt-common-uploader .main {
              min-width: 125px;
              .cell-operable-title {
                white-space: nowrap;
              }
            }
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 25%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
            .downStyle {
              cursor: pointer;
              color: #00469c;
              margin: 0 5px;
            }
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
