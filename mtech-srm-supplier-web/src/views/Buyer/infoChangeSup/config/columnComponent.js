import Vue from 'vue'
import utils from '@/utils/utils'
import commonData from '@/utils/constant'

// 预测管理 Component 集合
const ForecastColumnComponent = {
  // 文本 新增行时带出的数据，不可编辑
  changedInput: () => {
    const template = () => {
      return {
        template: Vue.component('changedInputComponent', {
          template: `
          <mt-input
                :value="data[dataKey]"
                disabled
              ></mt-input>`,
          data: function () {
            return {}
          }
        })
      }
    }
    return template
  },
  // 编辑时某些字段不能编辑
  notEdit: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div :id="dataKey">{{data[dataKey]}}</div>`,
          data: function () {
            return {
              data: {},
              dataKey
            }
          },
          methods: {}
        })
      }
    }
    return template
  },
  // 编辑行时的 文本数据
  input: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <mt-input type="text" :id="dataKey" v-model="data[dataKey]"></mt-input>`,
          data: function () {
            return {
              data: {},
              dataKey
            }
          },
          methods: {}
        })
      }
    }
    return template
  },
  // 编辑行时的 文本数据
  number: (args) => {
    const { dataKey, precisionNum } = args
    const template = () => {
      return {
        template: Vue.component('numberComponent', {
          template: `
          <div>
            <mt-input :id="dataKey" :value="data[dataKey]" style="display: none"></mt-input>
            <mt-inputNumber
              :id="dataKey"
              :precision=precision
              :min="0"
              v-model="data[dataKey]"
            ></mt-inputNumber>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              precision: 2
            }
          },
          mounted() {
            if (!!precisionNum || precisionNum == 0) {
              this.precision = precisionNum
            }
          },
          methods: {}
        })
      }
    }
    return template
  },
  // 编辑行时的 日期选择框
  datePicker: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('dateComponent', {
          template: `<mt-date-picker :id="dataKey" v-model="data[dataKey]" :openOnFocus="true" placeholder=""></mt-date-picker>`,
          data: function () {
            return {
              data: {},
              dataKey
            }
          },
          created() {
            this.data[dataKey] = this.checkDate(this.data[dataKey])
          },
          methods: {
            checkDate(e) {
              if (e) {
                if (e == 0) {
                  return (e = '')
                } else if (typeof e == 'object') {
                  return utils.formateTime(e, 'yyyy-MM-dd')
                } else if (typeof e == 'string') {
                  if (e.indexOf('T') != -1) {
                    // return e.substr(0,10);
                    return utils.formateTime(new Date(e), 'yyyy-MM-dd')
                  } else {
                    let val = parseInt(e)
                    return utils.formateTime(val, 'yyyy-MM-dd')
                  }
                } else if (typeof e == 'number') {
                  return utils.formateTime(e, 'yyyy-MM-dd')
                } else {
                  return e
                }
              } else {
                return e
              }
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 新增行时可编辑
  select: (args) => {
    const { dataKey, fields, dataList } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <mt-select
            :id="dataKey"
            v-model="data[dataKey]"
            :dataSource="dataList"
            :fields="fields"
            :showClearButton="true"
          ></mt-select>`,
          data: function () {
            return {
              data: {},
              dataKey,
              fields,
              dataList
            }
          }
        })
      }
    }
    return template
  },
  areaSelect: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('areaSelect', {
          template: `
            <mt-select
              :id="dataKey"
              :disabled="disabled"
              v-model="data[dataKey]"
              :dataSource="dataList"
              :fields="{ text: 'itemName', value: 'itemCode' }"
              :showClearButton="true"
              @open="open"
              @change="handleChange"
            ></mt-select>
          `,
          data: function () {
            return {
              data: {},
              dataKey,
              disabled: false,
              dataList: []
            }
          },
          mounted() {
            this.getOptions()
          },
          methods: {
            open() {},
            getOptions() {
              this.$API.infoChange['queryDict']({
                dictCode: 'supply_area'
              }).then((res) => {
                if (res.code) {
                  this.dataList = res.data
                }
              })
            },
            handleChange(e) {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'area',
                value: {
                  areaName: e.itemData?.itemName,
                  areaValue: e.itemData?.itemCode
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 新增CheckBox选择框
  checkbox: () => {
    return {
      template: Vue.component('checkboxComponent', {
        template: `<div><mt-checkbox
          v-model="checkboxVal"
        ></mt-checkbox></div>`,
        data: function () {
          return {
            data: {},
            checkboxVal: false
          }
        }
      })
    }
  },
  // 上传文件
  mtComUpload: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('mtCommonUploadComponent', {
          template: `
          <div>
            <mt-input :id="dataKey" :value="inputValue" style="display: none"></mt-input>
            <mt-common-uploader :save-url="saveUrl" :downloadUrl="downloadUrl" :id="dataKey" :isSingleFile='false' type='line' v-model="fileList"></mt-common-uploader>
          </div>
          `,
          data: function () {
            return {
              data: {},
              dataKey,
              fileList: [],
              saveUrl: commonData.privateFileUrl,
              downloadUrl: commonData.downloadUrl,
              inputValue: ''
            }
          },
          created() {
            // 编辑的时候传过来的附件集合
            if (this.data.attachment) {
              this.fileList = JSON.parse(JSON.stringify(this.data.attachment))
              let arr = []
              this.data.attachment.forEach((item) => {
                arr.push(item.fileName)
              })
              this.inputValue = arr.join(',')
            }
          },
          watch: {
            fileList: {
              handler(v) {
                if (!this.data.attachment || JSON.stringify(this.data.attachment) === '[""]') {
                  this.data.attachment = []
                }
                // if(v.length>0){
                let arr = []
                v.forEach((item) => {
                  if (item.fileId) {
                    item.id = item.fileId
                  }
                  arr.push(item.fileName)
                  // this.data[dataKey] = arr.join(',')
                })
                this.inputValue = arr.join(',')
                this.$parent.$emit('cellEdit', {
                  fileList: v
                })
                // }
              },
              deep: true,
              immediate: true
            }
          }
        })
      }
    }
    return template
  },
  // 编辑时的 checkBox下变成空
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  /**
   * 预测数据
   *
   * timeInfo => 预测时间;
   * forecastNum => D1预测;
   * orderNum => D2订单;
   * total => D;
   * buyerNum => P;
   * supplierNum => C;
   * planGroupNum => F;
   */
  forecast: (args) => {
    const {
      colIndex,
      type,
      dynamicTitle,
      ForecastDataTypeCell,
      ComponentType,
      Status,
      rowDataTemp
    } = args
    const template = () => {
      return {
        template: Vue.component('forecastComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column" v-if="data[dynamicTitle[colIndex]]">
            <!-- D1(预测)行 view -->
            <div class="forecast-item" v-if="(type === ComponentType.view || data.id) && type !== ComponentType.mustEdit">{{data[dynamicTitle[colIndex]].forecastNum}}</div>
            <!-- D1(预测)行 edit -->
            <div v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit">
              <mt-input-number
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].forecastNum"
                @change="changeForecastNum"
                placeholder="">
              </mt-input-number>
            </div>
            <!-- D2(订单)行 view -->
            <div class="forecast-item" v-if="(type === ComponentType.view || data.id) && type !== ComponentType.mustEdit">{{data[dynamicTitle[colIndex]].orderNum}}</div>
            <!-- D2(订单)行 edit -->
            <div v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit">
              <mt-input-number
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].orderNum"
                @change="changeOrderNum"
                placeholder="">
              </mt-input-number>
            </div>
            <!-- D行 view -->
            <div class="forecast-item" v-if="(type === ComponentType.view || data.id) && type !== ComponentType.mustEdit">{{data[dynamicTitle[colIndex]].total}}</div>
            <!-- D行 edit -->
            <div v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit">
              <mt-input-number
                disabled
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].total"
                @change="changeTotal"
                placeholder="">
              </mt-input-number>
            </div>
            <!-- P行 view -->
            <div
              :class="['forecast-item', isBuyerNumHighlight() && 'forecast-highlight']"
              v-show="type === ComponentType.view"
              >{{data[dynamicTitle[colIndex]].buyerNum}}</div>
            <!-- P行 edit -->
            <div v-if="type === ComponentType.edit || type === ComponentType.mustEdit">
              <mt-input-number
                :show-clear-button="false"
                :show-spin-button="false"
                :disabled="isDisabledBuyerNumEdit()"
                v-model="data[dynamicTitle[colIndex]].buyerNum"
                @change="changeBuyerNum"
                placeholder="">
              </mt-input-number>
            </div>
            <!-- C行 仅可查看 -->
            <div class="forecast-item">{{data[dynamicTitle[colIndex]].supplierNum}}</div>
            <!-- Gap行 仅可查看 -->
            <div class="forecast-item">{{gap}}</div>
            <!-- F行 view -->
            <div class="forecast-item"
              v-show="type === ComponentType.view"
              >{{data[dynamicTitle[colIndex]].planGroupNum}}</div>
            <!-- F行 edit -->
            <div v-if="type === ComponentType.edit">
              <mt-input-number
                :show-clear-button="false"
                :show-spin-button="false"
                :disabled="isDisabledPlanGroupNumEdit()"
                v-model="data[dynamicTitle[colIndex]].planGroupNum"
                @change="changePlanGroupNum"
                placeholder="">
              </mt-input-number>
            </div>
          </div>
          <div class="grid-edit-column mt-flex-direction-column" v-else>
            <div v-for="(cellItem, index) in cellItems" :key="index" class="forecast-item"></div>
          </div>`,
          data: function () {
            return {
              data: {},
              colIndex,
              dynamicTitle,
              ComponentType,
              type,
              gap: '',
              cellItems: ForecastDataTypeCell
            }
          },
          mounted() {
            // 计算 Gap 行
            this.calculateGap()
          },
          methods: {
            // 计算 Gap 行
            calculateGap() {
              const supplierNum = this.data[dynamicTitle[colIndex]]?.supplierNum || 0 // C行
              const buyerNum = this.data[dynamicTitle[colIndex]]?.buyerNum || 0 // P行
              if (supplierNum >= 0 || buyerNum >= 0) {
                this.gap = supplierNum - buyerNum // Gap 差异 = C行 - P行
              }
            },
            // change D1预测 forecastNum
            changeForecastNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].forecastNum =
                Number(value)
              // 关联D行
              const orderNum = Number(this.data[dynamicTitle[colIndex]].orderNum)
              const total = orderNum + Number(value)
              this.data[dynamicTitle[colIndex]].total = total
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = total
            },
            // change D2订单 orderNum
            changeOrderNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].orderNum = Number(value)
              // 关联D行
              const forecastNum = Number(this.data[dynamicTitle[colIndex]].forecastNum)
              const total = forecastNum + Number(value)
              this.data[dynamicTitle[colIndex]].total = total
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = total
            },
            // change D total
            changeTotal(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = Number(value)
              // 计算 Gap 行
              this.calculateGap()
            },
            // change P buyerNum
            changeBuyerNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].buyerNum = Number(value)
              // 计算 Gap 行
              this.calculateGap()
            },
            // P 行可编辑的条件
            isDisabledBuyerNumEdit() {
              let disabled = true
              // 条件
              const condition = [
                Status.feedbackAbnormal, // 反馈异常
                Status.feedbackNormal, // 反馈异常
                Status.modified, // 已修改
                Status.new // 新增
              ]
              if (condition.includes(this.data?.status)) {
                disabled = false
              }

              return disabled
            },
            // change F planGroupNum
            changePlanGroupNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].planGroupNum =
                Number(value)
            },
            // F 行可编辑的条件
            isDisabledPlanGroupNumEdit() {
              let disabled = true
              // 条件
              const condition = [
                Status.new, // 新建
                Status.modified, // 待反馈
                Status.pendingFeedback, // 待反馈
                Status.feedbackNormal, // 反馈正常
                Status.feedbackAbnormal, // 反馈异常
                Status.confirmed // 已确认
              ]
              if (condition.includes(this.data?.status)) {
                disabled = false
              }

              return disabled
            },
            // P 行不等于 D 行时高亮
            isBuyerNumHighlight() {
              let isHighlight = false
              const buyerNum = this.data[dynamicTitle[colIndex]].buyerNum // P
              const total = this.data[dynamicTitle[colIndex]].total // D

              if (buyerNum != total) {
                isHighlight = true
              }

              return isHighlight
            }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的类型 D1(预测) D2(订单) D P C F
  type: (args) => {
    const { ForecastDataTypeCell } = args
    return {
      template: Vue.component('typeComponent', {
        template: `<div class="grid-edit-column mt-flex-direction-column forecast-type-box"><div class="forecast-item" v-for="(cellItem, index) in cellItems" :key="index">{{cellItem.title}}</div></div>`,
        data: function () {
          return {
            data: {},
            cellItems: ForecastDataTypeCell
          }
        }
      })
    }
  },
  // 不可编辑的文字
  text: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span>{{data[dataKey]}}</span></div></div>`,
          data: function () {
            return { data: {}, dataKey }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的错误文字
  errorText: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span class="errorText">{{data[dataKey]}}</span></div></div>`,
          data: function () {
            return { data: {}, dataKey }
          }
        })
      }
    }
    return template
  },
  // 版本
  versionText: () => {
    return {
      template: Vue.component('textComponent', {
        template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span>{{version}}</span></div></div>`,
        data: function () {
          return { data: {}, version: '' }
        },
        mounted() {
          if (this.data.syncVersion && this.data.publishVersion >= 0) {
            this.version = `${this.data.syncVersion}.${this.data.publishVersion}`
          } else if (this.data.syncVersion) {
            this.version = this.data.syncVersion
          }
        }
      })
    }
  },
  // 状态
  status: (args) => {
    const { type, cellTools, StatusClass, StatusText, ComponentType } = args
    const template = () => {
      return {
        // 按钮：发布，取消发布，删除，确认
        template: Vue.component('statusComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"
          >
            <div class="field-content"><span :class="[StatusClass[data.status]]">{{data.status | dataFormat}}</span></div>
            <div class="column-tool mt-flex invite-btn" v-if="type === ComponentType.view">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              type,
              StatusClass,
              StatusText,
              cellTools,
              ComponentType
            }
          },
          filters: {
            // 值转换
            dataFormat(value) {
              let result = value
              if (StatusText[value]) {
                result = StatusText[value]
              }
              return result
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  }
}

export default ForecastColumnComponent
