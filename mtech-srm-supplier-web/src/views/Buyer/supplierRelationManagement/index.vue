<template>
  <div>
    <mt-tabs
      ref="mtTabsRef"
      :e-tab="false"
      :data-source="tabList"
      :halt-select="false"
      @handleSelectTab="handleTabChange"
    />
    <keep-alive>
      <component ref="mainContent" :is="activeComponent" />
    </keep-alive>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeTabIndex: 0,
      tabList: [
        { title: this.$t('原厂及代理供应商') },
        { title: this.$t('屏采集团供应商') },
        { title: this.$t('多级供应商') },
        { title: this.$t('海外直采供应商') }
      ]
    }
  },
  computed: {
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 原厂及代理供应商
          comp = () => import('./tabsPage/originalAndAgent/index.vue')
          break
        case 1:
          // 屏采集团供应商
          comp = () => import('./tabsPage/screenMiningGroupSupplier/index.vue')
          break
        case 2:
          // 多级供应商
          comp = () => import('./tabsPage/multilevelSupplier/index.vue')
          break
        case 3:
          // 海外直采供应商
          comp = () => import('./tabsPage/overseasDirectProcurement/index.vue')
          break
        default:
          break
      }
      return comp
    }
  },
  methods: {
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    }
  }
}
</script>
