// 认证需求
<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    :height="445"
    :width="600"
  >
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('所属公司')" label-style="top" prop="orgId">
          <RemoteAutocomplete
            v-model="formInfo.orgCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :placeholder="$t('请选择所属公司')"
          />
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('供应商名称')"
          label-style="top"
          prop="supplierCode"
        >
          <RemoteAutocomplete
            v-model="formInfo.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierCode', 'supplierName']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('集团供应商名称')"
          label-style="top"
          prop="groupSupplierName"
        >
          <mt-input
            v-model="formInfo.groupSupplierName"
            :placeholder="$t('请输入集团供应商名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('集团供应商编号')"
          label-style="top"
          prop="groupSupplierCode"
        >
          <mt-input
            v-model="formInfo.groupSupplierCode"
            :placeholder="$t('请输入集团供应商编号')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
// import utils from '@/utils/utils'
// import { cloneDeep } from 'lodash'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  data() {
    return {
      demandDesc: '',
      imgUrl: '',

      isCateShow: true,
      isEmpShow: true,
      isSupShow: true,
      employeeId: null,
      // orgList: [],
      deptList: [],
      categoryList: [],
      supplierList: [],
      userList: [],
      rules: {
        orgCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        groupSupplierName: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        groupSupplierCode: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      formInfo: {
        orgCode: '',
        groupSupplierName: '',
        supplierCode: [],
        groupSupplierCode: ''
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    disable() {
      return this.modalData.disable
    },
    buttons() {
      return [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  async created() {},
  mounted() {
    this.show()
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$loading()
          console.log('this.formInfo', this.formInfo)
          this.$API.supplierRelationshipReport
            .addPingcaiSuplier(this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$hloading()
                console.log('res', res)
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              } else {
                this.$hloading()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-rich-text-editor {
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    height: 170px !important;
    padding: 0px 7px;
    em {
      font: revert;
    }
    ol,
    ul {
      list-style: revert;
    }
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
// .form-item-spec {
//   width: 414px;
// }
// /deep/ .form-box {
//   display: flex;
//   flex-wrap: wrap;
//   justify-content: space-between;
// }
/deep/ .full-width {
  width: 100% !important;
}
</style>
<style lang="scss">
.d72562-c40d-4933-9a24-98c3298365ac {
  li {
    display: flex;
    align-items: center;
    & > div {
      flex-shrink: 0;
    }
    & > div:first-child {
      margin-left: 20px;
    }
  }
}
</style>
