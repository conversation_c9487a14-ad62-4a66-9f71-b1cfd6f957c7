<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <!-- <mt-form-item
              class="form-item"
              :label="$t('供应商名称')"
              label-style="top"
              prop="supplierCode"
            >
              <RemoteAutocomplete
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :placeholder="$t('请选择供应商')"
                popup-width="330"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierCode', 'supplierName']"
              ></RemoteAutocomplete>
            </mt-form-item> -->
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <mt-input v-model="searchFormModel.supplierCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input v-model="searchFormModel.supplierName"></mt-input>
            </mt-form-item>

            <mt-form-item
              prop="internationalTradeTermsCode"
              :label="$t('国际贸易条件')"
              label-style="top"
            >
              <mt-input v-model="searchFormModel.internationalTradeTermsCode"></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="internationalTermsTrade"
              :label="$t('加点贸易条件')"
              label-style="top"
            >
              <mt-input v-model="searchFormModel.internationalTermsTrade"></mt-input>
            </mt-form-item>
            <mt-form-item prop="payConditionCode" :label="$t('结算方式')" label-style="top">
              <mt-select
                v-model="searchFormModel.payConditionCode"
                css-class="rule-element"
                :data-source="payCondition"
                :fields="{ text: 'paymentTermsName', value: 'paymentTermsCode' }"
                :allow-filtering="true"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="groupSupplierCode" :label="$t('集团供应商编号')" label-style="top">
              <mt-input v-model="searchFormModel.groupSupplierCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="groupSupplierName" :label="$t('集团供应商名称')" label-style="top">
              <mt-input v-model="searchFormModel.groupSupplierName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input v-model="searchFormModel.createUserName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                :placeholder="$t('请选择创建时间')"
                @change="(e) => handleDateTimeChange(e, 'CreateDate')"
              ></mt-date-range-picker>
            </mt-form-item>
            <mt-form-item prop="modifyDate" :label="$t('更新时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.modifyDate"
                :placeholder="$t('请选择最后更新时间')"
                @change="(e) => handleDateTimeChange(e, 'ModifyDate')"
              ></mt-date-range-picker>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>
<script>
import { deadmaterialCols, editSettings } from './config'
// import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getHeadersFileName, download } from '@/utils/utils.js'

export default {
  // components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [
        {
          gridId: '6931300e-ecf2-4267-83eb-c5c3a8fa73dc',
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'maintainRelationship',
                  icon: 'icon_solid_Createorder ',
                  title: this.$t('供应商关系维护')
                },
                { id: 'ImportCate', icon: 'icon_solid_upload', title: this.$t('导入') },
                { id: 'ExportCate', icon: 'icon_solid_Download', title: this.$t('导出') },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete ',
                  title: this.$t('删除')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: deadmaterialCols(this),
            editSettings,
            frozenColumns: 1,
            asyncConfig: {
              url: '/supplier/tenant/buyer/pingcai/group/list'
            }
          }
        }
      ],
      payCondition: []
    }
  },
  created() {
    this.queryPaymentTerms()
  },
  methods: {
    // 付款条件接口
    queryPaymentTerms() {
      return this.$API.supplierEffective.queryPaymentTerms({}).then((res) => {
        if (res.code === 200) {
          this.payCondition = res.data || []
        }
      })
    },
    // 重置
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (records.length <= 0 && e.toolbar.id === 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'maintainRelationship') {
        this.handleAdd()
        return
      } else if (e.toolbar.id == 'ImportCate') {
        this.supplierImportFn()
      } else if (e.toolbar.id == 'ExportCate') {
        this.supplierDownload()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(records)
      }
    },
    // 供应商关系维护
    handleAdd() {
      this.$dialog({
        modal: () => import('./coms/targetDialog.vue'),
        data: {
          title: this.$t('供应商关系维护')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //Excel导入
    supplierImportFn() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'multipartFile',
          importApi: this.$API.supplierRelationshipReport.supplierImport,
          downloadTemplateApi: this.$API.supplierRelationshipReport.supplierExportTmp
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //导出
    supplierDownload() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.supplierRelationshipReport.supplierExportQuery(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 删除
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          const params = ids.map((e) => e.id)
          this.$API.supplierRelationshipReport.deletePingcaiSuplier(params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })

            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleCreate(records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认创建数据？')
        },
        success: () => {
          const params = {
            createType: 1,
            headerId: records[0]['id']
          }
          this.$API.deadMaterials.createClaimVoucher(params).then(() => {
            this.$toast({
              content: this.$t('创建成功'),
              type: 'success'
            })

            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // handleClickCellTitle(e) {
    //   if (e && e.field == 'code') {
    //     let { data } = e
    //     sessionStorage.setItem(
    //       'deadMaterialsListInfo',
    //       JSON.stringify({
    //         id: data.id,
    //         // approvalNo: data.approvalNo,
    //         // companyCode: data.companyCode,
    //         // companyName: data.companyName
    //         ...data
    //       })
    //     )
    //     this.$router.push({
    //       path: '/supplier/pur/idle-material-claimsDetail',
    //       query: {
    //         id: data.id,
    //         timeStamp: new Date().getTime()
    //       }
    //     })
    //   } else {
    //     this.$router.push({
    //       name: 'idle-material-claimsDetail'
    //     })
    //   }
    // },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        const validateMap = {
          internationalTradeTermsCode: {
            value: data.internationalTradeTermsCode,
            msg: this.$t('请选择国际贸易条件')
          },
          internationalTermsTrade: {
            value: data.internationalTermsTrade,
            msg: this.$t('请选择加点国际贸易条件')
          },
          payConditionName: {
            value: data.payConditionName,
            msg: this.$t('请选择结算方式')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType, data } = args
      if (requestType == 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    save(rowData) {
      const params = { ...rowData }
      this.$API.supplierRelationshipReport.updatePingcaiSuplier(params).then((res) => {
        if (res.code === 200) {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
