import { i18n } from '@/main.js'
import Vue from 'vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

// import selectedItemCode from '../coms/selectItemCode'
// import cellChanged from '../coms/cellChanged.vue'

export default {
  components: {
    RemoteAutocomplete
  }
}
// export const payCondition = axios
//   .post('/api/masterDataManagement/tenant/payment-terms/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}', {})
//   .then((res) => {
//     console.log('payConditionpayCondition', res.data.data)

//     return res.data.data || []
//   })

export const deadmaterialCols = (that) => {
  const config = [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      field: 'supplierNameEn',
      headerText: i18n.t('供应商英文名称'),
      allowEditing: false
    },
    {
      field: 'supplierAddr',
      headerText: i18n.t('供应商地址'),
      allowEditing: false
    },
    {
      field: 'supplierAddrEn',
      headerText: i18n.t('供应商英文地址'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'internationalTradeTermsCode',
      headerText: i18n.t('国际贸易条件'),
      ignore: true,
      headerTemplate: () => {
        return {
          template: Vue.component('autoCalcIndexTypeTemplate', {
            template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('国际贸易条件')}}</span>
              </div>
            `
          })
        }
      }
    },
    {
      field: 'internationalTermsTrade',
      headerText: i18n.t('加点国际贸易条件'),
      headerTemplate: () => {
        return {
          template: Vue.component('autoCalcIndexTypeTemplate', {
            template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('加点国际贸易条件')}}</span>
              </div>
            `
          })
        }
      }
    },
    {
      field: 'payConditionName',
      headerText: i18n.t('结算方式'),
      headerTemplate: () => {
        return {
          template: Vue.component('autoCalcIndexTypeTemplate', {
            template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('结算方式')}}</span>
              </div>
            `
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.paymentTermsCode}
              fields={{ text: 'paymentTermsName', value: 'paymentTermsCode' }}
              dataSource={that.payCondition}
              allow-filtering={true}
              filter-type='Contains'
              placeholder='请选择结算方式'
              onChange={(e) => {
                const val = e.value || ''
                const exitTitem = that.payCondition.find((item) => item.paymentTermsCode === val)
                scoped.payConditionName = exitTitem ? exitTitem.paymentTermsName : '未匹配'
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'groupSupplierCode',
      headerText: i18n.t('集团供应商编码'),
      allowEditing: false
    },
    {
      field: 'groupSupplierName',
      headerText: i18n.t('集团供应商名称'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建日期'),
      searchOptions: {
        elementType: 'date-range',
        operator: 'between',
        serializeValue: (e) => {
          //自定义搜索值，规则
          return e.map((x, i) => {
            if (i === 1) {
              return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
            }
            return Number(new Date(x.toString()))
          })
        }
      },
      ignore: true,
      allowEditing: false
    },
    {
      field: 'modifyDate',
      headerText: i18n.t('更新日期'),
      searchOptions: {
        elementType: 'date-range',
        operator: 'between',
        serializeValue: (e) => {
          //自定义搜索值，规则
          return e.map((x, i) => {
            if (i === 1) {
              return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
            }
            return Number(new Date(x.toString()))
          })
        }
      },
      ignore: true,
      allowEditing: false
    }
  ]
  return config
}

export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}
