import { i18n } from '@/main.js'
import dayjs from 'dayjs'

// 状态列表
export const statusList = [
  { text: i18n.t('新建'), value: 1, cssClass: '' },
  { text: i18n.t('生效'), value: 2, cssClass: '' },
  { text: i18n.t('失效'), value: 3, cssClass: '' }
]
// 是否列表
export const isNotList = [
  { text: i18n.t('否'), value: false, cssClass: '' },
  { text: i18n.t('是'), value: true, cssClass: '' }
]

// 列表
const columnData = [
  {
    field: 'companyCode',
    headerText: i18n.t('所属公司'),
    formatter: (column, row) => {
      return row.companyCode ? row.companyCode + '-' + row.companyName : ''
    }
  },
  {
    field: 'formCode',
    headerText: i18n.t('报表编码'),
    cssClass: 'field-content'
  },
  {
    field: 'formName',
    headerText: i18n.t('报表名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList
    }
  },
  {
    field: 'version',
    headerText: i18n.t('版本号')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'remark',
    headerText: i18n.t('发布时间'),
    formatter: (column, row) => {
      return row.remark ? dayjs(Number(row.remark)).format('YYYY-MM-DD HH:mm:ss') : ''
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间')
  }
]
export const pageConfig = [
  {
    title: i18n.t('多级供应商'),
    gridId: '2c99ede1-08a2-4a08-a72a-66e5dd3e85c2',
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    isUseCustomEditor: true,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'add', title: i18n.t('新增') },
          { id: 'effective', title: i18n.t('生效') },
          { id: 'ineffective', title: i18n.t('失效') }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      lineIndex: true,
      columnData,
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true, // 使用自定义勾选列
      showSelected: false,
      asyncConfig: {
        url: '/supplier/tenant/multiLevelSupplier/header/pageQuery'
      }
    }
  }
]

// 明细
const detailColumnData = [
  {
    field: 'lineNumber',
    headerText: i18n.t('行号')
  },
  {
    field: 'group',
    headerText: i18n.t('组别')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类'),
    formatter: (column, row) => {
      return row.categoryCode ? row.categoryCode + '-' + row.categoryName : ''
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    formatter: (column, row) => {
      return row.supplierCode ? row.supplierCode + '-' + row.supplierName : ''
    }
  },
  {
    field: 'businessContactPerson',
    headerText: i18n.t('业务联系人')
  },
  {
    field: 'contact',
    headerText: i18n.t('联系方式')
  },
  {
    field: 'address1',
    headerText: i18n.t('地址类别1')
  },
  {
    field: 'address2',
    headerText: i18n.t('地址类别2')
  },
  {
    field: 'address3',
    headerText: i18n.t('地址类别3')
  },
  {
    field: 'materialName',
    headerText: i18n.t('原材料名称')
  },
  {
    field: 'secondLevelSupplierName',
    headerText: i18n.t('二级供应商公司编码')
  },
  {
    field: 'secondLevelCompanyName',
    headerText: i18n.t('二级供应商公司名称或品牌名称')
  },
  {
    field: 'leadTime',
    headerText: i18n.t('原材料L/T（天）')
  },
  {
    field: 'exclusive',
    headerText: i18n.t('外发工序/原材料是否独家供应'),
    valueConverter: {
      type: 'map',
      map: isNotList
    }
  },
  {
    field: 'abroad',
    headerText: i18n.t('地址是否国外'),
    valueConverter: {
      type: 'map',
      map: isNotList
    }
  },
  {
    field: 'country',
    headerText: i18n.t('国家/地区')
  },
  {
    field: 'province',
    headerText: i18n.t('省/直辖市/特别行政区')
  },
  {
    field: 'city',
    headerText: i18n.t('市/县')
  },
  {
    field: 'district',
    headerText: i18n.t('区县')
  },
  {
    field: 'street',
    headerText: i18n.t('街道')
  },
  {
    field: 'detailAddress',
    headerText: i18n.t('详细地址')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const detailPageConfig = (that) => {
  const rightToolBarList = ['Refresh', 'Setting']
  // that.showQuickSearch
  //   ? rightToolBarList.unshift({
  //     id: 'closeQuickSearch',
  //     title: i18n.t('关闭快捷查询')
  //   })
  //   : rightToolBarList.unshift({
  //     id: 'openQuickSearch',
  //     title: i18n.t('打开快捷查询')
  //   })
  return [
    {
      title: i18n.t('多级供应商明细'),
      gridId: '40afeb28-d861-474d-838d-e977a1b64d9f',
      isUseCustomSearch: that.showQuickSearch,
      isCustomSearchRules: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'import', title: i18n.t('导入') },
            { id: 'export', title: i18n.t('导出') }
          ],
          rightToolBarList
        ]
      },
      useToolTemplate: false,
      grid: {
        columnData: detailColumnData,
        virtualPageSize: 30,
        enableVirtualization: true,
        showSelected: false,
        dataSource: []
      }
    }
  ]
}
