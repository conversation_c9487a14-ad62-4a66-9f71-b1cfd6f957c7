<template>
  <div class="full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div v-show="!isExpand">
              <span>{{ dataForm.formCode }}</span>
              <span class="sub-title">{{ dataForm.formName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickTopToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm">
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <vxe-select
                v-model="dataForm.companyCode"
                :options="companyList"
                :option-props="{ label: 'text', value: 'companyCode' }"
                :disabled="!isEditable"
                @change="
                  (e) => {
                    const selectedItem = companyList.find((item) => item.companyCode === e.value)
                    dataForm.companyName = selectedItem ? selectedItem.companyName : null
                  }
                "
              />
            </mt-form-item>
            <mt-form-item prop="formCode" :label="$t('报表编码')" label-style="top">
              <vxe-input v-model="dataForm.formCode" disabled />
            </mt-form-item>
            <mt-form-item prop="formName" :label="$t('报表名称')" label-style="top">
              <vxe-input v-model="dataForm.formName" :disabled="!isEditable" />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <vxe-select
                v-model="dataForm.status"
                :options="statusList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="version" :label="$t('版本号')" label-style="top">
              <vxe-input v-model="dataForm.version" disabled />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <vxe-input v-model="dataForm.createUserName" disabled />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <vxe-input v-model="dataForm.createTime" disabled />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('发布时间')" label-style="top">
              <vxe-input v-model="dataForm.remark" disabled />
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('更新人')" label-style="top">
              <vxe-input v-model="dataForm.updateUserName" disabled />
            </mt-form-item>
            <mt-form-item prop="updateTime" :label="$t('更新时间')" label-style="top">
              <vxe-input v-model="dataForm.updateTime" disabled />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        :hidden-tabs="true"
        @handleCustomReset="handleCustomReset"
        @handleClickToolBar="handleClickToolBar"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="group" :label="$t('组别')" label-style="top">
                <mt-input
                  v-model="searchFormModel.group"
                  :show-clear-button="true"
                  :placeholder="$t('请输入组别')"
                />
              </mt-form-item>
              <mt-form-item prop="categoryCode" :label="$t('品类')">
                <RemoteAutocomplete
                  v-model="searchFormModel.categoryCode"
                  url="/masterDataManagement/tenant/category/paged-query"
                  :fields="{ text: 'categoryName', value: 'categoryCode' }"
                  :search-fields="['categoryName', 'categoryCode']"
                  :placeholder="$t('请选择品类')"
                />
              </mt-form-item>
              <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
                <RemoteAutocomplete
                  v-model="searchFormModel.supplierCode"
                  url="/masterDataManagement/tenant/supplier/paged-query"
                  multiple
                  :fields="{ text: 'supplierName', value: 'supplierCode' }"
                  :search-fields="['supplierCode', 'supplierName']"
                  :placeholder="$t('请选择供应商')"
                />
              </mt-form-item>
              <mt-form-item prop="businessContact" :label="$t('业务联系人')" label-style="top">
                <mt-input
                  v-model="searchFormModel.businessContact"
                  :show-clear-button="true"
                  :placeholder="$t('请输入业务联系人')"
                />
              </mt-form-item>
              <mt-form-item prop="raowMaterialName" :label="$t('原材料名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.raowMaterialName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入原材料名称')"
                />
              </mt-form-item>
              <mt-form-item
                prop="secondLevelSupplierCode"
                :label="$t('二级供应商编码')"
                label-style="top"
              >
                <mt-input
                  v-model="searchFormModel.secondLevelSupplierCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入二级供应商编码')"
                />
              </mt-form-item>
              <mt-form-item
                prop="secondLevelSupplierName"
                :label="$t('二级供应商名称/品牌名称')"
                label-style="top"
              >
                <mt-input
                  v-model="searchFormModel.secondLevelSupplierName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入二级供应商名称/品牌名称')"
                />
              </mt-form-item>
              <mt-form-item prop="rawMaterialLT" :label="$t('原材料L/T（天）')" label-style="top">
                <mt-input
                  v-model="searchFormModel.rawMaterialLT"
                  :show-clear-button="true"
                  :placeholder="$t('请输入原材料L/T（天）')"
                />
              </mt-form-item>
              <mt-form-item
                prop="isExclusiveSupply"
                :label="$t('外发工序/原材料是否独家供应')"
                label-style="top"
              >
                <mt-select
                  v-model="searchFormModel.isExclusiveSupply"
                  :data-source="isNotList"
                  :fields="{ text: 'text', value: 'value' }"
                  :show-clear-button="true"
                  :placeholder="$t('请选择外发工序/原材料是否独家供应')"
                />
              </mt-form-item>
              <mt-form-item prop="addressIsForeign" :label="$t('地址是否国外')" label-style="top">
                <mt-select
                  v-model="searchFormModel.addressIsForeign"
                  :data-source="isNotList"
                  :fields="{ text: 'text', value: 'value' }"
                  :show-clear-button="true"
                  :placeholder="$t('请选择地址是否国外')"
                />
              </mt-form-item>
              <mt-form-item prop="searchFormModel" :label="$t('国家/地区')">
                <RemoteAutocomplete
                  v-model="searchFormModel.searchFormModel"
                  :fields="{ text: 'shortName', value: 'countryCode' }"
                  url="/masterDataManagement/tenant/country/paged-query"
                  :rule-params="[{ field: 'statusId', operator: 'equal', type: 'int', value: 1 }]"
                  :load-data="false"
                  :search-fields="[
                    'id',
                    'alpha2Code',
                    'countryCode',
                    'englishShortName',
                    'numericCode',
                    'shortName',
                    'statusDescription'
                  ]"
                  :title-switch="false"
                  select-type="country"
                  :placeholder="$t('请选择国家/地区')"
                />
              </mt-form-item>
              <mt-form-item
                prop="provinceCode"
                :label="$t('省/直辖市/特别行政区')"
                label-style="top"
              >
                <mt-select
                  v-model="searchFormModel.provinceCode"
                  :data-source="provinceList"
                  :fields="{ text: 'text', value: 'areaCode' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :placeholder="$t('请选择省/直辖市/特别行政区')"
                />
              </mt-form-item>
              <mt-form-item prop="city" :label="$t('市/县')" label-style="top">
                <mt-input
                  v-model="searchFormModel.city"
                  :show-clear-button="true"
                  :placeholder="$t('请输入市/县')"
                />
              </mt-form-item>
              <mt-form-item prop="county" :label="$t('区县')" label-style="top">
                <mt-input
                  v-model="searchFormModel.county"
                  :show-clear-button="true"
                  :placeholder="$t('请输入区县')"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      :upload-params="uploadParams"
      :is-show-tips="false"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import { Input as VxeInput, Button as VxeButton, Select as VxeSelect } from 'vxe-table'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog.vue'
import { statusList, isNotList, detailPageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: { VxeInput, VxeButton, VxeSelect, RemoteAutocomplete, UploadExcelDialog },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      type: 'detail',
      dataForm: {},
      companyList: [],
      statusList,
      isNotList,
      provinceList: [],
      activeTabIndex: 0,
      isExpand: true,
      searchFormModel: {},
      pageConfig: detailPageConfig(this),
      showQuickSearch: false,
      downTemplateName: '',
      downTemplateParams: {},
      requestUrls: {},
      uploadParams: {}
    }
  },
  computed: {
    isEditable() {
      return this.dataForm.status !== 2
    },
    detailToolbar() {
      return [
        {
          code: 'save',
          name: this.$t('保存'),
          status: ''
        },
        { code: 'back', name: this.$t('返回'), status: 'primary' }
      ]
    }
  },
  mounted() {
    this.getCompanyList()
    this.getProvinceList()
    this.getOrderInfo()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.getCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.companyCode + '-' + item.companyName
        })
        this.companyList = res.data || []
      }
    },
    // 获取省份下拉列表
    async getProvinceList() {
      const res = await this.$API.supplierInvitationAdd.selectByParentCode({
        parentCode: ''
      })
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.areaCode + '-' + item.areaName
        })
        this.provinceList = res.data || []
      }
    },
    // 获取单据信息
    async getOrderInfo() {
      const params = {
        id: this.$route.query.id
      }
      const res = await this.$API.supplierRelationshipReport.getMultiSupDetail(params)
      if (res.code === 200) {
        const { header, itemList } = res.data
        this.dataForm = header
        this.$set(this.pageConfig[0].grid, 'dataSource', itemList)
      }
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    },
    // 点击头部工具栏按钮
    handleClickTopToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        default:
          break
      }
    },
    // 重置
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 点击Table工具栏按钮
    handleClickToolBar(e) {
      const { toolbar } = e
      switch (toolbar.id) {
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        case 'openQuickSearch':
          this.showQuickSearch = true
          this.pageConfig = detailPageConfig(this)
          break
        case 'closeQuickSearch':
          this.showQuickSearch = false
          this.searchFormModel = {}
          this.pageConfig = detailPageConfig(this)
          break
        default:
          break
      }
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 保存
    async handleSave() {
      const { companyCode, companyName, formName } = this.dataForm
      const params = {
        id: this.$route.query.id,
        companyCode,
        companyName,
        formName
      }
      const res = await this.$API.supplierRelationshipReport.saveMultiSupDetail(params)
      if (res.code === 200) {
        this.$toast({ type: 'success', content: this.$t('保存成功') })
        this.getOrderInfo()
      }
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'supplierRelationshipReport',
        uploadUrl: 'importMultiSup'
      }
      this.uploadParams = {
        id: this.$route.query.id
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const params = {
        id: this.$route.query.id
      }
      const res = await this.$API.supplierRelationshipReport.exportMultiSup(params)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        download({ fileName: getHeadersFileName(res), blob: res.data })
      }
    },
    // 展示/隐藏上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = []
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.$toast({ type: 'success', content: this.$t('导入成功') })
      this.showUploadExcel(false)
      this.getOrderInfo()
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
  padding: 8px;
  background: #fff;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
