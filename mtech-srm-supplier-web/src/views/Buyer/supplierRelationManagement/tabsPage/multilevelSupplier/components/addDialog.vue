<template>
  <mt-dialog
    ref="dialogRef"
    :buttons="buttons"
    :header="modalData.title"
    width="550"
    height="350"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('所属公司')"
          label-style="top"
          prop="companyCode"
        >
          <RemoteAutocomplete
            v-model="formInfo.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO'
            }"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :placeholder="$t('请选择所属公司')"
            @change="
              (e) => {
                const { itemData } = e
                formInfo.companyName = itemData ? itemData.orgName : null
              }
            "
          />
        </mt-form-item>
        <mt-form-item prop="formName" :label="$t('报表名称')" label-style="top">
          <mt-input
            v-model="formInfo.formName"
            :show-clear-button="true"
            :placeholder="$t('请输入报表名称')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formInfo: {},
      rules: {
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        formName: [{ required: true, message: this.$t('请输入报表名称'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  computed: {},
  async created() {},
  mounted() {
    this.$refs.dialogRef?.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.formInfo.validate(async (valid) => {
        if (valid) {
          const res = await this.$API.supplierRelationshipReport.addMultilevelSupplier(
            this.formInfo
          )
          if (res.code === 200) {
            this.$toast({ type: 'success', content: this.$t('操作成功') })
            this.$emit('confirm-function', res.data)
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
