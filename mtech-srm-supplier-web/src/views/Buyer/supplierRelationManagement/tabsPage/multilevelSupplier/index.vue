<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCode" :label="$t('公司')">
              <RemoteAutocomplete
                v-model="searchFormModel.companyCode"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择公司')"
                select-type="administrativeCompany"
              />
            </mt-form-item>
            <mt-form-item prop="formCode" :label="$t('报表编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.formCode"
                :show-clear-button="true"
                :placeholder="$t('请输入报表编码')"
              />
            </mt-form-item>
            <mt-form-item prop="formName" :label="$t('报表名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.formName"
                :show-clear-button="true"
                :placeholder="$t('请输入报表名称')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')">
              <mt-select
                v-model="searchFormModel.status"
                :data-source="statusList"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                :open-on-focus="true"
                :placeholder="$t('请选择创建日期')"
                @change="(e) => handleDateRangeChange('createDate', e)"
              />
            </mt-form-item>
            <mt-form-item prop="releaseDate" :label="$t('发布日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.releaseDate"
                :open-on-focus="true"
                :placeholder="$t('请选择发布日期')"
                @change="(e) => handleDateRangeChange('releaseDate', e)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>
<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { statusList, pageConfig } from './config'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      statusList,
      pageConfig,
      downTemplateName: '',
      downTemplateParams: {},
      requestUrls: {},
      uploadParams: {}
    }
  },
  created() {},
  methods: {
    // 日期范围选择
    handleDateRangeChange(key, e) {
      const { startDate, endDate } = e
      this.searchFormModel[key + 'Start'] = startDate ? startDate.valueOf() : null
      this.searchFormModel[key + 'End'] = startDate ? endDate.valueOf() + 86399000 : null
    },
    // 重置
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const selectedRecords = gridRef.getCustomSelectedRows()
      if (['effective', 'ineffective'].includes(toolbar.id) && selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (toolbar.id) {
        case 'add':
          this.handleAdd()
          break
        case 'effective':
        case 'ineffective':
          this.handleUpdateStatus(toolbar.id, selectedRecords)
          break
        default:
          break
      }
    },
    // 点击单元格标题
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'formCode') {
        this.$router.push({
          name: 'multilevel-supplier-detail',
          query: {
            id: data.id,
            refreshId: Date.now()
          }
        })
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('新增多级供应商')
        },
        success: (id) => {
          this.$router.push({
            name: 'multilevel-supplier-detail',
            query: {
              id,
              refreshId: Date.now()
            }
          })
        }
      })
    },
    handleUpdateStatus(type, list) {
      if (list.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      for (let i = 0; i < list.length; i++) {
        if (type === 'effective' && list[i].status === 2) {
          this.$toast({ content: this.$t('单据状态为【生效】，不能进行该操作'), type: 'warning' })
          return
        }
        if (type === 'ineffective' && list[i].status === 3) {
          this.$toast({ content: this.$t('单据状态为【失效】，不能进行该操作'), type: 'warning' })
          return
        }
      }
      const mapObj = {
        effective: { text: this.$t('生效'), value: 2 },
        ineffective: { text: this.$t('失效'), value: 3 }
      }
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t(`${this.$t('确定') + mapObj[type].text + this.$t('选中行')}？`)
        },
        success: async () => {
          const params = {
            id: list[0].id,
            status: mapObj[type].value
          }
          const res = await this.$API.supplierRelationshipReport.updateMultiSupStatus(params)
          if (res.code === 200) {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({ type: 'success', content: this.$t('操作成功') })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
