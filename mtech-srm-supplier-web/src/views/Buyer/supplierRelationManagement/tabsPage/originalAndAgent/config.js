import { i18n } from '@/main.js'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import Vue from 'vue'

// import selectedItemCode from '../coms/selectItemCode'
// import cellChanged from '../coms/cellChanged.vue'

export default {
  components: {
    RemoteAutocomplete
  }
}
// export const payCondition = axios
//   .post('/api/masterDataManagement/tenant/payment-terms/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}', {})
//   .then((res) => {
//     console.log('payConditionpayCondition', res.data.data)

//     return res.data.data || []
//   })
// 供应商状态
export const supplierStatusOptions = [
  { text: '注册', value: 1 },
  { text: '潜在', value: 2 },
  { text: '新合格', value: 3 },
  { text: '临时', value: 4 },
  { text: '合格', value: 10 },
  { text: '预合格', value: 11 },
  { text: '冻结', value: 20 },
  { text: '黑名单', value: 30 },
  { text: '退出', value: 40 }
]

// 供应类型
export const supplierTypeOptions = [
  { text: '通采', value: 'commonPurchaseSupplier' },
  { text: '非采', value: 'noBiddingPurchaseSupplier' },
  { text: '物流商', value: 'logisticsProvider' }
]

// 关系类型
export const relationTypeOptions = [{ text: '代理-原厂', value: 2 }]

// 供应类型
export const statusOptions = [
  { text: '生效', value: 1 },
  { text: '失效', value: 2 }
]

export const deadmaterialCols = () => {
  const config = [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码-SRM'),
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商编码-SAP'),
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      field: 'supplierNameEn',
      headerText: i18n.t('供应商英文名称'),
      allowEditing: false
    },
    {
      field: 'supplierAbbr',
      headerText: i18n.t('供应商简称'),
      allowEditing: false
    },
    {
      field: 'orgName',
      headerText: i18n.t('所属公司'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'supplierTypeCode',
      headerText: i18n.t('供应商类型'),
      ignore: true,
      valueConverter: {
        type: 'map',
        map: {
          commonPurchaseSupplier: i18n.t('通采'),
          noBiddingPurchaseSupplier: i18n.t('非采'),
          logisticsProvider: i18n.t('物流商')
        }
      }
    },
    {
      field: 'supplyCategCode',
      headerText: i18n.t('品类')
    },
    {
      field: 'supplierStatusCode',
      headerText: i18n.t('供应商状态'),
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('注册'),
          2: i18n.t('潜在'),
          3: i18n.t('新合格'),
          4: i18n.t('临时'),
          10: i18n.t('合格'),
          11: i18n.t('预合格'),
          20: i18n.t('冻结'),
          30: i18n.t('黑名单'),
          40: i18n.t('退出')
        }
      }
    },
    {
      field: 'relationTypeDesc',
      headerText: i18n.t('关系类型'),
      allowEditing: false
    },
    {
      field: 'relationSupplierCode',
      headerText: i18n.t('关系供应商编号'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'relationSupplierName',
      headerText: i18n.t('关系供应商名称'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'relationSupplierSocialCreditCode',
      headerText: i18n.t('关系供应商社会信用代码'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'fileInfo',
      headerText: i18n.t('关系证书'),
      ignore: true,
      allowEditing: false,
      template: function () {
        return {
          template: Vue.component('actionOption', {
            template: `<div>
              <p style="margin-top:5px;"><a @click="preview()">{{data.fileInfo.fileName}}</a> <span style="margin-left:10px;cursor: pointer;" @click="upload()">下载</span></p>
            </div>`,
            data() {
              return { data: {}, sceneList: [] }
            },
            methods: {
              preview() {
                let params = {
                  id: this.data.fileInfo.id,
                  useType: 1
                }
                this.$API.SupplierPunishment.filepreview(params).then((res) => {
                  window.open(res.data)
                })
              },
              upload() {
                this.$API.SupplierPunishment.fileDownload(this.data.fileInfo.id).then((res) => {
                  let link = document.createElement('a')
                  link.style.display = 'none'
                  let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                  let url = window.URL.createObjectURL(blob)
                  link.href = url
                  link.setAttribute('download', `${this.data.fileInfo.fileName}`) // 给下载后的文件命名
                  link.click() // 点击下载
                  window.URL.revokeObjectURL(url)
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'status',
      headerText: i18n.t('关系状态'),
      ignore: true,
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('生效'),
          2: i18n.t('失效')
        }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      ignore: true,
      allowEditing: false
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建日期'),
      searchOptions: {
        elementType: 'date-range',
        operator: 'between',
        serializeValue: (e) => {
          //自定义搜索值，规则
          return e.map((x, i) => {
            if (i === 1) {
              return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
            }
            return Number(new Date(x.toString()))
          })
        }
      },
      ignore: true,
      allowEditing: false
    },
    {
      field: 'modifyDate',
      headerText: i18n.t('更新日期'),
      searchOptions: {
        elementType: 'date-range',
        operator: 'between',
        serializeValue: (e) => {
          //自定义搜索值，规则
          return e.map((x, i) => {
            if (i === 1) {
              return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
            }
            return Number(new Date(x.toString()))
          })
        }
      },
      ignore: true,
      allowEditing: false
    }
  ]
  return config
}

export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}
