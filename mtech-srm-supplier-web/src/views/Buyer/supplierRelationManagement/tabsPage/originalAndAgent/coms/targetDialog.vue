// 认证需求
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('所属公司')" label-style="top" prop="orgCode">
          <RemoteAutocomplete
            v-model="formInfo.orgCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :placeholder="$t('请选择所属公司')"
            popup-width="280"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO'
            }"
            :fields="{ text: 'orgName', value: 'orgCode' }"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('供应商名称')"
          label-style="top"
          prop="supplierCode"
        >
          <RemoteAutocomplete
            v-model="formInfo.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierCode', 'supplierName']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('品类')"
          label-style="top"
          prop="supplyCategCode"
        >
          <RemoteAutocomplete
            v-model="formInfo.supplyCategCode"
            url="/masterDataManagement/tenant/category/paged-query"
            :placeholder="$t('请选择品类')"
            popup-width="280"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
            @change="changeCateg"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('关系类型')"
          label-style="top"
          prop="relationType"
        >
          <mt-select
            v-model="formInfo.relationType"
            css-class="rule-element"
            :data-source="relationOptions"
            :fields="{ text: 'text', value: 'value' }"
            :allow-filtering="true"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="relationSupplierName" :label="$t('关系供应商名称')" label-style="top">
          <mt-input v-model="formInfo.relationSupplierName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="relationSupplierCode" :label="$t('关系供应商编号')" label-style="top">
          <mt-input v-model="formInfo.relationSupplierCode"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="relationSupplierSocialCreditCode"
          :label="$t('关系供应商社会信用代码号')"
          label-style="top"
        >
          <mt-input v-model="formInfo.relationSupplierSocialCreditCode"></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item radio-item"
          :label="$t('关系证书')"
          label-style="top"
          prop="template"
        >
          <mt-common-uploader
            :is-single-file="true"
            :accept="acceptType"
            :save-url="saveUrl"
            :download-url="downloadUrl"
            type="line"
            v-model="formInfo.fileInfo"
          ></mt-common-uploader>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            maxlength="20"
            :placeholder="$t('请输入，限制20字符以内')"
            float-label-type="Never"
            width="865"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
// import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import commonData from '@/utils/constant'

export default {
  components: { RemoteAutocomplete },
  data() {
    return {
      demandDesc: '',
      imgUrl: '',

      isCateShow: true,
      isEmpShow: true,
      isSupShow: true,
      employeeId: null,
      // orgList: [],
      deptList: [],
      categoryList: [],
      supplierList: [],
      userList: [],
      rules: {
        orgCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        supplyCategCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        relationType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        relationSupplierName: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        relationSupplierCode: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        relationSupplierSocialCreditCode: [
          { required: true, message: this.$t('请输入'), trigger: 'blur' }
        ]
      },
      formInfo: {
        orgCode: '',
        supplierCode: []
      },
      relationOptions: [{ text: '代理-原厂', value: 2 }],
      saveUrl: commonData.publicFileUrl,
      downloadUrl: commonData.downloadUrl,
      acceptType: ['.xls', '.xlsx']
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    // info: {
    //   handler(row) {
    //     this.init(row)
    //   },
    //   immediate: true
    // }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    info() {
      return this.modalData.info
    },
    disable() {
      return this.modalData.disable
    },
    buttons() {
      return [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  async created() {},
  mounted() {
    if (this.info) {
      this.init(this.info)
    }
    this.show()
  },
  methods: {
    changeCateg(e) {
      const { itemData } = e
      this.formInfo.supplyCategName = itemData.categoryName
    },
    init(row) {
      const deepInfo = cloneDeep(row)
      this.formInfo = deepInfo[0]
      this.formInfo.fileInfo = [deepInfo[0].fileInfo]
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$loading()
          const params = {
            ...this.formInfo,
            fileInfo: this.formInfo.fileInfo ? this.formInfo.fileInfo[0] : []
          }
          const curStatusUrl = this.type === 'add' ? 'addOriginalAgent' : 'updateOriginalAgent'
          this.$API.supplierRelationshipReport[curStatusUrl](params)
            .then((res) => {
              if (res.code == 200) {
                this.$hloading()
                console.log('res', res)
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              } else {
                this.$hloading()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-rich-text-editor {
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    height: 170px !important;
    padding: 0px 7px;
    em {
      font: revert;
    }
    ol,
    ul {
      list-style: revert;
    }
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .mt-form-item {
    width: 390px;
  }
  .full-width {
    width: 100%;
  }
}
/deep/ .full-width {
  width: 100% !important;
}
</style>
<style lang="scss">
.d72562-c40d-4933-9a24-98c3298365ac {
  li {
    display: flex;
    align-items: center;
    & > div {
      flex-shrink: 0;
    }
    & > div:first-child {
      margin-left: 20px;
    }
  }
}
</style>
