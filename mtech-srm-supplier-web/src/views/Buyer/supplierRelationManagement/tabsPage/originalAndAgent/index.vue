<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item
              class="form-item"
              :label="$t('供应商编码-SRM')"
              label-style="top"
              prop="supplierInnerCode"
            >
              <mt-input v-model="searchFormModel.supplierInnerCode"></mt-input>
            </mt-form-item>

            <mt-form-item prop="supplierCode" :label="$t('供应商编码-SAP')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :placeholder="$t('请选择供应商')"
                popup-width="330"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierCode', 'supplierName']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input v-model="searchFormModel.supplierName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierNameEn" :label="$t('供应商英文名称')" label-style="top">
              <mt-input v-model="searchFormModel.supplierNameEn"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierAbbr" :label="$t('供应商简称')" label-style="top">
              <mt-input v-model="searchFormModel.supplierAbbr"></mt-input>
            </mt-form-item>
            <mt-form-item prop="orgCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.orgCode"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :placeholder="$t('请选择所属公司')"
                popup-width="280"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO'
                }"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="societyCreditCode" :label="$t('社会信用代码号')" label-style="top">
              <mt-input v-model="searchFormModel.societyCreditCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.categCode"
                url="/masterDataManagement/tenant/category/paged-query"
                multiple
                :placeholder="$t('请选择品类')"
                popup-width="280"
                :fields="{ text: 'categoryName', value: 'categoryCode' }"
                :search-fields="['categoryName', 'categoryCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierStatusCode" :label="$t('供应商状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.supplierStatusCode"
                css-class="rule-element"
                :data-source="supplierStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
                :allow-filtering="true"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="supplierTypeCode" :label="$t('供应商类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.supplierTypeCode"
                css-class="rule-element"
                :data-source="supplierTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                :allow-filtering="true"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="relationType" :label="$t('关系类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.relationType"
                css-class="rule-element"
                :data-source="relationTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                :allow-filtering="true"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              prop="relationSupplierCode"
              :label="$t('关系供应商编码')"
              label-style="top"
            >
              <mt-input v-model="searchFormModel.relationSupplierCode"></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="relationSupplierName"
              :label="$t('关系供应商名称')"
              label-style="top"
            >
              <mt-input v-model="searchFormModel.relationSupplierName"></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="relationSupplierSocialCreditCode"
              :label="$t('关系供应商社会信用代码')"
              label-style="top"
            >
              <mt-input v-model="searchFormModel.relationSupplierSocialCreditCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('关系状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                css-class="rule-element"
                :data-source="statusOptions"
                :fields="{ text: 'text', value: 'value' }"
                :allow-filtering="true"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input v-model="searchFormModel.createUserName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                :placeholder="$t('请选择创建时间')"
              ></mt-date-range-picker>
            </mt-form-item>
            <mt-form-item prop="expireDate" :label="$t('失效日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.expireDate"
                :placeholder="$t('请选择创建时间')"
              ></mt-date-range-picker>
            </mt-form-item>
            <mt-form-item prop="modifyDate" :label="$t('更新时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.modifyDate"
                :placeholder="$t('请选择最后更新时间')"
              ></mt-date-range-picker>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>
<script>
import {
  deadmaterialCols,
  supplierStatusOptions,
  supplierTypeOptions,
  relationTypeOptions,
  statusOptions
} from './config'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getHeadersFileName, download } from '@/utils/utils.js'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      statusOptions,
      supplierTypeOptions,
      supplierStatusOptions,
      relationTypeOptions,
      searchFormModel: {},
      pageConfig: [
        {
          gridId: '6931300e-ecf2-4267-83eb-c5c3a8fa73dc',
          buttonQuantity: 7,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'maintainRelationship',
                  title: this.$t('供应商关系维护')
                },
                { id: 'ImportCate', title: this.$t('导入') },
                { id: 'ExportCate', title: this.$t('导出') },
                { id: 'Edit', title: this.$t('编辑') },
                {
                  id: 'Delete',
                  title: this.$t('删除')
                },
                {
                  id: 'Uneffectiveness',
                  title: this.$t('失效')
                },
                {
                  id: 'effectiveness',
                  title: this.$t('生效')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: deadmaterialCols(this),
            frozenColumns: 1,
            asyncConfig: {
              url: '/supplier/tenant/buyer/original/factory/agent/list'
            }
          }
        }
      ],
      payCondition: []
    }
  },
  created() {},
  methods: {
    // 重置
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (records.length <= 0 && (e.toolbar.id === 'Delete' || e.toolbar.id === 'Edit')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'maintainRelationship') {
        this.handleAdd()
        return
      } else if (e.toolbar.id == 'ImportCate') {
        this.supplierImportFn()
      } else if (e.toolbar.id == 'ExportCate') {
        this.supplierDownload()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(records)
      } else if (e.toolbar.id == 'Edit') {
        this.handleEdit(records)
      } else if (e.toolbar.id == 'Uneffectiveness' || e.toolbar.id == 'effectiveness') {
        if (records.length === 0)
          return this.$toast({
            content: '请至少选择一条数据操作',
            type: 'warning'
          })
        const _statusList = records.map((el) => Number(el.status))
        console.log('e.toolbar.ide.toolbar.ide.toolbar.id', e.toolbar.id, _statusList)
        if (_statusList.some((item) => item == (e.toolbar.id === 'Uneffectiveness' ? 2 : 1))) {
          console.log('UneffectivenessUneffectiveness')
          const contens =
            '所选单据中含有已' +
            (e.toolbar.id === 'Uneffectiveness' ? '失效' : '生效') +
            '状态的单据，无法' +
            (e.toolbar.id === 'Uneffectiveness' ? '生效' : '失效')
          this.$toast({
            content: contens,
            type: 'warning'
          })
          return
        }
        this.effective(records)
      }
    },
    // 生效/ 失效
    async effective(records) {
      const res = await this.$API.supplierRelationshipReport.operateOriginal({
        factoryAgentRequestList: records
      })
      if (res.code === 200) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // 供应商关系维护
    handleAdd() {
      this.$dialog({
        modal: () => import('./coms/targetDialog.vue'),
        data: {
          title: this.$t('供应商关系维护'),
          type: 'add'
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 编辑
    handleEdit(row) {
      if (row.length > 1)
        return this.$toast({ content: this.$t('一次只能编辑一条数据'), type: 'warning' })
      this.$dialog({
        modal: () => import('./coms/targetDialog.vue'),
        data: {
          title: this.$t('供应商关系维护-编辑'),
          type: 'edit',
          info: row.map((e) => e)
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //Excel导入
    supplierImportFn() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'multipartFile',
          importApi: this.$API.supplierRelationshipReport.originalImport,
          downloadTemplateApi: this.$API.supplierRelationshipReport.originalExportTmp
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //导出
    supplierDownload() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.supplierRelationshipReport.originalExportQuery(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 删除
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          const params = ids.map((e) => e.id)
          this.$API.supplierRelationshipReport.deleteOriginal(params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })

            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // handleClickCellTitle(e) {
    //   if (e && e.field == 'code') {
    //     let { data } = e
    //     sessionStorage.setItem(
    //       'deadMaterialsListInfo',
    //       JSON.stringify({
    //         id: data.id,
    //         // approvalNo: data.approvalNo,
    //         // companyCode: data.companyCode,
    //         // companyName: data.companyName
    //         ...data
    //       })
    //     )
    //     this.$router.push({
    //       path: '/supplier/pur/idle-material-claimsDetail',
    //       query: {
    //         id: data.id,
    //         timeStamp: new Date().getTime()
    //       }
    //     })
    //   } else {
    //     this.$router.push({
    //       name: 'idle-material-claimsDetail'
    //     })
    //   }
    // },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        const validateMap = {
          internationalTradeTermsCode: {
            value: data.internationalTradeTermsCode,
            msg: this.$t('请选择国际贸易条件')
          },
          internationalTermsTrade: {
            value: data.internationalTermsTrade,
            msg: this.$t('请选择加点国际贸易条件')
          },
          payConditionName: {
            value: data.payConditionName,
            msg: this.$t('请选择结算方式')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType, data } = args
      if (requestType == 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    save(rowData) {
      const params = { ...rowData }
      this.$API.supplierRelationshipReport.updatePingcaiSuplier(params).then((res) => {
        if (res.code === 200) {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
