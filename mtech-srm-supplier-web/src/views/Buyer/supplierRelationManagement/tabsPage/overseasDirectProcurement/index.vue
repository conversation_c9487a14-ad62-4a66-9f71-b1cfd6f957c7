<!-- 海外直采供应商 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item :label="$t('物料编码')" prop="materialCode">
            <mt-input
              v-model="materialCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
              @change="(e) => onChange(e, 'materialCode')"
            />
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂编码')">
            <RemoteAutocomplete
              v-model="searchFormModel.siteCodeList"
              :url="$API.masterData.getSiteFuzzyUrl"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              params-key="fuzzyParam"
              records-position="data"
            />
          </mt-form-item>
          <mt-form-item :label="$t('供应商编码')" prop="supplierCode">
            <mt-input
              v-model="supplierCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
              @change="(e) => onChange(e, 'supplierCode')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getHeadersFileName, download } from '@/utils/utils.js'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: { RemoteAutocomplete },
  data() {
    return {
      searchFormModel: {},
      materialCode: null,
      supplierCode: null,
      pageConfig: [
        {
          gridId: '036a0a81-d3c6-4545-89ed-3325f4bdec20',
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: {
            tools: [[{ id: 'export', title: this.$t('导出') }], ['Refresh', 'Setting']]
          },
          useToolTemplate: false,
          grid: {
            columnData: columnData(),
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/distribution/supplier/query',
              recordsPosition: 'data.distributionSupplierPage.records'
            }
          }
        }
      ]
    }
  },
  methods: {
    onChange(e, field) {
      if (e) {
        this.searchFormModel[`${field}List`] = this[field].split(' ')
      } else {
        this.searchFormModel[`${field}List`] = null
        this[field] = null
      }
    },
    // 重置
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.materialCode = null
      this.supplierCode = null
    },
    handleClickToolBar(e) {
      const { toolbar } = e
      if (toolbar.id === 'export') {
        this.handleExport()
      }
    },
    handleExport() {
      let params = {
        page: { current: 1, size: 99999999 },
        ...this.searchFormModel
      }
      this.$API.supplierRelationshipReport.exportOverseasApi(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
