import { i18n } from '@/main.js'
import Vue from 'vue'

export const columnData = () => {
  const column = [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'materialCode',
      headerText: i18n.t('物料编码')
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码')
    },
    {
      field: 'logisticsCycleDays',
      headerText: i18n.t('物流周期天数')
    },
    {
      field: 'mode',
      headerText: i18n.t('模式'),
      template: function () {
        return {
          template: Vue.component('mode', {
            template: `<div>
              {{data.mode === '1' ? data.mode + '-集散' : data.mode + '-非集散' }}
            </div>`,
            data() {
              return { data: {} }
            }
          })
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注')
    },
    {
      field: 'delFlag',
      headerText: i18n.t('是否删除'),
      template: function () {
        return {
          template: Vue.component('delFlag', {
            template: `<div>
              {{ data.delFlag ?  $t('是') : $t('否') }}
            </div>`,
            data() {
              return { data: {} }
            }
          })
        }
      }
    }
  ]
  return column
}
