<template>
  <div class="policy-setting">
    <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig">
      <!-- 准入策略 -->
      <access-process slot="slot-0" index="0"></access-process>
      <!-- 评审策略 -->
      <review-homepage slot="slot-1" index="1"></review-homepage>
      <!-- 认证策略 -->
      <authent-strategy slot="slot-2" index="2"></authent-strategy>
      <!-- 绩效策略 -->
      <performance-strategy slot="slot-3" index="3"></performance-strategy>
      <!-- 分级策略 -->
      <grading-strategy slot="slot-4" index="4"></grading-strategy>
    </mt-template-page>
  </div>
</template>

<script>
import accessProcess from './components/AccessProcess/accessProcess'
export default {
  // 策略设置 - 绩效策略
  name: 'PolicySetting',
  components: {
    // 准入策略
    accessProcess,
    // 评审策略
    reviewHomepage: () =>
      import(
        /* webpackChunkName: "AuthentStrategy" */ './components/reviewHomepage/reviewHomepage.vue'
      ),
    // 认证策略
    authentStrategy: () =>
      import(
        /* webpackChunkName: "AuthentStrategy" */ './components/AuthentStrategy/authentStrategy'
      ),
    //绩效策略
    performanceStrategy: () =>
      import(
        /* webpackChunkName: "performanceStrategy" */ './components/performanceStrategy/performanceStrategy'
      ),
    // 认证策略
    gradingStrategy: () =>
      import(
        /* webpackChunkName: "gradingStrategy" */ './components/GradingStrategy/gradingStrategy'
      )
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: '7e52ff98-2a6e-4c8c-aad3-90374cd624f2',
          title: this.$t('准入策略')
        },
        {
          gridId: '7f50e511-4324-44df-8607-c6e5ff15d62d',
          title: this.$t('评审策略')
        },
        {
          gridId: 'ffac50f5-9cd2-42f0-af9b-ed5126ad859e',
          title: this.$t('认证策略')
        },
        {
          gridId: '73ded771-dcee-4791-9a1e-9b2eae2fd504',
          title: this.$t('绩效策略')
        },
        {
          gridId: '22ea0f30-e3f4-4477-9aec-8f48eafc5786',
          title: this.$t('分级策略')
        }
      ]
    }
  },
  mounted() {
    this.initListData()
  },
  beforeDestroy() {
    sessionStorage.removeItem('accessProcessTypeList')
    sessionStorage.removeItem('accessProcessAuthorList')
  },
  methods: {
    initListData() {
      let typeList = []
      let authorList = []
      this.$API.ModuleConfig.queryDict({
        dictCode: 'stageAccessType'
      }).then((res) => {
        res.data.forEach((e) => {
          typeList.push({
            text: e.itemName,
            value: parseInt(e.itemCode)
          })
        })
        sessionStorage.setItem('accessProcessTypeList', JSON.stringify(typeList))
      })

      this.$API.ModuleConfig.queryDict({
        dictCode: 'stageAuthType'
      }).then((res) => {
        res.data.forEach((e) => {
          authorList.push({
            text: e.itemName,
            value: parseInt(e.itemCode)
          })
        })
        sessionStorage.setItem('accessProcessAuthorList', JSON.stringify(authorList))
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-setting {
  height: 100%;
}
</style>
