<!-- 分级策略 -->
<template>
  <div class="access-content">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig()
    }
  },
  methods: {
    handleClickCellTitle(e) {
      console.log(e)
    },
    handleClickToolBar(e) {
      console.log(e)
      const _this = this
      const { toolbar } = e
      if (toolbar.id === 'Addautomatic') {
        this.$dialog({
          modal: () => import('./components/automaticGrading.vue'),
          data: {
            title: this.$t('新增自动分级')
            // lineInfo: grid
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.access-content {
  height: 100%;

  .form-design {
    position: absolute;
    top: 57px;
    z-index: 99;
    left: 125px;
  }
}
</style>
