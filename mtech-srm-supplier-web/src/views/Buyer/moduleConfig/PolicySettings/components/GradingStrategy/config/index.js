//维度设置Tab
import { i18n } from '@/main.js'
const toolbar = [
  {
    id: 'Addautomatic',
    title: i18n.t('新增'),
    icon: 'icon_solid_Createproject'
  },
  {
    id: 'Deletea',
    title: i18n.t('删除'),
    icon: 'icon_solid_Delete1'
  },
  { id: 'enablea', title: i18n.t('启用'), icon: 'icon_table_enable' },
  { id: 'disablea', title: i18n.t('停用'), icon: 'icon_table_disable' }
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'gradeStrategyCode',
    headerText: i18n.t('分级策略编号'),
    width: '180',
    cellTools: [
      {
        id: 'Edita',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'Deletea',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'gradeStrategyName',
    width: '120',
    headerText: i18n.t('分级策略名称')
  },
  {
    field: 'orgName',
    width: '120',
    headerText: i18n.t('适用组织')
  },
  {
    field: 'status',
    width: '80',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'enablea',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disablea',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]

export const pageConfig = () => [
  {
    gridId: '2d2241d2-f61d-4c5e-8490-2bc8e4dddc53',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url: '/supplier/tenant/buyer/automatic/grade/list'
      }
    }
  }
]
