//策略设置 Tab 表头
import Vue from 'vue'
import { i18n } from '@/main.js'
const toolbar = [
  {
    id: 'addNew',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('新增')
  },
  {
    id: 'editPolicy',
    icon: 'icon_Editor',
    title: i18n.t('编辑')
  },
  {
    id: 'delete',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  },
  {
    id: 'Enable',
    icon: 'icon_table_enable',
    title: i18n.t('启用')
  },
  {
    id: 'disable',
    icon: 'icon_table_disable',
    title: i18n.t('停用')
  },
  {
    id: 'copy',
    icon: 'icon_table_copy',
    title: i18n.t('复制')
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'strategyCode',
    headerText: i18n.t('编码'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        permission: ['O_02_0041']
        // visibleCondition: (data) => {
        //   return data.inviteStatus === 1 || data.inviteStatus === 4 || data.inviteStatus === 5;
        // },
      },
      { id: 'delete', icon: 'icon_Delete', title: i18n.t('删除'), permission: ['O_02_0040'] }
    ]
  },
  {
    field: 'strategyName',
    headerText: i18n.t('策略名称')
  },
  {
    field: 'planName',
    headerText: i18n.t('适用品类'),
    // 创建组件
    template: () => {
      return {
        template: Vue.component('maxScore', {
          template: `
              <div>
                <template v-if="data.categoryType === 2">{{ i18nTitle }}</template>
                <template v-else>
                  <mt-tooltip :content="content" target="#base-txt">
                    <div id="base-txt" >
                      <span id="line" v-for="(item, index) in data.categoryList" v-if="index < 2">{{item.categoryName}}</span>
                      <span v-if="data.categoryList.length>=3">+{{data.categoryList.length - 2}}</span>
                    </div>
                  </mt-tooltip>
                </template> 
              </div>`,
          data: function () {
            return {
              isShowPop: false,
              data: {}
            }
          },
          computed: {
            i18nTitle() {
              return i18n.t('不限品类')
            },
            content() {
              return this.data.categoryList.map((item) => item.categoryName).join('、')
            }
          },
          methods: {
            showPop() {
              this.isShowPop = !this.isShowPop
            }
          }
        })
      }
    }
  },
  {
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    field: 'planName',
    headerText: i18n.t('适用分析模板名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: 80,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('已创建'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: {
        text: 'label',
        value: 'status'
      }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Start',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'Stop',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'remark',
    headerText: i18n.t('规则描述')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    queryType: 'date',
    // allowTextWrap: true,
    width: 80,
    // format: "yyyy-MM-dd  hh:mm:ss"
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  }
  // {
  //   field: "period",
  //   headerText: i18n.t("考评周期"),
  //   valueConverter: {
  //     type: "map",
  //     map: { 0: i18n.t("年度"), 1: i18n.t("半年度"), 2: i18n.t("季度"), 3: i18n.t("月度"), 4: "单日" },
  //   },
  // },
  // {
  //   field: "finishedCount", //totalCount
  //   headerText: i18n.t("完成度"),
  //   template: () => {
  //     return {
  //       template: Vue.component("maxScore", {
  //         template: `
  //             <div>
  //               {{data.finishedCount}}/{{data.totalCount}}
  //             </div>`,
  //         data: function () {
  //           return { data: {} };
  //         },
  //       }),
  //     };
  //   },
  // },
]

export const pageConfig = (url) => [
  {
    gridId: '51118068-ef8f-406c-9c75-157ca63f5925',
    toolbar,

    grid: {
      columnData,
      allowTextWrap: true,
      asyncConfig: {
        url
      }
    }
  }
]

export const logicalSymbol = [
  {
    type: 1,
    name: i18n.t('并且')
  },
  {
    type: 2,
    name: i18n.t('或者')
  }
]

export const type = [
  {
    type: 1,
    name: i18n.t('最近')
  },
  {
    type: 2,
    name: i18n.t('连续')
  },
  {
    type: 3,
    name: i18n.t('单次')
  }
]

export const cycleType = [
  {
    type: 1,
    name: i18n.t('月')
  },
  {
    type: 2,
    name: i18n.t('季度')
  },
  {
    type: 3,
    name: i18n.t('年')
  },
  {
    type: 4,
    name: i18n.t('次考评')
  }
]

export const assessClass = [
  {
    type: 1,
    name: i18n.t('综合')
  },
  {
    type: 2,
    name: i18n.t('质量')
  }
]
export const assessTypes = [
  {
    type: 3,
    name: i18n.t('综合得分')
  },
  {
    type: 4,
    name: i18n.t('质量得分')
  },
  {
    type: 5,
    name: i18n.t('综合灯色')
  },
  {
    type: 6,
    name: i18n.t('质量灯色')
  },
  {
    type: 7,
    name: i18n.t('同品类供应商排名')
  },
  {
    type: 8,
    name: i18n.t('是否观察期')
  }
]
export const assessType = [
  {
    type: 1,
    name: i18n.t('综合')
  },
  {
    type: 2,
    name: i18n.t('质量')
  },
  {
    type: 3,
    name: i18n.t('综合得分')
  },
  {
    type: 4,
    name: i18n.t('质量得分')
  },
  {
    type: 5,
    name: i18n.t('综合灯色')
  },
  {
    type: 6,
    name: i18n.t('质量灯色')
  },
  {
    type: 7,
    name: i18n.t('同品类供应商排名')
  },
  {
    type: 8,
    name: i18n.t('是否观察期')
  }
]

export const lampColorNumber = [
  {
    type: 1,
    name: i18n.t('红灯个数')
  },
  {
    type: 2,
    name: i18n.t('黄灯个数')
  },
  {
    type: 3,
    name: i18n.t('绿灯个数')
  }
]

export const assessTarget = [
  {
    type: 1,
    name: i18n.t('灯色')
  },
  {
    type: 2,
    name: i18n.t('得分')
  },
  {
    type: 3,
    name: i18n.t('排名')
  },
  {
    type: 4,
    name: i18n.t('是否')
  },
  {
    type: 5,
    name: i18n.t('灯色个数')
  }
]

export const lampColor = [
  {
    type: 1,
    name: i18n.t('红灯')
  },
  {
    type: 2,
    name: i18n.t('黄灯')
  },
  {
    type: 3,
    name: i18n.t('绿灯')
  }
]
export const judgeSymbols = [
  {
    type: '5',
    name: i18n.t('空')
  },
  {
    type: '6',
    name: i18n.t('非空')
  },
  {
    type: '7',
    name: i18n.t('包含')
  },
  {
    type: '8',
    name: i18n.t('不包含')
  }
]
export const judgeSymbol = [
  {
    type: '0',
    name: '='
  },
  {
    type: '1',
    name: '≥'
  },
  {
    type: '2',
    name: '>'
  },
  {
    type: '3',
    name: '<'
  },
  {
    type: '4',
    name: '≤'
  }
  // {
  //   type: "5",
  //   name: i18n.t("空"),
  // },
  // {
  //   type: "6",
  //   name: i18n.t("非空"),
  // },
  // {
  //   type: "7",
  //   name: i18n.t("包含"),
  // },
  // {
  //   type: "8",
  //   name: i18n.t("不包含"),
  // },
  // {
  //   type: "9",
  //   name: i18n.t("等于"),
  // },
  // {
  //   type: "10",
  //   name: i18n.t("不等于"),
  // },
]

export const equalType = [
  {
    type: '9',
    name: i18n.t('等于')
  },
  {
    type: '10',
    name: i18n.t('不等于')
  }
]

export const outputType = [
  {
    type: 0,
    name: i18n.t('综合红灯'),
    mold: 1
  },
  {
    type: 1,
    name: i18n.t('综合黄灯'),
    mold: 1
  },
  {
    type: 2,
    name: i18n.t('综合绿灯'),
    mold: 1
  },
  {
    type: 3,
    name: i18n.t('质量红灯'),
    mold: 2
  },
  {
    type: 4,
    name: i18n.t('质量黄灯'),
    mold: 2
  },
  {
    type: 5,
    name: i18n.t('质量绿灯'),
    mold: 2
  },

  {
    type: 6,
    name: i18n.t('移入观察期'),
    mold: 4
  },
  {
    type: 7,
    name: i18n.t('移除观察期'),
    mold: 4
  },
  {
    type: 8,
    name: i18n.t('推荐评审'),
    mold: 3
  },
  {
    type: 9,
    name: i18n.t('推荐整改'),
    mold: 3
  },
  {
    type: 10,
    name: i18n.t('推荐淘汰'),
    mold: 3
  },
  {
    type: 11,
    name: i18n.t('推荐拉黑'),
    mold: 3
  },
  {
    type: 12,
    name: i18n.t('推荐停用'),
    mold: 3
  },
  {
    type: 13,
    name: i18n.t('推荐供货比例降低'),
    mold: 3
  },
  {
    type: 14,
    name: i18n.t('处置单'),
    mold: 5
  },
  {
    type: 15,
    name: i18n.t('警告书'),
    mold: 6
  }
]

export const whether = [
  {
    type: 0,
    name: i18n.t('否')
  },
  {
    type: 1,
    name: i18n.t('是')
  }
]
export const mapNumber = {
  1: i18n.t('一'),
  2: i18n.t('二'),
  3: i18n.t('三'),
  4: i18n.t('四'),
  5: i18n.t('五'),
  6: i18n.t('六'),
  7: i18n.t('七'),
  8: i18n.t('八'),
  9: i18n.t('九'),
  0: i18n.t('零')
}

// 数字转字符串
export const filterValue = (value) => {
  let filterName = ''
  if (!value) return ''
  if (!!value && value.length > 0) {
    filterName = mapNumber[value]
  }
  let strArr = (value + '').split('')
  strArr.forEach((v) => {
    filterName = filterName + '' + mapNumber[Number(v)]
  })

  return filterName
}
