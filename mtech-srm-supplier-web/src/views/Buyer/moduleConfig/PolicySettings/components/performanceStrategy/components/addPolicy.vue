<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>

      <div class="slider-content">
        <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="strategyName" class="form-item positive" :label="$t('策略名称')">
                <mt-input
                  :max-length="50"
                  :disabled="editorIdstatus"
                  v-model="formInfo.strategyName"
                  :placeholder="$t('请输入策略名称')"
                  float-label-type="Never"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="orgIdArr" class="form-item positive" :label="$t('公司')">
                <mt-DropDownTree
                  :disabled="editorIdstatus"
                  v-if="fieldsarr.dataSource.length > 0"
                  v-model="formInfo.orgIdArr"
                  :placeholder="$t('请选择公司')"
                  :popup-height="500"
                  :fields="fieldsarr"
                  @input="selectCompany"
                  id="baseTreeSelect"
                ></mt-DropDownTree>
                <mt-select v-else :placeholder="$t('请选择公司')" :data-source="[]"></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="planId" class="form-item positive" :label="$t('计划')">
                <mt-select
                  :disabled="editorIdstatus"
                  v-model="formInfo.planId"
                  :value="formInfo.planId"
                  :allow-filtering="false"
                  :data-source="planeArrList"
                  :placeholder="$t('请选择计划')"
                  @select="selectPlan"
                  :fields="{ text: 'planName', value: 'id' }"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="categoryArr" class="form-item positive" :label="$t('品类')">
                <mt-DropDownTree
                  v-if="categoryListArrList.dataSource.length"
                  :fields="categoryListArrList"
                  v-model="formInfo.categoryArr"
                  :allow-multi-selection="true"
                  :auto-check="true"
                  :show-check-box="true"
                  id="checkboxTreeSelect"
                  :disabled="editorIdstatus"
                  :placeholder="$t('请选择品类')"
                  @input="selectCategoryas"
                  :key="categoryListArrList.key"
                ></mt-DropDownTree>
                <mt-select v-else :placeholder="$t('请选择品类')" :data-source="[]"></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row>
            <mt-col :span="24">
              <rules
                ref="rules"
                v-for="(item, index) of strategyRules"
                :rule-index="index + 1"
                :key="item.id"
                :item="item"
                :disabled="editorIdstatus"
                @clearGroup="clearGroup"
              ></rules>
              <div
                class="add-rule-group"
                v-if="!editorIdstatus"
                @click="addRuleGroup"
                :disabled="editorIdstatus"
              >
                {{ $t('添加规则') }}
              </div>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>

      <div class="slider-footer mt-flex">
        <span @click="confirm" v-if="isnone">{{ $t('确定') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils.js'
import rules from './rules.vue'
import { filterValue } from '../config/index'
export default {
  data() {
    return {
      isnone: true,
      fieldsarr: {
        dataSource: [], //公司树下拉数组
        value: 'id',
        text: 'name',
        child: 'children',
        code: 'orgCode'
      },
      editorIdstatus: false, //全局禁用 查看专用
      isShow: false,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.submitForm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      // 品类选择数组 适用品类集合
      categoryList: [
        {
          categoryCode: '', // 品类编码
          categoryId: '', // 	品类id
          categoryName: '' // 品类名称
        }
      ],
      // 表单参数
      formInfo: {
        strategyName: '', //策略名称

        orgIdArr: [], //组织绑定
        orgCode: '', // 	公司（组织机构）编码
        orgId: '', // 	公司（组织机构）id
        orgName: '', // 公司（组织机构）名称

        planVersion: '', // 计划版本
        planName: '', // 计划名称
        planCode: '', // 	计划编码
        planId: '',

        // 品类
        categoryArr: [],
        categoryList: [],
        categoryType: '', // 品类类型（1，选择了品类；2，不限）

        priority: '', // 		优先级 0到9

        remark: '' //	策略描述
      },
      // companyArrList: [], //公司下来数组
      planeArrList: [], //计划下拉数组
      // 表单验证
      rules: {
        categoryArr: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        strategyName: [
          {
            required: true,
            message: this.$t('请输入策略名称'),
            trigger: 'blur'
          }
        ],
        planId: [
          {
            required: true,
            message: this.$t('请选择计划'),
            trigger: 'blur'
          }
        ],
        orgIdArr: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ]
      },
      // 规则集合
      strategyRules: [
        {
          name: '', //	策略规则名称
          remark: '', //规则描述
          id: (Math.random() * 100000).toFixed(0),
          strategyRuleInputs: [
            {
              id: (Math.random() * 100000).toFixed(0),
              type: 1 //规则类型（1，最近；2，连续；3，单次）
            }
          ],
          //输出结果 逗号分割，最多2个输出
          outputTypeList: [
            {
              outputType: ''
            }
          ]
        }
      ],
      // 规则组数量 需要添加一个不限 还未定对应id值
      categoryListArrList: {
        dataSource: [], //品类树下拉数组
        value: 'categoryCode',
        text: 'categoryName',
        child: 'childrens',
        key: 1
      },
      selectCategoryList: [] // 分类数组
    }
  },
  props: {
    // 父组件传值 数据集合
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    rules
  },
  // watch:{
  //   'formInfo.orgIdArr':{
  //     handler(){
  //       this.$refs.formInfo.validateField("orgIdArr");
  //     }
  //   },
  // },
  computed: {
    // 父组件传值 数据集合初始化
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    viewlock() {
      return this.modalData.viewlock
    },
    policyData() {
      return this.modalData.policyData
    }
  },
  async created() {
    // 编辑
    if (this.modalData.isEdit) {
      let emitData = this.modalData.policyData //行内传值数据
      this.strategies(emitData.id)
    } else if (this.modalData.viewlock == 1) {
      let emitData = this.modalData.policyData //行内传值数据
      this.strategies(emitData.id)
      this.editorIdstatus = true
      this.isnone = false
    } else {
      this.TreeByAccount()
    }
    // 初始化获取公司列表
    // this.TreeByAccount();

    // // 请求计划
    // let companyOrg = await this.getUserDetail();
    // if (!companyOrg || !companyOrg.id) {
    //   this.$hloading();
    //   this.$toast({ content: "获取当前组织信息失败，请重试", type: "error" });
    //   return;
    // }
  },
  methods: {
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 选取公司递归
    fn(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.formInfo.orgId = ele.id
          this.formInfo.orgCode = ele.orgCode
          this.formInfo.orgName = ele.name
          return
        }
        if (ele.children && ele.children.length > 0) {
          this.fn(ele.children, id)
        }
      })
    },
    // 选择公司
    selectCompany(e) {
      this.fn(this.fieldsarr.dataSource, e[0])
      this.addListQuery() //调用计划接口
      this.formInfo.planId = '' //获取品类传值id
      this.formInfo.planCode = ''
      this.formInfo.planName = ''
      this.formInfo.planVersion = ''
      this.formInfo.categoryList = []
      this.formInfo.categoryArr = []
      this.$refs.formInfo.validateField('orgIdArr')
    },
    // 初始化获取公司列表
    TreeByAccount() {
      this.$API.supplierIndex
        .getOrgTree({
          orgLevelCode: 'ORG02'
        })
        .then((res) => {
          if (res.code == 200) {
            this.$set(this.fieldsarr, 'dataSource', [...res.data])
          }
        })
      // this.$API.analysisOfSetting["TreeByAccount"]({}).then((res) => {
      //   this.$set(this.fieldsarr, "dataSource", [...res.data]);
      // });
    },
    // 获取策略详情列表
    strategies(val) {
      this.$API.policySetting['getDetail']({
        id: val // 传值id
      }).then((res) => {
        this.formInfo = Object.assign(this.formInfo, res.data)
        this.formInfo.orgIdArr = [res.data.orgId]
        this.formInfo.categoryArr = this.formInfo.categoryList.map((item) => {
          return item.categoryCode
        })
        this.addListQuery()
        this.addByPlanuery()
        this.TreeByAccount()
        this.strategyRules = res.data.strategyRules.map((item) => {
          if (this.editorIdstatus) {
            item['editorIdstatus'] = true
          }
          let arrdtas = item.outputResult.split(',')
          item.outputTypeList = []
          arrdtas.forEach((e) => {
            item.outputTypeList.push({
              outputType: Number(e)
            })
          })
          return {
            ...item
          }
        })
      })
    },

    // 获取计划列表
    addListQuery() {
      this.$API.policySetting['addListQuery']({
        companyId: this.formInfo.orgId // 传值级联公司id
      }).then((result) => {
        this.$hloading()
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          this.planeArrList = result.data
        } else {
          this.planeArrList = []
        }
      })
    },
    // 获取计划品类列表
    addByPlanuery() {
      this.$API.policySetting['addByPlanuery']({
        planId: this.formInfo.planId
      }).then((result) => {
        this.$hloading()
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          this.$set(this.categoryListArrList, 'dataSource', [...result.data])
          this.$set(this.categoryListArrList, 'key', this.randomString())
        }
      })
    },
    // 获取当前用户信息
    getUserDetail() {
      this.$loading()
      setTimeout(() => {
        this.$hloading()
      }, 1000)
      return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        return companyOrg
      })
    },
    getCategory(data, arr, categoryList) {
      data.forEach((item) => {
        if (arr.indexOf(item.categoryCode) != -1) {
          categoryList.push({
            categoryCode: item.categoryCode,
            categoryId: item.id,
            categoryName: item.categoryName
          })
        }
        if (item.childrens.length > 0) {
          this.getCategory(item.childrens, arr, categoryList)
        }
      })
    },
    // 选择品类
    selectCategoryas(e) {
      this.formInfo.categoryList = []
      this.getCategory(this.categoryListArrList.dataSource, e, this.formInfo.categoryList)
      this.$nextTick(() => {
        this.$refs.formInfo.validateField('categoryArr')
      })
    },
    // 删除
    removed(e) {
      let { itemData } = e
      let selectCategoryList =
        this.selectCategoryList.filter((item) => item.id !== itemData.id) || []
      this.selectCategoryList = selectCategoryList
      this.isShow = this.selectCategoryList.some((item) => {
        return item.id === '0'
      })
    },

    // 选择计划
    selectPlan(e) {
      let { itemData } = e
      this.formInfo.planId = itemData.id //获取品类传值id
      this.formInfo.planCode = itemData.planCode
      this.formInfo.planName = itemData.planName
      this.formInfo.planVersion = itemData.version
      this.addByPlanuery() // 获取计划品类列表
      this.formInfo.categoryList = []
      this.formInfo.categoryArr = []
      // this.categoryListArrList[0].categoryName = "不限";
    },
    // 输入的时候
    filteringCompany(e) {
      let { text } = e
      if (!text) return
      // filterCompanyTxt = text
      let getCompanyList = this.getCompanyList
      let result = utils.debounce(getCompanyList, 1000)
      result()
    },
    // 添加规则
    addPolicy() {
      this.$refs.formInfo.validate((val) => {
        if (val) {
          let bol = false
          this.strategyRules.forEach((item) => {
            item.outputTypeList.forEach((e) => {
              if (e.outputType === '') {
                bol = true
              }
            })
          })
          if (bol) {
            this.$toast({ content: this.$t('请选择输出值'), type: 'warning' })
            return
          }

          let query = {
            categoryList: this.formInfo.categoryList,
            categoryType: 1,
            orgCode: this.formInfo.orgCode,
            orgId: this.formInfo.orgId,
            orgName: this.formInfo.orgName,
            planCode: this.formInfo.planCode,
            planName: this.formInfo.planName,
            planId: this.formInfo.planId,
            planVersion: this.formInfo.planVersion,
            priority: 0,
            remark: '',
            strategyName: this.formInfo.strategyName,
            strategyRules: []
          }
          this.strategyRules.forEach((item, index) => {
            let obj = {
              name: this.$t('规则') + filterValue(index + 1),
              outputResult: '',
              remark: item.remark,
              strategyRuleInputs: []
            }
            item.outputTypeList.forEach((e, i) => {
              obj.outputResult += e.outputType
              if (item.outputTypeList.length - 1 != i) {
                obj.outputResult += ','
              }
            })
            item.strategyRuleInputs.forEach((e, i) => {
              let newData = {}
              if (i == 0) {
                newData.logicalSymbol = 0
              } else {
                newData.logicalSymbol = e.logicalSymbol ? e.logicalSymbol : 0
                if (e.logicalSymbol != 1 && e.logicalSymbol != 2) {
                  bol = true
                }
              }
              if (e.type == 1) {
                newData.type = e.type
                newData.cycleValue = e.cycleValue
                newData.cycleType = e.cycleType
                newData.assessClass = e.assessClass
                newData.lampColorNumber = e.lampColorNumber
                newData.judgeSymbol = e.judgeSymbol
                newData.compareValue = e.compareValue
              }
              if (e.type == 2) {
                newData.type = e.type
                newData.cycleValue = e.cycleValue
                newData.assessClass = e.assessClass
                newData.lampColor = e.lampColor
              }
              if (e.type == 3) {
                newData.type = e.type
                newData.assessType = e.assessType
                newData.judgeSymbol = e.judgeSymbol
                newData.compareValue = e.compareValue
                newData.lampColor = e.lampColor
                newData.isPeriod = e.isPeriod
              }
              obj.strategyRuleInputs.push(newData)
            })
            query.strategyRules.push(obj)
          })
          if (bol) {
            this.$toast({
              content: this.$t('除了第一个规则表达式，其他的逻辑符号必须为且或'),
              type: 'warning'
            })
            return
          }
          delete query.orgIdArr // 公司
          this.$loading()
          if (this.modalData.isEdit) {
            query.id = this.formInfo.id
            this.$API.policySetting.update(query).then(() => {
              this.$hloading()
              this.cancel()
              this.$emit('confirm-function')
            })
          } else {
            this.$API.policySetting.addPolicy(query).then(() => {
              this.$hloading()
              this.cancel()
              this.$emit('confirm-function')
            })
          }
        }
      })
    },

    // 添加规则组
    addRuleGroup() {
      this.strategyRules.push({
        id: (Math.random() * 100000).toFixed(0),
        strategyRuleInputs: [
          {
            id: (Math.random() * 100000).toFixed(0),
            type: 1
          }
        ],
        outputTypeList: [
          {
            outputType: ''
          }
        ],
        remark: ''
      })
    },
    // 删除规则组
    clearGroup(ruleIndex) {
      let index = Number(ruleIndex) - 1
      this.strategyRules.splice(index, 1)
    },

    beforeClose() {
      this.$emit('cancel-function')
    },
    cancel() {
      this.$emit('cancel-function')
      this.isnone = true
    },
    submitForm() {},
    // 确认提交
    confirm() {
      if (this.modalData.viewlock != 1) {
        this.addPolicy()
      } else {
        this.cancel()
      }
    }
  }
}
</script>

<style lang="scss">
.mt-form-item {
  margin-bottom: 20px;
}
.mt-form-item-topLabel {
  .label {
    color: #292929;
    font-weight: normal;
  }
}
</style>

<style lang="scss" scoped>
.slider-panel-container {
  .slider-modal {
    width: 860px !important;
    .slider-header {
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }

    .add-rule-group {
      margin-top: 30px;
      height: 14px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      width: 100%;
      text-align: center;
      cursor: pointer;
    }

    .slider-footer {
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
</style>
