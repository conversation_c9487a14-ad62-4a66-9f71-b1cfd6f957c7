<template>
  <!-- 策略设置Tab -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      index: 0,
      pageConfig: pageConfig(this.$API.policySetting.getList)
    }
  },
  methods: {
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let { toolbar } = e
      if (toolbar.id === 'addNew') {
        this.addNewPolicy()
        return
      }
      let _selectRecords = e.gridRef.getMtechGridRecords()
      if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 编辑
      if (toolbar.id === 'editPolicy') {
        if (_selectRecords.length > 1) {
          this.$toast({ content: this.$t('一次只能编辑一行!'), type: 'warning' })
          return
        }
        this.editPolicy(_selectRecords)
        return
      }
      // 删除
      if (toolbar.id === 'delete') {
        //   if (_selectRecords.length > 1) {
        //     this.$toast({ content: "一次只能编辑一行!", type: "warning" });
        //     return;
        //   }
        this.DeleteInline(_selectRecords)
        return
      }
      // 表头启用
      if (e.toolbar.id == 'Enable') {
        this.handleBatchStart(_selectRecords, 1)
      }
      // 禁用按钮事件
      if (e.toolbar.id == 'disable') {
        this.handleBatchStart(_selectRecords, 2)
      }
      // 复制
      if (e.toolbar.id == 'copy') {
        // 复制维度操作
        this.cayInline(_selectRecords)
      }
    },
    // 新建策略
    addNewPolicy() {
      const _this = this
      this.$dialog({
        modal: () => import('./components/addPolicy.vue'),
        data: {
          title: this.$t('新增策略设置')
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 编辑
    editPolicy(policyData) {
      // 判断  policyData 类型 是数组还是对象
      if ((policyData.constructor == Array) == true) {
        policyData = policyData[0]
      }
      if (policyData.status == 1) {
        this.$toast({ content: this.$t('启用状态下不可编辑!'), type: 'warning' })
      } else {
        const _this = this
        this.$dialog({
          modal: () => import('./components/addPolicy.vue'),
          data: {
            title: this.$t('编辑策略设置'),
            isEdit: true,
            policyData
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    // 点击单元格内按钮  编辑 加 启用等按钮
    handleClickCellTool(e) {
      if (e.tool.id == 'Start') {
        // 启用
        this.handleHeparture([e.data.id], 1)
      } else if (e.tool.id == 'Stop') {
        // 禁用操作
        this.handleHeparture([e.data.id], 2)
      } else if (e.tool.id == 'edit') {
        // 编辑维度操作
        this.editPolicy(e.data)
      } else if (e.tool.id == 'delete') {
        // 删除维度操作
        this.DeleteInline(e.data)
      }
    },

    //更新配置的状态(行内操作+批量操作)
    handleHeparture(idList, num) {
      //状态 1: this.$t("启用"), 2: "停用"
      let status = 0
      if (num == 2) {
        status = 1
      } else {
        status = 0
      }
      let _params = {
        idList,
        status: num
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {
          this.$API.policySetting.enable(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // 删除
    DeleteInline(val) {
      if ((val.constructor == Array) == true) {
        val = val[0]
      }
      if (val.status == 1) {
        this.$toast({ content: this.$t('启用状态下不可删除!'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除数据？')
          },
          success: () => {
            let idList = []
            // 判断  val 类型 是数组还是对象
            if ((val.constructor == Array) == true) {
              val.forEach((item) => {
                idList.push(item.id)
                return
              })
            } else {
              idList.push(val.id)
            }
            this.$API.policySetting['deletePolicy']({ idList }).then(() => {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              idList = []
              this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
            })
          }
        })
      }
    },
    //批量复制操作
    cayInline(_selectRecords) {
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _unRecords = _selectRecords.length
      if (_unRecords > 1) {
        this.$toast({
          content: this.$t('请选择一行数据进行复制'),
          type: 'warning'
        })
        return
      } else {
        this.cayture(_selectIds)
      }
    },
    // 复制
    cayture(val) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认复制数据？')
        },
        success: () => {
          // let idList = [];
          // idList.push(val.id);
          let params = new FormData()
          params.append('id', val[0])
          this.$API.policySetting['copy'](params).then(() => {
            this.$toast({
              content: this.$t('复制成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
          })
        }
      })
    },

    //批量提交操作
    handleBatchStart(_selectRecords, num) {
      //状态 0: this.$t("启用"), 1: "停用"
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _unRecords = _selectRecords.filter((s) => s.status == num)
      if (_unRecords.length > 0) {
        //选中数据中，存在‘启用’状态
        this.$toast({
          content: `${this.$t('勾选数据中，存在')}'${
            num == 2 ? this.$t('停用') : this.$t('启用')
          }'${this.$t('状态的数据')}.`,
          type: 'warning'
        })
        return
      } else {
        this.handleHeparture(_selectIds, num)
      }
    },

    // 点击单元格文字 编码跳转
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e, this.index)
      if (e.field == 'strategyCode') {
        this.lookDetails(e.data)
      }
      //查看策略操作
    },
    // 查看策略设置
    lookDetails(policyData) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/addPolicy.vue'),
        data: {
          title: this.$t('查看策略设置'),
          viewlock: 1,
          policyData
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.mt-data-grid {
  .grid-edit-column {
    justify-content: center;
  }
  .status-label {
    font-size: 12px;
    padding: 2px;
    border-radius: 2px;
    &.status-disable {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-enable {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
.grade-config-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #6386c1;
  .template-status {
    display: inline-block;
    padding: 4px;
    background: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
    line-height: 12px;
    margin-bottom: 4px;
  }
  .status-disable {
    color: #9a9a9a;
    background: rgba(154, 154, 154, 0.1);
  }
  .btn-status {
    cursor: pointer;
  }
}

.toop-tip {
  position: relative;
  cursor: pointer;

  .base-txt {
    span {
      display: inline-block;
      padding-right: 4px;
      color: #00469c;
      cursor: pointer;
    }
  }

  .pop-txt {
    display: none;
    padding: 4px;
    border: 1px solid #ddd;
    background: #fff;
    position: absolute;
    z-index: 9;
    right: 0%;
    top: -100%;

    &::after {
      position: absolute;
      top: 50%;
      left: -8px;
      transform: translateY(-8px);
      content: '';
      display: inline-block;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-right: 8px solid #616161;
    }
  }
}

.toop-tip:hover {
  .pop-txt {
    display: block;
  }
}
</style>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
}
</style>
