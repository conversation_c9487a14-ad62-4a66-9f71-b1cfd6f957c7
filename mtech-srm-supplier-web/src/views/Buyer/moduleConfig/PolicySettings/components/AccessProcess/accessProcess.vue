<template>
  <div class="access-process">
    <mt-tabs
      id="tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <process-config method="access" v-if="selectIndex === 0"></process-config>
    <process-view v-if="selectIndex === 1"></process-view>
  </div>
</template>

<script>
import ProcessConfig from './components/processConfig/index.vue'
import ProcessView from './components/processView/index.vue'

export default {
  components: {
    ProcessConfig,
    ProcessView
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('流程配置')
        },
        {
          title: this.$t('流程适用视图')
        }
      ]
    }
  },
  methods: {
    handleSelectTab(index) {
      this.selectIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.access-process {
  overflow: auto;
  #tabs,
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
