<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="title"
    :buttons="buttons"
    :open="onOpen"
    :close="init"
  >
    <div class="uploader">
      <!-- 上传框 -->
      <mt-uploader
        ref="uploadObj"
        class="to-upload"
        :name="inputName"
        v-if="!isView && (!isSingleFile || (isSingleFile && viewFileData.length == 0))"
        :async-settings="asyncSettings"
        :uploading="uploading"
        :max-file-size="1000000000000"
        :multiple="true"
        :show-file-list="false"
        @progress="progress"
        @success="success"
        @selected="selected"
      >
        <div class="upload-box" @click="startToUpload">
          <mt-icon class="plus-icon" name="icon_Close_1"></mt-icon>
          <div class="right-state">
            <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
            <div class="warn-text">{{ $t('文件最大不可超过') }}50M</div>
          </div>
        </div>
      </mt-uploader>

      <!-- 上传状态 -->
      <div v-for="(item, index) in viewFileData" class="has-file" :key="index">
        <!-- 左边的信息 -->
        <div class="left-info">
          <div class="file-box">
            <div class="file-info">
              <div
                :class="['file-name', item.progress === undefined && 'upload']"
                @click="fileNameClick(item)"
              >
                {{ item.fileName }}
              </div>
              <mt-progress
                v-if="item.progress"
                :id="index"
                ref="progress"
                type="Linear"
                height="20"
                width="100%"
                :value="item.progress"
              >
              </mt-progress>
              <div class="file-size">{{ item.fileSize | byteToKB }}KB</div>
            </div>
          </div>
        </div>
        <!-- 删除按钮 -->
        <mt-icon
          v-if="!isView && item.progress === undefined"
          name="icon_Close_1"
          class="close-icon"
          @click.native="handleRemove(item)"
        ></mt-icon>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { HEADER_TOKEN_KEY, getToken } from '@mtech-sso/single-sign-on'
import { PROXY_FILE } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import MtProgress from '@mtech-ui/progress'
import MtUploader from '@mtech-ui/uploader'

export default {
  components: { MtProgress, MtUploader },
  data() {
    return {
      title: this.$t('附件上传'),
      isView: false,
      required: true,
      uploadData: [], // 上传完成的文件数据
      viewFileData: [], // 查看状态的数据
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      isSingleFile: false, // 是否是单文件上传
      inputName: 'UploadFiles', // 上传组件的 name，此值为调用 api 请求参数的 key
      asyncSettings: {
        saveUrl: `/api${PROXY_FILE}/user/file/uploadPrivate?useType=2`
      },
      files: [] // 上传后得到的数据
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100
  },
  methods: {
    init() {
      this.$refs.uploader.init()
    },
    fileChange(data) {
      this.uploadData = data
      this.$emit('change', data)
    },
    /**
     * fileData: 文件信息;
     * isView：是否为查看状态;
     * required：是否必须;
     * title: 标题;
     */
    dialogInit(entryInfo) {
      const { fileData, isView, required, title } = entryInfo
      this.viewFileData = fileData || []
      this.isView = isView || false // 默认 上传状态
      this.required = required != null ? required : true // 默认 必须
      this.title = title || this.$t('附件上传') // 默认 "附件上传"
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      if (
        !this.isView &&
        this.required &&
        this.uploadData.length == 0 &&
        this.viewFileData.length == 0
      ) {
        this.$toast({ content: this.$t('请上传附件'), type: 'warning' })
        return
      }
      this.$emit('confirm')
      this.handleClose()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },

    startToUpload() {
      this.$refs.uploadObj.pickFile()
    },

    // 移除文件
    handleRemove(e) {
      this.viewFileData = this.viewFileData.filter((item) => item.id !== e.id)
      const inputElement =
        document.querySelector(`[name=${this.inputName}] input`) ||
        document.querySelector(`.e-file-select input`)
      inputElement.value = '' // 清空上传 input
      this.$emit('change', this.filterEmitFiles())
    },
    // 文件名点击事件
    fileNameClick(e) {
      if (e.remoteUrl) {
        window.open(`http://filepreview.dev.qeweb.com/preview/onlinePreview?url=${e.remoteUrl}`)
      }
    },
    // 选择文件后
    selected(args) {
      if (args.filesData[0].size > 52428800) {
        args.cancel = true // 取消选择
        this.$toast({
          content: this.$t('单个文件，限制50M')
        })
      }
    },
    // 上传开始
    uploading(args) {
      args.currentRequest.setRequestHeader(HEADER_TOKEN_KEY, getToken())
      this.viewFileData.push({
        statusName: 'uploading', // 状态名称 "uploading"
        id: args.fileData.id, // 组件内文件id
        fileName: args.fileData.name, // 文件名
        fileSize: args.fileData.size, // 文件大小
        progress: 0 // 上传进度
      })
    },
    // 进度事件
    progress(args) {
      const fileId = args.file.id
      const fileList = cloneDeep(this.viewFileData)
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].id === fileId) {
          fileList[i].statusName = 'progress' // 状态名称 "progress"
          fileList[i].progress = (args.e.loaded / args.e.total) * 100 // 上传进度
          break
        }
      }
      this.viewFileData = fileList
    },
    // 上传成功
    success(args) {
      const fileId = args.file.id
      const fileList = cloneDeep(this.viewFileData)
      if (args.operation === 'upload') {
        // 上传完成
        let _response = args.e.target.response
        if (!_response) return
        const response = JSON.parse(_response)
        if (response.code == 200) {
          // 保存api返回的文件信息;
          for (let i = 0; i < fileList.length; i++) {
            if (fileList[i].id === fileId) {
              fileList[i] = {
                fileData: args.file,
                ...response.data // api 返回的数据
              }
              break
            }
          }
          this.viewFileData = fileList
          this.$emit('change', this.filterEmitFiles())
        } else {
          for (let i = 0; i < fileList.length; i++) {
            if (fileList[i].id === fileId) {
              fileList.splice(i, 1) // 移除当前项
              break
            }
          }
          this.viewFileData = fileList
          // 错误处理
          // this.$toast({
          //   content: response.msg,
          //   type: "error",
          // });
        }
      }
    },
    // 过滤出事件发出的数据
    filterEmitFiles() {
      const fileList = cloneDeep(this.viewFileData)
      const files = fileList.filter((item) => {
        let pass = false
        if (item.progress === undefined) {
          // 过滤出api返回的数据
          item.fileData = undefined // 置空不必要的参数
          pass = true
        }

        return pass
      })

      return files
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader {
  padding: 4px;
  // background: rgba(251, 252, 253, 1);
  position: relative;

  /deep/ .to-upload {
    display: flex;
    height: 70px;
    position: relative;
    border: 1px dashed rgba(232, 232, 232, 1);
    margin: 10px 0 10px 0;
    cursor: pointer;

    & > div {
      width: 100%;
    }

    .mt-dialog {
      flex: 1;
    }

    .e-control-wrapper {
      display: none;
    }
  }

  .upload-input {
    cursor: pointer;
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    background: #fbfcfd;
    margin-bottom: 10px;
    .plus-icon {
      transform: rotate(45deg);
      color: #98aac3;
      margin: 0 12px 0 10px;
      opacity: 0.6;
      width: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 24px;
    }
    .right-state {
      .plus-txt {
        width: 200px;
        height: 20px;
        font-size: 20px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(152, 170, 195, 1);
        margin-bottom: 10px;
      }
      .warn-text {
        font-size: 10px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(241, 62, 62, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }
  }

  .has-file {
    height: 70px;
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px dashed rgba(232, 232, 232, 1);
    margin: 10px 0 10px 0;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: inherit;

      .file-box {
        height: inherit;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        overflow: auto;
        img {
          height: inherit;
          width: fit-content;
        }
        .file-info {
          padding: 0 10px;
          width: 100%;
          .file-name {
            color: rgba(154, 154, 154, 1);
            font-size: 14px;

            &.upload {
              color: rgba(0, 70, 156, 1);
              cursor: pointer;
            }
          }
          .file-size {
            color: #9a9a9a;
            font-size: 12px;
          }
        }
      }
    }

    .close-icon {
      font-size: 14px;
      padding-right: 30px;
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
