<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- 阶段为行数据 列为新增的场景  -->
      <mt-data-grid
        id="Grid"
        ref="dataGrid"
        height="350"
        :edit-settings="editSettingsBatch"
        :data-source="dataSource"
        :column-data="columnData"
        :toolbar="toolbarOptions"
        @toolbarClick="clickHandler"
      ></mt-data-grid>
    </div>
  </mt-dialog>
</template>

<script>
let fieldName = ''
import bus from '@/utils/bus'
import {
  editSettingsBatch,
  toolbarOptions,
  addHeader,
  query2Local,
  scenFieldObj
} from '../config/index.js'

export default {
  data() {
    return {
      dataSource: [],
      columnData: [],
      toolbarOptions,
      editSettingsBatch,
      buttons: [
        {
          click: this.next,
          buttonModel: { content: this.$t('下一步') }
        },
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      sceneDefineList: [], // 场景枚举
      sceneStageDetail: [], // 场景枚举
      isCheckedList: [], // 是否勾选了
      checkedObj: {}, // 场景键值对 是否勾选
      isAdding: false, // 是否在新增
      scenFieldObj1: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    _this() {
      return this.modalData._this
    },
    header() {
      return this.modalData.title
    },
    // 阶段为行数据
    accessTemplate() {
      return this.modalData.accessTemplate
    }
  },
  async created() {
    this.$loading()
    let { id: accessTemplateId, stageTemplationDTOList } = this.accessTemplate
    // 获取场景定义枚举
    let sceneDefineList = await this.querySceneDefine()
    this.sceneDefineList = sceneDefineList
    let sceneStageDetail = await this.querySceneStageDetail(accessTemplateId)
    this.sceneStageDetail = sceneStageDetail
    this.$hloading()
    this.renderGrid(sceneStageDetail, sceneDefineList, stageTemplationDTOList)

    this.$nextTick(() => {
      this.bindEvents()
      setTimeout(() => {
        this.$refs.dataGrid.ejsRef.selectRow(0, true)
        this.$refs.dataGrid.ejsRef.startEdit()
      }, 600)
    })
  },
  mounted() {
    this.show()
  },
  methods: {
    chagneDataGrid(event) {
      console.log('chagneDataGrid', event)
    },
    actionComplete(event) {
      console.log('actionComplete', event, this.$refs.dataGrid.ejsRef)
      const { action, requestType } = event
      if (action === 'edit' && requestType === 'save') {
        // 判断数据是否能正常
      }
    },
    endEdit() {
      this.$refs.dataGrid.ejsRef.endEdit()
    },
    bindEvents() {
      bus.$off('getScene')
      bus.$off('getChecked')
      bus.$off('setDropDown')
      bus.$on('getScene', (data, fieldName) => {
        this.setItemScene(data, fieldName)
        console.log('head', data)
      })
      bus.$on('getChecked', (data) => {
        let index = this.isCheckedList.findIndex((item) => item.fieldNames === data.fieldNames)
        if (index >= 0) {
          this.isCheckedList.splice(index, 1)
        } else {
          this.isCheckedList.push(data)
        }
        let num = data.fieldNames.substring(9)
        this.checkedObj[num] = data.checked
        console.log('Checked', data, this.checkedObj)
      })
      // 每次编辑的时候 列名 及时赋值 没有行
      bus.$on('setDropDown', (itemData, field, index) => {
        this.$nextTick(() => {
          this.dataSource.forEach((item, i) => {
            if (i == index) {
              item[field] = itemData.value
            }
          })
        })
      })
    },

    // 设置dataSource 列场景值
    setItemScene(data, fieldName) {
      this.scenFieldObj1[`${fieldName}Name`] = data.text
      this.scenFieldObj1[`${fieldName}Value`] = data.value
      this.dataSource = this.dataSource.map((item) => {
        return {
          ...item,
          fileCallNamed: fieldName,
          [fieldName]: item[fieldName] || '',
          [this.scenFieldObj1[`${fieldName}Value`]]: item[fieldName] || '',
          [`${fieldName}Name`]: this.scenFieldObj1[`${fieldName}Name`],
          [`${fieldName}Value`]: this.scenFieldObj1[`${fieldName}Value`]
        }
      })
    },

    // 分页查询场景定义
    querySceneDefine() {
      return this.$API.AccessProcess['querySceneDefine']({
        condition: '',
        page: {
          current: 1,
          size: 1000
        }
      })
        .then((res) => {
          if (res.code == 200 && res.data && res.data.records && res.data.records.length > 0) {
            let sceneMap = {}
            for (let i of res.data.records) {
              sceneMap[i.id] = i.sceneName
            }
            return sceneMap
          } else {
            this.$toast({ content: res.msg || this.$t('系统异常'), type: 'error' })
            return []
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
          return []
        })
    },
    // 根据格子数据来获取grid的配置
    renderGrid(sceneStageDetail, sceneDefineList, stageTemplationDTOList) {
      let columnObject = {}
      sceneStageDetail.forEach((item) => {
        // 先获取列的对象 然后通过forin拿出来键值对数组 生成colunmData 然后根据场景filter对应的单元格数据
        !columnObject[item.sceneDefineId] &&
          (columnObject[item.sceneDefineId] = sceneDefineList[item.sceneDefineId])
      })
      console.log(columnObject)
      this.columnData = this.getStageTemplationClumn(columnObject)
      console.log(this.columnData)
      this.dataSource = this.getGridList(stageTemplationDTOList)
      // 已有详情 编辑
      if (sceneStageDetail.length > 0) {
        sceneStageDetail.forEach((item) => {
          // 存在这个场景
          let fieldName = this.scenFieldObj1[item.sceneDefineId]
          if (fieldName) {
            this.dataSource.forEach((cItem, cIndex, arr) => {
              arr[cIndex] = {
                ...cItem,
                fileCallNamed: fieldName,
                [this.scenFieldObj1[`${fieldName}Value`]]: item[fieldName] || '',
                [`${fieldName}Name`]: this.scenFieldObj1[`${fieldName}Name`],
                [`${fieldName}Value`]: this.scenFieldObj1[`${fieldName}Value`]
              }
              // 行列 相同
              if (
                item.stageTemplateId === cItem.stageTemplateIdName // && !!this.scenFieldObj1[item.sceneDefineId]
              ) {
                arr[cIndex] = {
                  [`${fieldName}id`]: item.id,
                  ...arr[cIndex],
                  [fieldName]: query2Local[item.status] || ''
                }
              }
            })
          }
        })
        console.log('detail datasource', this.dataSource)
      }
      console.log(this.dataSource)
    },
    // 获取行数据
    getGridList(stageTemplationDTOList) {
      let dataList = []
      // 阶段
      stageTemplationDTOList.forEach((item, index) => {
        let lineObj = {
          ['stageTemplateId']: item.stageName,
          ['stageTemplateIdName']: item.id,
          index: index
        }
        dataList.push(lineObj)
      })
      return dataList
    },

    // 获取列定义
    getStageTemplationClumn(columnObject) {
      let columnList = [
        {
          width: '150',
          field: 'stageTemplateId',
          headerText: this.$t('阶段'),
          allowEditing: false
        }
      ]
      for (let colunmId in columnObject) {
        // 获取 场景
        let sceneNameList = []
        for (let [key, value] of Object.entries(this.sceneDefineList)) {
          sceneNameList.push({
            text: value,
            value: key
          })
        }

        fieldName = `editField${columnList.length + 1}`
        this.scenFieldObj1[`${fieldName}Name`] = columnObject[colunmId]
        this.scenFieldObj1[`${fieldName}Value`] = colunmId
        this.scenFieldObj1[colunmId] = fieldName

        columnList = addHeader(columnList, sceneNameList, fieldName, colunmId)
      }
      return columnList
    },

    // 按钮点击事件
    clickHandler(args) {
      if (args.item.id === 'AddBtn') {
        this.addScene()
        return
      }

      if (args.item.id === 'Deletebtn') {
        this.deleteCol()
        return
      }
    },

    // 删除列
    deleteCol() {
      const _this = this
      let checkedList = this.isCheckedList.filter((item) => item.checked)
      if (checkedList.length === 0) {
        this.$toast({ content: this.$t('请勾选场景'), type: 'warning' })
        return
      }

      // let sceneDefineIdList =
      //   checkedList.filter((item) => !!item.id).map((item) => item.id) || [];

      // 存在空列 并且全部是空列(未上传得)
      // if (
      //   sceneDefineIdList.length !== checkedList.length &&
      //   sceneDefineIdList.length === 0
      // ) {
      _this.deleColunData()
      // return;
      // }

      // this.$dialog({
      //   data: {
      //     title: this.$t("删除"),
      //     message: this.$t("是否确认删除所选场景？"),
      //     confirm: () =>
      //       _this.$API.AccessProcess["deleteScene"]({
      //         accessTemplateId,
      //         sceneDefineIds: sceneDefineIdList.join(","),
      //       }),
      //   },
      //   success: () => {
      //     _this.$toast({ content: this.$t("删除成功"), type: "success" });
      //     _this.deleColunData();
      //   },
      // });
    },

    // 数据上删除列
    deleColunData() {
      for (let key in this.checkedObj) {
        if (this.checkedObj[key]) {
          let index = -1
          this.columnData.forEach((item, i) => {
            if (item.field.indexOf(key) != -1) {
              index = i
            }
          })
          this.columnData.splice(index, 1)
          this.dataSource.forEach((item) => {
            delete item[`editField${key}`]
            delete item[`editField${key}Name`]
            delete item[`editField${key}Value`]
            delete item[`fileCallNamed`]
            delete item[`editField${key}id`]
          })
          delete scenFieldObj[`editField${key}`]
          delete this.scenFieldObj1[`editField${key}Name`]
          delete this.scenFieldObj1[`editField${key}Value`]
        } else {
          continue
        }
      }
      // 不 this.columnData 上 splice 就会渲染grid挂了
      // 重置场景勾选checked
      this.isCheckedList = []
      this.checkedObj = {}
      this.diguidelete()
    },

    diguidelete() {
      let isHasEmpty = this.columnData.filter((item) => !item.field).length > 0 ? true : false
      if (isHasEmpty) {
        this.columnData.forEach((item, index) => {
          if (!item.field) {
            this.columnData.splice(index, 1)
          }
        })
        this.diguidelete()
      }
    },

    // 添加列场景
    addScene() {
      // 获取详情里面没有的 场景
      let sceneNameList = []
      let colNum = this.columnData.length - 1
      for (let [key, value] of Object.entries(this.sceneDefineList)) {
        // 去除已经勾选过得场景 不需要
        // if (
        //   this.sceneStageDetail.filter((item) => item.sceneDefineId === key)
        //     .length === 0
        // ) {
        sceneNameList.push({
          text: value,
          value: key
        })
        // }
      }
      console.log(sceneNameList)
      // 超过长度场景的长度
      if (colNum >= sceneNameList.length) {
        this.$toast({
          content: this.$t('可添加场景已用完！'),
          type: 'warning'
        })
        return
      }

      colNum >= 1 && this.setDataSource(fieldName)
      // 信息填充
      let index = 0
      if (this.columnData.length == 1) {
        index = 1
      } else {
        index = this.columnData[this.columnData.length - 1].field.substring(9)
      }
      fieldName = `editField${Number(index) + 1}`
      this.columnData = addHeader(this.columnData, sceneNameList, fieldName, '')
      // 重置场景勾选checked
      this.isCheckedList = []
      this.checkedObj = {}
    },

    // 设置单元格值
    setDataSource() {
      this.$refs.dataGrid.ejsRef.endEdit()
      let BatchChanges = this.$refs.dataGrid.ejsInstances.getBatchChanges()
      let dataSource = JSON.parse(JSON.stringify(this.dataSource))
      console.log(this.dataSource)
      console.log('setDataSourc', BatchChanges)
      // 行数据
      let { changedRecords = [] } = BatchChanges

      dataSource = dataSource.map((dItem) => {
        for (let item of changedRecords) {
          // 列 行 获取对应的下拉值 editFieldn的值
          if (
            !!item.hasOwnProperty(dItem.fileCallNamed) &&
            dItem.stageTemplateId === item.stageTemplateId
          ) {
            dItem = {
              ...dItem,
              ...item
            }
          }
        }
        return dItem
      })

      this.dataSource = JSON.parse(JSON.stringify(dataSource))
    },

    // 是否全部都编辑了
    isFullEdit() {
      // let len = this.columnData.length - 1;
      let isFullEdit = true
      // for (let i = 0; i < len; i++) {
      //   if (
      //     this.dataSource.filter((item) => !item[`editField${i + 2}`]).length >
      //     0
      //   ) {
      //     isFullEdit = false;
      //     break;
      //   }
      // }
      this.columnData.forEach((item, index) => {
        if (index != 0) {
          let arr = this.dataSource.filter((e) => !e[item.field])
          if (arr.length > 0) {
            isFullEdit = false
          }
        }
      })
      return isFullEdit
    },

    // 判断是否又重复得场景
    isSimilarScene() {
      let isSimilarScene = false
      // let len = this.columnData.length - 1;
      let judgeObj = {}
      // for (let i = 0; i < len; i++) {
      //   if (judgeObj[this.scenFieldObj1[`editField${i + 2}Value`]]) {
      //     isSimilarScene = true;
      //     break;
      //   }
      //   judgeObj[this.scenFieldObj1[`editField${i + 2}Value`]] = true;
      // }
      this.columnData.forEach((item, index) => {
        if (index != 0) {
          if (judgeObj[this.scenFieldObj1[item.field + 'Value']]) {
            isSimilarScene = true
          }
          judgeObj[this.scenFieldObj1[item.field + 'Value']] = true
        }
      })
      return isSimilarScene
    },

    next() {
      this.endEdit()
      this.setDataSource(fieldName)
      console.log('dataSourceLine', this.dataSource)
      console.log('dataSourceLine', this.columnData)
      console.log('dataSourceLine', this.scenFieldObj1)

      let isempty = false
      this.columnData.forEach((item, index) => {
        if (index != 0) {
          if (!this.scenFieldObj1[item.field + 'Value']) {
            isempty = true
          }
        }
      })
      if (isempty) {
        this.$toast({
          content: this.$t('请选择场景!'),
          type: 'warning'
        })
        return
      }
      let isSimilarScene = this.isSimilarScene()
      if (isSimilarScene) {
        this.$toast({
          content: this.$t('场景名称不能重复!'),
          type: 'warning'
        })
        return
      }
      let isFullEdit = this.isFullEdit()
      if (!isFullEdit) {
        this.$toast({
          content: this.$t('请填写完整！'),
          type: 'warning'
        })
        return
      }
      this.queryAddScene()
    },

    queryAddScene() {
      this.$loading()
      // 新增和编辑是一个接口 新增带id接口
      let params = []
      let { id: accessTemplateId } = this.accessTemplate
      this.dataSource.forEach((item) => {
        this.columnData.forEach((e, i) => {
          if (i != 0) {
            let id = e.field
            let singleItem = {
              sceneDefineId: item[`${id}Value`],
              sceneName: item[`${id}Name`],
              stageName: item.stageTemplateId,
              stageTemplateId: item.stageTemplateIdName,
              status: this.getRealStatus(item[id])
            }
            !!item[`${id}id`] && (singleItem.id = item[`${id}id`])
            params.push(singleItem)
          }
        })
      })
      let fullParams = {
        accessTemplateId,
        list: params
      }
      this.$hloading()
      this.$emit('confirm-function', fullParams)
      // this.$API.AccessProcess["querySaveOrUpdate"](fullParams).then((res) => {
      //   if (res.code == 200) {
      //     this.$emit("confirm-function",fullParams);
      //   }
      // });
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 查询场景任务矩阵
    querySceneStageDetail(accessTemplateId) {
      return this.$API.AccessProcess['querySceneStageDetail'](accessTemplateId)
        .then((res) => {
          if (res.code == 200) {
            return res.data
          } else {
            this.$toast({ content: res.msg || this.$t('系统异常'), type: 'error' })
            return []
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
          return []
        })
    },
    // 状态（0默认，1适用，2不适用）
    getRealStatus(status) {
      let result = 0
      switch (status) {
        case 1:
          result = 1
          break
        case 2:
          result = 0
          break
        case 3:
          result = 2
          break
      }
      return result
    }
  },
  beforeDestroy() {
    bus.$off('getScene')
    bus.$off('getChecked')
    bus.$off('setDropDown')
  }
}
</script>

<style lang="scss">
.dialog-content {
  margin-top: 20px;
}
.circle {
  width: 12px;
  height: 12px;
  background: rgba(0, 70, 156, 1);
  border-radius: 50%;
}
.trangle {
  width: 0;
  height: 0;
  border-bottom: 12px solid #8acc40;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
}
.cance {
  font-size: 12px;
  color: #f53f3f;
}

.category-checkbox {
  margin-left: 10px;
}
</style>
<style lang="scss" scoped></style>
