<!-- 晋级效果表单 -->
<template>
  <div class="upgrade-effect">
    <h4>{{ $t('晋级效果') }}</h4>
    <div v-for="(formInfo, index) in effectValueList" :key="formInfo.id">
      <mt-form class="form-box" :model="formInfo" :rules="rules" :ref="'effectRef' + index">
        <mt-row :gutter="20">
          <mt-col :xs="24" :md="11" :lg="11">
            <mt-form-item class="form-item" prop="effectType">
              <mt-select
                v-model="formInfo.effectType"
                :placeholder="$t('选择晋级效果类型')"
                float-label-type="Never"
                :data-source="effectTypeList"
                :fields="stageFields"
                @select="handleSlteffectType($event, index)"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :xs="24" :md="11" :lg="11">
            <mt-form-item class="form-item" prop="effectValue">
              <mt-select
                v-model="formInfo.effectValue"
                :placeholder="$t('选择晋级效果')"
                float-label-type="Never"
                :data-source="formInfo.effectValueList"
                :fields="stageFields"
                @select="handleSlteffectValue($event, index)"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :xs="24" :md="1" :lg="1">
            <div class="close-icon" @click.stop="deleteCurrentTask(index)">
              <i class="mt-icons mt-icon-icon_input_clear"></i>
            </div>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <div class="add-task-btn" @click="addEffect">
      <i class="mt-icons mt-icon-icon_card_plus"></i>
      <span>{{ $t('添加晋级效果') }}</span>
    </div>
  </div>
</template>

<script>
import { validateFormList, getRandom } from './index'
const DefaultTask = {
  effectType: null,
  effectTypeName: null,
  effectValue: null,
  effectValueName: null,
  effectValueList: []
}
let effectTypeListMap = new Map()
export default {
  props: {
    isEdit: {
      type: Boolean,
      required: false,
      default: false
    },
    dataSource: {
      type: Array,
      required: false,
      default: () => []
    },
    effectTypeList: {
      type: Array,
      default: () => []
    }
  },
  watch: {},
  mounted() {
    this.initData()
  },
  data() {
    return {
      // 晋级效果数组
      effectValueList: [
        JSON.parse(
          JSON.stringify({
            id: getRandom(),
            ...DefaultTask
          })
        )
      ],
      formInfo: {
        effectType: null,
        effectValue: null
      },
      stageFields: {
        text: 'itemName',
        value: 'itemCode'
      },
      rules: {
        effectType: [
          // { required: true, message: this.$t("选择晋级效果类型"), trigger: "blur" },
          { required: true, validator: this.validateUni, trigger: 'blur' }
        ],
        effectValue: [{ required: true, message: this.$t('选择晋级效果'), trigger: 'blur' }]
      }
    }
  },
  methods: {
    initData() {
      if (this.dataSource.length > 0) {
        this.effectValueList = JSON.parse(JSON.stringify(this.dataSource))
        this.effectValueList.forEach((item, index) => {
          item.effectType && this.queryStatusType(item.effectType, index)
        })
      }
    },
    validateUni(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('选择晋级效果类型')))
      }
      if (value) {
        let isHasSimilarNum = 0
        this.effectValueList.forEach((item) => {
          if (item.effectType === value) {
            isHasSimilarNum++
          }
        })
        if (isHasSimilarNum >= 2) {
          callback(new Error(this.$t('晋级效果类型不能重复')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    // 晋级效果不能重复
    handleSlteffectType(e, index) {
      const { itemData } = e
      const { itemCode, itemName } = itemData
      let isHasSimilar = false
      this.effectValueList.forEach((item) => {
        if (item.effectType === itemCode) {
          isHasSimilar = true
        }
      })
      if (isHasSimilar) {
        this.$set(this.effectValueList[index], 'effectType', null)
        this.$set(this.effectValueList[index], 'effectTypeName', null)
        this.$set(this.effectValueList[index], 'effectValue', null)
        this.$set(this.effectValueList[index], 'effectValueName', null)
        this.$toast({
          content: this.$t('晋级效果类型不能重复'),
          type: 'warning'
        })
        return
      }

      this.$set(this.effectValueList[index], 'effectType', itemCode)
      this.$set(this.effectValueList[index], 'effectTypeName', itemName)

      this.$loading()
      this.queryStatusType(itemData.itemCode, index)
    },

    handleSlteffectValue(e, index) {
      const { itemData } = e
      const { itemCode, itemName } = itemData
      this.$set(this.effectValueList[index], 'effectValue', itemCode)
      this.$set(this.effectValueList[index], 'effectValueName', itemName)
    },

    // 根据晋级状态 获取晋级效果
    queryStatusType(dictCode, index) {
      if (effectTypeListMap.has(dictCode)) {
        this.$hloading()
        this.effectValueList[index].effectValueList = effectTypeListMap.get(dictCode)
        return
      }
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        this.$hloading()
        setTimeout(() => {
          this.$hloading()
        }, 1000)
        const { code, data } = res
        if (code == 200 && data) {
          let reData = data.map((item) => {
            item.itemCode = Number(item.itemCode)
            return item
          })
          this.$set(this.effectValueList, index, {
            ...this.effectValueList[index],
            effectValueList: reData
          })
          // this.effectValueList[index].effectValueList = data;
          effectTypeListMap.set(dictCode, data)
        }
      })
    },

    // 循环校验规则
    validateTaskConfig() {
      return validateFormList(this.effectValueList, 'effectRef', this)
    },

    // 删除
    deleteCurrentTask(index) {
      if (this.effectValueList.length === 1) return
      this.effectValueList.splice(index, 1)
    },

    // 添加晋级效果
    addEffect() {
      if (this.effectValueList.length >= this.effectTypeList.length) {
        this.$toast({
          content: this.$t('晋级效果数量超过最大数量！'),
          type: 'warning'
        })
        return
      }
      let rs = this.validateTaskConfig()
      if (rs) {
        this.effectValueList.push(
          JSON.parse(
            JSON.stringify({
              id: getRandom(),
              ...DefaultTask
            })
          )
        )
      }
    },

    // 父级获取数据
    getCurrentEffectData() {
      return this.effectValueList.map((item) => {
        let tmpItem = JSON.parse(JSON.stringify(item))
        delete tmpItem.effectValueList
        if (this.isEdit) {
          return tmpItem
        } else {
          delete tmpItem.id
          return tmpItem
        }
      })
    }
  }
}
</script>

<style lang="scss">
.add-task-btn {
  display: inline-flex;
  color: #6386c1;
  cursor: pointer;
  margin: 10px 0;
  > i {
    font-size: 18px;
  }
  > span {
    display: inline-block;
    font-size: 14px;
    margin-left: 10px;
    line-height: 18px;
  }
}

.upgrade-effect {
  .form-item {
    flex: 1;
    & > div {
      flex: 1;
    }
  }

  .close-icon {
    display: inline-flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    color: #9baac1;
    cursor: pointer;
  }
}
</style>
<style lang="scss" scoped>
h4 {
  font-size: 14px;
  line-height: 14px;
  color: #292929;
  font-weight: bold;
  margin: 20px 0 10px 0;
}
</style>
