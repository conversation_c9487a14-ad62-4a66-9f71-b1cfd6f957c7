<template>
  <!-- 晋级 -->
  <mt-form ref="promotionRef" :model="promotionInfo" :rules="promotionRules">
    <h4>{{ $t('触发晋级条件') }}</h4>
    <mt-form-item
      class="form-item"
      :label="$t('任务完成大于等于')"
      label-style="top"
      prop="upgradeRuleFront"
    >
      <mt-inputNumber v-model="promotionInfo.upgradeRuleFront"></mt-inputNumber>
    </mt-form-item>
    <h4>{{ $t('晋级方式') }}</h4>
    <mt-row :gutter="10">
      <mt-col :xs="24" :md="6" :lg="6">
        <mt-form-item
          class="form-item"
          :label="$t('晋级方式')"
          label-style="top"
          prop="upgradeRuleType"
        >
          <mt-select
            v-model="promotionInfo.upgradeRuleType"
            :placeholder="$t('选择晋级方式')"
            float-label-type="Never"
            :data-source="upgradeRuleTypeList"
            @select="promotionInfo.upgradeWorkflowKey = null"
          ></mt-select>
        </mt-form-item>
      </mt-col>
      <mt-col v-if="promotionInfo.upgradeRuleType === 3" :xs="24" :md="18" :lg="18">
        <mt-form-item
          class="form-item"
          :label="$t('选择审批流')"
          label-style="top"
          prop="upgradeWorkflowKey"
        >
          <mt-select
            v-model="promotionInfo.upgradeWorkflowKey"
            :placeholder="$t('选择晋级对应的审批流')"
            float-label-type="Never"
            :data-source="workflowList"
            :fields="workflowKeyFields"
            @select="handlSltWorkflow"
          ></mt-select>
        </mt-form-item>
      </mt-col>
    </mt-row>
  </mt-form>
</template>

<script>
export default {
  props: {
    workflowList: {
      type: Array,
      default: () => []
    },
    dataSource: {
      type: Object,
      required: false,
      default: () => {}
    },
    isDefault: {
      type: Boolean,
      required: false,
      default: false
    },
    index: {
      type: Number,
      required: false,
      default: -1
    }
  },
  data() {
    // 任务完成数量要大于0，但不能大于配置的任务的数量
    var validateUpgradeRuleFront = (rule, value, callback) => {
      if (value === null || value < 1) {
        callback(new Error(this.$t('任务完成数需大于0')))
      } else if (this.isDefault || this.index > -1) {
        let taskList = this.isDefault
          ? this.$parent.getDefaultTaskConfig()
          : this.$parent.getTaskConfigByIndex(this.index)
        let len = taskList ? taskList.length : 0
        if (value > len) {
          callback(new Error(this.$t('任务完成数不能大于任务数')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      // 晋级
      promotionInfo: {
        upgradeRuleFront: 0, // 晋级前置条件，任务完成大于等于数
        upgradeRuleType: null, // 晋级方式，1:自动晋级，2:手动晋级，3:审批晋级
        upgradeWorkflowKey: null, // 晋级工作流程模板key
        upgradeWorkflowKeyName: null // 晋级工作流程模板名称
      },
      promotionRules: {
        upgradeRuleFront: [
          // { required: true, message: this.$t("请输入触发晋级条件"), trigger: "blur" },
          { validator: validateUpgradeRuleFront, trigger: 'blur' }
        ],
        upgradeRuleType: [
          {
            required: true,
            message: this.$t('请选择晋级方式'),
            trigger: 'blur'
          }
        ],
        upgradeWorkflowKey: [
          {
            required: true,
            message: this.$t('请选择晋级方式'),
            trigger: 'blur'
          }
        ]
      },
      // this.$t("晋级方式")
      upgradeRuleTypeList: [
        {
          text: this.$t('自动晋级'),
          value: 1
        },
        {
          text: this.$t('手动晋级'),
          value: 2
        }
        // {
        //   text: this.$t("审批晋级"),
        //   value: 3,
        // },
      ],
      // this.$t("审批")
      workflowKeyFields: {
        text: 'completionTypeName',
        value: 'completionTypeId'
      }
    }
  },
  watch: {
    dataSource(val) {
      if (val && Object.keys(val).length) {
        this.promotionInfo = Object.assign({}, this.promotionInfo, this.dataSource)
      }
    }
  },

  mounted() {
    if (this.dataSource && Object.keys(this.dataSource).length) {
      this.promotionInfo = Object.assign({}, this.promotionInfo, this.dataSource)
    }
  },
  methods: {
    validPromotion() {
      let validRs = true
      this.$refs.promotionRef.validate((valid) => {
        validRs = valid
      })
      return validRs
    },
    getCurrentPromotionConfig() {
      return this.promotionInfo
    },
    handlSltWorkflow(args) {
      const { itemData } = args
      if (itemData) {
        const { completionTypeName } = itemData
        this.$set(this.promotionInfo, 'upgradeWorkflowKeyName', completionTypeName)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
h4 {
  font-size: 14px;
  color: #292929;
  font-weight: bold;
  margin: 20px 0 10px 0;
}
</style>
