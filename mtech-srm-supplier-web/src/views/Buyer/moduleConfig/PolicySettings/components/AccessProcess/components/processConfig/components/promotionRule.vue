<template>
  <div class="promotion-box">
    <h4>{{ $t('晋级规则') }}</h4>
    <div class="promotion-item" v-for="(rule, index) in ruleTemplateList" :key="rule.id + index">
      <div class="task-title">
        <span>{{ $t('规则') }}{{ (index + 1) | taskOrder }}</span>
        <div class="icon-delete-box" @click="deleteCurrentTask(index)" v-if="index != 0">
          <i class="mt-icons mt-icon-icon_solid_delete_2 icon-delete"></i
          ><span class="delete-tips">{{ $t('删除规则') }}</span>
        </div>
      </div>

      <mt-form class="task-item-select" :ref="'taskRef' + index" :model="rule" :rules="ruleRules">
        <!-- 添加前置条件 晋级规则必填一条 -->
        <div
          class="pre-condition"
          v-for="(crule, cindex) in rule.ruleConditionDTOList"
          :key="crule.id"
        >
          <mt-form
            class="task-item-select"
            :ref="'ruleRef' + cindex"
            :model="crule"
            :rules="cruleRules"
          >
            <mt-row :gutter="10">
              <mt-col :xs="24" :md="3" :lg="3" v-if="cindex !== 0">
                <mt-form-item prop="conditionType">
                  <mt-select
                    v-model="crule.conditionType"
                    :data-source="rulesObj.ruleType"
                    :fields="fields"
                    @select="setInputName($event, index, cindex, 'conditionTypeName')"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :xs="24" :md="6" :lg="6" v-if="cindex !== 0">
                <mt-form-item prop="conditionObject">
                  <mt-select
                    :placeholder="$t('请选择对象值')"
                    v-model="crule.conditionObject"
                    :data-source="rulesObj.ruleConditionObject"
                    @select="setInputName($event, index, cindex, 'conditionObjectName')"
                    :fields="fields"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :xs="24" :md="9" :lg="9" v-if="cindex == 0">
                <mt-form-item prop="conditionObject">
                  <mt-select
                    :placeholder="$t('请选择对象值')"
                    v-model="crule.conditionObject"
                    :data-source="rulesObj.ruleConditionObject"
                    @select="setInputName($event, index, cindex, 'conditionObjectName')"
                    :fields="fields"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :xs="24" :md="7" :lg="7">
                <mt-form-item prop="conditionAttribute">
                  <mt-select
                    v-model="crule.conditionAttribute"
                    :data-source="rulesObj.ruleConditionAttribute"
                    :fields="fields"
                    :placeholder="$t('请选择属性值')"
                    @select="setInputName($event, index, cindex, 'conditionAttributeName')"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <!-- 联动的 -->
              <mt-col :xs="24" :md="3" :lg="3">
                <mt-form-item
                  prop="conditionSymbol"
                  v-if="crule.conditionAttribute != 'ruleConditionAttributeFinish'"
                >
                  <mt-select
                    v-model="crule.conditionSymbol"
                    :data-source="SymbolListOther"
                    :fields="fields"
                    @select="setInputName($event, index, cindex, 'conditionSymbolName')"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col
                :xs="24"
                :md="3"
                :lg="3"
                v-if="crule.conditionAttribute == 'ruleConditionAttributeFinish'"
              >
                <mt-form-item prop="conditionSymbol">
                  <mt-select
                    v-model="crule.conditionSymbol"
                    :data-source="rulesObj.ruleConditionSymbol"
                    :fields="fields"
                    @select="setInputName($event, index, cindex, 'conditionSymbolName')"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :xs="24" :md="4" :lg="4">
                <mt-form-item
                  prop="conditionTarget"
                  v-if="crule.conditionAttribute == 'ruleConditionAttributeStatus'"
                >
                  <mt-select
                    v-model="crule.conditionTarget"
                    :placeholder="$t('请选择')"
                    :data-source="rulesObj.effectValueList"
                    :fields="{
                      text: 'itemName',
                      value: 'itemCode'
                    }"
                    @select="setInputName($event, index, cindex, 'conditionTargetName')"
                  ></mt-select>
                </mt-form-item>
                <mt-form-item
                  prop="conditionTarget"
                  v-if="crule.conditionAttribute == 'ruleConditionAttributeArea'"
                >
                  <mt-select
                    v-model="crule.conditionTarget"
                    :placeholder="$t('请选择')"
                    :data-source="rulesObj.ruleConditionAttributeArea"
                    :fields="{
                      text: 'itemName',
                      value: 'itemCode'
                    }"
                    @select="setInputName($event, index, cindex, 'conditionTargetName')"
                  ></mt-select>
                </mt-form-item>
                <mt-form-item
                  prop="conditionTarget"
                  v-if="crule.conditionAttribute == 'ruleConditionAttributeSameEnterprise'"
                >
                  <mt-select
                    v-model="crule.conditionTarget"
                    :placeholder="$t('请选择')"
                    :data-source="rulesObj.ruleConditionAttributeSameEnterprise"
                    :fields="{
                      text: 'itemName',
                      value: 'itemCode'
                    }"
                    @select="setInputName($event, index, cindex, 'conditionTargetName')"
                  ></mt-select>
                </mt-form-item>
                <mt-form-item
                  prop="conditionTarget"
                  v-if="
                    crule.conditionAttribute != 'ruleConditionAttributeArea' &&
                    crule.conditionAttribute != 'ruleConditionAttributeStatus' &&
                    crule.conditionAttribute != 'ruleConditionAttributeSameEnterprise'
                  "
                >
                  <mt-input
                    v-model="crule.conditionTarget"
                    :placeholder="$t('请输入')"
                    @select="setInputName($event, index, cindex, 'conditionTargetName')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :xs="24" :md="1" :lg="1">
                <div class="close-icon" v-if="cindex !== 0" @click.stop="deleteRule(index, cindex)">
                  <i class="mt-icons mt-icon-icon_input_clear"></i>
                </div>
              </mt-col>
            </mt-row>
          </mt-form>
        </div>

        <div class="add-task-btn pre-btn" @click="addPrecondition(index)">
          <i class="mt-icons mt-icon-icon_card_plus"></i>
          <span>{{ $t('添加前置条件') }}</span>
        </div>
        <upgrade-effect
          :ref="'buyerStageEffectTemplateDTOList' + index"
          :is-edit="isEdit"
          :data-source="rule.buyerStageEffectTemplateDTOList"
          :effect-type-list="effectTypeList"
        ></upgrade-effect>
        <mt-row :gutter="10">
          <mt-col :xs="24" :md="24" :lg="24">
            <mt-form-item class="form-item" :label="$t('输出')" label-style="top" prop="ruleOutput">
              <mt-select
                v-model="rule.ruleOutput"
                :placeholder="$t('选择输出')"
                float-label-type="Never"
                :data-source="ruleOutputList"
                :fields="fields"
                @select="selectRules($event, index, 'ruleOutputName')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <!-- 只需要一条晋级规则 不需要按钮 -->
    <div class="add-task-btn" @click="addRules">
      <i class="mt-icons mt-icon-icon_card_plus"></i>
      <span>{{ $t('添加晋级规则') }}</span>
    </div>
  </div>
</template>

<script>
import upgradeEffect from './upgradeEffect.vue'
const DefaultTask = {
  ruleOutput: '',
  ruleConditionDTOList: []
}
let DefaultRule = {
  conditionType: 1, //	条件类型(0无、1且 、 2 或)
  conditionAttribute: '', // 属性值集
  conditionObject: '', // 对象值集
  conditionSymbol: 0, // 操作符值集(=,>,<等)
  conditionTarget: '' // 目标值
}
let INDEX = -1 // 添加规则的下标
let AttributeMap = new Map()
import { validateFormList, getRandom } from './index'
import utils from '@/utils/utils'
export default {
  filters: {
    taskOrder(val) {
      return utils.numIntToChinese(val)
    }
  },
  props: {
    dataSource: {
      type: Array,
      required: false,
      default: () => []
    },
    rulesObj: {
      type: Object,
      required: false,
      default: () => {}
    },
    isEdit: {
      type: Boolean,
      required: false,
      default: false
    },
    hasGetRules: {
      type: Boolean,
      required: false,
      default: false
    },
    rulesType: {
      type: String,
      required: false,
      default: ''
    },
    ruleOutputList: {
      type: Array,
      required: false,
      default: () => []
    }
  },
  components: {
    upgradeEffect
  },
  watch: {
    dataSource: {
      handler(newValue) {
        if (newValue.length > 0) {
          this.initData()
        }
      },
      immediate: true
    },
    rulesObj: {
      handler(newValue) {
        if (Object.keys(newValue).length > 0) {
          if (!this.isEdit) {
            this.addPrecondition(0)
          }
        }
      },
      immediate: true
    }
    // hasGetRules(nv) {
    //   if (nv && this.rulesType === "rule") {
    //     // 默认 赋值   编辑的时候 第一次新增不需要赋初始值
    //     if (!this.isEdit) {
    //       DefaultRule = {
    //         conditionType: this.rulesObj.ruleType[1].itemCode, //	条件类型(0无、1且 、 2 或)
    //         conditionTypeName: this.rulesObj.ruleType[1].itemName, //	条件类型(0无、1且 、 2 或)
    //         conditionAttribute:
    //           this.rulesObj.ruleConditionAttribute[0].itemCode || "", // 属性值集
    //         conditionAttributeName:
    //           this.rulesObj.ruleConditionAttribute[0].itemName || "", // 属性值集
    //         conditionObject:
    //           this.rulesObj.ruleConditionObject[0].itemCode || "", // 对象值集
    //         conditionObjectName:
    //           this.rulesObj.ruleConditionObject[0].itemName || "", // 对象值集
    //         conditionSymbol:
    //           this.rulesObj.ruleConditionSymbol[4].itemCode || "", // 操作符值集(=,>,<等)
    //         conditionSymbolName:
    //           this.rulesObj.ruleConditionSymbol[4].itemName || "", // 操作符值集(=,>,<等)
    //         conditionTarget: "", // 目标值
    //         conditionTargetName: "", // 目标值
    //       };
    //       this.setInputValue();
    //     }
    //   }
    // },
  },
  data() {
    return {
      effectTypeList: [],
      defaultUpgradeEffect: [], // 默认晋级效果
      // 配置任务列表
      ruleTemplateList: [
        JSON.parse(
          JSON.stringify({
            id: getRandom(),
            ...DefaultTask
          })
        )
      ],
      fields: {
        text: 'itemName',
        value: 'itemCode'
      },
      SymbolListOther: [
        {
          itemCode: '5',
          itemName: '='
        },
        {
          itemCode: '6',
          itemName: '≠'
        }
      ],
      ruleRules: {
        ruleOutput: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }] //	条件类型(0无、1且 、 2 或)
      },
      cruleRules: {
        conditionType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], //	条件类型(0无、1且 、 2 或)
        conditionAttribute: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], // 属性值集
        conditionObject: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], // 对象值集
        conditionSymbol: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], // 操作符值集(=,>,<等)
        conditionTarget: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }] // 目标值
      },
      conditionObjectList: [],
      isGetRulesConfig: false,
      buyerStageEffectTemplateDTOListResult: []
    }
  },
  mounted() {
    // 晋级规则必填一条 默认打开一条
    this.queryEffectType()
  },
  methods: {
    // 初始化编辑的情况 传入详情数据
    initData() {
      if (this.dataSource.length > 0) {
        this.ruleTemplateList = JSON.parse(JSON.stringify(this.dataSource))
      }
    },
    validateRulesConfig(index) {
      return validateFormList(this.ruleTemplateList[index]['ruleConditionDTOList'], 'ruleRef', this)
    },
    validateRulesConfigAll() {
      let buyerStageEffectTemplateDTOListRuleResult = []
      let ruleRefResult = []
      this.buyerStageEffectTemplateDTOListResult = []
      this.ruleTemplateList.map((item, index) => {
        buyerStageEffectTemplateDTOListRuleResult.push(
          this.$refs['buyerStageEffectTemplateDTOList' + index][0].validateTaskConfig()
        )
        ruleRefResult.push(this.validateRulesConfig(index))
        this.buyerStageEffectTemplateDTOListResult.push(
          this.$refs['buyerStageEffectTemplateDTOList' + index][0].getCurrentEffectData()
        )
      })
      let buyerStageEffectTemplateDTOListRuleResultB =
        buyerStageEffectTemplateDTOListRuleResult.some((item) => {
          return item == true
        })
      let ruleRefResultB = ruleRefResult.some((item) => {
        return item == true
      })
      if (!buyerStageEffectTemplateDTOListRuleResultB) {
        buyerStageEffectTemplateDTOListRuleResult = []
        this.buyerStageEffectTemplateDTOListResult = []
      }
      if (!ruleRefResultB) {
        ruleRefResultB = []
      }
      let taskRefB = validateFormList(this.ruleTemplateList, 'taskRef', this)
      if (!ruleRefResultB || !buyerStageEffectTemplateDTOListRuleResultB || !taskRefB) {
        this.$toast({
          content: this.$t('请填写齐全晋级规则阶段所有晋级规则的前置条件和晋级效果和输出！'),
          type: 'error'
        })
        return
      }
      return ruleRefResultB && buyerStageEffectTemplateDTOListRuleResultB && taskRefB
    },

    // 新增预设条件
    addPrecondition(index) {
      INDEX = index
      if (!this.hasGetRules) {
        // 获取条件里面所有的枚举
        this.$emit('getRules')
      } else {
        let ruleConditionObjectTask = this.rulesObj.ruleConditionObject.filter((item) => {
          return item.itemCode == 'ruleConditionObjectTask'
        })
        let ruleConditionAttribute = this.rulesObj.ruleConditionAttribute.filter((item) => {
          return item.itemCode == 'ruleConditionAttributeFinish'
        })
        DefaultRule = {
          conditionType: this.rulesObj.ruleType[1].itemCode, //	条件类型(0无、1且 、 2 或)
          conditionTypeName: this.rulesObj.ruleType[1].itemName, //	条件类型(0无、1且 、 2 或)
          conditionAttribute:
            ruleConditionAttribute.length > 0
              ? ruleConditionAttribute[0].itemCode
              : this.rulesObj.ruleConditionAttribute[0].itemCode || '', // 属性值集
          conditionAttributeName:
            ruleConditionAttribute.length > 0
              ? ruleConditionAttribute[0].itemName
              : this.rulesObj.ruleConditionAttribute[0].itemName || '', // 属性值集
          conditionObject:
            ruleConditionObjectTask.length > 0
              ? ruleConditionObjectTask[0].itemCode
              : this.rulesObj.ruleConditionObject[0].itemCode || '', // 对象值集
          conditionObjectName:
            ruleConditionObjectTask.length > 0
              ? ruleConditionObjectTask[0].itemName
              : this.rulesObj.ruleConditionObject[0].itemName || '', // 对象值集
          conditionSymbol: this.rulesObj.ruleConditionSymbol[4].itemCode || '', // 操作符值集(=,>,<等)
          conditionSymbolName: this.rulesObj.ruleConditionSymbol[4].itemName || '', // 操作符值集(=,>,<等)
          conditionTarget: '', // 目标值
          conditionTargetName: '' // 目标值
        }
        this.setInputValue()
      }
    },

    setInputValue() {
      //校验规则
      let rs = true
      if (this.ruleTemplateList[INDEX]['ruleConditionDTOList'].length != 0) {
        rs = this.validateRulesConfig(INDEX)
      }

      if (rs) {
        this.ruleTemplateList[INDEX]['ruleConditionDTOList'].push(
          JSON.parse(
            JSON.stringify({
              id: getRandom(),
              ...DefaultRule
            })
          )
        )
      }
    },

    deleteRule(index, cindex) {
      if (
        !this.ruleTemplateList[index] ||
        !this.ruleTemplateList[index]['ruleConditionDTOList'] ||
        this.ruleTemplateList[index]['ruleConditionDTOList'].length <= 1
      )
        return
      this.ruleTemplateList[index]['ruleConditionDTOList'].splice(cindex, 1)
    },

    validateTaskConfig() {
      let ruleResult = this.ruleTemplateList.map((item, index) => {
        return this.$refs['buyerStageEffectTemplateDTOList' + index][0].validateTaskConfig()
      })
      console.log(ruleResult)
      let ruleResultB = ruleResult.some((item) => {
        return item == true
      })
      let taskRefB = validateFormList(this.ruleTemplateList, 'taskRef', this)
      return ruleResultB && taskRefB
    },

    deleteCurrentTask(index) {
      this.ruleTemplateList.splice(index, 1)
    },

    addRules() {
      let rs = this.validateTaskConfig()
      if (rs) {
        this.ruleTemplateList.push(
          JSON.parse(
            JSON.stringify({
              id: getRandom(),
              ...DefaultTask
            })
          )
        )
        let index = this.ruleTemplateList.length - 1
        let ruleConditionObjectTask = this.rulesObj.ruleConditionObject.filter((item) => {
          return item.itemCode == 'ruleConditionObjectTask'
        })
        let ruleConditionAttribute = this.rulesObj.ruleConditionAttribute.filter((item) => {
          return item.itemCode == 'ruleConditionAttributeFinish'
        })
        DefaultRule = {
          conditionType: this.rulesObj.ruleType[1].itemCode, //	条件类型(0无、1且 、 2 或)
          conditionTypeName: this.rulesObj.ruleType[1].itemName, //	条件类型(0无、1且 、 2 或)
          conditionAttribute:
            ruleConditionAttribute.length > 0
              ? ruleConditionAttribute[0].itemCode
              : this.rulesObj.ruleConditionAttribute[0].itemCode || '', // 属性值集
          conditionAttributeName:
            ruleConditionAttribute.length > 0
              ? ruleConditionAttribute[0].itemName
              : this.rulesObj.ruleConditionAttribute[0].itemName || '', // 属性值集
          conditionObject:
            ruleConditionObjectTask.length > 0
              ? ruleConditionObjectTask[0].itemCode
              : this.rulesObj.ruleConditionObject[0].itemCode || '', // 对象值集
          conditionObjectName:
            ruleConditionObjectTask.length > 0
              ? ruleConditionObjectTask[0].itemName
              : this.rulesObj.ruleConditionObject[0].itemName || '', // 对象值集
          conditionSymbol: this.rulesObj.ruleConditionSymbol[4].itemCode || '', // 操作符值集(=,>,<等)
          conditionSymbolName: this.rulesObj.ruleConditionSymbol[4].itemName || '', // 操作符值集(=,>,<等)
          conditionTarget: '', // 目标值
          conditionTargetName: '' // 目标值
        }
        this.ruleTemplateList[index]['ruleConditionDTOList'].push(
          JSON.parse(
            JSON.stringify({
              id: getRandom(),
              ...DefaultRule
            })
          )
        )
      }
    },
    selectRules(e, index, attribute) {
      const { itemData } = e
      const { itemName } = itemData
      this.$set(this.ruleTemplateList[index], [attribute], itemName)
    },
    setInputName(e, index, cIndex, attribute) {
      const { itemData } = e
      const { itemName, itemCode } = itemData
      // TODO: 选择完成数的时候，清空target值
      // 只更新变化
      if (attribute == 'conditionObjectName') {
        this.$set(
          this.ruleTemplateList[index]['ruleConditionDTOList'][cIndex],
          [attribute],
          itemName
        )
      }
      if (attribute == 'conditionTypeName') {
        for (let i = 0; i < this.ruleTemplateList[index]['ruleConditionDTOList'].length; i++) {
          this.$set(
            this.ruleTemplateList[index]['ruleConditionDTOList'][i],
            'conditionType',
            itemCode
          )
          this.$set(this.ruleTemplateList[index]['ruleConditionDTOList'][i], [attribute], itemName)
        }
      }
      this.$set(this.ruleTemplateList[index]['ruleConditionDTOList'][cIndex], [attribute], itemName)
    },
    // 条件的对象 => 属性的联动
    conditionAttributeSelect(e, index, cindex) {
      const { itemData } = e
      this.setInputName(e, index, cindex, 'conditionObjectName')
      this.setAttributeList(itemData.itemCode, index, cindex)
    },

    setAttributeList(itemCode, index, cindex) {
      const dictCode = 'ruleConditionAttribute' // itemCode ||
      // 缓存
      if (AttributeMap.has(dictCode)) {
        let data = AttributeMap.get(dictCode)
        this.$set(this.ruleTemplateList[index][cindex], 'conditionAttribute ', data)
        return
      }
      this.$loading()
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        this.$hloading()
        const { code, data } = res
        if (code == 200 && data) {
          AttributeMap.set(dictCode, data)
          this.$set(this.ruleTemplateList[index][cindex], 'conditionAttribute', data)
        } else {
          this.$set(this.ruleTemplateList[index][cindex], 'conditionAttribute', [])
        }
      })
    },

    // 获取当前的配置列表，父级可在提交表单时调用
    getCurrentPromotionConfig() {
      // let { ruleOutput, ruleOutputName, ruleConditionDTOList } =
      //   this.ruleTemplateList[0];
      // ruleConditionDTOList = ruleConditionDTOList.map((item) => {
      //   let tmpItem = JSON.parse(JSON.stringify(item));
      //   if (this.isEdit) {
      //     return tmpItem;
      //   } else {
      //     delete tmpItem.id;
      //     return tmpItem;
      //   }
      // });
      let result = []
      this.ruleTemplateList.map((item, index) => {
        let tempRuleConditionDTOList = item.ruleConditionDTOList.map((item) => {
          let tmpItem = JSON.parse(JSON.stringify(item))
          if (this.isEdit) {
            return tmpItem
          } else {
            delete tmpItem.id
            return tmpItem
          }
        })
        result.push({
          buyerStageEffectTemplateDTOList: this.buyerStageEffectTemplateDTOListResult[index],
          ruleConditionDTOList: tempRuleConditionDTOList,
          ruleOutput: item.ruleOutput,
          ruleOutputName: item.ruleOutputName,
          ruleType: item.ruleType,
          ruleTypeName: item.ruleTypeName,
          status: item.status,
          id: item.id
        })
      })
      return result
    },
    // 晋级效果类型
    queryEffectType() {
      const dictCode = 'effectType'
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.effectTypeList = data
        }
      })
    }
  }
}
</script>

<style lang="scss">
.mt-select {
  width: 100%;
}
</style>
<style lang="scss" scoped>
h4 {
  font-size: 14px;
  color: #292929;
  font-weight: bold;
  margin: 20px 0 10px 0;
}
.promotion-box {
  .promotion-item {
    background: #f5f6f9;
    padding: 16px;
    margin-bottom: 10px;
  }

  .close-icon {
    display: inline-flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    color: #9baac1;
    cursor: pointer;
  }
  .task-title {
    display: flex;
    margin-bottom: 20px;
    > span {
      font-size: 14px;
      font-weight: bold;
      color: #292929;
      flex: 1;
    }
    .icon-delete-box {
      color: #ff4949;
      font-weight: bold;
      cursor: pointer;
      .delete-tips {
        font-size: 14px;
        margin-left: 10px;
      }
    }
  }
}
</style>
