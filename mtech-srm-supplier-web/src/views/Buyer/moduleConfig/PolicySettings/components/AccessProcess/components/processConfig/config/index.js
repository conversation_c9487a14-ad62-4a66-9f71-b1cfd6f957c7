import { i18n } from '@/main.js'
export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Bottom'
}
export const editSettings4Display = {
  allowEditing: false,
  allowAdding: false,
  allowDeleting: false,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Bottom'
}
export const editSettingsBatch = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Batch', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Bottom'
}

import selectedOrder from '../components/selectedOrder.vue'
import selectedOrder1 from '../components/selectedOrder1.vue'

import Vue from 'vue'
import bus from '@/utils/bus'

export const toolbarOptions = [
  {
    text: i18n.t('新增场景'),
    prefixIcon: '',
    id: 'AddBtn'
  },
  {
    text: i18n.t('删除'),
    prefixIcon: '',
    id: 'Deletebtn'
  }
]

export const query2Local = {
  0: 2,
  1: 1,
  2: 3
}

// let operetorList = [
//   { value: 1, text: i18n.t('必选') },
//   { value: 2, text: i18n.t('可选') },
//   { value: 3, text: i18n.t('不可选') }
// ]

export let scenFieldObj = {}

export const addHeader = (columnData, sceneNameList, fieldName, colunmId = '') => {
  if (fieldName === 'editField2') {
    scenFieldObj = {}
  }
  // 非第一行都不能编辑
  // columnData = columnData.map((v) => (v.allowEditing = false));
  return [
    ...columnData,
    {
      width: '150',
      field: fieldName,
      headerText: '',
      allowEditing: true,
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
            <div class="headers">
              <div class="e-headertext" style="display: flex">
                <span style="position:absolute;z-index:-1;">{{displayFieldName}}</span>
                <mt-select
                  v-model="fieldName"
                  v-if="!isResetSelect"
                  :disabled="!!fieldName"
                  :dataSource="sceneNameList"
                  :showClearButton="false"
                  @select="handleChangeStage"
                  :placeholder="i18ntitle"
                ></mt-select>
                <mt-checkbox
                  :checked="isChecked"
                  class="category-checkbox"
                  @change="
                    handleCheckbox($event)
                  "
                ></mt-checkbox>
                </div>
            </div>
          `,
            data() {
              return {
                data: {},
                fieldName: '',
                isChecked: false,
                isResetSelect: false
              }
            },
            computed: {
              i18ntitle() {
                return i18n.t('请选择场景名称')
              },
              sceneNameList() {
                return sceneNameList
              },
              fieldNames() {
                return fieldName
              },
              displayFieldName() {
                const _find = this.sceneNameList.find((item) => item.value === this.fieldName)
                return _find?.text
              }
            },
            methods: {
              handleChangeStage(args) {
                console.log('change', args)
                const { itemData } = args
                let selectSceneList = Object.values(scenFieldObj)
                if (selectSceneList.indexOf(itemData.value) >= 0) {
                  // this.$toast({
                  //   content: "场景名称不能重复!",
                  //   type: "warning",
                  // });
                }
                bus.$emit('getScene', itemData, fieldName)
                scenFieldObj[fieldName] = itemData.value
              },
              handleCheckbox(event) {
                const { checked } = event
                bus.$emit(
                  'getChecked',
                  {
                    checked,
                    id: this.fieldName,
                    num: columnData.length + 1,
                    fieldNames: this.fieldNames
                  },
                  {
                    [this.fieldName]: checked
                  }
                )
              }
            },
            mounted() {
              // colunmId 场景id为真的话 是已有的编辑的
              if (colunmId) {
                scenFieldObj[fieldName] = colunmId
              }
              this.$nextTick(() => {
                this.fieldName = scenFieldObj[fieldName] ? scenFieldObj[fieldName] : ''
              })
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: selectedOrder
        }
      },
      // editType: "dropdownedit",
      // edit: {
      //   params: {
      //     dataSource: operetorList,
      //     fields: { value: "value", text: "text" },
      //     query: new Query(),
      //     change: (e) => {
      //       console.log("我是下拉的change事件", e);
      //       let { itemData } = e;
      //       console.log(this)
      //       bus.$emit("setDropDown", {
      //         itemData,
      //         fieldName,
      //       });
      //     },
      //   },
      // },
      template: () => {
        return {
          template: Vue.component('iconCell', {
            template: `
              <div class="icon-box">
                <div v-if="data[fieldName] === 1" class="circle"></div>
                <div v-if="data[fieldName] === 2" class="trangle"></div>
                <div v-if="data[fieldName] === 3" class="cance">
                <i class="mt-icons mt-icon-icon_X"></i>
                </div>
              </div>
            `,
            data() {
              return {
                data: {}
              }
            },
            computed: {
              fieldName() {
                return fieldName
              }
            }
          })
        }
      }
    }
  ]
}

let operetorList2 = [
  { value: 1, text: i18n.t('必选') },
  { value: 0, text: i18n.t('可选') },
  { value: 2, text: i18n.t('不可选') }
]

export const judgeToast = (itemData, previousItemData, _this) => {
  let pass = true
  let { value } = previousItemData
  // 阶段可选，阶段下的任务可选或不可选
  if (value === 0) {
    if (itemData.value === 1) {
      pass = false
      _this.$toast({
        content: i18n.t('阶段可选，阶段下的任务只能是可选或不可选!'),
        type: 'warning'
      })
    }
  }
  // 阶段必选，阶段下的任务必选、可选或不可选
  // if (value === 1) {}
  // 阶段不可选，阶段下的任务都不可选
  if (value === 2) {
    if (itemData.value !== 2) {
      pass = false
      _this.$toast({
        content: i18n.t('阶段不可选，阶段下的任务都不可选!'),
        type: 'warning'
      })
    }
  }

  return pass
}

export const bodyEdit = (item) => {
  return {
    width: '150',
    field: 'status' + item.sceneDefineId,
    headerText: '',
    allowEditing: true,
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
          <div class="headers">
            {{sceneName}}
          </div>
        `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            sceneName() {
              return item[`sceneName${item.sceneDefineId}`]
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: selectedOrder1
      }
    },
    // editType: "dropdownedit",
    // edit: {
    //   params: {
    //     dataSource: operetorList2,
    //     fields: { value: "value", text: "text" },
    //     query: new Query(),
    //     change: (e) => {
    //       console.log("我是下拉的change事件", e);

    //     },
    //   },
    // },
    template: () => {
      return {
        template: Vue.component('iconCell', {
          template: `
              <div class="icon-box">
                <div v-if="data['status' + sceneDefineId] === 1" class="circle"></div>
                <div v-if="data['status' + sceneDefineId] === 0" class="trangle"></div>
                <div v-if="data['status' + sceneDefineId] === 2" class="cance">
                <i class="mt-icons mt-icon-icon_X"></i>
                </div>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            sceneDefineId() {
              return item.sceneDefineId
            }
          },
          created() {},
          mounted() {}
        })
      }
    }
  }
}

export const bodynoEdit = (item) => {
  return {
    width: '150',
    field: 'statusold' + item.sceneDefineId,
    headerText: '',
    allowEditing: false,
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
          <div class="headers">
            {{sceneName}}
          </div>
        `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            sceneName() {
              return item[`oldsceneName${item.sceneDefineId}`]
            }
          }
        })
      }
    },
    editType: 'dropdownedit',
    edit: {
      params: {
        dataSource: operetorList2,
        fields: { value: 'value', text: 'text' },
        // enabled: false,
        query: new Query(),
        change: (e) => {
          console.log('我是下拉的change事件', e)
          // let { itemData, previousItemData } = e;
          // judgeToast(itemData, previousItemData, _this);
          // bus.$emit("setDropDown2", { itemData, item });
        }
      }
    },
    template: () => {
      return {
        template: Vue.component('iconCell', {
          template: `
              <div class="icon-box">
                <div v-if="data['statusold' + sceneDefineId] === 1" class="circle"></div>
                <div v-if="data['statusold' + sceneDefineId] === 0" class="trangle"></div>
                <div v-if="data['statusold' + sceneDefineId] === 2" class="cance">
                <i class="mt-icons mt-icon-icon_X"></i>
                </div>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            sceneDefineId() {
              return item.sceneDefineId
            }
          },
          created() {},
          mounted() {}
        })
      }
    }
  }
}

//arr为传入函数的目标数组，根据传入的key重组数组
export const filterData = (key, arr) => {
  let map = {},
    dest = []
  for (var i = 0; i < arr.length; i++) {
    var ai = arr[i]
    // 区分同行不同的场景 s
    ai[`sceneName${ai.sceneDefineId}`] = ai.sceneName
    ai[`sceneNameId${ai.sceneDefineId}`] = ai.sceneDefineId
    ai[`detailId${ai.sceneDefineId}`] = ai.id
    // 区分同行不同的场景 s
    if (!map[ai[key]]) {
      dest.push({
        [key]: ai[key],
        data: [ai]
      })
      map[ai[key]] = ai
    } else {
      for (var j = 0; j < dest.length; j++) {
        var dj = dest[j]
        if (dj[key] == ai[key]) {
          dj.data.push(ai)
          break
        }
      }
    }
  }
  return dest
}

// 对比找到同一行dataSource
export const forCheckAllInArray = (keyArray, leftItem, rightItem) => {
  let isEquel = true
  let keyLength = keyArray.length
  for (let i = 0; i < keyLength; i++) {
    if (leftItem[keyArray[i]] === rightItem[keyArray[i]]) {
      continue
    } else {
      isEquel = false
      break
    }
  }
  return isEquel
}

export const fiterSimilarRow = (key, list) => {
  let map = {}
  let dist = []
  for (let i of list) {
    let data = i.data
    if (data && data.length > 0) {
      let len = data.length
      let cData = []
      for (let i = 0; i < len; i++) {
        let item = data[i]
        cData = i === 0 ? [] : cData
        if (!map[item[key]]) {
          map[item[key]] = item[key]
          cData.push(item)
        } else {
          let index = cData.findIndex((citem) => citem[key] === item[key])
          index = index > -1 ? index : 0
          cData[index] = {
            ...cData[index],
            ...item
          }
        }
        if (i === len - 1) {
          cData[0].rowSpan = cData.length
          dist = [].concat(dist, cData)
        }
      }
    }
  }
  return dist
}

export const headerTemplate = (selectData = []) => {
  return {
    template: Vue.component('requiredCell', {
      template: `
                  <div class="headers">
                    <div class="e-headertext">
                    <mt-select
                        v-model="fieldName"
                        :width="400"
                        :show-clear-button="true"
                        :dataSource="selectData"
                        :showClearButton="true"
                        @change="handleChangeStage"
                        :placeholder="$t('请输入场景名称')"
                      ></mt-select>
                    </idv>
                    <mt-checkbox
                    :checked="isChecked"
                    @change="changeCheckbox"
                  ></mt-checkbox>
                  </div>
                `,
      data() {
        return {
          data: {},
          fieldName: '',
          isChecked: false
        }
      },
      computed: {
        selectData() {
          return selectData
        }
      },
      methods: {
        handleChangeStage(args) {
          console.log(args)
        },
        changeCheckbox(event) {
          console.log(event)
        }
      },
      mounted() {}
    })
  }
}

export const dataSource = []

import { TextBox, NumericTextBox } from '@syncfusion/ej2-inputs'
import { Query } from '@syncfusion/ej2-data'
import cellChanged from './cellChanged.vue'

/**
 * 单元格需要被改变时，可以通过xxxObj.value=‘’。。。或者通过bus监听（cellChanged组件）
 * 多个需要写xxxObj时，可以通用commonEdit配置
 * 如果不能用ej2自带格式的，需要手写template/editTemplate
 */

export const checkCol = [
  {
    width: '0',
    field: 'otherData',
    headerText: i18n.t('额外数据'),
    // visible: false, // 设置成不显示就会导致赋值不上，因为没有这个元素了
    allowEditing: false,
    editTemplate: function () {
      return {
        template: cellChanged
      }
    }
  }
]

// 因为xxxEle，xxxObj需要配置全局，可以在此批量生成。配置这个为了能实时 得到/改变 该单元格的数据
const numberFields = ['normalNumber1', 'normalNumber2', 'resNumber']
var wholeObj = {}
numberFields.forEach((item) => {
  wholeObj[`${item}Ele`] = null
  wholeObj[`${item}Obj`] = null
})

var commonEdit = (params) => {
  // type:单元格的类型 等于ej2包含的类型，默认字符串类型
  // field: 列的field，必填
  // pld：placeholder，可不填
  // canEdit：是否可以编辑，默认可以
  // required: 是否必填，默认非必填。。必填时给cssClass赋值isRequired
  // callback: 回调（按照业务逻辑来）
  // calc：计算参数，仅举例（按照业务逻辑来）
  let { type = 'stringedit', field, pld, canEdit = true, required = false, calc } = params
  return {
    create: () => {
      wholeObj[`${field}Ele`] = document.createElement('input')
      return wholeObj[`${field}Ele`]
    },
    read: () => {
      return wholeObj[`${field}Obj`].value
    },
    destroy: () => {
      wholeObj[`${field}Obj`].destroy()
    },
    write: (args) => {
      console.log(args)
      switch (type) {
        case 'stringedit':
          wholeObj[`${field}Obj`] = new TextBox({
            enabled: canEdit,
            cssClass: required ? 'isRequired' : null,
            value: args.rowData[args.column.field],
            placeholder: pld,
            floatLabelType: 'Never',
            change: () => {
              // console.log(i18n.t("我改变了"), calc, e, wholeObj);
            }
          })
          break
        case 'numericedit':
          wholeObj[`${field}Obj`] = new NumericTextBox({
            enabled: canEdit,
            cssClass: required ? 'isRequired' : null,
            value: args.rowData[args.column.field],
            placeholder: pld,
            floatLabelType: 'Never',
            change: (e) => {
              // console.log(i18n.t("我改变了"), calc, e, wholeObj);
              if (calc && calc.length) {
                // 以简单乘法计算为例
                if (wholeObj[`${calc[0]}Obj`] && wholeObj[`${calc[1]}Obj`]) {
                  wholeObj[`${calc[1]}Obj`].value = wholeObj[`${calc[0]}Obj`].value * e.value
                }
              }
            }
          })
          break
        default:
          break
      }
      wholeObj[`${field}Obj`].appendTo(wholeObj[`${field}Ele`])
    }
  }
}

// ToDo
// 默认值、校验、级联
var beDropedEle, beDropedObj
export const columnData = (params, that) => {
  return [
    {
      width: '150',
      field: 'visiIpt',
      headerText: i18n.t('不可修改'),
      allowEditing: false,
      defaultValue: i18n.t('我是默认值'),
      formatter: (column, data) => {
        return `${data[column.field]}^^^`
      },
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">123</span>
                          <mt-checkbox
                            v-model="fieldName"
                        ></mt-checkbox>
                        </div>
                      `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {}
          })
        }
      }
    },
    {
      width: '150',
      field: 'normalIpt',
      headerText: i18n.t('普通输入框'),
      editType: 'stringedit'
    },
    {
      width: '150',
      field: 'normalNumber',
      headerText: i18n.t('数字'),
      editType: 'numericedit',
      format: 'c2' // n2-默认；p2-百分比；c2-金额$
    },
    {
      width: '150',
      field: 'currency',
      headerText: i18n.t('下拉框'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: params.currencyData,
          fields: { value: 'currencyName', text: 'currencyName' },
          query: new Query(),
          actionComplete: () => false,
          change: (e) => {
            console.log('我是下拉的change事件', e)
          }
        }
      }
    },
    {
      width: '150',
      field: 'normalCheck',
      headerText: i18n.t('复选框'),
      editType: 'booleanedit',
      // 修改显示
      valueAccessor: function (field, data) {
        return data[field] ? i18n.t('成功') : i18n.t('失败')
      }
    },
    {
      width: '150',
      field: 'iptRequired',
      headerText: i18n.t('我是必填'),
      validationRules: {
        required: true,
        minLength: 3
      },
      edit: commonEdit({
        field: 'iptRequired',
        pld: i18n.t('请输入必填项'),
        required: true
      })
    },
    {
      width: '150',
      field: 'normalNumber1',
      headerText: '数字1',
      editType: 'numericedit',
      edit: commonEdit({
        type: 'numericedit',
        field: 'normalNumber1',
        pld: '数字1请输入',
        calc: ['normalNumber2', 'resNumber']
      })
    },
    {
      width: '150',
      field: 'normalNumber2',
      headerText: '数字2',
      editType: 'numericedit',
      edit: commonEdit({
        type: 'numericedit',
        field: 'normalNumber2',
        pld: '数字2请输入',
        calc: ['normalNumber1', 'resNumber']
      })
    },
    {
      width: '150',
      field: 'resNumber',
      headerText: i18n.t('乘积'),
      allowEditing: false,
      editType: 'numericedit',
      edit: commonEdit({
        field: 'resNumber',
        canEdit: false,
        pld: '=数字1*数字2'
      })
    },
    {
      width: '150',
      field: 'currency1',
      headerText: i18n.t('下拉框'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: params.currencyData,
          fields: { value: 'currencyName', text: 'currencyName' },
          query: new Query(),
          cssClass: 'isRequired',
          actionComplete: () => false,
          change: (e) => {
            console.log('我是下拉的change事件', e)
            beDropedObj.value = e.itemData.code

            // 改变其他、设置其他的额外值  field: value
            let changeFields = {
              changed1: `changed1-${e.itemData.currencyName}`,
              changed2: `changed2-${e.itemData.currencyName}`,
              otherData: {
                currencyId: e.itemData.id,
                code: e.itemData.code
              }
            }

            for (let i in changeFields) {
              that.$bus.$emit(`${i}Change`, changeFields[i])
            }
          }
        }
      },
      validationRules: {
        required: true
      }
    },
    {
      width: '150',
      field: 'beDroped',
      headerText: i18n.t('被下拉改变的'), // 简单的被改变
      allowEditing: false,
      edit: {
        // params: {
        //   value: beDropedVal, // 直接用个变量不行
        // },
        create: () => {
          beDropedEle = document.createElement('input')
          return beDropedEle
        },
        read: () => {
          return beDropedObj.value
        },
        destroy: () => {
          beDropedObj.destroy()
        },
        write: (args) => {
          beDropedObj = new TextBox({
            value: args.rowData[args.column.field],
            readonly: true,
            enabled: false
          })
          beDropedObj.appendTo(beDropedEle)
        }
      }
    },
    {
      width: '150',
      field: 'changed1',
      headerText: '被改变1',
      allowEditing: false,
      editTemplate: function () {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '150',
      field: 'changed2',
      headerText: '被改变2',
      allowEditing: false,
      editTemplate: function () {
        return {
          template: cellChanged
        }
      }
    }
  ]
}
