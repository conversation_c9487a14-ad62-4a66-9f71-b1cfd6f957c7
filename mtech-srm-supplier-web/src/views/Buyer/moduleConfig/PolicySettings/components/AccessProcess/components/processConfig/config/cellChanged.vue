<template>
  <div class="cell-changed">
    <mt-input :id="data.column.field" :value="data[data.column.field]" disabled></mt-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      field: ''
    }
  },
  mounted() {
    this.field = this.data.column.field
    // 监听被变化
    this.$bus.$off(`${this.field}Change`)
    this.$bus.$on(`${this.field}Change`, (e) => {
      console.log(this.$t('我监听到了变化了'), e, this.data)
      if (this.field != 'otherData') {
        this.$set(this.data, this.field, e)
      } else {
        let originData = this.data[this.field]
        originData = JSON.parse(originData)
        originData = {
          ...originData,
          ...e
        }
        this.$set(this.data, this.field, JSON.stringify(originData))
      }
    })
  }
}
</script>

<style></style>
