<template>
  <!-- 动态配置任务 -->
  <div>
    <h4>{{ $t('任务配置') }}</h4>
    <div v-for="(task, index) in taskTemplateList" :key="task.id" class="task-item">
      <div class="task-title">
        <span>{{ $t('任务') }}{{ (index + 1) | taskOrder }}</span>
        <div class="icon-delete-box" @click="deleteCurrentTask(index)" v-if="index != 0">
          <i class="mt-icons mt-icon-icon_solid_delete_2 icon-delete"></i
          ><span class="delete-tips">{{ $t('删除任务') }}</span>
        </div>
      </div>
      <!-- 调查表 有填写方 校验填写方 -->
      <mt-form
        class="task-item-select"
        :ref="'taskRef' + index"
        :model="task"
        :rules="task.taskTemplateClassify === 'surveyTempType' ? taskRulesAll : taskRules"
      >
        <!-- 添加前置条件 -->
        <template
          v-if="
            !!task.buyerRuleDTO &&
            !!task.buyerRuleDTO.ruleConditionDTOList &&
            task.buyerRuleDTO.ruleConditionDTOList.length > 0
          "
        >
          <div
            class="pre-condition"
            v-for="(rule, cindex) in task.buyerRuleDTO.ruleConditionDTOList"
            :key="rule.id"
          >
            <mt-form
              class="task-item-select"
              :ref="'ruleRef' + cindex"
              :model="rule"
              :rules="ruleRules"
            >
              <mt-row :gutter="10">
                <mt-col :xs="24" :md="3" :lg="3" v-if="cindex !== 0">
                  <mt-form-item prop="conditionType">
                    <mt-select
                      v-model="rule.conditionType"
                      :data-source="rulesObj.ruleType"
                      :fields="fields"
                      @select="setInputName($event, index, cindex, 'conditionTypeName')"
                    ></mt-select>
                  </mt-form-item>
                </mt-col>
                <mt-col :xs="24" :md="6" :lg="6" v-if="cindex !== 0">
                  <mt-form-item prop="conditionObject">
                    <!--  -->
                    <mt-select
                      :placeholder="$t('请选择对象值')"
                      v-model="rule.conditionObject"
                      :data-source="rulesObj.ruleConditionObject"
                      @select="setInputName($event, index, cindex, 'conditionObjectName')"
                      :fields="fields"
                    ></mt-select>
                  </mt-form-item>
                </mt-col>
                <mt-col :xs="24" :md="9" :lg="9" v-if="cindex == 0">
                  <mt-form-item prop="conditionObject">
                    <!--  -->
                    <mt-select
                      :placeholder="$t('请选择对象值')"
                      v-model="rule.conditionObject"
                      :data-source="rulesObj.ruleConditionObject"
                      @select="setInputName($event, index, cindex, 'conditionObjectName')"
                      :fields="fields"
                    ></mt-select>
                  </mt-form-item>
                </mt-col>
                <mt-col :xs="24" :md="7" :lg="7">
                  <mt-form-item prop="conditionAttribute">
                    <mt-select
                      v-model="rule.conditionAttribute"
                      :data-source="rulesObj.ruleConditionAttribute"
                      :fields="fields"
                      :placeholder="$t('请选择属性值')"
                      @select="setInputName($event, index, cindex, 'conditionAttributeName')"
                    ></mt-select>
                  </mt-form-item>
                </mt-col>
                <!-- 联动的 -->
                <mt-col :xs="24" :md="3" :lg="3">
                  <mt-form-item
                    prop="conditionSymbol"
                    v-if="rule.conditionAttribute == 'ruleConditionAttributeFinish'"
                  >
                    <mt-select
                      v-model="rule.conditionSymbol"
                      :data-source="rulesObj.ruleConditionSymbol"
                      :fields="fields"
                      @select="setInputName($event, index, cindex, 'conditionSymbolName')"
                    ></mt-select>
                  </mt-form-item>
                </mt-col>
                <mt-col
                  :xs="24"
                  :md="3"
                  :lg="3"
                  v-if="rule.conditionAttribute != 'ruleConditionAttributeFinish'"
                >
                  <mt-form-item prop="conditionSymbol">
                    <mt-select
                      v-model="rule.conditionSymbol"
                      :data-source="SymbolListOther"
                      :fields="fields"
                      @select="setInputName($event, index, cindex, 'conditionSymbolName')"
                    ></mt-select>
                  </mt-form-item>
                </mt-col>
                <mt-col :xs="24" :md="4" :lg="4">
                  <mt-form-item
                    prop="conditionTarget"
                    v-if="rule.conditionAttribute == 'ruleConditionAttributeStatus'"
                  >
                    <mt-select
                      v-model="rule.conditionTarget"
                      :placeholder="$t('请选择')"
                      :data-source="rulesObj.effectValueList"
                      :fields="{
                        text: 'itemName',
                        value: 'itemCode'
                      }"
                      @select="setInputName($event, index, cindex, 'conditionTargetName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item
                    prop="conditionTarget"
                    v-if="rule.conditionAttribute == 'ruleConditionAttributeArea'"
                  >
                    <mt-select
                      v-model="rule.conditionTarget"
                      :placeholder="$t('请选择')"
                      :data-source="rulesObj.ruleConditionAttributeArea"
                      :fields="{
                        text: 'itemName',
                        value: 'itemCode'
                      }"
                      @select="setInputName($event, index, cindex, 'conditionTargetName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item
                    prop="conditionTarget"
                    v-if="rule.conditionAttribute == 'ruleConditionAttributeSameEnterprise'"
                  >
                    <mt-select
                      v-model="rule.conditionTarget"
                      :placeholder="$t('请选择')"
                      :data-source="rulesObj.ruleConditionAttributeSameEnterprise"
                      :fields="{
                        text: 'itemName',
                        value: 'itemCode'
                      }"
                      @select="setInputName($event, index, cindex, 'conditionTargetName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item
                    prop="conditionTarget"
                    v-if="
                      rule.conditionAttribute != 'ruleConditionAttributeArea' &&
                      rule.conditionAttribute != 'ruleConditionAttributeStatus' &&
                      rule.conditionAttribute != 'ruleConditionAttributeSameEnterprise'
                    "
                  >
                    <mt-input
                      v-model="rule.conditionTarget"
                      :placeholder="$t('请输入')"
                      @select="setInputName($event, index, cindex, 'conditionTargetName')"
                    ></mt-input>
                  </mt-form-item>
                </mt-col>
                <mt-col :xs="24" :md="1" :lg="1">
                  <div class="close-icon" @click.stop="deleteRule(index, cindex)">
                    <i class="mt-icons mt-icon-icon_input_clear"></i>
                  </div>
                </mt-col>
              </mt-row>
            </mt-form>
          </div>
        </template>

        <div class="add-task-btn pre-btn" @click="addPrecondition(index)">
          <i class="mt-icons mt-icon-icon_card_plus"></i>
          <span>{{ $t('添加前置条件') }}</span>
        </div>

        <mt-row :gutter="10">
          <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item :label="$t('任务类型')" label-style="top" prop="taskTemplateClassify">
              <mt-select
                :placeholder="$t('请选择任务类型')"
                v-model="task.taskTemplateClassify"
                :data-source="taskTemplateClassifyData"
                :fields="fields"
                @select="taskTemplateClassifySelect($event, index)"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <!-- itemCode === "selfTempType" || itemData.itemCode === "sceneSelfTempType" -->
          <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item
              :label="
                task.taskTemplateClassify === 'selfTempType' ||
                task.taskTemplateClassify === 'sceneSelfTempType'
                  ? $t('评审包')
                  : $t('模板类型')
              "
              label-style="top"
              prop="taskTemplateType"
            >
              <mt-select
                :placeholder="$t('请选择模板类型')"
                v-model="task.taskTemplateType"
                :data-source="task.templateTypeList"
                :fields="fields"
                @select="taskDefineSelect($event, index)"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :xs="24" :md="8" :lg="8">
            <!--  调查表 = 显示填写方 -->
            <template v-if="task.taskTemplateClassify === 'surveyTempType'">
              <mt-form-item :label="$t('填写方')" label-style="top" prop="taskOwner">
                <mt-select
                  v-model="task.taskOwner"
                  :data-source="taskOwnerList"
                  @select="setInputName2($event, index, 'taskOwnerName')"
                  :placeholder="$t('请选择填写方')"
                ></mt-select>
              </mt-form-item>
            </template>
          </mt-col>
        </mt-row>
        <mt-row :gutter="10">
          <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item :label="$t('通过条件')" label-style="top" prop="finishCondition">
              <mt-select
                v-model="task.finishCondition"
                :data-source="finishConditionList"
                :placeholder="$t('请选择完成条件')"
                @select="setInputName2($event, index, 'finishConditionName')"
                :fields="fields"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item :label="$t('触发条件')" label-style="top" prop="triggerCondition">
              <mt-select
                v-model="task.triggerCondition"
                :data-source="triggerConditionList"
                :placeholder="$t('请选择触发条件')"
                @select="setInputName2($event, index, 'triggerConditionName')"
                :fields="fields"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <!-- <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item
              :label="$t('完成条件')"
              label-style="top"
              prop="finishCondition"
            >
              <mt-select
                v-model="task.finishCondition"
                :data-source="conditionList"
                :placeholder="$t('请选择完成条件')"
                :fields="conditionFields"
              ></mt-select>
            </mt-form-item>
          </mt-col> -->
        </mt-row>
      </mt-form>
    </div>
    <div class="add-task-btn" @click="addOneTask">
      <i class="mt-icons mt-icon-icon_card_plus"></i>
      <span>{{ $t('添加任务') }}</span>
    </div>
  </div>
</template>

<script>
let TaskTemplateTypeMap = new Map()
let PackageListMap = new Map()
let AttributeMap = new Map()
import { validateFormList, getRandom, taskRulesAll, taskRules } from './index'
import utils from '@/utils/utils'

const DefaultTask = {
  taskDefineName: '', //调查表名称
  taskOwner: '', // 填写方
  taskTemplateName: '', // 任务模板名称
  taskTemplateType: '', // 模板类型（分类对应的模板）
  taskTemplateClassify: '', // 任务类型
  finishCondition: '', // 完成条件id
  triggerCondition: '', // 完成条件id
  templateTypeList: [], // 模板类型： 调查表列表，根据调查表类型动态获取
  buyerRuleDTO: {
    ruleConditionDTOList: []
  } // 规则条件
}
let DefaultRule = {
  conditionType: 1, //	条件类型(0无、1且 、 2 或)
  conditionAttribute: '', // 属性值集
  conditionObject: '', // 对象值集
  conditionSymbol: 0, // 操作符值集(=,>,<等)
  conditionTarget: '' // 目标值
}
let INDEX = -1 // 添加规则的下标
export default {
  filters: {
    taskOrder(val) {
      return utils.numIntToChinese(val)
    }
  },
  props: {
    rulesType: {
      type: String,
      required: false,
      default: ''
    },
    rulesObj: {
      type: Object,
      required: false,
      default: () => {}
    },
    dataSource: {
      type: Array,
      required: false,
      default: () => []
    },
    isEdit: {
      type: Boolean,
      required: false,
      default: false
    },
    // 准入/认证任务分类
    taskTemplateClassifyData: {
      type: Array,
      default: () => []
    },
    // 触发条件
    triggerConditionList: {
      type: Array,
      default: () => []
    },
    // 通过条件
    finishConditionList: {
      type: Array,
      default: () => []
    },
    hasGetRules: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      // 配置任务列表
      taskTemplateList: [
        JSON.parse(
          JSON.stringify({
            id: getRandom(),
            ...DefaultTask
          })
        )
      ],
      fields: {
        text: 'itemName',
        value: 'itemCode'
      },
      taskDefineFields: {
        text: 'taskTemplateName',
        value: 'id'
      },

      SymbolList: [
        {
          text: '>',
          value: 0
        },
        {
          text: '≥',
          value: 1
        },
        {
          text: '<',
          value: 2
        },
        {
          text: '≤',
          value: 3
        },
        {
          text: '=',
          value: 4
        },
        {
          text: '≠',
          value: 5
        }
      ],
      SymbolListOther: [
        {
          itemCode: '5',
          itemName: '='
        },
        {
          itemCode: '6',
          itemName: '≠'
        }
      ],
      conditionAttributeList: [],
      conditionObjectList: [],

      // 填写方
      taskOwnerList: [
        {
          text: this.$t('采方'),
          value: '0'
        },
        {
          text: this.$t('供方'),
          value: '1'
        }
      ],

      taskRoleFields: {
        text: 'taskRoleName',
        value: 'taskRoleId'
      },

      // 完成条件
      conditionList: [
        {
          conditionName: this.$t('自动完成'),
          finishCondition: 1
        },
        {
          conditionName: this.$t('审批完成'),
          finishCondition: 2
        }
      ],
      conditionFields: {
        text: 'conditionName',
        value: 'finishCondition'
      },

      // 审批流
      workflowKeyFields: {
        text: 'completionTypeName',
        value: 'completionTypeId'
      },

      taskRules,
      taskRulesAll,

      ruleRules: {
        conditionType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], //	条件类型(0无、1且 、 2 或)
        conditionAttribute: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], // this.$t("属性值集")
        conditionObject: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], // this.$t("对象值集")
        conditionSymbol: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], // this.$t("操作符值集")(=,>,<等)
        conditionTarget: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }] // 目标值
      },

      initLoaded: false,
      isGetRulesConfig: false // 是否请求过rules的接口信息
    }
  },
  watch: {
    dataSource: {
      handler(newValue) {
        if (newValue.length > 0) {
          this.initData()
        }
      },
      immediate: true
    },
    rulesObj: {
      handler(newValue) {
        if (Object.keys(newValue).length > 0) {
          if (!this.isEdit) {
            this.addPrecondition(0)
          }
        }
      },
      immediate: true
    }
    // hasGetRules(nv) {
    //   if (nv && this.rulesType === "task") {
    //     if(!this.isEdit){
    //       DefaultRule = {
    //       conditionType: this.rulesObj.ruleType[1].itemCode, //	条件类型(0无、1且 、 2 或)
    //       conditionAttribute:
    //         this.rulesObj.ruleConditionAttribute[0].itemCode || "", // 属性值集
    //       conditionObject: this.rulesObj.ruleConditionObject[0].itemCode || "", // 对象值集
    //       conditionSymbol: this.rulesObj.ruleConditionSymbol[4].itemCode || "", // 操作符值集(=,>,<等)
    //       conditionTarget: "", // 目标值
    //     };
    //     this.setInputValue();
    //     }
    //   }
    // },
  },

  mounted() {
    // window.vm = this
  },
  methods: {
    validateRulesConfig(index) {
      return validateFormList(
        this.taskTemplateList[index]['buyerRuleDTO']['ruleConditionDTOList'],
        'ruleRef',
        this
      )
    },
    // 新增预设条件
    addPrecondition(index) {
      INDEX = index
      // if (!this.hasGetRules) {
      //   // 获取条件里面所有的枚举
      //   this.$emit("getRules");
      // } else {

      // }
      DefaultRule = {
        conditionType: this.rulesObj.ruleType[1].itemCode, //	条件类型(0无、1且 、 2 或)
        conditionAttribute: this.rulesObj.ruleConditionAttribute[0].itemCode || '', // 属性值集
        conditionObject: this.rulesObj.ruleConditionObject[0].itemCode || '', // 对象值集
        conditionSymbol: this.rulesObj.ruleConditionSymbol[4].itemCode || '', // 操作符值集(=,>,<等)
        conditionTarget: '' // 目标值
      }
      this.setInputValue()
    },
    setInputValue() {
      //校验规则
      let rs = true
      if (
        !!this.taskTemplateList[INDEX]['buyerRuleDTO'] &&
        !!this.taskTemplateList[INDEX]['buyerRuleDTO']['ruleConditionDTOList'] &&
        this.taskTemplateList[INDEX]['buyerRuleDTO']['ruleConditionDTOList'].length != 0
      ) {
        rs = this.validateRulesConfig(INDEX)
      }

      if (rs) {
        // 初始化
        if (
          !this.taskTemplateList[INDEX]['buyerRuleDTO'] ||
          !this.taskTemplateList[INDEX]['buyerRuleDTO'].ruleConditionDTOList
        ) {
          this.taskTemplateList[INDEX]['buyerRuleDTO'] = {
            ruleConditionDTOList: []
          }
        }
        this.taskTemplateList[INDEX]['buyerRuleDTO'].ruleConditionDTOList.push(
          JSON.parse(
            JSON.stringify({
              rid: getRandom(),
              ...DefaultRule
            })
          )
        )
      }
    },
    deleteRule(index, cindex) {
      if (
        !this.taskTemplateList[index] ||
        !this.taskTemplateList[index]['buyerRuleDTO']['ruleConditionDTOList'] ||
        this.taskTemplateList[index]['buyerRuleDTO']['ruleConditionDTOList'].length === 0
      )
        return
      this.taskTemplateList[index]['buyerRuleDTO'].ruleConditionDTOList.splice(cindex, 1)
    },
    // 编辑时，任务列表的初始化。根据列表中的调查表类型，把每个任务的调查表列表数据源对应上
    initData() {
      if (this.dataSource.length > 0) {
        if (this.initLoaded === true) return
        this.taskTemplateList = JSON.parse(JSON.stringify(this.dataSource))
        this.taskTemplateList.forEach((item, index) => {
          if (
            item.taskTemplateClassify === 'selfTempType' ||
            item.taskTemplateClassify === 'sceneSelfTempType'
          ) {
            this.setSelfTempType(item.taskTemplateClassify, index)
          } else {
            this.setTemplateTypeList(item.taskTemplateClassify, index)
          }
        })
        this.initLoaded = true
      }
    },

    // 获取当前的配置列表，父级可在提交表单时调用
    getCurrentTaskConfig() {
      return this.taskTemplateList.map((item) => {
        let {
          id,
          taskOwner,
          taskOwnerName,
          finishCondition,
          finishConditionName,
          triggerCondition,
          triggerConditionName,
          taskTemplateClassify,
          taskTemplateClassifyName,
          taskTemplateName,
          taskTemplateNameName,
          taskTemplateType,
          taskTemplateTypeName,
          buyerRuleDTO
        } = item
        if (!!buyerRuleDTO && !!buyerRuleDTO.ruleConditionDTOList) {
          buyerRuleDTO = {
            ...buyerRuleDTO,
            ruleType: 'prepRule'
          }
        } else {
          buyerRuleDTO = {}
        }
        taskOwner = +taskOwner
        if (id && String(id).length < 10) {
          id = ''
        }
        return {
          taskOwner,
          taskOwnerName,
          finishCondition,
          finishConditionName,
          triggerCondition,
          triggerConditionName,
          taskTemplateClassify,
          taskTemplateClassifyName,
          taskTemplateName,
          taskTemplateNameName,
          taskTemplateType,
          taskTemplateTypeName,
          buyerRuleDTO,
          id
        }
      })
    },

    validateTaskConfig() {
      let rulesResult = this.taskTemplateList.map((item, index) => {
        if (item['buyerRuleDTO'] !== null && Object.keys(item['buyerRuleDTO']).length > 0) {
          return this.validateRulesConfig(index)
        } else {
          return true
        }
      })
      let rulesResultB = rulesResult.some((item) => {
        return item == true
      })
      let taskRefB = validateFormList(this.taskTemplateList, 'taskRef', this)
      if (!rulesResultB || !taskRefB) {
        this.$toast({
          content: this.$t('请填写任务配置阶段所有任务基础信息和前置条件信息！'),
          type: 'error'
        })
        return
      }
      return rulesResultB && taskRefB
    },

    deleteCurrentTask(index) {
      this.taskTemplateList.splice(index, 1)
    },

    addOneTask() {
      let rs = this.validateTaskConfig()
      if (rs) {
        this.taskTemplateList.push(
          JSON.parse(
            JSON.stringify({
              id: getRandom(),
              ...DefaultTask
            })
          )
        )
        let newLen = this.taskTemplateList.length - 1
        this.taskTemplateList[newLen]['buyerRuleDTO'].ruleConditionDTOList.push(
          JSON.parse(
            JSON.stringify({
              rid: getRandom(),
              ...DefaultRule
            })
          )
        )
      }
    },

    // 根据列表中的调查表类型，把每个任务的调查表列表数据源对应上
    setTemplateTypeList(classify, index) {
      const dictCode = classify
      // 缓存
      if (TaskTemplateTypeMap.has(dictCode)) {
        let data = TaskTemplateTypeMap.get(dictCode)
        this.$set(this.taskTemplateList[index], 'templateTypeList', data)
        return
      }
      this.$loading()
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        this.$hloading()
        const { code, data } = res
        if (code == 200 && data) {
          TaskTemplateTypeMap.set(dictCode, data)
          this.$set(this.taskTemplateList[index], 'templateTypeList', data)
        }
      })
    },

    // 供应商自查 、 现场审查 调 评审包 接口
    setSelfTempType(classify, index) {
      const dictCode = classify
      // 缓存
      if (PackageListMap.has(dictCode)) {
        let data = PackageListMap.get(dictCode)
        this.$set(this.taskTemplateList[index], 'templateTypeList', data)
        return
      }
      this.$loading()
      this.$API.AccessProcess['queryPackageListQuery']().then((res) => {
        this.$hloading()
        const { code, data } = res
        if (code == 200 && data) {
          let dataTmp = data.map((item) => {
            return {
              ...item,
              itemCode: item.packageCode,
              itemName: item.packageName
            }
          })
          PackageListMap.set(dictCode, dataTmp)
          this.$set(this.taskTemplateList[index], 'templateTypeList', dataTmp)
        }
      })
    },

    // 选择调查表类型时需要更新调查表列表
    taskTemplateClassifySelect(e, index) {
      const { itemData } = e
      // 联动
      if (itemData.itemCode === 'selfTempType' || itemData.itemCode === 'sceneSelfTempType') {
        this.setSelfTempType(itemData.itemCode, index)
      } else {
        this.setTemplateTypeList(itemData.itemCode, index)
      }
      this.setInputName2(e, index, 'taskTemplateClassifyName')
    },

    taskDefineSelect(e, index) {
      const { itemData } = e
      if (itemData) {
        const { itemName, itemCode } = itemData
        this.$set(this.taskTemplateList[index], 'taskTemplateName', itemName)
        this.$set(this.taskTemplateList[index], 'taskTemplateType', itemCode)
        this.setInputName2(e, index, 'taskTemplateTypeName')
      }
    },

    setInputName(e, index, cIndex, attribute) {
      const { itemData } = e
      const { itemName, itemCode } = itemData
      this.$set(
        this.taskTemplateList[index]['buyerRuleDTO']['ruleConditionDTOList'][cIndex],
        [attribute],
        itemName
      )
      if (attribute == 'conditionTypeName') {
        for (
          let i = 0;
          i < this.taskTemplateList[index]['buyerRuleDTO']['ruleConditionDTOList'].length;
          i++
        ) {
          this.$set(
            this.taskTemplateList[index]['buyerRuleDTO']['ruleConditionDTOList'][i],
            ['conditionType'],
            itemCode
          )
          this.$set(
            this.taskTemplateList[index]['buyerRuleDTO']['ruleConditionDTOList'][i],
            [attribute],
            itemName
          )
        }
      }
    },

    // 获取itemName
    setInputName2(e, index, attribute) {
      const { itemData } = e
      let { itemName } = itemData
      itemName = attribute === 'taskOwnerName' ? itemData.text : itemName
      this.$set(this.taskTemplateList[index], [attribute], itemName)
    },

    // 条件的对象 => 属性的联动
    conditionAttributeSelect(e, index, cindex) {
      const { itemData } = e
      this.setInputName(e, index, cindex, 'conditionObjectName')
      this.setAttributeList(itemData.itemCode, index, cindex)
    },

    setAttributeList(itemCode, index, cindex) {
      const dictCode = 'ruleConditionAttribute' // itemCode ||
      // 缓存
      if (AttributeMap.has(dictCode)) {
        let data = AttributeMap.get(dictCode)
        this.$set(this.taskTemplateList[index][cindex], 'conditionAttribute ', data)
        return
      }
      this.$loading()
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        this.$hloading()
        const { code, data } = res
        if (code == 200 && data) {
          AttributeMap.set(dictCode, data)
          this.$set(this.taskTemplateList[index][cindex], 'conditionAttribute', data)
        } else {
          this.$set(this.taskTemplateList[index][cindex], 'conditionAttribute', [])
        }
      })
    },
    handlSltTaskRole(e, index) {
      const { itemData } = e
      if (itemData) {
        const { taskRoleName } = itemData
        this.$set(this.taskTemplateList[index], 'taskRoleName', taskRoleName)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
h4 {
  font-size: 14px;
  color: #292929;
  font-weight: bold;
  margin: 20px 0 10px 0;
}
.task-item {
  padding: 16px;
  background: #f5f6f9;
  border-radius: 6px;
  margin-bottom: 10px;
}
.close-icon {
  display: inline-flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  color: #9baac1;
  cursor: pointer;
}
.task-title {
  display: flex;
  margin-bottom: 20px;
  > span {
    font-size: 14px;
    font-weight: bold;
    color: #292929;
    flex: 1;
  }
  .icon-delete-box {
    color: #ff4949;
    font-weight: bold;
    cursor: pointer;
    .delete-tips {
      font-size: 14px;
      margin-left: 10px;
    }
  }
}

.add-task-btn {
  display: inline-flex;
  color: #6386c1;
  cursor: pointer;
  margin: 10px 0;
  > i {
    font-size: 18px;
  }
  > span {
    display: inline-block;
    font-size: 14px;
    margin-left: 10px;
    line-height: 18px;
    user-select: none;
  }
}
.pre-btn {
  margin: 10px 0 25px 0;
}
.custom-rule-container {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 20px;
  margin-top: 40px;
}
</style>
