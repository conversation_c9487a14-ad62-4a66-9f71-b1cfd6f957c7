<template>
  <div class="range-content">
    <h4>{{ range.orgName }}</h4>
    <!-- type是1，才展示品类项 -->
    <div
      v-if="type && range.categoryRelationDTOList && range.categoryRelationDTOList.length"
      class="range-item-box"
    >
      <tag-item
        class="range-item"
        v-for="(rangeItem, rangeIndex) in range.categoryRelationDTOList"
        :key="rangeIndex"
        >{{ rangeItem.categoryName || '--' }}</tag-item
      >
    </div>
  </div>
</template>

<script>
import TagItem from './tag-item.vue'
export default {
  components: {
    TagItem
  },
  props: {
    range: {
      type: Object,
      default: () => {}
    },
    type: {
      type: Number,
      default: 0
    }
  }
}
</script>

<style lang="scss" scoped>
.range-content {
  margin: 10px 0 15px 0;
  > h4 {
    font-size: 12px;
    color: #292929;
    font-weight: 600;
    margin-bottom: 5px;
  }
  .range-item-box {
    .range-item {
      margin-bottom: 5px;
    }
  }
}
</style>
