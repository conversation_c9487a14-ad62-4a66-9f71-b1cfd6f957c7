import { i18n } from '@/main.js'
export function validateFormList(list = [], ref = 'ref', _this) {
  let len = list.length
  let validRs = true
  for (let i = 0; i < len; i++) {
    const refName = ref + i
    _this.$refs[refName] &&
      _this.$refs[refName][0] &&
      _this.$refs[refName][0].validate((valid) => {
        if (!valid) {
          validRs = false
          return
        }
      })
    if (!!list[i].ruleConditionDTOList && !!list[i].ruleConditionDTOList.length > 0) {
      let validRuleRs = validateFormList(list[i].ruleConditionDTOList, 'ruleRef', _this)
      if (!validRuleRs) {
        validRs = false
        return
      }
    }
  }
  return validRs
}
export function validateRefList(refList = [], validateMethod = '') {
  let len = refList.length
  let validRs = true
  for (let i = 0; i < len; i++) {
    let refItem = refList[i]
    validRs = refItem[validateMethod]()
    if (!validRs) {
      return false
    }
  }
  return validRs
}

export function getRandom() {
  return Math.floor(Math.random() * 1000000)
}

export const taskRules = {
  taskTemplateClassify: [{ required: true, message: i18n.t('请选择任务分类'), trigger: 'blur' }],
  taskTemplateType: [{ required: true, message: i18n.t('请选择模板类型'), trigger: 'blur' }],
  // taskOwner: [{ required: false, message: this.$t("请选择填写方"), trigger: "blur" }],
  finishCondition: [{ required: true, message: i18n.t('请选择完成条件'), trigger: 'blur' }],
  triggerCondition: [{ required: true, message: i18n.t('请选择触发条件'), trigger: 'blur' }]
}

export const taskRulesAll = {
  ...taskRules,
  taskOwner: [{ required: true, message: i18n.t('请选择填写方'), trigger: 'blur' }]
}
