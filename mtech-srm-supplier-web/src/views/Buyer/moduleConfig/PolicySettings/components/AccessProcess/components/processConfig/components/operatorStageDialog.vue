<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>

      <div class="slider-content">
        <h4>{{ $t('配置阶段') }}</h4>
        <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
          <mt-row :gutter="20">
            <mt-col :xs="24" :md="12" :lg="12">
              <mt-form-item
                class="form-item"
                :label="$t('阶段来源')"
                label-style="top"
                prop="stageType"
              >
                <mt-select
                  v-model="formInfo.stageType"
                  :placeholder="$t('请选择阶段来源')"
                  float-label-type="Never"
                  :data-source="stageTypeList"
                  :fields="stageTypeFields"
                  @select="handleSltstageType"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <!-- 新增阶段下拉 -->
            <mt-col :xs="24" :md="12" :lg="12">
              <mt-form-item
                class="form-item"
                :label="$t('阶段名称')"
                label-style="top"
                prop="stageDefineId"
              >
                <mt-select
                  v-model="formInfo.stageDefineId"
                  :placeholder="$t('请输入阶段名称')"
                  float-label-type="Never"
                  :data-source="stageDefineList"
                  :fields="stageFields"
                  @select="handleSltstageDefine"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>

        <!-- 晋级效果 条件单选 来自枚举接口-->
        <template v-if="formInfo.stageType === 'addStage'">
          <!-- 配置任务 :task-template-classify-list="taskTemplateClassifyList"-->
          <configure-task
            v-if="defaultTaskList && rulesObj"
            ref="defaultConfigureTask"
            :data-source="defaultTaskList"
            :is-edit="isEdit"
            :task-template-classify-data="taskTemplateClassifyList2"
            :trigger-condition-list="triggerConditionList"
            :finish-condition-list="finishConditionList"
            :rules-obj="rulesObj"
            :has-get-rules="hasGetRules"
            :rules-type="rulesType1"
          ></configure-task>
        </template>
        <!-- 晋级规则 -->
        <promotion-rule
          v-if="defaultPromotionRule && rulesObj"
          ref="promotionRule"
          :is-edit="isEdit"
          :data-source="defaultPromotionRule"
          :rules-obj="rulesObj"
          :rules-type="rulesType2"
          :has-get-rules="hasGetRules"
          :rule-output-list="ruleOutputList"
        >
        </promotion-rule>
      </div>

      <div class="slider-footer mt-flex">
        <span @click="confirm">{{ $t('确定') }}</span>
        <span v-if="isEdit" @click="deleteCurrentStage">
          {{ $t('删除此阶段') }}
        </span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import ConfigureTask from './configureTask.vue'
import promotionRule from './promotionRule.vue'

export default {
  components: {
    ConfigureTask,
    promotionRule
  },
  data() {
    return {
      formInfo: {
        stageDefineId: '', // 阶段名称
        stageType: null // 阶段来源
      },
      searchList: [], // 阶段标识
      stageTypeFields: {
        text: 'itemName',
        value: 'itemCode'
      },

      stageDefineList: [
        // { stageName: "阶段1", id: "1122" }
      ], // 阶段名称
      stageTypeList: [
        // { stageName: this.$t("新增阶段"), id: "0" },
      ], // 阶段名称
      stageFields: {
        text: 'stageName',
        value: 'id'
      },
      sltStageDefine: {}, // 选择的阶段名称
      taskTemplateClassifyList: [], // 调查表类型
      taskTemplateClassifyList2: [], // 准入/认证任务分类
      triggerConditionList: [], // 准入/认证任务分类
      finishConditionList: [], // 准入/认证任务分类

      rules: {
        stageDefineId: [
          {
            required: true,
            message: this.$t('请输入阶段名称'),
            trigger: 'blur'
          }
        ],
        stageType: [
          {
            required: true,
            message: this.$t('请选择阶段来源'),
            trigger: 'blur'
          }
        ]
      },

      defaultTaskList: [], // 默认规则配置的任务列表
      defaultPromotionRule: [], // 默认晋级规则
      defaultUpgradeEffect: [], // 默认晋级效果

      // 特殊规则/自定义
      customRule: {},
      customRuleRules: {
        taskRuleName: [
          {
            required: true,
            message: this.$t('请命名此特殊规则'),
            trigger: 'blur'
          }
        ],
        taskRuleExpression: [
          {
            required: true,
            message: this.$t('请选择调查表标识'),
            trigger: 'blur'
          }
        ]
      },

      // 特殊规则 范围
      showTree: false, // treeview更新数据源，视图不更新

      // 品类列表，根据选择的公司
      categoryList: [],
      effectTypeList: [],
      sltCompanyId: '',
      sltCategoryTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'category'
      },
      relationIds: [], // 品类，特殊规则，选中的品类，接口传值
      rulesObj: {},
      hasGetRules: false,
      rulesType1: 'task',
      rulesType2: 'rule',
      ruleOutputList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    method() {
      return this.modalData.method
    },
    isEdit() {
      return this.modalData.isEdit
    },
    accessTemplate() {
      return this.modalData.accessTemplate
    },
    stageInfo() {
      return this.modalData.stageInfo
    }
  },
  async mounted() {
    await this.getRules()
    await this.queryStage()
    await this.queryStageType()
    // this.queryEffectType();
    await this.querySearchType()
    await this.queryTaskTemplateClassify()
    await this.queryTriggerCondition()
    await this.queryFinishCondition()
    await this.queryRuleOutput()
    this.isEdit && (await this.queryStageDetail())
  },
  methods: {
    async queryStageDetail() {
      this.$store.commit('startLoading')
      const { id } = this.stageInfo
      await this.$API.AccessProcess['queryStageDetail'](id).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          let { stageDefineId, stageName, stageType, buyerRuleDTO, stageTaskTemplateDTOList } = data
          this.formInfo = Object.assign({}, this.formInfo, {
            stageDefineId,
            stageType,
            stageName
          })

          stageTaskTemplateDTOList =
            !!stageTaskTemplateDTOList && stageTaskTemplateDTOList.length > 0
              ? stageTaskTemplateDTOList.map((v) => {
                  return {
                    ...v,
                    taskOwner: v.taskOwner + ''
                  }
                })
              : []

          this.defaultTaskList = stageTaskTemplateDTOList
          this.defaultPromotionRule = buyerRuleDTO
        }
        this.$store.commit('endLoading')
      })
    },

    // 阶段名称列表
    async queryStage(stageType = '') {
      let { accessTemplateType } = this.accessTemplate
      if (stageType) {
        accessTemplateType = stageType
      }
      let params = {
        condition: '',
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          {
            condition: 'equal',
            field: 'accessType',
            label: this.$t('阶段类型'),
            operator: 'equal',
            type: 'number',
            value: accessTemplateType
          }
        ]
      }

      this.$API.AccessProcess['queryStage'](params).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          const { records } = data
          this.stageDefineList = records
        }
      })
    },
    // 阶段名称
    async queryStageType() {
      const dictCode = 'stageType'
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          let stageTypeList = data
          // 认证流程 不能在选择引入认证流程
          if (this.method !== 'access') {
            stageTypeList = data.filter((v) => v.itemCode === 'addStage')
          }
          this.stageTypeList = stageTypeList
          // 赋初始值
          this.formInfo.stageType = !!data[0] && !!data[0].itemCode ? data[0].itemCode : ''
        }
      })
    },
    // 晋级效果类型
    async queryEffectType() {
      const dictCode = 'effectType'
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.effectTypeList = data
        }
      })
    },
    // 阶段标识
    // queryStageType() {
    //   const { accessTemplateType } = this.accessTemplate;
    //   const dictCode =
    //     accessTemplateType == 0 ? "companyStageMark" : "categortyStageMark";
    //   this.$API.AccessProcess["queryDict"]({
    //     dictCode,
    //   }).then((res) => {
    //     const { code, data } = res;
    //     if (code == 200 && data) {
    //       this.stageTypeList = data;
    //     }
    //   });
    // },
    // 调查表标识
    async querySearchType() {
      const dictCode = 'formTaskMark'
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.searchList = data
        }
      })
    },
    // // 调查表分类
    // queryTaskClassify() {
    //   this.$API.AccessProcess["queryDict"]({
    //     dictCode: "adTaskClassify",
    //   }).then((res) => {
    //     const { code, data } = res;
    //     if (code == 200 && data) {
    //       this.taskTemplateClassifyList = data;
    //     }
    //   });
    // },
    // 准入/认证任务分类
    async queryTaskTemplateClassify() {
      this.$API.AccessProcess['queryDict']({
        dictCode: 'taskTemplateClassify'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          let taskTemplateClassifyList2 = data || []
          // 准入只能加调查表
          if (this.method === 'access') {
            taskTemplateClassifyList2 = data.filter(
              (v) => v.itemCode === 'surveyTempType' //itemName === "调查表"
            )
          }
          this.taskTemplateClassifyList2 = taskTemplateClassifyList2
        }
      })
    },
    // 触发条件
    async queryTriggerCondition() {
      this.$API.AccessProcess['queryDict']({
        dictCode: 'triggerCondition'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.triggerConditionList = data
        }
      })
    },
    // 通过条件
    async queryFinishCondition() {
      this.$API.AccessProcess['queryDict']({
        dictCode: 'finishCondition'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.finishConditionList = data
        }
      })
    },
    // 通过条件
    async queryRuleOutput() {
      this.$API.AccessProcess['queryDict']({
        dictCode: 'ruleOutput'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.ruleOutputList = data
        }
      })
    },

    // 获取规则下的几个接口
    async getRules() {
      if (this.hasGetRules) {
        return
      }
      this.$loading()
      let ruleConditionObject = this.$API.AccessProcess['queryDict']({
        dictCode: 'ruleConditionObject'
      })
      let ruleConditionAttribute = this.$API.AccessProcess['queryDict']({
        dictCode: 'ruleConditionAttribute'
      })
      // let ruleConditionSymbol = this.$API.AccessProcess["queryDict"]({
      //   dictCode: "ruleConditionSymbol",
      // });

      let ruleConditionType = this.$API.AccessProcess['queryDict']({
        dictCode: 'ruleConditionType'
      })
      let supplierStatus = this.$API.AccessProcess['queryDict']({
        dictCode: 'supplierStatus'
      })

      Promise.all([
        ruleConditionObject,
        ruleConditionAttribute,
        // ruleConditionSymbol,
        ruleConditionType,
        supplierStatus
      ])
        .then((result) => {
          this.$hloading()
          let rulesObj = {}
          rulesObj['ruleConditionAttributeArea'] = [
            {
              itemCode: 'CN',
              itemName: '中国大陆'
            }
          ]
          rulesObj['ruleConditionAttributeSameEnterprise'] = [
            {
              itemCode: '1',
              itemName: '同一企业'
            },
            {
              itemCode: '0',
              itemName: '非同一企业'
            }
          ]
          rulesObj['ruleConditionObject'] = result[0].data
          rulesObj['ruleConditionAttribute'] = result[1].data
          // rulesObj["ruleConditionSymbol"] = result[2].data;
          rulesObj['ruleType'] = result[2].data
          rulesObj['effectValueList'] = result[3].data
          rulesObj['ruleConditionSymbol'] = [
            {
              itemCode: '1',
              itemName: '>'
            },
            {
              itemCode: '2',
              itemName: '<'
            },
            {
              itemCode: '3',
              itemName: '>='
            },
            {
              itemCode: '4',
              itemName: '<='
            },
            {
              itemCode: '5',
              itemName: '='
            },
            {
              itemCode: '6',
              itemName: '≠'
            },
            {
              itemCode: '7',
              itemName: '空'
            }
          ]
          this.rulesObj = rulesObj
          this.hasGetRules = true
        })
        .catch(() => {
          this.$hloading()
          this.$toast({
            content: '获取规则参数失败,请重试！',
            type: 'warning'
          })
        })
    },

    clearRange() {
      this.showTree = false
      this.$set(this.sltCategoryTree, 'dataSource', [])
      this.categoryList = this.categoryList.map((item) => {
        return {
          ...item,
          checked: false
        }
      })
      setTimeout(() => {
        this.showTree = true
      }, 200)
    },

    // 选择阶段名称
    handleSltstageDefine(args) {
      const { itemData } = args
      if (itemData) {
        const { id: stageDefineId, stageName } = itemData
        this.sltStageDefine = {
          stageDefineId,
          stageName
        }
      }
    },
    // 选择阶段来源
    // 选择引入 右侧select变成引入流程下拉 晋级效果依然存在 下面的条件没了
    handleSltstageType(args) {
      console.log(args)
      let { itemData } = args
      // 引用流程
      if (itemData.itemCode === 'quoteProcess') {
        this.queryStage(itemData.itemCode)
      } else {
        this.queryStage()
      }
    },

    // 默认的规则数据
    getDefaultRule() {
      // let buyerStageEffectTemplateDTOList =
      //   this.$refs.buyerStageEffectTemplateDTOList.getCurrentEffectData();

      let stageTaskTemplateDTOList = []
      let buyerRuleDTO = {}
      if (this.formInfo.stageType !== 'quoteProcess') {
        // 配置的任务
        stageTaskTemplateDTOList = this.$refs.defaultConfigureTask.getCurrentTaskConfig()
      }
      // 晋升规则
      buyerRuleDTO = this.$refs.promotionRule.getCurrentPromotionConfig()
      // 合并数据
      let defaultStageRuleTemplate = {
        // buyerStageEffectTemplateDTOList,
        buyerRuleDTO,
        stageTaskTemplateDTOList
      }
      return defaultStageRuleTemplate
    },

    confirm() {
      this.$refs.formInfo.validate((valid) => {
        // 校验默认的任务
        if (valid) {
          // this.$loading();
          // 晋级效果得正则
          // let upgradeEffectRs =
          //   this.$refs.buyerStageEffectTemplateDTOList.validateTaskConfig();
          // if (!upgradeEffectRs){
          //   this.$hloading();
          //   return;
          // }
          let taskRs = null
          // 引用流程没有任务和规则
          if (this.formInfo.stageType !== 'quoteProcess') {
            // 校验默认任务的规则
            taskRs = this.$refs.defaultConfigureTask.validateTaskConfig()
          }
          if (!taskRs) {
            this.$hloading()
            return
          }
          // 校验默认任务的晋升
          let promotionRule = this.$refs.promotionRule.validateRulesConfigAll()
          if (!promotionRule) {
            this.$hloading()
            return
          }
          const { id: accessTemplateId } = this.accessTemplate
          const { stageType } = this.formInfo
          let defaultRule = this.getDefaultRule()
          let params = {
            accessTemplateId,
            ...this.sltStageDefine,
            stageType,
            ...defaultRule
          }
          if (this.isEdit) {
            const { id } = this.stageInfo
            params = Object.assign({}, params, { id })
          }
          const methodName = this.isEdit ? 'updateStage' : 'addStage'
          const toastInfo = this.isEdit ? this.$t('更新') : this.$t('新增')
          this.$API.AccessProcess[methodName](params).then((res) => {
            this.$hloading()
            const { code } = res
            if (code == 200) {
              this.$toast({
                content: `${toastInfo}公司阶段成功`,
                type: 'success'
              })
              this.$emit('confirm-function')
            } else {
              this.$toast({
                content: res.msg || this.$t('系统异常'),
                type: 'error'
              })
            }
          })
        } else {
          this.$toast({
            content: this.$t('请补全配置阶段相关基础信息！'),
            type: 'error'
          })
        }
      })
    },
    deleteCurrentStage() {
      const { id } = this.stageInfo
      // this.$dialog({
      //   data: {
      //     title: this.$t("删除"),
      //     message: "是否确认删除此阶段？",
      //   },
      //   success: () => {
      this.$API.AccessProcess['deleteStage']({ stageTemplateId: id })
        .then((res) => {
          const { code } = res
          if (code == 200) {
            this.$emit('confirm-function')
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.slider-header {
  background: #00469c !important;
}

.slider-panel-container .slider-modal .slider-header .slider-title {
  color: #fff;
}

.slider-panel-container .slider-modal .slider-header .slider-close {
  color: #fff;
}

.add-task-btn {
  display: inline-flex;
  color: #6386c1;
  cursor: pointer;
  margin: 10px 0;
  > i {
    font-size: 18px;
  }
  > span {
    display: inline-block;
    font-size: 14px;
    margin-left: 10px;
    line-height: 18px;
  }
}

h4 {
  font-size: 14px;
  line-height: 14px;
  color: #292929;
  font-weight: bold;
  margin: 20px 0 10px 0;
}

.custom-rule-container {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 20px;
  margin-top: 40px;
  .delete-btn {
    margin: 20px 0 8px 0;
    font-size: 14px;
    font-weight: bold;
    color: #00469c;
    text-align: center;
    cursor: pointer;
  }
}
.add-custom-btn {
  font-size: 14px;
  font-weight: bold;
  color: #00469c;
  text-align: center;
  line-height: 60px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-top: 20px;
  cursor: pointer;
  > i {
    margin-right: 10px;
  }
}

.category-select-range {
  display: flex;
  width: 100%;
  .config-range {
    flex: 1;
    margin-left: 20px;
    .clear-btn {
      float: right;
      color: #00469c;
      cursor: pointer;
    }
  }
  .all-list-box {
    width: 500px;
    min-height: 210px;
    height: calc(100% - 44px);
    display: flex;
    background: #fff;
    border: 1px solid #e8e8e8;
    .company-list {
      width: 260px;
      border-right: 1px solid #e8e8e8;
    }
    .category-list {
      flex: 1;
    }

    .company-category-item {
      font-size: 14px;
      padding-left: 20px;
      line-height: 30px;
      cursor: pointer;
      .category-checkbox {
        margin-right: 10px;
      }
      &:hover {
        background: #f5f6f9;
      }
    }
    .is-select {
      color: #00469c;
      font-weight: bold;
      background: #f5f6f9;
    }
  }

  .range-list {
    flex: 1;
    min-height: 210px;
    background: #fff;
    border: 1px solid #e8e8e8;
    /deep/ .mt-tree-view {
      width: 100%;
    }
  }
  .list-no-data {
    font-size: 14px;
    color: #9a9a9a;
    padding-top: 20px;
    text-align: center;
  }
}
</style>
