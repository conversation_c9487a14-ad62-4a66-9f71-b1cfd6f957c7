<template>
  <div class="process-config">
    <div class="add-card" @click="addAccess">
      <i class="mt-icons mt-icon-icon_solid_add"></i>
      <span>{{ method === 'access' ? $t('新增准入流程') : $t('新增认证流程') }}</span>
    </div>
    <template v-if="accessList && accessList.length">
      <process-item
        :ref="'processItem' + access.id"
        v-for="access in accessList"
        :key="access.id"
        class="config-process-item"
        :access-list="accessList"
        :access-template="access"
        :index="access.id"
        @editAccess="handleEditAccess(access)"
        @copyAccess="handleCopyAccess(access)"
        @deleteAccess="handleDeleteAccess(access)"
        @operatorStageSuccess="initData"
        @updateRangeSuccess="initData"
        @changeStatusSuccess="initData"
        @move-success="initData"
        @move-fail="initData"
      ></process-item>
    </template>

    <mt-page
      :page-settings="pageSettings"
      :total-pages="totalPages"
      @currentChange="goToPage"
      @sizeChange="changePageSize"
    ></mt-page>
  </div>
</template>

<script>
import ProcessItem from './components/process-item.vue'
import MtPage from '@mtech-ui/page'

export default {
  components: {
    ProcessItem,
    MtPage
  },
  props: {
    method: {
      type: String,
      default: 'access'
    }
  },
  data() {
    return {
      accessList: [], // 准入流程列表

      pageParams: {
        rules: [
          {
            label: this.$t('流程类型'),
            field: 'accessTemplateType',
            type: 'number',
            operator: this.method === 'access' ? 'notequal' : 'equal',
            value: 2
          }
        ],
        condition: '',
        page: {
          current: 1,
          size: 5
        }
      },
      pageSettings: { pageSize: 5, pageCount: 5, pageSizes: [5, 10, 15, 20] },
      total: 1
    }
  },
  computed: {
    totalPages() {
      let pageSize = Math.ceil(this.total / this.pageSettings.pageSize)
      return pageSize
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.$loading()
      this.accessList = []
      this.$API.AccessProcess['queryAccess'](this.pageParams)
        .then((res) => {
          this.$store.commit('endLoading')
          const { code, data } = res
          if (code == 200 && data) {
            const { records, total } = data
            this.accessList = records
            this.total = Number(total)
            this.$nextTick(() => {
              records.forEach((item) => {
                // 停用时候 可以拖拽编辑
                if (item.status === 2) {
                  let processRef = this.$refs['processItem' + item.id]
                  !!processRef && !!processRef[0] && processRef[0].draggable(item.id)
                }
              })
            })
            this.$hloading()
          }
        })
        .catch(() => {
          this.$hloading()
        })
    },

    // 新增流程
    addAccess() {
      const _this = this
      const { method } = _this
      this.$dialog({
        modal: () => import('./components/operatorAccessDialog.vue'),
        data: {
          title: method === 'access' ? this.$t('新增准入流程') : this.$t('新增认证流程'),
          method
        },
        success: () => {
          _this.initData()
        }
      })
    },

    // 编辑流程
    handleEditAccess(access) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/operatorAccessDialog.vue'),
        data: {
          title: this.$t('编辑准入流程'),
          isEdit: true,
          info: access,
          method: this.method
        },
        success: () => {
          _this.initData()
        }
      })
    },
    // 复制流程
    handleCopyAccess(access) {
      const _this = this
      const {
        id: accessTemplateId,
        accessTemplateName,
        accessTemplateType,
        triggerCondition
      } = access
      let params = {
        accessTemplateId,
        accessTemplateName,
        accessTemplateType,
        triggerCondition
      }
      this.$dialog({
        data: {
          title: this.$t('复制'),
          message: this.$t('是否确认复制所选流程？'),
          confirm: () => _this.$API.AccessProcess['copyAccess'](params)
        },
        success: () => {
          _this.$toast({ content: this.$t('复制成功'), type: 'success' })
          _this.initData()
        }
      })
    },
    // 删除流程
    handleDeleteAccess(access) {
      const _this = this
      const { id: accessTemplateId } = access
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选流程？'),
          confirm: () => _this.$API.AccessProcess['deleteAccess']({ accessTemplateId })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.initData()
        }
      })
    },

    goToPage(num) {
      this.$set(this.pageParams.page, 'current', num)
      this.initData()
    },
    changePageSize(size) {
      this.$set(this.pageParams.page, 'size', size)
      this.$set(this.pageSettings, 'pageSize', size)
      this.initData()
    }
  }
}
</script>

<style lang="scss" scoped>
.process-config {
  padding-top: 10px;
  .add-card {
    min-width: 1250px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    font-size: 20px;
    color: var(--accent); // #00469c;
    font-weight: 600;
    margin-bottom: 20px;
    cursor: pointer;
    > i {
      font-size: 24px;
      margin-right: 10px;
    }
  }
  .config-process-item {
    margin-bottom: 20px;
  }
}
</style>
