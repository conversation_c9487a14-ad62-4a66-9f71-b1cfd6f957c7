<template>
  <div>
    <div class="stage-content-item">
      <!-- 默认任务 -->
      <div class="default-task-box">
        <!-- <template v-if="stage.buyerStageEffectTemplateDTOList">
          <h3>{{ $t("晋级效果") }}：</h3>
          <p
            class="upgrade-info"
            v-for="(effect, index) in stage.buyerStageEffectTemplateDTOList"
            :key="index"
          >{{ effect.effectTypeName }}-{{ effect.effectValueName }}</p>
        </template> -->
        <template v-if="stage.stageTaskTemplateDTOList">
          <h3>{{ $t('任务') }}：</h3>
          <ul>
            <li v-for="(task, taskIndex) in stage.stageTaskTemplateDTOList" :key="taskIndex">
              <p class="item-form-title">{{ task.taskTemplateClassifyName }}</p>
            </li>
          </ul>
        </template>

        <template v-if="stage.buyerRuleDTO">
          <h3>{{ $t('晋级规则') }}：</h3>
          <div v-for="(buyerRule, buyerRuleIndex) in stage.buyerRuleDTO" :key="buyerRuleIndex">
            <div class="buyer-rule-flex">
              <div class="number_top_circle">
                {{ buyerRuleIndex + 1 }}
              </div>
              <div class="buyer-rule">
                <div v-for="(task, taskIndex) in buyerRule.ruleConditionDTOList" :key="taskIndex">
                  <div class="upgrade-info">
                    <p>
                      {{ task.conditionTypeName === $t('无') ? $t('当') : task.conditionTypeName }}
                      {{ task.conditionObjectName }}{{ task.conditionAttributeName }}
                      <span v-if="task.conditionAttribute != 'ruleConditionAttributeFinish'">
                        {{ task.conditionSymbolName }} {{ task.conditionTargetName }}
                      </span>
                      <span v-if="task.conditionAttribute == 'ruleConditionAttributeFinish'">
                        {{ task.conditionSymbolName }} {{ task.conditionTarget }}
                      </span>
                    </p>
                    <!-- <span class="result-name">{{ stage.buyerRuleDTO.ruleOutputName }}</span> -->
                  </div>
                </div>

                <div
                  v-for="(task, taskIndex) in buyerRule.buyerStageEffectTemplateDTOList"
                  :key="taskIndex"
                >
                  <div class="upgrade-info">
                    {{ task.effectTypeName }} 更新为 {{ task.effectValueName }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { upgradeRuleTypeSetting, taskOnwerSetting } from '@/utils/setting.js'

export default {
  components: {},
  filters: {
    getFinishCondition(val) {
      return upgradeRuleTypeSetting[val]
    },
    getTaskOwner(val) {
      return taskOnwerSetting[val]
    },
    setConditionTarget(val) {
      if (val == 'CHN') {
        return '中国大陆'
      } else {
        return val
      }
    }
  },
  props: {
    stage: {
      type: Object,
      default: () => {}
    },
    isCompany: {
      type: Boolean,
      default: true
    }
  },
  mounted() {}
}
</script>

<style lang="scss" scoped>
.stage-content-item {
  h3 {
    font-size: 12px;
    color: #292929;
    font-weight: 600;
    margin: 20px 0 10px 0;
  }
  .upgrade-info {
    font-size: 12px;
    margin-bottom: 10px;
  }
  .result-name {
    margin-bottom: 20px;
    margin-top: 10px;
  }
  .company-name {
    font-size: 12px;
    color: #292929;
    margin-bottom: 5px;
  }
}

.item-form-title {
  font-size: 12px;
  color: #00469c;
  font-weight: 600;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.item-form-content {
  font-size: 12px;
  color: #9a9a9a;
  margin-bottom: 10px;
}
.custom-rule-box {
  border-top: 1px solid #e8e8e8;
  .custom-rule-content {
    margin-bottom: 20px;
  }
}
.number_top_circle {
  font-size: 14px;
  border-radius: 18px;
  border: #000 1px solid;
  width: 18px;
  height: 18px;
  text-align: center;
  vertical-align: middle;
  color: #000;
  margin-right: 10px;
  justify-content: center;
  align-items: center;
  text-align: center;
  display: flex;
}
.buyer-rule-flex {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}
</style>
