<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="operetorList"
      :placeholder="$t('请选择')"
      @change="selectChange"
    >
    </mt-select>
  </div>
</template>

<script>
import bus from '@/utils/bus'
export default {
  props: {},
  data() {
    return {
      dataSource: [],
      operetorList: [
        { value: 1, text: this.$t('必选') },
        { value: 2, text: this.$t('可选') },
        { value: 3, text: this.$t('不可选') }
      ]
    }
  },
  mounted() {},
  methods: {
    selectChange(e) {
      let { itemData } = e
      bus.$emit('setDropDown', itemData, this.data.column.field, this.data.index)
    }
  }
}
</script>

<style scoped lang="scss"></style>
