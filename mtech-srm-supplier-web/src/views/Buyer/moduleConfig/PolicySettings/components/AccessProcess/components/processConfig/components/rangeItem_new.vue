<template>
  <div class="range-content">
    <!-- 公司下拉区域 -->
    <mt-form ref="selectFormRef" :model="selectFormModel">
      <mt-form-item prop="scoreNull" :label="$t('公司范围')" label-style="top">
        <mt-select
          v-model="selectFormModel.scoreNull"
          css-class="rule-element"
          :data-source="companyOptions"
          :show-clear-button="true"
          @change="selectCompany"
          :fields="{
            text: 'orgName',
            value: 'orgId'
          }"
        ></mt-select>
      </mt-form-item>
    </mt-form>

    <!-- type是1，才展示品类项 -->
    <div
      v-if="type && categoryRelationDTOList && categoryRelationDTOList.length"
      class="range-item-box"
    >
      <div class="pc-select">
        <div class="in-cell" id="in-cell">
          <mt-input v-model="category" :width="130" :placeholder="$t('请输入搜索品类')"></mt-input>
          <mt-icon
            style="width: 20px"
            name="icon_list_refuse"
            @click.native="handleClear"
          ></mt-icon>
          <mt-icon
            style="width: 20px"
            name="icon_input_search"
            @click.native="showDialog"
          ></mt-icon>
        </div>
      </div>
      <tag-item
        class="range-item"
        v-for="(rangeItem, rangeIndex) in searchedcategory"
        :key="rangeIndex"
        >{{ rangeItem.categoryName || '--' }}</tag-item
      >
    </div>
  </div>
</template>

<script>
import TagItem from './tag-item.vue'
export default {
  components: {
    TagItem
  },
  props: {
    range: {
      type: Array,
      default: () => []
    },
    type: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      category: null,
      selectFormModel: {},
      companyOptions: [], // 公司下拉集合
      categoryRelationDTOList: [], // 品类集合下拉
      searchedcategory: [] // 查询到的品类集合
    }
  },
  mounted() {
    this.companyOptions = this.range
  },
  methods: {
    // 下拉选择公司
    selectCompany(e) {
      console.log('categoryRelationDTOListcategoryRelationDTOList', e)

      this.categoryRelationDTOList = e.itemData.categoryRelationDTOList
      this.category = null
      this.searchedcategory = []
    },
    // 搜索品类
    showDialog() {
      this.searchedcategory = []
      this.categoryRelationDTOList.forEach((item) => {
        if (item.categoryName.includes(this.category)) {
          let categoryRes = {
            categoryName: item.categoryName + '-' + item.categoryCode,
            categoryCode: item.categoryCode,
            categoryId: item.categoryId
          }
          this.searchedcategory.push(categoryRes)
        }
        console.log('categoryRelationDTOListcategoryRelationDTOList', this.searchedcategory)
      })
    },
    handleClear() {
      this.category = null
      this.searchedcategory = []
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
.range-content {
  margin: 10px 0 15px 0;
  > h4 {
    font-size: 12px;
    color: #292929;
    font-weight: 600;
    margin-bottom: 5px;
  }
  .range-item-box {
    .range-item {
      margin-bottom: 5px;
    }
  }
}
</style>
