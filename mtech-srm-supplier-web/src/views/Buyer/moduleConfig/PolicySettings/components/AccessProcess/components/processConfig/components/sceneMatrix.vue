<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-data-grid
        id="Grid1"
        class="pe-edit-grid edit-grid"
        :data-source="dataSource"
        :column-data="columnData"
        ref="dataGrid"
        :allow-paging="allowPaging"
        :edit-settings="editSettings"
        :toolbar="toolbarOptions"
        :query-cell-info="customiseCell"
        @toolbarClick="clickHandler"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      ></mt-data-grid>
    </div>
  </mt-dialog>
</template>

<script>
import { dataSource, checkCol, columnData, editSettings, toolbarOptions } from '../config/index.js'

export default {
  data() {
    return {
      dataSource,
      columnData: [],
      editSettings,
      toolbarOptions,
      allowPaging: false, // 产品要求：新增/编辑时，不分页；查看时要分页
      isEditStatus: false, // 正处于编辑状态
      actionFlag: '', // 点击按钮，可能是 保存草稿-save; 提交-submit;...
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    _this() {
      return this.modalData._this
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.show()
    let selectSources = {
      currencyData: [
        {
          id: '1',
          code: 'ren',
          currencyName: this.$t('人民币')
        },
        {
          id: '2',
          code: 'mei',
          currencyName: this.$t('美元')
        }
      ]
    }
    this.columnData = checkCol.concat(columnData(selectSources, this))

    this.$nextTick(() => {
      window.gridObj = this.$refs.dataGrid
    })
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      const methodName = this.isEdit ? 'updateAccess' : 'addAccess'
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          let { accessTemplateType, accessTemplateName } = this.formInfo
          this.$API.AccessProcess[methodName]({
            accessTemplateType,
            accessTemplateName
          }).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },

    // this.$t("调整单元格的样式")
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      }
      if (args.column?.validationRules?.required) {
        args.cell.classList.add('bg-red')
      }
    },

    // this.$t("按钮点击事件")
    clickHandler(args) {
      if (args.item.id === 'DataBtn') {
        console.log(this.$t('获取到的当前数据'), window.gridObj.ejsRef.getCurrentViewRecords())
      }
      if (args.item.id === 'close') {
        window.gridObj.ejsRef.closeEdit()
      }

      if (args.item.id === 'end') {
        window.gridObj.ejsRef.endEdit()
      }
    },

    actionBegin(args) {
      console.log('actionBegin', args)
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isEditStatus = true
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
    },

    actionComplete(args) {
      console.log('actionComplete', args)
      if (args.requestType == 'save') {
        this.isEditStatus = false
        if (this.actionFlag == 'submit') {
          this.handleSubmit()
        }
      }
    },

    // 提交: 如果处于编辑状态，需要先结束掉，再获取数据
    startSubmit() {
      this.actionFlag = 'submit'
      if (this.isEditStatus) {
        window.gridObj.ejsRef.endEdit()
      } else {
        this.handleSubmit()
      }
    },

    handleSubmit() {
      console.log(this.$t('点击了提交'), window.gridObj.ejsRef.getCurrentViewRecords())
    },

    /**
     * 设置toolbar是否可点击
     * toolbarFlags:[{idName: true}] 按钮的id: true/false
     */
    enDisableToolItems(toolbarFlags) {
      for (let i in toolbarFlags) {
        this.$refs.grid.ej2Instances.toolbarModule.enableItems([i], toolbarFlags[i])
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
