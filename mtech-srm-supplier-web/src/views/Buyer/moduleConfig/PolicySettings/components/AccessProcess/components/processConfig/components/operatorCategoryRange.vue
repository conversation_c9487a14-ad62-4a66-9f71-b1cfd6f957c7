<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <!-- {{sltCompanyList}} -->
    <!-- {{selectTree}} -->
    <div class="dialog-content">
      <div>
        <h4>{{ $t('待选项') }}</h4>
        <div class="all-list-box">
          <div class="company-tree-box">
            <mt-tree-view
              key="company-tree"
              class="company-tree"
              v-if="companyTree.dataSource.length > 0"
              ref="treeViewCompany"
              :checked-nodes="checkedNodes"
              :selected-nodes="checkedNodes"
              :fields="companyTree"
              @nodeSelected="nodeSelectedCompany"
              @hook:mounted="taskTreeMounted"
            ></mt-tree-view>
          </div>
          <div class="category-tree-box">
            <!-- {{selectCategoryNodes.length}} -->
            <!-- 采用分页的品类选择 -->
            <!-- <category-select-page-or-all
              ref="treeViewCategory2"
              v-if="tenantId && showCategoryTree"
              :tenant-id="tenantId"
              :is-use-local-select-all="isUseLocalSelectAll"
              v-model="selectCategoryNodes"
              @change="nodeCheckedCategoryNew"
              @onlineSelectAllChange="onlineSelectAllChange"
            /> -->
            <!-- 虚拟滚动表格的品类选择 -->
            <!-- <CategorySelect
              ref="treeViewCategory2"
              :tenant-id="tenantId"
              v-if="tenantId && showCategoryTree"
              v-model="selectCategoryNodes"
              @change="nodeCheckedCategoryNew"
              @onlineSelectAllChange="onlineSelectAllChange"
            /> -->
            <!-- 虚拟列表的品类选择 -->
            <CategoryList
              ref="treeViewCategory2"
              v-if="tenantId && showCategoryTree"
              :tenant-id="tenantId"
              :is-use-local-select-all="isUseLocalSelectAll"
              v-model="selectCategoryNodes"
              @change="nodeCheckedCategoryNew"
              @onlineSelectAllChange="onlineSelectAllChange"
            />
            <!-- <mt-tree-view
              key="category-tree"
              class="category-tree"
              v-if="showCategoryTree"
              ref="treeViewCategory"
              :fields="categoryTree"
              :show-check-box="true"
              :checked-nodes="checkedNodesCategory"
              @nodeChecked="nodeCheckedCategory"
            ></mt-tree-view> -->
            <p v-if="!tenantId" class="category-no-data">
              {{ $t('请先选择公司') }}
            </p>
          </div>
        </div>
      </div>
      <div class="select-list-box">
        <h4>{{ $t('已选项') }}</h4>
        <div class="select-list">
          <template v-if="selectTree && selectTree.dataSource && selectTree.dataSource.length">
            <div class="select-tree">
              <mt-tree-view
                key="select-tree"
                v-if="showSelectTree"
                ref="treeViewSelect"
                :fields="selectTree"
              ></mt-tree-view>
            </div>
            <p class="clear-btn" @click="clearSltCompany">
              {{ $t('清空选择') }}
            </p>
          </template>
          <p v-else class="category-no-data">{{ $t('请先选择公司和品类') }}</p>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
// import CategorySelectPageOrAll from '@/components/select/CategorySelectPageOrAll.vue'
// import CategorySelect from '@/components/select/categorySelectVxe.vue'
import CategoryList from '@/components/select/CategoryList.vue'

let isInit = true
export default {
  components: { CategoryList },
  data() {
    return {
      isUseLocalSelectAll: 1, // 是否使用系统的全选按钮
      onlineSelectAllData: null,
      selectCategoryNodes: [],
      tenantId: null,
      companyTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      selectTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      categoryTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      checkedNodesCategory: [],
      checkedNodesObj: [],
      sltCompanyList: [],
      showCompanyTree: false, // treeview数据不更新。。
      showCategoryTree: false, // treeview数据不更新。。
      showSelectTree: false, // treeview数据不更新。。
      currentCompany: {},
      checkedNodes: [],

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存范围配置') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    accessTemplate() {
      return this.modalData.accessTemplate
    },
    rangeList() {
      return this.modalData.rangeList
    }
  },
  mounted() {
    if (this.isEdit) {
      isInit = true
    }
    this.getCompanyTree()
    this.show()
    console.log('init', this.rangeList)
  },
  methods: {
    taskTreeMounted() {
      this.$refs.treeViewCompany.$refs.ejsRef.expandAll()
    },
    // 数据回显
    initData() {
      if (this.rangeList && this.rangeList.length) {
        // const treeRef = this.$refs.treeViewCompany.ejsRef;
        this.rangeList.forEach((item) => {
          // let node = treeRef.getNode(item.orgId);
          this.sltCompanyList.push({
            id: item.orgId,
            name: item.orgName,
            text: item.orgName,
            children:
              item.categoryRelationDTOList.map((cItem) => {
                return {
                  ...cItem,
                  name: cItem.categoryName,
                  id: cItem.categoryId
                }
              }) || []
          })
        })
        if (this.sltCompanyList.length) {
          const { id } = this.sltCompanyList[0]
          // this.selectedNodesCompany = [id];
          this.checkedNodes.length = 0
          this.checkedNodes.push(id)
        }
        // this.$nextTick(() => {
        //   this.checkedNodes = this.rangeList.map((item) => {
        //     return item.orgId;
        //   });
        //   treeRef.expandAll();
        // });
      }
    },

    getCompanyTree() {
      // this.$API.supplierInvitation
      //   .findOrganizationCompanyTreeByAccount({
      //     accountId: 0,
      //   })
      this.$API.supplierInvitation
        .findOrganizationCompanyTreeByAccount3({
          orgLevelCode: 'ORG02',
          orgType: 'ORG001PRO'
        })
        .then((res) => {
          const { code, data } = res
          if (code == 200 && data) {
            this.$set(this.companyTree, 'dataSource', data)
            this.showCompanyTree = true
            if (this.isEdit) {
              this.$nextTick(() => {
                this.initData()
              })
            }
            // this.$nextTick(() => {
            //   const treeViewRef =
            //     this.$refs.treeViewCompany && this.$refs.treeViewCompany.ejsRef;
            //   treeViewRef && treeViewRef.expandAll();
            // });
          }
        })
    },

    // 设置品类树的选中状态
    setCategoryTreeChecked() {
      const { id: currentCompanyId } = this.currentCompany
      const dataList = this.selectTree.dataSource || []
      let index2 = -1
      dataList.forEach((item, index) => {
        if (item.id === currentCompanyId) {
          index2 = index
        }
      })
      if (index2 > -1 && dataList[index2] && dataList[index2].children.length > 0) {
        this.selectCategoryNodes = JSON.parse(JSON.stringify(dataList[index2].children))
      } else {
        this.selectCategoryNodes = []
      }
    },

    /**
     * id 公司id
     */
    getCategoryTree(id) {
      this.showCategoryTree = false
      this.tenantId = id
      this.$nextTick(() => {
        if (this.isEdit && isInit) {
          //设置初始值  首次打开弹窗的时候走这里
          isInit = false
          if (this.rangeList[0] && this.rangeList[0].categoryRelationDTOList) {
            //selectCategoryNodes
            this.sltCompanyList
            this.sltCompanyList.forEach((item, index) => {
              // let checkedNodesCategory =
              //   item.categoryRelationDTOList &&
              //   item.categoryRelationDTOList.length > 0
              //     ? item.categoryRelationDTOList.map(
              //         (cItem) => cItem.categoryId
              //       )
              //     : [];
              if (index === 0) {
                this.selectCategoryNodes = item.children
                // this.checkedNodesCategory = checkedNodesCategory;
                // this.checkedNodesObj.push({
                //   id: item.orgId,
                //   nodes: checkedNodesCategory,
                // });
              } else {
                this.checkedNodesObj.push({
                  id: item.orgId,
                  nodes: []
                })
                console.log('3444444444444444444', this.checkedNodesObj)
              }
            })
          }
          // console.log("66666", this.checkedNodesCategory);
        } else {
          this.setCategoryTreeChecked()
        }
        const treeViewRef = this.$refs.treeViewCategory && this.$refs.treeViewCategory.ejsRef
        treeViewRef && treeViewRef.expandAll()
      })

      setTimeout(() => {
        this.showCategoryTree = true
      }, 200)
    },

    nodeSelectedCompany(args) {
      const { nodeData } = args

      if (!nodeData) return
      // 获取品类树
      this.currentCompany = nodeData
      this.getCategoryTree(nodeData.id)
      let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)
      index === -1 && this.sltCompanyList.push(this.currentCompany)
    },

    nodeCheckedCategory() {
      /**
       * 返回的data数组中，第一个应该是当前点击的currentNode
       * 如果当前动作是check,如果currentNode的hasChildren是true，找到他下面的叶子节点，禁用
       * 如果currentNode的hasChildren是false，，选中的nodes里如果有hasChildren是true的，禁用他的子节点
       * 如果当前动作是uncheck：取消所有的禁用，...
       */
      this.showSelectTree = false //卸载掉树的dom节点
      const treeViewRef = this.$refs.treeViewCategory.ejsRef
      let allCheckedNodes = treeViewRef.getAllCheckedNodes() //可替换
      // 保存当前公司的品类选择

      //如果当前公司在里面是没有值的   就推进去
      let index = this.checkedNodesObj.findIndex((item) => item.id === this.currentCompany.id)
      if (index === -1) {
        this.checkedNodesObj.push({
          id: this.currentCompany.id,
          nodes: allCheckedNodes
        })
      } else {
        //如果有的话，就变更
        this.$set(this.checkedNodesObj[index], 'nodes', allCheckedNodes)
      }
      console.log(this.checkedNodesObj, '99999')

      // 叶子节点的禁用逻辑
      // if (action === "check" && currentNode.hasChildren) {
      //   let currentTree = treeViewRef.getTreeData(currentNode.id);//
      //    console.log(currentTree,'获取当前id的数据对象')
      //   let leafs = this.getLeaf(currentTree);
      //  treeViewRef.disableNodes(leafs);
      // } else {
      //    action === "uncheck" && treeViewRef.enableNodes(disabledNodes);
      //   let sltNodes = allCheckedNodes.map((id) => {
      //     return treeViewRef.getNode(id);
      //   });
      //   sltNodes.forEach((node) => {
      //     if (node.hasChildren) {
      //       let currentTree = treeViewRef.getTreeData(node.id);
      //       let leafs = this.getLeaf(currentTree);
      //       treeViewRef.disableNodes(leafs);
      //     }
      //   });
      // }

      let nodes = this.getSltNodes()
      if (nodes) {
        this.$set(this.currentCompany, 'children', nodes)
        let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)
        console.log(this.currentCompany, '---------')
        this.sltCompanyList[index] = {
          ...this.currentCompany,
          expanded: true
        }

        let sltTreeData = this.sltCompanyList
          .filter((item) => item.children && item.children.length)
          .map((node) => {
            return {
              id: node.id,
              name: node.text,
              children: node.children
            }
          })
        this.$set(this.selectTree, 'dataSource', sltTreeData)
        setTimeout(() => {
          this.showSelectTree = true
          // this.$nextTick(() => {
          // const treeViewSelectRef =
          //   this.$refs.treeViewSelect && this.$refs.treeViewSelect.ejsRef;
          // treeViewSelectRef && treeViewSelectRef.expandAll();
          // });
        }, 200)
      }
    },
    onlineSelectAllChange(val) {
      this.onlineSelectAllData = val
      console.log('this.search', this.checkedNodesObj)
    },
    nodeCheckedCategoryNew(args) {
      console.log('argsargsargs', args)
      this.showSelectTree = false //卸载掉树的dom节点
      const newRef = this.$refs.treeViewCategory2
      let allCheckedNodes = newRef.getAllCheckedNodes() // 获取到所有选择的节点
      //如果当前公司在里面是没有值的   就推进去
      console.log('this.checkedNodesObj', this.checkedNodesObj)
      let index = this.checkedNodesObj.findIndex((item) => item.id === this.currentCompany.id)
      if (index === -1) {
        this.checkedNodesObj.push({
          id: this.currentCompany.id,
          nodes: allCheckedNodes
        })
      } else {
        //如果有的话，就变更
        this.$set(this.checkedNodesObj[index], 'nodes', allCheckedNodes)
      }
      let nodes = args
      if (nodes) {
        console.log('childrenchildren', nodes)
        this.$set(this.currentCompany, 'children', nodes)
        let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)
        this.sltCompanyList[index] = {
          ...this.currentCompany,
          expanded: true
        }
        let sltTreeData = this.sltCompanyList
          .filter((item) => item.children && item.children.length > 0)
          .map((node) => {
            return {
              id: node.id,
              name: node.text,
              children: node.children
            }
          })
        this.$set(this.selectTree, 'dataSource', sltTreeData)
        setTimeout(() => {
          this.showSelectTree = true
        }, 200)
      }
    },

    /**
     * 选中的节点，保存为树形
     * 非叶子节点的isChecked表示该节点是否选中（他的叶子节点被选中了部分，则是false）
     */
    getSltNodes() {
      const treeViewRef = this.$refs.treeViewCategory.ejsRef
      let checkedId = treeViewRef.getAllCheckedNodes()
      console.log(checkedId, 'checkedIdcheckedId')
      let sltParentNode = []
      if (checkedId && checkedId.length) {
        const { dataSource } = this.categoryTree
        let tree = this.setCheckStatus(dataSource, checkedId)
        const getSltParentNode = (tree) => {
          if (tree && tree.length) {
            tree.forEach((node) => {
              if (node.isChecked) {
                delete node.children
                sltParentNode.push(node)
              } else if (node.children && node.children.length) {
                getSltParentNode(node.children)
              }
            })
          }
        }
        getSltParentNode(tree)
      }
      return sltParentNode || []
    },

    // 加状态
    setCheckStatus(tree, checkedId) {
      if (tree && tree.length) {
        tree.forEach((node) => {
          node.isChecked = false
          if (node.children && node.children.length) {
            let childrenIds = node.children.map((child) => {
              return child.id
            })
            node.isChecked = this.isContainArr(checkedId, childrenIds)
            this.setCheckStatus(node.children, checkedId)
          } else {
            let index = checkedId.findIndex((id) => id === node.id)
            index > -1 && (node.isChecked = true)
          }
        })
      }
      return tree
    },

    isContainArr(parent, child) {
      return child.every((item) => {
        return parent.some((v) => {
          return item == v
        })
      })
    },

    // 获取叶子节点的id
    getLeaf(tree) {
      const leaf = []
      const getChild = (tree) => {
        tree &&
          tree.forEach((item) => {
            if (item.children && item.children.length) {
              getChild(item.children)
            } else {
              let index = leaf.findIndex((leafItem) => leafItem === item.id)
              index === -1 && leaf.push(item.id)
            }
          })
      }
      getChild(tree)
      return leaf
    },

    // 清空选择
    clearSltCompany() {
      this.$refs.treeViewCategory2.clearAll()
      this.tenantId = null
      this.checkedNodes = []
      this.checkedNodesObj = []
      this.currentCompany = {}
      this.sltCompanyList = []
      this.$set(this.selectTree, 'dataSource', [])
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      const { id: accessTemplateId } = this.accessTemplate
      const { dataSource } = this.selectTree
      let companyCategoryDTOList = []
      console.log(this.selectTree, 'this.selectTree')
      dataSource.forEach((item) => {
        const { id, name, orgCode } = item
        let companyInfo = {
          orgCode: orgCode,
          orgId: id,
          orgName: name
        }
        console.log('this.onlineSelectAllData', this.onlineSelectAllData)
        // if (this.onlineSelectAllData === 1) {
        //   companyCategoryDTOList.push({
        //     ...companyInfo
        //   })
        // }
        const { children } = item
        if (children && children.length) {
          children.forEach((child) => {
            const { id, name, categoryCode, allChoose = 0 } = child
            let categoryInfo = {
              allChoose,
              categoryCode: categoryCode,
              categoryId: id,
              categoryName: name
            }
            companyCategoryDTOList.push({
              ...companyInfo,
              ...categoryInfo
            })
          })
        }
      })
      let params = {
        accessTemplateId,
        companyCategoryDTOList
      }
      if (companyCategoryDTOList && companyCategoryDTOList.length) {
        this.$API.AccessProcess['updateAccessScope'](params).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function')
          }
        })
      } else {
        this.$toast({ content: this.$t('请先选择公司和品类'), type: 'error' })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  display: flex;
  h4 {
    font-size: 14px;
    color: #292929;
    font-weight: bold;
    margin: 20px 0 10px 0;
  }
  .all-list-box {
    display: flex;
    background: #fff;
    border: 1px solid #e8e8e8;
    min-height: 300px;
  }
  .company-tree-box {
    width: 300px;
    min-height: 300px;
    /deep/ .mt-tree-view {
      width: 100%;
    }
  }

  .category-tree-box {
    flex: 1;
    width: 300px;
    min-height: 300px;
    border-left: 1px solid #e8e8e8;
    /deep/ .mt-tree-view {
      width: 100%;
    }
  }
  .select-list-box {
    flex: 1;
    margin-left: 20px;
    .select-list {
      width: 248px;
      min-height: 300px;
      max-height: 390px;
      overflow-y: auto;
      background: #fff;
      border: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;
      .select-tree {
        flex: 1;
        /deep/ .mt-tree-view {
          width: 100%;
        }
      }
    }
    .clear-btn {
      font-size: 14px;
      font-weight: bold;
      color: #00469c;
      text-align: center;
      margin: 20px 0 38px 0;
      cursor: pointer;
    }
  }
  .category-no-data {
    font-size: 14px;
    color: #9a9a9a;
    padding-top: 20px;
    text-align: center;
  }
}
</style>
