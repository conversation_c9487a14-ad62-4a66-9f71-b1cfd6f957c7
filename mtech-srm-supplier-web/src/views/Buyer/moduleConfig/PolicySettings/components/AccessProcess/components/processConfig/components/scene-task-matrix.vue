<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- 阶段为行数据 列为新增的场景  -->
      <mt-data-grid
        id="Grid"
        ref="dataGrid"
        :edit-settings="editSettings"
        :data-source="dataSource"
        :column-data="columnData"
        height="350"
        :query-cell-info="queryCellInfoEvent"
        @actionComplete="actionComplete"
        @change="chagneDataGrid"
      ></mt-data-grid>
    </div>
  </mt-dialog>
</template>

<script>
import bus from '@/utils/bus'
import {
  editSettings,
  editSettings4Display,
  bodyEdit,
  filterData,
  fiterSimilarRow,
  forCheckAllInArray
} from '../config/index.js'

export default {
  data() {
    return {
      dataSource: [],
      columnData: [],
      editSettings,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    _this() {
      return this.modalData._this
    },
    header() {
      return this.modalData.title
    },
    // 阶段为行数据
    accessTemplate() {
      return this.modalData.accessTemplate
    },
    isDisplay() {
      return this.modalData.isDisplay
    }
  },
  async created() {
    let { id: accessTemplateId } = this.accessTemplate
    let sceneTaskDetail = await this.querySceneTaskDetail(accessTemplateId)
    this.editSettings = this.isDisplay ? editSettings4Display : editSettings
    this.buttons = this.isDisplay
      ? [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          }
        ]
      : [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
    if (sceneTaskDetail.length === 0) {
      this.$emit('confirm-function')
      this.$toast({
        content: this.$t('查询场景任务详情失败，请先添加！'),
        type: 'success'
      })
      return
    }
    // 渲染column数据
    this.columnData = this.renderColumnData(sceneTaskDetail)

    // 渲染dataSource数据 扁平化数据
    this.dataSource = this.floatData(sceneTaskDetail)
    // dataSourceBak = JSON.parse(JSON.stringify(this.dataSource))
  },
  mounted() {
    this.show()
    this.bindEvents()
  },
  methods: {
    endEdit() {
      this.$refs.dataGrid.ejsRef.endEdit()
    },

    actionComplete(event) {
      console.log('actionComplete', event, this.$refs.dataGrid.ejsRef)
      const { action, requestType, data, previousData, rowIndex } = event
      if (action === 'edit' && requestType === 'save') {
        // 判断数据是否能正常
        this.diffJudge(data, previousData, rowIndex)
      }
    },

    // 新老数据对比
    diffJudge() {
      let dataSource = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()
      // 不做校验了
      // let allKeysArrays = Object.keys(data);
      // let keyArrays = allKeysArrays.filter(
      //   (kItem) => kItem.indexOf("status") >= 0
      // );
      // let pass = true;

      // for (let item of keyArrays) {
      //   if (data[item] !== previousData[item]) {
      //     pass = judgeToast(
      //       { value: data[item] },
      //       { value: previousData[item] },
      //       this
      //     );
      //     if (!pass) {
      //       dataSource.splice(
      //         rowIndex,
      //         1,
      //         Object.assign(data, { [item]: previousData[item] })
      //       );
      //     }
      //   }
      // }

      this.dataSource = JSON.parse(JSON.stringify(dataSource))
      // dataSourceBak = JSON.parse(JSON.stringify(dataSource))
    },

    chagneDataGrid(event) {
      // const { index } = event;
      // this.gridDataSource.splice(index, 1, {
      //   ...this.gridDataSource[index],
      //   ...event,
      // });
      // this.$refs.dataGrid.ejsRef.updateRow(index, event);
      console.log('chagneDataGrid', event)
    },
    bindEvents() {
      bus.$off('setDropDown2')
      bus.$on('setDropDown2', (data) => {
        console.log(data)
      })
      setTimeout(() => {
        !this.isDisplay && this.$refs.dataGrid.ejsRef.selectRow(0, true)
        !this.isDisplay && this.$refs.dataGrid.ejsRef.startEdit()
      }, 600)
    },

    queryCellInfoEvent() {
      // console.log(args, args.column.field);
      // 一合并列 就tmd编辑报错
      // const { column, data } = args;
      // if (column.field === "stageName") {
      //   args.rowSpan = data.rowSpan;
      //   args.colspan = 1;
      // }
    },
    // 单元格数据
    floatData(sceneTaskDetail) {
      sceneTaskDetail = sceneTaskDetail.map((item) => {
        item['status' + item.sceneDefineId] = item.status
        item.rowSpan = 1
        return item
      })
      // 拆分合并同一任务
      let stageList = filterData('stageTemplateId', sceneTaskDetail)
      let similarRow = fiterSimilarRow('stageTaskTemplateId', stageList)
      console.log('similarRow', similarRow)
      return similarRow
    },
    // 渲染列数组
    renderColumnData(sceneTaskDetail) {
      let columnList = [
        {
          width: '150',
          field: 'stageName',
          headerText: this.$t('阶段'),
          allowEditing: false
        },
        {
          width: '150',
          field: 'taskName',
          headerText: this.$t('任务名称'),
          allowEditing: false
        }
      ]
      let sceneDefineIdObj = {}
      sceneTaskDetail.forEach((item) => {
        if (!sceneDefineIdObj[item.sceneDefineId]) {
          // 新增场景命名 方便再列上展示名字
          item[`sceneName${item.sceneDefineId}`] = item.sceneName
          item[`sceneNameId${item.sceneDefineId}`] = item.sceneDefineId
          let columnItem = bodyEdit(item, this)
          columnList.push(columnItem)
          sceneDefineIdObj[item.sceneDefineId] = true
        }
      })

      console.log('columnList', columnList)
      return columnList
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    // 设置改变得datasource
    setDatasource(changedRecords) {
      let { dataSource } = this

      let dataSourceTmp = dataSource.map((item) => {
        let allKeysArrays = Object.keys(item)
        let keyArrays = Object.keys(item).filter(
          (kItem) =>
            kItem.indexOf('detailId') >= 0 ||
            kItem.indexOf('sceneName') >= 0 ||
            kItem.indexOf('sceneNameId') >= 0 ||
            kItem.indexOf('sceneStageTemplateId') >= 0 ||
            kItem.indexOf('stageTaskTemplateId') >= 0
        )

        console.log('keyArrays', keyArrays)

        for (let cItem of changedRecords) {
          if (forCheckAllInArray(keyArrays, item, cItem)) {
            let statusArray = allKeysArrays.filter((kItem) => kItem.indexOf('status') >= 0)
            statusArray.forEach((sItem) => {
              item[sItem] = cItem[sItem]
            })
          }
        }
        return item
      })
      return dataSourceTmp
    },
    // 拆分 单行数据为 单元格数据
    getGridItem() {
      console.log('getGridItem', this.dataSource)
      let reg_g = /sceneNameId(.+)/
      let rowItem = []

      // 获取sceneNameId 数组
      let sceneIdArray = Object.keys(this.dataSource[0])
        .filter((kItem) => kItem.indexOf('sceneNameId') >= 0)
        .map((sItem) => {
          let result = sItem.match(reg_g)
          return !!result && result[1] ? result[1] : ''
        })

      this.dataSource.forEach((item) => {
        sceneIdArray.forEach((sceneId) => {
          rowItem.push({
            id: item[`detailId${sceneId}`],
            remark: '',
            sceneDefineId: item[`sceneNameId${sceneId}`],
            sceneName: item[`sceneName${sceneId}`],
            sceneStageTemplateId: item.sceneStageTemplateId,
            stageName: item.stageName,
            stageTaskTemplateId: item.stageTaskTemplateId,
            stageTemplateId: item.stageTemplateId,
            status: item[`status${sceneId}`],
            taskName: item.taskName
          })
        })
      })
      console.log('finally', rowItem)
      return rowItem
    },
    confirm() {
      this.endEdit()
      // let BatchChanges = this.$refs.dataGrid.ejsInstances.getBatchChanges();
      // console.log(
      //   "setDataSource",
      //   BatchChanges,
      //   this.$refs.dataGrid.ejsInstances
      // );
      // let { changedRecords } = BatchChanges;
      // if (changedRecords.length === 0) {
      //   this.$toast({
      //     content: "请修改状态再保存！",
      //     type: "warning",
      //   });
      //   return;
      // }
      let { id: accessTemplateId } = this.accessTemplate
      // let list = this.setDatasource(changedRecords);
      let list = this.getGridItem()
      let fullParams = {
        accessTemplateId,
        list
      }
      this.$API.AccessProcess['updateSceneTaskUpdate'](fullParams).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$emit('confirm-function')
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 查询场景任务矩阵
    querySceneTaskDetail(accessTemplateId) {
      return this.$API.AccessProcess['querySceneTaskDetail'](accessTemplateId)
        .then((res) => {
          if (res.code == 200) {
            return res.data
          } else {
            this.$toast({
              content: res.msg || this.$t('系统异常'),
              type: 'error'
            })
            return []
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
          return []
        })
    },
    // 状态（0默认，1适用，2不适用）
    getRealStatus(status) {
      let result = 0
      switch (status) {
        case 1:
          result = 1
          break
        case 2:
          result = 0
          break
        case 3:
          result = 2
          break
      }
      return result
    }
  },
  beforeDestroy() {
    bus.$off('setDropDown2')
  }
}
</script>

<style lang="scss">
.dialog-content {
  margin-top: 20px;
}
.circle {
  width: 12px;
  height: 12px;
  background: rgba(0, 70, 156, 1);
  border-radius: 50%;
}
.trangle {
  width: 0;
  height: 0;
  border-bottom: 12px solid #8acc40;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
}
.cance {
  font-size: 12px;
  color: #f53f3f;
}
</style>
<style lang="scss" scoped></style>
