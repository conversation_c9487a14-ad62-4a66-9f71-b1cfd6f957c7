<!-- 认证策略 -->
<template>
  <div class="access-process">
    <process-config method="authent"></process-config>
  </div>
</template>

<script>
import ProcessConfig from '../AccessProcess/components/processConfig/index.vue'

export default {
  components: {
    ProcessConfig
  },
  data() {
    return {}
  },
  methods: {},
  mounted() {}
}
</script>

<style lang="scss" scoped>
.access-process {
  overflow: auto;
  #tabs,
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
