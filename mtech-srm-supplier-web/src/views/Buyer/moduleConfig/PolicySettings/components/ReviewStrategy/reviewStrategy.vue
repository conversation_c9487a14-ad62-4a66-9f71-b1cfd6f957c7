<template>
  <div class="access-content">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.performanceScoreMy.myQueryHistory)
    }
  },
  methods: {
    handleClickCellTitle(e) {
      console.log(e)
    }
  }
}
</script>

<style lang="scss" scoped>
.access-content {
  height: 100%;
}
</style>
