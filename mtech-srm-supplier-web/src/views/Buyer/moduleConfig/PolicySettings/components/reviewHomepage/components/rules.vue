<template>
  <div class="rule-item">
    <div class="title">
      <span style="color: #f44336">*</span>{{ $t('规则') }}{{ ruleIndex | filterRule }}
    </div>

    <div class="rule-line fbox" v-for="(e, index) of item.strategyRuleInputs" :key="e.id">
      <div class="rule-input w60">
        <template v-if="index === 0">
          <mt-input v-model="value" height="40" :disabled="true"></mt-input>
        </template>

        <template v-else>
          <!-- 逻辑符号（0，无；1，且；2，或） -->
          <mt-select
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionType"
            :value="item.strategyRuleInputs[index].conditionType"
            :allow-filtering="false"
            :data-source="rulesObj.conditionType"
            :placeholder="$t('请选择条件')"
            width="80"
            :fields="{ text: 'name', value: 'type' }"
          ></mt-select>
        </template>
      </div>
      <div class="rule-input">
        <!-- 规则类型（1，最近；2，本次；3，单次） -->
        <mt-select
          :disabled="item.editorIdstatus"
          v-model="item.strategyRuleInputs[index].conditionObject"
          :value="item.strategyRuleInputs[index].conditionObject"
          :allow-filtering="false"
          :data-source="rulesObj.conditionObject"
          height="40"
          width="80"
          placeholder
          :fields="{ text: 'name', value: 'type' }"
          @change="changePeriod($event, index)"
        ></mt-select>
      </div>
      <!-- 最近  点击时得切换 -->
      <template v-if="item.strategyRuleInputs[index].conditionObject === '1'">
        <div class="rule-input w80 relative">
          <mt-input
            style="margin-top: 10px"
            :max-length="1"
            oninput="value=value.replace(/[^\d]/g,'')"
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionCycleValue"
            height="40"
            :placeholder="$t('请填值')"
            class="number-item"
          ></mt-input>
        </div>
        <div class="rule-input w52">
          <!-- 周期类型（1，月；2，季度；3，年） -->
          <mt-select
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionCycleType"
            :value="item.strategyRuleInputs[index].conditionCycleType"
            :allow-filtering="false"
            :data-source="rulesObj.conditionCycleType"
            height="40"
            :placeholder="$t('请选择周期类型')"
            :fields="{ text: 'name', value: 'type' }"
          ></mt-select>
        </div>
        &nbsp; &nbsp;
        <div class="rule-input w108">
          <mt-select
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionAttribute"
            :value="item.strategyRuleInputs[index].conditionAttribute"
            :allow-filtering="false"
            :data-source="rulesObj.conditionAttribute"
            :placeholder="$t('请选择')"
            height="40"
            :fields="{ text: 'name', value: 'type' }"
          ></mt-select>
        </div>
        <div class="rule-input w108">
          <mt-select
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionAccessType"
            :value="item.strategyRuleInputs[index].conditionAccessType"
            :allow-filtering="false"
            :data-source="rulesObj.conditionAccessType"
            :placeholder="$t('请选择')"
            height="40"
            :fields="{ text: 'name', value: 'type' }"
          ></mt-select>
        </div>
        <div class="rule-input w48">
          <!-- 判断符号 -->
          <mt-select
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionSymbol"
            :value="item.strategyRuleInputs[index].conditionSymbol"
            :allow-filtering="false"
            :data-source="rulesObj.conditionSymbol"
            :placeholder="$t('请选择判断符号')"
            height="40"
            :fields="{ text: 'name', value: 'type' }"
          ></mt-select>
        </div>
        <div class="rule-input relative w108 inputone">
          <mt-input
            style="margin-top: 10px"
            :max-length="4"
            oninput="value=value.replace(/[^\d]/g,'')"
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionTarget"
            height="40"
            :placeholder="$t('请输入比较值')"
            class="number-item"
          ></mt-input>
        </div>
      </template>
      <!-- 本次点击时得切换 -->
      <template v-if="item.strategyRuleInputs[index].conditionObject === '2'">
        <div class="rule-input w272">
          <mt-select
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionAttribute"
            :allow-filtering="false"
            :data-source="rulesObj.conditionAttribute"
            :placeholder="$t('请选择考核类型')"
            height="40"
            :fields="{ text: 'name', value: 'type' }"
          ></mt-select>
        </div>
        <div class="rule-input w272">
          <mt-select
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionAccessType"
            :value="item.strategyRuleInputs[index].conditionAccessType"
            :allow-filtering="false"
            :data-source="rulesObj.conditionAccessType"
            :placeholder="$t('请选择')"
            height="40"
            :fields="{ text: 'name', value: 'type' }"
          ></mt-select>
        </div>
        <!-- 分数 类型维度 -->
        <div class="rule-input w48">
          <!-- 判断符号 -->
          <mt-select
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionSymbol"
            :value="item.strategyRuleInputs[index].conditionSymbol"
            :allow-filtering="false"
            :data-source="rulesObj.conditionSymbol"
            :placeholder="$t('请选择判断符号')"
            height="40"
            :fields="{ text: 'name', value: 'type' }"
          ></mt-select>
        </div>
        <div class="rule-input relative w108">
          <mt-input
            oninput="value=value.replace(/[^\d]/g,'')"
            :max-length="4"
            style="margin-top: 10px"
            :disabled="item.editorIdstatus"
            v-model="item.strategyRuleInputs[index].conditionTarget"
            height="40"
            :placeholder="$t('请输入比较值')"
            class="number-item"
          ></mt-input>
        </div>
      </template>
      <!-- 删除规则 -->
      <div class="rule-input w12 clear-btn" @click="deleteRule(index)" v-if="!disabled">
        <mt-icon name="icon_input_clear" />
      </div>
    </div>

    <div class="add-rule fbox" v-if="item.strategyRuleInputs.length < 20" v-show="!disabled">
      <div class="left-icon" @click="addRule">
        <mt-icon name="icon_card_plus" />
      </div>
      <div class="right-txt" @click="addRule">{{ $t('添加条件') }}</div>
    </div>

    <!-- <div class="output" v-for="(e, index) in item.outputTypeList" :key="'a' + index">
      <div class="output-title">{{ $t("输出") }}</div>
      <div class="output-select">
        <mt-select
          style="width: 98%"
          v-model="e.outputType"
          :disabled="item.editorIdstatus"
          :value="e.outputType"
          :allow-filtering="false"
          :data-source="outputTypeCopy(index)"
          :placeholder="$t('请选择')"
          height="40"
          :fields="{ text: 'name', value: 'type' }"
    ></mt-select>-->
    <!-- 删除输出 -->
    <!-- <span class="rule-input w12 clear-btn" @click="deleteoutput(index)" v-show="!disabled">
          <mt-icon name="icon_input_clear" />
        </span>
      </div>
    </div>-->
    <!-- <div class="add-rule fbox" v-if="item.outputTypeList.length < 5" v-show="!disabled">
      <div class="left-icon" @click="addOutput">
        <mt-icon name="icon_card_plus" />
      </div>
      <div class="right-txt" @click="addOutput" :disabled="item.editorIdstatus">{{ $t("添加输出") }}</div>
    </div>-->
    <!-- 触发方式 -->
    <!-- <div class="output-title">{{ $t("触发方式") }}</div>
    <div class="output-select">
      <mt-select
        style="width: 98%"
        :disabled="item.editorIdstatus"
        :allow-filtering="false"
        :data-source="rulesObj.equalType"
        :placeholder="$t('请选择')"
        height="40"
        :fields="{ text: 'name', value: 'type' }"
      ></mt-select>
    </div>-->

    <!-- 备注 -->
    <div class="remark-box fbox">
      <div class="remark-title">
        <span style="color: #f44336">*</span>{{ this.$t('规则描述') }}:
      </div>
      <div class="remark-input" :class="{ 'txt-box': disableRemark, 'input-box': !disableRemark }">
        <!-- 展示绑定 -->
        <template v-if="disableRemark">
          <div class="remark-txt">{{ item.remark }}</div>
        </template>
        <!-- 填写绑定 -->
        <template v-if="!disableRemark">
          <mt-input
            :max-length="200"
            :disabled="item.editorIdstatus"
            v-model="item.remark"
            :multiline="true"
            :rows="3"
            type="text"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </template>
      </div>
      <!-- 编辑按钮 -->
      <div class="icon-edit" @click="openEdit" v-show="!disabled">
        <template v-if="disableRemark">
          <mt-icon name="icon_Editor" />
        </template>
        <template v-if="!disableRemark">
          <mt-icon name="icon_V" />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import {
  conditionType,
  conditionObject,
  conditionCycleType,
  conditionAttribute,
  conditionSymbol,
  equalType,
  // outputType,
  filterValue,
  conditionAccessType
} from '../config/index'
export default {
  data() {
    return {
      // 规则数组
      rulesObj: {},
      value: this.$t('当'),
      values: this.$t('次'),
      // outputType: 0,
      disableRemark: true
    }
  },
  props: {
    ruleIndex: {
      type: Number,
      default: 1
    },
    item: {
      type: Object,
      default() {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  filters: {
    filterRule: function (value) {
      let tmpValue = filterValue(value)
      return tmpValue
    }
  },
  // computed: {
  //   outputTypeCopy() {
  //     return function (index) {
  //       let arr = JSON.parse(JSON.stringify(outputType));
  //       let selectedArr = [];
  //       this.item.outputTypeList.forEach((e, i) => {
  //         if (index != i) {
  //           outputType.forEach((e1) => {
  //             if (e1.type === e.outputType) {
  //               selectedArr.push(e1.mold);
  //             }
  //           });
  //         }
  //       });
  //       for (let i = 0; i < arr.length; i++) {
  //         if (selectedArr.indexOf(arr[i].mold) != -1) {
  //           arr.splice(i, 1);
  //           i--;
  //         }
  //       }
  //       return arr;
  //     };
  //   },
  // },
  created() {
    // 最近的
    // let mixDimensionLight = this.mixmixDimensionLight();
    // 连续的
    // let mixDimensionLightNum = this.mixDimensionLightNum();

    this.rulesObj = {
      conditionType,
      conditionObject,
      conditionCycleType,
      conditionAttribute,
      conditionAccessType,
      conditionSymbol,
      equalType
      // outputType,
      // mixDimensionLight,
      // mixDimensionLightNum,
    }
  },
  methods: {
    // 添加输出
    addOutput() {
      if (this.item.outputTypeList.length < 5) {
        this.item.outputTypeList.push({
          outputType: ''
        })
      }
    },
    // 添加规则
    addRule() {
      if (this.item.strategyRuleInputs.length < 20) {
        this.item.strategyRuleInputs.push({
          id: (Math.random() * 100000).toFixed(0),
          conditionObject: '1'
        })
      }
    },
    // 删除规则
    deleteRule(index) {
      if (index == 0) {
        this.$toast({ content: this.$t('请保留一种条件'), type: 'warning' })
      } else {
        this.item.strategyRuleInputs.splice(index, 1)
        if (this.item.strategyRuleInputs.length === 0) {
          // 清除规则组
          this.$emit('clearGroup', this.ruleIndex)
        }
        this.$forceUpdate()
      }
    },
    // 删除输出
    deleteoutput(index) {
      this.item.outputTypeList.splice(index, 1)
    },
    // 修改 最近 连续 单次
    changePeriod(e, index) {
      // 修改上面的维度
      let { itemData } = e
      // 单次
      if (itemData.conditionObject === '2') {
        // 组件vif显示尽然默认赋初值null 坑 重置赋值
        setTimeout(() => {
          this.$set(this.item.strategyRuleInputs, index, {
            ...this.item.strategyRuleInputs[index],
            conditionObject: '2',
            conditionSymbol: 0,
            equalType: 0,
            score: 0
          })
        }, 500)
      }
    },
    // 开始编辑 remark
    openEdit() {
      this.disableRemark = !this.disableRemark
    }
    // // 混合维度和灯内容
    // mixmixDimensionLight() {
    //   let mixArray = [];
    //   conditionAttribute.forEach((dv) => {
    //     lampColor.forEach((cv) => {
    //       mixArray.push({
    //         lObj: dv,
    //         rObj: cv,
    //         conditionObject: dv.conditionObject + "" + cv.conditionObject,
    //         name: dv.name + "" + cv.name,
    //       });
    //     });
    //   });
    //   return mixArray;
    // },
    // mixDimensionLightNum() {
    //   let mixArray = [];
    //   conditionAttribute.forEach((dv) => {
    //     lampColor.forEach((cv) => {
    //       mixArray.push({
    //         lObj: dv,
    //         rObj: cv,
    //         conditionObject: dv.conditionObject + "" + cv.conditionObject,
    //         name: dv.name + "" + cv.name,
    //       });
    //     });
    //   });
    //   return mixArray;
    // },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .mt-input-number input[data-v-4fce8e3b] {
  width: 30px;
}
.rule-item {
  width: 100%;
  height: auto;
  padding: 16px;
  background: #f5f5f5;
  font-size: 14px;
  border-radius: 6px;
  margin-bottom: 20px;

  .title {
    height: 14px;
    font-size: 14px;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin-bottom: 16px;
  }

  .rule-line {
    margin-bottom: 16px;
    width: 100%;
    height: 40px;
    justify-content: space-evenly;

    .rule-input {
      margin-right: 16px;
      height: 40px;

      & > div {
        height: 40px;
      }
    }

    .clear-btn {
      padding-top: 10px;
    }

    .relative {
      position: relative;

      & > div {
        width: 100%;
        position: absolute;
        top: -9px;
      }
    }

    .rule-input:last-child {
      margin-right: 0;
    }

    .w60 {
      width: 9.4%;
    }
    .w80 {
      width: 12.5%;
    }
    .w52 {
      width: 10%;
    }
    .w296 {
      width: 46.25%;
    }
    .w108 {
      width: 22%;
    }

    .w108 {
      width: 12%;
    }
    .w48 {
      width: 12%;
    }
    .w12 {
      width: 12px;
      cursor: pointer;
      i {
        font-size: 12px;
        color: #9baac1;
      }
    }
    .w12:hover {
      i {
        font-size: 12px;
        color: red;
      }
    }
    .w272 {
      width: 42.5%;
    }
  }

  .add-rule {
    height: 18px;
    margin-bottom: 24px;

    .left-icon {
      cursor: pointer;
      width: 18px;
      height: 18px;
      font-size: 18px;
      margin-right: 10px;
      color: rgba(99, 134, 193, 1);
    }
    .right-txt {
      cursor: pointer;
      font-size: 14px;
      color: rgba(99, 134, 193, 1);
      line-height: 18px;
      user-select: none;
    }
  }

  .output {
    .output-title {
      height: 14px;
      font-size: 14px;
      color: rgba(41, 41, 41, 1);
    }
    .output-select {
      width: 100%;
      height: 40px;
      .w12 {
        width: 12px;
        cursor: pointer;
        i {
          font-size: 12px;
          color: #9baac1;
        }
      }
      .w12:hover {
        i {
          font-size: 12px;
          color: red;
        }
      }
    }
  }

  .remark-box {
    margin-top: 24px;
    font-size: 14px;
    color: rgba(41, 41, 41, 1);
    flex-wrap: wrap;
    transition: all 0.6s ease-in-out;

    .remark-title {
      font-size: 14px;
      width: 70px;
      margin-right: 10px;
    }
    .remark-input {
    }
    .txt-box {
      display: inline-flex;
      align-items: center;
      flex: 1;
    }
    .input-box {
      flex: 1;
    }
    .icon-edit {
      cursor: pointer;
      font-size: 14px;
      color: #9baac1;
      margin-left: 14px;
    }
  }
}
</style>
