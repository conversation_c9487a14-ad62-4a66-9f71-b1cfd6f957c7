import { taskTemplateStatusSetting, taskTemplateTypeSetting } from '@/utils/setting'
import { i18n } from '@/main.js'
export const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'stageCode',
    headerText: i18n.t('阶段编码'),
    width: '180',
    cellTools: ['edit', 'delete'],
    cssClass: ''
  },
  {
    field: 'stageName',
    width: '120',
    headerText: i18n.t('阶段名称')
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('阶段状态'),
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    }
  },
  {
    field: 'accessType',
    width: '120',
    headerText: i18n.t('阶段类型'),
    valueConverter: {
      type: 'map',
      map: taskTemplateTypeSetting
    }
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]
