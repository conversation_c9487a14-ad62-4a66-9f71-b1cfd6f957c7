<template>
  <mt-template-page
    ref="templateRef"
    :template-config="pageConfig"
    :hidden-tabs="true"
    :padding-top="true"
    @handleClickToolBar="handleClickToolBar"
    @handleClickCellTool="handleClickCellTool"
    @handleClickCellTitle="handleClickCellTitle"
  ></mt-template-page>
</template>

<script>
import { columns } from './config/columns'

export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: 'ee3736f0-0f79-4de5-9dc0-b259f5463021',
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                'Edit',
                { id: 'enable', title: this.$t('启用'), icon: 'icon_table_enable' },
                { id: 'disable', title: this.$t('停用'), icon: 'icon_table_disable' }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: [...columns],
            asyncConfig: {
              url: '/supplier/tenant/buyer/define/stage/query'
            }
          }
        }
      ],
      tabSltIndex: 0,
      isEdit: false,
      isBatchEdit: false
    }
  },
  mounted() {},
  methods: {
    setGridData(records) {
      this.$set(this.pageConfig[0].grid, 'dataSource', records)
    },

    handleClickToolBar(e) {
      // 表格顶部 toolbar
      const _this = this
      const { toolbar, gridRef } = e
      if (toolbar.id === 'Add') {
        this.$dialog({
          modal: () => import('./components/operatorDialog.vue'),
          data: {
            title: this.$t('新增阶段')
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
      if (toolbar.id == 'Delete') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id == 'Refresh') {
        _this.$refs.templateRef.refreshCurrentGridData()
      }
      if (toolbar.id === 'enable') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length) {
          const { id, status } = sltList[0]
          if (status === 1) {
            this.$toast({ content: this.$t('当前已处于启用状态'), type: 'warning' })
            return
          }
          let params = {
            status: 1,
            stageDefineId: id
          }
          this.updateStatus(params)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id === 'disable') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length) {
          const { id, status } = sltList[0]
          if (status === 2) {
            this.$toast({ content: this.$t('当前已处于停用状态'), type: 'warning' })
            return
          }
          let params = {
            status: 2,
            stageDefineId: id
          }
          this.updateStatus(params)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    // 更新调查表状态
    updateStatus(params) {
      this.$API.AccessStage.changeStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    handleClickCellTool(e) {
      // 单元格 this.$t("图标点击") tool
      const { tool, data } = e
      const { id } = tool || {}
      if (id === 'edit') {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        this.editStage(data)
      } else if (id === 'delete') {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可删除'),
            type: 'warning'
          })
          return
        }
        this.deleteRecord([data])
      }
    },

    handleClickCellTitle(e) {
      //单元格Title点击
      console.log(e)
      const { field, data } = e
      if (field === 'stageNo' && data && data.id) {
        this.$router.push({
          path: '/supplier/questionnaire-config-detail',
          query: {
            id: data.id
          }
        })
      }
    },

    // 单个删除和批量删除
    deleteRecord(data) {
      const _this = this
      if (!Array.isArray(data)) {
        console.log('批量删除传数组')
        return
      }
      let ids = data.map((item) => {
        return item.id
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选阶段？'),
          confirm: () => _this.$API.AccessStage['delStage'](ids)
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    editStage(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/operatorDialog.vue'),
        data: {
          title: this.$t('编辑阶段'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
