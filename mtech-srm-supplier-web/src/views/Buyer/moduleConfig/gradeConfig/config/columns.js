// import Vue from 'vue'
// import { taskStatusSetting } from '@/utils/setting'
import { i18n } from '@/main.js'
export const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'labelCode',
    headerText: i18n.t('级别编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'labelName',
    width: '120',
    headerText: i18n.t('级别名称')
  },
  {
    field: 'labelType',
    width: '120',
    headerText: i18n.t('级别标识')
  },
  {
    field: 'status',
    width: '80',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
    // template: () => {
    //   return {
    //     template: Vue.component("status", {
    //       template: `
    //         <div class="grade-config-box">
    //           <span :class="['template-status', data.status==1&&'status-disable']">{{status}}</span>
    //           <div class="btn-status" @click="changeStatus">
    //             <i class="mt-icons mt-icon-icon_list_disable"></i>
    //             <span>{{toStatus}}</span>
    //           </div>
    //         </div>`,
    //       data: function () {
    //         return { data: {} };
    //       },
    //       computed: {
    //         status() {
    //           return taskStatusSetting[this.data.status];
    //         },
    //         toStatus() {
    //           return taskStatusSetting[this.data.status == 1 ? 2 : 1];
    //         },
    //       },
    //       mounted() {

    //       },
    //       methods: {
    //         changeStatus() {
    //           this.$parent.$emit("handleClickCellTool", {
    //             tool: { id: this.data.status == 0 ? "disable" : "enable" },
    //             data: this.data,
    //           });
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]

export const columnb = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'labelCode',
    headerText: i18n.t('类别编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'labelName',
    width: '120',
    headerText: i18n.t('类别名称')
  },
  {
    field: 'labelType',
    width: '120',
    headerText: i18n.t('分类标识')
  },
  {
    field: 'status',
    width: '80',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
    // template: () => {
    //   return {
    //     template: Vue.component("status", {
    //       template: `
    //         <div class="grade-config-box">
    //           <span :class="['template-status', data.status==1&&'status-disable']">{{status}}</span>
    //           <div class="btn-status" @click="changeStatus">
    //             <i class="mt-icons mt-icon-icon_list_disable"></i>
    //             <span>{{toStatus}}</span>
    //           </div>
    //         </div>`,
    //       data: function () {
    //         return { data: {} };
    //       },
    //       computed: {
    //         status() {
    //           return taskStatusSetting[this.data.status];
    //         },
    //         toStatus() {
    //           return taskStatusSetting[this.data.status == 1 ? 2 : 1];
    //         },
    //       },
    //       mounted() {
    //         console.log(this.$parent, "this");
    //       },
    //       methods: {
    //         changeStatus() {
    //           this.$parent.$emit("handleClickCellTool", {
    //             tool: { id: this.data.status == 2 ? "disable" : "enable" },
    //             data: this.data,
    //           });
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]

export const columnc = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'labelCode',
    headerText: i18n.t('标签编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'labelName',
    width: '120',
    headerText: i18n.t('标签名称')
  },
  {
    field: 'labelType',
    width: '120',
    headerText: i18n.t('标签标识')
  },
  {
    field: 'status',
    width: '80',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
    // template: () => {
    //   return {
    //     template: Vue.component("status", {
    //       template: `
    //         <div class="grade-config-box">
    //           <span :class="['template-status', data.status==1&&'status-disable']">{{status}}</span>
    //           <div class="btn-status" @click="changeStatus">
    //             <i class="mt-icons mt-icon-icon_list_disable"></i>
    //             <span>{{toStatus}}</span>
    //           </div>
    //         </div>`,
    //       data: function () {
    //         return { data: {} };
    //       },
    //       computed: {
    //         status() {
    //           return taskStatusSetting[this.data.status];
    //         },
    //         toStatus() {
    //           return taskStatusSetting[this.data.status == 1 ? 2 : 1];
    //         },
    //       },
    //       mounted() {
    //         console.log(this.$parent, "this");
    //       },
    //       methods: {
    //         changeStatus() {
    //           this.$parent.$emit("handleClickCellTool", {
    //             tool: { id: this.data.status == 2 ? "disable" : "enable" },
    //             data: this.data,
    //           });
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]

export const columnd = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'gradeStrategyCode',
    headerText: i18n.t('分级策略编号'),
    width: '180',
    cellTools: [
      {
        id: 'Edita',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'Deletea',
        icon: 'icon_Delete',
        title: i18n.t('删除'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'gradeStrategyName',
    width: '120',
    headerText: i18n.t('分级策略名称')
  },
  {
    field: 'orgName',
    width: '120',
    headerText: i18n.t('适用组织')
  },
  {
    field: 'status',
    width: '80',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'enablea',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disablea',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
    // template: () => {
    //   return {
    //     template: Vue.component("status", {
    //       template: `
    //         <div class="grade-config-box">
    //           <span :class="['template-status', data.status==1&&'status-disable']">{{status}}</span>
    //           <div class="btn-status" @click="changeStatus">
    //             <i class="mt-icons mt-icon-icon_list_disable"></i>
    //             <span>{{toStatus}}</span>
    //           </div>
    //         </div>`,
    //       data: function () {
    //         return { data: {} };
    //       },
    //       computed: {
    //         status() {
    //           return taskStatusSetting[this.data.status];
    //         },
    //         toStatus() {
    //           return taskStatusSetting[this.data.status == 1 ? 2 : 1];
    //         },
    //       },
    //       mounted() {
    //         console.log(this.$parent, "this");
    //       },
    //       methods: {
    //         changeStatus() {
    //           this.$parent.$emit("handleClickCellTool", {
    //             tool: { id: this.data.status == 0 ? "disablea" : "enablea" },
    //             data: this.data,
    //           });
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]
