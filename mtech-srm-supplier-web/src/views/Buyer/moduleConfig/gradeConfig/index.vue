<template>
  <mt-template-page
    ref="templateRef"
    :template-config="pageConfig"
    :hidden-tabs="false"
    :padding-top="true"
    :use-tool-template="false"
    @handleClickToolBar="handleClickToolBar"
    @handleClickCellTool="handleClickCellTool"
    @handleClickCellTitle="handleClickCellTitle"
  ></mt-template-page>
</template>

<script>
import { columns, columnb, columnc, columnd } from './config/columns'
import utils from '../../../../utils/utils'

export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '76a56a3e-ffac-4ea0-9ae9-1fe46966d073',
          title: this.$t('级别定义'),
          permission: ['O_02_0027'],
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增'),
                  permission: ['O_02_0056']
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  permission: ['O_02_0057']
                },
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable',
                  permission: ['O_02_0058']
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable',
                  permission: ['O_02_0028']
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: [...columns],
            asyncConfig: {
              url: '/supplier/tenant/buyer/label/define/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: this.$t('状态'),
                  field: 'labelDefineType',
                  type: 'number',
                  operator: 'equal',
                  value: 1
                }
              ]
            }
          }
        },
        {
          gridId: '661ccce0-dbd3-4504-969b-d28233011fda',
          useToolTemplate: false,
          title: this.$t('自动分级'),
          permission: ['O_02_0029'],
          toolbar: {
            tools: [
              [
                {
                  id: 'Addautomatic',
                  title: this.$t('新增'),
                  icon: 'icon_solid_Createproject',
                  permission: ['O_02_0059']
                },
                {
                  id: 'Deletea',
                  title: this.$t('删除'),
                  icon: 'icon_solid_Delete1',
                  permission: ['O_02_0060']
                },
                {
                  id: 'enablea',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable',
                  permission: ['O_02_0061']
                },
                {
                  id: 'disablea',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable',
                  permission: ['O_02_0030']
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: [...columnd],
            asyncConfig: {
              url: '/supplier/tenant/buyer/automatic/grade/list'
            }
          }
        },
        {
          gridId: 'dfb1b4ca-f22d-44b4-94ca-c854a6b10eba',
          useToolTemplate: false,
          title: this.$t('关系类别定义'),
          permission: ['O_02_0031'],
          toolbar: {
            tools: [
              [
                {
                  id: 'Add2',
                  title: this.$t('新增'),
                  icon: 'icon_solid_Createproject',
                  permission: ['O_02_0031']
                },
                {
                  id: 'Delete',
                  title: this.$t('删除'),
                  icon: 'icon_solid_Delete1',
                  permission: ['O_02_0064']
                },
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable',
                  permission: ['O_02_0063']
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable',
                  permission: ['O_02_0065']
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: [...columnb],
            asyncConfig: {
              url: '/supplier/tenant/buyer/label/define/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: this.$t('状态'),
                  field: 'labelDefineType',
                  type: 'number',
                  operator: 'equal',
                  value: 2
                }
              ]
            }
          }
        },
        {
          gridId: '79744d65-5497-4f6c-9838-cac7f10bade3',
          useToolTemplate: false,
          title: this.$t('标签库管理'),
          permission: ['O_02_0033'],
          toolbar: {
            tools: [
              [
                {
                  id: 'Add3',
                  title: this.$t('新增'),
                  icon: 'icon_solid_Createproject',
                  permission: ['O_02_0066']
                },
                {
                  id: 'Delete',
                  title: this.$t('删除'),
                  icon: 'icon_solid_Delete1',
                  permission: ['O_02_0067']
                },
                {
                  id: 'enable',
                  title: this.$t('启用'),
                  icon: 'icon_table_enable',
                  permission: ['O_02_0068']
                },
                {
                  id: 'disable',
                  title: this.$t('停用'),
                  icon: 'icon_table_disable',
                  permission: ['O_02_0034']
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: [...columnc],
            dataSource: [{}],
            asyncConfig: {
              url: '/supplier/tenant/buyer/label/define/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: this.$t('状态'),
                  field: 'labelDefineType',
                  type: 'number',
                  operator: 'equal',
                  value: 3
                }
              ]
            }
          }
        }
      ],
      tabSltIndex: 0,
      isEdit: false,
      isBatchEdit: false
    }
  },
  mounted() {},
  methods: {
    setGridData(records) {
      this.$set(this.pageConfig[0].grid, 'dataSource', records)
    },

    handleClickToolBar(e) {
      // 表格顶部 toolbar
      const _this = this
      const { toolbar, gridRef, tabIndex } = e
      let currentViewData = []
      try {
        currentViewData =
          this.$refs.templateRef.getCurrentTabRef().grid.ej2Instances.currentViewData
      } catch (error) {
        console.log(error)
      }

      if (toolbar.id === 'Add') {
        this.$dialog({
          modal: () => import('./components/operatorDialog.vue'),
          data: {
            title: this.$t('新增级别定义'),
            labelDefineType: 1,
            currentViewData
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
      if (toolbar.id === 'Add2') {
        this.$dialog({
          modal: () => import('./components/operatorDialog.vue'),
          data: {
            title: this.$t('新增关系'),
            labelDefineType: 2,
            currentViewData
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
      if (toolbar.id === 'Add3') {
        this.$dialog({
          modal: () => import('./components/operatorDialog.vue'),
          data: {
            title: this.$t('新增标签'),
            labelDefineType: 3,
            currentViewData
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
      if (toolbar.id === 'Addautomatic') {
        this.$dialog({
          modal: () => import('./components/automaticGrading.vue'),
          data: {
            title: this.$t('新增自动分级')
            // lineInfo: grid
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
      if (toolbar.id == 'Delete') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length) {
          // S 战略不能删
          if (sltList.some((item) => item.status === 1)) {
            this.$toast({
              content: this.$t('当前存在已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          if (tabIndex === 0 && sltList.some((item) => item.labelType === 'S')) {
            this.$toast({
              content: this.$t('战略级别不能删除！'),
              type: 'warning'
            })
            return
          }

          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id == 'Deletea') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length) {
          if (sltList.some((item) => item.status === 1)) {
            this.$toast({
              content: this.$t('当前存在已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecorda(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id == 'Refresh') {
        _this.$refs.templateRef.refreshCurrentGridData()
      }
      if (toolbar.id === 'enable' || toolbar.id === 'disable') {
        let sltList = gridRef.getMtechGridRecords()
        const toStatus = toolbar.id === 'enable' ? 1 : 2
        if (sltList && sltList.length) {
          let len = sltList.length
          let idList = []
          for (let i = 0; i < len; i++) {
            const { id, status } = sltList[i]
            if (status === toStatus) {
              this.$toast({
                content: `${this.$t('当前已有处于')}${
                  toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')
                }${this.$t('状态')}`,
                type: 'warning'
              })
              return
            }
            idList.push(id)
          }

          let params = {
            status: toStatus,
            idList: idList
          }
          this.updateStatus(params)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id === 'enablea' || toolbar.id === 'disablea') {
        let sltList = gridRef.getMtechGridRecords()
        const toStatus = toolbar.id === 'enablea' ? 1 : 2
        if (sltList && sltList.length) {
          let len = sltList.length
          let idList = []
          for (let i = 0; i < len; i++) {
            const { id, status } = sltList[i]
            if (status === toStatus) {
              this.$toast({
                content: `${this.$t('当前已有处于')}${
                  toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')
                }${this.$t('状态')}`,
                type: 'warning'
              })
              return
            }
            idList.push(id)
          }

          let params = {
            status: toStatus,
            idList: idList
          }
          if (e.tabIndex == 1) {
            this.updateStatusa(params)
          } else {
            this.updateStatus(params)
          }
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    // 更新调查表状态
    updateStatus(params) {
      this.$API.GradeConfig.changeStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 更新自动分级
    updateStatusa(params) {
      this.$API.GradeConfig.changeStatusa(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      console.log(e)
      const { tool, data, tabIndex } = e
      if (['edit', 'delete'].includes(tool.id)) {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        // 战略级别不能删除！
        if (tool.id === 'delete' && tabIndex === 0 && data.labelType === 'S') {
          this.$toast({
            content: this.$t('战略级别不能删除！'),
            type: 'warning'
          })
          return
        }
        tool.id === 'edit' && this.editStage(data)
        tool.id === 'delete' && this.deleteRecord([data])
      } else if (['Edita', 'Deletea'].includes(tool.id)) {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        tool.id === 'Edita' && this.editStagea(data)
        tool.id === 'Deletea' && this.deleteRecorda([data])
      } else if (['enable', 'disable'].includes(tool.id)) {
        const toStatus = data.status == 1 ? 2 : 1
        let params = {
          status: toStatus,
          idList: [data.id]
        }
        this.updateStatus(params)
      } else if (['enablea', 'disablea'].includes(tool.id)) {
        const toStatus = data.status == 1 ? 2 : 1
        let params = {
          status: toStatus,
          idList: [data.id]
        }
        this.updateStatusa(params)
      }
    },

    handleClickCellTitle(e) {
      //单元格Title点击
      const { field, data } = e
      if (field === 'stageNo' && data && data.id) {
        this.$router.push({
          path: '/supplier/questionnaire-config-detail',
          query: {
            id: data.id
          }
        })
      }
    },

    // 单个删除和批量删除
    deleteRecord(data) {
      const _this = this
      if (utils.isEmpty(data) || data.length === 0) {
        _this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      let idList = data.map((item) => {
        return item.id
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选阶段？'),
          confirm: () => _this.$API.GradeConfig['deleteGrade']({ idList })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },

    deleteRecorda(data) {
      const _this = this
      if (utils.isEmpty(data) || data.length === 0) {
        _this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      let idList = data.map((item) => {
        return item.id
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选阶段？'),
          confirm: () => _this.$API.GradeConfig['deleteGradea']({ idList })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    editStage(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/operatorDialog.vue'),
        data: {
          title: this.$t('编辑关系'),
          isEdit: true,
          labelDefineType: data.labelDefineType,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editStagea(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/automaticGrading.vue'),
        data: {
          title: this.$t('编辑分级'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.mt-data-grid {
  .grid-edit-column {
    justify-content: center;
  }
  .status-label {
    font-size: 12px;
    padding: 2px;
    border-radius: 2px;
    &.status-disable {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-enable {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
.grade-config-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #6386c1;
  .template-status {
    display: inline-block;
    padding: 4px;
    background: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
    line-height: 12px;
    margin-bottom: 4px;
  }
  .status-disable {
    color: #9a9a9a;
    background: rgba(154, 154, 154, 0.1);
  }
  .btn-status {
    cursor: pointer;
  }
}
</style>
