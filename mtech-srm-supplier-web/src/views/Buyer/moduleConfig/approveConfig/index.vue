<template>
  <mt-template-page
    ref="templateRef"
    :template-config="pageConfig"
    :hidden-tabs="true"
    :padding-top="true"
    @handleClickToolBar="handleClickToolBar"
    @handleClickCellTool="handleClickCellTool"
    @handleClickCellTitle="handleClickCellTitle"
  ></mt-template-page>
</template>

<script>
import { columns } from './config/columns'

export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '142e7fb1-6a02-4c7f-99f3-3423a4d9dac6',
          title: this.$t('审批流配置'),
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                { id: 'enable', title: this.$t('启用'), icon: 'icon_table_enable' },
                { id: 'disable', title: this.$t('停用'), icon: 'icon_table_disable' }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: [...columns],
            dataSource: [{}],
            asyncConfig: {
              url: '/supplier/tenant/buyer/approve/define/query'
            }
          }
        }
      ],
      tabSltIndex: 0,
      isEdit: false,
      isBatchEdit: false
    }
  },
  mounted() {},
  methods: {
    setGridData(records) {
      this.$set(this.pageConfig[0].grid, 'dataSource', records)
    },

    handleClickToolBar(e) {
      // 表格顶部 toolbar
      const _this = this
      const { toolbar, gridRef } = e
      if (toolbar.id === 'Add') {
        this.$dialog({
          modal: () => import('./components/operatorDialog.vue'),
          data: {
            title: this.$t('新增审批流配置')
          },
          success: () => {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
      if (toolbar.id == 'Delete') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id == 'Refresh') {
        _this.$refs.templateRef.refreshCurrentGridData()
      }
      if (toolbar.id === 'enable' || toolbar.id === 'disable') {
        let sltList = gridRef.getMtechGridRecords()
        const toStatus = toolbar.id === 'enable' ? 2 : 1
        if (sltList && sltList.length) {
          let len = sltList.length
          let ids = []
          for (let i = 0; i < len; i++) {
            const { id, status } = sltList[i]
            if (status === toStatus) {
              this.$toast({
                content: `${this.$t('当前已有处于')}${
                  toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')
                }${this.$t('状态')}`,
                type: 'warning'
              })
              return
            }
            ids.push(id)
          }

          let params = {
            status: toStatus,
            ids: ids
          }
          this.updateStatus(params)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    // 更新调查表状态
    updateStatus(params) {
      this.$API.GradeConfige.changeStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (['edit', 'delete'].includes(tool.id)) {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        tool.id === 'edit' && this.editStage(data)
        tool.id === 'delete' && this.deleteRecord([data])
      }

      if (['enable', 'disable'].includes(tool.id)) {
        const toStatus = data.status == 1 ? 2 : 1
        let params = {
          status: toStatus,
          ids: [data.id]
        }
        this.updateStatus(params)
      }
    },

    handleClickCellTitle(e) {
      //单元格Title点击
      console.log(e)
      const { field, data } = e
      if (field === 'stageNo' && data && data.id) {
        this.$router.push({
          path: '/supplier/questionnaire-config-detail',
          query: {
            id: data.id
          }
        })
      }
    },

    // 单个删除和批量删除
    deleteRecord(data) {
      const _this = this
      if (!Array.isArray(data)) {
        console.log('批量删除传数组')
        return
      }
      let ids = data.map((item) => {
        return item.id
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选阶段？'),
          confirm: () => _this.$API.GradeConfige['deleteGrade']({ ids: ids })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    editStage(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/operatorDialog.vue'),
        data: {
          title: this.$t('编辑审批流配置'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.approve-config-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #6386c1;
  .template-status {
    display: inline-block;
    padding: 4px;
    background: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
    line-height: 12px;
    margin-bottom: 4px;
  }
  .status-disable {
    color: #9a9a9a;
    background: rgba(154, 154, 154, 0.1);
  }
  .btn-status {
    cursor: pointer;
  }
}
</style>
