import Vue from 'vue'
import { i18n } from '@/main.js'
import { taskStatusSetting, taskTemplateaccessStage } from '@/utils/setting'

export const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'approveDefineCode',
    headerText: i18n.t('审批流配置编码'),
    width: '180',
    cellTools: ['edit', 'delete'],
    cssClass: ''
  },
  {
    field: 'approveDefineType',
    width: '120',
    headerText: i18n.t('审批节点名称'),
    valueConverter: {
      type: 'map',
      map: taskTemplateaccessStage
    }
  },
  {
    field: 'workflowKey',
    width: '120',
    headerText: i18n.t('关联审批流编码')
  },
  {
    field: 'workflowName',
    width: '120',
    headerText: i18n.t('关联审批流名称')
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('状态'),
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              <span :class="['template-status', data.status==1&&'status-disable']">{{status}}</span>
              <div class="btn-status" @click="changeStatus">
                <i class="mt-icons mt-icon-icon_list_disable"></i>
                <span>{{toStatus}}</span>
              </div>
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            status() {
              return taskStatusSetting[this.data.status]
            },
            toStatus() {
              return taskStatusSetting[this.data.status == 1 ? 2 : 1]
            }
          },
          mounted() {
            console.log(this.$parent, 'this')
          },
          methods: {
            changeStatus() {
              this.$parent.$emit('handleClickCellTool', {
                tool: { id: this.data.status == 1 ? 'disable' : 'enable' },
                data: this.data
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]
