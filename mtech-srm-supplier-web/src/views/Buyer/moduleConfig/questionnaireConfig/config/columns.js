import { taskTemplateStatusSetting, taskTemplateTypeSetting } from '@/utils/setting'
import { i18n } from '@/main.js'
export const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'surveyTemplateName',
    headerText: i18n.t('调查表编码'),
    width: '180',
    cellTools: ['edit', 'delete']
  },
  {
    field: 'taskTemplateName',
    width: '120',
    headerText: i18n.t('调查表名称')
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('调查表状态'),
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    }
  },
  {
    field: 'taskTemplateType',
    width: '120',
    headerText: i18n.t('调查表类型'),
    valueConverter: {
      type: 'map',
      map: taskTemplateTypeSetting
    }
  },
  {
    field: 'taskTemplateClassify',
    width: '120',
    headerText: i18n.t('调查表分类')
    // valueConverter: {
    //   type: "map",
    //   map: {
    //     ad1: "xxxxx",
    //   },
    // },
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'date'
    }
  },

  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  },
  {
    field: 'version',
    width: '80',
    headerText: i18n.t('版本')
  }
]
