<template>
  <mt-template-page
    ref="templateRef"
    :template-config="pageConfig"
    :hidden-tabs="true"
    :padding-top="true"
    @handleClickToolBar="handleClickToolBar"
    @handleClickCellTool="handleClickCellTool"
    @handleClickCellTitle="handleClickCellTitle"
  ></mt-template-page>
</template>

<script>
import { columns } from './config/columns'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '26f1faeb-2262-4b70-b87d-5d57680eb9c2',
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                'Edit',
                { id: 'enable', title: this.$t('启用'), icon: 'icon_table_enable' },
                { id: 'disable', title: this.$t('停用'), icon: 'icon_table_disable' }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columns,
            asyncConfig: {
              url: '/supplier/tenant/buyer/template/task/query'
            }
          }
        }
      ],
      columns,
      tabSltIndex: 0,
      isEdit: false,
      isBatchEdit: false,
      taskClassifyList: [], // 分类列表
      taskClassifyListMap: {}
    }
  },
  mounted() {
    this.queryTaskClassify()
  },
  methods: {
    // 调查表类型
    async queryTaskClassify() {
      const { code, data } = await this.$API.AccessProcess['queryDict']({
        dictCode: 'adTaskClassify'
      })
      if (code == 200 && data) {
        this.taskClassifyList = data
        data.forEach((classify) => {
          const { itemCode, itemName } = classify
          this.taskClassifyListMap[itemCode] = itemName
          let index = this.columns.findIndex((item) => item.field === 'taskTemplateClassify')
          index > -1 &&
            this.$set(this.columns[index], 'valueConverter', {
              type: 'map',
              map: this.taskClassifyListMap
            })
          this.$set(this.pageConfig[0].grid, 'columnData', this.columns)
        })
      }
    },

    // 更新调查表状态
    updateStatusApi(params) {
      this.$API.QuestionnaireConfig.changeStatus(params)
        .then((res) => {
          const { code } = res
          if (code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
        })
    },

    handleClickToolBar(e) {
      // 表格顶部 toolbar
      const _this = this
      const { toolbar, gridRef } = e

      if (toolbar.id === 'Add') {
        const { taskClassifyList } = this
        this.$dialog({
          modal: () => import('./components/operatorDialog.vue'),
          data: {
            title: this.$t('新增调查表'),
            taskClassifyList
          },
          success: (id) => {
            if (id) {
              this.$router.push({
                path: '/supplier/questionnaire-config-detail',
                query: {
                  id
                }
              })
            } else {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          }
        })
      }
      if (toolbar.id === 'Delete') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id === 'Refresh') {
        this.$refs.templateRef.refreshCurrentGridData()
      }
      if (toolbar.id === 'enable' || toolbar.id === 'disable') {
        let sltList = gridRef.getMtechGridRecords()
        let ids = []
        let newStatus = toolbar.id === 'enable' ? 1 : 2
        let statusName = toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')
        if (sltList && sltList.length) {
          for (let i = 0; i < sltList.length; i++) {
            const { id, status } = sltList[i]
            if (status === newStatus) {
              this.$toast({
                content: `${this.$t('当前已处于')}${statusName}${this.$t('状态')}`,
                type: 'warning'
              })
              return
            }
            ids.push(id)
          }
          let params = {
            status: newStatus,
            taskTemplateId: ids
          }
          this.updateStatusApi(params)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      const { id } = tool || {}
      if (id === 'edit') {
        this.editTask(data)
      } else if (id === 'delete') {
        const { status } = data
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可删除'),
            type: 'warning'
          })
          return
        }
        this.deleteRecord([data])
      }
    },
    handleChangeCellCheckBox(a) {
      // 单元格,Checkbox change事件
      console.log('use-handleChangeCellCheckBox', a)
    },
    handleClickCellTitle(e) {
      //单元格Title点击
      console.log(e)
      const { field, data } = e
      if (field === 'surveyTemplateName' && data && data.id) {
        this.$router.push({
          path: '/supplier/questionnaire-config-detail',
          query: {
            id: data.id
          }
        })
      }
    },
    // 单个删除和批量删除
    deleteRecord(data) {
      const _this = this
      if (!Array.isArray(data)) {
        console.log('批量删除传数组')
        return
      }
      let ids = data.map((item) => {
        return item.id
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表？'),
          confirm: () => this.$API.QuestionnaireConfig.delFormTemplate(ids)
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editTask(data) {
      const _this = this
      const { taskClassifyList } = this
      this.$dialog({
        modal: () => import('./components/operatorDialog.vue'),
        data: {
          title: this.$t('编辑阶段'),
          isEdit: true,
          info: data,
          taskClassifyList
        },
        success: (id) => {
          if (id) {
            this.$router.push({
              path: '/supplier/questionnaire-config-detail',
              query: {
                id
              }
            })
          } else {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    }
  }
}
</script>
