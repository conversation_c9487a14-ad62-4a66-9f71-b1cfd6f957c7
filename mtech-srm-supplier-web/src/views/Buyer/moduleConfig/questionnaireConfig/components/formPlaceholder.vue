<template>
  <div class="placeholder-box">
    <div>
      <div class="common"></div>
      <div class="common"></div>
    </div>
    <div class="common"></div>
    <div class="common"></div>
    <div>
      <div class="common"></div>
      <div class="common"></div>
      <div class="common"></div>
    </div>
  </div>
</template>

<script>
export default {}
</script>

<style lang="scss" scoped>
.placeholder-box {
  width: 100%;
  max-width: 880px;
  .common {
    height: 60px;
    background: #fafafa;
    border-radius: 10px;
    margin-bottom: 90px;
  }

  > div:first-child {
    display: flex;
    justify-content: space-between;
    > div {
      width: 48%;
    }
  }
  > div:last-child {
    display: flex;
    justify-content: space-between;
    > div {
      width: 30%;
      margin: 0;
    }
  }
}
</style>
