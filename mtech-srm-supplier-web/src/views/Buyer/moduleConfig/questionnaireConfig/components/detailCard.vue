<template>
  <div class="detail-card">
    <div class="logo">{{ logo }}</div>
    <div class="desc">
      <div class="desc-title-box">
        <span class="title">{{ title }}</span>
        <span v-if="status" class="tag status">{{ status | getStatus }}</span>
        <span v-if="classify" class="tag group">{{ getTaskTemplateClassify(classify) }}</span>
      </div>
      <div class="desc-detail-box">
        <div v-for="(item, index) in detail" :key="index" class="detail-item">
          <span>{{ item.text }}：</span>
          <span>{{ item.value || '--' }}</span>
        </div>
      </div>
    </div>
    <div class="buttons-box">
      <span
        class="btn"
        :class="{ 'is-disabled': item.disabled }"
        v-for="item in btns"
        :key="item.id"
        @click="btnClick(item)"
        >{{ item.text }}</span
      >
    </div>
  </div>
</template>

<script>
import { taskTemplateStatusSetting } from '@/utils/setting'

export default {
  filters: {
    getStatus(val) {
      return taskTemplateStatusSetting[val]
    }
  },
  props: {
    logo: {
      type: String,
      default: 'D'
    },
    title: {
      type: String,
      default: ''
    },
    status: {
      type: [String, Number],
      default: ''
    },
    classify: {
      type: [String, Number],
      default: ''
    },
    detail: {
      type: Array,
      default: () => []
    },
    buttons: {
      type: Array,
      default: () => [
        {
          id: 'back',
          text: this.$t('返回')
        },
        {
          id: 'save',
          text: this.$t('保存')
        },
        {
          id: 'saveAndEnable',
          text: this.$t('保存并启用')
        }
      ]
    }
  },
  computed: {
    btns() {
      return this.status === 1
        ? this.buttons.map((item) => {
            let disabled = item.id === 'back' ? false : true
            return {
              ...item,
              disabled
            }
          })
        : this.buttons.map((item) => {
            return {
              ...item,
              disabled: false
            }
          })
    }
  },
  data() {
    return {
      taskClassifyList: []
    }
  },
  created() {
    this.queryTaskClassify()
  },
  methods: {
    btnClick(item) {
      if (item.disabled) return
      this.$emit('btn-click', item)
    },

    // 调查表分类
    queryTaskClassify() {
      this.$API.AccessProcess['queryDict']({
        dictCode: 'adTaskClassify'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          data.forEach((item) => {
            const { itemCode, itemName } = item
            this.taskClassifyList.push({
              itemCode,
              itemName
            })
          })
        }
      })
    },

    getTaskTemplateClassify(classify) {
      let index =
        this.taskClassifyList &&
        this.taskClassifyList.findIndex((item) => item.itemCode === classify)
      return index > -1 ? this.taskClassifyList[index].itemName : '--'
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-card {
  width: 100%;
  height: 100px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 70px 20px 20px;
  display: flex;
  .logo {
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    background: #00469c;
    font-size: 40px;
    font-weight: bold;
    color: #fff;
    border-radius: 50%;
    margin-right: 22px;
  }
  .desc {
    flex: 1;
    .desc-title-box {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .title {
        font-size: 20px;
        font-weight: bold;
        color: #292929;
      }
      .tag {
        font-size: 12px;
        display: inline-block;
        padding: 4px;
        border-radius: 2px;
      }
      .status {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
        margin: 0 10px;
      }
      .group {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
    .desc-detail-box {
      display: flex;
      .detail-item {
        font-size: 12px;
        color: #9a9a9a;
        margin-right: 20px;
      }
    }
  }
  .buttons-box {
    display: flex;
    align-items: center;
    .btn {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      color: #00469c;
      margin-left: 50px;
      cursor: pointer;
    }
    .is-disabled {
      color: #ccc;
    }
  }
}
</style>
