<template>
  <div class="policy-setting">
    <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig">
      <!-- 评审策略 -->
      <review-homepage slot="slot-0" index="0"></review-homepage>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  // 评审策略
  name: 'ReviewHomepageSetting',
  components: {
    //评审策略
    reviewHomepage: () => import('./components/reviewHomepage/reviewHomepage')
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: 'ea7a086d-a386-4f2e-b2a5-8adfd9adba01',
          title: this.$t('评审策略')
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-setting {
  height: 100%;
}
</style>
