<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>

      <div class="slider-content">
        <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="strategyName" class="form-item positive" :label="$t('策略名称')">
                <mt-input
                  :max-length="50"
                  :disabled="editorIdstatus"
                  v-model="formInfo.strategyName"
                  :placeholder="$t('请输入策略名称')"
                  float-label-type="Never"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="buyerOrgId" :label="$t('公司')">
                <RemoteAutocomplete
                  :params="{
                    organizationLevelCodes: ['ORG02', 'ORG01'],
                    orgType: 'ORG001PRO',
                    includeItself: true
                  }"
                  v-model="formInfo.buyerOrgCode"
                  :fields="{ text: 'orgName', value: 'orgCode' }"
                  url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                  @change="selectCompany"
                  :title-switch="false"
                  :disabled="editorIdstatus"
                  :width="414"
                  :placeholder="$t('请选择')"
                  select-type="administrativeCompany"
                ></RemoteAutocomplete>
              </mt-form-item>
            </mt-col>
          </mt-row>

          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="categoryArr" class="form-item positive" :label="$t('品类')">
                <mt-DropDownTree
                  :fields="categoryListArrList"
                  v-model="formInfo.categoryArr"
                  :allow-multi-selection="true"
                  :auto-check="true"
                  :show-check-box="true"
                  id="checkboxTreeSelect"
                  :disabled="editorIdstatus"
                  :placeholder="$t('请选择')"
                  @input="selectCategoryas"
                  :key="categoryListArrList.key"
                ></mt-DropDownTree>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row>
            <mt-col :span="24">
              <rules
                ref="rules"
                v-for="(item, index) of strategyRules"
                :rule-index="index + 1"
                :key="item.id"
                :item="item"
                :disabled="editorIdstatus"
                @clearGroup="clearGroup"
              ></rules>
              <div
                class="add-rule-group"
                v-if="!editorIdstatus"
                @click="addRuleGroup"
                :disabled="editorIdstatus"
              >
                {{ $t('添加规则') }}
              </div>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>

      <div class="slider-footer mt-flex">
        <span @click="confirm" v-if="isnone">{{ $t('确定') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils.js'
import rules from './rules.vue'
import { filterValue } from '../config/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  data() {
    return {
      // orgIdArr: [], //组织绑定
      isnone: true,

      editorIdstatus: false, //全局禁用 查看专用
      isShow: false,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.submitForm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      // 品类选择数组 适用品类集合
      categoryList: [
        {
          categoryCode: '', // 品类编码
          categoryId: '', // 	品类id
          categoryName: '' // 品类名称
        }
      ],
      // 表单参数
      formInfo: {
        strategyName: '', //策略名称

        // orgIdArr: [], //组织绑定
        buyerOrgCode: '', // 	（组织机构）编码
        buyerOrgId: '', // 	（组织机构）id
        buyerOrgName: '', // （组织机构）名称
        ruleOutput: '', // 输出
        triggerType: '', // 触发方式
        validityTime: '', // 有效期

        buyerEnterpriseId: '', // 公司id
        buyerEnterpriseCode: '', // 公司code
        buyerEnterpriseName: '', // 	公司名称

        categoryList: [],
        strategyRules: [],

        categoryArr: []
      },

      // 表单验证
      rules: {
        categoryArr: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        strategyName: [
          {
            required: true,
            message: this.$t('请输入策略名称'),
            trigger: 'blur'
          }
        ],
        buyerOrgId: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ]
      },
      // 规则集合
      strategyRules: [
        {
          name: '', //	策略规则名称
          remark: '', //规则描述
          id: (Math.random() * 100000).toFixed(0),
          strategyRuleInputs: [
            {
              id: (Math.random() * 100000).toFixed(0),
              conditionObject: '1' //规则类型（1，最近；2，连续；）
            }
          ],
          //输出结果 逗号分割，最多2个输出
          outputTypeList: [
            {
              outputType: ''
            }
          ]
        }
      ],
      // 规则组数量 需要添加一个不限 还未定对应id值
      categoryListArrList: {
        dataSource: [], //品类树下拉数组
        value: 'categoryCode',
        text: 'categoryName',
        child: 'childrens',
        key: 1
      },
      selectCategoryList: [], // 分类数组

      loadCompanyData: false // :loadData会监听去控制每次置为true就会重新发请求下拉组件数据
    }
  },
  props: {
    // 父组件传值 数据集合
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    rules,
    RemoteAutocomplete
  },

  computed: {
    // 父组件传值 数据集合初始化
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    viewlock() {
      return this.modalData.viewlock
    },
    policyData() {
      return this.modalData.policyData
    }
  },
  async created() {
    // 编辑
    if (this.modalData.isEdit) {
      let emitData = this.modalData.policyData //行内传值数据
      this.strategies(emitData.id)
    } else if (this.modalData.viewlock == 1) {
      let emitData = this.modalData.policyData //行内传值数据
      this.strategies(emitData.id)
      this.editorIdstatus = true
      this.isnone = false
    } else {
      // this.TreeByAccount(); // 初始化获取公司列表
      // :loadData会监听去控制每次置为true就会重新发请求下拉组件数据
      this.loadCompanyData = true
      // 加延迟将:loadData置为false，否则组件无法watch监听变化
      setTimeout(() => {
        this.loadCompanyData = false
      }, 500)
      this.addByPlanuery() // 获取计划品类列表
    }
  },
  methods: {
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },

    // 选择公司
    selectCompany(e) {
      let itemData = e.itemData
      console.log(itemData)
      this.formInfo.buyerOrgCode = itemData.orgCode
      this.formInfo.buyerOrgId = itemData.id
      this.formInfo.buyerOrgName = itemData.orgName
    },
    // 初始化获取公司列表
    // TreeByAccount() {
    //   this.$API.reviewPlan["findSpecif"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((res) => {
    //     this.orgIdArr = res.data;
    //   });
    // },
    // 获取策略详情列表
    strategies(val) {
      this.$API.reviewPlan['detailriveAdd']({
        id: val // 传值id
      }).then((res) => {
        this.formInfo = res.data
        this.formInfo.categoryArr = this.formInfo.categoryList.map((item) => {
          return item.categoryCode
        })
        this.addByPlanuery() //获取品类接口
        // this.TreeByAccount(); //获取公司接口
        // :loadData会监听去控制每次置为true就会重新发请求下拉组件数据
        this.loadCompanyData = true
        // 加延迟将:loadData置为false，否则组件无法watch监听变化
        setTimeout(() => {
          this.loadCompanyData = false
        }, 500)
        this.strategyRules = this.formInfo.strategyRules
      })
    },

    // 获取品类列表
    addByPlanuery() {
      this.$API.reviewPlan['productecif']({
        planId: this.formInfo.planId
      }).then((result) => {
        this.$hloading()
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          this.$set(this.categoryListArrList, 'dataSource', [...result.data])
          this.$set(this.categoryListArrList, 'key', this.randomString())
        }
      })
    },

    // 品类递归接口
    getCategory(data, arr, categoryList) {
      data.forEach((item) => {
        if (arr.indexOf(item.categoryCode) != -1) {
          categoryList.push({
            categoryCode: item.categoryCode,
            categoryId: item.id,
            categoryName: item.categoryName
          })
        }
      })
    },
    // 选择品类
    selectCategoryas(e) {
      this.formInfo.categoryList = []
      this.getCategory(this.categoryListArrList.dataSource, e, this.formInfo.categoryList)
      this.$nextTick(() => {
        this.$refs.formInfo.validateField('categoryArr')
      })
    },

    // 添加规则
    addPolicy() {
      this.$refs.formInfo.validate((val) => {
        if (val) {
          // let bol = false;
          // this.strategyRules.forEach((item) => {
          //   item.outputTypeList.forEach((e, i) => {
          //     if (e.outputType === "") {
          //       bol = true;
          //     }
          //   });
          // });
          // if (bol) {
          //   this.$toast({ content: this.$t("请选择输出值"), type: "warning" });
          //   return;
          // }

          let query = {
            buyerOrgCode: this.formInfo.buyerOrgCode,
            buyerOrgId: this.formInfo.buyerOrgId,
            buyerOrgName: this.formInfo.buyerOrgName,
            categoryList: this.formInfo.categoryList,
            categoryType: 1,
            priority: 0,
            remark: '',
            strategyName: this.formInfo.strategyName,
            strategyRules: []
          }
          console.log(this.strategyRules)
          this.strategyRules.forEach((item, index) => {
            let obj = {
              name: this.$t('规则') + filterValue(index + 1),
              outputResult: '',
              remark: item.remark,
              strategyRuleInputs: []
            }
            item.strategyRuleInputs.forEach((e, i) => {
              let newData = {}
              if (i == 0) {
                newData.conditionType = '0'
              } else {
                newData.conditionType = e.conditionType ? e.conditionType : '0'
                if (e.conditionType != '1' && e.conditionType != '2') {
                  // bol = true
                }
              }
              if (e.conditionObject == '1') {
                newData.conditionObject = e.conditionObject
                newData.conditionTarget = e.conditionTarget
                newData.conditionCycleValue = e.conditionCycleValue
                newData.conditionCycleType = e.conditionCycleType
                newData.conditionAccessType = e.conditionAccessType
                newData.conditionSymbol = e.conditionSymbol
                newData.conditionAttribute = e.conditionAttribute
              }
              if (e.conditionObject == '2') {
                newData.conditionObject = e.conditionObject
                newData.conditionTarget = e.conditionTarget
                newData.conditionCycleType = e.conditionCycleType
                newData.conditionAttribute = e.conditionAttribute
                newData.conditionAccessType = e.conditionAccessType
                newData.conditionSymbol = e.conditionSymbol
              }
              obj.strategyRuleInputs.push(newData)
            })
            query.strategyRules.push(obj)
          })

          this.$loading()
          // 编辑接口
          console.log(this.modalData.isEdit)
          if (this.modalData.isEdit) {
            query.id = this.formInfo.id
            this.$API.reviewPlan.updateriveAdd(query).then(() => {
              this.$hloading()
              this.cancel()
              this.$emit('confirm-function')
            })
          } else {
            // 提交接口
            this.$API.reviewPlan.strategyriveAdd(query).then(() => {
              this.$hloading()
              this.cancel()
              this.$emit('confirm-function')
            })
          }
        }
      })
    },

    // 添加规则组
    addRuleGroup() {
      this.strategyRules.push({
        id: (Math.random() * 100000).toFixed(0),
        strategyRuleInputs: [
          {
            id: (Math.random() * 100000).toFixed(0),
            conditionObject: '1'
          }
        ],
        outputTypeList: [
          {
            outputType: ''
          }
        ],
        remark: ''
      })
    },
    // 删除规则组
    clearGroup(ruleIndex) {
      let index = Number(ruleIndex) - 1
      this.strategyRules.splice(index, 1)
    },

    beforeClose() {
      this.$emit('cancel-function')
    },
    cancel() {
      this.$emit('cancel-function')
      this.isnone = true
    },
    submitForm() {},
    // 确认提交
    confirm() {
      if (this.modalData.viewlock != 1) {
        this.addPolicy()
      } else {
        this.cancel()
      }
    }
  }
}
</script>

<style lang="scss">
.mt-form-item {
  margin-bottom: 20px;
}
.mt-form-item-topLabel {
  .label {
    color: #292929;
    font-weight: normal;
  }
}
</style>

<style lang="scss" scoped>
.slider-panel-container {
  .slider-modal {
    width: 860px !important;
    .slider-header {
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }

    .add-rule-group {
      margin-top: 30px;
      height: 14px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      width: 100%;
      text-align: center;
      cursor: pointer;
    }

    .slider-footer {
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
</style>
