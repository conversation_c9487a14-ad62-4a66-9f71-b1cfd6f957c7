//策略设置 Tab 表头
import { i18n } from '@/main.js'
const toolbar = [
  {
    id: 'addNew',
    icon: 'icon_solid_Newinvitation',
    title: i18n.t('新增')
  },
  {
    id: 'editPolicy',
    icon: 'icon_Editor',
    title: i18n.t('编辑')
  },
  {
    id: 'delete',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  },
  {
    id: 'Enable',
    icon: 'icon_table_enable',
    title: i18n.t('启用')
  },
  {
    id: 'disable',
    icon: 'icon_table_disable',
    title: i18n.t('停用')
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'strategyCode',
    headerText: i18n.t('编码'),
    cellTools: [
      { id: 'edit', icon: 'icon_Editor', title: i18n.t('编辑'), permission: ['O_02_0041'] },
      { id: 'delete', icon: 'icon_Delete', title: i18n.t('删除'), permission: ['O_02_0040'] }
    ]
  },
  {
    field: 'strategyName',
    headerText: i18n.t('策略名称')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('适用品类')
  },
  {
    field: 'buyerEnterpriseName',
    headerText: i18n.t('公司')
  },

  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: 80,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('已创建'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: {
        text: 'label',
        value: 'status'
      }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Start',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'Stop',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'remark',
    headerText: i18n.t('规则描述')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    queryType: 'date',
    width: 80,
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  }
]

export const pageConfig = (url) => [
  {
    gridId: 'e3364452-f76a-423d-ad2d-ab3f350a7a80',
    toolbar,

    grid: {
      columnData,
      allowTextWrap: true,
      asyncConfig: {
        url
      }
    }
  }
]

export const conditionType = [
  {
    type: '1',
    name: i18n.t('并且')
  },
  {
    type: '2',
    name: i18n.t('或者')
  }
]

export const conditionObject = [
  {
    type: '1',
    name: i18n.t('最近')
  },
  {
    type: '2',
    name: i18n.t('本次')
  }
]

export const conditionCycleType = [
  {
    type: '1',
    name: i18n.t('月')
  },
  {
    type: '2',
    name: i18n.t('季度')
  },
  {
    type: '3',
    name: i18n.t('年')
  }
]

export const conditionAttribute = [
  {
    type: '1',
    name: i18n.t('品类')
  },
  {
    type: '2',
    name: i18n.t('供应商')
  }
]

export const conditionAccessType = [
  {
    type: '1',
    name: i18n.t('关键程度')
  },
  {
    type: '2',
    name: i18n.t('被公司评审次数')
  }
]

export const conditionSymbol = [
  {
    type: '0',
    name: '='
  },
  {
    type: '1',
    name: '≥'
  },
  {
    type: '2',
    name: '>'
  },
  {
    type: '3',
    name: '<'
  },
  {
    type: '4',
    name: '≤'
  },
  {
    type: '5',
    name: i18n.t('空')
  },
  {
    type: '6',
    name: i18n.t('非空')
  },
  {
    type: '7',
    name: i18n.t('包含')
  },
  {
    type: '8',
    name: i18n.t('不包含')
  }
]

export const equalType = [
  {
    type: '0',
    name: i18n.t('手动')
  },
  {
    type: '1',
    name: i18n.t('自动')
  }
]

export const mapNumber = {
  1: i18n.t('一'),
  2: i18n.t('二'),
  3: i18n.t('三'),
  4: i18n.t('四'),
  5: i18n.t('五'),
  6: i18n.t('六'),
  7: i18n.t('七'),
  8: i18n.t('八'),
  9: i18n.t('九'),
  0: i18n.t('零')
}

// 数字转字符串
export const filterValue = (value) => {
  let filterName = ''
  if (!value) return ''
  if (!!value && value.length > 0) {
    filterName = mapNumber[value]
  }
  let strArr = (value + '').split('')
  strArr.forEach((v) => {
    filterName = filterName + '' + mapNumber[Number(v)]
  })

  return filterName
}
