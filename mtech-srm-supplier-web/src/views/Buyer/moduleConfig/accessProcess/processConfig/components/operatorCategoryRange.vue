<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <div>
        <h4>{{ $t('待选项') }}</h4>
        <div class="all-list-box">
          <div class="company-tree-box">
            <mt-tree-view
              key="company-tree"
              class="company-tree"
              v-if="showCompanyTree"
              ref="treeViewCompany"
              :fields="companyTree"
              @nodeSelected="nodeSelectedCompany"
            ></mt-tree-view>
          </div>
          <div class="category-tree-box">
            <mt-tree-view
              key="category-tree"
              class="category-tree"
              v-if="showCategoryTree"
              ref="treeViewCategory"
              :fields="categoryTree"
              :show-check-box="true"
              :checked-nodes="checkedNodesCategory"
              @nodeChecked="nodeCheckedCategory"
            ></mt-tree-view>
            <p v-if="categoryTree.dataSource.length === 0" class="category-no-data">
              {{ $t('请先选择公司') }}
            </p>
          </div>
        </div>
      </div>
      <div class="select-list-box">
        <h4>{{ $t('已选项') }}</h4>
        <div class="select-list">
          <template v-if="selectTree && selectTree.dataSource && selectTree.dataSource.length">
            <div class="select-tree">
              <mt-tree-view
                key="select-tree"
                v-if="showSelectTree"
                ref="treeViewSelect"
                :fields="selectTree"
              ></mt-tree-view>
            </div>
            <p class="clear-btn" @click="clearSltCompany">{{ $t('清空选择') }}</p>
          </template>
          <p v-else class="category-no-data">{{ $t('请先选择公司和品类') }}</p>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      companyTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      selectTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      categoryTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      checkedNodesCategory: [],
      checkedNodesObj: [],
      sltCompanyList: [],
      showCompanyTree: false, // treeview数据不更新。。
      showCategoryTree: false, // treeview数据不更新。。
      showSelectTree: false, // treeview数据不更新。。
      currentCompany: {},

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存范围配置') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    accessTemplate() {
      return this.modalData.accessTemplate
    },
    rangeList() {
      return this.modalData.rangeList
    }
  },
  mounted() {
    this.show()
    this.getCompanyTree()
  },
  methods: {
    // 数据回显
    initData() {
      if (this.rangeList && this.rangeList.length) {
        const treeRef = this.$refs.treeViewCompany.ejsRef
        this.rangeList.forEach((item) => {
          let node = treeRef.getNode(item.orgId)
          this.sltCompanyList.push(node)
        })
        if (this.sltCompanyList.length) {
          const { id } = this.sltCompanyList[0]
          this.selectedNodesCompany = [id]
        }
      }
    },

    getCompanyTree() {
      // this.$API.supplierInvitation
      //   .findOrganizationCompanyTreeByAccount({
      //     accountId: 0,
      //   })
      this.$API.supplierInvitation
        .findOrganizationCompanyTreeByAccount3({ orgLevelCode: 'ORG02', orgType: 'ORG001PRO' })
        .then((res) => {
          const { code, data } = res
          if (code == 200 && data) {
            this.$set(this.companyTree, 'dataSource', data)
            this.showCompanyTree = true
            this.$nextTick(() => {
              const treeViewRef = this.$refs.treeViewCompany && this.$refs.treeViewCompany.ejsRef
              treeViewRef && treeViewRef.expandAll()
              // this.initData();
            })
          }
        })
    },

    // 设置品类树的选中状态
    setCategoryTreeChecked() {
      const { id: currentCompanyId } = this.currentCompany
      let index = this.checkedNodesObj.findIndex((item) => item.id === currentCompanyId)
      this.checkedNodesCategory = index > -1 ? this.checkedNodesObj[index].nodes : []
    },

    /**
     * id 公司id
     */
    getCategoryTree(id) {
      // 假接口，不传值
      this.showCategoryTree = false
      this.$API.supplierInvitation
        .getProdcutTree({
          id: id
        })
        .then((res) => {
          const { code, data } = res
          if (code == 200 && data) {
            this.categoryTree.dataSource = data
            setTimeout(() => {
              this.showCategoryTree = true
              this.$nextTick(() => {
                this.setCategoryTreeChecked()
                const treeViewRef =
                  this.$refs.treeViewCategory && this.$refs.treeViewCategory.ejsRef
                treeViewRef && treeViewRef.expandAll()
              })
            }, 200)
          }
        })
    },

    nodeSelectedCompany(args) {
      const { nodeData } = args
      if (!nodeData) return
      // 获取品类树
      this.getCategoryTree(nodeData.id)

      this.currentCompany = nodeData
      let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)
      index === -1 && this.sltCompanyList.push(this.currentCompany)
    },

    nodeCheckedCategory(args) {
      /**
       * 返回的data数组中，第一个应该是当前点击的currentNode
       * 如果当前动作是check,如果currentNode的hasChildren是true，找到他下面的叶子节点，禁用
       * 如果currentNode的hasChildren是false，，选中的nodes里如果有hasChildren是true的，禁用他的子节点
       * 如果当前动作是uncheck：取消所有的禁用，...
       */
      this.showSelectTree = false
      const treeViewRef = this.$refs.treeViewCategory.ejsRef
      const { action, data } = args || {}
      let currentNode = data && data.length && data[0]
      let disabledNodes = treeViewRef.getDisabledNodes()
      let allCheckedNodes = treeViewRef.getAllCheckedNodes()
      // 保存当前公司的品类选择
      let index = this.checkedNodesObj.findIndex((item) => item.id === this.currentCompany.id)
      if (index === -1) {
        this.checkedNodesObj.push({
          id: this.currentCompany.id,
          nodes: allCheckedNodes
        })
      } else {
        this.$set(this.checkedNodesObj[index], 'nodes', allCheckedNodes)
      }

      // 叶子节点的禁用逻辑
      if (action === 'check' && currentNode.hasChildren) {
        let currentTree = treeViewRef.getTreeData(currentNode.id)
        let leafs = this.getLeaf(currentTree)
        treeViewRef.disableNodes(leafs)
      } else {
        action === 'uncheck' && treeViewRef.enableNodes(disabledNodes)
        let sltNodes = allCheckedNodes.map((id) => {
          return treeViewRef.getNode(id)
        })
        sltNodes.forEach((node) => {
          if (node.hasChildren) {
            let currentTree = treeViewRef.getTreeData(node.id)
            let leafs = this.getLeaf(currentTree)
            treeViewRef.disableNodes(leafs)
          }
        })
      }

      let nodes = this.getSltNodes()
      if (nodes) {
        this.$set(this.currentCompany, 'children', nodes)
        let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)
        this.sltCompanyList[index] = {
          ...this.currentCompany,
          expanded: true
        }

        let sltTreeData = this.sltCompanyList
          .filter((item) => item.children && item.children.length)
          .map((node) => {
            return {
              id: node.id,
              name: node.text,
              children: node.children
            }
          })
        this.$set(this.selectTree, 'dataSource', sltTreeData)
        setTimeout(() => {
          this.showSelectTree = true
          this.$nextTick(() => {
            const treeViewSelectRef = this.$refs.treeViewSelect && this.$refs.treeViewSelect.ejsRef
            treeViewSelectRef && treeViewSelectRef.expandAll()
          })
        }, 200)
      }
    },

    /**
     * 选中的节点，保存为树形
     * 非叶子节点的isChecked表示该节点是否选中（他的叶子节点被选中了部分，则是false）
     */
    getSltNodes() {
      const treeViewRef = this.$refs.treeViewCategory.ejsRef
      let checkedId = treeViewRef.getAllCheckedNodes()
      let sltParentNode = []
      if (checkedId && checkedId.length) {
        const { dataSource } = this.categoryTree
        let tree = this.setCheckStatus(dataSource, checkedId)
        const getSltParentNode = (tree) => {
          if (tree && tree.length) {
            tree.forEach((node) => {
              if (node.isChecked) {
                delete node.children
                sltParentNode.push(node)
              } else if (node.children && node.children.length) {
                getSltParentNode(node.children)
              }
            })
          }
        }
        getSltParentNode(tree)
      }
      return sltParentNode || []
    },

    // 加状态
    setCheckStatus(tree, checkedId) {
      if (tree && tree.length) {
        tree.forEach((node) => {
          node.isChecked = false
          if (node.children && node.children.length) {
            let childrenIds = node.children.map((child) => {
              return child.id
            })
            node.isChecked = this.isContainArr(checkedId, childrenIds)
            this.setCheckStatus(node.children, checkedId)
          } else {
            let index = checkedId.findIndex((id) => id === node.id)
            index > -1 && (node.isChecked = true)
          }
        })
      }
      return tree
    },

    isContainArr(parent, child) {
      return child.every((item) => {
        return parent.some((v) => {
          return item == v
        })
      })
    },

    // 获取叶子节点的id
    getLeaf(tree) {
      const leaf = []
      const getChild = (tree) => {
        tree &&
          tree.forEach((item) => {
            if (item.children && item.children.length) {
              getChild(item.children)
            } else {
              let index = leaf.findIndex((leafItem) => leafItem === item.id)
              index === -1 && leaf.push(item.id)
            }
          })
      }
      getChild(tree)
      return leaf
    },

    // 清空选择
    clearSltCompany() {
      this.checkedNodesCategory = []
      this.checkedNodesObj = []
      this.currentCompany = {}
      this.sltCompanyList = []
      this.$set(this.selectTree, 'dataSource', [])
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      const { id: accessTemplateId } = this.accessTemplate
      const { dataSource } = this.selectTree
      let companyCategoryDTOList = []
      dataSource.forEach((item) => {
        const { id, name, orgCode } = item
        let companyInfo = {
          orgCode: orgCode,
          orgId: id,
          orgName: name
        }
        const { children } = item
        if (children && children.length) {
          children.forEach((child) => {
            const { id, name, categoryCode } = child
            let categoryInfo = {
              categoryCode: categoryCode,
              categoryId: id,
              categoryName: name
            }
            companyCategoryDTOList.push({
              ...companyInfo,
              ...categoryInfo
            })
          })
        }
      })
      let params = {
        accessTemplateId,
        companyCategoryDTOList
      }
      if (companyCategoryDTOList && companyCategoryDTOList.length) {
        this.$API.AccessProcess['updateAccessScope'](params).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function')
          }
        })
      } else {
        this.$toast({ content: this.$t('请先选择公司和品类'), type: 'error' })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  display: flex;
  h4 {
    font-size: 14px;
    color: #292929;
    font-weight: bold;
    margin: 20px 0 10px 0;
  }
  .all-list-box {
    display: flex;
    background: #fff;
    border: 1px solid #e8e8e8;
    min-height: 300px;
  }
  .company-tree-box {
    width: 300px;
    min-height: 300px;
    /deep/ .mt-tree-view {
      width: 100%;
    }
  }

  .category-tree-box {
    flex: 1;
    width: 200px;
    min-height: 300px;
    border-left: 1px solid #e8e8e8;
    /deep/ .mt-tree-view {
      width: 100%;
    }
  }
  .select-list-box {
    flex: 1;
    margin-left: 20px;
    .select-list {
      min-height: 300px;
      background: #fff;
      border: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;
      .select-tree {
        flex: 1;
        /deep/ .mt-tree-view {
          width: 100%;
        }
      }
    }
    .clear-btn {
      font-size: 14px;
      font-weight: bold;
      color: #00469c;
      text-align: center;
      margin: 20px 0 38px 0;
      cursor: pointer;
    }
  }
  .category-no-data {
    font-size: 14px;
    color: #9a9a9a;
    padding-top: 20px;
    text-align: center;
  }
}
</style>
