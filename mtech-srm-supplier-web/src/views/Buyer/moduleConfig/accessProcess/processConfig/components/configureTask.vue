<template>
  <!-- 动态配置任务 -->
  <div>
    <h4>{{ $t('配置任务') }}</h4>
    <div v-for="(task, index) in taskTemplateList" :key="task.id" class="task-item">
      <div class="task-title">
        <span>{{ $t('任务') }}{{ (index + 1) | taskOrder }}</span>
        <div class="icon-delete-box" @click="deleteCurrentTask(index)">
          <i class="mt-icons mt-icon-icon_solid_delete_2 icon-delete"></i
          ><span class="delete-tips">{{ $t('删除任务') }}</span>
        </div>
      </div>
      <mt-form class="task-item-select" :ref="'taskRef' + index" :model="task" :rules="taskRules">
        <mt-row :gutter="10">
          <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item :label="$t('调查表分类')" label-style="top" prop="taskDefineClassify">
              <mt-select
                :placeholder="$t('请选择调查表分类')"
                v-model="task.taskDefineClassify"
                :data-source="taskTemplateClassifyList"
                :fields="fields"
                @select="taskTemplateClassifySelect($event, index)"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item :label="$t('调查表')" label-style="top" prop="taskDefineId">
              <mt-select
                :placeholder="$t('请选择调查表')"
                v-model="task.taskDefineId"
                :data-source="task.taskDefineList"
                :fields="taskDefineFields"
                @select="taskDefineSelect($event, index)"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item :label="$t('填写方')" label-style="top" prop="taskOwner">
              <mt-select
                v-model="task.taskOwner"
                :data-source="taskOwnerList"
                :placeholder="$t('请选择填写方')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <!-- <mt-col :xs="24" :md="6" :lg="6">
            <mt-form-item :label="$t('填写角色')" label-style="top" prop="taskRoleId">
              <mt-select
                v-model="task.taskRoleId"
                :data-source="taskRoleList"
                :placeholder="$t('选择填写角色')"
                :fields="taskRoleFields"
                @select="handlSltTaskRole($event, index)"
              ></mt-select>
            </mt-form-item>
          </mt-col> -->
        </mt-row>
        <mt-row :gutter="10">
          <mt-col :xs="24" :md="8" :lg="8">
            <mt-form-item :label="$t('完成条件')" label-style="top" prop="finishCondition">
              <mt-select
                v-model="task.finishCondition"
                :data-source="conditionList"
                :placeholder="$t('请选择完成条件')"
                :fields="conditionFields"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col v-if="task.finishCondition === 2" :xs="24" :md="16" :lg="16">
            <mt-form-item :label="$t('选择审批流')" label-style="top" prop="workflowKey">
              <mt-select
                v-model="task.workflowKey"
                :data-source="workflowList"
                :placeholder="$t('请选择审批流')"
                :fields="workflowKeyFields"
                @select="handlSltWorkflowKey($event, index)"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <div class="add-task-btn" @click="addOneTask">
      <i class="mt-icons mt-icon-icon_solid_add"></i>
      <span>{{ $t('添加任务') }}</span>
    </div>
  </div>
</template>

<script>
import { validateFormList, getRandom } from './index'
import utils from '@/utils/utils'
const DefaultTask = {
  taskDefineClassify: '', // 调查表分类
  taskDefineType: '', // 调查表类型
  taskDefineId: '', // 调查表id
  taskDefineName: '', //调查表名称
  taskOwner: '', // 填写方
  // taskRoleId: "", // 填写角色
  // taskRoleName: "", // 填写角色名称
  finishCondition: '', // 完成条件id
  workflowKey: null, // 工作流程模板key
  workflowKeyName: null, // 工作流程模板名称
  taskDefineList: [] // 调查表列表，根据调查表类型动态获取
}

export default {
  filters: {
    taskOrder(val) {
      return utils.numIntToChinese(val)
    }
  },
  props: {
    dataSource: {
      type: Array,
      required: false,
      default: () => []
    },
    // 审批流
    workflowList: {
      type: Array,
      default: () => []
    },
    // 调查表类型
    taskTemplateClassifyList: {
      type: Array,
      default: () => []
    },
    // 公司类型0，品类1
    isCompany: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 配置任务列表
      taskTemplateList: [
        JSON.parse(
          JSON.stringify({
            id: getRandom(),
            ...DefaultTask
          })
        )
      ],
      fields: {
        text: 'itemName',
        value: 'itemCode'
      },
      taskDefineFields: {
        text: 'taskTemplateName',
        value: 'id'
      },

      // 填写方
      taskOwnerList: [
        {
          text: this.$t('采方'),
          value: 0
        },
        {
          text: this.$t('供方'),
          value: 1
        }
      ],

      // 任务填写方
      taskRoleList: [
        {
          taskRoleName: this.$t('采方'),
          taskRoleId: '0'
        },
        {
          taskRoleName: this.$t('供方'),
          taskRoleId: '1'
        }
      ],
      taskRoleFields: {
        text: 'taskRoleName',
        value: 'taskRoleId'
      },

      // 完成条件
      conditionList: [
        {
          conditionName: this.$t('自动完成'),
          finishCondition: 1
        },
        {
          conditionName: this.$t('审批完成'),
          finishCondition: 2
        }
      ],
      conditionFields: {
        text: 'conditionName',
        value: 'finishCondition'
      },

      // this.$t("审批流")
      workflowKeyFields: {
        text: 'completionTypeName',
        value: 'completionTypeId'
      },

      taskRules: {
        taskDefineClassify: [
          { required: true, message: this.$t('请选择调查表分类'), trigger: 'blur' }
        ],
        taskDefineId: [{ required: true, message: this.$t('请选择调查表'), trigger: 'blur' }],
        taskOwner: [{ required: true, message: this.$t('请选择填写方'), trigger: 'blur' }],
        // taskRoleId: [
        //   { required: true, message: this.$t("请选择填写角色"), trigger: "blur" },
        // ],
        finishCondition: [{ required: true, message: this.$t('请选择完成条件'), trigger: 'blur' }],
        workflowKey: [{ required: true, message: this.$t('请选择审批流'), trigger: 'blur' }]
      },

      initLoaded: false
    }
  },
  watch: {
    dataSource() {
      this.initData()
    }
  },

  mounted() {
    this.initData()
  },
  methods: {
    // 编辑时，任务列表的初始化。根据列表中的调查表类型，把每个任务的调查表列表数据源对应上
    initData() {
      if (this.dataSource.length) {
        if (this.initLoaded === true) return
        this.taskTemplateList = JSON.parse(JSON.stringify(this.dataSource))
        this.taskTemplateList.forEach((item, index) => {
          this.setTaskDefineList(item.taskDefineClassify, index)
        })
        this.initLoaded = true
      }
    },

    // 获取当前的配置列表，父级可在提交表单时调用
    getCurrentTaskConfig() {
      return this.taskTemplateList.map((item) => {
        const {
          taskDefineClassify,
          taskDefineType,
          taskDefineId,
          taskDefineName,
          taskOwner,
          // taskRoleId,
          // taskRoleName,
          finishCondition,
          workflowKey,
          workflowKeyName
        } = item
        return {
          taskDefineClassify,
          taskDefineType,
          taskDefineId,
          taskDefineName,
          taskOwner,
          // taskRoleId,
          // taskRoleName,
          finishCondition,
          workflowKey,
          workflowKeyName
        }
      })
    },

    validateTaskConfig() {
      return validateFormList(this.taskTemplateList, 'taskRef', this)
    },

    deleteCurrentTask(index) {
      this.taskTemplateList.splice(index, 1)
    },

    addOneTask() {
      let rs = this.validateTaskConfig()
      if (rs) {
        this.taskTemplateList.push(
          JSON.parse(
            JSON.stringify({
              id: getRandom(),
              ...DefaultTask
            })
          )
        )
      }
    },

    // 根据列表中的调查表类型，把每个任务的调查表列表数据源对应上
    setTaskDefineList(classify, index) {
      const taskTemplateType = this.isCompany ? 0 : 1
      let params = {
        condition: '',
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          { field: 'status', operator: 'equal', value: 1 }, // 启用
          {
            field: 'taskTemplateType',
            operator: 'equal',
            value: taskTemplateType
          },
          {
            field: 'taskTemplateClassify',
            operator: 'equal',
            value: classify
          }
        ]
      }
      this.$API.QuestionnaireConfig['queryFormTemplate'](params).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          const { records } = data
          this.$set(this.taskTemplateList[index], 'taskDefineList', records)
        }
      })
    },

    // 选择调查表类型时需要更新调查表列表
    taskTemplateClassifySelect(e, index) {
      const { itemData } = e
      console.log(itemData.itemCode, index, this.$t('选择调查表类型'))
      this.setTaskDefineList(itemData.itemCode, index)
    },

    taskDefineSelect(e, index) {
      const { itemData } = e
      if (itemData) {
        const { taskTemplateType, taskTemplateName } = itemData
        this.$set(this.taskTemplateList[index], 'taskDefineName', taskTemplateName)
        this.$set(this.taskTemplateList[index], 'taskDefineType', taskTemplateType)
      }
    },
    handlSltTaskRole(e, index) {
      const { itemData } = e
      if (itemData) {
        const { taskRoleName } = itemData
        this.$set(this.taskTemplateList[index], 'taskRoleName', taskRoleName)
      }
    },
    handlSltWorkflowKey(e, index) {
      const { itemData } = e
      if (itemData) {
        const { workflowKeyName } = itemData
        this.$set(this.taskTemplateList[index], 'workflowKeyName', workflowKeyName)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
h4 {
  font-size: 14px;
  color: #292929;
  font-weight: bold;
  margin: 20px 0 10px 0;
}
.task-title {
  display: flex;
  margin-bottom: 20px;
  > span {
    font-size: 14px;
    font-weight: bold;
    color: #292929;
    flex: 1;
  }
  .icon-delete-box {
    color: #ff4949;
    font-weight: bold;
    cursor: pointer;
    .delete-tips {
      font-size: 14px;
      margin-left: 10px;
    }
  }
}

.add-task-btn {
  display: inline-flex;
  color: #6386c1;
  cursor: pointer;
  margin: 10px 0;
  > i {
    font-size: 20px;
  }
  > span {
    display: inline-block;
    font-size: 14px;
    font-weight: bold;
    margin-left: 10px;
    line-height: 24px;
  }
}
.custom-rule-container {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 20px;
  margin-top: 40px;
}
</style>
