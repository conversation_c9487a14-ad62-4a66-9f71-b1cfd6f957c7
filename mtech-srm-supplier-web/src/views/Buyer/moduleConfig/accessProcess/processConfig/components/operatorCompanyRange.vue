<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <div>
        <h4>{{ $t('待选项') }}</h4>
        <div class="all-list-box">
          <mt-tree-view
            v-if="showTree"
            ref="treeView"
            :checked-nodes="checkedNodes"
            :fields="plateTree"
            :show-check-box="true"
            :auto-check="false"
            @nodeChecked="nodeChecked"
          ></mt-tree-view>
        </div>
      </div>
      <div class="select-list-box">
        <h4>{{ $t('已选项') }}</h4>
        <div class="select-list">
          <template v-if="sltList && sltList.length">
            <div class="select-list-item">
              <p v-for="slt in sltList" :key="slt.id">
                {{ slt.name }}
              </p>
            </div>
            <p class="clear-btn" @click="clearSltCompany">{{ $t('清空选择') }}</p>
          </template>
          <p v-else class="select-no-data">{{ $t('请先选择公司') }}</p>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      plateTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      checkedNodes: [],
      sltList: [],
      companyList: [],
      showTree: false, // treeview数据不更新。。

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存范围配置') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    accessTemplate() {
      return this.modalData.accessTemplate
    },
    rangeList() {
      return this.modalData.rangeList
    }
  },
  mounted() {
    this.show()
    this.getCompanyTree()
  },
  methods: {
    getCompanyTree() {
      // this.$API.supplierInvitation
      //   .findOrganizationCompanyTreeByAccount({
      //     accountId: 0,
      //   })
      this.$API.supplierInvitation
        .findOrganizationCompanyTreeByAccount3({ orgLevelCode: 'ORG02', orgType: 'ORG001PRO' })
        .then((res) => {
          const { code, data } = res
          if (code == 200 && data) {
            this.$set(this.plateTree, 'dataSource', data)
            this.showTree = true
            this.$nextTick(() => {
              this.checkedNodes = this.rangeList.map((item) => {
                return item.orgId
              })
              const treeViewRef = this.$refs.treeView.ejsRef
              treeViewRef.expandAll()
            })
          }
        })
    },

    nodeChecked() {
      const treeViewRef = this.$refs.treeView.ejsRef
      let sltNodes = treeViewRef.getAllCheckedNodes()
      let arr = []
      sltNodes.forEach((node) => {
        let sltData = treeViewRef.getTreeData(node)[0]
        arr.push(sltData)
      })
      this.sltList = arr
    },

    // 清空选择
    clearSltCompany() {
      this.checkedNodes = []
      this.sltList = []
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      const { id: accessTemplateId } = this.accessTemplate
      let companyCategoryDTOList = this.sltList.map((item) => {
        const { id, name, orgCode } = item
        return {
          orgCode: orgCode,
          orgId: id,
          orgName: name
        }
      })
      let params = {
        accessTemplateId,
        companyCategoryDTOList
      }
      if (companyCategoryDTOList && companyCategoryDTOList.length) {
        this.$API.AccessProcess['updateAccessScope'](params)
          .then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
          .catch((err) => {
            this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
          })
      } else {
        this.$toast({ content: this.$t('请先选择公司'), type: 'error' })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  display: flex;
  h4 {
    font-size: 14px;
    color: #292929;
    font-weight: bold;
    margin: 20px 0 10px 0;
  }
  .all-list-box {
    display: flex;
    width: 500px;
    background: #fff;
    border: 1px solid #e8e8e8;
    min-height: 300px;
    /deep/ #treeview {
      width: 500px;
    }
  }
  .company-list {
    width: 300px;
  }
  .select-list-box {
    flex: 1;
    margin-left: 20px;
    .select-list {
      padding: 10px 20px;
      min-height: 300px;
      background: #fff;
      border: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;
      .select-list-item {
        flex: 1;
      }
    }
    .select-no-data {
      font-size: 14px;
      color: #9a9a9a;
      padding-top: 20px;
      text-align: center;
    }
    .clear-btn {
      font-size: 14px;
      font-weight: bold;
      color: #00469c;
      text-align: center;
      margin: 20px 0 38px 0;
      cursor: pointer;
    }
  }
}
</style>
