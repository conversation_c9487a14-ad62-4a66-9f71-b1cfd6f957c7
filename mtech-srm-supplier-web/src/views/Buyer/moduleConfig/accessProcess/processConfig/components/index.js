export function validateFormList(list = [], ref = 'ref', _this) {
  let len = list.length
  let validRs = true
  for (let i = 0; i < len; i++) {
    const refName = ref + i
    _this.$refs[refName] &&
      _this.$refs[refName][0] &&
      _this.$refs[refName][0].validate((valid) => {
        if (!valid) {
          validRs = false
          return
        }
      })
  }
  return validRs
}
export function validateRefList(refList = [], validateMethod = '') {
  let len = refList.length
  let validRs = true
  for (let i = 0; i < len; i++) {
    let refItem = refList[i]
    validRs = refItem[validateMethod]()
    if (!validRs) {
      return false
    }
  }
  return validRs
}

export function getRandom() {
  return Math.floor(Math.random() * 1000000)
}
