<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>

      <div class="slider-content">
        <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
          <mt-row :gutter="20">
            <mt-col :xs="24" :md="12" :lg="12">
              <mt-form-item
                class="form-item"
                :label="$t('阶段名称')"
                label-style="top"
                prop="stageDefineId"
              >
                <mt-select
                  v-model="formInfo.stageDefineId"
                  :placeholder="$t('请输入阶段名称')"
                  float-label-type="Never"
                  :data-source="stageDefineList"
                  :fields="stageFields"
                  @select="handleSltstageDefine"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :xs="24" :md="12" :lg="12">
              <mt-form-item
                class="form-item"
                :label="$t('阶段标识')"
                label-style="top"
                prop="stageType"
              >
                <mt-select
                  :placeholder="$t('请选择阶段标识')"
                  v-model="formInfo.stageType"
                  :data-source="stageTypeList"
                  :fields="stageTypeFields"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>

        <!-- 配置任务 -->
        <configure-task
          ref="defaultConfigureTask"
          :data-source="defaultTaskList"
          :workflow-list="workflowList"
          :is-company="isCompany"
          :task-template-classify-list="taskTemplateClassifyList"
        ></configure-task>
        <!-- 晋级 -->
        <config-promotion
          ref="defaultConfigPromotion"
          :data-source="defaultPromotion"
          :workflow-list="workflowList"
          :is-company="isCompany"
          :is-default="true"
        ></config-promotion>

        <!-- 特殊规则/自定义 -->
        <div
          v-for="(custom, customIndex) in customRuleList"
          :key="custom.id"
          class="custom-rule-container"
        >
          <mt-form :ref="'customRuleRef' + customIndex" :model="custom" :rules="customRuleRules">
            <mt-form-item
              class="form-item"
              :label="$t('特殊规则名称')"
              label-style="top"
              prop="taskRuleName"
            >
              <mt-input
                v-model="custom.taskRuleName"
                :placeholder="$t('命名此特殊规则')"
              ></mt-input>
            </mt-form-item>
            <h4>{{ $t('特殊规则范围') }}</h4>
            <mt-form-item
              v-if="isCompany"
              class="form-item"
              :label="$t('调查表标识')"
              label-style="top"
              prop="taskRuleExpression"
            >
              <mt-select
                :placeholder="$t('选择调查表标识')"
                v-model="custom.taskRuleExpression"
                :data-source="searchList"
                :fields="stageTypeFields"
              ></mt-select>
            </mt-form-item>
          </mt-form>
          <!-- 品类范围选择 -->
          <div v-if="!isCompany" class="category-select-range">
            <div>
              <h4>{{ $t('全部范围') }}</h4>
              <div class="all-list-box">
                <div class="company-list">
                  <template v-if="companyList && companyList.length">
                    <p
                      :class="[
                        'company-category-item',
                        sltCompanyId === companyItem.orgId && 'is-select'
                      ]"
                      v-for="(companyItem, companyItemIndex) in companyList"
                      :key="companyItem.orgId"
                      @click="handleSltCompany(companyItem, companyItemIndex)"
                    >
                      {{ companyItem.orgName }}
                    </p>
                  </template>
                  <p v-else class="company-category-item">{{ $t('暂无数据') }}</p>
                </div>

                <div class="category-list">
                  <template v-if="categoryList && categoryList.length">
                    <div
                      v-for="categoryItem in categoryList"
                      :key="categoryItem.categoryId"
                      :class="['company-category-item', categoryItem.checked && 'is-select']"
                    >
                      <mt-checkbox
                        :checked="categoryItem.checked"
                        class="category-checkbox"
                        @change="handleCheckbox($event, categoryItem, customIndex)"
                      ></mt-checkbox>
                      <span>{{ categoryItem.categoryName }}</span>
                    </div>
                  </template>
                  <p v-else class="list-no-data">{{ $t('请先选择公司') }}</p>
                </div>
              </div>
            </div>
            <div class="config-range">
              <h4>
                {{ $t('特殊规则范围')
                }}<span class="clear-btn" @click="clearRange">{{ $t('清空') }}</span>
              </h4>
              <div class="range-list">
                <template
                  v-if="
                    sltCategoryTree &&
                    sltCategoryTree.dataSource &&
                    sltCategoryTree.dataSource.length
                  "
                >
                  <mt-tree-view v-if="showTree" :fields="sltCategoryTree"></mt-tree-view>
                </template>
                <p v-else class="list-no-data">{{ $t('请先选择公司和品类') }}</p>
              </div>
            </div>
          </div>

          <!-- 配置任务 -->
          <configure-task
            ref="customConfigTask"
            :data-source="custom.customTaskList"
            :task-template-classify-list="taskTemplateClassifyList"
            :workflow-list="workflowList"
            :is-company="isCompany"
          ></configure-task>
          <!-- 晋级 -->
          <config-promotion
            ref="customConfigPromotion"
            :data-source="custom.customPromotion"
            :workflow-list="workflowList"
            :is-company="isCompany"
            :index="customIndex"
          ></config-promotion>

          <p class="delete-btn" @click="deleteCurrentCustomRule(customIndex)">
            {{ $t('删除此规则') }}
          </p>
        </div>

        <div class="add-custom-btn" @click="addCustomRule">
          <i class="mt-icons mt-icon-icon_solid_add"></i>
          <span>{{ $t('添加特殊规则') }}</span>
        </div>
      </div>

      <div class="slider-footer mt-flex">
        <span @click="confirm">{{ $t('确定') }}</span>
        <span v-if="isEdit" @click="deleteCurrentStage">{{ $t('删除此阶段') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import ConfigureTask from './configureTask.vue'
import ConfigPromotion from './configPromotion.vue'
import { validateFormList, getRandom, validateRefList } from './index'

export default {
  components: {
    ConfigureTask,
    ConfigPromotion
  },
  data() {
    return {
      formInfo: {
        stageDefineId: '', // 阶段名称
        stageType: null // 阶段标识
      },
      stageTypeList: [], // 阶段标识
      searchList: [], // 阶段标识
      stageTypeFields: {
        text: 'itemName',
        value: 'itemCode'
      },

      stageDefineList: [{ stageName: this.$t('阶段1'), id: '1122' }], // 阶段名称
      stageFields: {
        text: 'stageName',
        value: 'id'
      },
      sltStageDefine: {}, // 选择的阶段名称
      taskTemplateClassifyList: [], // 调查表类型

      rules: {
        stageDefineId: [{ required: true, message: this.$t('请输入阶段名称'), trigger: 'blur' }],
        stageType: [{ required: true, message: this.$t('请选择阶段标识'), trigger: 'blur' }]
      },

      defaultTaskList: [], // 默认规则配置的任务列表
      defaultPromotion: {}, // 默认规则配置的晋升

      // 自定义规则列表
      customRuleList: [],
      // 特殊规则/自定义
      customRule: {},
      customRuleRules: {
        taskRuleName: [{ required: true, message: this.$t('请命名此特殊规则'), trigger: 'blur' }],
        taskRuleExpression: [
          { required: true, message: this.$t('请选择调查表标识'), trigger: 'blur' }
        ]
      },

      // 特殊规则 范围
      showTree: false, // treeview更新数据源，视图不更新
      companyList: [],

      // 品类列表，根据选择的公司
      categoryList: [],
      sltCompanyId: '',
      sltCategoryTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'category'
      },
      relationIds: [] // 品类，特殊规则，选中的品类，接口传值
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    isCompany() {
      return this.modalData.isCompany
    },
    accessTemplate() {
      return this.modalData.accessTemplate
    },
    stageInfo() {
      return this.modalData.stageInfo
    },
    // 审批流列表
    workflowList() {
      return this.modalData.workflowList
    }
  },
  mounted() {
    this.queryStage()
    this.queryStageType()
    this.querySearchType()
    this.queryTaskClassify()
    this.isEdit && this.queryStageDetail()
    !this.isCompany && this.queryAccessScope()
  },
  methods: {
    queryStageDetail() {
      this.$store.commit('startLoading')
      const { id } = this.stageInfo
      this.$API.AccessProcess['queryStageDetail'](id).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          const { stageDefineId, stageType, stageRuleTemplateViewDTOList } = data
          this.formInfo = Object.assign({}, this.formInfo, {
            stageDefineId,
            stageType
          })
          if (stageRuleTemplateViewDTOList && stageRuleTemplateViewDTOList.length) {
            // 默认
            let defaultIndex = stageRuleTemplateViewDTOList.findIndex(
              (item) => item.taskRuleType == 1
            )

            let defaultInfo = stageRuleTemplateViewDTOList[defaultIndex]
            this.defaultTaskList = defaultInfo.stageTaskTemplateDTOList
            const {
              upgradeRuleFront,
              upgradeRuleType,
              upgradeWorkflowKey,
              upgradeWorkflowKeyName
            } = defaultInfo
            this.defaultPromotion = {
              upgradeRuleFront,
              upgradeRuleType,
              upgradeWorkflowKey,
              upgradeWorkflowKeyName
            }
            // 自定义
            let customList = stageRuleTemplateViewDTOList
              .filter((item) => item.taskRuleType != 1)
              .map((item) => {
                const {
                  stageTaskTemplateDTOList,
                  upgradeRuleFront,
                  upgradeRuleType,
                  upgradeWorkflowKey,
                  upgradeWorkflowKeyName
                } = item
                return {
                  ...item,
                  customTaskList: stageTaskTemplateDTOList,
                  customPromotion: {
                    upgradeRuleFront,
                    upgradeRuleType,
                    upgradeWorkflowKey,
                    upgradeWorkflowKeyName
                  }
                }
              })
            this.customRuleList = customList
            console.log(customList, 'customList')
          }
        }
        this.$store.commit('endLoading')
      })
    },

    // 阶段名称列表
    queryStage() {
      const { accessTemplateType } = this.accessTemplate
      let params = {
        condition: '',
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          {
            condition: 'equal',
            field: 'accessType',
            label: this.$t('阶段类型'),
            operator: 'equal',
            type: 'number',
            value: accessTemplateType
          }
        ]
      }

      this.$API.AccessProcess['queryStage'](params).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          const { records } = data
          this.stageDefineList = records
        }
      })
    },
    // 阶段标识
    queryStageType() {
      const { accessTemplateType } = this.accessTemplate
      const dictCode = accessTemplateType == 0 ? 'companyStageMark' : 'categortyStageMark'
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.stageTypeList = data
        }
      })
    },
    // 调查表标识
    querySearchType() {
      const dictCode = 'formTaskMark'
      this.$API.AccessProcess['queryDict']({
        dictCode
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.searchList = data
        }
      })
    },
    // 调查表分类
    queryTaskClassify() {
      this.$API.AccessProcess['queryDict']({
        dictCode: 'adTaskClassify'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.taskTemplateClassifyList = data
        }
      })
    },
    // 特殊规则范围查询
    queryAccessScope() {
      const { id } = this.accessTemplate
      this.$API.AccessProcess['queryAccessScope'](id).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.companyList = data.companyRelationDTOList
        }
      })
    },

    clearRange() {
      this.showTree = false
      this.$set(this.sltCategoryTree, 'dataSource', [])
      this.categoryList = this.categoryList.map((item) => {
        return {
          ...item,
          checked: false
        }
      })
      setTimeout(() => {
        this.showTree = true
      }, 200)
    },

    // 自定义规则的表单校验
    validateCustom() {
      let rs = validateFormList(this.customRuleList, 'customRuleRef', this)

      if (rs) {
        if (!validateRefList(this.$refs.customConfigTask, 'validateTaskConfig')) return false
        if (!validateRefList(this.$refs.customConfigPromotion, 'validPromotion')) return false
      } else {
        return false
      }
      return true
    },
    // 删除当前自定义的规则
    deleteCurrentCustomRule(index) {
      this.customRuleList.splice(index, 1)
    },
    addCustomRule() {
      // 表单校验
      let len = this.customRuleList.length + 1
      if (len === 0) return false
      let rs = this.validateCustom()
      if (!rs) return false

      this.customRuleList.push({
        id: getRandom(),
        taskRuleType: 2, // 任务规则类型，2：自定义
        taskRuleName: '', // 任务规则名称
        taskRuleExpression: null, // 任务规则表达式
        sequenceNo: len, // 规则优先级
        stageTaskTemplateDTOList: [], // 任务模板列表
        upgradeRuleFront: null,
        upgradeRuleType: null,
        upgradeWorkflowKey: null,
        upgradeWorkflowKeyName: null
      })
    },
    // 选择阶段名称
    handleSltstageDefine(args) {
      const { itemData } = args
      if (itemData) {
        const { id: stageDefineId, stageName } = itemData
        this.sltStageDefine = {
          stageDefineId,
          stageName
        }
      }
    },
    // 特殊规则，选择公司
    handleSltCompany(item) {
      this.sltCompanyId = item.orgId
      if (this.sltCompanyId) {
        let index = this.companyList.findIndex((item) => item.orgId === this.sltCompanyId)
        if (index > -1) {
          const { categoryRelationDTOList } = this.companyList[index]
          this.categoryList =
            categoryRelationDTOList &&
            categoryRelationDTOList.map((item) => {
              const checked = item.checked === undefined ? false : item.checked
              return {
                company: this.companyList[index],
                ...item,
                checked
              }
            })
          console.log(this.categoryList, 'categoryList')
        }
      }
    },
    // 特殊规则，选择了公司，选范围
    handleCheckbox(e, categoryItem, customIndex) {
      // 拿到当前选中的品类的公司id，去树的第一层中找是否已存在
      // 如果已存在，在该节点中push/删除选中的品类,如果删完子节点长度为零，把父节点也清掉
      // 如果不存在，如果是checked是true，新增一级和二级，否则不处理
      categoryItem.checked = e.checked
      // 将选中的操作存到companyList
      if (this.sltCompanyId) {
        let index = this.companyList.findIndex((item) => item.orgId === this.sltCompanyId)
        if (index > -1) {
          const categoryRelationDTOList = this.categoryList.map((item) => {
            const { categoryId, categoryName } = item
            const checked = item.checked === undefined ? false : item.checked
            return {
              categoryId,
              categoryName,
              checked
            }
          })
          this.$set(this.companyList[index], 'categoryRelationDTOList', categoryRelationDTOList)
        }
      }

      this.showTree = false
      const { company, relationId } = categoryItem
      const { dataSource } = this.sltCategoryTree
      const { categoryId, categoryName } = categoryItem
      let index = dataSource.findIndex((item) => item.id === company.orgId)
      if (index > -1) {
        if (e.checked) {
          if (dataSource[index].category) {
            dataSource[index].category.push({
              id: categoryId,
              name: categoryName
            })
          } else {
            this.$set(dataSource[index], 'category', [{ id: categoryId, name: categoryName }])
          }
        } else {
          const { category } = dataSource[index]
          if (category) {
            let categoryIndex = category.findIndex((item) => item.id === categoryId)
            categoryIndex > -1 && category.splice(categoryIndex, 1)
          }
          if (category.length === 0) {
            dataSource.splice(index, 1)
          }
        }
      } else {
        if (e.checked) {
          const { orgId, orgName } = company
          dataSource.push({
            id: orgId,
            name: orgName,
            expanded: true,
            category: [{ id: categoryId, name: categoryName }]
          })
        }
      }

      // relationId
      let { taskRuleExpression: relationIds } = this.customRuleList[customIndex]
      relationIds = relationIds ? relationIds.split('|') : []
      let relationIdIndex = relationIds.findIndex((item) => item === relationId)
      if (e.checked) {
        if (relationIdIndex < 0) {
          relationIds.push(relationId)
        }
      } else {
        if (relationIdIndex > -1) {
          relationIds.slice(relationIdIndex, 1)
        }
      }
      this.$set(this.customRuleList[customIndex], 'taskRuleExpression', relationIds.join('|'))

      setTimeout(() => {
        this.showTree = true
      }, 500)
    },

    // 获取默认规则的任务列表
    getDefaultTaskConfig() {
      return this.$refs.defaultConfigureTask.getCurrentTaskConfig() || []
    },
    // 根据index获取指定的任务列表
    getTaskConfigByIndex(index) {
      return (
        (this.$refs.customConfigTask &&
          this.$refs.customConfigTask[index] &&
          this.$refs.customConfigTask[index].getCurrentTaskConfig()) ||
        [] ||
        []
      )
    },

    // 默认的规则数据
    getDefaultRule() {
      // 配置的任务
      let defaultStageTaskTemplate = this.$refs.defaultConfigureTask.getCurrentTaskConfig()
      // 晋升规则
      let defaultConfigPromotion = this.$refs.defaultConfigPromotion.getCurrentPromotionConfig()
      // 合并数据
      let defaultStageRuleTemplate = {
        sequenceNo: 0,
        stageTaskTemplateDTOList: defaultStageTaskTemplate,
        taskRuleExpression: '', // 任务规则表达式
        taskRuleName: this.$t('默认'), // 任务规则名称
        taskRuleType: 1, // 任务规则类型，1：默认，2：自定义
        ...defaultConfigPromotion
      }
      return defaultStageRuleTemplate
    },
    // 自定义的规则数据
    getCustomRule() {
      let customConfigPromotionRefs = this.$refs.customConfigPromotion || []
      if (customConfigPromotionRefs) {
        customConfigPromotionRefs.forEach((item, index) => {
          let current = item.getCurrentPromotionConfig()
          const { upgradeRuleFront, upgradeRuleType, upgradeWorkflowKey, upgradeWorkflowKeyName } =
            current

          this.$set(this.customRuleList[index], 'upgradeRuleFront', upgradeRuleFront)
          this.$set(this.customRuleList[index], 'upgradeRuleType', upgradeRuleType)
          if (upgradeWorkflowKey && upgradeWorkflowKeyName) {
            this.$set(this.customRuleList[index], 'upgradeWorkflowKey', upgradeWorkflowKey)
            this.$set(this.customRuleList[index], 'upgradeWorkflowKeyName', upgradeWorkflowKeyName)
          }
        })
      }

      let customConfigTaskRefs = this.$refs.customConfigTask || []
      if (customConfigTaskRefs) {
        customConfigTaskRefs.forEach((item, index) => {
          let current = item.getCurrentTaskConfig()
          this.$set(this.customRuleList[index], 'stageTaskTemplateDTOList', current)
        })
      }
      return this.customRuleList
    },

    confirm() {
      this.$refs.formInfo.validate((valid) => {
        // 校验默认的任务
        if (valid) {
          // 校验默认任务的规则
          let taskRs = this.$refs.defaultConfigureTask.validateTaskConfig()
          if (!taskRs) return
          // 校验默认任务的晋升
          let promotionRs = this.$refs.defaultConfigPromotion.validPromotion()
          if (!promotionRs) return

          // 校验特殊的
          let customRs = this.validateCustom()
          if (!customRs) return

          const { id: accessTemplateId } = this.accessTemplate
          const { stageType } = this.formInfo
          let stageRuleTemplateDTOList = []
          let defaultRule = this.getDefaultRule()
          let customRule = this.getCustomRule()
          stageRuleTemplateDTOList = [defaultRule].concat(customRule)

          let params = {
            accessTemplateId,
            ...this.sltStageDefine,
            stageType,
            stageRuleTemplateDTOList
          }
          if (this.isEdit) {
            const { id } = this.stageInfo
            params = Object.assign({}, params, { id })
          }

          const methodName = this.isEdit ? 'updateStage' : 'addStage'
          const toastInfo = this.isEdit ? this.$t('更新') : this.$t('新增')
          this.$API.AccessProcess[methodName](params).then((res) => {
            const { code } = res
            if (code == 200) {
              this.$toast({
                content: `${toastInfo}${this.$t('公司阶段成功')}`,
                type: 'success'
              })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    deleteCurrentStage() {
      const { id } = this.stageInfo
      this.$API.AccessProcess['deleteStage']({ stageTemplateId: id })
        .then((res) => {
          const { code } = res
          if (code == 200) {
            this.$emit('confirm-function')
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
h4 {
  font-size: 14px;
  line-height: 14px;
  color: #292929;
  font-weight: bold;
  margin: 20px 0 10px 0;
}

.custom-rule-container {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 20px;
  margin-top: 40px;
  .delete-btn {
    margin: 20px 0 8px 0;
    font-size: 14px;
    font-weight: bold;
    color: #00469c;
    text-align: center;
    cursor: pointer;
  }
}
.add-custom-btn {
  font-size: 14px;
  font-weight: bold;
  color: #00469c;
  text-align: center;
  line-height: 60px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-top: 20px;
  cursor: pointer;
  > i {
    margin-right: 10px;
  }
}

.category-select-range {
  display: flex;
  width: 100%;
  .config-range {
    flex: 1;
    margin-left: 20px;
    .clear-btn {
      float: right;
      color: #00469c;
      cursor: pointer;
    }
  }
  .all-list-box {
    width: 500px;
    min-height: 210px;
    height: calc(100% - 44px);
    display: flex;
    background: #fff;
    border: 1px solid #e8e8e8;
    .company-list {
      width: 260px;
      border-right: 1px solid #e8e8e8;
    }
    .category-list {
      flex: 1;
    }

    .company-category-item {
      font-size: 14px;
      padding-left: 20px;
      line-height: 30px;
      cursor: pointer;
      .category-checkbox {
        margin-right: 10px;
      }
      &:hover {
        background: #f5f6f9;
      }
    }
    .is-select {
      color: #00469c;
      font-weight: bold;
      background: #f5f6f9;
    }
  }

  .range-list {
    flex: 1;
    min-height: 210px;
    background: #fff;
    border: 1px solid #e8e8e8;
    /deep/ .mt-tree-view {
      width: 100%;
    }
  }
  .list-no-data {
    font-size: 14px;
    color: #9a9a9a;
    padding-top: 20px;
    text-align: center;
  }
}
</style>
