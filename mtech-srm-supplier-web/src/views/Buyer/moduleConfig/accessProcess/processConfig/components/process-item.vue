<template>
  <div class="process-item-container">
    <div class="proc-box proc-toolbar">
      <div class="toolbar-left">
        <span class="name words-nowrap"
          >{{ $t('名称') }}：{{ accessTemplate.accessTemplateName }}</span
        >
        <span :class="['type', 'words-nowrap', isCompany ? 'type-company' : 'type-category']">{{
          isCompany ? $t('供应商企业准入') : $t('品类货源准入')
        }}</span>
        <div
          v-for="btn in operatorBtns"
          :key="btn.id"
          :class="['toolbar-btns', btn.disabled && 'is-disabled']"
          @click="handleProcessClick(btn)"
        >
          <i v-if="btn.icon" :class="['mt-icons', btn.icon]"></i>
          <span>{{ btn.title }}</span>
        </div>
      </div>
      <div class="toolbar-right">
        <span class="detail-info">{{ $t('编号') }}：{{ accessTemplate.accessTemplateCode }}</span>
        <span class="detail-info">{{ $t('创建人') }}：{{ accessTemplate.createUserName }}</span>
        <span class="detail-info">{{ $t('创建时间') }}：{{ accessTemplate.createTime }}</span>

        <mt-switch
          v-model="accessTemplate.status"
          :on-label="$t('启用')"
          :off-label="$t('停用')"
          :active-value="1"
          :inactive-value="2"
          @change="handleSwitch"
        ></mt-switch>
      </div>
    </div>
    <!--  -->
    <div class="proc-box proc-cont">
      <div class="proc-item proc-range" @click.stop="openStageDetail">
        <div class="range-title">
          <span>{{ $t('适用范围') }}</span>
          <span v-if="!disabled" class="btn-edit" @click.stop="editRange">{{ $t('编辑') }}</span>
        </div>

        <div v-if="isOpen && rangeList && rangeList.length" @click.stop>
          <range-item
            v-for="range in rangeList"
            :key="range.orgId"
            :range="range"
            :type="type"
            class="range-content"
          />
        </div>
      </div>

      <!-- 可配置的阶段 -->
      <div class="draggable-container">
        <div
          v-for="(stage, stageIndex) in stageList"
          :key="stage.id"
          class="stage-box stage-box-active draggable-item"
          :class="[
            isCompany && 'stage-box-active-company',
            isOpen && isCompany && 'stage-company-open',
            isOpen && !isCompany && 'stage-category-open'
          ]"
          @click.stop="openStageDetail"
        >
          <div
            class="proc-item item-stage"
            :class="[
              isOpen && 'stage-open',
              isCompany && 'stage-company',
              stageIndex + 1 === stageList.length && disabled && 'stage-disabled'
            ]"
          >
            <span class="stage-no">{{ stage.sequenceNo }}</span>
            <span class="stage-name">{{ stage.stageName }}</span>
            <span v-if="!disabled" class="btn-edit" @click.stop="editStage(stage)">{{
              $t('编辑')
            }}</span>
          </div>
          <div
            v-if="isOpen && stage.content && stage.content.length"
            class="stage-content"
            @click.stop
          >
            <stage-item
              v-for="cont in stage.content"
              :key="cont.id"
              :stage="cont"
              :workflow-list="workflowList"
              :is-company="isCompany"
            ></stage-item>
          </div>
        </div>
      </div>

      <div v-if="!disabled" class="proc-item add-stage" @click.stop="addStage">
        <span>+ {{ $t('新增阶段') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import RangeItem from './range-item.vue'
import StageItem from './stage-item.vue'
import Sortable from 'sortablejs'
import MtSwitch from '@mtech-ui/switch'

export default {
  components: {
    RangeItem,
    StageItem,
    MtSwitch
  },
  props: {
    accessList: {
      type: Array,
      default: () => []
    },
    buttons: {
      type: Array,
      default: () => [
        {
          id: 'edit',
          title: this.$t('编辑'),
          icon: 'mt-icon-icon_solid_edit'
        },
        {
          id: 'copy',
          title: this.$t('复制'),
          icon: 'mt-icon-icon_solid_newphase'
        },
        {
          id: 'delete',
          title: this.$t('删除'),
          icon: 'mt-icon-icon_solid_Delete'
        }
      ]
    },
    accessTemplate: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isOpen: false,
      rangeList: [],
      stageListAll: [],
      stageListOld: [] // 如果拖拽了但接口报错，需要还原之前的顺序
    }
  },
  computed: {
    type() {
      return this.accessTemplate.accessTemplateType
    },
    // 状态1，启用，不可编辑；2停用
    disabled() {
      return this.accessTemplate.status == 1
    },
    operatorBtns() {
      return this.disabled
        ? this.buttons.map((item) => {
            let disabled = item.id !== 'copy'
            return {
              ...item,
              disabled
            }
          })
        : this.buttons.map((item) => {
            return {
              ...item,
              disabled: false
            }
          })
    },
    stageList() {
      const { stageTemplationDTOList } = this.accessTemplate
      let list = stageTemplationDTOList || []
      if (this.isOpen && list.length && this.stageListAll.length) {
        list = list.map((item) => {
          let content = this.stageListAll.filter((all) => all.id === item.id)
          return {
            ...item,
            content
          }
        })
      }
      return list
    },
    workflowList() {
      return this.$parent.workflowList
    },
    isCompany() {
      return this.accessTemplate.accessTemplateType == 0
    }
  },
  mounted() {
    // this.draggable();
  },
  methods: {
    openStageDetail() {
      if (this.isOpen === false) {
        this.queryStageAll()
      }
      this.isOpen = !this.isOpen
    },

    handleSwitch(val) {
      const { id } = this.accessTemplate
      let params = {
        status: val,
        accessTemplateId: id
      }
      this.$API.AccessProcess['changeAccessStatus'](params)
        .then((res) => {
          if (res.code == 200) {
            this.$emit('changeStatusSuccess')
          } else {
            this.$toast({ content: res.msg || this.$t('系统异常'), type: 'error' })
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
          this.accessTemplate.status = val == 1 ? 2 : 1
        })
    },

    // 阶段详情
    async queryStageAll() {
      this.$store.commit('startLoading')
      const { id } = this.accessTemplate
      const { code, data } = await this.$API.AccessProcess['queryStageAll'](id)
      this.$store.commit('endLoading')
      if (code == 200 && data) {
        const { companyRelationDTOList, stageTemplateResponseList } = data
        this.rangeList = companyRelationDTOList
        this.stageListAll = stageTemplateResponseList
      }
    },

    handleProcessClick(args) {
      if (args.disabled) return
      const { id } = args
      if (id === 'edit') {
        this.$emit('editAccess')
      }
      if (id === 'copy') {
        this.$emit('copyAccess')
      }
      if (id === 'delete') {
        this.$emit('deleteAccess')
      }
    },

    // 新增阶段
    addStage() {
      const _this = this
      const { accessTemplate, workflowList, isCompany } = this
      this.$dialog({
        modal: () => import('./operatorStageDialog.vue'),
        data: {
          title: this.$t('新增阶段'),
          isCompany,
          accessTemplate,
          workflowList
        },
        success: () => {
          _this.$emit('operatorStageSuccess')
        }
      })
    },
    // 编辑阶段
    editStage(data) {
      const _this = this
      const { accessTemplate, workflowList, isCompany } = this
      this.$dialog({
        modal: () => import('./operatorStageDialog.vue'),
        data: {
          title: this.$t('编辑阶段'),
          isCompany,
          isEdit: true,
          accessTemplate,
          stageInfo: data,
          workflowList
        },
        success: () => {
          _this.$emit('operatorStageSuccess')
        }
      })
    },

    async editRange() {
      await this.queryStageAll()
      const { accessTemplate, isCompany, rangeList } = this
      const _this = this
      if (this.isCompany) {
        this.$dialog({
          modal: () => import('./operatorCompanyRange.vue'),
          data: {
            title: this.$t('适用范围维护'),
            accessTemplate,
            isCompany,
            rangeList
          },
          success: () => {
            _this.$emit('updateRangeSuccess')
          }
        })
      } else {
        this.$dialog({
          modal: () => import('./operatorCategoryRange.vue'),
          data: {
            title: this.$t('适用范围维护'),
            accessTemplate,
            isCompany,
            rangeList
          },
          success: () => {
            _this.$emit('updateRangeSuccess')
          }
        })
      }
    },

    // 阶段拖拽
    moveStage(params) {
      this.$API.AccessProcess['moveStage'](params)
        .then((res) => {
          if (res.code == 200) {
            this.$emit('move-success')
          }
        })
        .catch((err) => {
          this.$emit('move-fail')
          this.$toast({ content: err.msg || this.$t('系统异常'), type: 'error' })
        })
    },

    // 实现拖拽
    draggable() {
      const drggableContainer = document.getElementsByClassName('draggable-container')
      const _this = this
      let len = drggableContainer.length

      // 先保存旧数据
      // this.stageListOld = JSON.parse(JSON.stringify(this.stageList));

      // 因为有多个流程，需要循环，给每个流程加上拖拽
      for (let i = 0; i < len; i++) {
        let items = _this.accessList[i].stageTemplationDTOList
        Sortable.create(drggableContainer[i], {
          group: { name: 'stage', pull: false, put: true },
          draggable: '.draggable-item',
          animation: 150,
          onEnd({ oldIndex, newIndex }) {
            if (oldIndex !== newIndex) {
              let sltRow = items[oldIndex]
              _this.moveStage({
                stageTemplateId: sltRow.id,
                targetSequenceNo: newIndex + 1
              })
            }
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.process-item-container {
  min-width: 1200px;
  // overflow-x: scroll;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}
.proc-box {
  display: flex;
  align-items: center;
  height: 100%;
}
.proc-toolbar {
  height: 54px;
  justify-content: space-between;
  background: #f5f5f5;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 30px;
  white-space: nowrap;
  .toolbar-left {
    // max-width: 450px;
    display: flex;
    align-items: center;
    .name {
      font-size: 14px;
      color: #292929;
      margin-right: 10px;
    }
    .type {
      display: inline-block;
      font-size: 12px;
      font-weight: 600;
      padding: 4px;
      margin-right: 20px;
    }
    .type-category {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
    .type-company {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
    .toolbar-btns {
      margin-right: 20px;
      color: #4d5b6f;
      cursor: pointer;
      > i {
        margin-right: 6px;
      }
      > span {
        font-size: 14px;
      }
    }
    .is-disabled {
      color: #ccc;
    }
  }
  .toolbar-right {
    .detail-info {
      display: inline-block;
      font-size: 14px;
      color: #292929;
      margin-right: 30px;
      &:last-child {
        margin-right: 40px;
      }
    }
  }
}
.proc-cont {
  align-items: flex-start;
  background: #fff;
  // border-top: none;
  border-radius: 0 0 8px 8px;
  padding: 30px;
  overflow-x: scroll;

  .proc-item {
    width: 200px;
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 0 10px;
    margin-right: 20px;
    flex-shrink: 0;
  }
  .btn-edit {
    float: right;
    color: #00469c;
    font-weight: 600;
    cursor: pointer;
  }
  .range-title {
    line-height: 44px;
  }
  .add-stage {
    line-height: 44px;
    text-align: center;
    color: #00469c;
    font-weight: 600;
    cursor: pointer;
  }
  .stage-box {
    width: 200px;
    margin-right: 20px;
    background: #fafafa;
  }
  .stage-box-active {
    border: 1px solid #dcebff;
    border-radius: 4px;
  }
  .stage-box-active-company {
    border: 1px solid #fbebd3;
  }
  .item-stage {
    width: 100%;
    line-height: 44px;
    border: none;
    background: #dcebff;
    position: relative;
    &::after {
      content: ' ';
      width: 20px;
      height: 2px;
      background: #00469c;
      position: absolute;
      top: 20px;
      right: -21px;
    }
    .stage-no {
      display: inline-block;
      padding: 2px 5px;
      border-radius: 50%;
      background: #00469c;
      font-size: 14px;
      color: #fff;
      margin-right: 5px;
      line-height: 1;
    }
    .stage-name {
      font-size: 14px;
      color: #292929;
    }
  }
  .stage-disabled::after {
    display: none;
  }
  .stage-company {
    background: #fbebd3;
  }

  .stage-open {
    border-radius: 4px 4px 0 0;
  }
  .stage-company-open {
    border: 1px solid #fbebd3;
  }
  .stage-category-open {
    border: 1px solid #dcebff;
  }
  .stage-content {
    padding: 0 10px;
    background: #fafafa;
  }
}
.draggable-container {
  display: flex;
  .draggable-item {
    cursor: move;
  }
}
.words-nowrap {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
