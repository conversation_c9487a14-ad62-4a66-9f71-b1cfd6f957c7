<template>
  <div>
    <template v-if="stage.stageRuleTemplateViewDTOList">
      <div
        v-for="(rule, index) in stage.stageRuleTemplateViewDTOList"
        :key="index"
        class="stage-content-item"
      >
        <!-- 特殊规则，多个标题 -->
        <div v-if="rule.taskRuleType == 2" class="custom-rule-box">
          <h3>{{ $t('特殊规则') }}：</h3>
          <p v-if="isCompany" class="custom-rule-content">
            {{ rule.taskRuleExpression }}
          </p>
          <template v-if="!isCompany && rule.taskRuleDetailList">
            <div v-for="taskRuleDetail in rule.taskRuleDetailList" :key="taskRuleDetail.orgId">
              <p class="company-name">{{ taskRuleDetail.orgName }}</p>
              <template v-if="taskRuleDetail.categoryRelationDTOList">
                <tag-item
                  v-for="category in taskRuleDetail.categoryRelationDTOList"
                  :key="category.categoryId"
                  >{{ category.categoryName }}</tag-item
                >
              </template>
            </div>
          </template>
        </div>
        <!-- {{ $t("默认任务") }} -->
        <div class="default-task-box">
          <h3>{{ $t('任务') }}：</h3>
          <ul v-if="rule.stageTaskTemplateDTOList">
            <li v-for="(task, taskIndex) in rule.stageTaskTemplateDTOList" :key="taskIndex">
              <p class="item-form-title">{{ task.taskDefineName }}</p>
              <p class="item-form-content">
                {{ task.taskOwner | getTaskOwner }}-
                <!-- {{ task.taskRoleName}}填写- -->
                {{ task.finishCondition | getFinishCondition }}
              </p>
            </li>
          </ul>

          <h3>{{ $t('晋级前置条件') }}：</h3>
          <p class="upgrade-info">{{ $t('任务完成大于等于') }}{{ rule.upgradeRuleFront }}</p>
          <h3>{{ $t('晋级方式') }}：</h3>
          <p class="upgrade-info">
            {{ rule.upgradeRuleType | getUpgradeRuleType(rule.upgradeWorkflowKey, workflowList) }}
          </p>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import TagItem from './tag-item.vue'
import { upgradeRuleTypeSetting, taskOnwerSetting } from '@/utils/setting.js'

export default {
  components: {
    TagItem
  },
  filters: {
    getUpgradeRuleType(val, flowKey, workflowList) {
      let rs = upgradeRuleTypeSetting[val]
      if (val == 3 && flowKey && workflowList) {
        let flowKeyItem = workflowList.filter((item) => item.completionTypeId === flowKey)
        if (flowKeyItem && flowKeyItem.length) {
          rs += '-' + flowKeyItem[0].completionTypeName
        }
      }
      return rs
    },
    getFinishCondition(val) {
      return upgradeRuleTypeSetting[val]
    },
    getTaskOwner(val) {
      return taskOnwerSetting[val]
    }
  },
  props: {
    stage: {
      type: Object,
      default: () => {}
    },
    workflowList: {
      type: Array,
      default: () => []
    },
    isCompany: {
      type: Boolean,
      default: true
    }
  },
  mounted() {}
}
</script>

<style lang="scss" scoped>
.stage-content-item {
  h3 {
    font-size: 12px;
    color: #292929;
    font-weight: 600;
    margin: 20px 0 10px 0;
  }
  .upgrade-info {
    margin-bottom: 20px;
  }
  .company-name {
    font-size: 12px;
    color: #292929;
    margin-bottom: 5px;
  }
}

.item-form-title {
  font-size: 12px;
  color: #00469c;
  font-weight: 600;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.item-form-content {
  font-size: 12px;
  color: #9a9a9a;
  margin-bottom: 10px;
}
.custom-rule-box {
  border-top: 1px solid #e8e8e8;
  .custom-rule-content {
    margin-bottom: 20px;
  }
}
</style>
