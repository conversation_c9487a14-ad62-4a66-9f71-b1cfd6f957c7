<template>
  <div class="access-process">
    <div class="access-banner fbox">
      <div class="search-box">
        <div class="e-input-group">
          <span class="e-input-group-icon mt-icons mt-icon-icon_search"></span>
          <input
            class="e-input"
            type="text"
            v-model="searchValue"
            :placeholder="$t('请输入公司名称')"
          />
        </div>
      </div>
      <div class="second-box flex1">{{ $t('流程名称') }}</div>
      <div class="process-box">{{ $t('对应阶段') }}</div>
      <!-- <div class="expand-all"><i class="mt-icons mt-icon-MT_FullScreen"></i> 全部展开</div> -->
    </div>
    <div class="access-content">
      <!-- 外层行 带底部横线的 内部包含树的 -->
      <div class="process-line2 fbox" v-for="(item, idx) of processData1" :key="idx">
        <div
          class="left-line"
          :class="{ none: !isCompanyExpanded(item[0].id) || item.length <= 1 }"
        ></div>
        <!-- 内层行 不带底部的横线的 -->

        <div
          class="process-inline fbox"
          :class="{
            'active-expend': !isCompanyExpanded(cItem.id),
            none: !isCompanyExpanded(item[0].id) && cIdx !== 0
          }"
          v-for="(cItem, cIdx) of item"
          :key="cItem.id"
        >
          <!-- 父节点 -->
          <template v-if="cIdx === 0">
            <div
              class="company-box parent-line"
              @click="checkNode(cItem.id, cItem.accessTemplateId)"
            >
              <!-- 第一级父节点 -->
              <div class="arrow-wrap fbox">
                <i class="mt-icons mt-icon-icon_Packup"></i>
                <mt-tooltip
                  :content="cItem.name || '--'"
                  :target="'#arrow-txt-' + cIdx"
                  position="BottomCenter"
                >
                  <div :id="'arrow-txt-' + cIdx" class="arrow-txt" @click="selectCompany(cItem)">
                    {{ cItem.name || '--' }}
                  </div>
                </mt-tooltip>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="company-box children-line">
              <!-- 第二级子节点 -->
              <div class="arrow-wrap fbox">
                <mt-tooltip
                  :content="cItem.name || '--'"
                  :target="'#arrow-txt-' + cIdx"
                  position="BottomCenter"
                >
                  <div :id="'arrow-txt-' + cIdx" class="arrow-txt" @click="selectCompany(cItem)">
                    {{ cItem.name || '--' }}
                  </div>
                </mt-tooltip>
              </div>
            </div>
          </template>

          <div class="line-box flex1">
            <mt-tooltip
              :content="cItem.processName || '--'"
              :target="'#line-box-' + cIdx"
              position="BottomCenter"
            >
              <span :id="'line-box-' + cIdx">{{ cItem.processName || '--' }}</span>
            </mt-tooltip>
          </div>

          <div class="process-wrap" :class="{ 'max-height': isExpanded }">
            <div
              class="process-box fbox"
              :class="{
                'company-bg': cIdx === 0
              }"
            >
              <!-- 阶段按钮 -->
              <div
                class="process-item"
                v-for="(singleItem, singleIndex) of cItem.buyerStageInstanceList"
                :key="singleItem.id"
              >
                <!-- 主box -->
                <div
                  :class="['mian-wrap', cItem.expanded && 'is-expanded']"
                  @click="stageClick(cItem, cIdx, item, idx)"
                >
                  <div class="number-box">{{ singleIndex + 1 }}</div>
                  <div class="process-name">
                    {{ singleItem.stageName }}
                  </div>
                </div>
                <!-- 下拉模块 -->
                <div v-if="cItem.expanded" class="stage-content-dropdown">
                  <stage-item :stage="singleItem"></stage-item>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <mt-page
      :page-settings="pageSettings"
      :total-pages="totalPages"
      @currentChange="goToPage"
      @sizeChange="changePageSize"
    ></mt-page>
  </div>
</template>

<script>
import utils from '@/utils/utils.js'
import StageItem from '../processConfig/components/stage-item.vue'
import MtPage from '@mtech-ui/page'

// let buyerFormTaskInstanceListMap = new Map();

export default {
  components: {
    StageItem,
    MtPage
  },
  watch: {
    searchValue(nv) {
      if (nv) {
        this.searchForCompany(nv)
      } else {
        this.processData1 = JSON.parse(JSON.stringify(this.originProcessData1))
      }
    }
  },
  data() {
    return {
      searchValue: '',
      processData: [],
      originProcessData: [],
      processData1: [],
      originProcessData1: [],
      isExpanded: false,
      expandeArr: {},
      expandCompanyIdArr: [],

      stageRecords: [], // 阶段查询接口返回的records

      pageParams: {
        condition: '',
        page: {
          current: 1,
          size: 10
        }
      },
      pageSettings: {
        pageSize: 10,
        pageCount: 10,
        pageSizes: [10, 20, 50, 100]
      },
      total: 1
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSettings.pageSize)
    }
  },
  created() {
    this.stageQuery()
  },
  methods: {
    isEmpty(value) {
      return utils.isEmpty(value)
    },
    isCompanyExpanded(companyId) {
      return this.expandCompanyIdArr.includes(companyId)
    },
    checkNode(id, accessTemplateId) {
      if (!this.isCompanyExpanded(id)) {
        this.categoryQuery(id, accessTemplateId)
      }
      // 展开的公司id 放入数组 =》 根据数组有值 展开
      if (this.expandCompanyIdArr.includes(id)) {
        let index = this.expandCompanyIdArr.findIndex((v) => v.id === id)
        this.expandCompanyIdArr.splice(index, 1)
      } else {
        this.expandCompanyIdArr.push(id)
      }
    },
    // 搜索筛选
    searchForCompany(value) {
      let reg = new RegExp(value, 'g')
      let tmpProcessData = []
      this.originProcessData1.forEach((v) => {
        if (!!v && !!v.length && reg.test(v[0].name)) {
          tmpProcessData.push(v)
        }
      })
      this.processData1 = JSON.parse(JSON.stringify(tmpProcessData))
    },

    // 获取阶段列表
    stageQuery() {
      this.$loading()
      // 获取对应的数据
      let params = {
        ...this.pageParams,
        defaultRules: [
          // {
          //   condition: "equal",
          //   field: "accessType",
          //   label: this.$t("准入类型"),
          //   operator: "equal",
          //   type: "number",
          //   value: 0,
          // },
          {
            condition: 'notequal',
            field: 'accessType',
            label: this.$t('流程类型'),
            operator: 'notequal',
            type: 'number',
            value: 2
          }
        ]
      }

      this.$API.AccessView.queryAccessRelation(params).then((res) => {
        let { code, data } = res
        if (code === 200 && !utils.isEmpty(data)) {
          // 渲染公司树数据
          // this.renderCompanyTree(data);
          const { records, total } = data
          this.stageRecords = records
          this.total = total
          this.renderCompanyTree1(records)
        } else {
          this.$toast({
            content: this.$t('获取阶段数据失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    // 查询公司的品类列表
    categoryQuery(id, accessTemplateId) {
      this.$loading()
      // 获取对应的数据
      let params = {
        condition: '',
        page: {
          current: 1,
          size: 10
        },
        defaultRules: [
          {
            condition: 'equal',
            field: 'accessTemplateId',
            label: this.$t('准入模板ID'),
            operator: 'equal',
            type: 'string',
            value: accessTemplateId
          },
          {
            condition: 'equal',
            field: 'accessType',
            label: this.$t('准入类型'),
            operator: 'equal',
            type: 'number',
            value: 1
          },
          {
            condition: 'equal',
            field: 'orgId',
            label: this.$t('公司ID'),
            operator: 'equal',
            type: 'string',
            value: id
          }
        ]
      }
      let index = this.stageRecords.findIndex((item) => item.orgId === id)

      this.$API.AccessView.queryAccessRelation(params).then((res) => {
        let { code, data } = res
        if (code === 200 && !utils.isEmpty(data)) {
          // 渲染公司树数据
          const { records } = data
          this.$set(this.stageRecords[index], 'buyerPartnerCategoryList', records)
          this.renderCompanyTree1(this.stageRecords)
        } else {
          this.$toast({
            content: this.$t('获取公司品类数据失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    // 渲染公司树 生成一个列为单位的 数组
    renderCompanyTree1(data) {
      let tmpDataArr = []
      data.forEach((item) => {
        // 公司 =》 品类  没有三级菜单 不在继续递归
        // 一个公司下的 多行数组
        let processLineArr = []

        processLineArr.push({
          id: item.orgId,
          name: item.orgName,
          expanded: false, //展开
          processName: item.accessTemplateName,
          accessTemplateId: item.accessTemplateId,
          buyerStageInstanceList: item.stageTemplationDTOList
        })

        if (
          !utils.isEmpty(item.buyerPartnerCategoryList) &&
          item.buyerPartnerCategoryList.length > 0
        ) {
          // 树数据
          item.buyerPartnerCategoryList.forEach((cItem) => {
            processLineArr.push({
              id: cItem.id,
              name: cItem.categoryName,
              expanded: false,
              ...cItem,
              processName: cItem.accessTemplateName,
              accessTemplateId: item.accessTemplateId,
              buyerStageInstanceList: cItem.stageTemplationDTOList
            })
          })
        }
        tmpDataArr.push(processLineArr)
      })
      this.processData1 = tmpDataArr
      // 备份
      this.originProcessData1 = JSON.parse(JSON.stringify(tmpDataArr))
      this.$hloading()
    },

    // 点击阶段模块
    stageClick(cItem, cIdx, item, idx) {
      // 改掉item的cItem的buyerStageInstanceList
      if (cItem.expanded === true) {
        cItem.expanded = false
      } else {
        this.$loading()
        this.$API.AccessView.getAccessStageDetail(cItem.accessTemplateId).then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            cItem.expanded = true
            this.$set(this.processData1[idx][cIdx], 'buyerStageInstanceList', data)
            this.$hloading()
          } else {
            this.$toast({
              content: this.$t('暂无阶段详情!'),
              type: 'warning'
            })
          }
        })
      }
    },

    nodeOnClick(e) {
      console.log(e)
    },

    // 展开
    nodeExpanding(e) {
      // let { nodeData } = e;
      this.isExpanded = true
      console.log('nodeExpanded:', e)
    },

    // 缩起
    nodeCollapsing(e) {
      // let { nodeData } = e;
      this.isExpanded = false
      console.log('nodeCollapsed:', e)
    },

    // 分页
    goToPage(num) {
      this.$set(this.pageParams.page, 'current', num)
      this.stageQuery()
    },
    changePageSize(size) {
      this.$set(this.pageParams.page, 'size', size)
      this.stageQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: center;
}
.flex1 {
  flex: 1;
}

.none {
  transition: all 0.4s ease-in-out;
  display: none !important;
}

.access-process {
  margin-top: 20px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  overflow-x: auto;

  .access-banner {
    width: 100%;
    height: 54px;
    line-height: 54px;
    background: rgba(245, 245, 245, 1);
    font-size: 14px;
    position: relative;
    .search-box {
      padding-left: 20px;
      width: 400px;
    }

    .second-box {
      // width: 160px;
      padding-left: 20px;
      color: rgba(41, 41, 41, 1);
    }

    .process-wrap {
      width: 500px;
      display: flex;
      flex-direction: column;
    }

    .process-box {
      width: 500px;
      height: 30px;
      padding-left: 20px;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 20px;
      align-items: flex-start;
    }

    .expand-all {
      position: absolute;
      height: 100%;
      right: 40px;
    }
  }

  .company-bg {
    .mian-wrap {
      background: #fbebd3 !important;
    }
  }

  .stage-content-dropdown {
    padding: 10px;
    background: #fafafa;
  }

  .access-content {
    background: #fff;
    border-bottom: 1px solid rgba(232, 232, 232, 1);

    .process-line {
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      align-items: flex-start;
      .company-box {
        width: 400px;
        // border-right: 1px solid rgba(232,232,232,1);
      }

      .line-box {
        padding: 20px 0;
        // width: 160px;
        padding-left: 20px;
        font-size: 14px;

        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-left: 1px solid rgba(232, 232, 232, 1);
        overflow: hidden;
        transition: all 0.6s linear;

        .normal-line {
          min-height: 30px;
          line-height: 30px;
          margin-top: 20px;

          .normal-line-item {
            height: 30px;
            line-height: 30px;
          }
        }

        .normal-line:nth-child(1) {
          margin-top: 0 !important;
        }
      }

      .limit-height {
        height: 70px;
        overflow: hidden;
      }
      .max-height {
        height: auto;
        overflow: hidden;
      }

      .process-wrap {
        width: 500px;
        padding: 20px 0;
        overflow: hidden;
        transition: all 0.6s linear;
      }

      .limit-height1 {
        height: 70px;
        overflow: hidden;
      }
      .max-height {
        height: auto;
      }

      .process-box {
        padding-left: 20px;
        margin-top: 20px;
        align-items: flex-start;

        .process-item {
          margin-right: 10px;
          // height: 30px;
          font-size: 14px;
          position: relative;

          .icon-box {
            font-size: 16px;
            margin-right: 5px;
            height: 30px;
            line-height: 30px;
            display: flex;
            align-items: center;
            i {
              line-height: 20px;
              width: 16px;
              height: 16px;
              color: #6386c1;
              opacity: 0.5;
            }
          }

          .process-name {
            // margin-right: 55px;
            min-width: 120px;
            padding: 0 0 0 5px;
            font-size: 14px;
            color: #292929;
          }
          .number-box {
            width: 16px;
            height: 16px;
            line-height: 16px;
            font-size: 14px;
            text-align: center;
            margin-right: 5px;
            background: rgba(0, 70, 156, 1);
            color: #fff;
            border-radius: 100%;
          }
          .process-dor {
            min-width: 38px;
            text-align: right;
            white-space: nowrap;
            .blue-txt {
              color: #00469c;
            }
          }
        }

        .mian-wrap {
          display: flex;
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          border-radius: 4px;
          justify-content: space-between;
          background: rgba(220, 235, 255, 1);
          align-items: center;
          cursor: pointer;
        }

        .dropDown-wrap {
          // height: 0;
          overflow: hidden;

          padding: 10px;
          background: #fafafa;
          border-left: 1px solid rgba(251, 235, 211, 1);
          border-right: 1px solid rgba(251, 235, 211, 1);
          border-bottom: 1px solid rgba(251, 235, 211, 1);

          .mian-title {
            font-size: 12px;

            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            margin-bottom: 10px;
          }

          .task-name {
            margin-bottom: 5px;
            font-size: 12px;

            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            margin-top: 5px;
          }

          .task-name1 {
            margin-bottom: 5px;
            font-size: 12px;

            font-weight: 500;
            color: #9a9a9a;
            margin-top: 5px;
          }

          .task-status {
            font-size: 10px;

            font-weight: normal;
            color: rgba(154, 154, 154, 1);
            transform: scale(1, 0.9);
          }

          .grey-task {
            color: #9a9a9a;
          }

          .mt-20 {
            margin-top: 20px;
          }
        }

        .expand-item {
          padding: 0 !important;
        }

        .opacity {
          background: rgba(220, 235, 255, 1);
          border-radius: 4px;
          color: #292929;
          .mian-wrap {
            opacity: 0.5;
          }
        }
        .grey {
          color: rgba(154, 154, 154, 1);
          background: rgba(245, 245, 245, 1) !important;
          .number-box {
            background: #9a9a9a;
            color: #fff;
          }
          .mian-wrap {
            background: rgba(245, 245, 245, 1);
          }
        }
      }
      .process-box:nth-child(1) {
        margin-top: 0 !important;
      }
    }

    // ***********
    // 新的样式下的
    // ***********
    // ***********
    // ***********

    .process-line2 {
      padding: 0px 0 20px 20px;
      flex-direction: column;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      align-items: flex-start;
      position: relative;
      transition: all 0.6s linear;

      .left-line {
        display: inline-block;
        width: 1px;
        height: calc(100% - 60px);
        background: #9baac1;
        left: 32px;
        top: 40px;
        position: absolute;
      }

      &::before {
        content: '';
        display: inline-block;
        width: 1px;
        height: 100%;
        background: #e8e8e8;
        position: absolute;
        left: 400px;
        top: 0;
      }

      &::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 100%;
        background: #e8e8e8;
        position: absolute;
        right: 500px;
        top: 0;
      }

      .company-box {
        width: calc(400px - 20px);
        position: relative;
      }

      .children-line {
        &::before {
          content: ' ';
          display: inline-block;
          width: 12px;
          height: 1px;
          background: #9baac1;
          position: absolute;
          left: 12px;
          top: 50%;
        }
      }

      .line-box {
        height: 30px;
        line-height: 30px;
        // width: 160px;
        padding-left: 20px;
        font-size: 14px;

        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        // border-right: 1px solid rgba(232, 232, 232, 1);
        // border-left: 1px solid rgba(232, 232, 232, 1);
        overflow: hidden;
        transition: all 0.6s linear;

        .normal-line {
          min-height: 30px;
          line-height: 30px;
          margin-top: 20px;

          .normal-line-item {
            height: 30px;
            line-height: 30px;
          }
        }

        .normal-line:nth-child(1) {
          margin-top: 0 !important;
        }
      }

      .limit-height {
        height: 70px;
        overflow: hidden;
      }
      .max-height {
        height: auto;
        overflow: hidden;
      }

      .process-wrap {
        width: 500px;
        // height: 30px;
        overflow: hidden;
        transition: all 0.6s linear;
      }

      .limit-height1 {
        height: 70px;
        overflow: hidden;
      }
      .max-height {
        height: auto;
      }

      .process-box {
        padding-left: 20px;
        margin-top: 20px;
        align-items: flex-start;

        .process-item {
          margin-right: 10px;
          // height: 30px;
          font-size: 14px;
          position: relative;

          .icon-box {
            font-size: 16px;
            margin-right: 5px;
            height: 30px;
            line-height: 30px;
            display: flex;
            align-items: center;
            i {
              line-height: 20px;
              width: 16px;
              height: 16px;
              color: #6386c1;
              opacity: 0.5;
            }
          }

          .process-name {
            min-width: 100px;
            padding: 0 0 0 5px;
          }
          .number-box {
            width: 16px;
            height: 16px;
            line-height: 16px;
            font-size: 14px;
            text-align: center;
            margin-right: 5px;
            background: rgba(0, 70, 156, 1);
            color: #fff;
            border-radius: 100%;
          }
          .process-dor {
            min-width: 38px;
            text-align: right;
            white-space: nowrap;
            .blue-txt {
              color: #00469c;
            }
          }
        }

        .mian-wrap {
          display: flex;
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          border-radius: 4px;
          justify-content: space-between;
          background: rgba(220, 235, 255, 1);
          align-items: center;
          cursor: pointer;
        }

        .dropDown-wrap {
          overflow: hidden;

          padding: 10px;
          background: #fafafa;
          border-left: 1px solid rgba(251, 235, 211, 1);
          border-right: 1px solid rgba(251, 235, 211, 1);
          border-bottom: 1px solid rgba(251, 235, 211, 1);

          .mian-title {
            font-size: 12px;

            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            margin-bottom: 10px;
          }

          .task-name {
            margin-bottom: 5px;
            font-size: 12px;

            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            margin-top: 5px;
          }

          .task-name1 {
            margin-bottom: 5px;
            font-size: 12px;

            font-weight: 500;
            color: #9a9a9a;
            margin-top: 5px;
          }

          .task-status {
            font-size: 10px;

            font-weight: normal;
            color: rgba(154, 154, 154, 1);
            transform: scale(1, 0.9);
          }

          .grey-task {
            color: #9a9a9a;
          }

          .mt-20 {
            margin-top: 20px;
          }
        }

        .expand-item {
          padding: 0 !important;
        }

        .opacity {
          background: rgba(220, 235, 255, 1);
          border-radius: 4px;
          color: #292929;
          .mian-wrap {
            opacity: 0.5;
          }
        }
        .grey {
          color: rgba(154, 154, 154, 1);
          background: rgba(245, 245, 245, 1) !important;
          // .number-box {
          //   background: #9a9a9a;
          //   color: #fff;
          // }
          .mian-wrap {
            background: rgba(245, 245, 245, 1);
          }
        }
      }
      .process-box:nth-child(1) {
        margin-top: 0 !important;
      }
      .process-inline {
        width: 100%;
        margin-top: 20px;
        align-items: flex-start;

        .company-box,
        .parent-line {
          cursor: pointer;

          .arrow-wrap {
            height: 30px;
            line-height: 30px;

            i {
              transition: all 0.2s linear;
              display: inline-block;
              width: 24px;
              height: 24px;
              line-height: 24px;
              text-align: center;
              font-size: 12px;
              color: #9baac1;
              transform: rotate(90deg);
            }

            .arrow-txt {
              font-size: 14px;
              color: #292929;
              padding-left: 2px;
            }
          }
        }
        .children-line {
          padding-left: 30px;
        }
      }
      .active-expend {
        .arrow-wrap {
          .mt-icons {
            transform: rotate(0deg) !important;
          }
        }
      }
    }
  }
  .access-content:last-child {
    border-bottom: none;
  }
  .mian-wrap.is-expanded {
    border-radius: 4px 4px 0 0 !important;
  }
}
</style>
<style lang="scss">
.access-process {
  .e-input-group {
    border: none;
  }

  .access-content {
    .process-line {
      .company-box {
        .e-treeview > .e-ul {
          padding: 20px 0 20px 20px;
          margin-top: 0 !important;
          .e-list-item {
            margin-top: 0;
          }
        }

        .e-treeview .e-ul {
          li {
            padding: 0;
          }
        }

        .e-treeview .e-list-text {
          font-size: 14px;
          color: #292929;
        }

        .e-treeview .e-level-2 {
          margin-top: 20px !important;
        }
      }
    }
  }
}
</style>
