<template>
  <div>
    <mt-form ref="propertiesForm" :model="propertiesForm" :rules="rules">
      <div id="rules" class="pass-rule">
        <mt-form-item prop="conditionType">
          <mt-select
            v-model="propertiesForm.conditionType"
            :data-source="conditionTypeList"
            :placeholder="$t('请选择')"
            width="150"
            class="qualified"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="conditionAttribute">
          <mt-select
            v-model="propertiesForm.conditionAttribute"
            :data-source="conditionAttributeList"
            :placeholder="$t('请选择')"
            width="190"
            class="qualified"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="conditionSymbol">
          <mt-select
            v-model="propertiesForm.conditionSymbol"
            :data-source="symbolList"
            :placeholder="$t('请选择')"
            width="150"
            class="qualified"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="conditionTarget">
          <mt-inputNumber
            :show-clear-button="true"
            :show-spin-button="false"
            v-model="propertiesForm.conditionTarget"
            :placeholder="$t('非负整数')"
            width="150"
          ></mt-inputNumber>
        </mt-form-item>
        <i class="mt-icons mt-icon-icon_list_refuse rule-add" @click="deleteRow"></i>
      </div>
    </mt-form>
  </div>
</template>

<script>
import { conditionTypeList, symbolList, conditionAttributeList } from '../config/index'
import { cloneDeep } from 'lodash'
export default {
  name: 'ModelProps',
  props: {
    params: {
      type: Object,
      default: () => {},
      requerid: true
    },
    index: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      conditionAttributeList: conditionAttributeList,
      symbolList: symbolList,
      conditionTypeList: conditionTypeList,
      propertiesForm: {},
      rules: {
        conditionType: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        conditionAttribute: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        conditionSymbol: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        conditionTarget: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      }
    }
  },
  watch: {
    propertiesForm: {
      handler: function (newValue) {
        cloneDeep(this.propertiesForm, newValue)
        this.$emit('changeValue', this.index, this.propertiesForm)
      },
      deep: true
    }
  },
  mounted() {
    this.propertiesForm = { ...this.params }
  },
  methods: {
    sendForm(fn) {
      fn(this.propertiesForm)
    },
    deleteRow() {
      this.$emit('deleteIndex', this.index, this.params)
    }
  }
}
</script>

<style></style>
