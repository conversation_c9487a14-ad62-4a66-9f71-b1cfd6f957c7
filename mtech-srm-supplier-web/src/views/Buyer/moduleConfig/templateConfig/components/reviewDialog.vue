// 评审包
<template>
  <mt-dialog ref="reviewDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('评审包名称')"
          label-style="top"
          prop="packageName"
        >
          <mt-input
            v-model="formInfo.packageName"
            :placeholder="$t('请输入评审包名称')"
            float-label-type="Never"
            width="414"
            :maxlength="200"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('模板类型')"
          label-style="top"
          prop="templateType"
        >
          <mt-DropDownTree
            ref="dropDownTree"
            v-if="!!fields.dataSource && fields.dataSource.length > 0"
            :show-check-box="true"
            :placeholder="$t('请选择')"
            v-model="formInfo.templateType"
            :fields="fields"
            id="multiTreeSelect"
            :allow-multi-selection="true"
            :auto-check="true"
            @change="templateTypesChange"
            width="414"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('通过条件')"
          prop="fullScore"
          label-style="top"
        ></mt-form-item>
        <mt-form
          class="task-item-select first-rule-width"
          ref="ruleRef"
          :model="firstPassRules"
          :rules="firstRules"
        >
          <div id="rules" class="pass-rule">
            <mt-form-item prop="conditionType">
              <mt-input
                :placeholder="$t('当')"
                width="130"
                class="qualified"
                :disabled="true"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateType">
              <mt-select
                v-model="firstPassRules.templateType"
                :data-source="templateTypelist"
                :placeholder="$t('请选择')"
                width="150"
                class="qualified"
                :fields="{ text: 'itemName', value: 'itemCode' }"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="conditionAttribute">
              <mt-select
                v-model="firstPassRules.conditionAttribute"
                :data-source="conditionAttributeList"
                :placeholder="$t('请选择')"
                width="150"
                class="qualified"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="conditionSymbol">
              <mt-select
                v-model="firstPassRules.conditionSymbol"
                :data-source="symbolList"
                :placeholder="$t('请选择')"
                width="130"
                class="qualified"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="conditionTarget">
              <mt-input
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="firstPassRules.conditionTarget"
                width="130"
                :max-length="4"
                :max="1000"
                type="number"
                oninput="if(value>1000){value=1000}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
                height="40"
                :placeholder="$t('非负整数')"
              ></mt-input>
            </mt-form-item>
          </div>
        </mt-form>
        <review-form-item
          v-for="(p, index) in form.properties"
          :key="index"
          :ref="`properties_${index}`"
          :index="index"
          :params="p"
          :template-types="templateTypelist"
          @changeValue="changeValue"
          @deleteIndex="deleteRow"
          class="full-width"
        ></review-form-item>
        <i class="mt-icons mt-icon-icon_card_plus rule-add-button" @click="addRule">{{
          $t(' 添加条件')
        }}</i>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import reviewFormItem from './reviewFormItem.vue'
import { conditionTypeList, symbolList, conditionAttributeList } from '../config/index'
export default {
  components: { reviewFormItem },
  data() {
    return {
      templateTypelist: [],
      conditionAttributeList: conditionAttributeList,
      symbolList: symbolList,
      conditionTypeList: conditionTypeList,
      form: {
        properties: []
      },
      firstPassRules: {
        conditionAttribute: null,
        conditionType: 0
      },
      formInfo: {
        status: 2,
        reviewPackagePassRules: []
      },
      fields: {
        dataSource: [],
        value: 'id',
        parentValue: 'parentId',
        text: 'itemName',
        hasChildren: 'hasChild'
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      rules: {
        packageName: [
          {
            required: true,
            message: this.$t('请输入评审包名称'),
            trigger: 'blur'
          }
        ],
        templateType: [
          {
            required: true,
            message: this.$t('请选择模板类型'),
            trigger: 'blur'
          }
        ]
      },
      firstRules: {
        templateType: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        conditionAttribute: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        conditionSymbol: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        conditionTarget: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    show() {
      this.$refs['reviewDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['reviewDialog'].ejsRef.hide()
    },
    // 获取页面初始数据
    async initData() {
      await this.getTypeList()
      if (this.isEdit) {
        this.formInfo = {
          ...this.info,
          templateType: this.info.templateTypes.split(',')
        }
        this.firstPassRules = {
          ...this.info.reviewPackagePassRules[0]
        }
        if (this.info.reviewPackagePassRules.length > 1) {
          this.form.properties = this.info.reviewPackagePassRules.slice(1)
        }
      }

      this.show()
    },
    async getTypeList() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'reviewType'
      }).then((res) => {
        if (res.code == 200) {
          let result = res.data
          let _max = Math.max.apply(
            null,
            result.map((e) => e.treeLevel)
          )

          for (let i = _max; i > 0; i--) {
            result
              .filter((e) => e.treeLevel === i)
              .forEach((item) => {
                result.find((x) => x.id == item.parentId).hasChild = true
              })
          }
          result
            .filter((e) => e.treeLevel === 0)
            .forEach((item) => {
              delete item.parentId
            })
          this.fields.dataSource = result.filter(
            (e) =>
              Object.prototype.hasOwnProperty.call(e, 'hasChild') ||
              Object.prototype.hasOwnProperty.call(e, 'parentId')
          )
          // const templateCode = `${this.fields.dataSource.find((x) => x.id == e.parentId).itemCode}-${
          //   e.itemCode
          // }`
          const parentTar = {}
          this.fields.dataSource.forEach((item) => {
            if (item.parentId === undefined || item.parentId === null) {
              parentTar[item.id] = item.itemCode
            }
          })
          this.fields.dataSource = this.fields.dataSource.map((item) => {
            if (item.parentId !== undefined && item.parentId !== null) {
              item.itemCode = parentTar[item.parentId] + '-' + item.itemCode
            }
            return item
          })
        }
      })
    },
    // 追加条件
    addRule() {
      let len = this.form.properties.length
      this.$refs.ruleRef.validate((valid) => {
        if (valid && len > 0) {
          for (let i = 0; i < len; i++) {
            this.$refs['properties_' + i][0].$refs.propertiesForm.validate((valid) => {
              if (valid && i == len - 1) {
                this.form.properties.push({})
              }
            })
          }
        } else if (valid && len == 0) {
          this.form.properties.push({})
        }
      })
    },
    // 条件变更
    changeValue(e, data) {
      console.log('change', e, data)
      this.form.properties[e] = data
    },
    // 删除条件
    deleteRow(e) {
      console.log('this.form.properties', e, this.form.properties[e])
      this.form.properties.splice(e, 1)
    },
    // 模板类型变更
    templateTypesChange(e) {
      let _temp = this.fields.dataSource.filter(
        (x) => Object.prototype.hasOwnProperty.call(x, 'parentId') && e.value.indexOf(x.id) > -1
      )
      this.templateTypelist = _temp
      console.log('this.fields.dataSourcethis.fields.dataSource', this.fields.dataSource, e)
      this.$set(this.templateTypelist, _temp)
      this.formInfo.templateTypes = _temp.map((item) => item.itemCode).toString()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$refs.ruleRef.validate((valid) => {
            if (valid) {
              let len = this.form.properties.length
              this.formInfo.reviewPackagePassRules.length = 0
              const methodName = this.isEdit ? 'updateReviewPackage' : 'addReviewPackage'
              if (len > 0) {
                for (let i = 0; i < len; i++) {
                  this.$refs['properties_' + i][0].$refs.propertiesForm.validate((valid) => {
                    if (valid && i == len - 1) {
                      this.formInfo.reviewPackagePassRules.push(this.firstPassRules)
                      this.formInfo.reviewPackagePassRules =
                        this.formInfo.reviewPackagePassRules.concat(this.form.properties)
                      this.saveData(methodName)
                    }
                  })
                }
              } else if (len == 0) {
                this.formInfo.reviewPackagePassRules.push(this.firstPassRules)
                this.saveData(methodName)
              }
            }
          })
        }
      })
    },
    saveData(methodName) {
      this.$API.ModuleConfig[methodName](this.formInfo)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$emit('confirm-function')
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
/deep/ .full-width {
  width: 100%;
}
/deep/ .first-rule-width {
  width: 818px;
}
/deep/ .pass-rule {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .is-required {
    display: none;
  }
}
@mixin rule-icon {
  line-height: 35px;
  font-size: 14px;
  font-weight: normal;
  color: rgba(99, 134, 193, 1);
}
/deep/ .rule-add {
  @include rule-icon;
  &-button {
    @include rule-icon;
    width: 100px;
  }
}
/deep/ .relative-select {
  width: 280px;
  float: right;
  margin-left: 10px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
</style>
