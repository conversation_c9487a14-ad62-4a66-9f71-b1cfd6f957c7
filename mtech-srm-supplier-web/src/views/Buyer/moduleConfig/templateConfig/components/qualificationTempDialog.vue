<template>
  <!--资质模板弹出框-->
  <mt-dialog ref="dialog" :buttons="buttons" :header="header">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('名称')"
          label-style="top"
          prop="qualificationTemplateName"
        >
          <mt-input
            v-model="formInfo.qualificationTemplateName"
            :placeholder="$t('请输入名称')"
            float-label-type="Never"
            width="414"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          class="form-item"
          :label="$t('类型')"
          label-style="top"
          prop="qualificationTemplateTypeCode"
        >
          <mt-select
            class="f-1"
            v-model="formInfo.qualificationTemplateTypeCode"
            :show-clear-button="true"
            :data-source="typeList"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :placeholder="$t('请选择类型')"
            width="414"
            @change="itemNameChange"
          ></mt-select>
        </mt-form-item>
        <div class="flex_f">
          <mt-form-item
            class="form-item"
            :label="$t('适用品类')"
            label-style="top"
            prop="suitCateg"
          >
            <div class="relative-select">
              <mt-select
                v-model="formInfo.suitCateg"
                :data-source="categoryTypeList"
                width="414"
                :placeholder="$t('请选择')"
                @change="categoryFlagChange"
              ></mt-select>
            </div>
          </mt-form-item>
          <!-- :style="{visibility: categoryFlag == 1? 'visible': 'hidden'}" -->
          <mt-form-item
            v-if="categoryFlag === '1'"
            class="form-item"
            :label="$t('选择品类')"
            prop="categoryCode"
            label-style="top"
          >
            <mt-multi-select
              ref="categoryRef"
              v-model="formInfo.categoryCode"
              :allow-filtering="true"
              :filtering="filteringResource"
              :data-source="categoryList"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :fields="{ text: 'categoryName', value: 'categoryCode' }"
              width="414"
              @change="categoryChange"
            ></mt-multi-select>
          </mt-form-item>
        </div>
        <div class="flex_f">
          <mt-form-item class="form-item" :label="$t('适用组织')" label-style="top">
            <div class="relative-select">
              <mt-select
                v-model="orgFlag"
                :data-source="orgTypeList"
                width="414"
                :placeholder="$t('请选择')"
                @change="orgFlagChange"
              ></mt-select>
            </div>
          </mt-form-item>
          <!-- :style="{visibility:
              !!fields.dataSource &&
              fields.dataSource.length > 0 &&
              orgFlag == 1?'visible':'hidden'}
            " -->
          <mt-form-item
            v-if="orgFlag == 1"
            class="form-item"
            :label="$t('选择组织')"
            prop="organizationCode"
            label-style="top"
          >
            <mt-DropDownTree
              :placeholder="$t('请选择')"
              v-model="formInfo.organizationCode"
              :fields="fields"
              :show-check-box="true"
              :allow-multi-selection="true"
              id="multiTreeSelect"
              style="width: 414px"
              :auto-check="true"
              @input="selectCompany"
            ></mt-DropDownTree>
          </mt-form-item>
        </div>
        <mt-form-item class="form-item" :label="$t('适用场景')" prop="sceneCode" label-style="top">
          <div class="relative-select">
            <!-- <mt-select
              v-model="sceneFlag"
              :data-source="sceneTypeList"
              width="300"
              :placeholder="$t('请选择')"
              @change="sceneFlagChange"
            ></mt-select> -->
            <mt-multi-select
              ref="multiSelectRef"
              v-model="formInfo.sceneCode"
              :show-clear-button="true"
              :data-source="dataSceneArr"
              :fields="{ text: 'sceneName', value: 'sceneCode' }"
              :placeholder="$t('请选择适用场景')"
              @change="sceneCodeChange"
              @open="sceneCodeOpen"
              width="414"
            ></mt-multi-select>
          </div>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { categoryTypeList, orgTypeList, sceneTypeList } from '../config/index'
import { cloneDeep } from 'lodash'
import utils from '@/utils/utils'
export default {
  data() {
    return {
      // 品类
      categoryTypeList: categoryTypeList,
      categoryFlag: '0',
      categoryList: [],
      categoryListCopy: [],
      categoryclass: '',
      categoryParams: [], // 品类参数ID
      // 组织
      orgFlag: '0',
      organization: [], // 组织参数ID
      orgTypeList: orgTypeList,
      fields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      // 场景
      // sceneFlag: "0",
      sceneTypeList: sceneTypeList,
      dataSceneArr: [],
      // showScene: {
      //   categoryFlagCon: false,
      //   orgFlagCon: false,
      // },
      formInfo: {
        qualificationTemplateName: '', // 名称
        qualificationTemplateTypeCode: '', // 类型
        qualificationTemplateTypeName: '',
        categoryInfoList: [], //类目编码
        sceneInfoList: [],
        organizationInfoList: [], //组织编码
        suitCateg: [], // 适用品类(一级类目)
        organizationCode: [], // 组织编码
        sceneCode: [] // 适用场景
      },
      typeList: [],

      //弹出框==必选参数
      rules: {
        qualificationTemplateName: [
          { required: true, message: this.$t('请输入名称'), trigger: 'blur' },
          { whitespace: true, message: this.$t('请输入名称'), trigger: 'blur' }
        ],
        qualificationTemplateTypeCode: [
          { required: true, message: this.$t('请选择类型'), trigger: 'blur' }
        ],
        suitCateg: [
          {
            required: true,
            message: this.$t('请选择适用品类'),
            trigger: 'blur'
          }
        ],
        organizationCode: [
          {
            required: true,
            message: this.$t('请选择适用组织'),
            trigger: 'blur'
          }
        ],
        sceneCode: [
          {
            required: true,
            message: this.$t('请选择适用场景'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmEnter,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      flag: {
        scene: true,
        org: true,
        category: true
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    disabled() {
      return this.modalData.disabled
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.filteringResource = utils.debounce(this.filteringResource, 300)
    this.show()
    this.init()
  },
  methods: {
    //模糊搜索==品类
    async filteringResource(e) {
      await this.getCategorys(e.text)
      e.updateData(this.categoryListCopy)
      this.$forceUpdate()
    },
    async init() {
      this.$API.ModuleConfig.qualificationsTempType().then((res) => {
        if (res.code == 200) {
          this.typeList = res.data
        }
      })
      await this.getCategorys('')
      this.getStatedLimitTree(() => {
        this.isEdit &&
          this.initData(() => {
            this.applicablesceneInterface(this.categoryParams, this.organization)
          })
      })
    },
    async getCategorys(val) {
      await this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'statusId',
              type: 'int',
              operator: 'equal',
              value: 1
            },
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          this.categoryListCopy = res.data.records
          this.categoryListCopy.forEach((item) => {
            let bol = this.categoryList.some((e) => {
              return item.categoryCode == e.categoryCode
            })
            item.categoryName = item.categoryCode + '-' + item.categoryName
            if (!bol) {
              this.categoryList.push(item)
            }
          })
        })
    },
    // 获取组织树
    async getStatedLimitTree(callback) {
      let query = 'ORG02'
      await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        orgType: 'ORG001PRO'
      }).then((res) => {
        if (res.code == 200 && res.data) {
          let filterObj = function (item) {
            if (item.orgLeveLTypeCode == query) return true
            if (Object.prototype.hasOwnProperty.call(item, 'children')) {
              item.children = item.children.filter(function (child) {
                if (child.orgLeveLTypeCode == query) {
                  return child.orgLeveLTypeCode == query
                } else if (Object.prototype.hasOwnProperty.call(child, 'children')) {
                  return filterObj(child)
                }
              })
              if (item.children.length > 0) {
                return true
              }
            } else {
              return item.orgLeveLTypeCode == query
            }
          }
          let filter = res.data.filter(function (item) {
            return filterObj(item)
          })

          this.fields.dataSource = cloneDeep(filter)

          if (typeof callback == 'function') {
            callback()
          }
        }
      })
    },
    initData(callback) {
      if (this.info && Object.keys(this.info).length) {
        const {
          qualificationTemplateCode,
          qualificationTemplateName,
          qualificationTemplateTypeCode,
          qualificationTemplateTypeName,
          categoryInfoList,
          sceneInfoList,
          organizationInfoList,
          suitCateg
        } = this.info
        console.log('suitCategsuitCategsuitCateg', this.info)
        if (categoryInfoList.length > 0) {
          this.flag.category = false
          this.categoryFlag = '1'
        }
        if (sceneInfoList.length > 0) {
          this.flag.org = false
          this.sceneFlag = '1'
        }
        if (organizationInfoList.length > 0) {
          this.flag.scene = false
          this.orgFlag = '1'
        }
        this.formInfo = {
          suitCateg,
          qualificationTemplateCode,
          qualificationTemplateName,
          qualificationTemplateTypeCode,
          qualificationTemplateTypeName,
          categoryInfoList,
          sceneInfoList,
          organizationInfoList,
          categoryCode: categoryInfoList.map((item) => item.categoryCode),
          organizationCode: organizationInfoList.map((item) => item.organizationId),
          // sceneCode
          sceneCode: sceneInfoList.map((item) => item.sceneCode)
        }

        //编辑的时候给适用场景传值
        if (this.isEdit && this.formInfo.categoryCode) {
          this.categoryParams = this.formInfo.categoryInfoList.map((cate) => {
            return cate.categoryId
          })
        }
        if (this.isEdit && this.formInfo.categoryCode) {
          this.organization = this.formInfo.organizationCode
        }
        // this.$refs.multiSelectRef.ejsRef
        // this.formInfo["sceneCode"] = sceneInfoList.map((item) => item.sceneCode),
      }
      if (typeof callback == 'function') {
        callback()
      }
    },
    //===弹出框=显示===
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    //===弹出框=取消按钮===
    cancel() {
      this.$refs['dialog'].ejsRef.hide()
    },
    //===弹出框=保存并进入按钮===
    confirmEnter() {
      this.confirm(1)
    },
    //===弹出框=保存按钮===
    confirm(val) {
      this.$refs.formInfo.validate((bol) => {
        if (bol) {
          let params = {
            suitCateg: this.formInfo.suitCateg,
            qualificationTemplateName: this.formInfo.qualificationTemplateName,
            qualificationTemplateTypeCode: this.formInfo.qualificationTemplateTypeCode,
            qualificationTemplateTypeName: this.formInfo.qualificationTemplateTypeName,
            sceneInfoList: this.formInfo.sceneInfoList,
            categoryInfoList: this.formInfo.categoryInfoList,
            organizationInfoList: this.formInfo.organizationInfoList
          }

          if (this.formInfo.qualificationTemplateCode) {
            params.qualificationTemplateCode = this.formInfo.qualificationTemplateCode
            this.$API.ModuleConfig.qualificationTemplatemodify(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('编辑成功'),
                  type: 'success'
                })
                if (val == 1) {
                  this.$emit('confirm-function', this.formInfo.qualificationTemplateCode)
                } else {
                  this.$emit('confirm-function')
                }
                this.$refs['dialog'].ejsRef.hide()
              }
            })
          } else {
            this.$API.ModuleConfig.qualificationTemplateAdd(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('新增成功'),
                  type: 'success'
                })
                if (val == 1) {
                  this.$emit('confirm-function', res.data)
                } else {
                  this.$emit('confirm-function')
                }
                this.$refs['dialog'].ejsRef.hide()
              }
            })
          }
        }
      })
    },
    //使用场景==接口
    applicablesceneInterface(categoryParams, organization, callback) {
      if (categoryParams && organization) {
        // this.$refs.multiSelectRef.ejsRef.showPopup();
        let params = {
          categoryId: categoryParams || [],
          orgId: organization || []
          // tenantId: 0
        }
        this.$API.ModuleConfig.applicablescene(params)
          .then((res) => {
            if (res.code == 200) {
              this.dataSceneArr = res.data
            }
            if (typeof callback == 'function') {
              callback()
            }
          })
          .catch(() => {
            if (typeof callback == 'function') {
              callback()
            }
          })
      }
    },
    // 选择类型
    itemNameChange(e) {
      if (e.itemData) {
        this.formInfo.qualificationTemplateTypeName = e.itemData.itemName
      }
    },
    // 选择品类
    categoryChange(e) {
      let val = []
      if (e.value)
        e.value.forEach((i) => {
          let param = this.$refs.categoryRef.ejsRef.getDataByValue(i)
          val.push(param.id)
        })
      this.categoryParams = val
      this.formInfo.categoryInfoList = []
      this.categoryList.forEach((item) => {
        if (val.indexOf(item.id) != -1) {
          this.formInfo.categoryInfoList.push({
            categoryCode: item.categoryCode,
            categoryId: item.id,
            categoryName: item.categoryName
          })
        }
      })
      this.categoryclass = e.value
    },
    // 适用组织
    selectCompany(e) {
      this.organization = e
      // let showP =
      //   !!this.fields.dataSource &&
      //   this.fields.dataSource.length > 0 &&
      //   this.orgFlag == 1;
      // this.showScene.orgFlagCon = showP ? true : false;
      this.formInfo.organizationInfoList = []
      this.fn(this.fields.dataSource, e)
    },
    // 选择场景==打开时
    sceneCodeOpen() {
      this.applicablesceneInterface(this.categoryParams, this.organization)
    },
    // 选择场景==change改变时
    sceneCodeChange(e) {
      this.formInfo.sceneInfoList = []
      this.dataSceneArr.forEach((item) => {
        if (e.value.indexOf(item.sceneCode) != -1) {
          this.formInfo.sceneInfoList.push({
            sceneCode: item.sceneCode,
            sceneId: item.id,
            sceneName: item.sceneName
          })
        }
      })
    },
    fn(data, arr) {
      data.forEach((ele) => {
        if (arr.indexOf(ele.id) != -1 && ele.orgLeveLTypeCode == 'ORG02') {
          this.formInfo.organizationInfoList.push({
            organizationCode: ele.orgCode,
            organizationId: ele.id,
            organizationName: ele.name
          })
        }
        if (ele.children && ele.children.length > 0) {
          this.fn(ele.children, arr)
        }
      })
    },
    // 是否限品类
    categoryFlagChange(e) {
      if (e.value === '-99' || e.value === '4' || e.value === '3') {
        this.formInfo.categoryInfoList = []
      }
      if (this.flag.category) {
        console.log('是否限品类是否限品类', e)
        this.categoryFlag = e.value
        this.formInfo.categoryCode = []
        this.formInfo.categoryInfoList = []
        this.dataSceneArr = [] //适用场景
        this.formInfo.sceneCode = []
        this.categoryParams = []
      } else {
        console.log('是否限品类是否限品类123', e)
        this.categoryFlag = e.value
        this.flag.category = true
      }
    },
    // 是否限组织
    orgFlagChange(e) {
      if (this.flag.org) {
        this.orgFlag = e.value
        this.formInfo.organizationCode = []
        this.formInfo.organizationInfoList = []
        this.dataSceneArr = [] //组织数据
        this.formInfo.sceneCode = []
        this.organization = []
      } else {
        this.flag.org = true
      }
    }
    // 是否限场景
    // sceneFlagChange(e) {
    //   if (this.flag.scene) {
    //     // this.sceneFlag = e.value;
    //     this.formInfo.sceneCode = [];
    //     this.formInfo.sceneInfoList = [];
    //   } else {
    //     this.flag.scene = true;
    //   }
    // },
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}

//当前页==通用样式
.mgn-left-10 {
  margin-left: 10px;
}
.flex {
  display: flex;
}
.f-1 {
  flex: 1;
}

.flex_f {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
//-----多选框调整-----
//默认文字
/deep/ .e-multi-select-wrapper .e-searcher {
  width: 100%;
}
/deep/ .e-multi-select-wrapper {
  width: 100%;
}
/deep/ .relative-select {
  display: flex;
  justify-content: space-between;
  .mt-drop-down-tree {
    width: 300px;
  }
}

/deep/ .mt-drop-down-tree .e-chips-wrapper {
  // width: 0% !important;
  position: absolute !important;
}
</style>
