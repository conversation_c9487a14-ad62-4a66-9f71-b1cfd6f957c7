// 门槛模板
<template>
  <mt-dialog ref="thresholdDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('门槛模板名称')"
          label-style="top"
          prop="templateName"
        >
          <mt-input
            v-model="formInfo.templateName"
            :placeholder="$t('请输入门槛模板名称')"
            float-label-type="Never"
            width="414"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('类型')" label-style="top" prop="templateType">
          <mt-select
            v-model="formInfo.templateType"
            :data-source="thresholdTypeList"
            :placeholder="$t('请选择类型')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            width="414"
          ></mt-select>
        </mt-form-item>
        <div class="form-item-spec">
          <mt-form-item class="form-item" :label="$t('适用组织')" label-style="top" prop="orgFlag">
            <mt-select
              v-model="orgFlag"
              :data-source="orgTypeList"
              width="414"
              :placeholder="$t('请选择')"
              @change="orgFlagChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('请选择组织')"
            label-style="top"
            prop="orgIds"
            v-if="!!fields.dataSource && fields.dataSource.length > 0 && orgFlag == 1"
          >
            <mt-DropDownTree
              :show-check-box="true"
              :placeholder="$t('请选择')"
              v-model="formInfo.orgIds"
              :fields="fields"
              id="multiTreeSelect"
              :allow-multi-selection="true"
              :auto-check="true"
              width="414"
            ></mt-DropDownTree>
          </mt-form-item>
        </div>
        <div class="form-item-spec">
          <mt-form-item
            class="form-item"
            :label="$t('适用品类')"
            label-style="top"
            prop="categoryFlag"
          >
            <mt-select
              v-model="categoryFlag"
              :data-source="categoryTypeList"
              width="414"
              :placeholder="$t('请选择')"
              @change="categoryFlagChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('请选择品类')"
            label-style="top"
            prop="categoryList"
            v-if="categoryFlag == 1"
          >
            <mt-multi-select
              ref="categoryRef"
              :allow-filtering="true"
              :filtering="filteringResource"
              v-model="formInfo.categoryList"
              :data-source="categoryList"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :fields="{ text: 'categoryName', value: 'id' }"
              width="414"
            ></mt-multi-select>
          </mt-form-item>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
import { categoryTypeList, orgTypeList } from '../config/index'
import utils from '@/utils/utils'
export default {
  data() {
    return {
      categoryFlag: '0',
      orgFlag: '0',
      cateEditTimes: 0,
      orgEditTimes: 0,
      isShow: true,
      isCateShow: true,
      categoryTypeList: categoryTypeList,
      orgTypeList: orgTypeList,
      thresholdTypeList: [],
      fields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      categoryList: [],
      categoryListCopy: [],
      formInfo: {
        orgIds: ['-99'],
        categoryList: ['-99'],
        version: 0
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmAndEnter,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      rules: {
        templateName: [
          {
            required: true,
            message: this.$t('请输入模板名称'),
            trigger: 'blur'
          }
        ],
        templateType: [
          {
            required: true,
            message: this.$t('请选择模板类型'),
            trigger: 'blur'
          }
        ],
        orgIds: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryList: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.show()
    this.filteringResource = utils.debounce(this.filteringResource, 300)
  },
  methods: {
    async filteringResource(e) {
      await this.getCategoryList(e.text)
      e.updateData(this.categoryListCopy)
      this.$forceUpdate()
    },
    // 获取页面初始数据
    initData() {
      if (this.isEdit) {
        this.categoryFlag = this.info.categoryIds == '-99' ? '0' : '1'
        this.orgFlag = this.info.orgIds == '-99' ? '0' : '1'
        this.$nextTick(() => {
          this.formInfo = {
            ...this.info,
            categoryList: this.info.categoryIds.split(','),
            orgIds: this.info.orgIds.split(',')
          }
        })
      }
      this.getThresholdTypeList()
      this.getCategoryList('')
      this.getStatedLimitTree()
    },
    show() {
      this.$refs['thresholdDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdDialog'].ejsRef.hide()
    },
    // 获取门槛模板类型列表
    getThresholdTypeList() {
      this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdTempType'
      }).then((res) => {
        if (res.code == 200) {
          this.thresholdTypeList = res.data
        }
      })
    },
    // 获取品类列表
    async getCategoryList(val) {
      await this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'statusId',
              type: 'int',
              operator: 'equal',
              value: 1
            },
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          this.categoryListCopy = res.data.records
          this.categoryListCopy.forEach((item) => {
            let bol = this.categoryList.some((e) => {
              return item.categoryCode == e.categoryCode
            })
            item.categoryName = item.categoryCode + '-' + item.categoryName
            if (!bol) {
              this.categoryList.push(item)
            }
          })
        })
    },
    // 获取组织树
    getStatedLimitTree() {
      let query = 'ORG02'

      this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        // orgType: "ORG001ADM",
        orgType: 'ORG001PRO'
      }).then((res) => {
        let filterObj = function (item) {
          if (item.orgLeveLTypeCode == query) return true

          if (Object.prototype.hasOwnProperty.call(item, 'children')) {
            item.children = item.children.filter(function (child) {
              if (child.orgLeveLTypeCode == query) {
                return child.orgLeveLTypeCode == query
              } else if (Object.prototype.hasOwnProperty.call(child, 'children')) {
                return filterObj(child)
              }
            })
            if (item.children.length > 0) {
              return true
            }
          } else {
            return item.orgLeveLTypeCode == query
          }
        }
        let filter = res.data.filter(function (item) {
          return filterObj(item)
        })
        // filter.unshift({ name: this.$t("通用"), id: -99 });
        this.fields.dataSource = cloneDeep(filter)
      })
    },
    // 是否限品类
    categoryFlagChange(e) {
      // if (this.cateEditTimes == 0 && this.isEdit) {
      //   ++this.cateEditTimes;
      //   return;
      // }
      this.categoryFlag = e.value
      if (e.value === '1') {
        this.formInfo.categoryList.length = 0
      } else {
        this.formInfo.categoryList.length = 0
        this.formInfo.categoryList.push('-99')
      }
    },
    // 是否限组织
    orgFlagChange(e) {
      if (this.orgEditTimes == 0 && this.isEdit) {
        ++this.orgEditTimes
        return
      }
      this.orgFlag = e.value
      if (e.value === '1') {
        this.formInfo.orgIds.length = 0
      } else {
        this.formInfo.orgIds.length = 0
        this.formInfo.orgIds.push('-99')
      }
    },
    confirm(type = 'confirm') {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'updateThresholdTemp' : 'addThresholdTemp'
          let flatData = this.flatTreeData(this.fields.dataSource)
            .filter((e) => e.orgLeveLTypeCode === 'ORG02')
            .map((x) => x.id)
          let _tempInfo = {
            ...this.formInfo,
            categoryIds: this.formInfo.categoryList.toString(),
            orgIds:
              this.orgFlag == '0'
                ? '-99'
                : flatData.filter((v) => this.formInfo.orgIds.includes(v)).toString()
          }

          this.$API.ModuleConfig[methodName](_tempInfo)
            .then((res) => {
              const { code, data } = res
              if (code == 200 && data) {
                if (type === 'enter') {
                  if (this.formInfo.id || data) {
                    this.$emit('confirm-function', this.formInfo.id || data)
                  }
                } else {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$emit('confirm-function')
                }
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'children') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    confirmAndEnter() {
      this.confirm('enter')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
/deep/ .form-item-spec {
  width: 864px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .relative-select {
  display: flex;
  justify-content: space-between;
  .mt-drop-down-tree {
    width: 300px;
  }
}
</style>
