// 评审模板
<template>
  <!-- 新增评审模板添加 -->
  <mt-dialog ref="reviewTempDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('评审模板名称')"
          label-style="top"
          prop="templateName"
        >
          <mt-input
            v-model="formInfo.templateName"
            :placeholder="$t('请输入评审模板名称')"
            float-label-type="Never"
            width="414"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          class="form-item"
          :label="$t('模板类型')"
          label-style="top"
          prop="templateType"
        >
          <mt-select
            v-model="formInfo.templateType"
            :data-source="templateTypeList"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            width="414"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <!-- 适用品类01-左 -->
        <div style="width: 100%; display: flex; justify-content: space-between">
          <mt-form-item
            class="form-item form-item-spec"
            :label="$t('适用品类')"
            label-style="top"
            prop="suitCateg"
          >
            <div class="relative-select">
              <mt-select
                ref="categoryRef"
                v-model="formInfo.suitCateg"
                :data-source="dcategoryry"
                :placeholder="$t('请选择')"
                :fields="{ text: 'oneName', value: 'dcategoryryid' }"
                width="414"
                @input="dcategoryryChange"
              ></mt-select>
            </div>
          </mt-form-item>
          <!-- 适用品类02-右 -->
          <mt-form-item
            v-if="switchnit"
            class="form-item form-item-spec"
            :label="$t('请选择品类')"
            label-style="top"
            prop="categoryList"
          >
            <div class="relative-select">
              <mt-multi-select
                v-if="isCateShow"
                ref="categoryRef"
                v-model="formInfo.categoryList"
                :allow-filtering="true"
                :filtering="filteringResource"
                :data-source="categoryList"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :fields="{ text: 'categoryName', value: 'id' }"
                width="414"
                @input="categoryFlagChange"
              ></mt-multi-select>
            </div>
          </mt-form-item>
        </div>
        <div style="width: 100%; display: flex; justify-content: space-between">
          <!-- 适用组织01-左 -->
          <mt-form-item
            class="form-item form-item-spec"
            :label="$t('适用组织')"
            label-style="top"
            prop="oganizoryryid"
          >
            <div class="relative-select">
              <mt-select
                ref="categoryRef"
                v-model="formInfo.oganizoryryid"
                :data-source="oganizoryry"
                :placeholder="$t('请选择')"
                :fields="{ text: 'twoName', value: 'oganizoryryid' }"
                width="414"
                @input="oganizoryryChange"
              ></mt-select>
            </div>
          </mt-form-item>
          <!-- 适用组织02-右 -->
          <mt-form-item
            v-if="oganizoryrynit"
            class="form-item form-item-spec"
            :label="$t('请选择组织')"
            label-style="top"
            prop="orgList"
          >
            <div class="relative-select">
              <mt-DropDownTree
                ref="dropDownTree"
                v-if="!!fields.dataSource && fields.dataSource.length > 0 && isShow"
                :show-check-box="true"
                :placeholder="$t('请选择')"
                v-model="formInfo.orgList"
                :fields="fields"
                id="multiTreeSelect"
                :allow-multi-selection="true"
                :auto-check="true"
                width="414"
                @input="orgFlagChange"
              ></mt-DropDownTree>
            </div>
          </mt-form-item>
        </div>

        <mt-form-item
          class="form-item"
          :label="$t('满分')"
          prop="templateFullScore"
          label-style="top"
        >
          <mt-input
            width="460"
            :max-length="4"
            :max="1000"
            type="number"
            :show-clear-button="true"
            :show-spin-button="false"
            v-model="formInfo.templateFullScore"
            oninput="if(value>1000){value=1000}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
            height="40"
            :placeholder="$t('非负整数')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item form-item-spec"
          :label="$t('供应类型')"
          label-style="top"
          prop="suitSupplierType"
        >
          <div class="relative-select">
            <mt-select
              ref="categoryRef"
              v-model="formInfo.suitSupplierType"
              :data-source="suitSupplierTypeOptions"
              :placeholder="$t('请选择')"
              :fields="{ text: 'text', value: 'value' }"
              width="414"
              @input="oganizoryryChange"
            ></mt-select>
          </div>
        </mt-form-item>
        <mt-form-item
          class="form-item full-width"
          :label="$t('通过条件')"
          prop="reviewTemplatePassRules"
          label-style="top"
        ></mt-form-item>
        <mt-form
          class="task-item-select first-rule-width"
          ref="ruleRef"
          :model="firstPassRules"
          :rules="firstRules"
        >
          <div id="rules" class="pass-rule">
            <mt-form-item prop="conditionType">
              <mt-input
                :placeholder="$t('当')"
                :disabled="true"
                width="150"
                class="qualified"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="conditionAttribute">
              <mt-select
                v-model="firstPassRules.conditionAttribute"
                :data-source="conditionAttributeList"
                :placeholder="$t('请选择')"
                width="190"
                class="qualified"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="conditionSymbol">
              <mt-select
                v-model="firstPassRules.conditionSymbol"
                :data-source="symbolList"
                :placeholder="$t('请选择')"
                width="150"
                class="qualified"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="conditionTarget">
              <mt-input
                :show-clear-button="true"
                :show-spin-button="false"
                v-model="firstPassRules.conditionTarget"
                :placeholder="$t('非负整数')"
                width="150"
                oninput="if(value>1000){value=1000}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
              ></mt-input>
            </mt-form-item>
          </div>
        </mt-form>
        <form-item
          v-for="(p, index) in form.properties"
          :key="index"
          :ref="`properties_${index}`"
          :index="index"
          :params="p"
          @changeValue="changeValue"
          @deleteIndex="deleteRow"
          class="full-width"
        ></form-item>
        <i class="mt-icons mt-icon-icon_card_plus rule-add-button" @click="addRule">{{
          $t(' 添加条件')
        }}</i>
        <span></span>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
import formItem from './formItem.vue'
import utils from '@/utils/utils'

import {
  conditionTypeList,
  symbolList,
  conditionAttributeList,
  categoryTypeList,
  orgTypeList
} from '../config/index'
export default {
  components: { formItem },
  data() {
    return {
      suitSupplierTypeOptions: [
        { text: this.$t('原厂'), value: 1 },
        { text: this.$t('代理'), value: 2 }
      ],
      insoue: false, // tijiao
      switchnit: false, // 切换展示否品类
      // onecategoryId: "", //品类
      oganizoryrynit: false, // 切换展示否
      // orgIdryId: "", //组织
      dcategoryry: [
        {
          oneName: this.$t('通用'),
          dcategoryryid: '-99'
        },
        {
          oneName: this.$t('限品类'),
          dcategoryryid: '1'
        },
        {
          oneName: this.$t('通采'),
          dcategoryryid: '3'
        },
        {
          oneName: this.$t('非采'),
          dcategoryryid: '4'
        }
      ], //适用品类通用集合
      oganizoryry: [
        {
          twoName: this.$t('通用'),
          oganizoryryid: '-99'
        },
        {
          twoName: this.$t('限组织'),
          oganizoryryid: '1'
        }
      ], //适用组织通用集合
      templateTypeList: [],
      firstPassRules: {
        conditionAttribute: null,
        conditionType: 0
      },
      firstRules: {
        conditionAttribute: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        conditionSymbol: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        conditionTarget: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      form: {
        properties: []
      },
      cateEditTimes: 0,
      orgEditTimes: 0,
      categoryListCopy: [],
      categoryList: [],
      isShow: true,
      isCateShow: true,
      conditionAttributeList: conditionAttributeList,
      symbolList: symbolList,
      conditionTypeList: conditionTypeList,
      categoryTypeList: categoryTypeList,
      orgTypeList: orgTypeList,
      fields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },

      formInfo: {
        onecategoryId: '-99',
        orgIdryId: '-99',
        dcategoryryid: '-99',
        oganizoryryid: '-99',
        orgList: [],
        categoryList: [],
        stageName: '', // 阶段模板名称
        accessType: null, // 阶段类型
        sortValue: null, // 排序值
        status: 1, // 是否启用
        remark: '', //备注
        reviewTemplatePassRules: []
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmAndEnter,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      rules: {
        templateName: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        templateType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        templateFullScore: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        orgList: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryList: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        dcategoryryid: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }], //适用品类通用集合
        oganizoryryid: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }] //适用组织通用集合
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.initData()
    this.filteringResource = utils.debounce(this.filteringResource, 300)
  },
  methods: {
    //模糊搜索==品类
    async filteringResource(e) {
      await this.getCategoryList(e.text)
      e.updateData(this.categoryListCopy)
      this.$forceUpdate()
    },
    // 获取页面初始数据
    async initData() {
      await this.getCategoryList('')
      await this.getStatedLimitTree()
      await this.getTypeList()

      if (this.isEdit) {
        this.$nextTick(() => {
          if (this.info.categoryId != '-99') {
            this.switchnit = true
            this.formInfo.dcategoryryid = '1'
            this.formInfo.categoryList = this.info.categoryId.split(',')
          } else {
            this.formInfo.onecategoryId = '-99'
            this.formInfo.dcategoryryid = '-99'
            this.switchnit = false
          }
          if (this.info.orgId != '-99') {
            this.oganizoryrynit = true
            this.formInfo.oganizoryryid = '1'
            this.formInfo.orgList = this.info.orgId.split(',')
          } else {
            this.formInfo.oganizoryryid = '-99'
            this.formInfo.orgIdryId = '-99'
            this.oganizoryrynit = false
          }
          this.formInfo = {
            ...this.info,
            // templateType: this.info.templateType.split(","),
            // categoryList: this.info.categoryId.split(","),
            categoryList: this.formInfo.categoryList,
            // orgList: this.info.orgId.split(","),
            orgList: this.formInfo.orgList,
            dcategoryryid: this.formInfo.dcategoryryid,
            onecategoryId: this.formInfo.onecategoryId,
            orgIdryId: this.formInfo.orgIdryId,
            oganizoryryid: this.formInfo.oganizoryryid,
            categoryId: this.formInfo.dcategoryryid ? this.formInfo.dcategoryryid : '-99',
            orgId: this.formInfo.oganizoryryid ? this.formInfo.oganizoryryid : '-99'
          }
          this.firstPassRules = {
            ...this.info.reviewTemplatePassRules[0]
          }
          if (this.info.reviewTemplatePassRules.length > 1) {
            this.form.properties = this.info.reviewTemplatePassRules.slice(1)
          }
        })
      }
      this.show()
    },
    show() {
      this.$refs['reviewTempDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['reviewTempDialog'].ejsRef.hide()
    },
    // 获取评审模板类型列表
    async getTypeList() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'reviewType'
      }).then((res) => {
        if (res.code == 200) {
          let result = res.data
          let _max = Math.max.apply(
            null,
            result.map((e) => e.treeLevel)
          )
          this.templateTypeList.length = 0
          for (let i = _max; i > 0; i--) {
            result
              .filter((e) => e.treeLevel === i)
              .forEach((item) => {
                // result.find((x) => x.id == item.parentId).hasChild = true;
                item.itemName = `${result.find((x) => x.id == item.parentId).itemName}-${
                  item.itemName
                }`
                item.itemCode = `${result.find((x) => x.id == item.parentId).itemCode}-${
                  item.itemCode
                }`
                this.templateTypeList.push(item)
              })
          }
        }
      })
    },
    // 获取品类列表
    async getCategoryList(val) {
      await this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'statusId',
              type: 'int',
              operator: 'equal',
              value: 1
            },
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          this.categoryListCopy = res.data.records
          this.categoryListCopy.forEach((item) => {
            let bol = this.categoryList.some((e) => {
              return item.categoryCode == e.categoryCode
            })
            item.categoryName = item.categoryCode + '-' + item.categoryName
            if (!bol) {
              this.categoryList.push(item)
            }
          })
        })
    },
    // 获取组织树
    async getStatedLimitTree() {
      let query = 'ORG02'

      await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        // orgType: "ORG001ADM",
        orgType: 'ORG001PRO'
      }).then((res) => {
        let filterObj = function (item) {
          if (item.orgLeveLTypeCode == query) return true

          if (Object.prototype.hasOwnProperty.call(item, 'children')) {
            item.children = item.children.filter(function (child) {
              if (child.orgLeveLTypeCode == query) {
                return child.orgLeveLTypeCode == query
              } else if (Object.prototype.hasOwnProperty.call(child, 'children')) {
                return filterObj(child)
              }
            })
            if (item.children.length > 0) {
              return true
            }
          } else {
            return item.orgLeveLTypeCode == query
          }
        }
        let filter = res.data.filter(function (item) {
          return filterObj(item)
        })
        filter
          .unshift
          // { name: this.$t("通用"), id: -99 }
          // { name: this.$t("限制组织"), id: -999 }
          ()
        this.fields.dataSource = cloneDeep(filter)
      })
    },
    // 通用品类下拉
    dcategoryryChange(e) {
      if (e == '-99') {
        this.switchnit = false
        this.formInfo.onecategoryId = e
        this.formInfo.categoryList.push('-99')
      } else if (e == '1') {
        this.switchnit = true
      } else if (e == '3' || e == '4') {
        this.switchnit = false
      }
    },
    // 适用组织下拉
    oganizoryryChange(e) {
      if (e == '-99') {
        this.oganizoryrynit = false
        this.formInfo.orgIdryId = e
        this.formInfo.orgList.push('-99')
      } else {
        this.oganizoryrynit = true
      }
    },
    // 是否限品类
    categoryFlagChange(e) {
      if (this.cateEditTimes == 0) {
        ++this.cateEditTimes
        return
      }
      if (e.length > 1) {
        if (e[e.length - 1] == '-99') {
          this.formInfo.categoryList.length = 0
          this.formInfo.categoryList.push('-99')
          this.isCateShow = false

          setTimeout(() => {
            this.isCateShow = true
            this.$nextTick(() => {
              this.$refs.categoryRef.ejsRef.showPopup()
            })
          }, 50)
        } else if (e[0] == '-99') {
          this.formInfo.categoryList = cloneDeep(
            this.formInfo.categoryList.filter((e) => e != '-99')
          )
          this.isCateShow = false

          setTimeout(() => {
            this.isCateShow = true
            this.$nextTick(() => {
              this.$refs.categoryRef.ejsRef.showPopup()
            })
          }, 50)
        }
      }
    },
    // 是否限组织
    orgFlagChange(e) {
      if (this.orgEditTimes == 0) {
        ++this.orgEditTimes
        return
      }
      if (e.length > 1) {
        if (e[e.length - 1] == '-99') {
          this.formInfo.orgList.length = 0
          this.formInfo.orgList.push('-99')
          this.isShow = false

          setTimeout(() => {
            this.isShow = true
            this.$nextTick(() => {
              this.$refs.dropDownTree.ejsRef.showPopup()
            })
          }, 50)
        } else if (e[0] == '-99') {
          this.formInfo.orgList = cloneDeep(this.formInfo.orgList.filter((e) => e != '-99'))
          this.isShow = false

          setTimeout(() => {
            this.isShow = true
            this.$nextTick(() => {
              this.$refs.dropDownTree.ejsRef.showPopup()
              this.$refs.dropDownTree.ejsRef.hidePopup()
              this.$refs.dropDownTree.ejsRef.showPopup()
            })
          }, 50)
        }
      }
    },
    // 添加条件
    addRule() {
      let len = this.form.properties.length
      this.$refs.ruleRef.validate((valid) => {
        if (valid && len > 0) {
          for (let i = 0; i < len; i++) {
            this.$refs['properties_' + i][0].$refs.propertiesForm.validate((valid) => {
              if (valid && i == len - 1) {
                this.form.properties.push({})
              }
            })
          }
        } else if (valid && len == 0) {
          this.form.properties.push({})
        }
      })
    },
    // 条件变更
    changeValue(e, data) {
      this.form.properties[e] = data
    },
    // 删除条件
    deleteRow(e) {
      this.form.properties.splice(e, 1)
    },
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'children') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    confirm(type = 'confirm') {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'updateReviewTemp' : 'addReviewTemp'
          let flatData = this.flatTreeData(this.fields.dataSource)
            .filter((e) => e.orgLeveLTypeCode === 'ORG02')
            .map((x) => x.id)
          // 品类赋值;
          if (this.switchnit === false) {
            // this.formInfo.onecategoryId = this.formInfo.onecategoryId
            this.formInfo.categoryList.push('-99')
          } else {
            this.formInfo.onecategoryId = this.formInfo.categoryList.toString()
          }
          // 组织赋值
          if (this.oganizoryrynit === false) {
            // this.formInfo.orgIdryId = this.formInfo.orgIdryId
            this.formInfo.orgList.push('-99')
          } else {
            this.formInfo.orgIdryId = flatData
              .filter((v) => this.formInfo.orgList.includes(v))
              .toString()
          }
          let _tempInfo = {
            ...this.formInfo,
            reviewTemplatePassRules: [],
            categoryId: this.formInfo.onecategoryId,

            orgId: this.formInfo.orgIdryId
          }

          this.$refs.ruleRef.validate((valid) => {
            if (valid) {
              _tempInfo.reviewTemplatePassRules.length = 0

              if (this.form.properties.length > 0) {
                for (let i = 0; i < this.form.properties.length; i++) {
                  this.$refs['properties_' + i][0].$refs.propertiesForm.validate((valid) => {
                    if (valid && i == this.form.properties.length - 1) {
                      _tempInfo.reviewTemplatePassRules.push(this.firstPassRules)
                      _tempInfo.reviewTemplatePassRules = _tempInfo.reviewTemplatePassRules.concat(
                        this.form.properties
                      )
                      let bol = _tempInfo.reviewTemplatePassRules.every((item) => {
                        if (item.conditionAttribute == '1') {
                          if (Number(item.conditionTarget) > Number(_tempInfo.templateFullScore)) {
                            return false
                          } else {
                            return true
                          }
                        } else {
                          return true
                        }
                      })
                      if (!bol) {
                        this.$toast({
                          content: this.$t('条件分不能大于满分值'),
                          type: 'error'
                        })
                      } else {
                        this.saveData(methodName, _tempInfo, type)
                      }
                    }
                  })
                }
              } else if (this.form.properties.length == 0) {
                _tempInfo.reviewTemplatePassRules.push(this.firstPassRules)
                let bol = _tempInfo.reviewTemplatePassRules.every((item) => {
                  if (item.conditionAttribute == '1') {
                    if (Number(item.conditionTarget) > Number(_tempInfo.templateFullScore)) {
                      return false
                    } else {
                      return true
                    }
                  } else {
                    return true
                  }
                })
                if (!bol) {
                  this.$toast({
                    content: this.$t('条件分不能大于满分值'),
                    type: 'error'
                  })
                } else {
                  this.saveData(methodName, _tempInfo, type)
                }
              }
            }
          })
        }
      })
    },
    saveData(methodName, data, type) {
      this.$API.ModuleConfig[methodName](data)
        .then((res) => {
          if (res.code == 200) {
            if (type === 'enter') {
              if (this.formInfo.id || data.templateId) {
                this.$emit('confirm-function', this.formInfo.id || data.templateId)
              }
              this.$emit('confirm-function', res.data.templateId)
            } else {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },
    confirmAndEnter() {
      this.confirm('enter')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
/deep/ .form-item-spec {
  width: 414px;
}
/deep/ .full-width {
  width: 100%;
}
/deep/ .first-rule-width {
  width: 782px;
}
/deep/ .pass-rule {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .is-required {
    display: none;
  }
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
@mixin rule-icon {
  line-height: 35px;
  font-size: 14px;
  font-weight: normal;
  color: rgba(99, 134, 193, 1);
}
/deep/ .rule-add {
  @include rule-icon;
  width: 35px;
  &-button {
    @include rule-icon;
    width: 100px;
  }
}

/deep/ .relative-select {
  display: flex;
  justify-content: space-between;
  .mt-drop-down-tree {
    width: 300px;
  }
}
/deep/ .mt-drop-down-tree .e-chips-wrapper {
  position: absolute !important;
}
</style>
