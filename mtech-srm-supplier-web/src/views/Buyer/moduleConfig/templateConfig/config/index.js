import { i18n } from '@/main.js'
import Vue from 'vue'
import { taskTemplateStatusSetting } from '@/utils/setting'

export const categoryTypeList = [
  { text: i18n.t('通用'), value: '-99' },
  { text: i18n.t('限品类'), value: '1' },
  { text: i18n.t('通采'), value: '3' },
  { text: i18n.t('非采'), value: '4' }
]
export const orgTypeList = [
  { text: i18n.t('通用'), value: '0' },
  { text: i18n.t('限组织'), value: '1' }
]
export const suitSupplierTypeOptions = [
  { text: i18n.t('原厂'), value: 1 },
  { text: i18n.t('代理'), value: 2 }
]
export const sceneTypeList = [
  { text: i18n.t('通用'), value: '0' },
  { text: i18n.t('限场景'), value: '1' }
]
export const applyTypeList = [
  {
    text: i18n.t('品类准入'),
    value: '1'
  },
  { text: i18n.t('企业准入'), value: '2' }
]
export const conditionAttributeList = [
  { text: i18n.t('得分'), value: '1' },
  { text: i18n.t('不合格项个数'), value: '3' },
  { text: i18n.t('合格项个数'), value: '2' }
]
export const symbolList = [
  {
    text: '>',
    value: '1'
  },
  {
    text: '<',
    value: '2'
  },
  {
    text: '≥',
    value: '3'
  },
  {
    text: '≤',
    value: '4'
  },
  {
    text: '=',
    value: '5'
  }
]
export const conditionTypeList = [
  { text: i18n.t('且'), value: '1' },
  { text: i18n.t('或'), value: '2' }
]
export let templateTypesList = []
export let categoryList = []
export let orgList = []
export let questionnaireTypeList = []
export let thresholdTypeList = []

// const mapJson = (data) => {
//   const json = {}
//   data.map((item) => {
//     json[item.id] = item.itemName
//   })
//   return json
// }

// ==========调查表模板==========
export const questionnaireTempColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'surveyTemplateCode',
    headerText: i18n.t('编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ]
  },
  {
    field: 'surveyTemplateName',
    // width: "150",
    headerText: i18n.t('调查表名称')
  },
  {
    field: 'surveyTemplateType',
    // width: "150",
    headerText: i18n.t('调查表类型'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: null
            }
          },
          mounted() {
            let _record = questionnaireTypeList.find(
              (e) => e.itemCode == this.data.surveyTemplateType
            )
            if (_record != null) {
              this.content = _record.itemName
            } else {
              this.content = ''
            }
          }
        })
      }
    }
  },
  {
    field: 'categoryName',
    // width: "150",
    headerText: i18n.t('适用品类'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: null
            }
          },
          mounted() {
            this.content = this.data.categoryIds == '-99' ? i18n.t('通用') : this.data.categoryName
          }
        })
      }
    }
  },
  {
    field: 'orgName',
    // width: "150",
    headerText: i18n.t('适用组织'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: null
            }
          },
          mounted() {
            this.content = this.data.orgIds == '-99' ? i18n.t('通用') : this.data.orgName
          }
        })
      }
    }
  },
  {
    field: 'surveyTemplateVersion',
    // width: "150",
    headerText: i18n.t('版本')
  },
  {
    field: 'status',
    // width: "150",
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  }
]

// ==========评审模板==========
export const reviewTempColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'templateCode',
    headerText: i18n.t('编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] !== '1'
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] !== '1'
        }
      }
    ]
  },
  {
    field: 'templateName',
    // width: "150",
    headerText: i18n.t('评审模板名称')
  },
  {
    field: 'templateTypeName',
    width: '150',
    headerText: i18n.t('模板类型')
  },
  {
    field: 'categoryName',
    // width: "150",
    headerText: i18n.t('适用品类'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: null
            }
          },
          mounted() {
            this.content = this.data.categoryId == '-99' ? i18n.t('通用') : this.data.categoryName
          }
        })
      }
    }
  },
  {
    field: 'orgName',
    // width: "150",
    headerText: i18n.t('适用组织'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: null
            }
          },
          mounted() {
            this.content = this.data.orgId == '-99' ? i18n.t('通用') : this.data.orgName
          }
        })
      }
    }
  },
  {
    field: 'suitSupplierType',
    // width: "150",
    headerText: i18n.t('供应类型'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('原厂'),
        2: i18n.t('代理'),
        0: i18n.t('-')
      }
    }
  },
  {
    field: 'version',
    // width: "150",
    headerText: i18n.t('版本')
  },
  {
    field: 'status',
    // width: "150",
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('创建日期')
    // valueConverter: {
    //   type: "date",
    // },
    // searchOptions: {
    //   type: "date",
    // },
  }
]
// ==========门槛模板==========
export const thresholdTempColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'templateCode',
    headerText: i18n.t('编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      }
    ]
  },
  {
    field: 'templateName',
    // width: "150",
    headerText: i18n.t('门槛模板名称')
  },
  {
    field: 'categoryName',
    // width: "150",
    headerText: i18n.t('适用品类'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: null
            }
          },
          mounted() {
            this.content = this.data.categoryIds == '-99' ? i18n.t('通用') : this.data.categoryName
          }
        })
      }
    }
  },
  {
    field: 'orgName',
    // width: "150",
    headerText: i18n.t('适用组织'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: null
            }
          },
          mounted() {
            this.content = this.data.orgIds == '-99' ? i18n.t('通用') : this.data.orgName
          }
        })
      }
    }
  },
  {
    field: 'version',
    // width: "150",
    headerText: i18n.t('版本')
  },
  {
    field: 'status',
    // width: "150",
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('启用'),
        2: i18n.t('停用'),
        3: i18n.t('待审核')
      }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  }
]

// ==========资质模板==========
export const qualificationData = [
  {
    questionnaireCode: '001',
    versionName: i18n.t('资质名称'),
    version: i18n.t('资质模板类型')
  }
]
export const qualificationColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'qualificationTemplateCode',
    headerText: i18n.t('编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] !== '1'
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] !== '1'
        }
      }
    ]
  },
  {
    field: 'qualificationTemplateName',
    headerText: i18n.t('资质模板名称')
  },
  {
    field: 'qualificationTemplateTypeName',
    headerText: i18n.t('资质模板类型')
  },
  {
    field: 'categoryInfoList',
    // width: "150",
    headerText: i18n.t('适用品类'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: ''
            }
          },
          mounted() {
            if (this.data.categoryInfoList.length == 0) {
              this.content = i18n.t('通用')
            } else {
              for (let i = 0; i < this.data.categoryInfoList.length; i++) {
                const item = this.data.categoryInfoList[i]
                this.content += item.categoryName
                if (i != this.data.categoryInfoList.length - 1) {
                  this.content += '、'
                }
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'organizationInfoList',
    // width: "150",
    headerText: i18n.t('适用组织'),
    template: () => {
      return {
        template: Vue.component('common', {
          template: `<div>{{content}}</div>`,
          data() {
            return {
              data: {},
              content: ''
            }
          },
          mounted() {
            if (this.data.organizationInfoList.length == 0) {
              this.content = i18n.t('通用')
            } else {
              for (let i = 0; i < this.data.organizationInfoList.length; i++) {
                const item = this.data.organizationInfoList[i]
                this.content += item.organizationName
                if (i != this.data.organizationInfoList.length - 1) {
                  this.content += '、'
                }
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'status',
    // width: "150",
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date'
    }
  }
]

// ==========评审包==========
export const reviewColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'packageCode',
    headerText: i18n.t('编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] != 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] != 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'packageName',
    // width: "150",
    headerText: i18n.t('评审包名称')
  },
  {
    field: 'templateTypeNames',
    // width: "150",
    headerText: i18n.t('模板类型')
    // template: () => {
    //   return {
    //     template: Vue.component("common", {
    //       template: `<div>{{content}}</div>`,
    //       data() {
    //         return {
    //           data: {},
    //           content: null,
    //         };
    //       },
    //       mounted() {
    //         this.content = [];
    //         let tempTypes = this.data.templateTypes.split(",");
    //         tempTypes.forEach((element) => {
    //           let child = templateTypesList.find((e) => e.id === element);
    //           let parent = templateTypesList.find(
    //             (e) => e.id === child.parentId
    //           );
    //           this.content.push(`${parent.itemName}-${child.itemName}`);
    //         });
    //         this.content = this.content.toString();
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'ruleNames',
    // width: "150",
    headerText: i18n.t('通过条件')
    // template: () => {
    //   return {
    //     template: Vue.component("common", {
    //       template: `<div>{{content}}</div>`,
    //       data() {
    //         return {
    //           data: {},
    //           content: null,
    //         };
    //       },
    //       mounted() {
    //         this.content = "";
    //         if (this.data.reviewPackagePassRules.length > 0) {
    //           this.data.reviewPackagePassRules.forEach((e) => {
    //             if (e.conditionType == 0) {
    //               this.content = this.content
    //                 .concat(i18n.t("当"))
    //                 .concat(
    //                   conditionAttributeList.find(
    //                     (element) => element.value == e.conditionAttribute
    //                   ).text
    //                 )
    //                 .concat(
    //                   symbolList.find(
    //                     (element) => element.value == e.conditionSymbol
    //                   ).text
    //                 )
    //                 .concat(e.conditionTarget);
    //             } else {
    //               this.content = this.content
    //                 .concat(
    //                   conditionTypeList.find(
    //                     (element) => element.value == e.conditionType
    //                   ).text
    //                 )
    //                 .concat(
    //                   conditionAttributeList.find(
    //                     (element) => element.value == e.conditionAttribute
    //                   ).text
    //                 )
    //                 .concat(
    //                   symbolList.find(
    //                     (element) => element.value == e.conditionSymbol
    //                   ).text
    //                 )
    //                 .concat(e.conditionTarget);
    //             }
    //           });
    //         } else {
    //           this.content = i18n.t("无");
    //         }
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'status',
    // width: "150",
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('创建日期')
    // valueConverter: {
    //   type: "date",
    // },
    // searchOptions: {
    //   type: "date",
    // },
  }
]
