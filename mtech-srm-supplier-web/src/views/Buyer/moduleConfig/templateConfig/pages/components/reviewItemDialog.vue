// 添加指标弹窗
<template>
  <mt-dialog ref="thresholdDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <mt-template-page ref="template1" :template-config="pageConfig"></mt-template-page>
  </mt-dialog>
</template>
<script>
import { reviewItemColumns } from '../config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          // gridId: '8cd2ffb4-c22d-4df1-82d7-f23603c7054d',
          toolbar: [],
          grid: {
            // allowPaging: false,
            columnData: reviewItemColumns,
            // dataSource: [],
            asyncConfig: {
              url: '/supplier/tenant/buyer/define/review/query'
            }
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    usedIndexs() {
      return this.modalData.indexDTOS
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.show()
    },

    show() {
      this.$refs['thresholdDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdDialog'].ejsRef.hide()
    },
    confirm() {
      let _records = this.$refs.template1.getCurrentTabRef().gridRef.getMtechGridRecords()
      let usedIndexList = []
      if (this.usedIndexs && _records.length > 0) {
        usedIndexList = _records.filter((item) =>
          this.usedIndexs.some((e) => e.reviewId == item.reviewId)
        )
      }
      if (usedIndexList.length) {
        this.$toast({
          content: this.$t(
            `当前维度下,名称为：（${usedIndexList[0].reviewName}）的不能重复配置指标`
          ),
          type: 'warning'
        })
        return
      }
      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .relative-select {
  display: flex;
  justify-content: space-between;
  .mt-drop-down-tree {
    width: 300px;
  }
}
</style>
