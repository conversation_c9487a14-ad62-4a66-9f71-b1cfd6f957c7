<template>
  <!-- 资质模板模板项==添加 -->
  <mt-dialog ref="thresholdDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @checkBoxChange="rowSelected"
    ></mt-template-page>
  </mt-dialog>
</template>
<script>
import { qualificationColumns } from '../config/index'
const editSettings = {
  allowEditing: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false
}
export default {
  data() {
    return {
      pageConfig: [
        {
          // gridId:"2a7d67d5-ef73-4ad6-ad81-68c5a27a7cc6",
          toolbar: {
            tools: []
          },
          grid: {
            lineIndex: true,
            columnData: qualificationColumns(),
            editSettings,
            asyncConfig: {
              url: this.$API.ModuleConfig.qualificationQueryPage,
              defaultRules: [
                {
                  label: this.$t('状态'),
                  field: 'status',
                  type: 'string',
                  operator: 'equal',
                  value: '1'
                }
              ], // 过滤只有启用状态的数据才可以添加
              afterAsyncData: (res) => {
                if (res.code === 200 && res.data.records) {
                  this.dataSource = res.data.records
                  let arrNum = []
                  this.dataSource.map((i, idx) => {
                    this.info.map((ic) => {
                      if (i.qualificationCode === ic.qualificationCode) {
                        arrNum.push(idx)
                      }
                    })
                  })
                  setTimeout(() => {
                    this.$refs.templateRef.getCurrentTabRef().grid.selectRows(arrNum, true)
                  }, 300)
                }
              }
            }
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('添加') }
        }
      ],
      dataSource: [],
      infoArr: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.show()
    this.infoArr = JSON.parse(JSON.stringify(this.modalData.info))
  },
  methods: {
    rowSelected(e) {
      let selectArr = []
      let noselectArr = []
      this.dataSource.forEach((item, index) => {
        if (e.selectedRowIndexes.indexOf(index) != -1) {
          selectArr.push(item)
        } else {
          noselectArr.push(item)
        }
      })
      selectArr.forEach((item) => {
        let bol = this.infoArr.some((e) => {
          return e.qualificationCode == item.qualificationCode
        })
        if (!bol) {
          this.infoArr.push(item)
        }
      })
      for (let i = 0; i < this.infoArr.length; i++) {
        let bol = noselectArr.some((item) => {
          return item.qualificationCode == this.infoArr[i].qualificationCode
        })
        if (bol) {
          this.infoArr.splice(i, 1)
          i--
        }
      }
    },
    handleClickCellTitle(e) {
      const { data } = e
      if (e.field == 'modelName') {
        this.$API.fileService
          .downloadPrivateFileTypeOne(data.modelUrl)
          .then((res) => {
            let link = document.createElement('a') // 创建元素
            link.style.display = 'none'
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            let url = window.URL.createObjectURL(blob)
            link.href = url
            link.setAttribute('download', `${data.modelName}`) // 给下载后的文件命名
            link.click() // 点击下载
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$toast({
              content: this.$t('导出失败，请重试!'),
              type: 'warning'
            })
          })
      }
    },
    show() {
      this.$refs['thresholdDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdDialog'].ejsRef.hide()
    },
    confirm() {
      // let arr = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getSelectedRecords(); //得到选择行

      if (this.infoArr.length == 0)
        return this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      this.infoArr = JSON.parse(JSON.stringify(this.infoArr))
      this.$emit('confirm-function', this.infoArr)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .relative-select {
  display: flex;
  justify-content: space-between;
  .mt-drop-down-tree {
    width: 300px;
  }
}
</style>
