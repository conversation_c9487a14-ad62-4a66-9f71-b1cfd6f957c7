<template>
  <div class="task-detail">
    <detail-card
      :title="detailInfo.title"
      :status="detailInfo.status"
      :classify="detailInfo.classify"
      :detail="detailInfo.detail"
      @btn-click="btnClick"
    ></detail-card>
    <!-- 表单配置 -->
    <div class="console-box">
      <div class="form-left">
        <div class="form-bg search-box">
          <div class="e-input-group">
            <span class="e-input-group-icon mt-icons mt-icon-icon_search"></span>
            <input
              v-model="searchVal"
              class="e-input"
              type="text"
              :placeholder="$t('搜索表单库')"
              :maxlength="30"
            />
          </div>
          <!-- <span class="e-input-group-icon mt-icons mt-icon-icon_search"></span>
          <mt-input v-model="searchVal" :placeholder="$t('搜索表单库')"></mt-input> -->
        </div>

        <div class="form-bg form-list form-default-list">
          <div id="drggable-default">
            <div
              :class="['form-item', isEdit && 'drggable-item']"
              v-for="item in formDefaultList"
              :key="item.id"
            >
              <i class="mt-icons mt-icon-icon_outline_Multiselect icon-list"></i>
              <span>{{ item.name }}</span>
              <i class="mt-icons mt-icon-icon_relate icon-drggable"></i>
            </div>
          </div>
          <!-- 可选择的表单列表为空 -->
          <div v-if="!formDefaultList.length" class="no-list form-bg">
            <img src="../../../../../assets/images/outofForm.png" alt="" />
            <p>{{ $t('表单库中还没有表单') }}</p>
            <p class="link-btn" @click="toFormEdit">
              {{ $t('前往动态表单创建') }}
            </p>
          </div>
        </div>

        <div class="left-placeholder">
          <i class="mt-icons mt-icon-icon_card_transfer"></i>
        </div>

        <!-- 用户已选择的表单列表 -->
        <div class="form-bg form-list form-select-list">
          <div id="drggable-select">
            <div
              :class="['form-item', isEdit && 'drggable-item']"
              v-for="item in formTemplateList"
              :key="item.id"
            >
              <i class="mt-icons mt-icon-icon_outline_Multiselect icon-list"></i>
              <span>{{ item.name }}</span>
              <i class="mt-icons mt-icon-icon_relate icon-drggable"></i>
            </div>
          </div>
          <div v-if="formTemplateList.length === 0" class="no-list form-bg">
            <img src="../../../../../assets/images/dargForm.png" alt="" />
            <p>{{ $t('从上方表单库拖动表单到此处 以添加调查表配置') }}</p>
          </div>
        </div>
      </div>

      <div class="form-right" :class="[!formTemplateList.length && 'form-no-data']">
        <template v-if="formTemplateList.length">
          <div v-for="item in formTemplateList" :key="item.id">
            <div class="accordion">
              <div
                :class="['accordion-header', item.expanded && 'header-expanded']"
                @click="toggleAccordion(item)"
              >
                <span class="header-name">{{ item.name }}</span>
                <i class="mt-icons mt-icon-select_1 icon-arrow"></i>
              </div>
              <div v-if="item.expanded" class="accordion-content">
                <form-parser v-if="item.template" :form-conf="item.template"></form-parser>
              </div>
            </div>
          </div>
        </template>
        <!-- 没有数据 -->
        <form-placeholder v-else></form-placeholder>
      </div>
    </div>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import DetailCard from '../components/detailCard.vue'
import FormPlaceholder from '../components/formPlaceholder.vue'
import FormParser from '@mtech-form-design/form-parser'
const getDomain = () => {
  let domain = document.domain === 'localhost' ? 'dev.' : document.domain
  const domainList = ['dev.', 'test.', 'demo.']
  let index = domainList.findIndex((item) => domain.indexOf(item) > -1)
  domain = index > -1 ? domainList[index] : ''

  return domain
}

export default {
  components: {
    DetailCard,
    FormPlaceholder,
    FormParser
  },
  data() {
    return {
      taskTemplateId: '',
      detailInfo: {},
      sltFormList: [],
      formDefaultList: [],
      formTemplateList: [],
      apiFormTemplateList: [], // 接口返回的列表
      showSltPic: true,
      // 搜索表单库
      searchVal: '',
      formListCopy: [] // 备份
    }
  },
  computed: {
    taskId() {
      return this.$route.query.id || null
    },
    isEdit() {
      return this.detailInfo.status == 2
    }
  },
  watch: {
    isEdit(v) {
      if (v === true) {
        this.draggable()
      }
    },
    searchVal(val) {
      if (val) {
        this.searchForm(val)
      } else {
        // 重置数据
        this.formDefaultList = JSON.parse(JSON.stringify(this.formListCopy))
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    async initData() {
      await this.initDetail()
    },

    async initDetail() {
      this.$loading()
      await this.queryFormDesignList()
      const { code, data } = await this.$API.QuestionnaireConfig.detailFormTemplate(this.taskId)
      if (code == 200) {
        const { id, status, surveyTemplateName, taskTemplateClassify } = data
        this.taskTemplateId = id
        this.detailInfo = {
          status,
          title: surveyTemplateName,
          classify: taskTemplateClassify,
          detail: [
            { text: this.$t('调查表编码'), value: data.surveyTemplateCode },
            {
              text: this.$t('创建人'),
              value: data.createUserName
            },
            {
              text: this.$t('创建时间'),
              value: data.createTime
            },
            {
              text: this.$t('备注'),
              value: data.remark
            }
          ]
        }
        await this.queryFormTask()
      }
    },
    // 获取动态表单库的列表
    async queryFormDesignList() {
      let params = {
        businessType: 'QuestionnaireTemplate',
        current: 1,
        size: 1000,
        isActive: true
      }
      await this.$API.QuestionnaireConfig.queryFormDesignList(params)
        .then((res) => {
          const { code, data } = res
          if (code == 200 && data) {
            const { data: records } = data
            // this.formDefaultList = records;
            this.formListCopy = JSON.parse(JSON.stringify(records))
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },
    // 获取已配置的列表
    async queryFormTask() {
      const { code, data } = await this.$API.QuestionnaireConfig.queryFormTask(this.taskTemplateId)
      if (code == 200 && data) {
        const { items } = data
        let ids = []

        if (items.length === 0) {
          this.formDefaultList = this.formListCopy
          console.log('this.formDefaultList', this.formDefaultList)
          this.$hloading()
        }

        items.forEach((item, index) => {
          const { formTemplateKey, formTemplateVersion } = item
          ids.push(formTemplateKey)
          item && this.getFormDesignTemplate(formTemplateKey, formTemplateVersion, index)
        })

        this.apiFormTemplateList = items

        this.formDefaultList = this.formListCopy.filter((item) => {
          let index = ids.findIndex((id) => id === item.id)
          return index === -1
        })
      }
    },

    // 根据id获取表单结构
    getFormDesignTemplate(id, version, position) {
      this.$loading()
      if (!id) return
      let params = version ? `id=${id}&version=${version}` : `id=${id}`
      this.$API.QuestionnaireConfig.getFormDesignTemplate(params)
        .then((res) => {
          const { code, data } = res
          if (code == 200 && data) {
            let formTemplate = Object.assign({}, data, {
              id,
              expanded: true
            })
            this.formTemplateList.splice(position, 0, formTemplate)
          }
          this.$hloading()
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },

    toggleAccordion(item) {
      item.expanded = !item.expanded
    },

    searchForm(searchVal) {
      this.formDefaultList = this.formDefaultList.filter(
        (item) => item.name.indexOf(searchVal) > -1
      )
    },

    toFormEdit() {
      window.open(
        `//platform.${getDomain()}qeweb.com/#/middlePlatform/formEdit?classifyId=&type=add&id=`,
        '_href'
      )
    },

    btnClick(args) {
      if (args.id === 'back') {
        // 返回未保存提示
        if (this.judgeIsEquel()) {
          this.$router.push('/supplier/pur/template-config')
        } else {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('您的修改未保存，确定要返回？')
            },
            success: () => {
              this.$router.push('/supplier/pur/template-config')
            }
          })
        }
      }
      if (args.id === 'save' || args.id === 'saveAndEnable') {
        // const { taskTemplateId } = this;
        const surveyTemplateId = this.$route.query.id
        let surveyTemplateItemList = this.formTemplateList.map((item, index) => {
          const { id, name, businessType, version } = item
          return {
            formTemplateKey: id,
            formTemplateName: name,
            formTemplateType: businessType,
            formTemplateVersion: version,
            sequenceNo: index
          }
        })
        if (!surveyTemplateItemList.length) {
          this.$toast({ content: this.$t('表单模板不能为空'), type: 'error' })
          return
        }
        let params =
          args.id === 'save'
            ? {
                surveyTemplateItemList,
                surveyTemplateId
              }
            : {
                surveyTemplateItemList,
                surveyTemplateId,
                status: 1
              }
        this.$API.ModuleConfig.updateFormTask(params).then((res) => {
          const { code } = res
          if (code == 200) {
            this.apiFormTemplateList = params.surveyTemplateItemList
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            if (args.id === 'saveAndEnable') {
              this.$router.push('/supplier/pur/template-config')
            }
          }
        })
      }
    },

    // 判断是否有修改选择
    judgeIsEquel() {
      let isEquel = true
      if (this.apiFormTemplateList.length === this.formTemplateList.length) {
        isEquel = true
        this.formTemplateList.forEach((v) => {
          if (this.apiFormTemplateList.filter((cv) => v.id === cv.formTemplateKey).length === 0) {
            isEquel = false
            return isEquel
          }
        })
      } else {
        isEquel = false
      }

      return isEquel
    },

    // 实现拖拽
    draggable() {
      const defaultList = document.getElementById('drggable-default')
      const sltList = document.getElementById('drggable-select')
      const _this = this
      Sortable.create(defaultList, {
        group: { name: 'form', pull: true, put: true },
        draggable: '.drggable-item',
        sort: false,
        animation: 150,
        onEnd({ oldIndex, newDraggableIndex, to }) {
          if (to.id === 'drggable-select') {
            let sltRow = _this.formDefaultList[oldIndex]
            _this.formDefaultList.splice(oldIndex, 1)
            // 用于搜索后的重置
            let index = _this.formListCopy.findIndex((item) => item.id === sltRow.id)
            _this.formListCopy.splice(index, 1)

            const { id, version } = sltRow
            _this.getFormDesignTemplate(id, version, newDraggableIndex)
          }
        }
      })
      Sortable.create(sltList, {
        group: { name: 'form', pull: true, put: true },
        draggable: '.drggable-item',
        animation: 150,
        onEnd({ newIndex, oldIndex, newDraggableIndex, to }) {
          let sltRow = _this.formTemplateList[oldIndex]
          if (to.id === 'drggable-select') {
            _this.formTemplateList.splice(oldIndex, 1)
            _this.formTemplateList.splice(newIndex, 0, sltRow)
          }
          if (to.id === 'drggable-default') {
            _this.formDefaultList.splice(newDraggableIndex, 0, sltRow)
            // 用于搜索后的重置
            _this.formListCopy.splice(newDraggableIndex, 0, sltRow)

            _this.$nextTick(() => {
              _this.formTemplateList.splice(oldIndex, 1)
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.task-detail {
  height: 100vh;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  .console-box {
    flex: 1;
    display: flex;
    margin-top: 20px;
    width: 100%;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    background: #fff;
    overflow: hidden;
  }
  .form-left {
    width: 360px;
    background: #fff;
    display: flex;
    flex-direction: column;
    .form-bg {
      margin: 20px;
      background: #fff;
    }
    .form-list {
      padding-right: 10px;
      .form-item {
        width: 100%;
        line-height: 50px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        margin-bottom: 10px;
        padding: 0 10px;
        > span {
          font-size: 16px;
          color: #292929;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
      .drggable-item {
        cursor: move;
      }
    }
    .search-box {
      margin: 20px 20px 0;
      display: flex;
    }
    .form-default-list {
      height: 45%;
      overflow-y: scroll;
      margin-bottom: 20px;
    }
    .form-select-list {
      flex: 1;
      overflow-y: scroll;
    }
    .left-placeholder {
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;
      border-top: 1px solid #e8e8e8;
      font-size: 30px;
      color: #9baac1;
    }
    .no-list {
      font-size: 16px;
      color: #9a9a9a;
      text-align: center;
      > img {
        display: inline-block;
        width: 160px;
        height: 160px;
        margin-bottom: 10px;
        margin: auto;
      }
      .link-btn {
        font-size: 14px;
        color: #00469c;
        margin-top: 28px;
        font-weight: 600;
        cursor: pointer;
      }
    }
  }
  .form-right {
    flex: 1;
    overflow-y: scroll;
    padding: 20px;
    border-left: 1px solid #e8e8e8;
  }
  .form-no-data {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.accordion {
  border: 1px solid #e8e8e8;
  .accordion-header {
    height: 46px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    cursor: pointer;
    font-size: 14px;
    color: #292929;
    font-weight: 600;
    .header-name {
      flex: 1;
    }
  }
  .header-expanded {
    background: #fafafa;
    .header-name {
      color: #6386c1;
      font-weight: normal;
    }
    .icon-arrow {
      transform: rotate(180deg) scale(0.7);
    }
  }
  &-content {
    padding: 20px;
    border-top: 1px solid #e8e8e8;
  }

  .icon-arrow {
    color: #9baac1;
    font-size: 12px;
    transform: scale(0.7);
    transition: transform 0.5;
  }
}

.icon-list {
  color: #00469c;
  margin-right: 6px;
}
.icon-drggable {
  color: #00469c;
  float: right;
  line-height: 50px;
}

::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}
</style>
