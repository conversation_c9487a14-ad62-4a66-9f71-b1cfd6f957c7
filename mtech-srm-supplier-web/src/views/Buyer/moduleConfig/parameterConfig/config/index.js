import { i18n } from '@/main.js'
import Vue from 'vue'
import { taskTemplateStatusSetting, taskTemplateTypeSetting } from '@/utils/setting'
export const categoryTypeList = [
  { text: i18n.t('通用'), value: '0' },
  { text: i18n.t('限品类'), value: '1' }
]
export const orgTypeList = [
  { text: i18n.t('通用'), value: '0' },
  { text: i18n.t('限组织'), value: '1' }
]
export const supplierStatus = {
  '-99': i18n.t('通用')
}
const symbolMap = {
  1: '>',
  2: '<',
  3: '≥',
  4: '≤',
  5: '=',
  6: i18n.t('非空'),
  7: i18n.t('为空')
}
// =====阶段定义=====
const stageDefcolumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'stageCode',
    headerText: i18n.t('编号'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'accessType',
    width: '120',
    headerText: i18n.t('阶段类型'),
    valueConverter: {
      type: 'map',
      map: taskTemplateTypeSetting
    }
  },
  {
    field: 'stageName',
    width: '120',
    headerText: i18n.t('阶段名称')
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'sortValue',
    headerText: i18n.t('排序值')
    // width: "180",
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]

// =====信息定义=====
const infoDefcolumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'businessFirstType',
    headerText: i18n.t('一级信息类型')
  },
  {
    field: 'businessSecondType',
    headerText: i18n.t('二级信息类型'),
    width: '180'
  },
  {
    field: 'fieldName',
    width: '120',
    headerText: i18n.t('字段名称')
  },
  {
    field: 'fieldSet',
    width: '120',
    headerText: i18n.t('字段类型')
  },
  {
    field: 'dimension',
    width: '120',
    headerText: i18n.t('管控维度'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('通用') }
    }
  },
  {
    field: 'approve',
    width: '120',
    headerText: i18n.t('审批'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'status',
    width: '180',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('已创建'), 1: i18n.t('启用'), 2: i18n.t('停用') }
    }
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  }
]

const thresholdDefcolumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'thresholdName',
    headerText: i18n.t('门槛名称'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'fieldName',
    headerText: i18n.t('监控字段')
    // width: "180",
  },
  {
    field: 'formType',
    headerText: i18n.t('门槛类型'),
    // width: "180",
    valueConverter: {
      type: 'map',
      map: {}
    }
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('启用'),
        2: i18n.t('停用'),
        3: i18n.t('待审核')
      }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'isRedLine',
    headerText: i18n.t('是否红线项'),
    valueConverter: {
      type: 'map',
      map: {
        2: '否',
        1: '是'
      }
    }
  },
  {
    field: 'belongDomain',
    headerText: i18n.t('归属领域'),
    valueConverter: {
      type: 'map',
      map: {
        1: '研发',
        2: '质量',
        3: '成本',
        4: '采购'
      }
    }
  },
  {
    field: 'originalValue',
    headerText: i18n.t('门槛原值')
    // width: "180",
  },
  {
    field: 'thresholdProject',
    headerText: i18n.t('门槛项目'),
    // width: "180",
    valueConverter: {
      type: 'map',
      map: {}
    }
  },
  {
    field: 'symbol',
    headerText: i18n.t('操作符'),
    // width: "180",
    valueConverter: {
      type: 'map',
      map: symbolMap
    }
  },
  {
    field: 'defaultValue',
    headerText: i18n.t('默认目标值')
    // width: "180",
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
    // width: "180",
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  }
]

const reviewItemDefcolumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'reviewCode',
    headerText: i18n.t('编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑')
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除')
      }
    ],
    cssClass: ''
  },
  {
    field: 'reviewName',
    headerText: i18n.t('名称')
    // width: "180",
  },
  {
    field: 'reviewStandard',
    headerText: i18n.t('评分规范')
    // width: "180",
  },
  {
    field: 'lowestScore',
    headerText: i18n.t('最低分')
    // width: "180",
  },
  // {
  //   field: "qualifiedScore",
  //   headerText: i18n.t("默认合格线"),
  //   // width: "180",
  //   template: function () {
  //     return {
  //       template: Vue.component("fileTypeOption", {
  //         template: `<div><span>{{symbol}}{{data.qualifiedScore}}</span></div>`,
  //         data() {
  //           return { data: {}, symbol: null };
  //         },
  //         mounted() {
  //           this.symbol = symbolMap[this.data.qualifiedSymbol];
  //         },
  //       }),
  //     };
  //   },
  // },
  {
    field: 'qualifiedSymbol',
    headerText: i18n.t('操作符'),
    valueConverter: {
      type: 'map',
      map: {
        1: '>',
        2: '<',
        3: '>=',
        4: '<=',
        5: '='
      }
    }
  },
  {
    field: 'qualifiedScore',
    headerText: i18n.t('默认目标值')
    // width: "180",
  },
  {
    field: 'highestScore',
    headerText: i18n.t('最高')
    // width: "180",
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
    // width: "180",
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('创建时间')
  }
]

// 资质项定义
const qualificationItemDefcolumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'qualificationCode',
    headerText: i18n.t('资质编码'),
    // width: "180",
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] != 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] != 1
        }
      }
    ]
  },
  {
    field: 'qualificationName',
    headerText: i18n.t('资质名称')
    // width: "180",
  },
  {
    field: 'auditRemark',
    width: '150',
    headerText: i18n.t('评审重点说明')
  },
  {
    field: 'modelName',
    headerText: i18n.t('附件模板'),
    // width: "180",
    cellTools: true
  },
  {
    field: 'qualificationType',
    headerText: i18n.t('资质类型'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: {
        1: i18n.t('三证'),
        2: i18n.t('代理资质'),
        3: i18n.t('专利资质'),
        4: i18n.t('奖项'),
        5: i18n.t('专项')
      }
    }
    // width: "180",
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'businessName',
    headerText: i18n.t('业务类型')
    // width: "180",
  },
  {
    field: 'confirmDeptName',
    headerText: i18n.t('确认部门')
  },
  {
    field: 'confirmUserName',
    headerText: i18n.t('确认人')
  },
  {
    field: 'remindInvalid',
    headerText: i18n.t('失效提前提醒')
    // width: "180",
  },
  {
    field: 'qualificationDimension',
    headerText: i18n.t('所属维度'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: {
        0: i18n.t('集团-供应商'),
        1: i18n.t('集团-供应商-品类'),
        2: i18n.t('公司-供应商'),
        3: i18n.t('公司-供应商-品类')
      }
    }
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      type: 'date'
    }
  },
  {
    field: 'auditRemark',
    width: '150',
    headerText: i18n.t('评审重点说明')
  }
]

const statusDefcolumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'statusCode',
    headerText: i18n.t('编码')
    // width: "180",
  },
  {
    field: 'statusName',
    headerText: i18n.t('名称')
    // width: "180",
  },
  {
    field: 'statusType',
    headerText: i18n.t('状态类型')
    // width: "180",
  },
  {
    field: 'isDefault',
    headerText: i18n.t('默认状态'),
    // width: "180",
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'businessAuthority',
    headerText: i18n.t('业务权限')
    // width: "180",
  },
  {
    field: 'addLinkage',
    headerText: i18n.t('新建时联动')
    // width: "180",
  },
  {
    field: 'relieveLinkage',
    headerText: i18n.t('解除时联动')
    // width: "180",
  },
  {
    field: 'businessRuleExpression',
    headerText: i18n.t('业务规则（预设）'),
    width: '180'
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  }
]

const certificationSceneDefcolumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'sceneCode',
    headerText: i18n.t('编码'),
    // width: "180",
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'sceneName',
    headerText: i18n.t('名称'),
    width: '220'
  },
  {
    field: 'sceneSort',
    headerText: i18n.t('排序值')
    // width: "180",
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    // width: "180",
    valueConverter: {
      type: 'map',
      map: taskTemplateStatusSetting
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'categoryName',
    headerText: i18n.t('适用品类'),
    // width: "180",
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><span>{{categoryName}}</span></div>`,
          data() {
            return { data: {}, categoryName: null }
          },
          mounted() {
            this.categoryName =
              this.data.categoryIds == '-99'
                ? '通用'
                : this.data.categoryName
                ? this.data.categoryName
                : ''
          }
        })
      }
    }
  },
  {
    field: 'appliesStatus',
    headerText: i18n.t('适用供应商状态'),
    width: '180',
    searchOptions: {
      elementType: 'select',
      dataSource: [],
      fields: { text: 'statusName', value: 'statusCode' },
      operator: 'contains'
    },
    template: function () {
      return {
        template: Vue.component('appliesStatusList', {
          template: `<div><span>{{appliesStatusShow}}</span></div>`,
          data() {
            return { data: {}, appliesStatusShow: null, obj: supplierStatus }
          },
          mounted() {
            let arr = JSON.parse(JSON.stringify(this.data.appliesStatusList))
            for (let i = 0; i < arr.length; i++) {
              arr[i] = this.obj[arr[i]]
            }
            this.appliesStatusShow = arr.join('、')
          }
        })
      }
    }
  },
  {
    field: 'orgName',
    headerText: i18n.t('适用组织'),
    // width: "180",
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><span>{{orgName}}</span></div>`,
          data() {
            return { data: {}, orgName: null }
          },
          mounted() {
            this.orgName =
              this.data.orgIds == '-99' ? '通用' : this.data.orgName ? this.data.orgName : ''
          }
        })
      }
    }
  },
  {
    field: 'relyOnAuth',
    headerText: i18n.t('依赖认证需求'),
    // width: "180",
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
    // width: "180",
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  }
]

const levelDefColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'labelCode',
    headerText: i18n.t('级别编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'labelName',
    width: '120',
    headerText: i18n.t('级别名称')
  },
  {
    field: 'labelType',
    width: '120',
    headerText: i18n.t('级别标识')
  },
  {
    field: 'status',
    width: '80',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]

const labelColumns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'labelCode',
    headerText: i18n.t('标签编码'),
    width: '180',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),

        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      }
    ],
    cssClass: ''
  },
  {
    field: 'labelName',
    width: '120',
    headerText: i18n.t('标签名称')
  },
  {
    field: 'labelType',
    width: '120',
    headerText: i18n.t('标签标识')
  },
  {
    field: 'status',
    width: '80',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 2,
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),

        visibleCondition: (data) => {
          return data['status'] == 2
        }
      },
      {
        id: 'disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),

        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    width: '150',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]

//=====参数设置配置项=====
export const pageConfig = [
  {
    gridId: '48d9cb65-290d-40d6-9d3d-b06fb53bb3e9',
    title: i18n.t('阶段定义'),
    toolbar: {
      tools: [
        [
          'Add',
          'Delete',
          { id: 'enable', title: i18n.t('启用'), icon: 'icon_table_enable' },
          {
            id: 'disable',
            title: i18n.t('停用'),
            icon: 'icon_table_disable'
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: stageDefcolumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/define/stage/query'
      }
    }
  },
  {
    gridId: '93573f95-ae4b-423d-8aa5-74794ec3fa75',
    title: i18n.t('信息定义'),
    toolbar: {
      tools: [
        // ["Add", "Delete"],
        [],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: infoDefcolumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/field/instance/queryDetail'
      }
    }
  },
  {
    gridId: 'dd9db032-a701-49bd-836b-ae9aebcaed2b',
    title: i18n.t('门槛项定义'),
    toolbar: {
      tools: [
        [
          'Add',
          'Delete',
          {
            id: 'enable',
            title: i18n.t('启用'),
            icon: 'icon_table_enable'
          },
          {
            id: 'disable',
            title: i18n.t('停用'),
            icon: 'icon_table_disable'
          },
          {
            id: 'audit',
            title: i18n.t('查看OA审批'),
            icon: 'icon_solid_editsvg'
          },
          {
            id: 'thresholdDefImport',
            title: i18n.t('导入'),
            icon: 'icon_solid_editsvg'
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: thresholdDefcolumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/threshold/define/query'
      }
    }
  },
  {
    gridId: '238e899c-8e85-4941-9180-5de9a65c6a86',
    title: i18n.t('评审项定义'),
    toolbar: {
      tools: [
        ['Add', 'Delete', { id: 'ImportCate', title: i18n.t('导入'), icon: 'icon_solid_upload' }],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: reviewItemDefcolumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/define/review/query'
      }
    }
  },
  {
    gridId: 'f7d50a23-70ca-47a1-bc10-3c64da475690',
    title: i18n.t('资质项定义'),
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          'Add',
          'Delete',
          { id: 'enable', title: i18n.t('启用'), icon: 'icon_table_enable' },
          {
            id: 'disable',
            title: i18n.t('停用'),
            icon: 'icon_table_disable'
          },
          {
            id: 'qualificationDefinitionImport',
            title: i18n.t('导入'),
            icon: 'icon_table_disable'
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: qualificationItemDefcolumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/qualification/define/queryPage',
        params: {
          hasDynamic: 1
        }
      }
    }
  },
  {
    gridId: 'b6216b4d-3fdf-4895-a550-8f2174d42fae',
    title: i18n.t('状态定义'),
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        // ["Add", "Delete", "Edit"],
        [],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: statusDefcolumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/define/status/query'
      }
    }
  },
  {
    gridId: 'd089c129-b9bc-4f4c-9222-4a32c3829281',
    title: i18n.t('认证场景定义'),
    toolbar: {
      tools: [
        [
          'Add',
          'Delete',
          {
            id: 'enable',
            title: i18n.t('启用'),
            icon: 'icon_table_enable'
            // permission: ["O_02_0068"],
          },
          {
            id: 'disable',
            title: i18n.t('停用'),
            icon: 'icon_table_disable'
            // permission: ["O_02_0034"],
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: certificationSceneDefcolumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/scene/define/pageQuery'
      }
    }
  },
  {
    gridId: '069649d0-7797-4f32-86b3-3992608d596d',
    title: i18n.t('级别定义'),
    permission: ['O_02_0027'],
    toolbar: {
      tools: [
        [
          {
            id: 'Add',
            icon: 'icon_solid_Createorder',
            title: i18n.t('新增'),
            permission: ['O_02_0056']
          },
          {
            id: 'Delete',
            icon: 'icon_solid_Delete',
            title: i18n.t('删除'),
            permission: ['O_02_0057']
          },
          {
            id: 'enable',
            title: i18n.t('启用'),
            icon: 'icon_table_enable',
            permission: ['O_02_0058']
          },
          {
            id: 'disable',
            title: i18n.t('停用'),
            icon: 'icon_table_disable',
            permission: ['O_02_0028']
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: levelDefColumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/label/define/list',
        rules: [
          //配置rules，数组格式，请求入参时，追加到rules中
          {
            label: i18n.t('状态'),
            field: 'labelDefineType',
            type: 'number',
            operator: 'equal',
            value: 1
          }
        ]
      }
    }
  },
  {
    gridId: '07b20dd4-c88e-49ce-a682-a47237df4e10',
    useToolTemplate: false,
    title: i18n.t('标签定义'),
    permission: ['O_02_0033'],
    toolbar: {
      tools: [
        [
          {
            id: 'Add',
            title: i18n.t('新增'),
            icon: 'icon_solid_Createproject'
            // permission: ["O_02_0066"],
          },
          {
            id: 'Delete',
            title: i18n.t('删除'),
            icon: 'icon_solid_Delete1'
            // permission: ["O_02_0067"],
          },
          {
            id: 'enable',
            title: i18n.t('启用'),
            icon: 'icon_table_enable'
            // permission: ["O_02_0068"],
          },
          {
            id: 'disable',
            title: i18n.t('停用'),
            icon: 'icon_table_disable'
            // permission: ["O_02_0034"],
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: labelColumns,
      asyncConfig: {
        url: '/supplier/tenant/buyer/label/define/list',
        rules: [
          //配置rules，数组格式，请求入参时，追加到rules中
          {
            label: i18n.t('状态'),
            field: 'labelDefineType',
            type: 'number',
            operator: 'equal',
            value: 3
          }
        ]
      }
    }
  }
]
