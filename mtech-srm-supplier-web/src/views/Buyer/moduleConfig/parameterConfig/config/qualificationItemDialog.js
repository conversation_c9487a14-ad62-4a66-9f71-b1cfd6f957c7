import { i18n } from '@/main.js'
// =====对话框表格数据源=====
import Vue from 'vue'
export let dialogGridDataSource = [
  {
    taskName: i18n.t('是否显示'),
    certificateNo: false,
    effectiveDate: false,
    expirationDate: false,
    getDate: false,
    issuingAuthority: false,
    auditDate: false,
    auditFindings: false,
    file: false,
    agentProducts: false,
    agentBrand: false,
    agentFactory: false,
    contacts: false,
    mobileNo: false,
    phoneNo: false,
    remark: false
  },
  {
    taskName: i18n.t('是否必填'),
    certificateNo: false,
    effectiveDate: false,
    expirationDate: false,
    getDate: false,
    issuingAuthority: false,
    auditDate: false,
    auditFindings: false,
    file: false,
    agentProducts: false,
    agentBrand: false,
    agentFactory: false,
    contacts: false,
    mobileNo: false,
    phoneNo: false,
    remark: false
  }
]
// =====对话框表格列=====
//对话框列参数封装 TODO 做组件引用
export let dialogGridParams = (params) => {
  return {
    template: Vue.component('actionOption', {
      template: `<mt-checkbox v-model="checkedVal" :disabled="disabled" @change="certificateNochange"></mt-checkbox>`,
      data() {
        return {
          data: {},
          checkedVal: false,
          disabled: false
        }
      },
      mounted() {
        dialogGridDataSource.forEach((item) => {
          if (item.taskName == this.data.taskName) {
            this.checkedVal = item[params]
          }
        })
        if (dialogGridDataSource[0].isView) {
          this.disabled = true
        }
      },
      methods: {
        certificateNochange(val) {
          // this.checkedVal = Object.values(this.data[params])
          this.checkedVal = !this.checkedVal
          this.data[params] = val.checked
          this.$parent.$emit('paramsCon', this.data.taskName, params, val.checked)
        }
      }
    })
  }
}
export let dialogGridColumnData = [
  {
    field: 'taskName',
    headerText: ' ',
    textAlign: 'center',
    width: '100'
  },
  {
    field: 'certificateNo',
    headerText: i18n.t('证书编号'),
    textAlign: 'center',
    width: '80',
    template: () => {
      return dialogGridParams('certificateNo')
    }
  },
  {
    field: 'effectiveDate',
    headerText: i18n.t('证书生效日期'),
    width: '80',
    textAlign: 'center',
    template: () => {
      return dialogGridParams('effectiveDate')
    }
  },
  {
    field: 'expirationDate',
    headerText: i18n.t('证书失效日期'),
    width: '80',
    textAlign: 'center',
    template: () => {
      return dialogGridParams('expirationDate')
    }
  },
  {
    field: 'getDate',
    headerText: i18n.t('获取日期'),
    width: '80',
    textAlign: 'center',
    template: () => {
      return dialogGridParams('getDate')
    }
  },
  {
    field: 'issuingAuthority',
    headerText: i18n.t('发证机关'),
    width: '80',
    textAlign: 'center',
    template: () => {
      return dialogGridParams('issuingAuthority')
    }
  },
  {
    field: 'auditDate',
    headerText: i18n.t('审核日期'),
    textAlign: 'center',
    width: '80',
    template: () => {
      return dialogGridParams('auditDate')
    }
  },
  {
    field: 'auditFindings',
    headerText: i18n.t('审核结果'),
    width: '80',
    textAlign: 'center',
    template: () => {
      return dialogGridParams('auditFindings')
    }
  },
  {
    field: 'file',
    headerText: i18n.t('附件'),
    width: '80',
    textAlign: 'center',
    template: () => {
      return dialogGridParams('file')
    }
  },
  {
    field: 'agentProducts',
    headerText: i18n.t('代理产品'),
    textAlign: 'center',
    width: '80',
    template: () => {
      return dialogGridParams('agentProducts')
    }
  },
  {
    field: 'agentBrand',
    headerText: i18n.t('代理品牌'),
    width: '80',
    textAlign: 'center',
    template: () => {
      return dialogGridParams('agentBrand')
    }
  },
  {
    field: 'agentFactory',
    headerText: i18n.t('代理工厂'),
    width: '80',
    textAlign: 'center',
    template: () => {
      return dialogGridParams('agentFactory')
    }
  },
  {
    field: 'contacts',
    headerText: i18n.t('联系人'),
    textAlign: 'center',
    width: '80',
    template: () => {
      return dialogGridParams('contacts')
    }
  },
  {
    field: 'mobileNo',
    headerText: i18n.t('手机号'),
    textAlign: 'center',
    width: '80',
    template: () => {
      return dialogGridParams('mobileNo')
    }
  },
  {
    field: 'phoneNo',
    headerText: i18n.t('电话'),
    textAlign: 'center',
    width: '80',
    template: () => {
      return dialogGridParams('phoneNo')
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    textAlign: 'center',
    width: '80',
    template: () => {
      return dialogGridParams('remark')
    }
  }
]
