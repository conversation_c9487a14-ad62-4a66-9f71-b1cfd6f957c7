// 信息定义
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('信息类型')" label-style="top" prop="infoType">
          <mt-select
            v-model="formInfo.infoType"
            :data-source="typeList"
            :placeholder="$t('请选择信息类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('字段名称')" label-style="top" prop="stageName">
          <mt-input
            v-model="formInfo.stageName"
            :placeholder="$t('请输入阶段名称')"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('管控维度')" label-style="top" prop="accessType">
          <mt-select
            v-model="formInfo.accessType"
            :data-source="typeList"
            :placeholder="$t('请选择管控维度')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('审批')" label-style="top" prop="approve">
          <mt-radio v-model="formInfo.approve" :data-source="radioData"></mt-radio>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import MtRadio from '@mtech-ui/radio'

export default {
  components: { MtRadio },
  data() {
    return {
      radioData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '2'
        }
      ],
      formInfo: {
        stageName: '', // 阶段模板名称
        infoType: null, // 信息类型
        approve: '2',
        status: 1, // 是否启用
        remark: '' //备注
      },
      typeList: [
        { value: 1, text: this.$t('租户（集团）') },
        { value: 2, text: this.$t('组织') }
      ],
      rules: {
        stageName: [
          {
            required: true,
            message: this.$t('请输入字段名称'),
            trigger: 'blur'
          },
          {
            whitespace: true,
            message: this.$t('请输入字段名称'),
            trigger: 'blur'
          }
        ],
        infoType: [
          {
            required: true,
            message: this.$t('请选择信息类型'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.show()
    this.isEdit && this.initData()
  },
  methods: {
    initData() {
      if (this.info && Object.keys(this.info).length) {
        const { id, stageName, infoType, status, remark } = this.info
        this.formInfo = Object.assign({}, this.formInfo, {
          id,
          stageName,
          infoType,
          status,
          remark
        })
      }
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'editStage' : 'addStage'
          this.$API.AccessStage[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
