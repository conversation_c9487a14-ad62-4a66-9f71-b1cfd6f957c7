// 认证场景定义
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('字段名称')" label-style="top" prop="sceneName">
          <mt-input
            v-model="formInfo.sceneName"
            :placeholder="$t('请输入认证场景名称')"
            float-label-type="Never"
            :maxlength="30"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('排序值')" prop="sceneSort" label-style="top">
          <mt-inputNumber
            :show-clear-button="true"
            :show-spin-button="false"
            v-model="formInfo.sceneSort"
            :placeholder="$t('排序值不能重复')"
          ></mt-inputNumber>
        </mt-form-item>
        <div class="form-item-spec">
          <mt-form-item class="form-item" :label="$t('适用组织')" label-style="top" prop="orgFlag">
            <mt-select
              v-model="orgFlag"
              :data-source="orgTypeList"
              width="390"
              :placeholder="$t('请选择')"
              @change="orgFlagChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('请选择组织')"
            label-style="top"
            prop="orgCode"
            v-if="!!fields.dataSource && fields.dataSource.length > 0 && orgFlag == 1"
          >
            <mt-DropDownTree
              :show-check-box="true"
              :placeholder="$t('请选择')"
              v-model="formInfo.orgCode"
              :fields="fields"
              id="multiTreeSelect"
              :allow-multi-selection="true"
              :auto-check="true"
              width="390"
            ></mt-DropDownTree>
          </mt-form-item>
        </div>
        <div class="form-item-spec">
          <mt-form-item
            class="form-item"
            :label="$t('适用品类')"
            label-style="top"
            prop="categoryFlag"
          >
            <mt-select
              v-model="categoryFlag"
              :data-source="categoryTypeList"
              width="390"
              :placeholder="$t('请选择')"
              @change="categoryFlagChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('请选择品类')"
            label-style="top"
            prop="categoryList"
            v-if="categoryFlag == 1"
          >
            <mt-multi-select
              ref="categoryRef"
              v-model="formInfo.categoryList"
              :data-source="categoryList"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :filtering="filteringResource"
              :allow-filtering="true"
              :fields="{ text: 'categoryName', value: 'categoryCode' }"
              width="390"
            ></mt-multi-select>
          </mt-form-item>
        </div>
        <mt-form-item
          class="form-item"
          :label="$t('依赖认证需求')"
          label-style="top"
          prop="relyOnAuth"
        >
          <mt-select
            v-model="formInfo.relyOnAuth"
            :data-source="relyOnAuthList"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('适用供应商状态')"
          label-style="top"
          prop="appliesStatusList"
        >
          <!-- <mt-select
            v-if="appliesStatusList.length > 0 && isStatusShow"
            v-model="formInfo.appliesStatus"
            :data-source="appliesStatusList"
            :placeholder="$t('请选择')"
            width="390"
            :fields="{ text: 'statusName', value: 'id' }"
          ></mt-select> -->
          <mt-multi-select
            v-if="appliesStatusList.length > 0 && isStatusShow"
            v-model="formInfo.appliesStatusList"
            :data-source="appliesStatusList"
            :placeholder="$t('请选择')"
            width="390"
            :fields="{ text: 'statusName', value: 'statusCode' }"
            @change="appliesStatusChange"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item class="form-item full-width" :label="$t('备注')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import { categoryTypeList, orgTypeList } from '../config/index'
import utils from '@/utils/utils'
export default {
  data() {
    return {
      categoryFlag: '0',
      orgFlag: '0',
      categoryTypeList: categoryTypeList,
      orgTypeList: orgTypeList,
      cateEditTimes: 0,
      orgEditTimes: 0,
      statusEditTimes: 0,
      isShow: true,
      isCateShow: true,
      isStatusShow: true,
      relyOnAuthList: [
        {
          value: 1,
          text: this.$t('是（意味着创建本场景引入时必须关联需求，手动或自动）')
        },
        { value: 0, text: this.$t('否') }
      ],
      appliesStatusList: [],
      fields: {
        dataSource: [],
        value: 'orgCode',
        text: 'name',
        child: 'children'
      },
      formInfo: {
        appliesStatusList: [],

        orgCode: ['-99'],
        categoryList: ['-99'],
        rangeDTOList: [],
        relyOnAuth: 0,
        remark: '',
        sceneDesc: '',
        sceneName: '',
        sceneSort: 0
      },
      categoryList: [],
      categoryListCopy: [],
      rules: {
        sceneName: [
          {
            required: true,
            message: this.$t('请输入字段名称'),
            trigger: 'blur'
          },
          {
            whitespace: true,
            message: this.$t('请输入字段名称'),
            trigger: 'blur'
          }
        ],
        sceneSort: [{ required: true, message: this.$t('请输入排序值'), trigger: 'blur' }],
        categoryList: [{ required: true, message: this.$t('请选择品类'), trigger: 'blur' }],
        orgCode: [{ required: true, message: this.$t('请选择组织'), trigger: 'blur' }],
        relyOnAuth: [
          {
            required: true,
            message: this.$t('请选择依赖认证需求'),
            trigger: 'blur'
          }
        ],
        appliesStatusList: [
          {
            required: true,
            message: this.$t('请选择适用供应商状态'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.show()
    this.filteringResource = utils.debounce(this.filteringResource, 500)
  },
  methods: {
    appliesStatusChange(e) {
      if (e.value) {
        if (e.value.indexOf('-99') != -1 && e.value.length > 1) {
          this.$toast({
            content: `${this.$t('通用')}与其它状态不能复选！`,
            type: 'error'
          })
        }
      }
    },
    async initData() {
      let detailData, _orgCode, _category
      await this.getStatedLimitTree()
      await this.getCategoryList('')
      await this.getStatusDef()
      if (this.isEdit) {
        await this.$API.ModuleConfig.querySceneDetail(this.info.id).then((res) => {
          detailData = res.data
        })
        if (detailData.rangeDTOList != null && detailData.rangeDTOList.length > 0) {
          _orgCode = detailData.rangeDTOList
            .filter((item) => {
              return item.rangeType == 2
            })
            .map((e) => e.orgCode)
          _category = detailData.rangeDTOList
            .filter((item) => {
              return item.rangeType == 1
            })
            .map((i) => i.categoryCode)
          this.categoryFlag = _category[0] == '-99' ? '0' : '1'
          this.orgFlag = _orgCode[0] == '-99' ? '0' : '1'
        }

        this.$nextTick(() => {
          this.formInfo = {
            ...this.formInfo,
            ...detailData,
            orgCode: _orgCode,
            categoryList: _category
          }
        })
      }
    },
    async filteringResource(e) {
      await this.getCategoryList(e.text)
      e.updateData(this.categoryListCopy)
      this.$forceUpdate()
    },
    async getCategoryList(val) {
      await this.$API.supplierInvitation
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'statusId',
              type: 'int',
              operator: 'equal',
              value: 1
            },
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          this.categoryListCopy = res.data.records
          this.categoryListCopy.forEach((item) => {
            let bol = this.categoryList.some((e) => {
              return item.categoryCode == e.categoryCode
            })
            if (!bol) {
              this.categoryList.push(item)
            }
          })
        })
    },
    getStatedLimitTree() {
      let query = 'ORG02'

      this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        // orgType: "ORG001ADM",
        orgType: 'ORG001PRO'
      }).then((res) => {
        let filterObj = function (item) {
          if (item.orgLeveLTypeCode == query) return true

          if (Object.prototype.hasOwnProperty.call(item, 'children')) {
            item.children = item.children.filter(function (child) {
              if (child.orgLeveLTypeCode == query) {
                return child.orgLeveLTypeCode == query
              } else if (Object.prototype.hasOwnProperty.call(child, 'children')) {
                return filterObj(child)
              }
            })
            if (item.children.length > 0) {
              return true
            }
          } else {
            return item.orgLeveLTypeCode == query
          }
        }
        let filter = res.data.filter(function (item) {
          return filterObj(item)
        })
        // filter.unshift({ name: this.$t("通用"), orgCode: -99 });
        this.fields.dataSource = cloneDeep(filter)
      })
    },

    getStatusDef() {
      this.$API.ModuleConfig.getStatusDef({
        page: { current: 1, size: 10000 }
      }).then((res) => {
        let _tempList = cloneDeep(res.data.records)
        _tempList.unshift({
          statusName: this.$t('通用'),
          statusCode: '-99'
        })
        this.isStatusShow = false
        this.appliesStatusList = _tempList
        setTimeout(() => {
          this.isStatusShow = true
        }, 50)
      })
    },
    categoryFlagChange(e) {
      if (this.cateEditTimes == 0 && this.isEdit) {
        ++this.cateEditTimes
        return
      }
      this.categoryFlag = e.value
      if (e.value === '1') {
        this.formInfo.categoryList.length = 0
      } else {
        this.formInfo.categoryList.length = 0
        this.formInfo.categoryList.push('-99')
      }
    },
    orgFlagChange(e) {
      if (this.orgEditTimes == 0 && this.isEdit) {
        ++this.orgEditTimes
        return
      }
      this.orgFlag = e.value
      if (e.value === '1') {
        this.formInfo.orgCode.length = 0
      } else {
        this.formInfo.orgCode.length = 0
        this.formInfo.orgCode.push('-99')
      }
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (
            this.formInfo.appliesStatusList.indexOf('-99') != -1 &&
            this.formInfo.appliesStatusList.length > 1
          ) {
            this.$toast({
              content: `${this.$t('通用')}与其它状态不能复选！`,
              type: 'error'
            })
            return
          }
          const methodName = this.isEdit ? 'editSceneDef' : 'addSceneDef'

          let temp = []
          if (this.categoryFlag == 0) {
            temp.push({
              categoryCode: '-99',
              categoryId: -99,
              categoryName: this.$t('通用'),
              rangeType: '1',
              sceneDefineId: 0,
              tenantId: 0
            })
          } else {
            this.formInfo.categoryList.forEach((element) => {
              let test = this.categoryList.find((e) => e.categoryCode == element)
              temp.push({
                categoryCode: test.categoryCode,
                categoryId: test.id,
                categoryName: test.categoryName,
                rangeType: '1'
              })
            })
          }
          let tempdata = []
          if (this.orgFlag == 0) {
            tempdata = [
              {
                orgCode: '-99',
                orgDimension: '0',
                orgName: this.$t('通用'),
                orgId: -99,
                rangeType: '2',
                sceneDefineId: 0,
                tenantId: 0
              }
            ]
          } else {
            tempdata = this.treeToArray(this.fields.dataSource)
          }
          this.$API.ModuleConfig[methodName]({
            ...this.formInfo,
            rangeDTOList: temp.concat(tempdata)
          })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    treeToArray(tree) {
      let res = []
      for (const item of tree) {
        const { children, ...i } = item
        if (children && children.length) {
          res = res.concat(this.treeToArray(children))
        }
        if (i.orgLeveLTypeCode == 'ORG02' && this.formInfo.orgCode.indexOf(i.orgCode) > -1) {
          res.push({
            orgCode: i.orgCode,
            orgDimension: i.orgLeveLTypeCode,
            orgId: i.id,
            orgName: i.name,
            rangeType: '2'
          })
        }
      }
      return res
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
  }
}
/deep/ .full-width {
  width: 100% !important;
}
/deep/.mt-input-number input {
  width: 100%;
}
/deep/ .form-item-spec {
  width: 864px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
</style>
