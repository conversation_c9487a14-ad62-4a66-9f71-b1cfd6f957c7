<template>
  <!-- 资质项自定义 -->
  <mt-dialog ref="thresholdItemDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('资质名称')"
          label-style="top"
          prop="qualificationName"
        >
          <mt-input
            class="f-1"
            v-model="formInfo.qualificationName"
            :placeholder="$t('请输入资质名称')"
            float-label-type="Never"
            :maxlength="200"
            :disabled="isView"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('失效前提醒（天）')"
          label-style="top"
          prop="remindInvalid"
        >
          <mt-inputNumber
            id="mtInputNumber"
            v-model.number="formInfo.remindInvalid"
            :disabled="isView"
            class="number-item"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('业务类型')"
          label-style="top"
          prop="businessType"
        >
          <mt-select
            v-model="formInfo.businessType"
            :data-source="qualificationBizTypeSource"
            :placeholder="$t('请选择业务类型')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :disabled="isView"
            @change="qualificationBizTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('所属维度')"
          label-style="top"
          prop="qualificationDimension"
        >
          <mt-select
            v-model="formInfo.qualificationDimension"
            :data-source="qualificationDimensionSource"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :disabled="isView"
            :placeholder="$t('请选择所属维度')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('资质类型')"
          label-style="top"
          prop="qualificationType"
        >
          <mt-select
            v-model="formInfo.qualificationType"
            :data-source="qualificationTypeSource"
            :placeholder="$t('请选择资质类型')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :disabled="isView"
          ></mt-select>
        </mt-form-item>

        <!-- <mt-form-item
          class="form-item"
          :label="$t('确认部门')"
          label-style="top"
          prop="confirmDeptName"
        >
          <div class="flex">
            <mt-input
              class="f-1"
              :disabled="true"
              v-model="formInfo.confirmDeptName"
              :placeholder="$t('请选择部门')"
              float-label-type="Never"
              :maxlength="30"
              @change="changeConfirmDeptName"
            ></mt-input>
            <mt-button class="mgn-left-10" :disabled="isView" @click="selectDepartmentBtn">{{
              $t('选择部门')
            }}</mt-button>
          </div>
        </mt-form-item> -->
        <mt-form-item class="form-item" :label="$t('人员')" label-style="top" prop="confirmUserId">
          <!-- <mt-select
            v-if="!isView"
            v-model="formInfo.confirmUserId"
            :data-source="confirmUserSelect"
            :allow-filtering="true"
            :fields="{ text: 'employeeName', value: 'id' }"
            :filtering="inputbaseMeasureConfirmUser"
            :disabled="isView"
            :show-clear-button="true"
            @change="changeEmployee"
            :placeholder="$t('请选择人员')"
          ></mt-select> -->
          <mt-select
            v-if="!isView"
            v-model="formInfo.confirmUserId"
            id="dropDownTreeCom"
            style="width: 100%"
            :fields="{ text: 'ownerName', value: 'employeeId' }"
            :data-source="ownerList"
            :allow-filtering="true"
            :filtering="getOwnerList"
            filter-type="Contains"
            :placeholder="$t('负责人员')"
            :disabled="isView"
            :filter-bar-placeholder="$t('请输入用户名称进行搜索')"
            @select="selectOwner"
            :no-records-template="noRecordsTemplate"
          ></mt-select>
          <mt-input v-else v-model="formInfo.confirmUserName" :disabled="isView"></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('附件模板')" prop="modelName" label-style="top">
          <div class="flex">
            <mt-input
              class="f-1"
              :disabled="true"
              v-model="formInfo.modelName"
              :placeholder="$t('请上传附件模板')"
              float-label-type="Never"
              :maxlength="30"
            ></mt-input>
            <input
              ref="file"
              type="file"
              id="btn_file"
              style="display: none"
              :name="fileName"
              @change="changeInputBtn"
            />
            <mt-button class="mgn-left-10" :disabled="isView" @click="browseBtn">{{
              $t('上传文件')
            }}</mt-button>
          </div>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('评审重点说明')"
          label-style="top"
          prop="auditRemark"
        >
          <mt-input
            class="f-1"
            :multiline="true"
            v-model="formInfo.auditRemark"
            :placeholder="$t('请输入评审重点说明')"
            float-label-type="Never"
            :maxlength="500"
            :disabled="isView"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item">
          <div style="margin-top: 20px; color: #f44336; font-size: 12px">
            {{
              $t(
                '注：文件大小不超过50M，文件格式支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'
              )
            }}
          </div>
        </mt-form-item>
        <div class="demo-block">
          <mt-DataGrid
            :data-source="dialogGridDataSource"
            :column-data="dialogGridColumnData"
            ref="dataGrid"
            @paramsCon="paramsCon"
            @rowSelected="getSelectedRecords"
          ></mt-DataGrid>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
// import { dataColumn } from "../../../analysisModule/performanceAnalysis/archivesDetail/config";
import { dialogGridDataSource, dialogGridColumnData } from '../config/qualificationItemDialog'
import { cloneDeep } from 'lodash'
import { utils } from '@mtech-common/utils'
let fileData = null
export default {
  data() {
    return {
      dialogGridDataSource,
      dialogGridColumnData,
      sourceLabel: this.$t('选择资质'),

      //=====弹出框数据=====
      qualificationBizTypeSource: [
        // {
        //   itemName: this.$t("质量"),
        //   itemCode: "quality",
        // },
        // {
        //   itemName: this.$t("财务"),
        //   itemCode: "finance",
        // },
        // {
        //   itemName: this.$t("设备"),
        //   itemCode: "device",
        // },
        // {
        //   itemName: this.$t("人员"),
        //   itemCode: "staff",
        // },
        // {
        //   itemName: this.$t("其他"),
        //   itemCode: "other",
        // },
      ], //业务类型==选择框数据
      qualificationDimensionSource: [
        {
          itemName: this.$t('集团-供应商'),
          itemCode: '0'
        },
        {
          itemName: this.$t('集团-供应商-品类'),
          itemCode: '1'
        },
        {
          itemName: this.$t('公司-供应商'),
          itemCode: '2'
        },
        {
          itemName: this.$t('公司-供应商-品类'),
          itemCode: '3'
        }
      ], //所属维度==选择框数据
      qualificationTypeSource: [
        {
          itemName: this.$t('三证'),
          itemCode: '1'
        },
        {
          itemName: this.$t('代理资质'),
          itemCode: '2'
        },
        {
          itemName: this.$t('专利资质'),
          itemCode: '3'
        },
        {
          itemName: this.$t('奖项'),
          itemCode: '4'
        },
        {
          itemName: this.$t('其他'),
          itemCode: '5'
        }
      ], //资质类型==选择框数据
      ruleConditionObjectSource: [{}, {}], //确认部门==选择框数据
      fields: {
        //确认部门==选择框数据
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children',
        key: 1
      },
      //人员
      confirmUserSelect: [],
      ownerList: [],
      noRecordsTemplate: this.$t('请输入用户名称进行搜索'),
      //=====表单得到参数=====
      formInfo: {
        qualificationTemplateCode: '', //资质模板编码
        qualificationName: '', //资质名称
        remindInvalid: '', //失效提前提醒(天)
        businessType: '', //业务类型
        businessName: '', //业务类型名称
        qualificationDimension: '', //所属维度==(资质维度，0-集团-供应商，1-集团-供应商-品类，2-公司-供应商，3-公司-供应商-品类)
        qualificationType: '', //资质类型==(字典表code)
        confirmUserId: '', //人员
        confirmDept: '', //确认部门==编码
        confirmDeptName: '', //确认部门==名称
        modelName: '', //模板文件名称
        modelUrl: '', //模板文件的下载地址
        defineConfigList: [], //动态字段
        auditRemark: '' //评审重点说明
      },
      formInfoUpdata: {
        //编辑更新
        qualificationCode: '',
        qualificationName: '',
        qualificationType: '',
        confirmUserId: '', //人员id
        confirmUserName: '',
        confirmDeptId: '',
        qualificationDimension: '',
        remindInvalid: '',
        businessType: '',
        modelName: '',
        modelUrl: '',
        confirmDept: '',
        confirmDeptName: '',
        businessName: '',
        status: '',
        tenantId: 0,
        auditRemark: '',
        defineConfigList: [
          {
            fieldCode: '',
            fieldName: '',
            showFlag: false,
            required: false,
            qualificationCode: '',
            id: 0
          }
        ]
      },
      formTypeList: [],
      rules: {
        qualificationName: [
          {
            required: true,
            message: this.$t('请输入资质名称'),
            trigger: 'blur'
          }
        ],
        businessType: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        qualificationDimension: [
          {
            required: true,
            message: this.$t('请选择所属维度'),
            trigger: 'blur'
          }
        ],
        qualificationType: [
          {
            required: true,
            message: this.$t('请选择资质类型'),
            trigger: 'blur'
          }
        ],
        confirmDeptName: [
          {
            required: true,
            message: this.$t('请选择确认部门'),
            trigger: 'blur'
          }
        ],
        // confirmUserId: [
        //   {
        //     required: true,
        //     message: this.$t('请选择人员'),
        //     trigger: 'blur'
        //   }
        // ],
        auditRemark: [
          {
            required: true,
            message: this.$t('请输入评审重点说明'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      uploadInfo: {}, // 上传后信息
      fileName: ''
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isView() {
      return this.modalData.isView
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.getOwnerList = utils.debounce(this.getOwnerList, 300)
    this.inputbaseMeasureConfirmUser = utils.debounce(this.inputbaseMeasureConfirmUser, 1000)
    this.getDict()
    this.queryDictQualificationBizTypeInterface()
    this.show()
    if (
      (this.header == this.$t('编辑资质项定义') || this.header == this.$t('资质项定义')) &&
      this.formInfo
    ) {
      this.formInfo = this.info
      if (this.info.defineConfigList)
        this.info.defineConfigList.map((i) => {
          dialogGridDataSource[0][i.fieldCode] = i.showFlag ? true : false
          dialogGridDataSource[1][i.fieldCode] = i.required ? true : false
          dialogGridDataSource[0]['isView'] = this.isView
          dialogGridDataSource[1]['isView'] = this.isView
        })
      let confirmUserName = this.formInfo.confirmUserName?.split('-')[0]
      this.getOwnerList({ text: confirmUserName })
    }
    if (this.isView) {
      this.buttons = []
      let params = {
        employeeName: this.info.confirmUserName,
        orgId: this.formInfo.confirmDeptId,
        tenantId: '10000'
      }
      this.$API.ModuleConfig.getOrganizationEmployees(params).then((res) => {
        this.confirmUserSelect = res.data.map((item) => {
          item.employeeName = item.employeeName + '-' + item.employeeCode
          return item
        })
        this.formInfo.confirmUserId = this.info.confirmUserId
        this.formInfo.confirmUserName = this.info.confirmUserName
      })
    }
  },
  methods: {
    // 获取人员下拉列表
    getOwnerList(val) {
      let params = {
        fuzzyName: val.text
      }
      this.$API.supplierIndex.currentTenantEmployees(params).then((res) => {
        this.ownerList = []
        if (res.code == 200 && res.data != null) {
          this.ownerList = res.data.map((item) => {
            item.ownerName = item.employeeName + '-' + item.employeeCode
            return item
          })
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },
    // 选择责任人
    selectOwner(e) {
      if (e.itemData === null) {
        this.formInfo.confirmUserId = ''
        this.formInfo.confirmUserName = ''
        this.formInfo.confirmDeptId = ''
        this.formInfo.confirmDept = ''
        this.formInfo.confirmDeptName = ''
      }
      this.formInfo.confirmUserId = e.itemData.employeeId
      this.formInfo.confirmUserName = e.itemData.ownerName
      this.formInfo.confirmDeptId = e.itemData.departmentOrgId
      this.formInfo.confirmDept = e.itemData.departmentOrgCode
      this.formInfo.confirmDeptName = e.itemData.departmentOrgName
    },
    //========【接口】==========
    //弹出框内容==接口
    async getDict() {
      await this.getStatedLimitTree() //公司
    },
    //业务类型==字典接口
    queryDictQualificationBizTypeInterface() {
      this.$API.ModuleConfig.queryDict({ dictCode: 'qualificationBizType' }).then((res) => {
        if (res.code === 200 && res.data) {
          res.data.forEach((i) => {
            this.qualificationBizTypeSource.push({
              itemName: i.itemName,
              itemCode: i.itemCode
            })
          })
        }
      })
    },
    // 获取组织树==公司接口
    async getStatedLimitTree() {
      let query = 'ORG02'
      await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        orgType: 'ORG001PRO'
      }).then((res) => {
        let filterObj = function (item) {
          if (item.orgLeveLTypeCode == query) return true
          if (Object.prototype.hasOwnProperty.call(item, 'children')) {
            item.children = item.children.filter(function (child) {
              if (child.orgLeveLTypeCode == query) {
                return child.orgLeveLTypeCode == query
              } else if (Object.prototype.hasOwnProperty.call(child, 'children')) {
                return filterObj(child)
              }
            })
            if (item.children.length > 0) {
              return true
            }
          } else {
            return item.orgLeveLTypeCode == query
          }
        }
        let filter = res.data.filter(function (item) {
          return filterObj(item)
        })

        this.fields.dataSource = cloneDeep(filter)
        this.fields.key = this.randomString()
      })
    },
    randomString(len) {
      len = len || 32
      var $chars =
        'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678' /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
      var maxPos = $chars.length
      var pwd = ''
      for (var i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
      }
      return pwd
    },

    getSelectedRecords() {
      // let Obj = this.$refs.dataGrid.ejsRef.getSelectedRecords()
    },
    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    //业务类型==改变时
    fieldIdChange() {},
    //表体复选框选中时
    paramsCon(data, params, checked) {
      this.dialogGridDataSource.forEach((item) => {
        if (item.taskName == data) {
          item[params] = checked
        }
      })
    },
    paramsFieldCodeShow() {
      if (dialogGridDataSource[0])
        for (let key in dialogGridDataSource[0]) {
          if (key !== 'taskName') dialogGridDataSource[0][key] = false
        }
      if (dialogGridDataSource[1])
        for (let key2 in dialogGridDataSource[1]) {
          if (key2 !== 'taskName') dialogGridDataSource[1][key2] = false
        }
    },
    //保存==按钮
    async confirm() {
      this.formInfo['defineConfigList'] = []
      //动态字段==字典接口
      await this.$API.ModuleConfig.qualificationGetAllDynamicFields().then((res) => {
        if (res.code == 200 && res.data) {
          let { data } = res
          data.map((i) => {
            this.formInfo['defineConfigList'].push({
              fieldCode: i.fieldCode,
              fieldName: i.fieldName,
              showFlag: dialogGridDataSource[0][i.fieldCode] ? 1 : 0,
              required: dialogGridDataSource[1][i.fieldCode] ? 1 : 0
            })
          })
        }
      })
      await this.$refs.formInfo.validate((valid) => {
        if (valid) {
          const methodName = this.isEdit ? 'qualificationDefineUpdate' : 'qualificationDefineAdd'
          console.log('validatevalidate', this.formInfo)
          if (
            this.formInfo.confirmUserId == '0' ||
            this.formInfo.confirmUserId == '' ||
            this.formInfo.confirmUserId === null
          ) {
            ;(this.formInfo.confirmUserId = '0'),
              (this.formInfo.confirmUserName = this.$t('发起人'))
          }
          // 编辑过滤需要的参数
          if (this.isEdit) {
            for (let key in this.formInfoUpdata) {
              this.formInfoUpdata[key] = this.formInfo[key]
            }
          }
          this.$API.ModuleConfig[methodName](this.isEdit ? this.formInfoUpdata : this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
                this.paramsFieldCodeShow()
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        } else {
          this.$toast({ content: this.$t('请输入必填项'), type: 'error' })
        }
      })
    },
    cancel() {
      this.formInfo['defineConfigList'] = []
      this.$emit('cancel-function')
      this.paramsFieldCodeShow()
    },
    //上传文件==按钮
    browseBtn() {
      let fileId = document.getElementById('btn_file')
      fileId.click()
    },
    //选择部门==按钮
    selectDepartmentBtn() {
      this.$dialog({
        modal: () => import('./components/qualificationItemDialogDepartment.vue'),
        data: {
          title: this.$t('新增阶段定义')
        },
        success: (val) => {
          this.confirmDepartment = val
          this.formInfo['confirmDeptName'] = val.department
          this.formInfo['confirmDept'] = val.departmentCode
          this.formInfo['confirmDeptId'] = val.departmentId
          if (this.formInfo.confirmUserId !== '0') {
            this.formInfo.confirmUserId = ''
            this.formInfo.confirmUserName = ''
          }
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    },
    changeInputBtn(data) {
      var btnFile = document.getElementById('btn_file').value.toLowerCase().split('.')
      this.$loading()
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // this.headerFlag = false;
        // 您未选择需要上传的文件
        return
      }
      if (
        !['xls', 'xlsx', 'doc', 'docx', 'pdf', 'ppt', 'pptx', 'png', 'jpg', 'zip', 'rar'].includes(
          btnFile[btnFile.length - 1]
        )
      ) {
        this.$toast({
          content:
            data.msg ||
            this.$t(`上传格式错误,正确格式为:xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar`),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      this.fileName = files[0].name
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('UploadFiles', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      fileData = _data
      this.uploadFile()
    },
    // 上传文件
    uploadFile() {
      this.$refs.file.value = ''
      //useType=1	(1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      this.$API.fileService
        .uploadPrivateFileTypeOne(fileData)
        .then((res) => {
          if (res.code == 200 && res.data) {
            const { data } = res
            this.$hloading()
            this.formInfo.modelName = data.fileName
            this.formInfo.modelUrl = data.id
          } else {
            this.$hloading()
            this.$toast({
              content: res.msg || this.$t(`上传格式错误,正确格式为:xlsx、docx、pptx、pdf`),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
        })
    },

    //====内容事件=====

    //业务类型
    qualificationBizTypeChange(val) {
      this.formInfo.businessName = val.itemData.itemName
    },
    //确认部门
    changeConfirmDeptName() {
      console.log(this.formInfo.confirmDeptId)
      if (!this.formInfo.confirmDeptId) return
      let params = {
        employeeName: '',
        orgId: this.formInfo.confirmDeptId,
        tenantId: '10000'
      }
      this.$API.ModuleConfig.getOrganizationEmployees(params).then((res) => {
        this.confirmUserSelect = res.data.map((item) => {
          item.employeeName = item.employeeName + '-' + item.employeeCode
          return item
        })
      })
    },
    //监听人员
    changeEmployee(e) {
      if (e.itemData === null) {
        this.formInfo.confirmUserId = ''
        this.formInfo.confirmUserName = ''
      }
      this.formInfo.confirmUserId = e.itemData.id
      this.formInfo.confirmUserName = e.itemData.employeeName
    },
    //模糊查询人员
    inputbaseMeasureConfirmUser(val) {
      let params = {
        employeeName: val.text || '',
        orgId: this.formInfo.confirmDeptId,
        tenantId: '10000'
      }
      this.$API.ModuleConfig.getOrganizationEmployees(params).then((res) => {
        this.confirmUserSelect = res.data.map((item) => {
          item.employeeName = item.employeeName + '-' + item.employeeCode
          return item
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
  .demo-block {
    width: 100%;
  }
  //当前页==通用样式
  .mgn-left-10 {
    margin-left: 10px;
  }
  .flex {
    display: flex;
  }
  .f-1 {
    flex: 1;
  }
}
/deep/ .mt-input-number {
  width: 100%;
}
/deep/ .mt-input-number .input--wrap {
  width: 100%;
}
/deep/ #mtInputNumber {
  width: 100% !important;
}
</style>
