// 评审项定义
<template>
  <mt-dialog ref="thresholdItemDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item
          class="form-item"
          :label="$t('评审项名称')"
          label-style="top"
          prop="reviewName"
        >
          <mt-input
            v-model="formInfo.reviewName"
            :placeholder="$t('请输入评审项名称')"
            float-label-type="Never"
            :maxlength="400"
            width="390"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('最低分')" prop="lowestScore" label-style="top">
          <mt-input
            :max-length="5"
            :max="1000"
            type="number"
            :show-clear-button="true"
            :show-spin-button="false"
            v-model="formInfo.lowestScore"
            oninput="if(value>1000){value=1000}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
            height="40"
            :placeholder="$t('非负整数')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('最高分')" prop="highestScore" label-style="top">
          <mt-input
            :show-clear-button="true"
            :show-spin-button="false"
            v-model="formInfo.highestScore"
            :placeholder="$t('非负整数，大于等于最低分')"
            :max-length="5"
            :max="1000"
            type="number"
            oninput="if(value>1000){value=1000}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
            height="40"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('合格线')"
          label-style="top"
          prop="symbolAndQualified"
        >
          <mt-select
            v-model="formInfo.qualifiedSymbol"
            :disabled="qualifiedSymbolDisabled"
            :data-source="symbolList"
            :placeholder="$t('请选择')"
            width="165"
            class="qualified"
            @change="symbolChange"
          ></mt-select>
          <mt-input
            class="qualified-score"
            :show-clear-button="true"
            :show-spin-button="false"
            v-model="formInfo.qualifiedScore"
            :disabled="qualifiedSymbolDisabled"
            :placeholder="$t('非负整数')"
            width="190"
            @input="qualifiedScoreChange"
            oninput="if(value>1000000000000000){value=100000000000000}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          class="form-item full-width"
          :label="$t('评分规范')"
          label-style="top"
          prop="reviewStandard"
        >
          <mt-input
            v-model="formInfo.reviewStandard"
            :multiline="true"
            :rows="2"
            maxlength="800"
            float-label-type="Never"
            :placeholder="$t('字数不超过800字')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item full-width" :label="$t('备注')" label-style="top">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      symbolList: [
        {
          text: '>',
          value: '1'
        },
        {
          text: '<',
          value: '2'
        },
        {
          text: '≥',
          value: '3'
        },
        {
          text: '≤',
          value: '4'
        },
        {
          text: '=',
          value: '5'
        }
      ],
      formInfo: {
        highestScore: null,
        lowestScore: null,
        qualifiedScore: null,
        qualifiedSymbol: null,
        remark: '',
        reviewName: '',
        reviewStandard: null,
        symbolAndQualified: null
      },

      rules: {
        reviewName: [
          {
            required: true,
            message: this.$t('请输入评审项名称'),
            trigger: 'blur'
          },
          {
            whitespace: true,
            message: this.$t('请输入评审项名称'),
            trigger: 'blur'
          }
        ],
        lowestScore: [{ required: true, message: this.$t('请输入最低分'), trigger: 'blur' }],
        highestScore: [{ required: true, message: this.$t('请输入最高分'), trigger: 'blur' }],
        symbolAndQualified: [{ required: true, message: this.$t('请输入合格线'), trigger: 'blur' }],
        reviewStandard: [
          {
            required: true,
            message: this.$t('请输入评分规范'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    qualifiedSymbolDisabled() {
      let disabled = false
      /* 模块设置-》参数设置-》评审项定义时，最高分大于等于最低分。当最高分与最低分相等时，合格线就只能等于，
      并且合格线分数就不需要再填写了 直接就是前置条件的分数（最低分）即可 */
      if (
        this.formInfo.highestScore &&
        this.formInfo.highestScore != '' &&
        this.formInfo.lowestScore &&
        this.formInfo.lowestScore != '' &&
        this.formInfo.highestScore == this.formInfo.lowestScore
      ) {
        disabled = true
        this.$set(this.formInfo, 'qualifiedSymbol', '5')
        this.$set(this.formInfo, 'qualifiedScore', this.formInfo.lowestScore)
      }
      return disabled
    }
  },
  mounted() {
    this.show()
    this.isEdit && this.initData()
  },
  methods: {
    initData() {
      if (this.info && Object.keys(this.info).length) {
        this.formInfo = { ...this.info }
      }
    },
    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    symbolChange(e) {
      if (e.value != null && this.formInfo.qualifiedScore != null) {
        this.formInfo.symbolAndQualified = true
      } else {
        this.formInfo.symbolAndQualified = null
      }
    },
    qualifiedScoreChange(e) {
      if (e != null && this.formInfo.qualifiedSymbol != null) {
        this.formInfo.symbolAndQualified = true
      } else {
        this.formInfo.symbolAndQualified = null
      }
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (
            this.formInfo.highestScore < 0 ||
            this.formInfo.lowestScore < 0 ||
            this.formInfo.qualifiedScore < 0
          ) {
            this.$toast({
              content: this.$t('请输入非负整数'),
              type: 'warning'
            })
            return
          }
          if (Number(this.formInfo.highestScore) < Number(this.formInfo.lowestScore)) {
            this.$toast({
              content: this.$t('最低分大于最高分'),
              type: 'warning'
            })
            return
          }

          // 编辑时先请求接口判断该资质项定义有没有被模板引用，如果引用了，加个二次弹框确认
          if (this.isEdit) {
            this.$API.ModuleConfig.adoptedReviewDef({
              reviewId: this.formInfo.reviewId
            })
              .then((res) => {
                if (res.code == 200) {
                  if (res.data) {
                    this.$dialog({
                      data: {
                        title: this.$t('提示'),
                        message: this.$t(`该评审项定义的模板会发生相应的改变，确认提交保存吗？`)
                      },
                      success: () => {
                        this.requestConfirmSave()
                      }
                    })
                  } else {
                    this.requestConfirmSave()
                  }
                }
              })
              .catch((err) => {
                this.$toast({
                  content: err.msg || this.$t('系统异常'),
                  type: 'error'
                })
              })
          } else {
            this.requestConfirmSave()
          }
        }
      })
    },
    requestConfirmSave() {
      this.formInfo.highestScore = Number(this.formInfo.highestScore)
      this.formInfo.lowestScore = Number(this.formInfo.lowestScore)
      this.formInfo.qualifiedScore = Number(this.formInfo.qualifiedScore)
      // 判断是否编辑修改接口
      const methodName = this.isEdit ? 'editReviewDef' : 'addReviewDef'
      this.$API.ModuleConfig[methodName](this.formInfo)
        .then((res) => {
          if (res.code == 200) {
            const contentName = this.isEdit
              ? res.data !== 'success'
                ? res.data
                : this.$t('操作成功')
              : this.$t('操作成功') //"该评审项定义的模板会发生相应的改变"

            this.$toast({ content: contentName, type: 'success' })
            this.$emit('confirm-function')
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('系统异常'),
            type: 'error'
          })
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
  }
}
/deep/ .qualified {
  float: left;
  margin-right: 10px;
}
/deep/ .full-width {
  width: 100% !important;
}
/deep/.mt-input-number input {
  width: 100%;
}
/deep/.qualified-score {
  float: left;
  margin-left: 23px;
}
</style>
