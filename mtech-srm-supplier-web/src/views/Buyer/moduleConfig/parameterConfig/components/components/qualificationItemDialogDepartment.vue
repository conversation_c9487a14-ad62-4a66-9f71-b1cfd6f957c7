<template>
  <!-- 资质项自定义 -->
  <mt-dialog
    ref="thresholdItemDialog"
    :buttons="buttons"
    size="small"
    :header="$t('选择部门')"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo">
        <mt-form-item :label="$t('组织')" label-style="top" prop="organizationId">
          <mt-DropDownTree
            :placeholder="$t('请选择组织')"
            v-model="formInfo.organizationId"
            :fields="fields"
            :key="fields.key"
            @input="organizationIdChange"
            id="orgTreeSelect"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item :label="$t('部门')" label-style="top" prop="departmentId">
          <!-- <mt-select
            v-model="formInfo.departmentId"
            :data-source="departmentSource"
            :placeholder="$t('请选择部门')"
            :fields="{ text:'name', value:'id'} "
            @change="departmentChange"
          ></mt-select> -->
          <mt-DropDownTree
            :placeholder="$t('请选择部门')"
            v-model="formInfo.departmentId"
            :fields="fieldsDepartment"
            :key="fieldsDepartment.key"
            @input="departmentChange"
            id="departmentTreeSelect"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    }
  },
  data() {
    return {
      formInfo: {
        organizationId: '', //公司ID
        department: '', //部门
        departmentId: [] //部门Id
      },
      fields: {
        //公司数据
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children',
        key: 1
      },
      departmentSource: [], //公司数据
      fieldsDepartment: {
        dataSource: [],
        key: 2,
        value: 'id',
        text: 'name',
        child: 'children'
      },
      rules: {
        defaultValue: [
          {
            required: true,
            message: this.$t('请输入默认目标值'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.getStatedLimitTree() //公司
    this.show()
  },
  methods: {
    // 获取组织树==公司接口
    async getStatedLimitTree() {
      let query = 'ORG02'
      await this.$API.ModuleConfig.getStatedLimitTree({
        orgLevelCode: query,
        orgType: 'ORG001ADM' //ORG001ADM TCL用，勿动
      }).then((res) => {
        let filterObj = function (item) {
          if (item.orgLeveLTypeCode == query) return true
          if (Object.prototype.hasOwnProperty.call(item, 'children')) {
            item.children = item.children.filter(function (child) {
              if (child.orgLeveLTypeCode == query) {
                return child.orgLeveLTypeCode == query
              } else if (Object.prototype.hasOwnProperty.call(child, 'children')) {
                return filterObj(child)
              }
            })
            if (item.children.length > 0) {
              return true
            }
          } else {
            return item.orgLeveLTypeCode == query
          }
        }
        let filter = res.data.filter(function (item) {
          return filterObj(item)
        })

        this.fields.dataSource = cloneDeep(filter)
        this.fields.key = this.randomString()
      })
    },
    randomString(len) {
      len = len || 32
      var $chars =
        'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678' /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
      var maxPos = $chars.length
      var pwd = ''
      for (var i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
      }
      return pwd
    },

    //====弹出框==监听====
    //组织==监听
    organizationIdChange(e) {
      ;(this.fieldsDepartment = {
        dataSource: [],
        key: this.randomString(),
        value: 'id',
        text: 'name',
        child: 'children'
      }),
        this.$loading()
      //获取公司==公司接口
      this.$API.ModuleConfig.qualificationGetCompanyDepartmentTree({ organizationId: e[0] })
        .then((res) => {
          if (res.code === 200) {
            this.fieldsDepartment.dataSource = res.data
            this.fieldsDepartment.key = this.randomString()
            this.$hloading()
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    //部门==监听
    departmentChange(e) {
      this.formInfo['department'] = ''
      this.formInfo['departmentCode'] = ''
      this.fn(this.fieldsDepartment.dataSource, e[0])
    },
    fn(arr, str) {
      arr.forEach((item) => {
        if (item.id == str) {
          this.formInfo['department'] = item.name
          this.formInfo['departmentCode'] = item.departmentCode
        } else if (Array.isArray(item.children)) {
          this.fn(item.children, str)
        }
      })
    },
    //====弹出框==按钮=====
    //确认
    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    //隐藏
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    //确认==按钮
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          let obj = JSON.parse(JSON.stringify(this.formInfo))
          obj.departmentId = obj.departmentId[0]
          this.$emit('confirm-function', obj)
        }
      })
    },
    //取消==按钮
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
  .demo-block {
    width: 100%;
  }
  .demo-block1 {
    display: flex;
    justify-content: center;
    align-items: center;
    .xx {
      color: #9a9a9a;
      text-align: center;
      font-size: 14px;
    }
  }

  //当前页==通用样式
  .mgn-left-10 {
    margin-left: 10px;
  }
  .flex {
    display: flex;
  }
  .f-1 {
    flex: 1;
  }
}
/deep/ .mt-input-number {
  width: 100%;
}
/deep/ .mt-input-number .input--wrap {
  width: 100%;
}
/deep/ #mtInputNumber {
  width: 100% !important;
}
</style>
