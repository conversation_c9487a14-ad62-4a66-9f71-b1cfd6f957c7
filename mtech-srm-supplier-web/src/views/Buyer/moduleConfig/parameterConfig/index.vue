<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :current-tab="currentTab"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    ></mt-template-page>
  </div>
</template>
<script>
import { pageConfig, supplierStatus } from './config/index'
import utils from '../../../../utils/utils'
export default {
  computed: {
    showType() {
      return this.$route.query.showType
    }
  },
  data() {
    return {
      pageConfig: [...pageConfig],
      currentTab: 0,
      isEdit: false,
      isOAaudit: false
    }
  },
  mounted() {
    this.currentTab = this.showType ? Number(this.showType) : 0
    this.handleSelectTab(this.currentTab)
  },
  methods: {
    handleSelectTab(e) {
      if (e == 2) {
        this.$API.ModuleConfig.queryDict({
          dictCode: 'thresholdType'
        }).then((res) => {
          if (res.code == 200) {
            let _tempMap = {}
            res.data.forEach((e) => {
              _tempMap[e.itemCode] = e.itemName
            })
            this.pageConfig[2].grid.columnData[3].valueConverter.map = _tempMap
          }
        })
        this.$API.ModuleConfig.queryDict({
          dictCode: 'thresholdProjectList'
        }).then((res) => {
          if (res.code == 200) {
            let _tempMap = { 0: '-' }
            res.data.forEach((e) => {
              _tempMap[e.itemCode] = e.itemName
            })
            this.pageConfig[2].grid.columnData[6].valueConverter.map = _tempMap
          }
        })
      } else if (e === 6) {
        this.$API.ModuleConfig.getStatusDef({
          page: { current: 1, size: 10000 }
        }).then((res) => {
          let _tempMap = [
            {
              statusCode: '-99',
              statusName: this.$t('通用')
            },
            ...res.data.records
          ]
          this.pageConfig[6].grid.columnData[6].searchOptions.dataSource = _tempMap
          res.data.records.forEach((item) => {
            supplierStatus[item.statusCode] = item.statusName
          })
        })
      }
    },
    handleClickToolBar(e) {
      // 表格顶部 toolbar
      const _this = this
      const { toolbar, gridRef, tabIndex } = e
      let currentViewData =
        this.$refs.templateRef.getCurrentTabRef().grid.ej2Instances.currentViewData

      if (toolbar.id == 'qualificationDefinitionImport') {
        // 资质项定义： 导入
        this.ImportOnSiteReview()
        return
      }
      if (toolbar.id == 'ImportCate') {
        // 评审项定义： 导入
        this.ImportOnReview()
        return
      }

      if (toolbar.id === 'Add') {
        if (tabIndex === 0) {
          this.$dialog({
            modal: () => import('./components/operatorDialog.vue'),
            data: {
              title: this.$t('新增阶段定义')
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (tabIndex === 1) {
          this.$dialog({
            modal: () => import('./components/infoDialog.vue'),
            data: {
              title: this.$t('新增信息定义')
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (tabIndex === 2) {
          this.$dialog({
            modal: () => import('./components/thresholdItemDialog.vue'),
            data: {
              title: this.$t('新增门槛项定义')
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (tabIndex === 3) {
          this.$dialog({
            modal: () => import('./components/reviewItemDialog.vue'),
            data: {
              title: this.$t('新增评审项定义')
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (tabIndex === 4) {
          this.$dialog({
            modal: () => import('./components/qualificationItemDialog.vue'),
            data: {
              title: this.$t('新增资质项定义')
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
        // else if (tabIndex === 5) {
        //   this.$dialog({
        //     modal: () => import("./components/statusDialog.vue"),
        //     data: {
        //       title: this.$t("新增状态定义"),
        //     },
        //     success: () => {
        //       _this.$refs.templateRef.refreshCurrentGridData();
        //     },
        //   });
        // }
        else if (tabIndex === 6) {
          this.$dialog({
            modal: () => import('./components/certificationSceneDialog.vue'),
            data: {
              title: this.$t('新增认证场景定义')
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (tabIndex === 7) {
          this.$dialog({
            modal: () => import('./components/levelDialog.vue'),
            data: {
              title: this.$t('新增级别定义'),
              labelDefineType: 1,
              currentViewData
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (tabIndex === 8) {
          this.$dialog({
            modal: () => import('./components/levelDialog.vue'),
            data: {
              title: this.$t('新增标签'),
              labelDefineType: 3,
              currentViewData
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      }
      let sltList = gridRef.getMtechGridRecords()

      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add' && toolbar.id != 'audit') {
        this.$toast({ content: this.$t('至少选择一行数据'), type: 'warning' })
        return
      }
      if (toolbar.id == 'Delete') {
        if (tabIndex === 0) {
          let ids = sltList.map((e) => e.id)
          let _status = sltList.map((e) => e.status)
          if (_status.includes(1)) {
            this.$toast({
              content: this.$t('当前有选中数据已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteStageRecord(ids)
        } else if (tabIndex > 6) {
          // S 战略不能删
          if (sltList.some((item) => item.status === 1)) {
            this.$toast({
              content: this.$t('当前存在已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          if (tabIndex === 7 && sltList.some((item) => item.labelType === 'S')) {
            this.$toast({
              content: this.$t('战略级别不能删除！'),
              type: 'warning'
            })
            return
          }
          this.deleteLevelRecord(sltList)
        } else if (tabIndex === 2) {
          let ids = sltList.map((e) => e.id)
          let _status = sltList.map((e) => e.status)
          if (_status.includes('1') || _status.includes('3')) {
            this.$toast({
              content: this.$t('启用或待审核的数据无法删除'),
              type: 'warning'
            })
            return
          }
          this.deleteThresholdRecord(ids)
        } else if (tabIndex === 3) {
          let ids = sltList.map((e) => e.reviewId)
          this.deleteReviewRecord(ids)
        } else if (tabIndex === 4) {
          //资质项定义==删除
          let _status = sltList.map((e) => e.status)
          if (_status.includes('1')) {
            this.$toast({
              content: this.$t('当前存在已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          let ids = sltList.map((e) => e.qualificationCode)
          this.qualificationDefineDel(ids)
        } else if (tabIndex === 6) {
          let ids = sltList.map((e) => e.id)
          this.deleteSceneRecord(ids)
        }
      }

      if (toolbar.id === 'enable' || toolbar.id === 'disable') {
        let toStatus = toolbar.id === 'enable' ? 1 : 2
        if (tabIndex === 0) {
          let ids = sltList.map((e) => e.id)
          let _status = sltList.map((e) => e.status)
          if (_status.indexOf(toStatus) > -1) {
            this.$toast({
              content: `${
                this.$t('已') +
                (toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')) +
                this.$t('的行无法进行该操作')
              }`,
              type: 'warning'
            })
            return
          }
          let params = {
            status: toStatus,
            stageDefineId: ids
          }
          this.updateStageStatus(params)
        } else if (tabIndex === 2) {
          let ids = sltList.map((e) => e.id)
          let _status = sltList.map((e) => e.status)
          let params = { idList: ids, status: toStatus }
          if (_status.indexOf('3') > -1) {
            this.$toast({
              content: this.$t('待审批的数据无法进行该操作'),
              type: 'warning'
            })
            return
          }
          this.editThresholdDefStatus(params)
        } else if (tabIndex === 4) {
          // 资质项定义==停启用
          let qualificationCodes = sltList.map((e) => e.qualificationCode)
          let _status = sltList.map((e) => e.status)
          if (_status.indexOf(toStatus.toString()) > -1) {
            this.$toast({
              content: `${
                this.$t('已') +
                (toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')) +
                this.$t('的行无法进行该操作')
              }`,
              type: 'warning'
            })
            return
          }
          this.updateQualificationDefineStatus(toStatus, qualificationCodes.join(','))
        } else if (tabIndex === 6) {
          let ids = sltList.map((e) => e.id)
          let _status = sltList.map((e) => e.status)
          if (_status.indexOf(toStatus.toString()) > -1) {
            this.$toast({
              content: `${
                this.$t('已') +
                (toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')) +
                this.$t('的行无法进行该操作')
              }`,
              type: 'warning'
            })
            return
          }
          let params = {
            status: toStatus,
            ids: ids
          }
          this.updateSceneStatus(params)
        } else if (tabIndex > 6) {
          let len = sltList.length
          let idList = []
          for (let i = 0; i < len; i++) {
            const { id, status } = sltList[i]
            if (status === toStatus) {
              this.$toast({
                content: `${
                  this.$t('已') +
                  (toolbar.id === 'enable' ? this.$t('启用') : this.$t('停用')) +
                  this.$t('的行无法进行该操作')
                }`,
                type: 'warning'
              })
              return
            }
            idList.push(id)
          }

          let params = {
            status: toStatus,
            idList: idList
          }
          this.updateStatus(params)
        }
      }
      if (toolbar.id === 'audit') {
        this.audit(sltList)
      }
    },
    // (评审项导入)
    ImportOnReview() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'multipartFile',
          importApi: this.$API.ModuleConfig.importReviewFile,
          downloadTemplateApi: this.$API.ModuleConfig.downloadReviewTemplate
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //Excel导入 - 现场审查模板 (资质项)
    ImportOnSiteReview() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'multipartFile',
          importApi: this.$API.ModuleConfig.importParameterFile,
          downloadTemplateApi: this.$API.ModuleConfig.downloadParameterTemplate
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].applyId
      }
      if (sltList[0].bizType == 2) {
        params.businessType = 'thresholdDefine'
      } else {
        params.businessType = ''
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    // 停启用认证场景定义
    updateSceneStatus(params) {
      this.$API.ModuleConfig.updateSceneStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除认证场景定义
    deleteSceneRecord(ids) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选认证场景定义？'),
          confirm: () => _this.$API.ModuleConfig.deleteSceneDef({ ids: ids })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    // 停启用门槛定义
    editThresholdDefStatus(params) {
      this.$API.ModuleConfig.updateThresholdStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteThresholdRecord(ids) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选门槛项？'),
          confirm: () => _this.$API.ModuleConfig.deleteThresholdDef({ idList: ids })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    deleteReviewRecord(ids) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选评审项？'),
          confirm: () => _this.$API.ModuleConfig.deleteReviewDef(ids)
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    // 停启用资质项定义
    updateQualificationDefineStatus(toStatus, qualificationCode) {
      // toStatus 1启用  2停用
      let url =
        toStatus == 1
          ? this.$API.ModuleConfig.qualificationDefineEnable
          : this.$API.ModuleConfig.qualificationDefineDisable
      url(qualificationCode).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //资质项删除
    qualificationDefineDel(qualificationCode) {
      const _this = this
      console.log(qualificationCode, 'qualificationCode')
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选资质项？'),
          confirm: () => _this.$API.ModuleConfig.qualificationDefineDel(qualificationCode)
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    // 更新阶段定义状态
    updateStageStatus(params) {
      this.$API.AccessStage.changeStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 更新级别、标签定义状态
    updateStatus(params) {
      this.$API.GradeConfig.changeStatus(params).then((res) => {
        const { code } = res
        if (code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data, tabIndex } = e
      const { id } = tool || {}
      const { status } = data
      if (id === 'edit' || id === 'delete') {
        if (status === 1) {
          this.$toast({
            content: this.$t('当前已处于启用状态，不可编辑'),
            type: 'warning'
          })
          return
        }
        if (tabIndex === 2) {
          id === 'edit' && this.editThreshold(data)
          id === 'delete' && this.deleteThresholdRecord([data.id])
        }
        if (tabIndex === 3) {
          id === 'edit' && this.editReview(data)
          id === 'delete' && this.deleteReviewRecord([data.reviewId])
        }
        if (tabIndex === 4) {
          console.log(data, 'datadatadata')
          id === 'edit' && this.editQualification(data)
          id === 'delete' && this.qualificationDefineDel(data.qualificationCode)
        }
        if (tabIndex === 6) {
          id === 'edit' && this.editCertifiScene(data)
          id === 'delete' && this.deleteSceneRecord([data.id])
        }
        // 战略级别不能删除！
        if (id === 'delete' && tabIndex === 7 && data.labelType === 'S') {
          this.$toast({
            content: this.$t('战略级别不能删除！'),
            type: 'warning'
          })
          return
        }
        if (tabIndex === 0) {
          id === 'edit' && this.editStage(data)
          id === 'delete' && this.deleteStageRecord([data.id])
        } else if (tabIndex > 6) {
          id === 'edit' && this.editLevel(data)
          id === 'delete' && this.deleteLevelRecord([data])
        }
      } else if (['enable', 'disable'].includes(tool.id)) {
        const toStatus = data.status == 1 ? 2 : 1

        if (tabIndex === 0) {
          let params = {
            status: toStatus,
            stageDefineId: [data.id]
          }
          this.updateStageStatus(params)
        } else if (tabIndex === 2) {
          let params = { idList: [data.id], status: toStatus }
          this.editThresholdDefStatus(params)
        } else if (tabIndex === 4) {
          // 资质项定义==停启用
          this.updateQualificationDefineStatus(toStatus, data.qualificationCode)
        } else if (tabIndex === 6) {
          let params = {
            status: toStatus,
            ids: [data.id]
          }
          this.updateSceneStatus(params)
        } else if (tabIndex > 6) {
          let params = {
            status: toStatus,
            idList: [data.id]
          }
          this.updateStatus(params)
        }
      }
    },
    editThreshold(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/thresholdItemDialog.vue'),
        data: {
          title: this.$t('编辑门槛项'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editCertifiScene(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/certificationSceneDialog.vue'),
        data: {
          title: this.$t('编辑认证场景'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    editReview(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/reviewItemDialog.vue'),
        data: {
          title: this.$t('编辑评审项'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 资质项定义-点击资质编码-查看资质项详情（弹窗）
    viewQualification(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/qualificationItemDialog.vue'),
        data: {
          title: this.$t('资质项定义'),
          isView: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editQualification(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/qualificationItemDialog.vue'),
        data: {
          title: this.$t('编辑资质项定义'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleClickCellTitle(e) {
      //单元格Title点击
      const { field, data } = e
      if (field == 'qualificationCode') {
        //资质编码==查看详情（弹窗）
        this.viewQualification(data)
      }
      if (field === 'stageNo' && data && data.id) {
        this.$router.push({
          path: '/supplier/questionnaire-config-detail',
          query: {
            id: data.id
          }
        })
      }
      if (field === 'modelName' && data && data.id) {
        // console.log(data,"datadata");
        this.$API.fileService
          .downloadPrivateFileTypeOne(data.modelUrl)
          .then((res) => {
            let link = document.createElement('a') // 创建元素
            link.style.display = 'none'
            // link.id = new Date().getTime();
            // link.href = data.modelUrl.replace('http','https')
            let blob = new Blob([res.data], { type: 'application/x-msdownload' })
            let url = window.URL.createObjectURL(blob)
            link.href = url
            link.setAttribute('download', `${data.modelName}`) // 给下载后的文件命名
            // document.body.appendChild(link);
            link.click() // 点击下载
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$toast({ content: this.$t('导出失败，请重试!'), type: 'warning' })
          })
      }
    },

    // 单个删除和批量删除级别、标签定义
    deleteLevelRecord(data) {
      const _this = this
      if (utils.isEmpty(data) || data.length === 0) {
        _this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      let idList = data.map((item) => {
        return item.id
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选阶段？'),
          confirm: () => _this.$API.GradeConfig['deleteGrade']({ idList })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },

    // 单个删除和批量删除阶段定义
    deleteStageRecord(data) {
      const _this = this
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选阶段？'),
          confirm: () => _this.$API.AccessStage['delStage'](data)
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
          // 刷新
        }
      })
    },
    editStage(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/operatorDialog.vue'),
        data: {
          title: this.$t('编辑阶段'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editLevel(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/levelDialog.vue'),
        data: {
          title: this.$t('编辑关系'),
          isEdit: true,
          labelDefineType: data.labelDefineType,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
  min-height: 300px;
}
</style>
