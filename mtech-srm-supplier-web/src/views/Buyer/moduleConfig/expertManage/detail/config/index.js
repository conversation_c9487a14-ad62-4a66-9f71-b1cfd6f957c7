import { i18n } from '@/main.js'
import Vue from 'vue'
const tabToolbar0 = [
  [
    {
      id: 'AddTab0',
      icon: 'icon_solid_Createorder',
      title: i18n.t('新增领域')
    },
    { id: 'DeleteTab0', icon: 'icon_solid_edit', title: i18n.t('删除领域') }
  ],
  []
]
const tabToolbar1 = [
  [
    {
      id: 'AddTab1',
      icon: 'icon_solid_Createorder',
      title: i18n.t('新增成果')
    },
    { id: 'DeleteTab1', icon: 'icon_solid_edit', title: i18n.t('删除成果') }
  ],
  []
]
const tabToolbar2 = [
  [
    {
      id: 'AddTab2',
      icon: 'icon_solid_Createorder',
      title: i18n.t('新增履历')
    },
    { id: 'DeleteTab2', icon: 'icon_solid_edit', title: i18n.t('删除履历') }
  ],
  []
]
const tabToolbar3 = [
  [
    {
      id: 'AddTab3',
      icon: 'icon_solid_Createorder',
      title: i18n.t('新增经历')
    },
    { id: 'DeleteTab3', icon: 'icon_solid_edit', title: i18n.t('删除经历') }
  ],
  []
]
const tabToolbar4 = [
  [
    {
      id: 'AddTab4',
      icon: 'icon_solid_Createorder',
      title: i18n.t('新增附件')
    },
    { id: 'DeleteTab4', icon: 'icon_solid_edit', title: i18n.t('删除附件') }
  ],
  []
]
const tabColumnData0 = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineIndex',
    headerText: i18n.t('序号'),
    cssClass: '',
    cellTools: ['edit', 'delete']
  },
  {
    field: 'specialtyName',
    headerText: i18n.t('领域名称')
  },
  {
    field: 'primarySpecialty',
    headerText: i18n.t('首要专精'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]
const tabColumnData1 = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineIndex',
    headerText: i18n.t('序号'),
    cssClass: '',
    cellTools: ['edit', 'delete']
  },
  {
    field: 'specialtyResultSpec',
    headerText: i18n.t('专业领域（包含但不限于专业奖项、专业论文，专业著作、理论、案例）')
  },
  {
    field: 'sourcingFileSaveRequest',
    headerText: i18n.t('附件'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{getFileName}}</span>`,
          data() {
            return { data: {} }
          },
          computed: {
            getFileName() {
              return this?.data?.sourcingFileSaveRequest?.fileName ?? ''
            }
          }
        })
      }
    }
  }
]
const tabColumnData2 = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineIndex',
    headerText: i18n.t('序号'),
    cssClass: '',
    cellTools: ['edit', 'delete']
  },
  {
    field: 'startTime',
    headerText: i18n.t('时间从'),
    valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
  },
  {
    field: 'endTime',
    headerText: i18n.t('时间至'),
    valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
  },
  {
    field: 'jobPlace',
    headerText: i18n.t('工作单位')
  },
  {
    field: 'jobPosition',
    headerText: i18n.t('职位')
  }
]
const tabColumnData3 = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineIndex',
    headerText: i18n.t('序号'),
    cssClass: '',
    cellTools: ['edit', 'delete']
  },
  {
    field: 'startTime',
    headerText: i18n.t('时间从'),
    valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
  },
  {
    field: 'endTime',
    headerText: i18n.t('时间至'),
    valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
  },
  {
    field: 'eduInstitution',
    headerText: i18n.t('学校学院或其他机构')
  },
  {
    field: 'eduExperience',
    headerText: i18n.t('教育经历')
  },
  {
    field: 'eduDegree',
    headerText: i18n.t('学位')
  },
  {
    field: 'aptitude',
    headerText: i18n.t('执照、许可或资格')
  },
  {
    field: 'certificateNo',
    headerText: i18n.t('证书编号')
  },
  {
    field: 'highestQualifications',
    headerText: i18n.t('最高学历')
  }
]
const tabColumnData4 = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineIndex',
    headerText: i18n.t('序号'),
    cssClass: '',
    cellTools: ['edit', 'delete']
  },
  {
    field: 'fileName',
    headerText: i18n.t('附件名称')
  },
  {
    field: 'fileDetailInfo',
    headerText: i18n.t('附件说明')
  },
  {
    field: 'createUserId',
    headerText: i18n.t('上传人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('上传时间')
  }
]
export const pageConfig = (isNotDraft) => [
  {
    title: i18n.t('专业领域'),
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: isNotDraft ? [] : tabToolbar0
    },
    useToolTemplate: false,
    gridId: '4a31704c-c3df-11ec-b694-47f33ff801c3',
    grid: {
      allowFiltering: true,
      allowPaging: false,
      columnData: tabColumnData0,
      dataSource: []
    }
  },
  {
    title: i18n.t('专业成果'),
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: isNotDraft ? [] : tabToolbar1
    },

    useToolTemplate: false,
    gridId: '4a31704d-c3df-11ec-b694-47f33ff801c3',
    grid: {
      allowFiltering: true,
      allowPaging: false,
      columnData: tabColumnData1,
      dataSource: []
    }
  },
  {
    title: i18n.t('职业履历'),
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: isNotDraft ? [] : tabToolbar2
    },
    useToolTemplate: false,
    gridId: '4a31704e-c3df-11ec-b694-47f33ff801c3',
    grid: {
      allowFiltering: true,
      allowPaging: false,
      columnData: tabColumnData2,
      dataSource: []
    }
  },
  {
    title: i18n.t('教育经历'),
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: isNotDraft ? [] : tabToolbar3
    },
    useToolTemplate: false,
    gridId: '4a31704f-c3df-11ec-b694-47f33ff801c3',
    grid: {
      allowFiltering: true,
      allowPaging: false,
      columnData: tabColumnData3,
      dataSource: []
    }
  },
  {
    title: i18n.t('上传附件'),
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: isNotDraft ? [] : tabToolbar4
    },
    useToolTemplate: false,
    gridId: '4a317050-c3df-11ec-b694-47f33ff801c3',
    grid: {
      allowFiltering: true,
      allowPaging: false,
      columnData: tabColumnData4,
      dataSource: []
    }
  }
]
