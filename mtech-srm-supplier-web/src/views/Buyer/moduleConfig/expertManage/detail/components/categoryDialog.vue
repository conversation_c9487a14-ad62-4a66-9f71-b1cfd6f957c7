<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="confirm"
  >
    <div class="dialog-content">
      <mt-form ref="effectiveOrgDTO" class="detail-effectiveorg--form" :model="searchForm">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="searchForm" :label="$t('品类编码')">
              <mt-input v-model="searchForm.categoryCode" :placeholder="$t('请输入品类编码')" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="searchForm" :label="$t('品类名称')">
              <mt-input v-model="searchForm.categoryName" :placeholder="$t('请输入品类名称')" />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
      <span class="detail-header-button--wrap">
        <mt-button class="detail-header-button" @click="reset">{{ $t('重置') }}</mt-button>
        <mt-button class="detail-header-button" @click="search">{{ $t('查询') }}</mt-button>
      </span>
      <ScTable
        ref="xTable"
        :is-show-right-btn="false"
        :columns="columns"
        :table-data="tableData"
        :tree-config="null"
        header-align="center"
        align="center"
      >
      </ScTable>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import cloneDeep from 'lodash/cloneDeep'

export default {
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      searchForm: {
        categoryCode: null,
        categoryName: null
      },
      buttons: [
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      tableData: [],
      columns: [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: '50'
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码')
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称')
        }
      ]
    }
  },
  watch: {
    'modalData.data': {
      handler(arr) {
        if (arr) {
          this.searchData = cloneDeep(JSON.parse(arr))
          this.tableData = JSON.parse(arr)
        } else {
          this.tableData = []
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show() //显示弹窗
  },
  methods: {
    confirm() {
      this.$emit('confirm-function') //关闭弹窗
    },
    reset() {
      this.searchForm = {
        categoryCode: null,
        categoryName: null
      }
      this.tableData = this.searchData
    },
    search() {
      const newData = this.searchData.filter((item) => {
        if (
          this.searchForm.categoryCode &&
          this.searchForm.categoryCode != '' &&
          item.categoryCode.indexOf(this.searchForm.categoryCode) === -1
        ) {
          return false
        }
        if (
          this.searchForm.categoryName &&
          this.searchForm.categoryName != '' &&
          item.categoryName.indexOf(this.searchForm.categoryName) === -1
        ) {
          return false
        }
        return true
      })
      console.log('confirm-functionconfirm-function', newData)
      this.tableData = newData
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
.detail-effectiveorg--form {
  padding-top: 10px;
}
.detail-header-button--wrap {
  float: right;
  .detail-header-button {
    margin-right: 24px;
  }
}
</style>
