import { i18n } from '@/main.js'

export const categoryTypeList = [
  { text: i18n.t('通用'), value: '0' },
  { text: i18n.t('限品类'), value: '1' }
]
export const orgTypeList = [
  { text: i18n.t('通用'), value: '0' },
  { text: i18n.t('限组织'), value: '1' }
]
export const sceneTypeList = [
  { text: i18n.t('通用'), value: '0' },
  { text: i18n.t('限场景'), value: '1' }
]
export const applyTypeList = [
  {
    text: i18n.t('品类准入'),
    value: '1'
  },
  { text: i18n.t('企业准入'), value: '2' }
]
export const conditionAttributeList = [
  { text: i18n.t('得分'), value: '1' },
  { text: i18n.t('不合格项个数'), value: '3' },
  { text: i18n.t('合格项个数'), value: '2' }
]
export const symbolList = [
  {
    text: '>',
    value: '1'
  },
  {
    text: '<',
    value: '2'
  },
  {
    text: '≥',
    value: '3'
  },
  {
    text: '≤',
    value: '4'
  },
  {
    text: '=',
    value: '5'
  }
]
const sourceStatusMap = [
  { applyStatus: -1, label: i18n.t('已停用'), cssClass: 'title-#9a9a9a' },
  { applyStatus: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
  { applyStatus: 1, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
  {
    applyStatus: 3,
    label: i18n.t('审批拒绝'),
    cssClass: 'title-#ed5633'
  },
  { applyStatus: 4, label: i18n.t('已生效'), cssClass: 'title-#6386c1' },
  {
    applyStatus: 5,
    label: i18n.t('提交状态'),
    cssClass: 'title-#6386c1'
  }
]

export const bizTypeMap = [
  { text: i18n.t('招投标'), value: 0, cssClass: '' },
  { text: i18n.t('现场审查'), value: 1, cssClass: '' }
]

export const expertTypeMap = [
  { text: i18n.t('商务'), value: 0, cssClass: '' },
  { text: i18n.t('技术'), value: 1, cssClass: '' },
  { text: '商务&技术', value: 2, cssClass: '' },
  { text: i18n.t('现场考察'), value: 3, cssClass: '' },
  { text: i18n.t('质量专家'), value: 4, cssClass: '' },
  { text: i18n.t('采购专家'), value: 5, cssClass: '' },
  { text: i18n.t('研发专家'), value: 6, cssClass: '' },
  { text: i18n.t('CSR体系专家'), value: 7, cssClass: '' }
]

export const conditionTypeList = [
  { text: i18n.t('且'), value: '1' },
  { text: i18n.t('或'), value: '2' }
]
export let templateTypesList = []
export let categoryList = []
export let orgList = []
export let questionnaireTypeList = []
export let thresholdTypeList = []

// ==========专家库==========
export const expertDatabase = [
  {
    width: '50',
    type: 'checkbox',
    customAttributes: {
      class: 'sticky-col-0'
    }
  },
  {
    field: 'applyCode',
    headerText: i18n.t('申请单编码'),
    width: '220',
    cellTools: ['']
  },
  {
    width: '120',
    field: 'expertSource',
    headerText: i18n.t('专家来源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('内部'), 1: i18n.t('外聘') }
    }
  },
  {
    width: '120',
    field: 'expertName',
    headerText: i18n.t('专家姓名')
  },
  {
    width: '120',
    field: 'expertLevel',
    headerText: i18n.t('专家级别'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('二级'), 2: i18n.t('一级') }
    }
  },
  {
    width: '120',
    field: 'expertCategory',
    headerText: i18n.t('专家类型'),
    valueConverter: {
      type: 'map',
      map: expertTypeMap,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    width: '120',
    field: 'mobilePhone',
    headerText: i18n.t('移动电话')
  },
  {
    field: 'subAccount',
    width: '150',
    headerText: i18n.t('账号'),
    ignore: true
  },
  {
    field: 'email',
    width: '150',
    headerText: i18n.t('邮箱'),
    ignore: true
  },
  {
    field: 'gender',
    width: '80',
    headerText: i18n.t('专家性别'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('男'),
        1: i18n.t('女')
      }
    }
  },
  {
    field: 'applyStatus',
    // width: "150",
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: sourceStatusMap,
      fields: { text: 'label', value: 'applyStatus' }
    }
  },
  {
    field: 'bizType',
    // width: "150",
    headerText: i18n.t('业务类型'),
    valueConverter: {
      type: 'map',
      map: bizTypeMap,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'categoryInfo',
    headerText: i18n.t('品类'),
    width: 80,
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return i18n.t('查看品类')
        } else {
          return ''
        }
      }
    }
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     if (e) {
    //       categoryList = JSON.parse(e || '[]')
    //       const res =
    //         Array.isArray(categoryList) && categoryList.map((el) => el.categoryName).join()
    //       return res
    //     } else {
    //       return '-'
    //     }
    //   }
    // }
  },
  {
    field: 'organizationInfo',
    width: '150',
    headerText: i18n.t('所属公司'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          orgList = JSON.parse(e || '[]')
          const res = Array.isArray(orgList) && orgList.map((el) => el.orgName).join()
          return res
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'sceneApproveCnt',
    width: '150',
    headerText: i18n.t('现场审查次数'),
    ignore: true
  },
  {
    field: 'reportApproveCnt',
    width: '150',
    headerText: i18n.t('报告审核次数'),
    ignore: true
  },
  {
    field: 'refuseExecutionCnt',
    width: '150',
    headerText: i18n.t('拒绝执行次数'),
    ignore: true
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    ignore: true
  },
  {
    field: 'mustNotApproveDate',
    width: '150',
    headerText: i18n.t('不可审查日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          const dateList = JSON.parse(e || '[]')
          const res = Array.isArray(dateList) && dateList.join()
          return res
        } else {
          return '-'
        }
      }
    }
  }
]
