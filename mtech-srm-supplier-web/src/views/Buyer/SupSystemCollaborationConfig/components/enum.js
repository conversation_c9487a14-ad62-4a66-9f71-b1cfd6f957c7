import { i18n } from '@/main.js'

// 是否启用枚举
const Rohs2Options = [
  { text: i18n.t('启用'), value: 1 },
  { text: i18n.t('未启用'), value: 0 }
]
// 状态枚举
const StatusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('生效'), value: 1 },
  { text: i18n.t('失效'), value: 2 }
]
// 中文 - 英文
const functionOptions = [
  { text: i18n.t('供方库存'), value: 'SUPPLIER_INVENTORY' },
  { text: i18n.t('采购订单'), value: 'PURCHASE_ORDER' },
  { text: i18n.t('送货单'), value: 'DELIVERY_ORDER' },
  { text: i18n.t('供方发票'), value: 'SUPPLIER_INVOICE' },
  { text: i18n.t('其他'), value: 'OTHER' }
]
// 中文 - 中文
const functionTypeOptions = [
  { text: i18n.t('供方库存'), value: '供方库存' },
  { text: i18n.t('采购订单'), value: '采购订单' },
  { text: i18n.t('送货单'), value: '送货单' },
  { text: i18n.t('供方发票'), value: '供方发票' },
  { text: i18n.t('其他'), value: '其他' }
]

const collectTypeOptions = [
  { text: i18n.t('推送'), value: '推送' },
  { text: i18n.t('拉取'), value: '拉取' }
]

const receiverOptions = [
  { text: i18n.t('SRM'), value: 'SRM' },
  { text: i18n.t('供方系统'), value: '供方系统' }
]

export {
  Rohs2Options,
  StatusOptions,
  collectTypeOptions,
  functionOptions,
  functionTypeOptions,
  receiverOptions
}
