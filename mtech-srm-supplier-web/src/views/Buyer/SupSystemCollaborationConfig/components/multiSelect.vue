<template>
  <div>
    <mt-multi-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
    ></mt-multi-select>
  </div>
</template>

<script>
import { functionTypeOptions, Rohs2Options, collectTypeOptions, receiverOptions } from './enum'
import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: 'text', value: 'value' },

      dataSource: [],

      userArrList: []
    }
  },

  mounted() {
    const _enum = ['status']
    // this.data[this.data.column.field] = this.data[this.data.column.field].join(',')
    console.log(this.data[this.data.column.field], 'field')
    if (this.data.column.field === 'receiver') {
      this.data[this.data.column.field] = this.data[this.data.column.field]?.split(',')
    }

    if (_enum.includes(this.data.column.field)) {
      this.dataSource = Rohs2Options
    } else if (this.data.column.field === 'functionType') {
      this.dataSource = functionTypeOptions
    } else if (this.data.column.field === 'collectType') {
      this.dataSource = collectTypeOptions
    } else if (this.data.column.field === 'receiver') {
      this.dataSource = receiverOptions
    }
    this.$nextTick(() => {
      this.data[this.data.column.field] = this.data[this.data.column.field]
    })
  },

  methods: {
    // 获取工厂信息
    getSiteCodeInfo(e) {
      const { text, setSelectData } = e
      // 获取工厂列表数据
      const params = {
        fuzzyParam: text || ''
      }
      this.$API.assessManage.fuzzySiteQuery(params).then((res) => {
        if (res.code === 200) {
          this.dataSource = (cloneDeep(res.data) ?? []).map((item) => {
            item.theCodeName = item.siteCode + '-' + item.siteName
            return item
          })
          this.fields = { text: 'theCodeName', value: 'siteCode' }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        }
      })
    },

    startOpen() {
      console.log(this.data, '下拉打开时最新的行数据')
    },

    selectChange(e) {
      console.log(e)
    }
  },
  beforeDestroy() {
    this.$bus.$off('crrEnterpriseNamebus')
    this.$bus.$off('crrCategoryCodebus')
    this.$bus.$off('crrCategoryTypebus')
    this.$bus.$off('maintainOutsideItemcategoryCode')
  }
}
</script>
