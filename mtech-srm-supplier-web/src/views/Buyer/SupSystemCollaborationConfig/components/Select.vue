<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :filtering="filtering"
      :disabled="data.id && data.column.field == 'categoryName'"
      :allow-filtering="true"
    ></mt-select>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import { functionOptions, Rohs2Options, collectTypeOptions } from './enum'
import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: 'text', value: 'value' },

      dataSource: [],

      userArrList: []
    }
  },

  mounted() {
    console.log('moutedmoutedmoutedmoutedmouted', this.data)
    this.filtering = utils.debounce(this.filtering, 1000)
    const _enum = ['status']

    //品类编码
    if (this.data.column.field === 'categoryCode') {
      this.$loading()
      let params = {
        fuzzyNameOrCode: this.data.categoryCode
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.dataSource = _data
          this.fields = { text: 'text', value: 'categoryCode' }
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    } else if (this.data.column.field === 'supplierCode') {
      this.getSupplierInfo({
        text: this.data[this.data.column.field] || '',
        setSelectData: () => {
          this.data['supplierCode'] = this.data[this.data.column.field]
        }
      })
    } else if (this.data.column.field === 'siteCode') {
      this.getSiteCodeInfo({
        text: this.data[this.data.column.field] || '',
        setSelectData: () => {
          this.data['siteCode'] = this.data[this.data.column.field]
        }
      })
    } else if (_enum.includes(this.data.column.field)) {
      this.dataSource = Rohs2Options
    } else if (this.data.column.field === 'function') {
      this.dataSource = functionOptions
    } else if (this.data.column.field === 'collectType') {
      this.dataSource = collectTypeOptions
    }
  },

  methods: {
    // 获取供应商信息
    getSupplierInfo(e) {
      const { text, setSelectData } = e
      this.$loading()
      const params = {
        fuzzyNameOrCode: text || ''
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          this.dataSource = (cloneDeep(res.data) ?? [])
            .filter((item) => !!item?.supplierCode)
            .map((item) => {
              item.theCodeName = item.supplierCode + '-' + item.supplierName
              return item
            })
          this.fields = { text: 'theCodeName', value: 'supplierCode' }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        })
        .catch((e) => {
          this.$toast({
            type: 'error',
            content: e.msg || this.$t('获取供应商数据失败')
          })
        })
        .finally(() => {
          this.$hloading()
        })
    },
    // 获取工厂信息
    getSiteCodeInfo(e) {
      const { text, setSelectData } = e
      // 获取工厂列表数据
      const params = {
        fuzzyParam: text || ''
      }
      this.$API.assessManage.fuzzySiteQuery(params).then((res) => {
        if (res.code === 200) {
          this.dataSource = (cloneDeep(res.data) ?? []).map((item) => {
            item.theCodeName = item.siteCode + '-' + item.siteName
            return item
          })
          this.fields = { text: 'theCodeName', value: 'siteCode' }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        }
      })
    },
    // 模糊搜索
    filtering(e) {
      if (this.data.column.field === 'categoryCode') {
        this.$loading()
        let params = {
          fuzzyNameOrCode: e.text
        }
        this.$API.maintainOutsideItem
          .criteriaQuery(params)
          .then((res) => {
            res.data.map((item) => {
              item.text = item.categoryCode + '-' + item.categoryName
            })
            e.updateData(res.data)
            this.$hloading()
          })
          .catch((error) => {
            this.$hloading()
            this.$toast({
              content: error.msg || this.$t('查询失败，请重试'),
              type: 'error'
            })
          })
      } else if (this.data.column.field === 'supplierCode') {
        this.getSupplierInfo(e)
      } else if (this.data.column.field === 'siteCode') {
        this.getSiteCodeInfo(e)
      }
    },

    startOpen() {
      console.log(this.data, '下拉打开时最新的行数据')
    },

    selectChange(e) {
      if (this.data.column.field === 'categoryCode') {
        let params = {
          fieldCode: 'categoryName',
          itemInfo: {
            categoryId: e.itemData.id,
            categoryCode: e.itemData.categoryCode,
            categoryName: e.itemData.categoryName
          }
        }
        this.$parent.$emit('parentcategoryCode', params)
        this.$bus.$emit('maintainOutsideItemcategoryCode', e.itemData)
      } else if (this.data.column.field === 'supplierCode') {
        this.$bus.$emit('supplierCodeChange', e)
        this.data.supplierName = e.itemData.supplierName
      } else if (this.data.column.field === 'siteCode') {
        this.data.siteName = e.itemData.siteName
        this.$bus.$emit('siteCodeChange', e)
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('crrEnterpriseNamebus')
    this.$bus.$off('crrCategoryCodebus')
    this.$bus.$off('crrCategoryTypebus')
    this.$bus.$off('maintainOutsideItemcategoryCode')
  }
}
</script>
