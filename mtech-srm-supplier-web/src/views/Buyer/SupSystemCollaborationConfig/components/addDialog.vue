<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="orgLevel" :label="$t('组织层级')">
        <mt-select
          v-model="addForm.orgLevel"
          :disabled="dialogType === 'edit'"
          :data-source="orgLevelData"
          :fields="{ text: 'itemName', value: 'treeLevel' }"
          :show-clear-button="true"
          :placeholder="$t('请选择组织层级')"
          @change="orgLevelChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="orgName"
        :label="$t('组织名称')"
        v-if="addForm.orgLevel !== 0 && addForm.orgLevel !== 1"
      >
        <mt-select
          v-model="addForm.orgName"
          :data-source="orgData"
          @change="orgNameChange"
          :fields="{ text: 'orgName', value: 'orgName' }"
          :show-clear-button="true"
          :placeholder="$t('请选择组织名称')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="orgName" :label="$t('组织名称')" v-else>
        <mt-input
          v-model="addForm.orgName"
          :show-clear-button="true"
          :placeholder="$t('请选择组织名称')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="orgCode" :label="$t('组织编码')">
        <mt-input
          :disabled="addForm.orgLevel !== 0 && addForm.orgLevel !== 1"
          v-model="addForm.orgCode"
          :show-clear-button="true"
          :multiline="false"
          :placeholder="$t('请输入组织编码')"
          :maxlength="32"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="parentOrgId" :label="$t('父组织名称')" v-if="addForm.orgLevel !== 0">
        <mt-select
          v-model="addForm.parentOrgId"
          :data-source="parentOrgData"
          :allow-filtering="true"
          :fields="{ text: 'orgName', value: 'id' }"
          :show-clear-button="true"
          :placeholder="$t('请选择父组织名称')"
          @change="parentOrgChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="effectiveDate" :label="$t('生效日期')">
        <mt-date-picker
          :disabled="dialogType === 'edit'"
          v-model="addForm.effectiveDate"
          :show-clear-button="true"
          :min="new Date()"
          :allow-edit="false"
          :placeholder="$t('选择生效日期')"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="invalidDate" :label="$t('失效日期')">
        <mt-date-picker
          :disabled="dialogType === 'edit'"
          v-model="addForm.invalidDate"
          :min="addForm.effectiveDate"
          :show-clear-button="true"
          :allow-edit="false"
          :placeholder="$t('选择失效日期')"
        ></mt-date-picker>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    dialogType: {
      type: String,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: this.$t('组织新增'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        orgLevel: '',
        orgType: '',
        orgTypeName: '',
        orgName: '',
        id: '',
        orgCode: '',
        parentOrgId: '',
        parentOrgName: '',
        effectiveDate: new Date(),
        invalidDate: ''
      },
      orgLevelData: [],
      orgNameData: [],
      orgData: [],
      parentOrgData: [],
      copyAddForm: null,
      copyRules: null,
      rules: {
        orgLevel: [
          {
            required: true,
            message: this.$t('请选择组织层级'),
            trigger: 'blur'
          }
        ],
        orgName: [
          {
            required: true,
            message: this.$t('请选择组织名称'),
            trigger: 'blur'
          }
        ],
        orgCode: [
          {
            required: true,
            message: this.$t('请输入组织编码'),
            trigger: 'blur'
          }
        ],
        parentOrgId: [
          {
            required: true,
            message: this.$t('请选择父组织名称'),
            trigger: 'blur'
          }
        ],
        effectiveDate: [
          {
            required: true,
            message: this.$t('请选择生效日期'),
            trigger: 'blur'
          }
        ]
        // invalidDate: [
        //   {
        //     required: true,
        //     message: this.$t('请选择失效日期'),
        //     trigger: 'blur'
        //   }
        // ]
      },
      taskTypeData: [],
      supplierData: [],
      templateTypeData: [],
      evaluationCycleData: [],
      fieldData: null,
      fieldConfig: null,
      flag: true
    }
  },
  computed: {},
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    this.getSelectDictList()

    if (this.dialogType === 'edit') {
      this.addForm = Object.assign({}, this.addForm, this.dialogData)
    }
    this.copyRules = Object.assign({}, this.rules)
  },
  methods: {
    // 弹框 - 开启
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    // 弹框 - 关闭
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    // 弹框 - 确认
    handleConfirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return
        const postData = {
          ...this.addForm,
          effectiveDate: this.addForm.effectiveDate
            ? utils.formateTime(new Date(this.addForm.effectiveDate))
            : '',
          invalidDate: this.addForm.invalidDate
            ? utils.formateTime(new Date(this.addForm.invalidDate))
            : ''
        }
        // 如果是集团 或者 组织层级
        if (postData.orgLevel === 0) {
          delete postData.parentOrgId
          delete postData.parentOrgName
        }
        this.$API.performanceManage[
          this.dialogType === 'edit' ? 'updateStructureList' : 'addStructureList'
        ](postData)
          .then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
          .catch(() => {
            this.$emit('handleAddDialogShow', false)
          })
      })
    },
    // 获取任务名称
    async getSelectDictList() {
      this.orgLevelData = await this.getDictItems('orglevel')
    },
    // 根据名称获取字典数据
    getDictItems(key) {
      return this.$API.supplierInvitation
        .getDictCode({
          dictCode: key,
          nameLike: ''
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            return res.data
          }
        })
    },
    // 获取父组织列表数据
    getParentOrgList(v) {
      this.$API.performanceManage.getParentOrgList({ orgLevel: v }).then((res) => {
        this.parentOrgData = res.data
      })
    },
    // 获取组织列表数据
    getOrgList(v) {
      this.$API.performanceManage.getOrgList({ orgLevel: v }).then((res) => {
        this.orgData = res.data
      })
    },
    // 组织层级
    orgLevelChange(v) {
      const _level = v.itemData.treeLevel || 0
      const _code = v.itemData.itemCode || ''
      const _name = v.itemData.itemName || ''
      this.getParentOrgList(_level)
      this.getOrgList(_level)
      this.addForm.orgType = _code
      this.addForm.orgTypeName = _name
      if (v.e) {
        this.addForm.parentOrgId = ''
        this.addForm.parentOrgName = ''
        this.addForm.orgName = ''
        this.addForm.orgCode = ''
      }
    },
    // 组织名称
    orgNameChange(v) {
      this.addForm.orgCode = v.itemData?.orgCode || ''
      this.addForm.id = v.itemData?.id || ''
    },
    // 父组织切换
    parentOrgChange(v) {
      this.addForm.parentOrgName = v.itemData?.orgName
    }
  }
}
</script>

<style>
.create-proj-dialog .e-dlg-content {
  padding-top: 20px !important;
}
</style>
