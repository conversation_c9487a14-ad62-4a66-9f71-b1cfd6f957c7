<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
      @parentcategoryCode="parentcategoryCode"
      @handleCustomReset="handleCustomReset"
    >
      <!-- <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
              <mt-select
                v-model="searchFormModel.categoryCode"
                css-class="rule-element"
                :data-source="categoryCodeENUM"
                :show-clear-button="true"
                :filtering="filtering"
                :allow-filtering="true"
                @change="selectCategory"
                :fields="{
                  text: 'text',
                  value: 'categoryCode'
                }"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
              <mt-input v-model="searchFormModel.categoryName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="levelCode" :label="$t('层级')" label-style="top">
              <mt-select
                v-model="searchFormModel.levelCode"
                css-class="rule-element"
                :data-source="levelENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="orgCode" :label="$t('事业部')" label-style="top">
              <mt-select
                v-model="searchFormModel.orgCode"
                css-class="rule-element"
                :data-source="bussinessUnitENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="valid" :label="$t('是否生效')" label-style="top">
              <mt-select
                v-model="searchFormModel.valid"
                css-class="rule-element"
                :data-source="validENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="domainCode" :label="$t('归属领域')" label-style="top">
              <mt-select
                v-model="searchFormModel.domainCode"
                css-class="rule-element"
                :data-source="domainENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="quoteMethod" :label="$t('询报价模式')" label-style="top">
              <mt-select
                v-model="searchFormModel.quoteMethod"
                css-class="rule-element"
                :data-source="quoteMethodENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="effectiveMethod" :label="$t('供应商引入')" label-style="top">
              <mt-select
                v-model="searchFormModel.effectiveMethod"
                css-class="rule-element"
                :data-source="effectiveMethodENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="deliveryMethod" :label="$t('交货模式')" label-style="top">
              <mt-select
                v-model="searchFormModel.deliveryMethod"
                css-class="rule-element"
                :data-source="deliveryMethodENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="gradingResult" :label="$t('分层分级结果')" label-style="top">
              <mt-select
                v-model="searchFormModel.gradingResult"
                css-class="rule-element"
                :data-source="gradingResultENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="user" :label="$t('人员')" label-style="top">
              <mt-input v-model="searchFormModel.user" maxlenght="200"></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </template> -->
    </mt-template-page>
    <span>
      <uploadDialog
        ref="uploadDialog"
        class="upload-dialog-style"
        v-if="openDialog"
        :modal-data="modalData"
        @cancel-function="() => (openDialog = false)"
        @confirm-function="closeUploadDialog"
      />
    </span>
  </div>
</template>

<script>
// import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
import { toolbar, columnData } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils.js'
import { functionOptions } from './components/enum'
import {
  levelENUM,
  domainENUM,
  quoteMethodENUM,
  mapSelectionENUM,
  costMouldENUM,
  effectiveMethodENUM,
  deliveryMethodENUM,
  bussinessUnitENUM,
  gradingResultENUM
  // getCurPerson
} from './components/enum.js'
import cloneDeep from 'lodash/cloneDeep'
import uploadDialog from '@/components/uploadDialog/index.vue'
import utils from '@/utils/utils'

export default {
  components: {
    // UploadExcelDialog
    uploadDialog
  },
  // provide() {
  //   return {
  //     searchFormModelList: [this.searchFormModel]
  //   }
  // },
  data() {
    return {
      searchFormModel: {}, //自定义模板
      tempPersonList: [],
      categoryCodeObj: {},
      pageConfig: [
        {
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '6c2ff10f-4f56-40fc-bc06-5f3922f2d3c4',
          useCombinationSelection: false,
          grid: {
            columnData,
            // commandClick: this.commandClick, //配置单击进入行编辑
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowEditing: true, //开启表格编辑操作
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/config/query'
            }
          }
        }
      ],
      levelENUM,
      domainENUM,
      quoteMethodENUM,
      mapSelectionENUM,
      costMouldENUM,
      effectiveMethodENUM,
      deliveryMethodENUM,
      bussinessUnitENUM,
      gradingResultENUM,
      // getCurPerson,
      //------------------------>
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'categoryRelationshipReport',
        templateUrl: '', // 下载模板接口方法名
        uploadUrl: 'import', // 上传接口方法名
        file: 'multipartFile' //后端接收参数名
      },
      categoryCodeENUM: [], //品类数据相关值集
      openDialog: false,
      modalData: {
        title: this.$t('导入'),
        paramsKey: 'excel',
        importApi: this.$API.SupSystemCollaborationConfig.importHazardousSubstancesInfo,
        downloadTemplateApi: this.$API.SupSystemCollaborationConfig.importTemplateDown
      }
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('refreshCurrentGridData', () => {
      this.refreshPage()
    })
    // this.init()
    this.filtering = utils.debounce(this.filtering, 1000)

    this.getCategoryInfos()
  },
  methods: {
    // 获取数据
    async init() {
      const params = {
        page: { current: 1, size: 10000 }
      }
      const res = await this.$API.categoryConfigurationReport.getPageInfo(params)

      res.data.records = res.data.records.map((item) => {
        const personConfig = JSON.parse(item.personConfig)
        for (let key in personConfig) {
          const curFieldName = key.replace('_NAME', '')
          item[curFieldName] = personConfig[key]
        }
        const templateConfig = JSON.parse(item.templateConfig)
        for (let key in templateConfig) {
          item[key] = templateConfig[key]
        }
        return item
      })
      console.log('resresres', res)
      this.pageConfig[0].grid.dataSource = res.data.records
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 获取品类相关数据
    getCategoryInfos() {
      this.$API.maintainOutsideItem
        .criteriaQuery()
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.categoryCodeENUM = _data
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    // 模糊搜索品类
    filtering(e) {
      this.$loading()
      let params = {
        fuzzyNameOrCode: e.text
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          res.data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          e.updateData(res.data)
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    // 品类下拉
    selectCategory(e) {
      const { itemData } = e
      this.searchFormModel.categoryName = itemData.categoryName
    },
    // 同步数据
    parentcategoryCode(val) {
      if (val.fieldCode === 'categoryName') {
        Object.assign(this.categoryCodeObj, val.itemInfo || {})
      } else if (this.tempPersonList === null) {
        this.tempPersonList = [val]
      } else if (this.tempPersonList.some((item) => item.fieldCode === val.fieldCode)) {
        const index = this.tempPersonList.findIndex((item) => item.fieldCode === val.fieldCode)
        this.tempPersonList.splice(index, 1)
        this.tempPersonList.push(val)
      } else {
        this.tempPersonList.push(val)
      }
      console.log(
        'parentcategoryCodeparentcategoryCodeparentcategoryCodeparentcategoryCode',
        this.tempPersonList
      )
    },
    commandClick(args) {
      if (args.commandColumn.type === 'Deletes') {
        // 如果不存在id,直接删除(获取不到rowIndex,暂定刷新)
        if (!args.rowData.id) {
          this.$refs.tepPage.refreshCurrentGridData()
          return
        }
        let params = {
          id: args.rowData.id
        }
        this.$API.categoryRelationshipReport.remove(params).then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
        })
      } else if (args.commandColumn.type === 'history') {
        this.$dialog({
          modal: () =>
            import(/* webpackChunkName: "components/upload" */ './components/history.vue'),
          data: {
            title: this.$t('历史记录'),
            id: args.rowData.id
          },
          success: () => {}
        })
      }
    },
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (e.toolbar.id === 'closeEdit') {
        e.grid.closeEdit()
        this.$refs.tepPage.refreshCurrentGridData()
      }
      if (['Delete', 'ConfigActive', 'ConfigInactive'].includes(e.toolbar.id)) {
        if (records.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        if (e.toolbar.id !== 'Delete' && records.length > 1) {
          this.$toast({ content: this.$t('当前操作只能操作单条数据'), type: 'warning' })
          return
        }
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      } else if (e.toolbar.id === 'upload') {
        this.handleClickToolBarUpload()
      } else if (e.toolbar.id === 'ConfigActive') {
        // 生效
        this.handleClickToolBarConfigActive(records)
      } else if (e.toolbar.id === 'ConfigInactive') {
        // 失效
        this.handleClickToolBarConfigInactive(records)
      } else if (e.toolbar.id === 'Download') {
        this.handleClickToolBarDownload()
      } else if (e.toolbar.id === 'Delete') {
        this.handleClickToolBarDelete(records)
      }
    },
    handleClickToolBarConfigActive(list = []) {
      for (let i = 0; i < list.length; i++) {
        if ([1, '1'].includes(list[i].status)) {
          this.$toast({
            type: 'error',
            content: this.$t('已启用的数据不能二次启用')
          })
          return
        }
      }
      const _ids = list
        .map((item) => ({ id: item.id, status: 1 }))
        .filter((item) => item.id !== null)
      this.$store.commit('startLoading')
      this.$API.SupSystemCollaborationConfig.startAndStopSupSystemCollaborationConfig(_ids[0])
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t(res.msg ?? this.$t('操作成功'))
            })
            this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? this.$t('操作失败'))
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? this.$t('操作失败'))
          })
        })
        .finally(() => {
          this.refreshPage()
          this.$store.commit('endLoading')
        })
    },
    handleClickToolBarConfigInactive(list = []) {
      for (let i = 0; i < list.length; i++) {
        if ([0, '0'].includes(list[i].status)) {
          this.$toast({
            type: 'error',
            content: this.$t('已停用的数据不能二次停用')
          })
          return
        }
      }
      const _ids = list
        .map((item) => ({ id: item.id, status: 0 }))
        .filter((item) => item.id !== null)
      this.$store.commit('startLoading')
      this.$API.SupSystemCollaborationConfig.startAndStopSupSystemCollaborationConfig(_ids[0])
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t(res.msg ?? this.$t('操作成功'))
            })
            this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? this.$t('操作失败'))
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? this.$t('操作失败'))
          })
        })
        .finally(() => {
          this.refreshPage()
          this.$store.commit('endLoading')
        })
    },
    handleClickToolBarAdd() {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // 删除
    handleClickToolBarDelete(val) {
      if (val != null && val.some((item) => item.id != null)) {
        const ids = val.map((item) => item.id).filter((item) => item !== null)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除数据？')
          },
          success: () => {
            this.$API.SupSystemCollaborationConfig.deleteSupSystemCollaborationConfig(ids).then(
              (res) => {
                if (res && res.code === 200) {
                  this.$toast({
                    type: 'success',
                    content: this.$t(res.msg)
                  })
                  this.refreshPage()
                }
              }
            )
          }
        })
      }
    },
    // refresh页面
    refreshPage() {
      this.$refs.tepPage.refreshCurrentGridData()
    },
    // 导入
    handleClickToolBarUpload() {
      // 打开上传弹窗
      this.openDialog = true
      // 设置下载模板字体样式
      this.$nextTick(() => {
        const element = document.querySelector(
          '.uploader-box .template-to-import .import-the-template'
        )
        element.style.cssText =
          'color: rgb(241, 62, 62, 1);cursor:pointer;font-size: 20px;text-decoration:underline'
      })
    },
    closeUploadDialog() {
      this.openDialog = false
      this.$refs.tepPage.refreshCurrentGridData()
    },
    // 导出
    handleClickToolBarDownload() {
      const _rule = this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: _rule.condition || '',
        page: { current: 1, size: 10000 },
        rules: _rule.rules || []
      }
      this.$API.SupSystemCollaborationConfig.SupSystemCollaborationConfigExport(params).then(
        (res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        }
      )
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        const { functionType: fun, supplierCode, siteCode, collectType, receiver } = data
        let submitStatus = true
        if (!supplierCode) {
          this.$toast({
            type: 'error',
            content: this.$t('供应商编码不能为空')
          })
          submitStatus = false
        } else if (!siteCode) {
          this.$toast({
            type: 'error',
            content: this.$t('工厂编码不能为空')
          })
          submitStatus = false
        } else if (!receiver || receiver?.length === 0) {
          this.$toast({
            type: 'error',
            content: this.$t('接收方不能为空')
          })
          submitStatus = false
        }
        if (!submitStatus) {
          this.startEdit(rowIndex || 0)
          return
        }
        // 所属功能
        if (fun && fun.length > 0) {
          let _fun = fun
            .map((item) => {
              let _item = functionOptions.filter((n) => n.text === item)
              if (_item && item.length > 0) {
                return _item[0].value
              }
            })
            .filter((m) => !!m)
          data.functionType = _fun.join()
        }
        // 集成类型
        if (collectType && collectType.length > 0) {
          data.collectType = collectType.join()
        }
        if (receiver && receiver.length > 0) {
          data.receiver = receiver.join()
        }
        // 工厂编码
        // if (siteCode && siteCode.length > 0) {
        //   data.siteCode = siteCode.join()
        // }
        this.handleSave(data, rowIndex, requestType)
      }
    },
    // 保存
    handleSave(rowData, rowIndex = 0) {
      const _API = rowData.id
        ? 'addSupSystemCollaborationConfig'
        : 'saveSupSystemCollaborationConfig'
      console.log('rowDatarowData', rowData)
      this.$API.SupSystemCollaborationConfig[_API](rowData)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: res.msg || this.$t('操作成功'), type: 'success' })
            this.$refs.tepPage.refreshCurrentGridData()
          } else {
            this.$toast({ content: res.msg, type: 'error' })
            this.startEdit(rowIndex)
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg, type: 'error' })
          this.startEdit(rowIndex)
        })
    },
    startEdit(rowIndex) {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    actionBegin(args) {
      const { requestType, data } = args
      console.log('datadatadata', data, requestType)
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background-color: #fff;
  .e-color {
    color: #000;
  }
}
</style>
