import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
import inputShow from '../components/inputShow.vue'
import multiSelect from '../components/multiSelect.vue'
import InputView from '../components/inputView.vue'
import {
  functionOptions,
  functionTypeOptions,
  collectTypeOptions,
  receiverOptions
} from '../components/enum'

export const toolbar = {
  tools: [
    [
      { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
      { id: 'closeEdit', icon: 'icon_table_delete', title: i18n.t('取消编辑') },
      { id: 'Delete', icon: 'icon_solid_Createorder', title: i18n.t('删除') },
      { id: 'ConfigActive', icon: 'icon_solid_Activateorder', title: i18n.t('启用') },
      { id: 'ConfigInactive', icon: 'icon_solid_Pauseorder', title: i18n.t('停用') },
      { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
      { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
    ],
    ['Filter', 'Refresh', 'Setting']
  ]
}

const statusOptions = [
  { text: i18n.t('未启用'), value: 0 },
  { text: i18n.t('启用'), value: 1 }
]

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: 120,
    field: 'status',
    headerText: i18n.t('启动状态'),
    allowEditing: false,
    template: () => {
      const template = {
        template: `<div>
          <div>
            <span>{{ data.status | transformValue }}</span>
          </div>
          <div :style="btnParentStyle">
            <span v-if="data.status === 0" @click="startEnable" :style="btnStyle">{{ $t('启用') }}</span>
            <span v-else @click="endEnable" :style="btnStyle">{{ $t('停用') }}</span>
          </div>
        </div>
        `,
        data() {
          return {
            data: {},
            btnStyle: {
              color: 'var(--plugin-ct-content-color)',
              fontSize: '12px',
              cursor: 'pointer'
            },
            btnParentStyle: {
              display: 'flex'
            }
          }
        },
        filters: {
          transformValue(e) {
            if (e || [0, '0'].includes(e)) {
              return [1, '1'].includes(e) ? i18n.t('启用') : i18n.t('停用')
            }
            return e
          }
        },
        methods: {
          startEnable() {
            let _row = this.data
            this.handleClickToolBarConfigActive([_row])
          },
          endEnable() {
            let _row = this.data
            this.handleClickToolBarConfigInactive([_row])
          },
          handleClickToolBarConfigActive(list = []) {
            for (let i = 0; i < list.length; i++) {
              if ([1, '1'].includes(list[i].status)) {
                this.$toast({
                  type: 'error',
                  content: this.$t('已启用的数据不能二次启用')
                })
                return
              }
            }
            const _ids = list
              .map((item) => ({ id: item.id, status: 1 }))
              .filter((item) => item.id !== null)
            this.$store.commit('startLoading')
            this.$API.SupSystemCollaborationConfig.startAndStopSupSystemCollaborationConfig(_ids[0])
              .then((res) => {
                if (res && res.code === 200) {
                  this.$toast({
                    type: 'success',
                    content: this.$t('操作成功')
                  })
                  return
                }
                this.$toast({
                  type: 'error',
                  content: this.$t(res.msg ?? '操作失败')
                })
              })
              .catch((err) => {
                this.$toast({
                  type: 'error',
                  content: this.$t(err.msg ?? '操作失败')
                })
              })
              .finally(() => {
                this.$bus.$emit('refreshCurrentGridData')
                this.$store.commit('endLoading')
              })
          },
          handleClickToolBarConfigInactive(list = []) {
            for (let i = 0; i < list.length; i++) {
              if ([0, '0'].includes(list[i].status)) {
                this.$toast({
                  type: 'error',
                  content: this.$t('已停用的数据不能二次停用')
                })
                return
              }
            }
            const _ids = list
              .map((item) => ({ id: item.id, status: 0 }))
              .filter((item) => item.id !== null)
            this.$store.commit('startLoading')
            this.$API.SupSystemCollaborationConfig.startAndStopSupSystemCollaborationConfig(_ids[0])
              .then((res) => {
                if (res && res.code === 200) {
                  this.$toast({
                    type: 'success',
                    content: this.$t('操作成功')
                  })
                  return
                }
                this.$toast({
                  type: 'error',
                  content: this.$t(res.msg ?? '操作失败')
                })
              })
              .catch((err) => {
                this.$toast({
                  type: 'error',
                  content: this.$t(err.msg ?? '操作失败')
                })
              })
              .finally(() => {
                this.$bus.$emit('refreshCurrentGridData')
                this.$store.commit('endLoading')
              })
          }
        }
      }
      return { template }
    },
    // cellTools: [
    //   {
    //     title: i18n.t('启用'),
    //     id: 'line_configActive',
    //     visibleCondition: (data) => data.status === 0
    //   },
    //   {
    //     title: i18n.t('停用'),
    //     id: 'line_configcInative',
    //     visibleCondition: (data) => data.status === 1
    //   }
    // ],
    // valueConverter: {
    //   type: 'map',
    //   map: [
    //     { text: i18n.t('启用'), value: 1, cssClass: '' },
    //     { text: i18n.t('停用'), value: 0, cssClass: '' }
    //   ]
    // },
    searchOptions: {
      elementType: 'select',
      dataSource: statusOptions,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    width: '300',
    field: 'functionType',
    headerText: i18n.t('所属功能'),
    headerTemplate: () => {
      const template = {
        template: `<div>
          <span style="color: red"></span>
          <span>{{ i18n.t('所属功能') }}</span>
        </div>`
      }
      return { template }
    },
    editTemplate: () => {
      return { template: multiSelect }
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (!e || e.length === 0) return []
        let _keys = e
        let _values = []
        if (e) {
          _keys.forEach((item) => {
            functionTypeOptions.forEach((n) => {
              if (n.value === item) {
                _values.push(n.text)
              }
            })
          })
          return _values.join(',')
        }
        return e
      }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: functionOptions,
      operator: 'contains',
      fields: { text: 'text', value: 'value' }
    },
    allowEditing: false
  },
  {
    width: 250,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    headerTemplate: () => {
      const template = {
        template: `
        <div class="headers">
          <span style="color: red">*</span>
          <span class="e-headertext">{{$t('供应商编码')}}</span>
        </div>
      `
      }
      return { template }
    },

    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'masterdata-selects',
      type: 'select',
      selectType: 'supplier',
      fields: { text: 'supplierCode', value: 'supplierCode' },
      params: {},
      multiple: true,
      operator: 'in',
      placeholder: ' '
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    editTemplate: () => {
      return { template: inputShow }
    },
    allowEditing: false
  },
  {
    width: 250,
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    headerTemplate: () => {
      const template = {
        template: `
        <div class="headers">
          <span style="color: red">*</span>
          <span class="e-headertext">{{$t('工厂编码')}}</span>
        </div>
      `
      }
      return { template }
    },

    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'masterdata-selects',
      type: 'select', // 下拉选择
      selectType: 'factoryAddress', // 工厂
      fields: { text: 'title', value: 'siteCode' },
      multiple: true,
      operator: 'in',
      placeholder: ' '
    }
  },
  {
    width: '300',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    width: '300',
    field: 'customerCode',
    headerText: i18n.t('交易方代码'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    width: '300',
    field: 'consignee',
    headerText: i18n.t('收货方代码'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'collectType',
    headerText: i18n.t('集成类型'),
    headerTemplate: () => {
      const template = {
        template: `
        <div class="headers">
          <span style="color: red"></span>
          <span class="e-headertext">{{$t('集成类型')}}</span>
        </div>
      `
      }
      return { template }
    },
    editTemplate: () => {
      return { template: multiSelect }
    },
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     if (e) {
    //       return collectTypeOptions.filter((item) => item.value === e)[0]?.text
    //     }
    //     return e
    //   }
    // },
    searchOptions: {
      elementType: 'select',
      operator: 'contains',
      dataSource: collectTypeOptions,
      fields: { text: 'text', value: 'value' }
    },
    allowEditing: false
  },
  {
    field: 'receiver',
    headerText: i18n.t('数据接收方'),
    editTemplate: () => {
      return { template: multiSelect }
    },
    searchOptions: {
      elementType: 'select',
      operator: 'contains',
      dataSource: receiverOptions,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'signSecret',
    headerText: i18n.t('签名密码'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'aesSecret',
    headerText: i18n.t('AES密钥'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    editTemplate: () => {
      return { template: InputView }
    },
    allowEditing: false
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('更新时间'),
    ignore: true,
    allowEditing: false
  }
]
