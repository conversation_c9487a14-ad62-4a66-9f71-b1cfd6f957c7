import { i18n } from '@/main.js'
export const listColumn = [
  {
    field: 'name',
    headerText: i18n.t('供应商代码'),
    width: '200'
  },
  {
    field: 'name',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'name',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'name',
    headerText: i18n.t('工厂') / i18n.t('地点')
  },
  {
    field: 'name',
    headerText: i18n.t('状态')
  },
  {
    field: 'name',
    headerText: i18n.t('生效时间')
  },
  {
    field: 'name',
    headerText: i18n.t('失效时间')
  },
  {
    field: 'name',
    headerText: i18n.t('是否参与MRP')
  },
  {
    field: 'name',
    headerText: i18n.t('限次')
  },
  {
    field: 'name',
    headerText: i18n.t('限量')
  },
  {
    field: 'name',
    headerText: i18n.t('累计限额')
  }
]
