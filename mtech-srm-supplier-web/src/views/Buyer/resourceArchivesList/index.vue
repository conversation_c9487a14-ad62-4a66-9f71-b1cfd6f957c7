// 货源档案列表
<template>
  <div class="intro-box">
    <div class="tree-list">
      <mt-DropDownTree
        ref="dropDownTree"
        id="checkboxTreeSelect"
        v-model="checkedNodes"
        :fields="fields"
        :show-check-box="true"
        :allow-multi-selection="true"
        :allow-filtering="true"
        :filter-type="Contains"
        :auto-check="true"
        class="category-type"
        :placeholder="$t('请选择')"
        @input="getData"
        :tree-settings="treeSettings"
      ></mt-DropDownTree>
      <mt-button css-class="e-flat" v-show="!isExpand" @click="collapseAll" :is-primary="true">{{
        $t('折叠')
      }}</mt-button>
      <mt-button css-class="e-flat" v-show="isExpand" @click="expandAll" :is-primary="true">{{
        $t('展开')
      }}</mt-button>
      <mt-treeView
        ref="treeView"
        id="treeview"
        v-model="weekDay"
        :fields="fields"
        :show-check-box="true"
        :auto-check="true"
        :node-template="Template"
        :checked-nodes="checkedNodes"
        @nodeOnCheck="nodeOnCheck"
        :node-clicked="nodeChecked"
      ></mt-treeView>
    </div>
    <div class="detail-list">
      <mt-template-page ref="template" :template-config="pageConfig"> </mt-template-page>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import MtTreeView from '@mtech-ui/tree-view'
import MtDropDownTree from '@mtech-ui/drop-down-tree'
import { listColumn } from './config/index.js'

var data = [
  {
    code: 'AF',
    name: 'Africa',
    countries: [
      { code: 'NGA', name: 'Nigeria' },
      { code: 'EGY', name: 'Egypt' },
      { code: 'ZAF', name: 'South Africa' }
    ]
  },
  {
    code: 'AS',
    name: 'Asia',
    expanded: true,
    countries: [
      { code: 'CHN', name: 'China' },
      { code: 'IND', name: 'India', selected: true },
      { code: 'JPN', name: 'Japan' }
    ]
  },
  {
    code: 'EU',
    name: 'Europe',
    countries: [
      { code: 'DNK', name: 'Denmark' },
      { code: 'FIN', name: 'Finland' },
      { code: 'AUT', name: 'Austria' }
    ]
  },
  {
    code: 'NA',
    name: 'North America',
    countries: [
      { code: 'USA', name: 'United States of America' },
      { code: 'CUB', name: 'Cuba' },
      { code: 'MEX', name: 'Mexico' }
    ]
  },
  {
    code: 'SA',
    name: 'South America',
    countries: [
      { code: 'BRA', name: 'Brazil' },
      { code: 'COL', name: 'Colombia' },
      { code: 'ARG', name: 'Argentina' }
    ]
  },
  {
    code: 'OC',
    name: 'Oceania',
    countries: [
      { code: 'AUS', name: 'Australia' },
      { code: 'NZL', name: 'New Zealand' },
      { code: 'WSM', name: 'Samoa' }
    ]
  },
  {
    code: 'AN',
    name: 'Antarctica',
    countries: [
      { code: 'BVT', name: 'Bouvet Island' },
      { code: 'ATF', name: 'French Southern Lands' }
    ]
  }
]

export default {
  components: {
    MtTreeView,
    MtDropDownTree
  },
  props: {
    moduleKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      treeSettings: { autoCheck: true },
      Contains: 'Contains',
      fields: {
        dataSource: data,
        id: 'code',
        value: 'code',
        text: 'name',
        child: 'countries'
      },
      checkedNodes: [],
      weekDay: [],
      radioVal: null,
      isExpand: false,
      Template: function () {
        return {
          template: Vue.component('common', {
            template: `<div class="treeviewdiv" @click="nodeClicked(data)" style="width: 300px">
                        <div class="nodetext">
                          {{ data.name }}
                        </div>
                        <div v-if="data.count" class="nodebadge">
                          <span class="treeCount e-badge e-badge-primary">{{ data.count }}</span>
                        </div>
                      </div>`,
            data() {
              return { data: {} }
            },
            props: {
              mtData: {
                // 拿到数据
                type: Object,
                default: () => {}
              }
            },
            methods: {
              nodeClicked(args) {
                console.log('nodeClicked', args)
              }
            }
          })
        }
      },
      pageConfig: [
        {
          gridId: '2bebf066-2cc1-4a31-8076-926a6cca88c3',
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                'Add',
                'Edit',
                'Delete',
                {
                  id: 'download',
                  icon: 'icon_solid_export',
                  title: this.$t('生效申请')
                },
                {
                  id: 'download',
                  icon: 'icon_solid_export',
                  title: this.$t('失效')
                },
                {
                  id: 'download',
                  icon: 'icon_solid_export',
                  title: this.$t('冻结')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            height: 'auto',
            allowPaging: false,
            // lineSelection: true,
            columnData: listColumn,
            dataSource: [
              {
                weight: null,
                allocateScore: null,
                dimensionName: this.$t('质量'),
                dimensionId: '1442780060260077569',
                name: '1'
              }
            ]
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  mounted() {
    this.$refs.treeView.$refs.ejsRef.expandAll()
    // this.$refs.dropDownTree.$refs.ejsRef.showPopup();
  },
  methods: {
    collapseAll() {
      this.$refs.treeView.$refs.ejsRef.collapseAll()
      this.isExpand = true
    },
    expandAll() {
      this.$refs.treeView.$refs.ejsRef.expandAll()
      this.isExpand = false
    },
    dropDownInput(e) {
      this.checkedNodes = e
      // this.$refs.treeView.$refs.ejsRef.ej2Instances.checkedElement = e;
      console.log(this.$refs.treeView.$refs.ejsRef, e)
    },
    nodeOnCheck(e) {
      this.checkedNodes = e
      // console.log("e", e, this.$refs.treeView);
    },
    getData(args) {
      console.log('getData', args, this.$refs.dropDownTree.$refs.ejsRef)
    },
    nodeChecked(args) {
      var treeObj = this.$refs.treeView.$refs.ejsRef
      var checkedNode = [args.node]
      console.log('nodeChecked', args)
      if (args.event.target.classList.contains('e-fullrow') || args.event.key == 'Enter') {
        var getNodeDetails = treeObj.getNode(args.node)
        console.log('getNodeDetails', getNodeDetails)
        if (getNodeDetails.isChecked == 'true') {
          treeObj.uncheckAll(checkedNode)
        } else {
          treeObj.checkAll(checkedNode)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.intro-box {
  width: 100%;
  display: flex;
  flex: 1;
  background: #fff;
  .setting-banner {
    width: 100%;
    height: 50px;
    cursor: pointer;
    padding: 0 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(79, 91, 109, 1);

    i {
      font-size: 16px;
      line-height: 14px;
      display: inline-block;
      height: 16px;
      width: 16px;
      cursor: pointer;
    }

    span {
      display: inline-block;
      margin-left: 6px;
      cursor: pointer;
    }
  }
}
.intro-box {
  display: flex;
  background-color: #fff;
  height: 100%;
  .detail-list {
    margin-left: 50px;
    float: left;
    width: calc(100% - 450px);
  }
}
.tree-list {
  width: 400px;
}
/deep/ .mt-tree-view {
  float: left;
}
/deep/ .treeviewdiv {
  .nodetext {
    float: left;
  }
  .nodebadge {
    float: left;
    margin-left: 5px;
  }
}
.mt-drop-down-tree {
  float: left;
}
.mt-button {
  margin: 28px 10px;
}
.category-type {
  margin: 24px 0 16px 30px;
  width: 250px;
}
/deep/.e-fullrow {
  z-index: 999;
}
</style>
