import Vue from 'vue'
import utils from '@/utils/utils'
import { i18n } from '@/main'
export const registColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'inviteNo',
    headerText: i18n.t('编号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        permission: ['O_02_1128']
      },
      {
        id: 'deleted',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_1129']
      }
    ]
  },
  {
    width: '200',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商企业名称')
  },
  {
    width: '100',
    field: 'contactName',
    headerText: i18n.t('联系人')
  },
  {
    width: '130',
    field: 'contactMobile',
    headerText: i18n.t('联系方式')
  },
  {
    width: '130',
    field: 'contactEmail',
    headerText: i18n.t('联系邮箱')
  },
  {
    width: '100',
    field: 'businessType',
    headerText: i18n.t('来源'),

    valueConverter: {
      type: 'map',
      map: { 3: i18n.t('自主注册') }
    }
  },
  {
    width: '130',
    field: 'extList',
    headerText: i18n.t('供应范围'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        let arr = []
        e.forEach((item) => {
          arr.push(item.categoryName)
        })
        return arr.join()
      }
    }
  },
  // {
  //   width: '110',
  //   field: 'inviteStatus',
  //   headerText: i18n.t('状态'),
  //   searchOptions: {
  //     elementType: 'select',
  //     dataSource: [
  //       { key: 3, value: i18n.t('已确认') },
  //       { key: 4, value: i18n.t('已拒绝') },
  //       { key: 5, value: i18n.t('已撤回') },
  //       { key: 2, value: i18n.t('待确认') }
  //     ],
  //     fields: { text: 'value', value: 'key' }
  //   },
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e, data) => {
  //       if (e == 1) {
  //         return i18n.t('待邀请')
  //       } else if (e == 3) {
  //         return i18n.t('已确认')
  //       } else if (e == 4) {
  //         return i18n.t('已拒绝')
  //       } else if (e == 5) {
  //         return i18n.t('已撤回')
  //       }
  //       if (data.businessType == 1 || data.businessType == 2) {
  //         if (e == 2) {
  //           return i18n.t('待确认')
  //         }
  //       } else if (data.businessType == 3) {
  //         if (e == 2) {
  //           return i18n.t('待处理')
  //         }
  //       }
  //     }
  //   },
  //   cellTools: [
  //     {
  //       id: 'invite',
  //       title: i18n.t('邀请'),
  //       visibleCondition: (data) => {
  //         return (
  //           (data['inviteStatus'] == 1 || data['inviteStatus'] == 4 || data['inviteStatus'] == 5) &&
  //           data['businessType'] != 3
  //         )
  //       }
  //     },
  //     {
  //       id: 'recall',
  //       title: i18n.t('撤回'),
  //       visibleCondition: (data) => {
  //         return data['inviteStatus'] == 2 && data['businessType'] != 3
  //       }
  //     },
  //     {
  //       id: 'agree',
  //       title: i18n.t('批准'),
  //       visibleCondition: (data) => {
  //         return data['inviteStatus'] == 2 && data['businessType'] == 3
  //       }
  //     },
  //     {
  //       id: 'disAgree',
  //       title: i18n.t('驳回'),
  //       visibleCondition: (data) => {
  //         return data['inviteStatus'] == 2 && data['businessType'] == 3
  //       }
  //     }
  //   ]
  // },

  {
    width: '150',
    field: 'supplierType',
    headerText: i18n.t('供应商类型'),
    valueConverter: {
      type: 'map',
      map: {
        noBiddingPurchaseSupplier: i18n.t('非采'),
        commonPurchaseSupplier: i18n.t('通采'),
        logisticsProvider: i18n.t('物流商'),
        '': i18n.t('--')
      }
    }
  },
  {
    width: '200',
    field: 'applyerOrgName',
    headerText: i18n.t('公司')
  },
  // {
  //   width: '100',
  //   field: 'approvalRemark',
  //   headerText: i18n.t('审批备注'),
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       return e || '--'
  //     }
  //   }
  // },
  {
    width: '100',
    field: 'applyerName',
    headerText: i18n.t('创建人')
  },
  {
    width: '130',
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    width: '100',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const inviteColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商全称')
  },
  {
    width: '150',
    field: 'contactName',
    headerText: i18n.t('供应商联系人')
  },
  {
    width: '150',
    field: 'contactMobile',
    headerText: i18n.t('联系方式')
  },
  {
    width: '150',
    field: 'contactEmail',
    headerText: i18n.t('注册邮箱')
  },
  {
    width: '150',
    field: 'applyerName',
    headerText: i18n.t('创建人')
  },
  {
    width: '130',
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (typeof e == 'object') {
            return utils.formateTime(e, 'yyyy-MM-dd')
          } else if (typeof e == 'string') {
            let val = parseInt(e)
            return utils.formateTime(val, 'yyyy-MM-dd')
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]

export const addCateColumn = (that) => {
  return [
    // {
    //     width: "60",
    //     type: "checkbox",
    //     headerText:i18n.t('全选'),
    // },
    {
      width: '70%',
      field: 'categoryName',
      // headerText:i18n.t('全选'),
      headerTemplate: () => {
        return {
          template: Vue.component('categoryName', {
            template: `<mt-checkbox
                                    :disabled="disabled"
                                    :checked="allchecked"
                                    :label="headerText"
                                    @change="allcheck"
                                ></mt-checkbox>`,
            data: function () {},
            computed: {
              headerText() {
                return i18n.t('全选')
              },
              disabled() {
                return that.categoryTree.dataSource.every((item) => item.pldisabled)
              },
              allchecked() {
                return that.allchecked
              }
            },
            methods: {
              allcheck() {
                that.allchecked = !that.allchecked
                if (that.allchecked) {
                  that.categoryData = []
                  that.categoryTree.dataSource.forEach((item) => {
                    item.selected = true
                    item.disabled = true
                    that.categoryData.push(item)
                  })
                  that.addextList()
                  that.addCategory()
                } else {
                  that.categoryData = []
                  that.categoryTree.dataSource.forEach((item) => {
                    item.selected = false
                    item.disabled = false
                  })
                  that.addextList()
                  that.addCategory()
                }
              }
            }
          })
        }
      },
      template: () => {
        return {
          template: Vue.component('categoryName', {
            template: `<mt-checkbox
                                    :disabled="data.pldisabled"
                                    :checked="data.selected"
                                    :label="data.categoryName"
                                    @change="selectData($event, data, data.index)"
                                ></mt-checkbox>`,
            data: function () {},
            methods: {
              selectData(e, state, item) {
                that.selectData(e, state, item)
              }
            }
          })
        }
      }
    },
    {
      // width: '30%',
      field: 'categoryType',
      headerText: i18n.t('全部设置'),
      textAlign: 'center',
      template: () => {
        return {
          template: Vue.component('categoryType', {
            template: `<mt-select
                                    v-model="data.categoryType"
                                    :width="65"
                                    :height="30"
                                    :data-source="categoryDataArr"
                                    :showClearButton="false"
                                    :disabled="data.disabled"
                                    :placeholder="$t('请选择')"
                                ></mt-select>`,
            data: function () {
              return {
                categoryDataArr: []
              }
            },
            mounted() {
              this.categoryDataArr = that.categoryDataArr
            },
            methods: {
              // onchange(val){
              //     this.$parent.$emit("update",this.data.index,'contactName',val)
              // }
            }
          })
        }
      }
    }
  ]
}

export const addColumnData = [
  {
    width: '150',
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('申请企业编码')
  },
  {
    width: '150',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('申请企业名称')
  },
  {
    width: '150',
    field: 'contactName',
    clipMode: 'Ellipsis',
    headerTemplate: () => {
      return {
        template: Vue.component('contactName', {
          template: `<div style="width:calc(100% - 20px);">
                    <span style="color:red">*</span>  联系人
                    </div>`,
          data: function () {}
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('contactName', {
          template: `<div style="width:calc(100% - 20px);">
                        <mt-input type="text" v-model="data.contactName" style="width:calc(100% - 20px);    display: inline-block;" @change="onchange"></mt-input>
                    </div>`,
          data: function () {},
          methods: {
            onchange(val) {
              this.$parent.$emit('update', this.data.index, 'contactName', val)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'contactMobile',
    clipMode: 'Ellipsis',
    headerTemplate: () => {
      return {
        template: Vue.component('contactName', {
          template: `<div style="width:calc(100% - 20px);">
                    <span style="color:red">*</span>  联系电话
                    </div>`,
          data: function () {}
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('contactMobile', {
          template: `<div style="width:calc(100% - 20px);">
                    <mt-input type="text" v-model="data.contactMobile" style="width:calc(100% - 20px);    display: inline-block;" @change="onchange"></mt-input>
                </div>`,
          data: function () {},
          methods: {
            onchange(val) {
              this.$parent.$emit('update', this.data.index, 'contactMobile', val)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'contactEmail',
    clipMode: 'Ellipsis',
    headerTemplate: () => {
      return {
        template: Vue.component('contactName', {
          template: `<div style="width:calc(100% - 20px);">
                    <span style="color:red">*</span>  联系邮箱
                    </div>`,
          data: function () {}
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('contactEmail', {
          template: `<div style="width:calc(100% - 20px);">
                    <mt-input type="text" v-model="data.contactEmail" style="width:calc(100% - 20px);    display: inline-block;" @change="onchange"></mt-input>
                </div>`,
          data: function () {},
          methods: {
            onchange(val) {
              this.$parent.$emit('update', this.data.index, 'contactEmail', val)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'orgCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '150',
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '150',
    field: 'factoryCode',
    headerText: i18n.t('地点编码')
  },
  {
    width: '150',
    field: 'factoryName',
    headerText: i18n.t('地点名称')
  },
  {
    width: '150',
    field: 'categoryId',
    headerText: i18n.t('品类ID')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

export const addColumnDataexamine = [
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
