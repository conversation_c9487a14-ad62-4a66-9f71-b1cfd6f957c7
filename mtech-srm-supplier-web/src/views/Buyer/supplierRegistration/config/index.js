import utils from '@/utils/utils'
import { i18n } from '../../../../main'
export const validateMobile = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(i18n.t('请输入联系方式')))
  } else if (!utils.isMobile(value)) {
    callback(new Error(i18n.t('请输入正确的联系方式')))
  } else {
    callback()
  }
}

export const validateMEmail = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(i18n.t('请输入联系人邮箱')))
  } else if (!utils.isEmail(value)) {
    callback(new Error(i18n.t('请输入正确的联系人邮箱')))
  } else {
    callback()
  }
}
