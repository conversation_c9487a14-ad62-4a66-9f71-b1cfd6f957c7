<template>
  <mt-dialog
    ref="inviteDialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :header="header"
    :open="onOpen"
    :buttons="buttons"
    @beforeClose="cancel"
  >
    <div v-show="isShow">
      <mt-form ref="ruleForm" class="ruleForm" :model="ruleForm" :rules="rules">
        <mt-row>
          <mt-col :span="12">
            <mt-form-item :label="$t('邀请单名称')" prop="invitationName">
              <mt-input
                v-model="ruleForm.invitationName"
                max-length="30"
                :placeholder="$t('请输入邀请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item :label="$t('选择邮件模板')" prop="templateCode">
              <mt-select
                v-model="ruleForm.templateCode"
                :data-source="emailArr"
                :show-clear-button="true"
                :placeholder="$t('请选择邮件模板')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="24">
            <mt-form-item :label="$t('邀请原因')" prop="invitationReason">
              <mt-input
                v-model="ruleForm.invitationReason"
                :multiline="true"
                max-length="200"
                :placeholder="$t('请输入邀请原因')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <div class="bottomTable">
          <mt-template-page
            ref="inviteRef"
            :template-config="inviteConfig"
            @handleClickToolBar="handleClickToolBar"
          ></mt-template-page>
        </div>
      </mt-form>
      <!-- <div class="slider-footer mt-flex">
        <mt-button type="text" @click="confirmInvite">{{
          $t("确定")
        }}</mt-button>
        <mt-button type="text" @click="cancel">{{ $t("取消") }}</mt-button>
      </div> -->
    </div>
    <div v-show="!isShow">
      <mt-template-page
        ref="templateRef"
        :use-tool-template="false"
        :template-config="templateConfig"
        @handleClickToolBar="clickToolBars"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>
<script>
import { registColumn, inviteColumn } from '../config/column'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isShow: true,
      emailArr: [{ text: this.$t('默认模板'), value: 'message20210721-a382' }],
      templateConfig: [
        {
          gridId: 'ed1b12c7-be53-435b-9293-8eaa7d46a495',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'confirm', icon: '', title: this.$t('确定') },
                { id: 'cancel', icon: '', title: this.$t('取消') }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            allowPaging: false,
            page: {
              current: 1,
              size: 10000
            },
            columnData: registColumn,
            asyncConfig: {
              url: '/supplier/tenant/buyer/invite/list',
              page: {
                size: 1000000
              }
            }
          }
        }
      ],
      ruleForm: {
        invitationName: '',
        invitationReason: '',
        templateCode: '',
        idList: ''
      },
      rules: {
        invitationName: [
          {
            required: true,
            message: this.$t('请输入邀请单名称'),
            trigger: 'blur'
          }
        ],
        templateCode: [
          {
            required: true,
            message: this.$t('请选择邮件模板'),
            trigger: 'blur'
          }
        ]
      },
      inviteConfig: [
        {
          gridId: '09fd086e-a666-4670-be43-316edea2f4ce',
          title: this.$t('邀请供应商'),
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_table_new', title: this.$t('新增') },
                {
                  id: 'deleted',
                  icon: 'icon_table_delete',
                  title: this.$t('删除')
                }
              ]
            ]
          },
          grid: {
            columnData: inviteColumn,
            dataSource: []
          }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    record() {
      return this.modalData.record
    },
    buttons() {
      if (this.isShow) {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirmInvite,
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
      } else {
        return []
      }
    }
  },
  created() {
    this.$set(this.inviteConfig[0].grid, 'dataSource', this.record)
  },
  mounted() {
    this.show()
  },
  methods: {
    show() {
      // this.$set(this.inviteConfig[0].grid, "dataSource", this.record);
      this.$refs['inviteDialog'].ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 表格操作
    handleClickToolBar(item) {
      // 外层表格所有数据
      let allTableData = this.$refs.templateRef.getCurrentTabRef().grid.ej2Instances.currentViewData
      // 邀请供应商表格所有数据
      let selectRecords = this.$refs.inviteRef.getCurrentTabRef().grid.ej2Instances.currentViewData
      if (item.toolbar.id == 'add') {
        this.$loading()
        //新增
        let indexList = []
        // 对比前面已经选中的数据在新弹窗自动选中
        allTableData.forEach((item, index) => {
          selectRecords.forEach((sitem) => {
            if (item.id == sitem.id) {
              indexList.push(index)
            }
          })
        })
        //表格CheckBox勾选为true，入参为数据index的数组
        this.$refs.templateRef.getCurrentTabRef().grid.ej2Instances.selectRows(indexList)
        this.$hloading()
        this.isShow = false
      } else if (item.toolbar.id == 'deleted') {
        this.$loading()
        //删除选中数据
        let selected = item.gridRef.getMtechGridRecords()
        // 表格所有数据
        let allTableData = item.grid.ej2Instances.currentViewData
        if (selected.length == 0) {
          this.$hloading()
          this.$toast({
            content: this.$t('请选择要删除的数据'),
            type: 'warning'
          })
        } else {
          allTableData.forEach((item, index) => {
            selected.forEach((sitem) => {
              if (item.id == sitem.id) {
                allTableData.splice(index, 1)
              }
            })
          })
          this.$set(this.inviteConfig[0].grid, 'dataSource', allTableData)
          this.$hloading()
        }
      }
    },
    clickToolBars(item) {
      if (item.toolbar.id == 'confirm') {
        // 拿到新的勾选的数据覆盖
        let selectRecords = item.gridRef.getMtechGridRecords()
        // this.$set(this.inviteConfig[0].grid, "dataSource", selectRecords);
        this.inviteConfig = [
          {
            ...this.inviteConfig[0],
            grid: {
              ...this.inviteConfig[0].grid,
              dataSource: selectRecords
            }
          }
        ]
        this.$forceUpdate()
        this.$nextTick(() => {
          this.isShow = true
        })
      } else if (item.toolbar.id == 'cancel') {
        this.isShow = true
      }
    },
    //邀请提交
    confirmInvite() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$loading()
          let currentViewData =
            this.$refs.inviteRef.getCurrentTabRef().grid.ej2Instances.currentViewData
          let ids = []
          currentViewData.forEach((item) => {
            ids.push(item.id)
          })
          let obj = {
            idList: ids,
            invitationName: this.ruleForm.invitationName,
            invitationReason: this.ruleForm.invitationReason,
            templateCode: this.ruleForm.templateCode
          }
          this.$API.supplierRegister
            .sendInvite(obj)
            .then(() => {
              this.$hloading()
              this.$toast({
                content: this.$t('邀请成功'),
                type: 'success'
              })
              this.$emit('confirm-function')
            })
            .catch((err) => {
              this.$hloading()
              this.$toast({
                content: err.msg,
                type: 'warning'
              })
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px;
}
.ruleForm {
  margin-top: 10px;
  .bottomTable {
    height: 350px;
  }
}
.slider-footer {
  height: 30px;
  color: #00469c;
  font-size: 14px;
  display: flex;
  justify-content: flex-end;
}
</style>
