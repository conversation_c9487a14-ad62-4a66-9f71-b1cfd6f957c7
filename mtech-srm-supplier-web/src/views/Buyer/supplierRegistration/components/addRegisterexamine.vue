<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="slider-content">
        <mt-form ref="ruleForm" class="ruleForm" :model="ruleForm" :rules="rules">
          <mt-row>
            <mt-col :span="6">
              <mt-form-item :label="$t('请选择供应商')" prop="supplierTenantId">
                <mt-input
                  :disabled="inputState"
                  v-model="ruleForm.supplierEnterpriseName"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('请选择供应商编码')" prop="supplierTenantId">
                <mt-input :disabled="inputState" v-model="supplierInternalCode"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item prop="companyId" class="form-item" :label="$t('请选择准入公司')">
                <mt-input :disabled="true" v-model="ruleForm.applyerOrgName"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item prop="companyId" class="form-item" :label="$t('准入公司')">
                <mt-input :disabled="true" v-model="ruleForm.applyerOrgName"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item prop="companyId" class="form-item" :label="$t('准入公司编码')">
                <mt-input :disabled="true" v-model="ruleForm.applyerOrgCode"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item prop="companyId" class="form-item" :label="$t('联系人')">
                <mt-input :disabled="true" v-model="ruleForm.contactName"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item prop="companyId" class="form-item" :label="$t('联系电话')">
                <mt-input :disabled="true" v-model="ruleForm.contactMobile"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item prop="companyId" class="form-item" :label="$t('联系邮箱')">
                <mt-input :disabled="true" v-model="ruleForm.contactEmail"></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row>
            <mt-col>
              <mt-form-item :label="$t('备注')">
                <mt-input
                  :multiline="true"
                  :rows="2"
                  v-if="doSome"
                  v-model="ruleForm.remark"
                  :placeholder="$t('备注')"
                  max-length="200"
                ></mt-input>
                <mt-input
                  v-else
                  :multiline="true"
                  :rows="2"
                  :disabled="inputState"
                  v-model="ruleForm.remark"
                  :placeholder="$t('备注')"
                  max-length="200"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <div class="dialog-content-h4 fbox">
            <div style="width: 100%">
              <h4>{{ $t('已选列表') }}</h4>
              <mt-DataGrid
                :data-source="addDataSource"
                :column-data="addColumnDataexamine"
                ref="dataGrid"
                :key="DataGridKey"
                @update="update"
              ></mt-DataGrid>
            </div>
          </div>
        </mt-form>
      </div>
      <!-- <div v-if="doSome" style="margin-bottom: 30px">
        <span style="font-weight: 700; padding-left: 20px"> 审批备注</span>
        <mt-input
          style="padding-left: 20px"
          :maxlength="200"
          v-model="approvalRemark"
          type="text"
          placeholder="请输入审批备注"
        ></mt-input>
        <div style="padding-top: 30px">
          <a
            style="padding-right: 80px; padding-left: 20px; color: #00469c"
            @click="() => confirm('agree')"
            >批准</a
          >
          <a style="color: #00469c" @click="confirm">驳回</a>
        </div>
      </div> -->
      <!-- <div class="slider-footer mt-flex" v-show="!inputState"> -->
      <div class="slider-footer mt-flex">
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { validateMobile, validateMEmail } from '../config/index'
import { addColumnDataexamine } from '../config/column'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      doSome: null, //是否能操作
      inviteStatus: null,
      approvalRemark: '',
      extList: [],
      addColumnDataexamine: [],
      addDataSource: [],

      factoryDataSelected: [], //选中的工厂集合

      categoryDataArr: [
        { text: this.$t('原厂'), value: 1 },
        { text: this.$t('代理'), value: 2 }
      ],
      dataArr: [],
      companyArrList: [],
      inputState: false,
      isDis: false,
      searchComData: [
        { text: this.$t('区域1'), value: 0 },
        { text: this.$t('区域2'), value: 1 }
      ],
      factoryArray: [],
      treeViewFields: {
        //checkBox树型参数
        dataSource: [],
        id: 'nodeId',
        text: 'nodeText',
        child: 'nodeChild'
      },
      expandedNodes: [],
      categoryTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      selectTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      radioVal: '0', //选择品类/地点邀请==切换
      radioData: [
        {
          label: this.$t('按地点邀请'),
          value: '0'
        },
        {
          label: this.$t('按品类邀请'),
          value: '1'
        }
      ],

      // 已选品类
      selectCateGroyArr: {},
      // 已勾选的ID

      checkedNodes: [],
      checkedNodesObj: [],

      deepArr: [],

      parentNodeArr: [],
      parentNode: [],

      sltCompanyList: [],

      currentFactory: {},
      factoryId: -1,
      // 已选的公司对象 单选
      currentCompany: {
        id: ''
      },
      showCategoryTree: false, // treeview数据不更新。。
      showSelectTree: false, // treeview数据不更新。。
      ruleInterfaceParams: [],
      ruleForm: {
        id: '',
        supplierTenantId: '',
        supplierEnterpriseId: '',
        supplierEnterpriseCode: '',
        supplierInternalCode: '',
        supplierEnterpriseName: '',
        contactName: '',
        contactMobile: '',
        contactEmail: '',
        remark: '',
        extList: '',
        companyId: '',
        categoryType: 1,
        businessType: 1
      },
      rules: {
        supplierTenantId: [
          {
            required: true,
            message: this.$t('请输入供应商企业全称'),
            trigger: 'blur'
          }
        ],
        contactName: [{ required: true, message: this.$t('请输入联系人'), trigger: 'blur' }],
        contactMobile: [{ required: true, validator: validateMobile, trigger: 'blur' }],
        contactEmail: [{ required: true, validator: validateMEmail, trigger: 'blur' }],
        extList: [
          {
            required: true,
            message: this.$t('请选择供应范围'),
            trigger: 'blur'
          }
        ]
      },
      searchInput: '',
      treeChildId: [],
      categoryData: [], //品类选中集合
      DataSupplierArrFtr: [], //供应商选择后的数据
      argsParams: [],
      currentCompanyParams: [],
      radioInviteShow: true,
      categoryNameArr: [],
      categoryidArr: [],
      plantcheckedNodes: [],
      plantlist: [],
      companyIdKey: 1,
      DataGridKey: 1
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    record() {
      return this.modalData.record
    },
    supplierInternalCode() {
      return this.ruleForm.supplierInternalCode
        ? this.ruleForm.supplierInternalCode
        : this.ruleForm.supplierEnterpriseCode
    }
  },
  async created() {},
  async mounted() {
    this.doSome =
      this.modalData.itemData.businessType == 3 && this.modalData.itemData.inviteStatus == 2
    let arr = await this.getCateGoryTree()
    arr.data.forEach((item) => {
      item.categoryType = 1
      item.selected = false
      item.disabled = true //下拉框的禁用
      item.pldisabled = true //选择框的禁用：初始化因为没有选择工厂 品类禁止勾选
    })
    this.categoryTree.dataSource = arr.data

    let companyOrg = await this.getUserDetail()
    if (!companyOrg || !companyOrg.id) {
      this.$toast({
        content: this.$t('获取当前组织信息失败，请重试'),
        type: 'error'
      })
      return
    }

    arr = await this.getChildrenCompanyOrganization(companyOrg.id)
    let companyArrList = arr.data.filter((item) => {
      return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
    })
    this.companyArrList = companyArrList
    console.log(this.record)
    this.filteringCompany = utils.debounce(this.filteringCompany, 1000)
    if (this.type == 'add') {
      // 根据名字搜索供应商企业全称接口 新增一开始不访问，输入要查询的再调用
      this.ruleForm.id = ''
      this.isDis = false
      this.inputState = false
      return
    } else if (this.type == 'edit') {
      //编辑赋值
      this.searchInput = this.record[0].supplierEnterpriseName
      // 根据名字搜索供应商企业全称接口
      await this.fuzzyQueryTenantByName()
    }
    this.ruleForm.id = this.record[0].id
    // 查询详情
    this.findById(this.ruleForm.id)
    if (this.type == 'edit') {
      this.isDis = true
      this.inputState = false
    } else if (this.type == 'view') {
      this.searchInput = this.record[0].supplierEnterpriseName
      await this.fuzzyQueryTenantByName()
      //查看：禁止编辑
      this.inputState = true
    }
  },
  methods: {
    confirm(agree) {
      if (!agree) {
        if (!this.approvalRemark) {
          this.$toast({
            content: this.$t('驳回状态下审批备注必填哦~'),
            type: 'warning'
          })
          return
        }
      }
      // this.$loading();

      let idList = [this.modalData.itemData.id]

      let obj = {
        idList,
        inviteStatus: agree === 'agree' ? 3 : 4,
        approvalRemark: this.approvalRemark,
        remark: this.ruleForm.remark
      }
      this.$API.supplierRegister
        .handleRegister(obj)
        .then(() => {
          this.$hloading()
          this.modalData.refresh()

          this.cancel()
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'warning'
          })
        })
    },
    update(index, type, val) {
      if (this.addDataSource.length > 0) {
        this.addDataSource[index][type] = val
      }
    },
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    // 查询供应商企业全称下拉框信息
    async filteringCompany(e) {
      if (e.text.trim() == '') {
        return
      }
      this.searchInput = e.text
      await this.fuzzyQueryTenantByName()
      e.updateData(this.dataArr)
    },
    async fuzzyQueryTenantByName() {
      let name = this.searchInput
      this.$loading()
      await this.$API.supplierRegister
        .fuzzyQueryTenantByName(name)
        .then((res) => {
          this.$hloading()
          this.dataArr = res.data
          this.dataArr.forEach((i) => {
            i['nameCode'] = i.name + '：' + i.code
          })
          console.log(this.dataArr, 'this.dataArr')
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    changeSupplier(str) {
      this.addDataSource = []
      if (this.type == 'add') {
        this.ruleForm.companyId = []
      }
      this.DataSupplierArrFtr = this.dataArr.filter((iFtr) => {
        return str.value.includes(iFtr.id)
      }) //得到选择的数据
      this.ruleInterfaceParams = []
      this.DataSupplierArrFtr.forEach((i) => {
        //供应商
        this.ruleInterfaceParams.push({
          supplierTenantId: i.id,
          supplierEnterpriseId: i.enterpriseId,
          supplierEnterpriseCode: i.code,
          supplierInternalCode: i.code,
          supplierEnterpriseName: i.name
        })
        this.queryContactInfo(i.enterpriseId, i) // 获取一下之前注册过的联系人信息
      })
    },
    async queryContactInfo(id, item) {
      this.$loading()
      await this.$API.supplierRegister
        .queryContactInfo({ supplierEnterpriseId: id })
        .then((res) => {
          this.$hloading()
          if (res.data) this.queryContactInfoResData = res.data
          this.ruleInterfaceParams.forEach((iRule) => {
            if (iRule.supplierEnterpriseId == item.enterpriseId) {
              iRule['contactName'] = res.data.contactName || '即将补充'
              iRule['contactMobile'] = res.data.contactMobile || '即将补充'
              iRule['contactEmail'] = res.data.contactEmail || '即将补充'
              iRule['businessType'] = 1
            }
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    findById(id) {
      this.$loading()
      this.$API.supplierRegister.inviteDetail(id).then(async (res) => {
        this.$hloading()
        this.ruleForm = res.data
        // 公司值显示，工厂，品类回显
        this.ruleForm.supplierTenantId = [res.data.supplierTenantId]

        this.ruleForm.companyId = [res.data.applyerOrgId]
        this.ruleForm.remark = res.data.remark
        console.log(
          this.ruleForm.supplierTenantId,
          this.ruleForm.companyId,
          'this.ruleForm.supplierTenantId'
        )
        // await this.changeCompany({value:this.ruleForm.companyId});
        // 编辑：勾选数据
        this.getDataTreeStyle(res.data)
      })
      // .catch((err) => {
      //   this.$hloading();
      //   this.$toast({
      //     content: err.msg,
      //     type: "error",
      //   });
      // });
    },
    // 品类勾选已选中数据，已选显示所有树结构
    getDataTreeStyle(data) {
      if (data.extList.length > 0) {
        this.categoryTree.dataSource.forEach((item) => {
          item.pldisabled = false
          item.selected = false
          item.disabled = false
          item.categoryType = 1
        })
        let bol = data.extList[0].factoryCode
        if (bol) {
          this.radioVal = '0'
          this.addColumnDataexamine = JSON.parse(JSON.stringify(addColumnDataexamine))
          this.DataGridKey = this.randomString()
        } else {
          this.radioVal = '1'
          let arr = JSON.parse(JSON.stringify(addColumnDataexamine))
          for (let i = 0; i < arr.length; i++) {
            if (arr[i].field == 'factoryCode' || arr[i].field == 'factoryName') {
              arr.splice(i, 1)
              i--
            }
          }
          this.addColumnDataexamine = arr
          this.DataGridKey = this.randomString()
        }
        let arr = []
        let category = []
        let factory = []
        data.extList.forEach((item) => {
          arr.push(item.factoryId)
          category.push({
            categoryCode: item.categoryCode,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
            categoryType: item.categoryType
          })
          factory.push({
            factoryCode: item.factoryCode,
            factoryId: item.factoryId,
            factoryName: item.factoryName
          })
        })
        let categoryNameArr = category.map((item) => {
          let str = item.categoryType == 1 ? this.$t('原厂') : this.$t('代理')
          return `${item.categoryName}(${str})`
        })
        let categoryCode = category.map((item) => {
          return item.categoryCode
        })
        let categoryidArr = category.map((item) => {
          return item.categoryId
        })
        let factoryNames = factory.map((item) => {
          return item.factoryName
        })
        let factoryCodes = factory.map((item) => {
          return item.factoryCode
        })
        this.addDataSource = [
          {
            businessType: 1,
            categoryId: categoryidArr.join('，'),
            categoryName: categoryNameArr.join('，'),
            categoryCode: categoryCode,
            factoryName: factoryNames.join('，'),
            factoryCode: factoryCodes.join('，'),
            contactEmail: data.contactEmail,
            contactMobile: data.contactMobile,
            contactName: data.contactName,
            orgId: data.applyerOrgId,
            orgCode: data.applyerOrgCode,
            orgName: data.applyerOrgName,
            supplierEnterpriseCode: data.supplierEnterpriseCode,
            supplierInternalCode: data.supplierInternalCode,
            supplierEnterpriseId: data.supplierEnterpriseId,
            supplierEnterpriseName: data.supplierEnterpriseName,
            supplierTenantId: data.supplierTenantId[0]
          }
        ]
      }
    },
    //左侧地点==树点击
    nodeChecked(args) {
      if (this.type == 'view') {
        return
      }
      let _this = this
      if (args.length >= this.ruleForm.companyId.length) {
        if (this.plantlist.length > args.length) {
          //未选中==删
          let arr = this.plantlist.filter((item) => {
            return args.indexOf(item) == -1
          })
          _this.treeChildId.forEach((i, idx) => {
            if (i.nodeId == arr[0]) {
              _this.treeChildId.splice(idx, 1)
            }
          })
          if (_this.treeChildId.length == 0) {
            _this.categoryTree.dataSource.forEach((item) => {
              item.pldisabled = true
              item.selected = false
              item.disabled = true
              item.categoryType = 1
            })
          }
        } else if (this.plantlist.length < args.length) {
          let arr = args.filter((item) => {
            //选中==增
            return this.plantlist.indexOf(item) == -1
          })
          _this.treeViewFields.dataSource.forEach((i) => {
            i.nodeChild &&
              i.nodeChild.forEach((ic) => {
                if (ic.nodeId == arr[0]) {
                  _this.treeChildId.push(ic) //得到树类孩子的选择ID==也就是选择的工厂ID
                }
              })
          })
          if (_this.treeChildId.length == 1) {
            _this.categoryTree.dataSource.forEach((item) => {
              item.pldisabled = false
              item.selected = false
              item.disabled = false
              item.categoryType = 1
            })
          }
        }
        this.addfactory()
        this.addextList()
      }
      if (args.length < this.ruleForm.companyId.length) {
        this.plantlist = JSON.parse(JSON.stringify(this.ruleForm.companyId))
      } else {
        this.plantlist = JSON.parse(JSON.stringify(args))
      }
    },
    addfactory() {
      this.addDataSource.forEach((item) => {
        item['factoryName'] = ''
        item['factoryCode'] = ''
        item['factoryId'] = ''
      })
      this.treeChildId.forEach((item) => {
        this.addDataSource.forEach((i) => {
          if (item.parentId == i.orgId) {
            i['factoryName'] += item.siteName + ','
            i['factoryCode'] += item.siteCode + ','
            i['factoryId'] += item.id + ','
          }
        })
      })
      this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
    },
    nodeChecking(arg) {
      let bol = this.ruleForm.companyId.some((item) => {
        return item == arg.data[0].id
      })
      if (bol) {
        arg.cancel = true
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 以下是供应范围的接口
    // 获取当前用户信息
    getUserDetail() {
      this.$loading()
      setTimeout(() => {
        this.$hloading()
      }, 1000)
      return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        return companyOrg
      })
    },
    // 获取ORG02公司 数组格式
    getChildrenCompanyOrganization() {
      return this.$API.supplierInvitation['getChildrenCompanyOrganization2']({
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true
      })
    },

    // 选择公司
    async changeCompany(e) {
      this.addDataSource = []
      let _this = this
      this.currentCompanyParams = this.companyArrList.filter((iFtr) => {
        return e.value.includes(iFtr.id)
      })
      this.factoryArray = []
      this.plantcheckedNodes = []
      this.plantlist = []

      this.expandedNodes = []
      this.treeViewFields.dataSource = []
      let arr = []
      let tree = []
      if (this.radioVal == 0) {
        this.currentCompanyParams.forEach((item) => {
          arr.push(this.getSite(item.id))
        })
        let errArr = []
        let res = await Promise.all(arr)
        let currentCompanyParamsCopy = JSON.parse(JSON.stringify(this.currentCompanyParams))
        currentCompanyParamsCopy.forEach((item, index) => {
          if (res[index].length == 0) {
            let i = this.ruleForm.companyId.indexOf(item.id)
            this.ruleForm.companyId.splice(i, 1)
            this.currentCompanyParams.splice(i, 1)
            errArr.push(item.orgName)
          } else {
            let nodeChild = []
            res[index].forEach((iCld) => {
              nodeChild.push({
                parentId: item.id,
                parentName: item.orgName,
                parentCode: item.orgCode,
                nodeId: iCld.id,
                nodeText: iCld.siteName,
                ...iCld
              })
            })
            tree.push({
              nodeId: item.id,
              nodeText: item.orgName,
              nodeCode: item.orgCode,
              expanded: true,
              disabled: true,
              nodeChild
            })
            this.plantcheckedNodes.push(item.id)
            this.plantlist.push(item.id)
            //根据id展开树
            this.expandedNodes.push(item.id)
            if (this.ruleInterfaceParams.length > 0) {
              //TODO下面表格根据冲突再调整
              this.ruleInterfaceParams.forEach((ip) => {
                //供应商数据
                console.log(ip, 'ipip')
                this.addDataSource.push({
                  supplierTenantId: ip.supplierTenantId || '',
                  supplierEnterpriseId: ip.supplierEnterpriseId || '',
                  supplierEnterpriseCode: ip.supplierEnterpriseCode || '',
                  supplierInternalCode: ip.supplierInternalCode || '',
                  supplierEnterpriseName: ip.supplierEnterpriseName || '',
                  contactName: ip.contactName || '',
                  contactMobile: ip.contactMobile || '',
                  contactEmail: ip.contactEmail || '',
                  // companyId:  || "",
                  businessType: 1,
                  orgId: item.id || '',
                  orgName: item.orgName || '',
                  orgCode: item.orgCode || '',
                  categoryName: _this.categoryNameArr ? _this.categoryNameArr.join('，') : '',
                  categoryId: _this.categoryidArr ? _this.categoryidArr.join('，') : ''
                })
              })
            } else {
              this.addDataSource.push({
                supplierTenantId: '',
                supplierEnterpriseId: '',
                supplierEnterpriseName: '',
                supplierEnterpriseCode: '',
                supplierInternalCode: '',
                contactName: '',
                contactMobile: '',
                contactEmail: '',
                businessType: 1,
                orgId: item.id || '',
                orgName: item.orgName || '',
                orgCode: item.orgCode || '',
                categoryName: _this.categoryNameArr ? _this.categoryNameArr.join('，') : '',
                categoryId: _this.categoryidArr ? _this.categoryidArr.join('，') : ''
              })
            }
          }
        })
        this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
        this.treeViewFields.dataSource = JSON.parse(JSON.stringify(tree))
        this.companyIdKey = this.randomString()
        if (errArr.length > 0) {
          let str = errArr.join('、')
          this.$toast({ content: str + this.$t('工厂数据为空！'), type: 'error' })
        }
      } else {
        _this.categoryTree.dataSource.forEach((item) => {
          item.pldisabled = false
          item.selected = false
          item.disabled = false
          item.categoryType = 1
        })
        this.currentCompanyParams.forEach((item) => {
          //根据id展开树
          if (this.ruleInterfaceParams.length > 0) {
            this.ruleInterfaceParams.forEach((ip) => {
              //供应商数据
              this.addDataSource.push({
                supplierTenantId: ip.supplierTenantId || '',
                supplierEnterpriseId: ip.supplierEnterpriseId || '',
                supplierEnterpriseCode: ip.supplierEnterpriseCode || '',
                supplierInternalCode: ip.supplierInternalCode || '',
                supplierEnterpriseName: ip.supplierEnterpriseName || '',
                contactName: ip.contactName || '',
                contactMobile: ip.contactMobile || '',
                contactEmail: ip.contactEmail || '',
                // companyId:  || "",
                businessType: 1,
                orgId: item.id || '',
                orgName: item.orgName || '',
                orgCode: item.orgCode || '',
                categoryName: _this.categoryNameArr ? _this.categoryNameArr.join('，') : '',
                categoryId: _this.categoryidArr ? _this.categoryidArr.join('，') : ''
              })
            })
          } else {
            this.addDataSource.push({
              supplierTenantId: '',
              supplierEnterpriseId: '',
              supplierEnterpriseName: '',
              supplierEnterpriseCode: '',
              supplierInternalCode: '',
              contactName: '',
              contactMobile: '',
              contactEmail: '',
              businessType: 1,
              orgId: item.id || '',
              orgName: item.orgName || '',
              orgCode: item.orgCode || '',
              categoryName: _this.categoryNameArr ? _this.categoryNameArr.join('，') : '',
              categoryId: _this.categoryidArr ? _this.categoryidArr.join('，') : ''
            })
          }
        })
        this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
      }
    },
    getSite(id) {
      let query = {
        parentId: id || ''
      }
      this.$loading()
      return this.$API.supplierInvitationAdd['getSite'](query)
        .then((result) => {
          this.$hloading()
          if (result.code === 200 && !utils.isEmpty(result)) {
            result.data.forEach((item) => {
              item.selected = false
              item.activeClass = false
            })
            this.factoryArray.push(result.data)
            return result.data
          } else {
            this.factoryArray = []
            return []
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    /**
     * 获取品类
     */
    getCateGoryTree() {
      return this.$API.supplierRegister.productList({ level: 1 })
    },
    tips(item) {
      if (this.radioInviteShow) {
        if (item.pldisabled) {
          this.$toast({
            content: this.$t('请先选择工厂'),
            type: 'warning'
          })
        }
      }
    },
    addextList() {
      this.extList = []
      if (this.radioVal == 1) {
        this.currentCompanyParams.forEach((i) => {
          this.categoryData.forEach((item) => {
            this.extList.push({
              orgId: i.id || '',
              orgCode: i.orgCode || '',
              orgName: i.orgName || '',
              factoryId: '',
              factoryName: '',
              factoryCode: '',
              categoryCode: item.categoryCode || '',
              categoryId: item.id || '',
              categoryName: item.categoryName || '',
              categoryType: item.categoryType || '',
              relatedType: 1
            })
          })
        })
      } else {
        this.treeChildId.forEach((i) => {
          this.categoryData.forEach((item) => {
            this.extList.push({
              orgId: i.parentId || '',
              orgCode: i.parentCode || '',
              orgName: i.parentName || '',
              factoryId: i.nodeId || '',
              factoryName: i.nodeText || '',
              factoryCode: i.siteCode || '',
              categoryCode: item.categoryCode || '',
              categoryId: item.id || '',
              categoryName: item.categoryName || '',
              categoryType: item.categoryType || '',
              relatedType: 1
            })
          })
        })
      }
    },
    addCategory() {
      let categoryNameArr = this.categoryData.map((item) => {
        let str = item.categoryType == 1 ? this.$t('原厂') : this.$t('代理')
        return `${item.categoryName}(${str})`
      })
      let categoryidArr = this.categoryData.map((item) => {
        return item.id
      })
      this.addDataSource.forEach((i) => {
        i['categoryName'] = categoryNameArr.join('，')
        i['categoryId'] = categoryidArr.join('，')
      })
      this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource))
    },
    //右侧点击==品类
    selectData(state, item) {
      // // 品类
      if (state.checked) {
        item.selected = true
        item.disabled = true
        this.categoryData.push(item)
        this.addextList()
        this.addCategory()
      } else {
        item.selected = false
        item.disabled = false
        for (let i = 0; i < this.categoryData.length; i++) {
          if (this.categoryData[i].id == item.id) {
            this.categoryData.splice(i, 1)
            i--
          }
        }
        this.categoryData.push(item)
        this.addextList()
        this.addCategory()
      }
    },
    // 选择后，值改变 已选项也要改变  //右侧点击==代理/原厂
    changeCategory(e, item) {
      item.categoryType = e.value
      console.log(this.categoryTree.dataSource, 'categoryTree.dataSource')
      let str = e.value == 1 ? this.$t('原厂') : this.$t('代理')
      this.categoryData.map((i) => {
        if (i.id == item.id) {
          ;(i.categoryType = item.categoryType), (i.name = item.categoryName + '—' + str)
        }
      })

      this.factoryDataSelected.forEach((fitem) => {
        if (this.nowSelectedFactory.id == fitem.id) {
          fitem.children = []
          fitem.children = this.categoryData
        }
      })
      // 为已选项赋值所选品类
      this.selectTree.dataSource.forEach((ditem) => {
        if (ditem.id == this.currentCompany.id) {
          // this.selectTree.dataSource[dindex].children = this.factoryDataSelected
          ditem.children = []
          ditem.children = this.factoryDataSelected
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px 10px 10px;
}
/deep/ .select-container {
  height: 46px;
  padding: 5px;
}
/deep/ .e-input-group {
  padding-left: 5px;
}
.searchInput {
  /deep/ .e-input-group {
    border: none;
  }
}
.slider-panel-container {
  .slider-modal {
    width: 850px;
    border: none;
    .slider-header {
      height: 58px;
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }

    .add-rule-group {
      margin-top: 30px;
      height: 14px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      width: 100%;
      text-align: center;
      cursor: pointer;
    }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
.ruleForm {
  .fbox {
    display: flex;
  }
  .dialog-content-h4 {
    h4 {
      font-size: 14px;
      color: #292929;
      font-weight: bold;
      margin: 20px 0 10px 0;
    }
    .all-list-box {
      display: flex;
      background: #fff;
      border: 1px solid #e8e8e8;
      height: 30vh;
      border-radius: 4px;
    }
    .mt-radio-style {
      margin: 10px;
    }
    .company-tree-box {
      width: 300px;
      height: 100%;
      overflow-y: scroll;

      .factory-item {
        padding-left: 16px;
        width: 100%;
        height: 30px;
        line-height: 30px;
        cursor: pointer;
      }
      .category-content {
        display: flex;
        justify-content: left;
        height: 30px;
        line-height: 30px;
        padding: 0 16px;
        /deep/ .select-container {
          height: 30px;
          padding: 0;
        }
        .plname {
          width: 190px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-left: 10px;
        }
      }

      .active {
        background: #f5f6f9;
      }
    }

    .category-tree-box {
      flex: 1;
      width: 200px;
      height: 100%;
      border-left: 1px solid #e8e8e8;
      overflow: scroll;
      .category-content {
        display: flex;
        justify-content: space-between;
        height: 30px;
        line-height: 30px;
        padding: 0 16px;
        /deep/ .select-container {
          height: 30px;
          padding: 0;
        }
        .plname {
          width: 110px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .select-list-box {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-left: 20px;
      .select-list {
        padding: 10px 20px;
        height: 300px;
        overflow: auto;
        background: #fff;
        border: 1px solid #e8e8e8;
        width: 100%;
        border-radius: 4px;

        .select-tree {
          width: 100%;
        }
      }
    }
    .category-no-data {
      font-size: 14px;
      color: #9a9a9a;
      padding-top: 20px;
      text-align: center;
    }
    .category-delete-data {
      width: 100%;
      text-align: center;
      font-size: 14px;

      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      margin-top: 38px;
      cursor: pointer;
    }
    /deep/.mt-data-grid .e-grid .e-gridcontent {
      overflow: auto;
      max-height: 30vh;
    }
  }
}
</style>
