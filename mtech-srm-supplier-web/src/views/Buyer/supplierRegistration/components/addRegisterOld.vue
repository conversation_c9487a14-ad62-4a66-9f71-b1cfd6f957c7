<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>

      <div class="slider-content">
        <mt-form ref="ruleForm" class="ruleForm" :model="ruleForm" :rules="rules">
          <mt-row>
            <mt-col>
              <mt-form-item :label="$t('请选择供应商')" prop="supplierTenantId">
                <mt-input
                  v-show="type == 'view'"
                  :disabled="inputState"
                  v-model="ruleForm.supplierEnterpriseName"
                ></mt-input>
                <mt-multi-select
                  v-show="type != 'view'"
                  :disabled="inputState"
                  v-model="ruleForm.supplierTenantId"
                  :data-source="dataArr"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  :filtering="filteringCompany"
                  :placeholder="$t('请选择供应商')"
                  :fields="{ text: 'nameCode', value: 'id' }"
                  @change="changeSupplier"
                ></mt-multi-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="companyId" class="form-item" :label="$t('请选择准入公司')">
                <mt-multi-select
                  v-model="ruleForm.companyId"
                  :allow-filtering="true"
                  :show-clear-button="true"
                  :disabled="inputState || isDis"
                  :data-source="companyArrList"
                  :placeholder="$t('请选择准入公司')"
                  @change="changeCompany"
                  :fields="{ text: 'orgNameOrgCode', value: 'id' }"
                ></mt-multi-select>
              </mt-form-item>
            </mt-col>
          </mt-row>

          <mt-row>
            <mt-col>
              <mt-form-item :label="$t('备注')">
                <mt-input
                  :multiline="true"
                  :rows="2"
                  :disabled="inputState"
                  v-model="ruleForm.remark"
                  :placeholder="$t('备注')"
                  max-length="200"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <div class="dialog-content-h4 fbox" v-if="!!ruleForm.companyId">
            <div class="flex1" v-show="type == 'edit' || type == 'add'">
              <h4>{{ $t('是否按地点邀请') }}</h4>
              <div class="all-list-box">
                <div class="company-tree-box flex1">
                  <template v-if="treeViewFields.dataSource.length > 0">
                    <div class="demo-block">
                      <mt-treeView
                        ref="treeViewFieldsRef"
                        :fields="treeViewFields"
                        :show-check-box="true"
                        :expanded-nodes="expandedNodes"
                        @nodeOnCheck="nodeChecked"
                        @nodeSelected="nodeSelected"
                      ></mt-treeView>
                    </div>
                  </template>
                  <template v-else>
                    <p class="category-no-data">{{ $t('暂无数据') }}</p>
                  </template>
                </div>
                <div class="category-tree-box flex1">
                  <template v-if="categoryTree.dataSource.length > 0">
                    <div
                      class="category-content"
                      v-for="(item, index) in categoryTree.dataSource"
                      :key="item.id"
                      @click="tips(item)"
                    >
                      <mt-checkbox
                        :disabled="item.pldisabled"
                        :checked="item.selected"
                        @change="selectData($event, item, index)"
                      ></mt-checkbox>
                      <div class="plname">{{ item.categoryName }}</div>
                      <mt-select
                        v-model="item.categoryType"
                        :width="65"
                        :height="30"
                        :data-source="categoryDataArr"
                        :show-clear-button="false"
                        :disabled="item.disabled"
                        @change="changeCategory($event, item)"
                        :placeholder="$t('请选择')"
                      ></mt-select>
                    </div>
                  </template>
                  <p v-if="categoryTree.dataSource.length === 0" class="category-no-data">
                    {{ $t('暂无数据') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="dialog-content-h4 fbox">
            <div style="width: 100%">
              <h4>{{ $t('已选列表') }}</h4>
              <mt-DataGrid
                :data-source="addDataSource"
                :column-data="addColumnData"
                ref="dataGrid"
              ></mt-DataGrid>
            </div>
          </div>
        </mt-form>
      </div>
      <div class="slider-footer mt-flex" v-show="!inputState">
        <span @click="confirm">{{ $t('保存') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { validateMobile, validateMEmail } from '../config/index'
import { addColumnData } from '../config/column'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      addColumnData,
      addDataSource: [],
      selectTreeKey: 1,
      nowSelectedFactory: {}, //目前正操作的工厂
      factoryDataSelected: [], //选中的工厂集合
      categoryData: [], //品类选中集合
      categoryDataArr: [
        { text: this.$t('原厂'), value: 1 },
        { text: this.$t('代理'), value: 2 }
      ],
      dataArr: [],
      companyArrList: [],
      inputState: false,
      isDis: false,
      searchComData: [
        { text: this.$t('区域1'), value: 0 },
        { text: this.$t('区域2'), value: 1 }
      ],
      factoryArray: [],
      treeViewFields: {
        //checkBox树型参数
        dataSource: [],
        id: 'nodeId',
        text: 'nodeText',
        child: 'nodeChild'
      },
      expandedNodes: [],
      categoryTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      selectTree: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      // 已选品类
      selectCateGroyArr: {},
      // 已勾选的ID

      checkedNodes: [],
      checkedNodesObj: [],

      deepArr: [],

      parentNodeArr: [],
      parentNode: [],

      sltCompanyList: [],

      currentFactory: {},
      factoryId: -1,
      // 已选的公司对象 单选
      currentCompany: {
        id: ''
      },
      showCategoryTree: false, // treeview数据不更新。。
      showSelectTree: false, // treeview数据不更新。。
      ruleInterfaceParams: [],
      ruleForm: {
        id: '',
        supplierTenantId: '',
        supplierEnterpriseId: '',
        supplierEnterpriseCode: '',
        supplierEnterpriseName: '',
        contactName: '',
        contactMobile: '',
        contactEmail: '',
        remark: '',
        extList: '',
        companyId: '',
        categoryType: 1,
        businessType: 1
      },
      rules: {
        supplierTenantId: [
          {
            required: true,
            message: this.$t('请输入供应商企业全称'),
            trigger: 'blur'
          }
        ],
        contactName: [{ required: true, message: this.$t('请输入联系人'), trigger: 'blur' }],
        contactMobile: [{ required: true, validator: validateMobile, trigger: 'blur' }],
        contactEmail: [{ required: true, validator: validateMEmail, trigger: 'blur' }],
        extList: [
          {
            required: true,
            message: this.$t('请选择供应范围'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      searchInput: '',
      treeChildId: [],
      DataSupplierArrFtr: [] //供应商选择后的数据
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    record() {
      return this.modalData.record
    }
  },
  async created() {
    let companyOrg = await this.getUserDetail()
    if (!companyOrg || !companyOrg.id) {
      this.$hloading()
      // this.$toast({
      //   content: this.$t("获取当前组织信息失败，请重试"),
      //   type: "error",
      // });
      return
    }
    this.getChildrenCompanyOrganization(companyOrg.id)
  },
  async mounted() {
    this.$refs.treeViewFieldsRef.$ejsRef.expandAll()
    console.log(this.$refs.treeViewFieldsRef.$ejsRef.expandAll(), 'this.$refs.treeViewFieldsRef')
    this.filteringCompany = utils.debounce(this.filteringCompany, 1000)
    if (this.type == 'add') {
      // 根据名字搜索供应商企业全称接口 新增一开始不访问，输入要查询的再调用
      // await this.fuzzyQueryTenantByName();
      this.ruleForm.id = ''
      this.isDis = false
      this.inputState = false
      return
    } else if (this.type == 'edit') {
      //编辑赋值
      this.searchInput = this.record[0].supplierEnterpriseName
      // 根据名字搜索供应商企业全称接口
      await this.fuzzyQueryTenantByName()
    }
    this.ruleForm.id = this.record[0].id
    // 查询详情
    this.findById(this.ruleForm.id)
    if (this.type == 'edit') {
      this.isDis = true
      this.inputState = false
    } else if (this.type == 'view') {
      //查看：禁止编辑
      this.inputState = true
    }
  },
  methods: {
    // 查询供应商企业全称下拉框信息
    async filteringCompany(e) {
      console.log(e, 'eeeee')
      if (e.text.trim() == '') {
        return
      }
      this.searchInput = e.text
      await this.fuzzyQueryTenantByName()
      e.updateData(this.dataArr)
    },
    async fuzzyQueryTenantByName() {
      let name = this.searchInput
      this.$loading()
      await this.$API.supplierRegister
        .fuzzyQueryTenantByName(name)
        .then((res) => {
          this.$hloading()
          this.dataArr = res.data
          this.dataArr.forEach((i) => {
            i['nameCode'] = i.name + '：' + i.code
          })
          console.log(this.dataArr, 'this.dataArr')
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    changeSupplier(str) {
      this.DataSupplierArrFtr = this.dataArr.filter((iFtr) => {
        return [...str.value].includes(iFtr.id)
      }) //得到选择的数据
      this.ruleInterfaceParams = []
      this.addDataSource = []
      this.DataSupplierArrFtr.forEach((i) => {
        this.ruleInterfaceParams.push({
          supplierTenantId: i.id,
          supplierEnterpriseId: i.enterpriseId,
          supplierEnterpriseCode: i.code,
          supplierEnterpriseName: i.name
        })
        // if(this.currentCompanyParams){ //TODO下面表格根据冲突再调整
        //   this.currentCompanyParams.forEach( ip => {
        //     this.ruleInterfaceParams.push({
        //       contactName = this.queryContactInfoResData.contactName,
        //       contactMobile = this.queryContactInfoResData.contactMobile,
        //       contactEmai = this.queryContactInfoResData.contactEmail,
        //     })
        //     // this.addDataSource.push({
        //     //   name:i.name,
        //     //   code:i.code,

        //     // })
        //   })
        // }else{
        //   // this.addDataSource.push({
        //   //   name:i.name,
        //   //   code:i.code,
        //   // })
        // }
        this.queryContactInfo(i.enterpriseId) // 获取一下之前注册过的联系人信息
      })
    },
    async queryContactInfo(id) {
      this.$loading()
      await this.$API.supplierRegister
        .queryContactInfo({ supplierEnterpriseId: id })
        .then((res) => {
          this.$hloading()
          if (res.data) {
            this.queryContactInfoResData = res.data
            this.ruleInterfaceParams &&
              this.ruleInterfaceParams.forEach((i) => {
                i['contactName'] = res.data.contactName
                i['contactMobile'] = res.data.contactMobile
                i['contactEmail'] = res.data.contactEmail
              })
            console.log(this.ruleInterfaceParams, 'this.ruleFormthis.ruleForm')
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })

      // this.addDataSource = this.ruleForm;
      // this.addDataSource = JSON.parse(JSON.stringify(this.addDataSource));
    },
    findById(id) {
      this.$loading()
      this.$API.supplierRegister
        .inviteDetail(id)
        .then(async (res) => {
          this.$hloading()
          this.ruleForm = res.data
          // 公司值显示，工厂，品类回显
          this.ruleForm.companyId = res.data.applyerOrgId
          await this.changeCompany(res.data)
          // 编辑：勾选数据
          this.getDataTreeStyle(res.data)
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 品类勾选已选中数据，已选显示所有树结构
    getDataTreeStyle(data) {
      // 组成树结构
      // 已选中的公司
      // let company = {
      //   orgId: this.currentCompany.id,
      //   orgName: this.currentCompany.name,
      //   orgCode: this.currentCompany.orgCode,
      // };
      // 已选中的工厂
      // 全部的工厂对比 详情中返回的之前提交的工厂，勾选已选中的工厂
      this.factoryArray.forEach((item) => {
        data.extList.forEach((eitem) => {
          if (eitem.factoryId == item.id) {
            let factory = {}
            item.activeClass = true
            item.selected = true
            factory.id = item.id
            factory.name = item.siteName
            factory.factoryId = item.id
            factory.factoryName = item.siteName
            factory.factoryCode = item.siteCode
            // 已选中的工厂赋值 extlist会有多个相同的公厂，需要去重
            let state = true
            if (this.factoryDataSelected.length == 0) {
              this.factoryDataSelected.push(factory)
            } else {
              this.factoryDataSelected.forEach((fitem) => {
                if (fitem.id == eitem.factoryId) {
                  state = false
                }
              })
              if (state) {
                this.factoryDataSelected.push(factory)
              }
            }
          }
        })
      })
      // 自动取第一个工厂数据进行选中，activeClass = true
      this.nowSelectedFactory = this.factoryDataSelected[0]
      // 已选中的品类
      this.categoryTree.dataSource.forEach((item) => {
        if (this.factoryDataSelected.length > 0) {
          item.pldisabled = false
          item.selected = false
          item.disabled = false
        } else {
          item.pldisabled = true
          item.selected = true
          item.disabled = true
        }
      })

      // 已选中的品类
      data.extList.forEach((eitem) => {
        this.categoryTree.dataSource.forEach((item) => {
          // id selected disabled
          if (item.id == eitem.categoryId) {
            item.categoryType = parseInt(eitem.categoryType == '' ? 1 : eitem.categoryType)
            item.selected = true
            // item.disabled = true;
            item.pldisabled = false
            let str = eitem.categoryType == 1 ? this.$t('原厂') : this.$t('代理')
            let obj = {
              id: eitem.categoryId,
              name: eitem.categoryName + '—' + str,
              categoryId: eitem.categoryId,
              categoryCode: eitem.categoryCode,
              categoryName: eitem.categoryName,
              categoryType: eitem.categoryType,
              factoryId: eitem.factoryId
            }
            this.categoryData.push(obj)
          }
        })
      })

      // // 品类和工厂组装 this.factoryDataSelected(已选中工厂) this.categoryData(已选中品类) 根据factoryId组装
      // this.factoryDataSelected.forEach((fitem) => {
      //   fitem.expanded = true;
      //   fitem.children = [];
      //   this.categoryData.forEach((citem) => {
      //     if (fitem.factoryId == citem.factoryId) {
      //       fitem.children.push(citem);
      //     }
      //   });
      // });

      // let sltTreeData = [];
      // sltTreeData.push({
      //   ...company,
      //   id: company.orgId,
      //   name: company.orgName,
      //   expanded: true,
      //   children: this.factoryDataSelected,
      // });
      // this.$set(this.selectTree, "dataSource", sltTreeData);
    },

    //左侧地点==树点击
    nodeChecked(args) {
      this.treeChildId = []
      this.treeViewFields.dataSource.forEach((i) => {
        i.nodeChild &&
          i.nodeChild.forEach((ic) => {
            if (args.includes(ic.nodeId)) {
              this.treeChildId.push(ic.nodeId) //得到树类孩子的选择ID==也就是选择的工厂ID
            }
          })
      })
      this.categoryTree.dataSource.forEach((item) => {
        item.pldisabled = this.treeChildId ? false : true
        item.selected = this.treeChildId ? false : true
        item.disabled = this.treeChildId ? false : true
        item.categoryType = 1
      })
    },
    nodeSelected(args) {
      console.log('nodeSelected ', args)
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 以下是供应范围的接口
    // 获取当前用户信息
    getUserDetail() {
      this.$loading()
      setTimeout(() => {
        this.$hloading()
      }, 1000)
      return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        return companyOrg
      })
    },
    // 获取ORG02公司 数组格式
    getChildrenCompanyOrganization() {
      // this.$API.supplierInvitation["getChildrenCompanyOrganization"]({
      //   organizationId,
      // }).then((result) => {
      this.$API.supplierInvitation['getChildrenCompanyOrganization2']({
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true
      }).then((result) => {
        this.$hloading()
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          let companyArrList = result.data.filter((item) => {
            return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
          })
          this.companyArrList = companyArrList
          this.companyArrList.forEach((i) => {
            i['orgNameOrgCode'] = i.orgName + '：' + i.orgCode
          })
          console.log(this.companyArrList, 'this.companyArrList')
        } else {
          this.companyArrList = []
        }
      })
    },

    // 选择公司
    async changeCompany(e) {
      console.log(this.ruleForm.companyId)
      this.ruleForm.companyId = []
      // if (e.itemData) {
      //   itemData = e.itemData;
      // } else {
      // if (e.value) {
      this.currentCompanyParams = this.companyArrList.filter((iFtr) => {
        return e.value.includes(iFtr.id)
      })
      // }
      // else {
      //   let item = e.extList[0];
      //   itemData = {
      //     id: item.orgId,
      //     name: item.orgName,
      //     orgName: item.orgName,
      //     orgCode: item.orgCode,
      //     tenantId: e.tenantId,
      //   };
      // }
      console.log(this.currentCompanyParams, 'currentCompanyParamscurrentCompanyParams')
      // this.$refs.treeViewFieldsRef.$ejsRef.expandAll()
      this.factoryArray = []
      this.expandedNodes = []
      this.treeViewFields.dataSource = []
      this.currentCompanyParams.forEach((i) => {
        i['name'] = i.orgName
        this.ruleForm.companyId.push(i.id)
        this.getSite(i.id).then((res) => {
          i['children'] = res
          let nodeChild = []
          if (i.children.length > 0) {
            //公司下的工厂==给工厂树用
            i.children.forEach((iCld) => {
              nodeChild.push({
                nodeId: iCld.id,
                nodeText: iCld.siteName
              })
            })
          } else {
            nodeChild.push({
              nodeId: '',
              nodeText: '暂无数据'
            })
            // this.$refs.treeViewFieldsRef.disableNodes(nodes)
          }
          this.treeViewFields.dataSource.push({
            //工厂==树
            nodeId: i.id,
            nodeText: i.name,
            expanded: true,
            nodeChild
          })
          this.expandedNodes.push(i.id) //根据id展开树
        })
      })

      // let dataSource =
      //   this.$set(this.treeViewFields, "dataSource", dataSource);
      console.log(this.treeViewFields, 'this.treeViewFieldsthis.treeViewFields')
      console.log(this.factoryArray, 'this.factoryArray')
      console.log(this.currentCompanyParams, 'this.currentCompanyParams')
      // itemData.name = itemData.orgName;
      // 新增
      // let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompanyParams.id);
      // index === -1 && this.sltCompanyList.push(this.currentCompany);
      // // 如果当前客户没有选择工厂品类 就切换另一个客户，那么这个客户则不保留在已选项里面，因为也新增不了
      // this.selectTree.dataSource.forEach((item, index) => {
      //   if (item.id != e.itemData.id && item.children.length == 0) {
      //     this.selectTree.dataSource.splice(index, 1);
      //   }
      //   this.sltCompanyList.forEach((sitem, sindex) => {
      //     if (item.id == sitem.id && item.children.length == 0) {
      //       this.sltCompanyList.splice(sindex, 1);
      //     }
      //   });
      // });

      // 获取公司对应的工厂 todo 接口缺失 ==== (是否按地点邀请)

      console.log(this.$refs.treeViewFieldsRef, 'this.$refs.treeViewFieldsRes')
      // if (this.factoryArray.length === 0) {
      //   index = 0;
      //   this.$toast({ content: this.$t("工厂数据为空！"), type: "error" });
      //   // 重置 品类
      //   this.categoryTree = {
      //     dataSource: [],
      //     id: "id",
      //     text: "name",
      //     child: "children",
      //   };
      //   return;
      // }

      // // 渲染 公司名称 已选树 start
      // this.showSelectTree = false;
      // if (index === -1) {
      //   // 新增
      //   let dataSource = this.selectTree.dataSource;
      //   dataSource = [
      //     ...dataSource,
      //     {
      //       id: itemData.id,
      //       name: itemData.orgName,
      //       code: itemData.orgCode,
      //       expanded: true,
      //       children: [],
      //     },
      //   ];
      //   this.$set(this.selectTree, "dataSource", dataSource);
      // }
      // this.categoryData = []; //切换公司，清空所选品类树
      // setTimeout(() => {
      //   this.showSelectTree = true;
      // }, 200);
      // 渲染已选树 end
      console.log(this.factoryArray, 'this.factoryArray[0][0]')
      // this.currentFactory = !!this.factoryArray[0] && !utils.isEmpty(this.factoryArray[0])? this.factoryArray[0]: {};
      // 根据公司获取品类树 接口还没有 暂时用账户获取品类数 getProdcutTree
      // this.factoryArray.forEach( i => {
      this.categoryTree.dataSource = await this.getCateGoryTree('', true)
      // })
      console.log(
        this.categoryTree.dataSource,
        'this.categoryTree.dataSourcethis.categoryTree.dataSource123'
      )
      let cindex = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)
      console.log(
        this.factoryArray[0],
        this.sltCompanyList[cindex].children,
        ' this.sltCompanyList[cindex].children'
      )
      this.sltCompanyList[cindex].children = [this.factoryArray[0]]
    },

    /**
     * 根据公司获取工厂==接口
     */
    getSite(id) {
      // console.log(this.currentCompany,"this.currentCompany");
      let query = {
        parentId: id || ''
      }
      this.$loading()
      return this.$API.supplierInvitationAdd['getSite'](query)
        .then((result) => {
          this.$hloading()
          if (result.code === 200 && !utils.isEmpty(result)) {
            result.data.forEach((item) => {
              item.selected = false
              item.activeClass = false
            })
            this.factoryArray.push(result.data)
            return result.data
          } else {
            this.factoryArray = []
            return []
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    /**
     * 获取品类
     */
    getCateGoryTree(factoryObjectId, isInit = false) {
      if (isInit) {
        this.factoryId = factoryObjectId
      }
      this.$loading()
      return this.$API.supplierRegister
        .productList({ level: 1 })
        .then((res) => {
          this.$hloading()
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            res.data.forEach((item) => {
              item.categoryType = 1
              item.selected = false
              item.disabled = true //下拉框的禁用
              item.pldisabled = true //选择框的禁用：初始化因为没有选择工厂 品类禁止勾选
            })
            return res.data
            // this.categoryTree = Object.assign({},this.categoryTree,"dataSource",res.data);
          } else {
            return []
            // this.categoryTree = Object.assign({},this.categoryTree,"dataSource",[]);
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    // 初始化品类的勾选
    initSelectTree() {
      const { id: currentCompanyId } = this.currentCompany
      let index = this.checkedNodesObj.findIndex(
        (item) => item.id === currentCompanyId + '_' + this.factoryId
      )
      this.checkedNodes = index > -1 ? this.checkedNodesObj[index].nodes : []
    },

    // 选择工厂
    selectFactory(state, item) {
      item.selected = state.checked
      // 所选中的公司
      let company = {
        orgId: this.currentCompany.id,
        orgName: this.currentCompany.name,
        orgCode: this.currentCompany.orgCode
      }
      // 选中工厂，追加一条
      let factory = {}
      if (state.checked) {
        // 工厂切换勾选，品类集合清空
        this.categoryData = []
        // 最后选中的数据放在nowSelectedFactory,对应之后品类的操作
        this.nowSelectedFactory = item
        // 选中当前项，item.selected 为true，背景颜色加深，其他项颜色不变
        this.factoryArray.forEach((fitem) => {
          if (item.id == fitem.id) {
            fitem.activeClass = true
          } else {
            fitem.activeClass = false
          }
        })
        factory.factoryId = item.id
        factory.factoryName = item.siteName
        factory.factoryCode = item.siteCode
        // 工厂集合
        this.factoryDataSelected.push({
          ...factory,
          id: factory.factoryId,
          name: factory.factoryName
        })
        this.factoryDataSelected.map((i) => {
          i.expanded = true
        })
        this.categoryTree.dataSource.forEach((item) => {
          item.pldisabled = false
          item.selected = false
          item.disabled = false
          item.categoryType = 1
        })
      } else {
        this.factoryDataSelected.forEach((sitem, sindex) => {
          if (item.id == sitem.id) {
            this.factoryDataSelected.splice(sindex, 1)
          }
        })
        if (this.factoryDataSelected.length == 0) {
          this.categoryTree.dataSource.forEach((item) => {
            item.pldisabled = true
            item.selected = false
            // item.disabled = true;
            item.categoryType = 1
          })
        } else {
          this.nowSelectedFactory = this.factoryDataSelected[0]
          this.factoryArray.forEach((item) => {
            if (item.id == this.nowSelectedFactory.id) {
              item.activeClass = true
            } else {
              item.activeClass = false
            }
          })
          this.categoryTree.dataSource.forEach((item) => {
            item.pldisabled = false
            item.disabled = false
            item.selected = false
            item.categoryType = 1
          })
        }
      }
      // 已选项里面对比同一公司下工厂放一起
      this.selectTree.dataSource.forEach((item) => {
        if (item.id == company.orgId) {
          item.children = []
          item.children = this.factoryDataSelected
        }
      })
    },

    tips(item) {
      if (item.pldisabled) {
        this.$toast({
          content: this.$t('请先选择工厂'),
          type: 'warning'
        })
      }
    },

    // 选择品类
    selectData(state, item) {
      // 已选中的公司
      let company = {
        orgId: this.currentCompany.id,
        orgName: this.currentCompany.name,
        orgCode: this.currentCompany.orgCode
      }
      // 品类
      if (state.checked) {
        item.selected = true
        // 选中时 下拉选项 禁用，反则不禁用
        // item.disabled = true;
        // 添加数据
        let str = item.categoryType == 1 ? this.$t('原厂') : this.$t('代理')
        let obj = {
          id: item.id,
          name: item.categoryName + '—' + str,
          categoryId: item.id,
          categoryCode: item.categoryCode,
          categoryName: item.categoryName,
          categoryType: item.categoryType
        }
        // this.categoryData.push(obj);
        if (this.categoryData.length == 0) {
          this.categoryData.push(obj)
        } else {
          let num = 0
          this.categoryData.forEach((item) => {
            if (item.id == obj.id) {
              num++
            }
          })
          if (num == 0) {
            this.categoryData.push(obj)
          }
        }
      } else {
        item.selected = false
        item.disabled = false
        this.categoryData.forEach((cm, cn) => {
          if (cm.id == item.id) {
            this.categoryData.splice(cn, 1)
          }
        })
      }
      // id,name 为了树形展示，其他各自name为了传递给后台
      this.factoryDataSelected.forEach((fitem) => {
        if (this.nowSelectedFactory.id == fitem.id) {
          fitem.children = []
          fitem.children = this.categoryData
        }
      })
      // 为已选项赋值所选品类
      this.selectTree.dataSource.forEach((ditem) => {
        if (ditem.id == company.orgId) {
          // this.selectTree.dataSource[dindex].children = this.factoryDataSelected
          ditem.children = []
          ditem.children = this.factoryDataSelected
        }
      })
      // 刷新已选项
      // this.selectTreeKey ++;
    },

    // 选择后，值改变 已选项也要改变
    changeCategory(e, item) {
      item.categoryType = e.value
      let str = e.value == 1 ? this.$t('原厂') : this.$t('代理')
      this.categoryData.map((i) => {
        if (i.id == item.id) {
          ;(i.categoryType = item.categoryType), (i.name = item.categoryName + '—' + str)
        }
      })

      this.factoryDataSelected.forEach((fitem) => {
        if (this.nowSelectedFactory.id == fitem.id) {
          fitem.children = []
          fitem.children = this.categoryData
        }
      })
      // 为已选项赋值所选品类
      this.selectTree.dataSource.forEach((ditem) => {
        if (ditem.id == this.currentCompany.id) {
          // this.selectTree.dataSource[dindex].children = this.factoryDataSelected
          ditem.children = []
          ditem.children = this.factoryDataSelected
        }
      })
    },

    nodeCheckedCategory(args) {
      /**
       * 返回的data数组中，第一个应该是当前点击的currentNode
       * 如果当前动作是check,如果currentNode的hasChildren是true，找到他下面的叶子节点，禁用
       * 如果currentNode的hasChildren是false，，选中的nodes里如果有hasChildren是true的，禁用他的子节点
       * 如果当前动作是uncheck：取消所有的禁用，...
       */
      this.showSelectTree = false
      const treeViewRef = this.$refs.treeViewCategory.ejsRef
      const { action, data } = args
      let currentNode = data && data.length && data[0]
      let disabledNodes = treeViewRef.getDisabledNodes()
      let allCheckedNodes = treeViewRef.getAllCheckedNodes()
      // 保存当前公司的品类选择
      let index = this.checkedNodesObj.findIndex(
        (item) => item.id === this.currentCompany.id + '_' + this.factoryId
      )

      // 叶子节点的禁用逻辑
      if (action === 'check' && currentNode.hasChildren) {
        let currentTree = treeViewRef.getTreeData(currentNode.id)
        let leafs = this.getLeaf(currentTree)
        treeViewRef.disableNodes(leafs)

        index === -1 &&
          this.checkedNodesObj.push({
            id: this.currentCompany.id + '_' + this.factoryId,
            nodes: allCheckedNodes
          })
      } else {
        action === 'uncheck' && treeViewRef.enableNodes(disabledNodes)
        let sltNodes = allCheckedNodes.map((id) => {
          return treeViewRef.getNode(id)
        })
        sltNodes.forEach((node) => {
          if (node.hasChildren) {
            let currentTree = treeViewRef.getTreeData(node.id)
            let leafs = this.getLeaf(currentTree)
            treeViewRef.disableNodes(leafs)
          }
        })

        index > -1 && this.checkedNodesObj.splice(index, 1)
      }

      let nodes = this.getSltNodes()
      if (!!nodes && nodes.length > 0) {
        let factoryCategroy = {
          ...this.currentFactory,
          id: this.currentFactory.id,
          name: this.currentFactory.siteName,
          children: nodes
        }

        let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)

        let cIndex = this.sltCompanyList[index].children.findIndex(
          (item) => item.id === this.factoryId
        )

        this.sltCompanyList[index].children[cIndex] = {
          ...factoryCategroy
        }

        let sltTreeData = this.sltCompanyList.map((node) => {
          let children = node.children
          if (node.children) {
            children = children
              .filter((item) => item.children && item.children.length)
              .map((vNode) => {
                return {
                  ...vNode,
                  expanded: true
                }
              })
          }
          return {
            id: node.id,
            name: node.name,
            expanded: true,
            children
          }
        })
        this.$set(this.selectTree, 'dataSource', sltTreeData)
        setTimeout(() => {
          this.showSelectTree = true
        }, 200)
      } else {
        // 当前的工厂下没选择品类 清空当前工厂下的品类数组
        let factoryCategroy = {
          ...this.currentFactory,
          id: this.currentFactory.id,
          name: this.currentFactory.siteName,
          children: []
        }

        let index = this.sltCompanyList.findIndex((item) => item.id === this.currentCompany.id)

        let cIndex = this.sltCompanyList[index].children.findIndex(
          (item) => item.id === this.factoryId
        )

        this.sltCompanyList[index].children[cIndex] = {
          ...factoryCategroy
        }

        let sltTreeData = this.sltCompanyList.map((node) => {
          let children = node.children
          if (node.children) {
            children = children
              .filter((item) => item.children && item.children.length) // 只收集 工厂下面有品类得
              .map((vNode) => {
                return {
                  ...vNode,
                  expanded: true
                }
              })
          }
          return {
            id: node.id,
            name: node.name,
            expanded: true,
            children
          }
        })
        this.$set(this.selectTree, 'dataSource', sltTreeData)
        setTimeout(() => {
          this.showSelectTree = true
        }, 200)
      }
    },

    // 清除选择
    clearSelect() {
      // 清除选择树
      this.selectTree = {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      }
      // 选中的工厂清空
      this.factoryDataSelected = []
      // 清除勾选的状态 start
      this.checkedNodesObj = []
      this.checkedNodes = []
      this.sltCompanyList = []
      this.categoryData = []
      // 工厂的勾选
      this.factoryArray.forEach((item) => {
        item.activeClass = false
        item.selected = false
      })
      // 品类的勾选
      this.categoryTree.dataSource.forEach((item) => {
        item.pldisabled = true
        item.disabled = true
        item.selected = false
        item.categoryType = 1
      })
      // 清除勾选的状态 end

      // 恢复到当前公司选择
      let currentCompany = this.currentCompany
      this.sltCompanyList.push(this.currentCompany)

      // 渲染 公司名称 已选树 start
      this.showSelectTree = false

      this.$set(this.selectTree, 'dataSource', [
        {
          id: currentCompany.id,
          name: currentCompany.name,
          expanded: true,
          children: []
        }
      ])
      setTimeout(() => {
        this.showSelectTree = true
      }, 200)
      // 渲染已选树 end
    },

    /**
     * 选中的节点，保存为树形
     * 非叶子节点的isChecked表示该节点是否选中（他的叶子节点被选中了部分，则是false）
     */
    getSltNodes() {
      const treeViewRef = this.$refs.treeViewCategory.ejsRef
      let checkedId = treeViewRef.getAllCheckedNodes()
      let sltParentNode = []
      if (checkedId && checkedId.length) {
        // const { dataSource } = this.categoryTree;
        let dataSource = JSON.parse(JSON.stringify(this.categoryTree.dataSource))
        let tree = this.setCheckStatus(dataSource, checkedId)

        const getSltParentNode = (tree) => {
          if (tree && tree.length) {
            tree.forEach((node) => {
              if (node.isChecked) {
                delete node.children // 只收集父节点
                sltParentNode.push(node)
              } else if (node.children && node.children.length) {
                getSltParentNode(node.children)
              }
            })
          }
        }
        getSltParentNode(tree)
      }
      return sltParentNode || []
    },

    // 加状态 isChecked
    setCheckStatus(tree, checkedId) {
      if (tree && tree.length) {
        tree.forEach((node) => {
          node.isChecked = false
          if (node.children && node.children.length) {
            let childrenIds = node.children.map((child) => {
              return child.id
            })
            node.isChecked = this.isContainArr(checkedId, childrenIds)
            this.setCheckStatus(node.children, checkedId)
          } else {
            let index = checkedId.findIndex((id) => id === node.id)
            index > -1 && (node.isChecked = true)
          }
        })
      }
      return tree
    },

    isContainArr(parent, child) {
      return child.every((item) => {
        return parent.some((v) => {
          return item == v
        })
      })
    },

    // 获取叶子节点的id
    getLeaf(tree) {
      const leaf = []
      const getChild = (tree) => {
        tree &&
          tree.forEach((item) => {
            if (item.children && item.children.length) {
              getChild(item.children)
            } else {
              let index = leaf.findIndex((leafItem) => leafItem === item.id)
              index === -1 && leaf.push(item.id)
            }
          })
      }
      getChild(tree)
      return leaf
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.sltCompanyList.length === 0) {
            this.$toast({ content: this.$t('请选择供应商'), type: 'error' })
            return
          }
          this.$loading()
          let floatData = this.floatCompanyList(this.selectTree.dataSource)
          let num = 0
          floatData.forEach((fm) => {
            let id = fm.customerCategoryId ? fm.customerCategoryId : fm.categoryId
            if (!id) {
              num++
            }
          })
          if (num == 0) {
            this.ruleForm.extList = floatData
            if (this.type == 'add') {
              this.$API.supplierRegister
                .addInvite(this.ruleForm)
                .then((res) => {
                  this.$hloading()
                  this.$toast({ content: res.msg, type: 'success' })
                  this.$emit('confirm-function')
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            } else if (this.type == 'edit') {
              this.$API.supplierRegister
                .updateInvite(this.ruleForm)
                .then((res) => {
                  this.$hloading()
                  this.$toast({ content: res.msg, type: 'success' })
                  this.$emit('confirm-function')
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            }
          } else {
            this.$hloading()
            this.$toast({
              content: this.$t('请勾选对应工厂下的品类'),
              type: 'warning'
            })
          }
        }
      })
    },

    // 扁平化树
    floatCompanyList(treeData) {
      let finalData = []
      let companyData = {}
      let factoryData = {}
      let categroyData = {}
      treeData.forEach((item) => {
        companyData = {
          orgId: item.id,
          orgCode: item.code,
          orgName: item.name
        }
        if (!!item.children && item.children.length > 0) {
          let categroyArray = item.children.filter(
            (CCItem) => !!CCItem.children && !!CCItem.children.length > 0
          )
          if (categroyArray.length > 0) {
            categroyArray.forEach((cItem) => {
              factoryData = {
                factoryId: cItem.id,
                factoryName: cItem.name,
                factoryCode: cItem.factoryCode
              }
              cItem.children.forEach((CCItem) => {
                categroyData = {
                  categoryCode: CCItem.categoryCode,
                  categoryId: CCItem.id,
                  categoryName: CCItem.categoryName,
                  categoryType: CCItem.categoryType
                }

                finalData.push({
                  ...companyData,
                  ...factoryData,
                  ...categroyData,
                  // 类型 0:公司 1:品类
                  relatedType: 1
                })
              })
            })
          } else {
            finalData.push({
              ...companyData,
              factoryId: '',
              factoryName: '',
              factoryCode: '',

              categoryCode: '',
              categoryId: '',
              categoryName: '',

              relatedType: 0
            })
          }
        } else {
          finalData.push({
            ...companyData,
            factoryId: '',
            factoryName: '',
            factoryCode: '',

            categoryCode: '',
            categoryId: '',
            categoryName: '',

            relatedType: 0
          })
        }
      })

      return finalData
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px 10px 10px;
}
/deep/ .select-container {
  height: 46px;
  padding: 5px;
}
/deep/ .e-input-group {
  padding-left: 5px;
}
.searchInput {
  /deep/ .e-input-group {
    border: none;
  }
}
.slider-panel-container {
  .slider-modal {
    width: 850px;
    border: none;
    .slider-header {
      height: 58px;
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }

    .add-rule-group {
      margin-top: 30px;
      height: 14px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0, 70, 156, 1);
      width: 100%;
      text-align: center;
      cursor: pointer;
    }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
.ruleForm {
  .fbox {
    display: flex;
  }
  .dialog-content-h4 {
    h4 {
      font-size: 14px;
      color: #292929;
      font-weight: bold;
      margin: 20px 0 10px 0;
    }
    .all-list-box {
      display: flex;
      background: #fff;
      border: 1px solid #e8e8e8;
      height: 300px;
      border-radius: 4px;
    }
    .company-tree-box {
      width: 300px;
      height: 100%;
      overflow-y: scroll;

      .factory-item {
        padding-left: 16px;
        width: 100%;
        height: 30px;
        line-height: 30px;
        cursor: pointer;
      }
      .category-content {
        display: flex;
        justify-content: left;
        height: 30px;
        line-height: 30px;
        padding: 0 16px;
        /deep/ .select-container {
          height: 30px;
          padding: 0;
        }
        .plname {
          width: 190px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-left: 10px;
        }
      }

      .active {
        background: #f5f6f9;
      }
    }

    .category-tree-box {
      flex: 1;
      width: 200px;
      height: 100%;
      border-left: 1px solid #e8e8e8;
      overflow: scroll;
      .category-content {
        display: flex;
        justify-content: space-between;
        height: 30px;
        line-height: 30px;
        padding: 0 16px;
        /deep/ .select-container {
          height: 30px;
          padding: 0;
        }
        .plname {
          width: 110px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .select-list-box {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-left: 20px;
      .select-list {
        padding: 10px 20px;
        height: 300px;
        overflow: auto;
        background: #fff;
        border: 1px solid #e8e8e8;
        width: 100%;
        border-radius: 4px;

        .select-tree {
          width: 100%;
        }
      }
    }
    .category-no-data {
      font-size: 14px;
      color: #9a9a9a;
      padding-top: 20px;
      text-align: center;
    }
    .category-delete-data {
      width: 100%;
      text-align: center;
      font-size: 14px;

      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      margin-top: 38px;
      cursor: pointer;
    }
  }
}
</style>
