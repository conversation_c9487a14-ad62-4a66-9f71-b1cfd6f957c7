<template>
  <!-- 评分配置页面 -->
  <div class="score-setting">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      :current-tab="currentTab"
      @handleSelectTab="handleSelectTab"
    >
      <!-- 建议评审清单 -->
      <tab-dimension slot="slot-0"></tab-dimension>
      <!-- 评审计划 -->
      <tab-target slot="slot-1"></tab-target>
      <!-- 评审清单 -->
      <tab-template slot="slot-2" :template-type-list="templateTypeList"></tab-template>
      <!-- 我的评分 -->
      <tab-myscore slot="slot-3"></tab-myscore>
    </mt-template-page>
  </div>
</template>

<script>
import { masterInit } from './components/template/config/index'
export default {
  components: {
    //建议评审清单
    tabDimension: () => import('./components/dimension'),
    //评审计划
    tabTarget: () => import('./components/target'),
    //评审清单
    tabTemplate: () => import('./components/template'),
    //我的评分
    tabMyscore: () => import('./components/myscore')
  },
  data() {
    const currentTab = Number(this.$route.query.tab) ? Number(this.$route.query.tab) : 0
    return {
      currentTab,
      pageConfig: [
        {
          gridId: 'a276acd5-16ea-4169-9788-bc5920777b09',
          title: this.$t('建议评审清单')
        },
        {
          gridId: 'a0882c38-a427-4260-9ac1-526ca30f110f',
          title: this.$t('评审计划')
        },
        {
          gridId: 'dc04002e-9a53-4e63-b5b9-8712f0dedbb7',
          title: this.$t('评审清单')
        },
        {
          gridId: 'a2d7b5c1-93fd-4827-9586-7f77845b6c7e',
          title: this.$t('我的评分')
        }
      ],
      templateTypeList: [] //模板类型列表
    }
  },
  mounted() {
    this.getTemplateTypeList()
  },
  methods: {
    handleSelectTab(e) {
      if (e != this.currentTab) {
        this.currentTab = e
      }
    },
    getTemplateTypeList() {
      //模板类型列表 - templateTypeList
      this.$API.performanceScoreSetting
        .dictionaryGetList({
          dictCode: 'PERFORMANCE_TEMPLATE_TYPE'
        })
        .then((res) => {
          let _list = [...res.data]
          _list.forEach((e) => {
            e.cssClass = 'user-column'
          })
          this.templateTypeList = _list
        })
    }
  },
  computed: {
    // currentTab() {
    //   return +this.$route.query.tab || 0
    // }
  },
  created() {
    masterInit()
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
