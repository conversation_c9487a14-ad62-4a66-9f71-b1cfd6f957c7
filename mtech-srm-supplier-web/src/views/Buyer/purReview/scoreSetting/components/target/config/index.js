import { i18n } from '@/main.js'
import Vue from 'vue'
import utils from '@/utils/utils.js'
//指标定义Tab
const toolbar = [
  {
    id: 'Submit',
    icon: 'icon_solid_Createorder',
    title: i18n.t('提交申请')
  },
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('编辑')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
  },
  {
    id: 'export',
    title: i18n.t('导出')
  }
]
const columnData = (_this) => [
  {
    type: 'checkbox',
    width: 100,
    template: () => {
      return {
        template: Vue.component('checkbox', {
          template: `<span  class="e-treecell">
            <div v-if="data.reviewPlanItemList && data.reviewPlanItemList.length" class="e-checkbox-wrapper e-css">
              <input class="e-checkselect e-focus" type="checkbox">
              <span class="e-frame e-icons e-uncheck"></span>
              <span class="e-label"> </span>
            </div>
          </span>`,
          data: function () {}
        })
      }
    }
  },
  {
    width: 200,
    field: 'planCode',
    headerText: i18n.t('评审计划编码'),
    template: () => {
      return {
        template: Vue.component('planCode', {
          template: `<mt-tooltip v-if="data.planCode && data.planCode != ''" :content="data.planCode" target="#box-planCode" position="BottomCenter">
            <div id="box-planCode" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.planCode}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },
  {
    width: 200,
    field: 'planName',
    headerText: i18n.t('计划名称'),
    template: () => {
      return {
        template: Vue.component('planName', {
          template: `<mt-tooltip v-if="data.planName && data.planName != ''" :content="data.planName" target="#box-planName" position="BottomCenter">
            <div id="box-planName" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.planName}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },
  {
    width: 200,
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM'),
    ignore: true,
    template: () => {
      return {
        template: Vue.component('supplierInternalCode', {
          template: `<div style="color:#00469c;">
            <mt-tooltip
              ref="tooltipHide"
              v-if="data.supplierInternalCode && data.supplierInternalCode != ''"
              :content="data.supplierInternalCode"
              target="#box-supplierInternalCode"
              position="BottomCenter"
            >
              <div id="box-supplierInternalCode"
                style="width:150px;height:61px;display:flex;align-items:center;cursor:pointer;"
                @click="pushRouterBtn"
              >{{data.supplierInternalCode}}</div>
            </mt-tooltip>
          </div>`,
          data() {
            return {
              data: {}
            }
          },
          // props:{
          //   supplierCode:{
          //     type:Object,
          //     default:()=>{
          //       return
          //     }
          //   },
          // },
          methods: {
            pushRouterBtn() {
              if (this.data.buyerPartnerRelationLinkResponse) {
                let params = this.data.buyerPartnerRelationLinkResponse
                this.$refs.tooltipHide.ejsRef.close()
                _this.$router.push({
                  path: 'profileDetail',
                  query: {
                    partnerArchiveId: params.partnerArchiveId,
                    orgId: params.orgId,
                    supplierEnterpriseId: params.supplierEnterpriseId,
                    partnerRelationCode: params.partnerRelationCode
                  }
                })
              }
            }
          }
        })
      }
    }
  },
  {
    width: 200,
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP'),
    ignore: true,
    template: () => {
      return {
        template: Vue.component('supplierCode', {
          template: `<div style="color:#00469c;">
            <mt-tooltip
              ref="tooltipHide"
              v-if="data.supplierCode && data.supplierCode != ''"
              :content="data.supplierCode"
              target="#box-supplierCode"
              position="BottomCenter"
            >
              <div style="color: #585e6b;">{{data.supplierCode}}</div>
            </mt-tooltip>
          </div>`,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: 200,
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    template: () => {
      return {
        template: Vue.component('supplierName', {
          template: `<mt-tooltip v-if="data.supplierName && data.supplierName != ''" :content="data.supplierName" target="#box-supplierName" position="BottomCenter">
            <div id="box-supplierName" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.supplierName}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },
  {
    width: 120,
    field: 'supplierProvinceName',
    headerText: i18n.t('省份'),
    template: () => {
      return {
        template: Vue.component('supplierProvinceName', {
          template: `<mt-tooltip v-if="data.supplierProvinceName && data.supplierProvinceName != ''" :content="data.supplierProvinceName" target="#box-supplierProvinceName" position="BottomCenter">
            <div id="box-supplierProvinceName" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.supplierProvinceName}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },
  {
    width: 120,
    field: 'supplierCityName',
    headerText: i18n.t('城市'),
    template: () => {
      return {
        template: Vue.component('supplierCityName', {
          template: `<mt-tooltip v-if="data.supplierCityName && data.supplierCityName != ''" :content="data.supplierCityName" target="#box-supplierCityName" position="BottomCenter">
            <div id="box-supplierCityName" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.supplierCityName}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类'),
    template: () => {
      return {
        template: Vue.component('categoryName', {
          template: `<mt-tooltip v-if="data.categoryName && data.categoryName != ''" :content="data.categoryName" target="#box-categoryName" position="BottomCenter">
            <div id="box-categoryName" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.categoryName}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('公司'),
    template: () => {
      return {
        template: Vue.component('buyerOrgName', {
          template: `<mt-tooltip v-if="data.buyerOrgName && data.buyerOrgName != ''" :content="data.buyerOrgName" target="#box-buyerEnterpriseName" position="BottomCenter">
            <div id="box-buyerEnterpriseName" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.buyerOrgName}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },
  {
    field: 'planReviewTime',
    headerText: i18n.t('计划评审时间'),
    template: () => {
      return {
        template: Vue.component('planReviewTime', {
          template: `<mt-tooltip v-if="data.planTimeBegin && data.planTimeBegin != ''" :content="planReviewTime" target="#box-planReviewTime" position="BottomCenter">
            <div id="box-planReviewTime" style='width:150px;height:61px;display:flex;align-items:center;'>
              <span>{{planReviewTime}}</span>
            </div>
          </mt-tooltip>`,
          data: function () {
            return {
              utils
            }
          },
          computed: {
            planReviewTime() {
              if (this.data.planTimeBegin && this.data.planTimeBegin !== '0') {
                return `${this.utils.formateTime(
                  Number(this.data.planTimeBegin),
                  'yyyy-MM-dd'
                )}-${this.utils.formateTime(Number(this.data.planTimeEnd), 'yyyy-MM-dd')}`
              }
              return ''
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('原因'),
    template: () => {
      return {
        template: Vue.component('remark', {
          template: `<mt-tooltip v-if="data.remark && data.remark != ''" :content="data.remark" target="#box-remark" position="BottomCenter">
            <div id="box-remark" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.remark}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },

  {
    field: 'planStatus',
    headerText: i18n.t('状态'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        {
          key: 10,
          value: i18n.t('草稿')
        },
        {
          key: 20,
          value: i18n.t('已提交')
        },
        {
          key: 30,
          value: i18n.t('驳回')
        },
        {
          key: 40,
          value: i18n.t('已通过')
        }
      ],
      fields: { text: 'value', value: 'key' }
    },
    template: () => {
      return {
        template: Vue.component('planStatusone', {
          template: `<mt-tooltip v-if="planStatus && planStatus != ''" :content="planStatus" target="#box-planStatus" position="BottomCenter">
            <div id="box-planStatus" style='width:150px;height:61px;display:flex;align-items:center;'>
              <div  style="width:40px;height:20px;color:#90c8f9;">{{planStatus}}</div>
            </div>
          </mt-tooltip>`,
          // background-color: #eff3f9;
          data: function () {
            return {
              data: {}
            }
          },
          methods: {
            format(val) {
              switch (val) {
                case 10:
                  return i18n.t('草稿')
                case 20:
                  return i18n.t('已提交')
                case 30:
                  return i18n.t('驳回')
                case 40:
                  return i18n.t('已通过')
                default:
                  return ''
              }
            }
          },
          computed: {
            planStatus() {
              return this.format(this.data.planStatus)
            }
          }
        })
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('申请人'),
    template: () => {
      return {
        template: Vue.component('createUserName', {
          template: `<mt-tooltip v-if="data.createUserName && data.createUserName != ''" :content="data.createUserName" target="#box-createUserName" position="BottomCenter">
            <div id="box-createUserName" style='width:150px;height:61px;display:flex;align-items:center;'>{{data.createUserName}}</div>
          </mt-tooltip>`,
          data: function () {}
        })
      }
    }
  },
  {
    field: 'createDate',
    headerText: i18n.t('申请日期'),
    template: () => {
      return {
        template: Vue.component('createDate', {
          template: `<mt-tooltip v-if="data.createDate && data.createDate != ''" :content="createDate" target="#box-createTime" position="BottomCenter">
            <div id="box-createTime" style='width:150px;height:61px;display:flex;align-items:center;'>
              <span>{{createDate}}</span>
            </div>
          </mt-tooltip>`,
          data: function () {
            return {
              utils
            }
          },
          computed: {
            createDate() {
              return this.utils.formateTime(Number(this.data.createDate), 'yyyy-MM-dd')
            }
          }
        })
      }
    }
  }
]
export const pageConfig = (url, _this, dataBound) => [
  {
    gridId: '28897441-0185-45b8-9557-525e58035f5b',
    toolbar,
    treeGrid: {
      // allowPaging: true, //可不设置，默认值为false.
      // pageSettings: {
      //   pageSize: 10,
      //   pageCount: 1,
      //   pageSizes: [10, 50, 100, 200],
      // },
      // totalPages: 1,
      expandAll: false,
      columnData: columnData(_this),
      childMapping: 'reviewPlanItemList',
      asyncConfig: {
        url
        // afterAsyncData: (res) => {
        //   _this.$set(
        //     _this.pageConfig[0].treeGrid,
        //     "totalPages",
        //     Number(res.data.pages)
        //   );
        // },
        // 时间转换
        // serializeList: (list) => {
        //   list.forEach(e => {
        //     e.planReviewTime = Number(e.planReviewTime)
        //     e.time = _this.$utils.formateTime(e.planReviewTime, "yyyy-MM-dd hh:mm:ss")
        //   });
        //   return list;
        // },
      },
      dataBound,
      frozenColumns: 3
    }
  }
]
