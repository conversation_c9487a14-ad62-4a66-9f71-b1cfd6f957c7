<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="import-process">
      <div :class="headerFlag ? 'import' : 'choose'">
        <i>1</i><span>{{ $t('选择文件') }}</span>
      </div>
      <ul>
        <li v-for="index in 4" :key="index"></li>
      </ul>
      <div :class="headerFlag ? 'choose' : 'import'">
        <i>2</i><span>{{ $t('导入文件') }}</span>
      </div>
    </div>
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item prop="file" class="form-item">
          <div class="cell-upload">
            <div class="to-upload">
              <input type="file" ref="file" class="upload-input" @change="chooseFiles" />
              <div class="upload-box">
                <div class="plus-icon"></div>
                <div class="right-state">
                  <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
                  <div class="warn-text">
                    {{ $t('文件最大不可超过50M， 文件格式仅支持.xls .xlsx .doc') }}
                  </div>
                </div>
              </div>
            </div>
            <div class="has-file" v-if="!!uploadInfo.fileName && !!uploadInfo.remoteUrl">
              <div class="left-info">
                <div class="file-title">
                  <span class="text-ellipsis">{{ uploadInfo.fileName }}</span>
                  <span>{{ uploadInfo.fileSize }} kb</span>
                </div>
              </div>
              <mt-icon
                v-if="!isEdit"
                name="icon_Close_2"
                class="close-icon"
                @click.native="handleRemove"
              ></mt-icon>
            </div>
          </div>
        </mt-form-item>
        <div class="template-to-import">
          {{ $t('为了保证数据导入顺利，推荐您下载使用')
          }}<span class="import-the-template" @click="importTemplate">{{ $t('导入模板') }}</span
          >,{{ $t('并按照规范示例录入数据') }}
        </div>
        <div class="specification">
          {{ $t('上传的 Excel 表符合以下规范') }}: <br />•
          {{ $t('文件大小不超过20MB，这里是规范限制文案') }} <br />•
          {{ $t('文件格式仅支持') }} (*.xls *.xlsx)<br />•
          {{ $t('请确保您需要导入的sheet表头中不包含空的单元格，否则该sheet页数据系统将不做导入') }}
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
let fileData = null
export default {
  data() {
    return {
      formInfo: {
        remark: ''
      },
      rules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      uploadInfo: {}, // 上传后信息
      headerFlag: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    fileInfo() {
      return this.modalData.fileInfo
    },
    applyInfo() {
      return this.modalData.applyInfo
    }
  },
  mounted() {
    this.show()
    // 编辑下的恢复数据
    if (this.isEdit) {
      this.uploadInfo = {
        ...this.fileInfo,
        fileId: this.fileInfo.fileId,
        fileName: this.fileInfo.fileName,
        fileSize: this.fileInfo.fileSize,
        fileType: this.fileInfo.fileType,
        remoteUrl: this.fileInfo.fileUrl
      }
      this.formInfo.remark = this.fileInfo.remark
    }
  },
  methods: {
    chooseFiles(data) {
      console.log(this.$t('点击上传'), data)
      this.$loading()
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        this.headerFlag = false
        // 您未选择需要上传的文件
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('UploadFiles', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true

          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      fileData = _data
      this.uploadFile()
    },

    // 上传文件
    uploadFile() {
      this.$refs.file.value = ''
      this.$API.SupplierPunishment.fileUpload(fileData)
        .then((res) => {
          const { code, data } = res
          this.$hloading()
          if (code == 200 && !utils.isEmpty(data)) {
            this.uploadInfo = {
              ...data,
              fileId: data.id
            }
            this.headerFlag = true
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          } else {
            this.uploadInfo = {}
            this.$toast({ content: data.msg || this.$t('上传失败'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.uploadInfo = {}
          this.$hloading()
          this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
        })
    },

    // 移除文件
    handleRemove() {
      this.uploadInfo = {}
      fileData = null
      this.headerFlag = false
      console.log('点击了remove-file', this.data)
    },
    importTemplate() {
      console.log('为保证数据下载使用顺利,推荐您下载使用导入模块')
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      if (utils.isEmpty(this.uploadInfo)) {
        this.$toast({ content: this.$t('请选择文件上传！'), type: 'warning' })
        return
      }

      let query = {
        bizId: this.applyInfo.id,
        bizType: this.applyInfo.applyType,
        fileId: this.uploadInfo.fileId,
        fileName: this.uploadInfo.fileName,
        fileSize: this.uploadInfo.fileSize,
        fileType: this.uploadInfo.fileType,
        fileUrl: this.uploadInfo.remoteUrl,
        remark: this.formInfo.remark
      }

      if (this.isEdit) {
        query.id = this.uploadInfo.id
        query.sequenceNo = this.uploadInfo.sequenceNo
      }

      let url = this.isEdit ? 'updateFile' : 'adduploader'
      this.$API.SupplierPunishment[url](query)
        .then((res) => {
          const { code, data } = res
          if (code == 200 && !utils.isEmpty(data)) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function', 'reload')
          } else {
            this.$toast({ content: data.msg || this.$t('上传失败'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
//选择文件 导入文件
.import-process {
  width: 360px;
  height: 24px;
  margin: 45px auto 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    width: 40px;
    height: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    li {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #6386c1;
    }
  }

  .choose {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #6386c1;
      text-align: center;
      line-height: 24px;
      color: #fff;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      border-bottom: 2px solid #6386c1;
    }
  }
  .import {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid #6386c1;
      text-align: center;
      line-height: 24px;
      color: #6386c1;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: black;
      line-height: 24px;
    }
  }
}
//主体
.uploader-box {
  width: 100%;
  margin: 50px auto 0;
  .form-box {
    width: 100%;
    .form-item {
      width: 820px;
      margin: 0 auto;
      .cell-upload {
        position: relative;
        width: 820px;
        height: 250px;
        background: rgba(251, 252, 253, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        margin: 0 auto;
        //覆盖的选择文件框
        .upload-input {
          width: 100%;
          height: 75%;
          background: rgba(251, 252, 253, 1);
          border: 1px solid rgba(232, 232, 232, 1);
          box-sizing: border-box;
          position: absolute;
          z-index: 2;
          top: 0;
          background: transparent;
          opacity: 0;
        }
        //添加文档和说明文字区域
        .upload-box {
          width: 100%;
          height: 100%;
          //十字架
          .plus-icon {
            width: 60px;
            height: 60px;
            position: relative;
            margin: 50px auto 0;
            border: 1px dashed #000;
            &::before {
              content: ' ';
              display: inline-block;
              width: 60px;
              height: 2px;
              background: #98aac3;
              position: absolute;
              top: 50%;
              left: -1px;
            }

            &::after {
              content: ' ';
              display: inline-block;
              width: 2px;
              height: 60px;
              background: #98aac3;
              position: absolute;
              top: -1px;
              left: 50%;
            }
          }
          //文字
          .right-state {
            text-align: center;

            .plus-txt {
              margin: 20px auto 0;
              width: 270px;
              height: 24px;
              font-size: 24px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(152, 170, 195, 1);
            }
            .warn-text {
              margin: 16px auto 0;
              font-size: 12px;
              width: 369px;
              height: 21px;
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(241, 62, 62, 1);
            }
          }
        }
      }
    }
  }
  //导入模板规范
  .template-to-import {
    width: 566px;
    height: 24px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin-top: 10px;
    margin-left: 15px;
    .import-the-template {
      color: #00469c;
    }
  }
  .specification {
    width: 639px;
    height: 98px;
    margin-top: 31px;
    margin-left: 15px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }
}
</style>
