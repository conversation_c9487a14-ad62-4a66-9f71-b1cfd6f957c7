<template>
  <mt-dialog ref="dialog" width="1200" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- 建议评审清单 -->
      <div class="score-setting-dimension" style="width: 100%; height: 600px">
        <mt-template-page
          ref="templateRef"
          :use-tool-template="false"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </mt-dialog>
</template>
<script>
import { pageConfig } from '../config/threeindex'
export default {
  data() {
    return {
      arrid: [],
      selectGridRecords: [],

      // 弹窗底部按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
        // {
        //   click: this.confirm,
        //   buttonModel: { isPrimary: "true", content: "确认" },
        // },
      ],
      //列表调用
      pageConfig: pageConfig(this.$API.reviewPlan.Listfilter)
    }
  },

  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.$nextTick(() => {
        // this.$set(this.buttons[1].buttonModel, "content", "保存");
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      })
      let _data = { ...this.modalData.data }
      this.formObject = {
        id: _data.id, //配置Id
        dimensionName: _data.dimensionName, //名称
        remark: _data.remark //备注
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },

    seldata() {
      // 这里是选中全部
      return this.modalData.seldata
    }
  },
  created() {
    //调用子组件列表刷新
    this.buyerReview()
  },
  methods: {
    // //调用子组件列表刷新
    buyerReview() {
      this.seldata.forEach((item) => {
        this.arrid.push(item.id)
      })
      this.pageConfig = pageConfig(this.$API.reviewPlan.Listfilter, {
        ids: this.arrid
      })
    },
    // 确认按钮
    confirm() {
      this.$emit('confirm-function', this.selectGridRecords)
    },
    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      //勾选的每一列的数据
      this.selectGridRecords = e.gridRef.getMtechGridRecords()
      if (this.selectGridRecords.length <= 0 && e.toolbar.id == 'Add') {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id == 'Add') {
        this.confirm()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
