<template>
  <!-- 评审计划 -->
  <div class="score-setting-target">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  data() {
    return {
      isoney: false,
      plancollection: [
        {
          id: '',
          planStatus: null
        }
      ],
      delBuyerAssessRequest: {
        ids: []
      },
      index: 0,
      pageConfig: pageConfig(this.$API.reviewPlan.cateplantegy, this, this.dataBound)
    }
  },
  mounted() {},
  methods: {
    dataBound() {
      setTimeout(() => {
        this.$refs.templateRef.getCurrentTabRef().treeGrid.collapseAll()
      }, 0.17)
    },

    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Add' || e.toolbar.id == 'Delete' || e.toolbar.id == 'Submit')
      ) {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // //编辑
      if (e.toolbar.id == 'Add') {
        let arr = _selectGridRecords.filter((item) => {
          return item.reviewPlanItemList
        })
        if (arr.length > 1) {
          this.$toast({
            content: this.$t('编辑只能编辑一条数据!'),
            type: 'warning'
          })
          return
        }
        this.analysisOfSettingAdd(_selectGridRecords)
      }
      //删除
      else if (e.toolbar.id == 'Delete') {
        this.analysisOfSettingDelete(_selectGridRecords)
      }
      //提交
      else if (e.toolbar.id == 'Submit') {
        this.analysisOfSettingEXimport(_selectGridRecords)
      }
      // 导出
      else {
        this.handleExport('exportReviewPlan')
      }
    },
    handleClickCellTitle() {
      // 供应商编码跳转写在index.js配置模板中了
    },
    //新增配置
    analysisOfSettingAdd(val) {
      if (val[0].planStatus !== 10 && val[0].planStatus !== 30) {
        //提示弹框
        this.$toast({
          content: this.$t('请选择草稿或已驳回状态计划'),
          type: 'warning'
        })
      } else {
        this.$dialog({
          modal: () => import('./components/createaplvue.vue'),
          data: {
            title: this.$t('编辑评审计划'),
            isEdit: true,
            seldata: val //选中数组集合
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //删除配置
    analysisOfSettingDelete(val) {
      // 判断所选内容
      let arat = val.some((e) => {
        return e.reviewPlanItemList
      })
      if (arat) {
        val.forEach((item) => {
          if (item.reviewPlanItemList) {
            this.isoney = true
            if (item.planStatus !== 10 && item.planStatus !== 30) {
              this.isoney = false
            }
          }
        })
        if (this.isoney == false) {
          //提示弹框
          this.$toast({
            content: this.$t('请选择草稿或已驳回状态计划'),
            type: 'warning'
          })
        } else {
          // 循环判断值是否包含子级
          val.forEach((item) => {
            if (item.reviewPlanItemList) {
              this.delBuyerAssessRequest.ids.push(item.id)
            }
          })
          let bol = val.some((e) => {
            return e.reviewPlanItemList !== undefined
          })
          if (bol) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除数据？')
              },
              success: () => {
                this.$API.reviewPlan.straplandel(this.delBuyerAssessRequest).then(() => {
                  this.$toast({
                    content: this.$t('删除成功'),
                    type: 'success'
                  })
                  this.$refs.templateRef.refreshCurrentGridData()
                })
                this.delBuyerAssessRequest.ids = []
              }
            })
          } else {
            //提示弹框
            this.$toast({
              content: this.$t('请选择评审计划'),
              type: 'warning'
            })
          }
        }
      } else {
        //提示弹框
        this.$toast({ content: this.$t('请选择评审计划'), type: 'warning' })
      }
    },
    //提交
    analysisOfSettingEXimport(val) {
      // 判断所选内容
      let arat = val.filter((e) => {
        return e.reviewPlanItemList
      })
      let bol = arat.every((item) => {
        return item.planStatus == 10 || item.planStatus == 30
      })
      if (bol) {
        let arr = []
        arat.forEach((item) => {
          arr.push({
            id: item.id,
            planStatus: 20
          })
        })
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认提交选中数据？')
          },
          success: () => {
            this.$API.reviewPlan.updatePlanStatus({ plancollection: arr }).then(() => {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      } else {
        this.$toast({
          content: this.$t('请选择草稿或者驳回的数据提交'),
          type: 'warning'
        })
      }

      // if (arat) {
      //   val.forEach((item) => {
      //     if (item.reviewPlanItemList) {
      //       if (item.planStatus !== 10 && item.planStatus !== 30) {
      //         //提示弹框
      //         this.$toast({ content: this.$t("请选择草稿状态提交"), type: "warning" });
      //       } else {
      //         // 循环判断值是否包含子级
      //         this.plancollection = [];
      //         val.forEach((item) => {
      //           if (item.reviewPlanItemList) {
      //             this.plancollection.push({
      //               id: item.id,
      //               planStatus: 20,
      //               // planStatus: item.planStatus,
      //             });
      //           }
      //         });
      //         let bol = val.some((e) => {
      //           return e.reviewPlanItemList !== undefined;
      //         });
      //         if (bol) {
      //           this.$dialog({
      //             data: {
      //               title: this.$t("提示"),
      //               message: "确认提交选中数据？",
      //             },
      //             success: () => {
      //               this.$API.reviewPlan
      //                 .updatePlanStatus({ plancollection: this.plancollection })
      //                 .then(() => {
      //                   this.$toast({
      //                     content: this.$t("提交成功"),
      //                     type: "success",
      //                   });
      //                   this.$refs.templateRef.refreshCurrentGridData();
      //                 });
      //               this.plancollection = [];
      //             },
      //           });
      //         } else {
      //           //提示弹框
      //           this.$toast({ content: this.$t("请选择评审计划"), type: "warning" });
      //         }
      //       }
      //     }
      //   });
      // } else {
      //   //提示弹框
      //   this.$toast({ content: this.$t("请选择评审计划"), type: "warning" });
      // }
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-target {
  height: 100%;
  .filter-org {
    background: #fff;
    padding: 10px;
    /deep/.owner-org-tree {
      width: 300px;
    }
  }
}
</style>
