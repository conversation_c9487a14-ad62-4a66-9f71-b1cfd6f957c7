import { i18n } from '@/main.js'
//维度设置Tab
// const toolbar = []

const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    cssClass: ''
  },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称'),
    cssClass: 'field-content'
  },

  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },

  {
    field: 'ruleDesc',
    headerText: i18n.t('规则描述')
  }
]
// 调用列表
export const pageConfig = (url, query = {}) => [
  {
    gridId: 'fb62d050-5fc0-4eb0-84e5-98018a3dc224',
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [[], []]
    },
    grid: {
      height: 'auto',
      columnData,
      asyncConfig: {
        url,
        params: query,
        recordsPosition: 'data'
      }
    }
  }
]
