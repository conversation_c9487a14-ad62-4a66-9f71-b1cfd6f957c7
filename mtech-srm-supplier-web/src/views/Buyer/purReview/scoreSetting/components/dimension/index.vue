<template>
  <!-- 建议评审清单 -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  data() {
    return {
      delBuyerAssessRequest: {
        ids: []
      },
      //列表调用
      pageConfig: pageConfig(this.$API.reviewPlan.catesuggest, this)
    }
  },
  methods: {
    // 点击单元格内按钮  编辑 加 启用等按钮
    handleClickCellTool(e) {
      if (e.tool.id == 'edit') {
        // 编辑维度操作
        this.editPolicy(e.data)
      } else if (e.tool.id == 'delete') {
        // 删除维度操作
        this.DeleteInline(e.data)
      }
    },
    handleClickCellTitle(e) {
      const { field, data } = e
      let params = data.buyerPartnerRelationLinkResponse
      if (field === 'supplierInternalCode' && data && data.id) {
        this.$router.push({
          path: 'profileDetail',
          query: {
            partnerArchiveId: params.partnerArchiveId,
            orgId: params.orgId,
            supplierEnterpriseId: params.supplierEnterpriseId,
            partnerRelationCode: params.partnerRelationCode
          }
        })
      }
    },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Enable' ||
          e.toolbar.id == 'Delete' ||
          e.toolbar.id == 'Edit' ||
          e.toolbar.id == 'Disable')
      ) {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id == 'Add') {
        this.analysisOfSettingAdd()
      }
      //删除
      else if (e.toolbar.id == 'Delete') {
        this.DeleteInline(_selectGridRecords)
      }
      //创建评审清单
      else if (e.toolbar.id == 'Create') {
        this.analysisOfSettingCreate(_selectGridRecords)
      }
      //编辑
      else if (e.toolbar.id == 'Edit') {
        if (_selectGridRecords.length > 1) {
          this.$toast({
            content: this.$t('一次只能编辑一行!'),
            type: 'warning'
          })
          return
        }
        this.editPolicy(_selectGridRecords)
        return
      } else {
        // 导出
        this.handleExport('exportSuggestList')
      }
    },
    //新增配置
    analysisOfSettingAdd() {
      this.$dialog({
        modal: () => import('./components/dimensionDialog.vue'),
        data: {
          title: this.$t('新建')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //创建评审计划
    analysisOfSettingCreate(seldata) {
      // 选中数据id查找删除
      let bol = true
      for (let i = 0; i < seldata.length; i++) {
        for (let j = i + 1; j < seldata.length; j++) {
          if (seldata[i].buyerEnterpriseName != seldata[j].buyerEnterpriseName) {
            bol = false
          }
        }
      }
      if (bol) {
        this.$dialog({
          modal: () => import('./components/createaplvue.vue'),
          data: {
            title: this.$t('创建评审计划'),
            seldata //选中数组集合
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else {
        this.$toast({
          content: this.$t('请选择相同公司创建评审计划!'),
          type: 'warning'
        })
      }
    },
    // 删除
    DeleteInline(val) {
      // if ((val.constructor == Array) == true) {
      //   val = val[0];
      // }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          let ids = []
          // 判断  val 类型 是数组还是对象
          if ((val.constructor == Array) == true) {
            val.forEach((item) => {
              ids.push(item.id)
              return
            })
          } else {
            ids.push(val.id)
          }
          this.$API.reviewPlan['suggestdel']({ ids }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            ids = []
            this.$refs.templateRef.refreshCurrentGridData() //刷新表格统一方法
          })
        }
      })
    },
    // 编辑
    editPolicy(policyData) {
      // 判断  policyData 类型 是数组还是对象
      if ((policyData.constructor == Array) == true) {
        policyData = policyData[0]
      }
      const _this = this
      this.$dialog({
        modal: () => import('./components/dimensionDialog.vue'),
        data: {
          title: this.$t('编辑计划模板'),
          isEdit: true,
          policyData
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
</style>
