import { i18n } from '@/main.js'
import Vue from 'vue'
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },

  {
    field: 'supplierCityName',
    headerText: i18n.t('城市')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    width: '200',
    field: 'planReviewTime',
    headerText: i18n.t('计划审查时间'),
    template: () => {
      return {
        template: Vue.component('planReviewTime', {
          template: `<div style='width:150px'>
            <mt-date-range-picker v-model="range" :width="180" :placeholder="$t('选择日期')" @change="input"></mt-date-range-picker>
          </div>`,
          data: function () {
            return {
              range: []
            }
          },
          methods: {
            input(e) {
              this.$parent.$emit('input', this.data.id, 'planReviewTime', e)
            }
          },
          computed: {},
          created() {
            if (this.data.planTimeBegin) {
              this.range = [new Date(+this.data.planTimeBegin), new Date(+this.data.planTimeEnd)]
            }
          }
        })
      }
    }
  },
  // {
  //   field: "remark",
  //   headerText: i18n.t("原因"),
  // },
  {
    width: '200',
    field: 'remark',
    headerText: i18n.t('原因'),
    template: () => {
      return {
        template: Vue.component('remark', {
          template: `<mt-tooltip :content="data.remark" target="#box" position="BottomCenter">
            <div id="box" style='width:150px;height:61px;display:flex;align-items:center;'>
              <mt-input id="remark-input" v-model.number='data.remark'  @blur='input' />
            </div>
          </mt-tooltip>`,
          data: function () {},
          methods: {
            input(e) {
              if (e) {
                this.$parent.$emit('input', this.data.id, 'remark', this.data.remark)
              }
            }
          },
          computed: {}
        })
      }
    }
  }
]

// 调用列表
export const pageConfig = (seldata) => [
  {
    gridId: '55a3f371-1c61-4420-bbc7-a695255ff2c0',
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'Add',
            icon: 'icon_solid_Createorder ',
            title: i18n.t('新增')
          },
          {
            id: 'Delete',
            icon: 'icon_solid_Delete ',
            title: i18n.t('删除')
          }
        ],
        []
      ]
    },
    grid: {
      height: 'auto',
      columnData,
      // asyncConfig: {
      //   // url,
      //   parms
      // },
      dataSource: seldata //不能同上面同时使用
    }
  }
]
