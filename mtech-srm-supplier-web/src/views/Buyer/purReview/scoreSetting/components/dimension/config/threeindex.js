import { i18n } from '@/main.js'
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },

  {
    field: 'supplierCityName',
    headerText: i18n.t('城市')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'planReviewTime',
    headerText: i18n.t('计划审查时间')
  },
  {
    field: 'remark',
    headerText: i18n.t('原因')
  }
]

// 调用列表
export const pageConfig = (url, query = {}) => [
  {
    gridId: 'ca45f44e-e1f2-49b8-abe7-0b5a08338454',
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'Add',
            icon: 'icon_solid_Createorder ',
            title: i18n.t('添加')
          }
        ],
        []
      ]
    },
    grid: {
      columnData,
      asyncConfig: {
        url,
        params: query,
        recordsPosition: 'data' //多传值显示必写项
      }
    }
  }
]
