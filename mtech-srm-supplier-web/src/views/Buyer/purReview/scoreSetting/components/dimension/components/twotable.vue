<template>
  <!-- 建议评审清单 -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @input="seldataChange"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>
<script>
import { pageConfig } from '../config/twoindex'
export default {
  data() {
    return {
      //列表调用
      pageConfig: pageConfig(this.seldata)
    }
  },
  props: {
    //接收父组件传值
    seldata: {
      type: Array,
      default: () => []
    }
  },
  created() {},
  methods: {
    seldataChange(a, b, c) {
      this.seldata.forEach((item) => {
        if (item.id == a) {
          if (b == 'planReviewTime') {
            if (c.startDate) {
              item['planTimeBegin'] = Date.parse(this.$utils.formateTime(c.startDate, 'yyyy-MM-dd'))
              item['planTimeEnd'] = Date.parse(this.$utils.formateTime(c.endDate, 'yyyy-MM-dd'))
            } else {
              item['planTimeBegin'] = null
              item['planTimeEnd'] = null
            }
          } else {
            item[b] = c
          }
        }
      })
    },
    //新增配置
    analysisOfSettingAdd() {
      this.$dialog({
        modal: () => import('./threetable'),
        data: {
          title: this.$t('新增'),
          seldata: this.seldata //再次传值
        },
        success: (arrdata) => {
          // 回传选中数据添加数组变更列表
          arrdata.forEach((item) => {
            this.seldata.push(item)
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Enable' ||
          e.toolbar.id == 'Delete' ||
          e.toolbar.id == 'Edit' ||
          e.toolbar.id == 'Disable')
      ) {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id == 'Add') {
        this.analysisOfSettingAdd()
      }
      //删除
      else if (e.toolbar.id == 'Delete') {
        this.analysisOfSettingDelete(_selectGridRecords)
      }
    },
    //删除配置
    analysisOfSettingDelete(val) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          // 选中数据id查找删除
          for (let i = 0; i < this.seldata.length; i++) {
            let bol = val.some((e) => {
              return e.id == this.seldata[i].id
            })
            if (bol) {
              this.seldata.splice(i, 1)
              i--
            }
          }
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
</style>
