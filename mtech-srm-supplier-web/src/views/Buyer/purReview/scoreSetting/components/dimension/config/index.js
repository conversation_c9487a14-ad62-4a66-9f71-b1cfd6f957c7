import { i18n } from '@/main.js'
//维度设置Tab
import $utils from '@/utils/utils'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder ',
    title: i18n.t('新增')
  },
  {
    id: 'Edit',
    icon: 'icon_solid_edit ',
    title: i18n.t('编辑')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete ',
    title: i18n.t('删除')
  },
  {
    id: 'Create',
    icon: 'icon_solid_Createorder ',
    title: i18n.t('创建评审计划')
  },
  {
    id: 'export',
    title: i18n.t('导出')
  }
]

const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM'),
    cssClass: 'field-content',
    width: 280,
    cellTools: [
      {
        id: 'edit',
        title: i18n.t('编辑')
      },
      {
        id: 'delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP'),
    width: 280
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'supplierProvinceName',
    headerText: i18n.t('省份')
  },
  {
    field: 'supplierCityName',
    headerText: i18n.t('城市')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('公司')
  },

  {
    field: 'remark',
    headerText: i18n.t('评审原因')
  },

  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期')
  }
]

// 调用列表
export const pageConfig = (url) => [
  {
    gridId: 'cb9b6d14-96d4-4202-90e0-c5c4f6b6bf33',
    toolbar,
    grid: {
      // allowTextWrap: true,
      columnData,
      asyncConfig: {
        url,
        // 时间转换
        serializeList: (list) => {
          list.forEach((e) => {
            e.createDate = Number(e.createDate)
            e.createDate = $utils.formateTime(e.createDate, 'yyyy-MM-dd  hh:mm:ss')
          })
          return list
        }
      },
      frozenColumns: 3
    }
  }
]
