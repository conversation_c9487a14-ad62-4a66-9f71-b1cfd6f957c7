<template>
  <!-- 评审策略清单 -->
  <div class="score-setting-dimension">
    <mt-template-page ref="templateRef" :use-tool-template="false" :template-config="pageConfig" />
  </div>
</template>
<script>
import { pageConfig } from '../config/oneindex'
export default {
  data() {
    return {
      //列表调用
      pageConfig: pageConfig(this.$API.reviewPlan.relationgest)
    }
  },
  methods: {
    //调用子组件列表刷新
    buyerReview(arr) {
      // 第一种单独传值
      // this.$set(
      //   this.pageConfig[0].grid.asyncConfig,
      //   "params",
      //   {
      //     buyerReviewStrategys: arr,
      //   }
      // );
      // 第二种单独传值
      this.pageConfig = pageConfig(this.$API.reviewPlan.relationgest, {
        buyerReviewStrategys: arr
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
</style>
