<template>
  <div class="teamWraper">
    <div style="margin-top: 24px">
      <vxe-button
        status="default"
        :content="$t('确认')"
        size="small"
        :disabled="isDisabledBtn"
        @click="handleIsConfirm(20)"
      />
      <vxe-button
        status="default"
        :content="$t('拒绝')"
        size="small"
        :disabled="isDisabledBtn"
        @click="handleIsConfirm(30)"
      />
    </div>
    <div class="main-tabel">
      <table frame="border" style="border-collapse: collapse">
        <tr style="background-color: #f2f2f2; font-weight: bold">
          <th></th>
          <th>{{ $t('质量专家') }}1</th>
          <th>{{ $t('质量专家') }}2</th>
          <th>{{ $t('研发专家') }}</th>
          <th>{{ $t('采购专家') }}</th>
          <th>CSR{{ $t('体系专家') }}</th>
          <th style="width: 250px">{{ $t('备注') }}</th>
        </tr>
        <tr style="background-color: #f2f2f2; font-weight: bold">
          <td>{{ $t('审查表单') }}</td>
          <td>QPA/QSA/{{ $t('环保') }}</td>
          <td>QPA/QSA/{{ $t('环保') }}</td>
          <td>{{ $t('研发') }}</td>
          <td>{{ $t('商务') }}</td>
          <td>CSR</td>
          <td></td>
        </tr>
        <tr style="font-weight: bold">
          <td>{{ $t('现场审查小组') }}</td>
          <template v-for="item in teamData.reviewResponse">
            <td :key="item.userId">
              {{ item.userName }}
            </td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('邮箱') }}</td>
          <template v-for="item in teamData.reviewResponse">
            <td :key="item.userId">{{ item.email }}</td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('移动电话') }}</td>
          <template v-for="item in teamData.reviewResponse">
            <td :key="item.userId">{{ item.phone }}</td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('职级') }}</td>
          <template v-for="item in teamData.reviewResponse">
            <td :key="item.userId">{{ item.jobRankName }}</td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('角色') }}</td>
          <template v-for="item in teamData.reviewResponse">
            <td :key="item.userId">{{ item.roleName }}</td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('状态') }}</td>
          <template v-for="item in teamData.reviewResponse">
            <td :key="item.userId">{{ item.confirmStatusName }}</td>
          </template>
        </tr>
        <tr style="font-weight: bold">
          <td>{{ $t('一级专家小组') }}</td>
          <template v-for="item in teamData.firstLevelResponse">
            <td :key="item.userId">{{ item.userName }}</td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('邮箱') }}</td>
          <template v-for="item in teamData.firstLevelResponse">
            <td :key="item.userId">{{ item.email }}</td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('移动电话') }}</td>
          <template v-for="item in teamData.firstLevelResponse">
            <td :key="item.userId">{{ item.phone }}</td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('职级') }}</td>
          <template v-for="item in teamData.firstLevelResponse">
            <td :key="item.userId">{{ item.jobRankName }}</td>
          </template>
        </tr>
        <tr>
          <td>{{ $t('状态') }}</td>
          <template v-for="item in teamData.firstLevelResponse">
            <td :key="item.userId">{{ item.confirmStatusName }}</td>
          </template>
        </tr>
      </table>
    </div>
  </div>
</template>
<script>
// import { columnData } from '../teamTemplate/config/index'
export default {
  props: {
    teamORgItem: {
      type: Object,
      default: () => {}
    },
    teamData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isDisabledBtn: true,
      isConfirmENUM: {
        10: this.$t('待确认'),
        20: this.$t('已确认'),
        30: this.$t('已拒绝')
      }
    }
  },
  // watch: {
  //   'teamData.firstLevelResponse': {
  //     handler(val) {
  //       console.log('userInfo123', JSON.parse(sessionStorage.getItem('userInfo')))
  //       this.isDisabledBtn = val.userType !== 1
  //     }
  //   },
  //   immediate: true,
  //   deep: true
  // },
  created() {
    const curUserName = JSON.parse(sessionStorage.getItem('userInfo')).username
    if (
      this.teamData.firstLevelResponse != null &&
      this.teamData.firstLevelResponse.some(
        (item) => item.userName === curUserName && item.confirmStatus === 10
      )
    ) {
      this.isDisabledBtn = false
    } else if (
      this.teamData.reviewResponse != null &&
      this.teamData.reviewResponse.some(
        (item) => item.userName === curUserName && item.confirmStatus === 10
      )
    ) {
      this.isDisabledBtn = false
    }
    if (this.teamData.firstLevelResponse === null) {
      this.teamData.firstLevelResponse = [{}]
    }
    while (this.teamData.firstLevelResponse.length < 6) {
      this.teamData.firstLevelResponse.push({})
    }
    if (this.teamData.reviewResponse === null) {
      this.teamData.reviewResponse = [{}]
    }
    while (this.teamData.reviewResponse.length < 6) {
      this.teamData.reviewResponse.push({})
    }
  },
  methods: {
    handleIsConfirm(ENUM) {
      let confirmObj = {}
      const curUserId = JSON.parse(sessionStorage.getItem('userInfo')).uid
      if (this.teamData.firstLevelResponse) {
        this.teamData.firstLevelResponse.forEach((item) => {
          if (item.userId === curUserId) {
            confirmObj = {
              confirmStatus: ENUM,
              taskUserId: item.id
            }
          }
        })
      }
      if (this.teamData.reviewResponse) {
        this.teamData.reviewResponse.forEach((item) => {
          if (item.userId === curUserId) {
            confirmObj = {
              confirmStatus: ENUM,
              taskUserId: item.id
            }
          }
        })
      }
      this.$loading()
      this.$API.supplierReviewTask
        .setExpertIsConfirm(confirmObj)
        .then((res) => {
          if (res.code === 200 && res.code) {
            this.$hloading()
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$parent.$parent.getDetailData()
            setTimeout(() => {
              const curUserName = JSON.parse(sessionStorage.getItem('userInfo')).username
              if (
                this.teamData.firstLevelResponse != null &&
                this.teamData.firstLevelResponse.some(
                  (item) => item.userName === curUserName && item.confirmStatus === 20
                )
              ) {
                this.isDisabledBtn = true
              } else if (
                this.teamData.reviewResponse != null &&
                this.teamData.reviewResponse.some(
                  (item) => item.userName === curUserName && item.confirmStatus === 20
                )
              ) {
                this.isDisabledBtn = true
              }
            }, 1200)
          }
        })
        .catch(() => {
          this.$hloading()
        })
    }
  }
}
</script>
<style scoped>
.teamWraper {
  height: 100%;
}
.main-tabel {
  margin-top: 12px;
}
tr {
  height: 50px;
}
table td {
  width: 100px;
}
</style>
