<!-- 新增现场审查/供应商自查 -->
<template>
  <div class="full-height">
    <div class="detail-card">
      <div class="desc">
        <div class="desc-title-box">
          <span class="title">{{ title }}</span>
        </div>
        <div>
          <div class="desc-detail-box">
            <div class="detail-item">
              <span>{{ $t('评审任务编码') }}：</span>
              <span>{{ formObject.code }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('创建人') }}：</span>
              <span>{{ formObject.createUserName }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('创建时间') }}：</span>
              <span>{{ formObject.createDate }}</span>
            </div>
          </div>
          <div class="desc-detail-box">
            <div class="detail-item">
              <span>{{ $t('公司') }}：</span>
              <span>{{ formObject.buyerOrgName }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('供应商') }}：</span>
              <span>{{ formObject.supplierName }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('品类') }}：</span>
              <span>{{ formObject.categoryName }}</span>
            </div>
            <div class="detail-item">
              <span>{{ $t('任务类型') }}：</span>
              <span>{{ formObject.bizTypeName }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="buttons-box">
        <span class="btn" @click="save">{{ $t('保存') }}</span>
        <span class="btn" @click="publish">{{ $t('发布') }}</span>
        <span class="btn" @click="handleCancel">{{ $t('返回') }}</span>
      </div>
    </div>

    <div class="outer-box">
      <mt-form ref="formRef" :model="formObject" :rules="rules">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('评审任务名称')" prop="name">
              <mt-input
                v-model="formObject.name"
                :placeholder="$t('请输入评审任务名称')"
                :max-length="30"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('供应商')" prop="supplierInternalCode">
              <mt-select
                v-model="formObject.supplierInternalCode"
                :data-source="supplierList"
                :fields="{ text: 'supplierName', value: 'supplierInternalCode' }"
                :allow-filtering="true"
                :disabled="editStatus"
                :placeholder="$t('请选择供应商')"
                @change="supplierChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('评审类型')" prop="bizTypeName">
              <mt-input v-model="formObject.bizTypeName" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('品类')" prop="categoryName">
              <mt-input v-model="formObject.categoryName" :disabled="true" />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('场景')" prop="sceneDefineName">
              <mt-input v-model="formObject.sceneDefineName" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('评审包')" prop="packageName">
              <mt-input v-model="packageName" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('计划时间')" prop="planTimeEnd">
              <mt-date-range-picker
                v-model="timeRanges"
                :placeholder="$t('选择开始时间和结束时间')"
                :open-on-focus="true"
                @change="dateChange"
              />
            </mt-form-item>
          </mt-col>
          <!-- 供应商自查没有 -->
          <mt-col :span="6" v-if="taskCode !== 'selfTempType'">
            <mt-form-item :label="$t('注册地址')" prop="companyRegisterAddr">
              <mt-input v-model="formObject.companyRegisterAddr" :disabled="true" />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6" v-if="taskCode !== 'selfTempType'">
            <mt-form-item :label="$t('工厂地址')" prop="factoryAddr">
              <mt-input
                v-model="formObject.factoryAddr"
                :placeholder="$t('请输入工厂地址')"
                :max-length="30"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6" v-if="taskCode !== 'selfTempType'">
            <mt-form-item :label="$t('抽取方式')" prop="supplyType">
              <mt-select
                v-model="formObject.supplyType"
                :data-source="supplierTypeList"
                :placeholder="$t('请选择抽取方式')"
                :disabled="editStatus"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24" v-if="formObject.supplierInternalCode">
          <mt-col :span="24">
            <mt-form-item :label="$t('条件')">
              <sc-table
                ref="sctableRef"
                :loading="loading"
                :columns="columns"
                :table-data="formObject.ruleLists"
                :fix-height="300"
                :show-header="false"
                :is-show-toolbar="false"
                style="margin-top: 20px"
              >
                <template #operateDefault="{ row }">
                  <div
                    v-if="
                      [
                        'quality-qpa',
                        'RD-rd1',
                        'fc-zrvn',
                        'odmin-zh',
                        'gf-gfzl',
                        'gf-gfsw',
                        'gf-gfjs',
                        'gf-gfzlyf',
                        'tx-txyf',
                        'tx-txsw',
                        'tx-txzl'
                      ].includes(row.templateTypeId)
                    "
                    style="color: #2783fe; cursor: pointer"
                    @click="handleUpload(row)"
                  >
                    <span v-if="row.fileInfoDTOList.length === 0">
                      {{ $t('点击上传') }}
                    </span>
                    <span v-else>
                      {{ $t('已上传数') }}：{{ row.fileInfoDTOList.length || 0 }}
                    </span>
                  </div>
                </template>
              </sc-table>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24" v-if="formObject.bizType !== 4 && formObject.supplyType === 0">
          <mt-col :span="24">
            <mt-form-item :label="$t('团队成员')">
              <team-template
                :team-org-item="teamOrgItem"
                :table-data="formObject.userLists"
                @add="teamAdd"
                @delete="teamDelete"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
      <div v-show="formObject.supplyType === 0 && formObject.userLists.length !== 0">
        <div class="oreder-warp" style="height: 100%">
          <div class="treeView">
            <ul class="left-nav">
              <li
                v-for="(item, index) in reviewContent"
                :key="item.templateTypeCode"
                :class="{ active: index_ == index }"
                @click="indexChange(index)"
              >
                {{ item.templateTypeName }}
              </li>
            </ul>
          </div>
          <div class="table">
            <sc-table
              v-if="!showTable"
              ref="xTable"
              :columns="scoreColumns"
              :table-data="scoreTableData"
              :checkbox-config="checkedConfig"
              :tree-config="treeConfig"
              :fix-height="300"
            >
              <template slot="custom-tools">
                <vxe-button
                  v-for="item in toolbar"
                  :key="item.code"
                  :status="item.status"
                  :icon="item.icon"
                  :loading="item.loading"
                  size="small"
                  @click="handleClickToolBar(item)"
                >
                  {{ item.name }}
                </vxe-button>
              </template>
            </sc-table>
            <div v-else>
              <vxe-button size="small" @click="setScoretor">
                {{ $t('设置打分人') }}
              </vxe-button>
              <mt-form ref="scoreRef" :model="scoreForm" style="margin-top: 20px">
                <mt-row :gutter="24">
                  <mt-col :span="8">
                    <mt-form-item :label="$t('评分人')" prop="scoretor" label-style="left">
                      <mt-input v-model="scoreForm.scoretor" :disabled="true" />
                    </mt-form-item>
                  </mt-col>
                </mt-row>
              </mt-form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import TeamTemplate from './components/TeamTemplate.vue'
import { columnData } from './config'
import { utils as $utils } from '@mtech-common/utils'
export default {
  components: { ScTable, TeamTemplate },
  data() {
    return {
      currentId: null,
      formObject: {
        code: null,
        name: null, // 名称
        supplierInternalCode: null,
        supplierCode: null, // 供应商code
        supplierName: null, // 供应商name
        partnerArchiveId: null, // 供应商档案id
        partnerRelationId: null, // 供应商关系id
        supplierEnterpriseId: null, //供应商企业id
        supplierEnterpriseCode: null, //供应商企业code
        supplierEnterpriseName: null, // 供应商企业名称
        buyerOrgId: null, // 采方组织id
        buyerOrgCode: null, // 采方组织code
        buyerOrgName: null, // 采方组织名字
        bizType: null, // 评审类型
        bizTypeName: null, // 评审类型名称
        categoryId: null, // 品类id
        categoryCode: null, // 品类code
        categoryName: null, // 品类名称
        sceneDefineId: null, // 场景id
        sceneDefineName: null, // 场景name
        planTimeBegin: null, // 开始时间
        planTimeEnd: null, // 结束时间
        companyRegisterAddr: null, // 注册地址
        factoryAddr: null, // 工厂地址
        ruleLists: [], // 规则集合
        userLists: [] // 用户组
      },
      rules: {
        name: [
          {
            required: true,
            message: this.$t('请输入评审任务名称'),
            trigger: 'blur'
          }
        ],
        supplierInternalCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        planTimeEnd: [
          {
            required: true,
            message: this.$t('请选择日期'),
            trigger: 'blur'
          }
        ],
        supplyType: [
          {
            required: true,
            message: this.$t('请选择抽取方式'),
            trigger: 'blur'
          }
        ]
      },

      supplierList: [],
      supplierTypeList: [
        { text: this.$t('系统抽取'), value: 1 },
        { text: this.$t('手动选择'), value: 0 }
      ],
      packageName: null, // 评审包
      timeRanges: [], // 计划时间
      teamOrgItem: {},
      templateTypes: null,

      loading: false,
      columns: columnData,

      reviewTypeOptions: [],

      index_: 0,
      reviewContent: [],
      scoreColumns: [
        { type: 'checkbox', treeNode: true, fixed: 'left' },
        {
          field: 'reviewName',
          title: this.$t('评审项'),
          width: '360'
        },
        {
          field: 'range',
          title: this.$t('分值范围')
        },
        {
          field: 'weight',
          title: this.$t('权重(%)')
        },
        {
          field: 'selfScore',
          title: this.$t('自查得分')
        },
        {
          field: 'selfReason',
          title: this.$t('自查原因')
        },
        {
          field: 'unqualified',
          title: this.$t('合格线')
        },
        {
          field: 'scoreUserName',
          title: this.$t('打分人')
        }
      ],
      scoreTableData: [],
      checkedConfig: {
        checkField: 'checked'
      },
      treeConfig: {
        transform: true,
        rowField: 'reviewCode',
        parentField: 'parentDimensionCode',
        expandAll: true
      },
      selectedRows: [],
      toolbar: [{ code: 'setting', name: this.$t('设置打分人'), status: 'info', loading: false }],

      member: '',

      queryReviewTaskTemplateResponses: [],
      showTable: false,
      scoreForm: {
        scoretor: null
      },
      qpaScoretor: {
        userId: null,
        userName: null
      }, // QPA打分人
      rdScoretor: {
        userId: null,
        userName: null
      }, // 研发打分人
      zrvnScoretor: {
        userId: null,
        userName: null
      }, // 越南非采打分人
      odminScoretor: {
        userId: null,
        userName: null
      }, // odmin打分人
      gfzlScoretor: {
        userId: null,
        userName: null
      }, // 光伏质量打分人
      gfswScoretor: {
        userId: null,
        userName: null
      }, // 光伏商务打分人
      gfjsScoretor: {
        userId: null,
        userName: null
      }, // 光伏技术打分人
      gfzlyfScoretor: {
        userId: null,
        userName: null
      }, // 光伏研发打分人
      txyfScoretor: {
        userId: null,
        userName: null
      },
      txswScoretor: {
        userId: null,
        userName: null
      },
      txzlScoretor: {
        userId: null,
        userName: null
      }
    }
  },
  computed: {
    taskCode() {
      return this.$route.query?.taskCode
    },
    title() {
      return this.taskCode === 'selfTempType'
        ? this.$t('新增供应商自查单')
        : this.editStatus
        ? this.$t('编辑现场审查单')
        : this.$t('新增现场审查单')
    },
    editStatus() {
      // 是否编辑
      return this.$route.query?.type === 'edit'
    },
    reviewTaskId() {
      return this.$route.query?.reviewTaskId
    }
  },
  mounted() {
    this.editStatus && this.getDetailById()
    this.getCateSupplierList()
    this.getReviewTypeTemplate()
  },
  methods: {
    getDetailById() {
      this.$API.supplierReviewTask
        .queryReviewTaskApi({
          reviewTaskId: this.editStatus ? this.reviewTaskId : this.currentId
        })
        .then((res) => {
          if (res.code === 200) {
            this.formObject = res.data.reviewTaskResponse
            this.currentId = this.formObject.id
            this.formObject.userLists = res.data.reviewTaskUserResponses
            this.queryReviewTaskTemplateResponses = res.data.queryReviewTaskTemplateResponses
            this.timeRanges = [
              new Date(Number(this.formObject.planTimeBegin)),
              new Date(Number(this.formObject.planTimeEnd))
            ]
            this.getFileTypeScoreUserInfo()
          }
        })
    },
    getCateSupplierList() {
      this.$API.CategoryCertification.getSupListByAuthCode({
        authProjectCode: this.$route.query?.authProjectCode
      }).then((res) => {
        if (res.code == 200) {
          this.supplierList = res.data
        } else {
          this.supplierList = []
        }
      })
    },
    // 评审业务类型
    getReviewTypeTemplate() {
      this.$API.supplierReviewTask
        .getMasterReviewTemplateType({ dictCode: 'reviewType' })
        .then((res) => {
          if (res.code == 200) {
            this.reviewTypeOptions = res.data
          } else {
            this.reviewTypeOptions = []
          }
        })
    },
    supplierChange({ itemData }) {
      this.getDetailAddress(itemData.supplierEnterpriseId)
      if (this.taskCode) {
        this.formObject.supplierInternalCode = itemData.supplierInternalCode
        this.formObject.supplierCode = itemData.supplierCode
        this.formObject.supplierName = itemData.supplierName
        this.formObject.partnerArchiveId = itemData.partnerArchiveId
        this.formObject.partnerRelationId = itemData.partnerRelationId
        this.formObject.supplierEnterpriseId = itemData.supplierEnterpriseId
        this.formObject.supplierEnterpriseCode = itemData.supplierEnterpriseCode
        this.formObject.supplierEnterpriseName = itemData.supplierEnterpriseName
        this.formObject.buyerOrgId = itemData.orgId
        this.formObject.buyerOrgCode = itemData.orgCode
        this.formObject.buyerOrgName = itemData.orgName
        this.formObject.bizType = this.taskCode === 'selfTempType' ? 4 : 3
        this.formObject.bizTypeName =
          this.taskCode === 'selfTempType'
            ? this.$t('认证审查-供应商自查')
            : this.$t('认证审查-现场审查')
        this.formObject.categoryId = itemData.categoryId
        this.formObject.categoryCode = itemData.categoryCode
        this.formObject.categoryName = itemData.categoryName
        this.formObject.sceneDefineId = itemData.sceneId
        this.formObject.sceneDefineName = itemData.sceneName
        this.formObject.authProjectId = itemData.authProjectId
        this.formObject.authProjectCode = itemData.authProjectCode
        this.formObject.authProjectName = itemData.authProjectName
        this.teamOrgItem.organizationId = itemData.orgId
        this.teamOrgItem.organizationName = itemData.orgName
        this.teamOrgItem.organizationCode = itemData.orgCode
        this.queryByOrgIdAndCategoryId()
      }
    },
    async queryByOrgIdAndCategoryId() {
      this.loading = true
      const orgId = this.formObject.buyerOrgId
      const categoryId = this.formObject.categoryId
      const taskTemplateCode = this.formObject.bizType
      let packageCode = null
      const resp = await this.$API.supplierReviewTask.queryByOrgIdAndCategoryIdPre({
        orgId,
        categoryId,
        taskTemplateCode
      })
      if (resp) {
        packageCode = resp.data.packageCode
      }
      this.$API.supplierReviewTask.queryByOrgIdAndCategoryId(packageCode).then((res) => {
        this.formObject.ruleLists = []
        this.packageName = res.data.packageName
        this.templateTypes = res.data.templateTypes
        res.data.reviewPackagePassRules.forEach((item) => {
          this.formObject.ruleLists.push({
            conditionObject: item.conditionAttribute,
            symbol: item.conditionSymbol,
            standard: item.conditionTarget,
            joinSymbol: item.conditionType,
            templateTypeId: item.templateType,
            templateTypeName: this.getTemplateTypeNameById(item.templateType),
            fileInfoDTOList: []
          })
        })
        if (this.editStatus) {
          this.formObject.ruleLists.forEach((item) => {
            this.queryReviewTaskTemplateResponses.forEach((e) => {
              if (item.templateTypeName === e.templateTypeName) {
                item.fileInfoDTOList = e.fileInfoResponseList
              }
            })
          })
        }
        this.loading = false
        this.getPrestItemById()
      })
    },
    getTemplateTypeNameById(id) {
      let templateTypeName = ''
      let _id = id
      if (isNaN(id)) {
        _id = id.split('-')[1]
      }
      this.reviewTypeOptions.forEach((item) => {
        item.children?.forEach((ele) => {
          if (ele.id == _id || ele.itemCode == _id) {
            templateTypeName = `${item.name}-${ele.name}`
          }
        })
      })
      return templateTypeName
    },
    getPrestItemById() {
      const ids = this.formObject.ruleLists.map((v) => v.templateTypeId)
      const orgId = this.formObject.buyerOrgId
      const categoryId = this.formObject.categoryId
      const params = {
        orgId,
        categoryId,
        ids
      }
      this.$API.supplierReviewTask.getPrestItem(params).then((res) => {
        this.setReviewContent(res.data)
      })
    },
    setReviewContent(data) {
      if (this.editStatus) {
        this.reviewContent = data.map((item) => {
          this.queryReviewTaskTemplateResponses.forEach((r) => {
            if (r.templateTypeName === item.templateTypeName) {
              item.dimensionDTOS?.forEach((ele) => {
                ele.indexDTOS?.forEach((e) => {
                  r.itemResponses?.forEach((re) => {
                    if (e.reviewCode === re.itemCode) {
                      e.scoreUserId = re.scoreUserId
                      e.scoreUserName = re.scoreUserName
                    }
                  })
                })
              })
            }
          })
          return item
        })
      } else {
        this.reviewContent = data
      }
      this.indexChange(0)
    },
    getFileTypeScoreUserInfo() {
      let params = {
        taskCode: this.formObject.code
      }
      this.$API.supplierReviewTask.getFileTypeScoreUserInfo(params).then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            if (item.templateType.includes('quality-qpa')) {
              this.qpaScoretor.userId = item.scoreUserId
              this.qpaScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('RD-rd1')) {
              this.rdScoretor.userId = item.scoreUserId
              this.rdScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('fc-zrvn')) {
              this.zrvnScoretor.userId = item.scoreUserId
              this.zrvnScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('odmin-zh')) {
              this.odminScoretor.userId = item.scoreUserId
              this.odminScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('gf-gfzl')) {
              this.gfzlScoretor.userId = item.scoreUserId
              this.gfzlScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('gf-gfsw')) {
              this.gfswScoretor.userId = item.scoreUserId
              this.gfswScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('gf-gfjs')) {
              this.gfjsScoretor.userId = item.scoreUserId
              this.gfjsScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('gf-gfzlyf')) {
              this.gfzlyfScoretor.userId = item.scoreUserId
              this.gfzlyfScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('tx-txyf')) {
              this.txyfScoretor.userId = item.scoreUserId
              this.txyfScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('tx-txsw')) {
              this.txswScoretor.userId = item.scoreUserId
              this.txswScoretor.userName = item.scoreUserName
            }
            if (item.templateType.includes('tx-txzl')) {
              this.txzlScoretor.userId = item.scoreUserId
              this.txzlScoretor.userName = item.scoreUserName
            }
          })
        }
      })
    },
    // 获取注册地址
    getDetailAddress(id) {
      this.$API.supplierReviewTask.getDetailAddress({ id }).then((res) => {
        this.formObject.companyRegisterAddr = res.data.detailAddress || null
      })
    },
    dateChange(e) {
      if (e.startDate) {
        this.formObject.planTimeBegin = new Date(e.startDate).getTime()
        this.formObject.planTimeEnd = new Date(e.endDate).getTime()
      } else {
        this.formObject.planTimeBegin = null
        this.formObject.planTimeEnd = null
      }
    },
    handleUpload(row) {
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          fileList: row.fileInfoDTOList
        },
        failed: (res) => {
          row.fileInfoDTOList = res
        }
      })
    },
    setCode() {
      this.$API.supplierReviewTask
        .queryReviewTaskApi({
          reviewTaskId: this.currentId
        })
        .then((res) => {
          if (res.code === 200) {
            this.formObject.code = res.data.reviewTaskResponse.code
          }
        })
    },
    save() {
      if (this.taskCode === 'selfTempType') {
        this.supSlefSave()
      } else if (this.taskCode === 'sceneSelfTempType') {
        this.sceneSelfSave()
      }
    },
    // 供应商自查保存
    supSlefSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.validateFileList(this.formObject)) {
            this.$toast({
              content: this.$t('请上传附件'),
              type: 'warning'
            })
            return
          }
          this.$loading()
          const param = $utils.cloneDeep(this.formObject)
          const api = this.$API.supplierReviewTask.saveReviewTaskApi
          delete param.templateList
          delete param.userLists
          api(param)
            .then((res) => {
              this.$hloading()
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('保存成功！'),
                  type: 'success'
                })
                this.currentId = res.data
                this.formObject.id = this.currentId
                this.setCode()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        } else {
          this.$toast({
            content: this.$t('请完成必填项'),
            type: 'warning'
          })
        }
      })
    },
    // 现场审查保存
    sceneSelfSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.validateFileList(this.formObject)) {
            this.$toast({
              content: this.$t('请上传附件'),
              type: 'warning'
            })
            return
          }
          // 调接口 请求抽取专家 专家抽取成功即提示抽取完成，失败则提示：审查专家抽取失败
          if (this.formObject.supplyType === 1) {
            this.$loading()

            // 1.获取评审模板
            const ids = this.templateTypes.split(',')
            const orgId = this.formObject.buyerOrgId
            const categoryId = this.formObject.categoryId
            const params = {
              orgId,
              categoryId,
              ids
            }
            this.$API.supplierReviewTask.getPrestItem(params).then((res) => {
              let templateArr = []
              res.data.forEach((item) => {
                let obj = {
                  templateCode: item.templateCode,
                  templateTypeName: item.templateTypeName,
                  templateTypeId: item.templateTypeId,
                  templateItemRequests: []
                }
                if (item.dimensionDTOS) {
                  item.dimensionDTOS.forEach((e) => {
                    if (e.indexDTOS) {
                      e.indexDTOS.forEach((e1) => {
                        obj.templateItemRequests.push({
                          itemCode: e1.reviewCode
                        })
                      })
                    }
                  })
                }
                templateArr.push(obj)
              })
              // 2.获取模板成功后，调用保存add接口，新增专家
              // 2.1 入参拼接
              this.formObject.templateLists = templateArr
              const param = $utils.cloneDeep(this.formObject)
              delete param.userLists

              this.$API.supplierReviewTask.saveReviewTaskApi(param).then((res) => {
                if (res.code === 200) {
                  this.$toast({
                    content: this.$t('保存成功！'),
                    type: 'success'
                  })
                  this.currentId = res.data
                  this.formObject.id = this.currentId
                  this.setCode()
                }
                this.$hloading()
              })
            })

            return
          }
          if (!this.formObject.userLists.length) {
            this.$toast({
              content: this.$t('请添加团队成员！'),
              type: 'warning'
            })
            return
          }
          const leaders = this.formObject.userLists.filter((v) => v.roleCode === 'leader')
          if (!leaders.length) {
            this.$toast({
              content: this.$t('未设置组长，请设置！'),
              type: 'warning'
            })
            return
          }
          let arr = []
          this.reviewContent.forEach((item) => {
            let obj = {
              templateCode: item.templateCode,
              templateTypeName: item.templateTypeName,
              templateTypeId: item.templateTypeId,
              templateItemRequests: []
            }
            if (
              [
                'quality-qpa',
                'RD-rd1',
                'fc-zrvn',
                'odmin-zh',
                'gf-gfzl',
                'gf-gfsw',
                'gf-gfjs',
                'gf-gfzlyf',
                'tx-txyf',
                'tx-txsw',
                'tx-txzl'
              ].includes(item.templateTypeId)
            ) {
              if (item?.templateTypeId === 'quality-qpa') {
                obj.templateItemRequests.push({
                  scoreUserId: this.qpaScoretor?.userId,
                  scoreUserName: this.qpaScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'RD-rd1') {
                obj.templateItemRequests.push({
                  scoreUserId: this.rdScoretor?.userId,
                  scoreUserName: this.rdScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'fc-zrvn') {
                obj.templateItemRequests.push({
                  scoreUserId: this.zrvnScoretor?.userId,
                  scoreUserName: this.zrvnScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'odmin-zh') {
                obj.templateItemRequests.push({
                  scoreUserId: this.odminScoretor?.userId,
                  scoreUserName: this.odminScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'gf-gfzl') {
                obj.templateItemRequests.push({
                  scoreUserId: this.gfzlScoretor?.userId,
                  scoreUserName: this.gfzlScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'gf-gfsw') {
                obj.templateItemRequests.push({
                  scoreUserId: this.gfswScoretor?.userId,
                  scoreUserName: this.gfswScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'gf-gfjs') {
                obj.templateItemRequests.push({
                  scoreUserId: this.gfjsScoretor?.userId,
                  scoreUserName: this.gfjsScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'gf-gfzlyf') {
                obj.templateItemRequests.push({
                  scoreUserId: this.gfzlyfScoretor?.userId,
                  scoreUserName: this.gfzlyfScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'tx-txyf') {
                obj.templateItemRequests.push({
                  scoreUserId: this.txyfScoretor?.userId,
                  scoreUserName: this.txyfScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'tx-txsw') {
                obj.templateItemRequests.push({
                  scoreUserId: this.txswScoretor?.userId,
                  scoreUserName: this.txswScoretor?.userName
                })
              }
              if (item?.templateTypeId === 'tx-txzl') {
                obj.templateItemRequests.push({
                  scoreUserId: this.txzlScoretor?.userId,
                  scoreUserName: this.txzlScoretor?.userName
                })
              }
            } else {
              if (item.dimensionDTOS) {
                item.dimensionDTOS.forEach((e) => {
                  if (e.indexDTOS) {
                    e.indexDTOS.forEach((e1) => {
                      obj.templateItemRequests.push({
                        itemCode: e1.reviewCode,
                        scoreUserId: e1.scoreUserId,
                        scoreUserName: e1.scoreUserName
                      })
                    })
                  }
                })
              }
            }
            arr.push(obj)
          })
          this.formObject.templateLists = arr
          this.$loading()
          this.$API.supplierReviewTask.saveReviewTaskApi(this.formObject).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('保存成功！'),
                type: 'success'
              })
              this.currentId = res.data
              this.formObject.id = this.currentId
              this.setCode()
            }
            this.$hloading()
          })
        } else {
          this.$toast({
            content: this.$t('请完成必填项'),
            type: 'warning'
          })
        }
      })
    },
    validateFileList(form) {
      let flag = false
      for (let i = 0; i < form.ruleLists.length; i++) {
        const ele = form.ruleLists[i]
        if (
          [
            'quality-qpa',
            'RD-rd1',
            'fc-zrvn',
            'odmin-zh',
            'gf-gfzl',
            'gf-gfsw',
            'gf-gfjs',
            'gf-gfzlyf',
            'tx-txyf',
            'tx-txsw',
            'tx-txzl'
          ].includes(ele.templateTypeId) &&
          ele.fileInfoDTOList.length === 0
        ) {
          flag = true
        }
      }
      return flag
    },
    teamAdd(e) {
      this.formObject.userLists.push(e)
    },
    teamDelete(rows) {
      this.formObject.userLists = this.formObject.userLists.filter(
        (item) => !rows.some((e) => item.id === e.id)
      )
    },
    indexChange(index) {
      this.index_ = index
      if (this.reviewContent.length > 0) {
        const tempList = this.reviewContent[this.index_].dimensionDTOS
        this.scoreTableData = this.flatTableData(tempList)
      } else {
        this.scoreTableData = []
      }
      let item = this.reviewContent[this.index_]
      this.showTable = [
        'quality-qpa',
        'RD-rd1',
        'fc-zrvn',
        'odmin-zh',
        'gf-gfzl',
        'gf-gfsw',
        'gf-gfjs',
        'gf-gfzlyf',
        'tx-txyf',
        'tx-txsw',
        'tx-txzl'
      ].includes(item?.templateTypeId)
      if (item?.templateTypeId === 'quality-qpa') {
        this.scoreForm.scoretor = this.qpaScoretor?.userName || null
      }
      if (item?.templateTypeId === 'RD-rd1') {
        this.scoreForm.scoretor = this.rdScoretor?.userName || null
      }
      if (item?.templateTypeId === 'fc-zrvn') {
        this.scoreForm.scoretor = this.zrvnScoretor?.userName || null
      }
      if (item?.templateTypeId === 'odmin-zh') {
        this.scoreForm.scoretor = this.odminScoretor?.userName || null
      }
      if (item?.templateTypeId === 'gf-gfzl') {
        this.scoreForm.scoretor = this.gfzlScoretor?.userName || null
      }
      if (item?.templateTypeId === 'gf-gfsw') {
        this.scoreForm.scoretor = this.gfswScoretor?.userName || null
      }
      if (item?.templateTypeId === 'gf-gfjs') {
        this.scoreForm.scoretor = this.gfjsScoretor?.userName || null
      }
      if (item?.templateTypeId === 'gf-gfzlyf') {
        this.scoreForm.scoretor = this.gfzlyfScoretor?.userName || null
      }
      if (item?.templateTypeId === 'tx-txyf') {
        this.scoreForm.scoretor = this.txyfScoretor?.userName || null
      }
      if (item?.templateTypeId === 'tx-txsw') {
        this.scoreForm.scoretor = this.txswScoretor?.userName || null
      }
      if (item?.templateTypeId === 'tx-txzl') {
        this.scoreForm.scoretor = this.txzlScoretor?.userName || null
      }
    },
    flatTableData(list) {
      let arr = []
      list.forEach((item) => {
        item.reviewName = item.dimensionName // 因为表格列设置中绑定的field是reviewName
        item.reviewCode = item.dimensionCode
        arr = arr.concat(item.indexDTOS || [])
      })
      const res = arr.concat(list)
      res.forEach((item) => {
        item.checked = false
        item.unqualified = item.qualifiedSymbol ? item.qualifiedSymbol + item.qualifiedScore : ''
        item.range = item.highestScore ? `${item.lowestScore || 0}-${item.highestScore}` : ''
      })
      return res
    },
    handleClickToolBar(e) {
      const selectedRecords = this.$refs.xTable.$refs.xGrid.getCheckboxRecords()
      this.selectedRows = selectedRecords
      const commonToolbar = ['setting']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'setting':
          this.setScoretor()
          break
        default:
          break
      }
    },
    setScoretor() {
      this.$dialog({
        modal: () => import('./components/setUserDialog.vue'),
        data: {
          userLists: this.formObject.userLists
        },
        success: (res) => {
          this.member = res
          let scoretor = this.formObject.userLists.find((item) => item.userId === this.member)
          this.patchScoretor(scoretor)
        }
      })
    },
    patchScoretor(scoretor) {
      if (
        [
          'quality-qpa',
          'RD-rd1',
          'fc-zrvn',
          'odmin-zh',
          'gf-gfzl',
          'gf-gfsw',
          'gf-gfjs',
          'gf-gfzlyf',
          'tx-txyf',
          'tx-txsw',
          'tx-txzl'
        ].includes(this.reviewContent[this.index_].templateTypeId)
      ) {
        if (this.reviewContent[this.index_].templateTypeId === 'quality-qpa') {
          this.qpaScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'RD-rd1') {
          this.rdScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'fc-zrvn') {
          this.zrvnScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'odmin-zh') {
          this.odminScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'gf-gfzl') {
          this.gfzlScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'gf-gfsw') {
          this.gfswScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'gf-gfjs') {
          this.gfjsScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'gf-gfzlyf') {
          this.gfzlyfScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'tx-txyf') {
          this.txyfScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'tx-txsw') {
          this.txswScoretor = scoretor
        }
        if (this.reviewContent[this.index_].templateTypeId === 'tx-txzl') {
          this.txzlScoretor = scoretor
        }
        this.scoreForm.scoretor = scoretor.userName
      } else {
        this.selectedRows.forEach((item) => {
          // 只给子级数据设置打分人
          if (item.parentDimensionCode) {
            item.scoreUserId = scoretor.userId
            item.scoreUserName = scoretor.userName
          }
        })
      }
      this.$toast({ content: this.$t('操作成功！'), type: 'success' })
      this.$forceUpdate()
    },
    publish() {
      if (!this.currentId) {
        this.$toast({ content: this.$t('请先保存数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认发布？')
        },
        success: () => {
          this.$loading()
          const api = this.$API.supplierReviewTask.publishReviewTaskApi
          api(this.currentId)
            .then((res) => {
              this.$hloading()
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('发布成功！'),
                  type: 'success'
                })
              }
              this.handleCancel()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    handleCancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.detail-card {
  width: 100%;
  height: 100px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px 70px 20px 20px;
  margin-bottom: 16px;
  display: flex;
  .desc {
    flex: 1;
    .desc-title-box {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .title {
        font-size: 20px;
        font-weight: bold;
        color: #292929;
      }
      .tag {
        font-size: 12px;
        display: inline-block;
        padding: 4px;
        border-radius: 2px;
      }
      .status {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
        margin: 0 10px;
      }
      .group {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
    .desc-detail-box {
      display: flex;
      .detail-item {
        margin-right: 20px;
        font-size: 13px;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }
    }
  }
  .buttons-box {
    display: flex;
    align-items: center;
    .btn {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      color: #00469c;
      margin-left: 50px;
      cursor: pointer;
    }
    .is-disabled {
      color: #ccc;
    }
  }
}
.outer-box {
  height: 100%;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  padding: 24px;
  overflow-y: scroll;
}
.h2 {
  font-size: 14px;
  font-weight: bold;
}

.oreder-warp {
  display: flex;
  .treeView {
    width: 180px;
    margin-right: 20px;
  }
  .table {
    width: calc(100% - 180px);
    height: 100%;
    flex: 1;
  }
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: rgba(0, 0, 0, 0.87);
}
::v-deep .e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #fff;
  border-color: #fff;
  border-bottom: 2px solid #00469c;
}
::v-deep .mt-tree-view .e-treeview .e-fullrow {
  opacity: 1;
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: #00469c;
}

.left-nav {
  line-height: 40px;
  text-align: center;
  li {
    cursor: pointer;
  }
  .active {
    background: linear-gradient(135deg, rgba(245, 84, 72, 1) 0%, rgba(255, 128, 118, 1) 100%);
    color: #fff;
  }
}
::v-deep .e-gridcontent .e-content {
  height: calc(100vh - 240px) !important;
}
</style>
