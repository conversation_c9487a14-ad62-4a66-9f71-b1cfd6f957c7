<template>
  <div>
    <mt-dialog
      ref="toast"
      :header="$t('选择评分人')"
      :buttons="sonbuttons"
      size="small"
      :show-close-icon="true"
    >
      <div style="padding: 50px">
        <mt-select
          :data-source="modalData.returnScorerData"
          :show-clear-button="true"
          :placeholder="$t('请选择评分人')"
          v-model="member"
          :fields="{ text: 'text', value: 'value' }"
          @select="setScorersSuccess"
        ></mt-select>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
// import { i18n } from '@/main'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      member: '',
      scorer: '',
      sonbuttons: [
        {
          click: this.soncancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.sonconfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      formObject: {}
    }
  },
  mounted() {
    this.show()
    console.log('modalData.returnScorerData', this.modalData.returnScorerData)
  },
  methods: {
    show() {
      this.$refs['toast'].ejsRef.show()
    },
    hide() {
      this.$refs['toast'].ejsRef.hide()
    },
    soncancel() {
      this.hide()
    },
    setScorersSuccess(e) {
      console.log('setScorersSuccesssetScorersSuccesssetScorersSuccess', e)
      this.scorer = {
        userName: e.itemData.text,
        userId: e.itemData.value
      }
    },
    sonconfirm() {
      this.hide()
      this.$emit('confirm-function', this.scorer)
    }
  }
}
</script>

<style></style>
