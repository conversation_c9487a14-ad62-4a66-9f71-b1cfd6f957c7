<template>
  <div>
    <mt-template-page
      ref="teamTabRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      :hidden-tabs="true"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from '../belowItem/config/index'
export default {
  props: {
    belowData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: '8e2dd321-bab3-4f8d-bb27-b64267e9b6b9',
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'fileUpload',
                  title: this.$t('附件上传')
                }
              ]
            ]
          },
          grid: {
            dataSource: this.belowData,
            columnData: columnData
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar } = e
      if (toolbar.id === 'fileUpload') {
        this.$dialog({
          modal: () => import('./components/FileManage.vue'),
          data: {
            title: this.$t('附件上传'),
            id: this.$route.query.id
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
