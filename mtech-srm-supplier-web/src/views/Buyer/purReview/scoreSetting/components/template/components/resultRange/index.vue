<template>
  <div class="oreder-warp">
    <div class="treeView">
      <ul class="left-nav">
        <li
          :class="{ active: index_ == index }"
          v-for="(item, index) in resultData"
          :key="item.templateTypeId"
          @click="indexchang(index)"
        >
          {{ item.templateTypeName }}
        </li>
      </ul>
    </div>
    <div class="table">
      <div class="top">
        <div class="e-table-wrap">
          <div class="th bg">
            <div class="td col2">{{ $t('维度') }}</div>
            <div class="td col1">{{ $t('满分') }}</div>
            <div class="td col1">{{ $t('及格分') }}</div>
            <div class="td col1">{{ $t('自检得分') }}</div>
            <div class="td col1">{{ $t('现场得分') }}</div>
            <div class="td col1">{{ $t('权重') }}</div>
          </div>
          <div class="tbody" v-if="resultData.length > 0">
            <div
              class="tr"
              v-for="(item, index) in resultData[index_].itemResponses"
              :key="'a' + index"
            >
              <div class="td col2">{{ item.itemName }}</div>
              <div class="td col1">{{ item.fullScore }}</div>
              <div class="td col1">{{ item.score }}</div>
              <div class="td col1">{{ item.selfScore }}</div>
              <div class="td col1">{{ item.actScore }}</div>
              <div class="td col1">{{ item.weight }}</div>
            </div>
            <div class="tr bg">
              <div class="td col2">{{ $t('供应商自评得分') }}</div>
              <div class="td col32">{{ resultData[index_].selfScore }}</div>
              <div class="td col1">{{ $t('现场考察得分') }}</div>
              <div class="td col1">{{ resultData[index_].actScore }}</div>
              <div class="td col1">{{ resultData[index_].weight }}</div>
            </div>
            <div class="tr">
              <div class="td col2">{{ $t('结论') }}</div>
              <div class="td col5">
                <mt-input
                  style="width: 100%"
                  :disabled="info.status != 50"
                  v-model="resultData[index_].report"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="echart" id="radar"></div>
      </div>
      <div>
        <div class="result">
          <div class="left bg">{{ $t('总结') }}</div>
          <div class="right">
            <div class="tr">
              <div class="td">{{ $t('优势') }}</div>
              <div class="td fr">
                <mt-input
                  style="width: 100%"
                  css-class="e-outline"
                  :multiline="true"
                  :rows="3"
                  v-if="resultData.length > 0"
                  v-model="resultData[index_].advantage"
                  :disabled="info.status != 50"
                ></mt-input>
              </div>
            </div>

            <div class="tr">
              <div class="td">{{ $t('劣势') }}</div>
              <div class="td fr">
                <mt-input
                  style="width: 100%"
                  css-class="e-outline"
                  :multiline="true"
                  :rows="3"
                  v-if="resultData.length > 0"
                  v-model="resultData[index_].inferiority"
                  :disabled="info.status != 50"
                ></mt-input>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  props: {
    resultData: {
      type: Array,
      default() {
        return []
      }
    },
    info: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      index_: 0,
      radarChart: null
    }
  },
  mounted() {
    let radarChartDom = document.getElementById('radar')
    this.radarChart = echarts.init(radarChartDom)
    if (this.resultData.length > 0) {
      this.initChart()
    }
  },
  methods: {
    indexchang(index) {
      this.index_ = index
      this.initChart()
    },
    initChart() {
      let radarLabel = []
      let radarData = [
        {
          value: [],
          name: this.$t('满分')
        },
        {
          value: [],
          name: this.$t('及格分')
        },
        {
          value: [],
          name: this.$t('自检得分')
        },
        {
          value: [],
          name: this.$t('现场得分')
        }
      ]
      this.resultData[this.index_].itemResponses.forEach((item) => {
        radarLabel.push({
          name: item.itemName,
          max: 100
        })
        radarData[0].value.push(item.fullScore)
        radarData[1].value.push(item.score)
        radarData[2].value.push(item.selfScore)
        radarData[3].value.push(item.actScore)
      })
      let radarOption = {
        legend: {
          data: [this.$t('满分'), this.$t('及格分'), this.$t('自检得分'), this.$t('现场得分')]
        },
        tooltip: {
          trigger: 'item'
        },
        radar: {
          indicator: radarLabel
        },
        series: [
          {
            type: 'radar',
            emphasis: {
              lineStyle: {
                width: 4
              }
            },
            data: radarData
          }
        ]
      }
      radarOption && this.radarChart.setOption(radarOption)
    }
  },
  watch: {
    resultData: {
      handler(n) {
        this.$parent.$emit('input', n)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.oreder-warp {
  display: flex;
  .treeView {
    width: 180px;
    margin-right: 20px;
    margin-left: 10px;
  }
  .table {
    width: calc(100% - 180px);
    flex: 1;
    .top {
      width: 100%;
      display: flex;
      .echart {
        width: calc(100% - 680px);
        margin-left: 25px;
        min-height: 400px;
      }
    }
  }
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: rgba(0, 0, 0, 0.87);
}
::v-deep .e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #fff;
  border-color: #fff;
  border-bottom: 2px solid #00469c;
}
::v-deep .mt-tree-view .e-treeview .e-fullrow {
  opacity: 1;
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: #00469c;
}
.e-table-wrap {
  width: 650px;
  border: 1px solid #eaeaea;
  border-right: none;
  border-bottom: none;
  .th,
  .tr {
    display: flex;
    min-height: 40px;

    justify-content: center;
    div {
      text-align: center;
    }
    .col2 {
      width: 20%;
    }
    .col1 {
      width: 16%;
    }
    .col5 {
      width: 80%;
    }
    .col32 {
      width: 32%;
    }
    .td {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      padding: 5px;
      line-height: normal;
      text-align: center;
      border-right: 1px solid #eaeaea;
      border-bottom: 1px solid #eaeaea;
    }
  }
  .bg {
    background-color: rgba(99, 134, 193, 0.1);
    font-weight: bold;
  }
}
.result {
  display: flex;
  border-top: 1px solid #eaeaea;
  border-left: 1px solid #eaeaea;
  margin-top: 30px;
  .left {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    width: 130px;
    border-right: 1px solid #eaeaea;
  }
  .bg {
    background-color: rgba(99, 134, 193, 0.1);
    font-weight: bold;
  }
  .right {
    display: block;
    width: calc(100% - 130px);
    .tr {
      display: flex;
      .td {
        min-height: 40px;
        padding: 20px;
        display: flex;
        align-items: center;
        border-right: 1px solid #eaeaea;
        border-bottom: 1px solid #eaeaea;
      }
      .fr {
        flex: 1;
      }
    }
  }
}
.left-nav {
  line-height: 40px;
  text-align: center;
  li {
    cursor: pointer;
  }
  .active {
    background: linear-gradient(135deg, rgba(245, 84, 72, 1) 0%, rgba(255, 128, 118, 1) 100%);
    color: #fff;
  }
}
</style>
