<template>
  <mt-dialog
    ref="dialog"
    :enable-resize="false"
    css-class="right-wrapper"
    :buttons="buttons"
    :header="header"
    height="600px"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" class="form-box" :model="formData" :rules="rules">
        <mt-form-item :label="$t('成员类型')">
          <mt-radio
            :data-source="memberTypeList"
            v-model="formObj.userType"
            @change="typeChange"
          ></mt-radio>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('部门')" v-if="formObj.userType == 2">
          <mt-DropDownTree
            :fields="fieldsTemplate"
            :popup-height="500"
            id="baseTreeSelect"
            :placeholder="$t('请选择')"
            :key="fieldsTemplate.key"
            @select="nodeOnCheck"
          ></mt-DropDownTree>
        </mt-form-item> -->
        <mt-form-item :label="$t('姓名')" prop="userName" v-if="formObj.userType == 1">
          <mt-select
            :data-source="userList"
            :allow-filtering="true"
            :fields="userFields"
            v-model="formData.userName"
            @change="userHandle"
            :placeholder="$t('请选择员工')"
            :filtering="expertNamechange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('姓名')" prop="userName" v-if="formObj.userType == 2">
          <mt-select
            v-model="formData.userName"
            :fields="{ text: 'name', value: 'uid' }"
            :data-source="anodrslist"
            filter-bar-:placeholder="$t('Search')"
            :allow-filtering="true"
            :filtering="fuzzyNamechange"
            :placeholder="$t('请选择员工')"
            @select="userHandle"
          ></mt-select>
        </mt-form-item>

        <mt-form-item :label="$t('评审角色')" prop="roleCode">
          <mt-select
            :data-source="roleList"
            :fields="{ value: 'itemCode', text: 'name' }"
            @change="roleHandle"
            v-model="formData.roleCode"
            :placeholder="$t('请选择角色')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('职责描述')" style="width: 100%">
          <mt-input
            :multiline="true"
            :rows="3"
            max-length="200"
            v-model="formObj.dutyDescription"
            :placeholder="$t('请输入职责描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import MtRadio from '@mtech-ui/radio'

export default {
  components: { MtRadio },
  data() {
    return {
      // 表单验证
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('新增') }
        }
      ],
      memberTypeList: [
        { value: '1', label: this.$t('专家') },
        { value: '2', label: this.$t('公司员工') }
      ],
      radioVal: '1',
      fieldsTemplate: {
        dataSource: [],
        value: 'departmentCode',
        text: 'name',
        child: 'children',
        code: 'departmentCode',
        key: 1
      },
      roleList: [],
      userList: [],
      userFields: { value: 'id', text: 'employeeName' },

      formObj: { dutyDescription: null, userType: '1' },
      roleObj: {},
      // 人
      userObj: {},
      // 部门
      deptObj: {},
      formData: {
        userName: null,
        roleCode: null
      },
      rules: {
        userName: [{ required: true, message: this.$t('请选择用户'), trigger: 'blur' }],
        roleCode: [
          {
            required: true,
            message: this.$t('请选择用户角色'),
            trigger: 'blur'
          }
        ]
      },

      anodrslist: []
      // selectItemTemplate: function () {
      //   return {
      //     template: Vue.component('select-item', {
      //       template: `<div :title="data.expertName">{{data.expertName}} - {{data.departmentName}}</div>`,
      //       data() {
      //         return { data: {} }
      //       }
      //     })
      //   }
      // },
      // selectItemTemplateTwo: function () {
      //   return {
      //     template: Vue.component('select-item', {
      //       template: `<div :title="data.employeeName">
      //        {{data.employeeName}} - {{data.departmentOrgName}}
      //       </div>`,
      //       data() {
      //         return { data: {} }
      //       }
      //     })
      //   }
      // }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    teamData() {
      return this.modalData.teamData
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    // this.getDepartMentList();
    this.getRoleList()
    this.getSpecialist()

    // 引入mtech-common/utils中的防抖，(mtech-common/utils )
    this.fuzzyNamechange = utils.debounce(this.fuzzyNamechange, 300)
  },
  async created() {},
  watch: {},
  methods: {
    fuzzyNamechange(val) {
      let params = {
        fuzzyName: val.text,
        orgLevelCode: 'ORG05',
        orgType: 'ORG001ADM'
      }
      this.$API.supplierIndex.currentTenantEmployees(params).then((res) => {
        this.anodrslist = []
        if (res.code == 200 && res.data != null) {
          this.anodrslist = res.data.map((item) => {
            item.name = item.employeeName + item.phoneNum + '-' + item.departmentOrgName
            return item
          })
        }
      })
    },
    expertNamechange(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(this.userList.filter((f) => f?.expertName.indexOf(e.text) > -1))
      } else {
        e.updateData(this.userList)
      }
    },
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    typeChange(val) {
      this.$refs.dialogRef.clearValidate()
      if (val == 1) {
        this.getSpecialist()
      } else if (val == 2) {
        if (this.anodrslist.length == 0) {
          // 切换成员类型为公司员工-获取姓名（员工）下拉数据（初始没参的时候默认传个"a"（和后端定好的）,列出默认的前20条）
          this.fuzzyNamechange({ text: 'a' })
        }
      }
      this.userObj = {}
      this.roleObj = {}
      this.deptObj = {}
      this.userList = []
      this.formData.roleCode = ''
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((value) => {
        if (value) {
          const masterArray = this.teamData.filter((i) => i.roleCode === 'leader')
          const hasMember = this.teamData.filter((i) => i.userId === this.userObj.userId)
          if (masterArray.length && this.roleObj.roleCode === 'leader') {
            this.$toast({
              content: this.$t('已存在组长，请重新选择！'),
              type: 'warning'
            })
            return
          }
          if (hasMember.length) {
            this.$toast({
              content: this.$t('已存在该团队成员，请重新选择！'),
              type: 'warning'
            })
            return
          }
          const obj = {
            ...this.userObj,
            ...this.roleObj,
            ...this.deptObj,
            ...this.formObj
          }
          this.$emit('confirm-function', obj)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 根据公司查询部门
    async getDepartMentList() {
      const param = { organizationId: this.modalData.data.organizationId }
      // const param = { organizationId: "1475797553271898114" };
      const res = await this.$API.supplierReviewTask.getMasterDepartMent(param)
      if (res) {
        this.fieldsTemplate.dataSource = res?.data || []
        this.fieldsTemplate.key = this.randomString()
      }
    },
    // 查询角色
    async getRoleList() {
      const param = { dictCode: 'reviewUserType' }
      const res = await this.$API.supplierReviewTask.getMasterReviewTemplateType(param)
      if (res) {
        this.roleList = res?.data || []
      }
    },
    // 根据部门查询员工、
    async getUserList(id) {
      const param = { orgId: id }
      const res = await this.$API.supplierReviewTask.getMaseterEmplyee(param)
      console.error(res, 'res123')
      if (res) {
        this.userFields = { value: 'id', text: 'employeeName' }
        this.userList = res?.data || []
      }
    },
    // 查询专家
    async getSpecialist() {
      const param = {
        page: { current: 1, size: 1000 },
        condition: 'and',
        rules: [
          {
            label: this.$t('专家姓名'),
            field: 'expertName',
            type: 'string',
            operator: 'contains',
            value: ''
          }
        ]
      }
      const res = await this.$API.supplierReviewTask.getSpecialist(param)
      if (res) {
        this.userFields = { value: 'id', text: 'expertName' }
        let _records = res?.data?.records || []
        _records.map((item) => {
          if (item.departmentName) {
            return (item.expertName =
              item.expertName + '-' + item.email + '-' + item.departmentName)
          } else {
            return (item.expertName = item.expertName + '-' + item.email)
          }
        })
        this.userList = _records
      }
    },
    // 选择人
    userHandle({ itemData }) {
      console.error(itemData, 'itemData123')
      if (!itemData) return
      if (this.formObj.userType == 1) {
        const { expertName, mobilePhone, userId, email, departmentCode, departmentName } = itemData
        const e = this.userObj
        e.phone = mobilePhone
        e.userName = expertName
        e.userId = userId
        e.email = email
        this.deptObj.deptCode = departmentCode
        this.deptObj.deptName = departmentName
      } else {
        const { uid, employeeName, email, phoneNum, departmentOrgCode, departmentOrgName } =
          itemData
        const e = this.userObj
        e.phone = phoneNum
        e.userName = employeeName
        e.userId = uid
        e.email = email
        this.deptObj.deptCode = departmentOrgCode
        this.deptObj.deptName = departmentOrgName
      }
    },
    // 选择角色
    roleHandle({ itemData }) {
      const { itemCode, name } = itemData
      const e = this.roleObj
      e.roleCode = itemCode
      e.roleName = name
    },
    fn(arr, id, obj) {
      arr.forEach((item) => {
        if (item.departmentCode == id) {
          obj.deptCode = item.departmentCode
          obj.deptName = item.name
          obj.id = item.id
        } else if (item.children && item.children.length > 0) {
          this.fn(item.children, id, obj)
        }
      })
    },
    // 选择部门
    nodeOnCheck({ itemData }) {
      const { id } = itemData
      this.fn(this.fieldsTemplate.dataSource, id, this.deptObj)
      this.getUserList(this.deptObj.id)
      delete this.deptObj.id
      this.userObj = {}
    }
  }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
.responsible {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-box {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.mt-form-item {
  width: 48%;
}
</style>
