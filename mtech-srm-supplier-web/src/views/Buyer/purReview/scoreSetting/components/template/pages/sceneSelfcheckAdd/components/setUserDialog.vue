<template>
  <mt-dialog
    ref="userDialog"
    :header="$t('选择评分人')"
    :buttons="buttons"
    size="small"
    :show-close-icon="true"
  >
    <div style="padding: 50px">
      <mt-select
        v-model="member"
        :data-source="userLists"
        :fields="{ text: 'userName', value: 'userId' }"
        :show-clear-button="true"
        :placeholder="$t('请选择评分人')"
      />
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      member: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  computed: {
    userLists() {
      return this.modalData.userLists
    }
  },
  mounted() {
    this.$refs['userDialog'].ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      if (this.member === '') {
        this.$toast({ content: this.$t('请选择打分人！'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', this.member)
    }
  }
}
</script>

<style></style>
