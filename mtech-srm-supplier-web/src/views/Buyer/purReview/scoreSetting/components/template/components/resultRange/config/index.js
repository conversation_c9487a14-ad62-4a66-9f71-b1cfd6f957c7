import { i18n } from '@/main'
const columnData = [
  {
    type: 'checkbox'
  },
  {
    field: 'a',
    width: '400',
    headerText: i18n.t('评审项')
  },
  {
    field: 'b',
    width: '105',
    headerText: i18n.t('分值范围')
  },
  {
    field: 'c',
    width: '105',
    headerText: i18n.t('权重')
  },
  {
    field: 'd',
    width: '105',
    headerText: i18n.t('自查得分')
  },
  {
    field: 'e',
    width: '150',

    headerText: i18n.t('自查得分原因')
  },
  {
    field: 'f',
    width: '105',
    headerText: i18n.t('合格线')
  },
  {
    field: 'g',
    width: '105',
    headerText: i18n.t('得分')
  },
  {
    field: 'h',
    headerText: i18n.t('打分原因')
  },
  // {
  //   field: "i",
  //   headerText: i18n.t("不适用"),
  // },
  {
    field: 'j',
    width: '105',
    headerText: i18n.t('打分人')
  },
  {
    field: 'k',
    headerText: i18n.t('打分时间')
  }
]

const dataSource = [
  {
    a: 1,
    b: 2,
    c: 3,
    d: 4,
    e: 5,
    f: 6,
    g: 7,
    h: 8,
    i: 9,
    j: 10,
    k: 11,
    childrens: [{ a: 1, b: 2, c: 3, d: 4, e: 5, f: 6, g: 7, h: 8, i: 9, j: 10, k: 11 }]
  }
]
export const pageConfig = [
  {
    useToolTemplate: false,
    treeGrid: {
      height: 400,
      allowPaging: true,
      columnData,
      childMapping: 'childrens',
      autoCheckHierarchy: true,
      // dataSource: [],
      dataSource: dataSource
    }
  }
]
