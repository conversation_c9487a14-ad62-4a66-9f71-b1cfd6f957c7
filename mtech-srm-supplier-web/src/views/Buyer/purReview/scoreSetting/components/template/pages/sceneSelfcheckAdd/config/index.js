import { i18n } from '@/main.js'

const joinSymbolOptions = [
  { label: i18n.t('当'), value: '0' },
  { label: i18n.t('且'), value: '1' },
  { label: i18n.t('或'), value: '2' }
]
const conditionObjectOptions = [
  { label: i18n.t('得分'), value: '1' },
  { label: i18n.t('合格项'), value: '2' },
  { label: i18n.t('不合格项'), value: '3' },
  { label: i18n.t('结果'), value: '4' }
]
const symbolOptions = [
  { label: '>', value: '1' },
  { label: '<', value: '2' },
  { label: '>=', value: '3' },
  { label: '<=', value: '4' },
  { label: '=', value: '5' },
  { label: '!=', value: '6' }
]

export const columnData = [
  {
    field: 'joinSymbol',
    title: i18n.t('逻辑符号'),
    formatter: ({ cellValue }) => {
      let item = joinSymbolOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'templateTypeName',
    title: i18n.t('评审类型')
  },
  {
    field: 'conditionObject',
    title: i18n.t('评审条件'),
    formatter: ({ cellValue }) => {
      let item = conditionObjectOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'symbol',
    title: i18n.t('操作符'),
    formatter: ({ cellValue }) => {
      let item = symbolOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'standard',
    title: i18n.t('得分')
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    slots: {
      default: 'operateDefault'
    }
  }
]
