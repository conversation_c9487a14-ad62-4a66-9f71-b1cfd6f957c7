import { i18n } from '@/main'
import utils from '@/utils/utils'
import Vue from 'vue'
export const columnData = [
  {
    type: 'checkbox'
  },
  {
    field: 'itemName',
    headerText: i18n.t('评审项'),
    width: '360'
  },
  {
    field: 'range', //拼接scoreBegin-scoreEnd
    headerText: i18n.t('分值范围'),
    width: 100
  },
  {
    field: 'weight',
    headerText: i18n.t('权重(%)'),
    width: 100
  },
  {
    field: 'selfScore',
    headerText: i18n.t('自查得分'),
    width: 100
  },
  {
    field: 'selfReason',
    headerText: i18n.t('自查原因'),
    width: 200
  },
  {
    field: 'unqualified', //symbol  score
    headerText: i18n.t('合格线'),
    width: 120
  },
  {
    field: 'actScore',
    headerText: i18n.t('得分'),
    width: 100
  },
  {
    field: 'reason',
    headerText: i18n.t('打分依据'),
    width: 200
  },
  // {
  //   field: "useSetStatus",
  //   headerText: i18n.t("不适用"),
  //   template: () => {
  //     return {
  //       template: Vue.component("useSetStatus", {
  //         template: `<div>
  //           <span v-if="!data.itemResponses">{{status}}</span>
  //         </div>`,
  //         data() {
  //           return {
  //             utils,
  //           };
  //         },
  //         computed: {
  //           status() {
  //             return this.data.useSetStatus == 1 ? i18n.t("是") : i18n.t("否");
  //           },
  //         },
  //       }),
  //     };
  //   },
  // },
  {
    field: 'scoreUserName',
    headerText: i18n.t('打分人'),
    width: 160
  },
  {
    field: 'scoreTime',
    headerText: i18n.t('打分时间'),
    width: 180,
    template: () => {
      return {
        template: Vue.component('scoreTime', {
          template: `<div>
            <span>{{scoreTime}}</span>
          </div>`,
          data() {
            return {
              utils
            }
          },
          computed: {
            scoreTime() {
              if (this.data.scoreTime) {
                return this.utils.formateTime(Number(this.data.scoreTime), 'yyyy-MM-dd hh:mm:ss')
              } else {
                return ''
              }
            }
          }
        })
      }
    }
  }
]
