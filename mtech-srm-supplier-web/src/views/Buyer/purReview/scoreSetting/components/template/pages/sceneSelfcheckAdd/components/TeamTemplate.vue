<template>
  <div>
    <sc-table
      ref="teamTable"
      :columns="columns"
      :table-data="tableData"
      :fix-height="300"
      style="margin-top: 20px"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  props: {
    teamOrgItem: {
      type: Object,
      default: () => {}
    },
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      columns: [
        { type: 'checkbox', width: '50' },
        {
          field: 'userName',
          title: this.$t('姓名')
        },
        {
          field: 'email',
          title: this.$t('邮箱')
        },
        {
          field: 'deptName',
          title: this.$t('部门')
        },
        {
          field: 'dutyDescription',
          title: this.$t('职责描述')
        },
        {
          field: 'phone',
          title: this.$t('电话')
        },
        {
          field: 'roleName',
          title: this.$t('角色')
        }
      ],
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      const selectedRecords = this.$refs.teamTable.$refs.xGrid.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$dialog({
        modal: () => import('./addTeamer.vue'),
        data: {
          title: this.$t('新增团队成员'),
          data: this.teamOrgItem,
          teamData: this.tableData
        },
        success: (e) => {
          if (e.userType == 2) {
            e.orgCode = this.teamOrgItem.organizationCode
            e.orgName = this.teamOrgItem.organizationName
          }
          this.$emit('add', e)
        }
      })
    },
    handleDelete(selectedRecords) {
      this.$emit('delete', selectedRecords)
    }
  }
}
</script>

<style></style>
