import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox' //该列为checkox
  },
  {
    field: 'userName',
    headerText: i18n.t('姓名')
  },
  {
    field: 'email',
    headerText: i18n.t('邮箱')
  },
  {
    field: 'deptName',
    headerText: i18n.t('部门')
  },
  {
    field: 'dutyDescription',
    headerText: i18n.t('职责描述')
  },
  {
    field: 'phone',
    headerText: i18n.t('电话')
  },
  {
    field: 'roleName',
    headerText: i18n.t('角色')
  }
]
export const pageConfig = () => [
  {
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'Add', title: i18n.t('新增'), icon: 'icon_table_new' },
          { id: 'Delete', title: i18n.t('删除'), icon: 'icon_list_delete' }
        ],
        []
      ]
    },
    grid: {
      // allowTextWrap: true,
      allowPaging: false,
      columnData,
      dataSource: []
    }
  }
]
