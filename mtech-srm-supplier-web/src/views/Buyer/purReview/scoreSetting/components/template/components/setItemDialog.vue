<template>
  <mt-dialog
    ref="dialog"
    :enable-resize="false"
    :position="{ X: 'right', Y: 'top' }"
    css-class="right-wrapper"
    :buttons="buttons"
    :header="header"
    height="100%"
    @beforeClose="cancel"
  >
    <div class="dialog-content" style="height: 100%">
      <div class="oreder-warp" style="height: 100%">
        <div class="treeView">
          <ul class="left-nav">
            <li
              :class="{ active: index_ == index }"
              v-for="(item, index) in reviewContent"
              :key="item.templateTypeCode"
              @click="indexChange(index)"
            >
              {{ item.templateTypeName }}
            </li>
          </ul>
        </div>
        <div class="table">
          <!-- <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="pageConfig"
            @handleClickToolBar="handleClickToolBar"
          ></mt-template-page> -->
          <vxe-toolbar>
            <template #buttons>
              <vxe-button status="primary" @click="setScoretor">{{ $t('设置打分人') }}</vxe-button>
            </template>
          </vxe-toolbar>
          <ScTable
            ref="xTable"
            :columns="columns"
            :table-data="tableData"
            :checkbox-config="checkedConfig"
            :tree-config="treeConfig"
            height="80%"
            @checkbox-all="selectAllEvent"
            @checkbox-change="selectChangeEvent"
          ></ScTable>
        </div>
      </div>
    </div>
    <mt-dialog
      ref="toast"
      :header="$t('选择评分人')"
      :buttons="sonbuttons"
      size="small"
      :show-close-icon="true"
    >
      <div style="padding: 50px">
        <mt-select
          :data-source="formObject.userLists"
          :show-clear-button="true"
          :placeholder="$t('请选择评分人')"
          v-model="member"
          :fields="{ text: 'userName', value: 'userId' }"
        ></mt-select>
      </div>
    </mt-dialog>
  </mt-dialog>
</template>
<script>
import ScTable from '@/components/ScTable/src/index'
import { i18n } from '@/main'
// import { pageConfig, columnData } from './config/index'

export default {
  components: {
    ScTable
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      index_: 0,
      reviewContent: [],
      pageConfig: [],
      sonbuttons: [
        {
          click: this.soncancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.sonconfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      formObject: {},
      member: '',
      columns: [
        { type: 'checkbox', width: 120, treeNode: true, fixed: 'left' },
        {
          field: 'reviewName',
          title: i18n.t('评审项'),
          width: '360'
        },
        {
          field: 'range',
          title: i18n.t('分值范围'),
          width: 100
        },
        {
          field: 'weight',
          title: i18n.t('权重(%)'),
          width: 100
        },
        {
          field: 'selfScore',
          title: i18n.t('自查得分'),
          width: 100
        },
        {
          field: 'selfReason',
          title: i18n.t('自查原因'),
          width: 200
        },
        {
          field: 'unqualified', //symbol  score
          title: i18n.t('合格线'),
          width: 120
        },
        {
          field: 'scoreUserName',
          title: i18n.t('打分人'),
          width: 160
        }
      ],
      checkedConfig: {
        checkField: 'checked'
      },
      treeConfig: {
        transform: true,
        rowField: 'reviewCode',
        parentField: 'parentDimensionCode',
        expandAll: true
      },
      tableData: [],
      selectedRows: []
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    buttons() {
      return [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.formObject = this.modalData.data
    // 编辑评审清单-数据回显
    // 如果 评审类型 为 【例行审查】，点击 下一步 需要单独请求接口去获取 评审项页 编辑回显数据（和新增一样）
    this.reviewContent = this.modalData.itemResponse
    this.indexChange(0)
  },
  methods: {
    soncancel() {
      this.$refs.toast.ejsRef.hide()
    },
    sonconfirm() {
      if (this.member == '') {
        this.$toast({ content: this.$t('请选择打分人！'), type: 'warning' })
        return
      }
      let scoretor = this.formObject.userLists.find((item) => item.userId === this.member)
      this.patchScoretor(scoretor)
      this.$refs.toast.ejsRef.hide()
    },
    patchScoretor(scoretor) {
      this.selectedRows.forEach((item) => {
        // 只给子级数据设置打分人
        if (item.parentDimensionCode) {
          item.scoreUserId = scoretor.userId
          item.scoreUserName = scoretor.userName
        }
      })
      this.$toast({ content: this.$t('操作成功！'), type: 'success' })
      this.$forceUpdate()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      let arr = []
      this.reviewContent.forEach((item) => {
        let obj = {
          templateCode: item.templateCode,
          templateTypeName: item.templateTypeName,
          templateTypeId: item.templateTypeId,
          templateItemRequests: []
        }
        if (item.dimensionDTOS) {
          item.dimensionDTOS.forEach((e) => {
            if (e.indexDTOS) {
              e.indexDTOS.forEach((e1) => {
                obj.templateItemRequests.push({
                  itemCode: e1.reviewCode,
                  scoreUserId: e1.scoreUserId,
                  scoreUserName: e1.scoreUserName
                })
              })
            }
          })
        }
        arr.push(obj)
      })
      this.formObject.templateLists = arr
      this.$API.supplierReviewTask.taskReviewTaskSave(this.formObject).then((res) => {
        console.log(res)
        this.$emit('confirm-function')
      })
    },
    indexChange(index) {
      this.index_ = index
      if (this.reviewContent.length > 0) {
        const tempList = this.reviewContent[this.index_].dimensionDTOS
        this.tableData = this.flatTableData(tempList)
      } else {
        this.tableData = []
      }
    },
    flatTableData(list) {
      let arr = []
      list.forEach((item) => {
        item.reviewName = item.dimensionName // 因为表格列设置中绑定的field是reviewName
        item.reviewCode = item.dimensionCode
        arr = arr.concat(item.indexDTOS || [])
      })
      const res = arr.concat(list)
      res.forEach((item) => {
        item.checked = false
        item.unqualified = item.qualifiedSymbol ? item.qualifiedSymbol + item.qualifiedScore : ''
        item.range = item.highestScore ? `${item.lowestScore || 0}-${item.highestScore}` : ''
      })
      return res
    },
    setScoretor() {
      if (!this.selectedRows.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.member = ''
      this.$refs.toast.ejsRef.show()
    },
    selectAllEvent({ $table }) {
      this.selectedRows = $table.getCheckboxRecords()
      this.$forceUpdate()
    },
    selectChangeEvent({ $table }) {
      this.selectedRows = $table.getCheckboxRecords()
      this.$forceUpdate()
    }
  }
}
</script>

<style lang="scss" scoped>
.oreder-warp {
  display: flex;
  .treeView {
    width: 180px;
    margin-right: 20px;
  }
  .table {
    width: calc(100% - 180px);
    height: 100%;
    flex: 1;
  }
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: rgba(0, 0, 0, 0.87);
}
::v-deep .e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #fff;
  border-color: #fff;
  border-bottom: 2px solid #00469c;
}
::v-deep .mt-tree-view .e-treeview .e-fullrow {
  opacity: 1;
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: #00469c;
}

.left-nav {
  line-height: 40px;
  text-align: center;
  li {
    cursor: pointer;
  }
  .active {
    background: linear-gradient(135deg, rgba(245, 84, 72, 1) 0%, rgba(255, 128, 118, 1) 100%);
    color: #fff;
  }
}
::v-deep .e-gridcontent .e-content {
  height: calc(100vh - 240px) !important;
}
</style>
