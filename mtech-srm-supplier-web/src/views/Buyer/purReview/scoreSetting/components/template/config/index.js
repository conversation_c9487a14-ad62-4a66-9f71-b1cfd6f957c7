//绩效考评模板Tab
// import Vue from "vue";
import { i18n } from '@/main'
import APIS from '@/apis/modules/supplierReviewTask.js'
import Vue from 'vue'
import utils from '@/utils/utils.js'
const asyncResult = async (param) => {
  const res = await APIS.APIS.getMasterReviewType(param)
  return res.data
}
let taskType = []
let taskStatus = []
let taskResult = []

export const masterInit = () => {
  asyncResult({ dictCode: 'reviewTaskType' }).then((r) => {
    taskType = r
  })
  asyncResult({ dictCode: 'reviewTaskStatus' }).then((r) => {
    taskStatus = r
  })

  asyncResult({ dictCode: 'reviewTaskResultStatus' }).then((r) => {
    taskResult = r
  })
}
const mapJson = (data) => {
  console.log('taskStatustaskStatustaskStatus13', taskStatus)
  const json = {}
  data.map((item) => {
    json[Number(item.itemCode)] = i18n.t(item.itemName)
  })
  return json
}
export const toolbar = (taskCode) => {
  if (taskCode == 'selfTempType') {
    // 自查
    return [
      {
        id: 'Add',
        icon: 'icon_solid_Createorder',
        title: i18n.t('新增')
      },
      // {
      //   id: 'ReviewApply',
      //   icon: 'icon_solid_upload',
      //   title: i18n.t('提交评审申请')
      // }
      {
        id: 'export',
        title: i18n.t('导出')
      }
    ]
  } else {
    // 现场审查
    return [
      {
        id: 'Add',
        icon: 'icon_solid_Createorder',
        title: i18n.t('新增')
        // permission: ["O_02_0049"],
      },
      {
        id: 'ReviewExpertExtraction',
        icon: 'icon_solid_upload',
        title: i18n.t('审查专家抽取')
      },
      {
        id: 'ReviewResult',
        icon: 'icon_solid_upload',
        title: i18n.t('提交评审结果')
      },
      {
        id: 'Release',
        icon: 'icon_solid_upload',
        title: i18n.t('发布')
      },
      {
        id: 'export',
        title: i18n.t('导出')
      }
    ]
  }
}
const columnData = () => {
  // if (taskCode == 'selfTempType') {
  return [
    {
      type: 'checkbox',
      width: 50
    },
    {
      width: 200,
      field: 'code',
      headerText: i18n.t('评审任务编码'),
      cellTools: [
        {
          id: 'edit',
          title: i18n.t('编辑'),
          visibleCondition: (data) => {
            return data.status == 10 || data.status == 30
          }
        },
        {
          id: 'delete',
          title: i18n.t('删除'),
          visibleCondition: (data) => {
            return data.status == 10 || data.status == 30
          }
        }
      ]
    },
    {
      field: 'name',
      headerText: i18n.t('名称'),
      width: 260
    },
    {
      field: 'supplierInternalCode',
      headerText: i18n.t('供应商编号-SRM'),
      cssClass: 'field-content',
      width: 260,
      cellTools: []
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编号-SAP'),
      width: 260
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: 260
    },
    {
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: {
          10: i18n.t('草稿'),
          20: i18n.t('申请待审批'),
          30: i18n.t('申请已驳回'),
          40: i18n.t('申请已批准'),
          50: i18n.t('结果待提交'),
          60: i18n.t('结果待审批'),
          70: i18n.t('结果已驳回'),
          80: i18n.t('结果已批准'),
          90: i18n.t('待反馈'),
          100: i18n.t('已反馈'),
          112: i18n.t('审查专家抽取失败'),
          113: i18n.t('审查专家待确认'),
          114: i18n.t('审查专家已确认'),
          115: i18n.t('现场审查中'),
          116: i18n.t('审查结果已提交'),
          117: i18n.t('审查结果已审批')
        }
      }
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类')
    },
    {
      field: 'buyerOrgName',
      headerText: i18n.t('公司')
    },
    {
      field: 'bizType',
      headerText: i18n.t('评审类型'),
      valueConverter: {
        type: 'map',
        map: mapJson(taskType)
      }
    },
    {
      field: 'sourceCode',
      headerText: i18n.t('来源')
    },
    {
      field: 'planTimeBegin',
      headerText: i18n.t('计划开始时间'),
      template: () => {
        return {
          template: Vue.component('planTimeBegin', {
            template: `<div>
                <span>{{time}}</span>
              </div>`,
            data: function () {
              return {
                time: ''
              }
            },
            created() {
              this.time = this.timeUtils()
            },
            methods: {
              timeUtils() {
                const timeArr = []
                if (this.data.planTimeBegin) {
                  timeArr.push(utils.formateTime(Number(this.data.planTimeBegin), 'yyyy-MM-dd'))
                }
                return timeArr.join('-')
              }
            }
          })
        }
      }
    },
    {
      field: 'planTimeEnd',
      headerText: i18n.t('计划结束时间'),
      template: () => {
        return {
          template: Vue.component('planTimeEnd', {
            template: `<div>
                <span>{{time}}</span>
              </div>`,
            data: function () {
              return {
                time: ''
              }
            },
            created() {
              this.time = this.timeUtils()
            },
            methods: {
              timeUtils() {
                const timeArr = []
                if (this.data.planTimeEnd) {
                  timeArr.push(utils.formateTime(Number(this.data.planTimeEnd), 'yyyy-MM-dd'))
                }
                return timeArr.join('-')
              }
            }
          })
        }
      }
    },
    {
      field: 'resultStatus',
      headerText: i18n.t('评审结果'),
      valueConverter: {
        type: 'map',
        map: mapJson(taskResult)
      }
    },
    {
      field: 'unqualifiedCount',
      headerText: i18n.t('不合格项数')
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建日期'),
      queryType: 'date',
      format: 'yyyy-MM-dd hh:mm:ss'
    }
  ]
  // } else {
  //   return [
  //     {
  //       type: 'checkbox',
  //       width: 50
  //     },
  //     {
  //       width: 200,
  //       field: 'code',
  //       headerText: i18n.t('评审任务编码'),
  //       cellTools: [
  //         {
  //           id: 'edit',
  //           title: i18n.t('编辑'),
  //           visibleCondition: (data) => {
  //             return data.status == 10 || data.status == 30
  //           }
  //         }
  //       ]
  //     },
  //     {
  //       field: 'name',
  //       headerText: i18n.t('名称'),
  //       width: 260
  //     },
  //     {
  //       field: 'supplierInternalCode',
  //       headerText: i18n.t('供应商编号-SRM'),
  //       cssClass: 'field-content',
  //       width: 260,
  //       cellTools: []
  //     },
  //     {
  //       field: 'supplierCode',
  //       headerText: i18n.t('供应商编号-SAP'),
  //       width: 260
  //     },
  //     {
  //       field: 'supplierName',
  //       headerText: i18n.t('供应商名称'),
  //       width: 260
  //     },
  //     {
  //       field: 'status',
  //       headerText: i18n.t('状态'),
  //       valueConverter: {
  //         type: 'map',
  //         map: mapJson(taskStatus)
  //       }
  //     },
  //     {
  //       field: 'supplierProvinceName',
  //       headerText: i18n.t('省')
  //     },
  //     {
  //       field: 'supplierCityName',
  //       headerText: i18n.t('市')
  //     },
  //     {
  //       field: 'categoryName',
  //       headerText: i18n.t('品类')
  //     },
  //     {
  //       field: 'buyerOrgName',
  //       headerText: i18n.t('公司')
  //     },
  //     {
  //       field: 'bizType',
  //       headerText: i18n.t('评审类型'),
  //       valueConverter: {
  //         type: 'map',
  //         map: mapJson(taskType)
  //       }
  //     },
  //     {
  //       field: 'sourceCode',
  //       headerText: i18n.t('来源')
  //     },
  //     {
  //       field: 'planTimeBegin',
  //       headerText: i18n.t('计划开始时间'),
  //       template: () => {
  //         return {
  //           template: Vue.component('planTimeBegin', {
  //             template: `<div>
  //               <span>{{time}}</span>
  //             </div>`,
  //             data: function () {
  //               return {
  //                 time: ''
  //               }
  //             },
  //             created() {
  //               this.time = this.timeUtils()
  //             },
  //             methods: {
  //               timeUtils() {
  //                 const timeArr = []
  //                 if (this.data.planTimeBegin) {
  //                   timeArr.push(utils.formateTime(Number(this.data.planTimeBegin), 'yyyy-MM-dd'))
  //                 }
  //                 return timeArr.join('-')
  //               }
  //             }
  //           })
  //         }
  //       }
  //     },
  //     {
  //       field: 'planTimeEnd',
  //       headerText: i18n.t('计划结束时间'),
  //       template: () => {
  //         return {
  //           template: Vue.component('planTimeEnd', {
  //             template: `<div>
  //               <span>{{time}}</span>
  //             </div>`,
  //             data: function () {
  //               return {
  //                 time: ''
  //               }
  //             },
  //             created() {
  //               this.time = this.timeUtils()
  //             },
  //             methods: {
  //               timeUtils() {
  //                 const timeArr = []
  //                 if (this.data.planTimeEnd) {
  //                   timeArr.push(utils.formateTime(Number(this.data.planTimeEnd), 'yyyy-MM-dd'))
  //                 }
  //                 return timeArr.join('-')
  //               }
  //             }
  //           })
  //         }
  //       }
  //     },
  //     {
  //       field: 'resultStatus',
  //       headerText: i18n.t('评审结果'),
  //       valueConverter: {
  //         type: 'map',
  //         map: mapJson(taskResult)
  //       }
  //     },
  //     {
  //       field: 'unqualifiedCount',
  //       headerText: i18n.t('不合格项数')
  //     },
  //     {
  //       field: 'createUserName',
  //       headerText: i18n.t('创建人')
  //     },
  //     {
  //       field: 'createDate',
  //       headerText: i18n.t('创建日期'),
  //       queryType: 'date',
  //       format: 'yyyy-MM-dd hh:mm:ss'
  //     }
  //   ]
  // }
}

export const pageConfig = (url, taskCode) => [
  {
    gridId: '713de498-bd35-4b80-aa47-e504712c8303',
    toolbar: toolbar(taskCode),
    grid: {
      columnData: columnData(taskCode),
      asyncConfig: {
        url
      },
      frozenColumns: 3
    }
  }
]

//详情

export const detailPageConfig = [
  {
    gridId: 'fda29216-84fb-40e5-9858-24697c1f35e0',
    title: i18n.t('评审单')
  },
  {
    gridId: '0d3566cd-6e9a-4ab9-ab24-ab1dd52e8a4b',
    title: i18n.t('评审团队')
  },
  {
    gridId: '14d56eba-c8e1-43d8-bf7e-084431b43f82',
    title: i18n.t('不合格项')
  },
  {
    gridId: 'bbe85ff7-e943-49e3-99b3-5fc2779e7861',
    title: i18n.t('结果页')
  }
]

export const newdetailPageConfig = [
  {
    gridId: 'f721989b-21f2-4843-a78e-5fa151f441b5',
    title: i18n.t('评审单')
  },
  {
    gridId: '27df8052-e6ba-4f65-b8f6-1dfd7168e6bb',
    title: i18n.t('评审团队')
  }
]
