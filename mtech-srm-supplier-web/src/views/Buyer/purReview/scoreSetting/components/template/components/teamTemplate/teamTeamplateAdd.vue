<template>
  <div class="teamWraper">
    <div style="margin: 5px">
      <vxe-button status="primary" :content="$t('确认')" :disabled="isDisabledBtn" />
      <vxe-button status="primary" :content="$t('拒绝')" :disabled="isDisabledBtn" />
    </div>
    <mt-template-page
      ref="teamTabRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      :hidden-tabs="true"
    ></mt-template-page>
  </div>
</template>
<script>
import { columnData } from '../teamTemplate/config/index'
export default {
  props: {
    teamORgItem: {
      type: Object,
      default: () => {}
    },
    teamData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      isDisabledBtn: false,
      pageConfig: [
        {
          gridId: 'b370cca4-7409-423b-bbb1-51429fa1230d',
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [],
              [
                {
                  id: 'Add',
                  title: this.$t('新增'),
                  icon: 'icon_table_new',
                  visibleCondition: () => {
                    return !!this.teamORgItem
                  }
                },
                {
                  id: 'Delete',
                  title: this.$t('删除'),
                  icon: 'icon_list_delete',
                  visibleCondition: () => {
                    return !!this.teamORgItem
                  }
                }
              ]
            ]
          },
          grid: {
            allowPaging: false,
            columnData: columnData,
            dataSource: []
          }
        }
      ]
    }
  },
  watch: {
    teamData: {
      handler(val) {
        console.log('this.teamDatathis.teamDatathis.teamData123', val.userType !== 1)
        this.isDisabledBtn = val.userType !== 1
      }
    },
    immediate: true,
    deep: true
  },
  mounted() {
    this.pageConfig[0].grid.dataSource = this.teamData || []
  },
  methods: {
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'Delete') {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id == 'Add') {
        this.analysisOfSettingAdd()
      }
      if (e.toolbar.id == 'Delete') {
        for (let i = 0; i < this.teamData.length; i++) {
          let bol = _selectGridRecords.some((item) => {
            return item.userId == this.teamData[i].userId
          })
          if (bol) {
            this.teamData.splice(i, 1)
            i--
          }
        }
      }
    },
    analysisOfSettingAdd() {
      this.$dialog({
        modal: () => import('./addTeamer.vue'),
        data: {
          title: this.$t('新增团队成员'),
          data: this.teamORgItem,
          teamData: this.teamData
        },
        success: (e) => {
          if (e.userType == 2) {
            e.orgCode = this.teamORgItem.organizationCode
            e.orgName = this.teamORgItem.organizationName
          }
          this.teamData.push(e)
        }
      })
    }
  }
}
</script>
<style scoped>
.teamWraper {
  height: 100%;
}
</style>
