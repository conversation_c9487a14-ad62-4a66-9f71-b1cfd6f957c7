<template>
  <!-- 评审清单 -->
  <div class="score-setting-target">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { pageConfig, toolbar } from './config'
import MixIn from '../../config/mixin'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    taskCode: {
      type: String,
      default: () => {
        return ''
      }
    },
    taskFlag: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  provide() {
    return {
      tabNum: this.tabNum
    }
  },
  mixins: [MixIn],
  data() {
    return {
      delBuyerAssessRequest: {
        ids: []
      },
      //列表调用
      pageConfig: [],
      tabNum: 2
    }
  },
  computed: {
    routeName() {
      return this.$route.name
    }
  },
  mounted() {
    let _pageConfig = pageConfig(this.$API.supplierReviewTask.taskReviewTaskRecord, this.taskCode)
    _pageConfig[0].grid.asyncConfig.defaultRules = []
    console.log('this.info.status', this.info.status)
    if (this.routeName === 'pur-category-certification-detail') {
      _pageConfig[0].grid.asyncConfig.defaultRules.push({
        label: this.$t('品类认证项目编号'),
        field: 'authProjectCode',
        type: 'string',
        operator: 'equal',
        value: this.info.projectCode
      })
      if (this.taskCode == 'selfTempType') {
        _pageConfig[0].grid.asyncConfig.defaultRules.push({
          label: this.$t('评审类型'),
          field: 'bizType',
          operator: 'equal',
          value: '4'
        })
      } else {
        _pageConfig[0].grid.asyncConfig.defaultRules.push({
          label: this.$t('评审类型'),
          field: 'bizType',
          operator: 'equal',
          value: '3'
        })
      }

      if (this.info.status != 10 && this.info.status != 30) {
        _pageConfig[0].toolbar = []
        _pageConfig[0].useToolTemplate = true
      } else {
        _pageConfig[0].toolbar = toolbar(this.taskCode)
        _pageConfig[0].useToolTemplate = false
      }
    }
    this.pageConfig = _pageConfig
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field === 'code') {
        this.$router.push({
          path: '/supplier/pur/review-detail?tab=2',
          query: {
            id: e.data.id,
            code: e.data.code,
            flag: this.taskFlag
          }
        })
      }
      const { field, data } = e
      let params = data.buyerPartnerRelationLinkResponse
      if (field === 'supplierInternalCode' && data) {
        this.$router.push({
          path: 'profileDetail',
          query: {
            partnerArchiveId: params.partnerArchiveId,
            orgId: params.orgId,
            supplierEnterpriseId: params.supplierEnterpriseId,
            partnerRelationCode: params.partnerRelationCode
          }
        })
      }
    },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length <= 0 &&
        ['ReviewApply', 'ReviewResult', 'ReviewExpertExtraction'].includes(e.toolbar.id)
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.toolbar.id) {
        case 'Add':
          this.analysisOfSettingAdd()
          break
        case 'ReviewApply':
          this.submitReviewApplyBtn(_selectGridRecords)
          break
        case 'ReviewResult':
          this.submitReviewResultBtn(_selectGridRecords)
          break
        case 'ReviewExpertExtraction':
          this.submitReviewExpertExtractionBtn(_selectGridRecords)
          break
        case 'export':
          this.handleExport('exportReviewList')
          break
        default:
          break
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'edit') {
        // this.$dialog({
        //   modal: () => import('./components/addReviewRecordDialog.vue'),
        //   data: {
        //     title: this.$t('编辑评审清单'),
        //     code: e.data.code,
        //     info: this.info,
        //     taskCode: this.taskCode
        //   },
        //   success: () => {
        //     // this.dialogItem(e);
        //     this.$refs.templateRef.refreshCurrentGridData()
        //   }
        // })
        this.$router.push({
          path: '/supplier/pur/category-certification-detail/scene-selfcheck-add',
          query: {
            id: this.$route.query?.id,
            authProjectCode: e.data.authProjectCode,
            taskCode: e.data.bizType === 3 ? 'sceneSelfTempType' : 'selfTempType',
            type: 'edit',
            reviewTaskId: e.data.id
          }
        })
      } else if (e.tool.id == 'delete') {
        // 供应商自查-采方-删除
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            this.$API.supplierReviewTask.delReviewTask({ code: e.data.code }).then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('删除成功'), type: 'success' })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      }
    },
    // 重新抽取专家 审查专家抽取
    submitReviewExpertExtractionBtn(row) {
      if (row.length > 1)
        return this.$toast({ content: this.$t('只允许对一行数据进行操作'), type: 'warning' })
      const curRowCode = { code: row[0].code }
      console.log('curRowCodecurRowCode', curRowCode, row)
      this.$API.supplierReviewTask.redrawExpert(curRowCode).then((res) => {
        if (res.code === 200 && res.code) {
          return this.$toast({ content: res.msg, type: 'success' })
        }
      })
    },
    //新增配置
    analysisOfSettingAdd() {
      this.$router.push({
        path: '/supplier/pur/category-certification-detail/scene-selfcheck-add',
        query: {
          id: this.$route.query?.id,
          authProjectCode: this.info.projectCode,
          taskCode: this.taskCode,
          type: 'add'
        }
      })
      // this.$dialog({
      //   modal: () => import('./components/addReviewRecordDialog.vue'),
      //   data: {
      //     title: this.$t('新增评审清单'),
      //     // ownerOrg: this.ownerOrg,
      //     info: this.info,
      //     taskCode: this.taskCode
      //   },
      //   success: (e) => {
      //     this.$refs.templateRef.refreshCurrentGridData()
      //     // this.dialogItem(e);
      //   }
      // })
    },
    //提交审查申请
    submitReviewApplyBtn(val) {
      const acrossResult = val.filter((e) => e.status == 40)
      if (acrossResult.length) {
        this.$toast({
          content: this.$t('已审批状态的任务不可提交'),
          type: 'warning'
        })
        return
      }
      val.forEach((item) => {
        this.delBuyerAssessRequest.ids.push(item.code)
      })
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('提交审查申请')
        },
        success: () => {
          this.$API.analysisOfSetting.submitReviewApply(this.delBuyerAssessRequest.ids).then(() => {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
          this.delBuyerAssessRequest.ids = []
        }
      })
    },
    // 提交审查结果
    submitReviewResultBtn(val) {
      let arrCode = []
      val.forEach((item) => {
        arrCode.push(item.code)
      })
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('提交审查结果')
        },
        success: () => {
          this.$API.analysisOfSetting.submitReviewResult(arrCode).then(() => {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
          arrCode = []
        }
      })
    },
    //Excel导入
    analysisOfSettingEXimport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ './components/Excelimport.vue'
          ),
        data: {
          title: this.$t('上传/导入')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //Excel模板下载
    analysisOfSettingEXdownload() {
      console.log('Excel模板下载逻辑')
    },
    dialogItem(data) {
      this.$dialog({
        modal: () => import('./components/setItemDialog.vue'),
        data: {
          title: this.$t('维护指标评审人'),
          data: data
        },
        success: (e) => {
          console.log(e)
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-target {
  height: 100%;
  .filter-org {
    background: #fff;
    padding: 10px;
    /deep/.owner-org-tree {
      width: 300px;
    }
  }
}
</style>
