import { i18n } from '@/main'
import Vue from 'vue'
export const columnData = [
  // {
  //   width: "150",
  //   showCheckbox: true,
  //   // type: "checkbox",
  // },
  {
    field: 'dimensionName',
    headerText: i18n.t('评审项'),
    width: '400',
    template: () => {
      return {
        template: Vue.component('planEndTime', {
          template: `<div>
          {{data.dimensionName?data.dimensionName:data.reviewName}}
          </div>`,
          data: function () {}
        })
      }
    }
  },
  {
    field: 'highestScore',
    headerText: i18n.t('分值范围'),
    template: () => {
      return {
        template: Vue.component('highestScore', {
          template: `<div>
          {{score()}}
          </div>`,
          methods: {
            score() {
              if (
                this.data.lowestScore !== null &&
                this.data.lowestScore !== '' &&
                this.data.lowestScore !== undefined
              ) {
                return `${this.data.lowestScore}-${this.data.highestScore}`
              }
              return ''
            }
          }
        })
      }
    }
  },
  {
    field: 'weight',
    width: '105',
    headerText: i18n.t('权重(%)'),
    template: () => {
      return {
        template: Vue.component('weight', {
          template: `<div>
          {{data.weight?data.weight:''}}
          </div>`,
          data: function () {}
        })
      }
    }
  },
  {
    field: 'd',
    width: '105',
    headerText: i18n.t('自查得分')
  },
  {
    field: 'e',
    width: '140',
    headerText: i18n.t('自查得分原因')
  },
  {
    field: 'qualifiedScore',
    width: '105',
    headerText: i18n.t('合格线'),
    template: () => {
      return {
        template: Vue.component('qualifiedScore', {
          template: `<div>
          {{data.qualifiedSymbol?data.qualifiedSymbol:''}}{{data.qualifiedScore?data.qualifiedScore:''}}
          </div>`,
          data: function () {}
        })
      }
    }
  },
  {
    field: 'scoreUserName',
    width: '105',
    headerText: i18n.t('打分人')
  }
]
export const pageConfig = (dataSource) => [
  {
    gridId: '24d4ecd2-8694-4207-a507-80f5232051c7',
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [[{ id: 'Add', title: i18n.t('设置打分人'), icon: 'icon_table_new' }]]
    },
    treeGrid: {
      allowPaging: false,
      columnData: [],
      childMapping: 'indexDTOS',
      enableVirtualization: true,
      autoCheckHierarchy: true,
      dataSource
    }
  }
]
