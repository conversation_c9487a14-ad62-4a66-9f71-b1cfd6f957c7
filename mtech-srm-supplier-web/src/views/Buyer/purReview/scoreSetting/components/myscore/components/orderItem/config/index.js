import { i18n } from '@/main'
import Vue from 'vue'
import MtSwitch from '@mtech-ui/switch'

export default {
  components: {
    MtSwitch
  }
}

export let reviewTemplateData = []
const totalScore = () => {
  reviewTemplateData.forEach((item) => {
    item.actScore = item.itemResponses.reduce((p, c) => {
      return p + Number(c.actScore)
    }, 0)
  })
}
import utils from '@/utils/utils.js'
export const columnData = () => [
  {
    type: 'checkbox'
  },
  {
    field: 'itemName',
    headerText: i18n.t('评审项'),
    width: '300'
  },
  {
    field: 'range', //拼接scoreBegin-scoreEnd
    headerText: i18n.t('分值范围')
  },
  {
    field: 'weight',
    headerText: i18n.t('权重(%)')
  },
  {
    field: 'selfScore',
    headerText: i18n.t('自查得分')
  },
  {
    field: 'selfReason',
    headerText: i18n.t('自查原因')
  },
  {
    field: 'unqualified', //symbol  score
    headerText: i18n.t('合格线')
  },
  {
    field: 'actScore',
    headerText: i18n.t('得分'),
    template: function () {
      return {
        template: Vue.component('actScore', {
          template: `<div>
          <mt-inputNumber :disabled="enable" ref="inputNumber" :min="data.scoreBegin" :max="data.scoreEnd" width="120" height="26"
           @change="handleInputNum"  v-model="data.actScore"></mt-inputNumber>
          </div>

       `,
          data() {
            return {
              data: {},
              i18n: i18n
            }
          },
          mounted() {
            console.log('==this.data==', this.data)
            totalScore()
            // this.symbol = symbolMap[this.data.qualifiedSymbol];
          },
          computed: {
            enable() {
              // RESULT_WAIT(50,"结果待提交"),//20
              // RESULT_REJECT(70,"结果已驳回"),//23
              return this.data.level == 0 || this.data.statusCode != 10
            }
          },
          methods: {
            handleInputNum(e) {
              if (e < this.data.scoreBegin && e !== null && this.data.scoreBegin !== null) {
                e = this.data.scoreBegin
              }
              if (e > this.data.scoreEnd && e !== null && this.data.scoreEnd !== null) {
                e = this.data.scoreEnd
              }

              if (!reviewTemplateData.length) return
              if (Object.hasOwnProperty.call(this.data, 'parentItem')) {
                reviewTemplateData
                  .find((e) => e.itemCode === this.data.parentItem.itemCode)
                  .itemResponses.find((x) => x.itemCode === this.data.itemCode).actScore =
                  e != null ? Number(e) : e
              } else {
                reviewTemplateData.find((e) => e.itemCode === this.data.itemCode).actScore =
                  e != null ? Number(e) : e
              }
              totalScore()
              console.log('=reviewTemplateData=', reviewTemplateData)
            }
          },
          watch: {
            'data.actScore': function (n) {
              if (n === null) {
                this.handleInputNum(n)
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'reason',
    headerText: i18n.t('打分原因'),
    template: function () {
      return {
        template: Vue.component('reason', {
          template: `<div>
          <mt-tooltip :content="data.reason" target="#box" position="BottomCenter">
            <mt-input ref="input" width="120" height="26" id="box"
            @change="handleInputNum" v-model="data.reason" :maxLength="200" v-if="data.level != 0" :disabled="enable"></mt-input>
          </mt-tooltip>
          </div>

       `,
          data() {
            return {
              data: {},
              i18n: i18n
            }
          },
          mounted() {},
          methods: {
            handleInputNum(e) {
              if (!reviewTemplateData.length) return
              if (Object.hasOwnProperty.call(this.data, 'parentItem')) {
                reviewTemplateData
                  .find((e) => e.itemCode === this.data.parentItem.itemCode)
                  .itemResponses.find((x) => x.itemCode === this.data.itemCode).reason = e
              } else {
                reviewTemplateData.find((e) => e.itemCode === this.data.itemCode).reason = e
              }
              console.log('=reviewTemplateData=', reviewTemplateData)
            }
          },
          computed: {
            enable() {
              // RESULT_WAIT(10,"结果待提交"),//
              // RESULT_REJECT(20,"已提交"),//23
              return this.data.level == 0 || this.data.statusCode != 10
            }
          }
        })
      }
    }
  },
  {
    field: 'useSetStatus',
    headerText: i18n.t('不适用'),
    template: function () {
      return {
        template: Vue.component('useSetStatus', {
          template: `<div><mt-switch
          v-model="data.useSetStatus"
          :active-value="1"
          :inactive-value = "0"
          @change="handleInputNum"
          :disabled="enable"
          v-if="data.level != 0"
        ></mt-switch></div>
       `,
          data() {
            return {
              data: {},
              i18n: i18n
            }
          },
          mounted() {
            console.log('==this.data==', this.data)
            // this.symbol = symbolMap[this.data.qualifiedSymbol];
          },
          methods: {
            handleInputNum(e) {
              if (!reviewTemplateData.length) return
              if (Object.hasOwnProperty.call(this.data, 'parentItem')) {
                reviewTemplateData
                  .find((e) => e.itemCode === this.data.parentItem.itemCode)
                  .itemResponses.find((x) => x.itemCode === this.data.itemCode).useSetStatus = e
              } else {
                reviewTemplateData.find((e) => e.itemCode === this.data.itemCode).useSetStatus = e
              }
            }
          },
          computed: {
            enable() {
              // RESULT_WAIT(50,"结果待提交"),//20
              // RESULT_REJECT(70,"结果已驳回"),//23
              // RESULT_WAIT(10,"结果待提交"),//
              // RESULT_REJECT(20,"已提交"),//23
              return this.data.level == 0 || this.data.statusCode != 10
            }
          }
        })
      }
    }
  },
  {
    field: 'scoreUserName',
    headerText: i18n.t('打分人')
  },
  {
    field: 'scoreTime',
    headerText: i18n.t('打分时间'),
    template: () => {
      return {
        template: Vue.component('scoreTime', {
          template: `<div>
              <span>{{data.scoreTime? utils.formateTime(Number(data.scoreTime), "yyyy-MM-dd hh:mm:ss"):""}}</span>
            </div>`,
          data: function () {
            return {
              utils
            }
          }
        })
      }
    }
  }
]

// const dataSource = [
//   {
//     a: 1,
//     b: 2,
//     c: 3,
//     d: 4,
//     e: 5,
//     f: 6,
//     g: 7,
//     h: 8,
//     i: 9,
//     j: 10,
//     k: 11,
//     childrens: [
//       { a: 1, b: 2, c: 3, d: 4, e: 5, f: 6, g: 7, h: 8, i: 9, j: 10, k: 11 },
//     ],
//   },
// ];
// export const pageConfig = [
//   {
//     useToolTemplate: false,
//     treeGrid: {
//       height: 400,
//       allowPaging: true,
//       columnData,
//       childMapping: "childrens",
//       autoCheckHierarchy: true,
//       // dataSource: [],
//       dataSource: dataSource,
//     },
//   },
// ];
