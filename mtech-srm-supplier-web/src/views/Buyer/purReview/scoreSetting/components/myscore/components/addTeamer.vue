data
<template>
  <mt-dialog
    ref="dialog"
    :enable-resize="false"
    css-class="right-wrapper"
    :buttons="buttons"
    :header="header"
    height="600px"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" class="form-box" :model="formData" :rules="rules">
        <mt-form-item :label="$t('成员类型')">
          <mt-radio
            :data-source="memberTypeList"
            v-model="formData.userType"
            @change="typeChange"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item :label="$t('姓名')" prop="expertName">
          <mt-select
            :data-source="userList"
            :allow-filtering="true"
            :fields="userFields"
            v-model="formData.expertName"
            @change="userHandle"
            :placeholder="$t('请选择员工')"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('姓名')" prop="userName" v-if="formData.userType == 2">
          <mt-select
            v-model="formData.userName"
            :fields="{ text: 'name', value: 'uid' }"
            :data-source="anodrslist"
            filter-bar-:placeholder="$t('Search')"
            :allow-filtering="true"
            :filtering="fuzzyNamechange"
            :placeholder="$t('请选择员工')"
            @select="userHandle"
          ></mt-select>
        </mt-form-item> -->
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import MtRadio from '@mtech-ui/radio'

export default {
  components: { MtRadio },
  data() {
    return {
      // 表单验证
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      memberTypeList: [
        { value: '1', label: this.$t('专家') }
        // { value: "2", label: this.$t("公司员工") },
      ],
      fieldsTemplate: {
        dataSource: [],
        value: 'departmentCode',
        text: 'name',
        child: 'children',
        code: 'departmentCode',
        key: 1
      },
      userList: [],
      userFields: { value: 'id', text: 'expertName' },

      formData: {
        userType: '1'
      },
      userObj: {},
      deptObj: {},
      rules: {
        userName: [{ required: true, message: this.$t('请选择用户'), trigger: 'blur' }]
      },

      anodrslist: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getDepartMentList()
    this.getSpecialist()
    this.fuzzyNamechange({ text: 'a' })

    this.fuzzyNamechange = utils.debounce(this.fuzzyNamechange, 300)
    // this.filteringSupplier = utils.debounce(this.filteringSupplier, 500)
  },
  async created() {
    this.formData.userType = String(this.modalData.data[0])
  },
  watch: {},
  methods: {
    fuzzyNamechange(val) {
      let params = {
        fuzzyName: val.text,
        orgLevelCode: 'ORG05',
        orgType: 'ORG001ADM'
      }
      this.$API.supplierIndex.currentTenantEmployees(params).then((res) => {
        this.anodrslist = []
        if (res.code == 200 && res.data != null) {
          this.anodrslist = res.data.map((item) => {
            item.name = item.employeeName + item.phoneNum
            return item
          })
        }
      })
    },
    randomString(e) {
      e = e || 32
      var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    typeChange(val) {
      this.$refs.dialogRef.clearValidate()
      if (val == 1) {
        this.getSpecialist()
      } else if (val == 2) {
        if (this.anodrslist.length == 0) {
          // 切换成员类型为公司员工-获取姓名（员工）下拉数据（初始没参的时候默认传个"a"（和后端定好的）,列出默认的前20条）
          this.fuzzyNamechange({ text: 'a' })
        }
      }
      this.deptObj = {}
      this.userObj = {}
      this.userList = []
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((value) => {
        if (value) {
          let obj = {
            userType: this.formData.userType,
            userId: this.userObj.userId,
            expertName: this.userObj.expertName,
            email: this.userObj.email,
            mobilePhone: this.userObj.mobilePhone
          }
          if (this.formData.userType == 2) {
            obj.deptCode = this.deptObj.deptCode
            obj.deptName = this.deptObj.deptCode
          }
          this.$emit('confirm-function', obj)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 根据公司查询部门
    async getDepartMentList() {
      const param = { organizationId: this.modalData.buyerOrgId }
      // const param = { organizationId: "1475797553271898114" };
      const res = await this.$API.supplierReviewTask.getMasterDepartMent(param)
      if (res) {
        this.fieldsTemplate.dataSource = res?.data || []
        this.fieldsTemplate.key = this.randomString()
      }
    },
    // 查询专家
    async getSpecialist() {
      // const param = {
      //   page: { current: 1, size: 1000 },
      //   condition: 'and',
      //   rules: [
      //     {
      //       label: this.$t('专家姓名'),
      //       field: 'expertName',
      //       type: 'string',
      //       operator: 'contains',
      //       value: ''
      //     }
      //   ]
      // }
      let res = []
      console.log('this.modalData.supplyType', this.modalData)
      if (this.modalData.selectedRow[0].supplyType === 0) {
        res = await this.$API.supplierReviewTask.getSpecialist()
      } else if (this.modalData.selectedRow[0].supplyType === 1) {
        res = await this.$API.supplierReviewTask.getSpecialist_ordinarySpplyType()
      }
      if (res) {
        this.userFields = { value: 'userId', text: 'expertName' }
        this.userList = res?.data || []
      }
    },
    // 专家模糊搜索
    // filteringSupplier(e) {
    //   let params = {
    //     fuzzyNameOrCode: e.text
    //   }
    //   this.$API.supplierReviewTask.getSpecialist_ordinarySpplyType(params).then((res) => {
    //     let _data = cloneDeep(res.data)
    //     this.userFields = { value: 'userId', text: 'expertName' }
    //     this.userList = _data
    //   })
    // },
    // 选择人
    userHandle({ itemData }) {
      console.log('itemDataitemData', itemData)
      if (!itemData) return
      if (this.formData.userType == 1) {
        const { expertName, mobilePhone, userId, email } = itemData
        const e = this.userObj
        e.mobilePhone = mobilePhone
        e.expertName = expertName
        e.userId = userId
        e.email = email
      } else {
        const { uid, employeeName, email, phoneNum, departmentOrgCode, departmentOrgName } =
          itemData
        const e = this.userObj
        e.phone = phoneNum
        e.userName = employeeName
        e.userId = uid
        e.email = email
        this.deptObj.deptCode = departmentOrgCode
        this.deptObj.deptName = departmentOrgName
      }
    },
    fn(arr, id, obj) {
      arr.forEach((item) => {
        if (item.departmentCode == id) {
          obj.deptCode = item.departmentCode
          obj.deptName = item.name
          obj.id = item.id
        } else if (item.children && item.children.length > 0) {
          this.fn(item.children, id, obj)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
// :disabled="modalData.data.length == 1"
.dialog-content {
  padding-top: 20px;
}
.responsible {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-box {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.mt-form-item {
  width: 90%;
}
</style>
