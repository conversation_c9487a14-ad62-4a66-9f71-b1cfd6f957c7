<template>
  <!-- 我的评分 -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      delBuyerAssessRequest: {
        ids: []
      },
      //列表调用
      pageConfig: pageConfig(this.$API.supplierReviewTask.getScoreList, this)
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field === 'code') {
        this.$router.push({
          path: '/supplier/pur/myscore-detail',
          query: {
            code: e.data.code,
            id: e.data.id
          }
        })
      }
      const { field, data } = e
      let params = data.buyerPartnerRelationLinkResponse
      if (field === 'supplierInternalCode' && data) {
        this.$router.push({
          path: 'profileDetail',
          query: {
            partnerArchiveId: params.partnerArchiveId,
            orgId: params.orgId,
            supplierEnterpriseId: params.supplierEnterpriseId,
            partnerRelationCode: params.partnerRelationCode
          }
        })
      }
    },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id == 'Submit') {
        this.submitScoreBatch(_selectGridRecords)
      } else if (e.toolbar.id == 'Turn') {
        this.turnScoreBatch(_selectGridRecords)
      } else {
        // 导出
        this.handleExport('exportMyScore')
      }
    },
    turnScoreBatch(arr) {
      if (arr.length == 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (arr.length > 1) {
        this.$toast({ content: this.$t('只允许对一条数据进行操作'), type: 'warning' })
        return
      }
      let bol = arr.some((item) => {
        return item.buyerOrgId != arr[0].buyerOrgId
      })
      if (bol) {
        this.$toast({ content: this.$t('请选择相同公司下的数据'), type: 'warning' })
        return
      }
      let params = arr.map((item) => {
        return item.code
      })
      this.$API.reviewPlan.taskuserType(params).then((res) => {
        this.$dialog({
          modal: () => import('./components/addTeamer.vue'),
          data: {
            title: this.$t('选择团队成员'),
            buyerOrgId: arr[0].buyerOrgId,
            data: res.data,
            selectedRow: arr
          },
          success: (e) => {
            let params = {
              codes: arr.map((item) => item.code),
              addReviewTaskUserRequest: e
            }
            if (e.userType == 2) {
              params.orgCode = arr[0].buyerOrgCode
              params.orgName = arr[0].buyerOrgName
            }
            this.$API.reviewPlan.taskTurn(params).then(() => {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      })
    },
    //批量提交评分操作
    submitScoreBatch(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.code)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交所选数据？')
        },
        success: () => {
          this.$API.reviewPlan.submitScoreBatch(_selectIds).then(() => {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
</style>
