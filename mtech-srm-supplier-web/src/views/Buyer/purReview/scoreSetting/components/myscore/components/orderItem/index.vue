<template>
  <div class="oreder-warp">
    <div class="treeView">
      <ul class="left-nav">
        <li
          :class="{ active: index_ == index }"
          v-for="(item, index) in reviewContent"
          :key="index"
          @click="tabClick(index)"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
    <div class="table">
      <div v-if="!isShow">
        <vxe-toolbar>
          <template #buttons>
            <vxe-button v-if="info.status !== 20" @click="handeleScoreBatch">{{
              $t('批量打分')
            }}</vxe-button>
            <vxe-button @click="importExcel">{{ $t('导入') }}</vxe-button>
            <vxe-button @click="exportExcel">{{ $t('导出') }}</vxe-button>
          </template>
        </vxe-toolbar>
        <ScTable
          ref="xTable"
          :columns="columns"
          :table-data="tableData"
          :tree-config="treeConfig"
          height="560"
          :row-config="{ height: 120 }"
          :checkbox-config="checkedConfig"
          @checkbox-all="selectAllEvent"
          @checkbox-change="selectChangeEvent"
        />
        <div style="margin-top: -20px">
          <span>{{ $t('已选择') }} {{ quantity }} {{ $t('项') }} </span>
        </div>
      </div>
      <div v-else>
        <div style="padding-bottom: 20px; display: flex">
          <div style="margin-right: 20px">
            <span>{{ $t('评审模板') }}：</span>
            <span style="color: #2783fe; cursor: pointer" @click="fileDownload">{{
              $t('附件下载')
            }}</span>
          </div>
          <div>
            <span>{{ $t('审查结果') }}：</span>
            <span style="color: #2783fe; cursor: pointer" @click="fileUpload">{{
              $t('附件上传')
            }}</span>
          </div>
        </div>
        <sc-table
          ref="oTable"
          :columns="otherColumns"
          :table-data="tableData"
          height="600"
          :row-config="{ height: 120 }"
          keep-source
          show-overflow
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item)"
            >
              {{ item.name }}
            </vxe-button>
          </template>
          <template #weightDefault="{ row }">
            <vxe-input
              v-model="row.weight"
              type="number"
              :min="1"
              :max="100"
              :placeholder="$t('请输入权重')"
              clearable
              @change="weightChange(row)"
            />
          </template>
        </sc-table>
      </div>
      <mt-dialog ref="dialog" width="400" height="400" :buttons="buttons" :header="$t('打分')">
        <mt-form ref="dialogRef" :model="formObject" :rules="rules">
          <mt-form-item prop="score" :label="$t('打分')">
            <mt-input-number
              v-model="formObject.score"
              :show-clear-button="true"
              min="0"
              max="99999"
              :placeholder="$t('请输入分数')"
              :max-length="50"
            ></mt-input-number>
          </mt-form-item>
          <mt-form-item prop="reason" :label="$t('打分原因')">
            <mt-input
              :multiline="true"
              :rows="3"
              :max-length="200"
              type="text"
              :placeholder="$t('请输入文本')"
              v-model="formObject.reason"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </mt-dialog>
    </div>
    <input
      type="file"
      v-if="!showUpload"
      class="upload-input"
      @change="chooseFiles"
      ref="files"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    />
    <mt-common-uploader
      ref="uploader"
      class="common-uploader"
      :save-url="saveUrl"
      :is-single-file="false"
      type="line"
      v-model="curRow.scoringBasisFileList"
    ></mt-common-uploader>
  </div>
</template>
<script>
import { reviewTemplateData } from '../orderItem/config/index'
import ScTable from '@/components/ScTable/src/index'
import axios from 'axios'
import utils from '@/utils/utils'
// import { number } from 'echarts'
export default {
  components: {
    ScTable
  },
  props: {
    orderData: {
      type: Array,
      default: () => {
        return []
      }
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      quantity: 0,
      index_: 0,
      reviewContent: [],
      checkedConfig: {
        checkField: 'checked'
      },
      treeConfig: {
        transform: true,
        rowField: 'itemCode',
        parentField: 'parentCode',
        expandAll: true
      },
      columns: [
        { type: 'checkbox', width: 100, treeNode: true },
        {
          field: 'itemName',
          title: this.$t('评审项'),
          width: '540'
        },
        {
          field: 'range',
          title: this.$t('分值范围'),
          width: 100
        },
        {
          field: 'weight',
          title: this.$t('权重(%)'),
          width: 100
        },
        {
          field: 'selfScore',
          title: this.$t('自查得分'),
          width: 100
        },
        {
          field: 'supportFile',
          title: this.$t('支持文件'),
          width: 100
        },
        {
          field: 'fileSupportSituation',
          title: this.$t('文件支撑情况'),
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.fileSupportSituation}
                  disabled={!row.parentCode}
                  type='text'
                  clearable
                  onChange={() => {
                    this.changeScore(row)
                  }}></vxe-input>
              ]
            }
          }
        },
        {
          field: 'sampleInspectInfo',
          title: this.$t('抽查信息'),
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.sampleInspectInfo}
                  disabled={!row.parentCode}
                  type='text'
                  clearable
                  onChange={() => {
                    this.changeScore(row)
                  }}></vxe-input>
              ]
            }
          }
        },
        {
          field: 'executionMatchDegree',
          title: this.$t('执行吻合度'),
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.executionMatchDegree}
                  disabled={!row.parentCode}
                  type='text'
                  clearable
                  onChange={() => {
                    this.changeScore(row)
                  }}></vxe-input>
              ]
            }
          }
        },
        {
          field: 'scoringBasisFileList',
          title: this.$t('打分依据'),
          width: 200,
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <input
                    ref='file'
                    type='file'
                    style='display: none'
                    onClick={() => {
                      this.chooseRowsFiles(row)
                    }}
                    multiple='multiple'
                    accept='.xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,'
                  />
                  <vxe-button
                    v-show={row.parentCode}
                    class='mgn-left-10'
                    size='mini'
                    onClick={() => {
                      this.handleUpload(row)
                    }}>
                    {this.$t('上传文件')}
                  </vxe-button>
                  {row.scoringBasisFileList?.map((e) => {
                    return (
                      <div>
                        <p>
                          <a
                            onClick={() => {
                              this.preview(e)
                            }}>
                            {e.fileName}
                          </a>
                          <span
                            style='margin-left:10px;cursor: pointer;'
                            onClick={() => {
                              this.handleDownload(e)
                            }}>
                            {this.$t('下载')}
                          </span>
                          <span
                            style='margin-left:10px;cursor: pointer;'
                            onClick={() => {
                              this.handleDelete(e, row)
                            }}>
                            {this.$t('删除')}
                          </span>
                        </p>
                      </div>
                    )
                  })}
                </div>
              ]
            }
          }
        },
        // {
        //   field: 'selfReason',
        //   title: this.$t('自查原因'),
        //   width: 200
        // },
        // {
        //   field: 'unqualified', //symbol  score
        //   title: this.$t('合格线'),
        //   width: 120
        // },
        {
          field: 'actScore',
          title: this.$t('得分'),
          width: 150,
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.actScore}
                  disabled={!row.parentCode}
                  placeholder={row.range}
                  type='integer'
                  min={row.scoreBegin}
                  max={row.scoreEnd}
                  clearable
                  onChange={() => {
                    this.changeScore(row)
                  }}></vxe-input>
              ]
            }
          }
        },
        {
          field: 'reason',
          title: this.$t('打分原因'),
          type: 'input',
          width: 200,
          slots: {
            default({ row }) {
              return [<vxe-input v-model={row.reason} disabled={!row.parentCode}></vxe-input>]
            }
          }
        },
        {
          field: 'scoreUserName',
          title: this.$t('打分人'),
          width: 160
        },
        {
          field: 'scoreTime',
          title: this.$t('打分时间'),
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <span>
                  {row.scoreTime
                    ? utils.formateTime(Number(row.scoreTime), 'yyyy-MM-dd hh:mm:ss')
                    : ''}
                </span>
              ]
            }
          }
        },
        {
          field: 'useSetStatus',
          title: this.$t('是否适用'),
          slots: {
            default: ({ row }) => {
              return [
                <vxe-select
                  v-show={row.parentCode}
                  v-model={row.useSetStatus}
                  options={this.useSetStatusOptions}
                  transfer
                />
              ]
            }
          }
        }
      ],
      tableData: [],
      // 表单验证
      formObject: {
        score: '',
        reason: '',
        useSetStatus: ''
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      selectGridRecords: [],
      selectGrids: [],
      showUpload: false,
      curRow: {
        scoringBasisFileList: []
      }, // 当前行
      saveUrl: '/api/file/user/file/uploadPublic?useType=1', // 文件上传路径待
      downloadUrl: '/api/file/user/file/downloadPublicFile', //文件下载

      isShow: false,
      useSetStatusOptions: [
        { label: this.$t('是'), value: 0 },
        { label: this.$t('否'), value: 1 }
      ],
      templateId: null,

      odminCategoryList: [], // ODMIN品类
      odminReviewItemList: [] // ODMIN评审项
    }
  },
  computed: {
    rules() {
      return {
        score: [
          {
            required: this.formObject.socre == null,
            message: this.$t('请填写分数'),
            trigger: 'blur'
          }
        ]
      }
    },
    toolbar() {
      let data = []
      if (this.info.status !== 20) {
        data = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return data
    },
    otherColumns() {
      return [
        { type: 'checkbox', width: 50 },
        {
          field: 'itemName',
          title: this.$t('维度'),
          slots: {
            default: ({ row }) => {
              return this.odminCategoryList.some((v) => v.itemCode === this.info.categoryCode)
                ? [
                    <vxe-select
                      v-model={row.itemName}
                      options={this.odminReviewItemList}
                      option-props={{ label: 'itemName', value: 'itemName' }}
                      clearable
                      transfer
                    />
                  ]
                : [<vxe-input v-model={row.itemName} type='text' clearable />]
            }
          }
        },
        {
          field: 'weight',
          title: this.$t('权重'),
          slots: {
            // default: 'weightDefault'
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.weight}
                  type='number'
                  min={1}
                  max={100}
                  clearable
                  onChange={() => this.weightChange(row)}
                />
              ]
            }
          }
        },
        {
          field: 'fullScore',
          title: this.$t('满分')
        },
        {
          field: 'score',
          title: this.$t('及格分'),
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.score}
                  type='number'
                  min={0}
                  max={row.fullScore}
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'actScore',
          title: this.$t('现场得分'),
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.actScore}
                  type='number'
                  min={0}
                  max={row.fullScore}
                  clearable
                />
              ]
            }
          }
        }
      ]
    }
  },
  watch: {
    'curRow.scoringBasisFileList.length'() {
      this.curRow.scoringBasisFileList.forEach((item) => {
        item.fileId = item.id
        item.fileUrl = item.url
      })
    }
  },
  created() {
    this.getOdminCategoryList()
    this.getOdminReviewItemList()
    this.orderData.forEach((item) => {
      let obj = {
        id: '',
        name: ''
      }
      obj.id = item.templateCode
      obj.name = item.templateName
      this.reviewContent.push(obj)
    })
    this.handleData()
  },
  methods: {
    getOdminCategoryList() {
      this.$API.assessManage
        .getDictCode({
          dictCode: 'ODMIN_CATEGORY'
        })
        .then((res) => {
          if (res.code === 200) {
            this.odminCategoryList = res.data
          }
        })
    },
    getOdminReviewItemList() {
      this.$API.assessManage
        .getDictCode({
          dictCode: 'ODMIN_REVIEW_ITEM'
        })
        .then((res) => {
          if (res.code === 200) {
            this.odminReviewItemList = res.data
          }
        })
    },
    weightChange(row) {
      row.fullScore = row.weight
      if (row.score > row.fullScore) {
        row.score = 0
      }
      if (row.actScore > row.fullScore) {
        row.actScore = 0
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.$refs.oTable.$refs.xGrid.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.deleteRows(ids)
          break
        default:
          break
      }
    },
    handleAdd() {
      const currentViewRecords = this.$refs.oTable.$refs.xGrid.getTableData().visibleData
      let weight = 100
      currentViewRecords.forEach((i) => {
        weight -= i.weight
      })
      if (weight <= 0) {
        this.$toast({
          content: this.$t('权重之和不能大于100'),
          type: 'warning'
        })
        return
      }
      const item = {
        itemName: null,
        fullScore: weight,
        symbol: '3',
        score: 0,
        actScore: 0,
        weight: weight,
        useSetStatus: 0
      }
      this.$refs.oTable.$refs.xGrid.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.$refs.oTable.$refs.xGrid.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.$refs.oTable.$refs.xGrid.setEditRow(currentViewRecords[0])
      })
    },
    deleteRows(ids) {
      ids.forEach((id) => {
        this.tableData = this.tableData.filter((item) => item.id !== id)
      })
    },
    fileDownload() {
      let fileList = this.orderData[this.index_]?.fileInfoResponseList || []
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('附件下载'),
          fileList,
          type: 'download'
        }
      })
    },
    fileUpload() {
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          type: 'upload',
          id: this.templateId,
          taskCode: this.$route.query.code
        }
      })
    },
    handleDelete(item, curRow) {
      const index = curRow.scoringBasisFileList.findIndex((file) => file.fileId === item.fileId)
      curRow.scoringBasisFileList.splice(index, 1)
    },
    // 附件下载功能
    handleDownload(item) {
      this.$API.fileService
        .downloadPublicFile({
          id: item.fileId
        })
        .then((res) => {
          utils.download({
            fileName: item.fileName,
            blob: res.data
          })
        })
    },
    // 附件打开预览功能
    preview(item) {
      const params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    handleUpload(curRow) {
      // this.$dialog({
      //   modal: () => import('../coms/upload.vue'),
      //   data: {
      //     title: this.$t('附件上传')
      //   },
      //   success: (res) => {
      //     if (curRow.scoringBasisFileList != null) {
      //       curRow.scoringBasisFileList.push(res)
      //     } else {
      //       curRow.scoringBasisFileList = [res]
      //     }
      //   }
      // })
      if (!curRow.scoringBasisFileList) curRow.scoringBasisFileList = []
      this.curRow = curRow
      this.$refs.uploader.$children[1].showFileBaseInfo()
    },
    changeScore(e) {
      this.tableData.forEach((item) => {
        if (e.parentCode === item.itemCode) {
          let nums = 0
          item.children.forEach((item) => {
            if (item.actScore == null) {
              nums += 0
            } else {
              nums += parseInt(item.actScore)
            }
          })
          item.actScore = nums
        }
      })
    },
    selectAllEvent(e) {
      this.selectGridRecords = e.records
      this.quantity = e.records.length
      this.$forceUpdate()
    },
    selectChangeEvent(e) {
      this.selectGridRecords = e.records
      this.quantity = e.records.length
      this.$forceUpdate()
    },
    flatTableData(list) {
      let arr = []
      list.forEach((item) => {
        arr = arr.concat(item.itemResponses || [])
      })
      const res = arr.concat(list)
      res.forEach((item) => {
        item.checked = false
        item.unqualified = item.symbol + ' ' + item.score
        item.range = item.scoreEnd ? `${item.scoreBegin || 0}-${item.scoreEnd}` : ''
      })
      return res
    },
    handleData() {
      let item = this.orderData[this.index_]
      this.isShow = [
        'quality-qpa',
        'RD-rd1',
        'fc-zrvn',
        'odmin-zh',
        'gf-gfzl',
        'gf-gfsw',
        'gf-gfjs',
        'gf-gfzlyf',
        'tx-txyf',
        'tx-txsw',
        'tx-txzl'
      ].includes(item.templateTypeId)
      this.templateId = this.isShow ? item.id : ''
      this.tableData = this.isShow
        ? this.orderData[this.index_]?.itemResponses
        : this.flatTableData(this.orderData[this.index_]?.itemResponses || [])
    },
    // 左侧tab切换
    tabClick(index) {
      this.setData()
      this.index_ = index
      let item = this.orderData[this.index_]
      this.isShow = [
        'quality-qpa',
        'RD-rd1',
        'fc-zrvn',
        'odmin-zh',
        'gf-gfzl',
        'gf-gfsw',
        'gf-gfjs',
        'gf-gfzlyf',
        'tx-txyf',
        'tx-txsw',
        'tx-txzl'
      ].includes(item.templateTypeId)
      console.log('tabClick', this.orderData[this.index_])
      this.templateId = this.isShow ? item.id : ''
      this.tableData = this.isShow
        ? this.orderData[this.index_]?.itemResponses
        : this.flatTableData(this.orderData[this.index_]?.itemResponses || [])
      this.$nextTick(() => {
        !this.isShow && this.$refs.xTable.setAllTreeExpand(true)
      })
    },
    setData() {
      if (this.isShow) {
        this.orderData[this.index_].itemResponses =
          this.$refs.oTable.$refs.xGrid.getTableData().visibleData
      }
    },
    // 批量打分
    handeleScoreBatch() {
      if (this.selectGridRecords.length === 0) {
        this.$toast({
          content: this.$t('请至少选择一条数据')
        })
        return
      }
      this.selectGrids = []
      this.formObject = {
        score: '',
        reason: '',
        useSetStatus: ''
      }
      // 调用打分
      this.$refs['dialog'].ejsRef.show()
      let bolsone = true
      this.selectGridRecords.forEach((item) => {
        this.selectGrids.push(item.itemCode)
        if (item.itemResponses && item.itemResponses.length > 0) {
          item.itemResponses.forEach((e) => {
            if (e.useSetStatus != 1) {
              bolsone = false
            }
          })
        }
      })
      if (bolsone == false) {
        this.formObject.useSetStatus = '0'
      } else {
        this.formObject.useSetStatus = '1'
      }
    },
    // 确认按钮
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          // 确认
          // this.throwTree(this.selectGridRecords, this.pageConfig[0].treeGrid.dataSource, true)
          this.throwTree(this.selectGrids, this.tableData, true)
          reviewTemplateData.length = 0
          this.tableData.forEach((e) => {
            reviewTemplateData.push(e)
          })
          this.$refs['dialog'].ejsRef.hide()
          this.formObject = {
            score: '',
            reason: ''
          }
        }
      })
    },
    throwTree(arr, sourceData, boolean) {
      sourceData.forEach((item) => {
        if (arr.indexOf(item.itemCode) != -1) {
          if (this.formObject.score >= item.scoreBegin && this.formObject.score <= item.scoreEnd) {
            item.actScore = this.formObject.score
          } else if (this.formObject.score < item.scoreBegin) {
            item.actScore = item.scoreBegin
          } else if (this.formObject.score > item.scoreEnd) {
            item.actScore = item.scoreEnd
          }

          item.reason = this.formObject.reason
          // item.useSetStatus = Number(this.formObject.useSetStatus)
        }

        if (boolean) {
          reviewTemplateData.push(item) //别问为啥这么改   为了干掉confirm里面的那个遍历 页面卡死
        }
        if (item.itemResponses && item.itemResponses.length > 0) {
          this.throwTree(arr, item.itemResponses, false)
        }
      })
    },
    cancel() {
      this.$refs['dialog'].ejsRef.hide()
    },
    importExcel() {
      this.$refs.files.click()
    },
    //选择文件
    chooseFiles(data) {
      let { files } = data.target
      this.showUpload = true
      var reg = /^.*\.(?:xls|xlsx)$/i
      if (!reg.test(files[0].name)) {
        this.$toast({
          content: this.$t('文件格式不正确.xls.xlsx'),
          type: 'warning'
        })
        return
      }
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        this.headerFlag = false
        // 您未选择需要上传的文件
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('multipartFile', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$loading()
      this.uploadFile(_data)
    },
    async uploadFile(data) {
      this.$refs.files.value = ''
      try {
        const res = await this.$API.supplierReviewTask.importExcel(data)
        this.$hloading()
        if (res.code == 200) {
          this.$toast({
            content: this.$t('导入成功'),
            type: 'success'
          })
          this.$parent.getDetailData()
          this.showUpload = false
        }
      } catch (error) {
        this.showUpload = false
      }
    },
    async exportExcel() {
      const response = this.orderData[this.index_]
      const { templateCode, templateName, templateTypeName } = response
      axios
        .post(
          `/api/supplier/tenant/buyer/review/task/export`,
          {
            code: this.$route.query.code,
            templateCode,
            templateName,
            templateTypeName,
            name: this.info.name
          },
          { responseType: 'blob' }
        )
        .then((res) => {
          const nowTimes = new Date().getTime()
          const filename = `${this.$route.query.code}-${nowTimes}.xlsx`
          const url = window.URL.createObjectURL(new Blob([res.data]))
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.download = filename
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href) // 释放URL 对象
          document.body.removeChild(link)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.oreder-warp {
  display: flex;
  .treeView {
    width: 180px;
    margin-right: 20px;
  }
  .table {
    width: calc(100% - 180px);
    flex: 1;
  }
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: rgba(0, 0, 0, 0.87);
}
::v-deep .e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #fff;
  border-color: #fff;
  border-bottom: 2px solid #00469c;
}
::v-deep .mt-tree-view .e-treeview .e-fullrow {
  opacity: 1;
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: #00469c;
}

.left-nav {
  line-height: 40px;
  text-align: center;
  li {
    cursor: pointer;
  }
  .active {
    background: linear-gradient(135deg, rgba(245, 84, 72, 1) 0%, rgba(255, 128, 118, 1) 100%);
    color: #fff;
  }
}
.upload-input {
  display: none;
}
.common-uploader {
  display: none;
}
</style>
