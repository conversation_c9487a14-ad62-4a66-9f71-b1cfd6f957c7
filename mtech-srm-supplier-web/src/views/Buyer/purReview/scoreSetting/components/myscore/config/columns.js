import { i18n } from '@/main.js'
import Vue from 'vue'
const toolbar = {
  useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
  // tools: [
  //   [{ id: 'dafen', icon: 'icon_solid_edit', title: i18n.t('批量打分') }],
  //   ['Filter', 'Refresh', 'Setting']
  // ],
  tools: [
    [{ id: 'dafen', icon: 'icon_solid_edit', title: i18n.t('应用自查得分') }],
    ['Filter', 'Refresh', 'Setting']
  ]
}

const columnData = [
  {
    width: '50',
    type: 'checkbox'
    // showCheckbox:true
  },
  // { field: "name", headerText: "组" },

  { field: 'supplierEnterpriseCode', headerText: i18n.t('评审项'), width: '400' },
  { field: 'supplierEnterpriseName', headerText: i18n.t('分值范围') },
  // { field: "dimensionName", headerText: "维度" },
  // { field: "name", headerText: "指标类" },
  { field: 'categoryName', headerText: i18n.t('权重') },
  { field: 'indexName', headerText: i18n.t('自查得分') }, //todo 缺少字段
  {
    field: 'indexType',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: {
        '': i18n.t('维度'),
        1: i18n.t('指标类'),
        2: i18n.t('指标')
      }
    }
  }, //todo 缺少字段
  { field: 'indexDescribe', headerText: i18n.t('描述') },
  { field: 'allocateScore', headerText: i18n.t('分配分值') },
  {
    width: '200',
    field: 'score',
    headerText: i18n.t('得分'),
    template: () => {
      return {
        template: Vue.component('score', {
          template: `<div style='width:150px'>
          <mt-input v-if='data.indexType==2' v-model.number='data.score' type='number' @blur='input' />
          <span v-else ></span>
        </div>`,
          data: function () {},
          methods: {
            input(e) {
              if (e) {
                let num = e * 1
                this.data.score = num.toFixed(this.data.keepDecimal || 1)
                this.$parent.$emit('input', this.data.i, 'score', this.data.score)
              }
              // this.$parent.$emit('handleClickCellTitle', { field: 'scoringDetails', data: this.data })
            }
          },
          computed: {}
        })
      }
    }
  },
  {
    width: '240',
    field: 'reason',
    headerText: i18n.t('原因'),
    template: () => {
      return {
        template: Vue.component('reason', {
          template: `<div>
          <mt-input v-if='data.indexType==2' v-model.number='data.reason'  @blur='input' />
          <span v-else ></span>
        </div>`,
          data: function () {},
          methods: {
            input(e) {
              if (e) {
                this.$parent.$emit('input', this.data.i, 'reason', e)
              }
            }
          },
          computed: {}
        })
      }
    }
  }
]
export const pageConfig = (data) => [
  {
    gridId: '65dd459e-f95e-407d-8d4c-bb3cb43758dd',
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar,
    // allowGrouping:true,
    grid: {
      allowPaging: false,
      allowGrouping: true,
      columnData,
      // asyncConfig: {
      //   url:url,
      // },
      dataSource: data
    }
  }
]
// 供应商共享
export const columnShare = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('档案编码'),
    width: '150'
  },
  {
    field: 'applyName',
    width: '120',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'relationDTOList',
    width: '300',
    headerText: i18n.t('计划策略')
  },
  {
    field: 'sourceOrgCode',
    width: '120',
    headerText: i18n.t('期号')
  },
  {
    field: 'sourceOrgName',
    width: '120',
    headerText: i18n.t('提交截止日期')
  },
  {
    field: 'destinationOrgCode',
    width: '150',
    headerText: i18n.t('完成进度')
  },
  {
    field: 'destinationOrgName',
    width: '150',
    headerText: i18n.t('公司')
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('评分人')
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('任务创建日期')
  },
  {
    field: 'applyerDeptName',
    width: '180',
    headerText: i18n.t('状态')
  }
]

//进货记录表
export const recodeColumn = [
  {
    field: 'remark',
    width: '100',
    headerText: i18n.t('批次')
  },
  {
    field: 'remark',
    width: '100',
    headerText: i18n.t('品类')
  },
  {
    field: 'remark',
    width: '100',
    headerText: i18n.t('供应商')
  },
  {
    field: 'remark',
    width: '100',
    headerText: i18n.t('检验结果')
  },
  {
    field: 'remark',
    width: '100',
    headerText: i18n.t('订单量')
  },
  {
    field: 'remark',
    width: '100',
    headerText: i18n.t('时间')
  },
  {
    field: 'remark',
    width: '150',
    headerText: i18n.t('设备有限时间')
  }
]
