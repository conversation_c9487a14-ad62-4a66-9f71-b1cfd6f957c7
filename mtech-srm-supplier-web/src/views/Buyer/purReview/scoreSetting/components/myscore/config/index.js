//维度设置Tab
import APIS from '@/apis/modules/supplierReviewTask.js'
import { i18n } from '@/main'
import Vue from 'vue'
import utils from '@/utils/utils.js'
const asyncResult = async (param) => {
  const res = await APIS.APIS.getMasterReviewType(param)
  return res.data
}
let taskType = []
// let taskStatus = [];
let taskResult = []
asyncResult({ dictCode: 'reviewTaskType' }).then((r) => {
  taskType.length = 0
  taskType = r
})
// asyncResult({ dictCode: "reviewTaskStatus" }).then((r) => {
//   taskStatus.length = 0;
//   taskStatus = r;
// });

asyncResult({ dictCode: 'reviewTaskResultStatus' }).then((r) => {
  taskResult.length = 0
  taskResult = r
})
// const toolbar = [
// {
//   id: "Add",
//   icon: "icon_solid_Createorder ",
//   title: i18n.t("新增"),
//   // permission: ["O_02_0039"],
// },
// {
//   id: "Delete",
//   icon: "icon_solid_Delete ",
//   title: i18n.t("删除"),
//   // permission: ["O_02_0039"],
// },
// {
//   id: "Enable",
//   icon: "icon_table_disable ",
//   title: i18n.t("启用"),
//   // permission: ["O_02_0041"],
// },
// {
//   id: "Disable",
//   icon: "icon_table_enable ",
//   title: i18n.t("停用"),
//   // permission: ["O_02_0040"],
// },
// ];
const mapJson = (data) => {
  const json = {}
  data.map((item) => {
    json[Number(item.itemCode)] = item.itemName
  })
  return json
}
const columnData = () => {
  return [
    {
      type: 'checkbox',
      width: 50
    },
    // {
    //   field: "categoryCode",
    //   headerText: i18n.t("品类编码"),
    //   cssClass: "field-content",
    // },
    {
      field: 'code',
      headerText: i18n.t('评审任务编码'),
      width: '200',
      cssClass: 'field-content'
    },
    {
      field: 'name',
      headerText: i18n.t('名称'),
      cssClass: '',
      width: 260
    },
    {
      field: 'supplierInternalCode',
      headerText: i18n.t('供应商编号-SRM'),
      cssClass: 'field-content',
      width: 260,
      cellTools: []
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编号-SAP'),
      width: 260
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: 260,
      cssClass: ''
    },
    {
      field: 'status',
      headerText: i18n.t('状态'),
      cssClass: '',
      valueConverter: {
        type: 'map',
        map: {
          10: i18n.t('待提交'),
          20: i18n.t('已提交')
        }
      }
    },
    {
      field: 'supplierProvinceName',
      headerText: i18n.t('省'),
      cssClass: ''
    },
    {
      field: 'supplierCityName',
      headerText: i18n.t('市'),
      cssClass: ''
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类'),
      cssClass: ''
    },
    {
      field: 'buyerEnterpriseName',
      headerText: i18n.t('公司'),
      cssClass: ''
    },
    {
      field: 'bizType',
      headerText: i18n.t('评审类型'),
      valueConverter: {
        type: 'map',
        map: mapJson(taskType)
      }
    },
    {
      field: 'sourceCode',
      headerText: i18n.t('来源'),
      cssClass: '',
      width: 200
    },
    {
      field: 'planTimeBegin', //planTimeBegin - planTimeEnd
      headerText: i18n.t('计划开始时间'),
      template: () => {
        return {
          template: Vue.component('planTimeBegin', {
            template: `<div>
            <span>{{time}}</span>
          </div>`,
            data: function () {
              return {
                time: ''
              }
            },
            created() {
              this.time = this.timeUtils()
            },
            methods: {
              timeUtils() {
                let Time = ''
                if (this.data.planTimeBegin) {
                  Time = utils.formateTime(Number(this.data.planTimeBegin), 'yyyy-MM-dd')
                }
                return Time
                // utils.formateTime(
                //   Number(this.data.planTimeBegin),
                //   "yyyy-MM-dd"
                // )
                // utils.formateTime(Number(this.data.planTimeEnd), "yyyy-MM-dd")
              }
            }
          })
        }
      }
    },
    {
      field: 'planTimeEnd', //planTimeBegin - planTimeEnd
      headerText: i18n.t('计划结束时间'),
      template: () => {
        return {
          template: Vue.component('planTimeEnd', {
            template: `<div>
            <span>{{time}}</span>
          </div>`,
            data: function () {
              return {
                time: ''
              }
            },
            created() {
              this.time = this.timeUtils()
            },
            methods: {
              timeUtils() {
                let Time = ''
                if (this.data.planTimeEnd) {
                  Time = utils.formateTime(Number(this.data.planTimeEnd), 'yyyy-MM-dd')
                }
                return Time
              }
            }
          })
        }
      }
    },
    {
      field: 'reviewSchedule',
      headerText: i18n.t('评审进度'),
      cssClass: '',
      ignore: true // 单列设置ignore，不出现在筛选字段中。
    },
    {
      field: 'applyTime',
      headerText: i18n.t('提交时间'),
      template: () => {
        return {
          template: Vue.component('applyTime', {
            template: `<div>
                <span>{{data.applyTime?utils.formateTime(Number(data.applyTime), "yyyy-MM-dd hh:mm:ss"):""}}</span>
              </div>`,
            data: function () {
              return {
                utils
              }
            }
          })
        }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      cssClass: ''
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建时间')
    }
  ]
}
// 调用列表
export const pageConfig = (url, _this) => [
  {
    gridId: '2fc69053-731e-482f-be43-e4fc079017c8',
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          // {
          //   id: 'Submit',
          //   icon: 'icon_solid_Createorder ',
          //   title: i18n.t('提交打分')
          // },
          {
            id: 'Turn',
            icon: 'icon_solid_Createorder ',
            title: i18n.t('转办')
          },
          {
            id: 'export',
            title: i18n.t('导出')
          }
        ],
        ['Filter', 'refresh', 'setting']
      ]
    },
    grid: {
      // allowTextWrap: true,
      columnData: columnData(),
      asyncConfig: {
        url,
        // 时间转换
        serializeList: (list) => {
          list.forEach((e) => {
            e.createTime = Number(e.createTime)
            e.time = _this.$utils.formateTime(e.createTime, 'yyyy-MM-dd hh:mm:ss')
          })
          return list
        }
        // serializeList: (list) => {
        //   let arr = list.map((item) => {
        //     item.createTime = Number(item.createTime);
        //     return { ...item };
        //   });
        //   return arr;
        // },
      },
      frozenColumns: 3
    }
  }
]
