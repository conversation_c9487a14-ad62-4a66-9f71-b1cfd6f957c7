<!-- 品类分工报表 -->
<template>
  <!--       @clickOperation="clickOperation" -->
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @orgNamedialog="orgNamedialog"
      @handleClickToolBar="handleClickToolBar"
      @parentEditrowdata="parentEditrowdata"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    ></mt-template-page>
    <!-- 导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
import { columnData, toolbar } from './config/index'
import utils from '@/utils/utils'

export default {
  components: {
    UploadExcelDialog
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowEditing: true, //开启表格编辑操作
            columnData,
            commandClick: this.commandClick, //配置单击进入行编辑
            asyncConfig: {
              url: '/supplier/tenant/buyer/categoryDivision/list',
              serializeList: (list) => {
                list.forEach((item) => {
                  // if (item.startDate) {
                  //   item.startDate = Number(new Date(item.startDate))
                  // }
                  // if (item.endDate) {
                  //   item.endDate = Number(new Date(item.endDate))
                  // }
                  if (item.devUserName) {
                    item.devUserName = item.devUserExternal + '-' + item.devUserName
                  }
                  if (item.priceUserName) {
                    item.priceUserName = item.priceUserExternal + '-' + item.priceUserName
                  }
                  if (item.financeUserName) {
                    item.financeUserName = item.financeUserExternal + '-' + item.financeUserName
                  }
                  if (item.qualityUserName) {
                    item.qualityUserName = item.qualityUserExternal + '-' + item.qualityUserName
                  }
                  if (item.devHeadName) {
                    item.devHeadName = item.devHeadExternal + '-' + item.devHeadName
                  }
                  if (item.priceHeadName) {
                    item.priceHeadName = item.priceHeadExternal + '-' + item.priceHeadName
                  }
                  if (item.financeHeadName) {
                    item.financeHeadName = item.financeHeadExternal + '-' + item.financeHeadName
                  }
                  if (item.qualityHeadName) {
                    item.qualityHeadName = item.qualityHeadExternal + '-' + item.qualityHeadName
                  }
                  if (item.bargainingName) {
                    item.bargainingName = item.bargainingExternal + '-' + item.bargainingName
                  }
                  if (item.bargainingHeadName) {
                    item.bargainingHeadName =
                      item.bargainingHeadExternal + '-' + item.bargainingHeadName
                  }
                })
                return list
              }
            }
          }
        }
      ],
      editrowdata: {},
      //------------------------>
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'categoryDivisionReport',
        templateUrl: 'exportTmp', // 下载模板接口方法名
        uploadUrl: 'import', // 上传接口方法名
        file: 'multipartFile' //后端接收参数名
      }
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      } else if (e.toolbar.id === 'upload') {
        this.handleClickToolBarUpload()
      } else if (e.toolbar.id === 'Download') {
        this.handleClickToolBarDownload()
      }
    },
    handleClickToolBarAdd() {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // handleClickToolBarUpload() {
    //   this.$dialog({
    //     modal: () =>
    //       import(/* webpackChunkName: "components/upload" */ './components/locationUpload.vue'),
    //     data: {
    //       title: this.$t('上传')
    //     },
    //     success: () => {
    //       this.$refs.tepPage.refreshCurrentGridData()
    //       this.$toast({
    //         content: this.$t('操作成功'),
    //         type: 'success'
    //       })
    //     }
    //   })
    // },
    handleClickToolBarUpload() {
      this.showUploadExcel(true)
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      console.log(flag)
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm(data) {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t(data),
        type: 'success'
      })
      this.$refs.tepPage.refreshCurrentGridData()
    },
    handleClickToolBarDownload() {
      // let ids = selectList.map((item) => item.id)
      let params = {
        page: { current: 1, size: 20 }
      }
      this.$API.categoryDivisionReport.exportQuery(params).then((res) => {
        let blob = new Blob([res.data], {
          type: 'application/x-msdownload'
        })
        // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
        let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

        // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = this.$t('品类分工报表.xlsx')
        a.click()
        // 5.释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
    },
    orgNamedialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "material/components/AddDialog.vue" */ './components/dialog.vue'
          ),
        data: {
          title: this.$t('选择公司')
        },
        success: (data) => {
          console.log(data)
          // this.$refs.tepPage.refreshCurrentGridData();
          // this.$toast({
          //   content: this.$t("操作成功"),
          //   type: "success",
          // });
        }
      })
    },
    actionComplete(e) {
      if (e.requestType == 'save') {
        const { rowIndex } = e
        if (!e.data.orgName) {
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('公司不能为空'), type: 'warning' })
          return
        }
        if (!e.data.categoryCode) {
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('品类不能为空'), type: 'warning' })
          return
        }
        if (!e.data.startDate) {
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('开始日期不能为空'), type: 'warning' })
          return
        }
        if (
          e.data.startDate &&
          e.data.endDate &&
          Number(new Date(e.data.startDate)) > Number(new Date(e.data.endDate))
        ) {
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('开始日期不能大于结束日期'), type: 'warning' })
          return
        }
        let params = [this.editrowdata]
        params[0].startDate = e.data.startDate
        params[0].endDate = e.data.endDate
        if (Object.prototype.hasOwnProperty.call(e.data, 'id')) {
          params[0].id = e.data.id
        }
        console.log(params[0])
        this.$API.categoryDivisionReport
          .saveData(params)
          .then((res) => {
            if (res.data.failList.length == 0) {
              this.editrowdata = {}
              this.$refs.tepPage.refreshCurrentGridData()
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            } else {
              this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
              this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
              this.$toast({ content: this.$t(`${res.data.failList[0].result}`), type: 'warning' })
            }
          })
          .catch(() => {
            this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          })
      }
    },
    actionBegin(e) {
      console.log('actionBegin', e)
      if (e.requestType == 'save') {
        if (e.data.startDate) {
          e.data.startDate = utils.formateTime(e.data.startDate, 'yyyy-MM-dd')
        }
        if (e.data.endDate) {
          e.data.endDate = utils.formateTime(e.data.endDate, 'yyyy-MM-dd')
        }
      }
      if (e.requestType == 'beginEdit') {
        if (e.rowData.startDate) {
          e.rowData.startDate = new Date(e.rowData.startDate)
        }
        if (e.rowData.endDate) {
          e.rowData.endDate = new Date(e.rowData.endDate)
        }
      }
    },
    commandClick(args) {
      if (args.commandColumn.type === 'Deletes') {
        // let idList = [args.rowData.id]
        let params = {
          // idList: idList
          id: args.rowData.id
        }
        this.$API.categoryDivisionReport.remove(params).then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
        })
      }
    },
    parentEditrowdata(e) {
      if (e.text == 'orgName') {
        this.editrowdata.orgCode = e.data.entityCode
        this.editrowdata.orgName = e.data.entityName
      }
      if (e.text == 'categoryCode') {
        this.editrowdata.categoryCode = e.data.categoryCode
        this.editrowdata.categoryName = e.data.categoryName
        this.editrowdata.categoryId = e.data.id
      }
      if (e.text == 'devUserName') {
        this.editrowdata.devUserExternal = e.data.userName
        this.editrowdata.devUserName = e.data.fullName
        this.editrowdata.devUserId = e.data.id
        this.editrowdata.devUserSubjectId = e.data.subjectId
      }
      if (e.text == 'priceUserName') {
        this.editrowdata.priceUserExternal = e.data.userName
        this.editrowdata.priceUserName = e.data.fullName
        this.editrowdata.priceUserId = e.data.id
        this.editrowdata.priceUserSubjectId = e.data.subjectId
      }
      if (e.text == 'financeUserName') {
        this.editrowdata.financeUserExternal = e.data.userName
        this.editrowdata.financeUserName = e.data.fullName
        this.editrowdata.financeUserId = e.data.id
        this.editrowdata.financeUserSubjectId = e.data.subjectId
      }
      if (e.text == 'qualityUserName') {
        this.editrowdata.qualityUserExternal = e.data.userName
        this.editrowdata.qualityUserName = e.data.fullName
        this.editrowdata.qualityUserId = e.data.id
        this.editrowdata.qualityUserSubjectId = e.data.subjectId
      }
      if (e.text == 'devHeadName') {
        this.editrowdata.devHeadExternal = e.data.userName
        this.editrowdata.devHeadName = e.data.fullName
        this.editrowdata.devHeadId = e.data.id
        this.editrowdata.devHeadSubjectId = e.data.subjectId
      }
      if (e.text == 'priceHeadName') {
        this.editrowdata.priceHeadExternal = e.data.userName
        this.editrowdata.priceHeadName = e.data.fullName
        this.editrowdata.priceHeadId = e.data.id
        this.editrowdata.priceHeadSubjectId = e.data.subjectId
      }
      if (e.text == 'financeHeadName') {
        this.editrowdata.financeHeadExternal = e.data.userName
        this.editrowdata.financeHeadName = e.data.fullName
        this.editrowdata.financeHeadId = e.data.id
        this.editrowdata.financeHeadSubjectId = e.data.subjectId
      }
      if (e.text == 'qualityHeadName') {
        this.editrowdata.qualityHeadExternal = e.data.userName
        this.editrowdata.qualityHeadName = e.data.fullName
        this.editrowdata.qualityHeadId = e.data.id
        this.editrowdata.qualityHeadSubjectId = e.data.subjectId
      }
      if (e.text == 'bargainingName') {
        this.editrowdata.bargainingExternal = e.data.userName
        this.editrowdata.bargainingName = e.data.fullName
        this.editrowdata.bargainingId = e.data.id
        this.editrowdata.bargainingSubjectId = e.data.subjectId
      }
      if (e.text == 'bargainingHeadName') {
        this.editrowdata.bargainingHeadExternal = e.data.userName
        this.editrowdata.bargainingHeadName = e.data.fullName
        this.editrowdata.bargainingHeadId = e.data.id
        this.editrowdata.bargainingHeadSubjectId = e.data.subjectId
      }
    }
  }
}
</script>

<style lang="scss">
.full-height {
  height: 100vh;
  background-color: #fff;
  .operation {
    span {
      color: #2783fe;
      text-decoration: underline;
      margin: 0 10px;
    }
  }
  .operation:hover {
    cursor: pointer;
  }
  .e-color {
    color: #000;
  }
}
</style>
