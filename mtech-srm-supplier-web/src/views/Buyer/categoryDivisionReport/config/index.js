import { i18n } from '@/main.js'
// import Vue from 'vue'
import Select from '../components/Select.vue'
import inputShow from '../components/inputShow.vue'
import dataSelect from '../components/dataSelect.vue'
// import inputName from '../components/inputName.vue'
// import dialogShow from '../components/dialogShow.vue' ---弹框

export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  // { id: 'save', icon: 'icon_solid_Save', title: i18n.t('保存') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
  // { id: 'export', icon: 'icon_solid_export', title: i18n.t('重新下发') }
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: 200,
    field: 'orgName',
    headerText: i18n.t('公司'),
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'remote-autocomplete',
      renameField: 'orgCode',
      selectType: 'administrativeCompany', // 业务公司
      fields: { text: 'orgName', value: 'orgCode' },
      params: {
        organizationLevelCodes: ['ORG01', 'ORG02']
      },
      multiple: true,
      operator: 'in',
      url: '/masterDataManagement/tenant/organization/specified-level-paged-query'
    }
  },
  {
    width: 200,
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    allowEditing: false,
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 200,
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    editTemplate: () => {
      return { template: inputShow }
    }
  },
  {
    width: 150,
    field: 'devUserName',
    headerText: i18n.t('采购开发'),
    editTemplate: () => {
      return { template: Select }
    }
  },

  {
    width: 150,
    field: 'priceUserName',
    headerText: i18n.t('核价人员'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'financeUserName',
    headerText: i18n.t('财务人员'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'qualityUserName',
    headerText: i18n.t('SQE'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'devHeadName',
    headerText: i18n.t('采购开发负责人'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'priceHeadName',
    headerText: i18n.t('核价负责人'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'financeHeadName',
    headerText: i18n.t('财务负责人'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'qualityHeadName',
    headerText: i18n.t('SQE负责人'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'bargainingName',
    headerText: i18n.t('议价员'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'bargainingHeadName',
    headerText: i18n.t('议价负责人'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'startDate',
    headerText: i18n.t('开始日期'),
    editTemplate: () => {
      return { template: dataSelect }
    },
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    }
  },
  {
    width: 150,
    field: 'endDate',
    headerText: i18n.t('结束日期'),
    editTemplate: () => {
      return { template: dataSelect }
    },
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    }
  },
  {
    width: 250,
    field: 'operation',
    headerText: i18n.t('操作'),
    commands: [
      {
        type: 'Edit',
        buttonOption: {
          content: i18n.t('编辑'),
          iconCss: ' e-icons e-edit',
          cssClass: 'e-flat e-color'
        }
      },
      {
        type: 'Deletes',
        buttonOption: {
          content: i18n.t('删除'),
          iconCss: 'e-icons e-delete',
          cssClass: 'e-flat e-color'
        }
      }
    ],
    ignore: true
  }
  // {
  //   field: 'operation',
  //   headerText: i18n.t('操作'),
  //   template: () => {
  //     return {
  //       template: Vue.component('operation', {
  //         template: `<div class="operation">
  //         <span @click="clickSubmit">编辑</span><span @click="clickdisable">删除</span><span @click="clickhistory">修改记录</span></div>`,
  //         data: function () {
  //           return {
  //             data: {}
  //           }
  //         },
  //         methods: {
  //           clickSubmit() {
  //             this.$parent.$emit('clickOperation', { data: this.data, value: 'edit' })
  //           },
  //           clickdisable() {
  //             this.$parent.$emit('clickOperation', { data: this.data, value: 'delete' })
  //           },
  //           clickhistory() {
  //             this.$parent.$emit('clickOperation', { data: this.data, value: 'history' })
  //           }
  //         }
  //       })
  //     }
  //   }
  // }
]
// export const pageConfig = [
//   {
//     toolbar,
//     useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
//     grid: {
//       editSettings: {
//         allowEditing: true,
//         allowAdding: true,
//         allowDeleting: true,
//         mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
//         showConfirmDialog: false,
//         showDeleteConfirmDialog: false,
//         newRowPosition: 'Top'
//       },
//       allowEditing: true, //开启表格编辑操作
//       columnData,
//       asyncConfig: {
//         url: '/supplier/tenant/buyer/categoryDivision/list',
//         serializeList: (list) => {
//           list.forEach((item) => {
//             if (item.startDate) {
//               item.startDate = new Date(Number(item.startDate))
//             }
//           })
//           return list
//         }
//       }
//     }
//   }
// ]
