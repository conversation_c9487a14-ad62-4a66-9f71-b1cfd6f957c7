import { i18n } from '@/main.js'

export const columnData = [
  {
    field: 'entityCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'entityName',
    headerText: i18n.t('公司名称')
  }
]
export const pageConfig = [
  {
    toolbar: [],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      height: 350,
      allowPaging: true,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData,
      asyncConfig: {
        url: `/masterDataManagement/tenant/entity/paged-query`
      }
    }
  }
]
