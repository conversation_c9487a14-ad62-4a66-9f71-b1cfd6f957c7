<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :filtering="filtering"
      :allow-filtering="true"
    ></mt-select>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'

export default {
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: '', value: '' },

      dataSource: []
    }
  },

  mounted() {
    this.filtering = utils.debounce(this.filtering, 1000)
    //公司
    if (this.data.column.field === 'orgName') {
      let params = {
        page: { current: 1, size: 1000 }
      }
      this.$API.categoryDivisionReport.pagedQuery(params).then((res) => {
        this.dataSource = res.data.records
        this.fields = { text: 'entityName', value: 'entityName' }
      })
    }
    //品类
    if (this.data.column.field === 'categoryCode') {
      let params = {
        fuzzyNameOrCode: this.data.categoryCode ? this.data.categoryCode : ''
      }
      this.$API.categoryDivisionReport.fuzzyQuery(params).then((res) => {
        this.dataSource = res.data.map((item) => {
          return {
            theCodeName: item.categoryCode + '-' + item.categoryName,
            categoryCode: item.categoryCode,
            categoryName: item.categoryName
          }
        })
        this.fields = { text: 'theCodeName', value: 'categoryCode' }
      })
    }
    //采购开发
    if (this.data.column.field === 'devUserName') {
      // let value = ''
      // if (this.data.devUserName && this.data.devUserName.indexOf('-') != -1) {
      //   value = this.data.devUserName.split('-')[0]
      // } else if (this.data.devUserName) {
      //   value = this.data.devUserName
      // }
      let params = null
      if (this.data.devUserName) {
        console.log('asdfasdfasdf1234', this.data)
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.devUserExternal
                ? this.data.devUserExternal
                : this.data.devUserName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        console.log('asdfasdfasdf')
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
    }
    //核价人员
    if (this.data.column.field === 'priceUserName') {
      // let value = ''
      // if (this.data.priceUserName && this.data.priceUserName.indexOf('-') != -1) {
      //   value = this.data.priceUserName.split('-')[1]
      // } else if (this.data.priceUserName) {
      //   value = this.data.priceUserName
      // }
      let params = null
      if (this.data['priceUserName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.priceUserExternal
                ? this.data.priceUserExternal
                : this.data.priceUserName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })

      // let params = {
      //   fuzzyName: value
      // }
      // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
      //   let data = cloneDeep(res.data)
      //   data.map((item) => {
      //     item.employeeName = item.accountName + '-' + item.employeeName
      //   })
      //   this.dataSource = data
      //   this.fields = { text: 'employeeName', value: 'employeeName' }
      // })
    }
    //财务人员
    if (this.data.column.field === 'financeUserName') {
      // let value = ''
      // if (this.data.financeUserName && this.data.financeUserName.indexOf('-') != -1) {
      //   value = this.data.financeUserName.split('-')[1]
      // } else if (this.data.financeUserName) {
      //   value = this.data.financeUserName
      // }
      let params = null
      if (this.data['financeUserName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.financeUserExternal
                ? this.data.financeUserExternal
                : this.data.financeUserName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
      // let params = {
      //   fuzzyName: value
      // }
      // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
      //   let data = cloneDeep(res.data)
      //   data.map((item) => {
      //     item.employeeName = item.accountName + '-' + item.employeeName
      //   })
      //   this.dataSource = data
      //   this.fields = { text: 'employeeName', value: 'employeeName' }
      // })
    }
    //品质人员
    if (this.data.column.field === 'qualityUserName') {
      // let value = ''
      // if (this.data.qualityUserName && this.data.qualityUserName.indexOf('-') != -1) {
      //   value = this.data.qualityUserName.split('-')[1]
      // } else if (this.data.qualityUserName) {
      //   value = this.data.qualityUserName
      // }
      let params = null
      if (this.data['qualityUserName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.qualityUserExternal
                ? this.data.qualityUserExternal
                : this.data.qualityUserName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
      // let params = {
      //   fuzzyName: value
      // }
      // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
      //   let data = cloneDeep(res.data)
      //   data.map((item) => {
      //     item.employeeName = item.accountName + '-' + item.employeeName
      //   })
      //   this.dataSource = data
      //   this.fields = { text: 'employeeName', value: 'employeeName' }
      // })
    }
    //采购开发负责人
    if (this.data.column.field === 'devHeadName') {
      // let value = ''
      // if (this.data.devHeadName && this.data.devHeadName.indexOf('-') != -1) {
      //   value = this.data.devHeadName.split('-')[1]
      // } else if (this.data.devHeadName) {
      //   value = this.data.devHeadName
      // }
      let params = null
      if (this.data['devHeadName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.devHeadExternal
                ? this.data.devHeadExternal
                : this.data.devHeadName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
      // let params = {
      //   fuzzyName: value
      // }
      // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
      //   let data = cloneDeep(res.data)
      //   data.map((item) => {
      //     item.employeeName = item.accountName + '-' + item.employeeName
      //   })
      //   this.dataSource = data
      //   this.fields = { text: 'employeeName', value: 'employeeName' }
      // })
    }
    //核价负责人
    if (this.data.column.field === 'priceHeadName') {
      // let value = ''
      // if (this.data.priceHeadName && this.data.priceHeadName.indexOf('-') != -1) {
      //   value = this.data.priceHeadName.split('-')[1]
      // } else if (this.data.priceHeadName) {
      //   value = this.data.priceHeadName
      // }
      let params = null
      if (this.data['priceHeadName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.priceHeadExternal
                ? this.data.priceHeadExternal
                : this.data.priceHeadName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
      // let params = {
      //   fuzzyName: value
      // }
      // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
      //   let data = cloneDeep(res.data)
      //   data.map((item) => {
      //     item.employeeName = item.accountName + '-' + item.employeeName
      //   })
      //   this.dataSource = data
      //   this.fields = { text: 'employeeName', value: 'employeeName' }
      // })
    }
    //财务负责人
    if (this.data.column.field === 'financeHeadName') {
      // let value = ''
      // if (this.data.financeHeadName && this.data.financeHeadName.indexOf('-') != -1) {
      //   value = this.data.financeHeadName.split('-')[1]
      // } else if (this.data.financeHeadName) {
      //   value = this.data.financeHeadName
      // }
      let params = null
      if (this.data['financeHeadName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.financeHeadExternal
                ? this.data.financeHeadExternal
                : this.data.financeHeadName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
      // let params = {
      //   fuzzyName: value
      // }
      // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
      //   let data = cloneDeep(res.data)
      //   data.map((item) => {
      //     item.employeeName = item.accountName + '-' + item.employeeName
      //   })
      //   this.dataSource = data
      //   this.fields = { text: 'employeeName', value: 'employeeName' }
      // })
    }
    //品质负责人
    if (this.data.column.field === 'qualityHeadName') {
      // let value = ''
      // if (this.data.qualityHeadName && this.data.qualityHeadName.indexOf('-') != -1) {
      //   value = this.data.qualityHeadName.split('-')[1]
      // } else if (this.data.qualityHeadName) {
      //   value = this.data.qualityHeadName
      // }
      let params = null
      if (this.data['qualityHeadName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.qualityHeadExternal
                ? this.data.qualityHeadExternal
                : this.data.qualityHeadName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
      // let params = {
      //   fuzzyName: value
      // }
      // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
      //   let data = cloneDeep(res.data)
      //   data.map((item) => {
      //     item.employeeName = item.accountName + '-' + item.employeeName
      //   })
      //   this.dataSource = data
      //   this.fields = { text: 'employeeName', value: 'employeeName' }
      // })
    }
    //议价员
    if (this.data.column.field === 'bargainingName') {
      let params = null
      if (this.data['bargainingName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.bargainingExternal
                ? this.data.bargainingExternal
                : this.data.bargainingName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
    }
    //议价负责人
    if (this.data.column.field === 'bargainingHeadName') {
      let params = null
      if (this.data['bargainingHeadName']) {
        params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'and',
          defaultRules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: this.data.bargainingHeadExternal
                ? this.data.bargainingHeadExternal
                : this.data.bargainingHeadName.split('-')[0]
            }
          ],
          subjectType: 0
        }
      } else {
        params = {
          page: {
            current: 1,
            size: 20
          },
          subjectType: 0
        }
      }
      this.$API.categoryDivisionReport.page(params).then((res) => {
        let data = cloneDeep(res.data.records)
        data.map((item) => {
          item.employeeName = item.userName + '-' + item.fullName
        })
        this.dataSource = data
        this.fields = { text: 'employeeName', value: 'employeeName' }
      })
    }
  },

  methods: {
    filtering(e) {
      if (this.data.column.field === 'categoryCode') {
        let params = {
          fuzzyNameOrCode: e.text
        }
        this.$API.categoryDivisionReport.fuzzyQuery(params).then((res) => {
          this.dataSource = res.data.map((item) => {
            return {
              theCodeName: item.categoryCode + '-' + item.categoryName,
              categoryCode: item.categoryCode,
              categoryName: item.categoryName
            }
          })
          this.fields = { text: 'theCodeName', value: 'categoryCode' }
        })
      }
      //采购开发
      if (this.data.column.field === 'devUserName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
        // let params = {
        //   fuzzyName: e.text
        // }
        // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
        //   let data = cloneDeep(res.data)
        //   data.map((item) => {
        //     item.employeeName = item.accountName + '-' + item.employeeName
        //   })
        //   this.dataSource = data
        //   this.fields = { text: 'employeeName', value: 'employeeName' }
        // })
      }
      //核价人员
      if (this.data.column.field === 'priceUserName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
        // let params = {
        //   fuzzyName: e.text
        // }
        // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
        //   let data = cloneDeep(res.data)
        //   data.map((item) => {
        //     item.employeeName = item.accountName + '-' + item.employeeName
        //   })
        //   this.dataSource = data
        //   this.fields = { text: 'employeeName', value: 'employeeName' }
        // })
      }
      //财务人员
      if (this.data.column.field === 'financeUserName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
        // let params = {
        //   fuzzyName: e.text
        // }
        // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
        //   let data = cloneDeep(res.data)
        //   data.map((item) => {
        //     item.employeeName = item.accountName + '-' + item.employeeName
        //   })
        //   this.dataSource = data
        //   this.fields = { text: 'employeeName', value: 'employeeName' }
        // })
      }
      //品质人员
      if (this.data.column.field === 'qualityUserName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
        // let params = {
        //   fuzzyName: e.text
        // }
        // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
        //   let data = cloneDeep(res.data)
        //   data.map((item) => {
        //     item.employeeName = item.accountName + '-' + item.employeeName
        //   })
        //   this.dataSource = data
        //   this.fields = { text: 'employeeName', value: 'employeeName' }
        // })
      }
      //采购开发负责人
      if (this.data.column.field === 'devHeadName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
        // let params = {
        //   fuzzyName: e.text
        // }
        // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
        //   let data = cloneDeep(res.data)
        //   data.map((item) => {
        //     item.employeeName = item.accountName + '-' + item.employeeName
        //   })
        //   this.dataSource = data
        //   this.fields = { text: 'employeeName', value: 'employeeName' }
        // })
      }
      //核价负责人
      if (this.data.column.field === 'priceHeadName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
        // let params = {
        //   fuzzyName: e.text
        // }
        // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
        //   let data = cloneDeep(res.data)
        //   data.map((item) => {
        //     item.employeeName = item.accountName + '-' + item.employeeName
        //   })
        //   this.dataSource = data
        //   this.fields = { text: 'employeeName', value: 'employeeName' }
        // })
      }
      //财务负责人
      if (this.data.column.field === 'financeHeadName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
        // let params = {
        //   fuzzyName: e.text
        // }
        // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
        //   let data = cloneDeep(res.data)
        //   data.map((item) => {
        //     item.employeeName = item.accountName + '-' + item.employeeName
        //   })
        //   this.dataSource = data
        //   this.fields = { text: 'employeeName', value: 'employeeName' }
        // })
      }
      //品质负责人
      if (this.data.column.field === 'qualityHeadName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
        // let params = {
        //   fuzzyName: e.text
        // }
        // this.$API.categoryDivisionReport.currentTenantEmployees(params).then((res) => {
        //   let data = cloneDeep(res.data)
        //   data.map((item) => {
        //     item.employeeName = item.accountName + '-' + item.employeeName
        //   })
        //   this.dataSource = data
        //   this.fields = { text: 'employeeName', value: 'employeeName' }
        // })
      }
      //议价员
      if (this.data.column.field === 'bargainingName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
      }
      //议价负责人
      if (this.data.column.field === 'bargainingHeadName') {
        let params = {
          page: {
            current: 1,
            size: 20
          },
          condition: 'or',
          rules: [
            {
              label: this.$t('账号'),
              field: 'userName',
              type: 'string',
              operator: 'contains',
              value: e.text
            },
            {
              label: this.$t('姓名'),
              field: 'fullName',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          subjectType: 0
        }
        this.$API.categoryDivisionReport.page(params).then((res) => {
          let data = cloneDeep(res.data.records)
          data.map((item) => {
            item.employeeName = item.userName + '-' + item.fullName
          })
          this.dataSource = data
          this.fields = { text: 'employeeName', value: 'employeeName' }
        })
      }
    },
    startOpen() {},
    selectChange(e) {
      //公司
      if (this.data.column.field === 'orgName') {
        let params = {
          data: e.itemData,
          text: 'orgName'
        }
        this.$parent.$emit('parentEditrowdata', params)
        // this.$bus.$emit('entityNamebus', e.itemData)
        // this.$bus.$emit('entityIdbus', e.itemData)
      }
      //品类
      if (this.data.column.field === 'categoryCode') {
        let params = {
          data: e.itemData,
          text: 'categoryCode'
        }
        console.log('selectChange', params)
        this.$parent.$emit('parentEditrowdata', params)
        this.$bus.$emit('categoryCodebus', e.itemData)
      }
      //采购开发
      if (this.data.column.field === 'devUserName') {
        let params = {
          data: e.itemData,
          text: 'devUserName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //核价人员
      if (this.data.column.field === 'priceUserName') {
        let params = {
          data: e.itemData,
          text: 'priceUserName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //财务人员
      if (this.data.column.field === 'financeUserName') {
        let params = {
          data: e.itemData,
          text: 'financeUserName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //品质人员
      if (this.data.column.field === 'qualityUserName') {
        let params = {
          data: e.itemData,
          text: 'qualityUserName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //采购开发负责人
      if (this.data.column.field === 'devHeadName') {
        let params = {
          data: e.itemData,
          text: 'devHeadName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //核价负责人
      if (this.data.column.field === 'priceHeadName') {
        let params = {
          data: e.itemData,
          text: 'priceHeadName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //财务负责人
      if (this.data.column.field === 'financeHeadName') {
        let params = {
          data: e.itemData,
          text: 'financeHeadName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //品质负责人
      if (this.data.column.field === 'qualityHeadName') {
        let params = {
          data: e.itemData,
          text: 'qualityHeadName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //议价员
      if (this.data.column.field === 'bargainingName') {
        let params = {
          data: e.itemData,
          text: 'bargainingName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
      //议价负责人
      if (this.data.column.field === 'bargainingHeadName') {
        let params = {
          data: e.itemData,
          text: 'bargainingHeadName'
        }
        this.$parent.$emit('parentEditrowdata', params)
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('entityNamebus')
    this.$bus.$off('categoryCodebus')
    this.$bus.$off('entityIdbus')
  }
}
</script>
