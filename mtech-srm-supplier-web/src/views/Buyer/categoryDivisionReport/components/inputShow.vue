<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  mounted() {
    // if (this.data.column.field == 'orgCode') {
    //   this.$bus.$on('entityNamebus', (val) => {
    //     this.data.orgCode = val.entityCode
    //   })
    // }
    if (this.data.column.field == 'categoryCode') {
      this.$bus.$on('categoryCodebus', (val) => {
        this.data.categoryCode = val.categoryCode
      })
    }
    // if (this.data.column.field == 'orgId') {
    //   this.$bus.$on('entityIdbus', (val) => {
    //     this.data.orgId = val.entityId
    //   })
    // }
  },
  methods: {
    onInput(e) {
      console.log(e)
    }
  }
}
</script>
