<template>
  <div>
    <div class="orgName">
      <mt-input
        v-model="data[data.column.field]"
        autocomplete="off"
        :show-clear-button="false"
        :disabled="true"
        @input="onInput"
      ></mt-input>
      <mt-icon name="icon_input_search" @click.native="handleSearch"></mt-icon>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },

  mounted() {},

  methods: {
    handleSearch() {
      console.log('弹框')
      this.$parent.$emit('orgNamedialog', this.data)
    },
    onInput() {}
  },
  beforeDestroy() {}
}
</script>

<style lang="scss" scoped>
.orgName {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .mt-input {
    width: 90%;
  }
  /deep/.mt-icon {
    width: 40px;
  }
}
</style>
