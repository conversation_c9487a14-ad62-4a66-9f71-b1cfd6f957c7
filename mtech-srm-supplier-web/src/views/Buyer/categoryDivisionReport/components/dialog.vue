<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="dialog-main"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <mt-template-page ref="templateRef" :hidden-tabs="true" :template-config="pageConfig">
      </mt-template-page>
    </mt-dialog>
  </div>
</template>
<script>
import { pageConfig } from '../config/dialog'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      let _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      if (_records.length < 1) {
        this.$toast({ content: this.$t('请先选择一行数据'), type: 'warning' })
      }
      // this.$emit('confirm-function')
    },

    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
          .mt-icons {
            cursor: pointer;
          }
          .allowed {
            cursor: no-drop;
          }
        }
      }
    }
    .common-template-page {
      height: 100%;
      .e-gridcontent {
        max-height: 340px;
      }
      .mt-pagertemplate {
        overflow: inherit !important;
      }
    }
  }
}
</style>
