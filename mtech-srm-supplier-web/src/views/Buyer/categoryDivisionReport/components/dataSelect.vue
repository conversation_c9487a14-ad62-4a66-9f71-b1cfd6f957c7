<template>
  <div>
    <!-- <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :filtering="filtering"
      :allow-filtering="true"
    ></mt-select> -->

    <!-- <mt-date-range-picker
      :id="data.column.field"
      v-model="data[data.column.field]"
      :show-clear-button="true"
      :allow-edit="false"
      :placeholder="$t('请选择')"
    ></mt-date-range-picker> -->
    <mt-date-picker
      :id="data.column.field"
      v-model="data[data.column.field]"
      :open-on-focus="true"
      :allow-edit="false"
      :placeholder="$t('请选择开始时间')"
    ></mt-date-picker>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },

  mounted() {},
  methods: {},
  beforeDestroy() {}
}
</script>
