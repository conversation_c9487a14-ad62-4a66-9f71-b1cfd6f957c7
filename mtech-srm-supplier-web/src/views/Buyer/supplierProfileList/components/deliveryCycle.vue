<template>
  <div class="supplier-portrait">
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('基本信息') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="baseInfo"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox" v-show="isShow">
      <div class="b-info">
        <div class="b-info-title">{{ $t('主要原材料采购周期') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="procurementInfo"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox" v-show="isShow">
      <div class="b-info">
        <div class="b-info-title">{{ $t('生产周期') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="productionPhase"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox" v-show="isShow">
      <div class="b-info">
        <div class="b-info-title">{{ $t('委外加工') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="subcontract"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox" v-show="isShow">
      <div class="b-info">
        <div class="b-info-title">{{ $t('运输时间') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="haulageTime"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox" v-show="isShow">
      <div class="b-info">
        <div class="b-info-title">{{ $t('分析结论') }}</div>
        <div class="b-info-content">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('原材料采购周期方面') }}</div>
            <div class="textareaClass">
              {{ analyse.rawMaterialsAspect || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('生产周期方面') }}</div>
            <div class="textareaClass">
              {{ analyse.produceCycleAspect || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('运输时间方面') }}</div>
            <div class="textareaClass">
              {{ analyse.transportTimeAspect || '--' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  baseInfoColumn,
  procurementCycle,
  productionColumn,
  subcontractColumn,
  haulageColumn
} from '../config/index'
export default {
  props: {
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isShow: false,
      mainId: '',
      infoForm: {},
      analyse: {},
      baseInfo: [
        {
          gridId: 'd4989422-ab25-4bac-b5b2-494392a763e7',
          useToolTemplate: false,
          grid: {
            allowPaging: false,
            rowSelected: this.rowSelected,
            selectionSettings: {
              type: 'Single',
              mode: 'Row',
              enableToggle: false,
              checkboxMode: 'ResetOnRowClick'
            },
            columnData: baseInfoColumn,
            dataSource: []
          }
        }
      ],
      procurementInfo: [
        {
          gridId: 'e5315907-f9a8-47fb-9e60-0f2f3a6cc445',
          grid: {
            allowPaging: false,
            columnData: procurementCycle,
            dataSource: []
          }
        }
      ],
      productionPhase: [
        {
          gridId: '5a963789-7d9c-43a9-9f7f-f9902ab1af66',
          grid: {
            allowPaging: false,
            columnData: productionColumn,
            dataSource: []
          }
        }
      ],
      subcontract: [
        {
          gridId: 'c4d573d8-b3eb-4a20-aa17-d4798f27054f',
          grid: {
            allowPaging: false,
            columnData: subcontractColumn,
            dataSource: []
          }
        }
      ],
      haulageTime: [
        {
          gridId: '998e9e8a-9712-4730-987f-471ae9b52d81',
          grid: {
            allowPaging: false,
            columnData: [],
            dataSource: []
          }
        }
      ],
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      deliveryList: [],
      transportList: []
    }
  },
  mounted() {
    this.searchCycle()
    this.getAllSelectList()
  },
  methods: {
    async getAllSelectList() {
      // 交货地点
      await this.getEnterpriseNature('deliveryPlace')
      // 运输方式
      await this.getEnterpriseNature('transportType')
      this.nextThen()
    },
    async getEnterpriseNature(dictCode) {
      this.$loading()
      await this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'deliveryPlace') {
              this.deliveryList = data
            } else if (dictCode == 'transportType') {
              this.transportList = data
            }
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    nextThen() {
      this.$set(
        this.haulageTime[0].grid,
        'columnData',
        haulageColumn(this.deliveryList, this.transportList)
      )
    },
    // 查询第一个表格
    searchCycle() {
      let obj = {
        partnerArchiveId: this.partnerArchiveId,
        orgId: this.orgId,
        partnerRelationCode: this.partnerRelationCode,
        supplierEnterpriseId: this.supplierEnterpriseId
      }
      this.$loading()
      this.$API.supplierProfile
        .searchCycle(obj)
        .then((res) => {
          this.$hloading()
          this.baseInfo[0].grid = Object.assign({}, this.baseInfo[0].grid, {
            dataSource: res.data.deliveryInfoInsideDTOList
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 点击第一个表格的数据，拿到当前行id，查询其他表格
    rowSelected(filed) {
      let { data } = filed
      this.isShow = true
      this.mainId = data.id
      // 单独选择checkbox框得时候isInteracted=true，点击行选中的时候isInteracted = false；
      // 点击行选中查询，单独点击CheckBox不需要查询
      if (!filed.isInteracted) {
        this.searchCycleById()
      }
    },
    searchCycleById() {
      this.$loading()
      this.$API.supplierProfile
        .searchCycleById(this.mainId)
        .then((res) => {
          this.$hloading()
          let materialList = []
          let productionList = []
          let extList = []
          let transmitList = []
          // 1=原材料，2=生产周期，3=委外加工 4=运输
          res.data.cycleInsideDTOList.forEach((item) => {
            if (item.cycleType == 1) {
              materialList.push(item)
            } else if (item.cycleType == 2) {
              productionList.push(item)
            } else if (item.cycleType == 3) {
              extList.push(item)
            } else if (item.cycleType == 4) {
              transmitList.push(item)
            }
          })
          this.procurementInfo[0].grid = Object.assign({}, this.procurementInfo[0].grid, {
            dataSource: materialList
          })
          this.productionPhase[0].grid = Object.assign({}, this.productionPhase[0].grid, {
            dataSource: productionList
          })
          this.subcontract[0].grid = Object.assign({}, this.subcontract[0].grid, {
            dataSource: extList
          })
          this.haulageTime[0].grid = Object.assign({}, this.haulageTime[0].grid, {
            dataSource: transmitList
          })
          this.analyse = res.data.deliveryInfoInsideDTO
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 85%;
            padding: 5px 10px;
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
