<template>
  <div class="swiper-box">
    <!-- Swiper -->
    <div class="swiper">
      <div class="swiper-wrapper">
        <div class="swiper-slide" v-for="item of fileUrlArr" :key="item"><img :src="item" /></div>
      </div>
      <div class="swiper-button-next"></div>
      <div class="swiper-button-prev"></div>
    </div>
  </div>
</template>

<script>
import Swiper from '@/assets/swiper/swiper-bundle.min.js'
import '@/assets/swiper/swiper-bundle.min.css'
export default {
  props: {
    fileUrlArr: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      swiper: null
    }
  },
  mounted() {
    this.swiper = new Swiper('.swiper', {
      slidesPerView: 3,
      spaceBetween: 60,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      on: {
        resize: function () {}
      }
    })
  },
  beforeDestroy() {
    this.swiper = null
  }
}
</script>

<style lang="scss" scoped>
.swiper-box {
  width: 100%;
  min-height: 260px;

  .swiper {
    width: 100%;
    height: 100%;
    padding-left: 6.6%;
    padding-right: 6.6%;
  }

  .swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;

    img {
      display: block;
      width: 100%;
    }
  }

  .swiper-button-next {
    right: 20px;
    background: #e8e8e8;
    font-size: 24px;
    border: 1px solid #ddd;

    &::after {
      font-size: 24px;
      color: #9baac1;
    }
  }

  .swiper-button-prev {
    left: 20px;
    background: #e8e8e8;
    font-size: 24px;
    border: 1px solid #ddd;

    &::after {
      font-size: 24px;
      color: #9baac1;
    }
  }
}
</style>
