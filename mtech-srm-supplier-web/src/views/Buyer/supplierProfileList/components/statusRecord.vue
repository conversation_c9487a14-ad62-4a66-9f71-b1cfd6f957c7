<!-- 状态变化记录弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="1000px"
    height="750px"
  >
    <div class="dialog-content" style="padding-top: 1rem">
      <sc-table
        ref="sctableRef"
        grid-id="c9f72ed3-c83d-46cb-8488-a5d1f37bb95b"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        keep-source
        :sortable="false"
        :is-show-right-btn="true"
        :is-show-refresh-bth="true"
        @refresh="handleSearch"
      >
      </sc-table>
      <mt-page
        ref="pageRef"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.modalData.title
    },
    partnerRelationCode() {
      return this.modalData.partnerRelationCode
    },
    supplierInternalCode() {
      return this.modalData.supplierInternalCode
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          field: 'statusDesc',
          title: this.$t('状态')
        },
        {
          field: 'createTime',
          title: this.$t('生效时间')
        }
      ]
    }
  },
  mounted() {
    this.handleSearch()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        partnerRelationCode: this.partnerRelationCode,
        supplierInternalCode: this.supplierInternalCode
      }
      this.loading = true
      this.$API.supplierProfile
        .pageStatusRecordApi(params)
        .then((res) => {
          if (res.code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)

            const records = res.data?.records || []
            this.tableData = records
          }
        })
        .finally(() => (this.loading = false))
    },
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
