<template>
  <div class="supplier-portrait">
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('工商信息') }}</div>
        <div class="b-info-content">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('企业名称') }}</div>
            <div class="info-content-left">
              {{ businessDTO.enterpriseName || '--' }}
            </div>
            <div class="info-title epls">{{ $t('纳税人识别号') }}</div>
            <div class="info-content-right">
              {{ businessDTO.taxpayerIdentificationNo || '--' }}
            </div>
          </div>

          <div class="info-line fbox">
            <div class="info-title epls" :title="$t('企业英文名称')">{{ $t('企业英文名称') }}</div>
            <div class="info-content-left">
              {{ businessDTO.enterpriseEnglishName || '--' }}
            </div>
            <div class="info-title epls" :title="$t('统一社会信用代码')">
              {{ $t('统一社会信用代码') }}
            </div>
            <div class="info-content-right">
              {{ businessDTO.socialCreditCode || '--' }}
            </div>
          </div>

          <div class="info-line fbox">
            <div class="info-title">{{ $t('企业类型') }}</div>
            <div class="info-content-left">
              {{ businessDTO.enterpriseRegisterTypeName || '--' }}
            </div>
            <div class="info-title">{{ $t('工商注册号') }}</div>
            <div class="info-content-right">
              {{ businessDTO.businessRegNo || '--' }}
            </div>
          </div>

          <div class="info-line fbox">
            <div class="info-title epls" :title="$t('法人代表姓名')">{{ $t('法人代表姓名') }}</div>
            <div class="info-content-left">
              {{ businessDTO.corporation || '--' }}
            </div>
            <div class="info-title epls" :title="$t('组织机构代码')">{{ $t('组织机构代码') }}</div>
            <div class="info-content-right">
              {{ businessDTO.organizationCode || '--' }}
            </div>
          </div>

          <div class="info-line fbox">
            <div class="info-title">{{ $t('注册资金') }}</div>
            <div class="info-content-left">
              {{ businessDTO.registerCapital || '--' }} {{ businessDTO.capitalCurrency }}
            </div>
            <div class="info-title epls" :title="$t('公司成立时间')">{{ $t('公司成立时间') }}</div>
            <div class="info-content-right">
              {{ businessDTO.enterpriseRegisteredDate || '--' }}
            </div>
          </div>

          <div class="info-line fbox">
            <div class="info-title">{{ $t('经营状态') }}</div>
            <div class="info-content-left">
              {{ this.$t('存续') }}
            </div>
            <div class="info-title">{{ $t('营业期限') }}</div>
            <template v-if="businessDTO.businessEndDate === -1">
              <div class="info-content-right">{{ $t('长期有效') }}</div>
            </template>
            <template>
              <div class="info-content-right">
                {{ businessDTO.businessStartDate || '--' }}-{{
                  businessDTO.businessEndDate || '--'
                }}
              </div>
            </template>
          </div>

          <div class="info-line fbox">
            <div class="info-title">{{ $t('所属行业') }}</div>
            <div class="info-content-left">
              {{ businessDTO.serviceTypeName || '--' }}
            </div>
            <div class="info-title">{{ $t('核准日期') }}</div>
            <div class="info-content-right">
              {{ businessDTO.issuranceDate || '--' }}
            </div>
          </div>

          <div class="spc-info-line fbox">
            <div class="info-title">{{ $t('经营范围') }}</div>
            <div class="info-content-expand flex1">
              <div class="txt-wrap" :class="{ 'is-expand': isExpand }">
                {{ businessDTO.businessScope || '--' }}
              </div>
              <div class="expand-icon" :class="{ rotate: isExpand }" @click="expandTxt">
                <i class="mt-icons mt-icon-icon_input_arrow"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="b-relation">
        <div class="b-info-title fbox">
          {{ $t('关系图谱') }}
          <div class="update-msg fbox">
            <div class="update-time">
              {{ $t('更新日期') + '：' + enterpriseShipList.lastUpdateTime }}
            </div>
            <div class="update" @click="updata">{{ $t('更新') }}</div>
          </div>
        </div>

        <div class="b-info-content echar-wrap" id="supplier" style="height: 420px"></div>
      </div>
    </div>

    <div class="business-information fbox mr-30">
      <div class="b-info">
        <div class="b-info-title">{{ $t('合作履历') }}</div>
        <div class="b-content-corp">
          <partner-event :partner-event-list="PartnerEventList"></partner-event>
        </div>
      </div>
      <div class="b-relation">
        <div class="b-info-title">{{ $t('投标信息') }}</div>
        <div class="b-info-bid">
          <bid-info :get-record-bidding-list="getRecordBiddingList"></bid-info>
        </div>
      </div>
    </div>

    <div class="business-information fbox mr-30">
      <div class="b-info">
        <div class="b-info-title">{{ $t('绩效信息') }}</div>
        <div class="b-content-corp">
          <div v-if="!emptyPerformance" class="performance-box" id="performance"></div>
        </div>
      </div>
      <div class="b-relation">
        <div class="radar-title fbox">
          <div class="b-info-title">{{ $t('维度得分') }}</div>
          <div class="switch-btn" @click="switchDisplay">
            <mt-icon name="icon_card_transfer"></mt-icon>{{ $t('切换列表视图') }}
          </div>
        </div>
        <div class="b-info-bid">
          <div class="radar-box" id="radar" v-show="radarDisplay === 'radar' && !emptyRadar"></div>

          <div class="table-box" v-show="radarDisplay === 'table'">
            <mt-template-page
              ref="templateRef"
              :use-tool-template="false"
              :template-config="pageConfig"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="access-process-warp">
      <div class="b-info-title">{{ $t('准入信息') }}</div>
      <slot></slot>
    </div>

    <div class="swiper-box" v-show="fileUrlArr.length > 0">
      <div class="b-info-title">{{ $t('认证信息') }}</div>
      <mt-swiper :file-url-arr="fileUrlArr"></mt-swiper>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import PartnerEvent from './partnerEvent'
import BidInfo from './bidInfo'
import swiper from './swiper'
import { pageConfig } from '../config'
import utils from '@/utils/utils.js'
let chartDom = null
let myChart = null
// 绩效
let performanceDom = null
let performanceChart = null
// 雷达
let radarDom = null
let radarChart = null
export default {
  name: 'SupplierPortrait',
  components: {
    PartnerEvent,
    BidInfo,
    'mt-swiper': swiper
  },
  data() {
    return {
      pageConfig,
      isExpand: false,
      // 合作履历列表
      PartnerEventList: [],
      getRecordBiddingList: {},
      performanceInfo: {},
      radarDisplay: 'radar',
      emptyRadar: false,
      emptyPerformance: false
    }
  },
  props: {
    businessDTO: {
      type: Object,
      default: () => {
        return {}
      }
    },
    enterpriseShipList: {
      type: Object,
      default: () => {
        return {}
      }
    },
    partnerArchiveId: {
      type: String,
      default: ''
    },
    organizationId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    fileUrlArr: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    enterpriseShipList: {
      handler(nv) {
        if (nv && nv.nodes) {
          this.$nextTick(() => {
            this.initEchartFunction()
          })
        }
      },
      deep: true
    },
    organizationId: {
      handler(nv) {
        if (nv) {
          this.getPartnerEvent(this.partnerArchiveId, nv, this.supplierEnterpriseId)
          this.getRecordBidding(this.partnerArchiveId, nv, this.supplierEnterpriseId)
          this.getPerformance(this.partnerArchiveId, nv, this.supplierEnterpriseId)
        }
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    initEchartFunction() {
      chartDom = document.getElementById('supplier')
      myChart = echarts.init(chartDom)
      this.$nextTick(() => {
        this.initEchart(this.enterpriseShipList)
      })
    },
    // 更新关系图
    updata() {
      this.$emit('refresh')
    },
    expandTxt() {
      this.isExpand = !this.isExpand
    },
    // 初始化关系图
    initEchart(graph) {
      let option = {}
      // myChart.showLoading();
      // myChart.hideLoading();
      let nodes = graph.nodes.map((v) => {
        // 当前节点 rgb(255, 147, 73)  企业 rgb(0, 139, 248) 人员 rgb(245, 87, 62)  投资 任职
        let itemStyle = {}
        // 人员
        if (v.type === 'P') {
          itemStyle = {
            color: '#f5573e',
            borderColor: '#ff3d1f',
            overflow: 'break',
            borderWidth: 2
          }
        }
        // 企业
        if (v.type === 'E') {
          itemStyle = {
            color: '#42A2F1',
            width: 50,
            borderColor: '#6eb9f7',

            borderWidth: 2
          }
        }

        return {
          ...v,
          name: v.name + '', // 节点只支持字符串
          itemStyle: itemStyle // 节点样式
        }
      })
      console.log(nodes)
      let links = graph.links.map((v) => {
        return {
          ...v,
          source: v.sourceId + '',
          target: v.targetId + '',
          name:
            !!v.properties && !!v.properties.relationDescDetail
              ? v.properties.relationDescDetail
              : '',
          label: {
            show: true,
            fontSize: 16,
            overflow: 'hidden',
            align: 'center',
            verticalAlign: 'middle'
            // formatter: '{@relationDescDetail}'
          }
        }
      })
      // let categories = graph.links.map(function (a) {
      //   return {
      //     name: a.type,
      //   };
      // });
      // graph.nodes.forEach(function (node) {
      //   node.label = {
      //     show: node.symbolSize > 30
      //   };
      // });

      option = {
        title: {
          text: ''
        },
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        // 线上弹框
        tooltip: {
          formatter: function (a) {
            return a.data.name
          }
        },
        legend: [
          // 线上文字
          {
            // selectedMode: 'single',
            // data: categories.map(function (a) {
            //   return a.name;
            // }),
          }
        ],
        series: [
          {
            type: 'graph',
            layout: 'force',
            focusNodeAdjacency: true, // hover 高亮关系图
            draggable: false,
            // symbolSize: 50,
            // 设置节点大小
            symbolSize: (value, params) => {
              let { data } = params
              let symbolSize = 50
              if (data.type === 'P') {
                symbolSize = 60
              }
              params.symbolSize = symbolSize
              return params.symbolSize
            },
            roam: true,
            // 节点上的文字
            label: {
              show: true,
              color: '#fff',
              width: 50,
              overflow: 'breakAll'
            },
            force: {
              repulsion: 150,
              edgeLength: 100
            },
            legendHoverLink: true, // hover 时的联动高亮。
            edgeSymbol: ['circle', 'arrow'], // 箭头头尾样式
            edgeSymbolSize: [4, 10],
            data: nodes,
            links: links,
            edgeLabel: {
              // 线上文字
              show: true,
              formatter: function (x) {
                return x.data.name
              },
              color: '#333',
              fontSize: 16,
              align: 'center',
              verticalAlign: 'middle'
            },
            labelLayout: {
              // 在缩放时可以实现自动的标签隐藏。
              hideOverlap: true
            },
            scaleLimit: {
              min: 0.4,
              max: 2
            },
            lineStyle: {
              color: '#999',
              curveness: 0.1,
              opacity: 0.9,
              width: 1,
              show: true
            }
          }
        ]
      }
      myChart.setOption(option)
    },

    // 获取合作履历
    getPartnerEvent(partnerArchiveId, organizationId, supplierEnterpriseId) {
      this.$API.supplierlifecycle
        .getPartnerEvent({
          partnerArchiveId,
          organizationId,
          supplierEnterpriseId: supplierEnterpriseId
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            let PartnerEventList = res.data.map((result) => {
              return {
                ...result,
                filterTime: result.createTime // utils.formateTime(res.data.createTime, "yyyy-MM"),
              }
            })
            this.PartnerEventList = PartnerEventList
          } else {
            this.PartnerEventList = []
          }
        })
    },
    // 中标信息、合作次数、金额
    getRecordBidding(partnerArchiveId, organizationId, supplierEnterpriseId) {
      this.$API.supplierlifecycle
        .getRecordBidding({
          partnerArchiveId,
          organizationId,
          supplierEnterpriseId: supplierEnterpriseId
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.getRecordBiddingList = res.data
            this.$emit('setBiddingData', {
              cooperationTimes: res.data.cooperationTimes,
              cooperationMoney: res.data.cooperationMoney,
              joinTenderNumber: res.data.joinTenderNumber
            })
          } else {
            this.getRecordBiddingList = []
          }
        })
    },
    // 绩效信息、级别、、绩效得分
    getPerformance(partnerArchiveId, organizationId, supplierEnterpriseId) {
      this.$API.supplierlifecycle
        .getPerformance({
          partnerArchiveId,
          organizationId,
          supplierEnterpriseId: supplierEnterpriseId
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.performanceInfo = res.data

            this.$emit('setPerformanceScore', {
              performanceScore: res.data.performanceScore,
              supplierGradeName: res.data.supplierGradeName
            })

            if (utils.isEmpty(res.data.dimensionScores)) {
              this.emptyRadar = true
              radarDom = null
              radarChart = null
            } else {
              this.emptyRadar = false
              this.$nextTick(() => {
                this.initRadarChart(res.data.dimensionScores)
              })
            }
            if (utils.isEmpty(res.data.tenCycleScores)) {
              this.emptyPerformance = true // 空绩效图
              performanceDom = null
              performanceChart = null
            } else {
              this.emptyPerformance = false
              this.$nextTick(() => {
                this.initPerformanceChart(res.data.tenCycleScores)
              })
            }
          } else {
            this.performanceInfo = {}
          }
        })
    },
    // 渲染绩效chart
    initPerformanceChart(tenCycleScores) {
      // if (utils.isEmpty(tenCycleScores)) {
      //   this.emptyPerformance = true  // 空绩效图
      // } else {
      //   this.emptyPerformance = false
      // }

      performanceDom = document.getElementById('performance')
      performanceChart = echarts.init(performanceDom)

      let option
      let xData = tenCycleScores.map((v) => v.itemName)
      let yData = tenCycleScores.map((v) => v.itemScore)

      option = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xData
        },
        yAxis: {
          type: 'value'
        },
        grid: {
          bottom: '10%',
          height: '80%'
        },
        series: [
          {
            data: yData,
            type: 'line',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(0,70,156,0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(0,70,156,1)'
                }
              ])
            }
          }
        ]
      }

      option && performanceChart.setOption(option)
    },

    // 渲染雷达图
    initRadarChart(dimensionScores) {
      // if (utils.isEmpty(dimensionScores)) {
      //   this.emptyRadar = true
      //   radarDom = null
      //   radarChart = null
      // } else {
      //   this.emptyRadar = false
      // }
      this.initTable(dimensionScores)

      radarDom = document.getElementById('radar')
      radarChart = echarts.init(radarDom)
      let option
      let indicatorArr = dimensionScores.map((v) => {
        return {
          name: v.dimensionName,
          max: v.dimensionFullScore
        }
      })
      let seriesValue = dimensionScores.map((v) => v.dimensionScore)
      console.log(indicatorArr, seriesValue)
      option = {
        title: {
          text: ''
        },
        // 分类
        // legend: {
        //   // data: ['Allocated Budget', 'Actual Spending']
        // },
        radar: {
          shape: 'circle',
          indicator: indicatorArr || [],
          axisName: {
            color: '#292929',
            fontSize: 16
          }
        },
        splitArea: {
          areaStyle: {
            color: ['#F5F6F9', '#fff', '#fff', '#fff'],
            shadowColor: 'rgba(0, 0, 0, 0.2)',
            shadowBlur: 10
          }
        },
        axisLine: {
          lineStyle: {
            color: 'red'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'green'
          }
        },
        series: [
          {
            type: 'radar',
            data: [
              {
                value: seriesValue
              }
            ]
          }
        ]
      }

      option && radarChart.setOption(option)
    },

    initTable(dimensionScores) {
      dimensionScores.forEach((e) => {
        e.targetScores.forEach((i) => {
          i.dimensionName = i.targetName
          i.dimensionScore = i.targetScore
          i.dimensionFullScore = i.fullScore
        })
      })
      this.pageConfig[0].treeGrid['dataSource'] = []
      this.$nextTick(() => {
        this.$set(this.pageConfig[0].treeGrid, 'dataSource', dimensionScores)
      })
    },

    switchDisplay() {
      this.radarDisplay = this.radarDisplay === 'radar' ? 'table' : 'radar'
    }
  },
  beforeDestroy() {
    chartDom = null
    myChart = null
    performanceDom = null
    performanceChart = null
    radarDom = null
    radarChart = null
    // echarts.clear()
    // echarts.dispose()
  }
}
</script>

<style lang="scss" scoped>
.mr-30 {
  margin-top: 30px;
}
.fbox {
  display: flex;
  align-items: center;
}
.flex1 {
  flex: 1;
}

.epls {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.b-info-title {
  position: relative;
  width: 100%;
  height: 16px;
  line-height: 16px;
  font-size: 16px;
  color: rgba(41, 41, 41, 1);
  padding-left: 14px;

  .update-msg {
    position: absolute;
    right: 40px;

    .update-time {
      font-size: 12px;
      color: rgba(154, 154, 154, 1);
      margin-right: 10px;
    }
    .update {
      font-size: 12px;
      color: rgba(0, 70, 156, 1);
      cursor: pointer;
    }
  }

  &::before {
    content: ' ';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: #00469c;
    position: absolute;
    left: 0;
    top: 0;
  }
}

.supplier-portrait {
  margin-top: 20px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;

    .b-info {
      width: 57%;

      .b-info-content {
        padding-left: 0;
        margin-top: 16px;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            width: 18%;
            background: rgba(99, 134, 193, 0.1);
            padding-left: 30px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-left {
            width: 39%;
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            width: 26%;
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            width: 18%;
            background: rgba(99, 134, 193, 0.1);
            padding-left: 30px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
