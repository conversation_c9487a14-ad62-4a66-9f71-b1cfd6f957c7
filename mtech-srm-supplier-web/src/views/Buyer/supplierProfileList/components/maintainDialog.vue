<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="orgName" :label="$t('公司名称')">
          <mt-input
            v-model="formObject.orgName"
            float-label-type="Never"
            :disabled="true"
            :placeholder="$t('请选择公司')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商')">
          <mt-input
            v-model="formObject.supplierName"
            float-label-type="Never"
            :disabled="true"
            :placeholder="$t('请选择供应商')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="groupSupplier" :label="$t('集团供应商')">
          <mt-input
            v-model="formObject.groupSupplier"
            float-label-type="Never"
            :placeholder="$t('请输入集团供应商')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        organizationName: '', //公司名称
        supplierName: '', // 供应商名称
        groupSupplier: '' //集团供应商
      },
      //必填项
      formRules: {
        organizationName: [
          {
            required: true,
            message: this.$t('公司名称'),
            trigger: 'blur'
          }
        ]
      },
      //公司下拉框
      companySelect: [],
      //供应商下拉框
      supplierSelect: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    listData() {
      return this.modalData.data
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    let { supplierName, supplierCode, orgName, orgCode } = this.listData
    this.formObject.supplierName = supplierName
    this.formObject.supplierCode = supplierCode
    this.formObject.orgName = orgName
    this.formObject.orgCode = orgCode
  },
  methods: {
    //点击确认
    confirm() {
      console.log(this.formObject.groupSupplier, this.listData)
      let params = {
        groupSupplier: this.formObject.groupSupplier,
        id: this.listData.partnerArchiveId
      }
      this.$API.supplierProfile.update(params).then(() => {
        this.$emit('confirm-function')
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
