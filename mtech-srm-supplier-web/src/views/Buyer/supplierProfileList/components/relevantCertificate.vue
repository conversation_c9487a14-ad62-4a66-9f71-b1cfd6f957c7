<template>
  <div class="supplier-portrait">
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('三证信息') }}</div>
        <div class="b-info-content">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('统一社会信用代码') }}</div>
            <div class="info-content-left">
              {{ certBaseInfo.creditCode || '--' }}
            </div>
            <div class="info-title epls">{{ $t('相关经营证书') }}</div>
            <div class="info-content-right">
              {{ certBaseInfo.businessCertificate || '--' }}
            </div>
            <div class="info-title epls">{{ $t('营业执照附件') }}</div>
            <div class="info-content-right">
              <div style="width: 100%">
                <span
                  class="downStyle"
                  v-for="(item, index) in certBaseInfo.licenseAttachment"
                  :key="index"
                  @click="downAttachment(item)"
                  >{{ item.fileName }}</span
                >
              </div>
              <!-- <mt-common-uploader
                :key="comUploadKey"
                :isSingleFile="false"
                :isView="true"
                type="line"
                v-model="certBaseInfo.licenseAttachment"
              ></mt-common-uploader> -->
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('截止日期') }}</div>
            <div class="info-content-left">
              {{ checkDate(certBaseInfo.expireDate) || '--' }}
            </div>
            <div class="info-title epls">{{ $t('附件') }}</div>
            <div class="info-content-right" style="width: 52%">
              <div style="width: 100%">
                <span
                  class="downStyle"
                  v-for="(item, index) in certBaseInfo.certificateAttachment"
                  :key="index"
                  @click="downAttachment(item)"
                  >{{ item.fileName }}</span
                >
              </div>
              <!-- <mt-common-uploader
                :key="certiUploadKey"
                :isSingleFile="false"
                :isView="true"
                type="line"
                v-model="certBaseInfo.certificateAttachment"
              ></mt-common-uploader> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">
          {{ $t('质量保证体系、代理证书、行业资质证书、从业资质证书') }}
        </div>
        <div class="b-info-content">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('应标相关行业资质证书') }}</div>
            <div class="info-content-left">
              <div style="width: 100%">
                <span
                  class="downStyle"
                  v-for="(item, index) in certBaseInfo.industryCertificate"
                  :key="index"
                  @click="downAttachment(item)"
                  >{{ item.fileName }}</span
                >
              </div>
            </div>
            <div class="info-title epls">
              {{ $t('应标相关行业人员从业资质证书') }}
            </div>
            <div class="info-content-right" style="width: 52%">
              <div style="width: 100%">
                <span
                  class="downStyle"
                  v-for="(item, index) in certBaseInfo.industryStaffCertificate"
                  :key="index"
                  @click="downAttachment(item)"
                  >{{ item.fileName }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">
          {{ $t('质量保证体系及代理认证情况-其他') }}
        </div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="certificateInfo"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('代理商信息维护') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="patentInfo"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('专利信息') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="InnovationAwards"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('创新奖项') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="templateConfig"
          ></mt-template-page>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  certificateInfoColumn,
  patentInfoColumn,
  InnovationAwardsColumn,
  templateColumn
} from '../config/index'
export default {
  props: {
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      certBaseInfo: {}, //三证信息
      dataArr: [],
      certificateInfo: [
        {
          gridId: '6e81bcbf-d98b-4aa0-ba03-463cddf4534e',
          // toolbar: {
          //   useBaseConfig: false,
          //   tools: [
          //     [
          //       { id: "add", icon: "icon_table_new", title: "新增" },
          //       { id: "edit", icon: "icon_table_edit", title: "编辑" },
          //       { id: "deleted", icon: "icon_table_delete", title: "删除" },
          //     ],
          //     ["Filter", "export", "Refresh", "Setting"],
          //   ],
          // },
          grid: {
            allowPaging: false,
            columnData: [],
            dataSource: []
          }
        }
      ],
      patentInfo: [
        {
          // gridId: 'ea20e789-bf58-47e1-bb00-afed120addc2',
          grid: {
            allowPaging: false,
            columnData: patentInfoColumn,
            dataSource: []
          }
        }
      ],
      InnovationAwards: [
        {
          gridId: 'a2bca1b3-f87c-46a4-97eb-4d3ddf259ffa',
          grid: {
            allowPaging: false,
            columnData: InnovationAwardsColumn,
            dataSource: []
          }
        }
      ],
      templateConfig: [
        {
          gridId: '5a43bddf-e641-45e7-8b8a-88cd33933c77',
          grid: {
            allowPaging: false,
            columnData: templateColumn,
            dataSource: []
          }
        }
      ],
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      certificateList: [],
      acceptMsgTypeOptions: []
    }
  },
  async mounted() {
    await this.getAcceptMsgTypeOptions()
    this.searchCertificate()
    this.getEnterpriseNature('certificateType')
  },
  methods: {
    async getAcceptMsgTypeOptions() {
      this.$API.infoChange['queryDict']({
        dictCode: 'acceptMsgType'
      }).then((res) => {
        const { code, data } = res
        if (code == 200 && data) {
          this.acceptMsgTypeOptions = data
        }
      })
    },
    getEnterpriseNature(dictCode) {
      this.$loading()
      this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'certificateType') {
              this.certificateList = data
              this.$set(
                this.certificateInfo[0].grid,
                'columnData',
                certificateInfoColumn(this.certificateList)
              )
            }
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    downAttachment(item) {
      this.$utils.downloadAttachment(item)
    },
    checkDate(e) {
      if (e) {
        if (e == 0) {
          return (e = '')
        } else if (typeof e == 'object') {
          return this.$utils.formateTime(e, 'yyyy-MM-dd')
        } else if (typeof e == 'string') {
          let val = parseInt(e)
          return this.$utils.formateTime(val, 'yyyy-MM-dd')
        } else {
          return e
        }
      } else {
        return e
      }
    },
    searchCertificate() {
      let obj = {
        partnerArchiveId: this.partnerArchiveId,
        orgId: this.orgId,
        partnerRelationCode: this.partnerRelationCode,
        supplierEnterpriseId: this.supplierEnterpriseId
      }
      this.$loading()
      this.$API.supplierProfile
        .searchCertificate(obj)
        .then((res) => {
          this.$hloading()

          this.certBaseInfo = res.data.baseInfoExtDTO

          let qualityList = [] //质量保证
          let agentList = [] //代理
          let patentList = [] //专利
          let awardList = [] //创新
          res.data.certificateDTOList.forEach((item) => {
            // 3专利，2创新奖项，1质量证书，4代理商
            if (item.certType == 1) {
              qualityList.push(item)
            } else if (item.certType == 4) {
              agentList.push(item)
            } else if (item.certType == 3) {
              patentList.push(item)
            } else if (item.certType == 2) {
              awardList.push(item)
            }
          })
          agentList.forEach((item) => {
            item.originAcceptMsg = item.originAcceptMsg ? JSON.parse(item.originAcceptMsg) : []
            item.originAcceptMsg = item.originAcceptMsg.map((v) => {
              const matchedItem = this.acceptMsgTypeOptions.find((ele) => ele.itemCode === v)
              return matchedItem ? matchedItem.itemName : null
            })
          })
          this.certificateInfo[0].grid = Object.assign({}, this.certificateInfo[0].grid, {
            dataSource: qualityList
          })
          this.patentInfo[0].grid = Object.assign({}, this.patentInfo[0].grid, {
            dataSource: agentList.map((item) => {
              return {
                ...item,
                agentCategoryInfo: JSON.parse(item.agentCategoryInfo)?.join(','),
                originAcceptMsg: item.originAcceptMsg.join(',')
              }
            })
          })
          this.InnovationAwards[0].grid = Object.assign({}, this.InnovationAwards[0].grid, {
            dataSource: patentList
          })
          this.templateConfig[0].grid = Object.assign({}, this.templateConfig[0].grid, {
            dataSource: awardList
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 85%;
            padding: 5px 10px;
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            .downStyle {
              cursor: pointer;
              color: #00469c;
              margin: 0 5px;
            }
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-word;
            .downStyle {
              cursor: pointer;
              color: #00469c;
              margin: 0 5px;
            }
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
