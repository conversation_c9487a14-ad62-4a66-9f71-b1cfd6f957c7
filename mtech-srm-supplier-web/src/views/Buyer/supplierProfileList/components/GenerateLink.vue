<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :height="270"
    :width="500"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="link" :label="$t('供方注册链接（有效期为24小时）')">
          <mt-input v-model="formObject.link" :readonly="true"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('复制链接') }
        }
      ],
      //v-model获取的值
      formObject: {
        link: ''
      },
      formRules: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    registerInvalidKey() {
      return this.modalData.registerInvalidKey
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    // 获取当前环境 (需确保项目已配置环境变量)
    const env = process.env.NODE_ENV || 'development'

    // 定义不同环境的基础URL
    const baseUrls = {
      development: 'https://srm-uat.eads.tcl.com/login/register',
      uat: 'https://srm-uat.eads.tcl.com/login/register',
      production: 'https://srm.tcl.com/login/register'
    }

    // 获取当前环境的URL
    const baseUrl = baseUrls[env] || baseUrls.development

    // 生成完整链接
    this.formObject.link = baseUrl + '?registerInvalidKey=' + this.registerInvalidKey
  },
  methods: {
    //点击确认
    confirm() {
      this.$emit('confirm-function', this.formObject.link)
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
