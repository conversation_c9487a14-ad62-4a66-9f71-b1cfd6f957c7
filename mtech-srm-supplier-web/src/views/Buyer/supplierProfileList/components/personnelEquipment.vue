<template>
  <div class="supplier-portrait">
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('基本信息') }}</div>
        <div class="b-info-content">
          <div class="info-line fbox">
            <div class="info-title">{{ $t('研发人员（数量）') }}</div>
            <div class="info-content-left">
              {{ staffBaseInfo.researcherNumber || '--' }}
            </div>
            <div class="info-title epls">
              {{ $t('从业5年以上研发人员（数量）') }}
            </div>
            <div class="info-content-right">
              {{ staffBaseInfo.oldResearcherNumber || '--' }}
            </div>
            <div class="info-title epls">
              {{ $t('专职供应链管理人员（数量）') }}
            </div>
            <div class="info-content-right">
              {{ staffBaseInfo.supplyManagerNumber || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('公司员工总数（数量）') }}</div>
            <div class="info-content-right">
              {{ staffBaseInfo.totalEmployeesNumber || '--' }}
            </div>
            <div class="info-title">{{ $t('技术和专业人员（数量）') }}</div>
            <div class="info-content-left">
              {{ staffBaseInfo.technologyNumber || '--' }}
            </div>
            <div class="info-title epls">
              {{ $t('技术和专业人员占总人数比率（%）') }}
            </div>
            <div class="info-content-right">
              {{ staffBaseInfo.technologyScale || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">
              {{ $t('实施、售后、运维人员数量') }}
            </div>
            <div class="info-content-right">
              {{ staffBaseInfo.maintenanceNumber || '--' }}
            </div>
            <div class="info-title">{{ $t('质量管理人员（数量）') }}</div>
            <div class="info-content-left">
              {{ staffBaseInfo.qualityManageNumber || '--' }}
            </div>
            <div class="info-title epls">{{ $t('管理人员（数量）') }}</div>
            <div class="info-content-right">
              {{ staffBaseInfo.managerNumber || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('生产人员（数量）') }}</div>
            <div class="info-content-right">
              {{ staffBaseInfo.producerNumber || '--' }}
            </div>
            <div class="info-title">{{ $t('FAE人员（数量）') }}</div>
            <div class="info-content-left">
              {{ staffBaseInfo.faePersonnelNumber || '--' }}
            </div>
            <div class="info-title epls">
              {{ $t('从业8年以上的FAE人员（数量）') }}
            </div>
            <div class="info-content-right">
              {{ staffBaseInfo.eightFaeNumber || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title epls">{{ $t('产品执行标准') }}</div>
            <div class="info-content-right">
              <mt-select
                :disabled="true"
                v-model="staffBaseInfo.productExecutiveStandard"
                :data-source="productList"
                :fields="stageTypeFields"
              ></mt-select>
            </div>
            <div class="info-title">{{ $t('工程人员（数量）') }}</div>
            <div class="info-content-left">
              {{ staffBaseInfo.engineerNumber || '--' }}
            </div>
            <div class="info-title epls">{{ $t('本科学历以上（%）') }}</div>
            <div class="info-content-right">
              {{ staffBaseInfo.bachelorDegreeProportion || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('产品认证状况') }}</div>
            <div class="textareaClass">
              {{ staffBaseInfo.productCertificationStatus || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('备注') }}</div>
            <div class="textareaClass">
              {{ staffBaseInfo.remark || '--' }}
            </div>
          </div>
          <div class="info-line fbox">
            <div class="info-title">{{ $t('附件') }}</div>
            <div class="textareaClass">
              <div style="width: 100%">
                <span
                  class="downStyle"
                  v-for="(item, index) in staffBaseInfo.staffAttachment"
                  :key="index"
                  @click="downAttachment(item)"
                  >{{ item.fileName }}</span
                >
              </div>
              <!-- <mt-common-uploader
                :key="comUploadKey"
                :isSingleFile="false"
                :isView="true"
                type="line"
                v-model="staffBaseInfo.staffAttachment"
              ></mt-common-uploader> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('管理信息系统') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="certificateInfo"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('生产设备') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="patentInfo"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('检测/试验设备') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="testInfo"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <div class="business-information fbox">
      <div class="b-info">
        <div class="b-info-title">{{ $t('主要客户') }}</div>
        <div class="infos">
          <mt-template-page
            ref="templateRef"
            :use-tool-template="false"
            :template-config="InnovationAwards"
          ></mt-template-page>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { managementInfomation, testColumn, equipment, salesQuantity } from '../config/index'
export default {
  props: {
    partnerArchiveId: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    supplierEnterpriseId: {
      type: String,
      default: ''
    },
    partnerRelationCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      staffBaseInfo: {},
      certificateInfo: [
        {
          gridId: 'dddfe9c9-c436-441f-b70f-30dd7216803d',
          // toolbar: {
          //   useBaseConfig: false,
          //   tools: [
          //     [
          //       { id: "add", icon: "icon_table_new", title: "新增" },
          //       { id: "edit", icon: "icon_table_edit", title: "编辑" },
          //       { id: "deleted", icon: "icon_table_delete", title: "删除" },
          //     ],
          //     ["Filter", "export", "Refresh", "Setting"],
          //   ],
          // },
          grid: {
            allowPaging: false,
            columnData: managementInfomation,
            dataSource: []
          }
        }
      ],
      patentInfo: [
        {
          gridId: '22e4992b-5d92-4314-a5eb-29b312997765',
          grid: {
            allowPaging: false,
            columnData: equipment,
            dataSource: []
          }
        }
      ],
      testInfo: [
        {
          gridId: '706b3d1f-175f-44de-a413-6f6d5200c4b8',
          grid: {
            allowPaging: false,
            columnData: testColumn,
            dataSource: []
          }
        }
      ],
      InnovationAwards: [
        {
          gridId: '2ab91974-fdd2-4d68-8221-5f21f91972a8',
          grid: {
            allowPaging: false,
            columnData: salesQuantity,
            dataSource: []
          }
        }
      ],
      stageTypeFields: {
        text: 'name',
        value: 'itemCode'
      },
      productList: []
    }
  },
  mounted() {
    this.searchDevice()
    this.getAllSelectList()
  },
  methods: {
    downAttachment(item) {
      this.$utils.downloadAttachment(item)
    },
    getAllSelectList() {
      // 产品执行标准
      this.getEnterpriseNature('productStandard')
    },
    getEnterpriseNature(dictCode) {
      this.$loading()
      this.$API.infoChange['queryDict']({
        dictCode
      })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200 && data) {
            if (dictCode == 'productStandard') {
              this.productList = data
            }
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    searchDevice() {
      let obj = {
        partnerArchiveId: this.partnerArchiveId,
        orgId: this.orgId,
        partnerRelationCode: this.partnerRelationCode,
        supplierEnterpriseId: this.supplierEnterpriseId
      }
      this.$loading()
      this.$API.supplierProfile
        .searchDevice(obj)
        .then((res) => {
          this.$hloading()
          this.staffBaseInfo = res.data.staffInfoDTO

          let systemList = []
          let prodDeviceList = []
          let testList = []
          // 1=系统，2=设备 3=检测设备
          res.data.deviceDTOList.forEach((item) => {
            if (item.deviceType == 1) {
              systemList.push(item)
            } else if (item.deviceType == 2) {
              prodDeviceList.push(item)
            } else if (item.deviceType == 3) {
              testList.push(item)
            }
          })
          this.certificateInfo[0].grid = Object.assign({}, this.certificateInfo[0].grid, {
            dataSource: systemList
          })
          this.patentInfo[0].grid = Object.assign({}, this.patentInfo[0].grid, {
            dataSource: prodDeviceList
          })
          this.testInfo[0].grid = Object.assign({}, this.testInfo[0].grid, {
            dataSource: testList
          })
          this.InnovationAwards[0].grid = Object.assign({}, this.InnovationAwards[0].grid, {
            dataSource: res.data.cooperatorDTOList
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-portrait {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;

  .access-process-warp {
    padding-top: 30px;
  }

  .swiper-box {
    padding-top: 30px;
  }

  .business-information:last-child {
    border-bottom: none;
  }
  .business-information {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 30px;
    justify-content: space-between;
    align-items: stretch;
    .b-info-title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 16px;
      font-size: 16px;
      color: rgba(41, 41, 41, 1);
      padding-left: 14px;
      margin-top: 10px;

      .update-msg {
        position: absolute;
        right: 40px;

        .update-time {
          font-size: 12px;
          color: rgba(154, 154, 154, 1);
          margin-right: 10px;
        }
        .update {
          font-size: 12px;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
      }

      &::before {
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #00469c;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .b-info {
      width: 100%;

      .b-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);

        .info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .textareaClass {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 85%;
            padding: 5px 10px;
            .downStyle {
              cursor: pointer;
              color: #00469c;
              margin: 0 5px;
            }
          }

          .info-content-left {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 42px;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }

          .info-content-right {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 18%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
          }
        }

        .spc-info-line {
          width: 100%;
          min-height: 50px;
          line-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid #e8e8e8;
          align-items: stretch;

          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 15%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
          }

          .info-content-expand {
            color: rgba(41, 41, 41, 1);
            padding-left: 20px;
            padding-right: 42px;
            overflow: auto;
            position: relative;
            display: flex;

            .txt-wrap {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .is-expand {
              line-height: 24px;
              overflow: auto;
              text-overflow: inherit;
              white-space: inherit;
              padding: 10px 0;
            }

            .expand-icon {
              height: 12px;
              line-height: 12px;
              font-size: 12px;
              color: #00469c;
              cursor: pointer;
              margin-top: 18px;
            }

            .rotate {
              transform: rotate(180deg);
            }
          }
        }

        .info-item {
          margin-top: 14px;

          .info-title {
            height: 14px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(154, 154, 154, 1);
            margin-bottom: 10px;
          }

          .info-desc {
            font-size: 20px;
            color: rgba(41, 41, 41, 1);

            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }

          .expand {
            overflow: auto;
          }

          .bold-weight {
            font-weight: 600;
          }

          .ligth-weight {
            font-weight: normal;
          }

          .sm-desc {
            font-size: 16px;
          }

          .blue {
            color: #00469c;
          }
        }
      }

      .echar-wrap {
        height: 500px;
      }

      .b-content-corp {
        .performance-box {
          width: 100%;
          height: 250px;
        }
      }
      .infos {
        width: 100%;
        height: 300px;
      }
    }
    .radar-title {
      width: 100%;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
    }

    .b-relation {
      width: 38%;
      position: relative;

      .switch-btn {
        width: 160px;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        position: relative;
        bottom: 10px;

        i {
          transform: rotate(90deg);
        }
      }

      .radar-box {
        width: 100%;
        height: 250px;
        margin-top: 20px;
      }

      .table-box {
        border-bottom: 1px solid #ddd;
        height: 250px;
        overflow: auto;
      }
    }
  }
}
</style>
