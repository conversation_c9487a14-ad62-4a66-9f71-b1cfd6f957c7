<template>
  <div class="bid-box fbox">
    <div class="svg-box flex1">
      <svg>
        <circle class="circle1" cx="115" cy="115" r="105" fill="none" stroke-width="12"></circle>
        <!-- 动态的 -->
        <circle
          class="circle2"
          cx="115"
          cy="115"
          r="105"
          fill="none"
          stroke-width="12"
          :stroke-dashoffset="rate2"
        ></circle>

        <circle class="circle3" cx="116" cy="116" r="84" fill="none" stroke-width="12"></circle>
        <!-- 动态的 -->
        <circle
          class="circle4"
          cx="116"
          cy="116"
          r="84"
          fill="none"
          stroke-width="12"
          :stroke-dashoffset="rate1"
        ></circle>
      </svg>
    </div>

    <div class="text-info">
      <div class="total-info">
        <div class="info-value">{{ getRecordBiddingList.joinTenderNumber }}</div>
        <div class="info-title">{{ $t('累计报名') }}</div>
      </div>
      <template
        v-if="
          !!getRecordBiddingList.biddingStageDTOList &&
          getRecordBiddingList.biddingStageDTOList.length > 0
        "
      >
        <div
          v-for="(item, index) in getRecordBiddingList.biddingStageDTOList"
          class=""
          :class="{ 'getin-info': index % 2 === 0, 'geton-info': index % 2 !== 0 }"
          :key="item.stageName"
        >
          <div class="getin-value">{{ item.count }}%</div>
          <div class="getin-title">{{ item.stageName }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  data() {
    return {
      rate1: 528,
      rate2: 706
    }
  },
  props: {
    getRecordBiddingList: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    getRecordBiddingList: {
      handler(nv) {
        if (!utils.isEmpty(nv)) {
          this.initCalcuteWidth(nv)
        }
      },
      deep: true
    }
  },
  methods: {
    initCalcuteWidth(getRecordBiddingList) {
      if (
        !!getRecordBiddingList.biddingStageDTOList &&
        !utils.isEmpty(getRecordBiddingList.biddingStageDTOList)
      ) {
        let count1 =
          !!getRecordBiddingList.biddingStageDTOList[0] &&
          getRecordBiddingList.biddingStageDTOList[0].count
            ? getRecordBiddingList.biddingStageDTOList[0].count
            : 0
        let count2 =
          !!getRecordBiddingList.biddingStageDTOList[1] &&
          getRecordBiddingList.biddingStageDTOList[1].count
            ? getRecordBiddingList.biddingStageDTOList[1].count
            : 0

        let rate2 = 706 - Math.floor((471 * count2) / 100)
        let rate1 = 528 - Math.floor((352 * count1) / 100)
        // console.log(rate2, rate1)
        this.rate2 = rate2
        this.rate1 = rate1
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bid-box {
  position: relative;

  .text-info {
    padding-right: 20%;
    text-align: right;

    .total-info {
      .info-value {
        height: 42px;
        line-height: 42px;
        font-size: 36px;
        font-weight: bold;
        color: rgba(237, 86, 51, 1);
      }

      .info-title {
        height: 22px;
        line-height: 22px;
        font-size: 16px;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
        margin-top: 4px;
      }
    }

    .getin-info {
      margin-top: 40px;

      .getin-value {
        height: 28px;
        line-height: 28px;
        font-size: 24px;

        font-weight: bold;
        color: #00469c;
      }

      .getin-title {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #9a9a9a;
        margin-top: 3px;
      }
    }

    .geton-info {
      margin-top: 10px;

      .getin-value {
        height: 28px;
        line-height: 28px;
        font-size: 24px;
        font-weight: bold;
        color: #33a617;
      }

      .getin-title {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #9a9a9a;
        margin-top: 3px;
      }
    }
  }

  svg {
    display: block;
    width: 250px;
    height: 200px;
    margin: 0 auto;
    margin-top: 30px;
  }

  .circle1 {
    fill: none;
    stroke: #e8e8e8;
    stroke-width: 12;
    stroke-dasharray: 706; // 直径 225 * 3.14
    stroke-dashoffset: 235; // 1/3
    stroke-linecap: round;
    transform: rotate(141deg);
    transform-origin: center;
    transform-box: fill-box;
  }

  .circle2 {
    fill: none;
    stroke: #33a617;
    stroke-width: 12;
    stroke-dasharray: 706;

    stroke-linecap: round;
    transform: rotate(141deg);
    transform-origin: center;
    transform-box: fill-box;
  }

  .circle3 {
    fill: none;
    stroke: #e8e8e8;
    stroke-width: 12;
    stroke-dasharray: 528;
    stroke-dashoffset: 176;
    stroke-linecap: round;
    transform: rotate(150deg);
    transform-origin: center;
    transform-box: fill-box;
  }

  .circle4 {
    fill: none;
    stroke: #00469c;
    stroke-width: 12;
    stroke-dasharray: 528;
    // stroke-dashoffset: 400;
    stroke-linecap: round;
    transform: rotate(150deg);
    transform-origin: center;
    transform-box: fill-box;
  }
}
</style>
