<template>
  <div class="lifeCycle-container">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnDataMain } from './config/index.js'
import axios from 'axios'
export default {
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$hloading()
    })
  },
  data() {
    return {
      componentConfig: [
        {
          gridId: 'eccc6abd-5b16-4334-9887-6d5375a6ddad',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                // { id: "exit", icon: "icon_table_new", title: this.$t("退出") },
                { id: 'maintain', icon: 'icon_table_edit', title: this.$t('集团供应商维护') },
                { id: 'generateLink', icon: '', title: this.$t('生成供方注册链接') }
                // {
                //   id: "freeze",
                //   icon: "icon_table_delete",
                //   title: this.$t("冻结"),
                // },
                // {
                //   id: "enable",
                //   icon: "icon_card_invite",
                //   title: this.$t("拉黑"),
                // },
                // // {
                // //   id: "thaw",
                // //   icon: "icon_card_invite",
                // //   title: this.$t("解冻"),
                // // },
                // // {
                // //   id: "remove",
                // //   icon: "icon_card_invite",
                // //   title: this.$t("移除黑名单"),
                // // },
              ],
              //
              [
                'Filter',
                // { id: 'importData', icon: 'icon_solid_Import', title: this.$t('导入') },
                { id: 'exportData', icon: 'icon_solid_export', title: this.$t('导出') },
                'Refresh',
                'Setting'
              ]
            ]
          },
          grid: {
            columnData: columnDataMain,
            asyncConfig: {
              url: '/supplier/tenant/buyer/process/perspective/query',
              serializeList: (list) => {
                if (list.length > 0) {
                  list.forEach((item) => {
                    let text = ''
                    if (item.supplyAddress) {
                      text = JSON.parse(item?.supplyAddress)
                        ?.map((v) => {
                          return this.supplyAddressOptions.find((e) => e.itemCode === v)?.itemName
                        })
                        .join(',')
                    }
                    item.supplyAddressText = text
                  })
                }
                return list
              }
            },
            frozenColumns: 3
          }
        }
      ],
      supplyAddressOptions: []
    }
  },
  created() {
    this.getOptions()
  },
  methods: {
    getOptions() {
      this.$API.infoChange['queryDict']({
        dictCode: 'supply_area'
      }).then((res) => {
        if (res.code) {
          this.supplyAddressOptions = res.data
        }
      })
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()

      if (e.toolbar.id == 'generateLink') {
        this.handleGenerateLink()
      }

      if (e.toolbar.id == 'maintain') {
        if (_selectGridRecords.length < 1) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        if (_selectGridRecords.length > 1) {
          this.$toast({ content: this.$t('只能选择一行'), type: 'warning' })
          return
        }
      }
      if (e.toolbar.id == 'maintain') {
        this.$dialog({
          modal: () => import('./components/maintainDialog.vue'),
          data: {
            title: this.$t('集团供应商维护'),
            data: _selectGridRecords[0]
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          },
          close: () => {}
        })
      }
      let _this = this
      let id = e.toolbar.id
      let records = e.data ? [e.data] : e.gridRef.getMtechGridRecords()
      if (id == 'importData') {
        this.$dialog({
          modal: () => import('./components/importComp.vue'),
          data: {
            title: this.$t('导入'),
            type: 'importData'
          },
          success: () => {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          },
          close: () => {}
        })
      } else {
        // if (e.gridRef.getMtechGridRecords().length == 1) {
        if (id == 'exit') {
          //退出
          this.$dialog({
            modal: () => import('../supplierDevelopment/supplierExit/components/addDialog.vue'),
            data: {
              title: this.$t('新建退出单'),
              isEdit: false
            },
            success: (data) => {
              console.log(data)
              if (data === 'reload') {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            }
          })
        } else if (id == 'edit') {
          //整改
          this.$dialog({
            modal: () =>
              import('../RectifyManagement/components/toBeRectified/components/addDialog.vue'),
            data: {
              title: this.$t('新建整改单')
            },
            success: () => {
              _this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else if (id == 'freeze') {
          //冻结
          let arr = e.gridRef.getMtechGridRecords()
          if (arr.length <= 0) {
            this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
            return
          }
          if (arr.length > 1) {
            this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
            return
          }
          this.$dialog({
            modal: () => import('../supplierDevelopment/supplierFreeze/components/addDialog.vue'),
            data: {
              title: this.$t('新建冻结单'),
              isEdit: false,
              obj: {
                orgId: arr[0].orgId,
                applyType: 'punish',
                supplierEnterpriseId: arr[0].supplierEnterpriseId
              }
            },
            success: (data) => {
              console.log(data)
              if (data === 'reload') {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            }
          })
        } else if (id == 'enable') {
          //拉黑
          this.$dialog({
            modal: () =>
              import('../supplierDevelopment/supplierBlackList/components/addDialog.vue'),
            data: {
              title: this.$t('新建黑名单'),
              isEdit: false
            },
            success: (data) => {
              console.log(data)
              if (data === 'reload') {
                _this.$refs.templateRef.refreshCurrentGridData()
              }
            }
          })
        } else if (id == 'exportData') {
          //导出
          // this.$refs.templateRef.getCurrentTabRef().grid.excelExport();
          let idList = []
          records.forEach((item) => {
            idList.push(item.id)
          })
          let params = this.$refs.templateRef.getAsyncParams()
          params['ids'] = idList
          this.$loading()
          axios
            .post('/api/supplier/tenant/buyer/process/archive/excel/export', params, {
              responseType: 'blob' // 1.首先设置responseType对象格式为 blob:
            })
            .then((res) => {
              this.$hloading()
              // console.log(res); //把response打出来，看下图
              let blob = new Blob([res.data], {
                type: 'application/x-msdownload'
              })
              // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
              let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象

              // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
              let a = document.createElement('a')
              a.href = url
              a.download = '供应商档案管理.xlsx'
              a.click()
              // 5.释放这个临时的对象url
              window.URL.revokeObjectURL(url)
            })
            .catch(() => {
              this.$hloading()
              this.$toast({ content: '导出失败，请重试!', type: 'warning' })
            })
        }
        // else if (id == "thaw") {//解冻
        //   this.$dialog({
        //     modal: () => import("../supplierDevelopment/supplierFreeze/components/addDialog.vue"),
        //     data: {
        //       title: this.$t("新建解冻单"),
        //       isEdit: false,
        //     },
        //     success: (data) => {
        //       console.log(data);
        //       if (data === "reload") {
        //         _this.$refs.templateRef.refreshCurrentGridData();
        //       }
        //     },
        //   });
        // }else if (id == "remove") {//移除黑名单
        //   this.$dialog({
        //     modal: () => import("../supplierDevelopment/supplierBlackList/components/addDialog.vue"),
        //     data: {
        //       title: this.$t("移除黑名单"),
        //       isEdit: false,
        //     },
        //     success: (data) => {
        //       console.log(data);
        //       if (data === "reload") {
        //         _this.$refs.templateRef.refreshCurrentGridData();
        //       }
        //     },
        //   });
        // }
        // } else {
        //   this.$toast({
        //     content: this.$t("请选择一条数据进行操作"),
        //     type: "warning",
        //   });
        // }
      }
    },
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'profileDetail',
        query: {
          partnerArchiveId: data.partnerArchiveId,
          orgId: data.orgId,
          supplierEnterpriseId: data.supplierEnterpriseId,
          partnerRelationCode: data.partnerRelationCode,
          status: data.status,
          supplierInternalCode: data.supplierInternalCode
        }
      })
    },
    async handleGenerateLink() {
      try {
        const res = await this.$API.supplierProfile.createKeyApi()
        if (res.code === 200 && res.data) {
          this.$dialog({
            modal: () => import('./components/GenerateLink.vue'),
            data: {
              title: this.$t('链接'),
              registerInvalidKey: res.data
            },
            success: (data) => {
              navigator.clipboard
                .writeText(data)
                .then(() => {
                  this.$toast({ content: this.$t('复制成功'), type: 'success' })
                })
                .catch((err) => {
                  console.error('复制失败:', err)
                  this.$toast({ content: this.$t('复制失败'), type: 'warning' })
                })
            }
          })
        }
      } catch (error) {
        console.error('Failed to generate link:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.lifeCycle-container {
  width: 100%;
  height: 100%;
}
</style>
