import { i18n } from '@/main.js'

// 是否启用枚举
const Rohs2Options = [
  { text: i18n.t('启用'), value: 1 },
  { text: i18n.t('未启用'), value: 0 }
]
// 状态枚举
const StatusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('生效'), value: 1 },
  { text: i18n.t('失效'), value: 2 }
]

// 数据来源
const DataSourceOptions = [
  { text: i18n.t('手工新增'), value: 0 },
  { text: i18n.t('系统自动生成'), value: 1 }
]

export { Rohs2Options, StatusOptions, DataSourceOptions }
