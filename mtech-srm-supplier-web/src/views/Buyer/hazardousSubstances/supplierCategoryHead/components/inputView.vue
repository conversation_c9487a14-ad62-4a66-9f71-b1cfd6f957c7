<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
      @keyup="value = value.replace(/^(0+)|[^\d]+/g, '')"
      @beforepaste="
        clipboardData.setData('text', clipboardData.getData('text').replace(/[^\d]/g, ''))
      "
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: false }
  },
  mounted() {
    if (this.data.column.field === 'categoryName') {
      this.disabled = true
      this.$bus.$on('maintainOutsideItemcategoryCode', (val) => {
        this.data.categoryName = val.categoryName
      }) //接受品类描述
    }
    if (this.data.column.field === 'minSupplierQuality') {
      this.$bus.$on('maintainOutsideItemcategoryCode', (val) => {
        this.data.categoryName = val.categoryName
      }) //接受品类描述
    }
    if (this.data.column.field === 'supplierName') {
      this.disabled = true
      this.$bus.$on('supplierCodeChange', (val) => {
        this.data.supplierName = val.itemData.supplierName
      })
    }
    if (this.data.column.field === 'companyCode') {
      this.disabled = true
    }
    if (this.data.column.field === 'companyName') {
      this.disabled = true
      this.$bus.$on('companyCodeChange', (val) => {
        this.data.companyName = val.itemData.companyName
      })
    }
    if (this.data.column.field === 'sqeName') {
      this.$bus.$on('sqeAcctChange', (val) => {
        this.data.sqeName = val.itemData.employeeName
      })
    }
    if (this.data.column.field === 'approverName') {
      this.$bus.$on('approverChange', (val) => {
        this.data.approverName = val.itemData.employeeName
      })
    }
  },
  methods: {
    onInput(e) {
      console.log('chile ma ', e)
      const res = e.replace(/[^\d.]/g, '')
      return res
    }
  }
}
</script>
