<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :filtering="filtering"
      :disabled="data.id && data.column.field == 'categoryName'"
      :allow-filtering="true"
      :popup-width="350"
    ></mt-select>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import { StatusOptions, Rohs2Options } from './enum'
import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: 'text', value: 'value' },

      dataSource: [],

      userArrList: []
    }
  },

  mounted() {
    console.log('moutedmoutedmoutedmoutedmouted', this.data)
    this.filtering = utils.debounce(this.filtering, 1000)
    const _enum = [
      'isEffective',
      'euRoHs',
      'euReachSvHc',
      'reachFile',
      'germanyPaHs',
      'usCalifornia65',
      'batteryDirective',
      'euPackagingDirective',
      'chinaVoc',
      'frenchMineralOil',
      'pfas',
      'pops',
      'usTsca'
    ]
    if (this.data.column.field === 'companyType') {
      this.getCompanyType()
    }

    if (this.data.column.field === 'companyCode') {
      this.getCompany()
    }

    //品类编码
    if (this.data.column.field === 'categoryCode') {
      this.getCategoryCode({ text: null })
    }
    if (this.data.column.field === 'supplierCode') {
      this.getSupplierList({ text: '' })
    } else if (['sqeAcct', 'firstApproverAcct'].includes(this.data.column.field)) {
      this.getPersonList({ text: null })
    }
    if (_enum.includes(this.data.column.field)) {
      this.dataSource = Rohs2Options
    } else if (this.data.column.field === 'isEffective') {
      this.dataSource = StatusOptions
    }
  },

  methods: {
    getCompanyType() {
      this.dataSource = [
        { text: this.$t('空调'), value: 'KT' },
        { text: this.$t('万颗子'), value: 'WKZ' },
        { text: this.$t('白电'), value: 'BD' }
      ]
    },
    getCompany() {
      this.$API.masterData
        .getCompanyList()
        .then((res) => {
          if (res.code === 200) {
            let _data = res.data.map((item) => {
              return {
                text: item.companyCode + '-' + item.companyName,
                value: item.companyCode,
                companyName: item.companyName
              }
            })
            this.dataSource = _data
          }
        })
        .catch(() => {})
    },
    getCategoryCode(e) {
      this.$loading()
      let params = {
        fuzzyNameOrCode: e.text || this.data.categoryCode
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.dataSource = _data
          this.fields = { text: 'text', value: 'categoryCode' }
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    getSupplierList(e) {
      this.$loading()
      const param = {
        fuzzyNameOrCode: e.text || this.data.supplierCode
      }
      this.$API.masterData
        .getSupplier(param)
        .then((res) => {
          let _data = res.data.map((item) => {
            return {
              text: item.supplierCode + '-' + item.supplierName,
              value: item.supplierCode,
              supplierName: item.supplierName
            }
          })
          this.dataSource = _data
        })
        .catch((e) => {
          this.$toast({
            type: 'error',
            content: e.msg || this.$t('获取供应商数据失败')
          })
        })
        .finally(() => {
          this.$hloading()
        })
    },
    getPersonList(e) {
      this.$loading()
      let params = {
        fuzzyName: e.text || this.data[this.data.column.field],
        orgLevelCode: 'ORG05',
        orgType: 'ORG001ADM'
      }
      this.$API.categoryRelationshipReport
        .getBuyerList(params)
        .then((res) => {
          if (res.code == 200 && res.data != null) {
            const userArrList = res.data
            const newArr = userArrList.concat(this.userArrList)
            let map = new Map()
            for (let item of newArr) {
              map.set(item.accountName, item)
            }
            this.dataSource = [...map.values()]
            this.fields = { text: 'employeeName', value: 'accountName' }
          }
          this.$nextTick(() => {
            this.data[this.data.column.field] = this.data[this.data.column.field]
          })
        })
        .catch((error) => {
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
        .finally(() => {
          this.$hloading()
        })
    },
    // 模糊搜索
    filtering(e) {
      if (this.data.column.field === 'categoryCode') {
        this.getCategoryCode(e)
      } else if (this.data.column.field === 'supplierCode') {
        this.getSupplierList(e)
      } else if (['sqeAcct', 'firstApproverAcct'].includes(this.data.column.field)) {
        this.getPersonList(e)
      }
    },

    startOpen() {
      console.log(this.data, '下拉打开时最新的行数据')
    },

    selectChange(e) {
      if (this.data.column.field === 'categoryCode') {
        let params = {
          fieldCode: 'categoryName',
          itemInfo: {
            categoryId: e.itemData.id,
            categoryCode: e.itemData.categoryCode,
            categoryName: e.itemData.categoryName
          }
        }
        this.$parent.$emit('parentcategoryCode', params)
        this.$bus.$emit('maintainOutsideItemcategoryCode', e.itemData)
      } else if (this.data.column.field === 'supplierCode') {
        this.$bus.$emit('supplierCodeChange', e)
        this.data.supplierName = e.itemData.supplierName
      } else if (this.data.column.field === 'sqeAcct') {
        this.$bus.$emit('sqeAcctChange', e)
      } else if (this.data.column.field === 'firstApproverAcct') {
        this.$bus.$emit('approverChange', e)
      } else if (this.data.column.field === 'companyCode') {
        this.$bus.$emit('companyCodeChange', e)
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('crrEnterpriseNamebus')
    this.$bus.$off('crrCategoryCodebus')
    this.$bus.$off('crrCategoryTypebus')
    this.$bus.$off('maintainOutsideItemcategoryCode')
  }
}
</script>
