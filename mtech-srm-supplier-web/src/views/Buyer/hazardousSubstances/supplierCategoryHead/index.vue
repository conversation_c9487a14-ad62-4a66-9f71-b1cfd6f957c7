<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
      @parentcategoryCode="parentcategoryCode"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="status" :label="$t('状态')">
            <mt-select
              v-model="searchFormModel.status"
              :data-source="statusOptions"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="companyType" :label="$t('公司类型')">
            <mt-select
              v-model="searchFormModel.companyType"
              :data-source="[
                { text: $t('空调'), value: 'KT' },
                { text: $t('万颗子'), value: 'WKZ' },
                { text: $t('白电'), value: 'BD' }
              ]"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司')">
            <RemoteAutocomplete
              v-model="searchFormModel.companyCode"
              url="/masterDataManagement/tenant/organization/specified-level-paged-query"
              :placeholder="$t('请选择')"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              :params="{
                organizationLevelCodes: ['ORG01', 'ORG02']
              }"
              multiple
              @change="companyChange"
            />
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
            <mt-input
              v-model="searchFormModel.supplierCode"
              :show-clear-button="true"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierName" :label="$t('供应商名称')">
            <mt-input
              v-model="searchFormModel.supplierName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierContactUserMailbox" :label="$t('供方接收信息邮箱')">
            <mt-input
              v-model="searchFormModel.supplierContactUserMailbox"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="categoryCode" :label="$t('品类编码')">
            <mt-input
              v-model="searchFormModel.categoryCode"
              :show-clear-button="true"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="categoryName" :label="$t('品类名称')">
            <mt-input
              v-model="searchFormModel.categoryName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="sqeAcct" :label="$t('SQE负责人账号')">
            <mt-input
              v-model="searchFormModel.sqeAcct"
              :show-clear-button="true"
              :placeholder="$t('仅支持单个精准查询')"
            />
          </mt-form-item>
          <mt-form-item prop="sqeName" :label="$t('SQE负责人姓名')">
            <mt-input
              v-model="searchFormModel.sqeName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="firstApproverAcct" :label="$t('审核人账号')">
            <mt-input
              v-model="searchFormModel.firstApproverAcct"
              :show-clear-button="true"
              :placeholder="$t('仅支持单个精准查询')"
            />
          </mt-form-item>
          <mt-form-item prop="approverName" :label="$t('审核人姓名')">
            <mt-input
              v-model="searchFormModel.approverName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="dataSource" :label="$t('数据来源')">
            <mt-select
              v-model="searchFormModel.dataSource"
              :data-source="dataSourceOptions"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item :label="$t('创建人')" prop="createUserName">
            <mt-input
              v-model="searchFormModel.createUserName"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建时间')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :placeholder="$t('请选择日期')"
              :show-clear-button="true"
              :change="(e) => dateTimeChange(e, 'createTime')"
            />
          </mt-form-item>
          <mt-form-item :label="$t('更新人')" prop="updateUserName">
            <mt-input
              v-model="searchFormModel.updateUserName"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="updateTime" :label="$t('更新时间')">
            <mt-date-range-picker
              v-model="searchFormModel.updateTime"
              :placeholder="$t('请选择日期')"
              :show-clear-button="true"
              :change="(e) => dateTimeChange(e, 'updateTime')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { toolbar, columnData } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils.js'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { StatusOptions, DataSourceOptions } from './components/enum.js'
import cloneDeep from 'lodash/cloneDeep'
import utils from '@/utils/utils'

export default {
  components: {
    RemoteAutocomplete
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      statusOptions: StatusOptions,
      dataSourceOptions: DataSourceOptions,

      tempPersonList: [],
      categoryCodeObj: {},
      pageConfig: [
        {
          isUseCustomSearch: true, // 是否使用自定义查询
          isCustomSearchRules: true,
          // isUseCustomEditor: true,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar,
          gridId: '2ff90fce-e5c3-4776-b013-5927b52cfa5d',
          grid: {
            allowPaging: true, // 是否使用内置分页器
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData,
            asyncConfig: {
              url: '/supplier/tenant/supplier/category/director/query'
            }
          }
        }
      ],

      categoryCodeENUM: [] //品类数据相关值集
    }
  },
  computed: {},
  mounted() {
    this.filtering = utils.debounce(this.filtering, 1000)
    this.getCategoryInfos()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    // 获取数据
    async init() {
      const params = {
        page: { current: 1, size: 10000 }
      }
      const res = await this.$API.categoryConfigurationReport.getPageInfo(params)

      res.data.records = res.data.records.map((item) => {
        const personConfig = JSON.parse(item.personConfig)
        for (let key in personConfig) {
          const curFieldName = key.replace('_NAME', '')
          item[curFieldName] = personConfig[key]
        }
        const templateConfig = JSON.parse(item.templateConfig)
        for (let key in templateConfig) {
          item[key] = templateConfig[key]
        }
        return item
      })
      console.log('resresres', res)
      this.pageConfig[0].grid.dataSource = res.data.records
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 获取品类相关数据
    getCategoryInfos() {
      this.$API.maintainOutsideItem
        .criteriaQuery()
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.categoryCodeENUM = _data
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    // 模糊搜索品类
    filtering(e) {
      this.$loading()
      let params = {
        fuzzyNameOrCode: e.text
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          res.data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          e.updateData(res.data)
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    // 品类下拉
    selectCategory(e) {
      const { itemData } = e
      this.searchFormModel.categoryName = itemData.categoryName
    },
    // 同步数据
    parentcategoryCode(val) {
      if (val.fieldCode === 'categoryName') {
        Object.assign(this.categoryCodeObj, val.itemInfo || {})
      } else if (this.tempPersonList === null) {
        this.tempPersonList = [val]
      } else if (this.tempPersonList.some((item) => item.fieldCode === val.fieldCode)) {
        const index = this.tempPersonList.findIndex((item) => item.fieldCode === val.fieldCode)
        this.tempPersonList.splice(index, 1)
        this.tempPersonList.push(val)
      } else {
        this.tempPersonList.push(val)
      }
      console.log(
        'parentcategoryCodeparentcategoryCodeparentcategoryCodeparentcategoryCode',
        this.tempPersonList
      )
    },
    commandClick(args) {
      if (args.commandColumn.type === 'Deletes') {
        // 如果不存在id,直接删除(获取不到rowIndex,暂定刷新)
        if (!args.rowData.id) {
          this.$refs.tepPage.refreshCurrentGridData()
          return
        }
        let params = {
          id: args.rowData.id
        }
        this.$API.categoryRelationshipReport.remove(params).then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
        })
      } else if (args.commandColumn.type === 'history') {
        this.$dialog({
          modal: () =>
            import(/* webpackChunkName: "components/upload" */ './components/history.vue'),
          data: {
            title: this.$t('历史记录'),
            id: args.rowData.id
          },
          success: () => {}
        })
      }
    },
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (
        records.length <= 0 &&
        ['Delete', 'ConfigActive', 'ConfigInactive'].includes(e.toolbar.id)
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      } else if (e.toolbar.id === 'upload') {
        this.handleClickToolBarUpload()
      } else if (e.toolbar.id === 'ConfigActive') {
        // 生效
        this.handleClickToolBarConfigActive(records)
      } else if (e.toolbar.id === 'ConfigInactive') {
        // 失效
        this.handleClickToolBarConfigInactive(records)
      } else if (e.toolbar.id === 'Download') {
        this.handleClickToolBarDownload()
      } else if (e.toolbar.id === 'Delete') {
        this.handleClickToolBarDelete(records)
      }
    },
    // 生效
    handleClickToolBarConfigActive(list = []) {
      const _ids = list.map((item) => item.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .effectiveHazardousSubstancesSupplierCategory(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.refreshPage()
          this.$store.commit('endLoading')
        })
    },
    // 失效
    handleClickToolBarConfigInactive(list = []) {
      const _ids = list.map((item) => item.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .expireHazardousSubstancesSupplierCategory(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.refreshPage()
          this.$store.commit('endLoading')
        })
    },
    handleClickToolBarAdd() {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // 删除
    handleClickToolBarDelete(val) {
      if (val != null && val.some((item) => item.id != null)) {
        const ids = val.map((item) => item.id).filter((item) => item !== null)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除数据？')
          },
          success: () => {
            this.$API.hazardousSubstances
              .deleteHazardousSubstancesSupplierCategory(ids)
              .then((res) => {
                if (res && res.code === 200) {
                  this.$toast({
                    type: 'success',
                    content: this.$t(res.msg)
                  })
                  this.refreshPage()
                }
              })
          }
        })
      }
    },
    // refresh页面
    refreshPage() {
      this.$refs.tepPage.refreshCurrentGridData()
    },
    // 导入
    handleClickToolBarUpload() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'file',
          importApi: this.$API.hazardousSubstances.importHazardousSubstancesSupplierCategory,
          downloadTemplateApi: this.$API.hazardousSubstances.importTemplateDownSupplierCategory
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
        }
      })
    },
    // 导出
    handleClickToolBarDownload() {
      const asyncParams = this.$refs.tepPage.getAsyncParams()
      let params = {
        page: {
          current: asyncParams.page.current,
          size: asyncParams.page.size
        },
        ...this.searchFormModel
      }
      this.$API.hazardousSubstances
        .exportHazardousSubstancesSupplierCategoryList(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.handleSave(data, rowIndex)
      }
    },
    // 保存
    handleSave(rowData, rowIndex = 0) {
      console.log('rowDatarowData', rowData)
      this.$API.hazardousSubstances
        .addHazardousSubstancesSupplierCategory([rowData])
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
          } else {
            this.startEdit(rowIndex)
          }
        })
        .catch(() => {
          this.startEdit(rowIndex)
          this.$refs.tepPage.refreshCurrentGridData()
        })
    },
    startEdit(rowIndex) {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    actionBegin(args) {
      const { requestType, data } = args
      console.log('datadatadata', data, requestType)
      if (requestType === 'save') {
        const validateMap = {
          // companyCode: {
          //   value: data.companyCode,
          //   msg: this.$t('请选择公司代码')
          // },
          supplierCode: {
            value: data.supplierCode,
            msg: this.$t('请选择供应商编码')
          },
          categoryCode: {
            value: data.categoryCode,
            msg: this.$t('请选择品类编码')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background-color: #fff;
  .e-color {
    color: #000;
  }
}
</style>
