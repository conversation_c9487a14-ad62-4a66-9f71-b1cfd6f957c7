import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
import InputView from '../components/inputView.vue'
import Vue from 'vue'
import { StatusOptions } from '../components/enum.js'

export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Delete', icon: 'icon_solid_Createorder', title: i18n.t('删除') },
  { id: 'ConfigActive', icon: 'icon_solid_Activateorder', title: i18n.t('生效') },
  { id: 'ConfigInactive', icon: 'icon_solid_Pauseorder', title: i18n.t('失效') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
]

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: 120,
    field: 'status',
    headerText: i18n.t('状态'),
    allowEditing: false,
    editTemplate: () => {
      const template = {
        template: `<span>{{ data.status | transformValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transformValue(e) {
            return StatusOptions.filter((item) => item.value === e)[0].text
          }
        }
      }
      return { template }
    },
    valueConverter: {
      type: 'map',
      map: [
        { text: i18n.t('草稿'), value: 0, cssClass: '' },
        { text: i18n.t('生效'), value: 1, cssClass: '' },
        { text: i18n.t('失效'), value: 2, cssClass: '' }
      ]
    }
  },
  {
    width: 100,
    field: 'companyType',
    headerText: i18n.t('公司类型'),
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: {
        KT: i18n.t('空调'),
        WKZ: i18n.t('万颗子'),
        BD: i18n.t('白电')
      }
    }
  },
  {
    width: 100,
    field: 'companyCode',
    headerText: i18n.t('公司代码'),
    editTemplate: () => {
      return { template: InputView }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component('companyCodeTemplate', {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('公司代码')}}</span>
    //           </div>
    //         `
    //     })
    //   }
    // }
  },
  {
    width: 160,
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    editTemplate: () => ({ template: InputView }),
    allowEditing: false
  },
  {
    width: 100,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('supplierCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商编码')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    width: 160,
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    editTemplate: () => ({ template: InputView }),
    allowEditing: false
  },
  {
    width: 160,
    field: 'supplierContactUserMailbox',
    headerText: i18n.t('供方接收信息邮箱')
  },
  {
    width: 100,
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('categoryCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('品类编码')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    editTemplate: () => {
      return { template: InputView }
    },
    allowEditing: false
  },
  {
    width: 120,
    field: 'sqeAcct',
    headerText: i18n.t('SQE负责人'),
    template: () => {
      const template = {
        template: `<span>{{ data.sqeName }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => ({ template: Select })
  },
  {
    width: 0,
    field: 'sqeName',
    headerText: i18n.t('SEQ负责人名称'),
    editTemplate: () => {
      return { template: InputView }
    },
    allowEditing: false
  },
  {
    width: 120,
    field: 'firstApproverAcct',
    headerText: i18n.t('审核人'),
    template: () => {
      const template = {
        template: `<span>{{ data.approverName }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => ({ template: Select })
  },
  {
    width: 0,
    field: 'approverName',
    headerText: i18n.t('审核人'),
    editTemplate: () => {
      return { template: InputView }
    },
    allowEditing: false
  },
  {
    width: 100,
    field: 'dataSource',
    headerText: i18n.t('数据来源'),
    valueConverter: {
      type: 'map',
      map: [
        { text: i18n.t('手工新增'), value: 0, cssClass: '' },
        { text: i18n.t('系统自动生成'), value: 1, cssClass: '' }
      ]
    },
    ignore: true,
    allowEditing: false
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    ignore: true,
    allowEditing: false
  },
  {
    width: 160,
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    ignore: true,
    allowEditing: false
  },
  {
    width: 160,
    field: 'modifyDate',
    headerText: i18n.t('更新时间'),
    ignore: true,
    allowEditing: false
  }
]
