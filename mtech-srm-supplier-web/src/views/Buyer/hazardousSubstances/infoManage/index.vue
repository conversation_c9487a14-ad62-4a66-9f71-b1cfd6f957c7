<template>
  <div class="full-height">
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :selected-item="selectedItem"
      :data-source="tableList"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <TabConfirm v-show="tabIndex === 0" />
      <TabSummary v-show="tabIndex === 1" />
      <TabSummaryDetail v-show="tabIndex === 2" />
      <TabOverdue v-show="tabIndex === 3" />
      <TabRohs v-show="tabIndex === 4" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    TabConfirm: require('./pages/TabConfirm.vue').default,
    TabSummary: require('./pages/TabSummary.vue').default,
    TabSummaryDetail: require('./pages/TabSummaryDetail.vue').default,
    TabOverdue: require('./pages/TabOverdue.vue').default,
    TabRohs: require('./pages/TabRohs.vue').default
  },
  data() {
    // const tabIndex = Number(sessionStorage.getItem('BuyerTabIndex')) ?? 0
    return {
      tabIndex: this.$route.query.currentIndex ?? 0,
      tableList: [
        {
          title: this.$t('待确认')
        },
        {
          title: this.$t('汇总查询')
        },
        {
          title: this.$t('汇总明细查询')
        },
        {
          title: this.$t('已归档/超期查询')
        },
        {
          title: this.$t('RoHs查询')
        }
      ],
      selectedItem: this.$route.query.currentIndex ?? 0
    }
  },
  mounted() {
    this.selectItem = this.tabIndex
  },
  beforeDestory() {
    sessionStorage.removeItem('BuyerTabIndex')
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background-color: #fff;
  .e-color {
    color: #000;
  }
}
</style>
