<template>
  <div>
    <mt-input-number
      :id="data.column.field"
      v-model="data[data.column.field]"
      :open-on-focus="true"
      :allow-edit="false"
      :placeholder="$t('')"
      :max="maxNumber"
      @change="inputChange"
    ></mt-input-number>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      maxNumber: 600
    }
  },
  computed: {
    dateValue() {
      return this.data[this.data.column.field]
    }
  },
  mounted() {},
  methods: {
    inputChange(e) {
      // this.date[this.data.column.field] = e
      const _dataSource = this.$parent.dataSource
      _dataSource.map((item) => {
        if (item.id === this.data['id']) {
          item[this.data.column.field] = e
        }
      })
    }
  },
  beforeDestroy() {}
}
</script>
