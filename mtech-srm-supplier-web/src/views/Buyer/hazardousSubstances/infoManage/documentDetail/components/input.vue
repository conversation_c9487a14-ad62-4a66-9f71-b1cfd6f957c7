<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :open-on-focus="true"
      :allow-edit="false"
      :placeholder="$t('')"
      @change="inputChange"
      :disabled="true"
    ></mt-input>
  </div>
</template>

<script>
export default {
  data() {
    return { data: {} }
  },
  computed: {
    dateValue() {
      return this.data[this.data.column.field]
    }
  },
  mounted() {},
  methods: {
    inputChange(e) {
      // this.data[this.data.column.field] = e
      const _dataSource = this.$parent.dataSource
      _dataSource.map((item) => {
        if (item.id === this.data['id']) {
          item[this.data.column.field] = e
        }
      })
    }
  },
  beforeDestroy() {}
}
</script>
