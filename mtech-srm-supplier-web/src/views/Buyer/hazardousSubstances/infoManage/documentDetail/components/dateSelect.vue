<template>
  <div>
    <mt-date-picker
      :id="data.column.field"
      v-model="data[data.column.field]"
      :open-on-focus="true"
      :allow-edit="false"
      :placeholder="$t('请选择开始时间')"
      :disabled="true"
      @change="dateChange"
    ></mt-date-picker>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { timestampToDate } from '@/utils/utils'
export default {
  data() {
    return {
      data: {}
    }
  },
  mounted() {
    let _date = this.data[this.data.column.field]
    if (!isNaN(_date)) {
      if (Number(_date) < 0) {
        _date = _date.replace(/-/g, '')
      }
      if (_date.length > 1 && _date.length <= 13 && Number(_date) > 0) {
        while (_date.length < 13) {
          _date = _date + '0'
        }
      } else if (_date.length === 1) {
        _date = new Date().getTime()
      }
      this.data[this.data.column.field] = timestampToDate(_date, 'date')
    }
    if (this.data.column.field === 'nextReportTime') {
      this.$bus.$on('reportTimeChange', (val) => {
        const _dataSource = this.$parent.dataSource
        const _dateRow = _dataSource.filter((item) => {
          if (item.id === this.data['id']) {
            return
          }
        })[0]
        if (val) {
          _dateRow['nextReportTime'] = dayjs(val).add(1, 'year')
        } else {
          _dateRow['nextReportTime'] = dayjs(new Date()).add(1, 'year')
        }
      })
    }
  },
  methods: {
    dateChange(e) {
      // this.data[this.data.column.field] = dayjs(e).format('yyyy-MM-dd')
      const _dataSource = this.$parent.dataSource
      _dataSource.map((item) => {
        if (item.id === this.data['id']) {
          item[this.data.column.field] = dayjs(e).valueOf()
        }
      })
      if (this.data.column.field === 'reportTime') {
        this.$parent.$emit('reportTimeChange', {
          date: e,
          id: this.data['id'],
          key: 'nextReportTime'
        })
      }
    }
  },
  beforeDestroy() {}
}
</script>
