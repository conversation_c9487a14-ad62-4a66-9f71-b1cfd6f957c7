<!-- 采方-详情 -->
<template>
  <div class="detail-container">
    <div class="header">
      <div class="title">{{ $t('有害物质管理-单据详情') }}</div>
      <div class="operate-bar">
        <div v-if="canEdit" class="op-item mt-flex" @click="handleSave">
          {{ $t('保存') }}
        </div>
        <div v-if="isEdit" class="op-item mt-flex" @click="handleSubmit">
          {{ $t('确认并提交审批') }}
        </div>
        <div v-if="isEdit" class="op-item mt-flex" @click="handleReject">
          {{ $t('驳回至供方') }}
        </div>
        <div class="op-item mt-flex" @click="handleBack">
          {{ $t('返回') }}
        </div>
      </div>
    </div>
    <div class="form-warp">
      <mt-form ref="formRef" :model="formModel" :rules="formRules">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('公司类型')" label-style="top" prop="companyType">
              <mt-select
                v-model="formModel.companyType"
                :data-source="companyTypeOptions"
                :show-clear-button="true"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="!canEdit"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('供应商编码')" label-style="top" prop="supplierCode">
              <div style="display: flex; align-items: baseline">
                <mt-input
                  v-model="formModel.supplierCode"
                  :disabled="true"
                  :style="{ width: canEdit ? '96%' : '100%' }"
                />
                <mt-icon
                  v-if="canEdit"
                  style="width: 20px; cursor: pointer; padding: 4px"
                  name="icon_input_search"
                  @click.native="handleClick"
                ></mt-icon>
              </div>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('供应商名称')" label-style="top" prop="supplierName">
              <mt-input v-model="formModel.supplierName" :disabled="true" />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('品类编码')" label-style="top" prop="categoryCode">
              <div style="display: flex; align-items: baseline">
                <mt-input
                  v-model="formModel.categoryCode"
                  :disabled="true"
                  :style="{ width: canEdit ? '96%' : '100%' }"
                />
                <mt-icon
                  v-if="canEdit"
                  style="width: 20px; cursor: pointer; padding: 4px"
                  name="icon_input_search"
                  @click.native="handleClick"
                ></mt-icon>
              </div>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('品类名称')" label-style="top" prop="categoryName">
              <mt-input v-model="formModel.categoryName" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('承诺书')" label-style="top" prop="fileCode">
              <upload-file
                v-if="!isEdit || formModel.id"
                ref="uploadFile"
                v-model="formModel.fileCode"
                :row-id="formModel.id"
                @listChange="fileListChange"
                :disabled="canEdit"
              ></upload-file>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="18">
            <mt-form-item :label="$t('采方备注')" label-style="top" prop="remark">
              <mt-input v-model="formModel.remark" :disabled="!canEdit" />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <div class="table-content">
      <div class="table-title">{{ $t('明细信息') }}</div>
      <div>
        <sc-table
          ref="sctableRef"
          grid-id="e6798903-6a07-4bbb-befa-d763327c2091"
          :loading="loading"
          :columns="columns"
          :table-data="tableData"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
          @edit-closed="editComplete"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item)"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
    </div>
    <categorySelect ref="categorySelectRef" @confirm="categorySelectConfirm" />
    <MaintenanceDetails ref="maintenanceDetailsRef" @confirm="maintenanceDetailsConfirm" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import categorySelect from './components/categorySelect.vue'
import uploadFile from '../infoManage/documentDetail/components/uploadFile.vue'
import MaintenanceDetails from './components/MaintenanceDetails.vue'
import { companyTypeOptions } from './config'
export default {
  components: { ScTable, categorySelect, uploadFile, MaintenanceDetails },
  data() {
    return {
      formModel: {
        companyCode: null,
        companyName: null,
        supplierCode: null,
        supplierName: null,
        categoryCode: null,
        categoryName: null,
        sqeAcct: null,
        sqeName: null,
        fileList: [],
        fileCode: null
      },
      formRules: {
        companyType: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择公司类型')
          }
        ],
        supplierCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择供应商编码')
          }
        ],
        categoryCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择品类编码')
          }
        ],
        fileCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请上传承诺书')
          }
        ]
      },
      companyTypeOptions,
      testTypeOptions: [],

      loading: false,
      tableData: [
        // {
        //   testType: 'A',
        //   testTypeName: '欧盟RoHS 2.0'
        // },
        // {
        //   testType: 'B',
        //   testTypeName: '欧盟REACH SVHC'
        // },
        // {
        //   testType: 'C',
        //   testTypeName: 'REACH 附件XVII'
        // },
        // {
        //   testType: 'F',
        //   testTypeName: '美国TSCA'
        // }
      ],
      editRules: {
        testType: [{ required: true, message: this.$t('必填') }],
        testOrg: [{ required: true, message: this.$t('必填') }],
        reportTime: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    type() {
      return this.$route.query?.type
    },
    isEdit() {
      return this.type === 'edit'
    },
    canEdit() {
      return this.type !== 'check'
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'testType',
          title: this.$t('测试类型'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [<div>{row.testTypeName}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.testType}
                  options={this.testTypeOptions}
                  option-props={{ label: 'label', value: 'value' }}
                  transfer
                  placeholder={this.$t('请选择')}
                  on-change={(item) => {
                    const selectedItem = this.testTypeOptions.find((e) => e.value === item.value)
                    row.testTypeName = selectedItem.label
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'testOrg',
          title: this.$t('测试机构'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.testOrg}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'reportTime',
          title: this.$t('出具报告日期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date > dayjs().startOf('day') || date < dayjs().subtract(1, 'year')
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.reportTime}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                  onChange={() => {
                    if (row.reportTime) {
                      row.nextReportTime = dayjs(row.reportTime)
                        .add(1, 'year')
                        .subtract(1, 'day')
                        .format('YYYY-MM-DD')
                    } else {
                      row.nextReportTime = null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'nextReportTime',
          title: this.$t('下次报告更新日期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.nextReportTime}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'file',
          title: this.$t('报告附件'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <span
                    style='color: #2783fe;cursor: pointer'
                    onClick={() => this.handleUpload(row)}>
                    {this.$t('附件上传')}
                  </span>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  <span
                    style='color: #2783fe;cursor: pointer'
                    onClick={() => this.handleUpload(row)}>
                    {this.$t('附件上传')}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'detail',
          title: this.$t('测试数据明细'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.testType === 'A' ? (
                  <div>
                    <span
                      style='color: #2783fe;cursor: pointer'
                      onClick={() => this.handleShow(row)}>
                      {this.$t('维护明细')}
                    </span>
                  </div>
                ) : null
              ]
            },
            edit: ({ row }) => {
              return [
                row.testType === 'A' ? (
                  <div>
                    <span
                      style='color: #2783fe;cursor: pointer'
                      onClick={() => this.handleShow(row)}>
                      {this.$t('维护明细')}
                    </span>
                  </div>
                ) : null
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remark}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.type !== 'check',
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (['add', 'edit'].includes(this.type)) {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  mounted() {
    if (this.$route.query?.id) {
      this.getDetail()
      this.getDetailList()
    }
  },
  methods: {
    getTestTypeOptions() {
      if (this.formModel?.categoryCode) {
        let params = {
          categoryCode: this.formModel.categoryCode
        }
        this.$API.hazardousSubstances.queryTestTypeListApi(params).then((res) => {
          if (res.code === 200) {
            this.testTypeOptions = res.data.map((item) => {
              return {
                label: item.testTypeName,
                value: item.testType
              }
            })
          }
        })
      } else {
        this.testTypeOptions = []
      }
    },
    getDetail() {
      let params = {
        id: this.$route.query?.id
      }
      this.$API.hazardousSubstances.getHazardousSubstancesDocumentDetail(params).then((res) => {
        if (res.code === 200) {
          this.formModel = res.data ?? this.formModel
        }
      })
    },
    getDetailList() {
      let params = {
        page: { current: 1, size: 20 },
        defaultRules: [
          {
            label: 'ID',
            field: 'mainFormId',
            type: 'string',
            operator: 'equal',
            value: this.$route?.query.id
          }
        ]
      }
      this.$API.hazardousSubstances.queryLineHeadApi(params).then((res) => {
        if (res.code === 200) {
          if (res.data.records.length !== 0) {
            this.tableData = res.data.records.map((item) => {
              return {
                ...item,
                reportTime: dayjs(Number(item?.reportTime)).format('YYYY-MM-DD'),
                nextReportTime: dayjs(Number(item?.nextReportTime)).format('YYYY-MM-DD'),
                harmfulCategoryFileRequestList: []
              }
            })
            this.tableData.forEach((row) => {
              this.getFileList(row)
            })
          }
        }
      })
    },
    getFileList(row) {
      this.$API.hazardousSubstances.getHazardousSubstancesDetailId({ id: row.id }).then((res) => {
        const _list = res.data ?? []
        const _harmfulCategoryFileRequestList = []
        const _map = new Map()
        _list.map((item) => {
          if (!_map.get(item.fileName)) {
            _harmfulCategoryFileRequestList.push(item)
            _map.set(item.fileName, true)
          }
        })
        this.$set(row, 'harmfulCategoryFileRequestList', _harmfulCategoryFileRequestList)
      })
    },
    handleShow(row) {
      let obj = {
        pb: row?.pb || 0,
        cd: row?.cd || 0,
        hg: row?.hg || 0,
        cr6: row?.cr6 || 0,
        pbb: row?.pbb || 0,
        pbde: row?.pbde || 0,
        dehp: row?.dehp || 0,
        dibp: row?.dibp || 0,
        bbp: row?.bbp || 0,
        dbp: row?.dbp || 0
      }
      this.$refs.maintenanceDetailsRef.dialogInit({
        title: this.$t('维护明细'),
        tableData: [obj]
      })
    },
    maintenanceDetailsConfirm(row) {
      this.tableData.forEach((item) => {
        if (item.testType === 'A') {
          for (const key in row) {
            if (key !== 'id') {
              item[key] = row[key]
            }
          }
        }
      })
    },
    handleUpload(row) {
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          fileList: row?.harmfulCategoryFileRequestList || []
        },
        failed: (res) => {
          row.harmfulCategoryFileRequestList = res
        }
      })
    },
    fileListChange(list) {
      this.formModel.fileList = list?.harmfulCategoryFileRequestList ?? []
      if (this.formModel.fileList?.length !== 0) {
        this.formModel.fileCode = '1'
      } else {
        this.formModel.fileCode = null
      }
    },
    handleClick() {
      if (!this.formModel.companyType) {
        this.$toast({ content: this.$t('请先选择公司类型'), type: 'warning' })
        return
      }
      this.$refs.categorySelectRef.dialogInit({
        title: this.$t('选择供应商品类'),
        companyType: this.formModel.companyType
      })
    },
    categorySelectConfirm(row) {
      for (const key in row) {
        if (Object.hasOwnProperty.call(this.formModel, key)) {
          if (key !== 'id') {
            this.formModel[key] = row[key]
          }
        }
      }
      this.getTestTypeOptions()
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.tableRef.removeCheckboxRow()
          break
        default:
          break
      }
    },
    handleAdd() {
      if (!this.formModel?.categoryCode) {
        this.$toast({ content: this.$t('请先选择品类'), type: 'warning' })
        return
      }
      const item = {}
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    handleSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.tableRef.getTableData().visibleData.length === 0) {
            this.$toast({ content: this.$t('请新增明细'), type: 'warning' })
            return
          }
          let params = this.getParams()
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认保存？')
            },
            success: () => {
              this.$API.hazardousSubstances.saveHazardousSubstancesInfo([params]).then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('保存成功'), type: 'success' })
                  this.$router.push({
                    name: 'hazardous-substances/info-manage',
                    query: {
                      timeStamp: new Date().getTime()
                    }
                  })
                }
              })
            }
          })
        }
      })
    },
    getParams() {
      let params = { ...this.formModel }
      params.harmfulCategoryLineRequests = this.tableRef.getTableData().visibleData.map((item) => {
        if (item.testType === 'A') {
          return {
            id: item?.id?.includes('row_') ? null : item.id,
            testType: item.testType,
            testTypeName: item.testTypeName,
            testOrg: item.testOrg,
            reportTime: dayjs(item.reportTime).valueOf(),
            nextReportTime: dayjs(item.nextReportTime).valueOf(),
            remark: item.remark,
            harmfulCategoryFileRequestList: item.harmfulCategoryFileRequestList,
            pb: item?.pb || 0,
            cd: item?.cd || 0,
            hg: item?.hg || 0,
            cr6: item?.cr6 || 0,
            pbb: item?.pbb || 0,
            pbde: item?.pbde || 0,
            dehp: item?.dehp || 0,
            dibp: item?.dibp || 0,
            bbp: item?.bbp || 0,
            dbp: item?.dbp || 0
          }
        } else {
          return {
            id: item?.id?.includes('row_') ? null : item.id,
            testType: item.testType,
            testTypeName: item.testTypeName,
            testOrg: item.testOrg,
            reportTime: dayjs(item.reportTime).valueOf(),
            nextReportTime: dayjs(item.nextReportTime).valueOf(),
            remark: item.remark,
            harmfulCategoryFileRequestList: item.harmfulCategoryFileRequestList
          }
        }
      })
      if (this.formModel?.fileList) {
        params.harmfulCategoryFileRequestList = JSON.parse(JSON.stringify(this.formModel.fileList))
      }
      return params
    },
    handleSubmit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交？')
        },
        success: () => {
          const ids = [this.formModel?.id]
          this.$API.hazardousSubstances.hazardousSubstancesConfirmApprovals(ids).then((res) => {
            if (res.code === 200) {
              this.$toast({
                type: 'success',
                content: this.$t('提交成功')
              })
              this.$router.push({
                name: 'hazardous-substances/info-manage',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            }
          })
        }
      })
    },
    handleReject() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认驳回？')
        },
        success: () => {
          const ids = [this.formModel?.id]
          this.$API.hazardousSubstances.hazardousSubstancesManageReject(ids).then((res) => {
            if (res.code === 200) {
              this.$toast({
                type: 'success',
                content: this.$t('操作成功')
              })
              this.$router.push({
                name: 'hazardous-substances/info-manage',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            }
          })
        }
      })
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-container {
  background: #fff;
  height: 100%;
  .header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      height: 100%;
      line-height: 40px;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      padding: 5px 10px;
    }
    .operate-bar {
      height: 100%;
      float: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;

      .op-item {
        cursor: pointer;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }
  }
  .form-warp {
    padding: 0 10px;
  }
  .table-content {
    padding: 10px;
    .table-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
    }
  }
}
</style>
