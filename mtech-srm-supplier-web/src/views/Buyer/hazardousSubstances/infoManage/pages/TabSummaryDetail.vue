<!-- 汇总明细查询 -->
<template>
  <div>
    <mt-template-page
      ref="summaryTemplateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="docNo" :label="$t('单据号')">
            <mt-input
              v-model="searchFormModel.docNo"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('单据状态')">
            <mt-select
              v-model="searchFormModel.status"
              :data-source="statusOptions"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="companyType" :label="$t('公司类型')">
            <mt-select
              v-model="searchFormModel.companyType"
              :data-source="[
                { text: $t('空调'), value: 'KT' },
                { text: $t('万颗子'), value: 'WKZ' },
                { text: $t('白电'), value: 'BD' }
              ]"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司')">
            <RemoteAutocomplete
              v-model="searchFormModel.companyCode"
              url="/masterDataManagement/auth/company/auth-fuzzy"
              :placeholder="$t('请选择')"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              params-key="fuzzyParam"
              records-position="data"
              multiple
            />
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
            <mt-input
              v-model="searchFormModel.supplierCode"
              :show-clear-button="true"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierName" :label="$t('供应商名称')">
            <mt-input
              v-model="searchFormModel.supplierName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="categoryCode" :label="$t('品类编码')">
            <mt-input
              v-model="searchFormModel.categoryCode"
              :show-clear-button="true"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="categoryName" :label="$t('品类名称')">
            <mt-input
              v-model="searchFormModel.categoryName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="sqeAcct" :label="$t('SQE负责人账号')">
            <mt-input
              v-model="searchFormModel.sqeAcct"
              :show-clear-button="true"
              :placeholder="$t('仅支持单个精准查询')"
            />
          </mt-form-item>
          <mt-form-item prop="sqeName" :label="$t('SQE负责人名称')">
            <mt-input
              v-model="searchFormModel.sqeName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="submitter" :label="$t('提交人')">
            <mt-input
              v-model="searchFormModel.submitter"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="submitTime" :label="$t('提交日期')">
            <mt-date-range-picker
              v-model="searchFormModel.submitTime"
              @change="(e) => dateTimeChange(e, 'submitTime')"
              :placeholder="$t('请选择提交日期')"
            />
          </mt-form-item>
          <mt-form-item prop="testOrg" :label="$t('测试机构')">
            <mt-input
              v-model="searchFormModel.testOrg"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
          <mt-form-item prop="reportTime" :label="$t('出具报告日期')">
            <mt-date-range-picker
              v-model="searchFormModel.reportTime"
              @change="(e) => dateTimeChange(e, 'reportTime')"
              :placeholder="$t('请选择出具报告日期')"
            />
          </mt-form-item>
          <mt-form-item prop="nextReportTime" :label="$t('下次出具报告日期')">
            <mt-date-range-picker
              v-model="searchFormModel.nextReportTime"
              @change="(e) => dateTimeChange(e, 'nextReportTime')"
              :placeholder="$t('请选择下次出具报告日期')"
            />
          </mt-form-item>
          <mt-form-item prop="purchaserRemark" :label="$t('采方备注')">
            <mt-input
              v-model="searchFormModel.purchaserRemark"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierRemark" :label="$t('供应商备注')">
            <mt-input
              v-model="searchFormModel.supplierRemark"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
          <mt-form-item prop="rejectReason" :label="$t('采方驳回理由')">
            <mt-input
              v-model="searchFormModel.rejectReason"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
          <mt-form-item :label="$t('创建人')" prop="createUserName">
            <mt-input
              v-model="searchFormModel.createUserName"
              :placeholder="$t('支持模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建日期')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              @change="(e) => dateTimeChange(e, 'createTime')"
              :placeholder="$t('请选择创建日期')"
            />
          </mt-form-item>
          <mt-form-item :label="$t('更新人')" prop="updateUserName">
            <mt-input
              v-model="searchFormModel.updateUserName"
              :placeholder="$t('支持模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="updateTime" :label="$t('更新日期')">
            <mt-date-range-picker
              v-model="searchFormModel.updateTime"
              @change="(e) => dateTimeChange(e, 'updateTime')"
              :placeholder="$t('请选择更新日期')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { SummaryDetailToolbar, SummaryDetailColumnData, statusOptions } from '../config/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getHeadersFileName, download } from '@/utils/utils.js'
export default {
  components: {
    RemoteAutocomplete
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      statusOptions,
      pageConfig: [
        {
          isUseCustomSearch: true, // 是否使用自定义查询
          isCustomSearchRules: true,
          toolbar: SummaryDetailToolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: 'bad4e46d-7f2e-4019-8777-fc780ec6c85e',
          grid: {
            columnData: SummaryDetailColumnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/user/harmful/category/head/queryDetailPage'
            }
          }
        }
      ]
    }
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'docNo') {
        this.$router.push({
          path: '/supplier/hazardous-substances/info-manage-detail',
          query: {
            ...data,
            id: data.id,
            isEdit: [20, '20', 40, '40', 60, '60', 70, '70'].includes(data.status)
          }
        })
      }
      sessionStorage.setItem('BuyerTabIndex', Number(this.tabIndex))
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'summaryDetailExport') {
        this.handleExport()
      }
    },
    handleExport() {
      const asyncParams =
        this.$refs.summaryTemplateRef.getCurrentUsefulRef().pluginRef.asyncParams || {}
      const _params = {
        ...asyncParams
      }
      this.$API.hazardousSubstances.hazardousSubstancesSummaryDetailExport(_params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
