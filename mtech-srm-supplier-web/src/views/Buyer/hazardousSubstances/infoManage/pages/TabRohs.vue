<!-- RoHs查询 -->
<template>
  <div>
    <mt-template-page
      ref="rohsTemplateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { roHsToolbar, roHsColumnData } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils.js'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: roHsToolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: 'b5b3295d-6fdc-4bec-9beb-9ce8a02a3918',
          grid: {
            columnData: roHsColumnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/user/harmful/category/head/getRoHSByPage'
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'roHsExport') {
        this.handleExport()
        return
      }
    },
    handleExport() {
      const _params = this.getRules()
      this.$API.hazardousSubstances.hazardousSubstancesRoHsExport(_params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    getRules() {
      // 获取查询条件
      const _rule =
        this.$refs.rohsTemplateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: _rule.condition || '',
        page: { current: 1, size: 20000 },
        rules: _rule.rules || []
      }
      return params
    }
  }
}
</script>
