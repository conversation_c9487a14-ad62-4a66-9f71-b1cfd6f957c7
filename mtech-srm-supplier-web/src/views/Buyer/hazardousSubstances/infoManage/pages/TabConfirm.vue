<!-- 待确认 -->
<template>
  <div>
    <mt-template-page
      ref="confirmTemplateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="docNo" :label="$t('单据号')">
            <mt-input
              v-model="searchFormModel.docNo"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
          <!-- <mt-form-item prop="status" :label="$t('单据状态')">
            <mt-select
              v-model="searchFormModel.status"
              :data-source="statusOptions"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            />
          </mt-form-item> -->
          <mt-form-item prop="companyType" :label="$t('公司类型')">
            <mt-select
              v-model="searchFormModel.companyType"
              :data-source="companyTypeOptions"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司')">
            <RemoteAutocomplete
              v-model="searchFormModel.companyCode"
              url="/masterDataManagement/auth/company/auth-fuzzy"
              :placeholder="$t('请选择')"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              params-key="fuzzyParam"
              records-position="data"
              multiple
            />
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
            <mt-input
              v-model="searchFormModel.supplierCode"
              :show-clear-button="true"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierName" :label="$t('供应商名称')">
            <mt-input
              v-model="searchFormModel.supplierName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
          <mt-form-item prop="sqeAcct" :label="$t('SQE负责人账号')">
            <mt-input
              v-model="searchFormModel.sqeAcct"
              :show-clear-button="true"
              :placeholder="$t('仅支持单个精准查询')"
            />
          </mt-form-item>
          <mt-form-item prop="sqeName" :label="$t('SQE负责人名称')">
            <mt-input
              v-model="searchFormModel.sqeName"
              :show-clear-button="true"
              :placeholder="$t('支持模糊查询')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { toolbar, columnData, statusOptions, companyTypeOptions } from '../config/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
export default {
  components: {
    RemoteAutocomplete
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      statusOptions,
      companyTypeOptions,
      pageConfig: [
        {
          isUseCustomSearch: true, // 是否使用自定义查询
          isCustomSearchRules: true,
          toolbar,
          buttonQuantity: 7,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: 'cd2eaba0-3a23-4791-a53d-6675d132de13',
          grid: {
            columnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/user/harmful/category/head/query',
              params: {
                statusList: [10, 20]
              }
            }
          }
        }
      ]
    }
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'docNo') {
        this.$router.push({
          path: '/supplier/hazardous-substances/info-manage-detail',
          query: {
            id: data.id,
            type: [10, '10', 20, '20'].includes(data.status) ? 'edit' : 'check',
            timeStamp: new Date().getTime()
          }
        })
      }
      sessionStorage.setItem('BuyerTabIndex', Number(this.tabIndex))
    },
    handleClickToolBar(e) {
      let selectRecords = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'upload') {
        this.handleUpload()
        return
      }
      if (selectRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'confirmApproval') {
        this.handleSubmit(selectRecords)
      } else if (e.toolbar.id === 'rejectSup') {
        this.handleReject(selectRecords)
      } else if (e.toolbar.id === 'delete') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除？')
          },
          success: () => {
            this.handleDelete(selectRecords)
          }
        })
      } else if (e.toolbar.id === 'publish') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认发布？')
          },
          success: () => {
            this.handlePublish(selectRecords)
          }
        })
      }
    },
    handleAdd() {
      this.$router.push({
        path: '/supplier/hazardous-substances/info-manage-detail',
        query: {
          type: 'add',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleDelete(selectRecords) {
      const ids = selectRecords.map((v) => v.id)
      this.$API.hazardousSubstances.deleteHeadApi(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.confirmTemplateRef.refreshCurrentGridData()
        }
      })
    },
    handlePublish(selectRecords) {
      const ids = selectRecords.map((v) => v.id)
      this.$API.hazardousSubstances.publishApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
          this.$refs.confirmTemplateRef.refreshCurrentGridData()
        }
      })
    },
    handleUpload() {
      this.$dialog({
        modal: () => import('@/components/Upload/index.vue'),
        data: {
          title: this.$t('上传承诺书')
        },
        success: (params) => {
          params.fileId = params.id
          this.$API.hazardousSubstances.uploadCommitmentTemplateApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({
                type: 'success',
                content: this.$t('操作成功')
              })
            }
          })
        }
      })
    },
    handleSubmit(selectRecords) {
      const _ids = selectRecords.map((v) => v.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .hazardousSubstancesConfirmApprovals(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.$refs.confirmTemplateRef.refreshCurrentGridData()
          this.$store.commit('endLoading')
        })
    },
    handleReject(selectRecords) {
      const _ids = selectRecords.map((v) => v.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .hazardousSubstancesManageReject(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.$refs.confirmTemplateRef.refreshCurrentGridData()
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
