import { i18n } from '@/main.js'
import { StatusOptions } from '../components/enum.js'
import { timestampToDate } from '@/utils/utils.js'

export const statusOptions = [
  { text: i18n.t('草稿'), value: 10 },
  { text: i18n.t('待提交'), value: 11 },
  { text: i18n.t('采方驳回'), value: 15 },
  { text: i18n.t('待确认'), value: 20 },
  { text: i18n.t('审批中'), value: 30 },
  { text: i18n.t('审批驳回'), value: 40 },
  { text: i18n.t('审批通过'), value: 50 },
  { text: i18n.t('审批废弃'), value: 60 },
  { text: i18n.t('审批撤回'), value: 70 }
]

export const companyTypeOptions = [
  { text: i18n.t('空调'), value: 'KT' },
  { text: i18n.t('万颗子'), value: 'WKZ' },
  { text: i18n.t('白电'), value: 'BD' }
]

export const testTypeOptions = [
  { label: i18n.t('欧盟RoHS 2.0'), value: 'A' },
  { label: i18n.t('欧盟REACH SVHC'), value: 'B' },
  { label: i18n.t('REACH 附件XVII'), value: 'C' },
  { label: i18n.t('美国TSCA'), value: 'F' }
]

// 待确认 - 按钮组
export const toolbar = [
  { id: 'confirmApproval', icon: '', title: i18n.t('确认并提交审批') },
  { id: 'rejectSup', icon: '', title: i18n.t('驳回至供方') },
  { id: 'add', icon: '', title: i18n.t('新增') },
  { id: 'delete', icon: '', title: i18n.t('删除') },
  { id: 'publish', icon: '', title: i18n.t('发布') },
  { id: 'upload', icon: '', title: i18n.t('上传承诺书') }
]

// 汇总查询 - 按钮组
export const SummaryToolbar = [
  { id: 'export', icon: '', title: i18n.t('导出') },
  { id: 'detailApproval', icon: '', title: i18n.t('查看OA审批') }
]

// 汇总明细查询 - 按钮组
export const SummaryDetailToolbar = [{ id: 'summaryDetailExport', icon: '', title: i18n.t('导出') }]

// 已归档/超期查询 - 按钮组
export const overdueToolbar = [{ id: 'overdueExport', icon: '', title: i18n.t('导出') }]

// RoHS2.0查询
export const roHsToolbar = [{ id: 'roHsExport', icon: '', title: i18n.t('导出') }]

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'docNo',
    headerText: i18n.t('单据号'),
    cellTools: []
  },
  {
    width: 120,
    field: 'status',
    headerText: i18n.t('单据状态'),
    template: () => {
      const template = {
        template: `<span>{{ data.status }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: StatusOptions,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('草稿'),
        11: i18n.t('待提交'),
        15: i18n.t('采方驳回'),
        20: i18n.t('待确认'),
        30: i18n.t('审批中'),
        40: i18n.t('审批驳回'),
        50: i18n.t('审批通过'),
        60: i18n.t('审批废弃'),
        70: i18n.t('审批撤回')
      }
    }
  },
  {
    width: 100,
    field: 'companyType',
    headerText: i18n.t('公司类型'),
    valueConverter: {
      type: 'map',
      map: {
        KT: i18n.t('空调'),
        WKZ: i18n.t('万颗子'),
        BD: i18n.t('白电')
      }
    }
  },
  {
    width: 250,
    field: 'companyCode',
    headerText: i18n.t('公司代码'),
    searchOptions: {
      elementType: 'remote-autocomplete',
      selectType: 'administrativeCompany', // 业务公司
      fields: { text: 'orgName', value: 'orgCode' },
      params: {
        organizationLevelCodes: ['ORG01', 'ORG02']
      },
      multiple: true,
      operator: 'in',
      url: '/masterDataManagement/tenant/organization/specified-level-paged-query'
    }
  },
  {
    width: 250,
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    ignore: true
  },
  {
    width: 250,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    width: 250,
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 250,
    field: 'sqeName',
    headerText: i18n.t('SQE')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('更新时间'),
    ignore: true,
    allowEditing: false
  }
]

export const SummaryColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'docNo',
    headerText: i18n.t('单据号'),
    cellTools: []
  },
  {
    width: 120,
    field: 'status',
    headerText: i18n.t('单据状态'),
    searchOptions: {
      elementType: 'select',
      dataSource: StatusOptions,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('草稿'),
        11: i18n.t('待提交'),
        15: i18n.t('采方驳回'),
        20: i18n.t('待确认'),
        30: i18n.t('审批中'),
        40: i18n.t('审批驳回'),
        50: i18n.t('审批通过'),
        60: i18n.t('审批废弃'),
        70: i18n.t('审批撤回')
      }
    }
  },
  {
    width: 100,
    field: 'companyType',
    headerText: i18n.t('公司类型'),
    valueConverter: {
      type: 'map',
      map: {
        KT: i18n.t('空调'),
        WKZ: i18n.t('万颗子'),
        BD: i18n.t('白电')
      }
    }
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司代码'),
    searchOptions: {
      elementType: 'remote-autocomplete',
      selectType: 'administrativeCompany', // 业务公司
      fields: { text: 'orgName', value: 'orgCode' },
      params: {
        organizationLevelCodes: ['ORG01', 'ORG02']
      },
      multiple: true,
      operator: 'in',
      url: '/masterDataManagement/tenant/organization/specified-level-paged-query'
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    ignore: true
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'sqeAcct',
    headerText: i18n.t('SQE负责人'),
    template: () => {
      const template = {
        template: `<span>{{ data.sqeAcct }}-{{ data.sqeName }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    }
  },
  {
    field: 'submitter',
    headerText: i18n.t('提交人')
  },
  {
    field: 'submitTime',
    headerText: i18n.t('提交日期'),
    template: () => {
      const template = {
        template: `<span>{{ data.submitTime | transFormValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transFormValue(e) {
            let _date = e
            if (!isNaN(_date)) {
              if (Number(_date) < 0) {
                _date = _date.replace(/-/g, '')
              }
              if (
                _date.length > 1 &&
                _date.length <= 13 &&
                isNaN(Number(_date)) &&
                Number(_date) > 0
              ) {
                while (_date.length < 13) {
                  _date = _date + '0'
                }
              } else if (_date.length === 1) {
                _date = new Date().getTime()
              }
              return timestampToDate(_date, 'date')
            }
          }
        }
      }
      return { template }
    }
  },
  {
    field: 'purchaserRemark',
    headerText: i18n.t('采方备注')
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注')
  },
  {
    field: 'rejectReason',
    headerText: i18n.t('采方驳回理由')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('更新时间'),
    ignore: true,
    allowEditing: false
  }
]

export const SummaryDetailColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'docNo',
    headerText: i18n.t('单据号')
    // cellTools: []
  },
  {
    width: 120,
    field: 'status',
    headerText: i18n.t('单据状态'),
    template: () => {
      const template = {
        template: `<span>{{ data.status }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: StatusOptions,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('草稿'),
        11: i18n.t('待提交'),
        15: i18n.t('采方驳回'),
        20: i18n.t('待确认'),
        30: i18n.t('审批中'),
        40: i18n.t('审批驳回'),
        50: i18n.t('审批通过'),
        60: i18n.t('审批废弃'),
        70: i18n.t('审批撤回')
      }
    }
  },
  {
    width: 100,
    field: 'companyType',
    headerText: i18n.t('公司类型'),
    valueConverter: {
      type: 'map',
      map: {
        KT: i18n.t('空调'),
        WKZ: i18n.t('万颗子'),
        BD: i18n.t('白电')
      }
    }
  },
  {
    width: 120,
    field: 'companyCode',
    headerText: i18n.t('公司代码')
  },
  {
    width: 250,
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: 120,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: 250,
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 120,
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: 120,
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'sqeAcct',
    headerText: i18n.t('SQE负责人'),
    template: () => {
      const template = {
        template: `<span>{{ data.sqeAcct }}-{{ data.sqeName }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    }
  },
  {
    width: 120,
    field: 'submitter',
    headerText: i18n.t('提交人')
  },
  {
    field: 'submitTime',
    headerText: i18n.t('提交日期'),
    template: () => {
      const template = {
        template: `<span>{{ data.submitTime | transFormValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transFormValue(e) {
            let _date = e
            if (!isNaN(_date)) {
              if (Number(_date) < 0) {
                _date = _date.replace(/-/g, '')
              }
              if (
                _date.length > 1 &&
                _date.length <= 13 &&
                isNaN(Number(_date)) &&
                Number(_date) > 0
              ) {
                while (_date.length < 13) {
                  _date = _date + '0'
                }
              } else if (_date.length === 1) {
                _date = new Date().getTime()
              }
              return timestampToDate(_date, 'date')
            }
          }
        }
      }
      return { template }
    }
  },
  {
    width: 120,
    field: 'testTypeName',
    headerText: i18n.t('报告提供类型')
  },
  {
    width: 120,
    field: 'testOrg',
    headerText: i18n.t('测试机构')
  },
  {
    width: 120,
    field: 'reportTime',
    headerText: i18n.t('出具报告日期'),
    template: () => {
      const template = {
        template: `<span>{{ data.reportTime | transFormValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transFormValue(e) {
            let _date = e
            if (!isNaN(_date)) {
              if (Number(_date) < 0) {
                _date = _date.replace(/-/g, '')
              }
              if (
                _date.length > 1 &&
                _date.length <= 13 &&
                isNaN(Number(_date)) &&
                Number(_date) > 0
              ) {
                while (_date.length < 13) {
                  _date = _date + '0'
                }
              } else if (_date.length === 1) {
                _date = new Date().getTime()
              }
              return timestampToDate(_date, 'date')
            }
          }
        }
      }
      return { template }
    }
  },
  {
    width: 120,
    field: 'nextReportTime',
    headerText: i18n.t('下次出具报告日期'),
    template: () => {
      const template = {
        template: `<span>{{ data.nextReportTime | transFormValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transFormValue(e) {
            let _date = e
            if (!isNaN(_date)) {
              if (Number(_date) < 0) {
                _date = _date.replace(/-/g, '')
              }
              if (
                _date.length > 1 &&
                _date.length <= 13 &&
                isNaN(Number(_date)) &&
                Number(_date) > 0
              ) {
                while (_date.length < 13) {
                  _date = _date + '0'
                }
              } else if (_date.length === 1) {
                _date = new Date().getTime()
              }
              return timestampToDate(_date, 'date')
            }
          }
        }
      }
      return { template }
    }
  },
  {
    width: 120,
    field: 'fileName',
    headerText: i18n.t('报告附件')
  },
  {
    width: 120,
    field: 'purchaserRemark',
    headerText: i18n.t('采方备注')
  },
  {
    width: 120,
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注')
  },
  {
    width: 120,
    field: 'rejectReason',
    headerText: i18n.t('采方驳回理由')
  },
  {
    width: 120,
    field: 'lineRemark',
    headerText: i18n.t('供应商行备注')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    template: () => {
      const template = {
        template: `<span>{{ data.createTime | transFormValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transFormValue(e) {
            let _date = e
            if (!isNaN(_date)) {
              if (Number(_date) < 0) {
                _date = _date.replace(/-/g, '')
              }
              if (
                _date.length > 1 &&
                _date.length <= 13 &&
                isNaN(Number(_date)) &&
                Number(_date) > 0
              ) {
                while (_date.length < 13) {
                  _date = _date + '0'
                }
              } else if (_date.length === 1) {
                _date = new Date().getTime()
              }
              return timestampToDate(_date, 'date')
            }
          }
        }
      }
      return { template }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('更新时间'),
    template: () => {
      const template = {
        template: `<span>{{ data.modifyDate | transFormValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transFormValue(e) {
            let _date = e
            if (!isNaN(_date)) {
              if (Number(_date) < 0) {
                _date = _date.replace(/-/g, '')
              }
              if (
                _date.length > 1 &&
                _date.length <= 13 &&
                isNaN(Number(_date)) &&
                Number(_date) > 0
              ) {
                while (_date.length < 13) {
                  _date = _date + '0'
                }
              } else if (_date.length === 1) {
                _date = new Date().getTime()
              }
              return timestampToDate(_date, 'date')
            }
          }
        }
      }
      return { template }
    }
  }
]

export const overdueColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'docNo',
    headerText: i18n.t('单据号'),
    cellTools: []
  },
  {
    width: 120,
    field: 'status',
    headerText: i18n.t('单据状态'),
    template: () => {
      const template = {
        template: `<span>{{ data.status }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: StatusOptions,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('草稿'),
        11: i18n.t('待提交'),
        15: i18n.t('采方驳回'),
        20: i18n.t('待确认'),
        30: i18n.t('审批中'),
        40: i18n.t('审批驳回'),
        50: i18n.t('审批通过'),
        60: i18n.t('审批废弃'),
        70: i18n.t('审批撤回')
      }
    }
  },
  {
    width: 100,
    field: 'companyType',
    headerText: i18n.t('公司类型'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { text: i18n.t('空调'), value: 'KT' },
        { text: i18n.t('万颗子'), value: 'WKZ' },
        { text: i18n.t('白电'), value: 'BD' }
      ],
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: {
        KT: i18n.t('空调'),
        WKZ: i18n.t('万颗子'),
        BD: i18n.t('白电')
      }
    }
  },
  {
    width: 180,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: 200,
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 160,
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: 200,
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    width: 120,
    field: 'categStatus',
    headerText: i18n.t('品类状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        3: i18n.t('新合格'),
        4: i18n.t('临时'),
        10: i18n.t('合格'),
        11: i18n.t('预合格'),
        20: i18n.t('冻结'),
        30: i18n.t('黑名单'),
        40: i18n.t('退出')
      }
    }
  },
  {
    width: 150,
    field: 'sqeName',
    headerText: i18n.t('SQE负责人')
  },
  {
    width: 150,
    field: 'testTypeName',
    headerText: i18n.t('测试类型')
  },
  {
    width: 150,
    field: 'testOrg',
    headerText: i18n.t('测试机构')
  },
  {
    width: 180,
    field: 'reportTime',
    headerText: i18n.t('出具报告日期'),
    template: () => {
      const template = {
        template: `<span>{{ data.reportTime | transFormValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transFormValue(e) {
            let _date = e
            if (!isNaN(_date)) {
              if (Number(_date) < 0) {
                _date = _date.replace(/-/g, '')
              }
              if (_date.length > 1 && _date.length <= 13 && Number(_date) > 0) {
                while (_date.length < 13) {
                  _date = _date + '0'
                }
              } else if (_date.length === 1) {
                _date = new Date().getTime()
              }
              return timestampToDate(_date, 'date')
            }
          }
        }
      }
      return { template }
    }
  },
  {
    width: 180,
    field: 'nextReportTime',
    headerText: i18n.t('下次报告更新日期'),
    template: () => {
      const template = {
        template: `<span>{{ data.nextReportTime | transFormValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transFormValue(e) {
            let _date = e
            if (!isNaN(_date)) {
              if (Number(_date) < 0) {
                _date = _date.replace(/-/g, '')
              }
              if (
                _date.length > 1 &&
                _date.length <= 13 &&
                isNaN(Number(_date)) &&
                Number(_date) > 0
              ) {
                while (_date.length < 13) {
                  _date = _date + '0'
                }
              } else if (_date.length === 1) {
                _date = new Date().getTime()
              }
              return timestampToDate(_date, 'date')
            }
          }
        }
      }
      return { template }
    }
  },
  {
    width: 200,
    field: 'file',
    headerText: i18n.t('报告附件'),
    template: () => {
      const template = {
        template: `<div>
            <input
              ref="file"
              type="file"
              style="display: none"
              @change="chooseFiles"
              multiple="multiple"
              accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar, .pdf, .PDF"
            />
            <p
              style="margin-top:5px;"
              v-for="(item,index) in data.harmfulCategoryFileRequestList"
              :key="index"
            >
              <a @click="preview(item)">{{item.fileName}}</a>
            </p>
          </div>`,
        data() {
          return {
            data: {
              harmfulCategoryFileRequestList: []
            },
            sceneList: [],
            allowFileType: [
              'xls',
              'xlsx',
              'doc',
              'docx',
              'pdf',
              'ppt',
              'pptx',
              'png',
              'jpg',
              'zip',
              'rar'
            ]
          }
        },
        computed: {
          isEdit() {
            return this.$route.query.isEdit === 'true'
          }
        },
        mounted() {
          // if (this.data.harmfulCategoryFileRequestList?.length > 0) {
          //   this.data.harmfulCategoryFileRequestList =
          // }
          this.$API.hazardousSubstances
            .getHazardousSubstancesDetailId({ id: this.data['id'] })
            .then((res) => {
              const _list = res.data ?? []
              const _harmfulCategoryFileRequestList = []
              const _map = new Map()
              _list.map((item) => {
                if (!_map.get(item.fileName)) {
                  _harmfulCategoryFileRequestList.push(item)
                  _map.set(item.fileName, true)
                }
              })
              this.data.harmfulCategoryFileRequestList = _harmfulCategoryFileRequestList
            })
        },
        methods: {
          chooseFiles(data) {
            this.$loading()
            console.log('data.targetdata.targetdata.target', data.target)
            let { files } = data.target
            files = Object.values(files)
            let params = {
              type: 'array',
              limit: 50 * 1024,
              msg: this.$t('单个文件，限制50M')
            }
            if (files.length < 1) {
              this.$hloading()
              // 您未选择需要上传的文件
              return
            } else if (files.length > 5) {
              this.$hloading()
              this.$toast({
                content: this.$t('一次性最多选择5个文件'),
                type: 'warning'
              })
              return
            }
            let bol = files.some((item) => {
              let _tempInfo = item.name.split('.')
              return (
                _tempInfo.length < 2 ||
                !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
              )
            })
            if (bol) {
              this.$toast({
                content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
                type: 'warning'
              })
              this.$hloading()
              return
            }
            bol = files.some((item) => {
              return item.size > params.limit * 1024
            })
            if (bol) {
              this.$hloading()
              this.$toast({
                content: params.msg
              })
              return
            }
            this.$refs.file.value = ''
            this.uploadFile(files)
          },
          uploadFile(files) {
            const _dataSource = this.$parent.dataSource
            const _lineObj = _dataSource.filter((item) => item.id === this.data.id)[0]
            let arr = []
            files.forEach((item) => {
              let _data = new FormData()
              _data.append('UploadFiles', item)
              _data.append('useType', 1)
              arr.push(this.$API.SupplierPunishment.fileUpload(_data))
            })
            Promise.all(arr).then((res) => {
              res.forEach((item) => {
                if (item.code == 200) {
                  item.data.fileId = item.data.fileId ? item.data.fileId : item.data.id
                  item.data.fileUrl = item.data.fileUrl ? item.data.fileUrl : item.data.url
                  const fileInfo = item.data
                  delete fileInfo.id
                  if (
                    this.data.harmfulCategoryFileRequestList != null &&
                    !!_lineObj.harmfulCategoryFileRequestList
                  ) {
                    this.data.harmfulCategoryFileRequestList.push(fileInfo)
                    _lineObj.harmfulCategoryFileRequestList.push(fileInfo)
                  } else {
                    this.data.harmfulCategoryFileRequestList = [fileInfo]
                    _lineObj.harmfulCategoryFileRequestList = [fileInfo]
                  }
                  console.log('scoringFilelnfos123', this.data.harmfulCategoryFileRequestList)
                }
              })
              this.$hloading()
              this.$parent.$emit('cellEdit', this.data, 'harmfulCategoryFileRequestList')
            })
          },
          preview(item) {
            let params = {
              id: item.fileId,
              useType: 1
            }
            this.$API.SupplierPunishment.filepreview(params).then((res) => {
              window.open(res.data)
            })
          }
        }
      }
      return { template }
    }
  },
  {
    width: 200,
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const roHsColumnData = [
  {
    width: 100,
    field: 'companyType',
    headerText: i18n.t('公司类型'),
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { text: i18n.t('空调'), value: 'KT' },
        { text: i18n.t('万颗子'), value: 'WKZ' },
        { text: i18n.t('白电'), value: 'BD' }
      ],
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: {
        KT: i18n.t('空调'),
        WKZ: i18n.t('万颗子'),
        BD: i18n.t('白电')
      }
    }
  },
  {
    width: 160,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: 200,
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 160,
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: 200,
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    width: 120,
    field: 'categStatus',
    headerText: i18n.t('品类状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        3: i18n.t('新合格'),
        4: i18n.t('临时'),
        10: i18n.t('合格'),
        11: i18n.t('预合格'),
        20: i18n.t('冻结'),
        30: i18n.t('黑名单'),
        40: i18n.t('退出')
      }
    }
  },
  {
    width: 150,
    field: 'testTypeName',
    headerText: i18n.t('测试类型')
  },
  {
    width: 120,
    field: 'pb',
    headerText: i18n.t('Pb(ppm)')
  },
  {
    width: 120,
    field: 'cd',
    headerText: i18n.t('Cd(ppm)')
  },
  {
    width: 120,
    field: 'hg',
    headerText: i18n.t('Hg(ppm)')
  },
  {
    width: 250,
    field: 'cr6',
    headerText: i18n.t('Cr6+(ppm)')
  },
  {
    width: 120,
    field: 'pbb',
    headerText: i18n.t('PBB(ppm)')
  },
  {
    width: 120,
    field: 'pbde',
    headerText: i18n.t('pBDE(ppm)')
  },
  {
    width: 120,
    field: 'dehp',
    headerText: i18n.t('DEHP(ppm)')
  },
  {
    width: 120,
    field: 'dibp',
    headerText: i18n.t('DIBP(ppm)')
  },
  {
    width: 120,
    field: 'bbp',
    headerText: i18n.t('BBP(ppm)')
  },
  {
    width: 120,
    field: 'dbp',
    headerText: i18n.t('DBP(ppm)')
  }
]
