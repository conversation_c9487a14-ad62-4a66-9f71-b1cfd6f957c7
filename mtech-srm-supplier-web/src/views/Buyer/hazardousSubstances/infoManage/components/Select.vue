<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :filtering="filtering"
      :disabled="data.id && data.column.field == 'categoryName'"
      :allow-filtering="true"
    ></mt-select>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import { StatusOptions, Rohs2Options } from './enum'
import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: 'text', value: 'value' },

      dataSource: [],

      userArrList: []
    }
  },

  mounted() {
    console.log('moutedmoutedmoutedmoutedmouted', this.data)
    this.filtering = utils.debounce(this.filtering, 1000)
    const _enum = [
      'isEffective',
      'euRoHs',
      'euReachSvHc',
      'reachFile',
      'germanyPaHs',
      'usCalifornia65',
      'batteryDirective',
      'euPackagingDirective',
      'chinaVoc',
      'frenchMineralOil',
      'pfas',
      'pops',
      'usTsca'
    ]

    //品类编码
    if (this.data.column.field === 'categoryCode') {
      this.$loading()
      let params = {
        fuzzyNameOrCode: this.data.categoryCode
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.dataSource = _data
          this.fields = { text: 'text', value: 'categoryCode' }
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    }
    if (_enum.includes(this.data.column.field)) {
      this.dataSource = Rohs2Options
    } else if (this.data.column.field === 'isEffective') {
      this.dataSource = StatusOptions
    }
  },

  methods: {
    // 模糊搜索
    filtering(e) {
      if (this.data.column.field === 'categoryCode') {
        this.$loading()
        let params = {
          fuzzyNameOrCode: e.text
        }
        this.$API.maintainOutsideItem
          .criteriaQuery(params)
          .then((res) => {
            res.data.map((item) => {
              item.text = item.categoryCode + '-' + item.categoryName
            })
            e.updateData(res.data)
            this.$hloading()
          })
          .catch((error) => {
            this.$hloading()
            this.$toast({
              content: error.msg || this.$t('查询失败，请重试'),
              type: 'error'
            })
          })
      }
    },

    startOpen() {
      console.log(this.data, '下拉打开时最新的行数据')
    },

    selectChange(e) {
      if (this.data.column.field === 'categoryCode') {
        let params = {
          fieldCode: 'categoryName',
          itemInfo: {
            categoryId: e.itemData.id,
            categoryCode: e.itemData.categoryCode,
            categoryName: e.itemData.categoryName
          }
        }
        this.$parent.$emit('parentcategoryCode', params)
        this.$bus.$emit('maintainOutsideItemcategoryCode', e.itemData)
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('crrEnterpriseNamebus')
    this.$bus.$off('crrCategoryCodebus')
    this.$bus.$off('crrCategoryTypebus')
    this.$bus.$off('maintainOutsideItemcategoryCode')
  }
}
</script>
