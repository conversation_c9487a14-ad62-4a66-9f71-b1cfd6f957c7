<template>
  <mt-dialog
    ref="dialog"
    css-class="upload-dialogs"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uipload-box">
      <div class="standardTemplate">
        <!-- {{ $t("下载标准导入模板") }} -->
        <!-- <a
          :href="`${prefixPath}/static/tcl-直送销售维护导入模板.xlsx`"
          target="_blank"
          :download="`tcl-直送销售维护导入模板.xlsx`"
          >{{ $t("下载标准导入模板") }}</a
        > -->
      </div>
      <div id="drop" class="droparea">
        <div class="click-upbox" id="browse">
          <div class="plus-icon"></div>
          <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
          <input type="file" class="upload-input" @change="chooseFiles" />
        </div>
        <div class="warn-text">
          <span>{{ $t('注：文件最大不可超过50M， 文件格式仅支持（.xlsx）') }}</span>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      prefixPath: window.__POWERED_BY_QIANKUN__ ? '/masterData' : '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
        // {
        //   click: this.confirm,
        //   buttonModel: { isPrimary: "true", content: "上传" },
        // },
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons
        // cssClass: ("small-dialogs " + this.modalData.cssClass).trim(),
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    chooseFiles(event) {
      // console.log(event)
      const _files = event.target.files
      // console.log(_files['0'].name, 'fils0')
      const lastIndex = _files['0'].name.lastIndexOf('.')
      const text = _files['0'].name.substring(lastIndex + 1, _files['0'].name.length)

      if (text !== 'xlsx') {
        // console.log('格式为非xslx')
        this.$toast({
          content: this.$t('您选择需要上传的文件不是.xslx格式')
        })
        return
      }
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择的文件不是')
        })
        return
      }
      const _data = new FormData()
      let isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('multipartFile', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
      }
      // this.$store.commit('startLoading')
      console.log(_data) //exportData importData
      // console.log(this.$API.material.importData)
      this.$API.categoryRelationshipReport.import(_data).then(() => {
        // console.log('成功')
        this.$toast({ content: this.$t('上传成功'), type: 'success' })
        this.$emit('confirm-function')
      })
    },

    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-upload-file {
  display: none;
}
/deep/ .e-dlg-content {
  padding: 10px;
  overflow: hidden;
}
.upload-dialogs {
  width: 500px;
  height: 300px;
  .e-upload {
    float: none !important;
    border: none !important;
  }
  .e-file-select-wrap {
    display: none !important;
  }
  .e-upload-files {
    background: rgba(250, 250, 250, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;

    .e-upload-file-list {
      min-height: 16px !important;
      height: 16px !important;
      line-height: 16px !important;
      border-bottom: none !important;
    }

    .up-loadcontainer {
      .wrapper {
        display: flex;
        align-items: center;
      }
      .file-name {
        width: 80px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 0;
        padding-top: 0;
        color: #292929;
      }
      .upload-size {
        color: #9a9a9a !important;
        padding-right: 10px;
      }
      .upload-status {
        color: #0043a8 !important;
      }
      .upload-success {
        color: #6f982b !important;
      }
      .upload-failed {
        color: #ed5633 !important;
      }
    }
  }

  .uipload-box {
    width: 100%;
    .standardTemplate {
      width: 100%;
      text-align: center;
      padding-bottom: 20px;
      line-height: 30px;
      color: #44b6cd;
      cursor: pointer;
    }
    .droparea {
      background: rgba(251, 252, 253, 1);
      border: 1px dashed rgba(232, 232, 232, 1);
      border-radius: 4px;
      margin: 0 auto;
      padding: 28px 0;
      .click-upbox {
        cursor: pointer;
        position: relative;
        .upload-input {
          height: 100%;
          width: 100%;
          position: absolute;
          z-index: 2;
          top: 0;
          background: transparent;
          opacity: 0;
        }
        .plus-icon {
          width: 40px;
          height: 40px;
          margin: 0 auto;
          position: relative;
          &::after {
            content: ' ';
            display: inline-block;
            width: 2px;
            height: 40px;
            background: rgba(232, 232, 232, 1);
            border-radius: 100px;
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
          }
          &::before {
            content: ' ';
            display: inline-block;
            width: 40px;
            height: 2px;
            background: rgba(232, 232, 232, 1);
            border-radius: 100px;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .plus-txt {
          width: 100%;
          text-align: center;
          margin-top: 10px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(152, 170, 195, 1);
        }
      }
      .warn-text {
        width: 100%;
        text-align: center;
        padding: 0 10px;
        margin: 0 auto;
        margin-top: 10px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }
}
</style>
