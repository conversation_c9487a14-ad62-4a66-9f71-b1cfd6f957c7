<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
      @parentcategoryCode="parentcategoryCode"
      @handleCustomReset="handleCustomReset"
    >
      <!-- <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
              <mt-select
                v-model="searchFormModel.categoryCode"
                css-class="rule-element"
                :data-source="categoryCodeENUM"
                :show-clear-button="true"
                :filtering="filtering"
                :allow-filtering="true"
                @change="selectCategory"
                :fields="{
                  text: 'text',
                  value: 'categoryCode'
                }"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
              <mt-input v-model="searchFormModel.categoryName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="levelCode" :label="$t('层级')" label-style="top">
              <mt-select
                v-model="searchFormModel.levelCode"
                css-class="rule-element"
                :data-source="levelENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="orgCode" :label="$t('事业部')" label-style="top">
              <mt-select
                v-model="searchFormModel.orgCode"
                css-class="rule-element"
                :data-source="bussinessUnitENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="valid" :label="$t('是否生效')" label-style="top">
              <mt-select
                v-model="searchFormModel.valid"
                css-class="rule-element"
                :data-source="validENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="domainCode" :label="$t('归属领域')" label-style="top">
              <mt-select
                v-model="searchFormModel.domainCode"
                css-class="rule-element"
                :data-source="domainENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="quoteMethod" :label="$t('询报价模式')" label-style="top">
              <mt-select
                v-model="searchFormModel.quoteMethod"
                css-class="rule-element"
                :data-source="quoteMethodENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="effectiveMethod" :label="$t('供应商引入')" label-style="top">
              <mt-select
                v-model="searchFormModel.effectiveMethod"
                css-class="rule-element"
                :data-source="effectiveMethodENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="deliveryMethod" :label="$t('交货模式')" label-style="top">
              <mt-select
                v-model="searchFormModel.deliveryMethod"
                css-class="rule-element"
                :data-source="deliveryMethodENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="gradingResult" :label="$t('分层分级结果')" label-style="top">
              <mt-select
                v-model="searchFormModel.gradingResult"
                css-class="rule-element"
                :data-source="gradingResultENUM"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="user" :label="$t('人员')" label-style="top">
              <mt-input v-model="searchFormModel.user" maxlenght="200"></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </template> -->
    </mt-template-page>
  </div>
</template>

<script>
// import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
import { toolbar, columnData } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils.js'
import {
  levelENUM,
  domainENUM,
  quoteMethodENUM,
  mapSelectionENUM,
  costMouldENUM,
  effectiveMethodENUM,
  deliveryMethodENUM,
  bussinessUnitENUM,
  gradingResultENUM
  // getCurPerson
} from './components/enum.js'
import cloneDeep from 'lodash/cloneDeep'
import utils from '@/utils/utils'

export default {
  components: {
    // UploadExcelDialog
  },
  // provide() {
  //   return {
  //     searchFormModelList: [this.searchFormModel]
  //   }
  // },
  data() {
    return {
      searchFormModel: {}, //自定义模板
      tempPersonList: [],
      categoryCodeObj: {},
      pageConfig: [
        {
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: 'c4224375-62a7-44fd-ab9c-88e558d75e79',
          grid: {
            columnData,
            // commandClick: this.commandClick, //配置单击进入行编辑
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowEditing: true, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/tenant/harmful/category/config/query'
            }
          }
        }
      ],
      levelENUM,
      domainENUM,
      quoteMethodENUM,
      mapSelectionENUM,
      costMouldENUM,
      effectiveMethodENUM,
      deliveryMethodENUM,
      bussinessUnitENUM,
      gradingResultENUM,
      // getCurPerson,
      //------------------------>
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'categoryRelationshipReport',
        templateUrl: '', // 下载模板接口方法名
        uploadUrl: 'import', // 上传接口方法名
        file: 'multipartFile' //后端接收参数名
      },
      categoryCodeENUM: [] //品类数据相关值集
    }
  },
  computed: {},
  mounted() {
    // this.init()
    this.filtering = utils.debounce(this.filtering, 1000)

    this.getCategoryInfos()
  },
  methods: {
    // 获取数据
    async init() {
      const params = {
        page: { current: 1, size: 10000 }
      }
      const res = await this.$API.categoryConfigurationReport.getPageInfo(params)

      res.data.records = res.data.records.map((item) => {
        const personConfig = JSON.parse(item.personConfig)
        for (let key in personConfig) {
          const curFieldName = key.replace('_NAME', '')
          item[curFieldName] = personConfig[key]
        }
        const templateConfig = JSON.parse(item.templateConfig)
        for (let key in templateConfig) {
          item[key] = templateConfig[key]
        }
        return item
      })
      console.log('resresres', res)
      this.pageConfig[0].grid.dataSource = res.data.records
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 获取品类相关数据
    getCategoryInfos() {
      this.$API.maintainOutsideItem
        .criteriaQuery()
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.categoryCodeENUM = _data
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    // 模糊搜索品类
    filtering(e) {
      this.$loading()
      let params = {
        fuzzyNameOrCode: e.text
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          res.data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          e.updateData(res.data)
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    // 品类下拉
    selectCategory(e) {
      const { itemData } = e
      this.searchFormModel.categoryName = itemData.categoryName
    },
    // 同步数据
    parentcategoryCode(val) {
      if (val.fieldCode === 'categoryName') {
        Object.assign(this.categoryCodeObj, val.itemInfo || {})
      } else if (this.tempPersonList === null) {
        this.tempPersonList = [val]
      } else if (this.tempPersonList.some((item) => item.fieldCode === val.fieldCode)) {
        const index = this.tempPersonList.findIndex((item) => item.fieldCode === val.fieldCode)
        this.tempPersonList.splice(index, 1)
        this.tempPersonList.push(val)
      } else {
        this.tempPersonList.push(val)
      }
      console.log(
        'parentcategoryCodeparentcategoryCodeparentcategoryCodeparentcategoryCode',
        this.tempPersonList
      )
    },
    commandClick(args) {
      if (args.commandColumn.type === 'Deletes') {
        // 如果不存在id,直接删除(获取不到rowIndex,暂定刷新)
        if (!args.rowData.id) {
          this.$refs.tepPage.refreshCurrentGridData()
          return
        }
        let params = {
          id: args.rowData.id
        }
        this.$API.categoryRelationshipReport.remove(params).then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
        })
      } else if (args.commandColumn.type === 'history') {
        this.$dialog({
          modal: () =>
            import(/* webpackChunkName: "components/upload" */ './components/history.vue'),
          data: {
            title: this.$t('历史记录'),
            id: args.rowData.id
          },
          success: () => {}
        })
      }
    },
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (
        records.length <= 0 &&
        ['Delete', 'ConfigActive', 'ConfigInactive'].includes(e.toolbar.id)
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      } else if (e.toolbar.id === 'upload') {
        this.handleClickToolBarUpload()
      } else if (e.toolbar.id === 'ConfigActive') {
        // 生效
        this.handleClickToolBarConfigActive(records)
      } else if (e.toolbar.id === 'ConfigInactive') {
        // 失效
        this.handleClickToolBarConfigInactive(records)
      } else if (e.toolbar.id === 'Download') {
        this.handleClickToolBarDownload()
      } else if (e.toolbar.id === 'Delete') {
        this.handleClickToolBarDelete(records)
      }
    },
    handleClickToolBarConfigActive(list = []) {
      // for (let i = 0; i < list.length; i++) {
      //   if (list[i].isEffective === 1) {
      //     this.$toast({
      //       type: 'error',
      //       content: this.$t('已生效的数据不能二次生效')
      //     })
      //     return
      //   }
      // }
      const _ids = list.map((item) => item.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .effectiveHazardousSubstancesInfo(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.refreshPage()
          this.$store.commit('endLoading')
        })
    },
    handleClickToolBarConfigInactive(list = []) {
      const _ids = list.map((item) => item.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .expireHazardousSubstancesInfo(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.refreshPage()
          this.$store.commit('endLoading')
        })
    },
    handleClickToolBarAdd() {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // 删除
    handleClickToolBarDelete(val) {
      if (val != null && val.some((item) => item.id != null)) {
        const ids = val.map((item) => item.id).filter((item) => item !== null)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除数据？')
          },
          success: () => {
            this.$API.hazardousSubstances.deleteHazardousSubstancesInfo(ids).then((res) => {
              if (res && res.code === 200) {
                this.$toast({
                  type: 'success',
                  content: this.$t(res.msg)
                })
                this.refreshPage()
              }
            })
          }
        })
      }
    },
    // refresh页面
    refreshPage() {
      this.$refs.tepPage.refreshCurrentGridData()
    },
    // 导入
    handleClickToolBarUpload() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'file',
          importApi: this.$API.hazardousSubstances.importHazardousSubstancesInfo,
          downloadTemplateApi: this.$API.hazardousSubstances.importTemplateDown
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
        }
      })
    },
    // 导出
    handleClickToolBarDownload() {
      const _rule = this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: _rule.condition || '',
        page: { current: 1, size: 10000 },
        rules: _rule.rules || []
      }
      this.$API.hazardousSubstances.exportHazardousSubstancesList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.handleSave(data, rowIndex)
      }
    },
    // 保存
    handleSave(rowData, rowIndex = 0) {
      console.log('rowDatarowData', rowData)
      this.$API.hazardousSubstances
        .addHazardousSubstancesInfo([rowData])
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.tepPage.refreshCurrentGridData()
          } else {
            this.startEdit(rowIndex)
          }
        })
        .catch(() => {
          this.startEdit(rowIndex)
        })
    },
    startEdit(rowIndex) {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    actionBegin(args) {
      const { requestType, data } = args
      console.log('datadatadata', data, requestType)
      if (requestType === 'save') {
        const validateMap = {
          categoryCode: {
            value: data.categoryCode,
            msg: this.$t('请填写品类编码')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background-color: #fff;
  .e-color {
    color: #000;
  }
}
</style>
