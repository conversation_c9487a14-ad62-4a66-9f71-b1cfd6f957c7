import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
import InputView from '../components/inputView.vue'
import Vue from 'vue'
import { Rohs2Options, StatusOptions } from '../components/enum.js'

export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Delete', icon: 'icon_solid_Createorder', title: i18n.t('删除') },
  { id: 'ConfigActive', icon: 'icon_solid_Activateorder', title: i18n.t('生效') },
  { id: 'ConfigInactive', icon: 'icon_solid_Pauseorder', title: i18n.t('失效') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
]

const acOptions = {
  0: i18n.t('未启用'),
  1: i18n.t('启用')
}

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: 120,
    field: 'isEffective',
    headerText: i18n.t('状态'),
    allowEditing: false,
    template: () => {
      const template = {
        template: `<span>{{ data.isEffective }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      const template = {
        template: `<span>{{ data.isEffective | transformValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transformValue(e) {
            return StatusOptions.filter((item) => item.value === e)[0].text
          }
        }
      }
      return { template }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: StatusOptions,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('生效'),
        2: i18n.t('失效')
      }
    }
  },
  {
    width: 250,
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('categoryCodeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('品类编码')}}</span>
              </div>
            `
        })
      }
    },

    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    editTemplate: () => {
      return { template: InputView }
    },
    allowEditing: false
  },

  {
    field: 'euRoHs',
    headerText: i18n.t('欧盟ROHS2'),
    template: () => {
      const template = {
        template: `<span>{{ data.euRoHs }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 150,
    field: 'euReachSvHc',
    headerText: i18n.t('欧盟REACHSVHC'),
    template: () => {
      const template = {
        template: `<span>{{ data.euReachSvHc }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'reachFile',
    headerText: i18n.t('REACH 附件XVII'),
    template: () => {
      const template = {
        template: `<span>{{ data.reachFile }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'pops',
    headerText: i18n.t('POPs'),
    template: () => {
      const template = {
        template: `<span>{{ data.pops }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'germanyPaHs',
    headerText: i18n.t('德国PAHs'),
    template: () => {
      const template = {
        template: `<span>{{ data.germanyPaHs }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'usTsca',
    headerText: i18n.t('美国TSCA'),
    template: () => {
      const template = {
        template: `<span>{{ data.usTsca }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'usCalifornia65',
    headerText: i18n.t('美国加州65'),
    template: () => {
      const template = {
        template: `<span>{{ data.usCalifornia65 }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'batteryDirective',
    headerText: i18n.t('电池指令'),
    template: () => {
      const template = {
        template: `<span>{{ data.batteryDirective }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'euPackagingDirective',
    headerText: i18n.t('欧盟包装指令'),
    template: () => {
      const template = {
        template: `<span>{{ data.euPackagingDirective }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'chinaVoc',
    headerText: i18n.t('中国VOC'),
    template: () => {
      const template = {
        template: `<span>{{ data.chinaVoc }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'frenchMineralOil',
    headerText: i18n.t('法国矿物油'),
    template: () => {
      const template = {
        template: `<span>{{ data.frenchMineralOil }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'pfas',
    headerText: i18n.t('PFAS'),
    template: () => {
      const template = {
        template: `<span>{{ data.pfas }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    width: 120,
    field: 'frenchFoodGrade',
    headerText: i18n.t('法国食品级'),
    template: () => {
      const template = {
        template: `<span>{{ data.frenchFoodGrade }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    },
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'select',
      dataSource: Rohs2Options,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: acOptions
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    // ignore: true,
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('更新时间'),
    ignore: true,
    allowEditing: false
  }
]
