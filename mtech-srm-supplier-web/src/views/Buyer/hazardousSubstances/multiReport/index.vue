<!-- 有害物质多维度统计报表 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('统计维度')" prop="statisticalDimension">
          <mt-multi-select
            v-model="searchFormModel.statisticalDimension"
            :data-source="statisticalDimensionOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'text', value: 'value' }"
          />
        </mt-form-item>
        <mt-form-item prop="companyType" :label="$t('公司类型')">
          <mt-select
            v-model="searchFormModel.companyType"
            :data-source="[
              { text: $t('空调'), value: 'KT' },
              { text: $t('万颗子'), value: 'WKZ' },
              { text: $t('白电'), value: 'BD' }
            ]"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('公司')" prop="companyCode">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG01', 'ORG02']
            }"
            multiple
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
            multiple
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类编码')" prop="categoryCode">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类名称')" prop="categoryName">
          <mt-input
            v-model="searchFormModel.categoryName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('SQE负责人账号')" prop="sqeAcct">
          <mt-input
            v-model="searchFormModel.sqeAcct"
            :placeholder="$t('仅支持单个精准查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('SQE负责人名称')" prop="sqeName">
          <mt-input
            v-model="searchFormModel.sqeName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="5e0a9234-4223-4dbf-ab06-3990384bf0ed"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :tooltip-config="tooltipConfig"
      keep-source
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statisticalDimensionOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { CollapseSearch, ScTable, RemoteAutocomplete },
  data() {
    return {
      searchFormModel: {
        statisticalDimension: []
      },
      searchFormRules: {},
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statisticalDimensionOptions,

      tooltipConfig: {
        showAll: true,
        enterable: true,
        contentMethod: ({ column }) => {
          const { field } = column
          // 重写默认的提示内容
          if (field === 'updateTimelinessRate') {
            return `及时率=有效期内的报告总数/应及时更新的报告总数
            其中：有效期内的报告总数=所选统计维度对应有效期内的报告总数
            应及时更新的报告总数=所选统计维度对应应及时更新的报告总数
            `
          } else if (field === 'overdue') {
            return `超期的报告总数`
          } else if (field === 'warm') {
            return `有效期在30天内的预警总数（0≤有效期≤30天）`
          } else if (field === 'toBeSubmitted') {
            return `需供应商提交的报告总数`
          } else if (field === 'pendingReview') {
            return `待确认及审批中的报告总数`
          } else if (field === 'rate') {
            // 返回空字符串，控制单元格不显示提示内容
            return ''
          }
          // 其余的单元格使用默认行为
          return null
        }
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      let toolbarData = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      return toolbarData
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[field + 'End'] =
          dayjs(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')).valueOf() + 999 // + 999是后端要求
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          if (key === 'statisticalDimension') {
            this.searchFormModel[key] = []
          } else {
            this.searchFormModel[key] = null
          }
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.hazardousSubstances.multiStatistReportPageApi
      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
        this.handleColumns()
      }
    },
    handleColumns() {
      let currentColumn = columnData
      if (this.searchFormModel.statisticalDimension.length !== 0) {
        currentColumn.forEach((item) => {
          if (item.field === 'sqeName') {
            item.hide = !this.searchFormModel.statisticalDimension.includes(0)
          } else if (item.field === 'companyCode') {
            item.hide = !this.searchFormModel.statisticalDimension.includes(1)
          } else if (item.field === 'supplierCode') {
            item.hide = !this.searchFormModel.statisticalDimension.includes(2)
          } else if (item.field === 'categoryCode') {
            item.hide = !this.searchFormModel.statisticalDimension.includes(3)
          } else if (item.field === 'testTypeName') {
            item.hide = !this.searchFormModel.statisticalDimension.includes(4)
          } else {
            item.hide = false
          }
        })
      } else {
        currentColumn.forEach((item) => {
          item.hide = false
        })
      }
      currentColumn = currentColumn.filter((v) => !v.hide)
      this.columns = currentColumn
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.hazardousSubstances.multiStatistReportExportApi
      api(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
