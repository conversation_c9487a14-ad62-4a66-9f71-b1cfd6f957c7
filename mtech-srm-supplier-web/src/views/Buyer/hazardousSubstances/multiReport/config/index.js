import { i18n } from '@/main.js'

export const statisticalDimensionOptions = [
  { text: i18n.t('SQE工程师'), value: 0 },
  { text: i18n.t('公司'), value: 1 },
  { text: i18n.t('供应商'), value: 2 },
  { text: i18n.t('品类'), value: 3 },
  { text: i18n.t('测试类型'), value: 4 }
]

export const companyTypeOptions = [
  { text: i18n.t('空调'), value: 'KT' },
  { text: i18n.t('万颗子'), value: 'WKZ' },
  { text: i18n.t('白电'), value: 'BD' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'sqeName',
    title: i18n.t('SQE负责人')
  },
  {
    width: 100,
    field: 'companyType',
    headerText: i18n.t('公司类型'),
    formatter: ({ cellValue }) => {
      let item = companyTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.categoryName : ''
    }
  },
  {
    field: 'testTypeName',
    title: i18n.t('测试类型'),
    minWidth: 160
  },
  {
    field: 'updateTimelinessRate',
    title: i18n.t('有害物质管理更新及时率（%）'),
    minWidth: 160
  },
  {
    field: 'overdue',
    title: i18n.t('超期（份）'),
    minWidth: 160
  },
  {
    field: 'warm',
    title: i18n.t('预警（份）'),
    minWidth: 160
  },
  {
    field: 'toBeSubmitted',
    title: i18n.t('供应商待提交（份）'),
    minWidth: 160
  },
  {
    field: 'pendingReview',
    title: i18n.t('待审核（份）')
  }
]
