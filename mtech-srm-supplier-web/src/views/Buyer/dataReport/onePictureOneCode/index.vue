<template>
  <iframe :src="iframeUrl" frameborder="0" class="star-picture-iframe"></iframe>
</template>
<script>
export default {
  data() {
    return {
      iframeUrl: ''
    }
  },
  created() {
    // const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || {})
    this.iframeUrl = `https://data.tcl.com/databi/pc/dashboard/publish/cc6f5f19-78a2-458f-8e60-f8b540c596cc?teamwork_src=1&gcmt=ut_HLVHOBbo2mRZnNJHbvogfkIdLL97BFygYqP&accessToken=ut_HLVHOBbo2mRZnNJHbvogfkIdLL97BFygYqP&lang=zh_CN`
  }
}
</script>
<style lang="scss" scoped>
.star-picture-iframe {
  width: 100%;
  height: calc(100% - 30px);
  margin-top: 16px;
}
</style>
