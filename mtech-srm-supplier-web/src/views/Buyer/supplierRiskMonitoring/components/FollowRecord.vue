<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :height="440"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <sc-table
        ref="scTableRef"
        row-id="id"
        show-overflow
        keep-source
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        :fix-height="200"
        :edit-config="editConfig"
        :edit-rules="editRules"
        :is-show-right-btn="false"
      >
        <template #custom-tools>
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            v-bind="item"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
      </sc-table>

      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { before } from 'lodash'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],

      loading: false,
      tableData: [],
      toolbar: [
        {
          code: 'add',
          name: this.$t('添加'),
          status: 'info',
          loading: false
        }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      editRules: {
        followRemarkDetail: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    id() {
      return this.modalData.id
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        {
          field: 'followRemarkDetail',
          title: this.$t('跟进备注'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.followRemarkDetail}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'file',
          title: this.$t('附件'),
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={'color: #2783fe; cursor: pointer;'}
                  onClick={() => this.handleUpload(row)}>
                  {this.$t('附件查看')}
                </div>
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        },
        {
          field: 'operation',
          title: this.$t('操作'),
          slots: {
            default: ({ row }) => {
              return [
                row.id.includes('row_') ? (
                  <div
                    style={'color: #2783fe; cursor: pointer;'}
                    onClick={() => this.handleDelete(row)}>
                    {this.$t('删除')}
                  </div>
                ) : null
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: ({ row }) => {
          return row.id.includes('row_')
        }
      }
    }
  },
  mounted() {
    this.handleSearch()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          id: this.id,
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          }
        }

        const res = await this.$API.supplierRiskMonitoring.findExternalRiskMonitoringFollowRecord(
          params
        )

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    async handleClickToolBar(item) {
      const actionMap = {
        add: () => this.handleAdd()
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    async handleAdd() {
      const item = {}
      this.tableRef.insert([item])
      this.$nextTick(() => {
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },

    async handleDelete(row) {
      this.tableRef.remove(row)
    },

    handleUpload(row) {
      this.$dialog({
        modal: () => import('./FileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          fileList: row?.fileInfoResponseList || [],
          canEdit: row.id.includes('row_')
        },
        failed: (res) => {
          row.fileInfoResponseList = res
        }
      })
    },

    //点击确认
    async confirm() {
      const addRecords = this.tableRef.getInsertRecords()
      if (addRecords.length > 0) {
        const valid = await this.tableRef.validate(addRecords)
        if (valid) {
          this.$toast({ content: this.$t('请完成必填项后进行确定操作'), type: 'warning' })
          return
        }
        let params = {
          externalRiskFollowRecordSaveDetailRequestList: addRecords.map((item) => {
            return {
              externalRiskMonitoringPromptsId: this.id,
              followRemarkDetail: item.followRemarkDetail,
              fileInfoDTOList: item?.fileInfoResponseList || []
            }
          })
        }
        this.$emit('confirm-function', params)
      } else {
        this.$toast({ content: this.$t('请添加跟进记录'), type: 'warning' })
      }
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-main {
  .e-dlg-content {
    .dialog-content {
      padding: 10px;
    }
  }
}
</style>
