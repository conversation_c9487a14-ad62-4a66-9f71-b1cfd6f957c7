<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :height="440"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <sc-table
        ref="scTableRef"
        row-id="id"
        show-overflow
        keep-source
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        :fix-height="200"
        :is-show-right-btn="false"
      >
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],

      loading: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    tableData() {
      return this.modalData.tableData
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      const baseColumns = [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        }
      ]

      // 使用传入的列配置
      const typeColumns = this.modalData.columns || []

      // 合并基础列和类型特定列
      return [...baseColumns, ...typeColumns]
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-main {
  .e-dlg-content {
    .dialog-content {
      padding: 10px;
    }
  }
}
</style>
