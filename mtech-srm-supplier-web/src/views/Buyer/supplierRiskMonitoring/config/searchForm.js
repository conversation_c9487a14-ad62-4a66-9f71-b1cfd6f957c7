/**
 * 供应商风险监控搜索表单配置
 */
import { i18n } from '@/main.js'

export const getSearchFormItems = ({ riskTypeOptions, receivingTypeOptions, statusOptions }) => [
  {
    label: i18n.t('风险分类'),
    prop: 'riskTypeCode',
    component: 'mt-select',
    props: { placeholder: i18n.t('请选择'), dataSource: riskTypeOptions, showClearButton: true }
  },
  {
    label: i18n.t('SRM供应商编码'),
    prop: 'partnerCode',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('SAP供应商编码'),
    prop: 'supplierCode',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('供应商名称'),
    prop: 'supplierName',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('风险标签'),
    prop: 'riskLabel',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('类型'),
    prop: 'receivingTypeCode',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      dataSource: receivingTypeOptions,
      showClearButton: true
    }
  },
  {
    label: i18n.t('跟进状态'),
    prop: 'accordingStatus',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      dataSource: statusOptions,
      showClearButton: true
    }
  },
  {
    label: i18n.t('标题'),
    prop: 'title',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('内容'),
    prop: 'content',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('创建人'),
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('创建时间'),
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: i18n.t('请选择') }
  },
  {
    label: i18n.t('更新人'),
    prop: 'updateUserName',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('更新时间'),
    prop: 'updateTime',
    component: 'mt-date-range-picker',
    props: { placeholder: i18n.t('请选择') }
  }
]

export const getDetailSearchFormItems = ({ violationTypeOptions, resultOptions }) => [
  // {
  //   label: i18n.t('SAP编码'),
  //   prop: 'supplierCode',
  //   component: 'mt-input',
  //   props: { placeholder: i18n.t('请输入'), showClearButton: true }
  // },
  {
    label: i18n.t('SAP编码'),
    prop: 'supplierCodeList',
    component: 'RemoteAutocomplete',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      url: '/masterDataManagement/tenant/supplier/paged-query',
      multiple: true,
      fields: { text: 'supplierName', value: 'supplierCode' },
      searchFields: ['supplierName', 'supplierCode']
    }
  },
  {
    label: i18n.t('类型'),
    prop: 'violationType',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      dataSource: violationTypeOptions,
      showClearButton: true
    }
  },
  {
    label: i18n.t('结果'),
    prop: 'result',
    component: 'mt-select',
    props: { placeholder: i18n.t('请选择'), dataSource: resultOptions, showClearButton: true }
  },
  {
    label: i18n.t('失信列入日期'),
    prop: 'addBlackTime',
    component: 'mt-date-range-picker',
    props: { placeholder: i18n.t('请选择') }
  },
  {
    label: i18n.t('失信终止日期'),
    prop: 'valideTime',
    component: 'mt-date-range-picker',
    props: { placeholder: i18n.t('请选择') }
  },
  {
    label: i18n.t('创建人'),
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('创建时间'),
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: i18n.t('请选择') }
  },
  {
    label: i18n.t('更新人'),
    prop: 'updateUserName',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('更新时间'),
    prop: 'updateTime',
    component: 'mt-date-range-picker',
    props: { placeholder: i18n.t('请选择') }
  }
]
