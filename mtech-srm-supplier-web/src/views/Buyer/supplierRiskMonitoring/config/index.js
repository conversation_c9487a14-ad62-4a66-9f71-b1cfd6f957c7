import { i18n } from '@/main.js'

export const riskTypeOptions = [
  {
    value: 0,
    text: i18n.t('预警提醒')
  },
  {
    value: 1,
    text: i18n.t('自身风险')
  },
  {
    value: 2,
    text: i18n.t('周边风险')
  },
  {
    value: 3,
    text: i18n.t('历史风险')
  }
]

export const receivingTypeOptions = [
  {
    value: '43',
    text: i18n.t('严重违法')
  },
  {
    value: '42',
    text: i18n.t('行政处罚【工商局】')
  },
  {
    value: '410',
    text: i18n.t('行政处罚【信用中国】')
  },
  {
    value: '41',
    text: i18n.t('经营异常')
  },
  {
    value: '44',
    text: i18n.t('股权出质')
  },
  {
    value: '45',
    text: i18n.t('动产抵押')
  },
  {
    value: '46',
    text: i18n.t('欠税公告')
  },
  {
    value: '36',
    text: i18n.t('司法协助')
  },
  {
    value: '48',
    text: i18n.t('清算信息')
  },
  {
    value: '49',
    text: i18n.t('知识产权出质')
  },
  {
    value: '83',
    text: i18n.t('环保处罚')
  },
  {
    value: '84',
    text: i18n.t('公示催告')
  },
  {
    value: '81',
    text: i18n.t('税收违法')
  },
  {
    value: '47',
    text: i18n.t('司法拍卖')
  },
  {
    value: '85',
    text: i18n.t('土地抵押')
  },
  {
    value: '82',
    text: i18n.t('简易注销')
  },
  {
    value: '90',
    text: i18n.t('终本案件')
  },
  {
    value: '44',
    text: i18n.t('股权出质')
  },
  {
    value: '36',
    text: i18n.t('司法协助')
  },
  {
    value: '93',
    text: i18n.t('询价评估')
  },
  {
    value: '93',
    text: i18n.t('股权质押')
  }
]

export const statusOptions = [
  {
    value: 0,
    text: i18n.t('未跟进')
  },
  {
    value: 1,
    text: i18n.t('已跟进')
  }
]

export const violationTypeOptions = [
  { value: 0, text: i18n.t('无') },
  { value: 1, text: i18n.t('违规失信合作方') },
  { value: 2, text: i18n.t('合规筛查结果') }
]

export const resultOptions = [
  { value: 0, text: i18n.t('不通过') },
  { value: 1, text: i18n.t('通过') }
]

export const listToolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false
  }
]

export const detailToolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false
  }
]

export const listColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'riskTypeName',
    title: i18n.t('风险分类')
  },
  {
    field: 'partnerCode',
    title: i18n.t('SRM供应商编码'),
    minWidth: 140
  },
  {
    field: 'supplierCode',
    title: i18n.t('SAP供应商编码'),
    minWidth: 140
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 140
  },
  {
    field: 'riskLabel',
    title: i18n.t('风险标签')
  },
  {
    field: 'receivingType',
    title: i18n.t('类型')
  },
  {
    field: 'title',
    title: i18n.t('标题')
  },
  {
    field: 'content',
    title: i18n.t('内容')
  },
  {
    field: 'riskDetail',
    title: i18n.t('风险详情'),
    slots: {
      default: 'riskDetailDefault'
    }
  },
  {
    field: 'accordingStatus',
    title: i18n.t('跟进状态'),
    formatter: ({ cellValue }) => {
      let selectedItem = statusOptions.find((item) => item.value === cellValue)
      return selectedItem ? selectedItem.text : ''
    }
  },
  {
    field: 'followRecord',
    title: i18n.t('跟进记录'),
    slots: {
      default: 'followRecordDefault'
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]

export const detailColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'srmSupplierCode',
    title: i18n.t('SRM编码')
  },
  {
    field: 'supplierCode',
    title: i18n.t('SAP编码')
  },
  {
    field: 'identityCode',
    title: i18n.t('统一社会信用代码'),
    minWidth: 160
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 120
  },
  {
    field: 'violationType',
    title: i18n.t('类型'),
    formatter: ({ cellValue }) => {
      return violationTypeOptions.find((item) => item.value === cellValue)?.text || ''
    }
  },
  {
    field: 'result',
    title: i18n.t('结果'),
    formatter: ({ cellValue }) => {
      return resultOptions.find((item) => item.value === cellValue)?.text || ''
    }
  },
  {
    field: 'addBlackTime',
    title: i18n.t('失信列入日期'),
    minWidth: 160
  },
  {
    field: 'valideTime',
    title: i18n.t('失信终止日期'),
    minWidth: 160
  },
  {
    field: 'remark',
    title: i18n.t('备注'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
