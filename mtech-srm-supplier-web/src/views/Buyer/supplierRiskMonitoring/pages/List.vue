<!-- 外部识别风险 -->
<template>
  <div class="external-risk-monitoring-list">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="016e9ce1-367a-45cd-81f0-bbe794fa71ec"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #riskDetailDefault="{ row }">
        <div style="color: #2783fe; cursor: pointer" @click="detailClick(row)">
          {{ $t('详情') }}
        </div>
      </template>
      <template #followRecordDefault="{ row }">
        <div style="color: #2783fe; cursor: pointer" @click="recordClick(row)">
          {{ $t('记录') }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  listColumns,
  listToolbar,
  riskTypeOptions,
  receivingTypeOptions,
  statusOptions
} from '../config/index'
import { getSearchFormItems } from '../config/searchForm'
import { getHeadersFileName, download } from '@/utils/utils'
import columnsConfig from '../config/columns.json'
export default {
  name: 'ExternalRiskMonitoringList',
  components: { CollapseSearch, ScTable },

  data() {
    return {
      loading: false,
      searchFormModel: {},
      searchFormRules: {},
      tableData: [],
      columns: listColumns,
      toolbar: listToolbar,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      riskTypeOptions,
      receivingTypeOptions,
      statusOptions
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      let options = {
        riskTypeOptions: this.riskTypeOptions,
        receivingTypeOptions: this.receivingTypeOptions,
        statusOptions: this.statusOptions
      }
      const items = getSearchFormItems(options)
      // 为创建时间、更新时间字段添加onChange事件处理
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}Start`] = null
        this.searchFormModel[`${field}End`] = null
        return
      }

      this.searchFormModel[`${field}Start`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}End`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.supplierRiskMonitoring.pageExternalRiskMonitoring(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    // 转换日期字段
    transformDateFields(items) {
      if (!Array.isArray(items)) return items

      return items.map(item => {
        const newItem = { ...item }

        // 递归处理对象的所有字段
        const processObject = (obj) => {
          Object.keys(obj).forEach(key => {
            // 如果字段名包含 Date 且值不为空
            if (key.toLowerCase().includes('date') && obj[key]) {
              try {
                // 尝试转换为日期格式
                obj[key] = dayjs(obj[key]).format('YYYY-MM-DD')
              } catch (e) {
                console.warn(`Failed to convert date field ${key}:`, e)
              }
            }
            // 如果值是对象，递归处理
            else if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
              processObject(obj[key])
            }
          })
        }

        processObject(newItem)
        return newItem
      })
    },

    detailClick(row) {
      let detail = JSON.parse(row.contentDetail)
      console.log(detail)
      const typeColumns = columnsConfig[detail.type]?.columns || []
      // 转换日期字段
      const transformedItems = this.transformDateFields(detail.items)
      this.$dialog({
        data: {
          title: row.receivingType + '-' + row.supplierName,
          type: detail.type,
          tableData: transformedItems,
          columns: typeColumns
        },
        modal: () => import('../components/RiskDetails.vue')
      })
    },

    recordClick(row) {
      this.$dialog({
        data: {
          title: this.$t('跟进记录'),
          id: row.id
        },
        modal: () => import('../components/FollowRecord.vue'),
        success: async (params) => {
          let api = this.$API.supplierRiskMonitoring.saveExternalRiskMonitoringFollowRecord
          const res = await api(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('添加成功'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },

    async handleClickToolBar(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.supplierRiskMonitoring.exportExternalRiskMonitoring(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
