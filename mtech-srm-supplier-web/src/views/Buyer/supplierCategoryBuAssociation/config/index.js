import { i18n } from '@/main.js'

export const approvalStatusOptions = [
  { text: i18n.t('待提交'), label: i18n.t('待提交'), value: 0 },
  { text: i18n.t('审批中'), label: i18n.t('审批中'), value: 1 },
  { text: i18n.t('已通过'), label: i18n.t('已通过'), value: 2 },
  { text: i18n.t('已驳回'), label: i18n.t('已驳回'), value: 3 }
]

export const offerAttributeOptions = [
  { text: i18n.t('寄售价'), label: i18n.t('寄售价'), value: '1' },
  { text: i18n.t('标准价'), label: i18n.t('标准价'), value: '2' }
]

export const priceEffectiveModeOptions = [
  { text: i18n.t('按订单生效'), label: i18n.t('按订单生效'), value: '1' },
  { text: i18n.t('按入库生效'), label: i18n.t('按入库生效'), value: '5' },
  { text: i18n.t('按出库生效'), label: i18n.t('按出库生效'), value: '0' }
]

export const statusOptions = [
  { text: i18n.t('注册'), label: i18n.t('注册'), value: '1' },
  { text: i18n.t('潜在'), label: i18n.t('潜在'), value: '2' },
  { text: i18n.t('新合格'), label: i18n.t('新合格'), value: '3' },
  { text: i18n.t('临时'), label: i18n.t('临时'), value: '4' },
  { text: i18n.t('合格'), label: i18n.t('合格'), value: '10' },
  { text: i18n.t('预合格'), label: i18n.t('预合格'), value: '11' },
  { text: i18n.t('冻结'), label: i18n.t('冻结'), value: '20' },
  { text: i18n.t('黑名单'), label: i18n.t('黑名单'), value: '30' },
  { text: i18n.t('退出'), label: i18n.t('退出'), value: '40' }
]

export const communicationBuOptions = [
  { text: i18n.t('手机事业部'), label: i18n.t('手机事业部'), value: 'MP' },
  { text: i18n.t('智慧移动屏BU'), label: i18n.t('智慧移动屏BU'), value: 'SMD' },
  { text: i18n.t('智能连接终端BU'), label: i18n.t('智能连接终端BU'), value: 'SCD' }
]

export const organizationOptions = [
  { text: i18n.t('惠州TCL移动通信有限公司'), label: i18n.t('惠州TCL移动通信有限公司'), value: '2M01' },
  { text: i18n.t('TCL Mobile Communication'), label: i18n.t('TCL Mobile Communication'), value: '2S06' }
]
export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商SAP编码'),
    minWidth: 140
  },
  {
    field: 'partnerCode',
    title: i18n.t('供应商编码'),
    minWidth: 110
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 110
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称')
  },
  {
    field: 'organizationCode',
    title: i18n.t('公司法人'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.organizationName : ''
    },
    minWidth: 140
  },
  {
    field: 'communicationBuName',
    title: i18n.t('通讯标识'),
    formatter: ({ cellValue }) => {
      console.log(cellValue)
      let text = ''
      if (cellValue) {
        try {
          let arr = JSON.parse(cellValue)
          text = arr.join('，')
        } catch (e) {
          text = cellValue
        }
      }
      return text
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('采购员')
  },
  {
    field: 'approvalCode',
    title: i18n.t('审批单号'),
    minWidth: 120
  },
  {
    field: 'approvalStatus',
    title: i18n.t('审批状态'),
    formatter: ({ cellValue }) => {
      let item = approvalStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'purchaseOrgCode',
    title: i18n.t('采购组织编码'),
    minWidth: 140
  },
  {
    field: 'purchaseOrgName',
    title: i18n.t('采购组织名称'),
    minWidth: 140
  },
  {
    field: 'offerAttribute',
    title: i18n.t('报价属性'),
    formatter: ({ cellValue }) => {
      let item = offerAttributeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'priceEffectiveMode',
    title: i18n.t('价格生效方式'),
    formatter: ({ cellValue }) => {
      let item = priceEffectiveModeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    minWidth: 140
  },
  {
    field: 'statusId',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    slots: {
      default: 'operateDefault'
    }
  }
]
