<!-- 供应商品类BU关联表 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('供应商SAP编码')" prop="supplierCode">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商编码')" prop="partnerCode">
          <mt-input
            v-model="searchFormModel.partnerCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商名称')" prop="supplierName">
          <mt-input
            v-model="searchFormModel.supplierName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类编码')" prop="categoryCode">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类名称')" prop="categoryName">
          <mt-input
            v-model="searchFormModel.categoryName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('公司法人')" prop="organizationCode">
          <mt-input
            v-model="searchFormModel.organizationCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('通讯标识')" prop="communicationBuCode">
          <mt-select
            v-model="searchFormModel.communicationBuCode"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :data-source="communicationBuOptions"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购员')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('审批单号')" prop="approvalCode">
          <mt-input
            v-model="searchFormModel.approvalCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('审批状态')" prop="approvalStatus">
          <mt-select
            v-model="searchFormModel.approvalStatus"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :data-source="approvalStatusOptions"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购组织编码')" prop="purchaseOrgCode">
          <mt-input
            v-model="searchFormModel.purchaseOrgCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购组织名称')" prop="purchaseOrgName">
          <mt-input v-model="searchFormModel.purchaseOrgName" :placeholder="$t('请输入')" />
        </mt-form-item>
        <mt-form-item :label="$t('报价属性')" prop="offerAttribute">
          <mt-select
            v-model="searchFormModel.offerAttribute"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :data-source="offerAttributeOptions"
          />
        </mt-form-item>
        <mt-form-item :label="$t('价格生效方式')" prop="priceEffectiveMode">
          <mt-select
            v-model="searchFormModel.priceEffectiveMode"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :data-source="priceEffectiveModeOptions"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchFormModel.status"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :data-source="statusOptions"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="31fa60e9-bf74-4aed-9925-58d87feb7b02"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #operateDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleCheck(row)">
          {{ $t('详情') }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <AddOrEdit ref="addOrEditRef" @refresh="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import AddOrEdit from './components/AddOrEdit.vue'
import {
  columnData,
  communicationBuOptions,
  approvalStatusOptions,
  offerAttributeOptions,
  priceEffectiveModeOptions,
  statusOptions
} from './config/index'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: { CollapseSearch, ScTable, AddOrEdit },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'edit', name: this.$t('编辑'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'submitOA', name: this.$t('提交OA审批'), status: 'info', loading: false },
        { code: 'checkOA', name: this.$t('查看OA审批'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      communicationBuOptions,
      approvalStatusOptions,
      offerAttributeOptions,
      priceEffectiveModeOptions,
      statusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.supplierCategoryBuAssociation
        .pageQuery(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['edit', 'delete', 'submitOA', 'checkOA']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && ['edit', 'submitOA', 'checkOA'].includes(e.code)) {
        this.$toast({ content: this.$t('只能选择一行进行操作'), type: 'warning' })
        return
      }
      if (['edit'].includes(e.code)) {
        const approvalStatus = selectedRecords[0].approvalStatus
        if (approvalStatus !== 0 && approvalStatus !== 3) {
          this.$toast({ content: this.$t('只有待提交或已驳回状态才能编辑'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'edit':
          this.handleEdit(selectedRecords[0])
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'submitOA':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认提交OA审批？')
            },
            success: () => {
              this.handleSubmitOA(selectedRecords)
            }
          })
          break
        case 'checkOA':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认查看OA审批？')
            },
            success: () => {
              this.handleCheckOA(selectedRecords)
            }
          })
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('新增'),
        actionType: 'add'
      })
    },
    handleEdit(row) {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('编辑'),
        actionType: 'edit',
        row
      })
    },
    handleCheck(row) {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('查看'),
        actionType: 'detail',
        row
      })
    },
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.supplierCategoryBuAssociation.delete({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleSubmitOA(selectedRecords) {
      let params = { id: selectedRecords[0].id }
      this.$API.supplierCategoryBuAssociation.submitOa(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleCheckOA(selectedRecords) {
      let params = { id: selectedRecords[0].id }
      this.$API.supplierCategoryBuAssociation.checkOa(params).then((res) => {
        if (res.code === 200) {
          let url = res.data
          if (url) {
            window.open(url)
          } else {
            this.$toast({ content: this.$t('未提交OA审批'), type: 'warning' })
          }
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.supplierCategoryBuAssociation
        .export(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
