export const formItems = [
  {
    prop: 'organizationCode',
    label: '公司法人',
    component: 'mt-select',
    placeholder: '请选择'
  },
  {
    prop: 'supplierCode',
    label: '供应商编码',
    component: 'RemoteAutocomplete',
    placeholder: '请选择供应商',
    url: '/masterDataManagement/tenant/supply/source/getSupplySourceResponseByOrg',
    fields: { text: 'supplierName', value: 'supplierCode' },
    paramsKey: 'supplierInfo'
  },
  {
    prop: 'supplierName',
    label: '供应商名称',
    component: 'mt-input',
    placeholder: '请选择供应商',
    disabled: true
  },
  {
    prop: 'categoryCode',
    label: '品类编码',
    component: 'RemoteAutocomplete',
    placeholder: '请选择品类',
    url: '/masterDataManagement/tenant/supply/source/getSupplySourceResponseByOrgSuppCate',
    fields: { text: 'categoryName', value: 'categoryCode' },
    paramsKey: 'categoryInfo'
  },
  {
    prop: 'categoryName',
    label: '品类名称',
    component: 'mt-input',
    placeholder: '请选择品类',
    disabled: true
  },
  {
    prop: 'communicationBuCode',
    label: '通讯标识',
    component: 'mt-select',
    placeholder: '请选择'
  },
  {
    prop: 'createUserName',
    label: '采购员',
    component: 'mt-input',
    disabled: true
  },
  {
    prop: 'purchaseOrgCode',
    label: '采购组织编码',
    component: 'RemoteAutocomplete',
    placeholder: '请选择采购组织',
    url: '/masterDataManagement/tenant/business-org-org-rel/getPurInfoByOrgCode',
    method: 'get',
    recordsPosition: 'data',
    fields: { text: 'organizationName', value: 'organizationCode' },
  },
  {
    prop: 'purchaseOrgName',
    label: '采购组织名称',
    component: 'mt-input',
    placeholder: '请选择采购组织',
    disabled: true
  },
  {
    prop: 'offerAttribute',
    label: '报价属性',
    component: 'mt-select',
    placeholder: '请选择'
  },
  {
    prop: 'priceEffectiveMode',
    label: '价格生效方式',
    component: 'mt-select',
    placeholder: '请选择'
  },
  {
    prop: 'statusId',
    label: '状态',
    component: 'mt-select',
    placeholder: '请选择'
  },
  {
    prop: 'describeText',
    label: '说明',
    component: 'mt-input',
    placeholder: '请输入',
    clearable: true
  }
]
