<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="700"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item
        v-for="(item, index) in formItems"
        :key="index"
        :prop="item.prop"
        :label="$t(item.label)"
      >
        <component
          :is="item.component"
          v-model="formData[item.prop]"
          v-bind="getComponentProps(item)"
          @change="(e) => handleFieldChange(item.prop, e)"
        />
      </mt-form-item>
      <mt-form-item prop="categoryName" :label="$t('附件')">
        <mt-button v-if="actionType !== 'detail'" type="primary" @click="handleUpload">{{
          $t('上传附件')
        }}</mt-button>
        <div v-for="(item, index) in attachmentList" :key="index" class="attachment-item">
          <span class="attachment-link" @click="handlePreview(item)">{{ item.fileName }}</span>
          <template v-if="actionType !== 'detail'">
            <span class="attachment-link" @click="handleRemove(index)">{{ $t('删除') }}</span>
          </template>
          <template v-else>
            <span class="attachment-link" @click="handleDownload(item)">{{ $t('下载') }}</span>
          </template>
        </div>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  organizationOptions,
  communicationBuOptions,
  offerAttributeOptions,
  priceEffectiveModeOptions,
  statusOptions
} from '../config'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { download } from '@/utils/utils'
import { formItems } from './formConfig'

// 提取常量
const BASE_FIELDS = [
  'id',
  'organizationCode',
  'organizationName',
  'supplierCode',
  'supplierName',
  'partnerCode',
  'categoryId',
  'categoryCode',
  'categoryName',
  'purchaseOrgCode',
  'purchaseOrgName',
  'createUserName',
  'communicationBuCode',
  'communicationBuName',
  'offerAttribute',
  'priceEffectiveMode',
  'statusId',
  'describeText'
]

const READONLY_FIELDS = [
  'supplierName',
  'categoryName',
  'createUserName',
  'purchaseOrgName',
  'offerAttribute',
  'priceEffectiveMode',
  'statusId'
]

export default {
  name: 'SupplierCategoryBuAssociationForm',
  components: { RemoteAutocomplete },

  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      formData: {},
      rules: this.getFormRules(),
      formItems,
      organizationOptions,
      communicationBuOptions,
      offerAttributeOptions,
      priceEffectiveModeOptions,
      statusOptions,
      attachmentList: [],
      isInitialized: false
    }
  },

  computed: {
    buttons() {
      let btns = []
      if (['add', 'edit'].includes(this.actionType)) {
        btns = [
          {
            click: this.handleClose,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
      }
      return btns
    }
  },
  methods: {
    // 重置组件状态
    resetComponentState() {
      this.formData = {}
      this.attachmentList = []
      this.isInitialized = false
      this.$refs.ruleForm?.clearValidate()
    },

    async dialogInit(args) {
      try {
        // 重置组件状态
        this.resetComponentState()

        this.$refs.dialog.ejsRef.show()
        const { title, actionType, row } = args
        this.dialogTitle = title
        this.actionType = actionType

        if (actionType === 'add') {
          this.formData = this.getInitialFormData()
          this.isInitialized = true
        } else if (['edit', 'detail'].includes(actionType)) {
          await this.$nextTick()
          await this.getDetail(row.id)
        }
      } catch (error) {
        this.handleError('初始化失败', error)
      }
    },

    async getDetail(id) {
      try {
        const res = await this.$API.supplierCategoryBuAssociation.getDetail({ id })
        if (res.code === 200) {
          console.log('获取详情数据：', res.data)

          // 重置表单数据
          this.formData = {}
          await this.$nextTick()

          // 设置基础数据
          this.setMultipleFields(BASE_FIELDS, res.data)
          console.log('设置基础数据后的表单数据：', this.formData)

          // 等待组件初始化完成
          await this.waitForNextTick()

          // 按顺序设置数据
          if (res.data.organizationCode) {
            // 1. 先设置组织数据
            await this.setOrganizationData(res.data)

            // 2. 设置供应商数据
            if (res.data.supplierCode) {
              await this.setSupplierData(res.data)
            }
          }

          // 设置附件数据
          this.attachmentList = res.data.fileInfoResponseList || []

          // 标记初始化完成
          this.isInitialized = true

          await this.waitForNextTick()
          console.log('最终的表单数据：', this.formData)
        }
      } catch (error) {
        this.handleError('获取详情失败', error)
      }
    },

    getComponentProps(item) {
      const baseProps = {
        placeholder: this.$t(item.placeholder || '请选择'),
        disabled: this.getFieldDisabled(item.prop)
      }

      if (item.component === 'mt-select') {
        return this.getSelectProps(item, baseProps)
      }

      if (item.component === 'RemoteAutocomplete') {
        const props = this.getRemoteAutocompleteProps(item, baseProps)

        // 只在组件初始化完成后设置初始值
        if (this.isInitialized) {
          if (item.prop === 'supplierCode' && this.formData.supplierCode) {
            props.initialValue = this.getSupplierInitialValue()
            props.value = this.formData.supplierCode
          } else if (item.prop === 'categoryCode' && this.formData.categoryCode) {
            props.initialValue = {
              categoryId: this.formData.categoryId,
              categoryCode: this.formData.categoryCode,
              categoryName: this.formData.categoryName,
              label: `${this.formData.categoryCode} - ${this.formData.categoryName}`
            }
            props.value = this.formData.categoryCode
          } else if (item.prop === 'purchaseOrgCode' && this.formData.purchaseOrgCode) {
            props.initialValue = {
              businessOrganizationCode: this.formData.purchaseOrgCode,
              businessOrganizationName: this.formData.purchaseOrgName,
              label: `${this.formData.purchaseOrgCode} - ${this.formData.purchaseOrgName}`
            }
            props.value = this.formData.purchaseOrgCode
          }
        }

        return props
      }

      return baseProps
    },

    handleClose() {
      this.resetComponentState()
      this.$refs.dialog.ejsRef.hide()
    },

    close() {
      this.resetComponentState()
    },

    beforeOpen() {
      this.resetComponentState()
    },

    onOpen(args) {
      args.preventFocus = true
    },

    // 工具方法
    async waitForNextTick() {
      await this.$nextTick()
    },

    setFormField(field, value) {
      this.$set(this.formData, field, value || '')
    },

    setMultipleFields(fields, data) {
      fields.forEach((field) => {
        if (field in data) {
          if (field === 'communicationBuCode') {
            try {
              const parsedData = JSON.parse(data[field])
              this.setFormField(field, parsedData[0] || '')
            } catch (error) {
              console.error('解析通讯标识数据失败:', error)
              this.setFormField(field, data[field])
            }
          } else {
            this.setFormField(field, data[field])
          }
        }
      })
    },

    clearFields(fields) {
      fields.forEach((field) => this.setFormField(field, ''))
    },

    // 初始化相关方法
    getInitialFormData() {
      if (this.actionType === 'add') {
        const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
        return {
          createUserName: userInfo.accountName || ''
        }
      }
      return {}
    },

    getFormRules() {
      return {
        organizationCode: [{ required: true, message: this.$t('请选择公司法人'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        categoryCode: [{ required: true, message: this.$t('请选择品类'), trigger: 'blur' }],
        communicationBuCode: [
          { required: true, message: this.$t('请选择通讯标识'), trigger: 'blur' }
        ],
        purchaseOrgCode: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        offerAttribute: [{ required: true, message: this.$t('请选择报价属性'), trigger: 'blur' }],
        priceEffectiveMode: [
          { required: true, message: this.$t('请选择价格生效方式'), trigger: 'blur' }
        ]
      }
    },

    // 组件属性相关方法
    getSelectProps(item, baseProps) {
      const optionsMap = {
        organizationCode: this.organizationOptions,
        communicationBuCode: this.communicationBuOptions,
        offerAttribute: this.offerAttributeOptions,
        priceEffectiveMode: this.priceEffectiveModeOptions,
        statusId: this.statusOptions
      }

      return {
        ...baseProps,
        fields: { text: 'text', value: 'value' },
        'data-source': optionsMap[item.prop] || [],
        'allow-filtering': true
      }
    },

    getRemoteAutocompleteProps(item, baseProps) {
      const props = {
        ...baseProps,
        url: item.url,
        method: item.method || 'post',
        recordsPosition: item.recordsPosition || 'data.records',
        fields: item.fields,
        paramsKey: item.paramsKey,
        params: this.getRemoteParams(item),
        loadData: this.getLoadDataCondition(item)
      }

      return props
    },

    getSupplierInitialValue() {
      const { supplierCode, supplierName, partnerCode } = this.formData
      return {
        supplierCode,
        supplierName,
        partnerCode,
        label: `${supplierCode} - ${supplierName}`
      }
    },

    getFieldDisabled(field) {
      if (this.actionType === 'detail') {
        return true
      }

      if (READONLY_FIELDS.includes(field)) {
        return true
      }

      const disabledMap = {
        supplierCode: !this.formData.organizationCode,
        categoryCode: !this.formData.organizationCode || !this.formData.supplierCode,
        purchaseOrgCode:
          !this.formData.organizationCode ||
          !this.formData.supplierCode ||
          !this.formData.categoryCode
      }
      return disabledMap[field] || false
    },

    getRemoteParams(item) {
      const paramsMap = {
        supplierCode: { organizationCode: this.formData.organizationCode },
        categoryCode: {
          organizationCode: this.formData.organizationCode,
          supplierInfo: this.formData.supplierCode
        },
        purchaseOrgCode: {
          orgCode: this.formData.organizationCode
        }
      }
      return paramsMap[item.prop] || {}
    },

    getLoadDataCondition(item) {
      const conditionMap = {
        supplierCode: this.formData.organizationCode,
        categoryCode: this.formData.organizationCode && this.formData.supplierCode,
        purchaseOrgCode:
          this.formData.organizationCode && this.formData.supplierCode && this.formData.categoryCode
      }
      return conditionMap[item.prop] || false
    },

    // 事件处理方法
    handleFieldChange(field, event) {
      const changeHandlers = {
        organizationCode: this.handleOrganizationChange,
        supplierCode: this.handleSupplierChange,
        categoryCode: this.handleCategoryChange,
        communicationBuCode: this.handleCommunicationBuChange,
        purchaseOrgCode: this.handlePurchaseOrgChange
      }

      const handler = changeHandlers[field]
      if (handler) {
        handler(event)
      }
    },

    async setOrganizationData(data) {
      const orgData = {
        organizationCode: data.organizationCode,
        organizationName: data.organizationName,
        text: data.organizationName,
        value: data.organizationCode
      }

      console.log('设置组织数据：', orgData)
      await this.handleOrganizationChange({ itemData: orgData })
      await this.waitForNextTick()
    },

    async setSupplierData(data) {
      try {
        const supplierData = {
          supplierCode: data.supplierCode,
          supplierName: data.supplierName,
          partnerCode: data.partnerCode,
          label: `${data.supplierCode} - ${data.supplierName}`
        }

        console.log('设置供应商数据：', supplierData)

        // 设置供应商基础数据
        this.setMultipleFields(['supplierCode', 'supplierName', 'partnerCode'], data)
        await this.waitForNextTick()

        // 触发供应商选择事件
        await this.handleSupplierChange({ itemData: supplierData })
        await this.waitForNextTick()

        // 设置品类数据
        if (data.categoryCode) {
          await this.setCategoryData(data)
        }
      } catch (error) {
        this.handleError('设置供应商数据失败', error)
      }
    },

    async setCategoryData(data) {
      try {
        if (!data.organizationCode || !data.supplierCode) {
          throw new Error('组织编码或供应商编码不能为空')
        }

        console.log('开始获取品类数据，参数：', {
          organizationCode: data.organizationCode,
          supplierInfo: data.supplierCode
        })

        const categoryRes = await this.fetchCategoryData(data)
        console.log('品类数据接口返回：', categoryRes)

        const matchedCategory = this.findMatchedCategory(categoryRes, data.categoryCode)
        if (!matchedCategory) {
          throw new Error(`未找到匹配的品类数据：${data.categoryCode}`)
        }

        console.log('匹配到的品类数据：', matchedCategory)

        // 构造品类数据
        const categoryData = {
          categoryId: matchedCategory.categoryId,
          categoryCode: matchedCategory.categoryCode,
          categoryName: matchedCategory.categoryName,
          label: `${matchedCategory.categoryCode} - ${matchedCategory.categoryName}`
        }

        // 设置品类字段
        this.setMultipleFields(['categoryId', 'categoryCode', 'categoryName'], categoryData)
        await this.waitForNextTick()

        // 触发品类选择事件
        await this.handleCategoryChange({ itemData: categoryData })
        await this.waitForNextTick()

        // 设置采购组织数据
        if (data.purchaseOrgCode) {
          await this.setPurchaseOrgData(data)
        }
      } catch (error) {
        this.handleError('获取品类数据失败', error)
      }
    },

    async fetchCategoryData(data) {
      const res =
        await this.$API.supplierCategoryBuAssociation.getSupplySourceResponseByOrgSuppCate({
          organizationCode: data.organizationCode,
          supplierInfo: data.supplierCode,
          page: { current: 1, size: 1000 }
        })

      if (res.code !== 200) {
        throw new Error(res.message || '获取品类数据失败')
      }

      if (!res.data?.records) {
        throw new Error('接口返回数据格式不正确')
      }

      return res
    },

    findMatchedCategory(categoryRes, categoryCode) {
      return categoryRes.data.records.find((item) => item.categoryCode === categoryCode)
    },

    async setPurchaseOrgData(data) {
      try {
        const purchaseOrgData = {
          businessOrganizationCode: data.purchaseOrgCode,
          businessOrganizationName: data.purchaseOrgName,
          label: `${data.purchaseOrgCode} - ${data.purchaseOrgName}`
        }

        console.log('设置采购组织数据：', purchaseOrgData)

        // 设置采购组织基础数据
        this.setMultipleFields(['purchaseOrgCode', 'purchaseOrgName'], data)
        await this.waitForNextTick()

        // 触发采购组织选择事件
        await this.handlePurchaseOrgChange({ itemData: purchaseOrgData })
        await this.waitForNextTick()

        // 获取采购组织相关数据
        await this.getSupplySourceResponseByOrgSuppCatePur()
      } catch (error) {
        this.handleError('设置采购组织数据失败', error)
      }
    },

    handleError(message, error) {
      console.error(`${message}:`, error)
      this.$toast({
        content: this.$t(message) + (error.message ? `: ${error.message}` : ''),
        type: 'error'
      })
    },

    // 字段变更处理方法
    async handleOrganizationChange(e) {
      if (e?.itemData) {
        this.setFormField('organizationName', e.itemData.text)
        this.clearFields([
          'supplierCode',
          'partnerCode',
          'supplierName',
          'categoryId',
          'categoryCode',
          'categoryName',
          'purchaseOrgCode',
          'purchaseOrgName'
        ])
        await this.waitForNextTick()
      }
    },

    // 添加确认/保存方法
    async confirm() {
      try {
        // 表单验证
        await this.$refs.ruleForm.validate()

        // 准备提交数据
        const submitData = {
          ...this.formData,
          fileInfoDTOList: this.attachmentList
        }

        // 根据操作类型选择不同的API
        let apiMethod = this.$API.supplierCategoryBuAssociation.add
        let successMessage = this.$t('新增成功')

        if (this.actionType === 'edit') {
          apiMethod = this.$API.supplierCategoryBuAssociation.update
          successMessage = this.$t('修改成功')
        }

        // 显示加载状态
        this.$loading()

        // 发送请求
        const res = await apiMethod(submitData)

        // 隐藏加载状态
        this.$hloading()

        if (res.code === 200) {
          // 成功处理
          this.$toast({
            content: successMessage,
            type: 'success'
          })

          // 关闭对话框
          this.handleClose()

          // 通知父组件刷新列表
          this.$emit('refresh')
        } else {
          throw new Error(res.message || this.$t('操作失败'))
        }
      } catch (error) {
        this.$hloading()
        this.handleError(this.$t('保存失败'), error)
      }
    },

    async handleSupplierChange(e) {
      if (e?.itemData) {
        this.setMultipleFields(['supplierName', 'partnerCode'], e.itemData)
        this.clearFields([
          'categoryId',
          'categoryCode',
          'categoryName',
          'purchaseOrgCode',
          'purchaseOrgName'
        ])
        await this.waitForNextTick()
      }
    },

    async handleCategoryChange(e) {
      if (e?.itemData) {
        this.setMultipleFields(['categoryId', 'categoryName'], e.itemData)
        this.clearFields(['purchaseOrgCode', 'purchaseOrgName'])
        await this.waitForNextTick()
      }
    },

    handleCommunicationBuChange(e) {
      if (e?.itemData) {
        this.setFormField('communicationBuName', e.itemData.text)
      }
    },

    async handlePurchaseOrgChange(e) {
      if (e?.itemData) {
        console.log('采购组织选择事件触发：', e.itemData)
        const purchaseOrgData = {
          businessOrganizationCode:
            e.itemData.businessOrganizationCode || e.itemData.organizationCode || e.itemData.value,
          businessOrganizationName:
            e.itemData.businessOrganizationName || e.itemData.organizationName || e.itemData.text,
          label:
            e.itemData.label ||
            `${
              e.itemData.businessOrganizationCode || e.itemData.organizationCode || e.itemData.value
            } - ${
              e.itemData.businessOrganizationName || e.itemData.organizationName || e.itemData.text
            }`
        }

        this.setMultipleFields(['purchaseOrgCode', 'purchaseOrgName'], {
          purchaseOrgCode: purchaseOrgData.businessOrganizationCode,
          purchaseOrgName: purchaseOrgData.businessOrganizationName
        })

        await this.getSupplySourceResponseByOrgSuppCatePur()
      } else {
        this.clearFields([
          'communicationBuName',
          'statusId',
          'offerAttribute',
          'priceEffectiveMode'
        ])
      }
    },

    async getSupplySourceResponseByOrgSuppCatePur() {
      try {
        const res =
          await this.$API.supplierCategoryBuAssociation.getSupplySourceResponseByOrgSuppCatePur({
            organizationCode: this.formData.organizationCode,
            supplierInfo: this.formData.supplierCode,
            categoryInfo: this.formData.categoryCode,
            purchaseOrgCode: this.formData.purchaseOrgCode
          })

        console.log('获取采购组织相关数据返回：', res)

        if (res.code === 200 && res.data) {
          this.$set(this.formData, 'statusId', res.data.statusId || '')
          this.$set(this.formData, 'offerAttribute', res.data.offerAttribute || '')
          this.$set(this.formData, 'priceEffectiveMode', res.data.priceEffectiveMode || '')
        } else {
          throw new Error(res.message || '获取采购组织相关数据失败')
        }
      } catch (error) {
        console.error('获取采购组织相关数据失败：', error)
        this.$toast({
          content:
            this.$t('获取采购组织相关数据失败') + (error.message ? `: ${error.message}` : ''),
          type: 'error'
        })
      }
    },

    handleUpload() {
      this.$dialog({
        modal: () => import('@/components/Upload/index.vue'),
        data: {
          title: this.$t('上传')
        },
        success: (data) => {
          data.fileId = data.id
          this.attachmentList.push(data)
        }
      })
    },

    handleRemove(index) {
      this.attachmentList.splice(index, 1)
    },

    handlePreview(item) {
      this.$API.fileService.filePreview({ id: item.fileId }).then((res) => {
        if (res.code === 200) {
          window.open(res.data)
        }
      })
    },

    handleDownload(item) {
      this.$loading()
      this.$API.fileService.downloadPrivateFile({ id: item.fileId }).then((res) => {
        this.$hloading()
        download({
          fileName: item.fileName,
          blob: new Blob([res.data])
        })
      })
    }
  },

  watch: {
    // 监听表单数据变化，确保组件状态正确
    formData: {
      handler(newVal) {
        if (this.isInitialized && Object.keys(newVal).length > 0) {
          console.log('表单数据变化：', newVal)
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.attachment-item {
  margin-top: 10px;
}

.attachment-link {
  margin-right: 10px;
  cursor: pointer;
  color: #007aff;

  &:hover {
    text-decoration: underline;
  }
}
</style>
