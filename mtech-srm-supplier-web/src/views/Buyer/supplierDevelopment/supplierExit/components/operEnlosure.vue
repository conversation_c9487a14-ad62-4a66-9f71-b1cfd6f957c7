<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row>
          <mt-col :span="24">
            <!-- <mt-form-item prop="file" class="form-item">
              <div class="cell-upload">
                <div class="to-upload">
                  <input type="file" ref="file" class="upload-input" @change="chooseFiles" />
                  <div class="upload-box">
                    <div class="plus-icon"></div>
                    <div class="right-state">
                      <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
                      <div class="warn-text">
                        文件最大不可超过50M， <br />文件格式仅支持（.pdf .jpg .bmp .gif .ico .pcx
                        .jpeg .tif .png .txt .xls .xlsx .doc .docx .zip .7z .rar）
                      </div>
                    </div>
                  </div>
                </div>

                <div class="has-file" v-if="!!uploadInfo.fileName && !!uploadInfo.remoteUrl">
                  <div class="left-info">
                    <div class="file-title">
                      <span class="text-ellipsis">{{ uploadInfo.fileName }}</span>
                      <span class="text-ellipsis">{{ uploadInfo.fileSize }} kb</span>
                    </div>
                  </div>
                  <mt-icon
                    v-if="!isEdit"
                    name="icon_Close_2"
                    class="close-icon"
                    @click.native="handleRemove"
                  ></mt-icon>
                </div>
              </div>
            </mt-form-item> -->
            <mt-form-item class="form-item" :label="$t('附件')" prop="fileName" label-style="top">
              <mt-common-uploader
                :is-single-file="false"
                :save-url="saveUrl"
                :download-url="downloadUrl"
                type="line"
                v-model="uploadInfo"
              ></mt-common-uploader>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                :multiline="true"
                maxlength="200"
                :rows="3"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import commonData from '@/utils/constant'
import utils from '@/utils/utils'
let fileData = null
export default {
  data() {
    return {
      downloadUrl: commonData.downloadUrl,
      saveUrl: commonData.publicFileUrl,
      formInfo: {
        remark: ''
      },
      rules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      uploadInfo: {} // 上传后信息
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    fileInfo() {
      return this.modalData.fileInfo
    },
    applyInfo() {
      return this.modalData.applyInfo
    }
  },
  mounted() {
    this.show()
    // 编辑下的恢复数据
    if (this.isEdit) {
      this.uploadInfo = {
        ...this.fileInfo,
        fileId: this.fileInfo.fileId,
        fileName: this.fileInfo.fileName,
        fileSize: this.fileInfo.fileSize,
        fileType: this.fileInfo.fileType,
        remoteUrl: this.fileInfo.fileUrl
      }
      this.formInfo.remark = this.fileInfo.remark
    }
  },
  methods: {
    chooseFiles(data) {
      console.log(this.$t('点击上传'), data)
      this.$loading()
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('UploadFiles', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      fileData = _data
      this.uploadFile()
    },

    // 上传图片
    uploadFile() {
      this.$refs.file.value = ''
      this.$API.SupplierPunishment.fileUpload(fileData)
        .then((res) => {
          const { code, data } = res
          this.$hloading()
          if (code == 200 && !utils.isEmpty(data)) {
            let id = this.uploadInfo.id
            this.uploadInfo = {
              ...data,
              fileId: data.id,
              id
            }
            // this.$toast({ content: this.$t("操作成功"), type: "success" });
          } else {
            this.uploadInfo = {}
            this.$toast({ content: data.msg || this.$t('上传失败'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.uploadInfo = {}
          this.$hloading()
          this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
        })
    },

    // 移除文件
    handleRemove() {
      this.uploadInfo = {}
      fileData = null
      console.log('点击了remove-file', this.data)
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      if (utils.isEmpty(this.uploadInfo)) {
        this.$toast({ content: this.$t('请选择文件上传！'), type: 'warning' })
        return
      }
      if (this.uploadInfo.length != 1) {
        this.$toast({ content: this.$t('只能上传一个文件'), type: 'warning' })
        return
      }
      let query = {
        bizId: this.applyInfo.id,
        bizType: this.applyInfo.applyType,
        fileId: this.uploadInfo[0].id,
        fileName: this.uploadInfo[0].fileName,
        fileSize: this.uploadInfo[0].fileSize,
        fileType: this.uploadInfo[0].fileType,
        fileUrl: this.uploadInfo[0].remoteUrl,
        remark: this.formInfo.remark
      }

      if (this.isEdit) {
        query.id = this.uploadInfo.id
        query.sequenceNo = this.uploadInfo.sequenceNo
      }

      let url = this.isEdit ? 'updateFile' : 'adduploader'
      this.$API.SupplierPunishment[url](query)
        .then((res) => {
          const { code, data } = res
          if (code == 200 && !utils.isEmpty(data)) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function', 'reload')
          } else {
            this.$toast({ content: data.msg || this.$t('上传失败'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.uploader-box {
  .mt-form-item__content {
    flex: 1;
  }
}
</style>
<style lang="scss" scoped>
.uploader-box {
  padding: 40px 20px 0;
}

.cell-upload {
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          font-size: 14px;
          display: inline-block;
          vertical-align: middle;
          margin: 0 10px;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
