<template>
  <div class="task-center flex-box">
    <div class="task-sidebar">
      <div
        v-for="(taskItem, index) in taskItems"
        :key="index"
        @click="scrollInto(index)"
        :class="{ active: activeClass === index }"
      >
        <div class="collapse-header">
          {{ taskItem }}
        </div>
      </div>
    </div>
    <div class="collapse-content flex1">
      <div ref="reasonRef" v-if="applyInfo.applyType !== 'relieve'">
        <div class="subtitle mr20">{{ $t('原因说明') }}</div>
        <div class="enclosure">
          <rich-text-editor
            ref="editor"
            css-class="rich-box"
            :height="300"
            :max-length="500"
            :disabled="!canEdit"
            v-model="applyInfo.applyReason"
            :key="canEdit"
          />
        </div>
      </div>
      <div ref="attachmentRef" v-if="applyInfo.applyType !== 'relieve'">
        <div class="subtitle">{{ $t('附件') }}</div>
        <div class="enclosure">
          <mt-template-page
            ref="templateRef"
            :template-config="pageConfig"
            :use-tool-template="false"
            :padding-top="false"
            @handleClickToolBar="handleClickToolBar"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
import { fileColumn } from '../config/column'
import smoothscroll from 'smoothscroll-polyfill'

smoothscroll.polyfill()

const TOOLBAR_ACTIONS = {
  ADD: 'Add',
  DELETE: 'Delete',
  EDIT: 'Edit'
}

export default {
  name: 'DocumentConfig',
  components: { RichTextEditor },

  props: {
    applyInfo: {
      type: Object,
      default: () => ({
        applyReason: '',
        applyType: ''
      })
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      activeClass: 0,
      taskItems: [this.$t('原因说明'), this.$t('附件')],
      pageConfig: [
        {
          toolbar: {
            tools: [
              [
                {
                  id: TOOLBAR_ACTIONS.ADD,
                  icon: '',
                  title: this.$t('新增'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: TOOLBAR_ACTIONS.DELETE,
                  icon: '',
                  title: this.$t('删除'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: TOOLBAR_ACTIONS.EDIT,
                  icon: '',
                  title: this.$t('编辑'),
                  visibleCondition: () => this.canEdit
                }
              ]
            ]
          },
          grid: {
            columnData: fileColumn,
            asyncConfig: {
              url: '/supplier/tenant/common/file/list',
              rules: [
                {
                  label: '',
                  field: 'bizId',
                  type: 'number',
                  operator: 'equal',
                  value: this.$route.query.id
                }
              ],
              params: { bizId: this.$route.query.id }
            }
          }
        }
      ]
    }
  },

  computed: {
    id() {
      return this.$route.query.id
    }
  },

  watch: {
    'applyInfo.applyReason': {
      handler(newVal) {
        this.$emit('update:applyReason', newVal)
      },
      immediate: true
    }
  },

  methods: {
    handleClickToolBar({ toolbar, gridRef }) {
      const actionMap = {
        [TOOLBAR_ACTIONS.ADD]: () => this.handleAdd(),
        [TOOLBAR_ACTIONS.DELETE]: () => this.handleDelete(gridRef),
        [TOOLBAR_ACTIONS.EDIT]: () => this.handleEdit(gridRef)
      }

      const action = actionMap[toolbar.id]
      if (action) {
        action()
      }
    },

    handleAdd() {
      this.$dialog({
        modal: () => import('./operEnlosure.vue'),
        data: {
          title: this.$t('新增附件'),
          isEdit: false,
          fileInfo: {},
          applyInfo: this.applyInfo
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    handleEdit(gridRef) {
      const sltList = gridRef.getMtechGridRecords()
      if (!sltList?.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能编辑一条'), type: 'warning' })
        return
      }

      this.$dialog({
        modal: () => import('./operEnlosure.vue'),
        data: {
          title: this.$t('编辑附件'),
          isEdit: true,
          fileInfo: sltList[0],
          applyInfo: this.applyInfo
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    handleDelete(gridRef) {
      const sltList = gridRef.getMtechGridRecords()
      if (!sltList?.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const ids = sltList.map((item) => item.id).join(',')
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选申请单？'),
          confirm: () => this.$API.SupplierPunishment['delStage']({ ids })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    scrollInto(index) {
      this.activeClass = index

      const refs = {
        0: this.$refs.reasonRef,
        1: this.$refs.attachmentRef
      }

      const targetRef = refs[index]
      if (targetRef) {
        targetRef.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-box {
  display: flex;
}

.flex1 {
  flex: 1;
}
.flex2 {
  flex: 2;
}

.mr20 {
  margin-top: 20px;
}

.task-center {
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  border-right: 2px solid rgba(232, 232, 232, 1);

  .task-sidebar {
    width: 120px;
    height: 100%;
    margin-left: 30px;
    padding-top: 14px;
  }
  .collapse-header {
    height: 30px;
    line-height: 30px;
    padding-left: 30px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin-bottom: 10px;

    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
    user-select: none;
  }
  .active {
    color: rgba(0, 70, 156, 1);
    border-left: 4px solid rgba(0, 70, 156, 1);
  }
  .collapse-content {
    background: rgba(255, 255, 255, 0.568);
    padding-top: 20px;
    padding-bottom: 20px;
    overflow: auto;
  }
  .subtitle {
    height: 14px;
    border-left: 2px solid;
    padding-left: 10px;
    margin-left: 20px;
    margin-top: 20px;
  }
  .enclosure {
    min-height: 200px;
    margin-left: 20px;
    margin-top: 20px;
    margin-right: 20px;
  }
  .categroy-box {
    margin-right: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;

    .subtitle {
      margin: 10px 20px;
    }

    .line-banner {
      width: 100%;
      height: 30px;
      line-height: 30px;
      background: #eee;
      font-size: 14px;
      color: #333;

      .line-check {
        width: 60px;
      }
    }

    .list-box {
      height: 200px;
      overflow: auto;
    }

    .line-normal {
      width: 100%;
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      color: #666;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;

      .line-check {
        width: 60px;
        padding-left: 20px;

        input {
          visibility: visible !important;
          width: 16px;
          height: 16px;
          border-color: #ddd;
        }
      }
    }
    .pointer-class {
      cursor: pointer;
    }
    .line-normal:last-child {
      border-bottom: none;
    }
  }
}
</style>
