<!-- 批量冻结供应商清单 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('供应商编码')" prop="supplierCode">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类')" prop="categoryCodeOrName">
          <mt-input
            v-model="searchFormModel.categoryCodeOrName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('申请公司')" prop="orgCodeOrName">
          <mt-input
            v-model="searchFormModel.orgCodeOrName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>

    <sc-table
      ref="scTableRef"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-right-btn="false"
      :row-config="{
        keyField: 'rowId'
      }"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { listColumnData } from '../config/index'
export default {
  components: { CollapseSearch, ScTable },
  props: {
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: listColumnData,
      loading: false,
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    toolbar() {
      return this.canEdit
        ? [{ code: 'delete', name: this.$t('删除'), status: 'info', loading: false }]
        : []
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        batchFlag: 1,
        id: this.$route.query.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.SupplierPunishment.batchDetailApi(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        const data = res.data.applyBathDetailsResponses
        const total = data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = data?.records || []
        this.tableData = records.map((item, index) => {
          return {
            ...item,
            rowId: index // 行id，区分选中，后端返回id一致。
          }
        })
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        default:
          break
      }
    },
    handleDelete(selectedRecords) {
      this.$API.SupplierPunishment.batchDeleteApi({ deleteDetail: selectedRecords }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    }
  }
}
</script>
