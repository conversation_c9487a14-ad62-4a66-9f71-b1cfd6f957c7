<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    css-class="add-dialog"
    :buttons="buttons"
    @close="cancel"
    :open="onOpen"
  >
    <div class="form-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="applyName" class="form-item" :label="$t('单据名称')">
              <mt-input
                v-model="formInfo.applyName"
                :disabled="false"
                :max-length="30"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="applyerOrgId" :label="$t('申请人公司')">
              <!-- <mt-select
                v-model="formInfo.applyerOrgId"
                float-label-type="Never"
                :data-source="orgList"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="orgChange"
              ></mt-select> -->
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="formInfo.orgCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                @change="orgChange"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseId" :label="$t('供应商')">
              <mt-select
                v-model="formInfo.supplierEnterpriseId"
                float-label-type="Never"
                :data-source="supplierList"
                :fields="{
                  text: 'supplierEnterpriseName',
                  value: 'supplierEnterpriseId'
                }"
                :allow-filtering="true"
                :placeholder="$t('请选择供应商')"
                @change="supplierEnterpriseChange"
                css-class="d72562-c40d-4933-9a24-98c3298365ac"
                :item-template="iTemplate"
                :filtering="onFiltering"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="punishObject" class="form-item" :label="$t('对象维度')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.punishObject"
                :data-source="objectWith"
                :show-clear-button="true"
                :placeholder="$t('请选择对象维度')"
                @change="changeObjWidth"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="12">
            <mt-form-item prop="punishRange" class="form-item" :label="$t('组织维度')">
              <mt-select
                :disabled="formInfo.punishObject == 1"
                ref="companyRef"
                v-model="formInfo.punishRange"
                :data-source="orgWith"
                :show-clear-button="true"
                :placeholder="$t('请选择组织维度')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import itemVue from './itemVue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

let inInit = false
export default {
  components: {
    RemoteAutocomplete
  },
  data() {
    return {
      iTemplate: function () {
        return {
          template: itemVue
        }
      },
      formInfo: {
        applyName: '',
        applyerOrgId: '',
        orgCode: '',
        applyerOrgName: '',
        supplierEnterpriseId: '',
        punishObject: '', // 惩罚对象
        punishRange: '' // 惩罚范围
      },
      applyCompanyData: [],
      rules: {
        applyName: [
          {
            required: true,
            message: this.$t('请填写单据名称'),
            trigger: 'blur'
          }
        ],
        applyerOrgId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        supplierEnterpriseId: [
          { required: true, message: this.$t('请选择供应商'), trigger: 'blur' }
        ],
        punishObject: [
          {
            required: true,
            message: this.$t('请选择对象维度'),
            trigger: 'blur'
          }
        ],
        punishRange: [
          {
            required: true,
            message: this.$t('请选择组织维度'),
            trigger: 'blur'
          }
        ]
      },
      newDate: [],
      objectWith: [
        {
          text: this.$t('供应商-品类'),
          value: '1'
        },
        {
          text: this.$t('供应商'),
          value: '2'
        }
      ],
      orgWith: [
        {
          text: this.$t('公司'),
          value: '1'
        },
        {
          text: this.$t('采购组织'),
          value: '2'
        }
        // {
        //   text: this.$t("集团级"),
        //   value: "3",
        // },
      ],
      // orgList: [], // 公司列表
      supplierList: [], // 公司列表

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { content: this.$t('保存') }
        },
        {
          click: this.confirmFirstStep,
          buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
        }
      ],
      info: {},
      disableSave: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    dialogTitle() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    id() {
      return this.modalData.info[0].id
    }
  },
  async created() {
    // const userDetail = await this.getUserDetail();
    // if (userDetail && userDetail.id) {
    //   this.getChildrenCompanyOrganization(userDetail.id);
    // }
  },
  mounted() {
    if (this.isEdit) {
      inInit = true
      let id = this.modalData.info[0].id
      this.getdetail(id)
    }
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    onFiltering(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.supplierList.filter((item) => {
            if (
              item?.supplierName.indexOf(e.text) > -1 ||
              item?.supplierInternalCode.indexOf(e.text) > -1 ||
              item?.supplierCode.indexOf(e.text) > -1
            ) {
              return true
            } else {
              return false
            }
          })
        )
      } else {
        e.updateData(this.supplierList)
      }
    },
    // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
    handleCompanyData(resData = []) {
      resData = resData.filter((item) => {
        return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      })
      return resData
    },
    // this.$emit("confirm-function", id);
    // getUserDetail() {
    //   return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
    //     let { data } = res;
    //     let { companyOrg } = data;
    //     return companyOrg;
    //   });
    // },

    // getChildrenCompanyOrganization(userId) {
    //   // this.$API.supplierInvitation["getChildrenCompanyOrganization"]({
    //   //   organizationId: userId,
    //   // }).then((result) => {
    //   this.$API.supplierInvitation["getChildrenCompanyOrganization2"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((result) => {
    //     if (result.code === 200 && !utils.isEmpty(result.data)) {
    //       this.orgList = result.data.filter((item) => {
    //         return item.orgLevelTypeCode === "ORG02" ||
    //           item.orgLevelTypeCode === "ORG01";
    //       });
    //     } else {
    //       this.orgList = [];
    //     }
    //   });
    // },

    orgChange(event) {
      // 有级联
      const { itemData = {} } = event
      this.formInfo.applyerOrgId = itemData?.id
      this.formInfo.orgCode = itemData?.orgCode
      this.formInfo.applyerOrgName = itemData?.applyerOrgName
      !inInit && this.clearsupplierEnterprise()
      inInit = false
      if (itemData?.id) {
        this.$loading()
        this.getOrgPartnerRelations(itemData?.id)
      } else {
        this.supplierList = []
        this.formInfo.supplierEnterpriseId = ''
      }
    },

    // STATUS_DRAFT(1, "注册"),
    // STATUS_POTENTIAL(2, "潜在"),
    // STATUS_NORMAL(10, "合格"),
    // STATUS_FROZEN(20, "冻结"),
    // STATUS_BLACK(30, "黑名单"),
    // Integer 类型 这些状态可以惩罚
    getOrgPartnerRelations(orgId) {
      this.$API.supplierEffective
        .getOrgPartnerRelationsByStatus({
          orgId,
          status: [10, 20]
        })
        .then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.supplierList = res.data
            this.$hloading()
          } else {
            this.supplierList = []
          }
          this.$hloading()
        })
    },

    supplierEnterpriseChange(event) {
      const { itemData = {} } = event
      this.formInfo.supplierEnterpriseCode = itemData?.supplierEnterpriseCode
      this.formInfo.supplierEnterpriseName = itemData?.supplierEnterpriseName
      this.formInfo.supplierEnterpriseId = itemData?.supplierEnterpriseId
      this.formInfo.partnerRelationCode = itemData?.partnerRelationCode
      this.formInfo.partnerRelationId = itemData?.id
      this.formInfo.partnerArchiveId = itemData?.partnerArchiveId
      this.formInfo.supplierType = itemData?.supplierType
      this.formInfo.supplierName = itemData?.supplierName
      this.formInfo.categoryId = ''
      if (this.formInfo.punishObject === '1') {
        this.getCategoryPartnerRelationsByStatus()
      }
    },

    clearsupplierEnterprise() {
      this.formInfo.supplierEnterpriseCode = ''
      this.formInfo.supplierEnterpriseName = ''
      this.formInfo.supplierEnterpriseId = ''
      this.formInfo.partnerRelationCode = ''
      this.formInfo.partnerRelationId = ''
      this.formInfo.partnerArchiveId = ''
      this.formInfo.supplierType = ''
      this.formInfo.supplierName = ''
      this.formInfo.categoryId = ''
    },

    // 品类选择的时候 限制未绑定品类的供应商
    changeObjWidth(event) {
      const { itemData } = event
      if (itemData.value === '1') {
        this.formInfo.punishRange = '1'
        this.getCategoryPartnerRelationsByStatus()
      } else {
        this.disableSave = false
      }
    },

    getCategoryPartnerRelationsByStatus() {
      let partnerRelationId = this.formInfo.partnerRelationId
      if (!partnerRelationId) {
        this.$toast({
          content: this.$t('请先选择供应商！'),
          type: 'error'
        })
        return
      }
      this.disableSave = false
      this.$loading()
      this.$API.SupplierPunishment.getCategoryPartnerRelationsByStatus({
        status: [1, 10, 11, 20],
        partnerRelationId
      })
        .then((res) => {
          const { code, data } = res
          if (code == 200 && data.length === 0) {
            this.disableSave = true
            this.$toast({
              content: this.$t('您所选择的供应商尚未绑定品类，不能进行品类对象维度的惩罚！'),
              type: 'error'
            })
          } else if (code == 200 && data.length > 0) {
            this.disableSave = false
          }
          this.$hloading()
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    // 保存
    async save(fn) {
      if (this.disableSave) {
        this.$toast({
          content: this.$t('您所选择的供应商尚未绑定品类，不能进行品类对象维度的惩罚！'),
          type: 'error'
        })
        return
      }
      let formInfo = this.formInfo
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }
      const res = await this.$API.masterData.getUserInfo()
      let applyerName = ''
      let applyerDeptName = ''
      if (res.code == 200) {
        let userInfo = res.data
        applyerName = userInfo.username
        if (Array.isArray(userInfo.orgList)) {
          for (let i = 0; i < userInfo.orgList.length; i++) {
            if (userInfo.orgList[i].orgLevelTypeCode == 'ORG03') {
              applyerDeptName = userInfo.orgList[i].orgName
              break
            }
          }
        }
      }

      let infoDTO = {
        applyName: formInfo.applyName,
        applyerOrgId: formInfo.applyerOrgId,
        applyerOrgName: formInfo.applyerOrgName,
        applyType: 'punish',
        applyerName,
        applyerDeptName
      }
      this.isEdit && (infoDTO = { ...this.info.infoDTO, ...infoDTO })

      let punishDTO = {
        punishObject: formInfo.punishObject,
        punishRange: formInfo.punishRange,
        businessType: 'disuse'
      }
      this.isEdit && (punishDTO = { ...this.info.punishDTO, ...punishDTO })

      let relationDTOList = [
        {
          supplierEnterpriseCode: formInfo.supplierEnterpriseCode,
          supplierEnterpriseName: formInfo.supplierEnterpriseName,
          supplierEnterpriseId: formInfo.supplierEnterpriseId,
          partnerRelationCode: formInfo.partnerRelationCode,
          partnerRelationId: formInfo.partnerRelationId,
          partnerArchiveId: formInfo.partnerArchiveId,
          supplierType: formInfo.supplierType,
          supplierName: formInfo.supplierName
        }
      ]
      this.isEdit &&
        (relationDTOList = [
          {
            ...this.info.relationDTOList[0],
            ...relationDTOList[0]
          }
        ])

      let query = {
        infoDTO,
        punishDTO,
        relationDTOList
      }
      this.$loading()
      let url = this.isEdit
        ? this.$API.SupplierPunishment.updatapunishment
        : this.$API.SupplierPunishment.getaddpunish
      url(query)
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({ content: this.$t('新增成功'), type: 'success' })
            if (!!fn && typeof fn === 'function') {
              let { infoDTO } = res.data
              fn(infoDTO)
            } else {
              this.$emit('confirm-function', 'reload')
            }
          } else {
            this.$toast({
              content: this.$t('新增失败，请重试'),
              type: 'warning'
            })
          }
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('新增失败，请重试'),
            type: 'warning'
          })
        })
    },

    confirmFirstStep() {
      this.save((infoDTO) => {
        this.$emit('confirm-function', { type: 'jump', id: infoDTO.id })
      })
    },

    getdetail(id) {
      this.$API.SupplierPunishment.getchengfaDetail({ id: id }).then((res) => {
        this.info = res.data
        let { punishDTO, infoDTO, relationDTOList } = res.data
        this.formInfo = {
          ...punishDTO,
          ...infoDTO,
          ...relationDTOList[0]
        }
      })
    },

    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    cancel() {
      this.$refs['dialog'].ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.form-dialog {
  padding: 32px 6px 0 6px !important;

  /deep/ .active {
    background-color: rgb(17, 147, 255) !important;
  }
}
</style>
<style lang="scss">
.d72562-c40d-4933-9a24-98c3298365ac {
  li {
    display: flex;
    align-items: center;
    & > div {
      flex-shrink: 0;
    }
    & > div:first-child {
      margin-left: 20px;
    }
  }
}
</style>
