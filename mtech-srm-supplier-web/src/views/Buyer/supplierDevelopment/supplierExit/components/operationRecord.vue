<!-- 操作记录 -->
<template>
  <div>
    <sc-table
      ref="scTableRef"
      row-id="id"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-right-btn="false"
      @refresh="handleSearch"
    >
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { historyColumnData } from '../config/index'
export default {
  components: { ScTable },
  data() {
    return {
      toolbar: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: historyColumnData,
      loading: false,
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        batchId: this.$route.query.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      const res = await this.$API.SupplierPunishment.pageBatchLogQueryApi(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    }
  }
}
</script>
