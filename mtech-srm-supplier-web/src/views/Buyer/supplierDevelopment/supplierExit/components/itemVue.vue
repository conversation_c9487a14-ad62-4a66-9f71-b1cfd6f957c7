<template>
  <div rel="iTemplate" class="iTemplate">
    <p>{{ data.supplierInternalCode }}/{{ data.supplierCode }}</p>
    <p>{{ data.supplierName }}</p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {}
    }
  }
}
</script>
<style lang="scss" scoped>
.iTemplate {
  height: 54px;
  p {
    margin: 0;
    padding: 0;
    width: 350px;
    color: #292929;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  p:first-child {
    margin-top: 10px;
    font-size: 14px;
    height: 14px;
    line-height: 14px;
  }
  p:last-child {
    margin-top: 8px;
    font-size: 12px;
    color: #9a9a9a;
    height: 12px;
    line-height: 12px;
  }
}
</style>
