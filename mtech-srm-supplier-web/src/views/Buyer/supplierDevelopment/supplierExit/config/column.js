import Vue from 'vue'
import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export const freezeColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'applyCode',
    headerText: i18n.t('申请单编码'),
    headerTextAlign: 'center',
    cellTools: []
  },
  {
    width: '150',
    field: 'applyName',
    headerText: i18n.t('申请单名称'),
    headerTextAlign: 'center'
  },
  {
    width: '150',
    field: 'punishObject',
    headerText: i18n.t('对象维度'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('供应商-品类'),
        2: i18n.t('供应商')
      },
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    width: '150',
    field: 'punishRange',
    headerText: i18n.t('组织范围'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('公司级'),
        2: i18n.t('采购组织级'),
        3: i18n.t('集团级')
      },
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('codeTemplate', {
          template: `<div class="code-wrap">
              {{supplierCode}}
            </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            supplierCode() {
              return this.data.relationDTOList[0].supplierCode
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'failReason',
    headerText: i18n.t('下发SAP消息'),
    headerTextAlign: 'center'
  },
  {
    width: '150',
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('codeTemplate', {
          template: `<div class="code-wrap">
              {{supplierInternalCode}}
            </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            supplierInternalCode() {
              return this.data.relationDTOList[0].supplierInternalCode
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('codeTemplate', {
          template: `<div class="code-wrap">
              {{supplierEnterpriseName}}
            </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            supplierEnterpriseName() {
              return this.data.relationDTOList[0].supplierEnterpriseName
            }
          }
        })
      }
    }
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 10,
          text: i18n.t('待提交'),
          cssClass: 'col-inactive'
        },
        {
          value: 20,
          text: i18n.t('待审批'),
          cssClass: 'col-inactive'
        },
        {
          value: 30,
          text: i18n.t('已驳回'),
          cssClass: 'col-reject'
        },
        {
          value: 40,
          text: i18n.t('已完成'),
          cssClass: 'col-active'
        },
        {
          value: 50,
          text: i18n.t('已关闭'),
          cssClass: 'col-active'
        },
        {
          value: 60,
          text: i18n.t('待处理'),
          cssClass: 'col-inactive'
        },
        {
          value: 70,
          text: i18n.t('中止'),
          cssClass: 'col-inactive'
        },
        {
          value: 75,
          text: i18n.t('退出中'),
          cssClass: 'col-inactive'
        },
        {
          value: 80,
          text: i18n.t('已下发'),
          cssClass: 'col-active'
        },
        {
          value: 90,
          text: i18n.t('下发失败'),
          cssClass: 'col-inactive'
        },
        {
          value: 100,
          text: i18n.t('下发成功'),
          cssClass: 'col-active'
        },
        {
          value: 110,
          text: i18n.t('已发布'),
          cssClass: 'col-active'
        }
      ]
    }
    // template: () => {
    //   return {
    //     template: Vue.component("statusTemplate", {
    //       template: `<div class="status-wrap" style="">
    //           <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
    //             display: inline-block;
    //             padding: 0 4px;
    //             height: 20px;
    //             line-height: 20px;
    //             border-radius: 2px;
    //             font-size: 12px;
    //             font-weight: 500;
    //             background: rgba(99,134,193,0.1);
    //             color: #6386C1;
    //           " >{{ statusMap[data.applyStatus] }}</span>
    //           <span v-if="data.applyStatus == 30" style="
    //             display: inline-block;
    //             padding: 0 4px;
    //             height: 20px;
    //             line-height: 20px;
    //             border-radius: 2px;
    //             font-size: 12px;
    //             font-weight: 500;
    //             background: rgba(237,86,51,.1);
    //             color: #ED5633;
    //           " >{{ statusMap[data.applyStatus] }}</span>
    //           <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
    //             display: inline-block;
    //             padding: 0 4px;
    //             height: 20px;
    //             line-height: 20px;
    //             border-radius: 2px;
    //             font-size: 12px;
    //             font-weight: 500;
    //             background: rgba(154,154,154,.1);
    //             color: #9A9A9A;
    //           " >{{ statusMap[data.applyStatus] }}</span>

    //           </div>`,
    //       data: function () {
    //         return {
    //           data: {},
    //           statusMap: {
    //             10: i18n.t("待提交"),
    //             20: i18n.t("待审批"),
    //             30: i18n.t("已驳回"),
    //             40: i18n.t("已完成"),
    //             50: i18n.t("已关闭"),
    //           },
    //         };
    //       },
    //     }),
    //   };
    // },
  },
  {
    width: '150',
    field: 'failReason',
    headerText: i18n.t('同步备注'),
    headerTextAlign: 'center'
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    headerTextAlign: 'center'
  },
  {
    width: '150',
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    headerTextAlign: 'center',
    type: 'date',
    format: 'YYYY-MM-DD',
    template: () => {
      return {
        template: Vue.component('codeTemplate', {
          template: `<div class="code-wrap">
              {{ createDate }}
            </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            createDate() {
              return utils.formateTime(Number(this.data.createDate))
            }
          }
        })
      }
    }
  }
]

// 附件
export const fileColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    headerText: i18n.t('附件名称'),
    headerTextAlign: 'center',
    width: '180',
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>
            <a @click="preview()">{{data.fileName}}</a>
            <span style="margin-left:10px;cursor: pointer;" @click="upload">{{ $t('下载') }}</span>
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          },
          methods: {
            preview() {
              let params = {
                id: this.data.fileId,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload() {
              this.$API.SupplierPunishment.fileDownload(this.data.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${this.data.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'fileSize',
    width: '120',
    headerText: i18n.t('附件大小'),
    headerTextAlign: 'center'
  },
  {
    field: 'updateUserName',
    width: '120',
    headerText: i18n.t('上传人'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('codeTemplate', {
          template: `<div class="code-wrap">
              {{ createDate }}
            </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            createDate() {
              return this.data.updateUserName ? this.data.updateUserName : this.data.createUserName
            }
          }
        })
      }
    }
  },
  {
    field: 'modifyDate',
    width: '120',
    headerText: i18n.t('上传时间'),
    headerTextAlign: 'center',
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  },
  {
    field: 'remark',
    width: '180',
    headerTextAlign: 'center',
    headerText: i18n.t('备注')
  }
]
