<!-- 批量详情 -->
<template>
  <div class="supplierDetail-container">
    <!-- 顶部信息 start -->
    <div class="header-status flex-box">
      <div class="titles-box">
        <div class="main-line flex-box">
          <div class="main-title">
            {{ infoDTO.applyName || '--' }}
          </div>
          <div class="btns-box flex-box">
            <div class="invite-btn" @click="handleBack">
              <mt-button css-class="e-info">{{ $t('返回') }}</mt-button>
            </div>
            <template v-if="canEdit">
              <div class="invite-btn" @click="handleSave" v-if="infoDTO.applyType !== 'relieve'">
                <mt-button css-class="e-info">{{ $t('保存') }}</mt-button>
              </div>
              <div class="invite-btn" @click="handleSubmit">
                <mt-button css-class="e-info">{{ $t('保存并提交') }}</mt-button>
              </div>
            </template>
          </div>
        </div>
        <div class="sub-title flex-box">
          <div class="normal-title">{{ $t('申请单编码') }}：{{ infoDTO.applyCode || '--' }}</div>
          <div class="normal-title">{{ $t('创建人') }}：{{ infoDTO.createUserName || '--' }}</div>
          <div class="normal-title">{{ $t('创建时间') }}：{{ infoDTO.createTime || '--' }}</div>
          <div class="normal-title">{{ $t('申请人') }}：{{ infoDTO.applyerName || '--' }}</div>
          <div class="normal-title">
            {{ $t('申请人部门') }}：{{ infoDTO.applyerDeptName || '--' }}
          </div>
        </div>
        <div class="sub-title flex-box mr-20">
          <div class="normal-title-dan">
            <span>{{ $t('类型') }}:</span>
            {{ punishmentType[punishDTO.businessType] || $t('供应商淘汰-批量') }}
          </div>
          <div class="normal-title-dan"></div>
          <div class="normal-title-dan" v-if="punishDTO.penaltyPeriodStart">
            <span>{{ $t('处罚时间') }}：</span>
            {{ punishDTO.penaltyPeriodStart | filterTime }}～{{
              punishDTO.penaltyPeriodEnd | filterTime
            }}
          </div>
        </div>
      </div>
    </div>
    <!-- 头部结束 -->
    <div>
      <mt-tabs
        id="stage-config-tabs"
        class="toggle-tab"
        :e-tab="false"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>

      <div class="toggle-content">
        <component
          :is="currentComponent"
          v-show="selectIndex === currentComponentIndex"
          :ref="componentRefs[selectIndex]"
          :can-edit="canEdit"
          :apply-info="info.infoDTO"
          :id="punishDTO.applyId"
          :mile="punishDTO.milepost"
          @update:applyReason="handleApplyReasonUpdate"
        />
      </div>
    </div>
  </div>
</template>

<script>
import exitList from './components/exitList.vue'
import documentConfig from './components/documentConfig.vue'
import operationRecord from './components/operationRecord.vue'
import exitMilestone from './components/exitMilestone.vue'
import utils from '@/utils/utils'

export default {
  name: 'BatchDetail',
  components: { exitList, documentConfig, operationRecord, exitMilestone },

  data() {
    return {
      info: {
        infoDTO: {},
        punishDTO: {}
      },
      punishmentType: {
        black: this.$t('供应商拉黑'),
        freeze: this.$t('供应商冻结'),
        disuse: this.$t('供应商淘汰-批量'),
        removeBlack: this.$t('供应商解除拉黑'),
        removeFreeze: this.$t('供应商解除冻结'),
        removeDisuse: this.$t('供应商解除淘汰')
      },
      selectIndex: 0,
      tabList: [
        { title: this.$t('批量退出供应商清单') },
        { title: this.$t('单据配置') },
        { title: this.$t('操作记录') },
        { title: this.$t('退出里程碑') }
      ],
      componentRefs: ['exitListRef', 'documentConfigRef', 'operationRecordRef', 'exitMilestoneRef']
    }
  },

  computed: {
    infoDTO() {
      return this.info.infoDTO || {}
    },
    punishDTO() {
      return this.info.punishDTO || {}
    },
    canEdit() {
      return [10, 30].includes(this.infoDTO.applyStatus)
    },
    currentComponent() {
      const components = ['exitList', 'documentConfig', 'operationRecord', 'exitMilestone']
      return components[this.selectIndex]
    },
    currentComponentIndex() {
      return this.selectIndex
    }
  },

  filters: {
    filterTime: (value) => utils.formateTime(value, 'yyyy-MM-dd')
  },

  created() {
    this.getDetail()
  },

  methods: {
    async getDetail() {
      try {
        const params = {
          id: this.$route.query.id,
          batchFlag: 1,
          page: { current: 1, size: 20 }
        }
        const res = await this.$API.SupplierPunishment.batchDetailApi(params)
        if (res.code === 200) {
          this.info = res.data
        }
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$toast({
          content: this.$t('获取详情失败，请重试'),
          type: 'warning'
        })
      }
    },

    // 获取请求参数
    getRequestParams() {
      const { infoDTO, punishDTO, relationDTOList } = this.info
      return {
        infoDTO,
        punishDTO,
        relationDTOList
      }
    },

    // 处理API响应
    handleApiResponse(res, successMessage) {
      if (res.code === 200) {
        this.$toast({
          content: successMessage,
          type: 'success'
        })
        return true
      }
      return false
    },

    // 保存数据
    async saveData() {
      const params = this.getRequestParams()
      const res = await this.$API.SupplierPunishment.batchUpdateApi(params)
      if (!this.handleApiResponse(res, this.$t('保存成功！'))) {
        throw new Error(this.$t('保存失败'))
      }
      return res
    },

    // 提交数据
    async submitData() {
      const res = await this.$API.SupplierPunishment.batchSubmitApi([
        {
          batchFlag: 1,
          id: this.info.infoDTO.id
        }
      ])
      if (!this.handleApiResponse(res, this.$t('提交成功'))) {
        throw new Error(this.$t('提交失败'))
      }
      return res
    },

    // 刷新详情
    async refreshDetail() {
      await this.getDetail()
    },

    async handleSave() {
      this.$loading()
      try {
        await this.saveData()
        await this.refreshDetail()
      } catch (error) {
        this.$toast({
          content: error.message || this.$t('操作失败，请重试'),
          type: 'warning'
        })
      } finally {
        this.$hloading()
      }
    },

    async handleSubmit() {
      this.$loading()
      try {
        await this.saveData()
        await this.submitData()
        await this.refreshDetail()
      } catch (error) {
        this.$toast({
          content: error.message || this.$t('操作失败，请重试'),
          type: 'warning'
        })
      } finally {
        this.$hloading()
      }
    },

    handleSelectTab(index) {
      this.selectIndex = index
    },

    handleBack() {
      this.$router.go(-1)
    },

    handleApplyReasonUpdate(value) {
      this.info.infoDTO.applyReason = value
    }
  }
}
</script>

<style lang="scss">
.mt-tabs {
  margin: 10px 0;
  width: 100%;
}
</style>

<style lang="scss" scoped>
.supplierDetail-container {
  height: 100%;
  padding-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .titles-box {
      width: 100%;
      flex-direction: column;
      justify-content: space-between;

      .main-line {
        width: 100%;
        justify-content: space-between;
      }

      .main-title {
        font-size: 20px;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }

      .sub-title {
        width: 100%;
        font-size: 12px;
        color: rgba(41, 41, 41, 1);
        margin-top: 10px;

        .normal-title {
          font-size: 12px;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
        }

        .normal-title-dan {
          font-size: 14px;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
          font-weight: 600;

          span {
            font-weight: 600;
          }
        }
      }

      .mr-20 {
        margin-top: 20px;
      }
    }

    .btns-box {
      align-items: center;
      font-size: 14px;
      max-width: 300px;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      justify-content: flex-end;

      .invite-btn {
        margin-right: 20px;
        cursor: pointer;
        font-weight: 600;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.flex-box {
  display: flex;
}

.flex1 {
  flex: 1;
}
</style>
