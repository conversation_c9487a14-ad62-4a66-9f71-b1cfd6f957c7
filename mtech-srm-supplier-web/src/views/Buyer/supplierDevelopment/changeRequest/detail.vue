<template>
  <div class="detallWrap">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="titles-box">
        <div class="mian-line fbox">
          <div class="mian-title">
            <template> {{ title }} </template>
          </div>
          <div class="btns-box fbox">
            <template>
              <div class="invite-btn" @click="saveAndSubmit" v-if="pageType != 'detail'">
                <mt-button css-class="e-info">{{ $t('确认并提交') }}</mt-button>
              </div>
              <div class="invite-btn" @click="handleOver" v-if="pageType != 'detail'">
                <mt-button css-class="e-info">{{ $t('驳回') }}</mt-button>
              </div>
              <div class="invite-btn" @click="onBack">
                <mt-button css-class="e-info">{{ $t('返回') }}</mt-button>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="contentWrap">
      <div class="purInfo" v-show="pageType != 'detail'">
        <div class="b-info-title">{{ $t('品质评估意见') }}</div>
        <div class="purBox">
          <mt-form
            ref="formInfoRef"
            class="detail-effectiveorg--form labelWidth"
            :model="formObject"
            :rules="rules"
          >
            <mt-row :gutter="12">
              <mt-col :span="24">
                <mt-form-item prop="qualitySuggest" :label="$t('品质评估意见')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formObject.qualitySuggest"
                    :multiline="true"
                    :rows="4"
                    :placeholder="$t('填写品质风险及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :span="6">
                <mt-form-item prop="devId" :label="$t('研发评估人')" label-style="left">
                  <mt-select
                    css-class="e-outline"
                    v-model="formObject.devId"
                    :data-source="devList"
                    :fields="{ text: 'text', value: 'value' }"
                    :placeholder="$t('请选择研发评估人')"
                    @change="handleChangeDev($event)"
                    :open-dispatch-change="true"
                    :filtering="filtering"
                    :allow-filtering="true"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :span="6">
                <mt-form-item prop="purchaseId" :label="$t('采购评估人')" label-style="left">
                  <mt-select
                    css-class="e-outline"
                    v-model="formObject.purchaseId"
                    :show-clear-button="true"
                    :data-source="purchaseList"
                    :fields="{ text: 'text', value: 'value' }"
                    :placeholder="$t('请选择采购评估人')"
                    @change="handleChangePurchase($event)"
                    :open-dispatch-change="true"
                    :filtering="filtering2"
                    :allow-filtering="true"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :span="6">
                <mt-form-item
                  prop="manufactProcessId"
                  :label="$t('制造工艺评估人')"
                  label-style="left"
                >
                  <mt-select
                    css-class="e-outline"
                    v-model="formObject.manufactProcessId"
                    :data-source="manufactList"
                    :fields="{ text: 'text', value: 'value' }"
                    :placeholder="$t('请选择制造工艺评估人')"
                    @change="handleChangeManufactProcess($event)"
                    :open-dispatch-change="true"
                    :filtering="filtering3"
                    :allow-filtering="true"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :span="6">
                <mt-form-item
                  prop="jointTrialId"
                  :label="$t('技术支持会审评估人')"
                  label-style="left"
                >
                  <mt-select
                    css-class="e-outline"
                    v-model="formObject.jointTrialId"
                    :show-clear-button="true"
                    :data-source="jointTrialList"
                    :fields="{ text: 'text', value: 'value' }"
                    :placeholder="$t('请选择技术支持会审评估人')"
                    @change="handleChangeJointTrial($event)"
                    :open-dispatch-change="true"
                    :filtering="filtering4"
                    :allow-filtering="true"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
            </mt-row>
          </mt-form>
        </div>
      </div>
      <div class="purInfo" v-show="pageType === 'detail'">
        <div class="b-info-title">{{ $t('采方处理信息') }}</div>
        <div class="purBox">
          <mt-form
            ref="effectiveOrgDTO"
            class="detail-effectiveorg--form labelWidth"
            :model="formData"
            :rules="rules"
          >
            <mt-row :gutter="12">
              <mt-col :span="24">
                <mt-form-item prop="qualitySuggest" :label="$t('品质评估意见')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.qualitySuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示品质风险及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
            <mt-row :gutter="12" class="boxMiddle">
              <mt-col :span="6">
                <mt-form-item prop="changeCode" :label="$t('采购评估人')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    v-model="formData.purchaseName"
                    :disabled="true"
                  />
                </mt-form-item>
              </mt-col>
              <mt-col :span="18">
                <mt-form-item prop="purchaseSuggest" :label="$t('采购评估意见')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.purchaseSuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示成本变化及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
            <mt-row :gutter="12" class="boxMiddle">
              <mt-col :span="6">
                <mt-form-item prop="devName" :label="$t('研发评估人')" label-style="left">
                  <mt-input css-class="e-outline" v-model="formData.devName" :disabled="true" />
                </mt-form-item>
              </mt-col>
              <mt-col :span="18">
                <mt-form-item prop="devSuggest" :label="$t('研发评估意见')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.devSuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示技术风险及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
            <mt-row :gutter="12" class="boxMiddle">
              <mt-col :span="6">
                <mt-form-item
                  prop="manufactProcessName"
                  :label="$t('制造工艺评估人')"
                  label-style="left"
                >
                  <mt-input
                    css-class="e-outline"
                    v-model="formData.manufactProcessName"
                    :disabled="true"
                  />
                </mt-form-item>
              </mt-col>
              <mt-col :span="18">
                <mt-form-item
                  prop="manufactProcessSuggest"
                  :label="$t('制造工艺评估意见')"
                  label-style="left"
                >
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.manufactProcessSuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示制造工艺及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
            <mt-row :gutter="12" class="boxMiddle">
              <mt-col :span="6">
                <mt-form-item
                  prop="jointTrialName"
                  :label="$t('技术支持会审评估人')"
                  label-style="left"
                >
                  <mt-input
                    css-class="e-outline"
                    v-model="formData.jointTrialName"
                    :disabled="true"
                  />
                </mt-form-item>
              </mt-col>
              <mt-col :span="18">
                <mt-form-item
                  prop="jointTrialSuggest"
                  :label="$t('技术支持会审意见')"
                  label-style="left"
                >
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.jointTrialSuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示技术支持会审评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
          </mt-form>
        </div>
      </div>
      <div class="b-info-title">{{ $t('基本信息') }}</div>
      <div class="formWrap">
        <mt-form ref="formRef" class="detail-effectiveorg--form labelWidth2" :model="formData">
          <mt-row :gutter="12">
            <mt-col :span="8">
              <mt-form-item prop="changeCode" :label="$t('4M1E变更单号')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.changeCode"
                  :placeholder="$t('请输入4M1E变更单号')"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="bottom">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.supplierName"
                  :placeholder="$t('请输入供应商名称')"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="customerOrgCode" :label="$t('客户组织')" label-style="left">
                <mt-select
                  css-class="e-outline"
                  v-model="formData.customerOrgCode"
                  float-label-type="Never"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :show-clear-button="true"
                  :data-source="orgList"
                  :fields="{ text: 'orgName', value: 'orgCode' }"
                  :placeholder="$t('请选择客户组织')"
                  @change="handleFactoryChange($event)"
                  :disabled="true"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :span="24">
              <mt-form-item prop="company" :label="$t('客户公司')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.orgName"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="status" :label="$t('单据状态')" label-style="left">
                <mt-select
                  css-class="e-outline"
                  :disabled="true"
                  v-model="formData.status"
                  :data-source="statusList"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :span="16">
              <mt-form-item
                prop="changeObjIds"
                :label="$t('变更对象')"
                label-style="left"
                style="margin-bottom: 40px"
              >
                <div class="checkbox-wrap" v-for="item in changeObjList" :key="item.id">
                  <mt-checkbox
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item)"
                    :disabled="true"
                  ></mt-checkbox>
                </div>
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="headUserName" :label="$t('供方负责人')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.headUserName"
                  :placeholder="$t('请输入供方负责人')"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="headUserPhone" :label="$t('联系电话')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.headUserPhone"
                  :placeholder="$t('请输入联系电话')"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="24">
              <mt-form-item prop="reason" :label="$t('变更原因')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  ref="editorRef"
                  v-model="formData.reason"
                  :multiline="true"
                  :rows="4"
                  :disabled="true"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>
      <div class="textareaBox">
        <mt-row :gutter="24">
          <mt-col :span="12">
            <div class="b-info-title">{{ $t('现状描述') }}</div>
            <div class="articleBox">
              <rich-text-editor
                ref="editor"
                css-class="rich-box"
                :height="300"
                :max-length="500"
                v-model="formData.currentDesc"
                rich-text-id="d6f35f60-e7d7-4329-90b3-c08c8dd04522"
                :disabled="true"
              >
              </rich-text-editor>
            </div>
          </mt-col>
          <mt-col :span="12">
            <div class="b-info-title">{{ $t('预期的变更效果') }}</div>
            <div class="articleBox">
              <rich-text-editor
                ref="editor2"
                css-class="rich-box"
                :height="300"
                :max-length="500"
                v-model="formData.expectDesc"
                rich-text-id="951ce4c4-2e34-42c5-8bff-a7bc7ced0ac7"
                :disabled="true"
              >
              </rich-text-editor>
            </div>
          </mt-col>
        </mt-row>
      </div>
      <div class="fileBox" v-show="isShowFile">
        <div class="b-info-title">{{ $t('附件') }}</div>
        <mt-template-page
          ref="templateRef"
          height="300"
          :template-config="pageConfig"
          :use-tool-template="false"
          :padding-top="false"
          @handleClickCellTool="handleClickCellTool"
        >
        </mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import { enclosurea } from './config/detail.js'
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
export default {
  components: {
    RichTextEditor
  },
  data() {
    return {
      id: '', //变更单id
      changeObjList: [], // 变更对象数据源
      devList: [],
      purchaseList: [],
      manufactList: [],
      jointTrialList: [],
      statusList: [],
      orgList: [
        { orgCode: 'BDComCode', orgName: this.$t('白电公司') },
        { orgCode: 'KTComCode', orgName: this.$t('空调公司') },
        { orgCode: 'TVComCode', orgName: this.$t('TV公司') }
      ], //客户组织数据源
      formData: {
        changeCode: '',
        supplierName: '', //供应商名称
        supplierCode: '', //供应商Code
        supplierId: '0', //供应商Id
        customerOrgCode: '', //客户组织Code
        customerOrgName: '', //客户组织Name
        orgCode: '', //公司名称
        orgId: '0', //公司id
        orgName: '', //公司name
        status: 0,
        changeObjIds: [], // 变更对象id
        changeObjNames: [], // 变更对象
        headUserName: '', //供方负责人
        headUserPhone: '', //联系电话
        reason: '', //变更原因
        currentDesc: '', //现状描述
        expectDesc: '', //预期的变更效果
        purchaseSuggest: '',
        devSuggest: '',
        qualitySuggest: '',
        manufactProcessSuggest: '', //制造工艺意见
        jointTrialSuggest: '' //技术支持会审意见
      },
      //表单
      formObject: {
        devCode: '',
        devId: '',
        devName: '',
        purchaseCode: '',
        purchaseId: '',
        purchaseName: '',
        qualitySuggest: '',
        manufactProcessCode: '',
        manufactProcessId: '',
        manufactProcessName: '',
        jointTrialCode: '',
        jointTrialId: '',
        jointTrialName: ''
      },
      rules: {
        qualitySuggest: [
          { required: true, message: this.$t('请输入品质评估意见'), trigger: 'blur' }
        ],
        devId: [{ required: true, message: this.$t('请选择研发评估人') }],
        purchaseId: [{ required: true, message: this.$t('请选择采购评估人') }],
        manufactProcessId: [{ required: true, message: this.$t('请选择制造工艺评估人') }],
        jointTrialId: [{ required: true, message: this.$t('请选择技术支持会审评估人') }]
      },
      pageConfig: [
        {
          gridId: '0cd8a4c5-b457-4502-b4ed-5cdd22ffd64e',
          toolbar: {
            tools: [[]]
          },
          grid: {
            allowPaging: false, //不分页
            columnData: enclosurea,
            dataSource: []
            // asyncConfig: {
            //   url: '/supplier/tenant/common/file/list',
            //   rules: [
            //     //配置rules，数组格式，请求入参时，追加到rules中
            //     {
            //       label: '',
            //       field: 'bizId',
            //       type: 'number',
            //       operator: 'equal',
            //       value: '1565531962411302914'
            //     }
            //   ],
            //   params: { bizId: '1565531962411302914' }
            // }
          }
        }
      ]
    }
  },
  computed: {
    title() {
      //根据页面类型，返回不同的标题
      let title = ''
      switch (this.$route?.query?.type) {
        case 'edit':
          title = this.$t('确认4M1E变更单')
          break
        case 'detail':
          title = this.$t('4M1E变更单详情')
          break
        default:
          title = this.$t('4M1E变更单详情')
          break
      }
      return title
    },
    //是否显示附件 当变更对象changeObject 选中材料 3  checked 时显示
    isShowFile() {
      return this.formData.changeObjIds.includes(3)
    },
    //页面类型
    pageType() {
      return this.$route?.query?.type || 'add'
    }
  },
  async mounted() {
    if (this.pageType == 'edit') {
      this.filtering = utils.debounce(this.filtering, 1000)
      this.filtering2 = utils.debounce(this.filtering2, 1000)
      this.filtering3 = utils.debounce(this.filtering3, 1000)
      this.filtering4 = utils.debounce(this.filtering4, 1000)
      this.getPerson() //获取人员的下拉数据
    }
    //获取 query
    this.id = this.$route?.query?.id
    await this.getDict('4M1E STATUS', 'statusList')
    await this.getDict('changeObjNames', 'changeObjList')
    if (this.pageType == 'edit' || this.pageType == 'detail') {
      //获取详情 数据回显
      await this.getDetail()
    }
    //切换富文本编辑器的字数统计方式 字数  字节数
    this.$nextTick(() => {
      this.$refs.editor.toggleCountWords()
      this.$refs.editor2.toggleCountWords()
    })
  },
  methods: {
    filtering(e) {
      let text = e.text
      this.getPerson(text, 'devList')
    },
    filtering2(e) {
      let text = e.text
      this.getPerson(text, 'purchaseList')
    },
    filtering3(e) {
      let text = e.text
      this.getPerson(text, 'manufactList')
    },
    filtering4(e) {
      let text = e.text
      this.getPerson(text, 'jointTrialList')
    },
    //获取人员
    getPerson(text, type) {
      let params = {
        page: {
          current: 1,
          size: 20
        },
        subjectType: 0
      }
      if (text) {
        params['condition'] = 'or'
        params['rules'] = [
          {
            label: this.$t('账号'),
            field: 'externalCode',
            type: 'string',
            operator: 'contains',
            value: text
          },
          {
            label: this.$t('姓名'),
            field: 'employeeName',
            type: 'string',
            operator: 'contains',
            value: text
          }
        ]
      }
      this.$API.purChangeRequest.employee(params).then((res) => {
        let data = cloneDeep(res.data.records)
        let list = []
        data.forEach((item) => {
          list.push({
            text: item.employeeCode + '-' + item.employeeName,
            value: item.id,
            id: item.id,
            externalCode: item.externalCode,
            employeeName: item.employeeName
          })
        })
        if (type) {
          this[type] = list
        } else {
          this.devList = list
          this.purchaseList = list
          this.manufactList = list
          this.jointTrialList = list
        }
      })
    },
    //详情
    async getDetail() {
      this.$API.purChangeRequest.detail({ id: this.id }).then((res) => {
        if (res.code == 200) {
          res.data.changeObjIds = res.data.changeObjIds.split(',').map((item) => {
            return Number(item)
          }) //转为数字数组
          this.formData = res.data || {}
          //根据formObject的数据 比对res.data的数据 生成要回显的数据 品质评估和基本信息是不同的form对象
          let formObject = cloneDeep(this.formObject)
          for (let key in formObject) {
            if (res.data[key]) {
              formObject[key] = res.data[key] || ''
            }
          }
          this.formObject = formObject
          //回显多选框选中
          this.changeObjList.forEach((item) => {
            if (this.formData.changeObjIds.includes(Number(item.value))) {
              item.checked = true
            }
          })
          //回显附件
          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.fileList || [])
        }
      })
    },
    //获取当前页面需要的字典
    async getDict(code, key, type = 'number') {
      let res = await this.$API.supplierChangeRequest.queryDict({
        dictCode: code
      })
      let list = []
      if (res.code === 200) {
        res.data.map((item) => {
          list.push({
            value: type == 'number' ? Number(item.itemCode) : item.itemCode,
            text: item.itemName,
            cssClass: 'col-',
            checked: false
          })
        })
      }
      if (key) {
        this[key] = list
      }
    },
    handleClickCellTool() {},
    //多选框change
    checkChange(e) {
      //更新多选选中状态
      this.changeObjList.forEach((item) => {
        if (item.value === e.value) {
          item.checked = !item.checked
        }
      })
      //联动更新formData.changeObjIds
      let changeObject = this.changeObjList.filter((item) => item.checked).map((item) => item.value)
      this.$set(this.formData, 'changeObjIds', changeObject)
    },
    handleFactoryChange() {},
    //研发评估人下拉框change
    handleChangeDev(e) {
      const { itemData } = e
      if (itemData) {
        this.formObject.devId = itemData.value
        this.formObject.devName = itemData.employeeName
        this.formObject.devCode = itemData.externalCode
      } else {
        this.formObject.devId = ''
        this.formObject.devName = ''
        this.formObject.devCode = ''
      }
    },
    //采购评估人下拉框change
    handleChangePurchase(e) {
      const { itemData } = e
      if (itemData) {
        this.formObject.purchaseId = itemData.value
        this.formObject.purchaseName = itemData.employeeName
        this.formObject.purchaseCode = itemData.externalCode
      } else {
        this.formObject.purchaseId = ''
        this.formObject.purchaseName = ''
        this.formObject.purchaseCode = ''
      }
    },
    //制造工艺 下拉框change
    handleChangeManufactProcess(e) {
      const { itemData } = e
      if (itemData) {
        this.formObject.manufactProcessId = itemData.value
        this.formObject.manufactProcessName = itemData.employeeName
        this.formObject.manufactProcessCode = itemData.externalCode
      } else {
        this.formObject.manufactProcessId = ''
        this.formObject.manufactProcessName = ''
        this.formObject.manufactProcessCode = ''
      }
    },
    //技术支持 下拉框change
    handleChangeJointTrial(e) {
      const { itemData } = e
      if (itemData) {
        this.formObject.jointTrialId = itemData.value
        this.formObject.jointTrialName = itemData.employeeName
        this.formObject.jointTrialCode = itemData.externalCode
      } else {
        this.formObject.jointTrialId = ''
        this.formObject.jointTrialName = ''
        this.formObject.jointTrialCode = ''
      }
    },
    //驳回
    handleOver() {
      let params = {
        id: this.id,
        ...this.formObject
      }
      //驳回 检验品质评估意见必填
      if (params.qualitySuggest == '' || params.qualitySuggest == null) {
        this.$toast({ content: this.$t('品质评估意见必填'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认驳回当前变更单吗？')
        },
        success: () => {
          this.$API.purChangeRequest.refuse(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('驳回成功'),
                type: 'success'
              })
              this.onBack()
            }
          })
        }
      })
    },
    //确认提交
    saveAndSubmit() {
      //提交 校验品质评估意见 研发评估人 采购评估人必填
      let params = {
        id: this.id,
        ...this.formObject
      }
      this.$refs.formInfoRef.validate().then((res) => {
        if (res) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认提交当前变更单吗？')
            },
            success: () => {
              this.$API.purChangeRequest.submitOA(params).then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('提交成功'),
                    type: 'success'
                  })
                  this.onBack()
                }
              })
            }
          })
        }
      })
    },
    onBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .labelWidth .mt-form-item .label {
  width: 140px;
}
/deep/ .labelWidth {
  .mt-input,
  .mt-select {
    flex: 1;
  }
}
/deep/ .labelWidth2 .mt-form-item .label {
  width: 140px;
}
/deep/ .labelWidth2 {
  .mt-input,
  .mt-select {
    flex: 1;
  }
}
.boxMiddle .mt-col-6 {
  margin-top: 40px;
}
.toolbar-container {
  display: none;
}
.articleBox {
  width: 100%;
}
.detallWrap {
  min-height: 100%;
  padding-top: 20px;
  background: #fff;
  padding-bottom: 10px;
}
.textareaBox {
  width: 100%;
}
.fileBox {
  width: 100%;
  margin: 20px 0;
  height: 400px;
}
.header-status {
  padding: 20px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .titles-box {
    width: 100%;
    flex-direction: column;
    justify-content: space-between;

    .mian-line {
      width: 100%;
      justify-content: space-between;
    }

    .mian-title {
      font-size: 20px;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .scroll-box {
      width: 100%;
      display: flex;
      white-space: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      margin-top: 20px;

      .normal-title-box {
        height: 88px;
        background: rgba(255, 255, 255, 1);
        border-radius: 8px;
        font-size: 14px;

        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        margin-right: 30px;
        padding: 0 20px;
      }

      .gong-title {
        padding: 20px 0 0 0;
        font-size: 14px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
      }

      .gong-id {
        padding: 20px 0 0 0;
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 70, 156, 1);
      }
    }

    .sub-title {
      width: 100%;
      font-size: 12px;
      color: rgba(41, 41, 41, 1);
      margin-top: 10px;
      .normal-title {
        font-size: 12px;
        color: rgba(41, 41, 41, 1);
        margin-right: 30px;
      }
      .normal-title-dan {
        font-size: 14px;
        color: rgba(41, 41, 41, 1);
        margin-right: 30px;
        font-weight: 600;

        span {
          font-weight: 600;
        }

        .b-color {
          color: #00469c;
        }
      }
    }

    .mr-20 {
      margin-top: 20px;
    }
  }

  .btns-box {
    align-items: center;
    font-size: 14px;
    max-width: 300px;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
    justify-content: flex-end;

    .invite-btn {
      margin-right: 20px;
      cursor: pointer;
      font-weight: 600;
    }

    .invite-btn:last-child {
      margin-right: 0;
    }
  }
}
.purInfo {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
}
.purBox {
  width: 100%;
  .mt-input {
    width: 100%;
  }
}
.contentWrap {
  border: 1px solid rgba(232, 232, 232, 1);
  margin-top: 10px;
  height: auto;
  // height: calc(100vh - 200px);
  padding: 20px;
  // overflow: scroll;
  margin-bottom: 10px;
}
.b-info-title {
  position: relative;
  width: 100%;
  height: 30px;
  line-height: 16px;
  font-size: 16px;
  color: rgba(41, 41, 41, 1);
  padding-left: 14px;
  margin-top: 10px;
}
.b-info-title:before {
  content: ' ';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #00469c;
  position: absolute;
  left: 0;
  top: 0;
}
.formWrap {
  width: 100%;
}
/deep/ .formWrap .mt-input {
  width: 100%;
  // .e-input-group {
  //   border: 1px solid rgba(0, 0, 0, 0.42);
  //   padding-left: 5px;
  // }
}

.checkbox-wrap {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 2px;
  margin-right: 20px;
  display: flex;
  align-items: center;
}
</style>
