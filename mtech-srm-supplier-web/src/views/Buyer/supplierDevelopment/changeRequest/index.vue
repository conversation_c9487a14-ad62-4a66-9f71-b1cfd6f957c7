<template>
  <div class="changeRequestBox">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { columnData } from './config/index'
import { i18n } from '@/main.js'

export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: 'e9da56b3-6710-4ab3-afe2-f513e6144647',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'audit',
                  title: i18n.t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData(),
            asyncConfig: {
              url: '/supplier/tenant/buyer/change/4m/pageList',
              params: {},
              serializeList: (list) => {
                // list.map((item) => {
                //   item.changeObjIds = '1,2'
                // })
                // //转成数字数组 配合searchOptions的multi-select使用
                // list.map((item) => {
                //   item.changeObjIds = item.changeObjIds.split(',').map((item) => {
                //     return Number(item)
                //   })
                // })
                return list
              }
            }
          }
        }
      ]
    }
  },
  async mounted() {
    let statusList = await this.getDict('4M1E STATUS', 'number')
    let changeObjNamesList = await this.getDict('changeObjNames', 'number')
    this.pageConfig[0].grid.columnData = columnData(changeObjNamesList, statusList)
  },
  methods: {
    //快捷查询前对参数进行处理
    // handleQuickSearch(data) {
    //   const { rules } = data
    //   //将data.rules里的changeObjIds转为字符串 逗号分隔
    //   rules.rules.forEach((item) => {
    //     //
    //     if (item.field === 'changeObjIds') {
    //       console.log('changeObjIds', item.value)
    //       item.value = item.value.join(',')
    //     }
    //   })
    // },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id != 'audit') {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'audit') {
        const list = e.gridRef.getMtechGridRecords()
        this.audit(list)
      }
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: i18n.t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: i18n.t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].id,
        businessType: '4m1eChange'
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    //获取当前页面需要的字典
    async getDict(code, type = 'string') {
      let res = await this.$API.supplierChangeRequest.queryDict({
        dictCode: code
      })
      let list = []
      if (res.code === 200) {
        res.data.map((item) => {
          list.push({
            value: type === 'string' ? item.itemCode : Number(item.itemCode),
            text: item.itemName,
            cssClass: 'col-'
          })
        })
      }
      return list
    },
    //点击单元格
    handleClickCellTitle(e) {
      const { data } = e
      let type = 'edit'
      //单据状态为 3：审批中|| 4：已完成 ，点击单元格跳转到详情页只读
      if (data.status == 3 || data.status == 4) {
        type = 'detail'
      }
      this.$router.push({
        path: '/supplier/pur/change-request-detail',
        query: {
          id: data.id,
          type,
          time: new Date().getTime() //时间戳 防止缓存
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.changeRequestBox {
  width: 100%;
  height: 100%;
}
</style>
