import Vue from 'vue'
import { i18n } from '@/main.js'
// 附件
export const enclosurea = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    headerText: i18n.t('附件名称'),
    headerTextAlign: 'center',
    width: '180',
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>
            <a @click="preview()">{{data.fileName}}</a>
            <span style="margin-left:10px;cursor: pointer;" @click="upload">{{ $t('下载') }}</span>
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          },
          methods: {
            preview() {
              let params = {
                id: this.data.fileId,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload() {
              this.$API.SupplierPunishment.fileDownload(this.data.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${this.data.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    width: '180',
    headerTextAlign: 'center',
    headerText: i18n.t('备注')
  },
  // {
  //   field: 'fileSize',
  //   width: '120',
  //   headerText: i18n.t('附件大小'),
  //   headerTextAlign: 'center'
  // },
  {
    field: 'updateUserName',
    width: '120',
    headerText: i18n.t('上传人'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('codeTemplate', {
          template: `<div class="code-wrap">
              {{ createDate }}
            </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            createDate() {
              return this.data.updateUserName ? this.data.updateUserName : this.data.createUserName
            }
          }
        })
      }
    }
  },
  {
    field: 'createDate',
    width: '120',
    headerText: i18n.t('上传时间'),
    headerTextAlign: 'center',
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  }
]
