<template>
  <div class="freezeBox">
    <mt-template-page
      ref="templateRef"
      :template-config="blackConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { freezeColumn } from './config/column'
import utils from '@/utils/utils'
import * as XLSX from 'xlsx'
export default {
  data() {
    return {
      businessTypeMap: {
        black: this.$t('拉黑申请单')
      },
      applyStatusMap: {
        10: this.$t('待提交'),
        20: this.$t('待审批'),
        30: this.$t('已驳回'),
        40: this.$t('已完成'),
        50: this.$t('已关闭'),
        60: this.$t('待处理'),
        70: this.$t('中止'),
        80: this.$t('已下发'),
        90: this.$t('下发失败'),
        100: this.$t('下发成功'),
        110: this.$t('已发布')
      },
      sampleData: [],
      blackConfig: [
        {
          gridId: '8bf73fa4-b159-46ec-95fc-bd18e0da7e25',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_table_new', title: this.$t('新增') },
                { id: 'edit', icon: 'icon_table_edit', title: this.$t('编辑') },
                {
                  id: 'delete',
                  icon: 'icon_table_delete',
                  title: this.$t('删除')
                },
                {
                  id: 'updata',
                  icon: 'icon_solid_submit',
                  title: this.$t('提交')
                },
                {
                  id: 'issue',
                  icon: 'icon_solid_submit',
                  title: this.$t('重新下发')
                },
                {
                  id: 'pushorder',
                  icon: 'icon_solid_pushorder',
                  title: this.$t('解除黑名单')
                },
                { id: 'export1', icon: 'icon_table_new', title: this.$t('导出') },
                {
                  id: 'audit',
                  title: this.$t('查看OA审批'),
                  icon: 'icon_solid_editsvg'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: freezeColumn,
            asyncConfig: {
              url: '/supplier/tenant/buyer/apply/punish/pageQuery',
              params: {
                applyTypeList: ['punish'],
                businessTypeList: ['black', 'removeBlack']
              },
              serializeList: (list) => {
                this.sampleData = list
                return list
              }
              // defaultRules: [
              //   {
              //     label: this.$t("状态"),
              //     field: "businessType",
              //     type: "number",
              //     operator: "equal",
              //     value: "black",
              //   },
              // ],
            },
            frozenColumns: 3
          }
        }
      ]
    }
  },
  activated() {
    this.$refs.templateRef.refreshCurrentGridData()
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const sltList = gridRef.getMtechGridRecords()
      if (toolbar.id === 'add') {
        this.addDialog()
      }
      if (toolbar.id === 'edit') {
        this.edit(sltList)
      }
      if (toolbar.id === 'delete') {
        this.delete(sltList)
      }
      if (toolbar.id === 'updata') {
        this.updata(sltList)
      }
      if (toolbar.id === 'issue') {
        this.issue(sltList)
      }
      if (toolbar.id === 'pushorder') {
        this.pushorder(sltList)
      }
      if (toolbar.id == 'export1') {
        let data = []
        console.log(this.sampleData)
        if (sltList.length == 0) {
          data = this.sampleData
        } else {
          data = sltList
        }
        let tableData = [
          [
            this.$t('申请单编码'),
            this.$t('申请单名称'),
            this.$t('类型'),
            this.$t('供应商编号-SAP'),
            this.$t('供应商编号-SRM'),
            this.$t('供应商'),
            this.$t('状态'),
            this.$t('创建人'),
            this.$t('创建日期')
          ]
        ]
        data.forEach((item) => {
          let arr = []
          arr.push(
            item.applyCode,
            item.applyName,
            this.businessTypeMap[item.businessType],
            item?.relationDTOList[0]?.supplierCode,
            item?.relationDTOList[0]?.supplierInternalCode,
            item?.relationDTOList[0]?.supplierEnterpriseName,
            this.applyStatusMap[item.applyStatus],
            item.createUserName,
            utils.formateTime(Number(item.createDate))
          )
          tableData.push(arr)
        })
        console.log(tableData)
        let ws = XLSX.utils.aoa_to_sheet(tableData)
        let wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
        XLSX.writeFile(wb, this.$t(`供应商黑名单清单.xlsx`))
      }
      if (toolbar.id === 'audit') {
        this.audit(sltList)
      }
    },
    issue(sltList) {
      if (!sltList || sltList.length !== 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (sltList[0].applyStatus != 90) {
        this.$toast({ content: this.$t('请先选择下发失败的数据'), type: 'warning' })
        return
      }
      this.$API.SupplierPunishment.punishIssue({
        applyId: sltList[0].id
      }).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('下发成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    pushorder(sltList) {
      if (sltList[0].businessType != 'black') {
        this.$toast({ content: this.$t('请先选择黑名单数据'), type: 'warning' })
        return
      }
      if (
        sltList[0].applyStatus != 80 &&
        sltList[0].applyStatus != 90 &&
        sltList[0].applyStatus != 100
      ) {
        this.$toast({
          content: this.$t('请先选择已下发、下发成功、下发失败的数据'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('./components/blackDialog.vue'),
        data: {
          title: this.$t('解除黑名单'),
          black: true,
          info: sltList
        },
        success: (data) => {
          if (data === 'reload') {
            this.$refs.templateRef.refreshCurrentGridData()
          } else if (data.type === 'jump') {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$router.push({
              path: 'punish-detail',
              query: {
                id: data.id
              }
            })
          }
        }
      })
    },
    audit(sltList) {
      if (sltList.length < 1) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      if (sltList.length > 1) {
        this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
        return
      }
      let params = {
        applyId: sltList[0].id,
        businessType: sltList[0].businessType ? sltList[0].businessType : ''
      }
      this.$API.assessManage.infoGetOALink(params).then((res) => {
        let _data = res.data
        window.open(`${_data}`)
      })
    },
    handleClickCellTitle(e) {
      console.log(e)
      const { data } = e
      this.$router.push({
        path: 'punish-detail-black',
        query: {
          id: data.id
        }
      })
    },

    addDialog() {
      let _this = this
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('新建拉黑申请单'),
          isEdit: false
        },
        success: (data) => {
          console.log(data)
          if (data === 'reload') {
            _this.$refs.templateRef.refreshCurrentGridData()
          } else if (data.type === 'jump') {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$router.push({
              path: 'punish-detail-black',
              query: {
                id: data.id
              }
            })
          }
        }
      })
    },

    delete(sltList) {
      const _this = this
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = sltList.map((item) => item.id)

      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选申请单？'),
          confirm: () => _this.$API.SupplierPunishment.deleteNew({ applyIdList: ids })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    edit(sltList) {
      const _this = this
      if (sltList.some((item) => item.applyStatus === 20 || item.applyStatus === 40)) {
        this.$toast({
          content: this.$t('待审批、已完成状态不能编辑！'),
          type: 'warning'
        })
        return
      }

      if (!sltList || sltList.length !== 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('编辑黑名单'),
          isEdit: true,
          info: sltList
        },
        success: (data) => {
          console.log(data)
          if (data === 'reload') {
            _this.$refs.templateRef.refreshCurrentGridData()
          } else if (data.type === 'jump') {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$router.push({
              path: 'punish-detail-black',
              query: {
                id: data.id
              }
            })
          }
        }
      })
    },

    updata(sltList) {
      const _this = this
      if (sltList.some((item) => item.applyStatus !== 10 && item.applyStatus !== 30)) {
        this.$toast({
          content: this.$t('待提交和驳回的单据可以提交！'),
          type: 'warning'
        })
        return
      }

      let ids = sltList.map((item) => item.id)

      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('是否确认提交所选申请单？'),
          confirm: () =>
            _this.$API.SupplierPunishment.applySubmit({
              applyIdList: ids
            })
        },
        success: () => {
          _this.$toast({ content: this.$t('提交成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.freezeBox {
  width: 100%;
  height: 100%;
}
</style>
