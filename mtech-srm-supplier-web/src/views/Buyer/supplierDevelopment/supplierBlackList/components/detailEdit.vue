<template>
  <div class="task-center fbox">
    <div class="task-sidebar" v-if="fullTaskIndexArr">
      <div
        v-for="(taskItem, index) in fullTaskIndexArr"
        :key="index"
        @click="scrollInto(index)"
        :class="activeClass == index ? 'active' : ''"
      >
        <div class="collapse-header">
          {{ taskItem }}
        </div>
      </div>
    </div>
    <div class="collapse-content flex1">
      <div :ref="'formItem_' + 0">
        <div class="subtitle mr20">{{ $t('原因说明') }}</div>

        <div class="enclosure">
          <!--  @change="changeText" -->
          <rich-text-editor
            ref="editor"
            css-class="rich-box"
            :height="300"
            :max-length="500"
            v-model="applyInfo.applyReason"
            :disabled="!canEdit"
            :key="canEdit"
          >
          </rich-text-editor>
        </div>
      </div>
      <template v-if="punishObject === '1'">
        <div :ref="'formItem_' + 1">
          <div class="subtitle">{{ $t('选择范围') }}</div>
          <div class="enclosure fbox">
            <div class="categroy-box flex1">
              <div class="subtitle">{{ $t('一级品类') }}</div>
              <div class="line-banner fbox">
                <div class="line-check"></div>
                <div class="line-item flex1">{{ $t('品类编码') }}</div>
                <div class="line-item flex1">{{ $t('品类名称') }}</div>
              </div>

              <div class="list-box">
                <label
                  :for="'checkbox_' + item.id"
                  class="line-normal fbox"
                  v-for="item in categoryCheckList"
                  :key="item.id"
                >
                  <div class="line-check">
                    <input
                      type="checkbox"
                      :id="'checkbox_' + item.id"
                      :data-id="item.id"
                      :disabled="!canEdit"
                      v-model="item.isChecked"
                      @change="changeSelect"
                    />
                  </div>
                  <div class="line-item flex1">{{ item.categoryCode }}</div>
                  <div class="line-item flex1">{{ item.categoryName }}</div>
                </label>
              </div>
            </div>
            <div class="categroy-box flex1">
              <div class="subtitle">{{ $t('已选品类') }}</div>
              <div class="line-banner fbox">
                <div class="line-check"></div>
                <div class="line-item flex1">{{ $t('品类编码') }}</div>
                <div class="line-item flex1">{{ $t('品类名称') }}</div>
              </div>

              <div class="list-box">
                <label
                  for="checkbox"
                  class="line-normal fbox"
                  v-for="item in checkedList"
                  :key="item.id"
                >
                  <div class="line-check"></div>
                  <div class="line-item flex1">{{ item.categoryCode }}</div>
                  <div class="line-item flex1">{{ item.categoryName }}</div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div :ref="'formItem_' + 2">
        <div class="subtitle">{{ $t('附件') }}</div>
        <div class="enclosure">
          <mt-template-page
            ref="templateRef"
            :template-config="pageConfig"
            :use-tool-template="false"
            :padding-top="false"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTool="handleClickCellTool"
          >
          </mt-template-page>
        </div>
      </div>

      <!-- <div :ref="'formItem_' + 3">
        <div class="subtitle">{{ $t("审批流") }}</div>
        <div class="enclosure">
          <mt-bpmn-editor
            v-if="getCurrentFlowId"
            ref="bpmnEditorRef"
            :class="[{ 'bpmn-editor-disabled': true }]"
            :id="getCurrentFlowId"
            @select="selectFlowPoint"
          >
          </mt-bpmn-editor>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
// 表单tab后面的数据
import { enclosurea } from '../config/column'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
// polyfill for scrollIntoView
import smoothscroll from 'smoothscroll-polyfill'
// kick off the polyfill!
smoothscroll.polyfill()
export default {
  components: {
    RichTextEditor
  },
  data() {
    return {
      // toolbarSettings: {
      //   enable: true,
      //   enableFloating: true,
      //   type: 'Expand',
      //   items: [
      //     'Bold',
      //     'Italic',
      //     'Underline',
      //     '|',
      //     'Formats',
      //     'Alignments',
      //     'OrderedList',
      //     'UnorderedList',
      //     '|',
      //     'CreateLink',
      //     'Image',
      //     'backgroundColor',
      //     '|',
      //     'SourceCode',
      //     'Undo',
      //     'Redo'
      //   ],
      //   itemConfigs: {}
      // },
      backgroundColor: {
        columns: 5,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      },
      toolbarSettings: { enable: true, enableFloating: true, type: 'Expand' },
      pageConfig: [
        {
          gridId: '8912b75c-177f-43dc-aa16-baca836793f9',
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('新增'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'edit',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('编辑'),
                  visibleCondition: () => this.canEdit
                }
              ]
            ]
          },
          grid: {
            columnData: enclosurea,
            asyncConfig: {
              url: '/supplier/tenant/common/file/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: '',
                  field: 'bizId',
                  type: 'number',
                  operator: 'equal',
                  value: this.id
                }
              ],
              params: { bizId: this.id }
            }
          }
        }
      ],
      activeClass: 0,
      fullTaskIndexArr: [
        // this.$t("原因说明"),
        // this.$t("选择范围"),
        // this.$t("附件") /*this.$t("审批流")*/,
      ],
      checkedList: [],
      categoryCheckList: [],
      restrictionTypeCheckedList: [],
      currentSelectPoint: ''
    }
  },
  props: {
    id: {
      type: [Number, String],
      default: 0
    },
    punishObject: {
      type: [Number, String],
      default: '1'
    },
    canEdit: {
      type: Boolean,
      default: false
    },
    applyInfo: {
      type: Object,
      default: () => {}
    },
    relationDTOList: {
      type: Array,
      default: () => {
        return []
      }
    },
    categoryList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    id(nv) {
      if (nv) {
        this.initFileGrid(nv)
      }
    },
    punishObject: {
      handler(newValue) {
        if (newValue === '2') {
          this.fullTaskIndexArr = [this.$t('原因说明'), this.$t('附件') /*this.$t("审批流")*/]
        } else if (newValue === '1') {
          this.fullTaskIndexArr = [
            this.$t('原因说明'),
            this.$t('选择范围'),
            this.$t('附件') /*this.$t("审批流")*/
          ]
        }
      },
      immediate: true
    },
    categoryList(nv) {
      if (nv) {
        this.checkedList = []
        let categoryCheckList = this.categoryList.map((v) => {
          if (v.isChecked) {
            this.checkedList.push(v)
          }
          return {
            ...v,
            isChecked: v.isChecked ? true : false
          }
        })
        this.categoryCheckList = categoryCheckList
      }
    }
  },
  computed: {
    getCurrentFlowId() {
      return this.$route.query.id
    }
  },
  created() {},
  mounted() {
    console.log('mounter', this.id)
    this.initFileGrid(this.id)
  },
  methods: {
    selectFlowPoint(e) {
      this.currentSelectPoint = e
    },

    initFileGrid(id) {
      if (!id) {
        return
      }
      this.pageConfig = [
        {
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('新增'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'edit',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('编辑'),
                  visibleCondition: () => this.canEdit
                }
              ]
            ]
          },
          grid: {
            columnData: enclosurea,
            asyncConfig: {
              url: '/supplier/tenant/common/file/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: '',
                  field: 'bizId',
                  type: 'number',
                  operator: 'equal',
                  value: id
                }
              ],
              params: { bizId: id }
            }
          }
        }
      ]
    },
    createdTextEditor(value) {
      console.log('created', value)
    },
    handleClickToolBar(e) {
      const { toolbar, data, gridRef } = e
      if (toolbar.id === 'Add') {
        this.addNew(data)
        return
      }
      if (toolbar.id === 'Delete') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length > 0) {
          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id === 'edit') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length > 1) {
          this.$toast({ content: this.$t('只能编辑一条'), type: 'warning' })
        } else if (sltList && sltList.length === 1) {
          this.editRecord(sltList[0])
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    handleClickCellTitle(e) {
      const { data } = e
      if (!data.fileUrl) return
      let link = document.createElement('a') //创建a标签
      link.style.display = 'none' //使其隐藏
      link.href = data.fileUrl //赋予文件下载地址
      link.setAttribute('download', data.fileName) //设置下载属性 以及文件名
      document.body.appendChild(link) //a标签插至页面中
      link.click() //强制触发a标签事件
      document.body.removeChild(link)
    },
    handleClickCellTool() {},
    addNew() {
      const _this = this
      this.$dialog({
        modal: () => import('./operEnlosure.vue'),
        data: {
          title: this.$t('新增附件'),
          isEdit: false,
          fileInfo: {},
          applyInfo: this.applyInfo
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editRecord(item) {
      const _this = this
      this.$dialog({
        modal: () => import('./operEnlosure.vue'),
        data: {
          title: this.$t('编辑附件'),
          isEdit: true,
          fileInfo: item,
          applyInfo: this.applyInfo
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteRecord(data) {
      const _this = this
      let ids = data.map((item) => item.id).join(',')
      console.log(ids)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选申请单？'),
          confirm: () => _this.$API.SupplierPunishment['delStage']({ ids })
        },
        success: (val) => {
          console.log(this.$t('删除'), val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 表单滚动
    scrollInto(id) {
      this.activeClass = id
      let tmpId = id
      if (this.punishObject === '2' && id !== 0) {
        tmpId += 1
      }
      this.$refs['formItem_' + tmpId].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    },
    // 修改品类勾选
    changeSelect(e) {
      console.log(e)
      this.checkedList = []
      let checkedList = this.categoryCheckList.filter((item) => item.isChecked)
      this.checkedList = checkedList
    }
  },
  destroyed() {}
}
</script>
<style lang="scss">
.rich-box {
  height: 200px !important;
  .e-rte-content {
    height: 160px !important;
  }
}

.task-center {
  .e-content {
    min-height: 200px;
  }
}
</style>
<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}
.flex2 {
  flex: 2;
}

.mr20 {
  margin-top: 20px;
}

.task-center {
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  border-right: 2px solid rgba(232, 232, 232, 1);

  .task-sidebar {
    width: 120px;
    height: 100%;
    margin-left: 30px;
    padding-top: 14px;
  }
  .collapse-header {
    height: 30px;
    line-height: 30px;
    padding-left: 30px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin-bottom: 10px;

    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
    user-select: none;
  }
  .active {
    color: rgba(0, 70, 156, 1);
    border-left: 4px solid rgba(0, 70, 156, 1);
  }
  .collapse-content {
    background: rgba(255, 255, 255, 0.568);
    padding-top: 20px;
    padding-bottom: 20px;
    overflow: auto;
  }
  .subtitle {
    height: 14px;
    border-left: 2px solid;
    padding-left: 10px;
    margin-left: 20px;
    margin-top: 20px;
  }
  .enclosure {
    min-height: 200px;
    margin-left: 20px;
    margin-top: 20px;
    margin-right: 20px;
  }
  .categroy-box {
    margin-right: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;

    .subtitle {
      margin: 10px 20px;
    }

    .line-banner {
      width: 100%;
      height: 30px;
      line-height: 30px;
      background: #eee;
      font-size: 14px;
      color: #333;

      .line-check {
        width: 60px;
      }
    }

    .list-box {
      height: 200px;
      overflow: auto;
    }

    .line-normal {
      width: 100%;
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      color: #666;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;

      .line-check {
        width: 60px;
        padding-left: 20px;

        input {
          visibility: visible !important;
          width: 16px;
          height: 16px;
          border-color: #ddd;
        }
      }
    }
    .line-normal:last-child {
      border-bottom: none;
    }
  }
}
</style>
