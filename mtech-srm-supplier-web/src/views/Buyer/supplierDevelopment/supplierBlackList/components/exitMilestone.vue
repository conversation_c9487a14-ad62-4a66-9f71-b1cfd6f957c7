<template>
  <div>
    <div class="detail-content">
      <ScTable
        ref="xTable"
        max-height="100%"
        :columns="columns"
        :table-data="tableData"
        header-align="center"
        align="center"
        :row-config="{ isCurrent: true, keyField: 'id' }"
        :checkbox-config="{
          checkRowKeys: selectRowsId,
          reserve: true
        }"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="checkboxChangeEvent"
      >
      </ScTable>
      <!-- checkMethod: checCheckboxkMethod -- 禁用勾选框 -->
    </div>
    <mt-dialog
      ref="dialog"
      v-if="dialogVisible"
      :header="dialogTitle"
      css-class="add-dialog"
      :buttons="buttons"
      @close="hide"
      :open="onOpen"
    >
      <div class="detail-content">
        <ScTable
          ref="xTable"
          max-height="100%"
          :columns="dialogColumns"
          :table-data="tableDataDialog"
          header-align="center"
          align="center"
        >
        </ScTable>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { i18n } from '@/main'

export default {
  components: {
    ScTable
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    mile: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedRow: [], // 勾选的数据
      dialogVisible: false,
      dialogTitle: i18n.t('未清单据'),
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      tableDataDialog: [],
      tableData: [
        {
          id: 1,
          exitStage: i18n.t('停止新项目'),
          commissionMission: i18n.t('取消参与新项目资格（不能参与寻源、新品选型）'),
          undeclaredEvidence: null,
          department: i18n.t('采购部')
        },
        {
          id: 2,
          exitStage: i18n.t('停止PO'),
          commissionMission: i18n.t('参与中的项目降低配额/随项目淘汰'),
          undeclaredEvidence: null,
          department: i18n.t('采购部')
        },
        {
          id: 3,
          exitStage: i18n.t('停止PO'),
          commissionMission: i18n.t('二次开发完成认可'),
          undeclaredEvidence: null,
          department: i18n.t('CEG小组（PET）')
        },
        {
          id: 4,
          exitStage: i18n.t('停止PO'),
          commissionMission: i18n.t('停止新下PO'),
          undeclaredEvidence: null,
          department: i18n.t('PMC')
        },
        {
          id: 5,
          exitStage: i18n.t('停止PO'),
          commissionMission: i18n.t('清理PO（已下单给供应商但未交货的订单，安排消化）'),
          undeclaredEvidence: i18n.t('点击查看'),
          department: i18n.t('PMC')
        },
        {
          id: 6,
          exitStage: i18n.t('停止PO'),
          commissionMission: i18n.t('未消化PO/库存报呆'),
          undeclaredEvidence: null,
          department: i18n.t('PMC')
        },
        {
          id: 7,
          exitStage: i18n.t('停止PO'),
          commissionMission: i18n.t('清理库存（标准库存和寄售库存）'),
          undeclaredEvidence: null,
          department: i18n.t('PMC')
        },
        {
          id: 8,
          exitStage: i18n.t('财务账务清理'),
          commissionMission: i18n.t('嘉治具、模具的清理和调模'),
          undeclaredEvidence: null,
          department: i18n.t('采购部')
        },
        {
          id: 9,
          exitStage: i18n.t('财务账务清理'),
          commissionMission: i18n.t('图纸资料转移'),
          undeclaredEvidence: null,
          department: i18n.t('研发')
        },
        {
          id: 10,
          exitStage: i18n.t('财务账务清理'),
          commissionMission: i18n.t('专利风险的检查'),
          undeclaredEvidence: null,
          department: i18n.t('采购部/研发')
        },
        {
          id: 11,
          exitStage: i18n.t('财务账务清理'),
          commissionMission: i18n.t('质量索赔处理进度确认'),
          undeclaredEvidence: null,
          department: i18n.t('部品')
        },
        {
          id: 12,
          exitStage: i18n.t('财务账务清理'),
          commissionMission: i18n.t('交付索赔处理进度确认'),
          undeclaredEvidence: null,
          department: i18n.t('PMC')
        },
        {
          id: 13,
          exitStage: i18n.t('财务账务清理'),
          commissionMission: i18n.t('售后检查、产品线EOL确定'),
          undeclaredEvidence: null,
          department: i18n.t('采购/质量')
        },
        {
          id: 14,
          exitStage: i18n.t('财务账务清理'),
          commissionMission: i18n.t('供应商应付账款清理（索赔款清理、尾款、质保金退还等）'),
          undeclaredEvidence: null,
          department: i18n.t('财务')
        }
      ],
      columns: [
        {
          type: 'seq',
          title: i18n.t('序号'),
          width: 90
        },
        {
          field: 'exitStage',
          title: i18n.t('退出阶段')
        },
        {
          field: 'commissionMission',
          title: i18n.t('代办任务')
        },
        {
          field: 'undeclaredEvidence',
          title: i18n.t('未清单据'),
          // width: 800,
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.checkEvidence(row)
                  }}>
                  {row.undeclaredEvidence}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'department',
          title: i18n.t('部门')
        },
        {
          type: 'checkbox',
          title: i18n.t('完成情况'),
          width: 100
        }
      ],
      dialogColumns: [
        {
          type: 'seq',
          title: i18n.t('序号'),
          width: 90
        },
        {
          field: 'orderCode',
          title: i18n.t('采购订单号')
        },
        {
          field: 'showStatus',
          title: i18n.t('订单状态')
        },
        {
          field: 'orderTypeName',
          title: i18n.t('订单类型')
        },
        {
          field: 'orderDate',
          title: i18n.t('订单日期')
        },
        {
          field: 'buyerUserName',
          title: i18n.t('采购员')
        }
      ]
    }
  },
  computed: {
    selectRowsId() {
      if (!this.mile) return []
      const selectedArr = Object.keys(JSON.parse(this.mile)).map((item) =>
        Number(item.replace('STEP', ''))
      )
      return selectedArr
    }
  },
  methods: {
    // 点击弹出弹出框事件
    checkEvidence(row) {
      this.show()
      if (row.id === 5) {
        // 清理PO
        const id = this.id
        this.$API.SupplierPunishment.getPurchaseOrder({ id }).then((res) => {
          console.log('getPurchaseOrder', res)
          this.tableDataDialog = res.data
        })
      }
    },
    // // 禁用行选择框
    // checCheckboxkMethod({ row }) {
    //   return row.id !== 5
    // },
    // checkbox
    selectChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 全选
    checkboxChangeEvent(row) {
      this.selectedRow = row.records
    },
    onOpen(args) {
      args.preventFocus = true
    },
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.show()
      })
    },
    hide() {
      this.dialogVisible = false
      this.$nextTick(() => {
        this.$refs.dialog.ejsRef.hide()
      })
    }
  }
}
</script>

<style>
.detail-content {
  padding: 16px 0 16px 0;
}
</style>
