<template>
  <div class="freezeBox">
    <mt-template-page
      ref="templateRefClassify"
      :template-config="pageConfigClassify"
      :hidden-tabs="false"
      :padding-top="true"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBarClassify"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClick"
    ></mt-template-page>
  </div>
</template>
<script>
import { pageConfigClassify } from './config/column'
export default {
  data() {
    return {
      pageConfigClassify
    }
  },
  methods: {
    // this.$t("分级")
    handleClickToolBarClassify(e) {
      const { toolbar, data, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (sltList.some((item) => item.applyStatus === 20 || item.applyStatus === 40)) {
        this.$toast({
          content: this.$t('待审批、已完成状态不能编辑！'),
          type: 'warning'
        })
        return
      }
      if (toolbar.id === 'classify') {
        this.classifyTask(sltList)
        return
      }
      // 编辑
      if (toolbar.id === 'editClassify') {
        if (sltList && sltList.length === 1) {
          this.editClassify(sltList[0])
        } else if (sltList && sltList.length > 1) {
          this.$toast({ content: this.$t('只能选择一条进行编辑！'), type: 'warning' })
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
        return
      }

      // this.$t("删除")
      if (toolbar.id === 'DeleteClassify') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecordL(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }

      // this.$t("提交")
      if (toolbar.id === 'releaSse') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可提交'),
              type: 'warning'
            })
            return
          }
          this.submitTask(sltList, toolbar.origin)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      // this.$t("重新分级")
      if (toolbar.id === 'Reclassification') {
        if (sltList && sltList.length) {
          this.$dialog({
            modal: () => import('../../supplierResources/conponents/addFenDialog'),
            data: {
              title: this.$t('重新分级'),
              info: data
            },
            success: () => {},
            close: () => {}
          })
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    // 分级
    classifyTask(sltList) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/addClassifyDialog'),
        data: {
          info: sltList
        },
        success: (data) => {
          if (data.type === 'jump') {
            this.$router.push({
              path: 'classify-detail',
              query: {
                id: data.id
              }
            })
          }
          _this.$refs.templateRefClassify.refreshCurrentGridData()
        },
        close: () => {}
      })
    },
    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (data.applyStatus === 20 || data.applyStatus === 40) {
        this.$toast({
          content: this.$t('待审批、已完成状态不能编辑！'),
          type: 'warning'
        })
        return
      }
      // 共享
      if (['edit', 'delete'].includes(tool.id)) {
        tool.id === 'edit' && this.editTask(data)
        tool.id === 'delete' && this.deleteRecord([data])

        // 分级
      } else if (['editL', 'deleteL'].includes(tool.id)) {
        tool.id === 'editL' && this.editClassify(data)
        tool.id === 'deleteL' && this.deleteRecordL([data])
      }
    },
    handleClick(e) {
      //单元格Title点击
      console.log(e)
      const { field, data } = e
      if (field === 'applyCode' && data && data.id) {
        this.$router.push({
          path: 'classify-detail',
          query: {
            id: data.id
          }
        })
      }
    },

    submitTask(data, origin) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择要提交的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('是否确认提交所选申请？'),
          confirm: () => _this.$API.SupplierPunishment['deare']({ applyIdList: ids })
        },
        success: () => {
          _this.$toast({ content: this.$t('提交成功'), type: 'success' })
          _this.$refs[origin].refreshCurrentGridData()
        }
      })
    },
    editClassify(data) {
      const _this = this
      if (data.length > 1) {
        this.$toast({ content: this.$t('只能选择一条进行编辑！'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () => import('./components/addClassifyDialog.vue'),
        data: {
          title: this.$t('编辑分级'),
          isEdit: true,
          info: data
        },
        success: (data) => {
          if (data.type === 'jump') {
            this.$router.push({
              path: 'classify-detail',
              query: {
                id: data.id
              }
            })
          }
          _this.$refs.templateRefClassify.refreshCurrentGridData()
        }
      })
    },
    // this.$t("分级删除")
    deleteRecordL(data) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选申请单？'),
          confirm: () => _this.$API.SupplierPunishment['deleteba']({ applyIdList: ids })
        },
        success: (val) => {
          console.log(val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRefClassify.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.freezeBox {
  width: 100%;
  height: 100%;
}
</style>
