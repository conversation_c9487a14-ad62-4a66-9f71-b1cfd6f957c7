<template>
  <div class="task-center fbox">
    <div class="task-sidebar">
      <template v-for="(taskItem, index) in fullTaskIndexArr">
        <div
          :key="index"
          @click="scrollInto(index)"
          :class="{
            active: activeClass == index
          }"
        >
          <div class="collapse-header">
            {{ taskItem }}
          </div>
        </div>
      </template>
    </div>
    <div class="collapse-content flex1">
      <div :ref="'formItem_' + 0">
        <div class="subtitle">{{ $t('附件') }}</div>
        <div class="enclosureb">
          <mt-template-page
            ref="templateRef"
            :template-config="pageConfig"
            :use-tool-template="false"
            :padding-top="false"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTitle="handleClickCellTitle"
          >
          </mt-template-page>
        </div>
      </div>

      <div :ref="'formItem_' + 1">
        <div class="subtitle mr20">{{ $t('原因') }}</div>
        <div class="enclosureb">
          <mt-rich-text-editor
            ref="MtRichTextEditor"
            :max-length="500"
            :toolbar-settings="toolbarSettings"
            :background-color="backgroundColor"
            v-model="applyInfo.applyReason"
            @created="createdTextEditor"
          >
          </mt-rich-text-editor>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 表单tab后面的数据
import { enclosurea, columnPublish } from '../config/column'
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
// polyfill for scrollIntoView
import smoothscroll from 'smoothscroll-polyfill'
// kick off the polyfill!
smoothscroll.polyfill()
export default {
  components: {
    MtRichTextEditor
  },
  data() {
    return {
      // toolbarSettings: {
      //   enable: true,
      //   enableFloating: true,
      //   type: 'Expand',
      //   items: [
      //     'Bold',
      //     'Italic',
      //     'Underline',
      //     '|',
      //     'Formats',
      //     'Alignments',
      //     'OrderedList',
      //     'UnorderedList',
      //     '|',
      //     'CreateLink',
      //     'Image',
      //     'backgroundColor',
      //     '|',
      //     'SourceCode',
      //     'Undo',
      //     'Redo'
      //   ],
      //   itemConfigs: {}
      // },
      backgroundColor: {
        columns: 5,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      },
      toolbarSettings: { enable: true, enableFloating: true, type: 'Expand' },
      pageConfig: [],
      pageConfigPunish: [
        {
          gridId: 'e2dd9a54-20dc-4da2-b10b-99c36f9396fd',
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'addPublish',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('新增'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'deletePublish',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除'),
                  visibleCondition: () => this.canEdit
                }
              ]
            ]
          },
          grid: {
            columnData: columnPublish,
            dataSource: []
          }
        }
      ],
      activeClass: 0,
      fullTaskIndexArr: [this.$t('附件'), this.$t('原因说明')],
      checkedList: [],
      categoryCheckList: []
    }
  },
  props: {
    id: {
      type: [Number, String],
      default: 0
    },
    canEdit: {
      type: Boolean,
      default: false
    },
    applyInfo: {
      type: Object,
      default: () => {}
    },
    relationDTOList: {
      type: Array,
      default: () => {
        return []
      }
    },
    categoryList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    id(nv) {
      if (nv) {
        this.initFileGrid(nv)
      }
    },
    categoryList(nv) {
      if (nv) {
        let categoryCheckList = this.categoryList.map((v) => {
          if (v.isChecked) {
            this.checkedList.push(v)
          }
          return {
            ...v,
            isChecked: v.isChecked ? true : false
          }
        })
        this.categoryCheckList = categoryCheckList
      }
    }
  },
  mounted() {
    this.initFileGrid(this.id)
  },
  methods: {
    initFileGrid(id) {
      if (!id) {
        return
      }
      this.pageConfig = [
        {
          gridId: '5a354c3b-f60e-42d4-8b84-a32ae408ad0d',
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('新增'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'edit',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('编辑'),
                  visibleCondition: () => this.canEdit
                }
              ]
            ]
          },
          grid: {
            columnData: enclosurea,
            asyncConfig: {
              url: '/supplier/tenant/common/file/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: '',
                  field: 'bizId',
                  type: 'number',
                  operator: 'equal',
                  value: id
                }
              ],
              params: { bizId: id }
            }
          }
        }
      ]
    },
    createdTextEditor() {},
    handleClickToolBar(e) {
      const { toolbar, data, gridRef } = e
      if (toolbar.id === 'Add') {
        this.addNew(data)
        return
      }
      if (toolbar.id === 'Delete') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length > 0) {
          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id === 'edit') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length > 1) {
          this.$toast({ content: this.$t('只能编辑一条'), type: 'warning' })
        } else if (sltList && sltList.length === 1) {
          this.editRecord(sltList[0])
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    handleClickCellTitle(e) {
      const { data } = e
      if (!data.fileUrl) return
      let link = document.createElement('a') //创建a标签
      link.style.display = 'none' //使其隐藏
      let fileLink = data.fileUrl.replace(/(http|https)\:/, '')
      link.href = fileLink //赋予文件下载地址
      link.setAttribute('download', data.fileName) //设置下载属性 以及文件名
      document.body.appendChild(link) //a标签插至页面中
      link.click() //强制触发a标签事件
      document.body.removeChild(link)
    },
    handleClickToolBarPunish(e) {
      const { toolbar, data, gridRef } = e
      let _this = this
      if (toolbar.id === 'addPublish') {
        this.addPublish(data)
        return
      }
      if (toolbar.id === 'deletePublish') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length > 0) {
          this.$dialog({
            data: {
              title: this.$t('删除'),
              message: this.$t('是否确认删除所选惩罚供应商？')
            },
            success: () => {
              _this.deleteRelationRecord(sltList)
            }
          })
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },

    // 删除惩罚详情
    deleteRelationRecord(sltList) {
      let dataSource = this.pageConfigPunish[0].grid.dataSource
      let noRelationList = []
      // 找不相交的数据
      dataSource.forEach((v) => {
        if (sltList.filter((cv) => cv.id === v.id).length === 0) {
          noRelationList.push(v)
        }
      })

      this.$set(this.pageConfigPunish[0].grid, 'dataSource', noRelationList)
      this.$emit('changeRelation', noRelationList)
    },
    addNew() {
      const _this = this
      this.$dialog({
        modal: () => import('./operEnlosure.vue'),
        data: {
          title: this.$t('新增附件'),
          isEdit: false,
          fileInfo: {},
          applyInfo: this.applyInfo
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editRecord(item) {
      const _this = this
      this.$dialog({
        modal: () => import('./operEnlosure.vue'),
        data: {
          title: this.$t('编辑附件'),
          isEdit: true,
          fileInfo: item,
          applyInfo: this.applyInfo
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteRecord(data) {
      const _this = this
      let ids = data.map((item) => item.id).join(',')
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选文件？'),
          confirm: () => _this.$API.SupplierPunishment['delStage']({ ids })
        },
        success: (val) => {
          console.log(this.$t('删除'), val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 表单滚动
    scrollInto(id) {
      this.activeClass = id
      this.$refs['formItem_' + id].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    },

    // 修改品类勾选
    changeSelect() {
      this.checkedList = []
      let checkedList = this.categoryCheckList.filter((item) => item.isChecked)
      this.checkedList = checkedList
    }
  },
  destroyed() {}
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.flex2 {
  flex: 2;
}

.mr20 {
  margin-top: 20px;
}

.task-center {
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  border-right: 2px solid rgba(232, 232, 232, 1);
  padding-bottom: 20px;
  align-items: stretch;

  .task-sidebar {
    width: 120px;
    height: 100%;
    margin-left: 20px;
    padding-top: 14px;
  }
  .collapse-header {
    height: 30px;
    line-height: 30px;
    margin-bottom: 10px;
    padding-left: 30px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);

    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
    user-select: none;
  }
  .active {
    color: rgba(0, 70, 156, 1);
    border-left: 4px solid rgba(0, 70, 156, 1);
  }
  .none {
    display: none;
  }
  .collapse-content {
    background: rgba(255, 255, 255, 0.568);
    padding-top: 20px;
    padding-bottom: 20px;
    overflow: auto;
  }

  .subtitle {
    height: 14px;
    border-left: 2px solid;
    padding-left: 10px;
    margin-left: 20px;
  }
  .enclosure,
  .enclosureb {
    height: 100%;
    margin-left: 20px;
    margin-top: 20px;
    margin-right: 20px;
  }
  .enclosureb {
    height: 266px;
  }

  .categroy-box {
    margin-right: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
    padding-bottom: 10px;

    .subtitle {
      margin: 10px 20px;
    }

    .line-banner {
      width: 100%;
      height: 30px;
      line-height: 30px;
      background: #eee;
      font-size: 14px;
      color: #333;

      .line-check {
        width: 60px;
      }
    }

    .list-box {
      height: 200px;
      overflow: auto;
    }

    .line-normal {
      width: 100%;
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      color: #666;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;

      .line-check {
        width: 60px;
        padding-left: 20px;

        input {
          visibility: visible !important;
          width: 16px;
          height: 16px;
          border-color: #ddd;
        }
      }
    }
    .line-normal:last-child {
      border-bottom: none;
    }
  }
}
</style>
