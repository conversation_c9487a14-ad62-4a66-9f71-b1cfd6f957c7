<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-form-flex bule-bg"
    :header="$t('新增分级申请单')"
    :width="900"
    min-height="600"
    :buttons="buttons"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="applyName" :label="$t('申请单名称')" class="mt-form-item-names">
              <mt-input
                v-model="formInfo.applyName"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="orgId" :label="$t('申请人公司')">
              <!-- <mt-select
                v-model="formInfo.orgId"
                float-label-type="Never"
                :data-source="orgList"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="orgChange"
              ></mt-select> -->

              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="formInfo.orgCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                @change="orgChange"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseId" :label="$t('供应商')">
              <mt-select
                v-model="formInfo.supplierEnterpriseId"
                float-label-type="Never"
                :data-source="supplierList"
                :fields="{
                  text: 'supplierEnterpriseName',
                  value: 'supplierEnterpriseId'
                }"
                :allow-filtering="true"
                :placeholder="$t('请选择供应商')"
                @change="supplierEnterpriseChange"
              ></mt-select>
            </mt-form-item>
            <!-- <mt-form-item prop="hierarchical" :label="$t('分级组织')">
              <mt-input
                v-model="formInfo.orgName"
                :disabled="true"
                type="text"
                :placeholder="$t('请输入分级组织')"
              ></mt-input>
            </mt-form-item> -->
          </mt-col>
          <mt-col :span="12">
            <!-- 类型 -->
            <mt-form-item :label="$t('类型')" prop="recommend">
              <mt-select
                v-model="formInfo.recommend"
                :width="400"
                :show-clear-button="true"
                :data-source="typeArray"
                @change="handleChangeType"
                :placeholder="$t('请选择类型')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="formInfo.recommend === 1 ? 12 : 24">
            <!-- 级别 -->
            <mt-form-item prop="labelType" :label="$t('级别')">
              <mt-select
                class="mt-select-box"
                v-model="formInfo.labelType"
                :data-source="classesArray"
                :show-clear-button="true"
                :fields="{
                  text: 'labelName',
                  value: 'labelType'
                }"
                :placeholder="$t('请选择分级级别')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <template v-if="formInfo.recommend === 1">
            <mt-col :span="12">
              <!-- 推荐 -->
              <mt-form-item prop="superiororgId" :label="$t('推荐至')">
                <mt-DropDownTree
                  :fields="companyFields"
                  :show-check-box="false"
                  id="checkboxTreeSelect"
                  :placeholder="$t('组织树状图，可选至任意层级、单选')"
                  :allow-multi-selection="false"
                  :auto-check="false"
                  @select="companyChange"
                ></mt-DropDownTree>
              </mt-form-item>
            </mt-col>
          </template>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyerId" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.applyerId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyerOrgId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.applyerOrgId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyerDeptId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyerDeptId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="20">
          <!-- 备注 -->
          <mt-form-item prop="remark" :label="$t('备注')" class="mt-form-item-remarks">
            <mt-input
              css-class="e-outline"
              v-model="formInfo.remark"
              type="text"
              :multiline="true"
              :rows="3"
              maxlength="200"
              :placeholder="$t('请输入备注')"
            ></mt-input>
          </mt-form-item>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, areaColumnData } from '../config/column'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  data() {
    return {
      applyUserIdData: [],
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        applyName: this.$t('分级申请单'),
        orgId: '',
        orgCode: '',
        orgName: '',
        supplierEnterpriseId: '',
        orgDimension: '',

        recommend: 0,
        labelType: '',
        remark: '',

        applyerId: '',
        applyerOrgId: '',
        applyerDeptId: '',

        superiororgCode: '',
        superiororgId: '',
        superiorOrgName: ''
      },
      rules: {
        applyName: [{ required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }],
        orgId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        supplierEnterpriseId: [
          { required: true, message: this.$t('请选择供应商'), trigger: 'blur' }
        ],
        labelType: [{ required: true, message: this.$t('请选择级别'), trigger: 'blur' }],
        superiororgId: [{ required: true, message: this.$t('请选择推荐至组织'), trigger: 'blur' }],
        applyerId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        applyerOrgId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyerDeptId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmSubmit,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      typeArray: [
        {
          text: this.$t('本组织分级'),
          value: 0
        }
      ],
      componentConfig: [
        {
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: areaColumnData,
            dataSource: []
          }
        }
      ],
      classesArray: [],
      classesArrayBat: [],
      orgArray: [],
      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      currentCompany: {},
      // orgList: [], // 公司列表
      supplierList: [], // 公司列表
      userDetail: {},
      info: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    isEdit() {
      return this.modalData.isEdit
    }
  },
  async created() {
    this.queryDefinList()
    this.getCurrentTenantUsers()
    // const userDetail = await this.getUserDetail();
    // if (userDetail && userDetail.id) {
    //   this.getChildrenCompanyOrganization(userDetail.id);
    // }
  },
  mounted() {
    if (this.isEdit) {
      let id = this.modalData.info.id
      this.getclassifDetail(id)
    }
    this.show()
  },
  methods: {
    // 处理公司接口响应数据（处理返回的接口数据，会把接口请求的数据返回，然后数据处理完以后请用return返回）
    handleCompanyData(resData = []) {
      resData = resData.filter((item) => {
        return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      })
      return resData
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    getclassifDetail(id) {
      this.$API.SupplierPunishment.getclassificationDetail({ id }).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.info = res.data
          let { gradeDTO, infoDTO, relationDTOList } = res.data
          this.formInfo = {
            ...gradeDTO,
            ...infoDTO,
            ...relationDTOList[0],
            labelType: gradeDTO.gradeType
          }
        } else {
          this.$toast({
            content: this.$t('获取详情失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    // getUserDetail() {
    //   return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
    //     let { data } = res;
    //     let { companyOrg } = data;
    //     this.userDetail = data;
    //     return companyOrg;
    //   });
    // },
    // getChildrenCompanyOrganization(userId) {
    //   // this.$API.supplierInvitation["getChildrenCompanyOrganization"]({
    //   //   organizationId: userId,
    //   // }).then((result) => {
    //   this.$API.supplierInvitation["getChildrenCompanyOrganization2"]({
    //     organizationLevelCodes: ["ORG02", "ORG01"],
    //     orgType: "ORG001PRO",
    //     includeItself: true,
    //   }).then((result) => {
    //     if (result.code === 200 && !utils.isEmpty(result.data)) {
    //       this.orgList = result.data.filter((item) => {
    //         return item.orgLevelTypeCode === "ORG02" ||
    //           item.orgLevelTypeCode === "ORG01";
    //       });
    //     } else {
    //       this.orgList = [];
    //     }
    //   });
    // },

    orgChange(event) {
      // 有级联
      const { itemData = {} } = event
      this.formInfo.orgId = itemData?.id
      this.formInfo.orgCode = itemData?.orgCode
      this.formInfo.orgName = itemData?.orgName
      if (itemData?.id) {
        this.getOrgPartnerRelations(itemData?.id)
      } else {
        this.supplierList = []
        this.formInfo.supplierEnterpriseId = ''
      }
    },

    // STATUS_DRAFT(1, "注册"),
    // STATUS_POTENTIAL(2, "潜在"),
    // STATUS_NORMAL(10, "合格"),
    // STATUS_FROZEN(20, "冻结"),
    // STATUS_BLACK(30, "黑名单"),
    // Integer 类型 这些状态可以惩罚
    getOrgPartnerRelations(orgId) {
      this.$API.supplierEffective
        .getOrgPartnerRelationsByStatus({
          orgId,
          status: [1, 2, 10, 20, 30]
        })
        .then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.supplierList = res.data
          } else {
            this.supplierList = []
          }
        })
    },

    supplierEnterpriseChange(event) {
      const { itemData = {} } = event
      this.formInfo.supplierEnterpriseCode = itemData?.supplierEnterpriseCode
      this.formInfo.supplierEnterpriseName = itemData?.supplierEnterpriseName
      this.formInfo.supplierEnterpriseId = itemData?.supplierEnterpriseId
      this.formInfo.partnerRelationCode = itemData?.partnerRelationCode
      this.formInfo.partnerRelationId = itemData?.id
      this.formInfo.partnerArchiveId = itemData?.partnerArchiveId
      this.formInfo.supplierType = itemData?.supplierType
      this.formInfo.supplierName = itemData?.supplierName
      this.formInfo.orgDimension = itemData?.orgDimension
      this.formInfo.categoryId = ''
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
        })
      })
    },

    // 获取级别
    queryDefinList() {
      this.$API.supplierResources
        .getclassify({ labelDefineType: 1 })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            this.classesArray = data.map((v) => {
              return {
                ...v,
                text: v.labelName,
                value: v.labelType
              }
            })
            this.classesArrayBat = JSON.parse(JSON.stringify(this.classesArray))
          } else {
            this.$toast({
              content: this.$t('获取本组织分级信息失败！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取本组织分级信息失败！'),
            type: 'warning'
          })
        })
    },
    // 上级
    getParentsTreeById() {
      let formInfo = this.formInfo
      this.$API.supplierlifecycle
        .getParentsTreeById({
          orgId: formInfo.orgId,
          tenantId: this.userDetail.tenantId,
          includeItself: false
        })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            this.companyFields = Object.assign({}, this.companyFields, {
              dataSource: data
            })
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取上级组织信息失败！'),
            type: 'warning'
          })
        })
    },

    companyChange(node) {
      let value = node.id
      if (!value || this.formInfo.superiororgId === value) {
        return
      }

      // 获取 currentCompany
      this.filterNode(this.companyFields.dataSource, node.id)

      this.formInfo.superiororgCode = this.currentCompany.orgCode
      this.formInfo.superiororgId = this.currentCompany.id
      this.formInfo.superiorOrgName = this.currentCompany.name
    },
    /**
     * 根据公司 去获取对应的工厂  根据工厂 =》 对应的品类
     */
    filterNode(arr, id) {
      if (arr.filter((v) => v.id === id).length > 0) {
        let arrTmp = arr.filter((v) => v.id === id)
        this.currentCompany = arrTmp[0]
      } else {
        arr.forEach((vc) => {
          if (!!vc.children && vc.children.length > 0) {
            this.filterNode(vc.children, id)
          }
        })
      }
    },

    // 类型切换
    handleChangeType(data) {
      let { itemData } = data
      if (itemData.value === 1) {
        let classessArray = []
        this.classesArrayBat.forEach((item) => {
          if (item.recommend === 1) {
            classessArray.push(item)
          }
        })
        this.classesArray = classessArray
        this.companyFields.dataSource.length === 0 && this.getParentsTreeById()
      } else {
        this.classesArray = this.classesArrayBat
      }
    },

    handleUserChange(data) {
      let { itemData } = data
      this.formInfo.applyerId = itemData.id
      this.formInfo.applyerName = itemData.userName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      this.stageInfo = itemData
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyerId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyerId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            // 获取默认选择的公司
            this.formInfo.applyerOrgId = res.data[0].id
            this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            this.$hloading()
            // 校验当前分级组织为最高层级，取消推荐分级选项
            if (res.data[0].treeLevel !== 0) {
              this.typeArray.push({
                text: this.$t('推荐分级'),
                value: 1
              })
            }
          } else {
            this.$hloading()
            this.formInfo.applyerOrgId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.applyerOrgId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.applyerOrgId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyerId,
          companyOrganizationId: this.formInfo.applyerOrgId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyerDeptId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyerDeptId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.formInfo.applyerDeptId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    handleCompanyChange(data) {
      let { itemData } = data
      console.log(data, itemData)
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    },

    // 提交
    confirm(fn) {
      let formInfo = this.formInfo
      let validRs = false
      let levelObj = {}
      for (let i = 0; i < this.classesArray.length; i++) {
        let item = this.classesArray[i]
        if (item.labelType === this.formInfo.labelType) {
          levelObj.gradeId = item.id
          levelObj.gradeName = item.labelName
          levelObj.gradeType = item.labelType
          break
        }
      }
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }
      let gradeDTO = {
        ...levelObj,
        businessType: '',
        orgId: formInfo.orgId,
        orgName: formInfo.orgName,
        orgCode: formInfo.orgCode,
        orgDimension: formInfo.orgDimension,
        applyType: 'grade',
        remark: formInfo.remark,
        recommend: formInfo.recommend,
        superiorOrgCode: formInfo.superiororgCode,
        superiorOrgId: formInfo.superiororgId,
        superiorOrgName: formInfo.superiorOrgName
      }
      this.isEdit && (gradeDTO = { ...this.info.gradeDTO, ...gradeDTO })

      let infoDTO = {
        applyerDeptId: formInfo.applyerDeptId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyerId,
        applyerName: formInfo.applyerName,
        applyName: formInfo.applyName,
        applyerOrgId: formInfo.applyerOrgId,
        applyerOrgName: formInfo.applyerOrgName
      }
      this.isEdit && (infoDTO = { ...this.info.infoDTO, ...infoDTO })

      let relationDTOList = [
        {
          supplierEnterpriseCode: formInfo.supplierEnterpriseCode,
          supplierEnterpriseName: formInfo.supplierEnterpriseName,
          supplierEnterpriseId: formInfo.supplierEnterpriseId,
          partnerRelationCode: formInfo.partnerRelationCode,
          partnerRelationId: formInfo.partnerRelationId,
          partnerArchiveId: formInfo.partnerArchiveId,
          supplierType: formInfo.supplierType,
          supplierName: formInfo.supplierName
        }
      ]
      this.isEdit &&
        (relationDTOList = [
          {
            ...this.info.relationDTOList[0],
            ...relationDTOList[0]
          }
        ])

      let query = {
        gradeDTO,
        infoDTO,
        relationDTOList
      }
      this.$loading()
      let urlRequest = this.isEdit
        ? this.$API.SupplierPunishment.updatagrade
        : this.$API.supplierResources.addFormTemplatea
      urlRequest(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })

            if (!!fn && typeof fn === 'function') {
              fn(res.data)
            } else {
              this.$emit('confirm-function', 'reload')
            }
          } else {
            this.$toast({
              content: this.$t('操作失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('操作失败，请重试'),
            type: 'warning'
          })
        })
    },

    confirmSubmit() {
      this.confirm((data) => {
        this.$emit('confirm-function', {
          type: 'jump',
          id: data.gradeDTO.applyId
        })
      })
    },

    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.upgrade-dialog {
  padding: 20px;

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
  .supplier-table {
    height: 266px;
  }
}
</style>
