<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="orgName" class="form-item" :label="$t('分级组织')">
              <mt-input
                v-model="formInfo.orgName"
                :disabled="true"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入分级组织名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierStage" class="form-item" :label="$t('级别')">
              <mt-select
                v-model="formInfo.supplierStage"
                :width="400"
                :show-clear-button="true"
                :data-source="stageList"
                :fields="{ text: 'labelName', value: 'labelType' }"
                @change="handleChangeParent"
                :placeholder="$t('请选择级别')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyUserId" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.applyUserId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="companyId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.companyId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyDepId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyDepId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                :multiline="true"
                maxlength="200"
                :rows="2"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, areaColumnData } from '../config/column.js'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      infoDTO: {},
      relationDTOList: {},
      labelDefineType: 1,
      orgLevelTypeCode: 'ORG02',
      applyUserIdData: [],
      stageList: [],
      info: [],
      stageInfo: {},
      parentInfo: {},
      stagTypeL: [],
      TenantUserList: [], // 当前租户下的用户
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        supplierEnterpriseName: '',
        orgName: '',
        supplierStage: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        applyerOrgName: '',
        applyerDeptName: '',
        applyerName: ''
      },
      rules: {
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        supplierStage: [{ required: true, message: this.$t('请选择级别'), trigger: 'blur' }],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmSubmit,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      componentConfig: [
        {
          gridId: 'd473b9b3-5963-4ad8-9931-b83e0748d920',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: areaColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    let id = this.modalData.info.id
    this.getclassificationDetail(id)
    this.show()
    this.getCurrentTenantUsers()
    this.getclassify()
    this.getDictItem()

    // 出发change
    this.$set(this.componentConfig[0].grid, 'dataSource', this.datainfo)
  },
  computed: {
    datainfo() {
      return this.modalData.info.relationDTOList
    },
    header() {
      return this.modalData.title
    }
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    getDictItem() {
      let dictCode = 'shareType'
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.stagTypeL = res.data.map((item) => {
              return {
                ...item,
                text: item.name,
                value: item.itemCode
              }
            })
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
    },
    save() {
      this.confirm(() => {})
    },
    // 提交
    confirm(fn) {
      let formInfo = this.formInfo
      let validRs = false
      let levelObj = {}
      this.stageList.forEach((e) => {
        if (e.labelType === this.formInfo.supplierStage) {
          levelObj.gradeId = e.id
          levelObj.gradeName = e.labelName
          levelObj.gradeType = e.labelType
        }
      })
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }
      let gradeDTO = {
        ...levelObj,
        ...this.info,
        ...formInfo,
        orgCode: this.modalData.info.orgCode,
        remark: formInfo.remark
      }
      let infoDTO = {
        ...this.infoDTO,
        applyerDeptId: formInfo.applyDepId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyUserId,
        applyerName: formInfo.applyUser,
        applyName: formInfo.supplierEnterpriseName,
        applyerOrgId: formInfo.companyId,
        applyerOrgName: formInfo.applyerOrgName
      }
      let relationDTOList = this.componentConfig[0].grid.dataSource

      if (relationDTOList.length === 0) {
        this.$toast({
          content: this.$t('编辑失败，请至少有一个勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        gradeDTO,
        infoDTO,
        relationDTOList
      }
      this.$loading()

      this.$API.SupplierPunishment.updatagrade(query)
        .then((res) => {
          this.$hloading()
          if (res.code === 200) {
            this.$toast({ content: this.$t('编辑成功'), type: 'success' })
            fn(res.data)
            this.$emit('confirm-function', 'reload')
          } else {
            this.$toast({
              content: res.msg || this.$t('编辑失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('编辑失败，请重试'),
            type: 'warning'
          })
        })
    },

    confirmSubmit() {
      this.confirm((data) => {
        this.$emit('confirm-function', { type: 'jump', id: data.infoDTO.id })

        // this.$API.SupplierPunishment.applySubmit({
        //   applyIdList: [ this.modalData.info.id ],
        // }).then((res) => {
        //   this.$hloading();
        //   if (res.code === 200 && !utils.isEmpty(res.data)) {
        //     this.$toast({
        //       content: this.$t("提交分级成功")! ",
        //       type: "success",
        //     });
        //     this.$emit("confirm-function", "reload");
        //   } else {
        //     this.$toast({
        //       content: this.$t("提交分级失败，请重试")!",
        //       type: "warning",
        //     });
        //   }
        // });
      })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (sltList.filter((citem) => citem.id === item.id).length === 0) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },
    handleUserChange(data) {
      let { itemData } = data
      this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.userName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      this.stageInfo = itemData
      console.log(this.stageInfo)
    },
    // 副组织
    handleChangeParent(data) {
      let { itemData } = data
      this.parentInfo = itemData
      console.log(this.parentInfo)
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
          console.log(this.applyUserIdData)
        })
      })
    },

    getclassify() {
      let labelDefineType = this.labelDefineType
      this.$API.supplierResources.getclassify({ labelDefineType }).then((res) => {
        this.stagei = res.data
        console.log(res)
        this.stagei.forEach((item) => {
          let uip = {
            labelName: item.labelName,
            id: item.id,
            labelType: item.labelType
          }
          this.stageList.push(uip)
        })
      })
    },
    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            // this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },
    // 获取编辑的详情
    getclassificationDetail(id) {
      this.$API.SupplierPunishment.getclassificationDetail({ id }).then((res) => {
        this.infoDTO = res.data.infoDTO
        this.relationDTOList = res.data.relationDTOList
        this.info = res.data.gradeDTO
        this.info.applyUserId = res.data.infoDTO.applyerId
        this.info.orgId = res.data.infoDTO.applyerOrgId
        this.info.applyDepId = res.data.infoDTO.applyerDeptId
        // console.log(this.info)
        this.formInfo.supplierEnterpriseName = this.modalData.info.applyName
        this.formInfo.remark = this.modalData.info.remark
        this.formInfo.supplierStage = this.modalData.info.gradeType
        this.formInfo.orgName = this.info.orgName || '--'
        this.formInfo.applyUserId = this.info.applyUserId
        this.formInfo.companyId = this.info.orgId
        this.formInfo.applyDepId = this.info.applyDepId
      })
    },
    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    handleCompanyChange(data) {
      let { itemData } = data
      console.log(data, itemData)
      // this.formInfo.companyId = itemData.id
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
.upgrade-dialog {
  padding: 20px; // --*

  .e-content {
    min-height: 200px;
  }

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
</style>
