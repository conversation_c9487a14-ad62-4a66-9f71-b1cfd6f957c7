import { i18n } from '@/main.js'
import { statsea, blackState } from '@/utils/setting'
import utils from '@/utils/utils'
import Vue from 'vue'
//供应商分级
export const applicationforme = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('申请单编号'),
    headerTextAlign: 'center',
    width: '180',
    cellTools: [
      {
        id: 'editL',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['applyStatus'] !== 20 && data['applyStatus'] !== 40
        }
      },
      { id: 'deleteL', icon: 'icon_solid_Delete1', title: i18n.t('删除') }
    ]
  },
  {
    field: 'applyName',
    width: '120',
    headerText: i18n.t('申请单名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'relationDTOList',
    width: '300',
    headerText: i18n.t('供应商'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('relationDTOList', {
          template: `
              <div class="approve-config-box fbox" v-if="!!relationDTOList && !!relationDTOList[0]">
              <div v-if="!!relationDTOList && !!relationDTOList[0] && relationDTOList[0].supplierEnterpriseName" class="flex1">{{relationDTOList[0].supplierEnterpriseName}}</div>
              <mt-tooltip :content="content" position="TopCenter">
                <i style="font-size:16px; color:#6386C1" class="mt-icons mt-icon-icon_outline_prompt" ></i>
              </mt-tooltip>
              </div>`,
          data: function () {
            return { data: {}, content: '' }
          },
          computed: {
            relationDTOList() {
              return this.data.relationDTOList
            }
          },
          mounted() {
            this.getSupplier()
          },
          methods: {
            getSupplier() {
              this.data.relationDTOList.forEach((item) => {
                this.content = this.content + `<div>${item.supplierEnterpriseName} </div>`
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'gradeType',
    width: '120',
    headerText: i18n.t('申请级别'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgDimension',
    width: '150',
    headerText: i18n.t('组织维度'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgName',
    width: '150',
    headerText: i18n.t('组织名称'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.applyStatus] }}</span>
          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: statsea
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('申请人'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyerDeptName',
    width: '180',
    headerText: i18n.t('申请人部门'),
    headerTextAlign: 'center'
  },
  {
    field: 'createDate',
    width: '250',
    headerText: i18n.t('申请日期'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = utils.formateTime(Number(this.data.createDate), 'yyyy-MM-dd hh:mm:ss')
              return timeStr
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]
export const punishedformeg = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    headerTextAlign: 'center',
    width: '120'
  },
  {
    field: 'supplierEnterpriseName',
    width: '180',
    headerText: i18n.t('供应商名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'supplierType',
    width: '180',
    headerText: i18n.t('级别'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.status == 10 || data.status == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.status] }}</span>
          <span v-if="data.status == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.status] }}</span>
          <span v-if="data.status == 40 || data.status == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.status] }}</span>
          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: blackState
            }
          }
        })
      }
    }
  },
  {
    field: 'orgDimension',
    width: '180',
    headerText: i18n.t('组织维度'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgCode',
    width: '120',
    headerText: i18n.t('组织编码'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgName',
    width: '180',
    headerText: i18n.t('组织名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]
export const pageConfigClassify = [
  {
    gridId: 'a27f9e4e-936e-477b-966c-3ad179b4f1c6',
    title: i18n.t('申请单'),
    toolbar: {
      tools: [
        [
          { id: 'classify', icon: 'icon_table_setLevel', title: i18n.t('新增') },
          { id: 'editClassify', icon: 'icon_Editor', title: i18n.t('编辑') },
          {
            id: 'DeleteClassify',
            icon: 'icon_solid_Delete1',
            title: i18n.t('删除')
          },
          {
            id: 'releaSse',
            icon: 'icon_solid_submit',
            title: i18n.t('提交'),
            origin: 'templateRefClassify'
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: applicationforme,
      asyncConfig: {
        url: '/supplier/tenant/buyer/apply/grade/list'
      }
    }
  },
  {
    gridId: 'd085dc09-a69f-4e71-974f-0815573a882f',
    // useToolTemplate: false,
    title: i18n.t('已分级'),
    toolbar: {
      tools: [
        [
          // {
          //   id: "Reclassification",
          //   icon: "icon_solid_Newinvitation",
          //   title: i18n.t("重新分级"),
          // },
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: punishedformeg,
      asyncConfig: {
        url: '/supplier/tenant/buyer/process/manager/queryPageList',
        // params: {
        //   supplierGradeFlag: "1",
        // },
        rules: [
          //配置rules，数组格式，请求入参时，追加到rules中
          {
            field: 'supplierGradeFlag',
            label: i18n.t('供应商分级标记'),
            operator: 'equal',
            type: 'Integer',
            value: '1'
          }
        ]
      }
    }
  }
]

// 附件
export const enclosurea = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    headerText: i18n.t('附件名称'),
    headerTextAlign: 'center',
    width: '180',
    cellTools: []
  },
  {
    field: 'fileSize',
    width: '120',
    headerText: i18n.t('附件大小'),
    headerTextAlign: 'center'
  },
  {
    field: 'updateUserName',
    width: '120',
    headerText: i18n.t('上传人'),
    headerTextAlign: 'center'
  },
  {
    field: 'modifyDate',
    width: '120',
    headerText: i18n.t('上传时间'),
    headerTextAlign: 'center',
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  },
  {
    field: 'remark',
    width: '180',
    headerTextAlign: 'center',
    headerText: i18n.t('备注')
  }
]

export const supplyAreaToolbar = [
  // {
  //   id: "addNew",
  //   icon: "icon_solid_Newinvitation",
  //   title: i18n.t("新增"),
  // },
  {
    id: 'deleteList',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]

// 手动准入升降级

export const areaColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '100',
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    headerTextAlign: 'center'
  },
  {
    width: '100',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称'),
    headerTextAlign: 'center'
  }
]

export const columnPublish = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    headerTextAlign: 'center',
    width: '120'
  },
  {
    field: 'supplierEnterpriseName',
    width: '180',
    headerText: i18n.t('供应商'),
    headerTextAlign: 'center'
  },
  {
    field: 'categoryCode',
    width: '120',
    headerText: i18n.t('品类编码'),
    headerTextAlign: 'center'
  },
  {
    field: 'categoryName',
    width: '180',
    headerText: i18n.t('品类名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'categoryName',
    width: '180',
    headerText: i18n.t('处罚时限'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box" v-html="timeStr">
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = `${this.data.penaltyPeriodStart}<br/>${this.data.penaltyPeriodEnd}`
              return timeStr
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '180',
    headerText: i18n.t('申请人'),
    headerTextAlign: 'center'
  }
]
