<template>
  <div class="supplierDetall-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="titles-box">
        <div class="mian-line fbox">
          <div class="mian-title">
            <template v-if="!!info.infoDTO && !!infoDTO.applyName">
              {{ infoDTO.applyName || '--' }}
            </template>
          </div>
          <div class="btns-box fbox">
            <div class="invite-btn" @click="onBack">
              <mt-button css-class="e-info">{{ $t('返回') }}</mt-button>
            </div>
            <template>
              <div
                class="invite-btn"
                @click="saveText"
                v-if="canEdit && infoDTO.applyType != 'relieve'"
              >
                <mt-button css-class="e-info">{{ $t('保存') }}</mt-button>
              </div>
              <div class="invite-btn" @click="saveAndSubmit" v-if="canEdit">
                <mt-button css-class="e-info">{{ $t('保存并提交') }}</mt-button>
              </div>
            </template>
          </div>
        </div>
        <div class="sub-title fbox">
          <div class="normal-title" v-if="!!infoDTO.applyCode">
            {{ $t('申请单编码') }}：{{ infoDTO.applyCode }}
          </div>
          <div class="normal-title" v-if="!!infoDTO.createUserName">
            {{ $t('创建人') }}：{{ infoDTO.createUserName }}
          </div>
          <div class="normal-title" v-if="!!infoDTO.createTime">
            {{ $t('创建时间') }}： {{ infoDTO.createTime }}
          </div>
          <div class="normal-title">{{ $t('申请人') }}：{{ infoDTO.applyerName }}</div>
          <div class="normal-title">{{ $t('申请人部门') }}：{{ infoDTO.applyerDeptName }}</div>
        </div>
        <div class="sub-title fbox mr-20">
          <div class="normal-title-dan" v-if="!!punishDTO.businessType">
            <span>{{ $t('类型') }}:</span>
            {{ punimentType[punishDTO.businessType] }}
          </div>
          <div class="normal-title-dan"></div>
          <div class="normal-title-dan" v-if="!!punishDTO.penaltyPeriodStart">
            <span>{{ $t('处罚时间') }}：</span>{{ punishDTO.penaltyPeriodStart | filterTime }}～{{
              punishDTO.penaltyPeriodEnd | filterTime
            }}
          </div>
        </div>
        <div class="scroll-box fbox">
          <div
            class="normal-title-box"
            v-for="item in relationDTOList"
            :key="item.supplierEnterpriseCode"
          >
            <div class="gong-title">
              {{ item.supplierEnterpriseName || '--' }}
            </div>
            <div class="gong-id">{{ item.supplierEnterpriseCode || '--' }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 头部结束 -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>

    <detail-edit
      v-show="selectIndex === 0"
      :id="punishDTO.applyId"
      :apply-info="infoDTO"
      :can-edit="canEdit"
      :category-list="categoryList"
      :category-d-t-o-list="info.categoryDTOList"
      :restriction-type-list="restrictionTypeList"
      :punish-object="punishObject"
      :punish-range="punishRange"
      :purchase-org-list-all="purchaseOrgListAll"
      :checked-list-org="purchaseOrgListAll.filter((i) => i.isChecked)"
      @changeRelation="changeRelation"
      ref="editRef"
    ></detail-edit>
    <operator-history
      v-show="selectIndex === 1"
      :id="punishDTO.applyId"
      :apply-info="infoDTO"
    ></operator-history>
  </div>
</template>

<script>
import operatorHistory from './components/operatorHistory.vue'
import detailEdit from './components/detailEdit.vue'
import utils from '@/utils/utils'
export default {
  components: {
    detailEdit,
    operatorHistory
  },
  data() {
    return {
      purchaseOrgListAll: [],
      purchaseOrgDTOList: [],
      punishRange: '',
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('单据配置')
        },
        {
          title: this.$t('操作记录')
        }
      ],
      id: '',
      info: {
        infoDTO: {},
        relieveDTO: {},
        punishDTO: {}
      },
      canEdit: false, //  item.applyStatus === 20 || item.applyStatus === 40 不能编辑
      relationDTOList: [],
      punimentType: {
        black: this.$t('供应商拉黑'),
        freeze: this.$t('供应商冻结'),
        disuse: this.$t('供应商淘汰'),
        removeBlack: this.$t('供应商解除拉黑'),
        removeFreeze: this.$t('供应商解除冻结'),
        removeDisuse: this.$t('供应商解除淘汰')
      },
      categoryList: [],
      restrictionTypeList: [],
      punishObject: 1
    }
  },
  watch: {
    punishObject() {
      if (
        this.infoDTO.applyType == 'relieve' &&
        this.punishObject === '2' &&
        this.punishRange != 2
      ) {
        this.dataSource = [
          {
            title: this.$t('操作记录')
          }
        ]
        this.selectIndex = 1
      } else {
        this.dataSource = [
          {
            title: this.$t('单据配置')
          },
          {
            title: this.$t('操作记录')
          }
        ]
        this.selectIndex = 0
      }
    },
    punishRange() {
      if (
        this.infoDTO.applyType == 'relieve' &&
        this.punishObject === '2' &&
        this.punishRange != 2
      ) {
        this.dataSource = [
          {
            title: this.$t('操作记录')
          }
        ]
        this.selectIndex = 1
      } else {
        this.dataSource = [
          {
            title: this.$t('单据配置')
          },
          {
            title: this.$t('操作记录')
          }
        ]
        this.selectIndex = 0
      }
    }
  },
  filters: {
    filterTime: function (value) {
      return utils.formateTime(value, 'yyyy-MM-dd')
    }
  },
  async created() {
    let { id } = this.$route.query
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getAccessDetail(id)
  },
  computed: {
    infoDTO() {
      return this.info.infoDTO
    },
    punishDTO() {
      return this.info.punishDTO
    }
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    handleSelectTab(index) {
      this.selectIndex = index
    },

    getAccessDetail(id) {
      let detailUrl = 'getchengfaDetail'
      this.$API.SupplierPunishment[detailUrl]({ id })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            let categoryDTOList = res.data.categoryDTOList ? res.data.categoryDTOList : []
            this.info = res.data
            this.relationDTOList = res.data.relationDTOList
            this.canEdit =
              res.data.infoDTO.applyStatus === 10 || res.data.infoDTO.applyStatus === 30
                ? true
                : false

            this.getRestrictionType(res.data.punishDTO.restrictionType)
            let { punishObject, punishRange } = res.data.punishDTO
            // 1 品类 2 公司的惩罚
            this.punishObject = punishObject
            this.punishRange = punishRange
            // 品类选择接口
            if (punishObject === '1') {
              this.getCategoryPartnerRelationsByStatus(
                res.data.relationDTOList[0].partnerRelationId,
                categoryDTOList
              )
            } else if (punishObject === '2' && punishRange === '2') {
              //存一下已选的
              if (res.data.relationDTOList.length > 0) {
                this.getAllPurchaseOrg(res.data.relationDTOList[0].partnerRelationCode)
              }
              this.purchaseOrgDTOList = res.data.purchaseOrgDTOList
            }
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg || this.$t('获取信息失败，请重试'),
            type: 'warning'
          })
        })
    },
    getAllPurchaseOrg(partnerRelationCode) {
      this.$API.SupplierPunishment.getAllPurchaseOrg({
        partnerRelationCode,
        activeStatus: 1
      }).then((res) => {
        let purchaseOrgListAll = res.data || []
        //判断是否勾选
        if (purchaseOrgListAll.length > 0) {
          purchaseOrgListAll.forEach((item) => {
            const onOff = this.purchaseOrgDTOList.some(
              (v) => v.purchaseOrgId === item.purchaseOrgId
            )
            if (onOff) {
              item.isChecked = true
            } else {
              item.isChecked = false
            }
          })
          this.purchaseOrgDTOList.forEach((item) => {
            let bol = purchaseOrgListAll.some((v) => v.purchaseOrgId === item.purchaseOrgId)
            if (!bol) {
              item.isChecked = true
              purchaseOrgListAll.push(item)
            }
          })
        } else {
          this.purchaseOrgDTOList.forEach((item) => {
            let bol = purchaseOrgListAll.some((v) => v.purchaseOrgId === item.purchaseOrgId)
            if (!bol) {
              item.isChecked = true
              purchaseOrgListAll.push(item)
            }
          })
        }
        this.purchaseOrgListAll = purchaseOrgListAll
      })
    },
    getCategoryPartnerRelationsByStatus(partnerRelationId, categoryDTOList) {
      this.$API.SupplierPunishment.getCategoryPartnerRelationsByStatus({
        status: [10, 11],
        partnerRelationId
      })
        .then((res) => {
          const { code, data } = res
          if (code == 200 && data) {
            let categoryListTmp = []
            let categoryList = data
            categoryList.forEach((item) => {
              if (
                categoryDTOList.filter((cItem) => cItem.categoryId === item.categoryId).length > 0
              ) {
                item.isChecked = true
              } else {
                item.isChecked = false
              }
              categoryListTmp.push(item)
            })
            categoryDTOList.forEach((item) => {
              let bol = categoryList.some((e) => {
                return e.categoryId === item.categoryId
              })
              if (!bol) {
                item.isChecked = true
                categoryListTmp.push(item)
              }
            })
            this.categoryList = categoryListTmp
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    // 限制类型 获取枚举
    getRestrictionType(restrictionType) {
      let codeList = []
      if (restrictionType) {
        codeList = restrictionType.split(',') || []
      }
      this.$API.AccessProcess['queryDict']({
        dictCode: 'restrictionType'
      })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200 && data) {
            let restrictionTypeList = data.map((v) => {
              let isChecked = false
              if (codeList.indexOf(v.itemCode) >= 0) {
                isChecked = true
              }
              return {
                ...v,
                isChecked
              }
            })
            this.restrictionTypeList = restrictionTypeList
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    // 保存 富文本 // console.log(this.$refs.MtRichTextEditor.ejsRef.getHtml()) // console.log(this.$refs.MtRichTextEditor.ejsRef.getText())
    saveText(fn) {
      this.$loading()
      let applyReason = ''
      try {
        console.log(this.infoDTO?.applyReason, 'this.infoDTO?.applyReason')
        applyReason = this.infoDTO?.applyReason
      } catch (error) {
        this.$hloading()
        console.log('富文本问题：', error)
      }
      let restrictionTypeCheckedList = this.$refs.editRef.restrictionTypeCheckedList
        ? this.$refs.editRef.restrictionTypeCheckedList.map((v) => {
            return v.itemCode || ''
          })
        : []
      // console.log(this.$refs.editRef.$refs.MtRichTextEditor.ejsRef.getText());
      let { infoDTO, relationDTOList, punishDTO } = this.info

      let query = {
        infoDTO: {
          ...infoDTO,
          applyReason
        },
        punishDTO: {
          ...punishDTO,
          restrictionType: restrictionTypeCheckedList ? restrictionTypeCheckedList.join(',') : ''
        },
        relationDTOList
      }

      let saveUrl = 'updatapunishment'
      // 详情报错品类
      if (this.punishObject === '1') {
        this.saveCategroy(saveUrl, query, fn)
        // this.updatePurchaseOrg(saveUrl, query, fn);
      } else if ((this.punishObject === '2') & (this.punishRange === '2')) {
        this.updatePurchaseOrg(saveUrl, query, fn)
      } else {
        this.queryUpdata(saveUrl, query, fn)
      }
    },

    // 保存品类接口
    updatePurchaseOrg(saveUrl, query, fn) {
      let purchaseOrgDTOList = this.purchaseOrgListAll.filter((item) => item.isChecked)

      if (purchaseOrgDTOList.length === 0) {
        this.$toast({
          content: this.$t('选择组织未填写!'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      purchaseOrgDTOList.forEach((item) => {
        item.status = item.activeStatus
      })
      this.$API.SupplierPunishment.updatePurchaseOrg({
        applyId: this.info.infoDTO.id,
        purchaseOrgDTOList
      })
        .then((result) => {
          if (result.code === 200) {
            this.queryUpdata(saveUrl, query, fn)
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg || this.$t('保存失败！'),
            type: 'warning'
          })
        })
    },
    // 保存品类接口
    saveCategroy(saveUrl, query, fn) {
      let categoryDTOList = this.$refs.editRef.checkedList ? this.$refs.editRef.checkedList : []

      if (categoryDTOList.length === 0) {
        this.$toast({
          content: this.$t('选择范围未填写!'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      categoryDTOList = categoryDTOList.map((item) => {
        return {
          categoryCode: item.categoryCode,
          categoryId: item.categoryId,
          categoryName: item.categoryName,
          categoryRelationId: item.id,
          remark: '',
          status: item.status
        }
      })

      this.$API.SupplierPunishment.updateCategory({
        applyId: this.info.infoDTO.id,
        categoryDTOList: categoryDTOList
      })
        .then((result) => {
          if (result.code === 200) {
            this.queryUpdata(saveUrl, query, fn)
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg || this.$t('保存品类失败！'),
            type: 'warning'
          })
        })
    },
    // 上传接口
    queryUpdata(saveUrl, query, fn) {
      this.$API.SupplierPunishment[saveUrl](query)
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('保存详情成功！'),
              type: 'success'
            })

            if (!!fn && typeof fn === 'function') {
              fn()
            } else {
              this.$hloading()
            }
          } else {
            this.$hloading()
            this.$toast({
              content: this.$t('保存详情失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('保存详情失败，请重试'),
            type: 'warning'
          })
        })
    },
    // 保存并提交
    saveAndSubmit() {
      this.$loading()
      if (this.infoDTO.applyType == 'relieve') {
        this.$API.SupplierPunishment.applySubmit({
          applyIdList: [this.info.infoDTO.id]
        }).then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('提交详情成功'),
              type: 'success'
            })
            this.getAccessDetail(this.id)
          } else {
            this.$toast({
              content: this.$t('提交详情失败，请重试'),
              type: 'warning'
            })
          }
        })
      } else {
        this.saveText(() => {
          this.$API.SupplierPunishment.applySubmit({
            applyIdList: [this.info.infoDTO.id]
          }).then((res) => {
            this.$hloading()
            if (res.code === 200 && !utils.isEmpty(res.data)) {
              this.$toast({
                content: this.$t('提交详情成功'),
                type: 'success'
              })
              this.getAccessDetail(this.id)
            } else {
              this.$toast({
                content: this.$t('提交详情失败，请重试'),
                type: 'warning'
              })
            }
          })
        })
      }
    },
    // edit里面修改relationDTOList
    changeRelation(relationDTOList) {
      this.info.relationDTOList = relationDTOList
    }
  }
}
</script>
<style lang="scss">
.mt-tabs {
  margin: 10px 0;
  width: 100%;
}
</style>
<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.supplierDetall-container {
  height: 100%;
  padding-top: 20px;

  .inner-warp {
    min-height: 60vh;
  }

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .titles-box {
      width: 100%;
      flex-direction: column;
      justify-content: space-between;

      .mian-line {
        width: 100%;
        justify-content: space-between;
      }

      .mian-title {
        font-size: 20px;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }

      .scroll-box {
        width: 100%;
        display: flex;
        white-space: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        margin-top: 20px;

        .normal-title-box {
          height: 88px;
          background: rgba(255, 255, 255, 1);
          border-radius: 8px;
          font-size: 14px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
          padding: 0 20px;
        }

        .gong-title {
          padding: 20px 0 0 0;
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
        }

        .gong-id {
          padding: 20px 0 0 0;
          font-size: 14px;
          font-weight: 600;
          color: rgba(0, 70, 156, 1);
        }
      }

      .sub-title {
        width: 100%;
        font-size: 12px;
        color: rgba(41, 41, 41, 1);
        margin-top: 10px;
        .normal-title {
          font-size: 12px;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
        }
        .normal-title-dan {
          font-size: 14px;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
          font-weight: 600;

          span {
            font-weight: 600;
          }

          .b-color {
            color: #00469c;
          }
        }
      }

      .mr-20 {
        margin-top: 20px;
      }
    }

    .btns-box {
      align-items: center;
      font-size: 14px;
      max-width: 300px;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      justify-content: flex-end;

      .invite-btn {
        margin-right: 20px;
        cursor: pointer;
        font-weight: 600;
      }

      .invite-btn:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
