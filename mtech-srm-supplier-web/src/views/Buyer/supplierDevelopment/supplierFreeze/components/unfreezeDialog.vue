<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    css-class="add-dialog"
    :buttons="buttons"
    @close="cancel"
    :open="onOpen"
  >
    <div class="form-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="applyName" class="form-item" :label="$t('单据名称')">
              <mt-input
                v-model="formInfo.applyName"
                :disabled="false"
                :max-length="30"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <!-- <mt-form-item
              prop="applyType"
              class="form-item"
              :label="$t('申请单类型')"
            >
              <mt-select
                v-model="formInfo.applyType"
                :data-source="typeList"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item> -->
            <mt-form-item prop="applyerOrgId" :label="$t('申请人公司')">
              <mt-select
                v-model="formInfo.applyerOrgId"
                float-label-type="Never"
                disabled
                :data-source="orgList"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="orgChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseId" :label="$t('供应商')">
              <mt-input :value="formInfo.supplierEnterpriseName" disabled type="text"></mt-input>
              <!-- <mt-select
                v-model="formInfo.supplierEnterpriseId"
                float-label-type="Never"
                disabled
                :data-source="supplierList"
                :fields="{
                  text: 'supplierEnterpriseName',
                  value: 'supplierEnterpriseId',
                }"
                :allow-filtering="true"
                :placeholder="$t('请选择供应商')"
                @change="supplierEnterpriseChange"
              ></mt-select> -->
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="punishObject" class="form-item" :label="$t('对象维度')">
              <mt-select
                ref="companyRef"
                disabled
                v-model="formInfo.punishObject"
                :data-source="objectWith"
                :show-clear-button="true"
                :placeholder="$t('请选择对象维度')"
                @change="changeObjWidth"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="punishRange" class="form-item" :label="$t('组织维度')">
              <mt-select
                disabled
                ref="companyRef"
                v-model="formInfo.punishRange"
                :data-source="orgWith"
                :show-clear-button="true"
                :placeholder="$t('请选择组织维度')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <!-- <mt-form-item
              prop="penaltyPeriodStart"
              class="form-item"
              label="冻结时间"
            ><mt-date-range-picker
                :openOnFocus="true"
                :v-model="formInfo.penaltyPeriodStart"
                :startDate="formInfo.penaltyPeriodStart"
                :endDate="formInfo.penaltyPeriodEnd"
                format="yyyy-MM-dd"
                @change="changeTime"
                :min="new Date()"
                :placeholder="$t('选择开始时间和结束时间')"
              ></mt-date-range-picker>
            </mt-form-item> -->
            <mt-form-item prop="penaltyPeriodStart" class="form-item" :label="$t('处罚开始期限')">
              <mt-date-picker
                v-model="formInfo.penaltyPeriodStart"
                format="yyyy-MM-dd"
                :min="new Date()"
                :open-on-focus="true"
                :allow-edit="false"
                disabled
                :placeholder="$t('选择处罚开始日期')"
              ></mt-date-picker>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="penaltyPeriodEnd" class="form-item" :label="$t('处罚结束期限')">
              <mt-date-picker
                v-model="formInfo.penaltyPeriodEnd"
                format="yyyy-MM-dd"
                :min="new Date()"
                disabled
                :open-on-focus="true"
                :allow-edit="false"
                :placeholder="$t('选择处罚结束日期')"
              ></mt-date-picker>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
let inInit = false
export default {
  data() {
    return {
      formInfo: {
        applyName: '',
        applyerOrgId: '',
        orgCode: '',
        applyerOrgName: '',
        supplierEnterpriseId: '',
        punishObject: '', // 惩罚对象
        punishRange: '', // 惩罚范围
        penaltyPeriodStart: new Date() || '',
        penaltyPeriodEnd: new Date() || ''
      },
      typeList: [
        {
          text: '冻结',
          value: 'punish'
        },
        {
          text: '解冻',
          value: 'relieve'
        }
      ],
      applyCompanyData: [],
      rules: {
        applyName: [
          {
            required: true,
            message: this.$t('请填写单据名称'),
            trigger: 'blur'
          }
        ]
      },
      newDate: [],
      objectWith: [
        {
          text: this.$t('供应商-品类'),
          value: '1'
        },
        {
          text: this.$t('供应商'),
          value: '2'
        }
      ],
      orgWith: [
        {
          text: this.$t('公司'),
          value: '1'
        },
        {
          text: this.$t('采购组织'),
          value: '2'
        }
        // {
        //   text: this.$t("集团级"),
        //   value: "3",
        // },
      ],
      orgList: [], // 公司列表
      supplierList: [], // 公司列表

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { content: this.$t('保存') }
        },
        {
          click: this.confirmFirstStep,
          buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
        }
      ],
      info: {},
      disableSave: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    dialogTitle() {
      return this.modalData.title
    },
    id() {
      return this.modalData.info[0].id
    }
  },
  async created() {
    const userDetail = await this.getUserDetail()
    if (userDetail && userDetail.id) {
      this.getChildrenCompanyOrganization(userDetail.id)
    }
  },
  mounted() {
    inInit = true
    let id = this.modalData.info[0].id
    this.getdetail(id)
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // this.$emit("confirm-function", id);
    getUserDetail() {
      return this.$API.supplierPurchaseData.queryUserDetail().then((res) => {
        let { data } = res
        let { companyOrg } = data
        return companyOrg
      })
    },

    getChildrenCompanyOrganization() {
      // this.$API.supplierInvitation["getChildrenCompanyOrganization"]({
      //   organizationId: userId,
      // }).then((result) => {
      this.$API.supplierInvitation['getChildrenCompanyOrganization2']({
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true
      }).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          this.orgList = result.data.filter((item) => {
            return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
          })
        } else {
          this.orgList = []
        }
      })
    },

    orgChange(event) {
      const { itemData = {} } = event

      this.formInfo.orgCode = itemData?.orgCode
      this.formInfo.applyerOrgName = itemData?.applyerOrgName
      // 清空联动
      !inInit && this.clearsupplierEnterprise()
      inInit = false
      this.getOrgPartnerRelations(itemData?.id)
    },

    // STATUS_DRAFT(1, "注册"),
    // STATUS_POTENTIAL(2, "潜在"),
    // STATUS_NORMAL(10, "合格"),
    // STATUS_FROZEN(20, "冻结"),
    // STATUS_BLACK(30, "黑名单"),
    // Integer 类型 这些状态可以惩罚
    getOrgPartnerRelations(orgId) {
      this.$API.supplierEffective
        .getOrgPartnerRelationsByStatus({
          orgId,
          status: [1, 2, 10, 20, 30]
        })
        .then((res) => {
          if (res.code == 200 && !utils.isEmpty(res.data)) {
            this.supplierList = res.data
          } else {
            this.supplierList = []
          }
        })
    },

    supplierEnterpriseChange(event) {
      const { itemData = {} } = event
      this.formInfo.supplierEnterpriseCode = itemData?.supplierEnterpriseCode
      this.formInfo.supplierEnterpriseName = itemData?.supplierEnterpriseName
      this.formInfo.supplierEnterpriseId = itemData?.supplierEnterpriseId
      this.formInfo.partnerRelationCode = itemData?.partnerRelationCode
      this.formInfo.partnerRelationId = itemData?.id
      this.formInfo.partnerArchiveId = itemData?.partnerArchiveId
      this.formInfo.supplierType = itemData?.supplierType
      this.formInfo.supplierName = itemData?.supplierName
      this.formInfo.categoryId = ''
      if (this.formInfo.punishObject === '1') {
        this.getCategoryPartnerRelationsByStatus()
      }
    },

    clearsupplierEnterprise() {
      this.formInfo.supplierEnterpriseCode = ''
      this.formInfo.supplierEnterpriseName = ''
      this.formInfo.supplierEnterpriseId = ''
      this.formInfo.partnerRelationCode = ''
      this.formInfo.partnerRelationId = ''
      this.formInfo.partnerArchiveId = ''
      this.formInfo.supplierType = ''
      this.formInfo.supplierName = ''
      this.formInfo.categoryId = ''
    },

    // 品类选择的时候 限制未绑定品类的供应商
    changeObjWidth(event) {
      const { itemData } = event
      if (itemData.value === '1') {
        this.formInfo.punishRange = '1'
        this.getCategoryPartnerRelationsByStatus()
      } else {
        this.disableSave = false
      }
    },

    getCategoryPartnerRelationsByStatus() {
      let partnerRelationId = this.formInfo.partnerRelationId
      if (!partnerRelationId) {
        this.$toast({
          content: this.$t('请先选择供应商！'),
          type: 'error'
        })
        return
      }
      this.disableSave = false
      this.$loading()
      this.$API.SupplierPunishment.getCategoryPartnerRelationsByStatus({
        status: [1, 10, 11, 20],
        partnerRelationId
      })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code == 200 && data.length === 0) {
            this.disableSave = true
            this.$toast({
              content: this.$t('您所选择的供应商尚未绑定品类，不能进行品类对象维度的惩罚！'),
              type: 'error'
            })
          } else if (code == 200 && data.length > 0) {
            this.disableSave = false
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    confirmFirstStep() {
      this.save((infoDTO) => {
        this.$emit('confirm-function', { type: 'jump', id: infoDTO.id })
      })
    },
    queryUserInfo() {
      return this.$API.masterData.getUserInfo()
    },

    // 保存
    async save(fn) {
      let formInfo = this.formInfo
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }
      const res = await this.queryUserInfo()
      let applyerName = ''
      let applyerDeptName = ''
      if (res.code == 200) {
        let userInfo = res.data
        applyerName = userInfo.username
        if (Array.isArray(userInfo.orgList)) {
          for (let i = 0; i < userInfo.orgList.length; i++) {
            if (userInfo.orgList[i].orgLevelTypeCode == 'ORG03') {
              applyerDeptName = userInfo.orgList[i].orgName
              break
            }
          }
        }
      }
      let infoDTO = {
        applyName: formInfo.applyName,
        applyType: 'relieve',
        applyerName,
        applyerDeptName
      }

      let relieveDTO = {
        punishObject: formInfo.punishObject,
        punishRange: formInfo.punishRange,
        penaltyPeriodEnd: utils.formateTime(formInfo.penaltyPeriodEnd),
        penaltyPeriodStart: utils.formateTime(formInfo.penaltyPeriodStart),
        businessType: 'removeFreeze'
      }

      let relationDTOList = [
        {
          supplierEnterpriseCode: formInfo.supplierEnterpriseCode,
          supplierEnterpriseName: formInfo.supplierEnterpriseName,
          supplierEnterpriseId: formInfo.supplierEnterpriseId,
          partnerRelationCode: formInfo.partnerRelationCode,
          partnerRelationId: formInfo.partnerRelationId,
          partnerArchiveId: formInfo.partnerArchiveId,
          supplierType: formInfo.supplierType,
          supplierName: formInfo.supplierName,
          relatedBillId: this.modalData.info[0].id,
          status: formInfo.beforePartnerRelationStatus
        }
      ]

      let query = {
        infoDTO,
        relieveDTO,
        relationDTOList
      }
      this.$loading()
      let url = this.isEdit
        ? this.$API.SupplierPunishment.updatapuncomfm
        : this.$API.SupplierPunishment.getReliveAdd
      url(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({
              content: !this.isEdit ? this.$t('新增成功') : this.$t('编辑成功'),
              type: 'success'
            })
            if (!!fn && typeof fn === 'function') {
              let { infoDTO } = res.data
              fn(infoDTO)
            } else {
              this.$emit('confirm-function', 'reload')
            }
          } else {
            this.$toast({
              content: !this.isEdit ? this.$t('新增失败，请重试') : this.$t('编辑失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content:
              error.msg ||
              (!this.isEdit ? this.$t('新增失败，请重试') : this.$t('编辑失败，请重试')),
            type: 'warning'
          })
        })
    },
    getdetail(id) {
      this.$API.SupplierPunishment.getchengfaDetail({ id: id }).then((res) => {
        this.info = res.data
        let { punishDTO, infoDTO, relationDTOList } = res.data
        this.formInfo = {
          ...punishDTO,
          ...infoDTO,
          ...relationDTOList[0]
        }
        this.formInfo.applyName = ''
      })
    },

    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    cancel() {
      this.$refs['dialog'].ejsRef.hide()
    },

    changeTime(args) {
      const { startDate, endDate } = args
      this.formInfo = {
        ...this.formInfo,
        penaltyPeriodStart: startDate,
        penaltyPeriodEnd: endDate
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-dialog {
  padding: 32px 6px 0 6px !important;

  /deep/ .active {
    background-color: rgb(17, 147, 255) !important;
  }
}
</style>
