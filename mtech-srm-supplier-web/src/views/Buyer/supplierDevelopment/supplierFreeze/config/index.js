import { i18n } from '@/main.js'

export const listColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商SAP编码')
  },
  {
    field: 'supplierInternalCode',
    title: i18n.t('供应商SRM编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称')
  },
  {
    field: 'purchaseOrgCode',
    title: i18n.t('采购组织'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.purchaseOrgName  : ''
    }
  },
  {
    field: 'orgCode',
    title: i18n.t('申请公司'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.orgName : ''
    }
  },
  {
    field: 'punishObject',
    title: i18n.t('对象维度')
  },
  {
    field: 'punishRange',
    title: i18n.t('组织维度')
  }
]

export const historyColumnData = [
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'operateName',
    title: i18n.t('节点')
  },
  {
    field: 'operateType',
    title: i18n.t('操作类型')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商SAP编码')
  },
  {
    field: 'supplierInternalCode',
    title: i18n.t('供应商SRM编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称')
  },
  {
    field: 'purchaseOrgCode',
    title: i18n.t('采购组织'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.purchaseOrgName  : ''
    }
  },
  {
    field: 'orgCode',
    title: i18n.t('申请公司'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.orgName : ''
    }
  },
  {
    field: 'operateDescription',
    title: i18n.t('原因')
  },
  {
    field: 'createUserName',
    title: i18n.t('操作人')
  },
  {
    field: 'createTime',
    title: i18n.t('操作时间')
  }
]
