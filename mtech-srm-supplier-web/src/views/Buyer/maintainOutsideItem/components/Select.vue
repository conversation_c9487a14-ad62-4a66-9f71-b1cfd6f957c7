<template>
  <div>
    <!--
      @open="startOpen"
     -->
    <mt-select
      v-if="data.column.field === 'itemCode'"
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      :popup-width="450"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :filtering="filtering"
      :allow-filtering="true"
    ></mt-select>
    <mt-select
      v-else
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :filtering="filtering"
      :allow-filtering="true"
    ></mt-select>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {
      data: {},

      placeholder: this.$t('请选择'),

      fields: { text: 'text', value: 'categoryCode' },

      dataSource: [],

      isDisabled: false
    }
  },

  mounted() {
    this.filtering = utils.debounce(this.filtering, 500)
    if (this.data.column.field === 'itemCode') {
      this.fields = { text: 'text', value: 'itemCode' }
      if (!this.data.id) {
        this.isDisabled = false
        if (this.data.itemCode && this.data.itemName) {
          this.dataSource = [
            {
              text: this.data.itemCode + '-' + this.data.itemName,
              itemCode: this.data.itemCode,
              itemName: this.data.itemName,
              id: this.data.itemId
            }
          ]
        } else {
          this.itemSelect()
        }
      } else {
        this.isDisabled = true
        this.dataSource = [
          {
            text: this.data.itemCode + '-' + this.data.itemName,
            itemCode: this.data.itemCode,
            itemName: this.data.itemName,
            id: this.data.itemId
          }
        ]
      }
    }
    if (this.data.column.field === 'categoryCode') {
      this.$loading()
      let params = {
        fuzzyNameOrCode: this.data.categoryCode
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.dataSource = _data
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    }
  },

  methods: {
    filtering(e) {
      if (this.data.column.field === 'categoryCode') {
        this.$loading()
        let params = {
          fuzzyNameOrCode: e.text
        }
        this.$API.maintainOutsideItem
          .criteriaQuery(params)
          .then((res) => {
            res.data.map((item) => {
              item.text = item.categoryCode + '-' + item.categoryName
            })
            e.updateData(res.data)
            this.$hloading()
          })
          .catch((error) => {
            this.$hloading()
            this.$toast({
              content: error.msg || this.$t('查询失败，请重试'),
              type: 'error'
            })
          })
      }
      if (this.data.column.field === 'itemCode') {
        this.$loading()
        let params = {
          fuzzyNameOrCode: e.text
        }
        this.$API.maintainOutsideItem
          .fuzzyQuery(params)
          .then((res) => {
            res.data.map((item) => {
              item.text = item.itemCode + '-' + item.itemName
            })
            e.updateData(res.data)
            this.$hloading()
          })
          .catch((error) => {
            this.$hloading()
            this.$toast({
              content: error.msg || this.$t('查询失败，请重试'),
              type: 'error'
            })
          })
      }
    },
    selectChange(val) {
      if (this.data.column.field === 'itemCode') {
        let params = {
          fieldCode: 'itemName',
          itemInfo: {
            itemId: val.itemData.id,
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName
          }
        }

        this.$parent.$emit('parentcategoryCode', params)
        this.$bus.$emit('maintainOutsideItemCode', val.itemData)
      }
      if (this.data.column.field === 'categoryCode') {
        let params = {
          fieldCode: 'categoryName',
          itemInfo: {
            categoryId: val.itemData.id,
            categoryCode: val.itemData.categoryCode,
            categoryName: val.itemData.categoryName
          }
        }
        this.$parent.$emit('parentcategoryCode', params)
        this.$bus.$emit('maintainOutsideItemcategoryCode', val.itemData)
      }
    },
    itemSelect() {
      this.$loading()
      let params = {
        fuzzyNameOrCode: this.data.itemCode
      }
      this.$API.maintainOutsideItem
        .fuzzyQuery(params)
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.itemCode + '-' + item.itemName
          })
          this.dataSource = _data
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    }
  },
  beforeDestroy() {
    this.$bus.$off('maintainOutsideItemCode')
    this.$bus.$off('maintainOutsideItemcategoryCode')
  }
}
</script>
