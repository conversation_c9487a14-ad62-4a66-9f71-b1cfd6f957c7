<template>
  <div id="cell-changed">
    <div :id="data.column.field">{{ statusText }}</div>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  computed: {
    statusText() {
      return this.data.syncStatus == 0
        ? this.$t('未同步')
        : this.data.syncStatus == 1
        ? this.$t('同步成功')
        : this.data.syncStatus == 2
        ? this.$t('同步失败')
        : this.data.syncStatus == 3
        ? this.$t('同步中')
        : this.$t('未匹配')
    }
  }
}
</script>
