<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  mounted() {
    if (this.data.column.field === 'categoryName') {
      this.$bus.$on('maintainOutsideItemcategoryCode', (val) => {
        this.data.categoryName = val.categoryName
      }) //接受的物料描述
    }
    if (this.data.column.field === 'itemName') {
      this.$bus.$on('maintainOutsideItemCode', (val) => {
        this.data.itemName = val.itemName
      }) //接受的物料描述
    }
  },
  methods: {
    onInput(e) {
      console.log(e)
    }
  }
}
</script>
