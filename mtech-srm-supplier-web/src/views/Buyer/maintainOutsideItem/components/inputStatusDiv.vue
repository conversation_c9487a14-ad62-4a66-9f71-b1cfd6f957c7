<template>
  <div id="cell-changed">
    <div :id="data.column.field">{{ statusText }}</div>
    <!-- <mt-input
      :id="data.column.field"
      v-model="statusText"
      :disabled="disabled"
      @input="onInput"
    ></mt-input> -->
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  computed: {
    statusText() {
      return this.data.status == 1
        ? this.$t('创建')
        : this.data.status == 2
        ? this.$t('下发中')
        : this.data.status == 3
        ? this.$t('下发成功')
        : this.data.status == 4
        ? this.$t('下发失败')
        : this.$t('未匹配')
    }
  },
  mounted() {
    // if(this.data.column.field == "status"){
    // }
    // this.$bus.$on('categoryCode', (val) => {
    //   this.data.categoryCode = val.categoryCode
    //   console.log(this.data.categoryCode)
    // }) //接受的物料描述
    // this.$bus.$on("itemNameChange", (val) => {
    //   this.data.itemName = val.itemName;
    //   // this.data.lineNo = val.lineNo;
    //   this.data.stockUnit = val.unitCode;
    //   // this.data.unitCode = val.unitCode;
    // }); //接受的物料描述
    // this.$bus.$on('batchChange2', (val) => {
    //   // this.data.lineNo = val.lineNo;
    //   // this.data.unitCode = val.unitCode;
    //   console.log(val)
    //   this.data.maxDemandQuantity = val.maxDemandQuantity
    //   this.data.maxReceiveQuantity = Number(val.maxCreateQty)
    //   this.data.stockQuantity = Number(val.qty)
    //   // this.data.maxReceiveQuantity = val.maxReceiveQuantity;
    //   // this.data.stockQuantity = val.stockQuantity;
    // }) //接受的物料描述
    // this.$bus.$on('maxDemandQuantityChange', (val) => {
    //   this.data.maxDemandQuantity = val
    // })
    // this.$bus.$on('warehouseChange2', (val) => {
    //   console.log(val)
    //   console.log(this.data)
    //   this.data.warehouseName = val.warehouseName
    //   this.data.warehouseCode = val.warehouseCode
    //   this.$parent.$emit('selectedChanged', {
    //     //传出额外数据
    //     fieldCode: 'selectedChange',
    //     itemInfo: {
    //       ...this.data
    //     }
    //   })
    // })
  },
  methods: {
    // onInput(e) {
    //   console.log(e)
    // }
  }
}
</script>
