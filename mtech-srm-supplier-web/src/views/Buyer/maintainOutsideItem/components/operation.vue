<!-- template: () => {
  return {
    template: Vue.component('operation', {
      template: `<div class="operation">
      <span @click="clickSubmit">{{ $t('编辑') }}</span><span @click="clickdisable">{{ $t('删除') }}</span><span @click="clickhistory">{{ $t('修改记录') }}</span></div>`,
      data: function () {
        return {
          data: {}
        }
      },
      methods: {
        clickSubmit() {
          this.$parent.$emit('clickOperation', { data: this.data, value: 'edit' })
        },
        clickdisable() {
          this.$parent.$emit('clickOperation', { data: this.data, value: 'delete' })
        },
        clickhistory() {
          this.$parent.$emit('clickOperation', { data: this.data, value: 'history' })
        }
      }
    })
  }
} -->
<template>
  <div>
    <div class="operation">
      <span @click="clickSubmit">{{ $t('编辑') }}</span
      ><span @click="clickdisable">{{ $t('删除') }}</span
      ><span @click="clickhistory">{{ $t('修改记录') }}</span>
    </div>
  </div>
</template>
<script>
// import { Query } from '@syncfusion/ej2-data'
export default {
  data() {
    return {}
  },

  mounted() {
    // if (this.data.column.field === 'batch') {
    //   // console.log("......", this.data);
    //   // if(this.batch){
    //   // this.plan = "11211212";
    //   // }
    //   this.$bus.$on('batchChange', (val) => {
    //     if (val.stockBatchResponseList.length === 1) {
    //       this.data.batch = val.stockBatchResponseList[0].batch
    //       console.log(val)
    //       this.$bus.$emit('batchChange2', val.stockBatchResponseList[0])
    //       this.$bus.$emit('warehouseChange2', val)
    //     }
    //     if (val.stockBatchResponseList.length === 0) {
    //       this.$toast({
    //         content: '当前物料无库存!',
    //         type: 'warning'
    //       })
    //     }
    //     console.log(val)
    //     // this.data.warehouseCode = val.warehouseCode;
    //     // this.$parent.$emit("selectedChanged", {
    //     //   //传出额外数据
    //     //   fieldCode: "selectedChange",
    //     //   itemInfo: {
    //     //     ...this.data,
    //     //   },
    //     // });
    //     val.stockBatchResponseList.forEach((item) => {
    //       item.name = item.batch //  客户名称
    //       item.value = item.batch //   客户名称
    //     })
    //     this.dataSource = val.stockBatchResponseList || []
    //     this.fields = { text: 'name', value: 'value' }
    //   })
    //   // 批次下拉;
    //   // this.getCategoryItem();
    // }
    // if (this.data.column.field === 'warehouseName') {
    //   this.$bus.$on('warehouseChange', (val) => {
    //     console.log(val)
    //     if (val.length > 0) {
    //       val.forEach((item) => {
    //         item.name = item.warehouseCode + item.warehouseName // 	客户名称
    //         item.value = item.warehouseName == null ? '' : item.warehouseName // 	客户名称
    //       })
    //       this.dataSource = val || []
    //       console.log(this.dataSource)
    //       this.fields = { text: 'name', value: 'value' }
    //       this.data.warehouseName = val[0].warehouseName
    //       this.data.warehouseCode = val[0].warehouseCode
    //       this.$bus.$emit('batchChange', val[0])
    //     }
    //   })
    // }
    // this.dataSource = this.data.column.selectOptions
  },

  methods: {
    clickSubmit() {
      console.log(this)
      this.$parent.$emit('clickOperation', { data: this.data, value: 'edit' })
    },
    clickdisable() {
      this.$parent.$emit('clickOperation', { data: this.data, value: 'delete' })
    },
    clickhistory() {
      this.$parent.$emit('clickOperation', { data: this.data, value: 'history' })
    }
    //   filtering(e) {
    //     var searchData = this.dataSource
    //     // this.purOrderQueryOrder(e.text);
    //     // load overall data when search key empty.
    //     if (e.text == '') e.updateData(searchData)
    //     else {
    //       let query = new Query().select(['itemCode', 'itemCode'])
    //       // change the type of filtering
    //       query = e.text !== '' ? query.where('itemCode', 'contains', e.text, true) : query
    //       e.updateData(searchData, query)
    //     }
    //   },
    //   // getCategoryItem() {
    //   //   //物料下拉
    //   //   this.$API.masterData.getCategoryItem().then((res) => {
    //   //     this.dataSource = res.data || [];
    //   //   });
    //   //   this.fields = { text: "itemCode", value: "itemCode" };
    //   // },
    //   startOpen() {
    //     console.log(this.data, '下拉打开时最新的行数据')
    //     if (this.data.column.field === 'siteName') {
    //       //工厂下拉
    //       if (!this.data.itemCode && !this.itemCode) {
    //         this.$toast({
    //           content: this.$t('请先选择物料信息'),
    //           type: 'warning'
    //         })
    //       }
    //     }
    //     if (this.data.column.field === 'batch') {
    //       console.log(this.entryData)
    //       // console.log(this.plan);
    //       // console.log(this.dataSource);
    //       // console.log(this.data);
    //       // this.$bus.$on("batchChange", (val) => {
    //       //   console.log("......", this.data, val);
    //       //   val.forEach((item) => {
    //       //     item.name = item.batch; //   客户名称
    //       //     item.value = item.batch; //  客户名称
    //       //   });
    //       //   this.dataSource = val || [];
    //       //   this.fields = { text: "name", value: "value" };
    //       //   console.log(this.dataSource);
    //       // }); //接受的单位
    //       // console.log(this.data.column.entryData);
    //       // if (this.dataSource.length === 0) {
    //       //   let params = {
    //       //     buyerEnterpriseId: this.entryData.buyerEnterpriseId,
    //       //     orderQuantity: this.entryData.quantity,
    //       //     quantity: this.data.itemData.quantity,
    //       //     siteCode: this.entryData.siteCode,
    //       //     supplierCode: this.entryData.supplierCode,
    //       //     nature: "1",
    //       //     itemCode: this.data.itemData.itemCode,
    //       //   };
    //       //   this.$API.outsourcing.queryStock(params).then((r) => {
    //       //     r.data.forEach((item) => {
    //       //       item.name = item.batch; //   客户名称
    //       //       item.value = item.batch; //  客户名称
    //       //     });
    //       //     this.dataSource = r.data || [];
    //       //     this.fields = { text: "name", value: "value" };
    //       //   });
    //       // }
    //     }
    //   },
    //   selectChange(val) {
    //     console.log(val.itemData, '下拉数据的信息')
    //     if (this.data.column.field === 'batch') {
    //       // console.log("---batch", this.data);
    //       this.$bus.$emit('batchChange2', val.itemData)
    //     }
    //     if (this.data.column.field === 'itemCode') {
    //       //物料下拉 //传给物料名称
    //       this.data.quantity = val.itemData.quantity
    //       console.log(this.data)
    //       val.itemData.warehouseName = this.$bus.$emit(
    //         'itemNameChange',
    //         val.itemData
    //       )
    //       // console.log("this.entryData", this.entryData);
    //       let params = {
    //         buyerEnterpriseId: this.entryData.buyerEnterpriseId,
    //         orderQuantity: this.entryData.quantity,
    //         quantity: val.itemData.quantity,
    //         siteCode: this.entryData.siteCode,
    //         supplierCode: this.entryData.supplierCode,
    //         nature: '1',
    //         buyerOrgCode: this.entryData.buyerOrgCode,
    //         id: this.data.id,
    //         itemCode: val.itemData.itemCode
    //       }
    //       // this.$API.outsourcing.queryStock(params).then((r) => {
    //       //   this.$bus.$emit("batchChange", r.data);
    //       // });
    //       console.log(this.entryData)
    //       this.$API.outsourcing.supplierQueryWmsStock(params).then((r) => {
    //         this.$bus.$emit('warehouseChange', r.data)
    //         if (r.data.length === 0) {
    //           this.$API.outsourcing.supplierQueryMaxDemand(params).then((res) => {
    //             this.$bus.$emit('maxDemandQuantityChange', res.data)
    //           })
    //         }
    //       })
    //     }
    //     if (this.data.column.field === 'warehouseName') {
    //       console.log(val)
    //       this.$bus.$emit('batchChange', val.itemData)
    //     }
    //   }
    // },
    // beforeDestroy() {
    //   this.$bus.$off('batchChange2')
    //   this.$bus.$off('batchChange')
    //   this.$bus.$off('maxDemandQuantityChange')
    //   this.$bus.$off('warehouseChange')
    //   this.$bus.$off('itemNameChange')
    //   this.$bus.$off('warehouseChange2')
  }
}
</script>
