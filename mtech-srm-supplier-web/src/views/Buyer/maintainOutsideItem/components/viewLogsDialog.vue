<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-template-page ref="templateRef" :template-config="componentConfig" :hidden-tabs="true" />
  </mt-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import { i18n } from '@/main.js'

export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      LogColumnData: [
        {
          field: 'newValue',
          headerText: i18n.t('新值')
        },
        {
          field: 'oldValue',
          headerText: i18n.t('旧值') // 操作描述 操作内容
        },
        {
          field: 'createUserName',
          headerText: i18n.t('操作人')
        },
        {
          field: 'createTime',
          headerText: i18n.t('操作时间')
        }
      ],
      selectData: {},
      componentConfig: []
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, selectData } = entryInfo
      this.selectData = cloneDeep(selectData)
      const asyncConfig = {
        url: `/masterDataManagement/tenant/category-item/operate-record/find-by-category`,
        params: selectData,
        date: new Date()
      }
      this.componentConfig = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            useBaseConfig: false,
            tools: [[], ['Refresh', 'Setting']]
          },
          grid: {
            editSettings: {
              allowEditing: false
            },
            allowPaging: true, // 分页
            lineIndex: 0,
            columnData: this.LogColumnData,
            dataSource: [],
            asyncConfig: asyncConfig
          }
        }
      ]
      this.dialogTitle = title // 弹框名称
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
