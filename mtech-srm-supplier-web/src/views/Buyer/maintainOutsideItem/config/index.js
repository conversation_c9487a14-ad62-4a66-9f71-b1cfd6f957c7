import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
import InputView from '../components/InputView.vue'
import inputStatus from '../components/inputStatus.vue'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_upload', title: i18n.t('新增') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'SAP', icon: 'icon_solid_restart', title: i18n.t('同步SAP') },
  { id: 'Export1', icon: 'icon_solid_export', title: i18n.t('导出') }
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false //此列不允许编辑
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      operator: 'likeright'
    },
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    allowEditing: true,
    searchOptions: {
      operator: 'likeright'
    },
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        1: i18n.t('同步成功'),
        2: i18n.t('同步失败'),
        3: i18n.t('同步中')
      }
    },
    allowEditing: false,
    editTemplate: () => {
      return { template: inputStatus }
    }
  },
  {
    field: 'failReason',
    headerText: i18n.t('失败原因'),
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    allowEditing: false
  },
  {
    field: 'updateTime',
    headerText: i18n.t('最近更新时间'),
    allowEditing: false,
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000 - 1440000)

        //自定义搜索值，规则
        return obj
      },
      renameField: 'updateTime'
    }
  },
  {
    width: 120,
    field: 'operation',
    headerText: i18n.t('操作'),
    cellTools: [
      {
        id: 'View',
        icon: '',
        title: i18n.t('查看日志'),
        visibleCondition: (data) => {
          return Number(data.syncStatus || 0) > 0
        }
      }
    ],
    allowEditing: false,
    ignore: true
  }
]
