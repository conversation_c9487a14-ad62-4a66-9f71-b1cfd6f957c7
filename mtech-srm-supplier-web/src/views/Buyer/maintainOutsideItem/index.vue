<!-- 物料维护外部物料组 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @parentcategoryCode="parentcategoryCode"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <!-- 导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
    <view-logs-dialog ref="viewLogsDialog"></view-logs-dialog>
  </div>
</template>

<script>
import { download } from '@/utils/utils'
import { toolbar, columnData } from './config/index'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
import ViewLogsDialog from './components/viewLogsDialog.vue'

import { cloneDeep } from 'lodash'
export default {
  components: {
    UploadExcelDialog,
    ViewLogsDialog
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: '86715f74-aa22-473f-94cf-5ca3967577c8',
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          grid: {
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData,
            // commandClick: this.commandClick, //配置单击进入行编辑
            asyncConfig: {
              url: '/masterDataManagement/tenant/category-item/paged-query'
            }
          }
        }
      ],
      submitTableData: [],
      queryIPageData: [],
      categoryCodeObj: {},
      initial: {
        categoryCode: '',
        number: 0
      },
      // ---------->
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'maintainOutsideItem',
        templateUrl: 'export', // 下载模板接口方法名
        uploadUrl: 'import', // 上传接口方法名
        file: 'file' //后端接收参数名
      }
    }
  },
  computed: {},
  mounted() {},
  methods: {
    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (tool.id) {
        this.handleClickOption(data)
      }
    },
    handleClickToolBar(e) {
      let selectList = e.gridRef.getMtechGridRecords()
      if (selectList.length < 1 && (e.toolbar.id === 'save' || e.toolbar.id === 'export')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      } else if (e.toolbar.id === 'save') {
        this.handleClickToolBarSave(selectList)
      } else if (e.toolbar.id === 'upload') {
        this.handleClickToolBarUpload()
      } else if (e.toolbar.id === 'export') {
        this.handleClickToolBarExport(selectList)
      } else if (e.toolbar.id === 'Export1') {
        this.handleClickExport()
      } else if (e.toolbar.id === 'Download') {
        this.handleClickToolBarDownload(selectList)
      } else if (e.toolbar.id === 'SAP') {
        this.handleClickToolBarSAP(selectList)
      }
    },
    // 点击查看日志
    handleClickOption(data) {
      const params = {
        bizId: data.id
      }
      this.$refs.viewLogsDialog.dialogInit({
        title: this.$t('操作日志'),
        selectData: params
      })
    },
    handleClickExport() {
      let params = this.$refs.tepPage.getAsyncParams()
      params.page.size = 9999
      this.$API.maintainOutsideItem.categoryItemExport(params).then((res) => {
        const { data, headers } = res
        const _this = this
        if (data.type === 'application/json') {
          const reader = new FileReader() //创建一个FileReader实例
          reader.readAsText(data, 'utf-8') //读取文件,结果用字符串形式表示
          reader.onload = function () {
            //读取完成后,**获取reader.result**
            const { msg } = JSON.parse(reader.result)
            _this.$toast({
              content: msg || _this.$t('导出失败，请稍后尝试'),
              type: 'warning'
            })
          }
          return
        }
        download({
          // fileName: '模板清单.xlsx',
          fileName: decodeURI(headers['content-disposition'].split("=utf-8'zh_cn'")[1]),
          blob: data
        })
      })
    },
    handleClickToolBarAdd() {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // handleClickToolBarUpload() {
    //   this.$dialog({
    //     modal: () =>
    //       import(
    //         /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ '@/components/uploadDialog/index.vue'
    //       ),
    //     data: {
    //       title: this.$t('上传/导入'),
    //       paramsKey: 'file',
    //       importApi: this.$API.maintainOutsideItem.import,
    //       downloadTemplateApi: this.$API.maintainOutsideItem.export
    //     },
    //     success: () => {
    //       this.$refs.tepPage.refreshCurrentGridData()
    //     }
    //   })
    // },
    handleClickToolBarUpload() {
      this.showUploadExcel(true)
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      console.log(flag)
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm(msg) {
      this.showUploadExcel(false)
      this.$toast({
        content: msg ? msg : this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.tepPage.refreshCurrentGridData()
    },
    handleClickToolBarSAP(selectList) {
      let _ids = selectList.map((item) => item.id)
      this.$API.maintainOutsideItem.batchSync({ ids: _ids }).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    parentcategoryCode(val) {
      Object.assign(this.categoryCodeObj, val.itemInfo || {})
    },
    endEdit() {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    actionBegin(args) {
      let { requestType } = args
      if (requestType == 'save') {
        if (Object.values(this.categoryCodeObj).length > 0) {
          let _categoryCodeObj = cloneDeep(this.categoryCodeObj)
          args.data = {
            ...args.data,
            ..._categoryCodeObj
          }
          for (let key in this._categoryCodeObj) {
            delete this.categoryCodeObj[key]
          }
        }
      } else if (requestType == 'beginEdit') {
        if (this.initial.number == 0) {
          this.initial.categoryCode = args.rowData.categoryCode
        }
        this.initial.number++
      }
    },
    actionComplete(e) {
      if (e.requestType == 'save') {
        this.$loading()
        const { rowIndex } = e

        if (e.data.id) {
          if (e.data.categoryCode !== this.initial.categoryCode) {
            let params = {
              id: e.data.id,
              categoryId: e.data.categoryId,
              itemId: e.data.itemId
            }
            this.$API.maintainOutsideItem
              .updateAndSync(params)
              .then(() => {
                this.$hloading()
                Object.assign(this.categoryCodeObj, {})
                Object.assign(this.initial, {
                  categoryCode: '',

                  number: 0
                })
                this.$refs.tepPage.refreshCurrentGridData()
              })
              .catch(() => {
                this.$hloading()
                this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
                this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
              })
          } else {
            this.$hloading()
            Object.assign(this.categoryCodeObj, {})
            Object.assign(this.initial, {
              categoryCode: '',
              number: 0
            })
          }
        } else {
          let params = {
            categoryId: e.data.categoryId,
            itemId: e.data.itemId
          }
          this.$API.maintainOutsideItem
            .saveAndSync(params)
            .then(() => {
              this.$hloading()
              Object.assign(this.categoryCodeObj, {})
              Object.assign(this.initial, {
                categoryCode: '',
                number: 0
              })
              this.$refs.tepPage.refreshCurrentGridData()
            })
            .catch(() => {
              this.$hloading()
              this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
              this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
            })
        }
      }
    }
  }
}
</script>

<style lang="scss">
.full-height {
  height: 100vh;
  background-color: #fff;
  .e-flat {
    color: #2783fe;
  }
  .operation {
    span {
      color: #2783fe;
      text-decoration: underline;
      margin: 0 10px;
    }
  }
  .operation:hover {
    cursor: pointer;
  }
  .e-color {
    color: #000;
  }
}
</style>
