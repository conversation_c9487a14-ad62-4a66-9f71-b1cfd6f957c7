<template>
  <div class="supplierDetall-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="titles-box">
        <div class="mian-line fbox">
          <div class="mian-title" v-if="!!infoDTO.applyName">
            <template v-if="!!infoDTO.applyName">
              {{ infoDTO.applyName || '--' }}
            </template>
          </div>
          <div class="btns-box fbox">
            <div class="invite-btn" @click="onBack">
              <mt-button css-class="e-info">{{ $t('返回') }}</mt-button>
            </div>
            <template v-if="canEdit">
              <div class="invite-btn" @click="onSave">
                <mt-button css-class="e-info">{{ $t('保存') }}</mt-button>
              </div>
              <div class="invite-btn" @click="onSaveAndPush">
                <mt-button css-class="e-info">{{ $t('保存并提交') }}</mt-button>
              </div>
            </template>
          </div>
        </div>
        <div class="sub-title fbox">
          <div class="normal-title" v-if="!!infoDTO.applyCode">
            {{ $t('申请单编码') + '：' + infoDTO.applyCode }}
          </div>
          <div class="normal-title" v-if="!!infoDTO.createUserName || !!infoDTO.createTime">
            {{ $t('创建人') + '：' + infoDTO.createUserName }}
          </div>
          <div class="normal-title" v-if="!!info && !!info.infoDTO">
            {{ $t('创建时间') + '：' + infoDTO.createTime }}
          </div>
          <div class="normal-title" v-if="!!infoDTO.applyerName">
            {{ $t('申请人') + '：' + infoDTO.applyerName }}
          </div>
          <div class="normal-title" v-if="!!info && !!info.infoDTO">
            {{ $t('申请人部门') + '：' + infoDTO.applyerDeptName }}
          </div>
        </div>
        <div class="sub-title mr-20 fbox">
          <div class="normal-title-dan" v-if="!!info.gradeDTO && !!info.gradeDTO.businessType">
            <span>{{ $t('类型') }}：</span>{{ info.gradeDTO.businessType || '--' }}
          </div>
          <div class="normal-title-dan">
            <span>{{ $t('组织名称') }}：</span>{{ info.gradeDTO.orgName || '--' }}
          </div>
          <div class="normal-title-dan" v-if="!!info.gradeDTO && !!info.gradeDTO.orgDimension">
            <span>{{ $t('组织') }}：</span>{{ info.gradeDTO.orgDimension }}
          </div>
          <div class="normal-title-dan">
            <span>{{ $t('当前级别') }}：</span>{{ info.gradeDTO.gradeName }}
            <span>{{ info.gradeDTO.gradeType }}</span>
          </div>
        </div>
        <div class="scroll-box fbox">
          <div class="normal-title-box" v-for="(item, index) in info.relationDTOList" :key="index">
            <div class="gong-title">{{ item.supplierEnterpriseName || '--' }}</div>
            <div class="gong-id">{{ item.supplierEnterpriseCode || '--' }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 头部结束 -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>

    <detail-edit
      v-if="selectIndex === 0"
      :id="info.infoDTO.id"
      :can-edit="canEdit"
      :apply-info="infoDTO"
      ref="detailEdit"
    ></detail-edit>
    <operator-history
      v-if="selectIndex === 1"
      :id="info.infoDTO.id"
      :apply-info="infoDTO"
    ></operator-history>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import operatorHistory from './components/operatorHistory.vue'
import detailEdit from './components/detailEdit.vue'
export default {
  components: {
    detailEdit,
    operatorHistory
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('单据配置')
        },
        {
          title: this.$t('操作记录')
        }
      ],
      id: '',
      info: {},
      canEdit: false
    }
  },
  created() {
    let id = this.$route.query.id
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getclassifDetail(id)
  },
  computed: {
    infoDTO() {
      return !!this.info && this.info.infoDTO ? this.info.infoDTO : {}
    }
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    onSave() {
      // console.log(this.$refs.detailEdit.$refs.MtRichTextEditor.value);
      this.info.infoDTO.applyReason = this.$refs.detailEdit.$refs.MtRichTextEditor.value
      this.confirm()
    },
    onSaveAndPush() {
      // console.log(this.$refs.detailEdit.$refs.MtRichTextEditor.value);
      this.info.infoDTO.applyReason = this.$refs.detailEdit.$refs.MtRichTextEditor.value
      this.confirmSubmit()
    },
    // 提交
    confirm(fn = '') {
      let query = this.info
      this.$loading()
      this.$API.SupplierPunishment.updatagrade(query)
        .then((res) => {
          this.$hloading()
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
            if (!!fn && typeof fn === 'function') {
              fn()
            } else {
              this.$hloading()
            }
          } else {
            this.$hloading()
            this.$toast({
              content: this.$t('保存失败，请重试'),
              type: 'error'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('保存失败，请重试'),
            type: 'error'
          })
        })
    },

    confirmSubmit() {
      this.confirm(() => {
        this.$loading()
        this.$API.SupplierPunishment.applySubmit({
          applyIdList: [this.info.infoDTO.id]
        }).then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('提交分级成功!'),
              type: 'success'
            })
            this.getclassifDetail(this.id)
          } else {
            this.$toast({
              content: this.$t('提交分级失败，请重试!'),
              type: 'warning'
            })
          }
        })
      })
    },

    handleSelectTab(index) {
      this.selectIndex = index
    },
    getclassifDetail(id) {
      this.$loading()
      this.$API.SupplierPunishment.getclassificationDetail({ id }).then((res) => {
        this.$hloading()
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.info = res.data
          this.canEdit =
            res.data.infoDTO.applyStatus === 20 || res.data.infoDTO.applyStatus === 40
              ? false
              : true
        } else {
          this.$toast({
            content: this.$t('获取共享详情失败，请重试!'),
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss">
.mt-tabs {
  margin: 10px 0;
  width: 100%;
}
</style>
<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.supplierDetall-container {
  height: 100%;
  padding-top: 20px;
  // min-width: 1200px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .titles-box {
      width: 100%;
      flex-direction: column;
      justify-content: space-between;

      .mian-line {
        width: 100%;
        justify-content: space-between;
      }

      .mian-title {
        font-size: 20px;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }

      .scroll-box {
        width: 100%;
        display: flex;
        white-space: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        margin-top: 20px;

        .normal-title-box {
          height: 88px;
          background: rgba(255, 255, 255, 1);
          border-radius: 8px;
          font-size: 14px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
          padding: 0 20px;
        }

        .gong-title {
          padding: 20px 0 0 0;
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
        }

        .gong-id {
          padding: 20px 0 0 0;
          font-size: 14px;
          font-weight: 600;
          color: rgba(0, 70, 156, 1);
        }
      }

      .sub-title {
        width: 100%;
        font-size: 12px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        margin-top: 10px;
        .normal-title {
          font-size: 12px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
        }
        .normal-title-dan {
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
          font-weight: 600;

          span {
            font-weight: 600;
          }

          .b-color {
            color: #00469c;
          }
        }
        .normal-title-box {
          width: 298px;
          height: 88px;
          background: rgba(255, 255, 255, 1);
          border-radius: 8px;
          font-size: 14px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
        }
      }
      .mr-20 {
        margin-top: 20px;
      }
    }

    .btns-box {
      align-items: center;
      font-size: 14px;
      max-width: 300px;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      justify-content: flex-end;

      .invite-btn {
        margin-right: 20px;
        cursor: pointer;
        font-weight: 600;
      }
      .invite-btn:last-child {
        margin-right: 0;
      }
    }
    .gong-title {
      padding: 20px 0 0 20px;
      font-size: 14px;

      font-weight: normal;
      color: rgba(41, 41, 41, 1);
    }
    .gong-id {
      padding: 20px 0 0 20px;
      font-size: 14px;

      font-weight: 600;
      color: rgba(0, 70, 156, 1);
    }
  }
}
</style>
