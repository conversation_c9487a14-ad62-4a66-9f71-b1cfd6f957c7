import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import { applicationformtype, statsea, blackState } from '@/utils/setting'
import Vue from 'vue'
// 手动准入升降级

export const areaColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '100',
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    headerTextAlign: 'center'
  },
  {
    width: '100',
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称'),
    headerTextAlign: 'center'
  }
]
export const supplyAreaToolbar = [
  // {
  //   id: "addNew",
  //   icon: "icon_solid_Newinvitation",
  //   title: i18n.t("新增"),
  // },
  {
    id: 'deleteList',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]
export const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('申请单编号'),
    headerTextAlign: 'center',
    width: '180',
    cellTools: [
      {
        id: 'editUpDown',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['applyStatus'] !== 20 && data['applyStatus'] !== 40
        }
      },
      { id: 'Deleted', icon: 'icon_solid_Delete1', title: i18n.t('删除') }
    ]
  },
  {
    field: 'applyName',
    width: '150',
    headerText: i18n.t('申请单名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'relationDTOList',
    width: '300',
    headerText: i18n.t('供应商清单'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('relationDTOList', {
          template: `
              <div class="approve-config-box fbox">
              <div v-if="relationDTOList[0].supplierEnterpriseName" class="flex1">{{relationDTOList[0].supplierEnterpriseName}}</div>

              <mt-tooltip :content="content" position="TopCenter">
                <i style="font-size:16px; color:#6386C1" class="mt-icons mt-icon-icon_outline_prompt" ></i>
              </mt-tooltip>
              </div>`,
          data: function () {
            return { data: {}, content: '' }
          },
          computed: {
            relationDTOList() {
              return this.data.relationDTOList
            }
          },
          mounted() {
            this.getSupplier()
          },
          methods: {
            getSupplier() {
              this.data.relationDTOList.forEach((item) => {
                this.content = this.content + `<div>${item.supplierEnterpriseName} </div>`
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'sourceStageTemplateName',
    width: '120',
    headerText: i18n.t('当前阶段'),
    headerTextAlign: 'center'
  },
  {
    field: 'destinationStageTemplateName',
    width: '120',
    headerText: i18n.t('目标阶段'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgCode',
    width: '150',
    headerText: i18n.t('公司编码'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgName',
    width: '150',
    headerText: i18n.t('公司名称'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.applyStatus] }}</span>

          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: statsea
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('申请人'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyerDeptName',
    width: '100',
    headerText: i18n.t('申请人部门'),
    headerTextAlign: 'center'
  },
  {
    field: 'createDate',
    width: '150',
    headerText: i18n.t('创建日期'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = this.data.createDate.replace(/T/gi, ' ')
              timeStr = timeStr.replace(/\.000\+0000/gi, '')
              return timeStr
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]
// 供应商共享
export const columnShare = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('申请单编号'),
    headerTextAlign: 'center',
    width: '180',

    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['applyStatus'] !== 20 && data['applyStatus'] !== 40
        }
      },
      // "edit",
      'delete'
    ]
  },
  {
    field: 'applyName',
    width: '120',
    headerText: i18n.t('申请单名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'relationDTOList',
    width: '300',
    headerText: i18n.t('供应商'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('relationDTOList', {
          template: `
              <div class="approve-config-box fbox">
              <div v-if="relationDTOList[0].supplierEnterpriseName" class="flex1">{{relationDTOList[0].supplierEnterpriseName}}</div>

              <mt-tooltip :content="content" position="TopCenter">
                <i style="font-size:16px; color:#6386C1" class="mt-icons mt-icon-icon_outline_prompt" ></i>
              </mt-tooltip>
              </div>`,
          data: function () {
            return { data: {}, content: '' }
          },
          computed: {
            relationDTOList() {
              return this.data.relationDTOList
            }
          },
          mounted() {
            this.getSupplier()
          },
          methods: {
            getSupplier() {
              this.data.relationDTOList.forEach((item) => {
                this.content = this.content + `<div>${item.supplierEnterpriseName} </div>`
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'sourceOrgCode',
    width: '120',
    headerText: i18n.t('源组织编码'),
    headerTextAlign: 'center'
  },
  {
    field: 'sourceOrgName',
    width: '120',
    headerText: i18n.t('源组织名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'destinationOrgCode',
    width: '150',
    headerText: i18n.t('目标组织编码'),
    headerTextAlign: 'center'
  },
  {
    field: 'destinationOrgName',
    width: '150',
    headerText: i18n.t('目标组织名称'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.applyStatus] }}</span>

          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: statsea
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('申请人'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyerDeptName',
    width: '180',
    headerText: i18n.t('申请人部门'),
    headerTextAlign: 'center'
  },
  {
    field: 'createDate',
    width: '250',
    headerText: i18n.t('申请日期'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = this.data.createDate.replace(/T/gi, ' ')
              timeStr = timeStr.replace(/\.000\+0000/gi, '')
              return timeStr
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]
//供应商分级
export const applicationforme = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('申请单编号'),
    headerTextAlign: 'center',
    width: '180',
    cellTools: [
      {
        id: 'editL',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['applyStatus'] !== 20 && data['applyStatus'] !== 40
        }
      },
      { id: 'deleteL', icon: 'icon_solid_Delete1', title: i18n.t('删除') }
    ]
  },
  {
    field: 'applyName',
    width: '120',
    headerText: i18n.t('申请单名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'relationDTOList',
    width: '300',
    headerText: i18n.t('供应商'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('relationDTOList', {
          template: `
              <div class="approve-config-box fbox" v-if="!!relationDTOList && !!relationDTOList[0]">
              <div v-if="!!relationDTOList && !!relationDTOList[0] && relationDTOList[0].supplierEnterpriseName" class="flex1">{{relationDTOList[0].supplierEnterpriseName}}</div>

              <mt-tooltip :content="content" position="TopCenter">
                <i style="font-size:16px; color:#6386C1" class="mt-icons mt-icon-icon_outline_prompt" ></i>
              </mt-tooltip>
              </div>`,
          data: function () {
            return { data: {}, content: '' }
          },
          computed: {
            relationDTOList() {
              return this.data.relationDTOList
            }
          },
          mounted() {
            this.getSupplier()
          },
          methods: {
            getSupplier() {
              this.data.relationDTOList.forEach((item) => {
                this.content = this.content + `<div>${item.supplierEnterpriseName} </div>`
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'gradeType',
    width: '120',
    headerText: i18n.t('申请级别'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgDimension',
    width: '150',
    headerText: i18n.t('组织维度'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgName',
    width: '150',
    headerText: i18n.t('组织名称'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.applyStatus] }}</span>

          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: statsea
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('申请人'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyerDeptName',
    width: '180',
    headerText: i18n.t('申请人部门'),
    headerTextAlign: 'center'
  },
  {
    field: 'createDate',
    width: '250',
    headerText: i18n.t('申请日期'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = utils.formateTime(Number(this.data.createDate), 'yyyy-MM-dd hh:mm:ss')
              return timeStr
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]
//惩罚和淘汰申请
export const punishmentforme = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('申请单编号'),
    headerTextAlign: 'center',
    width: '180',
    cellTools: [
      {
        id: 'editPunishment',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['applyStatus'] !== 20 && data['applyStatus'] !== 40
        }
      },
      { id: 'Deleteb', icon: 'icon_solid_Delete1', title: i18n.t('删除') }
    ]
  },
  {
    field: 'applyName',
    width: '120',
    headerText: i18n.t('申请单名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'businessType',
    width: '120',
    headerText: i18n.t('申请单类型'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'map',
      map: applicationformtype
    }
  },
  {
    field: 'violationType',
    width: '120',
    headerText: i18n.t('原因类型'),
    headerTextAlign: 'center'
  },
  {
    field: 'relationDTOList',
    width: '300',
    headerText: i18n.t('供应商'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('relationDTOList', {
          template: `
              <div class="approve-config-box fbox" v-if="!!relationDTOList && !!relationDTOList[0]">
                <div v-if="!!relationDTOList && !!relationDTOList[0] && relationDTOList[0].supplierEnterpriseName" class="flex1">{{relationDTOList[0].supplierEnterpriseName}}</div>

                <mt-tooltip :content="content" position="TopCenter">
                  <i style="font-size:16px; color:#6386C1" class="mt-icons mt-icon-icon_outline_prompt" ></i>
                </mt-tooltip>
              </div>`,
          data: function () {
            return { data: {}, content: '' }
          },
          computed: {
            relationDTOList() {
              return this.data.relationDTOList
            }
          },
          mounted() {
            this.getSupplier()
          },
          methods: {
            getSupplier() {
              this.data.relationDTOList.forEach((item) => {
                this.content = this.content + `<div>${item.supplierEnterpriseName} </div>`
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'penaltyPeriodStart',
    width: '150',
    headerText: i18n.t('处罚开始'),
    headerTextAlign: 'center'
  },
  {
    field: 'penaltyPeriodEnd',
    width: '150',
    headerText: i18n.t('处罚结束'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.applyStatus] }}</span>

          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: statsea
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('申请人'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyerDeptName',
    width: '180',
    headerText: i18n.t('申请人部门'),
    headerTextAlign: 'center'
  },
  {
    field: 'createDate',
    width: '250',
    headerText: i18n.t('申请日期'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = utils.formateTime(Number(this.data.createDate), 'yyyy-MM-dd hh:mm:ss')
              return timeStr
            }
          }
        })
      }
    }
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]

export const pageConfig = (url) => [
  {
    toolbar,
    grid: {
      asyncConfig: {
        url
      }
    }
  }
]

//惩罚解除和启用申请
export const punishmentRelieve = (punishCauseTypeList) => {
  // let punishCauseTypeList = await getDictItem
  // console.log(punishCauseTypeList)

  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'applyCode',
      headerText: i18n.t('申请单编号'),
      headerTextAlign: 'center',
      width: '180',
      cellTools: [
        {
          id: 'editPunishment',
          icon: 'icon_Editor',
          title: i18n.t('编辑'),
          visibleCondition: (data) => {
            return data['applyStatus'] !== 20 && data['applyStatus'] !== 40
          }
        },
        { id: 'Deleteb', icon: 'icon_solid_Delete1', title: i18n.t('删除') }
      ]
    },
    {
      field: 'applyName',
      width: '120',
      headerText: i18n.t('申请单名称'),
      headerTextAlign: 'center'
    },
    {
      field: 'businessType',
      width: '120',
      headerText: i18n.t('申请单类型'),
      headerTextAlign: 'center',
      valueConverter: {
        type: 'map',
        map: applicationformtype
      }
    },
    {
      field: 'violationType',
      width: '120',
      headerText: i18n.t('原因类型'),
      headerTextAlign: 'center',
      valueConverter: {
        type: 'function',
        filter: (e) => {
          // console.log(e, punishCauseTypeList);
          if (
            !!e &&
            punishCauseTypeList.length > 0 &&
            punishCauseTypeList.filter((v) => v.itemCode + '' === e).length > 0
          ) {
            return punishCauseTypeList.filter((v) => v.itemCode + '' === e)[0]['name']
          } else {
            return '--'
          }
        }
      }
    },
    {
      field: 'relationDTOList',
      width: '300',
      headerText: i18n.t('供应商'),
      headerTextAlign: 'center',
      template: () => {
        return {
          template: Vue.component('relationDTOList', {
            template: `
                <div class="approve-config-box fbox" v-if="!!relationDTOList && !!relationDTOList[0]">
                  <div v-if="relationDTOList[0].supplierEnterpriseName" class="flex1">{{relationDTOList[0].supplierEnterpriseName}}</div>

                  <mt-tooltip :content="content" position="TopCenter">
                    <i style="font-size:16px; color:#6386C1" class="mt-icons mt-icon-icon_outline_prompt" ></i>
                  </mt-tooltip>
                </div>`,
            data: function () {
              return { data: {}, content: '' }
            },
            computed: {
              relationDTOList() {
                return this.data.relationDTOList
              }
            },
            mounted() {
              this.getSupplier()
            },
            methods: {
              getSupplier() {
                this.data.relationDTOList.forEach((item) => {
                  this.content = this.content + `<div>${item.supplierEnterpriseName} </div>`
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'penaltyPeriodStart',
      width: '150',
      headerText: i18n.t('处罚开始'),
      headerTextAlign: 'center'
      // valueConverter: { type: "date", format: "YYYY-MM-DD HH:mm:ss" },
    },
    {
      field: 'penaltyPeriodEnd',
      width: '150',
      headerText: i18n.t('处罚结束'),
      headerTextAlign: 'center'
    },
    {
      width: '110',
      field: 'applyStatus',
      headerText: i18n.t('状态'),
      headerTextAlign: 'center',
      template: () => {
        return {
          template: Vue.component('statusTemplate', {
            template: `<div class="status-wrap" style="">
            <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(99,134,193,0.1);
              color: #6386C1;
            " >{{ statusMap[data.applyStatus] }}</span>
            <span v-if="data.applyStatus == 30" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(237,86,51,.1);
              color: #ED5633;
            " >{{ statusMap[data.applyStatus] }}</span>
            <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(154,154,154,.1);
              color: #9A9A9A;
            " >{{ statusMap[data.applyStatus] }}</span>

            </div>`,
            data: function () {
              return {
                data: {},
                statusMap: statsea
              }
            }
          })
        }
      }
    },
    {
      field: 'applyerName',
      width: '100',
      headerText: i18n.t('申请人'),
      headerTextAlign: 'center'
    },
    {
      field: 'applyerDeptName',
      width: '180',
      headerText: i18n.t('申请人部门'),
      headerTextAlign: 'center'
    },
    {
      field: 'createDate',
      width: '250',
      headerText: i18n.t('申请日期'),
      headerTextAlign: 'center',
      template: () => {
        return {
          template: Vue.component('time-tmp', {
            template: `
                <div class="time-box">
                  {{ timeStr }}
                </div>`,
            data: function () {
              return { data: {}, time: '' }
            },
            computed: {
              timeStr() {
                let timeStr = utils.formateTime(Number(this.data.createDate), 'yyyy-MM-dd hh:mm:ss')
                return timeStr
              }
            }
          })
        }
      }
    },
    {
      field: 'remark',
      width: '180',
      headerText: i18n.t('备注'),
      headerTextAlign: 'center'
    }
  ]
}
export const punishmentformeg = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    isrelieve: 'relieve',
    headerText: i18n.t('申请单编号'),
    headerTextAlign: 'center',
    width: '180',
    cellTools: [
      {
        id: 'editRelieve',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['applyStatus'] !== 20 && data['applyStatus'] !== 40
        }
      },
      { id: 'Deletec', icon: 'icon_solid_Delete1', title: i18n.t('删除') }
    ]
  },
  {
    field: 'applyName',
    width: '120',
    headerText: i18n.t('申请单名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'businessType',
    width: '120',
    headerText: i18n.t('申请单类型'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'map',
      map: applicationformtype
    }
  },
  {
    field: 'relationDTOList',
    width: '300',
    headerText: i18n.t('供应商'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('relationDTOList', {
          template: `
              <div class="approve-config-box fbox">
              <div v-if="relationDTOList[0].supplierEnterpriseName" class="flex1">{{relationDTOList[0].supplierEnterpriseName}}</div>

              <mt-tooltip :content="content" position="TopCenter">
                <i style="font-size:16px; color:#6386C1" class="mt-icons mt-icon-icon_outline_prompt" ></i>
              </mt-tooltip>
              </div>`,
          data: function () {
            return { data: {}, content: '' }
          },
          computed: {
            relationDTOList() {
              return this.data.relationDTOList
            }
          },
          mounted() {
            this.getSupplier()
          },
          methods: {
            getSupplier() {
              this.data.relationDTOList.forEach((item) => {
                this.content = this.content + `<div>${item.supplierEnterpriseName || '--'} </div>`
              })
            }
          }
        })
      }
    }
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.applyStatus] }}</span>

          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: statsea
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('申请人'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyerDeptName',
    width: '180',
    headerText: i18n.t('申请人部门'),
    headerTextAlign: 'center'
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  },
  {
    field: 'createDate',
    width: '250',
    headerText: i18n.t('申请日期'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = utils.formateTime(Number(this.data.createDate), 'yyyy-MM-dd hh:mm:ss')
              return timeStr
            }
          }
        })
      }
    }
  }
]
export const punishmentformess = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    isrelieve: 'relieve',
    headerText: i18n.t('申请单编号'),
    headerTextAlign: 'center',
    width: '180',
    cellTools: []
  },
  {
    field: 'applyName',
    width: '120',
    headerText: i18n.t('申请单名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'businessType',
    width: '120',
    headerText: i18n.t('申请单类型'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'map',
      map: applicationformtype
    }
  },
  {
    field: 'relationDTOList',
    width: '300',
    headerText: i18n.t('供应商'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('relationDTOList', {
          template: `
              <div class="approve-config-box fbox">
              <div v-if="relationDTOList[0].supplierEnterpriseName" class="flex1">{{relationDTOList[0].supplierEnterpriseName}}</div>

              <mt-tooltip :content="content" position="TopCenter">
                <i style="font-size:16px; color:#6386C1" class="mt-icons mt-icon-icon_outline_prompt" ></i>
              </mt-tooltip>
              </div>`,
          data: function () {
            return { data: {}, content: '' }
          },
          computed: {
            relationDTOList() {
              return this.data.relationDTOList
            }
          },
          mounted() {
            this.getSupplier()
          },
          methods: {
            getSupplier() {
              this.data.relationDTOList.forEach((item) => {
                this.content = this.content + `<div>${item.supplierEnterpriseName} </div>`
              })
            }
          }
        })
      }
    }
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.applyStatus] }}</span>
          <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.applyStatus] }}</span>

          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: statsea
            }
          }
        })
      }
    }
  },
  // {
  //   field: "applyerName",
  //   width: "100",
  //   headerText: i18n.t("申请人"),
  // },
  // {
  //   field: "applyerDeptName",
  //   width: "180",
  //   headerText: i18n.t("申请人部门"),
  // },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  },
  {
    field: 'createDate',
    width: '250',
    headerText: i18n.t('申请日期'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `
              <div class="time-box">
                {{ timeStr }}
              </div>`,
          data: function () {
            return { data: {}, time: '' }
          },
          computed: {
            timeStr() {
              let timeStr = utils.formateTime(Number(this.data.createDate), 'yyyy-MM-dd hh:mm:ss')
              return timeStr
            }
          }
        })
      }
    }
  }
]
//已处罚列表
export const punishedforme = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    headerTextAlign: 'center',
    width: '120'
  },
  {
    field: 'supplierEnterpriseName',
    width: '200',
    headerText: i18n.t('供应商名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'businessType',
    width: '120',
    headerText: i18n.t('处罚类型'),
    headerTextAlign: 'center',
    valueConverter: {
      type: 'map',
      map: applicationformtype
    }
  },
  {
    field: 'applyerOrgCode',
    width: '150',
    headerText: i18n.t('公司编码'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.status == 10 || data.status == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.status] }}</span>
          <span v-if="data.status == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.status] }}</span>
          <span v-if="data.status == 40 || data.status == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.status] }}</span>
          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: blackState
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerOrgName',
    width: '180',
    headerText: i18n.t('公司名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'penaltyPeriodStart',
    width: '80',
    headerText: i18n.t('惩罚开始时间'),
    headerTextAlign: 'center'
  },
  {
    field: 'penaltyPeriodEnd',
    width: '80',
    headerText: i18n.t('惩罚结束时间'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyerName',
    width: '80',
    headerText: i18n.t('申请人'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyerDeptName',
    width: '180',
    headerText: i18n.t('申请人部门'),
    headerTextAlign: 'center'
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  },
  {
    field: 'applyTime',
    width: '100',
    headerText: i18n.t('申请日期'),
    headerTextAlign: 'center',
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  }
]
// 已淘汰列表
export const punishedformeg = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'supplierEnterpriseCode',
    headerText: i18n.t('供应商编码'),
    headerTextAlign: 'center',
    width: '120'
  },
  {
    field: 'supplierEnterpriseName',
    width: '180',
    headerText: i18n.t('供应商名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'supplierType',
    width: '180',
    headerText: i18n.t('级别'),
    headerTextAlign: 'center'
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

          <span v-if="data.status == 10 || data.status == 20" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(99,134,193,0.1);
            color: #6386C1;
          " >{{ statusMap[data.status] }}</span>
          <span v-if="data.status == 30" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(237,86,51,.1);
            color: #ED5633;
          " >{{ statusMap[data.status] }}</span>
          <span v-if="data.status == 40 || data.status == 50" style="
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(154,154,154,.1);
            color: #9A9A9A;
          " >{{ statusMap[data.status] }}</span>
          </div>`,
          data: function () {
            return {
              data: {},
              statusMap: blackState
            }
          }
        })
      }
    }
  },
  {
    field: 'orgDimension',
    width: '180',
    headerText: i18n.t('组织维度'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgCode',
    width: '120',
    headerText: i18n.t('组织编码'),
    headerTextAlign: 'center'
  },
  {
    field: 'orgName',
    width: '180',
    headerText: i18n.t('组织名称'),
    headerTextAlign: 'center'
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注'),
    headerTextAlign: 'center'
  }
]
// 附件
export const enclosurea = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    headerText: i18n.t('附件名称'),
    headerTextAlign: 'center',
    width: '180',
    cellTools: []
  },
  {
    field: 'fileSize',
    width: '120',
    headerText: i18n.t('附件大小'),
    headerTextAlign: 'center'
  },
  {
    field: 'updateUserName',
    width: '120',
    headerText: i18n.t('上传人'),
    headerTextAlign: 'center'
  },
  {
    field: 'modifyDate',
    width: '120',
    headerText: i18n.t('上传时间'),
    headerTextAlign: 'center',
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  },
  {
    field: 'remark',
    width: '180',
    headerTextAlign: 'center',
    headerText: i18n.t('备注')
  }
]
