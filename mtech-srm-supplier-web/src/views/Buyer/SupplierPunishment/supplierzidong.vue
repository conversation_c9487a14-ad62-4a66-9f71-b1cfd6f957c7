<template>
  <div class="supplierDetall-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="titles-box">
        <div class="mian-line fbox">
          <div class="mian-title">
            <template v-if="!!info && !!info.applyInfo">
              {{ applyInfo.applyName || '--' }}
            </template>
          </div>
          <div class="btns-box fbox">
            <div class="invite-btn" @click="onBack">
              <mt-button css-class="e-info">{{ $t('返回') }}</mt-button>
            </div>
            <template v-if="canEdit">
              <div class="invite-btn" @click="onSave">
                <mt-button css-class="e-info">{{ $t('保存') }}</mt-button>
              </div>
              <div class="invite-btn" @click="onSaveAndPush">
                <mt-button css-class="e-info">{{ $t('保存并提交') }}</mt-button>
              </div>
            </template>
          </div>
        </div>
        <div class="sub-title fbox">
          <div class="normal-title">{{ $t('申请单编码') }}：{{ applyInfo.applyCode }}</div>
          <div class="normal-title">{{ $t('创建人') }}：{{ applyInfo.createUserName }}</div>
          <div class="normal-title" v-if="!!applyInfo.createUserName">
            {{ $t('创建时间') }}： {{ applyInfo.createTime }}
          </div>
          <div class="normal-title">{{ $t('申请人') }}：{{ applyInfo.applyerName }}</div>
          <div class="normal-title" v-if="!!applyInfo.applyerDeptName">
            {{ $t('创建时间') }}： {{ applyInfo.applyerDeptName }}
          </div>
        </div>
        <div class="sub-title fbox mr20">
          <div class="normal-title-dan" v-if="!!info && !!info.applyStage">
            <span>{{ $t('类型') }}：</span>{{ this.dictList[info.applyStage.businessType] }}
          </div>
          <div class="normal-title-dan" v-if="!!info && !!applyInfo">
            <span>{{ $t('组织') }}</span
            >：{{ !!applyInfo.applyerOrgName ? applyInfo.applyerOrgName : '--' }}
            <span class="b-color">{{ applyInfo.applyCode || '--' }}</span>
          </div>
          <div class="normal-title-dan" v-if="!!info && !!info.applyStage">
            <span>{{ $t('当前阶段') }}：</span>{{ info.applyStage.sourceStageTemplateName || '--' }}
          </div>
          <div class="normal-title-dan" v-if="!!info && !!info.applyStage">
            <span>{{ $t('目标阶段') }}：</span>{{ info.applyStage.destinationStageTemplateName }}
          </div>
        </div>
        <div class="scroll-box fbox">
          <div class="normal-title-box" v-for="(item, index) in relationList" :key="index">
            <div class="gong-title">{{ item.supplierEnterpriseName || '--' }}</div>
            <div class="gong-id">{{ item.supplierEnterpriseCode || '--' }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 头部结束 -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <keep-alive>
      <detail-edit
        v-if="selectIndex === 0"
        :id="id"
        :can-edit="canEdit"
        :apply-info="info.applyInfo"
        ref="detailEdit"
      ></detail-edit>
      <operator-history
        v-if="selectIndex === 1"
        :id="id"
        :apply-info="info.applyInfo"
      ></operator-history>
    </keep-alive>
  </div>
</template>

<script>
import operatorHistory from './components/operatorHistory.vue'
import detailEdit from './components/detailEdit.vue'
import utils from '../../../utils/utils'
export default {
  components: {
    detailEdit,
    operatorHistory
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('单据配置')
        },
        {
          title: this.$t('操作记录')
        }
      ],
      id: '',
      info: {
        applyInfo: {},
        applyStage: {},
        relationList: []
      },
      relationList: [],
      canEdit: false, //  item.applyStatus === 20 || item.applyStatus === 40 不能编辑
      dictList: []
    }
  },
  computed: {
    applyInfo() {
      return !!this.info && this.info.applyInfo ? this.info.applyInfo : {}
    }
  },
  created() {
    let id = this.$route.query.id
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getDetail(id)
    this.getDictItem()
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    handleSelectTab(index) {
      this.selectIndex = index
    },
    onSave() {
      this.info.applyInfo.applyReason = this.$refs.detailEdit.$refs.MtRichTextEditor.value
      this.confirm()
    },
    onSaveAndPush() {
      this.info.applyInfo.applyReason = this.$refs.detailEdit.$refs.MtRichTextEditor.value
      this.confirmSubmit()
    }, // 提交
    confirm(fn) {
      this.$loading()
      this.$API.SupplierPunishment.upDownUpdate(this.info)
        .then((res) => {
          this.$hloading()
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            if (!!fn && typeof fn === 'function') {
              fn()
            } else {
              this.$emit('confirm-function', 'reload')
            }
          } else {
            this.$toast({
              content: this.$t('保存失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('修改失败，请重试!'),
            type: 'warning'
          })
        })
    },

    confirmSubmit() {
      this.confirm(() => {
        this.$loading()
        this.$API.SupplierPunishment.applySubmit({
          applyIdList: [this.info.applyInfo.id]
        }).then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('提交升降级成功!'),
              type: 'success'
            })
            this.getDetail(this.id)
          } else {
            this.$toast({
              content: this.$t('提交升降级失败，请重试!'),
              type: 'warning'
            })
          }
        })
      })
    },

    getDictItem() {
      let dictCode = 'stageType'
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            res.data.forEach((item) => {
              this.dictList[`${item.itemCode}`] = item.name
            })
          }
        })
        .catch(() => {})
    },
    getDetail(id) {
      this.$loading()
      this.$API.SupplierPunishment.getzidongfenjiDetail({ id }).then((res) => {
        this.$hloading()
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.canEdit =
            res.data.applyInfo.applyStatus === 20 || res.data.applyInfo.applyStatus === 40
              ? false
              : true
          this.info = res.data
          this.relationList = res.data.relationList
          this.$nextTick(() => {
            this.$refs.detailEdit.$refs.MtRichTextEditor.value = this.info.applyInfo.applyReason
          })
        } else {
          this.$toast({
            content: this.$t('获取详情失败，请重试!'),
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss">
.mt-tabs {
  margin: 10px 0;
  width: 100%;
}
</style>
<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.supplierDetall-container {
  height: 100%;
  padding-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .titles-box {
      width: 100%;
      flex-direction: column;
      justify-content: space-between;
      .mian-title {
        font-size: 20px;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }

      .mian-line {
        width: 100%;
        justify-content: space-between;
      }

      .scroll-box {
        width: 100%;
        display: flex;
        white-space: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        margin-top: 20px;

        .normal-title-box {
          height: 88px;
          background: rgba(255, 255, 255, 1);
          border-radius: 8px;
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
          padding: 0 20px;
        }

        .gong-title {
          padding: 20px 0 0 0;
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
        }

        .gong-id {
          padding: 20px 0 0 0;
          font-size: 14px;
          font-weight: 600;
          color: rgba(0, 70, 156, 1);
        }
      }

      .sub-title {
        width: 100%;
        font-size: 12px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        margin-top: 10px;

        .normal-title {
          font-size: 12px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
        }
        .normal-title-dan {
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;

          span {
            font-weight: 600;
          }

          .b-color {
            color: #00469c;
          }
        }
        .normal-title-box {
          width: 298px;
          height: 88px;
          background: rgba(255, 255, 255, 1);
          border-radius: 8px;
          font-size: 14px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
        }
      }
      .mr20 {
        margin-top: 20px;
      }
    }

    .btns-box {
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      justify-content: flex-end;
      max-width: 300px;
      .invite-btn {
        margin-right: 20px;
        cursor: pointer;
        font-weight: 600;
      }
      .invite-btn:last-child {
        margin-right: 0;
      }
    }
    .gong-title {
      padding: 20px 0 0 20px;
      font-size: 14px;

      font-weight: normal;
      color: rgba(41, 41, 41, 1);
    }
    .gong-id {
      padding: 20px 0 0 20px;
      font-size: 14px;

      font-weight: 600;
      color: rgba(0, 70, 156, 1);
    }
  }
}
.mt-tabs {
  margin-left: 30px;
}
</style>
