<template>
  <div class="invitation-detail">
    <div class="invitation-header">
      <div class="iv-title ellipsis">{{ ids }}</div>
      <div class="iv-tip ellipsis">
        {{ $t('共计') }} {{ !!dataArr && dataArr.length > 0 ? dataArr.length : 0 }}
        {{ $t('张邀请单') }}
      </div>

      <div class="form-wrap">
        <mt-form ref="ruleForm" :model="ruleForm" :rules="inviteRules">
          <mt-row :gutter="20">
            <mt-col :span="8">
              <mt-form-item prop="listName" class="form-item" :label="$t('邀请单名称')">
                <mt-input
                  v-model="ruleForm.listName"
                  :disabled="false"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入邀请单名称')"
                ></mt-input>
              </mt-form-item>
            </mt-col>

            <mt-col :span="8">
              <mt-form-item prop="Emailmodule" class="form-item" :label="$t('邮件模板')">
                <mt-select
                  v-model="ruleForm.Emailmodule"
                  :data-source="emailArr"
                  :show-clear-button="true"
                  :placeholder="$t('请输入邮件模板')"
                ></mt-select>
              </mt-form-item>
            </mt-col>

            <mt-col :span="8">
              <mt-form-item prop="ivReason" class="form-item" :label="$t('邀请原因')">
                <mt-input
                  v-model="ruleForm.ivReason"
                  :disabled="false"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入邀请原因')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>
    </div>
    <div class="invite-btn-box">
      <div class="invite-btn" @click="setInvites">{{ $t('发送邀请') }}</div>
    </div>
    <div class="invitation-content"></div>
  </div>
</template>

<script>
const rules = {
  listName: [
    {
      required: true,
      message: this.$t('请输入邀请单名称'),
      trigger: 'blur'
    }
  ],
  Emailmodule: [
    {
      required: true,
      message: this.$t('请输入邮件模板'),
      trigger: 'blur'
    }
  ],
  ivReason: [
    {
      required: false,
      message: this.$t('请输入邀请原因'),
      trigger: 'blur'
    }
  ]
}

import utils from '@/utils/utils'

export default {
  data() {
    return {
      ruleForm: {
        listName: '',
        Emailmodule: '',
        ivReason: ''
      },
      // 邮件模板应该是接口返回的
      emailArr: [{ text: this.$t('默认模板'), value: 'message20210721-a382' }],
      dataArr: [],
      ids: '',
      currentTabIndex: 0,
      tabPageInfo: {
        size: 10,
        current: 1
      }
    }
  },
  computed: {
    inviteRules() {
      return rules
    }
  },
  methods: {
    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      console.log(e, e.gridRef.getMtechGridRecords())

      // 新增
      if (e.toolbar.id === 'addNew') {
        this.addPopGrid()
      }
      if (e.toolbar.id === 'deleteList') {
        let selectArr = e.gridRef.getMtechGridRecords()
        let dataTmp = this.pageConfig[0].grid.dataSource
        let finalArr = []
        dataTmp.forEach((v) => {
          if (selectArr.filter((vc) => vc.id === v.id).length === 0) {
            finalArr.push(v)
          }
        })
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('是否确认删除所选邀请单？')
          },
          success: () => {
            this.$set(this.pageConfig[0].grid, 'dataSource', finalArr)
            // 获取供应范围
            this.getAreas(finalArr)
          }
        })
      }
    },

    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },

    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      let { data } = e
      if (e.tool.id == 'delete') {
        //删除操作
        this.deleteInvitation(data)
      }
    },

    // 删除申请
    deleteInvitation(data) {
      let dataTmp = this.pageConfig[0].grid.dataSource
      let index = dataTmp.findIndex((v) => v.id === data.id)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选邀请单？')
        },
        success: () => {
          dataTmp.splice(index, 1)
          this.$set(this.pageConfig[0].grid, 'dataSource', JSON.parse(JSON.stringify(dataTmp)))
          this.getAreas(dataTmp)
        }
      })
    },

    // 刷新页面
    handleRefresh() {
      this.tabPageInfo.current = 1
    },

    // checkbox
    handleChangeCellCheckBox(a) {
      // 单元格,Checkbox change事件
      console.log('use-handleChangeCellCheckBox', a)
    },

    // 弹框添加列表页面未邀请的list
    // addPopGrid() {
    //   this.$dialog({
    //     modal: () => import("../components/popList.vue"),
    //     data: {
    //       title: this.$t("新增邀请"),
    //       inviteArr: this.pageConfig[0].grid.dataSource  || []
    //     },
    //     success: (data) => {
    //       console.log(data)
    //       if(!data.inviteData || data.inviteData.length === 0) {
    //         // 没有新增勾选
    //         return
    //       }
    //       let tmpData = this.pageConfig[0].grid.dataSource;
    //       let finalData = [].concat(tmpData, data.inviteData);
    //       this.$set(this.pageConfig[0].grid, "dataSource", finalData);
    //       // 供应范围
    //       this.$set(this.pageConfig[1].grid, "dataSource", data.areaData);

    //       // 保存并邀请
    //       if (data.mode === "confirmAndInvite") {
    //         this.setInvites()
    //       }
    //     },
    //   });
    // },

    // 获取邮件模板
    getEmailList() {
      this.$API.supplierInvitation
        .getMessageList({
          enabled: 0,
          pageNo: 1,
          pageSize: 10
        })
        .then((res) => {
          if (!utils.isEmpty(res.data) && !utils.isEmpty(res.data.list)) {
            this.emailArr = res.data.list.map((result) => {
              return {
                text: result.templateName,
                value: result.templateCode,
                ...result
              }
            })
          }
        })
    },

    /**
     * 邀请
     */
    setInvites() {
      let allData = this.pageConfig[0].grid.dataSource
      if (!allData || allData.length === 0) {
        this.$toast({
          content: this.$t('请选择数据！'),
          type: 'warning'
        })
        return
      }
      let ids = allData.map((v) => v.id)

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$dialog({
            data: {
              title: this.$t('邀请'),
              message: this.$t('是否确定邀请所选供应商？'),
              confirm: () =>
                this.$API.supplierInvitation['sendInvitation']({
                  idList: ids,
                  invitationName: this.ruleForm.listName,
                  invitationReason: this.ruleForm.ivReason,
                  templateCode: this.ruleForm.Emailmodule
                })
            },
            success: (data) => {
              console.log(data)
              if (data.code === 200) {
                this.$toast({
                  content: this.$t('提交成功'),
                  type: 'success'
                })

                sessionStorage.setItem('reload', 'reload')
                setTimeout(() => {
                  this.$router.replace({
                    name: 'suppliermanage'
                  })
                }, 600)
              } else {
                this.$toast({
                  content: this.$t('提交邀请失败，请重试！'),
                  type: 'warning'
                })
              }
            }
          })
        }
      })
    },

    // 获取供应范围
    getAreas(dataArr) {
      let idArr = dataArr.map((v) => v.id)
      this.$API.supplierInvitation['sendInviteExtList']({
        inviteIdList: idArr
      }).then((result) => {
        if (result.code === 200 && !utils.isEmpty(result.data)) {
          // 供应范围
          this.$set(this.pageConfig[1].grid, 'dataSource', result.data)
        } else {
          this.$toast({
            content: result.msg || this.$t('获取数据失败，请重试！'),
            type: 'warning'
          })
        }
      })
    }
  },
  created() {
    let dataArr = []
    dataArr = this.$parent.inviteDatas

    if (!dataArr || dataArr.length === 0) {
      this.$toast({
        content: this.$t('获取数据失败，请重试！'),
        type: 'warning'
      })
      setTimeout(() => {
        this.$router.go(-1)
      }, 600)
    }

    let ids = this.$route.query.ids
    this.ids = ids
    this.dataArr = dataArr
    this.$set(this.pageConfig[0].grid, 'dataSource', dataArr)

    // 获取邮件列表
    this.getEmailList()
    // 接口获取供应返回
    this.getAreas(dataArr)

    this.$store.commit('startLoading')
  },
  destroyed() {}
}
</script>

<style lang="scss" scoped>
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.invitation-detail {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;
  margin-top: 20px;

  .invitation-header {
    padding: 40px;

    .iv-title {
      width: 100%;
      height: 20px;
      line-height: 20px;
      font-size: 20px;
      font-family: DINAlternate;
      font-weight: bold;
      color: #00469c;
    }

    .iv-tip {
      width: 100%;
      height: 14px;
      line-height: 14px;
      margin-top: 10px;
      font-size: 14px;

      font-weight: normal;
      color: rgba(41, 41, 41, 1);
    }

    .form-wrap {
      margin-top: 40px;
      height: 54px;
    }
  }

  .invite-btn-box {
    .invite-btn {
      cursor: pointer;
      height: 30px;
      line-height: 30px;
      padding: 0 10px;
      margin-left: 30px;
      margin-bottom: 30px;
      font-size: 14px;

      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
  }

  .invitation-content {
    .tab-wrap {
      width: 100%;
      height: 40px;
      background: rgba(250, 250, 250, 1);
      padding: 0 40px;
    }

    .grid-wrap {
    }
  }
}
</style>
<style lang="scss">
.invitation-detail {
  .mt-form-item-topLabel .label {
    font-size: 14px;

    font-weight: normal;
    color: rgba(41, 41, 41, 1);
  }

  .mt-tabs-container {
    height: 50px;
  }
}
</style>
