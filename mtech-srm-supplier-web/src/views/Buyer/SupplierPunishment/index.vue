<template>
  <div class="punishment-box">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :hidden-tabs="false"
      :padding-top="true"
      :current-tab="tabId"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    >
      <!-- 分级 -->
      <mt-template-page
        slot="slot-1"
        ref="templateRefClassify"
        :template-config="pageConfigClassify"
        :hidden-tabs="false"
        :padding-top="true"
        @handleClickToolBar="handleClickToolBarClassify"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClick"
      >
      </mt-template-page>
      <!-- 处罚与淘汰 -->
      <mt-template-page
        slot="slot-2"
        ref="templateRefPunishment"
        :template-config="pageConfigPunishment"
        :hidden-tabs="false"
        :padding-top="true"
        @handleClickToolBar="handleClickToolBarPunishment"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitlePunishment"
      >
      </mt-template-page>
    </mt-template-page>
  </div>
</template>

<script>
import {
  columns,
  columnShare,
  applicationforme,
  punishmentRelieve,
  punishmentformeg,
  punishmentformess,
  punishedforme,
  punishedformeg
} from './config/columns'
import utils from '@/utils/utils'
export default {
  beforeRouteEnter(to, from, next) {
    try {
      let tabId = window.sessionStorage.getItem('tabId')
      if (tabId) {
        window.sessionStorage.removeItem('tabId')
        next({
          name: 'supplierpunishment',
          query: {
            tabId: tabId
          }
        })
      } else {
        next(() => {})
      }
    } catch (error) {
      next(() => {})
    }
  },
  data() {
    return {
      tabId: 0, // 当前tab位置
      punishCauseTypeList: [],
      // 分级
      pageConfigClassify: [
        {
          gridId: 'b63d39a3-07d1-4cbc-b88e-45c2752da592',
          title: this.$t('申请单'),
          toolbar: {
            tools: [
              [
                { id: 'editClassify', icon: 'icon_Editor', title: this.$t('编辑') },
                {
                  id: 'DeleteClassify',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除')
                },
                {
                  id: 'releaSse',
                  icon: 'icon_solid_submit',
                  title: this.$t('提交'),
                  origin: 'templateRefClassify'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: applicationforme,
            asyncConfig: {
              url: '/supplier/tenant/buyer/apply/grade/list'
            }
          }
        },
        {
          gridId: '9968bda4-f23e-42d5-8f14-e2322e74115d',
          // useToolTemplate: false,
          title: this.$t('已分级'),
          toolbar: {
            tools: [
              [
                // {
                //   id: "Reclassification",
                //   icon: "icon_solid_Newinvitation",
                //   title: this.$t("重新分级"),
                // },
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: punishedformeg,
            asyncConfig: {
              url: '/supplier/tenant/buyer/process/manager/queryPageList',
              // params: {
              //   supplierGradeFlag: "1",
              // },
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  field: 'supplierGradeFlag',
                  label: this.$t('供应商分级标记'),
                  operator: 'equal',
                  type: 'Integer',
                  value: '1'
                }
              ]
            }
          }
        }
      ],
      // 惩罚和淘汰
      pageConfigPunishment: [
        {
          gridId: '5e8d3e17-97ea-4136-8497-c852df2657dd',
          useToolTemplate: false,
          title: this.$t('惩罚和淘汰申请'),
          toolbar: {
            tools: [
              [
                { id: 'editPunishment', icon: 'icon_Editor', title: this.$t('编辑') },
                {
                  id: 'DeletePunishment',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除')
                },
                {
                  id: 'releaSse',
                  icon: 'icon_solid_submit',
                  title: this.$t('提交'),
                  origin: 'templateRefPunishment'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: punishmentRelieve(),
            asyncConfig: {
              url: '/supplier/tenant/buyer/apply/punish/list'
            }
          }
        },
        {
          gridId: 'b8a1eb33-619d-409a-ad4c-2047e81d1961',
          useToolTemplate: false,
          title: this.$t('惩罚解除和启用申请'),
          toolbar: {
            tools: [
              [
                { id: 'editRelieve', icon: 'icon_Editor', title: this.$t('编辑') },
                { id: 'Deletec', icon: 'icon_solid_Delete1', title: this.$t('删除') },
                {
                  id: 'releaSse',
                  icon: 'icon_solid_submit',
                  title: this.$t('提交'),
                  origin: 'templateRefPunishment'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: punishmentformeg,
            asyncConfig: {
              url: '/supplier/tenant/buyer/apply/relive/list'
            }
          }
        },
        {
          gridId: 'e221d27f-312d-402d-876c-d09995d94a9c',
          useToolTemplate: false,
          title: this.$t('供应商申诉'),
          toolbar: {
            tools: [
              [
                {
                  id: 'releaSse',
                  icon: 'icon_solid_submit',
                  title: this.$t('提交'),
                  origin: 'templateRefPunishment'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: punishmentformess,
            asyncConfig: {
              url: '/supplier/tenant/buyer/apply/relive/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: this.$t('单据申请方'),
                  field: 'documentSender',
                  type: 'number',
                  operator: 'equal',
                  value: 1
                }
              ]
            }
          }
        },
        {
          gridId: '99a0c5ae-1666-4f03-b33a-b67504069c5b',
          useToolTemplate: false,
          title: this.$t('已惩罚列表'),
          toolbar: {
            tools: [
              [
                {
                  id: 'CAForm',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('解除申请单')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: punishedforme,
            asyncConfig: {
              url: '/supplier/tenant/buyer/partner/punish/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: this.$t('状态'),
                  field: 'businessType',
                  type: 'string',
                  operator: 'notequal',
                  value: 'disuse'
                }
              ]
            }
          }
        },
        {
          gridId: '345da1ce-4a0c-438a-9819-252a4f09c34b',
          useToolTemplate: false,
          title: this.$t('已淘汰列表'),
          toolbar: {
            tools: [
              [
                {
                  id: 'Enable',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('启用')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: punishedformeg,
            asyncConfig: {
              url: '/supplier/tenant/buyer/partner/punish/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: this.$t('状态'),
                  field: 'businessType',
                  type: 'string',
                  operator: 'equal',
                  value: 'disuse'
                }
              ]
            }
          }
        }
      ],
      // 主config
      pageConfig: [
        {
          gridId: '70d2e31f-7fd3-4906-b546-28f024189c34',
          useToolTemplate: false,
          title: this.$t('供应商共享'),
          toolbar: {
            tools: [
              [
                { id: 'edit', icon: 'icon_Editor', title: this.$t('编辑') },
                { id: 'Delete', icon: 'icon_solid_Delete1', title: this.$t('删除') },
                {
                  id: 'releaSse',
                  icon: 'icon_solid_submit',
                  title: this.$t('提交'),
                  origin: 'templateRef'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnShare,
            asyncConfig: {
              url: '/supplier/tenant/buyer/apply/share/list'
            }
          }
        },
        {
          gridId: '299cf96e-00d1-4891-829f-8580c2aafc05',
          title: this.$t('供应商分级')
        },
        {
          gridId: '4e0397e6-d55e-4386-914b-3c17a532e94d',
          title: this.$t('供应商处罚与淘汰')
        },
        {
          gridId: '738c9ce3-c739-4456-81ef-09fd238702ce',
          useToolTemplate: false,
          title: this.$t('阶段升降级'),
          toolbar: {
            tools: [
              [
                { id: 'editUpDown', icon: 'icon_Editor', title: this.$t('编辑') },
                { id: 'Deleted', icon: 'icon_solid_Delete1', title: this.$t('删除') },
                {
                  id: 'releaSse',
                  icon: 'icon_solid_submit',
                  title: this.$t('提交'),
                  origin: 'templateRef'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columns,
            asyncConfig: {
              url: '/supplier/tenant/buyer/apply/stage/list'
            }
          }
        }
      ]
    }
  },

  watch: {
    tabId(nv) {
      if (nv === 2) {
        this.getDictItem()
      }
    }
  },

  created() {
    let { tabId = 0 } = this.$route.query
    this.tabId = Number(tabId)
    this.getDictItem()
  },

  methods: {
    handleSelectTab(e) {
      this.tabId = e
    },
    handleClickCellTitle(e) {
      try {
        sessionStorage.setItem('tabId', this.tabId)
      } catch (error) {
        console.log(error)
      }
      //单元格Title点击
      console.log(e)
      const { field, data, tabIndex } = e
      if (field === 'applyCode' && data && data.id && tabIndex == 0) {
        this.$router.push({
          path: '/supplier/punishmentdetail',
          query: {
            id: data.id
          }
        })
      } else if (tabIndex == 3) {
        this.$router.push({
          path: '/supplier/supplierzidong',
          query: {
            id: data.id
          }
        })
      }
    },
    handleClick(e) {
      //单元格Title点击
      console.log(e)
      const { field, data } = e
      if (field === 'applyCode' && data && data.id) {
        try {
          sessionStorage.setItem('tabId', this.tabId)
        } catch (error) {
          console.log(error)
        }
        this.$router.push({
          path: '/supplier/classifDetall',
          query: {
            id: data.id
          }
        })
      }
    },

    handleClickCellTitlePunishment(e) {
      //单元格Title点击
      console.log(e)
      const { field, data } = e
      if (field === 'applyCode' && data && data.id) {
        let isrelieve = 'unrelieve' // 是否的处罚 还是解除处罚
        if (
          data.businessType === 'removeFreeze' ||
          data.businessType === 'removeBlack' ||
          data.businessType === 'removeDisuse'
        ) {
          isrelieve = 'relieve'
        }
        try {
          sessionStorage.setItem('tabId', this.tabId)
        } catch (error) {
          console.log(error)
        }

        this.$router.push({
          path: '/supplier/fenDetall',
          query: {
            id: data.id,
            isrelieve: isrelieve // 解除处罚
          }
        })
      }
    },
    handleClickToolBar(e) {
      console.log(e)
      //头部的图标
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (sltList.some((item) => item.applyStatus === 20 || item.applyStatus === 40)) {
        this.$toast({
          content: this.$t('待审批、已完成状态不能编辑！'),
          type: 'warning'
        })
        return
      }

      // 共享
      if (toolbar.id === 'edit') {
        if (sltList && sltList.length === 1) {
          this.editTask(sltList[0])
        } else if (sltList && sltList.length > 1) {
          this.$toast({ content: this.$t('请一次只编辑一行数据'), type: 'warning' })
        } else {
          this.$toast({ content: this.$t('请先选择一行数据'), type: 'warning' })
        }
      }
      // 手动准入升降级
      if (toolbar.id === 'editUpDown') {
        if (sltList && sltList.length) {
          this.editUpDownFun(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行数据'), type: 'warning' })
        }
      }

      // 申诉
      if (toolbar.id === 'editfes') {
        if (sltList && sltList.length) {
          this.adddfNew(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行数据'), type: 'warning' })
        }
      }
      if (toolbar.id === 'Delete') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }

      if (toolbar.id === 'Deletec') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecordc(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }

      // 所有提交
      if (toolbar.id === 'releaSse') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.submitTask(sltList, toolbar.origin)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      // 手动升降级
      if (toolbar.id === 'Deleted') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecordClassify(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }

      // 申诉
      if (toolbar.id === 'Deletee') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecorde(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    // 分级
    handleClickToolBarClassify(e) {
      const { toolbar, data, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (sltList.some((item) => item.applyStatus === 20 || item.applyStatus === 40)) {
        this.$toast({
          content: this.$t('待审批、已完成状态不能编辑！'),
          type: 'warning'
        })
        return
      }
      // 编辑
      if (toolbar.id === 'editClassify') {
        if (sltList && sltList.length === 1) {
          this.editClassify(sltList[0])
        } else if (sltList && sltList.length > 1) {
          this.$toast({ content: this.$t('只能选择一条进行编辑！'), type: 'warning' })
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
        return
      }

      // 删除
      if (toolbar.id === 'DeleteClassify') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecordL(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }

      // 提交
      if (toolbar.id === 'releaSse') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可提交'),
              type: 'warning'
            })
            return
          }
          this.submitTask(sltList, toolbar.origin)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      // 重新分级
      if (toolbar.id === 'Reclassification') {
        if (sltList && sltList.length) {
          this.$dialog({
            modal: () => import('../supplierResources/conponents/addFenDialog'),
            data: {
              title: this.$t('重新分级'),
              info: data
            },
            success: () => {},
            close: () => {}
          })
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    // 惩罚与淘汰
    handleClickToolBarPunishment(e) {
      const { toolbar, gridRef } = e

      let sltList = gridRef.getMtechGridRecords()
      if (sltList.some((item) => item.applyStatus === 20 || item.applyStatus === 40)) {
        this.$toast({
          content: this.$t('待审批、已完成状态不能编辑！'),
          type: 'warning'
        })
        return
      }
      // 惩罚申请
      if (toolbar.id === 'editPunishment') {
        if (sltList && sltList.length > 0) {
          this.editPunishment(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }

      if (toolbar.id === 'DeletePunishment') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可删除'),
              type: 'warning'
            })
            return
          }
          this.deleteRecordb(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }

      // 解除惩罚申请
      if (toolbar.id === 'editRelieve') {
        if (sltList && sltList.length) {
          this.adddeNew(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }

      // 解除
      if (toolbar.id === 'CAForm') {
        let typeObj = {}
        sltList.forEach((item) => {
          typeObj[item.businessType] = 'businessType'
        })
        let businessTypeArr = Object.keys(typeObj) // 同一种类型
        if (sltList && sltList.length > 0 && businessTypeArr.length === 1) {
          if (sltList[0].status === 10) {
            this.caform(sltList)
          } else {
            this.$toast({ content: this.$t('仅可以操作处罚中的订单！'), type: 'warning' })
          }
        } else {
          if (businessTypeArr.length > 1) {
            this.$toast({ content: this.$t('请先同一类型'), type: 'warning' })
          } else {
            this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          }
        }
      }

      // 启用
      if (toolbar.id === 'Enable') {
        let typeObj = {}
        sltList.forEach((item) => {
          typeObj[item.businessType] = 'businessType'
        })
        let businessTypeArr = Object.keys(typeObj) // 同一种类型

        if (sltList && sltList.length > 0 && businessTypeArr.length === 1) {
          if (sltList[0].status === 10) {
            this.Enable(sltList)
          } else {
            this.$toast({ content: this.$t('仅可以操作处罚中的订单！'), type: 'warning' })
          }
        } else {
          if (businessTypeArr.length > 1) {
            this.$toast({ content: this.$t('请先同一类型'), type: 'warning' })
          } else {
            this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          }
        }
      }

      // 提交
      if (toolbar.id === 'releaSse') {
        if (sltList && sltList.length) {
          const { status } = sltList[0]
          if (status === 1) {
            this.$toast({
              content: this.$t('当前已处于启用状态，不可提交'),
              type: 'warning'
            })
            return
          }
          this.submitTask(sltList, toolbar.origin)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },

    // 获取 惩罚原因枚举
    getDictItem(dictCode = 'punishCauseType') {
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.punishCauseTypeList = res.data
            this.pageConfigPunishment[0].grid.columnData = punishmentRelieve(res.data)
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg,
            type: 'warning'
          })
        })
    },

    //手动准入升降级
    editUpDownFun(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/operbNew.vue'),
        data: {
          title: this.$t('编辑升降级'),
          isEdit: true,
          info: data
        },
        success: (data) => {
          if (data.type === 'jump' && !!data.id) {
            this.$router.push({
              path: '/supplier/supplierzidong',
              query: {
                id: data.id
              }
            })
          }
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editClassify(data) {
      const _this = this
      if (data.length > 1) {
        this.$toast({ content: this.$t('只能选择一条进行编辑！'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () => import('./components/classificationDialog.vue'),
        data: {
          title: this.$t('编辑分级'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRefClassify.refreshCurrentGridData()
        }
      })
    },
    // 惩罚
    editPunishment(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/editPunishment.vue'),
        data: {
          title: this.$t('编辑惩罚和淘汰申请'),
          isEdit: true,
          info: data,
          punishCauseTypeList: this.punishCauseTypeList
        },
        success: (data) => {
          let { type, id } = data
          if (type === 'jump') {
            this.$router.push({
              path: '/supplier/fenDetall',
              query: {
                id: id,
                isrelieve: 'unrelieve'
              }
            })
          }
          _this.$refs.templateRefPunishment.refreshCurrentGridData()
        }
      })
    },
    // 惩罚解除
    adddeNew(data) {
      const _this = this
      this.$dialog({
        modal: () => import('./components/operdeNew.vue'),
        data: {
          title: this.$t('编辑解除处罚申请单'),
          isEdit: true,
          info: data
        },
        success: (data) => {
          let { type, id } = data
          if (type === 'jump') {
            this.$router.push({
              path: '/supplier/fenDetall',
              query: {
                id: id,
                isrelieve: 'relieve'
              }
            })
          }
          _this.$refs.templateRefPunishment.refreshCurrentGridData()
        }
      })
    },

    // 申诉
    adddfNew(data) {
      const _this = this
      const { taskClassifyList } = this
      this.$dialog({
        modal: () => import('./components/operdeNew.vue'),
        data: {
          title: this.$t('编辑阶段'),
          isEdit: true,
          info: data,
          taskClassifyList
        },
        success: (id) => {
          if (id) {
            this.$router.push({
              path: '/supplier/questionnaire-config-detail',
              query: {
                id
              }
            })
          } else {
            _this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },

    handleClickCellTool(e) {
      // 单元格 图标点击 tool
      const { tool, data } = e
      if (data.applyStatus === 20 || data.applyStatus === 40) {
        this.$toast({
          content: this.$t('待审批、已完成状态不能编辑！'),
          type: 'warning'
        })
        return
      }
      // 共享
      if (['edit', 'delete'].includes(tool.id)) {
        tool.id === 'edit' && this.editTask(data)
        tool.id === 'delete' && this.deleteRecord([data])

        // 分级
      } else if (['editL', 'deleteL'].includes(tool.id)) {
        tool.id === 'editL' && this.editClassify(data)
        tool.id === 'deleteL' && this.deleteRecordL([data])

        // 手动升降降级
      } else if (['editUpDown', 'Deleted'].includes(tool.id)) {
        tool.id === 'editUpDown' && this.editUpDownFun([data])
        tool.id === 'Deleted' && this.deleteRecordClassify([data])
      }
      // 惩罚申请
      else if (['editPunishment', 'Deleteb'].includes(tool.id)) {
        tool.id === 'editPunishment' && this.editPunishment([data])
        tool.id === 'Deleteb' && this.deleteRecordb([data])
      }
      // 解除处罚
      else if (['editRelieve', 'Deletec'].includes(tool.id)) {
        tool.id === 'editRelieve' && this.adddeNew([data])
        tool.id === 'Deletec' && this.deleteRecordc([data])
      }
    },
    // 共享删除
    deleteRecord(data) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id).join(',')

      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表？'),
          confirm: () => _this.$API.SupplierPunishment.deleteshare({ ids: ids })
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 分级删除
    deleteRecordL(data) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表？'),
          confirm: () => _this.$API.SupplierPunishment['deleteba']({ applyIdList: ids })
        },
        success: (val) => {
          console.log(val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRefClassify.refreshCurrentGridData()
        }
      })
    },
    deleteRecordb(data) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表？'),
          confirm: () => _this.$API.SupplierPunishment['deletebb']({ applyIdList: ids })
        },
        success: (val) => {
          console.log(val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRefPunishment.refreshCurrentGridData()
        }
      })
    },
    deleteRecordc(data) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表？'),
          confirm: () => _this.$API.SupplierPunishment['deletebc']({ applyIdList: ids })
        },
        success: (val) => {
          console.log(val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 分级删除
    deleteRecordClassify(data) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id).join(',')
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表？'),
          confirm: () => _this.$API.SupplierPunishment['deletebd']({ ids })
        },
        success: (val) => {
          console.log(val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteRecorde(data) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择删除的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id).join(',')
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表？'),
          confirm: () => _this.$API.SupplierPunishment['deletebc']({ applyIdList: ids })
        },
        success: (val) => {
          console.log(val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    submitTask(data, origin) {
      const _this = this
      if (!data || data.length === 0) {
        this.$toast({ content: this.$t('请选择要提交的栏目'), type: 'warning' })
        return
      }
      let ids = data.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('是否确认提交所选申请？'),
          confirm: () => _this.$API.SupplierPunishment['deare']({ applyIdList: ids })
        },
        success: () => {
          _this.$toast({ content: this.$t('提交成功'), type: 'success' })
          _this.$refs[origin].refreshCurrentGridData()
        }
      })
    },
    // 共享的celltool编辑
    editTask(data) {
      this.$dialog({
        modal: () => import('./components/shareDialog.vue'),
        data: {
          title: this.$t('共享编辑'),
          isEdit: true,
          info: data
        },
        success: (data) => {
          let { type, id } = data
          if (type === 'jump') {
            this.$router.push({
              path: '/supplier/punishmentdetail',
              query: {
                id: id
              }
            })
          }
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    caform(data) {
      console.log(data)
      const _this = this
      this.$dialog({
        modal: () => import('./components/opereNew.vue'),
        data: {
          title: this.$t('解除申请单'),
          info: data
        },
        success: (data) => {
          let { type, id } = data
          if (type === 'jump') {
            this.$router.push({
              path: '/supplier/fenDetall',
              query: {
                id: id,
                isrelieve: 'relieve' // 解除处罚
              }
            })
          }
          _this.$refs.templateRefPunishment.refreshCurrentGridData()
        }
      })
    },
    Enable(data) {
      console.log(data)
      const _this = this

      this.$dialog({
        modal: () => import('./components/opervNew.vue'),
        data: {
          title: this.$t('解除申请单'),
          isEdit: true,
          info: data
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.punishment-box {
  height: 100%;
  .mt-tooptip {
    display: inline-flex;
  }
}
</style>
