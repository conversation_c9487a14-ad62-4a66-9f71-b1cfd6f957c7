<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item class="form-item" :label="$t('当前阶段')">
              <mt-input
                v-model="formInfo.orgName"
                css-class="grey-input"
                :readonly="true"
                :width="400"
                :show-clear-button="false"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierTypel" class="form-item" :label="$t('申请类型')">
              <mt-select
                v-model="formInfo.supplierTypel"
                :width="400"
                :data-source="stagTypeL"
                :show-clear-button="true"
                @change="handleChangeApplyType"
                :placeholder="$t('请选择申请类型')"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="supplierStage" class="form-item" :label="$t('目标阶段')">
              <mt-select
                v-model="formInfo.supplierStage"
                :width="400"
                :data-source="stageList"
                :fields="{ text: 'stageName', value: 'id' }"
                :show-clear-button="true"
                @change="handleChangeStage"
                :placeholder="$t('请选择目标阶段')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyUserId" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.applyUserId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="companyId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.companyId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyDepId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyDepId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                maxlength="200"
                :disabled="false"
                :show-clear-button="true"
                :multiline="true"
                :rows="2"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, areaColumnData } from '../config/columns.js'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      applyUserIdData: [],
      stageList: [],
      applyTypeInfo: {},
      info: [],
      stageInfo: {},
      stagTypeL: [],
      TenantUserList: [], // 当前租户下的用户
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        supplierEnterpriseName: '',
        orgName: '',
        supplierStage: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        supplierTypel: [],
        applyerOrgName: '',
        applyerDeptName: '',
        applyerName: ''
      },
      rules: {
        supplierTypel: [{ required: true, message: this.$t('请输入申请单类型'), trigger: 'blur' }],
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        supplierStage: [{ required: true, message: this.$t('请选择目标组织'), trigger: 'blur' }],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmSubmit,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      componentConfig: [
        {
          gridId: '3580f19e-3a4e-48fd-8e4d-ccf9ed1599ce',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: areaColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    let id = this.modalData.info[0].id
    this.getDictCode()
    this.getdetail(id)
    this.show()
    this.getCurrentTenantUsers()

    // 出发change
    this.$set(this.componentConfig[0].grid, 'dataSource', this.relationDTOList)
  },
  computed: {
    lineInfo() {
      return this.modalData.info
    },
    relationDTOList() {
      return this.modalData.info[0].relationDTOList
    },
    header() {
      return this.modalData.title
    }
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    // 提交
    confirm(fn) {
      let formInfo = this.formInfo
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }
      let { applyInfo, applyStage } = this.info

      let applyInfoQuery = {
        ...applyInfo,
        applyName: formInfo.supplierEnterpriseName,
        applyerOrgId: formInfo.companyId,
        applyerOrgName: formInfo.applyerOrgName,
        applyerDeptId: formInfo.applyDepId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyUserId,
        applyerName: formInfo.applyUser,
        applyType: formInfo.supplierTypel,
        remark: formInfo.remark,
        id: this.info.applyInfo.id
      }

      let applyStageQuery = {
        ...applyStage,
        businessType: formInfo.supplierTypel,
        destinationStageTemplateId: this.stageInfo.id,
        destinationStageTemplateName: this.stageInfo.stageName
      }

      let relationList = this.componentConfig[0].grid.dataSource

      if (relationList.length === 0) {
        this.$toast({
          content: this.$t('修改失败，请至少有一个勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        applyInfo: applyInfoQuery,
        applyStage: applyStageQuery,
        relationList
      }
      this.$loading()

      this.$API.SupplierPunishment.upDownUpdate(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            if (!!fn && typeof fn === 'function') {
              fn()
            } else {
              this.$emit('confirm-function', 'reload')
            }
            this.$toast({ content: this.$t('修改成功'), type: 'success' })
          } else {
            this.$toast({
              content: res.msg || this.$t('修改失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('修改失败，请重试!'),
            type: 'warning'
          })
        })
    },

    getDictCode(dictCode = 'stageType') {
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.stagTypeL = res.data.map((v) => {
              return {
                ...v,
                text: v.name,
                value: v.itemCode
              }
            })
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
        })
    },

    confirmSubmit() {
      this.confirm(() => {
        this.$emit('confirm-function', { type: 'jump', id: this.info.applyInfo.id })
        // this.$API.SupplierPunishment.applySubmit({
        //   applyIdList: [this.info.applyInfo.id],
        // }).then((res) => {
        //   this.$hloading()
        //   if (res.code === 200 && !utils.isEmpty(res.data)) {
        //     this.$toast({
        //       content: "提交升降级成功! ",
        //       type: "success",
        //     });
        //     this.$emit("confirm-function", "reload");
        //   } else {
        //     this.$toast({
        //       content: "提交升降级失败，请重试!",
        //       type: "warning",
        //     });
        //   }
        // });
      })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (sltList.filter((citem) => citem.id === item.id).length === 0) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },
    handleClickCellTool() {},
    handleClickCellTitle() {},

    handleUserChange(data) {
      let { itemData } = data
      this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.userName
      this.getCompany()
    },

    handleChangeApplyType(data) {
      let { itemData } = data
      this.applyTypeInfo = itemData
    },

    handleChangeStage(data) {
      let { itemData } = data
      this.stageInfo = itemData
    },

    // 根据公司id 获取阶段模板
    queryStageTempalte(companyId) {
      this.emptyData = false
      this.$API.supplierAcc
        .queryStageTempalte({
          orgId: companyId
        })
        .then((result) => {
          let { data } = result
          if (result.code === 200 && !utils.isEmpty(data)) {
            this.stageList = data
          } else {
            this.$toast({ content: result.msg || this.$t('获取准入阶段失败！'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.$toast({ content: error.msg || this.$t('获取准入阶段失败！'), type: 'warning' })
        })
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
        })
      })
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            // this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },
    // 获取编辑的详情
    getdetail(id) {
      this.$API.SupplierPunishment.getzidongfenjiDetail({ id: id }).then((res) => {
        this.info = res.data
        this.formInfo.supplierEnterpriseName = this.modalData.info[0].applyName
        this.formInfo.supplierTypel = this.modalData.info[0].businessType
        this.formInfo.remark = this.modalData.info[0].remark
        this.formInfo.supplierStage = this.modalData.info[0].destinationStageTemplateId
        this.formInfo.orgName = this.modalData.info[0].sourceStageTemplateName
        this.formInfo.applyUserId = this.info.applyInfo.applyerId
        this.formInfo.companyId = this.info.applyInfo.applyerOrgId
        this.formInfo.applyDepId = this.info.applyInfo.applyerDeptId

        this.queryStageTempalte(res.data.applyInfo.applyerOrgId)
      })
    },
    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    handleCompanyChange(data) {
      let { itemData } = data
      // this.formInfo.companyId = itemData.id
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
.upgrade-dialog {
  padding: 20px; //--*

  /deep/.grey-input {
    background: #eee !important;
  }

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
</style>
