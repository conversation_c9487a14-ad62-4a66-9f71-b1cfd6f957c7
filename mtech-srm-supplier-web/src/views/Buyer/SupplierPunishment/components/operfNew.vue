<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请原因')">
              <mt-input
                v-model="formInfo.violationSource"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请原因')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierTypel" class="form-item" :label="$t('申请类型')">
              <mt-select
                v-model="formInfo.supplierTypel"
                :width="400"
                :data-source="stagTypeL"
                :show-clear-button="true"
                @change="handleChangeStage"
                :placeholder="$t('请选择申请类型')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyUserId" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.applyUserId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="companyId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.companyId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyDepId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyDepId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                :multiline="true"
                maxlength="200"
                :rows="2"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :hidden-tabs="true"
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, areaColumnData } from '../config/columns.js'
export default {
  data() {
    return {
      dictCode: 'violationType',
      orgLevelTypeCode: 'ORG02',
      applyUserIdData: [],
      stageList: [],
      stagei: [],
      stageInfo: {},
      stagTypeL: [
        {
          text: this.$t('解除供应商拉黑'),
          value: 'removeBlack'
        },
        {
          text: this.$t('解除供应商冻结'),
          value: 'removeFreeze'
        },
        {
          text: this.$t('解除供应商淘汰'),
          value: 'removeDisuse'
        }
      ],
      TenantUserList: [], // 当前租户下的用户
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        supplierEnterpriseName: '',
        orgName: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        supplierTypel: '',
        applyerOrgName: '',
        applyerDeptName: '',
        applyerName: '',
        violationSource: ''
      },
      rules: {
        supplierTypel: [{ required: true, message: this.$t('请输入申请单类型'), trigger: 'blur' }],
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmSubmit,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并提交') }
        }
      ],
      componentConfig: [
        {
          gridId: '0c36b0db-58fa-4988-bbca-89a3bdcf9e4d',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: areaColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  created() {
    console.log(121, this.modalData)
  },
  mounted() {
    let id = this.modalData.info[0].id
    this.show()
    this.getdetail(id)
    this.getCurrentTenantUsers()
    // 出发change
    console.log(234, this.componentConfig[0].grid)
    this.$set(this.componentConfig[0].grid, 'dataSource', this.datainfo)
  },
  computed: {
    datainfo() {
      return this.modalData.info[0].relationDTOList
    },
    header() {
      return this.modalData.title
    }
  },

  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    // 提交
    confirm() {
      let formInfo = this.formInfo
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }

      let infoDTO = {
        applyName: formInfo.supplierEnterpriseName,
        applyerOrgId: formInfo.companyId,
        applyerOrgName: formInfo.applyerOrgName,
        applyerDeptId: formInfo.applyDepId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyUserId,
        applyerName: formInfo.applyUser,
        applyType: formInfo.supplierTypel,

        id: this.info.infoDTO.id
      }
      let relieveDTO = {
        documentSender: '1',
        remark: formInfo.remark,
        tenantId: this.info.relieveDTO.applyId,
        businessType: formInfo.supplierTypel,
        id: this.info.relieveDTO.id
      }

      let relationDTOList = this.componentConfig[0].grid.dataSource.map((item) => {
        return {
          partnerRelationId: item.partnerRelationId,
          id: item.id
        }
      })

      if (relationDTOList.length === 0) {
        this.$toast({
          content: this.$t('新增失败，请至少有一个勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        infoDTO,
        relieveDTO,
        relationDTOList
      }
      this.$loading()

      this.$API.SupplierPunishment.updatapuncomfm(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({ content: this.$t('新增成功'), type: 'warning' })
            this.$emit('confirm-function', 'reload')
          } else {
            this.$toast({
              content: res.msg || this.$t('新增失败，请重试'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('新增失败，请重试'),
            type: 'warning'
          })
        })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (sltList.filter((citem) => citem.id === item.id).length === 0) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },
    handleClickCellTool() {},
    handleClickCellTitle() {},

    handleUserChange(data) {
      let { itemData } = data
      console.log(222, itemData)
      this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.userName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      console.log(itemData)
      this.stageInfo = itemData
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      let dictCode = this.dictCode
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          console.log(111, res)
          this.stagei = res.data
          this.stagei.forEach((item) => {
            let uip = {
              stageName: item.name,
              id: item.id,
              code: item.itemCode
            }
            this.stageList.push(uip)
          })
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取对应数据失败！'),
            type: 'warning'
          })
          return []
        })
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
          console.log(this.applyUserIdData)
        })
      })
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            console.log(res.data)
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },
    getdetail(id) {
      this.$API.SupplierPunishment.getjeiDetail({ id: id }).then((res) => {
        this.info = res.data
        console.log(2222, this.info)
        this.formInfo.supplierEnterpriseName = this.modalData.info[0].applyName
        this.formInfo.supplierTypel = this.modalData.info[0].businessType
        this.formInfo.remark = this.modalData.info[0].remark
        this.formInfo.violationSource = this.info.infoDTO.applyReason
        this.formInfo.applyUserId = this.info.infoDTO.applyerId
        this.formInfo.companyId = this.info.infoDTO.applyerOrgId
        this.formInfo.applyDepId = this.info.infoDTO.applyerDeptId
      })
    },
    handleCompanyChange(data) {
      let { itemData } = data
      console.log(data, itemData)
      // this.formInfo.companyId = itemData.id
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
.upgrade-dialog {
  padding: 20px; //--*

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
</style>
