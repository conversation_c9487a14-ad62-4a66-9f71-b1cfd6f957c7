<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('违规原因')">
              <mt-input
                v-model="formInfo.applyReason"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入违规原因')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="penaltyPeriodStart" class="form-item" :label="$t('处罚开始期限')">
              <mt-date-picker
                v-model="formInfo.penaltyPeriodStart"
                :width="400"
                :open-on-focus="true"
                :allow-edit="false"
                :min="new Date()"
                :placeholder="$t('选择日期')"
              ></mt-date-picker>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierTypel" class="form-item" :label="$t('申请类型')">
              <mt-select
                v-model="formInfo.supplierTypel"
                :width="400"
                :data-source="stagTypeL"
                :show-clear-button="true"
                @change="handleChangeStage"
                :placeholder="$t('请选择申请类型')"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="violationType" class="form-item" :label="$t('原因类型')">
              <mt-select
                v-model="formInfo.violationType"
                :width="400"
                :data-source="punishCauseTypeList"
                :fields="{ text: 'name', value: 'itemCode' }"
                :show-clear-button="true"
                @change="handleChangeStage"
                :placeholder="$t('请选择原因类型')"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="penaltyPeriodEnd" class="form-item" :label="$t('处罚结束期限')">
              <mt-date-picker
                v-model="formInfo.penaltyPeriodEnd"
                :width="400"
                :open-on-focus="true"
                :allow-edit="false"
                :min="new Date()"
                :placeholder="$t('选择日期')"
              ></mt-date-picker>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyUserId" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.applyUserId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="companyId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.companyId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyDepId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyDepId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                :multiline="true"
                maxlength="200"
                :rows="2"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :hidden-tabs="true"
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, areaColumnData } from '../config/columns.js'
export default {
  data() {
    let timeCompare = (start, end) => {
      if (!start || !end) {
        return false
      }
      let startTimeNum = new Date(start).getTime()
      let endTimeNum = new Date(end).getTime()
      console.log(startTimeNum, endTimeNum)
      if (startTimeNum >= endTimeNum) {
        return false
      } else {
        return true
      }
    }

    let validateEnd = (rule, value, callback) => {
      console.log(this.formInfo.penaltyPeriodEnd)
      let startTimeNummer = new Date(this.formInfo.penaltyPeriodEnd).getTime()
      let currentTimeNummer = new Date(new Date().toLocaleDateString()).getTime()

      if (startTimeNummer >= currentTimeNummer) {
        callback()
      } else {
        callback(new Error(this.$t('请确保结束时间等于或大于当前时间！')))
      }

      if (value === '') {
        callback(new Error(this.$t('请输入惩罚结束时间')))
      } else if (!this.formInfo.penaltyPeriodStart) {
        callback(new Error(this.$t('请输入处罚开始期限，并确保开始时间早于结束时间！')))
      } else if (
        !!this.formInfo.penaltyPeriodStart &&
        timeCompare(this.formInfo.penaltyPeriodStart, value)
      ) {
        callback()
      } else if (
        !!this.formInfo.penaltyPeriodStart &&
        !timeCompare(this.formInfo.penaltyPeriodStart, value)
      ) {
        callback(new Error(this.$t('请确保开始时间早于结束时间！')))
      } else {
        callback()
      }
    }

    let validateStart = (rule, value, callback) => {
      console.log(this.formInfo.penaltyPeriodEnd)
      let startTimeNummer = new Date(this.formInfo.penaltyPeriodStart).getTime()
      let currentTimeNummer = new Date(new Date().toLocaleDateString()).getTime()

      if (startTimeNummer >= currentTimeNummer) {
        callback()
      } else {
        callback(new Error(this.$t('请确保开始时间等于或大于当前时间！')))
      }

      if (value === '') {
        callback(new Error(this.$t('请输入惩罚开始时间')))
      } else if (!this.formInfo.penaltyPeriodEnd) {
        callback(new Error(this.$t('请输入处罚结束期限，并确保开始时间早于结束时间！')))
      } else if (
        !!this.formInfo.penaltyPeriodEnd &&
        timeCompare(value, this.formInfo.penaltyPeriodEnd)
      ) {
        callback()
      } else if (
        !!this.formInfo.penaltyPeriodEnd &&
        !timeCompare(value, this.formInfo.penaltyPeriodEnd)
      ) {
        callback(new Error(this.$t('请确保开始时间早于结束时间！')))
      } else {
        callback()
      }
    }

    return {
      applyUserIdData: [],
      stageInfo: {},
      stagTypeL: [
        {
          text: this.$t('供应商拉黑'),
          value: 'black'
        },
        {
          text: this.$t('供应商冻结'),
          value: 'freeze'
        },
        {
          text: this.$t('供应商淘汰'),
          value: 'disuse'
        }
        // {
        //   text: this.$t("供应商解除拉黑"),
        //   value: "removeBlack",
        // },
        // {
        //   text: this.$t("供应商解除冻结"),
        //   value: "removeFreeze",
        // },
        // {
        //   text: this.$t("供应商解除淘汰"),
        //   value: "removeDisuse",
        // },
      ],
      TenantUserList: [], // 当前租户下的用户
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        supplierEnterpriseName: '',
        orgName: '',
        violationType: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        supplierTypel: '',
        applyerOrgName: '',
        applyerDeptName: '',
        applyerName: '',
        applyReason: '',
        penaltyPeriodStart: '',
        penaltyPeriodEnd: ''
      },
      rules: {
        supplierTypel: [{ required: true, message: this.$t('请输入申请单类型'), trigger: 'blur' }],
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        violationType: [{ required: true, message: this.$t('请选择原因类型'), trigger: 'blur' }],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        penaltyPeriodStart: [
          { required: true, message: this.$t('请选择处罚开始时间'), trigger: 'blur' },
          { validator: validateStart, trigger: 'submit' }
        ],
        penaltyPeriodEnd: [
          { required: true, message: this.$t('请选择处罚结束时间'), trigger: 'blur' },
          { validator: validateEnd, trigger: 'submit' }
        ],
        // Period: [
        //   { required: true, validator: (rule, value, callback) => {
        //     console.log(value)
        //     if (value === "") {
        //       callback(new Error("请输入开始结束时间"));
        //     } else {
        //       callback();
        //     }
        //   }, trigger: "blur" },
        // ],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmSubmit,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      componentConfig: [
        {
          gridId: 'e6010a53-4027-4184-94f0-32e8537d4e2b',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: areaColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  created() {},
  mounted() {
    let id = this.modalData.info[0].id
    this.show()
    this.getdetail(id)
    this.getCurrentTenantUsers()

    this.$set(this.componentConfig[0].grid, 'dataSource', this.datainfo)
  },
  computed: {
    datainfo() {
      return this.modalData.info[0].relationDTOList
    },
    header() {
      return this.modalData.title
    },
    punishCauseTypeList() {
      return this.modalData.punishCauseTypeList
    }
  },

  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    //后台提交日期格式化
    date(time) {
      return this.$utils.formateTime(time, 'yyyy-MM-dd')
    },
    // 提交
    confirm(fn) {
      let formInfo = this.formInfo
      let { infoDTO, punishDTO } = this.info
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }

      let infoDTOQuery = {
        ...infoDTO,
        applyReason: this.formInfo.applyReason,
        applyName: formInfo.supplierEnterpriseName,
        applyerOrgId: formInfo.companyId,
        applyerOrgName: formInfo.applyerOrgName,
        applyerDeptId: formInfo.applyDepId,
        applyerDeptName: formInfo.applyerDeptName,
        applyerId: formInfo.applyUserId,
        applyerName: formInfo.applyUser,
        // applyType: formInfo.supplierTypel,
        remark: formInfo.remark
      }
      let punishDTOQuery = {
        ...punishDTO,
        violationType: formInfo.violationType,
        penaltyPeriodEnd: this.date(formInfo.penaltyPeriodEnd),
        penaltyPeriodStart: this.date(formInfo.penaltyPeriodStart),
        businessType: formInfo.supplierTypel
      }

      let relationDTOList = this.componentConfig[0].grid.dataSource
      if (relationDTOList.length === 0) {
        this.$toast({
          content: this.$t('新增失败，请至少有一个勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        infoDTO: infoDTOQuery,
        punishDTO: punishDTOQuery,
        relationDTOList
      }
      this.$loading()

      this.$API.SupplierPunishment.updatapunishment(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({ content: this.$t('编辑成功'), type: 'success' })
            if (!!fn && typeof fn === 'function') {
              fn()
            } else {
              this.$emit('confirm-function', 'reload')
            }
          } else {
            this.$toast({
              content: res.msg || this.$t('编辑失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('编辑失败，请重试!'),
            type: 'warning'
          })
        })
    },

    confirmSubmit() {
      this.confirm(() => {
        this.$emit('confirm-function', { type: 'jump', id: this.info.infoDTO.id })
        // this.$API.SupplierPunishment.applySubmit({
        //   applyIdList: [this.info.infoDTO.id],
        // }).then((res) => {
        //   this.$hloading()
        //   if (res.code === 200 && !utils.isEmpty(res.data)) {
        //     this.$toast({
        //       content: "提交成功! ",
        //       type: "success",
        //     });
        //     this.$emit("confirm-function", "reload");
        //   } else {
        //     this.$toast({
        //       content: "提交失败，请重试!",
        //       type: "warning",
        //     });
        //   }
        // });
      })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (sltList.filter((citem) => citem.id === item.id).length === 0) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },
    handleClickCellTool() {},
    handleClickCellTitle() {},

    handleUserChange(data) {
      let { itemData } = data
      console.log(222, itemData)
      this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.userName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      console.log(itemData)
      this.stageInfo = itemData
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
        })
      })
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            console.log(res.data)
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },
    getdetail(id) {
      this.$API.SupplierPunishment.getchengfaDetail({ id: id }).then((res) => {
        this.info = res.data
        let { punishDTO, infoDTO } = res.data
        this.formInfo.supplierEnterpriseName = this.modalData.info[0].applyName
        this.formInfo.supplierTypel = this.modalData.info[0].businessType
        this.formInfo.remark = this.modalData.info[0].remark
        this.formInfo.violationType = punishDTO.violationType
        this.formInfo.penaltyPeriodStart = this.modalData.info[0].penaltyPeriodStart
        this.formInfo.penaltyPeriodEnd = this.modalData.info[0].penaltyPeriodEnd

        this.formInfo.applyReason = infoDTO.applyReason
        this.formInfo.applyUserId = infoDTO.applyerId
        this.formInfo.companyId = infoDTO.applyerOrgId
        this.formInfo.applyDepId = infoDTO.applyerDeptId
      })
    },
    handleCompanyChange(data) {
      let { itemData } = data
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
.upgrade-dialog {
  padding: 20px; // --*

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
</style>
