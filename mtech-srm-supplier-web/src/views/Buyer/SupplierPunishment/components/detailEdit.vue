<template>
  <div class="task-center fbox">
    <div class="task-sidebar">
      <div
        v-for="(taskItem, index) in fullTaskIndexArr"
        :key="index"
        @click="scrollInto(index)"
        :class="activeClass == index ? 'active' : ''"
      >
        <div class="collapse-header">
          {{ taskItem }}
        </div>
      </div>
    </div>
    <div class="collapse-content flex1">
      <div :ref="'formItem_' + 0">
        <div class="subtitle">{{ $t('附件') }}</div>
        <div class="enclosure">
          <mt-template-page
            ref="templateRef"
            :template-config="pageConfig"
            :use-tool-template="false"
            :padding-top="false"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTool="handleClickCellTool"
            @handleClickCellTitle="handleClickCellTitle"
          >
          </mt-template-page>
        </div>
      </div>
      <div :ref="'formItem_' + 1">
        <div class="subtitle mr20">{{ $t('原因') }}</div>
        <div class="enclosure">
          <!--  @change="changeText" -->
          <mt-rich-text-editor
            ref="MtRichTextEditor"
            :toolbar-settings="toolbarSettings"
            :background-color="backgroundColor"
            v-model="applyInfo.applyReason"
            @created="createdTextEditor"
          >
          </mt-rich-text-editor>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 表单tab后面的数据
import { enclosurea } from '../config/columns'
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
// polyfill for scrollIntoView
import smoothscroll from 'smoothscroll-polyfill'
// kick off the polyfill!
smoothscroll.polyfill()
export default {
  components: {
    MtRichTextEditor
  },
  data() {
    return {
      // toolbarSettings: {
      //   enable: true,
      //   enableFloating: true,
      //   type: 'Expand',
      //   items: [
      //     'Bold',
      //     'Italic',
      //     'Underline',
      //     '|',
      //     'Formats',
      //     'Alignments',
      //     'OrderedList',
      //     'UnorderedList',
      //     '|',
      //     'CreateLink',
      //     'Image',
      //     'backgroundColor',
      //     '|',
      //     'SourceCode',
      //     'Undo',
      //     'Redo'
      //   ],
      //   itemConfigs: {}
      // },
      backgroundColor: {
        columns: 5,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      },
      toolbarSettings: { enable: true, enableFloating: true, type: 'Expand' },
      pageConfig: [
        {
          gridId: '98020105-67be-44cd-b861-cd5c27d84be3',
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('新增'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除'),
                  visibleCondition: () => this.canEdit
                },
                {
                  id: 'edit',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('编辑'),
                  visibleCondition: () => this.canEdit
                }
              ]
            ]
          },
          grid: {
            columnData: enclosurea,
            asyncConfig: {
              url: '/supplier/tenant/common/file/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  label: '',
                  field: 'bizId',
                  type: 'number',
                  operator: 'equal',
                  value: this.id
                }
              ],
              params: { bizId: this.id }
            }
          }
        }
      ],
      activeClass: 0,
      fullTaskIndexArr: [this.$t('附件'), this.$t('原因说明')]
    }
  },
  props: {
    id: {
      type: [Number, String],
      default: 0
    },
    canEdit: {
      type: Boolean,
      default: false
    },
    applyInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    console.log('mounter', this.id)
  },
  methods: {
    createdTextEditor(value) {
      console.log('created', value)
    },
    handleClickToolBar(e) {
      const { toolbar, data, gridRef } = e
      if (toolbar.id === 'Add') {
        this.addNew(data)
        return
      }
      if (toolbar.id === 'Delete') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length > 0) {
          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
      if (toolbar.id === 'edit') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length > 1) {
          this.$toast({ content: this.$t('只能编辑一条'), type: 'warning' })
        } else if (sltList && sltList.length === 1) {
          this.editRecord(sltList[0])
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    handleClickCellTitle(e) {
      const { data } = e
      if (!data.fileUrl) return
      let link = document.createElement('a') //创建a标签
      link.style.display = 'none' //使其隐藏
      link.href = data.fileUrl //赋予文件下载地址
      link.setAttribute('download', data.fileName) //设置下载属性 以及文件名
      document.body.appendChild(link) //a标签插至页面中
      link.click() //强制触发a标签事件
      document.body.removeChild(link)
    },
    handleClickCellTool() {},
    addNew() {
      const _this = this
      this.$dialog({
        modal: () => import('./operEnlosure.vue'),
        data: {
          title: this.$t('新增附件'),
          isEdit: false,
          fileInfo: {},
          applyInfo: this.applyInfo
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    editRecord(item) {
      const _this = this
      this.$dialog({
        modal: () => import('./operEnlosure.vue'),
        data: {
          title: this.$t('编辑附件'),
          isEdit: true,
          fileInfo: item,
          applyInfo: this.applyInfo
        },
        success: () => {
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    deleteRecord(data) {
      const _this = this
      let ids = data.map((item) => item.id).join(',')
      console.log(ids)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选调查表？'),
          confirm: () => _this.$API.SupplierPunishment['delStage']({ ids })
        },
        success: (val) => {
          console.log(this.$t('删除'), val)
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          _this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 表单滚动
    scrollInto(id) {
      this.activeClass = id
      this.$refs['formItem_' + id].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    }
  },
  destroyed() {}
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}
.flex2 {
  flex: 2;
}

.mr20 {
  margin-top: 20px;
}

.task-center {
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  border-right: 2px solid rgba(232, 232, 232, 1);

  .task-sidebar {
    width: 120px;
    height: 100%;
    margin-left: 30px;
    padding-top: 14px;
  }
  .collapse-header {
    height: 30px;
    line-height: 30px;
    padding-left: 30px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin-bottom: 10px;

    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
    user-select: none;
  }
  .active {
    color: rgba(0, 70, 156, 1);
    border-left: 4px solid rgba(0, 70, 156, 1);
  }
  .collapse-content {
    background: rgba(255, 255, 255, 0.568);
    padding-top: 20px;
    padding-bottom: 20px;
    overflow: auto;
  }
  .subtitle {
    height: 14px;
    border-left: 2px solid;
    padding-left: 10px;
    margin-left: 20px;
  }
  .enclosure {
    height: 266px;
    margin-left: 20px;
    margin-top: 20px;
    margin-right: 20px;
  }
}
</style>
