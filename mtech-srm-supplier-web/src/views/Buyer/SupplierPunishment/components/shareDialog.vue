<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="upgrade-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                css-class="height-32"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>

            <mt-form-item prop="supplierStage" class="form-item" :label="$t('源组织')">
              <mt-input
                v-model="formInfo.orgName"
                :disabled="true"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierTypel" class="form-item" :label="$t('申请类型')">
              <mt-select
                v-model="formInfo.supplierTypel"
                :width="400"
                :disabled="true"
                :data-source="stagTypeL"
                :show-clear-button="true"
                @change="handleChangeStage"
                :placeholder="$t('请选择申请类型')"
              ></mt-select>
            </mt-form-item>

            <mt-form-item prop="supplierStage" class="form-item" :label="$t('目标组织')">
              <mt-select
                v-model="formInfo.supplierStage"
                :width="400"
                :disabled="true"
                :data-source="parentOrgList"
                :fields="{ text: 'orgName', value: 'id' }"
                :show-clear-button="true"
                @change="handleChangeParent"
                :placeholder="$t('请选择目标组织')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="applyUserId" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.applyUserId"
                :data-source="applyUserIdData"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'userName', value: 'id' }"
                :placeholder="$t('请选择申请人')"
                @change="handleUserChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="companyId" class="form-item" :label="$t('申请人公司')">
              <mt-select
                ref="companyRef"
                v-model="formInfo.companyId"
                :data-source="applyCompanyData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="applyDepId" class="form-item" :label="$t('申请人部门')">
              <mt-select
                ref="depRef"
                v-model="formInfo.applyDepId"
                :data-source="applyDepartData"
                :show-clear-button="true"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择申请部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                maxlength="200"
                :show-clear-button="true"
                :multiline="true"
                :rows="2"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { supplyAreaToolbar, areaColumnData } from '../config/columns.js'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      orgLevelTypeCode: 'ORG02',
      applyUserIdData: [],
      parentOrgList: [],
      info: [],
      stageInfo: {},
      parentInfo: {},
      stagTypeL: [],
      TenantUserList: [], // 当前租户下的用户
      applyCompanyData: [],
      applyDepartData: [],
      formInfo: {
        supplierEnterpriseName: '',
        orgName: '',
        supplierStage: '',
        remark: '',
        applyUserId: '',
        companyId: '',
        applyDepId: '',
        supplierTypel: [],
        applyerOrgName: '',
        applyerDeptName: '',
        applyerName: ''
      },
      rules: {
        supplierTypel: [{ required: true, message: this.$t('请输入申请单类型'), trigger: 'blur' }],
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }
        ],
        supplierStage: [{ required: true, message: this.$t('请选择目标组织'), trigger: 'blur' }],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [{ required: true, message: this.$t('请选择申请部门'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmSubmit,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      componentConfig: [
        {
          gridId: 'e8ba68fd-53ec-4728-b12e-bd51e6818588',
          toolbar: supplyAreaToolbar,
          grid: {
            columnData: areaColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    let id = this.modalData.info.id
    this.getdetail(id)
    this.show()
    this.getCurrentTenantUsers()
    this.getRelationList()
    this.getDictItem()

    // 出发change
    this.$set(this.componentConfig[0].grid, 'dataSource', this.datainfo)
  },
  computed: {
    datainfo() {
      return this.modalData.info.relationDTOList
    },
    header() {
      return this.modalData.title
    }
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    getDictItem() {
      let dictCode = 'shareType'
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.stagTypeL = res.data.map((item) => {
              return {
                ...item,
                text: item.name,
                value: item.itemCode
              }
            })
            this.formInfo.supplierTypel = res.data[0].itemCode // 默认共享升级
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取申请类型失败！'),
            type: 'warning'
          })
        })
    },

    // 提交
    confirm(fn) {
      let formInfo = this.info
      let { applyInfo, applyShare } = formInfo
      let validRs = false
      this.$refs.formInfo.validate((valid) => {
        validRs = valid
      })
      if (!validRs) {
        return
      }

      let applyInfoQuery = {
        ...applyInfo,
        applyName: this.formInfo.supplierEnterpriseName,
        applyerOrgId: this.formInfo.companyId,
        applyerOrgName: this.formInfo.applyerOrgName,
        applyerDeptId: this.formInfo.applyDepId,
        applyerDeptName: this.formInfo.applyerDeptName,
        applyerId: this.formInfo.applyUserId,
        applyerName: this.formInfo.applyUser,
        // applyType: this.formInfo.supplierTypel, 大类型 惩罚 、 申诉
        remark: this.formInfo.remark,
        id: this.info.applyInfo.id
      }

      let applyShareQuery = {
        ...applyShare,
        businessType: this.stageInfo.itemCode,
        destinationOrgId: this.parentInfo.id,
        destinationOrgName: this.parentInfo.orgCode,
        destinationOrgCode: this.parentInfo.orgCode,
        destinationOrgDimension: this.parentInfo.orgLevelTypeCode
      }

      let relationListQuery = this.componentConfig[0].grid.dataSource

      if (relationListQuery.length === 0) {
        this.$toast({
          content: this.$t('请至少有一个勾选项！'),
          type: 'warning'
        })
        return
      }

      let query = {
        applyInfo: applyInfoQuery,
        applyShare: applyShareQuery,
        relationList: relationListQuery
      }
      console.log('updata', query)
      this.$loading()

      this.$API.SupplierPunishment.updatashare(query)
        .then((res) => {
          this.$hloading()
          if (!utils.isEmpty(res) && res.code === 200) {
            this.$toast({ content: this.$t('修改成功'), type: 'success' })
            if (!!fn && typeof fn === 'function') {
              fn()
            } else {
              this.$emit('confirm-function', 'reload')
            }
          } else {
            this.$toast({
              content: res.msg || this.$t('修改失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('修改失败，请重试!'),
            type: 'warning'
          })
        })
    },

    confirmSubmit() {
      this.confirm(() => {
        this.$emit('confirm-function', { type: 'jump', id: this.info.applyInfo.id })
      })
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length === 0) {
        this.$toast({ content: this.$t('请勾选数据'), type: 'warning' })
        return
      }

      if (toolbar.id === 'deleteList') {
        let dataSource = this.componentConfig[0].grid.dataSource
        let filterSltList = []
        dataSource.forEach((item) => {
          if (sltList.filter((citem) => citem.id === item.id).length === 0) {
            filterSltList.push(item)
          }
        })
        this.$set(this.componentConfig[0].grid, 'dataSource', filterSltList)
      }
    },
    handleUserChange(data) {
      let { itemData } = data
      this.formInfo.applyUserId = itemData.id
      this.formInfo.applyUser = itemData.userName
      this.getCompany()
    },

    handleChangeStage(data) {
      let { itemData } = data
      this.stageInfo = itemData
    },
    // 副组织
    handleChangeParent(data) {
      let { itemData } = data
      this.parentInfo = itemData
    },

    // 获取当前租户下的所有的用户
    getCurrentTenantUsers() {
      this.$API.supplierAcc.getCurrentTenantUsers().then((res) => {
        this.TenantUserList = res.data
        this.TenantUserList.forEach((item) => {
          let arrl = {
            userName: item.userName,
            id: item.id
          }
          this.applyUserIdData.push(arrl)
          console.log(this.applyUserIdData)
        })
      })
    },

    getRelationList() {
      let organizationId = this.modalData.info.sourceOrgId
      this.$API.SupplierPunishment.getParentOrg({
        organizationId
      })
        .then((res) => {
          console.log(res)
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            this.parentOrgList = [res.data]
            this.formInfo.supplierStage = res.data.id
          } else {
            this.parentOrgList = []
            this.$toast({
              content: this.$t('获取目标组织数据失败！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('获取目标组织失败！'),
            type: 'warning'
          })
        })
    },

    // 获取当前用户下的公司 选择第一个默认选择
    getCompany() {
      this.$loading()
      let { applyUserId } = this.formInfo
      this.$API.supplierAcc
        .getCompanysByUserId({ userId: applyUserId })
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            // 获取默认选择的公司
            this.formInfo.companyId = res.data[0].id
            // this.formInfo.applyerOrgName = res.data[0].orgName
            this.applyCompanyData = res.data
            // this.getDepart()
            this.$hloading()
          } else {
            this.$hloading()
            this.formInfo.companyId = ''
            this.formInfo.applyerOrgName = ''
            this.applyCompanyData = []
            this.$toast({
              content: res.msg || this.$t('获取公司列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.companyId = ''
          this.formInfo.applyerOrgName = ''
          this.applyCompanyData = []
          this.$toast({
            content: error.msg || this.$t('获取公司列表失败，请重试！'),
            type: 'warning'
          })
        })
    },
    // 获取编辑的详情
    getdetail(id) {
      this.$API.SupplierPunishment.getDetail({ id }).then((res) => {
        this.info = res.data
        // console.log(this.info)
        this.formInfo.supplierEnterpriseName = this.modalData.info.applyName
        this.formInfo.supplierTypel = this.modalData.info.businessType
        this.formInfo.remark = this.modalData.info.remark
        this.formInfo.supplierStage = this.modalData.info.destinationOrgId
        this.formInfo.orgName = this.modalData.info.sourceOrgName
        this.formInfo.applyUserId = this.info.applyInfo.applyerId
        this.formInfo.companyId = this.info.applyInfo.applyerOrgId
        this.formInfo.applyDepId = this.info.applyInfo.applyerDeptId
      })
    },
    // 获取当前用户下公司 的 部门
    getDepart() {
      if (!this.formInfo.companyId) {
        return
      }
      this.$loading()
      this.$API.supplierAcc
        .getDepartmentsByUserId({
          userId: this.formInfo.applyUserId,
          companyOrganizationId: this.formInfo.companyId
        })
        .then((res) => {
          this.$hloading()
          console.log('applyDepartData', this.applyDepartData)
          this.applyDepartData = res.data
          if (this.applyDepartData && this.applyDepartData.length > 0) {
            this.formInfo.applyDepId = this.applyDepartData[0].id
            this.formInfo.applyerDeptName = this.applyDepartData[0].orgName
          } else {
            this.formInfo.applyDepId = ''
            this.formInfo.applyerDeptName = ''
            this.$toast({
              content: res.msg || this.$t('获取部门列表失败，请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.formInfo.applyDepId = ''
          this.formInfo.applyerDeptName = ''
          this.$toast({
            content: error.msg || this.$t('获取部门列表失败，请重试！'),
            type: 'warning'
          })
        })
    },

    handleCompanyChange(data) {
      let { itemData } = data
      console.log(data, itemData)
      // this.formInfo.companyId = itemData.id
      this.formInfo.applyerOrgName = itemData.orgName
      this.getDepart()
    }
  }
}
</script>

<style lang="scss">
/deep/.height-32 {
  height: 32px !important;
}
.upgrade-dialog {
  padding: 20px; // --*

  /deep/.height-32 {
    height: 32px !important;
  }

  .label {
    font-size: 14px;
    color: #292929;
    font-weight: 400;
    margin-bottom: 10px;
  }
}
</style>
