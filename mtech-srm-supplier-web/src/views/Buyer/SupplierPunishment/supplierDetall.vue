<template>
  <div class="supplierDetall-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="titles-box">
        <div class="mian-line fbox">
          <div class="mian-title">
            <template v-if="!!info && !!info.applyInfo">
              {{ info.applyInfo.applyName || '--' }}
            </template>
          </div>
          <div class="btns-box fbox">
            <div class="invite-btn" @click="onBack">
              <mt-button css-class="e-info">{{ $t('返回') }}</mt-button>
            </div>
            <template v-if="canEdit">
              <div class="invite-btn" @click="saveText">
                <mt-button css-class="e-info">{{ $t('保存') }}</mt-button>
              </div>
              <div class="invite-btn" @click="saveAndSubmit">
                <mt-button css-class="e-info">{{ $t('保存并提交') }}</mt-button>
              </div>
            </template>
          </div>
        </div>

        <div class="sub-title fbox">
          <div class="normal-title" v-if="!!info && !!info.applyInfo">
            {{ $t('申请单编码') }}：{{ info.applyInfo.applyCode }}
          </div>
          <div class="normal-title" v-if="!!info && !!info.applyInfo">
            {{ $t('创建人') }}：{{ info.applyInfo.createUserName }}
          </div>
          <div class="normal-title" v-if="!!info && !!info.applyInfo">
            {{ $t('创建时间') }}：{{ info.applyInfo.createTime }}
          </div>
          <div class="normal-title" v-if="!!info && !!info.applyInfo">
            {{ $t('申请人') }}：{{ info.applyInfo.applyerName }}
          </div>
          <div class="normal-title" v-if="!!info && !!info.applyInfo">
            {{ $t('申请人部门') }}：{{ info.applyInfo.applyerDeptName }}
          </div>
        </div>
        <div class="sub-title fbox mr20">
          <div class="normal-title-dan" v-if="!!info && !!info.applyInfo">
            <span>{{ $t('类型') }}</span
            >：{{ this.dictList[info.applyShare.businessType] }}
          </div>
          <div class="normal-title-dan" v-if="!!info && !!info.applyInfo">
            <span>{{ $t('源组织') }}</span
            >：{{ info.applyShare.sourceOrgName }}
            <!-- <span class="b-color">{{ info.applyInfo.applyCode }}</span> -->
          </div>
          <div class="normal-title-dan" v-if="!!info && !!info.applyInfo">
            <span>{{ $t('目标组织') }}</span
            >：{{ info.applyShare.destinationOrgName }}
            <!-- <span class="b-color">{{ info.applyInfo.applyCode }}</span> -->
          </div>
        </div>
        <div class="scroll-box fbox">
          <div class="normal-title-box" v-for="(item, index) in relationList" :key="index">
            <div class="gong-title">{{ item.supplierEnterpriseName || '--' }}</div>
            <div class="gong-id">{{ item.supplierEnterpriseCode || '--' }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 头部结束 -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- <keep-alive> -->
    <template v-if="!!applyInfo.id">
      <detail-edit
        v-if="selectIndex === 0"
        :id="applyInfo.id"
        :apply-info="applyInfo"
        :can-edit="canEdit"
        ref="editRef"
      ></detail-edit>
      <operator-history
        v-if="selectIndex === 1"
        :id="applyInfo.id"
        :apply-info="applyInfo"
      ></operator-history>
    </template>
    <!-- </keep-alive> -->
  </div>
</template>

<script>
import operatorHistory from './components/operatorHistory.vue'
import detailEdit from './components/detailEdit.vue'
import utils from '../../../utils/utils'
export default {
  components: {
    detailEdit,
    operatorHistory
  },
  filters: {
    filterNo: function (value) {
      if (!value) {
        return 'D'
      }
      return value.substr(0, 1)
    }
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('单据配置')
        },
        {
          title: this.$t('操作记录')
        }
      ],
      id: '',
      canEdit: false, //  item.applyStatus === 20 || item.applyStatus === 40 不能编辑
      info: {},
      relationList: [],
      dictList: []
    }
  },
  computed: {
    applyInfo() {
      return !!this.info && this.info.applyInfo ? this.info.applyInfo : {}
    }
  },
  created() {
    let { id } = this.$route.query
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getDictItem()
    this.getAccessDetail(id)
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    // 保存 富文本 // console.log(this.$refs.MtRichTextEditor.ejsRef.getHtml()) // console.log(this.$refs.MtRichTextEditor.ejsRef.getText())
    saveText(fn = '') {
      this.$loading()
      let applyReason = ''
      try {
        applyReason = this.$refs.editRef.$refs.MtRichTextEditor.ejsRef.getText()
      } catch (error) {
        this.$hloading()
      }
      // console.log(this.$refs.editRef.$refs.MtRichTextEditor.ejsRef.getText());
      let { applyInfo, applyShare, relationList } = this.info
      let query = {
        applyInfo: {
          ...applyInfo,
          applyReason
        },
        applyShare,
        relationList
      }

      this.$API.SupplierPunishment.updatashare(query)
        .then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('更新共享详情成功！'),
              type: 'success'
            })
            if (!!fn && typeof fn === 'function') {
              fn()
            }
          } else {
            this.$toast({
              content: this.$t('更新共享详情失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('保存详情失败，请重试!'),
            type: 'warning'
          })
        })
    },
    // 保存并提交
    saveAndSubmit() {
      this.$loading()
      this.saveText(() => {
        this.$API.SupplierPunishment.applySubmit({
          applyIdList: [this.info.applyInfo.id]
        }).then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('提交共享详情成功!'),
              type: 'success'
            })

            this.getAccessDetail(this.id)
          } else {
            this.$toast({
              content: this.$t('提交共享详情失败，请重试!'),
              type: 'warning'
            })
          }
        })
      })
    },

    handleSelectTab(index) {
      this.selectIndex = index
    },
    getDictItem() {
      let dictCode = 'shareType'
      this.$API.supplierResources
        .getDictItem({
          dictCode
        })
        .then((res) => {
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            res.data.forEach((item) => {
              this.dictList[`${item.itemCode}`] = item.name
            })
          }
        })
        .catch(() => {})
    },

    getAccessDetail(id) {
      this.$loading()
      this.$API.SupplierPunishment.getDetail({ id }).then((res) => {
        this.$hloading()
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.info = res.data
          this.relationList = res.data.relationList
          this.canEdit =
            res.data.applyInfo.applyStatus === 20 || res.data.applyInfo.applyStatus === 40
              ? false
              : true
        } else {
          this.$toast({
            content: this.$t('获取共享详情失败，请重试!'),
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss">
.mt-tabs {
  margin: 10px 0;
  width: 100%;
}
</style>
<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.supplierDetall-container {
  height: 100%;
  padding-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .titles-box {
      width: 100%;
      flex-direction: column;
      justify-content: space-between;
      .mian-title {
        font-size: 20px;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }

      .mian-line {
        width: 100%;
        justify-content: space-between;
      }

      .scroll-box {
        width: 100%;
        display: flex;
        white-space: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        margin-top: 20px;

        .normal-title-box {
          height: 88px;
          background: rgba(255, 255, 255, 1);
          border-radius: 8px;
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
          padding: 0 20px;
        }

        .gong-title {
          padding: 20px 0 0 0;
          font-size: 14px;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
        }

        .gong-id {
          padding: 20px 0 0 0;
          font-size: 14px;
          font-weight: 600;
          color: rgba(0, 70, 156, 1);
        }
      }

      .sub-title {
        width: 100%;
        font-size: 12px;
        color: rgba(41, 41, 41, 1);
        margin-top: 10px;

        .normal-title {
          font-size: 12px;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
        }

        .normal-title-dan {
          font-size: 14px;
          color: rgba(41, 41, 41, 1);
          margin-right: 30px;
          font-weight: 600;

          span {
            font-weight: 600;
          }

          .b-color {
            color: #00469c;
          }
        }
      }

      .mr20 {
        margin-top: 20px;
      }
    }

    .btns-box {
      align-items: center;
      font-size: 14px;
      max-width: 300px;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      justify-content: flex-end;

      .invite-btn {
        margin-right: 20px;
        cursor: pointer;
        font-weight: 600;
      }
      .invite-btn:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
