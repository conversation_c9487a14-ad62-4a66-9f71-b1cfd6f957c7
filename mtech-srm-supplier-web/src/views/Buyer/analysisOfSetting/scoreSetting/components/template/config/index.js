//绩效考评模板Tab
import Vue from 'vue'
import { i18n } from '@/main.js'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
    // permission: ["O_02_0049"],
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
    // permission: ["O_02_0052"],
  },
  {
    id: 'Edit',
    icon: 'icon_solid_Edit',
    title: i18n.t('编辑')
    // permission: ["O_02_0052"],
  }
  // {
  //   id: "EXdownload",
  //   icon: "icon_solid_Download ",
  //   title: "下载Excel模板",
  //   // permission: ["O_02_0050"],
  // },
  // {
  //   id: "EXimport",
  //   icon: "icon_solid_upload   ",
  //   title: "Excel导入",
  //   // permission: ["O_02_0051"],
  // },
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'raterName',
    headerText: i18n.t('姓名')
  },
  {
    field: 'raterAccount',
    headerText: i18n.t('账号')
  },
  {
    field: 'orgCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'orgName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('部门')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('负责品类'),
    template: () => {
      return {
        template: Vue.component('categoryDTOList', {
          template: `<div style="text-align:left">
              <span v-for='(item,index) in data.categoryDTOList' :key='index'>
              {{item.categoryName}}
              <span v-if='index != data.categoryDTOList.length-1'>、</span>
            </span>
          </div>`,
          data: function () {}
        })
      }
    }
  },
  {
    field: 'planName',
    headerText: i18n.t('模板')
  },
  {
    field: 'indexDTOList',
    headerText: i18n.t('负责指标'),
    template: () => {
      return {
        template: Vue.component('indexDTOList', {
          template: `<div style="text-align:left">
              <span v-for='(item,index) in data.indexDTOList' :key='index'>
              {{item.indexName}}
              <span v-if='index != data.indexDTOList.length-1'>、</span>
            </span>
          </div>`,
          data: function () {}
        })
      }
    }
  },

  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('createTime', {
          template: `
              <div class="time-box">
                {{ data.createTime }}
              </div>`
        })
      }
    }
    // queryType: "date",
    // format: "yyyy-MM-dd <br/> hh:mm:ss",
  }
]

export const pageConfig = (url) => [
  {
    gridId: '16096664-239a-429d-833c-4e3a5faf417d',
    toolbar,
    grid: {
      // allowTextWrap: true,
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
