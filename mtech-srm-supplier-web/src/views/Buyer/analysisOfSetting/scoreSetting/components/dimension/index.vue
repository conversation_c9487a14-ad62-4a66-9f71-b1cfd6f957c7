<template>
  <!-- 品类策略 -->
  <div class="score-setting-dimension">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      delBuyerAssessRequest: {
        ids: []
      },
      //列表调用
      pageConfig: pageConfig(this.$API.analysisOfSetting.categoryStrategy)
    }
  },
  methods: {
    // 点击单元格内按钮  编辑 加 启用等按钮
    handleClickCellTool(e) {
      if (e.tool.id == 'Start') {
        // 启用
        this.handleHeparture([e.data.id], 1)
      } else if (e.tool.id == 'Stop') {
        // 禁用操作
        this.handleHeparture([e.data.id], 2)
      }
      // else if (e.tool.id == "edit") {
      //   // 编辑维度操作
      //   this.editPolicy(e.data);
      // } else if (e.tool.id == "delete") {
      //   // 删除维度操作
      //   this.DeleteInline(e.data);
      // }
    },
    //更新配置的状态(行内操作+批量操作)
    handleHeparture(ids, num) {
      //状态 1: this.$t("启用"), 2: "停用"
      let status = 0
      if (num == 2) {
        status = 1
      } else {
        status = 0
      }
      let _params = {
        ids,
        status: num
      }
      let _statusMap = [this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')} ${_statusMap[status]}？`
        },
        success: () => {
          this.$API.analysisOfSetting.categorySupdateStatus(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Enable' ||
          e.toolbar.id == 'Delete' ||
          e.toolbar.id == 'Edit' ||
          e.toolbar.id == 'Disable')
      ) {
        //this.$t("提示弹框")
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id == 'Add') {
        this.analysisOfSettingAdd()
      } //删除
      else if (e.toolbar.id == 'Delete') {
        this.analysisOfSettingDelete(_selectGridRecords)
      } //编辑
      else if (e.toolbar.id == 'Edit') {
        // if (_selectGridRecords.length > 1) {
        //   this.$toast({ content: "一次只能编辑一行!", type: "warning" });
        //   return;
        // }
        // this.editPolicy(_selectGridRecords);
        return
      } //启用
      else if (e.toolbar.id == 'Enable') {
        this.handleBatchStart(_selectGridRecords, 1)
      } //停用
      else if (e.toolbar.id == 'Disable') {
        this.handleBatchStart(_selectGridRecords, 2)
      } //Excel导入
      else if (e.toolbar.id == 'EXimport') {
        this.analysisOfSettingEXimport()
      } //Excel模板下载
      else if (e.toolbar.id == 'EXdownload') {
        this.analysisOfSettingEXdownload()
      }
    },
    //批量提交操作
    handleBatchStart(_selectGridRecords, num) {
      //状态 0: this.$t("启用"), 1: "停用"
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _unRecords = _selectGridRecords.filter((s) => s.status == num)
      if (_unRecords.length > 0) {
        //选中数据中，存在‘启用’状态
        this.$toast({
          content:
            num == 2
              ? this.$t('勾选数据中，存在停用状态的数据')
              : this.$t('勾选数据中，存在启用状态的数据'),
          type: 'warning'
        })
        return
      } else {
        this.handleHeparture(_selectIds, num)
      }
    },
    //新增配置
    analysisOfSettingAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/components/dimensionDialog" */ './components/dimensionDialog.vue'
          ),
        data: {
          title: this.$t('品类策略')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    //删除配置
    analysisOfSettingDelete(val) {
      val.forEach((item) => {
        this.delBuyerAssessRequest.ids.push(item.id)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.analysisOfSetting.categorydel(this.delBuyerAssessRequest).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })

            this.$refs.templateRef.refreshCurrentGridData()
          })
          this.delBuyerAssessRequest.ids = []
        }
      })
    },
    // 编辑
    // editPolicy(policyData) {
    //   // 判断  policyData 类型 是数组还是对象
    //   // if ((policyData.constructor == Array) == true) {
    //   //   policyData = policyData[0];
    //   // }
    //   const _this = this;
    //   this.$dialog({
    //     modal: () => import("./components/dimensionDialog.vue"),
    //     data: {
    //       title: this.$t("编辑计划模板"),
    //       isEdit: true,
    //       policyData,
    //     },
    //     success: () => {
    //       _this.$refs.templateRef.refreshCurrentGridData();
    //     },
    //   });
    // },

    //更新配置的状态(行内操作+批量操作)
    // handleUpdateConfigStatus(ids, status) {
    //   //状态 0: this.$t("启用"), 1: "停用"
    //   let _params = {
    //     ids,
    //     status: status,
    //   };
    //   let _statusMap = [this.$t("启用"), "停用"];
    //   this.$dialog({
    //     data: {
    //       title: this.$t("提示"),
    //       message: `确认更新状态为${_statusMap[status]}？`,
    //     },
    //     success: () => {
    //       this.$API.analysisOfSetting
    //         .categorySupdateStatus(_params)
    //         .then(() => {
    //           this.$toast({
    //             content: this.$t("操作成功"),
    //             type: "success",
    //           });
    //           this.$refs.templateRef.refreshCurrentGridData();
    //         });
    //     },
    //   });
    // },
    analysisOfSettingEXimport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ './components/Excelimport.vue'
          ),
        data: {
          title: this.$t('上传/导入')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    analysisOfSettingEXdownload() {
      console.log('Excel模板下载')
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-dimension {
  height: 100%;
}
</style>
