<template>
  <!-- 指标定义Tab -->
  <div class="score-setting-target">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
import MixIn from '../../config/mixin'

export default {
  mixins: [MixIn],
  data() {
    return {
      delBuyerAssessRequest: {
        ids: []
      },
      index: 0,
      pageConfig: pageConfig(this.$API.analysisOfSetting.cateexceptpageQuery)
    }
  },
  mounted() {},
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'supplierCode') {
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            supplierCode: e.data.supplierCode,
            orgCode: e.data.orgCode
          }
        })
      }
    },
    handleClickToolBar(e) {
      //勾选的每一列的数据
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'Delete') {
        //提示弹框
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id == 'Add') {
        this.analysisOfSettingAdd()
      } //删除
      else if (e.toolbar.id == 'Delete') {
        this.analysisOfSettingDelete(_selectGridRecords)
      } //Excel导入
      else if (e.toolbar.id == 'EXimport') {
        this.analysisOfSettingEXimport()
      } //Excel模板下载
      else if (e.toolbar.id == 'EXdownload') {
        this.analysisOfSettingEXdownload()
      }
    },
    //新增配置
    analysisOfSettingAdd() {
      this.$dialog({
        modal: () => import('./components/targetDialog.vue'),
        data: {
          title: this.$t('例外供应商'),
          ownerOrg: this.ownerOrg
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //删除配置
    analysisOfSettingDelete(val) {
      console.log(val)
      val.forEach((item) => {
        this.delBuyerAssessRequest.ids.push(item.id)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.analysisOfSetting.cateexceptiondel(this.delBuyerAssessRequest).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
          this.delBuyerAssessRequest.ids = []
        }
      })
    },
    //Excel导入
    analysisOfSettingEXimport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ './components/Excelimport.vue'
          ),
        data: {
          title: this.$t('上传/导入')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //Excel模板下载
    analysisOfSettingEXdownload() {
      console.log(this.$t('Excel模板下载逻辑'))
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting-target {
  height: 100%;
  .filter-org {
    background: #fff;
    padding: 10px;
    /deep/.owner-org-tree {
      width: 300px;
    }
  }
}
</style>
