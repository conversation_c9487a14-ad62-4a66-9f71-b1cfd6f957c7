//指标定义Tab
import { i18n } from '@/main.js'
import Vue from 'vue'
const toolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
    // permission: ["O_02_0044"],
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
    // permission: ["O_02_0045"],
  }
  // {
  //   id: "EXimport",
  //   icon: "icon_solid_upload   ",
  //   title: "Exceli18n.t("导入"),
  //   // permission: ["O_02_0046"],
  // },
  // {
  //   id: "EXdownload",
  //   icon: "icon_solid_Download ",
  //   title: "Excel模板下载",
  //   // permission: ["O_02_0047"],
  // },
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cellTools: []
  },
  {
    field: 'supplierEnterpriseName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    field: 'remark',
    headerText: i18n.t('原因')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    },
    template: () => {
      return {
        template: Vue.component('createTime', {
          template: `
              <div class="time-box">
                {{ data.createTime }}
              </div>`
        })
      }
    }
    // queryType: "date",
    // format: "yyyy-MM-dd <br/> hh:mm:ss",
  }
]

export const pageConfig = (url) => [
  {
    gridId: 'cb980f67-b1c2-4de3-921e-33ace7e9fb34',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
