<template>
  <!-- 评分配置页面 -->
  <div class="score-setting">
    <mt-template-page ref="templateRef" :use-tool-template="false" :template-config="pageConfig">
      <!-- 维度设置 -->
      <tab-dimension slot="slot-0"></tab-dimension>
      <!-- 指标定义 -->
      <tab-target slot="slot-1"></tab-target>
      <!-- 绩效考评模板 -->
      <tab-template slot="slot-2" :template-type-list="templateTypeList"></tab-template>
      <!-- 公司默认评分人 -->
      <tab-rater slot="slot-3" :template-type-list="templateTypeList"></tab-rater>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //品类策略
    tabDimension: () =>
      import(
        /* Buyer/analysisOfSetting/scoreSetting/dimension */
        /* webpackChunkName: "router/Buyer/analysisOfSetting/scoreSetting/dimension" */ './components/dimension'
      ),
    //例外供应商
    tabTarget: () =>
      import(
        /* webpackChunkName: "router/Buyer/analysisOfSetting/scoreSetting/target" */ './components/target'
      ),
    //手动评分人设置
    tabTemplate: () =>
      import(
        /* webpackChunkName: "router/Buyer/analysisOfSetting/scoreSetting/template" */ './components/template'
      )
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: '178220ce-ac5e-4dc7-b396-ce60d2fa4a35',
          title: this.$t('品类策略')
        },
        {
          gridId: 'a594fbfb-a7b8-4ebb-bd82-c45e2bdffe54',
          title: this.$t('例外供应商')
        },
        {
          gridId: '51b6d1b8-3f1a-4fd7-b0be-fa777c5c8ec1',
          title: this.$t('手动评分人设置')
        }
      ],
      templateTypeList: [] //模板类型列表
    }
  },
  mounted() {
    this.getTemplateTypeList()
  },
  methods: {
    getTemplateTypeList() {
      //模板类型列表 - templateTypeList
      this.$API.performanceScoreSetting
        .dictionaryGetList({
          dictCode: 'PERFORMANCE_TEMPLATE_TYPE'
        })
        .then((res) => {
          let _list = [...res.data]
          _list.forEach((e) => {
            e.cssClass = 'user-column'
          })
          this.templateTypeList = _list
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.score-setting {
  height: 100%;
  width: 100%;
  /deep/ .mt-data-grid {
    .status-label {
      font-size: 12px;
      padding: 4px;
      border-radius: 2px;
      &.status-disable {
        color: #9baac1;
        background: rgba(155, 170, 193, 0.1);
      }
      &.status-enable {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
  }
}
</style>
