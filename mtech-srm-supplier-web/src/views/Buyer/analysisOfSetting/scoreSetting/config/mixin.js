// import { i18n } from '@/main.js'
export default {
  methods: {
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (e.toolbar.id == 'Add') {
        this.handleAddConfig()
        return
      } else if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Start') {
        this.handleBatchStart(_selectRecords)
      } else if (e.toolbar.id == 'Stop') {
        this.handleBatchStop(_selectRecords)
      } else if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectRecords)
      } else if (e.toolbar.id == 'Edit') {
        if (_selectRecords.length > 1) {
          this.$toast({ content: this.$t('只支持修改一行数据'), type: 'warning' })
        } else {
          //编辑操作
          this.handleEditConfig(_selectRecords[0])
        }
      }
    },
    //单元格icons，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        //编辑操作
        this.handleEditConfig(e.data)
      } else if (e.tool.id == 'Start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.id], 0)
      } else if (e.tool.id == 'Stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.id], 1)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteConfig([e.data.id])
      }
    },
    // 单元格title，点击
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },
    //批量启动操作
    handleBatchStart(_selectRecords) {
      //状态 0: i18n.t("启用"), 1: "停用"
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _unRecords = _selectRecords.filter((s) => s.enabled < 1)
      console.log('当前-状态', _selectRecords, _unRecords)
      if (_unRecords.length > 0) {
        //选中数据中，存在‘启用’状态
        this.$toast({
          content: this.$t("勾选数据中，存在'启用'状态的数据."),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 0)
      }
    },
    //批量停用操作
    handleBatchStop(_selectRecords) {
      //状态 0: i18n.t("启用"), 1: "停用"
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _unRecords = _selectRecords.filter((s) => s.enabled > 0)
      if (_unRecords.length > 0) {
        //选中数据中，存在‘停用’状态
        this.$toast({
          content: this.$t("勾选数据中，存在'停用'状态的数据."),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 1)
      }
    },
    //批量删除操作
    handleBatchDelete(_selectRecords) {
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //新增配置
    handleAddConfig() {},
    //编辑配置
    handleEditConfig(data) {
      console.log('mixin-EditConfig', data)
    },
    //更新配置的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      console.log('mixin-UpdateConfigStatus', ids, status)
    },
    //删除配置
    handleDeleteConfig(ids) {
      console.log('mixin-DeleteConfig', ids)
    },
    /**
     * 拆解树为数组，并且不会对原始对象进行修改，原始对象子集列表也不会进行删除。
     * @param tree          {Array}          树形数组
     * @param children_key  {String}         子集对象 'key'
     * @return {[{}]} 树形被拆解后的数组
     */
    flatTreeData(tree, children_key = 'children') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    }
  }
}
