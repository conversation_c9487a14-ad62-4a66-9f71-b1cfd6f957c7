import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const listColumnData = [
  {
    width: '150',
    field: 'applyCode',
    headerText: i18n.t('申请单编码'),
    cellTools: []
  },
  {
    width: '200',
    field: 'supplierInternalCode',
    headerText: i18n.t('供应商编号-SRM')
    // ignore: true
  },
  {
    width: '200',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号-SAP')
    // ignore: true
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'complianceScreeningResult',
    headerText: i18n.t('合规筛查结果'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('通过'),
        0: i18n.t('风险供应商'),
        '': ''
      }
    }
  },
  {
    field: 'legalApproveResult',
    headerText: i18n.t('法务审核结果'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('通过'),
        0: i18n.t('不通过'),
        2: i18n.t('审核中'),
        '': ''
      }
    }
  },
  {
    width: '210',
    field: 'sceneDefineName',
    headerText: i18n.t('场景')
  },
  {
    width: '210',
    field: 'authProjectCode',
    headerText: i18n.t('品类认证项目编号')
  },
  {
    width: '150',
    field: 'orgName',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'blocked',
    headerText: i18n.t('是否违规失信合作方'),
    valueConverter: {
      type: 'map',
      map: {
        false: i18n.t('否'),
        true: i18n.t('是'),
        '': ''
      }
    }
  },
  {
    width: '150',
    field: 'effectiveType',
    headerText: i18n.t('生效类型'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: { 1: i18n.t('预生效'), 2: i18n.t('生效') }
    }
  },
  // {
  //   width: "150",
  //   field: "name",
  //   headerText: i18n.t("样品认证"),
  // },
  // {
  //   width: "150",
  //   field: "name",
  //   headerText: i18n.t("小批量认证"),
  // },
  {
    width: '150',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: {
        10: i18n.t('待提交'),
        20: i18n.t('待审批'),
        30: i18n.t('已驳回'),
        40: i18n.t('已完成'),
        50: i18n.t('已关闭'),
        60: i18n.t('待处理'),
        70: i18n.t('中止'),
        80: i18n.t('已下发'),
        90: i18n.t('下发失败'),
        100: i18n.t('下发成功')
      }
    }
  },
  {
    width: '150',
    field: 'failReason',
    headerText: i18n.t('同步备注'),
    formatter: (column, data) => {
      return data['failReason'] && data['failReason'] != '' ? data['failReason'] : '--'
    }
  },
  {
    width: '150',
    field: 'scoreResult',
    headerText: i18n.t('现场审查分值')
  },
  {
    width: '150',
    field: 'reportResult',
    headerText: i18n.t('意见')
  },
  {
    width: '150',
    field: 'publicityDays',
    headerText: i18n.t('已公示时间'),
    valueConverter: {
      type: 'function', //filter可不传，如果未传，则原数据返回
      filter: (data) => {
        if (data === 0) return ''
        return dayjs(+data).format('YYYY-MM-DD')
      }
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
    // headerTextAlign: "center",
  },
  {
    width: '150',
    field: 'createDate',
    headerText: i18n.t('创建日期'),
    // searchOptions:{
    //   type:"date",
    //   dateFormat:"YYYY-mm-dd"
    // },
    valueConverter: {
      type: 'function', //filter可不传，如果未传，则原数据返回
      filter: (data) => {
        return dayjs(+data).format('YYYY-MM-DD')
      }
    }
  }
  // {
  //   width: "150",
  //   field: "name",
  //   headerText: i18n.t("申请原因"),
  // },
]

export const pageConfig = [
  {
    gridId: '0e58d0af-4362-4c15-b636-a21475032813',
    useToolTemplate: false,
    buttonQuantity: 7,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'Add',
            icon: 'icon_solid_edit',
            title: i18n.t('新增')
          },
          {
            id: 'Submit',
            icon: 'icon_solid_edit',
            title: i18n.t('提交')
          },
          {
            id: 'Edit',
            icon: 'icon_solid_edit',
            title: i18n.t('编辑')
          },

          {
            id: 'Delete',
            icon: 'icon_solid_edit',
            title: i18n.t('删除')
          },

          {
            id: 'Issued',
            icon: 'icon_solid_edit',
            title: i18n.t('下发')
          },
          {
            id: 'audit',
            title: i18n.t('查看OA审批'),
            icon: 'icon_solid_editsvg'
          },
          {
            id: 'export',
            title: i18n.t('导出')
          }
          // {
          //   id: "change",
          //   icon: "icon_solid_edit",
          //   title: i18n.t("公示"),
          // },
          // {
          //   id: "change",
          //   icon: "icon_solid_edit",
          //   title: i18n.t("发布"),
          // },
          // {
          //   id: "change",
          //   icon: "icon_solid_edit",
          //   title: i18n.t("供方淘汰"),
          // },
        ],
        ['Filter', 'refresh', 'setting']
      ]
    },
    grid: {
      lineSelection: true,
      columnData: listColumnData,
      asyncConfig: {
        url: '/supplier/tenant/buyer/apply/effective/pageQuery'
      },
      frozenColumns: 3
    }
  }
]

export const defaultToolbar = {
  useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [
    [
      {
        id: 'Add',
        icon: 'icon_solid_edit',
        title: i18n.t('新增')
      },
      {
        id: 'Submit',
        icon: 'icon_solid_edit',
        title: i18n.t('提交')
      },
      {
        id: 'Edit',
        icon: 'icon_solid_edit',
        title: i18n.t('编辑')
      },

      {
        id: 'Delete',
        icon: 'icon_solid_edit',
        title: i18n.t('删除')
      },

      {
        id: 'Issued',
        icon: 'icon_solid_edit',
        title: i18n.t('下发')
      },

      {
        id: 'audit',
        title: i18n.t('查看OA审批'),
        icon: 'icon_solid_editsvg'
      },
      {
        id: 'export',
        title: i18n.t('导出')
      }
    ],
    ['Filter', 'refresh', 'setting']
  ]
}
