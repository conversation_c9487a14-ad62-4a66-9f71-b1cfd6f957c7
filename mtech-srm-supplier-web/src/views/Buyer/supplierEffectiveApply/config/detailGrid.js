import { i18n } from '@/main.js'
import Vue from 'vue'
import DetailGridCellTemplate from '../components/detailGridCellTemplate'
import DetailGridCellExtTemplate from '../components/detailGridCellExtTemplate'
import SchemaGroupCellChange from '../components/schemaGroupCellChange.vue'

export const gridColumnData = [
  {
    type: 'checkbox',
    width: '60px'
  },
  // {
  //   // type: "checkbox",
  //   width: 100,
  //   template: () => {
  //     return {
  //       template: Vue.component("checkbox", {
  //         template: `<div v-if="data.activeStatus == 0" class="e-checkbox-wrapper e-css">
  //           <input class="e-checkselect" type="checkbox" :disabled="true">
  //           <span class="e-frame e-icons">
  //           </span><span class="e-label"> </span>
  //         </div>`,
  //         data: function () {
  //           {
  //           }
  //         },
  //       }),
  //     };
  //   },
  // },
  {
    width: 0,
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织Name'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: 0,
    field: 'purchaseOrgId',
    headerText: i18n.t('purchaseOrgId'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '250',
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaseOrgCode', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购组织')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      // let purchaseOrgName =
      return data['purchaseOrgCode'] + '-' + data['purchaseOrgName']
    }
  },
  {
    width: '150',
    field: 'currency',
    headerText: i18n.t('币种'),
    headerTemplate: () => {
      return {
        template: Vue.component('currency', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('币种')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['currencyName']
    }
  },
  {
    width: 0,
    field: 'currencyName',
    headerText: i18n.t('currencyName'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'tax',
    headerText: i18n.t('税码'),
    headerTemplate: () => {
      return {
        template: Vue.component('tax', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('税码')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['taxName']
    }
  },
  {
    width: 0,
    field: 'taxName',
    headerText: i18n.t('taxName'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'transferFactory',
    headerText: i18n.t('是否转厂'),
    headerTemplate: () => {
      return {
        template: Vue.component('transferFactory', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('是否转厂')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['transferFactory'] === 1
        ? i18n.t('是')
        : data['transferFactory'] === 0
        ? i18n.t('否')
        : ''
    }
  },
  {
    width: '150',
    field: 'confirmControl',
    headerText: i18n.t('确认控制'),
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['confirmControlName']
    }
  },
  {
    width: 0,
    field: 'confirmControlName',
    headerText: i18n.t('confirmControlName'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'payCondition',
    headerText: i18n.t('付款条件'),
    headerTemplate: () => {
      return {
        template: Vue.component('paymentTerm', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('付款条件')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      console.log('paymentTermsNamepaymentTermsName', data)
      return data['payConditionName']
    }
  },
  {
    width: 0,
    field: 'payConditionName',
    headerText: i18n.t('付款条件'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'quoteAttr',
    headerText: i18n.t('报价属性'),
    headerTemplate: () => {
      return {
        template: Vue.component('paymentTerm', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('报价属性')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['quoteAttrName']
    }
  },
  {
    width: 0,
    field: 'quoteAttrName',
    headerText: i18n.t('报价属性名称'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'priceEffectiveMethod',
    headerText: i18n.t('价格生效方式'),
    headerTemplate: () => {
      return {
        template: Vue.component('paymentTerm', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('价格生效方式')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['priceEffectiveMethodName']
    }
  },
  {
    width: 0,
    field: 'priceEffectiveMethodName',
    headerText: i18n.t('价格生效方式名称'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'drawer',
    headerText: i18n.t('出票方'),
    headerTemplate: () => {
      return {
        template: Vue.component('drawer', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('出票方')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['drawerName']
    }
  },
  {
    width: 0,
    field: 'drawerName',
    headerText: i18n.t('出票方'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '250',
    field: 'schemeGroup',
    headerText: i18n.t('方案组'),
    headerTemplate: () => {
      return {
        template: Vue.component('schemeGroup', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('方案组')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: SchemaGroupCellChange
      }
    },
    formatter: (column, data) => {
      return data['schemeGroup'] + '-' + data['schemeGroupName']
    }
  },
  {
    width: 0,
    field: 'schemeGroupName',
    headerText: i18n.t('方案组'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'tradeTerm',
    headerText: i18n.t('国际贸易条件'),
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['tradeTerm'] + '-' + data['tradeTermName']
    }
  },
  {
    width: 0,
    field: 'tradeTermName',
    headerText: i18n.t('国际贸易条件'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'tradeTermSecond',
    headerText: i18n.t('国际贸易条件（部分2）'),
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    }
  },
  {
    width: '150',
    field: 'dateControl',
    headerText: i18n.t('定价日期控制'),
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['dateControlName']
    }
  },
  {
    width: 0,
    field: 'dateControlName',
    headerText: i18n.t('定价日期控制'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'orderAddress',
    headerText: i18n.t('订货地址'),
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      console.log('orderAddressNameorderAddressName', data)
      return data['orderAddressName']
    }
  },
  {
    width: 0,
    field: 'orderAddressName',
    headerText: i18n.t('订货地址'),
    editTemplate: function () {
      return {
        template: DetailGridCellExtTemplate
      }
    }
  },
  {
    width: '150',
    field: 'activeStatus',
    headerText: i18n.t('生效状态'),
    headerTemplate: () => {
      return {
        template: Vue.component('activeStatus', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('生效状态')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: function () {
      return {
        template: DetailGridCellTemplate
      }
    },
    formatter: (column, data) => {
      return data['activeStatus'] == 1
        ? i18n.t('生效')
        : data['activeStatus'] == 0
        ? i18n.t('失效')
        : ''
    },
    allowEditing: false
  }
]

export const attachmentColumnData = [
  { type: 'checkbox', width: '60px' },
  {
    field: 'index',
    headerText: i18n.t('序号'),
    template: function () {
      return {
        template: Vue.component('indexTemplate', {
          template: `<div>{{Number(data.index) + 1}}</div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'fileName',
    headerText: i18n.t('附件名称'),
    cellTools: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  },
  {
    field: 'fileSize',
    headerText: i18n.t('附件大小'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return `${Math.floor((e / 1024) * 100) / 100}KB`
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('上传时间')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
