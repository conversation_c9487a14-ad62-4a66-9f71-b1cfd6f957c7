<template>
  <div class="detail-grid-cell-template">
    <mt-input
      id="purchaseOrgName"
      v-if="field === 'purchaseOrgName'"
      v-model="data.purchaseOrgName"
    ></mt-input>
    <mt-input
      id="purchaseOrgCode"
      v-if="field === 'purchaseOrgCode'"
      v-model="data.purchaseOrgCode"
    ></mt-input>
    <mt-input
      id="currencyName"
      v-if="field === 'currencyName'"
      v-model="data.currencyName"
    ></mt-input>
    <mt-input id="taxName" v-if="field === 'taxName'" v-model="data.taxName"></mt-input>
    <mt-input
      id="confirmControlName"
      v-if="field === 'confirmControlName'"
      v-model="data.confirmControlName"
    ></mt-input>
    <mt-input id="drawerName" v-if="field === 'drawerName'" v-model="data.drawerName"></mt-input>
    <mt-input
      id="schemeGroupName"
      v-if="field === 'schemeGroupName'"
      v-model="data.schemeGroupName"
    ></mt-input>

    <mt-input
      id="tradeTermName"
      v-if="field === 'tradeTermName'"
      v-model="data.tradeTermName"
    ></mt-input
    ><mt-input
      id="dateControlName"
      v-if="field === 'dateControlName'"
      v-model="data.dateControlName"
    ></mt-input>
    <mt-input
      id="orderAddressName"
      v-if="field === 'orderAddressName'"
      v-model="data.orderAddressName"
    ></mt-input>
    <!-- 付款条件 -->
    <mt-input
      id="payConditionName"
      v-if="field === 'payConditionName'"
      v-model="data.payConditionName"
    ></mt-input>
    <!-- 报价属性 -->
    <mt-input id="quoteAttrName" v-if="field === 'quoteAttrName'" v-model="data.quoteAttrName" />
    <!-- 价格生效方式 -->
    <mt-input
      id="priceEffectiveMethodName"
      v-if="field === 'priceEffectiveMethodName'"
      v-model="data.priceEffectiveMethodName"
    />
  </div>
</template>

<script>
import bus from '../config/bus'

export default {
  data() {
    return {}
  },
  computed: {
    field() {
      return this.data.column.field
    }
  },
  mounted() {
    bus.$on('changeExt', this.changeExtData)
  },
  methods: {
    changeExtData(field, value) {
      this.data[field] = value
    }
  }
}
</script>

<style lang="scss" scoped></style>
