<template>
  <div class="add-head-second--wrap">
    <mt-form ref="ruleForm" :model="uploadFormData" :rules="rules">
      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item
            class="form-item"
            :label="$t('认证轮次')"
            prop="approveRound"
            label-style="top"
          >
            <mt-input
              :max="100"
              type="number"
              :show-clear-button="true"
              :show-spin-button="false"
              v-model="uploadFormData.approveRound"
              oninput="if(value>100){value=100}else{value=value.replace(/[^\d]/g,'')}if(value.indexOf(0)==0){value=0}"
              height="40"
              :placeholder="$t('非负整数')"
            ></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="11" style="margin-left: 20px">
          <mt-form-item
            class="form-item"
            :label="$t('认证结果')"
            label-style="top"
            prop="approveResult"
          >
            <mt-select
              v-model="uploadFormData.approveResult"
              :data-source="certificationList"
              :fields="{ text: 'itemName', value: 'itemCode' }"
              :placeholder="$t('请选择')"
              float-label-type="Never"
              width="414"
            ></mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>
      <!-- <mt-form-item prop="sampleList" :label="$t('样品认证')" >
        <div class="cell-upload">
          <div class="to-upload">
            <input
              type="file"
              ref="file1"
              class="upload-input"
              multiple="multiple"
              @change="chooseFiles"
              accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
            />
            <div class="upload-box">
              <div class="plus-icon"></div>
              <div class="right-state">
                <div class="plus-txt">
                  {{ $t("请拖拽文件或点击上传") }}
                </div>
                <div class="warn-text">
                  {{
                    $t(
                      "注：文件最大不可超过50M， 文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar"
                    )
                  }}
                </div>
              </div>
            </div>
          </div>
          <div
            class="has-file"
            v-for="(item, index) in uploadFormData.sampleList"
            :key="'a' + index"
          >
            <div class="left-info">
              <div class="file-title">
                <span class="text-ellipsis">
                  {{ item.fileName }}
                </span>
                <span>{{ item.fileSize }} kb</span>
              </div>
            </div>
            <mt-icon
              name="icon_Close_2"
              class="close-icon"
              @click.native="handleRemove('sampleList',index)"
            ></mt-icon>
          </div>
        </div>
      </mt-form-item> -->
      <mt-form-item class="form-item" :label="$t('附件')" prop="sampleList" label-style="top">
        <mt-common-uploader
          :is-single-file="false"
          :save-url="saveUrl"
          :download-url="downloadUrl"
          type="line"
          v-model="uploadFormData.sampleList"
        ></mt-common-uploader>
      </mt-form-item>
      <mt-form-item class="form-item" :label="$t('附件')" prop="smallBatchList" label-style="top">
        <mt-common-uploader
          :is-single-file="false"
          :save-url="saveUrl"
          :download-url="downloadUrl"
          type="line"
          v-model="uploadFormData.smallBatchList"
        ></mt-common-uploader>
      </mt-form-item>

      <!-- <mt-form-item prop="smallBatchList" :label="$t('小批量认证')" >
        <div class="cell-upload">
          <div class="to-upload">
            <input
              type="file"
              class="upload-input"
              ref="file2"
              multiple="multiple"
              @change="chooseFilesBatch"
              accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
            />
            <div class="upload-box">
              <div class="plus-icon"></div>
              <div class="right-state">
                <div class="plus-txt">
                  {{ $t("请拖拽文件或点击上传") }}
                </div>
                <div class="warn-text">
                  {{
                    $t(
                      "注：文件最大不可超过50M， 文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar"
                    )
                  }}
                </div>
              </div>
            </div>
          </div>
          <div
            class="has-file"
            v-for="(item, index) in uploadFormData.smallBatchList"
            :key="'a' + index"
          >
            <div class="left-info">
              <div class="file-title">
                <span class="text-ellipsis">
                  {{ item.fileName }}
                </span>
                <span>{{ item.fileSize }} kb</span>
              </div>
            </div>
            <mt-icon
              name="icon_Close_2"
              class="close-icon"
              @click.native="handleRemove('smallBatchList',index)"
            ></mt-icon>
          </div>
        </div>
      </mt-form-item> -->
    </mt-form>
  </div>
</template>
<script>
import commonData from '@/utils/constant'
// import Uploader from './uploader.vue'

// todo: 必填校验

export default {
  props: {
    firstData: {
      type: Object,
      default: () => {}
    },
    firstDatas: {
      type: Object,
      default: () => {}
    }
  },
  // components: {
  //   Uploader
  // },
  data() {
    return {
      downloadUrl: commonData.downloadUrl,
      saveUrl: commonData.publicFileUrl,
      certificationList: [], //认证结果字典
      uploadFormData: {
        approveRound: '',
        approveResult: '',
        sampleList: [],
        smallBatchList: []
      },
      allowFileType: [
        'xls',
        'xlsx',
        'doc',
        'docx',
        'pdf',
        'ppt',
        'pptx',
        'png',
        'jpg',
        'zip',
        'rar'
      ]
    }
  },
  computed: {
    rules() {
      if (this.firstData.sampleFlag && this.firstData.smallBatchFlag) {
        return {
          approveRound: [
            {
              required: true,
              message: this.$t('请输入认证轮次'),
              trigger: 'blur'
            }
          ],
          approveResult: [
            {
              required: true,
              message: this.$t('请选择认证结果'),
              trigger: 'blur'
            }
          ],
          sampleList: [
            {
              required: true,
              message: this.$t('请上传样品认证文件'),
              trigger: 'blur'
            }
          ],
          smallBatchList: [
            {
              required: true,
              message: this.$t('请上传小批量认证文件'),
              trigger: 'blur'
            }
          ]
        }
      }
      if (this.firstData.sampleFlag) {
        return {
          approveRound: [
            {
              required: true,
              message: this.$t('请输入认证轮次'),
              trigger: 'blur'
            }
          ],
          approveResult: [
            {
              required: true,
              message: this.$t('请选择认证结果'),
              trigger: 'blur'
            }
          ],
          sampleList: [
            {
              required: true,
              message: this.$t('请上传样品认证文件'),
              trigger: 'blur'
            }
          ]
        }
      }
      if (this.firstData.smallBatchFlag) {
        return {
          approveRound: [
            {
              required: true,
              message: this.$t('请输入认证轮次'),
              trigger: 'blur'
            }
          ],
          approveResult: [
            {
              required: true,
              message: this.$t('请选择认证结果'),
              trigger: 'blur'
            }
          ],
          smallBatchList: [
            {
              required: true,
              message: this.$t('请上传小批量认证文件'),
              trigger: 'blur'
            }
          ]
        }
      }
      if (
        this.uploadFormData.sampleList.length > 0 ||
        this.uploadFormData.smallBatchList.length > 0
      ) {
        return {
          approveRound: [
            {
              required: true,
              message: this.$t('请输入认证轮次'),
              trigger: 'blur'
            }
          ],
          approveResult: [
            {
              required: true,
              message: this.$t('请选择认证结果'),
              trigger: 'blur'
            }
          ]
        }
      }
      return {}
    }
  },
  async created() {
    this.certificationdict()
    this.uploadFormData = {
      approveRound: '',
      approveResult: '',
      sampleList: [],
      smallBatchList: []
    }
  },

  methods: {
    chooseFiles(data) {
      this.uploading(data, 'sample')
      this.$refs.file1.value = ''
    },
    chooseFilesBatch(data) {
      this.uploading(data, 'smallBatch')
      this.$refs.file2.value = ''
    },
    uploading(data, type) {
      this.$loading()
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      } else if (files.length > 5) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
      })
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      bol = files.some((item) => {
        return item.size > params.limit * 1024
      })
      if (bol) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      let arr = []
      files.forEach((item) => {
        let _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
        arr.push(this.$API.SupplierPunishment.fileUpload(_data))
      })
      Promise.all(arr)
        .then((res) => {
          this.$hloading()
          this.$toast({ content: this.$t('上传成功'), type: 'success' })
          res.forEach((item) => {
            if (type == 'sample') {
              this.uploadFormData.sampleList.push({
                ...item.data,
                bizId: this.firstData?.authProjectSupplierId,
                bizType: type,
                fileId: item.data.id,
                fileUrl: item.data.remoteUrl
              })
            } else {
              this.uploadFormData.smallBatchList.push({
                ...item.data,
                bizId: this.firstData?.authProjectSupplierId,
                bizType: type,
                fileId: item.data.id,
                fileUrl: item.data.remoteUrl
              })
            }
          })
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('上传失败'),
            type: 'warning'
          })
        })
    },

    certificationdict() {
      this.$API.CategoryCertification.approvedict({
        dictCode: 'approveResultType'
      }).then((res) => {
        this.certificationList = res.data
      })
    },

    next() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate((valid) => {
          console.log(valid)
          if (valid) {
            let params = {
              buyerSampleDTOList: [],
              fileInfoRequests: [
                ...this.uploadFormData.smallBatchList,
                ...this.uploadFormData.sampleList
              ]
            }
            if (this.firstData.sampleFlag || this.uploadFormData.sampleList.length > 0) {
              params.buyerSampleDTOList.push({
                bizId: this.firstData?.authProjectSupplierId,
                bizType: 'sample',
                approveRound: this.uploadFormData.approveRound,
                approveResult: this.uploadFormData.approveResult,
                authProjectId: this.firstData.authProjectId,
                authProjectCode: this.firstData.authProjectCode,
                partnerArchiveId: this.firstDatas.partnerArchiveId,
                partnerRelationId: this.firstDatas.partnerRelationId,
                supplierCode: this.firstDatas.supplierCode,
                supplierName: this.firstDatas.supplierName
              })
            }
            if (this.firstData.smallBatchFlag || this.uploadFormData.smallBatchList.length > 0) {
              params.buyerSampleDTOList.push({
                bizId: this.firstData?.authProjectSupplierId,
                bizType: 'smallBatch',
                approveRound: this.uploadFormData.approveRound,
                approveResult: this.uploadFormData.approveResult,
                authProjectId: this.firstData.authProjectId,
                authProjectCode: this.firstData.authProjectCode,
                partnerArchiveId: this.firstDatas.partnerArchiveId,
                partnerRelationId: this.firstDatas.partnerRelationId,
                supplierCode: this.firstDatas.supplierCode,
                supplierName: this.firstDatas.supplierName
              })
            }
            this.$API.SupplierPunishment.addUploaderaddAccessory(params).then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                return resolve()
              }
            })
          } else {
            return reject()
          }
        })
      })
    },
    handleRemove(type, index) {
      this.uploadFormData[type].splice(index, 1)
    }
  }
}
</script>
<style lang="scss" scoped>
.add-head-second--wrap {
  /deep/ .mt-form-item {
    width: 100% !important;
  }
}
.upload-item--wrap {
  margin-bottom: 24px;
  .upload-item-label {
    margin-bottom: 16px;
    color: #292929;
  }
}
.uploader-text--wrap {
  line-height: 24px;

  .uploader-text--tip {
    font-size: 20px;
    color: #9baac1;
    margin-bottom: 6px;
  }

  .uploader-text--warn {
    color: #cf3838;
  }
}
.cell-upload {
  margin-top: 15px;
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          width: 100%;
          display: inline-block;
          vertical-align: middle;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
