<template>
  <div class="upload-box">
    <div v-if="size === 'middle'" class="upload-content--wrap upload-middle--wrap">
      <input type="file" v-show="!imageUrl" class="upload-input" @change="chooseFiles" />
      <div class="droparea upload-box--border" v-show="!imageUrl">
        <div class="plus-icon"></div>
        <slot></slot>
      </div>

      <div class="image--wrap upload-box--border" v-show="imageUrl && preview">
        <img :src="imageUrl" />
        <div class="image-hover--wrap">
          <span class="tip">{{ $t('重新上传') }}</span>
          <input type="file" class="upload-input" @change="chooseFiles" />
        </div>
      </div>
    </div>

    <div class="upload-content--wrap upload-small--wrap" v-if="size === 'small'">
      <input v-show="!imageUrl" type="file" class="upload-input" @change="chooseFiles" />
      <div class="droparea upload-small--unload upload-box--border" v-show="!imageUrl">
        <div class="plus-icon"></div>
        <slot></slot>
      </div>
      <div class="droparea upload-small--loaded upload-box--border" v-show="imageUrl && !preview">
        <span class="upload-file--name">{{ fileData.fileName }}</span>

        <span class="upload-file--size">{{ fileSizeFormat }}</span>

        <MtProgress
          class="upload-file--process"
          type="Linear"
          height="60"
          :id="mtProcessId"
          :value="100"
          :animation="animation"
        />

        <span class="upload-file--status">{{ $t('上传完成') }}</span>

        <mt-icon class="upload-file--btn" name="icon_Close_1" @click.native="removeFile"></mt-icon>
      </div>
    </div>
  </div>
</template>

<script>
import MtProgress from '@mtech-ui/progress'

export default {
  components: { MtProgress },
  props: {
    size: {
      type: String,
      default: 'middle' // 'middle' | 'small'
    },

    preview: {
      type: Boolean,
      default: true
    },

    exts: {
      type: Array,
      default: () => []
    },
    isLogo: {
      type: Boolean,
      default: false
    },
    logoUrl: {
      type: String,
      default: ''
    },
    meta: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      imageUrl: '',
      fileData: {},
      mtProcessId: `mt-progress-${Math.random()}`,
      animation: {
        enable: true,
        duration: 2000,
        delay: 0
      }
    }
  },

  computed: {
    fileSizeFormat() {
      const { fileSize } = this.fileData

      if (fileSize) {
        if (Math.floor(fileSize / 1024 / 1024)) {
          const mbNumber = fileSize / 1024 / 1024
          return `${Math.round(mbNumber).toFixed(2)}Mb`
        } else if (Math.floor(fileSize / 1024)) {
          const kbNumber = fileSize / 1024
          return `${Math.round(kbNumber).toFixed(2)}Kb`
        } else {
          return `${Math.round(fileSize).toFixed(2)}b`
        }
      }

      return ''
    }
  },

  watch: {
    logoUrl(val) {
      if (val) {
        this.imageUrl = val
      }
    }
  },
  methods: {
    chooseFiles(event) {
      const files = event.target.files
      if (files.length === 1 && this.validImageExt(files) && this.validImageRange(files)) {
        const formData = new FormData()
        formData.append('UploadFiles', files[0])
        formData.append('useType', '1')

        let promise

        if (this.isLogo) {
          promise = this.$API.SupplierPunishment.uploadLogo(formData)
        } else {
          promise = this.$API.SupplierPunishment.fileUpload(formData)
        }

        promise.then((res) => {
          if (res.code === 200) {
            this.fileData = res.data
            this.imageUrl = res.data.remoteUrl
            this.$emit('success', res.data)
          }
        })
      }
      event.target.value = ''
    },

    removeFile() {
      this.$API.SupplierPunishment.delStage({
        // ids: this.fileData.id + "",
        ids: this.meta.id
      })
        .then((res) => {
          if (res.code === 200) {
            this.imageUrl = ''
            this.fileData = {}
            this.$emit('delete', res.data)
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('文件删除失败'),
            type: 'error'
          })
        })
    },

    validImageExt(files) {
      let isValid = true
      for (let i = 0; i < files.length; i++) {
        const names = files[i].name.split('.')
        const ext = '.' + names[names.length - 1]
        if (this.exts.length && !this.exts.includes(ext)) {
          isValid = false
          break
        }
      }
      return isValid
    },

    validImageRange(files) {
      const MAXSIZE = 5 * 1024 * 1024
      let isOutOfRange = true
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > MAXSIZE) {
          isOutOfRange = false
          break
        }
      }
      return isOutOfRange
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-box {
  width: 100%;
  .upload-box--border {
    background: rgba(251, 252, 253, 1);
    border: 1px dashed rgba(232, 232, 232, 1);
    border-radius: 4px;
    margin: 0 auto;
    padding: 10px 0;
    .warn-text {
      width: 100%;
      text-align: center;
      padding: 0 10px;
      margin: 0 auto;
      margin-top: 10px;
      font-size: 12px;
      font-weight: normal;
      color: rgba(237, 86, 51, 1);
    }
  }

  .plus-icon {
    width: 40px;
    height: 40px;
    position: relative;
    &::after {
      content: ' ';
      display: inline-block;
      width: 2px;
      height: 40px;
      background: rgba(232, 232, 232, 1);
      border-radius: 100px;
      position: absolute;
      left: 50%;
      top: 0;
      transform: translateX(-50%);
    }
    &::before {
      content: ' ';
      display: inline-block;
      width: 40px;
      height: 2px;
      background: rgba(232, 232, 232, 1);
      border-radius: 100px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .plus-txt {
    width: 100%;
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(152, 170, 195, 1);
  }

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }
}

.upload-content--wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.upload-small--wrap {
  .plus-icon {
    display: inline-block;
    margin: 0px 20px;
  }
  .upload-small--unload {
    display: flex;
    align-items: center;
  }

  .upload-small--loaded {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .upload-file--name {
      margin: 0 10px 0 20px;
      width: 25%;
      color: #00469c;
      text-align: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .upload-file--size {
      color: #9a9a9a;
    }

    .upload-file--process {
      max-width: 25%;
      min-width: 100px;
      height: 63px;
      overflow: hidden;
      color: #0a9947;
    }

    .upload-file--status {
      margin: 0 10px;
      color: #0a9947;
      min-width: 60px;
    }

    .upload-file--btn {
      cursor: pointer;
      color: #02469c;
      margin: 0 10px 0 20px;
    }
  }
}

.upload-middle--wrap {
  .droparea {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .plus-icon {
    margin: 0 auto;
  }
  .image--wrap {
    position: relative;
    height: 100%;
    text-align: center;

    img {
      max-width: 100%;
      max-height: 100%;
      text-align: center;
    }

    .image-hover--wrap {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 40px;
      background: rgba(0, 0, 0, 0.6);
      color: #00469c;

      .tip {
        line-height: 40px;
      }
    }
  }
}
</style>
