<template>
  <div>
    <div class="full-height">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        :use-tool-template="true"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleCustomReset"
        @handleClickCellTitle="handleClickCellTitle"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <!-- <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
                <mt-drop-downTree
                  v-if="orgFields.dataSource.length > 0"
                  v-model="orgIdArr"
                  :placeholder="$t('请选择组织')"
                  :popup-height="500"
                  :fields="orgFields"
                  :allow-filtering="true"
                  filter-type="Contains"
                  @change="selectOrg"
                  id="baseTreeSelect"
                />
                <mt-select
                  v-else
                  v-model="searchFormModel.orgId"
                  css-class="rule-element"
                  :data-source="[]"
                  :show-clear-button="true"
                  :placeholder="$t('请选择组织')"
                />
              </mt-form-item> -->

              <mt-form-item prop="evaluateCycle" :label="$t('评价周期')" label-style="top">
                <mt-select
                  v-model="searchFormModel.evaluateCycle"
                  css-class="rule-element"
                  :data-source="perEvaluationOptions"
                  :show-clear-button="true"
                  :fields="{ text: 'itemName', value: 'itemCode' }"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="evaluateYear" :label="$t('评价年份')" label-style="top">
                <mt-input v-model="searchFormModel.evaluateYear"></mt-input>
              </mt-form-item>
              <mt-form-item prop="categCode" :label="$t('品类名称')" label-style="top">
                <RemoteAutocomplete
                  v-model="searchFormModel.categCode"
                  url="/analysis/tenant/supplier/assess/strategy/supplier/result/pageQueryCategory"
                  multiple
                  :placeholder="$t('请选择品类')"
                  :fields="{ text: 'categoryName', value: 'categoryCode' }"
                  :search-fields="supplierSearchFields"
                ></RemoteAutocomplete>
              </mt-form-item>

              <mt-form-item prop="categLevel" :label="$t('品类层级')" label-style="top">
                <mt-select
                  v-model="searchFormModel.categLevel"
                  css-class="rule-element"
                  :data-source="perSuggestLevelOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="supplierLevel" :label="$t('供应商层级')" label-style="top">
                <mt-select
                  v-model="searchFormModel.supplierLevel"
                  css-class="rule-element"
                  :data-source="perSupplierLevelOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>

              <mt-form-item prop="approveStatus" :label="$t('审批状态')" label-style="top">
                <mt-select
                  v-model="searchFormModel.approveStatus"
                  css-class="rule-element"
                  :data-source="approveStatusOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="publishStatus" :label="$t('发布状态')" label-style="top">
                <mt-select
                  v-model="searchFormModel.publishStatus"
                  css-class="rule-element"
                  :data-source="submitStatusOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.createDate"
                  :placeholder="$t('请选择创建时间')"
                  @change="(e) => handleDateTimeChange(e, 'CreateDate')"
                ></mt-date-range-picker>
              </mt-form-item>
              <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
                <mt-input v-model="searchFormModel.updateUserName"></mt-input>
              </mt-form-item>
              <mt-form-item prop="modifyDate" :label="$t('最后更新时间')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.modifyDate"
                  :placeholder="$t('请选择最后更新时间')"
                  @change="(e) => handleDateTimeChange(e, 'ModifyDate')"
                ></mt-date-range-picker>
              </mt-form-item>
              <mt-form-item prop="categRespUserName" :label="$t('品类责任人')" label-style="top">
                <mt-input v-model="searchFormModel.categRespUserName"></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
  </div>
</template>

<script>
import {
  pageConfig,
  perGradeTypeOptions,
  // perEvaluationOptions,
  submitStatusOptions,
  approveStatusOptions,
  levelAdjustOptions
} from './config/index.js'
import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils.js'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      supplierSearchFields: ['categoryCode', 'categoryName'],
      orgIdArr: [],
      templateListArrList: [], // 绩效模板
      searchFormModel: {},
      // 得分是否为空
      scoreNullOptions: [
        { text: this.$t('是'), value: true },
        { text: this.$t('否'), value: false }
      ],
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      //分页数据
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      pageConfig,
      isLoading: false,
      perGradeTypeOptions,
      perEvaluationOptions: [],
      perSuggestLevelOptions: [],
      submitStatusOptions,
      approveStatusOptions,
      levelAdjustOptions,
      editrowdata: {},
      adjustLevelOption: [], //层级调整下拉值集
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'performanceManage',
        templateUrl: 'ResultsCategoryGradeExport', // 下载模板接口方法名
        uploadUrl: 'ResultsCategoryGradeImport', // 上传接口方法名
        file: 'excel' //后端接收参数名
      },
      resHeaderText: []
    }
  },

  created() {
    this.getOrgList() //获取组织下拉数据
    this.getLevelOptions() // 获取层级数据
  },
  mounted() {
    this.getDictItemByDictCode()
  },
  activated() {
    this.getOrgList()
  },
  deactivated() {
    this.orgFields.dataSource = []
  },
  computed: {},
  methods: {
    // 获取评价周期options
    getDictItemByDictCode() {
      const params = {
        code: 'PER_EVALUATION'
      }
      this.$API.performanceManage.queryDictItemByDictCode(params).then((res) => {
        if (res.code === 200) {
          this.perEvaluationOptions = res.data
        }
      })
    },
    getLevelOptions() {
      const getSupplierLevelOptions = this.getSupplierLevelOptions() //获取供应商层级数据

      const getSuggestLevelOptions = this.getSuggestLevelOptions() // 获取品类层级
      Promise.all([getSupplierLevelOptions, getSuggestLevelOptions]).then((result) => {
        this.perSupplierLevelOptions = result[0]
        this.perSuggestLevelOptions = result[1]
        this.pageConfig = pageConfig(this)
      })
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        console.log('e.startDate', e.startDate)
        this.searchFormModel['start' + field] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    // 重置
    handleCustomReset() {
      this.templateListArrList = []
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 选择绩效模板
    selectTemplate(e) {
      //清空供应商选中和品类选中
      let { itemData } = e
      if (itemData) {
        this.searchFormModel.templateId = itemData.id //模板id
        this.searchFormModel.templateCode = itemData.templateCode
      }
    },
    //获取绩效模板
    getTemplateList(para) {
      let params = {
        page: { current: 1, size: 9999 }
      }
      params = {
        ...params,
        ...para
      }
      this.$API.performanceManage.templateListQuery(params).then((result) => {
        this.templateListArrList = result.data
      })
    },
    // 获取供应商层级数据
    getSupplierLevelOptions() {
      return new Promise((resolve) => {
        this.$API.performanceManage
          .getQuerySupplierLevel()
          .then((res) => {
            resolve(
              res.data.map((item) => ({
                text: item.layeredLevelTypeName,
                value: item.layeredLevelType
              }))
            )
          })
          .catch(() => {
            resolve([])
          })
      })
    },
    // 获取建议层级数据
    getSuggestLevelOptions() {
      let params = {
        categCode: 'STRATEGIC_CATEGORY',
        layeredLevel: 1
      }
      return new Promise((resolve) => {
        this.$API.performanceManage
          .getSuggestLevelOptions(params)
          .then((res) => {
            resolve(
              res.data.map((item) => ({
                text: item.layeredLevelTypeName,
                value: item.layeredLevelType
              }))
            )
          })
          .catch(() => {
            resolve([])
          })
      })
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    selectOrg(e) {
      const { value } = e
      if (e.value.length === 0) {
        this.searchFormModel.orgCode = null
        this.searchFormModel.orgId = null
      }
      this.matchItem(this.orgFields.dataSource, e['value'][0])

      this.getTemplateList({
        orgId: value[0]
      })
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.searchFormModel.orgCode = ele.orgCode
          this.searchFormModel.orgId = ele.id
          this.searchFormModel.orgName = ele.name
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    // 点击工具栏
    handleClickToolBar(item) {
      // let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (item.toolbar.id == 'ExportCate') {
        this.handleClickDownload()
      }
    },

    //导出
    handleClickDownload() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.performanceManage.gradeResultExportCateSup(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    actionBegin(args) {
      // rowData是编辑前的行数据， data是编辑后的数据
      console.log('actionBeginactionBeginactionBeginactionBegin', args)
      const { requestType, rowData, data } = args
      if (requestType === 'save') {
        if (data.achievementAssessScoreType === '1' && !data.score) {
          this.$toast({ content: this.$t('打分类型为得分时“得分”项必填'), type: 'warning' })
          args.cancel = true
        }
        if (data.achievementAssessScoreType === '2' && !data.achievementAssessScoreValue) {
          this.$toast({ content: this.$t('打分类型为评分时得“评分值”项必填'), type: 'warning' })
          args.cancel = true
        }
      } else if (requestType === 'beginEdit') {
        const query = {
          categCode: rowData.achievementAssessType,
          layeredLevel: 1
        }
        this.$API.performanceManage.getAdjustLevelOption(query).then((res) => {
          if (res.code == 200) {
            this.adjustLevelOption = res.data
          }
        })
      }
    },

    actionComplete(args) {
      console.log('flag')
      const { requestType, data } = args
      if (data && data.suggestLevelName !== data.adjustLevelName && !data.adjustCause) {
        data.adjustCause = null
      }
      if (requestType === 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    async save(data) {
      const res = await this.$API.performanceManage.categoryGradeSave(data)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
      }
    },

    // 单元格字段点击事件,跳转页面
    handleClickCellTitle(e) {
      if (e.field === 'scoreDtl') {
        this.$router.push({
          path: '/supplier/layered-and-graded-management/result-query-detail',
          query: {
            id: e.data.id,
            score: JSON.stringify(e.data.templateTypeScores),
            selectIndex: 2
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  // height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
