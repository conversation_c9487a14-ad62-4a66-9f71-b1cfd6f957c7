import { i18n } from '@/main.js'

const categoryCols = [
  {
    field: 'orgName12',
    headerText: i18n.t('指标名称'),
    allowEditing: false
  },
  {
    field: 'evaluateCycleName12',
    headerText: i18n.t('评分逻辑描述'),
    allowEditing: false
  },
  {
    field: 'evaluateYear',
    headerText: i18n.t('评分数据来源'),
    width: '220',
    allowEditing: false,
    cssClass: 'field-content'
  },
  {
    field: 'categCode',
    headerText: i18n.t('满分'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'categName',
    headerText: i18n.t('加/扣分上限'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'categName2',
    headerText: i18n.t('备注'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'categName3',
    headerText: i18n.t('得分'),
    width: '220',
    allowEditing: false
  }
]

export const pageConfig = (url) => {
  const config = [
    {
      toolbar: {
        useBaseConfig: false
      },
      useToolTemplate: false,
      grid: {
        columnData: categoryCols,
        height: 250,
        asyncConfig: {
          url: url
        }
      }
    }
  ]
  return config
}
