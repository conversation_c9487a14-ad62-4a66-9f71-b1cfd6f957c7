<template>
  <div class="main_class">
    <div v-for="item in infolIist" :key="item.title">
      <!-- title区 -->
      <div class="bar_title">
        <span>{{ item.title }}</span>
      </div>
      <!-- 汇总区 -->
      <div class="score_sum">
        <span>
          {{ $t('满分') }}：
          <i>{{ item.sumScore }}</i>
        </span>
        <span style="margin-left: 10px">
          {{ $t('得分') }}：
          <i>{{ item.curScore }}</i>
        </span>
      </div>
      <!-- 详细数据区 -->
      <div>
        <mt-template-page
          ref="templateRef"
          :template-config="item.pageConfig"
          :hidden-tabs="true"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config/index.js'

export default {
  data() {
    return {
      pageConfig: [],
      infolIist: [
        {
          title: this.$t('品类供应商数量（资源竞争力）'),
          score: null,
          curScore: null,
          pageConfig: pageConfig()
        },
        {
          title: this.$t('行业集中度（垄断性）'),
          score: null,
          curScore: null,
          pageConfig: pageConfig()
        },
        {
          title: this.$t('产品可替代性（部品之间的可替代性）'),
          score: null,
          curScore: null,
          pageConfig: pageConfig()
        },
        {
          title: this.$t('该品类新供应商准入难度'),
          score: null,
          curScore: null,
          pageConfig: pageConfig()
        }
      ],
      categorySupplier_url: '/analysis/tenant/buyer/assess/categoryGrade/pageQuery',
      concentrationIndustry_url: '',
      productSubstitutability_url: '',
      accessDifficulty_url: ''
    }
  },
  mounted() {
    const paramList = [
      this.categorySupplier_url,
      this.concentrationIndustry_url,
      this.productSubstitutability_url,
      this.accessDifficulty_url
    ]
    for (var i = 0; i < this.infolIist.length; i++) {
      this.infolIist[i].pageConfig = pageConfig(paramList[i])
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .toolbar-container {
  height: 0;
  padding: 5px;
}
.bar_title {
  height: 35px;
  background-color: #f2f2f2;
  border: 1px solid #ede8e8;
  padding: 10px;
  font-weight: bold;
}
.score_sum {
  padding: 10px 0 0 0;
}
</style>
