<template>
  <div class="rule-box mt-flex">
    <div class="main-container mt-flex-direction-column" style="width: 100%">
      <div class="operate-bars">
        <div class="operate-bar">
          <span class="title-left">{{ $t('分层分级得分明细') }}</span>
          <span class="op-item mt-flex" @click="handleback">
            {{ $t('返回') }}
          </span>
        </div>
        <!-- 顶部主要信息 -->
        <div class="main-info">
          <mt-form ref="listForm" :model="listForm">
            <mt-form-item prop="orgName" :label="$t('组织')">
              <mt-input v-model="listForm.orgName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="categCode" :label="$t('品类编码')">
              <mt-input v-model="listForm.categCode" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="categName" :label="$t('品类名称')">
              <mt-input v-model="listForm.categName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item
              v-if="$route.query.selectIndex === '2'"
              prop="supplierCode"
              :label="$t('供应商编码')"
            >
              <mt-input v-model="listForm.supplierCode" disabled></mt-input>
            </mt-form-item>
            <mt-form-item
              v-if="$route.query.selectIndex === '2'"
              prop="supplierName"
              :label="$t('供应商名称')"
            >
              <mt-input v-model="listForm.supplierName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="evaluateCycleName" :label="$t('评价周期')">
              <mt-input v-model="listForm.evaluateCycleName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="evaluateYear" :label="$t('评价年份')">
              <mt-input v-model="listForm.evaluateYear" disabled></mt-input>
            </mt-form-item>
            <!-- <mt-form-item prop="finInfluenceScore" :label="$t('财务影响得分')">
              <mt-input v-model="listForm.finInfluenceScore" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="obtainDifficultyScore" :label="$t('可获取难以程度得分')">
              <mt-input v-model="listForm.obtainDifficultyScore" disabled></mt-input>
            </mt-form-item> -->
            <mt-form-item prop="suggestLevelName" :label="$t('建议层级')">
              <mt-input v-model="listForm.suggestLevelName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="adjustLevelName" :label="$t('调整层级')">
              <mt-input v-model="listForm.adjustLevelName" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="adjustCause" :label="$t('调整原因')">
              <mt-input v-model="listForm.adjustCause" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建时间')">
              <mt-input v-model="listForm.createDate" disabled></mt-input>
            </mt-form-item>
            <template v-for="item in prePageScores">
              <mt-form-item
                :key="item.templateType"
                :prop="item.templateType"
                :label="$t(item.templateTypeName)"
              >
                <mt-input v-model="item.score" disabled></mt-input>
              </mt-form-item>
            </template>
          </mt-form>
        </div>
      </div>
      <div class="rule-list">
        <mt-tabs
          :e-tab="false"
          overflow-mode="Popup"
          :data-source="dataSource"
          :selected-item="selectIndex"
          @handleSelectTab="handleSelectTab"
        ></mt-tabs>
        <div v-for="(item, index) in detailList" :key="item.templateTypeName">
          <div v-if="selectIndex === index">
            <detailInfoList :list="item.listInfo" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    detailInfoList: () => import('./coms/detailInfoList.vue')
  },
  data() {
    return {
      listForm: {
        orgName: null,
        categCode: null,
        categName: null,
        evaluateCycleName: null,
        evaluateYear: null,
        finInfluenceScore: null,
        obtainDifficultyScore: null,
        suggestLevelName: null,
        adjustLevelName: null,
        createDate: null
      },
      orgFields: null,
      selectIndex: 0,
      dataSource: [],
      prePageId: null,
      prePageScores: [],
      detailList: []
    }
  },
  mounted() {
    this.prePageId = this.$route.query.id
    this.prePageScores = JSON.parse(this.$route.query.score)
    console.log('routerrouterrouter', this.$route)
    this.init()
  },
  methods: {
    // 得分明细请求数据
    init() {
      const param = {
        id: this.prePageId
      }
      // this.$API.performanceManage.resultQueryDetailSUP(param).then((res) => {
      this.$API.performanceManage[this.$route.query.queryUrl](param).then((res) => {
        console.log('this.prePageId', res)
        this.listForm = res.data
        res.data.templateTypeScoreDetailResultResponses.forEach((item) => {
          this.dataSource.push({ title: item.templateTypeName })
          this.detailList.push({
            listInfo: item.dimTypeScoreDetailResultResponseList
          })
        })
      })
    },
    handleback() {
      this.$router.go(-1)
    },
    // 切换Tab
    handleSelectTab(e) {
      this.selectIndex = e
    }
  }
}
</script>

<style lang="scss" scoped>
.full-width {
  width: 100% !important;
}
/deep/ {
  .mt-drop-down-tree .e-input-group.e-disabled,
  .mt-drop-down-tree .e-input-group.e-disabled .e-input-group-icon.e-icons {
    background: #fafafa !important;
  }
  .mt-select-index {
    float: left;
  }

  .mt-template-page {
    /deep/ .repeat-template {
      padding: 20px;
    }
  }
  .mt-form-item {
    width: calc(33% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-bottom: 20px;
    .label {
      margin-bottom: 6px;
    }
  }
}

.rule-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .main-container {
    flex: 1;
    background: #fff;
    padding: 0 20px;

    .operate-bars {
      border-radius: 8px 8px 0 0;
      .main-info {
        background-color: #f2f2f2;
        padding: 10px;
      }
      .operate-bar {
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #4f5b6d;
        .title-left {
          font-weight: 700;
          font-size: 18px;
        }
        .op-item {
          cursor: pointer;
          align-items: center;
          margin-right: 20px;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          color: #00469c;
        }
      }
    }

    .rule-list {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
    }
  }
}
::v-deep .tab-wrap2 {
  margin: 0 0 10px 0;
}
::v-deep .mt-pagertemplate .mt-select-index {
  display: none;
}
</style>
