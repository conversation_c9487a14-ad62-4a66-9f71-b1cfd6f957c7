import { i18n } from '@/main.js'

const categoryCols = [
  {
    field: 'categCode',
    headerText: i18n.t('品类编码'),
    allowEditing: false
  },
  {
    field: 'categName',
    headerText: i18n.t('品类名称'),
    allowEditing: false
  },
  {
    field: 'categLevelName',
    headerText: i18n.t('品类层级'),
    width: '220',
    allowEditing: false
    // valueConverter: {
    //   type: 'map', //(map为key/value对象)：此时，fields可不传。
    //   map: {
    //     1: i18n.t('系统自动'),
    //     2: i18n.t('手工录入')
    //   }
    // }
  },
  {
    field: 'categRespUserName',
    headerText: i18n.t('品类责任人'),
    allowEditing: false
  }
]

const supplierCategoryCols = [
  {
    field: 'categCode',
    headerText: i18n.t('品类编码'),
    allowEditing: false
  },
  {
    field: 'categName',
    headerText: i18n.t('品类名称'),
    allowEditing: false
  },
  {
    field: 'categLevelName',
    headerText: i18n.t('品类层级'),
    width: '220',
    allowEditing: false
    // valueConverter: {
    //   type: 'map', //(map为key/value对象)：此时，fields可不传。
    //   map: {
    //     1: i18n.t('系统自动'),
    //     2: i18n.t('手工录入')
    //   }
    // }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '220',
    allowEditing: false
  },
  {
    field: 'supplierLevelName',
    headerText: i18n.t('供应商层级'),
    width: '220',
    allowEditing: false
  }
]

export const pageConfig = [
  {
    gridId: '9c0ae65d-5885-4ccd-8a21-c2d85d143a79',

    toolbar: {
      useBaseConfig: false
    },
    useToolTemplate: false,
    grid: {
      columnData: categoryCols,
      height: 'auto',
      dataSource: []
      // asyncConfig: {
      //   url: url
      // }
    }
  }
]

export const supplierPageConfig = [
  {
    gridId: '9c0ae65d-5885-4ccd-8a21-c2d85d143a56',

    toolbar: {
      useBaseConfig: false
    },
    useToolTemplate: false,
    grid: {
      columnData: supplierCategoryCols,
      height: 'auto',
      dataSource: []
      // asyncConfig: {
      //   url: url
      // }
    }
  }
]
