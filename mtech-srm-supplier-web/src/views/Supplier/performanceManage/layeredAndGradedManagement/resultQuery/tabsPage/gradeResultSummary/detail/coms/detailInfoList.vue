<template>
  <div class="main_class">
    <!-- 上传附件区 -->
    <div class="file_class">
      <span style="font-weight: bold">{{ $t('附件') }}</span>
      <mt-button
        :disabled="$route.query.approveStatus === '2' || $route.query.approveStatus === '3'"
        @click="hImport"
        >{{ $t('点击上传') }}</mt-button
      >
      <template v-for="item in fileList">
        <span class="every_file" :key="item.title"
          >{{ item.fileName }}
          <mt-button
            icon-css="mt-icons mt-icon-icon_Close_2"
            type="text"
            @click="hDelete(item)"
          ></mt-button
        ></span>
      </template>
    </div>
    <div v-for="item in infoList" :key="item.title">
      <!-- title区 -->
      <div class="bar_title">
        <span>{{ item.title }}</span>
      </div>

      <!-- 详细数据区 -->
      <div>
        <mt-template-page
          ref="templateRef"
          :template-config="item.pageConfig"
          :hidden-tabs="true"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig, supplierPageConfig } from './config/index.js'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      fileList: [],
      infoList: [
        { id: 0, title: this.$t('品类分层分级'), pageConfig: pageConfig },
        { id: 1, title: this.$t('供应商分层分级'), pageConfig: supplierPageConfig }
      ]
    }
  },
  mounted() {
    setTimeout(() => {
      this.list.forEach((item) => {
        this.fileList = item.buyerAssessFileAttachmentResponse
          ? item.buyerAssessFileAttachmentResponse
          : []
        pageConfig[0].grid.dataSource = item.categoryTotalApproveDetailList
        supplierPageConfig[0].grid.dataSource = item.supplierTotalApproveDetailList
      })
    }, 1500)
  },
  methods: {
    // 点击上传附件按钮事件
    hImport() {
      this.$dialog({
        modal: () => import('./upload.vue'),
        data: {
          title: this.$t('附件上传')
        },
        success: (res) => {
          this.fileList.push(res)
        }
      })
    },
    // 附件打开预览功能
    preview(item) {
      const params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 附件删除
    hDelete(item) {
      const index = this.fileList.findIndex((file) => file.fileId === item.fileId)
      this.fileList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .toolbar-container {
  height: 0;
  padding: 5px;
}
.file_class {
  padding: 10px;
  background-color: #f2f2f2;
  margin-bottom: 10px;
  .every_file {
    padding: 0 10px;
    text-decoration: underline;
    cursor: pointer;
  }
  // &:hover .every_file {
  //   color: #6386c1;
  // }
}
::v-deep .e-btn {
  color: #40a2eb;
  margin-left: 10px;
}
.bar_title {
  height: 35px;
  background-color: #f2f2f2;
  border: 1px solid #ede8e8;
  padding: 10px;
  font-weight: bold;
}
.score_sum {
  padding: 10px 0 0 0;
}
</style>
