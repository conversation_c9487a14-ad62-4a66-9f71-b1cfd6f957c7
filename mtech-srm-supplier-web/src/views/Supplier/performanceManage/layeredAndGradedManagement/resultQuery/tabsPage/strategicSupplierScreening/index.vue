<template>
  <div>
    <div class="full-height">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        :use-tool-template="false"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleCustomReset"
        @handleClickCellTitle="handleClickCellTitle"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <!-- <mt-form-item prop="orgIdArr" :label="$t('组织')" label-style="top">
                <mt-drop-downTree
                  v-if="orgFields.dataSource.length > 0"
                  v-model="orgIdArr"
                  :placeholder="$t('请选择组织')"
                  :popup-height="500"
                  :fields="orgFields"
                  :allow-filtering="true"
                  filter-type="Contains"
                  @change="selectOrg"
                  id="baseTreeSelect"
                />
                <mt-select
                  v-else
                  v-model="searchFormModel.orgId"
                  css-class="rule-element"
                  :data-source="[]"
                  :show-clear-button="true"
                  :placeholder="$t('请选择组织')"
                />
              </mt-form-item> -->
              <!-- <mt-form-item prop="orgId" :label="$t('组织')" label-style="top">
                <mt-DropDownTree
                  v-if="fieldsarr.dataSource.length"
                  v-model="searchFormModel.orgId"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :placeholder="$t('请选择组织：')"
                  :popup-height="500"
                  :fields="fieldsarr"
                  @select="selectCompany"
                  id="baseTreeSelect"
                ></mt-DropDownTree>
                <mt-select v-else :placeholder="$t('请选择组织')" :data-source="[]"></mt-select>
              </mt-form-item> -->
              <mt-form-item prop="evaluateCycle" :label="$t('评价周期')" label-style="top">
                <mt-select
                  v-model="searchFormModel.evaluateCycle"
                  css-class="rule-element"
                  :data-source="perEvaluationOptions"
                  :show-clear-button="true"
                  :fields="{ text: 'itemName', value: 'itemCode' }"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="evaluateYear" :label="$t('评价年份')" label-style="top">
                <mt-input v-model="searchFormModel.evaluateYear"></mt-input>
              </mt-form-item>
              <mt-form-item prop="categCode" :label="$t('品类名称')" label-style="top">
                <RemoteAutocomplete
                  v-model="searchFormModel.categCode"
                  url="/analysis/tenant/supplier/assess/strategy/supplier/result/pageQueryCategory"
                  multiple
                  :placeholder="$t('请选择品类')"
                  :fields="{ text: 'categoryName', value: 'categoryCode' }"
                  :search-fields="supplierSearchFields"
                ></RemoteAutocomplete>
              </mt-form-item>
              <!-- <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
                <mt-input v-model="searchFormModel.supplierCode"></mt-input>
              </mt-form-item>
              <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
                <mt-input v-model="searchFormModel.supplierName"></mt-input>
              </mt-form-item> -->

              <mt-form-item prop="levelAdjust" :label="$t('是否层级调整')" label-style="top">
                <mt-select
                  v-model="searchFormModel.levelAdjust"
                  css-class="rule-element"
                  :data-source="levelAdjustOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="suggestLevel" :label="$t('建议层级')" label-style="top">
                <mt-select
                  v-model="searchFormModel.suggestLevel"
                  css-class="rule-element"
                  :data-source="perSuggestLevelOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="adjustLevel" :label="$t('调整层级')" label-style="top">
                <mt-select
                  v-model="searchFormModel.adjustLevel"
                  css-class="rule-element"
                  :data-source="perSuggestLevelOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="adjustCause" :label="$t('调整原因')" label-style="top">
                <mt-input v-model="searchFormModel.adjustCause"></mt-input>
              </mt-form-item>
              <mt-form-item prop="approveStatus" :label="$t('审批状态')" label-style="top">
                <mt-select
                  v-model="searchFormModel.approveStatus"
                  css-class="rule-element"
                  :data-source="approveStatusOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="publishStatus" :label="$t('发布状态')" label-style="top">
                <mt-select
                  v-model="searchFormModel.publishStatus"
                  css-class="rule-element"
                  :data-source="submitStatusOptions"
                  :show-clear-button="true"
                ></mt-select>
              </mt-form-item>

              <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.createDate"
                  :placeholder="$t('请选择创建时间')"
                  @change="(e) => handleDateTimeChange(e, 'CreateDate')"
                ></mt-date-range-picker>
              </mt-form-item>
              <!-- <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
                <mt-input v-model="searchFormModel.createUserName"></mt-input>
              </mt-form-item> -->
              <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
                <mt-input v-model="searchFormModel.updateUserName"></mt-input>
              </mt-form-item>
              <mt-form-item prop="modifyDate" :label="$t('最后更新时间')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.modifyDate"
                  :placeholder="$t('请选择最后更新时间')"
                  @change="(e) => handleDateTimeChange(e, 'ModifyDate')"
                ></mt-date-range-picker>
              </mt-form-item>
              <mt-form-item prop="updateUserName" :label="$t('品类责任人')" label-style="top">
                <mt-input v-model="searchFormModel.updateUserName"></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import {
  pageConfig,
  perGradeTypeOptions,
  // perEvaluationOptions,
  submitStatusOptions,
  approveStatusOptions,
  levelAdjustOptions
} from './config/index.js'
import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils.js'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      supplierSearchFields: ['categoryCode', 'categoryName'],
      orgIdArr: [],
      templateListArrList: [], // 绩效模板
      searchFormModel: {},
      // 得分是否为空
      scoreNullOptions: [
        { text: this.$t('是'), value: true },
        { text: this.$t('否'), value: false }
      ],
      //组织树下拉数组
      orgFields: {
        dataSource: [], //组织树下拉数组
        value: 'id',
        text: 'orgName',
        child: 'childrenList',
        code: 'orgCode'
      },
      fieldsarr: {
        dataSource: [], // 组织树下拉数组
        value: 'dimensionCodeValue',
        text: 'dimensionNameValue',
        child: 'childrenList'
      },
      //分页数据
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      pageConfig: null,
      isLoading: false,
      perGradeTypeOptions,
      perEvaluationOptions: [],
      submitStatusOptions,
      approveStatusOptions,
      levelAdjustOptions,
      adjustLevelOption: [], //层级调整下拉值集
      perSuggestLevelOptions: []
    }
  },

  created() {
    // this.getOrgList() //获取组织下拉数据
    this.TreeByAccount() //获取组织下拉数据
    this.getSuggestLevelOptions() //获取建议层级数据
  },
  computed: {},
  mounted() {
    this.init()
    this.getDictItemByDictCode()
  },
  methods: {
    // 接收数据,处理表头
    async init() {
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      let res = await this.$API.performanceManage.getResultStrategicQueryFormSUP(params)
      this.pageConfig = pageConfig(this)
      if (res?.code == 200) {
        const total = res?.data?.buyerDemandPlanPage?.total || 0
        this.forecastPageSettings.totalPages = Math.ceil(
          Number(total) / this.forecastPageSettings.pageSize
        )
        let k = 0
        for (let j = 0; j < res.data.records.length; j++) {
          res.data.records[j].scoreDtl = this.$t('明细')
          for (let i = 0; i < res.data.records[j].templateTypeScores.length; i++) {
            const gridCofig = {
              field: res.data.records[0].templateTypeScores[i].templateType
                ? res.data.records[0].templateTypeScores[i].templateType
                : 'null',
              headerText: this.$t(res.data.records[0].templateTypeScores[i].templateTypeName),
              // width: '220',
              allowEditing: false
            }
            if (
              !this.pageConfig[0].grid.columnData.some((item) => item.field === gridCofig.field)
            ) {
              this.pageConfig[0].grid.columnData.splice(6 + k, 0, gridCofig)
              k += 1
            }
            const templateType = res.data.records[j].templateTypeScores[i].templateType
              ? res.data.records[j].templateTypeScores[i].templateType
              : 'null'
            res.data.records[j][templateType] = res.data.records[j].templateTypeScores[i].score
          }
        }
        this.$nextTick(() => {
          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
        })
      }
    },
    // 获取评价周期options
    getDictItemByDictCode() {
      const params = {
        code: 'PER_EVALUATION'
      }
      this.$API.performanceManage.queryDictItemByDictCode(params).then((res) => {
        if (res.code === 200) {
          this.perEvaluationOptions = res.data
        }
      })
    },
    // 分页 页码
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.init()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.init()
    },
    // 初始化获取组织列表
    TreeByAccount() {
      this.getTreeByAccountApi()().then((res) => {
        if (res.code == 200) {
          let value = 'orgId'
          let text = 'orgName'
          if (this.isPurchase) {
            value = 'dimensionCodeValue'
            text = 'dimensionNameValue'
          }
          this.$set(this.fieldsarr, 'value', value)
          this.$set(this.fieldsarr, 'text', text)
          this.$set(this.fieldsarr, 'dataSource', [...res.data])
        }
      })
    },
    getTreeByAccountApi() {
      return this.$API.performanceManage.getSupPermissionOrgList
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel['start' + field] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    // 重置
    handleCustomReset() {
      this.templateListArrList = []
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.orgIdArr = []
    },
    // 选择绩效模板
    selectTemplate(e) {
      //清空供应商选中和品类选中
      let { itemData } = e
      if (itemData) {
        this.searchFormModel.templateId = itemData.id //模板id
        this.searchFormModel.templateCode = itemData.templateCode
      }
    },
    // 获取建议层级数据
    getSuggestLevelOptions() {
      let params = {
        categCode: 'STRATEGIC_SUPPLIER',
        layeredLevel: 1
      }
      this.$API.performanceManage.getSuggestLevelOptions(params).then((res) => {
        if (res.code === 200) {
          this.perSuggestLevelOptions = res.data.map((item) => ({
            text: item.layeredLevelTypeName,
            value: item.layeredLevelType
          }))
        }
      })
    },
    //获取绩效模板
    getTemplateList(para) {
      let params = {
        page: { current: 1, size: 9999 }
      }
      params = {
        ...params,
        ...para
      }
      this.$API.performanceManage.templateListQuery(params).then((result) => {
        this.templateListArrList = result.data
      })
    },
    //获取组织树
    getOrgList() {
      this.$API.performanceManage.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.$set(this.orgFields, 'dataSource', [...res.data])
        }
      })
    },
    //选择组织
    selectOrg(e) {
      const { value } = e
      this.matchItem(this.orgFields.dataSource, e['value'][0])

      this.getTemplateList({
        orgId: value[0]
      })
    },
    // 选取组织递归
    matchItem(data, id) {
      data.forEach((ele) => {
        if (ele.id == id) {
          this.searchFormModel.orgCode = ele.orgCode
          this.searchFormModel.orgId = ele.id
          this.searchFormModel.orgName = ele.name
          return
        }
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.matchItem(ele.childrenList, id)
        }
      })
    },
    // 点击工具栏
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (
        records.length <= 0 &&
        (item.toolbar.id == 'submitApproval' || item.toolbar.id == 'Recall')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'submitApproval') {
        let _idList = records.map((e) => e.id)
        this.submitRecord(_idList)
      } else if (item.toolbar.id == 'ImportCate') {
        // 调整导入
        this.ImportFn()
      } else if (item.toolbar.id == 'ExportCate') {
        // 导出
        this.handleClickDownload()
      } else if (item.toolbar.id == 'Submit') {
        let _idList = records.map((e) => e.id)
        // 发布
        this.handleSubmit(_idList)
      } else if (item.toolbar.id == 'exportScoreDetail') {
        // 导出得分明细
        this.handleClickDownloadDetail()
      } else if (item.toolbar.id == 'filterDataByLocal') {
        this.init()
      } else if (item.toolbar.id == 'resetDataByLocal') {
        this.init()
      } else if (item.toolbar.id == 'refreshDataByLocal') {
        this.init()
      }
    },
    handleSubmit(ids) {
      this.$API.performanceManage
        .submitResultStrategicQueryForm({
          ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('发布成功'), type: 'success' })
            this.$refs.performanceManage.refreshCurrentGridData()
          }
          this.getList()
        })
    },
    //Excel导入 - 战略供应商
    ImportFn() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.performanceManage.resultImportFn,
          downloadTemplateApi: this.$API.performanceManage.resultExportCate
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //导出
    handleClickDownload() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.performanceManage.resultExportCateSUP(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 导出明细
    handleClickDownloadDetail() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$API.performanceManage.ResultsSupplierExportDetailSPU(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 提交
    submitRecord(ids) {
      this.$API.performanceManage
        .submitApprovalData({
          ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          this.getList()
        })
    },
    // 撤回
    revokeScoreDetailsFn(ids) {
      this.$API.performanceManage
        .revokeScoreDetails({
          ids: ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('撤回成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
            this.getList()
          }
        })
    },
    actionBegin(args) {
      // rowData是编辑前的行数据， data是编辑后的数据
      const { requestType, rowData, data } = args
      if (requestType === 'save') {
        if (data.achievementAssessScoreType === '1' && !data.score) {
          this.$toast({ content: this.$t('打分类型为得分时“得分”项必填'), type: 'warning' })
          args.cancel = true
        }
        if (data.achievementAssessScoreType === '2' && !data.achievementAssessScoreValue) {
          this.$toast({ content: this.$t('打分类型为评分时得“评分值”项必填'), type: 'warning' })
          args.cancel = true
        }
      } else if (requestType === 'beginEdit') {
        const query = {
          categCode: rowData.achievementAssessType,
          layeredLevel: 1
        }
        this.$API.performanceManage.getAdjustLevelOption(query).then((res) => {
          if (res.code == 200) {
            this.adjustLevelOption = res.data
          }
        })
      }
    },
    actionComplete(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    async save(data) {
      const res = await this.$API.performanceManage.resultRowSave(data)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
      }
    },
    // 单元格字段点击事件,跳转页面
    handleClickCellTitle(e) {
      if (e.field === 'scoreDtl') {
        this.$router.push({
          path: '/supplier/layered-and-graded-management/result-query-detail-sup',
          query: {
            id: e.data.id,
            score: JSON.stringify(e.data.templateTypeScores),
            selectIndex: 2,
            queryUrl: 'resultQueryDetailSUP'
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  // height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
