<template>
  <div class="main_class">
    <div v-for="item in infolIist" :key="item.title">
      <!-- title区 -->
      <div class="bar_title">
        <span>{{ item.title }}</span>
      </div>
      <!-- 汇总区 -->
      <div class="score_sum">
        <span>
          {{ $t('满分') }}：
          <i>{{ item.sumScore }}</i>
        </span>
        <span style="margin-left: 10px">
          {{ $t('得分') }}：
          <i>{{ item.curScore }}</i>
        </span>
      </div>
      <!-- 详细数据区 -->
      <div>
        <mt-template-page
          ref="templateRef"
          :template-config="item.pageConfig"
          :hidden-tabs="true"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config/index.js'

export default {
  data() {
    return {
      pageConfig: [],
      infolIist: [
        {
          title: this.$t('采购金额'),
          score: null,
          curScore: null,
          pageConfig: pageConfig()
        },
        {
          title: this.$t('溢价'),
          score: null,
          curScore: null,
          pageConfig: pageConfig()
        },
        {
          title: this.$t('影响'),
          score: null,
          curScore: null,
          pageConfig: pageConfig()
        }
      ],
      purchaseAmount_url: '/analysis/tenant/buyer/assess/categoryGrade/pageQuery',
      premium_url: '',
      influence_url: ''
    }
  },
  mounted() {
    const paramList = [this.purchaseAmount_url, this.premium_url, this.influence_url]
    for (var i = 0; i < this.infolIist.length; i++) {
      this.infolIist[i].pageConfig = pageConfig(paramList[i])
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .toolbar-container {
  height: 0;
  padding: 5px;
}
.bar_title {
  height: 35px;
  background-color: #f2f2f2;
  border: 1px solid #ede8e8;
  padding: 10px;
  font-weight: bold;
}
.score_sum {
  padding: 10px 0 0 0;
}
</style>
