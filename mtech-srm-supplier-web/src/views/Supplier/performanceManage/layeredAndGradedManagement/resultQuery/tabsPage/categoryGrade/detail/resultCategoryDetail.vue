<template>
  <div class="rule-box mt-flex">
    <div class="main-container mt-flex-direction-column" style="width: 100%">
      <div class="operate-bars">
        <div class="operate-bar">
          <span class="title-left">{{ $t('分层分级得分明细') }}</span>
          <span class="op-item mt-flex" @click="handleback">
            {{ $t('返回') }}
          </span>
        </div>
        <!-- 顶部主要信息 -->
        <div class="main-info">
          <mt-form ref="listForm" :model="listForm">
            <mt-form-item prop="orgCodeList" :label="$t('组织：')">
              <mt-DropDownTree
                :popup-height="500"
                :fields="orgFields"
                :enabled="false"
                v-model="listForm.orgCodeList"
                :allow-filtering="true"
                filter-type="Contains"
                id="orgTreeId"
              ></mt-DropDownTree>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('品类编码')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('品类名称')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('供应商编码')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('供应商名称')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('评价周期')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('评价年份')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('财务影响得分')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('可获取难以程度得分')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('建议层级')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('调整层级')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('调整原因')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="templateTypeCode" :label="$t('创建时间')">
              <mt-input v-model="listForm.remark" disabled></mt-input>
            </mt-form-item>
            <mt-form-item prop="name" :label="$t('规则名称：')">
              <mt-input
                v-model="listForm.name"
                float-label-type="Never"
                disabled
                :max-length="32"
              ></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="rule-list">
        <mt-tabs
          :e-tab="false"
          overflow-mode="Popup"
          :data-source="dataSource"
          :selected-item="selectIndex"
          @handleSelectTab="handleSelectTab"
        ></mt-tabs>
        <div v-if="selectIndex == 0">
          <financialImpact />
        </div>
        <div v-if="selectIndex == 1">
          <degreeAccessibility />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    financialImpact: () => import('./coms/financialImpact.vue'),
    degreeAccessibility: () => import('./coms/degreeAccessibility.vue')
  },
  data() {
    return {
      listForm: {
        orgCodeList: [],
        orgCode: '',
        orgId: '',
        orgName: '',
        templateTypeCode: '',
        templateTypeName: '',
        name: '',
        remark: '',
        levelRuleRelationList: []
      },
      orgFields: null,
      selectIndex: 0,
      dataSource: [{ title: this.$t('财务影响') }, { title: this.$t('可获得难以程度') }]
    }
  },
  methods: {
    handleback() {
      this.$router.go(-1)
    },
    // 切换Tab
    handleSelectTab(e) {
      this.selectIndex = e
    }
  }
}
</script>

<style lang="scss" scoped>
.full-width {
  width: 100% !important;
}
/deep/ {
  .mt-drop-down-tree .e-input-group.e-disabled,
  .mt-drop-down-tree .e-input-group.e-disabled .e-input-group-icon.e-icons {
    background: #fafafa !important;
  }
  .mt-select-index {
    float: left;
  }

  .mt-template-page {
    /deep/ .repeat-template {
      padding: 20px;
    }
  }
  .mt-form-item {
    width: calc(33% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-bottom: 20px;
    .label {
      margin-bottom: 6px;
    }
  }
}

.rule-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .main-container {
    flex: 1;
    background: #fff;
    padding: 0 20px;

    .operate-bars {
      border-radius: 8px 8px 0 0;
      .main-info {
        background-color: #f2f2f2;
        padding: 10px;
      }
      .operate-bar {
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #4f5b6d;
        .title-left {
          font-weight: 700;
          font-size: 18px;
        }
        .op-item {
          cursor: pointer;
          align-items: center;
          margin-right: 20px;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          color: #00469c;
        }
      }
    }

    .rule-list {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
    }
  }
}
::v-deep .tab-wrap2 {
  margin: 0 0 10px 0;
}
::v-deep .mt-pagertemplate .mt-select-index {
  display: none;
}
</style>
