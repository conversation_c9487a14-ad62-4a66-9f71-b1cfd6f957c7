<template>
  <div>
    <mt-tabs
      :e-tab="false"
      overflow-mode="Popup"
      :data-source="dataSource"
      :selected-item="selectIndex"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- <div v-if="selectIndex == 0">
      <categoryGrade />
    </div> -->
    <div v-if="selectIndex == 0">
      <strategicSupplierScreening />
    </div>
    <div v-if="selectIndex == 1">
      <nonStrategicSupplierRating />
    </div>
    <div v-if="selectIndex == 2">
      <gradeResultSummary />
    </div>
  </div>
</template>
<script>
export default {
  components: {
    // categoryGrade: () => import('./tabsPage/categoryGrade/index.vue'),
    strategicSupplierScreening: () => import('./tabsPage/strategicSupplierScreening/index.vue'),
    nonStrategicSupplierRating: () => import('./tabsPage/NonStrategicSupplierRating/index.vue'),
    gradeResultSummary: () => import('./tabsPage/gradeResultSummary/index.vue')
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        { title: this.$t('战略供应商筛选') },
        { title: this.$t('非战略供应商评级') },
        { title: this.$t('层级结果汇总') }
      ]
    }
  },
  methods: {
    // 切换Tab
    handleSelectTab(e) {
      console.log('handleSelectTab', e)
      this.selectIndex = e
    }
  }
}
</script>
