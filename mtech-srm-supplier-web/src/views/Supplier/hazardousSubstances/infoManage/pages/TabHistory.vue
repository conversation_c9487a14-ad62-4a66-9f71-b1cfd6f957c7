<!-- 提交历史 只查待提交 -->
<template>
  <div>
    <mt-template-page
      ref="historyTemplateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="docNo" :label="$t('单据号')">
            <mt-input
              v-model="searchFormModel.docNo"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
          <mt-form-item prop="submitter" :label="$t('提交人')">
            <mt-input
              v-model="searchFormModel.submitter"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
          <mt-form-item prop="submitTime" :label="$t('提交日期')">
            <mt-date-range-picker
              v-model="searchFormModel.submitTime"
              @change="(e) => dateTimeChange(e, 'submitTime')"
              :placeholder="$t('请选择提交日期')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { HistoryToolbar, HistoryColumnData } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils.js'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [
        {
          isUseCustomSearch: true, // 是否使用自定义查询
          isCustomSearchRules: true,
          toolbar: HistoryToolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '00412257-0fbb-4d0b-8dcd-64a0521a3791',
          grid: {
            columnData: HistoryColumnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/user/harmful/category/head/query',
              params: {
                statusList: [11, 15, 40]
              }
            }
          }
        }
      ]
    }
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.$router.push({
          path: '/supplier/hazardous-substances/sup-info-manage-detail',
          query: {
            id: data.id,
            type: [10, '10', 11, '11', 15, '15', 40, '40'].includes(data.status) ? 'edit' : 'check',
            date: new Date().getTime()
          }
        })
        sessionStorage.setItem('tabIndex', e.tabIndex)
      } else if (tool.id === 'delete') {
        this.handleClickDeleteRow(e.data)
      }
    },
    handleClickDeleteRow(obj) {
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .deleteHazardousSubstancesHistoryInfo([obj.id])
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('删除成功')
            })
            this.$refs.historyTemplateRef.refreshCurrentGridData()
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'docNo') {
        sessionStorage.setItem('tabIndex', Number(e.tabIndex))
        this.$router.push({
          path: '/supplier/hazardous-substances/sup-info-manage-detail',
          query: {
            id: data.id,
            type: [10, '10', 11, '11', 15, '15', 40, '40'].includes(data.status) ? 'edit' : 'check',
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    handleClickToolBar(e) {
      let selectRecords = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'historyExport') {
        this.handleExport()
        return
      }
      if (selectRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'delete') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除？')
          },
          success: () => {
            this.handleDelete(selectRecords)
          }
        })
      }
    },
    handleAdd() {
      this.$router.push({
        path: '/supplier/hazardous-substances/sup-info-manage-detail',
        query: {
          type: 'add',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleDelete(selectRecords) {
      const ids = selectRecords.map((v) => v.id)
      this.$API.hazardousSubstances.deleteHazardousSubstancesHistoryInfo(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.historyTemplateRef.refreshCurrentGridData()
        }
      })
    },
    handleExport() {
      const _params = this.getRules()
      this.$API.hazardousSubstances.hazardousSubstancesHistoryExport(_params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    getRules() {
      // 获取查询条件
      const _rule =
        this.$refs.historyTemplateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: _rule.condition || '',
        page: { current: 1, size: 20000 },
        rules: _rule.rules || []
      }
      return params
    }
  }
}
</script>
