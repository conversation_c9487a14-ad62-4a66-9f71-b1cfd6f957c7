<!-- 待提交 -->
<template>
  <div>
    <mt-template-page
      ref="submitTemplateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { toolbar, columnData } from '../config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: 'd216f2c3-a0b4-4822-b68e-e0781762b2f0',
          grid: {
            columnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/tenant/harmful/category/record/query'
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      let selectRecords = e.grid.getSelectedRecords()
      if (selectRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'confirmReport') {
        this.handleSubmit(selectRecords)
      }
    },
    handleSubmit(selectRecords) {
      const _ids = selectRecords.map((v) => v.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .hazardousSubstancesConfirm(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.$refs.submitTemplateRef.refreshCurrentGridData()
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
