<!-- 已归档/超期查询 -->
<template>
  <div>
    <mt-template-page
      ref="overdueTemplateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
      <div slot="slot-filter" class="zero-filter-switch">
        <div class="form-overdue">
          <span>{{ $t('超期快查') }}：</span>
          <span>
            <mt-select
              v-model="searchFormModel.overdue"
              :data-source="overdueOptions"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
              :fields="{ text: 'text', value: 'value' }"
              @change="selectChange"
            />
          </span>
        </div>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import { overdueToolbar, overdueColumnData } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils.js'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: overdueToolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '3b4c5894-591e-4726-a5f7-b6757dd0a70d',
          grid: {
            columnData: overdueColumnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/user/harmful/category/head/getLastByPage',
              defaultRules: []
            }
          }
        }
      ],
      searchFormModel: {},
      overdueOptions: [
        { text: this.$t('已超期'), value: 0 },
        { text: this.$t('一个月内超期'), value: 1 },
        { text: this.$t('三个月内超期'), value: 2 }
      ]
    }
  },
  methods: {
    selectChange(e) {
      let _data = [
        {
          label: this.$t('超期快查'),
          field: 'overdue',
          operator: 'equal',
          type: 'string',
          value: String(e.itemData?.value)
        }
      ]
      this.$set(this.pageConfig[0].grid.asyncConfig, 'defaultRules', _data)
    },
    handleClickToolBar(e) {
      let selectRecords = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'ArchivedExport') {
        this.handleExport()
        return
      }
      if (e.toolbar.id === 'ArchivedUpdateReport') {
        let _flag = true
        selectRecords.map((item, index) => {
          if (selectRecords[index + 1]) {
            if (item.sqeName !== selectRecords[index + 1].sqeName) {
              _flag = false
            }
          }
        })
        if (!_flag) {
          this.$toast({
            type: 'warning',
            content: this.$t('所选数据的SEQ负责人必须相同')
          })
          return
        }
        this.handleClickUpdateReport(selectRecords)
      }
    },
    handleClickUpdateReport(list = {}) {
      sessionStorage.setItem('sup-info-manage-detail-list', JSON.stringify(list))
      this.$router.push({
        path: '/supplier/hazardous-substances/sup-info-manage-detail',
        query: {
          ...list[0],
          id: list.headId,
          status: 10,
          type: 'updateReport',
          date: new Date().getTime()
        }
      })
    },
    handleExport() {
      const _params = this.getRules()
      _params['defaultRules'] = [
        {
          label: '超期快查',
          field: 'overdue',
          operator: 'equal',
          type: 'string',
          value: String(this.searchFormModel.overdue)
        }
      ]
      this.$API.hazardousSubstances.hazardousSubstancesArchivedExport(_params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    getRules() {
      // 获取查询条件
      const _rule =
        this.$refs.overdueTemplateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: _rule.condition || '',
        page: { current: 1, size: 20000 },
        rules: _rule.rules || []
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .zero-filter-switch {
  & .form-overdue {
    display: flex;
    justify-content: center;
    align-items: center;
    & span:nth-child(1) {
      font-size: 14px;
      font-weight: 600;
    }
  }
}
</style>
