<template>
  <div>
    <mt-date-picker
      :id="data.column.field"
      v-model="data[data.column.field]"
      :open-on-focus="true"
      :allow-edit="false"
      :placeholder="$t('')"
      @change="dateChange"
      :disabled="!isEdit || data.column.field === 'nextReportTime'"
    ></mt-date-picker>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { timestampToDate } from '@/utils/utils'
export default {
  data() {
    return {
      data: {}
    }
  },
  computed: {
    isEdit() {
      return [10, '10', 11, '11', 15, '15', 40, '40'].includes(this.$route.query?.status)
    }
  },
  // computed: {
  //   dataValue: {
  //     get() {
  //       return this.data[this.data.column.field]
  //     },
  //     set(oldvalue, value) {
  //       this.data[this.data.column.field] = value
  //     }
  //   }
  // },
  mounted() {
    let _date = this.data[this.data.column.field]
    if (!isNaN(_date)) {
      if (Number(_date) < 0) {
        _date = _date.replace(/-/g, '')
      }
      if (_date.length > 1 && _date.length <= 13 && Number(_date) > 0) {
        while (_date.length < 13) {
          _date = _date + '0'
        }
      } else if (_date.length === 1) {
        _date = new Date().getTime()
      }
      this.data[this.data.column.field] = timestampToDate(_date, 'date')
    }
    if (this.data.column.field === 'nextReportTime' && this.isEdit) {
      this.$bus.$on('reportTimeChange', (val) => {
        // 只允许修改当前行的日期
        if (val.id !== this.data['id']) return
        const _dataSource = this.$parent.dataSource
        const _dateRow = _dataSource.filter((item) => item.id === this.data['id'])[0]
        if (val.date) {
          _dateRow['nextReportTime'] = dayjs(val.date)
            .add(1, 'year')
            .subtract(1, 'day')
            .format('YYYY-MM-DD')
        } else {
          _dateRow['nextReportTime'] = dayjs(new Date())
            .add(1, 'year')
            .subtract(1, 'day')
            .format('YYYY-MM-DD')
        }
        this.data[this.data.column.field] = dayjs(val.date)
          .add(1, 'year')
          .subtract(1, 'day')
          .format('YYYY-MM-DD')
        console.log(this.data['nextReportTime'], 'nextReportTime')
      })
    }
  },
  methods: {
    dateChange(e) {
      // this.data[this.data.column.field] = dayjs(e).format('yyyy-MM-dd')
      const _dataSource = this.$parent.dataSource
      _dataSource.map((item) => {
        if (item.id === this.data['id']) {
          item[this.data.column.field] = dayjs(e).valueOf()
          if (this.data.column.field === 'reportTime') {
            // item['nextReportTime'] = dayjs(e).add(1, 'year')
          }
        }
      })
      if (this.data.column.field === 'reportTime' && this.isEdit) {
        this.$bus.$emit('reportTimeChange', {
          date: e,
          id: this.data['id'],
          key: 'nextReportTime'
        })
      }
    }
  },
  beforeDestroy() {}
}
</script>
