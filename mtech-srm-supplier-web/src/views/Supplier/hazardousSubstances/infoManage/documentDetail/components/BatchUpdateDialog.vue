<!-- 批量修改弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="650px"
    height="450px"
  >
    <div style="padding-top: 1rem">
      <mt-form ref="modelForm" :model="modelForm" :rules="rules">
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="testOrg" :label="$t('测试机构')">
              <mt-input
                v-model="modelForm.testOrg"
                show-clear-button
                :placeholder="$t('请输入，空代表不修改')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="reportTime" :label="$t('出具报告日期')">
              <mt-date-time-picker
                v-model="modelForm.reportTime"
                :open-on-focus="true"
                :allow-edit="false"
                :time-stamp="true"
                :placeholder="$t('请选择，空代表不修改')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="24">
            <mt-form-item prop="remark" :label="$t('备注')">
              <mt-input
                v-model="modelForm.remark"
                show-clear-button
                :placeholder="$t('请输入，空代表不修改')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {},
      rules: {},
      ids: []
    }
  },
  methods: {
    dialogInit(args) {
      const { title } = args
      this.dialogTitle = title
      this.modelForm = {}
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      let params = {
        ids: this.ids,
        ...this.modelForm
      }
      this.$refs.modelForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm', params)
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
