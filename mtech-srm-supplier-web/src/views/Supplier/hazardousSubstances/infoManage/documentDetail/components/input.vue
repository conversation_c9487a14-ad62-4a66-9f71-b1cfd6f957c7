<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :open-on-focus="true"
      :allow-edit="false"
      :placeholder="$t('')"
      :disabled="!isEdit"
      @change="inputChange"
    ></mt-input>
  </div>
</template>

<script>
export default {
  data() {
    return { data: {} }
  },
  computed: {
    dateValue() {
      return this.data[this.data.column.field]
    },
    isEdit() {
      return [10, '10', 11, '11', 15, '15', 40, '40'].includes(this.$route.query?.status)
    }
  },
  mounted() {},
  methods: {
    inputChange(e) {
      // this.data[this.data.column.field] = e
      const _dataSource = this.$parent.dataSource
      _dataSource.map((item) => {
        if (item.id === this.data['id']) {
          item[this.data.column.field] = e
        }
      })
    }
  },
  before<PERSON><PERSON><PERSON>() {}
}
</script>
