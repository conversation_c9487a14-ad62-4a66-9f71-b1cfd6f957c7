<template>
  <div class="detail-container">
    <div class="header">
      <div class="title">{{ $t('有害物质管理-单据详情') }}</div>
      <div class="operate-bar">
        <div v-if="isEdit" class="op-item mt-flex" @click="save">
          {{ $t('保存') }}
        </div>
        <div v-if="isEdit" class="op-item mt-flex" @click="submit">
          {{ $t('提交') }}
        </div>
        <div class="op-item mt-flex" @click="backToBusinessConfig">
          {{ $t('返回') }}
        </div>
      </div>
    </div>
    <div class="form-warp">
      <mt-form ref="formInstance" class="form-box" :model="formModel">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <!-- 单据号 -->
            <mt-form-item class="form-item" :label="$t('单据号')" label-style="top" prop="docNo">
              <mt-input v-model="formModel.docNo" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <!-- 供应商编码 -->
            <mt-form-item
              class="form-item"
              :label="$t('供应商编码')"
              label-style="top"
              prop="supplierCode"
            >
              <mt-input v-model="formModel.supplierCode" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <!-- 供应商名称 -->
            <mt-form-item
              class="form-item"
              :label="$t('供应商名称')"
              label-style="top"
              prop="supplierName"
            >
              <mt-input v-model="formModel.supplierName" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <!-- 提交日期 -->
            <mt-form-item
              class="form-item"
              :label="$t('提交日期')"
              label-style="top"
              prop="submitTime"
            >
              <mt-input v-model="formModel.createDate" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <!-- 单据状态 -->
            <mt-form-item class="form-item" :label="$t('单据状态')" label-style="top" prop="status">
              <mt-select
                v-model="formModel.status"
                :data-source="statusOptions"
                :show-clear-button="true"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('')"
                :disabled="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <!-- 提交人 -->
            <mt-form-item
              class="form-item"
              :label="$t('提交人')"
              label-style="top"
              prop="submitter"
            >
              <mt-input v-model="formModel.createUserName" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <!-- 承诺书 -->
            <mt-form-item class="form-item" :label="$t('承诺书')" label-style="top" prop="fileCode">
              <upload-file
                ref="uploadFile"
                v-if="formModel.id"
                v-model="formModel.fileCode"
                :row-id="formModel.id"
                @listChange="fileListChange"
                :disabled="isEdit"
              ></upload-file>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="22">
            <!-- 供应商备注 -->
            <mt-form-item
              class="form-item"
              style="width: 100%"
              :label="$t('供应商备注')"
              label-style="top"
              prop="supplierRemark"
            >
              <mt-input v-model="formModel.supplierRemark" :disabled="!isEdit"></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="22">
            <!-- 采方驳回理由 -->
            <mt-form-item
              style="width: 100%"
              class="form-item"
              :label="$t('采方驳回理由')"
              label-style="top"
              prop="rejectReason"
            >
              <mt-input v-model="formModel.rejectReason" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <div class="table-content">
      <div class="table-title">{{ $t('明细信息') }}</div>
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>
    </div>
    <BatchUpdateDialog ref="batchUpdateDialogRef" @confirm="batchUpdateConfirm" />
  </div>
</template>
<script>
import { editColumnData } from './config'
import uploadFile from './components/uploadFile.vue'
import dayjs from 'dayjs'
import cloneDeep from 'lodash/cloneDeep'

export default {
  components: {
    uploadFile,
    BatchUpdateDialog: () => import('./components/BatchUpdateDialog')
  },
  data() {
    return {
      supplierList: [],
      isEdit: false,
      statusOptions: [
        { text: this.$t('草稿'), value: 10 },
        { text: this.$t('待提交'), value: 11 },
        { text: this.$t('采方驳回'), value: 15 },
        { text: this.$t('待确认'), value: 20 },
        { text: this.$t('审批中'), value: 30 },
        { text: this.$t('审批驳回'), value: 40 },
        { text: this.$t('审批通过'), value: 50 },
        { text: this.$t('审批废弃'), value: 60 },
        { text: this.$t('审批撤回'), value: 70 }
      ],
      disabled: false, //禁用
      //表单数据
      formModel: {
        id: null,
        docNo: '', // 单据号
        status: 0, //单据状态
        supplierCode: '',
        supplierName: '',
        submitTime: '',
        submitter: '',
        rectificationCode: '',
        fileCode: '',
        fileList: [],
        rejectReason: '',
        sqeName: '',
        sqeAcct: ''
      },
      rules: {
        fileCode: [
          {
            required: true
          }
        ]
      },
      pageConfig: [
        {
          // title: this.$t('明细信息'),
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false
          },
          // toolbar: [{ id: 'batchUpdate', icon: '', title: this.$t('批量修改') }],
          grid: {
            allowPaging: false,
            columnData: [],
            lineIndex: 1,
            dataSource: [],
            asyncConfig: {
              url: '/supplier/user/harmful/category/head/queryLine',
              serializeList: (list) => {
                list.forEach((item) => {
                  if (!item?.harmfulCategoryFileRequestList) {
                    item.harmfulCategoryFileRequestList = null
                  }
                })
                return list
              },
              page: { current: 1, size: 20 },
              defaultRules: [
                {
                  label: 'ID',
                  field: 'mainFormId',
                  type: 'string',
                  operator: 'equal',
                  value: this.$route?.query.id
                }
              ]
            }
          }
        }
      ],
      factoryData: []
    }
  },
  computed: {
    form() {
      return this.$route?.query
    }
  },
  created() {
    this.initData()
    this.isRealChange = false
    setTimeout(() => {
      this.isRealChange = true
    }, 2000)
  },
  methods: {
    initData() {
      let _columnData = []
      const _userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      const _query = this.form
      if ([10, '10', 11, '11', 15, '15', '40', 40].includes(_query.status)) {
        _columnData = editColumnData
        this.isEdit = true
      } else {
        this.isEdit = false
        _columnData = editColumnData
      }
      this.pageConfig[0].grid.columnData = _columnData
      if (_query.type && _query.type === 'updateReport') {
        let list = JSON.parse(sessionStorage.getItem('sup-info-manage-detail-list'))
        let _sqeObj = list.filter((n) => n.sqeName)[0] ?? { sqeAcct: '', sqeName: '' }
        let _ids = list.map((item) => item.id).filter((n) => n)
        this.formModel.id = '1'
        this.formModel.docNo = ''
        this.formModel.status = 10
        this.formModel.supplierCode = _query.supplierCode
        this.formModel.supplierName = _query.supplierName
        this.formModel.createUserName = _userInfo.username
        this.formModel.sqeAcct = _sqeObj.sqeAcct
        this.formModel.sqeName = _sqeObj.sqeName
        this.formModel.createDate = dayjs(new Date()).format('YYYY-MM-DD')
        this.pageConfig[0].grid.asyncConfig.defaultRules = [
          {
            label: 'id',
            field: 'id',
            type: 'string',
            operator: 'in',
            value: _ids
          }
        ]
      } else {
        this.$API.hazardousSubstances
          .getHazardousSubstancesDocumentDetail({ id: _query.id })
          .then((res) => {
            this.formModel = res.data ?? this.formModel
            if (!this.isEdit) {
              this.formModel.submitter = _userInfo.username
              this.formModel.submitTime = dayjs(new Date()).format('yyyy-MM-dd')
            }
          })
      }
    },
    fileListChange(list = {}) {
      console.log(list)
      this.formModel.fileList = list?.harmfulCategoryFileRequestList ?? []
    },
    handleClickToolBar(e) {
      console.log(e)
      let selectRecords = e.grid.getSelectedRecords()
      if (selectRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'batchUpdate') {
        // TODO 批量修改
        this.$refs.batchUpdateDialogRef.dialogInit({
          title: this.$t('批量修改'),
          selectRecords
        })
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      if (e.tool.id === 'operateInfo') {
        this.$dialog({
          modal: () => import('../dataMaintenance/index.vue'),
          data: {
            status: this.$route?.query.status,
            data: () => {
              const _dataSource = e.grid.dataSource
              const obj = cloneDeep(_dataSource.filter((item) => item.id === e.data.id)[0])
              ;['bbp', 'cd', 'cr6', 'dbp', 'dehp', 'dibp', 'hg', 'pb', 'pbb', 'pbde'].map(
                (item) => {
                  obj[item] = obj[item] === null || obj[item] === 'null' ? null : Number(obj[item])
                }
              )
              return obj
            }
          },
          success: (data) => {
            const _dataSource = e.grid.dataSource
            const _lineInfo = _dataSource.filter((item) => item.id === e.data.id)[0]
            const _attrList = ['bbp', 'cd', 'cr6', 'dbp', 'dehp', 'dibp', 'hg', 'pb', 'pbb', 'pbde']
            _attrList.map((item) => {
              _lineInfo[item] = data[item]
            })
          }
        })
      } else if (e.tool.id === 'lineDelete') {
        const _dataSource = e.grid.dataSource
        let deleteIndex = _dataSource.map((item, index) => {
          if (item.id === e.data.id) {
            return index
          }
        })[0]
        _dataSource.splice(deleteIndex, 1)
        this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
      }
    },
    confirm() {
      const param = this.getSaveAndSubInfo()
      if (!param || !param.isPush) return
      if (this.form.type && this.form.type === 'updateReport') {
        delete param.id
        param.harmfulCategoryLineRequests.forEach((item) => {
          delete item.id
          if (
            item.harmfulCategoryLineRequests &&
            item.harmfulCategoryLineRequests instanceof Array
          ) {
            item.harmfulCategoryLineRequests.forEach((n) => delete n.id)
          }
        })
        param.harmfulCategoryFileRequestList.forEach((item) => delete item.id)
      }
      delete param.isPush
      delete param.fileList
      delete param.createDate
      delete param.createTime
      delete param.updateTime
      this.$API.hazardousSubstances
        .submitHazardousSubstancesInfo([param])
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
        .finally(() => {
          this.backToBusinessConfig()
        })
    },
    getSaveAndSubInfo() {
      const param = {
        isPush: false,
        ...this.formModel,
        harmfulCategoryFileRequestList: [], // 单据对象
        harmfulCategoryLineRequests: [] // 数据列表
      }
      const _list = this.$refs.templateRef.getCurrentTabRef()?.grid.getCurrentViewRecords()
      for (let i = 0; i < _list.length; i++) {
        const { testOrg, reportTime, nextReportTime, harmfulCategoryFileRequestList } = _list[i]
        // const { pb, cd, hg, cr6, pbb, pbde, dehp, dibp, bbp, dbp, testType }  = _list[i]
        const { testType } = _list[i]
        const keyList = ['pb', 'cd', 'hg', 'cr6', 'pbb', 'pbde', 'dehp', 'dibp', 'bbp', 'dbp']
        const obj = _list[i]
        for (let i = 0; i < keyList.length; i++) {
          let _keyValue = obj[keyList[i]]
          if (
            ![0, '0'].includes(_keyValue) &&
            !_keyValue &&
            _keyValue !== 'null' &&
            testType === 'A'
          ) {
            this.$toast({
              type: 'error',
              content: this.$t('测试明细数据未维护')
            })
            return
          }
        }
        if (!testOrg) {
          this.$toast({
            type: 'error',
            content: this.$t('测试机构不能为空')
          })
          return
        }
        if (!reportTime) {
          this.$toast({
            type: 'error',
            content: this.$t('出具报告日期不能为空')
          })
          return
        }
        if (!nextReportTime) {
          this.$toast({
            type: 'error',
            content: this.$t('下次报告更新日期不能为空')
          })
          return
        }
        if (!harmfulCategoryFileRequestList || harmfulCategoryFileRequestList.length <= 0) {
          this.$toast({
            type: 'error',
            content: this.$t('附件不能为空')
          })
          return
        }
      }
      if (this.formModel?.fileList) {
        param.harmfulCategoryFileRequestList = JSON.parse(JSON.stringify(this.formModel.fileList))
      }
      param.harmfulCategoryLineRequests = _list
      param.isPush = true
      return param
    },
    save() {
      const param = this.getSaveAndSubInfo()
      if (!param || !param.isPush) return
      if (this.form.type && this.form.type === 'updateReport') {
        delete param.id
        param.harmfulCategoryLineRequests.forEach((item) => {
          delete item.id
          if (
            item.harmfulCategoryLineRequests &&
            item.harmfulCategoryLineRequests instanceof Array
          ) {
            item.harmfulCategoryLineRequests.forEach((n) => delete n.id)
          }
        })
        param.harmfulCategoryFileRequestList.forEach((item) => delete item.id)
      }
      delete param.isPush
      delete param.fileList
      delete param.createDate
      delete param.createTime
      delete param.updateTime

      this.$API.hazardousSubstances
        .saveHazardousSubstancesInfo([param])
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            if (this.form.type && this.form.type === 'updateReport' && res.data) {
              this.$API.hazardousSubstances
                .getHazardousSubstancesDocumentDetail({ id: res.data })
                .then((res) => {
                  this.formModel.docNo = res.data?.docNo || this.formModel.docNo
                })
            }
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    submit() {
      this.confirm('saveAndRelease')
    },
    //返回列表页
    backToBusinessConfig() {
      this.$emit('cancel-function')
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.mt-form-item {
  width: calc(33% - 30px);
  min-width: 200px;
  display: inline-flex;
  margin-left: 10px;
  margin-right: 20px;
  .mt-input {
    width: 100%;
  }
}
.detail-container {
  background: #fff;
  height: 100%;
  .header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      height: 100%;
      line-height: 40px;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      padding: 5px 10px;
    }
    .operate-bar {
      height: 100%;
      float: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;

      .op-item {
        cursor: pointer;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }
  }
  .table-content {
    padding: 10px;
    .table-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
