import input from '../components/input.vue'

export const columnData = [
  {
    field: 'pb',
    width: 120,
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('Pb＜600ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'cd',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('Cd＜60ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'hg',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('Hg＜600ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'cr6',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('Cr6+＜600ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'pbb',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('PBB＜600ppmm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'pbde',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('PBDE＜600ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'dehp',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('DEHP＜600ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'dibp',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('DIBP＜600ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'bbp',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('BBP＜600ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  },
  {
    width: 120,
    field: 'dbp',
    headerTemplate: () => {
      const template = {
        template: `
          <div class="headers">
            <span style="color: red">*</span>
            <span class="e-headertext">{{$t('DBP＜600ppm')}}</span>
          </div>
        `
      }
      return { template }
    },
    template: () => ({ template: input })
  }
]
