<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="detail-container">
      <div class="table-content">
        <mt-template-page ref="templateRef" :hidden-tabs="true" :template-config="pageConfig">
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
export default {
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false
          },
          grid: {
            height: 'auto',
            allowPaging: false,
            columnData,
            dataSource: []
          }
        }
      ],
      rules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      uploadInfo: {} // 上传后信息
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return [10, '10', 11, '11', 15, '15', 40, '40'].includes(this.modalData.status)
    },
    fileInfo() {
      return this.modalData.fileInfo
    },
    dataSource() {
      return this.modalData.data()
    }
  },
  mounted() {
    this.initData()
    this.show()
    if (!this.isEdit) {
      this.buttons.pop()
      this.buttons[0].buttonModel = { content: this.$t('关闭') }
    }
  },
  methods: {
    initData() {
      this.$set(this.pageConfig[0].grid, 'dataSource', [this.dataSource])
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      const dataList = this.$refs.templateRef.getCurrentTabRef()?.grid.getCurrentViewRecords()
      const { pb, cd, hg, cr6, pbb, pbde, dehp, dibp, bbp, dbp } = dataList[0]
      if (![0, '0'].includes(pb) && (!pb || pb >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入Pb的值')
        })
        return
      }
      if (![0, '0'].includes(cd) && (!cd || cd >= 60)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入Cd的值')
        })
        return
      }
      if (![0, '0'].includes(hg) && (!hg || hg >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入Hg的值')
        })
        return
      }
      if (![0, '0'].includes(cr6) && (!cr6 || cr6 >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入Cr6的值')
        })
        return
      }
      if (![0, '0'].includes(pbb) && (!pbb || pbb >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入PBB的值')
        })
        return
      }
      if (![0, '0'].includes(pbde) && (!pbde || pbde >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入PBDE的值')
        })
        return
      }
      if (![0, '0'].includes(dehp) && (!dehp || dehp >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入DEHP的值')
        })
        return
      }
      if (![0, '0'].includes(dibp) && (!dibp || dibp >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入DIBP的值')
        })
        return
      }
      if (![0, '0'].includes(bbp) && (!bbp || bbp >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入BBP的值')
        })
        return
      }
      if (![0, '0'].includes(dbp) && (!dbp || dbp >= 600)) {
        this.$toast({
          type: 'error',
          content: this.$t('请按要求输入DBP的值')
        })
        return
      }

      if (pb + cd + hg + cr6 + pbb + pbde + dehp + dibp + bbp + dbp <= 0) {
        this.$toast({
          type: 'error',
          content: this.$t('明细数据不能全部为 0')
        })
        return
      }
      this.$bus.$emit('dataDetailChange', dataList[0])
      // this.cancel()
      this.$emit('confirm-function', dataList[0])
    },
    cancel() {
      this.$emit('cancel-function', '1111')
    }
  }
}
</script>
<style lang="scss">
.uploader-box {
  .mt-form-item__content {
    flex: 1;
  }
}
</style>
<style lang="scss" scoped>
.uploader-box {
  padding: 40px 20px 0;
}

.cell-upload {
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          font-size: 14px;
          display: inline-block;
          vertical-align: middle;
          margin: 0 10px;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
