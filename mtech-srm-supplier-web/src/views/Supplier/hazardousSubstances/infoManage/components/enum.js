import { i18n } from '@/main.js'

// 是否启用枚举
const Rohs2Options = [
  { text: i18n.t('启用'), value: 1 },
  { text: i18n.t('未启用'), value: 0 }
]
// 状态枚举
const StatusOptions = [
  { text: i18n.t('草稿'), value: 10 },
  { text: i18n.t('采方驳回'), value: 15 },
  { text: i18n.t('待确认'), value: 20 },
  { text: i18n.t('审批中'), value: 30 },
  { text: i18n.t('审批驳回'), value: 40 },
  { text: i18n.t('审批通过'), value: 50 },
  { text: i18n.t('审批废弃'), value: 60 },
  { text: i18n.t('审批撤回'), value: 70 }
]

export { Rohs2Options, StatusOptions }
