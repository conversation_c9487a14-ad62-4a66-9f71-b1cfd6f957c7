<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
      @keyup="value = value.replace(/^(0+)|[^\d]+/g, '')"
      @beforepaste="
        clipboardData.setData('text', clipboardData.getData('text').replace(/[^\d]/g, ''))
      "
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: false }
  },
  mounted() {
    if (this.data.column.field === 'categoryName') {
      this.disabled = true
      this.$bus.$on('maintainOutsideItemcategoryCode', (val) => {
        this.data.categoryName = val.categoryName
      }) //接受品类描述
    }
    if (this.data.column.field === 'minSupplierQuality') {
      this.$bus.$on('maintainOutsideItemcategoryCode', (val) => {
        this.data.categoryName = val.categoryName
      }) //接受品类描述
    }
  },
  methods: {
    onInput(e) {
      console.log('chile ma ', e)
      const res = e.replace(/[^\d.]/g, '')
      return res
    }
  }
}
</script>
