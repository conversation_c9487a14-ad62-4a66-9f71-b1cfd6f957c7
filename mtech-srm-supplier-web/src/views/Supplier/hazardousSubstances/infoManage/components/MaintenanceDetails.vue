<!-- 维护明细弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="1400px"
    height="500px"
  >
    <div class="dialog-content" style="padding-top: 1rem">
      <sc-table
        ref="sctableRef"
        grid-id="4421908e-0db3-407c-ac73-efc790704b38"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        keep-source
        :sortable="false"
        :is-show-right-btn="false"
        :is-show-refresh-bth="false"
      >
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      loading: false,
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          field: 'pb',
          title: this.$t('Pb < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.pb}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'cd',
          title: this.$t('Cd < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.cd}
                  min={0}
                  max={60}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'hg',
          title: this.$t('Hg < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.hg}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'cr6',
          title: this.$t('Cr6+ < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.cr6}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'pbb',
          title: this.$t('PBB < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.pbb}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'pbde',
          title: this.$t('PBDE < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.pbde}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'dehp',
          title: this.$t('DEHP < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.dehp}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'dibp',
          title: this.$t('DIBP < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.dibp}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'bbp',
          title: this.$t('BBP < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.bbp}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'dbp',
          title: this.$t('DBP < 600ppm'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.dbp}
                  min={0}
                  max={600}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        }
      ]
    }
  },
  methods: {
    dialogInit(args) {
      const { title, tableData } = args
      this.dialogTitle = title
      this.tableData = tableData
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      const tableData = this.tableRef.getTableData().visibleData
      let row = tableData[0]
      let isALLZero = true
      for (const key in row) {
        if (key !== 'id' && row[key] !== 0) {
          isALLZero = false
          break
        }
      }
      if (isALLZero) {
        this.$toast({
          content: this.$t('明细数据不能全部为 0'),
          type: 'warning'
        })
        return
      }
      this.$emit('confirm', row)
      this.handleClose()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
