<!-- 品类选择弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="1400px"
    height="780px"
  >
    <div class="dialog-content" style="padding-top: 1rem">
      <sc-table
        ref="scTableRef"
        grid-id="20ae6daa-4c4f-452d-9fd3-bccbe4336c8d"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        keep-source
        :sortable="false"
        :is-show-right-btn="true"
        :is-show-refresh-bth="true"
        @refresh="handleSearch"
      >
      </sc-table>
      <mt-page
        ref="pageRef"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { companyTypeOptions } from '../config/index'
export default {
  components: { ScTable },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      companyType: null,

      companyTypeOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'companyType',
          title: this.$t('公司类型'),
          formatter: ({ cellValue }) => {
            let item = this.companyTypeOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司编码')
        },
        {
          field: 'companyName',
          title: this.$t('公司名称')
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称')
        },
        {
          field: 'supplierContactUserMailbox',
          title: this.$t('供方接收信息邮箱')
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码')
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称')
        },
        {
          field: 'sqeAcct',
          title: this.$t('SQE负责人账号')
        },
        {
          field: 'sqeName',
          title: this.$t('SQE负责人姓名')
        }
      ]
    }
  },
  methods: {
    dialogInit(args) {
      const { title, companyType } = args
      this.dialogTitle = title
      this.companyType = companyType
      this.handleSearch()
      this.$refs.dialog.ejsRef.show()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    getTableData() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        status: 1, // 已生效
        companyType: this.companyType,
        supplierCode: userInfo.accountName || userInfo.enterpriseCode
      }
      this.loading = true
      this.$API.hazardousSubstances
        .pageDirectorCategoryApi(params)
        .then((res) => {
          if (res.code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)

            const records = res.data?.records || []
            this.tableData = records
          }
        })
        .finally(() => (this.loading = false))
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1) {
        this.$toast({ content: this.$t('只能选择一行'), type: 'warning' })
        return
      }
      let row = selectedRecords[0]
      this.$emit('confirm', row)
      this.handleClose()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
