<template>
  <div class="full-height">
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :selected-item="selectedItem"
      :data-source="tableList"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <TabHistory v-show="tabIndex === 0" />
      <TabSummary v-show="tabIndex === 1" />
      <TabSummaryDetail v-show="tabIndex === 2" />
      <TabOverdue v-show="tabIndex === 3" />
    </div>
    <!-- <mt-template-page
      ref="tepPage"
      :hidden-tabs="false"
      :current-tab="tabIndex"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
      @parentcategoryCode="parentcategoryCode"
      @handleCustomReset="handleCustomReset"
      @reportTimeChange="reportTimeChange"
      @handleSelectTab="handleSelectTab"
    >
      <div slot="slot-filter" class="zero-filter-switch">
        <div v-if="tabIndex === 2" class="form-overdue">
          <span>{{ $t('超期快查') }}：</span>
          <span>
            <mt-select
              v-model="searchFormModel.overdue"
              :data-source="overdueOptions"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
              :fields="{ text: 'text', value: 'value' }"
              @change="selectChange"
            />
          </span>
        </div>
      </div>
    </mt-template-page> -->
  </div>
</template>

<script>
// import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
import {
  toolbar,
  SummaryToolbar,
  overdueToolbar,
  columnData,
  overdueColumnData,
  SummaryColumnData
} from './config/index'
import { getHeadersFileName, download } from '@/utils/utils.js'
import {
  levelENUM,
  domainENUM,
  quoteMethodENUM,
  mapSelectionENUM,
  costMouldENUM,
  effectiveMethodENUM,
  deliveryMethodENUM,
  bussinessUnitENUM,
  gradingResultENUM
  // getCurPerson
} from './components/enum.js'
import cloneDeep from 'lodash/cloneDeep'
import utils from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    TabHistory: require('./pages/TabHistory.vue').default,
    TabSummary: require('./pages/TabSummary.vue').default,
    TabSummaryDetail: require('./pages/TabSummaryDetail.vue').default,
    TabOverdue: require('./pages/TabOverdue.vue').default
  },
  data() {
    // const tabIndex = sessionStorage.getItem('tabIndex') ?? 0
    return {
      tabIndex: this.$route.query.currentIndex ?? 0,
      tableList: [
        {
          title: this.$t('待提交')
        },
        {
          title: this.$t('汇总查询')
        },
        {
          title: this.$t('汇总明细查询')
        },
        {
          title: this.$t('已归档/超期查询')
        }
      ],
      selectedItem: this.$route.query.currentIndex ?? 0,

      tabSelectShow: false,
      overdueOptions: [
        { text: this.$t('已超期'), value: 0 },
        { text: this.$t('一个月内超期'), value: 1 },
        { text: this.$t('三个月内超期'), value: 2 }
      ],
      searchFormModel: {
        overdue: ''
      }, //自定义模板
      tempPersonList: [],
      categoryCodeObj: {},
      pageConfig: [
        {
          title: this.$t('待提交'),
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: 'd216f2c3-a0b4-4822-b68e-e0781762b2f0',
          grid: {
            columnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/tenant/harmful/category/record/query'
            }
          }
        },
        {
          title: this.$t('提交历史'),
          toolbar: SummaryToolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '00412257-0fbb-4d0b-8dcd-64a0521a3791',
          grid: {
            columnData: SummaryColumnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/user/harmful/category/head/query'
            }
          }
        },
        {
          title: this.$t('已归档/超期查询'),
          toolbar: overdueToolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '3b4c5894-591e-4726-a5f7-b6757dd0a70d',
          grid: {
            columnData: overdueColumnData,
            allowEditing: false, //开启表格编辑操作
            asyncConfig: {
              url: '/supplier/user/harmful/category/head/getLastByPage',
              defaultRules: []
            }
          }
        }
      ],
      levelENUM,
      domainENUM,
      quoteMethodENUM,
      mapSelectionENUM,
      costMouldENUM,
      effectiveMethodENUM,
      deliveryMethodENUM,
      bussinessUnitENUM,
      gradingResultENUM,
      // getCurPerson,
      //------------------------>
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'categoryRelationshipReport',
        templateUrl: '', // 下载模板接口方法名
        uploadUrl: 'import', // 上传接口方法名
        file: 'multipartFile' //后端接收参数名
      },
      categoryCodeENUM: [] //品类数据相关值集
    }
  },
  computed: {},
  mounted() {
    // this.init()
    this.filtering = utils.debounce(this.filtering, 1000)

    this.getCategoryInfos()
  },
  beforeDestory() {
    sessionStorage.removeItem('tabIndex')
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 点击单元格文字
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'docNo') {
        sessionStorage.setItem('tabIndex', Number(e.tabIndex))
        // 跳转计划清单详情页面
        this.$router.push({
          path: '/supplier/hazardous-substances/sup-info-manage-detail',
          query: {
            ...data,
            date: new Date().getTime()
          }
        })
      }
    },
    reportTimeChange(obj) {
      const dataList = this.$refs.templateRef.getCurrentTabRef()?.grid.getCurrentViewRecords()
      const rowData = dataList.filter((item) => item.id === obj.id)[0]
      rowData[obj.key] = dayjs(obj.date).add(1, 'year')
    },
    // handleSelectTab(e) {
    //   if (e === '2') {
    //     this.tabSelectShow = true
    //   } else {
    //     this.tabSelectShow = false
    //   }
    // },
    // 超期查询
    selectChange(e) {
      console.log(e, 'selectChange')
      let _data = [
        {
          label: this.$t('超期快查'),
          field: 'overdue',
          operator: 'equal',
          type: 'string',
          value: String(e.itemData?.value)
        }
      ]
      this.$set(this.pageConfig[2].grid.asyncConfig, 'defaultRules', _data)
    },
    // 获取数据
    async init() {
      const params = {
        page: { current: 1, size: 10000 }
      }
      const res = await this.$API.categoryConfigurationReport.getPageInfo(params)

      res.data.records = res.data.records.map((item) => {
        const personConfig = JSON.parse(item.personConfig)
        for (let key in personConfig) {
          const curFieldName = key.replace('_NAME', '')
          item[curFieldName] = personConfig[key]
        }
        const templateConfig = JSON.parse(item.templateConfig)
        for (let key in templateConfig) {
          item[key] = templateConfig[key]
        }
        return item
      })
      console.log('resresres', res)
      this.pageConfig[0].grid.dataSource = res.data.records
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 获取品类相关数据
    getCategoryInfos() {
      this.$API.maintainOutsideItem
        .criteriaQuery()
        .then((res) => {
          let _data = cloneDeep(res.data)
          _data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          this.categoryCodeENUM = _data
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    // 模糊搜索品类
    filtering(e) {
      this.$loading()
      let params = {
        fuzzyNameOrCode: e.text
      }
      this.$API.maintainOutsideItem
        .criteriaQuery(params)
        .then((res) => {
          res.data.map((item) => {
            item.text = item.categoryCode + '-' + item.categoryName
          })
          e.updateData(res.data)
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('查询失败，请重试'),
            type: 'error'
          })
        })
    },
    // 品类下拉
    selectCategory(e) {
      const { itemData } = e
      this.searchFormModel.categoryName = itemData.categoryName
    },
    // 同步数据
    parentcategoryCode(val) {
      if (val.fieldCode === 'categoryName') {
        Object.assign(this.categoryCodeObj, val.itemInfo || {})
      } else if (this.tempPersonList === null) {
        this.tempPersonList = [val]
      } else if (this.tempPersonList.some((item) => item.fieldCode === val.fieldCode)) {
        const index = this.tempPersonList.findIndex((item) => item.fieldCode === val.fieldCode)
        this.tempPersonList.splice(index, 1)
        this.tempPersonList.push(val)
      } else {
        this.tempPersonList.push(val)
      }
    },
    commandClick(args) {
      if (args.commandColumn.type === 'Deletes') {
        // 如果不存在id,直接删除(获取不到rowIndex,暂定刷新)
        if (!args.rowData.id) {
          this.$refs.tepPage.refreshCurrentGridData()
          return
        }
        let params = {
          id: args.rowData.id
        }
        this.$API.categoryRelationshipReport.remove(params).then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
        })
      } else if (args.commandColumn.type === 'history') {
        this.$dialog({
          modal: () =>
            import(/* webpackChunkName: "components/upload" */ './components/history.vue'),
          data: {
            title: this.$t('历史记录'),
            id: args.rowData.id
          },
          success: () => {}
        })
      }
    },
    handleClickCellTool(e) {
      console.log(e)
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.$router.push({
          path: '/supplier/hazardous-substances/sup-info-manage-detail',
          query: {
            ...data,
            date: new Date().getTime()
          }
        })
        sessionStorage.setItem('tabIndex', e.tabIndex)
      } else if (tool.id === 'delete') {
        this.handleClickDeleteRow(e.data)
      }
    },
    handleClickDeleteRow(obj) {
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .deleteHazardousSubstancesHistoryInfo([obj.id])
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('删除成功')
            })
            this.refreshPage()
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClickToolBar(e) {
      let records = e.data ? [e.data] : e.grid.getSelectedRecords()
      if (e.toolbar.id === 'export') {
        this.handleClickExport()
        return
      }
      if (e.toolbar.id === 'overdueExport') {
        this.handleClickOverdueExport()
        return
      }
      if (e.toolbar.id === 'roHsExport') {
        this.handleClickRoHsExport()
        return
      }
      if (e.toolbar.id === 'historyExport') {
        this.handleClickHistoryExport(records)
        return
      }
      if (e.toolbar.id === 'ArchivedExport') {
        this.handleClickArchivedExport(records)
        return
      }
      if (records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // if (e.toolbar.id === 'ArchivedUpdateReport' && records.length > 1) {
      //   this.$toast({ content: this.$t('只能选择一条数据'), type: 'warning' })
      //   return
      // }
      if (e.toolbar.id === 'confirmReport') {
        this.handleClickConfirmReport(records)
      } else if (e.toolbar.id === 'ArchivedUpdateReport') {
        let _flag = true
        records.map((item, index) => {
          if (records[index + 1]) {
            if (item.sqeName !== records[index + 1].sqeName) {
              _flag = false
            }
          }
        })
        if (!_flag) {
          this.$toast({
            type: 'warning',
            content: this.$t('所选数据的SEQ负责人必须相同')
          })
          return
        }
        this.handleClickUpdateReport(records)
      }
    },
    // 汇总查询 - 查看OA审批
    handleClickDetailApproval(list) {
      window.open(list.oaUrl)
    },
    getRules() {
      // 获取查询条件
      const _rule = this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: _rule.condition || '',
        page: { current: 1, size: 20000 },
        rules: _rule.rules || []
      }
      return params
    },
    // 提交历史 - 导出
    handleClickHistoryExport() {
      // 获取查询条件
      const _params = this.getRules()
      this.$API.hazardousSubstances.hazardousSubstancesHistoryExport(_params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 已归档/超期查询 - 导出
    handleClickArchivedExport() {
      // 获取查询条件
      const _params = this.getRules()
      _params['defaultRules'] = [
        {
          label: '超期快查',
          field: 'overdue',
          operator: 'equal',
          type: 'string',
          value: String(this.searchFormModel.overdue)
        }
      ]
      this.$API.hazardousSubstances.hazardousSubstancesArchivedExport(_params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickRoHsExport() {
      // 获取查询条件
      const _params = this.getRules()
      this.$API.hazardousSubstances.hazardousSubstancesRoHsExport(_params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickUpdateReport(list = {}) {
      console.log(list)
      sessionStorage.setItem('sup-info-manage-detail-list', JSON.stringify(list))
      this.$router.push({
        path: '/supplier/hazardous-substances/sup-info-manage-detail',
        query: {
          ...list[0],
          id: list.headId,
          status: 10,
          type: 'updateReport',
          date: new Date().getTime()
        }
      })
    },
    // 提交报告
    handleClickConfirmReport(list = []) {
      const _ids = list.map((item) => item.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .hazardousSubstancesConfirm(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.refreshPage()
          this.$store.commit('endLoading')
        })
    },
    handleClickrejectSupplier(list = []) {
      const _ids = list.map((item) => item.id).filter((item) => item !== null)
      this.$store.commit('startLoading')
      this.$API.hazardousSubstances
        .hazardousSubstancesConfirmApprovals(_ids)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg ?? '操作失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg ?? '操作失败')
          })
        })
        .finally(() => {
          this.refreshPage()
          this.$store.commit('endLoading')
        })
    },
    handleClickToolBarAdd() {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // 删除
    handleClickToolBarDelete(val) {
      if (val != null && val.some((item) => item.id != null)) {
        const ids = val.map((item) => item.id).filter((item) => item !== null)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除数据？')
          },
          success: () => {
            this.$API.hazardousSubstances.deleteHazardousSubstancesInfo(ids).then((res) => {
              if (res && res.code === 200) {
                this.$toast({
                  type: 'success',
                  content: this.$t(res.msg)
                })
                this.refreshPage()
              }
            })
          }
        })
      }
    },
    // refresh页面
    refreshPage() {
      this.$refs.tepPage.refreshCurrentGridData()
    },
    // 导入
    handleClickToolBarUpload() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'file',
          importApi: this.$API.hazardousSubstances.importHazardousSubstancesInfo,
          downloadTemplateApi: this.$API.hazardousSubstances.importTemplateDown
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
        }
      })
    },
    // 导出
    handleClickToolBarDownload() {
      const _rule = this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: _rule.condition || '',
        page: { current: 1, size: 10000 },
        rules: _rule.rules || []
      }
      this.$API.hazardousSubstances.exportHazardousSubstancesList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.handleSave(data, rowIndex)
      }
    },
    // 保存
    handleSave(rowData, rowIndex = 0) {
      console.log('rowDatarowData', rowData)
      this.$API.hazardousSubstances
        .addHazardousSubstancesInfo([rowData])
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.tepPage.refreshCurrentGridData()
          } else {
            this.startEdit(rowIndex)
          }
        })
        .catch(() => {
          this.startEdit(rowIndex)
        })
    },
    startEdit(rowIndex) {
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
      this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    actionBegin(args) {
      const { requestType, data } = args
      console.log('datadatadata', data, requestType)
      if (requestType === 'save') {
        const validateMap = {
          categoryCode: {
            value: data.categoryCode,
            msg: this.$t('请填写品类编码')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background-color: #fff;
  .e-color {
    color: #000;
  }
}
/deep/ .zero-filter-switch {
  & .form-overdue {
    display: flex;
    justify-content: center;
    align-items: center;
    & span:nth-child(1) {
      font-size: 14px;
      font-weight: 600;
    }
  }
}
</style>
