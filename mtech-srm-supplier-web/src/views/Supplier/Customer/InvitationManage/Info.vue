<template>
  <div class="invitation-detail">
    <div class="header-status fbox">
      <div class="header-logo">{{ info.inviteNo | filterNo }}</div>
      <div class="header-content flex1 fbox">
        <div class="titles-box">
          <div class="mian-title">{{ info.invitationName || '--' }}</div>
          <div class="sub-title fbox">
            <div class="normal-title" v-if="info.id">{{ $t('邀请单ID') }}：{{ info.id }}</div>
            <div class="normal-title" v-if="info.inviterName">
              {{ $t('邀请人') }}：{{ info.inviterName }}
            </div>
            <div class="normal-title" v-if="info.createTime">
              {{ $t('邀请时间') }}：{{ info.createTime }}
            </div>
          </div>
        </div>
        <div class="btns-box fbox">
          <div class="invite-btn" @click="onBack">
            <mt-button css-class="e-info">{{ $t('返回') }}</mt-button>
          </div>
          <div class="invite-btn" @click="onRefuse">
            <mt-button css-class="e-info">{{ $t('拒绝') }}</mt-button>
          </div>
          <div class="invite-btn" @click="onAccept">
            <mt-button css-class="e-info">{{ $t('接受') }}</mt-button>
          </div>
        </div>
      </div>
    </div>

    <div class="supplier-relation">
      <mt-template-page
        :padding-top="true"
        :use-tool-template="false"
        :template-config="componentConfig"
      >
      </mt-template-page>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomerInvitationInfo',
  filters: {
    filterNo: function (value) {
      if (!value) {
        return 'D'
      }
      return value.substr(0, 1)
    }
  },
  data() {
    return {
      info: {},
      componentConfig: [
        {
          gridId: 'ae034e31-cde4-4a22-9128-335c21d69fda',
          title: this.$t('供应关系'),
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowPaging: false,
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              },
              {
                width: '100',
                field: 'customerEnterpriseCode',
                cssClass: 'field-content',
                headerText: this.$t('公司编码')
              },
              {
                width: '100',
                field: 'customerEnterpriseName',
                headerText: this.$t('公司名称')
              },
              {
                width: '100',
                field: 'customerFactoryCode',
                headerText: this.$t('工厂编码')
              },
              {
                width: '100',
                field: 'customerFactoryName',
                headerText: this.$t('工厂名称')
              },
              {
                width: '100',
                field: 'customerCategoryCode',
                headerText: this.$t('品类编码')
              },
              {
                width: '100',
                field: 'customerCategoryName',
                headerText: this.$t('品类名称')
              }
            ],
            dataSource: []
          }
        }
      ]
    }
  },
  created() {
    if (this.$route.params.id) {
      this.getInvitationInfo(this.$route.params.id)
    }
  },
  methods: {
    onBack() {
      this.$router.go(-1)
    },
    // 接受邀请
    onAccept() {
      this.$dialog({
        data: {
          title: this.$t('警告'),
          message: `是否确定接受${this.info.inviterName}的邀请？`
        },
        success: () => {
          this.$API.customerInvitation
            .handleInviteStatus({
              idList: [this.info.id],
              inviteStatus: 3
            })
            .then(() => {
              this.onBack()
            })
            .catch((error) => {
              this.$toast({
                content: error.msg,
                type: 'warning'
              })
            })
        }
      })
    },
    // 拒绝邀请
    onRefuse() {
      this.$dialog({
        data: {
          title: this.$t('警告'),
          message: `是否确定拒绝${this.info.inviterName}的邀请？`
        },
        success: () => {
          this.$API.customerInvitation
            .handleInviteStatus({
              idList: [this.info.id],
              inviteStatus: 4
            })
            .then(() => {
              this.onBack()
            })
            .catch((error) => {
              this.$toast({
                content: error.msg,
                type: 'warning'
              })
            })
        }
      })
    },
    getInvitationInfo(id) {
      this.$API.customerInvitation.getInviteInfo({ id }).then((res) => {
        this.info = res.data
        !!res.data.extList &&
          res.data.extList.length > 0 &&
          this.$set(this.componentConfig[0].grid, 'dataSource', res.data.extList)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: center;
}
.flex1 {
  flex: 1;
}
.return-btn {
  margin-bottom: 20px;
}
.invitation-detail {
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  margin-top: 20px;
  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .header-logo {
      border-radius: 100%;
      width: 60px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 70, 156, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      font-size: 40px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      text-align: center;
    }
    .header-content {
      margin-left: 20px;
      justify-content: space-between;
      .titles-box {
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;

          font-weight: 600;
          color: rgba(41, 41, 41, 1);
          margin-bottom: 20px;
        }
        .sub-title {
          font-size: 12px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-top: 16px;
          .normal-title {
            font-size: 12px;

            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        max-width: 300px;
        .invite-btn {
          cursor: pointer;
          margin-right: 30px;
        }

        .invite-btn:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.supplier-relation {
  padding: 20px;
  .mian-title {
    width: 76px;
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
    border-bottom: 2px solid rgba(0, 70, 156, 1);
  }
}
.company-info-box {
  .info-item {
    display: inline-flex;
    width: 33.3%;
    font-size: 14px;
    color: #35404e;
    margin-bottom: 25px;
    line-height: 18px;
    .label {
      display: inline-block;
      width: 120px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .info {
      display: inline-block;
      text-align: left;
      max-width: 360px;
    }
  }
}
.accept-btn {
  margin-right: 30px;
}
</style>
