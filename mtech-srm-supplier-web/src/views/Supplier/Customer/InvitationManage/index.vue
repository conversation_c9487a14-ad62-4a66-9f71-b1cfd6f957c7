<template>
  <div class="customer-invitation-wrapper">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :padding-top="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnUn, columnHas } from './config/index.js'
export default {
  name: 'CustomerInvitation',
  data() {
    return {
      pageConfig: [
        {
          gridId: 'b12a403a-7e4e-4d09-af4b-3ba76ed34037',
          tab: { title: this.$t('待处理') + '（0）' },
          toolbar: [[], ['Filter', 'Refresh', 'Setting']],
          grid: {
            columnData: columnUn,
            asyncConfig: {
              url: '/supplier/tenant/supplier/invite/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  field: 'inviteStatus',
                  label: '',
                  operator: 'equal',
                  type: 'number',
                  value: 2
                }
              ],
              afterAsyncData: this.updateTabTitle
            }
          }
        },
        {
          tab: { title: this.$t('已处理') },
          toolbar: { toolbar: [[], ['Filter', 'Refresh', 'Setting']] },
          grid: {
            columnData: columnHas,
            asyncConfig: {
              url: '/supplier/tenant/supplier/invite/list',
              rules: [
                //配置rules，数组格式，请求入参时，追加到rules中
                {
                  field: 'inviteStatus',
                  label: '',
                  operator: 'equal',
                  type: 'number',
                  value: 3
                }
              ]
            }
          }
        }
      ],

      currentTabIndex: 0,
      tab0PageInfo: {
        size: 10,
        current: 1
      },
      tab1PageInfo: {
        size: 10,
        current: 1
      }
    }
  },
  mounted() {},
  methods: {
    // asyncConfig 异步请求之后 返回的数据
    updateTabTitle(data) {
      let { code, data: innerData } = data
      if (code === 200 && !!innerData.records && innerData.records.length >= 0) {
        this.$set(
          this.pageConfig[0].tab,
          'title',
          this.$t('待处理') + `（${innerData.records.length}）`
        )
      }
    },

    handleClickToolBar(e) {
      // 表格顶部 toolbar
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(e.toolbar.id == 'Filter' || e.toolbar.id == 'Refresh' || e.toolbar.id == 'Setting')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Refresh') {
        this.handleRefresh()
      }
    },

    handleClickCellTool(a) {
      // 单元格 图标点击 tool
      console.log('use-handleClickCellTool', a)
    },

    handleClickCellTitle(e) {
      //单元格Title点击
      this.$router.push({
        name: 'customer-invitation-info',
        params: {
          type: 'view',
          id: e.data.id
        }
      })
    }
  }
}
</script>

<style lang="scss">
.customer-invitation-wrapper {
  height: 100%;

  .status-item {
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
    background: rgba(99, 134, 193, 0.1);
    border-radius: 2px;
    opacity: 10;
    font-size: 12px;
    font-weight: 500;
    color: #6386c1;
  }
}
</style>
