import Vue from 'vue'
import { i18n } from '@/main.js'

export const columnUn = [
  //列数据配置，具体可参考mt-data-grid组件
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'id',
    headerText: i18n.t('邀请单ID'),
    width: '150',
    cellTools: [
      //用户单元格按钮，详见celltools备注。
    ]
  },
  {
    field: 'invitationName',
    headerText: i18n.t('邀请单名称'),
    width: '150'
  },
  {
    field: 'inviterName',
    width: '150',
    headerText: i18n.t('邀请人')
  },
  {
    field: 'inviterMobile',
    width: '150',
    headerText: i18n.t('邀请人联系方式')
  },
  {
    field: 'inviterEmail',
    width: '150',
    headerText: i18n.t('邀请人邮箱')
  },
  {
    width: '150',
    headerText: i18n.t('状态'),
    template: () => {
      return {
        template: Vue.component('status-box', {
          template: `<div class="status-box">
              <span class="status-item" v-if="tmpStatus === 1">{{ $t('待处理') }}</span>
              <span class="status-item" v-if="tmpStatus === 2">{{ $t('待确认') }}</span>
              <span class="status-item" v-if="tmpStatus === 3">{{ $t('已确认') }}</span>
              <span class="status-item" v-if="tmpStatus === 4">{{ $t('已拒绝') }}</span>
            </div>`,
          computed: {
            tmpStatus() {
              return this.data.inviteStatus
            }
          }
        })
      }
    }
  }
]

export const columnHas = [
  //列数据配置，具体可参考mt-data-grid组件
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'customerTenantCode',
    headerText: i18n.t('客户企业编码'),
    width: '150',
    cellTools: []
  },
  {
    field: 'customerTenantName',
    width: '150',
    headerText: i18n.t('客户企业名称')
  },
  {
    field: 'inviterName',
    width: '150',
    headerText: i18n.t('邀请人')
  },
  {
    field: 'inviterMobile',
    width: '150',
    headerText: i18n.t('邀请人联系方式')
  },
  {
    field: 'inviterEmail',
    width: '150',
    headerText: i18n.t('邀请人联系邮箱')
  },
  {
    width: '150',
    headerText: i18n.t('状态'),
    template: () => {
      return {
        template: Vue.component('status-box', {
          template: `<div class="status-box">
              <span class="status-item" v-if="tmpStatus === 1">{{ $t('待处理') }}</span>
              <span class="status-item" v-if="tmpStatus === 2">{{ $t('待确认') }}</span>
              <span class="status-item" v-if="tmpStatus === 3">{{ $t('已确认') }}</span>
              <span class="status-item" v-if="tmpStatus === 4">{{ $t('已拒绝') }}</span>
            </div>`,
          computed: {
            tmpStatus() {
              return this.data.inviteStatus
            }
          }
        })
      }
    }
  },
  {
    field: 'handlerName',
    width: '150',
    headerText: i18n.t('处理人')
  },
  {
    field: 'handleTime',
    width: '150',
    headerText: i18n.t('处理时间'),
    valueConverter: {
      type: 'date'
    }
  }
]
