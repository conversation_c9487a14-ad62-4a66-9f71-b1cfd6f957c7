import { i18n } from '@/main.js'
import Vue from 'vue'

export const invitationToolbar = [[], ['Filter', 'Refresh', 'Setting']]
export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'customerEnterpriseCode',
    headerText: i18n.t('客户编码'),
    cellTools: []
  },
  {
    width: '210',
    field: 'customerEnterpriseName',
    headerText: i18n.t('客户公司名')
  },
  {
    width: '130',
    field: 'currentStageName',
    headerText: i18n.t('供应商准入阶段')
  },
  {
    width: '300',
    field: 'successCount',
    headerText: i18n.t('任务提交进度'),
    template: () => {
      return {
        template: Vue.component('successCount', {
          template: `
                <div class="range-box mt-flex">
                    <div class="rang-wrap">
                        <div class="rang-inner" v-bind:style="{ width: process+'%'}"></div>
                    </div>
                    <div class="rang-txt">{{data.submittedCount}}/{{data.totalCount}}</div>
                </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            process() {
              let pos = (this.data.successCount / this.data.totalCount).toFixed(2)
              return pos * 100
            }
          },
          mounted() {
            console.log(this.$parent, 'this')
          },
          methods: {
            changeStatus() {
              this.$parent.$emit('handleClickCellTool', {
                data: this.data
              })
            }
          }
        })
      }
    }
  },
  {
    width: '300',
    field: 'totalCount',
    headerText: i18n.t('待客户处理任务'),
    template: () => {
      return {
        template: Vue.component('successCount', {
          template: `
              <div class="range-box mt-flex">
                  <div class="rang-wrap">
                      <div class="rang-inner" v-bind:style="{ width: process+'%'}"></div>
                  </div>
                  <div class="rang-txt">{{data.submittedCount}}/{{data.totalCount}}</div>
              </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            process() {
              let pos = (
                this.data.totalCount -
                this.data.successCount / this.data.totalCount
              ).toFixed(2)
              return pos * 100
            }
          },
          mounted() {
            console.log(this.$parent, 'this')
          },
          methods: {
            changeStatus() {
              this.$parent.$emit('handleClickCellTool', {
                data: this.data
              })
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'createDate',
    headerText: i18n.t('供应关系创建日期'),
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  }
]
