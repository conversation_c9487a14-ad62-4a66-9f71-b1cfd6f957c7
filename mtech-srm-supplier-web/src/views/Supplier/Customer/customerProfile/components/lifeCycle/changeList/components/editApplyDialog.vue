<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="applyName" :label="$t('申请单名称')">
          <mt-input
            v-model="formObject.applyName"
            float-label-type="Never"
            :placeholder="$t('请输入申请单名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('调查表')">
          <mt-input
            :disabled="true"
            v-model="formObject.taskInstanceName"
            :show-clear-button="false"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('信息变更关联客户')">
          <mt-input
            :disabled="true"
            v-model="formObject.relationNames"
            :show-clear-button="false"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('原因说明')" prop="applyReason">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            float-label-type="Never"
            v-model="formObject.applyReason"
            :placeholder="$t('请输入变更原因')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      formObject: {
        applyName: null, //申请单名称
        applyReason: null,
        taskInstanceName: null, //任务实例名称
        relationNames: null //关联客户
      },
      formRules: {
        applyName: [{ required: true, message: this.$t('请输入申请单名称'), trigger: 'blur' }],
        applyReason: [{ required: true, message: this.$t('请输入变更申请原因'), trigger: 'blur' }]
      },
      paramsObject: {},
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      let _data = { ...this.modalData.data }
      this.getFormDetail(_data.id)
    }
  },
  methods: {
    getFormDetail(id) {
      //表单详情
      this.$API.customerProfileChange.applyInfoDetail({ id }).then((res) => {
        this.paramsObject = res.data
        let { applyInfoDTO, formTaskDTO, relationDTOList } = res.data
        this.formObject.applyName = applyInfoDTO.applyName
        this.formObject.applyReason = applyInfoDTO.applyReason
        this.formObject.taskInstanceName = formTaskDTO.taskInstanceName
        let _relations = []
        relationDTOList.forEach((e) => {
          _relations.push(e.customerEnterpriseName)
        })
        this.formObject.relationNames = _relations.join(',')
      })
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          let submitParams = { ...this.paramsObject }
          submitParams.applyInfoDTO.applyName = params.applyName
          submitParams.applyInfoDTO.applyReason = params.applyReason
          if (this.editStatus) {
            this.$API.customerProfileChange.updateApplyInfo(submitParams).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          } else {
            // // delete params.id;
            // this.$API.customerProfileChange
            //   .addApplyInfo(submitParams)
            //   .then((res) => {
            //     if (res.code == 200) {
            //       this.$emit("confirm-function");
            //     }
            //   });
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
