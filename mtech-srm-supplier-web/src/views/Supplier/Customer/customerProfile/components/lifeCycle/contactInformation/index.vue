<template>
  <div class="contact-container mt-flex-direction-column">
    <div class="contact-item supplier-contact mt-flex-direction-column">
      <div class="contact-title">{{ $t('供方联系人信息') }}</div>
      <div class="contact-grid">
        <mt-template-page
          ref="templateSupplyRef"
          :template-config="pageConfigSupply"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
        />
      </div>
    </div>
    <div class="contact-item purchase-contact mt-flex-direction-column">
      <div class="contact-title">{{ $t('采方联系人信息') }}</div>
      <div class="contact-grid">
        <mt-template-page ref="templatePurchaseRef" :template-config="pageConfigPurchase" />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfigPurchase, pageConfigSupply } from './config/index'
export default {
  data() {
    return {
      pageConfigSupply,
      pageConfigPurchase,
      supplyId: null
    }
  },
  props: {},
  mounted() {
    let supplyId = this.$route.query.id
    if (!supplyId) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.supplyId = supplyId
    this.resetAsyncConfigParams()
  },

  methods: {
    //列表参数重新赋值
    resetAsyncConfigParams() {
      this.$set(this.pageConfigSupply[0].grid, 'asyncConfig', {
        url: this.$API.customerProfileContact.getSupplyContactList,
        params: {
          partnerRelationId: this.supplyId
        }
      })
      this.$set(this.pageConfigPurchase[0].grid, 'asyncConfig', {
        url: this.$API.customerProfileContact.getPurchaseContactList,
        params: {
          partnerRelationId: this.supplyId
        }
      })
    },
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (e.toolbar.id == 'Add') {
        this.handleAddConfig()
        return
      } else if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Delete') {
        if (_selectRecords.filter((item) => item.isDefault === 1).length >= 1) {
          this.$toast({ content: this.$t('默认联系人不能删除！'), type: 'warning' })
          return
        }
        this.handleBatchDelete(_selectRecords)
      } else if (e.toolbar.id == 'Edit') {
        if (_selectRecords.length > 1) {
          this.$toast({ content: this.$t('只支持修改一行数据'), type: 'warning' })
        } else {
          //编辑操作
          this.handleEditConfig(_selectRecords[0])
        }
      }

      if (e.toolbar.id == 'modify' && _selectRecords.length === 1) {
        if (_selectRecords[0].isDefault === 1) {
          this.$toast({ content: this.$t('当前联系人已经是默认联系人！'), type: 'warning' })
          return
        }
        this.handleModify(_selectRecords[0])
      } else {
        this.$toast({ content: this.$t('只支持修改一行数据'), type: 'warning' })
      }
    },
    //单元格icons，点击
    handleClickCellTool(e) {
      if (e.tool.id == 'edit') {
        //编辑操作
        this.handleEditConfig(e.data)
      } else if (e.tool.id == 'delete') {
        if (e.data.isDefault === 1) {
          this.$toast({ content: this.$t('默认联系人不能删除！'), type: 'warning' })
          return
        }
        this.handleDeleteConfig([e.data.id])
      } else if (e.tool.id == 'modify') {
        if (e.data.isDefault === 1) {
          this.$toast({ content: this.$t('当前联系人已经是默认联系人！'), type: 'warning' })
          return
        }
        this.handleModify(e.data)
      }
    },

    // 设置默认
    handleModify(_selectRecords) {
      this.$API.customerProfileContact
        .setDefaultSupplyContact({
          id: _selectRecords.id,
          partnerRelationId: this.supplyId
        })
        .then((result) => {
          let { code } = result
          if (code === 200) {
            this.$toast({
              content: this.$t('设置默认成功'),
              type: 'success'
            })
          } else {
            this.$toast({
              content: this.$t('设置默认失败'),
              type: 'warning'
            })
          }
          this.$refs.templateSupplyRef.refreshCurrentGridData()
        })
    },

    //批量删除操作
    handleBatchDelete(_selectRecords) {
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //新增配置
    handleAddConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/lifecycledetailsupplier/contactInformation/contactDialog" */ './components/contactDialog.vue'
          ),
        data: {
          title: this.$t('新增联系人'),
          partnerRelationId: this.supplyId
        },
        success: () => {
          this.$refs.templateSupplyRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditConfig(data) {
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/lifecycledetailsupplier/contactInformation/contactDialog" */ './components/contactDialog.vue'
            ),
          data: {
            title: this.$t('编辑联系人'),
            partnerRelationId: this.supplyId,
            data
          },
          success: () => {
            this.$refs.templateSupplyRef.refreshCurrentGridData()
          }
        })
      }
    },
    //删除配置
    handleDeleteConfig(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.customerProfileContact.deleteSupplyContact({ ids: ids.join(',') }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateSupplyRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.contact-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .contact-item {
    flex: 1;
    &.purchase-contact {
      margin-top: 20px;
    }
    .contact-title {
      font-size: 16px;
      color: #292929;
      display: inline-block;
      padding-left: 13px;
      position: relative;
      margin-bottom: 20px;

      &:before {
        content: '';
        position: absolute;
        width: 3px;
        height: 14px;
        background: #6386c1;
        border-radius: 0 2px 2px 0;
        left: 0;
        top: 2px;
      }
    }
    .contact-grid {
      flex: 1;
    }
  }
}
</style>
