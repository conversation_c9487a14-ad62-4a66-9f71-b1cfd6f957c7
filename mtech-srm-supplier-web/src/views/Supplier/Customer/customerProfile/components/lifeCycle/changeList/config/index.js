import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  // {
  //   width: "150",
  //   field: "index",
  //   headerText: i18n.t("序号"),
  // },
  {
    width: '150',
    field: 'applyCode',
    headerText: i18n.t('申请单编码'),
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_solid_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['applyStatus'] == 10 || data['applyStatus'] == 30
        }
      },
      'delete'
    ]
  },
  {
    width: '150',
    field: 'applyName',
    headerText: i18n.t('申请单名称')
  },
  {
    width: '210',
    field: 'taskInstanceName',
    headerText: i18n.t('修改调查表')
  },
  {
    width: '130',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        10: i18n.t('待填写'),
        20: i18n.t('待审批'),
        30: i18n.t('已驳回'),
        40: i18n.t('已完成'),
        50: i18n.t('已关闭')
      }
    }
  },
  {
    width: '210',
    field: 'applyerName',
    headerText: i18n.t('申请人')
  },
  {
    width: '150',
    field: 'createDate',
    headerText: i18n.t('申请日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('原因说明')
  }
]
export const pageConfig = [
  {
    gridId: '95f321fb-e64b-4b7d-a2d1-f5ef0737ea13',
    toolbar: [
      'add',
      'delete',
      { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
      { id: 'Apply', icon: 'icon_solid_submit', title: i18n.t('提交') }
    ],
    grid: {
      columnData,
      dataSource: []
      // asyncConfig: {
      //   url,
      // },
    }
  }
]
