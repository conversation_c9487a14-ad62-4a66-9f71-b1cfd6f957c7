<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="contactName" :label="$t('姓名')">
          <mt-input
            v-model="formObject.contactName"
            float-label-type="Never"
            :placeholder="$t('请输入姓名')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="contactPost" :label="$t('职务')">
          <mt-input
            v-model="formObject.contactPost"
            float-label-type="Never"
            :placeholder="$t('请输入职务')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="contactPhone" :label="$t('手机号')">
          <mt-input
            v-model="formObject.contactPhone"
            float-label-type="Never"
            maxlength="11"
            :placeholder="$t('请输入手机号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="contactMail" :label="$t('邮箱')">
          <mt-input
            v-model="formObject.contactMail"
            float-label-type="Never"
            :placeholder="$t('请输入邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('备注')" class="full-width">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            maxlength="200"
            float-label-type="Never"
            v-model="formObject.remark"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        id: 0,
        // customerContactGender: "M", //性别
        // isDefault: 0, //是否默认
        contactMail: null, //客户联系邮箱
        contactName: null, //客户联系人姓名
        contactPhone: null, //客户联系电话
        contactPost: null, //	联系人职务
        partnerRelationId: null, //	供应商关系id
        remark: null //	备注
      },
      formRules: {
        contactName: [{ required: true, message: this.$t('请输入姓名'), trigger: 'blur' }],
        path: [
          {
            required: false,
            pattern: /^1[3456789]\d{9}$/,
            message: this.$t('手机号格式有误'),
            trigger: 'blur'
          }
        ],
        contactPhone: [
          {
            required: false,
            pattern: /^1[3456789]\d{9}$/,
            message: this.$t('手机号格式有误'),
            trigger: 'blur'
          }
        ],
        contactMail: [
          {
            required: false,
            pattern: /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/,
            message: this.$t('邮箱格式有误'),
            trigger: 'blur'
          }
        ]
      },
      genderList: [
        { text: this.$t('男'), value: 'M' },
        { text: this.$t('女'), value: 'F' }
      ],
      isDefaultList: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      let _data = { ...this.modalData.data }
      this.getFormDetail(_data.id)
    }
  },
  methods: {
    getFormDetail(id) {
      //联系人详情
      this.$API.customerProfileContact.supplyContactInfo({ id }).then((res) => {
        let _data = res.data
        this.formObject = {
          id: _data.id,
          // customerContactGender: _data.customerContactGender, //性别
          // isDefault: _data.isDefault, //是否默认
          contactMail: _data.contactMail, //客户联系邮箱
          contactName: _data.contactName, //客户联系人姓名
          contactPhone: _data.contactPhone, //客户联系电话
          contactPost: _data.contactPost, //	联系人职务
          // partnerRelationId: _data.partnerRelationId, //	供应商关系id
          remark: _data.remark //	备注
        }
      })
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formObject))
          params.partnerRelationId = this.modalData.partnerRelationId
          if (this.editStatus) {
            this.$API.customerProfileContact.editSupplyContact(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          } else {
            delete params.id
            this.$API.customerProfileContact.addSupplyContact(params).then((res) => {
              if (res.code == 200) {
                this.$emit('confirm-function')
              }
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .full-width {
    width: 100% !important;
  }
}
</style>
