import { i18n } from '@/main.js'
export const columnDataSupply = [
  {
    width: '50',
    type: 'checkbox'
  },
  // {
  //   width: "150",
  //   field: "index",
  //   headerText: i18n.t("序号"),
  // },
  {
    width: '150',
    field: 'contactName',
    headerText: i18n.t('姓名'),
    cssClass: '',
    cellTools: [
      'edit',
      'delete',
      { id: 'modify', icon: 'icon_solid_edit', title: i18n.t('设为默认') }
    ]
  },
  // {
  //   width: "150",
  //   field: "customerContactGender",
  //   headerText: i18n.t("性别"),
  // },
  {
    width: '210',
    field: 'contactPost',
    headerText: i18n.t('职务')
  },
  {
    width: '130',
    field: 'contactPhone',
    headerText: i18n.t('手机号')
  },
  {
    width: '210',
    field: 'contactMail',
    headerText: i18n.t('邮箱')
  },
  {
    width: '150',
    field: 'isDefault',
    headerText: i18n.t('默认'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const pageConfigSupply = [
  {
    gridId: '150300ac-fedd-49ee-8903-d7e6602b185a',
    toolbar: [
      'add',
      'delete',
      { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
      { id: 'modify', icon: 'icon_solid_edit', title: i18n.t('设为默认') }
    ],
    grid: {
      columnData: columnDataSupply,
      dataSource: []
      // asyncConfig: {
      //   url,
      // },
    }
  }
]
export const columnDataPurchase = [
  // {
  //   width: "150",
  //   field: "index",
  //   headerText: i18n.t("序号"),
  // },
  {
    width: '150',
    field: 'customerContactName',
    headerText: i18n.t('姓名')
    // cssClass: "",
    // cellTools: [],
  },
  // {
  //   width: "150",
  //   field: "customerContactGender",
  //   headerText: i18n.t("性别"),
  // },
  {
    width: '210',
    field: 'customerContactPost',
    headerText: i18n.t('职务')
  },
  {
    width: '130',
    field: 'customerContactPhone',
    headerText: i18n.t('手机号')
  },
  {
    width: '210',
    field: 'customerContactMail',
    headerText: i18n.t('邮箱')
  },
  {
    width: '150',
    field: 'isDefault',
    headerText: i18n.t('默认')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const pageConfigPurchase = [
  {
    gridId: 'd2628674-24c4-463f-9e64-9531edc51654',
    toolbar: [],
    grid: {
      columnData: columnDataPurchase,
      dataSource: []
      // asyncConfig: {
      //   url,
      // },
    }
  }
]
