<template>
  <div class="lifeCycle-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="header-logo">G</div>
      <!-- <div class="header-logo">{{ info.orgName | filterNo }}</div> -->
      <div class="header-content flex1 fbox">
        <div class="titles-box">
          <div class="mian-title" v-if="!!info.enterpriseName">
            {{ info.enterpriseName || '--' }}
          </div>
          <div class="sub-title fbox">
            <div class="normal-title" v-if="!!info.enterpriseCode">
              {{ $t('客户编码') }}：{{ info.enterpriseCode }}
            </div>
            <div class="normal-title" v-if="!!info.createTime">
              {{ $t('创建时间') }}：{{ info.createTime }}
            </div>
          </div>
        </div>
        <div class="btns-box fbox">
          <div class="invite-btn" @click="onBack">{{ $t('返回') }}</div>
        </div>
      </div>
    </div>
    <!-- 顶部信息 end -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      :selected-item="selectIndex"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>

    <!-- 调查表 -->
    <task-center
      v-if="selectIndex === 0"
      :id="id"
      :is-relation-tab="true"
      @handleCloseApplyDialog="handleApplyChange"
    ></task-center>
    <!-- 联系人信息 -->
    <contact-information v-if="selectIndex === 1" :id="id"></contact-information>
    <!-- 信息变更 -->
    <change-list v-if="selectIndex === 2" :id="id"></change-list>
    <!-- 档案历史 -->
    <info-history
      v-if="selectIndex === 3"
      :id="id"
      :form-task="formTaskDTO"
      ref="questionnaireHistory"
    ></info-history>
  </div>
</template>

<script>
export default {
  components: {
    //调查表
    taskCenter: () =>
      import(/* webpackChunkName: "router/lifecycledetailsupplier/taskCenter" */ './taskCenter'),
    //联系人信息
    contactInformation: () =>
      import(
        /* webpackChunkName: "router/lifecycledetailsupplier/contactInformation" */ './lifeCycle/contactInformation/index.vue'
      ),
    //信息变更列表
    changeList: () =>
      import(
        /* webpackChunkName: "router/lifecycledetailsupplier/changeList" */ './lifeCycle/changeList/index.vue'
      ),
    //档案历史
    infoHistory: () =>
      import(
        /* webpackChunkName: "router/lifecycledetailsupplier/infoHistory" */ './lifeCycle/infoHistory'
      )
  },
  filters: {
    filterNo: function (value) {
      if (!value) {
        return 'D'
      }
      return value.substr(0, 1)
    }
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('调查表')
        },
        {
          title: this.$t('联系人信息')
        },
        {
          title: this.$t('信息变更')
        },
        {
          title: this.$t('档案历史')
        }
        // {
        //   title: this.$t("绩效信息"),
        // },
      ],
      id: '',
      info: {}
    }
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    handleSelectTab(index) {
      this.selectIndex = index
    },
    // 获取任务详情
    getAccessDetail(id) {
      this.$API.customerProfileChange.getSupplierDetail({ id }).then((res) => {
        console.log('获取详情信息----：', res)
        this.info = res.data
      })
    },
    //在调查表页面，关闭‘变更申请’的弹框，切换至‘变更信息Tab’
    handleApplyChange() {
      this.selectIndex = 2
    }
  },
  created() {
    let id = this.$route.query.id
    if (!id) {
      this.$toast({
        content: '获取ID失败，请重试!',
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getAccessDetail(id)
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.lifeCycle-container {
  height: 100%;
  margin-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .header-logo {
      border-radius: 100%;
      width: 60px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 70, 156, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      font-size: 40px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      margin-right: 20px;
    }
    .header-content {
      justify-content: space-between;
      .titles-box {
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;

          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .sub-title {
          font-size: 12px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-top: 16px;
          .normal-title {
            font-size: 12px;

            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        max-width: 300px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        .invite-btn {
          cursor: pointer;
        }
        .invite-btn:nth-child(1) {
          margin-right: 30px;
        }
      }
    }
  }
}
</style>
