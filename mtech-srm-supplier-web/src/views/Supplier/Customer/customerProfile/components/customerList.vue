<template>
  <div class="supply-area">
    <mt-template-page
      :hidden-tabs="true"
      :padding-top="true"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnDataMain, invitationToolbar } from '../config/index'

export default {
  data() {
    return {
      componentConfig: [
        {
          gridId: '24e9d716-f429-499a-9368-2c9dd81a6d8d',
          useToolTemplate: false,
          title: this.$t('客户档案'),
          toolbar: invitationToolbar,
          grid: {
            columnData: columnDataMain,
            asyncConfig: {
              url: '/supplier/tenant/supplier/process/access/query'
            }
          }
        }
      ]
    }
  },

  methods: {
    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      console.log(e, e.gridRef.getMtechGridRecords())
    },

    // cell tool
    handleClickCellTool(e) {
      console.log(e)
    },

    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'lifecycledetailsupplier',
        query: {
          id: data.id,
          customerEnterpriseId: data.customerEnterpriseId,
          customerEnterpriseName: data.customerEnterpriseName
        }
      })
    }
  },
  created() {},
  mounted() {}
}
</script>

<style lang="scss" scoped></style>
<style lang="scss">
.supply-area {
  height: calc(100% - 70px);
}
</style>
