<template>
  <div class="lifeCycle-container">
    <!-- 顶部信息 end -->
    <!-- <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      :selectedItem="selectIndex"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs> -->

    <div class="tab-box">
      <ul class="tab-container fbox">
        <li class="tab-item2" :class="{ active: selectIndex === 0 }" @click="handleSelectTab(0)">
          {{ $t('准入进展') }}
        </li>
        <li class="tab-item2" :class="{ active: selectIndex === 1 }" @click="handleSelectTab(1)">
          {{ $t('任务中心') }}
        </li>
        <li class="tab-item2" :class="{ active: selectIndex === 2 }" @click="handleSelectTab(2)">
          {{ $t('客户档案') }}
        </li>
      </ul>
    </div>

    <keep-alive v-if="selectIndex !== -1">
      <task-center
        :company-info="companyInfo"
        @reload="reloadTc"
        v-if="selectIndex === 1"
        :id="id"
      ></task-center>
      <access-process
        @jumpTaskCenter="jumpTaskCenter"
        v-if="selectIndex === 0"
        :id="id"
      ></access-process>
      <customer-list
        @jumpTaskCenter="jumpTaskCenter"
        v-if="selectIndex === 2"
        :id="id"
      ></customer-list>
    </keep-alive>
  </div>
</template>

<script>
import taskCenter from './components/taskCenter.vue'
import accessProcess from './components/accessProcess.vue'
import customerList from './components/customerList.vue'
export default {
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$hloading()
    })
  },
  components: {
    taskCenter,
    accessProcess,
    customerList
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('准入进展')
        },
        {
          title: this.$t('任务中心')
        },
        {
          title: this.$t('客户档案')
        }
      ],
      id: '',
      companyInfo: {}
    }
  },
  methods: {
    // 返回

    handleSelectTab(index) {
      this.selectIndex = index
    },

    // 尝试刷新
    reloadTc() {
      this.selectIndex = -1
      this.$nextTick(() => {
        this.selectIndex = 1
      })
    },

    // 跳转到任务中心
    jumpTaskCenter(data) {
      console.log('7777', data)
      this.selectIndex = 1
      this.companyInfo = data
    }
  },
  created() {
    this.$hloading()
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.tab-box {
  box-sizing: border-box;
  height: 50px;
  border: none;
  border-radius: 4px 4px 0 0;
  background: #fafafa;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;

  .tab-container {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    border: 0;
  }

  .tab-item2 {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    white-space: nowrap;
    position: relative;
    font-size: 14px;
    font-weight: 400;
    border: none;
    margin: 0 10px;
    padding: 8px 25px;
    height: 48px;
    color: #9a9a9a;
    cursor: pointer;
    box-sizing: border-box;
    border-bottom: 3px solid transparent;
  }
  .active {
    border-bottom: 2px solid #6386c1;
    font-weight: 600;
    color: #00469c;
  }
}

.lifeCycle-container {
  height: 100%;
  margin-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .header-logo {
      border-radius: 100%;
      width: 60px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 70, 156, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      font-size: 40px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      margin-right: 20px;
    }
    .header-content {
      justify-content: space-between;
      .titles-box {
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;

          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .sub-title {
          font-size: 12px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          margin-top: 16px;
          .normal-title {
            font-size: 12px;

            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        max-width: 300px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        .invite-btn {
          cursor: pointer;
        }
        .invite-btn:nth-child(1) {
          margin-right: 30px;
        }
      }
    }
  }
}
</style>
