<template>
  <div class="customer-access-wrapper">
    <mt-template-page
      :padding-top="true"
      :use-tool-template="false"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  name: 'AccessManage',
  data() {
    return {
      componentConfig: [
        {
          gridId: '2dd3d40a-81df-486a-9b7c-e3b4edd54b2c',
          useToolTemplate: false,
          toolbar: [],
          grid: {
            columnData: [
              //列数据配置，具体可参考mt-data-grid组件
              {
                width: '50',
                type: 'checkbox'
              },
              {
                field: 'customerCode',
                headerText: this.$t('采方企业编码'),
                width: '150',
                cellTools: [],
                valueConverter: {
                  type: 'function',
                  filter: (e) => {
                    return e || '—'
                  }
                }
              },
              {
                field: 'customerName',
                width: '150',
                headerText: this.$t('采方企业名称')
              },
              {
                field: 'customerSubName',
                width: '150',
                headerText: this.$t('采方公司名称')
              },
              {
                field: 'f5',
                width: '150',
                headerText: this.$t('任务提交进度'),
                template: () => {
                  return {
                    template: Vue.component('range', {
                      template: `
                        <div class="range-box mt-flex">
                            <div class="rang-wrap">
                              <div class="rang-inner" :style="'width: ' + data.submittedCount/data.totalCount * 100 + '%;'"></div>
                            </div>
                            <div class="rang-txt">{{data.submittedCount}}/{{data.totalCount}}</div>
                        </div>`,
                      data: function () {
                        return { data: {} }
                      }
                    })
                  }
                }
              },
              {
                field: 'f6',
                width: '150',
                headerText: this.$t('任务审核进度'),
                template: () => {
                  return {
                    template: Vue.component('range', {
                      template: `
                        <div class="range-box mt-flex">
                            <div class="rang-wrap">
                              <div class="rang-inner" :style="'width: ' + data.successCount/data.totalCount * 100 + '%;'"></div>
                            </div>
                            <div class="rang-txt">{{data.successCount}}/{{data.totalCount}}</div>
                        </div>`,
                      data: function () {
                        return { data: {} }
                      }
                    })
                  }
                }
              },
              {
                field: 'createTime',
                width: '150',
                headerText: this.$t('创建时间')
              }
            ],
            asyncConfig: {
              url: '/supplier/tenant/supplier/access/query'
            }
          }
        }
      ]
    }
  },

  methods: {
    handleClickToolBar(e) {
      // 表格顶部 toolbar
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(e.toolbar.id == 'Filter' || e.toolbar.id == 'Refresh' || e.toolbar.id == 'Setting')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },

    handleClickCellTitle(e) {
      let { data } = e
      //单元格Title点击
      this.$router.push({
        name: 'customer-access-info',
        params: {
          id: data.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.customer-access-wrapper {
  height: 100%;
  /deep/.range-box {
    padding: 0 40px 0 10px;

    .rang-wrap {
      width: 230px;
      height: 20px;
      background: rgba(232, 232, 232, 0.8);
      border-radius: 2px 100px 100px 2px;
      display: flex;
      justify-content: flex-start;
      .rang-inner {
        display: inline-block;
        width: 70%;
        height: 20px;
        background: rgba(99, 134, 193, 0.5);
        border-radius: 2px 100px 100px 2px;
      }
    }
    .rang-txt {
      width: 30px;
      font-size: 12px;

      font-weight: normal;
      color: rgba(176, 176, 176, 1);
      margin-left: 6px;
    }
  }
}
</style>
