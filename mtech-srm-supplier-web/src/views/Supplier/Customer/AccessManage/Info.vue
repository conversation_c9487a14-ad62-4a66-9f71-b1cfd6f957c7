<template>
  <div class="access-detail">
    <mt-button class="return-btn" @click="onBack">{{ $t('返回') }}</mt-button>
    <mt-card class="card" :title="$t('采方信息')">
      <div class="company-info-box">
        <div class="info-item">
          <span class="label">{{ $t('准入任务批次编号') }}：</span>
          <span class="info"></span>
        </div>
        <div class="info-item">
          <span class="label">{{ $t('采方企业全称') }}：</span>
          <span class="info">{{ info.customerName }}</span>
        </div>
      </div>
    </mt-card>
    <div class="relation-ships">
      <div class="tab-box mt-flex">
        <div class="tab-item" :class="{ active: tabId === 0 }" @click="selectTab(0)">
          {{ $t('待处理任务') }}
        </div>
        <div class="tab-item" :class="{ active: tabId === 1 }" @click="selectTab(1)">
          {{ $t('已处理任务') }}
        </div>
      </div>
      <!-- tab content 待处理任务-->
      <div class="tab-content" v-show="tabId === 0">
        <div class="content-steps mt-flex">
          <div class="left-tabs">
            <div
              v-for="(item, index) in pendingTasks"
              :key="item.id"
              :class="{
                'tab-items': true,
                'mt-flex': true,
                active: subTabIndex === index
              }"
              @click="onSubTabChange(index, item)"
            >
              <div class="tb-mian">{{ item.taskName }}</div>
              <div class="tb-status">{{ statusMap[item.status] }}</div>
            </div>
          </div>
          <div class="right-contents flex1">
            <!-- 待处理任务表单 -->
            <div
              v-for="(item, index) in pendingTasks"
              :key="item.id"
              v-show="subTabIndex === index"
            >
              <div class="right-btn">
                <div class="top-status">
                  <span class="status-title">{{ $t('状态') }}：</span>
                  <span class="status-item">{{ statusMap[item.status] }}</span>
                </div>
                <div class="op-item mt-flex add-new" @click="onSave(index)">
                  <i class="mt-icons icon_solid_Save"></i>
                  {{ $t('保存') }}
                </div>
                <div class="op-item mt-flex" @click="onSubmit(index)">
                  <i class="mt-icons mt-icon-icon_Share_2"></i>
                  {{ $t('提交') }}
                </div>
              </div>
              <div v-for="(form, idx) in item.supplierTaskDataDTOList" :key="idx">
                <div class="normal-title" style="padding: 20px">
                  {{ form.supplierTaskDefineDTO.formDefineName }}
                </div>
                <!-- form表单的框 -->
                <div class="form-content mt-flex">
                  <mt-parser
                    :ref="`parser_${index}_${idx}`"
                    :form-conf="form.supplierTaskDefineDTO.formDefineData"
                    @submit="sumbitForm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- tab content 已处理任务-->
      <div class="tab-content" v-show="tabId === 1">
        <div class="content-steps mt-flex">
          <div class="left-tabs">
            <div
              v-for="(item, index) in handledTasks"
              :key="item.id"
              :class="{
                'tab-items': true,
                'mt-flex': true,
                active: handledSubTabIndex === index
              }"
              @click="onHandledSubTabChange(index)"
            >
              <div class="tb-mian">{{ item.taskName }}</div>
              <div class="tb-status">{{ statusMap[item.status] }}</div>
            </div>
          </div>
          <div class="right-contents flex1">
            <!-- 已处理任务详情 -->
            <div
              v-for="(item, index) in handledTasks"
              :key="item.id"
              v-show="handledSubTabIndex === index"
            >
              <div v-for="(form, idx) in item.supplierTaskDataDTOList" :key="idx">
                <div class="normal-title" style="padding: 20px">
                  {{ form.supplierTaskDefineDTO.formDefineName }}
                </div>
                <info-parser
                  :form-conf="form.supplierTaskDefineDTO.formDefineData"
                  :value-data="form.supplierTaskDataDTO.formData"
                  label-class="parser-label"
                  value-class="parser-value"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Parser from '@mtech-form-design/form-parser'
import InfoParser from '@mtech-form-design/information-parser'
import Card from '@/components/Cards'
export default {
  name: 'CustomerAccessInfo',
  data() {
    return {
      pendingTasks: [], // 待处理任务列表
      handledTasks: [], // 已处理任务列表
      tabId: 0, // 0：待处理任务 1：已处理任务
      subTabIndex: 0, // 待处理任务tab index
      handledSubTabIndex: 0, // 已处理任务tab index
      info: {
        customerName: '',
        customerCode: ''
      },
      statusMap: {
        0: this.$t('待提交'),
        1: this.$t('待填写'),
        2: this.$t('待审批'),
        8: this.$t('已驳回'),
        9: this.$t('已完成'),
        10: this.$t('已关闭')
      }
    }
  },
  components: {
    'mt-card': Card,
    'mt-parser': Parser,
    InfoParser
  },
  created() {
    if (this.$route.params.id) this.getData(this.$route.params.id)
  },
  methods: {
    // 获取准入信息
    getData(id) {
      this.$API.customerAccess.getAccessInfo({ id }).then((res) => {
        Object.assign(this.info, res.data)
        this.getStageTaskTabs(res.data.id)
      })
    },
    onTabSwitch() {
      if (
        (this.tabId === 0 && this.pendingTasks.length) ||
        (this.tabId === 1 && this.handledTasks.length)
      )
        this.$API.customerAccess
          .getStageTaskInfo({
            id:
              this.tabId === 0
                ? this.pendingTasks[this.subTabIndex].id
                : this.handledTasks[this.handledSubTabIndex].id
          })
          .then((res) => {
            res.data.supplierTaskFormDTOList.forEach((element) => {
              if (element.supplierTaskDataDTO.formData)
                this.fillFormData(
                  element.supplierTaskDefineDTO.formDefineData,
                  element.supplierTaskDataDTO.formData
                )
            })
            if (this.tabId === 0) {
              this.pendingTasks[this.subTabIndex].supplierTaskDataDTOList =
                res.data.supplierTaskFormDTOList
            } else {
              this.handledTasks[this.handledSubTabIndex].supplierTaskDataDTOList =
                res.data.supplierTaskFormDTOList
            }
            // 回显数据 // latest version
            // this.$nextTick(() => {
            //   res.data.supplierTaskFormDTOList.forEach((element, i) => {
            //     this.$refs[`parser_${this.subTabIndex}_${i}`][0].setFormData(element.supplierTaskDataDTO.formData)
            //   });
            // });
          })
    },
    // 获取阶段任务tab列表
    getStageTaskTabs(id) {
      this.$API.customerAccess
        .getStageTaskList({ stageViewId: id, taskTypeFlag: 1 })
        .then((res) => {
          res.data.map((item) => (item.supplierTaskDataDTOList = []))
          this.pendingTasks = res.data
          if (res.data.length) this.onTabSwitch()
        })
      this.$API.customerAccess
        .getStageTaskList({ stageViewId: id, taskTypeFlag: 2 })
        .then((res) => {
          res.data.map((item) => (item.supplierTaskDataDTOList = []))
          this.handledTasks = res.data
        })
    },
    onBack() {
      this.$router.go(-1)
    },
    selectTab(tabId) {
      this.tabId = tabId
      this.onTabSwitch()
    },
    onSubTabChange(index) {
      this.subTabIndex = index
      this.onTabSwitch()
    },
    onHandledSubTabChange(index) {
      this.handledSubTabIndex = index
      this.onTabSwitch()
    },
    sumbitForm(e) {
      console.log(e)
    },
    // 保存
    async onSave(index) {
      await this.validator(index)
      const data = {
        supplierTaskDataDTOList: this.pendingTasks[index].supplierTaskDataDTOList.map((item) => ({
          formData: item.supplierTaskDataDTO.formData,
          formDataId: item.supplierTaskDataDTO.formDataId
        })),
        taskId: this.pendingTasks[index].id
      }
      this.$API.customerAccess.saveStageTask(data).then(() => {
        location.reload()
      })
    },
    // 提交
    async onSubmit(index) {
      await this.validator(index)
      const data = {
        supplierTaskDataDTOList: this.pendingTasks[index].supplierTaskDataDTOList.map((item) => ({
          formData: item.supplierTaskDataDTO.formData,
          formDataId: item.supplierTaskDataDTO.formDataId
        })),
        taskId: this.pendingTasks[index].id
      }
      this.$API.customerAccess.submitStageTask(data).then(() => {
        location.reload()
      })
    },
    // 验证表单
    async validator(index) {
      for (let i = 0; i < this.pendingTasks[index].supplierTaskDataDTOList.length; i++) {
        const element = this.pendingTasks[index].supplierTaskDataDTOList[i]
        await this.$refs[`parser_${index}_${i}`][0].valiteFormData()
        element.supplierTaskDataDTO.formData = this.$refs[`parser_${index}_${i}`][0].getFormData()
      }
    },
    // 如果表单有默认值，调用该方法初始化默认值
    fillFormData(form, data) {
      form.fields.forEach((item) => {
        const val = data[item.__vModel__]
        if (val) {
          // item.__config__.defaultValue = val
          this.$set(item.__config__, 'defaultValue', val)
          console.log('item.__config__.defaultValue', item.__config__.defaultValue)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.access-detail {
  .return-btn {
    margin-bottom: 20px;
  }
  .card {
    margin-bottom: 20px;
  }
  .company-info-box {
    .info-item {
      display: inline-flex;
      width: 33.3%;
      font-size: 14px;
      color: #35404e;
      margin-bottom: 25px;
      line-height: 18px;
      .label {
        display: inline-block;
        width: 140px;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .info {
        display: inline-block;
        text-align: left;
        max-width: 360px;
      }
    }
  }
}
.normal-title {
  width: 100%;
  height: 14px;
  line-height: 14px;
  font-size: 14px;
  color: #292929;

  font-weight: 500;

  &:before {
    content: ' ';
    display: inline-block;
    vertical-align: baseline;
    width: 2px;
    height: 10px;
    background: rgba(0, 70, 156, 1);
    border-radius: 1px;
    margin-right: 10px;
  }
}
.relation-ships {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px;
  margin-top: 20px;

  .tab-box {
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #e8e8e8;
    position: relative;

    .tab-item {
      font-size: 14px;
      height: 40px;
      line-height: 40px;

      font-weight: normal;
      color: rgba(40, 41, 41, 1);
      padding: 0 38px;
      cursor: pointer;
    }
    .active {
      font-size: 16px;

      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      border-bottom: 4px solid #00469c;
    }
  }
  .tab-content {
    .content-steps {
      width: 100%;

      .left-tabs {
        width: 240px;
        height: 100%;

        padding-top: 24px;

        .tab-items {
          width: 239px;
          height: 50px;
          line-height: 50px;
          background: #fff;
          padding: 0 30px;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;
          .tb-mian {
            font-size: 14px;

            font-weight: normal;
            color: rgba(41, 41, 41, 1);
          }
          .tb-status {
            padding: 0 4px;
            font-size: 12px;
            height: 20px;
            line-height: 20px;
            background: rgba(244, 244, 244, 1);
            border-radius: 2px;
          }
        }
        .active {
          background: rgba(245, 246, 249, 1);
          .tb-mian {
            color: #00469c;
          }
        }
      }

      .right-contents {
        border-left: 1px solid rgba(232, 232, 232, 1);
        .right-btn {
          height: 40px;
          position: absolute;
          right: 0;
          top: -40px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          transition: all 0.6s ease-in;

          .top-status {
            font-size: 14px;
            color: #35404e;
            height: 20px;
            vertical-align: middle;
            margin-right: 60px;

            .status-item {
              display: inline-block;
              vertical-align: middle;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              padding: 0 6px;
              background: #e8eaea;
              color: #778080;
              border-radius: 11px;
              margin-left: 6px;
            }
          }
          i {
            margin-right: 4px;
            color: #6386c1;
          }
          .op-item {
            color: #6386c1;
            align-items: center;
            margin-right: 20px;
            align-items: center;
            cursor: pointer;
          }
          .add-new {
            i {
              color: #6386c1;
            }
            color: #6386c1;
          }
        }
        .form-content {
          padding: 20px 0 20px 20px;
        }
      }
    }
  }
}
</style>
