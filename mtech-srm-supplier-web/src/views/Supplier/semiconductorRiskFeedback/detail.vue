<template>
  <div class="full-height">
    <!-- 顶部信息 -->
    <div class="top-content">
      <div class="left-title">
        {{ baseInfo.code }}
        <span>{{ baseInfo.companyCode + '-' + baseInfo.companyName }}</span>
      </div>
      <div class="right-btns">
        <vxe-button status="primary" size="small" @click="handleBack">{{ $t('返回') }} </vxe-button>
      </div>
    </div>
    <!-- 自定义查询条件 -->
    <!-- <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="orderNo" :label="$t('单据号')" label-style="top">
          <mt-input
            v-model="searchFormModel.orderNo"
            :show-clear-button="true"
            :placeholder="$t('请输入单据号')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusList"
            :show-clear-button="true"
            :show-select-all="true"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('外部物料组')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            records-position="data"
            url="/masterDataManagement/tenant/item/fuzzy-query"
            :fields="{ value: 'itemCode', text: 'itemName' }"
            params-key="fuzzyNameOrCode"
            :title-switch="false"
            :placeholder="$t('请选择外部物料组')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商名称')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierCode', 'supplierName']"
            :placeholder="$t('请选择供应商')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search> -->
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="72f345c1-a97f-4512-a849-3c0437236de6"
      keep-source
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
// import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import mixin from './config/mixin'

export default {
  components: { ScTable },
  mixins: [mixin],
  data() {
    return {
      type: 'detail',
      tableData: [],
      loading: false,
      baseInfo: {}
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  mounted() {
    this.getTableData()
    this.baseInfo = JSON.parse(sessionStorage.getItem('supRowInfo'))
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = { headerId: this.$route.query.id }
      this.loading = true
      const res = await this.$API.semiconductorRiskManagement
        .querySemiRiskDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
      }
    },

    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (e.code === 'submit' && !selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'save':
          this.hanldeSave()
          break
        case 'submit':
          this.hanldeSubmit(selectedRecords)
          break
        default:
          break
      }
    },
    // 保存
    async hanldeSave() {
      const list = this.tableRef.getUpdateRecords()
      const dataList = []
      list.forEach((item) => {
        const { id, vendorInventory, deliveryOrder, returnPlan } = item
        dataList.push({
          itemId: id,
          vendorInventory,
          deliveryOrder,
          returnPlan
        })
      })
      const res = await this.$API.semiconductorRiskManagement.saveSemiRiskDetailList(dataList)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.getTableData()
      }
    },
    // 提交
    async hanldeSubmit(list) {
      const idList = []
      list.forEach((item) => idList.push(item.id))
      const res = await this.$API.semiconductorRiskManagement.submitSemiRiskDetailList({
        itemIdList: idList.join(',')
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t('提交成功'), type: 'success' })
        this.getTableData()
      }
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
  .top-content {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .left-title {
      font-size: 20px;
      font-weight: 700;
      color: #292929;
      span {
        font-size: 15px;
        color: #777;
      }
    }
  }
}
</style>
