<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="code" :label="$t('档案编号')" label-style="top">
          <mt-input
            v-model="searchFormModel.code"
            :show-clear-button="true"
            :placeholder="$t('请输入档案编号')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <!-- 暂时只能选择1503 -->
          <mt-select
            v-model="searchFormModel.companyCode"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'companyCode' }"
            :show-clear-button="true"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="adae553d-8f89-4cd0-9138-a713f99d37ca"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    />
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'

export default {
  components: { ScTable, CollapseSearch },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      type: 'list',
      tableData: [],
      loading: false
    }
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.semiconductorRiskManagement
        .querySupSemiRiskList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data.records || []
        this.total = res.data?.total || 0
      }
    },
    // 点击单元格标题
    handleClickCellTitle(row, column) {
      if (column.field === 'code') {
        this.handleViewDetail(row)
      }
    },
    // 查看详情
    handleViewDetail(row) {
      sessionStorage.setItem('supRowInfo', JSON.stringify(row))
      this.$router.push({
        name: 'semiconductor-risk-feedback-detail',
        query: {
          id: row.id,
          refreshId: Date.now()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
