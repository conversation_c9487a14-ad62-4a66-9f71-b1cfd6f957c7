import dayjs from 'dayjs'
import { i18n } from '@/main'

export default {
  data() {
    return {
      companyList: [],
      statusList: [
        { value: 4, text: i18n.t('待填写') },
        { value: 3, text: i18n.t('已完成') }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      switch (this.type) {
        case 'detail':
          return [
            { code: 'save', name: this.$t('保存'), status: 'info' },
            { code: 'submit', name: this.$t('提交'), status: 'info' }
          ]
        default:
          return []
      }
    },
    columns() {
      switch (this.type) {
        case 'list':
          return [
            {
              type: 'seq',
              title: this.$t('序号'),
              width: 50
            },
            {
              field: 'code',
              title: this.$t('档案编号'),
              slots: {
                default: ({ row, column }) => {
                  return [
                    <a
                      style='color: #409eff;'
                      on-click={() => this.handleClickCellTitle(row, column)}>
                      {row.code}
                    </a>
                  ]
                }
              }
            },
            {
              field: 'companyCode',
              title: this.$t('所属公司'),
              minWidth: 250,
              slots: {
                default: ({ row }) => {
                  return [<span>{row.companyCode + '-' + row.companyName}</span>]
                }
              }
            },
            {
              field: 'createTime',
              title: this.$t('创建时间'),
              slots: {
                default: ({ row }) => {
                  const t = row.createTime
                    ? dayjs(Number(row.createTime)).format('YYYY-MM-DD HH:mm:ss')
                    : 0
                  return [<span>{t}</span>]
                }
              }
            }
          ]
        case 'detail':
          return [
            {
              type: 'checkbox',
              width: 50
            },
            {
              field: 'status',
              title: this.$t('状态'),
              slots: {
                default: ({ row }) => {
                  const selectItem = this.statusList.find((item) => item.value === row.status)
                  const statusName = selectItem?.text
                  return [<div>{statusName}</div>]
                }
              }
            },
            {
              field: 'materialCode',
              title: this.$t('TCL物料编码')
            },
            {
              field: 'categoryCode',
              title: this.$t('外部物料组'),
              minWidth: 250,
              slots: {
                default: ({ row }) => {
                  return [<span>{row.categoryCode + '-' + row.categoryName}</span>]
                }
              }
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商'),
              minWidth: 250,
              slots: {
                default: ({ row }) => {
                  return [<span>{row.supplierCode + '-' + row.supplierName}</span>]
                }
              }
            },
            {
              field: 'sourceFactoryName',
              title: this.$t('原厂简称')
            },
            {
              field: 'supplierShortName',
              title: this.$t('供应商简称')
            },
            {
              field: 'leadTime1',
              title: this.$t('L/T')
            },
            {
              field: 'leadTime2',
              title: this.$t('无条件L/T')
            },
            {
              field: 'factory1',
              title: this.$t('晶圆厂')
            },
            {
              field: 'factory2',
              title: this.$t('封装厂')
            },
            {
              field: 'factory3',
              title: this.$t('测试厂')
            },
            {
              field: 'country1',
              title: this.$t('圆晶厂国家/地区'),
              minWidth: 150
            },
            {
              field: 'country2',
              title: this.$t('封装厂国家/地区'),
              minWidth: 150
            },
            {
              field: 'country3',
              title: this.$t('测试厂国家/地区'),
              minWidth: 150
            },
            {
              field: 'province1',
              title: this.$t('晶圆厂省份')
            },
            {
              field: 'province2',
              title: this.$t('封装厂省份')
            },
            {
              field: 'province3',
              title: this.$t('测试厂省份')
            },
            {
              field: 'area1',
              title: this.$t('晶圆厂地区')
            },
            {
              field: 'area2',
              title: this.$t('封装厂地区')
            },
            {
              field: 'area3',
              title: this.$t('测试厂地区')
            },
            {
              field: 'size',
              title: this.$t('wafer尺寸')
            },
            {
              field: 'process',
              title: this.$t('纳米制程')
            },
            {
              field: 'logisticsCenter',
              title: this.$t('物流集散地')
            },
            {
              field: 'demand',
              title: this.$t('需求')
            },
            {
              field: 'ownInventory',
              title: this.$t('自有库存')
            },
            {
              field: 'outstandingOrder',
              title: this.$t('未交PO')
            },
            {
              field: 'vendorInventory',
              title: this.$t('供应商库存'),
              minWidth: 160,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.vendorInventory}
                      type='integer'
                      clearable
                      disabled={row.status === 3}
                    />
                  ]
                }
              }
            },
            {
              field: 'deliveryOrder',
              title: this.$t('供应商在途订单'),
              minWidth: 160,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.deliveryOrder}
                      type='integer'
                      clearable
                      disabled={row.status === 3}
                    />
                  ]
                }
              }
            },
            {
              field: 'returnPlan',
              title: this.$t('N+2回货计划'),
              minWidth: 160,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.returnPlan}
                      type='integer'
                      clearable
                      disabled={row.status === 3}
                    />
                  ]
                }
              }
            },
            {
              field: 'replaceResource',
              title: this.$t('PTP替代资源')
            },
            {
              field: 'unreplaceResource',
              title: this.$t('非PTP功能替代资源'),
              minWidth: 160
            },
            {
              field: 'channel',
              title: this.$t('调货渠道')
            },
            {
              field: 'remark',
              title: this.$t('备注')
            }
          ]
        default:
          return []
      }
    }
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.getCompanyList()
      if (res.code === 200) {
        // 限制只显示1503公司
        const t = res.data.find((item) => item.companyCode === '1503')
        t.text = t.companyCode + '-' + t.companyName
        this.companyList = [t]
      }
    }
  }
}
