import { i18n } from '@/main'
import utils from '@/utils/utils.js'
import dayjs from 'dayjs'
import Vue from 'vue'
const _L = (_str) => i18n.t(_str)
export let reviewTemplateData = [] // 全局存储变量
export const pageConfig = [
  {
    gridId: 'b4bb6559-0dc2-4c8f-8358-640f30861dd4',
    useToolTemplate: false,
    toolbar: [
      // { id: 'submit', icon: 'icon_solid_Createorder', title: i18n.t('提交') }
    ],
    grid: {
      columnData: [
        { type: 'checkbox', width: 50 },
        {
          field: 'code',
          headerText: _L('项目编码'),
          width: 260,
          cellTools: []
        },
        { field: 'totalScore', headerText: i18n.t('总分') },
        { field: 'name', headerText: i18n.t('评审任务名称') },
        { field: 'buyerEnterpriseCode', headerText: i18n.t('客户编码'), width: 260 },
        { field: 'buyerEnterpriseName', headerText: i18n.t('客户名称') },
        { field: 'categoryName', headerText: i18n.t('品类') },
        {
          field: 'planTimeBegin',
          headerText: _L('计划时间'),
          template: () => {
            return {
              template: Vue.component('planTimeBegin', {
                template: `<div>
                  <span>{{time}}</span>
                </div>`,
                data: function () {
                  return {
                    utils
                  }
                },
                computed: {
                  time() {
                    if (!this.data.planTimeBegin) return ''
                    return `${this.utils.formateTime(
                      Number(this.data.planTimeBegin)
                    )}-${this.utils.formateTime(Number(this.data.planTimeEnd))}`
                  }
                }
              })
            }
          }
        },
        {
          field: 'status',
          headerText: _L('状态'),
          valueConverter: {
            type: 'map',
            map: {
              10: _L('草稿'),
              20: _L('申请待审批'),
              30: _L('申请已驳回'),
              40: _L('申请已批准'),
              50: _L('结果待提交'),
              60: _L('结果待审批'),
              70: _L('结果已驳回'),
              80: _L('结果已批准'),
              90: _L('待反馈'),
              100: _L('已反馈')
            }
          }
        },
        { field: 'createTime', headerText: _L('创建时间') }
      ],
      asyncConfig: {
        url: '/supplier/tenant/supplier/review/task/pageList'
      }
    }
  }
]

export const detailPageConfig = (data) => [
  {
    gridId: '969513ac-d4e0-4d3f-94de-9d5904b00b11',
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'score',
            icon: 'icon_solid_Createorder',
            title: i18n.t('批量打分'),
            visibleCondition: () => {
              return data.status == 10 || data.status == 90
            }
          },
          {
            id: 'save',
            icon: 'icon_solid_Createorder',
            title: i18n.t('保存'),
            visibleCondition: () => {
              return data.status == 10 || data.status == 90
            }
          },
          {
            id: 'saveSubmit',
            icon: 'icon_solid_Createorder',
            title: i18n.t('保存并提交'),
            visibleCondition: () => {
              return data.status == 10 || data.status == 90
            }
          },
          { id: 'back', icon: 'icon_solid_Createorder', title: i18n.t('返回') },
          {
            id: 'export1',
            icon: 'icon_solid_Createorder ',
            title: i18n.t('导出'),
            visibleCondition: () => {
              return data.status == 10 || data.status == 90
            }
          },
          {
            id: 'import',
            icon: 'icon_solid_Createorder ',
            title: i18n.t('导入'),
            visibleCondition: () => {
              return data.status == 10 || data.status == 90
            }
          }
        ],
        []
      ]
    },
    grid: {
      columnData: [
        { type: 'checkbox', width: 50 },
        {
          field: 'itemName',
          headerText: _L('评审项'),
          width: 360,
          allowEditing: false
        },
        { field: 'scoreBegin', headerText: i18n.t('最低分'), width: 100, allowEditing: false },
        { field: 'scoreEnd', headerText: i18n.t('最高分'), width: 100, allowEditing: false },
        {
          field: 'selfScore',
          headerText: i18n.t('得分'),
          width: 100,
          template: function () {
            return {
              template: Vue.component('selfScore', {
                template: `<div>
                <mt-inputNumber :disabled="enable" ref="inputNumber" :min="data.scoreBegin" :max="data.scoreEnd" width="120" height="26"
                 @change="handleInputNum($event,'old')"  v-model="data.selfScore"></mt-inputNumber>
                </div>
             `,
                data() {
                  return {
                    data: {},
                    i18n: i18n
                    // oldSelfScore:null
                  }
                },
                mounted() {
                  // this.oldSelfScore =  this.data.selfScore
                },
                methods: {
                  handleInputNum(e, old) {
                    console.log(e, old)
                    if (e < this.data.scoreBegin && e !== null && this.data.scoreBegin !== null) {
                      e = this.data.scoreBegin
                    }
                    if (e > this.data.scoreEnd && e !== null && this.data.scoreEnd !== null) {
                      e = this.data.scoreEnd
                    }
                    if (!reviewTemplateData.length) return
                    if (Object.hasOwnProperty.call(this.data, 'parentItem')) {
                      reviewTemplateData
                        .find((e) => e.itemCode === this.data.parentItem.itemCode)
                        .itemResponses.find((x) => x.itemCode === this.data.itemCode).selfScore =
                        e != null ? Number(e) : e
                    } else {
                      reviewTemplateData.find((e) => e.itemCode === this.data.itemCode).selfScore =
                        e != null ? Number(e) : e
                    }
                    // console.log("=reviewTemplateData=",e, old);
                    this.$parent.$emit('changeSelfScorenumber')
                    // let newValue = e
                    // let oldValue = null
                    // if(old == "old"){
                    //   oldValue = this.data.selfScore
                    // }else{
                    //   oldValue = old
                    // }
                    // if(e == null || e == undefined)  newValue = 0
                    // if(oldValue == null || oldValue == undefined)  oldValue = 0
                    // console.log(newValue,oldValue);
                    // if(newValue > oldValue){
                    //     let num = newValue - oldValue
                    //    console.log("+++",num);
                    // }else if(newValue < oldValue){
                    //   let num = newValue - oldValue
                    //   console.log("---",num);

                    // }
                  }
                },
                watch: {
                  'data.selfScore': function (n, o) {
                    if (n === null) {
                      this.handleInputNum(null, o)
                    }
                  }
                },
                computed: {
                  enable() {
                    return this.data.statusCode != 10 && this.data.statusCode != 90
                  }
                }
              })
            }
          }
        },
        {
          field: 'executionSituationDesc',
          headerText: _L('执行情况'),
          width: 200,
          template: function () {
            return {
              template: Vue.component('executionSituationDesc', {
                template: `<div>
                <mt-input ref="input" :disabled="enable" width="120" height="26"
                  v-model="data.executionSituationDesc" @blur="dataChange"></mt-input>
                </div>`,
                data() {
                  return {
                    data: {},
                    i18n: i18n
                  }
                },
                computed: {
                  enable() {
                    return this.data.statusCode != 10 && this.data.statusCode != 90
                  }
                },
                methods: {
                  dataChange() {
                    this.$parent.$emit('cellEdit', this.data, 'executionSituationDesc')
                  }
                }
              })
            }
          }
        },
        {
          field: 'isControlled',
          headerText: _L('是否受控'),
          width: 200,
          template: function () {
            return {
              template: Vue.component('isControlled', {
                template: `<div>
                <mt-select ref="input" :id="data.id + Date.parse(new Date())" :disabled="enable" :dataSource="dataSource"
                  v-model="data.isControlled" @change="dataChange"></mt-select>
                </div>`,
                data() {
                  return {
                    i18n: i18n,
                    dataSource: [
                      { text: i18n.t('是'), value: '0' },
                      { text: i18n.t('否'), value: '1' }
                    ]
                  }
                },
                computed: {
                  enable() {
                    return this.data.statusCode != 10 && this.data.statusCode != 90
                  }
                },
                methods: {
                  dataChange(e) {
                    this.data.isControlled = e.itemData.value
                    // debugger
                    // this.$parent.$parent.$parent.$parent.$parent.$parent.rowCellEdit(
                    //   this.data,
                    //   'isControlled'
                    // )
                    this.$parent.$emit('cellEdit', this.data, 'isControlled')
                    // console.log('isControlled', this.data.isControlled, this.data)
                    // this.$parent.$parent.$parent.$parent.$parent.$parent.resultDetail
                  }
                }
              })
            }
          }
        },
        {
          field: 'opinionDesc',
          headerText: _L('描述'),
          width: 200,
          template: function () {
            return {
              template: Vue.component('opinionDesc', {
                template: `<div>
                <mt-input ref="input" :disabled="enable"  width="120" height="26"
                  v-model="data.opinionDesc" @blur="dataChange"></mt-input>
                </div>`,
                data() {
                  return {
                    data: {},
                    i18n: i18n
                  }
                },
                computed: {
                  enable() {
                    return this.data.statusCode != 10 && this.data.statusCode != 90
                  }
                },
                methods: {
                  dataChange() {
                    this.$parent.$emit('cellEdit', this.data, 'opinionDesc')
                  }
                }
              })
            }
          }
        },
        {
          field: 'supportFile',
          headerText: _L('支持文件'),
          width: 200,
          template: function () {
            return {
              template: Vue.component('supportFile', {
                template: `<div>
                <mt-input ref="input" :disabled="enable" width="120" height="26"
                  v-model="data.supportFile" @blur="dataChange"></mt-input>
                </div>`,
                data() {
                  return {
                    data: {},
                    i18n: i18n
                  }
                },
                computed: {
                  enable() {
                    return this.data.statusCode != 10 && this.data.statusCode != 90
                  }
                },
                methods: {
                  dataChange() {
                    this.$parent.$emit('cellEdit', this.data, 'supportFile')
                  }
                }
              })
            }
          }
        },
        {
          field: 'scoringFileInfos',
          headerText: i18n.t('打分依据'),
          width: 200,
          allowEditing: false,
          template: function () {
            return {
              template: Vue.component('scoringFilelnfos', {
                template: `<div>
                  <input
                    ref="file"
                    type="file"
                    style="display: none"
                    @change="chooseFiles"
                    multiple="multiple"
                    accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
                  />
                  <mt-button v-if="data.status != 20 && data.status != 40" :disabled="data.statusCode == 100" class="mgn-left-10" @click="browseBtn()">{{
                    $t("上传文件")
                  }}</mt-button>
                  <p style="margin-top:5px;" v-for="(item,index) in data.scoringFileInfos" :key="index" ><a @click="preview(item)">{{item.fileName}}</a> <span v-if="data.status != 20 && data.status != 40 && data.statusCode !== 100" style="margin-left:10px;cursor: pointer;" @click="deleteFile(item)">删除</span> <span style="margin-left:10px;cursor: pointer;" @click="upload(item)">下载</span></p>
                </div>`,
                data() {
                  return {
                    data: {},
                    sceneList: [],
                    allowFileType: [
                      'xls',
                      'xlsx',
                      'doc',
                      'docx',
                      'pdf',
                      'ppt',
                      'pptx',
                      'png',
                      'jpg',
                      'zip',
                      'rar'
                    ]
                  }
                },
                mounted() {
                  console.log('scoringFilelnfosscoringFilelnfos', this.data.scoringFilelnfos)
                },
                methods: {
                  chooseFiles(data) {
                    this.$loading()
                    console.log('data.targetdata.targetdata.target', data.target)
                    let { files } = data.target
                    files = Object.values(files)
                    let params = {
                      type: 'array',
                      limit: 50 * 1024,
                      msg: this.$t('单个文件，限制50M')
                    }
                    if (files.length < 1) {
                      this.$hloading()
                      // 您未选择需要上传的文件
                      return
                    } else if (files.length > 5) {
                      this.$hloading()
                      this.$toast({
                        content: this.$t('一次性最多选择5个文件'),
                        type: 'warning'
                      })
                      return
                    }
                    let bol = files.some((item) => {
                      let _tempInfo = item.name.split('.')
                      return (
                        _tempInfo.length < 2 ||
                        !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
                      )
                    })
                    if (bol) {
                      this.$toast({
                        content: this.$t(
                          '文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'
                        ),
                        type: 'warning'
                      })
                      this.$hloading()
                      return
                    }
                    bol = files.some((item) => {
                      return item.size > params.limit * 1024
                    })
                    if (bol) {
                      this.$hloading()
                      this.$toast({
                        content: params.msg
                      })
                      return
                    }
                    this.$refs.file.value = ''
                    this.uploadFile(files)
                  },
                  uploadFile(files) {
                    console.log(123)
                    let arr = []
                    files.forEach((item) => {
                      let _data = new FormData()
                      _data.append('UploadFiles', item)
                      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
                      arr.push(this.$API.SupplierPunishment.fileUpload(_data))
                    })
                    Promise.all(arr).then((res) => {
                      res.forEach((item) => {
                        if (item.code == 200) {
                          item.data.fileId = item.data.fileId ? item.data.fileId : item.data.id
                          item.data.fileUrl = item.data.fileUrl ? item.data.fileUrl : item.data.url

                          if (this.data.scoringFileInfos != null) {
                            this.data.scoringFileInfos.push(item.data)
                          } else {
                            this.data.scoringFileInfos = [item.data]
                          }
                          console.log('scoringFilelnfos123', this.data.scoringFileInfos)
                        }
                      })
                      this.$hloading()
                      this.$parent.$emit('cellEdit', this.data, 'scoringFileInfos')
                    })
                  },
                  browseBtn() {
                    this.$refs.file.click()
                  },
                  preview(item) {
                    let params = {
                      id: item.fileId,
                      useType: 1
                    }
                    this.$API.SupplierPunishment.filepreview(params).then((res) => {
                      window.open(res.data)
                    })
                  },
                  upload(item) {
                    this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
                      let link = document.createElement('a')
                      link.style.display = 'none'
                      let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                      let url = window.URL.createObjectURL(blob)
                      link.href = url
                      link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
                      link.click() // 点击下载
                      window.URL.revokeObjectURL(url)
                    })
                  },
                  deleteFile(item) {
                    const index = this.data.scoringFileInfos.findIndex(
                      (file) => file.id === item.id
                    )
                    this.data.scoringFileInfos.splice(index, 1)
                    this.$parent.$emit('cellEdit', this.data, 'scoringFileInfos')
                  }
                }
              })
            }
          }
        },
        { field: 'scoreUserName', headerText: i18n.t('打分人'), width: 120, allowEditing: false },
        {
          field: 'scoreTime',
          headerText: i18n.t('打分时间'),
          width: 180,
          allowEditing: false,
          template: () => {
            return {
              template: Vue.component('scoreTime', {
                template: `<div>
                    <span>{{data.scoreTime? utils.formateTime(Number(data.scoreTime), "yyyy-MM-dd hh:mm:ss"):""}}</span>
                  </div>`,
                data: function () {
                  return {
                    utils
                  }
                }
              })
            }
          }
        }
      ],
      dataSource: []
    }
  }
]

export const columnData = [
  { type: 'checkbox', width: 100, treeNode: true, fixed: 'left' },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    field: 'itemName',
    title: i18n.t('评审项'),
    minWidth: 360
  },
  {
    field: 'scoreBegin',
    title: i18n.t('最低分'),
    minWidth: 100
  },
  {
    field: 'scoreEnd',
    title: i18n.t('最高分'),
    minWidth: 100
  },
  {
    field: 'selfScore',
    title: i18n.t('得分'),
    minWidth: 100,
    editRender: {},
    slots: {
      default: 'scoreDefault'
    }
  },
  {
    field: 'executionSituationDesc',
    title: i18n.t('执行情况'),
    minWidth: 200,
    editRender: {},
    slots: {
      default: 'executionSituationDescDefault'
    }
  },
  {
    field: 'isControlled',
    title: i18n.t('是否受控'),
    minWidth: 100,
    editRender: {},
    slots: {
      default: 'isControlledDefault'
    }
  },
  {
    field: 'useSetStatus',
    title: i18n.t('是否适用'),
    minWidth: 100,
    editRender: {},
    slots: {
      default: 'useSetStatusDefault'
    }
  },
  {
    field: 'opinionDesc',
    title: i18n.t('描述'),
    minWidth: 200,
    editRender: {},
    slots: {
      default: 'opinionDescDefault'
    }
  },
  {
    field: 'supportFile',
    title: i18n.t('支持文件'),
    minWidth: 200,
    editRender: {},
    slots: {
      default: 'supportFileDefault'
    }
  },
  {
    field: 'scoringFileInfos',
    title: i18n.t('打分依据'),
    minWidth: 400,
    editRender: {},
    slots: {
      default: 'scoringFileInfosDefault'
    }
  },
  {
    field: 'scoreUserName',
    title: i18n.t('打分人'),
    minWidth: 100
  },
  {
    field: 'scoreTime',
    title: i18n.t('打分时间'),
    minWidth: 200,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  }
]
