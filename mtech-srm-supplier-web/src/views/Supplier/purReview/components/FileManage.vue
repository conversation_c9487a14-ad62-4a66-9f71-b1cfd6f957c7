<!-- 附件管理 -->
<template>
  <mt-dialog ref="fileDialog" height="900" :header="header" @close="close">
    <div class="dialog-content">
      <sc-table ref="fileTable" :columns="columns" :table-data="tableData" :loading="loading">
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #fileNameDefault="{ row }">
          <span style="color: #2783fe; cursor: pointer" @click="handlePreview(row)">
            {{ row.fileName }}
          </span>
        </template>
        <template #operateDefault="{ row }">
          <span
            v-if="type === 'upload'"
            style="color: #2783fe; cursor: pointer; margin-right: 20px"
            @click="handleDelete(row)"
          >
            {{ $t('删除') }}
          </span>
          <span style="color: #2783fe; cursor: pointer" @click="handleDownload(row)">
            {{ $t('下载') }}
          </span>
        </template>
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'
export default {
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      columns: [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'fileName',
          title: this.$t('文件名称'),
          slots: {
            default: 'fileNameDefault'
          }
        },
        {
          field: 'operate',
          title: this.$t('操作'),
          slots: {
            default: 'operateDefault'
          }
        }
      ],
      toolbar: [],
      tableData: [],
      loading: false
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    }
  },
  mounted() {
    if (this.type === 'download') {
      this.toolbar = []
      this.tableData = this.modalData.fileList
    } else if (this.type === 'upload') {
      this.toolbar = [{ code: 'upload', name: this.$t('附件上传'), status: 'info', loading: false }]
      this.getFileList()
    }
    this.$refs['fileDialog'].ejsRef.show()
  },
  methods: {
    getFileList() {
      this.loading = true
      let params = {
        reviewTaskTemplateId: this.modalData.id,
        taskCode: this.modalData.taskCode
      }
      this.$API.supplierReviewTask.getSelfResultFileApi(params).then((res) => {
        if (res.code === 200) {
          this.loading = false
          this.tableData = res.data
        }
      })
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'upload':
          this.$dialog({
            modal: () => import('@/components/Upload/index.vue'),
            data: {
              title: this.$t('导入')
            },
            success: (res) => {
              res.fileId = res.id
              res.fileUrl = res.url
              this.handleUpload(res)
            }
          })
          break
        default:
          break
      }
    },
    handleUpload(file) {
      file.id = null
      let params = {
        reviewTaskTemplateId: this.modalData.id,
        taskCode: this.modalData.taskCode,
        fileInfoDTOList: [file]
      }
      this.$API.supplierReviewTask.uploadSelfResultFileApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('上传成功'),
            type: 'success'
          })
          this.getFileList()
        }
      })
    },
    handlePreview(row) {
      let params = {
        id: row?.fileId || row.id,
        useType: 2
      }
      this.$API.fileService.filePreview(params).then((res) => {
        window.open(res.data)
      })
    },
    handleDelete(row) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除该数据？')
        },
        success: () => {
          let params = {
            reviewTaskTemplateId: this.modalData.id,
            taskCode: this.modalData.taskCode,
            fileInfoDTOList: [row]
          }
          this.$API.supplierReviewTask.deleteSelfResultFileApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.getFileList()
            }
          })
        }
      })
    },
    handleDownload(row) {
      this.$loading()
      this.$API.fileService.downloadPrivateFile({ id: row?.fileId || row.id }).then((res) => {
        this.$hloading()
        download({
          fileName: row.fileName,
          blob: new Blob([res.data])
        })
      })
    },
    close() {
      this.$emit('cancel-function', this.tableData)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  margin: 16px 0;
  ::v-deep .toolbar-container {
    padding: 0 !important;
  }
  .common-uploader {
    display: none;
  }
}
</style>
