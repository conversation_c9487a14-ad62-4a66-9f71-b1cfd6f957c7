<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config/index'
export default {
  components: {},
  data() {
    return {
      pageConfig
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      console.log(e)
      const list = e.gridRef.getMtechGridRecords()
      //   console.log("handleClickToolBar", e);
      if (e.toolbar.id == 'submit' && !list.length) {
        this.$toast({
          content: this.$t('请选择一条数据'),
          type: 'warning'
        })
        return
      }
      this.submit(list)
      //   const list = e.gridRef.getMtechGridRecords()
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
      if (e.field == 'code') {
        this.$router.push({
          path: '/supplier/sup/review-detail',
          query: {
            code: e.data.code,
            status: e.data.status
          }
        })
      }
    },
    handleClickCellTool(e) {
      const id = e.tool.id
      const data = e.data
      if (id === 'edit') {
        this.$router.push({
          path: '/supplier/sup/review-detail',
          query: {
            code: data.code
          }
        })
      }
    },
    submit(data) {
      const disabeldList = data.filter((i) => i.status == 20)
      if (disabeldList.length) {
        this.$toast({
          content: this.$t('存在已提交的任务，请重新选择'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('是否确认提交？')
        },
        success: async () => {
          const param = data.map((i) => i.code)
          const res = await this.$API.supplierReviewTask.saveSupplier(param)
          if (res) {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },
    confirm() {}
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
