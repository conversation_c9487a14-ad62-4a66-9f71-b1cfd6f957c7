<template>
  <div class="oreder-warp" style="height: 100%">
    <div class="treeView">
      <ul class="left-nav">
        <li
          :class="{ active: currentIndex == index }"
          v-for="(item, index) in reviewContent"
          :key="item.templateTypeCode"
          @click="indexChange(index)"
        >
          {{ item.templateTypeName }}
        </li>
        <li :class="{ active: currentIndex == index }">{{ $t('总分：') }}{{ totalScore }}</li>
      </ul>
    </div>
    <div class="table">
      <div v-if="!isShow">
        <sc-table
          ref="sctableRef"
          :loading="loading"
          :columns="columns"
          :table-data="tableData"
          :tree-config="treeConfig"
          header-align="left"
          align="left"
          :row-config="{ height: rowHeight }"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item)"
            >
              {{ item.name }}
            </vxe-button>
          </template>
          <template #scoreDefault="{ row }">
            <vxe-input
              v-model="row.selfScore"
              type="number"
              :min="row.scoreBegin"
              :max="row.scoreEnd"
              :placeholder="$t('请输入')"
              clearable
              size="mini"
              :disabled="!row.parentCode"
              @change="selfScoreChange"
            />
          </template>
          <template #executionSituationDescDefault="{ row }">
            <vxe-input
              v-model="row.executionSituationDesc"
              :placeholder="$t('请输入')"
              clearable
              size="mini"
              :disabled="!row.parentCode"
            />
          </template>
          <template #isControlledDefault="{ row }">
            <vxe-select
              v-model="row.isControlled"
              :placeholder="$t('请选择')"
              clearable
              transfer
              size="mini"
              :disabled="!row.parentCode"
            >
              <vxe-option value="0" label="是"></vxe-option>
              <vxe-option value="1" label="否"></vxe-option>
            </vxe-select>
          </template>
          <template #useSetStatusDefault="{ row }">
            <vxe-select
              v-model="row.useSetStatus"
              :placeholder="$t('请选择')"
              clearable
              transfer
              size="mini"
              :disabled="!row.parentCode"
              @change="selfScoreChange"
            >
              <vxe-option :value="0" label="是"></vxe-option>
              <vxe-option :value="1" label="否"></vxe-option>
            </vxe-select>
          </template>
          <template #opinionDescDefault="{ row }">
            <vxe-input
              v-model="row.opinionDesc"
              :placeholder="$t('请输入')"
              clearable
              size="mini"
              :disabled="!row.parentCode"
            />
          </template>
          <template #supportFileDefault="{ row }">
            <vxe-input
              v-model="row.supportFile"
              :placeholder="$t('请输入')"
              clearable
              size="mini"
              :disabled="!row.parentCode"
            />
          </template>
          <template #scoringFileInfosDefault="{ row }">
            <div style="display: flex; align-items: center">
              <div>
                <input
                  ref="file"
                  type="file"
                  style="display: none"
                  @change="(data) => chooseScoreFiles(data, row)"
                  multiple="multiple"
                  accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar,"
                />
                <vxe-button @click="handleUpload(row)" :disabled="!row.parentCode">{{
                  $t('上传文件')
                }}</vxe-button>
              </div>
              <div style="margin-left: 10px">
                <p
                  style="margin-top: 5px"
                  v-for="(item, index) in row.scoringFileInfos"
                  :key="index"
                >
                  <a @click="preview(item)">{{ item.fileName }}</a>
                  <span
                    v-if="row.status != 20 && row.status != 40 && row.statusCode !== 100"
                    style="margin-left: 10px; cursor: pointer"
                    @click="deleteFile(item, row)"
                    >{{ $t('删除') }}</span
                  >
                  <span style="margin-left: 10px; cursor: pointer" @click="upload(item)">{{
                    $t('下载')
                  }}</span>
                </p>
              </div>
            </div>
          </template>
        </sc-table>
      </div>
      <div v-else>
        <div style="padding: 20px; display: flex">
          <div style="margin-right: 20px">
            <span>{{ $t('评审模板') }}：</span>
            <span style="color: #2783fe; cursor: pointer" @click="handleDownload">{{
              $t('附件下载')
            }}</span>
          </div>
          <div>
            <span>{{ $t('供应商自查结果') }}：</span>
            <span style="color: #2783fe; cursor: pointer" @click="resultUpload">{{
              $t('附件上传')
            }}</span>
          </div>
        </div>
        <sc-table
          ref="oTable"
          :columns="otherColumns"
          :table-data="tableData"
          :row-config="{ height: rowHeight }"
          keep-source
          show-overflow
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in otherToolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickOtherToolBar(item)"
            >
              {{ item.name }}
            </vxe-button>
          </template>
          <template #weightDefault="{ row }">
            <vxe-input
              v-model="row.weight"
              type="number"
              :min="1"
              :max="100"
              :placeholder="$t('请输入权重')"
              clearable
              @change="weightChange(row)"
            />
          </template>
        </sc-table>
      </div>
    </div>
    <input
      ref="files"
      type="file"
      v-show="false"
      @change="chooseImportFiles"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    />
    <mt-dialog ref="toast" :header="$t('批量打分')" :buttons="sonbuttons" :show-close-icon="true">
      <div style="padding: 50px">
        <mt-form ref="dialogRef" :model="pathScroe" class="form-box">
          <mt-form-item prop="selfScore" :label="$t('分数：')">
            <mt-inputNumber
              :placeholder="$t('请输入分数')"
              :min="0"
              :max="9999"
              v-model="pathScroe.selfScore"
            ></mt-inputNumber>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config/index'
import { decimalAdd, decimalMul, decimalDiv } from '@/utils/math.js'

export default {
  components: { ScTable },
  data() {
    return {
      currentIndex: 0,
      reviewContent: [],
      resultDetail: null,
      sonbuttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      pathScroe: {
        selfScore: null,
        useSetStatus: null
      },
      selectItemCode: [],
      totalScore: 0,

      loading: false,
      rowHeight: 48,
      treeConfig: {
        transform: true,
        rowField: 'itemCode',
        parentField: 'parentCode',
        expandAll: true
      },
      columns: columnData,
      tableData: [],
      scoreFileId: null,

      isShow: false,
      otherColumns: [
        { type: 'checkbox', width: 50, fixed: 'left' },
        {
          field: 'itemName',
          title: this.$t('维度'),
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.itemName}
                  type='text'
                  placeholder={this.$t('请输入维度')}
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'weight',
          title: this.$t('权重'),
          slots: {
            default: 'weightDefault'
          }
        },
        {
          field: 'fullScore',
          title: this.$t('满分')
        },
        {
          field: 'score',
          title: this.$t('及格分'),
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.score}
                  type='number'
                  min={0}
                  max={row.fullScore}
                  placeholder={this.$t('请输入及格分')}
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'selfScore',
          title: this.$t('自检得分'),
          slots: {
            default: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.selfScore}
                  type='number'
                  min={0}
                  max={row.fullScore}
                  placeholder={this.$t('请输入自检得分')}
                  clearable
                  onChange={this.selfScoreChange}
                />
              ]
            }
          }
        }
      ],
      useSetStatusOptions: [
        { label: this.$t('是'), value: 0 },
        { label: this.$t('否'), value: 1 }
      ],
      templateId: null
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      let data = [
        { code: 'score', name: this.$t('批量打分'), status: 'info', loading: false },
        { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
        { code: 'saveSubmit', name: this.$t('保存并提交'), status: 'info', loading: false },
        { code: 'back', name: this.$t('返回'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ]
      if (this.resultDetail.status === 100) {
        data = [{ code: 'back', name: this.$t('返回'), status: 'info', loading: false }]
      }
      return data
    },
    otherToolbar() {
      let toolbar = [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
        { code: 'saveSubmit', name: this.$t('保存并提交'), status: 'info', loading: false },
        { code: 'back', name: this.$t('返回'), status: 'info', loading: false }
      ]
      if (this.resultDetail.status === 100) {
        toolbar = [{ code: 'back', name: this.$t('返回'), status: 'info', loading: false }]
      }
      return toolbar
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      const res = await this.$API.supplierReviewTask.supplierPreviewDetail({
        code: this.$route.query.code
      })
      if (res.code === 200) {
        this.resultDetail = res.data
        this.reviewContent = this.resultDetail.templateResponses
        this.insetStatus(this.reviewContent)
        this.indexChange(this.currentIndex)
      }
    },
    insetStatus(data) {
      data.forEach((item) => {
        item.statusCode = this.resultDetail.status
        if (item.itemResponses && item.itemResponses.length) {
          this.insetStatus(item.itemResponses)
        }
      })
    },
    flatTableData(list) {
      let arr = []
      list.forEach((item) => {
        arr = arr.concat(item.itemResponses || [])
      })
      const res = arr.concat(list)
      res.forEach((item) => {
        item.checked = false
        item.unqualified = item.symbol + ' ' + item.score
        item.range = item.scoreEnd ? `${item.scoreBegin || 0}-${item.scoreEnd}` : ''
      })
      return res
    },
    indexChange(index) {
      this.setData()
      this.currentIndex = index
      let item = this.resultDetail.templateResponses[this.currentIndex]
      this.isShow = [
        'quality-qpa',
        'RD-rd1',
        'fc-zrvn',
        'gf-gfzl',
        'gf-gfsw',
        'gf-gfjs',
        'gf-gfzlyf',
        'tx-txyf',
        'tx-txsw',
        'tx-txzl'
      ].includes(item?.templateTypeId)
      this.templateId = this.isShow ? item.id : ''
      this.tableData = this.isShow
        ? this.resultDetail.templateResponses[this.currentIndex]?.itemResponses
        : this.flatTableData(
            this.resultDetail.templateResponses[this.currentIndex]?.itemResponses || []
          )
      this.totalScore = 0
      this.resultDetail.templateResponses.forEach((item) => {
        if (
          [
            'quality-qpa',
            'RD-rd1',
            'fc-zrvn',
            'gf-gfzl',
            'gf-gfsw',
            'gf-gfjs',
            'gf-gfzlyf',
            'tx-txyf',
            'tx-txsw',
            'tx-txzl'
          ].includes(item?.templateTypeId)
        ) {
          item.itemResponses?.forEach((ele) => {
            this.totalScore = decimalAdd(this.totalScore, Number(ele?.selfScore || 0))
          })
        } else {
          item.itemResponses?.forEach((ele) => {
            let numerator = 0
            let denominator = 0
            ele.itemResponses?.forEach((e) => {
              if (e?.useSetStatus === 0) {
                numerator = decimalAdd(numerator, Number(e?.selfScore || 0))
                denominator = decimalAdd(denominator, Number(e?.scoreEnd || 0))
              }
            })
            this.totalScore = decimalAdd(
              this.totalScore,
              decimalMul(
                decimalDiv(numerator, denominator),
                (Number(ele.weight) / 100) * item.templateFullScore
              )
            )
          })
        }
      })
      this.$nextTick(() => {
        !this.isShow && this.$refs.sctableRef.setAllTreeExpand(true)
      })
    },
    setData() {
      if (this.isShow) {
        this.resultDetail.templateResponses[this.currentIndex].itemResponses =
          this.$refs.oTable.$refs.xGrid.getTableData().visibleData
      }
    },
    handleDownload() {
      let fileList = this.resultDetail.templateResponses[this.currentIndex].fileInfoResponseList
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('附件下载'),
          fileList,
          type: 'download'
        }
      })
    },
    resultUpload() {
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          type: 'upload',
          id: this.templateId,
          taskCode: this.$route.query.code
        }
      })
    },
    handleUpload(row) {
      this.scoreFileId = row.id
      this.$refs.file.click()
    },
    // 选择打分依据
    chooseScoreFiles(data, row) {
      this.$loading()
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      } else if (files.length > 5) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return (
          _tempInfo.length < 2 ||
          ![
            'xls',
            'xlsx',
            'doc',
            'docx',
            'pdf',
            'ppt',
            'pptx',
            'png',
            'jpg',
            'zip',
            'rar'
          ].includes(_tempInfo[_tempInfo.length - 1])
        )
      })
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      bol = files.some((item) => {
        return item.size > params.limit * 1024
      })
      if (bol) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$refs.file.value = ''
      this.uploadScoreFile(files, row)
    },
    uploadScoreFile(files) {
      let arr = []
      files.forEach((item) => {
        let _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
        arr.push(this.$API.SupplierPunishment.fileUpload(_data))
      })
      Promise.all(arr).then((res) => {
        res.forEach((item) => {
          if (item.code == 200) {
            item.data.fileId = item.data.fileId ? item.data.fileId : item.data.id
            item.data.fileUrl = item.data.fileUrl ? item.data.fileUrl : item.data.url

            const row = this.tableData.find((e) => this.scoreFileId === e.id)

            if (row?.scoringFileInfos != null) {
              row.scoringFileInfos.push(item.data)
            } else {
              row.scoringFileInfos = [item.data]
            }
          }
        })
        this.setRowHeight()
        this.$hloading()
      })
    },
    setRowHeight() {
      let row = 0
      this.tableData.forEach((item) => {
        row = item.scoringFileInfos?.length > row ? item.scoringFileInfos.length : row
      })
      this.rowHeight = 48 + 12 * row
      this.tableRef.refreshColumn()
    },
    preview(item) {
      let params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    upload(item) {
      this.$API.SupplierPunishment.fileDownload(item.fileId).then((res) => {
        let link = document.createElement('a')
        link.style.display = 'none'
        let blob = new Blob([res.data], { type: 'application/x-msdownload' })
        let url = window.URL.createObjectURL(blob)
        link.href = url
        link.setAttribute('download', `${item.fileName}`) // 给下载后的文件命名
        link.click() // 点击下载
        window.URL.revokeObjectURL(url)
      })
    },
    deleteFile(item, row) {
      const index = row.scoringFileInfos.findIndex((file) => file.id === item.id)
      row.scoringFileInfos.splice(index, 1)
      this.setRowHeight()
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['delete', 'score']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请选择至少一条数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'score':
          this.$refs.toast.ejsRef.show()
          this.selectItemCode = selectedRecords.map((i) => i.itemCode)
          break
        case 'save':
          this.save(0)
          break
        case 'saveSubmit':
          this.save(1)
          break
        case 'back':
          this.$router.push({
            path: '/supplier/sup/review'
          })
          break
        case 'import':
          this.$refs.files.click()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    weightChange(row) {
      row.fullScore = row.weight
      if (row.score > row.fullScore) {
        row.score = 0
      }
      if (row.selfScore > row.fullScore) {
        row.selfScore = 0
      }
    },
    handleClickOtherToolBar(e) {
      const selectedRecords = this.$refs.oTable.$refs.xGrid.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请选择至少一条数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete(ids)
          break
        case 'save':
          this.setData()
          this.save(0)
          break
        case 'saveSubmit':
          this.setData()
          this.save(1)
          break
        case 'back':
          this.$router.push({
            path: '/supplier/sup/review'
          })
          break
      }
    },
    handleAdd() {
      const currentViewRecords = this.$refs.oTable.$refs.xGrid.getTableData().visibleData
      let weight = 100
      currentViewRecords.forEach((i) => {
        weight -= i.weight
      })
      if (weight <= 0) {
        this.$toast({
          content: this.$t('权重之和不能大于100'),
          type: 'warning'
        })
        return
      }
      const item = {
        itemName: null,
        fullScore: weight,
        symbol: '3',
        score: 0,
        selfScore: 0,
        weight: weight,
        useSetStatus: 0
      }
      this.$refs.oTable.$refs.xGrid.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.$refs.oTable.$refs.xGrid.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.$refs.oTable.$refs.xGrid.setEditRow(currentViewRecords[0])
      })
    },
    handleDelete(ids) {
      ids.forEach((id) => {
        this.tableData = this.tableData.filter((item) => item.id !== id)
      })
      this.resultDetail.templateResponses[this.currentIndex].itemResponses = this.tableData
      this.totalScore = 0
      this.resultDetail.templateResponses.forEach((item) => {
        if (
          [
            'quality-qpa',
            'RD-rd1',
            'fc-zrvn',
            'gf-gfzl',
            'gf-gfsw',
            'gf-gfjs',
            'gf-gfzlyf',
            'tx-txyf',
            'tx-txsw',
            'tx-txzl'
          ].includes(item?.templateTypeId)
        ) {
          item.itemResponses?.forEach((ele) => {
            this.totalScore = decimalAdd(this.totalScore, Number(ele?.selfScore || 0))
          })
        } else {
          item.itemResponses?.forEach((ele) => {
            let numerator = 0
            let denominator = 0
            ele.itemResponses?.forEach((e) => {
              if (e?.useSetStatus === 0) {
                numerator = decimalAdd(numerator, Number(e?.selfScore || 0))
                denominator = decimalAdd(denominator, Number(e?.scoreEnd || 0))
              }
            })
            this.totalScore = decimalAdd(
              this.totalScore,
              decimalMul(
                decimalDiv(numerator, denominator),
                (Number(ele.weight) / 100) * item.templateFullScore
              )
            )
          })
        }
      })
    },
    // 选择导入文件
    chooseImportFiles(data) {
      let { files } = data.target
      var reg = /^.*\.(?:xls|xlsx)$/i
      if (!reg.test(files[0].name)) {
        this.$toast({
          content: this.$t('文件格式不正确.xls.xlsx'),
          type: 'warning'
        })
        return
      }
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('multipartFile', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$loading()
      this.uploadFile(_data)
    },
    async uploadFile(data) {
      this.$refs.files.value = ''
      const res = await this.$API.supplierReviewTask.taskImport(data)
      this.$hloading()
      if (res.code == 200) {
        this.$toast({
          content: this.$t('导入成功'),
          type: 'success'
        })
        this.init()
      }
    },
    handleExport(e) {
      this.$API.supplierReviewTask
        .taskExport({
          code: this.$route.query.code
        })
        .then((res) => {
          const nowTimes = new Date().getTime()
          const filename = `${this.$route.query.code}-${nowTimes}.xlsx`
          const url = window.URL.createObjectURL(new Blob([res.data]))
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.download = filename
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href) // 释放URL 对象
          document.body.removeChild(link)
        })
        .finally(() => {
          e.loading = false
        })
    },
    confirm() {
      let { selfScore, useSetStatus } = this.pathScroe
      this.resultDetail.templateResponses[this.currentIndex].itemResponses?.forEach((item) => {
        item.itemResponses?.forEach((e) => {
          if (this.selectItemCode.includes(e.itemCode)) {
            let selfScoreCopy = selfScore
            if (selfScoreCopy < e.scoreBegin) {
              selfScoreCopy = e.scoreBegin
            }
            if (selfScoreCopy > e.scoreEnd) {
              selfScoreCopy = e.scoreEnd
            }
            e.selfScore = selfScoreCopy
            e.useSetStatus = Number(useSetStatus)
          }
        })
      })
      this.totalScore = 0
      this.resultDetail.templateResponses.forEach((item) => {
        if (
          [
            'quality-qpa',
            'RD-rd1',
            'fc-zrvn',
            'gf-gfzl',
            'gf-gfsw',
            'gf-gfjs',
            'gf-gfzlyf',
            'tx-txyf',
            'tx-txsw',
            'tx-txzl'
          ].includes(item?.templateTypeId)
        ) {
          item.itemResponses?.forEach((ele) => {
            this.totalScore = decimalAdd(this.totalScore, Number(ele?.selfScore || 0))
          })
        } else {
          item.itemResponses?.forEach((ele) => {
            let numerator = 0
            let denominator = 0
            ele.itemResponses?.forEach((e) => {
              if (e?.useSetStatus === 0) {
                numerator = decimalAdd(numerator, Number(e?.selfScore || 0))
                denominator = decimalAdd(denominator, Number(e?.scoreEnd || 0))
              }
            })
            this.totalScore = decimalAdd(
              this.totalScore,
              decimalMul(
                decimalDiv(numerator, denominator),
                (Number(ele.weight) / 100) * item.templateFullScore
              )
            )
          })
        }
      })
      this.$refs.toast.ejsRef.hide()
      this.pathScroe = {
        selfScore: null,
        useSetStatus: null
      }
    },
    cancel() {
      this.$refs.toast.ejsRef.hide()
    },
    save(num) {
      let valid = true
      let arr = this.resultDetail.templateResponses
      for (let i = 0; i < arr.length; i++) {
        const ele = arr[i]
        // if (['质量-QPA', '研发-研发'].includes(ele.templateTypeName)) {
        if (
          [
            'quality-qpa',
            'RD-rd1',
            'fc-zrvn',
            'gf-gfzl',
            'gf-gfsw',
            'gf-gfjs',
            'gf-gfzlyf',
            'tx-txyf',
            'tx-txsw',
            'tx-txzl'
          ].includes(ele.templateTypeId)
        ) {
          // let weight = 0
          for (let j = 0; j < ele.itemResponses?.length; j++) {
            const item = ele.itemResponses[j]
            // weight += Number(item.weight)
            if (!item.itemName) {
              this.$toast({
                content: this.$t('请输入维度'),
                type: 'warning'
              })
              valid = false
              break
            }
            if (item.selfScore === null || item.selfScore === '') {
              this.$toast({
                content: this.$t('请输入自检得分'),
                type: 'warning'
              })
              valid = false
              break
            }
          }
          // if (valid && weight !== 100) {
          //   this.$toast({
          //     content: this.$t('权重之和只能为100'),
          //     type: 'warning'
          //   })
          //   valid = false
          //   break
          // }
        } else {
          if (num === 1) {
            for (let j = 0; j < ele.itemResponses?.length; j++) {
              const item = ele.itemResponses[j]
              for (let k = 0; k < item.itemResponses.length; k++) {
                const e = item.itemResponses[k]
                if (e.useSetStatus !== 1 && (e.selfScore === null || e.selfScore === '')) {
                  this.$toast({
                    content: this.$t('有评分项还未打分请检查'),
                    type: 'warning'
                  })
                  valid = false
                  break
                }
              }
            }
          }
        }
      }
      if (!valid) {
        return
      }
      const enms = ['是否确定保存该打分结果？', '是否确定保存并提交该打分结果？']
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`总分为：${this.totalScore}分，${enms[num]}`)
        },
        success: () => {
          this.$loading()
          const param = {}
          param.code = this.$route.query.code
          param.isSubmit = num
          param.templateLists = this.resultDetail.templateResponses.map((i) => {
            // if (['质量-QPA', '研发-研发'].includes(i.templateTypeName)) {
            if (
              [
                'quality-qpa',
                'RD-rd1',
                'fc-zrvn',
                'gf-gfzl',
                'gf-gfsw',
                'gf-gfjs',
                'gf-gfzlyf',
                'tx-txyf',
                'tx-txsw',
                'tx-txzl'
              ].includes(i.templateTypeId)
            ) {
              return {
                templateCode: i.templateCode,
                templateTypeId: i.templateTypeId,
                templateTypeName: i.templateTypeName,
                templateItemRequests: i.itemResponses?.map((e) => {
                  return {
                    id: e.id?.includes('row_') ? null : e.id,
                    taskCode: this.$route.query.code,
                    templateCode: i.templateCode,
                    itemName: e.itemName,
                    fullScore: e.fullScore,
                    symbol: e.symbol,
                    score: e.score,
                    selfScore: e.selfScore,
                    actScore: e.selfScore,
                    weight: e.weight,
                    useSetStatus: e.useSetStatus
                  }
                })
              }
            } else {
              let templateItemRequests = []
              i.itemResponses?.forEach((ele) => {
                ele.itemResponses?.forEach((e) => {
                  templateItemRequests.push({
                    itemCode: e.itemCode,
                    selfScore: e.selfScore,
                    actScore: e.selfScore,
                    useSetStatus: e.useSetStatus ? e.useSetStatus : '0',
                    supportFile: e.supportFile,
                    scoringFileInfos: e.scoringFileInfos,
                    opinionDesc: e.opinionDesc,
                    isControlled: e.isControlled,
                    executionSituationDesc: e.executionSituationDesc
                  })
                })
              })
              return {
                templateCode: i.templateCode,
                templateTypeId: i.templateTypeId,
                templateTypeName: i.templateTypeName,
                templateItemRequests: templateItemRequests
              }
            }
          })
          this.$API.supplierReviewTask
            .supplierSaveOrSubmit(param)
            .then((res) => {
              if (res) {
                if (num == 1) {
                  this.$router.push({
                    path: '/supplier/sup/review'
                  })
                } else {
                  this.$toast({
                    content: res.msg,
                    type: 'success'
                  })
                  this.init()
                }
              }
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    // 总分计算
    selfScoreChange() {
      this.setData()
      this.totalScore = 0
      this.resultDetail.templateResponses.forEach((item) => {
        // if (['质量-QPA', '研发-研发'].includes(item?.templateTypeName)) {
        if (
          [
            'quality-qpa',
            'RD-rd1',
            'fc-zrvn',
            'gf-gfzl',
            'gf-gfsw',
            'gf-gfjs',
            'gf-gfzlyf',
            'tx-txyf',
            'tx-txsw',
            'tx-txzl'
          ].includes(item?.templateTypeId)
        ) {
          item.itemResponses?.forEach((ele) => {
            this.totalScore = decimalAdd(this.totalScore, Number(ele?.selfScore || 0))
          })
        } else {
          item.itemResponses?.forEach((ele) => {
            let numerator = 0
            let denominator = 0
            ele.itemResponses?.forEach((e) => {
              if (e?.useSetStatus === 0) {
                numerator = decimalAdd(numerator, Number(e?.selfScore || 0))
                denominator = decimalAdd(denominator, Number(e?.scoreEnd || 0))
              }
            })
            this.totalScore = decimalAdd(
              this.totalScore,
              decimalMul(
                decimalDiv(numerator, denominator),
                (Number(ele.weight) / 100) * item.templateFullScore
              )
            )
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.oreder-warp {
  display: flex;
  .treeView {
    width: 180px;
    margin-right: 20px;
  }
  .table {
    width: calc(100% - 180px);
    flex: 1;
  }
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: rgba(0, 0, 0, 0.87);
}
::v-deep .e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #fff;
  border-color: #fff;
  border-bottom: 2px solid #00469c;
}
::v-deep .mt-tree-view .e-treeview .e-fullrow {
  opacity: 1;
}
::v-deep .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: #00469c;
}

.left-nav {
  line-height: 40px;
  text-align: center;
  li {
    cursor: pointer;
  }
  .active {
    background: linear-gradient(135deg, rgba(245, 84, 72, 1) 0%, rgba(255, 128, 118, 1) 100%);
    color: #fff;
  }
}
</style>
