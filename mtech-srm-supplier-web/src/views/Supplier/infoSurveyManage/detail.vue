<template>
  <div class="full-height">
    <div class="form-dialog">
      <!-- 右侧各种操作按钮 -->
      <div class="operateButton">
        <mt-button css-class="e-flat" @click="cancel" :is-primary="true">{{
          $t('取消')
        }}</mt-button>
        <mt-button css-class="e-flat" @click="saveData" :is-primary="true">{{
          $t('保存并提交')
        }}</mt-button>
      </div>
      <div class="categroy-list fbox" v-if="buyerPartnerFactoryRelationList.length > 0">
        <div
          class="factory-item fbox"
          v-for="item of buyerPartnerFactoryRelationList"
          :key="item.id"
        >
          <span class="factory-name">{{ item.factoryName }}</span>
          <template
            v-if="
              !!item.buyerPartnerCategoryRelationList &&
              item.buyerPartnerCategoryRelationList.length > 0
            "
          >
            <span
              class="factory-category"
              v-for="cItem of item.buyerPartnerCategoryRelationList"
              :key="cItem.id"
              >{{ cItem.categoryName }}</span
            >
          </template>
        </div>
      </div>
      <div class="task-form fbox">
        <div class="side-bar">
          <div
            class="side-item ellipsis"
            :class="{ active: activeForm.id === item.id }"
            v-for="item of buyerFormInstanceList"
            :key="item.id"
            @click="scrollInto(item.id)"
          >
            {{ item.formName }}
          </div>
          <!--@click="selectFromItem(item)"-->
        </div>

        <div class="form-content flex1" ref="formContent">
          <template v-if="formTemplateArr.length > 0">
            <div
              v-for="item of formTemplateArr"
              :key="item.id"
              class="display-item"
              :ref="'formItem_' + item.id"
              :data-id="item.id"
            >
              <!--class="none" :class="{'display-item': activeForm.id === item.id}"-->
              <div class="parse-title">{{ item.name }}</div>
              <mt-parser :ref="`parser_${item.id}`" :form-conf="item.value" />
            </div>
          </template>
          <template v-else-if="!!emptyMsg">
            <div class="empty-container">
              <img src="../../../assets/emptyData.png" />
              <div class="empty-txt">{{ emptyMsg }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Parser from '@mtech-form-design/form-parser'

export default {
  components: {
    'mt-parser': Parser
  },
  data() {
    return {
      emptyMsg: '',
      //公司准入关系
      buyerPartnerFactoryRelationList: {},
      //表单详情
      buyerFormInstanceList: {},
      // 表单模板
      formTemplateArr: []
    }
  },
  methods: {
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      }
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
      if (e.field == 'listCode') {
        this.addDialogShow = true
      }
    },
    handleAdd() {
      console.log('点击新增')
      this.addDialogShow = true
    },
    cancel() {
      this.addDialogShow = false
    },
    saveData() {}
  }
}
</script>
<style lang="scss" scoped>
.task-form {
  flex: 1;
  .side-bar {
    width: 160px;
    border-right: 1px solid rgba(232, 232, 232, 1);
    padding-bottom: 20px;
    .ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .side-item {
      width: 100%;
      height: 50px;
      line-height: 50px;
      font-size: 14px;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      padding-left: 20px;
      cursor: pointer;
      -moz-user-select: none;
      -ms-user-select: none;
      -webkit-user-select: none;
      user-select: none;
    }
    .active {
      color: #00469c;
      background: #f5f6f9;
    }
  }

  .form-content {
    padding: 30px 20px;
    overflow: auto;
    -webkit-overflow-scrolling: auto;
    transform: rotate(1);
    position: relative;

    .parse-title {
      color: #292929;
      position: relative;
      display: flex;
      padding-left: 12px;
      margin-bottom: 20px;
      font-size: 14px;
      font-weight: 500;

      &::before {
        content: ' ';
        display: inline-block;
        position: absolute;
        width: 2px;
        height: 14px;
        background: #00469c;
        left: 0;
      }
    }

    .empty-box {
      margin: 0 auto;
      margin-top: 40px;
      font-size: 16px;
      color: #333;
      text-align: center;
    }

    .empty-container {
      height: 240px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate3d(-50%, -50%, 0);

      img {
        display: block;
        width: 200px;
        height: 160px;
      }

      .empty-txt {
        text-align: center;
        margin-top: 20px;
        font-size: 14px;
        color: #9a9a9a;
      }
    }

    .none {
      display: none;
    }
    .display-item {
      display: block;
    }
  }
}
.fbox {
  display: flex;
  align-items: stretch;
}
.full-height {
  height: 100%;
}
.form-dialog {
  height: 100%;
  width: 100%;
  background-color: #fff;
  position: absolute;
  top: 0;
  .operateButton {
    position: absolute;
    right: 18px;
    top: 18px;
  }
  .categroy-list {
    margin-top: 14px;

    .factory-item {
      margin-right: 30px;
      .factory-name {
        height: 20px;
        line-height: 20px;
        font-size: 12px;

        font-weight: 500;
        color: rgba(41, 41, 41, 1);
      }
      .factory-category {
        margin-left: 10px;
        height: 20px;
        line-height: 20px;
        padding: 0 4px;
        background: rgba(99, 134, 193, 0.1);
        border-radius: 2px;
        font-size: 12px;

        font-weight: 500;
        color: rgba(99, 134, 193, 1);
      }
    }
  }
}
</style>
