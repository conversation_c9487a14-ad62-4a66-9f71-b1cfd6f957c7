<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config/index'

export default {
  components: {},
  data() {
    return {
      emptyMsg: '',
      //公司准入关系
      buyerPartnerFactoryRelationList: {},
      //表单详情
      buyerFormInstanceList: {},
      // 表单模板
      formTemplateArr: [],
      addDialogShow: false,
      pageConfig: pageConfig
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      }
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
      if (e.field == 'listCode') {
        this.addDialogShow = true
      }
    },
    handleAdd() {
      console.log('点击新增')
      this.addDialogShow = true
    },
    cancel() {
      this.addDialogShow = false
    },
    saveData() {}
  }
}
</script>
<style lang="scss" scoped></style>
