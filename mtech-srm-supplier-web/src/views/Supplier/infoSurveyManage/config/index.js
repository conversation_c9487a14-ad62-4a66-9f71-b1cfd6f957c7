import { i18n } from '@/main.js'
export const listColumnData = [
  {
    width: '150',
    field: 'listCode',
    headerText: i18n.t('表单编码'),
    cellTools: []
  },
  {
    width: '150',
    field: 'name',
    headerText: i18n.t('表单名称')
  },
  {
    width: '150',
    field: 'name',
    headerText: i18n.t('来源')
  },
  {
    width: '210',
    field: 'contactName',
    headerText: i18n.t('调查表类型')
  },
  {
    width: '210',
    field: 'contactPhone',
    headerText: i18n.t('客户编码')
  },
  {
    width: '150',
    field: 'name',
    headerText: i18n.t('客户名称')
  },
  {
    width: '150',
    field: 'name',
    headerText: i18n.t('状态')
  },
  {
    width: '150',
    field: 'name',
    headerText: i18n.t('创建日期')
  }
]

export const pageConfig = [
  {
    gridId: '52bb3639-f919-49aa-a212-d2637b037e81',
    title: i18n.t('处理中'),
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'Add',
            icon: 'icon_solid_edit',
            title: i18n.t('新增')
          },
          {
            id: 'change',
            icon: 'icon_solid_edit',
            title: i18n.t('发布')
          }
        ],
        ['Filter', 'refresh', 'setting']
      ]
    },
    grid: {
      lineSelection: true,
      columnData: listColumnData,
      asyncConfig: {
        url: '/supplier/tenant/buyer/survey/task/query' //采方
        // url: "/supplier/tenant/supplier/survey/task/query",  供方
      },
      dataSource: []
    }
  },
  {
    gridId: '85da2624-f529-47bb-b312-dcc009cb0b24',
    title: i18n.t('已完成'),
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [[], ['Filter', 'refresh', 'setting']]
    },
    grid: {
      lineSelection: true,
      columnData: listColumnData,
      dataSource: []
    }
  }
]
