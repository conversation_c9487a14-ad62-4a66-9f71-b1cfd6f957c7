<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <div class="toggle-tag" @click="isExpended = !isExpended">
            <span>{{ isExpended ? $t('收起') : $t('展开') }}</span>
            <i
              class="mt-icons mt-icon-icon_Sort_up"
              :style="isExpended ? '' : 'transform: rotate(180deg) scale(0.4)'"
            />
            <i
              class="mt-icons mt-icon-icon_Sort_up"
              :style="isExpended ? '' : 'transform: rotate(180deg) scale(0.4)'"
            />
          </div>
          <mt-form ref="ruleForm" :model="forecastTemplate">
            <mt-form-item prop="billCode" :label="$t('PPAP单号')">
              <mt-input
                v-model="forecastTemplate.billCode"
                type="text"
                :show-clear-button="true"
                :placeholder="$t('请输入模板编号')"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="billType" :label="$t('单据类型')">
              <mt-select
                v-model="forecastTemplate.billType"
                :data-source="typeList"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择状态')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="orgId" :label="$t('公司:')">
              <mt-select
                v-model="forecastTemplate.orgId"
                :data-source="companyList"
                :fields="{ text: 'orgName', value: 'id' }"
                :placeholder="$t('请选择公司')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <vxe-button
              class="e-flat1"
              status="info"
              icon="vxe-icon-search"
              size="mini"
              @click="submitEvent"
              >{{ $t('搜索') }}</vxe-button
            >
            <mt-form-item prop="supplierName" :label="$t('供应商名称：')" v-show="isExpended">
              <mt-input
                v-model="forecastTemplate.supplierName"
                type="text"
                :show-clear-button="true"
                :placeholder="$t('请输入供应商名称')"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="categoryName" :label="$t('品类')" v-show="isExpended">
              <mt-input
                v-model="forecastTemplate.categoryName"
                type="text"
                :show-clear-button="true"
                :placeholder="$t('请输入品类名称')"
              >
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="billStatus" :label="$t('单据状态')" v-show="isExpended">
              <mt-select
                ref="indexRef"
                v-model="forecastTemplate.billStatus"
                :data-source="billList"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择状态')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              class="form-item"
              :label="$t('创建日期')"
              label-style="top"
              prop="createDate"
              v-show="isExpended"
            >
              <mt-date-picker
                v-model="forecastTemplate.createDate"
                :show-clear-button="true"
                :placeholder="$t('选择生效日期')"
              ></mt-date-picker>
            </mt-form-item>
            <mt-form-item
              class="form-item"
              :label="$t('反馈截止日期')"
              label-style="top"
              prop="feedbackEndDate"
              v-show="isExpended"
            >
              <mt-date-time-picker
                v-model="forecastTemplate.feedbackEndDate"
                :placeholder="$t('选择反馈截止日期')"
                :show-clear-button="true"
              ></mt-date-time-picker>
            </mt-form-item>
          </mt-form>
          <div class="detail-content">
            <vxe-toolbar>
              <template #buttons>
                <vxe-button
                  @click="handleEditDimension"
                  status="primary"
                  icon="vxe-icon-edit"
                  size="mini"
                  >{{ $t('编辑') }}</vxe-button
                >
                <vxe-button
                  @click="handleSubmitDimension"
                  status="primary"
                  icon="vxe-icon-file-txt"
                  size="mini"
                  >{{ $t('提交') }}</vxe-button
                >
              </template>
            </vxe-toolbar>
            <ScTable
              ref="xTable"
              :loading="loading"
              :loading-config="{ icon: 'vxe-icon-indicator roll', text: '正在拼命加载中...' }"
              max-height="100%"
              :row-config="{ height: 38 }"
              :columns="columns"
              :table-data="tableData"
              :tree-config="{}"
              header-align="center"
              align="center"
              @checkbox-change="selectChangeEvent"
              @checkbox-all="checkboxChangeEvent"
            >
            </ScTable>
            <vxe-pager
              background
              :current-page.sync="pageConfig.current"
              :page-size.sync="pageConfig.size"
              :total="totalNum"
              @page-change="handlePageChange"
              :layouts="[
                'Total',
                'PrevJump',
                'PrevPage',
                'JumpNumber',
                'NextPage',
                'NextJump',
                'FullJump',
                'Sizes'
              ]"
            >
            </vxe-pager>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import ScTable from '@/components/ScTable/src/index'
import utils from '@/utils/utils'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      pageConfig: {
        current: 1,
        size: 20
      },
      totalNum: 0,
      loading: false,
      isExpended: false,
      companyList: [],
      selectedRow: [],
      forecastTemplate: {
        billCode: '',
        billStatus: null,
        billType: null,
        categoryName: '',
        createDate: '',
        feedbackEndDate: '',
        orgId: '',
        supplierName: ''
      },
      formData: {},
      tableData: [],
      typeList: [
        {
          text: this.$t('全新供应商'),
          value: 1
        },
        {
          text: this.$t('三新物料'),
          value: 2
        }
      ],
      billList: [
        {
          text: this.$t('待反馈'),
          value: 20
        },
        {
          text: this.$t('待审批'),
          value: 30
        },
        {
          text: this.$t('已驳回'),
          value: 40
        },
        {
          text: this.$t('已通过'),
          value: 50
        },
        {
          text: this.$t('已废弃'),
          value: 60
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 70
        },
        {
          field: 'billCode',
          title: i18n.t('PPAP单号'),
          width: 150,
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.codeClickEvent(row)
                  }}>
                  {row.billCode}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'templateName',
          title: i18n.t('PPAP模板')
        },
        {
          field: 'billType',
          title: i18n.t('单据类型'),
          width: 200,
          formatter: (data) => {
            if (data.row.billType == 1) {
              return i18n.t('全新供应商')
            } else if (data.row.billType == 2) {
              return i18n.t('三新物料')
            }
          }
        },
        {
          field: 'orgName',
          title: i18n.t('公司'),
          width: 260
        },
        {
          field: 'supplierName',
          title: i18n.t('供应商名称'),
          width: 260
        },
        {
          field: 'categoryName',
          title: i18n.t('品类'),
          width: 150
        },
        {
          field: 'createDate',
          title: i18n.t('建立日期'),
          width: 150
        },
        {
          field: 'feedbackEndDate',
          title: i18n.t('反馈截止日期'),
          width: 150
        },
        {
          field: 'createUserName',
          title: i18n.t('创建人'),
          width: 150
        },
        {
          field: 'billStatus',
          title: i18n.t('单据状态'),
          formatter: (data) => {
            if (data.row.billStatus == 10) {
              return i18n.t('新增')
            } else if (data.row.billStatus == 20) {
              return i18n.t('待反馈')
            } else if (data.row.billStatus == 30) {
              return i18n.t('待审批')
            } else if (data.row.billStatus == 40) {
              return i18n.t('已驳回')
            } else if (data.row.billStatus == 50) {
              return i18n.t('已通过')
            } else {
              return i18n.t('已废弃')
            }
          }
        }
      ]
    }
  },

  mounted() {
    this.getCompanyList()
    this.getFormDetail()
  },
  activated() {
    this.getFormDetail()
  },
  methods: {
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.pageConfig.current = currentPage
      this.pageConfig.size = pageSize
      this.getFormDetail()
    },
    // 获取公司列表
    getCompanyList() {
      const params = {
        orgCode: 'KT01',
        treeType: 0
      }
      this.$API.PPAPConfig.getCompanyList(params).then((res) => {
        this.companyList = res.data
      })
    },
    getFormDetail() {
      const params = {
        page: this.pageConfig,
        billCode: this.forecastTemplate.billCode,
        billStatus: this.forecastTemplate.billStatus,
        billType: this.forecastTemplate.billType,
        categoryName: this.forecastTemplate.categoryName,
        createDate: this.forecastTemplate.createDate
          ? utils.formateTime(this.forecastTemplate.createDate, 'yyyy-MM-dd')
          : '',
        feedbackEndDate: this.forecastTemplate.feedbackEndDate
          ? utils.formateTime(this.forecastTemplate.feedbackEndDate, 'yyyy-MM-dd hh:mm:ss')
          : '',
        orgId: this.forecastTemplate.orgId,
        supplierName: this.forecastTemplate.supplierName
      }
      this.loading = true
      this.$API.PPAPConfig.feedbackQuery(params).then((res) => {
        this.totalNum = Number(res.data.total)
        this.tableData = res.data.records
        this.loading = false
      })
    },
    selectChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 全选
    checkboxChangeEvent(row) {
      this.selectedRow = row.records
    },
    // 点击单据号
    codeClickEvent(row) {
      let query = {
        id: row.id
      }
      this.$router.push({
        path: '/supplier/sup/ppap-feedback-detail',
        query: query
      })
    },
    // 编辑
    handleEditDimension() {
      if (this.selectedRow.length !== 1) {
        this.$toast({
          content: this.$t('请先选择一行数据'),
          type: 'warning'
        })
        return
      }
      if (
        this.selectedRow[0].billStatus == 30 ||
        this.selectedRow[0].billStatus == 50 ||
        this.selectedRow[0].billStatus == 60
      ) {
        this.$toast({
          content: this.$t('单据状态不可编辑'),
          type: 'warning'
        })
        return
      }
      let query = {
        id: this.selectedRow[0].id
      }
      this.$router.push({
        path: '/supplier/sup/ppap-feedback-detail',
        query: query
      })
    },
    // 提交
    handleSubmitDimension() {
      if (this.selectedRow.length == 0) {
        this.$toast({
          content: this.$t('请先选择数据'),
          type: 'warning'
        })
        return
      }
      let billIds = []
      for (let i = 0; i < this.selectedRow.length; i++) {
        if (this.selectedRow[i].billStatus !== 20 && this.selectedRow[i].billStatus !== 40) {
          this.$toast({
            content: this.$t('单据不能提交'),
            type: 'warning'
          })
          return
        }
      }
      this.selectedRow.forEach((item) => {
        billIds.push(item.id)
      })

      let params = {
        billIds: billIds
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交数据？')
        },
        success: () => {
          this.$API.PPAPConfig.billInfoSubmit(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.getFormDetail()
              this.loading = false
            }
          })
        }
      })
    },
    // 查询
    submitEvent() {
      this.getFormDetail()
    }
  },
  watch: {}
}
</script>
<style lang="scss" scoped>
.toggle-tag {
  padding: 0 15px 8px 0;
  color: #2783fe;
  display: inline-block;
  font-size: 14px;
  position: relative;
  cursor: pointer;
  user-select: none;
  .mt-icons {
    font-size: 12px;
    position: absolute;
    transform: scale(0.4);
    top: -2px;
    left: 26px;
    &:nth-child(2) {
      top: 2px;
    }
  }
}
/deep/ .vxe-header--column {
  .vxe-resizable.is--line:before {
    width: 0px;
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  padding-top: 10px;
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(30% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-left: 20px;

    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
  }
  /deep/ .e-flat1 {
    position: absolute;
    top: 43px;
    right: 1%;
  }
  .detail-content {
    .vxe-toolbar {
      padding: 8px 0 0 8px;
    }
  }
}
.header-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
