<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <div style="position: relative; float: right">
            <vxe-toolbar>
              <template #buttons>
                <vxe-button
                  @click="backTo"
                  status="primary"
                  icon="vxe-icon-arrow-left"
                  size="mini"
                  >{{ $t('返回') }}</vxe-button
                >
                <vxe-button
                  @click="submitEvent"
                  status="primary"
                  icon="vxe-icon-save"
                  size="mini"
                  :disabled="
                    forecastTemplate.billStatus == 30 ||
                    forecastTemplate.billStatus == 50 ||
                    forecastTemplate.billStatus == 60
                  "
                  >{{ $t('保存') }}</vxe-button
                >
                <vxe-button
                  @click="saveAndSubmit"
                  status="primary"
                  icon="vxe-icon-send"
                  size="mini"
                  :disabled="
                    forecastTemplate.billStatus == 30 ||
                    forecastTemplate.billStatus == 50 ||
                    forecastTemplate.billStatus == 60
                  "
                  >{{ $t('保存并提交') }}</vxe-button
                >
              </template>
            </vxe-toolbar>
          </div>
          <mt-form ref="ruleForm" :model="forecastTemplate" :rules="rules">
            <mt-form-item prop="billCode" :label="$t('PPAP单号：')">
              <mt-input :disabled="true" v-model="forecastTemplate.billCode" type="text">
              </mt-input>
            </mt-form-item>
            <mt-form-item prop="billType" :label="$t('单据类型')">
              <mt-select
                v-model="forecastTemplate.billType"
                :disabled="true"
                :data-source="typeList"
                :fields="{ text: 'text', value: 'value' }"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="templateName" :label="$t('PPAP模板')">
              <mt-input
                v-model="forecastTemplate.templateName"
                :disabled="true"
                type="text"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="orgName" :label="$t('公司名称')">
              <mt-input v-model="forecastTemplate.orgName" :disabled="true" type="text"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')">
              <mt-input
                ref="indexRef"
                v-model="forecastTemplate.supplierName"
                :disabled="true"
                type="text"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="categoryName" :label="$t('品类')">
              <mt-input
                v-model="forecastTemplate.categoryName"
                :disabled="true"
                type="text"
              ></mt-input>
            </mt-form-item>
            <mt-form-item
              class="form-item"
              :label="$t('反馈截止时间')"
              label-style="top"
              prop="feedbackEndDate"
            >
              <mt-date-time-picker
                v-model="forecastTemplate.feedbackEndDate"
                :disabled="true"
              ></mt-date-time-picker>
            </mt-form-item>
            <mt-form-item prop="billStatus" :label="$t('单据状态')">
              <mt-select
                :disabled="true"
                v-model="forecastTemplate.billStatus"
                :data-source="billList"
                :fields="{ text: 'text', value: 'value' }"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')">
              <mt-input :disabled="true" v-model="forecastTemplate.createUserName" type="text">
              </mt-input>
            </mt-form-item>
            <mt-form-item
              prop="oaApprovalComments"
              :label="$t('OA审批意见')"
              v-show="forecastTemplate.billStatus == 40 || forecastTemplate.billStatus == 60"
            >
              <mt-input
                :disabled="true"
                v-model="forecastTemplate.oaApprovalComments"
                type="textarea"
                :placeholder="$t('无')"
              >
              </mt-input>
            </mt-form-item>
          </mt-form>
          <div class="detail-content">
            <ScTable
              ref="xTable"
              :loading="loading"
              :loading-config="{ icon: 'vxe-icon-indicator roll', text: '正在拼命加载中...' }"
              max-height="100%"
              :row-config="{ height: 38 }"
              :columns="columns"
              :table-data="tableData"
              header-align="center"
              :tree-config="{}"
              align="center"
            >
            </ScTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'
import utils from '@/utils/utils'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      pageConfig: {
        current: 1,
        size: 20
      },
      totalNum: 0,
      loading: false,
      forecastTemplate: {
        billCode: '', //单据编号
        billStatus: 0, //单据状态
        billType: 0, //单据类型
        categoryCode: '', //品类编码
        categoryId: '',
        categoryName: '',
        feedbackEndDate: '', //反馈截止时间
        oaApprovalComments: '', //OA审批意见
        orgCode: '', //组织编码
        orgId: '',
        orgName: '',
        supplierId: '',
        supplierName: '',
        supplierTeantId: '',
        supplierCode: '' //供应商编码
      },
      formData: {},
      tableData: [],
      billList: [
        {
          text: this.$t('新增'),
          value: 10
        },
        {
          text: this.$t('待反馈'),
          value: 20
        },
        {
          text: this.$t('待审批'),
          value: 30
        },
        {
          text: this.$t('已驳回'),
          value: 40
        },
        {
          text: this.$t('已通过'),
          value: 50
        },
        {
          text: this.$t('已废弃'),
          value: 60
        }
      ],
      typeList: [
        {
          text: this.$t('全新供应商'),
          value: 1
        },
        {
          text: this.$t('三新物料'),
          value: 2
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 90
        },
        {
          type: 'seq',
          title: i18n.t('序号'),
          width: 90
        },
        {
          field: 'subTemplateName',
          title: i18n.t('子模板'),
          width: 500,
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.modouleClickEvent(row)
                  }}>
                  {row.subTemplateName}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'narrative',
          title: i18n.t('描述'),
          width: 180
        },
        {
          field: 'uploadType',
          title: i18n.t('是否必须上传附件'),
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <div style='width:100%; display: inline-flex;justify-content: center;'>
                  <div style='width:100%; display: flex;justify-content: center;'>
                    <vxe-checkbox value={row.uploadType}></vxe-checkbox>
                  </div>
                </div>
              ]
            }
          }
        },
        {
          field: 'fileName',
          title: i18n.t('附件'),
          width: 350,
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <vxe-button
                  status='primary'
                  type='text'
                  size='mini'
                  onClick={() => {
                    this.fileClickEvent(row)
                  }}>
                  {row.fileName}
                </vxe-button>
              ]
            }
          }
        },
        {
          field: 'option',
          title: i18n.t('操作'),
          width: 150,
          slots: {
            default: ({ row }) => {
              return [
                <div style='width:100%;flex-direction: column; display: inline-flex;justify-content: space-between;'>
                  <div style='width:100%; display: flex;'>
                    <vxe-button
                      status='primary'
                      icon='mt-icons mt-icon-icon_list_edit'
                      type='text'
                      size='mini'
                      disabled={row.fileName !== '' || row.billStatus == 20 || row.billStatus == 50}
                      onClick={() => {
                        this.handleUpload(row)
                      }}>
                      上传
                    </vxe-button>
                    <vxe-button
                      status='primary'
                      icon='mt-icons mt-icon-icon_list_delete'
                      type='text'
                      size='mini'
                      disabled={row.fileName === '' || row.billStatus == 20 || row.billStatus == 50}
                      onClick={() => {
                        this.handleDelete(row)
                      }}>
                      删除
                    </vxe-button>
                  </div>
                </div>
              ]
            }
          }
        },
        {
          field: 'remark',
          title: i18n.t('备注')
        }
      ],
      rules: {}
    }
  },
  mounted() {
    if (this.$route.query.id) {
      this.getFormDetail(this.$route.query.id)
    }
  },
  methods: {
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.pageConfig.current = currentPage
      this.pageConfig.size = pageSize
      this.getTableList()
    },
    // 下载子模板
    codeClickEvent(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.subTemplateId
        })
        .then((res) => {
          download({
            fileName: data.subTemplateName,
            blob: res.data
          })
        })
    },
    // 下载子附件
    fileClickEvent(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.fileId
        })
        .then((res) => {
          download({
            fileName: data.fileName,
            blob: res.data
          })
        })
    },
    //获取模板详情数据
    getFormDetail(id) {
      this.loading = true
      const params = {
        page: this.pageConfig,
        billId: id
      }
      this.$API.PPAPConfig.feedbackDetailQuery(params).then((res) => {
        setTimeout(() => {
          res.data.detailsResponseList.forEach((item) => {
            if (item.uploadType == 1) {
              item.uploadType = true
            } else {
              item.uploadType = false
            }
          })
          this.tableData = res.data.detailsResponseList
          this.forecastTemplate = res.data
          this.loading = false
        }, 1000)
      })
    },
    // 点击子模板
    modouleClickEvent(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.subTemplateId
        })
        .then((res) => {
          download({
            fileName: data.subTemplateName,
            blob: res.data
          })
        })
    },
    handleUpload(row) {
      this.$dialog({
        modal: () => import('./components/uploadDialog.vue'),
        data: {
          fileData: [],
          isView: false, // 是否为预览
          required: false, // 是否必须
          title: this.$t('新增附件')
        },
        success: (res) => {
          this.handleUploadFiles(res, row.id)
        }
      })
    },
    handleUploadFiles(data, id) {
      this.tableData.forEach((item) => {
        if (item.id === id) {
          item.fileId = data.id
          item.fileSzie = data.fileSize
          item.fileUrl = data.url
          item.fileName = data.fileName
          item.remark = data.remark
        }
      })
    },
    handleDelete(row) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.tableData.forEach((item) => {
            if (item.id === row.id) {
              item.fileName = ''
              item.remark = ''
              item.fileId = ''
              item.fileUrl = ''
            }
          })
        }
      })
    },
    // 返回
    backTo() {
      this.$router.push({
        path: '/supplier/sup/ppap-feedback'
      })
    },
    // 保存
    submitEvent() {
      let detailsRequestList = this.tableData
      for (let i = 0; i < detailsRequestList.length; i++) {
        if (detailsRequestList[i].uploadType == true) {
          if (detailsRequestList[i].fileName == '') {
            this.$toast({
              content: this.$t('需要上传附件'),
              type: 'warning'
            })
            return
          } else {
            detailsRequestList[i].uploadType = 1
          }
        } else if (detailsRequestList[i].uploadType == false) {
          detailsRequestList[i].uploadType = 0
        }
      }
      let params = this.forecastTemplate
      params.detailsRequestList = this.tableData
      delete params.detailsResponseList
      params.feedbackEndDate = utils.formateTime(
        this.forecastTemplate.feedbackEndDate,
        'yyyy-MM-dd hh:mm:ss'
      )
      this.$API.PPAPConfig.billSupplierSave(params).then(() => {
        this.$toast({
          content: this.$t('保存成功'),
          type: 'success'
        })
        this.$router.push({
          path: '/supplier/sup/ppap-feedback'
        })
      })
    },
    // 提交并保存
    saveAndSubmit() {
      let detailsRequestList = this.tableData
      for (let i = 0; i < detailsRequestList.length; i++) {
        if (detailsRequestList[i].uploadType == true) {
          if (detailsRequestList[i].fileName == '') {
            this.$toast({
              content: this.$t('需要上传附件'),
              type: 'warning'
            })
            return
          } else {
            detailsRequestList[i].uploadType = 1
          }
        } else if (detailsRequestList[i].uploadType == false) {
          detailsRequestList[i].uploadType = 0
        }
      }
      let params = this.forecastTemplate
      params.detailsRequestList = this.tableData
      delete params.detailsResponseList
      params.feedbackEndDate = utils.formateTime(
        this.forecastTemplate.feedbackEndDate,
        'yyyy-MM-dd hh:mm:ss'
      )
      params.createDate = utils.formateTime(this.forecastTemplate.createDate, 'yyyy-MM-dd')
      this.$API.PPAPConfig.billSupplierSubimit(params).then(() => {
        this.$toast({
          content: this.$t('保存并提交成功'),
          type: 'success'
        })
        this.$router.push({
          path: '/supplier/sup/ppap-feedback'
        })
      })
    }
  },
  watch: {}
}
</script>
<style lang="scss" scoped>
/deep/ .vxe-header--column {
  .vxe-resizable.is--line:before {
    width: 0px;
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  padding-top: 10px;
  width: 100%;
  /deep/ .mt-form {
    margin-top: 50px;
    margin-left: 20px;
  }
  /deep/ .mt-form-item {
    width: calc(30% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;

    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
  }
  /deep/ .btn-tools {
    padding: 8px 0 0 8px;
  }
}
.header-box {
  width: 100%;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
