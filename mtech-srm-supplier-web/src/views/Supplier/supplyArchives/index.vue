<template>
  <div class="lifeCycle-container">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnDataMain } from './config/index.js'
export default {
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$hloading()
    })
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: '965ab2bd-50ba-4efe-baa1-b632edf632b8',
          toolbar: {
            useBaseConfig: false,
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          grid: {
            columnData: columnDataMain,
            asyncConfig: {
              url: '/supplier/tenant/supplier/partner/category/query'
            }
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    handleClickToolBar() {},
    handleClickCellTool() {},
    /**
     * 点击标题跳转
     */
    handleClickCellTitle() {
      // let { data } = e;
      // this.$router.push({
      //   name: "fileDetail",
      //   query: {
      //     partnerArchiveId: data.id,
      //     orgId: data.customerOrgId,
      //     orgName:data.customerName,
      //     supplierEnterpriseId: data.customerEnterpriseId,
      //     partnerRelationCode: data.partnerRelationCode,
      //     status: data.status,
      //   },
      // });
    }
  }
}
</script>

<style lang="scss" scoped>
.lifeCycle-container {
  width: 100%;
  height: 100%;
}
</style>
