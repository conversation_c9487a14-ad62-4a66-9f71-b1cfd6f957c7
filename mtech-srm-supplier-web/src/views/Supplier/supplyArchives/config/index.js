import { i18n } from '@/main.js'
export const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '210',
    field: 'customerCode',
    headerText: i18n.t('客户编码')
  },
  {
    width: '130',
    field: 'customerName',
    headerText: i18n.t('客户名称')
  },
  {
    width: '210',
    field: 'customerCategoryCode',
    headerText: i18n.t('品类编码'),
    cellTools: []
  },
  {
    width: '130',
    field: 'customerCategoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('品类状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('注册'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('潜在'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('新合格'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('临时'), cssClass: 'col-active' },
        { value: 10, text: i18n.t('合格'), cssClass: 'col-active' },
        { value: 11, text: i18n.t('预合格'), cssClass: 'col-active' },
        { value: 20, text: i18n.t('失效'), cssClass: 'col-inactive' },
        { value: 40, text: i18n.t('退出'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '100',
    field: 'modifyDate',
    headerText: i18n.t('状态更新时间'),
    searchOptions: {
      type: 'date',
      dateFormat: 'YYYY-mm-dd'
    }
  }
]
