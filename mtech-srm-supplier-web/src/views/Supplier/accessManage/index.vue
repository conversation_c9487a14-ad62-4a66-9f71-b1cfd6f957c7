<template>
  <div class="accessManage-container fbox">
    <mt-tabs
      :e-tab="false"
      overflow-mode="Popup"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="mian-content">
      <task-center v-if="selectIndex === 0" :id="id" :org-id="orgId"></task-center>
      <access-process v-if="selectIndex === 1" :id="id" :org-id="orgId"></access-process>
      <SupplierSelfReview v-if="selectIndex === 2" />
      <PreReviewInvestigation v-if="selectIndex === 3" />
    </div>
  </div>
</template>

<script>
import TaskCenter from './components/taskCenter.vue'
import AccessProcess from './components/accessProcess.vue'
import SupplierSelfReview from './components/supplierSelfReview.vue'
import PreReviewInvestigation from './components/preReviewInvestigation.vue'
export default {
  components: {
    TaskCenter,
    AccessProcess,
    SupplierSelfReview,
    PreReviewInvestigation
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('任务中心')
        },
        {
          title: this.$t('准入进展')
        },
        {
          title: this.$t('供应商自查')
        },
        {
          title: this.$t('预审调查表')
        }
      ],
      id: '',
      orgId: '',
      info: {}
    }
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },
    handleSelectTab(index) {
      this.selectIndex = index
    }
  },
  created() {
    let id = this.$route.query.id
    let orgId = this.$route.query.orgId
    this.id = id
    this.orgId = orgId
  }
}
</script>

<style lang="scss">
.mt-tabs {
  // margin-top: 10px;
  width: 100%;
}
</style>

<style lang="scss" scoped>
.fbox {
  display: flex;
  align-items: stretch;
}

.flex1 {
  flex: 1;
}

.accessManage-container {
  flex-direction: column;
  min-height: 100%;
  min-height: 100vh;
  position: absolute;
  z-index: 99;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(#fafafa, #fafafa), linear-gradient(#000, #000);

  min-width: 1200px;

  .mian-content {
    flex: 1;
  }
}
</style>
