<template>
  <div>
    <div class="submit-btn" @click="proSubmitTask()">
      <template v-if="[10, 30, 60].includes(formInfo.status)">{{ $t('提交') }}</template>
    </div>
    <div class="task-center fbox">
      <div class="task-sidebar">
        <!-- <div class="search-box">
        <i class="mt-icons mt-icon-MT_Search"></i>
        <ejs-autocomplete
          id="orgName"
          :fields="{
            text: 'customerEnterpriseName',
            value: 'customerEnterpriseName',
          }"
          :data-source="orgNameArray"
          css-class="orgName"
          data-name="orgName"
          :width="240"
          :placeholder="$t('搜索公司')"
          :show-popup-button="true"
          v-model="orgName"
          @select="selectItem"
        ></ejs-autocomplete>
      </div> -->

        <div class="task-collapse">
          <div v-for="(taskItem, index) of fullTaskIndexArr" :key="taskItem">
            <div class="collapse-header" :class="{ active: index === 0 }">
              {{ taskItem }} ({{ fullTaksArr[index] | noFilterStatus10 }})
            </div>
            <!-- 供方的不会有公司品类的展开 采方的是 公司品类 需要一个抽屉展开 -->
            <div class="collapse-content">
              <template v-for="item of fullTaksArr[index]">
                <mt-collapse
                  :accordion="false"
                  :has-arr="true"
                  :key="item.customerEnterpriseCode"
                  v-model="activeCompanyId"
                >
                  <mt-panel :name="item.customerEnterpriseCode + '_' + index">
                    <div slot="header" class="accordion-header">
                      <span
                        class="company-name"
                        :alt="item.customerEnterpriseName"
                        :title="item.customerEnterpriseName"
                        >{{ item.customerEnterpriseName }}</span
                      >
                      <span class="header-number"
                        >({{ item.supplierTaskList | noFilterStatus102 }})</span
                      >
                    </div>
                    <div slot="content">
                      <div
                        class="sub-item"
                        v-for="childItem of item.supplierTaskList"
                        :key="childItem.id"
                        @click="selectTask(childItem, index)"
                        :class="{
                          active: activeIndexId === childItem.id,
                          grey:
                            childItem.status === 30 ||
                            childItem.status === 40 ||
                            childItem.status === 50
                        }"
                      >
                        <div
                          class="task-name ellipsis"
                          :alt="childItem.taskName"
                          :title="childItem.taskName"
                        >
                          {{ childItem.taskName }}
                        </div>
                        <div class="sub-tips">
                          {{ mapState[childItem.status] }}
                        </div>
                      </div>
                    </div>
                  </mt-panel>
                </mt-collapse>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="task-content flex1">
        <div class="task-info fbox">
          <div class="mian-info flex1">
            <div class="task-list fbox">
              <span>{{ $t('调查表名称：') }}{{ supplierTaskInstance.taskName || '--' }}</span>
              <!-- <span>调查表类型：企业信息调查表</span> -->
              <span
                >{{ $t('调查表填写方：')
                }}{{
                  supplierTaskInstance && supplierTaskInstance.taskOwner === 1
                    ? $t('供方')
                    : supplierTaskInstance && supplierTaskInstance.taskOwner === 0
                    ? $t('采方')
                    : '--'
                }}</span
              >
              <span>{{ $t('邀请人：') }}{{ supplierTaskInstance.inviterName || '--' }}</span>
              <span>{{ $t('邀请时间：') }}{{ supplierTaskInstance.createTime || '--' }}</span>
              <!-- 待审批 的驳回通过放入 审批中心去做了 此处不展示 -->
              <span class="reject-reason" v-if="formInfo.status === 20">{{
                $t('该调查表已发送至相关人员的审批中心等待审批')
              }}</span>
            </div>
            <div class="categroy-list fbox" v-if="supplierPartnerRelationList.length > 0">
              <div
                class="factory-item fbox"
                v-for="item of supplierPartnerRelationList"
                :key="item.id"
              >
                <span class="factory-name"
                  >{{ item.enterpriseName }}-{{ item.customerFactoryName }}</span
                >
                <template
                  v-if="
                    !!item.supplierPartnerCategoryRelationList &&
                    item.supplierPartnerCategoryRelationList.length > 0
                  "
                >
                  <span
                    class="factory-category"
                    v-for="cItem of item.supplierPartnerCategoryRelationList"
                    :key="cItem.id"
                    >{{ cItem.customerCategoryName }}</span
                  >
                </template>
              </div>
            </div>
            <div class="categroy-list fbox" style="width: 100%">
              <div class="factory-item fbox" style="width: 100%">
                <span class="factory-name">{{ $t('审核意见：') }}</span>
                <mt-radio
                  v-model="supplierTaskInstance.approveStatus"
                  :data-source="[
                    {
                      label: this.$t('批准'),
                      value: 40
                    },
                    {
                      label: this.$t('驳回'),
                      value: 30
                    }
                  ]"
                  disabled
                ></mt-radio>
                <textarea
                  v-model="supplierTaskInstance.approveOpinion"
                  disabled
                  :multiline="true"
                  :rows="3"
                  float-label-type="Never"
                  style="flex: 1"
                ></textarea>
              </div>
            </div>
          </div>
          <div class="btn-box fbox">
            <!-- <template v-if="formInfo.status === 30 && !!supplierTaskInstance.auditRemark">
              <mt-tooltip :content="supplierTaskInstance.auditRemark" target="#box">
                <div class="err-tips" id="box">
                  <i class="mt-icons mt-icon-MT_info"></i>{{ $t('驳回原因') }}
                </div>
              </mt-tooltip>
            </template> -->

            <template v-if="[10, 30, 60].includes(formInfo.status)">
              <div class="normal-btn" @click="saveTaskAjax()">{{ $t('保存') }}</div>
              <!-- <div class="normal-btn" @click="proSubmitTask()">
              <template v-if="formInfo.status === 10">{{ $t('提交') }}</template>
              <template v-if="formInfo.status === 30">{{ $t('重新提交') }}</template>
            </div> -->
            </template>
            <!-- <template v-if="formInfo.status === 30 && !!supplierTaskInstance.auditRemark">
              <mt-tooltip :content="supplierTaskInstance.auditRemark" target="#box">
                <div class="err-tips" id="box">
                  <i class="mt-icons mt-icon-MT_info"></i>{{ $t('驳回原因') }}
                </div>
              </mt-tooltip>
            </template> -->
            <template v-if="isRelationTab && formInfo.status === 40">
              <div class="normal-btn" @click="changeTask()">
                {{ $t('信息变更') }}
              </div>
            </template>
            <!-- <template v-if="formInfo.status === 20">
            <div class="normal-btn" @click="refuseTask()">{{ $t("驳回") }}</div>
            <div class="normal-btn" @click="aggressiveTask()">{{ $t("通过") }}</div>
          </template> -->
          </div>
        </div>
        <div class="task-form fbox">
          <div class="form-content flex1" ref="formContent">
            <template v-if="formTemplateArr.length > 0">
              <div
                v-for="item of formTemplateArr"
                :key="item.id"
                class="display-item"
                :ref="'formItem_' + item.id"
                :data-id="item.id"
              >
                <!--class="none" :class="{'display-item': activeForm.id === item.id}"-->
                <div class="parse-title">{{ item.name }}</div>
                <mt-parser :ref="`parser_${item.id}`" :form-conf="item.value" />
                <!-- <mt-parser ref="parser1" :form-conf="item.value" /> -->
              </div>
            </template>
            <template v-else>
              <div class="empty-box">{{ $t('暂无表单定义数据') }}</div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MtRadio from '@mtech-ui/radio'

import mtCollapse from '../../../../components/collapse/collapse.vue'
import mtPanel from '../../../../components/collapse/panel.vue'
import Parser from '@mtech-form-design/form-parser'
import utils from '@/utils/utils.js'
import dayjs from 'dayjs'
// polyfill for scrollIntoView
import smoothscroll from 'smoothscroll-polyfill'
// kick off the polyfill!
smoothscroll.polyfill()

// 表单tab后面的数据
let FORMLISTDATA = new Map()

export default {
  components: {
    'mt-collapse': mtCollapse,
    'mt-panel': mtPanel,
    'mt-parser': Parser,
    MtRadio
  },
  filters: {
    // 遍历二级数组下 status为10 待填写 的数量
    filterStatus10(dataArr) {
      console.log('11111', dataArr)
      let dataNum = 0
      dataArr.forEach((dItem) => {
        let filterData =
          !!dItem.supplierTaskList && dItem.supplierTaskList.filter((cv) => cv.status === 10).length
        dataNum = dataNum + filterData
      })
      return dataNum
    },
    noFilterStatus10(dataArr) {
      let dataNum = 0
      dataArr.forEach((dItem) => {
        let filterData = !!dItem.supplierTaskList && dItem.supplierTaskList.length
        dataNum = dataNum + filterData
      })
      return dataNum
    },
    filterStatus102(dataArr) {
      let dataNum = !!dataArr && dataArr.filter((cv) => cv.status === 10).length
      return dataNum
    },
    noFilterStatus102(dataArr) {
      let dataNum = !!dataArr && dataArr.length
      return dataNum
    },
    filterStatus(value) {
      let statusTxt = ''
      switch (value) {
        case 10:
          statusTxt = this.$t('待填写')
          break
        case 20:
          statusTxt = this.$t('待审批')
          break
        case 30:
          statusTxt = this.$t('已驳回')
          break
        case 40:
          statusTxt = this.$t('已完成')
          break
        case 50:
          statusTxt = this.$t('已关闭')
          break
        case 60:
          statusTxt = this.$t('待提交')
          break
        default:
          statusTxt = '--'
      }
      return statusTxt
    }
  },
  props: {
    // companyInfo: {
    //   type: Object,
    //   default: () => {},
    // },
    isRelationTab: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    // orgName(nv) {
    //   if (!nv || utils.isEmpty(nv)) {
    //     this.initTasks();
    //   }
    // },
    // companyInfo: {
    //   handler(nv) {
    //     if (!!nv && !!nv.customerEnterpriseName && !!nv.customerEnterpriseId) {
    //       this.orgName = nv.customerEnterpriseName;
    //       this.initTasks(nv.customerEnterpriseId);
    //     }
    //   },
    //   immediate: true,
    //   deep: true,
    // },
  },

  data() {
    return {
      displayOperation: false,
      orgName: '',
      orgNameArray: [],
      supplyId: null,
      mapState: {
        10: this.$t('待填写'),
        20: this.$t('待审批'),
        30: this.$t('已驳回'),
        40: this.$t('已完成'),
        50: this.$t('已关闭'),
        60: this.$t('待提交')
      },

      activeIndexId: -1,
      oldactiveIndexId: -1,
      activeCompanyId: -1,
      activeForm: {},
      // 全部的 待办数据
      // fullTaskIndexArr: [this.$t('供方待办'), this.$t('客户待办'), this.$t('全部任务')],
      fullTaskIndexArr: [this.$t('待填写任务'), this.$t('待修改任务'), this.$t('已完成任务')],
      fullTaksArr: [[], [], []],
      // 表单的详情
      formInfo: {},

      // 我的待办
      pengingData: [],
      // 供方待办
      buyerTasks: [],
      // 全部待办
      historyData: [],
      // 任务详情
      taskDetail: {},

      // 任务详情
      supplierTaskInstance: {},
      //表单详情
      supplierFormInstanceList: {},
      //公司准入关系
      supplierPartnerRelationList: [],

      // 表单模板
      formTemplateArr: [],

      oldValue: {},
      initData: {}, //切换完初始数据
      editFlag: false,
      isGroupTask: false, // 是否为集团级类型的task

      groupLevelList: [
        'S_BASIC_BASE',
        'S_FINANCE_BASE',
        'S_BASIC_BASE_EXT',
        'S_BASIC_SHAREHOLDING',
        'S_CERTIFICATE_CREDIT'
      ], // 集团级基本信息字典
      supplierInfo: {}, // 企业信息
      taxCreditInfo: {}, // 纳税信用信息
      baseInfo: {}, // 企业基本信息
      profile: {}, // 企业简介
      shareholderInfo: {} // 股东信息
    }
  },
  created() {
    // this.queryCustomers()
    this.getBusinessEnterpriseInformation()
    this.getTaxRating()
    this.getBaseInfo()
    this.getProfile()
    this.getHolder()
  },
  mounted() {
    let supplyId = this.$route.query.id
    this.supplyId = supplyId
    // this.initTasks();
    let customerEnterpriseId = this.$route.query.customerEnterpriseId
    this.orgName = this.$route.query.customerEnterpriseName
    if (customerEnterpriseId) {
      this.initTasks(customerEnterpriseId)
    } else {
      this.initTasks()
    }
  },
  methods: {
    // 初始化待办任务 全部任务
    initTasks(companyId = '') {
      this.$loading()

      // 采方待办任务查询
      const getPendingTasks = this.getPendingTasks(companyId)
      // 供方待办任务查询
      const getBuyerTasks = this.getBuyerTasks(companyId)
      // 全部任务查询
      const gethistory = this.gethistory(companyId)

      Promise.all([getPendingTasks, getBuyerTasks, gethistory]).then((result) => {
        this.fullTaksArr = result
        console.log('this.fullTaksArr', this.fullTaksArr)
        this.pengingData = result[0]
        this.buyerTasks = result[1]
        this.historyData = result[2]

        // 获取默认展开项目 获取对应的详情
        let formInfo = {}
        let defaultCompanyId = -1
        let defaultId = -1
        let selectIndex = 0
        for (let index in result) {
          if (!utils.isEmpty(result[index]) && defaultId === -1) {
            for (let cItem of result[index]) {
              if (!utils.isEmpty(cItem.supplierTaskList)) {
                defaultCompanyId = cItem.customerEnterpriseCode + '_' + index // 增加唯一性
                formInfo = cItem.supplierTaskList[0]
                defaultId = cItem.supplierTaskList[0].id
                selectIndex = index
                break
              } else {
                continue
              }
            }
          } else {
            continue
          }
        }
        // 默认展开
        if (!utils.isEmpty(formInfo)) {
          this.activeIndexId = formInfo.id
          this.activeCompanyId = defaultCompanyId
          this.$hloading()
          this.selectTask(formInfo, selectIndex)
        } else {
          this.$hloading()
        }
      })
    },
    refreshTasks(companyId = '') {
      this.$loading()
      // 采方待办任务查询
      const getPendingTasks = this.getPendingTasks(companyId)
      // 供方待办任务查询
      const getBuyerTasks = this.getBuyerTasks(companyId)
      // 全部任务查询
      const gethistory = this.gethistory(companyId)
      Promise.all([getPendingTasks, getBuyerTasks, gethistory]).then((result) => {
        this.fullTaksArr = result
        this.pengingData = result[0]
        this.buyerTasks = result[1]
        this.historyData = result[2]
        this.$hloading()
      })
    },

    // 根据企业ID获取中国工商企业信息
    getBusinessEnterpriseInformation() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.$API.customerAccess
        .getBusinessEnterpriseInformationApi({ id: userInfo.enterpriseId })
        .then((res) => {
          if (res.code === 200) {
            this.supplierInfo = res.data?.result
          }
        })
    },
    // 根据企业ID获取中国工商企业纳税评级信息
    getTaxRating() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.$API.customerAccess.getTaxRatingApi({ id: userInfo.enterpriseId }).then((res) => {
        if (res.code === 200) {
          this.taxCreditInfo = res.data?.result?.items[0]
        }
      })
    },
    // 根据企业ID获取企业基本信息
    getBaseInfo() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.$API.customerAccess.getBaseInfoApi({ id: userInfo.enterpriseId }).then((res) => {
        if (res.code === 200) {
          this.baseInfo = res.data?.result
        }
      })
    },
    // 根据企业ID获取企业简介
    getProfile() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.$API.customerAccess.getProfileApi({ id: userInfo.enterpriseId }).then((res) => {
        if (res.code === 200) {
          this.profile = res.data?.result
        }
      })
    },
    // 根据企业ID获取企业股东信息
    getHolder() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.$API.customerAccess.getHolderApi({ id: userInfo.enterpriseId }).then((res) => {
        if (res.code === 200) {
          this.shareholderInfo = res.data?.result?.items.map((v) => {
            return {
              stockHolder: v.name,
              shareholdingRatio: parseFloat(v.capital[0].percent),
              // beneficialShares: parseFloat(v.capital[0].percent),
              capitalContributionMoney: parseFloat(v.capital[0].amomon),
              capitalContributionDate: dayjs(v.capital[0].time).valueOf()
            }
          })
        }
      })
    },

    // 搜索选择
    selectItem(e) {
      let { itemData } = e
      this.orgName = itemData.customerEnterpriseName

      this.initTasks(itemData.customerEnterpriseId)
      console.log(itemData)
    },

    isEmpty(value) {
      return utils.isEmpty(value)
    },

    getPendingTasks() {
      return new Promise((resolve) => {
        // 【待填写任务】：展示全新触发的“待填写”状态及、“待提交”及“待审核”状态的调查表；
        this.$API.customerAccess
          .queryQuestionnaire({ stageTaskStatus: [10, 20, 60] })
          .then((res) => {
            let { code, data } = res
            if (code === 200 && !utils.isEmpty(data)) {
              resolve(data)
            } else {
              resolve([])
            }
          })
          .catch(() => {
            resolve([])
          })
      })
    },

    getBuyerTasks() {
      return new Promise((resolve) => {
        // 【待修改任务】：展示被采方驳回的“已驳回”状态的调查表，修改“已驳回”状态的调查表并保存后，调查表的状态变为“待提交”，提交后变为“待审核”；
        this.$API.customerAccess
          .queryQuestionnaire({ stageTaskStatus: [30] })
          .then((res) => {
            let { code, data } = res
            if (code === 200 && !utils.isEmpty(data)) {
              resolve(data)
            } else {
              resolve([])
            }
          })
          .catch(() => {
            resolve([])
          })
      })
    },

    gethistory() {
      return new Promise((resolve) => {
        // 【已完成任务】：展示“已完成”状态的调查表；
        this.$API.customerAccess
          .queryQuestionnaire({ stageTaskStatus: [40] })
          .then((res) => {
            let { code, data } = res
            if (code === 200 && !utils.isEmpty(data)) {
              resolve(data)
            } else {
              resolve([])
            }
          })
          .catch(() => {
            resolve([])
          })
      })
    },

    // 关键字查询公司 前端筛选
    queryCustomers(orgName = '') {
      this.$API.customerAccess.queryCustomers({ orgName: orgName }).then((res) => {
        let { code, data } = res
        if (code === 200 && !utils.isEmpty(data)) {
          this.orgNameArray = data
        } else {
          this.orgNameArray = []
        }
      })
    },

    switchTask(formInfo, index) {
      this.editFlag = formInfo.status == 40

      // 是否展示操作按钮  采方待办 也就是第一个下标的 展示
      if (Number(index) === 0) {
        this.displayOperation = true
      } else {
        this.displayOperation = false
      }

      this.formInfo = formInfo
      let { id } = formInfo

      // 默认展开
      this.activeIndexId = formInfo.id

      // 重置掉表单
      this.formTemplateArr.length = 0
      FORMLISTDATA.clear()

      this.$loading()
      // 兜底去除loading
      setTimeout(() => {
        this.$hloading()
      }, 1000)

      this.$API.customerAccess
        .getTaskDetail({ taskInstanceId: id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            this.$hloading()
            // 任务详情 表单详情 公司准入关系
            const { supplierTaskInstance, supplierFormInstanceList, supplierPartnerRelation } = data

            this.supplierTaskInstance = supplierTaskInstance
            this.supplierFormInstanceList = supplierFormInstanceList
            this.supplierPartnerRelationList =
              this.renderSupplierPartnerRelation(supplierPartnerRelation)

            this.$nextTick(() => {
              setTimeout(() => {
                this.saveOldData()
                this.oldactiveIndexId = formInfo.id
              }, 1000)
            })
          } else {
            this.$hloading()
            // this.$toast({
            //   content: "获取任务详情失败，请重试!",
            //   type: "warning"
            // })
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg || this.$t('获取任务详情失败，请重试!'),
            type: 'warning'
          })
          this.oldValue = {}
          this.oldactiveIndexId = formInfo.id
        })

      this.getAllFormDefine()
    },

    // 选择对应的调查表任务 任务id
    async selectTask(formInfo, index) {
      this.isGroupTask = formInfo.taskName.includes('集团级')
      let realTimeData = await this.getAllDate()
      if (
        this.formTemplateArr.length &&
        JSON.stringify(this.initData) !== JSON.stringify(realTimeData)
      ) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('填写的内容还未保存，切换后会丢失，确认切换吗？')
          },
          success: () => {
            this.switchTask(formInfo, index)
          }
        })
      } else {
        this.switchTask(formInfo, index)
      }
    },

    async saveOldData() {
      this.initData = JSON.parse(JSON.stringify(await this.getAllDate()))
    },

    getAllDate() {
      return new Promise((resolve, reject) => {
        let formDataArr = []
        this.$nextTick(function () {
          if (this.formTemplateArr.length) {
            this.$refs[`parser_${this.formTemplateArr[0].id}`][0].getFormData()
            // “行内编辑在不脱离编辑状态的情况下直接点右上角保存”
            // “循环调用组件方法脱离编辑状态拿到编辑中的数据”
            this.formTemplateArr.forEach((e) => {
              let parseName = `parser_${e.id}`
              let _formData = this.getData(parseName)
              _formData
                .then((res) => {
                  formDataArr.push({
                    formData: res,
                    formInstanceId: e.id
                  })
                  // “if条件是用来判断有没有遍历完动态表单的，满足if条件代表遍历了，else一定会走，不用管”
                  if (formDataArr.length === this.formTemplateArr.length) {
                    resolve(formDataArr)
                  }
                })
                .catch(() => {
                  reject({})
                })
            })
          } else {
            resolve({})
          }
        })
      })
    },

    async getAllFormDefine() {
      let { id } = this.formInfo
      const formAllDetail = await this.queryAllFormData()
      if (FORMLISTDATA.has(id)) {
        // 存入表单数据
        this.$nextTick(() => {
          if (!utils.isEmpty(formAllDetail) && formAllDetail.length > 0) {
            formAllDetail.forEach((detail) => {
              if (!utils.isEmpty(detail) && !utils.isEmpty(detail.formData)) {
                setTimeout(() => {
                  this.$refs[`parser_${detail.formInstanceId}`][0].setFormData(detail.formData)
                }, 1000)
              }
            })
          }
        })
        this.$hloading()
        return
      }
      this.$API.customerAccess.getAllFormDefine({ taskInstanceId: id }).then((res) => {
        let { code, data } = res
        if (code === 200 && !utils.isEmpty(data)) {
          // 非可编辑的表单 循环disabled
          if (data.length > 0) {
            data.forEach((tmpItem) => {
              if (
                !utils.isEmpty(tmpItem) &&
                !utils.isEmpty(tmpItem.formDefineResponse) &&
                !utils.isEmpty(tmpItem.formDefineResponse.template)
              ) {
                let template = this.disableTemplate(tmpItem.formDefineResponse.template)
                this.formTemplateArr.push({
                  id: tmpItem.id,
                  value: template,
                  name: tmpItem.formName,
                  formTemplateType: tmpItem.formTemplateType
                })
              }
            })
            // 缓存当前表单结构数据
            FORMLISTDATA.set(id, this.formTemplateArr)

            this.$nextTick(() => {
              // 存入表单数据
              if (!utils.isEmpty(formAllDetail) && formAllDetail.length > 0) {
                formAllDetail.forEach((detail) => {
                  if (!utils.isEmpty(detail) && !utils.isEmpty(detail.formData)) {
                    setTimeout(() => {
                      this.$refs[`parser_${detail.formInstanceId}`][0].setFormData(detail.formData)
                    }, 1000)
                  }
                })
              }
              this.setGroupLevelInfo()
              this.$hloading()
            })
            this.saveOldData()
          }
        } else {
          this.$hloading()
          this.$toast({
            content: this.$t('获取表单定义模板失败，请重试!'),
            type: 'warning'
          })
        }
      })
    },

    setGroupLevelInfo() {
      if ([10, 30, 60].includes(this.formInfo.status)) {
        this.groupLevelList.forEach((key) => {
          let id = this.formTemplateArr.find((v) => v.formTemplateType === key)?.id
          if (id) {
            let obj = this.$refs[`parser_${id}`][0].getFormData()
            if (key === 'S_BASIC_BASE') {
              this.$refs[`parser_${id}`][0].setFormData({
                supplierShortName: obj?.supplierShortName || this.supplierInfo?.alias,
                establishmentTime:
                  obj?.establishmentTime || Number(this.supplierInfo?.estiblishTime),
                countryRegion: obj?.countryRegion || 'CN',
                province: obj?.province || this.supplierInfo?.areaCode,
                city: obj?.city || this.supplierInfo?.city + '/' + this.supplierInfo?.district,
                detailAddress: obj?.detailAddress || this.supplierInfo?.regLocation,
                enterpriseEmail: obj?.enterpriseEmail || this.baseInfo?.email,
                enterpriseNature: obj?.enterpriseNature,
                enterprisePhone: obj?.enterprisePhone || this.baseInfo?.phoneNumber,
                factoryArea: obj?.factoryArea,
                fax: obj?.fax,
                industryBelong: obj?.industryBelong,
                industryEnterprise: obj?.industryEnterprise,
                industryRank: obj?.industryRank,
                website: obj?.website || this.baseInfo?.websiteList,
                zipCode: obj?.zipCode,
                legalRepresentative: obj?.legalRepresentative || this.supplierInfo?.legalPersonName,
                taxNumber: obj?.taxNumber || this.supplierInfo?.taxNumber
              })
            }
            if (key === 'S_FINANCE_BASE') {
              this.$refs[`parser_${id}`][0].setFormData({
                registeredCapital:
                  obj?.registeredCapital || this.supplierInfo?.regCapital.match(/\d+/g)
                    ? this.supplierInfo?.regCapital.match(/\d+/g)[0]
                    : null,
                paidInCapital:
                  obj?.paidInCapital || this.supplierInfo?.actualCapital.match(/\d+/g)
                    ? this.supplierInfo?.actualCapital.match(/\d+/g)[0]
                    : null,
                taxCreditEvaluation: obj?.taxCreditEvaluation || this.taxCreditInfo?.grade,
                fixedAssets: obj?.fixedAssets,
                totalAnnualSalesOne: obj?.totalAnnualSalesOne,
                totalAnnualSalesTwo: obj?.totalAnnualSalesTwo,
                totalAnnualSalesThree: obj?.totalAnnualSalesThree,
                workingCapital: obj?.workingCapital
              })
            }
            if (key === 'S_BASIC_BASE_EXT') {
              this.$refs[`parser_${id}`][0].setFormData({
                businessScope:
                  obj?.businessScope || this.supplierInfo?.businessScope?.substring(0, 2000),
                controlledByCompetitors: obj?.controlledByCompetitors,
                enterpriseProfile: obj?.enterpriseProfile || this.profile?.substring(0, 2000),
                hasResignedEmployee: obj?.hasResignedEmployee,
                industryAdvantage: obj?.industryAdvantage,
                mainCustomerName: obj?.mainCustomerName,
                mainCustomerName2: obj?.mainCustomerName2,
                mainCustomerName3: obj?.mainCustomerName3,
                mainCustomerNumber: obj?.mainCustomerNumber,
                productProfile: obj?.productProfile
              })
            }
            if (key === 'S_BASIC_SHAREHOLDING') {
              this.$refs[`parser_${id}`][0].setFormData({
                synergyPartnerOwnershipStructureList:
                  obj?.synergyPartnerOwnershipStructureList.length !== 0
                    ? obj?.synergyPartnerOwnershipStructureList
                    : this.shareholderInfo
              })
            }
            if (key === 'S_CERTIFICATE_CREDIT') {
              this.$refs[`parser_${id}`][0].setFormData({
                expireDate: obj?.expireDate || Number(this.supplierInfo?.toTime),
                licenseAttachment: obj?.licenseAttachment
              })
            }
          }
        })
      }
    },

    // 获取all 表单生成器数据
    queryAllFormData() {
      let { id } = this.formInfo
      return this.$API.customerAccess
        .queryAllFormData({ taskInstanceId: id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            return data
          } else {
            return {}
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'warning'
          })
          this.$hloading()
        })
    },

    // 表单滚动
    scrollInto(id) {
      this.$refs['formItem_' + id][0].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    },

    renderSupplierPartnerRelation(supplierPartnerRelation) {
      let tmpArr = []
      let tmpObj = {}

      !!supplierPartnerRelation.supplierPartnerFactoryRelationDTOList &&
        supplierPartnerRelation.supplierPartnerFactoryRelationDTOList.length > 0 &&
        supplierPartnerRelation.supplierPartnerFactoryRelationDTOList.forEach((cv) => {
          tmpObj['enterpriseName'] = supplierPartnerRelation.enterpriseName
          tmpObj['customerFactoryName'] = cv.customerFactoryName
          tmpObj['supplierPartnerCategoryRelationList'] = cv.supplierPartnerCategoryRelationList
          tmpArr.push(tmpObj)
        })

      return tmpArr
    },

    /**
     * (formInfo.status === 10 || formInfo.status === 30) && displayOperation
     * disable不能操作的表单结构
     */
    disableTemplate(template) {
      if ([10, 30, 60].includes(this.formInfo.status)) {
        return template
      } else {
        !!template.fields &&
          template.fields.forEach((tItem) => {
            tItem.disabled = true
          })
        return template
      }
    },

    // 获取表单生成器数据
    queryFormDataDetail(defaultForm) {
      return this.$API.customerAccess
        .queryFormData({ formInstanceId: defaultForm.id })
        .then((res) => {
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            return data
          } else {
            return {}
          }
        })
    },

    // 保存的接口请求
    saveTaskAjax() {
      // 校验表单的promise
      let promiseValidataArr = []
      let companyContactorDataSource = []
      for (let va of this.formTemplateArr) {
        let parseName = `parser_${va.id}`
        promiseValidataArr.push(this.$refs[parseName][0].valiteFormData())
        if (va.formTemplateType === 'S_BASIC_CONTACT_INSIDE') {
          const tableVal = this.$refs[parseName][0].getFormData()
          companyContactorDataSource = tableVal?.synergyPartnerContactInsideList || []
        }
      }
      if (companyContactorDataSource.length) {
        const res = companyContactorDataSource.some((item) => {
          const unAbleTypeList = [
            'businessMan',
            'businessLeader',
            'qualityMan',
            'qualityLeader',
            'viceGeneralManager',
            'generalManager'
          ]
          return unAbleTypeList.includes(item.contactPost)
        })
        if (!res) {
          this.$toast({
            content: this.$t(
              '基本资料-联系人-公司级 的联系人至少要有一位是以下几种职务：业务、业务负责人、质量、质量负责人、副总经理、总经理!'
            ),
            type: 'warning'
          })
          return
        }
      }
      Promise.all(promiseValidataArr)
        .then(async () => {
          this.saveTask(1)
        })
        .catch((reason) => {
          this.$hloading()
          if (this.isGroupTask) {
            this.saveTask(0)
            return
          }
          setTimeout(() => {
            this.$toast({
              content: reason.msg || this.$t('数据校验不通过，请检查!'),
              type: 'warning'
            })
          }, 100)
        })
    },

    // 保存任务
    async saveTask(temporarySave) {
      this.$loading()
      const ajaxSaveTask = await this.saveTaskWithoutCheckAjax(temporarySave)
      this.$hloading()

      let { code } = ajaxSaveTask
      if (code && code === 200) {
        this.$toast({
          content: ajaxSaveTask.msg || this.$t('保存成功!'),
          type: 'success'
        })
        this.saveOldData()
        this.refreshTasks(this.$route.query.customerEnterpriseId)

        // 更新页面数据
        // location.reload();
      } else {
        this.$toast({
          content: ajaxSaveTask.msg || this.$t('获取表单定义模板数据失败，请重试!'),
          type: 'warning'
        })
      }
    },

    saveTaskWithoutCheckAjax(temporarySave) {
      return new Promise((resolve) => {
        let formDataArr = []
        // 这种写法势必需要第一个动态项是表单而不是表格
        this.$refs[`parser_${this.formTemplateArr[0].id}`][0].getFormData()

        // “行内编辑在不脱离编辑状态的情况下直接点右上角保存”
        // “循环调用组件方法脱离编辑状态拿到编辑中的数据”
        this.formTemplateArr.forEach((e) => {
          let parseName = `parser_${e.id}`
          let _formData = this.getData(parseName)
          _formData
            .then((res) => {
              formDataArr.push({
                formData: res,
                formInstanceId: e.id
              })
              // “if条件是用来判断有没有遍历完动态表单的，满足if条件代表遍历了，else一定会走，不用管”
              if (formDataArr.length === this.formTemplateArr.length) {
                resolve(
                  this.$API.customerAccess.saveFormData({
                    supplierFormDataRequestList: formDataArr,
                    taskInstanceId: this.formInfo.id,
                    temporarySave
                  })
                )
              }
            })
            .catch((error) => {
              resolve({
                msg: error.msg
              })
            })
        })
      })
    },
    getData(parseName) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(this.$refs[parseName][0].getFormData())
        }, 100)
      })
    },

    // 预提交
    proSubmitTask() {
      if ([10, 30, 60].includes(this.formInfo.status)) {
        let flag = this.formTemplateArr.some((e) =>
          e.name?.includes('基础信息-基本信息-集团级-国内')
        )
        if (flag) {
          let arr = []
          this.formTemplateArr.forEach((item) => {
            arr.push(this.$refs[`parser_${item.id}`][0].valiteFormData())
          })
          Promise.all(arr)
            .then(() => {
              this.submitTask()
            })
            .catch(() => {
              this.$toast({
                content: this.$t('有必填项未填写，请完善！'),
                type: 'warning'
              })
            })
        } else {
          this.submitTask()
        }
      }
    },

    // 提交任务
    async submitTask() {
      const todoList = []
      this.fullTaksArr.forEach((arr) => {
        arr.length && todoList.push(...arr)
      })
      const taskList = []
      if (todoList.length) {
        todoList.forEach((item) => {
          if (item.supplierTaskList?.length) {
            const supplierTaskList = []
            item.supplierTaskList.forEach((t) => {
              const { id, status } = t
              supplierTaskList.push({ id, status })
            })
            taskList.push({
              customerEnterpriseId: item.customerEnterpriseId,
              customerEnterpriseCode: item.customerEnterpriseCode,
              categoryCode: item.categoryCode,
              customerEnterpriseName: item.customerEnterpriseName,
              supplierTaskList
            })
          }
        })
      }
      this.$loading()
      this.$API.customerAccess
        .submitAllTask({ taskList })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: res.msg || this.$t('提交成功'),
              type: 'success'
            })

            this.refreshTasks(this.$route.query.customerEnterpriseId)
          } else {
            this.$toast({
              content: res.msg || this.$t('提交失败，请重试!'),
              type: 'warning'
            })
          }
          this.$hloading()
        })
        .catch((err) => {
          this.$toast({
            content: err.msg || this.$t('提交失败，请重试!'),
            type: 'warning'
          })
          this.$hloading()
        })
    },

    // 通过任务 不搞了
    aggressiveTask() {},

    // 拒绝 不搞了
    refuseTask() {},

    //信息变更
    changeTask() {
      console.log('信息变更', this.formInfo)
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/lifecycledetailsupplier/contactInformation/applyDialog" */ './lifeCycle/changeList/components/applyDialog.vue'
          ),
        data: {
          title: this.$t('新增信息变更申请'),
          selectForm: this.formInfo,
          partnerRelationId: this.supplyId
        },
        success: (res) => {
          console.log('任务中心--返回', res)
          this.$emit('handleCloseApplyDialog')
        }
      })
    }
  },
  destroyed() {
    FORMLISTDATA.clear()
    this.$hloading()
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../node_modules/@mtech-form-design/form-parser/build/esm/bundle.css';
.submit-btn {
  padding: 0 10px;
  font-size: 14px;

  font-weight: 500;
  color: rgba(0, 70, 156, 1);
  cursor: pointer;
  position: absolute;
  right: 60px;
  top: 8px;
  display: inline;
}
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.task-center {
  margin-top: 20px;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  border-right: 2px solid rgba(232, 232, 232, 1);
  // overflow: hidden;

  .task-sidebar {
    width: 240px;
    height: 100%;
    background: #fff;

    .search-box {
      position: relative;
      width: 200px;
      height: 46px;
      margin-top: 20px;
      margin-left: 20px;
      .mt-icons {
        position: absolute;
        font-size: 16px;
        left: 0;
        bottom: 14px;
        color: #9baac1;
      }
      .orgName {
        width: 200px;
        height: 46px;
      }
    }
  }

  .task-collapse {
    .collapse-header {
      height: 60px;
      line-height: 60px;
      padding-left: 30px;
      font-size: 16px;

      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      border-top: 1px solid rgba(232, 232, 232, 1);

      -moz-user-select: none;
      -ms-user-select: none;
      -webkit-user-select: none;
      user-select: none;
    }

    .collapse-header:nth-child(1) {
      border-top: none;
    }

    .active {
      color: rgba(0, 70, 156, 1);
      // border-bottom: 1px solid rgba(232,232,232,1);
    }
  }

  .task-content {
    border-left: 1px solid rgba(232, 232, 232, 1);
    display: flex;
    flex-direction: column;
    overflow-x: auto;

    .task-info {
      padding: 24px 30px 24px 20px;
      background: transparent;
      justify-content: space-between;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      .mian-info {
        .task-list {
          .reject-reason {
            color: #eda133;
          }
          flex-wrap: wrap;
          span {
            margin-right: 30px;
            font-size: 12px;

            font-weight: normal;
            color: rgba(100, 100, 100, 1);
          }
        }

        .categroy-list {
          margin-top: 14px;

          .factory-item {
            margin-right: 30px;
            flex-wrap: wrap;
            .factory-name {
              height: 20px;
              line-height: 20px;
              font-size: 12px;

              font-weight: 500;
              color: rgba(41, 41, 41, 1);
            }
            .factory-category {
              margin-left: 10px;
              height: 20px;
              line-height: 20px;
              padding: 0 4px;
              background: rgba(99, 134, 193, 0.1);
              border-radius: 2px;
              font-size: 12px;

              font-weight: 500;
              color: rgba(99, 134, 193, 1);
              margin-bottom: 10px;
            }
          }
        }
      }
      .btn-box {
        white-space: nowrap;
        // width: calc( 48px + 48px + 30px);
        .normal-btn {
          padding: 0 10px;
          font-size: 14px;

          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          cursor: pointer;
        }
        .normal-btn:nth-child(1) {
          margin-right: 30px;
        }

        .err-tips {
          display: flex;
          i {
            position: relative;
            top: 3px;
            margin-right: 4px;
          }
        }
      }
    }

    .task-form {
      flex: 1;
      .side-bar {
        width: 160px;
        border-right: 1px solid rgba(232, 232, 232, 1);
        .side-item {
          width: 100%;
          height: 50px;
          line-height: 50px;
          font-size: 14px;

          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          padding-left: 20px;

          cursor: pointer;
          -moz-user-select: none;
          -ms-user-select: none;
          -webkit-user-select: none;
          user-select: none;
        }
        .active {
          color: #00469c;
          background: #f5f6f9;
        }
      }

      .form-content {
        padding: 30px 20px;
        height: calc(100vh - 246px);
        overflow: auto;
        &.editFlag {
          pointer-events: none;
        }

        .empty-box {
          margin: 0 auto;
          margin-top: 40px;
          font-size: 16px;
          color: #333;
          text-align: center;
        }

        .none {
          display: none;
        }
        .display-item {
          display: block;

          .parse-title {
            color: #292929;
            position: relative;
            display: flex;
            padding-left: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;

            &::before {
              content: ' ';
              display: inline-block;
              position: absolute;
              width: 2px;
              height: 14px;
              background: #00469c;
              left: 0;
            }
          }
          .mt-row {
            // min-height: 300px;
            .mt-form-item {
              min-height: 300px;
              .e-grid {
                min-height: 200px !important;
              }
              .e-content {
                min-height: 200px !important;
              }
            }
            /deep/.mt-data-grid {
              min-height: 200px !important;
              .e-grid {
                min-height: 200px !important;
              }
              .e-content {
                min-height: 200px !important;
              }
            }
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.search-box {
  .orgName {
    width: 200px !important;
    height: 46px;
    padding-left: 26px !important;
  }
}
.task-center {
  .mt-tooptip {
    margin-right: 40px;
    line-height: 16px;
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .accordion-header {
    width: 140px;
    font-size: 14px;

    color: rgba(41, 41, 41, 1);
    display: flex;

    .company-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .header-number {
      // width: ;
      color: #00469c;
    }
  }

  // children
  .sub-item {
    height: 50px;
    line-height: 50px;
    padding-left: 50px;
    background: #fff;
    font-size: 14px;
    border-bottom: 1px solid #e8e8e8;
    font-weight: 500;
    color: #292929;
    display: flex;
    align-items: center;
    cursor: pointer;

    .task-name {
      width: 130px;
    }

    .sub-tips {
      display: inline-block;
      padding: 0 4px;
      height: 20px;
      line-height: 20px;
      background: rgba(237, 161, 51, 0.1);
      border-radius: 2px;
      color: #eda133;
    }
    .red-bg {
      background: rgba(237, 86, 51, 0.1) !important;
      color: #ed5633 !important;
    }
  }

  .sub-item:last-child {
    border-bottom: none;
  }

  .active {
    background: rgba(245, 246, 249, 1);
    color: rgba(0, 70, 156, 1);
  }

  .grey {
    color: rgba(154, 154, 154, 1);
    .sub-tips {
      display: inline-block;
      padding: 0 4px;
      height: 20px;
      line-height: 20px;
      background: rgba(154, 154, 154, 0.1);
      border-radius: 2px;
      color: #9a9a9a;
    }
  }

  .ivu-collapse > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header {
    background: rgba(245, 246, 249, 1);
    border-bottom: 1px solid #e8e8e8 !important;
  }
  .ivu-collapse > .ivu-collapse-item > .ivu-collapse-header {
    padding-left: 30px;
  }
  .ivu-collapse-content {
    padding: 0;
  }
}
</style>
