<!-- 供方-预审调查表 -->
<template>
  <div style="height: 100%; background-color: #fff">
    <sc-table
      ref="sctableRef"
      grid-id="e7a2b2ec-4564-40ac-82aa-17762a0f15b4"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template #codeDefault="{ row }">
        <div style="color: #409eff; cursor: pointer" @click="handleClick(row)">
          {{ row.code }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      loading: false,
      tableData: [],

      statusOptions: [
        { text: this.$t('未发布'), value: 0 },
        { text: this.$t('待填写'), value: 1 },
        { text: this.$t('已填写'), value: 2 },
        { text: this.$t('已批准'), value: 3 },
        { text: this.$t('已驳回'), value: 4 }
      ],

      oaApproveStatusOptions: [
        { text: this.$t('待OA审批'), value: 0 },
        { text: this.$t('OA审批中'), value: 1 },
        { text: this.$t('已通过'), value: 2 },
        { text: this.$t('已驳回'), value: 3 }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'code',
          title: this.$t('单据编码'),
          slots: {
            default: 'codeDefault'
          }
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称')
        },
        {
          field: 'categoryCode',
          title: this.$t('品类')
        },
        {
          field: 'orgCode',
          title: this.$t('公司'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.orgName : ''
          }
        },
        {
          field: 'industryBelong',
          title: this.$t('所属行业')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          formatter: ({ cellValue }) => {
            let item = this.statusOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'oaApproveStatus',
          title: this.$t('OA审批状态'),
          formatter: ({ cellValue }) => {
            let item = this.oaApproveStatusOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'rejectReason',
          title: this.$t('驳回理由')
        }
      ]
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleClick(row) {
      this.$router.push({
        path: '/supplier/sup/preReviewInvestigationDetail',
        query: {
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.customerAccess.queryPreReviewInvestigationSupApi
      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    }
  }
}
</script>
