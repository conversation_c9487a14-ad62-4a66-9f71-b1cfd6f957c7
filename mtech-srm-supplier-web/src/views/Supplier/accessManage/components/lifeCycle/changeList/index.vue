<template>
  <div class="change-list-container">
    <mt-template-page
      v-if="!detailMode"
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
    <apply-detail
      v-if="detailMode"
      :select-apply="selectApply"
      @changeDetailMode="changeDetailMode"
    ></apply-detail>
  </div>
</template>

<script>
import { pageConfig } from './config/index'
export default {
  components: {
    //变更申请-详情
    applyDetail: () =>
      import(
        /* webpackChunkName: "router/lifecycledetailsupplier/changeList/applyDetail" */ './applyDetail.vue'
      )
  },
  data() {
    return {
      pageConfig,
      supplyId: null,
      selectApply: null,
      detailMode: false
    }
  },
  props: {},
  mounted() {
    let supplyId = this.$route.query.id
    if (!supplyId) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.supplyId = supplyId
    this.resetAsyncConfigParams()
  },

  methods: {
    //列表参数重新赋值
    resetAsyncConfigParams() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.customerProfileChange.getApplyInfoChangeList,
        params: {
          partnerRelationId: this.supplyId
        }
      })
    },
    changeDetailMode(edit) {
      this.detailMode = false
      if (edit) {
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'applyCode') {
        //进入详情
        this.redirectToApplyDetail(e.data)
      }
    },
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (e.toolbar.id == 'Add') {
        this.handleAddConfig()
        return
      } else if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectRecords)
      } else if (e.toolbar.id == 'Edit') {
        if (_selectRecords.length > 1) {
          this.$toast({
            content: this.$t('只支持修改一行数据'),
            type: 'warning'
          })
        } else {
          // //编辑操作
          let _disableStatusRecords = _selectRecords.filter(
            (e) => e.applyStatus !== 10 && e.applyStatus !== 30
          )
          if (_disableStatusRecords.length > 0) {
            //选中数据中，存在不可操作的状态  10和30可以执行‘申请操作’
            // 10: "待填写",
            // 20: "待审批",
            // 30: "已驳回",
            // 40: "已完成",
            // 50: "已关闭",
            this.$toast({
              content: this.$t("只有'待填写'、'已驳回'状态，可执行该操作"),
              type: 'warning'
            })
            return
          } else {
            this.handleEditConfig(_selectRecords[0])
          }
        }
      } else if (e.toolbar.id == 'Apply') {
        let _disableStatusRecords = _selectRecords.filter(
          (e) => e.applyStatus !== 10 && e.applyStatus !== 30
        )
        if (_disableStatusRecords.length > 0) {
          //选中数据中，存在不可操作的状态  10和30可以执行‘申请操作’
          // 10: "待填写",
          // 20: "待审批",
          // 30: "已驳回",
          // 40: "已完成",
          // 50: "已关闭",
          this.$toast({
            content: this.$t("只有'待填写'、'已驳回'状态，可执行该操作"),
            type: 'warning'
          })
          return
        } else {
          this.handleBatchApply(_selectRecords)
        }
      }
    },
    //单元格icons，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'Edit') {
        //编辑操作
        this.handleEditConfig(e.data)
      } else if (e.tool.id == 'delete') {
        //删除操作
        this.handleDeleteConfig([e.data.id])
      }
      // else if (e.tool.id == "apply") {
      //   //提交操作
      //   this.handleApplyRecords([e.data.id]);
      // }
    },
    //批量删除操作
    handleBatchDelete(_selectRecords) {
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //批量删除操作
    handleBatchApply(_selectRecords) {
      let _selectIds = []
      _selectRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleApplyRecords(_selectIds)
    },
    //新增配置
    handleAddConfig() {
      console.log('新增信息变更申请')
      // this.$toast({ content: "请在调查表页面，执行变更申请", type: "warning" });
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/lifecycledetailsupplier/contactInformation/applyDialog" */ './components/applyDialog.vue'
          ),
        data: {
          title: this.$t('新增信息变更申请'),
          partnerRelationId: this.supplyId
        },
        success: (res) => {
          if (res.type === 'detail') {
            //进入详情
            this.redirectToApplyDetail(res.data)
          }
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditConfig(data) {
      console.log('编辑配置', data)
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/lifecycledetailsupplier/contactInformation/editApplyDialog" */ './components/editApplyDialog.vue'
            ),
          data: {
            title: this.$t('编辑信息变更申请'),
            partnerRelationId: this.supplyId,
            data
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //删除配置
    handleDeleteConfig(ids) {
      console.log('删除配置', ids)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.customerProfileChange.deleteApplyInfo({ ids: ids.join(',') }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //执行'提交'操作
    handleApplyRecords(ids) {
      console.log("执行'提交'操作", ids)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'提交'操作？")
        },
        success: () => {
          this.$API.customerProfileChange.submitApplyInfo({ ids: ids.join(',') }).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //进入详情页
    redirectToApplyDetail(data) {
      //切换到详情页
      this.selectApply = data
      this.detailMode = true
    }
  }
}
</script>
<style lang="scss" scoped>
.contact-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .contact-item {
    flex: 1;
    &.purchase-contact {
      margin-top: 20px;
    }
    .contact-title {
      font-size: 16px;
      color: #292929;
      display: inline-block;
      padding-left: 13px;
      position: relative;
      margin-bottom: 20px;

      &:before {
        content: '';
        position: absolute;
        width: 3px;
        height: 14px;
        background: #6386c1;
        border-radius: 0 2px 2px 0;
        left: 0;
        top: 2px;
      }
    }
    .contact-grid {
      flex: 1;
    }
  }
}
</style>
