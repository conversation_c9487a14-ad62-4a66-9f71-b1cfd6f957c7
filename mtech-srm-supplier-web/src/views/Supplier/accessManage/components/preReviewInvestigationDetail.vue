<!-- 供方-预审调查表-详情 -->
<template>
  <div class="detail">
    <div class="header">
      <div class="title">{{ $t('供应商资格预审调查表') }}</div>
      <div class="btns">
        <mt-button type="text" @click="handleSave">{{ $t('保存') }}</mt-button>
        <mt-button type="text" @click="handleBack">{{ $t('返回') }}</mt-button>
      </div>
    </div>
    <div class="content">
      <div class="title">一、基本信息</div>
      <div class="base-info">
        <div class="base-info-content">
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('供应商名称') }}</div>
            <div class="info-content-item">
              {{ baseInfo.supplierName || '--' }}
            </div>
            <div class="info-title">{{ $t('法人代表') }}</div>
            <div class="info-content-item">
              {{ baseInfo.legalRepresentative || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('拟入围物资名称') }}</div>
            <div class="info-content-item" style="width: 88% !important; display: block !important">
              <div v-for="e in 2" :key="e" style="display: flex">
                <div class="checkbox-wrap" v-for="(item, key) in materialList" :key="key">
                  <mt-checkbox
                    v-if="e === 1 && key < 9"
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'materialName')"
                    :disabled="item.disabled"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                  <mt-checkbox
                    v-if="e === 2 && key >= 9"
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'materialName')"
                    :disabled="item.disabled"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                </div>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('营业执照注册号') }}</div>
            <div class="info-content-item">
              {{ baseInfo.taxNumber || '--' }}
            </div>
            <div class="info-title">{{ $t('注册资本（万元）') }}</div>
            <div class="info-content-item">
              {{ baseInfo.registeredCapital || '--' }}
            </div>
            <div class="info-title">{{ $t('企业成立日期') }}</div>
            <div class="info-content-item">
              {{ baseInfo.establishmentTime | formatTime }}
            </div>
            <div class="info-title">{{ $t('注册日期') }}</div>
            <div class="info-content-item">
              {{ baseInfo.registrationDate | formatTime }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('公司注册地址') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              {{ baseInfo.detailAddress || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('公司经营地址') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              {{ baseInfo.businessAddress || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('生产工厂详细地址') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              {{ baseInfo.factoryAddress || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('公司电话') }}</div>
            <div class="info-content-item">
              {{ baseInfo.enterprisePhone || '--' }}
            </div>
            <div class="info-title">{{ $t('公司网址') }}</div>
            <div class="info-content-item">
              {{ baseInfo.website || '--' }}
            </div>
            <div class="info-title">{{ $t('开户行') }}</div>
            <div class="info-content-item">
              {{ baseInfo.bankName || '--' }}
            </div>
            <div class="info-title">{{ $t('开户账号') }}</div>
            <div class="info-content-item">
              {{ baseInfo.bankAccount || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('企业性质') }}</div>
            <div class="info-content-item" style="width: 88% !important; display: block !important">
              <div style="display: flex">
                <div class="checkbox-wrap" v-for="(item, key) in enterpriseNatureList" :key="key">
                  <mt-checkbox
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'enterpriseNature')"
                    :disabled="true"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                </div>
              </div>
              <div style="display: flex">
                <div class="checkbox-wrap" v-for="(item, key) in isListedCompanyList" :key="key">
                  <mt-checkbox
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'isListedCompany')"
                    :disabled="item.disabled"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                </div>
              </div>
              <div style="display: flex">
                <div class="checkbox-wrap" v-for="(item, key) in enterpriseTypeList" :key="key">
                  <mt-checkbox
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'enterpriseType')"
                    :disabled="item.disabled"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                </div>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('厂区面积（平方米）') }}</div>
            <div class="info-content-item">
              {{ baseInfo.factoryArea || '--' }}
            </div>
            <div class="info-title">{{ $t('建筑面积（平方米）') }}</div>
            <div class="info-content-item">
              <mt-input-number v-model="baseInfo.buildingArea" :min="0" :show-spin-button="false" />
            </div>
            <div class="info-title">{{ $t('已使用生产能力') }}</div>
            <div class="info-content-item">
              <mt-input-number
                v-model="baseInfo.usedProduceAbility"
                :min="0"
                :show-spin-button="false"
              />
            </div>
          </div>
        </div>
      </div>
      <div style="padding: 20px 0">
        注：<span style="color: #2783fe; cursor: pointer" @click="handleClick('baseInfoFileList')"
          >附营业执照</span
        >
      </div>
      <div class="title">二、主要客户</div>
      <div style="width: 65%">
        <sc-table
          ref="clientTableRef"
          grid-id="6a31c2dd-2bda-400d-a577-74223431e1c0"
          :loading="loading"
          :columns="customerColumns"
          :table-data="clientList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'clientTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span style="color: #2783fe; cursor: pointer" @click="handleClick('clientFileList')"
          >业绩表</span
        >
      </div>
      <div class="title">三、主要竞争对手</div>
      <div style="width: 65%">
        <sc-table
          ref="competitorTableRef"
          grid-id="70ef0b8c-e010-48f7-8145-64511ad49736"
          :loading="loading"
          :columns="competitorColumns"
          :table-data="competitorList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'competitorTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div class="title">四、二级供应商</div>
      <div style="width: 65%">
        <sc-table
          ref="levelTwoSupplierTableRef"
          grid-id="386289fa-fc0c-4456-a117-16726df52ee2"
          :loading="loading"
          :columns="levelTwoSupplierColumns"
          :table-data="levelTwoSupplierList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'levelTwoSupplierTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div class="title">五、组织机构</div>
      <div style="width: 65%">
        <sc-table
          ref="organizationTableRef"
          grid-id="8dd909d4-39f8-4b0d-8f77-d93d70f69365"
          :loading="loading"
          :columns="organizationColumns"
          :table-data="organizationList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'organizationTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span
          style="color: #2783fe; cursor: pointer"
          @click="handleClick('organizationFileList')"
          >附组织结构图</span
        >
      </div>
      <div class="title">六、生产设备</div>
      <div style="width: 65%">
        <sc-table
          ref="produceDeviceTableRef"
          grid-id="c5ab9f7b-162c-4f44-8f35-abde26c4071c"
          :loading="loading"
          :columns="produceDeviceColumns"
          :table-data="produceDeviceList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'produceDeviceTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span
          style="color: #2783fe; cursor: pointer"
          @click="handleClick('produceDeviceFileList')"
          >附设备照片</span
        >
      </div>
      <div class="title">七、检验设备</div>
      <div style="width: 65%">
        <sc-table
          ref="testDeviceTableRef"
          grid-id="a7668f2a-d0ad-44aa-9d9e-af9dee3fae8b"
          :loading="loading"
          :columns="testDeviceColumns"
          :table-data="testDeviceList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'testDeviceTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span style="color: #2783fe; cursor: pointer" @click="handleClick('testDeviceFileList')"
          >附设备照片</span
        >
      </div>
      <div class="title">八、持证情况</div>
      <div style="width: 65%">
        <sc-table
          ref="certificateTableRef"
          grid-id="9694013d-e6e5-49a4-86aa-b8509ddcc7ec"
          :loading="loading"
          :columns="certificateColumns"
          :table-data="certificateList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'certificateTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span
          style="color: #2783fe; cursor: pointer"
          @click="handleClick('certificateFileList')"
          >附证书扫描件</span
        >
      </div>
      <div class="title">九、资信情况</div>
      <div class="base-info">
        <div class="base-info-content">
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('银行信用等级') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div class="checkbox-wrap" v-for="(item, key) in bankCreditLevelOptions" :key="key">
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'bankCreditLevel')"
                  :disabled="item.disabled"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('工商企业信用等级') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div
                class="checkbox-wrap"
                v-for="(item, key) in businessCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'businessCreditLevel')"
                  :disabled="item.disabled"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('纳税信用等级') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div class="checkbox-wrap" v-for="(item, key) in taxCreditLevelOptions" :key="key">
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'taxCreditLevel')"
                  :disabled="item.disabled"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('财务会计信用等级') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div
                class="checkbox-wrap"
                v-for="(item, key) in financeCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'financeCreditLevel')"
                  :disabled="item.disabled"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('企业产品质量信用') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div style="margin-right: 20px">最近 1-3 年是否出现重大安全事故投诉记录</div>
              <div
                class="checkbox-wrap"
                v-for="(item, key) in productQualityCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'productQualityCreditLevel')"
                  :disabled="item.disabled"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('重大项目投标信用') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div style="margin-right: 20px">
                最近 1-3 年间在投标项目中有无重大窜标围标等恶性竞标记录
              </div>
              <div
                class="checkbox-wrap"
                v-for="(item, key) in projectBidCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'projectBidCreditLevel')"
                  :disabled="item.disabled"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('法定代表人个人信用') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div style="margin-right: 20px">最近 1-3 年间法定代表人有无不良信用记录</div>
              <div
                class="checkbox-wrap"
                v-for="(item, key) in legalPersonCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'legalPersonCreditLevel')"
                  :disabled="item.disabled"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="padding: 20px 0">
        注：<span
          style="color: #2783fe; cursor: pointer"
          @click="handleClick('certificateCreditFileList')"
          >附支持材料扫描件、信用中国查询截图</span
        >
      </div>
      <div class="title">十、正在进行及即将开始的法律纠纷</div>
      <div style="width: 65%">
        <sc-table
          ref="legalDisputesTableRef"
          grid-id="8b3a3de9-13b5-4ea2-b343-03a29e1b5fbb"
          :loading="loading"
          :columns="legalDisputesColumns"
          :table-data="legalDisputesList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'legalDisputesTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div class="title">十一、股权结构</div>
      <div style="width: 65%">
        <sc-table
          ref="shareholdTableRef"
          grid-id="9ddd5b84-a70e-45f6-9018-78106acf849e"
          :loading="loading"
          :columns="shareholdColumns"
          :table-data="shareholdList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'shareholdTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div style="padding: 20px 0">注：<span>提供前 5 大股东的名称和比例</span></div>
      <div class="title">十二、财务报表</div>
      <div style="width: 65%">
        <sc-table
          ref="financeTableRef"
          grid-id="cc3471fa-b828-4b55-a5c3-f4e8734b9207"
          :loading="loading"
          :columns="financeColumns"
          :table-data="financeList"
          :fix-height="200"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item, 'financeTableRef')"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span style="color: #2783fe; cursor: pointer" @click="handleClick('financeFileList')"
          >提供近三年资产负债表、利润表、现金流量表</span
        >
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      baseInfo: {},
      loading: false,
      tableData: [],

      materialList: [
        { text: this.$t('组件'), value: '组件', checked: false },
        { text: this.$t('逆变器'), value: '逆变器', checked: false },
        { text: this.$t('并网箱'), value: '并网箱', checked: false },
        { text: this.$t('支架'), value: '支架', checked: false },
        { text: this.$t('彩钢瓦支架'), value: '彩钢瓦支架', checked: false },
        { text: this.$t('电缆'), value: '电缆', checked: false },
        { text: this.$t('储能'), value: '储能', checked: false },
        { text: this.$t('一次二次设备'), value: '一次二次设备', checked: false },
        { text: this.$t('充电桩'), value: '充电桩', checked: false },
        { text: this.$t('风电'), value: '风电', checked: false },
        { text: this.$t('H钢（钢结构）'), value: 'H钢（钢结构）', checked: false },
        { text: this.$t('辅材'), value: '辅材', checked: false },
        { text: this.$t('其他'), value: '其他', checked: false },
        { text: this.$t('钢材'), value: '钢材', checked: false },
        { text: this.$t('铝材'), value: '铝材', checked: false },
        { text: this.$t('硅片'), value: '硅片', checked: false },
        { text: this.$t('硅料'), value: '硅料', checked: false },
        { text: this.$t('元器件'), value: '元器件', checked: false }
      ],
      enterpriseNatureList: [],
      isListedCompanyList: [
        { text: this.$t('上市公司'), value: 1, checked: false, disabled: false },
        { text: this.$t('非上市公司'), value: 2, checked: false, disabled: false }
      ],
      enterpriseTypeList: [
        { text: this.$t('制造商'), value: 1, checked: false, disabled: false },
        { text: this.$t('贸易商'), value: 2, checked: false, disabled: false },
        { text: this.$t('代理商'), value: 3, checked: false, disabled: false }
      ],
      clientList: [],
      competitorList: [],
      levelTwoSupplierList: [],
      organizationList: [],
      produceDeviceList: [],
      testDeviceList: [],
      certificateList: [],
      legalDisputesList: [],
      shareholdList: [],
      financeList: [],

      employeeTypeOptions: [
        { text: this.$t('EHS人数'), value: 1 },
        { text: this.$t('研发人数'), value: 2 },
        { text: this.$t('生产人数'), value: 3 },
        { text: this.$t('质检人数'), value: 4 },
        { text: this.$t('售后人数'), value: 5 }
      ],
      financeTypeOptions: [
        { text: this.$t('固定资产'), value: 1 },
        { text: this.$t('流动资产'), value: 2 },
        { text: this.$t('资产负债率'), value: 3 },
        { text: this.$t('主营业务收入'), value: 4 },
        { text: this.$t('净利润'), value: 5 },
        { text: this.$t('净利润率'), value: 6 }
      ],
      bankCreditLevelOptions: [
        { text: this.$t('AAA级'), value: 1, checked: false, disabled: false },
        { text: this.$t('AA级'), value: 2, checked: false, disabled: false },
        { text: this.$t('A级'), value: 3, checked: false, disabled: false },
        { text: this.$t('其他'), value: 4, checked: false, disabled: false }
      ],
      businessCreditLevelOptions: [
        { text: this.$t('AAA级'), value: 1, checked: false, disabled: false },
        { text: this.$t('AA级'), value: 2, checked: false, disabled: false },
        { text: this.$t('A级'), value: 3, checked: false, disabled: false },
        { text: this.$t('其他'), value: 4, checked: false, disabled: false }
      ],
      taxCreditLevelOptions: [
        { text: this.$t('A级'), value: 1, checked: false, disabled: false },
        { text: this.$t('B级'), value: 2, checked: false, disabled: false },
        { text: this.$t('C级'), value: 3, checked: false, disabled: false },
        { text: this.$t('D级'), value: 4, checked: false, disabled: false },
        { text: this.$t('其他'), value: 5, checked: false, disabled: false }
      ],
      financeCreditLevelOptions: [
        { text: this.$t('A级'), value: 1, checked: false, disabled: false },
        { text: this.$t('B级'), value: 2, checked: false, disabled: false },
        { text: this.$t('C级'), value: 3, checked: false, disabled: false },
        { text: this.$t('D级'), value: 4, checked: false, disabled: false },
        { text: this.$t('其他'), value: 5, checked: false, disabled: false }
      ],
      productQualityCreditLevelOptions: [
        { text: this.$t('有'), value: 1, checked: false, disabled: false },
        { text: this.$t('无'), value: 0, checked: false, disabled: false }
      ],
      projectBidCreditLevelOptions: [
        { text: this.$t('有'), value: 1, checked: false, disabled: false },
        { text: this.$t('无'), value: 0, checked: false, disabled: false }
      ],
      legalPersonCreditLevelOptions: [
        { text: this.$t('有'), value: 1, checked: false, disabled: false },
        { text: this.$t('无'), value: 0, checked: false, disabled: false }
      ]
    }
  },
  filters: {
    formatTime: (e) => {
      if (e && !isNaN(e) && e.length == 13) {
        e = Number(e)
        return dayjs(e).format('YYYY-MM-DD')
      } else {
        return '-'
      }
    }
  },
  computed: {
    currentId() {
      return this.$route.query?.id
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    editRules() {
      return {
        companyCode: [{ required: true, message: this.$t('必填') }]
      }
    },
    toolbar() {
      return [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
      ]
    },
    customerColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'clientName',
          title: this.$t('客户名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.clientName} />]
            }
          }
        },
        {
          field: 'material',
          title: this.$t('所供物料及规格型号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.material} />]
            }
          }
        },
        {
          field: 'goodsRate',
          title: this.$t('占自身出货率'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.goodsRate} type='integer' clearable min={0} max={100} />
              ]
            }
          }
        }
      ]
    },
    competitorColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'product',
          title: this.$t('产品'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.product} />]
            }
          }
        },
        {
          field: 'competitorName',
          title: this.$t('行业龙头/竞争对手名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.competitorName} />]
            }
          }
        },
        {
          field: 'marketShareRate',
          title: this.$t('市场份额百分比'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.marketShareRate}
                  type='integer'
                  clearable
                  min={0}
                  max={100}
                />
              ]
            }
          }
        },
        {
          field: 'advantage',
          title: this.$t('优势'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.advantage} />]
            }
          }
        },
        {
          field: 'disadvantage',
          title: this.$t('劣势'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.disadvantage} />]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} />]
            }
          }
        }
      ]
    },
    levelTwoSupplierColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'materialName',
          title: this.$t('材料名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.materialName} />]
            }
          }
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.supplierName} />]
            }
          }
        },
        {
          field: 'unit',
          title: this.$t('单位'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.unit} />]
            }
          }
        },
        {
          field: 'yearPurchaseQuantity',
          title: this.$t('年采购量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.yearPurchaseQuantity} type='integer' clearable min={0} />
              ]
            }
          }
        },
        {
          field: 'ratio',
          title: this.$t('占比'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.ratio} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} />]
            }
          }
        }
      ]
    },
    organizationColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'employeeType',
          title: this.$t('类型'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text = this.employeeTypeOptions.find(
                (item) => item.value === row.employeeType
              )?.text
              return [<div>{text || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.employeeType}
                  options={this.employeeTypeOptions}
                  option-props={{ label: 'text', value: 'value' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'employeeQuantity',
          title: this.$t('人数'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.employeeQuantity} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'ratio',
          title: this.$t('占比'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.ratio} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'contactPerson',
          title: this.$t('对接人'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.contactPerson} />]
            }
          }
        },
        {
          field: 'contactPhone',
          title: this.$t('对接电话'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.contactPhone} />]
            }
          }
        }
      ]
    },
    produceDeviceColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'deviceName',
          title: this.$t('主要设备名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.deviceName} />]
            }
          }
        },
        {
          field: 'model',
          title: this.$t('规格型号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.model} />]
            }
          }
        },
        {
          field: 'quantity',
          title: this.$t('数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.quantity} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'brand',
          title: this.$t('生产厂家或品牌'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.brand} />]
            }
          }
        },
        {
          field: 'monthCapacity',
          title: this.$t('设备月产能'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.monthCapacity} />]
            }
          }
        },
        {
          field: 'startUserDate',
          title: this.$t('开始使用年月'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.startUserDate?.length === 13
                  ? dayjs(Number(row.startUserDate)).format('YYYY-MM-DD')
                  : row.startUserDate
              return text
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.startUserDate} type={'date'} transfer clearable />]
            }
          }
        }
      ]
    },
    testDeviceColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'deviceName',
          title: this.$t('主要设备名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.deviceName} />]
            }
          }
        },
        {
          field: 'model',
          title: this.$t('规格型号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.model} />]
            }
          }
        },
        {
          field: 'quantity',
          title: this.$t('数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.quantity} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'brand',
          title: this.$t('生产厂家或品牌'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.brand} />]
            }
          }
        },
        {
          field: 'testItem',
          title: this.$t('测试项目'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.testItem} />]
            }
          }
        },
        {
          field: 'startUserDate',
          title: this.$t('开始使用年月'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.startUserDate?.length === 13
                  ? dayjs(Number(row.startUserDate)).format('YYYY-MM-DD')
                  : row.startUserDate
              return text
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.startUserDate} type={'date'} transfer clearable />]
            }
          }
        }
      ]
    },
    certificateColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'certificateType',
          title: this.$t('认证类型'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.certificateType} />]
            }
          }
        },
        {
          field: 'issueUnit',
          title: this.$t('发证单位'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.issueUnit} />]
            }
          }
        },
        {
          field: 'issueDate',
          title: this.$t('发证日期'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.issueDate?.length === 13
                  ? dayjs(Number(row.issueDate)).format('YYYY-MM-DD')
                  : row.issueDate
              return text
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.issueDate} type={'date'} transfer clearable />]
            }
          }
        },
        {
          field: 'validEndDate',
          title: this.$t('有效期至'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.validEndDate?.length === 13
                  ? dayjs(Number(row.validEndDate)).format('YYYY-MM-DD')
                  : row.validEndDate
              return text
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.validEndDate} type={'date'} transfer clearable />]
            }
          }
        }
      ]
    },
    legalDisputesColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'legalRole',
          title: this.$t('公司一审角色（原告/被告）'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.legalRole} />]
            }
          }
        },
        {
          field: 'currentStage',
          title: this.$t('目前所在阶段'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.currentStage} />]
            }
          }
        },
        {
          field: 'counterpart',
          title: this.$t('相对方'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.counterpart} />]
            }
          }
        },
        {
          field: 'amount',
          title: this.$t('金额'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.amount} type='integer' clearable min={0} />]
            }
          }
        }
      ]
    },
    shareholdColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'shareholder',
          title: this.$t('股东名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.shareholder} />]
            }
          }
        },
        {
          field: 'ratio',
          title: this.$t('占比'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.ratio} type='integer' clearable min={0} max={100} />]
            }
          }
        }
      ]
    },
    financeColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'financeType',
          title: this.$t('财务类型'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text = this.financeTypeOptions.find(
                (item) => item.value === row.financeType
              )?.text
              return [<div>{text || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.financeType}
                  options={this.financeTypeOptions}
                  option-props={{ label: 'text', value: 'value' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'beforeLastYear',
          title: this.$t('前年'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.beforeLastYear} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'lastYear',
          title: this.$t('去年'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.lastYear} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'currentYear',
          title: this.$t('今年预期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.currentYear} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'nextYear',
          title: this.$t('明年预期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.nextYear} type='integer' clearable min={0} />]
            }
          }
        }
      ]
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'invoiceNo',
          title: this.$t('客户名称')
        },
        {
          field: 'invoiceNo',
          title: this.$t('所供物料及规格型号')
        },
        {
          field: 'invoiceNo',
          title: this.$t('占自身出货率')
        }
      ]
    }
  },
  created() {
    this.getEnterpriseNatureList()
    this.getDetail()
  },
  methods: {
    getEnterpriseNatureList() {
      this.$API.infoChange['queryDict']({
        dictCode: 'EnterpriseType'
      }).then((res) => {
        if (res.code === 200) {
          this.enterpriseNatureList = res.data.map((item) => {
            return {
              text: item.itemName,
              value: item.itemCode,
              checked: false
            }
          })
        }
      })
    },
    getDetail() {
      let params = { id: this.currentId }
      this.$API.customerAccess.detailPreReviewInvestigationSupApi(params).then((res) => {
        if (res.code === 200) {
          this.baseInfo = res.data
          this.clientList = res.data.clientList
          this.competitorList = res.data.competitorList
          this.levelTwoSupplierList = res.data.levelTwoSupplierList
          this.organizationList = res.data.organizationList
          this.produceDeviceList = res.data.produceDeviceList
          this.testDeviceList = res.data.testDeviceList
          this.certificateList = res.data.certificateList
          this.legalDisputesList = res.data.legalDisputesList
          this.shareholdList = res.data.shareholdList
          this.financeList = res.data.financeList
          this.baseInfo.factoryAddress = res.data.factoryAddress
            ? JSON.parse(res.data.factoryAddress)
                ?.map((v) => JSON.parse(v)?.address)
                .join(',')
            : ''
          this.setCheckbox()
        }
      })
    },
    setCheckbox() {
      let materials = this.baseInfo.materialName?.split(',')
      this.materialList.forEach((item) => {
        if (materials.includes(item.value)) {
          item.checked = !item.checked
        }
      })
      let enterpriseNature = this.baseInfo.enterpriseNature
      this.enterpriseNatureList.forEach((item) => {
        if (enterpriseNature) {
          if (item.value === enterpriseNature) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let isListedCompany = this.baseInfo.listedCompanyFlag
      this.isListedCompanyList.forEach((item) => {
        if (isListedCompany) {
          if (item.value === isListedCompany) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let enterpriseType = this.baseInfo.enterpriseType
      this.enterpriseTypeList.forEach((item) => {
        if (enterpriseType) {
          if (item.value === enterpriseType) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let bankCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].bankCreditLevel
          : null
      this.bankCreditLevelOptions.forEach((item) => {
        if (bankCreditLevel) {
          if (item.value === bankCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      this.$set(this.baseInfo, 'bankCreditLevel', bankCreditLevel)
      let businessCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].businessCreditLevel
          : null
      this.businessCreditLevelOptions.forEach((item) => {
        if (businessCreditLevel) {
          if (item.value === businessCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      this.$set(this.baseInfo, 'businessCreditLevel', businessCreditLevel)
      let taxCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].taxCreditLevel
          : null
      this.taxCreditLevelOptions.forEach((item) => {
        if (taxCreditLevel) {
          if (item.value === taxCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      this.$set(this.baseInfo, 'taxCreditLevel', taxCreditLevel)
      let financeCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].financeCreditLevel
          : null
      this.financeCreditLevelOptions.forEach((item) => {
        if (financeCreditLevel) {
          if (item.value === financeCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      this.$set(this.baseInfo, 'financeCreditLevel', financeCreditLevel)
      let productQualityCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].productQualityCreditLevel
          : null
      this.productQualityCreditLevelOptions.forEach((item) => {
        if (productQualityCreditLevel !== null) {
          if (item.value === productQualityCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      this.$set(this.baseInfo, 'productQualityCreditLevel', productQualityCreditLevel)
      let projectBidCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].projectBidCreditLevel
          : null
      this.projectBidCreditLevelOptions.forEach((item) => {
        if (projectBidCreditLevel !== null) {
          if (item.value === projectBidCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      this.$set(this.baseInfo, 'projectBidCreditLevel', projectBidCreditLevel)
      let legalPersonCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].legalPersonCreditLevel
          : null
      this.legalPersonCreditLevelOptions.forEach((item) => {
        if (legalPersonCreditLevel !== null) {
          if (item.value === legalPersonCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      this.$set(this.baseInfo, 'legalPersonCreditLevel', legalPersonCreditLevel)
    },
    checkChange(e, key) {
      let materials,
        enterpriseNature,
        isListedCompany,
        enterpriseType,
        bankCreditLevel,
        businessCreditLevel,
        taxCreditLevel,
        financeCreditLevel,
        productQualityCreditLevel,
        projectBidCreditLevel,
        legalPersonCreditLevel = null
      switch (key) {
        case 'materialName':
          this.materialList.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            }
          })
          materials = this.materialList.filter((item) => item.checked).map((item) => item.value)
          this.$set(this.baseInfo, 'materialName', materials.join(','))
          break
        case 'enterpriseNature':
          this.enterpriseNatureList.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          enterpriseNature = this.enterpriseNatureList.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'enterpriseNature', enterpriseNature)
          break
        case 'isListedCompany':
          this.isListedCompanyList.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          isListedCompany = this.isListedCompanyList.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'listedCompanyFlag', isListedCompany)
          break
        case 'enterpriseType':
          this.enterpriseTypeList.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          enterpriseType = this.enterpriseTypeList.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'enterpriseType', enterpriseType)
          break
        case 'bankCreditLevel':
          this.bankCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          bankCreditLevel = this.bankCreditLevelOptions.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'bankCreditLevel', bankCreditLevel)
          break
        case 'businessCreditLevel':
          this.businessCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          businessCreditLevel = this.businessCreditLevelOptions.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'businessCreditLevel', businessCreditLevel)
          break
        case 'taxCreditLevel':
          this.taxCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          taxCreditLevel = this.taxCreditLevelOptions.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'taxCreditLevel', taxCreditLevel)
          break
        case 'financeCreditLevel':
          this.financeCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          financeCreditLevel = this.financeCreditLevelOptions.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'financeCreditLevel', financeCreditLevel)
          break
        case 'productQualityCreditLevel':
          this.productQualityCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          productQualityCreditLevel = this.productQualityCreditLevelOptions.find(
            (item) => item.checked
          )?.value
          this.$set(this.baseInfo, 'productQualityCreditLevel', productQualityCreditLevel)
          break
        case 'projectBidCreditLevel':
          this.projectBidCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          projectBidCreditLevel = this.projectBidCreditLevelOptions.find(
            (item) => item.checked
          )?.value
          this.$set(this.baseInfo, 'projectBidCreditLevel', projectBidCreditLevel)
          break
        case 'legalPersonCreditLevel':
          this.legalPersonCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          legalPersonCreditLevel = this.legalPersonCreditLevelOptions.find(
            (item) => item.checked
          )?.value
          this.$set(this.baseInfo, 'legalPersonCreditLevel', legalPersonCreditLevel)
          break
        default:
          break
      }
    },
    handleClick(key) {
      let title = ''
      let fileList = []
      switch (key) {
        case 'baseInfoFileList':
          title = '营业执照'
          fileList = cloneDeep(this.baseInfo.baseInfoFileList)
          break
        case 'clientFileList':
          title = '业绩表'
          fileList = cloneDeep(this.baseInfo.clientFileList)
          break
        case 'organizationFileList':
          title = '组织结构图'
          fileList = cloneDeep(this.baseInfo.organizationFileList)
          break
        case 'produceDeviceFileList':
          title = '生产设备'
          fileList = cloneDeep(this.baseInfo.produceDeviceFileList)
          break
        case 'testDeviceFileList':
          title = '检验设备'
          fileList = cloneDeep(this.baseInfo.testDeviceFileList)
          break
        case 'certificateFileList':
          title = '证书扫描件'
          fileList = cloneDeep(this.baseInfo.certificateFileList)
          break
        case 'certificateCreditFileList':
          title = '支持材料扫描件、信用中国查询截图'
          fileList = cloneDeep(this.baseInfo.certificateCreditFileList)
          break
        case 'financeFileList':
          title = '近三年资产负债表、利润表、现金流量表'
          fileList = cloneDeep(this.baseInfo.financeFileList)
          break
        default:
          break
      }
      this.$dialog({
        modal: () => import('./detailFileMange.vue'),
        data: {
          title,
          fileList,
          type: 'upload'
        },
        success: (list) => {
          this.$set(this.baseInfo, key, list)
        }
      })
    },
    handleClickToolBar(e, ref) {
      const selectedRecords = this.$refs[ref].$refs.xGrid.getCheckboxRecords()
      if (['delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd(ref)
          break
        case 'delete':
          this.handleDelete(ref)
          break
        default:
          break
      }
    },
    handleAdd(ref) {
      const item = {}
      this.$refs[ref].$refs.xGrid.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.$refs[ref].$refs.xGrid.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.$refs[ref].$refs.xGrid.setEditRow(currentViewRecords[0])
      })
    },
    handleDelete(ref) {
      this.$refs[ref].$refs.xGrid.removeCheckboxRow()
    },
    editComplete(args, ref) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.$refs[ref].$refs.xGrid.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    handleSave() {
      let params = this.getParams()
      let valid = this.validData(params)
      if (valid) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认保存？')
          },
          success: () => {
            this.$loading()
            this.$API.customerAccess
              .savePreReviewInvestigationSupApi(params)
              .then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('保存成功'), type: 'success' })
                  this.handleBack()
                }
              })
              .finally(() => {
                this.$hloading()
              })
          }
        })
      }
    },
    getParams() {
      console.log('getParams baseInfo', this.baseInfo)
      let params = {
        id: this.baseInfo.id,
        supplierName: this.baseInfo.supplierName,
        legalRepresentative: this.baseInfo.legalRepresentative,
        taxNumber: this.baseInfo.taxNumber,
        registeredCapital: this.baseInfo.registeredCapital,
        establishmentTime: this.baseInfo.establishmentTime,
        registrationDate: this.baseInfo.registrationDate,
        detailAddress: this.baseInfo.detailAddress,
        businessAddress: this.baseInfo.businessAddress,
        factoryAddress: this.baseInfo.factoryAddress,
        enterprisePhone: this.baseInfo.enterprisePhone,
        website: this.baseInfo.website,
        bankName: this.baseInfo.bankName,
        bankAccount: this.baseInfo.bankAccount,
        enterpriseNature: this.baseInfo.enterpriseNature,
        factoryArea: this.baseInfo.factoryArea,
        materialName: this.baseInfo.materialName,
        listedCompanyFlag: this.baseInfo.listedCompanyFlag,
        enterpriseType: this.baseInfo.enterpriseType,
        buildingArea: this.baseInfo.buildingArea,
        produceAbility: this.baseInfo.produceAbility,
        usedProduceAbility: this.baseInfo.usedProduceAbility
      }
      params.clientList = this.$refs['clientTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id
          }
        })
      params.competitorList = this.$refs['competitorTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id
          }
        })
      params.levelTwoSupplierList = this.$refs['levelTwoSupplierTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id
          }
        })
      params.organizationList = this.$refs['organizationTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id
          }
        })
      params.produceDeviceList = this.$refs['produceDeviceTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id,
            startUserDate:
              item.startUserDate?.length === 13
                ? item.startUserDate
                : dayjs(item.startUserDate).valueOf()
          }
        })
      params.testDeviceList = this.$refs['testDeviceTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id,
            startUserDate:
              item.startUserDate?.length === 13
                ? item.startUserDate
                : dayjs(item.startUserDate).valueOf()
          }
        })
      params.certificateList = this.$refs['certificateTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id,
            issueDate:
              item.issueDate?.length === 13 ? item.issueDate : dayjs(item.issueDate).valueOf(),
            validEndDate:
              item.validEndDate?.length === 13
                ? item.validEndDate
                : dayjs(item.validEndDate).valueOf()
          }
        })
      params.legalDisputesList = this.$refs['legalDisputesTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id
          }
        })
      params.shareholdList = this.$refs['shareholdTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id
          }
        })
      params.financeList = this.$refs['financeTableRef'].$refs.xGrid
        .getTableData()
        .visibleData.map((item) => {
          return {
            ...item,
            id: item.id?.includes('row_') ? null : item.id
          }
        })
      params.certificateCreditList = [
        {
          bankCreditLevel: this.baseInfo.bankCreditLevel,
          businessCreditLevel: this.baseInfo.businessCreditLevel,
          taxCreditLevel: this.baseInfo.taxCreditLevel,
          financeCreditLevel: this.baseInfo.financeCreditLevel,
          productQualityCreditLevel: this.baseInfo.productQualityCreditLevel,
          projectBidCreditLevel: this.baseInfo.projectBidCreditLevel,
          legalPersonCreditLevel: this.baseInfo.legalPersonCreditLevel
        }
      ]
      params.baseInfoFileList = this.baseInfo.baseInfoFileList.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          fileType: item.fileType,
          fileUrl: item.fileUrl
        }
      })
      params.clientFileList = this.baseInfo.clientFileList.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          fileType: item.fileType,
          fileUrl: item.fileUrl
        }
      })
      params.organizationFileList = this.baseInfo.organizationFileList.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          fileType: item.fileType,
          fileUrl: item.fileUrl
        }
      })
      params.produceDeviceFileList = this.baseInfo.produceDeviceFileList.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          fileType: item.fileType,
          fileUrl: item.fileUrl
        }
      })
      params.testDeviceFileList = this.baseInfo.testDeviceFileList.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          fileType: item.fileType,
          fileUrl: item.fileUrl
        }
      })
      params.certificateFileList = this.baseInfo.certificateFileList.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          fileType: item.fileType,
          fileUrl: item.fileUrl
        }
      })
      params.certificateCreditFileList = this.baseInfo.certificateCreditFileList.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          fileType: item.fileType,
          fileUrl: item.fileUrl
        }
      })
      params.financeFileList = this.baseInfo.financeFileList.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          fileType: item.fileType,
          fileUrl: item.fileUrl
        }
      })
      console.log('getParams params', params)
      return params
    },
    validData(params) {
      let valid = true
      if (!params.materialName) {
        this.$toast({ content: this.$t('请选择拟入围物资'), type: 'warning' })
        valid = false
        return
      }
      if (!params.listedCompanyFlag) {
        this.$toast({ content: this.$t('请选择是否上市公司'), type: 'warning' })
        valid = false
        return
      }
      if (!params.enterpriseType) {
        this.$toast({ content: this.$t('请选择企业性质'), type: 'warning' })
        valid = false
        return
      }
      if (!params.buildingArea) {
        this.$toast({ content: this.$t('请输入建筑面积'), type: 'warning' })
        valid = false
        return
      }
      if (!params.usedProduceAbility) {
        this.$toast({ content: this.$t('请输入已使用生产能力'), type: 'warning' })
        valid = false
        return
      }
      if (params?.clientList.length === 0) {
        this.$toast({ content: this.$t('请填写主要客户'), type: 'warning' })
        valid = false
        return
      }
      if (params?.clientFileList.length === 0) {
        this.$toast({ content: this.$t('请上传业绩表'), type: 'warning' })
        valid = false
        return
      }
      if (params?.competitorList.length === 0) {
        this.$toast({ content: this.$t('请填写主要竞争对手'), type: 'warning' })
        valid = false
        return
      }
      if (params?.levelTwoSupplierList.length === 0) {
        this.$toast({ content: this.$t('请填写二级供应商'), type: 'warning' })
        valid = false
        return
      }
      if (params?.organizationList.length === 0) {
        this.$toast({ content: this.$t('请填写组织机构'), type: 'warning' })
        valid = false
        return
      }
      if (params?.organizationFileList.length === 0) {
        this.$toast({ content: this.$t('请上传组织结构图'), type: 'warning' })
        valid = false
        return
      }
      if (params?.produceDeviceList.length === 0) {
        this.$toast({ content: this.$t('请填写生产设备'), type: 'warning' })
        valid = false
        return
      }
      if (params?.produceDeviceFileList.length === 0) {
        this.$toast({ content: this.$t('请上传生产设备照片'), type: 'warning' })
        valid = false
        return
      }
      if (params?.testDeviceList.length === 0) {
        this.$toast({ content: this.$t('请填写检验设备'), type: 'warning' })
        valid = false
        return
      }
      if (params?.testDeviceFileList.length === 0) {
        this.$toast({ content: this.$t('请上传检验设备照片'), type: 'warning' })
        valid = false
        return
      }
      if (params?.certificateList.length === 0) {
        this.$toast({ content: this.$t('请填写持证情况'), type: 'warning' })
        valid = false
        return
      }
      if (params?.certificateFileList.length === 0) {
        this.$toast({ content: this.$t('请上传证书扫描件'), type: 'warning' })
        valid = false
        return
      }
      if (params?.certificateCreditFileList.length === 0) {
        this.$toast({ content: this.$t('请上传资信材料'), type: 'warning' })
        valid = false
        return
      }
      if (params?.financeList.length === 0) {
        this.$toast({ content: this.$t('请填写财务报表'), type: 'warning' })
        valid = false
        return
      }
      if (params?.financeFileList.length === 0) {
        this.$toast({ content: this.$t('请上传财务材料'), type: 'warning' })
        valid = false
        return
      }
      return valid
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  background-color: #fff;
  .header {
    text-align: center;
    padding: 20px;
    .title {
      display: inline-block;
      text-align: center;
      font-size: 26px;
      font-weight: 600;
    }
    .btns {
      float: right;
    }
  }
  .content {
    padding: 0 20px;
    .title {
      font-size: 16px;
      padding: 8px 0;
    }
    .base-info {
      width: 100%;
      .base-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);
        .base-info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);
          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 12%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            word-break: break-all;
          }
          .info-content-item {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 13%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
        }
      }
    }
  }
}
.checkbox-wrap {
  height: 30px;
  line-height: 30px;
  border-radius: 2px;
  display: flex;
  align-items: center;
}
</style>
