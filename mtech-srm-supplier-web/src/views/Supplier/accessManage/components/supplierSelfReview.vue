<template>
  <div style="background-color: #fff">
    <sc-table
      ref="sctableRef"
      grid-id="c6eed7fc-d91e-429d-a0a2-43c29810a004"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template #fileTempDefault>
        <div style="color: #409eff; cursor: pointer" @click="handleClick">
          {{ $t('供应商自查模板') }}
        </div>
      </template>
      <template #fileDefault="{ row }">
        <div style="color: #409eff; cursor: pointer" @click="handleUpload(row)">
          {{ $t('上传附件') }}
        </div>
      </template>
      <template #remarkDefault="{ row }">
        <div>
          <vxe-input
            v-model="row.supplierReason"
            :placeholder="$t('请输入')"
            @blur="saveRemark(row)"
          />
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'
export default {
  components: { ScTable },
  data() {
    return {
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      loading: false,
      tableData: [],

      resultOptions: [
        { text: this.$t('待提交'), value: 0 },
        { text: this.$t('待审批'), value: 1 },
        { text: this.$t('通过'), value: 2 },
        { text: this.$t('驳回'), value: 3 }
      ],

      fileId: null,
      suffix: 'xlsx'
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'selfReviewCode',
          title: this.$t('评审任务编码')
        },
        {
          field: 'selfReviewName',
          title: this.$t('评审任务名称')
        },
        {
          field: 'orgCode',
          title: this.$t('公司'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.orgName : ''
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.categoryName : ''
          }
        },
        {
          field: 'categoryRelationCode',
          title: this.$t('品类采供关系编码')
        },
        {
          field: 'supplierEnterpriseName',
          title: this.$t('供应商企业名称')
        },
        {
          field: 'fileTemp',
          title: this.$t('附件模板'),
          slots: {
            default: 'fileTempDefault'
          }
        },
        {
          field: 'file',
          title: this.$t('结果附件'),
          slots: {
            default: 'fileDefault'
          }
        },
        {
          field: 'reviewResult',
          title: this.$t('自查结果'),
          formatter: ({ cellValue }) => {
            let item = this.resultOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'rejectReason',
          title: this.$t('驳回原因')
        },
        {
          field: 'approverUserName',
          title: this.$t('审批人')
        },
        {
          field: 'supplierReason',
          title: this.$t('供方备注'),
          slots: {
            default: 'remarkDefault'
          }
        }
      ]
    }
  },
  created() {
    this.getTableData()
    this.getFileId()
  },
  methods: {
    getFileId() {
      this.$API.customerAccess.getOdminSelfReviewResultTemplateFileApi().then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.fileId = res.data.find((v) => v.itemCode === 'id')?.itemName
            this.suffix = res.data.find((v) => v.itemCode === 'fileType')?.itemName
          }
        }
      })
    },
    handleClick() {
      this.$loading()
      this.$API.fileService.downloadPrivateFile({ id: this.fileId }).then((res) => {
        this.$hloading()
        download({
          fileName: this.$t('供应商自查模板') + '.' + this.suffix,
          blob: new Blob([res.data])
        })
      })
    },
    handleUpload(row) {
      this.$dialog({
        modal: () => import('./FileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          type: 'upload',
          id: row.id,
          selfReviewCode: row.selfReviewCode,
          supplierEnterpriseId: row.supplierEnterpriseId
        }
      })
    },
    saveRemark(row) {
      let params = {
        id: row.id,
        supplierReason: row.supplierReason
      }
      this.$API.customerAccess.updateSupplierOdminSelfReviewApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('修改备注成功'),
            type: 'success'
          })
          this.handleSearch()
        }
      })
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.customerAccess.getOdminSelfReviewApi
      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    }
  }
}
</script>
