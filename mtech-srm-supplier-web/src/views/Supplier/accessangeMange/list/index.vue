<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { getListColumnData, Qualification } from './config/index'
export default {
  components: {},
  data() {
    return {
      data: '',
      query: {
        //资质库弹窗接口参数
        applyCode: '',
        qualificationCode: ''
      },
      arr: [],
      emptyMsg: '',
      //公司准入关系
      buyerPartnerFactoryRelationList: {},
      //表单详情
      buyerFormInstanceList: {},
      // 表单模板
      formTemplateArr: [],
      pageConfig: [
        {
          gridId: 'bbd90735-6c3e-4d41-a7c4-e33caa9128c6',
          title: this.$t('待处理'),
          useToolTemplate: false,
          toolbar: [
            {
              id: 'Commit',
              icon: 'icon_solid_Createorder',
              title: this.$t('提交')
            }
          ],
          grid: {
            lineSelection: true,
            columnData: getListColumnData(),
            asyncConfig: {
              url: this.$API.accessangeMange.getExamination,
              methods: 'post'
            }
            // frozenColumns: 1,
          }
        },
        {
          gridId: 'ef32a275-70e9-43e6-8572-bd2dc20ff7a3',
          title: this.$t('资质库'),
          useToolTemplate: false,
          toolbar: [],
          grid: {
            columnData: Qualification(),
            // dataSource: [
            //   {
            //     surveyTaskNo1: 2,
            //     surveyTaskName1: 1,
            //     surveySource1: 2,
            //     surveyTemplateType1: 3,
            //     createTime1: 4,
            //   },
            // ],
            asyncConfig: {
              url: this.$API.accessangeMange.getqualification,
              methods: 'post'
            }
            // frozenColumns: 1,
          }
        }
      ],
      supplierStatus: '0',
      page: {
        current: 1,
        size: 10
      }
      //this.pageConfig[0].grid.dataSource = res.data
    }
  },
  mounted() {
    // this.getExamination(); //资质审查单接口数据
    // this.getqualification(); //资质库接口数据
  },
  methods: {
    handleClickToolBar(e) {
      let list = e.gridRef.getMtechGridRecords()
      if (list.length == 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Commit') {
        let arr = list.map((item) => {
          return item.applyCode
        })
        const parmes = {
          applyCodeList: arr
        }
        // const res = JSON.stringify(parmes)
        //提交的接口
        this.$API.accessangeMange.getsubmission(parmes).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({ content: this.$t('提交失败'), type: 'warning' })
            return
          }
        })
      }
    },
    handleClickCellTitle(e) {
      let params = {
        applyCode: e.data.applyCode,
        qualificationCode: e.data.qualificationCode
      }
      if (e.field == 'qualificationCode') {
        this.qualification(params)
      }
      if (e.field == 'applyCode') {
        localStorage.accessangeMange = JSON.stringify(e.data) //点击资质单号存本地一个
        this.$router.push({
          path: '/supplier/sup/survey'
        })
      }
    },
    //资质库的资质项按钮
    qualification(val) {
      this.$dialog({
        modal: () => import('./components/qualificationItemDialog.vue'),
        data: val
      })
    }

    //供方查询资质库-详细(弹层里的内容)
    // async Dynamiclibrary() {
    //   const { Dynamiclibrary } = this.$API.accessangeMange;
    //   try {
    //     const { data } = await Dynamiclibrary();
    //   } catch (error) {
    //   }
    // },
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
// /deep/ .mt-data-grid .e-grid .e-gridheader{
//   padding-right: 0px !important;
// }
</style>
