<template>
  <!-- 资质项自定义 -->
  <mt-dialog
    ref="thresholdItemDialog"
    :buttons="buttons"
    :header="$t('查看资质项')"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="demo-block" v-show="boolen1">
        <!-- <mt-DataGrid
              :dataSource="dialogGridDataSource"
              :columnData="dialogGridColumnData"
              ref="dataGrid"
              @rowSelected="getSelectedRecords"
            ></mt-DataGrid> -->
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTitle="handleClickCellTitle"
        />
      </div>
      <div class="demo-block1" v-show="!boolen1">
        <div>
          <div><img :src="logo" /></div>
          <div class="xx">{{ $t('无详细信息') }}</div>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>
<script>
import Vue from 'vue'
import { dialogGridColumnData } from '../config/index'
// import { dialogGridDataSource, dialogGridColumnData} from "../config/qualificationItemDialog"
import utils from '@/utils/utils.js'
let fileData = null
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      logo: require('./暂无数据.png'),
      boolen1: false,
      list: [],
      // dialogGridDataSource,
      dialogGridColumnData,
      // 表格的配置
      pageConfig: [],
      sourceLabel: this.$t('选择资质'),
      radioData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '2'
        }
      ],
      bizTypeList: [
        {
          text: this.$t('资质项'),
          value: 1
        },
        {
          text: this.$t('业务类型'),
          value: 2
        }
      ],
      symbolList: [
        {
          text: '>',
          value: 1
        },
        {
          text: '<',
          value: 2
        },
        {
          text: '≥',
          value: 3
        },
        {
          text: '≤',
          value: 4
        },
        {
          text: '=',
          value: 5
        },
        {
          text: this.$t('非空'),
          value: 6
        },
        {
          text: this.$t('为空'),
          value: 7
        }
      ],
      formInfo: {
        bizId: 0,
        bizType: 2,
        defaultValue: '',
        fieldCode: null,
        fieldId: null,
        fieldName: null,
        formType: 0,
        symbol: 0,
        thresholdName: '',
        source: '1'
      },
      thresholdFieldList: [],
      formTypeList: [],
      rules: {
        thresholdName: [
          {
            required: true,
            message: this.$t('请输入门槛名称'),
            trigger: 'blur'
          },
          {
            whitespace: true,
            message: this.$t('请输入门槛名称'),
            trigger: 'blur'
          }
        ],
        formType: [
          {
            required: true,
            message: this.$t('请选择门槛类型'),
            trigger: 'blur'
          }
        ],
        bizType: [{ required: true, message: this.$t('请选择数据源'), trigger: 'blur' }],
        fieldCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        symbol: [
          {
            required: true,
            message: this.$t('请选择所属维度'),
            trigger: 'blur'
          }
        ],
        defaultValue: [
          {
            required: true,
            message: this.$t('请输入默认目标值'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      uploadInfo: {} // 上传后信息
    }
  },
  mounted() {
    // this.initData();
    this.show()
    this.getReview()
  },
  created() {},
  beforeMount() {
    // params()
  },
  methods: {
    async getReview() {
      const { data } = await this.$API.surveyTemplate.getReview(this.modalData)
      this.list = data
      this.getlist() //处理动态数据
    },
    getlist() {
      const { submitList } = this.list

      let healder = []
      let obj = {}
      submitList.forEach((item) => {
        let obj1 = {
          field: item.fieldCode,
          headerText: item.fieldName
        }
        if (item.fieldCode == 'file') {
          obj1.template = () => {
            return {
              template: Vue.component('modelName', {
                template: `<div>
                  <p v-for="(item,index) in fileList" :key="index" style="color:#00469c;cursor:pointer;" @click="fieldDownloadBtn(item)">{{item.fileName}}</p>
                </div>`,
                data() {
                  return {}
                },
                computed: {
                  fileList() {
                    try {
                      if (this.data.file && this.data.file != '') {
                        return JSON.parse(this.data.file)
                      } else {
                        return []
                      }
                    } catch (err) {
                      return []
                    }
                  }
                },
                methods: {
                  fieldDownloadBtn(item) {
                    console.log(this.data, 'this.data.file')
                    if (this.data.file) {
                      this.$API.fileService
                        .downloadPrivateFileTypeOne(item.fileValue)
                        .then((res) => {
                          let link = document.createElement('a') // 创建元素
                          link.style.display = 'none'
                          let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                          let url = window.URL.createObjectURL(blob)
                          link.href = url
                          link.setAttribute('download', item.fileName) //文件命名
                          link.click() // 点击下载
                          window.URL.revokeObjectURL(url)
                        })
                        .catch(() => {
                          this.$toast({ content: this.$t('导出失败，请重试!'), type: 'warning' })
                        })
                    }
                  }
                }
              })
            }
          }
        }
        healder.push(obj1)
        obj[item.fieldCode] = item.fieldValue
        obj['fieldComment'] = item.fieldComment
      })
      this.pageConfig = [
        {
          gridId: 'c62d3c4b-3422-40d2-9a59-c6feae4a06ae',
          useToolTemplate: false,
          useBaseConfig: false,
          toolbar: {
            tool: []
          },

          grid: {
            // lineSelection: true,
            allowPaging: false,
            columnData: healder,
            dataSource: [obj] //表格内容
            // asyncConfig: {
            //   url: this.$API.surveyTemplate.getReview,
            //   methods: "get",
            //   params: this.modalData,
            // },
            // frozenColumns: 1,
          }
        }
      ]
      if (this.pageConfig[0].grid.columnData.length > 0) {
        this.boolen1 = true
      }
    },
    // // 资质库弹层里的逻辑
    // async getReview() {
    //   const { getReview } = this.$API.surveyTemplate;
    //   const data = await getReview(this.query);
    //   this.data=data
    // },
    handleClickToolBar() {
      // let list = e.gridRef.getMtechGridRecords();
      // if (list.length == 0) {
      //   this.$toast({ content: this.$t("请先选择一行"), type: "warning" });
      //   return;
      // }
      // if (e.toolbar.id == "Commit") {
      //   this.arr = list.map((item) => {
      //     return item.applyCode;
      //   });
      //   this.$API.accessangeMange.getsubmission(this.arr).then((res) => {
      //   });
      // }
    },
    handleClickCellTitle(e) {
      // 文件下载功能
      if (e.field == 'modelName') {
        let link = document.createElement('a') // 创建元素
        link.style.display = 'none'
        link.id = new Date().getTime()
        link.href = e.data.modelUrl
        link.setAttribute('download', `${e.data.modelName}`) // 给下载后的文件命名
        document.body.appendChild(link)
        link.click() // 点击下载
      }
    },

    // 失效前提醒（天）==触发时
    handleChange() {},
    // getSelectedRecords() {
    //   // let Obj = this.$refs.dataGrid.ejsRef.getSelectedRecords()
    // },
    onFiltering(e) {
      e.updateData(this.thresholdFieldList.filter((x) => x.fieldName.includes(e.text)))
    },
    symbolChange(e) {
      if (e.value > 5) {
        this.formInfo.defaultValue = null
        this.rules.defaultValue[0].required = false
      } else {
        this.rules.defaultValue[0].required = true
      }
    },
    async initData() {
      await this.getDict()
      // await this.getThresholdFieldList(this.info.bizType);
      await this.getThresholdFieldList()
      if (this.isEdit && this.info && Object.keys(this.info).length) {
        this.formInfo = {
          ...this.info,
          bizType: 2,
          formType: this.info.formType.toString()
        }
      }
    },
    async getDict() {
      await this.$API.ModuleConfig.queryDict({
        dictCode: 'thresholdType'
      }).then((res) => {
        this.formTypeList = res.data
      })
    },
    show() {
      this.$refs['thresholdItemDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdItemDialog'].ejsRef.hide()
    },
    // bizTypeChange(e) {
    //   if (e.value == 1) {
    //     this.sourceLabel = "选择资质";
    //   } else {
    //     this.sourceLabel = "选择信息";
    //   }
    //   this.getThresholdFieldList(e.value);
    // },
    async getThresholdFieldList() {
      await this.$API.ModuleConfig.queryThresholdFieldList()
        .then((res) => {
          if (res.code === 200) {
            this.thresholdFieldList = res.data
          }
        })
        .catch((e) => {
          this.thresholdFieldList = []
          this.$toast({ content: e.msg, type: 'warning' })
          return
        })
    },
    fieldIdChange(e) {
      if (e != null && e.value != null) {
        this.formInfo.fieldCode = e.itemData.fieldCode
        this.formInfo.fieldId = e.itemData.id
        this.formInfo.fieldName = e.itemData.fieldName
      } else {
        this.formInfo.fieldCode = null
        this.formInfo.fieldId = null
        this.formInfo.fieldName = null
      }
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (this.formInfo.defaultValue < 0) {
            this.$toast({
              content: this.$t('请输入非负整数'),
              type: 'warning'
            })
            return
          }
          const methodName = this.isEdit ? 'updateThresholdDef' : 'addThresholdDef'

          this.$API.ModuleConfig[methodName](this.formInfo)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg || this.$t('系统异常'),
                type: 'error'
              })
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    browseBtn() {
      document.getElementById('btn_file').click()
      // this.$dialog({
      //   modal: () =>
      //     import(
      //       "../config/Excelimport.vue"
      //       /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */
      //     ),
      //   data: {
      //     title: "上传/导入",
      //   },
      //   success: () => {
      //     this.$refs.templateRef.refreshCurrentGridData();
      //   },
      // });
    },
    chooseFiles(data) {
      this.$loading()
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // this.headerFlag = false;
        // 您未选择需要上传的文件
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('UploadFiles', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      fileData = _data
      this.uploadFile()
    },
    // 上传文件
    uploadFile() {
      this.$API.SupplierPunishment.fileUpload(fileData)
        .then((res) => {
          const { code, data } = res
          this.$hloading()
          if (code == 200 && !utils.isEmpty(data)) {
            this.uploadInfo = {
              ...data,
              fileId: data.id
            }
            // this.headerFlag = true;
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          } else {
            this.uploadInfo = {}
            this.$toast({ content: data.msg || this.$t('上传失败'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.uploadInfo = {}
          this.$hloading()
          this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0 20px;
  font-size: 16px;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
    }
    .full-width {
      width: 100%;
    }
  }
  .demo-block {
    width: 100%;
  }
  .demo-block1 {
    display: flex;
    justify-content: center;
    align-items: center;
    .xx {
      color: #9a9a9a;
      text-align: center;
      font-size: 14px;
    }
  }

  //当前页==通用样式
  .mgn-left-10 {
    margin-left: 10px;
  }
  .flex {
    display: flex;
  }
  .f-1 {
    flex: 1;
  }
}
/deep/ .mt-input-number {
  width: 100%;
}
/deep/ .mt-input-number .input--wrap {
  width: 100%;
}
/deep/ #mtInputNumber {
  width: 100% !important;
}
</style>
