// import dayjs from "dayjs";
import { i18n } from '@/main.js'

//待处理
export const getListColumnData = function () {
  const listColumnData = [
    {
      field: 'applyCode',
      headerText: i18n.t('审查单编号'),
      cellTools: []
    },
    {
      field: 'applyName',
      headerText: i18n.t('申请单名称')
    },
    {
      field: 'customerCode',
      headerText: i18n.t('客户编码')
    },
    {
      width: '160',
      field: 'customerName',
      headerText: i18n.t('客户名称')
    },
    {
      width: '160',
      field: 'categoryName',
      headerText: i18n.t('品类')
    },
    {
      width: '160',
      field: 'supplierStatus', //213
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map', //(map为key/value对象)：此时，fields可不传。
        map: {
          0: i18n.t('待处理'),
          10: i18n.t('待提交'),
          20: i18n.t('待审批'),
          30: i18n.t('被驳回'),
          40: i18n.t('已确认')
        }
      }
      // template: function () {
      //   return {
      //     template: Vue.component("spanState", {
      //       template: `<span>{{paramsName}}</span>`,
      //       data() {
      //         return {
      //           data: {},
      //           paramsName: "",
      //         };
      //       },
      //       mounted() {
      //         if (this.data.supplierStatus == 0) {
      //           this.paramsName = "待处理";
      //         }
      //         if (this.data.supplierStatus == 10) {
      //           this.paramsName = "已保存，待提交";
      //         }
      //         if (this.data.supplierStatus == 20) {
      //           this.paramsName = "已提交（待审批）";
      //         }
      //         if (this.data.supplierStatus == 30) {
      //           this.paramsName = "被驳回";
      //         }
      //         if (this.data.supplierStatus == 40) {
      //           this.paramsName = "审核通过";
      //         }
      //       },
      //     }),
      //   };
      // }
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建日期')
    }
  ]
  return listColumnData
}

// 资质库
export const Qualification = function () {
  const listColumnData = [
    {
      width: '245',
      field: 'qualificationCode',
      headerText: i18n.t('资质项编号'),
      cellTools: []
    },
    {
      width: '245',
      field: 'qualificationName',
      headerText: i18n.t('资质项')
    },
    {
      width: '245',
      field: 'customerName',
      headerText: i18n.t('客户')
    },
    {
      width: '245',
      field: 'categoryName',
      headerText: i18n.t('品类')
    },
    {
      width: '245',
      field: 'validityDate',
      headerText: i18n.t('有效期'),
      ignore: true,
      valueConverter: {
        type: 'function',
        filter: (e) => {
          return e ? e : '-'
        }
      }
      // valueConverter: {
      //   type: "map", //(map为key/value对象)：此时，fields可不传。
      //   map: {
      //     0: i18n.t("待处理"),
      //     10: i18n.t("待提交"),
      //     20: i18n.t("待审批"),
      //     30: i18n.t("被驳回"),
      //     40: i18n.t("已确认"),
      //   },
      // },
      // template: function () {
      //   return {
      //     template: Vue.component("spanState", {
      //       template: `<span>{{paramsName}}</span>`,
      //       data() {
      //         return {
      //           data: {},
      //           paramsName: "",
      //         };
      //       },
      //       mounted() {
      //         if (!this.data.validityDate) {
      //           this.paramsName = '-'
      //         }
      //         // effectiveDate    //生效日期
      //         // expirationDate //失效日期
      //         // this.paramsName =
      //         // switch (this.data.qualificationDimension) {
      //         //   case 0:
      //         //     this.paramsName = "集团-供应商"
      //         //     break;
      //         //   case 1:
      //         //     this.paramsName = "集团-供应商-品类"
      //         //     break;
      //         //   case 2:
      //         //     this.paramsName = "公司-供应商"
      //         //     break;
      //         //   case 3:
      //         //     this.paramsName = "公司-供应商-品类"
      //         //     break;
      //         //   default:
      //         //     break;
      //         // }
      //       },
      //     }),
      //   };
      // },
    },
    {
      width: '245',
      field: 'createDate',
      headerText: i18n.t('创建日期')
    }
  ]
  return listColumnData
}

// 弹窗
export let dialogGridColumnData = function () {
  const listColumnData = [
    // {
    //   width: "150",
    //   field: "taskName",
    //   headerText: i18n.t("证书编号"),
    // },
    // {
    //   field: "taskID",
    //   headerText: i18n.t("生效日期"),
    //   width: "150",
    // },
    // {
    //   field: "efficacy",
    //   headerText: i18n.t("失效日期"),
    //   width: "150",
    // },
    // {
    //   field: "duration",
    //   headerText: i18n.t("获取日期"),
    //   width: "150",
    // },
    // {
    //   field: "progress",
    //   headerText: i18n.t("发证机关"),
    //   width: "150",
    // },
    // {
    //   field: "accessory",
    //   headerText: i18n.t("附件"),
    //   width: "150",
    //   cellTools: []
    // },
    // {
    //   field: "products",
    //   headerText: i18n.t("代理产品"),
    //   width: "150",
    // },
    // {
    //   field: "factory",
    //   headerText: i18n.t("代理工厂"),
    //   width: "150",
    // },
    // {
    //   field: "priority",
    //   headerText: i18n.t("联系人"),
    //   width: "150",
    // },
  ]
  return listColumnData
}
