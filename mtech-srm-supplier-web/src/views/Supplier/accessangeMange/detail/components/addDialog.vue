<template>
  <mt-dialog ref="toast" :header="header" :buttons="buttons" @close="cancel">
    <mt-form class="form-dialog" ref="form" :model="fromInfo" :rules="rules">
      <div v-for="(item, index) in fromData" :key="index">
        <mt-form-item :prop="item.value" :label="item.text">
          <mt-date-picker
            v-if="
              item.value == 'expirationDate' ||
              item.value == 'effectiveDate' ||
              item.value == 'getDate' ||
              item.value == 'auditDate'
            "
            v-model="fromInfo[item.value]"
            :placeholder="$t('请选择')"
            format="yyyy-MM-dd"
            :allow-edit="false"
          ></mt-date-picker>
          <div v-else-if="item.value == 'file'">
            <input
              type="file"
              id="btn_file"
              ref="file"
              multiple="multiple"
              accept=".xls,.xlsx,.doc,.docx,.pdf,.ppt,.pptx,.png,.jpg,.zip,.rar"
              style="display: none"
              @change="(v) => fileChange(v, item.value)"
            />
            <mt-button class="mgn-left-10" @click="browseBtn()">{{ $t('上传文件') }}</mt-button>
            <div
              style="display: flex; align-items: center"
              v-for="(e, index) in fileList"
              :key="index"
            >
              <mt-input
                :disabled="true"
                v-model="e.fileName"
                style="width: calc(100% - 90px)"
              ></mt-input>
              <mt-button class="mgn-left-10" @click="downloadBtn(e)">{{ $t('下载') }}</mt-button>
              <span style="margin-left: 10px; cursor: pointer" @click="deleteItem(index)"
                ><MtIcon name="close"
              /></span>
            </div>
          </div>
          <mt-input
            v-else-if="item.value == 'remark'"
            v-model="fromInfo[item.value]"
            type="text"
            :placeholder="$t('请输入')"
            float-label-type="Never"
            :multiline="true"
            maxlength="200"
            :rows="4"
          ></mt-input>
          <mt-input
            v-else
            v-model="fromInfo[item.value]"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </div>
    </mt-form>
  </mt-dialog>
</template>
<script>
export default {
  components: {},
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      fromInfo: {},
      rules: {},
      fromData: [],
      fileList: [],
      required: false
      // fileName: "",
      // fieldValue: "",
      // fieldValueName: "",
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    buttons() {
      return [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  mounted() {
    // this.fromInfo = JSON.parse(JSON.stringify(this.modalData.data))
    // this.fromData = JSON.parse(JSON.stringify(this.modalData.fromData))
    this.fromInfo = this.modalData.data
    this.fromData = this.modalData.fromData
    this.rules = {}
    console.log(this.fromInfo, 'this.fromInfofromInfofromInfo')
    this.fromData.forEach((item) => {
      if (item.required == 1) {
        this.rules[item.value] = [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      }
      if (item.value == 'file' && item.required == 1) {
        this.required = true
      }
    })
    if (this.fromInfo.fieldList) {
      this.fromInfo.fieldList.map((i) => {
        if (i.fieldCode === 'file') {
          this.fileList = this.fileListchang(this.fromInfo.file)
        }
      })
    }
    this.$nextTick(() => {
      this.show()
    })
  },
  methods: {
    fileListchang(val) {
      try {
        if (val && val != '') {
          return JSON.parse(val)
        } else {
          return []
        }
      } catch (err) {
        return []
      }
    },
    show() {
      this.$refs.toast.ejsRef.show()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    save() {
      this.fromInfo.file = this.fileList
      this.$refs.form.validate((val) => {
        if (val) {
          if (this.required && this.fileList.length == 0) {
            this.$toast({
              content: this.$t(`请上传附件！`),
              type: 'warning'
            })
            return
          }
          // 失效日期
          if (this.fromInfo.expirationDate) {
            this.fromInfo.expirationDate = this.$utils.formateTime(
              new Date(this.fromInfo.expirationDate),
              'yyyy-MM-dd'
            )
          }
          // 生效日期
          if (this.fromInfo.effectiveDate) {
            this.fromInfo.effectiveDate = this.$utils.formateTime(
              new Date(this.fromInfo.effectiveDate),
              'yyyy-MM-dd'
            )
          }
          // 校验 失效日期 应大于 生效日期
          if (this.fromInfo.expirationDate && this.fromInfo.effectiveDate) {
            if (this.fromInfo.effectiveDate > this.fromInfo.expirationDate) {
              this.$toast({
                content: this.$t('失效日期应大于生效日期'),
                type: 'warning'
              })
              return
            }
          }
          if (this.fromInfo.getDate) {
            this.fromInfo.getDate = this.$utils.formateTime(
              new Date(this.fromInfo.getDate),
              'yyyy-MM-dd'
            )
          }
          if (this.fromInfo.auditDate) {
            this.fromInfo.auditDate = this.$utils.formateTime(
              new Date(this.fromInfo.auditDate),
              'yyyy-MM-dd'
            )
            let today = new Date()
            let nowDate = null
            if (today.getMonth() <= 9) {
              nowDate = today.getFullYear() + '-0' + (today.getMonth() + 1) + '-' + today.getDate()
            } else {
              nowDate = today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate()
            }
            let auditDate = this.fromInfo.auditDate
            if (auditDate > nowDate) {
              this.$toast({
                content: this.$t('审核日期不得晚于今日之前，请重新选择'),
                type: 'warning'
              })
              return
            }
          }
          this.fromInfo.file = JSON.stringify(this.fileList)
          this.$emit('confirm-function', this.fromInfo)
        } else {
          this.$toast({
            content: this.$t(`有必填项未填写，请完善！`),
            type: 'warning'
          })
        }
      })
    },
    deleteItem(index) {
      this.fileList.splice(index, 1)
    },
    fileChange(data) {
      this.$loading()
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        return
      }
      if (files.length > 5) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      // if (
      //   ![
      //     "xlsx",
      //     "docx",
      //     "pptx",
      //     "pdf",
      //     "xls",
      //     "doc",
      //     "png",
      //     "jpg",
      //     "zip",
      //     "rar",
      //     "ppt",
      //   ].includes(btnFile[btnFile.length - 1])
      // ) {
      //   this.$toast({
      //     content:
      //       data.msg ||
      //       this.$t(
      //         `上传格式错误,正确格式为:xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar`
      //       ),
      //     type: "warning",
      //   });
      //   this.$hloading();
      //   return;
      // }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return (
          _tempInfo.length < 2 ||
          ![
            'xlsx',
            'docx',
            'pptx',
            'pdf',
            'xls',
            'doc',
            'png',
            'jpg',
            'zip',
            'rar',
            'ppt'
          ].includes(_tempInfo[_tempInfo.length - 1])
        )
      })
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      let isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$refs.file.value = ''
      files.forEach((item) => {
        let _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1)
        this.uploadFile(_data)
      })
    },
    uploadFile(_data) {
      let _this = this
      this.$API.fileService.uploadPrivateFileTypeOne(_data).then((res) => {
        const { code, data } = res
        this.$hloading()
        if (code == 200 && data) {
          if (this.fileList.length < 50) {
            this.fileList.push({
              fileName: data.fileName,
              fileValue: data.id
            })
            _this.$toast({ content: this.$t('操作成功'), type: 'success' })
          } else {
            _this.$toast({
              content: data.msg || this.$t('最多上传50个附件！'),
              type: 'warning'
            })
          }
        } else {
          _this.$toast({
            content: data.msg || this.$t('上传失败'),
            type: 'warning'
          })
        }
      })
      // .catch((error) => {
      //   this.$hloading();
      //   this.$toast({
      //     content: error.msg || this.$t("上传失败"),
      //     type: "warning",
      //   });
      // });
    },
    browseBtn() {
      let fileId = document.getElementById('btn_file')
      fileId.click()
    },
    // 下载
    downloadBtn(item) {
      if (item.fileValue) {
        this.$API.fileService
          .downloadPrivateFileTypeOne(item.fileValue)
          .then((res) => {
            let link = document.createElement('a') // 创建元素
            link.style.display = 'none'
            let blob = new Blob([res.data], {
              type: 'application/x-msdownload'
            })
            let url = window.URL.createObjectURL(blob)
            link.href = url
            link.setAttribute('download', item.fileName) // 给下载后的文件命名
            link.click() // 点击下载
            window.URL.revokeObjectURL(url)
          })
          .catch(() => {
            this.$toast({
              content: this.$t('导出失败，请重试!'),
              type: 'warning'
            })
          })

        // window.open(this.templateFileUrl);
        // window.location.href = this.templateFileUrl;

        // let link = document.createElement("a"); // 创建元素
        // link.style.display = "none";
        // link.id = new Date().getTime();
        // link.href = this.fieldValue
        //   .replace("http:", window.location.protocol)
        //   .replace("https:", window.location.protocol); // 创建下载的链接
        // link.setAttribute("download", this.fieldValueName); // 给下载后的文件命名
        // link.download = this.fieldValueName;
        // document.body.appendChild(link);
        // link.click(); // 点击下载
      } else {
        this.$toast({
          content: this.$t('当前附件不存在,请先进行上传文件')
        })
      }
      return
    }
  }
}
</script>
<style lang="scss" scoped>
.form-dialog {
  padding-top: 20px;
}
.mgn-left-10 {
  margin-left: 10px;
}
</style>
