<template>
  <div :id="data.column.field">{{ this.data[this.data.column.field] }}</div>
</template>

<script>
// import { cloneDeep } from "lodash";
// import { i18n } from "@/main.js";
// 草稿或审批拒绝状态才能再次上传
export default {
  data() {
    return {
      data: {}
    }
  },
  mounted() {
    if (this.data[this.data.column.field]) {
      this.data[this.data.column.field] = this.$utils.formateTime(
        new Date(this.data[this.data.column.field]),
        'yyyy-MM-dd'
      )
      console.log(this.data)
    }
  }
}
</script>

<style lang="scss" scoped></style>
