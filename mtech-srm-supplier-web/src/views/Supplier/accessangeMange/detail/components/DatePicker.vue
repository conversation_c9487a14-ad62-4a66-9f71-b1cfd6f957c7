<template>
  <div>
    <mt-date-picker
      :id="data.column.field"
      v-model="data[data.column.field]"
      :placeholder="$t('选择日期')"
      :show-clear-button="false"
      :allow-edit="false"
      :disabled="disabled"
    ></mt-date-picker>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formateDate: '',
      disabled: false,
      data: {}
    }
  },
  mounted() {
    console.log(this.data.supplierStatus, 'this.data')
    if (this.data.supplierStatus == '20' || this.data.supplierStatus == '40') {
      this.disabled = true
    }
  }
}
</script>
