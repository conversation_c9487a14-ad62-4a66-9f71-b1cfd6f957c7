<template>
  <div class="cell-upload" :id="'cell-upload-' + data.index">
    <!-- file:{{ data.file }} -->
    <mt-input id="file" style="display: none" :value="data.file"></mt-input>
    <div
      @click.self="showFileBaseInfo"
      :class="['cell-operable-title', { active: isAvtive(data.file) }]"
    >
      {{ data | listNumFormat }}
    </div>

    <!-- 需求附件弹窗 -->
    <uploader-dialog @change="fileChange" @confirm="setFile" ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { i18n } from '@/main.js'

// 草稿或审批拒绝状态才能再次上传
export default {
  components: {
    UploaderDialog: () => import('./uploaderDialog')
  },
  data() {
    return {
      data: {},
      uploadFileList: [] // 上传的附件(初始值赋值之前上传过的)
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100,
    listNumFormat(value) {
      const fileList = value.file ? JSON.parse(value.file) : []
      if (['20', '40'].includes(value.supplierStatus)) {
        return `${i18n.t('查看')}(${fileList.length})`
      } else {
        return `${i18n.t('上传附件')}(${fileList.length})`
      }
    }
  },
  computed: {
    isAvtive() {
      return (value) => {
        return value && value.length > 0
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.data.file && this.data.file.length) {
        this.uploadFileList = JSON.parse(this.data.file)
      }
    })
  },
  methods: {
    showFileBaseInfo() {
      const dialogParams = {
        fileData: cloneDeep(this.uploadFileList),
        isView: ['20', '40'].includes(this.data?.column?.supplierStatus), // 是否仅可查看
        required: false, // 是否必须
        title: i18n.t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 行附件弹窗内文件变动
    fileChange(data) {
      this.uploadFileList = data
    },
    // 点击行附件上传的确认按钮
    setFile() {
      this.data.file = JSON.stringify(this.uploadFileList)
      this.$parent.$emit('fileChange', this.data.id, this.uploadFileList)
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-operable-title {
  display: inline-block;
  padding: 10px;

  color: #00469c;
  font-size: 14px;
  cursor: pointer;
}
</style>
