import Vue from 'vue'
import { i18n } from '@/main.js'
import modelName from '../components/modelName.vue' // 单元格上传
export let oneArr = [
  {
    width: '50',
    type: 'checkbox' //该列为checkox
    // allowEditing: false
  },
  {
    field: 'qualificationCode',
    headerText: i18n.t('资质编码')
    // allowEditing: false
  },
  {
    field: 'qualificationName',
    headerText: i18n.t('资质名称')
    // allowEditing: false
  },
  {
    field: 'modelName',
    headerText: i18n.t('附件模板'),
    cellTools: [],
    // allowEditing: false,
    editTemplate: () => {
      return {
        template: modelName
      }
    }
  },
  {
    field: 'supplierStatus', //213
    // field: 'supplierStatusTxT', //213
    headerText: i18n.t('状态'),
    // allowEditing: false
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: {
        0: i18n.t('待处理'),
        10: i18n.t('待提交'),
        20: i18n.t('待审批'),
        30: i18n.t('被驳回'),
        40: i18n.t('已确认')
      }
    }
  }
]

export let tweArr = [
  {
    field: 'remakeFieldValue',
    headerText: i18n.t('驳回原因'),
    // allowEditing: false,
    template: function () {
      return {
        template: Vue.component('spanState', {
          template: `<span>{{paramsName}}</span>`,
          data() {
            return {
              data: {},
              paramsName: ''
            }
          },
          mounted() {
            if (!this.data.remakeFieldValue) {
              this.paramsName = '-'
            } else {
              this.paramsName = this.data.remakeFieldValue
            }
            // if (this.data.supplierStatus == 0) {
            //   this.paramsName = "待处理";
            // }
            // if (this.data.supplierStatus == 10) {
            //   this.paramsName = "已保存，待提交";
            // }
            // if (this.data.supplierStatus == 20) {
            //   this.paramsName = "已提交（待审批）";
            // }
            // if (this.data.supplierStatus == 30) {
            //   this.paramsName = "被驳回";
            // }
            // if (this.data.supplierStatus == 40) {
            //   this.paramsName = "审核通过";
            // }
          }
        })
      }
    }
  }
]

export let pageConfig = (columnData, dataSource) => [
  {
    gridId: '9f808d86-caa9-41da-b297-1e7f0f6aef95',
    useToolTemplate: false,

    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [[{ id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') }]]
      // tools: [[]]
    },
    grid: {
      // editSettings: {
      //   allowEditing: true,
      //   allowAdding: true,
      //   allowDeleting: true,
      //   mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
      //   showConfirmDialog: false,
      //   // showDeleteConfirmDialog: true,
      //   newRowPosition: 'Top'
      // },
      allowPaging: false,
      columnData,
      dataSource,
      queryCellInfo: null,
      class: 'pe-edit-grid custom-toolbar-grid'
    }
  }
]
