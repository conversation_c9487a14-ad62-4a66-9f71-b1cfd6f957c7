import { i18n } from '@/main.js'
import Vue from 'vue'
import utils from '@/utils/utils'

export const dialogColumns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'attachment',
    headerText: i18n.t('附件'),
    cellTools: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e[0].fileName
      }
    }
  },
  {
    width: 200,
    field: 'unqualifiedDesc',
    headerText: i18n.t('描述')
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{ time }}</div></div>`,
          data: function () {
            return { data: {}, time: null }
          },
          mounted() {
            this.time = utils.formateTime(new Date(Number(this.data.uploadTime)))
          }
        })
      }
    }
  }
]
