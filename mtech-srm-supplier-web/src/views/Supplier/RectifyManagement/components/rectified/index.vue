<template>
  <div class="rectified-content">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  methods: {
    // 下载文件
    handleDownloadFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPublicFile({
          id: data.feedbackAttachment[0].fileId
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({
            fileName: data.feedbackAttachment[0].fileName,
            blob: res.data
          })
        })
    },
    handleClickCellTitle(e) {
      if (e.field === 'feedbackAttachment') {
        this.handleDownloadFile(e.data)
      } else if (e.field == 'rectificationCode') {
        this.$router.push({
          name: `sup-rectification-detail`,
          query: {
            type: 'detail',
            id: e.data['id'],
            refreshKey: new Date().getTime()
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.rectified-content {
  height: 100%;
}
</style>
