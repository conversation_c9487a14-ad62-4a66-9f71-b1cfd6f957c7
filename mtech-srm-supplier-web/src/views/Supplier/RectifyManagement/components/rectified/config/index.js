//维度设置Tab
import Vue from 'vue'
import { i18n } from '@/main'
import utils from '@/utils/utils'
import { download } from '@/utils/utils'

/**
 * 时间戳转日期显示
 * @param data: { formatString: <"YYYY-MM-dd"/"HH:MM:SS">, value: i18n.t("时间戳") }
 * @returns String
 */
export const timeNumberToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

const rectificationTypeMap = JSON.parse(sessionStorage.getItem('rectificationTypeMap'))

const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'rectificationCode',
    headerText: i18n.t('整改单号'),
    cellTools: []
  },
  {
    width: 200,
    field: 'rectificationType',
    headerText: i18n.t('整改类型'),
    valueConverter: {
      type: 'map',
      map: rectificationTypeMap
    }
  },
  {
    field: 'buyerCode',
    width: 200,
    headerText: i18n.t('客户编码')
  },
  {
    field: 'buyerName',
    width: 200,
    headerText: i18n.t('客户名称')
  },
  {
    field: 'templateType',
    headerText: i18n.t('整改模板'),
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(0,70,156,1);" @click="download">{{templateName}}</a>`,
          data() {
            return { data: {}, templateName: '' }
          },
          mounted() {
            this.templateName =
              this.data.templateType == '1' ? i18n.t('8D电子版') : this.data.template[0].fileName
          },
          methods: {
            download() {
              if (this.data.templateType == '1') {
                const a = document.createElement('a')
                a.href = 'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/supplier/8D电子版.xlsx'
                a.style.display = 'none'
                a.download = i18n.t('8D电子版')
                document.body.appendChild(a)
                a.click()
                document.body.removeChild(a)
              } else {
                this.$store.commit('startLoading')
                this.$API.fileService
                  .downloadPublicFile({
                    id: this.data.template[0].fileId
                  })
                  .then((res) => {
                    this.$store.commit('endLoading')
                    download({
                      fileName: this.data.template[0].fileName,
                      blob: res.data
                    })
                  })
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'deadline',
    headerText: i18n.t('整改截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  },
  // {
  //   field: 'feedbackAttachment',
  //   headerText: i18n.t('反馈'),
  //   cellTools: [],
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       if (e && e.length > 0) {
  //         return e[0].fileName
  //       }
  //     }
  //   }
  // },
  {
    field: 'status',
    headerText: i18n.t('单据状态'),
    valueConverter: {
      type: 'map',
      map: { 40: i18n.t('已完成') }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    width: 160,
    headerText: i18n.t('备注')
  }
]

export const pageConfig = [
  {
    gridId: '1cd18670-7183-441c-8132-6a1501480e2c',
    // toolbar,
    toolbar: [],
    grid: {
      columnData,
      asyncConfig: {
        url: '/supplier/tenant/supplier/partner/reform/history/list'
      }
    }
  }
]
