<template>
  <div class="detail-container">
    <div class="header">
      <div class="title">{{ $t('反馈') }}</div>
      <div class="operate-bar">
        <div v-if="!formDisabled" class="op-item mt-flex" @click="save">
          {{ $t('保存') }}
        </div>
        <div v-if="!formDisabled" class="op-item mt-flex" @click="submit">
          {{ $t('保存并提交') }}
        </div>
        <div class="op-item mt-flex" @click="backToBusinessConfig">
          {{ $t('返回') }}
        </div>
      </div>
    </div>
    <div class="form-warp">
      <mt-form ref="formInstance" class="form-box" :model="formModel" :rules="rules">
        <mt-form-item :label="$t('整改单号')" label-style="top" prop="rectificationCode">
          <mt-input v-model="formModel.rectificationCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('单据状态')" label-style="top" prop="status">
          <mt-input :value="statusMap[formModel.status]" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('整改类型')" label-style="top" prop="rectificationType">
          <mt-input
            :value="rectificationTypeMap[formModel.rectificationType]"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('供应商')" label-style="top" prop="supplierName">
          <mt-input v-model="formModel.supplierName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('单据来源')" label-style="top" prop="documentSource">
          <mt-input
            :value="documentSourceMap[formModel.documentSource]"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('来源单据号')" label-style="top" prop="sourceApplyCode">
          <mt-input :value="formModel.sourceApplyCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('整改截止日期')" label-style="top" prop="deadline">
          <mt-input :value="formateDate(formModel.deadline)" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('整改模板')" label-style="top" prop="template">
          <div class="download-input">
            <mt-input :value="filterTemplate" :disabled="true"></mt-input>
            <mt-button @click="downloadTemplate">{{ $t('下载') }}</mt-button>
          </div>
        </mt-form-item>
        <mt-form-item :label="$t('缺陷附件')" label-style="top" prop="defect">
          <div class="download-input">
            <mt-input :value="filterResFile" :disabled="true"></mt-input>
            <mt-button @click="downloadResFile">{{ $t('下载') }}</mt-button>
          </div>
        </mt-form-item>
        <mt-form-item
          class="form-item radio-item"
          :label="$t('整改结果附件')"
          label-style="top"
          prop="feedbackAttachment"
        >
          <mt-common-uploader
            :is-single-file="true"
            :accept="acceptType"
            :save-url="saveUrl"
            :download-url="downloadUrl"
            :disabled="formDisabled"
            type="line"
            v-model="formModel.feedbackAttachment"
          ></mt-common-uploader>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('不良大类')"
          label-style="top"
          prop="defectBigCateg"
        >
          <mt-input :value="formModel.defectBigCateg" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-col :span="24">
          <mt-form-item :label="$t('备注')" label-style="top" prop="remark">
            <mt-input v-model="formModel.remark" :disabled="true" width="300%"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-form>
    </div>
    <div class="table-content">
      <div class="table-title">{{ $t('缺陷明细') }}</div>
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
      </mt-template-page>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import {
  dialogColumns,
  detailColumns,
  statusMap,
  documentSourceMap,
  rectificationTypeMap
} from './config/index'
import { download, timestampToDate } from '@/utils/utils'
import commonData from '@/utils/constant'
import { cloneDeep } from 'lodash'

export default {
  data() {
    return {
      acceptType: ['.xls', '.xlsx'],
      saveUrl: commonData.publicFileUrl,
      downloadUrl: commonData.downloadUrl,
      templateName: '',
      templateFileId: '',
      pageConfig: [
        {
          gridId: 'fbbf80e0-5e37-40e0-9471-adc9d98838d3',
          useToolTemplate: false,
          grid: {
            height: 'calc(100vh - 580px)',
            allowPaging: false,
            columnData: this.$route.query?.type === 'edit' ? dialogColumns : detailColumns,
            lineIndex: 1,
            dataSource: []
          }
        }
      ],
      formModel: {
        feedbackAttachment: []
      },
      rules: {
        feedbackAttachment: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ]
      },
      uploadInfo: {}, // 上传后信息
      statusMap,
      documentSourceMap,
      rectificationTypeMap
    }
  },
  computed: {
    filterTemplate() {
      let res = ''
      if (this.formModel.template && Array.isArray(this.formModel.template)) {
        if (this.formModel.templateType == 1) {
          res = this.$t('8D电子版') + '.xlsx'
        } else {
          res = this.formModel.template[0]?.fileName || ''
        }
      }
      return res
    },
    filterResFile() {
      let res = ''
      if (this.formModel.defect && Array.isArray(this.formModel.defect)) {
        res = this.formModel.defect[0]?.fileName || ''
      }
      return res
    },
    pageType() {
      return this.$route?.query?.type || 'add'
    },
    isEdit() {
      return this.pageType === 'edit' || this.pageType === 'edit'
    },
    formId() {
      return this.$route?.query?.id
    },
    formDisabled() {
      return this.formModel.status > '30' || this.pageType === 'detail'
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    downloadTemplate() {
      if (!this.templateFileId) return
      if (this.formModel.templateType == 1) {
        const a = document.createElement('a')
        a.href = 'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/supplier/8D电子版.xlsx'
        a.style.display = 'none'
        a.download = this.$t('8D电子版')
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      } else {
        this.$store.commit('startLoading')
        this.$API.fileService
          .downloadPublicFile({
            id: this.templateFileId
          })
          .then((res) => {
            this.$store.commit('endLoading')
            download({ fileName: this.templateName, blob: res.data })
          })
      }
    },
    downloadResFile() {
      if (!Array.isArray(this.formModel.defect) || !this.formModel.defect[0]?.fileName) {
        return
      }
      const defectFile = this.formModel.defect
      // const fileId = defectFile[0]?.id
      const id = defectFile[0]?.fileId
      const fileName = defectFile[0]?.fileName
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPublicFile({
          id
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: fileName, blob: res.data })
        })
    },
    initData() {
      this.$API.rectifyManagement.querySupDetail(this.formId).then((res) => {
        if (res.code === 200) {
          this.formModel = { ...this.formModel, ...res.data }
          this.templateName =
            res.data.templateType == 1
              ? this.$t('8D电子版')
              : res.data.template.length > 0
              ? res.data.template[0]?.fileName
              : ''
          this.templateFileId = res.data.templateType == 2 ? res.data.template[0]?.fileId : ''
          if (this.formModel.feedbackAttachment.length > 0) {
            this.formModel.feedbackAttachment.map((item) => {
              item.id = item.fileId
            })
          }
          this.formModel.feedbackAttachment = this.formModel.feedbackAttachment || []
          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.buyerRectificationExtDTOList)
          // 被驳回
          // if (res.data.status === 30 && res.data.feedbackAttachment?.length) {
          //   this.uploadInfo = { ...res.data.feedbackAttachment[0] }
          // }
        }
      })
    },
    formateDate(val) {
      if (!val) return null
      return timestampToDate(val)
    },
    handleClickCellTitle(e) {
      if (e.field == 'attachment') {
        // this.preview(e.data?.attachment[0])

        // 此处attchment是缺陷明细表里的attechment
        const fileList = e.data?.attachment
        if (!fileList || !fileList.length) return
        const fileId = fileList[0]?.id
        const fileName = fileList[0]?.fileName
        this.$store.commit('startLoading')
        this.$API.fileService
          .downloadPublicFile({
            id: fileId
          })
          .then((res) => {
            this.$store.commit('endLoading')
            download({ fileName: fileName, blob: res.data })
          })
      }
    },
    preview(item) {
      let params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data, componentData } = e
      const { id } = tool || {}
      if (id === 'edit') {
        this.$dialog({
          modal: () => import('./components/uploadDialog.vue'),
          data: {
            title: this.$t('编辑缺陷'),
            isEdit: true,
            info: data,
            index: componentData.index,
            factoryData: this.factoryData
          },
          success: (data) => {
            let _tempData = cloneDeep(this.pageConfig[0].grid.dataSource)
            _tempData[componentData.index] = data
            this.$set(this.pageConfig[0].grid, 'dataSource', _tempData)
          }
        })
      }
    },
    save(saveType) {
      saveType = saveType === 'saveAndSubmit' ? saveType : 'supSave'
      this.$refs.formInstance.validate((valid) => {
        if (valid) {
          // if (utils.isEmpty(this.uploadInfo)) {
          //   this.$toast({
          //     content: this.$t('请选择文件上传！'),
          //     type: 'warning'
          //   })
          //   return
          // }
          let params = {
            attachment: this.formModel.feedbackAttachment,
            ...this.formModel,
            id: this.formModel.id,
            buyerRectificationExtDTOList: this.pageConfig[0].grid.dataSource
          }
          const defectListRes = this.pageConfig[0].grid.dataSource.some(
            (item) => !item.reasonAnalysis || !item.improveMeasures
          )
          if (defectListRes) {
            this.$toast({
              content: this.$t('缺陷明细中有原因分析或者改善措施没有填写'),
              type: 'warning'
            })
            return
          }
          this.$API.rectifyManagement[saveType](params)
            .then((res) => {
              const { code, data } = res
              if (code == 200 && !utils.isEmpty(data)) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                if (saveType === 'saveAndSubmit') {
                  this.$router.replace({ name: 'sup-rectification' })
                }
              }
            })
            .catch((error) => {
              this.$toast({
                content: error.msg || this.$t('操作失败'),
                type: 'warning'
              })
            })
        }
      })
    },
    submit() {
      this.save('saveAndSubmit')
    },
    //返回列表页
    backToBusinessConfig() {
      this.$emit('cancel-function')
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.mt-form-item {
  width: calc(33% - 30px);
  min-width: 200px;
  display: inline-flex;
  margin-left: 10px;
  margin-right: 20px;
  .mt-input {
    width: 100%;
  }
  .download-input {
    display: flex;
    align-items: center;
    button {
      color: #0378d5 !important;
    }
  }
}
.detail-container {
  background: #fff;
  height: 100%;
  .header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      height: 100%;
      line-height: 40px;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      padding: 5px 10px;
    }
    .operate-bar {
      height: 100%;
      float: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;

      .op-item {
        cursor: pointer;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }
  }
  .table-content {
    padding: 10px;
    .table-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
    }
  }
}
</style>
