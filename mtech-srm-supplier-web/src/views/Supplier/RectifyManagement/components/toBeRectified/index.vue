<template>
  <div class="rectified-content">
    <mt-template-page
      ref="templateRef"
      :use-tool-template="false"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  methods: {
    // 下载文件
    handleDownloadFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPublicFile({
          id: data.feedbackAttachment[0].fileId
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({
            fileName: data.feedbackAttachment[0].fileName,
            blob: res.data
          })
        })
    },
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field === 'feedbackAttachment') {
        this.handleDownloadFile(e.data)
      } else if (e.field == 'rectificationCode') {
        this.$router.push({
          name: `sup-rectification-detail`,
          query: {
            type: 'detail',
            id: e.data['id'],
            refreshKey: new Date().getTime()
          }
        })
      }
    },
    handleClickToolBar(e) {
      const { toolbar, grid, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if ((!sltList || sltList.length <= 0) && toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      console.log('=e=', e, grid)
      if (toolbar.id === 'feedback') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不支持多条记录同时反馈'),
            type: 'warning'
          })
          return
        } else if (sltList[0].status == '20') {
          this.$toast({
            content: this.$t('无法操作待确认的数据'),
            type: 'warning'
          })
          return
        } else {
          this.$router.push({
            name: `sup-rectification-detail`,
            query: {
              type: 'edit',
              id: sltList[0]['id'],
              refreshKey: new Date().getTime()
            }
          })
        }
      } else if (toolbar.id === 'submit') {
        let idList = sltList.map((e) => e.id)
        let submitStatusList = sltList.map((e) => e.status)
        let _status = [10, 20, 30]
        let isInclude = _status.filter((v) => submitStatusList.includes(v))
        console.log('===', submitStatusList, isInclude)
        if (isInclude.length > 0) {
          this.$toast({
            content: this.$t('未反馈或待确认的数据无法提交'),
            type: 'warning'
          })
          return
        }
        this.submit(idList)
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'feedback') {
        // this.$dialog({
        //   modal: () => import('./components/uploadDialog.vue'),
        //   data: {
        //     title: this.$t('反馈'),
        //     id: data.id
        //   },
        //   success: () => {
        //     this.$refs.templateRef.refreshCurrentGridData()
        //   }
        // })
        this.$router.push({
          name: `sup-rectification-detail`,
          query: {
            type: 'edit',
            id: data.id,
            refreshKey: new Date().getTime()
          }
        })
      } else if (id == 'submit') {
        this.submit([data.id])
      }
    },
    submit(idList) {
      this.$API.rectifyManagement.submit({ ids: idList }).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.rectified-content {
  height: 100%;
}
</style>
