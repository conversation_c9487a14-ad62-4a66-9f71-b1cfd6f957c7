<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo">
        <mt-row>
          <mt-col :span="24">
            <!-- <mt-form-item prop="file" class="form-item">
              <div class="cell-upload">
                <div class="to-upload">
                  <input type="file" class="upload-input" ref="file" @change="chooseFiles" />
                  <div class="upload-box" v-show="!uploadInfo.fileName">
                    <div class="plus-icon"></div>
                    <div class="right-state">
                      <div class="plus-txt">
                        {{ $t('请拖拽文件或点击上传') }}
                      </div>
                      <div class="warn-text">
                        {{
                          $t(
                            '注：文件大小不超过50M，文件格式支持xls/xlsx/doc/docx/pdf/ppt/pptx/png/jpg/zip/rar'
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="has-file" v-if="!!uploadInfo.fileName">
                  <div class="left-info">
                    <div class="file-title">
                      <span class="text-ellipsis">{{ uploadInfo.fileName }}</span>
                      <span>{{ uploadInfo.fileSize }} kb</span>
                    </div>
                  </div>
                  <mt-icon
                    name="icon_Close_2"
                    class="close-icon"
                    @click.native="handleRemove"
                  ></mt-icon>
                </div>
              </div>
            </mt-form-item> -->
            <mt-form-item class="form-item" :label="$t('附件')" prop="fileName" label-style="top">
              <mt-common-uploader
                :is-single-file="false"
                :save-url="saveUrl"
                :download-url="downloadUrl"
                type="line"
                v-model="uploadInfo"
              ></mt-common-uploader>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-form-item
          class="form-item"
          :label="$t('整改模板')"
          label-style="left"
          prop="supplierId"
        >
          <a
            style="font-size: 14px; color: rgba(99, 134, 193, 1)"
            @click="downloadFile(templateFileId, templateName)"
            >{{ templateName }}</a
          >
        </mt-form-item>
        <mt-form-item class="form-item radio-item" :label="$t('不合格项')"> </mt-form-item>
      </mt-form>

      <div class="slider-content-list">
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import commonData from '@/utils/constant'
import utils from '@/utils/utils'
let fileData = null
import { dialogColumns } from '../config/index'
import { download } from '@/utils/utils'

export default {
  data() {
    return {
      downloadUrl: commonData.downloadUrl,
      saveUrl: commonData.publicFileUrl,
      allowFileType: [
        'xls',
        'xlsx',
        'doc',
        'docx',
        'pdf',
        'ppt',
        'pptx',
        'png',
        'jpg',
        'zip',
        'rar'
      ],
      templateName: '',
      templateFileId: '',
      pageConfig: [
        {
          gridId: 'fbbf80e0-5e37-40e0-9471-adc9d98838b9',
          useToolTemplate: false,
          grid: {
            height: '250',
            allowPaging: false,
            columnData: dialogColumns,
            lineIndex: 0,
            dataSource: []
          }
        }
      ],
      formInfo: {
        attachment: []
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.saveAndSubmit,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并提交') }
        }
      ],
      uploadInfo: {} // 上传后信息
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    id() {
      return this.modalData.id
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'attachment') {
        this.preview(e.data?.attachment[0])
      }
    },
    preview(item) {
      let params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPublicFile({
          id: data.attachment[0].fileId
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: data.attachment[0].fileName, blob: res.data })
        })
    },
    downloadFile(templateFileId, templateName) {
      if (this.formInfo.templateType == 1) {
        const a = document.createElement('a')
        a.href = 'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/supplier/8D电子版.xlsx'
        a.style.display = 'none'
        a.download = this.$t('8D电子版')
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      } else {
        this.$store.commit('startLoading')
        this.$API.fileService
          .downloadPublicFile({
            id: templateFileId
          })
          .then((res) => {
            this.$store.commit('endLoading')
            download({ fileName: templateName, blob: res.data })
          })
      }
    },
    initData() {
      this.$API.rectifyManagement.querySupDetail(this.id).then((res) => {
        if (res.code === 200) {
          this.formInfo = { ...this.formInfo, ...res.data }
          this.templateName =
            res.data.templateType == 1 ? this.$t('8D电子版') : res.data.template[0].fileName
          this.templateFileId = res.data.templateType == 2 ? res.data.template[0].fileId : ''
          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.buyerRectificationExtDTOList)
          if (res.data.status === 30) {
            this.uploadInfo = { ...res.data.feedbackAttachment[0] }
          }
          this.show()
        }
      })
    },
    chooseFiles(data) {
      this.$loading()
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      }
      console.log('files', files)
      let _tempInfo = files[0].name.split('.')
      if (_tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])) {
        this.$toast({
          content: this.$t('文件格式仅支持' + this.allowFileType.join('/')),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('UploadFiles', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      fileData = _data
      this.uploadFile()
    },

    // 上传图片
    uploadFile() {
      this.$refs.file.value = ''
      this.$API.SupplierPunishment.fileUpload(fileData)
        .then((res) => {
          const { code, data } = res
          this.$hloading()
          if (code == 200 && !utils.isEmpty(data)) {
            this.uploadInfo = {
              ...data,
              fileId: data.id
            }
          } else {
            this.uploadInfo = {}
            this.$toast({
              content: data.msg || this.$t('上传失败'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.uploadInfo = {}
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('上传失败'),
            type: 'warning'
          })
        })
    },

    // 移除文件
    handleRemove() {
      this.uploadInfo = {}
      fileData = null
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    save(saveType) {
      saveType = saveType === 'saveAndSubmit' ? saveType : 'supSave'
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (utils.isEmpty(this.uploadInfo)) {
            this.$toast({
              content: this.$t('请选择文件上传！'),
              type: 'warning'
            })
            return
          }
          let params = {
            attachment: [{ ...this.uploadInfo, fileUrl: this.uploadInfo.url }],
            id: this.formInfo.id
          }
          this.$API.rectifyManagement[saveType](params)
            .then((res) => {
              const { code, data } = res
              if (code == 200 && !utils.isEmpty(data)) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$emit('confirm-function')
              } else {
                this.$toast({
                  content: data.msg || this.$t('上传失败'),
                  type: 'warning'
                })
              }
            })
            .catch((error) => {
              this.$toast({
                content: error.msg || this.$t('上传失败'),
                type: 'warning'
              })
            })
        }
      })
    },
    saveAndSubmit() {
      this.save('saveAndSubmit')
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'download') {
        this.handleDownloadFile(data)
        // this.downloadFile(
        //   data.attachment[0].fileId,
        //   data.attachment[0].fileName
        // );
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.uploader-box {
  .mt-form-item__content {
    flex: 1;
  }
}
</style>
<style lang="scss" scoped>
.uploader-box {
  padding: 10px 20px 0;
}

.cell-upload {
  margin-top: 15px;
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          width: 100%;
          display: inline-block;
          vertical-align: middle;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
