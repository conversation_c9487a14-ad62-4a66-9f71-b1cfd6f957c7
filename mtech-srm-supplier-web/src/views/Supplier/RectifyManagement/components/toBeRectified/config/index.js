//维度设置Tab
import Vue from 'vue'
import { i18n } from '@/main'
import utils from '@/utils/utils'
import { download } from '@/utils/utils'

/**
 * 时间戳转日期显示
 * @param data: { formatString: <"YYYY-MM-dd"/"HH:MM:SS">, value: i18n.t("时间戳") }
 * @returns String
 */
export const timeNumberToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

export const rectificationTypeMap = JSON.parse(sessionStorage.getItem('rectificationTypeMap') || {})
export const statusMap = {
  10: i18n.t('待处理'),
  19: i18n.t('已保存'),
  20: i18n.t('待确认'),
  30: i18n.t('被驳回'),
  50: i18n.t('已逾期')
}
export const documentSourceMap = {
  0: i18n.t('手工创建'),
  10: i18n.t('现场评审单'),
  20: i18n.t('QMS报检单'),
  30: i18n.t('限期整改'),
  40: i18n.t('绩效整改')
}

const toolbar = [
  { id: 'feedback', icon: 'icon_solid_Newinvitation', title: i18n.t('反馈') },
  { id: 'submit', icon: 'icon_solid_edit', title: i18n.t('提交') }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'rectificationCode',
    headerText: i18n.t('整改单号'),
    cellTools: []
  },
  {
    width: 200,
    field: 'rectificationType',
    headerText: i18n.t('整改类型'),
    valueConverter: {
      type: 'map',
      map: rectificationTypeMap
    }
  },
  {
    field: 'purchaserCode',
    width: 200,
    headerText: i18n.t('客户编码')
  },
  {
    field: 'purchaserName',
    width: 200,
    headerText: i18n.t('客户名称')
  },
  {
    field: 'templateType',
    headerText: i18n.t('整改模板'),
    template: function () {
      return {
        template: Vue.component('demandDesc', {
          template: `<a style="font-size: 14px;color: rgba(0,70,156,1);" @click="download">{{templateName}}</a>`,
          data() {
            return { data: {}, templateName: '' }
          },
          mounted() {
            this.templateName =
              this.data.templateType == '1' ? i18n.t('8D电子版') : this.data.template[0].fileName
          },
          methods: {
            download() {
              if (this.data.templateType == '1') {
                const a = document.createElement('a')
                a.href = 'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/supplier/8D电子版.xlsx'
                a.style.display = 'none'
                a.download = i18n.t('8D电子版')
                document.body.appendChild(a)
                a.click()
                document.body.removeChild(a)
              } else {
                this.$store.commit('startLoading')
                this.$API.fileService
                  .downloadPublicFile({
                    id: this.data.template[0].fileId
                  })
                  .then((res) => {
                    this.$store.commit('endLoading')
                    download({
                      fileName: this.data.template[0].fileName,
                      blob: res.data
                    })
                  })
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'deadline',
    headerText: i18n.t('整改截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  },
  // {
  //   field: 'attachment',
  //   headerText: i18n.t('反馈'),
  //   cellTools: [],
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       if (e.length > 0) {
  //         return e[0].fileName
  //       } else {
  //         return ''
  //       }
  //     }
  //   }
  // },
  {
    field: 'status',
    headerText: i18n.t('单据状态'),
    valueConverter: {
      type: 'map',
      map: statusMap
    },
    cellTools: [
      {
        id: 'feedback',
        icon: 'icon_solid_Newinvitation',
        title: i18n.t('反馈'),
        visibleCondition: (data) => {
          return data['status'] == 10 || data['status'] == 19 || data['status'] == 30
        }
      },
      {
        id: 'submit',
        icon: 'icon_solid_edit',
        title: i18n.t('提交'),
        visibleCondition: (data) => {
          return data['status'] == 19
        }
      }
    ]
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'yyyy-MM-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      type: 'date'
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: 160
  }
]

export const pageConfig = [
  {
    gridId: '03ce24c7-a698-4b8e-9340-a5fdde16d4e1',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url: '/supplier/tenant/supplier/partner/reform/wait/list',
        serializeList: (list) => {
          list.forEach((e) => {
            let nameList = []
            let codeList = []
            e.purchaserCompanyInfo.forEach((item) => {
              nameList.push(item.purchaserName)
              codeList.push(item.purchaserCode)
            })
            e.purchaserName = nameList.join()
            e.purchaserCode = codeList.join()
          })
          return list
        }
      }
    }
  }
]

export const dialogColumns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 160,
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: 160,
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: 160,
    field: 'unqualifiedDesc',
    headerText: i18n.t('缺陷描述')
  },
  {
    width: 160,
    field: 'reviewProjectDescription',
    headerText: i18n.t('项目评审说明')
  },
  {
    field: 'attachment',
    headerText: i18n.t('附件'),
    cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e[0]?.fileName || ''
      }
    }
  },
  {
    width: 160,
    field: 'reasonAnalysis',
    headerText: i18n.t('原因分析'),
    cssClass: '',
    cellTools: ['edit']
  },
  {
    field: 'improveMeasures',
    headerText: i18n.t('改善措施'),
    cssClass: '',
    cellTools: ['edit']
  }
]

export const detailColumns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 160,
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: 160,
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: 160,
    field: 'unqualifiedDesc',
    headerText: i18n.t('缺陷描述')
  },
  {
    width: 160,
    field: 'reviewProjectDescription',
    headerText: i18n.t('项目评审说明')
  },
  {
    field: 'attachment',
    headerText: i18n.t('附件'),
    cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e[0]?.fileName || ''
      }
    }
  },
  {
    width: 160,
    field: 'reasonAnalysis',
    headerText: i18n.t('原因分析')
  },
  {
    field: 'improveMeasures',
    headerText: i18n.t('改善措施')
  }
]
