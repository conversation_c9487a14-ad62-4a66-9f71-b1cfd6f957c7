<template>
  <mt-dialog ref="unqualified-dialog" :buttons="buttons" :header="modalData.title">
    <div class="unqualified-dialog">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { dialogColumns } from '../config/index'
import { download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '1d497940-e9e8-46a8-a334-d3c74fa1c1b0',
          grid: {
            allowPaging: false,
            columnData: dialogColumns,
            asyncConfig: {
              url: `/supplier/tenant/buyer/partner/reform/unqualified/${this.modalData.id}`,
              methods: 'get',
              recordsPosition: 'data'
            }
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('关闭') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.$refs['unqualified-dialog'].ejsRef.show()
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'attachment') {
        this.preview(e.data?.attachment[0])
      }
    },
    preview(item) {
      let params = {
        id: item.fileId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPublicFile({
          id: data.attachment[0].fileId
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: data.attachment[0].fileName, blob: res.data })
        })
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'download') {
        this.handleDownloadFile(data)
      }
    },
    cancel() {
      this.$refs['unqualified-dialog'].ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.unqualified-dialog {
  padding-top: 40px;
  // height: 100%;
  height: 467px;
}
</style>
