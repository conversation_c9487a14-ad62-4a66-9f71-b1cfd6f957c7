<!--
  供应商整改管理
-->
<template>
  <div class="rectify-content">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      v-if="rectificationTypeMap"
    >
      <!-- 待整改 -->
      <to-be-rectified slot="slot-0" index="0"></to-be-rectified>
      <!-- 已整改 -->
      <rectified slot="slot-1" index="1"></rectified>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  components: {
    //待整改
    toBeRectified: () =>
      import(/* webpackChunkName: "supplierToBeRectified" */ './components/toBeRectified'),
    // 整改历史
    rectified: () => import(/* webpackChunkName: "supplierRectified" */ './components/rectified')
  },
  created() {
    this.getTypelist()
  },
  data() {
    return {
      rectificationTypeMap: null,
      pageConfig: [
        {
          gridId: '965e133b-e1bb-46ce-bf89-2c76961d657f',
          title: this.$t('待整改')
        },
        {
          gridId: '02996b61-123c-43c0-816b-24cd19378145',
          title: this.$t('整改历史')
        }
      ]
    }
  },
  methods: {
    getTypelist() {
      this.$API.masterData.queryDict({ dictCode: 'rectificationType' }).then((res) => {
        if (res.code === 200) {
          let _rectificationTypeMap = {}
          res.data.forEach((e) => {
            _rectificationTypeMap[e.itemCode] = e.name
          })
          this.rectificationTypeMap = _rectificationTypeMap
          sessionStorage.setItem('rectificationTypeMap', JSON.stringify(_rectificationTypeMap))
          console.log(' this.rectificationTypeMap', _rectificationTypeMap)
        }
      })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('rectificationTypeMap')
  }
}
</script>

<style lang="scss" scoped>
.rectify-content {
  width: 100%;
  height: 100%;
}
</style>
