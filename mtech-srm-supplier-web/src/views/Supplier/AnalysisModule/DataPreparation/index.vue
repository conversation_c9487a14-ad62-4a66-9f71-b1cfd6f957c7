<template>
  <div class="dataPre-box">
    <mt-template-page
      :template-config="tabConfig"
      :current-tab="currentTab"
      @handleSelectTab="handleSelectTab"
    >
      <div class="DS" slot="slot-0">
        <div class="DS-content">
          <div class="left-box">
            <div class="DS-top">
              <!-- <mt-select
                :dataSource="dataArr"
                :showClearButton="true"
                :placeholder="$t('请选择区域')"
              ></mt-select>
            </div>
            <div class="DS-left">
                <div class="left-title">
                <MtIcon
                  name="icon_solid_Createproject"
                  style="margin-right: 10px"
                ></MtIcon>
                新增文件夹
              </div> -->
              <MtCommonTree
                id="MtCommonTree"
                :fields="fileds"
                :un-button="true"
                @onButton="onButton"
                @nodeSelected="nodeSelected"
              ></MtCommonTree>
              <!-- <mt-treeView
              :fields="fileds"
              :id="'pre-' + new Date().getTime()"
            ></mt-treeView> -->
            </div>
          </div>
          <div class="DS-right">
            <mt-template-page
              ref="templateRef"
              :hidden-tabs="false"
              :padding-top="true"
              :template-config="recodeTable"
              @handleClickToolBar="handleClickToolBar"
            ></mt-template-page>
          </div>
        </div>
      </div>
      <div slot="slot-1" style="height: 100%">
        <mt-template-page
          :template-config="scoreTable"
          @handleClickCellTitle="handleClickCellTitle"
        ></mt-template-page>
      </div>
    </mt-template-page>
    <div>
      <mt-dialog
        ref="toast"
        :header="$t('时间段替换')"
        :buttons="buttons"
        :open="onOpen"
        size="small"
        :show-close-icon="true"
      >
        <div style="padding: 24px">
          <div>{{ $t('请选择要替换数据的时间段') }}</div>
          <div>
            <mt-date-range-picker
              v-model="newDate"
              @change="change"
              :placeholder="$t('请选择要替换数据的时间段')"
            ></mt-date-range-picker>
          </div>
        </div>
      </mt-dialog>
      <mt-dialog
        ref="dialog"
        css-class="bule-bg"
        :width="900"
        min-height="600"
        :buttons="buttonss"
        :header="header"
        @close="handleClose"
      >
        <div class="import-process">
          <div :class="headerFlag ? 'import' : 'choose'">
            <i>1</i><span>{{ $t('选择文件') }}</span>
          </div>
          <ul>
            <li v-for="index in 4" :key="index"></li>
          </ul>
          <div :class="headerFlag ? 'choose' : 'import'">
            <i>2</i><span>{{ $t('导入文件') }}</span>
          </div>
        </div>
        <div class="uploader-box">
          <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
            <mt-form-item prop="file" class="form-item">
              <div class="cell-upload">
                <div class="has-file" v-if="uploadInfo.name">
                  <div class="left-info">
                    <div class="file-title">
                      <span class="text-ellipsis">{{ uploadInfo.name }}</span>
                      <span>{{ uploadInfo.size }} kb</span>
                    </div>
                  </div>
                  <mt-icon
                    style="margin-left: 20px"
                    name="icon_Close_2"
                    class="close-icon"
                    @click.native="handleRemove"
                  ></mt-icon>
                </div>
                <div v-else class="to-upload">
                  <input
                    type="file"
                    accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    class="upload-input"
                    @change="chooseFiles"
                    ref="file"
                  />
                  <div class="upload-box">
                    <div class="plus-icon"></div>
                    <div class="right-state">
                      <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
                      <div class="warn-text">
                        {{ $t('文件最大不可超过50M， 文件格式仅支持.xls .xlsx') }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </mt-form-item>
            <div class="template-to-import">
              {{ $t('为了保证数据导入顺利，推荐您下载使用')
              }}<span class="import-the-template" @click="importTemplate">{{ $t('导入模板') }}</span
              >,{{ $t('并按照规范示例录入数据') }}
            </div>
            <div class="specification">
              {{ $t('上传的 Excel 表符合以下规范') }}:<br />
              • {{ $t('文件大小不超过20MB，这里是规范限制文案') }}<br />
              • {{ $t('仅支持 (*.xls 和 *.xlsx)文件') }}<br />
              •
              {{
                $t('请确保您需要导入的sheet表头中不包含空的单元格，否则该sheet页数据系统将不做导入')
              }}
            </div>
          </mt-form>
        </div>
      </mt-dialog>
    </div>
  </div>
</template>
<script>
import { columnShare, recodeColumn } from './config/columns'

let fileData = null
export default {
  data() {
    return {
      formInfo: {
        remark: ''
      },
      rules: {},
      currentTab: 0,
      buttonss: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      uploadInfo: {}, // 上传后信息
      headerFlag: false,

      newDate: [],
      dataArr: [],
      tabConfig: [
        {
          gridId: '316be5fb-9aaf-4692-9b23-6b7c6b44b2d8',
          title: this.$t('数据集')
        },
        {
          gridId: '76886d60-1fcc-4ec7-8e7f-83a6145fec80',
          title: this.$t('手动打分')
        }
      ],
      fileds: {
        dataSource: [],
        id: 'id',
        parentID: 'parentID',
        text: 'name',
        child: 'childrenList'
        // hasChildren: "childrenList",
      },
      type: 1,
      recodeTable: this.recodeTableFn(),
      purchaseEnterpriseId: '',
      scoreTable: [
        {
          gridId: 'ffa020ab-4339-429e-84de-14138acbb1ef',
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: [
            // { id: "tijiao", icon: "icon_solid_Createproject", title: "提交" },
          ],
          grid: {
            columnData: columnShare,

            asyncConfig: {
              url: '/analysis/tenant/buyer/assess/archive/manual/pageList'
            }
          }
        }
      ],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: () => {
            console.log('确认')
          },
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      obj: {},
      modalData: {
        title: '',
        isEdit: false,
        fileInfo: null,
        applyInfo: null
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title || this.$t('上传/导入')
    },
    isEdit() {
      return this.modalData.isEdit
    },
    fileInfo() {
      return this.modalData.fileInfo
    },
    applyInfo() {
      return this.modalData.applyInfo
    }
  },
  created() {
    console.log(this.$route.query)
    //
    if (this.$route.query.tab) {
      this.currentTab = Number(this.$route.query.tab)
    }
    this.init()
  },
  methods: {
    handleSelectTab(e) {
      if (e != this.currentTab) {
        this.currentTab = e
        this.init()
        this.recodeTable = this.recodeTableFn()
      }
    },
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    recodeTableFn(data = {}) {
      // console.log(data)
      // // console.log(this.type)
      // let type=this.type||1
      // let params={
      //     "purchaseEnterpriseId":this.purchaseEnterpriseId||'',
      //     "tableCode":data.tableCode||'',
      // }
      // let recordsPosition=data.tableCode?'data.'+data.tableCode+'.records':''
      // if(type==2){
      //   params={
      //       "tableId":data.tableId||'',
      //   }
      //   recordsPosition='data'
      // }
      return [
        {
          gridId: '5ec1c78f-b398-4193-a2a7-1de0de80fb10',
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: [
            // { id: "add", icon: "icon_solid_Createproject", title: "追加" },
            // { id: "timeChange", icon: "icon_solid_Createprojet", title: "时间段替换" },
            // { id: "history", icon: "icon_solid_Createproject", title: "历史版本" },
            // { id: "tableStructure", icon: "icon_solid_Createproject", title: "表结构展示" },
          ],
          grid: {
            // columnData: recodeColumn(data.tableCode,type),
            columnData: recodeColumn(),
            asyncConfig: {
              // url: type==1?"/analysis/tenant/buyer/assess/data/field/queryBasicDate":'/analysis/tenant/buyer/assess/data/field/query',
              url: data.tableCode ? '/analysis/tenant/buyer/assess/rawMaterials/pageQuery' : ''
              // params,
              // recordsPosition:recordsPosition
            }
          }
        }
      ]
    },
    datafor(data) {
      data.forEach((ele) => {
        ele['name'] = ele.catalogueName
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.datafor(ele.childrenList)
        }
      })
    },
    init() {
      // this.fileds
      this.$API.DataPreparation.queryAllCatalogue({
        parentId: 0
      }).then((res) => {
        this.$set(this.fileds, 'dataSource', res.data)
        this.datafor(this.fileds.dataSource)
        // this.fileds.dataSource=res.data
        console.log(this.fileds)
        console.log(res)
      })
    },
    chooseFiles(data) {
      console.log(this.$t('点击上传'), data)
      this.$loading()
      let { files } = data.target
      console.log(files)
      console.log(files[0], files[0].name)
      if (
        ![
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ].includes(files[0].type)
      ) {
        this.$toast({ content: this.$t('暂不支持该文件类型'), type: 'warning' })
        this.$hloading()
        return
      }
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        this.headerFlag = false
        // 您未选择需要上传的文件
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('excelFile', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true

          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }

      this.uploadInfo = files[0]
      console.log(this.uploadInfo)
      _data.append('table', this.obj.tableCode) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      fileData = _data
      this.$refs.file.value = ''
      this.$hloading()
      // this.uploadFile();
    },

    // 移除文件
    handleRemove() {
      this.uploadInfo = {}
      fileData = null
      this.headerFlag = false
      console.log('点击了remove-file', this.data)
    },
    importTemplate() {
      console.log('为保证数据下载使用顺利,推荐您下载使用导入模块')
    },
    show() {
      this.uploadInfo = {}
      fileData = null
      this.headerFlag = false
      console.log(this.headerFlag, fileData, this.uploadInfo)
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      if (!this.uploadInfo.size && !fileData) {
        this.$toast({ content: this.$t('请选择文件上传！'), type: 'warning' })
        return
      }
      console.log(this.uploadInfo)
      // let query={
      //   excelFile:this.uploadInfo,
      //   table:this.obj.tableCode||''
      // }
      this.$API.DataPreparation.importBasicData(fileData)
        .then((res) => {
          console.log(res)
          const { code, data } = res
          if (code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm-function', 'reload')
            this.$refs.dialog.ejsRef.hide()
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({ content: data.msg || this.$t('上传失败'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
        })
    },
    cancel() {
      console.log(123)
      // this.$emit("cancel-function");
      this.$refs.dialog.ejsRef.hide()
    },
    onOpen() {
      console.log('打开')
    },
    change(value) {
      console.log(value)
      console.log(this.newDate)
    },
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field == 'archiveCode') {
        this.$router.push({
          name: `data-preparation-detail`,
          query: {
            id: e.data.archiveId
          }
        })
      }
    },
    datafors(id, data) {
      data.forEach((ele) => {
        if (id == ele.id) {
          this.obj = {
            // purchaseEnterpriseId:ele.id,
            isTable: ele.isTable,
            tableId: ele.id,
            tableCode: ele.catalogueCode
          }
          if (this.obj.isTable == 1) {
            if (this.obj.tableCode == 'mt_buyer_assess_raw_materials') {
              this.recodeTable = this.recodeTableFn(this.obj)
            }
          }
          return
        }
        // ele['name']=ele.catalogueName
        if (ele.childrenList && ele.childrenList.length > 0) {
          this.datafors(id, ele.childrenList)
        }
      })
    },
    nodeSelected(item) {
      // this.purchaseEnterpriseId=item.nodeData.id
      this.datafors(item.nodeData.id, this.fileds.dataSource)
      // let obj={}

      // this.$refs.templateRef.refreshCurrentGridData();
    },
    onButton(item) {
      console.log(item)
      if (item.onBtn.id == 'timeChange') {
        // 新增
      } else if (item.onBtn.id == 'add') {
        this.show()
      }
    },
    // 点击表格工具栏
    handleClickToolBar(item) {
      //  追加
      console.log(item)
      if (item.toolbar.id == 'add') {
        if (this.obj.isTable == 1) {
          this.show()
          // this.$refs.dialog.ejsRef.show();
        }
      } else if (item.toolbar.id == 'tableStructure') {
        //时间段替换弹窗
        if (this.type == 1) {
          this.type = 2
        } else {
          this.type = 1
        }
        this.recodeTable = this.recodeTableFn(this.obj)
        // console.log('tableStructure')
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import 'components/css/bundle.css';
// /deep/ .common-template-page .repeat-template {
//   height: calc(100% - 50px);
// }
// /deep/.e-treeview {
//   height: 100%;
// }
// /deep/.e-treeview .e-ul {
//   height: 100%;
// }
.dataPre-box {
  height: 100%;
  .DS {
    height: 100%;
    .DS-content {
      width: 100%;
      height: 100%;
      display: flex;
      .left-box {
        width: 30%;
        height: 100%;
        background: rgba(255, 255, 255, 1);

        margin-right: 16px;
        .DS-top {
          width: 90%;
          height: 40px;
          line-height: 40px;
          margin: auto;
        }
        .DS-left {
          margin-top: 10px;
          width: 100%;
          height: calc(100% - 50px);
          border: 1px solid rgba(232, 232, 232, 1);
          border-radius: 6px;
          font-size: 14px;
          color: #4d5b6f;
          .left-title {
            height: 50px;
            line-height: 50px;
            padding-left: 20px;
          }
        }
      }

      .DS-right {
        width: 70%;
      }
    }
  }
}
//选择文件 导入文件
.import-process {
  width: 360px;
  height: 24px;
  margin: 45px auto 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    width: 40px;
    height: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    li {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #6386c1;
    }
  }

  .choose {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #6386c1;
      text-align: center;
      line-height: 24px;
      color: #fff;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      border-bottom: 2px solid #6386c1;
    }
  }
  .import {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid #6386c1;
      text-align: center;
      line-height: 24px;
      color: #6386c1;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: black;
      line-height: 24px;
    }
  }
}
//主体
.uploader-box {
  width: 100%;
  margin: 50px auto 0;
  .has-file {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20px;
    .text-ellipsis {
      padding-right: 20px;
    }
  }
  .form-box {
    width: 100%;
    .form-item {
      width: 820px;
      margin: 0 auto;
      .cell-upload {
        position: relative;
        width: 820px;
        height: 250px;
        background: rgba(251, 252, 253, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        margin: 0 auto;
        //覆盖的选择文件框
        .upload-input {
          width: 100%;
          height: 75%;
          background: rgba(251, 252, 253, 1);
          border: 1px solid rgba(232, 232, 232, 1);
          box-sizing: border-box;
          position: absolute;
          z-index: 2;
          top: 0;
          background: transparent;
          opacity: 0;
        }
        //添加文档和说明文字区域
        .upload-box {
          width: 100%;
          height: 100%;
          //十字架
          .plus-icon {
            width: 60px;
            height: 60px;
            position: relative;
            margin: 50px auto 0;
            border: 1px dashed #000;
            &::before {
              content: ' ';
              display: inline-block;
              width: 60px;
              height: 2px;
              background: #98aac3;
              position: absolute;
              top: 50%;
              left: -1px;
            }

            &::after {
              content: ' ';
              display: inline-block;
              width: 2px;
              height: 60px;
              background: #98aac3;
              position: absolute;
              top: -1px;
              left: 50%;
            }
          }
          //文字
          .right-state {
            text-align: center;

            .plus-txt {
              margin: 20px auto 0;
              width: 270px;
              height: 24px;
              font-size: 24px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(152, 170, 195, 1);
            }
            .warn-text {
              margin: 16px auto 0;
              font-size: 12px;
              width: 369px;
              height: 21px;
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(241, 62, 62, 1);
            }
          }
        }
      }
    }
  }
  //导入模板规范
  .template-to-import {
    width: 566px;
    height: 24px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin-top: 10px;
    margin-left: 15px;
    .import-the-template {
      color: #00469c;
    }
  }
  .specification {
    width: 639px;
    height: 98px;
    margin-top: 31px;
    margin-left: 15px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }
}
</style>
