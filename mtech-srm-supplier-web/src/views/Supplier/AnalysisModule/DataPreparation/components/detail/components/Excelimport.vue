<template>
  <mt-dialog
    ref="dialog"
    :width="900"
    min-height="700"
    :buttons="buttons"
    :header="header"
    @close="cancel"
  >
    <div class="import-process">
      <div :class="headerFlag ? 'import' : 'choose'">
        <i>1</i><span>{{ $t('选择文件') }}</span>
      </div>
      <ul>
        <li v-for="index in 4" :key="index"></li>
      </ul>
      <div :class="headerFlag ? 'choose' : 'import'">
        <i>2</i><span>{{ $t('导入文件') }}</span>
      </div>
    </div>
    <div class="uploader-box" v-show="!headerFlag">
      <div class="cell-upload">
        <div class="has-file" v-if="uploadInfo.name">
          <div class="left-info">
            <div class="file-title">
              <span class="text-ellipsis">{{ uploadInfo.name }}</span>
              <span>{{ uploadInfo.size }} kb</span>
            </div>
          </div>
          <mt-icon
            style="margin-left: 20px"
            name="icon_Close_2"
            class="close-icon"
            @click.native="handleRemove"
          ></mt-icon>
        </div>
        <div v-else class="to-upload">
          <input
            type="file"
            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ref="file"
            class="upload-input"
            @change="chooseFiles"
          />
          <div class="upload-box">
            <div class="plus-icon"></div>
            <div class="right-state">
              <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
              <div class="warn-text">
                {{ $t('文件最大不可超过50M， 文件格式仅支持.xls .xlsx') }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="template-to-import">
        {{ $t('为了保证数据导入顺利，推荐您下载使用') }}<span>{{ $t('导入模板') }}</span
        >,{{ $t('并按照规范示例录入数据') }}
      </div>
      <div class="specification">
        {{ $t('上传的 Excel 表符合以下规范') }}:<br />
        • {{ $t('文件大小不超过20MB，这里是规范限制文案') }}<br />
        • {{ $t('仅支持 (*.xls 和 *.xlsx)文件') }}<br />
        •
        {{ $t('请确保您需要导入的sheet表头中不包含空的单元格，否则该sheet页数据系统将不做导入') }}
      </div>
    </div>
    <div v-show="headerFlag" style="margin-top: 20px; height: 300px">
      <mt-DataGrid
        v-if="headerFlag"
        :enable-virtualization="true"
        height="400"
        :data-source="sampleData"
        :column-data="sampleColumns"
      ></mt-DataGrid>
    </div>
  </mt-dialog>
</template>

<script>
import { fileColumn } from '../config/columns'
import * as XLSX from 'xlsx'

export default {
  components: {},
  data() {
    return {
      headerFlag: false,
      uploadInfo: {},
      formInfo: {},
      sampleData: [],
      sampleColumns: fileColumn,
      errFlag: true
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.titl
    },
    archiveId() {
      return this.modalData.archiveId
    },
    buttons() {
      if (!this.headerFlag) {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirmFirstStep,
            buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
          }
        ]
      } else {
        if (this.errFlag) {
          return [
            {
              click: this.cancelSecondStep,
              buttonModel: { content: this.$t('下载导入文件'), cssClass: 'model-button-left' }
            },
            {
              click: this.cancel,
              buttonModel: { content: this.$t('取消') }
            },
            {
              click: this.confirmSecondStep,
              buttonModel: { isPrimary: 'true', content: this.$t('导入数据') }
            }
          ]
        } else {
          return [
            {
              click: this.cancel,
              buttonModel: { content: this.$t('取消') }
            },
            {
              click: this.confirmSecondStep,
              buttonModel: { isPrimary: 'true', content: this.$t('导入数据') }
            }
          ]
        }
      }
    }
  },
  mounted() {
    this.show()
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    handleRemove() {
      this.uploadInfo = {}
    },
    chooseFiles(data) {
      let { files } = data.target
      if (files.length < 1) {
        this.headerFlag = false
        return
      }
      if (
        ![
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ].includes(files[0].type)
      ) {
        this.$toast({ content: this.$t('暂不支持该文件类型'), type: 'warning' })
        return
      }
      if (files[0].size > 50 * 1024 * 1024) {
        this.$toast({
          content: this.$t('单个文件，限制50M')
        })
        return
      }
      this.uploadInfo = files[0]
      this.$refs.file.value = ''
    },
    confirmFirstStep() {
      if (!this.uploadInfo.name) {
        this.$toast({
          content: this.$t('请选择文件'),
          type: 'error'
        })
        return
      }
      let params = new FormData()
      params.append('archiveId', this.archiveId)
      params.append('excelFile', this.uploadInfo)
      this.$API.DataPreparation.fileUpload(params).then((res) => {
        if (res.code == 200) {
          this.sampleData = res.data
          this.errFlag = this.sampleData.some((item) => {
            return item.errorInfo != ''
          })
          this.headerFlag = true
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    cancelSecondStep() {
      let tableData = [
        [
          this.$t('错误信息'),
          this.$t('档案编码'),
          this.$t('模板名称'),
          this.$t('供应商编码'),
          this.$t('供应商名称'),
          this.$t('品类编码'),
          this.$t('品类'),
          this.$t('指标编码'),
          this.$t('指标名称'),
          this.$t('类型'),
          this.$t('指标类'),
          this.$t('维度'),
          this.$t('描述'),
          this.$t('评分范围'),
          this.$t('权重值'),
          this.$t('满分值'),
          this.$t('分配分值'),
          this.$t('得分'),
          this.$t('原因')
        ]
      ]
      this.sampleData.forEach((item) => {
        let arr = []
        arr.push(
          item.errorInfo,
          item.archiveCode,
          item.planName,
          item.supplierCode,
          item.supplierEnterpriseName,
          item.categoryCode,
          item.categoryName,
          item.indexCode,
          item.indexName,
          item.indexKind,
          item.indexClassName,
          item.dimensionName,
          item.indexDescribe,
          item.indexRange,
          item.scoreWeight,
          item.maxValue,
          item.assignPoints,
          item.score,
          item.reason
        )
        tableData.push(arr)
      })
      console.log(tableData)
      let ws = XLSX.utils.aoa_to_sheet(tableData)
      let wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, this.$t('供应商绩效评分模板'))
      const nowTimes = new Date().getTime()
      XLSX.writeFile(wb, this.$t(`供应商绩效评分模板_${nowTimes}.xlsx`))
    },
    confirmSecondStep() {
      let _this = this
      if (this.errFlag) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('当前导入有错误数据，本次导入只导入正确数据!')
          },
          success: () => {
            _this.$emit('confirm-function', this.sampleData)
          }
        })
      } else {
        this.$emit('confirm-function', this.sampleData)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.import-process {
  width: 360px;
  height: 24px;
  margin: 45px auto 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    width: 40px;
    height: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    li {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #6386c1;
    }
  }

  .choose {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #6386c1;
      text-align: center;
      line-height: 24px;
      color: #fff;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      border-bottom: 2px solid #6386c1;
    }
  }
  .import {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid #6386c1;
      text-align: center;
      line-height: 24px;
      color: #6386c1;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: black;
      line-height: 24px;
    }
  }
}
//主体
.uploader-box {
  width: 100%;
  margin: 50px auto 0;
  .has-file {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20px;
    .text-ellipsis {
      padding-right: 20px;
    }
  }
  .cell-upload {
    position: relative;
    width: 820px;
    height: 250px;
    background: rgba(251, 252, 253, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    margin: 0 auto;
    //覆盖的选择文件框
    .upload-input {
      width: 100%;
      height: 75%;
      background: rgba(251, 252, 253, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      box-sizing: border-box;
      position: absolute;
      z-index: 2;
      top: 0;
      background: transparent;
      opacity: 0;
    }
    //添加文档和说明文字区域
    .upload-box {
      width: 100%;
      height: 100%;
      //十字架
      .plus-icon {
        width: 60px;
        height: 60px;
        position: relative;
        margin: 50px auto 0;
        border: 1px dashed #000;
        &::before {
          content: ' ';
          display: inline-block;
          width: 60px;
          height: 2px;
          background: #98aac3;
          position: absolute;
          top: 50%;
          left: -1px;
        }

        &::after {
          content: ' ';
          display: inline-block;
          width: 2px;
          height: 60px;
          background: #98aac3;
          position: absolute;
          top: -1px;
          left: 50%;
        }
      }
      //文字
      .right-state {
        text-align: center;

        .plus-txt {
          margin: 20px auto 0;
          width: 270px;
          height: 24px;
          font-size: 24px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(152, 170, 195, 1);
        }
        .warn-text {
          margin: 16px auto 0;
          font-size: 12px;
          width: 369px;
          height: 21px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(241, 62, 62, 1);
        }
      }
    }
  }
  //导入模板规范
  .template-to-import {
    width: 566px;
    height: 24px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin-top: 10px;
    margin-left: 15px;
    .import-the-template {
      color: #00469c;
    }
  }
  .specification {
    width: 639px;
    height: 98px;
    margin-top: 31px;
    margin-left: 15px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }
}
</style>
<style lang="scss">
.model-button-left {
  margin-right: 630px;
  padding-left: 0;
}
</style>
