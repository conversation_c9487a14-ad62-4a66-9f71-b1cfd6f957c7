<template>
  <div class="presentbox">
    <div ref="line"></div>
    <div ref="offerNum"></div>
    <div ref="drawLine"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {}
  },
  mounted() {
    this.initLine()
    this.initOfferNum()
    this.drawLine()
  },
  methods: {
    initLine() {
      let lineEchart = echarts.init(this.$refs.line)
      lineEchart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '11%'
        },
        legend: {
          data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          name: this.$t('合格率'),
          type: 'value',
          axisLabel: {
            formatter: '{value} %'
          }
        },
        series: [
          {
            name: 'Email',
            type: 'line',
            stack: 'Total',
            data: [120, 132, 101, 134, 90, 230, 210],
            markLine: {
              label: {
                show: true,
                formatter: this.$t('合格线')
              },
              lineStyle: {
                color: '#000',
                width: 2
              },
              data: [
                {
                  name: this.$t('Y 轴值为 100 的水平线'),
                  yAxis: '1000'
                }
              ]
            }
          },
          {
            name: 'Union Ads',
            type: 'line',
            stack: 'Total',
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: 'Video Ads',
            type: 'line',
            stack: 'Total',
            data: [150, 232, 201, 154, 190, 330, 410]
          },
          {
            name: 'Direct',
            type: 'line',
            stack: 'Total',
            data: [320, 332, 301, 334, 390, 330, 320]
          },
          {
            name: 'Search Engine',
            type: 'line',
            stack: 'Total',
            data: [820, 932, 901, 934, 1290, 1330, 1320]
          }
        ]
      })
    },
    initOfferNum() {
      let offer = echarts.init(this.$refs.offerNum)
      offer.setOption({
        title: {
          text: 'World Population'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['Brazil', 'Indonesia', 'USA', 'India', 'China', 'World']
        },
        series: [
          {
            name: '2011',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230]
          }
        ]
      })
    },
    drawLine() {
      let myChart = echarts.init(this.$refs.drawLine)
      let option = {
        tooltip: {
          trigger: 'item'
        },
        color: ['#00469C', '#EDA133'],
        legend: {
          top: '5%',
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: this.$t('供应商'),
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'center',
              formatter: '100\n总计'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: '25',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: this.$t('采购金额') },
              { value: 735, name: this.$t('销售金额') }
            ]
          }
        ]
      }
      myChart.setOption(option, true)
    }
  }
}
</script>
<style lang="scss" scoped>
.presentbox {
  div {
    width: 500px;
    height: 300px;
  }
}
</style>
