<template>
  <!-- 绩效最高Top10，柱状图 -->
  <div class="chart-panel mt-flex-direction-column">
    <div class="chart-title">{{ $t('绩效最高 top 10') }}</div>
    <div class="content" id="topList" style="height: 420px"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
let chartDom = null
let myChart = null
export default {
  props: {
    chartList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chartOptions: {
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            rotate: -45,
            color: '#292929'
          },
          axisLine: {
            lineStyle: {
              color: '#333333',
              width: 1,
              type: 'solid',
              opacity: 0.75
            }
          },
          axisTick: {
            show: false
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        yAxis: {
          type: 'value',
          max: 100,
          min: 0,
          interval: 20,
          axisLabel: {
            color: '#9A9A9A'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#333333',
              width: 1,
              type: 'dashed',
              opacity: 0.25
            }
          }
        },
        series: [
          {
            data: [],
            type: 'bar',
            color: '#6386C1',
            barWidth: 20,
            itemStyle: {
              borderRadius: [4, 4, 0, 0]
            }
          }
        ]
      }
    }
  },
  mounted() {
    chartDom = document.getElementById('topList')
    myChart = echarts.init(chartDom)
    this.initChart()
  },
  methods: {
    initChart() {
      setTimeout(() => {
        myChart.clear()
        myChart.setOption(this.chartOptions)
      }, 20)
    }
  },
  watch: {
    chartList: {
      handler(n) {
        console.log(n)
        let _names = [],
          _values = []
        if (Array.isArray(n) && n.length) {
          n.forEach((e) => {
            _names.push(e.supplierName)
            _values.push(e.compositeScore)
          })
        }
        this.$set(this.chartOptions.xAxis, 'data', _names)
        this.$set(this.chartOptions.series[0], 'data', _values)
        this.initChart()
      },
      deep: true
    }
  },
  beforeDestroy() {
    chartDom = null
    myChart = null
  }
}
</script>
