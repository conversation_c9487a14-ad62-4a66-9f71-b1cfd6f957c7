<template>
  <!-- 各维度得分，雷达图 -->
  <div class="chart-panel mt-flex-direction-column">
    <div class="chart-title">{{ $t('各维度得分') }}</div>
    <div class="content" id="radarList" style="height: 420px"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
let chartDom = null
let myChart = null
export default {
  props: {
    chartList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chartOptions: {
        color: [
          '#00469C',
          '#42A2F1',
          '#8ACC40',
          '#EDA133',
          '#ED5633',
          '#4D7EBA',
          '#7BBEF5',
          '#ADDB7A',
          '#F2BD71',
          '#F28971',
          '#85A7D0',
          '#A5D3F9',
          '#C7E7A4',
          '#F7D29D',
          '#F7AE9D',
          '#B4C9E2',
          '#C8E4FB',
          '#DDF0C7',
          '#FAE4C3',
          '#FACDC3'
        ],
        textStyle: {
          fontSize: 16
        },
        tooltip: {
          trigger: 'item'
        },
        radar: {
          shape: 'circle',
          indicator: [
            {
              name: '',
              max: 100,
              color: 'rgba(197, 25, 25, 1)'
            }
          ]
        },
        series: [
          {
            type: 'radar',
            data: [
              // {
              //   value: [43],
              // },
            ],
            label: {
              show: false
            },
            symbol: 'circle',
            symbolSize: 14,
            lineStyle: {
              color: '#E8E8E8',
              width: 1
            },
            areaStyle: {
              color: '#E8E8E8',
              opacity: 0.25
            }
          }
        ]
      }
    }
  },
  mounted() {
    chartDom = document.getElementById('radarList')
    myChart = echarts.init(chartDom)
    this.initChart()
  },
  methods: {
    initChart() {
      setTimeout(() => {
        myChart.clear()
        myChart.setOption(this.chartOptions)
      }, 20)
    }
  },
  watch: {
    chartList: {
      handler: function (n) {
        console.log('todo---雷达数据', n)
        let _names = [],
          _values = []
        if (Array.isArray(n) && n.length) {
          let _colors = this.chartOptions.color
          n.forEach((e, i) => {
            _names.push({
              name: `${e[0].dimensionName}：${e[0].score}`,
              max: e[0].allocateScore || 100,
              color: i < _colors.length ? _colors[i] : '#000'
            })
            _values.push(e[0].score)
          })
        }
        this.$set(this.chartOptions.radar, 'indicator', _names)
        this.$set(this.chartOptions.series[0], 'data', [
          {
            value: _values
          }
        ])
        this.initChart()
      }
    }
  },
  beforeDestroy() {
    chartDom = null
    myChart = null
  }
}
</script>
