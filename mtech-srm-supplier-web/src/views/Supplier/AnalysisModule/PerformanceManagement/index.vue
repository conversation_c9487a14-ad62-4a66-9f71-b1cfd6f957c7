<template>
  <div class="perform-box" :style="setStyle ? 'width:1633px' : 'width:100%'">
    <mt-template-page
      :template-config="tabConfig"
      @handleSelectTab="handleSelectTab"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <!-- 列表得分明细弹窗 -->
    <div
      style="position: absolute; top: 0; left: 0; right: 0; bottom: 0"
      @click="isShow = false"
      v-if="isShow"
    >
      <div @click.stop class="scoreDetail" id="scoreId">
        <div>
          <span>{{ scoreDetail.data.supplierEnterpriseName }}</span>
          <span style="margin-left: 20px">{{ scoreDetail.data.categoryName }}</span>
        </div>
        <div class="perEcharts">
          <div ref="myRadar"></div>
          <div ref="myLine"></div>
        </div>
        <div class="score-detail-title">
          <div>{{ $t('得分明细') }}</div>
          <div class="section-active">
            <div v-show="scoreDetail.isOpen" @click="toggleScoreDetailTreeTable(true)">
              {{ $t('收起') }}<span class="active-icons"><mt-icon name="MT_DownArrow" /></span>
            </div>
            <div v-show="!scoreDetail.isOpen" @click="toggleScoreDetailTreeTable(false)">
              {{ $t('展开') }}<span class="expand-icons"><mt-icon name="MT_DownArrow" /></span>
            </div>
          </div>
        </div>
        <div style="height: 190px; margin-top: 15px">
          <mt-template-page ref="detailtable" :template-config="detailConfig"></mt-template-page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { columnList, detailColumn } from './config/columns'
import * as echarts from 'echarts'

export default {
  data() {
    return {
      isShow: false,
      scoreDetail: {
        data: null, // 点击行数据
        isOpen: true // 标记treeTable是否展开，默认是展开的
      },
      setStyle: false,
      timeLoadMore: false,
      tabConfig: [
        {
          gridId: 'cf29037b-6893-4cef-bb6e-95b0a27d4015',
          // title: "列表",
          useToolTemplate: false,
          toolbar: [
            // {
            //   id: "release",
            //   icon: "icon_solid_submit ",
            //   title: this.$t("发布"),
            //   // permission: ["O_02_0039"],
            // },
            // {
            //   id: "Print",
            //   icon: "icon_solid_edit ",
            //   title: this.$t("打印"),
            //   // permission: ["O_02_0039"],
            // },
          ],
          grid: {
            asyncConfig: {
              url: '/analysis/tenant/supplier/assess/archive/result/pageQuery'
            },
            columnData: columnList,
            allowSorting: false

            // dataSource: [
            //   {
            //     abolished: 0,
            //     archiveId: 0,
            //     categoryCode: "",
            //     categoryId: 0,
            //     categoryName: "一级品类",
            //     compositeScore: 90,
            //     comprehensiveLight: "",
            //     cost: "",
            //     createTime: {
            //       am: true,
            //       date: 0,
            //       day: 0,
            //       firstDayOfWeek: "",
            //       hours: 0,
            //       leapYear: true,
            //       minutes: 0,
            //       month: 0,
            //       mutable: true,
            //       pm: true,
            //       seconds: 0,
            //       time: 0,
            //       timeZone: {
            //         displayName: "",
            //         dstsavings: 0,
            //         id: "",
            //         rawOffset: 0,
            //       },
            //       timezoneOffset: 0,
            //       weekend: true,
            //       year: 0,
            //       zoneId: {
            //         id: "",
            //         rules: {
            //           fixedOffset: true,
            //           transitionRules: [
            //             {
            //               dayOfMonthIndicator: 0,
            //               dayOfWeek: "",
            //               localTime: {
            //                 hour: 0,
            //                 minute: 0,
            //                 nano: 0,
            //                 second: 0,
            //               },
            //               midnightEndOfDay: true,
            //               month: "",
            //               offsetAfter: {
            //                 id: "",
            //                 rules: {},
            //                 totalSeconds: 0,
            //               },
            //               offsetBefore: {
            //                 id: "",
            //                 rules: {},
            //                 totalSeconds: 0,
            //               },
            //               standardOffset: {
            //                 id: "",
            //                 rules: {},
            //                 totalSeconds: 0,
            //               },
            //               timeDefinition: "",
            //             },
            //           ],
            //           transitions: [
            //             {
            //               dateTimeAfter: "",
            //               dateTimeBefore: "",
            //               duration: {
            //                 nano: 0,
            //                 negative: true,
            //                 seconds: 0,
            //                 units: [
            //                   {
            //                     dateBased: true,
            //                     duration: {},
            //                     durationEstimated: true,
            //                     timeBased: true,
            //                   },
            //                 ],
            //                 zero: true,
            //               },
            //               gap: true,
            //               instant: "",
            //               offsetAfter: {
            //                 id: "",
            //                 rules: {},
            //                 totalSeconds: 0,
            //               },
            //               offsetBefore: {
            //                 id: "",
            //                 rules: {},
            //                 totalSeconds: 0,
            //               },
            //               overlap: true,
            //             },
            //           ],
            //         },
            //       },
            //     },
            //     deliver: "",
            //     handlingSuggestions: "",
            //     id: 1,
            //     innovate: "",
            //     isNotice: "",
            //     issueNum: "2021W51",
            //     orgCode: "",
            //     orgId: 0,
            //     orgName: "",
            //     quality: "",
            //     qualityLight: "",
            //     qualityScore: 0,
            //     redLightCount: "",
            //     scoreRank: "",
            //     service: "",
            //     supplierEnterpriseCode: "DJBM001920201121",
            //     supplierEnterpriseId: 0,
            //     supplierEnterpriseName: "华夏天信智能物联股份有限公司",
            //     templateName: "SQDC年度考评",
            //     templateVersion: "",
            //     tenantId: 0,
            //   },
            // ],
          }
        }
      ],
      detailConfig: [
        {
          gridId: '42b3e0f0-ea7e-4ab6-aea9-603ee979171c',
          treeGrid: {
            allowPaging: false, //关闭/隐藏表格底部，翻页操作
            childMapping: 'subtasks', //配置属性数据的子节点标记为'children'，默认配置为'subtasks'
            columnData: detailColumn,
            asyncConfig: {
              url: '/analysis/tenant/supplier/assess/archive/result/detail/indexScoreDetailList',
              methods: 'get',
              params: {
                resultId: null
              },
              recordsPosition: 'data', // 默认值为"data.records",可以根据实际情况传值，如果数据位置在response.data,则传值'data'
              serializeList: (list) => {
                // 一级根据“scoreRank”排名排序
                list.forEach((e) => {
                  e.id = e.templateItemId //这里面默认子树的标识为id！！！
                })
                return list
              }, // 用于数据序列化，API返回数据，二次处理。
              transform: true, // 设置为true后，执行Array向Tree结构的转化
              parentId: 'parentTemplateItemId', // 默认值为 "parentId",可根据实际情况传值，如parentId:'pid'、parentId:'categoryId'
              rootTag: '0' // 这个参数如果传值，代表第一层级数据的标记，根据实际情况传值,如果parentId='-1'代表第一层级数据，则rootTag='-1'
            }
          }
          // dataSource: [
          //   {
          //     taskID: 1,
          //     taskName: "Planning",
          //     startDate: new Date("02/03/2017"),
          //     endDate: new Date("02/07/2017"),
          //     progress: 100,
          //     duration: 5,
          //     priority: "Normal",
          //     approved: false,
          //     subtasks: [
          //       {
          //         taskID: 2,
          //         taskName: "Plan timeline",
          //         startDate: new Date("02/03/2017"),
          //         endDate: new Date("02/07/2017"),
          //         duration: 5,
          //         progress: 100,
          //         priority: "Normal",
          //         approved: false,
          //       },
          //       {
          //         taskID: 3,
          //         taskName: "Plan budget",
          //         startDate: new Date("02/03/2017"),
          //         endDate: new Date("02/07/2017"),
          //         duration: 5,
          //         progress: 100,
          //         priority: "Low",
          //         approved: true,
          //       },
          //     ],
          //   },
          // ],
        }
      ]
    }
  },
  created() {
    //   获取弹窗的id，判断点击弹窗以外的部分则关闭弹窗
    // document.addEventListener("click", (e) => {
    //   console.log(e)
    //   let scoreId = document.getElementById("scoreId");
    //   if (scoreId.contains(e.target)) {
    //   } else {
    //     this.isShow = false;
    //   }
    // });
  },
  methods: {
    handleScroll(e) {
      // 如果x轴滑动，e.bubbles 为true，y轴滑动为false
      if (e.bubbles) {
        return
      }
      if (this.timeLoadMore) {
        clearTimeout(this.timeLoadMore)
      }
      this.timeLoadMore = setTimeout(() => {
        let sh = e.target ? e.target.scrollHeight : 0
        let st = e.target ? e.target.scrollTop : 0
        let ch = e.target ? e.target.clientHeight : 0
        st = parseInt(st.toFixed(0))
        // 通过id确定四个区域哪个在滑动
        console.log(e.target.parentElement.id)
        if (st + ch >= sh) {
          console.log('到底了：查询下一页')
          // if(this.totalPages>0 && this.pageNum < this.totalPages){
          //   this.pageParams.page.current ++;
          //   this.getListData();
          // }
        }
      }, 300)
    },
    // tab框点击，返回当前索引
    handleSelectTab(e) {
      if (e == 1) {
        this.setStyle = true
      } else {
        this.setStyle = false
      }
    },
    handleClickToolBar(e) {
      let { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (!sltList || sltList.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id == 'release') {
        // TODO: 待发布
        console.log(sltList)
      }
    },
    handleClickCellTitle(e) {
      // console.log(e.field, e, " ==supplierEnterpriseCode");
      if (e.field == 'scoringDetails') {
        // 得分明细
        this.scoreDetail.data = e.data
        let resultId = e.data.id
        this.$set(this.detailConfig[0].treeGrid.asyncConfig.params, 'resultId', resultId)
        this.isShow = true
        this.$nextTick(() => {
          let params = { resultId: resultId }
          this.$API.performanceManagement.supplierDetailIndexScoreList(params).then((res) => {
            if (res.code == 200) {
              this.initRadar(res.data)
            }
          })
          // this.initLine();
        })
      }
      if (e.field == 'supplierCode') {
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            supplierCode: e.data.supplierCode,
            orgCode: e.data.orgCode
          }
        })
      }
    },
    // 得分明细-雷达图
    initRadar(data) {
      let indicator = data.map((item) => {
        let newItem = {}
        newItem.text = item.indexName
        return newItem
      })
      let series = data.map((item) => {
        return item.score
      })
      let myechart = echarts.init(this.$refs.myRadar)
      myechart.setOption({
        color: ['#6386C1'],
        radar: [
          {
            // indicator: [
            //   { text: "产品质量" },
            //   { text: "财务表现" },
            //   { text: "市场表现" },
            //   { text: "履约能力" },
            //   { text: "企业信用" },
            // ],
            indicator: indicator,
            center: ['50%', '50%'],
            radius: 100,
            shape: 'circle'
          }
        ],
        series: [
          {
            type: 'radar',
            data: [
              {
                // value: [84, 36, 43, 78, 76],
                value: series,
                areaStyle: {
                  color: '#E8EEF6'
                }
              }
            ]
          }
        ]
      })
    },
    // 得分明细-折线图
    initLine() {
      let myLine = echarts.init(this.$refs.myLine)
      myLine.setOption({
        color: '#00469C',
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#BCCFE5' },
                  { offset: 0.5, color: '#D4E0EE' },
                  { offset: 1, color: '#F7F9FC' }
                ])
              }
            }
          }
        ]
      })
    },
    // 得分明细（弹窗）-得分明细table切换展开/收起
    toggleScoreDetailTreeTable(isOpen) {
      // 组件没把方法抛出，只能这样逐级去找。。。
      let ser1 = this.$refs.detailtable.$children[this.$refs.detailtable.$children.length - 1]
      let ser2 = ser1.$children[ser1.$children.length - 1]
      let ser3 = ser2.$children[ser2.$children.length - 1]
      let ser4 = ser3.$children[ser3.$children.length - 1]
      // console.log(123,ser4);
      this.scoreDetail.isOpen = !this.scoreDetail.isOpen
      if (isOpen) {
        ser4.collapseAll() // 收起全部
      } else {
        ser4.expandAll() // 展开全部
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
.perform-box {
  width: 100%;
  position: relative;
  height: 100%;
  /deep/ .e-rowcell {
    .red {
      background-color: rgba(237, 86, 51, 1);
    }
    .green {
      background-color: rgba(138, 204, 64, 1);
    }
    .yellow {
      background-color: rgba(237, 161, 51, 1);
    }
  }
  .yellowcolor {
    color: rgba(237, 161, 51, 1) !important;
  }
  .bluecolor {
    color: #6386c1 !important;
  }
  .blurbgcolor {
    background-color: #00469c;
  }
  .scoreDetail {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 840px;
    height: 550px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
    padding: 15px;
    font-size: 14px;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    .score-detail-title {
      display: flex;
      align-items: center;
    }
    .section-active {
      margin-top: 2px;
      margin-left: 16px;
      color: #6386c1;
      font-size: 12px;
      font-weight: normal;
      cursor: pointer;
      .active-icons {
        display: inline-block;
      }
      .expand-icons {
        display: inline-block;
        transform: rotate(180deg);
        vertical-align: text-top;
      }
      span {
        margin-left: 5px;
      }
    }
    /deep/ .e-rowcell {
      text-align: left;
    }
  }
  .perEcharts {
    margin-top: 15px;
    height: 274px;
    display: flex;
    div {
      width: 50%;
      height: 100%;
      margin-right: 8px;
    }
  }
  .board {
    height: 100%;
    display: flex;
    .list {
      width: 388px;
      height: 100%;
      border-radius: 8px;
      background-color: #f5f5f5;
      margin: 0 8px;
      padding: 16px;
      .listTop {
        height: 30px;
        line-height: 25px;
        display: flex;
        justify-content: space-between;
        color: #292929;
        font-size: 18px;
        margin-bottom: 8px;
        span {
          color: #ed5633;
          font-size: 28px;
          margin-right: 16px;
        }
      }
      .listCon {
        height: calc(100% - 30px);
        overflow-y: scroll;
        .listDetails {
          width: 340px;
          height: 106px;
          border-radius: 8px;
          background-color: #fff;
          margin-bottom: 16px;
          padding: 16px 24px;
          font-size: 14px;
          color: #292929;
          font-weight: 600;
          .detailtitle {
            font-weight: 600;
            display: flex;
            justify-content: space-between;
          }
          .detailCon {
            display: flex;
            justify-content: space-between;
            font-weight: normal;
            margin: 16px 0;
          }
          .detailTip {
            color: #ed5633;
            font-weight: normal;
          }
        }
      }
    }
  }
}
</style>
