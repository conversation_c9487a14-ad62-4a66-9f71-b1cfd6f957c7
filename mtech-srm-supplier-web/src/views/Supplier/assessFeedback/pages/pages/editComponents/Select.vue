<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
import bus from '@/utils/bus'
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      costCenterCode: '',
      isDisabled: false,
      siteName: ''
    }
  },
  async mounted() {
    if (this.data.column.field === 'costCenterCode') {
      //成本中心下拉
      this.getCostCenter()
      this.fields = { text: 'costCenterCode', value: 'costCenterCode' }
    }
    if (this.data.column.field === 'currencyCode') {
      // 币种
      this.getCurrencyList()
      this.fields = { text: 'currencyName', value: 'currencyCode' }
    }
    if (this.data.column.field === 'taxInclusiveName') {
      // bus.$emit("taxInclusiveNameChange", this.data.taxInclusiveName);
      this.dataSource = [
        { label: this.$t('否'), value: false },
        { label: this.$t('是'), value: true }
      ]
      this.fields = { text: 'label', value: 'label' }
    }
    if (this.data.column.field === 'unitName' || this.data.column.field === 'refResUnitName') {
      this.getUnitList()
      this.fields = { text: 'unitName', value: 'unitName' }
    }
    if (this.data.column.field === 'taxTypeName') {
      console.log('this.data.taxInclusiveName', this.data)
      if (!this.data.taxInclusive) {
        this.isDisabled = true
      }
      this.$bus.$on('taxInclusiveNameChange', (val) => {
        console.log('taxInclusiveNameChange=', val)
        this.isDisabled = !val.itemData.value
        if (!val.itemData.value) {
          this.data.taxTypeName = null
        }
      })
      this.getTaxItemList()
      this.fields = { text: 'taxItemName', value: 'taxItemName' }
    }
    if (this.data.column.field === 'standName') {
      this.getAssessIndexList()
      this.fields = { text: 'standName', value: 'standName' }
    }
    if (this.data.column.field === 'siteName') {
      if (this?.data?.siteId) {
        sessionStorage.setItem('organizationId', this.data.siteId)
      }
      this.getFactoryList()
      this.fields = { text: 'siteName', value: 'siteName' }
    }
  },
  methods: {
    getFactoryList() {
      let _company = JSON.parse(sessionStorage.getItem('inspectionSheetCompany'))
        ? JSON.parse(sessionStorage.getItem('inspectionSheetCompany'))
        : ''
      this.$API.masterData
        .getSiteList({
          condition: '',
          page: {
            current: 1,
            size: 1000
          },
          defaultRules: [
            {
              label: this.$t('公司'),
              field: 'parentCode',
              type: 'string',
              operator: 'equal',
              value: _company.companyCode ? _company.companyCode : ''
            }
          ],
          pageFlag: false
        })
        .then((res) => {
          this.dataSource = res.data.records
        })
    },
    getAssessIndexList() {
      let _claimTypeCode = sessionStorage.getItem('claimTypeCode')
      let _claimCompanyCode = sessionStorage.getItem('claimCompanyCode')
      this.$API.assessManage
        .listAvailableClaimStandByCompany({
          companyCode: _claimCompanyCode,
          typeCode: _claimTypeCode
        })
        .then((res) => {
          console.log('11111111111', res.data)
          this.dataSource = res.data
        })
    },
    getTaxItemList() {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        this.dataSource = res.data
      })
    },
    getUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.dataSource = res.data.records
      })
    },
    async getCostCenter() {
      await this.$API.masterData
        .postCostCenterCriteriaQuery()
        .then((res) => {
          this.dataSource = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async getCurrencyList() {
      await this.$API.masterData
        .queryAllCurrency()
        .then((res) => {
          this.dataSource = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    serchText(val) {
      val.updateData(
        this.dataSource.filter(
          (e) => e[this.fields.value].includes(val.text) || e[this.fields.text].includes(val.text)
        )
      )
    },
    startOpen() {
      // // 成本中心
      // if (this.data.column.field === "costCenterCode") {
      //   console.log("startOpen=", this);
      //   if (!!this.data.companyId) {
      //     this.getCostCenter();
      //   }
      // }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log('val', val, this.data)
      if (this.data.column.field === 'costCenterCode') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'costCenterCode',
          itemInfo: {
            costCenterDesc: val.itemData.costCenterDesc
          }
        })
        bus.$emit('costCenterDescChange', val.itemData.costCenterDesc)
      }
      if (this.data.column.field === 'currencyCode') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'currencyCode',
          itemInfo: {
            currencyName: val.itemData.currencyName
          }
        })
        bus.$emit('currencyNameChange', val.itemData.currencyName)
      }
      if (this.data.column.field === 'standName') {
        // bus.$emit('standCodeChange', val) //传给描述
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'standCode',
          itemInfo: {
            standDesc: val.itemData.claimStandCalcRule.standDesc,
            standName: val.itemData.standName,
            standCode: val.itemData.standCode
          }
        })
        bus.$emit('standCodeChange', val.itemData.standCode)
        bus.$emit('changeUntaxedPricebus', val.itemData)
        bus.$emit('standDescChange', val.itemData.claimStandCalcRule.standDesc)
      }
      if (this.data.column.field === 'taxInclusiveName') {
        bus.$emit('taxInclusiveNameChange', val)
        if (!val.itemData.value) {
          bus.$emit('taxAmountChange', null)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxInclusiveName',
            itemInfo: {
              taxInclusiveName: val.itemData.label,
              taxInclusive: val.itemData.value,
              taxAmount: null,
              taxedPrice: null,
              taxTypeName: null
            }
          })
        } else {
          bus.$emit('taxAmountChange', null)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxInclusiveName',
            itemInfo: {
              taxInclusiveName: val.itemData.label,
              taxInclusive: val.itemData.value,
              taxAmount: null,
              untaxedPrice: null,
              taxTypeName: null
            }
          })
        }
      }
      if (this.data.column.field === 'taxTypeName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'taxTypeName',
          itemInfo: {
            taxTypeName: val.itemData.taxItemName,
            taxTypeId: val.itemData.id,
            taxTypeCode: val.itemData.taxItemCode,
            taxRate: val.itemData.taxRate
          }
        })
        bus.$emit('taxedPriceChange', val)
      }
      if (this.data.column.field === 'siteName') {
        sessionStorage.setItem('organizationId', val.itemData.organizationId)
        bus.$emit('itemCodeChange', null)
        bus.$emit('itemNameChange', null)
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'siteName',
          itemInfo: {
            siteId: val.itemData.organizationId,
            siteCode: val.itemData.siteCode,
            siteName: val.itemData.siteName,
            itemId: null,
            itemCode: null,
            itemName: null
          }
        })
      }
      if (this.data.column.field === 'unitName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'unitName',
          itemInfo: {
            unitCode: val.itemData.unitCode,
            unitName: val.itemData.unitName
          }
        })
      }
      if (this.data.column.field === 'refResUnitName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'refResUnitName',
          itemInfo: {
            refResUnitCode: val.itemData.unitCode,
            refResUnitName: val.itemData.unitName
          }
        })
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('changeUntaxedPricebus')
  }
}
</script>
