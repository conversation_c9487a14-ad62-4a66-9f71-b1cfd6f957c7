import { i18n } from '@/main.js'
export const priceColumnData = [
  {
    width: '150',
    field: 'sortNo',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  { width: '150', field: 'itemName', headerText: i18n.t('物料名称') },
  {
    width: '150',
    field: 'latestPrice',
    headerText: i18n.t('最新执行价')
  }
]
export const columnData = [
  {
    width: '150',
    field: 'sortNo',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  { width: '150', field: 'itemName', headerText: i18n.t('物料名称') },
  {
    width: '150',
    field: 'freePrice',
    headerText: i18n.t('未税单价')
  }
]
