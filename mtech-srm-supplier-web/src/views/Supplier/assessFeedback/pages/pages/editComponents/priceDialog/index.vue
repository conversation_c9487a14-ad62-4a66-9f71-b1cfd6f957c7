<template>
  <mt-dialog ref="dialog" size="large" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <span class="accordion-title">{{ $t('最新采购执行价') }}</span>
      <div class="grid-area">
        <mt-dataGrid
          ref="templetePage"
          :data-source="dataConfig.dataSource"
          :column-data="dataConfig.columnData"
        ></mt-dataGrid>
      </div>
      <span class="accordion-title">{{ $t('未清PO单价') }}</span>
      <div class="grid-area">
        <mt-dataGrid
          ref="templetePage"
          :data-source="dataConfig1.dataSource"
          :column-data="dataConfig1.columnData"
        ></mt-dataGrid>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { columnData, priceColumnData } from './config'
export default {
  data() {
    return {
      data: {},
      buttons: [
        // {
        //   click: this.confirm,
        //   buttonModel: { isPrimary: "true", content: this.$t("确定") },
        // },
        {
          click: this.exportFile,
          buttonModel: { content: this.$t('导出') }
        },
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      dataConfig: {
        dataSource: [],
        columnData: priceColumnData
      },
      dataConfig1: {
        dataSource: [],
        columnData
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getPriceData()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    getPriceData() {
      // const params = {
      //   headerId: this.propsData.orgInfo.headerId,
      //   indexItemId: this.propsData.orgInfo.id
      // }
      const formdata = new FormData()
      formdata.append('headerId', this.propsData.orgInfo.headerId)
      formdata.append('indexItemId', this.propsData.orgInfo.id)
      this.$API.deadMaterials.queryPrice(formdata).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.dataConfig.dataSource = data.latestPriceList || []
          this.dataConfig1.dataSource = data.outstandingOrderPriceList || []
        }
      })
    },
    exportFile() {
      console.log('导出-----', this.propsData)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  // display: flex;
  height: 100%;
  width: 100%;
  .accordion-title {
    // float: left;
    font-size: 14px;
    // margin-left: 20px;
    font-family: PingFangSC;
    font-weight: 500;
    color: #292929;
    text-indent: 10px;
    border-left: 5px solid #00469c;
    margin-bottom: 20px;
    border-radius: 2px 0 0 2px;
    padding-left: 10px;
  }
  .grid-area {
    padding: 18px 0;
  }
}
</style>
