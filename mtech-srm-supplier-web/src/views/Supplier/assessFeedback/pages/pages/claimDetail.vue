<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('基本信息') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="basicInfo" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('TCL承担损失总额')"
            label-style="top"
            prop="purchaseAmt"
          >
            <mt-input
              :disabled="true"
              :width="300"
              v-model="basicInfo.purchaseAmt"
              :min="0"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('供应商承担损失总额')"
            label-style="top"
            prop="supplierAmt"
          >
            <mt-input
              :disabled="true"
              :width="300"
              v-model="basicInfo.supplierAmt"
              :min="0"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('是否计提')"
            label-style="top"
            prop="needAccrual"
          >
            <mt-select
              :disabled="queryType != 1 && queryType != 4 && queryType != 7"
              :width="300"
              :data-source="complaintAccessory"
              v-model="basicInfo.needAccrual"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item>
          <!-- <mt-form-item class="form-item" :label="$t('是否盖章')" label-style="top" prop="needSeal">
            <mt-select
              :disabled="queryType != 1 && queryType != 4 && queryType != 7"
              :width="300"
              :data-source="complaintAccessory"
              v-model="basicInfo.needSeal"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item> -->
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !assessIndexExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('索赔指标详情') }}</div>
      <div class="sort-box" @click="assessIndexExpand = !assessIndexExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="assessIndexExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="assessIndexTemplateRef"
          :template-config="assessmentIndexConfig"
          @actionBegin="assessIndexActionBegin"
          @actionComplete="assessIndexActionComplete"
          @handleClickCellTool="handleClickCellTool"
          @handleClickToolBar="handleClickAssessToolBar"
          @selectedChanged="assessIndexSelectedChanged"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !assessItemExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('物料详情') }}</div>
      <div class="sort-box" @click="assessItemExpand = !assessItemExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="assessItemExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="assessItemTemplateRef"
          :template-config="assessmentItemConfig"
          @actionBegin="assessItemActionBegin"
          @actionComplete="assessItemActionComplete"
          @handleClickToolBar="handleClickAssessItemToolBar"
          @selectedChanged="assessItemSelectedChanged"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !reasonExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">
        <!-- <span style="color: red">{{ '*' }}&nbsp;</span> -->
        {{ $t('原因说明') }}
      </div>
      <div class="sort-box" @click="reasonExpand = !reasonExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="reasonExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box reason-desc">
        <textarea
          :disabled="queryType != 1 && queryType != 4 && queryType != 7"
          ref="editorRef"
          v-model="templateText"
          :multiline="true"
          :rows="6"
          maxlength="5000"
          float-label-type="Never"
          :placeholder="$t('字数不超过5000字')"
        ></textarea>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !purAttachExpand && 'top-filter-small', 'mb-30']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('采方附件') }}</div>
      <div class="sort-box" @click="purAttachExpand = !purAttachExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="purAttachExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="attachmentRef"
          :template-config="purchaseAttachmentConfig"
          @handleClickToolBar="handleClickPurchaseAttachmentToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { download } from '@/utils/utils'
import {
  assessmentIndexColumn,
  assessmentItemColumn,
  attachmentColumn,
  editSettings
} from './config/index'
import commonData from '@/utils/constant'
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    }
  },
  mounted() {
    let params = {
      companyCode: this.info.companyCode,
      companyName: this.info.companyName
    }
    sessionStorage.setItem('inspectionSheetCompany', JSON.stringify(params))
    this.basicInfo = {
      ...this.info,
      feedbackEndTime: this.info.feedbackEndTime
        ? new Date(Number(this.info.feedbackEndTime))
        : null,
      offlineEnsure: this.info?.offlineEnsure?.toString() || null,
      appealDealDesc: this.info?.claimAppealDeal?.appealDealDesc,
      appealDeal: this.info?.claimAppealDeal?.appealDeal
    }
    if (this.basicInfo.claimTypeCode == 'S05') {
      this.basicInfo.referenceClaimCode = this.info?.referenceClaim?.claimCode
    } else if (this.basicInfo.claimTypeCode == 'S03') {
      this.basicInfo.referenceClaimCode = this.info?.referenceOrder?.claimCode
    }
    this.info.indexItemList.forEach((e) => {
      e.happenTime = utils.formateTime(new Date(Number(e.happenTime)), 'yyyy-MM-dd hh:mm')
    })
    this.info.indexItemList.forEach((e) => (e.addId = this.addId++))
    this.assessmentIndexConfig[0].grid.dataSource = this.info.indexItemList
    // this.assessmentItemConfig[0].grid.columnData = assessmentItemColumn(this.info.indexItemList)

    setTimeout(() => {
      this.assessmentItemConfig = [
        {
          // toolbar: {
          // useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
          // tools: [
          //   [
          //     'Add',
          //     'Delete',
          //     {
          //       id: 'Import',
          //       icon: 'icon_solid_Import ',
          //       title: this.$t('导入')
          //     }
          //   ],
          //   []
          // ]
          // },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowSorting: false,
            allowPaging: false,
            lineIndex: 1,
            columnData: assessmentItemColumn(this.info.indexItemList),
            height: 100,
            dataSource: this.info.materialItemList.map((i) => {
              // 物料明细行对应的索赔指标行id拼装进去
              let indexItemId = ''
              this.info.indexItemList.forEach((item) => {
                if (item.factoryCode === i.factoryCode && item.materialCode === i.materialCode) {
                  indexItemId = item.id
                }
              })
              return {
                ...i,
                indexItemId,
                siteAndItemName: `${i.factoryName} - ${i.materialDesc} - ${i.categoryName} - ${i.purchaseOrgName}`,
                siteAndItemCode: `${i.factoryCode} - ${i.materialCode} - ${i.categoryCode} - ${i.purchaseOrgCode}`
              }
            })
          }
        }
      ]
      this.purchaseAttachmentConfig[0].grid.dataSource = this.info.attachmentList
      this.templateText = this.info.reason
    }, 0.17)
  },
  data() {
    return {
      saveUrl: commonData.publicFileUrl,
      nowEditRowFlag: '', //当前编辑的行id
      addId: 1,
      templateText: null,
      selectedOtherInfo: {},
      selectedAssessIndexOtherInfo: {},
      isInner: '',
      selectedAssessItemOtherInfo: {},
      complaintAccessory: [
        { text: this.$t('否'), value: 0 },
        { text: this.$t('是'), value: 1 }
      ],
      basicExpand: true,
      assessIndexExpand: true,
      assessItemExpand: true,
      reasonExpand: true,
      purAttachExpand: true,
      basicInfo: {},
      rules: {
        currencyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        feedbackEndTime: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        offlineEnsure: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        referenceClaimCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      assessmentIndexConfig: [
        {
          toolbar: {
            useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
            // tools: [['Add', 'Delete'], []]
            // tools: [[], []]
          },
          useToolTemplate: false,
          grid: {
            // editSettings: {
            //   allowEditing: true,
            //   allowAdding: true,
            //   allowDeleting: true,
            //   mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
            //   showConfirmDialog: false,
            //   showDeleteConfirmDialog: false,
            //   newRowPosition: 'Top'
            // },
            allowSorting: false,
            allowPaging: false,
            lineIndex: 1,
            columnData: assessmentIndexColumn,
            height: 100,
            dataSource: []
          }
        }
      ],
      assessmentItemConfig: [],
      purchaseAttachmentConfig: [
        {
          // toolbar: {
          // useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
          // tools: [['Add', 'Edit', 'Delete'], []]
          // },
          useToolTemplate: false,
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 'auto',
            dataSource: []
          }
        }
      ]
    }
  },
  created() {
    // if (this.queryType == 1) {
    //   delete this.assessmentIndexConfig[0].toolbar
    //   delete this.assessmentItemConfig[0].toolbar
    //   // delete this.purchaseAttachmentConfig[0].toolbar
    // }
    if (this.queryType != 1 && this.queryType != 4 && this.queryType != 7) {
      delete this.assessmentIndexConfig[0].toolbar
      delete this.assessmentItemConfig[0].toolbar
      delete this.purchaseAttachmentConfig[0].toolbar
    }
    if (this.info.claimTypeCode != 'S05') {
      delete this.rules.referenceClaimCode
    }
  },
  provide() {
    return {
      assessIndexInfo: this.selectedAssessIndexOtherInfo,
      assessItemInfo: this.selectedAssessItemOtherInfo
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'attachmentName') {
        this.preview(e.data)
      }
    },
    preview(item) {
      let params = {
        id: item.attachmentId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.attachmentId
        })
        .then((res) => {
          download({
            fileName: data.attachmentName,
            blob: res.data
          })
        })
    },
    assessIndexSelectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedAssessIndexOtherInfo, val.itemInfo || {})
    },
    assessItemSelectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedAssessItemOtherInfo, val.itemInfo || {})
    },
    assessIndexActionBegin(args) {
      let { data, requestType } = args
      // 行内数据新增
      if (requestType == 'add') {
        for (let key in this.selectedAssessIndexOtherInfo) {
          this.selectedAssessIndexOtherInfo[key] = ''
        }
        args.data.addId = this.addId++
        this.selectedAssessIndexOtherInfo.addId = args.data.addId
        this.nowEditRowFlag = args.data.addId
      } else if (requestType == 'beginEdit') {
        let _tempData = cloneDeep(args)
        Object.assign(this.selectedAssessIndexOtherInfo, _tempData.rowData)
        delete this.selectedAssessIndexOtherInfo.happenTime
        delete this.selectedAssessIndexOtherInfo.quantity
        delete this.selectedAssessIndexOtherInfo.unitPrice
        delete this.selectedAssessIndexOtherInfo.claimDesc
        delete this.selectedAssessIndexOtherInfo.refRes
        delete this.selectedAssessIndexOtherInfo.refResUnitName
        delete this.selectedAssessIndexOtherInfo.refResQuantity
        delete this.selectedAssessIndexOtherInfo.refAmount
        delete this.selectedAssessIndexOtherInfo.remark
        this.nowEditRowFlag = args.rowData.addId
      } else if (requestType == 'save') {
        data.isInner = this.isInner
      }
    },
    assessItemActionBegin(args) {
      let { data, requestType } = args
      // 行内数据新增
      if (requestType == 'add') {
        for (let key in this.selectedAssessItemOtherInfo) {
          this.selectedAssessItemOtherInfo[key] = ''
        }
        args.data.addId = this.addId++
        this.selectedAssessItemOtherInfo.addId = args.data.addId
        this.nowEditRowFlag = args.data.addId
      } else if (requestType == 'beginEdit') {
        let _tempData = cloneDeep(args)
        Object.assign(this.selectedAssessItemOtherInfo, _tempData.rowData)
        delete this.selectedAssessItemOtherInfo.happenTime
        delete this.selectedAssessItemOtherInfo.quantity
        delete this.selectedAssessItemOtherInfo.unitPrice
        this.nowEditRowFlag = args.rowData.addId
      } else if (requestType == 'save') {
        data.isInner = this.isInner
      }
    },
    assessIndexActionComplete(item) {
      let { requestType } = item
      if (requestType == 'save') {
        // 验证必输
        // if (!data.costCenterCode || !data.shareRatio || !data.untaxedPrice) {
        //   this.$toast({
        //     content: this.$t("有字段未输入"),
        //     type: "warning",
        //   });
        // }
      }
      this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    assessItemActionComplete(item) {
      let { requestType } = item
      if (requestType == 'save') {
        // 验证必输
        // if (!data.costCenterCode || !data.shareRatio || !data.untaxedPrice) {
        //   this.$toast({
        //     content: this.$t("有字段未输入"),
        //     type: "warning",
        //   });
        // }
      }
      // let row = this.getAssessIndexRow()
      this.$refs.assessItemTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    getAssessIndexRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.assessIndexTemplateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedAssessIndexOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    handleClickAssessToolBar(args) {
      const { toolbar, grid } = args
      if (toolbar.id == 'Add') {
        // 新增
        this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'Delete') {
        if (this.type == 'edit') {
          this.$toast({
            content: this.$t('编辑状态下不可删除行数据'),
            type: 'warning'
          })
        } else {
          // 选中的数据
          let selectedRecords = grid.getSelectedRecords()
          if (selectedRecords.length > 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                this.$refs.assessIndexTemplateRef
                  .getCurrentUsefulRef()
                  .gridRef.ejsRef.deleteRecord()
              }
            })
          } else {
            this.$toast({
              content: this.$t('请先选择一行'),
              type: 'warning'
            })
          }
        }
      } else if (toolbar.id == 'endEdit') {
        this.$refs.assessIndexTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    handleClickAssessItemToolBar(args) {
      const { toolbar, grid } = args
      if (toolbar.id == 'Add') {
        // 新增
        this.$refs.assessItemTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id == 'Delete') {
        if (this.type == 'edit') {
          this.$toast({
            content: this.$t('编辑状态下不可删除行数据'),
            type: 'warning'
          })
        } else {
          // 选中的数据
          let selectedRecords = grid.getSelectedRecords()
          if (selectedRecords.length > 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                this.$refs.assessItemTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
              }
            })
          } else {
            this.$toast({
              content: this.$t('请先选择一行'),
              type: 'warning'
            })
          }
        }
      } else if (toolbar.id == 'endEdit') {
        this.$refs.assessItemTemplateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id == 'Import') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "routerrouter/Buyer/analysisOfSetting/scoreSetting/dimension/Excelimport" */ '@/components/uploadDialog/index.vue'
            ),
          data: {
            title: this.$t('上传/导入'),
            // paramsKey: 'multipartFile',
            asyncParams: {
              headerId: this.queryId
            },
            importApi: this.$API.deadMaterials.importMaterialItem,
            downloadTemplateApi: this.$API.deadMaterials.downloadImportMaterialItemTemplate
          },
          success: (res) => {
            const { msg, data } = res
            if (msg === 'success') {
              if (data && data.length) {
                data.forEach((i) => {
                  let indexItemId = ''
                  this.info.indexItemList.forEach((item) => {
                    if (
                      item.factoryCode === i.factoryCode &&
                      item.materialCode === i.materialCode
                    ) {
                      indexItemId = item.id
                    }
                  })
                  const item = {
                    ...i,
                    indexItemId,
                    siteAndItemName: `${i.factoryName} - ${i.materialDesc} - ${i.categoryName} - ${i.purchaseOrgName}`,
                    siteAndItemCode: `${i.factoryCode} - ${i.materialCode} - ${i.categoryCode} - ${i.purchaseOrgCode}`
                  }
                  this.assessmentItemConfig[0].grid.dataSource.push(item)
                })
              }
            }
            // 导入之后刷新列表
            // this.search()
          }
        })
      }
    },
    handleClickPurchaseAttachmentToolBar(e) {
      let sltList = e.grid.getSelectedRecords()
      let selectedRowIndexes = e.grid.getSelectedRowIndexes()
      if (
        (!sltList || sltList.length <= 0) &&
        (e.toolbar.id == 'Edit' || e.toolbar.id == 'Delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./editComponents/uploadDialog.vue'),
          data: {
            fileData: [],
            isView: false, // 是否为预览
            required: false, // 是否必须
            title: this.$t('新增采方附件')
          },
          success: (res) => {
            this.handleUploadFiles(res)
          }
        })
      } else if (e.toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./editComponents/uploadDialog.vue'),
          data: {
            title: this.$t('编辑采方附件'),
            isEdit: true,
            info: {
              ...sltList[0],
              fileName: sltList[0].attachmentName,
              fileSize: sltList[0].attachmentSize,
              url: sltList[0].attachmentUrl
            },
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _tempData)
            this.$refs.attachmentRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id == 'Delete') {
        let _tempData = cloneDeep(this.purchaseAttachmentConfig[0].grid.dataSource)
        let _fileIds = sltList.map((x) => x.id)
        let _newData = _tempData.filter((element) => !_fileIds.includes(element.id))
        this.$set(this.purchaseAttachmentConfig[0].grid, 'dataSource', _newData)
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'download') {
        this.handleDownloadFile(data)
      }
      if (id == 'viewPrice') {
        this.showPriceDialog({ ...data })
      }
    },
    showPriceDialog(orgInfo) {
      this.$dialog({
        modal: () => import('./editComponents/priceDialog/index.vue'),
        data: {
          title: this.$t('呆料价格表'),
          orgInfo
        },
        success: (data) => {
          console.log('成功', data)
        }
      })
    },
    handleUploadFiles(data) {
      let _tempData = {
        ...data,
        attachmentId: data.id,
        attachmentName: data.fileName,
        attachmentSize: data.fileSize,
        attachmentUrl: data.url
      }
      this.purchaseAttachmentConfig[0].grid.dataSource.push(_tempData)
    }
  }
}
</script>
<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
      /deep/ textarea {
        color: #000000de !important;
        -webkit-text-fill-color: #000000de;
      }
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
.reason-desc {
  /deep/ .mt-input {
    textarea {
      color: #000000de !important;
      -webkit-text-fill-color: #000000de !important;
    }
  }
  /deep/ textarea {
    width: 100%;
    background: #fff;
  }
  /deep/ textarea[disabled] {
    background: #efefef;
  }
}
/deep/ .mt-rich-text-editor {
  margin: 30px 10px 0 10px;
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-toolbar-items {
    margin-left: -59px;
  }
  .e-rte-content {
    height: 300px !important;
  }
}
.mb-30 {
  // margin-bottom: 30px;
}
</style>
