import { i18n } from '@/main.js'
import Select from '../editComponents/Select.vue'
import cellChanged from '../editComponents/cellChanged.vue'
import bus from '@/utils/bus'
import Vue from 'vue'
import dayjs from 'dayjs'

export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}

export const assessmentItemColumn = (indexList) => [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'addId',
    headerText: 'addId',
    width: '0',
    allowEditing: false,
    visible: false
  },
  {
    field: 'siteAndItemCode',
    headerText: i18n.t('工厂+ 物料+ 采购组织'),
    editTemplate: () => {
      return {
        template: Vue.component('siteAndItemCode', {
          template: `
              <mt-select
              :id="data.column.field"
              v-model="data[data.column.field]"
              :data-source="dataSource"
              :fields="fields"
              :popup-width="750"
              :placeholder="placeholder"
              @change="selectChange"
              :open-dispatch-change="false"
              :disabled="isDisabled"
              :allow-filtering="true"
              :filtering="serchText"
            ></mt-select>
          `,
          data() {
            return {
              data: {},
              placeholder: this.$t('请选择'),
              fields: { text: 'siteAndItemName', value: 'siteAndItemCode' },
              dataSource: [],
              isDisabled: false
            }
          },
          mounted() {
            if (indexList && indexList.length) {
              this.dataSource = indexList.map((i) => {
                return {
                  ...i,
                  indexItemId: i.id,
                  siteAndItemName: `${i.factoryName} - ${i.materialDesc} - ${i.purchaseOrgName}`,
                  siteAndItemCode: `${i.factoryCode} - ${i.materialCode} - ${i.purchaseOrgCode}`
                }
              })
            }
          },
          methods: {
            serchText(val) {
              val.updateData(
                this.dataSource.filter(
                  (e) =>
                    e[this.fields.value].includes(val.text) ||
                    e[this.fields.text].includes(val.text)
                )
              )
            },
            selectChange(val) {
              if (val.itemData.unitPrice) {
                bus.$emit('isHavaUnitPriceChange', 2)
              } else {
                bus.$emit('isHavaUnitPriceChange', 1)
              }
              console.log('12323', val)
              this.$set(this.data, 'siteAndItemCode', val.value)
              bus.$emit('indexItemIdChange', val.itemData.indexItemId)
              bus.$emit('factoryNameChange', val.itemData.factoryName)
              bus.$emit('factoryCodeChange', val.itemData.factoryCode)
              bus.$emit('materialCodeChange', val.itemData.materialCode)
              bus.$emit('materialDescChange', val.itemData.materialDesc)
              bus.$emit('categoryNameChange', val.itemData.categoryName)
              bus.$emit('categoryCodeChange', val.itemData.categoryCode)
              bus.$emit('purchaseOrgNameChange', val.itemData.purchaseOrgName)
              bus.$emit('purchaseOrgCodeChange', val.itemData.purchaseOrgCode)
              bus.$emit('currencyChange', val.itemData.currency)
              bus.$emit('costCenterChange', val.itemData.costCenter)
              bus.$emit('unitPriceChange', val.itemData.unitPrice)
            }
          }
        })
      }
    }
  },
  {
    field: 'isHavaUnitPrice',
    headerText: i18n.t('是否可编辑单价'),
    width: 0.1
    // editTemplate: () => {
    //   return { template: Select }
    // }
    // editTemplate: () => {
    //   return { template: priceCellChange }
    // }
  },
  {
    field: 'indexItemId',
    headerText: i18n.t('指标行id'),
    width: 0.1,
    // editTemplate: () => {
    //   return { template: Select }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂编码'),
    // editTemplate: () => {
    //   return { template: Select }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'factoryName',
    headerText: i18n.t('工厂名称'),
    // editTemplate: () => {
    //   return { template: Select }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    editTemplate: () => {
      return { template: cellChanged }
    },
    ignore: true
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    editTemplate: () => {
      return { template: cellChanged }
    },
    ignore: true
  },
  {
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织编码'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  // {
  //   field: 'factoryCode',
  //   width: 0.1,
  //   headerText: i18n.t('物料编码'),
  //   // editTemplate: () => {
  //   //   return {
  //   //     template: selectedItemCodeNew
  //   //   }
  //   // }
  //   editTemplate: () => {
  //     return { template: cellChanged }
  //   }
  // },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码'),
    // editTemplate: () => {
    //   return {
    //     template: selectedItemCodeNew
    //   }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'materialDesc',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'materialStatus',
    headerText: i18n.t('物料状态')
  },
  {
    field: 'claimDate',
    headerText: i18n.t('索赔日期'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `<span>{{ getTimeFmt() }}</span>`,
          methods: {
            getTimeFmt() {
              if (this.data[this.data.column.field]) {
                return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
              }
              return ''
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <mt-date-picker
                :id="data.column.field"
                v-model="data[data.column.field]"
                :open-on-focus="true"
                :allow-edit="false"
                :placeholder="$t('请选择索赔日期')"
              ></mt-date-picker>
            `
        })
      }
    }
  },
  {
    field: 'qty',
    headerText: i18n.t('实际盘点数量'),
    headerTemplate: () => {
      return {
        template: Vue.component('qtyTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('实际盘点数量')}}</span>
              </div>
            `
        })
      }
    },
    editType: 'numericedit',
    edit: {
      params: {
        min: 0,
        decimals: 3,
        format: '###',
        validateDecimalOnType: true,
        htmlAttributes: { type: 'number' },
        showSpinButton: false
      }
    }
  },
  {
    field: 'currency',
    headerText: i18n.t('币种'),
    // editTemplate: () => {
    //   return { template: Select }
    // }
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'unitPrice',
    headerText: i18n.t('单价')
    // editType: 'numericedit',
    // edit: {
    //   params: {
    //     min: 0,
    //     decimals: 3,
    //     format: '###.###',
    //     validateDecimalOnType: true,
    //     htmlAttributes: { type: 'number' },
    //     showSpinButton: false
    //   }
    // }
  },
  {
    field: 'actualAmt',
    headerText: i18n.t('实际损失金额'),
    allowEditing: false
  },
  {
    field: 'purchaserPercent',
    headerText: i18n.t('TCL承担比例'),
    headerTemplate: () => {
      return {
        template: Vue.component('purchaserPercentTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('TCL承担比例')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'purchaseAmt',
    headerText: i18n.t('TCL承担损失'),
    allowEditing: false
  },
  {
    field: 'supplierPercent',
    headerText: i18n.t('供应商承担比例'),
    headerTemplate: () => {
      return {
        template: Vue.component('supplierPercentTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商承担比例')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'supplierAmt',
    headerText: i18n.t('供应商承担损失'),
    allowEditing: false
  },
  {
    field: 'costCenter',
    headerText: i18n.t('成本中心代码'),
    editTemplate: () => {
      return { template: cellChanged }
    },
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('采购方备注')
  }
]
export const assessmentIndexColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'addId',
    headerText: 'addId',
    width: '0',
    allowEditing: false,
    visible: false
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂代码')
    // editTemplate: () => {
    //   return { template: Select }
    // }
  },
  {
    field: 'factoryName',
    headerText: i18n.t('工厂名称'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  // {
  //   field: 'occurTime',
  //   width: 230,
  //   headerText: i18n.t('发生时间'),
  //   editType: 'dateTimePickerEdit',
  //   edit: {
  //     params: {
  //       // format: "yyyy-MM-d hh:mm",
  //       max: new Date()
  //     }
  //   },
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       if (e) {
  //         if (e == 0) {
  //           return (e = '')
  //         } else if (typeof e == 'object') {
  //           return utils.formateTime(e, 'yyyy-MM-dd')
  //         } else if (typeof e == 'string') {
  //           if (e.indexOf('T') != -1) {
  //             // return e.substr(0,10);
  //             return utils.formateTime(new Date(e), 'yyyy-MM-dd')
  //           } else {
  //             let val = parseInt(e)
  //             return utils.formateTime(val, 'yyyy-MM-dd')
  //           }
  //         } else if (typeof e == 'number') {
  //           return utils.formateTime(e, 'yyyy-MM-dd')
  //         } else {
  //           return e
  //         }
  //       } else {
  //         return e
  //       }
  //     }
  //   }
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            v-model={scoped.category}
            url='/masterDataManagement/tenant/category/paged-query'
            multiple={false}
            placeholder={i18n.t('请选择品类编码')}
            popup-width='250'
            fields={{ text: 'categoryName', value: 'categoryCode' }}
            search-fields={['categoryCode', 'categoryName']}
            onChange={(e) => {
              scoped.categoryCode = e.itemData?.categoryCode
              scoped.categoryName = e.itemData?.categoryName
            }}></RemoteAutocomplete>
        </div>
      )
    },
    ignore: true
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    },
    ignore: true
  },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'materialDesc',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织编码')
  },
  {
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织名称'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    field: 'qty',
    headerText: i18n.t('数量'),
    editType: 'numericedit',
    edit: {
      params: {
        min: 0,
        decimals: 3,
        format: '###',
        validateDecimalOnType: true,
        htmlAttributes: { type: 'number' },
        showSpinButton: false
      }
    }
  },
  {
    field: 'currency',
    headerText: i18n.t('币种'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'unitPrice',
    headerText: i18n.t('单价'),
    editType: 'numericedit',
    edit: {
      params: {
        min: 0,
        decimals: 3,
        format: '###.###',
        validateDecimalOnType: true,
        htmlAttributes: { type: 'number' },
        showSpinButton: false
      }
    }
  },
  // {
  //   field: 'purchaserPercent',
  //   headerText: i18n.t('tcl承担比例'),
  //   ignore: true
  // },
  // {
  //   field: 'purchaseAmt',
  //   headerText: i18n.t('tcl承担损失'),
  //   ignore: true
  // },
  // {
  //   field: 'supplierPercent',
  //   headerText: i18n.t('供应商承担比例'),
  //   ignore: true
  // },
  // {
  //   field: 'supplierAmt',
  //   headerText: i18n.t('供应商承担损失'),
  //   ignore: true
  // },
  {
    field: 'costCenter',
    headerText: i18n.t('成本中心代码'),
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('成本中心代码')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
  // {
  //   field: 'viewPrice',
  //   headerText: i18n.t('价格'),
  //   cellTools: [
  //     {
  //       id: 'viewPrice',
  //       title: i18n.t('点击查看'),
  //       visibleCondition: true
  //     }
  //   ],
  //   template: () => {
  //     return {
  //       template: Vue.component('viewPrice', {
  //         template: `<span style="color: #2783fe; cursor: pointer">{{$t('点击查看')}}</span>`
  //       })
  //     }
  //   }
  // }
]
export const attachmentColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'attachmentName',
    headerText: i18n.t('附件名称'),
    cellTools: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  },
  {
    field: 'attachmentSize',
    headerText: i18n.t('附件大小'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return `${Math.floor((e / 1024) * 100) / 100}KB`
      }
    },
    allowEditing: false
  },
  {
    field: 'uploadUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
