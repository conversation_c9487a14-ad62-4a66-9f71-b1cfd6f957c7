<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !supplierFeedbackExpand && 'top-filter-small']"
      class="top-filter"
      v-if="info.allowAppeal"
    >
      <div class="accordion-title">{{ $t('供方反馈') }}</div>
      <div class="sort-box" @click="supplierFeedbackExpand = !supplierFeedbackExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="supplierFeedbackExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form
          ref="feedbackFormInfo"
          :model="feedbackFormInfo"
          :rules="rules"
          :auto-complete="false"
        >
          <mt-form-item
            class="form-item"
            :label="$t('是否申诉')"
            label-style="top"
            prop="appealFlag"
          >
            <mt-select
              v-model="feedbackFormInfo.appealFlag"
              :data-source="booleanList"
              :placeholder="$t('请选择')"
              width="300"
              :disabled="isDisabled"
              @change="appealFlagChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('申诉内容')"
            label-style="top"
            prop="appealContext"
            v-if="feedbackFormInfo.appealFlag == 'true'"
          >
            <mt-select
              :disabled="isDisabled"
              v-model="feedbackFormInfo.appealContext"
              :data-source="claimTypeList"
              :placeholder="$t('请选择')"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item full-width"
            :label="$t('反馈意见')"
            label-style="top"
            v-if="isShow"
          >
            <mt-input
              v-model="feedbackFormInfo.appealSuggestion"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :placeholder="$t('字数不超过200字')"
              :disabled="isDisabled"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item full-width"
            :label="$t('反馈意见')"
            label-style="top"
            prop="appealSuggestion"
            v-else
          >
            <mt-input
              v-model="feedbackFormInfo.appealSuggestion"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :placeholder="$t('字数不超过200字')"
              :disabled="isDisabled"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !supplierAttachmentExpand && 'top-filter-small']"
      class="top-filter"
      v-if="info.allowAppeal"
    >
      <div class="accordion-title">
        <span v-if="basicInfo.needAttachment" style="color: red">{{ '*' }}&nbsp;</span
        >{{ $t('供方附件') }}
      </div>
      <div class="sort-box" @click="supplierAttachmentExpand = !supplierAttachmentExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="supplierAttachmentExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="supAttachmentRef"
          :template-config="supAttachmentConfig"
          @handleClickToolBar="handleClickSupAttachmentToolBar"
          @handleClickCellTool="handleClickSupCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('基本信息') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="basicInfo" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('反馈截止时间')"
            label-style="top"
            prop="feedbackEndTime"
          >
            <mt-date-time-picker
              :disabled="true"
              :width="300"
              v-model="basicInfo.feedbackEndTime"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item class="form-item" :label="$t('币种')" label-style="top" prop="currencyName">
            <mt-input :disabled="true" v-model="basicInfo.currencyName" width="300"></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('索赔总额')"
            label-style="top"
            prop="claimTotalAmount"
          >
            <mt-input :disabled="true" :width="300" v-model="basicInfo.claimTotalAmount"></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('发票')"
            label-style="top"
            v-if="queryType == 17 && invoiceList.length > 0"
          >
            <mt-common-uploader
              :is-view="true"
              :is-single-file="false"
              type="line"
              v-model="invoiceList"
            ></mt-common-uploader>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="templateRef"
          :template-config="assessmentIndexConfig"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('原因说明') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-input
          ref="editorRef"
          v-model="basicInfo.reasonDesc"
          :multiline="true"
          :rows="2"
          :disabled="true"
        ></mt-input>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('附件') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="purAttachmentRef"
          :template-config="purchaseAttachmentConfig"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
import { download } from '@/utils/utils'
import { assessmentIndexColumn, attachmentColumn, editSettings } from '../config/index'
export default {
  components: {},
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    },
    isDisabled() {
      let _tempFlag
      if (
        this.queryType == 11 ||
        this.queryType == 12 ||
        this.queryType == 15 ||
        this.queryType == 16 ||
        this.queryType == 17
      ) {
        _tempFlag = true
      } else {
        _tempFlag = false
      }
      return _tempFlag
    }
  },
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    btn: {
      type: String,
      default: ''
    }
  },
  created() {
    if (
      this.queryType == 11 ||
      this.queryType == 12 ||
      this.queryType == 15 ||
      this.queryType == 16 ||
      this.queryType == 17
    ) {
      delete this.supAttachmentConfig[0].toolbar
    }
    if (this.queryType == 17) {
      this.$API.assessManage.listClaimInvoice({ id: this.queryId }).then((res) => {
        this.invoiceList.length = 0
        if (res.data.length > 0) {
          res.data.forEach((e) => {
            this.invoiceList.push({
              id: e.attachmentId,
              fileName: e.attachmentName,
              fileSize: e.attachmentSize,
              url: e.attachmentUrl,
              uploadTime: e.uploadTime,
              uploadUserId: e.uploadUserId,
              uploadUserName: e.uploadUserName
            })
          })
        }
      })
    }
  },
  mounted() {
    console.log('btnntmntm', this.btn, this.info)
    if (this.btn === 'bill' && this.info.status >= 12 && this.info.offlineEnsure === true) {
      this.supAttachmentConfig[0].toolbar = {
        useBaseConfig: false,
        tools: [
          ['Add', 'Edit', 'Delete'],
          [{ id: 'Save', icon: 'icon_solid_edit', title: this.$t('保存') }]
        ]
      }
    }
    // else {
    //   delete this.supAttachmentConfig[0].toolbar
    // }

    if (this.info.createType != 1) {
      this.claimTypeList.splice(1, 0, { text: this.$t('申诉考核指标'), value: 1 })
    }
    this.basicInfo = {
      ...this.info,
      feedbackEndTime: new Date(Number(this.info.feedbackEndTime))
    }
    this.feedbackFormInfo = {
      ...this.info.claimAppeal,
      appealFlag: this?.info?.claimAppeal?.appealFlag?.toString()
    }
    // this.info.standDetailList.forEach(
    //   (e) =>
    //     (e.happenTime = utils.formateTime(
    //       new Date(Number(e.happenTime)),
    //       "yyyy-MM-dd hh:mm"
    //     ))
    // );
    this.info.costCenterList.forEach((e) => (e.costCenterAddId = this.costCenterAddId++))
    setTimeout(() => {
      this.assessmentIndexConfig[0].grid.dataSource = this.info.standDetailList
      this.purchaseAttachmentConfig[0].grid.dataSource = this.info.attachmentList
    }, 0.17)
    // this.info.claimAppealAttachmentList.forEach(
    //   (e) =>
    //     (e.uploadTime = utils.formateTime(
    //       new Date(Number(e.uploadTime)),
    //       "yyyy-MM-dd hh:mm"
    //     ))
    // );
    setTimeout(() => {
      this.supAttachmentConfig[0].grid.dataSource = this?.info?.claimAppealAttachmentList || []
    }, 0.17)
  },
  data() {
    return {
      invoiceList: [],
      isShow: true,
      currencyList: [],
      claimTypeList: [
        { text: this.$t('申诉考核金额'), value: 0 },
        { text: this.$t('非我公司责任'), value: 2 },
        { text: this.$t('其他'), value: 3 }
      ],
      templateText: '',
      isPerPublish: true,
      booleanList: [
        { text: this.$t('否'), value: 'false' },
        { text: this.$t('是'), value: 'true' }
      ],
      feedbackFormInfo: {
        appealFlag: 'false'
      },
      basicInfo: {},
      rules: {
        appealFlag: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        appealContext: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        appealSuggestion: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      basicExpand: true,
      supplierAttachmentExpand: true,
      supplierFeedbackExpand: true,
      purchaseAttachmentConfig: [
        {
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 100,
            dataSource: []
          }
        }
      ],
      supAttachmentConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Edit', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 100,
            dataSource: []
          }
        }
      ],
      assessmentIndexConfig: [
        {
          grid: {
            allowPaging: false,
            lineIndex: 1,
            columnData: assessmentIndexColumn,
            height: 100,
            dataSource: []
          }
        }
      ]
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'attachmentName') {
        this.preview(e.data)
      }
    },
    preview(item) {
      let params = {
        id: item.attachmentId,
        useType: 1
      }
      this.$API.SupplierPunishment.filepreview(params).then((res) => {
        window.open(res.data)
      })
    },
    appealFlagChange(e) {
      if (e.value == 'true') {
        this.isShow = false
      } else {
        this.isShow = true
      }
    },
    // 下载文件
    handleDownloadFile(data) {
      this.$API.fileService
        .downloadPublicFile({
          id: data.attachmentId
        })
        .then((res) => {
          download({
            fileName: data.attachmentName,
            blob: res.data
          })
        })
    },
    handleClickSupAttachmentToolBar(e) {
      let sltList = e.grid.getSelectedRecords()
      let selectedRowIndexes = e.grid.getSelectedRowIndexes()
      if (
        (!sltList || sltList.length <= 0) &&
        (e.toolbar.id == 'Edit' || e.toolbar.id == 'Delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            fileData: [],
            isView: false, // 是否为预览
            required: false, // 是否必须
            title: this.$t('新增供方附件')
          },
          success: (res) => {
            this.handleUploadFiles(res)
          }
        })
      } else if (e.toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            title: this.$t('编辑供方附件'),
            isEdit: true,
            info: {
              ...sltList[0],
              fileName: sltList[0].attachmentName,
              fileSize: sltList[0].attachmentSize,
              url: sltList[0].attachmentUrl,
              createTime: sltList[0].uploadTime
            },
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            let _tempData = cloneDeep(this.supAttachmentConfig[0].grid.dataSource)
            console.log('_tempData', _tempData)
            // _tempData[selectedRowIndexes[0]] = data
            _tempData[selectedRowIndexes[0]] = {
              ...data,
              attachmentId: data.id,
              attachmentName: data.fileName,
              attachmentSize: data.fileSize,
              attachmentUrl: data.url
            }
            this.$set(this.supAttachmentConfig[0].grid, 'dataSource', _tempData)
            this.$refs.supAttachmentRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id == 'Delete') {
        let _tempData = cloneDeep(this.supAttachmentConfig[0].grid.dataSource)
        let _fileIds = sltList.map((x) => x.id)
        let _newData = _tempData.filter((element) => !_fileIds.includes(element.id))
        this.$set(this.supAttachmentConfig[0].grid, 'dataSource', _newData)
      } else if (e.toolbar.id == 'Save') {
        this.saveAdditionalAttachmentFn()
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'download') {
        this.handleDownloadFile(data)
      }
    },
    handleClickSupCellTool(e) {
      const { tool, data } = e
      const { id } = tool || {}
      if (id == 'download') {
        this.handleDownloadFile(data)
      }
    },
    handleUploadFiles(data) {
      console.log('handleUploadFiles=', data)
      let _tempData = {
        ...data,
        attachmentId: data.id,
        attachmentName: data.fileName,
        attachmentSize: data.fileSize,
        attachmentUrl: data.url
      }
      this.supAttachmentConfig[0].grid.dataSource.push(_tempData)
    },
    // 发票补录附件保存
    saveAdditionalAttachmentFn() {
      const dataSource = cloneDeep(this.supAttachmentConfig[0].grid.dataSource)
      const ary = []
      dataSource.forEach((item) => {
        ary.push({
          uploadUser: item.uploadUserName,
          uploadTime: item.uploadTime,
          remark: item.remark,
          attachmentId: item.id,
          attachmentName: item.fileName,
          attachmentSize: item.fileSize,
          attachmentUrl: item.url
        })
      })

      const data = {
        attachmentItemList: ary,
        claimCode: this.info.claimCode,
        type: 2
      }
      this.$API.assessManage.saveAdditionalAttachment(data).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('新增成功'),
            type: 'success'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../../../../../node_modules/@mtech/mtech-common-uploader/build/esm/bundle.css';
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
/deep/ .mt-rich-text-editor {
  margin: 30px 10px 0 10px;
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-toolbar-items {
    margin-left: -59px;
  }
  .e-rte-content {
    height: 300px !important;
  }
}
</style>
