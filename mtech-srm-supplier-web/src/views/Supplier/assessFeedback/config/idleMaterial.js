import { i18n } from '@/main.js'
import Vue from 'vue'
export const idleMaterialCols = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'claimCode',
    headerText: i18n.t('索赔单编码'),
    cellTools: [
      {
        id: 'edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.status == 1 || data.status == 4 || data.status == 7
        }
      },
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data.status == 1 || data.status == 4 || data.status == 7
        }
      }
    ]
  },
  {
    field: 'claimDimension',
    headerText: i18n.t('索赔维度'),
    template: function () {
      return {
        template: Vue.component('claimDimension', {
          template: `<span>{{ $t('呆料索赔') }}</span>`
        })
      }
    },
    ignore: true
  },
  // {
  //   field: 'companyName',
  //   headerText: i18n.t('索赔指标'),
  //   width: '220'
  // },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司'),
    width: '220'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '220'
  },
  // {
  //   field: 'supplierCode',
  //   headerText: i18n.t('供应商代码'),
  //   width: '220'
  // },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('新建'),
        2: i18n.t('赔付比例审批中'),
        3: i18n.t('赔付比例审批拒绝'),
        4: i18n.t('赔付比例审批通过'),
        5: i18n.t('索赔单待审批'),
        6: i18n.t('已废弃'),
        7: i18n.t('索赔单审批拒绝'),
        8: i18n.t('扣款失败'),
        9: i18n.t('已完成'),
        10: i18n.t('已入账'),
        13: i18n.t('索赔用章审批中'),
        14: i18n.t('索赔用章审批拒绝'),
        15: i18n.t('索赔用章审批通过')
      }
    }
  },
  {
    field: 'purchaseAmt',
    headerText: i18n.t('TCL承担损失'),
    ignore: true
  },
  {
    field: 'supplierAmt',
    headerText: i18n.t('供应商承担损失'),
    ignore: true
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    searchOptions: {
      renameField: 'mc.createUserName'
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      renameField: 'mc.createTime',
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x, i) => {
          if (i === 1) {
            return Number(new Date(x.toString().replace('00:00:00', '23:59:59')).getTime())
          }
          return Number(new Date(x.toString()).getTime())
        })
      }
    },
    ignore: true
  },
  // {
  //   field: 'deductVoucherNo',
  //   headerText: i18n.t('扣款凭证号')
  // },
  // {
  //   field: 'sapDeductVoucherNo',
  //   headerText: i18n.t('SAP扣款凭证号')
  // },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    ignore: true
  }
]
