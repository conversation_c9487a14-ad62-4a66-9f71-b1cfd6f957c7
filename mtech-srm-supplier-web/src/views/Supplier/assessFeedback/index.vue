<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { firstCols, appealCols, lastCols } from './config/index'
import { idleMaterialCols } from './config/idleMaterial'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '7824cdc9-8bbe-4daf-ad95-66578b476c74',
          title: this.$t('待反馈'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'feedback',
                  icon: 'icon_table_new',
                  title: this.$t('反馈考核单')
                },
                {
                  id: 'print',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                },
                {
                  id: 'export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            frozenColumns: 1,
            columnData: firstCols.concat(lastCols),
            asyncConfig: {
              url: '/analysis/tenant/supplierClaim/pageClaim',
              params: { type: 0 },
              defaultRules: []
            }
          }
        },
        {
          gridId: '7630a402-bd8d-4fc3-acf6-ad70f9bfa27f',
          title: this.$t('考核历史'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'historyPrint',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                },
                {
                  id: 'supplement',
                  icon: 'icon_table_new',
                  title: this.$t('附件补录')
                },
                {
                  id: 'export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            frozenColumns: 1,
            columnData: firstCols.concat(appealCols, lastCols),
            asyncConfig: {
              url: '/analysis/tenant/supplierClaim/pageClaim',
              params: { type: 1 }
            }
          }
        },
        {
          gridId: 'b4bd5fba-72cc-c445-6c15-b4e4bbd5ce1f',
          title: this.$t('呆料索赔单'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'idleMaterialPrint',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            frozenColumns: 1,
            columnData: idleMaterialCols,
            asyncConfig: {
              url: '/analysis/tenant/idleMaterialClaimVoucher/supplierHeaderPageQuery',
              params: { type: 1 }
            }
          }
        }
      ]
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.pageConfig[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
    }
  },
  methods: {
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()

      if (item.toolbar.id == 'export') {
        this.handleExport()
      }
      if (records.length <= 0 && item.toolbar.id !== 'export') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'print') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能打印单行'), type: 'warning' })
          return
        }
        this.printPdf(records[0].id)
      }
      if (item.toolbar.id == 'historyPrint') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能打印单行'), type: 'warning' })
          return
        }
        this.historyPrint(records[0].id)
      }
      if (item.toolbar.id == 'idleMaterialPrint') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能打印单行'), type: 'warning' })
          return
        }
        this.idlePrintPdf(records[0].id)
      }
      if (item.toolbar.id == 'supplement') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能附件补录单行'), type: 'warning' })
          return
        }
        this.$router.push({
          name: 'purchase-assessmanage-assessFeedbackDetail',
          query: {
            btn: 'bill',
            type: records[0].status,
            id: records[0].id
          }
        })
      }
      if (item.toolbar.id == 'feedback') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
          return
        }

        this.$router.push({
          name: 'purchase-assessmanage-assessFeedbackDetail',
          query: {
            type: records[0].status,
            id: records[0].id
          }
        })
      }
    },
    handleExport() {
      let params = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.asyncParams || {}
      this.$API.assessFeedback.exportClaim(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    async historyPrint(id) {
      let buffer = await this.$API.assessFeedback.printClaim({ id }).catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    // 呆料打印
    async idlePrintPdf(data) {
      const formdata = new FormData()
      formdata.append('id', data)
      let buffer = await this.$API.deadMaterials.purchaserPrintClaim(formdata).catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    async printPdf(data) {
      let buffer = await this.$API.assessFeedback.printClaim({ id: data }).catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    handleClickCellTool() {},
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      if (e.field == 'claimCode') {
        let { data } = e
        const urlName =
          e.tabIndex === 2
            ? 'purchase-assessmanage-assessFeedback-idleMaterialDetail'
            : 'purchase-assessmanage-assessFeedbackDetail'
        this.$router.push({
          name: urlName,
          query: {
            type: data.status,
            id: data.id
          }
        })
      }
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
