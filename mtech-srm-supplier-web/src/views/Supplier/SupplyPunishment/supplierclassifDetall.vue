<template>
  <div class="supplierDetall-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="header-content flex1 fbox">
        <div class="titles-box">
          <div class="mian-title" v-if="!!infoDTO">
            {{ infoDTO.applyName || '--' }}
          </div>
          <div class="btns-box fbox">
            <div class="invite-btn" @click="onBack">{{ $t('返回') }}</div>
            <template v-if="canEdit">
              <div class="invite-btn" @click="saveText">{{ $t('保存') }}</div>
              <div class="invite-btn" @click="saveAndSubmit">{{ $t('保存并提交') }}</div>
            </template>
          </div>
          <div class="sub-title fbox">
            <div class="normal-title" v-if="!!infoDTO">
              {{ $t('申请单编码') }}：{{ infoDTO.applyCode }}
            </div>
            <div class="normal-title" v-if="!!infoDTO">
              {{ $t('申请人') }}：{{ infoDTO.applyerName || '--' }}
            </div>
            <div class="normal-title" v-if="!!infoDTO">
              {{ $t('申请人部门') }}：{{ infoDTO.applyerDeptName || '--' }}
            </div>
            <div class="normal-title" v-if="!!infoDTO">
              {{ $t('申请结束时间') }}：{{ infoDTO.applyFinishTime || '--' }}
            </div>
          </div>
          <div class="sub-title fbox">
            <div class="normal-title-dan" v-if="!!relieveDTO">
              <span>{{ $t('类型') }}：{{ applicationformtype[relieveDTO.businessType] }}</span>
            </div>
            <div class="scroll-box fbox">
              <div class="normal-title-box" v-for="(item, index) in relationDTOList" :key="index">
                <div class="gong-title">{{ item.customerEnterpriseName }}</div>
                <div class="gong-id">{{ item.customerEnterpriseCode }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 头部结束 -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <template v-if="!!id">
      <detail-edit
        v-if="selectIndex === 0"
        :id="id"
        :apply-info="infoDTO"
        :can-edit="canEdit"
        ref="editRef"
      ></detail-edit>
      <operator-history v-if="selectIndex === 1" :id="id" :apply-info="infoDTO"></operator-history>
    </template>
  </div>
</template>

<script>
import detailEdit from './components/detailEdit.vue'
import operatorHistory from './components/operatorHistory.vue'
import utils from '@/utils/utils'
import { applicationformtype } from '@/utils/setting'
export default {
  components: {
    detailEdit,
    operatorHistory
  },
  filters: {
    filterCreateTime: function (value) {
      return utils.formateTime(Number(value), 'yyyy-MM-dd hh:mm:ss')
    }
  },
  computed: {
    applicationformtype() {
      return applicationformtype
    },
    infoDTO() {
      return this.applyInfo.infoDTO
    },
    relieveDTO() {
      return this.applyInfo.relieveDTO
    }
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('单据配置')
        },
        {
          title: this.$t('操作记录')
        }
      ],
      id: '',
      canEdit: false,
      applyInfo: {}
    }
  },
  created() {
    let id = this.$route.query.id
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getAccessDetail(id)
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },

    // 保存 富文本 // console.log(this.$refs.MtRichTextEditor.ejsRef.getHtml()) // console.log(this.$refs.MtRichTextEditor.ejsRef.getText())
    saveText(fn) {
      this.$loading()
      let applyReason = ''
      try {
        applyReason = this.$refs.editRef.$refs.MtRichTextEditor.ejsRef.getText()
      } catch (error) {
        this.$hloading()
      }

      let { infoDTO, relieveDTO, relationDTOList } = this.applyInfo
      let query = {
        infoDTO: {
          ...infoDTO,
          applyReason
        },
        relieveDTO,
        relationDTOList
      }

      this.$API.SupplyPunishment.queryReliveUpdate(query)
        .then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('更新解除处罚详情成功！'),
              type: 'success'
            })

            !!fn && typeof fn === 'function' && fn()
          } else {
            this.$toast({
              content: this.$t('更新解除处罚失败，请重试!'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('保存详情失败，请重试!'),
            type: 'warning'
          })
        })
    },
    // 保存并提交
    saveAndSubmit() {
      this.$loading()
      this.saveText(() => {
        this.$API.SupplyPunishment.queryStartFlow({
          applyIdList: [this.infoDTO.id]
        }).then((res) => {
          this.$hloading()
          if (res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('提交解除处罚详情成功! 即将跳回'),
              type: 'success'
            })

            setTimeout(() => {
              this.onBack()
            }, 600)
          } else {
            this.$toast({
              content: this.$t('提交解除处罚失败，请重试!'),
              type: 'warning'
            })
          }
        })
      })
    },

    handleSelectTab(index) {
      this.selectIndex = index
    },
    getAccessDetail(id) {
      this.$API.SupplyPunishment.queryReliveDetail({ id }).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.applyInfo = res.data
          this.canEdit =
            res.data.infoDTO.applyStatus === 20 || res.data.infoDTO.applyStatus === 40
              ? false
              : true
        } else {
          this.$toast({
            content: this.$t('获取解除处罚详情失败，请重试!'),
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.supplierDetall-container {
  height: 100%;
  padding-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .header-content {
      width: 100%;
      justify-content: space-between;
      .titles-box {
        width: 100%;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }

        .sub-title {
          font-size: 12px;
          color: rgba(41, 41, 41, 1);

          margin-top: 16px;

          .normal-title {
            font-size: 12px;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }

          .normal-title-dan {
            font-size: 14px;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;

            span {
              font-weight: 600;
            }

            .b-color {
              color: #00469c;
            }
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        max-width: 300px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        justify-content: flex-end;

        .invite-btn {
          margin-right: 40px;
          cursor: pointer;
          font-weight: 600;
        }
      }
    }
  }
}
.mt-tabs {
  margin-left: 30px;
}
</style>
