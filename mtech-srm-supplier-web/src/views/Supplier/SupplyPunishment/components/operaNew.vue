<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="share-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="24">
            <mt-form-item prop="supplierEnterpriseName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                maxlength="200"
                :multiline="true"
                :rows="3"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :hidden-tabs="true"
          :padding-top="false"
          :use-tool-template="false"
          :template-config="componentConfig"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
import { columnInner } from '../config/columns'
export default {
  data() {
    return {
      componentConfig: [
        {
          gridId: 'c7793651-f62e-4df9-9dae-03f377f7c732',
          toolbar: [],
          grid: {
            columnData: columnInner,
            dataSource: []
          }
        }
      ],
      formInfo: {
        supplierEnterpriseName: '',
        remark: ''
      },
      rules: {
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入申请单名称名称'), trigger: 'blur' }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('保存') }
        },
        {
          click: this.confirmAndIn,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.info
    }
  },
  mounted() {
    this.show()
    this.renderData()
  },
  methods: {
    renderData() {
      this.$set(this.componentConfig[0].grid, 'dataSource', [this.info])
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    confirm(method = '') {
      let formInfo = this.formInfo
      let infoDTO = {
        applyName: formInfo.supplierEnterpriseName,
        applyReason: '',
        remark: formInfo.remark
      }
      let relationDTOList = [
        {
          partnerArchiveId: this.info.partnerArchiveId,
          relatedBillId: this.info.id,
          partnerRelationId: this.info.partnerRelationId
        }
      ]
      let relieveDTO = {
        remark: formInfo.remark
      }
      let query = {
        infoDTO,
        relationDTOList,
        relieveDTO
      }
      this.$API.SupplyPunishment.addRelive(query)
        .then((res) => {
          if (!utils.isEmpty(res) && res.code === 200 && !utils.isEmpty(res.data)) {
            this.$toast({
              content: this.$t('新增申诉单成功！'),
              type: 'success'
            })
            this.$emit('confirm-function', method)
          } else {
            this.$toast({
              content: this.$t('新增申诉单失败,请重试！'),
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.$toast({
            content: error.msg ? error.msg : this.$t('新增申诉单失败！'),
            type: 'warning'
          })
        })
    },

    confirmAndIn() {
      this.confirm('jumpIn')
    },

    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.share-dialog {
  padding: 40px 20px;
}
</style>
