<template>
  <div class="questionaire-history flex1">
    <div class="period-wrap">
      <!-- 阶段块 start-->
      <template v-if="operateRecord.length">
        <div
          class="period-item"
          :class="{ active: index === 0 }"
          v-for="(item, index) in operateRecord"
          :key="index"
        >
          <div class="pot-box"></div>
          <div class="period-time">{{ item.updateTime | fliterTime }}</div>
          <div class="period-des fbox">
            <div class="des-name">{{ item.operateName }}</div>
            <div class="des-changer flex1">{{ item.operateName }}</div>
            <template v-if="item.operateName === $t('驳回')">
              <div class="reject-reason" @click="showRejectReason(item)">
                {{ $t('查看驳回原因') }}
              </div>
            </template>
          </div>
        </div>
        <!-- 阶段块 end-->
      </template>
    </div>

    <template v-if="operateRecord.length === 0 || !operateRecord">
      <empty-data :msg="msg"></empty-data>
    </template>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import emptyData from '@/components/emptyData/emptyData.vue'
export default {
  components: {
    emptyData
  },
  data() {
    return {
      operateRecord: [],
      msg: this.$t('获取操作历史失败，请重试！')
    }
  },
  filters: {
    fliterTime: function (value) {
      let formatData = ''
      if (value) {
        let applyFinishTime = value.replace(/T/g, ' ')
        formatData = utils.formateTime(applyFinishTime, 'yyyy年MM月dd日 hh:mm:ss')
      }
      return formatData
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  watch: {
    id: {
      handler(val) {
        if (val) {
          this.getOperateRecord(val)
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {},
  methods: {
    getOperateRecord(nv) {
      this.$loading()
      // 获取操作表历史
      this.$API.supplierInfoChange
        .operateRecord({ bizId: nv })
        .then((res) => {
          this.$loading()
          let { code, data } = res
          if (code === 200 && !utils.isEmpty(data)) {
            this.operateRecord = data
          } else {
            this.operateRecord = []
            this.msg = res.msg
            // this.$toast({
            //   content: this.msg || "获取操作历史失败，请重试！",
            //   type: "warning"
            // })
          }
        })
        .catch((err) => {
          this.operateRecord = []
          this.msg = err.msg || this.$t('获取操作历史失败，请重试！')
          // this.msg = err.msg
          // this.$toast({
          //   content: err.msg,
          //   type: "warning"
          // })
          this.$hloading()
        })
    },
    showRejectReason(item) {
      this.$dialog({
        data: {
          title: this.$t('驳回原因'),
          message: item.operateDescription
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.questionaire-history {
  padding: 40px;
  width: 100%;
  height: 100%;
  background: #fff;
  position: relative;

  .period-wrap {
    position: relative;
  }

  .period-item {
    padding-left: 24px;
    position: relative;

    &::before {
      content: '';
      display: block;
      width: 1px;
      height: 100%;
      background: #e8e8e8;
      position: absolute;
      left: 5px;
      top: 0;
    }

    .pot-box {
      width: 12px;
      height: 14px;
      position: absolute;
      left: 0;
      top: 0;
      background: #fff;

      &::before {
        content: '';
        display: inline-block;
        width: 7px;
        height: 7px;
        border: 1px solid #9a9a9a;
        box-sizing: border-box;
        position: absolute;
        border-radius: 100%;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%, -50%, 0);
        z-index: 2;
      }

      &::after {
        content: '';
        width: 12px;
        height: 1px;
        background: #9a9a9a;
        position: absolute;
        top: 7px;
        left: 0;
        z-index: 1;
      }
    }

    .period-time {
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      font-weight: normal;
      color: #9a9a9a;
    }

    .period-des {
      padding: 30px 0;
      max-width: 400px;
      position: relative;

      .des-name {
        font-size: 16px;
        font-weight: normal;
        color: #9a9a9a;
      }

      .des-detail {
        font-size: 14px;
        font-weight: normal;
        color: rgba(0, 70, 156, 1);
        margin-left: 10px;
        cursor: pointer;
      }

      .des-changer {
        font-size: 16px;
        font-weight: normal;
        color: #9a9a9a;
        text-align: right;
        white-space: nowrap;
      }

      .reject-reason {
        position: absolute;
        left: 100%;
        color: rgba(0, 70, 156, 1);
        font-size: 14px;
        margin-left: 10px;
        margin-top: 1px;
        white-space: nowrap;
        cursor: pointer;
      }
    }
  }

  // 顶部的
  .active {
    padding-left: 24px;
    position: relative;

    &::before {
      content: '';
      display: block;
      width: 1px;
      height: 100%;
      background: #e8e8e8;
      position: absolute;
      left: 5px;
      top: 0;
    }

    .pot-box {
      width: 12px;
      height: 14px;
      position: absolute;
      left: 0;
      top: 0;
      background: #fff;

      &::before {
        content: '';
        display: inline-block;
        width: 7px;
        height: 7px;
        background: #00469c;
        border: none;
        box-sizing: border-box;
        position: absolute;
        border-radius: 100%;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%, -50%, 0);
        z-index: 2;
      }

      &::after {
        content: '';
        width: 12px;
        height: 1px;
        background: #00469c;
        position: absolute;
        top: 7px;
        left: 0;
        z-index: 1;
      }
    }

    .period-time {
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      font-weight: normal;
      color: #292929;
    }

    .period-des {
      padding: 30px 0;
      max-width: 400px;

      .des-name {
        font-size: 16px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        padding-right: 10px;
      }

      .des-detail {
        font-size: 14px;
        font-weight: normal;
        color: rgba(0, 70, 156, 1);
        margin-left: 10px;
      }

      .des-changer {
        font-size: 16px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        text-align: right;
      }
    }
  }
}
</style>
