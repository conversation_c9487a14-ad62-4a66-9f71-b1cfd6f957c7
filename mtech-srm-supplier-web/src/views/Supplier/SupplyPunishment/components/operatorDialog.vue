<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="share-dialog">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="contactName" class="form-item" :label="$t('申请单名称')">
              <mt-input
                v-model="formInfo.supplierEnterpriseName"
                :disabled="false"
                :width="400"
                :show-clear-button="true"
                type="text"
                :placeholder="$t('请输入申请单名称')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierType" class="form-item" :label="$t('申请类型')">
              <mt-select
                v-model="formInfo.supplierType"
                :width="400"
                :data-source="claimArr"
                :show-clear-button="true"
                :placeholder="$t('请选择申请类型')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="contactName" class="form-item" :label="$t('源组织')">
              <mt-select
                v-model="formInfo.supplierType"
                :width="400"
                :data-source="claimArr"
                :show-clear-button="true"
                :placeholder="$t('请选择源组织')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierType" class="form-item" :label="$t('目标组织')">
              <mt-select
                v-model="formInfo.supplierType"
                :width="400"
                :data-source="claimArr"
                :show-clear-button="true"
                :placeholder="$t('请选择目标组织')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="20">
          <mt-col :span="8">
            <mt-form-item prop="contactName" class="form-item" :label="$t('申请人')">
              <mt-select
                v-model="formInfo.supplierType"
                :width="260"
                :data-source="claimArr"
                :show-clear-button="true"
                :placeholder="$t('请选择申请人')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="supplierType" class="form-item" :label="$t('申请人公司')">
              <mt-select
                v-model="formInfo.supplierType"
                :width="260"
                :data-source="claimArr"
                :show-clear-button="true"
                :placeholder="$t('请选择申请人公司')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item prop="supplierType" class="form-item" :label="$t('申请人部门')">
              <mt-select
                v-model="formInfo.supplierType"
                :width="260"
                :data-source="claimArr"
                :show-clear-button="true"
                :placeholder="$t('请选择申请人部门')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="remark" class="form-item" :label="$t('备注')">
              <mt-input
                css-class="e-outline"
                v-model="formInfo.remark"
                :disabled="false"
                :show-clear-button="true"
                maxlength="200"
                :multiline="true"
                :rows="3"
                type="text"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>

      <div class="supplier-table">
        <mt-template-page
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { applicationforme } from '../config/columns'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: 'da7581ef-84d0-450c-be0a-facec508274b',
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('新增')
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: applicationforme,
            asyncConfig: {
              url: '/supplier/tenant/buyer/template/task/query'
            }
          }
        }
      ],
      claimArr: [],
      formInfo: {
        supplierEnterpriseName: '',
        supplierType: -1,
        remark: ''
      },
      rules: {
        supplierEnterpriseName: [
          { required: true, message: this.$t('请输入准入流程名称'), trigger: 'blur' }
        ],
        taskTemplateType: [{ required: true, message: this.$t('请选择流程类型'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.show()
  },
  methods: {
    show() {
      this.$refs['dialog'].ejsRef.show()
    },

    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },

    confirm() {
      this.$emit('confirm-function')
    },

    cancel() {
      this.$emit('cancel-function')
    },

    handleClickToolBar() {},
    handleClickCellTool() {},
    handleClickCellTitle() {}
  }
}
</script>

<style lang="scss" scoped>
.share-dialog {
  padding: 40px 20px;
}
</style>
