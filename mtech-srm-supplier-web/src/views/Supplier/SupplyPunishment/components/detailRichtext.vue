<template>
  <div>
    <mt-rich-text-editor
      ref="MtRichTextEditor"
      :toolbar-settings="toolbarSettings"
      :background-color="backgroundColor"
      v-model="value"
      @change="changeText"
      @created="createdTextEditor"
    >
    </mt-rich-text-editor>
  </div>
</template>

<script>
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
export default {
  components: {
    MtRichTextEditor
  },
  data() {
    return {
      // toolbarSettings: {
      //   enable: true,
      //   enableFloating: true,
      //   type: 'Expand',
      //   items: [
      //     'Bold',
      //     'Italic',
      //     'Underline',
      //     '|',
      //     'Formats',
      //     'Alignments',
      //     'OrderedList',
      //     'UnorderedList',
      //     '|',
      //     'CreateLink',
      //     'Image',
      //     'backgroundColor',
      //     '|',
      //     'SourceCode',
      //     'Undo',
      //     'Redo'
      //   ],
      //   itemConfigs: {}
      // },
      backgroundColor: {
        columns: 5,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      },
      value: '',
      toolbarSettings: { enable: true, enableFloating: true, type: 'Expand' }
    }
  },

  computed: {},

  mounted: {},

  methods: {}
}
</script>
<style lang="scss" scoped></style>
