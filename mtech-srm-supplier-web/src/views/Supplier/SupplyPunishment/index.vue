<template>
  <div class="supplier-puniment">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :hidden-tabs="false"
      :padding-top="true"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columns, columnse } from './config/columns'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: 'ba0288b8-751e-4b39-9b15-a2de45bf0a52',
          title: this.$t('惩罚信息'),
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createproject',
                  title: this.$t('新增申诉')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnse,
            asyncConfig: {
              url: '/supplier/tenant/supplier/partner/punish/list'
            }
          }
        },
        {
          gridId: '24f6df35-17c9-45a5-9bab-d2bf4179a8ef',
          title: this.$t('申诉单'),
          toolbar: {
            tools: [
              [
                {
                  id: 'release',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('发布')
                },
                {
                  id: 'delete',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columns,
            asyncConfig: {
              url: '/supplier/tenant/supplier/apply/relive/list'
            }
          }
        }
      ]
    }
  },

  methods: {
    handleClickCellTitle(e) {
      //单元格Title点击
      const { field, data } = e
      if (field === 'enterApplyCode' && data && data.id) {
        this.$router.push({
          name: 'supplyPunishmentdetail',
          query: {
            id: data.id
          }
        })
        return
      }
      if (field === 'applyCode' && data && data.id) {
        this.$router.push({
          name: 'supplyApplyDetail',
          query: {
            id: data.id
          }
        })
        return
      }
    },
    handleClickToolBar(e) {
      //头部的图标
      const { toolbar, gridRef } = e
      let sltList = gridRef.getMtechGridRecords()
      if (toolbar.id === 'Add') {
        let toastTxt = ''
        if (sltList.length === 0) {
          toastTxt = this.$t('请先选择一行数据')
        } else if (sltList.length > 1) {
          toastTxt = this.$t('请一次只操作一条数据')
        } else if (sltList[0].status !== 10) {
          toastTxt = this.$t('仅处状态是处罚中的栏目才可以申诉！')
        }

        if (toastTxt) {
          this.$toast({ content: toastTxt, type: 'warning' })
          return
        }

        this.addNew(sltList[0])
      }

      if (toolbar.id === 'release') {
        if (sltList.length === 0) {
          let toastTxt = this.$t('请先选择一行数据')
          this.$toast({ content: toastTxt, type: 'warning' })
          return
        }

        this.handleRealse(sltList)
      }

      if (toolbar.id === 'delete') {
        if (sltList.length === 0) {
          let toastTxt = this.$t('请先选择一行数据')
          this.$toast({ content: toastTxt, type: 'warning' })
          return
        }
        this.handleDelete(sltList)
      }
    },

    addNew(data) {
      this.$dialog({
        modal: () => import('./components/operaNew.vue'),
        data: {
          title: this.$t('新建申诉单'),
          info: data
        },
        success: (method) => {
          this.$refs.templateRef.refreshCurrentGridData()
          if (method === 'jumpIn') {
            this.$router.push({
              path: 'supplyPunishmentdetail',
              query: {
                id: data.id
              }
            })
          }
        }
      })
    },

    handleRealse(sltList) {
      let ids = sltList.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('发布'),
          message: this.$t('是否发布所选申请？'),
          confirm: () => this.$API.SupplyPunishment['queryStartFlow']({ applyIdList: ids })
        },
        success: () => {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    handleDelete(sltList) {
      let ids = sltList.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否删除所选申请？'),
          confirm: () => this.$API.SupplyPunishment['reliveDelete']({ applyIdList: ids })
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-puniment {
  height: 100%;
}
</style>
