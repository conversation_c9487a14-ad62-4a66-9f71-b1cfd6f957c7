import { applicationformtype, statsePuniment } from '@/utils/setting'
import utils from '../../../../utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
// 惩罚单
export const columnInner = [
  {
    field: 'customerorgCode',
    width: '190',
    headerText: i18n.t('客户公司编号')
  },
  {
    field: 'customerorgName',
    headerText: i18n.t('客户公司名称'),
    width: '180',
    cellTools: []
  }
]
export const columnse = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'enterApplyCode',
    headerText: i18n.t('惩罚单编号'),
    width: '180',
    cellTools: []
  },
  {
    field: 'enterApplyName',
    width: '190',
    headerText: i18n.t('惩罚单名称')
  },
  {
    field: 'businessType',
    width: '120',
    headerText: i18n.t('惩罚类型'),
    valueConverter: {
      type: 'map',
      map: applicationformtype
    }
  },
  {
    field: 'customerEnterpriseCode',
    width: '120',
    headerText: i18n.t('客户编码')
  },
  {
    field: 'customerEnterpriseName',
    width: '160',
    headerText: i18n.t('客户公司名')
  },
  {
    field: 'penaltyPeriodStart',
    width: '160',
    headerText: i18n.t('惩罚时限'),
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="time-box" >
              <div>{{data.penaltyPeriodStart}}</div>
              <div>{{data.penaltyPeriodEnd}}</div>
            </div>`,
          data: function () {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">
            <span v-if="data.status == 20 " style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(99,134,193,0.1);
              color: #6386C1;
            " >{{ statusMap[data.status] }}</span>
            <span v-if="data.status == 10" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(237,86,51,.1);
              color: #ED5633;
            " >{{ statusMap[data.status] }}</span>
            <span v-if="data.status == 30" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(154,154,154,.1);
              color: #9A9A9A;
            " >{{ statusMap[data.status] }}</span>
            </div>`,
          data: function () {
            return {
              data: {},
              statusMap: statsePuniment
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('创建人')
  },
  {
    field: 'applyerDeptName',
    width: '100',
    headerText: i18n.t('创建人部门')
  },
  {
    field: 'createTime',
    width: '150',
    headerText: i18n.t('创建日期'),
    template: () => {
      return {
        template: Vue.component('time-tmp', {
          template: `<div class="time-box" >
              {{createTimeData}}
            </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            createTimeData() {
              return utils.formateTime(Number(this.data.createTime), 'yyyy-MM-dd hh:mm:ss')
            }
          }
        })
      }
    }
  }
]
// 申诉单
export const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('申诉单编号'),
    width: '180',
    cellTools: []
  },
  {
    field: 'applyName',
    width: '120',
    headerText: i18n.t('申诉单名称')
  },
  {
    field: 'enterApplyCode',
    width: '120',
    headerText: i18n.t('解除对象')
  },
  {
    field: 'customerEnterpriseName',
    width: '120',
    headerText: i18n.t('客户公司名')
  },
  {
    width: '110',
    field: 'applyStatus',
    headerText: i18n.t('状态'),
    template: () => {
      return {
        template: Vue.component('statusTemplate', {
          template: `<div class="status-wrap" style="">

            <span v-if="data.applyStatus == 10 || data.applyStatus == 20" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(99,134,193,0.1);
              color: #6386C1;
            " >{{ statusMap[data.applyStatus] }}</span>
            <span v-if="data.applyStatus == 30" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(237,86,51,.1);
              color: #ED5633;
            " >{{ statusMap[data.applyStatus] }}</span>
            <span v-if="data.applyStatus == 40 || data.applyStatus == 50" style="
              display: inline-block;
              padding: 0 4px;
              height: 20px;
              line-height: 20px;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 500;
              background: rgba(154,154,154,.1);
              color: #9A9A9A;
            " >{{ statusMap[data.applyStatus] }}</span>

            </div>`,
          data: function () {
            return {
              data: {},
              statusMap: {
                10: i18n.t('待提交'),
                20: i18n.t('待审批'),
                30: i18n.t('已驳回'),
                40: i18n.t('已完成'),
                50: i18n.t('已关闭')
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'applyerName',
    width: '100',
    headerText: i18n.t('申请人')
  },
  {
    field: 'createDate',
    width: '250',
    headerText: i18n.t('申请日期'),
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  }
]

// 附件
export const enclosurea = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    headerText: i18n.t('附件名称'),
    width: '180',
    cellTools: []
  },
  {
    field: 'fileSize',
    width: '120',
    headerText: i18n.t('附件大小')
  },
  {
    field: 'updateUserName',
    width: '120',
    headerText: i18n.t('上传人')
  },
  {
    field: 'modifyDate',
    width: '120',
    headerText: i18n.t('上传时间'),
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  },
  {
    field: 'remark',
    width: '180',
    headerText: i18n.t('备注')
  }
]
