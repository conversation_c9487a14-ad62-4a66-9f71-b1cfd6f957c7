<template>
  <div class="supplierDetall-container">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="header-content flex1 fbox">
        <div class="titles-box">
          <div class="mian-title" v-if="!!punishmentInfo">
            {{ punishmentInfo.enterApplyName || '--' }}
          </div>
          <div class="btns-box fbox">
            <div class="invite-btn" @click="onBack">{{ $t('返回') }}</div>
          </div>
          <div class="sub-title fbox">
            <div class="normal-title" v-if="!!punishmentInfo">
              {{ $t('惩罚单编码') }}：{{ punishmentInfo.enterApplyId }}
            </div>
            <div class="normal-title" v-if="!!punishmentInfo">
              {{ $t('客户公司名称') }}：{{ punishmentInfo.customerEnterpriseName }}
            </div>
            <div class="normal-title" v-if="!!punishmentInfo">
              {{ $t('客户公司编号') }}：{{ punishmentInfo.customerEnterpriseCode }}
            </div>

            <div class="normal-title" v-if="!!punishmentInfo">
              {{ $t('申请人') }}：{{ punishmentInfo.applyerName }}
            </div>
            <div class="normal-title" v-if="!!punishmentInfo">
              {{ $t('申请人部门') }}：{{ punishmentInfo.applyerDeptName }}
            </div>
            <div class="normal-title" v-if="!!punishmentInfo">
              {{ $t('申请时间') }}：{{ punishmentInfo.createTime | filterCreateTime }}
            </div>
          </div>
          <div class="sub-title fbox">
            <div class="normal-title-dan" v-if="!!punishmentInfo">
              <span>{{ $t('类型') }}：{{ applicationformtype[punishmentInfo.businessType] }}</span>
            </div>
            <div class="normal-title-dan" v-if="!!punishmentInfo">
              <span>{{ $t('惩罚开始时间') }}：{{ punishmentInfo.penaltyPeriodStart }}</span>
            </div>
            <div class="normal-title-dan" v-if="!!punishmentInfo">
              <span>{{ $t('惩罚结束时间') }}：{{ punishmentInfo.penaltyPeriodEnd }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 头部结束 -->
    <mt-tabs
      id="stage-config-tabs"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <template v-if="!!id">
      <detail-edit
        v-if="selectIndex === 0"
        :id="id"
        :apply-info="punishmentInfo"
        :can-edit="canEdit"
        ref="editRef"
      ></detail-edit>
      <operator-history
        v-if="selectIndex === 1"
        :id="id"
        :apply-info="punishmentInfo"
      ></operator-history>
    </template>
  </div>
</template>

<script>
import detailEdit from './components/detailEdit.vue'
import operatorHistory from './components/operatorHistory.vue'
import utils from '@/utils/utils'
import { applicationformtype } from '@/utils/setting'
export default {
  components: {
    detailEdit,
    operatorHistory
  },
  filters: {
    filterCreateTime: function (value) {
      return utils.formateTime(Number(value), 'yyyy-MM-dd hh:mm:ss')
    }
  },
  computed: {
    applicationformtype() {
      return applicationformtype
    }
  },
  data() {
    return {
      selectIndex: 0,
      dataSource: [
        {
          title: this.$t('单据配置')
        },
        {
          title: this.$t('操作记录')
        }
      ],
      id: '',
      canEdit: false,
      punishmentInfo: {}
    }
  },
  created() {
    let id = this.$route.query.id
    if (!id) {
      this.$toast({
        content: this.$t('获取ID失败，请重试!'),
        type: 'warning'
      })
      return
    }
    this.id = id
    this.getAccessDetail(id)
  },
  methods: {
    // 返回
    onBack() {
      this.$router.go(-1)
    },

    handleSelectTab(index) {
      this.selectIndex = index
    },
    getAccessDetail(id) {
      this.$API.SupplyPunishment.queryPunishDetail({ id }).then((res) => {
        if (res.code === 200 && !utils.isEmpty(res.data)) {
          this.punishmentInfo = res.data
          this.canEdit = res.data.status === 10 ? true : false
        } else {
          this.$toast({
            content: this.$t('获取共享详情失败，请重试!'),
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.fbox {
  display: flex;
}

.flex1 {
  flex: 1;
}

.supplierDetall-container {
  height: 100%;
  padding-top: 20px;

  .header-status {
    padding: 20px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .header-content {
      width: 100%;
      justify-content: space-between;
      .titles-box {
        width: 100%;
        flex-direction: column;
        justify-content: space-between;
        .mian-title {
          font-size: 20px;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }

        .sub-title {
          font-size: 12px;
          color: rgba(41, 41, 41, 1);

          margin-top: 16px;

          .normal-title {
            font-size: 12px;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;
          }

          .normal-title-dan {
            font-size: 14px;
            color: rgba(41, 41, 41, 1);
            margin-right: 30px;

            span {
              font-weight: 600;
            }

            .b-color {
              color: #00469c;
            }
          }
        }
      }

      .btns-box {
        align-items: center;
        font-size: 14px;
        max-width: 300px;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        justify-content: flex-end;

        .invite-btn {
          margin-right: 40px;
          cursor: pointer;
          font-weight: 600;
        }
      }
    }
  }
}
.mt-tabs {
  margin-left: 30px;
}
</style>
