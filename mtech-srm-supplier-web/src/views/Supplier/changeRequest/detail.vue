<template>
  <div class="detallWrap">
    <!-- 顶部信息 start -->
    <div class="header-status fbox">
      <div class="titles-box">
        <div class="mian-line fbox">
          <div class="mian-title">
            <template> {{ title }} </template>
          </div>
          <div class="btns-box fbox">
            <div class="invite-btn" @click="onBack">
              <mt-button css-class="e-info">{{ $t('返回') }}</mt-button>
            </div>
            <template>
              <div class="invite-btn" @click="saveText(false)" v-if="pageType != 'detail'">
                <mt-button css-class="e-info">{{ $t('保存') }}</mt-button>
              </div>
              <div class="invite-btn" @click="saveText(true)" v-if="pageType != 'detail'">
                <mt-button css-class="e-info">{{ $t('保存并提交') }}</mt-button>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="contentWrap">
      <div class="purInfo" v-show="pageType === 'detail'">
        <div class="b-info-title">{{ $t('采方处理信息') }}</div>
        <div class="purBox">
          <mt-form
            ref="effectiveOrgDTO"
            class="detail-effectiveorg--form labelWidth"
            :model="formData"
            :rules="rules"
          >
            <mt-row :gutter="12">
              <mt-col :span="24">
                <mt-form-item prop="qualitySuggest" :label="$t('品质评估意见')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.qualitySuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示品质风险及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
            <mt-row :gutter="12" class="boxMiddle">
              <mt-col :span="6">
                <mt-form-item prop="changeCode" :label="$t('采购评估人')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    v-model="formData.purchaseName"
                    :disabled="true"
                  />
                </mt-form-item>
              </mt-col>
              <mt-col :span="18">
                <mt-form-item prop="purchaseSuggest" :label="$t('采购评估意见')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.purchaseSuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示成本变化及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
            <mt-row :gutter="12" class="boxMiddle">
              <mt-col :span="6">
                <mt-form-item prop="devName" :label="$t('研发评估人')" label-style="left">
                  <mt-input css-class="e-outline" v-model="formData.devName" :disabled="true" />
                </mt-form-item>
              </mt-col>
              <mt-col :span="18">
                <mt-form-item prop="devSuggest" :label="$t('研发评估意见')" label-style="left">
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.devSuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示技术风险及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
            <mt-row :gutter="12" class="boxMiddle">
              <mt-col :span="6">
                <mt-form-item
                  prop="manufactProcessName"
                  :label="$t('制造工艺评估人')"
                  label-style="left"
                >
                  <mt-input
                    css-class="e-outline"
                    v-model="formData.manufactProcessName"
                    :disabled="true"
                  />
                </mt-form-item>
              </mt-col>
              <mt-col :span="18">
                <mt-form-item
                  prop="manufactProcessSuggest"
                  :label="$t('制造工艺评估意见')"
                  label-style="left"
                >
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.manufactProcessSuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示制造工艺及可操作性评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
            <mt-row :gutter="12" class="boxMiddle">
              <mt-col :span="6">
                <mt-form-item
                  prop="jointTrialName"
                  :label="$t('技术支持会审评估人')"
                  label-style="left"
                >
                  <mt-input
                    css-class="e-outline"
                    v-model="formData.jointTrialName"
                    :disabled="true"
                  />
                </mt-form-item>
              </mt-col>
              <mt-col :span="18">
                <mt-form-item
                  prop="jointTrialSuggest"
                  :label="$t('技术支持会审意见')"
                  label-style="left"
                >
                  <mt-input
                    css-class="e-outline"
                    ref="editorRef"
                    v-model="formData.jointTrialSuggest"
                    :multiline="true"
                    :rows="4"
                    :disabled="true"
                    :placeholder="$t('展示技术支持会审评估意见')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
          </mt-form>
        </div>
      </div>
      <div class="b-info-title">{{ $t('基本信息') }}</div>
      <div class="formWrap">
        <mt-form
          ref="formRef"
          class="detail-effectiveorg--form labelWidth2"
          :model="formData"
          :rules="rules"
        >
          <mt-row :gutter="12">
            <mt-col :span="8">
              <mt-form-item prop="changeCode" :label="$t('4M1E变更单号')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.changeCode"
                  :placeholder="$t('请输入4M1E变更单号')"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="bottom">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.supplierName"
                  :placeholder="$t('请输入供应商名称')"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="customerOrgCode" :label="$t('客户组织')" label-style="left">
                <mt-select
                  css-class="e-outline"
                  v-model="formData.customerOrgCode"
                  float-label-type="Never"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :show-clear-button="true"
                  :data-source="orgList"
                  :fields="{ text: 'orgName', value: 'orgCode' }"
                  :placeholder="$t('请选择客户组织')"
                  @change="handleFactoryChange($event)"
                  :disabled="pageType === 'detail'"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :span="24">
              <mt-form-item prop="orgName" :label="$t('客户公司')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.orgName"
                  :multiline="true"
                  :rows="2"
                  :disabled="true"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="status" :label="$t('单据状态')" label-style="left">
                <mt-select
                  css-class="e-outline"
                  :disabled="true"
                  v-model="formData.status"
                  :data-source="statusList"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :span="16">
              <mt-form-item
                prop="changeObjIds"
                :label="$t('变更对象')"
                label-style="left"
                style="margin-bottom: 40px"
              >
                <div class="checkbox-wrap" v-for="item in changeObjList" :key="item.id">
                  <mt-checkbox
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item)"
                    :disabled="pageType === 'detail'"
                  ></mt-checkbox>
                </div>
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="headUserName" :label="$t('供方负责人')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.headUserName"
                  :placeholder="$t('请输入供方负责人')"
                  :disabled="pageType === 'detail'"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <mt-form-item prop="headUserPhone" :label="$t('联系电话')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  v-model="formData.headUserPhone"
                  :placeholder="$t('请输入联系电话')"
                  :disabled="pageType === 'detail'"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="24">
              <mt-form-item prop="reason" :label="$t('变更原因')" label-style="left">
                <mt-input
                  css-class="e-outline"
                  ref="editorRef"
                  v-model="formData.reason"
                  :multiline="true"
                  :rows="4"
                  :disabled="pageType === 'detail'"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>
      <div class="textareaBox">
        <mt-row :gutter="24">
          <mt-col :span="12">
            <div class="b-info-title">{{ $t('现状描述') }}</div>
            <div class="articleBox">
              <rich-text-editor
                ref="editor"
                css-class="rich-box"
                :height="300"
                :max-length="20"
                v-model="formData.currentDesc"
                rich-text-id="d6f35f60-e7d7-4329-90b3-c08c8dd04522"
                :disabled="pageType === 'detail'"
              >
              </rich-text-editor>
            </div>
          </mt-col>
          <mt-col :span="12">
            <div class="b-info-title">{{ $t('预期的变更效果') }}</div>
            <div class="articleBox">
              <rich-text-editor
                ref="editor2"
                css-class="rich-box"
                :height="300"
                :max-length="20"
                v-model="formData.expectDesc"
                rich-text-id="951ce4c4-2e34-42c5-8bff-a7bc7ced0ac7"
                :disabled="pageType === 'detail'"
              >
              </rich-text-editor>
            </div>
          </mt-col>
        </mt-row>
      </div>
      <div class="fileBox" v-show="isShowFile">
        <div class="b-info-title">{{ $t('附件') }}</div>
        <mt-template-page
          ref="templateRef"
          height="300"
          :template-config="pageConfig"
          :use-tool-template="false"
          :padding-top="false"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
        >
        </mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import { enclosurea } from './config/detail.js'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor.vue'
export default {
  components: {
    RichTextEditor
  },
  data() {
    return {
      id: '', //变更单id
      changeObjList: [
        { value: 1, text: this.$t('人'), cssClass: 'col-', checked: false },
        { value: 2, text: this.$t('机械'), cssClass: 'col-', checked: false },
        { value: 3, text: this.$t('材料'), cssClass: 'col-', checked: false },
        { value: 4, text: this.$t('方法'), cssClass: 'col-', checked: false },
        { value: 5, text: this.$t('环境'), cssClass: 'col-', checked: false }
      ], // 变更对象数据源
      orgList: [
        { orgCode: 'BDComCode', orgName: this.$t('白电公司') },
        { orgCode: 'KTComCode', orgName: this.$t('空调公司') },
        { orgCode: 'TVComCode', orgName: this.$t('TV公司') }
      ], //客户组织数据源
      statusList: [
        { value: 0, text: this.$t('新建'), cssClass: 'col-' },
        { value: 1, text: this.$t('待品质确认'), cssClass: 'col-' },
        { value: 2, text: this.$t('已驳回'), cssClass: 'col-' },
        { value: 3, text: this.$t('审批中'), cssClass: 'col-' },
        { value: 4, text: this.$t('已完成'), cssClass: 'col-' }
      ],
      formData: {
        changeCode: '',
        supplierName: '', //供应商名称
        supplierCode: '', //供应商Code
        supplierId: '', //供应商Id
        customerOrgCode: '', //客户组织Code
        customerOrgName: '', //客户组织Name
        orgCode: '', //公司名称
        orgId: '', //公司id
        orgName: '', //公司name
        status: 0,
        changeObjIds: [], // 变更对象id
        changeObjNames: [], // 变更对象
        headUserName: '', //供方负责人
        headUserPhone: '', //联系电话
        reason: '', //变更原因
        currentDesc: '', //现状描述
        expectDesc: '', //预期的变更效果
        purchaseSuggest: '',
        devSuggest: '',
        qualitySuggest: ''
      },
      rules: {
        customerOrgCode: [{ required: true, message: this.$t('请选择客户组织'), trigger: 'blur' }],
        changeObjIds: [{ required: true, message: this.$t('请选择变更对象'), trigger: 'blur' }],
        headUserName: [{ required: true, message: this.$t('请输入供方负责人'), trigger: 'blur' }],
        headUserPhone: [{ required: true, message: this.$t('请输入供方联系电话'), trigger: 'blur' }]
        // purchaseSuggest: [
        //   { required: true, message: this.$t('请输入采方处理信息'), trigger: 'blur' }
        // ],
        // devSuggest: [
        //   { required: true, message: this.$t('请输入研发评估意见'), trigger: 'blur' }
        // ],
        // qualitySuggest: [
        //   { required: true, message: this.$t('请输入品质评估意见'), trigger: 'blur' }
        // ]
      },
      pageConfig: [
        {
          gridId: '0cd8a4c5-b457-4502-b4ed-5cdd22ffd64e',
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Newinvitation',
                  title: this.$t('新增'),
                  visibleCondition: () => this.pageType != 'detail'
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete1',
                  title: this.$t('删除'),
                  visibleCondition: () => this.pageType != 'detail'
                }
              ]
            ]
          },
          grid: {
            allowPaging: false, //不分页
            columnData: enclosurea,
            dataSource: []
            // asyncConfig: {
            //   url: '/supplier/tenant/common/file/list',
            //   rules: [
            //     //配置rules，数组格式，请求入参时，追加到rules中
            //     {
            //       label: '',
            //       field: 'bizId',
            //       type: 'number',
            //       operator: 'equal',
            //       value: '1565531962411302914'
            //     }
            //   ],
            //   params: { bizId: '1565531962411302914' }
            // }
          }
        }
      ],
      userInfo: JSON.parse(sessionStorage.getItem('userInfo')) || {},
      columnData: enclosurea
    }
  },
  computed: {
    title() {
      //根据页面类型，返回不同的标题
      let title = ''
      switch (this.$route?.query?.type) {
        case 'add':
          title = this.$t('新建4M1E变更单')
          break
        case 'edit':
          title = this.$t('编辑4M1E变更单')
          break
        case 'detail':
          title = this.$t('4M1E变更单详情')
          break
        default:
          title = this.$t('4M1E变更单详情')
          break
      }
      return title
    },
    //是否显示附件 当变更对象changeObjIds 选中材料 3  checked 时显示
    isShowFile() {
      return this.formData.changeObjIds.includes(3)
    },
    //页面类型
    pageType() {
      return this.$route?.query?.type || 'add'
    }
  },
  async mounted() {
    //获取 query
    this.id = this.$route?.query?.id
    await this.getDict('4M1E STATUS', 'statusList')
    await this.getDict('changeObjNames', 'changeObjList')
    if (this.pageType == 'edit' || this.pageType == 'detail') {
      //获取详情 数据回显
      await this.getDetail()
    }
    //新增时 设置供应商名称和供方负责人
    if (this.pageType == 'add') {
      this.getQueryBaseContactInfo() //联系人信息
      this.formData.supplierName = this.userInfo?.enterpriseName //供应商名称
      this.formData.supplierCode = this.userInfo?.enterpriseCode //供应商Code
      this.formData.supplierId = this.userInfo?.enterpriseId //供应商Id
    }
    //切换富文本编辑器的字数统计方式 字数  字节数
    this.$nextTick(() => {
      this.$refs.editor.toggleCountWords()
      this.$refs.editor2.toggleCountWords()
    })
  },
  methods: {
    //供应商档案-联系人信息
    getQueryBaseContactInfo() {
      let params = {
        contactPost: 'qualityLeader',
        supplierEnterpriseId: this.userInfo?.enterpriseId
      }
      this.$API.supplierChangeRequest.queryBaseContactInfo(params).then((res) => {
        if (res.code == 200 && res.data && res.data.length > 0) {
          this.formData.headUserName = res.data[0].contactName
          this.formData.headUserPhone = res.data[0].contactMobile
          this.formData.headUserId = res.data[0].contactId
        }
      })
    },
    //根据客户组织 获取客户公司
    getPartnerRelation(item) {
      let params = {
        dictCode: item.orgCode,
        supplierEnterpriseId: this.userInfo?.enterpriseId
      }
      this.$API.supplierChangeRequest.selectPartnerRelation(params).then((res) => {
        if (res.code == 200) {
          if (res.data && res.data.length > 0) {
            this.formData.orgCode = res.data.map((item) => item.orgCode).join(',')
            // this.formData.orgId = res.data.map((item) => item.orgId).join(',')
            this.formData.orgName = res.data.map((item) => item.orgName).join(',')
          } else {
            this.formData.orgCode = ''
            this.formData.orgId = ''
            this.formData.orgName = ''
          }
        }
      })
    },
    //详情
    async getDetail() {
      this.$API.supplierChangeRequest.detail({ id: this.id }).then((res) => {
        if (res.code == 200) {
          res.data.changeObjIds = res.data.changeObjIds.split(',').map((item) => {
            return Number(item)
          }) //转为数字数组
          this.formData = res.data || {}
          //回显多选框选中
          this.changeObjList.forEach((item) => {
            if (this.formData.changeObjIds.includes(Number(item.value))) {
              item.checked = true
            }
          })
          //回显附件
          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.fileList || [])
        }
      })
    },
    //获取当前页面需要的字典
    async getDict(code, key, type = 'number') {
      let res = await this.$API.supplierChangeRequest.queryDict({
        dictCode: code
      })
      let list = []
      if (res.code === 200) {
        res.data.map((item) => {
          list.push({
            value: type == 'number' ? Number(item.itemCode) : item.itemCode,
            text: item.itemName,
            cssClass: 'col-',
            checked: false
          })
        })
      }
      if (key && list.length > 0) {
        this[key] = list
      }
    },
    handleClickToolBar(e) {
      const { toolbar, data, gridRef } = e
      if (toolbar.id === 'Add') {
        this.addNew(data)
        return
      }
      if (toolbar.id === 'Delete') {
        let sltList = gridRef.getMtechGridRecords()
        if (sltList && sltList.length > 0) {
          this.deleteRecord(sltList)
        } else {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        }
      }
    },
    //新增附件
    addNew() {
      this.$dialog({
        modal: () => import('./components/operEnlosure.vue'),
        data: {
          title: this.$t('新增附件'),
          isEdit: false,
          fileInfo: {}
        },
        success: (data) => {
          let list = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords() || []
          list.push(data)
          //更新附件列表
          this.$set(this.pageConfig[0].grid, 'dataSource', list)
        }
      })
    },
    //删除附件
    deleteRecord(data) {
      const _this = this
      let ids = data.map((item) => item.id).join(',')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          _this.$toast({ content: this.$t('删除成功'), type: 'success' })
          let list = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords() || []
          list = list.filter((item) => !ids.includes(item.id))
          this.$set(this.pageConfig[0].grid, 'dataSource', list)
        }
      })
    },
    handleClickCellTool() {},
    //多选框change
    checkChange(e) {
      //更新多选选中状态
      this.changeObjList.forEach((item) => {
        if (item.value === e.value) {
          item.checked = !item.checked
        }
      })
      //联动更新formData.changeObjIds
      let changeObject = this.changeObjList.filter((item) => item.checked).map((item) => item.value)
      this.$set(this.formData, 'changeObjIds', changeObject)
    },
    //组织下拉
    handleFactoryChange(e) {
      // 工厂下拉变更
      const { itemData } = e
      if (itemData) {
        this.formData.customerOrgCode = itemData.orgCode
        this.formData.customerOrgName = itemData.orgName
        this.getPartnerRelation(itemData)
      } else {
        this.formData.customerOrgCode = ''
        this.formData.customerOrgName = ''
      }
    },
    //保存
    saveText(isSubmit = false) {
      //getText 获取 富文本的纯文本 去掉空格和换行 字数不能大于255
      let editor = this.$refs.editor.getText().replace(/\s+/g, '')
      let editor2 = this.$refs.editor2.getText().replace(/\s+/g, '')
      if (editor.length > 255) {
        this.$toast({ content: this.$t('现状描述不能超过255个字符'), type: 'warning' })
        return
      }
      if (editor2.length > 255) {
        this.$toast({ content: this.$t('预期的变更效果不能超过255个字符'), type: 'warning' })
        return
      }
      let params = { ...this.formData }
      params.currentDescContent = editor
      params.expectDescContent = editor2
      //提交前对数据进行处理
      params.changeObjIds = params.changeObjIds.join(',')
      //选中的变更对象的text
      params.changeObjNames = this.changeObjList
        .filter((item) => item.checked)
        .map((item) => item.text)
        .join(',')
      delete params.orgId //删除orgId
      //附件
      let list = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords() || []
      params.fileList = list
      this.$refs.formRef.validate().then((res) => {
        if (res) {
          this.$API.supplierChangeRequest.save(params).then((res) => {
            if (res.code == 200) {
              //数据回显
              this.id = res.data.id
              this.formData.id = res.data.id
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              if (isSubmit) {
                this.saveAndSubmit()
              }
            }
          })
        }
      })
    },
    //提交
    saveAndSubmit() {
      if (!this.formData.id) {
        return
      }
      let params = { id: this.formData.id }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交当前变更单吗？')
        },
        success: () => {
          this.$API.supplierChangeRequest.submit(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.onBack()
            }
          })
        }
      })
    },
    onBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .labelWidth .mt-form-item .label {
  width: 140px;
}
/deep/ .labelWidth .mt-input {
  flex: 1;
}
/deep/ .labelWidth2 .mt-form-item .label {
  width: 140px;
}
/deep/ .labelWidth2 {
  .mt-input,
  .mt-select {
    flex: 1;
  }
}
.boxMiddle .mt-col-6 {
  margin-top: 40px;
}
.btnBox {
  margin: 10px 0;
  .mt-button {
    margin-right: 10px;
  }
}
.articleBox {
  width: 100%;
}
.detallWrap {
  min-height: 100%;
  padding-top: 20px;
  background: #fff;
  padding-bottom: 10px;
}
.textareaBox {
  width: 100%;
}
.fileBox {
  width: 100%;
  margin: 20px 0;
  height: 400px;
}
.header-status {
  padding: 20px;
  background: rgba(99, 134, 193, 0.08);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .titles-box {
    width: 100%;
    flex-direction: column;
    justify-content: space-between;

    .mian-line {
      width: 100%;
      justify-content: space-between;
    }

    .mian-title {
      font-size: 20px;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .scroll-box {
      width: 100%;
      display: flex;
      white-space: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      margin-top: 20px;

      .normal-title-box {
        height: 88px;
        background: rgba(255, 255, 255, 1);
        border-radius: 8px;
        font-size: 14px;

        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        margin-right: 30px;
        padding: 0 20px;
      }

      .gong-title {
        padding: 20px 0 0 0;
        font-size: 14px;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
      }

      .gong-id {
        padding: 20px 0 0 0;
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 70, 156, 1);
      }
    }

    .sub-title {
      width: 100%;
      font-size: 12px;
      color: rgba(41, 41, 41, 1);
      margin-top: 10px;
      .normal-title {
        font-size: 12px;
        color: rgba(41, 41, 41, 1);
        margin-right: 30px;
      }
      .normal-title-dan {
        font-size: 14px;
        color: rgba(41, 41, 41, 1);
        margin-right: 30px;
        font-weight: 600;

        span {
          font-weight: 600;
        }

        .b-color {
          color: #00469c;
        }
      }
    }

    .mr-20 {
      margin-top: 20px;
    }
  }

  .btns-box {
    align-items: center;
    font-size: 14px;
    max-width: 300px;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
    justify-content: flex-end;

    .invite-btn {
      margin-right: 20px;
      cursor: pointer;
      font-weight: 600;
    }

    .invite-btn:last-child {
      margin-right: 0;
    }
  }
}
.purInfo {
  width: 100%;
}
.purBox {
  width: 100%;
  .mt-input {
    width: 100%;
  }
}
.contentWrap {
  border: 1px solid rgba(232, 232, 232, 1);
  margin-top: 10px;
  height: auto;
  // height: calc(100vh - 200px);
  padding: 20px;
  // overflow: scroll;
  margin-bottom: 10px;
}
.b-info-title {
  position: relative;
  width: 100%;
  height: 30px;
  line-height: 16px;
  font-size: 16px;
  color: rgba(41, 41, 41, 1);
  padding-left: 14px;
  margin-top: 10px;
}
.b-info-title:before {
  content: ' ';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #00469c;
  position: absolute;
  left: 0;
  top: 0;
}
.formWrap {
  width: 100%;
}
/deep/ .formWrap .mt-input {
  width: 100%;
  // .e-input-group {
  //   border: 1px solid rgba(0, 0, 0, 0.42);
  //   padding-left: 5px;
  // }
}

.checkbox-wrap {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 2px;
  margin-right: 20px;
  display: flex;
  align-items: center;
}
// /deep/ .e-input-group textarea.e-input,
// /deep/ .e-input-group.e-control-wrapper textarea.e-input {
//   border-color: rgba(0, 0, 0, 0.42);
//   border-width: 1px;
// }
// /deep/ .e-multi-line-input::before {
//   display: none;
// }
// /deep/ .e-input-group.e-control-wrapper textarea.e-input::after {
//   border-color: rgba(0, 0, 0, 0.42);
//   border-width: 1px;
// }

// /deep/ .e-input-group.e-control-wrapper textarea.e-input:hover {
//   border: 1px solid;
//   border-width: 1px;
// }
// /deep/ .e-input-group.e-control-wrapper textarea.e-input:focus {
//   border: 1px solid #00469c !important;
//   border-width: 1px !important;
// }
</style>
