<template>
  <div class="changeRequestBox">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { columnData } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: '942c68b5-5de2-452e-90b9-abf6a4d0876b',
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_table_new', title: this.$t('新增') },
                { id: 'edit', icon: 'icon_table_edit', title: this.$t('编辑') },
                { id: 'submit', icon: 'icon_solid_submit', title: this.$t('提交') },
                { id: 'delete', icon: 'icon_table_delete', title: this.$t('删除') }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData(),
            asyncConfig: {
              url: '/supplier/tenant/supplier/change/4m/pageList',
              params: {},
              serializeList: (list) => {
                // list.map((item) => {
                //   item.changeObjIds = '1,2'
                // })
                // //转成数字数组 配合searchOptions的multi-select使用
                // list.map((item) => {
                //   item.changeObjIds = item.changeObjIds.split(',').map((item) => {
                //     return Number(item)
                //   })
                // })
                return list
              }
            }
          }
        }
      ],
      changeObjNamesList: [
        { value: 1, text: this.$t('人'), cssClass: 'col-', checked: false },
        { value: 2, text: this.$t('机械'), cssClass: 'col-', checked: false },
        { value: 3, text: this.$t('材料'), cssClass: 'col-', checked: false },
        { value: 4, text: this.$t('方法'), cssClass: 'col-', checked: false },
        { value: 5, text: this.$t('环境'), cssClass: 'col-', checked: false }
      ], // 变更对象数据源
      statusList: [
        { value: 0, text: this.$t('新建'), cssClass: 'col-' },
        { value: 1, text: this.$t('待品质确认'), cssClass: 'col-' },
        { value: 2, text: this.$t('已驳回'), cssClass: 'col-' },
        { value: 3, text: this.$t('审批中'), cssClass: 'col-' },
        { value: 4, text: this.$t('已完成'), cssClass: 'col-' }
      ]
    }
  },
  async mounted() {
    let statusList = await this.getDict('4M1E STATUS', 'number', 'statusList')
    let changeObjNamesList = await this.getDict('changeObjNames', 'number', 'changeObjNamesList')
    this.pageConfig[0].grid.columnData = columnData(changeObjNamesList, statusList)
  },
  methods: {
    //快捷查询前对参数进行处理
    // handleQuickSearch(data) {
    //   const { rules } = data
    //   //将data.rules里的changeObjIds转为字符串 逗号分隔
    //   rules.rules.forEach((item) => {
    //     //
    //     if (item.field === 'changeObjIds') {
    //       console.log('changeObjIds', item.value)
    //       item.value = item.value.join(',')
    //     }
    //   })
    // },
    //获取当前页面需要的字典
    async getDict(code, type = 'string', key) {
      let res = await this.$API.supplierChangeRequest.queryDict({
        dictCode: code
      })
      let list = []
      if (res.code === 200) {
        res.data.map((item) => {
          list.push({
            value: type === 'string' ? item.itemCode : Number(item.itemCode),
            text: item.itemName,
            cssClass: 'col-'
          })
        })
      }
      if (list.length > 0) {
        return list
      } else {
        return this[key]
      }
    },
    //点击工具栏
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const sltList = gridRef.getMtechGridRecords()
      //编辑 只能选中一条数据
      if (toolbar.id === 'edit' && sltList.length !== 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //删除和提交 至少选中一条数据
      if ((toolbar.id === 'delete' || toolbar.id === 'submit') && sltList.length !== 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (toolbar.id === 'add') {
        this.handleAdd()
      }
      //编辑
      if (toolbar.id === 'edit') {
        this.handleEdit(sltList)
      }
      //提交
      if (toolbar.id === 'submit') {
        this.handleSubmit(sltList)
      }
      //删除
      if (toolbar.id === 'delete') {
        this.handleDelete(sltList)
      }
    },
    //新增
    handleAdd() {
      this.$router.push({
        path: '/supplier/sup/change-request-detail',
        query: {
          type: 'add',
          time: new Date().getTime() //时间戳 防止缓存
        }
      })
    },
    //编辑
    handleEdit(sltList) {
      //状态为新建status=0或已驳回status=2时 才能进入编辑页 否则提示 只有新建或已驳回状态的单据可以编辑！
      if (sltList[0].status != 0 && sltList[0].status != 2) {
        this.$toast({
          content: this.$t('只有新建或已驳回状态的单据可以编辑！'),
          type: 'warning'
        })
        return
      }

      this.$router.push({
        path: '/supplier/sup/change-request-detail',
        query: {
          id: sltList[0].id,
          type: 'edit',
          time: new Date().getTime() //时间戳 防止缓存
        }
      })
    },
    handleSubmit(sltList) {
      //状态为新建/已驳回时 才能提交 否则提示 只有新建或已驳回状态的单据可以提交！
      if (sltList[0].status != 0 && sltList[0].status != 2) {
        this.$toast({
          content: this.$t('只有新建或已驳回状态的单据可以提交！'),
          type: 'warning'
        })
        return
      }
      // if (sltList.some((item) => item.applyStatus !== 10 && item.applyStatus !== 30)) {
      // }
      let ids = sltList.map((item) => item.id).join(',')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交单据？')
        },
        success: () => {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
          this.$API.supplierChangeRequest.submit({ id: ids }).then(() => {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除
    handleDelete(sltList) {
      //状态为新建时 才能进入删除 否则提示 只有新建状态的单据可以删除！
      if (sltList[0].status != 0) {
        this.$toast({
          content: this.$t('只有新建状态的单据可以删除！'),
          type: 'warning'
        })
        return
      }
      let ids = sltList.map((item) => item.id).join(',')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$toast({
            content: this.$t('删除成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
          this.$API.supplierChangeRequest.remove({ id: ids }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //点击单元格
    handleClickCellTitle(e) {
      const { data } = e
      let type = 'detail'
      //单据状态为 0：新建 || 2：驳回 ，点击单元格跳转到详情页可编辑
      if (data.status == 0 || data.status == 2) {
        type = 'edit'
      }
      this.$router.push({
        path: '/supplier/sup/change-request-detail',
        query: {
          id: data.id,
          type: type,
          time: new Date().getTime() //时间戳 防止缓存
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.changeRequestBox {
  width: 100%;
  height: 100%;
}
</style>
