import Vue from 'vue'
import { i18n } from '@/main.js'
// import utils from '@/utils/utils'
//列表配置
// export const columnData = [
export const columnData = function (changeObjList = [], statusList = []) {
  console.log(changeObjList)
  let columns = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '150',
      field: 'changeCode',
      headerText: i18n.t('4M变更单号'),
      headerTextAlign: 'center',
      cellTools: []
    },
    {
      width: '150',
      field: 'changeObjNames',
      // field: 'changeObjIds',
      headerText: i18n.t('变更对象'),
      headerTextAlign: 'center'
      // searchOptions: {
      //   elementType: 'multi-select',
      //   operator: 'notin',
      //   dataSource: changeObjNames
      // },
      // valueAccessor: (field, data) => {
      //   return data.changeObjNames
      // }
    },
    {
      width: '150',
      field: 'orgName',
      headerText: i18n.t('客户名称'),
      headerTextAlign: 'center'
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      headerTextAlign: 'center'
      // template: () => {
      //   return {
      //     template: Vue.component('codeTemplate', {
      //       template: `<div class="code-wrap">
      //           {{supplierEnterpriseName}}
      //         </div>`,
      //       data: function () {
      //         return {
      //           data: {}
      //         }
      //       },
      //       computed: {
      //         supplierEnterpriseName() {
      //           return this.data.relationDTOList[0]?.supplierEnterpriseName
      //         }
      //       }
      //     })
      //   }
      // }
    },
    {
      width: '110',
      field: 'status',
      headerText: i18n.t('单据状态'),
      valueConverter: {
        type: 'map',
        map: statusList
      }
    },
    {
      width: '150',
      field: 'headUserName',
      headerText: i18n.t('供方负责人'),
      headerTextAlign: 'center'
    },
    {
      width: '150',
      field: 'headUserPhone',
      headerText: i18n.t('联系电话'),
      headerTextAlign: 'center'
    },
    {
      width: '150',
      field: 'reason',
      headerText: i18n.t('变更原因'),
      headerTextAlign: 'center'
    },
    {
      width: '150',
      field: 'createDate',
      headerText: i18n.t('创建日期'),
      headerTextAlign: 'center',
      searchOptions: {
        type: 'date',
        dateFormat: 'YYYY-mm-dd'
      },
      template: () => {
        return {
          template: Vue.component('time-tmp', {
            template: `
                  <div class="time-box">
                    {{ data.createDate }}
                  </div>`
          })
        }
      }
    }
  ]
  return columns
}

// 附件
export const enclosurea = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    headerText: i18n.t('附件名称'),
    headerTextAlign: 'center',
    width: '180',
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div>
            <a @click="preview()">{{data.fileName}}</a>
            <span style="margin-left:10px;cursor: pointer;" @click="upload">{{ $t('下载') }}</span>
          </div>`,
          data() {
            return { data: {}, sceneList: [] }
          },
          methods: {
            preview() {
              let params = {
                id: this.data.id,
                useType: 1
              }
              this.$API.SupplierPunishment.filepreview(params).then((res) => {
                window.open(res.data)
              })
            },
            upload() {
              this.$API.SupplierPunishment.fileDownload(this.data.fileId).then((res) => {
                let link = document.createElement('a')
                link.style.display = 'none'
                let blob = new Blob([res.data], { type: 'application/x-msdownload' })
                let url = window.URL.createObjectURL(blob)
                link.href = url
                link.setAttribute('download', `${this.data.fileName}`) // 给下载后的文件命名
                link.click() // 点击下载
                window.URL.revokeObjectURL(url)
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'fileSize',
    width: '120',
    headerText: i18n.t('附件大小'),
    headerTextAlign: 'center'
  },
  {
    field: 'updateUserName',
    width: '120',
    headerText: i18n.t('上传人'),
    headerTextAlign: 'center',
    template: () => {
      return {
        template: Vue.component('codeTemplate', {
          template: `<div class="code-wrap">
              {{ createDate }}
            </div>`,
          data: function () {
            return {
              data: {}
            }
          },
          computed: {
            createDate() {
              return this.data.updateUserName ? this.data.updateUserName : this.data.createUserName
            }
          }
        })
      }
    }
  },
  {
    field: 'modifyDate',
    width: '120',
    headerText: i18n.t('上传时间'),
    headerTextAlign: 'center',
    queryType: 'date',
    format: 'yyyy-MM-dd <br/> hh:mm:ss'
  },
  {
    field: 'remark',
    width: '180',
    headerTextAlign: 'center',
    headerText: i18n.t('备注')
  }
]
