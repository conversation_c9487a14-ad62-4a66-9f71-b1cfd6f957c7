<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="confirm"
  >
    <div class="dialog-content">
      <ScTable
        ref="sctableRef"
        :columns="columns"
        :table-data="tableData"
        :sort-config="sortConfig"
      />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import { stepTypeList } from './config/index'

export default {
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      tableData: []
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    sortConfig() {
      return {
        defaultSort: {
          field: 'stageAmount',
          order: 'asc'
        },
        showIcon: false
      }
    },
    columns() {
      const dedaultColumns = [
        {
          field: 'rebateLadderType',
          title: this.$t('阶梯类型'),
          slots: {
            default: ({ row }) => {
              const selectedItem = stepTypeList.find((item) => item.value === row.rebateLadderType)
              const rebateLadderTypeName = selectedItem?.text || null
              return [<span>{rebateLadderTypeName}</span>]
            }
          }
        },
        {
          field: 'stageAmount',
          title: this.$t('截止数值'),
          sortable: true
        },
        {
          field: 'rebateRatio',
          title: this.$t('返利比例（%）')
        },
        {
          field: 'rebateAmount',
          title: this.$t('返利金额（元）')
        }
      ]

      if (this.modalData.type === 7) {
        const col = {
          field: 'rebatePriceDiff',
          title: this.$t('返利差价')
        }
        dedaultColumns.push(col)
      }

      return dedaultColumns
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show() //显示弹窗
    this.tableData = this.modalData.list
  },
  methods: {
    confirm() {
      this.$emit('confirm-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
