<template>
  <!-- 明细table -->
  <div class="detail-table">
    <ScTable
      ref="sctableRef"
      grid-id="a6ecd79e-7f55-44bd-a88b-99e9b286cce1"
      :columns="columns"
      :table-data="tableData"
      show-footer
      :footer-method="footerMethod"
    >
    </ScTable>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'

export default {
  components: {
    ScTable
  },
  mixins: [mixin],
  data() {
    return {
      type: 'detail'
    }
  },
  computed: {
    dataList() {
      const dataList = []
      this.tableData.forEach((item, index) => {
        if (item.isAdd) {
          item.id = null
        } else if (this.tableRef.isUpdateByRow(item)) {
          item.optType = 'modify'
        }
        dataList.push({
          ...item,
          lineNumber: index + 1,
          actualStartDate: new Date(item.actualStartDate).getTime()
        })
      })
      return dataList
    }
  },
  mounted() {},
  methods: {
    footerMethod({ columns, data }) {
      const sums = []
      columns.forEach((column) => {
        let sumCell = null
        switch (column.property) {
          case 'settleQty':
          case 'settleAmt':
          case 'amtUntaxed':
          case 'amtTaxed':
            sumCell = `${this.$t('总计')}：${this.sumNum(data, column.property)}`
            break
        }
        sums.push(sumCell)
      })
      // 返回一个二维数组的表尾合计
      return [sums]
    },
    // 求和
    sumNum(list, field) {
      let count = 0
      list.forEach((item) => {
        count += Number(item[field])
      })
      // return count
      return this.formatAmount(count, 5)
    },
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'ladderInfo':
          this.$dialog({
            modal: () => import('./stepRankDiaolog.vue'),
            data: {
              title: this.$t('阶梯返利等级查看'),
              id: row.id,
              type: row.rebateType,
              list: row.ladderInfo ? JSON.parse(row.ladderInfo) : []
            },
            success: (list) => {
              row.ladderInfo = JSON.stringify(list)
              const index = this.tableData.findIndex((r) => r.id === row.id)
              this.$set(this.tableData, index, row)
            }
          })
          break
        default:
          break
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
