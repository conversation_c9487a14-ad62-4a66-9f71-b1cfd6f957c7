

## 供方保存返利协议红字开票数据


**接口地址**:`/tenant/rebateHeader/supplierSaveRedInvoice`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "attachmentList": [
    {
      "attachmentId": 0,
      "attachmentName": "",
      "attachmentSize": 0,
      "attachmentUrl": "",
      "claimId": 0,
      "id": 0,
      "remark": "",
      "tenantId": 0,
      "uploadTime": 0,
      "uploadUserId": 0,
      "uploadUserName": ""
    }
  ],
  "id": 0,
  "redInvoiceList": [
    {
      "buyerName": "",
      "buyerTaxNumber": "",
      "createTime": 0,
      "createUserId": 0,
      "createUserName": "",
      "headerId": 0,
      "id": 0,
      "limitAmount": 0,
      "productName": "",
      "productTaxItem": "",
      "remark": "",
      "sellerName": "",
      "sellerTaxNumber": "",
      "updateTime": 0,
      "updateUserId": 0,
      "updateUserName": ""
    }
  ],
  "supplierFeedbackRemark": "",
  "supplierHandleRemark": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|request|request|body|true|返利协议单据请求对象|返利协议单据请求对象|
|&emsp;&emsp;attachmentList|附件||false|array|考核单附件通用返回|
|&emsp;&emsp;&emsp;&emsp;attachmentId|附件文件id||false|integer||
|&emsp;&emsp;&emsp;&emsp;attachmentName|附件文件名称||false|string||
|&emsp;&emsp;&emsp;&emsp;attachmentSize|附件文件大小||false|number||
|&emsp;&emsp;&emsp;&emsp;attachmentUrl|附件文件路径||false|string||
|&emsp;&emsp;&emsp;&emsp;claimId|考核单id||false|integer||
|&emsp;&emsp;&emsp;&emsp;id|主键||false|integer||
|&emsp;&emsp;&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;&emsp;&emsp;tenantId|租户ID||false|integer||
|&emsp;&emsp;&emsp;&emsp;uploadTime|上传时间||false|integer||
|&emsp;&emsp;&emsp;&emsp;uploadUserId|上传用户id||false|integer||
|&emsp;&emsp;&emsp;&emsp;uploadUserName|上传用户名称||false|string||
|&emsp;&emsp;id|id传||false|integer(int64)||
|&emsp;&emsp;redInvoiceList|红字开票明细||false|array|RebateRedInvoiceResponse|
|&emsp;&emsp;&emsp;&emsp;buyerName|购买方名称||false|string||
|&emsp;&emsp;&emsp;&emsp;buyerTaxNumber|购买方税号||false|string||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间||false|integer||
|&emsp;&emsp;&emsp;&emsp;createUserId|创建用户ID||false|integer||
|&emsp;&emsp;&emsp;&emsp;createUserName|创建用户名称||false|string||
|&emsp;&emsp;&emsp;&emsp;headerId|返利协议单ID||false|integer||
|&emsp;&emsp;&emsp;&emsp;id|ID||false|integer||
|&emsp;&emsp;&emsp;&emsp;limitAmount|开票含税限额||false|number||
|&emsp;&emsp;&emsp;&emsp;productName|商品名称||false|string||
|&emsp;&emsp;&emsp;&emsp;productTaxItem|商品税目||false|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;&emsp;&emsp;sellerName|销售方名称||false|string||
|&emsp;&emsp;&emsp;&emsp;sellerTaxNumber|销售方税号||false|string||
|&emsp;&emsp;&emsp;&emsp;updateTime|更新时间||false|integer||
|&emsp;&emsp;&emsp;&emsp;updateUserId|更新用户ID||false|integer||
|&emsp;&emsp;&emsp;&emsp;updateUserName|更新用户名称||false|string||
|&emsp;&emsp;supplierFeedbackRemark|供应商反馈意见||false|string||
|&emsp;&emsp;supplierHandleRemark|供应商处理意见||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|通用返回统一封装对象«string»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|响应编码|integer(int32)|integer(int32)|
|data|数据|string||
|errorStackTrace|错误堆栈信息|string||
|msg|提示信息|string||
|success|是否成功|boolean||
|traceId|追踪ID|string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": "",
	"errorStackTrace": "",
	"msg": "",
	"success": true,
	"traceId": ""
}
```