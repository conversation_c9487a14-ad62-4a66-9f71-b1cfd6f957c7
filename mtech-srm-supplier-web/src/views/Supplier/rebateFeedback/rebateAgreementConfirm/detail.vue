<template>
  <div class="full-height">
    <!-- 头部表单 -->
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-btns">
          <vxe-button
            v-for="item in toolbar"
            v-show="!item.isHidden"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :disabled="item.disabled"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="rebateCode" :label="$t('返利协议单号')" label-style="top">
              <mt-input v-model="dataForm.rebateCode" disabled />
            </mt-form-item>
            <mt-form-item prop="rebateName" :label="$t('返利协议名称')" label-style="top">
              <mt-input v-model="dataForm.rebateName" type="text" maxlength="50" disabled />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <mt-input v-model="dataForm.company" disabled />
            </mt-form-item>
            <mt-form-item prop="agreementTemplateCode" :label="$t('协议书模板')" label-style="top">
              <mt-select
                v-model="dataForm.agreementTemplateCode"
                :data-source="agreementTemplateList"
                :fields="{ text: 'text', value: 'agreementCode' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="extendCompanyCodes"
              :label="$t('拓展公司')"
              label-style="top"
            >
              <mt-input v-model="dataForm.extendCompanyNames" disabled />
            </mt-form-item>
            <mt-form-item prop="factory" :label="$t('工厂')" label-style="top">
              <mt-input v-model="dataForm.factory" disabled />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <mt-input v-model="dataForm.supplier" disabled />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="agreementType"
              :label="$t('协议类型')"
              label-style="top"
            >
              <mt-select
                v-model="dataForm.agreementType"
                :data-source="agreementTypeList"
                disabled
              />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="specialRebateAmount"
              :label="$t('专案返利金额')"
              label-style="top"
            >
              <mt-input v-model="dataForm.specialRebateAmount" disabled />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="taxNumber"
              :label="$t('购买方税号')"
              label-style="top"
            >
              <mt-input v-model="dataForm.taxNumber" disabled />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="rebatePaymentType"
              :label="$t('返利支付方式')"
              label-style="top"
            >
              <mt-select
                v-model="dataForm.rebatePaymentType"
                :data-source="rebatePaymentMethodList"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select v-model="dataForm.status" :data-source="statusList" disabled />
            </mt-form-item>
            <mt-form-item prop="currency" :label="$t('币种')" label-style="top">
              <mt-select
                v-model="dataForm.currency"
                :data-source="currencyList"
                disabled
                :fields="{ text: 'text', value: 'currencyCode' }"
              />
            </mt-form-item>
            <mt-form-item prop="costCurrency" :label="$t('本位币编码')" label-style="top">
              <mt-select
                v-model="dataForm.costCurrency"
                :data-source="currencyList"
                disabled
                filter-type="Contains"
                :fields="{ text: 'text', value: 'currencyCode' }"
              />
            </mt-form-item>
            <mt-form-item prop="startDate" :label="$t('返利起始日')" label-style="top">
              <mt-date-picker v-model="dataForm.startDate" disabled />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('返利结束日')" label-style="top">
              <mt-date-picker v-model="dataForm.endDate" disabled />
            </mt-form-item>
            <mt-form-item prop="feedbackDate" :label="$t('要求反馈日期')" label-style="top">
              <mt-date-picker v-model="dataForm.feedbackDate" disabled placeholder="" />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <mt-input
                v-model="dataForm.remark"
                disabled
                :multiline="true"
                :rows="1"
                type="text"
                maxlength="200"
              />
            </mt-form-item>
            <mt-form-item prop="supplierHandleRemark" :label="$t('处理意见')" label-style="top">
              <mt-select
                v-model="dataForm.supplierHandleRemark"
                :data-source="handleRemarkList"
                clearable
                :disabled="!editable"
                :placeholder="$t('请选择处理意见')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierFeedbackRemark" :label="$t('反馈意见')" label-style="top">
              <mt-input
                v-model="dataForm.supplierFeedbackRemark"
                clearable
                :disabled="!editable"
                :multiline="true"
                :rows="1"
                type="text"
                maxlength="500"
                :placeholder="$t('请输入反馈意见')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <!-- 内容部分table -->
    <div class="body-container">
      <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab" />
      <div v-if="currentTabIndex === 0" class="table-item">
        <detail-table ref="detailRef" :detail-info="dataForm" :list="detailList" />
      </div>
      <div v-if="currentTabIndex === 1">
        <!-- 采方附件 -->
        <div class="table-item">
          <div class="item-top">
            <div class="item-title">
              <div class="title-prefix" />
              {{ $t('附件上传') }}
            </div>
            <div
              class="item-icon"
              :class="[!purIsExpand && 'item-icon-hidden']"
              @click="purIsExpand = !purIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <purchase-attachment
            v-show="purIsExpand"
            ref="purAttachmentRef"
            :list="purAttachmentList"
          />
        </div>
        <!-- 供方附件 -->
        <div class="table-item">
          <div class="item-top">
            <div class="item-title">
              <div class="title-prefix" />
              {{ $t('供方附件') }}
            </div>
            <div
              class="item-icon"
              :class="[!supIsExpand && 'item-icon-hidden']"
              @click="supIsExpand = !supIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <supplier-attachment
            v-show="supIsExpand"
            ref="supAttachmentRef"
            :detail-info="dataForm"
            :list="supAttachmentList"
          />
        </div>
      </div>
      <div v-if="showKtField && currentTabIndex === 2">
        <!-- 红字开票 -->
        <red-invoice-table
          ref="redInvoiceRef"
          :detail-info="dataForm"
          :editable="redInvoiceEditable"
          :red-invoice-list="dataForm.redInvoiceList"
          @data-change="handleRedInvoiceDataChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { statusList, handleRemarkList } from './config/index'
import DetailTable from './components/detailTable.vue'
import PurchaseAttachment from './components/purchaseAttachment.vue'
import SupplierAttachment from './components/supplierAttachment.vue'
import RedInvoiceTable from './components/redInvoiceTable.vue'

export default {
  components: { DetailTable, PurchaseAttachment, SupplierAttachment, RedInvoiceTable },
  data() {
    return {
      type: 'detail',
      isExpand: true,
      dataForm: {
        factoryCodeList: [],
        status: 1
      },
      statusList,
      handleRemarkList,
      agreementTemplateList: [],
      currencyList: [],
      showKtField: false, // 是否显示KT事业部字段
      agreementTypeList: [
        {
          value: 1,
          text: this.$t('专案返利')
        },
        {
          value: 2,
          text: this.$t('框架返利')
        }
      ],
      // 返利支付方式选项
      rebatePaymentMethodList: [
        {
          value: 1,
          text: this.$t('红票')
        },
        {
          value: 2,
          text: this.$t('低开')
        },
        {
          value: 3,
          text: this.$t('高开')
        }
      ],
      purIsExpand: true,
      supIsExpand: true,
      detailList: [], //明细列表
      purAttachmentList: [], //采方附件
      supAttachmentList: [], // 供方附件
      currentRedInvoiceData: [], // 当前红字开票数据
      isInit: true,
      currentTabIndex: 0
    }
  },
  computed: {
    editable() {
      return this.dataForm.status === 2
    },
    // 红字开票表格是否可编辑 - 当状态为待供应商维护红字发票时
    redInvoiceEditable() {
      return this.dataForm.status === 20
    },
    // 动态生成标签页列表，根据是否为空调事业部显示红字开票tab
    tabList() {
      const tabs = [
        {
          title: this.$t('明细')
        },
        {
          title: this.$t('附件')
        }
      ]

      // 只有空调事业部才显示红字开票tab
      if (this.showKtField) {
        tabs.push({
          title: this.$t('红字开票')
        })
      }

      return tabs
    },
    toolbar() {
      const { id } = this.$route.query
      const toolbar = [
        {
          code: 'print',
          name: this.$t('协议打印'),
          isHidden: !id
        },
        {
          code: 'save',
          name: this.$t('保存'),
          isHidden: !this.editable
        },
        {
          code: 'submit',
          name: this.$t('保存并提交'),
          isHidden: !this.editable
        },
        {
          code: 'saveRedInvoice',
          name: this.$t('保存红字开票'),
          isHidden: this.dataForm.status !== 20 // 状态为待供应商维护红字发票时显示
        },
        {
          code: 'submitRedInvoice',
          name: this.$t('提交红字开票'),
          isHidden: this.dataForm.status !== 20 // 状态为待供应商维护红字发票时显示
        },
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        }
      ]
      return toolbar
    },
    formRules() {
      return {
        rebateCode: [
          {
            required: this.$route.query.type === 'edit',
            message: this.$t('请输入定价单名称'),
            trigger: 'blur'
          }
        ],
        rebateName: [{ required: true, message: this.$t('请输入返利协议名称'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        agreementTemplateCode: [
          { required: true, message: this.$t('请选择协议书模板'), trigger: 'blur' }
        ],
        factory: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        currency: [{ required: true, message: this.$t('请选择币种'), trigger: 'blur' }],
        costCurrency: [{ required: true, message: this.$t('请选择本位币编码'), trigger: 'blur' }],
        startDate: [{ required: true, message: this.$t('请选择返利起始日'), trigger: 'blur' }],
        endDate: [{ required: true, message: this.$t('请选择返利结束日'), trigger: 'blur' }],
        supplierHandleRemark: [
          { required: true, message: this.$t('请选择处理意见'), trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    // 监听showKtField变化，如果当前在红字开票tab且showKtField变为false，则切换到明细tab
    showKtField(newVal) {
      if (!newVal && this.currentTabIndex === 2) {
        this.currentTabIndex = 0
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getCurrencyList()
      this.getDetailInfo()
    },
    // 获取明细信息
    async getDetailInfo() {
      this.isInit = true
      const res = await this.$API.rebateFeedback.getRacDetailById({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        const { header, itemList, purchaseFileList, supplierFileList, redInvoiceList } = res.data
        const {
          companyCode,
          companyName,
          siteInfo,
          supplierCode,
          supplierName,
          startDate,
          endDate,
          feedbackDate
        } = header
        const tempfactoryList = []
        const factoryList = siteInfo ? JSON.parse(siteInfo) : []
        factoryList.forEach((item) => tempfactoryList.push(item.code + '-' + item.name))
        this.dataForm = {
          ...header,
          company: companyCode + '-' + companyName,
          factory: tempfactoryList.join('; '),
          supplier: supplierCode + '-' + supplierName,
          startDate: startDate ? dayjs(Number(startDate)).format('YYYY-MM-DD') : null,
          endDate: endDate ? dayjs(Number(endDate)).format('YYYY-MM-DD') : null,
          feedbackDate: feedbackDate ? dayjs(Number(feedbackDate)).format('YYYY-MM-DD') : null,
          // 处理拓展公司显示
          extendCompanyNames: header.extendCompanyNames || '',
          // 红字开票数据
          redInvoiceList: redInvoiceList || []
        }
        this.detailList = itemList
        this.purAttachmentList = purchaseFileList
        this.supAttachmentList = supplierFileList
        // 初始化当前红字开票数据
        this.currentRedInvoiceData = redInvoiceList || []

        this.getAgreementTemplateList(header.companyCode)
        this.checkIsKtCompany(header.companyCode)
      }
    },
    // 协议模板下拉列表
    async getAgreementTemplateList(companyCode) {
      const params = {
        status: 1,
        companyCode
      }
      const res = await this.$API.rebateFeedback.queryAgreemenTempalteList(params)
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.agreementCode + '-' + item.agreementName
        })
        this.agreementTemplateList = res.data || []
      }
    },
    // 获取币种下拉列表
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.currencyCode + '-' + item.currencyName
        })
        this.currencyList = res.data
      }
    },
    // 检查是否为KT事业部
    async checkIsKtCompany(companyCode) {
      if (!companyCode) return
      try {
        const res = await this.$API.rebateFeedback.isKtCompany({ companyCode })
        if (res.code === 200) {
          this.showKtField = res.data === true
        }
      } catch (error) {
        console.warn('检查KT事业部失败:', error)
        this.showKtField = false
      }
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.$router.go(-1)
          break
        case 'print':
          this.handlePrint()
          break
        case 'save':
        case 'submit':
          this.handleOperate(e.code)
          break
        case 'saveRedInvoice':
          this.handleSaveRedInvoice()
          break
        case 'submitRedInvoice':
          this.handleSubmitRedInvoice()
          break
        default:
          break
      }
    },
    // 保存、提交
    handleOperate(type) {
      this.$refs.dataFormRef.validate(async (valid) => {
        if (valid) {
          const {
            id,
            isAgree,
            status,
            supplierCode,
            supplierFeedbackRemark,
            supplierHandleRemark
          } = this.dataForm
          const params = {
            id,
            isAgree,
            status,
            supplierCode,
            supplierFeedbackRemark,
            supplierHandleRemark,
            attachmentList:
              this.currentTabIndex === 1
                ? this.$refs.supAttachmentRef?.tableData
                : this.supAttachmentList
          }

          const res = await this.$API.rebateFeedback[type + 'RacDetail'](params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.getDetailInfo()
            // this.$router.replace({
            //   name: 'rebate-agreement-create-detail',
            //   query: {
            //     type: 'edit',
            //     id: res.data,
            //     refreshId: Date.now()
            //   }
            // })
          }
        }
      })
    },
    // 打印
    async handlePrint() {
      let buffer = await this.$API.rebateFeedback
        .printRacDetail({ id: this.$route.query.id })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    handleSelectTab(index) {
      index === 0 && (this.supAttachmentList = this.$refs.supAttachmentRef?.tableData)
      this.currentTabIndex = index

      // 切换到红字开票tab时，确保数据正确回显
      if (index === 2 && this.showKtField) {
        this.$nextTick(() => {
          // 使用新的刷新方法，保持新增行的编辑状态
          if (this.$refs.redInvoiceRef && this.$refs.redInvoiceRef.refreshWithNewRowState) {
            this.$refs.redInvoiceRef.refreshWithNewRowState(this.dataForm.redInvoiceList)
          }
        })
      }
    },
    // 处理红字开票数据变化
    handleRedInvoiceDataChange(data) {
      this.currentRedInvoiceData = data || []
    },
    // 保存红字开票数据
    async handleSaveRedInvoice() {
      try {
        // 使用当前红字开票数据，避免v-if不可见时无法获取$refs的问题
        const redInvoiceData = this.currentRedInvoiceData || []

        if (!redInvoiceData.length) {
          this.$toast({ content: this.$t('红字开票不能为空'), type: 'warning' })
          return
        }

        // 构建请求数据
        const requestData = {
          id: this.dataForm.id,
          redInvoiceList: redInvoiceData.map(item => ({
            buyerName: item.buyerName,
            buyerTaxNumber: item.buyerTaxNumber,
            headerId: this.dataForm.id,
            limitAmount: item.limitAmount,
            productName: item.productName,
            productTaxItem: item.productTaxItem,
            sellerName: item.sellerName,
            sellerTaxNumber: item.sellerTaxNumber,
            id: item?.id.includes('row_') ? null : item.id
          })),
          attachmentList: this.supAttachmentList || [],
          supplierFeedbackRemark: this.dataForm.supplierFeedbackRemark || '',
          supplierHandleRemark: this.dataForm.supplierHandleRemark || ''
        }

        const res = await this.$API.rebateFeedback.supplierSaveRedInvoice(requestData)
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          // 刷新页面数据
          this.getDetailInfo()
          return true // 返回保存成功标识
        } else {
          this.$toast({ content: res.msg || this.$t('保存失败'), type: 'error' })
          return false // 返回保存失败标识
        }
      } catch (error) {
        console.error('保存红字开票数据失败:', error)
        this.$toast({ content: error.msg || this.$t('保存失败'), type: 'error' })
        return false // 返回保存失败标识
      }
    },
    // 提交红字开票数据
    async handleSubmitRedInvoice() {
      try {
        // 先保存数据，保存成功才能提交
        const saveSuccess = await this.handleSaveRedInvoice()

        if (!saveSuccess) {
          this.$toast({ content: this.$t('保存失败，无法提交'), type: 'warning' })
          return
        }

        // 保存成功后再提交
        const res = await this.$API.rebateFeedback.supplierSubmitRedInvoice({ id: this.dataForm.id })
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          // 刷新页面数据
          this.getDetailInfo()
        } else {
          this.$toast({ content: res.msg || this.$t('提交失败'), type: 'error' })
        }
      } catch (error) {
        console.error('提交红字开票数据失败:', error)
        this.$toast({ content: error.msg || this.$t('提交失败'), type: 'error' })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  width: 100%;
  height: auto;
  padding: 8px;
  background: #fff;
  overflow: scroll;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-btns {
      text-align: right;
      margin-bottom: 10px;
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 8px 8px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 8px 8px 0 0;
    }
  }
}

.body-container {
  .table-item {
    width: 100%;
    margin-top: 10px;
    .item-top {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .item-title {
        display: flex;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-weight: 500;
        .title-prefix {
          width: 5px;
          height: 18px;
          background-color: #409eff;
          margin: 7px 15px 0 0;
        }
      }

      .item-icon {
        color: #409eff;
        transform: rotate(270deg);
      }
      .item-icon-hidden {
        transform: rotate(90deg);
        margin-right: 16px;
      }
    }
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
  }

  .j-select.ant-select .ant-select-selection {
    background-color: #f5f5f5;
    border-color: rgba(0, 0, 0, 0.42);
  }
}
</style>
