<template>
  <div class="red-invoice-demo">
    <div class="demo-header">
      <h2>红字开票表格演示</h2>
      <div class="demo-controls">
        <vxe-button @click="toggleEditable">
          {{ editable ? '禁用编辑' : '启用编辑' }}
        </vxe-button>
        <vxe-button @click="toggleKtField">
          {{ isKtCompany ? '切换为非空调事业部' : '切换为空调事业部' }}
        </vxe-button>
        <vxe-button @click="showData">查看当前数据</vxe-button>
      </div>
    </div>

    <div class="demo-content">
      <div class="demo-status">
        <p><strong>当前状态：</strong></p>
        <p>是否为空调事业部：{{ isKtCompany ? '是' : '否' }}</p>
        <p>是否可编辑：{{ editable ? '是' : '否' }}</p>
        <p>表格显示：{{ isKtCompany ? '显示红字开票表格' : '隐藏红字开票表格（非空调事业部）' }}</p>
      </div>

      <red-invoice-table
        v-if="isKtCompany"
        ref="redInvoiceRef"
        :detail-info="mockDetailInfo"
        :editable="editable"
        :red-invoice-list="mockDetailInfo.redInvoiceList"
        @data-change="handleDataChange"
      />

      <div v-else class="no-kt-message">
        <p>当前不是空调事业部，不显示红字开票功能</p>
      </div>
    </div>

    <!-- 数据展示弹窗 -->
    <mt-dialog
      v-if="showDataDialog"
      ref="dataDialog"
      width="80%"
      height="60%"
      :header="{ title: '当前表格数据' }"
      @beforeClose="showDataDialog = false"
    >
      <div class="data-display">
        <pre>{{ JSON.stringify(tableData, null, 2) }}</pre>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import RedInvoiceTable from './components/redInvoiceTable.vue'

export default {
  name: 'RedInvoiceDemo',
  components: { RedInvoiceTable },
  data() {
    return {
      editable: true,
      isKtCompany: true, // 是否为空调事业部
      showDataDialog: false,
      tableData: [],
      currentRedInvoiceData: [], // 当前红字开票数据
      mockDetailInfo: {
        id: 'demo-001',
        rebateCode: '*********',
        rebateName: '测试返利协议',
        status: 20, // 待供应商维护红字发票状态
        supplierName: '深圳市富豪装投资有限公司', // 供应商名称
        supplier: '深圳市富豪装投资有限公司',
        companyName: '王牌电器(滁州)有限公司', // 公司名称
        company: '王牌电器(滁州)有限公司',
        redInvoiceList: [
          {
            id: 1,
            sellerName: '深圳市富豪装投资有限公司',
            sellerTaxNumber: '91440300123456789X',
            buyerName: '王牌电器(滁州)有限公司',
            buyerTaxNumber: '91341100987654321A',
            productName: '空调配件',
            productTaxItem: '1090101010000000000',
            limitAmount: 10000.00
          },
          {
            id: 2,
            sellerName: '深圳市富豪装投资有限公司',
            sellerTaxNumber: '91440300123456789X',
            buyerName: '王牌电器(滁州)有限公司',
            buyerTaxNumber: '91341100987654321A',
            productName: '制冷设备',
            productTaxItem: '1090101020000000000',
            limitAmount: 15000.00
          }
        ]
      }
    }
  },

  methods: {
    toggleEditable() {
      this.editable = !this.editable
    },
    toggleKtField() {
      this.isKtCompany = !this.isKtCompany
    },
    showData() {
      // 使用当前数据，避免v-if不可见时无法获取$refs的问题
      this.tableData = this.currentRedInvoiceData || []
      this.showDataDialog = true
    },
    handleDataChange(data) {
      // 处理红字开票数据变化
      this.currentRedInvoiceData = data || []
      console.log('红字开票数据变化:', data)
    }
  }
}
</script>

<style scoped lang="scss">
.red-invoice-demo {
  padding: 20px;
  background: #fff;
  min-height: 100vh;

  .demo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e8e8e8;

    h2 {
      margin: 0;
      color: #333;
    }

    .demo-controls {
      display: flex;
      gap: 10px;
    }
  }

  .demo-content {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;

    .demo-status {
      background: #fff;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
      border-left: 4px solid #409eff;

      p {
        margin: 5px 0;
        font-size: 14px;

        &:first-child {
          font-weight: bold;
          margin-bottom: 10px;
        }
      }
    }

    .no-kt-message {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
      padding: 20px;
      border-radius: 4px;
      text-align: center;

      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }

  .data-display {
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;

    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      color: #333;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}
</style>
