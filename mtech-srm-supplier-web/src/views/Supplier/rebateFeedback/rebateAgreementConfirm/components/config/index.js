import { i18n } from '@/main.js'

// 返利类型列表
export const rebateTypeList = [
  { text: i18n.t('物料类别采购金额*返利比例'), value: 1 },
  { text: i18n.t('物料类别采购金额*阶梯返利（金额、比例)'), value: 2 },
  { text: i18n.t('物料类别采购数量*阶梯返利(金额、比例)'), value: 3 },
  { text: i18n.t('物编采购金额*返利比例'), value: 4 },
  { text: i18n.t('物编采购数量*单位数量返利金额'), value: 5 },
  { text: i18n.t('物编采购金额*阶梯返利(金额、比例)'), value: 6 },
  { text: i18n.t('物编采购数量*阶梯返利(金额、比例、差价）'), value: 7 },
  { text: i18n.t('其他-一次执行'), value: 8 },
  { text: i18n.t('其他-分频次执行'), value: 9 },
  { text: i18n.t('付现比例'), value: 10 }
]

// 返利计算频次列表
export const rebateCalculationFrequencyList = [
  { text: i18n.t('月度'), value: 1 },
  { text: i18n.t('双月'), value: 2 },
  { text: i18n.t('季度'), value: 3 },
  { text: i18n.t('半年'), value: 4 },
  { text: i18n.t('年度'), value: 5 },
  { text: i18n.t('一次返利'), value: 6 }
]

// 阶梯类型
export const stepTypeList = [
  { text: i18n.t('返利比例'), value: 1 },
  { text: i18n.t('返利金额'), value: 2 },
  { text: i18n.t('单片差价'), value: 3 }
]
