# 红字开票表格组件

## 概述

`redInvoiceTable.vue` 是一个基于 ScTable 组件开发的红字开票管理表格，用于供方返利协议确认页面的红字开票功能。

## 功能特性

- ✅ 表格数据展示
- ✅ 条件显示（仅空调事业部显示红字开票tab）
- ✅ 条件编辑（当状态为待供应商维护红字发票时可编辑）
- ✅ 新增行功能（自动填充供应商和公司信息）

- ✅ 删除行功能
- ✅ 行内编辑
- ✅ 数字类型输入（开票含税金额）
- ✅ 必填字段验证
- ✅ 响应式设计

## 使用方法

### 基本用法

```vue
<template>
  <!-- 红字开票tab只在空调事业部显示 -->
  <div v-if="showKtField && currentTabIndex === 2">
    <red-invoice-table
      ref="redInvoiceRef"
      :detail-info="dataForm"
      :editable="redInvoiceEditable"
      :red-invoice-list="dataForm.redInvoiceList"
    />
  </div>
</template>

<script>
import RedInvoiceTable from './components/redInvoiceTable.vue'

export default {
  components: { RedInvoiceTable },
  computed: {
    redInvoiceEditable() {
      // 当状态为待供应商维护红字发票时可编辑
      return this.dataForm.status === 20
    },
    // 动态生成标签页列表，根据是否为空调事业部显示红字开票tab
    tabList() {
      const tabs = [
        { title: this.$t('明细') },
        { title: this.$t('附件') }
      ]

      // 只有空调事业部才显示红字开票tab
      if (this.showKtField) {
        tabs.push({ title: this.$t('红字开票') })
      }

      return tabs
    }
  }
}
</script>
```

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| detailInfo | Object | {} | 详情信息对象，包含返利协议相关信息 |
| editable | Boolean | false | 是否可编辑，控制新增、删除、编辑功能 |
| redInvoiceList | Array | [] | 红字开票数据列表，从detail接口返回 |

### 表格字段

| 字段名 | 标题 | 类型 | 必填 | 可编辑 | 说明 |
|--------|------|------|------|------|------|
| sellerName | 销售方名称（全称） | String | ❌ | ✅ | 开票方的完整名称，新增时自动填充供应商名称 |
| sellerTaxNumber | 销售方税号 | String | ✅ | ✅ | 开票方的税务登记号 |
| buyerName | 购买方名称 | String | ❌ | ✅ | 购买方的名称，新增时自动填充公司名称 |
| buyerTaxNumber | 购买方税号 | String | ❌ | ❌ | 购买方的税务登记号，由后端自动赋值 |
| productName | 商品名称 | String | ✅ | ✅ | 商品或服务的名称 |
| productTaxItem | 商品税目 | String | ✅ | ✅ | 商品的税收分类编码 |
| limitAmount | 开票含税金额 | Number | ✅ | ✅ | 含税的开票金额，数字类型 |

### 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| initTableData | 初始化表格数据（使用props数据） | - |
| handleAdd | 新增一行数据（自动填充供应商和公司信息） | - |
| handleDelete | 删除选中的行 | - |
| refreshTableData | 强制刷新表格数据（用于切换tab时的数据回显） | - |
| refreshWithNewRowState | 刷新数据并保持新增行的编辑状态 | interfaceData: Array |
| loadInterfaceData | 加载接口返回的数据（直接赋值，非新增状态） | data: Array |
| insertNewRows | 插入新增数据（使用insert方法，有新增状态） | newRows: Array |

### 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| data-change | 表格数据变化时触发 | data: Array - 当前表格的visibleData |
| edit-closed | 编辑完成事件 | { row, column, $event } |

## 工具栏功能

当 `editable` 为 `true` 时，表格顶部会显示以下操作按钮：

- **新增**: 在表格顶部插入一行新数据并自动进入编辑状态
  - 自动填充销售方名称为单据供应商名称
  - 自动填充购买方名称为单据所属公司名称
  - 购买方税号由后端赋值，不可编辑
- **删除**: 删除选中的行（需要先勾选要删除的行，删除时不校验必填项）

## 编辑模式

- **触发方式**: 点击单元格
- **编辑模式**: 行编辑模式
- **验证**: 支持必填字段验证
- **状态显示**: 显示编辑状态标识

- **数字输入**: 开票含税金额字段支持数字类型输入



## 样式定制

组件使用 SCSS 编写样式，支持以下自定义：

```scss
.red-invoice-table {
  .table-item {
    // 表格容器样式
  }
  
  .item-top {
    // 标题栏样式
  }
  
  .title-prefix {
    // 标题前缀色块样式
  }
}
```

## 数据回显与传递

### 数据回显
红字开票数据通过props从父组件传入：

1. **数据来源**: 从detail接口返回的 `redInvoiceList` 字段
2. **数据传递**: 通过props `red-invoice-list` 传入组件
3. **数据监听**: 组件内部监听props变化，自动更新表格数据
4. **数据格式**: 数组格式，每个元素包含完整的红字开票信息

### 数据传递到父组件
为了解决v-if不可见时无法通过$refs获取数据的问题，组件采用事件方式传递数据：

1. **触发时机**: 新增、删除、编辑完成时自动触发
2. **事件名称**: `data-change`
3. **传递数据**: 当前表格的visibleData
4. **父组件处理**: 通过事件监听器接收并存储数据

```vue
<!-- 父组件使用 -->
<red-invoice-table
  :red-invoice-list="dataForm.redInvoiceList"
  @data-change="handleRedInvoiceDataChange"
/>

<!-- 父组件方法 -->
handleRedInvoiceDataChange(data) {
  this.currentRedInvoiceData = data || []
}
```

### 切换Tab数据回显与新增状态保持
基于vxe-table的数据加载机制，实现新增状态和非新增状态的区分：

#### 数据处理逻辑
1. **接口数据**：使用 `loadData()` 直接赋值，这些数据**没有新增状态**
2. **新增数据**：使用 `insert()` 方法插入，这些数据**有新增状态**

#### vxe-table数据状态区分
```javascript
// 接口数据 - 直接赋值（无新增状态）
this.tableRef.loadData(interfaceData)

// 新增数据 - insert插入（有新增状态）
this.tableRef.insert(newRows)

// 获取不同状态的数据
const tableData = this.tableRef.getTableData()
const savedData = tableData.tableData      // 已保存的数据
const insertData = tableData.insertRecords // 新增的数据
```

#### 实现方式
```vue
handleSelectTab(index) {
  if (index === 2 && this.showKtField) {
    this.$nextTick(() => {
      if (this.$refs.redInvoiceRef && this.$refs.redInvoiceRef.refreshWithNewRowState) {
        this.$refs.redInvoiceRef.refreshWithNewRowState(this.dataForm.redInvoiceList)
      }
    })
  }
}
```

#### 数据加载流程
1. 先加载接口数据（`loadInterfaceData`）- 无新增状态
2. 再插入新增数据（`insertNewRows`）- 有新增状态
3. 自动设置第一个新增行为编辑状态

## 显示逻辑

红字开票功能的显示遵循以下规则：

1. **事业部限制**: 只有空调事业部（KT事业部）才显示红字开票tab
2. **状态控制**: 当状态为"待供应商维护红字发票"（status === 20）时才可编辑
3. **动态切换**: 当事业部类型变化时，会自动隐藏/显示红字开票tab
4. **自动跳转**: 如果用户在红字开票tab时事业部变为非空调，会自动跳转到明细tab

## 注意事项

1. 组件依赖 ScTable 组件，确保项目中已正确安装和配置
2. 红字开票功能仅对空调事业部可见，其他事业部不显示此功能
3. 编辑功能受 `editable` 属性控制，建议根据业务状态动态设置
4. 必填字段验证在编辑完成时触发，请确保用户填写完整信息
5. 删除操作会弹出确认对话框，避免误删数据

## 演示页面

项目中包含了一个演示页面 `redInvoiceDemo.vue`，可以用来测试组件的各项功能：

- 切换编辑/只读模式
- 查看当前表格数据
- 测试新增、删除、编辑功能

## 保存和提交功能

detail.vue页面新增了红字开票的保存和提交按钮：

### 按钮显示逻辑
- **保存红字开票**: 只有状态不是"待供应商维护红字发票"(status !== 20)时显示
- **提交红字开票**: 只有状态不是"待供应商维护红字发票"(status !== 20)时显示

### 功能说明
- **保存**: 保存当前红字开票表格数据和附件信息，返回保存结果
- **提交**: 先保存数据，只有保存成功后才能提交给后端处理

## API 集成

组件集成了以下API接口：

```javascript
// 保存红字开票数据
const res = await this.$API.rebateFeedback.supplierSaveRedInvoice({
  id: this.dataForm.id,
  redInvoiceList: [...],
  attachmentList: [...],
  supplierFeedbackRemark: '',
  supplierHandleRemark: ''
})

// 提交红字开票数据
const res = await this.$API.rebateFeedback.supplierSubmitRedInvoice({
  id: this.dataForm.id
})
```
