<template>
  <!-- 明细table -->
  <div class="detail-table">
    <ScTable
      ref="sctableRef"
      grid-id="f21fc3ba-dfc8-4e12-9d74-4c1dfc9cb0e5"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'

export default {
  components: {
    ScTable
  },
  mixins: [mixin],
  data() {
    return {
      type: 'detail'
    }
  },
  computed: {
    dataList() {
      const dataList = []
      this.tableData.forEach((item, index) => {
        if (item.isAdd) {
          item.id = null
        } else if (this.tableRef.isUpdateByRow(item)) {
          item.optType = 'modify'
        }
        dataList.push({
          ...item,
          lineNumber: index + 1,
          actualStartDate: new Date(item.actualStartDate).getTime()
        })
      })
      return dataList
    }
  },
  mounted() {},
  methods: {
    // 点击单元格
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'stepRank':
          this.$dialog({
            modal: () => import('./stepRankDiaolog.vue'),
            data: {
              title: this.$t('阶梯返利等级设置'),
              id: row.id,
              type: row.rebateType,
              list: row.stepList
            },
            success: (list) => {
              row.ladderInfo = JSON.stringify(list)
              const index = this.tableData.findIndex((r) => r.id === row.id)
              this.$set(this.tableData, index, row)
            }
          })
          break
        default:
          break
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
