<template>
  <div class="red-invoice-table">
    <div class="table-item">
      <div>
        <sc-table
          ref="redInvoiceTableRef"
          :loading="loading"
          :columns="columns"
          :table-data="tableData"
          :edit-config="editConfig"
          :edit-rules="editRules"
          keep-source
          :sortable="false"
          :is-show-right-btn="true"
          :is-show-refresh-bth="false"
          @edit-actived="editActived"
          @edit-closed="editComplete"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              v-show="!item.isHidden"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'

export default {
  name: 'RedInvoiceTable',
  components: { ScTable },
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    editable: {
      type: Boolean,
      default: false
    },
    redInvoiceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        sellerTaxNumber: [{ required: true, message: this.$t('必填') }],
        productName: [{ required: true, message: this.$t('必填') }],
        productTaxItem: [{ required: true, message: this.$t('必填') }],
        limitAmount: [{ required: true, message: this.$t('必填') }]
      },
      isEditing: false // 是否正在编辑
    }
  },
  computed: {
    tableRef() {
      return this.$refs.redInvoiceTableRef?.$refs?.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'sellerName',
          title: this.$t('销售方名称'),
          minWidth: 200,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.sellerName}
                  transfer
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'sellerTaxNumber',
          title: this.$t('销售方税号'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.sellerTaxNumber}
                  placeholder={this.$t('请填写')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'buyerName',
          title: this.$t('购买方名称'),
          minWidth: 200,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.buyerName}
                  transfer
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'buyerTaxNumber',
          title: this.$t('购买方税号'),
          minWidth: 150
        },
        {
          field: 'productName',
          title: this.$t('商品名称'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.productName}
                  placeholder={this.$t('请填写')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'productTaxItem',
          title: this.$t('商品税目'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.productTaxItem}
                  placeholder={this.$t('请填写')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'limitAmount',
          title: this.$t('开票含税金额'),
          minWidth: 130,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.limitAmount}
                  type="number"
                  placeholder={this.$t('请填写金额')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      const btns = []
      if (this.editable) {
        btns.push(
          { code: 'add', name: this.$t('新增'), icon: '', status: 'info' },
          { code: 'delete', name: this.$t('删除'), icon: '', status: 'info' }
        )
      }
      return btns
    }
  },
  mounted() {
    this.initTableData()
  },
  watch: {
    // 监听redInvoiceList变化，更新表格数据
    redInvoiceList: {
      handler(newVal) {
        this.loadInterfaceData(newVal || [])
      },
      immediate: true,
      deep: true
    },
    // 监听表格数据变化，向父组件发送事件
    tableData: {
      handler(newVal) {
        this.$emit('data-change', newVal || [])
      },
      deep: true
    }
  },
  methods: {
    // 初始化表格数据
    initTableData() {
      // 使用props传入的数据初始化表格
      this.loadInterfaceData(this.redInvoiceList || [])
    },
    // 加载接口返回的数据
    loadInterfaceData(data) {
      this.tableData = data || []
      this.$nextTick(() => {
        if (this.tableRef) {
          this.tableRef.loadData(this.tableData)
        }
      })
    },

    // 工具栏按钮点击事件
    handleClickToolBar(item) {
      const selectedRecords = this.tableRef?.getCheckboxRecords() || []

      switch (item.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          if (selectedRecords.length === 0) {
            this.$toast({ content: this.$t('请先选择要删除的行'), type: 'warning' })
            return
          }
          this.handleDelete()
          break
        default:
          break
      }
    },
    // 新增行
    handleAdd() {
      if (this.isEditing) {
        this.$toast({ content: this.$t('请先保存当前编辑的行'), type: 'warning' })
        return
      }
      // 从详情信息中获取供应商名称和公司名称
      const supplierName = this.detailInfo.supplierName || this.detailInfo.supplier || ''
      const companyName = this.detailInfo.companyName || this.detailInfo.company || ''

      const newRow = {
        sellerName: supplierName, // 销售方名称赋值为单据供应商名称
        sellerTaxNumber: '',
        buyerName: companyName, // 购买方名称为单据所属公司名称
        buyerTaxNumber: '', // 购买方税号由后端赋值，不可编辑
        productName: '',
        productTaxItem: '',
        limitAmount: ''
      }

      // 直接添加到tableData中
      this.tableData.push(newRow)
      this.$nextTick(() => {
        if (this.tableRef) {
          // 重新加载数据
          this.tableRef.loadData(this.tableData)
          // 设置最后一行为编辑状态
          const lastRowIndex = this.tableData.length - 1
          this.tableRef.setEditRow(lastRowIndex)
          this.isEditing = true
        }
        // 触发数据变化事件
        this.emitDataChange()
      })
    },
    // 删除行
    handleDelete() {
      const selectedRecords = this.tableRef?.getCheckboxRecords() || []
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的行？')
        },
        success: () => {
          // 从tableData中删除选中的行
          selectedRecords.forEach(record => {
            const index = this.tableData.findIndex(item => item === record)
            if (index > -1) {
              this.tableData.splice(index, 1)
            }
          })

          // 重新加载表格数据
          this.$nextTick(() => {
            if (this.tableRef) {
              this.tableRef.loadData(this.tableData)
            }
            // 重置编辑状态
            this.isEditing = false
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            // 触发数据变化事件
            this.emitDataChange()
          })
        }
      })
    },
    // 编辑激活事件
    editActived() {
      this.isEditing = true
    },
    // 编辑完成事件
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验必填项
        this.tableRef?.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }

      // 重置编辑状态
      this.isEditing = false
      // 触发数据变化事件
      this.$nextTick(() => {
        this.emitDataChange()
      })
    },
    // 触发数据变化事件
    emitDataChange() {
      // 直接使用tableData，无需区分新增状态
      this.$emit('data-change', this.tableData || [])
    },
    // 强制刷新表格数据
    refreshTableData() {
      if (this.tableRef) {
        this.tableRef.loadData(this.tableData)
        this.$nextTick(() => {
          this.emitDataChange()
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.red-invoice-table {
  .table-item {
    width: 100%;
    margin-top: 10px;

    .item-top {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .item-title {
        display: flex;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-weight: 500;

        .title-prefix {
          width: 5px;
          height: 18px;
          background-color: #409eff;
          margin: 7px 15px 0 0;
        }
      }

      .item-icon {
        color: #409eff;
        transform: rotate(270deg);
        cursor: pointer;
      }

      .item-icon-hidden {
        transform: rotate(90deg);
        margin-right: 16px;
      }
    }
  }
}
</style>
