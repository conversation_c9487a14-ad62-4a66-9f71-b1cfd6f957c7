module.exports = {
  plugins: [
    ['import', { libraryName: 'ant-design-vue', libraryDirectory: 'es', style: 'css' }],
    [
      'component',
      {
        libraryName: '@digis/j-antdv',
        styleLibrary: {
          name: 'style',
          base: false
        }
      }
    ],
    [
      'import',
      {
        libraryName: 'vxe-table',
        style: true // 样式是否也按需加载
      },
      'vxe-table'
    ],
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator'
  ]
}
