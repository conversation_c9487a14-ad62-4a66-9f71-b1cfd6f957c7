{"name": "mtech-srm-supplier-web", "version": "0.0.1", "private": true, "scripts": {"build": "vue-cli-service build", "build:uat": "vue-cli-service build --mode uat", "lint": "vue-cli-service lint", "build:report": "vue-cli-service build --report --report-json", "buildAll": "npm-run-all theme build --continue-on-error", "dev": "vue-cli-service serve", "serveAll": "npm-run-all theme serve --continue-on-error", "upgrade:mtech-ui": "npx update-by-scope -t latest @mtech npm install", "information": "node build/dict.js", "translate": "node translate.js", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"@digis/component-props-state": "^1.2.6", "@digis/dictionary-plugin": "^1.1.4", "@digis/digis-bpmn-editor": "0.2.1", "@digis/internationalization": "^1.1.16", "@mtech-common/http": "^1.0.5", "@mtech-common/utils": "^0.19.0", "@mtech-form-design/form-parser": "^1.6.4", "@mtech-form-design/information-parser": "^1.0.19", "@mtech-micro-frontend/vue-cli-plugin-micro": "^1.0.0", "@mtech-sso/single-sign-on": "^1.2.5-tcl.7", "@mtech-ui/base": "^1.10.10", "@mtech-ui/checkbox": "^1.11.14", "@mtech-ui/date-range-picker": "^1.11.14", "@mtech-ui/date-time-picker": "^1.11.14", "@mtech-ui/drop-down-tree": "^1.11.14", "@mtech-ui/input-number": "^1.10.6", "@mtech-ui/listbox": "^1.11.14", "@mtech-ui/mtech-ui": "^1.11.14", "@mtech-ui/multi-select": "^1.11.27", "@mtech-ui/query-builder": "^1.11.27", "@mtech-ui/select": "^1.11.27", "@mtech-ui/switch": "^1.11.14", "@mtech-ui/time-picker": "^1.11.14", "@mtech-ui/tree-grid": "^1.11.14", "@mtech/common-loading": "^0.1.41", "@mtech/common-permission": "^1.2.5", "@mtech/common-tree-view": "^0.1.41", "@mtech/mtech-common-uploader": "^1.8.4", "@syncfusion/ej2-vue-dropdowns": "^19.4.56", "@tinymce/tinymce-vue": "^3.2.8", "ant-design-vue": "^1.7.8", "axios": "^0.21.1", "babel-plugin-component": "^1.1.1", "core-js": "^3.6.5", "cross-env": "^7.0.3", "echarts": "^5.2.1", "html2canvas": "^1.3.2", "increase-memory-limit": "^1.0.7", "js-cookie": "^2.2.1", "jspdf": "^2.4.0", "lodash": "^4.17.21", "node-sass": "^4.14.1", "pdfobject": "^2.2.5", "smoothscroll-polyfill": "^0.4.4", "sortablejs": "^1.14.0", "tinymce": "^5.10.5", "vue": "^2.6.11", "vue-class-component": "^8.0.0-rc.1", "vue-router": "^3.5.1", "vue-wordcloud": "^1.1.1", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "vuex-persistedstate": "^4.0.0", "vxe-table": "^3.6.13", "xe-utils": "^3.5.11", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@digis-vue-cli-preset/vue-cli-plugin-performance": "^1.1.5", "@mtech/eslint-config-vue": "^0.0.4", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "^4.5.13", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.5", "chalk": "^2.4.2", "element-resize-detector": "^1.2.4", "eslint": "^6.7.2", "husky": "^8.0.3", "lint-staged": "^13.1.4", "node-sass": "^4.14.1", "npm-run-all": "^4.1.5", "sass-loader": "^8.0.0", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.5", "vue-cli-plugin-style-resources-loader": "~0.1.5", "vue-template-compiler": "^2.6.11", "webpack-fix-style-only-entries": "^0.6.1", "webpack-glob-entry": "^2.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "mtMicro": {"type": "slave"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"]}}