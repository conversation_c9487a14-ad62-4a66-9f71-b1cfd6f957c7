.DS_Store
node_modules

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
build
dist
docs
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
xe-utils.js
<EMAIL>

# files
src/views/purchase/componentsDoc
src/components/template-page
vue.config.js
src/views/Buyer/supplierResources/children/components/world.js
src/views/Buyer/supplierResources/children/components/china.js
src/views/Buyer/supplierRegistration/components/addRegister copy.vue
src/views/Buyer/registrationManagement/components/addRegisterOld.vue
src/views/Buyer/RectifyManagement/components/toBeRectified/detail.vue
src/views/Buyer/RectifyManagement/components/toBeRectified/components/addDialog.vue
src/views/Buyer/purReview/scoreSetting/components/dimension/components/dimensionDialog.vue
src/views/Buyer/moduleConfig/questionnaireConfig/components/operatorDialog.vue
src/views/Buyer/moduleConfig/PolicySettings/components/AccessProcess/components/processConfig/components/scene-matrix.vue
src/views/Buyer/categoryCertifiManage/components/certificationDialog.vue
src/views/Buyer/analysisOfSetting/scoreSetting/components/template/components/templateDialog.vue
src/views/Buyer/analysisModule/performanceAnalysis/planDetail/components/templateDetail.vue
src/components/RichTextEditor/tinymce/langs/zh_TW.js
src/components/RichTextEditor/tinymce/langs/zh_CN.js
src/components/RichTextEditor/tinymce/ax_wordlimit/plugin.min.js
src/components/RichTextEditor/tinymce/ax_wordlimit/plugin.js
src/components/RichTextEditor/RichTextEditor.vue
src/views/Buyer/assessManage/appealDeal/detail.vue
src/assets/swiper/swiper-bundle.min.js
