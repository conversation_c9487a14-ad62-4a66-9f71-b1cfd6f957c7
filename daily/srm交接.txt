SRM采购方端-UAT环境网址：https://srm-uat-main.eads.tcl.com 
账号：lv2.li 密码：idM@UAT#2022
SRM供应商端-UAT环境网址：https://srm-uat.eads.tcl.com/#home
账号：108106 密码：cs123456@

采购执行web：https://devops.tone.tcl.com/code/operation-srm/mtech-srm-purchase-execute-web/-/tree
寻源web：https://devops.tone.tcl.com/code/operation-srm/mtech-srm-sourcing-web/-/tree
采方登录web：https://devops.tone.tcl.com/code/operation-srm/mtech-login-web/-/tree
供方登录web：https://devops.tone.tcl.com/code/operation-srm/mtech-login-supplier-web/-/tree
主应用web：https://devops.tone.tcl.com/code/operation-srm/mtech-srm-main-web/-/tree
安装依赖运行使用node-v14.16.0

login-web、main-web：
proxy.config.js文件 target: 'https://srm-uat-main.eads.tcl.com',
login-supplier-web: 
proxy.config.js文件 target: process.env.PROXY_TARGET || 'http://srm-uat.eads.tcl.com',
nuxt.config.js文件 port: 9013

开发时：采方登录web、供方登录web、主应用web这三个必须启动；需要开发的web对应启动
ps:注意9011端口是采方登录，9013端口是供方登录；如果正确输入账号密码还是登录不进去，考虑开启atrust规避网络问题引起的本地登录失败问题

本地采方：http://localhost:9011/login
本地供方：http://localhost:9013/login

vxe组件：https://vxeui.com/v3/#/grid/api

删掉node_modules
重启终端
设置tone镜像 npm config set registry https://tone.tcl.com/mirrors/npm/
node版本14.16.0
公司网络

寻源web：/sourcing/jig-material-relationship 物料与治具关系表 参考