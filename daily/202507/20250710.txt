0710

    <common-list-container
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :table-data="tableData"
      :loading="loading"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :search-form-data="searchForm"
      :grid-id="gridId"
      :search-grid-id="searchGridId"
      :pagination="pagination"
      @refresh="handleRefresh"
      @search="handleSearch"
      @toolbarClick="handleToolbarAction"
      @pageChange="handlePageChange"
      @pageSizeChange="handlePageSizeChange"
    >
      <template #testReportDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleFileClick(row)">
          {{ $t('附件') }}
        </div>
      </template>
      <template #processStatusDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleStatusClick(row)">
          {{ row.processStatusDesc }}
        </div>
      </template>
    </common-list-container>

    重构CommonListContainer组件，不用兼容CommonList组件，删除ListToolbar组件
    只需要按上述写法就能实现搜索、查询、按钮、表格、分页等功能