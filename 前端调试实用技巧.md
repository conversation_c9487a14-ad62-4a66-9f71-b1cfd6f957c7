# 前端调试实用技巧

## 目录
- [浏览器开发者工具](#浏览器开发者工具)
- [Console 调试技巧](#console-调试技巧)
- [网络调试](#网络调试)
- [性能调试](#性能调试)
- [Vue.js 调试](#vuejs-调试)
- [移动端调试](#移动端调试)
- [常见问题排查](#常见问题排查)

## 浏览器开发者工具

### 快捷键
- **F12** 或 **Ctrl+Shift+I** (Windows/Linux) / **Cmd+Option+I** (Mac) - 打开开发者工具
- **Ctrl+Shift+C** / **Cmd+Option+C** - 元素选择器
- **Ctrl+Shift+J** / **Cmd+Option+J** - 直接打开 Console
- **F5** / **Cmd+R** - 刷新页面
- **Ctrl+F5** / **Cmd+Shift+R** - 强制刷新（忽略缓存）

### Elements 面板技巧
1. **快速定位元素**
   - 右键页面元素 → "检查元素"
   - 使用选择器工具（Ctrl+Shift+C）

2. **编辑 HTML/CSS**
   - 双击元素可直接编辑
   - 右键 → "Edit as HTML" 编辑整个元素
   - 在 Styles 面板中实时修改 CSS

3. **查看元素状态**
   - 在 Styles 面板中点击 `:hov` 查看伪类状态
   - 使用 Computed 面板查看最终计算样式

## Console 调试技巧

### 基础调试方法
```javascript
// 1. 基本输出
console.log('普通信息');
console.warn('警告信息');
console.error('错误信息');
console.info('提示信息');

// 2. 格式化输出
console.log('%c自定义样式', 'color: red; font-size: 20px;');
console.log('用户名: %s, 年龄: %d', 'John', 25);

// 3. 分组输出
console.group('用户信息');
console.log('姓名: 张三');
console.log('年龄: 25');
console.groupEnd();

// 4. 表格输出
const users = [
  { name: '张三', age: 25 },
  { name: '李四', age: 30 }
];
console.table(users);
```

### 高级调试技巧
```javascript
// 1. 条件断点
console.assert(user.age > 18, '用户年龄必须大于18岁');

// 2. 计时器
console.time('API请求');
// ... 执行代码
console.timeEnd('API请求');

// 3. 计数器
console.count('点击次数');

// 4. 堆栈跟踪
console.trace('执行路径');

// 5. 清空控制台
console.clear();
```

### 调试对象和数组
```javascript
// 1. 深度查看对象
console.dir(document.body);

// 2. 查看对象属性
const obj = { a: 1, b: { c: 2 } };
console.log(JSON.stringify(obj, null, 2));

// 3. 监控变量变化
let count = 0;
Object.defineProperty(window, 'debugCount', {
  get() { return count; },
  set(val) {
    console.log(`count 从 ${count} 变为 ${val}`);
    count = val;
  }
});
```

## 网络调试

### Network 面板使用
1. **筛选请求**
   - 按类型筛选：XHR、JS、CSS、Img 等
   - 按状态码筛选：4xx、5xx 错误
   - 使用搜索框过滤特定请求

2. **分析请求详情**
   - Headers：查看请求头和响应头
   - Preview：预览响应内容
   - Response：查看原始响应
   - Timing：分析请求时间

3. **模拟网络环境**
   - 在 Network 面板顶部选择网络速度
   - 模拟慢速网络测试加载性能

### 常见网络问题排查
```javascript
// 1. 监控 AJAX 请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
  console.log('Fetch 请求:', args);
  return originalFetch.apply(this, args)
    .then(response => {
      console.log('Fetch 响应:', response);
      return response;
    });
};

// 2. 监控 XMLHttpRequest
const originalOpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url) {
  console.log(`XHR ${method} 请求:`, url);
  return originalOpen.apply(this, arguments);
};
```

## 性能调试

### Performance 面板
1. **录制性能**
   - 点击录制按钮开始
   - 执行需要分析的操作
   - 停止录制查看结果

2. **分析指标**
   - FCP (First Contentful Paint)
   - LCP (Largest Contentful Paint)
   - FID (First Input Delay)
   - CLS (Cumulative Layout Shift)

### 内存调试
```javascript
// 1. 监控内存使用
console.log(performance.memory);

// 2. 检测内存泄漏
const weakMap = new WeakMap();
function trackObject(obj, name) {
  weakMap.set(obj, name);
  console.log(`追踪对象: ${name}`);
}

// 3. 性能标记
performance.mark('开始处理');
// ... 执行代码
performance.mark('结束处理');
performance.measure('处理时间', '开始处理', '结束处理');
```

## Vue.js 调试

### Vue DevTools
1. **安装 Vue DevTools 浏览器扩展**
2. **组件调试**
   - 查看组件层级结构
   - 实时修改组件数据
   - 监控组件事件

### Vue 调试技巧
```javascript
// 1. 在模板中调试
<template>
  <div>
    {{ console.log('当前数据:', data) || '' }}
    {{ debugData }}
  </div>
</template>

// 2. 监听数据变化
watch: {
  userData: {
    handler(newVal, oldVal) {
      console.log('用户数据变化:', { newVal, oldVal });
    },
    deep: true
  }
}

// 3. 生命周期调试
mounted() {
  console.log('组件已挂载:', this.$el);
  console.log('组件数据:', this.$data);
}
```

## 移动端调试

### 远程调试
1. **Chrome 远程调试**
   - 手机开启 USB 调试
   - Chrome 访问 `chrome://inspect`
   - 选择设备进行调试

2. **Safari 远程调试**
   - iOS 设备开启 Web 检查器
   - Mac Safari 开发菜单连接设备

### 移动端调试工具
```javascript
// 1. vConsole - 移动端控制台
import VConsole from 'vconsole';
const vConsole = new VConsole();

// 2. Eruda - 移动端开发者工具
import eruda from 'eruda';
eruda.init();

// 3. 触摸事件调试
document.addEventListener('touchstart', (e) => {
  console.log('触摸开始:', e.touches);
});
```

## 常见问题排查

### JavaScript 错误
```javascript
// 1. 全局错误捕获
window.addEventListener('error', (e) => {
  console.error('JavaScript 错误:', e.error);
});

// 2. Promise 错误捕获
window.addEventListener('unhandledrejection', (e) => {
  console.error('未处理的 Promise 错误:', e.reason);
});

// 3. 资源加载错误
window.addEventListener('error', (e) => {
  if (e.target !== window) {
    console.error('资源加载失败:', e.target.src || e.target.href);
  }
}, true);
```

### CSS 调试
```css
/* 1. 边框调试法 */
* {
  border: 1px solid red !important;
}

/* 2. 背景色调试法 */
.debug {
  background-color: rgba(255, 0, 0, 0.3) !important;
}

/* 3. 网格调试 */
.container {
  background-image: 
    linear-gradient(rgba(255,0,0,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,0,0,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
```

### 响应式调试
1. **设备模拟**
   - F12 → 点击设备图标
   - 选择不同设备尺寸测试

2. **自定义断点**
   - 在 Sources 面板设置断点
   - 使用条件断点：`window.innerWidth < 768`

## 调试最佳实践

### 1. 代码组织
- 使用有意义的变量名和函数名
- 添加适当的注释
- 保持代码结构清晰

### 2. 错误处理
- 使用 try-catch 包装可能出错的代码
- 提供有意义的错误信息
- 记录错误日志

### 3. 测试策略
- 单元测试覆盖核心逻辑
- 集成测试验证组件交互
- 端到端测试模拟用户操作

### 4. 性能优化
- 避免不必要的重渲染
- 使用防抖和节流
- 优化图片和资源加载

## 总结

前端调试是一个系统性的技能，需要：
1. 熟练掌握浏览器开发者工具
2. 了解各种调试技巧和方法
3. 建立良好的调试习惯
4. 持续学习新的调试工具和技术

记住：**好的调试技能能让开发效率提升数倍！**
