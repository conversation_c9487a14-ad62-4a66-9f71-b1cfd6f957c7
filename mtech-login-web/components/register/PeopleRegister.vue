<template>
  <div class="step-three--wrapper">
    <p class="tip">请填写管理员的个人注册信息，如您手机号已注册，将提示您直接登录账号即可。</p>
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" class="mt-form--wrap">
      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_tel" />
        <mt-form-item prop="phone" class="form-item" label="手机号">
          <mt-select
            width="50"
            popupWidth="80"
            :value="selectedArea"
            :dataSource="areaCodeList"
            :readonly="false"
          ></mt-select>
          <mt-input
            v-model="ruleForm.phone"
            :disabled="false"
            :width="400"
            :showClearButton="true"
            type="text"
            placeholder="请输入手机号"
          ></mt-input>
        </mt-form-item>
      </div>

      <div class="form-item--wrap form-item-verificaCode--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_Comple" />
        <mt-form-item prop="verificaCode" class="form-item" label="手机验证码">
          <mt-input
            :width="450"
            v-model="ruleForm.verificaCode"
            type="text"
            placeholder="请输入手机验证码"
          ></mt-input>
          <mt-button cssClass="verifica--button">发送验证码</mt-button>
        </mt-form-item>
      </div>

      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_password" />
        <mt-form-item prop="password" class="form-item" label="密码">
          <mt-input
            :width="450"
            v-model="ruleForm.password"
            type="password"
            placeholder="请输入密码"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_password" />
        <mt-form-item prop="repassword" class="form-item" label="再次输入密码">
          <mt-input
            :width="450"
            v-model="ruleForm.repassword"
            type="password"
            placeholder="确定密码"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_name" />
        <mt-form-item prop="name" class="form-item" label="姓名">
          <mt-input
            :width="450"
            v-model="ruleForm.repassword"
            type="text"
            placeholder="请输入姓名"
          ></mt-input>
        </mt-form-item>
      </div>

      <div class="form-item--more" @click="isShowMore = !isShowMore">
        <svg-icon v-show="!isShowMore" style="font-size: 20px" icon-class="icon_pack_up" />
        <svg-icon v-show="isShowMore" style="font-size: 20px" icon-class="icon_expansion" />
        <span
          ><span v-show="!isShowMore">展开</span><span v-show="isShowMore">收起</span>更多信息</span
        >
      </div>

      <span v-show="isShowMore">
        <div class="form-item--wrap">
          <svg-icon class="icon--wrap" icon-class="icon_login_name" />
          <mt-form-item prop="username" class="form-item" label="用户名">
            <mt-input
              :width="450"
              v-model="ruleForm.repassword"
              type="text"
              placeholder="请输入用户名"
            ></mt-input>
          </mt-form-item>
        </div>

        <div class="form-item--wrap">
          <svg-icon class="icon--wrap" icon-class="icon_login_mail" />
          <mt-form-item prop="email" class="form-item" label="邮箱">
            <mt-input
              :width="450"
              v-model="ruleForm.repassword"
              type="text"
              placeholder="请输入邮箱"
            ></mt-input>
          </mt-form-item>
        </div>

        <div class="form-item--wrap">
          <svg-icon class="icon--wrap" icon-class="icon_login_mail_authentication" />
          <mt-form-item prop="emailValid" class="form-item" label="邮箱认证">
            <mt-input
              :width="450"
              v-model="ruleForm.repassword"
              type="text"
              placeholder="请输入邮箱认证"
            ></mt-input>
          </mt-form-item>
        </div>
      </span>
    </mt-form>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {},
  middleware: ['config']
  // async asyncData({ app, error }) {}
})
export default class RegisterFlow extends Vue {
  ruleForm = {}

  rules = {
    phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
    verificaCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    repassword: [{ required: true, message: '请再次输入密码', trigger: 'blur' }],
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  }

  isShowMore = false

  selectedArea = '+86'
  areaCodeList: object[] = [
    {
      value: '+86',
      text: '+86'
      // text: '中国',
    },
    {
      value: '+852',
      text: '+852'
      // text: '香港',
    },
    {
      value: '+853',
      text: '+853'
      // text: '澳门',
    }
  ]

  handleNext() {
    this.$emit('next')
    // this.$parent.nextStep()
  }
  handlePrev() {
    this.$emit('prev')
    // this.$parent.prevStep()
  }
}
</script>

<style lang="less" scoped>
.step-three--wrapper {
  width: 700px;
  margin: 0 auto;
  text-align: center;

  .verifica--button {
    width: 144px;
    height: 40px;
    background: #eda133;
    color: #fff;
    box-shadow: none;
    margin-bottom: 4px;
  }
}
.tip {
  font-size: 14px;
  font-weight: normal;
  color: rgba(0, 70, 156, 1);
  margin: 50px 0 80px 0;
  text-align: center;
}
.mt-form--wrap {
  display: inline-block;
  .icon--wrap {
    font-size: 24px;
    margin-bottom: 12px;
    margin-right: 10px;
  }

  .form-item {
    display: inline-block;
  }
  .form-item-verificaCode--wrap {
    position: relative;

    /deep/ .mt-button {
      position: absolute;
      bottom: 0;
      right: 0;
    }
  }
}

.form-item--more {
  text-align: right;
  color: #00469c;
  font-size: 14px;
  > * {
    cursor: pointer;
  }
}
</style>
