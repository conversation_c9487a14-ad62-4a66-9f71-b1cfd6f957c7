<template>
  <div style="display: inline-block">
    <span v-if="data.icon">
      <svg v-if="data.icon" class="icon" aria-hidden="true">
        <use :xlink:href="'#icon-' + data.icon"></use>
      </svg>
    </span>
    <span :class="{ menuText: data.icon }" style="font-size: 14px">{{ data.name }}</span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {}
    }
  }
}
</script>
