<template>
  <div class="pf-header">
    <img :src="logoUrl" alt="" @click="toUrl" />
    <!-- <p>{{ name }} {{ title }}</p> -->
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
@Component
export default class PfHeader extends Vue {
  @Prop({ type: String, required: false, default: '' }) readonly title!: String

  logoUrl: string = ''
  name: string = ''
  url: string = ''
  created() {
    if (this.$store.state && this.$store.state.application) {
      const { application } = this.$store.state
      this.logoUrl = application.logoUrl
      this.name = application.applicationName
      this.url = application.url
    }
  }

  toUrl() {
    if (this.url) {
      window.location.href = this.url
    }
  }
}
</script>

<style lang="less" scoped>
.pf-header {
  display: flex;
  align-items: center;
  overflow: hidden;
  width: 100%;
  // height: 90px;
  // line-height: 90px;
  // background-color: #fff;
  position: fixed;
  top: 100px;
  left: 100px;

  // padding: 20px;
  > img {
    // width: 200px;
    // height: 100%;
    // margin-right: 20px;
    width: 263px;
    // height: 68px;
    cursor: pointer;
  }
  > p {
    font-size: 28px;
    color: #444344;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
