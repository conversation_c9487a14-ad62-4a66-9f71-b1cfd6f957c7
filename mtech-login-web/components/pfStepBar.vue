<template>
  <div class="pf-step-bar">
    <div
      v-for="(item, index) in stepArr"
      :key="item.title"
      :class="['step-item', index + 1 === stepList.length && 'step-item-last']"
    >
      <!-- <img class="step-img" :src="item.imgUrl" /> -->
      <span :class="['step-index', !activeIndexArr.includes(index) && 'step-grey']">
        {{ index + 1 }}
      </span>
      <div :class="['step-words', !activeIndexArr.includes(index) && 'words-black']">
        <!-- <p>{{ item.title }}</p> -->
        <p>{{ item.content }}</p>
      </div>
      <div
        v-if="stepList.length && index + 1 < stepList.length"
        :class="[
          'step-line',
          activeIndexArr.includes(index) && activeIndexArr.includes(index + 1) && 'line-actived'
        ]"
      ></div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
@Component({})
export default class PfStepBar extends Vue {
  @Prop({ type: Array, required: true, default: () => [] }) readonly stepList!: Array<string>
  @Prop({ type: Number, required: false, default: 0 }) readonly activeIndex!: number

  get activeIndexArr() {
    let arr = []
    for (let i = 0; i <= this.activeIndex; i++) {
      arr.push(i)
    }
    return arr
  }

  get stepArr() {
    let arr: any = []
    this.stepList.forEach((item: string, index: number) => {
      if (index < 3) {
        arr.push({
          title: `Step ${index + 1}`,
          content: item,
          imgUrl: this.activeIndexArr.includes(index)
            ? require(`../assets/images/step_${index + 1}.png`)
            : require(`../assets/images/step_grey_${index + 1}.png`)
        })
      }
    })
    return arr
  }
}
</script>

<style lang="less" scoped>
.pf-step-bar {
  width: 100%;
  display: flex;
  .step-item {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    .step-img {
      width: 40px;
      height: 40px;
    }
    .step-index {
      width: 38px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      background: #00469c;
      color: #fff;
      font-size: 27px;
      border-radius: 50%;
      &.step-grey {
        width: 24px;
        height: 24px;
        line-height: 24px;
        font-size: 18px;
        background: #3e3e3e;
      }
    }
    .step-words {
      // height: 40px;
      // padding: 4px 0;
      margin-left: 8px;
      color: #003c86;
      font-size: 24px;
      > p {
        line-height: 15px;
        // font-size: 15px;
        white-space: nowrap;
        &:first-child {
          margin-bottom: 2px;
        }
      }
    }
    .words-grey {
      color: #999;
      font-size: 18px;
    }
    .words-black {
      color: #292929;
      font-size: 16px;
    }
    .step-line {
      flex: 1;
      height: 0;
      // height: 1px;
      // background-color: #ccc;
      margin: 0 10px;
      border-bottom: 1px dashed #979797;
    }
    .line-actived {
      background-color: #005da9;
    }
  }
  .step-item-last {
    width: auto;
  }
}
</style>
