<template>
  <div class="change-language">
    <mt-select
      :width="100"
      :popupWidth="100"
      cssClass="lang-select"
      v-model="lang"
      :fields="{ text: 'name', value: 'code' }"
      :dataSource="dataArr"
      @showPopup="showPopup"
    ></mt-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
@Component
export default class ChangeLanguage extends Vue {
  dataArr: object = []
  lang: String = localStorage.getItem('internationlization') || 'zh'
  created() {
    this.getLangList()
    this.$changeL(this.lang)
    this.$cookies.set('lange', this.lang)
  }
  // mounted() {
  //   this.$nextTick(() => {
  //     this.showPopup()
  //   })
  // }
  // showPopup() {}
  changeLang(val: Object) {
    let _locale = (val as any).value
    this.$store.commit('setLocale', _locale)
    this.$cookies.set('lange', _locale)
    this.$changeL(_locale)
  }
  async getLangList() {
    const res = await this.$api.common.getLangList()
    this.dataArr = res.data || []
  }

  @Watch('lang')
  onWatchInvoiceList(val: any[]) {
    this.changeLang({ value: val })
  }
}
</script>

<style lang="less" scoped>
.change-language {
  position: absolute;
  right: 0;
  top: -31px;

  /deep/ .lang-select {
    color: #fff;
    font-size: 16px;

    .e-float-line {
      display: none;
    }
    .e-input-group-icon {
      color: #fff;
    }
  }
}
</style>
