import router from './modules/router'

export const state = () => ({
  application: {},
  countryList: [
  ],
  defaultProvinceList:[],
  locale: 'zh',
})
export const getters = {
  getApplication: (state: any) => state.application,
  getCountryList: (state: any) => state.countryList,
  getDefaultProvinceList: (state: any) => state.defaultProvinceList
}

export const mutations = {
  setApplication(state: any, playload: Object) {
    state.application = playload
  },
  setCountryList(state: any, playload: Object[]) {
    state.countryList = playload
  },
  setLocale(state: any, payload: string) {
    state.locale = payload
    localStorage.setItem('internationlization',payload);
    location.reload()
  },
  setDefaultProvinceList(state: any, payload: string) {
    state.defaultProvinceList = payload
  },
}

export const actions = {
  // nuxtServerInit({ commit }: any, { app }: any) {
  //   const token: string = app.$cookies.get('token')
  //   if (token) {
  //     commit('user/SET_TOKEN', token)
  //   }
  // },
  setApplication: ({ commit }: any, playload: Object) => {
    commit('setApplication', playload)
  },
  setCountryList: ({ commit }: any, playload: Object[]) => {
    commit('setCountryList', playload)
  },
  setDefaultProvinceList: ({ commit }: any, playload: Object[]) => {
    commit('setDefaultProvinceList', playload)
  },
}

export const modules = {
  router,
}
