{"name": "mtech-login-web", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=dev nuxt-ts", "dev-check": "cross-env NODE_ENV=devCheck nuxt-ts", "build": "cross-env NODE_ENV=production nuxt-ts build", "build-dev": "cross-env NODE_ENV=dev nuxt-ts build", "build-test": "cross-env NODE_ENV=test nuxt-ts build", "build-pro": "cross-env NODE_ENV=pro nuxt-ts build", "start": "cross-env NODE_ENV=production nuxt-ts start", "generate": "nuxt-ts generate", "lint:js": "eslint --fix --ext .js,.vue --ignore-path .eslint<PERSON>ore .", "lint": "npm --max-old-space-size=4096 run lint:js", "test": "jest", "fix-memory-limit": "cross-env LIMIT=20240 increase-memory-limit", "upgrade:mtech-ui": "npx update-by-scope -t latest @mtech-ui npm install", "dictionary": "node build/dict.js", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"@digis/dictionary-plugin": "^1.1.11", "@digis/internationalization": "^1.1.17", "@mtech-common/http": "^1.0.1", "@mtech-common/utils": "^1.0.0", "@mtech-sso/single-sign-on": "1.2.5-tcl.7", "@mtech-ui/accordion": "^1.6.2", "@mtech-ui/checkbox-group": "^1.6.2", "@mtech-ui/col": "^1.6.2", "@mtech-ui/data-grid": "^1.6.2", "@mtech-ui/date-picker": "^1.6.2", "@mtech-ui/date-range-picker": "^1.6.2", "@mtech-ui/dialog": "^1.6.2", "@mtech-ui/input-number": "^1.6.2", "@mtech-ui/multi-select": "^1.6.2", "@mtech-ui/progress": "^1.6.2", "@mtech-ui/radio": "^1.6.2", "@mtech-ui/row": "^1.6.2", "@mtech-ui/tag": "^1.6.2", "@mtech-ui/tree-view": "^1.6.2", "@mtech-ui/uploader": "^1.6.2", "@nuxt/typescript-runtime": "^2.0.0", "@nuxtjs/axios": "^5.12.2", "@nuxtjs/proxy": "^2.0.1", "@nuxtjs/universal-storage": "^0.5.5", "cookie-universal-nuxt": "^2.1.4", "core-js": "^3.6.5", "cross-env": "^7.0.2", "currency.js": "^2.0.4", "es6-promise": "^4.2.8", "js-cookie": "^3.0.0", "jsencrypt": "^3.2.1", "koa": "^2.13.0", "koa-bodyparser": "^4.3.0", "koa-router": "^10.0.0", "nuxt": "^2.14.6", "vue-class-component": "^7.2.6", "vue-i18n": "^8.24.4", "vue-property-decorator": "^9.0.2", "vuex-class": "^0.3.2"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@mtech-ui/base": "^1.6.2", "@mtech-ui/button": "^1.6.2", "@mtech-ui/checkbox": "^1.6.2", "@mtech-ui/form": "^1.6.2", "@mtech-ui/form-item": "^1.6.2", "@mtech-ui/icon": "^1.6.2", "@mtech-ui/input": "^1.6.2", "@mtech-ui/select": "^1.6.2", "@mtech-ui/toast": "^1.6.2", "@mtech/eslint-config-vue": "0.0.4", "@nuxt/types": "^2.14.6", "@nuxt/typescript-build": "^2.0.3", "@nuxtjs/eslint-config": "^3.1.0", "@nuxtjs/eslint-config-typescript": "^3.0.0", "@nuxtjs/eslint-module": "^2.0.0", "@vue/test-utils": "^1.1.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.5.0", "eslint": "^7.10.0", "eslint-config-prettier": "^6.12.0", "eslint-loader": "^4.0.2", "eslint-plugin-html": "^6.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-nuxt": "^1.0.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-vue": "^7.1.0", "husky": "^8.0.3", "jest": "^26.5.0", "less": "^3.12.2", "less-loader": "^7.1.0", "lint-staged": "^13.1.4", "prettier": "^2.1.2", "prettier-eslint-cli": "^5.0.0", "svg-sprite-loader": "^6.0.9", "ts-jest": "^26.4.1", "typescript": "^4.0.7", "vue-jest": "^3.0.4"}, "mtMicro": {"type": "master"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"]}}