<template>
  <div class="pf-phone">
    <!-- <pf-header title="修改手机号"></pf-header> -->
    <div class="phone-box">
      <div class="phone-body">
        <pf-step-bar :step-list="stepList" :active-index="stepIndex"></pf-step-bar>
        <div class="form-box">
          <template v-if="stepIndex === 0">
            <form id="phoneOldForm">
              <mt-input
                ref="oldPhoneRef"
                key="oldPhone"
                v-model="oldPhone"
                class="input-item"
                :validate-msg="errMsg"
                :required="true"
                :country-phone="true"
                field="oldPhone"
                css-class="e-outline"
                type="number"
                :placeholder="$t('请输入原手机号码')"
                @select="handlePhoneSelect"
                @keyup.enter="oldPhoneKeyup"
              >
                <img slot="preIcon" src="../../../assets/images/icon_phone.png" />
              </mt-input>
              <mt-input
                ref="oldVerifyCodeRef"
                key="oldVerificationCode"
                v-model="oldVerifyCode"
                class="input-item"
                :validate-msg="errMsg"
                :required="true"
                field="oldVerificationCode"
                type="number"
                css-class="e-outline"
                :placeholder="$t('验证码')"
                @keyup.enter="handleNext"
              >
                <img slot="preIcon" src="../../../assets/images/icon_psw.png" />
                <pf-verify-button
                  key="old"
                  ref="verifyBtnOldRef"
                  slot="back"
                  @click.native="openCaptcha"
                  @finish="getVerifyCode"
                ></pf-verify-button>
              </mt-input>
            </form>
            <button class="submit-btn" @click="handleNext">{{ $t('下一步') }}</button>
          </template>
          <template v-if="stepIndex === 1">
            <form id="phoneNewForm">
              <mt-input
                ref="newPhoneRef"
                key="newPhone"
                v-model="newPhone"
                class="input-item"
                :validate-msg="errMsg"
                :required="true"
                :country-phone="true"
                field="newPhone"
                css-class="e-outline"
                type="number"
                :placeholder="$t('请输入新手机号码')"
                @select="handlePhoneSelect"
                @keyup.enter="newPhoneKeyup"
              >
                <img slot="preIcon" src="../../../assets/images/icon_phone.png" />
              </mt-input>
              <mt-input
                ref="newVerifyCodeRef"
                key="newVerificationCode"
                v-model="newVerifyCode"
                class="input-item"
                :validate-msg="errMsg"
                :required="true"
                field="newVerificationCode"
                type="number"
                css-class="e-outline"
                :placeholder="$t('验证码')"
                @keyup.enter="handleConfirm"
              >
                <img slot="preIcon" src="../../../assets/images/icon_psw.png" />
                <pf-verify-button
                  key="new"
                  ref="verifyBtnNewRef"
                  slot="back"
                  @click.native="openCaptcha"
                  @finish="getVerifyCode"
                ></pf-verify-button>
              </mt-input>
            </form>
            <button class="submit-btn" @click="handleConfirm">{{ $t('确认') }}</button>
            <span class="btn-back" @click="goBack">{{ $t('上一步') }}</span>
          </template>
          <template v-if="stepIndex === 2">
            <div class="finish-img-box">
              <img src="../../../assets/images/finish.png" />
            </div>
            <p class="finish-tips">{{ $t('恭喜！手机号已重置！') }}</p>
            <button class="submit-btn" @click="$router.push('/login')">{{ $t('立即登录') }}</button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
// import { FormValidator } from '@syncfusion/ej2-vue-inputs'
import MtInput from '@/mtComponents/mtInput/index.vue'
// import PfHeader from '@/components/pfHeader.vue'
import PfStepBar from '@/components/pfStepBar.vue'
import PfVerifyButton from '@/components/pfVerifyButton.vue'
import { phoneRegex } from '@/utils/my.validators'
@Component({
  components: {
    MtInput,
    // PfHeader,
    PfStepBar,
    PfVerifyButton
  },
  middleware: ['auth', 'config', 'country'],
  async asyncData({ app, route, error }) {
    let validRs = {
      validOldData: {},
      validNewData: {}
    }
    const { code, phone } = route.query
    try {
      const validOldRs = await app.$api.reset.validForPhoneNext()
      const validNewRs = await app.$api.reset.validForPhoneConfirm()
      if (validOldRs.code == 200) {
        validRs.validOldData = validOldRs.data || {}
      }
      if (validNewRs.code == 200) {
        validRs.validNewData = validNewRs.data || {}
      }
    } catch (err) {
      error({ statusCode: 500 })
    }
    return {
      validOldData: validRs.validOldData,
      validNewData: validRs.validNewData,
      oldPhone: phone || '',
      RegionCode: code || ''
    }
  }
})
export default class ResetPhone extends Vue {
  /** data -start */
  stepList = [this.$t('验证手机号'), this.$t('修改手机号'), this.$t('修改完成')]
  stepIndex = 0
  oldPhone: number | null = null
  oldVerifyCode: number | null = null
  newPhone: number | null = null
  newVerifyCode: number | null = null
  countryRegionParam: object = {
    countryRegionId: 1,
    countryRegionCode: '0086',
    countryRegionName: '中国 0086'
  }

  validOldData: object = {
    oldPhone: {},
    oldVerificationCode: {}
  }

  validNewData: object = {
    newPhone: {},
    newVerificationCode: {}
  }

  formValidate: any = ''
  errMsg: object = {}
  oldOptions: object = {}
  newOptions: object = {}
  /** data -end */

  @Watch('stepIndex')
  onStepIndex(val: number) {
    this.$nextTick(() => {
      if (val === 0) {
        this.getOldValid()
      } else if (val === 1) {
        this.getNewValid()
      }
    })
  }

  mounted() {
    if (this.stepIndex === 0) {
      this.getOldValid()
    } else if (this.stepIndex === 1) {
      this.getNewValid()
    }
  }

  /** method --start */
  getOldValid() {
    if (this.validOldData) {
      const { oldPhone, oldVerificationCode } = this.validOldData as any
      this.oldOptions = {
        rules: {
          oldPhone,
          oldVerificationCode
        },
        validationComplete: (args: object) => {
          if (Object.keys(args).length > 0) {
            const { inputName, status } = args as any
            this.$set(this.errMsg, `${inputName}`, status)
          }
        }
      }
      // this.formValidate = new FormValidator('#phoneOldForm', this.oldOptions)
      // if ((this.countryRegionParam as any).countryRegionCode === '0086') {
      //   this.formValidate.addRules('oldPhone', { regex: [phoneRegex, '手机号格式不正确'] })
      // }
    }
  }

  getNewValid() {
    if (this.validNewData) {
      const { newPhone, newVerificationCode } = this.validNewData as any
      this.newOptions = {
        rules: {
          newPhone,
          newVerificationCode
        },
        validationComplete: (args: object) => {
          if (Object.keys(args).length > 0) {
            const { inputName, status } = args as any
            this.$set(this.errMsg, `${inputName}`, status)
          }
        }
      }
      // this.formValidate = new FormValidator('#phoneNewForm', this.newOptions)
      // if ((this.countryRegionParam as any).countryRegionCode === '0086') {
      //   this.formValidate.addRules('newPhone', { regex: [phoneRegex, '手机号格式不正确'] })
      // }
    }
  }

  openCaptcha() {
    if (this.stepIndex === 0) {
      if (!this.formValidate.validate('oldPhone')) return false
      this.$refs.verifyBtnOldRef && (this.$refs.verifyBtnOldRef as any).openCaptcha()
    } else if (this.stepIndex === 1) {
      if (!this.formValidate.validate('newPhone')) return false
      this.$refs.verifyBtnNewRef && (this.$refs.verifyBtnNewRef as any).openCaptcha()
    }
  }

  async getVerifyCode(res: any) {
    let verificationCodeType = this.stepIndex === 0 ? 3 : 4
    let phone = this.stepIndex === 0 ? this.oldPhone : this.newPhone
    let params = {
      phone,
      rand: res.randstr,
      ticket: res.ticket,
      verificationCodeType,
      ...this.countryRegionParam
    }
    const { code, data } = await this.$api.reset.getVerifyCodeForPhone(params)
    if (code == 200) {
      if (this.stepIndex === 0) {
        this.$refs.verifyBtnOldRef && (this.$refs.verifyBtnOldRef as any).countDown()
      } else if (this.stepIndex === 1) {
        this.$refs.verifyBtnNewRef && (this.$refs.verifyBtnNewRef as any).countDown()
      }
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        if (this.stepIndex === 0) {
          data.errorLabels.forEach((item: any) => {
            if (item.label === 'phone') {
              item.label = 'oldPhone'
            }
          })
          this.handleApiError(data.errorLabels, 'oldPhone')
        } else if (this.stepIndex === 1) {
          data.errorLabels.forEach((item: any) => {
            if (item.label === 'phone') {
              item.label = 'newPhone'
            }
          })
          this.handleApiError(data.errorLabels, 'newPhone')
        }
      }
    }
  }

  async handleNext() {
    if (!this.formValidate.validate()) return false
    let params = {
      oldPhone: this.oldPhone,
      oldVerificationCode: this.oldVerifyCode,
      ...this.countryRegionParam
    }
    const { code, data } = await this.$api.reset.updatePhoneNext(params)
    if (code == 200) {
      this.stepIndex = 1
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.handleApiError(data.errorLabels)
      }
    }
  }

  async handleConfirm() {
    if (!this.formValidate.validate()) return false
    let params = {
      oldPhone: this.oldPhone,
      oldVerificationCode: this.oldVerifyCode,
      newPhone: this.newPhone,
      newVerificationCode: this.newVerifyCode,
      ...this.countryRegionParam
    }
    const { code, data } = await this.$api.reset.updatePhoneConfirm(params)
    if (code == 200) {
      this.stepIndex = 2
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.handleApiError(data.errorLabels)
      }
    }
  }

  goBack() {
    this.stepIndex = 0
  }

  // 接口返回的错误显示到表单,暂时写成校验后立即去掉，后期再优化
  handleApiError(errorLabels: object[], validItem?: string | null) {
    const { rules } = this.formValidate
    errorLabels.forEach((item: any) => {
      Object.prototype.hasOwnProperty.call(rules, `${item.label}`) &&
        this.formValidate.addRules(`${item.label}`, {
          regCustom: [
            () => {
              return false
            },
            item.message
          ]
        })
    })
    validItem ? this.formValidate.validate(`${validItem}`) : this.formValidate.validate()
    errorLabels.forEach((item: any) => {
      Object.prototype.hasOwnProperty.call(rules, `${item.label}`) &&
        this.formValidate.removeRules(`${item.label}`, ['regCustom'])
    })
  }

  handlePhoneSelect(val: any) {
    if (!val.itemData) return false
    this.formValidate.removeRules('phone', ['regex'])
    this.formValidate.removeRules('phone', ['rangeLength'])

    const { itemData } = val
    this.countryRegionParam = {
      countryRegionId: itemData.id,
      countryRegionCode: itemData.code
    }
    // 只校验中国的手机号
    if (itemData.code === '0086') {
      this.formValidate.addRules('phone', { regex: [phoneRegex, this.$t('手机号格式不正确')] })
    } else {
      let phoneRge = {}
      const { length, maxLength, minLength } = itemData
      if (maxLength && minLength) {
        phoneRge = {
          rangeLength: [[minLength, maxLength], this.$t('手机号格式不正确')]
        }
      } else if (length) {
        phoneRge = Object.assign(phoneRge, {
          rangeLength: [[length, length], this.$t('手机号格式不正确')]
        })
      }
      if (phoneRge) {
        this.formValidate.addRules('phone', phoneRge)
      }
    }
  }

  oldPhoneKeyup() {
    if (!this.formValidate.validate('oldPhone')) return false
    this.$refs.oldVerifyCodeRef && (this.$refs.oldVerifyCodeRef as any).focusIn()
  }

  newPhoneKeyup() {
    if (!this.formValidate.validate('newPhone')) return false
    this.$refs.newVerifyCodeRef && (this.$refs.newVerifyCodeRef as any).focusIn()
  }
  /** method --end */
}
</script>

<style lang="less" scoped>
.pf-phone {
  height: 100%;
  overflow: hidden;
}
.phone-box {
  height: calc(100% - 90px);
  display: flex;
  align-items: center;
}
.phone-body {
  width: 800px;
  height: 460px;
  overflow: hidden;
  margin: 0 auto;
  background-color: #fff;
  padding: 50px 60px 30px 60px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  .form-box {
    width: 450px;
    margin: 40px auto 0 auto;
    text-align: center;
    // overflow: hidden;
    .form-inner {
      margin: 15px 0;
    }
    .input-item {
      margin-bottom: 20px;
    }
    .form-group {
      position: relative;
    }
    .input-other-tips {
      display: inline-block;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      min-width: 130px;
      font-size: 12px;
      color: #606266;
      margin-left: 8px;
      .strong-level {
        margin-left: 10px;
      }
      .level-actived {
        color: #f44336;
      }
    }
  }
  .finish-img-box {
    width: 200px;
    height: 160px;
    margin: 0 auto 20px auto;
    > img {
      width: 100%;
      height: 100%;
    }
  }
  .finish-tips {
    font-size: 16px;
    color: #005da9;
    margin-bottom: 10px;
  }
  .btn-back {
    display: inline-block;
    font-size: 14px;
    color: #999;
    cursor: pointer;
    margin-top: 20px;
  }
}
.submit-btn {
  cursor: pointer;
  text-align: center;
  border-radius: 5px;
  background-color: #005ca9;
  color: #fff;
  padding: 0;
  border: none;
  width: 100%;
  font-size: 18px;
  color: #fff;
  margin-top: 20px;
  padding: 12px 0;
}
</style>
