<template>
  <div class="pf-register">
    <!-- <pf-header title="用户注册"></pf-header> -->
    <pf-left-text></pf-left-text>
    <div class="register-body">
      <change-language></change-language>

      <!-- <pf-step-bar :step-list="stepList" :active-index="stepIndex"></pf-step-bar> -->
      <div class="form-content">
        <div class="titles">{{ $t('手机号注册') }}</div>
        <div class="form-box">
          <template>
            <mt-form id="accountForm" ref="accountForm" :model="accountForm" :rules="rules">
              <mt-form-item prop="username" class="form-group input-item">
                <mt-input
                  ref="accountNameRef"
                  v-model="accountForm.username"
                  type="text"
                  :placeholder="$t('请输入用户名')"
                  @blur="handleUserNameBlur"
                  @keyup.enter="accountNameKeyup"
                >
                  <!-- <img slot="preIcon" src="../../assets/images/icon_user.png" /> -->
                  <svg
                    slot="preIcon"
                    t="*************"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="4998"
                    width="24"
                    height="24"
                  >
                    <path
                      d="M1021.76 965.376v-2.304l-0.192-2.368C1004.16 743.232 887.104 576 512 576 143.936 576 24.384 737.024 3.52 948.544l-1.088 12.288a58.496 58.496 0 0 0 53.632 62.976L60.736 1024h902.4c32.384 0 58.624-26.24 58.624-58.624z"
                      fill="#6386C1"
                      p-id="4999"
                    ></path>
                    <path d="M448 576h128v448H448z" fill="#B0C1DF" p-id="5000"></path>
                    <path
                      d="M512 576c375.04 0 492.096 167.232 509.568 384.64l0.128 2.432 0.064 2.304c0 32.384-26.24 58.624-58.624 58.624h-173.184L353.856 587.904c41.152-6.848 86.72-10.88 137.152-11.712z"
                      fill="#000000"
                      fill-opacity=".1"
                      p-id="5001"
                    ></path>
                    <path
                      d="M512 640c159.04 0 288-143.296 288-384 0-176.704-128.96-256-288-256S224 79.296 224 256c0 240.704 128.96 384 288 384z"
                      fill="#FCE2CC"
                      p-id="5002"
                    ></path>
                    <path
                      d="M512 0c19.712 0 38.976 1.216 57.6 3.712V128a128 128 0 0 1-128 128H224l0.32-15.36C231.68 74.688 357.632 0 512 0zM569.6 3.712C701.056 21.12 800 101.12 800 256h-102.4a128 128 0 0 1-128-128z"
                      fill="#EDA133"
                      p-id="5003"
                    ></path>
                  </svg>
                </mt-input>
                <span v-if="userNameRepeat" class="e-error username-error">
                  {{ $t('用户名已存在,请直接') }} <a href="/login"> {{ $t('登录') }} </a></span
                >
              </mt-form-item>

              <mt-form-item prop="nickName" class="form-group input-item">
                <mt-input
                  ref="accountNameRef"
                  v-model="accountForm.nickName"
                  type="text"
                  :placeholder="$t('请输入姓名')"
                >
                  <!-- <img slot="preIcon" src="../../assets/images/icon_user.png" /> -->
                  <svg
                    slot="preIcon"
                    t="*************"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="4998"
                    width="24"
                    height="24"
                  >
                    <path
                      d="M1021.76 965.376v-2.304l-0.192-2.368C1004.16 743.232 887.104 576 512 576 143.936 576 24.384 737.024 3.52 948.544l-1.088 12.288a58.496 58.496 0 0 0 53.632 62.976L60.736 1024h902.4c32.384 0 58.624-26.24 58.624-58.624z"
                      fill="#6386C1"
                      p-id="4999"
                    ></path>
                    <path d="M448 576h128v448H448z" fill="#B0C1DF" p-id="5000"></path>
                    <path
                      d="M512 576c375.04 0 492.096 167.232 509.568 384.64l0.128 2.432 0.064 2.304c0 32.384-26.24 58.624-58.624 58.624h-173.184L353.856 587.904c41.152-6.848 86.72-10.88 137.152-11.712z"
                      fill="#000000"
                      fill-opacity=".1"
                      p-id="5001"
                    ></path>
                    <path
                      d="M512 640c159.04 0 288-143.296 288-384 0-176.704-128.96-256-288-256S224 79.296 224 256c0 240.704 128.96 384 288 384z"
                      fill="#FCE2CC"
                      p-id="5002"
                    ></path>
                    <path
                      d="M512 0c19.712 0 38.976 1.216 57.6 3.712V128a128 128 0 0 1-128 128H224l0.32-15.36C231.68 74.688 357.632 0 512 0zM569.6 3.712C701.056 21.12 800 101.12 800 256h-102.4a128 128 0 0 1-128-128z"
                      fill="#EDA133"
                      p-id="5003"
                    ></path>
                  </svg>
                </mt-input>
              </mt-form-item>

              <mt-form-item prop="phone" class="input-item">
                <mt-input
                  v-model="accountForm.phone"
                  :country-phone="true"
                  css-class="e-outline"
                  type="number"
                  :placeholder="$t('请输入手机号')"
                  @select="handleSelect"
                  @keyup.enter="phoneNumberKeyup"
                >
                  <!-- <img slot="preIcon" src="../../assets/images/icon_phone.png" /> -->
                  <svg
                    slot="preIcon"
                    t="*************"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="3634"
                    width="24"
                    height="24"
                  >
                    <path
                      d="M224 0h576a64 64 0 0 1 64 64v896a64 64 0 0 1-64 64H224a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64z"
                      fill="#6386C1"
                      p-id="3635"
                    ></path>
                    <path
                      d="M288 128m32 0l384 0q32 0 32 32l0 512q0 32-32 32l-384 0q-32 0-32-32l0-512q0-32 32-32Z"
                      fill="#B0C1DF"
                      p-id="3636"
                    ></path>
                    <path
                      d="M864 0v960a64 64 0 0 1-64 64H160L864 0z"
                      fill="#000000"
                      fill-opacity=".1"
                      p-id="3637"
                    ></path>
                    <path
                      d="M416 832m32 0l128 0q32 0 32 32l0 0q0 32-32 32l-128 0q-32 0-32-32l0 0q0-32 32-32Z"
                      fill="#EDA133"
                      p-id="3638"
                    ></path>
                  </svg>
                </mt-input>
              </mt-form-item>
              <mt-form-item prop="verificationCode" class="input-item">
                <mt-input
                  ref="verifyCodeRef"
                  v-model="accountForm.verificationCode"
                  type="number"
                  css-class="e-outline"
                  :placeholder="$t('请输入验证码')"
                  @keyup.enter="verifyCodeKeyup"
                >
                  <!-- <img slot="preIcon" src="../../assets/images/icon_psw.png" /> -->
                  <svg
                    slot="preIcon"
                    t="*************"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="4863"
                    width="24"
                    height="24"
                  >
                    <path
                      d="M320 0h448l256 256v704a64 64 0 0 1-64 64H320a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64z"
                      fill="#6386C1"
                      p-id="4864"
                    ></path>
                    <path
                      d="M1024 460.8l-225.6-214.336a63.616 63.616 0 0 0 26.304 9.152L832 256h192v204.8zM384 416.96l601.664 601.664A63.808 63.808 0 0 1 960 1024H320a64 64 0 0 1-64-64V544.896l128-128z"
                      fill="#000000"
                      fill-opacity=".1"
                      p-id="4865"
                    ></path>
                    <path
                      d="M1024 256h-192a64 64 0 0 1-64-64V0l256 256z"
                      fill="#B0C1DF"
                      p-id="4866"
                    ></path>
                    <path
                      d="M34.752 409.664l35.648 356.48a250.88 250.88 0 0 0 499.2 0l35.648-356.48L320 352.64 34.752 409.6z"
                      fill="#EDA133"
                      p-id="4867"
                    ></path>
                    <path
                      d="M139.008 653.248a32 32 0 0 1 45.248 0l67.84 67.84L455.68 517.504a32 32 0 1 1 45.248 45.248l-203.648 203.584-22.592 22.656a32 32 0 0 1-45.248 0l-90.496-90.496a32 32 0 0 1 0-45.248z"
                      fill="#FFFFFF"
                      p-id="4868"
                    ></path>
                  </svg>
                  <pf-verify-button
                    ref="verifyBtnRef"
                    slot="back"
                    @click.native="openCaptcha"
                    @finish="getVerifyCode"
                  ></pf-verify-button>
                </mt-input>
              </mt-form-item>
              <mt-form-item prop="password" class="form-group input-item">
                <mt-input
                  ref="userPasswordRef"
                  v-model="accountForm.password"
                  type="password"
                  css-class="e-outline"
                  :placeholder="$t('请输入密码')"
                  @input="handlePswInput"
                  @keyup.enter="userPasswordKeyup"
                >
                  <!-- <img slot="preIcon" src="../../assets/images/icon_psw.png" /> -->
                  <svg
                    slot="preIcon"
                    t="*************"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="3768"
                    width="24"
                    height="24"
                  >
                    <path
                      d="M512 0a256 256 0 0 1 256 256v256H256V256a256 256 0 0 1 256-256z m0 128a128 128 0 0 0-127.68 118.4L384 256v128h256V256a128 128 0 0 0-118.4-127.68L512 128z"
                      fill="#EDA133"
                      p-id="3769"
                    ></path>
                    <path
                      d="M0 384m128 0l768 0q128 0 128 128l0 384q0 128-128 128l-768 0q-128 0-128-128l0-384q0-128 128-128Z"
                      fill="#6386C1"
                      p-id="3770"
                    ></path>
                    <path
                      d="M448 576m64 0l0 0q64 0 64 64l0 128q0 64-64 64l0 0q-64 0-64-64l0-128q0-64 64-64Z"
                      fill="#B0C1DF"
                      p-id="3771"
                    ></path>
                    <path
                      d="M768 384h128a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H256l512-640z"
                      fill="#000000"
                      fill-opacity=".1"
                      p-id="3772"
                    ></path>
                  </svg>
                </mt-input>

                <pwd-strength v-if="isShowPswLevel" :strengths="passwordStrength"></pwd-strength>
              </mt-form-item>
              <mt-form-item prop="confirmPassword" class="input-item">
                <mt-input
                  ref="confirmPswRef"
                  v-model="accountForm.confirmPassword"
                  type="password"
                  css-class="e-outline"
                  :placeholder="$t('请确认密码')"
                  @keyup.enter="register"
                >
                  <!-- <img slot="preIcon" src="../../assets/images/icon_psw.png" /> -->
                  <svg
                    slot="preIcon"
                    t="*************"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="3768"
                    width="24"
                    height="24"
                  >
                    <path
                      d="M512 0a256 256 0 0 1 256 256v256H256V256a256 256 0 0 1 256-256z m0 128a128 128 0 0 0-127.68 118.4L384 256v128h256V256a128 128 0 0 0-118.4-127.68L512 128z"
                      fill="#EDA133"
                      p-id="3769"
                    ></path>
                    <path
                      d="M0 384m128 0l768 0q128 0 128 128l0 384q0 128-128 128l-768 0q-128 0-128-128l0-384q0-128 128-128Z"
                      fill="#6386C1"
                      p-id="3770"
                    ></path>
                    <path
                      d="M448 576m64 0l0 0q64 0 64 64l0 128q0 64-64 64l0 0q-64 0-64-64l0-128q0-64 64-64Z"
                      fill="#B0C1DF"
                      p-id="3771"
                    ></path>
                    <path
                      d="M768 384h128a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H256l512-640z"
                      fill="#000000"
                      fill-opacity=".1"
                      p-id="3772"
                    ></path>
                  </svg>
                </mt-input>
              </mt-form-item>

              <div class="clause-box">
                <input type="checkbox" @click="handleCheckboxClick" />
                <div class="clause-content">
                  {{ $t('已阅读并同意') }}
                  <span style="color: #005ca9; cursor: pointer" @click="toClause"
                    >《{{ $t('条款') }}》</span
                  >
                </div>
                <div v-if="showClauseError" class="e-error custom-error">
                  {{ $t('同意相关条款才可登录') }}
                </div>
              </div>
            </mt-form>
            <button class="blur-btn" @click.prevent="register">{{ $t('注 册') }}</button>
            <span class="btn-back" @click="$router.push({ path: '/login' })">
              {{ $t('返回登录') }}
            </span>
          </template>
          <template v-if="stepIndex === 1">
            <div class="finish-img-box">
              <img src="../../assets/images/reset_success.png" />
            </div>
            <p class="finish-tips">{{ $t('恭喜！注册成功！') }}</p>
            <button class="blur-btn" @click="$router.push('/login')">{{ $t('立即登录') }}</button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import MtInput from '@/mtComponents/mtInput/index.vue'
// import PfHeader from '@/components/pfHeader.vue'
import PfStepBar from '@/components/pfStepBar.vue'
import PfVerifyButton from '@/components/pfVerifyButton.vue'
import PwdStrength from '@/components/pwdStrength.vue'
import { phoneRegex, pswRegex } from '@/utils/my.validators'
import { formatRules } from '@/utils/index'

@Component({
  components: {
    MtInput,
    // PfHeader,
    PfStepBar,
    PfVerifyButton,
    PwdStrength,
    PfLeftText: require('@/components/pfLeftText').default,
    changeLanguage: require('@/components/changeLanguage.vue').default
  },
  middleware: ['config', 'country'],
  async asyncData({ app, error }) {
    let validData = {}
    try {
      const validRs = await app.$api.register.registerValidate()
      if (validRs.code == 200) {
        validData = validRs.data ? formatRules(validRs.data) : {}
      }
    } catch (err) {
      error({ statusCode: 500 })
    }
    return {
      validData
    }
  }
})
export default class Register extends Vue {
  validateCheckPsw = (rule: object, value: string, callback: Function) => {
    rule
    const { password } = this.accountForm as any
    if (value === '') {
      callback(new Error(this.$t('请再次输入密码')))
    } else if (value !== password) {
      callback(new Error(this.$t('两次输入的密码不一致')))
    } else {
      callback()
    }
  }

  /** data -start */
  // stepList = ['账号注册', '注册成功']
  passwordStrength: number = 0 // 密码强度
  isShowPswLevel: boolean = false // 是否显示密码强度
  stepIndex: number = 0
  isChecked: boolean = false // 是否可以直接登录
  userNameRepeat: boolean = false // 用户名是否可用
  isUsernameChange: boolean = false
  showClauseError: boolean = false // 是否显示条款的错误
  countryRegionParam: object = {
    countryRegionId: 1,
    countryRegionCode: '0086',
    countryRegionName: '中国 0086'
  }

  accountForm: object = {
    phone: '',
    username: '',
    nickName: '',
    password: '',
    confirmPassword: '',
    countryRegionId: '',
    verificationCode: ''
  }

  validData: object = {}
  ruleCN = { pattern: phoneRegex, message: this.$t('手机号格式不正确'), trigger: 'blur' }
  rules: object = {
    phone: [
      { required: true, message: this.$t('手机号码不能为空'), trigger: 'blur' },
      { ...this.ruleCN }
    ],
    username: [
      { required: true, message: this.$t('用户名不能为空'), trigger: 'blur' },
      {
        pattern: /^(?!\d+$)[\dA-Za-z]{3,10}$/,
        message: '用户名必须为3-10位数字或字母',
        trigger: 'blur'
      }
      // { pattern: /^(?!\\d+$)[\\dA-Za-z]{3,10}$/, message: '用户名必须为3-10位数字或字母', trigger: 'blur' }
    ],
    password: [
      { required: true, message: this.$t('密码不能为空'), trigger: 'blur' },
      { pattern: pswRegex, message: this.$t('密码必须为6-20位数字/字母/特殊字符'), trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: this.$t('密码不能为空'), trigger: 'blur' },
      { validator: this.validateCheckPsw, trigger: 'blur' }
    ],
    verificationCode: [{ required: true, message: this.$t('必填'), trigger: 'blur' }],
    countryRegionId: [{ required: true, message: this.$t('必填'), trigger: 'blur' }]
  }
  /** data -end */

  get formRef() {
    return this.$refs.accountForm && (this.$refs.accountForm as any)
  }

  @Watch('accountForm.username')
  onaccountName() {
    this.userNameRepeat = false
    this.isUsernameChange = true
  }

  created() {
    this.validData['confirmPassword'] = [
      { required: true, message: this.$t('密码不能为空'), trigger: 'blur' },
      { validator: this.validateCheckPsw, trigger: 'blur' }
    ]
  }

  mounted() {
    //  this.$tips.show({
    //     title: '标题',
    //     content: '内容内容内容内容内容内容内容内容内容内容内容内容',
    //     icon: 'e-warning toast-icons'
    //   })
  }

  // 手机号选择区号
  handleSelect(event: any): void {
    const { itemData } = event || {}
    const { phone } = this.rules as any
    if (itemData.code === '0086') {
      phone[1] = this.ruleCN
    } else {
      const { length, maxLength, minLength } = itemData
      let ruleForeign =
        maxLength && minLength
          ? {
              max: maxLength,
              min: minLength,
              message: this.$t('手机号格式不正确'),
              trigger: 'submit'
            }
          : length
          ? { len: length, message: this.$t('手机号格式不正确'), trigger: 'submit' }
          : {}
      phone[1] = ruleForeign
    }
    const { phone: phoneNumber } = this.accountForm as any
    phoneNumber && this.formRef.validateField('phone')
  }

  openCaptcha() {
    this.formRef.validateField('phone', (valid: boolean) => {
      if (valid) {
        ;(this.$refs.verifyBtnRef as any).openCaptcha()
      }
    })
  }

  async getVerifyCode(res: any) {
    const { phone } = this.accountForm as any
    let params = {
      phone,
      rand: res.randstr,
      ticket: res.ticket,
      ...this.countryRegionParam
    }
    const { code, data } = await this.$api.register.getVerifyCode(params)
    if (code == 200) {
      ;(this.$refs.verifyBtnRef as any).countDown()
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.formRef.addErrorLabels(data.errorLabels)
      }
    }
  }

  register() {
    this.formRef.validate(async (valid: boolean) => {
      if (valid) {
        if (this.isChecked === false) {
          this.showClauseError = true
          return false
        }
        if (this.userNameRepeat) {
          return false
        }
        let params: object = {
          ...this.accountForm,
          passwordStrength: this.passwordStrength, // 密码强度(0低;1中;2高)
          ...this.countryRegionParam
        }
        this.$load.show()
        const { code, data } = await this.$api.register.register(params)
        if (code == 200) {
          this.stepIndex = 1
          this.$load.hide()
        } else {
          this.$load.hide()
          if (data && data.errorLabels && data.errorLabels.length) {
            this.formRef.addErrorLabels(data.errorLabels)
          }
        }
      }
    })
  }

  toLogin() {
    this.$router.push({ path: '/login' })
  }

  toClause() {
    const { href } = this.$router.resolve({ path: '/clause' })
    window.open(href, '_blank')
  }

  handlePswInput(val: any) {
    if (!val) {
      this.passwordStrength = 0
      this.isShowPswLevel = false
      return
    }
    if (val.trim().length < 6) {
      this.passwordStrength = 0
      this.isShowPswLevel = false
      return
    }
    this.isShowPswLevel = true
    const high = /^.*(?=.{6,})(?=.*\d)(?=.*[A-Za-z])(?=.*[_!@#$%^&*`~()-+=]).*$/
    const middle1 = /^.*(?=.{6,})(?=.*\d)(?=.*[A-Za-z]).*$/
    const middle2 = /^.*(?=.{6,})(?=.*\d)(?=.*[_!@#$%^&*`~()-+=]).*$/
    const middle3 = /^.*(?=.{6,})(?=.*[A-Za-z])(?=.*[_!@#$%^&*`~()-+=]).*$/
    if (val) {
      if (high.test(val)) {
        this.passwordStrength = 2
      } else if (middle1.test(val) || middle2.test(val) || middle3.test(val)) {
        this.passwordStrength = 1
      } else {
        this.passwordStrength = 0
      }
    }
  }

  handleCheckboxClick(event: any) {
    this.isChecked = event.target.checked
    this.isChecked && (this.showClauseError = false)
  }

  handleUserNameBlur(val: any) {
    if (!this.isUsernameChange || !val || !val.trim()) return false
    this.formRef.validateField('username', async (valid: boolean) => {
      if (valid) {
        this.isUsernameChange = false
        this.$load.show()
        const { code, data } = await this.$api.register.checkAccountName({ username: val })
        if (code == 200) {
          this.userNameRepeat = data === 1 // 0否1是
          this.$load.hide()
        } else {
          this.userNameRepeat = false // 0否1是
          this.$load.hide()
        }
      }
    })
  }

  phoneNumberKeyup() {
    this.$refs.verifyCodeRef && (this.$refs.verifyCodeRef as any).focusIn()
  }

  verifyCodeKeyup() {
    this.$refs.accountNameRef && (this.$refs.accountNameRef as any).focusIn()
  }

  accountNameKeyup() {
    this.$refs.userPasswordRef && (this.$refs.userPasswordRef as any).focusIn()
  }

  userPasswordKeyup() {
    this.$refs.confirmPswRef && (this.$refs.confirmPswRef as any).focusIn()
  }
  /** method --end */
}
</script>

<style lang="less" scoped>
.pf-register {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.register-body {
  width: 550px;
  // height: 620px;
  // margin: 0 auto;
  margin-right: 75px;
  background-color: #fff;
  padding: 20px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  position: relative;

  .form-content {
    height: 100%;
    padding: 40px;
    background: #fff;
  }
  .titles {
    font-size: 18px;
    color: #003c86;
    text-align: center;
    font-weight: bold;
  }

  .form-box {
    // width: 450px;
    margin: 30px auto 0 auto;
    text-align: center;
    // overflow: hidden;
    .input-item {
      margin-bottom: 20px;
    }
    .form-group {
      position: relative;
      align-items: center;
    }
    .username-error {
      position: absolute;
      left: 0;
      bottom: -18px;
      // display: inline-block;
      // position: absolute;
      // top: 50%;
      // transform: translateY(-50%);
      // min-width: 150px;
      // margin-left: 8px;
      // text-align: left;
    }
  }
  .clause-box {
    display: flex;
    align-items: center;
    position: relative;
    .clause-content {
      font-size: 14px;
      color: #999;
      margin-left: 5px;
    }
    .custom-error {
      position: absolute;
      top: 20px;
      left: 0;
    }
  }
  .btn-back {
    display: inline-block;
    font-size: 14px;
    color: #999;
    cursor: pointer;
    margin-top: 20px;
  }
  .finish-img-box {
    width: 200px;
    height: 160px;
    margin: 50px auto 30px auto;
    > img {
      width: 100%;
      height: 100%;
    }
  }
  .finish-tips {
    font-size: 16px;
    color: #005da9;
    margin-bottom: 120px;
  }
}
.blur-btn {
  cursor: pointer;
  text-align: center;
  background-color: #005ca9;
  color: #fff;
  padding: 0;
  border: none;
  width: 100%;
  font-size: 18px;
  color: #fff;
  margin-top: 20px;
  padding: 12px 0;
}
</style>
