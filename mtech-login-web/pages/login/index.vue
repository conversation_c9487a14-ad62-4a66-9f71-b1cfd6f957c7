<template>
  <div class="login-container">
    <div class="logo">
      <img src="~assets/images/logo_tcl.png" alt="" />
    </div>
    <!-- <pf-header title="用户登录"></pf-header> -->
    <!-- <mt-date-time-picker :width="300" :placeholder="$t('选择日期和时间')"></mt-date-time-picker> -->
    <!-- <pf-left-text></pf-left-text> -->
    <div class="lang-container">
      <mt-select
        :data-source="langList"
        width="120"
        popup-width="150px"
        float-label-type="Never"
        :value="langVal"
        :fields="{ text: 'name', value: 'code' }"
        @change="langChange"
      ></mt-select>
    </div>
    <div class="login-form">
      <!-- <change-language></change-language> -->
      <!-- <div class="login-choose">
        <div class="right">
          <div :class="chooseActive == 0 ? 'choose-active' : ''" @click="chooseActive = 0">{{$t('平台账号')}}</div>
          <div :class="chooseActive == 1 ? 'choose-active' : ''" @click="chooseActive = 1">{{$t('企业账号')}}</div>
        </div>
      </div> -->
      <div class="login-title">
        <img src="~assets/images/icon-title.png" alt="" />
        <span>供应商关系管理系统(SRM)</span>
      </div>
      <!-- <div class="login-title">
        <div :class="['login-title-item', loginType === 0 && 'title-actived']" @click="loginType = 0">
          <span> {{$t('账号密码登录')}} </span>
          <div :class="['line', loginType === 0 && 'line-actived']"></div>
        </div>
        <div :class="['login-title-item', loginType === 1 && 'title-actived']" @click="loginType = 1">
          <span>{{$t('手机验证码登录')}}</span>
          <div :class="['line', loginType === 1 && 'line-actived']"></div>
        </div>
      </div> -->
      <mt-form
        v-show="!loginType"
        ref="accountForm"
        :model="accountForm"
        :rules="validateByName"
        class="form-box"
      >
        <mt-form-item
          prop="username"
          :label="$t('账号')"
          :show-required-icon="false"
          class="input-item"
        >
          <mt-input
            ref="usernameRef"
            key="account-name"
            :show-clear-button="true"
            v-model="accountForm.username"
            type="text"
            cssClass="e-outline"
            :placeholder="$t('请输入用户名/邮箱/手机号')"
            @keyup.enter="usernameKeyup"
          >
          </mt-input>
        </mt-form-item>
        <mt-form-item
          prop="password"
          :label="$t('密码')"
          :show-required-icon="false"
          class="input-item"
        >
          <mt-input
            ref="accountPwdRef"
            key="account-psw"
            :showClearButton="true"
            v-model="accountForm.password"
            type="password"
            cssClass="e-outline"
            :placeholder="$t('请输入密码')"
            @keyup.enter="loginSubmit(0)"
          >
          </mt-input>
        </mt-form-item>
      </mt-form>
      <div class="other-tips">
        <div class="check-box" @click="handleCheckbox">
          <input ref="checkbox" type="checkbox" class="checkbox" v-model="isRemember" />
          <span>{{ $t('记住密码') }}</span>
        </div>
        <span
          v-if="loginType === 0"
          class="to-path forget-pwd"
          @click="$router.push({ path: '/resetPassword' })"
        >
          {{ $t('忘记密码') }} ?</span
        >
      </div>
      <button class="login-btn" @click="loginSubmit(0)">
        {{ $t('登录') }}
      </button>
      <!-- <div class="to-path to-register" @click="$router.push({ path: '/register-flow' })"> {{$t('没有账户')}} ?<span> {{$t('立即注册')}} </span></div> -->
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { useTCaptcha } from '@/utils/index'
import MtInput from '@/mtComponents/mtInput/index.vue'
// import MtButton from '@/mtComponents/mtButton/index.vue'
// import PfHeader from '@/components/pfHeader.vue'
import PfVerifyButton from '@/components/pfVerifyButton.vue'

@Component({
  components: {
    MtInput,
    // MtButton,
    // PfHeader,
    PfVerifyButton,
    PfLeftText: require('@/components/pfLeftText').default,
    changeLanguage: require('@/components/changeLanguage.vue').default
  },
  middleware: ['config']
  // async asyncData({ app, error }) {
  // let validateByName = {}, validateByPhone = {}
  // try{
  //   const byUserRes = await app.$api.login.unifyLoginByusername();
  //   if(byUserRes.code == 200 && byUserRes.data) {
  //     validateByName = byUserRes.data ? formatRules(byUserRes.data, false) : {}
  //   }
  //   const byPhoneRes = await app.$api.login.unifyLoginByPhone();
  //   if(byPhoneRes.code == 200 && byPhoneRes.data) {
  //     validateByPhone = byPhoneRes.data ? formatRules(byPhoneRes.data, false) : {}
  //   }
  // } catch(err) {
  //   error({ statusCode: 500 })
  // }
  // return {
  //   validateByName, validateByPhone
  // }
  // }
})
export default class Login extends Vue {
  username: string = ''
  accountPwd: string = ''
  phoneNumber: number | null = null
  verifyCode: number | null = null
  isRemember: boolean = false
  loginType: number = 0 // 0 账户，1短信，2微信
  application: object = {}
  chooseActive: number = 0
  countryRegionParam: object = {
    countryRegionId: 1,
    countryRegionCode: '0086'
  }
  ruleCN = {
    pattern: /^1[3-9]\d{9}$/,
    message: this.$t('手机号格式不正确'),
    trigger: 'submit'
  }
  rules: object = {
    enterprise_code: [{ required: true, message: this.$t('必填'), trigger: 'submit' }],
    username: [{ required: true, message: this.$t('必填'), trigger: 'submit' }],
    password: [{ required: true, message: this.$t('必填'), trigger: 'submit' }],
    phone: [{ required: true, message: this.$t('必填'), trigger: 'submit' }, { ...this.ruleCN }],
    validateCode: [{ required: true, message: this.$t('必填'), trigger: 'submit' }]
  }

  accountForm: object = {
    username: '',
    password: '',
    enterprise_code: ''
  }
  validateByName: object = {}

  phoneForm: object = {
    enterprise_code: '',
    phone: '',
    validateCode: ''
  }

  validateByPhone: object = {}

  langList: any[] = []

  langVal: string = window.localStorage.getItem('internationlization') ?? 'zh'

  @Watch('loginType')
  onLoginType() {
    this.formRef.resetFields()
  }

  get formRef() {
    const name = this.loginType ? 'phone' : 'account'
    return this.$refs[`${name}Form`] && (this.$refs[`${name}Form`] as any)
  }

  /** 生命周期 */
  async mounted() {
    const _this: any = this
    const hasAuth = (this as any).$hasAuth()
    if (hasAuth) {
      await this.isClearCoolie()
    }
    _this.$clearParentAuth()
    this.getLanguage()
    // this.isClearCallBack()
    this.application = this.$store.state.application || {}
    if (window.localStorage.getItem('userLoginInfo')) {
      let info = window.localStorage.getItem('userLoginInfo') || ''
      this.chooseActive = JSON.parse(info).chooseActive
      this.isRemember = JSON.parse(info).isRemember
      this.loginType = JSON.parse(info).loginType
      this.$set(this.accountForm, 'enterprise_code', JSON.parse(info).enterprise_code)
      if (this.isRemember) {
        this.$set(this.accountForm, 'username', JSON.parse(info).username)
        this.$set(this.accountForm, 'password', JSON.parse(info).password)
      } else {
        this.$set(this.accountForm, 'username', '')
        this.$set(this.accountForm, 'password', '')
      }
    }
  }

  /** 方法 --start */
  // 手机号选择区号
  handleSelect(event: any): void {
    const { itemData } = event || {}
    const { phone } = this.validateByPhone as any
    if (itemData.code === '0086') {
      phone[1] = this.ruleCN
    } else {
      const { length, maxLength, minLength } = itemData
      let ruleForeign =
        maxLength && minLength
          ? {
              max: maxLength,
              min: minLength,
              message: this.$t('手机号格式不正确'),
              trigger: 'submit'
            }
          : length
          ? {
              len: length,
              message: this.$t('手机号格式不正确'),
              trigger: 'submit'
            }
          : {}
      phone[1] = ruleForeign
    }
    const { phone: phoneNumber } = this.phoneForm as any
    phoneNumber && this.formRef.validateField('phone')
  }

  handleCheckbox(event: any) {
    if (event.target.checked === undefined) {
      this.isRemember = !this.isRemember
      const checkbox = this.$refs.checkbox
      ;(checkbox as any).checked = this.isRemember
    } else {
      this.isRemember = event.target.checked
    }
  }

  // 发短信开启防水墙
  openCaptcha() {
    this.formRef.validateField('phone', (valid: boolean) => {
      if (valid) {
        ;(this.$refs.verifyBtnRef as any).openCaptcha()
      }
    })
  }

  // 短信登录获取验证码
  async getVerifyCode(res: any) {
    this.formRef.validateField('phone', (valid: boolean) => {
      if (!valid) {
        return
      }
    })
    const { phone } = this.phoneForm as any
    let params = {
      mobile: phone || '',
      rand: res.randstr,
      ticket: res.ticket
    }
    const { code, data } = await this.$api.login.loginVerifyCode(params)
    if (code == 200) {
      if (this.loginType === 1) {
        ;(this.$refs.verifyBtnRef as any).countDown()
      }
    } else if (code == 601) {
      this.openCaptcha()
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.formRef.addErrorLabels(data.errorLabels)
      }
    }
  }

  loginSubmit(data = 0) {
    this.formRef.validate(async (valid: boolean) => {
      if (valid) {
        // let params: object = { username: this.accountForm['username'] }
        // if (this.loginType === 1) {
        //  params = { phone: this.phoneNumber || '' }
        // }
        // const { code, data } = await this.$api.login.ifOpenCaptcha(params)
        const { code } = { code: 200 }
        if (code == 200) {
          let loginParams: object = {
            remember: this.isRemember,
            rand: '',
            ticket: ''
          }
          if (this.loginType === 0) {
            let { username, password, enterprise_code } = this.accountForm as any
            if (this.chooseActive == 0) {
              enterprise_code = '0'
            }
            loginParams = Object.assign(loginParams, {
              username: username,
              password: (this as any).$$encrypt(password),
              enterprise_code
            })
          } else {
            const { phone, validateCode, enterprise_code } = this.phoneForm as any
            if (this.chooseActive == 0) {
              loginParams = Object.assign(loginParams, {
                mobile: phone,
                validateCode: validateCode,
                enterprise_code: '0'
              })
            } else {
              loginParams = Object.assign(loginParams, {
                mobile: phone,
                validateCode: validateCode,
                enterprise_code: enterprise_code
              })
            }
          }
          if (data == 0) {
            this.loginApi(loginParams)
          }
          if (data == 1) {
            useTCaptcha().then((resCaptcha: any) => {
              ;(loginParams as any).rand = resCaptcha.randstr
              ;(loginParams as any).ticket = resCaptcha.ticket
              this.loginApi(loginParams)
            })
          }
        } else {
          // if (data && data.errorLabels && data.errorLabels.length) {
          //   this.formRef.addErrorLabels(data.errorLabels)
          // }
        }
      }
    })
  }

  async loginApi(params: object) {
    if (this.loginType === 0) {
      var { code, data } = await this.$api.login.login(params)
    } else {
      var { code, data } = await this.$api.login.loginVerify(params)
    }
    if (code == 200) {
      const { enterprise_code, username, password } = this.accountForm as any
      window.localStorage.setItem(
        'userLoginInfo',
        JSON.stringify({
          chooseActive: this.chooseActive,
          loginType: this.loginType,
          enterprise_code,
          isRemember: this.isRemember,
          username,
          password
        })
      )
      this.redirectUrl(data)
    } else if (code == 601) {
      this.loginSubmit(1)
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.formRef.addErrorLabels(data.errorLabels)
      }
    }
  }

  async redirectUrl(data: any) {
    let href = ''
    href = data?.callbackUrl
    if (location.hostname.indexOf('localhost') > -1) {
      href = 'http://localhost:9012'
    }
    sessionStorage.removeItem('currentSideMenu')
    window.location.href = href
  }

  phoneNumberKeyup() {
    this.$refs.verifyCodeRef && (this.$refs.verifyCodeRef as any).focusIn()
  }

  usernameKeyup() {
    this.$refs.accountPwdRef && (this.$refs.accountPwdRef as any).focusIn()
  }

  comNameKeyup() {
    this.$refs.usernameRef && (this.$refs.usernameRef as any).focusIn()
  }

  // getUserInfo() {}
  async isClearCoolie() {
    await this.$api.login.isClearToken()
  }

  async getLanguage() {
    const { code, data } = await this.$api.login.getLanguage()
    if (code === 200) {
      this.langList = data
    }
  }

  langChange(e: any) {
    if (e.isInteracted) {
      const lang = e.value
      window.localStorage.setItem('internationlization', lang)
      location.reload()
    }
  }
  /** method --end */
}
</script>

<style lang="less" scoped>
.login-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .lang-container {
    position: absolute;
    top: 40px;
    right: 3%;
    .select-container {
      ::v-deep .e-control-wrapper {
        border: 0;
        input {
          text-align: right;
          padding-right: 8px;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
  .logo {
    position: absolute;
    top: 40px;
    left: 80px;
    img {
      height: 32px;
    }
  }
}
.login-form {
  // width: 480px;
  box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.07);
  border-radius: 8px;
  width: 27%;
  min-width: 440px;
  background: #fff;
  padding: 42px;
  position: absolute;
  right: 3%;
  .login-choose {
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-bottom: 50px;
    flex-direction: column;
    .left {
      width: 98px;
      height: 14px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
    }
    .right {
      display: flex;
      border-radius: 0 2px 2px 0;
      div {
        width: 120px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        border: 1px solid rgba(0, 70, 156, 1);
      }
      .choose-active {
        background: rgba(0, 70, 156, 1);
        color: #ffffff;
      }
    }
  }
  // .login-title {
  //   display: flex;
  //   padding: 0;
  //   margin-bottom: 18px;
  //   border-bottom: 2px solid #D8D8D8;
  //   .login-title-item {
  //     width: 40%;
  //     font-size: 18px;
  //     color: #999;
  //     text-align: center;
  //     cursor: pointer;
  //     position: relative;

  //     &:first-child {
  //       margin-right: 20%;
  //     }

  //     .line {
  //       width: 100%;
  //       height: 4px;
  //       background: #fff;
  //       border-radius: 1px;
  //       margin: 8px auto auto;
  //     }
  //     .line-actived {
  //       background: #6386C1;
  //       position: absolute;
  //       bottom: -3px;
  //     }
  //   }
  //   .title-actived {
  //     color: #00469C;
  //   }
  // }

  .other-tips {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666666;
    padding: 0 10px;
    // margin-top: 10px;

    .forget-pwd {
      color: #666666;
      cursor: pointer;
    }
  }
  .check-box {
    font-size: 14px;
    color: #292929;
    display: flex;
    align-items: center;
    cursor: pointer;
    > span {
      margin-left: 5px;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .checkbox:checked {
      background: #00469c;
    }
    .checkbox {
      width: 15px;
      height: 15px;
      background-color: #ffffff;
      border: solid 2px #4e5a70;
      -webkit-border-radius: 50%;
      border-radius: 50%;
      font-size: 0.8rem;
      margin: 0;
      padding: 0;
      position: relative;
      display: inline-block;
      vertical-align: top;
      cursor: default;
      -webkit-appearance: none;
      -webkit-user-select: none;
      user-select: none;
      -webkit-transition: background-color ease 0.1s;
      transition: background-color ease 0.1s;
    }
    .checkbox:checked::after {
      content: '';
      top: 2px;
      left: 2px;
      position: absolute;
      background: transparent;
      border: #fff solid 2px;
      border-top: none;
      border-right: none;
      height: 5px;
      width: 8px;
      -moz-transform: rotate(-45deg);
      -ms-transform: rotate(-45deg);
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
    }
  }
  .to-register {
    margin-top: 15px;
    text-align: center;
    font-size: 14px;
    span {
      color: #00469c;
      cursor: pointer;
    }
  }
}

.form-box {
  display: flex;
  flex-direction: column;
  .input-item {
    margin-bottom: 16px;
  }
  .verify-wrap {
    display: flex;
    justify-content: space-between;
    .verify-code-btn {
      border: 1px solid #ccc;
      color: #606266;
      border-radius: 3px;
      background: #f5f5f5;
      height: 40px;
      font-size: 14px;
      padding: 0 15px;
      cursor: pointer;
    }
  }
}

.login-btn {
  cursor: pointer;
  text-align: center;
  background-color: #4e5a70;
  color: #fff;
  padding: 0;
  border: none;
  width: 100%;
  font-size: 18px;
  color: #fff;
  margin-top: 24px;
  padding: 12px 0;
  letter-spacing: 20px;
  border-radius: 4px;
}
.login-title {
  font-size: 24px;
  color: #4e5a70;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  span {
    padding-left: 20px;
    position: relative;
    margin-left: 20px;
    &::after {
      content: '';
      width: 1px;
      height: 24px;
      opacity: 0.2;
      background: #4a556b;
      position: absolute;
      left: 0;
      top: 5px;
    }
  }
}
.input__wrap {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-top: 4px;
}
.login-form .check-box .checkbox:checked {
  background: #4e5a70;
  border: solid 2px #4e5a70;
}
.dialog {
  z-index: 1001;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  .noticeMain {
    width: 1000px;
    height: 476px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border-radius: 5px;
    .main-text {
      position: relative;
      padding: 0;
    }
    img {
      width: 100%;
      height: 100%;
    }
    i {
      position: absolute;
      display: inline-block;
      width: 28px;
      height: 28px;
      background: url('~assets/images/close.png');
      background-size: 28px 28px;
      top: 12px;
      right: 12px;
      cursor: pointer;
    }
  }
}
</style>
