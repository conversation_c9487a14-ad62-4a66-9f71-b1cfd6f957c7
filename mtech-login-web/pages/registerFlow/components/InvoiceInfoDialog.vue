<template>
  <mt-dialog
    ref="dialog"
    :header="header"
    class="invoice-info--dialog"
    :buttons="buttons"
    @close="hide"
  >
    <mt-form ref="form" class="dialog-form" :model="formData" :rules="formRules">
      <mt-form-item :label="$t('企业名称')" prop="invoiceTitle">
        <mt-input type="text" v-model="formData.invoiceTitle"></mt-input>
      </mt-form-item>
      <mt-form-item :label="$t('税号')" prop="taxNo">
        <mt-input type="text" v-model="formData.taxNo"></mt-input>
      </mt-form-item>
      <mt-form-item :label="$t('开户银行')" prop="bankName">
        <mt-input type="text" v-model="formData.bankName"></mt-input>
      </mt-form-item>
      <mt-form-item :label="$t('银行账号')" prop="bankAccount">
        <mt-input type="text" v-model="formData.bankAccount"></mt-input>
      </mt-form-item>
      <mt-form-item :label="$t('电话号码')" prop="phoneNo">
        <mt-input type="text" v-model="formData.phoneNo"></mt-input>
      </mt-form-item>
      <mt-form-item :label="$t('注册地址')" prop="registerAddress">
        <address-select :address="addressData" @change="changeRegisterAddress" />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import AddressSelect from './AddressSelect.vue'
import { phoneRegex } from '@/utils/my.validators'
// import login from '~/api/login'

@Component({
  layout: 'blank',
  components: {
    AddressSelect
  }
})
export default class InvoiceInfoDialog extends Vue {
  @Prop()
  value!: boolean

  @Prop()
  data!: any

  get header() {
    if (this.data && Object.keys(this.data).length) {
      return this.$t('编辑')
    }
    return this.$t('新增')
  }

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: this.$t('取消') }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: this.$t('确定') }
    }
  ]

  addressData: any = {}

  formData: any = {
    registerAddress: '',
    registerAddressCountryCode: 'CHN',
    registerAddressCountryName: '中国 内地'
  }

  formRules: any = {
    invoiceTitle: [{ required: true, message: this.$t('请输入企业名称'), trigger: 'blur' }],
    taxNo: [{ required: true, message: this.$t('请输入税号'), trigger: 'blur' }],
    registerAddress: [{ required: true, validator: this.validateRegisterAddress, trigger: 'blur' }],
    bankName: [{ required: true, message: this.$t('请输入开户银行'), trigger: 'blur' }],
    bankAccount: [{ required: true, message: this.$t('请输入银行账号'), trigger: 'blur' }],
    phoneNo: [{ required: true, validator: this.validatePhoneNo, trigger: 'blur' }]
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  @Watch('visible')
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    }
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
    this.resetFormData()
  }

  show() {
    const ref: any = this.$refs.dialog
    this.formData = Object.assign({}, this.data || {})
    this.setAddressData()

    ref.ejsRef.show()
  }

  changeRegisterAddress(formData: any) {
    const registerAddress =
      formData.countryName + formData.provinceName + formData.cityName + formData.addressDetail
    this.$set(this.formData, 'registerAddress', registerAddress)
    this.formData.registerAddressCountryCode = formData.countryCode
    this.formData.registerAddressProvinceCode = formData.provinceCode
    this.formData.registerAddressCityCode = formData.cityCode
    this.formData.registerAddressCountryName = formData.countryName
    this.formData.registerAddressProvinceName = formData.provinceName
    this.formData.registerAddressCityName = formData.cityName
    this.formData.registerAddressDetail = formData.addressDetail
  }

  validatePhoneNo(rule: any, value: any, callback: any) {
    if (!phoneRegex.test(value)) {
      callback(new Error(this.$t('电话号码格式不正确')))
    } else {
      callback()
    }
  }

  validateRegisterAddress(rule: any, value: any, callback: any) {
    const {
      registerAddressCountryCode,
      registerAddressProvinceCode,
      registerAddressCityCode,
      registerAddressDetail
    } = this.formData
    if (
      registerAddressCountryCode &&
      registerAddressProvinceCode &&
      registerAddressCityCode &&
      registerAddressDetail
    ) {
      callback()
    } else {
      callback(new Error(this.$t('请输入完整的注册地址')))
    }
  }

  private handleConfirm() {
    ;(this.$refs.form as any).validate((valid: boolean) => {
      if (valid) {
        if (!this.formData.primaryId) {
          this.formData.primaryId = Math.random() + '' + Math.random()
        }
        this.$emit('save', this.formData)
        this.hide()
      }
    })
  }

  private resetFormData() {
    this.formData = {}
  }

  private setAddressData() {
    this.addressData = {
      countryCode: this.formData.registerAddressCountryCode,
      provinceCode: this.formData.registerAddressProvinceCode,
      cityCode: this.formData.registerAddressCityCode,
      countryName: this.formData.registerAddressCountryName,
      provinceName: this.formData.registerAddressProvinceName,
      cityName: this.formData.registerAddressCityName,
      addressDetail: this.formData.registerAddressDetail
    }
  }
}
</script>

<style lang="less" scoped>
.dialog-form {
  padding: 40px 22px;
}
</style>
