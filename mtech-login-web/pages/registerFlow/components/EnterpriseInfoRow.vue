<template>
  <tr class="enterprise-info--row">
    <td class="file-row file-type--row">
      <p class="title">
        {{ data.fileType === 1 ? $t('资质文件') : $t('企业授权函') }}
      </p>

      <p v-show="isShowError" class="tile-error--tip">
        {{ $t('请上传') }}{{ data.fileType === 1 ? $t('资质文件') : $t('企业授权函') }}
      </p>
    </td>

    <td class="file-row file-id--row">
      <Uploader
        size="small"
        :isLogo="false"
        :preview="false"
        :exts="extList"
        @success="saveFileId"
        @delete="deleteFileId"
      >
        <div class="uploader-text--tip">
          <div>{{ $t('点击上传') }}</div>
          <div class="uploader-text--warn">{{ $t('文件最大不可超过5M') }}</div>
        </div>
      </Uploader>
    </td>

    <td class="file-row file-template--row">
      <a :href="qualificationUrl" target="_blank" v-if="data.fileType === 1">
        <mt-button cssClass="e-flat" :isPrimary="true">{{ $t('示例查看') }}</mt-button>
      </a>
      <mt-button
        v-if="data.fileType === 2"
        cssClass="e-flat"
        :isPrimary="true"
        @click="downloadFile"
        >{{ $t('下载') }}</mt-button
      >
    </td>

    <td class="file-row file-rule--row">
      {{ data.fileRule }}
    </td>
  </tr>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Uploader from './uploader.vue'
import bus from '../company/stepTwo/config/eventBus'

@Component({
  layout: 'blank',
  components: {
    Uploader
  }
})
export default class EnterpriseInfoRow extends Vue {
  data: any = {}
  isShowError = false

  qualificationUrl = `${location.origin}/enterprise_qualification.jpg`

  get extList() {
    let arr: string[] = []
    if (this.data.fileType === 1) {
      arr = ['.jpg', '.bmp', '.jpeg', '.png', '.webp']
    } else if (this.data.fileType === 2) {
      arr = []
    }
    return arr
  }

  mounted() {
    bus.$on('EnterpriseInfoRow-Error', this.showErrorTip)
  }

  saveFileId(id: string) {
    this.isShowError = false
    bus.$emit('EnterpriseInfoRow-Save', {
      id,
      index: this.data.index
    })
  }

  deleteFileId() {
    bus.$emit('EnterpriseInfoRow-Delete', {
      index: this.data.index
    })
  }

  downloadFile() {
    this.fileLinkToStreamDownload(
      `${location.origin}/enterprise_auth.docx`,
      '企业授权函_模板.docx',
      'msword'
    )
  }

  private showErrorTip(data: any) {
    if (data.index === this.data.index) {
      this.isShowError = !data.fileId
    }
  }

  private fileLinkToStreamDownload(url: string, fileName: string, type: string) {
    let reg =
      /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/ // eslint-disable-line
    if (!reg.test(url)) {
      throw new Error(this.$t('传入参数不合法,不是标准的文件链接'))
    } else {
      let xhr = new XMLHttpRequest()
      xhr.open('get', url, true)
      xhr.setRequestHeader('contentType', `application/${type}`)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status == 200) {
          //接受二进制文件流
          var blob = xhr.response
          this.downloadExportFile(blob, fileName)
        }
      }
      xhr.send()
    }
  }

  private downloadExportFile(blob: string, tagFileName: string) {
    let downloadElement = document.createElement('a')
    let href = blob
    if (typeof blob === 'string') {
      downloadElement.target = '_blank'
    } else {
      href = window.URL.createObjectURL(blob) //创建下载的链接
    }

    downloadElement.href = href
    downloadElement.download = tagFileName //下载后文件名
    const event = new MouseEvent('click') // 创建一个单击事件
    downloadElement.dispatchEvent(event) // 触发a的单击事件
    if (typeof blob != 'string') {
      window.URL.revokeObjectURL(href) //释放掉blob对象
    }
  }
}
</script>

<style lang="less" scoped>
.file-row {
  padding: 10px;
}
.file-type--row {
  .title {
    &::before {
      content: '*';
      color: #e50011;
    }
  }
  .tile-error--tip {
    color: #e50011;
  }
}
.uploader-text--tip {
  line-height: 24px;
  text-align: center;

  .uploader-text--warn {
    color: #cf3838;
  }
}
</style>
