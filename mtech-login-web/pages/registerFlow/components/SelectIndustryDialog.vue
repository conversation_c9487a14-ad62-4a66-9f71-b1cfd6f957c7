<template>
  <mt-dialog
    ref="dialog"
    :header="$t('选择所属行业')"
    class="select-industry--dialog"
    :buttons="buttons"
    @close="hide"
  >
    <div class="search--wrap">
      <label class=""> {{ $t('选择所属行业：') }}</label>
      <mt-input
        ref="input"
        class="search-input"
        cssClass="e-outline"
        v-model="searchText"
        :placeholder="$t('请输入关键字进行过滤')"
        @keyup.enter.native="searchTextByEnter"
        @change="searchTextFromTree"
      />
    </div>

    <div>
      <mt-treeView
        v-if="sourceTreeData.dataSource.length"
        class="tree-view--template"
        ref="treeView"
        :fields="sourceTreeData"
        :auto-check="true"
        :show-check-box="true"
      ></mt-treeView>
    </div>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'

@Component({
  layout: 'blank',
  components: {}
})
export default class SelectIndustryDialog extends Vue {
  @Prop()
  value!: boolean

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: this.$t('取消') }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: this.$t('确定') }
    }
  ]

  searchIndustryList: any[] = [] // 查询出来的地区列表
  allIndustryData: any[] = [] // 完整的地区列表

  sourceTreeData: any = {
    dataSource: [],
    id: 'itemCode',
    text: 'name',
    child: 'children'
  }
  searchText = null

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  @Watch('visible')
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    }
  }

  mounted() {
    this.getIndustryData()
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()
  }

  searchTextByEnter() {
    ;(this.$refs.input as any).ejsInstances.focusOut()
  }

  searchTextFromTree() {
    const ref = this.$refs.treeView as any
    ref.ejsInstances.removeNodes(
      [...this.allIndustryData, ...this.searchIndustryList].map((v: any) => v.itemCode)
    )

    if (this.searchText) {
      this.$api.common
        .getDictItemTree({
          dictCode: 'Industry',
          nameLike: this.searchText
        })
        .then((res: any) => {
          if (res.code === 200 && res.data) {
            ref.ejsInstances.addNodes(res.data)
            this.searchIndustryList = res.data
          }
        })
    } else {
      ref.ejsInstances.addNodes(this.allIndustryData)
    }
  }

  private handleConfirm() {
    const ref = this.$refs.treeView as any
    const allCheckedNodes = ref.ejsInstances.getAllCheckedNodes()

    const treeDataList = allCheckedNodes.map((v: string) => {
      return ref.ejsInstances.getTreeData(v)[0]
    })

    this.$emit('save', treeDataList)
    this.hide()
  }

  private async getIndustryData() {
    const res = await this.$api.common.getDictItemTree({
      dictCode: 'Industry'
    })

    if (res.code === 200 && res.data) {
      // const ref = this.$refs.treeView as any
      // ref.ejsInstances.addNodes(res.data)
      this.sourceTreeData.dataSource.push(...(res.data || []))
      this.allIndustryData = res.data
    }
  }
}
</script>

<style lang="less" scoped>
.search--wrap {
  margin: 20px;
  display: flex;
  align-items: center;
  label {
    color: #3a3a3a;
  }
  .search-input {
    flex: 1;
  }
}
</style>
