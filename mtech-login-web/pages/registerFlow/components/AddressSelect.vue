<template>
  <div class="address-select--wrap">
    <mt-select
      class="address-select--item"
      :allowFiltering="true"
      :fields="countryField"
      :dataSource="countryList"
      v-model="formData.countryCode"
      :placeholder="$t('请选择国家/地区')"
      :change="handleCountryChange"
    >
    </mt-select>

    <mt-select
      class="address-select--item"
      :allowFiltering="true"
      :fields="areaField"
      :dataSource="provinceList"
      v-model="formData.provinceCode"
      :placeholder="$t('请选择省')"
      :change="handleProvinceChange"
    >
    </mt-select>

    <mt-select
      class="address-select--item"
      :allowFiltering="true"
      :fields="areaField"
      :dataSource="cityList"
      v-model="formData.cityCode"
      :placeholder="$t('请选择市')"
      :change="handleCityChange"
    >
    </mt-select>

    <mt-input
      class="address-input--item"
      v-model="formData.addressDetail"
      :placeholder="$t('请输入明细地址')"
      @change="handleDetailChange"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'

interface IAddressData {
  countryCode: string
  provinceCode: string
  cityCode: string
  countryName?: string
  provinceName?: string
  cityName?: string
  addressDetail?: string
}

@Component({
  layout: 'blank',
  components: {}
})
export default class AddressSelect extends Vue {
  @Prop()
  address!: IAddressData

  formData: IAddressData = {
    countryCode: 'CHN',
    countryName: '中国 内地',
    provinceCode: '',
    provinceName: '',
    cityCode: '',
    cityName: '',
    addressDetail: ''
  }
  countryList: any[] = []
  provinceList: any[] = []
  cityList: any[] = []

  countryField: any = {
    value: 'countryCode',
    text: 'shortName'
  }

  areaField: any = {
    value: 'areaCode',
    text: 'areaName'
  }

  @Watch('address', { immediate: true })
  onWatchAddress(val: IAddressData) {
    if (val && Object.keys(val).length) {
      this.formData = Object.assign({}, this.formData, val)
      if (Object.values(this.formData).every((v) => !v)) {
        this.formData.countryCode = 'CHN'
        this.formData.countryName = '中国 内地'
      }
    }
  }

  mounted() {
    this.getCountryList()
  }

  handleDetailChange(value: string) {
    // this.formData.addressDetail = value
    this.$set(this.formData, 'addressDetail', value || '')
    this.$emit('change', this.formData)
  }

  handleCityChange(event: any) {
    const { itemData } = event
    // this.formData.cityCode = itemData?.areaCode || ''
    this.$set(this.formData, 'cityCode', itemData?.areaCode)
    this.formData.cityName = itemData?.areaName || ''
    this.$emit('change', this.formData)
  }

  handleProvinceChange(event: any) {
    const { itemData, e } = event
    // this.formData.provinceCode = itemData?.areaCode
    this.$set(this.formData, 'provinceCode', itemData?.areaCode)
    this.formData.provinceName = itemData?.areaName
    if (e) {
      this.handleCityChange({ itemData: {} })
    } else {
      this.handleCityChange({
        itemData: {
          areaCode: this.formData.cityCode,
          areaName: this.formData.cityName
        }
      })
    }

    if (itemData?.areaCode) {
      this.getCityList(itemData.areaCode)
    } else {
      this.cityList = []
    }
  }

  handleCountryChange(event: any) {
    const { itemData } = event
    // this.formData.countryCode = itemData?.countryCode
    this.$set(this.formData, 'countryCode', itemData?.countryCode)
    this.formData.countryName = itemData?.shortName
    this.handleProvinceChange({})

    if (this.formData.countryCode === 'CHN') {
      this.getProvinceList()
      this.cityList = []
    } else {
      this.provinceList = []
      this.cityList = []
    }
  }

  private async getCountryList() {
    const countryList: any = this.$store.state.countryList
    if (!countryList.length) {
      const res = await this.$api.common.getAllCountryList()
      this.countryList = res?.data || []
      this.$store.dispatch('setCountryList', res.data)
    } else {
      this.countryList = countryList
    }
  }

  private async getProvinceList() {
    const ProvinceList = this.$store.state.defaultProvinceList
    if (!ProvinceList.length) {
      const res = await this.$api.common.getAreaListByParent({
        parentCode: ''
      })
      this.provinceList = res?.data || []
    } else {
      this.provinceList = ProvinceList
    }
  }

  private async getCityList(code: string) {
    const res = await this.$api.common.getAreaListByParent({
      parentCode: code
    })
    this.cityList = res?.data || []
  }
}
</script>

<style lang="less" scoped>
.address-select--wrap {
  display: flex;
  .address-select--item {
    width: 20%;
    margin-right: 20px;
  }
  .address-input--item {
    width: 40%;
  }
}
</style>
