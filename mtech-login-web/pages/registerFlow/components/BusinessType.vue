<template>
  <div class="type-options-wrapper justify-between">
    <div
      class="type-option justify-between items-center relative"
      :class="{ active: businessType.includes(2) }"
      @click="selectBusinessType(2)"
    >
      <div class="option-icon">
        <svg-icon class="icon-style icon-pur" icon-class="picture_cover" />
      </div>
      <div class="option-label">
        <div>{{ $t('我是采购方') }}</div>
        <div>{{ $t('我要采购商品/服务') }}</div>
      </div>
      <mt-checkbox
        v-show="businessType.includes(2)"
        class="option-checkbox"
        :checked="true"
      ></mt-checkbox>
    </div>
    <div
      class="type-option justify-between items-center relative"
      :class="{ active: businessType.includes(1) }"
      @click="selectBusinessType(1)"
    >
      <div class="option-icon">
        <svg-icon class="icon-style icon-sup" icon-class="picture_supplier" />
      </div>
      <div class="option-label">
        <div>{{ $t('我是供应方') }}</div>
        <div>{{ $t('我要供应商品/服务') }}</div>
      </div>
      <mt-checkbox
        v-show="businessType.includes(1)"
        class="option-checkbox"
        :checked="true"
      ></mt-checkbox>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from 'vue-property-decorator'

@Component({
  layout: 'blank',
  components: {}
})
export default class BusinessType extends Vue {
  @Prop()
  type!: 1 | 2 | 3

  @Prop({
    default: false
  })
  disabled!: boolean

  businessType: number[] = []

  @Watch('type', { immediate: true })
  onWatchType(val: number) {
    if (val === 3) {
      this.businessType.splice(0, this.businessType.length, 2, ...[1, 2])
    } else if (val && !this.businessType.includes(val)) {
      this.businessType.push(val)
    }
  }

  selectBusinessType(type: number) {
    if (this.disabled) return
    let set = new Set([...this.businessType])
    if (!this.businessType.length) {
      set = new Set([type])
    } else if (set.has(type)) {
      set.delete(type)
    } else {
      set.add(type)
    }
    this.businessType = [...set]
    this.saveBusinessType()
  }

  saveBusinessType() {
    if (this.businessType.length === 1) {
      this.$emit('change', this.businessType[0])
    } else if (this.businessType.length === 2) {
      this.$emit('change', 3)
    } else {
      this.$emit('change', undefined)
    }
  }
}
</script>

<style lang="less" scoped>
.justify-between {
  display: flex;
  justify-content: space-between;
}
.items-center {
  display: flex;
  align-items: center;
}
.relative {
  position: relative;
}
.type-options-wrapper {
  width: 604px;
  .type-option {
    width: 280px;
    height: 100px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(227, 226, 226, 1);
    border-radius: 8px;
    overflow: hidden;
    padding: 0 41px;
    cursor: pointer;
    .option-icon {
      width: 60px;
      height: 60px;
      .icon-style {
        width: 60px;
        height: 60px;
      }
      .icon-pur {
        color: #6dcddf;
      }
      .icon-sup {
        color: #ffc500;
      }
    }
    .option-label {
      height: 60px;
      line-height: 30px;
      div:first-child {
        font-weight: 500;
      }
      div:last-child {
        font-size: 14px;
        color: rgba(156, 156, 156, 1);
      }
    }
    .option-checkbox {
      position: absolute;
      right: -1px;
      top: -1px;
    }
  }
  .active {
    border: 1px solid #00469c;
  }

  /deep/ .e-checkbox-wrapper {
    display: block;
    label {
      display: block;
    }
  }
}
</style>
