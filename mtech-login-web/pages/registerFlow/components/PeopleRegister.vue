<template>
  <div class="step-three--wrapper">
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" class="mt-form--wrap">
      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_tel" />
        <mt-form-item prop="mobile" class="form-item" :label="$t('手机号')">
          <mt-select
            width="120"
            popupWidth="120"
            :value="selectedArea"
            :dataSource="areaCodeList"
            :readonly="false"
          ></mt-select>
          <mt-input
            v-model="ruleForm.mobile"
            :disabled="false"
            :width="330"
            :showClearButton="true"
            type="text"
            :placeholder="$t('请输入手机号')"
          ></mt-input>
        </mt-form-item>
        <p class="mobile--error" v-if="hasMobileError">
          {{ $t('您的手机号已注册，请') }}<a @click="login">{{ $t('直接登录') }}</a>
        </p>
      </div>

      <div class="form-item--wrap form-item-verificaCode--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_Comple" />
        <mt-form-item prop="smsCode" class="form-item" :label="$t('手机验证码')">
          <mt-input
            :width="450"
            v-model="ruleForm.smsCode"
            type="text"
            :placeholder="$t('请输入手机验证码')"
          ></mt-input>
          <mt-button cssClass="verifica--button" @click="getSmsCode">
            {{ smsCodeCountDown >= 0 ? `${smsCodeCountDown}s` : $t('发送验证码') }}
          </mt-button>
        </mt-form-item>
      </div>

      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_password" />
        <mt-form-item prop="password" class="form-item" :label="$t('密码')">
          <mt-input
            :width="450"
            v-model="ruleForm.password"
            type="password"
            :placeholder="$t('请输入密码')"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_password" />
        <mt-form-item prop="confirmPassword" class="form-item" :label="$t('确认密码')">
          <mt-input
            :width="450"
            v-model="ruleForm.confirmPassword"
            type="password"
            :placeholder="$t('确认密码')"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_name" />
        <mt-form-item prop="lastName" class="form-item" :label="$t('姓')">
          <mt-input
            :width="160"
            v-model="ruleForm.lastName"
            type="text"
            :placeholder="$t('请输入姓氏')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="firstName" class="form-item" :label="$t('名')">
          <mt-input
            :width="280"
            v-model="ruleForm.firstName"
            type="text"
            :placeholder="$t('请输入名称')"
          ></mt-input>
        </mt-form-item>
      </div>

      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_name" />
        <mt-form-item prop="username" class="form-item" :label="$t('用户名')">
          <mt-input
            :width="450"
            v-model="ruleForm.username"
            type="text"
            :placeholder="$t('请输入用户名')"
          ></mt-input>
        </mt-form-item>
      </div>

      <div class="form-item--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_mail" />
        <mt-form-item prop="mail" class="form-item" :label="$t('邮箱')">
          <mt-input
            :width="450"
            v-model="ruleForm.mail"
            type="text"
            :placeholder="$t('请输入邮箱')"
          ></mt-input>
        </mt-form-item>
      </div>

      <div class="form-item--wrap form-item-verificaCode--wrap">
        <svg-icon class="icon--wrap" icon-class="icon_login_mail_authentication" />
        <mt-form-item prop="mailCode" class="form-item" :label="$t('邮箱认证')">
          <mt-input
            :width="450"
            v-model="ruleForm.mailCode"
            type="text"
            :placeholder="$t('请输入邮箱认证')"
          ></mt-input>
          <mt-button cssClass="verifica--button" @click="getMailCode">
            {{ mailCodeCountDown >= 0 ? `${mailCodeCountDown}s` : $t('发送验证码') }}
          </mt-button>
        </mt-form-item>
      </div>
    </mt-form>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { phoneRegex, pswRegex, emailRegex } from '@/utils/my.validators'

@Component({
  components: {},
  middleware: ['config']
  // async asyncData({ app, error }) {}
})
export default class PeopleRegister extends Vue {
  @Prop()
  enterpriseId!: string

  ruleForm: any = {}

  get rules() {
    return {
      mobile: [
        // { required: true, message: '请输入手机号', trigger: 'blur' },
        // {required: true, validator: this.validateAddress, trigger: 'blur' }
        { required: true, validator: this.validateMobile, trigger: 'blur' }
      ],
      smsCode: [{ required: true, message: this.$t('请输入验证码'), trigger: 'blur' }],
      password: [{ required: true, validator: this.validatePwd, trigger: 'blur' }],
      confirmPassword: [{ required: true, validator: this.validateConfirmPwd, trigger: 'blur' }],
      firstName: [{ required: true, message: this.$t('请输入姓氏'), trigger: 'blur' }],
      lastName: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
      mail: [{ required: true, validator: this.validateMail, trigger: 'blur' }],
      mailCode: [{ required: true, message: this.$t('请输入邮箱验证码'), trigger: 'blur' }],
      username: [{ required: true, message: this.$t('请输入用户名'), trigger: 'blur' }]
    }
  }

  smsCodeCountDown = -1
  mailCodeCountDown = -1
  smsCodeTimer!: NodeJS.Timeout
  mailCodeTimer!: NodeJS.Timeout

  hasMobileError = false // 是否显示手机号已注册的错误

  selectedArea = '+86'
  areaCodeList: object[] = [
    {
      value: '+86',
      text: '中国大陆+86'
    }
  ]

  validForm() {
    return new Promise((resolve, reject) => {
      ;(this.$refs.ruleForm as any).validate((valid: any) => {
        if (valid) {
          const params = {
            ...this.ruleForm
          }
          if (this.enterpriseId) {
            params.enterpriseId = this.enterpriseId
          }
          this.$api.register
            .register(params)
            .then((res: any) => {
              if (res.code === 200) {
                resolve({
                  ...this.ruleForm,
                  userId: res.data?.userId || ''
                })
              } else {
                if (res.data && res.data.errorLabels && res.data.errorLabels.length) {
                  ;(this.$refs.ruleForm as any).addErrorLabels(res.data.errorLabels)
                }
                reject(res)
              }
            })
            .catch((err: any) => {
              reject(err)
            })
        } else {
          reject(this.ruleForm)
        }
      })
    })
  }

  async getSmsCode() {
    const mobile = this.ruleForm.mobile
    if (!mobile || !phoneRegex.test(mobile) || this.smsCodeCountDown >= 0) return

    this.hasMobileError = false
    this.$api.register
      .getVerifyCode({
        mobile: mobile
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.smsCodeCountDown = 60
          this.smsCodeTimer = setInterval(() => {
            if (this.smsCodeCountDown > 0) {
              this.smsCodeCountDown--
            } else {
              this.smsCodeCountDown = -1
              clearInterval(this.smsCodeTimer)
            }
          }, 1000)
        } else if (res.code === 602) {
          this.hasMobileError = true
          // (this.$refs.ruleForm as any).addErrorLabels([
          //   {label: "mobile",message: "手机号不能为空"}
          // ])
        }
      })
  }

  async getMailCode() {
    const mail = this.ruleForm.mail
    if (!mail || !emailRegex.test(mail) || this.mailCodeCountDown >= 0) return

    this.$api.register
      .getMailSmsCode({
        mail: mail
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.mailCodeCountDown = 60
          this.mailCodeTimer = setInterval(() => {
            if (this.mailCodeCountDown > 0) {
              this.mailCodeCountDown--
            } else {
              this.mailCodeCountDown = -1
              clearInterval(this.mailCodeTimer)
            }
          }, 1000)
        }
      })
  }

  validateMobile(rule: any, value: any, callback: any) {
    this.hasMobileError = false
    if (!value) {
      callback(new Error(this.$t('请输入手机号')))
    } else if (!phoneRegex.test(value)) {
      callback(new Error(this.$t('手机号格式不正确')))
    } else {
      callback()
    }
  }

  validateMail(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error(this.$t('请输入邮箱地址')))
    } else if (!emailRegex.test(value)) {
      callback(new Error(this.$t('邮箱地址格式不正确')))
    } else {
      callback()
    }
  }

  validatePwd(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error(this.$t('请输入密码')))
    } else if (!pswRegex.test(value)) {
      callback(new Error(this.$t('密码必须为6-20位数字/字母/特殊字符')))
    } else {
      callback()
    }
  }

  validateConfirmPwd(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error(this.$t('请再次输入密码')))
    } else if (value !== this.ruleForm.password) {
      callback(new Error(this.$t('两次密码输入内容不一致')))
    } else {
      callback()
    }
  }

  // href="/login"
  login() {
    ;(this as any).$clearAuth()
    window.location.href = '/login'
  }
}
</script>

<style lang="less" scoped>
.tip {
  font-size: 14px;
  font-weight: normal;
  color: rgba(0, 70, 156, 1);
  margin: 50px 0 80px 0;
  text-align: center;
}
.mt-form--wrap {
  display: inline-block;
  .icon--wrap {
    font-size: 24px;
    margin-bottom: 12px;
    margin-right: 10px;
  }

  .form-item {
    display: inline-block;
  }
  .form-item-verificaCode--wrap {
    position: relative;

    /deep/ .mt-button {
      position: absolute;
      bottom: 0;
      right: 0;
    }
  }
}
.form-item--wrap {
  position: relative;
}
.mobile--error {
  color: #f44336;
  font-size: 12px;
  font-weight: normal;
  position: absolute;
  bottom: 4px;
  left: 40px;
  line-height: 1;
  display: inline-block;
  padding-top: 4px;

  a {
    color: #1455a4;
    cursor: pointer;
  }
}
/deep/ .verifica--button {
  width: 144px;
  height: 32px;
  background: #eda133;
  color: #fff;
  box-shadow: none;
  margin-bottom: 4px;
}
</style>
