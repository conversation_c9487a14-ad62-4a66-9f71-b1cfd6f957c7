<!--
 * @Author: your name
 * @Date: 2021-08-03 14:14:01
 * @LastEditTime: 2021-08-03 19:57:04
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-login-web\pages\registerFlow\index.vue
-->
<template>
  <div>
    <div class="header-wrapper">
      <img class="logo" src="~assets/images/logo.png" @click="toHome" />
    </div>
    <div class="wrapper">
      <mt-icon class="icon-back" name="M_PV_Previous" @click.native="toHome"></mt-icon>
      <p class="flow--title">{{ $t('请选择您的身份') }}</p>
      <div class="flow-item--wrap justify-center">
        <div class="entry-wrapper justify-center items-center" @click="toPage(1)">
          <div class="ctx justify-between">
            <img class="ctx-image" src="../../assets/images/button_cover.png" alt="" />
            <div class="ctx-text items-around">
              <div class="text-top">{{ $t('采方/供方企业认证') }}</div>
              <div class="text-mid">{{ $t('申请成为采方企业或供方企业') }}</div>
              <div class="text-bot">
                {{ $t('注意：采方企业需要以合同盖章的企业注册并开通权限') }}
              </div>
            </div>
          </div>
        </div>
        <div class="entry-wrapper justify-center items-center" @click="toPage(2)">
          <div class="ctx justify-between">
            <img class="ctx-image" src="../../assets/images/button_personage.png" alt="" />
            <div class="ctx-text items-around">
              <div class="text-top">{{ $t('个人注册') }}</div>
              <div class="text-mid">{{ $t('申请个人注册，暂不申请企业或专家认证或加入企业') }}</div>
              <div class="text-bot">
                {{
                  $t('注意：如需同时申请企业注册或专家认证或加入企业，可以直接到选择对应的方式。')
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
// import { formatRules } from '@/utils/index'

@Component({
  layout: 'blank',
  components: {},
  middleware: ['config']
  // async asyncData({ app, error }) {}
})
export default class RegisterFlow extends Vue {
  toPage(type: number) {
    if (type === 1) {
      this.$router.push({ path: '/register-flow/company' })
    }
    if (type === 2) {
      this.$router.push({ path: '/register-flow/personal' })
    }
  }

  toHome() {
    this.$router.push({ path: '/' })
  }
}
</script>

<style lang="less" scoped>
.justify-between {
  display: flex;
  justify-content: space-between;
}
.justify-center {
  display: flex;
  justify-content: center;
}
.items-around {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.items-center {
  display: flex;
  align-items: center;
}
.header-wrapper {
  width: calc(100% - 40px);
  margin: 0 20px;
  padding: 10px 20px;
  box-shadow: inset 0 -2px 0 0 rgba(242, 242, 242, 1);
  .logo {
    width: 120px;
    height: 38px;
    cursor: pointer;
  }
}
.wrapper {
  position: relative;
  overflow: hidden;
  .icon-back {
    position: absolute;
    left: 40px;
    top: 30px;
    font-size: 32px;
    cursor: pointer;
  }
  .flow--title {
    margin: 148px 0 100px;
    text-align: center;
    font-size: 48px;
  }
  .entry-wrapper {
    width: 700px;
    height: 360px;
    margin: 0 70px;
    background: rgba(245, 250, 253, 1);
    border: 2px solid rgba(242, 242, 242, 1);
    border-radius: 8px;
    cursor: pointer;
    .ctx {
      width: 554px;
      height: 182px;
      .ctx-image {
        width: 221px;
        height: 182px;
      }
      .ctx-text {
        width: 256px;
        height: 182px;
        .text-top {
          width: 256px;
          height: 34px;
          font-size: 30px;
          font-weight: 600;
          color: rgba(0, 70, 156, 1);
        }
        .text-mid {
          width: 241px;
          height: 19px;
          font-size: 18px;
          color: rgba(156, 156, 156, 1);
        }
        .text-bot {
          width: 241px;
          height: 18px;
          font-size: 16px;
          color: rgba(214, 44, 44, 1);
        }
      }
    }
    &:hover {
      box-shadow: 0 2px 60px 0 rgba(0, 70, 156, 0.3);
      border: 2px solid rgba(0, 70, 156, 1);
    }
  }
  // .active {
  //   box-shadow:  0 2px 60px 0 rgba(0,70,156,0.3);
  //   border: 2px solid rgba(0,70,156,1);
  // }
}
</style>
