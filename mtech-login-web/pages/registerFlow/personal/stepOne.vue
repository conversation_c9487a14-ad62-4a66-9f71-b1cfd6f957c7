<template>
  <div class="step-three--wrapper">
    <p class="register--header">{{ $t('请填写您的个人注册信息') }}</p>
    <PeopleRegister ref="register" />

    <div>
      <div class="agree-wrapper">
        <mt-checkbox
          :checked="isCheckedUser"
          @change="(e) => (isCheckedUser = e.checked)"
        ></mt-checkbox>
        <span
          >{{ $t('我已阅读，并同意') }}
          <span class="agree-user">{{ $t('《用户协议》') }}</span></span
        >
      </div>
      <div class="agree-wrapper">
        <mt-checkbox
          :checked="isCheckedPrivacy"
          @change="(e) => (isCheckedPrivacy = e.checked)"
        ></mt-checkbox>
        <span
          >{{ $t('我已阅读，并同意') }}<span class="agree-ys">{{ $t('《隐私策略》') }}</span></span
        >
        <span class="agree-user"> ，&nbsp;&nbsp; {{ $t('请阅读并勾选用户协议/隐私策略') }} </span>
      </div>
    </div>

    <div class="footer--wrap">
      <mt-button
        :is-primary="true"
        class="footer-prev-btn"
        cssClass="next-btn"
        @click="handlePrev"
        >{{ $t('上一步') }}</mt-button
      >
      <mt-button
        :is-primary="true"
        :disabled="!isCheckedUser || !isCheckedPrivacy"
        cssClass="next-btn"
        @click="handleNext"
        >{{ $t('下一步') }}</mt-button
      >
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import PeopleRegister from '../components/PeopleRegister.vue'

@Component({
  components: {
    PeopleRegister
  }
  // async asyncData({ app, error }) {}
})
export default class RegisterFlowOne extends Vue {
  isCheckedUser = false
  isCheckedPrivacy = false

  handleNext() {
    if (!this.isCheckedUser || !this.isCheckedPrivacy) {
      this.$tips.warning({
        title: this.$t('警告'),
        content: this.$t('请先阅读并同意上述协议')
      })
      return
    }
    ;(this.$refs.register as any)
      .validForm()
      .then(() => {
        this.$emit('next')
      })
      .catch((err: any) => {
        this.$tips.error({
          title: this.$t('提示'),
          content: err.msg || this.$t('个人注册失败')
        })
      })
  }
  handlePrev() {
    this.$emit('prev')
  }
}
</script>

<style lang="less" scoped>
.step-three--wrapper {
  width: 500px;
  margin: 0 auto;
  text-align: center;
}

.register--header {
  margin: 35px 0;
  font-size: 14px;
  color: #00469c;
}
.footer--wrap {
  margin: 20px 0 0;
  padding-bottom: 100px;
  .protocol--wrap {
    color: rgba(107, 101, 101, 1);
    margin-bottom: 16px;

    .checkbox {
      margin-right: 5px;
    }
  }
  .protocol-user {
    color: #00469c;
    cursor: pointer;
  }
  .protocol-primary {
    color: #d62c2c;
    cursor: pointer;
  }
  .foot-btn--wrap {
    margin-top: 70px;
  }
  .footer-prev-btn {
    margin-right: 20px;
    background: rgba(242, 242, 242, 1);
  }
}

.agree-wrapper {
  margin-bottom: 15px;
  color: #6b6565;
  font-size: 14px;
  text-align: left;
  .agree-user {
    color: #1455a4;
    cursor: pointer;
  }
  .agree-ys {
    color: #da3e3e;
    cursor: pointer;
  }
}

.footer--wrap {
  margin-top: 20px;
  text-align: center;
  padding-bottom: 30px;
}

/deep/.next-btn {
  width: 200px;
  height: 44px;
  color: #fff;
  font-size: 14px;
  background-color: #00469c;
}
</style>
