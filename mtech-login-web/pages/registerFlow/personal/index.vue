<template>
  <div>
    <div class="header-wrapper">
      <img class="logo" src="~assets/images/logo.png" @click="toHome" />
    </div>
    <div class="steppers-wrapper justify-between">
      <div :class="['stepper', 0 === currentStep ? 'active' : '']">
        <span>1</span>
        <span>{{ $t('个人注册') }}</span>
      </div>
      <div :class="['stepper', 1 === currentStep ? 'active' : '']">
        <span>2</span>
        <span>{{ $t('提交审核') }}</span>
      </div>
    </div>
    <div class="ctx-wrapper">
      <step-one v-show="currentStep === 0" @next="nextStep" @prev="prevStep"></step-one>
      <step-two v-show="currentStep === 1" @next="nextStep" @prev="prevStep"></step-two>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
// import { formatRules } from '@/utils/index'
import StepOne from './stepOne.vue'
import stepTwo from './stepTwo.vue'

@Component({
  layout: 'blank',
  components: {
    StepOne,
    stepTwo
  }
  // async asyncData({ app, error }) {}
})
export default class RegisterFlow extends Vue {
  currentStep: number = 0

  nextStep() {
    this.currentStep++
  }

  prevStep() {
    if (this.currentStep === 0) {
      this.$router.push({ path: '/register-flow' })
      return
    }
    this.currentStep--
  }

  toHome() {
    this.$router.push({ path: '/' })
  }
}
</script>

<style scoped lang="less">
.justify-between {
  display: flex;
  justify-content: space-between;
}
.header-wrapper {
  width: calc(100% - 40px);
  margin: 0 20px;
  padding: 10px 20px;
  box-shadow: inset 0 -2px 0 0 rgba(242, 242, 242, 1);
  .logo {
    width: 120px;
    height: 38px;
    cursor: pointer;
  }
}
.steppers-wrapper {
  width: 1200px;
  height: 50px;
  line-height: 50px;
  margin: 40px auto;
  font-family: PingFangSC;
  color: #3e3e3e;
  .stepper {
    position: relative;
    width: 50%;
    text-align: center;
    span:first-child {
      display: inline-block;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      border-radius: 50%;
      background-color: rgba(62, 62, 62, 1);
      color: #fff;
      font-size: 20px;
    }
  }
  .active {
    span:first-child {
      width: 50px;
      height: 50px;
      line-height: 50px;
      background-color: #00469c;
      font-size: 26px;
    }
    span:last-child {
      color: #00469c;
      font-size: 24px;
    }
  }
  .stepper:not(:first-child):before {
    content: '';
    position: absolute;
    top: 52%;
    left: 0;
    background: rgba(151, 151, 151, 1);
    width: 35%;
    height: 1px;
  }
  .stepper:not(:last-child):after {
    content: '';
    position: absolute;
    top: 52%;
    right: 0;
    background: rgba(151, 151, 151, 1);
    width: 35%;
    height: 1px;
  }
}
.ctx-wrapper {
  margin: 0 auto;
  color: #3e3e3e;
}
</style>
