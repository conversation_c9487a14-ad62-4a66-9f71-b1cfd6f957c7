<template>
  <div class="step-four--wrapper">
    <p class="tip">
      {{ $t('提交审核后可以返回个人中心，可以查看公司审核的进度和对管理员个人信息进行修改。') }}
    </p>

    <img class="image" src="@/assets/images/register_complete.png" />

    <p class="success-tip bolder">
      {{ $t('恭喜！您已注册成功！您可以返回') }} <a @click="gotoUc">{{ $t('个人中心') }}</a>
    </p>
    <!-- <p class="success-tip">{{ $t("您可以立刻") }}<a @click="gotoUc">{{ $t("加入企业") }}</a>{{ $t("或者") }}<a @click="gotoUc">{{ $t("专家注册") }}</a></p> -->
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { getOrigin } from '@/utils/env'

@Component({
  components: {},
  middleware: ['config']
  // async asyncData({ app, error }) {}
})
export default class RegisterFlowTwo extends Vue {
  handleNext() {
    this.$emit('next')
    // this.$parent.nextStep()
  }
  handlePrev() {
    this.$emit('prev')
    // this.$parent.prevStep()
  }

  gotoUc() {
    ;(this as any).$clearAuth()
    const origin = getOrigin('platform')
    window.location.href = origin + '/#/platform/personal'
  }
}
</script>
<style lang="less" scoped>
.step-four--wrapper {
  width: 700px;
  margin: 0 auto;
  text-align: center;
}
.tip {
  font-size: 14px;
  font-weight: normal;
  color: rgba(0, 70, 156, 1);
  margin: 50px 0 100px 0;
  text-align: center;
}
.image {
  width: 223px;
  height: 244px;
  opacity: 50;
  margin-bottom: 40px;
}
.success-tip {
  font-size: 14px;
  line-height: 24px;
  color: rgba(156, 156, 156, 1);
  a {
    color: #00469c;
    cursor: pointer;
    text-decoration: none;
  }
}
.bolder {
  font-weight: 700;
  color: rgba(62, 62, 62, 1);
}
</style>
