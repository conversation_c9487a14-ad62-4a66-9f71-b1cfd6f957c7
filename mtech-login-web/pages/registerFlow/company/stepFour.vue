<template>
  <div class="step-four--wrapper">
    <p class="tip">
      {{ $t('提交审核后可以返回个人中心，可以查看公司审核的进度和对管理员个人信息进行修改。') }}
    </p>

    <img class="image" src="@/assets/images/register_complete.png" />

    <p class="success-tip bolder">
      {{ $t('您的企业：') }} {{ enterpriseName }}{{ $t('，已提交审核') }}
    </p>
    <p class="success-tip">{{ $t('审批通过后可通过管理员的个人信息登录公司后台') }}</p>
    <p class="success-tip">
      {{ $t('您已完成注册流程，您可以返回') }}
      <a @click="gotoUc" v-if="!source">{{ $t('个人中心') }}</a>
      <a @click="gotoLg" v-else>{{ $t('登录') }}</a>
    </p>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { getOrigin } from '@/utils/env'

@Component({
  components: {},
  middleware: ['config']
  // async asyncData({ app, error }) {}
})
export default class RegisterFlowFour extends Vue {
  @Prop({
    default: ''
  })
  enterpriseName!: string
  @Prop({
    default: false
  })
  source!: boolean

  gotoUc() {
    ;(this as any).$clearAuth()
    const origin = getOrigin('platform')
    window.location.href = origin + '/#/platform/personal'
  }
  gotoLg() {
    window.location.href = '/login'
  }
}
</script>

<style lang="less">
.step-four--wrapper {
}
</style>

<style lang="less" scoped>
.step-four--wrapper {
  width: 700px;
  margin: 0 auto;
  text-align: center;
}
.tip {
  font-size: 14px;
  font-weight: normal;
  color: rgba(0, 70, 156, 1);
  margin-bottom: 60px;
  text-align: center;
}
.image {
  width: 223px;
  height: 244px;
  opacity: 50;
  margin-bottom: 40px;
}
.success-tip {
  font-size: 14px;
  line-height: 24px;
  color: rgba(156, 156, 156, 1);
  a {
    color: #00469c;
    cursor: pointer;
    text-decoration: none;
  }
}
.bolder {
  font-weight: 700;
  color: rgba(62, 62, 62, 1);
}
</style>
