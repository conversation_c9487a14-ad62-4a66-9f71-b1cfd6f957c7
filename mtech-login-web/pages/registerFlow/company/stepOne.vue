<!--
 * @Author: your name
 * @Date: 2021-08-03 09:31:47
 * @LastEditTime: 2021-08-03 20:09:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-login-web\pages\registerFlow\company\stepOne.vue
-->
<template>
  <div class="wrapper">
    <div class="tips">
      {{
        $t(
          '请填写企业的名称进行搜索，根据第三方工商信息将自动填入，查看勾选用户协议和隐私协议才能进行下一步哦！'
        )
      }}
    </div>

    <mt-form ref="ruleForm" :model="baseForm" :rules="formRules" class="base-form-wrapper">
      <mt-form-item prop="businessType" :label="$t('类型')">
        <BusinessType @change="changeBusinessType" />
      </mt-form-item>

      <mt-form-item prop="countryCode" :label="$t('国家地区')">
        <mt-select
          :width="590"
          :allowFiltering="true"
          :fields="countryField"
          :dataSource="countryList"
          v-model="baseForm.countryCode"
          :placeholder="$t('请选择国家检索')"
          @change="changeCountry"
        >
        </mt-select>
      </mt-form-item>

      <!-- <mt-form-item prop="enterpriseTypeCode" :label="$t('工商类型')">
        <mt-select
          :width="590"
          :fields="enterpriseTypeField"
          :dataSource="enterpriseTypeList"
          v-model="baseForm.enterpriseTypeCode"
          :placeholder="$t('请选择工商类型')"
          @change="changeEnterpriseType">
        </mt-select>
      </mt-form-item> -->

      <mt-form-item prop="enterpriseName" :label="$t('企业名称')" class="relative mt30">
        <mt-input
          :width="590"
          v-model="baseForm.enterpriseName"
          type="text"
          :placeholder="$t('请输入企业名称或者统一社会信用代码进行查询')"
        ></mt-input>
        <!-- <mt-button cssClass="search-btn">{{ $t("搜索") }}</mt-button> -->
      </mt-form-item>

      <div class="agree-wrapper">
        <mt-checkbox
          :checked="isCheckedUser"
          @change="(e) => (isCheckedUser = e.checked)"
        ></mt-checkbox>
        <span
          >{{ $t('我已阅读，并同意')
          }}<span class="agree-user"> {{ $t('《用户协议》') }}</span></span
        >
      </div>
      <div class="agree-wrapper">
        <mt-checkbox
          :checked="isCheckedPrivacy"
          @change="(e) => (isCheckedPrivacy = e.checked)"
        ></mt-checkbox>
        <span
          >{{ $t('我已阅读，并同意') }}<span class="agree-ys"> {{ $t('《隐私策略》') }}</span></span
        >
        <span class="agree-user"> ，&nbsp;&nbsp;{{ $t('请阅读并勾选用户协议/隐私策略') }}</span>
      </div>
    </mt-form>

    <div class="btn--wrap">
      <mt-button cssClass="next-btn mr30" @click="handlePrev">{{ $t('返回') }}</mt-button>
      <mt-button cssClass="next-btn" :loading="confirmLoading" @click="handleNext">{{
        $t('下一步')
      }}</mt-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import BusinessType from '../components/BusinessType.vue'
// import { formatRules } from '@/utils/index'

@Component({
  components: {
    BusinessType
  },
  middleware: ['config']
  // async asyncData({ app, error }) {}
})
export default class RegisterFlowOne extends Vue {
  enterpriseId = ''
  baseForm: any = {
    businessType: '',
    countryCode: 'CHN',
    countryName: '中国 内地',
    enterpriseName: ''
  }
  countryField: any = {
    value: 'countryCode',
    text: 'shortName'
  }
  countryList: any[] = []

  enterpriseTypeField: any = {
    value: 'itemCode',
    text: 'name'
  }
  enterpriseTypeList: any[] = []

  isCheckedUser = false
  isCheckedPrivacy = false
  confirmLoading = false

  // formRules = {
  //   businessType: [
  //     { required: true, message: this.$t('请选择企业类型'), trigger: 'blur' }
  //   ],
  //   countryCode: [
  //     { required: true, message: this.$t('请选择国家地区'), trigger: 'blur' }
  //   ],
  //   enterpriseName: [
  //     { required: true, validator: this.validEnterpriseName, trigger: 'blur' }
  //   ]
  // }

  get formRules() {
    return {
      businessType: [{ required: true, message: this.$t('请选择企业类型'), trigger: 'blur' }],
      countryCode: [{ required: true, message: this.$t('请选择国家地区'), trigger: 'blur' }],
      enterpriseName: [{ required: true, validator: this.validEnterpriseName, trigger: 'blur' }]
    }
  }

  mounted() {
    // this.getValidRules()
    this.getCountryList()
    // this.getDictItemTree()
  }

  changeBusinessType(type: number) {
    this.baseForm.businessType = type
  }

  changeCountry(event: any) {
    const { itemData } = event
    this.baseForm.countryName = itemData.shortName
  }

  changeEnterpriseType() {
    // const { itemData } = event
    // this.baseForm.enterpriseTypeName = itemData.name
  }

  handlePrev() {
    this.$emit('prev')
  }

  handleNext() {
    if (this.isCheckedUser && this.isCheckedPrivacy) {
      ;(this.$refs['ruleForm'] as any).validate((valid: any) => {
        if (valid) {
          this.saveHeader()
        } else {
          return false
        }
      })
    } else {
      this.$tips.warning({
        title: this.$t('警告'),
        content: this.$t('请先阅读并同意上述协议')
      })
    }
  }

  validEnterpriseName(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error(this.$t('企业名称')))
    } else {
      this.$api.register
        .validateCompanyName({
          companyName: value
        })
        .then((res: any) => {
          if (res.code === 200 && res.data === 1) {
            callback(new Error('您的企业已经注册，无需注册'))
          } else {
            callback()
          }
        })
    }
  }

  private async getCountryList() {
    const countryList: any = this.$store.state.countryList
    if (!countryList.length) {
      const res = await this.$api.common.getAllCountryList()
      this.countryList = res?.data || []
      this.$store.dispatch('setCountryList', res.data)
    } else {
      this.countryList = countryList
    }
  }

  // private async getDictItemTree() {
  //   const res = await this.$api.common.getDictItemTree({
  //     dictCode: 'EnterpriseType'
  //   })
  //   this.enterpriseTypeList = (res?.data || [])
  // }

  private saveHeader() {
    if (this.enterpriseId) {
      this.$emit('next', {
        ...this.baseForm,
        enterpriseId: this.enterpriseId
      })
      return
    }

    const params = {
      ...this.baseForm
    }
    this.confirmLoading = true
    this.$api.register
      .enterpriseSaveHeader(params)
      .then((res: any) => {
        if (res.code === 200) {
          this.enterpriseId = res.data
          this.$emit('next', {
            ...this.baseForm,
            enterpriseId: this.enterpriseId
          })
        } else {
          if (res.data && res.data.errorLabels && res.data.errorLabels.length) {
            ;(this.$refs.ruleForm as any).addErrorLabels(res.data.errorLabels)
          }
        }
      })
      .finally(() => {
        this.confirmLoading = false
      })
  }

  // private getValidRules() {
  //   this.$api.register.enterpriseSaveHeaderValid().then( (res:any) => {
  //     this.formRules = res.data ? formatRules(res.data) : {}
  //   })
  // }
}
</script>

<style lang="less" scoped>
.relative {
  position: relative;
}
.mt30 {
  margin-top: 30px;
}
.mr30 {
  margin-right: 30px;
}
.wrapper {
  width: 700px;
  margin: 0 auto;
  color: #3e3e3e;
  .tips {
    font-size: 14px;
    font-weight: normal;
    color: rgba(0, 70, 156, 1);
    margin-bottom: 60px;
  }
  .type-options-label {
    width: 48px;
    text-align: right;
    font-size: 14px;
    font-weight: 600;
    span {
      color: red;
      font-weight: normal;
    }
  }

  .base-form-wrapper {
    margin-top: 80px;
    /deep/.search-btn {
      position: absolute;
      left: 0;
      bottom: -8px;
      width: 80px;
      height: 40px;
      font-size: 14px;
      background: #00469c;
      color: #fff;
      box-shadow: none;
    }
    .agree-wrapper {
      margin-bottom: 15px;
      color: #6b6565;
      font-size: 14px;
      .agree-user {
        color: #1455a4;
        cursor: pointer;
      }
      .agree-ys {
        color: #da3e3e;
        cursor: pointer;
      }
    }
  }

  .btn--wrap {
    margin-top: 160px;
  }
  /deep/.next-btn {
    width: 300px;
    height: 44px;
    color: #fff;
    font-size: 14px;
    background-color: #00469c;
  }
}
</style>
