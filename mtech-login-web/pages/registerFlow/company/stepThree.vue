<template>
  <div class="step-three--wrapper">
    <div class="main-des">
      {{ $t('请填写您的个人注册信息') }}
    </div>
    <PeopleRegister :enterpriseId="enterpriseId" ref="register" />

    <div class="footer--wrap">
      <mt-button
        :is-primary="true"
        class="footer-prev-btn"
        cssClass="next-btn"
        @click="handlePrev"
        >{{ $t('上一步') }}</mt-button
      >
      <mt-button :is-primary="true" cssClass="next-btn" @click="handleNext">{{
        $t('下一步')
      }}</mt-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
// import { formatRules } from '@/utils/index'
import PeopleRegister from '../components/PeopleRegister.vue'

@Component({
  components: {
    PeopleRegister
  }
  // async asyncData({ app, error }) {}
})
export default class Register<PERSON><PERSON><PERSON>hree extends Vue {
  @Prop()
  enterpriseId!: string

  handleNext() {
    // this.$emit('next')
    ;(this.$refs.register as any)
      .validForm()
      .then(() => {
        this.$emit('next')
      })
      .catch((err: any) => {
        this.$tips.error({
          title: this.$t('提示'),
          content: err.msg || this.$t('个人注册失败')
        })
      })
  }
  handlePrev() {
    this.$emit('prev')
  }
}
</script>

<style lang="less" scoped>
.step-three--wrapper {
  width: 700px;
  margin: 0 auto;
  text-align: center;
}
.main-des {
  color: rgba(0, 70, 156, 1);
  text-align: center;
  margin-bottom: 60px;
}
.footer--wrap {
  margin-top: 20px;
  text-align: center;
  padding-bottom: 30px;
}

/deep/.next-btn {
  width: 200px;
  height: 44px;
  color: #fff;
  font-size: 14px;
  background-color: #00469c;
}
</style>
