<template>
  <div>
    <mt-form class="service-info" ref="form" :model="formData" :rules="formRules">
      <mt-form-item prop="businessType" :label="$t('类型')">
        <BusinessType :type="firstData && firstData.businessType" @change="changeBusinessType" />
      </mt-form-item>

      <mt-form-item prop="industryList" :label="$t('所属行业')">
        <div class="service-item--wrap">
          <div class="service-btn--wrap">
            <span v-if="formData.industryList.length === 0" class="service-item--null">{{
              $t('您还没选择行业，请选择您的行业')
            }}</span>

            <span class="service-btn" @click="showIndustryDialog">
              <svg-icon icon-class="icon_login_choice"></svg-icon>
              {{ $t('选择') }}
            </span>
          </div>
          <div class="service-item--list" v-if="formData.industryList.length">
            <mt-tag
              class="service-item"
              v-for="(industry, index) in formData.industryList"
              :key="industry.dictCode"
              :showCloseIcon="true"
              @close="closeServiceItem(formData.industryList, index)"
            >
              {{ industry.dictName }}
            </mt-tag>
          </div>
        </div>
      </mt-form-item>

      <mt-form-item prop="serviceAreaList" :label="$t('服务区域')">
        <div class="service-item--wrap">
          <div class="service-btn--wrap">
            <span v-if="formData.serviceAreaList.length === 0" class="service-item--null">
              {{ $t('您还没选择服务，请选择您的服务区域') }}</span
            >

            <span class="service-btn" @click="showServiceAreaDialog">
              <svg-icon icon-class="icon_login_choice"></svg-icon>
              {{ $t('选择') }}
            </span>
          </div>
          <div class="service-item--list" v-if="formData.serviceAreaList.length">
            <mt-tag
              class="service-item"
              v-for="(serviceArea, index) in formData.serviceAreaList"
              :key="serviceArea.dictCode"
              :showCloseIcon="true"
              @close="closeServiceItem(formData.serviceAreaList, index)"
            >
              {{ serviceArea.dictName }}
            </mt-tag>
          </div>
        </div>
      </mt-form-item>

      <mt-form-item prop="productList" :label="$t('主要产品/服务')">
        <div class="service-item--wrap">
          <div class="service-btn--wrap">
            <div v-if="formData.productList.length === 0" class="service-item--null">
              <p>{{ $t('您还没选择产品/服务，请选择产品/服务') }}</p>
              <p>
                {{ $t('您最多可选择') }}<span class="service-item--tip">20</span>
                {{ $t('个分类，准确的选择您企业提供的产品与服务分类，更有利于您企业获取精准商机') }}
              </p>
            </div>
            <span class="service-btn" @click="showProductDialog">
              <svg-icon icon-class="icon_login_choice"></svg-icon>
              {{ $t('选择') }}
            </span>
          </div>
          <div class="service-item--list" v-if="formData.productList.length">
            <mt-tag
              class="service-item"
              v-for="(product, index) in formData.productList"
              :key="product.dictCode"
              :showCloseIcon="true"
              @close="closeServiceItem(formData.productList, index)"
            >
              {{ product.dictName }}
            </mt-tag>
          </div>
        </div>
      </mt-form-item>

      <mt-form-item prop="enterpriseLabelList" :label="$t('企业标签')">
        <div class="service-item--wrap">
          <div class="service-btn--wrap">
            <div v-if="formData.enterpriseLabelList.length === 0" class="service-item--null">
              <p>{{ $t('您还没选择企业标签，请选择企业标签') }}</p>
              <p>
                {{
                  $t(
                    '添加您企业能够提供的产品与服务相关的标签，例如基建、清洁用品、IT服务等，有利于您获得精准商机。您最多添加'
                  )
                }}<span class="service-item--tip">5</span>
                {{ $t('个标签，每个标签最多输入10个字符。') }}
              </p>
            </div>
            <span class="service-btn" @click="showEnterpriseLabelDialog">
              <svg-icon icon-class="icon_login_choice"></svg-icon>
              {{ $t('添加标签') }}
            </span>
          </div>
          <div class="service-item--list" v-if="formData.enterpriseLabelList.length">
            <mt-tag
              class="service-item"
              v-for="(enterpriseLabel, index) in formData.enterpriseLabelList"
              :key="enterpriseLabel.dictCode"
              :showCloseIcon="true"
              @close="closeServiceItem(formData.enterpriseLabelList, index)"
            >
              {{ enterpriseLabel.dictName }}
            </mt-tag>
          </div>
        </div>
      </mt-form-item>
    </mt-form>
    <SelectIndustryDialog v-model="isShowIndustryDialog" @save="saveIndustryData" />
    <ServiceAreaDialog v-model="isShowServiceAreaDialog" @save="saveServiceArea" />
    <SelectProductDialog v-model="isShowProductDialog" @save="saveProductData" />
    <SelectLabelDialog v-model="isShowEnterpriseLabelDialog" @save="saveLabelData" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import BusinessType from '../../components/BusinessType.vue'
import SelectIndustryDialog from '../../components/SelectIndustryDialog.vue'
import SelectProductDialog from '../../components/SelectProductDialog.vue'
import ServiceAreaDialog from '../../components/ServiceAreaDialog.vue'
import SelectLabelDialog from '../../components/SelectLabelDialog.vue'

@Component({
  layout: 'blank',
  components: {
    BusinessType,
    SelectIndustryDialog,
    ServiceAreaDialog,
    SelectProductDialog,
    SelectLabelDialog
  }
})
export default class ServiceInfo extends Vue {
  @Prop()
  firstData!: any

  isShowIndustryDialog = false
  isShowServiceAreaDialog = false
  isShowProductDialog = false
  isShowEnterpriseLabelDialog = false

  formData: any = {
    businessType: '',
    industryList: [],
    serviceAreaList: [],
    productList: [],
    enterpriseLabelList: []
  }

  formRules: any = {
    businessType: [{ required: true, message: this.$t('请选择类型'), trigger: 'blur' }],
    industryList: [{ required: true, message: this.$t('请选择所属行业'), trigger: 'blur' }],
    serviceAreaList: [{ required: true, message: this.$t('请选择服务区域'), trigger: 'blur' }],
    productList: [{ required: true, message: this.$t('请选择主要产品/服务'), trigger: 'blur' }],
    enterpriseLabelList: [{ required: true, message: this.$t('请选择企业标签'), trigger: 'blur' }]
  }

  get enterpriseId() {
    return this.firstData?.enterpriseId || ''
  }

  @Watch('firstData.businessType')
  onWatchFirstData(val: any) {
    // firstData
    if (val) {
      this.$set(this.formData, 'businessType', val)
    }
  }

  mounted() {
    // this.businessDataSource[0].content = this.$refs.business
  }

  changeBusinessType(type: number) {
    this.formData.businessType = type
  }

  showIndustryDialog() {
    this.isShowIndustryDialog = true
  }

  showServiceAreaDialog() {
    this.isShowServiceAreaDialog = true
  }

  showProductDialog() {
    this.isShowProductDialog = true
  }

  showEnterpriseLabelDialog() {
    this.isShowEnterpriseLabelDialog = true
  }

  closeServiceItem(list: any[], index: number) {
    list.splice(index, 1)
  }

  // 保存所属行业数据
  saveIndustryData(list: any[]) {
    this.formData.industryList = list.map((v) => {
      return {
        enterpriseId: this.enterpriseId,
        serviceType: 1,
        dictName: v.name,
        dictCode: v.itemCode
      }
    })
  }

  // 保存服务区域
  saveServiceArea(list: any[]) {
    this.formData.serviceAreaList = list.map((v) => {
      return {
        enterpriseId: this.enterpriseId,
        serviceType: 4,
        dictName: v.text,
        dictCode: v.id
      }
    })
  }

  // 保存产品服务数据
  saveProductData(list: any[]) {
    this.formData.productList = list.map((v) => {
      return {
        enterpriseId: this.enterpriseId,
        serviceType: 2,
        dictName: v.text,
        dictCode: v.categoryCode
      }
    })
  }

  // 保存标签数据
  saveLabelData(list: any[]) {
    this.formData.enterpriseLabelList = list.map((v) => {
      return {
        enterpriseId: this.enterpriseId,
        serviceType: 3,
        dictName: v.name,
        dictCode: v.itemCode
      }
    })
  }

  // 获取表单元素
  getFormData() {
    return new Promise((resolve, reject) => {
      try {
        ;(this.$refs.form as any).validate((valid: boolean) => {
          if (valid) {
            const { industryList, serviceAreaList, productList, enterpriseLabelList } =
              this.formData
            const param = [
              ...industryList,
              ...serviceAreaList,
              ...productList,
              ...enterpriseLabelList
            ]

            resolve({
              businessType: this.formData.businessType,
              enterpriseServiceDTOList: param
            })
          } else {
            reject(valid)
          }
        })
      } catch (error) {
        reject(error)
      }
    })
  }
}
</script>

<style lang="less" scoped>
.service-item--wrap {
  padding: 10px;
  border-bottom: 1px solid #e8e8e8;
}
.service-btn--wrap {
  line-height: 18px;
  text-align: center;
  .service-btn {
    color: #4d5b6f;
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
  }
}
.service-item--list {
  // max-height: 50px;
  overflow: auto;
}
.service-item--null {
  color: #9a9a9a;
  font-size: 12px;
}
.service-item--tip {
  color: #f92800;
}
</style>
