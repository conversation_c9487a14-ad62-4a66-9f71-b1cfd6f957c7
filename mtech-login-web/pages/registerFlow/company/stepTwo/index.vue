<template>
  <div class="step-two--wrap">
    <div class="hash-link">
      <div class="hash-item" :class="{ active: hashId === 0 }" @click="hashJump(0)">
        <a href="#pNode0">{{ $t('工商信息') }}</a>
      </div>
      <div class="hash-item" :class="{ active: hashId === 1 }" @click="hashJump(1)">
        <a href="#pNode1">{{ $t('服务信息') }}</a>
      </div>
      <div class="hash-item" :class="{ active: hashId === 2 }" @click="hashJump(2)">
        <a href="#pNode2">{{ $t('企业证明') }}</a>
      </div>
      <div class="hash-item" :class="{ active: hashId === 3 }" @click="hashJump(3)">
        <a href="#pNode3">{{ $t('发票信息') }}</a>
      </div>
    </div>

    <div class="form-content--wrap">
      <div class="main-des">
        {{
          $t(
            '请填写企业工商信息，服务信息，执照信息，发票信息，对于第三方接口带入的工商信息值，不允许被修改。'
          )
        }}
      </div>

      <div class="form-content--item mb--20" id="pNode0">
        <div class="title-bar">
          <p>{{ $t('工商信息') }}</p>
        </div>
        <BusinessContent
          ref="BusinessContent"
          class="ph--20 pv--32"
          :firstData="firstData"
          :enterpriseData="enterpriseData"
        />
      </div>

      <div class="form-content--item mb--20" id="pNode1">
        <div class="title-bar">
          <p>{{ $t('服务信息') }}</p>
        </div>
        <ServiceInfo
          ref="ServiceInfo"
          class="ph--20 pv--32"
          :firstData="firstData"
          :rules="formRules"
        />
      </div>

      <div class="form-content--item mb--20" id="pNode2">
        <div class="title-bar">
          <p>{{ $t('企业证明') }}</p>
        </div>
        <EnterpriseInfo
          ref="EnterpriseInfo"
          class="ph--20 pv--32"
          :enterpriseId="enterpriseId"
          :rules="formRules"
        />
      </div>

      <div class="form-content--item mb--20" id="pNode3">
        <div class="title-bar">
          <p>{{ $t('发票信息') }}</p>
        </div>
        <InvoiceInfo
          ref="InvoiceInfo"
          class="ph--20 pv--32"
          :enterpriseId="enterpriseId"
          :enterpriseData="enterpriseData"
        />
      </div>
    </div>

    <div class="btn--wrap">
      <mt-button cssClass="next-btn" class="mr--30" :isPrimary="true" @click="handlePrev">{{
        $t('上一步')
      }}</mt-button>
      <mt-button
        cssClass="next-btn"
        :loading="confirmLoading"
        :isPrimary="true"
        @click="handleNext"
        >{{ $t('下一步') }}</mt-button
      >
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import BusinessContent from './BusinessContent.vue'
import ServiceInfo from './ServiceInfo.vue'
import EnterpriseInfo from './EnterpriseInfo.vue'
import InvoiceInfo from './InvoiceInfo.vue'
// import { formatRules } from '@/utils/index'

@Component({
  layout: 'blank',
  components: {
    BusinessContent,
    ServiceInfo,
    EnterpriseInfo,
    InvoiceInfo
  },
  middleware: ['config']
  // async asyncData({ app, error }) {}
})
export default class RegisterFlowTwo extends Vue {
  @Prop()
  enterpriseId!: string

  @Prop()
  firstData!: any

  hashId = 0

  hadCreated = false
  confirmLoading = false
  enterpriseData: any = {}

  @Watch('firstData.enterpriseName', { immediate: true })
  onWatchEnterprise(newVal: string) {
    if (newVal) {
      this.$api.register
        .queryEnterpriseInfoByName({
          enterpriseName: newVal
        })
        .then((res: any) => {
          if (res.code === 200) {
            this.enterpriseData = res.data
          }
        })
    }
  }

  formRules: any = {}

  mounted() {
    // this.getValidRules() // TODO: 本地校验信息更完善
  }

  hashJump(id: number = 0) {
    this.hashId = id
    // let idName = `pNode${id}`
    //   document.getElemetnById(idName).scrollIntoView(true)
  }

  handleNext() {
    Promise.all(this.getFormRefs().map((v) => v.getFormData()))
      .then((res) => {
        const params = res.reduce((total, curr) => {
          total = {
            ...total,
            ...curr
          }
          return total
        }, {})
        this.saveEnterpriseData(params)
        // this.$emit('next')
      })
      .catch((err) => {
        this.$tips.error({
          title: this.$t('提示'),
          content: err.msg || this.$t('请检查输入的值')
        })
      })
  }
  handlePrev() {
    this.$emit('prev')
  }

  private saveEnterpriseData(params: any) {
    if (this.hadCreated) {
      this.$emit('next')
      return
    }
    this.confirmLoading = true
    this.$api.register
      .enterpriseSaveMain(params)
      .then((res: any) => {
        if (res.code === 200) {
          this.$emit('next')
          this.hadCreated = true
        } else {
          if (res.data && res.data.errorLabels && res.data.errorLabels.length) {
            this.getFormRefs().forEach((v) => v.addErrorLabels(res.data.errorLabels))
          }
        }
      })
      .catch((err: any) => {
        this.$tips.error({
          title: this.$t('提示'),
          content: err.msg || this.$t('请检查输入的值')
        })
      })
      .finally(() => {
        this.confirmLoading = false
      })
  }

  private getFormRefs() {
    const businessContentRef: any = this.$refs.BusinessContent
    const serviceInfoRef: any = this.$refs.ServiceInfo
    const enterpriseInfoRef: any = this.$refs.EnterpriseInfo
    const invoiceInfoRef: any = this.$refs.InvoiceInfo

    return [businessContentRef, serviceInfoRef, enterpriseInfoRef, invoiceInfoRef]
  }
}
</script>

<style scoped lang="less">
@media screen and (max-width: 1200px) {
  .hash-link {
    display: none;
  }
}
.mb--20 {
  margin-bottom: 20px;
}
.mr--30 {
  margin-right: 30px;
}
.ph--20 {
  padding-left: 32px;
  padding-right: 32px;
}
.pv--32 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.step-two--wrap {
  width: 1200px;
  margin: 0 auto;
  position: relative;
  padding-bottom: 30px;
}

.hash-link {
  width: 200px;
  position: fixed;
  left: 50%;
  top: 264px;
  transform: translateX(-850px);
  &::before {
    content: ' ';
    display: inline-block;
    width: 1px;
    background: rgba(156, 156, 156, 1);
    position: absolute;
    left: 32px;
    height: calc(100% - 36px);
    top: 50%;
    transform: translateY(-50%);
  }
  .hash-item {
    cursor: pointer;
    padding-left: 50px;
    width: 200px;
    height: 50px;
    line-height: 50px;
    position: relative;
    a {
      text-decoration: none;
      color: #3e3e3e;
    }
    &::before {
      content: ' ';
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 1px solid rgba(156, 156, 156, 1);
      position: absolute;
      left: 24px;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 100%;
      background: #fff;
      z-index: 2;
    }
  }
  .active {
    color: #00469c;
    a {
      text-decoration: none;
      color: #00469c;
    }
    &::before {
      content: ' ';
      display: inline-block;
      width: 15px;
      height: 15px;
      position: absolute;
      left: 24px;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 100%;
      z-index: 2;
      border: 4px solid #fff;
      background: #00469c;
      box-shadow: 0 0 0 1px #00469c;
    }
  }
}

.form-content--wrap {
  .main-des {
    font-size: 14px;
    // height: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(0, 70, 156, 1);
    text-align: center;
    margin-bottom: 60px;
  }

  .form-content--item {
    border: 1px solid #e8e8e8;

    .title-bar {
      border-bottom: 1px solid #e8e8e8;
      line-height: 46px;
      height: 46px;
      background: #fafafa;
      color: #6386c1;
      padding-left: 20px;
    }
  }
}
.btn--wrap {
  margin-top: 160px;
  text-align: center;
  margin-bottom: 30px;
}
/deep/.next-btn {
  width: 300px;
  height: 44px;
  color: #fff;
  font-size: 14px;
  background-color: #00469c;
}
</style>
