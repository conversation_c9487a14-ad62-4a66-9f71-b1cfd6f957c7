<template>
  <div class="enterprise-info--wrap">
    <!-- <div class="enterprise-btn--wrap">
      <span class="service-btn" @click="showInfoData">
        <svg-icon icon-class="icon_login_choice"></svg-icon>
        新增附件
      </span>
    </div> -->

    <mt-data-grid
      class="data-grid--content"
      :dataSource="dataSource"
      :columnData="columnData"
      :rowTemplate="rowTemplate"
      :allowPaging="false"
      ref="eventDataGrid"
    ></mt-data-grid>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { enterpriseInfoColumnData } from './config/index'
import EnterpriseInfoRow from '../../components/EnterpriseInfoRow.vue'
import bus from './config/eventBus'

@Component({
  layout: 'blank',
  components: {},
  middleware: ['config']
})
export default class EnterpriseInfo extends Vue {
  @Prop()
  enterpriseId!: string

  isShowInfoData = false
  dataSource: any[] = [
    {
      index: 0,
      fileId: '',
      fileType: 1,
      fileRule: this.$t('请上传企业资质证明加盖公章，上传文件不超过5MB'),
      template: '',
      remark: ''
    },
    {
      index: 1,
      fileId: '',
      fileType: 2,
      fileRule: this.$t('请下载企业授权委托模板并填写，填写完成后加盖公章并上传扫描件'),
      template: '',
      remark: ''
    }
  ]
  rowTemplate = function () {
    return { template: EnterpriseInfoRow }
  }
  columnData = enterpriseInfoColumnData

  mounted() {
    bus.$on('EnterpriseInfoRow-Save', this.saveFileId)
    bus.$on('EnterpriseInfoRow-Delete', this.deleteFileId)
  }

  showInfoData() {
    this.isShowInfoData = true
  }

  getFormData() {
    return new Promise((resolve, reject) => {
      try {
        const params: any[] = []
        let isValid = true

        this.dataSource.forEach((data) => {
          if (isValid && data.fileId) {
            params.push({
              fileId: data.fileId,
              fileType: data.fileType,
              enterpriseId: this.enterpriseId
            })
          } else {
            isValid = false
          }
          bus.$emit('EnterpriseInfoRow-Error', data)
        })
        if (isValid) {
          resolve({
            enterpriseInfoProofFileDTOList: params
          })
        } else {
          reject(isValid)
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  private saveFileId(param: any) {
    this.dataSource[param.index]['fileId'] = param.id
  }

  private deleteFileId(param: any) {
    this.dataSource[param.index]['fileId'] = ''
  }
}
</script>

<style scoped lang="less">
.data-grid--content {
  /deep/ .e-headertext {
    color: #292929;
    font-weight: 500;
    font-size: 14px;
  }
}
.enterprise-btn--wrap {
  text-align: right;
  margin-bottom: 15px;
  .service-btn {
    cursor: pointer;
  }
}
</style>
