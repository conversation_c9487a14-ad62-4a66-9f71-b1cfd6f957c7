export default ($axios: any) => ({
  // 平台账号注册
  register: (params: Object) => {
    return $axios.$post('/iam/common/account/register', params)
  },
  // 获取注册时的手机验证码
  getVerifyCode: (params: Object) => {
    return $axios.$post('/iam/common/account/sms_register_code', params)
  },
  // // 获取短信验证码-检查手机号有效性 
  // mobileCheckCode(params: any) {
  //   return $axios.$post('/iam/common/account/sms_mobile_check_code', params)
  // },
  // 获取注册时的邮箱验证码
  getMailSmsCode: (params: Object) => {
    return $axios.$post('/iam/common/account/mail_register_code', params)
  },
  // 验证用户名是否存在
  checkAccountName: (params: Object) => {
    return $axios.$post('/user/common/register/checkLoginName', params)
  },
  // 注册表单校验规则
  registerValidate: () => {
    return $axios.$get('/user/common/register/register-valid')
  },
  // 保存企业头部信息
  enterpriseSaveHeader: ( params: any) => {
    return $axios.$post('/platform/common/enterprise/saveHeader', params)
  },
  // 获取企业头部信息的校验规则
  enterpriseSaveHeaderValid() {
    return $axios.$get('/platform/common/enterprise/saveHeader-valid')
  },

  // 保存企业详情信息
  enterpriseSaveMain: ( params: any) => {
    return $axios.$post('/platform/common/enterprise/saveMain', params)
  },
  // 获取企业详情信息的校验规则
  enterpriseSaveMainValid: ( params: any) => {
    return $axios.$get('/platform/common/enterprise/saveMain-valid', params)
  },
  // 验证企业名称是否已经注册 
  validateCompanyName: ( params: any) => {
    return $axios.$get('/platform/common/enterprise/validateCompanyName', {
      params
    })
  },

  // 绑定管理员账号并提交审核
  bindAdminId: (params: any) => {
    return $axios.$get('/platform/admin/enterprise/bindAdminId', {
      params
    })
  },

  // 获取产品服务地址 platform-eshop-tree
  getEshopTree(params: any = {}) {
    return $axios.$post('/masterDataManagement/common/category/platform-product-tree',params )
  },

  //  根据父级获取产品服务
  findPlatformProduct(params: any = {}) {
    return $axios.$post('/masterDataManagement/common/category/find-platform-product-child-categorys', params)
  },

  // 查询征信企业基本信息接口
  queryEnterpriseInfoByName(params: any = {}) {
    return $axios.$get('/platform/common/enterprise/queryEnterpriseInfoByName', {
      params
    })
  }


})
