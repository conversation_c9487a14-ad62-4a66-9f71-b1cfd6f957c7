export default ($axios: any) => ({ 
  // 条件查询国家或地区 
  getCountryList: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/country/criteria-query', params)
  },
  // 查询所有国家或地区 
  getAllCountryList: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/country/queryAll', params)
  },
  // 根据parentCode获取地址 
  getAreaListByParent: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/area/selectByParentCode', params)
  },

  // 条件查询获取地址
  getAreaListByCriteria: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/area/criteria-query', params)
  },

  // 根据字典类型代码获取字典明细树
  getDictItemTree: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/dict-item/item-tree', params)
  },

  // 条件查询字典明细树
  getDictItemTreeByCriteria: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/dict-item/criteria-query', params)
  },

  // 获取币种类型
  getCapitalCurrency(params: any) {
    return $axios.$get('/masterDataManagement/common/currency/queryActiveCurrency', params)
  },


  // 获取系统相关配置
  getApplication: (params: string) => {
    return $axios.$get(`/user/common/application/queryloginpage?system=${params}`)
  },
  // 获取用户信息
  getUserInfo: () => {
    // return $axios.$get('/user/user/userInfo/getUserInfo')
    return $axios.$get('/iam/common/account/get-user-info')
  },

  // 获取语言列表信息
  getLangList: () => {
    // return $axios.$get('/user/user/userInfo/getUserInfo')
    return $axios.$get('/i18n/common/lang/list')
  }
})
