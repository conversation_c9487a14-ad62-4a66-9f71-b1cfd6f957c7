export default ($axios: any) => ({
  /**
   * 修改手机号
   * start
   */
  // 获取验证码
  getVerifyCodeForPhone: (params: Object) => {
    return $axios.$post('/user/user/phone/getVerificationCode', params)
  },
  // 验证原有手机号（点击下一步）
  updatePhoneNext: (params: Object) => {
    return $axios.$post('/user/user/phone/renewPhoneNext', params)
  },
  // 验证新手机号（确认）
  updatePhoneConfirm: (params: Object) => {
    return $axios.$post('/user/user/phone/renewPhoneConfirm', params)
  },
  // 获取校验规则
  validForPhoneNext: () => {
    return $axios.$get('/user/user/phone/renewPhoneNext-valid')
  },
  validForPhoneConfirm: () => {
    return $axios.$get('/user/user/phone/renewPhoneConfirm-valid')
  },
  /** 修改手机号 end */

  /**
   * 找回密码
   * start
   */
  // 获取验证码
  getVerifyCodeForPassword: (params: Object) => {
    return $axios.$post('/iam/common/account/sms_password_reset_code', params)
  },
  // 验证手机号（下一步）
  updatePasswordNext: (params: Object) => {
    return $axios.$post('/iam/common/account/sms_password_reset_code/check', params)
  },
  // 获取邮箱验证码
  getVerifyCodeForPasswordMail: (params: Object) => {
    return $axios.$post('/iam/common/account/mail_password_reset_code', params)
  },
  // 验证邮箱号号（下一步）
  updatePasswordNextMail: (params: Object) => {
    return $axios.$post('/iam/common/account/mail_password_reset_code/check', params)
  },
  // 更新密码
  updatePasswordConfirm: (params: Object) => {
    return $axios.$post('/iam/common/account/password/reset', params)
  },
  // 获取校验规则
  // validForPasswordNext: () => {
  //   return $axios.$get('/user/common/password/updatePwdNext-valid')
  // },
  // validForPasswordConfirm: () => {
  //   return $axios.$get('/user/common/password/updatePwdConfirm-valid')
  // },
  /** 重置密码 end */

  /** 修改密码 */
  // 修改密码校验规则
  validPassword: () => {
    return $axios.$get('/user/user/userInfo/updateUserPwd-valid')
  },
  // 修改密码
  updatePassword: (params: object) => {
    return $axios.$put('/user/user/userInfo/updateUserPwd', params)
  }
  /** 修改密码  end  */
})
