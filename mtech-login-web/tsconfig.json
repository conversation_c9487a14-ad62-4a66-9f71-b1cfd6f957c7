{"compilerOptions": {"target": "ES2018", "module": "ESNext", "moduleResolution": "Node", "lib": ["ESNext", "ESNext.AsyncIterable", "DOM"], "esModuleInterop": true, "allowJs": true, "sourceMap": true, "strict": true, "noEmit": true, "experimentalDecorators": true, "suppressImplicitAnyIndexErrors": true, "baseUrl": ".", "paths": {"~/*": ["./*"], "@/*": ["./*"]}, "types": ["@types/node", "@nuxt/types"]}, "exclude": ["node_modules", ".nuxt", "dist"]}