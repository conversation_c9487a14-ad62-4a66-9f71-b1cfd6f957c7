export default async function ({ app, store, route, error }: any) {
  let system: string = ''
  if (route.path === '/login' || route.path === '/') {
    // 如果在login页面手动改了路径上的system，则更新到cookie。
    system = route.query.system || ''
    let domain = '.qeweb.com'
    if (process.env.NODE_ENV === 'development') {
      domain = 'localhost'
    }
    app.$cookies.set('system', system, { domain, path: '/' })
  } else {
    // 其他页面取cookie的值
    system = app.$cookies.get('system') || ''
  }
  // 要请求接口的情况：vuex里数据为空 或 system值和cookie里不一样
  // if (Object.keys(store.state.application).length === 0 || store.state.application.system !== app.$cookies.get('system')) {
  //   try {
  //     const { code, data } = await app.$api.common.getApplication(system)
  //     console.log(data)
  //     if (code == 200 && data) {
  //       data.system = system
  //       store.dispatch('setApplication', data)
  //       data.applicationName && (app.head.title = data.applicationName)
  //       if (data.faviconUrl) {
  //         let index = app.head.link.findIndex((item: any) => item.rel === 'icon')
  //         if (index !== -1) {
  //           app.head.link[index] = { rel: 'icon', type: 'image/x-icon', href: data.faviconUrl }
  //         }
  //       }
  //     }
  //   } catch (err) {
  //     console.log('config.ts中，进入了500', err)
  //     error({ statusCode: 500 })
  //   }
  // }
}
