export default async function ({ app, store, error }: any) {
  if(!process.client) return
  // 国家列表
  if (store.state && store.state.countryList && store.state.countryList.length <= 1) {
    try {
      const res = await app.$api.common.getAllCountryList()
      if (res.code == 200 && res.data && res.data.length) {
         store.dispatch('setCountryList', res.data)
      }
    } catch (err) {
      console.log('countyr.ts中，进入了500')
      error({ statusCode: 500 })
    }
  }
  // 默认省份
  if (store.state && store.state.defaultProvinceList && store.state.defaultProvinceList.length <= 1) {
    try {
      //开始请求省份
      console.log('开始请求省份');
      const res = await app.$api.common.getAreaListByParent({
        parentCode: ''
     })
      if (res.code == 200 && res.data && res.data.length) {
         store.dispatch('setDefaultProvinceList', res.data)
      }
    } catch (err) {
      console.log('省份.ts中，进入了500')
      error({ statusCode: 500 })
    }
  }
}
