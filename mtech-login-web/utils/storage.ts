// 获取 localStorage
function getLStorage(storageKey: string): object | string | undefined {
  let storage: object | string | undefined = ''
  storage = getStorageVal(1, storageKey)
  return storage
}

// 设置 localStorage
function setLStorage(storageKey: string, storageValue: any): void {
  setStorageVal(1, storageKey, storageValue)
}

// 获取 sessionStorage
function getSStorage(storageKey: string): object | string | undefined {
  let storage: object | string | undefined = ''
  storage = getStorageVal(2, storageKey)
  return storage
}

// 设置 sessionStorage
function setSStorage(storageKey: string, storageValue: any): void {
  setStorageVal(2, storageKey, storageValue)
}
// Storage取值
function getStorageVal(type: number, storageKey: string) {
  let storageVal: object | string | undefined
  let stroVal: string = ''
  let storage: any
  if (type === 1) {
    // storage = window.localStorage
  } else if (type === 2) {
    // storage = window.sessionStorage
  }
  stroVal = storage.getItem(storageKey) || ''
  try {
    storageVal = JSON.parse(stroVal)
  } catch (error) {
    storageVal = storage.getItem(storageKey) || ''
  }
  return storageVal
}

// Storage设值
function setStorageVal(type: number, storageKey: string, storageValue: any) {
  let storage: any
  if (type === 1) {
    // storage = window.localStorage
  } else if (type === 2) {
    // storage = window.sessionStorage
  }
  if (typeof storageValue === 'object') {
    storage.setItem(storageKey, JSON.stringify(storageValue))
  } else {
    storage.setItem(storageKey, storageValue)
  }
}

export { getLStorage, setLStorage, getSStorage, setSStorage }
