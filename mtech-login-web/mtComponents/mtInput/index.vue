<template>
  <div
    class="input__wrap"
    :class="[$slots.preIcon && 'input__wrap--prefix', isError && 'is-error']"
    :style="{ width: width + 'px' }"
    @keyup.enter.prevent="handleKeyup"
    @keydown.enter.prevent="handleKeydown"
    @keypress.enter.prevent="handleKeypress"
  >
    <template v-if="type !== 'textarea'">
      <div v-if="countryPhone" class="select__wrap">
        <ejs-dropdownlist
          :value="selectedCountry"
          :width="50"
          :popup-width="80"
          :allow-filtering="true"
          :data-source="paramList"
          :readonly="dropdownReadonly"
          @select="handleDropdownSelect"
        ></ejs-dropdownlist>

        <!-- <mt-select
          width="100"
          popupWidth="200"
          :value="selectedCountry"
          :dataSource="paramList"
          :showClearButton="true"
          :allowFiltering="true"
          :filtering="onProvinceFiltering"
          :readonly="dropdownReadonly"
          :fields="fields"
          :itemTemplate="iTemplate"
          @select="handleDropdownSelect"
        ></mt-select> -->
      </div>
      <input
        ref="input"
        class="input__inner"
        v-bind="$attrs"
        :type="type"
        :disabled="readonly"
        :placeholder="placeholder"
        :maxlength="maxlength"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @change="handleChange"
      />
      <div v-if="$slots.back" class="input-behind">
        <slot name="back"></slot>
      </div>
      <!-- 前置图标 -->
      <span v-if="$slots.preIcon" class="input-prefix">
        <slot name="preIcon"></slot>
      </span>
      <!-- 后置图标 -->
      <span v-if="$slots.sufIcon" class="input-suffix">
        <slot name="sufIcon"></slot>
      </span>
    </template>
    <textarea v-else ref="textarea"></textarea>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Mixins, Prop, Watch, Inject } from 'vue-property-decorator'
import Emitter from '~/utils/mixins/emitter'
import { Query } from '@syncfusion/ej2-data'

@Component
export default class MtInput extends Mixins(Emitter) {
  @Inject({ from: 'mtFormItem', default: '' }) readonly mtFormItem!: any
  /* 对应vue-js的props -start- */
  @Prop({ type: [String, Number], required: false, default: '' }) readonly value!: string | number
  @Prop({ type: String, required: false, default: null }) readonly title!: string
  @Prop({ type: Boolean, required: false, default: false }) readonly required!: boolean
  @Prop({ type: Boolean, required: false, default: false }) readonly disabled!: boolean
  @Prop({ type: Boolean, required: false, default: false }) readonly size!: boolean
  // 国际手机号
  @Prop({ type: Boolean, required: false, default: false }) readonly countryPhone!: boolean // 是否国际手机号
  @Prop({ type: String, required: false, default: '+86' }) readonly selectedCountry!: string // 默认选中的国家
  @Prop({ type: Boolean, required: false, default: false }) readonly dropdownReadonly!: boolean // 默认选中的国家
  // 原组件的props
  @Prop({ type: String, required: false, default: null }) readonly type!: string
  @Prop({ type: String, required: false, default: null }) readonly placeholder!: string
  @Prop({ type: Number, required: false, default: null }) readonly maxlength!: number
  @Prop({ type: Boolean, required: false, default: false }) readonly readonly!: boolean
  @Prop({ type: Boolean, required: false, default: false }) readonly showClearButton!: boolean
  @Prop({ type: [String, Number], required: false, default: null }) readonly width!: string | number
  @Prop({ type: String, required: false, default: 'on' }) readonly autocomplete!: string
  @Prop({ type: String, required: false, default: '' }) readonly cssClass!: string
  /* props -end- */
  paramList: object[] = [
    {
      code: '+86',
      text: '+86'
      // text: '中国',
    },
    {
      code: '+852',
      text: '+852'
      // text: '香港',
    },
    {
      code: '+853',
      text: '+853'
      // text: '澳门',
    }
  ]

  fields: object = {
    text: 'code',
    value: 'value'
  }

  iTemplate: any = function () {
    return {
      template: Vue.component('ItemTemplate', {
        template: '<div>{{data.text}}-{{data.value}}</div>',
        data() {
          return {
            data: {}
          }
        }
      })
    }
  }

  /** computed -start */
  get validateState() {
    return this.mtFormItem ? this.mtFormItem.validateState : ''
  }

  get isError() {
    return this.validateState === 'error'
  }

  get cssClassStr() {
    return this.disabled
      ? this.cssClass + ' e-filled'
      : this.validateState === 'error'
      ? this.cssClass + ' e-error'
      : this.cssClass
  }

  get nativeInputValue() {
    return this.value === null || this.value === undefined ? '' : String(this.value)
  }
  /** computed -end */

  @Watch('nativeInputValue')
  onValue() {
    this.setNativeInputValue()
  }

  created() {
    // if (this.countryPhone && this.$store.state && this.$store.state.countryList && this.$store.state.countryList.length) {
    //   const { countryList } = this.$store.state
    //   this.paramList = countryList
    // }
  }

  mounted() {
    this.setNativeInputValue()
  }

  // 筛选
  onProvinceFiltering(e: any) {
    // console.log('筛选--', e)
    var searchData = this.paramList
    // console.log('列表', this.paramList)
    var query = new Query()
    query = e.text != '' ? query.where('text', 'contains', e.text, true) : query
    e.updateData(searchData, query)
  }

  /* methods *start*  */
  setNativeInputValue() {
    const input = this.getInput()
    if (!input) return false
    if ((input as any).value === this.nativeInputValue) return false
    ;(input as any).value = this.nativeInputValue
  }

  getInput() {
    return this.$refs.input || this.$refs.textarea
  }

  /** 方法 */
  focusIn() {
    const input = this.getInput()
    if (!input) return false
    ;(input as any).focus()
  }

  /** emit */
  handleBlur(event: any): void {
    this.$emit('blur', event.target.value)
    this.dispatch('MtFormItem', 'mt.form.blur', [this.value])
  }

  handleFocus(event: any): void {
    this.$emit('focus', event.target.value)
  }

  handleInput(event: any): void {
    this.$emit('input', event.target.value)
    this.$nextTick(this.setNativeInputValue)
  }

  handleChange(event: any): void {
    this.$emit('change', event.target.value)
  }

  handleDropdownSelect(event: any): void {
    this.$emit('select', event)
  }

  handleKeyup(event: any) {
    this.$emit('keyup', event)
  }

  handleKeydown(event: any) {
    this.$emit('keydown', event)
  }

  handleKeypress(event: any) {
    this.$emit('keypress', event)
  }
  /* methods *end*  */
}
</script>

<style lang="less" scoped>
/** 重置样式 */
input[type='text']:focus,
input[type='password']:focus {
  border: none;
  background: #f9f9f9;
  outline: none;
}
.input__inner {
  background: none;
  outline: none;
  border: none;
  width: 100%;
}
::-webkit-input-placeholder {
  /* WebKit, Blink, Edge */
  color: #999;
}
:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #999;
}
::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #999;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #999;
}

.input__wrap {
  width: 100%;
  height: 48px;
  display: inline-flex;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
  padding: 10px 0;
  position: relative;
  &.is-error {
    border-bottom: 1px solid #f44336;
  }
}
.input__wrap--prefix {
  padding-left: 22px;
}
.input__inner {
  font-size: 14px;
  display: inline-block;
  height: 30px;
  line-height: 30px;
  padding: 0 40px 0 15px;
}
.input-prefix {
  width: 22px;
  height: 100%;
  position: absolute;
  left: 0px;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  > img {
    // width: 22px;
    height: 20px;
  }
}
.input-behind {
  // margin-right: 10px;
}
.select__wrap {
  padding-left: 20px;
}
input[type='text'][data-v-c2b12c7c]:focus,
input[type='password'][data-v-c2b12c7c]:focus {
  background: transparent;
}
</style>
