<template>
  <div id="mtLoading"></div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
// import { createSpinner, showSpinner, hideSpinner } from '@syncfusion/ej2-popups'
@Component
export default class MtLoading extends Vue {
  duration: number = 18000 // 默认5分钟
  isShow: boolean = false

  mounted() {
    try {
      // createSpinner({ target: document.getElementById('mtLoading') as HTMLElement })
      // if (this.isShow) {
      //   showSpinner(document.getElementById('mtLoading') as HTMLElement)
      // } else {
      //   hideSpinner(document.getElementById('mtLoading') as HTMLElement)
      // }
      // setInterval(() => {
      //   hideSpinner(document.getElementById('mtLoading') as HTMLElement)
      // }, this.duration)
    } catch (error) {
      error
    }
  }
}
</script>
