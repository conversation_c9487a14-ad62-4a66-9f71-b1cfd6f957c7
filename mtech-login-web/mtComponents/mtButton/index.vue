<template>
  <div class="mt-btn" style="display: inline-block">
    <!-- <ejs-button
      ref="mtBtnRef"
      type="button"
      :content="label"
      :css-class="cssClass"
      :disabled="disabled"
      :icon-css="iconCss"
      :icon-position="iconPosition"
      :is-primary="isPrimary"
      :is-toggle="isToggle"
      :enable-html-sanitizer="enableHtmlSanitizer"
      :enable-persistence="enablePersistence"
      :enable-rtl="enableRtl"
      @click.native="handleClick"
      @focusIn="handleFocusIn"
    >
      <template v-if="!label">
        <slot></slot>
      </template>
    </ejs-button> -->
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
// import { ButtonPlugin } from '@syncfusion/ej2-vue-buttons'
// Vue.use(ButtonPlugin)

@Component
export default class MtButton extends Vue {
  /** props start */
  @Prop({ type: String, required: false, default: '' }) readonly type!: string
  // 原组件
  @Prop({ type: String, required: false, default: '' }) readonly content!: string
  @Prop({ type: String, required: false, default: '' }) readonly cssClass!: string
  @Prop({ type: Boolean, required: false, default: false }) readonly disabled!: boolean
  @Prop({ type: Boolean, required: false, default: false }) readonly enableHtmlSanitizer!: boolean
  @Prop({ type: Boolean, required: false, default: false }) readonly enablePersistence!: boolean
  @Prop({ type: Boolean, required: false, default: false }) readonly enableRtl!: boolean
  @Prop({ type: String, required: false, default: '' }) readonly iconCss!: string
  @Prop({ type: String, required: false, default: 'left' }) readonly iconPosition!: string
  @Prop({ type: Boolean, required: false, default: false }) readonly isPrimary!: boolean
  @Prop({ type: Boolean, required: false, default: false }) readonly isToggle!: boolean
  /** props end */

  // computed
  get label() {
    let val = ''
    if (this.type) {
      if (this.type == 'return') {
        val = '返回'
      } else if (this.type == 'submit') {
        val = '提交'
      } else if (this.type == 'active') {
        val = '激活'
      } else if (this.type == 'check') {
        val = '审批'
      } else if (this.type == 'publish') {
        val = '发布'
      } else if (this.type == 'save') {
        val = '保存'
      } else if (this.type == 'close') {
        val = '关闭'
      } else if (this.type == 'reject') {
        val = '驳回'
      } else if (this.type == 'upload') {
        val = '上传'
      }
    }
    if (this.content) {
      val = this.content
    }
    return val
  }

  // 方法
  handleClick(): void {
    this.$emit('click')
  }

  handleFocusIn(e: Object): void {
    this.$emit('focus-in', e)
  }
}
</script>

<style></style>
