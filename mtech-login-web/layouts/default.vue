<template>
  <div
    :class="[
      'container',
      { 'container-bg': $route.path == '/register' || $route.path == '/login' }
    ]"
  >
    <div :class="$route.path != '/resetPassword' ? 'header2' : 'header'">
      <!-- <img :src="logoUrl" alt="" @click="toUrl" /> -->
      <!-- <img v-if="$route.path != '/resetPassword'" src="~assets/images/logo.png" alt="" @click="toUrl" />  -->

      <!-- <img v-else src="~assets/images/logo.png" alt="" @click="toUrl" /> -->
      <!-- <img v-else src="~assets/images/logo_blue.png" alt="" @click="toUrl" /> -->
    </div>
    <div class="main">
      <!-- <div
        class="bg"
        :style="{
          backgroundImage: 'url(' + application.backgroundUrl + ')'
        }"
      >
      </div>
      -->
      <!-- <img src="~/assets/images/bg.png" alt=""> -->
      <!-- <div class="bg" style="background-image: url('~assets/images/bg.png')"></div> -->
      <client-only>
        <Nuxt />
      </client-only>
    </div>
    <!-- <div :class="['footer',  $route.path == '/resetPassword'&&'footer-white-bg']">
      <p>@Copyright 企汇购信息技术（苏州）有限公司</p>
      <p class="footer-beian" @click="openBeian">备案号：沪ICP备 14039535号-6</p>
    </div> -->
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
@Component({})
export default class Layout extends Vue {
  application: object = {}
  logoUrl: string = ''
  url: string = ''

  mounted() {
    if (this.$cookies.get('locale')) {
      this.$changeL(this.$cookies.get('locale'))
    }

    if (this.$store.state && this.$store.state.application) {
      const { application } = this.$store.state

      this.logoUrl = application.logoUrl
      this.url = application.url
      this.application = application
    }
  }

  openBeian() {
    window.open('https://beian.miit.gov.cn/', '_blank')
  }

  toUrl() {
    if (this.url) {
      window.location.href = this.url
    }
  }
}
</script>
<style>
html {
  font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  word-spacing: 1px;
  -ms-name-size-adjust: 100%;
  -webkit-name-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.button--green {
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #3b8070;
  color: #3b8070;
  name-decoration: none;
  padding: 10px 30px;
}

.button--green:hover {
  color: #fff;
  background-color: #3b8070;
}

.button--grey {
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #35495e;
  color: #35495e;
  name-decoration: none;
  padding: 10px 30px;
  margin-left: 15px;
}

.button--grey:hover {
  color: #fff;
  background-color: #35495e;
}

/*
#__nuxt {
  width: 100%;
  height: 100%;
} */
#__layout {
  /* width: 100vw;
  height: 100vh; */
}
.default-layout {
  width: 100vw;
  height: 100vh;
}
.default-layout .main-content {
  width: 100%;
  height: calc(100% - 50px);
  padding-top: 2px;
}
.default-layout #layout-right {
  height: 100%;
}
.layout-right {
  height: 100%;
  overflow: auto;
}
</style>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-width: 100vw;
  min-height: 100vh;
  position: relative;
  // background: #f0f0f1;
  background: #fff;

  &.container-bg {
    background-image: url('~assets/images/bg_no_logo.jpeg');
    background-repeat: no-repeat;
    background-position: center top;
    // background-size: auto 100%;
    background-size: 100% 100%;
  }
}
.header {
  top: 10px;
  border-bottom: 2px solid #e8e8e8;
  // width: 90vw;
  margin: 10px 40px;
  padding: 0 0 0 10px;
  z-index: 1;
}
.header2 {
  position: absolute;
  left: 80px;
  top: 20px;
  z-index: 1;
}
.main {
  width: 100%;
  // flex: 1;
  height: 100%;
  // min-height: 800px;
  // background: #f0f0f1;
  position: relative;
  .bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: auto 100%;
    // background-image: url('~assets/images/bg.png')
  }
}
.footer {
  // width: 100%;
  text-align: center;
  font-size: 14px;
  // color: #444344;
  color: #fff;
  text-overflow: ellipsis;
  white-space: nowrap;
  // padding-left: 100px;
  // padding-bottom: 80px;
  text-align: left;
  position: absolute;
  left: 0;
  bottom: 0;
  .footer-beian {
    cursor: pointer;
  }

  &.footer-white-bg {
    color: #3e3e3e;
  }
}
</style>
