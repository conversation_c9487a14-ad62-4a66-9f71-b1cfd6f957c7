<template>
  <div class="blank-layout">
    <client-only>
      <Nuxt />
    </client-only>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class Layout extends Vue {}
</script>
<style>
html {
  font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  word-spacing: 1px;
  -ms-name-size-adjust: 100%;
  -webkit-name-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.button--green {
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #3b8070;
  color: #3b8070;
  name-decoration: none;
  padding: 10px 30px;
}

.button--green:hover {
  color: #fff;
  background-color: #3b8070;
}

.button--grey {
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #35495e;
  color: #35495e;
  name-decoration: none;
  padding: 10px 30px;
  margin-left: 15px;
}

.button--grey:hover {
  color: #fff;
  background-color: #35495e;
}
.default-layout {
  width: 100vw;
  height: 100vh;
}
.default-layout .main-content {
  width: 100%;
  height: calc(100% - 50px);
  padding-top: 2px;
}
.default-layout #layout-right {
  height: 100%;
}
.layout-right {
  height: 100%;
  overflow: auto;
}
.center-view {
  margin: 6px;
  background-color: #fff;
}
</style>
