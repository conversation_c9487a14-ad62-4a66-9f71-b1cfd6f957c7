const staticRoutes = [
  {
    path: '/',
    redirect: { name: 'login' }
  },
  {
    path: '/login',
    name: 'login',
    component: 'pages/login/index.vue'
  },
  // {
  //   path: '/register',
  //   name: 'Register',
  //   component: 'pages/register/index.vue'
  // },
  // 重置密码
  {
    path: '/resetPassword',
    name: 'ResetPassword',
    component: 'pages/reset/password/reset/index.vue'
  },
  // 修改密码
  {
    path: '/updatePassword',
    name: 'UpdatePassword',
    component: 'pages/reset/password/update/index.vue'
  },
  {
    path: '/resetPhone',
    name: 'ResetPhone',
    component: 'pages/reset/phone/index.vue'
  },
  // {
  //   path: '/clause',
  //   name: 'Clause',
  //   component: 'pages/clause/index.vue'
  // },
  // {
  //   path: '/register-flow',
  //   name: 'registerFlow',
  //   component: 'pages/registerFlow/index.vue'
  // },
  // {
  //   path: '/register-flow/company',
  //   name: 'flowCompany',
  //   component: 'pages/registerFlow/company/index.vue'
  // },
  // {
  //   path: '/register-flow/personal',
  //   name: 'flowPersonal',
  //   component: 'pages/registerFlow/personal/index.vue'
  // }
]

module.exports = [...staticRoutes]
