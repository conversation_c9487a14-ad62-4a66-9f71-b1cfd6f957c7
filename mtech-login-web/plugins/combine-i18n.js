import { setLocal } from '@mtech-ui/base'

import Vue from 'vue'
import VueI18n from 'vue-i18n'
Vue.use(VueI18n)

export default ({ app, store }, inject) => {
  const i18n = new VueI18n({
    locale: store.state.locale || 'zh-CN',
    messages: {
      en: require('@/combile-i18n/en/login.json'),
      'zh-CN': require('@/combile-i18n/zh/login.json')
    }
  })
  app.i18n = i18n
  console.log('当前语言：', i18n.locale)

  const changeLocale = (locale) => {
    app.i18n.locale = locale
    setLocal(locale)
  }

  inject('changeL', changeLocale)
}
