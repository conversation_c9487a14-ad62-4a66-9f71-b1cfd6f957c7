import Vue from 'vue'
import { setLocal } from '@mtech-ui/base'
import indexDB from '@digis/internationalization'
import { utils } from '@mtech-common/utils'
export const i18n = indexDB.digisI18n(Vue, 'login-tcl')
export default ({ app, store }, inject) => {
  indexDB.layoutCreatLanguage().finally(() => {})
  utils.setAppCode('login-tcl')
  const internationlization = localStorage.getItem('internationlization')
  if (internationlization === 'zh' || internationlization === 'zh-CH') {
    setLocal('zh-CN')
  }
  app.i18n = i18n
  const changeLocale = (locale) => {
    app.i18n.locale = locale
    setLocal(locale)
  }
  inject('changeL', changeLocale)
}
