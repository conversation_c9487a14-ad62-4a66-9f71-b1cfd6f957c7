import { sso, clearAuth, clearParentAuth, hasAuth } from '@mtech-sso/single-sign-on' //, hasAuth, clearAuth

sso({
  authSuccessHandler: () => {
    console.log('认证成功')
  },
  // option
  authFailureHandler: (code, message) => {
    console.log('认证失败', code, message)
  },
  loginRequestUrl: ['/api/iam/common/login/third', '/api/iam/common/login/sms'],
  whiteList: new Set(['/api/iam/common/account/register'])
})

export default (event, inject) => {
  inject('clearAuth', clearAuth)
  inject('clearParentAuth', clearParentAuth)
  inject('hasAuth', hasAuth)
}
