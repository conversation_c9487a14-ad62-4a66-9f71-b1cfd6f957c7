import { Promise } from 'es6-promise'
import { baseConfig } from '@mtech-common/http'
baseConfig.setDefault({
  baseURL: '/api'
})
const ERR_MSG = {
  302: '未登录',
  400: '账号异常，登录超时，请重新登录',
  401: '无权查看',
  500: '系统异常，请稍后再试',
  501: '表单验证未通过',
  503: '数据异常，操作失败',
  505: 'excel导入异常',
  default: '系统异常，请稍后再试'
}
const NO_TIP_CODE = [303]
const APP_CODE = {
  development: '9a31789f3f0197500a482fff7d86e44f', // 登录
  production: '9a31789f3f0197500a482fff7d86e44f', // 登录
  test: '9a31789f3f0197500a482fff7d86e44f' // 登录
}
export default ({ $axios, app, redirect }) => {
  // 环境变量
  const isServer = !!process.server // 是否服务端
  const isClient = !!process.client // 是否客户端
  const env = process.env.NODE_ENV

  // $axios.defaults.retryDelay = 1000
  $axios.defaults.timeout = 15000
  $axios.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8'

  // 种下token
  $axios.interceptors.request.use(
    (config) => {
      if (config.deleteParams) {
        config.data = config.deleteParams
      }
      // config.headers['api-token'] = app.$cookies.get('  ') || ''
      config.headers['app-code'] = APP_CODE[`${env}`]
      config.headers['digis-lang'] = localStorage.getItem('internationlization') ?? 'zh-CH'
      return config
    },
    (err) => {
      console.log(err)
      return Promise.reject(err)
    }
  )
  // 状态码判断
  $axios.interceptors.response.use(
    (res) => {
      const { data, status, config } = res
      if (status < 200 || status >= 300) {
        app.$tips.error({
          title: '提示',
          content: ERR_MSG.default
        })
        return Promise.reject(res)
      } else {
        const { code } = data
        if (code != 200) {
          // 302未登录或400token过期
          if (code == 302 || code == 400) {
            const token = app.$cookies.get('token')
            if (token) {
              app.$cookies.remove('token')
            }
            // 跳转到登录页
            let url = '/login'
            let system = app.$cookies.get('system') || ''
            if (system) {
              url = '/login?system=' + system
            }
            if (isServer) {
              redirect(url)
            } else if (isClient) {
              app.router.push(url)
            }
          }
          if (code != 601) {
            handleWarnMessage(data, config || {})
          }
        }
        return res
      }
    },
    (err) => {
      app.$tips.error({
        title: '提示',
        content: ERR_MSG.default
      })
      return Promise.reject(err)
    }
  )

  function handleWarnMessage(res, config) {
    const { msg, code, data } = res
    let _message = msg || ERR_MSG[`${code}`] || ERR_MSG.default
    // 全局弹框提示错误
    if (config.hideErrTips) {
      return false
    }
    if (!(data && data.errorLabels && data.errorLabels.length)) {
      if (!NO_TIP_CODE.includes(code)) {
        app.$tips.error({
          title: '提示',
          content: _message
        })
      }
    }
  }
}
