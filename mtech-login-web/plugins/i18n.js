import { L10n, setCulture, loadCldr } from '@syncfusion/ej2-base'
// import { L10n, setCulture, loadCldr } from '@mtech-ui/base'
loadCldr(
  require('@/i18n/cldr-data/supplemental/numberingSystems.json'),
  require('@/i18n/cldr-data/main/zh/ca-gregorian.json'),
  require('@/i18n/cldr-data/main/zh/numbers.json'),
  require('@/i18n/cldr-data/main/zh/timeZoneNames.json'),
  require('@/i18n/cldr-data/main/zh/currencies.json'),
  require('@/i18n/cldr-data/main/zh/all.json')
)
export default ({ app }, inject) => {
  // 切换语言 待优化 暂定
  const transfer = (L, l10n) => {
    L10n.load(langJson)
    if (l10n) {
      l10n.setLocale(L)
    } else {
      setCulture(L)
    }
  }
  let langJson
  // 取语言当前语言
  const langObj = app.$storage.getLocalStorage('language')
  if (!langObj || langObj.lang === 'zh') {
    langJson = require('@/i18n/zh/zh.json')
    // 取数据扩充语言包
    langJson.zh.index = {
      intenational: '国际化',
      fine: '好了吗？',
      first: '第{0}个翻译'
    }
    // transfer('zh')
    L10n.load(langJson)
    try {
      setCulture('zh')
    } catch (error) {
      // 会找不到window 服务器会崩
      // setTimeout(() => {
      // setCulture('zh')
      // }, 1000)
    }
  } else if (langObj.lang === 'en-US') {
    langJson = require('@/i18n/en/en-US.json')
    langJson['en-US'].index = {
      intenational: '国际化',
      fine: '好了吗？',
      first: '第{0}个翻译'
    }
    // transfer('en-US')
    L10n.load(langJson)
    setCulture('en-US')
  }
  // 路由守卫加载当前的模块 ？ 或者不分模块 ： 不分（全挂在index下，键值不可重复）
  const _L10n = new L10n('index')
  const format = (str, args) => {
    try {
      let regx
      // 先翻译
      str = _L10n.getConstant(str)
      for (let i = 0; i < args.length; i++) {
        regx = new RegExp('\\{' + i + '\\}', 'gm')
        // 再替换
        str = str.replace(regx, args[i].toString())
      }
      return str
    } catch (error) {
      return str
    }
  }
  // sf 翻译方法容错
  const translate = (str) => {
    try {
      return _L10n.getConstant(str)
    } catch (error) {
      return str
    }
  }
  inject('L10n', _L10n)
  inject('t', translate)
  inject('tFormat', format)
  inject('switchL', transfer)
}
