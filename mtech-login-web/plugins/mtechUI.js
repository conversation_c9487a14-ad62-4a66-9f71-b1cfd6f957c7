/*
 * @Author: your name
 * @Date: 2021-07-26 11:37:04
 * @LastEditTime: 2021-08-03 17:30:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-login-web\plugins\mtechUI.js
 */
import Vue from 'vue'

import MtButton from '@mtech-ui/button'
import MtInput from '@mtech-ui/input'
import MtInputNumber from '@mtech-ui/input-number'
import MtForm from '@mtech-ui/form'
import MtFormItem from '@mtech-ui/form-item'
import MtToast from '@mtech-ui/toast'
import MtIcon from '@mtech-ui/icon'
import MtSelect from '@mtech-ui/select'
import MtMultiSelect from '@mtech-ui/multi-select'
import MtCheckboxGroup from '@mtech-ui/checkbox-group'
import MtCheckbox from '@mtech-ui/checkbox'
import MtRadio from '@mtech-ui/radio'
import MtTag from '@mtech-ui/tag'
import MtRow from '@mtech-ui/row'
import MtCol from '@mtech-ui/col'
import MtAccordion from '@mtech-ui/accordion'
import MtDialog from '@mtech-ui/dialog'
import MtTreeView from '@mtech-ui/tree-view'
import MtDataGrid from '../node_modules/@mtech-ui/data-grid/build/esm/index.js'
import MtUploader from '@mtech-ui/uploader'
import MtDatePicker from '@mtech-ui/date-picker'
import MtDateRangePicker from '@mtech-ui/date-range-picker'
// import { ProgressBarPlugin } from '@syncfusion/ej2-vue-progressbar' // MtProgress 写法有问题，不支持ssr
import MtProgress from '@mtech-ui/progress'

// //  引入样式
// import '@mtech-ui/base/build/esm/bundle.css' // 引入基础样式

Vue.use(MtDataGrid)
Vue.use(MtButton)
  .use(MtInput)
  .use(MtInputNumber)
  .use(MtCheckboxGroup)
  .use(MtCheckbox)
  .use(MtRadio)
  .use(MtForm)
  .use(MtFormItem)
  .use(MtToast)
  .use(MtSelect)
  .use(MtIcon)
  .use(MtRow)
  .use(MtCol)
  .use(MtAccordion)
  .use(MtUploader)
  .use(MtDateRangePicker)
  .use(MtDatePicker)
  .use(MtDialog)
  .use(MtTreeView)
  .use(MtMultiSelect)
  .use(MtProgress)

Vue.component('MtTag', MtTag)
// Vue.use(ProgressBarPlugin)
