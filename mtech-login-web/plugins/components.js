// import Vue from 'vue'
// import { TextBoxPlugin } from '@syncfusion/ej2-vue-inputs'
// import { DropDownListPlugin, MultiSelectPlugin } from '@syncfusion/ej2-vue-dropdowns'
// import { MultiSelect, CheckBoxSelection } from '@syncfusion/ej2-dropdowns'

// MultiSelect.Inject(CheckBoxSelection)
// Vue.use(MultiSelectPlugin)
// Vue.use(DropDownListPlugin)
// Vue.use(TextBoxPlugin)
// // Vue.use(GridPlugin)
// // Vue.use(DatePickerPlugin)
// // Vue.use(DateRangePickerPlugin)

// if (process.client) {
//   const { mtForm, mtFormItem, mtInput, mtSelect, mtMultiSelect, mtDatePicker, mtDateRangePicker } = window
//   Vue.component('MtForm', mtForm)
//   Vue.component('MtFormItem', mtFormItem)
//   Vue.component('MtInput', mtInput)
//   Vue.component('MtSelect', mtSelect)
//   Vue.component('MtMultiSelect', mtMultiSelect)
//   Vue.component('MtDatePicker', mtDatePicker)
//   Vue.component('MtDateRangePicker', mtDateRangePicker)
// }
