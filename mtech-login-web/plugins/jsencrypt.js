import JsEncrypt from 'jsencrypt'
export default ({ app, store }, inject) => {
  const pubKey = `-----BEGIN PUBLIC KEY-----MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDGZzbf3nIuDzSwrnkzATD4wDA9/lCdTnpeSn+J6qbgeKJYaRMgIk2d6wQIm3zp5nco1jVgTxerxX2pdQiOsjqG/6adOiA9MgHY97NWfNHZjdYIWJwQeoTgYyfNUwugoC/qjo/Gx3sAA6y6DIJgL7zpUdHy5MemHdi5alTqrq+OdQIDAQAB-----END PUBLIC KEY-----` // main.js 中
  let encryptStr = new JsEncrypt()
  encryptStr.setPublicKey(pubKey) // 设置 加密公钥
  const $encrypt = (value) => encryptStr.encrypt(value)
  inject('$encrypt', $encrypt)
}
