#__nuxt {
    width: 100%;
    height: 100%;

    #__layout {
        // min-width: 100vw;
        // min-height: 100vh;
        width: 100%;
        height: 100%;

        .container {
            // min-width: 1440px;
            // min-height: 760px;
            min-width: 1260px;  // 以小屏幕电脑为准
            min-height: 570px;
            position: relative;
            overflow: hidden;

            .e-float-input.e-input-group .e-clear-icon, .e-float-input.e-input-group.e-control-wrapper .e-clear-icon {
                display: none!important;
            }
        }
    }
}


// @media screen and (max-width: 1440px)  {
    @font-size-scale: 0.9;
    @padd-inner: 5%;
    @padd-topbottom: 4%;
    #__nuxt #__layout .container {
        .header {
            left: 80px;
            top: 10px;
            img {
                width: 120px;
            }
        }
        .main {
            .left-text {
                margin-left: @padd-inner;
                p {
                    font-size: calc(36px * @font-size-scale);
                }
                ul {
                    font-size: calc(27px * @font-size-scale);;
                }
            }
            .register-body {
                // width: 400px; // 495px;
                margin-right: @padd-inner;
            }

        }
        .footer {
            padding-left: @padd-inner;
            padding-bottom: @padd-topbottom;
        }
    }
// }

@media screen and (max-width: 1440px) {
  @padd-inner: 4%;
  #__nuxt #__layout .container {
    .header {
        left: @padd-inner;
        top: 6%;
        img {
            width: 197px;
        }
    }
    .main {
        .left-text {
            margin-left: @padd-inner;
            p {
                font-size: calc(36px * @font-size-scale);
            }
            ul {
                font-size: calc(27px * @font-size-scale);;
            }
        }
        .login-form {
            width: 440px; // 495px;
            // margin-right: @padd-inner;
            padding: 42px;
            box-sizing: border-box;
            position: absolute;
            right:  3%;
        }
        .register-body {
          width: 400px; // 495px;
          height: 80%;
          margin-right: @padd-inner;
          padding: 0;

          .form-content {
            height: 100%;
            padding: 20px 0;
            display: flex;
            flex-direction: column;

            .form-box {
              flex: 1;
              overflow: auto;
              padding: 0 40px;
            }
          }
        }
    }
    .footer {
        padding-left: @padd-inner;
        padding-bottom: @padd-topbottom;
    }
}
}

// @media screen and (max-width: 1680px)  {
//     @font-size-scale: 0.8;
//     @padd-inner: 88px;
//     #__nuxt #__layout .container {
//         .header {
//             left: @padd-inner;
//             top: @padd-inner;
//             img {
//                 width: 230px;
//             }
//         }
//         .main {
//             .left-text {
//                 left: @padd-inner;
//                 top: 394px;
//                 p {
//                     font-size: calc(42px * @font-size-scale);
//                 }
//                 ul {
//                     font-size: calc(32px * @font-size-scale);;
//                 }
//             }
//             .login-form {
//                 width: 578px;
//                 right: @padd-inner;
//             }
//         }
//         .footer {
//             padding-left: @padd-inner;
//             padding-bottom: 70px;
//         }
//     }
// }

// @media screen and (max-width: 1920px)  {
//     @font-size-scale: 0.8;
//     @padd-inner: 100px;
//     #__nuxt #__layout .container {
//         .header {
//             left: @padd-inner;
//             top: @padd-inner;
//             img {
//                 width: 263px;
//             }
//         }
//         .main {
//             .left-text {
//                 left: @padd-inner;
//                 top: 450px;
//                 p {
//                     font-size: calc(48px * @font-size-scale);
//                 }
//                 ul {
//                     font-size: calc(36px * @font-size-scale);;
//                 }
//             }
//             .login-form {
//                 width: 660px;
//                 right: @padd-inner;
//             }
//         }
//         .footer {
//             padding-left: @padd-inner;
//             padding-bottom: 80px;
//         }
//     }
// }
