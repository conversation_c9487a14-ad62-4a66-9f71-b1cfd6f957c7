const path = require('path')
const proxyConfig = require('./config/proxy.config.js')
// const alias = require('./config/alias.js')
const env = require('./config/env.js')
const head = require('./config/head.js')
const routes = require('./router/index')

const resolve = (pathUrl) => path.resolve(__dirname, pathUrl)
const DictionaryPlugin = require('@digis/dictionary-plugin')

const resolveRoutes = (routes) => {
  return routes.map((route) => {
    return {
      ...route,
      component: route.component ? resolve(route.component) : '',
      children: route.children && route.children.length ? resolveRoutes(route.children) : []
    }
  })
}

export default {
  // Global page headers (https://go.nuxtjs.dev/config-head)
  head: Object.assign({}, head),

  // Global CSS (https://go.nuxtjs.dev/config-css)
  css: [
    // 引入css，scss文件
    // '@/assets/style/commonCss/material.css'
    '@/assets/style/commonCss/common.css',
    '@/assets/style/resetCss/reset.less',
    '@/assets/style/commonCss/layout.less'
  ],
  server: {
    port: 9011 // default: 3000
  },
  router: {
    base: process.env.NODE_ENV === 'production' ? process.env.ROUTER_BASE ?? '/' : '/'
  },
  // env
  dev: process.env.NODE_ENV !== 'production' || process.env.NODE_ENV !== 'test',
  env: Object.assign({}, env),

  loading: {
    color: '#f00'
  },

  // Plugins to run before rendering page (https://go.nuxtjs.dev/config-plugins)
  plugins: [
    // '@/plugins/syncfusion.js'
    {
      src: '@/plugins/api.js',
      ssr: true
    },
    {
      src: '@/plugins/axios.js'
    },
    {
      src: '@/plugins/jsencrypt.js',
      ssr: false
    },
    // {
    //   // src: '@/plugins/i18n.js'
    //   src: '@/plugins/combine-i18n.js',
    //   // ssr: true
    // },
    {
      src: '@/plugins/routerHook.js'
    },
    {
      src: '@/plugins/mtechUI.js'
    },
    {
      src: '@/plugins/tips.js',
      ssr: false
    },
    {
      src: '@/plugins/loading.js'
    },
    {
      src: '@/plugins/SvgIcon.js',
      ssr: true
    },
    {
      src: '@/plugins/sso.js',
      ssr: false
    },
    {
      src: '@/plugins/internationlization.js',
      ssr: false
    }
    // {
    //   src: '@/plugins/components.js',
    //   ssr: false
    // },
  ],

  // Auto import components (https://go.nuxtjs.dev/config-components)
  components: true,

  // Modules for dev and build (recommended) (https://go.nuxtjs.dev/config-modules)
  buildModules: [
    // https://go.nuxtjs.dev/typescript
    '@nuxt/typescript-build'
  ],

  serverMiddleware: ['@/config/server.js'],

  axios: {
    proxy: true,
    prefix: '/api', // baseURL
    credentials: true
  },
  proxy: proxyConfig, // 代理
  // Modules (https://go.nuxtjs.dev/config-modules)
  modules: [
    // 'axios',
    '@nuxtjs/axios',
    '@nuxtjs/proxy',
    '@nuxtjs/universal-storage',
    'cookie-universal-nuxt'
  ],
  // 路由
  hooks: {
    build: {
      extendRoutes: (nuxtRoutes) => {
        nuxtRoutes.splice(0, nuxtRoutes.length, ...resolveRoutes(routes))
      }
    }
  },

  storage: {
    vuex: {
      namespace: 'storage'
    },
    cookie: {
      prefix: '',
      options: {
        path: '/'
      }
    },
    localStorage: {
      prefix: ''
    },
    ignoreExceptions: false
  },

  // Build Configuration (https://go.nuxtjs.dev/config-build)
  build: {
    vendor: ['vue-i18n'], // webpack vue-i18n.bundle.js
    extend(config, { isClient }) {
      // Run ESLint on save
      // config.module.rules.push({
      //   enforce: 'pre',
      //   test: /\.(ts|js|vue)$/,
      //   loader: 'eslint-loader',
      //   exclude: /(node_modules)/
      // })
      const svgRule = config.module.rules.find((rule) => rule.test.test('.svg'))
      svgRule.exclude = [resolve('assets/icon')]

      // 添加loader规则
      config.module.rules.push({
        test: /\.svg$/, // 匹配.svg
        include: [resolve('assets/icon')], // 将存放svg的目录加入到loader处理目录
        use: [{ loader: 'svg-sprite-loader', options: { symbolId: 'icon-[name]' } }]
      })
      if (isClient) {
        config.plugins.push(new DictionaryPlugin())
      }

      config.resolve.alias.vue$ = '@/node_modules/vue/dist/vue.esm.js'
      // syncfusion 组件 生产测试 使用压缩文件
      // if (!isDev) {
      //   alias(config)
      // }
    },
    cache: true
  }
}
