# 送货计划功能测试用例

## 功能概述
送货计划管理是VMI（供应商管理库存）系统的核心功能，包括送货计划列表管理和明细管理两个主要模块。

## 测试用例分类

### 1. 送货计划列表页面测试用例

#### 1.1 页面加载测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_001 | 页面初始化加载 | 用户已登录系统 | 1. 访问送货计划页面<br>2. 等待页面加载完成 | 1. 页面正常显示<br>2. 默认显示列表tab<br>3. 搜索条件默认为最近一个月 | 高 |
| TC_DP_002 | Tab切换功能 | 页面已加载 | 1. 点击"明细"tab<br>2. 点击"列表"tab | 1. Tab切换正常<br>2. 对应内容正确显示 | 中 |

#### 1.2 搜索功能测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_003 | 送货计划单号搜索 | 页面已加载 | 1. 输入有效的送货计划单号<br>2. 点击搜索按钮 | 显示匹配的送货计划记录 | 高 |
| TC_DP_004 | 省区搜索 | 页面已加载 | 1. 输入省份名称<br>2. 点击搜索按钮 | 显示对应省份的送货计划 | 中 |
| TC_DP_005 | 仓库编码搜索 | 页面已加载 | 1. 输入仓库编码<br>2. 点击搜索按钮 | 显示对应仓库的送货计划 | 中 |
| TC_DP_006 | 仓库名称搜索 | 页面已加载 | 1. 输入仓库名称<br>2. 点击搜索按钮 | 显示对应仓库的送货计划 | 中 |
| TC_DP_007 | 供应商搜索 | 页面已加载 | 1. 选择供应商<br>2. 点击搜索按钮 | 显示对应供应商的送货计划 | 高 |
| TC_DP_008 | 状态筛选 | 页面已加载 | 1. 选择状态（草稿/待审核/待发货等）<br>2. 点击搜索按钮 | 显示对应状态的送货计划 | 高 |
| TC_DP_009 | 创建时间范围搜索 | 页面已加载 | 1. 选择时间范围<br>2. 点击搜索按钮 | 显示时间范围内的送货计划 | 中 |
| TC_DP_010 | 组合条件搜索 | 页面已加载 | 1. 同时输入多个搜索条件<br>2. 点击搜索按钮 | 显示满足所有条件的记录 | 高 |
| TC_DP_011 | 重置搜索条件 | 已输入搜索条件 | 1. 点击重置按钮 | 1. 搜索条件清空<br>2. 恢复默认时间范围<br>3. 重新加载数据 | 中 |

#### 1.3 表格操作测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_012 | 表格数据显示 | 页面已加载 | 查看表格内容 | 1. 正确显示所有列<br>2. 数据格式正确<br>3. 状态显示正确 | 高 |
| TC_DP_013 | 送货计划单号点击 | 表格有数据 | 点击送货计划单号链接 | 跳转到送货计划详情页面 | 高 |
| TC_DP_014 | 表格排序 | 表格有数据 | 点击列标题进行排序 | 数据按指定列排序 | 中 |
| TC_DP_015 | 分页功能 | 数据超过一页 | 1. 点击下一页<br>2. 修改每页显示数量 | 分页正常工作 | 中 |
| TC_DP_016 | 表格刷新 | 表格已加载 | 点击刷新按钮 | 重新加载最新数据 | 中 |

#### 1.4 批量操作测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_017 | 批量通过-未选择记录 | 表格有数据 | 1. 不选择任何记录<br>2. 点击通过按钮 | 提示"请选择要操作的行" | 高 |
| TC_DP_018 | 批量通过-选择记录 | 表格有待审核数据 | 1. 选择一条或多条记录<br>2. 点击通过按钮<br>3. 确认操作 | 1. 显示确认对话框<br>2. 操作成功后显示成功提示<br>3. 刷新数据 | 高 |
| TC_DP_019 | 批量驳回-未选择记录 | 表格有数据 | 1. 不选择任何记录<br>2. 点击驳回按钮 | 提示"请选择要操作的行" | 高 |
| TC_DP_020 | 批量驳回-选择记录 | 表格有待审核数据 | 1. 选择一条或多条记录<br>2. 点击驳回按钮<br>3. 输入驳回原因<br>4. 确认操作 | 1. 显示驳回对话框<br>2. 操作成功后显示成功提示<br>3. 刷新数据 | 高 |
| TC_DP_021 | 批量删除-未选择记录 | 表格有数据 | 1. 不选择任何记录<br>2. 点击删除按钮 | 提示"请选择要操作的行" | 高 |
| TC_DP_022 | 批量删除-选择记录 | 表格有草稿状态数据 | 1. 选择一条或多条记录<br>2. 点击删除按钮<br>3. 确认操作 | 1. 显示确认对话框<br>2. 操作成功后显示成功提示<br>3. 刷新数据 | 高 |

#### 1.5 导出功能测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_023 | 导出当前页数据 | 表格有数据 | 点击导出按钮 | 1. 成功下载Excel文件<br>2. 文件内容与页面数据一致 | 中 |
| TC_DP_024 | 导出筛选后数据 | 已应用搜索条件 | 1. 设置搜索条件<br>2. 点击导出按钮 | 导出的数据符合筛选条件 | 中 |

### 2. 送货计划明细页面测试用例

#### 2.1 明细页面搜索测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DPD_001 | 明细页面基本搜索 | 切换到明细tab | 1. 输入送货计划单号<br>2. 点击搜索 | 显示对应的明细记录 | 高 |
| TC_DPD_002 | 品类搜索 | 明细页面已加载 | 1. 选择品类<br>2. 点击搜索 | 显示对应品类的明细 | 中 |
| TC_DPD_003 | 规格搜索 | 明细页面已加载 | 1. 输入规格信息<br>2. 点击搜索 | 显示匹配规格的明细 | 中 |

#### 2.2 明细表格显示测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DPD_004 | 明细表格列显示 | 明细页面有数据 | 查看表格列 | 1. 显示所有必要列<br>2. 包含物料信息、库存信息等 | 高 |
| TC_DPD_005 | 库存数据显示 | 明细页面有数据 | 查看库存相关列 | 1. 正确显示可用库存<br>2. 正确显示在途库存<br>3. 正确显示占用库存 | 高 |
| TC_DPD_006 | 计划数据显示 | 明细页面有数据 | 查看计划相关列 | 1. 正确显示计划送货量<br>2. 正确显示计划送货日期 | 高 |

#### 2.3 明细导出测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DPD_007 | 明细数据导出 | 明细页面有数据 | 点击导出按钮 | 1. 成功下载Excel文件<br>2. 包含所有明细字段 | 中 |

### 3. 异常场景测试用例

#### 3.1 网络异常测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_025 | 网络中断时加载数据 | 网络连接中断 | 1. 访问送货计划页面<br>2. 尝试搜索数据 | 显示网络错误提示 | 中 |
| TC_DP_026 | 网络超时处理 | 网络响应缓慢 | 1. 执行搜索操作<br>2. 等待响应 | 1. 显示加载状态<br>2. 超时后显示错误提示 | 中 |

#### 3.2 权限测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_027 | 无操作权限用户 | 用户无相关权限 | 1. 访问送货计划页面<br>2. 尝试执行操作 | 1. 隐藏无权限按钮<br>2. 或显示权限不足提示 | 高 |

#### 3.3 数据边界测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_028 | 空数据处理 | 系统无送货计划数据 | 1. 访问送货计划页面<br>2. 执行搜索 | 显示"暂无数据"提示 | 中 |
| TC_DP_029 | 大量数据加载 | 系统有大量数据 | 1. 搜索大量数据<br>2. 查看性能表现 | 1. 分页正常<br>2. 响应时间合理 | 中 |

### 4. 状态流转测试用例

#### 4.1 送货计划状态测试
| 用例编号 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_DP_030 | 草稿状态计划操作 | 存在草稿状态计划 | 1. 选择草稿状态计划<br>2. 执行各种操作 | 1. 可以删除<br>2. 可以通过审核 | 高 |
| TC_DP_031 | 待审核状态计划操作 | 存在待审核状态计划 | 1. 选择待审核计划<br>2. 执行操作 | 1. 可以通过<br>2. 可以驳回<br>3. 不能删除 | 高 |
| TC_DP_032 | 已驳回状态计划操作 | 存在已驳回状态计划 | 1. 选择已驳回计划<br>2. 查看驳回原因 | 1. 显示驳回原因<br>2. 可以重新提交 | 高 |

## 测试数据准备

### 基础测试数据
1. **供应商数据**：至少3个不同的供应商
2. **仓库数据**：不同省区的仓库信息
3. **物料数据**：不同品类的物料信息
4. **送货计划数据**：各种状态的送货计划

### 边界测试数据
1. **特殊字符**：包含特殊字符的搜索条件
2. **长文本**：超长的仓库名称、地址等
3. **数值边界**：最大最小的数量值

## 测试环境要求
1. **浏览器兼容性**：Chrome、Firefox、Safari、Edge
2. **分辨率**：1920x1080、1366x768等常见分辨率
3. **网络环境**：正常网络、慢网络、断网情况

### 5. API接口测试用例

#### 5.1 送货计划列表接口测试
| 用例编号 | 测试场景 | 请求方法 | 接口路径 | 请求参数 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| TC_API_001 | 分页查询送货计划 | POST | /tenant/pv/vmi/delivery/plan/buyer/page/query | page: {current:1, size:50} | 返回分页数据，code=200 | 高 |
| TC_API_002 | 按单号查询 | POST | /tenant/pv/vmi/delivery/plan/buyer/page/query | deliveryPlanCode: "DP001" | 返回匹配的计划数据 | 高 |
| TC_API_003 | 按供应商查询 | POST | /tenant/pv/vmi/delivery/plan/buyer/page/query | supplierCodeList: ["SUP001"] | 返回该供应商的计划 | 高 |
| TC_API_004 | 按状态查询 | POST | /tenant/pv/vmi/delivery/plan/buyer/page/query | statusList: [1,2] | 返回指定状态的计划 | 高 |
| TC_API_005 | 按时间范围查询 | POST | /tenant/pv/vmi/delivery/plan/buyer/page/query | createTimeS, createTimeE | 返回时间范围内的数据 | 中 |

#### 5.2 送货计划操作接口测试
| 用例编号 | 测试场景 | 请求方法 | 接口路径 | 请求参数 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| TC_API_006 | 批量通过送货计划 | POST | /tenant/pv/vmi/delivery/plan/batch/pass | ids: [1,2,3] | 操作成功，code=200 | 高 |
| TC_API_007 | 批量驳回送货计划 | POST | /tenant/pv/vmi/delivery/plan/batch/rejected | ids: [1,2], rejectReason: "原因" | 操作成功，code=200 | 高 |
| TC_API_008 | 批量删除送货计划 | POST | /tenant/pv/vmi/delivery/plan/batch/delete | ids: [1,2,3] | 操作成功，code=200 | 高 |
| TC_API_009 | 导出送货计划 | POST | /tenant/pv/vmi/delivery/plan/buyer/export | 查询条件参数 | 返回Excel文件流 | 中 |

#### 5.3 送货计划明细接口测试
| 用例编号 | 测试场景 | 请求方法 | 接口路径 | 请求参数 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| TC_API_010 | 分页查询明细 | POST | /tenant/pv/vmi/delivery/plan/detail/buyer/page/query | page: {current:1, size:50} | 返回明细分页数据 | 高 |
| TC_API_011 | 按计划单号查询明细 | POST | /tenant/pv/vmi/delivery/plan/detail/buyer/page/query | deliveryPlanCode: "DP001" | 返回该计划的明细 | 高 |
| TC_API_012 | 按品类查询明细 | POST | /tenant/pv/vmi/delivery/plan/detail/buyer/page/query | catoryCodeList: ["CAT001"] | 返回该品类的明细 | 中 |
| TC_API_013 | 导出明细数据 | POST | /tenant/pv/vmi/delivery/plan/detail/buyer/export | 查询条件参数 | 返回明细Excel文件 | 中 |

#### 5.4 接口异常测试
| 用例编号 | 测试场景 | 请求方法 | 接口路径 | 请求参数 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| TC_API_014 | 无效的分页参数 | POST | /tenant/pv/vmi/delivery/plan/buyer/page/query | page: {current:-1, size:0} | 返回参数错误提示 | 中 |
| TC_API_015 | 空的ID列表操作 | POST | /tenant/pv/vmi/delivery/plan/batch/delete | ids: [] | 返回参数错误提示 | 中 |
| TC_API_016 | 不存在的ID操作 | POST | /tenant/pv/vmi/delivery/plan/batch/delete | ids: [99999] | 返回数据不存在提示 | 中 |
| TC_API_017 | 无权限访问 | POST | /tenant/pv/vmi/delivery/plan/buyer/page/query | 无权限token | 返回401或403错误 | 高 |

### 6. 性能测试用例

#### 6.1 页面加载性能测试
| 用例编号 | 测试场景 | 测试条件 | 性能指标 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_PERF_001 | 首次页面加载 | 正常网络环境 | 页面完全加载时间 | ≤3秒 | 高 |
| TC_PERF_002 | 大数据量加载 | 10000+条记录 | 表格渲染时间 | ≤5秒 | 中 |
| TC_PERF_003 | 搜索响应时间 | 复杂查询条件 | 搜索结果返回时间 | ≤2秒 | 高 |
| TC_PERF_004 | 导出大文件 | 5000+条记录导出 | 文件生成时间 | ≤10秒 | 中 |

#### 6.2 并发性能测试
| 用例编号 | 测试场景 | 并发用户数 | 测试时长 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_PERF_005 | 并发查询测试 | 50用户 | 5分钟 | 响应时间≤3秒，成功率≥99% | 中 |
| TC_PERF_006 | 并发操作测试 | 20用户 | 3分钟 | 操作成功率≥95% | 中 |

### 7. 兼容性测试用例

#### 7.1 浏览器兼容性测试
| 用例编号 | 测试场景 | 浏览器版本 | 测试内容 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_COMP_001 | Chrome浏览器测试 | Chrome 90+ | 所有功能正常 | 功能完全正常 | 高 |
| TC_COMP_002 | Firefox浏览器测试 | Firefox 88+ | 所有功能正常 | 功能完全正常 | 中 |
| TC_COMP_003 | Safari浏览器测试 | Safari 14+ | 所有功能正常 | 功能完全正常 | 中 |
| TC_COMP_004 | Edge浏览器测试 | Edge 90+ | 所有功能正常 | 功能完全正常 | 中 |

#### 7.2 分辨率兼容性测试
| 用例编号 | 测试场景 | 屏幕分辨率 | 测试内容 | 预期结果 | 优先级 |
|---------|---------|---------|---------|---------|--------|
| TC_COMP_005 | 高分辨率显示 | 1920x1080 | 页面布局正常 | 布局美观，无错位 | 高 |
| TC_COMP_006 | 标准分辨率显示 | 1366x768 | 页面布局正常 | 布局正常，可用性好 | 高 |
| TC_COMP_007 | 低分辨率显示 | 1024x768 | 页面布局适配 | 基本功能可用 | 中 |

### 8. 安全测试用例

#### 8.1 输入验证测试
| 用例编号 | 测试场景 | 输入内容 | 预期结果 | 优先级 |
|---------|---------|---------|---------|--------|
| TC_SEC_001 | SQL注入测试 | 搜索框输入SQL语句 | 系统正常处理，无SQL注入 | 高 |
| TC_SEC_002 | XSS攻击测试 | 输入JavaScript代码 | 代码被转义，不执行 | 高 |
| TC_SEC_003 | 特殊字符测试 | 输入特殊字符 | 系统正常处理 | 中 |

#### 8.2 权限控制测试
| 用例编号 | 测试场景 | 测试条件 | 预期结果 | 优先级 |
|---------|---------|---------|---------|--------|
| TC_SEC_004 | 越权访问测试 | 无权限用户访问 | 拒绝访问或隐藏功能 | 高 |
| TC_SEC_005 | Token过期测试 | 使用过期Token | 重新登录或刷新Token | 高 |

### 9. 用户体验测试用例

#### 9.1 界面友好性测试
| 用例编号 | 测试场景 | 测试内容 | 预期结果 | 优先级 |
|---------|---------|---------|---------|--------|
| TC_UX_001 | 加载状态提示 | 数据加载过程 | 显示加载动画或进度条 | 中 |
| TC_UX_002 | 操作反馈提示 | 执行操作后 | 显示成功或失败提示 | 高 |
| TC_UX_003 | 错误信息提示 | 操作失败时 | 显示明确的错误信息 | 高 |
| TC_UX_004 | 确认对话框 | 删除等危险操作 | 显示确认对话框 | 高 |

#### 9.2 操作便利性测试
| 用例编号 | 测试场景 | 测试内容 | 预期结果 | 优先级 |
|---------|---------|---------|---------|--------|
| TC_UX_005 | 快捷键支持 | 使用键盘操作 | 支持常用快捷键 | 低 |
| TC_UX_006 | 批量选择 | 全选/反选功能 | 批量选择功能正常 | 中 |
| TC_UX_007 | 记住搜索条件 | 页面刷新后 | 保持之前的搜索条件 | 低 |

## 测试执行计划

### 第一阶段：功能测试（预计3天）
1. **Day 1**：基础功能测试（页面加载、搜索、表格显示）
2. **Day 2**：操作功能测试（批量操作、导出、状态流转）
3. **Day 3**：异常场景测试（网络异常、权限、边界数据）

### 第二阶段：接口测试（预计2天）
1. **Day 4**：API接口功能测试
2. **Day 5**：API接口异常测试

### 第三阶段：非功能测试（预计2天）
1. **Day 6**：性能测试、兼容性测试
2. **Day 7**：安全测试、用户体验测试

## 缺陷管理

### 缺陷等级定义
- **严重**：系统崩溃、数据丢失、核心功能无法使用
- **重要**：主要功能异常、影响用户正常使用
- **一般**：次要功能问题、界面显示问题
- **轻微**：文字错误、界面美观问题

### 缺陷跟踪
1. 使用缺陷管理工具记录所有发现的问题
2. 及时与开发团队沟通严重和重要缺陷
3. 验证修复后的功能是否正常

## 验收标准
1. 所有高优先级用例通过率100%
2. 中优先级用例通过率≥95%
3. 页面响应时间≤3秒
4. 无严重UI显示问题
5. 数据准确性100%
6. 无严重和重要级别的未修复缺陷
7. 兼容主流浏览器（Chrome、Firefox、Safari、Edge）
8. 通过安全测试，无安全漏洞
