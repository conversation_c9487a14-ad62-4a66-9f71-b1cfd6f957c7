用例编号,测试模块,测试场景,优先级,前置条件,操作步骤,预期结果,实际结果,测试状态,执行人,执行时间,备注
TC_DP_001,送货计划列表,页面初始化加载,高,用户已登录系统,"1. 访问送货计划页面
2. 等待页面加载完成","1. 页面正常显示
2. 默认显示列表tab
3. 搜索条件默认为最近一个月",,未执行,,,
TC_DP_002,送货计划列表,Tab切换功能,中,页面已加载,"1. 点击""明细""tab
2. 点击""列表""tab","1. Tab切换正常
2. 对应内容正确显示",,未执行,,,
TC_DP_003,送货计划列表,送货计划单号搜索,高,页面已加载,"1. 输入有效的送货计划单号
2. 点击搜索按钮",显示匹配的送货计划记录,,未执行,,,
TC_DP_004,送货计划列表,省区搜索,中,页面已加载,"1. 输入省份名称
2. 点击搜索按钮",显示对应省份的送货计划,,未执行,,,
TC_DP_005,送货计划列表,仓库编码搜索,中,页面已加载,"1. 输入仓库编码
2. 点击搜索按钮",显示对应仓库的送货计划,,未执行,,,
TC_DP_006,送货计划列表,仓库名称搜索,中,页面已加载,"1. 输入仓库名称
2. 点击搜索按钮",显示对应仓库的送货计划,,未执行,,,
TC_DP_007,送货计划列表,供应商搜索,高,页面已加载,"1. 选择供应商
2. 点击搜索按钮",显示对应供应商的送货计划,,未执行,,,
TC_DP_008,送货计划列表,状态筛选,高,页面已加载,"1. 选择状态（草稿/待审核/待发货等）
2. 点击搜索按钮",显示对应状态的送货计划,,未执行,,,
TC_DP_009,送货计划列表,创建时间范围搜索,中,页面已加载,"1. 选择时间范围
2. 点击搜索按钮",显示时间范围内的送货计划,,未执行,,,
TC_DP_010,送货计划列表,组合条件搜索,高,页面已加载,"1. 同时输入多个搜索条件
2. 点击搜索按钮",显示满足所有条件的记录,,未执行,,,
TC_DP_011,送货计划列表,重置搜索条件,中,已输入搜索条件,1. 点击重置按钮,"1. 搜索条件清空
2. 恢复默认时间范围
3. 重新加载数据",,未执行,,,
TC_DP_012,送货计划列表,表格数据显示,高,页面已加载,查看表格内容,"1. 正确显示所有列
2. 数据格式正确
3. 状态显示正确",,未执行,,,
TC_DP_013,送货计划列表,送货计划单号点击,高,表格有数据,点击送货计划单号链接,跳转到送货计划详情页面,,未执行,,,
TC_DP_014,送货计划列表,表格排序,中,表格有数据,点击列标题进行排序,数据按指定列排序,,未执行,,,
TC_DP_015,送货计划列表,分页功能,中,数据超过一页,"1. 点击下一页
2. 修改每页显示数量",分页正常工作,,未执行,,,
TC_DP_016,送货计划列表,表格刷新,中,表格已加载,点击刷新按钮,重新加载最新数据,,未执行,,,
TC_DP_017,送货计划列表,批量通过-未选择记录,高,表格有数据,"1. 不选择任何记录
2. 点击通过按钮",提示"请选择要操作的行",,未执行,,,
TC_DP_018,送货计划列表,批量通过-选择记录,高,表格有待审核数据,"1. 选择一条或多条记录
2. 点击通过按钮
3. 确认操作","1. 显示确认对话框
2. 操作成功后显示成功提示
3. 刷新数据",,未执行,,,
TC_DP_019,送货计划列表,批量驳回-未选择记录,高,表格有数据,"1. 不选择任何记录
2. 点击驳回按钮",提示"请选择要操作的行",,未执行,,,
TC_DP_020,送货计划列表,批量驳回-选择记录,高,表格有待审核数据,"1. 选择一条或多条记录
2. 点击驳回按钮
3. 输入驳回原因
4. 确认操作","1. 显示驳回对话框
2. 操作成功后显示成功提示
3. 刷新数据",,未执行,,,
TC_DP_021,送货计划列表,批量删除-未选择记录,高,表格有数据,"1. 不选择任何记录
2. 点击删除按钮",提示"请选择要操作的行",,未执行,,,
TC_DP_022,送货计划列表,批量删除-选择记录,高,表格有草稿状态数据,"1. 选择一条或多条记录
2. 点击删除按钮
3. 确认操作","1. 显示确认对话框
2. 操作成功后显示成功提示
3. 刷新数据",,未执行,,,
TC_DP_023,送货计划列表,导出当前页数据,中,表格有数据,点击导出按钮,"1. 成功下载Excel文件
2. 文件内容与页面数据一致",,未执行,,,
TC_DP_024,送货计划列表,导出筛选后数据,中,已应用搜索条件,"1. 设置搜索条件
2. 点击导出按钮",导出的数据符合筛选条件,,未执行,,,
TC_DPD_001,送货计划明细,明细页面基本搜索,高,切换到明细tab,"1. 输入送货计划单号
2. 点击搜索",显示对应的明细记录,,未执行,,,
TC_DPD_002,送货计划明细,品类搜索,中,明细页面已加载,"1. 选择品类
2. 点击搜索",显示对应品类的明细,,未执行,,,
TC_DPD_003,送货计划明细,规格搜索,中,明细页面已加载,"1. 输入规格信息
2. 点击搜索",显示匹配规格的明细,,未执行,,,
TC_DPD_004,送货计划明细,明细表格列显示,高,明细页面有数据,查看表格列,"1. 显示所有必要列
2. 包含物料信息、库存信息等",,未执行,,,
TC_DPD_005,送货计划明细,库存数据显示,高,明细页面有数据,查看库存相关列,"1. 正确显示可用库存
2. 正确显示在途库存
3. 正确显示占用库存",,未执行,,,
TC_DPD_006,送货计划明细,计划数据显示,高,明细页面有数据,查看计划相关列,"1. 正确显示计划送货量
2. 正确显示计划送货日期",,未执行,,,
TC_DPD_007,送货计划明细,明细数据导出,中,明细页面有数据,点击导出按钮,"1. 成功下载Excel文件
2. 包含所有明细字段",,未执行,,,
TC_DP_025,异常场景,网络中断时加载数据,中,网络连接中断,"1. 访问送货计划页面
2. 尝试搜索数据",显示网络错误提示,,未执行,,,
TC_DP_026,异常场景,网络超时处理,中,网络响应缓慢,"1. 执行搜索操作
2. 等待响应","1. 显示加载状态
2. 超时后显示错误提示",,未执行,,,
TC_DP_027,异常场景,无操作权限用户,高,用户无相关权限,"1. 访问送货计划页面
2. 尝试执行操作","1. 隐藏无权限按钮
2. 或显示权限不足提示",,未执行,,,
TC_DP_028,异常场景,空数据处理,中,系统无送货计划数据,"1. 访问送货计划页面
2. 执行搜索",显示"暂无数据"提示,,未执行,,,
TC_DP_029,异常场景,大量数据加载,中,系统有大量数据,"1. 搜索大量数据
2. 查看性能表现","1. 分页正常
2. 响应时间合理",,未执行,,,
TC_DP_030,状态流转,草稿状态计划操作,高,存在草稿状态计划,"1. 选择草稿状态计划
2. 执行各种操作","1. 可以删除
2. 可以通过审核",,未执行,,,
TC_DP_031,状态流转,待审核状态计划操作,高,存在待审核状态计划,"1. 选择待审核计划
2. 执行操作","1. 可以通过
2. 可以驳回
3. 不能删除",,未执行,,,
TC_DP_032,状态流转,已驳回状态计划操作,高,存在已驳回状态计划,"1. 选择已驳回计划
2. 查看驳回原因","1. 显示驳回原因
2. 可以重新提交",,未执行,,,
