// 送货计划功能自动化测试
describe('送货计划管理', () => {
  beforeEach(() => {
    // 登录系统
    cy.login()
    // 访问送货计划页面
    cy.visit('/purchase-pv/vmi-management/delivery-plan')
    // 等待页面加载
    cy.get('[data-testid="delivery-plan-container"]').should('be.visible')
  })

  describe('页面基础功能', () => {
    it('应该正确加载页面', () => {
      // 验证页面标题
      cy.get('.page-title').should('contain', '送货计划')
      
      // 验证Tab切换
      cy.get('.toggle-tab').should('be.visible')
      cy.get('.toggle-tab').contains('列表').should('have.class', 'active')
      cy.get('.toggle-tab').contains('明细').should('be.visible')
      
      // 验证搜索表单
      cy.get('[data-testid="search-form"]').should('be.visible')
      
      // 验证表格
      cy.get('[data-testid="delivery-plan-table"]').should('be.visible')
    })

    it('应该支持Tab切换', () => {
      // 切换到明细Tab
      cy.get('.toggle-tab').contains('明细').click()
      cy.get('[data-testid="detail-container"]').should('be.visible')
      
      // 切换回列表Tab
      cy.get('.toggle-tab').contains('列表').click()
      cy.get('[data-testid="list-container"]').should('be.visible')
    })
  })

  describe('搜索功能', () => {
    it('应该支持按送货计划单号搜索', () => {
      const planCode = 'DP202312010001'
      
      // 输入送货计划单号
      cy.get('[data-testid="delivery-plan-code-input"]').type(planCode)
      
      // 点击搜索
      cy.get('[data-testid="search-button"]').click()
      
      // 等待搜索结果
      cy.get('[data-testid="loading"]').should('not.exist')
      
      // 验证搜索结果
      cy.get('[data-testid="delivery-plan-table"] tbody tr').should('have.length.at.least', 1)
      cy.get('[data-testid="delivery-plan-table"]').should('contain', planCode)
    })

    it('应该支持按供应商搜索', () => {
      // 点击供应商选择器
      cy.get('[data-testid="supplier-selector"]').click()
      
      // 选择供应商
      cy.get('[data-testid="supplier-option"]').first().click()
      
      // 点击搜索
      cy.get('[data-testid="search-button"]').click()
      
      // 验证搜索结果
      cy.get('[data-testid="loading"]').should('not.exist')
      cy.get('[data-testid="delivery-plan-table"] tbody tr').should('have.length.at.least', 1)
    })

    it('应该支持按状态筛选', () => {
      // 选择状态
      cy.get('[data-testid="status-selector"]').click()
      cy.get('[data-testid="status-option-1"]').click() // 待审核状态
      
      // 点击搜索
      cy.get('[data-testid="search-button"]').click()
      
      // 验证搜索结果
      cy.get('[data-testid="loading"]').should('not.exist')
      cy.get('[data-testid="delivery-plan-table"]').should('contain', '待审核')
    })

    it('应该支持重置搜索条件', () => {
      // 输入搜索条件
      cy.get('[data-testid="delivery-plan-code-input"]').type('test')
      cy.get('[data-testid="warehouse-code-input"]').type('WH001')
      
      // 点击重置
      cy.get('[data-testid="reset-button"]').click()
      
      // 验证搜索条件已清空
      cy.get('[data-testid="delivery-plan-code-input"]').should('have.value', '')
      cy.get('[data-testid="warehouse-code-input"]').should('have.value', '')
      
      // 验证数据已重新加载
      cy.get('[data-testid="loading"]').should('not.exist')
    })
  })

  describe('表格操作', () => {
    it('应该显示送货计划数据', () => {
      // 验证表格列
      const expectedColumns = [
        '送货计划单号', '省区', '仓库编码', '仓库名称', 
        '供应商编码', '供应商名称', '状态', '创建人', '创建时间'
      ]
      
      expectedColumns.forEach(column => {
        cy.get('[data-testid="delivery-plan-table"] thead').should('contain', column)
      })
      
      // 验证数据行
      cy.get('[data-testid="delivery-plan-table"] tbody tr').should('have.length.at.least', 1)
    })

    it('应该支持点击送货计划单号跳转详情', () => {
      // 点击第一行的送货计划单号
      cy.get('[data-testid="delivery-plan-table"] tbody tr:first-child')
        .find('[data-testid="plan-code-link"]')
        .click()
      
      // 验证跳转到详情页
      cy.url().should('include', '/delivery-plan-detail')
    })

    it('应该支持分页', () => {
      // 验证分页组件存在
      cy.get('[data-testid="pagination"]').should('be.visible')
      
      // 如果有多页数据，测试翻页
      cy.get('[data-testid="pagination"]').then($pagination => {
        if ($pagination.find('[data-testid="next-page"]').length > 0) {
          cy.get('[data-testid="next-page"]').click()
          cy.get('[data-testid="loading"]').should('not.exist')
          cy.get('[data-testid="delivery-plan-table"] tbody tr').should('have.length.at.least', 1)
        }
      })
    })
  })

  describe('批量操作', () => {
    beforeEach(() => {
      // 确保有待审核的数据
      cy.get('[data-testid="status-selector"]').click()
      cy.get('[data-testid="status-option-1"]').click() // 待审核
      cy.get('[data-testid="search-button"]').click()
      cy.get('[data-testid="loading"]').should('not.exist')
    })

    it('未选择记录时应该提示', () => {
      // 不选择任何记录，直接点击通过按钮
      cy.get('[data-testid="pass-button"]').click()
      
      // 验证提示信息
      cy.get('[data-testid="toast"]').should('contain', '请选择要操作的行')
    })

    it('应该支持批量通过', () => {
      // 选择第一条记录
      cy.get('[data-testid="delivery-plan-table"] tbody tr:first-child')
        .find('input[type="checkbox"]')
        .check()
      
      // 点击通过按钮
      cy.get('[data-testid="pass-button"]').click()
      
      // 确认操作
      cy.get('[data-testid="confirm-dialog"]').should('be.visible')
      cy.get('[data-testid="confirm-button"]').click()
      
      // 验证操作成功
      cy.get('[data-testid="toast"]').should('contain', '通过成功')
      
      // 验证数据已刷新
      cy.get('[data-testid="loading"]').should('not.exist')
    })

    it('应该支持批量驳回', () => {
      // 选择第一条记录
      cy.get('[data-testid="delivery-plan-table"] tbody tr:first-child')
        .find('input[type="checkbox"]')
        .check()
      
      // 点击驳回按钮
      cy.get('[data-testid="reject-button"]').click()
      
      // 输入驳回原因
      cy.get('[data-testid="reject-dialog"]').should('be.visible')
      cy.get('[data-testid="reject-reason-input"]').type('数据不符合要求')
      cy.get('[data-testid="reject-confirm-button"]').click()
      
      // 验证操作成功
      cy.get('[data-testid="toast"]').should('contain', '驳回成功')
    })

    it('应该支持批量删除', () => {
      // 切换到草稿状态数据
      cy.get('[data-testid="status-selector"]').click()
      cy.get('[data-testid="status-option-0"]').click() // 草稿
      cy.get('[data-testid="search-button"]').click()
      cy.get('[data-testid="loading"]').should('not.exist')
      
      // 选择记录
      cy.get('[data-testid="delivery-plan-table"] tbody tr:first-child')
        .find('input[type="checkbox"]')
        .check()
      
      // 点击删除按钮
      cy.get('[data-testid="delete-button"]').click()
      
      // 确认删除
      cy.get('[data-testid="confirm-dialog"]').should('be.visible')
      cy.get('[data-testid="confirm-button"]').click()
      
      // 验证操作成功
      cy.get('[data-testid="toast"]').should('contain', '删除成功')
    })
  })

  describe('导出功能', () => {
    it('应该支持导出数据', () => {
      // 点击导出按钮
      cy.get('[data-testid="export-button"]').click()
      
      // 验证下载开始（这里可能需要根据实际情况调整）
      cy.get('[data-testid="export-button"]').should('contain', '导出中')
      
      // 等待导出完成
      cy.get('[data-testid="export-button"]', { timeout: 10000 })
        .should('not.contain', '导出中')
    })
  })

  describe('明细页面', () => {
    beforeEach(() => {
      // 切换到明细Tab
      cy.get('.toggle-tab').contains('明细').click()
      cy.get('[data-testid="detail-container"]').should('be.visible')
    })

    it('应该显示明细数据', () => {
      // 验证明细表格
      cy.get('[data-testid="detail-table"]').should('be.visible')
      
      // 验证明细表格列
      const detailColumns = [
        '送货计划单号', '物料编码', '物料名称', '计划送货量', 
        '计划送货日期', '可用库存', '在途库存'
      ]
      
      detailColumns.forEach(column => {
        cy.get('[data-testid="detail-table"] thead').should('contain', column)
      })
    })

    it('应该支持明细搜索', () => {
      // 输入送货计划单号
      cy.get('[data-testid="detail-plan-code-input"]').type('DP202312010001')
      
      // 点击搜索
      cy.get('[data-testid="detail-search-button"]').click()
      
      // 验证搜索结果
      cy.get('[data-testid="loading"]').should('not.exist')
      cy.get('[data-testid="detail-table"] tbody tr').should('have.length.at.least', 1)
    })

    it('应该支持明细导出', () => {
      // 点击导出按钮
      cy.get('[data-testid="detail-export-button"]').click()
      
      // 验证导出开始
      cy.get('[data-testid="detail-export-button"]').should('contain', '导出中')
    })
  })

  describe('异常处理', () => {
    it('应该处理网络错误', () => {
      // 模拟网络错误
      cy.intercept('POST', '**/delivery/plan/buyer/page/query', {
        statusCode: 500,
        body: { message: '服务器错误' }
      }).as('serverError')
      
      // 触发搜索
      cy.get('[data-testid="search-button"]').click()
      
      // 验证错误处理
      cy.wait('@serverError')
      cy.get('[data-testid="toast"]').should('contain', '获取数据失败')
    })

    it('应该处理空数据', () => {
      // 模拟空数据响应
      cy.intercept('POST', '**/delivery/plan/buyer/page/query', {
        statusCode: 200,
        body: {
          code: 200,
          data: {
            records: [],
            total: 0
          }
        }
      }).as('emptyData')
      
      // 触发搜索
      cy.get('[data-testid="search-button"]').click()
      
      // 验证空数据显示
      cy.wait('@emptyData')
      cy.get('[data-testid="empty-data"]').should('be.visible')
      cy.get('[data-testid="empty-data"]').should('contain', '暂无数据')
    })
  })
})

// 自定义命令
Cypress.Commands.add('login', () => {
  // 登录逻辑
  cy.visit('/login')
  cy.get('[data-testid="username"]').type('testuser')
  cy.get('[data-testid="password"]').type('password')
  cy.get('[data-testid="login-button"]').click()
  cy.url().should('not.include', '/login')
})
