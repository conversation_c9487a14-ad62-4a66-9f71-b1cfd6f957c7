{"info": {"name": "送货计划API测试集合", "description": "VMI管理-送货计划相关接口测试", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "{{host}}/srm-purchase-pv", "type": "string"}, {"key": "token", "value": "{{authToken}}", "type": "string"}], "item": [{"name": "送货计划列表接口", "item": [{"name": "分页查询送货计划", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": 1,\n    \"size\": 50\n  }\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/buyer/page/query", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "buyer", "page", "query"]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"状态码为200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"响应包含必要字段\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('records');", "    pm.expect(jsonData.data).to.have.property('total');", "});", "", "pm.test(\"响应时间小于3秒\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}]}, {"name": "按送货计划单号查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": 1,\n    \"size\": 50\n  },\n  \"deliveryPlanCode\": \"DP202312010001\"\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/buyer/page/query", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "buyer", "page", "query"]}}, "response": []}, {"name": "按供应商查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": 1,\n    \"size\": 50\n  },\n  \"supplierCodeList\": [\"SUP001\", \"SUP002\"]\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/buyer/page/query", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "buyer", "page", "query"]}}, "response": []}, {"name": "按状态查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": 1,\n    \"size\": 50\n  },\n  \"statusList\": [1, 2]\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/buyer/page/query", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "buyer", "page", "query"]}}, "response": []}]}, {"name": "送货计划操作接口", "item": [{"name": "批量通过送货计划", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/batch/pass", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "batch", "pass"]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"操作成功\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "});"], "type": "text/javascript"}}]}, {"name": "批量驳回送货计划", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"ids\": [1, 2, 3],\n  \"rejectReason\": \"数据不符合要求\"\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/batch/rejected", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "batch", "rejected"]}}, "response": []}, {"name": "批量删除送货计划", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/batch/delete", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "batch", "delete"]}}, "response": []}, {"name": "导出送货计划", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": 1,\n    \"size\": 1000\n  }\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/buyer/export", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "buyer", "export"]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"导出成功\", function () {", "    pm.response.to.have.status(200);", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/vnd.openxmlformats');", "});"], "type": "text/javascript"}}]}]}, {"name": "送货计划明细接口", "item": [{"name": "分页查询明细", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": 1,\n    \"size\": 50\n  }\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/detail/buyer/page/query", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "detail", "buyer", "page", "query"]}}, "response": []}, {"name": "按计划单号查询明细", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": 1,\n    \"size\": 50\n  },\n  \"deliveryPlanCode\": \"DP202312010001\"\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/detail/buyer/page/query", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "detail", "buyer", "page", "query"]}}, "response": []}, {"name": "导出明细数据", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": 1,\n    \"size\": 1000\n  }\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/detail/buyer/export", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "detail", "buyer", "export"]}}, "response": []}]}, {"name": "异常测试", "item": [{"name": "无效分页参数", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": {\n    \"current\": -1,\n    \"size\": 0\n  }\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/buyer/page/query", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "buyer", "page", "query"]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"参数错误处理\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 500]);", "});"], "type": "text/javascript"}}]}, {"name": "空ID列表操作", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"ids\": []\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/batch/delete", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "batch", "delete"]}}, "response": []}, {"name": "不存在的ID操作", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"ids\": [99999, 88888]\n}"}, "url": {"raw": "{{baseUrl}}/tenant/pv/vmi/delivery/plan/batch/delete", "host": ["{{baseUrl}}"], "path": ["tenant", "pv", "vmi", "delivery", "plan", "batch", "delete"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 设置全局变量", "pm.globals.set('timestamp', Date.now());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局测试脚本", "pm.test('响应格式为JSON', function() {", "    pm.response.to.be.json;", "});"]}}]}