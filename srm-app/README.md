#### 介绍
#### 
srm 移动端项目


#### 安装教程

- 使用 HBuidlerX

文件->导入->从本地目录导入

选中项目 src 中任意一个文件，点击运行（或者快捷键 ctrl+r），编译过会自动拉起微信开发者工具 PS：微信开发工具要先开启服务端口 （设置->通用->安全->服务端口开启）

### commit 规范

- feat：新功能（feature）。
- fixbug：修复 bug，可以是 QA 发现的 BUG，也可以是研发自己发现的 BUG。
- update: 更新、修改某些功能
- docs：文档（documentation）。
- style：格式（不影响代码运行的变动）。
- refactor：重构（即不是新增功能，也不是修改 bug 的代码变动）。
- perf：优化相关，比如提升性能、体验。
- test：增加测试。
- chore：构建过程或辅助工具的变动。
- revert：回滚到上一个版本。
- merge：代码合并。
- sync：同步主线或分支的 Bug。

```
提交示例：feat: 1.新增某某页面；2.新增某某功能
示例说明：type + :（英文冒号）+ 空格 + 具体内容
```

### 开启 eslint 配置

- vscode

1. 安装 eslint 插件；
2. 在设置里加上以下配置：

```
"eslint.workingDirectories": [
    ".eslintrc.js",
    {
        "mode": "auto"
    }
],
"editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
},
```

- HBuidlerX 工具-插件安装-安装新插件-前往插件市场分别搜索 eslint-js、eslint-plugin-vue 并安装安装之后选中配置，勾选上自动修复
