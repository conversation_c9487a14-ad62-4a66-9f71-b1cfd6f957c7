import http from '@/utils/http'
import i18n from '@/locale/index.js'
import storage from '@/utils/storage'
import config from '@/config'
import {
  isNotEmpty,
} from '@/utils/tools.js'
import en from '@/locale/en.json'
import zhHans from '@/locale/zh-Hans.json'
import vi from '@/locale/vi.json'

const state = {
  language: storage.getItemSync('locale') ? storage.getItemSync('locale') : uni.getLocale(),
  messages: {},
  localeList: [],
  timezone: storage.getItemSync('timezone') ? storage.getItemSync('timezone') : 'GMT+8',
}

const getters = {
  language: (state) => state.language,
  timezone: (state) => state.timezone,
}

const mutations = {
  SET_MESSAGES: (state, messages) => {
    state.messages = messages
  },
  SET_LOCALELIST: (state, localeList) => {
    state.localeList = localeList
  },
  SET_LANGUAGE: (state, lang) => {
    state.language = lang
    storage.setItemSync('locale', lang)
  },
  SET_TIMEZONE: (state, timezone) => {
    state.timezone = timezone
    storage.setItemSync('timezone', timezone)
  },
}

const actions = {
  // 获取语言类型
  async getLocaleList({
    commit,
  }) {
    const res = await http.setOnceConfig({
      setting: {
        showLoading: false,
      },
    }).post('/tms-platform-backend/v1/0/baseCenter/tmsLocale/pageTmsLocale', {
        status: 2,
      },
      // {
      //   cache: true,
      //   storage_expire: 1000 * 60 * 60 * 24 * 2, // 缓存2天
      // }
    )
    if (res.code === 0) {
      commit('SET_LOCALELIST', res.data.content)
    }
  },
  // 获取模块词条
  async getModuleMessages({
    state,
  }, moduleCodeList = []) {
    // 请求模块对应的词条内容
    const codeList = [...config.comModuleCodes, ...moduleCodeList]
    let lang = state.language
    if (!lang) {
      lang = 'zh-Hans'
    }

    const version = config.version
    const map = {
      en,
      'zh-Hans': zhHans,
      vi,
    }
    // 从缓冲中读取数据，写入i18n
    const messages = {
      ...map[lang],
    }
    // 找出本地不存在的模块
    const requestList = codeList.filter(code => {
      const data = storage.getItemExpire(`${code}.${lang}.${version}`)
      if (data !== null && data !== undefined) {
        messages[code] = data
      }
      return data === null || data === undefined
    })
    let isSuccess = true
    // 请求后台，放入当前缓存中
    if (requestList.length > 0) {
      isSuccess = false
      let langTemp = lang
      if (lang === 'zh-Hans') {
        langTemp = 'zh_CN'
      } else if (lang === 'vi') {
        langTemp = 'vi_VN'
      } else if (lang === 'en') {
        langTemp = 'en_US'
      }
      const res = await http.setOnceConfig({
        setting: {
          showLoading: false,
        },
      }).post('xxxx', requestList)
      if (res.code === 0) {
        isSuccess = true
        const data = res.data
        Object.keys(data).forEach(moduleCode => {
          const key = `${moduleCode}.${lang}.${version}`
          // 保留i18nExpire天
          storage.setItemExpire(key, data[moduleCode], 1000 * 60 * 60 * 24 * config.i18nExpire)
        })
        requestList.forEach(code => {
          const data = storage.getItemExpire(`${code}.${lang}.${version}`)
          if (data !== null && data !== undefined) {
            messages[code] = data
          }
        })
      }
    }

    i18n.setLocaleMessage(lang, messages)
    i18n.locale = lang
    // uni.setLocale(lang)
    return isSuccess
  },
  async saveLocale({
    commit,
  }, lang) {
    if (!isNotEmpty(storage.getItemSync('AccessToken'))) {
      // 未登录时，调用设置临时语音状态接口，当前此接口只在pc端登录有效，应用登录未实现语音关联
      // await http.post('/oauth/login/lang', {
      //   lang: lang === 'zh-Hans' ? 'zh_CN' : lang,
      // })
    } else {
      // 设置用户语言环境
      let language = lang || 'zh_CN'
      if (lang === 'zh-Hans') {
        language = 'zh_CN'
      } else if (lang === 'vi') {
        language = 'vi_VN'
      } else if (lang === 'en') {
        language = 'en_US'
      }
      await http.put(`/iam/tcl/v1/users/default-language?language=${language}`)
    }
    commit('SET_LANGUAGE', lang)
  },
  async getMiddlegroundLang(context, lang) {
    const isLogin = isNotEmpty(storage.getItemSync('AccessToken'))
    if (!isLogin) return false
    // 设置用户语言环境
    let language = lang || 'zh_CN'
    if (lang === 'zh-Hans') {
      language = 'zh_CN'
    } else if (lang === 'vi') {
      language = 'vi_VN'
    } else if (lang === 'en') {
      language = 'en_US'
    }
    const res = await http.setOnceConfig({
      setting: {
        showLoading: false,
      },
    }).post('/oauth/login/lang?lang=' + language)
    return res
  },
  setTimezone({
    commit,
  }, timezone) {
    commit('SET_TIMEZONE', timezone)
    return true
  },
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}
