import http from '@/utils/http'
import storage from '@/utils/storage'
import {encrypt,decrypt} from '@/utils/encrypt.js'
import {
  formdata,
  useTCaptcha
} from '@/utils/tools.js'

const state = {
  token: storage.getItemSync('token'),
  role: storage.getItemSync('role'),
  userInfo: storage.getItemSync('userInfo'),
  username: storage.getItemSync('username'),
  password: storage.getItemSync('password'),
}

const getters = {
  token: (state) => state.token,
  role: (state) => state.role,
  userInfo: (state) => state.userInfo,
  username: (state) => state.username,
  password: (state) => state.password,
}

const mutations = {
  SET_USERNAME: (state, username) => {
    state.username = username
    storage.setItemSync('username', username)
  },
  SET_PASSWORD: (state, password) => {
    state.password = encrypt(password)
    storage.setItemSync('password', encrypt(password))
  },
  SET_TOKEN: (state, token) => {
    state.token = token
    storage.setItemSync('token', token)
  },
  SET_ROLE: (state, role) => {
    state.role = role
    storage.setItemSync('role', role)
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo
    storage.setItemSync('userInfo', userInfo)
  },
  CLEAR_LOGININFO:(state) => {
    state.username = null
    state.password = null
    storage.removeItemAsync('username')
    storage.removeItemAsync('password')
  },
  CLEAR_STORE: (state) => {
    state.token = null
    state.role = null
    state.userInfo = null
    storage.removeItemAsync('token')
    storage.removeItemAsync('role')
    storage.removeItemAsync('userInfo')
  },

}

const actions = {
  // 用户登录
  async login({
    commit,
    dispatch,
  }, userInfo) {
    let {
      username,
      password,
      rememberChecked
    } = userInfo
    const params = {
      username, // 用户名（采,供）
      password: encrypt(password), // 密码
    }
    const res = await http.postForm('/api/iam/app-api/public-api/login/appLogin', formdata(params))
    if (res.code === 200) {
      storage.setItemSync('token', res.data.token)
      storage.setItemSync('role', res.data.role)
      commit('SET_TOKEN', res.data.token)
      commit('SET_ROLE', res.data.role)
      commit('SET_ROLE', res.data.role)
      dispatch('getUserInfo')
      if(rememberChecked) {
        commit('SET_USERNAME', username)
        commit('SET_PASSWORD', password)
      } else {
        commit('CLEAR_LOGININFO', password)
      }
    }
    return res || {}
  },
  // 用户登出
  async logout({
    commit,
    dispatch,
  }) {
    commit('CLEAR_STORE')
    return
    const res = await http.get('/api/iam/common/logout')
    if (res.code === 200) {
      commit('CLEAR_STORE')
    }
    return res || {}
  },
  // 获取用户信息
  async getUserInfo({
    commit,
    dispatch,
    state,
  }, playload = true) {
    const res = await http.get(`/api/iam/app-api/${state.role === 1 ? 'purchase' : 'supplier'}/account/userinfo`,{},{
            showLoading: false
          })
    if (res.code === 200) {
      storage.setItemSync('userInfo', res.data)
      commit('SET_USERINFO', res.data)
    }
    return res
  },
  // 获取验证码
  async getMailVertCode({
    commit,
    dispatch,
    state,
  }, userInfo) {
    let {
      userName,
      mail,
    } = userInfo
    const params = {
      userName,
      mail
    }
    const resCaptcha = await useTCaptcha();
    let res = {}
    if (resCaptcha.randstr && resCaptcha.ticket) {
      res = await http.post(`/api/iam/app-api/public-api/account/mail_password_reset_code`, {
        ...params,
        rand: resCaptcha.randstr,
        ticket: resCaptcha.ticket
      })
    }
    return res
  },
  // 验证码验证
  async validVertCode({
    commit,
    dispatch,
    state,
  }, userInfo) {
    return await http.post(`/api/iam/app-api/public-api/account/mail_password_reset_code/check`, {
      ...userInfo
    })
  },

  // 密码重置
  async resetPwd({
    commit,
    dispatch,
    state,
  }, info) {
    return await http.post(`/api/iam/app-api/public-api/account/password/reset`, {
      ...info,
    })
  },
}


export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}
