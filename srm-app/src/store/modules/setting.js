import http from '@/utils/http'
import storage from '@/utils/storage'

const state = {
  notify: storage.getItemSync('notify'),
  voice: storage.getItemSync('password'),
}

const getters = {
  notify: (state) => state.notify, // 1 关闭  2 开启
  voice: (state) => state.voice, // 1 关闭  2 开启
}

const mutations = {
  SET_NOTIFY: (state, notify) => {
    state.notify = notify
    storage.setItemSync('notify', notify)
  },
  SET_VOICE: (state, voice) => {
    state.voice = voice
    storage.setItemSync('voice', voice)
  },
}



export default {
  namespaced: true,
  state,
  getters,
  mutations,
}
