/**
* 返回监听
*/
export const backPress = {
  onLoad() {
      // #ifdef H5
      window.addEventListener('popstate', this.back)
      // #endif
  },
  onUnload() {
      // #ifdef H5
      window.removeEventListener("popstate", this.back);
      // #endif
  },
  onBackPress(e) {
    if (e.from === 'backbutton') {
      this.back()
      return true;
    }
  },
  methods: {
    back(){
      const searchVisible =  this.$refs.searchPage.searrchVisible // 搜索页面
      const detailVisible =  this.$refs.searchPage.detailVisible // 详情页面
      if(searchVisible) {
         this.$refs.searchPage.handleBack()
      }
      if(detailVisible) {
        this.$refs.searchPage.handleDetailBack()
      }
      if(!searchVisible && !detailVisible) {
       this.$refs.searchPage.back()
      }
    }
  }
}
/**
 * 是否显示 已全部加载完毕 提示
 * @param 传入节点类名即可获取
 */
import {
  getDomInfo,
} from '@/utils/tools'

export const isShowMixin = {
  data() {
    return {
      systemHeight: 0, // 设备可使用窗口高度
      pageHeight: 0, // 页面总高度
    }
  },
  onLoad() {
    // #ifndef H5 || APP-PLUS
    const {
      top,
      height
    } = uni.getMenuButtonBoundingClientRect()
    const {
      screenHeight
    } = getApp().globalData.systemInfo // 屏幕高度
    const navigationBarHeight = top + height // 导航栏的高度
    this.systemHeight = screenHeight - navigationBarHeight
    // #endif
  },
  methods: {
    // 获取 dom 信息
    getDomInfo(ele) {
      getDomInfo(ele, res => {
        this.pageHeight = res ? res.height : 0
      })
    },
  },
  computed: {
    isShow() {
      return this.pageHeight > this.systemHeight
    },
  },
}
