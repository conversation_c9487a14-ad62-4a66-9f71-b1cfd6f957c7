.flex {
  display: flex;
}

.flex-r {
  display: flex;
  flex-direction: row-reverse;
}

.flex-acen-sb {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.flex-row-center {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.flex-row-acenter {
  display: flex;
  flex-direction: row;
	align-items: center;
}

.flex-colr {
  display: flex;
  flex-direction: column-reverse;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

.flex-wrapr {
  display: flex;
  flex-wrap: wrap-reverse;
}

.flex-jcon-start {
  display: flex;
  justify-content: flex-start;
}

.flex-jcon-end {
  display: flex;
  justify-content: flex-end;
}

.flex-jcon-center {
  display: flex;
  justify-content: center;
}

.flex-jcon-sb {
  display: flex;
  justify-content: space-between;
}

.flex-jcon-sa {
  display: flex;
  justify-content: space-around;
}

.flex-aitem-start {
  display: flex;
  align-items: flex-start;
}

.flex-aitem-end {
  display: flex;
  align-items: flex-end;
}

.flex-aitem-center {
  display: flex;
  align-items: center;
}

.flex-aitem-baseline {
  display: flex;
  align-items: baseline;
}

.flex-aitem-stretch {
  display: flex;
  align-items: stretch;
}

.flex-acon-start {
  display: flex;
  align-content: flex-start;
}

.flex-acon-end {
  display: flex;
  align-content: flex-end;
}

.flex-acon-center {
  display: flex;
  align-content: flex-center;
}

.flex-acon-sb {
  display: flex;
  align-content: space-between;
}

.flex-acon-sa {
  display: flex;
  align-content: space-around;
}

.flex-acon-stretch {
  display: flex;
  align-content: stretch;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex1 {
  flex: 1;
}

image,
img {
  vertical-align: middle;
}

/*
@font-face {
    font-family: PingFangSC-Regular;
    src: url('./fonts/PingFang Regular.ttf');
}

@font-face {
    font-family: PingFangSC-Semibold;
    src: url('./fonts/PingFang Bold.ttf');
}

@font-face {
    font-family: PingFangSC-Medium;
    src: url('./fonts/PingFang Medium.ttf');
} */

body {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: 0;

  /* padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom); */
  font-size: 24rpx;
}

uni-page-body {
  font-size: 24rpx !important;
}

view {
  box-sizing: border-box;
}

.text-placeholder {
  color: #999;
}

.text-gray {
  color: #999;
}

/* 文本超出两行隐藏 */
.text-hidden-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 文本超出一行隐藏 */
.text-hidden-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
}

.text-bold {
  font-weight: 550;
}

/* 文字方向 */
.t-center {
  text-align: center;
}

.t-left {
  text-align: left;
}

.t-right {
  text-align: right;
}

/* .f18 {
  font-size: 18rpx;
}

.f20 {
  font-size: 20rpx;
}

.f22 {
  font-size: 22rpx;
}

.f24 {
  font-size: 24rpx;
}

.f26 {
  font-size: 26rpx;
}

.f28 {
  font-size: 28rpx;
}

.f30 {
  font-size: 30rpx;
}

.f32 {
  font-size: 32rpx;
}

.f34 {
  font-size: 34rpx;
}

.f36 {
  font-size: 36rpx;
}

.f38 {
  font-size: 38rpx;
}

.f40 {
  font-size: 40rpx;
} */

/**
 * H1
 */

/* .f48 {
  font-size: 48rpx;
} */

.h96 {
  height: 96rpx;
}

.h120 {
  height: 120rpx;
}

.h144 {
  height: 144rpx;
}

.wp100 {
  width: 100%;
}

.hp100 {
  height: 100%;
}

.mleft8 {
  margin-left: 8rpx;
}

.p-t-96 {
  padding-top: 96rpx;
}

.p-b-144 {
  padding-bottom: 144rpx;
}

.p-t-48 {
  padding-top: 48px !important;
}

/* .mleft16 {
  margin-left: 16rpx;
}

.mleft24 {
  margin-left: 24rpx;
}

.mleft32 {
  margin-left: 32rpx;
}

.mleft64 {
  margin-left: 64rpx;
}

.mtop8 {
  margin-top: 8rpx;
}

.mtop16 {
  margin-top: 16rpx;
}

.mtop24 {
  margin-top: 24rpx;
}

.mtop32 {
  margin-top: 32rpx;
}

.mtop40 {
  margin-top: 40rpx;
}

.mtop60 {
  margin-top: 60rpx;
}

.pleft24 {
  padding-left: 24rpx;
}

.pleft32 {
  padding-left: 32rpx;
}

.pright32 {
  padding-right: 32rpx;
} */

/* 全部加载完毕样式 */
.load-end {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 96rpx;
  font-size: 22rpx;
  color: #999;
}

.ptop96-bg-def-wrap {
  min-height: 100vh;
  padding-top: 96rpx;
  background: #f6f8ff;
}

/* 遮罩层样式 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
}

.box-shadow {
	box-shadow: 0 0 24rpx 0 rgba(207, 208, 210, 0.08);
}

.text-center {
  text-align: center;
}

.w80 {
    width: 80rpx;
  }

.w120 {
  width: 120rpx;
}
