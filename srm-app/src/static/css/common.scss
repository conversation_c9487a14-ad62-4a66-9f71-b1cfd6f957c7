image {
  vertical-align: middle;
}

// 兼容iphoneX这类有刘海屏的安全区域
body {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  font-family: PingFangSC-Medium, PingFang SC;
  background-color: $color-bg-def;
}

.safe-area {
  padding-bottom: 0 !important;
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}

.color-bg-def {
  background-color: $color-bg-def;
}

.color-main {
  color: $color-main;
}

.color-text-black {
  color: $color-text-black;
}

.color-text-depgray {
  color: $color-text-depgray;
}

.color-text-gray {
  color: $color-text-gray;
}

.color-text-orange {
  color: $color-text-orange;
}

.color-text-red {
  color: $color-text-red;
}

.color-text-blue {
  color: $color-text-blue;
}

.color-text-green {
  color: $color-text-green;
}

.color-text-orange {
  color: $color-text-orange;
}

.color-text-white {
  color: $color-text-white;
}

.color-text-lightgrey {
  color: $color-text-lightgrey;
}

.color-bg-white {
  background-color: $color-text-white;
}

.color-bg-gray {
  background-color: $color-bg-gray;
}

// 定义内外边距，历遍1-80
@for $i from 0 through 80 {
  // 只要双数和能被5除尽的数
  @if $i % 2 == 0 or $i % 5 == 0 {
    // 得出：margin-30或者m-30
    .m-#{$i} {
      margin: $i + rpx !important;
    }

    // 得出：p-30
    .p-#{$i} {
      padding: $i + rpx !important;
    }

    @each $short, $long in l left, t top, r right, b bottom {
      // 缩写版，结果如： m-l-30
      // 定义外边距
      .m-#{$short}-#{$i} {
        margin-#{$long}: $i + rpx !important;
      }

      // 定义内边距
      .p-#{$short}-#{$i} {
        padding-#{$long}: $i + rpx !important;
      }
    }
  }
}

.p-t-96 {
  padding-top: 96rpx;
}

.p-b-144 {
  padding-bottom: 144rpx;
}

// 定义字体(rpx)单位，大于或等于16的都为rpx单位字体 fs-18
@for $i from 16 through 48 {
  .fs-#{$i} {
    font-size: $i + rpx;
  }
}

/* 待处理状态样式 */
.state-padding {
	background: $color-text-orange;
}

.state-cancel {
	background: $color-text-gray;
}

.state-done {
	background: #52C41A;
}

.state-danger {
  background: $color-text-red;
}