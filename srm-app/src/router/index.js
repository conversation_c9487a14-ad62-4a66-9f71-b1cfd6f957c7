const install = (Vue, vm) => {
  const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
  // 用遍历的方式分别为,uni.navigateTo,uni.redirectTo,uni.reLaunch,uni.switchTab这4个路由方法添加拦截器
  list.forEach(item => {
    uni.addInterceptor(item, {
      invoke(e) {
        // 获取要跳转的页面路径（url去掉"?"和"?"后的参数）
        const url = e.url.split('?')[0]
        const routers = url.split('/')
        if (routers.length > 1) {
          const rootPages = routers[1]
          if (rootPages && rootPages !== 'pages') {
            // 取分包多语言词条
            Vue.prototype.$store.dispatch('locale/getModuleMessages', Vue.prototype.$config.moduleCodes[rootPages])
          }
        }
        return e
      },
      fail(err) { // 失败回调拦截
        console.log(err)
      },
    })
  })
}

export default {
  install,
}
