import Vue from 'vue'
import App from './App.vue'
import http from '@/utils/http'
import store from './store'
import _ from 'lodash'
import i18n from './locale'
import { $tl } from '@/utils/i18n'

import httpInterceptor from '@/utils/http/http.interceptor.js'
import message from '@/utils/message'
import dayjs from 'dayjs' // ES 2015
import config from '@/config'
import storage from '@/utils/storage'
import router from '@/router'

// #ifdef H5
// 提交前需要注释  本地调试使用
// const Vconsole = require('vconsole')
// Vue.prototype.$vconsole = new Vconsole() // 使用vconsole

// #endif

Vue.config.productionTip = false
Vue.prototype.$http = http
Vue.prototype.$message = message
Vue.prototype.$storage = storage
Vue.prototype.$dayjs = dayjs
Vue.prototype.$store = store
Vue.prototype.$config = config
Vue.prototype.$_ = _
Vue.prototype.$tl = $tl
Vue.use(httpInterceptor)
Vue.use(router)

App.mpType = 'app'
const app = new Vue({
  ...App,
  store,
	i18n,
})
app.$mount()
