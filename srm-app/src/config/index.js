const baseURL = {
  dev: 'https://srm-uat-h5.eads.tcl.com', //开发环境
  sit: 'https://srm-uat-h5.eads.tcl.com', // 测试环境
  uat: 'https://srm-uat-h5.eads.tcl.com', // uat 环境
  prod: 'https://srm-h5.tcl.com' // 生产环境
}
let env = ''
// #ifdef WEB || APP-PLUS
  env = process.env.NODE_ENV === 'production' ? 'prod':'uat'
// #endif

let webviewHost = env === 'prod' ? 'https://srm-h5.tcl.com' : 'https://srm-uat-h5.eads.tcl.com'
export default {
  /**
   * @description 接口请求baseurl
   */
  baseURL,
  webviewHost: webviewHost,
  /**
   * @description 调试环境 sit：测试环境，uat：uat环境，prod：生产环境
   */
  env,
  /**
   * @description 消息订阅
   */
  subscriptions: {
    key1: '',
  },
  /**
   * 词条过期时间（单位：天）
   */
  i18nExpire: 1,
  /**
   * 词条存储空间
   */
  i18nNamespace: 'SRM_LOCALE',
}
