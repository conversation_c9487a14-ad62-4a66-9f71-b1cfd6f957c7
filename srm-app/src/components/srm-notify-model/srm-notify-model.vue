<template>
  <view class="mask" @touchmove.stop.prevent @click.stop>
    <view class="mask-content">
      <view class="t-center text-bold fs-32 p-b-24">{{ title }}</view>
      <scroll-view class="content flex1" :scroll-y="true" :lower-threshold="30" :scroll-anchoring="true" @scrolltolower="scrolltolower">
        <slot name="hd" />
        <view v-for="(item, index) in content" :key="index" :class="item.class">{{ item.value }}</view>
      </scroll-view>
      <view class="footer">
        <view v-if="!tolower" class="disabled btn hp100">{{ $tl('mp_common_text.upArgee|请上滑看完本条款再同意') }}</view>
        <view v-else class="sure btn hp100" @click="confirm">{{ confirmText }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import { $tl } from '@/utils/i18n'
  export default {
    props: {
      title: {
        type: String,
        default: `《${$tl('mp_common_text.admissionSafetyNotice|入园安全告知书')}》`,
      },
      confirmText: {
        type: String,
        default: $tl('mp_mainpackage_home.agree|同意'),
      },
      content: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        tolower: false,
      }
    },
    methods: {
      confirm() {
        this.$emit('confirm')
      },
      scrolltolower() {
        this.tolower = true
      },
    },
  }
</script>

<style lang="scss" scoped>
  /* component/modal/modal.wxss */
  .mask {
    position: fixed;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.64);
    z-index: 9999;
  }

  .mask-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 85%;
    height: 760rpx;
    padding: 40rpx 0 24rpx;
    border-radius: 32rpx;
    color: #000000;
    font-size: 28rpx;
    background: rgba(255, 255, 255, 1);
    line-height: 44rpx;
  }

  .mask-content .content {
    box-sizing: border-box;
    padding: 0 48rpx 16rpx;
    overflow: auto;
  }

  .mask-content .content .item {
    text-indent: 56rpx;
  }

  .mask-content .content .text-align-right {
    text-align: right;
  }

  .footer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 80rpx;
    color: #fff;
    font-size: 36rpx;
    padding-bottom: 8rpx;
  }

  .footer .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 80rpx;
    color: #fff;
    font-size: 32rpx;
    border-radius: 40rpx;
  }

  .footer .btn.disabled {
    background-color: $color-text-gray;;
  }

  .footer .sure {
    background-color: $color-main;
  }
</style>
