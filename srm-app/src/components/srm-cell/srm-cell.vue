<template>
  <view class="srm-content" :class="{'h112':type==='info'}" @click="action">
    <view class="left-content" :style="{alignSelf: type==='info' ? 'flex-start' : 'center'}">
      <image v-if="leftIcon !== ''" class="left-icon" :src="leftIcon" />
      <text class="srm-fs-32 sccolor-text-black" :class="{need:need}">{{ title }}</text>
    </view>
    <view class="right-content" :class="{ flex1: type!=='picker'}">
      <block v-if="type==='input'">
        <input v-model.trim="pvalue" class="right-input" :class="{'sccolor-text-blue':isPhone, 'colorRed': colorRed}"
          :disabled="disabled" :type="inputType" :maxlength="!$sc.test.isEmpty(maxLength)?maxLength:200"
          :placeholder="placeholder" placeholder-class="text-placeholder" style="border:0;outline:medium;"
          @input="inputChange" @click="inputClick" @blur="inputBlur">
        <block v-if="rightIcon !== ''">
          <image :src="rightIcon" mode="widthFix" class="srm-m-l-16" :style="[rIconSize]" @click.stop="rightAction" />
        </block>
      </block>

      <block v-if="type==='info'">
        <view class="srm-flex-rowcenter">
          <text class="srm-fs-32 sccolor-text-depgray" v-if="value">{{value}}</text>
          <text class="srm-fs-32 sccolor-text-gray" v-else>{{placeholder}}</text>
          <block v-if="iconShow">
            <image v-if="rightIcon !== ''" :src="rightIcon" mode="widthFix" class="more-nav" :style="[rIconSize]" />
            <image v-else class="more-nav" :src="moreNav" mode="widthFix" />
          </block>
        </view>
      </block>
      <block v-if="type==='slot'">
        <slot />
      </block>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'SrmCell',
    props: {
      /**
       * @description 右边类型，可选1.input：输入框，2.picker：选择框，3.slot:插槽，4.info 只展示，不带右边文案显示
       * */
      type: {
        type: String,
        default: 'input',
      },
      /**
       * @description 是否必填
       * */
      need: {
        type: Boolean,
        default: false,
      },
      /**
       * @description 左边图标
       * */
      leftIcon: {
        type: String,
        default: '',
      },
      /*
       * @description 是否只读
       * */
      disabled: {
        type: Boolean,
        default: false,
      },
      /**
       * @description 左边文案
       * */
      title: {
        type: String,
        default: '',
      },
      /**
       * @description 右边文案
       * */
      placeholder: {
        type: String,
        default: '',
      },
      /**
       * @description 右边图标
       * */
      rightIcon: {
        type: String,
        default: '',
      },
      /**
       * @description 右边图标是否显示
       * */
      iconShow: {
        type: Boolean,
        default: true,
      },
      /**
       * @description 当type=input时，input的类型
       * */
      inputType: {
        type: String,
        default: 'text',
      },
      /**
       * @description 当type=input时，input的最大输入值，默认200
       * */
      maxLength: {
        type: [String, Number],
        default: '200',
      },
      /**
       * @description 当type=picker时，picker的类型，其中值为dateTime时，显示自定时间选择框
       * */
      pickerMode: {
        type: String,
        default: 'selector',
      },
      /**
       * @description picker数据源
       * */
      range: {
        type: Array,
        default () {
          return []
        },
      },
      /**
       * @description picker range-key 当 range 是一个二维 Array＜Object＞ 时，
       * 通过 range-key 来指定 Object 中 key 的值作为选择器显示内容
       * */
      rangeKey: {
        type: String,
        default: '',
      },
      /**
       * @description 有效日期范围的开始，字符串格式为"YYYY-MM-DD"
       * */
      startDate: {
        type: String,
        default: '',
      },
      /**
       * @description 有效日期范围的结束，字符串格式为"YYYY-MM-DD"
       * */
      endDate: {
        type: String,
        default: '',
      },
      /**
       * @description 点击事件
       * */
      clickAction: {
        type: String,
        default: '',
      },
      /**
       * @description 输入框值
       * */
      value: {
        type: String,
        default: '',
      },
      /**
       * @description picker选择下拉index
       * */
      index: {
        type: Number,
        default: -1
      },
      /**
       * @description 距离当前最早能选择的时间，单位为分钟，默认20分钟
       */
      startMinutes: {
        type: Number,
        default: () => {
          return 20
        }
      },
      /**
       * @description 是否为电话
       */
      isPhone: {
        type: Boolean,
        default: false,
      },
      /**
       * @description 红色文字
       */
      colorRed: {
        type: Boolean,
        default: false,
      },
      /**
       * @description 属性
       */
      propertry: {
        type: [String, Number],
        default: ''
      }
    },
    data() {
      return {
        moreNav: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAA/VJREFUaEPtmcuLXEUUh3+n7722b0LiY2JUxKgYxQduRDFMTzLBB66DRmLb3beqJOLChTv9C9yJqFU1PWMPEhGR4GtCXGSyEBeKwYQoakQITBJfea2GnunuI2XaEMeZqZK+t6cDudv6cev77qmqU00TLvCHLnB+XBRY6QrmUoGpqanizMzMKwC2MvMQEe1n5leVUl9kLZy5ADMXrLV7mXl4AWwHwEtKqdezlMhcwBjzHDNPLAP5olLqjawk8hBoMPOzywES0QtSyjezkMhcQGv9DoCyR4AB7JBSvt2rROYCAUvob2YiYiJ6XghhepHIXKC7ifcx80YfmJNgZqmUGvNllxrPXMBNNDY2trrdbu8FcJ8PzEkASKWU477sYuO5CLiJGo3Gmmaz6Y7TewPAOkRUk1K6/fO/ntwEHIXW+hoArhL3BFB1CoVCRQgxGZA9F8lVwM0yPj5+bavVmmbmuwPAnERZCPFuQPbsYRAa7CU3OTl53ezs7DSAuwLe4zr2dqXUzoBsfwQciLX2emZ2ldjgAyOiNhFtF0K85836AlmOT0xMDM3NzblK3Ol7b1fiGSHE+8tl+7KEzgfQWq8lItcn7vBJAGgB2KaU+mCpbN8FHEi9Xr+h1WrtA3B7iAQRPSWl/LCvfcAH1mg01jWbTVeJ23xZVwki2iql3LUwuyIV+AfCWntjp9NxlVgfINFMkuSBarX6/fnZFRXo9omb5ufnncStARKfKaWeHCgBB6O1vhnAlwDWLXviEJ2UUq4ZOAFr7RPMvIuZL/EIHJFS3jJQAsaYxx08gGLAEnpLKbVjYASMMY8y80ch8ER0JEmS+yuVyumBELDWbmHmj5n5Ut+XJ6KZOI6Hq9XqLwNxjBpjRgF8EgIP4GgURaU0TX8eiEZmrd3U6XQ+BXBZwJc/liRJqVKpHB6Iq4TWegSAg7/cBw/gOBGVpJQ/DcRlzhgzzMxTgfC/xnFcqtVqP/pE+9KJrbUbmXk3M1/hBSL6jZlLSqkffFk3nruA1voRALsBXBkA9HuSJCML7zsrtoSMMQ8z854QeCL6A8CIlPK7ANFzkdwqUK/XH2q323uY+aoAoD+jKBpJ0/RQQPZfkVwEtNYPAvgcwNUBQCcKhcImIcTBgOx/IpkLdH+oHGLmVQFAJ+I43lyr1Q4EZBeNZC5gjNnJzE/7gIjoJIDNUspvfdm+bmKt9TEAaz1QpwCMKqX29wKfyzGqtT4OYGjJ1k90mohGhRDf9Aqfi4BnCZ0hoi1Syq+zgM9FoLuJDzLz6gWQp6IoeixN06+ygs9FwL20K/GauxKc/TOGpovF4svlcvlolvC5CWQN2ddTqJ/wFyvQ76+92Hx/Aa7xfUBGvmMQAAAAAElFTkSuQmCC',
        closePic: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAMKADAAQAAAABAAAAMAAAAADbN2wMAAAFQUlEQVRoBdVaTUgkRxSumfF3RXRkESVIQOagIKKwEvEgu6A5BGXjRQ8m8RB07jnklMBC9rSHvavkILtevBhRPIyHzUlM8CQic4gHFRUkOMqgjviX7+t0NT0zXXZ1z4+zBVqvqt579b2qV6+qqyYg8pAWFhaqz87OBh8eHl5BXVsgEIiADiOvpXrQSdAJ5P+gGAf9qb6+fm10dPSK7bmkgF/h+fn58MXFxWuAeg0dX+PvmUddl+CPwZilmpqapfHx8YRHeYPdswHT09PP0OlPkP4Z4I0R9tOxXQb6kii/g7730WiUhmknbQPgJqHz8/Mf7+/v30B7s3YP3hiPg8Hgm7q6ut/hXnc6oloGzM3NfZFKpf6Awhc6SvPAs1lVVfXtxMTEoZsuVwPgMl9BySL+CjXqKozHaBiBS/2lYmB98LHG2dnZ7+Cff4Kn2OAJq5l9EwMLqqScAQrC3z+oBItZj3Xx/eTk5EenPh0NoNvQekSFKiehYtcBSwpYXjq5U5YLccEC4GKpgOdgmVgWTWxp45dmAEOlGW2ewufTgDkUmomNGO1taQYwzqOxWKHSjkOXfmFitPitNcAdFrU8q5Ti6FuAQTC8RuSObc0AFgqPB1rgKyoqxMDAgBgbGxNdXV125dp0WVmZaG9vF62trdoyJiPDK7EayZgB82C2h8Widbbp6ekR3d3dUofY3t4W6+vrVtmNqK6uFkNDQyIcDhusW1tbYmNjw03MaocBSRwAv+QB0JgB81SpBZ5aysvLLWUkOjo6RF9fX1qdqpAJnnwtLS0qdsd6DjQxs9EwABVGwZHboZIjdnWVfpTXMcIJPNUfHroeebJQSMwBfowkEol/weHpPI8PEjE8PCwIyp5U7qQCv7+/L2KxmMCub1ejQ1/CBZ+HBgcHvwH3DzoSdh7EZMHOuQjtLtXY2CgqKyvFwcGBxV4A8NRdfn19/XcQU/HK6skjgc9Isby8/Kg7FQi8gZTYy0C1ecSdxi6NyHQnrolQKCSampqsaCMFc3AbqULmbUGEpIgs+c2lEZkLm3FehkqpO4/gBbHThf4PxrIHn7nKCLu6fIKnXmLnDGjHfzsYJ5pGqCJKMplUtjnp0qkjdusooSPgxsMF29/fL/ABksVaW1srent7s+pzraAL8Uoj56SKNnbFOpudnd+NJna6kK8LJbtyFfijo6NHQ6xdhx+a2DkDPEL7Tirwe3t7YnV11XWf8N0xBImdzhr3q+Qx8Gtra8bxQBWd8uROcbrQJz8G6ICXegtlBLEHeUuMjjzdR3oBbzdiZWXFcU34jE6XxB40r7hjsiOdHAfArB2WPi/dRqUDp17hZERnZ6doaGhQianqY8RuBGxMxZKKK7Nenm/s9TrgJb/KCH5iekkSs2EA7+dRobUf3N3dCYZHmbyAlzI0gqfY09NTY6Hv7OyIk5MT2eyaEysxk9G6lZiZmfkFYek3V2kwcLQikYi4ubkRu7u7OiJKHoBhOFS2OzVA5tepqam3bLP2fCh5jzKvLFzT7e2tiMfjOYNnR17BQ+TYxGrgtAzgPQsfF4zaEv5HjPJOiDAtA1jgywiyTdIlmjZNjBa8NAP4rMOXEbRquZKlpTjEMbFlPj2lGUAc5rPOCBZKqji43HsxsYw4PTllGUB1vIeH0KS76uJwEAsxOfXmaAAZ+SLCl5GnnAn2TQyq1xnitPYBFpxSqT/yuRpAo0r5mVXpQvbZ4OLB9UgvpjOK+kJGKD50R9mX04K1Y5K01gxIZuZ8CIFvfn4/NbAbQfqz/bFHpiEs84YbX11P8nOb/wC5zZYlJmbsLwAAAABJRU5ErkJggg==',
        pvalue: this.value,
        pIndex: this.index,
        rIconSize: {
          width: '42rpx',
        },
        selTime: ''
      }
    },
    computed: {},
    watch: {
      value(newVal) {
        this.pvalue = newVal
      },
      index(newVal) {
        this.pIndex = newVal
      }
    },
    methods: {
      pickerChange(e) {
        if (this.pickerMode === 'date') { // 日期选择
          const {
            value
          } = e.detail
          this.pvalue = value
          this.$emit('update:value', this.pvalue)
          this.$emit('change', value)
          return
        }
        this.pIndex = Number(e.detail.value)
        const obj = this.range[this.pIndex]
        if (!this.$sc.test.isEmpty(this.rangeKey)) {
          this.pvalue = obj[this.rangeKey]
        } else {
          this.pvalue = obj
        }
        this.$emit('update:value', this.pvalue)
        this.$emit('change', obj)
      },
      inputChange(e) {
        let {
          value
        } = e.detail
        if (this.inputType === 'number') {
          value = value.replace(/\s+/g, '')
        }
        this.$emit('update:value', value)
        this.$emit('input', e)
      },
      inputBlur() {
        this.$emit('blur', this.propertry)
      },
      deleteValue() {
        if (this.pickerMode === 'dateTime') {
          this.selTime = ''
          this.$emit('delete')
          return
        }
        if (this.pickerMode === 'date') {
          this.pvalue = ''
        } else {
          this.pIndex = -1
        }
        this.$emit('update:value', '')
        this.$emit('delete')
      },
      action() {
        if (this.type === 'info') {
          this.$emit('click', this.clickAction)
        }
      },
      inputClick() {
        if (this.disabled) {
          this.rightAction()
        }
      },
      rightAction() {
        if (this.clickAction !== '') {
          this.$emit('click', this.clickAction)
        } else {
          this.$emit('click')
        }
      },
      dateTimeChange(dateTime) {
        this.selTime = dateTime
        this.$emit('update:value', this.selTime)
        this.$emit('change', this.selTime)
      }
    },
  }
</script>

<style lang="scss" scoped>
  .srm-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 30rpx;
    padding-bottom: 30rpx;
    font-size: 32rpx;
    width: 100%;

    .left-content {
      min-width: 230rpx;

      .left-icon {
        width: 42rpx;
        height: 42rpx;
        margin-right: 26rpx;
      }

      .need {
        margin-left: -25rpx;

        &::before {
          content: '*';
          color: red;
          width: 25rpx;
          height: 23rpx;
          display: inline-block;
          vertical-align: middle;
          text-align: center;
          line-height: 25rpx;
        }
      }
    }

    .right-content {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      word-break: break-all;

      &.flex1 {
        flex: 1;
        justify-content: flex-end;
      }

      .right-input {
        flex: 1;
        width: 100%;
        height: 60rpx;
        font-size: 32rpx;
        color: #666;
        text-align: right;
        font-weight: 500;
        font-family: PingFangsrm-Regular, PingFang SC;

        &.colorRed {
          color: red;
        }
      }

      .more-nav {
        width: 28rpx;
        height: 28rpx;
        margin-left: 16rpx;

        &.m-l-40 {
          margin-left: 40rpx;
        }
      }

      .delete {
        position: absolute;
        top: 6rpx;
        right: 26rpx;
        width: 32rpx;
        height: 32rpx;
      }
    }

    .h112 {
      height: 112rpx !important;
    }
  }

  ::v-deep .text-placeholder {
    color: #999999;
  }
</style>
