<template>
  <view class="search-container">
    <uni-nav-bar left-icon="left" :title="$t('高级搜索')" @clickLeft="handleBack" />
    <view class="form-container">
      <view v-for="(item,index) in fields" :key="index">
        <!-- 下拉选择框 -->
        <view class="form-item flex-aitem-center" v-if="item.type === 'select'">
          <text class="label">{{item.fieldName}}</text>
          <view class="item">
            <srm-select v-model="formData[item.fieldCode]" :fillName="formData[`${item.fieldCode}_label`]"
              :fields="item.fields" :filedCode="item.fieldCode" :dataSource="item.dataSource" :queryKey="item.queryKey" :method="item.method"
              :url="item.url" @onSelected="handleSelected" @onClear="handleClear" :showSearch="item.showSearch"></srm-select>
          </view>
        </view>
        <!-- 下拉多选框 -->
        <view class="form-item flex-aitem-center" v-else-if="item.type === 'multiSelect'">
          <text class="label">{{item.fieldName}}</text>
          <view class="item">
            <srm-select v-model="formData[item.fieldCode]" :fillName="formData[`${item.fieldCode}_label`]"
              :fillList="formData[`${item.fieldCode}_list`]" :fields="item.fields" :filedCode="item.fieldCode"
              :queryKey="item.queryKey" :dataSource="item.dataSource" :showSearch="item.showSearch" :method="item.method" :url="item.url" @onSelected="handleMultiSelected"
              @onClear="handleClearMulti" multiple></srm-select>
          </view>
        </view>
        <!-- 数量选择框 -->
        <view class="form-item flex-aitem-center" v-else-if="item.type === 'number'">
          <text class="label">{{item.fieldName}}</text>
          <view class="item">
            <srm-number v-model="formData[item.fieldCode]" :fillName="formData[`${item.fieldCode}_label`]"
              :fields="item.fields" :filedCode="item.fieldCode" @onClear="handleClear"
              @onConfirm="handleNumConfirm"></srm-number>
          </view>
        </view>
        <!-- 时间选择框 -->
        <view class="form-item flex-aitem-center" v-else-if="item.type === 'dateRange'">
          <text class="label">{{item.fieldName}}</text>
          <view class="item">
            <uni-easyinput v-model="formData[item.fieldCode]" class="flex1 input-calendar"
              placeholderStyle="color: #999999;font-size:32rpx;" :inputBorder="false" :placeholder="$tl('开始日期 → 截止日期')"
              @input="calendarInput" @focus="calendarOpen(item.fieldCode)" @clear="handleClearDate(item.fieldCode)"></uni-easyinput>
            <image class="icon-right" style="margin-left:0;" src="/static/image/icon-right.png"></image>
          </view>
        </view>
        <!-- 输入框 -->
        <view class="form-item flex-aitem-center" v-else>
          <text class="label">{{item.fieldName}}</text>
          <view class="item">
            <uni-easyinput class="input-item" v-model="formData[item.fieldCode]" placeholder="请输入"
              placeholderStyle="color: #999999;font-size:32rpx;" :inputBorder="false" @clear="handleClearIpt(item.fieldCode)"></uni-easyinput>
          </view>
        </view>
      </view>

    </view>
    <view>
      <uni-calendar ref="calendar" :range="true" :insert="false" :lunar="false" :start-date="startDate"
        :end-date="endDate" @confirm="dateConfirm" />
    </view>
    <view class="btn-group">
      <view class="btn-item" @click="handleReset">{{$tl('重置')}}</view>
      <view class="btn-item primary" @click="handleConfirm">{{$tl('确认')}}</view>
    </view>
  </view>
</template>

<script>
  import dayjs from '@/utils/dayjs.js'
  import {
    formatDatetime,
  } from '@/utils/tools'
  import {
    isEmpty,
    cloneDeep
  } from 'lodash'
  export default {
    components: {},
    data() {
      return {
        formData: {},
        selectValue: '',
        startDate: null,
        endDate: formatDatetime(new Date('2099-12-31'), 'YYYY-MM-DD'),
        currentTimeCode: ''
      }
    },
    props: {
      /**
       * @description 查询字段
       * */
      fields: {
        type: Array,
        default: () => [],
      },
    },

    watch: {
      fields: {
        handler: function(val) {
          this.initData(val)
        },
        immediate: true
      },
    },

    methods: {
      // 初始化数据
      initData(fields) {
        // 设置 formData 设置默认值
        if (!fields) return
        let _formData = {}
        // 从配置项里面取
        fields.forEach(item => {
          _formData[item.fieldCode] = item.defaultValue || ''
          if (item.type === 'select') {
            _formData[`${item.fieldCode}_label`] = item.defaultLabelValue || ''
          }
        })
        // 从缓存里面取
        const searchData = uni.getStorageSync('searchData')
        if (!isEmpty(searchData)) {
          for (let i in searchData) {
            _formData[i] = searchData[i]
          }
        }
        this.formData = {
          ...this.formData,
          ..._formData,
        }
      },
      // 清除
      handleClear(e) {
        this.$set(this.formData, `${e.fieldCode}`, null)
        this.$set(this.formData, `${e.fieldCode}_label`, null)
      },
      // 清除多选
      handleClearMulti(e) {
        this.$set(this.formData, `${e.fieldCode}`, null)
        this.$set(this.formData, `${e.fieldCode}_label`, null)
        this.$set(this.formData, `${e.fieldCode}_list`, null)
        this.$set(this.formData, `${e.fieldCode}List`, null)
      },
      // 清除日期
      handleClearDate(e) {
        this.$set(this.formData, `${e}S`, null)
        this.$set(this.formData, `${e}E`, null)
        this.$set(this.formData, `${e}`, null)
      },
      // 清除输入框
      handleClearIpt(e) {
        this.$set(this.formData, `${e}`, null)
      },
      // 选中
      handleSelected(e) {
        this.$set(this.formData, `${e.fieldCode}`, e.value)
        this.$set(this.formData, `${e.fieldCode}_label`, e.label)
      },
      // 多选
      handleMultiSelected(e) {
        // 多选这些字段作本地缓存用，不与后端对接
        this.$set(this.formData, `${e.fieldCode}`, null)
        this.$set(this.formData, `${e.fieldCode}_label`, e.label)
        this.$set(this.formData, `${e.fieldCode}_list`, e.list)
        // 与后端交互数据
        this.$set(this.formData, `${e.fieldCode}List`, e.value)
      },
      // 数量选择
      handleNumConfirm(e) {
        this.$set(this.formData, `${e.fieldCode}_label`, e.label)
        this.$set(this.formData, `${e.fieldCode}`, e.value)
      },
      // 重置
      handleReset() {
        this.$emit('onReset')
        uni.setStorageSync('searchData', {})
      },
      // 确认
      handleConfirm() {
        let _formData = cloneDeep(this.formData)
        if(!isEmpty(_formData)) {
          for(let i in _formData){
            if(!_formData[i]) delete _formData[i]
          }
        }
        this.$emit('onConfirm', _formData)
        uni.setStorageSync('searchData', _formData)
      },
      // back
      handleBack() {
        this.$emit('onBack')
      },
      calendarInput(e) {
        this.formData[this.currentTimeCode] = e
      },
      // 打开日历
      calendarOpen(fieldCode) {
        this.currentTimeCode = fieldCode
        this.$refs.calendar.open()
      },
      // 日期框 确认
      dateConfirm(e) {
        const {
          range,
        } = e
        if (!range.before || !range.after) return
        this.formData[this.currentTimeCode] = range.data[0] + ' → ' + range.data[range.data.length - 1]
        const obj = {
          [`${this.currentTimeCode}S`]: dayjs(dayjs(range.data[0]).format('YYYY-MM-DD 00:00:00')).valueOf(),
          [`${this.currentTimeCode}E`]: dayjs(dayjs(range.data[range.data.length - 1]).format(
            'YYYY-MM-DD 23:59:59')).valueOf()
        }
        this.formData = {
          ...this.formData,
          ...obj,
          [`${this.currentTimeCode}`]: formatDatetime(range.data[0], 'YYYY-MM-DD') + ' → ' + formatDatetime(
            range.data[range.data.length - 1], 'YYYY-MM-DD')
        }
        // this.$emit('dateChange', [range.data[0], range.data[range.data.length - 1]])
      },
    },
  }
</script>

<style lang="scss" scope;>
  .form-container {
    background: #ffffff;
    padding: 0 32rpx;
    height: calc(100vh - 160px);
    overflow-y: auto;
    .form-item {
      height: 120rpx;

      .label {
        font-size: 32rpx;
        color: #333333;
      }

      .item {
        display: flex;
        flex: 1;
        align-items: center;
        margin-left: auto;

        .input-item {
          text-align: right;
          width: 100%;
        }

        .custom-select {
          width: 100%;
          text-align: right;
          display: -webkit-box;
          /* 使用Webkit的盒子模型 */
          -webkit-line-clamp: 2;
          /* 限制最多显示2行 */
          -webkit-box-orient: vertical;
          /* 设置盒子内容的排列方向为垂直 */
          overflow: hidden;
          /* 隐藏超出容器的内容 */
          text-overflow: ellipsis;
          font-size: 32rpx;
        }

        .input-calendar {
          text-align: right;
        }

        .custom-select-placeholder {
          color: #999999;
        }
      }

      .icon-right {
        margin-left: 16rpx;
        width: 24rpx;
        height: 24rpx;
        margin-top: 5rpx;
      }
    }
  }

  .btn-group {
    position: fixed;
    display: flex;
    justify-content: space-between;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #ffffff;
    padding: 32rpx;

    .btn-item {
      width: 320rpx;
      height: 80rpx;
      text-align: center;
      line-height: 80rpx;
      border-radius: 80rpx;
      border: 1px solid #4E5A70;
      color: #4E5A70;
      font-weight: 500;
      font-size: 32rpx;

      &.primary {
        background: #4E5A70;
        color: #ffffff;
      }
    }
  }
</style>
