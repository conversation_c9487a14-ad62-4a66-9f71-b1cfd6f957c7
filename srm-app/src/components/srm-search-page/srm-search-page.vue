<template>
  <view>
    <srm-paging ref="paging" v-model="dataList" @query="queryList" @onRefresh="pageRefresh">
      <template #top>
        <uni-nav-bar left-icon="left" :title="title" @clickLeft="back" :right-icon="rightIcon"
          @clickRight="clickRight" />
        <view class="search-wrapper">
          <view class="search-box wp100 flex-aitem-center">
            <!-- 日期搜索框 start -->
            <view v-if="config.type==='dateRange'" class="search-input flex1 flex-aitem-center" @click="calendarOpen">
              <view class="search-input-icon flex-aitem-center">
                <image class="icon-calendar" src="/static/image/mine/icon-calendar.png" />
              </view>
              <uni-easyinput v-model="dateSearchValue" class="flex1 input-calendar"
                placeholderStyle="color: #999999;font-size:32rpx;" :inputBorder="false"
                :placeholder="$tl('开始日期 → 截止日期')" @input="calendarInput" disabled></uni-easyinput>
            </view>
            <!-- 日期搜索框 end -->

            <!-- input搜索框 start -->
            <view v-else class="search-input flex1 flex-aitem-center">
              <view class="search-input-icon flex-aitem-center" style="margin-left: 32rpx;">
                <image class="icon-search" :src="iconSearch" />
              </view>

              <uni-easyinput v-model="searchValue" class="flex1 input-general" style="background:blue;"
                :placeholder="placeholder" placeholderStyle="color: #999999;font-size:32rpx;" :inputBorder="false"
                @input="handleSearchInput"></uni-easyinput>
            </view>
            <!-- input搜索框 end -->

            <view class="hp100 flex-aitem-center p-l-24" @click="handleSearchPopup">
              <image :src="iconFilter" class="icon-filter-44" />
              <view class="m-l-16 fs-32 color-text-black">{{ $tl('更多') }}</view>
            </view>
            <view>
              <uni-calendar ref="calendar" :range="true" :insert="false" :lunar="false" :start-date="startDate"
                :end-date="endDate" @confirm="dateConfirm" />
            </view>
          </view>
          <!-- tab -->
          <srm-tabs v-model="currentTabCode" :tabs="config.tabs" :fieldCode="statusFieldCode"
            @change="handleTabChange"></srm-tabs>
          <view class="total-count">
            <text class="total-label">总条数: </text>
            <text class="total-num">{{totalNum}}</text>
          </view>
        </view>
      </template>

      <view class="content-wrapper">
        <view v-for="(item,key) in dataList" :key="key">
          <srm-card :config="cardConfig" :dataSource="item" :currentTabCode="currentTabCode"
            @toDetail="handleDetailPopup(item)" :checked="config.checked"></srm-card>
        </view>
      </view>
       <template #bottom>
          <view class="page-footer" v-if="config.checked && toolbar.length">
        <!--    <view class="selected-all" @click="handleClickAll">
              <image class="icon-check"
                :src="selectedAll ? '/static/image/home/<USER>' : '/static/image/home/<USER>'" />
              <text class="selected-all-text">全选</text>
            </view> -->
           <!-- 此处展示先取第一个，后续封装支持多个 -->
            <view class=page-footer-btn-group>
              <view v-for="(item, key) in toolbar" :key="key">
                <view class="page-footer-btn" :key="item.code" @click="handleClickToolbar(item)">{{item.text}}</view>
              </view>
            </view>
          </view>
        </template>
    </srm-paging>
    <!-- 详情页面 -->
    <uni-popup class="popup-container" ref="detailPopup" type="right" background-color="#F1F5F8">
      <search-detail :title="title" :config="cardConfig" :dataSource="detailDataSource" @onBack="handleDetailBack"
        :currentTabCode="currentTabCode">
        <slot></slot>
      </search-detail>
    </uni-popup>
    <!-- 高级搜索 -->
    <uni-popup class="popup-container" ref="popup" type="right" background-color="#F1F5F8">
      <search-form @onReset="handleReset" :fields="searchConfig" @onConfirm="handleConfirm"
        @onBack="handleBack"></search-form>
    </uni-popup>
  </view>
</template>

<script>
  import dayjs from '@/utils/dayjs.js'
  import {
    $tl,
  } from '@/utils/i18n'
  import {
    isEmpty,
    cloneDeep,
    debounce
  } from 'lodash'

  import {
    formatDatetime,
  } from '@/utils/tools'
  import SearchDetail from './search-detail.vue'
  import SearchForm from './search-form.vue'
  import http from '@/utils/http'
  import {
    mapGetters,
  } from 'vuex'
  export default {
    components: {
      SearchForm,
      SearchDetail
    },
    props: {
      /**
       * @description 标题
       * */
      title: {
        type: String,
        default: '',
      },
      rightIcon: {
        type: String,
        default: '',
      },
      /**
       * @description 输入框类型 input | dateRange
       * */
      config: {
        type: Object,
        default: {},
      },
      cardConfig: {
        type: Object,
        default: {},
      },
      /**
       * @description placeholder 提示
       * */
      placeholder: {
        type: String,
        default: $tl('请输入'),
      },
    },
    data() {
      return {
        formData: {},
        currentTabCode: null,
        dateSearchValue: '',
        searchValue: '',
        startDate: null,
        endDate: formatDatetime(new Date('2099-12-31'), 'YYYY-MM-DD'),
        value: '',
        iconSearch: 'data:image/png;base64,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',
        iconFilter: 'data:image/png;base64,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',
        dataList: [],
        moreSearchForm: {},
        searchConfig: [],
        detailDataSource: {},
        totalNum: 0,
        detailVisible: false,
        searchVisible: false,
        selectedAll: false,
      }
    },

    computed: {
      ...mapGetters('user', {
        role: 'role',
      }),
      statusFieldCode() {
        return this.config?.statusFieldCode || 'status'
      },
      method() { // 请求方法
        return this.config?.method || 'post'
      },
      url() { // 请求url
        return this.role === 1 ? this.config?.url : this.config?.supplierUrl
      },
      params() { // 默认参数
        return this.config?.params || {}
      },
      toolbar() {
        return this.role === 1 ? this.config?.toolbar : this.config?.supplierToolbar
      }
    },
    mounted() {
      this.init()
    },
    beforeDestroy() {
      uni.removeStorageSync('searchData')
    },

    methods: {
      // 初始化
      init() {
        let obj = {
          [`${this.config.headerFieldCode}S`]: this.config.headerDefaultValue[0] ? new Date(formatDatetime(this.config
            .headerDefaultValue[0], 'YYYY-MM-DD') + ' 00:00:00').getTime() : '',
          [`${this.config.headerFieldCode}E`]: this.config.headerDefaultValue[1] ? new Date(formatDatetime(this.config
            .headerDefaultValue[1], 'YYYY-MM-DD') + ' 23:59:59').getTime() : '',
          [`${this.statusFieldCode}`]: this.config?.currentTabCode,
        }
        if (this.config?.type === 'input') {
          obj = {
            [`${this.config.headerFieldCode}`]: this.config.headerDefaultValue,
            [`${this.statusFieldCode}`]: this.config?.currentTabCode,
          }
        }
        this.formData = {
          ...this.formData,
          ...this.params,
          ...obj
        }
        if (this.config?.type === 'dateRange') {
          this.dateSearchValue = formatDatetime(this.config.headerDefaultValue[0], 'YYYY-MM-DD') + ' → ' + formatDatetime(
            this.config.headerDefaultValue[1], 'YYYY-MM-DD')
        } else {
          this.dateSearchValue = ''
        }
        this.currentTabCode = this.config?.statusDefaultValue
        this.config?.statusFieldCode && this.config?.statusDefaultValue !== 'all' && this.$set(this.formData, this
          .config?.statusFieldCode, this.config
          ?.statusDefaultValue)
        this.searchConfig = cloneDeep(this.config.fields)
      },
      // 页面刷新
      pageRefresh() {
        // this.moreSearchForm = {}
      },
      // 点击右侧按钮
      clickRight() {
        this.$emit('clickRight')
      },
      // 查询列表数据
      async queryList(pageNo, pageSize) {
        const pageParams = {
          "page": {
            "current": pageNo,
            "size": pageSize,
          }
        }
        const fields = {
          ...this.formData,
          ...this.moreSearchForm
        }
        const filterFields = [`${this.statusFieldCode}`]
        if (this.config?.defaultNotQuery && this.judgeFieldsEmpty(fields,filterFields)) {
          // 默认不查询
          this.totalNum = 0
          this.$refs.paging.complete([]);
          return
        }
        const res = await http[this.method](this.url, {
          ...this.formData,
          ...pageParams,
          ...this.moreSearchForm
        }).catch(err => {
          this.$refs.paging.complete(false);
        });
        this.totalNum = res.data.total
        this.$refs.paging.complete(res.data?.records || []);
      },
      // 返回
      back() {
        uni.reLaunch({
          url: '/pages/tabBar/home/<USER>',
        })
        // 清空查询缓存
        uni.removeStorageSync('searchData')
      },
      calendarInput(e) {
        this.dateSearchValue = e
        this.$refs.paging.reload();
      },
      // 打开日历
      calendarOpen() {
        this.$refs.calendar.open()
      },
      // 日期框 确认
      dateConfirm(e) {
        const {
          range,
        } = e
        if (!range.before || !range.after) return
        this.dateSearchValue = range.data[0] + ' → ' + range.data[range.data.length - 1]
        const obj = {
          [`${this.config.headerFieldCode}S`]: dayjs(dayjs(range.data[0]).format('YYYY-MM-DD 00:00:00')).valueOf(),
          [`${this.config.headerFieldCode}E`]: dayjs(dayjs(range.data[range.data.length - 1]).format(
            'YYYY-MM-DD 23:59:59')).valueOf()
        }
        this.formData = {
          ...this.formData,
          ...obj
        }
        if (e) {
          const formData = uni.getStorageSync('searchData') || {}
          const searchData = { ...formData, ...obj }
          searchData[this.config.headerFieldCode] = this.dateSearchValue
          uni.setStorageSync('searchData', searchData)
          this.moreSearchForm = {
            ...this.moreSearchForm,
            [`${this.config.headerFieldCode}`]: range.data[0] + ' → ' + range.data[range.data.length - 1],
            ...obj
          }
        }
        this.$refs.paging.reload();
        this.$emit('dateChange', [range.data[0], range.data[range.data.length - 1]])
      },
      // search-input框 确认
      handleSearchInput(e) {
        this.$set(this.formData, this.config.headerFieldCode, e)
        this.inputSearch()
      },
      // 添加参数到 URL
      addUrlParameter(name, value) {
        const url = new URL(window.location.href);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
      },
      // search-input框 模糊搜索
      inputSearch: debounce(function(e) {
        this.$refs.paging.reload();
      }, 1000),
      // 详情页 展开
      handleDetailPopup(item) {
        // #ifdef H5
        window.history.pushState(null, null, document.URL)
        // #endif
        this.detailVisible = true
        this.detailDataSource = {
          ...item
        }
        this.$refs.detailPopup.open('right')
      },
      // 详情页 关闭
      handleDetailBack(item) {
        this.detailVisible = false
        this.$refs.detailPopup.close()
      },
      // 高级搜索 展开
      handleSearchPopup() {
        // this.setSearchConfigValue()
        // #ifdef H5
        window.history.pushState(null, null, document.URL)
        // #endif
        this.searchVisible = true
        this.$refs.popup.open('right')
      },
      // 高级搜索  重置
      handleReset() {
        this.moreSearchForm = {}
        this.$refs.popup.close()
        this.$refs.paging.reload()
      },
      // 高级搜索 返回
      handleBack() {
        this.searchVisible = false
        this.$refs.popup.close()
      },
      // 高级搜索 确认搜索
      handleConfirm(e) {
        this.handleBack()
        if (e[this.config.headerFieldCode]) {
          this.dateSearchValue = e[this.config.headerFieldCode]
          const data = e[this.config.headerFieldCode].split(' → ')
          const range = {
            data,
            before: data[0],
            after: data[data.length - 1]
          }
          this.setSearchData({ range })
          // this.dateConfirm({ range })
          // return
        }
        this.moreSearchForm = {
          ...e
        }
        this.$refs.paging.reload();
      },
      setSearchData(e) {
        const {
          range,
        } = e
        if (!range.before || !range.after) return
        this.dateSearchValue = range.data[0] + ' → ' + range.data[range.data.length - 1]
      },
      // 设置高级搜索默认值
      setSearchConfigValue() {
        if (isEmpty(this.moreSearchForm)) {
          this.searchConfig = cloneDeep(this.config.fields)
        }
        this.searchConfig.forEach(item => {
          if (item.fieldCode in this.moreSearchForm) item.defaultValue = this.moreSearchForm[item.fieldCode]
        })
      },
      // tab 切换
      handleTabChange(e) {
        this.$set(this.formData, this.statusFieldCode, e === 'all' ? '' : e)
        this.$refs.paging.reload();
      },
      judgeFieldsEmpty(obj, filterFields) {
        for (const key in obj) {
          if (!filterFields.includes(key) && obj.hasOwnProperty(key)) {
            if (obj[key] !== "" && obj[key] !== undefined && obj[key] !== null) {
              return false;
            }
          }
        }
        return true;
      },
      // 工具栏点击
      handleClickToolbar(item) {
        const params = {
          e: item,
          data: {
            isSelectedAll: this.selectedAll,
            selectedList: this.getSelectedList()
          }
        }
        this.$emit('handleClickToolbar',params)
      },
      // 点击全选按钮
      handleClickAll() {
        this.selectedAll = !this.selectedAll
        this.dataListAddChecked(this.selectedAll)
      },
      // 处理dataList
      dataListAddChecked(selected) {
         this.dataList = this.dataList.map(item=> {
           return {
             ...item,
             selected
           }
         })
      },
      // 筛选选中的数据
      getSelectedList() {
        return this.dataList.filter(item=> item.selected)
      }
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .is-disabled {
    color: rgb(51, 51, 51);
  }

  .search-wrapper {
    // padding-top: 108rpx;
  }

  .search-box {
    height: 116rpx;
    padding: 0 32rpx;
    background: $color-text-white;
    z-index: 9;
    /*  #ifdef  WEB  */
    // margin-top: 80rpx;
    /*  #endif  */
  }

  .search-input {
    width: 548rpx;
    height: 74rpx;
    padding-right: 28rpx;
    background: $color-bg-def;
    border-radius: 34rpx;

    ::v-deep .uni-easyinput__content {
      background: #f1f5f8 !important;
    }

    .input-calendar,
    .input-general {
      font-size: 28rpx;

    }

    .search-input-icon {
      margin-left: 32rpx;

      .icon-calendar {
        width: 42rpx;
        height: 42rpx;

      }
    }

    .icon-search {
      width: 40rpx;
      height: 40rpx;
    }

    input {
      padding-left: 16rpx;
    }
  }

  .text-placeholder {
    color: #999;
  }


  .icon-filter-44,
  .icon-scan-44 {
    width: 44rpx;
    height: 44rpx;
  }

  .content-wrapper {
    padding: 12rpx 32rpx 32rpx;
  }

  .total-count {
    padding: 12rpx 32rpx 8rpx 32rpx;
    font-size: 28rpx;

    .total-num {
      margin-left: 12rpx;
      color: #3979F9;
    }
  }

  .popup-container {
    height: 100vh;
    top: 1;
  }

  .page-footer {
    background: #ffffff;
    padding: 32rpx;
    display: flex;
    box-shadow: 0 -2px 3px -1px rgba(0, 0, 0, 0.1);
    align-items: center;
    .selected-all {
      display:flex;
        align-items: center;
      .icon-check {
        width: 32rpx;
        height: 32rpx;
        overflow: hidden;
        margin-right:16rpx;
      }
      .selected-all-text {
        font-size: 30rpx;
        color: #666666;
      }
    }

    .page-footer-btn-group {
      margin-left: auto;
      display: flex;
      gap: 10px;
      .page-footer-btn {
        width: 220rpx;
        height: 80rpx;
        border-radius: 44rpx;
        background: #4E5A70;
        color: #ffffff;
        text-align: center;
        line-height: 80rpx;
        font-size: 28rpx;
      }
    }
  }

</style>
