<template>
  <view class="detail-container" style="padding-bottom: 44px;">
    <uni-nav-bar left-icon="left" :title="title" @clickLeft="handleBack" />
    <view class="detail-wrap">
      <view class="inner-wrapper">
        <srm-card type="detail" :config="config" :dataSource="dataSource" :currentTabCode="currentTabCode"></srm-card>
      </view>
      <slot></slot>
    </view>
  </view>
</template>

<script>
  export default {
    components: {},
    data() {
      return {
        formData: {},
        selectValue: '',

      }
    },
    props: {
      /**
       * @description 标题
       * */
      title: {
        type: String,
        default: () => '',
      },
      /**
       * @description 配置信息
       * */
      config: {
        type: Object,
        default: () => {},
      },
      /**
       * @description 配置信息
       * */
      dataSource: {
        type: Object,
        default: () => {},
      },
      // 当前得页签
      currentTabCode: {
        type: [String, Number],
        default: () => '',
      }
    },
    computed: {},
    onLoad(options) {

    },

    mounted() {
      console.log(this.dataSource)
    },
    watch: {
      fields: {
        handler: function(val) {
          // this.initData(val)
        },
        immediate: true
      },
    },

    methods: {
      // 初始化数据
      initData(fields) {
        // 设置 formData 设置默认值
        if (!fields) return
        let _formData = {}
        fields.forEach(item => {
          _formData[item.fieldCode] = item.defaultValue || ''
          if (item.type === 'select') {
            _formData[`${item.fieldCode}_label`] = item.defaultName || ''
          }
        })
        this.formData = {
          ...this.formData,
          ..._formData
        }
      },

      // back
      handleBack() {
        this.$emit('onBack')
      }
    },
  }
</script>

<style lang="scss" scope;>
  .detail-container {
    .detail-wrap {
      height: calc(100vh - 88px);
      /*  #ifdef  APP-PLUS  */
      height: calc(100vh - 88px);
      /*  #endif  */
      overflow: scroll;
      .inner-wrapper {
        padding: 32rpx 32rpx 0 32rpx;
      }
    }
  }
</style>
