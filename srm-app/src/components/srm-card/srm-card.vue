<template>
  <view style="padding-bottom: 24rpx;">
    <view class="card-container">
      <view class="card-top">
        <view class="card-top-title" @click="handleSelectedRecords">
          <view class="card-top_left">
            <image v-if="checked" class="icon-check"
              :src="dataSource.selected ? '/static/image/home/<USER>' : '/static/image/home/<USER>'" />
            <image :src="`/static/image/card/icon-${headerConfig.icon}.png`" class="title-icon" />
            <text selectable>{{getTextByCode(headerConfig.fieldCode,'/')}}</text>
          </view>
          <view class="card-top_right">
            <view class="card-top-status" v-if="headerConfig.statusType === 'text'"
              :style="{fontSize:'32rpx',color: headerConfig.statusColorMap ?  headerConfig.statusColorMap[dataSource[headerConfig.statusColorKey]] : ''}">
              {{dataSource[headerConfig.statusCode]}}
            </view>
            <view class="card-top-status" v-else
              :style="{background:headerConfig.statusColorMap ?  headerConfig.statusColorMap[dataSource[headerConfig.statusColorKey]] : ''}">
              {{dataSource[headerConfig.statusCode]}}
            </view>
            <image v-if="type !== 'detail'" class="arrow-right" src="/static/image/card/icon-arrow-right.png"  @click="toDetail"/>
          </view>
        </view>
        <view class="card-top-simple">
          <view class="simple-item"
            :style="{ flexBasis: getFlexBasis(item),justifyContent: item.right ? 'flex-end' : 'flex-start',color:item.color}"
            v-for="(item,index) in simpleConfig" :class="item.type ? item.type : ''" :key="index">
            <view v-if="item.type === 'bg'" class="bg"
              :style="{background: dataSource[item.colorKey] || dataSource[item.colorKey] === 0 ? item.colorMap[dataSource[item.colorKey]].bg : '#ffffff',color:item.colorMap[dataSource[item.colorKey]].text}">
              <text selectable>{{getTextByCode(item.fieldCode, item.join || '/')}}</text>
            </view>
            <view v-else-if="item.type==='label'">
              <text class="label">{{item.fieldName}}</text>
              <text selectable>{{getTextByCode(item.fieldCode, item.join || '/')}}</text>
            </view>
            <view class="item-text-icon" v-else>
              <image class="simple-item-icon" :src="`/static/image/card/icon-${item.icon}.png`" />
              <text v-if="item.text">{{item.text}}</text>
              <text selectable>{{getTextByCode(item.fieldCode, item.join || '/')}}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="card-content" v-if="type === 'detail'">
        <view class="line" style="margin-bottom: 20rpx;"></view>
        <view class="content-item-wrapper" v-for="(item,i) in detailConfig" :key="i">
          <view class="line" v-if="item.type==='line'"></view>
          <view class="label-item" v-else-if="item.type==='label'">
            <image class="content-label-icon" :src="`/static/image/card/icon-${item.icon}.png`" />
            <view class="label">{{item.fieldName}}</view>
          </view>
          <view class="content-item" v-else>
            <view class="label">{{item.fieldName}}</view>
            <view class="value"><text selectable>{{getTextByCode(item.fieldCode,item.join)}}</text></view>
          </view>
        </view>
      </view>
      <view class="card-footer" v-if="showFooter">
        <view class="card-footer-tips" v-if="displayTime">该状态已停留：{{displayTime}}
        </view>
        <view class="card-footer-btn" v-if="showSendBtn" @click="sendNotice">{{$t('发送提醒')}}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import http from '@/utils/http'
  import {
    mapGetters,
  } from 'vuex'
  export default {
    props: {
      /**
       * @description 卡片类型 default: '' | detail
       * */
      type: {
        type: String,
        default: () => '',
      },
      /**
       * @description 配置
       * */
      config: {
        type: Object,
        default: () => {},
      },
      /**
       * @description 数据源
       * */
      dataSource: {
        type: Object,
        default: () => {},
      },
      // 当前得页签
      currentTabCode: {
        type: [String, Number],
        default: () => '',
      },
      // 是否展示选择按钮
      checked: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      ...mapGetters('user', {
        role: 'role',
      }),
      // 头部信息
      headerConfig() {
        return this.config?.header || {}
      },
      // 简述信息
      simpleConfig() {
        return this.config?.simple || []
      },
      // 详情信息
      detailConfig() {
        return this.config?.detail || []
      },
      // 底部信息
      footerConfig() {
        return this.type === 'detail' ? this.config?.detailFooter : this.config?.footer || null
      },
      // 是否显示发送提醒 目前供应商不显示，在此统一控制，待接口替换后再替换
      showSendBtn() {
        if (this.role !== 1) return false // 是否供方 role 1为采方
        return this.footerConfig.sendBtnTabsArr.includes(this.currentTabCode)
      },
      showFooter() {
        return this.showSendBtn || this.displayTime
      }
    },
    data() {
      return {
        timer: null,
        displayTime: null
      }
    },
    mounted() {
      if (this.dataSource[this.footerConfig?.waitCode] && this.dataSource[this.footerConfig
          ?.waitCode] !== '0') {
        this.timer = setInterval(() => {
          this.timeDifference(this.dataSource[this.footerConfig?.waitCode])
        }, 1000)
      }
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer)
    },
    onUnload() {

    },
    methods: {
      handleSelectedRecords() {
        this.$set(this.dataSource, 'selected', !this.dataSource.selected)
      },
      getFlexBasis(item) {
        if (item.width) {
          return item.width; // 返回配置的宽度
        }
        return '50%'; // 默认宽度
      },
      // 根据fieldCode获取信息
      getTextByCode(code, joinText = '-',substr) {
        let codeList = code.split('_')
        if (!codeList?.length) return ''
        return codeList.map(item => {
          // 此处场景较少，特殊处理，不作配置
          if(item ==='supplierShortName'){
            return this.dataSource[item].substring(0,8)
          }
          return this.dataSource[item]
        }).join(joinText)
      },
      // 跳转至详情页
      toDetail() {
        // 跳转至详情页
        this.$emit('toDetail')
      },
      // 倒计时
      timeDifference(time) {
        if (!time) return ''
        // 如果配置了waitTabsArr
        if(this.footerConfig?.waitTabsArr?.length && !this.footerConfig?.waitTabsArr.includes(this.currentTabCode)){
          return ''
        }
        const inputDate = time.includes('-') ? new Date(time) : new Date(Number(time))
        const currentDate = new Date();
        const diff = currentDate - inputDate;
        const differenceInSeconds = Math.floor(diff / 1000);
        const hours = Math.floor(differenceInSeconds / 3600);
        const minutes = Math.floor((differenceInSeconds % 3600) / 60);
        const seconds = differenceInSeconds % 60;
        // 返回结果
        this.displayTime = `${Math.abs(hours)}小时${Math.abs(minutes)}分钟${Math.abs(seconds)}秒`;
      },
      // 发送提醒
      async sendNotice() {
        // 暂时固定校验接口，目前是统一的，不从配置里面接收
        // const validRes = await http.get(
        //     `/api/contract/app-api/purchase/push/checkPush/${this.config.businessModuleCode}/${this.dataSource['id']}`, )
        //   .catch(err => {
        //     console.log(err)
        //   });
        // if (validRes.data) {
        //   this.$message
        //     .confirm({
        //       confirmText: $tl('确定'),
        //       confirmColor: '#3979F9',
        //       content: this.$tl('您已发送过提醒，是否再次发送') + '?',
        //     })
        //     .then((res) => {
        //       this.sendRequest()
        //     })
        //   return
        // }
        this.sendRequest()
      },
      // 发送请求
      async sendRequest() {
        const pageParams = this.mergeSendPamras()
        const res = await http.post(this.footerConfig.sendUrl, {
          ...pageParams,
        }).catch(err => {
          console.log(err)
        });
        if (res.code === 200) {
          uni.showToast({
            title: '发送提醒成功',
            duration: 2000
          })
        }
      },
      // 发送提醒参数拼接
      mergeSendPamras() {
        let params = {}
        this.footerConfig.sendParams.forEach(item => {
          params[`${item}`] = this.dataSource[item] || ''
          if (item === 'ids') {
            params.ids = [this.dataSource.id]
          }
          if (item === 'queryType') {
            params.queryType = this.currentTabCode
          }
        })
        return params
      }
    }
  }
</script>

<style lang="scss" scoped>
  .card-container {
    background: #FFFFFF;
    box-shadow: 0 8px 10px 0 #b5bfd43b;
    border-radius: 16rpx;

    .card-top {
      .card-top-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx;
        background: #4E5A70;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 16rpx 16rpx 0 0;
        font-size: 32rpx;
        color: #FFFFFF;

        .card-top_left {
          display: flex;
          align-items: center;
          .icon-check {
            width: 32rpx;
            height: 32rpx;
            overflow: hidden;
            margin-right: 30rpx;
          }
          .title-icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 20rpx;
          }
        }

        .card-top_right {
          display: flex;
          align-items: center;

          .card-top-status {
            margin-right: 12rpx;
            padding: 0 8rpx;
            height: 48rpx;
            line-height: 48rpx;
            border-radius: 8rpx;
            font-weight: 400;
            font-size: 26rpx;
            color: #FFFFFF;
          }

          .arrow-right {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }

      .card-top-simple {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 20rpx 20rpx 0 20rpx;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 30rpx;
        color: #333333;
        letter-spacing: 0;

        .simple-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20rpx;

          flex-wrap: wrap;
          .item-text-icon {
            display: flex;
            align-items: center;
          }

          .simple-item-icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
            flex-shrink: 0;
          }

          // &:nth-child(even) {
          //   // justify-content: flex-end;
          // }
          .label {
            color: #999999;
            font-size: 30rpx;
          }

          .bg {
            padding: 6rpx 14rpx;
            border-radius: 8rpx;
            font-size: 24rpx;
          }
        }

      }
    }

    .card-content {
      // border-top: 1rpx dashed #E6E6E6;
      padding: 20rpx 32rpx 2rpx;

      .line {
        width: 100%;
        border-top: 1rpx dashed #E6E6E6;
      }

      .content-item-wrapper {
        padding: 12rpx 0;

        .content-item {
          display: flex;
          align-items: center;

          .line {
            width: 100%;
            border-top: 1rpx dashed #E6E6E6;
            margin-top: 2px;
            margin-bottom: 2px;
          }

          .label {
            font-size: 28rpx;
            color: #666666;
            min-width: 130rpx;
          }

          .value {
            margin-left: auto;
            font-size: 28rpx;
            color: #333333;
            text-align: right;
          }
        }

        .label-item {
          display: flex;
          align-items: center;

          .content-label-icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
          }

          .label {
            font-weight: bold;
            color: #333333;
          }
        }
      }
    }

    .card-footer {
      border-top: 1rpx solid #E6E6E6;
      padding: 16rpx 32rpx;
      display: flex;
      align-items: center;

      .card-footer-tips {
        vertical-align: middle;
        color: #F55448;

        &::before {
          display: inline-block;
          content: '';
          width: 16rpx;
          height: 16rpx;
          background: #F55448;
          border: 2px solid #ffffff6e;
          box-shadow: 0 2px 6px 0 #c63e364d;
          border-radius: 16rpx;
          margin-right: 12rpx;
        }

      }

      .card-footer-btn {
        margin-left: auto;
        width: 176rpx;
        height: 64rpx;
        text-align: center;
        line-height: 64rpx;
        border: 1px solid #3979F9;
        color: #3979F9;
        border-radius: 64rpx;
      }
    }
  }
</style>
