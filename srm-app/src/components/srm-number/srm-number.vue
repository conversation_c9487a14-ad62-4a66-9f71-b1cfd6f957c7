<template>
  <view class="srm-select-wrappper">
    <!-- 选择框 -->
    <view class="srm-select" @click="showModal">
      <uni-easyinput v-model="selectName" class="srm-custom-input" :input-border="false" disabled
        placeholder-style="color: #999999;font-size:30rpx;" :placeholder="placeholder" />
      <uni-icons v-if="selectName" type="close" size="20" color="#999999" @click="handleClear"></uni-icons>
      <image class="icon-right" src="/static/image/icon-right.png" @click="showModal" />
    </view>

    <!-- 搜索框 -->
    <view class="search-modal" :class="isShowModal ? 'show' : ''" @tap="hideModal">
      <view class="search-dialog" @tap.stop="">
        <view v-if="showSearch" class="search-box">
          <uni-easyinput v-model="numValue" placeholder-style="color: #999999;font-size:32rpx;" :input-border="false"
            class="srm-custom-search" placeholder="请输入数量值n"  :focus="isFocus"/>
        </view>

        <view class="search-content">
          <view v-for="(item, index) in list" :key="item.value" class="search-item" @click="handleSelect(item)">
            <image class="icon-check"
              :src="item.value === selectValue ? '/static/image/home/<USER>' : '/static/image/home/<USER>'" />
            <view class="label">{{ item.label }}</view>
          </view>
        </view>
        <!-- 确认 -->
        <view class="btn-group">
          <view class="btn-item" @click="handleCancel">{{ $tl('取消') }}</view>
          <view class="btn-item primary" @click="handleConfirm">{{ $tl('确认') }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import http from '@/utils/http'
  import {
    debounce,
    cloneDeep,
    isEmpty
  } from 'lodash'
  export default {
    name: 'SrmSelect',
    model: {
      prop: 'modelVal',
      event: 'syncChange',
    },
    props: {
      modelVal: {
        type: [Number, String, Array, Object],
        default: null,
      },
      // 是否多选框
      multiple: {
        type: Boolean,
        default: false,
      },
      // 模糊匹配方法
      method: {
        type: String,
        default: 'post',
      },
      // 模糊匹配url
      url: {
        type: String,
        default: '',
      },
      queryKey: {
        type: String,
        default: '',
      },
      // 数据源头
      dataSource: {
        default: () => [],
        type: Array,
      },
      // 数据源匹配对象
      fields: {
        type: Object,
        default: () => {
          return {
            text: 'text',
            value: 'value',
          }
        },
      },
      // 是否显示搜索框
      showSearch: {
        type: Boolean,
        default: true,
      },
      // 占位符
      placeholder: {
        default: '请选择',
        type: String,
      },

      filedCode: {
        // select框字段值
        default: '',
        type: String,
      },
      labelKey: {
        // 指定list中labelKey的值作为下拉框显示内容
        default: 'label',
        type: String,
      },
      disabled: {
        default: false,
        type: Boolean,
      },
      fillName: {
        default: '',
        type: String,
      },
      fillList: {
        default: () => [],
        type: Array
      }
    },
    emits: ['openDeepScroll', 'closeDeepScroll'],
    data() {
      return {
        isShowModal: false,
        activeValue: '', // 选中的value值
        numValue: '',
        options: [],
        list: [{
            label: '>n',
            value: 'gt'
          },
          {
            label: '>=n',
            value: 'ge'
          },
          {
            label: '=n',
            value: 'eq'
          },
          {
            label: '≠n',
            value: 'ne'
          },
          {
            label: '<n',
            value: 'lt'
          },
          {
            label: '<=n',
            value: 'le'
          },
        ],
        searchText: '',
        selectValue: 'eq',
        refreshBySearch: false,
        isClickClear: false,
        isFocus: false
      }
    },
    computed: {
      valueKey() {
        return this.fields.value
      },
      selectName: {
        get() {
          return this.fillName
        },
        set(val) {
          this.fillName = val
        },
      },
      value: {
        get() {
          return this.modelVal
        },
        set(val) {
          this.$emit('syncChange', val)
        },
      },
    },
    mounted() {
      if (!isEmpty(this.value)) {
        this.selectValue = this.value.type
        this.numValue = this.value.num
      }
      // this.selectItem = cloneDeep(this.fillList)
    },
    methods: {
      // 初始化数据
      async initData(pageNo, pageSize) {
        const pageParams = {
          'page': {
            'current': pageNo || 1,
            'size': pageSize || 10,
          },
        }
        const res = await http[this.method](this.url, {
          ...pageParams,
          [`${this.queryKey}`]: this.searchValue,
        }).catch(res => {
          this.$refs.paging.complete(false)
        })
        if (res.code === 200) {
          const _list = this.arrSet(res.data?.records)
          this.$refs.paging.complete(this.dataFormat(_list))
        }
      },

      // 去重 & 拼接label - value
      arrSet(arr) {
        let obj = {}
        const res = arr.reduce((setArr, item) => {
          let _field = item[this.fields.value]
          let _label = this.generateLabel(item)
          if (!obj[_field]) {
            obj[_field] = true
            item.label = _label // 拼接codeValue
            item.selected = false
            setArr.push(item)
          } else {
            let _findIndex = setArr.findIndex((i) => i[_field] == _field)
            setArr[_findIndex] = {
              ...setArr[_findIndex],
              ...item,
              selected: false,
              label: _label, // 拼接codeValue
            }
          }
          return setArr
        }, [])
        return res
      },
      // 生成label (目前支持code-value形式)
      generateLabel(data) {
        if (!data) return ''
        let _text = this.fields.text.split('_')
        let _label = ''
        if (_text?.length > 1) {
          _label = _text.map((item) => data[item]).join('-')
        } else {
          _label = data[this.fields.value] + ' - ' + data[this.fields.text]
        }
        return _label
      },
      handleSearch() {
        this.$emit('search', this.searchInput)
      },
      // 清空slect选中的值
      handleClear(e) {
        this.isClickClear = true
        this.$emit('onClear', {
          fieldCode: this.filedCode,
        })
        setTimeout(() => {
          this.isClickClear = false
        })
      },
      // 取消搜索
      handleCancel() {
        this.hideModal()
      },
      // 确认搜索 （多选时候：待开发）
      handleConfirm() {
        if (!this.numValue && [0, '0'].includes(this.numValue)) {
          this.$message.error(this.$tl('请输入数量值n'))
          return
        }
        const _find = this.list.find(item => item.value === this.selectValue)
        const label = _find.label.split('n')[0] + ' ' + this.numValue

        this.$emit('onConfirm', {
          fieldCode: this.filedCode,
          value: {
            type: this.selectValue,
            num: Number(this.numValue)
          },
          label: label,
        })
        this.hideModal()
      },
      handleSelect(item) {
        this.selectValue = item.value
      },


      // 显示搜索框
      showModal() {
       if (this.isClickClear) return
        // 显示model
        this.isShowModal = true
        setTimeout(() => {
          this.isFocus = true
        }, 500) // 延时显示，避免键盘谈起影响布局
        // 打开禁止穿透滚动
        this.$emit('openDeepScroll')
        // this.initData()
        // this.$refs.paging.reload()
      },
      // 隐藏搜索框
      hideModal() {
        this.isShowModal = false
        his.isFocus = false
        // 关闭禁止穿透滚动
        this.$emit('closeDeepScroll')
      },
    },
  }
</script>
<style lang="scss">
  .title-main {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .title-detail {
    display: flex;
    width: 88%;
    justify-content: center;
    padding: 30rpx 0;
    /*  border-bottom: 1rpx solid #e6e1e1; */
  }

  .icongou:before {
    content: '\e61c';
  }

  .iconcross:before {
    content: '\e61a';
  }
</style>
<style lang="scss" scoped>
  .srm-select-wrappper {
    font-size: 28rpx;
    width: 100%;
  }

  ::v-deep {
    .uni-easyinput__content.is-disabled {
      background: #ffffff !important;
      color: #333333;
    }
  }

  .srm-select {
    display: flex;
    align-items: center;
    width: 100%;

    .srm-custom-input {
      border: none;
      text-align: right;
      padding-left: 16rpx;

      input {
        width: 100%;
        flex: 1;
        color: #333333;
        font-size: 32rpx;
      }
    }

    .icon-right {
      width: 24rpx;
      height: 24rpx;
      vertical-align: middle;
      margin-top: 4rpx;
    }
  }

  .search-modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    opacity: 0;
    outline: 0;
    text-align: center;
    -ms-transform: scale(1.185);
    transform: scale(1.185);
    backface-visibility: hidden;
    perspective: 2000rpx;
    background: rgba(0, 0, 0, 0.6);
    transition: all 0.3s ease-in-out 0s;
    pointer-events: none;
    margin-bottom: -1000rpx;

    &::before {
      content: '\200B';
      display: inline-block;
      height: 100%;
      vertical-align: bottom;
    }

    .search-dialog {
      position: absolute;
      left: 0;
      bottom: 0;
      display: inline-block;
      margin-left: auto;
      margin-right: auto;
      background-color: #ffffff;
      overflow: hidden;
      width: 100%;
      border-radius: 32rpx 32rpx 0 0;

      .search-box {
        display: flex;
        align-items: center;
        padding: 44rpx 32rpx;

        ::v-deep .uni-easyinput__content {
          background: #F1F5F8 !important;
          border-radius: 76rpx;
          font-size: 30rpx;
        }

        ::v-deep .uniui-search {
          padding: 0 16rpx 0 24rpx;
        }

      }

      .search-content {
        height: 50vh;
        overflow: auto;

        .search-item {
          text-align: left;
          display: flex;
          align-items: center;
          border-bottom: 1rpx solid #E0E0E0;
          padding-left: 32rpx;

          .label {
            position: relative;
            box-sizing: border-box;
            flex: 1;
            padding: 32rpx 64rpx 32rpx 16rpx;
            font-size: 32rpx;
            color: #333333;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .icon-check {
            width: 32rpx;
            height: 32rpx;
            border-radius: 50%;
            overflow: hidden;
          }

          &.active {
            .label {
              color: #3979F9;
            }

            ::after {
              position: absolute;
              top: 36rpx;
              right: 24rpx;
              content: '';
              display: inline-block;
              width: 32rpx;
              height: 32rpx;
              background: url('@/static/image/icon-selected.png') no-repeat center center;
              background-size: 32rpx 32rpx;
              margin-left: 16rpx;
              vertical-align: center;
            }
          }
        }
      }
    }
  }

  .search-modal.show {
    opacity: 1;
    transition-duration: 0.3s;
    -ms-transform: scale(1);
    transform: scale(1);
    overflow-x: hidden;
    overflow-y: auto;
    pointer-events: auto;
    margin-bottom: 0;
  }

  .select-bar {
    padding: 0 80rpx;
    display: flex;
    position: relative;
    align-items: center;
    min-height: 80rpx;
    justify-content: space-between;
    margin-bottom: 50rpx;

    .action {
      display: flex;
      align-items: center;
      height: 78rpx;
      justify-content: center;
      max-width: 100%;
      padding: 0 100rpx;
    }
  }

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 100%;
    background: #ffffff;
    padding: 32rpx;
    border-top: 16rpx solid #F1F5F8;

    .btn-item {
      width: 320rpx;
      height: 80rpx;
      text-align: center;
      line-height: 80rpx;
      border-radius: 80rpx;
      border: 1px solid #4E5A70;
      color: #4E5A70;
      font-weight: 500;
      font-size: 32rpx;

      &.primary {
        background: #4E5A70;
        color: #ffffff;
      }
    }
  }

  .single-btn-group {
    background: #ffffff;
    color: #999999;
    height: 120rpx;
    line-height: 120rpx;
    font-size: 36rpx;
    border-top: 10rpx solid #F1F5F8;
  }
</style>
