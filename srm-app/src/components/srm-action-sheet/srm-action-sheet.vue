<template>
  <uni-popup class="popup-container" ref="detailPopup" type="bottom" background-color="#F1F5F8">
    <view class="srm-sheet-content">
        <block>
          <view class="sheet-item" style="justify-content: center;" v-for="(item, index) in items" :key="index"
            @click="handleSelect(item)">
            {{ item.title }}
          </view>
          <view class="confirm-btn" @click="hide">
            {{cancelText}}
          </view>
        </block>
      </view>
  </uni-popup>
</template>

<script>
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      // 确定
      confirmText: {
        type: String,
        default: '确定'
      },
      // 取消
      cancelText: {
        type: String,
        default: '取消'
      },
      /**
       * @description 选项
       * */
      items: {
        type: Array,
        default: () => {
          return []
        }
      }
    },
    methods: {
      show() {
        this.$refs.detailPopup.open('bottom')
      },
      hide() {
         this.$refs.detailPopup.close()
      },
      handleSelect(item){
         this.$emit('secleted',item)
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-popup_content {
     border-top-left-radius: 16rpx !important;
     border-top-right-radius: 16rpx !important;
     overflow: hidden;
  }
  .srm-sheet-content {
    display: flex;
    flex-direction: column;
    padding-top: 24rpx;
    //#ifdef H5
    padding-bottom: 44px;
    //#endif
    color: #333;
    font-size: 36rpx;
    background-color: #fff;

    .sheet-group {
      .wire {
        width: 100%;
        height: 1px;
        background-color: #ffffff;
      }
    }

    .sheet-item {
      display: flex;
      flex-direction: row;
      // justify-content: center;
      align-items: flex-start;
      height: 118rpx;
      line-height: 120rpx;
      width: 100%;
      margin: 0 auto;
    }

    .confirm-btn {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: 120rpx;
      border-top: 10rpx solid  #F1F5F8;
    }
  }


</style>
