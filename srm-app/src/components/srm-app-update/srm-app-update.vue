<!-- 版本更新 -->
<template>
  <view class="mask" v-if="isVisible">
    <view class="mask-content">
      <view class="content">
        <view class="t-center text-bold fs-32">{{ title }} V{{ dataSource.versionNumber }}</view>
        <view class="contentpd">
          <view class="m-b-32">{{ $tl('版本说明') }}：</view>
          <view v-html="dataSource.versionDesc">
          </view>
        </view>
        <view class="tips">
          {{ $tl('后台升级不影响功能使用') }}
        </view>
      </view>
      <view class="footer">
        <!-- <view v-if="cancelText !== ''" class="cancel" @click="cancel">{{ cancelText }}</view> -->
        <view class="sure" @click="confirm">{{ confirmText }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import {
    $tl,
  } from '@/utils/i18n'
  export default {
    props: {
      title: {
        type: String,
        default: $tl('版本更新'),
      },
      confirmText: {
        type: String,
        default: $tl('立即升级'),
      },
      cancelText: {
        type: String,
        default: $tl('忽略'),
      },
      // 是否提示当前最新版本
      showTip: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        isVisible: false
      }
    },
    methods: {
      // 版本更新
      versionUpdate(data) {
        this.dataSource = {
          ...data
        }
        // #ifdef H5
        // h5 直接提示最新版本
        if (this.showTip) {
          uni.showToast({
            title: $tl('当前已是最新版本'),
            duration: 2000,
            icon: 'none',
            mask: false,
          })
        }
        // #endif

        // #ifdef APP-PLUS
        const {
          platform,
          appWgtVersion,
          appVersion
        } = getApp().globalData.systemInfo
        const _appVersion = appWgtVersion || appVersion
        if (this.compareVersion(this.dataSource.versionNumber, _appVersion)) {
          this.isVisible = true
        } else {
          if (this.showTip) {
            uni.showToast({
              title: $tl('当前已是最新版本'),
              duration: 2000,
              icon: 'none',
              mask: false,
            })
          }
        }
        // #endif
      },
      hideModal() {
        this.isVisible = false
      },
      // 确认 下载最新版本
      confirm() {
        const {
          platform
        } = getApp().globalData.systemInfo
        const _host = this.$config === 'prod' ? 'https://srm.tcl.com' : 'https://srm-uat.eads.tcl.com'
        const appleUrl = `itms-services:///?action=download-manifest&url=${_host}/api/purchase/app/public/pc/srm.plist`
        if (platform === 'ios') {
          plus.runtime.openURL(appleUrl)
          return
        }
        this.hideModal()
        uni.showLoading()
        const task = plus.downloader.createDownload(
          this.dataSource.downloadPath, {
            method: 'GET',
          }, (d, status) => {
            if (status == 200) {
              uni.hideLoading()
              console.log('下载成功安装: ' + d.filename)
              plus.runtime.install(d.filename)
            } else {
              uni.hideLoading()
              plus.nativeUI.alert('安装失败，请稍候重试: ' + status)
            }
          },
        )
        task.start()
        this.$emit('confirm')
      },
      // 取消
      cancel() {
        this.hideModal()
        this.$emit('cancel')
      },
      // 比较版本
      compareVersion(newVersion, oldVersion) {
        const newVer = newVersion.split('.');
        const oldVer = oldVersion.split('.');
        for (let i = 0; i < oldVer.length; i++) {
          if (parseInt(oldVer[i]) > (parseInt(newVer[i]) || 0)) {
            return false;
          } else if (parseInt(oldVer[i]) < (parseInt(newVer[i]) || 0)) {
            return true;
          }
        }
        return false;
      }
    }
  }
</script>

<style lang="scss" scoped>
  /* component/modal/modal.wxss */
  .mask {
    position: fixed;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.64);
    z-index: 9999;
  }

  .mask-content {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 40rpx 48rpx;
    border-radius: 32rpx;
    color: #000000;
    font-size: 28rpx;
    background: rgba(255, 255, 255, 1);
    line-height: 44rpx;
  }

  .mask-content .content {
    display: flex;
    flex-direction: column;
    width: 554rpx;
  }

  .mask-content .content .bold {
    font-weight: 600;
    font-size: 32rpx;
    margin-bottom: 10rpx;
  }

  .contentpd {
    padding: 32rpx 0 64rpx;
  }

  .tips {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    margin-bottom: 32rpx;
  }

  .footer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    color: #fff;
    font-size: 36rpx;
    padding-bottom: 8rpx;
  }

  .sure {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 248rpx;
    height: 80rpx;
    color: #fff;
    font-size: 32rpx;
    background-color: $color-main;
    border-radius: 40rpx;
  }

  .cancel {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 248rpx;
    height: 80rpx;
    margin-right: 48rpx;
    color: $color-text-black;
    font-size: 32rpx;
    font-weight: 550;
    background-color: $color-bg-gray;
    border-radius: 40rpx;
  }
</style>
