import {
  get,
} from '@/utils/lodash.js'
import config from '@/config'

export default {
  getItemSync,
  setItemSync,
  removeItemSync,
  getItemAsync,
  setItemAsync,
  removeItemAsync,
  removeAllItems,
  getPathItemSync,
  getPathItemAsync,
  getItemExpire,
  setItemExpire,
}

/**
 * @description 获取 storage 的数据
 * @param {string} key 键值
 * @param {any} [defaultValue] 默认数据
 * @return {any}
 */
function getItemSync(key, defaultValue = '') {
  const data = uni.getStorageSync(key)
  /**
   * @description 数据不存在
   * @type {boolean}
   */
  const isEmpty = typeof data === 'string' && data.length === 0

  return isEmpty ? defaultValue : data
}

/**
 * @description 设置 storage 的数据
 * @param {string} key 键值
 * @param {any} [data] 数据
 */
function setItemSync(key, data) {
  uni.setStorageSync(key, data)
}

/**
 * @description 删除 storage 的数据
 * @param {string} key 键值
 */
function removeItemSync(key) {
  uni.removeStorageSync(key)
}

/**
 * @description 根据 key 与 path 获取 storage 的数据
 * @param {string} key 键值
 * @param {string} path 路径
 * @param {any} [defaultValue] 默认数据
 * @return {any}
 */
function getPathItemSync(key, path, defaultValue = '') {
  const data = uni.getStorageSync(key)

  /**
   * @description 数据不存在
   * @type {boolean}
   */
  const isEmpty = typeof data === 'string' && data.length === 0
  if (isEmpty) return defaultValue

  return path ? get(data, path, defaultValue) : data
}

/**
 * @description 获取 storage 的数据 - 异步
 * @param {string} key 键值
 * @param {any} [defaultValue] 默认数据
 * @return {Promise<any>}
 */
async function getItemAsync(key, defaultValue) {
  /**
   * @description 是否需要返回错误信息（在出错的情况下）
   * @type {boolean}
   */
  const shouldReturnError = typeof defaultValue === 'undefined'

  const [err, res] = await uni.getStorage({
    key,
  })
  if (shouldReturnError) return err ? [err, null] : [null, res.data]
  return err ? defaultValue : res.data
}

/**
 * @description 根据 key 与 path 获取 storage 的数据 - 异步
 * @param {string} key 键值
 * @param {string} path 路径
 * @param {any} [defaultValue] 默认数据
 * @return {Promise<any>}
 */
async function getPathItemAsync(key, path, defaultValue) {
  /**
   * @description 是否需要返回错误信息（在出错的情况下）
   * @type {boolean}
   */
  const shouldReturnError = typeof defaultValue === 'undefined'

  const value = await getItemAsync(key, defaultValue)
  if (shouldReturnError) {
    const [err, data] = value
    if (err) return [err, null]

    const target = path ? get(data, path, defaultValue) : data
    return typeof target === 'undefined' ? [{
      errMsg: 'getStorage:fail',
    }, null] : [null, target]
  }

  return path ? get(value, path, defaultValue) : value
}

/**
 * @description 设置对应 key 的数据 - 异步
 * @param {string} key 键值
 * @param {any} data
 * @return {Promise<any>} 是否有错误
 */
async function setItemAsync(key, data) {
  const [err, res] = await uni.setStorage({
    key,
    data,
  })
  if (res && res.errMsg === 'setStorage:ok') return null
  return err || {
    errMsg: 'setStorage:fail',
  }
}

/**
 * @description 清除对应 key 的数据 - 异步
 * @param {string} key 键值
 * @return {Promise<any>} 是否有错误
 */
async function removeItemAsync(key) {
  const [err, res] = await uni.removeStorage({
    key,
  })
  if (res && res.errMsg === 'removeStorage:ok') return null
  return err || {
    errMsg: 'removeStorage:fail',
  }
}

function removeAllItems() {
  try {
    uni.clearStorageSync()
  } catch (e) {
    console.log(e)
  }
}

/**
 * @description 获取缓存数据-带有效期
 */
function getItemExpire(key, def = null) {
  const item = uni.getStorageSync(`${config.i18nNamespace}_${key}`)

  if (item !== null) {
    try {
      const data = JSON.parse(item)

      if (data.expire === null) {
        return data.value
      }

      if (data.expire >= new Date().getTime()) {
        return data.value
      }

      uni.removeStorageSync(key)
    } catch (err) {
      return def
    }
  }

  return def
}
/**
 * @description 设置环境数据-带有效期
 *
 * @param {string} name
 * @param {*} value
 * @param {number} expire - seconds
 */
function setItemExpire(key, value, expire = null) {
  const stringifyValue = JSON.stringify({
    value,
    expire: expire !== null ? new Date().getTime() + expire : null,
  })

  setItemSync(`${config.i18nNamespace}_${key}`, stringifyValue)
}
