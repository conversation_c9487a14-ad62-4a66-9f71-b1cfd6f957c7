import http from '@/utils/http'
import config from '@/config'
import i18n from '@/locale'
import storage from '@/utils/storage'

/**
 * app 版本更新
 */
export const checkAppVersion = async (data, showTip = false) => {
  /* 版本自动更新代码 */
  // #ifdef APP-PLUS
  const version = data.versionNumber
  const {
    platform,
    appWgtVersion,
    appVersion
  } = getApp().globalData.systemInfo
  const _appVersion = appWgtVersion || appVersion
  if (compareVersion(version, _appVersion)) {
    const {
      versionDesc,
      downloadPath
    } = data
    uni.showModal({
      title: i18n.t('发现新版本'),
      content: versionDesc,
      confirmText: i18n.t('更新'),
      success: (e) => {
        if (e.cancel) return
        if (platform === 'ios') { // 暂不考虑
          plus.runtime.openURL(download_url)
        } else {
          uni.showLoading()
          const task = plus.downloader.createDownload(
            downloadPath, {
              method: 'GET',
            }, (d, status) => {
              if (status == 200) {
                uni.hideLoading()
                console.log('下载成功安装: ' + d.filename)
                plus.runtime.install(d.filename)
              } else {
                uni.hideLoading()
                plus.nativeUI.alert('安装失败，请稍候重试: ' + status)
              }
            },
          )
          task.start()
        }
      },
    })
  } else {
    if (showTip) {
      uni.showToast({
        title: i18n.t('当前已是最新版本'),
        duration: 2000,
        icon: 'none',
        mask: false,
      })
    }
  }
  // #endif
}

/**
 * app 版本对比
 * newVersion 当前发布版本
 * oldVersion 当前系统使用版本
 * return true: 需要更新  false:
 */
const compareVersion = function(newVersion, oldVersion) {
  const newVer = newVersion.split('.');
  const oldVer = oldVersion.split('.');
  for (let i = 0; i < oldVer.length; i++) {
    if (parseInt(oldVer[i]) > (parseInt(newVer[i]) || 0)) {
      return false;
    } else if (parseInt(oldVer[i]) < (parseInt(newVer[i]) || 0)) {
      return true;
    }
  }
  return false;
}
