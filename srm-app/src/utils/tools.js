/* eslint-disable no-mixed-spaces-and-tabs */
/* eslint-disable no-redeclare */
/* eslint-disable no-array-constructor */
import dayjs from '@/utils/dayjs.js'
import { $tl } from '@/utils/i18n'

/**
 * @description 格式化日期时间
 * @param {Date | number | string} date
 * @param {string} template
 * @return {string}
 */
export function formatDatetime(date, template = 'YYYY-MM-DD HH:mm:ss') {
  return dayjs(date).format(template)
}
/**
 * formdata 转换
 * @param obj 要转换的对象
 * @return {string}
 */

export function formdata(obj = {}) {
  let result = ''
  for (let name of Object.keys(obj)) {
    let value = obj[name]
    result +=
      '\r\n--XXX' +
      '\r\nContent-Disposition: form-data; name=\"' + name + '\"' +
      '\r\n' +
      '\r\n' + value
  }
  return result + '\r\n--XXX--'
}
/**
 * 获取小程序节点信息
 * @param ele DOM 节点
 * @param callback 回调函数
 * @return {object}
 */
export function getDomInfo(ele, callback) {
  const query = uni.createSelectorQuery()
  query
    .select(ele)
    .boundingClientRect((res) => {
      callback && callback(res)
    })
    .exec()
}

export const formatNum = (num) => {
  return num < 10 ? '0' + num : num + ''
}
export const isNotEmpty = (str) => {
  if (str === null || str === '' || typeof str === 'undefined') {
    return false
  }

  return true
}

export const isEmpty = (str) => {
  if (str === null || str === '' || typeof str === 'undefined') {
    return false
  }

  return true
}

/**
 * 手机号检验
 * */
export const validatemobile = (mobile, showTip = true, msg = $tl('mp_common_text.inputPhone|请输入手机号'), msg2 = '') => {
  if (!mobile || mobile.length === 0) {
    if (showTip) {
      uni.showToast({
        title: msg,
        icon: 'none',
      })
    }

    return false
  }
  if (mobile.length !== 11) {
    if (showTip) {
      uni.showToast({
        title: msg2 + $tl('mp_common_text.phoneLengthError|手机号长度有误'),
        icon: 'none',
      })
    }

    return false
  }
  const myreg = /^1[3465789]\d{9}$/
  if (!myreg.test(mobile)) {
    if (showTip) {
      uni.showToast({
        title: msg2 + $tl('mp_common_text.phoneError|手机号有误'),
        icon: 'none',
      })
    }

    return false
  }
  return true
}

/**
 * 字符串打星
 * @param startloc 开头显示几个文字
 * @param endloc 结束显示几个文字
 * @return {boolean}
 */
export function secretStr(target, startloc = 3, endloc = 4) {
  if (!target) return ''
  return target.toString().substring(0, startloc) + ' **** ' + target.toString().substring(target.length - endloc)
}

/**
 *
 * @param {diffTime} number
 * @returns
 */
export const getTimeList = function (diffTime) {
  let _date = new Date()
  _date.setMonth(_date.getMonth() - diffTime)
  _date.setDate(1)
  return [new Date(_date).getTime(), new Date().getTime()]
}

// 调用腾讯Captcha
export const useTCaptcha = (appId = '2015497501 ') => {
  return new Promise((resolve, reject) => {
    if (window && window.TencentCaptcha) {
      window.pfCaptcha = new window.TencentCaptcha(
        appId,
        (res) => {
          if (res.ret === 0) {
            resolve(res)
          } else {
            reject(res)
            window.pfCaptcha && window.pfCaptcha.destroy()
          }
        },
        null
      )
      window.pfCaptcha.show()
    } else {
      reject(new Error('未找到TCaptcha.js脚本'))
    }
  })
}
