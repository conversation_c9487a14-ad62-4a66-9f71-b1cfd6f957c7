import Cache from './cache'
import {
  $tl
} from '@/utils/i18n'
import storage from '@/utils/storage'
/* eslint-disable no-mixed-spaces-and-tabs */
let Fly = null
// #ifdef H5
Fly = require('flyio/dist/npm/fly')
// #endif
// #ifdef MP-WEIXIN || APP-PLUS
Fly = require('flyio/dist/npm/wx')
// #endif
const $fly = new Fly()
const $Cache = new Cache(Fly) // 将当前 fly 对象传入 Cache 中
$fly.config = {
  baseURL: '', // 请求的基地址
  headers: {
    'Content-Type': 'application/json;',
  }, // 自定义的请求头
  method: 'GET', // 请求方法
  params: {}, // url get参数(post请求或默认的get参数)
  parseJson: true,
  timeout: 15000, // 请求的超时时间
  withCredentials: false, // 跨域请求是否发送第三方cookie
  setting: {
    showLoading: true,
    loadingMask: true,
    loadingText: $tl('loading...'),
    loadingTime: 0,
    showToast: true, // 是否显示错误弹窗
    storage_expire: 60 * 60 * 1000 * 2, // 本地缓存过期时间 默认2小时
    storage: true,
    needToken: true, // 是否需要token信息
    cache: false, // 是否需要缓存
  },
}
class Fetch {
  constructor() {
    this.$fly = $fly
    this.onceConfig = null
    this.timer = null
    this.interceptor = {
      // 请求前的拦截
      request: null,
      // 请求后的拦截,
      responseSuc: null, // 响应成功
      responseErr: null, // 错误、网络错误时
    }
    // 初始化拦截器
    this.initInterceptor()
    this.get = (url, data = {}, setting = {}) => {
      return this.request({
        url,
        data,
        setting,
        method: 'GET',
      })
    }

    this.put = (url, data = {}, setting = {}) => {
      return this.request({
        url,
        data,
        setting,
        method: 'PUT',
      })
    }

    this.post = (url, data = {}, setting = {}) => {
      return this.request({
        url,
        data,
        setting,
        method: 'POST',
      })
    }

    this.delete = (url, data = {}, setting = {}) => {
      return this.request({
        url,
        data,
        setting,
        method: 'DELETE',
      })
    }

    this.postForm = (url, data = {}, setting = {}) => {
      const config = this.deepObjectMerge({
          headers: {
            'Content-Type': 'multipart/form-data; boundary=XXX',
          },
        },
        this.onceConfig,
      )
      this.setOnceConfig(config)
      return this.post(url, data, setting)
    }
  }
  initInterceptor() {
    this.$fly.interceptors.request.use((request) => {
      // 清除临时的config配置
      this.clearOnceConfig()

      // 显示loading
      const {
        setting
      } = request
      if (setting.showLoading && !this.timer) {
        this.timer = setTimeout(() => {
          uni.showLoading({
            title: setting.loadingText,
            mask: setting.loadingMask,
          })
          this.timer = null
        }, setting.loadingTime)
      }
      // 处理cache
      if (setting.cache) {
        $Cache.useRequest(request)
      }

      if (this.isFunction(this.interceptor.request)) {
        return this.interceptor.request(request)
      } else {
        return request
      }
    })

    this.$fly.interceptors.response.use(
      async response => {
          // 响应成功
          uni.hideLoading()
          let newResponse
          if (this.isFunction(this.interceptor.responseSuc)) {
            newResponse = await this.interceptor.responseSuc(response)
          }
          // 处理cache
          if (response.request.setting.cache) {
            $Cache.useResponse(response)
          }
          return newResponse || response

        },
        (err) => {
          // 响应失败
          uni.hideLoading()
          clearTimeout(this.timer)
          this.timer = null
          if (this.isFunction(this.interceptor.responseErr)) {
            return this.interceptor.responseErr(err)
          } else {
            return err
          }
        },
    )
  }
  deepObjectMerge(target, source) {
    // 深度合并对象
    for (var key in source) {
      target[key] =
        target[key] && target[key].toString() === '[object Object]' ?
        this.deepObjectMerge(target[key], source[key]) :
        (target[key] = source[key])
    }
    return target
  }
  isFunction(obj) {
    return obj && typeof obj === 'function'
  }
  setConfig(extendConfig) {
    $fly.config = this.deepObjectMerge($fly.config, extendConfig)
    return this
  }

  request(options) {
    const {
      url,
      data,
      setting,
      method
    } = options
    if (setting && setting.cache) {
      const data = $Cache.useRequest({
        url,
        cache: true
      })
      if (data) {
        return new Promise((resolve, reject) => {
          resolve(data.data)
        })
      }
    }
    const flyConfig = {
      method,
      setting,
    }
    const config = this.deepObjectMerge(flyConfig, this.onceConfig || {})
    return $fly.request(url, data, config)
  }

  setOnceConfig(config = {}) {
    this.onceConfig = config
    return this
  }
  clearOnceConfig() {
    this.onceConfig = null
  }
}
export default new Fetch()
