// import { code2Msg } from './errCodes'
import {
  $tl,
} from '@/utils/i18n'
import config from '@/config'
// 获取当前网关
export function getBaseURL(setting = {}, curEnv) {
  const {
    baseURL
  } = config
  if (curEnv === 'prod') {
    if (setting.flag === 'uat_nx_login') {
      return baseURL.prod_nx_login
    } else if (setting.flag === 'uat_nx') {
      return baseURL.prod_nx
    } else {
      return baseURL.prod
    }
  } else if (curEnv === 'uat') {
    if (setting.flag === 'uat_nx_login') {
      return baseURL.uat_nx_login
    } else if (setting.flag === 'uat_nx') {
      return baseURL.uat_nx
    } else {
      return baseURL.uat
    }
  } else {
    return baseURL[curEnv] || baseURL.sit // 默认返回 SIT 环境
  }
}

function showModel(Vue, message) {
  Vue.prototype.$message.confirm({
    content: message,
    showCancel: false,
  }).then(() => {})
}

const install = (Vue, vm) => {
  const $http = Vue.prototype.$http
  const $config = Vue.prototype.$config
  let baseURL = $config.baseURL
  const env = $config.env === 'prod' ? $config.env : Vue.prototype.$storage.getItemSync('__ENV_') || $config.env
  baseURL = baseURL[env]

  $http.setConfig({
    baseURL: baseURL,
    timeout: 30000,
    formatData: false, // 对特定数据处理
  })

  // 请求前拦截
  $http.interceptor.request = request => {
    const curEnv = $config.env === 'prod' ? $config.env : Vue.prototype.$storage.getItemSync('__ENV_') || $config
      .env
    const {
      setting = {},
    } = request
    const NWenv = getBaseURL(setting, curEnv)
    if (NWenv) {
      request.baseURL = NWenv
    }

    let headers = {
      'app-token': Vue.prototype.$storage.getItemSync('token'), // 拼接token
    }

    // 设置设备类型
    // #ifdef H5
    headers['device-type'] = 'h5'
    // #endif
    // #ifdef App-Plus
    headers['device-type'] = 'h5'
    const {
      platform
    } = getApp().globalData.systemInfo
    headers['device-type'] = platform
    // #endif


    // // 多语言 待定
    // const language = Vue.prototype.$store.state.locale.language
    // if (language === 'zh-Hans') {
    //   headers.selectedLanguage = 'zh_CN'
    // } else if (language === 'vi') {
    //   headers.selectedLanguage = 'vi_VN'
    // } else if (language === 'en') {
    //   headers.selectedLanguage = 'en_US'
    // } else {
    //   headers.selectedLanguage = language
    // }

    // // 时区待定
    // const timezone = Vue.prototype.$store.state.locale.timezone
    // if (timezone) {
    //   headers.Timezone = timezone
    // } else {
    //   headers.Timezone = 'GMT+8'
    // }

    // // #ifdef MP-WEIXIN
    // headers.Timezone = 'GMT+8'
    // headers.selectedLanguage = 'zh_CN'
    // // #endif

    request.headers = Object.assign(request.headers, headers)
    // #ifdef MP-WEIXIN
    console.log('请求体', request)
    // #endif

    // #ifdef WEB || APP-PLUS
    console.log('请求体url:', request.url)
    // #endif
    console.log('request:', request)
    return request
  }
  // 请求后拦截--请求成功 （主要处理非200时候提示报错）
  $http.interceptor.responseSuc = response => {
    const {
      data,
    } = response

    if (data.code !== 200) {
      Vue.prototype.$message.error(data.msg || $tl('系统错误'))
    }
    return data
  }

  // 请求后拦截--网络错误时、请求失败、服务器错误
  $http.interceptor.responseErr = err => {
    console.error('请求失败', err)
    const {
      status,
      response,
      message,
      request,
    } = err
    const {
      setting: {
        showToast,
        flag,
      },
    } = request
    if (showToast && status === 401) {
      if (response.data?.code !== '302') {
        showModel(Vue, response.data.msg)
      } else {
        // token 过期
        Vue.prototype.$message.error($tl('登录已过期'))
        setTimeout(function(){
          uni.redirectTo({
            url: '/pages/login/login?home=1',
            success: () => {
              Vue.prototype.$store.dispatch('user/logout')
            },
          })
        },1000)

      }
      return
    }
    if (status === 500) {
      Vue.prototype.$message.error(response.data.msg)
    }

    return (response && response.data) || err
  }
}
export default {
  install,
}
