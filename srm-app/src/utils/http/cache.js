
let CACHES = {}
const PREFIX = 'API_CACHE_'

class Cache {
  constructor(fly, options) {
    this.fly = fly
    if (!this.fly) throw new Error('缺少fly实例')
    // this.cancelToken = axios.CancelToken
    this._initCaches()
  }

  _initCaches() {
    const storageInfo = uni.getStorageInfoSync()
    storageInfo.keys.forEach(cacheKey => {
      if (cacheKey.indexOf(PREFIX) > -1) {
        // 当前缓存为需要判断是否过期
        const cache = getStorage(cacheKey)
        const nowTime = getNowTime()
        if (cache.storage_expire < nowTime - cache.createTime) {
          // 接口缓存时间小于就删除，避免占用多余内存
          uni.removeStorageSync(cacheKey)
        } else {
          CACHES[cacheKey] = cache
        }
      }
    })
  }

  useRequest(config) {
    let url = config.url
    if (url.indexOf('{organizationId}') > -1) {
      const { organizationId } = uni.getStorageSync('USERINFO')
      if (organizationId) {
        url = url.replace(/{organizationId}/g, organizationId)
      }
    }
    const key = this._regHttp(url)
    const data = CACHES[key]
    const nowTime = getNowTime()
    // 判断缓存数据是否存在 存在的话 是否过期 没过期就返回
    if (data && nowTime - data.createTime < data.storage_expire) {
      return data
    } else {
      return false
    }
  }

  useResponse(response) {
    const { storage, storage_expire, cache } = response.config.setting
    if (cache) {
      console.log('设置接口缓存数据')
      const data = {
        storage_expire: storage_expire,
        createTime: getNowTime(),
        data: response.data,
      }
      CACHES[this._regHttp(response.config.url)] = data
      if (storage) mapStorage(CACHES)
    }
  }
  _regHttp(url = '') {
    var reg = /^http(s)?:\/\/(.*?)\//
    return PREFIX + url.replace(reg, '')
  }
}

/**
 * caches: 缓存列表
 * type: set->存 get->取
 */
function mapStorage(caches, type = 'set') {
  Object.entries(caches).map(([key, cache]) => {
    if (key.indexOf(PREFIX) > -1) {
      if (type === 'set') {
        setStorage(key, cache)
      } else if (type === 'get') {
        // 正则太弱 只能简单判断是否是json字符串
        const reg = /\{/g
        if (reg.test(cache)) CACHES[key] = JSON.parse(cache)
        else CACHES[key] = cache
      }
    }
  })
}

// 设置缓存
function setStorage(key, cache) {
  uni.setStorageSync(key, JSON.stringify(cache))
}

// 获取缓存
function getStorage(key) {
  const data = uni.getStorageSync(key)
  if (data) {
    return JSON.parse(data)
  } else {
    return data
  }
}

// 设置过期时间
function getNowTime() {
  return new Date().getTime()
}
export default Cache
