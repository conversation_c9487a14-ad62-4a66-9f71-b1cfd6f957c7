import { $tl } from '@/utils/i18n'

export const code2Msg = {
	400: $tl('发出的请求有错误，服务器没有进行新建或修改数据的操作。'),
	401: $tl('用户没有权限（令牌、用户名、密码错误），即将跳转到登录页面。'),
	403: $tl('用户没有得到授权。'),
	404: $tl('发出的请求针对的是不存在的记录，服务器没有进行操作。'),
	406: $tl('请求的格式不可得。'),
	410: $tl('请求的资源被永久删除，且不会再得到的。'),
	422: $tl('当创建一个对象时，发生一个验证错误。'),
	500: $tl('服务器发生错误，请检查服务器。'),
	502: $tl('服务网关错误。'),
	503: $tl('服务不可用，服务器暂时过载或维护。'),
	504: $tl('网关超时。'),
}
