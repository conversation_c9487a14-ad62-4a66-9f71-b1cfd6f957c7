import i18n from '@/locale' // internationalization
import Vue from 'vue'
// import router from '@/router/index.js'
// translate router.meta.title, be used in breadcrumb sidebar tagsview
export function generateTitle(title) {
  const hasKey = this.$tle('route.' + title)

  if (hasKey) {
    // $tl :this method from vue-i18n, inject in @/lang/index.js
    const translatedTitle = this.$tl('route.' + title)

    return translatedTitle
  }
  return title
}

/**
 * 插件里面使用i18n
 * @param path 引用的路径
 * @returns String 对应译文
 */
export function $tl1(path) {
  const locale = i18n.locale
  const messages = i18n.messages
  const lang = messages[locale]
  const getText = (path) => {
    const array = path.split('.')
    let current = lang
    let value = ''
    for (let i = 0, j = array.length; i < j; i++) {
      current = current[array[i]]
      if (!current) {
        break
      }
      if (i === j - 1) {
        value = current
      }
    }
    return value || path
  }
  return getText(path)
}
/**
 * 多语言词条转换
 *
 * 格式1 messageCode
 * 格式2 messageCode|注释
 * 格式3 moduleCode.messageCode
 * 格式4 moduleCode.messageCode|注释
 * @param key 词条code 必填
 * @param values 词条内的参数 可选
 */
export function $tl(key, values) {
  const reg = /^((([a-zA-Z]+_[a-zA-Z]+_[a-zA-Z0-9]+)\.)?([a-zA-Z0-9_]+))(\|([\s\S]+))?$/
  const result = reg.exec(key)
  /*
  result[1]: moduleCode.messageCode
  result[3]: moduleCode
  result[4]: messageCode
  result[6]: 注释
   */
  if (!result) {
    return key
  }
  const wholeCode = result[1]
  let i18nRes = result[6] ? result[6] : wholeCode
  const res = i18n.t(wholeCode, values)
  if (res && res !== wholeCode) {
    i18nRes = res
  }
  // console.log('i18n.locale:', i18n.locale, 'i18n.messages:', i18n.messages, 'wholeCode:', wholeCode, 'i18nRes:', i18nRes)
  return i18nRes
}

/**
 * 将资源名后面加上语言编码, 比如test.pgn 变成 test_zh_CN.pgn
 * @param resourceName
 */
export function resourceLocaleName(resourceName) {
  if (Vue.prototype._.isEmpty(resourceName)) {
    return resourceName
  }
  const index = resourceName.lastIndexOf('.')
  let fileName = resourceName
  let suffix = ''
  if (index !== -1) {
    fileName = resourceName.substring(0, index)
    suffix = resourceName.substring(index)
  }

  return fileName + '_' + i18n.locale + suffix
}

