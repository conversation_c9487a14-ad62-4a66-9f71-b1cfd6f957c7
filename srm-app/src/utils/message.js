import { $tl } from '@/utils/i18n'

function success(message) {
  uni.showToast({
    title: message,
    duration: 2000,
    mask: true,
  })
}

function error(message) {
  uni.showToast({
    title: message,
    duration: 2000,
    icon: 'none',
    mask: true,
  })
}

function loading(isVisibleMask = true) {
  uni.showLoading({
    mask: isVisibleMask,
    title: $tl('mp_common_text.loading|loading...'),
  })
  return uni.hideLoading
}

function confirm(obj) {
  uni.hideLoading()
  const defaultParams = {
    title: $tl('温馨提示'),
    content: $tl('确定取消吗') + '?',
    showCancel: true,
    cancelText: $tl('取消'),
    cancelColor: '#2D3132',
    confirmText: $tl('确定'),
    confirmColor: '#E64C3D',
  }
  const params = obj ? { ...defaultParams, ...obj } : defaultParams
  return new Promise((resolve, reject) => {
    uni.showModal({
      ...params,
      success: function(res) {
        if (res.confirm) {
          resolve()
        } else if (res.cancel) {
          reject()
        }
      },
    })
  })
}

export default { success, error, loading, confirm }
