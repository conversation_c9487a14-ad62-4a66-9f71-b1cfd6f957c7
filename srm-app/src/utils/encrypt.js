import JsEncrypt from 'jsencrypt'
const pubKey = `-----BEGIN PUBLIC KEY-----MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDGZzbf3nIuDzSwrnkzATD4wDA9/lCdTnpeSn+J6qbgeKJYaRMgIk2d6wQIm3zp5nco1jVgTxerxX2pdQiOsjqG/6adOiA9MgHY97NWfNHZjdYIWJwQeoTgYyfNUwugoC/qjo/Gx3sAA6y6DIJgL7zpUdHy5MemHdi5alTqrq+OdQIDAQAB-----END PUBLIC KEY-----`
const privateKey = `-----BEGIN Private KEY-----MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMZnNt/eci4PNLCueTMBMPjAMD3+UJ1Oel5Kf4nqpuB4olhpEyAiTZ3rBAibfOnmdyjWNWBPF6vFfal1CI6yOob/pp06ID0yAdj3s1Z80dmN1ghYnBB6hOBjJ81TC6CgL+qOj8bHewADrLoMgmAvvOlR0fLkx6Yd2LlqVOqur451AgMBAAECgYEAs0qitDv5QtZK7z88F3OEJcbH0b34a693ta3tA10h4MgjLXIG7osRSRwQURbyEw1JsYWiRp4y7e9SV8iDKzC04aCtOhNrkEp8I+U1NCHyV/8xoLaW71W9joV7d/Hz0FaL6k1/lP3ZVaJUe1UtejEmUL6mDlGi+pxERg40lQP/nQECQQD6FLYIDuuTv/5h3wIny2/5Bo+YVg7/WdTxR0kGovTRHC2NaKtd6cPvEcWdSVFDar+rnCneq8ElV2UWs9IpT1DtAkEAyxlgvlYv8fUpQBCY+m43BJFdiQPljeybvIcjGocF4Y8v8bi/7Fu5NjVeAHUyVu5AUApdesc5ChOy7ayhu/VqqQJBAJfgJiDEtusXKa1Um2GBmnIZOvHgKeGIM9uIWGAwbQ/l1gBQNeqmAnlZSLNaCIEx/k+BWqhBlN5RsjHUaWjvI40CQQDBYj6tvV8VofxPFf6zpbIZVVicl+tBweNgFcSlA5/j7/UU0ndcVNH7/cCt6umtwFnYXKY5u+xh3fPnPxMY50tRAkBpljDg93Si0P7ARjys8BXT9mfJLLWY/QQe2Xb5jFNBUVV8bWOtR7LwQFAn50Q3w+vXXcMNckyOXYXIQFLjZVup-----END Private KEY-----`
export function encrypt(str){
  let encrypt = new JsEncrypt()
  encrypt.setPublicKey(pubKey)
  return encrypt.encrypt(str)
}
// 解密
export function decrypt(str){
  let encrypt = new JsEncrypt()
  encrypt.setPublicKey(privateKey)
  return encrypt.decrypt(str)
}
