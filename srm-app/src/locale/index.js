import Vue from 'vue'
import VueI18n from 'vue-i18n'
import en from './en.json'
import zhHans from './zh-Hans.json'
import vi from './vi.json'

Vue.use(VueI18n)

export const messages = {
  en,
	'zh-Hans': zhHans,
	vi,
}
export function getLanguage() {
  return uni.getLocale()
  // return 'zh_CN'
}
const i18n = new VueI18n({
  // set locale
  locale: getLanguage(),
  // set locale messages
  messages,
})

export default i18n
