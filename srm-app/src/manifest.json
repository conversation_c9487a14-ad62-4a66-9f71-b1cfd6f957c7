{
    "name" : "TCL-SRM",
    "appid" : "__UNI__98E9EF9",
    "description" : "srm app",
    "versionName" : "1.4.5",
    "versionCode" : 145,
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "modules" : {
            "Push" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "minSdkVersion" : 21,
                "targetSdkVersion" : 33
            },
            "ios" : {
                "dSYMs" : false,
                "idfa" : false,
                "privacyDescription" : {
                    "NSLocationWhenInUseUsageDescription" : "APP需要您的同意访问位置信息，用于等功能",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "APP需要您的同意访问位置信息，用于等功能",
                    "NSLocationAlwaysUsageDescription" : "APP需要您的同意访问位置信息，用于等功能",
                    "NSPhotoLibraryUsageDescription" : "APP需要您的同意访问您的相册，用于",
                    "NSPhotoLibraryAddUsageDescription" : "APP需要您的同意保存图片到相册，用于",
                    "NSCameraUsageDescription" : "APP需要您的同意访问您的摄像头，用于"
                }
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "ad" : {},
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "81c2625c9eaa5d3e8d9a1113240eddf6",
                        "appkey_android" : "c7d210672f1acd16e077d162ebe6421d"
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "push" : {
                    "unipush" : {
                        "icons" : {
                            "small" : {
                                "ldpi" : "C:/Users/<USER>/Desktop/icon-srm.png"
                            }
                        }
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "iosStyle" : "common"
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        /* SDK配置 */
        "usingComponents" : true,
        "splashscreen" : {
            "waiting" : false,
            "alwaysShowBeforeRender" : false
        }
    },
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "usingComponents" : true,
        "appid" : "wx3aaf87c66ab3c045",
        "setting" : {
            "urlCheck" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于打卡定位"
            }
        },
        "requiredBackgroundModes" : [ "location" ],
        // "lazyCodeLoading": "requiredComponents",
        "optimization" : {
            "subPackages" : true
        },
        "requiredPrivateInfos" : [ "getLocation", "onLocationChange", "startLocationUpdateBackground" ]
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "mp-qq" : {
        "usingComponents" : true
    },
    // ***********:7300
    "h5" : {
        "devServer" : {
            "port" : 7300,
            "disableHostCheck" : true,
            "proxy" : {
                "/testApi" : {
                    "target" : "http://***********:7300",
                    "changeOrigin" : true,
                    "secure" : false
                }
            }
        },
        "router" : {
            "base" : ""
        },
        "sdkConfigs" : {
            "maps" : {}
        },
        "title" : "SRM APP"
    },
    "fallbackLocale" : "zh-Hans",
    "locale" : "auto"
}
/////////////////////////modified////////////////////////////

