<script>
  export default {
    globalData: {},
    async onLaunch() {
      // 获取设备信息
      this.globalData.systemInfo = uni.getSystemInfoSync()

      // 如果token 存在 则全局请求用户信息
      // if (this.$store.state.user.token && this.$store.state.user.role) {
      // 获取用户信息
      this.$store.dispatch('user/getUserInfo', false)
      // }
      // #ifdef APP-PLUS
      // 监听接收消息
      plus.push.addEventListener('receive', (message) => {
        if (this.$store.state.setting.notify !== 1) { // notify 1
          plus.push.createMessage(
            message?.content?.replace(/<.*?>/g, '') || '',
            'LocalMSG', {
              cover: false,
              title: 'SRM',
              sound: this.$store.state.setting.voice !== 1 ? 'system' : 'none',
            },
            false,
          )
        }
      })
      // 点击消息调整消息页面
      plus.push.addEventListener(
        'click',
        (e) => {
          uni.switchTab({
            url: '/pages/tabBar/news/news',
          })
        },
        false,
      )
      // #endif
      // // #ifdef WEB || H5
      // const configScript = document.createElement('script')
      // configScript.innerHTML = `
      //     window.difyChatbotConfig = {
      //       token: 'udFXJDzDieuxzeUv',
      //       baseUrl: 'https://srm-ai.eads.tcl.com',
      //       draggable: true,

      //     }
      //   `
      // document.head.appendChild(configScript)
      // const chatbotScript = document.createElement('script')
      // chatbotScript.src = 'https://srm-ai.eads.tcl.com/embed.min.js'
      // chatbotScript.id = 'udFXJDzDieuxzeUv'
      // chatbotScript.defer = true
      // document.head.appendChild(chatbotScript)
      // // #endif
    },
  }
</script>

<style lang="scss">
  @import url('./static/css/global.css');
  @import './static/css/common.scss';

  page {
    background-color: $color-bg-def;
  }
</style>
