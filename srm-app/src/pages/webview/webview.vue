<template>
  <view>
    <web-view :src="domain + src" />
  </view>
</template>

<script>
  import config from '@/config'
  export default {
    data() {
      return {
        src: '',
        domain: config.webviewHost,
      }
    },
    onLoad(options) {
      /* if (Object.prototype.hasOwnProperty.call(options, 'title')) {
        uni.setNavigationBarTitle({
          title: options.title,
        })
      } */
      this.src = decodeURIComponent(options.src)
    },
    methods: {},
  }
</script>

<style></style>
