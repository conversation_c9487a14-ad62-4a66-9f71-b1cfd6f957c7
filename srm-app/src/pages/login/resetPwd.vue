<template>
  <view class="reset-password-wrapper">
    <!-- title -->
    <text class="fs-48 color-text-black text-bold title">{{ $t('密码重置') }}</text>
    <!-- form表单主体 -->
    <view class="form-container">
      <view class="form-item password">
        <text class="label">{{ $t('请输入新密码') }}</text>
        <input v-model="modifiedPassword" :password="!pwdShow" :placeholder="$tl('新密码')"
          placeholder-class="text-placeholder" @input="inputModified">
        <view class="hp100 eyes">
          <image :src="pwdShow ? '/static/image/icon-eye-show.png' : '/static/image/icon-eye-hide.png'"
            style="width: 48rpx; height: 48rpx;" @click="pwdShow = !pwdShow" />
        </view>
      </view>
      <view class="form-item newPassword">
        <text class="label">{{ $t('请确认新密码') }}</text>
        <input v-model="confirmPassword" :password="!newPwdShow" :placeholder="$tl('确认新密码')"
          placeholder-class="text-placeholder" @input="inputConfirm" />
        <view class="hp100 eyes">
          <image :src="newPwdShow ? '/static/image/icon-eye-show.png' : '/static/image/icon-eye-hide.png'"
            style="width: 48rpx; height: 48rpx;" @click="newPwdShow = !newPwdShow" />
        </view>
      </view>
    </view>
    <!-- 密码校验 -->
    <view class="valid-tips">
      <view v-for="(item, index) in validTips" :key="index" class="valid-tips-item" :class="item.status">
        <view class="hp100 flex-row-center valid-tips-status">
          <image class="status-icon" :src="validImgMap[item.status]" />
        </view>
        <text class="valid-tips-text">{{ item.text }}</text>
      </view>
    </view>
    <view class="btn" @click="confirm">{{ $tl('确认') }}</view>
  </view>
</template>

<script>
  import {
    isNotEmpty,
  } from '@/utils/tools'
  import {
    mapGetters,
    mapActions
  } from 'vuex'
  export default {
    data() {
      return {
        modifiedPassword: '',
        confirmPassword: '',
        pwdShow: false,
        newPwdShow: false,
        validTips: {
          length: {
            code: 'length',
            status: 'default',
            text: this.$tl('密码长度为6-20位')
          },
          number: {
            code: 'number',
            status: 'default',
            text: this.$tl('至少含有一个数字')
          },
          contains: {
            code: 'contains',
            status: 'default',
            text: this.$tl(' 至少包含一个字母或特殊字符，\n如：_!@#$%^&*`~()-+= ')
          },
          same: {
            code: 'same',
            status: 'default',
            text: this.$tl('两次密码一致')
          }, // status: default 初始状态， fail 验证不通过  pass 验证通过
        },
        validImgMap: {
          default: '/static/image/icon-valid-default.png',
          fail: '/static/image/icon-valid-fail.png',
          pass: '/static/image/icon-valid-pass.png',
        },
      }
    },
    computed: {},
    onLoad(options) {
      this.validateCode = options.validateCode
      this.userName = options.userName
      this.mail = options.mail
    },
    methods: {
      ...mapActions('user', {
        resetPwd: 'resetPwd',
      }),
      confirm() {
        if (!this.validData()) return
        const params = {
          userName: this.userName,
          mail: this.mail,
          validateCode: this.validateCode,
          modifiedPassword: this.modifiedPassword,
          confirmPassword: this.confirmPassword
        }
        this.resetPwd(params).then(res => {
          if (res.code === 200) {
            uni.navigateTo({
              url: '/pages/login/resetPwdSuccess',
            })
          }
        })
      },
      // 校验密码
      inputModified(e) {
        const {
          value
        } = e.detail
        // 密码长度校验
        if (value?.length > 6 && value?.length < 20) {
          this.$set(this.validTips.length, )
          this.validTips.length.status = 'pass'
        } else {
          this.validTips.length.status = 'fail'
        }

        // 密码数字校验
        if (/\d/.test(value)) {
          this.validTips.number.status = 'pass'
        } else {
          this.validTips.number.status = 'fail'
        }

        // 字符判断
        if (/[a-zA-Z_!@#$%^&*`~()\-=+]/.test(value)) {
          this.validTips.contains.status = 'pass'
        } else {
          this.validTips.contains.status = 'fail'
        }

        // 两次密码一致校验
        if (this.modifiedPassword && this.confirmPassword && this.modifiedPassword === this.confirmPassword) {
          this.validTips.same.status = 'pass'
        } else {
          this.validTips.same.status = 'fail'
        }
      },
      inputConfirm(e) {
        // 两次密码一致校验
        if (this.modifiedPassword && this.confirmPassword && this.modifiedPassword === this.confirmPassword) {
          this.validTips.same.status = 'pass'
        } else {
          this.validTips.same.status = 'fail'
        }
      },
      // 数据校验
      validData() {
        // 新密码
        if (!this.modifiedPassword) {
          this.$message.error(this.$tl('请输入新密码'))
          return false
        }
        // 确认新密码
        if (!this.confirmPassword) {
          this.$message.error(this.$tl('请确认新密码'))
          return false
        }
        // 查询数据中是否包含
        const hasNoPassStatus = Object.values(this.validTips).some(tip => tip.status !== 'pass');
        if (hasNoPassStatus) {
          this.$message.error(this.$tl('请按要求输入密码'))
          return false
        }
        return true
      }
    },
  }
</script>

<style>
  page {
    background: #ffffff;
  }
</style>

<style lang="scss" scoped>
  .reset-password-wrapper {
    padding: 64rpx 80rpx 0;

    .title {
      margin-top: 64rpx;
      color: #1A1A1A;
    }
  }

  .form-container {
    margin-top: 52rpx;
  }

  .form-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 590rpx;
    height: 96rpx;
    margin-bottom: 50rpx;

    .label {
      align-self: flex-start;
      font-size: 28rpx;
      font-weight: 500;
      color: #B4B3B2;
      letter-spacing: 0;
      margin-bottom: 8rpx;
    }

    input {
      margin: 0 36rpx;
      width: 510rpx;
      color: $color-text-black;
      font-size: 28rpx;
      border-bottom: 1px solid $color-border-dark;
      padding: 16rpx 80rpx 16rpx 0;
    }

    .text-placeholder {
      color: $color-text-placeholder;
      font-size: 28rpx;
    }

    .eyes {
      position: absolute;
      top: 62rpx;
      right: 0;
    }
  }

  .valid-tips {
    align-self: flex-start;

    .valid-tips-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .valid-tips-status {
        width: 24rpx;
        height: 24rpx;
        margin-right: 12rpx;

        .status-icon {
          width: 24rpx;
          height: 24rpx;
        }
      }

      .valid-tips-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
      }
    }
  }

  .btn {
    position: fixed;
    width: 686rpx;
    height: 80rpx;
    border-radius: 80rpx;
    text-align: center;
    line-height: 80rpx;
    color: #ffffff;
    background: $color-bg-black;
    justify-content: center;
    left: 32rpx;
    bottom: 32rpx;
    font-size: 32rpx;
  }
</style>
