<template>
  <view class="mask">
    <view class="mask-content">
      <view class="content">
        <view class="t-center text-bold fs-32">{{ title }}</view>
        <view class="contentpd">
          <view class="m-t-32">{{ $tl('亲爱的用户，欢迎使用TCL SRM系统') }}</view>
          <view class="m-t-32">
            {{ $tl('在使用我们的产品和服务前，请您先阅读并了解') }}
            <text class="color-text-blue" @click.stop="serviceAgreement">《{{ $tl('服务协议') }}》</text>
            {{ $tl('mp_mainpackage_home.and|和') }}
            <text class="color-text-blue" @click.stop="privacyPolicy">《{{ $tl('隐私政策') }}》</text>。
          </view>
          <view class="m-t-32">
            {{ $tl('我们将严格按照上述协议为您提供服务，保护您的信息安全，点击“同意”即表示您已阅读并同意全部条款，可以继续使用我们的产品和服务。') }}
          </view>
        </view>
      </view>
      <view class="footer">
        <view v-if="cancelText !== ''" class="cancel" @click="cancel">{{ cancelText }}</view>
        <view class="sure" @click="confirm">{{ confirmText }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import {
    $tl,
  } from '@/utils/i18n'
  export default {
    props: {
      title: {
        type: String,
        default: $tl('服务协议和隐私政策提示'),
      },
      confirmText: {
        type: String,
        default: $tl('同意'),
      },
      cancelText: {
        type: String,
        default: $tl('不同意'),
      },
    },
    data() {
      return {}
    },
    methods: {
      confirm() {
        this.$emit('confirm')
      },
      cancel() {
        this.$emit('cancel')
        uni.reLaunch({
          url: '/pages/index/index',
        })
      },
      // 服务协议
      serviceAgreement() {
        // #ifdef APP-PLUS
        plus.runtime.openURL(
          'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/staticfile/srm/tenant/10000/document/sourceFile/Chinese.pdf'
        )
        // #endif
        // #ifdef WEB || H5
        uni.navigateTo({
          url: '/pages/mine/serviceAgreement/serviceAgreement',
        })
        // #endif
      },
      // 隐私政策
      privacyPolicy() {
        // #ifdef APP-PLUS
        uni.navigateTo({
          url: '/pages/mine/privacyPolicy/privacyPolicy',
        })
        // #endif
        // #ifdef WEB || H5
        window.open('https://www.tcl.com/cn/zh/legal/privacy-policy')
        // #endif
      },
    },
  }
</script>

<style>
  .text-blue {
    color: #2783fe;
  }
</style>

<style lang="scss" scoped>
  /* component/modal/modal.wxss */
  .mask {
    position: fixed;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.64);
    z-index: 9999;
  }

  .mask-content {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 40rpx 48rpx;
    border-radius: 32rpx;
    color: #000000;
    font-size: 28rpx;
    background: rgba(255, 255, 255, 1);
    line-height: 44rpx;
  }

  .mask-content .content {
    display: flex;
    flex-direction: column;
    width: 554rpx;
  }

  .mask-content .content .bold {
    font-weight: 600;
    font-size: 32rpx;
    margin-bottom: 10rpx;
  }

  .contentpd {
    padding: 32rpx 0 64rpx;
  }

  .footer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    color: #fff;
    font-size: 36rpx;
    padding-bottom: 8rpx;
  }

  .sure {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 248rpx;
    height: 80rpx;
    color: #fff;
    font-size: 32rpx;
    background-color: $color-main;
    border-radius: 40rpx;
  }

  .cancel {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 248rpx;
    height: 80rpx;
    margin-right: 48rpx;
    color: $color-text-black;
    font-size: 32rpx;
    font-weight: 550;
    background-color: $color-bg-gray;
    border-radius: 40rpx;
  }
</style>
