<template>
  <view class="verify-code-container">
    <text class="fs-48 color-text-black text-bold">{{$t('验证码')}}</text>
    <text class="m-t-32 fs-28 color-text-gray">{{$t('验证码已发送至邮箱')}}</text>
    <view class="flex-acen-sb m-t-16 fs-28">
      <text class="color-text-black">{{ mail }}</text>
      <view v-if="second > 0" class="color-text-gray"> {{ second }}S 后重获</view>
      <view v-else class="color-text-blue" @click="reGet">重新获取</view>
    </view>
    <view class="msg">
      <text class="error-msg" v-if="errorMsg">{{ errorMsg }}</text>
      <text class="label" v-else>{{$t('请输入验证码')}}</text>
    </view>
    <view class="input-container">
      <input v-model="yzm" adjust-position="false" class="yzm" type="number" :focus="yzmFocus" maxlength="4"
        hold-keyboard="true" autocomplete="one-time-code" @input="inputevent" @blur="lostBlur">
      <view class="input-mask" @click="yzmInputFocus">
        <block v-for="(item, index) in yzmArr" :key="index">
          <view v-if="currentIndex===index" class="item active">
            <view class="blur" />
          </view>
          <view v-else class="item">
            {{ item ? item : '' }}
          </view>
        </block>
      </view>
    </view>
  </view>
</template>

<script>
  import {
    isNotEmpty,
    validatemobile
  } from '@/utils/tools'
  import {
    mapActions,
    mapGetters
  } from 'vuex'
  export default {
    data() {
      return {
        yzm: '',
        yzmArr: ['', '', '', ''],
        yzmFocus: true,
        second: 0,
        mobile: '',
        currentIndex: 0,
        jscode: '',
        interval: null,
        errorMsg: '',
        userName: '',
        mail: ''
      }
    },
    computed: {
      ...mapGetters('user', {
        hasDriverInfo: 'hasDriverInfo'
      }),
    },
    onLoad(e) {
      this.userName = e.userName
      this.mail = e.mail
    },
    methods: {
      ...mapActions('user', {
        validVertCode: 'validVertCode',
        getMailVertCode: 'getMailVertCode',
      }),
      inputevent(e) {
        const inputVal = e.detail.value
        const yzmArr = inputVal.split('')
        yzmArr.length = 4
        this.errorMsg = ''
        if (inputVal.length === 4) {
          // 已经输入4位数
          this.sumbit()
        }
        this.currentIndex = inputVal.length
        this.yzmArr = yzmArr
      },
      yzmInputFocus() {
        if (this.yzmFocus) {
          this.yzmFocus = false
        }
        this.yzmFocus = true
      },
      lostBlur(e) {
        this.yzmFocus = false
      },
      async sumbit() {
        const res = await this.validVertCode({
          validateCode: this.yzm,
          mail: this.mail,
          userName: this.userName
        });
        if (res.code === 200) {
          uni.navigateTo({
            url: `/pages/login/resetPwd?validateCode=${this.yzm}&userName=${this.userName}&mail=${this.mail}`,
          })
        }
        //    this.$sc.throttle(async() => {
        //      const res = await this.login({ username: this.phone, code: this.yzm, grant_type: 'sms' })
        //      if (res.code === 0) {
        //        const hasDriverInfo = await this.$store.dispatch('user/getDriverInfo')
        //        if (!hasDriverInfo) {
        //          uni.redirectTo({
        //            url: '/minePages/registerInfo/registerInfo',
        //          })
        //        } else {
        //          uni.switchTab({
        //            url: '/pages/index/index',
        //          })
        //        }
        //      }
        //    })

      },
      // 设置验证码倒计时
      setUpTimer() {
        this.second = 60
        this.interval = setInterval(() => {
          if (this.second <= 0) {
            clearInterval(this.interval)
          } else {
            this.second--
          }
        }, 1000)
      },
      // 重新获取
      reGet() {
        this.yzm = ''
        this.yzmArr = ['', '', '', '']
        this.currentIndex = 0
        this.getVertCode()
      },
      async getVertCode() {
        const params = {
          userName: this.userName,
          mail: this.mail
        }
        this.getMailVertCode(params).then(res => {
          if (res.code === 200) {
            this.$message.success('已重新获取验证码')
            this.setUpTimer()
          }
        })
      },
   },
  }
</script>

<style>
  page {
    background-color: #FFFFFF;
  }
</style>

<style scoped lang="scss">
  .verify-code-container {
    display: flex;
    flex-direction: column;
    padding: 64rpx 80rpx;
  }

  .msg {
    margin-top: 108rpx;

    .error-msg {
      height: 36rpx;
      margin-top: 108rpx;
      font-size: 24rpx;
      color: $color-text-red;
    }

    .label {
      font-size: 26rpx;
      color: #666666;
    }
  }

  .input-container {
    margin-top: 42rpx;

    .yzm {
      position: absolute;
      height: 80rpx;
      top: -24000rpx;
      left: -5000rpx;
      width: 10000rpx;
      opacity: 0;
      z-index: 1;
      color: transparent;
    }

    .input-mask {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 80rpx;
    }

    .input-mask .item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 84rpx;
      height: 84rpx;
      border: 6rpx solid #E6E6E6;
      border-radius: 10rpx;
      font-size: 48rpx;
      font-weight: 550;
      color: $color-text-black;
      background: #F6F6F4;
    }

    .input-mask .item.active {
      border: 6rpx solid #4A556B;
    }

    .blur {
      width: 6rpx;
      height: 32rpx;
      background: #E6E6E6;
      border-radius: 3rpx;
    }
  }
</style>
