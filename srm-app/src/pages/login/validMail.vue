<template>
  <view class="flex-col-center valid-mail-wrapper">
    <!-- title -->
    <text class="fs-48 color-text-black text-bold title">{{$t('邮箱验证')}}</text>
    <!-- form表单主体 -->
    <view class="form-container">
      <view class="form-item username">
        <text class="label">{{$t('请输入账号')}}</text>
        <input v-model="userName" placeholder-class="text-placeholder">
      </view>
      <view class="form-item mail">
        <text class="label">{{$t('请输入邮箱')}}</text>
        <input v-model="mail" placeholder-class="text-placeholder">
      </view>
    </view>
    <view class="btn" @click="getVertCode">{{ $tl('获取验证码') }}</view>
  </view>
</template>

<script>
  import NotifyModal from './notifyModal.vue'
  import {
    isNotEmpty,
  } from '@/utils/tools'
  import {
    mapGetters,
    mapActions,
  } from 'vuex'
  export default {
    components: {
      NotifyModal,
    },
    data() {
      return {
        userName: '',
        mail: ''
      }
    },

    onLoad(options) {

    },
    methods: {
      ...mapActions('user', {
        getMailVertCode: 'getMailVertCode',
      }),
      /**
       * 验证码
       * */
      async getVertCode() {
        if(!this.validData()) return
        const params = {
          userName: this.userName,
          mail: this.mail
        }
        this.getMailVertCode(params).then(res => {
          if (res.code === 200) {
            uni.navigateTo({
              url: `/pages/login/verifyCode?userName=${this.userName}&mail=${this.mail}`,
            })
          }
        })
      },
      // 数据校验
      validData() {
        // 账号
        if (!this.userName) {
          this.$message.error(this.$tl('请输入账号'))
          return false
        }
        // 邮箱
        if (!this.mail) {
          this.$message.error(this.$tl('请输入邮箱'))
          return false
        }
        return true
      }
    },
  }
</script>

<style>
  page {
    background: #ffffff;
  }
</style>

<style lang="scss" scoped>
  .valid-mail-wrapper {
    .title {
      margin-top: 64rpx;
      align-self: flex-start;
      padding-left: 80rpx;
      color: #1A1A1A;
    }
  }

  .form-container {
    margin-top: 52rpx;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 590rpx;
    height: 96rpx;
    margin-bottom: 50rpx;

    .label {
      align-self: flex-start;
      font-size: 28rpx;
      font-weight: 500;
      color: #B4B3B2;
      letter-spacing: 0;
      margin-bottom: 8rpx;
    }

    input {
      margin: 0 36rpx;
      width: 100%;
      color: $color-text-black;
      font-size: 28rpx;
      border-bottom: 1px solid $color-border-dark;
      padding: 16rpx 0;
    }

    .text-placeholder {
      color: $color-text-placeholder;
      font-size: 28rpx;
    }
  }

  .btn {
    position: fixed;
    width: 686rpx;
    height: 80rpx;
    border-radius: 80rpx;
    text-align: center;
    line-height: 80rpx;
    color: #ffffff;
    background: $color-bg-black;
    justify-content: center;
    bottom: 32rpx;
    font-size: 32rpx;
  }
</style>
