<template>
  <view class="flex-col-center reset-success-wrapper">
    <!-- 成功图标显示 -->
    <view class="flex-row-center success-icon">
      <image src="/static/image/icon-success.png" style="width:300rpx;height:300rpx;"/>
    </view>
    <text class="text-success">{{ $tl('密码重置成功') }}</text>
    <text class="text-tips">{{ $tl('亲爱的用户，您已经成功重置了密码。') }}</text>
    <text class="text-tips">请点击<text style="color:#4E5A70;font-weight:500;">完成</text>按钮回到登录界面用新密码重新登录。</text>
     <view class="btn"  @click="confirm">{{ $tl('完成') }}</view>
  </view>
</template>

<script>
 export default {
    methods: {
      confirm(){
        uni.navigateTo({
          url: '/pages/login/login',
        })
      }
    },
  }
</script>

<style>
  page {
    background: #ffffff;
  }
</style>

<style lang="scss" scoped>

 .success-icon {
   width: 300rpx;
   height: 300rpx;
   margin-top: 100rpx;
   margin-bottom: 24rpx;
 }
 .text-success {
   margin-bottom: 40rpx;
   font-family: PingFangSC-SNaNpxibold;
   font-size: 40rpx;
   color: #2CDC9B;
 }
 .text-tips {
   font-size: 28rpx;
   line-height: 48rpx;
   color: #999999;
 }
  .btn {
    position: fixed;
    width: 686rpx;
    height: 80rpx;
    border-radius: 80rpx;
    text-align: center;
    line-height: 80rpx;
    color: #ffffff;
    background: $color-bg-black;
    justify-content: center;
    left: 32rpx;
    bottom: 32rpx;
    font-size: 32rpx;
  }
</style>
