<template>
  <view class="flex-col-center login-wrapper">
    <!-- title -->
    <view class="title-wrapper">
      <image class="logo" src="/static/image/logo.png" />
      <text class="line" />
      <text class="title">{{ $tl('TCL数智采购平台(SRM)') }}</text>
    </view>
    <!-- 登录主体 -->
    <view class="form-container">
      <view class="form-item account">
        <input v-model="account" :placeholder="$tl('请输入账号')" placeholder-class="text-placeholder">
      </view>
      <view class="form-item password">
        <input v-model="pwd" type="text" :password="!pwdShow" :placeholder="$tl('请输入密码')"
          placeholder-class="text-placeholder">
        <view class="hp100 flex-row-center m-r-24">
          <image :src="pwdShow ? '/static/image/icon-eye-show.png' : '/static/image/icon-eye-hide.png'"
            style="width: 48rpx; height: 48rpx;" @click="pwdShow = !pwdShow" />
        </view>
      </view>
      <view class="form-item btn" @click="handleLogin">{{ $tl('登录') }}</view>
      <view class="pwd-wrapper">
        <view @click="forgetPwd">
          <text class="forget-pwd">{{ $tl('忘记密码?') }}</text>
        </view>
        <view class="flex remember-pwd"  @click="rememberChecked = !rememberChecked">
          <image class="remember-checkbox" :src="!rememberChecked ? '/static/image/home/<USER>' : '/static/image/home/<USER>'"
            style="width: 28rpx; height: 28rpx;margin-right:10rpx;"/>
          <text class="remember-text">{{ $tl('记住密码') }}</text>
        </view>
      </view>
    </view>
    <view class="agreement" @click="checked = !checked">
      <!-- <checkbox class="aggreement-checkbox" :checked="checked" /> -->
      <image class="agreement-icon"
        :src="checked ? '/static/image/icon-radio-active.png' : '/static/image/icon-radio.png'" />
      {{ $tl('已阅读并同意') }}
      <text class="protocol" @click.stop="serviceAgreement">《{{ $tl('服务协议') }}》</text>
      <text class="protocol" @click.stop="privacyPolicy">《{{ $tl('隐私政策') }}》</text>
    </view>
    <srm-notify-modal v-if="showModal" @confirm="confirm" />
  </view>
</template>

<script>
  import {
    checkAppVersion,
  } from '@/utils/appUpdate'
  import {
    decrypt
  } from '@/utils/encrypt.js'
  import http from '@/utils/http'
  import {
    mapGetters,
    mapActions,
  } from 'vuex'
  import storage from '@/utils/storage'
  export default {
    data() {
      return {
        account: '',
        pwd: '',
        checked: false,
        rememberChecked: false,
        showModal: false,
        pwdShow: false,
      }
    },
    computed: {
      ...mapGetters('user', {
        username: 'username',
        password: 'password',
      }),
    },
    mounted() {
      this.account = this.username
      this.pwd = this.password ? decrypt(this.password) : ''
      if (this.account && this.pwd) {
        this.rememberChecked = true
        this.checked = true
      }
    },
    methods: {
      ...mapActions('user', {
        login: 'login',
      }),
      // 登录
      async handleLogin() {
        if (!this.validData()) return
        // 登录请求
        let res = await this.login({
          username: this.account,
          password: this.pwd,
          // username: 'lv2.li',
          // password: 'idM@UAT#2022',
          // username: this.account || 'ex_jiayu.zhang',
          // password: this.password || '1q2w3e4r..',
          // username: this.account || '100358',
          // password: this.password || 'cs123456@',
          rememberChecked: this.rememberChecked,
        })
        if (res.code === 200) {
          // 绑定设备cid
          //#ifdef APP-PLUS
          plus.push.getClientInfoAsync((info) => {
            let cid = info["clientid"];
            http.post(`/api/purchase/app-api/public-api/tenant/savePushFlag/${cid}`, {}, {
              showLoading: false
            }).catch(err => {
              console.log(err)
            });
          });
          //#endif
          // 获取主页数据
          const homeRes = await http.get('/api/purchase/app-api/public-api/tenant/getLatestValidAppInfo', {}, {
            showLoading: false
          })

          if (homeRes.code === 200) {
            // 存储data信息
            storage.setItemSync('homeInfo', homeRes.data)
          }

          uni.reLaunch({
            url: '/pages/tabBar/home/<USER>',
          })
        }
      },
      // 数据校验
      validData() {
        // 用户协议
        if (!this.checked) {
          this.$message.error(this.$tl('请阅读并勾选服务协议'))
          return false
        }
        // 账号
        if (!this.account) {
          this.$message.error(this.$tl('请输入账号'))
          return false
        }
        // 密码
        if (!this.pwd) {
          this.$message.error(this.$tl('请输入密码'))
          return false
        }
        return true
      },
      // 忘记密码
      forgetPwd() {
        uni.navigateTo({
          url: '/pages/login/resetChoice',
        })
      },
      // 服务协议
      serviceAgreement() {
        // #ifdef APP-PLUS
        plus.runtime.openURL(
          'https://srm-preview.tcl.com/onlinePreview?url=aHR0cHM6Ly9jbG91ZG9zczEudGNsLmNvbS9QQUFTUFJEOmE0OTg3NjYxLXNybS1wdWJsaWMvc3RhdGljZmlsZS9zcm0vdGVuYW50LzEwMDAwL2RvY3VtZW50L3NvdXJjZUZpbGUvQ2hpbmVzZS5wZGY='
        )
        // #endif
        // #ifdef WEB || H5
        uni.navigateTo({
          url: '/pages/mine/serviceAgreement/serviceAgreement',
        })
        // #endif
      },
      // 隐私政策
      privacyPolicy() {
        // #ifdef APP-PLUS
        uni.navigateTo({
          url: '/pages/mine/privacyPolicy/privacyPolicy',
        })
        // #endif
        // #ifdef WEB || H5
        window.open('https://www.tcl.com/cn/zh/legal/privacy-policy')
        // #endif
      },
    },
  }
</script>

<style>
  page {
    background: #ffffff;
  }
</style>

<style lang="scss" scoped>
  .login-wrapper {
    background: url(/static/image/bg_login.png) no-repeat top center;
    background-size: 100% auto;
  }

  .title-wrapper {
    display: flex;
    align-items: center;
    margin-top: 315rpx;

    .logo {
      width: 46rpx;
      height: 42rpx;
    }

    .line {
      width: 1px;
      height: 40rpx;
      background: #CCCCCC;
      margin: 0 16rpx;
    }

    .title {
      font-size: 42rpx;
      color: #333333;
    }
  }

  .form-container {
    margin-top: 135rpx;
  }

  .form-item {
    display: flex;
    align-items: center;
    width: 590rpx;
    height: 96rpx;
    font-size: 32rpx;
    background: $color-bg-gray;
    border-radius: 48rpx;
    margin-top: 40rpx;

    input {
      margin: 0 36rpx;
      width: 100%;
      color: $color-text-black;
      font-size: 32rpx;
    }

    .text-placeholder {
      color: $color-text-placeholder;
      font-size: 32rpx;
    }
  }

  .btn {
    width: 100%;
    color: #ffffff;
    background: $color-bg-black;
    justify-content: center;
    margin-top: 80rpx;
  }

  .pwd-wrapper {
    margin-top: 40rpx;
    display: flex;
    align-items: center;

    .forget-pwd {
      font-size: 30rpx;
      color: #3979F9;
    }

    .remember-pwd {
      margin-left: auto;
      font-size: 30rpx;
      align-items: center;
      .remember-text {
        color: #999990;
      }
    }
  }

  .agreement {
    position: fixed;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    left: 50%;
    bottom: 40rpx;
    width: 590rpx;
    transform: translateX(-50%);
    color: $color-text-gray;

    .agreement-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }

    .protocol {
      color: $color-main;
    }
  }
</style>
