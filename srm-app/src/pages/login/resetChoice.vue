<template>
  <view class="reset-choice-wrapper">
    <!-- title -->
    <text class="fs-48 color-text-black text-bold title">{{ $t('请选择角色类型') }}</text>
    <text class="text-bold sub-title">{{ $t('根据您的角色类型，选择修改密码方式') }}</text>
    <view class="choice-container">
      <view class="puchase choice-item">
        <view class="item-icon flex-row-center">
          <image src="/static/image/icon-purchaser.png"></image>
        </view>
        <view class="cnt">
          <text class="cnt-title">采购用户</text>
          <text class="sub-des">请登录系统<text style="color:#3979F9;margin-left:8rpx;">https://sis.tcl.com</text>进行密码修改</text>
        </view>
        <view class="copy-btn flex-row-center" @click="copyUrl">
          <image src="/static/image/icon-copy.png"></image>
        </view>
      </view>

      <view class="choice-item">
        <view class="item-icon supplier">
             <image src="/static/image/icon-supplier.png"></image>
        </view>
        <view class="cnt" style="width: 260rpx;">
          <text class="cnt-title">供应商用户</text>
          <text class="sub-des">请点击“修改密码” 前往修改</text>
        </view>
        <view class="choice-btn" @click="updatePwd">修改密码</view>
      </view>
    </view>
  </view>
</template>

<script>
  import {
    isNotEmpty,
  } from '@/utils/tools'
  import {
    mapGetters,
  } from 'vuex'
  export default {
    data() {
      return {
        isAndroid: false,
        showHomeBtn: false,
        areaCode: '+86',
        visible: false,
        checked: false,
        showModal: false,
        phone: '',
        password: '',
        rememberChecked: false,
        pwdShow: false,
        languageDesc: '简体中文',
        showSheet: false,
        sheetList: [{
            id: 1,
            title: '简体中文',
            value: 'zh-Hans'
          },
          // { id: 2, title: 'English', value: 'en_US' },
          {
            id: 3,
            title: 'Việt Nam',
            value: 'vi_VN'
          },
        ],
        items: [{
            lable: this.$tl('mp_mainpackage_home.chineseMainland|中国大陆'),
            value: '+86'
          },
          {
            lable: this.$tl('mp_mainpackage_home.vietnam|越南'),
            value: '+84'
          },
          {
            lable: 'OA、' + this.$tl('mp_mainpackage_home.selfAccount|自建账号'),
            value: this.$tl('mp_common_text.other|其他')
          },
        ],
        validTips: [ // status: default 初始状态， fail 验证不通过  pass 验证通过
          {
            code: 'length',
            'status': 'default',
            text: this.$tl('密码最低为6位')
          },
          {
            code: 'contains',
            'status': 'default',
            text: this.$tl('至少含有一个字母和一个数字')
          },
          {
            code: 'specialChars',
            'status': 'default',
            text: this.$tl('至少含有特殊字符，如：~!@#S%^&*-+.?')
          },
          {
            code: 'same',
            'status': 'default',
            text: this.$tl('两次密码一致')
          },
        ],
        validImgMap: {
          default: '/static/image/icon-valid-default.png',
          fail: '/static/image/icon-valid-fail.png',
          pass: '/static/image/icon-valid-pass.png',
        },
      }
    },
    computed: {
      ...mapGetters('locale', {
        language: 'language'
      }),
    },
    onLoad(options) {
      if (Object.prototype.hasOwnProperty.call(options, 'home')) {
        this.showHomeBtn = options.home
      }
      const showModal = uni.getStorageSync('had-show-notify')
      // this.showModal = !showModal
      this.showModal = false
      if (this.language.startsWith('zh')) {
        this.languageDesc = '简体中文'
      } else if (this.language.startsWith('vi')) {
        this.languageDesc = 'Việt Nam'
      } else if (this.language.startsWith('en')) {
        this.languageDesc = 'English'
      }
      this.isAndroid = getApp().globalData.systemInfo.platform.toLowerCase() === 'android'
      // this.getLoginInfo()
    },
    methods: {
      // 复制链接
      copyUrl() {
        uni.setClipboardData({
          data: 'https://sis.tcl.com',
          success: () => {
            this.$message.success($tl('复制链接成功'))
          },
          fail: () => {
            this.$message.error($tl('复制链接失败'))
          }
        })
      },
      // 修改密码
      updatePwd() {
         uni.navigateTo({
           url: '/pages/login/validMail',
         })
      }
   },
  }
</script>

<style>
  page {
    background: #ffffff;
  }
</style>

<style lang="scss" scoped>
  .reset-choice-wrapper {
    padding: 64rpx 44rpx 0;

    .title {
      padding-left: 20rpx;
      margin-top: 64rpx;
      color: #1A1A1A;
    }

    .sub-title {
      display: block;
      margin-top: 16rpx;
      margin-bottom: 120rpx;
      padding-left: 20rpx;
      color: #3979F9;
      font-size: 28rpx;
    }
  }

  .choice-container {
    .choice-item {
      width: 100%;
      height: 188rpx;
      background: #F1F5F8;
      display: flex;
      padding: 20rpx;
      margin-bottom: 60rpx;
      border-radius: 12rpx;

      .item-icon {
        display: flex;
        margin-right: 20rpx;
        width: 148rpx;
        height: 148rpx;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        border-radius: 12rpx;
        overflow: hidden;
        image {
          vertical-align: middle;
          width: 148rpx;
          height: 148rpx;
        }
      }

      .cnt {
        width: 400rpx;

        .cnt-title {
          margin-bottom: 16rpx;
          font-size: 32rpx;
          color: #2D3132;
          font-weight: bold;
          display: block;
        }

        .sub-des {
          font-size: 28rpx;
          color: #666666;
          line-height: 40rpx;
        }

      }

      .choice-btn {
        margin-left: auto;
        margin-top: 44rpx;
        width: 172rpx;
        height: 60rpx;
        line-height: 60rpx;
        border-radius: 60rpx;
        text-align: center;
        font-size: 26rpx;
        color: #ffffff;
        background: #3979F9;
      }

      .copy-btn {
        margin-top: 64rpx;
        width: 24rpx;
        height: 24rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
</style>
