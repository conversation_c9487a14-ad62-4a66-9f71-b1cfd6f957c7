<!-- 版本更新 -->
<template>
  <view class="mask">
    <view class="mask-content">
      <view class="content">
        <view class="t-center text-bold fs-32">{{ title }}{{ version }}</view>
        <view class="contentpd">
          <view class="m-b-32">{{ $tl('版本说明') }}：</view>
          <view v-for="(item, index) in content" :key="index">
            {{ item }}
          </view>
        </view>
        <view class="tips">
          {{ $tl('后台升级不影响功能使用') }}
        </view>
      </view>
      <view class="footer">
        <view v-if="cancelText !== ''" class="cancel" @click="cancel">{{ cancelText }}</view>
        <view class="sure" @click="confirm">{{ confirmText }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import {
    $tl,
  } from '@/utils/i18n'
  export default {
    props: {
      title: {
        type: String,
        default: $tl('版本更新'),
      },
      version: {
        type: String,
        default: '',
      },
      content: {
        type: Array,
        default: [],
      },
      confirmText: {
        type: String,
        default: $tl('立即升级'),
      },
      cancelText: {
        type: String,
        default: $tl('忽略'),
      },
    },
    data() {
      return {}
    },
    methods: {
      confirm() {
        this.$emit('confirm')
      },
      cancel() {
        this.$emit('cancel')
      }
    }
  }
</script>

<style lang="scss" scoped>
  /* component/modal/modal.wxss */
  .mask {
    position: fixed;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.64);
    z-index: 9999;
  }

  .mask-content {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 40rpx 48rpx;
    border-radius: 32rpx;
    color: #000000;
    font-size: 28rpx;
    background: rgba(255, 255, 255, 1);
    line-height: 44rpx;
  }

  .mask-content .content {
    display: flex;
    flex-direction: column;
    width: 554rpx;
  }

  .mask-content .content .bold {
    font-weight: 600;
    font-size: 32rpx;
    margin-bottom: 10rpx;
  }

  .contentpd {
    padding: 32rpx 0 64rpx;
  }

  .tips {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    margin-bottom: 10rpx;
  }

  .footer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    color: #fff;
    font-size: 36rpx;
    padding-bottom: 8rpx;
  }

  .sure {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 248rpx;
    height: 80rpx;
    color: #fff;
    font-size: 32rpx;
    background-color: $color-main;
    border-radius: 40rpx;
  }

  .cancel {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 248rpx;
    height: 80rpx;
    margin-right: 48rpx;
    color: $color-text-black;
    font-size: 32rpx;
    font-weight: 550;
    background-color: $color-bg-gray;
    border-radius: 40rpx;
  }
</style>
