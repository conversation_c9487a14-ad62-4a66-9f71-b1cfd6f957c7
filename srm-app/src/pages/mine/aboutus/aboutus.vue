<!-- 关于我们 -->
<template>
  <view class="abouts-wrapper">
    <view class="abouts-list">
      <block v-for="(item, index) in items" :key="index">
        <srm-cell v-if="item.show" :type="item.type" :title="item.title" :left-icon="item.leftIcon"
          :value.sync="item.value" :click-action="item.action" @click="clickAction" />
      </block>
    </view>
    <srm-app-update ref="modal" showTip />
  </view>
</template>

<script>
  import http from '@/utils/http'
  import {
    cloneDeep,
  } from 'lodash'
  export default {
    data() {
      return {
        items: [{
            title: this.$tl('版本更新'),
            type: 'info',
            show: true,
            value: '',
            action: 'update',
          },
          {
            title: this.$tl('下载地址'),
            type: 'info',
            show: true,
            value: '',
            action: 'download',
          },
          {
            title: this.$tl('版本足迹'),
            type: 'info',
            show: true,
            value: '',
            action: 'footprints',
          },
        ],
        showUpdateModal: false,
        appVersionData: '',
      }
    },
    created() {
      this.initData()
    },
    methods: {
      //数据初始化
      async initData() {
        const res = await http.get(`/api/purchase/app-api/public-api/tenant/getVersionLog`, {}, {
          showLoading: false
        })
        if (res.code === 200) {
          this.appVersionData = cloneDeep(res.data[0])
          uni.setStorageSync('versionList', res.data);
        }
      },
      // 事件监听
      clickAction(action) {
        this[action]()
      },
      // 版本更新
      update() {
        this.$refs.modal.versionUpdate(this.appVersionData)
      },
      // 下载地址
      download() {
        uni.navigateTo({
          url: '/pages/mine/aboutus/download',
        })
      },
      // 版本足迹
      footprints() {
        uni.navigateTo({
          url: '/pages/mine/aboutus/footprints',
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
  @import '~@/static/css/bgbase64.scss';

  .abouts-wrapper {
    overflow: hidden;

    .abouts-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 16rpx;
      padding: 0 32rpx;
      background-color: #fff;

      .h16 {
        height: 16rpx;
      }
    }
  }
</style>
