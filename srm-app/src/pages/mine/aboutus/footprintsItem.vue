<template>
  <view class="footprints-item">
    <view class="header">
      <view class="version">
        <view v-if="showIcon(item.versionNumber)" class="icon"></view>
        v{{ item.versionNumber }}
      </view>
      <view class="date">{{ dateFormt(item.updateTime) }}</view>
    </view>
    <view class="content" v-html="item.versionDesc">
    </view>
  </view>
</template>

<script>
  import dayjs from 'dayjs'
  export default {
    props: {
      item: {
        type: Object,
        default: () => {}
      },
    },
    data() {
      return {}
    },
    methods: {
      dateFormt(date) {
        return dayjs(Number(date)).format('YYYY.MM.DD')
      },
      showIcon(versionNumber) {
        let flag = false
        // #ifdef APP-PLUS
        const {
          platform,
          appWgtVersion,
          appVersion
        } = getApp().globalData.systemInfo
        const _appVersion = appWgtVersion || appVersion
        if(this.compareVersion(versionNumber, _appVersion)){
          flag = true
        }
        // #endif
        return flag
      },
      compareVersion(newVersion, oldVersion) {
        const newVer = newVersion.split('.');
        const oldVer = oldVersion.split('.');
        for (let i = 0; i < oldVer.length; i++) {
          if (parseInt(oldVer[i]) > (parseInt(newVer[i]) || 0)) {
            return false;
          } else if (parseInt(oldVer[i]) < (parseInt(newVer[i]) || 0)) {
            return true;
          }
        }
        return false;
      }
    }
  }
</script>

<style lang="scss" scoped>
  .footprints-item {
    background-color: #ffffff;
    border-radius: 10px;

    .header {
      padding: 24rpx 32rpx;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #EDEEF0;

      .version {
        display: flex;
        align-items: center;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        letter-spacing: 0;

        .icon {
          width: 16rpx;
          height: 16rpx;
          background: #E64C3D;
          border-radius: 16rpx;
          margin-right: 16rpx;
        }
      }

      .date {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 28rpx;
        color: #ABADAD;
      }
    }

    .content {
      padding: 24rpx 32rpx;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      letter-spacing: 0;
      line-height: 42rpx;
    }
  }
</style>
ex_jiayu.zhang
