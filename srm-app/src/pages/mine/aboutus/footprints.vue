<!-- 版本足迹 -->
<template>
  <view class="footprints">
    <footprints-item v-for="(item, index) in itemList" :key="index" :item="item" :show-icon="index === 0"
      class="m-b-32" />
  </view>
</template>

<script>
  import FootprintsItem from "./footprintsItem.vue"
  export default {
    components: {
      FootprintsItem
    },
    data() {
      return {
        itemList: [{
            version: 'v2.2',
            date: '2024-07-31',
            content: ['1、物料新增需求监控上线', '2、 一系列体验问题优化']
          },
          {
            version: 'v2.1',
            date: '2024-06-30',
            content: ['1、物料新增需求监控上线', '2、 一系列体验问题优化']
          }
        ]
      }
    },
    onShow() {
       this.getLocalData()
    },
    methods: {
      getLocalData() {
        this.itemList = uni.getStorageSync('versionList')
      },
    }
  }
</script>

<style lang="scss" scoped>
  .footprints {
    padding: 24rpx 32rpx;
  }
</style>
