<!-- 下载地址 -->
<template>
  <view class="download">
    <view class="download-container">
      <view class="download-content">
        <view class="download-img">
          <image :src="qrCodeUrl" />
        </view>
        <view class="download-tips">
          <image src="/static/image/icon-tips.png" />
          微信扫码，点<text style="font-weight: bold;color:#4E5A70;">右上角</text>，用系统浏览器打开
        </view>
      </view>
    </view>
    <view class="footer">
      <view class="cancel" @click="copy">{{ $tl('复制下载地址') }}</view>
      <view class="sure" @click="save">{{ $tl('保存图片') }}</view>
    </view>
  </view>
</template>

<script>
  import {
    $tl,
  } from '@/utils/i18n'
  export default {
    data() {
      return {
        qrCodeUrl: ''
      }
    },

    mounted() {
      const {
        platform
      } = getApp().globalData.systemInfo
      let _url = ''
      if (platform === 'ios') {
        _url = process.env.NODE_ENV === 'production' ?
          'https://srm.tcl.com/api/purchase/app/public/pc/getAppleQrcode' :
          'https://srm-uat.eads.tcl.com/api/purchase/app/public/pc/getAppleQrcode'
      } else {
        _url = process.env.NODE_ENV === 'production' ? 'https://srm.tcl.com/api/purchase/app/public/pc/getQrcode' :
          'https://srm-uat.eads.tcl.com/api/purchase/app/public/pc/getQrcode'
      }
      this.qrCodeUrl = _url
    },
    methods: {
      async copy() {
        uni.setClipboardData({
          data: this.qrCodeUrl,
          success: () => {
            this.$message.success($tl('复制成功'))
          },
          fail: () => {
            this.$message.error($tl('复制失败'))
          }
        })
      },
      save() {
        // TODO 保存到本地的api调试
        uni.saveImageToPhotosAlbum({
          filePath: '/static/image/tabbar/tabbar-home-nor.png',
          success: () => {
            this.$message.success($tl('保存成功'))
          },
          fail: () => {
            this.$message.error($tl('保存失败'))
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .download-container {
    width: 100%;
    height: 100%;
    padding: 32rpx;

    .download-content {
      background-color: #fff;
      border-radius: 16rpx;
      padding-top: 50rpx;
      height: calc(100vh - 150px);

      .download-img {
        display: flex;
        justify-content: center;
        align-items: center;

        image {
          width: 520rpx;
          height: 520rpx;
        }
      }

      .download-tips {
        display: flex;
        margin-top: 60rpx;
        padding-left: 64rpx;
        padding-bottom: 48rpx;
        color: #999999;
        font-size: 28rpx;
        font-weight: 400;
        line-height: 48rpx;

        image {
          width: 40rpx;
          height: 40rpx;
          margin-right: 12rpx;
        }
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    background-color: #fff;
    color: #fff;
    font-size: 36rpx;
    padding: 32rpx;
  }

  .sure {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 320rpx;
    height: 80rpx;
    color: #fff;
    font-size: 32rpx;
    background-color: $color-main;
    border-radius: 44rpx;
  }

  .cancel {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 320rpx;
    height: 80rpx;
    margin-right: 48rpx;
    border: 1px solid #4E5A70;
    color: #4E5A70;
    font-size: 32rpx;
    border-radius: 44rpx;
  }
</style>
