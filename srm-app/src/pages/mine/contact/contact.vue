<template>
  <view class="contact">
    <!-- <view class="search">搜索框</view> -->
    <view class="content">
      <contact-item v-for="(item, index) in contactList" :key="index" :item="item" class="m-b-32" />
    </view>
  </view>
</template>

<script>
  import ContactItem from "./contactItem.vue"
  import http from '@/utils/http'
  export default {
    components: {
      ContactItem
    },
    data() {
      return {
        searchKey: '',
        colorList: ['green','yellow','red','blue'],
        contactList:[]
      }
    },
    mounted() {
      this.initData()
    },
    methods: {
      async initData() {
        const res = await http.get(`/api/contract/app-api/public-api/tenant/getContractInfo`,{})
        if (res.code === 200) {
          this.contactList = res.data.map((item) => {
            return {
              ...item,
              mark: this.colorList[Math.floor(Math.random() * 4)],
              surname: item.name?.substring(0,1),
            }
          })
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .search {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 96rpx;
    background-color: #fff;

    .label {
      align-self: flex-start;
      font-size: 28rpx;
      font-weight: 500;
      color: #B4B3B2;
      letter-spacing: 0;
      margin-bottom: 8rpx;
    }

    input {
      margin: 0 36rpx;
      width: 100%;
      color: $color-text-black;
      font-size: 28rpx;
      border-bottom: 1px solid $color-border-dark;
      padding: 16rpx 0;
    }

    .text-placeholder {
      color: $color-text-placeholder;
      font-size: 28rpx;
    }
  }

  .content {
    padding: 24rpx 32rpx;
  }
</style>
