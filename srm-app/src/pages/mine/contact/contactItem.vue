<template>
  <view class="contact-item">
    <view class="left">{{ item.surname }}</view>
    <view class="right">
      <view class="right-one">
        <view class="name">{{ item.name }}</view>
        <view class="deparment" :class="item.mark">{{ item.position }}</view>
      </view>
      <view class="phone" @longpress="callPhone(item.tel)" longpress-start-time="500">{{ item.tel }}</view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      item: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {}
    },
    methods: {
      callPhone(tel){
        console.log(tel)
        // #ifdef APP-PLUS
        uni.makePhoneCall({
          phoneNumber: tel,
          success: function() {
            console.log("拨打电话成功！")
          },
          fail: function() {
            console.log("拨打电话失败！")
          }
        })
        // #endif
        // #ifdef H5
        window.location.href = `tel:${tel}`;
        // #endif
      }




    }
  }
</script>

<style lang="scss" scoped>
  .contact-item {
    height: 150rpx;
    background-color: #ffffff;
    border-radius: 10px;
    display: flex;
    align-items: center;

    .left {
      width: 100rpx;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      background-color: #4E5A70;
      border-radius: 32px;
      font-family: PingFangSC-SNaNpxibold;
      font-weight: 600;
      font-size: 32rpx;
      color: #FFFFFF;
      letter-spacing: 0;
      margin: 24rpx
    }

    .right {
      .right-one {
        display: flex;

        .name {
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: 32rpx;
          color: #333333;
          letter-spacing: 0;
          margin-right: 16rpx;
        }

        .deparment {
          width: 80rpx;
          height: 40rpx;
          line-height: 40rpx;
          text-align: center;
          background-color: rgba(252, 186, 22, 0.1);
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 24rpx;
          color: #FCBA16;
          border-radius: 8rpx;
        }

        .yellow {
          background-color: rgba(252, 186, 22, 0.1);
          color: #FCBA16;
        }

        .red {
          background-color: rgba(245, 84, 72, 0.1);
          color: #F55448;
        }

        .green {
          background-color: rgba(44, 220, 155, 0.1);
          color: #2CDC9B;
        }

        .blue {
          background-color: rgba(57, 121, 249, 0.1);
          color: #3979F9;
        }
      }

      .phone {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 28rpx;
        color: #3678FE;
        letter-spacing: 0;
        margin-top: 12rpx;
      }
    }
  }
</style>
