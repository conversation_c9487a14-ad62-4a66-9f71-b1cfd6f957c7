<template>
  <view class="setting-wrapper">
    <view class="setting-list">
      <block v-for="(item, index) in items" :key="index">
        <srm-cell v-if="item.show" :type="item.type" :title="item.title" :left-icon="item.leftIcon"
          :value.sync="item.value" :click-action="item.action" @click="clickAction" />
      </block>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        items: [
          // {
          //   title: this.$tl('账号与安全'),
          //   type: 'info',
          //   show: true,
          //   value: '',
          //   action: 'safety',
          // },
          {
            title: this.$tl('新消息通知'),
            type: 'info',
            show: true,
            value: '',
            action: 'notify',
          },
          // {
          //   title: this.$tl('字体大小'),
          //   type: 'info',
          //   show: true,
          //   value: '',
          //   action: 'fontsize',
          // },
          // {
          //   title: this.$tl('清理缓存'),
          //   type: 'info',
          //   show: true,
          //   value: '',
          //   action: 'clear',
          // }
        ],
      }
    },
    methods: {
      clickAction(action) {
        this[action]()
      },
      safety() {},
      notify() {
       uni.navigateTo({
         url: '/pages/mine/setting/notices',
       })
      },
      fontsize() {},
      clear() {}
    }
  }
</script>

<style lang="scss" scoped>
  @import '~@/static/css/bgbase64.scss';

  .setting-wrapper {
    overflow: hidden;

    .setting-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 16rpx;
      padding: 0 32rpx;
      background-color: #fff;

      .h16 {
        height: 16rpx;
      }
    }
  }
</style>
