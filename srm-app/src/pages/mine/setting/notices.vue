<template>
  <view class="setting-wrapper">
    <view class="setting-list">
      <srm-cell type="slot" title="新消息通知">
        <switch :checked="checked" @change="handleSwitch" style="transform:scale(0.7)" />
      </srm-cell>
      <srm-cell type="slot" title="消息提示音">
        <switch :checked="voiceChecked" @change="handleVoiceSwitch" style="transform:scale(0.7)" />
      </srm-cell>
    </view>
  </view>
</template>

<script>
  import {
    mapGetters,
    mapMutations
  } from 'vuex'
  export default {
    data() {
      return {
        switchs: ['', '']
      }
    },
    computed: {
      ...mapGetters('setting', {
        notify: 'notify',
        voice: 'voice',
      }),
    },
    onShow() {
      this.checked = this.notify !== 1
      this.voiceChecked = this.notify !== 1
    },
    methods: {
      ...mapMutations('setting', {
        setNotify: 'SET_NOTIFY',
        setVoice: 'SET_VOICE',
      }),
      handleSwitch(e) {
        this.setNotify(e.detail.value ? 2 : 1)
      },
      handleVoiceSwitch(e) {
        this.setVoice(e.detail.value ? 2 : 1)
      },
    }
  }
</script>

<style lang="scss" scoped>
  @import '~@/static/css/bgbase64.scss';

  .setting-wrapper {
    overflow: hidden;

    .setting-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 16rpx;
      padding: 0 32rpx;
      background-color: #fff;

      .h16 {
        height: 16rpx;
      }
    }
  }
</style>
