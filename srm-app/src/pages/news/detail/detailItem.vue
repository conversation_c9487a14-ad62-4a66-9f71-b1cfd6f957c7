<template>
  <view class="detail-item">
    <view class="top">
      {{ detailContent.pushTime }}
    </view>
    <view class="content">
      <view class="left">
        <image :src="detailContent.iconUrl" />
      </view>
      <view class="right">
        <text class="title" v-html="detailContent.message" selectable></text>
        <!-- <view class="btn" @click="btnClick">{{ $tl('去操作') }}</view> -->
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      detailContent: {
        type: Object,
        default: () => {}
      }
    },
    methods: {
      btnClick() {
        this.$emit('click', this.detailContent)
      }
    }
  }
</script>

<style lang="scss">
  .detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24rpx;
    .top {
      margin: 10rpx 0;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 28rpx;
      color: #ABADAD;
    }

    .content {
      display: flex;
      justify-content: space-evenly;

      .left {
        image {
          width: 80rpx;
          height: 80rpx;
        }
      }

      .right {
        width: 80%;
        background-image: linear-gradient(-21deg, #EEF4FF 0%, #FFFFFF 100%);
        box-shadow: 0 8rpx 10rpx 0 #b5bfd43b;
        border-radius: 0 16rpx 16rpx 16rpx;
        padding: 20rpx;

        .title {
          color: #333333;
          font-size: 24rpx;
          letter-spacing: 0;
          paragraph-spacing: 0;
          text-align: left;
          line-height: 36rpx;
          word-wrap: break-word;
          word-break: break-all;
        }

        .btn {
          padding-top: 20rpx;
          font-family: PingFangSC-Regular;
          font-weight: 500;
          font-size: 24rpx;
          color: #2C6AF8;
        }
      }
    }
  }
</style>
