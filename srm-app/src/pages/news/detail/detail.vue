<template>
  <view>
    <srm-paging ref="paging" v-model="detailList" @query="queryList">
      <template #top>
        <uni-nav-bar left-icon="left" :title="title" @clickLeft="back" />
      </template>
      <view class="detail-container">
        <view class="content">
          <detail-item v-for="(item, index) in detailList" :key="index" :detail-content="item" />
        </view>
      </view>
    </srm-paging>
  </view>
</template>

<script>
  import http from '@/utils/http'
  import dayjs from 'dayjs'
  import DetailItem from "./detailItem.vue"
  import {
    mapGetters,
  } from 'vuex'
  export default {
    components: {
      DetailItem
    },
    onLoad: function(option) {
      this.businessModuleCode = option.businessModuleCode
      this.title = this.businessOptions.find((v) => v.value == option.businessModuleCode)?.name
    },
    data() {
      return {
        title: '',
        businessModuleCode: '',
        detailList: [],
        businessOptions: [{
            name: this.$tl('库存情况'),
            value: 1
          },
          {
            name: this.$tl('交货计划'),
            value: 2
          },
          {
            name: this.$tl('叫料计划'),
            value: 3
          },
          {
            name: this.$tl('送货单列表'),
            value: 4
          },
          {
            name: this.$tl('采购订单'),
            value: 5
          },
          {
            name: this.$tl('质检监控'),
            value: 6
          },
          {
            name: this.$tl('产能管理'),
            value: 7
          }
        ],
        iconOotions: [{
            url: '/static/image/news/news-inventory-icon.png',
            value: 1
          },
          {
            url: '/static/image/news/news-schedule-icon.png',
            value: 2
          },
          {
            url: '/static/image/news/news-jit-icon.png',
            value: 3
          },
          {
            url: '/static/image/news/news-delivery-icon.png',
            value: 4
          },
          {
            url: '/static/image/news/news-delivery-icon.png',
            value: 5
          },
          {
            url: '/static/image/news/news-jit-icon.png',
            value: 6
          },
          {
            url: '/static/image/news/news-inventory-icon.png',
            value: 7
          }
        ]
      }
    },
    computed: {
      ...mapGetters('user', {
        role: 'role',
      }),
    },
    methods: {
      async queryList(pageNo, pageSize) {
        const _now = dayjs().startOf('day').valueOf()
        let params = {
          businessModuleCode: this.businessModuleCode,
          page: {
            current: pageNo,
            size: pageSize
          }
        }
        const res = await http.post(
          `/api/purchase/app-api/${this.role === 1 ? 'purchase': 'supplier'}/push/pageModuleMessage`, params)
        if (res.code === 200) {
          let detailList = res.data.records.map((item) => {
            return {
              iconUrl: this.iconOotions.find((v) => v.value == this.businessModuleCode)?.url,
              message: item.message,
              pushTime: item.pushTime < _now ? dayjs(Number(item.pushTime)).format('MM月DD日 HH:mm') : dayjs(Number(
                item.pushTime)).format('HH:mm'),
            }
          })
          this.$refs.paging.complete(detailList || [])
        }
      },
      back() {
        uni.reLaunch({
          url: '/pages/tabBar/news/news',
        })
      },
    }
  }
</script>
