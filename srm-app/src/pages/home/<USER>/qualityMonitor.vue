<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('质检监控')" :config="config" :cardConfig="cardConfig" @search="search"></srm-search-page>
</template>

<script>
  import {
    getTimeList,
  } from '@/utils/tools'
  import { backPress } from '@/mixin'
  export default {
    components: {},
    mixins: [backPress],
    data() {
      return {
        appDeliveryType: '',
        account: '',
        config: {
          type: 'dateRange', // 顶部输入框类型
          headerFieldCode: 'inspTime', // 顶部输入框字段
          headerDefaultValue: [new Date().getTime(), new Date().getTime()], // 顶部输入框值
          url: '/api/contract/app-api/purchase/inspection/query/page', // 接口请求api
          supplierUrl: '/api/contract/app-api/supplier/inspection/supplier/page', // 供方接口请求api
          fields: [ // 更多查询字段
            {
              fieldCode: 'inspectNo',
              fieldName: '送检单号',
            },
            {
              fieldCode: 'deliverNo',
              fieldName: '送货单号',
            },
            // {
            //   fieldCode: 'carNo',
            //   fieldName: '车牌号',
            // },
           {
             fieldCode: 'buyerOrgCode',
             fieldName: '采购组',
             type: 'multiSelect',
             url: '/api/masterDataManagement/app-api/public-api/business/group/paged-query',
             fields: {
               text: 'groupCode_groupName',
               value: 'groupCode'
             },
             queryKey: 'buyerGroup' // 查询关键字
           },
            {
              fieldCode: 'supplierCode',
              fieldName: '供应商编码',
            },
            {
              fieldCode: 'supplierName',
              fieldName: '供应商名称',
            },
            {
              fieldCode: 'itemCode',
              fieldName: '物料',
            },
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode'
              },
              queryKey: 'site' // 查询关键字
            },
            {
              fieldCode: 'inspTime',
              fieldName: '质检日期',
              type: 'dateRange',
            },
          ],
          statusFieldCode: 'stateCode', // 默认status, 后端约定字段
          statusDefaultValue: -1, // 默认选中状态
          tabs: [{
              tabCode: 1,
              tabName: '待质检'
            },
            {
              tabCode: 2,
              tabName: '质检中'
            },
            {
              tabCode: 3,
              tabName: '质检异常'
            },
            {
              tabCode: 4,
              tabName: '质检正常'
            },
            {
              tabCode: -1,
              tabName: '全部'
            },
          ]
        },
        cardConfig: {
          businessModuleCode: '6',
          header: { // 头信息配置
            icon: 'sjdh',
            fieldCode: 'inspectNo',
            statusCode: 'state',
            statusColorKey: 'stateCode',
            statusColorMap: { //此处是状态和颜色背景颜色的映射值
              1: '#2CDC9B', //检验完成
              2: '#FCBA16', //待检验
              3: '#3979F9', //检验前置已完成
              4: '#3979F9', //驻厂检验已完成
              5: '#FCBA16', //部门负责人审批
              6: '#FCBA16', //IQC检验组长审核
              7: '#FCBA16', //待送检
              8: '#3979F9', //检验中,
              9: '#2CDC9B', //已免检
              10: '#3979F9', //二次检验
              11: '#3979F9', //专业工程师确认
              12: '#999999', //其他状态
            }
          },
          simple: [ //简述信息配置
            {
              icon: 'gsgccgz',
              fieldCode: 'companyCode_factoryCode_buyerOrgCode'
            },
            {
              icon: 'supplier',
              fieldCode: 'supplierCode_supplierShortName',
              join: '-',
              right: true
            },
            {
              icon: 'item',
              fieldCode: 'materialCode',
            },
            {
              icon: 'number',
              fieldCode: 'sendInspectQty',
              right: true
            },
            {
              icon: 'time',
              fieldCode: 'inspDate',
              width: '60%'
            },
            {
              type: 'bg',
              fieldCode: 'inspectResults',
              colorKey: 'inspectResults',
              width: '40%',
              right: true,
              colorMap: {
                null: { // NG
                  text: '#F55448',
                  bg: 'rgba(245, 84, 72, 0.1)'
                },
                'NG': { // NG
                  text: '#F55448',
                  bg: 'rgba(245, 84, 72, 0.1)'
                },
                'OK': { // NG
                  text: '#2CDB9B',
                  bg: 'rgba(44, 219, 155, 0.1)'
                },
              }
            },
          ],
          detail: [ // 详情信息配置
            {
              fieldCode: 'deliverNo',
              fieldName: this.$tl('送货单号')
            },
            {
              fieldCode: 'materialName',
              fieldName: this.$tl('物料名称')
            },
            {
              fieldCode: 'subSiteAddressCode_subSiteAddress',
              fieldName: this.$tl('分厂库存地点')
            },
            {
              fieldCode: 'requireDeliveryDate',
              fieldName: this.$tl('要求交货日期')
            },
            {
              fieldCode: 'incomingCnt',
              fieldName: this.$tl('来料次数')
            },
            {
              fieldCode: 'maSampleQty',
              fieldName: this.$tl('最大抽样数')
            },
            {
              fieldCode: 'inspectResults',
              fieldName: this.$tl('检验结论'),

            },
            {
              fieldCode: 'unqltyReason',
              fieldName: this.$tl('不合格原因')
            },
            {
              fieldCode: 'inspectorName',
              fieldName: this.$tl('检验员')
            },
            {
              fieldCode: 'trialConcl',
              fieldName: this.$tl('审理结论'),
              join: '/'
            },
            {
              fieldCode: 'engineerName',
              fieldName: this.$tl('专业工程师')
            },
            {
              fieldCode: 'iqcInspectGroupLeader',
              fieldName: this.$tl('IQC检验组长')
            },
            {
              fieldCode: 'jitName',
              fieldName: this.$tl('是否JIT')
            },
            {
              fieldCode: 'prodtWorkNo',
              fieldName: this.$tl('生产工单号')
            },
            {
              fieldCode: 'vmiWarehouseTypeName',
              fieldName: this.$tl('生产线')
            },
            {
              fieldCode: 'supplierCode_supplierName',
              fieldName: this.$tl('供应商名称')
            },
            {
              fieldCode: 'factoryCode_factoryName',
              fieldName: this.$tl('工厂名称')
            },
            {
              fieldCode: 'buyerOrgName',
              fieldName: this.$tl('采购组名称')
            },
          ],
          footer: { // 列表页 footer
            sendBtnTabsArr: [1, 3], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/inspection/time/out/remind',
            sendParams: ['ids'],
          },
          detailFooter: { // 详情页 footer
            sendBtnTabsArr: [1, 3], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/inspection/time/out/remind',
            sendParams: ['ids'],
            hasTips: true,
          }
        },
      }
    },

    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },
    mounted() {},
    methods: {

    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }
</style>
