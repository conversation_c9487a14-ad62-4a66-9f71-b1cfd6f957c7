<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('供货计划 - 叫料计划')" :config="config" :cardConfig="cardConfig"
    :placeholder="$tl('请输入工厂/物料/销售订单号')" @search="search" @handleClickToolbar="handleClickToolbar"></srm-search-page>
</template>

<script>
  import {
    getTimeList,
  } from '@/utils/tools'
  import storage from '@/utils/storage'
    import http from '@/utils/http'
  import {
    backPress
  } from '@/mixin'
  export default {
    components: {},
    mixins: [backPress],
    data() {
      return {
        appDeliveryType: '',
        account: '',
        config: {
          type: 'input', // 顶部输入框类型
          headerFieldCode: 'searchText', // 顶部输入框字段
          headerDefaultValue: '', // 顶部输入框值
          toolbar: [], // 操作按钮，如果多个会显示操作更多，暂未封装
          supplierToolbar:[{
            text: '扫描装车',
            code: 'scanLoad',
          }],
          checked: true, // 是否可选择
          url: '/api/srm-purchase-execute/app-api/purchase/tv-jit/page', // 接口请求api
          supplierUrl: '/api/srm-purchase-execute/app-api/supplier/tv-jit/page', // 接口请求api
          fields: [ // 更多查询字段
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode'
              },
              queryKey: 'site' // 查询关键字
            },
            {
              fieldCode: 'itemCode',
              fieldName: '物料',
            },
            {
              fieldCode: 'saleOrderNo',
              fieldName: '销售订单号',
            },
            {
              fieldCode: 'demandDate',
              fieldName: '交货日期',
              type: 'dateRange'
            },
            {
              fieldCode: 'workCenter',
              fieldName: '工作中心',
            },
            {
              fieldCode: 'serialNo',
              fieldName: '序列号',
            },
            {
              fieldCode: 'receiverContactName',
              fieldName: '收货人',
            },
            {
              fieldCode: 'receiverContact',
              fieldName: '收货人联系方式',
            },
            {
              fieldCode: 'receiveAddress',
              fieldName: '收货地址',
            },
            // {
            //   fieldCode: 'createTime',
            //   fieldName: '创建时间',
            //   type: 'dateRange'
            // },
          ],
          statusFieldCode: 'queryType', // 默认status, 后端约定字段
          statusDefaultValue: 4, // 默认选中状态
          tabs: [{
              tabCode: 1,
              tabName: '未发货'
            },
            {
              tabCode: 2,
              tabName: '部分发货'
            },
            {
              tabCode: 3,
              tabName: '全部发货'
            },
            {
              tabCode: 4,
              tabName: '全部'
            }
          ],
          params: {
            // appDeliveryType: Number(this.appDeliveryType)
          }
        },
        cardConfig: {
          businessModuleCode: '2',
          header: { // 头信息配置
            icon: 'shd',
            fieldCode: 'saleOrderNo',
            statusCode: 'statusDesc',
            statusColorKey: 'status',
            statusColorMap: { //此处是状态和颜色背景颜色的映射值
              0: '#3979F9', // 新建
              1: '#3979F9', // 已修改
              2: '#3979F9', // 已发布
              3: '#2CDC9B', // 反馈正常
              4: '#F55448', // 反馈异常
              5: '#3979F9', // 已确认
              6: '#F55448', // 已关闭
              7: '#3979F9', // 同步中
              '-99': '#999999', // 未知
            }
          },
          simple: [ //简述信息配置
            {
              icon: 'item',
              fieldCode: 'itemCode',
              width: '100%'
            },
            {
              icon: 'gsgccgz',
              text: this.$tl('工厂/工作中心：'),
              fieldCode: 'siteCode_workCenterCode',
              width: '100%'
            },
            {
              icon: 'supplier',
              fieldCode: 'senderAddress',
              width: '100%'
            },
            {
              icon: 'time',
              fieldCode: 'requiredDeliveryDate_requiredDeliveryTime',
              join: ' ',
              width: '60%'
            },
            {
              icon: 'number',
              fieldCode: 'demandQty_remainCreateQty',
              width: '40%'
            },

          ],
          detail: [ // 详情信息配置
            {
              fieldCode: ' itemCode_itemName',
              fieldName: this.$tl('物料名称')
            },
            {
              fieldCode: 'siteCode_siteName',
              fieldName: this.$tl('工厂名称')
            },
            {
              fieldCode: 'workCenter',
              fieldName: this.$tl('工作中心名称')
            },
            {
              fieldCode: 'receiverContactName',
              fieldName: this.$tl('收货人')
            },
            {
              fieldCode: 'receiverContact',
              fieldName: this.$tl('收货人联系方式')
            },
            {
              fieldCode: 'receiveAddress',
              fieldName: this.$tl('收货地址')
            },
            {
              fieldCode: 'saleOrderNo',
              fieldName: this.$tl('销售订单号')
            },
            {
              fieldCode: 'demandQty',
              fieldName: this.$tl('需求数量')
            },
            {
              fieldCode: 'remainCreateQty',
              fieldName: this.$tl('剩余可创建数量')
            },
            {
              fieldCode: 'deliveryQty',
              fieldName: this.$tl('已发货数量')
            },
            {
              fieldCode: 'transitQty',
              fieldName: this.$tl('在途数量'),
              join: '/'
            },
            {
              fieldCode: 'receivedQuantity',
              fieldName: this.$tl('已收货数量')
            },
            // {
            //   fieldCode: 'createUserName',
            //   fieldName: this.$tl('创建人')
            // },
            // {
            //   fieldCode: 'createTime',
            //   fieldName: this.$tl('创建时间')
            // },
            // {
            //   fieldCode: 'updateUserName',
            //   fieldName: this.$tl('更新人')
            // },
            // {
            //   fieldCode: 'updateTime',
            //   fieldName: this.$tl('更新时间')
            // }
          ],
          footer: { // 列表页 footer
            sendBtnTabsArr: [], // 根据页签来显示发送按钮
            sendUrl: '',
            sendParams: ['ids', 'queryType'],
          },
          detailFooter: { // 详情页 footer
            sendUrl: '',
            sendParams: ['ids', 'queryType'],
            sendBtnTabsArr: [], // 根据页签来显示发送按钮
            hasTips: true,
          }
        },
      }
    },
    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },
    mounted() {},
    methods: {
      handleClickToolbar({
        e,
        data
      }) {
        if (e.code === 'scanLoad') {
          this.handleClickScanLoad(data)
        }
      },
      async handleClickScanLoad(e) {
        if (!e?.selectedList?.length) {
          uni.showToast({
            title: '请选择数据',
            duration: 1500,
            icon: 'none',
            mask: false,
          })
          return
        }
        let ids = []
        let list = []
        const params = e.selectedList?.forEach(item => {
          list.push({
            ...item
          })
          ids.push(item.id)
        })
        const res = await http.post(`/api/srm-purchase-execute/app-api/supplier/tv-qbj/loading-vehicle/appVerificationSupplyPlan`, {
          jitPlanIdList: ids
        }, {
          showLoading: false
        }).catch(err => {
          console.log(err)
        });
        if (res.code !== 200) {
          uni.showToast({
            title: res.msg,
            duration: 1500,
            icon: 'none',
            mask: false,
          })
          return
        }
        storage.setItemSync('scanLoadData', {list})
        uni.navigateTo({
          url: `/pages/home/<USER>/scanLoadFillInfo`,
        })
      }
    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }
</style>
