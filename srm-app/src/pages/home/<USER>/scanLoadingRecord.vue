<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('扫描装车记录')" :config="config" :card-config="cardConfig" :placeholder="$tl('请输入送货单号/工厂/车牌号/地址')" @search="search">
    <!-- 详情页面中的明细表格 -->
    <view class="item-info">
      <view class="info-title">明细信息<text class="tips">共计 <text class="bold">{{ detailTableData.length }}</text> 条记录</text></view>
      <view class="info-table-container">
        <srm-table :data="detailTableData" :columns="detailColumns" :formatter="handleFormatter" />
      </view>
    </view>
  </srm-search-page>
</template>

<script>
  import http from '@/utils/http'
  import { backPress } from '@/mixin'
  export default {
    components: {},
    mixins: [backPress],
    data() {
      return {
        appDeliveryType: '',
        account: '',
        config: {
          type: 'input', // 顶部输入框类型
          headerFieldCode: 'searchText', // 顶部输入框字段 - 对接接口字段
          headerDefaultValue: '', // 顶部输入框默认值
          url: '/api/srm-purchase-execute/app-api/purchase/tv-qbj/loading-vehicle/pageQuery', // 接口请求api
          supplierUrl: '/api/srm-purchase-execute/app-api/supplier/tv-qbj/loading-vehicle/pageQuery', // 供方接口请求api
          fields: [ // 更多查询字段
            {
              fieldCode: 'loadCarOrderNo',
              fieldName: '装车单号',
            },
            {
              fieldCode: 'deliveryCode',
              fieldName: '送货单号',
            },
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode',
              },
              queryKey: 'site', // 查询关键字
            },
            {
              fieldCode: 'vehicleNo',
              fieldName: '车牌号',
            },
            {
              fieldCode: 'supplierCode',
              fieldName: '供应商编码',
            },
            {
              fieldCode: 'supplierName',
              fieldName: '供应商名称',
            },
            {
              fieldCode: 'deliverAddr',
              fieldName: '收货地址',
            },
            {
              fieldCode: 'loadingTime',
              fieldName: '装车日期',
              type: 'dateRange',
            },
          ],
          params: { queryType: 6 },
          statusFieldCode: 'status',
          statusDefaultValue: null, // 默认选中状态
          tabs: [{
                   tabCode: 1,
                   tabName: '新建',
                 },
                 {
                   tabCode: 2,
                   tabName: '发货中',
                 },
                 {
                   tabCode: 3,
                   tabName: '已完成',
                 },
                 {
                   tabCode: 4,
                   tabName: '已取消',
                 },
                 {
                   tabCode: 5,
                   tabName: '已关闭',
                 },
                 {
                   tabCode: null,
                   tabName: '全部',
                 },
          ],
        },
        cardConfig: {
          businessModuleCode: '7',
          header: { // 头信息配置
            icon: 'title',
            fieldCode: 'loadCarOrderNo',
            statusCode: 'statusDesc',
            statusColorKey: 'status',
            statusColorMap: { // 此处是状态和颜色背景颜色的映射值
              1: '#FCBA16', // 新建
              2: '#3979F9', // 发货中
              3: '#2CDC9B', // 已完成
              4: '#999999', // 已取消
              5: '#999999', // 已关闭
            },
          },
          simple: [ // 简述信息配置
            {
              icon: 'gsgccgz',
              fieldCode: 'deliveryCode',
              width: '60%',
            },
            {
              icon: 'car',
              fieldCode: 'siteCode_vehicleNo',
              join: '/',
              width: '40%',
              right: true,
            },
            {
              icon: 'location',
              fieldCode: 'deliverAddr',
              width: '100%',
            },
            {
              icon: 'number',
              fieldCode: 'cardBoardQty_totalBoxQty',
              join: '/',
              width: '60%',
            },
            {
              type: 'bg',
              fieldCode: 'totalDriverQty',
              colorKey: 'status',
              width: '40%',
              right: true,
              colorMap: {
                1: { // 新建
                  text: '#F55448',
                  bg: 'rgba(245, 84, 72, 0.1)',
                },
                2: { // 发货中
                  text: '#F55448',
                  bg: 'rgba(245, 84, 72, 0.1)',
                },
                3: { // 已完成
                  text: '#F55448',
                  bg: 'rgba(245, 84, 72, 0.1)',
                },
                4: { // 已取消
                  text: '#F55448',
                  bg: 'rgba(245, 84, 72, 0.1)',
                },
                5: { // 已关闭
                  text: '#F55448',
                  bg: 'rgba(245, 84, 72, 0.1)',
                },
              },
            },
          ],
          footer: { // 列表页 footer
            sendBtnTabsArr: [], // 根据页签来显示同步按钮
            sendUrl: '/api/srm-purchase-execute/app-api/purchase/tv-qbj/loading-vehicle/syncTms',
            sendParams: ['ids'],
          },
          detailFooter: { // 详情页 footer
            sendBtnTabsArr: [], // 根据页签来显示同步按钮
            sendUrl: '/api/srm-purchase-execute/app-api/purchase/tv-qbj/loading-vehicle/syncTms',
            sendParams: ['ids'],
            hasTips: true,
          },
        },
        // 详情页面表格数据
        detailTableData: [],
        // 监听详情页面相关变量
        lastDetailId: null,
        detailMonitorTimer: null,
        // 详情页面表格列配置
        detailColumns: [
          {
            name: 'index',
            label: '序号',
            width: 60,
            type: 'index',
          },
          {
            name: 'boxId',
            label: 'BOXID',
            width: 140,
          },
          {
            name: 'materialCode',
            label: '物料编码',
            width: 120,
          },
          {
            name: 'materialName',
            label: '物料名称',
            width: 150,
          },
          {
            name: 'packingQuantity',
            label: '装箱数量',
            width: 100,
          },
          {
            name: 'boxBodyLength',
            label: '长(CM)',
            width: 100,
          },
          {
            name: 'boxBodyWide',
            label: '宽(CM)',
            width: 100,
          },
          {
            name: 'boxBodyHeight',
            label: '高(CM)',
            width: 100,
          },
          {
            name: 'grossWeight',
            label: '毛重(KG)',
            width: 100,
          },
          {
            name: 'netWeight',
            label: '净重(KG)',
            width: 100,
          },
          {
            name: 'saleOrderCode',
            label: '销售订单号',
            width: 100,
          },
          {
            name: 'barcodeLevel',
            label: '条码层级',
            width: 100,
          },
          {
            name: 'produceDate',
            label: '生产日期',
            width: 160,
          },
          {
            name: 'syncTmsStatus',
            label: '同步TMS状态',
            width: 120,
            formatter: this.formatSyncTmsStatus,
          },
          {
            name: 'syncTmsDesc',
            label: '同步TMS接口信息',
            width: 140,
          },
        ],
      }
    },

    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },

    mounted() {
      // 监听详情页面打开
      this.startDetailMonitor()
    },
    beforeDestroy() {
      // 清理定时器
      if (this.detailMonitorTimer) {
        clearInterval(this.detailMonitorTimer)
        this.detailMonitorTimer = null
      }
    },
    methods: {
      search() {},
      // 表格formatter处理器
      handleFormatter(row, column, rowIndex, columnIndex) {
        if (column.formatter && typeof column.formatter === 'function') {
          return column.formatter(row, column, rowIndex, columnIndex)
        }
        return row[column.name]
      },
      // 格式化同步TMS状态显示
      formatSyncTmsStatus(row) {
        const status = row.syncTmsStatus
        const statusMap = {
          0: '未同步',
          1: '同步成功',
          2: '同步失败',
        }
        return statusMap[status] || '--'
      },
      // 处理详情页面打开事件
      handleDetailOpen(item) {
        if (item && item.id) {
          // 调用接口获取明细数据
          this.getLoadingVehicleDetail(item.id)
        }
      },
      // 开始监听详情页面
      startDetailMonitor() {
        this.detailMonitorTimer = setInterval(() => {
          if (this.$refs.searchPage &&
            this.$refs.searchPage.detailVisible &&
            this.$refs.searchPage.detailDataSource &&
            this.$refs.searchPage.detailDataSource.id !== this.lastDetailId) {
            const item = this.$refs.searchPage.detailDataSource
            this.lastDetailId = item.id
            this.handleDetailOpen(item)
          }
        }, 500) // 每500ms检查一次
      },
      // 获取装车明细记录
      async getLoadingVehicleDetail(headerId) {
        try {
          const params = {
            headerId: headerId,
            page: {
              current: 1,
              size: 1000, // 获取所有明细
            },
          }

          const res = await http.post(
            '/api/srm-purchase-execute/app-api/supplier/tv-qbj/loading-vehicle/detailList',
            params,
            { showLoading: true },
          )

          if (res.code === 200 && res.data && res.data.records) {
            this.detailTableData = res.data.records.map((item, index) => ({
              ...item,
              index: index + 1, // 手动添加序号
            }))
          } else {
            this.detailTableData = []
            uni.showToast({
              title: '获取明细数据失败',
              icon: 'none',
              duration: 2000,
            })
          }
        } catch (error) {
          this.detailTableData = []
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none',
            duration: 2000,
          })
        }
      },
    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }

  .item-info {
    background: #ffffff;
    padding: 32rpx 32rpx 32rpx 32rpx;

    .info-title {
      font-weight: 500;
      font-size: 30rpx;
      color: #333333;
      margin-bottom: 24rpx;

      &::before {
        content: '';
        display: inline-block;
        width: 6rpx;
        height: 24rpx;
        background: #3678FE;
        border-radius: 0 100rpx 0 0;
        margin-right: 12rpx;
      }
    }

    .info-table-container {
      width: 720rpx;
      overflow-x: scroll;
      white-space: nowrap;
    }

  }

  .tips {
    margin-left: 16rpx;
    font-size: 28rpx;

    .bold {
      font-weight: bold;
      padding: 0 4rpx;
    }
  }
</style>
