<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('采购订单')" :config="config" :cardConfig="cardConfig"
    @search="search"></srm-search-page>
</template>

<script>
  import {
    getTimeList,
  } from '@/utils/tools'
  import {
    backPress
  } from '@/mixin'
  export default {
    components: {},
    mixins: [backPress],
    data() {
      return {
        config: {
          type: 'dateRange', // 顶部输入框类型
          headerFieldCode: 'orderTime', // 顶部输入框字段
          headerDefaultValue: [new Date().getTime(), new Date().getTime()], // 顶部输入框值
          url: '/api/contract/app-api/purchase/order/detail/page', // 接口请求api
          supplierUrl: '/api/contract/app-api/supplier/order/detail/page', // 供方接口请求api
          fields: [ // 更多查询字段
            {
              fieldCode: 'orderCode',
              fieldName: '订单号',
            },
            {
              fieldCode: 'buyerOrgCode',
              fieldName: '采购组',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/business/group/paged-query',
              fields: {
                text: 'groupCode_groupName',
                value: 'groupCode'
              },
              queryKey: 'buyerGroup' // 查询关键字
            },
            {
              fieldCode: 'supplierCode',
              fieldName: '供应商编码',
            },
            {
              fieldCode: 'supplierName',
              fieldName: '供应商名称',
            },
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode'
              },
              queryKey: 'site' // 查询关键字
            },
            {
              fieldCode: 'itemCode',
              fieldName: '物料编码',
            },
            {
              fieldCode: 'orderTime',
              fieldName: '订单日期',
              type: 'dateRange',
            },
          ],
          statusFieldCode: 'status', // 默认status, 后端约定字段
          statusDefaultValue: 0, // 默认选中状态
          tabs: [{
              tabCode: 0,
              tabName: '待发布'
            }, {
              tabCode: 1,
              tabName: '待反馈'
            },
            {
              tabCode: 2,
              tabName: '已反馈'
            },
            {
              tabCode: 3,
              tabName: '超时未反馈'
            },
            {
              tabCode: 'all',
              tabName: '全部'
            }
          ],
          params: {}
        },
        cardConfig: {
          businessModuleCode: '5',
          header: { // 头信息配置
            icon: 'title',
            fieldCode: 'orderCode_itemNo',
            statusCode: 'statusName',
            statusColorKey: 'status',
            statusColorMap: { //此处是状态和颜色背景颜色的映射值
              0: '#FFA105', // 待发布
              1: '#3979F9', // 待反馈
              2: '#2CDC9B', // 已反馈
              3: '#F55448', // 超时未反馈
              '-99': '#3979F9', // 未知
            }
          },
          simple: [ //简述信息配置
            {
              icon: 'gsgccgz',
              fieldCode: 'companyCode_siteCode_buyerOrgCode'
            },
            {
              icon: 'supplier',
              fieldCode: 'supplierCode_supplierShortName',
              join: '-',
              right: true,
            },
            {
              icon: 'item',
              fieldCode: 'itemCode',
              width: '60%'
            },
            {
              icon: 'number',
              fieldCode: 'quantity_preDeliveryQty',
              right: true,
              width: '40%'
            },
          ],
          detail: [ // 详情信息配置
            {
              fieldCode: 'buyerOrgCode_buyerOrgName',
              fieldName: this.$tl('采购组')
            },
            {
              fieldCode: 'buyerUserCode_buyerUserName',
              fieldName: this.$tl('采购员')
            },
            {
              fieldCode: 'supplierCode_supplierName',
              fieldName: this.$tl('供应商'),
            },
            {
              fieldCode: 'deliveryStatusName',
              fieldName: this.$tl('发货状态')
            },
            {
              fieldCode: 'warehouseStatusName',
              fieldName: this.$tl('入库状态')
            },
            {
              fieldCode: 'itemName',
              fieldName: this.$tl('物料名称')
            },
            {
              fieldCode: 'quantity',
              fieldName: this.$tl('订单数量')
            },
            {
              fieldCode: 'supplierPromiseQty',
              fieldName: this.$tl('承诺数量')
            },
            {
              fieldCode: 'preDeliveryQty',
              fieldName: this.$tl('待发货数量')
            },
            {
              fieldCode: 'transitQty',
              fieldName: this.$tl('在途数量')
            },
            {
              fieldCode: 'deliveryQty',
              fieldName: this.$tl('已发货数量')
            },
            {
              fieldCode: 'warehouseQty',
              fieldName: this.$tl('已入库数量')
            },
            {
              fieldCode: 'requiredDeliveryDate',
              fieldName: this.$tl('要求交期')
            },
            {
              fieldCode: 'warehouseCode_warehouse',
              fieldName: this.$tl('库存地点')
            },
            {
              fieldCode: 'workOrderRel',
              fieldName: this.$tl('关联工单号')
            },
            {
              fieldCode: 'siteCode_siteName',
              fieldName: this.$tl('工厂')
            },
            {
              fieldCode: 'categoryCode_categoryName',
              fieldName: this.$tl('品类')
            },
            {
              fieldCode: 'publishTime',
              fieldName: this.$tl('发布时间')
            },
            {
              fieldCode: 'timePromise',
              fieldName: this.$tl('承诺日期')
            },
            {
              fieldCode: 'domesticDemandFlagName',
              fieldName: this.$tl('是否内需跟单')
            },
            {
              fieldCode: 'domesticDemandCode',
              fieldName: this.$tl('内需单号')
            },
            {
              fieldCode: 'customerOrder_customerOrderLineNo',
              fieldName: this.$tl('销售订单号/行号')
            },
            {
              fieldCode: 'orderTypeName',
              fieldName: this.$tl('订单类型')
            },
            {
              fieldCode: 'orderTime',
              fieldName: this.$tl('订单日期')
            },
            {
              fieldCode: 'purUnitName',
              fieldName: this.$tl('单位')
            },
            {
              fieldCode: 'currencyCode_currencyName',
              fieldName: this.$tl('订单币种')
            },
            {
              fieldCode: 'returnIdentificationName',
              fieldName: this.$tl('退货标识')
            },
            {
              fieldCode: 'deliveryCompletedFlag',
              fieldName: this.$tl('交货已完成标识')
            },
            {
              fieldCode: 'receiveStatusName',
              fieldName: this.$tl('收货状态')
            },
            {
              fieldCode: 'sourceName',
              fieldName: this.$tl('订单来源')
            },
            {
              fieldCode: 'companyCode_companyName',
              fieldName: this.$tl('公司'),
            },
            {
              fieldCode: 'voucherDate',
              fieldName: this.$tl('凭证日期')
            }
          ],
          footer: { // 列表页 footer
            sendBtnTabsArr: [3],
            waitTabsArr: [0, 1, 3],
            sendUrl: '/api/contract/app-api/purchase/order/detail/time/out/remind',
            sendParams: ['ids'],
            waitCode: 'publishTime'
          },
          detailFooter: { // 详情页 footer
            sendBtnTabsArr: [3],
            waitTabsArr: [0, 1, 3],
            sendUrl: '/api/contract/app-api/purchase/order/detail/time/out/remind',
            sendParams: ['ids'],
            hasTips: true,
            waitCode: 'publishTime'
          }
        },
      }
    },

    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },
    mounted() {},
    methods: {

    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }
</style>
