<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('库存情况')" :config="config" :cardConfig="cardConfig" :rightIcon="rightIcon"
    @clickRight="clickRight" :placeholder="$tl('请输入物料编码')" @search="search">
  </srm-search-page>
</template>

<script>
  import {
    getTimeList,
  } from '@/utils/tools'
  import http from '@/utils/http'
  import {
    mapGetters,
  } from 'vuex'
  import { backPress } from '@/mixin'
  export default {
    components: {},
    mixins: [backPress],
    data() {
      return {
        config: {
          defaultNotQuery: true, // 没有查询条件不查询
          type: 'input', // 顶部输入框类型
          headerFieldCode: 'itemCode', // 顶部输入框字段
          headerDefaultValue: '', // 顶部输入框值
          url: '/api/contract/app-api/purchase/tenant/vmiStockMonitor/queryPage', // 接口请求api
          supplierUrl: '/api/contract/app-api/supplier/tenant/vmiStockMonitor/queryPage', // 供方接口api
          fields: [ // 更多查询字段
            {
              fieldCode: 'itemName',
              fieldName: '物料名称',
            },
            {
              fieldCode: 'buyerOrgCode',
              fieldName: '采购组',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/business/group/paged-query',
              fields: {
                text: 'groupCode_groupName',
                value: 'groupCode'
              },
              queryKey: 'buyerGroup' // 查询关键字
            },
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode'
              },
              queryKey: 'site' // 查询关键字
            },
            {
              fieldCode: 'supplierCode',
              fieldName: '供应商编码',
            },
            {
              fieldCode: 'supplierName',
              fieldName: '供应商名称',
            },
          ],
          statusFieldCode: 'dangerLevel', // 默认status, 后端约定字段
          statusDefaultValue: 0, // 默认选中状态
          tabs: [{
              tabCode: 4,
              tabName: '高风险'
            },
            {
              tabCode: 3,
              tabName: '中风险'
            },
            {
              tabCode: 2,
              tabName: '低风险'
            },
            {
              tabCode: 1,
              tabName: '满足'
            },
            {
              tabCode: 0,
              tabName: '全部'
            }, // key: 查询时候提交字段，default: status  all:定义传''
          ],
          params: {}
        },
        cardConfig: {
          businessModuleCode: '1',
          header: { // 头信息配置
            icon: 'inventory',
            fieldCode: 'itemCode',
            statusType: 'text', // text: 直接显示字体，默认显示背景
            statusCode: 'stockAchieveRate',
            statusColorKey: 'dangerLevel',
            statusColorMap: { //此处是状态和颜色背景颜色的映射值
              1: '#2CDC9B', // 满足
              2: '#FFFFFF', // 低风险
              3: '#FFA105', // 中风险
              4: '#F55448', // 高风险
            }
          },
          simple: [ //简述信息配置
            {
              icon: 'item',
              fieldCode: 'itemName',
              width: '100%'
            },
            {
              icon: 'gsgccgz',
              fieldCode: 'siteCode',
              width: '32%'
            },
            {
              icon: 'supplier',
              fieldCode: 'supplierCode_supplierShortName',
              width: '68%',
              join:'-',
              right: true,
            }
          ],
          detail: [ // 详情信息配置
            {
              fieldCode: 'purOrgCode_purOrgName',
              fieldName: this.$tl('采购组')
            },
            {
              fieldCode: 'maxDeliveryDate',
              fieldName: this.$tl('最大交付日期')
            },
            {
              fieldCode: 'periodDeliveryQty',
              fieldName: this.$tl('期间交付量')
            },
            {
              fieldCode: 'safeStockRate',
              fieldName: this.$tl('安全库存占比率')
            },
            {
              fieldCode: 'safeInvQty',
              fieldName: this.$tl('安全库存量'),
            },
            {
              fieldCode: 'stockQty',
              fieldName: this.$tl('库存总量')
            },
            {
              fieldCode: 'diffQty',
              fieldName: this.$tl('差异量')
            },
            {
              fieldCode: 'stockAchieveRate',
              fieldName: this.$tl('备货达成率')
            },
            {
              fieldCode: 'vmiInvQty',
              fieldName: this.$tl('VMI库存')
            },
            {
              fieldCode: 'onwayQty',
              fieldName: this.$tl('在途(直送本厂+VMI送本厂)')
            },
            {
              fieldCode: 'vmiOnwayQty',
              fieldName: this.$tl('VMI在途(供方送VMI)')
            },
            {
              fieldCode: 'stockSite',
              fieldName: this.$tl('原厂库存')
            },
            {
              fieldCode: 'platform',
              fieldName: this.$tl('平台')
            },
            {
              fieldCode: 'updateDate',
              fieldName: this.$tl('更新日期')
            },
            {
              fieldCode: 'unclearPoQty',
              fieldName: this.$tl('未清PO')
            },
            {
              fieldCode: 'planOrder',
              fieldName: this.$tl('计划订单')
            },
            {
              fieldCode: 'supplierCode_supplierName',
              fieldName: this.$tl('供应商')
            },
            {
              fieldCode: 'siteCode_siteName',
              fieldName: this.$tl('工厂')
            },
            {
              fieldCode: 'categoryCode_categoryName',
              fieldName: this.$tl('品类')
            }
          ],
          footer: { // 列表页 footer
            sendBtnTabsArr: [2, 3, 4], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/tenant/vmiStockMonitor/sendNotice',
            sendParams: ['ids'],
          },
          detailFooter: { // 详情页 footer
            sendBtnTabsArr: [2, 3, 4], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/tenant/vmiStockMonitor/sendNotice',
            sendParams: ['ids'],
            hasTips: true,
          }
        },
      }
    },
   computed: {
     ...mapGetters('user', {
       role: 'role',
     }),
     rightIcon() {
       return this.role === 1 ? 'refreshempty' : ''
     }
   },
    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },
    mounted() {},
    methods: {
      async clickRight() {
        const res = await http.get(`/api/contract/app-api/purchase/calculateVmiStockMonitor`)
        if (res.code === 200) {
         this.$message.success(res.msg)
        }
      }
    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }
</style>
