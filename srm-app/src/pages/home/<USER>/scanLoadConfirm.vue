<template>
  <view class="fillinfo-container">
    <!-- <uni-nav-bar left-icon="left" :title="$t('高级搜索')" @clickLeft="handleBack" /> -->
    <!-- 此处后续封装成组件 -->
    <view class="info-container">
      <view class="info-item">
        <text class="label">{{$tl('车牌号/车型/空车重（KG）:')}}</text>
        <text class="text">{{detailInfo.vehicleNo}}/{{detailInfo.truckType}}/{{detailInfo.vehicleWeight}}</text>
      </view>
      <view class="info-item">
        <text class="label">{{$tl('司机姓名/司机手机号:')}}</text>
        <text class="text">{{detailInfo.driverName}}/{{detailInfo.driverMobileNo}}</text>
      </view>
      <view class="info-item">
        <text class="label">{{$tl('工厂:')}}</text>
        <text class="text">{{detailInfo.siteCode}}</text>
      </view>
      <view class="info-item">
        <text class="label">{{$tl('物料:')}}</text>
        <text class="text">{{detailInfo.itemCodes}}</text>
      </view>
    </view>
    <view class="form-container">
      <view class="form-item flex-aitem-center">
        <text class="label">{{$tl('装箱条码')}}</text>
        <view class="item">
          <uni-easyinput class="input-item" v-model="formData.barcodeCode" placeholder="请输入"
            placeholderStyle="color: #999999;font-size:30rpx;" :inputBorder="false"
            @confirm="handleEnter"></uni-easyinput>
          <uni-icons type="scan" size="28" style="margin-left: 20rpx" @click="handleScanCode"></uni-icons>
        </view>
      </view>
      <view class="form-item flex-aitem-center">
        <text class="label">{{$tl('卡板数量')}}<text class="icon-required">*</text></text>
        <view class="item card-item">
          <uni-easyinput class="input-item" v-model="formData.cardBoardQty" placeholder="请输入"
            placeholderStyle="color: #999999;font-size:30rpx;" :inputBorder="false" type="number"
            @focus="handleFocus"></uni-easyinput>
          <text class="error" v-show="showError">{{$tl('请输入卡板数量')}}</text>
        </view>
      </view>
    </view>
    <view class="item-info">
      <view class="info-title">数据信息<text class="tips">已扫描 <text class="bold">{{scanedNum}}</text>张，共计 <text
            class="bold">{{totalNum}}</text>数量</text></view>
      <view class="info-table-container">
        <srm-table :data='loadingVehicleList' :columns='columns' @dele="dele"></srm-table>
      </view>

    </view>
    <view class="btn-group">
      <view class="btn-item" @click="handleBack">{{$tl('返回')}}</view>
      <view class="btn-item primary" @click="handleConfirm">{{$tl('提交')}}</view>
    </view>
  </view>
</template>

<script>
  import http from '@/utils/http'
  import dayjs from '@/utils/dayjs.js'
  import storage from '@/utils/storage'
  import {
    formatDatetime,
  } from '@/utils/tools'
  import {
    isEmpty,
    cloneDeep
  } from 'lodash'
  export default {
    components: {},
    data() {
      return {
        detailInfo: {},
        formData: {},
        scanLoadData: null,
        loadingVehicleList: [], //数据明细行
        list: [],
        tableData: [],
        columns: [{
            name: 'index',
            label: '序号',
            width: 60,
          },
          {
            name: 'operation',
            type: 'operation',
            label: '操作',
            renders: [{
              name: '删除',
              type: 'warn',
              func: "dele"
            }, ]
          },
          {
            name: 'boxId',
            label: 'BOXID',
            width: 150,
          },
          {
            name: 'materialCode',
            label: '物料编码',
            width: 150,
          },
          {
            name: 'materialName',
            label: '物料名称',
            width: 150,
          },
          {
            name: 'packingQuantity',
            label: '装箱数量',
            width: 100,
          },
          {
            name: 'boxBodyLength',
            label: '长(CM)',
            width: 100,
          },
          {
            name: 'boxBodyWide',
            label: '宽(CM)',
            width: 100,
          },
          {
            name: 'boxBodyHeight',
            label: '高(CM)',
            width: 100,
          },
          {
            name: 'grossWeight',
            label: '毛重(KG)',
            width: 100,
          },
          {
            name: 'netWeight',
            label: '净重(KG)',
            width: 100,
          },
          {
            name: 'saleOrderCode',
            label: '销售订单号',
            width: 100,
          },
          {
            name: 'barcodeLevel',
            label: '条码层级',
            width: 100,
          },
          {
            name: 'produceDate',
            label: '生产日期'
          },
        ],
        showError: false,
        totalNum: 0
      }
    },
    props: {
      /**
       * @description 查询字段
       * */
      fields: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      scanedNum() {
        return this.loadingVehicleList?.length
      }
    },
    watch: {

    },
    onLoad() {
      this.scanLoadData = storage.getItemSync('scanLoadData')
      this.initData()
    },
    methods: {
      initData() {
        const {
          driverMobileNo,
          driverName,
          truckType,
          vehicleNo,
          vehicleWeight,
          list
        } = this.scanLoadData
        this.list = [...list]
        const siteCode = list[0].siteCode
        const itemCodeList = [...new Set(list.map(item => item.itemCode))];
        const itemCodes = itemCodeList.join(", ");

        this.detailInfo = {
          driverMobileNo,
          driverName,
          truckType,
          vehicleNo,
          vehicleWeight,
          siteCode,
          itemCodes
        }
      },
      dele(e) {
        this.loadingVehicleList = this.loadingVehicleList.filter(item => item.id !== e.id)
        this.totalNum = 0
        this.loadingVehicleList.forEach((item, index) => {
          this.totalNum += item.packingQuantity
        })
      },
      handleFocus() {
        this.showError = false
      },
      // back
      handleBack() {
        uni.navigateTo({
          url: `/pages/home/<USER>/scanLoadFillInfo`,
        })
      },
      async handleEnter(e) {
        const params = {
          barcodeCode: e,
          loadingVehicleList: this.loadingVehicleList,
          jitPlanIdList: this.list.map(item => item.id)
        }
        const res = await http.post(
          `/api/srm-purchase-execute/app-api/supplier/tv-qbj/loading-vehicle/scanLoadingVehicle`, {
            ...params
          }, {
            showLoading: false
          }).catch(err => {
          console.log(err)
        });
        if (res.code === 200) {
          this.loadingVehicleList.unshift({
            ...res.data
          })
          this.totalNum = 0
          this.loadingVehicleList = this.loadingVehicleList.map((item, index) => {
            this.totalNum += item.packingQuantity
            return {
              ...item,
              index: index + 1
            }
          })
          this.formData.barcodeCode = ''
        }
      },
      async handleConfirm() {
        const {
          driverMobileNo,
          driverName,
          truckType,
          vehicleNo,
          vehicleWeight,
        } = this.scanLoadData
        if (!this.formData.cardBoardQty) {
          this.showError = true
          return
        }
        if (!this.loadingVehicleList?.length) {
          uni.showToast({
            title: '数据不能为空',
            duration: 1500,
            icon: 'none',
            mask: false,
          })
          return
        }
        const params = {
          barcodeCode: this.formData.barcodeCode,
          driverMobileNo,
          driverName,
          truckType,
          vehicleNo,
          vehicleWeight,
          cardBoardQty: this.formData.cardBoardQty,
          loadingVehicleList: this.loadingVehicleList,
          jitPlanIdList: this.list.map(item => item.id)
        }
        const res = await http.post(
          `/api/srm-purchase-execute/app-api/supplier/tv-qbj/loading-vehicle/submitLoadingVehicle`, {
            ...params
          }, {
            showLoading: false
          }).catch(err => {
          console.log(err)
        });
        if (res.code === 200) {
          storage.removeItemAsync('scanLoadData')
          uni.navigateTo({
            url: `/pages/home/<USER>/supplySchedule`,
          })
        }
      },
      handleScanCode() {
        uni.scanCode({
          success: (res) => {
            this.handleEnter()
          },
          // 扫码失败的回调函数
          fail: (err) => {
            console.log('扫描失败：', err);
          }
        });

      }
    },
  }
</script>
<style lang="scss" scope;>
  .form-container {
    background: #ffffff;
    padding: 0 32rpx;
    margin-top: 16rpx;

    .form-item {
      height: 120rpx;

      .label {
        font-size: 30rpx;
        color: #333333;

        .icon-required {
          margin-left: 4rpx;
          color: #ff0000
        }
      }

      .item {
        display: flex;
        flex: 1;
        align-items: center;
        margin-left: auto;

        &.card-item {
          flex-direction: column;
        }

        .input-item {
          text-align: right;
          width: 100%;
        }

        .error {
          width: 100%;
          text-align: right;
          color: red;
          font-size: 30rpx;
        }

        .custom-select {
          width: 100%;
          text-align: right;
          display: -webkit-box;
          /* 使用Webkit的盒子模型 */
          -webkit-line-clamp: 2;
          /* 限制最多显示2行 */
          -webkit-box-orient: vertical;
          /* 设置盒子内容的排列方向为垂直 */
          overflow: hidden;
          /* 隐藏超出容器的内容 */
          text-overflow: ellipsis;
          font-size: 32rpx;
        }

        .input-calendar {
          text-align: right;
        }

        .custom-select-placeholder {
          color: #999999;
        }
      }

      .icon-right {
        margin-left: 16rpx;
        width: 24rpx;
        height: 24rpx;
        margin-top: 5rpx;
      }
    }
  }

  .btn-group {
    position: fixed;
    display: flex;
    justify-content: space-between;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #ffffff;
    padding: 32rpx;

    .btn-item {
      width: 320rpx;
      height: 80rpx;
      text-align: center;
      line-height: 80rpx;
      border-radius: 80rpx;
      border: 1px solid #4E5A70;
      color: #4E5A70;
      font-weight: 500;
      font-size: 32rpx;

      &.primary {
        background: #4E5A70;
        color: #ffffff;
      }
    }
  }

  .item-info {
    background: #ffffff;
    padding: 32rpx 32rpx 32rpx 32rpx;

    .info-title {
      font-weight: 500;
      font-size: 30rpx;
      color: #333333;
      margin-bottom: 24rpx;

      &::before {
        content: '';
        display: inline-block;
        width: 6rpx;
        height: 24rpx;
        background: #3678FE;
        border-radius: 0 100rpx 0 0;
        margin-right: 12rpx;
      }
    }

    .info-table-container {
      width: 720rpx;
      overflow-x: scroll;
      white-space: nowrap;
    }

    .info-table {

      .info-th {
        display: flex;
        white-space: nowrap;
        background: #4E5A70;
        padding: 13rpx 0;

        .info-td {
          flex: 0 0 auto;
          padding-left: 24rpx;
          color: #ffffff;
          font-weight: 500;
          font-size: 26rpx;
          box-sizing: border-box;

          &:nth-of-type(1) {
            width: 432rpx;
          }

          &:nth-of-type(2) {
            width: 145rpx;
          }

          &:nth-of-type(3) {
            width: 120rpx;
          }

          &:nth-of-type(4) {
            width: 200rpx;
          }
        }
      }

      .info-tbody {
        .info-tr {
          display: flex;
          white-space: nowrap;

          .info-td {
            flex: 0 0 auto;
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            padding: 22rpx 0 22rpx 24rpx;

            &:nth-of-type(1) {
              width: 432rpx;
            }

            &:nth-of-type(2) {
              width: 145rpx;
            }

            &:nth-of-type(3) {
              width: 120rpx;
            }

            &:nth-of-type(4) {
              width: 200rpx;
            }
          }

          //     .empty-inner-wrapper {
          //       .empty-txt {
          //         display: inline-block;
          //         width: 100%;
          //         text-align: center;
          //         color: #aaaaaa;
          //         font-size: 28rpx;
          //         margin-top: 10rpx;
          //         padding: 0px 10px;
          //       }
          //     }

        }
      }
    }
  }

  .info-container {
    background: #ffffff;
    padding: 32rpx;
    font-size: 28rpx;
    line-height: 56rpx;

    .label {
      color: #999999;
      margin-right: 8rpx;
    }

    .text {
      color: #333333;
    }
  }

  .tips {
    margin-left: 16rpx;
    font-size: 28rpx;

    .bold {
      font-weight: bold;
      padding: 0 4rpx;
    }
  }
</style>
