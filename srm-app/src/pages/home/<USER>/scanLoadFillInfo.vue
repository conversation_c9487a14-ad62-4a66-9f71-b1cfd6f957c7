<template>
  <!-- 后续统一封装成组件 -->
  <view class="fillinfo-container">
    <uni-nav-bar left-icon="left" :title="$t('扫描装车')" @clickLeft="handleBack" />
    <view class="form-container">
      <view class="form-item flex-aitem-center">
        <text class="label">{{$tl('司机名称')}}<text class="icon-required">*</text></text>
        <view class="item">
          <srm-select v-model="formData.driverName" :fillName.sync="formData.driverName" queryKey="driverName"
            :fields="driverNameFields" method="post"
            url="/api/srm-purchase-execute/app-api/supplier/tv-jit/driverPageQuery" @onSelected="handleDriverSelected"
            @onClear="handleDriverClear" @focus="handleFocus" :disabled="false"></srm-select>
          <text class="error" v-show="errors.driverName">{{$tl('请输入司机名称')}}</text>
        </view>
      </view>
      <view class="form-item flex-aitem-center">
        <text class="label">{{$tl('司机手机号')}}<text class="icon-required">*</text></text>
        <view class="item">
          <uni-easyinput class="input-item" v-model="formData.driverMobileNo" placeholder="请输入"
            placeholderStyle="color: #999999;font-size:32rpx;" :inputBorder="false"
            @focus="handleFocus"></uni-easyinput>
          <text class="error" v-show="errors.driverMobileNo">{{$tl('请输入司机手机号')}}</text>
        </view>
      </view>
      <view class="form-item flex-aitem-center">
        <text class="label">{{$tl('车牌号')}} <text class="icon-required">*</text></text>
        <view class="item">
          <uni-easyinput class="input-item" v-model="formData.vehicleNo" placeholder="请输入"
            placeholderStyle="color: #999999;font-size:32rpx;" :inputBorder="false"
            @focus="handleFocus"></uni-easyinput>
          <text class="error" v-show="errors.vehicleNo">{{$tl('请输入车牌号')}}</text>
        </view>
      </view>
      <view class="form-item flex-aitem-center">
        <text class="label">{{$tl('车型')}} <text class="icon-required">*</text></text>
        <view class="item">
          <uni-easyinput class="input-item" v-model="formData.truckType" placeholder="请输入"
            placeholderStyle="color: #999999;font-size:32rpx;" :inputBorder="false"
            @focus="handleFocus"></uni-easyinput>
          <text class="error" v-show="errors.truckType">{{$tl('请输入车型')}}</text>
        </view>
      </view>
      <view class="form-item flex-aitem-center">
        <text class="label">{{$tl('空车(kg)')}}<text class="icon-required">*</text></text>
        <view class="item">
          <uni-easyinput class="input-item" v-model="formData.vehicleWeight" placeholder="请输入"
            placeholderStyle="color: #999999;font-size:32rpx;" :inputBorder="false" type="number"
            @focus="handleFocus"></uni-easyinput>
          <text class="error" v-show="errors.vehicleWeight">{{$tl('请输入空车(kg)')}}</text>
        </view>
      </view>

    </view>

    <view class="btn-group">
      <view class="btn-item" @click="handleReset">{{$tl('重置')}}</view>
      <view class="btn-item primary" @click="handleConfirm">{{$tl('确认')}}</view>
    </view>
  </view>
</template>

<script>
  import dayjs from '@/utils/dayjs.js'
  import storage from '@/utils/storage'
  import {
    formatDatetime,
  } from '@/utils/tools'
  import {
    isEmpty,
    cloneDeep
  } from 'lodash'
  export default {
    components: {},
    data() {
      return {
        formData: {},
        driverNameFields: {
          text: 'contact',
          value: 'name'
        },
        errors: {},
        scanLoadData: {}
      }
    },
    props: {

    },

    watch: {

    },
   onLoad() {
      this.scanLoadData = storage.getItemSync('scanLoadData')
      this.initData()
    },
    methods: {
      initData() {
        const {
          driverMobileNo,
          driverName,
          truckType,
          vehicleNo,
          vehicleWeight,
        } = this.scanLoadData
        this.formData = {
          driverMobileNo,
          driverName,
          truckType,
          vehicleNo,
          vehicleWeight,
        }
      },
      handleDriverSelected(e) {
        console.log(e)
        const _data = {
          driverName: e.value,
          driverMobileNo: e.data.contact,
          vehicleNo: e.data.license
        }
        this.formData = {
          ...this.formData,
          ..._data
        }
      },
      handleFocus(e) {
        this.errors = {}
      },
      // 清除
      handleDriverClear(e) {
        this.$set(this.formData, 'driverName', '')
      },
      // 重置
      handleReset() {
          this.formData = {}
      },
      validData() {
        const validFields = ['driverName', 'driverMobileNo', 'vehicleNo', 'truckType', 'vehicleWeight']
        let validFlag = true
        validFields.forEach(item => {
          if (!this.formData[item]) {
            this.$set(this.errors, item, true)
            validFlag = false
          } else {
            this.$set(this.errors, item, false)
          }
        })
        return validFlag
      },
      // 确认
      handleConfirm() {
        const validFlag = this.validData()
        if (!validFlag) return
        const scanLoadData = storage.getItemSync('scanLoadData')
        const list = {
          ...scanLoadData,
          ...this.formData
        }
        storage.setItemSync('scanLoadData', list)

        uni.navigateTo({
          url: `/pages/home/<USER>/scanLoadConfirm`,
        })
      },
      // back
      handleBack() {
        uni.navigateTo({
          url: `/pages/home/<USER>/supplySchedule`,
        })
      },
    },
  }
</script>
<style lang="scss" scope;>
  .form-container {
    background: #ffffff;
    padding: 0 32rpx;
    height: calc(100vh - 80px);
    overflow-y: auto;

    .form-item {
      height: 120rpx;

      .label {
        font-size: 32rpx;
        color: #333333;

        .icon-required {
          margin-left: 4rpx;
          color: #ff0000
        }
      }

      .item {
        display: flex;
        flex: 1;
        align-items: center;
        margin-left: auto;
        flex-direction: column;

        .input-item {
          text-align: right;
          width: 100%;
        }

        .error {
          width: 100%;
          text-align: right;
          color: red;
          font-size: 30rpx;
        }

        .custom-select {
          width: 100%;
          text-align: right;
          display: -webkit-box;
          /* 使用Webkit的盒子模型 */
          -webkit-line-clamp: 2;
          /* 限制最多显示2行 */
          -webkit-box-orient: vertical;
          /* 设置盒子内容的排列方向为垂直 */
          overflow: hidden;
          /* 隐藏超出容器的内容 */
          text-overflow: ellipsis;
          font-size: 32rpx;
        }

        .input-calendar {
          text-align: right;
        }

        .custom-select-placeholder {
          color: #999999;
        }
      }

      .icon-right {
        margin-left: 16rpx;
        width: 24rpx;
        height: 24rpx;
        margin-top: 5rpx;
      }
    }
  }

  .btn-group {
    position: fixed;
    display: flex;
    justify-content: space-between;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #ffffff;
    padding: 32rpx;

    .btn-item {
      width: 320rpx;
      height: 80rpx;
      text-align: center;
      line-height: 80rpx;
      border-radius: 80rpx;
      border: 1px solid #4E5A70;
      color: #4E5A70;
      font-weight: 500;
      font-size: 32rpx;

      &.primary {
        background: #4E5A70;
        color: #ffffff;
      }
    }
  }
</style>
