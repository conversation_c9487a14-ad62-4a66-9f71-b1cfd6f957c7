<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('叫料计划')" :config="config" :cardConfig="cardConfig"
    @search="search"></srm-search-page>
</template>

<script>
  import {
    getTimeList,
  } from '@/utils/tools'
  import {
    backPress
  } from '@/mixin'
  export default {
    components: {},
    mixins: [backPress],
    data() {
      return {
        appDeliveryType: '',
        account: '',
        config: {
          type: 'dateRange', // 顶部输入框类型
          headerFieldCode: 'deliveryDate', // 顶部输入框字段
          headerDefaultValue: [new Date().getTime(), new Date().getTime()], // 顶部输入框值
          url: '/api/contract/app-api/purchase/jit/info/page', // 接口请求api
          supplierUrl: '/api/contract/app-api/supplier/jit/info/page', // 供方接口请求api
          fields: [ // 更多查询字段
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode'
              },
              queryKey: 'site' // 查询关键字
            },
            {
              fieldCode: 'status',
              type: 'multiSelect',
              fieldName: '状态',
              showSearch: false,
              dataSource: [{
                  label: '新建',
                  value: '1'
                },
                {
                  label: '已转交',
                  value: '2'
                },
                {
                  label: '待反馈',
                  value: '3'
                },
                {
                  label: '反馈异常',
                  value: '4'
                },
                {
                  label: '反馈正常',
                  value: '5'
                },
                {
                  label: '已关闭',
                  value: '6'
                },
              ]
            },
            {
              fieldCode: 'productLine',
              fieldName: '生产线 ',
            },
            {
              fieldCode: 'workOrder',
              fieldName: '工单',
            },
            {
              fieldCode: 'buyerOrgCode',
              fieldName: '采购组',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/business/group/paged-query',
              fields: {
                text: 'groupCode_groupName',
                value: 'groupCode'
              },
              queryKey: 'buyerGroup' // 查询关键字
            },
            {
              fieldCode: 'itemCode',
              fieldName: '物料',
            },
            {
              fieldCode: 'supplierCode',
              fieldName: '供应商编码',
            },
            {
              fieldCode: 'supplierName',
              fieldName: '供应商名称',
            },
            {
              fieldCode: 'subSiteCode',
              fieldName: '分厂',
              type: 'multiSelect',
              url: '/api/contract/app-api/public-api/site/page/subSite',
              fields: {
                text: 'subSiteCode_subSiteName',
                value: 'subSiteCode'
              },
              queryKey: 'subSite' // 查询关键字
            },
            {
              fieldCode: 'deliveryNum',
              type: 'number',
              fieldName: '在途数量',
            },
            {
              fieldCode: 'remainingDeliveryNum',
              type: 'number',
              fieldName: '剩余可创建数量 ',
            },
            {
              fieldCode: 'createTime',
              fieldName: '创建时间',
              type: 'dateRange'
            },
            {
              fieldCode: 'createUserName',
              fieldName: '创建人',
            },
            {
              fieldCode: 'rowCode',
              fieldName: 'JIT单号',
            },
            {
              fieldCode: 'deliveryDate',
              fieldName: '交货日期',
              type: 'dateRange'
            },
          ],
          statusFieldCode: 'queryType', // 默认status, 后端约定字段
          statusDefaultValue: 4, // 默认选中状态
          tabs: [{
              tabCode: 1,
              tabName: '反馈正常'
            },
            {
              tabCode: 3,
              tabName: '发货异常'
            },
            {
              tabCode: 4,
              tabName: '全部'
            },

          ]
        },
        cardConfig: {
          businessModuleCode: '3',
          header: { // 头信息配置
            icon: 'shd',
            fieldCode: 'rowCode',
            statusCode: 'statusName',
            statusColorKey: 'status',
            statusColorMap: { //此处是状态和颜色背景颜色的映射值
              5: '#3979F9', // 反馈正常
              6: '#F55448', // 关闭
            }
          },
          simple: [ //简述信息配置
            {
              icon: 'gsgccgz',
              fieldCode: 'companyCode_siteCode_buyerOrgCode'
            },
            {
              icon: 'item',
              fieldCode: 'itemCode',
              right: true
            },
            {
              icon: 'number',
              fieldCode: 'bidNum_remainingDeliveryNum',
              text: this.$tl('需/剩：'),
              width: '45%',
            },
            {
              icon: 'location',
              fieldCode: 'subSiteName_subSiteAddressCode',
              right: true,
            },
            {
              icon: 'supplier',
              fieldCode: 'supplierCode_supplierShortName',
              join: '-',
            },
            {
              icon: 'scx',
              fieldCode: 'productLine_workOrder',
              width: '74%',
            },
            {
              icon: 'time',
              fieldCode: 'deliveryDate',
              width: '80%'
            },
          ],
          detail: [ // 详情信息配置
            {
              fieldCode: 'buyerOrgName',
              fieldName: this.$tl('采购组名称'),
              join: '/'
            },
            {
              fieldCode: 'supplierCode_supplierName',
              fieldName: this.$tl('供应商名称')
            },
            {
              fieldCode: 'itemName',
              fieldName: this.$tl('物料名称')
            },
            {
              fieldCode: 'deliveryDate',
              fieldName: this.$tl('交货日期')
            },
            {
              fieldCode: 'bidNum',
              fieldName: this.$tl('需求数量')
            },
            {
              fieldCode: 'deliveryNum',
              fieldName: this.$tl('在途数量')
            },
            {
              fieldCode: 'remainingDeliveryNum',
              fieldName: this.$tl('剩余送货数量')
            },
            {
              fieldCode: 'receiveNum',
              fieldName: this.$tl('收货数量')
            },
            {
              fieldCode: 'warehouseName',
              fieldName: this.$tl('库存地点名称')
            },
            {
              fieldCode: 'createUserName',
              fieldName: this.$tl('创建人')
            },
            {
              fieldCode: 'createTime',
              fieldName: this.$tl('创建时间')
            },
            {
              fieldCode: 'renewTime',
              fieldName: this.$tl('JIT更新时间')
            },
            {
              fieldCode: 'siteName',
              fieldName: this.$tl('工厂名称')
            },
            {
              fieldCode: 'vmiWarehouseTypeName',
              fieldName: this.$tl('VMI仓类型')
            }
          ],
          footer: { // 列表页 footer
            sendBtnTabsArr: [2, 3], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/jit/info/time/out/remind',
            sendParams: ['ids', 'queryType'],
          },
          detailFooter: { // 详情页 footer
            sendBtnTabsArr: [2, 3], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/jit/info/time/out/remind',
            sendParams: ['ids', 'queryType'],
            hasTips: true,
          }
        },
      }
    },

    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },
    mounted() {},
    methods: {

    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }
</style>
