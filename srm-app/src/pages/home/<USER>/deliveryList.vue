<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('送货单列表')" :config="config" :cardConfig="cardConfig"
    @search="search"></srm-search-page>
</template>

<script>
  import {
    getTimeList,
  } from '@/utils/tools'
  import {
    backPress
  } from '@/mixin'
  export default {
    components: {},
    mixins: [backPress],
    data() {
      return {
        appDeliveryType: '',
        account: '',
      }
    },
    computed: {
      cardConfig() {
        return {
          businessModuleCode: '4',
          header: { // 头信息配置
            icon: 'shd',
            fieldCode: 'deliveryCode_deliveryLineNo',
            statusCode: 'statusName',
            statusColorKey: 'status',
            statusColorMap: ['0', '1'].includes(this.appDeliveryType) ? { //此处是状态和颜色背景颜色的映射值
              1: '#3979F9', // 新建
              2: '#3979F9', // 发货中
              3: '#2CDC9B', // 已完成
              4: '#999999', // 已取消
              5: '#F55448', // 已关闭
            } : {
              0: '#3979F9', // 新建
              1: '#3979F9', // 待确认
              2: '#3979F9', // 已接收
              3: '#2CDC9B', // 已完成
              8: '#2CDC9B', // 已完成
              9: '#999999', // 已取消
            }
          },
          simple: [ //简述信息配置
            {
              icon: 'gsgccgz',
              fieldCode: 'companyCode_siteCode_buyerOrgCode',
              width: '70%'
            },
            {
              type: 'bg',
              width: '30%',
              right: true,
              fieldCode: 'onWayStatusName',
              colorKey: 'onWayStatus',
              colorMap: {
                0: { // 未出发
                  text: '#FCBA16',
                  bg: 'rgba(252, 186, 22, 0.1)'
                },
                1: { // 已入园
                  text: '#3979F9',
                  bg: 'rgba(57, 121, 249, 0.1)'
                },
                2: { // 已出发
                  text: '#3979F9',
                  bg: 'rgba(57, 121, 249, 0.1)'
                },
                3: { // 已报道
                  text: '#3979F9',
                  bg: 'rgba(57, 121, 249, 0.1)'
                },
                4: { // 已取消
                  text: '#999999',
                  bg: 'rgba(153, 153, 153, 0.1)'
                },
                5: { // 已关闭
                  text: '#F55448',
                  bg: 'rgba(245, 84, 72, 0.1)'
                },
                6: { // 已完成
                  text: '#2CDB9B',
                  bg: 'rgba(44, 219, 155, 0.1)'
                },
                7: { // 已到厂待卸货
                  text: '#3979F9',
                  bg: 'rgba(57, 121, 249, 0.1)'
                },
                8: { // 已卸货待报检
                  text: '#3979F9',
                  bg: 'rgba(57, 121, 249, 0.1)'
                },
                11: { // 保安门岗确认
                  text: '#3979F9',
                  bg: 'rgba(57, 121, 249, 0.1)'
                },
                12: { // 已离园
                  text: '#3979F9',
                  bg: 'rgba(57, 121, 249, 0.1)'
                }
              }
            }, //默认配置icon，如果是背景色type:bg
            {
              icon: 'item',
              fieldCode: 'itemCode'
            },
            {
              icon: 'car',
              fieldCode: 'carNo',
              right: true
            },
            {
              icon: 'number',
              fieldCode: 'deliveryQuantity',
              width: '32%'
            },
            {
              icon: 'supplier',
              fieldCode: 'supplierCode_supplierShortName',
              join: '-',
              right: true,
              width: '68%'
            },
            {
              icon: 'time',
              fieldCode: 'demandDate_demandTime',
              join: ' ',
              width: '100%'
            },
          ],
          detail: [ // 详情信息配置
            {
              fieldCode: 'buyerOrgCode_buyerOrgName',
              fieldName: this.$tl('采购组')
            },
            {
              fieldCode: 'supplierCode_supplierName',
              fieldName: this.$tl('供应商')
            },
            {
              fieldCode: 'itemName',
              fieldName: this.$tl('物料名称')
            },
            {
              fieldCode: 'demandDate_demandTime',
              join: ' ',
              fieldName: this.$tl('需求时间')
            },
            {
              fieldCode: 'deliveryQuantity',
              fieldName: this.$tl('本次送货数量')
            },
            {
              fieldCode: 'receiveQuantity',
              fieldName: this.$tl('收货数量'),
            },
            {
              fieldCode: 'workOrderNo',
              fieldName: this.$tl('关联工单号')
            },
            {
              fieldCode: 'warehouseCode_warehouseName',
              fieldName: this.$tl('交货库存地点')
            },
            {
              fieldCode: 'productLine',
              fieldName: this.$tl('生产线')
            },
            {
              fieldCode: 'createTime',
              fieldName: this.$tl('创建时间')
            },
            {
              fieldCode: 'forecastArriveTime',
              fieldName: this.$tl('预计到货日期')
            },
            {
              fieldCode: 'jitDeliveryNumber',
              fieldName: this.$tl('JIT编码号')
            },
            {
              fieldCode: 'vmiWarehouseName',
              fieldName: this.$tl('VMI仓库')
            },
            {
              fieldCode: 'siteCode_siteName',
              fieldName: this.$tl('工厂')
            },
            {
              fieldCode: 'jitName',
              fieldName: this.$tl('是否JIT')
            },
            {
              fieldCode: 'domesticDemandFlag',
              fieldName: this.$tl('是否内需跟单')
            },
            {
              fieldCode: 'domesticDemandCode',
              fieldName: this.$tl('内需单号')
            },
            {
              fieldCode: 'saleOrderNo_saleOrderLineNo',
              fieldName: this.$tl('销售订单号/行号')
            },
            {
              fieldCode: 'orderCode_lineNo',
              fieldName: this.$tl('采购订单号/行号'),
              join: '/'
            },
            {
              fieldCode: 'outsourcedTypeName',
              fieldName: this.$tl('委外方式')
            },
            {
              fieldCode: 'unitCode_unitName',
              fieldName: this.$tl('单位')
            },
            {
              fieldCode: 'deliveryTypeName',
              fieldName: this.$tl('送货单类型')
            },
            {
              fieldCode: 'cancelPersonName',
              fieldName: this.$tl('取消人')
            },
            {
              fieldCode: 'cancelTime',
              fieldName: this.$tl('取消时间')
            },
            {
              fieldCode: 'closePersonName',
              fieldName: this.$tl('关闭人')
            },
            {
              fieldCode: 'closeTime',
              fieldName: this.$tl('关闭时间')
            },
            {
              fieldCode: 'deliveryNumber',
              fieldName: this.$tl('来源编号')
            },
            {
              fieldCode: 'deliveryNo_deliveryItemNo',
              fieldName: this.$tl('SAP交货单号/行号'),
              join: '/'
            },
            {
              fieldCode: 'companyCode_companyName',
              fieldName: this.$tl('公司')
            }
          ],
          footer: { // 列表页 footer
            sendBtnTabsArr: ['0'], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/delivery/time/out/remind',
            sendParams: ['ids'],
            waitCode: 'onWayUpdateTime'
          },
          detailFooter: { // 详情页 footer
            sendBtnTabsArr: ['0'], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/delivery/time/out/remind',
            sendParams: ['ids'],
            hasTips: true,
            waitCode: 'onWayUpdateTime'
          }
        }
      },

      // config配置
      config() {
        const url = ['0', '1'].includes(this.appDeliveryType) ? '/api/contract/app-api/purchase/delivery/query/page' :
          '/api/contract/app-api/purchase/delivery/vmi/query/page'
        const supplierUrl = ['0', '1'].includes(this.appDeliveryType) ?
          '/api/contract/app-api/supplier/delivery/query/page' :
          '/api/contract/app-api/supplier/delivery/vmi/query/page'
        return {
          type: 'dateRange', // 顶部输入框类型
          headerFieldCode: 'createTime', // 顶部输入框字段
          headerDefaultValue: [new Date().getTime(), new Date().getTime()], // 顶部输入框值
          url, // 接口请求api
          supplierUrl, // 供方接口请求api
          fields: [ // 更多查询字段
            {
              fieldCode: 'status',
              type: 'multiSelect',
              fieldName: '状态',
              showSearch: false,
              dataSource: ['0', '1'].includes(this.appDeliveryType) ? [{
                  label: '新建',
                  value: '1'
                },
                {
                  label: '发货中',
                  value: '2'
                },
                {
                  label: '已完成',
                  value: '3'
                },
                {
                  label: '已取消',
                  value: '4'
                },
                {
                  label: '已关闭',
                  value: '5'
                },
              ] : [{
                  label: '新建',
                  value: '0'
                },
                {
                  label: '待确认',
                  value: '1'
                },
                {
                  label: '已接收',
                  value: '2'
                },
                {
                  label: '已完成',
                  value: '8'
                },
                {
                  label: '已取消',
                  value: '9'
                }
              ]
            },
            {
              fieldCode: 'onWayStatus',
              type: 'multiSelect',
              fieldName: '在途状态',
              showSearch: false,
              dataSource: [{
                  label: '未出发',
                  value: '0'
                },
                {
                  label: '已入园',
                  value: '1'
                },
                {
                  label: '已出发',
                  value: '2'
                },
                {
                  label: '已报到',
                  value: '3'
                },
                {
                  label: '已取消',
                  value: '4'
                },
                {
                  label: '已关闭',
                  value: '5'
                },
                {
                  label: '已完成',
                  value: '6'
                },
                {
                  label: '已到厂待卸货',
                  value: '7'
                },
                {
                  label: '已卸货待报检',
                  value: '8'
                },
                {
                  label: '保安门岗确认',
                  value: '11'
                },
                {
                  label: '已离园',
                  value: '12'
                },
              ]
            },
            {
              fieldCode: 'supplierCode',
              fieldName: '供应商编码',
            },
            {
              fieldCode: 'supplierName',
              fieldName: '供应商名称',
            },
            {
              fieldCode: 'buyerOrgCode',
              fieldName: '采购组',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/business/group/paged-query',
              fields: {
                text: 'groupCode_groupName',
                value: 'groupCode'
              },
              queryKey: 'buyerGroup' // 查询关键字
            },
            {
              fieldCode: 'itemCode',
              fieldName: '物料',
            },
            {
              fieldCode: 'deliveryCode',
              fieldName: '送货单号',
              // defaultValue: 'SRM'
            },
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode'
              },
              queryKey: 'site' // 查询关键字
            },
            {
              fieldCode: 'carNo',
              fieldName: '车牌号',
            },
            {
              fieldCode: 'orderCode',
              fieldName: '订单号',
            },
            {
              fieldCode: 'workOrderNo',
              fieldName: '工单号',
            },
            {
              fieldCode: 'createTime',
              fieldName: '创建时间',
              type: 'dateRange'
            },

          ],
          statusFieldCode: 'onWayStatus', // 默认status, 后端约定字段
          statusDefaultValue: 'all', // 默认选中状态
          tabs: [{
              tabCode: '0',
              tabName: '未发货'
            },
            {
              tabCode: '2',
              tabName: '已出车'
            },
            {
              tabCode: '3',
              tabName: '已报到'
            },
            {
              tabCode: '7',
              tabName: '已到厂待卸货'
            },
            {
              tabCode: 'all',
              tabName: '全部'
            }, // key: 查询时候提交字段，default: status  all:定义传''
          ],
          params: {
            appDeliveryType: Number(this.appDeliveryType)
          }
        }
      }
    },
    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },
    mounted() {},
    methods: {

    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }
</style>
