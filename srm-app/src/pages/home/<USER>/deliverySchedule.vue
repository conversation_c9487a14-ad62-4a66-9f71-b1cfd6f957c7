<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('交货计划')" :config="config" :cardConfig="cardConfig"
    @search="search"></srm-search-page>
</template>

<script>
  import {
    getTimeList,
  } from '@/utils/tools'
  import {
    backPress
  } from '@/mixin'
  export default {
    components: {},
    mixins: [backPress],
    data() {
      return {
        appDeliveryType: '',
        account: '',
        config: {
          type: 'dateRange', // 顶部输入框类型
          headerFieldCode: 'timeInfoTimestamp', // 顶部输入框字段
          headerDefaultValue: [new Date().getTime(), new Date().getTime()], // 顶部输入框值
          url: '/api/contract/app-api/purchase/goods/plan/page', // 接口请求api
          supplierUrl: '/api/contract/app-api/supplier/goods/plan/page', // 接口请求api
          fields: [ // 更多查询字段
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode'
              },
              queryKey: 'site' // 查询关键字
            },
            {
              fieldCode: 'buyerOrgCode',
              fieldName: '采购组',
              type: 'multiSelect',
              url: '/api/masterDataManagement/app-api/public-api/business/group/paged-query',
              fields: {
                text: 'groupCode_groupName',
                value: 'groupCode'
              },
              queryKey: 'buyerGroup' // 查询关键字
            },
            {
              fieldCode: 'itemCode',
              fieldName: '物料',
            },
            {
              fieldCode: 'supplierCode',
              fieldName: '供应商编码',
            },
            {
              fieldCode: 'supplierName',
              fieldName: '供应商名称',
            },
            {
              fieldCode: 'createTime',
              fieldName: '创建时间',
              type: 'dateRange'
            },
            {
              fieldCode: 'serialNumber',
              fieldName: '序列号',
            },
            {
              fieldCode: 'timeInfoTimestamp',
              fieldName: '需求日期',
              type: 'dateRange'
            },
          ],
          statusFieldCode: 'queryType', // 默认status, 后端约定字段
          statusDefaultValue: 5, // 默认选中状态
          tabs: [{
              tabCode: 1,
              tabName: '已发布'
            },
            {
              tabCode: 2,
              tabName: '超时未反馈'
            },
            {
              tabCode: 4,
              tabName: '发货异常'
            },
            {
              tabCode: 6,
              tabName: '物料新增'
            },
            {
              tabCode: 5,
              tabName: '全部'
            }, // key: 查询时候提交字段，default: status  all:定义传''
          ],
          params: {
            // appDeliveryType: Number(this.appDeliveryType)
          }
        },
        cardConfig: {
          businessModuleCode: '2',
          header: { // 头信息配置
            icon: 'shd',
            fieldCode: 'itemCode',
            statusCode: 'statusName',
            statusColorKey: 'status',
            statusColorMap: { //此处是状态和颜色背景颜色的映射值
              0: '#3979F9', // 新建
              1: '#3979F9', // 已修改
              2: '#3979F9', // 已发布
              3: '#2CDC9B', // 反馈正常
              4: '#F55448', // 反馈异常
              5: '#3979F9', // 已确认
              6: '#F55448', // 已关闭
              7: '#3979F9', // 同步中
              '-99': '#999999', // 未知
            }
          },
          simple: [ //简述信息配置
            {
              icon: 'gsgccgz',
              fieldCode: 'companyCode_siteCode_buyerOrgCode'
            },
            {
              icon: 'number',
              text: this.$tl('需/剩：'),
              fieldCode: 'buyerNum_remainingNum',
              right: true,
            },
            {
              icon: 'item',
              fieldCode: 'itemName',
              width: '100%'
            },
            {
              icon: 'number',
              fieldCode: 'serialNumber',
              width: '100%'
            },
            {
              icon: 'time',
              fieldCode: 'timeInfo',
              width: '32%'
            },
            {
              icon: 'supplier',
              fieldCode: 'supplierCode_supplierShortName',
              right: true,
              join: '-',
              width: '68%'
            },
          ],
          detail: [ // 详情信息配置
            {
              fieldCode: 'buyerOrgName',
              fieldName: this.$tl('采购组名称')
            },
            {
              fieldCode: ' itemCode_itemName',
              fieldName: this.$tl('物料名称')
            },
            {
              fieldCode: 'supplierCode_supplierName',
              fieldName: this.$tl('供应商名称')
            },
            {
              fieldCode: 'timeInfo',
              fieldName: this.$tl('需求日期')
            },
            {
              fieldCode: 'total',
              fieldName: this.$tl('D（原始需求）')
            },
            {
              fieldCode: 'buyerNum',
              fieldName: this.$tl('P（需求量）')
            },
            {
              fieldCode: 'supplierNum',
              fieldName: this.$tl('C（承诺量）')
            },
            {
              fieldCode: 'gapNum',
              fieldName: this.$tl('Gap（差额）')
            },
            {
              fieldCode: 'remainingNum',
              fieldName: this.$tl('剩余可创建数量')
            },
            {
              fieldCode: 'outstandingNum',
              fieldName: this.$tl('未清订单数量')
            },
            {
              fieldCode: 'haveDeliveryNum',
              fieldName: this.$tl('已打单数量'),
              join: '/'
            },
            {
              fieldCode: 'warehouseCode_warehouseName',
              fieldName: this.$tl('库存地点')
            },
            {
              fieldCode: 'associatedNumber',
              fieldName: this.$tl('关联工单号')
            },
            {
              fieldCode: 'jitName',
              fieldName: this.$tl('是否JIT')
            },
            {
              fieldCode: 'outsourcedTypeName',
              fieldName: this.$tl('委外方式')
            },
            {
              fieldCode: 'deliveryMethodName',
              fieldName: this.$tl('配送方式')
            },
            {
              fieldCode: 'createUserName',
              fieldName: this.$tl('创建人')
            },
            {
              fieldCode: 'createTime',
              fieldName: this.$tl('创建时间')
            },
            {
              fieldCode: 'updateUserName',
              fieldName: this.$tl('更新人')
            },
            {
              fieldCode: 'updateTime',
              fieldName: this.$tl('更新时间')
            },
            {
              fieldCode: 'domesticDemandFlagName',
              fieldName: this.$tl('是否内需跟单')
            },
            {
              fieldCode: 'domesticDemandCode',
              fieldName: this.$tl('内需单号')
            },
            {
              fieldCode: 'saleOrder_saleOrderRowCode',
              fieldName: this.$tl('销售订单号/行号')
            },
            {
              fieldCode: 'syncSapFlagName',
              fieldName: this.$tl('SAP同步状态')
            },
            {
              fieldCode: 'sapMessage',
              fieldName: this.$tl('导入sap信息')
            },
            {
              fieldCode: 'serialNumber',
              fieldName: this.$tl('序列号')
            },
            {
              fieldCode: 'companyCode_companyName',
              fieldName: this.$tl('公司名称')
            },
            {
              fieldCode: 'siteCode_siteName',
              fieldName: this.$tl('工厂名称')
            },
          ],
          footer: { // 列表页 footer
            sendBtnTabsArr: [2, 3, 4], // 根据页签来显示发送按钮
            sendUrl: '/api/contract/app-api/purchase/goods/plan/time/out/remind',
            sendParams: ['ids', 'queryType'],
          },
          detailFooter: { // 详情页 footer
            sendUrl: '/api/contract/app-api/purchase/goods/plan/time/out/remind',
            sendParams: ['ids', 'queryType'],
            sendBtnTabsArr: [2, 3, 4], // 根据页签来显示发送按钮
            hasTips: true,
          }
        },
      }
    },

    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },
    mounted() {},
    methods: {

    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }
</style>
