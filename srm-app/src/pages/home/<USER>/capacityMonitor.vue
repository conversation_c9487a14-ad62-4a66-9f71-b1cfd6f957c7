<template>
  <!-- 搜索 -->
  <srm-search-page ref="searchPage" :title="$tl('模具产能监控(待对接)')" :config="config" :cardConfig="cardConfig" @search="search">
    <view class="other-info">
      <view class="info-title">数据信息</view>
      <view class="info-table-container">
        <view class="info-table">
          <view class="info-th">
            <view class="info-td">供应商</view>
            <view class="info-td">理论产能</view>
            <view class="info-td">产出数</view>
            <view class="info-td">产能日期</view>
          </view>
          <view class="info-tbody">
            <view class="info-tr">
              <view class="info-td">110372-佛山星光优先公司</view>
              <view class="info-td">1000</view>
              <view class="info-td">2000</view>
              <view class="info-td">2024-09-01</view>
            </view>
            <view class="info-tr">
              <view class="info-td">110372-佛山星光优先公司</view>
              <view class="info-td">1000</view>
              <view class="info-td">2000</view>
              <view class="info-td">2024-09-01</view>
            </view>
            <view class="info-tr">
              <view class="info-td">110372-佛山星光优先公司</view>
              <view class="info-td">1000</view>
              <view class="info-td">2000</view>
              <view class="info-td">2024-09-01</view>
            </view>
          </view>
        </view>
      </view>

    </view>
  </srm-search-page>
</template>

<script>
  import {
    getTimeList,
  } from '@/utils/tools'
  export default {
    components: {},
    data() {
      return {
        appDeliveryType: '',
        account: '',
        config: {
          type: 'dateRange', // 顶部输入框类型
          headerFieldCode: 'inspTime', // 顶部输入框字段
          headerDefaultValue: [new Date().getTime(), new Date().getTime()], // 顶部输入框值
          url: '/api/contract/app-api/purchase/inspection/query/page', // 接口请求api
          supplierUrl: '/api/contract/app-api/supplire/inspection/query/page', // 接口请求api
          fields: [ // 更多查询字段
            {
              fieldCode: 'siteCode',
              fieldName: '工厂',
              type: 'select',
              url: '/api/masterDataManagement/app-api/public-api/site/paged-query',
              fields: {
                text: 'siteCode_siteName',
                value: 'siteCode'
              },
              queryKey: 'site' // 查询关键字
            },
            {
              fieldCode: 'buyerOrgCode',
              fieldName: '采购组',
            },
            {
              fieldCode: 'xxx',
              fieldName: '箱体',
            },
            {
              fieldCode: 'itemCode',
              fieldName: '物料编码',
            },
            {
              fieldCode: 'itemName',
              fieldName: '物料名称',
            },
            {
              fieldCode: 'supplierCode',
              fieldName: '供应商编码',
            },
            {
              fieldCode: 'supplierName',
              fieldName: '供应商名称',
            },

          ],
          statusFieldCode: 'stateCode', // 默认status, 后端约定字段
          statusDefaultValue: -1, // 默认选中状态
          tabs: [{
              tabCode: 1,
              tabName: '本月需均衡'
            },
            {
              tabCode: 2,
              tabName: '本月汇总缺口'
            },
            {
              tabCode: 3,
              tabName: '下月需均衡'
            },
            {
              tabCode: 4,
              tabName: '下月汇总缺口'
            },
            {
              tabCode: -1,
              tabName: '全部'
            },
          ]
        },
        cardConfig: {
          businessModuleCode: '7',
          header: { // 头信息配置
            icon: 'sjdh',
            fieldCode: 'inspectNo',
          },
          simple: [ //简述信息配置
            {
              fieldName: '工厂/采购组：',
              fieldCode: 'siteCode_buyerOrgCode'
            },
            {
              fieldName: '产出数：',
              fieldCode: 'supplierCode',
              right: true
            },
            {
              fieldName: '本月需求剩余：',
              fieldCode: 'sendInspectQty',
            },
            {
              fieldName: '结余数：',
              fieldCode: 'sendInspectQty',
              right: true,
              color: '#F55448'
            },
            {
              fieldName: '异常日期：',
              fieldCode: 'inspDate',
              width: '100%',
              color: '#F55448'
            },
          ],
          detail: [ // 详情信息配置
            {
              fieldCode: 'siteCode_siteName',
              fieldName: this.$tl('工厂')
            },
            {
              fieldCode: 'buyerOrgCode_buyerOrgName',
              fieldName: this.$tl('采购组')
            },
            {
              fieldCode: 'buyerOrgName',
              fieldName: this.$tl('最大产出数')
            },
            {
              fieldCode: 'itemName',
              fieldName: this.$tl('物料编码')
            },
            {
              type: 'line',
              fieldCode: '',
            },
            {
              icon: 'date',
              type: 'label',
              fieldName: this.$tl('本月')
            },
            {
              fieldCode: 'subSiteAddressCode_subSiteAddress',
              fieldName: this.$tl('剩余需求总数')
            },
            {
              fieldCode: 'requireDeliveryDate',
              fieldName: this.$tl('剩余产出总数')
            },
            {
              fieldCode: 'incomingCnt',
              fieldName: this.$tl('最后一天缺口数')
            },
            {
              fieldCode: 'maSampleQty',
              fieldName: this.$tl('需要均衡日期')
            },
            {
              icon: 'date',
              type: 'label',
              fieldName: this.$tl('下月')
            },
            {
              fieldCode: 'subSiteAddressCode_subSiteAddress',
              fieldName: this.$tl('剩余需求总数')
            },
            {
              fieldCode: 'requireDeliveryDate',
              fieldName: this.$tl('剩余产出总数')
            },
            {
              fieldCode: 'incomingCnt',
              fieldName: this.$tl('最后一天缺口数')
            },
            {
              fieldCode: 'maSampleQty',
              fieldName: this.$tl('需要均衡日期')
            },
          ],
          // detailFooter: { // 详情页 footer
          //   hasSendBtn: false, // 是否配置发送提醒
          //   hasTips: true,
          //   waitCode: 'onWayUpdateTime'
          // }
        },
      }
    },

    onLoad(options) {
      this.appDeliveryType = options.appDeliveryType
    },
    mounted() {},
    methods: {

    },
  }
</script>

<style lang="scss">
  .content-wrapper {
    padding: 32rpx;
  }

  .other-info {
    background: #ffffff;
    padding: 32rpx 32rpx 80rpx 32rpx;

    .info-title {
      font-weight: 500;
      font-size: 30rpx;
      color: #333333;
      margin-bottom: 24rpx;

      &::before {
        content: '';
        display: inline-block;
        width: 6rpx;
        height: 24rpx;
        background: #3678FE;
        border-radius: 0 100rpx 0 0;
        margin-right: 12rpx;
      }
    }

    .info-table-container {
      width: 720rpx;
      overflow-x: scroll;
      white-space: nowrap;
    }

    .info-table {
      width: 900rpx;

      .info-th {
        display: flex;
        white-space: nowrap;
        background: #4E5A70;
        padding: 13rpx 0;

        .info-td {
          flex: 0 0 auto;
          padding-left: 24rpx;
          color: #ffffff;
          font-weight: 500;
          font-size: 26rpx;
          box-sizing: border-box;

          &:nth-of-type(1) {
            width: 432rpx;
          }

          &:nth-of-type(2) {
            width: 145rpx;
          }

          &:nth-of-type(3) {
            width: 120rpx;
          }

          &:nth-of-type(4) {
            width: 200rpx;
          }
        }
      }

      .info-tbody {
        .info-tr {
          display: flex;
          white-space: nowrap;

          .info-td {
            flex: 0 0 auto;
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            padding: 22rpx 0 22rpx 24rpx;

            &:nth-of-type(1) {
              width: 432rpx;
            }

            &:nth-of-type(2) {
              width: 145rpx;
            }

            &:nth-of-type(3) {
              width: 120rpx;
            }

            &:nth-of-type(4) {
              width: 200rpx;
            }
          }
        }
      }
    }
  }
</style>
