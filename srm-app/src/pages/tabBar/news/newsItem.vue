<template>
  <view class="news-item" @click="handleClick">
    <view class="left">
      <image :src="newContent.iconUrl" />
    </view>
    <view class="middle">
      <view class="title" :class="[newContent.readStatus === 0 ? 'active' : '']">{{ newContent.businessModuleName }}
      </view>
      <view class="subTitle" v-html="newContent.message"></view>
    </view>
    <view class="right">{{ newContent.pushTime }}</view>
  </view>
</template>

<script>
  export default {
    props: {
      newContent: {
        type: Object,
        default: () => {}
      }
    },
    methods: {
      handleClick() {
        this.$emit('click', this.newContent)
      }
    }
  }
</script>

<style lang="scss">
  .news-item {
    background: #fff;
    border-bottom: 0.5px solid #ececec;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    height: 140rpx;

    .left {
      image {
        width: 80rpx;
        height: 80rpx;
      }
    }

    .middle {
      width: 63%;
      .title {
        font-family: PingFangSC-Medium;
        font-weight: 600;
        font-size: 34rpx;
        color: #333333;
        letter-spacing: 0;
      }
      .active::after {
        content: ' ';
        border: 6rpx solid #F55448;
        border-radius: 6rpx;
        position: absolute;
        margin-left: 10rpx;
        margin-top: 20rpx;
      }

      .subTitle {
        font-weight: 400;
        font-size: 30rpx;
        color: #999999;
        letter-spacing: 0;
        overflow: hidden;
      }
    }

    .right {
      text-align:right;
      width: 150rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #ABADAD;
    }
  }
</style>
