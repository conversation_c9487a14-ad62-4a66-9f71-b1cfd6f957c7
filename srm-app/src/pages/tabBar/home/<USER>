<template>
  <view :class="isIOS ? 'home-wrapper iOSWrapper' : 'home-wrapper'">
    <!-- banner -->
    <view class="banner">
      <swiper class="swiper" circular :indicator-dots="indicatorDots" indicator-color="#E0E0E0"
        indicator-active-color="#3979F9" :autoplay="autoplay" :interval="interval" :duration="duration">
        <swiper-item v-for="(item, key) in bannerList" :key="key">
          <view class="swiper-item">
            <image :src="item.url" />
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="container">
      <!-- notice -->
      <view class="notice" v-if="showNotice">
        <image style="width: 29rpx;height:29rpx;" src="/static/image/home/<USER>"></image>
        <view class="notice-wrapper">
          <text class="inner-text" :class="isScroll ? 'scroll' : ''">{{announce.announcementDetails}}</text>
        </view>
      </view>
      <!-- 功能列表 -->
      <view class="functionList">
        <view class="list-left" @click="goToPage('inventory')">
          <text class="title">{{$tl('库存情况')}}</text>
          <image class="icon" src="/static/image/home/<USER>"></image>
        </view>
        <view class="list-right">
          <view class="list-right-top" @click="goToPage('deliverySchedule')">
            <text class="title">{{$tl('交货计划')}}</text>
            <image class="icon" src="/static/image/home/<USER>"></image>
          </view>
          <view class="list-right-bottom">
            <view class="list-bottom-left" @click="goToPage('jitSchedule')">
              <text class="title">{{$tl('叫料计划')}}</text>
              <image class="icon" src="/static/image/home/<USER>"></image>
            </view>
            <view class="list-bottom-right" @click="showDeliveryDialog">
              <text class="title">{{$tl('送货单列表')}}</text>
              <image class="icon" src="/static/image/home/<USER>"></image>
            </view>
          </view>
        </view>

      </view>
      <!-- 其它功能 -->
      <view class="otherFunctionList">
        <view class="title">
          <view class="icon"></view>
          <text>{{$tl('其它功能')}}</text>
        </view>
        <view class="other-content">
          <view class="content-item" @click="goToPage('orderMonitor')">
            <image class="icon" src="/static/image/home/<USER>"></image>
            <text class="label">{{$tl('采购订单')}}</text>
          </view>
          <view class="content-item" @click="goToPage('qualityMonitor')">
            <image class="icon" src="/static/image/home/<USER>"></image>
            <text class="label">{{$tl('质检监控')}}</text>
          </view>
          <view class="content-item" @click="showSupplyDialog">
            <image class="icon" src="/static/image/home/<USER>"></image>
            <text class="label">{{$tl('供货计划')}}</text>
          </view>
          <view class="content-item" @click="goToPage('scanLoadingRecord')">
            <image class="icon" src="/static/image/home/<USER>"></image>
            <text class="label">{{$tl('扫描装车记录')}}</text>
          </view>
          <!--    <view class="content-item" @click="goToPage('capacityMonitor')">
            <image class="icon" src="/static/image/home/<USER>"></image>
            <text class="label">{{$tl('产能管理')}}</text>
          </view> -->
        </view>
      </view>
    </view>
    <!-- 切换交货计划 -->
    <srm-action-sheet ref="sheet" :items="sheetListDelivery" :cancel-text="$tl('取消')"
      @secleted="handleChangeSelected" />
    <srm-app-update ref="modal" />
    <!-- 切换交货供货计划 -->
    <srm-action-sheet ref="supplySheet" :items="sheetListSupply" :cancel-text="$tl('取消')"
      @secleted="handleSupplyChangeSelected" />
    <srm-app-update ref="modal" />
  </view>
</template>
<script>
  import {
    mapGetters
  } from 'vuex'
  import storage from '@/utils/storage'
  import {
    isNotEmpty
  } from '@/utils/tools'
  export default {
    data() {
      return {
        indicatorDots: true,
        autoplay: true,
        interval: 10000,
        duration: 2000,
        bannerList: [{
          'url': '/static/image/home/<USER>'
        }],
        announce: {},
        showDelivery: false,
        sheetListDelivery: [{
            id: 1,
            title: '供应商直送厂内',
            value: '0',
          },
          {
            id: 2,
            title: 'VMI直送厂内',
            value: '1',
          },
          {
            id: 3,
            title: '供应商直送VMI',
            value: '2',
          },
        ],
        sheetListSupply: [{
            id: 1,
            title: '采购订单',
            value: '0',
          },
          {
            id: 2,
            title: '交货计划',
            value: '1',
          },
          {
            id: 3,
            title: '叫料计划',
            value: '2',
          },
        ],
        isIOS: false
      }
    },
    computed: {
      isScroll() {
        return this.announce?.announcementDetails?.length > 25
      },
      showNotice() {
        return this.announce?.announcementDetails?.trim()
      },
      ...mapGetters('user', {
        token: 'token'
      }),
    },
    onLoad() {
      this.initData()
      const systemInfo = uni.getSystemInfoSync();
      this.isIOS = systemInfo.platform === 'ios';
    },
    methods: {
      // 显示送货单选择列表页
      showDeliveryDialog() {
        this.$refs.sheet.show()
      },
      // 显示供货计划选择列表
      showSupplyDialog() {
        this.$refs.supplySheet.show()
      },
      // 跳转到送货单列表页面
      handleChangeSelected(item) {
        // 跳转到页面
        uni.navigateTo({
          url: `/pages/home/<USER>/deliveryList?appDeliveryType=${item.value}`,
        })
      },
      // 供货计划跳转逻辑
      handleSupplyChangeSelected(item) {
        const {value} = item
        if(value === '0' || value === '1') {
          uni.showToast({
            title: '功能暂未开发',
            duration: 2000,
            icon: 'none',
            mask: false,
          })
          return
        }
        uni.navigateTo({
          url: `/pages/home/<USER>/supplySchedule`,
        })
      },
      // 获取页面数据
      initData() {
        // 初始化判断版本信息时候存储homeInfo信息
        const homeInfo = storage.getItemSync('homeInfo')
        if (isNotEmpty(homeInfo)) {
          this.bannerList = JSON.parse(homeInfo?.bannerImageConfig)
          this.announce = JSON.parse(homeInfo?.announce)
          // 版本更新
          this.$nextTick(() => {
            this.$refs.modal.versionUpdate({
              ...homeInfo
            })
          })
        }
      },
      goToPage(page) {
        if (!this.token) {
          uni.showToast({
            title: '请先登录',
            duration: 2000,
            icon: 'none',
            mask: false,
          })
          setTimeout(function() {
            uni.navigateTo({
              url: '/pages/login/login',
            })
          }, 1000)
          return
        }
        // 后续根据page来跳对应页面
        uni.navigateTo({
          url: `/pages/home/<USER>/${page}`,
        })
        return
      }
    }
  }
</script>
<style>
  page {
    background: #F9FBFF;
  }
</style>
<style lang="scss" scoped>
  @keyframes scroolNotice {
    0% {
      -webkit-transform: translateX(90%);
    }

    100% {
      -webkit-transform: translateX(-180%);
    }
  }

  .home-wrapper {
    background: url('/static/image/home/<USER>') no-repeat top center;
    background-size: 100%;
  }

  // #ifdef APP-PLUS
  .banner {
    padding-top: 80rpx;
  }

  .iOSWrapper .banner {
    padding-top: 0;
  }

  // #endif


  .swiper {
    height: 380rpx;

    .swiper-item {
      display: block;
      height: 380rpx;
      text-align: center;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }

  .container {
    padding: 24rpx 32rpx;

    .notice {
      display: flex;
      width: 100%;
      padding: 22rpx 24rpx;
      background-image: linear-gradient(270deg, #6F9FFF 0%, #ECF2FF 0%, #FAFBFD 100%);
      box-shadow: 0 2px 20px 0 #0000000a;
      border-radius: 16rpx;
      border: 1rpx solid #ffffff;

      .notice-wrapper {
        width: 100%;
        margin-left: 15rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .inner-text {
        display: block;
        width: 100%;
        color: #333333;
        font-size: 24rpx;
      }

      .inner-text.scroll {
        animation: scroolNotice 25s linear infinite;
      }
    }

    .functionList {
      margin-top: 24rpx;
      display: flex;

      .title {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
      }

      .list-left {
        display: inline-block;
        position: relative;
        width: 264rpx;
        height: 290rpx;
        padding: 24rpx 20rpx;
        background: url('/static/image/home/<USER>');
        border-radius: 16rpx;
        background-size: 100%;
        box-shadow: 0 4px 10px 0 #1e243c1a;

        .icon {
          position: absolute;
          bottom: 10rpx;
          right: 12rpx;
          width: 135rpx;
          height: 130rpx;
        }
      }

      .list-right {
        display: flex;
        flex-direction: column;
        margin-left: auto;

        .list-right-top {
          position: relative;
          width: 400rpx;
          height: 132rpx;
          padding: 24rpx 20rpx;
          background: url('/static/image/home/<USER>');
          background-size: 100%;
          box-shadow: 0 4px 10px 0 #1e243c1a;
          border-radius: 16rpx;

          .icon {
            position: absolute;
            width: 91rpx;
            height: 73rpx;
            bottom: 10rpx;
            right: 10rpx;
          }
        }

        .list-right-bottom {
          display: flex;
          margin-top: 24rpx;

          .list-bottom-left {
            position: relative;
            width: 188rpx;
            height: 132rpx;
            padding: 20rpx;
            background: url('/static/image/home/<USER>');
            background-size: 100%;
            box-shadow: 0 4px 10px 0 #1e243c1a;
            border-radius: 16rpx;

            .icon {
              position: absolute;
              width: 59rpx;
              height: 49rpx;
              bottom: 10rpx;
              right: 10rpx;
            }
          }

          .list-bottom-right {
            position: relative;
            margin-left: auto;
            width: 188rpx;
            height: 132rpx;
            padding: 20rpx;
            background: url('/static/image/home/<USER>');
            background-size: 100%;
            box-shadow: 0 4px 10px 0 #1e243c1a;
            border-radius: 16rpx;

            .icon {
              position: absolute;
              width: 44rpx;
              height: 50rpx;
              bottom: 10rpx;
              right: 10rpx;
            }
          }
        }
      }
    }

    .otherFunctionList {
      .title {
        display: flex;
        align-items: center;
        margin-top: 40rpx;
        margin-bottom: 24rpx;
        font-size: 30rpx;
        font-weight: 500;
        color: #333333;

        .icon {
          width: 6rpx;
          height: 24rpx;
          background: #3678FE;
          border-radius: 0 100px 0 0;
          margin-right: 18rpx;
        }
      }

      .other-content {
        display: flex;
        padding: 44rpx 0;
        background-image: linear-gradient(0deg, #FAFBFD 0%, #FFFFFF 43%, #ECF2FF 100%, #74A1FB 100%);
        border-radius: 16rpx;
        border: 1.4rpx solid #FFFFFF;
        box-shadow: 0 4px 10px 0 #2028431a;

        .content-item {
          width: 25%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .icon {
            width: 88rpx;
            height: 88rpx;
          }

          .label {
            font-size: 26rpx;
            color: #333333;
            margin-top: 35rpx;
          }
        }
      }
    }
  }
</style>
