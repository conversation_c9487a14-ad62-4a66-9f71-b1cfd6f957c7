<template>
  <view class="mine-wrapper">
    <view class="mine-header">
      <view class="mine-bg" />
      <view class="mine-header-wrapper">
        <view class="account-headshots">
          <image src="~@/static/image/mine/icon-default-account.png" class="wp100 hp100" />
        </view>
        <view class="account-txt">
          <block  v-if="accountName">
            <view class="account">
              <text class="text-bold fs-32 account-name">{{ accountName }}</text>
              <text class="status">{{ role ? $tl('采') : $tl('供') }}</text>
            </view>
            <view class="characters fs-28">{{ secretStr(userInfo['mobile']) }}</view>
          </block>
          <text v-else class="text-bold fs-32 loginText" @click="login">{{ $tl('请登录') }}</text>
        </view>
      </view>
    </view>
    <view class="bar" style="height:18rpx;background:#ffffff;"></view>
    <view class="mine-list mine-list-one">
      <block v-for="(item, index) in items" :key="index">
        <srm-cell v-if="item.show" :type="item.type" :title="item.title" :left-icon="item.leftIcon"
          :value.sync="item.value" :click-action="item.action" @click="clickAction" />
      </block>
    </view>
    <view class="mine-list mine-list-two">
      <block v-for="(item, index) in itemsTwo" :key="index">
        <srm-cell v-if="item.show" :type="item.type" :title="item.title" :left-icon="item.leftIcon"
          :value.sync="item.value" :click-action="item.action" @click="clickAction" />

      </block>
    </view>
    <!-- 选择弹窗 -->
    <!-- 切换语言 -->
    <srm-action-sheet ref="sheet" :items="sheetListLang" :cancel-text="$tl('取消')" @change="handleChangeSelected" />
  </view>
</template>

<script>
  // import './bgbase64.scss'
  import {
    mapActions,
    mapGetters,
  } from 'vuex'
  import {
    secretStr,
  } from '@/utils/tools.js'

  export default {
    data() {
      return {
        hasLogin: true,
        items: [],
        itemsTwo: [],
        countObj: {
          waitHandle: 0,
          processing: 0,
          complete: 0,
        },
        languageDesc: '简体中文',
        showSheetLang: false,
        sheetListLang: [{
            id: 1,
            title: '简体中文',
            value: 'zh-Hans',
          },
          // {
          //   id: 2,
          //   title: 'English',
          //   value: 'en',
          // },
          // {
          //   id: 3,
          //   title: 'Việt Nam',
          //   value: 'vi',
          // },
        ],
        tenantDesc: '',
        showSheetTenant: false,
        sheetListTenant: [],
        envDesc: '',
        showSheetEnv: false,
        language: 'zh',
      }
    },
    computed: {
      ...mapGetters('user', {
        userInfo: 'userInfo',
        role: 'role',
      }),

      accountName() {
        return this.role === 1 ? this.userInfo?.accountName : this.userInfo?.enterpriseName
      },
    },
    watch: {
      usrInfo() {
        this.initData()
      },
    },
    onLoad() {},
    onShow() {
      this.initData()
    },
    methods: {
      ...mapActions('user', {
        logout: 'logout',
        changeLocalEnv: 'changeLocalEnv',
      }),
      secretStr,
      initData() {
        if (this.language.startsWith('zh')) {
          this.languageDesc = '简体中文'
        } else if (this.language.startsWith('vi')) {
          this.languageDesc = 'Việt Nam'
        } else if (this.language.startsWith('en')) {
          this.languageDesc = 'English'
        }

        this.envDesc = this.$config.env === 'prod' ? this.$config.env : this.$storage.getItemSync('__ENV_') || this
          .$config.env
        this.items = [{
            title: this.$tl('服务协议'),
            leftIcon: '/static/image/mine/icon-server.png',
            type: 'info',
            show: true,
            value: '',
            action: 'server',
          },
          {
            title: this.$tl('隐私政策'),
            leftIcon: '/static/image/mine/icon-privacy.png',
            type: 'info',
            show: true,
            value: '',
            action: 'privacy',
          },
          {
            title: this.$tl('关于我们'),
            leftIcon: '/static/image/mine/icon-aboutus.png',
            type: 'info',
            action: 'aboutus',
            show: true,
            value: '',
          },
          {
            title: this.$tl('联系人'),
            leftIcon: '/static/image/mine/icon-contact.png',
            type: 'info',
            action: 'contact',
            show: true,
            value: '',
          },
          {
            title: this.$tl('切换语言'),
            leftIcon: '/static/image/mine/icon-lang.png',
            type: 'info',
            show: true,
            value: this.languageDesc,
            action: 'changeLang',
          },
          {
            title: this.$tl('设置'),
            leftIcon: '/static/image/mine/icon-setting.png',
            type: 'info',
            show: true,
            value: '',
            action: 'setting',
          },

        ]
        this.itemsTwo = [{
          title: this.$tl('退出登录'),
          leftIcon: '/static/image/mine/icon-logout.png',
          type: 'info',
          value: '',
          show: this.hasLogin,
          action: 'getOut',
        }]
      },
      clickAction(action) {
        this[action]()
      },
      // 跳转 - 服务协议
      server() {
        uni.navigateTo({
          url: '/pages/mine/serviceAgreement/serviceAgreement',
        })
      },
      // 跳转 - 隐私政策
      privacy() {
        // #ifdef APP-PLUS
        uni.navigateTo({
          url: '/pages/mine/privacyPolicy/privacyPolicy',
        })
        // #endif
        // #ifdef WEB || H5
        window.open('https://www.tcl.com/cn/zh/legal/privacy-policy')
        // #endif

      },
      // 跳转 - 关于我们
      aboutus() {
        uni.navigateTo({
          url: '/pages/mine/aboutus/aboutus',
        })
      },
      // 跳转 - 联系人
      contact() {
        uni.navigateTo({
          url: '/pages/mine/contact/contact',
        })
      },
      // 跳转 - 设置
      setting() {
        uni.navigateTo({
          url: '/pages/mine/setting/setting',
        })
      },
      // 操作 - 语言切换
      changeLang() {
        this.$refs.sheet.show()
      },
      // 操作 - 退出
      getOut() {
        this.$message
          .confirm({
            content: this.$tl('确定退出') + '?',
          })
          .then((res) => {
            this.logout()
            uni.reLaunch({
              url: '/pages/login/login',
            })
          })
      },
      // 操作 - 登录
      login() {
        uni.navigateTo({
          url: '/pages/login/login',
        })
      },
      async handleChangeSelected(item) {
        // if (item.value === this.language) return
        // if (this.isAndroid) {
        //   uni.showModal({
        //     content: this.$tl('应用此设置将重启App'),
        //     success: async(res) => {
        //       if (res.confirm) {
        //         this.languageDesc = item.title
        //         this.$i18n.locale = item.value
        //         // 设置语言环境
        //         await this.$store.dispatch('locale/saveLocale', item.value)
        //         // 获取主包语言包
        //         await this.$store.dispatch('locale/getModuleMessages', this.$config.comModuleCodes)
        //         // 获取技术平台语言包
        //         // await this.$store.dispatch('locale/getMiddlegroundLang', item.value)

        //         uni.setLocale(item.value) // 安卓会重启
        //         setTimeout(() => {
        //           plus.runtime.restart()
        //         }, 200)
        //       }
        //     },
        //   })
        // } else {
        //   this.languageDesc = item.title
        //   this.$i18n.locale = item.value
        //   // 设置语言环境
        //   await this.$store.dispatch('locale/saveLocale', item.value)
        //   // 获取主包语言包
        //   await this.$store.dispatch('locale/getModuleMessages', this.$config.comModuleCodes)
        //   // 获取技术平台语言包
        //   // await this.$store.dispatch('locale/getMiddlegroundLang', item.value)
        //   if (this.$store.state.user.hasUserInfo) {
        //     // 获取用户信息
        //     await this.$store.dispatch('user/getUserInfo', false)
        //   }
        //   uni.setLocale(item.value)
        //   setTimeout(() => {
        //     uni.reLaunch({
        //       url: '/pages/my/my',
        //     })
        //   }, 200)
        // }
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '~@/static/css/bgbase64.scss';

  .mine-wrapper {
    overflow: hidden;

    .mine-header {
      position: relative;
      background: #ffffff;
      height: 365rpx;
      overflow: hidden;
      // #ifdef WEB || H5
      height: 325rpx;
      // #endif

      .mine-bg {
        height: 100%;
        width: 100%;
        position: absolute;
        background: url('/static/image/mine/bg-account.png') no-repeat center left;
        background-size: 100%;
        opacity: 0.9;
        border-bottom-left-radius: 32rpx;
        border-bottom-right-radius: 32rpx;
      }

      .loginText {
        color: #333333;
      }

      .mine-header-wrapper {
        display: flex;
        align-items: center;
        padding: 145rpx 0 80rpx;
        // #ifdef WEB || H5
        padding-top: 115rpx;

        // #endif
        .account-headshots {
          margin-left: 40rpx;
          width: 140rpx;
          height: 140rpx;
          border-radius: 50%;
          overflow: hidden;
        }

        .account-txt {
          position: relative;
          display: flex;
          flex-direction: column;
          margin-left: 24rpx;

          .account {
            display: flex;

            .account-name {
              display: inline-block;
              color: #333333;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .status {
              display: inline-block;
              margin-left: 24rpx;
              padding: 8rpx 16rpx 6rpx;
              font-size: 26rpx;
              color: #ffffff;
              background: #D8B987;
              border-radius: 8rpx;
            }
          }

          .characters {
            margin-top: 6rpx;
            color: #333333;
          }
        }
      }
    }

    .mine-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 16rpx;
      padding: 0 32rpx;
      background-color: #fff;

      .h16 {
        height: 16rpx;
      }

      .bottom-line {
        width: 100%;
        height: 1px;
        background: #e5e5e5;
        margin: 7rpx;
      }
    }
  }
</style>
