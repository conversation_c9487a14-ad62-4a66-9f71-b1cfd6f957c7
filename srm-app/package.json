{"name": "srm-app", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:uat": "cross-env NODE_ENV=uat UNI_PLATFORM=h5 UNI_OUTPUT_DIR=dist/build/h5 vue-cli-service uni-build --mode production", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build --mode production", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --mode production", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch --mode development", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve --mode development", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "lint": "eslint --fix --ext .js,.vue src", "lint:eslint": "eslint --fix --ext \"src/**/*.{vue,ts,d.ts,less,css,scss}\"", "lint:prettier": "prettier --write --loglevel warn \"**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/"}, "dependencies": {"@dcloudio/uni-app": "2.0.2-4060620250520001", "@dcloudio/uni-app-plus": "2.0.2-4060620250520001", "@dcloudio/uni-h5": "2.0.2-4060620250520001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-i18n": "2.0.2-4060620250520001", "@dcloudio/uni-mp-360": "2.0.2-4060620250520001", "@dcloudio/uni-mp-alipay": "2.0.2-4060620250520001", "@dcloudio/uni-mp-baidu": "2.0.2-4060620250520001", "@dcloudio/uni-mp-harmony": "2.0.2-4060620250520001", "@dcloudio/uni-mp-jd": "2.0.2-4060620250520001", "@dcloudio/uni-mp-kuaishou": "2.0.2-4060620250520001", "@dcloudio/uni-mp-lark": "2.0.2-4060620250520001", "@dcloudio/uni-mp-qq": "2.0.2-4060620250520001", "@dcloudio/uni-mp-toutiao": "2.0.2-4060620250520001", "@dcloudio/uni-mp-vue": "2.0.2-4060620250520001", "@dcloudio/uni-mp-weixin": "2.0.2-4060620250520001", "@dcloudio/uni-mp-xhs": "2.0.2-4060620250520001", "@dcloudio/uni-quickapp-native": "2.0.2-4060620250520001", "@dcloudio/uni-quickapp-webview": "2.0.2-4060620250520001", "@dcloudio/uni-stacktracey": "2.0.2-4060620250520001", "@dcloudio/uni-stat": "2.0.2-4060620250520001", "@sc/scview-ui": "0.0.5", "@vue/shared": "3.3.4", "coordtransform": "^2.1.2", "core-js": "^3.6.5", "dayjs": "^1.10.6", "flyio": "0.6.14", "js-base64": "^3.7.2", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "regenerator-runtime": "^0.13.9", "util": "^0.12.5", "vconsole": "^3.15.1", "vue": "^2.6.11", "vue-class-component": "^6.3.2", "vue-i18n": "^7.8.1", "vue-property-decorator": "^8.0.0", "vuex": "3.6.2"}, "devDependencies": {"@babel/plugin-syntax-typescript": "^7.2.0", "@babel/runtime": "~7.12.0", "@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^13.1.0", "@dcloudio/types": "*", "@dcloudio/uni-automator": "2.0.2-4060620250520001", "@dcloudio/uni-cli-i18n": "2.0.2-4060620250520001", "@dcloudio/uni-cli-shared": "2.0.2-4060620250520001", "@dcloudio/uni-migration": "2.0.2-4060620250520001", "@dcloudio/uni-template-compiler": "2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-hbuilderx": "2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-uni": "2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-uni-optimize": "2.0.2-4060620250520001", "@dcloudio/webpack-uni-mp-loader": "2.0.2-4060620250520001", "@dcloudio/webpack-uni-pages-loader": "2.0.2-4060620250520001", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "1.13.8", "cross-env": "7.0.3", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.24.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.16.0", "jest": "25.5.4", "lint-staged": "^11.1.2", "mini-types": "*", "miniprogram-api-typings": "3.4.2", "postcss-comment": "^2.0.0", "prettier": "^2.3.2", "sass": "^1.67.0", "sass-loader": "^13.3.2", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-standard": "^22.0.0", "stylelint-order": "^4.1.0", "typescript": "^3.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "lint-staged": {"src/*/**/*.{js,vue}": ["eslint --fix --ext .js,.vue"]}, "uni-app": {"scripts": {"weixin": {"title": "微信小程序发布打包", "env": {"UNI_PLATFORM": "mp-weixin", "APP_ENV": "production", "baseURL": "https://sc-tms.tcl.com/tms-driver"}}}}}