# srm-demo-sub

## Project setup

```
npm install
```

### Compiles and hot-reloads for development

```
npm run serve
```

### Compiles and minifies for production

```
npm run build
```

### Lints and fixes files

```
npm run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

# 新建页面后的流程

1. 创建 router，将路由添加到平台站
2. 权限：gridId 以及按钮、tab 的权限
3. 页面编写

# 文件组织规范

## 文件组织说明

├── src │ ├── assets │ ├── components // 存放全局公用组件和全局布局组件 │ ├── icons │ ├── mock │ ├── router // 存放全局的路由组件 │ │ ├── routes // 根据业务划分的路由模块 │ │ │ ├── source.js │ │ │ └── detail.js │ │ └── index.js // 到处合法的路由模块 │ ├── api // 存放全局的路由组件 │ │ ├── modules // 根据业务划分的接口地址 │ │ │ ├── source.js │ │ │ └── detail.js │ │ └── index.js // 到处 api 的地址 │ ├── styles // 存放全局公用样式 │ ├── utils // 存放全局通用的工具方法，内部的文件名根据功能划分 │ │ ├── httpClient.js │ │ └── jsonParse.js │ ├── views // 存放项目的页面组件 │ │ ├── requestDetailPage // 文件夹根据一级子菜单进行划分，若有二级子菜单在内部再新建对应的文件夹 │ │ │ ├── components // 路由组件需要用到的子组件，公用组件 │ │ │ │ ├── ColumnList.vue │ │ │ │ └── DetailContent.vue │ │ │ ├── dict // 路由组件需要用到的页面常量等 │ │ │ │ ├── index.js │ │ │ └── Detail.vue // 对应路由地址的路由组件 │ │ │ └── List.vue // 对应路由地址的路由组件 │ │ └── sourcePackageConfig │ │ ├── sourcePackage │ │ │ ├── AddSourceDialog.vue │ │ │ ├── gridColumnTemplate.vue │ │ │ ├── sourceList.vue │ │ │ └── sourcePackage.vue │ │ └── index.vue │ ├── App.vue │ ├── main.js

## 文件命名约定

- Vue 组件以多个单词命名并始终采用单词大写开头
- JS，scss, 图片等采用首字母小写，其他单词首字母大写的命名格式

## 变量命名约定

- 变量命名也采用驼峰格式
- 变量应该尽可能语义化
- 方法名尽量以动词开头
- 布尔值的变量以 is,has, can 等类似的单词开头
- CSS 命名尽可能遵循 BEM 规则

# 公用方法说明

## http

- http 方法挂在 vue 实例上
- api 文件夹直接导出接口地址
- 对 response 的处理，200 状态直接返回，其他状态做全局拦截处理

# 代码格式规范

格式规范继承公司内部的`@mtech/eslint-config-vue`包, 并加以扩展

# 代码提交规范

- feat：新功能（feature）
- fix：修补 bug
- docs：文档（documentation）
- style： 格式（不影响代码运行的变动）
- refactor：重构（即不是新增功能，也不是修改 bug 的代码变动）
- test：增加测试
- chore：构建过程或辅助工具的变动
