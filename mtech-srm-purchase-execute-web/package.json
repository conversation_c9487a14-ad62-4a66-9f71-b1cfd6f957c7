{"name": "mtech-srm-purchase-execute-web", "version": "0.0.1", "private": true, "scripts": {"build": "vue-cli-service build", "build:report": "vue-cli-service build --report", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve", "upgrade:mtech-ui": "npx update-by-scope -t latest @mtech-ui npm install", "dictionary": "node build/dict.js", "translate": "node translate.js", "prepare": "husky install", "lint-staged": "lint-staged"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"]}, "dependencies": {"@mtech-ui/button": "^1.11.14", "@mtech-ui/checkbox": "^1.11.14", "@mtech-ui/col": "^1.11.14", "@mtech-ui/data-grid": "^1.11.21", "@mtech-ui/date-picker": "^1.11.14", "@mtech-ui/date-range-picker": "^1.11.14", "@mtech-ui/date-time-picker": "^1.11.14", "@mtech-ui/dialog": "^1.11.14", "@mtech-ui/drop-down-tree": "^1.11.14", "@mtech-ui/form": "^1.11.14", "@mtech-ui/form-item": "^1.11.14", "@mtech-ui/icon": "^1.10.11", "@mtech-ui/input": "^1.11.14", "@mtech-ui/input-number": "^1.11.14", "@mtech-ui/listbox": "^1.11.14", "@mtech-ui/loading": "^1.11.14", "@mtech-ui/multi-select": "^1.11.27", "@mtech-ui/page": "^1.11.14", "@mtech-ui/query-builder": "^1.11.14", "@mtech-ui/row": "^1.11.14", "@mtech-ui/select": "^1.11.27", "@mtech-ui/side-bar": "^1.11.14", "@mtech-ui/switch": "^1.11.14", "@mtech-ui/tabs": "^1.11.14", "@mtech-ui/time-picker": "^1.11.14", "@mtech-ui/tooltip": "^1.11.14", "@mtech/common-loading": "^1.0.0", "core-js": "^3.6.5", "echarts": "^5.5.1", "js-big-decimal": "^1.3.4", "lodash": "^4.17.21", "sortablejs": "^1.15.0", "vue": "^2.6.11", "vue-router": "^3.5.1", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "vxe-table": "^3.6.13", "xe-utils": "^3.5.11"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@digis-vue-cli-preset/vue-cli-plugin-performance": "^1.1.5", "@digis/component-props-state": "^1.2.6", "@digis/dictionary-plugin": "^1.1.11", "@digis/internationalization": "^1.1.17", "@digis/multilingual-input": "^1.1.20", "@mtech-common/http": "^1.0.5", "@mtech-common/utils": "^1.0.0", "@mtech-micro-frontend/vue-cli-plugin-micro": "^1.0.0", "@mtech-sso/single-sign-on": "^1.2.5-tcl.7", "@mtech-ui/base": "^1.10.10", "@mtech-ui/mtech-ui": "^1.11.14", "@mtech/common-permission": "^1.2.5", "@mtech/common-tree-view": "^0.1.45", "@mtech/eslint-config-vue": "^0.0.4", "@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-service": "^4.5.13", "axios": "^0.21.1", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.5", "chalk": "^2.4.2", "eslint": "^7.28.0", "husky": "^8.0.3", "js-cookie": "^2.2.1", "lint-staged": "^13.1.4", "md5": "^2.3.0", "node-sass": "^4.14.1", "npm-run-all": "^4.1.5", "pdfobject": "^2.2.5", "sass-loader": "^8.0.0", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.5", "vue-cli-plugin-style-resources-loader": "~0.1.5", "vue-template-compiler": "^2.6.11", "webpack-fix-style-only-entries": "^0.6.1", "webpack-glob-entry": "^2.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "mtMicro": {"type": "slave"}}