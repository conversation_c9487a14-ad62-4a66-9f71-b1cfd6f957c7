'use strict'
const path = require('path')
const proxyConfig = require('./src/config/proxy.config')
const DictionaryPlugin = require('@digis/dictionary-plugin')

function resolve(dir) {
  return path.join(__dirname, dir)
}
const name = 'mtech-srm-purchase-execute-web'

const port = process.env.port || process.env.npm_config_port || 8084 // dev port
// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  // publicPath: process.env.NODE_ENV === "production" ? "/purchase-execute/" : "/",
  publicPath: './',

  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',

  // lintOnSave: false,
  productionSourceMap: false,

  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    clientLogLevel: 'info',
    proxy: proxyConfig
  },

  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
        vue$: 'vue/dist/vue.esm.js'
      }
    },
    plugins: [
      new DictionaryPlugin() // new DictionaryPlugin({NODE_ENV:'production'}) 执行的插件的环境 默认为'production'
    ]
  },

  chainWebpack(config) {
    // fix HMR un-useful
    config.resolve.symlinks(true)

    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')

    // set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  },

  css: {
    requireModuleExtension: true, // 是否开启支持‘foo.module.css’样式
    extract: true, // 是否使用css分离插件 ExtractTextPlugin 抽离css
    sourceMap: process.env.NODE_ENV !== 'production' // 是否在构建样式地图，false将提高构建速度
  },

  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [path.resolve(__dirname, 'src/themes/_mtechUI.scss')]
    }
  }
}
