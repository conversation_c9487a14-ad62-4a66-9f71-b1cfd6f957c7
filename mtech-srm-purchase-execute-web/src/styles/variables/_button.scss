//button
//button-color
//-----------------------
//button-default dark-gray
$button-default-color: $white;
$button-default-bg: $dark-gray;
$button-default-border: darken($button-default-bg, 5%);
//button-primary light-blue
$button-primary-color: $white;
$button-primary-bg: $light-blue;
$button-primary-border: darken($button-primary-bg, 5%);
//button-success green
$button-success-color: $white;
$button-success-bg: $green;
$button-success-border: darken($button-success-bg, 5%);
//button-danger green
$button-danger-color: $white;
$button-danger-bg: $pink;
$button-danger-border: darken($button-danger-bg, 5%);
//button-disabled
$button-disabled-bg: #ccc;
$button-disabled-border: darken($button-disabled-bg, 5%);
$button-disabled-color: lighten($button-disabled-bg, 10%);
//-----
//button has border
//-----
$button-border-width: 1px;
$button-border-color: rgba(11, 173, 273, 0.3);
$button-font-color: $light-blue;
$button-border-font-color-hover: rgb(11, 173, 273);;
$button-border-font-color-active: rgb(0, 0, 0);

//button-size
//-----------------------
//button-default
$button-padding-default-vertical: $padding-default-vertical;
$button-padding-default-horizontal: $padding-default-horizontal;
$button-font-size-default: $font-size-default;
$button-border-radius-default: $border-radius-default;
//button-small
$button-padding-small-vertical: $padding-small-vertical;
$button-padding-small-horizontal: $padding-small-horizontal;
$button-font-size-small: $font-size-small;
$button-border-radius-small: $border-radius-default;
//button-medium
$button-padding-medium-vertical: $padding-medium-vertical;
$button-padding-medium-horizontal: $padding-medium-horizontal;
$button-font-size-medium: $font-size-medium;
$button-border-radius-medium: $border-radius-medium;
//button-large
$button-padding-large-vertical: $padding-large-vertical;
$button-padding-large-horizontal: $padding-large-horizontal;
$button-font-size-large: $font-size-large;
$button-border-radius-large: $border-radius-large;
