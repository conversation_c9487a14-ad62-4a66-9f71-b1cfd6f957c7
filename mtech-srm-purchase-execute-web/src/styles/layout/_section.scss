//页面结构
html {
  height: 100%;
}

body {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: $background-color;


}
.main {
  margin-left: $menu-width;
  margin-top: 60px;
  //width: calc(100vw - 200px);
  min-height: calc(100vh - 60px);
  //min-width: $main-width;
  overflow: auto;
  position: relative;
  & > section {
    padding: 15px;
  }
}
.main-content {
  margin: 10px;
  background: #fff;
  border: 1px solid #dcdcdc;
  min-width: 800px;
}

.box {
  .box-header {
    padding: 10px 0;
    position: relative;
    .box-header-content {
      width: 100%;
      &.box-search {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        .box-search-container {
          display: flex;
          // align-items: flex-start;
          .box-search-content {
            display: flex;
            flex-wrap: wrap;
            label {
              color: $main-font-color;
            }
          }
        }
        .button-tool {
          //align-self: center;
          margin: 2px 5px;
          display: inline-block;
          .button-search-right {
            //margin-right: 40px;
          }
          button {
            margin: 5px;
          }
        }
        .button-tool-right {
          display: flex;
          flex-wrap: nowrap
        }
      }
    }
  }

  .box-content {
    .box-content-left {
      height: calc(100vh - 200px);
      overflow-y: auto;
    }
  }
}
