html * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

*,
*:after,
*:before {
    box-sizing: border-box;
}

body {
    font: {
        size: $font-size-default;
        family: $primary-font-family;
    }
    color: $primary-font-color;
    background-color: $background-color;
    &.overflow-hidden {
        overflow: hidden;
    }
}

router-view {
    height: 100%;
}

a {
    color: $light-blue;
    text-decoration: none;
    cursor: pointer;
    &.disabled,
    &[disabled] {
        cursor: not-allowed;
    }
    &:hover {
        color: darken($light-blue, 8%);
    }
    &:active {
        color: darken($light-blue, 8%);
    }
}

img {
    max-width: 100%;
}

.float-l {
    float: left;
    display: inline;
}

.float-r {
    float: right;
    display: inline;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

.no-margin {
    margin: 0 !important;
}

.pull-right {
    float: right !important;
}

.pull-left {
    float: left !important;
}

.checkbox-label {
    margin-left: 5px;
    line-height: 25px;
}

.radioGroup {
    label {
        position: relative;
        padding-left: 20px;
        &::before,
        &::after {
            content: '';
            border-radius: 50%;
        }
        &::before {
            @include position(absolute, -1px null null 0);
            display: inline-block;
            width: 15px;
            height: 15px;
            border: $border;
        }
    }
    .radio:checked+label::before {
        border: 1px solid $light-blue;
    }
    .radio:checked+label::after {
        @include position(absolute, 3px null null 4px);
        width: 7px;
        height: 7px;
        background: $light-blue;
    }
}

//scrollBar-color
$scrollBar-track-color: #ffffff;
$scrollBar-thumb-color: #d8d8d8;
$scrollBar-thumb-hover-color: rgb(200, 200, 200);
$scrollBar-thumb-active-color: rgb(190, 190, 190);
//修改谷歌内核浏览器滚动条样式
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  border-radius: 2px;
  background-color: $scrollBar-track-color;
}

::-webkit-scrollbar-thumb {
  background-color: $scrollBar-thumb-color;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: $scrollBar-thumb-hover-color;
}

::-webkit-scrollbar-thumb:active {
  background-color: $scrollBar-thumb-hover-color;
}

::-webkit-input-placeholder { /* WebKit, Blink, Edge */
  color: $input-color-placeholder !important;
  font-size: 12px !important;
}
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
  color: $input-color-placeholder !important;
  font-size: 12px !important;
}
::-moz-placeholder { /* Mozilla Firefox 19+ */
  color: $input-color-placeholder !important;
  font-size: 12px !important;
}
:-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: $input-color-placeholder !important;
  font-size: 12px !important;
}
