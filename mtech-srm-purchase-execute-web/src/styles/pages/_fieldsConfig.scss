.fields-config-page {
  padding-top: 20px;
  height: 100%;
  width: 100%;
}
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-info {
  width: 100%;
  height: 120px;
  padding: 20px 20px 0 20px;
  justify-content: space-between;
  background: rgba(245, 248, 251, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 0 8px 0 0;

  .detail-info {
    padding: 0;
    display: flex;
    line-height: 1;

    .name-wrap {
      flex: 1;

      .first-line {
        display: flex;
        align-items: center;
        .code {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: #292929;
        }
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
          margin-left: 10px;

          &-1 {
            color: rgba(237, 161, 51, 1);
            background: rgba(237, 161, 51, 0.1);
          }
          &-2 {
            color: #6386c1;
            background: rgba(99, 134, 193, 0.1);
          }
        }
      }

      .second-line {
        display: flex;
        align-items: center;
        margin-top: 10px;
        .cai-name {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          @extend .text-ellipsis;
        }
        .cai-desc {
          margin-left: 60px;
        }
        ul {
          display: flex;
          li {
            margin-left: 30px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(157, 170, 191, 1);

            .mt-icons {
              font-size: 12px;
            }
            span {
              vertical-align: text-bottom;
              margin-left: 4px;
              @extend .text-ellipsis;
            }
          }
        }
      }
    }
    .btns-wrap {
      /deep/ .mt-button {
        margin-right: 20px;
        button {
          width: 76px;
          height: 34px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          box-shadow: unset;
          padding: 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
      }
    }
  }

  /deep/.mt-tabs {
    background: transparent;
    .tabs-arrow {
      display: none;
    }
    .tab-wrap {
      padding: 0;
      .tab-item {
        padding: 6px 10px;
        span {
          line-height: 1;
        }
      }
    }
  }
}
.config-container {
  flex: 1;
  margin-top: 10px;
  overflow: auto;
}
/deep/ .mt-data-grid {
  .checked {
    color: #54bf00;
    font-size: 16px;
  }

  .uncheck {
    color: #ed5633;
    font-size: 16px;
  }
}
/deep/.config-custom-tabs {
  background: #fafafa;
  padding: 0;
  width: 100%;
  /deep/.e-tab-header {
    background: transparent;
  }
  .tab-wrap {
    padding: 0;
  }
  ul.tab-container {
    display: flex;
    li.tab-item {
      flex-shrink: 0;
      color: #292929;
      font-size: 14px;
      font-weight: 400;
      height: 46px;
      line-height: 46px;
      min-width: 60px;
      position: relative;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .item-content {
        cursor: pointer;
        padding: 0;
        display: flex;
        position: relative;
        min-width: 60px;
        color: #4f5b6d;
        align-items: center;
        justify-content: center;
        .mt-icons {
          position: relative;
          top: -1px;
          margin-right: 6px;
        }
        .config-checkbox {
          &.mt-icon-a-icon_MultipleChoice_on {
            color: #6386c1;
          }
          &.mt-icon-a-icon_MultipleChoice_off {
            color: #9daabf;
          }
        }
        &.fixed {
          .config-checkbox {
            &.mt-icon-a-icon_MultipleChoice_on {
              color: #9a9a9a;
            }
          }
        }
      }

      &.active,
      &:hover {
        border-color: transparent;
        background: transparent;
      }

      &.active {
        color: #00469c;
        font-weight: 600;

        &:after {
          content: "";
          border: 1px solid #00469c;
          width: 60%;
          animation: active-tab 0.3s ease;
          position: absolute;
          bottom: 6px;
          left: 20%;
        }
        @keyframes active-tab {
          0% {
            width: 0;
            left: 50%;
          }
          100% {
            width: 60%;
            left: 20%;
          }
        }
      }
    }
  }
}
/deep/ .ext-tabs-container {
  height: 100%;
  width: 100%;
  background: #e8e8e8;
  .left-nav-tabs {
    flex-shrink: 0;
    background: #fff;
    width: 172px;
    margin-right: 10px;
    .nav-container {
      .nav-item {
        height: 54px;
        padding: 0 20px;
        position: relative;
        background: #ffffff;
        color: #232b39;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e8e8e8;
        .svg-option-item {
          display: flex;
        }
        .config-checkbox {
          &.mt-icon-a-icon_MultipleChoice_on {
            color: #6386c1;
          }
          &.mt-icon-a-icon_MultipleChoice_off {
            color: #9daabf;
          }
        }
        .config-arrow {
          display: none;
        }
        &.active {
          color: #6386c1;
          background: #f5f6f9;
          .mt-icons {
            display: block;
          }
        }
        &:hover {
          color: #6386c1;
          background: #fafbfd;
        }
      }
    }
  }
  .ext-content-container {
    flex: 1;
    background: #fff;
  }
}
/deep/td.e-rowcell {
  &:first-of-type {
    &.e-templatecell {
      padding-right: 0 !important;
    }
    .field-group {
      border-right: 1px solid #e8e8e8;
      position: flex;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;
      display: flex;
    }
  }
}
