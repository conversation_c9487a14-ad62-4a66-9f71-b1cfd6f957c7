.mt-flex {
  display: flex;
  position: relative;
}

// 去掉原生数字输入框的上下箭头
input[type="number"] {
  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    -webkit-appearance: none;
  }
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}

//active状态，左侧线条
.list-item-active {
  border-left: none;
  position: relative;
  &:before {
    content: "";
    height: 100%;
    width: 0;
    position: absolute;
    border-left: 2px solid #00469c;
    left: 0;
    top: 0;
    animation: list-item-active-animation 0.2s ease;
  }
  @keyframes list-item-active-animation {
    0% {
      top: 50%;
      height: 0;
    }
    100% {
      top: 0;
      height: 100%;
    }
  }
}

//左上角角标
.top-left-arrow-tag {
  padding-left: 5px;
  position: relative;
  &:before {
    content: "";
    height: 0;
    width: 0;
    border-right: 6px solid transparent;
    border-top: 6px solid #eda133;
    position: absolute;
    left: 0;
    top: 0;
  }
}

.svg-option-item {
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  position: relative;
  color: #4f5b6d;
  .mt-icons {
    margin-right: 6px;
  }
  span {
    word-break: keep-all;
    font-size: 14px;
  }
}

//侧拉框-分组规则、分配策略
.slider-panel-container {
  background: rgba(0, 0, 0, 0.2);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1001;

  .slider-modal {
    width: 800px;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    background: #fafafa;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 0 0 8px;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);

    // &:before {
    //   content: "";
    //   cursor: pointer;
    //   width: 12px;
    //   height: 60px;
    //   background: #f3f3f3;
    //   border-radius: 8px 0 0 8px;
    //   position: absolute;
    //   left: -12px;
    //   top: calc(50% - 30px);
    // }

    // &:after {
    //   content: "";
    //   width: 0;
    //   height: 0;
    //   cursor: pointer;
    //   border-width: 4px 0px 4px 4px;
    //   border-style: solid;
    //   border-color: transparent transparent transparent #6c7a8f;
    //   position: absolute;
    //   left: -8px;
    //   top: calc(50% - 4px);
    // }

    .slider-header {
      height: 60px;
      background: #f3f3f3;
      padding: 10px 20px 10px 30px;
      justify-content: space-between;
      align-items: center;

      .slider-title {
        font-size: 16px;
        font-weight: 500;
        color: #292929;
      }

      .slider-close {
        cursor: pointer;
        font-size: 30px;
        color: #4d5b6f;
        transform: rotate(45deg);
      }
    }

    .slider-content {
      flex: 1;
      padding: 20px;
      .rule-group {
        margin-top: 30px;

        &:first-of-type {
          margin-top: 0;
        }

        .group-header {
          font-size: 14px;
          color: #292929;
          display: inline-block;
          padding-left: 10px;
          position: relative;

          &:before {
            content: "";
            position: absolute;
            width: 3px;
            height: 12px;
            background: #eda133;
            border-radius: 2px 0 0 2px;
            left: 0;
            top: 1px;
          }
        }

        .group-description {
          padding-left: 10px;
          font-size: 12px;
          display: block;
          color: #9a9a9a;
          margin-top: 6px;
        }

        .group-content {
          padding: 20px 10px 0 10px;
          display: flex;
          .mt-form {
            margin: 0 10px;
            flex: 1;

            &:last-of-type {
              margin-right: 0;
            }
            &:first-of-type {
              margin-left: 0;
            }
          }
        }
      }
    }

    .slider-footer {
      height: 60px;
      background: #ffffff;
      border-radius: 0 0 0 8px;
      box-shadow: inset 0 1px 0 0 #e8e8e8;
      align-items: center;
      flex-direction: row-reverse;

      span {
        cursor: pointer;
        margin: 0 30px;
        font-size: 14px;
        color: #0043a8;
        font-weight: 500;
        padding: 5px 10px;
        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

.full-height {
  height: 100%;
}
.full-width {
  width: calc(100% - 20px) !important;
}
.pt20 {
  padding-top: 20px;
}

.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

@mixin col-mark {
  font-size: 12px;
  padding: 2px 6px;
  line-height: 1;
  border-radius: 2px;
  overflow: hidden;
}

.col-active {
  @include col-mark;
  color: #6386c1;
  background: rgba(238, 242, 249, 1);
}

.col-inactive {
  @include col-mark;
  color: #9baac1;
  background: rgba(155, 170, 193, 0.1);
}

.col-error {
  @include col-mark;
  color: #e51e10;
  background: rgba(155, 170, 193, 0.1);
}

// 采购执行 - 带行内编辑的表格的样式
.pe-edit-grid {
  .e-grid {
    // 去掉第一个单元格选中时的左边框样式
    tr td:first-child::before {
      display: none;
    }
    // 去掉 单元格的选中背景色
    td.e-active {
      background-color: transparent !important;
    }
    // 去掉行上 悬浮时的单元格背景色
    tr:hover td {
      background-color: transparent !important;
    }
    // 禁用的单元格 样式,,优先级第二高，更新了的还是会变成蓝色
    tr td {
      &.e-rowcell.bg-grey,
      &.e-rowcell.bg-grey.e-updatedtd {
        background-color: #f5f5f5 !important;
        color: #9a9a9a !important;
        cursor: not-allowed;
        &.e-gridchkbox {
          background-color: transparent !important;
          cursor: pointer;
        }
      }
    }
    // 去掉冻结列 悬浮时，改变背景色问题
    &.e-gridhover .e-frozenhover,
    .e-detailcell,
    .e-detailindentcell,
    .e-detailrowcollapse,
    .e-detailrowexpand,
    .e-groupcaption,
    .e-indentcell,
    .e-recordpluscollapse,
    .e-recordplusexpand,
    .e-rowcell {
      background-color: transparent;
    }
    // 待保存的单元格，背景色改变,,优先级最高
    tr td.e-rowcell.e-updatedtd {
      background-color: rgba(0, 70, 156, 0.1) !important;
    }
    // 去掉冻结列 右侧的蓝色边框
    .e-frozenheader .e-table,
    .e-frozencontent .e-table {
      border-right: 0;
    }

    // 编辑时禁用的单元格样式
    .e-editedrow,
    .e-addedrow {
      .e-rowcell .e-control-wrapper {
        &.e-disabled,
        &.cell-disabled,
        .e-input[readonly]:not(.e-dropdownlist) {
          background: #f5f5f5 !important;
          cursor: not-allowed;
        }
      }
    }
  }
}

.col-normal {
  @include col-mark;
  color: #8acc40;
  background: rgba(138, 204, 64, 0.1);
}

.col-abnormal {
  @include col-mark;
  color: #ed5633;
  background: rgba(237, 86, 51, 0.1);
}

.col-published {
  @include col-mark;
  color: #eda133;
  background: rgba(237, 161, 51, 0.1);
}

@mixin col-solid-point {
  &::before {
    content: "\2022";
    display: inline-block;
    font-size: 36px;
    vertical-align: middle;
    padding-right: 5px;
    height: 17px;
    transform: translateY(-6px);
  }
}

.col-notSynced {
  color: #9baac1;
  @include col-solid-point;
}

.col-synced {
  color: #6386c1;
  @include col-solid-point;
}
.col-notSynced1 {
  color: #8acc40;
  @include col-solid-point;
}
.col-synced1 {
  color: #ed5633;
  @include col-solid-point;
}

// 旋转图标 180°
.rotate-180::before {
  display: inline-block;
  transform: rotate(180deg);
}

// 表格可点击标题
.cell-operable-title {
  color: #00469c;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    font-weight: 500;
  }
}

.display-block {
  display: block;
}

// data-grid toolbar 按钮样式，使其与 mt-template-page toolbar 按钮一致
.custom-toolbar-grid {
  .e-toolbar .e-tbar-btn:hover,
  .e-toolbar .e-tbar-btn:focus {
    background: transparent !important;
    box-shadow: none !important;
  }

  .e-tbar-btn:hover span.e-tbar-btn-text,
  .e-tbar-btn:hover .mt-icons {
    color: #707b8b !important;
  }

  .e-btn-icon.mt-icons {
    font-family: element-icons, e-icons;
    vertical-align: baseline;
    color: #4f5b6d;
  }

  .e-tbar-btn span.e-tbar-btn-text {
    vertical-align: baseline;
  }
}

// 纵向的 flex 盒子
.vertical-flex-box {
  display: flex;
  flex-direction: column;
}

.flex-keep {
  flex: 0 0 auto;
}

.flex-fit {
  flex: 1 1 auto;
  height: 100%;
  overflow: auto;
}

.form-double-width-item {
  min-width: 420px !important; // 两倍宽度
  width: calc(40% - 20px) !important;
}

// 预测数据表格
// #forecast-manage-container .forecast-td,
// #predict-import-dialog-slot .forecast-td {
//   padding: 0px !important;
//   border-left: 0px !important;
//   border-right: 0px !important;
// }

.mt-tabs {
  flex-shrink: 0;
}

// 不使用列模板分页，自定义分页，使分页样式与列模板内置分页相同
.mt-pagertemplate.custom-page {
  height: auto;
  margin: 0;
  padding: 10px 0;
  box-sizing: border-box;
  background: var(--plugin-dg-bg-ff);
  border-top: 1px solid var(--plugin-dg-border-color);
}

// 可点击的 col title
.able-click-field {
  color: var(--plugin-ct-content-color);
  font-size: 14px;
  cursor: pointer;
  text-align: left;
  &:hover {
    font-weight: 500;
  }
}

/**--------------增加form-item的label文字解释-----lbj-2023.06.27-----------*/
.label-badge-content {
  display: block;
  position: absolute;
  left: 70px;
  top: 2px;
  height: 16px;
  line-height: 16px;
  width: 16px;
  text-align: center;
  background: rgba(0, 0, 0, 0.87);
  color: #fff;
  border-radius: 50%;
  &:after {
    content: attr(badge-msg);
    display: none;
    position: absolute;
    z-index: 9999;
    width: 300px;
    left: -90px;
    top: 18px;
    padding: 6px 8px;
    background: rgba(0, 0, 0, 0.7);
    color: #eee;
    border-radius: 4px;
    text-align: left;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
  }
}
.label-badge-content:hover::after {
  display: block;
}
/**--------------增加form-item的label文字解释-----lbj-2023.06.27-----------*/

.e-row.bg-light-red,.e-row.bg-light-red.e-updatedtd {
  background-color: #f5a1a1 !important;
  color: #9a9a9a !important;
  cursor: not-allowed;
  &.e-gridchkbox {
    background-color: transparent !important;
    cursor: pointer;
  }
}
