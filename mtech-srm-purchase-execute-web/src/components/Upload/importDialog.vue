<template>
  <mt-dialog
    ref="dialog"
    :css-class="dialogClass"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons[currentStep]"
    :open="onOpen"
  >
    <div class="dialog-content">
      <div class="tips" v-if="downLoad">
        <span>{{ $t('请使用批量导入模板') }}</span>
        <span class="downs" @click="downloadTemplate">
          <mt-icon name="icon_list_download"></mt-icon>
          {{ $t('下载模板') }}</span
        >
      </div>
      <!-- 头部 -->
      <div class="step-info" v-if="hasStep">
        <div :class="[currentStep == step.first && 'highlight']">
          <span class="number-circle">{{ step.first }}</span
          ><span class="step-title">{{ $t('选择文件') }}</span>
        </div>
        <div class="ellipsis">
          <span class="circle"></span>
          <span class="circle"></span>
          <span class="circle"></span>
          <span class="circle"></span>
        </div>
        <div :class="[currentStep == step.second && 'highlight']">
          <span class="number-circle">{{ step.second }}</span
          ><span class="step-title">{{ $t('导入文件') }}</span>
        </div>
      </div>
      <!-- 上传附件 -->
      <div class="upload-file-box" v-show="currentStep == step.first">
        <upload-file
          :accept="accept"
          :save-url="saveUrl"
          :from-data-key="fromDataKey"
          :direction="'column'"
          :is-single-file="true"
          :is-view="false"
          ref="uploader"
          @uploadCompleted="uploadCompleted"
        ></upload-file>
      </div>
      <div class="step-second-box full-height" v-show="currentStep == step.second">
        <slot></slot>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { PROXY_FILE } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    UploadFile: () => import('@/components/Upload/uploader')
  },
  props: {
    hasStep: {
      // 是否存在步骤
      type: Boolean,
      default: () => true
    },
    // 下载模板的组合参数
    downTemplateParams: {
      type: Object,
      default: () => {}
    },
    requestUrls: {
      type: Object,
      default: () => {}
    },
    downLoad: {
      // 是否存在步骤
      type: Boolean,
      default: () => false
    },
    accept: {
      // 可接受文件类型 例： [".xls", ".xlsx"]
      type: Array,
      default: () => []
    },
    saveUrl: {
      // 上传的请求地址
      type: String,
      default: `${PROXY_FILE}/user/file/uploadPrivate?useType=2`
    },
    fromDataKey: {
      // 上传组件的 name，此值为调用 api 请求参数的 key
      type: String,
      default: 'UploadFiles'
    },
    dialogClass: {
      // 接受自定义的 class String
      type: String,
      default: 'create-proj-dialog'
    }
  },
  data() {
    // 步骤常量
    const step = {
      first: 1,
      second: 2
    }

    return {
      step,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentStep: 1, // 步骤默认为 1
      dialogTitle: '', // 弹窗标题
      config: {}, // 弹窗配置
      buttons: {
        [step.first]: [
          {
            click: this.handleClose,
            buttonModel: { content: this.$t('取消') }
          },
          {
            // 功能缺失，与顾问沟通先放按钮上去，功能后期优化 2022、12-21
            // click: "",
            click: this.doImport,
            buttonModel: { content: this.$t('确认') }
          }
        ],
        [step.second]: [
          {
            click: this.doPrevious,
            buttonModel: { content: this.$t('上一步') }
          },
          {
            click: this.doImport,
            buttonModel: { content: this.$t('导入') }
          }
        ]
      }
    }
  },
  mounted() {},
  methods: {
    // 初始化
    init(entryInfo) {
      const { title, config } = entryInfo

      this.currentStep = this.step.first
      this.$refs.uploader.init()
      this.$refs.dialog.ejsRef.show()
      this.dialogTitle = title // 弹框名称
      this.config = config // 弹窗配置
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 导入
    doImport() {
      this.$emit('import')
    },
    // 上一步
    doPrevious() {
      this.currentStep--
      this.$refs.uploader.init()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 文件上传完成
    uploadCompleted(response) {
      this.$emit('uploadCompleted', response)
    },
    // 显示第二步
    showStepSecond() {
      this.currentStep = this.step.second
    },
    // 下载模板
    downloadTemplate() {
      this.$API[this.requestUrls.templateUrlPre]
        [this.requestUrls.templateUrl](this.downTemplateParams)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
}
.step-info {
  display: flex;
  justify-content: space-between;
  width: 40%;
  margin: auto;

  & .highlight {
    .step-title {
      color: #6a85bd;
      border-bottom: 2px solid #6a85bd;
    }
    .number-circle {
      color: #ffffff;
      background-color: #6a85bd;
    }
  }

  .ellipsis {
    .circle {
      display: inline-block;
      border-radius: 50%;
      height: 4px;
      margin: 3px;
      width: 4px;
      background-color: #dee4f1;
    }
  }

  .step-title {
    display: inline-block;
    padding: 2px 6px;
    margin-left: 4px;
  }
  .number-circle {
    display: inline-block;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    line-height: 21px;
    text-align: center;
    vertical-align: middle;
    color: #6a85bd;
    border: 2px solid #6a85bd;
  }
}
.upload-file-box {
  height: 80%;
  padding: 30px;
  overflow: auto;
}
.dialog-content {
  .tips {
    margin-bottom: 15px;
  }
  .downs {
    margin-left: 10px;
    color: #005ca9;
    cursor: pointer;
  }
}
</style>
