<template>
  <div class="uploader">
    <!-- 上传框 -->
    <mt-uploader
      v-if="uploaderReload"
      ref="uploadObj"
      :class="[
        'to-upload',
        direction === 'initial' && 'initial',
        direction === 'column' && 'column'
      ]"
      :name="fromDataKey"
      v-show="(!isView && !isSingleFile) || (isSingleFile && files.length == 0)"
      :async-settings="asyncSettings"
      :uploading="uploading"
      :max-file-size="1000000000000"
      :multiple="true"
      :show-file-list="false"
      @progress="progress"
      @success="success"
      @selected="selected"
    >
      <div class="upload-box">
        <mt-icon class="plus-icon" name="icon_Close_1"></mt-icon>
        <div class="right-state">
          <div class="plus-txt">{{ uploadText ? uploadText : $t('请拖拽文件或点击上传') }}</div>
          <div class="warn-text">
            {{ $t('文件最大不可超过50M') }}
            {{ acceptMsg ? `,${acceptMsg}` : '' }}
          </div>
        </div>
      </div>
    </mt-uploader>

    <!-- 提示/操作：清空/全部下载 -->
    <div class="file-actions" v-if="isHaveDownloadableFiles()">
      <span>{{ $t('已上传') }}{{ files.length }}{{ $t('个附件') }}</span>
      <span v-show="!isView && !isSingleFile" class="clear" @click="handleClearAll">
        {{ $t('清空') }}</span
      >
      <span v-show="!isSingleFile" class="download" @click="handleDownload('all')">{{
        $t('全部下载')
      }}</span>
    </div>

    <!-- 上传状态 -->
    <div v-for="(item, index) in files" class="has-file" :key="index">
      <!-- 左边的信息 -->
      <div class="left-info">
        <div class="file-box">
          <div class="file-info">
            <div
              :class="['file-name', item.progress === undefined && 'upload']"
              @click="fileNameClick(item)"
            >
              {{ item.fileName }}
            </div>
            <mt-progress
              v-if="item.progress"
              :id="index"
              ref="progress"
              type="Linear"
              height="20"
              width="100%"
              :value="item.progress"
            >
            </mt-progress>
            <div class="file-size">{{ item.fileSize | byteToKB }}KB</div>
          </div>
        </div>
      </div>
      <!-- 下载按钮 -->
      <mt-icon
        v-if="item.progress === undefined"
        name="icon_list_download"
        class="down-icon"
        @click.native="handleDownload('one', item)"
      ></mt-icon>
      <!-- 删除按钮 -->
      <mt-icon
        v-if="!isView && item.progress === undefined"
        name="icon_Close_1"
        class="close-icon"
        @click.native="handleRemove(item)"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { HEADER_TOKEN_KEY, getToken } from '@mtech-sso/single-sign-on'
import { PROXY_FILE } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import { download } from '@/utils/utils'

export default {
  props: {
    isView: {
      // 是只查看的状态，故没有上传和删除
      type: Boolean,
      default: false
    },
    uploadText: {
      // 布局方向 initial: 默认横向 column: 纵向
      type: String,
      default: ''
    },
    direction: {
      // 布局方向 initial: 默认横向 column: 纵向
      type: String,
      default: 'initial'
    },
    viewFileData: {
      // 查看状态下传入文件数据
      type: Array,
      default: () => []
    },
    accept: {
      // 可接受文件类型 例： [".xls", ".xlsx"]
      type: Array,
      default: () => []
    },
    isSingleFile: {
      // 单文件上传
      type: Boolean,
      default: false
    },
    saveUrl: {
      // 上传的请求地址
      type: String,
      default: `${PROXY_FILE}/user/file/uploadPrivate?useType=2`
    },
    fromDataKey: {
      // 上传组件的 name，此值为调用 api 请求参数的 key
      type: String,
      default: 'UploadFiles'
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100
  },
  data() {
    return {
      asyncSettings: {
        saveUrl: `/api${this.saveUrl}` // `api${this.saveUrl}`,
      },
      acceptMsg: '', // 可接受文件类型的提示信息
      files: [], // 上传后得到的数据
      uploaderReload: true // 为了可以上传相同文件
    }
  },
  watch: {
    viewFileData: {
      handler(value) {
        this.files = cloneDeep(value)
      },
      immediate: true
    },
    accept: {
      handler(value) {
        let typeStr = ''
        value.forEach((itemType) => {
          typeStr += `${itemType} `
        })
        if (typeStr) {
          typeStr = typeStr.substring(0, typeStr.length - 2) // 去掉末尾的空格
          this.acceptMsg = this.$t('文件格式仅支持') + `（${typeStr}）`
        }
      },
      immediate: true
    }
  },
  methods: {
    init() {
      this.files = []
      this.uploaderReload = false
      this.$nextTick(() => {
        this.uploaderReload = true
        this.clearInputElement()
      })
    },
    delReload() {
      this.uploaderReload = false
      this.$nextTick(() => {
        this.uploaderReload = true
      })
    },
    // 移除文件
    handleRemove(e) {
      this.files = this.files.filter((item) => item.id !== e.id)
      this.clearInputElement()
      this.$emit('change', this.filterEmitFiles())
      this.$emit('removeOne', { files: this.files, removedId: e.id })
      this.delReload()
    },
    // 文件名点击事件
    fileNameClick(e) {
      if (e.remoteUrl) {
        let params = {
          id: e.id || e.data?.id || e.data?.sysFileId,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(
            `${res.data}/onlinePreview?url=${e.remoteUrl || e.data?.remoteUrl || e.data?.url}`
          )
        })
      }
    },
    // 选择文件后
    selected(args) {
      const file = args.filesData[0]
      if (file?.size > 52428800) {
        args.cancel = true // 取消选择
        this.clearInputElement()
        this.$toast({
          content: this.$t('单个文件，限制50M'),
          type: 'warning'
        })
        return
      }
      if (this.accept.length > 0 && file) {
        let isValid = false
        for (let i = 0; i < this.accept.length; i++) {
          const fileNameLength = file.name.length - this.accept[i].length
          const acceptIndex = file.name.indexOf(this.accept[i])
          if (acceptIndex === fileNameLength) {
            isValid = true
            break
          }
        }
        if (!isValid) {
          this.$toast({
            content: this.acceptMsg,
            type: 'warning'
          })
          args.cancel = true // 取消选择
          this.clearInputElement()
          return
        }
      }
    },
    // 清空 dom input
    clearInputElement() {
      const inputElement =
        document.querySelector(`[name=${this.fromDataKey}] input`) ||
        document.querySelector(`.e-file-select input`)
      if (inputElement?.value) {
        inputElement.value = null // 清空上传 input
      }
    },
    // 上传开始
    uploading(args) {
      args.currentRequest.setRequestHeader(HEADER_TOKEN_KEY, getToken())
      this.files.push({
        statusName: 'uploading', // 状态名称 "uploading"
        id: args.fileData.id, // 组件内文件id
        fileName: args.fileData.name, // 文件名
        fileSize: args.fileData.size, // 文件大小
        progress: 0 // 上传进度
      })
    },
    // 进度事件
    progress(args) {
      const fileId = args.file.id
      const fileList = cloneDeep(this.files)
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].id === fileId) {
          fileList[i].statusName = 'progress' // 状态名称 "progress"
          fileList[i].progress = (args.e.loaded / args.e.total) * 100 // 上传进度
          break
        }
      }
      this.files = fileList
    },
    // 上传成功
    success(args) {
      const fileId = args.file.id
      const fileList = cloneDeep(this.files)
      if (args.operation === 'upload') {
        // 上传完成
        const response = JSON.parse(args?.e?.target?.response)
        if (response.code == 200) {
          // 保存api返回的文件信息
          for (let i = 0; i < fileList.length; i++) {
            if (fileList[i].id === fileId) {
              fileList[i] = {
                fileData: args.file,
                ...response?.data // api 返回的数据
              }
              break
            }
          }
          this.files = fileList
          this.$emit('change', this.filterEmitFiles())
          this.$emit('uploadCompleted', response)
        } else {
          for (let i = 0; i < fileList.length; i++) {
            if (fileList[i].id === fileId) {
              fileList.splice(i, 1) // 移除当前项
              break
            }
          }
          this.files = fileList
          // 错误处理
          this.$toast({
            content: response.msg,
            type: 'error'
          })
        }
        this.clearInputElement()
      }
    },
    // 过滤出事件发出的数据
    filterEmitFiles() {
      const fileList = cloneDeep(this.files)
      const files = fileList.filter((item) => {
        let pass = false
        if (item.progress === undefined) {
          // 过滤出api返回的数据
          item.fileData = undefined // 置空不必要的参数
          pass = true
        }

        return pass
      })

      return files
    },

    // 清空所有附件
    handleClearAll() {
      const removedIds = []
      this.files.forEach((item) => {
        removedIds.push(item.id)
      })
      this.files = []
      this.$emit('change', [])
      this.$emit('removeAll', { files: [], removedIds })
      this.delReload()
    },

    // 是否存在可下载的数据
    isHaveDownloadableFiles() {
      let isHave = false
      if (this.files?.length > 0) {
        for (let i = 0; i < this.files.length; i++) {
          if (this.files[i].progress === undefined) {
            isHave = true
          }
        }
      }

      return isHave
    },

    // 下载附件
    handleDownload(flag, item) {
      if (flag == 'all') {
        this.files.forEach((itemFiles) => {
          if (itemFiles.progress === undefined) {
            this.handlerDownloadPrivateFile(itemFiles)
          }
        })
      } else {
        this.handlerDownloadPrivateFile(item)
      }
    },
    handlerDownloadPrivateFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPrivateFile({
          id: data.sysFileId || data.id
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: data.fileName, blob: res.data })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader {
  padding: 4px;
  // background: rgba(251, 252, 253, 1);
  position: relative;
  height: 100%;

  /deep/ .to-upload {
    display: flex;
    position: relative;
    cursor: pointer;

    // /deep/ #dropArea {
    //   width: 100%;
    // }

    &.initial {
      flex-direction: initial;
      height: 70px;
    }

    &.column {
      flex-direction: column;
      height: 100%;

      .upload-box {
        flex-direction: column;
        justify-content: center;

        .right-state {
          width: 100%;
          text-align: center;

          .plus-txt {
            margin: 0px auto 10px;
          }

          .warn-text {
            transform-origin: initial;
          }
        }
      }
    }

    & > div {
      width: 100%;
      height: 100%;
    }

    .mt-dialog {
      flex: 1;
    }

    .e-control-wrapper {
      display: none;
    }
  }

  .upload-input {
    cursor: pointer;
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    background: #fbfcfd;
    margin-bottom: 10px;
    .plus-icon {
      transform: rotate(45deg);
      color: #98aac3;
      margin: 0 12px 0 10px;
      opacity: 0.6;
      width: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 24px;
    }
    .right-state {
      .plus-txt {
        width: 200px;
        height: 20px;
        font-size: 20px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(152, 170, 195, 1);
        margin-bottom: 10px;
      }
      .warn-text {
        font-size: 10px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(241, 62, 62, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }
  }

  .has-file {
    height: 70px;
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px dashed rgba(232, 232, 232, 1);
    margin: 10px 0 10px 0;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: inherit;

      .file-box {
        height: inherit;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        overflow: auto;
        img {
          height: inherit;
          width: fit-content;
        }
        .file-info {
          padding: 0 10px;
          width: 100%;
          .file-name {
            color: rgba(154, 154, 154, 1);
            font-size: 14px;

            &.upload {
              color: rgba(0, 70, 156, 1);
              cursor: pointer;
            }
          }
          .file-size {
            color: #9a9a9a;
            font-size: 12px;
          }
        }
      }
    }

    .close-icon {
      font-size: 14px;
      padding-right: 30px;
      cursor: pointer;
      color: #98aac3;
    }
    .down-icon {
      font-size: 18px;
      padding-right: 30px;
      cursor: pointer;
      color: #98aac3;
    }
  }

  /deep/ .file-actions {
    margin: 16px 0 16px 0;
    font-size: 14px;
    line-height: 1;
    color: #292929;

    .clear,
    .download {
      color: #00469c;
      cursor: pointer;
      margin-left: 20px;
    }
  }
}
</style>
