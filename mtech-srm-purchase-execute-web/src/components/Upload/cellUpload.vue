<template>
  <div class="cell-upload" :id="'cell-upload-' + data.index">
    <!-- data.file:{{ data.file }} -->
    <!-- 草稿或审批拒绝状态才能再次上传 -->
    <div class="to-upload" v-if="canUpload && (!data.file || !data.file.id)">
      <input type="file" class="upload-input" @click="isLimitedModify" @change="chooseFiles" />
      <div class="upload-box">
        <mt-icon class="plus-icon" name="icon_Close_1"></mt-icon>
        <div class="right-state">
          <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
          <div class="warn-text">{{ $t('文件最大不可超过50M') }}</div>
        </div>
      </div>
    </div>

    <div v-else-if="data.file && data.file.id" class="has-file">
      <div class="left-info">
        <div class="file-title">
          <span class="text-ellipsis">{{ data.file.fileName.split(data.file.fileType)[0] }}</span>
          <span>{{ data.file.fileType }}</span>
        </div>
        <div>{{ data.file.fileSize | byteToKB }}kb</div>
      </div>
      <mt-icon
        name="icon_Close_2"
        class="close-icon"
        v-if="canUpload"
        @click.native.stop="handleRemove"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getComponent } from '@syncfusion/ej2-base'
export default {
  data() {
    return {
      data: {
        file: {}
      },
      headerStatus: null, // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
      canUpload: true
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100
  },
  mounted() {
    this.$nextTick(() => {
      this.headerStatus = sessionStorage.getItem('headerStatus')

      this.setCanUpload()
      console.log('hi', this.canUpload, this.$route.path)
      console.log('this.data', this.data)
    })
  },
  methods: {
    setCanUpload() {
      // 不上预览状态 且 是草稿或审批拒绝 状态时，可以上传附件
      if (this.$route.path.includes('create-sourcing-requirement')) {
        // 创建寻源
        this.canUpload = true
      } else if (this.$route.path.includes('related-after-sale')) {
        // 相关附件
        const constantType = {
          Add: '1', // 新增
          Edit: '2', // 编辑
          Look: '3' // 查看
        }
        this.canUpload =
          this.$route.query.type === constantType.Add ||
          this.$route.query.type === constantType.Edit ||
          this.$route.query.type === constantType.EditDraft
      } else if (
        this.$route.path.includes('pr-apply-detail') ||
        this.$route.path.includes('normal-edit')
      ) {
        // 采购申请
        this.canUpload =
          (!this.$route.query.type || this.$route.query.type != 'view') &&
          (this.headerStatus == 0 || this.headerStatus == 3)
        // console.log("当前data--uploadcell", this.data, this.headerStatus);
      } else {
        // 采购订单
        this.canUpload =
          this.$route.query.type === '1' ||
          this.$route.query.type === '2' ||
          this.$route.query.type === '4'
        if (this.$route.name == 'custom-receipt-detail') {
          this.canUpload = false
        }
        if (this.$route.name == 'purchase-coordination-detail' && this.$route.query.type === '5') {
          this.canUpload = false
        }
      }
    },

    // 点击上传input时
    isLimitedModify() {
      // 暂时不能上传的情况：某些页面 && 有改动未保存
      let isLimited = true
      // if (
      //   (this.$route.path.includes("pr-apply-detail") &&
      //     this.getBatchChanges("Grid1")) ||
      //   (this.$route.path.includes("purchase-coordination-detail") &&
      //     this.getBatchChanges("Grid2")) ||
      //   (this.$route.path.includes("sales-coordination-detail") &&
      //     this.getBatchChanges("Grid3"))
      // ) {
      //   this.$toast({
      //     content: this.$t("请先保存表格数据再上传附件"),
      //     type: "warning",
      //   });
      //   e.preventDefault();
      //   isLimited = true;
      // }

      return isLimited
    },

    // 是否有修改未提交，true：有未提交的-----
    getBatchChanges(gridId) {
      var grid = new getComponent(gridId, 'grid')
      let changesObj = grid.getBatchChanges()
      if (
        changesObj.addedRecords.length ||
        changesObj.changedRecords.length ||
        changesObj.deletedRecords.length
      ) {
        return true
      }
      return false
    },

    chooseFiles(data) {
      console.log(this.$t('点击上传'), data, this.$parent)
      let _files = event.target.files
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }

      let params1 = {
        rowIndex: +this.data.index,
        addId: this.data.addId,
        id: this.data.id
      }

      // this.$store.commit("startLoading");
      this.$API.fileService
        .uploadPrivateFile(_data)
        .then((res) => {
          // this.$store.commit("endLoading");
          console.log('上传成功---', res?.data, this.data)
          this.data.file = res?.data
          params1.fileInfo = res?.data
        })
        .finally(() => {
          this.$parent.$emit('confirm-function', JSON.stringify(params1))
        })
    },

    // 移除文件
    handleRemove(e) {
      // 移除文件时也要校验是否有改动未保存
      // if (this.isLimitedModify(e)) {
      //   return;
      // }
      e.preventDefault()
      this.data.file = null
      // console.log("点击了remove-file", this.data);
      let params = {
        rowIndex: this.data.index,
        addId: this.data.addId,
        id: this.data.id
      }
      this.$parent.$emit('remove-file', JSON.stringify(params))
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-upload {
  padding: 4px;
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    .plus-icon {
      transform: rotate(45deg);
      color: #98aac3;
      margin: 0 12px 0 10px;
      opacity: 0.6;
    }
    .right-state {
      .plus-txt {
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(152, 170, 195, 1);
      }
      .warn-text {
        font-size: 10px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(241, 62, 62, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px dashed rgba(232, 232, 232, 1);
    background: rgba(251, 252, 253, 1);

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .file-title {
        width: 80%;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          width: 100%;
          display: inline-block;
          vertical-align: middle;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
