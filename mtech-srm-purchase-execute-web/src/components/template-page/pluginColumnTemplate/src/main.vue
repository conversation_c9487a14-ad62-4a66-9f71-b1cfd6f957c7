<template>
  <div class="grid-edit-column mt-flex-direction-column">
    <div
      :class="getColumnClass"
      :style="getColumnStyle"
      @click="componentClickCellTitle()"
      v-if="getConverter['value']"
    >
      <span :class="getConverter['class']" :style="getConverter['style']">
        {{ getConverter['value'] }}</span
      >
    </div>
    <div
      class="column-tool mt-flex invite-btn"
      :style="noTitleTools"
      v-if="geTemplateToolBars.length"
    >
      <div
        :class="['template-svg', { 'icon-disabled': checkOptionIsDiasbled(item) }]"
        v-for="(item, toolbarIndex) in geTemplateToolBars"
        :key="toolbarIndex"
        :name="item.icon"
        v-if="
          checkVisibleCondition(item) &&
          Object.prototype.hasOwnProperty.call(item, 'permission') &&
          Array.isArray(item.permission)
        "
        @click="componentClickCellTool(item)"
        v-permission="getPermission(item)"
      >
        <i
          :class="['mt-icons', 'mt-icon-' + item.icon]"
          v-if="item.icon && ((!!$parent && !$parent.$attrs.freezeOperationColumn) || !$parent)"
        ></i>
        <span class="icon-title">{{ item.title }}</span>
      </div>
      <div
        :class="['template-svg', { 'icon-disabled': checkOptionIsDiasbled(item) }]"
        v-for="(item, toolbarIndex) in geTemplateToolBars"
        :key="toolbarIndex"
        :name="item.icon"
        v-if="
          checkVisibleCondition(item) &&
          !(
            Object.prototype.hasOwnProperty.call(item, 'permission') &&
            Array.isArray(item.permission)
          )
        "
        @click="componentClickCellTool(item)"
      >
        <i
          :class="['mt-icons', 'mt-icon-' + item.icon]"
          v-if="item.icon && ((!!$parent && !$parent.$attrs.freezeOperationColumn) || !$parent)"
        ></i>
        <span class="icon-title">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>

<script>
// import MtIcon from '@mtech-ui/icon'
// import Vue from 'vue'
// import commonPermission from '@mtech/common-permission'
// Vue.use(commonPermission)

export default {
  name: 'PluginColumnTemplate',
  // components: { MtIcon },
  data() {
    return {
      data: {}
    }
  },
  mounted() {},
  computed: {
    // 自定义标题样式
    getColumnClass() {
      if (Object.prototype.hasOwnProperty.call(this.data, 'cssClass')) {
        if (typeof this.data.cssClass === 'function') {
          return this.data.cssClass(this.data)
        } else {
          return this.data.cssClass
        }
      } else {
        return ''
      }
    },
    //
    getColumnStyle() {
      if (Object.prototype.hasOwnProperty.call(this.data, 'cssClass')) {
        if (typeof this.data.cssClass === 'function') {
          return {}
        } else {
          return this.serializeStyle(this.data.cssClass)
        }
      } else {
        return {}
      }
    },
    // 自定义标题样式，通过valueConverter中的map传入
    getConverter() {
      const _value = this.valueConverter
      if (_value && Object.prototype.hasOwnProperty.call(_value, 'cssClass')) {
        // 存在数据序列化，并且序列化数据后，存在cssClass值
        return {
          style: this.serializeStyle(_value.cssClass),
          class: _value.cssClass ?? '',
          value: _value.text
        }
      } else {
        return {
          style: {},
          class: '',
          value: _value
        }
      }
    },
    checkVisibleCondition() {
      return (item) => {
        if (
          Object.prototype.hasOwnProperty.call(item, 'visibleCondition') &&
          typeof item.visibleCondition === 'function'
        ) {
          return item.visibleCondition(this.data)
        } else {
          return true
        }
      }
    },
    // 按钮如果传入了Permission指令值，使用指令
    getPermission() {
      return (item) => {
        if (
          Object.prototype.hasOwnProperty.call(item, 'permission') &&
          Array.isArray(item.permission)
        ) {
          return item.permission
        } else {
          return ['ignore-element']
        }
      }
    },
    // 校验当前按钮是否需要添加‘disable’标记
    checkOptionIsDiasbled() {
      return (item) => {
        const _iconIsVisible = this.checkVisibleCondition(item) // 按照‘可显示的逻辑’，不可显示的置灰
        const _ignoreToolList = [
          'more-option-left-btn',
          'more-option-right-btn',
          'template_preview',
          'template_delete',
          'template_edit'
        ]
        const _iconIsIgnore = _ignoreToolList.indexOf(item.id) > -1 // 按照‘ignoreList’，设置置灰
        return !_iconIsVisible || _iconIsIgnore
      }
    },
    geTemplateToolBars() {
      let toolbarMaps = {}
      if (this._i18n) {
        toolbarMaps = {
          edit: { id: 'edit', icon: 'icon_Editor', title: this.$t('编辑') },
          preview: { id: 'preview', icon: 'icon_Hiddenpassword', title: this.$t('预览') },
          delete: { id: 'delete', icon: 'icon_Delete', title: this.$t('删除') },
          publish: { id: 'publish', icon: 'icon_Share_2', title: this.$t('发布') },
          activation: { id: 'activation', icon: 'icon_Activation', title: this.$t('激活') },
          close: { id: 'close', icon: 'icon_Close_2', title: this.$t('关闭') }
        }
      }
      const getToolBars = (_tools) => {
        if (!_tools) return []
        if (Array.isArray(_tools) && _tools.length > 0) {
          for (const i in _tools) {
            if (typeof _tools[i] === 'string' && _tools[i]) {
              let _t = {}
              if (_tools[i]) {
                const _id = _tools[i].toLowerCase()
                if (Object.prototype.hasOwnProperty.call(toolbarMaps, _id)) {
                  _t = toolbarMaps[_id]
                }
              }
              if (Object.prototype.hasOwnProperty.call(_t, 'icon')) {
                _tools[i] = _t
              }
            }
          }
          return _tools
        } else {
          return []
        }
      }
      let _tools = getToolBars(this.data.cellTools)
      if (
        !this?.$parent?.$attrs?.freezeOperationColumn &&
        this?.$parent?.$attrs?.useToolTemplate &&
        this.data.columnIndex < 2
      ) {
        _tools = this.serializeTools(_tools)
      }

      return _tools
    },
    // 单元格数据序列化 Number、Map、Placeholder、Date、Function
    valueConverter() {
      const _field = this.data.templateField
      let _value = this.data[_field]
      if (_field?.indexOf('.') > -1) {
        const _fieldMap = _field.split('.')
        let _res = null
        for (let i = 0; i < _fieldMap.length; i++) {
          if (i < 1) {
            _res = this.data[_fieldMap[i]]
          } else {
            _res = _res[_fieldMap[i]]
          }
        }
        _value = _res
      }
      if (this.data && Object.prototype.hasOwnProperty.call(this.data, 'valueConverter')) {
        const _convertor = this.data.valueConverter
        let res = ''
        switch (_convertor.type) {
          case 'number':
            if (Object.prototype.hasOwnProperty.call(_convertor, 'digit')) {
              res = this.dataFormat(_value, parseInt(_convertor.digit))
            } else {
              res = this.dataFormat(_value)
            }
            break
          case 'date':
            if (Object.prototype.hasOwnProperty.call(_convertor, 'format')) {
              res = this.dateFormat(_value, _convertor.format)
            } else {
              res = this.dateFormat(_value)
            }
            break
          case 'placeholder':
            if (Object.prototype.hasOwnProperty.call(_convertor, 'placeholder')) {
              res = this.existFormat(_value, _convertor.placeholder)
            } else {
              res = this.existFormat(_value)
            }
            break
          case 'map':
            const _map = Object.prototype.hasOwnProperty.call(_convertor, 'map')
              ? _convertor.map
              : {}
            if (Object.prototype.hasOwnProperty.call(_convertor, 'fields')) {
              res = this.mapFormat(_value, _map, _convertor.fields)
            } else {
              res = this.mapFormat(_value, _map)
            }
            break
          case 'function':
            if (
              Object.prototype.hasOwnProperty.call(_convertor, 'filter') &&
              typeof _convertor.filter === 'function'
            ) {
              res = this.functionFormat(_value, _convertor.filter)
            } else {
              res = this.functionFormat(_value)
            }
            break
          default:
            res = _value
        }
        return res
      } else {
        return _value
      }
    },
    noTitleTools() {
      // return this.getConverter.value ? { 'margin-top': '2px' } : {}
      return {}
    }
  },
  methods: {
    // 序列化样式 主要是title样式
    serializeStyle(item) {
      const colorRgb = function (color) {
        var sColor = color.toLowerCase()
        // 十六进制颜色值的正则表达式
        var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
        // 如果是16进制颜色
        if (sColor && reg.test(sColor)) {
          if (sColor.length === 4) {
            var sColorNew = '#'
            for (var i = 1; i < 4; i += 1) {
              sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
            }
            sColor = sColorNew
          }
          // 处理六位的颜色值
          var sColorChange = []
          for (var j = 1; j < 7; j += 2) {
            sColorChange.push(parseInt('0x' + sColor.slice(j, j + 2)))
          }
          return {
            color: `RGB(${sColorChange.join(',')})`,
            background: `RGBA(${sColorChange.join(',')},0.1)`
          }
        }
        return {}
      }
      if (typeof item === 'string' && item?.indexOf('title-') > -1) {
        const _split = item.split('-')
        if (_split.length > 1) {
          const _seria = colorRgb(_split[1])
          if (Object.prototype.hasOwnProperty.call(_seria, 'color')) {
            const { color, background } = _seria
            return {
              fontSize: '12px',
              padding: '4px',
              borderRadius: '2px',
              color,
              background
            }
          } else {
            return {}
          }
        } else {
          return {}
        }
      } else {
        return {}
      }
    },
    componentClickCellTool(item) {
      const _ignoreToolList = [
        'more-option-left-btn',
        'more-option-right-btn',
        'template_preview',
        'template_delete',
        'template_edit'
      ]
      if (_ignoreToolList.indexOf(item.id) > -1) {
        // 对于_ignore列表中的cellIconId,点击不抛回事件
        return
      }
      const _data = { ...this.data }
      delete _data.cellTools
      delete _data.column
      delete _data.columnIndex
      delete _data.cssClass
      delete _data.foreignKeyData
      delete _data.index
      delete _data.templateField
      delete _data.valueConverter

      delete _data.checkboxState
      delete _data.uniqueID
      delete _data.childRecords
      delete _data.expanded
      delete _data.hasChildRecords
      delete _data.level
      delete _data.taskData
      delete _data.parentUniqueID
      delete _data.parentItem
      this.$parent.$emit('componentClickCellTool', {
        tool: item,
        data: _data,
        componentData: this.data
      })
    },
    componentClickCellTitle() {
      const _data = { ...this.data }
      delete _data.cellTools
      delete _data.column
      delete _data.columnIndex
      delete _data.cssClass
      delete _data.foreignKeyData
      delete _data.index
      delete _data.templateField
      delete _data.valueConverter

      delete _data.checkboxState
      delete _data.uniqueID
      delete _data.childRecords
      delete _data.expanded
      delete _data.hasChildRecords
      delete _data.level
      delete _data.taskData
      delete _data.parentUniqueID
      delete _data.parentItem
      this.$parent.$emit('componentClickCellTitle', {
        field: this.data.templateField,
        data: _data,
        componentData: this.data
      })
    },
    // 序列化数字类型，默认为2位小数点
    dataFormat(value, format = 2) {
      var minus = false
      if (value == null) return '0.00'
      const val = value.toString()
      if (val?.indexOf('-') > -1) {
        value = value.toString().substring(1)
        minus = true
      }
      const s = parseFloat((value + '').replace(/[^\d\.-]/g, '')).toFixed(format) + ''
      const l = s.split('.')[0].split('').reverse()
      const r = s.split('.')[1]
      let f = ''
      for (let i = 0; i < l.length; i++) {
        f += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? ',' : '')
      }
      if (minus) return '-' + f.split('').reverse().join('') + '.' + r
      return f.split('').reverse().join('') + '.' + r
    },
    // 序列化PlaceHolder，默认为'未填'
    existFormat(value, format = '未填') {
      if (!value || value === null || value === '') return format
      else return value
    },
    // 序列化枚举类型，可以自定义fields
    mapFormat(value, map = {}, fields = { text: 'text', value: 'value' }) {
      if (Array.isArray(map)) {
        const _find = map.filter((e) => {
          return e[fields.value] === value
        })
        if (_find.length > 0) {
          // return _find[0][fields.text]
          return { ..._find[0], text: _find[0][fields.text] }
        } else {
          return this.$t('未匹配')
        }
      } else if (typeof map === 'object') {
        if (Object.prototype.hasOwnProperty.call(map, value)) {
          return map[value]
        } else {
          return this.$t('未匹配')
        }
      } else {
        return ''
      }
    },
    // 序列化用户自定义的数据
    functionFormat(
      value,
      func = (e) => {
        return e
      }
    ) {
      if (typeof func === 'function') {
        return func(value, this.data)
      } else {
        return value
      }
    },
    // 序列化日期数据，默认为YYYY-MM-DD
    dateFormat(rawDate, format) {
      if (!rawDate) return ''
      var _date = new Date(rawDate)
      var __Y = _date.getFullYear()
      var __M = _date.getMonth() + 1
      var __D = _date.getDate()
      var __H = _date.getHours()
      var __m = _date.getMinutes()
      var __s = _date.getSeconds()
      /* add possible pre 0 */
      var preM = __M > 9 ? __M.toString() : '0' + __M.toString()
      var preD = __D > 9 ? __D.toString() : '0' + __D.toString()
      var preH = __H > 9 ? __H.toString() : '0' + __H.toString()
      var prem = __m > 9 ? __m.toString() : '0' + __m.toString()
      var pres = __s > 9 ? __s.toString() : '0' + __s.toString()
      var formatted
      switch (format) {
        case 'YYYY-MM-DD HH:mm:ss':
          formatted = __Y + '-' + preM + '-' + preD + ' ' + preH + ':' + prem + ':' + pres
          break
        case 'YYYY-MM-DD':
          formatted = __Y + '-' + preM + '-' + preD
          break
        case 'YYYY/MM/DD HH:mm:ss':
          formatted = __Y + '/' + preM + '/' + preD + ' ' + preH + ':' + prem + ':' + pres
          break
        case 'YYYY/MM/DD':
          formatted = __Y + '/' + preM + '/' + preD
          break
        default:
          formatted = __Y + '-' + preM + '-' + preD
          break
      }
      return formatted
    },
    serializeTools(_tools) {
      const _unuseTemplate = []
      const _toolTemplate = [
        { id: 'template_edit', icon: 'icon_Editor', title: this.$t('编辑') },
        // { id: "template_preview", icon: "icon_Hiddenpassword", title: "查看" },
        { id: 'template_delete', icon: 'icon_Delete', title: this.$t('删除') }
      ]
      _tools.forEach((tool) => {
        const _find = _toolTemplate.filter((temp) => {
          return temp.title === tool.title
        })
        if (_find.length) {
          if (
            Object.prototype.hasOwnProperty.call(tool, 'visibleCondition') &&
            typeof tool.visibleCondition === 'function'
          ) {
            if (tool.visibleCondition(this.data)) {
              // 按钮根据配置规则，如果需要显示，则使用用户配置的按钮
              _toolTemplate.forEach((_t) => {
                if (_t.title === tool.title) {
                  _t.id = tool.id
                  _t.icon = tool.icon
                }
              })
            }
          } else {
            _toolTemplate.forEach((_t) => {
              if (_t.title === tool.title) {
                _t.id = tool.id
                _t.icon = tool.icon
              }
            })
          }
        } else {
          _unuseTemplate.push(tool)
        }
      })
      return _toolTemplate.concat(_unuseTemplate)
    }
  }
}
</script>
<style lang="scss" scoped>
// @import '../themes/dark.scss';
.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}

.field-content {
  color: var(--plugin-ct-content-color);
  font-size: 14px;
  cursor: pointer;
  text-align: left;
  &:hover {
    font-weight: 500;
  }
}
.grid-edit-column {
  height: 40px;
  justify-content: center;
  // padding: 12px 0;
  box-sizing: border-box;
  // overflow-x: auto;
  // overflow-y: hidden;
  // &::-webkit-scrollbar {
  //   display: none;
  //   width: 4px;
  //   height: 4px;
  // }
  // &:hover {
  //   &::-webkit-scrollbar {
  //     display: block;
  //   }
  // }
  .column-tool {
    // margin-top: 8px;
    .template-svg {
      margin-left: 10px;
      line-height: 1;
      &:first-of-type {
        margin-left: 0;
      }
      .mt-icons,
      .icon-title {
        cursor: pointer;
        font-size: 12px;
        color: var(--plugin-ct-cell-icon-color);
      }
      .icon-title {
        font-family: 'Roboto', 'Segoe UI', 'GeezaPro', 'DejaVu Serif', 'sans-serif', '-apple-system',
          'BlinkMacSystemFont';
      }
      &.icon-disabled {
        .mt-icons,
        .icon-title {
          cursor: not-allowed;
          color: var(--plugin-ct-cell-icon-disbaled-color);
        }
      }
    }
  }
}
</style>
