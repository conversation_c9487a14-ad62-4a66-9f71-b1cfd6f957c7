<template>
  <mt-dialog
    ref="columnSettingRef"
    css-class="column-setting"
    :buttons="getButtons"
    @close="cancel"
    :header="$t('字段配置')"
  >
    <div class="dialog-content mt-flex-direction-column">
      <div class="setting-option">
        <div class="toolbar-item" @click="resetColumnSetting">
          <mt-icon name="icon_solid_Reset" />
          <span>{{ $t('恢复默认') }}</span>
        </div>
      </div>
      <div class="setting-content mt-flex">
        <div class="source-columns">
          <mt-listbox
            :data-source="resourceFields"
            :fields="{ text: 'headerText', value: 'field' }"
            :selection-settings="selectionSettings"
            v-model="selectFields"
            @change="changeSetting"
          />
        </div>
        <div class="result-columns mt-flex-direction-column">
          <div class="result-title">{{ $t('已选字段') }}</div>
          <div class="result-content mt-flex-direction-column">
            <div class="result-item mt-flex" v-for="(item, index) in visiblefields" :key="index">
              <div class="item-handler"></div>
              <div class="item-content">{{ item.headerText }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import MtDialog from '@mtech-ui/dialog'
import MtIcon from '@mtech-ui/icon'
import MtListbox from '@mtech-ui/listbox'
export default {
  name: 'PluginColumnSetting',
  components: { MtListbox, MtIcon, MtDialog },
  props: {
    visibleColumns: {
      type: Array,
      default: () => []
    },
    resourceColumns: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectionSettings: {
        mode: 'Single',
        showCheckbox: true,
        showSelectAll: true
      },
      visiblefields: this.visibleColumns,
      resourceFields: this.resourceColumns
    }
  },
  watch: {
    visibleColumns: {
      handler(n) {
        if (n) {
          this.visiblefields = n
          const _cols = []
          n.forEach((e) => {
            _cols.push(e.field)
          })
          this.selectFields = _cols
        }
      },
      immediate: true,
      deep: true
    },
    resourceColumns: {
      handler(n) {
        if (n) {
          this.resourceFields = n
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    getButtons() {
      if (this.visiblefields.length) {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          }
        ]
      } else {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            buttonModel: { disabled: 'true', content: this.$t('保存') }
          }
        ]
      }
    }
  },
  mounted() {
    this.$refs.columnSettingRef.ejsRef.show()
  },
  methods: {
    changeSetting(e) {
      this.visiblefields = e.items
    },
    resetColumnSetting() {
      const _resource = utils.cloneDeep(this.resourceColumns)
      this.visiblefields = _resource
      const _cols = []
      _resource.forEach((e) => {
        _cols.push(e.field)
      })
      this.selectFields = _cols
    },
    cancel() {
      this.$refs.columnSettingRef.ejsRef.hide()
      this.$emit('cancelColumnsDialog')
    },
    confirm() {
      this.$refs.columnSettingRef.ejsRef.hide()
      const _visible = []
      const _hiddens = []
      this.visiblefields.forEach((e) => {
        _visible.push(e.field)
      })
      this.resourceFields.forEach((e) => {
        if (_visible.indexOf(e.field) < 0) {
          _hiddens.push(e.field)
        }
      })
      if (_visible.length) {
        this.$emit('confirmColumnsDialog', { visible: _visible, hidden: _hiddens })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
// @import '../themes/dark.scss';
</style>
