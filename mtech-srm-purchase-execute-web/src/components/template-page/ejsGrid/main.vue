<template>
  <div class="mt-data-grid">
    <ejs-grid
      ref="ejsRef"
      v-bind="propData"
      v-on="$listeners"
      :id="id"
      :data-source="dataSource"
      :grid-lines="gridLines"
      :enable-virtualization="enableVirtualization"
      :page-settings="gridPageSetting"
      :allow-sorting="allowSorting"
      :sort-settings="sortSettings"
      :allow-filtering="allowFiltering"
      :filter-settings="filterSettings"
      :allow-reordering="allowReordering"
      :edit-settings="editSettings"
      :show-column-chooser="showColumnChooser"
      @rowDeselected="rowDeselected"
      @rowSelected="rowSelected"
      @dataBound="dataBound"
      pager-template=""
    >
      <e-columns>
        <e-column
          v-if="customSelection"
          field="customChecked"
          :header-template="allSelectTemplate"
          :template="rowSelectTemplate"
          :is-identity="true"
          :allow-editing="false"
          :allow-reordering="false"
          :allow-searching="false"
          :allow-filtering="false"
          :allow-sorting="false"
          :custom-attributes="{ class: 'custom-checkbox' }"
          width="50"
        ></e-column>
        <e-column
          v-for="(item, index) in columnsVal"
          v-bind="item"
          :key="'mt-col' + index"
          :field="item.field || ''"
          :header-text="item.headerText"
          :width="item.width || 'auto'"
          :text-align="item.textAlign || 'Left'"
          :allow-editing="item.allowEditing"
          :allow-filtering="item.allowFiltering"
          :allow-sorting="item.allowSorting"
          :allow-resizing="item.allowResizing"
          :allow-grouping="item.allowGrouping === undefined ? true : !!item.allowGrouping"
          :allow-reordering="item.allowReordering === undefined ? true : !!item.allowReordering"
          :allow-searching="item.allowSearching === undefined ? true : !!item.allowSearching"
          :auto-fit="item.autoFit || false"
          :columns="item.columns || null"
          :commands="item.commands || null"
          :data-source="item.dataSource || null"
          :default-value="item.defaultValue || null"
          :disable-html-encode="item.disableHtmlEncode || true"
          :display-as-check-box="item.displayAsCheckBox || false"
          :enable-group-by-format="item.enableGroupByFormat || true"
          :filter="item.filter || {}"
          :filter-bar-template="item.filterBarTemplate || null"
          :filter-template="item.filterTemplate || null"
          :foreign-key-field="item.foreignKeyField || null"
          :foreign-key-value="item.foreignKeyValue || null"
          :show-checkbox="item.showCheckbox"
          :format="item.format"
          :formatter="item.formatter || null"
          :freeze="item.freeze || null"
          :header-template="item.headerTemplate || null"
          :header-value-accessor="item.headerValueAccessor || null"
          :hide-at-media="item.hideAtMedia || ''"
          :index="item.index"
          :is-frozen="item.isFrozen || false"
          :is-primary-key="item.isPrimaryKey"
          :is-identity="item.isIdentity || false"
          :lock-column="item.lockColumn"
          :max-width="item.maxWidth"
          :min-width="item.minWidth"
          :show-column-menu="item.showColumnMenu || true"
          :show-in-column-chooser="
            item.showInColumnChooser === undefined ? true : !!item.showInColumnChooser
          "
          :sort-comparer="item.sortComparer"
          :template="item.template"
          :type="item.type"
          :uid="item.uid"
          :validation-rules="item.validationRules"
          :value-accessor="item.valueAccessor"
          :visible="item.visible"
          :edit="item.edit || {}"
          :edit-template="item.editTemplate || ''"
          :edit-type="item.editType || 'default'"
          :clip-mode="item.clipMode || 'EllipsisWithTooltip'"
          :custom-attributes="item.customAttributes || null"
        ></e-column>
      </e-columns>
    </ejs-grid>
    <template v-if="allowPaging">
      <mt-page
        :page-settings="pageSetting"
        :total-pages="total"
        :selected="selectCount"
        :show-selected="showSelected"
        ref="mtPage"
        @currentChange="goToPage"
        @sizeChange="changePageSize"
        @clearAll="clearAll"
        @onNum="onNum"
        :show-page-size="showPageSize"
      ></mt-page>
    </template>
  </div>
</template>

<script>
import Vue from 'vue'
import { emitterMinxins, createEJ2Props } from '@mtech-ui/base'
import MtPage from '@mtech-ui/page'
import * as DataGrid from '@syncfusion/ej2-vue-grids'

const provide = []
for (const i in DataGrid) {
  if (DataGrid[i].prototype && DataGrid[i].prototype.hasOwnProperty('getModuleName')) {
    provide.push(DataGrid[i]) // 属性
  }
}

Vue.use(DataGrid.GridPlugin)

export default {
  name: 'MtDataGrid',
  componentName: 'MtDataGrid',
  components: { MtPage },
  mixins: [emitterMinxins],
  props: {
    id: {
      type: String,
      default: () => {
        return Math.floor(Math.random() * 1000000) + ''
      }
    },
    selectId: {
      type: String,
      required: false,
      default: 'id'
    },
    dataSource: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },
    columnData: {
      type: Array,
      default: () => {
        return []
      }
    },
    allowPaging: {
      type: Boolean,
      required: false,
      default: false
    },
    showPageSize: {
      type: Boolean,
      required: false,
      default: true
    },
    allowSorting: {
      type: Boolean,
      required: false,
      default: false
    },
    allowFiltering: {
      type: Boolean,
      required: false,
      default: false
    },
    allowReordering: {
      type: Boolean,
      required: false,
      default: false
    },
    showColumnChooser: {
      type: Boolean,
      required: false,
      default: false
    },
    showSelected: {
      type: Boolean,
      required: false,
      default: true
    },
    filterSettings: {
      type: Object,
      default: () => {
        return {}
      }
    },
    editSettings: {
      type: Object,
      default: () => {
        return {}
      }
    },
    gridLines: {
      type: String,
      required: false,
      default: 'Default' // None ,Both,Horizontal,Vertical,Default
    },
    sortSettings: {
      type: Object,
      required: false,
      default() {
        return {}
      }
    },
    pageSettings: {
      type: Object,
      required: false,
      default() {
        return {
          // pager显示按钮数，超过显示...
          pageSize: 10,
          pageCount: 8,
          enableQueryString: false,
          pageSizes: [10, 20, 30],
          template: null,

          // 数据总条数
          totalRecordsCount: 0
        }
      }
    },
    radio: {
      type: Boolean,
      required: false,
      default: false
    },
    totalPages: {
      type: Number,
      required: false,
      default: 1
    },
    canFrontPaging: {
      type: Boolean,
      required: false,
      default: true
    },
    enableVirtualization: {
      type: Boolean,
      default: false
    },
    customSelection: {
      type: Boolean,
      defualt: false
    }
  },
  data() {
    return {
      storeIndex: {},
      currentIndex: 1,
      currentIndexOld: 1,
      storeRecords: {},
      selectIdRecords: []
      // allSelectTemplate() {
      //   return { template: allSelectTemplate }
      // },
      // rowSelectTemplate() {
      //   return { template: rowSelectTemplate }
      // }
    }
  },
  provide: {
    grid: provide
  },
  computed: {
    propData() {
      const data = createEJ2Props.call(this, DataGrid.GridComponent)
      return data
    },
    columnsVal() {
      const result = []
      this.columnData.forEach((column) => {
        const columnsObj = {}
        ;['allowEditing', 'allowFiltering', 'allowSorting', 'allowResizing'].forEach((item) => {
          if (column[item] === undefined) {
            columnsObj[item] = true
          }
        })
        result.push(Object.assign({}, column, columnsObj))
      })
      return result
    },
    pageSetting() {
      const result = Object.assign(
        {},
        {
          pageSize: 10,
          pageCount: 5,
          enableQueryString: false,
          pageSizes: [10, 20, 30],
          template: null
        },
        this.pageSettings
      )

      return result
    },
    gridPageSetting() {
      let _pageSize = this?.$attrs?.virtualPageSize ? this.$attrs.virtualPageSize : 30
      return { pageSize: _pageSize }
    },
    total() {
      let result = 1
      if (this.allowPaging) {
        result = Math.ceil(this.pageSetting.totalRecordsCount / this.pageSetting.pageSize)
      }
      return result
    },
    selectCount() {
      return this.selectIdRecords.length
    },
    customSelectedRowIndexs() {
      const arr = []
      this.dataSource.forEach((el, index) => {
        if (el.customChecked) arr.push(index)
      })
      return arr
    },
    allSelectTemplate() {
      const self = this
      const allSelectTemplate = {
        template: `
          <div class="template_checkbox">
              <input ref="allCheckbox" type="checkbox" v-model="isAllSelected" />
          </div>
        `,
        data() {
          return {
            data: {}
          }
        },
        computed: {
          isAllSelected: {
            get() {
              const res = self.dataSource.every((el) => el.customChecked)
              if (!res && self.dataSource.some((el) => el.customChecked)) {
                const checkboxDom = this.$refs.allCheckbox
                if (checkboxDom) checkboxDom.indeterminate = true
              } else {
                const checkboxDom = this.$refs.allCheckbox
                if (checkboxDom) checkboxDom.indeterminate = false
              }
              return res && self.dataSource.length
            },
            set(val) {
              self.dataSource.forEach((item) => {
                this.$set(item, 'customChecked', val)
              })
              self.$refs.ejsRef.refresh()
            }
          }
        }
      }
      return () => {
        return {
          template: allSelectTemplate
        }
      }
    },
    rowSelectTemplate() {
      const self = this
      const rowSelectTemplate = {
        template: `
          <div class="template_checkbox">
              <input type="checkbox" :data-id="data.id" v-model="isCurSelected" />
          </div>
        `,
        data() {
          return {
            data: {}
          }
        },
        computed: {
          isCurSelected: {
            get() {
              return this.data.customChecked
            },
            set(val) {
              // 存在id时
              if (self.dataSource[0]['id']) {
                const sourceItem = self.dataSource.find((item) => item.id === this.data.id)
                this.$set(sourceItem, 'customChecked', val)
              } else {
                this.$set(self.dataSource[this.data.index], 'customChecked', val)
              }
            }
          }
        }
      }
      return () => {
        return {
          template: rowSelectTemplate
        }
      }
    }
  },
  created() {
    if (this.enableVirtualization && this.showSelected) {
      this.showSelected = false
    }
  },
  watch: {
    selectIdRecords: {
      handler(v) {
        console.log('selectIdRecords', v)
      },
      deep: true
    },
    enableVirtualization(val) {
      console.log('111111111111', val)
    }
  },
  methods: {
    clearCustomSelection() {
      const viewRecords = this.$refs.ejsRef.getCurrentViewRecords()
      for (let i = 0; i < viewRecords.length; i++) {
        this.$set(viewRecords[i], 'customChecked', false)
        this.$set(this.dataSource[i], 'customChecked', false)
      }
      this.$refs.ejsRef.refresh()
    },
    onNum() {
      this.$emit('onNum', this.selectIdRecords)
    },
    clearAll() {
      this.selectIdRecords = []
      this.refresh()
    },
    rowDeselected(e) {
      console.log('rowDeselected', e, e.data instanceof Object)
      if (!e.isInteracted) return
      if (e.data instanceof Array && e.isHeaderCheckboxClicked == false) return
      if (e.data instanceof Object && e.target.tagName == 'BUTTON') return
      if (e.data instanceof Array) {
        this.selectIdRecords = this.ReduceArray(this.selectIdRecords, e.data)
      } else {
        this.selectIdRecords = this.ReduceArray(this.selectIdRecords, [e.data])
      }

      console.log('selectIdRecords', this.selectIdRecords)
    },
    rowSelected(e) {
      console.log('rowSelected', e)
      if (!e.isInteracted) return
      if (this.radio) {
        this.selectIdRecords = [e.data]
        return
      }
      if (e.data instanceof Array) {
        this.selectIdRecords = this.MergeArray(this.selectIdRecords, e.data)
      } else {
        this.selectIdRecords = this.MergeArray(this.selectIdRecords, [e.data])
      }
      console.log('selectIdRecords', this.selectIdRecords)
    },
    ReduceArray(arr1, arr2) {
      for (let i = arr1.length - 1; i >= 0; i--) {
        let a = arr1[i]
        for (let j = arr2.length - 1; j >= 0; j--) {
          let b = arr2[j]
          if (a[this.selectId] && b[this.selectId]) {
            if (a[this.selectId] == b[this.selectId]) {
              arr1.splice(i, 1)
              arr2.splice(j, 1)
              break
            }
          } else {
            if (JSON.stringify(a) == JSON.stringify(b)) {
              arr1.splice(i, 1)
              arr2.splice(j, 1)
              break
            }
          }
        }
      }
      return arr1
    },
    MergeArray(arr1, arr2) {
      console.log('MergeArray', arr1, arr2)
      let _arr = new Array()
      for (let i = 0; i < arr1.length; i++) {
        _arr.push(arr1[i])
      }
      for (let i = 0; i < arr2.length; i++) {
        let flag = true
        for (let j = 0; j < arr1.length; j++) {
          if (arr2[i][this.selectId] && arr2[i][this.selectId]) {
            if (arr2[i][this.selectId] == arr1[j][this.selectId]) {
              flag = false
              break
            }
          } else {
            if (JSON.stringify(arr2[i]) == JSON.stringify(arr1[j])) {
              flag = false
              break
            }
          }
        }
        if (flag) {
          _arr.push(arr2[i])
        }
      }
      return _arr
    },
    dataBound() {
      // 允许跨页勾选且未启用虚拟滚动
      if (this.showSelected && !this.enableVirtualization) {
        let arrIndex = new Array()
        let arr = new Array()
        if (this.canFrontPaging) {
          arr = this.$refs.ejsRef.getCurrentViewRecords()
        } else {
          arr = this.dataSource
        }
        this.selectIdRecords.forEach((item) => {
          arr.forEach((item2, j) => {
            if (item[this.selectId]) {
              if (item[this.selectId] == item2[this.selectId]) {
                arrIndex.push(j)
              }
            } else {
              if (JSON.stringify(item) == JSON.stringify(item2)) {
                arrIndex.push(j)
              }
            }
          })
        })
        // this.selectIdRecords.forEach((item,i)=>{
        //   this.dataSource.forEach((item2,j)=>{
        //     if(item[this.selectId]){
        //       if(item[this.selectId]==item2[this.selectId]){
        //         arrIndex.push(j)
        //       }
        //     } else {
        //       if(JSON.stringify(item)==JSON.stringify(item2)){
        //         arrIndex.push(j)
        //       }
        //     }
        //   })
        // })
        this.$refs.ejsRef.selectRows(arrIndex)
      }
    },
    refresh() {
      this.$refs.ejsRef.ej2Instances.refresh()
    },
    goNext(index) {
      this.$emit('nextClick', index)
    },
    goPrev(index) {
      this.$emit('prevClick', index)
    },
    goToPage(index) {
      // this.currentIndexOld = this.currentIndex
      this.$refs.ejsRef.ej2Instances.goToPage(index)
      // this.storeIndex[`local-${this.currentIndexOld}`] = this.$refs.ejsRef.getSelectedRowIndexes()
      // this.storeRecords[`local-${this.currentIndexOld}`] = this.$refs.ejsRef.getSelectedRecords()
      // console.log('selectId',this.selectId);
      this.$emit('currentChange', index)
      // this.currentIndex = index
    },
    changePageSize(count) {
      this.pageSettings.pageSize = count
      this.$emit('sizeChange', count)
    },
    getMtechGridRecords() {
      return this.selectIdRecords
    },
    getCustomSelectedRows() {
      return this.dataSource.filter((el) => el.customChecked)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .template_checkbox {
    text-align: left;
    input[type='checkbox'] {
      visibility: visible;
      width: 16px;
      height: 16px;
    }
  }
}
</style>
