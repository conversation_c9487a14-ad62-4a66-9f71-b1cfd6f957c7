<template>
  <div class="toolbar-container mt-flex invite-btn">
    <div class="mt-flex" v-for="(tool, index) in getToolBar" :key="getIds([index])">
      <div
        :class="[
          'toolbar-item',
          { 'icon-disabled': checkOptionIsDiasbled(base) },
          { 'custom-btn': useRightToolBarRule }
        ]"
        v-for="(base, _index) in tool.base"
        :key="getIds([index, _index])"
        :id="getIds([index, _index])"
        :tabIndex="_index"
        @click="componentClickToolBar($event, base)"
        @blur="hiddenExtToolbar"
        v-permission="getPermission(base)"
        :type="base.id == 'more-option-left-btn' ? 'default' : ''"
      >
        <!-- <mt-icon v-if="base.icon && !useRightToolBarRule" :name="base.icon" /> -->
        <span class="toolbar-btn" v-if="base.id == 'Filter'" v-waves
          >{{ $attrs.showQueryBuilder ? $t('关闭') : $t('打开') }}{{ base.title }}</span
        >
        <span class="toolbar-btn" v-else-if="base.id == 'QuickSearch'" v-waves>{{
          $attrs.showQuickSearch ? $t('关闭快捷搜索') : $t('打开快捷搜索')
        }}</span>
        <!-- default是蓝底白字，info是白底黑字，primary是黑底白字 -->
        <!-- 把箭头icon跟更多操作文案合并为一个按钮 -->
        <span
          :class="[
            'toolbar-btn',
            { 'e-ddl-icon': base.title === $t('更多操作') },
            { 'up-arrow': showLeftExtToolbar && base.title === $t('更多操作') }
          ]"
          v-else
          v-waves
          :type="
            index === getToolBar.length - 1
              ? _index === tool.base.length - 1
                ? 'primary'
                : 'info'
              : 'default'
          "
          >{{ base.title }}</span
        >
        <!-- 左侧拓展按钮 -->
        <!-- <span
          class="toolbar-btn"
          v-if="base.id == 'more-option-left-btn'"
          v-waves
          type="default"
          :class="['e-ddl-icon', { 'up-arrow': showLeftExtToolbar }]"
        >
        </span> -->
        <!-- 右侧拓展按钮 -->
        <span
          class="toolbar-btn"
          v-waves
          v-if="base.id == 'more-option-right-btn'"
          :class="['e-ddl-icon', { 'up-arrow': showRightExtToolbar }]"
        >
        </span>
        <!-- 左侧拓展按钮展开后的内容 -->
        <div
          class="ext-toolbar-container"
          v-if="base.id == 'more-option-left-btn' && showLeftExtToolbar"
        >
          <div
            :class="['toolbar-item', { 'icon-disabled': checkOptionIsDiasbled(ext) }]"
            v-for="(ext, _extIndex) in tool.ext"
            :key="getIds([index, _index, _extIndex])"
            :id="getIds([index, _index, _extIndex])"
            @click.stop="componentClickToolBar($event, ext)"
            v-permission="getPermission(ext)"
            type="default"
          >
            <!-- <mt-icon v-if="ext.icon && !useRightToolBarRule" :name="ext.icon" /> -->
            <span v-if="ext.id == 'QuickSearch'" class="toolbar-btn" v-waves>{{
              $attrs.showQuickSearch ? $t('关闭快捷搜索') : $t('打开快捷搜索')
            }}</span>
            <span v-else class="toolbar-btn" type="info" v-waves>{{ ext.title }}</span>
          </div>
        </div>
        <!-- 右侧拓展按钮展开后的内容 -->
        <div
          class="ext-toolbar-container"
          v-if="base.id == 'more-option-right-btn' && showRightExtToolbar"
        >
          <div
            :class="['toolbar-item', { 'icon-disabled': checkOptionIsDiasbled(ext) }]"
            v-for="(ext, _extIndex) in tool.ext"
            :key="getIds([index, _index, _extIndex])"
            :id="getIds([index, _index, _extIndex])"
            @click.stop="componentClickToolBar($event, ext)"
            v-permission="getPermission(ext)"
          >
            <!-- 暂时屏蔽掉按钮icon -->
            <!-- <mt-icon v-if="ext.icon && !useRightToolBarRule" :name="ext.icon" /> -->
            <span v-if="ext.id == 'QuickSearch'" class="toolbar-btn" v-waves>{{
              $attrs.showQuickSearch ? $t('关闭快捷搜索') : $t('打开快捷搜索')
            }}</span>
            <span v-else class="toolbar-btn" v-waves>{{ ext.title }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  name: 'PluginToolbar',
  props: {
    useToolTemplate: {
      type: Boolean,
      default: true
    },
    buttonQuantity: {
      type: Number,
      default: 5
    },
    useCombinationSelection: {
      type: Boolean,
      default: true
    },
    useRightToolBarRule: {
      type: Boolean,
      default: false
    },
    // 是否使用自定义快捷查询（当使用自定义快捷查询时需要屏蔽一些按钮）
    isUseCustomSearch: {
      type: Boolean,
      default: false
    },
    // 是否使用自定义快捷查询入参规则（需要屏蔽高级查询按钮）
    isCustomSearchRules: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showLeftExtToolbar: false, // 显示左侧更多操作
      showRightExtToolbar: false // 显示右侧更多操作
    }
  },
  computed: {
    // 按钮如果传入了visible逻辑，控制样式
    checkVisibleCondition() {
      return (item) => {
        if (
          Object.prototype.hasOwnProperty.call(item, 'visibleCondition') &&
          typeof item.visibleCondition === 'function'
        ) {
          return item.visibleCondition(this.data)
        } else {
          return true
        }
      }
    },
    // 按钮如果传入了Permission指令值，使用指令
    getPermission() {
      return (item) => {
        if (
          Object.prototype.hasOwnProperty.call(item, 'permission') &&
          Array.isArray(item.permission)
        ) {
          return item.permission
        } else {
          return ['ignore-element']
        }
      }
    },
    // 校验当前按钮是否需要添加‘disable’标记
    checkOptionIsDiasbled() {
      return (item) => {
        const _iconIsVisible = this.checkVisibleCondition(item) // 按照‘可显示的逻辑’，不可显示的置灰
        const _ignoreToolList = ['template_add', 'template_delete', 'template_edit']
        const _iconIsIgnore = _ignoreToolList.indexOf(item.id) > -1 // 按照‘ignoreList’，设置置灰
        return !_iconIsVisible || _iconIsIgnore
      }
    },
    // 处理顶部操作按钮的参数
    getToolBar() {
      let _res = this.$attrs.toolbarConfig ? this.serializeToolbar(this.$attrs.toolbarConfig) : []
      if (this.useRightToolBarRule) {
        return this.serializeToolBarWithCustomRule(_res)
      } else {
        if (this.useToolTemplate) {
          // 使用模板中固定的‘新增、删除、编辑’操作
          _res = this.serializeTemplateTools(_res)
        }
        return this.serializeToolBarWithMoreOption(_res)
      }
    }
  },
  methods: {
    hiddenExtToolbar() {
      this.showLeftExtToolbar = false
      this.showRightExtToolbar = false
    },
    getIds(ids) {
      return `toolbar-${[...ids].join('-')}`
    },
    // 初始格式化化Toolbar，使toolbar数组中的数据格式规范
    serializeToolbar(_tools) {
      if (!this.useCombinationSelection && _tools.length == 2) {
        _tools[1] = _tools[1].filter((e) => e !== 'combinationSearch')
      }
      const toolbarMaps = {
        add: { id: 'Add', icon: 'icon_solid_Createorder', title: this.$t('新增') },
        edit: { id: 'Edit', icon: 'icon_solid_edit', title: this.$t('编辑') },
        delete: { id: 'Delete', icon: 'icon_solid_Delete', title: this.$t('删除') },
        options: { id: 'Options', icon: 'icon_solid_Operation', title: this.$t('操作') },
        filter: { id: 'Filter', icon: 'icon_solid_Filter', title: this.$t('筛选') },
        export: { id: 'Export', icon: 'icon_solid_Download', title: this.$t('导出') },
        refresh: { id: 'Refresh', icon: 'icon_solid_Refresh', title: this.$t('刷新') },
        setting: { id: 'Setting', icon: 'icon_solid_Settingup', title: this.$t('设置') },
        quicksearch: {
          id: 'QuickSearch',
          icon: 'icon_solid_Filter',
          title: this.$t('打开快捷搜索')
        },
        combinationsearch: {
          id: 'CombinationSearch',
          icon: 'icon_table_filter',
          title: this.$t('高级查询')
        },
        resetsearch: { id: 'ResetSearch', icon: 'icon_table_clean', title: this.$t('清除查询') }
      }
      for (const i in _tools) {
        const _tool = _tools[i]
        if (Array.isArray(_tool)) {
          if (_tool.length > 0) {
            for (const j in _tool) {
              if (typeof _tool[j] === 'string' && _tool[j]) {
                let _t = {}
                if (_tool[j]) {
                  const _id = _tool[j].toLowerCase()
                  if (Object.prototype.hasOwnProperty.call(toolbarMaps, _id)) {
                    _t = toolbarMaps[_id]
                  }
                }
                if (Object.prototype.hasOwnProperty.call(_t, 'id')) {
                  _tool[j] = _t
                }
              }
            }
          }
        } else {
          _tools = this.serializeToolbar([_tools])
          break
        }
      }
      // 当使用自定义快捷查询时不显示“打开快捷搜索”按钮和“清除查询按钮”
      if (this.isUseCustomSearch) {
        let ids = ['QuickSearch', 'ResetSearch']
        if (this.isCustomSearchRules) {
          ids = ['QuickSearch', 'ResetSearch', 'CombinationSearch']
        }
        ids.forEach((el) => {
          const idx = _tools[1]?.findIndex((item) => item.id === el)
          if (idx > -1) _tools[1].splice(idx, 1)
        })
      }

      return _tools
    },
    // 使用toolTempalte时，序列化‘新增、编辑、删除’。只处理左侧tools
    serializeTemplateTools(_tools) {
      const serializeTools = (_tools) => {
        const _unuseTemplate = []
        const _toolTemplate = [
          { id: 'template_add', icon: 'icon_solid_Createorder', title: this.$t('新增') },
          { id: 'template_delete', icon: 'icon_solid_Delete', title: this.$t('删除') }
          // { id: "template_edit", icon: "icon_solid_edit", title: this.$t("编辑") }
        ]
        _tools.forEach((tool) => {
          const _find = _toolTemplate.filter((temp) => {
            return temp.title === tool.title
          })
          if (_find.length) {
            _toolTemplate.forEach((_t) => {
              if (_t.title === tool.title) {
                _t.id = tool.id
                _t.icon = tool.icon
              }
            })
          } else {
            _unuseTemplate.push(tool)
          }
        })
        return _toolTemplate.concat(_unuseTemplate)
      }
      const _res = []
      for (let i = 0; i < _tools.length; i++) {
        if (i < 1) {
          _res.push(serializeTools(_tools[i]))
        } else {
          _res.push(_tools[i])
        }
      }
      return _res
    },
    // TCL定制化 按钮操作
    serializeToolBarWithCustomRule(_tools) {
      const _res = []
      if (Array.isArray(_tools)) {
        _res.push([])
        if (_tools.length === 2) {
          const _left = utils.cloneDeep(_tools[0])
          const _right = utils.cloneDeep(_tools[1])
          if (Array.isArray(_right) && _right.length) {
            // 将所有右侧按钮，都作为‘设置’按钮内部操作
            _res.push({
              base: [
                {
                  id: 'more-option-right-btn',
                  icon: 'icon_solid_Configuration',
                  title: this.$t('设置')
                }
              ].concat(_left),
              ext: _right // 右侧按钮，作为扩展操作
            })
          } else {
            _res.push({
              base: _left,
              ext: []
            })
          }
        } else if (_tools.length === 1) {
          _res.push({
            base: _tools[0],
            ext: []
          })
        }
      }
      return _res
    },
    serializeToolBarWithMoreOption(_tools) {
      const _res = []
      let userPermission = window.elementPermissionSet
      _tools.forEach((e, index) => {
        if (e.length > this.buttonQuantity) {
          const _base = []
          const _ext = []
          for (let i = 0; i < e.length; i++) {
            if (Object.hasOwnProperty.call(e[i], 'permission')) {
              let permissionArray = e[i].permission.filter((v) => userPermission.includes(v))
              if (permissionArray.length === 0) {
                continue
              }
            }
            if (_base.length < this.buttonQuantity - 1) {
              _base.push(e[i])
            } else {
              _ext.push(e[i])
            }
          }
          if (_ext.length > 0 && index === 0) {
            _base.push({
              id: 'more-option-left-btn',
              icon: 'icon_solid_Configuration',
              title: this.$t('更多操作')
            })
          } else if (_ext.length > 0 && index != 0) {
            _base.push({
              id: 'more-option-right-btn',
              icon: 'icon_solid_Configuration',
              title: this.$t('更多操作')
            })
          }
          _res.push({
            data: e,
            base: _base,
            ext: _ext
          })
        } else {
          _res.push({
            data: e,
            base: e,
            ext: []
          })
        }
      })
      return _res
    },
    // 点击顶部按钮操作
    componentClickToolBar(e, temp) {
      // 重复点击‘更多操作’，是做底部的list切换
      if (temp.id == 'more-option-left-btn') {
        this.showLeftExtToolbar = !this.showLeftExtToolbar
      } else {
        this.showLeftExtToolbar = false
      }
      if (temp.id == 'more-option-right-btn') {
        this.showRightExtToolbar = !this.showRightExtToolbar
      } else {
        this.showRightExtToolbar = false
      }
      // this.showExtToolbar = (temp.id == 'more-option-btn');
      const _ignoreToolList = [
        'more-option-left-btn',
        'more-option-right-btn',
        'template_preview',
        'template_delete',
        'template_edit'
      ]
      if (_ignoreToolList.indexOf(temp.id) > -1) {
        // 对于_ignore列表中的cellIconId,点击不抛回事件
        return
      }
      if (this.checkOptionIsDiasbled(temp)) {
        // 当前按钮不可用，不抛出事件
        return
      }
      this.$emit('componentClickToolBar', { event: e, toolbar: temp })
    },
    // 点击顶部按钮-Tips
    componentClickToolTips(temp) {
      this.showLeftExtToolbar = temp.id == 'more-option-left-btn'
      this.showRightExtToolbar = temp.id == 'more-option-right-btn'
      if (typeof temp?.tips?.click === 'function') {
        temp?.tips?.click()
        return
      }
      this.$emit('componentClickToolTips', temp)
    },
    // 点击顶部按钮-Marker
    componentClickToolMarker(temp) {
      this.showLeftExtToolbar = temp.id == 'more-option-left-btn'
      this.showRightExtToolbar = temp.id == 'more-option-right-btn'
      if (typeof temp?.marker?.click === 'function') {
        temp?.marker?.click()
        return
      }
      this.$emit('componentClickToolMarker', temp)
    }
  }
}
</script>
<style lang="scss" scoped>
// @import '../themes/dark.scss';
.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}
.toolbar-container {
  justify-content: space-between;
  height: 50px;
  box-sizing: border-box;
  align-items: center;
  background: var(--plugin-tb-bg-ff);
  padding: 0 12px;
  flex-shrink: 0;

  .toolbar-item {
    min-width: auto;
    line-height: 1;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    display: flex;
    align-items: center;
    position: relative;
    border-radius: 5px;
    // background: #19a2d5;
    color: #fff;
    box-shadow: 1px 1px 4px 0px #aaa;

    &.custom-btn {
      background: #fff;
      height: 30px;
      padding: 0 10px;
      border-radius: 4px;

      // &:hover {
      //   background: #fff;
      //   color: #4a556b;
      //   .toolbar-btn {
      //     color: #4a556b;
      //   }
      // }
      &.icon-disabled {
        &:hover {
          background: #fff;
          span {
            color: #fff;
            background: #d3d4d6;
            border: 1px solid #d3d4d6;
          }
        }
      }
    }
    .mt-icons {
      font-size: 14px;
      color: white;
      margin-right: 5px;
    }
    // 按钮公共样式, 此处是type=info的样式
    .toolbar-btn {
      display: block;
      height: 28px;
      line-height: 26px;
      padding: 0 16px;
      border-radius: 4px;
      font-weight: 400;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      user-select: none;
      border: 1px solid #4a556b;
      // background: #19a2d5;
      color: #4a556b;
      white-space: nowrap;
      box-sizing: border-box;
      cursor: pointer;
    }
    // 未禁用按钮样式
    .toolbar-btn[type='primary'] {
      background: #4a556b;
      border: 1px solid #4a556b;
      color: #fff;
    }
    .toolbar-btn[type='default'] {
      background: #fff;
      border: 1px solid #2783fe;
      color: #4a556b;
    }
    &:first-of-type {
      margin-left: 0;
    }
    // &:hover {
    //   .mt-icons,
    //   span {
    //     color: white;
    //   }
    //   .tip-class,
    //   .marker-class {
    //     color: auto;
    //   }
    // }
    .e-ddl-icon {
      position: relative;
      &:before {
        content: '\e969';
        font-family: e-icons;
        position: relative;
        margin-left: 9px;
        font-size: 12px;
        top: 2px;
      }
      &.up-arrow {
        display: inline-flex;
        &::before {
          transform: rotate(180deg);
        }
      }
    }
    // 禁用按钮样式
    &.icon-disabled {
      // border: 1px solid #d3d4d6;
      cursor: not-allowed;
      .mt-icons,
      span.toolbar-btn {
        border-color: #a6d2ff;
        color: #c5c7ca;
      }
      // &:hover {
      //   cursor: not-allowed;
      //   .mt-icons,
      //   span {
      //     color: white;
      //   }
      // }
    }
  }
  // 左侧拓展下拉内按钮样式
  .toolbar-item[type='default'] {
    box-shadow: unset;
    .toolbar-btn {
      background: #fff;
      border: 1px solid #2783fe;
      color: #4a556b;
      box-shadow: 1px 1px 4px 0px #aaa;
    }
    .e-ddl-icon {
      position: relative;
      &:before {
        display: none;
      }
      &.up-arrow {
        display: none;
      }
    }
    .e-ddl-icon {
      position: relative;
      &:after {
        content: '\e969';
        font-family: e-icons;
        position: relative;
        margin-left: 9px;
        font-size: 12px;
        top: 2px;
      }
      &.up-arrow {
        display: inline-flex;
        &::after {
          transform: rotate(180deg);
        }
      }
    }
  }
  .ext-toolbar-container {
    position: absolute;
    min-width: 100px;
    background: white;
    border: 1px solid var(--plugin-tb-border-color);
    border-radius: 2px;
    box-shadow: 0 4px 10px 0 var(--plugin-tb-tool-container-shadow-color);
    padding: 16px 20px;
    left: -25px;
    top: 20px;
    z-index: 3;
    text-align: left;

    .toolbar-item {
      margin-left: 0;
      margin-top: 12px;
      // background: #4a556b;
      // padding: 5px 8px;
      color: white;
      border-radius: 5px;
      &:first-of-type {
        margin-top: 0;
      }
      // &:hover {
      //   span {
      //     font-weight: 600;
      //   }
      // }
    }
    .icon-disabled {
      cursor: not-allowed;
      .mt-icons,
      span {
        color: white;
        background: #d3d4d6;
        border: 1px solid #d3d4d6;
      }
      // &:hover {
      //   cursor: not-allowed;
      //   .mt-icons,
      //   span {
      //     color: white;
      //   }
      // }
    }
  }
}
</style>
