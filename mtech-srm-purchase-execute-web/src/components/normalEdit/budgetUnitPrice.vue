<template>
  <div>
    <!-- 预算单价（未税) -->
    <mt-input id="budgetUnitPrice" v-model="data.budgetUnitPrice" style="display: none"></mt-input>
    <mt-inputNumber
      v-model="data.budgetUnitPrice"
      :min="0"
      :precision="2"
      placeholder=""
      :step="3"
      :show-clear-button="false"
      @input="handleChange"
      v-if="allowEditing"
    ></mt-inputNumber>
    <span v-if="!allowEditing">{{ data.budgetUnitPrice }}</span>
  </div>
</template>
<script>
var bigDecimal = require('js-big-decimal')
export default {
  data() {
    return {
      quantity: '',
      allowEditing: true
    }
  },
  mounted() {
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    this.quantity = this.data.quantity
    this.$bus.$on('updateQuantity1', (e) => {
      this.quantity = e
    })
  },
  methods: {
    handleChange(e) {
      this.$bus.$emit('updateBudgetUnitPrice', e)
      if (!e) {
        this.$bus.$emit('budgetTotalPriceChange', '0.00')
      }
      if (this.quantity) {
        if (!e) return
        let budgetTotalPrice = bigDecimal.multiply(this.quantity, e)
        budgetTotalPrice = bigDecimal.round(budgetTotalPrice, 2)
        this.$bus.$emit('budgetTotalPriceChange', budgetTotalPrice)
      }
    }
  }
}
</script>
