<template>
  <!-- 成本中心 -->
  <div>
    <mt-input
      :id="data.column.field"
      :value="data[data.column.field]"
      :disabled="true"
      v-show="!data.column.allowEditing"
    ></mt-input>
    <select-filter
      v-show="data.column.allowEditing"
      id="costSharingAccName"
      :width="280"
      :fields="fields"
      :request-url="requestUrl"
      :request-key="requestKey"
      :init-val.sync="data['costSharingAccName']"
      :other-params="otherParams"
      :label-show-obj="labelShowObj"
      @handleChange="handleChange"
    ></select-filter>
  </div>
</template>

<script>
export default {
  computed: {
    prCompanyInfo() {
      let _prCompanyInfo
      if (this.$route.path.includes('pr-apply')) {
        // 采购申请
        _prCompanyInfo = this.$store.state.prCompanyInfo
      } else if (this.$route.path.includes('source-apply')) {
        // 寻源申请
        _prCompanyInfo = this.$store.state.sourceCompanyInfo
      }
      return _prCompanyInfo
    }
  },
  watch: {
    prCompanyInfo: {
      handler(newVal) {
        this.otherParams = {
          companyCode: newVal.companyCode
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      data: {},
      fields: {
        text: 'labelShow',
        value: 'labelShow'
      },
      requestUrl: {
        pre: 'masterData',
        url: 'postCostCenterCriteriaQuery'
      },
      requestKey: 'costData',
      otherParams: {},
      labelShowObj: {
        code: 'costCenterCode',
        name: 'costCenterDesc'
      }
    }
  },
  mounted() {
    this.otherParams = { companyCode: this.prCompanyInfo?.companyCode }
  },
  methods: {
    handleChange(e) {
      console.log(this.$t('接受到了变化'), e)
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'costSharingAccName',
        itemInfo: {
          // 因为这个传参 整体是个对象
          costSharing: {
            id: e.itemData?.id,
            costSharingAccCode: e.itemData?.costCenterCode,
            costSharingAccName: e.itemData?.costCenterName
          }
        }
      })
    }
  }
}
</script>

<style></style>
