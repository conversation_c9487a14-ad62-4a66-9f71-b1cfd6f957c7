<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <!-- 选择物料/品项编码、SKU -->
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="headerTxt"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { itemCodeColumnData, skuCodeColumnData } from './config/pcSelection.js' // 命名要与field code一致
import { PROXY_MDM_TENANT } from '@/utils/constant'
export default {
  data() {
    return {
      data: {
        // itemCode: null,
      },
      fieldName: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [],
      itemCodeColumnData,
      skuCodeColumnData,
      fields: {},
      headerTxt: '',
      requestUrl: [], // 因为主数据的物料和sku的下拉和弹窗是两个接口给的，所以这里有两个值。第一个是下拉的，第二个是弹窗的
      changedFieldArr: [],
      changedRowArr: [],
      clearFieldArr: [],
      clearRowArr: [],
      allowEditing: true,
      dialogShow: false
    }
  },
  mounted() {
    this.fieldName = this.data.column.field

    let _nowCellSessionInfo = null
    if (this.$route.path.includes('pr-apply') || this.$route.path.includes('source-apply')) {
      _nowCellSessionInfo = sessionStorage.getItem(`pr${this.fieldName}Data`) // 采购申请的
    } else {
      _nowCellSessionInfo = sessionStorage.getItem(`${this.fieldName}Data`) // 采购订单的
    }
    // console.log(this.data.column.allowEditing, "我是组的额范德萨发");
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return
    if (_nowCellSessionInfo) {
      _nowCellSessionInfo = JSON.parse(_nowCellSessionInfo)
      this.headerTxt = _nowCellSessionInfo?.title
      this.requestUrl = _nowCellSessionInfo?.requestUrl
      this.changedFieldArr = _nowCellSessionInfo?.changedFieldArr
      this.changedRowArr = _nowCellSessionInfo?.changedRowArr // 其实已经列出来了，可以不用这个字段
      this.clearFieldArr = _nowCellSessionInfo?.clearFieldArr
      this.clearRowArr = _nowCellSessionInfo?.clearRowArr
    }

    this.initDialog()

    // 物料、sku改变时，也会被清空或改变
    this.$bus.$off(`${this.fieldName}Change`) // 为了解决textWrapper拿不到的问题
    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      // if (!this.fieldName) return;
      console.log('￥emit的监听到了SKU物料组件------', this.fieldName, txt)
      this.$set(this.data, this.fieldName, txt)
    })
  },

  methods: {
    // 双击物料行，也进行提交
    recordDoubleClick(args) {
      console.log('recordDoubleClick', args)
      this.confirm([args.rowData])
    },

    // 提交
    confirm(records) {
      let _records = records
      if (!_records || !_records.length) {
        _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }

      if (!_records.length) return

      console.log('选择到的物料信息：', _records[0])
      if (_records[0]) {
        let selectedRowInfo = _records[0]
        // 如果是sku，需要整合一下数据源，将物料信息展开
        if (this.fieldName == 'skuCode') {
          selectedRowInfo = {
            // ...selectedRowInfo,
            itemId: selectedRowInfo?.item?.id, // 物料数据
            itemCode: selectedRowInfo?.item?.itemCode,
            itemName: selectedRowInfo?.item?.itemName,
            skuId: selectedRowInfo?.id, // sku数据
            skuCode: selectedRowInfo?.barCode,
            skuName: selectedRowInfo?.name,
            // 因为sku能带出物料，返回的数据 结构在item里面
            spec: selectedRowInfo?.item?.itemDescription, // 规格型号(采购申请)
            specification: selectedRowInfo?.item?.itemDescription, // 规格型号(采购订单)
            itemGroupCode: selectedRowInfo?.item?.itemGroupCode, //品项组编码
            itemGroupName: selectedRowInfo?.item?.itemGroupName, //品项组名称
            unitName: selectedRowInfo?.item?.baseMeasureUnitName, // 基本单位
            unitId: selectedRowInfo?.item?.baseMeasureUnitId,
            unitCode: selectedRowInfo?.item?.baseMeasureUnitCode
          }
        } else if (this.fieldName == 'itemCode') {
          selectedRowInfo = {
            // ...selectedRowInfo,
            itemId: selectedRowInfo?.id, // 物料数据
            itemCode: selectedRowInfo?.itemCode,
            itemName: selectedRowInfo?.itemName,
            spec: selectedRowInfo?.itemDescription, // 规格型号(采购申请)
            specification: selectedRowInfo?.itemDescription, // 规格型号(采购订单)
            itemGroupCode: selectedRowInfo?.itemGroupCode, //品项组编码
            itemGroupName: selectedRowInfo?.itemGroupName, //品项组名称
            unitName: selectedRowInfo?.baseMeasureUnitName, // 基本单位
            unitId: selectedRowInfo?.baseMeasureUnitId,
            unitCode: selectedRowInfo?.baseMeasureUnitCode
          }
        }
        // console.log("处理过的物料/sku数据：selectedRowInfo", selectedRowInfo);

        this.data[this.fieldName] = selectedRowInfo[this.fieldName]

        //  改变列的值、清空列的值
        this.changeOtherCol(selectedRowInfo)

        // 改变额外值、清空其他额外值
        this.setCellInfo(selectedRowInfo)

        if (this.$route.path.includes('purchase-coordination')) {
          //清空价格记录
          this.handleClearPriceArr()
        }

        // 关闭弹窗
        this.handleClose()
      }
    },
    handleClearPriceArr() {
      //清空价格记录
      this.$bus.$emit('taxidChange', null)
      this.$bus.$emit('taxPriceChange', null)
      this.$bus.$emit('taxTotalChange', null)
      this.$bus.$emit('freePriceChange', null)
      this.$bus.$emit('freeTotalChange', null)
      this.$bus.$emit('contractRelChange2', null)
    },
    // 调整其他列的数据：包括赋值和清空
    changeOtherCol(itemData) {
      // 如果配置了改变另一些列，就监听
      this.changedFieldArr.forEach((i) => {
        this.$bus.$emit(`${i}Change`, itemData[i] || null)
        // 如果物料改变了，需要再广播一下物料id。供工厂下拉去获取到
        if (i == 'itemCode') {
          this.$bus.$emit('itemIdChange1', itemData.itemId)
          this.$bus.$emit('itemIdChange', itemData.itemId, itemData.itemCode)
          this.$bus.$emit('itemCodeChange', itemData.itemCode)
          this.$bus.$emit('itemCodeChange1', itemData.itemCode)
          console.log(this.$t('是物料改变'), itemData)
        }
      })
      // 配置了 清空另一些列
      this.clearFieldArr.forEach((item) => {
        this.$bus.$emit(`${item}Change`, null)
      })

      // 如果物料改变了，需要再广播一下物料id。供工厂下拉去获取到
      if (this.fieldName == 'itemCode') {
        this.$bus.$emit('itemIdChange', itemData.itemId, itemData.itemCode)
        this.$bus.$emit('itemIdChange1', itemData.itemId)
        this.$bus.$emit('itemIdChange2', itemData.itemId)
        this.$bus.$emit('itemCodeChange', itemData.itemCode)
        this.$bus.$emit('itemCodeChange1', itemData.itemCode)
        this.$bus.$emit('itemCodeChange2', itemData.itemCode)
        console.log('是物料改变2222', itemData)
      }
      //如果SKU变了 ,传下SKU数据到价格记录组件
      if (this.fieldName == 'skuCode') {
        this.$bus.$emit('skuChange', {
          skuCode: itemData.skuCode,
          skuId: itemData.skuId
        })
      }
    },

    // 调整额外值：包括赋值和清空
    setCellInfo(itemData) {
      // 记录下这行的id、code、name
      let _data = itemData

      // 也要清空这一行中 被物料/sku 改变的数据
      this.clearFieldArr.forEach((item) => {
        _data[item] = null
      })

      // 清空其他额外值
      this.clearRowArr.forEach((item) => {
        _data[item] = null
      })

      this.$parent.$emit('selectedChanged', {
        fieldCode: this.fieldName,
        itemInfo: _data
      })

      // console.log(this.$t("组合后的数据"), _data);
    },

    // 点击 清除数据
    handleClear() {
      this.data[this.fieldName] = null

      let selectedRowInfo = {
        id: null,
        itemId: null,
        itemCode: null,
        itemName: null,
        skuId: null,
        skuCode: null,
        skuName: null,
        itemDescription: null, // 规格型号
        manufacturerName: null, // 制造商
        oldItemCode: null, // 旧物料编号
        barCode: null,
        name: null,
        requestOrderMethod: null
      }
      // 存入其他值
      this.setCellInfo(selectedRowInfo)

      // 改变其他列
      this.changeOtherCol(selectedRowInfo)
      this.handleClearPriceArr()
    },
    showDialog() {
      this.dialogShow = true
      this.$refs.dialog.ejsRef.show()
    },

    initDialog() {
      if (this.fieldName == 'itemCode') {
        this.pageConfig = [
          {
            useToolTemplate: false,
            toolbar: [],
            grid: {
              allowPaging: true,
              allowSelection: true,
              selectionSettings: {
                checkboxOnly: false
              },
              columnData: this[`${this.fieldName}ColumnData`],
              asyncConfig: {
                url: `${PROXY_MDM_TENANT}/${this.requestUrl[0]}`
                // recordsPosition: "data", //默认值为'data.records'
              }
            }
          }
        ]
      } else if (this.fieldName == 'skuCode') {
        this.pageConfig = [
          {
            useToolTemplate: false,
            toolbar: [],
            grid: {
              height: 350,
              allowPaging: true,
              allowSelection: true,
              selectionSettings: {
                checkboxOnly: false
              },
              columnData: this[`${this.fieldName}ColumnData`],
              asyncConfig: {
                url: `${PROXY_MDM_TENANT}/${this.requestUrl[0]}`,
                params: {
                  page: {
                    current: 1,
                    size: 10
                  }
                }
              }
            }
          }
        ]
      }
    },

    // 废弃
    // showDialog() {
    //   this.dialogShow = true;
    //   this.$refs.dialog.ejsRef.show();
    //   if (this.fieldName == "itemCode") {
    //     this.$set(this.pageConfig[0].grid, "allowPaging", false);
    //     this.$set(
    //       this.pageConfig[0].grid,
    //       "columnData",
    //       this[`${this.fieldName}ColumnData`]
    //     );
    //     this.$set(this.pageConfig[0].grid, "asyncConfig", {
    //       url: `${PROXY_MDM_TENANT}${this.requestUrl[0]}`,
    //       recordsPosition: "data", //默认值为'data.records'
    //     });
    //   } else if (this.fieldName == "skuCode") {
    //     this.$set(this.pageConfig[0].grid, "allowPaging", true);
    //     this.$set(
    //       this.pageConfig[0].grid,
    //       "columnData",
    //       this[`${this.fieldName}ColumnData`]
    //     );
    //     this.$set(this.pageConfig[0].grid, "asyncConfig", {
    //       url: `${PROXY_MDM_TENANT}${this.requestUrl[0]}`,
    //       params: {
    //         page: {
    //           current: 1,
    //           size: 10,
    //         },
    //       },
    //       // recordsPosition: "data", //默认值为'data.records'
    //     });
    //   }
    // },

    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
      // this.$bus.$emit("handleAddDialogShow", "addDialogShow", false);
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-pagertemplate {
  padding: 0px !important;
}
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px) !important;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
