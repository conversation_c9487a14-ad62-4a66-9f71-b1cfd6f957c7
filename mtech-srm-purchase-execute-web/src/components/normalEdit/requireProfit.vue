<template>
  <!-- 利润中心 -->
  <div>
    <mt-input
      :id="data.column.field"
      :value="data[data.column.field]"
      :disabled="true"
      v-show="!data.column.allowEditing"
    ></mt-input>
    <select-filter
      v-show="data.column.allowEditing"
      id="profitCenterName"
      :width="280"
      :fields="fields"
      :request-url="requestUrl"
      :request-key="requestKey"
      :init-val.sync="data['profitCenterName']"
      :other-params="otherParams"
      :label-show-obj="labelShowObj"
      @handleChange="handleChange"
    ></select-filter>
  </div>
</template>

<script>
export default {
  computed: {
    prCompanyInfo() {
      let _prCompanyInfo
      if (this.$route.path.includes('pr-apply')) {
        // 采购申请
        _prCompanyInfo = this.$store.state.prCompanyInfo
      } else if (this.$route.path.includes('source-apply')) {
        // 寻源申请
        _prCompanyInfo = this.$store.state.sourceCompanyInfo
      } else if (this.$route.path.includes('purchase-coordination-detail')) {
        _prCompanyInfo = {
          companyCode: this.$store.state.companyCode
        }
      }
      return _prCompanyInfo
    }
  },
  watch: {
    prCompanyInfo: {
      handler(newVal) {
        this.otherParams = {
          companyCode: newVal.companyCode
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      fields: {
        text: 'labelShow',
        value: 'labelShow'
      },
      requestUrl: {
        pre: 'masterData',
        url: 'postProfitCenterCriteriaQuery'
      },
      requestKey: 'profitData',
      otherParams: {},
      labelShowObj: {
        code: 'profitCenterCode',
        name: 'profitCenterName'
      }
    }
  },
  mounted() {
    this.otherParams = { companyCode: this.prCompanyInfo?.companyCode }
  },
  methods: {
    handleChange(e) {
      console.log(this.$t('接受到了变化'), e)
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'profitCenterName',
        itemInfo: {
          profitCenterId: e.itemData?.id,
          profitCenterCode: e.itemData?.profitCenterCode,
          profitCenterName: e.itemData?.profitCenterName
        }
      })
    }
  }
}
</script>

<style></style>
