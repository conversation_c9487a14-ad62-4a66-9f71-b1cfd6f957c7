<template>
  <div>
    <!-- 技术对接人 -->
    <mt-input
      id="techContactPerson"
      :value="data['techContactPerson']"
      style="display: none"
    ></mt-input>
    <!-- <mt-DropDownTree
      id="techContactPerson111"
      ref="distributeUserRef"
      v-model="mtTreeVal"
      :popup-height="400"
      :fields="organizateByUser"
      :allow-filtering="true"
      :show-clear-button="true"
      :placeholder="$t('请选择技术对接人')"
      v-if="allowEditing"
      @change="handleChange"
    ></mt-DropDownTree> -->
    <debounce-filter-select
      ref="userRef"
      id="techContactPerson111"
      v-if="allowEditing"
      :width="300"
      v-model="mtTreeVal"
      :request="getUser"
      :data-source="applyUserIdData"
      :show-clear-button="false"
      :fields="{ text: 'text', value: 'employeeId' }"
      :placeholder="$t('请选择技术对接人')"
      @change="handleChange"
    ></debounce-filter-select>

    <span v-if="!allowEditing">{{ showStr }}</span>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
export default {
  components: {
    debounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      allowEditing: true,
      organizateByUser: {},
      mtTreeVal: null,
      showStr: '',
      applyUserIdData: []
    }
  },
  mounted() {
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    // this.getDataSource(); // 废弃

    //引入mtech-common/utils中的防抖，(mtech-common/utils )
    this.getTree = utils.debounce(this.getTree, 300)

    if (this.data.techContactPerson) {
      this.getUser({ text: this.data.techContactPerson })
    }
  },
  methods: {
    // 获取 用户列表
    getUser(e) {
      const { text: fuzzyName } = e
      this.applyUserIdData = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })

          if (this.data.techContactPerson == item.employeeName) {
            this.mtTreeVal = item.employeeId
          }
        })
        this.applyUserIdData = tmp
      })
    },

    // 废弃
    getDataSource() {
      this.$API.masterData.getOrganizateTree({ orgLevelCode: 'ORG05' }).then((res) => {
        console.log(res)
        this.organizateByUser = {
          dataSource: res.data,
          value: 'id',
          text: 'name',
          child: 'children',
          parentValue: 'parentId'
        }

        let _otherDataPew = this.data.otherDataPew
        if (_otherDataPew && _otherDataPew.length) {
          _otherDataPew = JSON.parse(_otherDataPew)
          let _techContactPerson = _otherDataPew?.techContactPerson
          if (_techContactPerson) {
            let str = _techContactPerson.split('_')[1]
            this.mtTreeVal = [str]
            this.showStr = _techContactPerson.split('_')[0]
          }
        }
      })
    },
    handleChange(e) {
      // let _data = this.$refs.distributeUserRef.ejsRef.getData(e.value[0]);
      console.log(this.$t('技术对接人改变了'), e)
      let _data = e?.itemData
      this.data.techContactPerson = _data?.employeeName

      // 存到额外的行上
      // let _otherDataPew = this.data.otherDataPew;
      // _otherDataPew = _otherDataPew ? JSON.parse(_otherDataPew) : {};
      // if (_data?.employeeName) {
      //   _otherDataPew.techContactPerson =
      //     _data.employeeName +
      //     "_" +
      //     _data.employeeId +
      //     "_" +
      //     _data.employeeCode;
      // } else {
      //   _otherDataPew.techContactPerson = null;
      // }

      // this.$bus.$emit("otherDataPewChange", JSON.stringify(_otherDataPew));
    }
  }
}
</script>
