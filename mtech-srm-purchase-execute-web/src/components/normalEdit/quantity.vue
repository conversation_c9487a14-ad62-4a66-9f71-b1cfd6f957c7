<template>
  <div>
    <mt-input id="quantity" v-model="data.quantity" style="display: none"></mt-input>
    <mt-inputNumber
      v-model="data.quantity"
      :min="0"
      :precision="3"
      :step="1"
      :show-clear-button="false"
      @input="handleChange"
    ></mt-inputNumber>
  </div>
</template>
<script>
var bigDecimal = require('js-big-decimal')
export default {
  data() {
    return {
      budgetUnitPrice: '', //预算单价未税
      taxedUnitPrice: '', //预算单价含税
      unPrice: '' //不加点单价
    }
  },
  mounted() {
    this.budgetUnitPrice = this.data.budgetUnitPrice
    this.$bus.$on('updateBudgetUnitPrice', (e) => {
      //预算单价（未税)
      this.budgetUnitPrice = e
    })
    this.taxedUnitPrice = this.data.taxedUnitPrice
    this.$bus.$on('updateTaxedUnitPrice', (e) => {
      //预算单价（含税)
      this.taxedUnitPrice = e
    })
    this.unPrice = this.data.unPrice
    this.$bus.$on('updateUnPrice', (e) => {
      this.unPrice = e
    })
  },
  methods: {
    handleChange(e) {
      this.$bus.$emit('updateQuantity', e) //传到价格协议组件
      this.$bus.$emit('updateQuantity1', e) //传到预算单价（未税)组件
      this.$bus.$emit('updateQuantity2', e) //传到预算单价（含税)组件
      this.$bus.$emit('updateQuantity3', e) //传到不加点单价组件
      if (!e || e === '0') {
        this.$bus.$emit('budgetTotalPriceChange', '0.00')
        this.$bus.$emit('taxedTotalPriceChange', '0.00')
        this.$bus.$emit('unPriceChange', '0.00')
        return
      }
      if (this.budgetUnitPrice) {
        let budgetTotalPrice = bigDecimal.multiply(e, this.budgetUnitPrice)
        budgetTotalPrice = bigDecimal.round(budgetTotalPrice, 2)
        this.$bus.$emit('budgetTotalPriceChange', budgetTotalPrice)
      }
      if (this.taxedUnitPrice) {
        let taxedTotalPrice = bigDecimal.multiply(e, this.taxedUnitPrice)
        taxedTotalPrice = bigDecimal.round(taxedTotalPrice, 2)
        this.$bus.$emit('taxedTotalPriceChange', taxedTotalPrice)
      }
      if (this.unPrice) {
        let unTotal = bigDecimal.multiply(e, this.unPrice)
        unTotal = bigDecimal.round(unTotal, 2)
        this.$bus.$emit('unTotalChange', unTotal)
      }
    }
  }
}
</script>
