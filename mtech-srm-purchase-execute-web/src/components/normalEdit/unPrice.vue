<template>
  <div>
    <!-- 不加点单价 -->
    <mt-input id="unPrice" v-model="data.unPrice" style="display: none"></mt-input>
    <mt-inputNumber
      v-model="data.unPrice"
      :min="0"
      :precision="2"
      placeholder=""
      :step="3"
      :show-clear-button="false"
      @input="handleChange"
      v-if="allowEditing"
    ></mt-inputNumber>
    <span v-if="!allowEditing">{{ data.unPrice }}</span>
  </div>
</template>
<script>
var bigDecimal = require('js-big-decimal')
export default {
  data() {
    return {
      quantity: '',
      allowEditing: true
    }
  },
  mounted() {
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    this.quantity = this.data.quantity
    this.$bus.$on('updateQuantity3', (e) => {
      this.quantity = e
    })
  },
  methods: {
    handleChange(e) {
      console.log(e, '我改变了')
      this.$bus.$emit('updateUnPrice', e)
      if (!e) {
        this.$bus.$emit('unTotalChange', '0.00')
      }
      if (this.quantity) {
        if (!e) return
        let unTotal = bigDecimal.multiply(this.quantity, e)
        unTotal = bigDecimal.round(unTotal, 2)
        this.$bus.$emit('unTotalChange', unTotal)
      }
    }
  }
}
</script>
