<template>
  <div>
    <!-- 预算单价（含税) -->
    <mt-input id="taxedUnitPrice" v-model="data.taxedUnitPrice" style="display: none"></mt-input>
    <mt-inputNumber
      v-model="data.taxedUnitPrice"
      :min="0"
      :precision="2"
      placeholder=""
      :step="3"
      :show-clear-button="false"
      @input="handleChange"
      v-if="allowEditing"
    ></mt-inputNumber>
    <span v-if="!allowEditing">{{ data.taxedUnitPrice }}</span>
  </div>
</template>
<script>
var bigDecimal = require('js-big-decimal')
export default {
  data() {
    return {
      quantity: '',
      allowEditing: true
    }
  },
  mounted() {
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    this.quantity = this.data.quantity
    this.$bus.$on('updateQuantity2', (e) => {
      this.quantity = e
    })
  },
  methods: {
    handleChange(e) {
      this.$bus.$emit('updateTaxedUnitPrice', e)
      if (!e) {
        this.$bus.$emit('taxedTotalPriceChange', '0.00')
      }
      if (this.quantity) {
        if (!e) return
        let taxedTotalPrice = bigDecimal.multiply(this.quantity, e)
        taxedTotalPrice = bigDecimal.round(taxedTotalPrice, 2)
        this.$bus.$emit('taxedTotalPriceChange', taxedTotalPrice)
      }
    }
  }
}
</script>
