<template>
  <div class="img-show-cell">
    <img class="cell-img" v-if="data[data.column.field]" :src="data[data.column.field]" alt="" />
    <!-- <img class="cell-img" :src="testUrl" alt="" /> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {}
      // testUrl:
      // "http://minio.dev.qeweb.com/file/file/srm/tenant/17706479458443265/image/ce1e0005-309f-461d-8eb0-9acdf9411d66.png",
      // "https://storage.360buyimg.com/neos-static-files/ce7e1b31-c1e2-4284-b345-86ef81e6638a.png",
    }
  }
}
</script>

<style lang="scss">
.img-show-cell {
  .cell-img {
    max-height: 52px; // 单元格高度60px，此处上下间隔4px
    max-width: 100%;
  }
}
</style>
