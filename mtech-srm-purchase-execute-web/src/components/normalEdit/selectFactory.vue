<template>
  <div class="drop-influenced">
    <mt-select
      id="siteName"
      v-if="allowEditing"
      v-model="data.siteName"
      :data-source="dataSource"
      :fields="fields"
      style="width: 212px"
      :placeholder="headerTxt"
      @open="startOpen"
      @change="selectChange"
    ></mt-select>

    <mt-input
      id="siteName"
      v-else
      disabled
      v-model="data.siteName"
      :placeholder="headerTxt"
      style="width: 212px"
    ></mt-input>
  </div>
</template>

<script>
import { Query } from '@syncfusion/ej2-data'
export default {
  data() {
    return {
      data: {},
      fieldName: 'siteName',
      dataSource: [],
      fields: { text: 'label', value: 'organizationName' },
      valMatchInfo: {},
      changedFieldArr: [], // 改变其他列 通过bus
      changedRowArr: [], // 改变额外数据 通过 selectedChanged 传回行数据
      headerTxt: '',
      requestUrl: 'getFactoryList',
      itemCode: null, // 物料id
      // companyId: null, // 公司id
      allowEditing: true, // 允许编辑
      isEdit: '2'
    }
  },
  computed: {
    companyId() {
      if (
        this.$route.path.includes('pr-apply-detail') ||
        this.$route.path.includes('source-apply-detail')
      ) {
        return this.$store.state.prCompanyInfo?.companyId // 采购申请 选择的公司
      } else {
        return this.$store.state?.companyId // 采购订单 选择的公司
        // return ""; // 采购订单 选择的公司
      }
    }
  },
  mounted() {
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return
    // this.fieldName = this.data.column.field;
    let _nowCellSessionInfo = null
    if (this.$route.path.includes('pr-apply') || this.$route.path.includes('source-apply')) {
      _nowCellSessionInfo = sessionStorage.getItem(`pr${this.fieldName}Data`) // 采购申请的
    } else {
      _nowCellSessionInfo = sessionStorage.getItem(`${this.fieldName}Data`) // 采购订单的
    }
    // console.log("我是每次都进 工厂的下拉", this.data);
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (_nowCellSessionInfo) {
      _nowCellSessionInfo = JSON.parse(_nowCellSessionInfo)
      this.fields = _nowCellSessionInfo?.fields
      this.headerTxt = _nowCellSessionInfo?.title
      this.requestUrl = _nowCellSessionInfo?.requestUrl
      this.changedFieldArr = _nowCellSessionInfo?.changedFieldArr
      this.changedRowArr = _nowCellSessionInfo?.changedRowArr //TODO没用到
    }

    this.itemCode = this.data.itemCode

    // 监听物料id的变化
    this.$bus.$off('itemIdChange')
    this.$bus.$on('itemIdChange', (itemId, itemCode) => {
      // console.log("我是工厂下拉的组件，监听到了物料改变了", itemCode); // itemData.id 物料id
      this.itemCode = itemCode
    })

    // 编辑行数据时，如果有了工厂，给工厂赋数据源
    if (this.data.siteName) {
      this.getDataSource()
      this.isEdit = '1'
    }
    // 清空工厂数据begin
    var that = this
    this.fieldName = this.data.column.field
    this.$bus.$off(`${this.fieldName}Change`) // 为了解决textWrapper拿不到的问题
    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      // console.log("￥emit的工厂被展示的数据------", this.fieldName, txt);
      this.dataSource = []
      that.$set(that.data, this.fieldName, txt)
    })
    // 清空工厂数据end

    // 编辑行数据时，如果有了工厂，给工厂赋数据源
    if (this.data.siteName) {
      this.getDataSource()
    }
  },

  methods: {
    startOpen() {
      // console.log("我准备打开了--去获取工厂的下拉数据");
      if (!this.itemCode && !this.companyId) {
        this.$toast({
          content: this.$t('请先选择公司 或 物料/sku'),
          type: 'warning'
        })
        return
      }
      this.getDataSource()
    },

    getDataSource() {
      // 根据物料ID / 公司id 查 工厂
      let params = {}
      params = {
        orgLevelTypeCode: 'ORG06',
        itemCode: this.itemCode,
        parentOrgId: this.companyId
      }
      let _url = ''
      if (this.itemCode) {
        _url = this.requestUrl
        this.fields = { text: 'label', value: 'organizationName' }
      } else {
        _url = 'getFactoryListByCompany'
        params = { organizationId: this.companyId }
        this.fields = { text: 'label', value: 'siteName' }
      }
      this.$API.masterData[_url](params).then((res) => {
        // console.log(res);
        let _categoryItemData = res.data
        // 带出品类数据 放到categoryTypeInfo里
        _categoryItemData.forEach((item) => {
          if (item?.itemRelResponseList?.length) {
            item.categoryTypeInfo = item?.itemRelResponseList.find(
              (c) => c?.categoryTypeCode == 'product'
            )
          } else {
            item.categoryTypeInfo = null
          }
          if (this.itemCode) {
            item.label = `${item.organizationCode}-${item.organizationName}`
          } else {
            item.label = `${item.siteCode}-${item.siteName}`
          }
        })
        this.dataSource = _categoryItemData
      })
    },

    filtering(e) {
      // console.log(e);
      var searchData = this.dataSource
      if (e.text == '') e.updateData(searchData)
      else {
        let query = new Query().select(['label', 'label'])
        // change the type of filtering
        query = e.text !== '' ? query.where('label', 'contains', e.text, true) : query
        // console.log(query);
        e.updateData(searchData, query)
      }
    },

    selectChange(e) {
      if (!e.itemData) return
      if (this.isEdit === '1') {
        this.isEdit = '2'
        return
      }
      let selectedRowInfo = e.itemData
      // 如果没有物料
      if (!this.itemCode) {
        let _selectedRowInfo = {
          siteId: selectedRowInfo.organizationId,
          siteName: selectedRowInfo.siteName,
          siteCode: selectedRowInfo.siteCode
        }
        // 存入其他值
        this.setCellInfo(_selectedRowInfo)
        //清空价格记录
        this.handleClearPriceArr()
        this.$bus.$emit('siteCodeChange', selectedRowInfo.siteCode)
        //价格记录用到
        this.$bus.$emit('updateSiteId', _selectedRowInfo.siteId)
        this.$bus.$emit('updateSiteCode', _selectedRowInfo.siteCode)
        //订单明细库存地点用到
        this.$bus.$emit('updateSiteCode1', _selectedRowInfo.siteCode)
        return
      }
      // 根据物料+工厂带出 品类、采购组、基本单位、采购单位、质量免检标识
      this.$API.masterData
        .getBasicByFacItem({
          organizationId: selectedRowInfo.organizationId,
          itemCode: this.itemCode
        })
        .then((res) => {
          // 根据 物料+工厂 带出 质量免检标识
          this.$API.masterData
            .getQuantity({
              organizationId: selectedRowInfo.organizationId,
              itemCode: this.itemCode
            })
            .then((res1) => {
              let itemData1 = {
                qualityExemptionMark:
                  res1.data?.skipQualityControl == 1
                    ? 1
                    : res1.data?.skipQualityControl == 0
                    ? 0
                    : null, // 质量免检标识 0-否 需检验，1-是 免检
                qualityExemptionMarkName:
                  res1.data?.skipQualityControl == 1
                    ? this.$t('免检')
                    : res1.data?.skipQualityControl == 0
                    ? this.$t('需检验')
                    : null // 质量免检标识 0-否非免检，1-是 免检
              }
              // 在此把 被改变的列，以及这些列对应的id、code都列出来了，相当于是 changedFieldArr + changedRowArr
              let _selectedRowInfo = {
                // ...e.itemData,
                siteId: selectedRowInfo.organizationId, // 地点/工厂
                siteName: selectedRowInfo.organizationName,
                siteCode: selectedRowInfo.organizationCode,
                categoryId: selectedRowInfo?.categoryTypeInfo?.categoryId, // 品类
                categoryName: selectedRowInfo?.categoryTypeInfo?.categoryName,
                categoryCode: selectedRowInfo?.categoryTypeInfo?.categoryCode, // 品类code
                buyerOrgId: res.data?.purchasingInfo?.purchaseGroupId, // 采购组    采购组id
                buyerOrgCode: res.data?.purchasingInfo?.purchaseGroupCode, // 主数据没有这个  采购组code
                buyerOrgName: res.data?.purchasingInfo?.purchaseGroupName, //  采购组名称
                unitId: res.data?.itemInfo?.baseMeasureUnitId, // 基本单位
                unitCode: res.data?.itemInfo?.baseMeasureUnitCode, // 主数据没有这个字段
                unitName: res.data?.itemInfo?.baseMeasureUnitName,
                orderUnitId: res.data?.purchasingBasicInfo?.purchaseUnitId, // 采购单位 采购单位id
                orderUnitCode: 'orderUnitCode',
                orderUnitName: res.data?.purchasingBasicInfo?.purchaseUnitName, //采购单位名称
                purUnitName: res.data?.purchasingBasicInfo?.purchaseUnitName, //采购单位名称
                purUnitNameCode: res.data?.purchasingBasicInfo?.purchaseUnitCode, //采购单位code
                qualityExemptionMark: itemData1.qualityExemptionMark,
                qualityExemptionMarkName: itemData1.qualityExemptionMarkName
              }
              if (this.$route.name == 'purchase-coordination-detail') {
                //如果是采购订单需要走这个方法获取申请转化订单方式 1-独立；2-集中
                this.$API.masterData
                  .itemPlanningDetail({
                    itemCode: this.itemCode,
                    organizationCode: selectedRowInfo.organizationCode,
                    organizationId: selectedRowInfo.organizationId
                  })
                  .then((res) => {
                    _selectedRowInfo.requestOrderMethod = res.data?.independentOrCentralize
                    this.setCellInfo(_selectedRowInfo)
                  })
              } else {
                // 存入其他值
                this.setCellInfo(_selectedRowInfo)
              }
              // 改变其他列
              this.changeOtherCol(_selectedRowInfo)
              //清空价格记录
              this.handleClearPriceArr()
              this.$bus.$emit('siteCodeChange', _selectedRowInfo.siteCode)
              this.$bus.$emit('buyerOrgCodeChange', _selectedRowInfo.buyerOrgCode)
              //价格记录用到
              this.$bus.$emit('updateSiteId', _selectedRowInfo.siteId)
              this.$bus.$emit('updateSiteCode', _selectedRowInfo.siteCode)
              //订单明细库存地点用到
              this.$bus.$emit('updateSiteCode1', _selectedRowInfo.siteCode)
            })
        })
    },
    handleClearPriceArr() {
      //清空价格记录
      this.$bus.$emit('taxidChange', null)
      this.$bus.$emit('taxPriceChange', null)
      this.$bus.$emit('taxTotalChange', null)
      this.$bus.$emit('freePriceChange', null)
      this.$bus.$emit('freeTotalChange', null)
      this.$bus.$emit('contractRelChange1', null)
    },
    // 调整其他列的数据：包括赋值
    setCellInfo(itemData) {
      // console.log(this.$t("我是该阿范德萨范德萨"), this.fieldName, itemData);
      this.$parent.$emit('selectedChanged', {
        fieldCode: this.fieldName,
        itemInfo: itemData
      })
    },

    // 改变其他列的数据
    changeOtherCol(itemData) {
      // console.log(itemData, "我是itemdata11");
      // 如果配置了改变另一些列，就监听
      this.changedFieldArr.forEach((i) => {
        this.$bus.$emit(`${i}Change`, itemData[i] || null)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drop-influenced /deep/ .e-input.e-disabled {
  background: #f5f5f5 !important;
}
</style>
