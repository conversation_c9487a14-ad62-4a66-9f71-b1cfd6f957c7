<template>
  <div class="two-form">
    <div
      v-if="!allowEditing || nowRowHasItemCode"
      :class="['cell-changed', 'cell-changed-disabled']"
      id="cell-changed"
    >
      <mt-input :id="fieldName" style="display: none" :value="data[fieldName]"></mt-input>
      <mt-input v-model="data[fieldName]" disabled></mt-input>
    </div>
    <div class="selects" v-else>
      <mt-select
        :id="fieldName"
        v-model="data[fieldName]"
        :data-source="dataSource"
        :fields="fields"
        :show-clear-button="true"
        :allow-filtering="true"
        :placeholder="pld"
        @change="selectChange"
        :filtering="searchText"
        @open="startOpen"
      ></mt-select>
    </div>
  </div>
</template>

<script>
// 1. 默认无物料，这些显示下拉：品类、地点/工厂、采购组、基本单位、采购单位、质量免检标识
// 2. 选择了物料，这些变成被带出的字段
// 3. 不可修改的情况 allowEditing
import { utils } from '@mtech-common/utils'
export default {
  components: {
    // cellChanged: "@/components/normalEdit/cellChanged", // 单元格被改变（纯展示）
  },
  data() {
    return {
      data: {},
      fieldName: 'categoryName',
      nowRowHasItemCode: false, // 默认没有物料编码
      dataSource: [],
      fields: null,
      pld: '',
      changeRowObj: null, // 改变的额外行数据
      changeFieldObj: null, // 改变的其他列数据
      allowEditing: true
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.nowRowHasItemCode = this.data.itemCode ? true : false
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return
    this.$bus.$on(`itemCodeChange2`, (txt) => {
      console.log('在双重单元格中，监听到了物料编码变化了--itemCodeChange2', this.fieldName, txt)
      this.nowRowHasItemCode = txt ? true : false
    })
    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      this.$set(this.data, this.fieldName, txt)
    })
    let sessionData = sessionStorage.getItem(`${this.fieldName}Session`)
    if (sessionData) {
      sessionData = JSON.parse(sessionData)
      this.dataSource = sessionData.dataSource || []
      this.changeRowObj = sessionData.changeRowObj || null
      this.changeFieldObj = sessionData.changeFieldObj || null
      this.fields = sessionData.fields || {}
      this.pld = sessionData.pld || ''
    }
    if (this.fieldName === 'categoryName') {
      //品类
      let str = this.data.categoryName || ''
      this.getcategoryName(str)
      this.getcategoryName = utils.debounce(this.getcategoryName, 1000)
    }
    if (this.fieldName === 'unitName') {
      //基本单位
      let str = this.data.unitName || ''
      this.getunitName(str)
      this.getunitName = utils.debounce(this.getunitName, 1000)
    }
    if (this.fieldName === 'purUnitName') {
      //采购单位
      let str = this.data.purUnitName || ''
      this.getpurUnitName(str)
      this.getpurUnitName = utils.debounce(this.getpurUnitName, 1000)
    }
    if (this.fieldName === 'buyerOrgName') {
      //采购组
      let str = this.data.buyerOrgCode || ''
      this.getbussinessGroup({ text: str })
      this.getbussinessGroup = utils.debounce(this.getbussinessGroup, 1000)
    }
  },
  methods: {
    getbussinessGroup(val) {
      console.log(val, '我是采购组')
      let str = val?.text || ''
      let params = {
        fuzzyParam: str,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.groupCode} - ${item.groupName}`
        })
        this.dataSource = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.dataSource)
          })
        }
      })
    },
    getunitName(val, e) {
      //基本单位
      let params = {
        fuzzyParam: val,
        dataLimit: 20
      }
      this.$API.masterData.getUnitByFilter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.unitCode} - ${item.unitName}`
        })
        this.dataSource = list
        if (e?.updateData) {
          this.$nextTick(() => {
            e.updateData(this.dataSource)
          })
        }
      })
    },
    getpurUnitName(val, e) {
      //采购单位
      let params = {
        fuzzyParam: val,
        dataLimit: 20
      }
      this.$API.masterData.getUnitByFilter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.unitCode} - ${item.unitName}`
          item.purUnitName = item.unitName
        })
        this.dataSource = list
        if (e?.updateData) {
          this.$nextTick(() => {
            e.updateData(this.dataSource)
          })
        }
      })
    },
    getcategoryName(val, e) {
      //获取品类
      let params = {
        patternKeyword: val,
        dataLimit: 20
      }
      this.$API.masterData.getCategory(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.categoryCode} - ${item.categoryName}`
        })
        this.dataSource = list
        if (e?.updateData) {
          this.$nextTick(() => {
            e.updateData(this.dataSource)
          })
        }
      })
    },
    startOpen() {
      if (this.fieldName === 'categoryName') {
        if (!this.dataSource.length) {
          this.getcategoryName()
        }
      }
      if (this.fieldName === 'unitName') {
        if (!this.dataSource.length) {
          this.getunitName()
        }
      }
      if (this.fieldName === 'purUnitName') {
        if (!this.dataSource.length) {
          this.getpurUnitName()
        }
      }
      if (this.fieldName === 'buyerOrgName') {
        if (!this.dataSource.length) {
          this.getbussinessGroup()
        }
      }
    },
    searchText(e) {
      console.log(e, '我执行了')
      if (this.fieldName === 'categoryName') {
        this.getcategoryName(e?.text || '', e)
      }
      if (this.fieldName === 'unitName') {
        this.getunitName(e?.text || '', e)
      }
      if (this.fieldName === 'purUnitName') {
        this.getpurUnitName(e?.text || '', e)
      }
      if (this.fieldName === 'buyerOrgName') {
        this.getbussinessGroup(e)
      }
    },
    selectChange(e) {
      // console.log(this.$t("下拉选择改变了"), this.fieldName, e);
      // 改变行的额外数据
      if (this.changeRowObj) {
        let _itemInfo = {}
        for (let i in this.changeRowObj) {
          _itemInfo[i] = e.itemData[this.changeRowObj[i]]
        }
        this.$parent.$emit('selectedChanged', {
          fieldCode: this.fieldName,
          itemInfo: _itemInfo
        })
      }
      if (this.fieldName === 'categoryName') {
        //品类
        this.$bus.$emit('categoryCodeChange', e.itemData?.categoryCode)
      }
      if (this.fieldName === 'unitName') {
        //基本单位
        this.$bus.$emit('unitCodeChange', e.itemData?.unitCode)
      }
      if (this.fieldName === 'purUnitName') {
        //基本单位
        this.$bus.$emit('purUnitNameCodeChange', e.itemData?.unitCode)
      }
      if (this.fieldName === 'buyerOrgName') {
        //采购组
        this.$bus.$emit('buyerOrgCodeChange', e.itemData?.groupCode)
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off(`itemCodeChange2`)
    this.$bus.$off(`${this.fieldName}Change`)
  }
}
</script>

<style lang="scss" scoped>
#cell-changed /deep/ .e-input.e-disabled {
  height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
  padding-left: 10px !important;
  background: #f5f5f5 !important;
}
</style>
