import { i18n } from '@/main.js'
export const itemCodeColumnData = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  { width: '150', field: 'itemName', headerText: i18n.t('物料名称') },
  {
    width: '150',
    field: 'categoryResponse.categoryCode',
    headerText: i18n.t('品类')
  },
  { width: '150', field: 'itemDescription', headerText: i18n.t('规格型号') },
  { width: '150', field: 'oldItemCode', headerText: i18n.t('旧物料编号') },
  { width: '150', field: 'manufacturerName', headerText: i18n.t('制造商') }
]

export const skuCodeColumnData = [
  { width: '150', field: 'barCode', headerText: i18n.t('SKU编号') },
  { width: '150', field: 'name', headerText: i18n.t('SKU名称') },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    valueAccessor: function (field, data) {
      return data?.['item']?.[field]
    }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    valueAccessor: function (field, data) {
      return data?.['item']?.[field]
    }
  },
  // { width: "150", field: "", headerText: i18n.t("品类") }, // 品类由工厂带出
  {
    width: '150',
    field: 'itemDescription',
    headerText: i18n.t('规格型号'),
    valueAccessor: function (field, data) {
      return data?.['item']?.[field]
    }
  },
  {
    width: '150',
    field: 'oldItemCode',
    headerText: i18n.t('旧物料编号'),
    valueAccessor: function (field, data) {
      return data?.['item']?.[field]
    }
  },
  {
    width: '150',
    field: 'manufacturerName',
    headerText: i18n.t('制造商'),
    valueAccessor: function (field, data) {
      return data?.['item']?.[field]
    }
  }
]
