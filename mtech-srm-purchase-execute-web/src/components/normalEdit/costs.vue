<template>
  <div>
    <mt-input id="orderCosts" v-model="data.orderCosts" style="display: none"></mt-input>
    <!-- <mt-select
      id="orderCosts"
      v-model="data.orderCosts"
      :data-source="dataSource"
      :fields="{ text: 'costCenterName', value: 'costCenterName' }"
      @change="selectChange"
      v-if="
        (entryType === '1' && entrySource === '1') ||
        (entryType === '1' && entrySource === '2') ||
        (entryType === '2' && entryDraft === '1')
      "
    ></mt-select> -->
    <!-- 成本中心 -->
    <!-- <select-filter
      id="orderCosts"
      :fields="{ text: 'costCenterCode', value: 'costCenterCode' }"
      :request-url="requestUrl"
      :request-key="requestKey"
      :init-val.sync="data['orderCosts']"
      :other-params="{
        companyCode: companyCode,
      }"
      @handleSelect="handleSelect"
      v-if="
        (entryType === '1' && entrySource === '1') ||
        (entryType === '1' && entrySource === '2') ||
        (entryType === '2' && entryDraft === '1')
      "
    ></select-filter> -->
    <mt-select
      id="orderCosts"
      v-model="data.orderCosts"
      :data-source="dataSource"
      :fields="{ text: 'label', value: 'costCenterCode' }"
      @open="startOpen"
      @change="handleSelect"
      :open-dispatch-change="false"
      :allow-filtering="true"
      :filtering="serchText"
      v-if="
        (entryType === '1' && entrySource === '1') ||
        (entryType === '1' && entrySource === '2') ||
        (entryType === '2' && entryDraft === '1')
      "
    ></mt-select>
    <span @click="showDetail">
      <span
        v-if="
          (entryType === '2' && entrySource === '1' && entryDraft !== '1') ||
          (entryType === '2' && entrySource === '4') ||
          (entryType === '2' && entrySource === '0') ||
          (entryType === '1' && entrySource === '0') ||
          (entryType === '1' && entrySource === '4')
        "
        >{{ data.orderCosts }}-{{ data.costCenterName }}</span
      >
    </span>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="$t('成本中心')"
      :buttons="buttons"
      @close="handleCloseDialog"
    >
      <div class="full-height">
        <mt-template-page ref="templateRef" class="full-height" :template-config="pageConfig">
        </mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'

export default {
  components: {
    // selectFilter:
    //   require("@/components/businessComponents/selectFilter/index.vue").default,
  },
  data() {
    return {
      requestUrl: {
        pre: 'masterData',
        url: 'postCostCenterCriteriaQuery'
      },
      requestKey: 'costData',
      dataSource: [],
      fields: {},
      entryType: '',
      entrySource: '',
      entryDraft: '',
      buttons: [
        {
          click: this.handleCloseDialog,
          buttonModel: { content: this.$t('返回') }
        }
      ],
      pageConfig: [
        {
          grid: {
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              },
              {
                width: '150',
                field: 'costCenterName',
                headerText: this.$t('成本中心')
              },
              {
                width: '150',
                field: 'percentage',
                headerText: this.$t('分摊比例')
              },
              {
                width: '150',
                field: 'description',
                headerText: this.$t('分摊说明')
              }
            ],
            dataSource: [],
            frozenColumns: 1,
            allowPaging: false,
            height: 'auto'
          }
        }
      ]
    }
  },
  computed: {
    companyCode() {
      let companyCode = this.$store.state.companyCode
      return companyCode
    }
  },
  mounted() {
    this.entryType = this.$route.query.type
    this.entrySource = this.$route.query.source
    this.entryDraft = this.$route.query.draft
    if (this.$route.query.type === '1') {
      if (this.$route.query.source === '1' || this.$route.query.source === '2') {
        this.postCostCenterCriteriaQuery(this.data.orderCosts || '')
      }
    }
    if (this.$route.query.type === '2' && this.entryDraft === '1') {
      this.postCostCenterCriteriaQuery(this.data.orderCosts || '')
    }
    this.postCostCenterCriteriaQuery = utils.debounce(this.postCostCenterCriteriaQuery, 1000)
  },
  methods: {
    serchText(val) {
      if (this.data.column.field == 'orderCosts') {
        this.postCostCenterCriteriaQuery(val && val.text ? val.text : '')
      }
    },
    startOpen() {
      if (!this.dataSource.length) {
        this.postCostCenterCriteriaQuery()
      }
    },
    handleCloseDialog() {
      this.$refs.dialog.ejsRef.hide()
    },
    showDetail() {
      if (this.entryType === '1') {
        if (this.entrySource === '0' || this.entrySource === '4') {
          if (this.data.costSharingResponses) {
            let list = []
            this.data.costSharingResponses.forEach((item) => {
              list.push({
                costCenterName: item.costSharingAccName,
                percentage: item.appProportion
              })
            })
            this.pageConfig[0].grid.dataSource = list
          }
        }
      }
      if (this.entryType === '2') {
        if (this.entrySource === '0') {
          if (this.data.costs1) {
            this.pageConfig[0].grid.dataSource = this.data.costs1
            this.$refs.dialog.ejsRef.show()
          }
        }
        if (this.entrySource === '4') {
          if (this.data.entryFrom === '4') {
            if (this.data.costSharingResponses) {
              let list = []
              this.data.costSharingResponses.forEach((item) => {
                list.push({
                  costCenterName: item.costSharingAccName,
                  percentage: item.appProportion
                })
              })
              this.pageConfig[0].grid.dataSource = list
            }
          } else {
            if (this.data.costs1) {
              this.pageConfig[0].grid.dataSource = this.data.costs1
            }
          }
          this.$refs.dialog.ejsRef.show()
        }
      }
    },
    postCostCenterCriteriaQuery(val) {
      let params = {
        companyCode: this.companyCode,
        costData: val,
        dataLimit: 20
      }
      this.$API.masterData.postCostCenterCriteriaQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.costCenterCode}-${item.costCenterName}`
        })
        this.dataSource = list
      })
    },
    handleSelect(e) {
      console.log(this.$t('接受到了变化'), e)
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'orderCosts',
        itemInfo: {
          costCenterId: e.itemData.id,
          costCenterCode: e.itemData.costCenterCode,
          costCenterName: e.itemData.costCenterName
        }
      })
      this.$bus.$emit('costCenterNameChange', e.itemData.costCenterName)
    }
    // selectChange(e) {
    //   console.log(e, "我是成本中心改变");
    //   this.$parent.$emit("selectedChanged", {
    //     fieldCode: "orderCosts",
    //     itemInfo: e.itemData,
    //   });
    // },
  }
}
</script>
