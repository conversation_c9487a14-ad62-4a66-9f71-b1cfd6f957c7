<template>
  <div>
    <mt-input :id="data.column.field" :value="data[data.column.field]"></mt-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      fieldName: ''
    }
  },
  created() {},
  mounted() {
    var that = this
    this.fieldName = this.data.column.field
    this.$bus.$off(`${this.fieldName}Change`)
    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      that.$set(that.data, this.fieldName, txt)
    })
  }
}
</script>

<style lang="scss" scoped>
#cell-changed /deep/ .e-input.e-disabled {
  height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
  padding-left: 10px !important;
  background: #f5f5f5 !important;
}
</style>
