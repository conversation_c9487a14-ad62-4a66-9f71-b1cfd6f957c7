<template>
  <div class="drop-influenced">
    <mt-select
      id="freePrice"
      v-model="data.freePrice"
      :data-source="dataSource"
      :fields="{ text: 'label1', value: 'label1' }"
      :placeholder="$t('请选择未税单价')"
      @open="startOpen"
      @change="selectChange"
      v-if="allowEditing"
    ></mt-select>
    <span v-if="!allowEditing">{{ data.freePrice }}</span>
  </div>
</template>
<script>
var bigDecimal = require('js-big-decimal')
import { utils } from '@mtech-common/utils'

export default {
  name: 'SelectPrice',
  data() {
    return {
      dataSource: [],
      allowEditing: true,
      siteId: '',
      siteCode: '',
      quantity: 0,
      supplierId: '',
      itemId: '',
      itemCode: '',
      isEdit: '2',
      priceRecordCode: '',
      priceRecordId: '',
      bizCode: ''
    }
  },
  mounted() {
    this.itemId = this.data.itemId
    // 监听物料id的变化
    this.$bus.$off('itemIdChange1')
    this.$bus.$on('itemIdChange1', (itemId) => {
      this.itemId = itemId
    })
    this.itemCode = this.data.itemCode
    this.$bus.$off('itemCodeChange1')
    this.$bus.$on('itemCodeChange1', (itemCode) => {
      this.itemCode = itemCode
      if (!this.data.contractRel) {
        this.getDataSource1()
      }
    })
    this.siteId = this.data.siteId
    this.$bus.$off('updateSiteId')
    this.$bus.$on('updateSiteId', (e) => {
      this.siteId = e
    })
    this.siteCode = this.data.siteCode
    this.$bus.$off('updateSiteCode')
    this.$bus.$on('updateSiteCode', (e) => {
      this.siteCode = e
      if (!this.data.contractRel) {
        this.getDataSource1()
      }
    })
    if (this.data.siteCode && (this.data.itemCode || this.data.skuCode)) {
      if (!this.data.contractRel) {
        this.getDataSource1()
      }
    }
    this.skuCode = this.data.skuCode
    this.skuId = this.data.skuId
    this.$bus.$off('skuChange')
    this.$bus.$on('skuChange', (e) => {
      this.skuCode = e.skuCode
      this.skuId = e.skuId
    })
    this.$bus.$off('contractRelChange2')
    this.$bus.$on('contractRelChange2', (e) => {
      this.data.freePrice = e
      this.dataSource = []
    })
    this.$bus.$off('contractRelChange1')
    this.$bus.$on('contractRelChange1', (e) => {
      this.data.freePrice = e
      this.dataSource = []
    })
    this.quantity = this.data.quantity
    this.$bus.$off('updateQuantity')
    this.$bus.$on('updateQuantity', (e) => {
      //数量有更新 ,重新计算价格
      this.quantity = e
      if (!e || e === '0') {
        this.$bus.$emit('taxTotalChange', '')
        this.$bus.$emit('freeTotalChange', '')
        this.$bus.$emit('taxPriceChange', '')
        this.$bus.$emit('freePriceChange', '')
        this.$bus.$emit('taxidChange', '')
        this.$bus.$emit('priceUnitChange', '')
        return
      }
      this.priceCalc()
    })
    this.priceRecordId = this.data.contractRelId
    this.priceRecordCode = this.data.contractRelCode || this.data.contractRel
    console.log('啊哈哈哈,', this.data.column.allowEditing)
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    this.priceCalc = utils.debounce(this.priceCalc, 300)
    if (this.$route.query.type === '2') {
      if (this.$route.query.draft === '1') {
        this.allowEditing = true
      }
    }
    this.getBizCode()
    if (this.data.contractRel) {
      //当做编辑
      let supplierCode = sessionStorage.getItem('supplierCode')
      this.supplierCode = supplierCode
      this.getDataSource()
      this.isEdit = '1'
    }
    if (this.getBizCode) {
      //如果从采购申请或者合同进入的话并且没有物料
      if (!this.data.siteCode && (this.data.itemCode || this.data.skuCode)) {
        if (!this.data.contractRel) {
          this.getDataSource1()
        }
      }
    }
  },
  methods: {
    selectChange(e) {
      //改变价格协议
      console.log('我是下拉找执行了', e)
      if (!e.itemData) return
      if (this.isEdit === '1') {
        this.isEdit = '2'
        return
      }
      console.log(e.itemData, '价格协议改变')
      // this.priceRecordCode = e.itemData.label?.split("_")[0] || "";
      // this.priceRecordId = e.itemData.label?.split("_")[1] || "";
      this.priceRecordCode = e.itemData.label
      // this.priceRecordId = e.itemData.priceRecordId;
      this.$bus.$emit('contractRelChange', this.priceRecordCode)
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'contractRel',
        itemInfo: {
          contractRelId: this.priceRecordId,
          contractRelCode: this.priceRecordCode
        }
      })
      this.priceCalc()
    },
    startOpen() {
      // if (
      //   !this.itemId &&
      //   this.data.entryFrom !== "0" &&
      //   !this.$route.query.source === "2"
      // ) {
      //   this.$toast({ content: "请先选择物料/sku", type: "warning" });
      //   return;
      // }
      if (!this.itemId && this.$route.query.source === '1') {
        //新建必有物料
        this.$toast({ content: this.$t('请先选择物料/sku'), type: 'warning' })
        return
      }
      if (!this.siteId) {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        return
      }
      let supplierId = sessionStorage.getItem('supplierId')
      this.supplierId = supplierId
      let supplierCode = sessionStorage.getItem('supplierCode')
      this.supplierCode = supplierCode
      if (!supplierId || !supplierCode) {
        this.$toast({ content: this.$t('供应商未选择'), type: 'warning' })
        return
      }
      this.getDataSource()
    },
    getDataSource1() {
      let supplierCode = sessionStorage.getItem('supplierCode')
      this.supplierCode = supplierCode
      if (!supplierCode) {
        console.log('供应商未选择 去获取价格协议')
        return
      }
      if (!this.siteCode) {
        console.log('工厂未选择 去获取价格协议')
        return
      }
      this.getDataSource()
    },
    getDataSource() {
      //查询价格记录 bizCode 在申请新增或者合同新增 或者申请编辑 或者合同编辑进入的时候,如果没有物料信息 才会传
      let params = [
        {
          bizCode: '',
          itemCode: this.itemCode || '',
          siteCode: this.siteCode,
          supplierCode: this.supplierCode,
          skuCode: this.skuCode || ''
        }
      ]
      if (this.bizCode) {
        if (!this.itemCode) {
          params[0].bizCode = this.bizCode
        }
      }
      this.$API.purchaseOrder.queryPrice(params).then((res) => {
        //查询价格记录
        res.data = res.data || []
        let list = []
        res.data.forEach((item) => {
          // if (item.stageList) {
          if (item.stageType === -1) {
            // item.label = `${item.priceRecordCode}_${item.id}`;
            item.label = `${item.priceRecordCode}`
            item.label1 = item.untaxedUnitPrice
            list.push(item)
          }
          if (item.stageType !== -1 && item.stageList.length) {
            item.stageList.forEach((item1) => {
              list.push({
                ...item1,
                label: item1.priceRecordCode,
                label1: item1.untaxedUnitPrice
              })
            })
          }
          // }
        })
        console.log(list, '我是价格记录数据')
        if (list.length === 1) {
          // this.data.contractRel = list[0].label;
          this.data.freePrice = list[0].label1
        }
        this.dataSource = list
      })
    },
    getBizCode() {
      if (this.data.entryFrom === '0') {
        this.bizCode = this.data.requestCode + '_' + this.data.itemNo1
      }
      // this.$route.query.type === "1" &&
      if (this.$route.query.source === '0' || this.$route.query.source === '4') {
        this.bizCode = this.data.requestCode + '_' + this.data.itemNo1
      }
    },
    priceCalc() {
      //查询价格
      // if (
      //   !this.itemId &&
      //   this.data.entryFrom !== "0" &&
      //   this.$route.query.source !== "2"
      // ) {
      //   //新建进入一定有物料才可以查,没有不查
      //   return;
      // }
      if (!this.itemId && this.$route.query.source === '1') {
        //新建进入一定有物料才可以查,没有不查
        console.log('物料id没有')
        return
      }
      // if (!this.siteId) {
      //   console.log("工厂id没有");
      //   return;
      // }
      if (!this.siteCode) {
        console.log('工厂code没有')
        return
      }
      let supplierId = sessionStorage.getItem('supplierId')
      this.supplierId = supplierId
      let supplierCode = sessionStorage.getItem('supplierCode')
      this.supplierCode = supplierCode
      // if (!supplierId) {
      //   console.log("供应商id没有");
      //   return;
      // }
      if (!supplierCode) {
        console.log('供应商code没有')
        return
      }
      if (!this.priceRecordCode) {
        console.log('价格协议code没有')
        return
      }
      if (!this.quantity || this.quantity === 0 || this.quantity === '0.00') {
        console.log('订单数量没有')
        return
      }
      let businessInfo = JSON.parse(sessionStorage.getItem('businessInfo'))
      let businessTypeId = businessInfo.businessId
      let businessTypeCode = businessInfo.businessTypeCode
      let orderCode = businessInfo.orderCode
      if (!businessTypeId) {
        console.log('业务类型id没有')
        return
      }
      let params = {
        businessTypeCode: businessTypeCode,
        // businessTypeId: businessTypeId,
        supplierCode: this.supplierCode,
        // supplierId: this.supplierId,
        orderItemList: [
          {
            itemCode: this.itemCode,
            // itemId: this.itemId || 0,
            itemNo: this.data.itemNo1 || 0,
            priceRecordCode: this.priceRecordCode,
            quantity: this.quantity,
            siteCode: this.siteCode,
            // siteId: this.siteId,
            supplierCode: this.supplierCode
            // supplierId: this.supplierId,
          }
        ]
      }
      if (this.bizCode) {
        if (!params.orderItemList[0].itemCode) {
          params.orderItemList[0].bizCode = this.bizCode
        }
      }
      if (orderCode) {
        params.orderCode = orderCode
      }
      if (this.skuCode && this.skuId) {
        params.orderItemList[0].skuCode = this.skuCode
        // params.orderItemList[0].skuId = this.skuId;
      }
      this.$API.purchaseOrder.priceCalc(params).then((res) => {
        //查询到含税单价 未税单价 税率赋值 ,如果有订单数量,计算出含税总价 ,如果再有税率计算未税总价赋值  没有订单数量不用继续算
        if (res.data.orderItemList && res.data.orderItemList.length) {
          if (res.data.orderItemList[0].computeStatus != 1) {
            this.$toast({
              content: res.data.orderItemList[0].reason,
              type: 'error'
            })
            this.$bus.$emit('taxTotalChange', '')
            this.$bus.$emit('freeTotalChange', '')
            this.$bus.$emit('taxPriceChange', '')
            this.$bus.$emit('freePriceChange', '')
            this.$bus.$emit('taxidChange', '')
            this.$bus.$emit('priceUnitChange', '')
            this.$parent.$emit('selectedChanged', {
              fieldCode: 'taxCode',
              itemInfo: { taxCode: '' }
            })
            return
          }
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'taxCode',
            itemInfo: { taxCode: res.data.orderItemList[0].taxCode }
          })
          let taxRate = bigDecimal.multiply(res.data.orderItemList[0].taxRate, 100)
          this.$bus.$emit('taxidChange', taxRate)
          this.$bus.$emit(
            'taxPriceChange',
            // bigDecimal.round(res.data.orderItemList[0].taxPrice, 2)
            res.data.orderItemList[0].taxPrice
          )
          this.$bus.$emit('priceUnitChange', res.data.orderItemList[0].priceUnit)
          this.$bus.$emit(
            'freePriceChange',
            // bigDecimal.round(res.data.orderItemList[0].freePrice, 2)
            res.data.orderItemList[0].freePrice
          )
          this.$bus.$emit('taxTotalChange', bigDecimal.round(res.data.orderItemList[0].taxTotal, 2))
          this.$bus.$emit(
            'freeTotalChange',
            bigDecimal.round(res.data.orderItemList[0].freeTotal, 2)
          )
          let _freePrice = res.data.orderItemList[0].freePrice
          if (_freePrice != this.data.freePrice) {
            //如果不相等
            this.data.freePrice = _freePrice
            console.log(this.dataSource, this.data.freePrice, 'aha ')
          }
          // if (this.quantity) {
          //   //此时有订单数量 ,没有订单数量不用继续算
          //   let taxTotal = bigDecimal.multiply(
          //     this.quantity,
          //     res.data.orderItemList[0].taxPrice
          //   );
          //   taxTotal = bigDecimal.round(taxTotal, 2);
          //   this.$bus.$emit("taxTotalChange", taxTotal);
          //   if (res.data.orderItemList[0].taxRate) {
          //     //如果有税率
          //     let taxid = bigDecimal.add(1, res.data.orderItemList[0].taxRate);
          //     let freeTotal = bigDecimal.divide(taxTotal, taxid);
          //     freeTotal = bigDecimal.round(freeTotal, 2);
          //     this.$bus.$emit("freeTotalChange", freeTotal);
          //   }
          // }
        }
      })
    }
  }
}
</script>
