<template>
  <div class="two-form">
    <div
      v-if="!allowEditing || nowRowHasItemCode"
      :class="['cell-changed', 'cell-changed-disabled']"
      id="cell-changed"
    >
      <mt-input :id="fieldName" style="display: none" :value="data[fieldName]"></mt-input>
      <mt-input
        v-if="fieldName != 'qualityExemptionMark'"
        v-model="data[fieldName]"
        disabled
      ></mt-input>
      <!-- 质量免检标识的列，值是0/1/null，显示是文本 -->
      <span v-else>{{
        data.qualityExemptionMark == 1
          ? $t('免检')
          : data.qualityExemptionMark == 0
          ? $t('需检验')
          : ''
      }}</span>
    </div>
    <div class="selects" v-else>
      <mt-select
        :id="fieldName"
        v-model="data[fieldName]"
        :data-source="dataSource"
        :fields="fields"
        :show-clear-button="true"
        :placeholder="pld"
        @change="selectChange"
      ></mt-select>
    </div>
  </div>
</template>

<script>
// 1. 默认无物料，这些显示下拉：品类、地点/工厂、采购组、基本单位、采购单位、质量免检标识
// 2. 选择了物料，这些变成被带出的字段
// 3. 不可修改的情况 allowEditing
export default {
  components: {
    // cellChanged: "@/components/normalEdit/cellChanged", // 单元格被改变（纯展示）
  },
  data() {
    return {
      data: {},
      fieldName: '',
      nowRowHasItemCode: false, // 默认没有物料编码
      dataSource: [],
      fields: null,
      pld: '',
      changeRowObj: null, // 改变的额外行数据
      changeFieldObj: null, // 改变的其他列数据

      allowEditing: true
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.nowRowHasItemCode = this.data.itemCode ? true : false

    // console.log(
    //   this.data.column.allowEditing,
    //   "我是双重组件的判断是否可以编辑===="
    // );
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return

    // 监听是否有物料
    // this.$bus.$off(`itemCodeChange`);
    this.$bus.$on(`itemCodeChange1`, (txt) => {
      console.log('在双重单元格中，监听到了物料编码变化了--itemCodeChange1', this.fieldName, txt)
      this.nowRowHasItemCode = txt ? true : false
    })

    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      // console.log("￥emit的监听到了被展示的数据------", this.fieldName, txt);
      // that.data[_field] = txt;
      this.$set(this.data, this.fieldName, txt)
    })

    let sessionData = sessionStorage.getItem(`${this.fieldName}Session`)
    if (sessionData) {
      sessionData = JSON.parse(sessionData)
      this.dataSource = sessionData.dataSource || []
      this.changeRowObj = sessionData.changeRowObj || null
      this.changeFieldObj = sessionData.changeFieldObj || null
      this.fields = sessionData.fields || {}
      this.pld = sessionData.pld || ''
    }
  },
  methods: {
    selectChange(e) {
      // console.log(this.$t("下拉选择改变了"), this.fieldName, e);
      // 改变行的额外数据
      if (this.changeRowObj) {
        let _itemInfo = {}
        for (let i in this.changeRowObj) {
          _itemInfo[i] = e.itemData[this.changeRowObj[i]]
        }
        this.$parent.$emit('selectedChanged', {
          fieldCode: this.fieldName,
          itemInfo: _itemInfo
        })
      }

      // 改变行的其他列数据
      if (this.changeFieldObj) {
        for (let i in this.changeFieldObj) {
          this.$bus.$emit(`${i}Change`, e.itemData[this.changeFieldObj[i]] || null)
        }
      }
    }
  },

  beforeDestroy() {
    this.$bus.$off(`itemCodeChange`)
    this.$bus.$off(`${this.fieldName}Change`)
  }
}
</script>

<style lang="scss" scoped>
#cell-changed /deep/ .e-input.e-disabled {
  height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
  padding-left: 10px !important;
  background: #f5f5f5 !important;
}
</style>
