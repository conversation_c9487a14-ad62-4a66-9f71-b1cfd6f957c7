<template>
  <div class="mt-drop">
    <mt-select
      :id="fieldName"
      v-model="data[fieldName]"
      :data-source="dataSource"
      :fields="fields"
      @change="selectChange"
    ></mt-select>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      fieldName: '',
      dataSource: [],
      fields: {},
      valMatchInfo: {},
      changedFieldObj: null
    }
  },
  // computed: {
  //   dataSource() {
  //     let _field = this.data.column.field;
  //     return this.data.rowOptions[`${_field}Data`].dataSource || [];
  //   },
  //   fields() {
  //     let _field = this.data.column.field;
  //     return this.data.rowOptions[`${_field}Data`].fields;
  //   },
  //   valMatchInfo() {
  //     let _field = this.data.column.field;
  //     // 匹配数据源的id、code、name的对应叫法
  //     return this.data.rowOptions[`${_field}Data`].valMatchInfo;
  //   },
  //   changedFieldObj() {
  //     let _field = this.data.column.field;
  //     return this.data.rowOptions[`${_field}Data`].changedFieldObj
  //   }
  // },
  mounted() {
    this.fieldName = this.data.column.field
    let _nowCellSessionInfo = sessionStorage.getItem(`${this.fieldName}Data`)
    console.log('我是每次都进 mtDrop', this.fieldName)

    if (_nowCellSessionInfo) {
      _nowCellSessionInfo = JSON.parse(_nowCellSessionInfo)
      this.dataSource = _nowCellSessionInfo?.dataSource
      this.fields = _nowCellSessionInfo?.fields
      this.valMatchInfo = _nowCellSessionInfo?.valMatchInfo
      this.changedFieldObj = _nowCellSessionInfo?.changedFieldObj
    }
  },
  methods: {
    selectChange(e) {
      console.log(this.$t('下拉改变了'), e)

      // 记录下这行的id、code、name
      // if(this.data[`${this.fieldName}Data`]?.selectedInfo) {
      if (this.valMatchInfo) {
        let { id, code, name } = this.valMatchInfo
        // 第一个表示要提交给后台的字段名，第二个表示该值在数据源中的字段名
        let _data = {}
        if (id) {
          if (id.length == 1) id = [id[0], id[0]]
          _data[id[0]] = e.itemData[id[1]]
        }

        if (code) {
          if (code.length == 1) code = [code[0], code[0]]
          _data[code[0]] = e.itemData[code[1]]
        }

        if (name) {
          if (name.length == 1) name = [name[0], name[0]]
          _data[name[0]] = e.itemData[name[1]]
        }

        let _key = this.data.id || this.data.addId
        this.$parent.$emit('selectedChanged', { [_key]: _data })
        // this.data[`${this.fieldName}Data`].coinDataSelectedInfo = _data;
        console.log(this.$t('组合后的数据'), _data)
      }

      // 如果配置了改变另一些列，就监听
      if (this.changedFieldObj && Object.keys(this.changedFieldObj).length) {
        for (let i in this.changedFieldObj) {
          if (typeof this.changedFieldObj[i] == 'boolean') {
            this.$bus.$emit(`${i}Change`, this.data.index, e.itemData)
          } else {
            this.$bus.$emit(`${i}Change`, this.data.index, e.itemData[this.changedFieldObj[i]])
            // this.$bus.$emit("beDropedChange", this.data.index, e.itemData[name]);
          }
        }
      }
    }
  },
  beforeDestroy() {
    console.log('我进入了下拉的beforeDestory', this.fieldName)
    if (sessionStorage.getItem(`${this.fieldName}Data`)) {
      sessionStorage.removeItem(`${this.fieldName}Data`)
    }
  }
}
</script>

<style></style>
