<template>
  <div class="select-week-time" :value="value">
    <div class="row-item" v-for="(item, index) in value" :key="index">
      <mt-select
        class="week"
        v-model="item.week"
        @input="inputEmit"
        :data-source="weekOptions"
        :show-clear-button="true"
        :placeholder="$t('请选择')"
      ></mt-select>
      <div class="time">
        <mt-time-picker
          class="time-picker"
          :placeholder="$t('请选择')"
          v-model="item.time"
          @input="inputEmit"
          :allow-edit="false"
        ></mt-time-picker>
        <span class="del-btn" @click="removeWeekTimeItem({ item, index })">
          <mt-icon class="btn-icon" name="icon_input_clear" />
        </span>
      </div>
    </div>
    <div class="add-btn-row">
      <span class="add-btn" @click="addWeekTimeItem">
        <mt-icon class="btn-icon" name="icon_card_plus" />
        <span class="btn-title">{{ $t('添加循环时间') }}</span></span
      >
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  mounted() {},
  props: {
    value: {
      type: Array,
      default: () => [{ week: null, time: null }]
    },
    // 周选择下拉数据
    weekOptions: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    inputEmit() {
      this.$emit('input', this.value)
    },
    // 添加一行
    addWeekTimeItem() {
      let hasEmpty = false
      for (let i = 0; i < this.value.length; i++) {
        if (!this.value[i].week || !this.value[i].time) {
          // 存在空的数据
          this.$emit('required', true)
          hasEmpty = true
          break
        }
      }
      if (!hasEmpty) {
        this.value.push({ week: null, time: null })
      }
    },
    // 移除一行
    removeWeekTimeItem(data) {
      this.value.splice(data.index, 1)
    }
  }
}
</script>
<style lang="scss" scoped>
.select-week-time {
  // 行
  .row-item {
    display: flex;
    justify-content: space-between;

    // 周
    .week {
      width: 390px;
    }
    // 时间
    .time {
      width: 390px;
      .time-picker {
        width: calc(390px - 25px);
      }
      // 行删除按钮
      .del-btn {
        display: inline-block;
        line-height: 31px;
        width: 25px;
        text-align: right;
        .btn-icon {
          color: #9baac1;
          cursor: pointer;
        }
      }
    }
  }
  // 添加按钮
  .add-btn-row {
    color: #6386c1;
    margin-top: 8px;
    .add-btn {
      cursor: pointer;
      .btn-icon {
        font-size: 18px;
      }
      .btn-title {
        vertical-align: top;
        margin-left: 8px;
      }
    }
  }
}
</style>
