<template>
  <mt-dialog ref="dialog" :props-data="propsData" :open="onOpen" @save="confirm" @close="cancel">
    <div class="dialog-content">{{ modal['message'] }}</div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        header: this.modalData.title,
        width: '350px'
      }
    },
    modal() {
      return Object.assign(
        {
          title: this.$t('操作'),
          message: this.$t('要删除该数据吗？')
        },
        this.modalData
      )
    }
  },
  mounted() {
    this.$refs['dialog'].open()
  },
  methods: {
    confirm() {
      if (Object.prototype.hasOwnProperty.call(this.modalData, 'confirm')) {
        this.modalData.confirm().then((res) => {
          this.$toast({
            content: res.message ? res.message : this.$t('操作成功'),
            type: 'success'
          })
          this.$emit('confirm-function', res.message)
        })
      } else {
        this.$emit('confirm-function')
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    onOpen: function (args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
