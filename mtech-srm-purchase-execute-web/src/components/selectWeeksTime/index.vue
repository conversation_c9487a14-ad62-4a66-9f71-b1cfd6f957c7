<template>
  <div class="select-week-time" :value="privateValue">
    <div class="box">
      <mt-multi-select
        class="week"
        v-model="privateValue.week"
        @input="inputEmit"
        @blur="blurEmit"
        :data-source="weekOptions"
        :placeholder="$t('请选择')"
        :show-clear-button="true"
      ></mt-multi-select>
      <mt-time-picker
        class="time"
        :placeholder="$t('请选择')"
        v-model="privateValue.time"
        @input="inputEmit"
        @blur="blurEmit"
        :allow-edit="false"
      ></mt-time-picker>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      privateValue: { week: [], time: '' }
    }
  },
  mounted() {},
  props: {
    value: {
      type: Object,
      default: () => {
        return { week: [], time: '' }
      }
    },
    // 周选择下拉数据
    weekOptions: {
      type: Array, // Number[]
      default: () => []
    }
  },
  watch: {
    value: {
      handler(value) {
        if (value) {
          this.privateValue = value
        } else {
          this.privateValue = { week: [], time: '' }
        }
      },
      immediate: true
    }
  },
  methods: {
    inputEmit() {
      this.$emit('input', this.privateValue)
    },
    blurEmit() {
      this.$emit('blur', this.privateValue)
    }
  }
}
</script>
<style lang="scss" scoped>
.select-week-time {
  // 行
  .box {
    display: flex;
    justify-content: space-between;

    // 周
    .week {
      width: calc(50% - 10px);
    }
    // 时间
    .time {
      width: calc(50% - 10px);
    }
  }
}
</style>
