<template>
  <div
    class="mt-input-number"
    :class="{ 'mt-input-number-active': isFocus }"
    :style="{ width: width + 'px' }"
  >
    <span class="input--wrap">
      <input
        ref="ejsRef"
        type="number"
        v-bind="$attrs"
        v-model="ejsValue"
        :step="step"
        :min="min"
        :max="max"
        :disabled="disabled"
        :placeholder="placeholder"
        :readonly="readonly"
        :class="cssClass"
        @blur="handleBlur"
        @focus="handleFocus"
        @input="handleInput"
        @change="handleChange"
      />
    </span>

    <mt-icon
      v-if="ejsValue && showClearButton && !disabled && !readonly"
      name="MT_Close"
      class="icon-clear"
      @click.native="handleClear()"
    ></mt-icon>
    <span
      v-if="unitName.length > 0"
      :class="[
        'unit-name',
        ejsValue && showClearButton && !disabled && !readonly && 'unit-name-mr45'
      ]"
      >{{ unitName }}</span
    >
    <div class="step-box" v-if="showSpinButton && !disabled && !readonly">
      <div class="one-icon">
        <mt-icon
          name="icon_Sort_up"
          class="icon-p icon-up"
          @click.native="handleAction('plus')"
        ></mt-icon>
      </div>
      <div class="one-icon">
        <mt-icon
          name="icon_Sort_down"
          class="icon-p icon-down"
          @click.native="handleAction('minus')"
        ></mt-icon>
      </div>
    </div>
  </div>
</template>

<script>
import { emitterMinxins } from '@mtech-ui/base'
// import { NumericTextBoxPlugin } from '@syncfusion/ej2-vue-inputs'
// Vue.use(NumericTextBoxPlugin)

import bigDecimal from 'js-big-decimal'
import { utils } from '@mtech-common/utils'

export default {
  name: 'MtInputNumber',
  componentName: 'MtInputNumber',
  inheritAttrs: false, // 默认情况下父作用域的不被认作 props 的 attribute 绑定 (attribute bindings) 将会“回退”且作为普通的 HTML attribute 应用在子组件的根元素上。
  mixins: [emitterMinxins],
  inject: {
    mtFormItem: {
      default: ''
    }
  },
  props: {
    value: {
      type: [String, Number],
      required: false,
      default: ''
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    readonly: {
      type: Boolean,
      required: false,
      default: false
    },
    step: {
      type: [String, Number],
      required: false,
      default: 1
    },
    min: {
      type: [Number, String],
      required: false,
      default: null
    },
    max: {
      type: [Number, String],
      required: false,
      default: null
    },
    precision: {
      // 精度
      type: [Number, String],
      required: false,
      default: null
    },
    showClearButton: {
      type: Boolean,
      required: false,
      default: true
    },
    width: {
      type: [String, Number],
      required: false,
      default: null
    },
    cssClass: {
      type: String,
      required: false,
      default: ''
    },
    placeholder: {
      type: String,
      required: false,
      default: ''
    },
    showSpinButton: {
      type: Boolean,
      required: false,
      default: true
    },
    unitName: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      isFocus: false,
      ejsValue: null,
      originVal: null // input输入的值，blur时候如果与当时的ejsValue一致，就不触发input事件
    }
  },
  watch: {
    value(val) {
      if (this.ejsValue !== val) {
        this.ejsValue = val
      }
    },
    precision() {
      this.handleAction()
    },
    max() {
      this.handleAction()
    },
    min() {
      this.handleAction()
    }
  },
  mounted() {
    this.handleInput = utils.debounce(this.handleInput, 600)
    this.handleBlur = utils.debounce(this.handleBlur, 600)
    this.handleFocus = utils.debounce(this.handleFocus, 600)
    this.handleClear = utils.debounce(this.handleClear, 600)
    this.ejsValue = this.value
  },
  methods: {
    handleFocus(event) {
      // event.target.parentNode.className = 'mt-input-number mt-input-number-active'
      this.isFocus = true
      this.$emit('focus', event)
    },

    handleInput() {
      // console.log('发送：handleInput-------1------', event)
      // // 如果不在最小值和最大值的范围内，直接修改成 临界值
      // this.ejsValue = this.judgeBigger(event.target.value)
      // // 判断如果小数点超过了 设置的精度，截取掉
      // if(String(this.ejsValue).includes('.') && this.ejsValue.split('.')[1] && this.ejsValue.split('.')[1].length > this.precision) {
      //   this.ejsValue = Number(this.ejsValue).toFixed(this.precision)
      // }
      this.originVal = this.ejsValue
      this.$emit('input', Number(this.ejsValue))
    },

    handleBlur(event) {
      // console.log('发送：失去了光标了----------',event)
      // event.target.parentNode.className = 'mt-input-number'
      this.isFocus = false
      const _iptVal = event.target.value
      if (!_iptVal || _iptVal === undefined || isNaN(_iptVal)) {
        this.ejsValue = null
      } else {
        this.ejsValue = this.judgeBigger(_iptVal, true)
      }
      // 如果数据没改变，就不触发input
      if (Number(this.originVal) !== Number(this.ejsValue)) {
        this.$emit('input', Number(this.ejsValue))
      }
      this.originVal = this.ejsValue
      this.$emit('blur', event)
      // this.dispatch('MtFormItem', 'mt.form.blur', [this.ejsValue])
    },

    handleChange(event) {
      // console.log('发送：handleChange----------',event)
      this.$emit('change', Number(event.target.value))
      // this.dispatch('MtFormItem', 'mt.form.change', [this.ejsValue])
    },

    precisionVal(num) {
      if (isNaN(Number(num))) {
        return
      }
      if (this.precision) {
        return parseFloat(
          (
            Math.round((num + Number.EPSILON) * Math.pow(10, this.precision)) /
            Math.pow(10, this.precision)
          ).toFixed(this.precision)
        )
        // return Number(num).toFixed(this.precision)
      }
      return num
    },

    judgeBigger(params) {
      let _res = null
      // console.log('判断大小', this.min, this.max)
      if ((!!this.min || this.min == 0) && Number(params) < this.min) {
        _res = this.min
      } else if ((!!this.max || this.max == 0) && Number(params) > this.max) {
        _res = this.max
      } else {
        _res = Number(params)
      }
      // if(flag) {
      console.log('_res', _res)
      return this.precisionVal(_res)
      // }
      // return _res;
    },

    handleAction(flag) {
      if (flag === 'plus') {
        this.ejsValue = Number(bigDecimal.add(this.ejsValue, this.step))
      } else if (flag === 'minus') {
        this.ejsValue = Number(bigDecimal.subtract(this.ejsValue, this.step))
      }
      this.ejsValue = this.judgeBigger(this.ejsValue, true)
      this.$emit('change', this.ejsValue)
      this.$emit('input', Number(this.ejsValue))
      // this.dispatch('MtFormItem', 'mt.form.blur', [this.ejsValue])
    },

    handleClear() {
      // console.log(this.$refs.ejsRef)
      this.ejsValue = null
      this.$emit('input', Number(this.ejsValue))
      // this.dispatch('MtFormItem', 'mt.form.blur', [Number(this.ejsValue)])
    }
  }
}
</script>
