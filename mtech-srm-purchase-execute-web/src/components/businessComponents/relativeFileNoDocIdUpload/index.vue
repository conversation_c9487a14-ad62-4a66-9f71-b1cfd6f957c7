<template>
  <div class="r-d-container mt-flex">
    <div class="tree-view--wrap" v-show="!hideLeftNode">
      <mt-common-tree
        v-if="treeViewData.dataSource.length"
        ref="treeView"
        class="tree-view--template"
        :un-button="true"
        :fields="treeViewData"
        :selected-nodes="selectedNodes"
        :expanded-nodes="expandedNodes"
        @nodeSelected="nodeSelected"
      ></mt-common-tree>
    </div>
    <div class="table-container">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
        v-if="this.pageConfig[0].grid.columnData"
      >
      </mt-template-page>
    </div>
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog" @confirm="uploaderDialogOnConfirm"></uploader-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { download } from '@/utils/utils'
import { rdToolBar, rdColumnData, itemOtherCol, nodeType } from './config/relative.js'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import { API } from '@mtech-common/http'

export default {
  components: {
    UploaderDialog
  },
  props: {
    fileQueryParms: {
      type: Object,
      default: () => {}
    },
    moduleFileList: {
      type: Array,
      default: () => []
    },
    btnRequired: {
      // 是否可以进行某种操作：hasUpload: 、hasDownload、 hasDelete
      type: Object,
      default: () => {
        return {
          hasUpload: false,
          hasDownload: true,
          hasDelete: false
        }
      }
    },
    requireFiles: {
      type: Array,
      default: () => []
    },
    // 隐藏左侧节点：目前只有采购申请需要
    hideLeftNode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      saved: {}, //暂存的数据
      selectNodeId: '',
      selectNodeType: '',
      selectedNodes: [],
      expandedNodes: [],
      treeViewData: {
        //相关文件-目录结构-原数据
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxs">
                            <div>{{mtData.nodeName}}</div>
                          </div>`,
              data() {
                return { data: {} }
              },
              props: {
                mtData: {
                  // 拿到数据
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'id',
        text: 'nodeName'
        // child: "fileNodeResponseList", // 暂时不考虑子节点
      },
      pageConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: rdToolBar(this.btnRequired), // 双重数组，在rdToolBar中
          grid: {
            allowPaging: false,
            columnData: rdColumnData(this.btnRequired).concat(itemOtherCol),
            dataSource: []
          }
        }
      ],
      nodeCode: '', // 节点code， 整单：xx_header_file，明细：xx_item_file=====item的多几列
      isFirst: true // 是第一次打开
    }
  },

  watch: {
    moduleFileList: {
      handler(newVal) {
        if (newVal && Array.isArray(newVal) && newVal.length) {
          this.getFileFolderData(newVal)
        }
      },
      immediate: true
    },
    selectNodeType(newVal) {
      this.handleSelectNodeType(newVal)
    },
    // 同步时，监听表格数据源变化
    requireFiles(newVal) {
      if (this.selectNodeType == '02') {
        this.$set(this.pageConfig[0].grid, 'dataSource', newVal)
      }
    }
  },

  methods: {
    // 处理 toolbar和columnData
    handleSelectNodeType(data) {
      let _columnData
      this.$set(this.pageConfig[0].grid, 'columnData', null)
      if (data == nodeType.mainUpdate || data == nodeType.mainUpdateEdit) {
        let _btnRequired = {
          hasUpload: true,
          hasDownload: true,
          hasDelete: true
        }
        this.$set(this.pageConfig[0], 'toolbar', rdToolBar(_btnRequired))
        _columnData = rdColumnData(_btnRequired)
      } else if (data == nodeType.itemView || data == nodeType.itemUpdate) {
        this.$set(this.pageConfig[0], 'toolbar', rdToolBar(this.btnRequired))
        _columnData = rdColumnData(this.btnRequired).concat(itemOtherCol)
      } else if (data == nodeType.mainView) {
        this.$set(this.pageConfig[0], 'toolbar', rdToolBar(this.btnRequired))

        _columnData = rdColumnData(this.btnRequired)
      } else if (data == nodeType.mainViewData) {
        let _btnRequired = {
          hasUpload: false,
          hasDownload: true,
          hasDelete: false
        }
        this.$set(this.pageConfig[0], 'toolbar', rdToolBar(_btnRequired))
        _columnData = rdColumnData(_btnRequired) // 按照默认值，即不允许上传、删除
      }
      this.$nextTick(() => {
        this.$set(this.pageConfig[0].grid, 'columnData', _columnData)
      })
    },
    // 获取左侧节点，如果有，默认获取第一个的文件列表
    getFileFolderData(newVal) {
      if (Array.isArray(newVal) && newVal.length) {
        this.$set(this.treeViewData, 'dataSource', newVal)

        if (this.isFirst) {
          let hasMainUpdateEditFlag = false
          this.moduleFileList.forEach((item, index) => {
            if (item.type == nodeType.mainUpdateEdit) {
              hasMainUpdateEditFlag = true
              // this.$store.commit("startLoading");
              API[item.methods](item.url, item.params).then((res) => {
                this.saved[item.id] = res?.data || []
                this.$emit('initData', { data: res?.data || [], index })
                this.setSelectInfo(newVal)
                // this.$store.commit("endLoading");
              })
            }
          })

          // 如果没有 编辑已上传的附件，直接赋值选中第一个节点，并获取对应附件列表
          if (!hasMainUpdateEditFlag) {
            this.setSelectInfo(newVal)
          }
        } else {
          this.setSelectInfo(newVal)
        }
        this.isFirst = false
      }
    },

    // 赋值默认选择第一个节点
    setSelectInfo(newVal) {
      this.selectNodeId = newVal[0]['id']
      this.selectNodeType = newVal[0]['type']
      this.handleSelectNodeType(this.selectNodeType)
      this.nodeSelected({
        nodeData: {
          id: this.selectNodeId,
          type: this.selectNodeType,
          url: newVal[0]['url'],
          params: { ...newVal[0]['params'] },
          dataSource: newVal[0].dataSource
        }
      })
      this.selectedNodes = [newVal[0]['id']]
      this.expandedNodes = [newVal[0]['id']]
    },

    // 点击切换，获取文件列表
    nodeSelected(event) {
      if (event?.nodeData?.id) {
        this.selectNodeId = event?.nodeData?.id

        let nodeData = this.treeViewData.dataSource.find((item) => item.id == this.selectNodeId)
        this.selectNodeType = nodeData.type

        if (this.selectNodeType == nodeType.itemView || this.selectNodeType == nodeType.mainView) {
          this.$set(this.pageConfig[0].grid, 'dataSource', [])
          this.$set(this.pageConfig[0].grid, 'asyncConfig', {
            url: nodeData.url,
            params: nodeData.params,
            recordsPosition: 'data',
            methods: nodeData.methods ?? 'post'
          })
        } else if (
          this.selectNodeType == nodeType.mainUpdate ||
          this.selectNodeType == nodeType.mainUpdateEdit
        ) {
          this.$set(this.pageConfig[0].grid, 'asyncConfig', {})
          this.$set(this.pageConfig[0].grid, 'dataSource', this.saved?.[this.selectNodeId] || [])
        } else if (this.selectNodeType == nodeType.itemUpdate) {
          this.$set(this.pageConfig[0].grid, 'asyncConfig', {})
          this.$set(this.pageConfig[0].grid, 'dataSource', this.requireFiles)
        } else if (this.selectNodeType == nodeType.mainViewData) {
          this.$set(this.pageConfig[0].grid, 'dataSource', nodeData.dataSource)
        }
      }
    },

    handleClickToolBar(e) {
      if (e.toolbar.id === 'upload') {
        this.showFileBaseInfo()
      } else if (e.toolbar.id == 'download') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          _selectRows.forEach((item) => {
            this.handlerDownloadPrivateFile(item)
          })
        }
      } else if (e.toolbar.id == 'delete') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          this.handleBatchDelete(_selectRows)
        }
      }
    },
    // 下载文件
    handlerDownloadPrivateFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPrivateFile({
          id: data.sysFileId || data.id
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: data.fileName, blob: res.data })
        })
    },

    uploaderDialogOnConfirm(args) {
      const { fileList } = args
      // 批量执行上传文件
      fileList.map((item) => {
        this.handleUploadFiles(item)
      })
    },
    //执行上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, remoteUrl, sysName, createTime } = data
      const { username: createUserName } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      let _params = {
        docId: 0,
        docType: 'po',
        parentId: this.selectNodeId,
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        // remoteUrl: remoteUrl,
        sysFileId: id,
        sysName: sysName,
        url: remoteUrl,
        nodeId: this.selectNodeId,
        createUserName, // 创建人
        createTime // 创建时间
        // docType: "",
        // id: 0,
        // nodeId: 0,
        // nodeName: "",
        // nodeType: 0,
        // sortValue: 0,
      }
      let hasItem = false
      Object.keys(this.saved).some((item) => {
        if (item === _params.nodeId) {
          this.saved[item].push(_params)
          hasItem = true
        }
      })
      if (!hasItem) {
        this.$set(this.saved, _params.nodeId, [_params])
      }
      this.setDataSource()
    },
    //设置列表
    setDataSource() {
      Object.keys(this.saved).some((item) => {
        console.log('item', item)
        if (
          this.selectNodeType == nodeType.mainUpdate ||
          this.selectNodeType == nodeType.mainUpdateEdit
        ) {
          this.$set(this.pageConfig[0].grid, 'dataSource', this.saved[this.selectNodeId])
        }
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      console.log('_selectGridRecords', _selectGridRecords)
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.sysFileId)
      })
      this.handleDelete(_selectIds)
    },
    //删除文件
    handleDelete(ids) {
      let _this = this
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          for (let i = 0; i < ids.length; i++) {
            for (let j = 0; j < _this.saved[_this.selectNodeId].length; j++) {
              if (_this.saved[_this.selectNodeId][j].sysFileId === ids[i]) {
                _this.saved[_this.selectNodeId].splice(j, 1)
              }
            }
          }
        }
      })
    },
    //单元格按钮，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'download') {
        // window.open(e.data.remoteUrl);
        this.handlerDownloadPrivateFile(e.data)
        // window.open(e.data.url);
      } else if (e.tool.id == 'delete') {
        this.handleDelete([e.data.sysFileId])
      }
    },
    //单元格title文字点击，文件名点击预览
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle3', e)
      if (e.field == 'fileName') {
        let params = {
          id: e.data.sysFileId || e.data.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(
            // `${res.data}/onlinePreview?url=${e.data.remoteUrl || e.data.url}`
            `${res.data}`
          )
        })
      }
    },

    getUploadFlies(id) {
      if (typeof this.saved[id] != 'undefined' && this.saved[id].length > 0) {
        return this.saved[id]
      } else {
        return []
      }
    },
    // 显示表格文件弹窗
    showFileBaseInfo() {
      const dialogParams = {
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    }
  }
}
</script>

<style lang="scss" scoped>
.r-d-container {
  width: 100%;
  height: 100%;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .tree-view--wrap {
    min-width: 200px;
    background: #fff;
    box-shadow: inset -1px 0 0 0 rgba(232, 232, 232, 1);
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 50px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 20px;

      /deep/ .e-ul {
        .e-list-item {
          text-align: center;
          line-height: 1;
          font-size: 14px;
          color: #292929;
          padding-top: 6px;
          padding-bottom: 6px;
          &.e-active {
            background: #f5f6f9;
            border-right: 2px solid #6386c1;
          }
        }
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: auto;
  }

  .upload-input {
    width: 0;
  }
}
</style>
