<template>
  <div>
    <mt-input :id="id" style="display: none" :value="iptVal"></mt-input>
    <mt-select
      v-if="remoteAotuQuery"
      v-model="selectVal"
      :disabled="disabled"
      :style="`width: ${width}px`"
      :allow-filtering="true"
      :filtering="getDatalist"
      :data-source="dataList"
      :placeholder="placeholder"
      :fields="{ text: fields.text, value: fields.value }"
      :show-clear-button="true"
      @select="handleSelect"
      @change="handleChange"
    ></mt-select>
    <mt-select
      v-else
      v-model="selectVal"
      :disabled="disabled"
      :style="`width: ${width}px`"
      :allow-filtering="true"
      filter-type="Contains"
      :data-source="dataList"
      :placeholder="placeholder"
      :fields="{ text: fields.text, value: fields.value }"
      :show-clear-button="true"
      @select="handleSelect"
      @change="handleChange"
    ></mt-select>
  </div>
</template>

<script>
// import { cloneDeep } from "lodash";
import { utils } from '@mtech-common/utils'
/**
 * 1.使用mt-select的filtering，在输入数据时，调用接口，重新赋值列表数据。此时，filtering，需要加debounce
 * 2.如果是'未匹配'到数据，使用用户输入数据，可以参考链接：
 *   https://ej2.syncfusion.com/vue/demos/#/material/multi-select/custom-value.html
 * 过滤获取到值后未赋上问题修复：
 *  先修改dataSource的值，再使用updateData方法
 */
export default {
  props: {
    id: {
      // 行内编辑中必传
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 150
    },
    fields: {
      // 必传
      // {text:'', value: ""}
      type: Object,
      default: () => {}
    },
    // 请求地址
    requestUrl: {
      // 必传
      // {pre: 'masterData', url: 'getUserListByTenantId'}
      type: Object,
      default: () => {}
    },
    // 请求参数的key
    requestKey: {
      // 必传
      type: String,
      default: ''
    },
    initVal: {
      type: String,
      default: null
    },
    // 显示 code-name 组合的值是什么，非必传
    labelShowObj: {
      type: Object,
      default: () => {}
    },
    otherParams: {
      type: Object,
      default: () => {}
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: () => false
    },
    // 是否启用远程搜索  默认为是
    remoteAotuQuery: {
      type: Boolean,
      default: () => true
    }
  },
  computed: {
    iptVal() {
      return this.selectVal || this.initVal
    }
  },
  watch: {
    initVal: {
      handler(newVal) {
        this.selectVal = newVal
      },
      immediate: true
    }
  },
  data() {
    return {
      selectVal: this.initVal, // initVal 也时刻被改变着
      dataList: [],
      dataLimit: 20, // 限制返回条数
      valueTemplate: null
    }
  },
  mounted() {
    console.log(this.$t('进入来了'), this.initVal)
    // 如果有初始值
    if (this.initVal) {
      // 如果是code-name，就需要分隔，根据name 搜索
      if (this.labelShowObj && Object.keys(this.labelShowObj).length) {
        this.getDatalist({ text: this.initVal.split(' - ')[1] })
      } else {
        this.getDatalist({ text: this.initVal })
      }
    } else {
      this.getDatalist({ text: '' }) // 否则列出默认的前20条
    }

    //引入mtech-common/utils中的防抖，(mtech-common/utils )
    this.getDatalist = utils.debounce(this.getDatalist, 300)
  },
  methods: {
    getDatalist(e = { text: '' }) {
      // dataLimit 限制返回条数
      this.$API[this.requestUrl.pre]
        [this.requestUrl.url]({
          [this.requestKey]: e.text,
          dataLimit: this.dataLimit,
          ...this.otherParams
        })
        .then((res) => {
          console.log(this.$t('我得到了结果'), e, res.data)

          this.dataList = res.data

          // 如果有 拼接 code-名称，放到 labelShow 字段里
          if (this.labelShowObj && Object.keys(this.labelShowObj).length) {
            this.dataList.forEach((i) => {
              i.labelShow = i[this.labelShowObj.code] + ' - ' + i[this.labelShowObj.name]
            })
          }

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(res.data)
            }
          })

          if (res.total > this.dataLimit) {
            this.$toast({
              content: this.$t('搜索结果较多，请再输入更精确的查询条件'),
              type: 'warning'
            })
          }

          this.selectVal = this.initVal
        })
    },

    handleSelect(e) {
      // console.log(this.$t("下拉改变了"), e);
      this.$emit('handleSelect', e)
    },

    handleChange(e) {
      console.log('handleChange', e)
      this.$emit('handleChange', e)
      // 如果清空选中值
      if (!e.value && e.value != 0) {
        this.getDatalist({ text: '' }) // 否则列出默认的前20条
        this.$emit('update:initVal', null)
      } else {
        this.$emit('update:initVal', e.value)
      }
    }
  }
}
</script>
