<template>
  <div class="status-check">
    <div
      :class="['one-status', index == currentIndex && 'one-status-active']"
      v-for="(item, index) in statusList"
      :key="index"
      @click="handleChose(item, index)"
    >
      <span>{{ $t(item.label) }}</span>
      <span class="num-flag">{{ item.num }}</span>
      <!-- <mt-icon
        name="icon_Close_2"
        :class="[index != currentIndex && 'icon-hidden']"
        @click.native.stop="handleDelete()"
      ></mt-icon> -->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    statusData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentIndex: -1
      // statusList: [],
    }
  },
  computed: {
    statusList: {
      get() {
        return this.statusData.map((item) => {
          return {
            ...item,
            check: false
          }
        })
      },
      set(newVal) {
        console.log(this.$t('新值'), newVal)
      }
    }
  },
  watch: {
    statusList: {
      handler(newVal, oldVal) {
        // 默认选中第一个
        let oldVal1 = oldVal || []
        let hasOne = oldVal1.some((item) => {
          return item.check
        })
        if (hasOne) {
          newVal.forEach((item1) => {
            oldVal1.some((itme2) => {
              if (itme2.check) {
                item1.check = true
              }
            })
          })
          return
        }
        if (newVal.length) {
          this.handleChose(newVal[0], 0)
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.statusList = []
  },
  methods: {
    handleChose(item, index) {
      this.currentIndex = index
      this.$set(this.statusList[index], 'check', true)
      this.$emit('handleChose', item.value, item)
      console.log(this.$t('点击了'), item, index)
    },
    handleDelete() {
      this.$set(this.statusList[this.currentIndex], 'check', false)
      this.currentIndex = -1
      this.$emit('handleChose', null)
    }
  }
}
</script>

<style lang="scss" scoped>
.status-check {
  display: inline-flex;
  align-items: center;
  span {
    white-space: nowrap;
  }
  overflow: auto;
}
.one-status {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  color: rgba(41, 41, 41, 1);
  cursor: pointer;
  padding: 6px;
  border: 1px solid transparent;
  position: relative;

  span:first-child {
    display: inline-block;
    padding: 0 10px;
  }
  .icon-hidden {
    visibility: hidden;
  }

  .num-flag {
    position: absolute;
    right: -2px;
    top: -4px;
    padding: 2px 5px;
    border-radius: 4px;
    white-space: nowrap;
    background-color: rgba(214, 65, 19, 0.1);
    color: #d64113;
    transform: scale(0.75);
  }

  &-active {
    background: rgba(0, 70, 156, 0.06);
    border: 1px solid rgba(0, 70, 156, 0.1);
    border-radius: 4px;

    span:first-child {
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }

    .mt-icons {
      color: #9daabf;
    }
  }
}
</style>
