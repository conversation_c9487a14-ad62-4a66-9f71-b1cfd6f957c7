<template>
  <div class="r-d-container mt-flex">
    <div class="tree-view--wrap">
      <!-- <div class="trew-node--add">
        <div class="node-title">{{ $t("层级") }}</div>
      </div> -->
      <mt-common-tree
        v-if="treeViewData.dataSource.length"
        ref="treeView"
        class="tree-view--template"
        :un-button="true"
        :fields="treeViewData"
        :selected-nodes="selectedNodes"
        :expanded-nodes="expandedNodes"
        @nodeSelected="nodeSelected"
      ></mt-common-tree>
    </div>
    <div class="table-container">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
      </mt-template-page>
    </div>
    <input
      type="file"
      ref="uploadIptRef"
      class="upload-input"
      style="visibility: hidden; width: 0"
      @change="chooseFiles"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { download } from '@/utils/utils'
import { rdColumnData, itemOtherCol, centerCol } from './config/relative1.js'

export default {
  props: {
    requestUrlObj: {
      type: Object,
      default: () => {}
    },
    moduleFileList: {
      type: Array,
      default: () => []
    },
    entryType: {
      type: String,
      default: '1'
    },
    entryId: {
      type: String,
      default: '0'
    },
    entryFileList: {
      type: Array,
      default: () => []
    },
    newFileList: {
      //修改后最新的明细文件
      type: Array,
      default: () => []
    },
    changeNodeCode: {
      type: String,
      default: '0'
    },
    btnRequired: {
      // 是否可以进行某种操作：hasUpload: 、hasDownload、 hasDelete
      type: Object,
      default: () => {
        return {
          hasUpload: true,
          hasDownload: true,
          hasDelete: true
        }
      }
    }
  },
  data() {
    return {
      selectedNodes: [],
      selectNodeId: '',
      selectedInfo: {},
      expandedNodes: [],
      treeViewData: {
        //相关文件-目录结构-原数据
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxs">
                            <div>{{mtData.nodeName}}</div>
                          </div>`,
              data() {
                return { data: {} }
              },
              props: {
                mtData: {
                  // 拿到数据
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'nodeCode',
        text: 'nodeName'
        // child: "fileNodeResponseList", // 暂时不考虑子节点
      },
      pageConfig: [
        {
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowPaging: false,
            columnData: rdColumnData(this.btnRequired),
            dataSource: []
          }
        }
      ],
      saved: {}, //暂存的数据
      addId: 1
    }
  },
  watch: {
    moduleFileList: {
      handler(newVal) {
        if (newVal) {
          this.getFileFolderData(newVal)
        }
      },
      immediate: true
    },
    entryFileList: {
      handler(val) {
        if (val.length) {
          console.log(val, '带入附件')
          val.forEach((params) => {
            let hasItem = false
            Object.keys(this.saved).some((item) => {
              if (item === params.nodeCode) {
                this.saved[item].push(params)
                hasItem = true
              }
            })
            if (!hasItem) {
              this.$set(this.saved, params.nodeCode, [params])
            }
            this.setDataSource()
            this.updateFile()
          })
        } else {
          this.saved = {}
          this.setDataSource()
          this.updateFile()
        }
      },
      immediate: true
    },
    newFileList: {
      handler(newVal) {
        this.saved[this.changeNodeCode] = newVal
        this.setDataSource()
      },
      immediate: true
    }
  },
  methods: {
    setColumnData() {
      let _btnRequired = {
        hasUpload: false,
        hasDownload: true,
        hasDelete: false
      }
      console.log('nodeCode有新值了1ds fdsfd ', this.selectNodeId)
      if (!this.selectNodeId) return
      if (this.entryType === '3' || this.entryType === '5') {
        this.pageConfig[0].toolbar = []
        if (this.selectNodeId.includes('item')) {
          this.$set(
            this.pageConfig[0].grid,
            'columnData',
            rdColumnData(_btnRequired, '3').concat(centerCol).concat(itemOtherCol)
          )
        } else {
          this.$set(
            this.pageConfig[0].grid,
            'columnData',
            rdColumnData(_btnRequired, '3').concat(centerCol)
          )
        }
      }
      if (this.entryType === '1' || this.entryType === '2' || this.entryType === '4') {
        if (this.selectNodeId.includes('item')) {
          this.pageConfig[0].toolbar = []
          this.$set(
            this.pageConfig[0].grid,
            'columnData',
            rdColumnData(_btnRequired).concat(centerCol).concat(itemOtherCol)
          )
        } else {
          console.log('his.btnRequired', this.btnRequired)
          this.$set(
            this.pageConfig[0].grid,
            'columnData',
            rdColumnData(this.btnRequired).concat(centerCol)
          )
          console.log(this.$route, this.selectNodeId)
          if (this.$route.name === 'purchase-coordination-detail') {
            if (this.selectNodeId === 'po_sup_header_file') {
              this.pageConfig[0].toolbar = []
            }
            if (this.selectNodeId === 'po_header_file') {
              this.setTooBar()
            }
          }
          if (this.$route.name === 'custom-receipt-detail' && this.entryType === '1') {
            if (this.selectNodeId === 'po_sup_header_file') {
              this.setTooBar()
            }
            if (this.selectNodeId === 'po_header_file') {
              this.pageConfig[0].toolbar = []
            }
          }
        }
      }
    },
    setTooBar() {
      this.pageConfig[0].toolbar = [
        [
          {
            id: 'upload',
            icon: 'icon_solid_upload',
            title: this.$t('上传')
          },
          {
            id: 'download',
            icon: 'icon_solid_Download',
            title: this.$t('下载')
          },
          {
            id: 'delete',
            icon: 'icon_solid_Delete',
            title: this.$t('删除')
          }
        ]
      ]
    },
    // 获取左侧节点，如果有，默认获取第一个的文件列表
    getFileFolderData(newVal) {
      this.$set(this.treeViewData, 'dataSource', newVal)
      if (Array.isArray(newVal) && newVal.length) {
        this.selectNodeId = newVal[0]['nodeCode']
        this.nodeSelected({ nodeData: { id: this.selectNodeId } })
        this.setColumnData()
        this.selectedNodes = [newVal[0]['nodeCode']]
        this.expandedNodes = [newVal[0]['nodeCode']]
        this.setSelectInfo()
      }
    },
    // 点击切换，获取文件列表
    nodeSelected(event) {
      console.log('nodeSelected---', event)
      if (event?.nodeData?.id) {
        this.selectNodeId = event?.nodeData?.id
        // this.$set(this.pageConfig[0].grid, "asyncConfig", {
        //   url: this.requestUrlObj?.fileUrl,
        //   params: {
        //     docId: this.docId,
        //     parentId: this.selectNodeId,
        //   },
        //   methods: "get",
        //   recordsPosition: "data",
        // });
        this.setColumnData()
        let hasItem = false
        Object.keys(this.saved).some((item) => {
          if (item == this.selectNodeId) {
            this.pageConfig[0].grid.dataSource = this.saved[item]
            hasItem = true
          }
        })
        if (!hasItem) {
          this.pageConfig[0].grid.dataSource = []
        }
        this.setSelectInfo()
      }
    },
    setSelectInfo(list) {
      let hasItem = false
      if (list) {
        list.some((item) => {
          if (item.nodeCode == this.selectNodeId) {
            hasItem = true
            this.selectedInfo = item
            return
          }
        })
        if (!hasItem) {
          let list1 = []
          list.forEach((item) => {
            list1.push(item.moduleFileList)
          })
          this.setSelectInfo(list1)
        }
      } else {
        this.moduleFileList.some((item) => {
          if (item.nodeCode == this.selectNodeId) {
            hasItem = true
            this.selectedInfo = item
          }
        })
        if (!hasItem) {
          let list = []
          this.moduleFileList.forEach((item) => {
            list.push(item.moduleFileList)
          })
          this.setSelectInfo(list)
        }
      }
    },
    handleClickToolBar(e) {
      console.log('use-handleClickToolBar', e)
      if (e.toolbar.id === 'upload') {
        this.$refs.uploadIptRef.click()
      } else if (e.toolbar.id == 'download') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          // let _url = _selectRows[0]["remoteUrl"];
          // window.open(_url);
          _selectRows.forEach((item) => {
            this.handlerDownloadPrivateFile(item)
          })
        }
      } else if (e.toolbar.id == 'delete') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          this.handleBatchDelete(_selectRows)
        }
      }
    },
    // 下载文件
    handlerDownloadPrivateFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPrivateFile({
          id: data.sysFileId
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: data.fileName, blob: res.data })
        })
    },
    // 点击上传文件
    chooseFiles(data) {
      console.log(this.$t('点击上传'), data, this.$parent)
      let _files = event.target.files
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$store.commit('startLoading')
      this.$API.fileService.uploadPrivateFile(_data).then((res) => {
        this.$store.commit('endLoading')
        this.handleUploadFiles(res?.data)
      })
    },
    //执行上传文件
    async handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, remoteUrl, createTime } = data
      const { username: createUserName } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      let params = {
        docId: this.entryId,
        docType: 'po',
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        lineNo: this.selectedInfo.lineNo,
        nodeCode: this.selectedInfo.nodeCode,
        nodeName: this.selectedInfo.nodeName,
        nodeId: this.selectedInfo.id,
        nodeType: 1,
        orderDetailId: 0,
        orderId: this.entryId,
        remark: '',
        sysFileId: id,
        type: 0,
        url: remoteUrl,
        addId: this.addId++,
        createUserName, // 创建人
        createTime // 创建时间
      }
      if (this.$route.name == 'sales-coordination-detail') {
        params.docType = 'as_po'
        params.parentId = this.selectedInfo.id
      }
      if (this.$route.name === 'custom-receipt-detail') {
        params.docType = 'so'
        await this.saveRelativeFile(params)
        return
      }
      this.updateCurrentFile(params)
    },
    updateCurrentFile(params) {
      let hasItem = false
      Object.keys(this.saved).some((item) => {
        if (item === params.nodeCode) {
          this.saved[item].push(params)
          hasItem = true
        }
      })
      if (!hasItem) {
        this.$set(this.saved, params.nodeCode, [params])
      }
      this.setDataSource()
      this.updateFile()
    },
    async saveRelativeFile(params) {
      //供方采购订单 单独接口上传附件
      await this.$API.purchaseOrder.saveRelativeFile(params).then(() => {})
      //附件带入的信息
      this.getNewList()
    },
    async getNewList() {
      let params1 = {
        docId: this.entryId,
        parentId: 0,
        docType: 'so'
      }
      await this.$API.purchaseOrder.queryFileByDocId(params1).then((res) => {
        let entryFileList = []
        res.data.forEach((item) => {
          if (item.nodeType === 1) {
            item.id1 = item.id
            item.id = item.sysFileId
            item.remoteUrl = item.url
            entryFileList.push(item)
          }
        })
        this.saved['po_sup_header_file'] = entryFileList
        this.setDataSource()
        this.updateFile()
      })
    },
    //设置列表
    setDataSource() {
      Object.keys(this.saved).some((item) => {
        if (item === this.selectNodeId) {
          this.pageConfig[0].grid.dataSource = this.saved[item]
        }
      })
    },
    updateFile() {
      let fileList = []
      Object.keys(this.saved).forEach((item) => {
        if (!item.includes('item')) {
          fileList = fileList.concat(this.saved[item])
        }
      })
      this.$emit('updateFile', fileList)
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      let _selectIds1 = []
      let _selectIds2 = []
      _selectGridRecords.map((item) => {
        if (item.addId) {
          _selectIds1.push(item.addId)
        }
        if (item.id) {
          _selectIds.push(item.id)
        }
        if (item.id1) {
          _selectIds2.push(item.id1)
        }
      })
      if (this.$route.name === 'custom-receipt-detail') {
        this.handleDelete1(_selectIds2)
      } else if (this.$route.name === 'purchase-coordination-detail') {
        this.handleDelete2(_selectIds, _selectIds1)
      } else {
        this.handleDelete(_selectIds, _selectIds1)
      }
    },
    handleDelete1(ids) {
      //供方采购订单使用接口删除
      let _params = {
        idList: ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.fileService.deleteFile(_params).then(() => {
            this.$store.commit('endLoading')
            this.getNewList()
          })
        }
      })
    },
    handleDelete2(ids, ids1) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          Object.keys(this.saved).some((item) => {
            if (item == this.selectNodeId) {
              this.saved[item].forEach((item1, index1) => {
                if (item1.id) {
                  ids.some((item2) => {
                    if (item2 == item1.id) {
                      this.saved[item].splice(index1, 1)
                    }
                  })
                }
                if (item1.addId) {
                  ids1.some((item2) => {
                    if (item2 == item1.addId) {
                      this.saved[item].splice(index1, 1)
                    }
                  })
                }
              })
            }
          })
          this.setDataSource()
          this.updateFile()
        }
      })
    },
    //删除文件
    handleDelete(ids, ids1) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          Object.keys(this.saved).some((item) => {
            if (item == this.selectNodeId) {
              this.saved[item].forEach((item1, index1) => {
                console.log(item1)
                if (item1.addId) {
                  ids1.some((item2) => {
                    if (item2 == item1.addId) {
                      this.saved[item].splice(index1, 1)
                    }
                  })
                }
                if (item1.id) {
                  ids.some((item2) => {
                    if (item2 == item1.addId) {
                      this.saved[item].splice(index1, 1)
                    }
                  })
                }
              })
            }
          })
          this.setDataSource()
          this.updateFile()
        }
      })
    },
    //单元格按钮，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'download') {
        // window.open(e.data.remoteUrl);
        this.handlerDownloadPrivateFile(e.data)
      } else if (e.tool.id == 'delete') {
        if (this.$route.name === 'custom-receipt-detail') {
          this.handleDelete1([e.data.id1])
        } else if (this.$route.name === 'purchase-coordination-detail') {
          this.handleDelete2([e.data.id], [])
        } else {
          this.handleDelete([], [e.data.addId])
        }
      }
    },
    //单元格title文字点击，文件名点击预览
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle2', e)
      if (e.field == 'fileName') {
        let params = {
          id: e.data.id || e.data.sysFileId,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(
            // `${res.data}/onlinePreview?url=${e.data.remoteUrl || e.data.url}`
            `${res.data}`
          )
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.r-d-container {
  width: 100%;
  // overflow: hidden;
  // margin-top: 20px;
  // margin-left: 10px;
  height: 100%;
  // box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .table-container {
    width: 100%;
    overflow-x: auto;
  }
  .tree-view--wrap {
    min-width: 200px;
    background: #fff;
    box-shadow: inset -1px 0 0 0 rgba(232, 232, 232, 1);
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 50px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 20px;

      /deep/ .e-ul {
        .e-list-item {
          text-align: center;
          line-height: 1;
          font-size: 14px;
          color: #292929;
          padding-top: 6px;
          padding-bottom: 6px;
          &.e-active {
            background: #f5f6f9;
            border-right: 2px solid #6386c1;
          }
        }
      }
    }
  }
}
</style>
