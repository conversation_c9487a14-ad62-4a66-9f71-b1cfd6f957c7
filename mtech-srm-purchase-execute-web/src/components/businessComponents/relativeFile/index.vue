<template>
  <div class="r-d-container mt-flex">
    <div class="tree-view--wrap" v-if="isTree">
      <!-- <div class="trew-node--add">
        <div class="node-title">{{ $t("层级") }}</div>
      </div> -->
      <mt-common-tree
        v-if="treeViewData.dataSource.length"
        ref="treeView"
        class="tree-view--template"
        :un-button="true"
        :fields="treeViewData"
        :selected-nodes="selectedNodes"
        :expanded-nodes="expandedNodes"
        @nodeSelected="nodeSelected"
      ></mt-common-tree>
    </div>
    <div class="table-container">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
        v-if="this.pageConfig[0].grid.columnData"
      >
      </mt-template-page>
    </div>

    <input
      type="file"
      ref="uploadIptRef"
      class="upload-input"
      style="visibility: hidden; width: 0"
      @change="chooseFiles"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { download } from '@/utils/utils'
import { rdToolBar, rdColumnData, itemOtherCol } from './config/relative.js'
import { API } from '@mtech-common/http'

export default {
  props: {
    // preUrl: "purchaseRequest", // 前缀
    // listUrl: "getFileNodeByDocId", // 获取左侧节点
    // fileUrl: `${BASE_TENANT}/requestFile/queryFileByDocId`, // 获取附件列表的，要完整的url
    // saveUrl: "saveHeaderFile" // 将附件文件url保存到列表
    requestUrlObj: {
      type: Object,
      default: () => {}
    },
    // 传入自定义的左侧菜单
    // [
    //   {
    //     id: "reconciliation_header" // 选中时传给api的值
    //     nodeName: this.$t("订单明细附件") // 侧边栏名称
    //     nodeCode: 0, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
    //     btnRequired: {}, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
    //     hasItem: false, // 是否显示附件行数据（可选）
    //     deleteFileUrl: "", // 删除文件使用的 API
    //     deleteFileRequestMethod: "put", // 设置删除api的请求方法，默认 put
    //   }
    // ]
    moduleFileList: {
      type: Array,
      default: () => []
    },
    // fileQueryParms: {}, // 查询文件列表请求参数，除了 docId、parentId 外的额外参数
    fileQueryParms: {
      type: Object,
      default: () => {}
    },
    docId: {
      type: String,
      default: ''
    },
    btnRequired: {
      // 是否可以进行某种操作：hasUpload: 、hasDownload、 hasDelete
      type: Object,
      default: () => {
        return {
          hasUpload: true,
          hasDownload: true,
          hasDelete: true
        }
      }
    },
    isTree: {
      type: Boolean,
      default: true
    },
    isView: {
      // 是只查看的状态，故没有上传和删除
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedNodes: [],
      selectedNodeName: '',
      expandedNodes: [],
      treeViewData: {
        //相关文件-目录结构-原数据
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxs">
                            <div>{{mtData.nodeName}}</div>
                          </div>`,
              data() {
                return { data: {} }
              },
              props: {
                mtData: {
                  // 拿到数据
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'id',
        text: 'nodeName'
        // child: "fileNodeResponseList", // 暂时不考虑子节点
      },
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: rdToolBar(this.btnRequired, this.isView),
          grid: {
            allowPaging: false,
            columnData: rdColumnData(this.btnRequired, this.isView),
            dataSource: []
          }
        }
      ],
      nodeCode: '', // 节点code， 整单：xx_header_file，明细：xx_item_file=====item的多几列
      hasModuleFileList: false // 传入了左侧边栏
    }
  },

  watch: {
    moduleFileList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.hasModuleFileList = true
          this.getFolderDataByModuleFile(this.moduleFileList)
        }
      },
      immediate: true
    },
    nodeCode(newVal) {
      this.handlerToolbarColumn(newVal)
    },
    isView: {
      handler() {
        this.handlerToolbarColumn(this.nodeCode)
      },
      immediate: true
    }
  },

  mounted() {
    if (
      this.requestUrlObj &&
      this.requestUrlObj.preUrl &&
      this.requestUrlObj.listUrl &&
      this.requestUrlObj.fileUrl
    ) {
      this.getFileFolderData()
    } else if (this.moduleFileList && this.moduleFileList.length > 0) {
      this.hasModuleFileList = true
      this.getFolderDataByModuleFile(this.moduleFileList)
    }
  },

  methods: {
    // 通过 nodeCode 设置 Toolbar 和 Column
    handlerToolbarColumn(newVal) {
      console.log('nodeCode有新值了', newVal)
      let _btnRequired = {} // 按钮显示配置
      let columnData = [] // 表格配置
      if (this.moduleFileList && this.moduleFileList.length > 0) {
        // 如果传入了左侧菜单，则通过菜单内容配置 ToolBar 和 ColumnData
        _btnRequired = this.btnRequired
        if (this.moduleFileList[newVal].btnRequired) {
          _btnRequired = this.moduleFileList[newVal].btnRequired // newVal => index
        }
        columnData = rdColumnData(_btnRequired, this.isView)

        if (this.moduleFileList[newVal].hasItem) {
          // 包含行信息 itemOtherCol
          columnData.concat(itemOtherCol)
        }
      } else if (newVal.includes('item')) {
        // 如果 nodeCode 包含 item，即为行附件
        _btnRequired = {
          hasUpload: false,
          hasDownload: true,
          hasDelete: false
        }
        // 行附件只能下载
        columnData = rdColumnData(_btnRequired, this.isView)
        // 包含行信息 itemOtherCol
        columnData.concat(itemOtherCol)
      } else {
        // 使用 btnRequired 的配置
        _btnRequired = this.btnRequired
        columnData = rdColumnData(_btnRequired, this.isView)
      }

      // 设置 toolbar
      this.$set(this.pageConfig[0], 'toolbar', rdToolBar(_btnRequired, this.isView))
      //  设置 columnData
      this.$set(this.pageConfig[0].grid, 'columnData', null)
      this.$nextTick(() => {
        this.$set(this.pageConfig[0].grid, 'columnData', columnData)
      })
    },
    // 获取左侧节点，如果有，默认获取第一个的文件列表
    getFileFolderData() {
      let _params = {
        docId: this.docId,
        ...this.fileQueryParms
      }
      this.$API[this.requestUrlObj.preUrl][this.requestUrlObj.listUrl](_params).then((res) => {
        this.$set(this.treeViewData, 'dataSource', res.data)
        if (Array.isArray(res.data) && res.data.length) {
          this.selectNodeValue = res.data[0]['id']
          this.nodeSelected({ nodeData: { id: this.selectNodeValue } })
          this.selectedNodes = [res.data[0]['id']]
          this.expandedNodes = [res.data[0]['id']]
          this.nodeCode = res.data[0].nodeCode
          this.selectedNodeName = res.data[0].nodeName
        }
      })
    },
    // 使用传入的 moduleFileList 作为左侧边栏，默认获取第一个的文件列表
    getFolderDataByModuleFile(moduleFileList) {
      this.$set(this.treeViewData, 'dataSource', moduleFileList)
      this.selectNodeValue = moduleFileList[0]['id']
      this.nodeSelected({ nodeData: { id: this.selectNodeValue } })
      this.selectedNodes = [moduleFileList[0]['id']]
      this.expandedNodes = [moduleFileList[0]['id']]
      this.nodeCode = moduleFileList[0].nodeCode
      this.handlerToolbarColumn(this.nodeCode)
      this.selectedNodeName = moduleFileList[0].nodeName
    },
    // 点击切换，获取文件列表
    nodeSelected(event) {
      // 选中的 侧边栏值 selectNodeValue 传递给 api 的名称
      let selectDataAPIKey = 'parentId'
      // 设置 路由对应的 侧边栏值 selectNodeValue 传递给 api 的名称
      const routeName_doctype = {
        routeNameList: [
          'reconciliation-confirm-detail', // 客户对账协同（供方）-对账单确认详情
          'invoice-collaboration-upload', // 发票协同（采方）-上传发票
          'reconciliation-upload-invoice', // 发票协同（供方）-上传发票
          'statement-detail' // 客户对账协同（采方）-对账单确认详情
        ],
        selectDataAPIKey: 'doctype'
      }
      const routeName_docType = {
        routeNameList: [
          'contact-reconciled-detail', // 往来对账单详情-采方
          'contact-detail-supplier' // 往来对账单详情-供方
        ],
        selectDataAPIKey: 'docType'
      }
      // 匹配 路由对应的 侧边栏值 selectNodeValue 传递给 api 的名称
      if (routeName_doctype.routeNameList.includes(this.$route.name)) {
        // 设置路由对应的 APIKey
        selectDataAPIKey = routeName_doctype.selectDataAPIKey
      } else if (routeName_docType.routeNameList.includes(this.$route.name)) {
        // 设置路由对应的 APIKey
        selectDataAPIKey = routeName_docType.selectDataAPIKey
      }

      if (event?.nodeData?.id) {
        // "reconciliation_header_sup"
        this.selectNodeValue = event?.nodeData?.id
        this.selectedNodeName = event?.nodeData?.text
        this.nodeCode = this.treeViewData.dataSource.find(
          (item) => item.id == this.selectNodeValue
        ).nodeCode
        this.$set(this.pageConfig[0].grid, 'asyncConfig', {
          url: this.requestUrlObj?.fileUrl,
          params: {
            docId: this.docId,
            [selectDataAPIKey]: this.selectNodeValue,
            ...this.fileQueryParms
          },
          methods: 'get',
          recordsPosition: 'data',
          serializeList: this.serializeListing
        })
      }
    },
    // 序列化时可通过 fileListData 拿表格的数据
    serializeListing(list) {
      this.$emit('fileListData', { tab: this.selectNodeValue, data: list })

      return list
    },
    handleClickToolBar(e) {
      console.log('use-handleClickToolBar', e)
      if (e.toolbar.id === 'upload') {
        this.$refs.uploadIptRef.click()
      } else if (e.toolbar.id == 'download') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          _selectRows.forEach((item) => {
            this.handlerDownloadPrivateFile(item)
          })
          // let _url = _selectRows[0]["remoteUrl"];
          // window.open(_url);
        }
      } else if (e.toolbar.id == 'delete') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          this.handleBatchDelete(_selectRows)
        }
      } else if (e.toolbar.id == 'print') {
        this.handlePrintRecociliation() // 打印对账单
      }
    },
    // 下载文件
    handlerDownloadPrivateFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPrivateFile({
          id: data.sysFileId
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: data.fileName, blob: res.data })
        })
    },

    // 点击上传文件
    chooseFiles(data) {
      console.log(this.$t('点击上传'), data, this.$parent)
      let _files = event.target.files
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件')
        })
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$store.commit('startLoading')
      this.$API.fileService.uploadPrivateFile(_data).then((res) => {
        this.$store.commit('endLoading')
        this.handleUploadFiles(res?.data)
      })
    },

    //执行上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, url, remoteUrl, sysName, createTime } = data
      const { username: createUserName } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      let _params = {
        docId: this.docId ? this.docId : id,
        parentId: !this.hasModuleFileList ? this.selectNodeValue : 0, // 传入左侧边栏时为 0
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        remoteUrl: remoteUrl,
        sysFileId: id,
        sysName: sysName,
        url: url,
        createUserName, // 创建人
        createTime // 创建时间
      }
      // 通过路由判断修改上传参数的 类型
      const type1 = [
        'custom-order-detail' // 客户售后订单（售后订单协同） -- 供方
      ]
      const type2 = [
        'reconciliation-confirm-detail', // 客户对账协同（供方）-对账单确认详情
        'invoice-collaboration-upload', // 发票协同（采方）-上传发票
        'statement-detail', // 客户对账协同（采方）-对账单确认详情
        'contact-reconciled-detail', // 往来对账单详情-采方
        'contact-detail-supplier' // 往来对账单详情-供方
      ]
      const type3 = [
        'sup-recon-detail-tv', // 确认对账单-泛智屏-供方
        'sup-invoice-detail' // 发票上传-泛智屏-外发发票明细-供方
      ]
      if (type1.includes(this.$route.name)) {
        _params.docType = 'as_so'
        _params.nodeType = 1
        _params.nodeCode = this.nodeCode
        _params.nodeName = this.selectedNodeName
      } else if (type2.includes(this.$route.name)) {
        _params.docType = this.selectNodeValue
      } else if (type3.includes(this.$route.name)) {
        _params.docType = this.fileQueryParms.docType
        _params.nodeCode = this.fileQueryParms.nodeCode
      }
      this.$API[this.requestUrlObj.preUrl][this.requestUrlObj.saveUrl](_params).then(() => {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDelete(_selectIds)
    },
    //删除文件
    handleDelete(ids) {
      let _params = {
        idList: ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$store.commit('startLoading')
          // 使用传入的删除接口
          if (this.moduleFileList[this.nodeCode].deleteFileUrl) {
            const url = this.moduleFileList[this.nodeCode].deleteFileUrl
            const params = {
              idList: _params.idList,
              docId: this.docId
            }
            const deleteFileRequestMethod =
              this.moduleFileList[this.nodeCode].deleteFileRequestMethod || 'put' // 默认请求方法 put
            API[deleteFileRequestMethod](url, params).then(() => {
              this.$store.commit('endLoading')
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          } else {
            this.$API.fileService.deleteFile(_params).then(() => {
              this.$store.commit('endLoading')
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        }
      })
    },
    //打印对账单
    handlePrintRecociliation() {
      this.$API.reconciliationCollaboration
        .postTransactionReconciliationPrint({ id: this.$route.query?.id })
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },
    //单元格按钮，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'download') {
        // window.open(e.data.url);
        this.handlerDownloadPrivateFile(e.data)
      } else if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
    },
    //单元格title文字点击，文件名点击预览
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle1', e)
      if (e.field == 'fileName') {
        let params = {
          id: e.data.sysFileId || e.data.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(
            `${res.data}`
            // `${res.data}/onlinePreview?url=${e.data.remoteUrl || e.data.url}`
          )
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.r-d-container {
  width: 100%;
  height: 100%;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .tree-view--wrap {
    min-width: 200px;
    background: #fff;
    box-shadow: inset -1px 0 0 0 rgba(232, 232, 232, 1);
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 50px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 20px;

      /deep/ .e-ul {
        .e-list-item {
          text-align: center;
          line-height: 1;
          font-size: 14px;
          color: #292929;
          padding-top: 6px;
          padding-bottom: 6px;
          &.e-active {
            background: #f5f6f9;
            border-right: 2px solid #6386c1;
          }
        }
      }
    }
  }
}
</style>
