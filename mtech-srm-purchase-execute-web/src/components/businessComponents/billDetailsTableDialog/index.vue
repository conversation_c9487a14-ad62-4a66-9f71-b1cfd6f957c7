<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog full-size-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div class="full-height vertical-flex-box">
      <mt-tabs
        class="flex-keep"
        tab-id="bill-details-table-dialog-tab"
        :e-tab="false"
        :selected-item="activeTab"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div class="flex-fit">
        <mt-template-page
          v-if="isChanged"
          ref="templateRef"
          :current-tab="currentTab"
          :template-config="componentConfig"
          @dataBound="handleDataBound"
          @rowSelected="handleRowSelected"
          @rowDeselected="handleRowDeselected"
          @handleClickToolBar="handleClickToolBar"
          :hidden-tabs="true"
        />
      </div>
      <!-- 底部数据 -->
      <div
        class="amount-total flex-keep"
        v-show="
          actionType === BillDetailsTableDialogActionType.edit &&
          currentTabInfo.code === TabCode.dataItem
        "
      >
        <div class="item">
          {{ $t('勾选总金额（含税）：') }}{{ theTaxedTotalPriceTotal }}
          {{ currencyName }}
        </div>
        <div class="item">
          {{ $t('勾选总金额（未税）：') }}{{ theUntaxedTotalPriceTotal }}
          {{ currencyName }}
        </div>
        <div class="item">
          {{ $t('勾选总税额：') }}{{ theTaxAmountTotal }}
          {{ currencyName }}
        </div>
      </div>
      <div
        class="amount-total flex-keep"
        v-show="
          actionType === BillDetailsTableDialogActionType.edit &&
          currentTabInfo.code === TabCode.highLowInfo
        "
      >
        <div class="item">
          {{ $t('勾选总金额（含税）：') }}{{ theHighLowInfoTaxed }}
          {{ currencyName }}
        </div>
        <div class="item">
          {{ $t('勾选总金额（未税）：') }}{{ theHighLowInfoUntaxed }}
          {{ currencyName }}
        </div>
        <div class="item">
          {{ $t('勾选总税额：') }}{{ theHighLowInfoTaxAmount }}
          {{ currencyName }}
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import {
  BillDetailsTableDialogActionType,
  TabCode,
  HighLowInfoColumnData,
  DataItemTabConfig,
  HighLowInfoTabConfig
} from './config/constant'
import { cloneDeep } from 'lodash'
import bigDecimal from 'js-big-decimal'

export default {
  data() {
    return {
      isChanged: true,
      dialogTitle: '',
      TabCode,
      tabList: [
        {
          title: this.$t('关联明细'),
          code: TabCode.dataItem
        },
        {
          title: this.$t('关联高低开信息'),
          code: TabCode.highLowInfo
        }
      ],
      currentTabInfo: {}, // 当前tab的数据
      currentTab: 0, // 当前列模板显示的 Tab
      activeTab: 0, // 当前高亮的 Tab
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      BillDetailsTableDialogActionType,
      actionType: BillDetailsTableDialogActionType.view, // 默认类型：查看
      currentDataItemTableData: [], // 当前明细行表格数据
      currentSelectDataItems: [], // 本次选择的明细行数据 用于校验
      currentHighLowInfoTableList: [], // 高低开表格数据
      selectDataItemRowsIdsList: [], // 选中的明细行 ID 列表
      selectHighLowInfoRowsIdsList: [], // 选中的高低开行 ID 列表
      theUntaxedTotalPriceTotal: 0, // 勾选明细行执行未税总金额
      theTaxedTotalPriceTotal: 0, // 勾选明细行执行含税总金额
      theTaxAmountTotal: 0, // 勾选明细行税额
      theHighLowInfoTaxed: 0, // 勾选高低开执行未税总金额
      theHighLowInfoUntaxed: 0, // 勾选高低开执行含税总金额
      theHighLowInfoTaxAmount: 0, // 勾选高低开税额
      currencyName: '', // 币种名称
      componentConfig: [
        {
          ...DataItemTabConfig
        },
        {
          ...HighLowInfoTabConfig
        }
      ]
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(args) {
      const entryInfo = cloneDeep(args)
      const {
        title,
        actionType,
        // currencyName,

        // 明细行
        dataItemAsyncConfig,
        dataItemColumnData,
        dataItemSelectRowsIds,
        dataItemData,
        // 高低开信息
        highLowInfoAsyncConfig,
        highLowInfoSelectRowsIds,
        highLowInfoData
      } = entryInfo
      this.actionType = actionType
      // this.currencyName = currencyName || ""; // 货币名称
      this.selectDataItemRowsIdsList = dataItemSelectRowsIds || []
      this.selectHighLowInfoRowsIdsList = highLowInfoSelectRowsIds || []
      if (dataItemColumnData?.length == 0) {
        this.$toast({
          content: this.$t('当前单据类型，明细行表头配置为空'),
          type: 'warning'
        })
        return
      }
      const dataItemAsync = {
        ...dataItemAsyncConfig,
        serializeList: this.dataItemSerializeList
      }
      const highLowInfoAsync = {
        ...highLowInfoAsyncConfig,
        serializeList: this.highLowInfoSerializeList
      }

      // 初始化列模板组件
      this.initTemplatePage({
        actionType,
        dataItemColumnData,
        dataItemAsyncConfig: dataItemAsync,
        highLowInfoAsyncConfig: highLowInfoAsync,
        dataItemData,
        highLowInfoData
      })
      this.dialogTitle = title // 弹框名称
      this.currentTabInfo = this.tabList[0] // 选中第一个Tab
      this.currentTab = 0 // 当前tab的数据 默认为 关联明细
      this.activeTab = 0 // 当前高亮的 Tab
      // 设置组件当前勾选行 默认显示 关联明细tab
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.selectIdRecords =
        this.selectDataItemRowsIdsList.map((item) => {
          return { id: item }
        })
      this.$refs.dialog.ejsRef.show()
    },
    initTemplatePage(args) {
      const {
        actionType,
        dataItemColumnData,
        dataItemAsyncConfig,
        highLowInfoAsyncConfig,
        dataItemData,
        highLowInfoData
      } = args
      this.componentConfig = []
      let lineSelection = false
      let lineIndex = 0
      let frozenColumns = false
      if (actionType === BillDetailsTableDialogActionType.view) {
        // 查看状态
        lineSelection = false
        lineIndex = 0
        frozenColumns = false
        this.buttons = [
          {
            click: this.handleClose,
            buttonModel: { isPrimary: true, content: this.$t('确定') }
          }
        ]
      } else if (actionType === BillDetailsTableDialogActionType.edit) {
        // 编辑状态
        lineSelection = 0
        lineIndex = 1
        frozenColumns = 1
        this.buttons = [
          {
            click: this.handleClose,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: true, content: this.$t('确定') }
          }
        ]
        // 设置勾选的数值
        const {
          reconciliationTaxedAmount, // 明细汇总含税金额
          reconciliationUntaxAmount, // 明细汇总未税金额
          reconciliationTaxAmount // 明细汇总税额
        } = dataItemData
        const {
          taxedHighLow, // 高低开汇总含税金额
          untaxedHighLow, // 高低开汇总未税金额
          highLowInfoTaxAmount // 高低开汇总税额
        } = highLowInfoData

        this.theUntaxedTotalPriceTotal = reconciliationUntaxAmount || 0 // 勾选明细行执行未税总金额
        this.theTaxedTotalPriceTotal = reconciliationTaxedAmount || 0 // 勾选明细行执行含税总金额
        this.theTaxAmountTotal = reconciliationTaxAmount || 0 // 勾选明细行税额
        this.theHighLowInfoTaxed = untaxedHighLow || 0 // 勾选高低开未税总金额
        this.theHighLowInfoUntaxed = taxedHighLow || 0 // 勾选高低开含税总金额
        this.theHighLowInfoTaxAmount = highLowInfoTaxAmount || 0 // 勾选高低开税额
      }
      this.isChanged = !this.isChanged
      this.$nextTick(() => {
        this.componentConfig = [
          {
            ...DataItemTabConfig,
            grid: {
              ...DataItemTabConfig.grid,
              lineSelection,
              lineIndex,
              frozenColumns,
              columnData: dataItemColumnData,
              asyncConfig: dataItemAsyncConfig
            }
          },
          {
            ...HighLowInfoTabConfig,
            grid: {
              ...HighLowInfoTabConfig.grid,
              lineSelection,
              lineIndex,
              frozenColumns,
              columnData: HighLowInfoColumnData,
              asyncConfig: highLowInfoAsyncConfig
            }
          }
        ]
        setTimeout(() => {
          this.isChanged = !this.isChanged
        }, 100)
      })
    },
    onOpen(args) {
      args.preventFocus = true
    },

    dataItemSerializeList(list) {
      this.currentDataItemTableData = list
      return list
    },
    highLowInfoSerializeList(list) {
      this.currentHighLowInfoTableList = list
      return list
    },
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id === 'HighLowInfoRefresh') {
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },

    confirm() {
      // 数据校验
      if (this.doValidate()) {
        this.$emit('confirm', {
          //  明细行
          itemIds: this.selectDataItemRowsIdsList,
          theUntaxedTotalPriceTotal: this.theUntaxedTotalPriceTotal, // 明细行未税
          theTaxedTotalPriceTotal: this.theTaxedTotalPriceTotal, // 明细行含税
          theTaxAmountTotal: this.theTaxAmountTotal, // 明细行税额
          // 高低开
          highLowIds: this.selectHighLowInfoRowsIdsList,
          theHighLowInfoTaxed: this.theHighLowInfoTaxed, // 高低开执行未税总金额
          theHighLowInfoUntaxed: this.theHighLowInfoUntaxed, // 高低开执行含税总金额
          theHighLowInfoTaxAmount: this.theHighLowInfoTaxAmount // 高低开税额
        })
        this.handleClose()
      }
    },
    // 表格数据绑定完成
    handleDataBound() {
      if (this.actionType === BillDetailsTableDialogActionType.edit) {
        // 编辑状态 当前tab是关联明细
        if (this.currentTabInfo.code === TabCode.dataItem) {
          // 数据绑定完成-回显勾选的 明细表格数据
          this.doSelectDataItemRowsByIds()
        } else {
          // 数据绑定完成-回显勾选的 高低开表格数据
          this.doSelectHighLowInfoRowsByIds()
        }
      }
    },
    // 数据绑定完成-回显勾选的 明细表格数据
    doSelectDataItemRowsByIds() {
      // 设置组件当前勾选行
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.selectIdRecords =
        this.selectDataItemRowsIdsList.map((item) => {
          return { id: item }
        })
    },
    // 数据绑定完成-回显勾选的 高低开表格数据
    doSelectHighLowInfoRowsByIds() {
      const selectRowIndexList = []

      this.selectHighLowInfoRowsIdsList.forEach((item) => {
        const rowIndex = this.currentHighLowInfoTableList.findIndex((row) => {
          return row.id === item
        })
        // 历史勾选的数据的行索引
        selectRowIndexList.push(rowIndex)
      })
      // 勾选选中行
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRows(selectRowIndexList)
    },
    // 取消勾选时，计算勾选的数据的含税总金额、未税总金额、税额
    handleRowDeselected(args) {
      const { data, isHeaderCheckboxClicked, isInteracted } = args
      if (!isInteracted) return
      if (this.currentTabInfo.code === TabCode.dataItem) {
        // 关联明细
        const selectIdRecords = this.$refs.templateRef.getCurrentUsefulRef().gridRef.selectIdRecords
        if (selectIdRecords.length === 0) {
          // 如果是点击了清空按钮，则清空选择的数据
          this.selectDataItemRowsIdsList = []
          this.currentSelectDataItems = []
          this.theUntaxedTotalPriceTotal = 0
          this.theTaxedTotalPriceTotal = 0
          this.theTaxAmountTotal = 0
        } else {
          const calculateData = []
          if (isHeaderCheckboxClicked) {
            // 当前页批量取消
            this.currentDataItemTableData.forEach((itemTableData) => {
              if (this.selectDataItemRowsIdsList.includes(itemTableData.id)) {
                // 1. 获取取消的数据，用于计算
                calculateData.push(itemTableData)
              }
              const deselectedIndex = this.selectDataItemRowsIdsList.findIndex(
                (itemRowId) => itemRowId === itemTableData.id
              )
              if (deselectedIndex >= 0) {
                // 2. 更新 关联明细 选择的id数据
                this.selectDataItemRowsIdsList.splice(deselectedIndex, 1)
              }
              const selectDataIndex = this.currentSelectDataItems.findIndex(
                (itemRowData) => itemRowData.id === itemTableData.id
              )
              if (selectDataIndex >= 0) {
                // 3. 更新 关联明细 本次选择的数据
                this.currentSelectDataItems.splice(selectDataIndex, 1)
              }
            })
            // 2. 更新 关联明细 选择的id数据
          } else if (!isHeaderCheckboxClicked && typeof data === 'object') {
            // 当前页单个取消
            // 1. 获取取消的数据，用于计算
            calculateData.push(data)
            const deselectedIndex = this.selectDataItemRowsIdsList.findIndex(
              (itemRowId) => itemRowId === data.id
            )
            if (deselectedIndex >= 0) {
              // 2. 更新 关联明细 选择的id数据
              this.selectDataItemRowsIdsList.splice(deselectedIndex, 1)
            }
            const selectDataIndex = this.currentSelectDataItems.findIndex(
              (itemRowData) => itemRowData.id === data.id
            )
            if (selectDataIndex >= 0) {
              // 3. 更新 关联明细 本次选择的数据
              this.currentSelectDataItems.splice(selectDataIndex, 1)
            }
          }
          // 计算 关联明细 勾选的金额 减
          this.calculateDataItemAmount({
            data: calculateData,
            method: 'subtract'
          })
        }
      } else if (this.currentTabInfo.code === TabCode.highLowInfo) {
        // 高低开
        const calculateData = []
        if (isHeaderCheckboxClicked) {
          // 当前页批量取消
          this.currentHighLowInfoTableList.forEach((itemTableData) => {
            if (this.selectHighLowInfoRowsIdsList.includes(itemTableData.id)) {
              // 1. 获取取消的数据，用于计算
              calculateData.push(itemTableData)
            }
            const deselectedIndex = this.selectHighLowInfoRowsIdsList.findIndex(
              (itemRowId) => itemRowId === itemTableData.id
            )
            if (deselectedIndex >= 0) {
              // 2. 更新 高低开 选择的id数据
              this.selectHighLowInfoRowsIdsList.splice(deselectedIndex, 1)
            }
          })
        } else if (!isHeaderCheckboxClicked && typeof data === 'object') {
          // 当前页单个取消
          // 1. 获取取消的数据，用于计算
          calculateData.push(data)
          const deselectedIndex = this.selectHighLowInfoRowsIdsList.findIndex(
            (itemRowId) => itemRowId === data.id
          )
          if (deselectedIndex >= 0) {
            // 2. 更新 高低开 选择的id数据
            this.selectHighLowInfoRowsIdsList.splice(deselectedIndex, 1)
          }
        }
        // 计算 高低开 勾选的金额 减
        this.calculateHighLowInfoAmount({
          data: calculateData,
          method: 'subtract'
        })
      }
    },
    // 勾选时，计算勾选的数据的含税总金额、未税总金额、税额
    handleRowSelected(args) {
      const { isHeaderCheckboxClicked, data, isInteracted } = args
      if (!isInteracted) return
      if (this.currentTabInfo.code === TabCode.dataItem) {
        const calculateData = []

        if (isHeaderCheckboxClicked) {
          // 当前页批量勾选
          this.currentDataItemTableData.forEach((itemTableData) => {
            if (itemTableData.id && !this.selectDataItemRowsIdsList.includes(itemTableData.id)) {
              // 1. 获取勾选的数据，用于计算
              calculateData.push(itemTableData)
              // 2. 更新 关联明细 选择的id数据
              this.selectDataItemRowsIdsList.push(itemTableData.id)
              // 3. 更新 关联明细 本次选择的数据
              this.currentSelectDataItems.push(itemTableData)
            }
          })
        } else if (!isHeaderCheckboxClicked && typeof data === 'object') {
          // 当前页单个勾选
          if (data.id) {
            // 1. 获取勾选的数据，用于计算
            calculateData.push(data)
            // 2. 更新 关联明细 选择的id数据
            this.selectDataItemRowsIdsList.push(data.id)
            // 3. 更新 关联明细 本次选择的数据
            this.currentSelectDataItems.push(data)
          }
        }
        // 计算 关联明细 勾选的金额 加
        this.calculateDataItemAmount({
          data: calculateData,
          method: 'add'
        })
      } else if (this.currentTabInfo.code === TabCode.highLowInfo) {
        // 高低开
        const calculateData = []

        if (isHeaderCheckboxClicked) {
          // 当前页批量勾选
          this.currentHighLowInfoTableList.forEach((itemTableData) => {
            if (itemTableData.id && !this.selectHighLowInfoRowsIdsList.includes(itemTableData.id)) {
              // 1. 获取勾选的数据，用于计算
              calculateData.push(itemTableData)
              // 2. 更新 高低开 选择的id数据
              this.selectHighLowInfoRowsIdsList.push(itemTableData.id)
            }
          })
        } else if (!isHeaderCheckboxClicked && typeof data === 'object') {
          // 当前页单个勾选
          if (data.id) {
            // 1. 获取勾选的数据，用于计算
            calculateData.push(data)
            // 2. 更新 高低开 选择的id数据
            this.selectHighLowInfoRowsIdsList.push(data.id)
          }
        }
        // 计算 高低开 勾选的金额 加
        this.calculateHighLowInfoAmount({
          data: calculateData,
          method: 'add'
        })
      }
    },
    // 计算 关联明细 勾选的金额
    calculateDataItemAmount(args) {
      const { data, method } = args
      if (method === 'add') {
        data.forEach((item) => {
          // 计算 勾选执行含税总金额
          this.theTaxedTotalPriceTotal =
            bigDecimal.add(this.theTaxedTotalPriceTotal, item.executeTaxedTotalPrice) || 0
          // 计算 勾选执行未税总金额
          this.theUntaxedTotalPriceTotal =
            bigDecimal.add(this.theUntaxedTotalPriceTotal, item.executeUntaxedTotalPrice) || 0
        })
      } else if (method === 'subtract') {
        data.forEach((item) => {
          // 计算 勾选执行含税总金额
          this.theTaxedTotalPriceTotal =
            bigDecimal.subtract(this.theTaxedTotalPriceTotal, item.executeTaxedTotalPrice) || 0
          // 计算 勾选执行未税总金额
          this.theUntaxedTotalPriceTotal =
            bigDecimal.subtract(this.theUntaxedTotalPriceTotal, item.executeUntaxedTotalPrice) || 0
        })
      }

      // 计算勾选的税额
      this.theTaxAmountTotal = bigDecimal.subtract(
        this.theTaxedTotalPriceTotal,
        this.theUntaxedTotalPriceTotal
      )
    },
    // 计算 高低开 勾选的金额
    calculateHighLowInfoAmount(args) {
      const { data, method } = args
      if (method === 'add') {
        data.forEach((item) => {
          // 计算 高低开执行含税总金额
          this.theHighLowInfoTaxed = bigDecimal.add(this.theHighLowInfoTaxed, item.taxPrice) || 0
          // 计算 高低开执行未税总金额
          this.theHighLowInfoUntaxed =
            bigDecimal.add(this.theHighLowInfoUntaxed, item.freePrice) || 0
        })
      } else if (method === 'subtract') {
        data.forEach((item) => {
          // 计算 高低开执行含税总金额
          this.theHighLowInfoTaxed =
            bigDecimal.subtract(this.theHighLowInfoTaxed, item.taxPrice) || 0
          // 计算 高低开执行未税总金额
          this.theHighLowInfoUntaxed =
            bigDecimal.subtract(this.theHighLowInfoUntaxed, item.freePrice) || 0
        })
      }

      // 计算勾选的税额
      this.theHighLowInfoTaxAmount = bigDecimal.subtract(
        this.theHighLowInfoTaxed,
        this.theHighLowInfoUntaxed
      )
    },
    // 校验数据 本次勾选的数据
    doValidate() {
      let isValid = true
      for (let i = 0; i < this.currentSelectDataItems.length; i++) {
        const currentItem = this.currentSelectDataItems[i]
        const nextItem = this.currentSelectDataItems[i + 1]
        if (nextItem && currentItem.taxCode !== nextItem.taxCode) {
          // taxCode	税率编码
          isValid = false
          this.$toast({
            content: this.$t('勾选的明细行数据，税率不一致'),
            type: 'warning'
          })
          break
        }
      }

      return isValid
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 点击 tab
    handleSelectTab(e) {
      // this.isChanged = !this.isChanged
      this.currentTabInfo = this.tabList[e]
      this.currentTab = e
      this.activeTab = e
      // setTimeout(() => {
      //   this.isChanged = !this.isChanged
      // }, 100)
    }
  }
}
</script>
<style lang="scss" scoped>
.amount-total {
  text-align: right;
  z-index: 1;

  .item {
    margin-bottom: 10px;
  }
}
// /deep/ .common-template-page .toolbar-container.mt-flex.invite-btn {
//   display: none;
// }
</style>
