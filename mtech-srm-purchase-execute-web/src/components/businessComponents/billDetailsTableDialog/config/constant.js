import { i18n } from '@/main.js'

// 对账单明细弹框 显示类型
export const BillDetailsTableDialogActionType = {
  view: '1', // 查看
  edit: '2' // 编辑
}

// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  dataItem: 'reconciliationField' // 对账明细
}

// 高低开表格
export const HighLowInfoColumnData = [
  {
    width: '150',
    field: 'type',
    headerText: i18n.t('类型'), // 0-补差；1-返利；2-其他
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('补差'),
        1: i18n.t('返利'),
        2: i18n.t('其他')
      }
    }
  },
  {
    width: '200',
    field: 'freePrice',
    headerText: i18n.t('未税金额')
  },
  {
    width: '200',
    field: 'taxPrice',
    headerText: i18n.t('含税金额')
  },
  {
    width: 'auto',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 对账明细表格
export const DataItemTabConfig = {
  tab: { title: i18n.t('对账明细') },
  useToolTemplate: false, // 不使用预置(新增、编辑、删除)
  useBaseConfig: true, // 使用组件中的toolbar配置
  toolbar: [],
  grid: {
    // allowPaging: false, // 不分页
    allowPaging: true, // 分页
    columnData: [],
    asyncConfig: {},
    lineSelection: 0,
    lineIndex: 1,
    dataSource: [],
    frozenColumns: 1
  }
}

// 高低开表格
export const HighLowInfoTabConfig = {
  tab: { title: i18n.t('高低开信息') },
  useToolTemplate: false, // 不使用预置(新增、编辑、删除)
  useBaseConfig: false, // 不使用组件中的toolbar配置
  toolbar: [
    [],
    [
      {
        id: 'HighLowInfoRefresh',
        icon: 'icon_solid_Refresh',
        title: i18n.t('刷新')
      }
    ]
  ],
  grid: {
    allowPaging: false, // 不分页
    columnData: HighLowInfoColumnData,
    asyncConfig: {},
    lineSelection: 0,
    lineIndex: 1,
    dataSource: [],
    frozenColumns: 1
  }
}
