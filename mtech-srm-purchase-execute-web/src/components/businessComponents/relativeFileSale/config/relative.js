import { i18n } from '@/main.js'
import Vue from 'vue'

export const btnRequired = {
  itemView: '01', // 行附件查看
  itemUpdate: '02', // 行附件上传同步到相关附件
  mainView: '03', // 查看整单附件，数据从接口获取
  mainUpdate: '04', // 上传整单附件，数据保存在本地
  mainUpdateEdit: '05', // 编辑已上传的整单附件，数据保存在本地
  mainViewData: '06', // 查看整单附件，已有数据源
  mainEditViewData: '07' // 既可以上传也可以编辑，走dataSource 涉及的场景有关联明细的
}
export const rdToolBar = (btnRequired) => [
  [
    {
      id: 'upload',
      icon: 'icon_solid_upload',
      title: i18n.t('上传'),
      visibleCondition: () => btnRequired.hasUpload
    },
    {
      id: 'download',
      icon: 'icon_solid_Download',
      title: i18n.t('下载'),
      visibleCondition: () => btnRequired.hasDownload
    },
    {
      id: 'delete',
      icon: 'icon_solid_Delete',
      title: i18n.t('删除'),
      visibleCondition: () => btnRequired.hasDelete
    }
  ]
]
const iconSetting = {
  '.ppt': 'mt-icon-icon_ppt',
  '.docx': 'mt-icon-icon_word',
  '.pdf': 'mt-icon-icon_pdf',
  '.xls': 'mt-icon-icon_excel',
  '.png': 'mt-icon-icon_File1'
}
export const rdColumnData = (btnRequired) => [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '200',
    field: 'fileName',
    cssClass: 'field-content',
    headerText: i18n.t('文件名称'),
    cellTools: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载'),
        visibleCondition: () => btnRequired.hasDownload
      }
      // {
      //   id: "delete",
      //   icon: "icon_solid_Delete",
      //   title: i18n.t("删除"),
      //   visibleCondition: () => btnRequired.hasDelete,
      // },
    ]
  },
  {
    // width: "150",
    field: 'fileSize',
    headerText: i18n.t('文件大小'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return `${Math.floor((e / 1024) * 100) / 100}KB`
      }
    }
  },
  {
    // width: "150",
    field: 'fileType',
    headerText: i18n.t('文件类型'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><i :class="['mt-icons', icon]"></i><span style="margin-left: 5px">{{data.fileType}}{{btnRequired.hasDelete}}</span></div>`,
          data() {
            return { data: { data: {} }, btnRequired }
          },
          computed: {
            icon() {
              const { fileType } = this.data
              return fileType ? iconSetting[fileType] : ''
            }
          }
        })
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div class="action-boxs">
    //               <template v-if="data.fileType == '.xls'">
    //                 <mt-icon name="Pdf_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.docx'">
    //                 <mt-icon name="CSV_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.jpg'">
    //                 <mt-icon name="Excel_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.png'">
    //                 <mt-icon name="MT_Daterange" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.pdf'">
    //                 <mt-icon name="Pdf_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //           </div>`,
    //     }),
    //   };
    // },
  },
  {
    // width: "150",
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    // width: "150",
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  }
]

export const itemOtherCol = [
  {
    field: 'itemNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'skuCode',
    headerText: i18n.t('sku编号')
  },
  {
    field: 'skuName',
    headerText: i18n.t('sku名称')
  },
  {
    field: 'spec',
    headerText: i18n.t('规格型号')
  }
]
