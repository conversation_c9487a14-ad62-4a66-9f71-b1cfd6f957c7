<template>
  <div class="purchase-order">
    <div class="actions-box">
      <check-tabs :tab-list.sync="tabList" :active-tab.sync="activeTab"></check-tabs>

      <div class="btns-wrap">
        <mt-button @click.native="saveModuleConfig">{{ $t('保存') }}</mt-button>
      </div>
    </div>

    <table-check
      :table-list="tableList"
      :current-tab-object="currentTabObject"
      @handleChangeCellCheckBox="handleChangeCellCheckBox"
    ></table-check>

    <!-- 配置左侧第三层Tab菜单，竖向：暂时没有 -->
    <left-nav-check
      :left-nav-list="leftNavList"
      :current-tab-object="currentTabObject"
    ></left-nav-check>

    <!-- 配置 相关文件 -->
    <file-config
      ref="treeFile"
      :file-config-list="fileConfigList"
      :tree-view-data="treeViewData"
      :config-id="configId"
      :file-cheked-nodes="fileChekedNodes"
      :current-tab-object="currentTabObject"
    ></file-config>
  </div>
</template>

<script>
import Vue from 'vue'
import { cloneDeep } from 'lodash'
// base 部分是公共的；formType=0，代表非自定义表单，1代表表单设计器
// 兼容：有多个表格配置，即baseDataSource得改变
// required 0-非必填；1-必填；2-系统生成
import { commonColumn, baseColumn, nowColumn } from './config'
export default {
  components: {
    checkTabs: require('../checkTabs.vue').default,
    tableCheck: require('../tableCheck.vue').default,
    leftNavCheck: require('../leftNavCheck.vue').default,
    fileConfig: require('../fileConfig.vue').default
  },
  props: {
    // 明细列的moduleType：采购申请-0，采购订单-6，供应商接收订单-9，，售后订单-11，供应商接收售后订单-as_so
    // docType：采购申请-pr，采购订单-po，供应商接收订单-so，售后订单-as_po，供应商接收售后订单-as_so
    docType: {
      type: String,
      default: ''
    },
    baseDocType: {
      type: String,
      default: ''
    },
    baseColumnName: {
      type: String,
      default: ''
    },
    columnName: {
      type: String,
      default: ''
    },
    baseModuleType: {
      // 参考列的在那个模块的明细列的moduleType，不同模块不一样（比如采购订单参考的是采购申请，那么这里就是采购申请中 需求明细 的moduleType）
      type: Number,
      default: -1
    },
    relativeFileModuleType: {
      // 相关附件的moduleType，不同的模块不一样
      type: Number,
      default: -1
    },
    // 点击保存时，需要移除的gridId
    removeMemoryGrids: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      configInfo: {},
      configId: '',
      tabList: [], // 表格  当前列的通用配置，非参考列
      originTabList: [], // 初始从当前列的通用配置，不会被改变
      activeTab: 0,
      columnData: [],
      // docType: "po", //supplier_bid",
      tableList: [],
      baseDataSource: [], // 参考的表格数据
      leftNavList: [], // 左侧二级菜单
      fileConfigList: [], // 相关附件的
      fileChekedNodes: [],
      treeViewData: {
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxss">
                            <div>{{mtData.nodeName}}</div>
                          </div>`,
              data() {
                return { data: {} }
              },
              props: {
                mtData: {
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'id',
        text: 'nodeName',
        child: 'moduleFileList'
      }
    }
  },
  computed: {
    currentTabObject() {
      return this.tabList[this.activeTab]
    }
  },
  watch: {
    docType() {}
  },
  mounted() {
    //业务类型基本信息，存储在localstorage，不同Tab下数据保持一致
    let sourceModuleConfigInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    console.log('sourceModuleConfigInfo', sourceModuleConfigInfo)
    this.configInfo.businessTypeId = sourceModuleConfigInfo.businessTypeId
    this.configInfo.businessTypeCode = sourceModuleConfigInfo.businessTypeCode
    // this.configId = sourceModuleConfigInfo.configId;

    if (this.baseColumnName) {
      this.columnData = commonColumn.concat(
        baseColumn(this.baseColumnName),
        nowColumn(this.columnName)
      )
    } else {
      this.columnData = commonColumn.concat(nowColumn(this.columnName))
    }
    console.log('columnData', this.columnData)
    if (this.baseColumnName) {
      this.getBaseModule()
    } else {
      this.getModule()
    }
  },
  methods: {
    // 获取依赖模块的通用配置，主要是获取'需求明细的表格配置'，用于渲染'表格参考列'
    getBaseModule() {
      this.$API.bgConfig.getBaseModules({ docType: this.baseDocType }).then((res) => {
        console.log(res)
        let { modules } = res.data
        modules.forEach((item) => {
          if (item.moduleType == this.baseModuleType) {
            // moduleType=0，代表需求明细（当前页面只有一个表格勾选时）
            this.baseDataSource = item.fields
          }
        })
        this.getBaseConfigById()
      })
    },

    getBaseConfigById() {
      this.$API.bgConfig
        .getBusinessConfigDetail({
          businessTypeCode: this.configInfo.businessTypeCode,
          docType: this.baseDocType
        })
        .then((res) => {
          let modules = res?.data?.modules
          // 如果有配置
          if (Array.isArray(modules) && modules.length) {
            modules.forEach((item) => {
              if (item.moduleType == this.baseModuleType) {
                // 只看需求明细的
                let _baseFields = JSON.parse(JSON.stringify(this.baseDataSource)) || []
                let _fields = item.fields
                _baseFields.forEach((f) => {
                  // 找总列和配置列的共同列。如果该列配置过，以配置优先。否则就取原本模板中的值
                  let _finds = _fields.find((b) => b.fieldKey == f.fieldKey)
                  if (_finds) {
                    f.baseDemand = true
                    f.baseRequired = f.baseRequired == 2 ? f.required : _finds.required // 如果base里设置了这一列是系统配置，就以那个为准
                    f.baseFixed = _finds.fixed > 0
                  } else {
                    f.baseDemand = false
                    f.baseRequired = f.required
                    f.baseFixed = f.fixed > 0
                  }
                })
                this.baseDataSource = _baseFields
                // console.log("baseColumnName,,,,,有参考列，下面调自己的");
                this.getModule()
              }
            })
          } else {
            this.setBaseUseModules()
          }
        })
        .catch(() => {
          this.setBaseUseModules()
        })
    },

    // 没有配置过时，以基础模块为准
    setBaseUseModules() {
      console.log('setBaseUseModules')
      let _basic = JSON.parse(JSON.stringify(this.baseDataSource))
      _basic.forEach((f) => {
        f.baseDemand = false
        f.baseRequired = f.required
        f.baseFixed = f.fixed > 0
        f.demand = false //数据重置
        f.required = 0 //数据重置
        f.fixed = false //数据重置
      })
      this.baseDataSource = _basic
      this.getModule()
    },

    //重新设置配置数据，主要是更新version
    resetModuleConfigInfo(res) {
      let _version = res?.data?.version
      if (_version) {
        this.$set(this.configInfo, 'version', _version)
        localStorage.sourceModuleConfigInfo = JSON.stringify(this.configInfo)
      }
      this.configId = res?.data?.configId
    },

    // 获取当前页面的通用配置
    getModule() {
      this.$API.bgConfig.getBaseModules({ docType: this.docType }).then((res) => {
        // console.log(res);
        let modules = res?.data?.modules
        // console.log("getModule接口", modules);
        if (!modules) return
        this.tabList = modules.map((item) => {
          return {
            ...item,
            checked: item.fixed > 0,
            modules: item
          }
        })
        this.originTabList = cloneDeep(this.tabList)
        let _leftNavList = []
        let _fileConfigList = []
        modules.forEach((item) => {
          //构建-文件配置对应的参数
          if (
            item.moduleType === this.relativeFileModuleType &&
            item.moduleName === this.$t('相关附件')
          ) {
            _fileConfigList.push({
              text: item.moduleName,
              moduleKey: item.moduleKey,
              moduleId: item.moduleId,
              containField: item.containField,
              subModuleItems: item.subModuleItems,
              modules: item,
              fixed: item.fixed,
              fields: item.fields,
              checked: item.fixed > 0
            })
          }
          // 如果 左侧第三层Tab菜单
          if (item.subModuleItems && item.subModuleItems.length) {
            let _subItems = item.subModuleItems
            let _navItem = []
            _subItems.forEach((sub) => {
              _navItem.push({
                parentModuleKey: item.moduleKey,
                parentModuleId: item.moduleId,
                text: sub.moduleName,
                modules: sub,
                moduleKey: sub.moduleKey,
                moduleId: sub.moduleId,
                checked: item.fixed > 0
              })
            })
            _leftNavList.push({
              moduleKey: item.moduleKey,
              moduleId: item.moduleId,
              navList: _navItem
            })
          }
        })

        this.leftNavList = _leftNavList
        this.fileConfigList = _fileConfigList //文件配置
        // console.log("leftNavList", this.leftNavList);
        // console.log("fileConfigList", this.fileConfigList);
        this.getConfigById()
      })
    },

    // 获取当前页面的配置，要判断有无配置过
    getConfigById() {
      this.$API.bgConfig
        .getBusinessConfigDetail({
          docType: this.docType,
          businessTypeCode: this.configInfo.businessTypeCode
        })
        .then((res) => {
          this.resetModuleConfigInfo(res)
          // console.log(res);
          let modules = res?.data?.modules
          // 获取文件列表
          let moduleFileList = res?.data?.moduleFileList
          if (this.configId && Array.isArray(moduleFileList) && moduleFileList.length) {
            moduleFileList?.forEach((i) => {
              i.required = i.required ? true : false
            })
            //存在configId 并且moduleFileList存在数据，按照moduleFileList渲染数据
            this.$set(this.treeViewData, 'dataSource', moduleFileList)
            this.$set(this.treeViewData, 'isChecked', 'required')
            // console.log("treeViewData", this.treeViewData);
            this.serializeFileCheckedNodes()
          }
          let _tableList = []
          if (Array.isArray(modules) && modules.length) {
            // console.log(this.$t("我是当前列中配置过的"), cloneDeep(this.tabList), cloneDeep(modules));
            this.tabList?.forEach((tab, tabIndex) => {
              modules?.forEach((module) => {
                // console.log('tabList和modules的遍历', tab, module)
                if (tab.moduleKey == module.moduleKey) {
                  //存在moduleKey，对应的Tab设置勾选
                  // tab.checked = true;
                  // console.log('对比到了', tab, tabIndex)
                  this.$set(this.tabList[tabIndex], 'checked', true)
                  if (module.containField > 0) {
                    // 存在表格数据，处理表格数据
                    let _baseFields = tab.fields,
                      _fields = module.fields || []
                    // console.log("对比base和detail", _baseFields, _fields);
                    _baseFields?.forEach((b) => {
                      let _finds = _fields.find((f) => b.fieldKey == f.fieldKey)
                      // console.log("_finds", _finds);
                      if (_finds) {
                        b.demand = true //  //在基础数据中，匹配到，设置为已勾选
                        b.required = b.required == 2 ? b.required : _finds.required // 如果base里设置了这一列是系统配置，就以那个为准
                        b.fixed = _finds.fixed > 0
                        b.sortValue = _finds.sortValue || b.sortValue // 设置排序，修改base的排序，如果配置过，以detail中的为准
                      } else {
                        b.demand = false
                        // b.required = b.required > 0;
                        b.fixed = b.fixed > 0
                      }
                    })

                    // console.log("_baseFields 复制过了", _baseFields);

                    // 融合列（参考列没有的话，默认用他自己）
                    let _mergeFields = _baseFields
                    if (this.baseColumnName) {
                      _mergeFields = this.mergeBasicTableDataSource(_baseFields)
                    }
                    // _mergeFields = this.serializeFieldsGroup(_mergeFields);
                    // console.log("_mergeFields--------------", _mergeFields);
                    _tableList.push({
                      title: module.moduleName,
                      moduleKey: module.moduleKey,
                      moduleId: module.moduleId,
                      grid: {
                        dataSource: _mergeFields,
                        columnData: this.columnData
                      }
                    })
                  }
                }
              })
            })
            // 因为配置过detail，就用sortValue排序
            _tableList[0].grid.dataSource.sort(this.sortData)
            console.log('sortData', _tableList)
            this.tableList = _tableList
          } else {
            // console.log("我是当前列没配置过的==");
            this.tableList = this.setUseModules()
          }
          // console.log(" this.tableList", this.tableList);
        })
        .catch(() => {
          this.tableList = this.setUseModules()
        })
    },

    // 处理tableList：当前页只有module，没有配置过时
    setUseModules() {
      // console.log("setUseModules", this.tabList);
      let _tableList = []
      this.tabList.forEach((tab) => {
        if (tab.containField > 0) {
          let _baseFields = []
          tab.fields.forEach((f) => {
            _baseFields.push({
              ...f,
              demand: false
            })
          })

          // 融合参考列
          // console.log("_baseFields", _baseFields);
          let _mergeFields = _baseFields
          if (this.baseColumnName) {
            _mergeFields = this.mergeBasicTableDataSource(_baseFields)
          }
          // _mergeFields = this.serializeFieldsGroup(_mergeFields);
          // console.log("_mergeFields", _mergeFields);
          _tableList.push({
            title: tab.moduleName,
            moduleKey: tab.moduleKey,
            moduleId: tab.moduleId,
            grid: {
              dataSource: _mergeFields,
              columnData: this.columnData
            }
          })
        }
      })
      return _tableList
    },

    // 排序 由小到大
    sortData(a, b) {
      return a.sortValue - b.sortValue
    },

    // 合并参考列字段+当前页列字段
    // _fields：当前列， isUnion：为true就是取并集的逻辑
    mergeBasicTableDataSource(_fields, isUnion) {
      let _baseFields = JSON.parse(JSON.stringify(this.baseDataSource))
      let res = []
      if (!isUnion) {
        // 当前逻辑：以当前列为准，如果参考列没有，就显示无此字段
        _fields?.forEach((f) => {
          let _find = _baseFields.find((b) => b.fieldKey == f.fieldKey)
          if (!_find) {
            f.baseNoCol = true // 参考列没有此字段
          } else {
            f = {
              ..._find,
              ...f,
              baseNoCol: false
            }
          }
          res.push(f)
          // console.log(this.$t("合并列"), f);
        })
      } else {
        // 原逻辑：取参考列和当前列取并集
        _baseFields.forEach((b) => {
          _fields?.forEach((f) => {
            if (b.fieldKey == f.fieldKey) {
              res.push({
                ...b,
                demand: f.demand,
                required: f.required,
                fixed: f.fixed
              })
            }
          })
        })
      }

      return res
    },

    // 废弃：不用分类了  整理合并单元格
    serializeFieldsGroup(fields) {
      // console.log("serializeFieldsGroup--", fields);
      let _fieldObj = {},
        dataSource = []
      for (let i = 0; i < fields.length; i++) {
        let _groupName = fields[i].fieldGroup
        if (Object.prototype.hasOwnProperty.call(_fieldObj, _groupName)) {
          _fieldObj[_groupName].groupCount++
          _fieldObj[_groupName].children.push(fields[i])
        } else {
          _fieldObj[_groupName] = {
            groupCount: 1,
            children: [fields[i]]
          }
        }
      }
      for (let i in _fieldObj) {
        // console.log(i, _fieldObj[i]);
        _fieldObj[i].children[0].isFirstGroupField = true
        _fieldObj[i].children[0].groupCount = _fieldObj[i].groupCount
        dataSource = dataSource.concat(_fieldObj[i]?.children)
      }
      return dataSource
    },

    // 切换checkbox
    handleChangeCellCheckBox(e, f) {
      this.$set(this.tableList[e]['grid']['dataSource'][f.index], f.key, f.value)
      console.log(e, f, 'this.tableList', this.tableList)
    },

    getSaveFileConfig() {
      if (!this.$refs.treeFile.$refs.treeView) return []
      const treeView = this.$refs.treeFile.$refs.treeView[0]
      const instance = treeView?.getCommonMethods()
      let _nodes = instance.getAllCheckedNodes()
      let _records = [...this.treeViewData.dataSource]
      this.updateTreeViewNodes(_records, _nodes)
      return _records
    },
    updateTreeViewNodes(_list, nodes) {
      _list.forEach((e) => {
        e.required = 0
        if (nodes.indexOf(e.id) > -1) {
          e.required = 1
        }
        if (Array.isArray(e?.moduleFileList) && e.moduleFileList.length) {
          this.updateTreeViewNodes(e.moduleFileList, nodes)
        }
      })
    },
    serializeFileCheckedNodes() {
      let _records = [...this.treeViewData?.dataSource]
      this.fileChekedNodes = this.getTreeViewNodes(_records)
      // console.log("初始化文件节点：", this.fileChekedNodes);
    },
    getTreeViewNodes(_list) {
      let _res = []
      _list.forEach((e) => {
        if (e.required > 0) {
          _res.push(e.id)
        }
        if (Array.isArray(e?.moduleFileList) && e.moduleFileList.length) {
          _res = _res.concat(this.getTreeViewNodes(e.moduleFileList))
        }
      })
      return _res
    },

    // 保存
    saveModuleConfig() {
      console.log('saveModuleConfig', this.tabList)
      this.$store.commit('startLoading')
      let _moduleItems = []
      this.tabList.forEach((t) => {
        if (t.checked) {
          _moduleItems.push(t.modules)
        }
      })
      // this.leftNavList.forEach((l) => {
      //   let _navList = l.navList;
      //   _navList.forEach((n) => {
      //     if (n.checked) {
      //       _moduleItems.push(n.modules);
      //     }
      //   });
      // });
      _moduleItems.forEach((r) => {
        if (r.containField > 0) {
          let _table = this.tableList.find((e) => {
            return e.moduleKey === r.moduleKey
          })
          // console.log(this.$t("找到了"), _table);
          // 过滤，传勾选的数据，以及序号更改的数据
          let allRecords = _table?.grid?.dataSource || []
          let _filterRecords = []
          // originFields = this.originTabList[i].fields; // 初始的带表格的tab的fields
          allRecords.forEach((item) => {
            if (item.demand || item.required == 2) {
              _filterRecords.push(item)
            }
            // 逻辑 废弃：序号不同时，也传给后台
            // else {
            //   // 如果找到了,如果sortValue不同，就传进去
            //   let _find = originFields.find((f) => f.fieldKey == item.fieldKey);
            //   if (_find.sortValue != item.sortValue) {
            //     _filterRecords.push(item);
            //   }
            // }
          })
          // // 改成，全部传。因为添加了序号
          // let _filterRecords = _table?.grid?.dataSource || [];
          _filterRecords.forEach((_record) => {
            delete _record.baseDemand
            delete _record.baseRequired
            delete _record.baseFixed
            _record.fixed = _record.fixed ? 1 : 0
            _record.required = _record.required == 2 ? 2 : _record.required ? 1 : 0 // 如果是系统生成就是2，否则使用checkbox的true/false来判断：true-1,false-0
          })
          r.fields = _filterRecords
        }
      })
      let { businessTypeId, businessTypeCode } = this.configInfo
      let _save = {
        docType: this.docType,
        businessTypeId,
        businessTypeCode,
        moduleItems: _moduleItems,
        moduleFileList: this.getSaveFileConfig()
      }
      if (this.configInfo?.version) {
        _save.version = this.configInfo.version
      }
      console.log('save-params---', _save)
      // return;
      this.$API.bgConfig
        .saveBusinessConfig(_save)
        .then((res) => {
          this.$store.commit('endLoading')
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.resetModuleConfigInfo(res)
          //保存完毕，重新获取数据
          if (this.baseColumnName) {
            this.getBaseModule()
          } else {
            this.getModule()
          }

          // 移除表格记忆gridId
          this.removeGridIdMemory()
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },

    // 移除表格记忆gridId
    removeGridIdMemory() {
      this.$API.baseRequest.removeMemory({ gridIds: this.removeMemoryGrids })
    }
  }
}
</script>

<style lang="scss" scoped>
.purchase-order .actions-box {
  width: 100%;
  display: flex;
  background: #fafafa;
  position: absolute;
  z-index: 1;
  border-bottom: 1px solid #e0e0e0;

  /deep/ .config-custom-tabs {
    flex: 1;
  }

  .btns-wrap {
    display: flex;
    /deep/ .mt-button {
      button {
        width: 76px;
        height: 34px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        box-shadow: unset;
        padding: 0;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        margin-top: 8px;
        margin-right: 10px;
      }
    }
  }
}
</style>
