import Vue from 'vue'
import { i18n } from '@/main.js'
export const commonColumn = [
  // {
  //   width: "150",
  //   field: "fieldGroup",
  //   headerText: i18n.t("字段分类"),
  // },
  {
    width: '150',
    field: 'fieldName',
    headerText: i18n.t('字段名')
    // valueAccessor: (field, data, column) => {
    //   return data.fieldName + " - " + data.fieldCode;
    // },
  },
  {
    width: '100',
    field: 'sortValue',
    headerText: i18n.t('序号'),
    template: function () {
      return {
        template: Vue.component('sortIpt', {
          template: `<div class="sortIptNumber"><mt-inputNumber v-model="data.sortValue" :disabled="disableSort" @input="handleChangeSort"></mt-inputNumber></div>`,
          data() {
            return {
              disableSort: true,
              a: '11'
            }
          },
          mounted() {
            // 根据勾选 当前列是否需要，来判断是否能修改序号。
            // 1. 默认根据行数据来
            if (this.data.demand || this.data.required == 2) {
              this.disableSort = false // 如果需要，就不禁用
            }
            // 2. 如果勾选有变化
            this.$bus.$on('changeNowDemand', (e) => {
              if (e.index == this.data.index) {
                if (e.value) {
                  this.disableSort = false
                } else {
                  this.disableSort = true
                }
              }
            })
          },
          methods: {
            handleChangeSort() {
              this.$parent.$parent.$emit('handleChangeCellCheckBox', {
                index: this.data.index,
                key: 'sortValue',
                value: this.data.sortValue
              })
            }
          }
        })
      }
    }
  }
]

export const baseColumn = (baseColumnName) => {
  return [
    {
      width: '300',
      field: '',
      headerText: baseColumnName, // i18n.t("采购申请"),
      columns: [
        {
          width: '150',
          field: 'baseDemand',
          headerText: i18n.t('是否需要'),
          template: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div class="action-boxs">
                  <span v-if="data.baseNoCol">{{ $t('无此字段')}}</span>
                  <span v-else-if="data.baseRequired == 2">{{ $t('无需配置')}}</span>
                  <mt-icon name="icon_V" class="checked" v-else-if="data.baseDemand" src=""></mt-icon>
                  <mt-icon name="icon_X" class="uncheck" v-else></mt-icon>
                </div>`,
                data() {
                  return { data: { i18n } }
                }
              })
            }
          }
        },
        {
          width: '150',
          field: 'baseRequired',
          headerText: i18n.t('是否必填'),
          template: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div class="action-boxs">
                  <span v-if="data.baseNoCol">{{ $t('无此字段')}}</span>
                  <span v-else-if="data.baseRequired == 2">{{ $t('无需配置')}}</span>
                  <mt-icon name="icon_V" class="checked" v-else-if="data.baseRequired" src=""></mt-icon>
                  <mt-icon name="icon_X" class="uncheck" v-else></mt-icon>
                </div>`,
                data() {
                  return { data: { i18n } }
                }
              })
            }
          }
        }
      ]
    }
  ]
}

export const nowColumn = (nowColumnName) => {
  return [
    {
      width: '300',
      field: '',
      headerText: nowColumnName, // i18n.t("采购订单"),
      columns: [
        {
          width: '150',
          field: 'demand',
          headerText: i18n.t('是否需要'),
          template: function () {
            return {
              template: Vue.component('bindCheck', {
                // template: `<mt-checkbox class="checkbox-item"  v-model="data.demand ? true: false"  @change="handleChange" ></mt-checkbox>`,
                template: `<div class="action-boxs">
                  <span v-if="data.required == 2">{{ $t('无需配置')}}</span>
                  <mt-checkbox v-else class="checkbox-item"  v-model="data.demand ? true: false"  @change="handleChange" ></mt-checkbox>
                </div>`,
                data() {
                  return { i18n }
                },
                methods: {
                  handleChange(e) {
                    // console.log( $t("切换了"), e, this.data);
                    this.data.demand = e.checked
                    this.$parent.$parent.$emit('handleChangeCellCheckBox', {
                      index: this.data.index,
                      key: 'demand',
                      value: e.checked
                    })
                    this.$bus.$emit('changeNowDemand', {
                      index: this.data.index,
                      key: 'demand',
                      value: e.checked
                    })
                  }
                }
              })
            }
          }
        },
        {
          width: '150',
          field: 'required',
          headerText: i18n.t('是否必填'),
          template: function () {
            return {
              template: Vue.component('bindCheck', {
                // template: `<mt-checkbox class="checkbox-item"  v-model="data.required ? true: false"  @change="handleChange" ></mt-checkbox>`,
                template: `<div class="action-boxs">
                  <span v-if="data.required == 2">{{ $t('无需配置')}}</span>
                  <mt-checkbox v-else class="checkbox-item"  v-model="data.required ? true: false"  @change="handleChange" ></mt-checkbox>
                </div>`,
                data() {
                  return {
                    i18n
                  }
                },
                methods: {
                  handleChange(e) {
                    // console.log(i18n.t("切换了"), e, this.data);
                    this.data.required = e.checked
                    this.$parent.$emit('handleChangeCellCheckBox', {
                      index: this.data.index,
                      key: 'required',
                      value: e.checked
                    })
                  }
                }
              })
            }
          }
        }
      ]
    }
  ]
}

export const columnData = [
  {
    width: '150',
    field: 'fieldGroup',
    headerText: i18n.t('字段分类')
  },
  {
    width: '150',
    field: 'fieldName',
    headerText: i18n.t('字段名')
  },
  {
    width: '300',
    field: '',
    headerText: i18n.t('采购申请'),
    columns: [
      {
        width: '150',
        field: 'baseDemand',
        headerText: i18n.t('是否需要'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.baseDemand" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
              data() {
                return { data: {} }
              }
            })
          }
        }
      },
      {
        width: '150',
        field: 'baseRequired',
        headerText: i18n.t('是否必填'),
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.baseRequired" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
              data() {
                return { data: {} }
              }
            })
          }
        }
      }
    ]
  }
]
