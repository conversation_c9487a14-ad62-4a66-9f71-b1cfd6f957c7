<template>
  <mt-tabs
    class="config-custom-tabs"
    tab-id="rfx-steps-config-tab"
    :e-tab="false"
    :data-source="tabList"
    :selected-item="activeTab"
    @handleSelectTab="selectTab"
    :tabs-solt="true"
  >
    <!-- <template #templateContent="{ props }">
      <div :class="['item-content', { fixed: props.fixed }]">
        <mt-icon
          @click.native="handleClickTabIcon(props)"
          class="config-checkbox"
          :name="
            props.checked
              ? 'a-icon_MultipleChoice_on'
              : 'a-icon_MultipleChoice_off'
          "
        />
        <span @click="handleClickTabTitle(props)">{{ props.moduleName }}</span>
      </div>
    </template> -->
    <template v-slot:default="item">
      <div :class="['item-content', { fixed: item.fixed }]">
        <mt-icon
          @click.native="handleClickTabIcon(item)"
          class="config-checkbox"
          :name="item.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
        />
        <span @click="handleClickTabTitle(item)">{{ item.moduleName }}</span>
      </div>
    </template>
  </mt-tabs>
</template>

<script>
export default {
  props: {
    tabList: {
      type: Array,
      default: () => []
    },
    activeTab: {
      type: Number,
      default: 0
    }
  },
  methods: {
    selectTab(index) {
      this.$emit('update:activeTab', index)
    },
    handleClickTabIcon(e) {
      console.log('handleClickTabIcon', e)
      let _fixed = e.fixed
      if (_fixed > 0) {
        this.$toast({
          content: `'${e.moduleName}'${this.$t('为固定项')}.`,
          type: 'warning'
        })
        return
      } else {
        // e.checked = !e.checked;
        let _index = this.tabList.findIndex((i) => i.moduleKey == e.moduleKey)
        let _tabList = this.tabList
        _tabList[_index].checked = !_tabList[_index].checked
        this.$emit('update:tabList', _tabList)
      }
    },
    handleClickTabTitle(props) {
      console.log('handleClickTabTitle', props)
      this.$emit('handleClickTabTitle', props)
    }
  }
}
</script>

<style lang="scss" scoped>
.config-custom-tabs {
  background: #fafafa;
  padding: 0;
  /deep/.e-tab-header {
    background: transparent;
  }
  .tab-wrap {
    padding: 0;
  }
  /deep/ ul.tab-container {
    display: flex;
    li.tab-item {
      flex-shrink: 0;
      color: #292929;
      font-size: 14px;
      font-weight: 400;
      height: 46px;
      line-height: 46px;
      min-width: 60px;
      position: relative;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 40px;

      .item-content {
        cursor: pointer;
        padding: 0;
        display: flex;
        position: relative;
        min-width: 60px;
        color: #4f5b6d;
        align-items: center;
        justify-content: center;
        .mt-icons {
          position: relative;
          top: -1px;
          margin-right: 6px;
        }
        .config-checkbox {
          &.mt-icon-a-icon_MultipleChoice_on {
            color: #6386c1;
          }
          &.mt-icon-a-icon_MultipleChoice_off {
            color: #9daabf;
          }
        }
        &.fixed {
          .config-checkbox {
            &.mt-icon-a-icon_MultipleChoice_on {
              color: #9a9a9a;
            }
          }
        }
      }

      &.active,
      &:hover {
        border-color: transparent;
        background: transparent;
      }

      &.active {
        color: #00469c;
        font-weight: 600;

        &:after {
          content: '';
          border: 1px solid #00469c;
          width: 60%;
          animation: active-tab 0.3s ease;
          position: absolute;
          bottom: 6px;
          left: 20%;
        }
        @keyframes active-tab {
          0% {
            width: 0;
            left: 50%;
          }
          100% {
            width: 60%;
            left: 20%;
          }
        }
      }
    }
  }
}
</style>
