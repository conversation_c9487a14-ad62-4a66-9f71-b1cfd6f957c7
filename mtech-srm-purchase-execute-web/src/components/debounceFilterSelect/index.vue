<template>
  <mt-select
    ref="filterSelectRef"
    @change="changeEmit"
    @focus="focusEmit"
    :id="id"
    :class="[isActive === true ? 'mtSelect' : '']"
    :value="privateValue"
    :allow-filtering="true"
    :filtering="doGetDataSource"
    :data-source="dataSource"
    filter-type="Contains"
    :float-label-type="floatLabelType"
    :placeholder="placeholder"
    :show-clear-button="showClearButton"
    :disabled="disabled"
    :fields="fields"
    :value-template="valueTemplate"
    :open-dispatch-change="openDispatchChange"
    :popup-width="popupWidth"
  ></mt-select>
</template>

<script>
import Vue from 'vue'
import { utils } from '@mtech-common/utils'
const valueVueTemplate = Vue.component('valueVueTemplate', {
  template: `
    <div class="user-value-template mt-flex-direction-column">
      <div>{{data.name||data.employeeName}}</div>
    </div>`,
  data() {
    return { data: {} }
  }
})

export default {
  data() {
    return {
      privateValue: null
    }
  },
  computed: {
    // 通过计算属性解决request冲突问题
    doGetDataSource() {
      return utils.debounce(this.request, 1000)
    }
  },
  mounted() {},
  props: {
    // debounce 执行的请求函数
    isActive: {
      type: Boolean,
      default: true
    },
    request: {
      type: Function,
      default: () => {
        return () => {}
      }
    },
    openDispatchChange: {
      type: Boolean,
      default: true
    },
    filterType: {
      // eslint-disable-next-line
      type: String | Number | Boolean,
      default: () => {
        return null
      }
    },
    // 值
    value: {
      // eslint-disable-next-line
      type: String | Number | Boolean,
      default: () => {
        return null
      }
    },
    valueTemplate: {
      type: Function,
      default: () => {
        return { template: valueVueTemplate }
      }
    },
    // 占位符
    placeholder: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 指定是显示还是隐藏文本框中的清除图标
    showClearButton: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // placeholder的浮动行为
    floatLabelType: {
      type: String,
      default: () => {
        return 'Never'
      }
    },
    // 字段配置
    fields: {
      type: Object,
      default: () => {
        return {
          // 映射列表中每个列表项的文本
          text: 'text',
          // 映射列表中每个列表项的value
          value: 'value',
          // 映射列表中每个列表项的icon
          iconCss: 'iconCss',
          // 映射列表中分组
          groupBy: 'groupBy'
        }
      }
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    id: {
      type: String,
      default: ''
    },
    popupWidth: {
      type: String,
      default: '100%'
    }
  },
  watch: {
    value: {
      handler(value) {
        this.privateValue = value
      },
      immediate: true
    }
  },
  methods: {
    changeEmit(e) {
      this.privateValue = e.value
      this.$emit('input', this.privateValue)
      this.$emit('change', e)
    },
    focusEmit(e) {
      this.$emit('focus', e)
    }
  }
}
</script>
