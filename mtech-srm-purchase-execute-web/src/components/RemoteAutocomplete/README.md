# RemoteAutocomplete 模糊匹配下拉组件
# 一、基本使用：

*<u>**1、代码示例**</u>*

```vue
<template>
  <div>
    <RemoteAutocomplete
      v-model="supplierList"
      url="/masterDataManagement/tenant/supplier/paged-query"
      multiple
      :placeholder="$t('请选择')"
      :fields="{
       text: 'supplierName',
       value: 'supplierCode',
      }"
      :search-fields="supplierSearchFields"
      @change="change"
    ></RemoteAutocomplete>
  </div>
</template>
<script>
import Vue from 'vue'
export default {
  name: 'demo',
  data() {
    return {
      supplierList: [],//value
      supplierSearchFields: ["supplierCode", "supplierName"], //接口通过rule查询时候传递的数据
    }
  },
  methods: {
    change(args) {
      console.log(this.$t('返回结果'), args)
    }
  }
}
</script>
```

*<u>**2、参数说明**</u>*

2.1、**属性**

| 参数          | 说明                                                         | 类型          | 默认值       | 是否必填 |
| ------------- | ------------------------------------------------------------ | ------------- | ---------- | -------- |
| value/v-model | 绑定值，下拉选择选中项, 取值根据fields适配 | string             |              |             |   是     |
| dataSource    | dataSource                                                   | array        |    []        | 否       |
| fields        | 数据源字段匹配                                                | object       |              | 否        |
| multiple      | 是否多选                                                      | boolean      |  false      | 否        |
| url           | 通过url获取数据源                                             | string       |              | 否       |
| method        | 通过url请求时候的请求方法                                      | string       | post         | 否       |
| params        | 通过url请求时候的传参                                         | object/array  |              | 否       |
| paramsKey     | 通过url输入框查询的key值                                      | string        | 'fuzzyParam' | 否       |
| searchFields  | 通过rules查询时候的查询字段                                    | array         |             | 否       |
| ruleParams    | 额外的rules params（与rules拼接）                             | arrry         | []           | 否      |
| placeholder   | 自定义默认文本                                                | string        |              | 否      |
| clearable     | 是否可清除                                                    | boolean       |  true       | 否       |

2.2、**事件**

| 事件   | 说明                      | 回调参数                                        |
| ------ | ------------------------- | ---------------------------------------------- |
| change | 选中发生变化               | 下拉选择选中项 (返回整条数据)                    |


