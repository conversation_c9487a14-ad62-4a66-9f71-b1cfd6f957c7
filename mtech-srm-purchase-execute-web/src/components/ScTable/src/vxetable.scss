$vxe-table-header-background-color: #f5f6f7;
$vxe-table-header-font-color: #333;
$vxe-table-row-striped-background-color: #FAFAFA;
$vxe-table-row-hover-striped-background-color: #f5f7fa;
// $vxe-checkbox-border-width: 1px !default;
$vxe-checkbox-border-radius: 1px !default;

.vxe-table--render-default {
  color: #333 !important;
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--checked-icon:after {
  top: 6px !important;
}
.vxe-table-btns {
  display: flex;
  align-items: center;
  justify-content: center;
}
.vxe-table-btns .vxe-table-btn{
  font-size: 14px;
  font-weight: 400;
  color: #2783FE;
  cursor: pointer;
  margin-right:16px;
  &:last-child{
    margin-right:0;
  }
}
.vxe-button.type--button {
  border: 1px solid #4a556b !important;
  // background-color: #4a556b !important;
  // color: #fff !important;
  box-shadow: 1px 1px 4px 0px #aaa;
}
.vxe-button.type--button:hover {
  color: #4a556b !important;
}
.vxe-button.type--button.is--disabled,
.vxe-button.type--button.is--disabled:hover {
  border: 1px solid #959aaa !important;
  color: #bfbfbb !important;
}
// type=info 按钮样式
.vxe-button.type--button.theme--info:not(.is--disabled) {
  border-color: #2783fe !important;
  background-color: #fff !important;
  color: #4a556b !important;
}
.vxe-button.type--button.theme--info.is--disabled,
.vxe-button.type--button.theme--info.is--disabled:hover {
  border-color: #a6d2ff !important;
  background-color: #fff !important;
  color: #c5c7ca !important;
}
// type=primary按钮样式
.vxe-button.type--button.theme--primary:not(.is--disabled) {
  border-color: #4a556b !important;
  background-color: #4a556b !important;
  color: #fff !important;
}
// type=info 按钮hover样式
.vxe-button.type--button.theme--info:hover {
  color: #4a556b !important;
}
.vxe-button.size--small.type--button {
  height: 28px !important;
  padding: 0 16px !important;
}
.vxe-header--row{
    background: #f5f6f7;
}

.vxe-table-btns .vxe-table-btn {
  font-size: 14px;
  font-weight: 400;
  color: #2783FE;
  cursor: pointer;
  margin-right: 16px;

  &:last-child {
    margin-right: 0;
  }
}
.vxe-table .vxe-cell--sort {
  width: 15px !important;
  height: 15px !important;
}
.vxe-table .vxe-sort--asc-btn {
  top: -0.15rem !important;
}
.vxe-table .vxe-sort--desc-btn {
  bottom: -0.15rem !important;
}

.vxe-header--row {
  background: #f5f6f7;
}
table th, table td {
  border: none;
  border-left: 1px solid #e6e9ed;
}


@import 'vxe-table/styles/index.scss';
