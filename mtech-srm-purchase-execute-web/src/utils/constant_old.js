export const PROXY_FILE = '/file'
export const BASE_TENANT = '/srm-purchase-execute/tenant'
export const STAT_TENANT = '/statistics/tenant'

export const BASE_MESSAGE = '/srm-purchase-execute/message'
export const IAM_TENANT = '/iam/tenant'
export const PRICE_TENANT = '/price/tenant'
export const BASE_COMMON = '/srm-purchase-execute/common'
export const BASE_INTERNAL = '/srm-purchase-execute/internal'
export const BASE_FLOW = '/flow'
export const SUPPLIER_BASE_TENANT = '/supplier/tenant'

export const PROXY_MDM_TENANT = '/masterDataManagement/tenant'
export const PROXY_MDM_AUTH = '/masterDataManagement/auth'
export const PROXY_PRICE_TENANT = '/price/tenant'
export const PROXY_MDM_USER = '/masterDataManagement/user'
export const PROXY_MDM_COMMON = '/masterDataManagement/common/tenant'
export const PROXY_MDM_COMMON_TENANT = '/masterDataManagement/common'
export const maxPageSize = 9999
export const BASE_CONTRACT = '/contract'
export const LOW_CODE = '/lowcodeWeb/tenant'

// 正则表达式
export const RegExpMap = {
  /**
   * 身份证号码
   *
   * 第一位不可能是0
   * 第二位到第六位可以是0-9
   * 第七位到第十位是年份，所以七八位为19，或者2或3开头的4位数
   * 十一位和十二位是月份，这两位是01-12之间的数值
   * 十三位和十四位是日期，是从01-31之间的数值
   * 十五，十六，十七都是数字0-9
   * 十八位可能是数字0-9，也可能是X
   */
  idCardReg:
    /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0-9]{3}|[3][0-9]{3})([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/,
  /**
   * 车牌号
   *
   * 组成：省份简称（1位汉字）+发牌机关代号（1位字母）+序号（6位），总计8个字符，序号不能出现字母I和字母O
   * 通用规则：不区分大小写，第一位：省份简称（1位汉字），第二位：发牌机关代号（1位字母）
   * 序号位：
   * 小型车，第一位：只能用字母D或字母F，第二位：字母或者数字，后四位：必须使用数字
   * ([DF][A-HJ-NP-Z0-9][0-9]{4})
   * 大型车，前五位：必须使用数字，第六位：只能用字母D或字母F。
   * ([0-9]{5}[DF])
   */
  numberPlateReg:
    /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([DF]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[DF]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$/,
  // 手机号
  phoneNumReg: /^1[3-9]\d{9}$/,
  // 整数
  integerReg: /^\d+$/,
  // 英文/数字
  englishAndNumReg: /^[A-Za-z0-9]+$/,
  // 半角英文数字和符号
  halfWidthEngNumAndSymbolsReg: /^[\w\d -/:-@\\[-`{-~]+$/,
  // 中文
  hasChinese: /[\u4e00-\u9fa5]/g
}

// 表格 column 的主数据选择 searchOptions 的配置 默认配置
// searchable: true, // 默认 可搜索
// 可多选字段：行政公司，业务公司，工厂，物料，状态，供应商，客户，工作中心，库存地点
export const MasterDataSelect = {
  // 时间区间选择
  timeRange: {
    elementType: 'date-range',
    operator: 'between',
    serializeValue: (e) => {
      let obj = e.map((x) => Number(new Date(x.toString())))
      obj[1] = obj[1] + Number(86400000 - 1440000)

      //自定义搜索值，规则
      return obj
    }
  },
  // 日期范围选择 00:00:00 - 23:59:59
  dateRange: {
    elementType: 'date-range',
    operator: 'between',
    serializeValue: (e) => {
      //自定义搜索值，规则
      return e.map((x, i) => {
        if (i === 1) {
          return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
        }
        return Number(new Date(x.toString()))
      })
    }
  },
  // 单个时间的值：时间戳
  timeStamp: {
    elementType: 'date',
    serializeValue: (e) => {
      //自定义搜索值，规则
      return Number(new Date(e.toString()))
    }
  },
  // 单个时间的值：非时间戳
  timeOnly: {
    elementType: 'datetime',
    dateFormat: 'YYYY-mm-dd HH:MM:SS'
  },
  // 单位
  unit: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'unit', // 货币
    fields: { text: 'title', value: 'unitCode' },
    inputAble: false,
    placeholderLeft: ' ',
    placeholderRight: ' '
  },
  // 货币
  money: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'money', // 货币
    fields: { text: 'title', value: 'currencyCode' },
    placeholder: ' '
  },
  // 币种
  moneyType: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'money', // 货币
    renameField: 'currencyId',
    fields: { text: 'title', value: 'id' },
    placeholder: ' '
  },
  // 税率
  taxRate: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'taxRate', // 货币
    fields: { text: 'title', value: 'taxTypeCode' }, // taxTypeCode taxRate
    operator: 'equal',
    placeholder: ' '
  },
  // 税率
  saleTaxRate: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'taxRate', // 货币
    fields: { text: 'title', value: 'taxRate' }, // taxTypeCode taxRate
    operator: 'equal',
    placeholder: ' '
  },
  // 成本中心
  costCenter: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'costCenter', // 成本中心
    fields: { text: 'title', value: 'costCenterCode' },
    placeholder: ' '
  },
  // 利润中心
  profitCenter: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'profitCenter', // 利润中心
    fields: { text: 'title', value: 'profitCenterCode' },
    placeholder: ' '
  },
  // 行政公司
  administrativeCompany: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'administrativeCompany', // 行政公司
    fields: { text: 'title', value: 'orgCode' },
    params: {
      orgType: 'ORG001ADM',
      organizationLevelCodes: ['ORG01', 'ORG02']
    },
    multiple: true,
    operator: 'in',
    placeholder: ' '
  },
  // 业务公司
  businessCompany: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'administrativeCompany', // 业务公司
    fields: { text: 'title', value: 'orgCode' },
    params: {
      organizationLevelCodes: ['ORG01', 'ORG02']
    },
    multiple: true,
    operator: 'in',
    placeholder: ' '
  },
  // 部门
  department: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'department', // 部门
    fields: { text: 'title', value: 'departmentCode' },
    placeholder: ' '
  },
  // 岗位
  job: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'job', // 岗位
    fields: { text: 'title', value: 'stationCode' },
    placeholder: ' '
  },
  // 员工
  staff: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'staff', // 员工
    fields: { text: 'title', value: 'employeeCode' },
    placeholder: ' '
  },
  // 工厂
  factoryAddress: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // 工厂
    fields: { text: 'title', value: 'siteCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' '
  },
  // 工厂 供方
  factorySupplierAddress: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', //
    fields: { text: 'title', value: 'siteCode' },
    multiple: true,
    operator: 'in',
    placeholder: '请选择工厂',
    extend: {
      mode: 'normal',
      placeholder: '请选择工厂',

      method: 'post',
      url: `masterDataManagement/auth/site/auth-fuzzy?BU_CODE=${localStorage.getItem('currentBu')}`,
      searchUrl: '',
      searchFields: ['fuzzyParam'],
      input: (item) => `${item.siteCode} ${item.siteName}`,
      title: (item) => `${item.siteCode} ${item.siteName}`,

      recordsPosition: 'data'
    }
  },
  // 分厂 （交货计划） 供方
  subSiteCodeSupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // 工厂
    fields: { text: 'title', value: 'subSiteCode' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择分厂',
    extend: {
      mode: 'normal',
      placeholder: '请选择分厂',
      searchFields: ['subSiteCode'],
      rulesAbled: true,

      method: 'post',
      url: 'srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=subSiteCode',
      searchUrl: '',
      // searchFields: ["fuzzyParam"],
      input: (item) => `${item.subSiteCode} ${item.subSiteName}`,
      title: (item) => `${item.subSiteCode} ${item.subSiteName}`,

      recordsPosition: 'data.records'
    }
  },
  // 分厂库存地点 （交货计划） 供方
  subSiteAddressSupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // 分厂
    fields: { text: 'title', value: 'subSiteAddressCode' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择分厂库存地点',
    extend: {
      mode: 'normal',
      placeholder: '请选择分厂库存地点',
      searchFields: ['subSiteAddress'],
      rulesAbled: true,
      method: 'post',
      url: 'srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=subSiteAddressCode',
      searchUrl: '',

      input: (item) => `${item.subSiteAddressCode} ${item.subSiteAddress}`,
      title: (item) => `${item.subSiteAddressCode} ${item.subSiteAddress}`,

      recordsPosition: 'data.records'
    }
  },
  // 分厂 （jit计划） 采
  subSiteCodeBuyer: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // 工厂
    fields: { text: 'title', value: 'subSiteCode' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择分厂',
    extend: {
      mode: 'normal',
      placeholder: '请选择分厂',
      searchFields: ['subSiteCode'],
      rulesAbled: true,

      method: 'post',
      url: 'srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteCode',
      searchUrl: '',
      // searchFields: ["fuzzyParam"],
      input: (item) => `${item.subSiteCode} ${item.subSiteName}`,
      title: (item) => `${item.subSiteCode} ${item.subSiteName}`,

      recordsPosition: 'data.records'
    }
  },
  // 分厂库存地点 （jit计划） 采方
  subSiteAddressBuyer: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // 分厂
    fields: { text: 'title', value: 'subSiteAddressCode' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择分厂库存地点',
    extend: {
      mode: 'normal',
      placeholder: '请选择分厂库存地点',
      searchFields: ['subSiteAddress'],
      rulesAbled: true,
      method: 'post',
      url: 'srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteAddressCode',
      searchUrl: '',

      input: (item) => `${item.subSiteAddressCode} ${item.subSiteAddress}`,
      title: (item) => `${item.subSiteAddressCode} ${item.subSiteAddress}`,

      recordsPosition: 'data.records'
    }
  },
  // 关联采购订单 （交货计划） 供方
  orderCodeSupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // 分厂
    fields: { text: 'saleOrder', value: 'saleOrder' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择关联采购订单',
    extend: {
      mode: 'normal',
      placeholder: '请选择关联采购订单',
      searchFields: ['saleOrder'],
      rulesAbled: true,
      method: 'post',
      url: 'srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=saleOrder',
      searchUrl: '',

      recordsPosition: 'data.records'
    }
  },
  // 物料 供方
  itemSupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', //
    fields: { text: 'title', value: 'itemCode' },
    multiple: true,
    operator: 'in',
    placeholder: '请选择物料',
    extend: {
      mode: 'normal',
      placeholder: '请选择物料',
      rulesAbled: true,
      operator: 'likeright',

      method: 'post',
      url: `masterDataManagement/tenant/item/paged-auth?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      searchUrl: '',
      searchFields: ['itemCode', 'itemName'],
      input: (item) => `${item.itemCode} ${item.itemName}`,
      title: (item) => `${item.itemCode} ${item.itemName}`,

      recordsPosition: 'data.records'
    }
  },
  // 公司 供方
  companySupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // 工厂
    fields: { text: 'title', value: 'orgCode' },
    multiple: true,
    operator: 'in',
    placeholder: '请选择公司',
    extend: {
      mode: 'normal',
      placeholder: '请选择公司',

      method: 'post',
      url: 'masterDataManagement/auth/company/auth-fuzzy',
      searchUrl: '',
      searchFields: ['fuzzyParam'],
      input: (item) => `${item.orgCode} ${item.orgName}`,
      title: (item) => `${item.orgCode} ${item.orgName}`,

      recordsPosition: 'data'
    }
  },
  // VMI 仓
  vmiWarehouse: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'vmiWarehouse', // Vmi仓
    fields: { text: 'title', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' '
  },
  // VMI 仓
  vmiWarehouseSupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'vmiWarehouse', // Vmi仓
    fields: { text: 'title', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择VMI仓',
      method: 'post',
      url: 'srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier',
      searchUrl: '',
      searchFields: ['base.vmiWarehouseCode', 'base.vmiWarehouseName'],
      input: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,
      title: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,
      rulesAbled: true,
      statusAbled: false,
      recordsPosition: 'data'
    }
  },
  // 钢材VMI 仓
  vmiWarehouseSteel: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'vmiWarehouse', // Vmi仓
    fields: { text: 'title', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择VMI仓',
      method: 'post',
      url: 'srm-purchase-execute/tenant/vmiSteel/vmi-receive-order/supplier-page-query',
      searchUrl: '',
      searchFields: ['vmiWarehouseCode', 'vmiWarehouseName'],
      input: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,
      title: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,
      rulesAbled: true,
      statusAbled: false,
      recordsPosition: 'data.records'
    }
  },
  // 库存地点
  stockAddress: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'stockAddress', // 库存地点
    fields: { text: 'title', value: 'locationCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' '
  },
  stockAddressName: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'stockAddress', // 库存地点
    fields: { text: 'title', value: 'locationName' },
    multiple: true,
    operator: 'in',
    extend: {
      mode: 'normal',
      selectType: 'stockAddress',
      name: '库存地点/编码',
      method: 'post',
      operator: 'likeright',
      url: 'masterDataManagement/tenant/location/paged-query',
      searchFields: [
        // "id",
        'locationCode',
        // "locationDescription",
        'locationName'
      ],
      recordsPosition: 'data.records',
      input: (item) => `${item.locationCode} ${item.locationName}`,
      title: (item) => `${item.locationCode} ${item.locationName}`,
      title2: (item) => `${item.companyOrgName || ''}`, //TODO  缺公司1
      rulesAbled: true,
      statusAbled: false
    },
    placeholder: ' '
  },
  // 库存地点 供方
  stockSupplierAddressName: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    multiple: true,
    operator: 'in',
    // searchFields: ["fuzzyParam"],
    fields: { text: 'locationName', value: 'locationCode' },
    placeholder: '请选择库存地点',
    extend: {
      mode: 'normal',
      placeholder: '请选择库存地点',

      method: 'post',
      url: 'masterDataManagement/auth/location/auth-fuzzy',
      searchUrl: '',
      searchFields: ['fuzzyParam'],
      input: (item) => `${item.locationCode} ${item.locationName}`,
      title: (item) => `${item.locationCode} ${item.locationName}`,

      recordsPosition: 'data'
    }
  },
  // 库存地点 供方 以name为查询条件
  stockSupplierAddressOrName: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    multiple: true,
    operator: 'in',
    // searchFields: ["fuzzyParam"],
    fields: { text: 'title', value: 'locationName' },
    placeholder: '请选择库存地点',
    extend: {
      mode: 'normal',
      placeholder: '请选择库存地点',
      operator: 'likeright',

      method: 'post',
      url: 'masterDataManagement/auth/location/auth-fuzzy',
      searchUrl: '',
      searchFields: ['fuzzyParam'],
      input: (item) => `${item.locationCode} ${item.locationName}`,
      title: (item) => `${item.locationCode} ${item.locationName}`,

      recordsPosition: 'data'
    }
  },
  // 采购组 供方
  businessCodeName: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    multiple: true,
    placeholder: '请选择采购组',

    operator: 'in',
    // searchFields: ["fuzzyParam"],
    fields: { text: 'groupName', value: 'groupCode' },
    extend: {
      placeholder: '请选择采购组',
      mode: 'normal',
      method: 'post',
      url: `masterDataManagement/auth/business-group/auth-fuzzy?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      searchUrl: '',
      searchFields: ['fuzzyParam'],
      input: (item) => `${item.groupCode} ${item.groupName}`,
      title: (item) => `${item.groupCode} ${item.groupName}`,

      recordsPosition: 'data'
    }
  },
  // 工作中心
  workCenter: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'workCenter', // 工作中心
    fields: { text: 'title', value: 'workCenterCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' '
  },
  // 业务组
  businessGroup: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'businessGroup', // 业务组
    fields: { text: 'title', value: 'groupCode' },
    placeholder: ' ',
    extend: {
      mode: 'normal',
      selectType: 'businessGroup',
      name: '业务组/编码',
      method: 'post',
      url: `masterDataManagement/tenant/business-group/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      searchFields: ['groupCode', 'groupName'],
      recordsPosition: 'data.records',
      input: (item) => `${item.groupCode} ${item.groupName}`,
      title: (item) => `${item.groupCode} ${item.groupName}`,
      title2: (item) => `${item.companyOrgName || ''}`, //TODO  缺公司 1
      params: {},
      rulesAbled: true,
      statusAbled: false
    }
  },
  // 业务组 多选
  businessGroupIn: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'businessGroup', // 业务组
    fields: { text: 'title', value: 'groupCode' },
    multiple: true,
    operator: 'in',
    extend: {
      mode: 'normal',
      selectType: 'businessGroup',
      name: '业务组/编码',
      method: 'post',
      url: `masterDataManagement/tenant/business-group/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      searchFields: ['groupCode', 'groupName'],
      recordsPosition: 'data.records',
      input: (item) => `${item.groupCode} ${item.groupName}`,
      title: (item) => `${item.groupCode} ${item.groupName}`,
      title2: (item) => `${item.companyOrgName || ''}`, //TODO  缺公司 1
      params: {},
      rulesAbled: true,
      statusAbled: false
    }
  },
  // 业务组织
  businessGroupUnit: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'businessGroupUnit', // 业务组织
    fields: { text: 'title', value: 'organizationCode' },
    placeholder: ''
  },
  // 国家
  country: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'country', // 国家
    fields: { text: 'title', value: 'countryCode' },
    placeholder: ' '
  },
  // 收货地址
  receivingAddress: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'receivingAddress', // 收货地址
    fields: { text: 'title', value: 'TODO' },
    placeholder: ' '
  },
  // 供应商
  supplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'title', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' '
  },
  // 供应商
  supplierAll: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'title', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择供应商',
      method: 'post',
      url: 'masterDataManagement/tenant/supplier/paged-query',
      searchUrl: '',
      params: {},
      searchFields: ['id', 'supplierCode', 'supplierDescription', 'supplierName'],
      recordsPosition: 'data.records',
      input: (item) => `${item.supplierCode} ${item.supplierName}`,
      title: (item) => `${item.supplierCode}`,
      title2: (item) => `${item.supplierName}`, //TODO  搜索后无法显示数据
      rulesAbled: true
    }
  },
  // 供应商 供方
  supplierSu: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'title', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择供应商',
      method: 'post',
      url: 'srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier',
      searchUrl: '',
      searchFields: ['supplierCode', 'supplierName'],
      input: (item) => `${item.supplierCode} ${item.supplierName}`,
      title: (item) => `${item.supplierCode} ${item.supplierName}`,
      rulesAbled: true,
      statusAbled: false,
      recordsPosition: 'data'
    }
  },
  // 钢材供应商 供方
  supplierVMI: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'title', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择钢材供应商',
      method: 'post',
      url: 'srm-purchase-execute/tenant/vmiSteel/vmi_warehouse_supplier_rel/criteria-query-for-supplier',
      searchUrl: '',
      searchFields: ['supplierCode', 'supplierName'],
      input: (item) => `${item.supplierCode} ${item.supplierName}`,
      title: (item) => `${item.supplierCode} ${item.supplierName}`,
      rulesAbled: true,
      statusAbled: false,
      recordsPosition: 'data'
    }
  },
  // 钣金供应商 供方
  SteelSupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'title', value: 'processorCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择钣金供应商',
      method: 'post',
      url: 'srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/supplier-page-query',
      searchUrl: '',
      searchFields: ['processorCode', 'processorName'],
      input: (item) => `${item.processorCode} ${item.processorName}`,
      title: (item) => `${item.processorCode} ${item.processorName}`,
      rulesAbled: true,
      statusAbled: false,
      recordsPosition: 'data.records'
    }
  },
  // 原材料供应商 供方
  materialSupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'title', value: 'processorCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择原材料供应商',
      method: 'post',
      url: 'srm-purchase-execute/tenant/supplierOrderDelivery',
      searchUrl: '',
      searchFields: ['materialSupplierCode', 'materialSupplierName'],
      input: (item) => `${item.materialSupplierCode} ${item.materialSupplierName}`,
      title: (item) => `${item.materialSupplierCode} ${item.materialSupplierName}`,
      rulesAbled: true,
      statusAbled: false,
      recordsPosition: 'data.records'
    }
  },
  // 第三方物流 供方
  thirdPartSupplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'title', value: 'thirdPartyLogisticsTenantId' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择第三方物流',
      method: 'post',
      url: 'srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier',
      searchUrl: '',
      searchFields: ['thirdPartyLogisticsCode', 'thirdPartyLogisticsName'],
      input: (item) => `${item.thirdPartyLogisticsCode} ${item.thirdPartyLogisticsName}`,
      title: (item) => `${item.thirdPartyLogisticsCode} ${item.thirdPartyLogisticsName}`,
      rulesAbled: true,
      statusAbled: false,
      recordsPosition: 'data'
    }
  },
  // 第三方物流 供应商
  supplierThird: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    // selectType: "supplier", // 供应商
    fields: { text: 'title', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择供应商',
      method: 'post',
      url: 'srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForLogistic',
      searchUrl: '',
      searchFields: ['supplierCode', 'supplierName'],
      input: (item) => `${item.supplierCode} ${item.supplierName}`,
      title: (item) => `${item.supplierCode} ${item.supplierName}`,
      params: {},
      rulesAbled: true,
      recordsPosition: 'data',
      statusAbled: false
    }
  },
  // 第三方物流 VMI仓
  vmiThird: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    // selectType: "vmiWarehouse", // Vmi仓
    fields: { text: 'title', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    placeholder: ' ',
    extend: {
      mode: 'normal',
      placeholder: '请选择VMI仓',
      method: 'post',
      url: 'srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForLogistic',
      searchUrl: '',
      searchFields: ['base.vmiWarehouseCode', 'base.vmiWarehouseName'],
      input: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,
      title: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,
      params: {},
      rulesAbled: true,
      recordsPosition: 'data',
      statusAbled: false
    }
  },
  // sku
  sku: {
    elementType: 'masterdata-selects',
    type: 'dialog', // 弹框选择
    dialogType: 'sku', // sku
    operator: 'in',
    placeholder: ' '
  },
  // 物料
  material: {
    elementType: 'masterdata-selects',
    type: 'select', // 弹框选择
    selectType: 'material', // 物料
    multiple: true,
    operator: 'in',
    fields: { text: 'title', value: 'itemCode' },
    placeholder: ' '
  },
  // 货源关系
  supply: {
    elementType: 'masterdata-selects',
    type: 'dialog', // 弹框选择
    dialogType: 'supply', // 货源关系
    operator: 'in',
    placeholder: ' '
  },
  // 品类
  category: {
    elementType: 'masterdata-selects',
    type: 'dialog', // 弹框选择
    dialogType: 'category', // 品类
    operator: 'in',
    placeholder: ' '
  },
  // 品类 建议用这个
  categoryName: {
    elementType: 'remote-autocomplete',
    renameField: 'categoryCode',
    fields: { text: 'categoryName', value: 'categoryCode' },
    params: {},
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/tenant/category/paged-query',
    placeholder: ' ',
    supplierSearchFields: ['categoryCode', 'categoryName']
  },
  // 字典 OrderType 订单类型
  dictOrderType: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    multiple: true,
    operator: 'in',
    fields: { text: 'title', value: 'itemCode' },
    extend: {
      mode: 'normal',
      method: 'post',
      url: 'masterDataManagement/tenant/dict-item/dict-code',

      searchUrl: '',
      searchFields: [],
      input: (item) => `${item.itemCode} ${item.itemName}`,
      title: (item) => `${item.itemCode} ${item.itemName}`,
      params: {
        dictCode: 'OrderType' // 字典类型编码
      },
      recordsPosition: 'data'
    },
    placeholder: ' '
  },
  // 字典 供方 OrderType 订单类型
  dictOrderSupplierType: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    multiple: true,
    placeholder: '请选择类型',

    operator: 'in',
    fields: { text: 'title', value: 'itemCode' },
    extend: {
      mode: 'normal',
      placeholder: '请选择类型',

      method: 'post',
      url: 'masterDataManagement/auth/dict-item/auth-find',
      searchUrl: '',
      searchFields: [],
      input: (item) => `${item.itemCode} ${item.itemName}`,
      title: (item) => `${item.itemCode} ${item.itemName}`,
      params: {
        dictCode: 'OrderType' // 字典类型编码
      },
      recordsPosition: 'data'
    }
  },
  // 字典 payMethod 付款方式
  dictPaymentMode: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    multiple: true,
    operator: 'in',
    fields: { text: 'title', value: 'itemCode' },
    extend: {
      mode: 'normal',
      method: 'post',
      url: 'masterDataManagement/tenant/dict-item/dict-code',
      searchUrl: '',
      searchFields: [],
      input: (item) => `${item.itemCode} ${item.itemName}`,
      title: (item) => `${item.itemCode} ${item.itemName}`,
      params: {
        dictCode: 'payMethod' // 字典类型编码
      },
      recordsPosition: 'data'
    },
    placeholder: ' '
  },
  // 送货单列表 明细下拉
  deliverSupplierVmiWarehouseCode: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // VMi仓
    fields: { text: 'title', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择VMi仓',
    extend: {
      mode: 'normal',
      placeholder: '请选择VMi仓',
      searchFields: ['vmiWarehouseCode'],
      rulesAbled: true,

      method: 'post',
      url: 'srm-purchase-execute/tenant/supplierOrderDeliveryItem/page?conditionType=vmiWarehouseCode',
      searchUrl: '',
      // searchFields: ["fuzzyParam"],
      input: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,
      title: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,

      recordsPosition: 'data.records'
    }
  },
  deliverSupplierThirdTenantCode: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // VMi仓
    fields: { text: 'title', value: 'thirdTenantCode' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择第三方物流商',
    extend: {
      mode: 'normal',
      placeholder: '请选择第三方物流商',
      searchFields: ['thirdTenantCode'],
      rulesAbled: true,

      method: 'post',
      url: 'srm-purchase-execute/tenant/supplierOrderDeliveryItem/page?conditionType=thirdTenantCode',
      searchUrl: '',
      // searchFields: ["fuzzyParam"],
      input: (item) => `${item.thirdTenantCode} ${item.thirdTenantName}`,
      title: (item) => `${item.thirdTenantCode} ${item.thirdTenantName}`,

      recordsPosition: 'data.records'
    }
  },
  deliver: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // VMi仓
    fields: { text: 'title', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择VMi仓',
    extend: {
      mode: 'normal',
      placeholder: '请选择VMi仓',
      searchFields: ['vmiWarehouseCode'],
      rulesAbled: true,

      method: 'post',
      url: 'srm-purchase-execute/tenant/buyerOrderDelivery/query/item/page?conditionType=vmiWarehouseCode',
      searchUrl: '',
      // searchFields: ["fuzzyParam"],
      input: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,
      title: (item) => `${item.vmiWarehouseCode} ${item.vmiWarehouseName}`,

      recordsPosition: 'data.records'
    }
  },
  deliverThirdTenantCode: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // VMi仓
    fields: { text: 'title', value: 'thirdTenantCode' },
    multiple: true,
    operator: 'in',
    params: {
      // conditionType: "subSiteCode",
    },
    placeholder: '请选择VMi仓',
    extend: {
      mode: 'normal',
      placeholder: '请选择VMi仓',
      searchFields: ['thirdTenantCode'],
      rulesAbled: true,

      method: 'post',
      url: 'srm-purchase-execute/tenant/buyerOrderDelivery/query/item/page?conditionType=thirdTenantCode',
      searchUrl: '',
      // searchFields: ["fuzzyParam"],
      input: (item) => `${item.thirdTenantCode} ${item.thirdTenantName}`,
      title: (item) => `${item.thirdTenantCode} ${item.thirdTenantName}`,

      recordsPosition: 'data.records'
    }
  }
}
