import Vue from 'vue'

const utils = {}
utils.dateFormat = (timestamp, formats) => {
  // formats格式包括
  // 1. Y-m-d
  // 2. Y-m-d H:i:s
  // 3. Y年m月d日
  // 4. Y年m月d日 H时i分
  formats = formats || 'Y-m-d H:i:s'

  var zero = function (value) {
    if (value < 10) {
      return '0' + value
    }
    return value
  }

  var myDate = timestamp ? new Date(timestamp) : new Date()

  var year = myDate.getFullYear()
  var month = zero(myDate.getMonth() + 1)
  var day = zero(myDate.getDate())

  var hour = zero(myDate.getHours())
  var minite = zero(myDate.getMinutes())
  var second = zero(myDate.getSeconds())

  return formats.replace(/Y|m|d|H|i|s/gi, function (matches) {
    return {
      Y: year,
      m: month,
      d: day,
      H: hour,
      i: minite,
      s: second
    }[matches]
  })
}
utils.formateTime = (date, fmt = 'Y-m-d') => {
  if (!date) {
    return
  }
  let ret
  // YYYY/mm/dd HH:MM:SS
  const opt = {
    'Y+': date.getFullYear().toString(),
    'm+': (date.getMonth() + 1).toString(),
    'd+': date.getDate().toString(),
    'H+': date.getHours().toString(),
    'M+': date.getMinutes().toString(),
    'S+': date.getSeconds().toString()
  }
  for (let k in opt) {
    ret = new RegExp('(' + k + ')').exec(fmt)
    if (ret) {
      fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'))
    }
  }
  return fmt
}

utils.dataFormat = (value, format = 2) => {
  var minus = false
  if (value == null) return '0.00'
  if (value.toString().indexOf('-') > -1) {
    value = value.toString().substring(1)
    minus = true
  }
  let s = parseFloat((value + '').replace(/[^\d.-]/g, '')).toFixed(format) + ''
  let l = s.split('.')[0].split('').reverse(),
    r = s.split('.')[1]
  let f = ''
  for (let i = 0; i < l.length; i++) {
    f += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? ',' : '')
  }
  if (minus) return '-' + f.split('').reverse().join('') + '.' + r
  return f.split('').reverse().join('') + '.' + r
}
utils.randomString = (e = 20) => {
  var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',
    a = t.length,
    n = ''
  for (let i = 0; i < e; i++) {
    n += t.charAt(Math.floor(Math.random() * a))
  }
  return n
}
// {
//   "Name": {
//     "required": [true, "请输入"],
//     "email": [true, "请输入正确的email"],
//     "url": [true, "请输入正确的url"],
//     "date": [true, "请输入正确的date"],
//     "dateIso": [true, "请输入正确的dateIso"],
//     "number": [true, "请输入number"],
//     "maxLength": [2, "最大长度2"],
//     "minLength": [2, "最小长度2"],
//     "rangeLength": [[1, 5], "长度在1-5个之间"],
//     "range": [[1, 5], "1-5之间的数字"],
//     "max": [5, "小于5的数字"],
//     "min": [5, "大于5的数字"],
//     "regex": ["^[A-z]+$", "字母"]
//     }
//   }
utils.formatRules = (rules) => {
  if (Object.prototype.toString.call(rules) != '[object Object]') {
    return {}
  }
  let res = {}
  for (var i in rules) {
    let _oneRule = []
    for (var j in rules[i]) {
      if (typeof rules[i][j][0] == 'boolean' && j != 'required') {
        _oneRule.push({
          type: j,
          message: rules[i][j][1],
          trigger: 'blur'
        })
      } else {
        _oneRule.push({
          [j]: rules[i][j][0],
          message: rules[i][j][1],
          trigger: 'blur'
        })
      }
    }
    res[i] = _oneRule
  }
  return res
}

// 平级数据转化成有树形结构的
utils.transformToTree = (array) => {
  let parents = array.filter((item) => item.treeLevel == 0)
  let childrens = array.filter((item) => item.treeLevel != 0)

  let translator = (parents, childrens) => {
    parents.forEach((parent) => {
      childrens.forEach((current, index) => {
        // 此时找到父节点对应的一个子节点
        if (parent.id == current.parentId) {
          // 对子节点数据进行深拷贝，
          let temp = JSON.parse(JSON.stringify(childrens))
          // 让当前子节点从temp中移除，temp作为新的子节点数据，这里是为了让递归时，子节点的遍历次数更少，如果父子关系的层级越多，越有利
          temp.splice(index, 1)

          // 让当前子节点作为唯一的父节点，去递归查找其对应的子节点
          translator([current], temp)

          // 把找到子节点放入父节点的children属性中
          typeof parent.children !== 'undefined'
            ? parent.children.push(current)
            : (parent.children = [current])
        }
      })
    })
  }

  //调用转换方法
  translator(parents, childrens)

  return parents
}

utils.toQueryParams = (params) => {
  const keys = Object.keys(params)
  let queryParams = []

  keys.forEach((k) => {
    queryParams.push(`${k}=${params[k]}`)
  })

  return queryParams.join('&')
}

// 采购申请 处理动态字段，平铺到行数据上
utils.platFieldList = (list) => {
  let obj = {}
  list.forEach((item) => {
    obj[item.fieldCode] = item.fieldData
  })
  return obj
}

// 数组排序
export const sort = function (property) {
  return function (a, b) {
    var value1 = a[property]
    var value2 = b[property]
    return value1 - value2
  }
}

// 通过 a 标签下载文件
export const download = (data) => {
  const { fileName, blob } = data

  if (!blob) {
    return
  }

  if (blob?.type === 'application/json') {
    const reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = function () {
      const readerRes = reader.result
      const resObj = JSON.parse(readerRes)
      Vue.prototype.$toast({
        content: resObj.msg,
        type: 'error'
      })
    }

    return
  }

  const a = document.createElement('a')
  a.href = URL.createObjectURL(blob)
  a.style.display = 'none'
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

// 获取浏览器滚动条宽度
export const getScrollbarWidth = () => {
  const scrollDiv = document.createElement('div')
  scrollDiv.style.cssText =
    'width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;'
  document.body.appendChild(scrollDiv)
  const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth || 0
  document.body.removeChild(scrollDiv)
  return scrollbarWidth
}

/**
 * 时间戳转日期显示
 * @param data: { formatString: <"YYYY-mm-dd"/"HH:MM:SS">, value: "时间戳" }
 * @returns String
 */
export const timeNumberToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

/**
 * 时间字符串转日期显示
 * @param data data: { formatString: <"YYYY-mm-dd"/"HH:MM:SS">, value: "yyyy-mm-dd hh:mm:ss" }
 * @returns String
 */
export const timeStringToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

/**
 * 获取请求头中的文件名称
 * @param data data: { headers: content-disposition: "<文件名信息>" }
 * @returns String
 */
export const getHeadersFileName = (data) => {
  const contentDisposition = data?.headers['content-disposition'] || ''
  const prefix = "attachment;filename*=utf-8'zh_cn'"
  const prefix1 = 'attachment; filename='
  const prefix2 = 'attachment;filename='
  let fileName = contentDisposition
  if (contentDisposition.indexOf(prefix) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix.length))
  } else if (contentDisposition.indexOf(prefix1) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix1.length))
  } else if (contentDisposition.indexOf(prefix2) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix2.length))
  }

  return fileName
}
const isString = (obj) => Object.prototype.toString.call(obj) === '[object String]'
const isEmpty = (obj) => {
  /* eslint-disable */
  if (obj == null) return true
  if (Array.isArray(obj) || isString(obj)) return obj.length === 0
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) return false
  }
  return true
}
utils.download = download
utils.getHeadersFileName = getHeadersFileName
utils.isEmpty = isEmpty
export default utils

/**
 * 字符串通过特定字符分割 转 数组
 * @param args args: { str: String, character: String }
 * @returns Array
 */
export const strByCharacterToArray = (args) => {
  const { str, character } = args
  let arr = []
  if (!str || typeof str !== 'string') {
    return arr
  }
  arr = str.split(character).filter((item) => item.length > 0) // 指定字符分割，过滤空项

  return [...new Set(arr)] // 去重
}

// 禁止输入 e 、E、-、+ 使 input-number 仅可输入数字
export const numberInputOnKeyDown = (e) => {
  if (['e', 'E', '-', '+'].includes(e.key)) {
    e.returnValue = false
  } else {
    e.returnValue = true
  }
}

// 禁止输入 e 、E、+ 使 input-number 仅可输入数字、可负数
export const negativeInputOnKeyDown = (e) => {
  if (['e', 'E', '+'].includes(e.key)) {
    e.returnValue = false
  } else {
    e.returnValue = true
  }
}

// 显示 code+name
export const codeNameColumn = (args) => {
  const { firstKey, secondKey, thirdKey, fourthKey, cssClass } = args
  const template = () => {
    return {
      template: Vue.component('codeNameColumnComponent', {
        template: `
          <div style="display: flex; align-items: center; height: 100%;">
            <div :class="['text-ellipsis', cssClass]">{{theColumnData}}</div>
          </div>
        `,
        data: function () {
          return {
            data: {},
            firstKey,
            secondKey,
            thirdKey,
            fourthKey,
            cssClass,
            theColumnData: ''
          }
        },
        watch: {
          data: {
            handler(newVal) {
              // 设置数据格子的值
              this.setTheColumnData(newVal)
            },
            immediate: true
          }
        },
        mounted() {
          // 设置数据格子的值
          this.setTheColumnData(this.data)
          if (cssClass === 'need-jit-font-red' && this.data.status !== 4) {
            this.cssClass = ''
          }
        },
        beforeDestroy() {},
        methods: {
          // 设置数据格子的值
          setTheColumnData(data) {
            if (data[firstKey] && data[secondKey] && data[thirdKey] && data[fourthKey]) {
              // firstKey-secondKey-thirdKey-fourthKey
              this.theColumnData = `${data[firstKey]}-${data[secondKey]}-${data[thirdKey]}-${data[fourthKey]}`
            } else if (data[firstKey] && data[secondKey] && data[thirdKey]) {
              // firstKey-secondKey-thirdKey
              this.theColumnData = `${data[firstKey]}-${data[secondKey]}-${data[thirdKey]}`
            } else if (data[firstKey] && data[secondKey]) {
              // firstKey-secondKey
              this.theColumnData = `${data[firstKey]}-${data[secondKey]}`
            } else if (data[firstKey]) {
              // firstKey
              this.theColumnData = data[firstKey]
            } else if (data[secondKey]) {
              // secondKey
              this.theColumnData = data[secondKey]
            } else {
              // ""
              this.theColumnData = ''
            }
          }
        }
      })
    }
  }
  return template
}

// 在数组中增加 theCodeName 字段
export const addCodeNameKeyInList = (args) => {
  const { firstKey, secondKey, thirdKey, fourthKey, list } = args
  if (list) {
    return list.map((item) => {
      const itemData = { ...item, theCodeName: '' }
      if (itemData[firstKey] && itemData[secondKey] && itemData[thirdKey] && itemData[fourthKey]) {
        // firstKey-secondKey-thirdKey-fourthKey
        itemData.theCodeName = `${itemData[firstKey]}-${itemData[secondKey]}-${itemData[thirdKey]}-${itemData[fourthKey]}`
      } else if (itemData[firstKey] && itemData[secondKey] && itemData[thirdKey]) {
        // firstKey-secondKey-thirdKey
        itemData.theCodeName = `${itemData[firstKey]}-${itemData[secondKey]}-${itemData[thirdKey]}`
      } else if (itemData[firstKey] && itemData[secondKey]) {
        // firstKey-secondKey
        itemData.theCodeName = `${itemData[firstKey]}-${itemData[secondKey]}`
      } else if (item[firstKey]) {
        // firstKey
        itemData.theCodeName = itemData[firstKey]
      } else if (itemData[secondKey]) {
        // secondKey
        itemData.theCodeName = itemData[secondKey]
      }
      return itemData
    })
  } else {
    return []
  }
}

// 根据code或name是否存在显示 code - name
export const judgeFormatCodeName = (code = '', name = '') => {
  if (code && name) return `${code} - ${name}`
  if (code && !name) return `${code}`
  if (!code && name) return `${name}`
  return null
}

import { MasterDataSelect } from './constant'
export const formatMasterFilter = (col) => {
  // 供应商、物料、sku、品类、基本单位、采购单位、币种、业务公司、工厂、成本中心、利润中心、税率
  // renameField：搜索列表接口时的key；filterName：constant里的内容；ignore：忽略搜索；isNotUnit不组合显示code-name
  let masterObj = {
    supplierName: {
      renameField: 'supplierCode',
      filterName: 'supplier'
    },
    itemCode: {
      renameField: 'itemCode',
      filterName: 'material',
      isNotUnit: true
    },
    itemName: {
      ignore: true //‘忽略’此字段
    },
    skuCode: {
      renameField: 'skuCode',
      filterName: 'sku',
      isNotUnit: true
    },
    skuName: {
      ignore: true //‘忽略’此字段
    },
    categoryName: {
      renameField: 'categoryCode',
      filterName: 'categoryName'
    },
    unitName: {
      renameField: 'unitCode',
      filterName: 'unit'
    },
    orderUnitName: {
      renameField: 'orderUnitCode',
      filterName: 'unit'
    },
    currencyName: {
      renameField: 'currencyCode',
      filterName: 'money'
    },
    companyName: {
      renameField: 'companyCode',
      filterName: 'businessCompany'
    },
    siteName: {
      renameField: 'siteCode',
      filterName: 'factoryAddress'
    },
    costSharingAccName: {
      renameField: 'costSharingAccCode',
      filterName: 'costCenter'
    },
    profitCenterName: {
      renameField: 'profitCenterCode',
      filterName: 'profitCenter'
    },
    taxRateCode: {
      renameField: 'taxRateCode',
      filterName: 'taxRate',
      isNotUnit: true
      // valueAccessor: (field, data) => {
      //   return data?.[field] + ' - ' + data?.taxid*100 + '%'
      // }
    }
  }
  let _col = { ...col }
  if (masterObj[col.field]) {
    // 设置主数据搜索
    let _oneMaster = masterObj[col.field]
    // 物料、sku的 name 不用搜索
    if (_oneMaster.ignore) {
      _col.ignore = true
      return _col
    }

    _col.searchOptions = {
      ...MasterDataSelect[_oneMaster.filterName],
      renameField: _oneMaster.renameField
    }
    // 整合code-name
    if (!_oneMaster?.isNotUnit) {
      _col.width = '300' // 物料编号或者sku编码，不用宽度300
      _col.valueAccessor = (field, data) => {
        return judgeFormatCodeName(data?.[_oneMaster.renameField], data?.[field])
      }
    }

    // 如果自带valueAccessor
    if (_oneMaster?.valueAccessor) _col.valueAccessor = _oneMaster.valueAccessor
  }
  return _col
}

export const shapeRandom = () => {
  return Math.random().toString(36).substring(1, 11)
}

/**
 *
 * @param {*} to
 * @param {*} from
 * @param {*} unKeepRoute 对象，key为来源的path，from为不缓存的path
 */
export const unKeepRouteLeave = (to, from, unKeepRoute) => {
  // let prePath = "/purchase-execute/";
  // 如果是数组，那么如果跳转的链接在 非keep的路由里，就添加random
  if (unKeepRoute && Object.keys(unKeepRoute)?.length) {
    let _toPath = to.name
    let _fromPath = from.name
    // console.log("unKeepRouteLeave--", to, _fromPath, unKeepRoute);
    if (unKeepRoute[_fromPath] && unKeepRoute[_fromPath].includes(_toPath)) {
      to.meta.random = shapeRandom()
    } else if (unKeepRoute[_toPath] && unKeepRoute[_toPath].includes(_fromPath)) {
      to.meta.random = '' // 如果是新增的详情页返回列表时，才去掉随机数
    }
  }
}

export const getPurchaseDict = (code) => {
  const dict = JSON.parse(sessionStorage.getItem('purchaseDictionary')) || {}
  if (code && !isEmpty(dict)) {
    return dict[code]
  } else {
    return []
  }
}
