// 收发货协同
export default {
  receiptAndDelivery: {
    // *********** 采方 ***********
    // * 供货计划-采方 类型：菜单
    supplyPlan: {
      // 采购订单
      purchaseOrder: '0ad513b0-50bf-42e7-b94d-07898726c723',
      // 交货计划
      deliverySchedule: 'db5a2cdc-a78e-4bb9-86c3-be73913494e9'
    },
    // * 送货单-列表-采方 类型：菜单
    deliverList: {
      // 送货单列表
      list: 'de4beacf-b694-4d38-b3e3-d2c1b38ef89c',
      // 送货明细
      details: 'ca925352-c3ff-7131-60d1-b5bba0c57176'
    },
    // * 送货单-详情-采方
    deliverDetail: {
      // 物料信息
      materialInfo: '0f898779-6f1f-4a48-8fae-ba09e927e3a6'
    },
    // * 收退货记录-采方 类型：菜单
    receiptReturn: {
      list: '044031f2-1afc-41e1-95c8-8488952e4bef'
    },
    // * 预约送货-采方 类型：菜单
    reservationDeliver: {
      list: '1d256410-4944-4148-987c-9e8a6217f3e8'
    },
    // * 收货-采方 类型：菜单
    receiptListProcessing: {
      // 主单视图
      masterOrder: '0853b920-526a-47bc-ad8c-ff28bff7e960',
      // 明细视图
      detailView: '3339f2f3-45f1-4745-9305-e84b8a5f4e0f'
    },
    // * 收货-采方 类型：详情
    receiptListDetail: {
      // 物料信息
      materialInfo: '3b04e997-c051-498a-a3bc-dc73164034e3'
    },
    // *********** 供方 ***********
    // * 供货计划-供方 类型：菜单
    supplyPlanSupplier: {
      // 采购订单
      purchaseOrder: 'd91041de-7639-44b8-89cc-9a6b5e1a3a5f',
      // 交货计划
      deliverySchedule: 'e8c5ca36-2c58-4229-bc4f-7aeeb7896a77'
    },
    // * 批量创建送货单-供方
    batchDeliverySupplier: {},
    // * 送货单-列表-供方 类型：菜单
    deliverListSupplier: {
      // 送货单列表
      list: '24e73c2a-b5fd-4d8d-bc16-cea1867d4a6d',
      // 送货明细
      details: 'f257e30d-3e43-4af3-8212-cf28bdc81bb9'
    },
    // * 手工新建无PO送货单-供方
    createNoOrderSupplier: {},
    // * 送货单-详情-供方/送货单（无采购订单）-详情-供方
    deliverDetailSupplier: {
      // 物料信息
      materialInfo: '6724f67c-c9d2-463f-91ac-10fb8b1ae709'
    },
    // * 送货单-维护物流信息-供方
    deliverLogisticsSupplier: {
      // 物料信息
      materialInfo: '3649f445-dfba-4c7a-b014-750e99747126'
    },
    // * 送货司机信息维护-供方 类型：菜单
    driverInfoSupplier: {
      list: '160a7c1c-ada8-485e-88ff-bb1787cf4122'
    },
    // * 收退货记录-供方 类型：菜单
    receiptReturnSupplier: {
      list: '6715932f-8ab7-4340-b62b-af5a4ca0fada'
    },
    // * 预约送货-供方 类型：菜单
    reservationDeliverSupplier: {
      list: 'ba8c32d6-191f-4c4f-82e5-553d4b0709d6'
    },
    // *
    // *********** 加工方 ***********
    // * 送货单-列表-加工方 类型：菜单
    deliverListProcessing: {
      // 主单视图
      masterOrder: 'f9ce369b-50bb-4c52-88bc-7312efc04c02',
      // 明细视图
      detailView: '6faf5782-6587-4063-9780-d1f24868f727'
    },
    // * 送货单-详情-加工方
    deliverDetailProcessing: {},
    // *
    // * ***********条码打印***********
    // * 条码需求管理 类型：菜单
    requireManagement: {
      list: 'ae391ddd-0fb0-4970-bda8-a15f035a28ac',
      ProcessedList: 'ba79d3ae-4e11-41c0-8f41-ade5439ab248'
    },
    // * 条码需求设置 类型：菜单
    barcodeRequireSetup: {
      list: 'd6528ee6-3ed2-4aa7-834a-ca12a5f14435'
    },
    // * 条码关联 类型：菜单
    barcodeAssociation: {
      list: '285f069c-5125-463f-996f-e0a4026e78d6'
    },
    // * 条码设置
    barcodeSetup: {
      list: 'fc607075-771a-4a05-85e3-cda23cf681ca'
    },
    // * 出入库记录
    inoutRecord: {
      list: '2402f704-ab6b-44af-8ada-b3d9732d5d33'
    }
  }
}
