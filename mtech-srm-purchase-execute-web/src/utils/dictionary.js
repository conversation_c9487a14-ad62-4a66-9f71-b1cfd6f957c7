import Vue from 'vue'
const codeList = [
  { code: 'JIT_SUBMIT_STATUS', type: 'string' }, // JIT提交状态
  { code: 'JIT_SOURCE_TYPE', type: 'string' }, // 来源方式
  { code: 'temp_access_photo_declaration_desc', type: 'string' }, // 王牌临时出入证照片声明
  { code: 'temp_access_photo_requirements_desc', type: 'string' } // 王牌临时出入证 - 照片要求
]
export default function () {
  Vue.prototype.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
    const { code, data } = res
    if (code === 200 && data) {
      // 给所有字典加一个cssClass的空值，避免template-page序列化的映射出现问题
      for (const key in data) {
        const arr = data[key]
        arr.forEach((item) => {
          item.cssClass = ''
        })
      }
      sessionStorage.setItem('purchaseDictionary', JSON.stringify(data))
    }
  })
}
