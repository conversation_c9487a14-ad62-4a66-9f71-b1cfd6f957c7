import Vue from 'vue'

// 通过 a 标签下载文件
export const download = (data) => {
  const { fileName, blob } = data
  console.log(fileName, blob, '22222222222222222')
  if (!blob) {
    return
  }

  if (blob?.type === 'application/json') {
    const reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = function () {
      const readerRes = reader.result
      const resObj = JSON.parse(readerRes)
      Vue.prototype.$toast({
        content: resObj.msg,
        type: 'error'
      })
    }

    return
  }

  const a = document.createElement('a')
  a.href = URL.createObjectURL(blob)
  a.style.display = 'none'
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

/**
 * 获取请求头中的文件名称
 * @param data data: { headers: content-disposition: "<文件名信息>" }
 * @returns String
 */
export const getHeadersFileName = (data) => {
  const contentDisposition = data?.headers['content-disposition'] || ''
  const prefix = 'attachment;filename='
  let fileName = contentDisposition
  if (contentDisposition.indexOf(prefix) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix.length))
  }

  return fileName
}
