import Vue from 'vue'
import axios from 'axios'
import Cookies from 'js-cookie'
const ERR_MSG = {
  302: '未登录',
  400: '账号异常，登录超时，请重新登录',
  401: '无权查看',
  500: '系统异常，请稍后再试',
  501: '表单验证未通过',
  503: '数据异常，操作失败',
  505: 'excel导入异常',
  default: '系统异常，请稍后再试'
}
let $http = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest'
  },
  timeout: 1000 * 30
})

$http.interceptors.request.use(
  (config) => {
    if (Cookies.get('token')) {
      //如果接入了login，使用此代码
      config.headers['api-token'] = JSON.parse(Cookies.get('token')) || ''
    }
    // config.headers["api-token"] = "000c4f3c-c443-4764-8671-f045d560df07";
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

$http.interceptors.response.use(
  (response) => {
    if (response.status === 200) {
      return response.data
    }
    return response
  },
  (error) => {
    return Promise.reject(error.response)
  }
)

const handleRequest = (req) => {
  return new Promise((resolve, reject) => {
    req
      .then((res) => {
        const { code, msg } = res
        if (code === 200) {
          resolve(res)
        } else {
          console.error(`${code}:${msg}`)
          handleWarnMessage(res)
          reject(res)
        }
      })
      .catch((err) => {
        handleWarnMessage(ERR_MSG.default)
        reject(err)
      })
  })
}

// const handleWarnMessage = (res) => {
//   // const { msg, code, data } = res;
//   const { msg, code } = res;
//   let _message = msg || ERR_MSG[`${code}`] || ERR_MSG.default;
//   let _content = code ? `${code}:${_message}` : `${_message}`;
//   Vue.prototype.$toast({ content: _content });
// };

function handleWarnMessage(res) {
  const { msg, code, data } = res
  let _message = msg || ERR_MSG[`${code}`] || ERR_MSG.default
  if (data && data.errorLabels && data.errorLabels.length) {
    Vue.prototype.$toast({ content: data.errorLabels[0]['label'] })
  } else {
    let _content = code ? `${code}:${_message}` : `${_message}`
    Vue.prototype.$toast({ content: _content })
  }
}

const transformRequest = (data) => {
  let ret = ''
  for (let it in data) {
    ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
  }
  return ret
}

// let jsonParse = require("./jsonParse");
// const transformResponse = (data) => {
//   if (typeof data === "string") {
//     try {
//       data = jsonParse(data);
//     } catch (e) {
//       /* Ignore */
//     }
//   }
//   return data;
// };

$http.get = (url, params = {}, config = {}) => {
  let _query = {
    method: 'get',
    url: url,
    params: params
    // transformResponse: [transformResponse],
  }

  if (config) {
    //todo extend params
    if (config.header) {
      _query['headers'] = config.header
    }
  }
  return handleRequest($http(_query))
}

$http.post = (url, data = {}, config = {}) => {
  let _query = {
    method: 'post',
    url: url,
    data: data
    // transformResponse: [transformResponse],
  }
  if (config) {
    //todo extend params
    if (typeof config.serialize === 'boolean' && config.serialize) {
      _query['transformRequest'] = [transformRequest]
    }
    if (config.header) {
      _query['headers'] = config.header
    }
  }
  return handleRequest($http(_query))
}

$http.put = (url, data = {}, config = {}) => {
  let _query = {
    method: 'put',
    url: url,
    data: data
    // transformResponse: [transformResponse],
  }
  if (config) {
    //todo extend params
    if (typeof config.serialize === 'boolean' && config.serialize) {
      _query['transformRequest'] = [transformRequest]
    }
    if (config.header) {
      _query['headers'] = config.header
    }
  }
  return handleRequest($http(_query))
}

$http.delete = (url, data = {}, config = {}) => {
  let _query = {
    method: 'delete',
    url: url,
    data: data,
    // transformResponse: [transformResponse],
    transformRequest: [transformRequest],
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    }
  }
  if (config) {
    //todo extend params
    if (typeof config.serialize === 'boolean' && config.serialize) {
      _query['transformRequest'] = [transformRequest]
    }
    if (config.header) {
      _query['headers'] = config.header
    }
  }
  return handleRequest($http(_query))
}

export { $http }
