/*
 * @Author: your name
 * @Date: 2022-03-31 10:11:50
 * @LastEditTime: 2022-03-31 12:00:48
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \i18n\i18nReplace.js
 */
var fs = require('fs')
const path = require('path')

// 单步调试 路径要在src下面。。node运行是相对路径
const targetUrl = '../views/demandPool/bgConfig/prApplyConfig/config' // 文件路径，替换即可

// const targetUrl =
//   "src/views/reconciliationSettlement/reconConfig/salePriceConfig/detail"; // 文件路径，替换即可

fileDisplay(targetUrl)

// 遍历文件
/**
 * 文件遍历方法
 * @param filePath 需要遍历的文件路径
 */
function fileDisplay(filePath) {
  // 根据文件路径读取文件，返回文件列表
  fs.readdir(filePath, (err, files) => {
    if (err) {
      // console.warn('请使用build打包同步生成字典文件')
    } else {
      // 遍历读取到的文件列表
      files.forEach((filename) => {
        // 获取当前文件的绝对路径
        const filedir = path.join(filePath, filename)
        // 根据文件路径获取文件信息，返回一个fs.Stats对象
        fs.stat(filedir, (eror, stats) => {
          if (eror) {
            console.warn('获取文件stats失败')
          } else {
            const isFile = stats.isFile() // 是文件
            const isDir = stats.isDirectory() // 是文件夹
            if (isFile) {
              console.log('准备处理：', filedir)
              parseOneFile(filedir, filename)
            }
            if (isDir) {
              fileDisplay(filedir) // 递归，如果是文件夹，就继续遍历该文件夹下面的文件
            }
          }
        })
      })
    }
  })
}

function parseOneFile(targetUrl, filename) {
  const textBefore = fs.readFileSync(targetUrl).toString()
  let textAfter = ''
  textAfter = htmlAttrTextReplace(textBefore)
  textAfter = htmlStrTextReplace(textAfter)

  if (filename.split('.')[1] == 'js') {
    textAfter = jsFileTextReplace(textAfter)
    const im = `import { i18n } from "@/main.js";`
    if (textBefore !== textAfter && !textBefore.includes(im)) {
      textAfter = im + '\n' + textAfter
    }
  } else {
    textAfter = jsTextReplace(textAfter)
  }

  // 如果是单个路径，用split去获取文件名
  // let splits = targetUrl.split("/");
  // // 如果是js文件
  // if (splits[splits.length - 1].split(".")[1] == "js") {
  //   var im = `import { i18n } from "@/main.js"`;
  //   if (!text.includes(im)) {
  //     text = im + "\n" + text;
  //   }

  //   text = jsFileTextReplace();
  // } else {
  //   text = jsTextReplace();
  // }
  fileRewrite(targetUrl, textAfter)
}

// 属性文本替换，如：label="xxx" :xx-xx="$t('中文/中文，包含中英文符号?？、')"
function htmlAttrTextReplace(text) {
  return text.replace(
    /[a-z-]+="[a-zA-Z]*[\u4E00-\u9FFF]+\/*[\u4E00-\u9FFF+：:,，、？?()0-9a-zA-Z]+"|[a-z-]+="[\u4E00-\u9FFF]"/g,
    function (str) {
      const cn = str.split('"')[1]
      const prefix = str.split('"')[0].replace('=', '')

      return `:${prefix}="$t('${cn}')"`
    }
  )
}

// 模板的字符串替换，如：<div class="uploader-spu--remark">{{ $t("请拖拽图片或点击上传") }}</div>
function htmlStrTextReplace(text) {
  return text.replace(
    />[a-zA-Z]*[\u4E00-\u9FFF]+\/*[\u4E00-\u9FFF+：:,，、？?()0-9a-zA-Z]+<|>"[\u4E00-\u9FFF]"</g,
    function (str) {
      const cn = str.split('>')[1].split('<')[0]

      return `>{{ $t("${cn}") }}<`
    }
  )
}

// js内文字替换，如：headerText: i18n.t("公司法人"),注意替换规则为 : "xxx" 或 = "xxx"
function jsTextReplace(text) {
  return text.replace(
    /[:=] "[a-zA-Z]*[\u4E00-\u9FFF]+\/*[\u4E00-\u9FFF+：:,，、？?()0-9a-zA-Z]+"|[:=] "[\u4E00-\u9FFF]"/g,
    function (str) {
      const cn = str.split('"')[1]
      const prefix = str.split('"')[0].substring(0, 1)

      return `${prefix} this.$t("${cn}")`
    }
  )
}

// js文件的 文字替换
function jsFileTextReplace(text) {
  return text.replace(
    /[:=] "[a-zA-Z]*[\u4E00-\u9FFF]+\/*[\u4E00-\u9FFF+：:,，、？?()0-9a-zA-Z]+"|[:=] "[\u4E00-\u9FFF]"/g,
    function (str) {
      const cn = str.split('"')[1]
      const prefix = str.split('"')[0].substring(0, 1)

      return `${prefix} i18n.t("${cn}")`
    }
  )
}

function fileRewrite(targetFile, ctx) {
  fs.writeFile(targetFile, ctx, function (err) {
    if (err) {
      return console.error(err)
    }
  })
}
