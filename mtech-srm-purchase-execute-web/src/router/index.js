var modules = []

const routerJSFile = require.context('./modules', true, /\.js$/)
// console.log("apiJSFile", routerJSFile);

routerJSFile.keys().forEach((key) => {
  const mod = routerJSFile(key)
  let _mode = mod.default ? mod.default : mod
  modules = modules.concat(_mode)
})

// console.log("modules", modules);

const routes = [
  {
    path: '/purchase-execute',
    component: () => import('@/views/public.vue'),
    children: modules
  }
]

// console.log("routes", routes);

export default routes
