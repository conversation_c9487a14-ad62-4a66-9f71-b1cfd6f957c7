// 交货计划
export default [
  {
    meta: { keepAlive: false },
    path: 'purchase-schedule', //要货排期列表 - 采方
    name: 'purchase-schedule',
    component: () => import('@/views/deliverySchedule/purchaseSchedule/index.vue')
  },
  {
    meta: { keepAlive: false },
    path: 'purchase-schedule-kt', //要货排期列表 - 采方
    name: 'purchase-schedule-kt',
    component: () => import('@/views/deliverySchedule/purchaseScheduleKT/index.vue')
  },
  {
    meta: { keepAlive: false },
    path: 'purchase-schedule-tv', // 交货计划-TV - 采方
    name: 'purchase-schedule-tv',
    component: () => import('@/views/deliverySchedule/purchaseScheduleTV/index.vue')
  },
  {
    meta: { keepAlive: false },
    path: 'purchase-schedule-supplier', //要货排期列表 - 采方
    name: 'purchase-schedule-supplier',
    component: () => import('@/views/deliverySchedule/customSchedule/index.vue')
  },
  {
    meta: { keepAlive: false },
    path: 'purchase-schedule-supplier-kt', //要货排期列表 - 采方
    name: 'purchase-schedule-supplier-kt',
    component: () => import('@/views/deliverySchedule/customScheduleKT/index.vue')
  },
  {
    meta: { keepAlive: false },
    path: 'purchase-schedule-supplier-tv', //要货排期列表-TV - 采方
    name: 'purchase-schedule-supplier-tv',
    component: () => import('@/views/deliverySchedule/customScheduleTV/index.vue')
  },
  {
    meta: { keepAlive: false },
    path: 'schedule-history', //要货排期列表 - 历史查询
    name: 'schedule-history',
    component: () => import('@/views/deliverySchedule/purchaseScheduleHistory/index.vue')
  },
  {
    meta: { keepAlive: false },
    path: 'schedule-history-supplier', //要货排期列表 - 历史查询
    name: 'schedule-history-supplier',
    component: () => import('@/views/deliverySchedule/customScheduleHistory/index.vue')
  },
  {
    path: 'purchase-config', //交货排期配置页面
    name: 'purchase-config',
    component: () => import('@/views/deliverySchedule/systemConfig/purchaseConfig/index.vue')
  },
  {
    path: 'jit-config', //JIT计划配置
    name: 'jit-config',
    component: () => import('@/views/deliverySchedule/systemConfig/jitConfig/index.vue')
  },
  {
    path: 'jit-config-kt', //JIT计划配置 kt
    name: 'jit-config-kt',
    component: () => import('@/views/deliverySchedule/systemConfig/jitConfig/index.vue')
  },
  {
    path: 'purchase-publish-config', //交货计划发布配置
    name: 'purchase-publish-config',
    component: () => import('@/views/deliverySchedule/systemConfig/purchasePublishConfig/index.vue')
  },
  {
    path: 'purchase-plan-check', //交货计划校验
    name: 'purchase-plan-check',
    component: () => import('@/views/deliverySchedule/systemConfig/purchasePlanCheck/index.vue')
  },
  {
    path: 'send-address-config', //送货地址配置
    name: 'send-address-config',
    component: () => import('@/views/deliverySchedule/systemConfig/sendAddressConfig/index.vue')
  },
  {
    path: 'jit-supplier-allocation', // JIT叫料计划供应商分配
    name: 'jit-supplier-allocation',
    component: () => import('@/views/deliverySchedule/systemConfig/jitSupplierAllocation/index.vue')
  },
  {
    path: 'work-center-converter', //工作中心与加工商配置
    name: 'work-center-converter',
    component: () => import('@/views/deliverySchedule/systemConfig/workCenterConverter/index.vue')
  },
  {
    path: 'message-reminder-config', //消息提醒配置
    name: 'message-reminder-config',
    component: () => import('@/views/deliverySchedule/systemConfig/messageReminderConfig/index.vue')
  },
  {
    path: 'purchase-jit', // JIT 计划-采方
    name: 'purchase-jit',
    component: () => import('@/views/deliverySchedule/purchaseJit/index.vue')
  },
  {
    path: 'coordination-ageSteel-index',
    name: 'coordination-ageSteel-index',
    component: () => import('@/views/deliverySchedule/ageSteel/index.vue')
  },
  {
    path: 'purchase-jit-kt', // JIT 计划-采方 kt
    name: 'purchase-jit-kt',
    component: () => import('@/views/deliverySchedule/purchaseJit/index.vue')
  },
  {
    path: 'purchase-jit-zs', // JIT 计划-采方 zs
    name: 'purchase-jit-zs',
    component: () => import('@/views/deliverySchedule/purchaseJitZS/index.vue')
  },
  {
    path: 'purchase-jit-tv', // JIT 计划-采方 tv
    name: 'purchase-jit-tv',
    component: () => import('@/views/deliverySchedule/purchaseJitTv/index.vue')
  },
  {
    path: 'purchase-jit-supplier', // JIT 计划-供方
    name: 'purchase-jit-supplier',
    component: () => import('@/views/deliverySchedule/purchaseJitSupplier/index.vue')
  },
  {
    path: 'purchase-jit-supplier-tv', // JIT 计划-供方 tv
    name: 'purchase-jit-supplier-tv',
    component: () => import('@/views/deliverySchedule/purchaseJitSupplierTv/index.vue')
  },
  {
    path: 'rolling-delivery-date', // 滚动交期
    name: 'rolling-delivery-date',
    component: () => import('@/views/deliverySchedule/rollingDeliveryDate/index.vue')
  },
  {
    path: 'schedule-time', // 交货计划时间限制参数配置
    name: 'schedule-time',
    component: () => import('@/views/deliverySchedule/systemConfig/scheduleTimeConfig/index.vue')
  },
  {
    path: 'net-requirement', // 净需求查询
    name: 'net-requirement',
    component: () => import('@/views/deliverySchedule/netRequirement/index.vue')
  },
  {
    path: 'net-requirement-detail', // 净需求明细
    name: 'net-requirement-detail',
    component: () => import('@/views/deliverySchedule/netRequirement/detail.vue')
  },
  {
    path: 'batch-material-config', // 批次来料物料属性
    name: 'batch-material-config',
    component: () => import('@/views/deliverySchedule/systemConfig/batchMaterialConfig/index.vue')
  },
  {
    path: 'batchLncomingMaterialDetails', // 批次来料物料属性
    name: 'batchLncomingMaterialDetails',
    component: () =>
      import('@/views/deliverySchedule/systemConfig/batchLncomingMaterialDetails/index.vue')
  },
  {
    path: 'distribution-schedule', // 下发TMS交货计划
    name: 'distribution-schedule',
    component: () => import('@/views/deliverySchedule/distributionSchedule/index.vue')
  },
  {
    path: 'distribution-schedule-detail', // 下发TMS交货计划详情
    name: 'distribution-schedule-detail',
    component: () =>
      import('@/views/deliverySchedule/distributionSchedule/components/DetailPage.vue')
  },
  {
    path: 'jit-material-config', // JIT物料配置
    name: 'jit-material-config',
    component: () => import('@/views/deliverySchedule/systemConfig/jitMaterialConfig/index.vue')
  },
  {
    path: 'dispatcher-divide', // 调度员分工
    name: 'dispatcher-divide',
    component: () => import('@/views/deliverySchedule/systemConfig/dispatcherDivide/index.vue')
  },
  {
    path: 'material-delivery-type-config', // 物料送货类型配置
    name: 'material-delivery-type-config',
    component: () =>
      import('@/views/deliverySchedule/systemConfig/materialDeliveryTypeConfig/index.vue')
  },
  {
    path: 'delivery-plan-config', // 停用自动补交货计划配置
    name: 'delivery-plan-config',
    component: () => import('@/views/deliverySchedule/systemConfig/deliveryPlanConfig/index.vue')
  },
  {
    path: 'psi-config', // PSI范围设置
    name: 'psi-config',
    component: () => import('@/views/deliverySchedule/psiPages/psiConfig/index.vue')
  },
  {
    path: 'psi-delivery-schedule-pur', // 采方-PSI交货计划
    name: 'psi-delivery-schedule-pur',
    component: () =>
      import('@/views/deliverySchedule/psiPages/psiDeliverySchedule/purchase/index.vue')
  },
  {
    path: 'supplier-data-monitoring', // 采方-供方数据对接监控表
    name: 'supplier-data-monitoring',
    component: () =>
      import(
        '@/views/deliverySchedule/psiPages/psiDeliverySchedule/supplierDataMonitoring/index.vue'
      )
  },
  {
    path: 'psi-delivery-schedule-sup', // 供方-PSI交货计划
    name: 'psi-delivery-schedule-sup',
    component: () =>
      import('@/views/deliverySchedule/psiPages/psiDeliverySchedule/supplier/index.vue')
  },
  {
    path: 'psi-jit-pur', // 采方-PSI-JIT
    name: 'psi-jit-pur',
    component: () => import('@/views/deliverySchedule/psiPages/psiJit/purchase/index.vue')
  },
  {
    path: 'psi-jit-sup', // 供方-PSI-JIT
    name: 'psi-jit-sup',
    component: () => import('@/views/deliverySchedule/psiPages/psiJit/supplier/index.vue')
  }
]
