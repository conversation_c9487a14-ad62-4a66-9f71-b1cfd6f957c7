const Router = [
  // 客户对账协同（供方）
  {
    path: 'reconciliation-collaboration-summary', // 采购对账单清单-供方
    name: 'reconciliation-collaboration-summary',
    component: () => import('@/views/reconciliationCollaboration/statement/summary/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 客户对账协同查询（供方）
  {
    path: 'reconciliation-collaboration-summary-query', // 采购对账单查询清单-供方
    name: 'reconciliation-collaboration-summary-query',
    component: () => import('@/views/reconciliationCollaboration/statement/summaryQuery/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 采购对账单反馈-光伏（供方）
  {
    path: 'reconciliation-feedback-pv',
    name: 'reconciliation-feedback-pv',
    component: () => import('@/views/reconciliationCollaboration/statement/summary/gfIndex.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 采购对账单反馈详情-光伏（供方）
  {
    path: 'reconciliation-feedback-detail-pv',
    name: 'reconciliation-feedback-detail-pv',
    component: () =>
      import('@/views/reconciliationCollaboration/statement/confirmDetail/gfIndex.vue')
  },
  // 采购对账单查询-光伏 （供方）
  {
    path: 'reconciliation-query-pv',
    name: 'reconciliation-query-pv',
    component: () =>
      import('@/views/reconciliationCollaboration/statement/summaryQuery/gfIndex.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'reconciliation-confirm-detail', // 采购对账对账单确认详情-供方
    name: 'reconciliation-confirm-detail',
    component: () => import('@/views/reconciliationCollaboration/statement/confirmDetail/index.vue')
  },
  {
    path: 'reconciliation-invoice-summary', // 发票协同（供方）
    name: 'reconciliation-invoice-summary',
    component: () => import('@/views/reconciliationCollaboration/invoiceSummary/index.vue')
  },
  {
    path: 'reconciliation-upload-invoice', // 上传发票（供方）
    name: 'reconciliation-upload-invoice',
    component: () => import('@/views/reconciliationCollaboration/uploadInvoice/index.vue')
  },
  {
    path: 'reconciliation-be-reconciled', // 采购对账-待对账（供方）
    name: 'reconciliation-be-reconciled',
    component: () => import('@/views/reconciliationCollaboration/beRecon/beReconciled/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'reconciliation-create-statement', // 采购对账-创建对账单（供方）
    name: 'reconciliation-create-statement',
    component: () => import('@/views/reconciliationCollaboration/beRecon/createStatement/index.vue')
  },
  {
    path: 'contact-reconciled-supplier', // 往来对账单列表-供方
    name: 'contact-reconciled-supplier',
    component: () =>
      import('@/views/reconciliationCollaboration/contactReconciledSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-reconciled-supplier-query', // 往来对账单列表查询-供方
    name: 'contact-reconciled-supplier-query',
    component: () =>
      import('@/views/reconciliationCollaboration/contactReconciledSupplierQuery/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-detail-supplier', // 往来对账单详情-供方
    name: 'contact-detail-supplier',
    component: () => import('@/views/reconciliationCollaboration/contactDetailSupplier/index.vue')
  },
  // 销售对账
  {
    path: 'sale-recon-supplier', // 销售对账 对账单列表
    name: 'sale-recon-supplier',
    component: () => import('@/views/reconciliationCollaboration/saleReconSupplier/list/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 销售对账查询
  {
    path: 'sale-recon-supplier-query', // 销售对账查询
    name: 'sale-recon-supplier-query',
    component: () =>
      import('@/views/reconciliationCollaboration/saleReconSupplierQuery/list/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sale-detail-supplier', // 销售对账 对账单列表
    name: 'sale-detail-supplier',
    component: () =>
      import('@/views/reconciliationCollaboration/saleReconSupplier/detail/index.vue')
  },
  {
    path: 'number-recon-supplier', // 数量对账单 供方
    name: 'number-recon-supplier',
    component: () => import('@/views/reconciliationCollaboration/numberRecon/list/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'number-recon-supplier-query', // 数量对账单查询 供方
    name: 'number-recon-supplier-query',
    component: () => import('@/views/reconciliationCollaboration/numberRecon/listQuery/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'number-detail-supplier', // 数量对账单 供方
    name: 'number-detail-supplier',
    component: () => import('@/views/reconciliationCollaboration/numberRecon/detail/index.vue'),
    meta: {
      keepAlive: true
    }
  }
]

export default Router
