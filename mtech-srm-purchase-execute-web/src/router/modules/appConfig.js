const Router = [
  // 首页配置
  {
    path: 'home-page-config',
    name: 'home-page-config',
    component: () => import('@/views/appConfig/homePageConfig/index.vue')
  },
  // 库存监控配置
  {
    path: 'inventory-monitor-config',
    name: 'inventory-monitor-config',
    component: () => import('@/views/appConfig/inventoryMonitorConfig/index.vue')
  },
  // 订单监控配置
  {
    path: 'order-monitor-config',
    name: 'order-monitor-config',
    component: () => import('@/views/appConfig/orderMonitorConfig/index.vue')
  },
  // 交货计划监控配置
  {
    path: 'delivery-schedule-monitor-config',
    name: 'delivery-schedule-monitor-config',
    component: () => import('@/views/appConfig/deliveryScheduleMonitorConfig/index.vue')
  },
  // 叫料计划监控配置
  {
    path: 'jit-monitor-config',
    name: 'jit-monitor-config',
    component: () => import('@/views/appConfig/jitMonitorConfig/index.vue')
  },
  // 送货单监控配置
  {
    path: 'delivery-monitor-config',
    name: 'delivery-monitor-config',
    component: () => import('@/views/appConfig/deliveryMonitorConfig/index.vue')
  },
  // 原材料质检监控配置
  {
    path: 'quality-inspection-monitor-config',
    name: 'quality-inspection-monitor-config',
    component: () => import('@/views/appConfig/qualityInspectionMonitorConfig/index.vue')
  },
  // 物料新增需求监控配置
  {
    path: 'material-demand-monitor-config',
    name: 'material-demand-monitor-config',
    component: () => import('@/views/appConfig/newMaterialDemandMonitorConfig/index.vue')
  },
  // 产能监控配置
  {
    path: 'capacity-monitor-config',
    name: 'capacity-monitor-config',
    component: () => import('@/views/appConfig/capacityMonitorConfig/index.vue')
  }
]

export default Router
