// 供方
const Router = [
  // VMI库存调整管理
  {
    path: 'supplier-stock-administration', // 库存查询
    name: 'supplier-stock-administration',
    component: () => import('@/views/supplierCoordination/stockAdministration/stockIndex.vue')
  },
  {
    path: 'supplier-stock-index', // VMI库存调整管理
    name: 'supplier-stock-index',
    component: () => import('@/views/supplierCoordination/stockAdministration/index.vue')
  },
  {
    path: 'supplier-stock-replace', // VMI库存 供应商确认
    name: 'supplier-stock-replace',
    component: () => import('@/views/supplierCoordination/stockAdministration/stockReplace.vue')
  },
  {
    path: 'supplier-stock-allocation', // VMI库存 原材料供应商确认
    name: 'supplier-stock-allocation',
    component: () => import('@/views/supplierCoordination/stockAdministration/stockAllocation.vue')
  },
  {
    path: 'supplier-stock-build', // VMI库存 原材料供应商确认
    name: 'supplier-stock-build',
    component: () => import('@/views/supplierCoordination/stockAdministration/newlyBuild.vue')
  },
  // {
  //   path: "supplier-stock-build-detail", // 导入的详情
  //   name: "supplier-stock-build-detail",
  //   component: () =>
  //     import("@/views/supplierCoordination/stockAdministration/detail.vue"),
  // },
  // 入库管理
  {
    path: 'supplier-warehousing-index', // VMI头视图与明细视图
    name: 'supplier-warehousing-index',
    component: () => import('@/views/supplierCoordination/warehousingManagement/index.vue')
  },
  // {
  //   path: "supplier-warehousing-details", // 详情
  //   name: "supplier-warehousing-details",
  //   component: () => import("@/views/supplierCoordination/warehousingManagement/details.vue"),
  // },
  {
    path: 'supplier-warehousing-create', // 创建
    name: 'supplier-warehousing-create',
    component: () => import('@/views/supplierCoordination/warehousingManagement/create.vue')
  },
  // 领料管理
  {
    path: 'supplier-picking-index', // 头视图与明细视图 详情
    name: 'supplier-picking-index',
    component: () => import('@/views/supplierCoordination/pickingManagement/index.vue')
  },
  {
    path: 'supplier-picking-details', //  领料单详情页-供方确认
    name: 'supplier-picking-details',
    component: () => import('@/views/supplierCoordination/pickingManagement/pickingDetails.vue')
  },
  {
    path: 'supplier-picking-print', // 领料单详情页-供方确认后打印
    name: 'supplier-picking-print',
    component: () => import('@/views/supplierCoordination/pickingManagement/printIndex.vue')
  },
  // 退货管理
  {
    path: 'supplier-out-index', // VMI头视图与明细视图
    name: 'supplier-out-index',
    component: () => import('@/views/supplierCoordination/returnGoods/index.vue')
  },
  {
    path: 'supplier-out-details', // 详情
    name: 'supplier-out-details',
    component: () => import('@/views/supplierCoordination/returnGoods/details.vue')
  },
  {
    path: 'supplier-out-edit', // 供方接收
    name: 'supplier-out-edit',
    component: () => import('@/views/supplierCoordination/returnGoods/edit.vue')
  },
  // 物流公司信息维护
  {
    path: 'supplier-maintain-index', // VMI头视图与明细视图
    name: 'supplier-maintain-index',
    component: () => import('@/views/supplierCoordination/informationMaintain/index.vue')
  },
  // vmi供方库龄报表
  {
    path: 'supplier-plan-age', //vmi库存查询
    name: 'supplier-plan-age',
    component: () => import('@/views/supplierCoordination/ageSteel/index.vue')
  }
]
export default Router
