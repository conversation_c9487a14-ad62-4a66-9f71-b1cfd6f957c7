const Router = [
  //领料管理
  {
    path: 'acquisitionManagement', //头视图
    name: 'acquisitionManagement',
    component: () => import('@/views/supplierVMIManagement/acquisitionManagement/list.vue')
  },
  {
    path: 'acquisitionManagement/detail', //头视图
    name: 'acquisitionDetail',
    component: () => import('@/views/supplierVMIManagement/acquisitionManagement/detail.vue')
  },
  {
    path: 'acquisitionManagement/demo', //头视图
    name: 'acquisitionDetail',
    component: () => import('@/views/supplierVMIManagement/demo/index.vue')
  },
  {
    path: 'acquisitionManagement/demo1', //头视图
    name: 'acquisitionDetail',
    component: () => import('@/views/supplierVMIManagement/demo/detail.vue')
  },
  {
    path: 'acquisitionManagement/demo2', //头视图
    name: 'acquisitionDetail',
    component: () => import('@/views/supplierVMIManagement/demo/test.vue')
  },
  //供方退货管理
  {
    path: 'supplier-herder', //头视图
    name: 'supplier-herder',
    component: () => import('@/views/supplierVMIManagement/supplierReturn/index.vue')
  },
  {
    path: 'supplier-return-details', // 退货详情
    name: 'supplier-return-details',
    component: () => import('@/views/supplierVMIManagement/supplierReturnDetail/index.vue')
  }
]

export default Router
