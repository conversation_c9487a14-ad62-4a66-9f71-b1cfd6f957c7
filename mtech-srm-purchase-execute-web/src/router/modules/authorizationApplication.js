// 授权申请
const Router = [
  {
    path: 'sup-application-form', // 供应商授权书申请-供
    name: 'sup-application-form',
    component: () => import('@/views/authorizationApplication/applicationForm/index.vue')
  },
  {
    path: 'add-application-form', // 新增供应商授权书-供
    name: 'add-application-form',
    component: () => import('@/views/authorizationApplication/applicationForm/detail.vue')
  },
  {
    path: 'pur-application-form-manage', // 管理供应商授权书申请-采
    name: 'pur-application-form-manage',
    component: () => import('@/views/authorizationApplication/applicationFormManage/index.vue')
  },
  {
    path: 'sup-entry-application', // 临时出入申请-供
    name: 'sup-entry-application',
    component: () => import('@/views/authorizationApplication/entrySupApplication/index.vue')
  },
  {
    path: 'add-entry-application', // 新增临时出入申请-供
    name: 'add-entry-application',
    component: () => import('@/views/authorizationApplication/entrySupApplication/detail.vue')
  },
  {
    path: 'pur-entry-application', // 临时出入申请查询-采
    name: 'pur-entry-application',
    component: () => import('@/views/authorizationApplication/entryApplication/index.vue')
  },
  {
    path: 'add-pur-entry-application', // 新增临时出入申请-采
    name: 'add-pur-entry-application',
    component: () => import('@/views/authorizationApplication/entryApplication/detail.vue')
  },
  {
    path: 'pur-enter-park', // 入液晶产业园申请-采
    name: 'pur-enter-park',
    component: () => import('@/views/authorizationApplication/enterParkApplication/index.vue')
  },
  {
    path: 'sup-enter-park', // 入液晶产业园申请-供
    name: 'sup-enter-park',
    component: () => import('@/views/authorizationApplication/enterParkApplication/index.vue')
  },
  {
    path: 'sup-enter-park-detail', // 入液晶产业园申请-供
    name: 'sup-enter-park-detail',
    component: () =>
      import('@/views/authorizationApplication/enterParkApplication/detail/index.vue')
  },
  {
    path: 'pur-enter-park-detail', // 入液晶产业园申请-采
    name: 'pur-enter-park-detail',
    component: () =>
      import('@/views/authorizationApplication/enterParkApplication/detail/index.vue')
  }
]
export default Router
