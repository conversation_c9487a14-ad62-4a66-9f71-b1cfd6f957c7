// 采购需求
const Router = [
  {
    path: 'pr-apply', // 采购申请 列表
    name: 'pr-apply',
    component: () => import('@/views/demandPool/prApply/list/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'pr-apply-detail', // 采购申请 - 详情
    name: 'pr-apply-detail',
    component: () => import('@/views/demandPool/prApply/detail/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'pr-apply-logistics', // 采购申请-物流 列表
    name: 'pr-apply-logistics',
    component: () => import('@/views/demandPool/prApplyLogistics/list/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'pr-apply-logistics-detail', // 采购申请-物流 - 详情
    name: 'pr-apply-logistics-detail',
    component: () => import('@/views/demandPool/prApplyLogistics/detail/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'source-apply', // 寻源申请 列表
    name: 'source-apply',
    component: () => import('@/views/demandPool/sourceApply/list/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'source-apply-detail', // 寻源申请 - 详情
    name: 'source-apply-detail',
    component: () => import('@/views/demandPool/sourceApply/detail/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'demand-pool', // 需求池 列表
    name: 'demand-pool',
    component: () => import('@/views/demandPool/demandPool/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'order-distribute', // 后台配置 - 待转订单分配 列表
    name: 'order-distribute',
    component: () => import('@/views/demandPool/bgConfig/distribute/order/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'order-distribute-detail', // 后台配置 - 待转订单分配 - 详情
    name: 'order-distribute-detail',
    component: () => import('@/views/demandPool/bgConfig/distribute/order/detail.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'source-distribute', // 后台配置 - 待转寻源分配 列表
    name: 'source-distribute',
    component: () => import('@/views/demandPool/bgConfig/distribute/source/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'source-distribute-detail', // 后台配置 - 待转寻源分配 - 详情
    name: 'source-distribute-detail',
    component: () => import('@/views/demandPool/bgConfig/distribute/source/detail.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'business-config', // 后台配置 - 业务类型配置 列表
    name: 'business-config',
    component: () => import('@/views/demandPool/bgConfig/businessConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'business-item-config', // 后台配置 - 业务明细字段配置
    name: 'business-item-config',
    component: () => import('@/views/demandPool/bgConfig/businessItemConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'business-config-detail', // 后台配置 - 业务类型配置 - 详情
    name: 'business-config-detail',
    component: () => import('@/views/demandPool/bgConfig/businessConfig/detail.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'pr-apply-config', // 后台配置 - 采购申请配置
    name: 'pr-apply-config',
    component: () => import('@/views/demandPool/bgConfig/prApplyConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contract-create', // 合同创建
    name: 'contract-create',
    component: () => import('@/views/demandPool/contract/pages/create/index.vue')
  },
  {
    path: 'logistics-item-config', // 后台配置 - 物流类型明细字段配置
    name: 'logistics-item-config',
    component: () => import('@/views/demandPool/bgConfig/logisticsItemConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'logistics-template-config', // 后台配置 - 物流类型模板配置
    name: 'logistics-template-config',
    component: () => import('@/views/demandPool/bgConfig/logisticsTemplateConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'logistics-template-detail', // 后台配置 - 物流类型模板详情
    name: 'logistics-template-detail',
    component: () => import('@/views/demandPool/bgConfig/logisticsTemplateConfig/detail.vue'),
    meta: {
      keepAlive: true
    }
  }
]

export default Router
