/*
 * @Author: your name
 * @Date: 2021-10-13 14:27:01
 * @LastEditTime: 2021-10-25 14:59:13
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\router\modules\goodsOrigin.js
 */
const Inventory = [
  {
    path: 'purchase-inventory-list',
    name: 'purchase-inventory-list',
    component: () => import('@/views/Inventory/purchase/InventoryList/index.vue')
  },
  {
    path: 'purchase-inventory-details',
    name: 'purchase-inventory-details',
    component: () => import('@/views/Inventory/purchase/InventoryList/details/index.vue')
  },
  //列表
  {
    path: 'supplier-inventory-list',
    name: 'supplier-inventory-list',
    component: () => import('@/views/Inventory/supplier/InventoryList/index.vue')
  },
  //创建盘点单
  {
    path: 'inventory-details',
    name: 'inventory-details',
    component: () => import('@/views/Inventory/supplier/InventoryList/details/index.vue')
  },
  //盘点差异明细
  {
    path: 'supplier-inventory-Details',
    name: 'supplier-inventory-Details',
    component: () => import('@/views/Inventory/supplier/InventoryDetails/index.vue')
  },
  // 采方-PSI库存
  {
    path: 'psi-inventory-pur',
    name: 'psi-inventory-pur',
    component: () => import('@/views/Inventory/purchase/psiInventory/index.vue')
  },
  // 供方-PSI库存
  {
    path: 'psi-inventory-sup',
    name: 'psi-inventory-sup',
    component: () => import('@/views/Inventory/supplier/psiInventory/index.vue')
  },
  // 采方-PSI生产日计划
  {
    path: 'psi-daily-plan-pur',
    name: 'psi-daily-plan-pur',
    component: () => import('@/views/Inventory/purchase/psiDailyPlan/index.vue')
  },
  // 供方-PSI生产日计划
  {
    path: 'psi-daily-plan-sup',
    name: 'psi-daily-plan-sup',
    component: () => import('@/views/Inventory/supplier/psiDailyPlan/index.vue')
  },
  // 采方-PSI日生产合格率
  {
    path: 'psi-daily-pass-pur',
    name: 'psi-daily-pass-pur',
    component: () => import('@/views/Inventory/purchase/psiDailyPass/index.vue')
  },
  // 供方-PSI日生产合格率
  {
    path: 'psi-daily-pass-sup',
    name: 'psi-daily-pass-sup',
    component: () => import('@/views/Inventory/supplier/psiDailyPass/index.vue')
  }
]

export default Inventory
