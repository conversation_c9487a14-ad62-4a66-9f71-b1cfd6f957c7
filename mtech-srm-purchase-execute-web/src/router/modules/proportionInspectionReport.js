const Router = [
  {
    path: 'category-settings', // 采方 - 比例稽查报表 - 类别设置
    name: 'category-settings',
    component: () => import('@/views/proportionInspectionReport/categorySettings/index.vue')
  },
  {
    path: 'category-comparison-relationship', // 采方 - 比例稽查报表 - 类别品类对照关系
    name: 'category-comparison-relationship',
    component: () =>
      import('@/views/proportionInspectionReport/categoryComparisonRelationship/index.vue')
  },
  {
    path: 'quota-approval-list', // 采方 - 比例稽查报表 - 配额报批清单
    name: 'quota-approval-list',
    component: () => import('@/views/proportionInspectionReport/quotaApprovalList/index.vue')
  },
  {
    path: 'quota-approval-detail', // 采方 - 比例稽查报表 - 配额报批清单 - 详情
    name: 'quota-approval-detail',
    component: () => import('@/views/proportionInspectionReport/quotaApprovalDetail/index.vue')
  },
  {
    path: 'audit-report', // 采方 - 比例稽查报表
    name: 'audit-report',
    component: () => import('@/views/proportionInspectionReport/auditReport/index.vue')
  }
]

export default Router
