// 供方
const Router = [
  // VMI库存调整管理
  // {
  //   path: "supplier-stock-administration", // 库存查询
  //   name: "supplier-stock-administration",
  //   component: () =>
  //     import("@/views/supplierCoordination/stockAdministration/stockIndex.vue"),
  // },
  // {
  //   path: "supplier-stock-index", // VMI库存调整管理
  //   name: "supplier-stock-index",
  //   component: () =>
  //     import("@/views/supplierCoordination/stockAdministration/index.vue"),
  // },
  // {
  //   path: "supplier-stock-replace", // VMI库存 供应商确认
  //   name: "supplier-stock-replace",
  //   component: () =>
  //     import(
  //       "@/views/supplierCoordination/stockAdministration/stockReplace.vue"
  //     ),
  // },
  // {
  //   path: "supplier-stock-allocation", // VMI库存 原材料供应商确认
  //   name: "supplier-stock-allocation",
  //   component: () =>
  //     import(
  //       "@/views/supplierCoordination/stockAdministration/stockAllocation.vue"
  //     ),
  // },
  // {
  //   path: "supplier-stock-build", // VMI库存 原材料供应商确认
  //   name: "supplier-stock-build",
  //   component: () =>
  //     import("@/views/supplierCoordination/stockAdministration/newlyBuild.vue"),
  // },
  {
    path: 'supplier-stock-administration', // 库存查询
    name: 'supplier-stock-administration',
    component: () => import('@/views/supplierCoordination/stockAdministration/stockIndex.vue')
  },
  {
    path: 'supplier-stock-index', // VMI库存调整管理
    name: 'supplier-stock-index',
    component: () => import('@/views/supplierCoordination/stockAdministration/index.vue')
  },
  {
    path: 'supplier-stock-replace', // VMI库存 供应商确认
    name: 'supplier-stock-replace',
    component: () => import('@/views/supplierCoordination/stockAdministration/stockReplace.vue')
  },
  {
    path: 'supplier-stock-allocation', // VMI库存 原材料供应商确认
    name: 'supplier-stock-allocation',
    component: () => import('@/views/supplierCoordination/stockAdministration/stockAllocation.vue')
  },
  {
    path: 'supplier-stock-build', // VMI库存 原材料供应商确认
    name: 'supplier-stock-build',
    component: () => import('@/views/supplierCoordination/stockAdministration/newlyBuild.vue')
  },
  // 入库管理
  {
    path: 'new-supplier-warehousing-index', // VMI头视图与明细视图
    name: 'new-supplier-warehousing-index',
    component: () => import('@/views/newSupplierCoordination/warehousingManagement/index.vue')
  },
  // {
  //   path: "supplier-warehousing-details", // 详情
  //   name: "supplier-warehousing-details",
  //   component: () => import("@/views/supplierCoordination/warehousingManagement/details.vue"),
  // },
  // {
  //   path: "supplier-warehousing-create", // 创建
  //   name: "supplier-warehousing-create",
  //   component: () =>
  //     import("@/views/supplierCoordination/warehousingManagement/create.vue"),
  // },
  {
    path: 'supplier-warehousing-create', // 创建
    name: 'supplier-warehousing-create',
    component: () => import('@/views/supplierCoordination/warehousingManagement/create.vue')
  },
  {
    path: 'new-supplier-warehousing-create', // 创建 new
    name: 'new-supplier-warehousing-create',
    component: () => import('@/views/newSupplierCoordination/warehousingManagement/create.vue'),
    meta: { keepAlive: false }
  },
  // 领料管理
  // {
  //   path: "supplier-picking-index", // 头视图与明细视图 详情
  //   name: "supplier-picking-index",
  //   component: () =>
  //     import("@/views/supplierCoordination/pickingManagement/index.vue"),
  // },
  // new
  {
    path: 'new-supplier-picking-check', // 头视图与明细视图 钣金调料查询
    name: 'new-supplier-picking-check',
    component: () => import('@/views/newSupplierCoordination/pickingManagementCheck/index.vue')
  },
  {
    path: 'supplier-picking-index', // 头视图与明细视图 详情
    name: 'supplier-picking-index',
    component: () => import('@/views/supplierCoordination/pickingManagement/index.vue')
  },
  // new
  {
    path: 'new-supplier-picking-index', // 头视图与明细视图
    name: 'new-supplier-picking-index',
    component: () => import('@/views/newSupplierCoordination/pickingManagement/index.vue')
  },
  // {
  //   path: "supplier-picking-details", //  领料单详情页-供方确认
  //   name: "supplier-picking-details",
  //   component: () =>
  //     import(
  //       "@/views/supplierCoordination/pickingManagement/pickingDetails.vue"
  //     ),
  // },
  // new
  {
    path: 'new-supplier-picking-check', // 头视图与明细视图 钣金调料查询
    name: 'new-supplier-picking-check',
    component: () => import('@/views/newSupplierCoordination/pickingManagementCheck/index.vue')
  },
  {
    path: 'supplier-picking-details', //  领料单详情页-供方确认
    name: 'supplier-picking-details',
    component: () => import('@/views/supplierCoordination/pickingManagement/pickingDetails.vue')
  },
  // new
  {
    path: 'new-supplier-picking-details', //  领料单详情页-供方确认
    name: 'new-supplier-picking-details',
    component: () => import('@/views/newSupplierCoordination/pickingManagement/pickingDetails.vue')
  },
  {
    path: 'supplier-picking-print', // 领料单详情页-供方确认后打印
    name: 'supplier-picking-print',
    component: () => import('@/views/supplierCoordination/pickingManagement/printIndex.vue')
  },
  // {
  //   path: "supplier-picking-print", // 领料单详情页-供方确认后打印
  //   name: "supplier-picking-print",
  //   component: () =>
  //     import("@/views/supplierCoordination/pickingManagement/printIndex.vue"),
  // },
  // 退货管理 冲销
  {
    path: 'new-supplier-out-index', // VMI头视图与明细视图
    name: 'new-supplier-out-index',
    component: () => import('@/views/newSupplierCoordination/returnGoods/index.vue')
  },
  {
    path: 'new-supplier-out-details', // 详情
    name: 'new-supplier-out-details',
    component: () => import('@/views/newSupplierCoordination/returnGoods/details.vue')
  },
  {
    path: 'supplier-out-edit', // 供方接收
    name: 'supplier-out-edit',
    component: () => import('@/views/supplierCoordination/returnGoods/edit.vue')
  },
  // {
  //   path: "supplier-out-edit", // 供方接收
  //   name: "supplier-out-edit",
  //   component: () =>
  //     import("@/views/supplierCoordination/returnGoods/edit.vue"),
  // },
  // new 物流公司信息维护
  {
    path: 'new-supplier-maintain-index', // VMI头视图与明细视图
    name: 'new-supplier-maintain-index',
    component: () => import('@/views/newSupplierCoordination/informationMaintain/index.vue')
  },
  // new 钢材钣金协同 报表
  {
    path: 'new-supplier-report-index', //
    name: 'new-supplier-report-index',
    component: () => import('@/views/newSupplierCoordination/reportGoods/index.vue')
  },
  // 供方库存系统调用日志
  {
    path: 'warehouse-system-call',
    name: 'warehouse-system-call',
    component: () => import('@/views/newSupplierCoordination/warehousingSystemCall/index.vue')
  },
  // 供方库存系统调用日志 - 供方
  {
    path: 'warehouse-system-call-Sup',
    name: 'warehouse-system-call-Sup',
    component: () => import('@/views/newSupplierCoordination/warehousingSystemCallSup/index.vue')
  },
  // 供方库存系统调用日志 - 详情
  {
    path: 'warehouse-system-call-detail',
    name: 'warehouse-system-call-detail',
    component: () => import('@/views/newSupplierCoordination/warehousingSystemCall/detail.vue')
  },
  // 查看变更记录
  {
    path: 'supplier-stock-administration-change-Info',
    name: 'supplier-stock-administration-change-Info',
    component: () => import('@/views/supplierCoordination/stockAdministration/checkEditInfo.vue')
  }
]
export default Router
