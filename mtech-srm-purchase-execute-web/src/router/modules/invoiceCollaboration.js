const Router = [
  // 发票协同
  {
    path: 'invoice-collaboration-summary', // （采方）列表 旧页面
    name: 'invoice-collaboration-summary',
    component: () => import('@/views/invoiceCollaboration/summary/index.vue')
  },
  {
    path: 'invoice-collaboration-upload', // （采方）上传发票 旧页面
    name: 'invoice-collaboration-upload',
    component: () => import('@/views/invoiceCollaboration/upload/index.vue')
  },
  {
    path: 'invoice-summary-v2', // （采方）发票列表 新，需求变更后的页面
    name: 'invoice-summary-v2',
    component: () => import('@/views/invoiceCollaboration/summaryV2/index.vue')
  },
  {
    path: 'invoice-summary-tv', // （采方）发票列表 新，需求变更后的页面
    name: 'invoice-summary-tv',
    component: () => import('@/views/invoiceCollaboration/summaryTV/index.vue')
  },
  {
    path: 'invoice-summary', // （采方）发票列表 新，需求变更后的页面(非采)
    name: 'invoice-summary',
    component: () => import('@/views/invoiceCollaboration/summaryList/index.vue')
  },
  {
    path: 'invoice-summary-query', // （采方）发票列表查询 新
    name: 'invoice-summary-query',
    component: () => import('@/views/invoiceCollaboration/summaryQuery/index.vue')
  },
  {
    path: 'invoice-summary-query-tv', // （采方）发票列表查询-泛智屏
    name: 'invoice-summary-query-tv',
    component: () => import('@/views/invoiceCollaboration/summaryQueryTV/index.vue')
  },
  {
    path: 'invoice-clearing', // （采方）清账发票处理
    name: 'invoice-clearing',
    component: () => import('@/views/invoiceCollaboration/invoiceClearing/index.vue')
  },
  {
    path: 'invoice-detail', // （采方）发票详情页面 新，需求变更后的页面 (非采)
    name: 'invoice-detail',
    component: () => import('@/views/invoiceCollaboration/detail/index.vue')
  },
  {
    path: 'invoice-detail-v2', // （采方）发票详情页面 新，需求变更后的页面
    name: 'invoice-detail-v2',
    component: () => import('@/views/invoiceCollaboration/detailV2/index.vue')
  },

  {
    path: 'invoice-detail-tv', // （采方）发票详情页面 新，需求变更后的页面
    name: 'invoice-detail-tv',
    component: () => import('@/views/invoiceCollaboration/detailTV/index.vue')
  },
  {
    path: 'invoice-summary-supplier-v2', // （供方）发票列表 新，需求变更后的页面
    name: 'invoice-summary-supplier-v2',
    component: () => import('@/views/invoiceCollaboration/summarySupplierV2/index.vue')
  },
  {
    path: 'invoice-summary-supplier-tv', // （供方）采购发票处理(发票上载)
    name: 'invoice-summary-supplier-tv',
    component: () => import('@/views/invoiceCollaboration/summarySupplierTV/index.vue')
  },
  {
    path: 'invoice-detail-supplier', // （供方）发票详情页面 新，需求变更后的页面 (非采)
    name: 'invoice-detail-supplier',
    component: () => import('@/views/invoiceCollaboration/detailSupplier/index.vue')
  },
  {
    path: 'invoice-edit-supplier', // （供方）发票编辑页面
    name: 'invoice-edit-supplier',
    component: () => import('@/views/invoiceCollaboration/invoiceEditSupplier/index.vue')
  },
  {
    path: 'invoice-query-supplier-v2', // （供方）发票协同查询
    name: 'invoice-query-supplier-v2',
    component: () => import('@/views/invoiceCollaboration/querySupplierV2/index.vue')
  },
  {
    path: 'invoice-query-supplier-tv', // （供方）发票协同查询-泛智屏
    name: 'invoice-query-supplier-tv',
    component: () => import('@/views/invoiceCollaboration/querySupplierTV/index.vue')
  },
  {
    path: 'invoice-detail-supplier-v2', // （供方）发票详情页面 新，需求变更后的页面
    name: 'invoice-detail-supplier-v2',
    component: () => import('@/views/invoiceCollaboration/detailSupplierV2/index.vue')
  },
  {
    path: 'invoice-detail-supplier-v2-tv', // （供方）发票详情页面 新，需求变更后的页面
    name: 'invoice-detail-supplier-v2-tv',
    component: () => import('@/views/invoiceCollaboration/detailSupplierTV/index.vue')
  },
  {
    path: 'invoice-detail-clear-v2', // 采方 清账
    name: 'invoice-detail-clear-v2',
    component: () => import('@/views/invoiceCollaboration/detailClearV2/index.vue')
  },
  {
    path: 'invoice-detail-clear', // （采方）清账 (非采)
    name: 'invoice-detail-clear',
    component: () => import('@/views/invoiceCollaboration/detailClear/index.vue')
  },
  {
    path: 'invoice-edit-supplier', // （供方）发票编辑页面
    name: 'invoice-edit-supplier',
    component: () => import('@/views/invoiceCollaboration/invoiceEditSupplier/index.vue')
  },
  {
    path: 'invoice-edit-clearing', // （供方）发票编辑页面
    name: 'invoice-edit-clearing',
    component: () => import('@/views/invoiceCollaboration/invoiceClearingEdit/index.vue')
  },
  {
    path: 'invoice-edit-supplier-tv', // （供方）发票编辑页面
    name: 'invoice-edit-supplier-tv',
    component: () => import('@/views/invoiceCollaboration/invoiceEditSupplierTV/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'supplier-invoice-inquiry', // 采方 - 供方系统发票查询
    name: 'supplier-invoice-inquiry',
    component: () => import('@/views/invoiceCollaboration/supplierInvoiceInquiry/index.vue')
  },
  {
    path: 'invoice-inquiry', // 供方 - 本方系统发票查询
    name: 'invoice-inquiry',
    component: () => import('@/views/invoiceCollaboration/invoiceInquiry/index.vue')
  },
  {
    path: 'remittance-advice', // 采方 - 汇款通知
    name: 'remittance-advice',
    component: () => import('@/views/invoiceCollaboration/remittanceAdvice/index.vue')
  },
  {
    path: 'remittance-advice-supplier', // 供方 - 汇款通知
    name: 'remittance-advice-supplier',
    component: () => import('@/views/invoiceCollaboration/remittanceAdviceSupplier/index.vue')
  },
  {
    path: 'pur-invoice-detail', // 采方 - 外发发票 - 明细
    name: 'pur-invoice-detail',
    component: () => import('@/views/invoiceCollaboration/outInvoiceDetail/index.vue')
  },
  {
    path: 'sup-invoice-detail', // 供方 - 外发发票 - 明细
    name: 'sup-invoice-detail',
    component: () => import('@/views/invoiceCollaboration/outInvoiceDetail/index.vue')
  },
  {
    path: 'invoice-report-vn', // （采方）越南发票报表
    name: 'invoice-report-vn',
    component: () => import('@/views/invoiceCollaboration/vnInvoiceReport/index.vue'),
    meta: {
      keepAlive: true
    }
  }
]

export default Router
