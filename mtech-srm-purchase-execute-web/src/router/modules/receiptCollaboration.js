const Router = [
  // 收货协同
  {
    path: 'receipt-collaboration-summary', // 收货协同
    name: 'receipt-collaboration-summary',
    component: () => import('@/views/receiptCollaboration/summary/index.vue')
  },
  {
    path: 'delivery-order-detail', // 送货单详情：收货
    name: 'delivery-order-detail',
    component: () => import('@/views/receiptCollaboration/deliveryOrderDetail/index.vue')
  },
  {
    path: 'receipt-order-detail', // 收货单详情：打印、收货退回
    name: 'receipt-order-detail',
    component: () => import('@/views/receiptCollaboration/receiptOrderDetail/index.vue')
  },
  {
    path: 'receipt-cancel-order-detail', // 收货退回单详情：打印
    name: 'receipt-cancel-order-detail',
    component: () => import('@/views/receiptCollaboration/receiptCancelOrderDetail/index.vue')
  }
]

export default Router
