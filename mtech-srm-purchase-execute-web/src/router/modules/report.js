// 采购执行报表
const Router = [
  {
    path: 'report-vmiInboundRate', //入库执行报表
    name: 'report-vmiInboundRate',
    component: () => import('@/views/report/vmiInboundRate.vue')
  },
  {
    path: 'report-vmiInboundRate-month', //入库执行报表-按月汇总
    name: 'report-vmiInboundRate-month',
    component: () => import('@/views/report/vmiInboundRateMonth.vue')
  },
  {
    path: 'report-vmiInboundRate-purchase', //入库执行报表-采购分类维度
    name: 'report-vmiInboundRate-purchase',
    component: () => import('@/views/report/vmiInboundRatePurchase.vue')
  },
  {
    path: 'report-buyerStockRateReport', //备货达成率
    name: 'report-buyerStockRateReport',
    component: () => import('@/views/report/buyerStockRateReport.vue')
  },
  {
    path: 'report-failure', //回货价格失效
    name: 'report-failure',
    component: () => import('@/views/report/failure.vue')
  },
  {
    path: 'report-deliveryDateReplyRate', //交期回复及时/完整率
    name: 'report-deliveryDateReplyRate',
    component: () => import('@/views/report/deliveryDateReplyRate.vue')
  },
  {
    path: 'report-orderRate', //下单执行比例
    name: 'report-orderRate',
    component: () => import('@/views/report/orderRate.vue')
  },
  {
    path: 'report-orderRate-month', //下单执行比例报表-按月汇总
    name: 'report-orderRate-month',
    component: () => import('@/views/report/orderRateMonth.vue')
  },
  {
    path: 'report-plan', //交货计划需求满足率
    name: 'report-plan',
    component: () => import('@/views/report/plan.vue')
  },
  {
    path: 'report-buyerStockRateReport-set', //备货达成率报表配置
    name: 'report-buyerStockRateReport-set',
    component: () => import('@/views/report/buyerStockRateReportSet/index.vue')
  },
  {
    path: 'purchase/reconciliation', // 采方数量对账报表
    name: 'purchase/reconciliation', // quantityReconciliationSheet
    component: () => import('@/views/quantityReconciliationSheet/index.vue')
  },
  {
    path: 'supplier/reconciliation', // 供方数量对账报表
    name: 'supplier/reconciliation', // quantityReconciliationSheet
    component: () => import('@/views/quantityReconciliationSheet/index.vue')
  },
  {
    path: 'logistics/reconciliation', // 第三方物流数量对账报表
    name: 'logistics/reconciliation', // quantityReconciliationSheet
    component: () => import('@/views/quantityReconciliationSheet/index.vue')
  },
  {
    path: 'report/demandPlanTransverse', // 交货计划 - 横向报表
    name: 'report/demandPlanTransverse',
    component: () => import('@/views/report/demandPlanTransverse/index.vue')
  },
  {
    path: 'report/demandPlanTransverse-sup', // 交货计划 - 横向报表
    name: 'report/demandPlanTransverse-sup',
    component: () => import('@/views/report/demandPlanTransverseSup/index.vue')
  },
  {
    path: 'feedback-rate-report', // 交货计划 - 交货计划反馈率报表
    name: 'feedback-rate-report',
    component: () => import('@/views/report/feedbackRateReport/index.vue')
  },
  {
    path: 'predict-feedback-rate-report', // 预测管理 - 预测反馈率报表
    name: 'predict-feedback-rate-report',
    component: () => import('@/views/report/predictFeedbackRateReport/index.vue')
  },
  {
    path: 'purchase-jit-report', // 叫料计划送货跟踪报表
    name: 'purchase-jit-report',
    component: () => import('@/views/report/purchaseJitDeliveryTrackingReport/index.vue')
  }
]
export default Router
