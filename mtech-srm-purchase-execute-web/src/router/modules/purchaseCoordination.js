// 采方
const Router = [
  // 库存管理
  {
    path: 'coordination-stock-administration', // 库存查询
    name: 'coordination-stock-administration',
    component: () => import('@/views/purchaseCoordination/stockAdministration/stockIndex.vue')
  },
  {
    path: 'coordination-stock-record', // 调整记录
    name: 'coordination-stock-record',
    component: () => import('@/views/purchaseCoordination/stockAdministration/adjustmentRecord.vue')
  },
  {
    path: 'coordination-stock-stock', // 导入库存
    name: 'coordination-stock-stock',
    component: () => import('@/views/purchaseCoordination/stockAdministration/importStock.vue')
  },
  {
    path: 'coordination-stock-replace', // 库存替换
    name: 'coordination-stock-replace',
    component: () => import('@/views/purchaseCoordination/stockAdministration/stockReplace.vue')
  },
  {
    path: 'coordination-stock-allocation', // 物料调拨
    name: 'coordination-stock-allocation',
    component: () => import('@/views/purchaseCoordination/stockAdministration/stockAllocation.vue')
  },
  // VMI入库管理
  {
    path: 'coordination-warehousing-index', // 头视图与明细视图
    name: 'coordination-warehousing-index',
    component: () =>
      import('@/views/purchaseCoordination/warehousingManagement/warehousingIndex.vue')
  },
  {
    path: 'coordination-warehousing-establish', // 采方iqc
    name: 'coordination-warehousing-establish',
    component: () => import('@/views/purchaseCoordination/warehousingManagement/iqcEstablish.vue')
  },
  // {
  //   path: "coordination-warehousing-sentence", // iqc改判
  //   name: "coordination-warehousing-sentence",
  //   component: () => import("@/views/purchaseCoordination/warehousingManagement/iqcSentence.vue"),
  // },
  // 领料管理
  {
    path: 'coordination-picking-index', // 头视图与明细视图 详情
    name: 'coordination-picking-index',
    component: () => import('@/views/purchaseCoordination/pickingManagement/index.vue')
  },
  {
    path: 'coordination-picking-establish', //  创建页
    name: 'coordination-picking-establish',
    component: () => import('@/views/purchaseCoordination/pickingManagement/pickingDetails.vue')
  },
  // 退货管理
  {
    path: 'coordination-returnGoods-index', // 头视图与明细视图
    name: 'coordination-returnGoods-index',
    component: () => import('@/views/purchaseCoordination/returnGoods/index.vue')
  },
  {
    path: 'coordination-returnGoods-details', // 详情
    name: 'coordination-returnGoods-details',
    component: () => import('@/views/purchaseCoordination/returnGoods/details.vue')
  },
  {
    path: 'coordination-returnGoods-edit', // 编辑
    name: 'coordination-returnGoods-edit',
    component: () => import('@/views/purchaseCoordination/returnGoods/edit.vue')
  },
  // 配置管理
  {
    path: 'coordination-toConfigure-index', // 仓库管理配置
    name: 'coordination-toConfigure-index',
    component: () => import('@/views/purchaseCoordination/toConfigure/index.vue')
  },
  {
    path: 'coordination-inventory-index', // 供方库存审批管理
    name: 'coordination-inventory-index',
    component: () => import('@/views/purchaseCoordination/inventory/index.vue')
  },
  {
    path: 'coordination-toConfigure-relationship', // 仓库与供应商关系配置
    name: 'coordination-toConfigure-relationship',
    component: () => import('@/views/purchaseCoordination/toConfigure/relationshipIndex.vue')
  }
]

export default Router
