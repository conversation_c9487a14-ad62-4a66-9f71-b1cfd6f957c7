const Router = [
  /**
   * 合同打印
   *
   * 目录
   *
   *********** 采方 ***********
   * 合同打印
   * 合同打印与生成配置
   * 寄售合同打印
   */
  {
    path: 'purchase-contractPrint', // 采方--合同打印
    name: 'purchaseContractPrint',
    component: () => import('@/views/contractPrint/purchase/contractPrint/index.vue')
  },
  {
    path: 'purchase-buildPublish', // 合同打印与生成配置
    name: 'purchaseBuildPublish',
    component: () => import('@/views/contractPrint/purchase/buildPublish/index.vue')
  },
  {
    path: 'purchase-consignment', // 采方--寄售合同打印
    name: 'purchaseConsignment',
    component: () => import('@/views/contractPrint/purchase/consignment/index.vue')
  },
  /**
   * 合同打印
   *
   * 目录
   *
   *********** 供方 ***********
   * 合同打印
   * 合同预览页面
   * 寄售合同打印
   */
  {
    path: 'supplier-contractPrint', // 供方--合同打印
    name: 'supplierContractPrint',
    component: () => import('@/views/contractPrint/supplier/contractPrint/index.vue')
  },
  // {
  //   path: "supplier-previewContract", // 合同预览页面
  //   name: "supplierPreviewContract",
  //   component: () =>
  //     import("@/views/contractPrint/supplier/previewContract/index.vue"),
  // },
  {
    path: 'supplier-consignment', // 供方--寄售合同打印
    name: 'supplierConsignment',
    component: () => import('@/views/contractPrint/supplier/consignment/index.vue')
  },
  {
    path: 'supplier-HKmaterials', // 供方--港料合同打印
    name: 'supplier-HKmaterials',
    component: () => import('@/views/contractPrint/supplier/HKmaterials/index.vue')
  },
  {
    path: 'supplier-screenDemandPrint', // 供方--港料合同打印
    name: 'supplier-screenDemandPrint',
    component: () => import('@/views/contractPrint/supplier/screenDemandPrint/index.vue')
  },
  {
    path: 'purchase-screenDemandPrint', // 采方--港料合同打印
    name: 'purchase-screenDemandPrint',
    component: () => import('@/views/contractPrint/supplier/screenDemandPrint/index.vue')
  }
]

export default Router
