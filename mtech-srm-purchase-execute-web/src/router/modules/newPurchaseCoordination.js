// 采方
const Router = [
  // {
  //   path: 'coordination-stock-record', // 调整记录
  //   name: 'coordination-stock-record',
  //   component: () => import('@/views/purchaseCoordination/stockAdministration/adjustmentRecord.vue')
  // },
  // {
  //   path: 'coordination-stock-stock', // 导入库存
  //   name: 'coordination-stock-stock',
  //   component: () => import('@/views/purchaseCoordination/stockAdministration/importStock.vue')
  // },
  // {
  //   path: 'coordination-stock-replace', // 库存替换
  //   name: 'coordination-stock-replace',
  //   component: () => import('@/views/purchaseCoordination/stockAdministration/stockReplace.vue')
  // },
  // {
  //   path: 'coordination-stock-allocation', // 物料调拨
  //   name: 'coordination-stock-allocation',
  //   component: () => import('@/views/purchaseCoordination/stockAdministration/stockAllocation.vue')
  // },
  // VMI入库管理
  // {
  //   path: 'coordination-warehousing-index', // 头视图与明细视图
  //   name: 'coordination-warehousing-index',
  //   component: () =>
  //     import('@/views/purchaseCoordination/warehousingManagement/warehousingIndex.vue')
  // },
  // {
  //   path: 'coordination-warehousing-establish', // 采方iqc
  //   name: 'coordination-warehousing-establish',
  //   component: () => import('@/views/purchaseCoordination/warehousingManagement/iqcEstablish.vue')
  // },
  // {
  //   path: "coordination-warehousing-sentence", // iqc改判
  //   name: "coordination-warehousing-sentence",
  //   component: () => import("@/views/purchaseCoordination/warehousingManagement/iqcSentence.vue"),
  // },
  // 领料管理
  // {
  //   path: 'coordination-picking-index', // 头视图与明细视图 详情
  //   name: 'coordination-picking-index',
  //   component: () => import('@/views/purchaseCoordination/pickingManagement/index.vue')
  // },
  // {
  //   path: 'coordination-picking-establish', //  创建页
  //   name: 'coordination-picking-establish',
  //   component: () => import('@/views/purchaseCoordination/pickingManagement/pickingDetails.vue')
  // },
  // 退货管理
  // {
  //   path: 'coordination-returnGoods-index', // 头视图与明细视图
  //   name: 'coordination-returnGoods-index',
  //   component: () => import('@/views/purchaseCoordination/returnGoods/index.vue')
  // },
  // {
  //   path: 'coordination-returnGoods-details', // 详情
  //   name: 'coordination-returnGoods-details',
  //   component: () => import('@/views/purchaseCoordination/returnGoods/details.vue')
  // },
  // {
  //   path: 'coordination-returnGoods-edit', // 编辑
  //   name: 'coordination-returnGoods-edit',
  //   component: () => import('@/views/purchaseCoordination/returnGoods/edit.vue')
  // },
  // 配置管理
  {
    path: 'new-coordination-toConfigure-index', // 仓库管理配置
    name: 'new-coordination-toConfigure-index',
    component: () => import('@/views/purchaseNewCoordination/toConfigure/index.vue')
  },
  {
    path: 'new-coordination-toConfigure-relationship', // 仓库与供应商关系配置
    name: 'new-coordination-toConfigure-relationship',
    component: () => import('@/views/purchaseNewCoordination/toConfigure/relationshipIndex.vue')
  },
  {
    path: 'new-coordination-toConfigure-salesCope', // 销售委外应收应付金额控制方案
    name: 'new-coordination-toConfigure-salesCope',
    component: () => import('@/views/purchaseNewCoordination/salesCope/index.vue')
  },
  {
    path: 'new-coordination-toConfigure-salesCope-application', // 销售委外倒挂金额-申请单
    name: 'new-coordination-toConfigure-salesCope-application',
    component: () => import('@/views/purchaseNewCoordination/salesCopeApplication/index.vue')
  },
  {
    path: 'new-coordination-toConfigure-salesCope-manage', // 销售委外应收应付金额控制方案 - 管理员
    name: 'new-coordination-toConfigure-salesCope-manage',
    component: () => import('@/views/purchaseNewCoordination/salesCopeManage/index.vue')
  },
  // new 入库管理
  {
    path: 'new-coordination-warehousing-index', // 头视图与明细视图
    name: 'new-coordination-warehousing-index',
    component: () =>
      import('@/views/purchaseNewCoordination/warehousingManagement/warehousingIndex.vue')
  },
  // new领料管理
  {
    path: 'new-coordination-picking-index', // 头视图与明细视图
    name: 'new-coordination-picking-index',
    component: () => import('@/views/purchaseNewCoordination/pickingManagement/index.vue')
  },
  {
    path: 'new-coordination-picking-establish', //  创建页
    name: 'new-coordination-picking-establish',
    component: () => import('@/views/purchaseNewCoordination/pickingManagement/pickingDetails.vue')
  },
  // new 采方iqc
  {
    path: 'new-coordination-warehousing-establish', // 采方iqc
    name: 'new-coordination-warehousing-establish',
    component: () =>
      import('@/views/purchaseNewCoordination/warehousingManagement/iqcEstablish.vue')
  },
  // new 冲销管理 退货管理
  {
    path: 'new-coordination-returnGoods-index', // 头视图与明细视图
    name: 'new-coordination-returnGoods-index',
    component: () => import('@/views/purchaseNewCoordination/returnGoods/index.vue')
  },
  {
    path: 'new-coordination-returnGoods-details', // 头视图与明细视图
    name: 'new-coordination-returnGoods-details',
    component: () => import('@/views/purchaseNewCoordination/returnGoods/details.vue')
  },
  // new 钢材钣金协同 报表
  {
    path: 'new-coordination-report-index', //
    name: 'new-coordination-report-index',
    component: () => import('@/views/purchaseNewCoordination/reportGoods/index.vue')
  },
  // new 钢材钣金库龄报表
  {
    path: 'new-coordination-age-index', //
    name: 'new-coordination-age-index',
    component: () => import('@/views/purchaseNewCoordination/age/index.vue')
  },
  // new 钢材总数库龄报表
  {
    path: 'new-coordination-ageSteel-index', //
    name: 'new-coordination-ageSteel-index',
    component: () => import('@/views/purchaseNewCoordination/ageSteel/index.vue')
  },
  {
    path: 'new-coordination-address-config', //送货地址配置
    name: 'new-coordination-address-config',
    component: () => import('@/views/purchaseNewCoordination/sendAddressConfig/index.vue')
  }

  // new 钢材钣金  委外直退 采方 移至委外的路由
  // {
  //   path: "new-coordination-returnCargo-index-gc", //
  //   name: "new-coordination-returnCargo-index-gc",
  //   component: () =>
  //     import("@/views/outsourcingNew/purchase/purchaseReturnCargo/index.vue"),
  // },
]

export default Router
