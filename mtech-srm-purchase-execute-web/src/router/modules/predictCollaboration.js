const Router = [
  // 预测协同
  {
    path: 'predict-manage', // 预测管理-采方
    name: 'predict-manage',
    component: () => import('@/views/predictCollaboration/predictManage/index.vue')
  },
  {
    path: 'predict-manage-bd', // 预测管理-采方 new
    name: 'predict-manage-bd',
    component: () => import('@/views/predictCollaboration/predictManage/index.vue')
  },
  {
    path: 'predict-manage-kt', // 预测管理-采方 new
    name: 'predict-manage-kt',
    component: () => import('@/views/predictCollaboration/predictManage/ktIndexNew/index.vue')
  },
  {
    path: 'predict-manage-tv', // tv预测管理-采方 new
    name: 'predict-manage-tv',
    component: () => import('@/views/predictCollaboration/predictManage/tvIndexNew/index.vue')
  },
  {
    path: 'predict-history-bd', // 预测历史-采方 new
    name: 'predict-history-bd',
    component: () => import('@/views/predictCollaboration/predictHistory/index.vue')
  },
  {
    path: 'predict-history-kt', // 预测历史-采方 new
    name: 'predict-history-kt',
    component: () => import('@/views/predictCollaboration/predictHistory/index.vue')
  },
  {
    path: 'predict-history-tv', // 预测历史-采方 new
    name: 'predict-history-tv',
    component: () => import('@/views/predictCollaboration/predictHistoryTv/index.vue')
  },
  {
    path: 'predict-history-supplier-tv', // 预测历史-供方 new
    name: 'predict-history-supplier-tv',
    component: () => import('@/views/predictCollaboration/predictHistoryTv/index.vue')
  },
  {
    path: 'predict-history-detail-tv', // 预测历史-供方 new
    name: 'predict-history-detail-tv',
    component: () =>
      import('@/views/predictCollaboration/predictHistoryTv/components/historyVersion.vue')
  },
  {
    path: 'predict-manage-supplier', // 预测管理-供方
    name: 'predict-manage-supplier',
    component: () => import('@/views/predictCollaboration/predictManageSupplier/index.vue')
  },
  {
    path: 'predict-manage-supplier-bd', // 预测管理-供方 new
    name: 'predict-manage-supplier-bd',
    component: () => import('@/views/predictCollaboration/predictManageSupplier/index.vue')
  },
  {
    path: 'predict-manage-supplier-kt', // 预测管理-供方 new
    name: 'predict-manage-supplier-kt',
    component: () =>
      import('@/views/predictCollaboration/predictManageSupplier/ktIndexNew/index.vue')
  },
  {
    path: 'predict-manage-supplier-tv', // 预测管理-供方 new
    name: 'predict-manage-supplier-tv',
    component: () =>
      import('@/views/predictCollaboration/predictManageSupplier/tvIndexNew/index.vue')
  },
  {
    path: 'predict-history', // 预测历史数据列表-采方
    name: 'predict-history',
    component: () => import('@/views/predictCollaboration/predictHistory/index.vue')
  },
  {
    path: 'predict-history-supplier', // 预测历史数据列表-供方 new
    name: 'predict-history-supplier',
    component: () => import('@/views/predictCollaboration/predictHistorySupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'predict-history-supplier-bd', // 预测历史数据列表-供方 new
    name: 'predict-history-supplier-bd',
    component: () => import('@/views/predictCollaboration/predictHistorySupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'predict-history-supplier-kt', // 预测历史数据列表-供方 new
    name: 'predict-history-supplier-kt',
    component: () => import('@/views/predictCollaboration/predictHistorySupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'predict-view', // 预测历史数据详情-采方
    name: 'predict-view',
    component: () => import('@/views/predictCollaboration/predictView/index.vue')
  },
  {
    path: 'predict-view-supplier', // 预测历史数据详情-供方
    name: 'predict-view-supplier',
    component: () => import('@/views/predictCollaboration/predictViewSupplier/index.vue')
  },
  {
    path: 'predict-config', // 预测配置
    name: 'predict-config',
    component: () => import('@/views/predictCollaboration/predictConfig/index.vue')
  },
  {
    path: 'planner-assignment', // 计划员分工表
    name: 'planner-assignment',
    component: () => import('@/views/predictCollaboration/plannerAssignment/index.vue')
  },
  {
    path: 'planner-relation-schedule',
    name: 'planner-relation-schedule',
    component: () => import('@/views/predictCollaboration/plannerRelationSchedule/index.vue')
  }
]

export default Router
