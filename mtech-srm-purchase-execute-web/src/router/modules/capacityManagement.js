const Router = [
  {
    path: 'moldConfig',
    name: 'moldConfig',
    component: () => import('@/views/capacityManagement/moldConfig/index.vue')
  },
  {
    path: 'capacity-collection', // 采方 - 模具产能收集
    name: 'capacity-collection',
    component: () => import('@/views/capacityManagement/capacityCollection/index.vue')
  },
  {
    path: 'capacity-collection-detail', // 采方 - 模具产能收集 - 明细
    name: 'capacity-collection-detail',
    component: () => import('@/views/capacityManagement/capacityCollectionDetail/index.vue')
  },
  {
    path: 'capacity-reply', // 供方 - 模具产能回复
    name: 'capacity-reply',
    component: () => import('@/views/capacityManagement/capacityReply/index.vue')
  },
  {
    path: 'capacity-reply-detail', // 供方 - 模具产能回复 - 明细
    name: 'capacity-reply-detail',
    component: () => import('@/views/capacityManagement/capacityReplyDetail/index.vue')
  },
  {
    path: 'inventory-report', // 采方 - 模具库存报表
    name: 'inventory-report',
    component: () => import('@/views/capacityManagement/inventoryReport/index.vue')
  },
  {
    path: 'inventory-report-supplier', // 供方 - 模具库存报表
    name: 'inventory-report-supplier',
    component: () => import('@/views/capacityManagement/inventoryReportSupplier/index.vue')
  }
]
export default Router
