// 屏采协同
const Router = [
  {
    path: 'inventory-location-list', // 库位地点清单
    name: 'inventory-location-list',
    component: () => import('@/views/screenDemand/inventoryLocationList/index.vue')
  },
  {
    path: 'in-transit-cycle', // 在途周期
    name: 'in-transit-cycle',
    component: () => import('@/views/screenDemand/inTransitCycle/index.vue')
  },
  {
    path: 'screen-acquisition-delivery', // 屏采交货计划 - 采方
    name: 'screen-acquisition-delivery',
    component: () => import('@/views/screenDemand/deliverySchedule/index.vue')
  },
  {
    path: 'screen-acquisition-delivery-history', // 屏采交货计划历史 - 采方
    name: 'screen-acquisition-delivery-history',
    component: () => import('@/views/screenDemand/deliveryScheduleHistory/index.vue')
  },
  {
    path: 'sup-screen-acquisition-delivery', // 屏采交货计划 - 供方
    name: 'sup-screen-acquisition-delivery',
    component: () => import('@/views/screenDemand/deliveryScheduleSupplier/index.vue')
  },
  {
    path: 'sup-screen-acquisition-delivery-history', // 屏采交货计划历史 - 供方
    name: 'sup-screen-acquisition-delivery-history',
    component: () => import('@/views/screenDemand/deliveryScheduleHistory/index.vue')
  },
  {
    path: 'screen-supply-confirmation', // 屏供应确认
    name: 'screen-supply-confirmation',
    component: () => import('@/views/screenDemand/supplyConfirmation/index.vue')
  },
  {
    path: 'screen-supply-feedback', // 屏供应反馈
    name: 'screen-supply-feedback',
    component: () => import('@/views/screenDemand/supplyFeedback/index.vue')
  },
  {
    path: 'screen-predict-manage', // 屏需求预测管理
    name: 'screen-predict-manage',
    component: () => import('@/views/screenDemand/predictManage/index.vue')
  },
  {
    path: 'screen-demand-query', // 屏需求查询
    name: 'screen-demand-query',
    component: () => import('@/views/screenDemand/supplyDemandQuery/index.vue')
  },
  {
    path: 'screen-demand-query-preview', // 屏需求版本明细查询
    name: 'screen-demand-query-preview',
    component: () => import('@/views/screenDemand/supplyDemandQuery/detail/index.vue')
  },
  {
    path: 'screen-supply-demand-summary', // 屏供应商需求汇总查询
    name: 'screen-supply-demand-summary',
    component: () => import('@/views/screenDemand/supplyDemandSummary/index.vue')
  },
  {
    path: 'screen-supply-query-preview', // 屏供应商需求汇总明细查询
    name: 'screen-supply-query-preview',
    component: () => import('@/views/screenDemand/supplyDemandSummary/detail/index.vue')
  },
  {
    path: 'screen-supply-demand-summary-sup', // 屏供应商需求汇总查询
    name: 'screen-supply-demand-summary-sup',
    component: () => import('@/views/screenDemand/supplyDemandSummarySup/index.vue')
  },
  {
    path: 'screen-supply-query-preview-sup', // 屏供应商需求汇总明细查询
    name: 'screen-supply-query-preview-sup',
    component: () => import('@/views/screenDemand/supplyDemandSummarySup/detail/index.vue')
  },
  {
    path: 'screen-predict-query', // 屏预测查询 - 采方
    name: 'screen-predict-query',
    component: () => import('@/views/screenDemand/predictQuery/index.vue')
  },
  {
    path: 'sup-screen-predict-query', // 屏预测查询 - 供方
    name: 'sup-screen-predict-query',
    component: () => import('@/views/screenDemand/predictQuery/index.vue')
  },
  {
    path: 'inventory-management', // 视琨库存管理 - 采方
    name: 'inventory-management',
    component: () => import('@/views/screenDemand/inventoryManagement/index.vue')
  },
  {
    path: 'consignment-inventory', // 寄售库存 - 采方
    name: 'consignment-inventory',
    component: () => import('@/views/screenDemand/consignmentInventory/index.vue')
  },
  {
    path: 'sup-consignment-inventory', // 寄售库存 - 供方
    name: 'sup-consignment-inventory',
    component: () => import('@/views/screenDemand/supplyConsignmentInventory/index.vue')
  },
  {
    path: 'reserved-inventory-management', // 预留库存查询 - 采方
    name: 'reserved-inventory-management',
    component: () => import('@/views/screenDemand/reservedInventoryManagement/index.vue')
  }
]
export default Router
