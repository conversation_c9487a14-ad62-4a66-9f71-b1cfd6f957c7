export default [
  // 采方
  {
    path: 'drawings-release', // 图纸发布
    name: 'drawings-release',
    component: () => import('@/views/drawingTogether/release/index.vue')
  },
  {
    path: 'drawings-update', // 图纸更新
    name: 'drawings-update',
    component: () => import('@/views/drawingTogether/update/index.vue')
  },
  {
    path: 'variable-query', // 印刷品序列号变量查询
    name: 'variable-query',
    component: () => import('@/views/drawingTogether/variableQuery/index.vue')
  },
  // 供方
  {
    path: 'drawings-accept', // 图纸接受
    name: 'drawings-accept',
    component: () => import('@/views/drawingTogether/accept/index.vue')
  },
  {
    path: 'drawings-update-list', // 图纸更新待办列表
    name: 'drawings-update-list',
    component: () => import('@/views/drawingTogether/updateList/index.vue')
  },
  {
    path: 'variable-query-supplier', // 印刷品序列号变量查询
    name: 'variable-query-supplier',
    component: () => import('@/views/drawingTogether/variableQuerySupplier/index.vue')
  },
  {
    path: 'drawings-dwg', // dwg图纸
    name: 'drawings-dwg',
    component: () => import('@/views/drawingTogether/dwg/index.vue')
  },
  {
    path: 'drawings-check', // 查看图纸
    name: 'drawings-check',
    component: () => import('@/views/drawingTogether/check/index.vue')
  },
  {
    path: 'drawings-check-sup', // 供方-查看图纸
    name: 'drawings-check-sup',
    component: () => import('@/views/drawingTogether/check/index.vue')
  }
]
