const Router = [
  //第三方物流-vmi协同管理
  {
    path: 'vmiPickingList', //vmi领料单管理-物流
    name: 'vmiPickingList',
    component: () => import('@/views/thirdPartyVMICollaboration/VMIpickingList/index.vue')
  },
  {
    path: 'VMIpickingListCreate', //vmi领料单管理-批量创建送货单
    name: 'VMIpickingListCreate',
    component: () => import('@/views/thirdPartyVMICollaboration/VMIpickingList/create.vue')
  },
  {
    path: 'VMIpickingListNewCreate', //vmi领料单管理-批量创建送货单 new
    name: 'VMIpickingListNewCreate',
    component: () => import('@/views/thirdPartyVMICollaboration/VMIpickingNewList/create.vue')
  },
  {
    path: 'three-picking-details', //vmi领料单管理-批量创建送货单 - 详情
    name: 'three-picking-details',
    component: () => import('@/views/thirdPartyVMICollaboration/VMIpickingList/details.vue')
  },
  //第三方物流-vmi库存调整管理
  {
    path: 'thirdPartyStockAdministration', //vmi库存查询
    name: 'thirdPartyStockAdministration',
    component: () =>
      import('@/views/thirdPartyVMICollaboration/thirdPartyStockAdjust/stockIndex.vue')
  },
  //第三方物流-库龄报表

  {
    path: 'supply-plan-age', //vmi库存查询
    name: 'supply-plan-age',
    component: () => import('@/views/thirdPartyVMICollaboration/ageSteel/index.vue')
  },
  {
    path: 'thirdPartyStockAdjust', //vmi库存调整管理
    name: 'thirdPartyStockAdjust', //vmi库存调整管理
    component: () => import('@/views/thirdPartyVMICollaboration/thirdPartyStockAdjust/index.vue')
  },
  {
    path: 'allocation-create', //vmi库存调整管理-库存调拨单新建
    name: 'allocation-create',
    component: () =>
      import('@/views/thirdPartyVMICollaboration/thirdPartyStockAdjust/allocation-create.vue')
  },
  {
    path: 'replace-create', //vmi库存调整管理-物料替换单创建
    name: 'replace-create',
    component: () =>
      import('@/views/thirdPartyVMICollaboration/thirdPartyStockAdjust/replace-create.vue')
  },
  //第三方物流-vmi入库管理
  {
    path: 'thirdPartyImport', //vmi入库管理
    name: 'thirdPartyImport',
    component: () => import('@/views/thirdPartyVMICollaboration/thirdPartyImport/index.vue')
  },
  {
    path: 'third-party-details', //vmi入库管理详情
    name: 'third-party-details',
    component: () => import('@/views/thirdPartyVMICollaboration/thirdPartyImport/details.vue')
  },
  //第三方物流-vmi退货管理   后来改为 VNI税负管理
  {
    path: 'third-return-index', //VNI税负管理与-VNI佣金管理
    name: 'third-return-index',
    component: () => import('@/views/thirdPartyVMICollaboration/thirdReturn/index.vue')
  },
  {
    path: 'third-return-details', //vmi退货管理创建
    name: 'third-return-details',
    component: () => import('@/views/thirdPartyVMICollaboration/thirdReturn/details.vue')
  },
  // 原有的界面有  暂时不需要重新写
  // //第三方物流-送货单列表
  // {
  //   path: "thirdPartyDeliveryNote", //送货单列表
  //   name: "thirdPartyDeliveryNote",
  //   component: () => import( "@/views/thirdPartyVMICollaboration/thirdPartyDeliveryNote/index.vue"),
  // },
  // //第三方物流-送货单详情
  // {
  //   path: "thirdPartyDeliveryNoteInfo", //送货单详情
  //   name: "thirdPartyDeliveryNoteInfo",
  //   component: () => import( "@/views/thirdPartyVMICollaboration/thirdPartyDeliveryNoteInfo/index.vue"),
  // },
  // 第三方物流-vmi供货计划
  {
    path: 'supply-plan-vmi', // 供货计划-vmi 类型：菜单
    name: 'supply-plan-vmi',
    component: () => import('@/views/thirdPartyVMICollaboration/supplyPlanVmi/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: 'batch-delivery-vmi', // 批量创建送货单-vmi
    name: 'batch-delivery-vmi',
    component: () => import('@/views/thirdPartyVMICollaboration/batchDeliveryVmi/index.vue')
  }
]

export default Router
