const Router = [
  // 系统后台配置
  // {
  //   path: "business-configure", // 业务类型配置
  //   name: "business-configure",
  //   component: () => import("@/views/bgConfig/businessTypeConfigure/index.vue"),
  // },
  // {
  //   path: "business-configure-detail", // 业务类型配置详情
  //   name: "business-configure-detail",
  //   component: () =>
  //     import("@/views/bgConfig/businessTypeConfigure/configDetail.vue"),
  // },
  // {
  //   path: "policy-config", // 策略配置
  //   name: "policy-config",
  //   component: () => import("@/views/bgConfig/policyConfig/index.vue"),
  // },
  // {
  //   path: "order-consolid-config", // 策略配置- 并单策略
  //   name: "order-consolid-config",
  //   component: () =>
  //     import("@/views/bgConfig/systemConfig/orderConsolidConfig/index.vue"),
  // },
  // {
  //   path: "time-sync-config", // 策略配置- 定时同步策略
  //   name: "time-sync-config",
  //   component: () =>
  //     import("@/views/bgConfig/systemConfig/timeSyncConfig/index.vue"),
  // },
]

export default Router
