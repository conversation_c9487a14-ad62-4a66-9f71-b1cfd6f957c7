const Router = [
  {
    path: 'pur-query-statement-tv', // 查询对账单 - 泛智屏 - 采方
    name: 'pur-query-statement-tv',
    component: () => import('@/views/reconciliationSettlement/statementAccountTv/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sup-query-statement-tv', // 查询对账单 - 泛智屏 - 供方
    name: 'sup-query-statement-tv',
    component: () => import('@/views/reconciliationSettlement/statementAccountTv/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'pur-query-statement-detail-tv', // 查询对账单明细 - 泛智屏 - 采方
    name: 'pur-query-statement-detail-tv',
    component: () => import('@/views/reconciliationSettlement/statementAccountTv/detail.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'pur-recon-detail-tv', // 查询对账单销售对账、外发对账明细 - 泛智屏 - 采方
    name: 'pur-recon-detail-tv',
    component: () =>
      import('@/views/reconciliationSettlement/statementAccountTv/pages/reconDetail/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sup-query-statement-detail-tv', // 查询对账单明细 - 泛智屏 - 供方
    name: 'sup-query-statement-detail-tv',
    component: () => import('@/views/reconciliationSettlement/statementAccountTv/detail.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sup-recon-detail-tv', // 查询对账单销售对账、外发对账明细 - 泛智屏 - 供方
    name: 'sup-recon-detail-tv',
    component: () =>
      import('@/views/reconciliationSettlement/statementAccountTv/pages/reconDetail/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 对帐单结算-采方
  {
    path: 'statement-list', // 采购对账单列表-采方
    name: 'statement-list',
    component: () =>
      import('@/views/reconciliationSettlement/purchaseRecon/statement/statementList/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'statement-query', // 采购对账单查询列表-采方
    name: 'statement-query',
    component: () =>
      import(
        '@/views/reconciliationSettlement/purchaseRecon/statement/statementQueryList/index.vue'
      ),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'statement-detail', // 采购对帐单详情-订单
    name: 'statement-detail',
    component: () =>
      import('@/views/reconciliationSettlement/purchaseRecon/statement/statementDetail/index.vue')
  },
  {
    path: 'be-reconciled', // 采购待对账-采方
    name: 'be-reconciled',
    component: () =>
      import('@/views/reconciliationSettlement/purchaseRecon/beRecon/beReconciled/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'be-reconciled-detail', // 采购待对账详情-采方
    name: 'be-reconciled-detail',
    component: () =>
      import('@/views/reconciliationSettlement/purchaseRecon/beRecon/beReconciledDetail/index.vue')
  },
  {
    path: 'create-statement', // 采购对账-创建对账单-采方
    name: 'create-statement',
    component: () =>
      import('@/views/reconciliationSettlement/purchaseRecon/beRecon/createStatement/index.vue')
  },
  {
    path: 'reconciliation-type-config', // 对账类型 配置
    name: 'reconciliation-type-config',
    component: () =>
      import('@/views/reconciliationSettlement/reconConfig/reconciliationTypeConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'reconciliation-type-detail', // 对账类型 配置 - 详情
    name: 'reconciliation-type-detail',
    component: () =>
      import('@/views/reconciliationSettlement/reconConfig/reconciliationTypeConfig/detail.vue')
  },
  {
    path: 'reconciliation-history', // 对账历史查询
    name: 'reconciliation-history',
    component: () => import('@/views/reconciliationSettlement/reconciliationHistory/index.vue')
  },
  {
    path: 'consign-statement', // 寄售报表-采方
    name: 'reconciliation-history',
    component: () => import('@/views/reconciliationSettlement/consignStatement/index.vue')
  },
  {
    path: 'overdue-purchase-clear', // 采购过期清账
    name: 'overdue-purchase-clear',
    component: () =>
      import('@/views/reconciliationSettlement/overdue/overduePurchaseClear/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'overdue-clear-detail', // 采购过期清账 - 详情
    name: 'overdue-clear-detail',
    component: () =>
      import('@/views/reconciliationSettlement/purchaseRecon/beRecon/createStatement/index.vue')
  },
  // 配置管理
  {
    path: 'time-sync-config', // 配置管理 - 定时同步配置
    name: 'time-sync-config',
    component: () => import('@/views/reconciliationSettlement/reconConfig/timeSyncConfig/index.vue')
  },
  {
    path: 'automation-config', // 配置管理 - 自动化配置
    name: 'automation-config',
    component: () =>
      import('@/views/reconciliationSettlement/reconConfig/automationConfig/index.vue')
  },
  {
    path: 'sale-price-config',
    name: 'sale-price-config', // 配置管理 - 销售价格维护
    component: () =>
      import('@/views/reconciliationSettlement/reconConfig/salePriceConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'course-collect-config',
    name: 'course-collect-config', // 配置管理 - 往来对账科目汇总
    component: () =>
      import('@/views/reconciliationSettlement/reconConfig/courseCollectConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sale-price-config-kt',
    name: 'sale-price-config-kt', // 配置管理 - 销售价格维护 空调
    component: () =>
      import('@/views/reconciliationSettlement/reconConfig/salePriceConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sale-price-detail',
    name: 'sale-price-detail', // 配置管理 - 销售价格维护 - 详情
    component: () =>
      import('@/views/reconciliationSettlement/reconConfig/salePriceConfig/detail/index.vue')
  },
  // 销售对账
  {
    path: 'sale-reconciliation', // 销售待对账
    name: 'sale-reconciliation',
    component: () => import('@/views/reconciliationSettlement/saleRecon/beRecon/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sale-be-recon', // 创建 销售对账单
    name: 'sale-be-recon',
    component: () => import('@/views/reconciliationSettlement/saleRecon/detail/index.vue')
  },
  {
    path: 'sale-recon-list-detail', // 销售待对账详细界面
    name: 'sale-recon-list-detail',
    component: () => import('@/views/reconciliationSettlement/saleRecon/detail/index.vue')
  },
  {
    path: 'sale-recon-query-detail', // 销售待对账查询详细界面
    name: 'sale-recon-query-detail',
    component: () => import('@/views/reconciliationSettlement/saleRecon/detail/index.vue')
  },
  {
    path: 'sale-recon-list', // 销售对账单 列表
    name: 'sale-recon-list',
    component: () => import('@/views/reconciliationSettlement/saleRecon/reconList/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sale-recon-query', // 销售对账单 列表查询
    name: 'sale-recon-query',
    component: () => import('@/views/reconciliationSettlement/saleRecon/reconQuery/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 往来对账
  {
    path: 'contact-be-reconciled', // 往来对账-待对账-采方
    name: 'contact-be-reconciled',
    component: () =>
      import('@/views/reconciliationSettlement/contact/contactBeReconciled/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-be-reconciled-tv', // 往来对账-往来对账单通知-tv-采方
    name: 'contact-be-reconciled-tv',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactBeReconciled/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'create-contact-reconciled', // 创建往来对账单-采方
    name: 'create-contact-reconciled',
    component: () =>
      import('@/views/reconciliationSettlement/contact/createContactReconciled/index.vue')
  },
  {
    path: 'contact-reconciled-list', // 往来对账单列表-采方
    name: 'contact-reconciled-list',
    component: () =>
      import('@/views/reconciliationSettlement/contact/contactReconciledList/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-reconciled-list-tv', // 往来对账单列表-tv-采方
    name: 'contact-reconciled-list-tv',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactReconciledList/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-reconciled-detail-tv', // 往来对账单详情-tv-采方
    name: 'contact-reconciled-detail-tv',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactReconciledDetail/index.vue')
  },
  {
    path: 'contact-detail-supplier-tv', // 往来对账单详情-tv-供方
    name: 'contact-detail-supplier-tv',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactDetailSupplier/index.vue')
  },
  {
    path: 'contact-reconciled-query', // 往来对账单列表查询-采方
    name: 'contact-reconciled-query',
    component: () =>
      import('@/views/reconciliationSettlement/contact/contactReconciledQuery/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-reconciled-detail', // 往来对账单详情-采方
    name: 'contact-reconciled-detail',
    component: () =>
      import('@/views/reconciliationSettlement/contact/contactReconciledDetail/index.vue')
  },
  {
    path: 'contact-reconciled-query-detail', // 往来对账单查询详情-采方
    name: 'contact-reconciled-query-detail',
    component: () =>
      import('@/views/reconciliationSettlement/contact/contactReconciledDetail/index.vue')
  },
  {
    path: 'initial-balance-maintain', // 期初余额维护-采方
    name: 'initial-balance-maintain',
    component: () => import('@/views/reconciliationSettlement/initialBalanceMaintain/index.vue')
  },

  // 数量对账
  {
    path: 'number-reconciliation', // 数量待对账
    name: 'number-reconciliation',
    component: () => import('@/views/reconciliationSettlement/numberRecon/beRecon/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'number-be-recon', // 创建 数量待对账详情
    name: 'number-be-recon',
    component: () => import('@/views/reconciliationSettlement/numberRecon/detail/index.vue')
  },
  {
    path: 'number-recon-detail', // 数量对账单详情
    name: 'number-recon-detail',
    component: () => import('@/views/reconciliationSettlement/numberRecon/detail/index.vue')
  },
  {
    path: 'number-recon-query-detail', // 数量对账单查询详情
    name: 'number-recon-query-detail',
    component: () => import('@/views/reconciliationSettlement/numberRecon/detail/index.vue')
  },
  {
    path: 'number-recon-list', // 数量对账单 列表
    name: 'number-recon-list',
    component: () => import('@/views/reconciliationSettlement/numberRecon/reconList/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'number-recon-query', // 数量对账单 列表查询
    name: 'number-recon-query',
    component: () => import('@/views/reconciliationSettlement/numberRecon/reconQuery/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-reconciled-supplier-bd', // 往来对账单反馈-供方 // 白电与tv逻辑一致共用一套
    name: 'contact-reconciled-supplier-bd',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactReconciledSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-reconciled-supplier-tv', // 往来对账单反馈-供方
    name: 'contact-reconciled-supplier-tv',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactReconciledSupplier/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-reconciled-supplier-new', // 往来对账反馈-供方
    name: 'contact-reconciled-supplier-new',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactReconciledSupplierNew/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'contact-detail-supplier-new', // 往来对账反馈-详情-供方
    name: 'contact-detail-supplier-new',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactDetailSupplierNew/index.vue')
  },
  {
    path: 'contact-be-generate', // 往来对账-可生成往来对账单-采方
    name: 'contact-be-generate',
    component: () =>
      import('@/views/reconciliationSettlement/contactTv/contactBeGenerateSupplier/index.vue')
  },
  {
    path: 'recon-summary-detail', // 往来对账-往来对账通知-详情-采方
    name: 'recon-summary-detail',
    component: () =>
      import(
        '@/views/reconciliationSettlement/contactTv/contactBeReconciled/pages/reconSummaryDetail/index.vue'
      )
  },
  {
    path: 'vietnam-recon', // 越南对账-采方
    name: 'vietnam-recon',
    component: () => import('@/views/reconciliationSettlement/vietnamRecon/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'vietnam-recon-sup', // 越南对账-供方
    name: 'vietnam-recon-sup',
    component: () => import('@/views/reconciliationSettlement/vietnamRecon/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'pur-recon-detail-vn', // 越南日结对账-明细-采方
    name: 'pur-recon-detail-vn',
    component: () => import('@/views/reconciliationSettlement/vietnamReconDetail/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'sup-recon-detail-vn', // 越南日结对账-明细-供方
    name: 'sup-recon-detail-vn',
    component: () => import('@/views/reconciliationSettlement/vietnamReconDetail/index.vue'),
    meta: {
      keepAlive: true
    }
  }
]

export default Router
