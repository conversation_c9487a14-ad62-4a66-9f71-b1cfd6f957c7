const Router = [
  // 审厂考核模板
  {
    path: 'review-assessment-template',
    name: 'review-assessment-template',
    component: () => import('@/views/moldManagement/reviewAssessmentTemplate/index.vue')
  },
  // 审厂考核模板 - 明细
  {
    path: 'review-assessment-template-detail',
    name: 'review-assessment-template-detail',
    component: () => import('@/views/moldManagement/reviewAssessmentTemplate/detail.vue')
  },
  // 审厂管理
  {
    path: 'factory-audit-management',
    name: 'factory-audit-management',
    component: () => import('@/views/moldManagement/factoryAuditManagement/index.vue')
  },
  // 审厂管理 - 明细
  {
    path: 'factory-audit-management-detail',
    name: 'factory-audit-management-detail',
    component: () => import('@/views/moldManagement/factoryAuditManagement/detail.vue')
  },
  // 审厂管理 - 打分
  {
    path: 'factory-audit-management-score',
    name: 'factory-audit-management-score',
    component: () => import('@/views/moldManagement/factoryAuditManagement/score.vue')
  },
  // 审厂管理 - 整改确认
  {
    path: 'factory-audit-management-appeal-confirm',
    name: 'factory-audit-management-appeal-confirm',
    component: () => import('@/views/moldManagement/factoryAuditManagement/appealConfirm.vue')
  },
  // 审厂管理 - OA审批页
  {
    path: 'factory-audit-management-oa',
    name: 'factory-audit-management-oa',
    component: () => import('@/views/moldManagement/factoryAuditManagement/oaApproval.vue')
  },
  // 审厂管理 - 供方
  {
    path: 'factory-audit-management-sup',
    name: 'factory-audit-management-sup',
    component: () => import('@/views/moldManagement/factoryAuditManagementSup/index.vue')
  },
  {
    path: 'factory-audit-management-sup-score',
    name: 'factory-audit-management-sup-score',
    component: () => import('@/views/moldManagement/factoryAuditManagementSup/score.vue')
  },
  // 交货数统计
  {
    path: 'delivery-quantity-statistics',
    name: 'delivery-quantity-statistics',
    component: () => import('@/views/moldManagement/deliveryQuantityStatistics/index.vue')
  },
  // 交货数统计 - 明细
  {
    path: 'delivery-quantity-statistics-detail',
    name: 'delivery-quantity-statistics-detail',
    component: () => import('@/views/moldManagement/deliveryQuantityStatistics/detail.vue')
  },
  // 交货数统计 - 供方
  {
    path: 'delivery-quantity-statistics-sup',
    name: 'delivery-quantity-statistics-sup',
    component: () => import('@/views/moldManagement/deliveryQuantityStatisticsSup/index.vue')
  },
  // 交货数统计 - 供方 - 明细
  {
    path: 'delivery-quantity-statistics-sup-detail',
    name: 'delivery-quantity-statistics-sup-detail',
    component: () => import('@/views/moldManagement/deliveryQuantityStatisticsSup/detail.vue')
  },
  // 模具台账
  {
    path: 'mould-page',
    name: 'mould-page',
    component: () => import('@/views/moldManagement/mould/index.vue')
  },
  // 模具台账 - 明细
  {
    path: 'mould-detail',
    name: 'mould-detail',
    component: () => import('@/views/moldManagement/mould/detail.vue')
  },
  // 模具履历记录
  {
    path: 'mold-history-record',
    name: 'mold-history-record',
    component: () => import('@/views/moldManagement/moldHistoryRecord/index.vue')
  },
  // 模具工作台
  {
    path: 'mould-workbench',
    name: 'mould-workbench',
    component: () => import('@/views/moldManagement/kanban/workbench/index.vue')
  }
]

export default Router
