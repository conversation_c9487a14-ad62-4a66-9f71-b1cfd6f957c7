const Router = [
  // 对账协同报表
  {
    path: '/purchase-execute/report-supplier-account', // 供应商应付款报表
    name: 'report-supplier-account',
    component: () => import('@/views/reconciliationReport/reportSupplierAccount/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 供应商供货报表
  {
    path: '/purchase-execute/summary-supplier-amount', // 供应商供货报表
    name: 'summary-supplier-amount',
    component: () => import('@/views/reconciliationReport/summarySupplierAmount/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 供应商供货报表-物料
  {
    path: '/purchase-execute/summary-item-amount', // 供应商供货报表-物料
    name: 'summary-item-amount',
    component: () => import('@/views/reconciliationReport/summaryItemAmount/index.vue'),
    meta: {
      keepAlive: true
    }
  }
]

export default Router
