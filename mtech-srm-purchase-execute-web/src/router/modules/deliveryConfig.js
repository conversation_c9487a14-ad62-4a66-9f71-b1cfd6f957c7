const Router = [
  /**
   * 送货配置
   *
   * 目录
   *
   *********** 采方 ***********
   * 关联项目文本批次物料 类型：菜单
   * 送货提前期维护 类型：菜单
   * 送货单并单校验 类型：菜单
   * 送货关联配置 类型：菜单
   * 自动补单配置 类型：菜单
   * 无订单送货配置 类型：菜单
   * 送货黑名单配置 类型：菜单
   * 卸货点配置 类型：菜单
   * 送货单超时取消配置 类型：菜单
   * 按采购订单送货物料配置 类型：菜单
   */
  {
    path: 'related-text-config', // 关联项目文本批次物料
    name: 'related-text-config',
    component: () => import('@/views/deliveryConfig/relatedTextConfig/index.vue')
  },
  {
    path: 'delivery-advance-config', // 送货提前期维护
    name: 'delivery-config-advance',
    component: () => import('@/views/deliveryConfig/deliveryAdvanceConfig/index.vue')
  },
  {
    path: 'delivery-merge-config', // 送货单并单校验
    name: 'delivery-merge-config',
    component: () => import('@/views/deliveryConfig/deliveryMergeConfig/index.vue')
  },
  {
    path: 'delivery-association-config', // 送货关联配置
    name: 'delivery-association-config',
    component: () => import('@/views/deliveryConfig/deliveryAssociationConfig/index.vue')
  },
  {
    path: 'auto-replenish-config', // 自动补单配置
    name: 'auto-replenish-config',
    component: () => import('@/views/deliveryConfig/autoReplenishConfig/index.vue')
  },
  {
    path: 'orderless-delivery-config', // 无订单送货配置
    name: 'orderless-delivery-config',
    component: () => import('@/views/deliveryConfig/orderlessDeliveryConfig/index.vue')
  },
  {
    path: 'delivery-blacklist-config', // 送货黑名单配置
    name: 'delivery-blacklist-config',
    component: () => import('@/views/deliveryConfig/deliveryBlacklistConfig/index.vue')
  },
  {
    path: 'movement-limit-maintenance', // 机芯限制关系维护
    name: 'movement-limit-maintenance',
    component: () => import('@/views/deliveryConfig/movementLimitMaintenance/index.vue')
  },
  {
    path: 'unloading-place-config', // 卸货点配置
    name: 'unloading-place-config',
    component: () => import('@/views/deliveryConfig/unloadingPlaceConfig/index.vue')
  },
  {
    path: 'delivery-timeout-config', // 送货单超时取消配置
    name: 'delivery-timeout-config',
    component: () => import('@/views/deliveryConfig/deliveryTimeoutConfig/index.vue')
  },
  // {
  //   path: "delivery-batch-config", // 批次表配置
  //   name: "delivery-batch-config",
  //   component: () =>
  //     import("@/views/deliveryConfig/deliveryBatchConfig/index.vue"),
  // },
  {
    path: 'delivery-materiel-config', // 按采购订单送货物料配置
    name: 'delivery-materiel-config',
    component: () => import('@/views/deliveryConfig/deliveryMaterielConfig/index.vue')
  },
  {
    path: 'shipping-matchOrder-config', // 发货匹配订单配置
    name: 'shipping-matchOrder-config',
    component: () => import('@/views/deliveryConfig/shippingMatchOrderConfig/index.vue')
  },
  {
    path: 'delivery-combine-config', // 送货并单配置
    name: 'delivery-combine-config',
    component: () => import('@/views/deliveryConfig/deliveryCombineConfig/index.vue')
  },
  {
    path: 'delivery-siteInventory-config', //
    name: 'delivery-siteInventory-config',
    component: () => import('@/views/deliveryConfig/siteInventory/index.vue')
  },
  {
    path: 'delivery-Idle-material-config', // 呆料送货单配置
    name: 'delivery-Idle-material-config',
    component: () => import('@/views/deliveryConfig/deliveryIdleMaterialConfig/index.vue')
  },
  {
    path: 'delivery-limit-config', // 采方-供方库存余量限制送货配置
    name: 'delivery-limit-config',
    component: () => import('@/views/deliveryConfig/deliveryLimitConfig/index.vue')
  },
  {
    path: 'delivery-appointment-config', // 采方-送货预约强控名单
    name: 'delivery-appointment-config',
    component: () => import('@/views/deliveryConfig/deliveryAppointmentConfig/index.vue')
  }
]

export default Router
