/*
 * @Author: zhaoriyang3 <EMAIL>
 * @Date: 2022-05-04 13:26:03
 * @LastEditors: zhaoriyang3 <EMAIL>
 * @LastEditTime: 2022-06-16 17:05:21
 * @FilePath: \mtech-srm-purchase-execute-web\src\config\proxy.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = {
  // "/api/masterDataManagement": {
  //   target: "http://srm.dev.qeweb.com",
  //   changeOrigin: true,
  //   pathRewrite: {
  //     // "^/api/": "",
  //   },
  // },

  '/api': {
    target: 'http://srm-sit-gw.eads.tcl.com',
    // target: "http://srm.test.qeweb.com",
    // target: "http://tcl-srm.jingcaiyun.com/",
    // target: "https://srm-sit-main.sct.tcl.com/",
    // target: "https://srm-sit-supplier.sct.tcl.com",
    changeOrigin: true,
    pathRewrite: {
      // "^/api/": "",
    }
  }
}
