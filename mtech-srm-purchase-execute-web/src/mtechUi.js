import Vue from 'vue'

//引入样式
import './styles/index.scss'
// import "@mtech-ui/base/build/esm/bundle.css";

//mtech-ui组件
import MtDialog from '@mtech-ui/dialog'
Vue.use(MtDialog)
import MtButton from '@mtech-ui/button'
Vue.use(MtButton)
import MtForm from '@mtech-ui/form'
Vue.use(MtForm)
import MtRow from '@mtech-ui/row'
Vue.use(MtRow)
import MtCol from '@mtech-ui/col'
Vue.use(MtCol)
import MtFormItem from '@mtech-ui/form-item'
Vue.use(MtFormItem)
import MtInput from '@mtech-ui/input'
Vue.use(MtInput)
import MtDataGrid from '@mtech-ui/data-grid'
Vue.use(MtDataGrid)
import MtTreeGrid from '@mtech-ui/tree-grid'
Vue.use(MtTreeGrid)
import MtSelect from '@mtech-ui/select'
Vue.use(MtSelect)
import MtTabs from '@mtech-ui/tabs'
Vue.use(MtTabs)
import MtSideBar from '@mtech-ui/side-bar'
Vue.use(MtSideBar)
import MtSlider from '@mtech-ui/slider'
Vue.use(MtSlider)
import MtTooltip from '@mtech-ui/tooltip'
Vue.use(MtTooltip)
import MtRadio from '@mtech-ui/radio'
Vue.use(MtRadio)
import MtToast from '@mtech-ui/toast'
Vue.use(MtToast)
import MtSwitch from '@mtech-ui/switch'
Vue.use(MtSwitch)
import MtIcon from '@mtech-ui/icon'
Vue.use(MtIcon)
import MtQueryBuilder from '@mtech-ui/query-builder'
Vue.use(MtQueryBuilder)
import MtCheckbox from '@mtech-ui/checkbox'
Vue.use(MtCheckbox)
import MtCheckboxGroup from '@mtech-ui/checkbox-group'
Vue.use(MtCheckboxGroup)
import MtDateTangePicker from '@mtech-ui/date-range-picker'
Vue.use(MtDateTangePicker)
import MtDateTimePicker from '@mtech-ui/date-time-picker'
Vue.use(MtDateTimePicker)
import MtTimePicker from '@mtech-ui/time-picker'
Vue.use(MtTimePicker)
import MtTreeView from '@mtech-ui/tree-view'
Vue.use(MtTreeView)
import MtDatePicker from '@mtech-ui/date-picker'
Vue.use(MtDatePicker)
import MtInputNumber from '@mtech-ui/input-number'
Vue.use(MtInputNumber)
import MtDropDownTree from '@mtech-ui/drop-down-tree'
Vue.use(MtDropDownTree)
import MtMultiSelect from '@mtech-ui/multi-select'
Vue.use(MtMultiSelect)
import MtBadge from '@mtech-ui/badge'
Vue.use(MtBadge)
// import MtChart from '@mtech-ui/chart'
// Vue.use(MtChart)
// import MtRichTextEditor from "@mtech-ui/rich-text-editor";
// Vue.use(MtRichTextEditor);
import MtUploader from '@mtech-ui/uploader'
Vue.use(MtUploader)
import MtProgress from '@mtech-ui/progress'
Vue.use(MtProgress)
import MtPage from '@mtech-ui/page'
Vue.use(MtPage)

Vue.component('mt-col', MtCol)
Vue.component('mt-row', MtRow)
Vue.component('mt-dialog', MtDialog)
Vue.component('mt-button', MtButton)
Vue.component('mt-form', MtForm)
Vue.component('mt-form-item', MtFormItem)
Vue.component('mt-input', MtInput)
Vue.component('mt-data-grid', MtDataGrid)
Vue.component('mt-tree-grid', MtTreeGrid)
Vue.component('mt-select', MtSelect)
Vue.component('mt-tabs', MtTabs)
Vue.component('mt-side-bar', MtSideBar)
Vue.component('mt-tooltip', MtTooltip)
Vue.component('mt-radio', MtRadio)
Vue.component('mt-toast', MtToast)
Vue.component('mt-switch', MtSwitch)
Vue.component('mt-date-picker', MtDatePicker)
Vue.component('mt-query-builder', MtQueryBuilder)
Vue.component('mt-tree-view', MtTreeView)
Vue.component('mt-icon', MtIcon)
Vue.component('mt-badge', MtBadge)
// Vue.component('mt-chart', MtChart)
// Vue.component("mt-rich-text-editor", MtRichTextEditor);
Vue.component('mt-uploader', MtUploader)
Vue.component('mt-progress', MtProgress)
Vue.component('mt-page', MtPage)

//common-tree-view组件
import MtCommonTree from '@mtech/common-tree-view'
import '@mtech/common-tree-view/build/esm/bundle.css'
Vue.component('mt-common-tree', MtCommonTree)

import * as Dialog from '@/components/Dialog'
import * as Toast from '@/components/Toast'
import * as ToolTip from '@/components/Tooltip'
Vue.prototype[Dialog['NAME']] = Dialog['COMPONENT']
Vue.prototype[Toast['NAME']] = Toast['COMPONENT']
Vue.prototype[ToolTip['NAME']] = ToolTip['COMPONENT']

// 设置appCode，上线后需删除
import { utils } from '@mtech-common/utils'
utils.setAppCode('srm')

// 引入权限
import commonPermission from '@mtech/common-permission'
Vue.use(commonPermission)

// mt-template-page 组件
// import MtTemplatePage from "@mtech/common-template-page";
// import "@mtech/common-template-page/build/esm/bundle.css";
import MtTemplatePage from '@/components/template-page'
Vue.component('mt-template-page', MtTemplatePage)

import MtMicroLoading from '@/components/micro-loading'
import '@mtech/common-loading/build/esm/bundle.css'
Vue.component('mt-loading', MtMicroLoading)

import loading from '@/components/loading'
Vue.use(loading)

// 接口搜索的select封装组件
import selectFilter from '@/components/businessComponents/selectFilter/index.vue'
Vue.component('select-filter', selectFilter)

// import MtCommonUploader from '@mtech/mtech-common-uploader'
// import '@mtech/mtech-common-uploader/build/esm/bundle.css'
// Vue.use(MtCommonUploader)

import RemoteAutocomplete from '@/components/RemoteAutocomplete'
Vue.component('RemoteAutocomplete', RemoteAutocomplete)
