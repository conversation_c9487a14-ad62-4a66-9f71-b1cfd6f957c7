import Vue from 'vue'
import '@digis/component-props-state'
import App from './App.vue'
import Router from 'vue-router'
import routes from './router'
import { baseConfig } from '@mtech-common/http'
import { sso } from '@mtech-sso/single-sign-on'
import API from './apis'
import store from './store'
import '@mtech-micro-frontend/vue-cli-plugin-micro/public-path.js'
import './mtechUi'
import '@/icons'
import { setLocal } from '@mtech-ui/base'
import waves from '@/directive/waves'
import { Theme } from '@mtech-common/utils'
import getDictionary from '@/utils/dictionary'

import tableUUID from '@/utils/tableUUID/index.js'
Vue.prototype.$tableUUID = tableUUID

import md5 from 'md5'
Vue.prototype.$md5 = md5

// 国际化
import indexDB from '@digis/internationalization'
const i18n = indexDB.digisI18n(Vue, 'purchase-execute') //第二个参数是当前使用的应用code
export { tableUUID, md5, i18n }

Vue.use(Router)
Vue.use(waves)
import Bus from '@/utils/bus.js'
Vue.prototype.$bus = Bus

Vue.config.productionTip = false
Vue.prototype.$API = API
Vue.prototype.$store = store

Vue.config.devtools = true // 打开浏览器中的devtools

// 记录 路由、形成随机数，处理keep-alive带来的问题
import { unKeepRouteLeave } from '@/utils/utils'
import unKeepRoute from '@/utils/unKeepRoute'

const internationlization = localStorage.getItem('internationlization')
if (internationlization === 'zh' || internationlization === 'zh-CH') {
  setLocal('zh-CN')
}

// 获取已配置的字典
getDictionary()
let router = null
let instance = null

// 主题色
function theme(props) {
  const { entry, container } = props

  const changeTheme = new Theme()

  const themeName = localStorage.getItem('mt-layout-theme') || 'default'

  changeTheme.add(themeName, container, entry)
}

function render(props = {}) {
  const { container, routerMode = 'hash', defaultPath } = props
  router = new Router({
    routes,
    mode: routerMode,
    scrollBehavior: (to, from, position) => {
      if (position) {
        return position
      } else {
        return { x: 0, y: 0 }
      }
    }
    // base: window.__POWERED_BY_QIANKUN__ ? "/purchase-execute" : "/",
    // base: "purchase-execute",
  })

  router.beforeEach((to, from, next) => {
    // if (window.__POWERED_BY_QIANKUN__) {
    //     let _toInfo = JSON.parse(JSON.stringify(to));
    //     _toInfo.path = '/purchase-execute' + _toInfo.path;
    //     Vue.prototype.addVisitedViews(_toInfo)
    // } else {
    //     next();
    // }
    // let _toInfo = JSON.parse(JSON.stringify(to));
    // _toInfo.path = "/purchase-execute" + _toInfo.path;
    next()
  })

  router.afterEach((to, from) => {
    // console.log("main.js", to, from);
    unKeepRouteLeave(to, from, unKeepRoute)
  })

  indexDB
    .layoutCreatLanguage()
    .then(() => {
      instance = new Vue({
        router,
        store,
        i18n,
        render: (h) => h(App)
      }).$mount(container ? container.querySelector('#purchase-execute') : '#purchase-execute')
    })
    .catch(() => {
      instance = new Vue({
        router,
        store,
        i18n,
        render: (h) => h(App)
      }).$mount(container ? container.querySelector('#purchase-execute') : '#purchase-execute')
    })

  if (defaultPath) {
    router.push(defaultPath)
  }
}
Vue.mixin({
  beforeRouteLeave(to, from, next) {
    console.log('离开了', to, from, next)
    console.log('this', this.$vnode)
    // this.removeKeepAliveCacheForVueInstance.call(this);
    // if (to.name == "pr-apply") {
    //   // from.meta.keepAlive = false;
    //   // this.$store.dispatch("deleteCacheView", this.$route.name);
    // } else {
    //   from.meta.keepAlive = true;
    //   // this.$store.dispatch("addCacheView", this.$route.name);
    // }
    next()
  }
})

if (!window.__POWERED_BY_QIANKUN__) {
  sso()
  render()
  theme({
    entry: location.origin,
    container: document.head
  })
}

function storeTest(props) {
  props.onGlobalStateChange &&
    props.onGlobalStateChange(
      (value, prev) => console.log(`[onGlobalStateChange - ${props.name}]:`, value, prev),
      true
    )
  props.setGlobalState &&
    props.setGlobalState({
      ignore: props.name,
      user: {
        name: props.name
      }
    })
}

//single-spa的生命周期函数
export async function bootstrap({ fns = [] } = {}) {
  Array.isArray(fns) &&
    fns.map((i) => {
      Vue.prototype[i.name] = i
    })
  console.log('%c ', 'color: green;', 'app bootstraped')
}

export async function mount(props) {
  // 在这里挂载vue
  storeTest(props)
  render(props)
  theme(props)
  console.log('%c ', 'color: green;', 'app mount', props)
}

export async function unmount() {
  // 在这里unmount实例的vue
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
  router = null
  console.log('%c ', 'color: green;', 'app unmount')
}

baseConfig.setDefault({
  baseURL: '/api'
})

baseConfig.addNotify({
  success: function (msg) {
    // 可以使用$toast组件，msg当前版本下默认为接口response.msg
    Vue.prototype.$toast({
      content: msg,
      type: 'success'
    })
  },
  error: function (msg) {
    Vue.prototype.$toast({
      content: msg,
      type: 'error'
    })
    Vue.prototype.$store.commit('endLoading')
  }
})
