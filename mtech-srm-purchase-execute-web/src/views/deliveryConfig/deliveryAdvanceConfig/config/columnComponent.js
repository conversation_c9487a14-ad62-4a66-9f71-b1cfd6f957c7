import Vue from 'vue'
import { GroupType } from '../config/constant'

// 配置类型改变的：采购组、计划组、采购组、供应商
export const textByGroupType = (args) => {
  const { dataKey } = args

  const template = () => {
    return {
      template: Vue.component('textByGroupType', {
        template: `
        <div>
          <div v-if="isShowData()"
          >{{componentValue}}</div>
          <div v-else>-</div>
        </div>`,
        data: function () {
          return {
            data: {},
            dataKey,
            GroupType,
            componentValue: ''
          }
        },
        mounted() {
          this.componentValue = this.formatValue()
        },
        methods: {
          // 是否显示当前数据
          isShowData() {
            let isShow = false
            if (this.data.groupType == this.GroupType.type1) {
              // 配置方式 == 工厂
              isShow = false
            } else if (
              (this.dataKey === 'planGroupCode' ||
                this.dataKey === 'buyerOrgCode' ||
                this.dataKey === 'materialGroupCode') &&
              this.data.groupType == this.GroupType.type2
            ) {
              // 配置方式 == 工厂+采购组/计划组/物料组
              isShow = true
            } else if (
              (this.dataKey === 'planGroupCode' ||
                this.dataKey === 'buyerOrgCode' ||
                this.dataKey === 'materialGroupCode' ||
                this.dataKey === 'supplierCode') &&
              this.data.groupType == this.GroupType.type3
            ) {
              // 配置方式 == 工厂+采购组/计划组/物料组+供应商
              isShow = true
            }

            return isShow
          },
          // 格式化数据
          formatValue() {
            const toCodeName = (args) => {
              const { code, name } = args
              if (code && name) {
                return `${code}-${name}`
              } else if (code) {
                return code
              } else if (name) {
                return name
              }
            }
            if (dataKey === 'planGroupCode') {
              // 计划组
              const code = this.data.planGroupCode
              const name = this.data.planGroupName
              return toCodeName({ code, name })
            } else if (dataKey === 'buyerOrgCode') {
              // 采购组
              const code = this.data.buyerOrgCode
              const name = this.data.buyerOrgName
              return toCodeName({ code, name })
            } else if (dataKey === 'materialGroupCode') {
              // 物料组
              const code = this.data.materialGroupCode
              const name = this.data.materialGroupName
              return toCodeName({ code, name })
            } else if (dataKey === 'supplierCode') {
              // 供应商
              const code = this.data.supplierCode
              const name = this.data.supplierName
              return toCodeName({ code, name })
            } else {
              return this.data[dataKey]
            }
          }
        }
      })
    }
  }

  return template
}
