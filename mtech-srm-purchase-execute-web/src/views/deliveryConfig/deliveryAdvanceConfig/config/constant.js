import { i18n } from '@/main.js'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    permission: ['O_02_0507'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0508'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0509'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0510'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0511'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0512'],
    title: i18n.t('导出')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'groupType', // 类型,/1-工厂+送货类别/2-工厂+采购组/计划组/物料组+送货类别/3-工厂+采购组/计划组/物料组+供应商+送货类别
    fieldName: i18n.t('配置类型')
  },
  {
    fieldCode: 'siteCode', // 工厂 code-name
    fieldName: i18n.t('工厂')
    // siteName
    // siteId 工厂id
    // siteCode	工厂code
  },
  {
    fieldCode: 'subSiteCode', // 分工厂 code-name
    fieldName: i18n.t('分工厂')
    // siteName
    // siteId 工厂id
    // siteCode	工厂code
  },
  {
    fieldCode: 'companyCode', // 公司 code-name
    fieldName: i18n.t('公司')
    // companyName
    // companyId	公司id
    // companyCode	公司code
  },
  {
    fieldCode: 'planGroupCode', // 计划组 code-name
    fieldName: i18n.t('计划组')
    // planGroupName
    // planGroupId	计划组id
    // planGroupCode	计划组code
  },

  {
    fieldCode: 'buyerOrgCode', // 采购组 code-name
    fieldName: i18n.t('采购组')
    // buyerOrgName
    // buyerOrgId	采购组id
    // buyerOrgCode	采购组code
  },
  {
    fieldCode: 'materialGroupCode', // 物料组
    fieldName: i18n.t('物料组')
    // materialGroupName
    // materialGroupId	物料组id
    // materialGroupCode	物料组code
  },
  {
    fieldCode: 'itemCode', // 物料
    fieldName: i18n.t('物料')
  },
  {
    fieldCode: 'supplierCode', // 供应商
    fieldName: i18n.t('供应商')
    // supplierName
    // supplierId	供应商id
    // supplierCode	供应商code
  },
  {
    fieldCode: 'deliveryMethod', // 交货方式 1-按JIT,2-按交货计划
    fieldName: i18n.t('交货方式')
  },
  {
    fieldCode: 'externalItemGroupCode', //
    fieldName: i18n.t('品类')
  },
  {
    fieldCode: 'advanceDay', // 提前送货天数
    fieldName: i18n.t('提前送货天数')
  },
  {
    fieldCode: 'advanceHour', // 提前送货天数
    fieldName: i18n.t('提前送货小时')
  },
  {
    fieldCode: 'feedbackDay', // 可反馈天数
    fieldName: i18n.t('可反馈天数')
  },
  {
    fieldCode: 'dayType', // 日历类型：1-自然日历,2-工作日历
    fieldName: i18n.t('日历')
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 Options
export const ConfigStatusOptions = [
  {
    // 启用
    text: ConfigStatusConst[ConfigStatus.active],
    value: ConfigStatus.active,
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    text: ConfigStatusConst[ConfigStatus.inactive],
    value: ConfigStatus.inactive,
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]

// 日历类型：1-自然日历,2-工作日历
export const DayType = {
  type1: 1, // 自然日历
  type2: 2 // 工作日历
}
// 日历类型
export const DayTypeConst = {
  [DayType.type1]: i18n.t('自然日历'),
  [DayType.type2]: i18n.t('工作日历')
}
// 日历类型 Options
export const DayTypeOptions = [
  {
    // 自然日历
    text: DayTypeConst[DayType.type1],
    value: DayType.type1,
    cssClass: ''
  },
  {
    // 工作日历
    text: DayTypeConst[DayType.type2],
    value: DayType.type2,
    cssClass: ''
  }
]

// 交货方式：1-PO,2-按交货计划,3-按JIT
export const DeliveryMethod = {
  type1: 1, // PO
  type3: 2, // 按交货计划
  type2: 3 // 按JIT
}
// 交货方式
export const DeliveryMethodConst = {
  [DeliveryMethod.type1]: i18n.t('PO'),
  [DeliveryMethod.type2]: i18n.t('按JIT'),
  [DeliveryMethod.type3]: i18n.t('按交货计划')
}
// 交货方式 Options
export const DeliveryMethodOptions = [
  {
    // PO
    text: DeliveryMethodConst[DeliveryMethod.type1],
    value: DeliveryMethod.type1,
    cssClass: ''
  },
  {
    // 按JIT
    text: DeliveryMethodConst[DeliveryMethod.type2],
    value: DeliveryMethod.type2,
    cssClass: ''
  },
  {
    // 按交货计划
    text: DeliveryMethodConst[DeliveryMethod.type3],
    value: DeliveryMethod.type3,
    cssClass: ''
  }
]

// 配置类型 1-工厂+送货类别/2-工厂+采购组/计划组/物料组+送货类别/3-工厂+采购组/计划组/物料组+供应商+送货类别
export const GroupType = {
  // type1: 1, // 工厂
  // type2: 2, // 工厂+采购组/计划组/物料组
  // type3: 3, // 工厂+采购组/计划组/物料组+供应商
  // type1: 1,
  // type2: 2,
  // type3: 3,
  // type4: 4,
  type1: 1,
  type2: 2,
  type3: 3,
  type4: 4,
  type5: 5,
  type6: 6,
  type7: 7,
  type8: 8
}
// 配置类型
export const GroupTypeConst = {
  // [GroupType.type1]: i18n.t("工厂"),
  // [GroupType.type2]: i18n.t("工厂+采购组/计划组/物料组"),
  // [GroupType.type3]: i18n.t("工厂+采购组/计划组/物料组+供应商"),
  // [GroupType.type1]: i18n.t("品类"),
  // [GroupType.type2]: i18n.t("工厂+品类"),
  // [GroupType.type3]: i18n.t("工厂+品类+采购组/计划组/物料组"),
  // [GroupType.type4]: i18n.t("工厂+品类+采购组/计划组/物料组+供应商"),
  [GroupType.type1]: i18n.t('工厂'),
  [GroupType.type2]: i18n.t('工厂+品类'),
  [GroupType.type3]: i18n.t('工厂+采购组/计划组/物料组'),
  [GroupType.type4]: i18n.t('工厂+采购组/计划组/物料组+品类'),
  [GroupType.type5]: i18n.t('工厂+采购组/计划组/物料组+供应商'),
  [GroupType.type6]: i18n.t('工厂+品类+采购组/计划组/物料组+供应商'),
  [GroupType.type7]: i18n.t('工厂+物料+供应商'),
  [GroupType.type8]: i18n.t('工厂+分工厂+物料+供应商')
}
// 配置类型 Options
export const GroupTypeOptions = [
  // {
  //   // 工厂
  //   text: GroupTypeConst[GroupType.type1],
  //   value: GroupType.type1,
  //   cssClass: "",
  // },
  // {
  //   // 工厂+采购组/计划组/物料组
  //   text: GroupTypeConst[GroupType.type2],
  //   value: GroupType.type2,
  //   cssClass: "",
  // },
  // {
  //   // 工厂+采购组/计划组/物料组+供应商
  //   text: GroupTypeConst[GroupType.type3],
  //   value: GroupType.type3,
  //   cssClass: "",
  // },
  {
    text: GroupTypeConst[GroupType.type1],
    value: GroupType.type1,
    cssClass: ''
  },
  {
    text: GroupTypeConst[GroupType.type2],
    value: GroupType.type2,
    cssClass: ''
  },
  {
    text: GroupTypeConst[GroupType.type3],
    value: GroupType.type3,
    cssClass: ''
  },
  {
    text: GroupTypeConst[GroupType.type4],
    value: GroupType.type4,
    cssClass: ''
  },
  {
    text: GroupTypeConst[GroupType.type5],
    value: GroupType.type5,
    cssClass: ''
  },
  {
    text: GroupTypeConst[GroupType.type6],
    value: GroupType.type6,
    cssClass: ''
  },
  {
    text: GroupTypeConst[GroupType.type7],
    value: GroupType.type7,
    cssClass: ''
  },
  {
    text: GroupTypeConst[GroupType.type8],
    value: GroupType.type8,
    cssClass: ''
  }
]
