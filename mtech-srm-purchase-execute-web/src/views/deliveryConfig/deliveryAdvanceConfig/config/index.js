import {
  ConfigStatus,
  ConfigStatusOptions,
  DeliveryMethodOptions,
  GroupTypeOptions,
  DayTypeOptions
} from './constant'
import { i18n } from '@/main.js'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.allowFiltering = false
      defaultCol.ignore = true
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.cellTools = [
        {
          id: 'ConfigEdit',
          icon: 'icon_list_edit',
          permission: ['O_02_0513'],
          title: i18n.t('编辑')
        },
        {
          id: 'ConfigDelete',
          icon: 'icon_list_delete',
          permission: ['O_02_0508'],
          title: i18n.t('删除')
        }
      ]
      defaultCol.ignore = true
    } else if (col.fieldCode === 'status') {
      // 配置状态
      defaultCol.valueConverter = {
        type: 'map',
        map: ConfigStatusOptions
      }
      defaultCol.cellTools = [
        {
          id: 'ConfigInactive',
          icon: '', // icon_list_disable
          permission: ['O_02_0510'],
          title: i18n.t('停用'),
          visibleCondition: (data) => data.status == ConfigStatus.active // 启用
        },
        {
          id: 'ConfigActive',
          icon: '', // icon_list_enable
          permission: ['O_02_0509'],
          title: i18n.t('启用'),
          visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
        }
      ]
    } else if (col.fieldCode === 'deliveryMethod') {
      // 走货方式
      defaultCol.valueConverter = {
        type: 'map',
        map: DeliveryMethodOptions
      }
    } else if (col.fieldCode === 'groupType') {
      // 配置类型
      defaultCol.valueConverter = {
        type: 'map',
        map: GroupTypeOptions
      }
      defaultCol.width = '250'
    } else if (col.fieldCode === 'siteCode') {
      // 工厂
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    } else if (col.fieldCode === 'subSiteCode') {
      // 工厂
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteCode',
        secondKey: 'subSiteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.subSiteCodeBuyer,
        placeholder: i18n.t('分工厂')
      }
    } else if (col.fieldCode === 'companyCode') {
      // 公司
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany,
        placeholder: i18n.t('公司')
      }
    } else if (col.fieldCode === 'dayType') {
      // 日历类型
      defaultCol.valueConverter = {
        type: 'map',
        map: DayTypeOptions
      }
    } else if (col.fieldCode === 'planGroupCode') {
      // 计划组
      defaultCol.template = codeNameColumn({
        firstKey: 'planGroupCode',
        secondKey: 'planGroupName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'externalItemGroupCode') {
      // 计划组
      defaultCol.template = codeNameColumn({
        firstKey: 'externalItemGroupCode',
        secondKey: 'externalItemGroupName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组

      defaultCol.template = codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'materialGroupCode') {
      // 物料组
      defaultCol.template = codeNameColumn({
        firstKey: 'materialGroupCode',
        secondKey: 'materialGroupName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'itemCode') {
      // 物料
      defaultCol.template = codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.material
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier,
        placeholder: i18n.t('供应商')
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
