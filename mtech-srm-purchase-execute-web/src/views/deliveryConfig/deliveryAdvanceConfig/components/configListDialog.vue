<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="groupType" :label="$t('配置类型')" class="">
        <mt-select
          v-model="formData.groupType"
          :data-source="GroupTypeOptions"
          @change="groupTypeChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')" class="">
        <debounce-filter-select
          v-model="formData.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        v-if="formData.groupType === GroupType.type8"
        prop="subSiteCode"
        :label="$t('分工厂')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.subSiteCode"
          :request="getSubSiteList"
          :data-source="subSiteCodeOptions"
          :fields="{ text: 'theCodeName', value: 'subSiteCode' }"
          :value-template="subSiteCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="subSiteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        v-if="formData.groupType === GroupType.type7 || formData.groupType === GroupType.type8"
        prop="itemCode"
        :label="$t('物料')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.itemCode"
          :request="getitemCodeList"
          :data-source="itemCodeOptions"
          :fields="{ text: 'theCodeName', value: 'itemCode' }"
          :value-template="itemCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="itemCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        v-if="
          formData.groupType === GroupType.type2 ||
          formData.groupType === GroupType.type4 ||
          formData.groupType === GroupType.type6
        "
        prop="externalItemGroupCode"
        :label="$t('品类')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.externalItemGroupCode"
          :request="getItemGroupList"
          :data-source="ItemGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'categoryCode' }"
          :value-template="ItemGroupTemplate"
          @change="ItemGroupChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>

      <mt-form-item
        v-show="
          formData.groupType === GroupType.type2 ||
          formData.groupType === GroupType.type3 ||
          formData.groupType === GroupType.type4
        "
        prop="companyName"
        :label="$t('公司')"
        class=""
      >
        <mt-input
          v-model="formData.companyName"
          :show-clear-button="false"
          :disabled="true"
          placeholder=""
        ></mt-input>
      </mt-form-item>

      <!-- 显示：配置类型=工厂+采购组/计划组/物料组 || 工厂+采购组/计划组/物料组+供应商 -->
      <mt-form-item
        v-show="
          formData.groupType === GroupType.type3 ||
          formData.groupType === GroupType.type4 ||
          formData.groupType === GroupType.type5 ||
          formData.groupType === GroupType.type6
        "
        prop="planGroupCode"
        :label="$t('计划组')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.planGroupCode"
          :request="getPlanGroupList"
          :data-source="planGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="planGroupValueTemplate"
          @change="planGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 显示：配置类型=工厂+采购组/计划组/物料组 || 工厂+采购组/计划组/物料组+供应商 -->
      <mt-form-item
        v-show="
          formData.groupType === GroupType.type3 ||
          formData.groupType === GroupType.type4 ||
          formData.groupType === GroupType.type5 ||
          formData.groupType === GroupType.type6
        "
        prop="buyerOrgCode"
        :label="$t('采购组')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.buyerOrgCode"
          :request="getBuyerOrgList"
          :data-source="buyerOrgOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="buyerOrgValueTemplate"
          @change="buyerOrgCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 显示：配置类型=工厂+采购组/计划组/物料组 || 工厂+采购组/计划组/物料组+供应商 -->
      <mt-form-item
        v-show="
          formData.groupType === GroupType.type3 ||
          formData.groupType === GroupType.type4 ||
          formData.groupType === GroupType.type5 ||
          formData.groupType === GroupType.type6
        "
        prop="materialGroupCode"
        :label="$t('物料组')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.materialGroupCode"
          :request="postItemGroupCriteriaQuery"
          :data-source="materialGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'code' }"
          :value-template="materialGroupCodeValueTemplate"
          @change="materialGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 显示：配置类型=工厂+采购组/计划组/物料组+供应商 -->
      <mt-form-item
        v-show="
          formData.groupType === GroupType.type5 ||
          formData.groupType === GroupType.type6 ||
          formData.groupType === GroupType.type7 ||
          formData.groupType === GroupType.type8
        "
        prop="supplierCode"
        :label="$t('供应商')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.supplierCode"
          :request="getSupplier"
          :data-source="supplierOptions"
          :fields="{ text: 'theCodeName', value: 'supplierCode' }"
          :value-template="supplierCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="supplierCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="deliveryMethod" :label="$t('交货方式')" class="">
        <mt-select
          v-model="formData.deliveryMethod"
          :data-source="DeliveryMethodOptions"
          :show-clear-button="true"
          :allow-filtering="false"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        v-if="formData.deliveryMethod === DeliveryMethod.type2"
        prop="advanceHour"
        :label="$t('提前送货小时')"
      >
        <mt-input-number
          :min="0"
          :show-clear-button="true"
          :show-spin-button="false"
          @keydown.native="numberInputOnKeyDown"
          v-model="formData.advanceHour"
          :placeholder="$t('请输入')"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item v-else prop="advanceDay" :label="$t('提前送货天数')">
        <mt-input-number
          :min="0"
          :show-clear-button="true"
          :show-spin-button="false"
          max="999999999999999"
          @keydown.native="numberInputOnKeyDown"
          v-model="formData.advanceDay"
          :placeholder="$t('请输入')"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item prop="feedbackDay" :label="$t('可反馈天数')">
        <mt-input-number
          :min="0"
          :show-clear-button="true"
          :show-spin-button="false"
          max="999999999999999"
          @keydown.native="numberInputOnKeyDown"
          v-model="formData.feedbackDay"
          :placeholder="$t('请输入')"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item prop="dayType" :label="$t('日历')" class="">
        <mt-select
          v-model="formData.dayType"
          :data-source="DayTypeOptions"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  DialogActionType,
  ConfigStatus,
  DeliveryMethodOptions,
  GroupType,
  GroupTypeOptions,
  DayTypeOptions,
  DeliveryMethod
} from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { numberInputOnKeyDown } from '@/utils/utils'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { cloneDeep } from 'lodash'
export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    // 计划组
    const planGroupCodeValidator = (rule, value, callback) => {
      if (
        !value &&
        !this.formData.buyerOrgCode &&
        !this.formData.materialGroupCode &&
        (this.formData.groupType == GroupType.type3 ||
          this.formData.groupType == GroupType.type4 ||
          this.formData.groupType == GroupType.type5 ||
          this.formData.groupType == GroupType.type6)
      ) {
        callback(new Error(this.$t('计划组/采购组/物料组，必选其一')))
      } else {
        this.$refs.ruleForm.clearValidate(['planGroupCode'])
        this.$refs.ruleForm.clearValidate(['buyerOrgCode'])
        this.$refs.ruleForm.clearValidate(['materialGroupCode'])
        callback()
      }
    }
    // 采购组
    const buyerOrgCodeValidator = (rule, value, callback) => {
      if (
        !value &&
        !this.formData.planGroupCode &&
        !this.formData.materialGroupCode &&
        (this.formData.groupType == GroupType.type3 ||
          this.formData.groupType == GroupType.type4 ||
          this.formData.groupType == GroupType.type5 ||
          this.formData.groupType == GroupType.type6)
      ) {
        callback(new Error(this.$t('计划组/采购组/物料组，必选其一')))
      } else {
        this.$refs.ruleForm.clearValidate(['buyerOrgCode'])
        this.$refs.ruleForm.clearValidate(['planGroupCode'])
        this.$refs.ruleForm.clearValidate(['materialGroupCode'])
        callback()
      }
    }
    // 物料组
    const materialGroupCodeValidator = (rule, value, callback) => {
      if (
        !value &&
        !this.formData.planGroupCode &&
        !this.formData.buyerOrgCode &&
        (this.formData.groupType == GroupType.type3 ||
          this.formData.groupType == GroupType.type4 ||
          this.formData.groupType == GroupType.type5 ||
          this.formData.groupType == GroupType.type6)
      ) {
        callback(new Error(this.$t('计划组/采购组/物料组，必选其一')))
      } else {
        this.$refs.ruleForm.clearValidate(['materialGroupCode'])
        this.$refs.ruleForm.clearValidate(['planGroupCode'])
        this.$refs.ruleForm.clearValidate(['buyerOrgCode'])
        callback()
      }
    }
    // 供应商
    const supplierCodeValidator = (rule, value, callback) => {
      if (
        (!value && this.formData.groupType == GroupType.type5) ||
        this.formData.groupType == GroupType.type6
      ) {
        // 配置类型 == 工厂+采购组/计划组/物料组+供应商
        callback(new Error(this.$t('请选择供应商')))
      } else {
        this.$refs.ruleForm.clearValidate(['supplierCode'])
        callback()
      }
    }
    // 提前送货天数
    const advanceDayValidator = (rule, value, callback) => {
      //  else if (!/^\d+$/.test(value)) {
      //   callback(new Error("请输入整数"));
      // }
      if (!value) {
        callback(new Error(this.$t('请输入提前送货天数')))
      } else {
        this.$refs.ruleForm.clearValidate(['advanceDay'])
        callback()
      }
    }
    // 提前送货小时
    const advanceHourValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入提前送货小时')))
      } else {
        this.$refs.ruleForm.clearValidate('advanceHour')
        callback()
      }
    }
    // 可反馈天数
    const feedbackDayValidator = (rule, value, callback) => {
      //  else if (!/^\d+$/.test(value)) {
      //   callback(new Error("请输入整数"));
      // }
      if (!value) {
        callback(new Error(this.$t('请输入提前送货天数')))
      } else {
        this.$refs.ruleForm.clearValidate(['feedbackDay'])
        callback()
      }
    }

    return {
      DeliveryMethod,
      ItemGroupOptions: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      numberInputOnKeyDown,
      GroupTypeOptions, // 配置类型 下列选项
      GroupType, // 配置类型
      siteOptions: [], // 工厂 下列选项
      subSiteCodeOptions: [], // 分工厂 下列选项
      itemCodeOptions: [], // 物料 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂 下拉框模板
      subSiteCodeValueTemplate: codeNameColumn({
        firstKey: 'subSiteCode',
        secondKey: 'subSiteName'
      }), // 分工厂 下拉框模板
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      }), // 物料 下拉框模板
      planGroupOptions: [], // 计划组 下列选项
      planGroupValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 计划组
      ItemGroupTemplate: codeNameColumn({
        firstKey: 'categoryCode',
        secondKey: 'categoryName'
      }), //
      buyerOrgOptions: [], // 采购组 下列选项
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 采购组
      materialGroupOptions: [], // 物料组
      materialGroupCodeValueTemplate: codeNameColumn({
        firstKey: 'code',
        secondKey: 'name'
      }), // 供应商
      supplierOptions: [], // 供应商 下拉选项
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }), // 供应商
      DeliveryMethodOptions, // 走货方式 下列选项
      DayTypeOptions, // 日历类型 下列选项
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 配置类型
        groupType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        externalItemGroupCode: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 分工厂
        subSiteCode: [{ required: true, message: this.$t('请选择分工厂'), trigger: 'blur' }],
        // 物料
        itemCode: [{ required: true, message: this.$t('请选择物料'), trigger: 'blur' }],
        // 计划组
        planGroupCode: [
          {
            validator: planGroupCodeValidator,
            trigger: 'blur'
          }
        ],
        // 采购组
        buyerOrgCode: [
          {
            validator: buyerOrgCodeValidator,
            trigger: 'blur'
          }
        ],
        // 物料组
        materialGroupCode: [
          {
            validator: materialGroupCodeValidator,
            trigger: 'blur'
          }
        ],
        // 供应商
        supplierCode: [
          {
            required: true,
            validator: supplierCodeValidator,
            trigger: 'blur'
          }
        ],
        // 交货方式
        deliveryMethod: [
          {
            required: true,
            message: this.$t('请选择交货方式'),
            trigger: 'blur'
          }
        ],
        // 提前送货天数
        advanceDay: [{ required: true, validator: advanceDayValidator, trigger: 'blur' }],
        // 提前送货天数
        advanceHour: [{ required: true, validator: advanceHourValidator, trigger: 'blur' }],
        // 可反馈天数
        feedbackDay: [{ required: true, validator: feedbackDayValidator, trigger: 'blur' }],
        // 日历
        dayType: [{ required: true, message: this.$t('请选择日历'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',

        // 工厂
        siteName: '',
        siteId: '',
        siteCode: '',
        // 分工厂
        subSiteName: '',
        subSiteId: '',
        subSiteCode: '',
        // 物料
        itemName: '',
        itemId: '',
        itemCode: '',
        // 公司
        companyName: '',
        companyId: '',
        companyCode: '',
        // 物料组
        materialGroupName: '',
        materialGroupId: '',
        materialGroupCode: '',
        // 计划组
        planGroupName: '',
        planGroupId: '',
        planGroupCode: '',
        // 采购组
        buyerOrgName: '',
        buyerOrgId: '',
        buyerOrgCode: '',
        // 供应商
        supplierName: '',
        supplierId: '',
        supplierCode: '',

        groupType: '', // 配置类型
        deliveryMethod: '', // 交货方式
        externalItemGroupName: '',
        externalItemGroupCode: '',
        externalItemGroupId: '',
        advanceDay: '', // 提前送货天数
        feedbackDay: '', // 可反馈天数
        dayType: '', // 日历
        tenantId: '', // 租户id
        status: '', // 状态
        advanceHour: 0
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      console.log(entryInfo)
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = cloneDeep(this.initForm(selectData))
      console.log(this.formData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
      setTimeout(() => {
        this.$refs.dialog.ejsRef.show()
      }, 100)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息

      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',

          // 工厂
          siteName: '',
          siteId: '',
          siteCode: '',
          // 分工厂
          subSiteName: '',
          subSiteId: '',
          subSiteCode: '',
          // 物料
          itemName: '',
          itemId: '',
          itemCode: '',
          // 公司
          companyName: '',
          companyId: '',
          companyCode: '',
          // 物料组
          materialGroupName: '',
          materialGroupId: '',
          materialGroupCode: '',
          // 计划组
          planGroupName: '',
          planGroupId: '',
          planGroupCode: '',
          // 采购组
          buyerOrgName: '',
          buyerOrgId: '',
          buyerOrgCode: '',
          // 供应商
          supplierName: '',
          supplierId: '',
          supplierCode: '',

          groupType: '', // 配置类型
          deliveryMethod: '', // 交货方式
          externalItemGroupName: '',
          externalItemGroupCode: '',
          externalItemGroupId: '',
          advanceDay: '', // 提前送货天数
          feedbackDay: '', // 可反馈天数
          dayType: '', // 日历
          tenantId: tenantId, // 租户id
          status: ConfigStatus.active, // 配置状态 启用
          advanceHour: 0
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,

          // 工厂
          siteName: selectData.siteName,
          siteId: selectData.siteId,
          siteCode: selectData.siteCode,
          // 分工厂
          subSiteName: selectData.subSiteName,
          subSiteId: selectData.subSiteId,
          subSiteCode: selectData.subSiteCode,
          // 物料
          itemName: selectData.itemName,
          itemId: selectData.itemId,
          itemCode: selectData.itemCode,
          // 公司
          companyName: selectData.companyName,
          companyId: selectData.companyId,
          companyCode: selectData.companyCode,
          // 物料组
          materialGroupName: selectData.materialGroupName,
          materialGroupId: selectData.materialGroupId,
          materialGroupCode: selectData.materialGroupCode,
          // 计划组
          planGroupName: selectData.planGroupName,
          planGroupId: selectData.planGroupId,
          planGroupCode: selectData.planGroupCode,
          // 采购组
          buyerOrgName: selectData.buyerOrgName,
          buyerOrgId: selectData.buyerOrgId,
          buyerOrgCode: selectData.buyerOrgCode,
          // 供应商
          supplierName: selectData.supplierName,
          supplierId: selectData.supplierId,
          supplierCode: selectData.supplierCode,
          externalItemGroupName: selectData.externalItemGroupName,
          externalItemGroupCode: selectData.externalItemGroupCode,
          externalItemGroupId: selectData.externalItemGroupId,

          groupType: selectData.groupType, // 配置类型
          deliveryMethod: selectData.deliveryMethod, // 交货方式
          advanceDay: selectData.advanceDay, // 提前送货天数
          advanceHour: selectData.advanceHour, // 提前送货小时
          feedbackDay: selectData.feedbackDay, // 可反馈天数
          dayType: selectData.dayType, // 日历
          tenantId: tenantId, // 租户id
          status: selectData.status // 状态
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerDeliveryPeriodConfigSave()
        }
      })
    },
    handleClose() {
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息

      this.formData = {
        id: '',

        // 工厂
        siteName: '',
        siteId: '',
        siteCode: '',
        // 分工厂
        subSiteName: '',
        subSiteId: '',
        subSiteCode: '',
        // 物料
        itemName: '',
        itemId: '',
        itemCode: '',
        // 公司
        companyName: '',
        companyId: '',
        companyCode: '',
        // 物料组
        materialGroupName: '',
        materialGroupId: '',
        materialGroupCode: '',
        // 计划组
        planGroupName: '',
        planGroupId: '',
        planGroupCode: '',
        // 采购组
        buyerOrgName: '',
        buyerOrgId: '',
        buyerOrgCode: '',
        // 供应商
        supplierName: '',
        supplierId: '',
        supplierCode: '',

        groupType: '', // 配置类型
        deliveryMethod: '', // 交货方式
        externalItemGroupName: '',
        externalItemGroupCode: '',
        externalItemGroupId: '',
        advanceDay: '', // 提前送货天数
        feedbackDay: '', // 可反馈天数
        dayType: '', // 日历
        tenantId: tenantId, // 租户id
        status: ConfigStatus.active // 配置状态 启用
      }
      this.$refs.dialog.ejsRef.hide()
    },
    // 配置组合方式 change
    groupTypeChange(e) {
      if (e.value == GroupType.type1) {
        // 配置组合方式 工厂
        this.$refs.ruleForm.clearValidate(['buyerOrgCode']) // 采购组
        this.$refs.ruleForm.clearValidate(['planGroupCode']) // 计划组
        this.$refs.ruleForm.clearValidate(['externalItemGroupCode']) //品类

        this.$refs.ruleForm.clearValidate(['materialGroupCode']) // 物料组
        this.$refs.ruleForm.clearValidate(['supplierCode']) // 供应商
        this.formData.externalItemGroupId = ''
        this.formData.externalItemGroupCode = ''
        this.formData.externalItemGroupName = ''
        // 采购组
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
        // 计划组
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
        // 物料组
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
        // 供应商
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      }
      if (e.value == GroupType.type2) {
        // 配置组合方式 工厂
        this.$refs.ruleForm.clearValidate(['buyerOrgCode']) // 采购组
        this.$refs.ruleForm.clearValidate(['planGroupCode']) // 计划组
        this.$refs.ruleForm.clearValidate(['materialGroupCode']) // 物料组
        this.$refs.ruleForm.clearValidate(['supplierCode']) // 供应商
        // 采购组
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
        // 计划组
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
        // 物料组
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
        // 供应商
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      } else if (e.value == GroupType.type3) {
        // 配置组合方式 工厂+采购组/计划组/物料组
        // 供应商
        this.$refs.ruleForm.clearValidate(['externalItemGroupCode']) //品类
        this.$refs.ruleForm.clearValidate(['supplierCode']) // 供应商
        this.formData.externalItemGroupId = ''
        this.formData.externalItemGroupCode = ''
        this.formData.externalItemGroupName = ''
        this.formData.supplierId = ''

        this.formData.supplierCode = ''
        this.formData.supplierName = ''
        this.$refs.ruleForm.clearValidate(['supplierCode']) // 供应商
      } else if (e.value == GroupType.type4) {
        this.$refs.ruleForm.clearValidate(['supplierCode']) // 供应商
        this.formData.supplierId = ''

        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      } else if (e.value == GroupType.type5) {
        this.$refs.ruleForm.clearValidate(['externalItemGroupCode']) // 供应商
        this.formData.externalItemGroupId = ''
        this.formData.externalItemGroupCode = ''
        this.formData.externalItemGroupName = ''
        // } else if (e.value == GroupType.type6) {
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // 工厂
        this.formData.siteId = itemData.id
        this.formData.siteCode = itemData.siteCode
        this.formData.siteName = itemData.siteName
        // 公司
        this.formData.companyId = itemData.parentId
        this.formData.companyCode = itemData.parentCode
        this.formData.companyName = itemData.parentName
      } else {
        // 工厂
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
        // 公司
        this.formData.companyId = ''
        this.formData.companyCode = ''
        this.formData.companyName = ''
      }
    },
    // 分工厂 change
    subSiteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // 分工厂
        this.formData.subSiteId = itemData.id
        this.formData.subSiteCode = itemData.subSiteCode
        this.formData.subSiteName = itemData.subSiteName
      } else {
        // 分工厂
        this.formData.subSiteId = ''
        this.formData.subSiteCode = ''
        this.formData.subSiteName = ''
      }
    },
    // 物料 change
    itemCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // 分工厂
        this.formData.itemId = itemData.id
        this.formData.itemCode = itemData.itemCode
        this.formData.itemName = itemData.itemName
      } else {
        // 分工厂
        this.formData.itemId = ''
        this.formData.itemCode = ''
        this.formData.itemName = ''
      }
    },
    ItemGroupChange(e) {
      const { itemData } = e

      this.formData.externalItemGroupId = itemData.id
      this.formData.externalItemGroupCode = itemData.categoryCode
      this.formData.externalItemGroupName = itemData.categoryName
    },
    // 计划组 change
    planGroupCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.planGroupId = itemData.id
        this.formData.planGroupCode = itemData.groupCode
        this.formData.planGroupName = itemData.groupName
      } else {
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
      }
    },
    // 采购组 change
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.buyerOrgId = itemData.id
        this.formData.buyerOrgCode = itemData.groupCode
        this.formData.buyerOrgName = itemData.groupName
      } else {
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
      }
    },
    // 物料组 change
    materialGroupCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.materialGroupId = itemData.id
        this.formData.materialGroupCode = itemData.code
        this.formData.materialGroupName = itemData.name
      } else {
        this.formData.materialGroupId = ''
        this.formData.materialGroupCode = ''
        this.formData.materialGroupName = ''
      }
    },
    // 供应商 change
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.supplierId = itemData.id
        this.formData.supplierCode = itemData.supplierCode
        this.formData.supplierName = itemData.supplierName
      } else {
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      }
    },
    // 送货提前期维护-保存
    postBuyerDeliveryPeriodConfigSave() {
      const params = {
        id: this.formData.id || undefined, // 不传 id 就是新增

        // 工厂
        siteName: this.formData.siteName,
        siteId: this.formData.siteId,
        siteCode: this.formData.siteCode,
        // 分工厂
        subSiteName: this.formData.subSiteName,
        subSiteId: this.formData.subSiteId,
        subSiteCode: this.formData.subSiteCode,
        // 物料
        itemName: this.formData.itemName,
        itemId: this.formData.itemId,
        itemCode: this.formData.itemCode,
        // 公司
        companyName: this.formData.companyName,
        companyId: this.formData.companyId,
        companyCode: this.formData.companyCode,
        // 物料组
        materialGroupName: this.formData.materialGroupName || undefined,
        materialGroupId: this.formData.materialGroupId || undefined,
        materialGroupCode: this.formData.materialGroupCode || undefined,
        // 计划组
        planGroupName: this.formData.planGroupName || undefined,
        planGroupId: this.formData.planGroupId || undefined,
        planGroupCode: this.formData.planGroupCode || undefined,
        // 采购组
        buyerOrgName: this.formData.buyerOrgName || undefined,
        buyerOrgId: this.formData.buyerOrgId || undefined,
        buyerOrgCode: this.formData.buyerOrgCode || undefined,
        // 供应商
        supplierName: this.formData.supplierName,
        supplierId: this.formData.supplierId,
        supplierCode: this.formData.supplierCode,
        // 品类
        externalItemGroupName: this.formData.externalItemGroupName,
        externalItemGroupId: this.formData.externalItemGroupId,
        externalItemGroupCode: this.formData.externalItemGroupCode,
        groupType: this.formData.groupType, // 配置类型
        deliveryMethod: this.formData.deliveryMethod, // 交货方式
        advanceDay: this.formData.advanceDay, // 提前送货天数
        advanceHour: this.formData.advanceHour, // 提前送货小时
        feedbackDay: this.formData.feedbackDay, // 可反馈天数
        dayType: this.formData.dayType, // 日历
        tenantId: this.formData.tenantId, // 租户id
        status: this.formData.status // 状态
      }
      if ([3, '3'].includes(params.deliveryMethod)) {
        delete params.advanceDay
      } else {
        delete params.advanceHour
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDeliveryPeriodConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-分工厂
    getSubSiteList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            condition: 'and',
            rules: [
              {
                condition: 'or',
                label: '',
                field: 'subSiteCode',
                type: 'string',
                operator: 'contains',
                value: text || ''
              }
            ]
          }
        ]
      }
      this.$API.report
        .getSubSiteCodeListOptions(params)
        .then((res) => {
          if (res) {
            const list = res?.data.records || []
            this.subSiteCodeOptions = addCodeNameKeyInList({
              firstKey: 'subSiteCode',
              secondKey: 'subSiteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.subSiteCodeOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-物料
    getitemCodeList(args) {
      const { text, updateData, setSelectData } = args
      //物料下拉
      let params = {
        keyword: text || '',
        pageSize: 50
      }
      this.$API.masterData
        .getItemByKeyword(params)
        .then((res) => {
          if (res) {
            const list = res?.data.records || []
            this.itemCodeOptions = addCodeNameKeyInList({
              firstKey: 'itemCode',
              secondKey: 'itemName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.itemCodeOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    getItemGroupList(args) {
      const { text } = args
      const params = {
        fuzzyNameOrCode: text
        // groupTypeCode: "BG001CG",
      }
      this.$API.masterData
        .getCategoryfuzzy(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.ItemGroupOptions = addCodeNameKeyInList({
              firstKey: 'categoryCode',
              secondKey: 'categoryName',
              list
            })
            // if (updateData && typeof updateData === "function") {
            //   this.$nextTick(() => {
            //     updateData(this.buyerOrgOptions);
            //   });
            // }
            // if (setSelectData) {
            //   this.$nextTick(() => {
            //     setSelectData();
            //   });
            // }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-采购组
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-计划组
    getPlanGroupList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001JH'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.planGroupOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.planGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-物料组
    postItemGroupCriteriaQuery(e) {
      const { text, updateData, setSelectData } = e
      const params = {
        keyword: text
      }
      this.$API.masterData
        .postItemGroupCriteriaQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.materialGroupOptions = addCodeNameKeyInList({
              firstKey: 'code',
              secondKey: 'name',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.materialGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // siteCode 工厂
        this.postSiteFuzzyQuery({
          text: selectData.siteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.siteCode = selectData.siteCode
          }
        })
        // subSiteCode 分工厂
        this.getSubSiteList({
          text: selectData.subSiteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.subSiteCode = selectData.subSiteCode
          }
        })
        // itemCode 物料
        this.getitemCodeList({
          text: selectData.itemCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.itemCode = selectData.itemCode
          }
        })
        // materialGroupCode 物料组
        this.postItemGroupCriteriaQuery({
          text: selectData.materialGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.materialGroupCode = selectData.materialGroupCode
          }
        })
        // supplierCode 供应商
        this.getSupplier({
          text: selectData.supplierCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.supplierCode = selectData.supplierCode
          }
        })
        // planGroupCode 计划组
        this.getPlanGroupList({
          text: selectData.planGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.planGroupCode = selectData.planGroupCode
          }
        })
        // buyerOrgCode 采购组
        this.getBuyerOrgList({
          text: selectData.buyerOrgCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.buyerOrgCode = selectData.buyerOrgCode
          }
        })
        this.getItemGroupList({
          text: selectData.externalItemGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.externalItemGroupCode = selectData.externalItemGroupCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-获取工厂
        this.postSiteFuzzyQuery({ text: undefined })
        // 获取主数据-获取分工厂
        this.getSubSiteList({ text: undefined })
        // 获取主数据-获取物料
        this.getitemCodeList({ text: undefined })
        // 获取主数据-物料组
        this.postItemGroupCriteriaQuery({ text: undefined })
        // 获取主数据-供应商
        this.getSupplier({ text: undefined })
        // 获取主数据-获取计划组
        this.getPlanGroupList({ text: undefined })
        // 获取主数据-获取采购组
        this.getBuyerOrgList({ text: undefined })

        // 获取主数据-获取对外物料组
        this.getItemGroupList({ text: '' })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
