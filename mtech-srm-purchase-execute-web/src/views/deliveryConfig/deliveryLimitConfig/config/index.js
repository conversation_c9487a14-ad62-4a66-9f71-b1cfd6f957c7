import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    field: 'isEffective',
    title: i18n.t('启用状态'),
    minWidth: 120,
    slots: {
      default: ({ row }) => {
        return row.isEffective === 1 ? i18n.t('启用') : i18n.t('停用')
      }
    }
  },
  {
    field: 'configGroupType',
    title: i18n.t('配置方式'),
    minWidth: 120,
    slots: {
      default: ({ row }) => {
        return row.configGroupType === 1 ? `${i18n.t('工厂')}+${i18n.t('供应商')}` : i18n.t('工厂')
      }
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码'),
    minWidth: 120
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称'),
    minWidth: 140
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 140
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 140
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 140
  }
]
