<!-- 采方-供方库存余量限制送货配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="configGroupType" :label="$t('配置方式')">
        <mt-select
          v-model="formData.configGroupType"
          :data-source="typeOptions"
          :placeholder="$t('请选择')"
          @change="typeChange"
        />
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <RemoteAutocomplete
          v-model="formData.siteCode"
          :url="$API.masterData.getSiteListUrl"
          :placeholder="$t('请选择工厂')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
          style="flex: 1"
          @change="changeSite"
        />
      </mt-form-item>
      <mt-form-item v-if="formData.configGroupType === 1" prop="supplierCode" :label="$t('供应商')">
        <RemoteAutocomplete
          v-model="formData.supplierCode"
          url="/masterDataManagement/tenant/supplier/paged-query"
          :placeholder="$t('请选择供应商')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          style="flex: 1"
          @change="changeSupplier"
        />
      </mt-form-item>
      <mt-form-item prop="isEffective" :label="$t('是否启用')">
        <mt-switch
          v-model="formData.isEffective"
          :on-label="$t('是')"
          :off-label="$t('否')"
          :active-value="1"
          :inactive-value="0"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        configGroupType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        siteCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ]
      },
      typeOptions: [
        {
          text: `${this.$t('工厂')}+${this.$t('供应商')}`,
          value: 1
        },
        {
          text: this.$t('工厂'),
          value: 2
        }
      ]
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType } = args
      this.dialogTitle = title
      this.actionType = actionType
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    typeChange() {
      this.formData.supplierCode = ''
      this.formData.supplierName = ''
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      this.$API.deliveryConfig.saveDeliveryLimitConfigApi([params]).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    changeSite(e) {
      this.formData.siteName = e.itemData?.siteName
    },
    changeSupplier(e) {
      this.formData.supplierName = e.itemData?.supplierName
    }
  }
}
</script>
