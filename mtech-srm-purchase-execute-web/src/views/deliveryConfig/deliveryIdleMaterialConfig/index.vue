<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="materialCode" :label="$t('物料编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.materialCode"
                :url="$API.masterData.getItemUrl"
                :placeholder="$t('请选择物料')"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :search-fields="['itemName', 'itemCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                @change="(e) => handleDateTimeChange(e, 'createTime')"
                :placeholder="$t('请选择创建日期')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                css-class="rule-element"
                :data-source="statusList"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig, statusList } from './config'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      statusList,
      searchFormModel: {},
      pageConfig: [],
      indicatorTypeList: [], // 自动发计算指标类型列表
      categoryList: [], // 类别列表
      templateIndexMap: [], // 指标名称列表
      companyOptions: [] //业务公司
    }
  },
  computed: {},
  created() {
    this.pageConfig = pageConfig(this)
  },
  mounted() {},
  methods: {
    //查询业务公司下拉数据
    getCompanyOptions(e = { text: '' }) {
      this.$API.masterData
        .getCompanyBySup({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: e.text
        })
        .then((res) => {
          const companyOptions = res.data
          companyOptions.forEach((item) => {
            item.labelShow = item.orgCode + ' - ' + item.orgName
          })
          this.companyOptions = companyOptions

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(companyOptions)
            }
          })
        })
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'From'] = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel[field + 'To'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel[field + 'From'] = null
        this.searchFormModel[field + 'To'] = null
      }
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()

      if (toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'Markfreeze') {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅选择一行数据进行操作'), type: 'warning' })
          return
        }
        this.handleMarkfreeze(selectedRecords)
      } else if (toolbar.id === 'Unmarkfreezing') {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅选择一行数据进行操作'), type: 'warning' })
          return
        }
        this.handleUnmarkfreezing(selectedRecords)
      }
    },
    handleMarkfreeze(records) {
      // 冻结
      this.$loading()
      this.$API.deliveryConfig
        .idleMaterialOn(records[0])
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('冻结成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
        .finally(() => {
          this.$hloading()
        })
    },
    handleUnmarkfreezing(records) {
      // 解冻
      this.$loading()
      this.$API.deliveryConfig
        .idleMaterialOff(records[0])
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('解冻成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
        .finally(() => {
          this.$hloading()
        })
    },
    handleClickCellTitle() {},
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        const validateMap = {
          itemCode: {
            value: data.itemCode,
            msg: this.$t('请选择物料编码')
          },
          supplierCode: {
            value: data.supplierCode,
            msg: this.$t('请选择供应商编码')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType } = args
      if (requestType === 'save') {
        !args.cancel && this.handleSave(args)
      }
    },
    handleSave(args) {
      const { data, rowIndex } = args
      const params = {
        ...data,
        materialCode: data.itemCode,
        materialName: data.itemName
      }
      this.$loading()
      this.$API.deliveryConfig
        .idleMaterialAdd(params)
        .then((res) => {
          this.$hloading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$hloading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
        .finally(() => {
          this.$hloading()
        })
    }
  },
  beforeDestroy() {
    this.$bus.$off('applicationFormSupViewOA')
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
