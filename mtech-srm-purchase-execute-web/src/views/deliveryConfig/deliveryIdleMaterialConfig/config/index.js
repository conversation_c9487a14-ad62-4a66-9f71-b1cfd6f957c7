//现场评审得分设置Tab
import { i18n } from '@/main.js'
import cellUpload from '../components/cellUpload' // 单元格上传
import cellFileView from '../components/cellFileView' // 单元格附件查看
import Select from '../components/Select' // 单元格附件查看
import cellChanged from '@/components/normalEdit/cellChanged' // 单元格被改变（纯展示）
import Vue from 'vue'
import dayjs from 'dayjs'

export const statusList = [
  { value: '0', text: i18n.t('解冻'), cssClass: 'col-active' },
  { value: '1', text: i18n.t('冻结'), cssClass: 'col-inactive' }
]

const columnData = () => {
  const column = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'serialNumber', // 前端定义
      headerText: i18n.t('序号'),
      allowEditing: false
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      editTemplate: () => {
        return {
          template: Select
        }
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      editTemplate: () => {
        return {
          template: Select
        }
      }
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'status',
      headerText: i18n.t('状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: statusList
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span :class="data.status !== '3' ? 'col-active' : 'col-inactive'">{{ getStatusText() || '-' }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < statusList.length; i++) {
                  const item = statusList[i]
                  if (item.value === this.data.status) {
                    return item.text
                  }
                }
              }
            }
          })
        }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      allowEditing: false
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建日期'),
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(Number(this.data[this.data.column.field])).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      }
    },
    {
      field: 'updateUserName',
      headerText: i18n.t('修改人'),
      allowEditing: false
    },
    {
      field: 'updateTime',
      headerText: i18n.t('修改日期'),
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(Number(this.data[this.data.column.field])).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      }
    },
    {
      field: 'attachFilePath',
      headerText: i18n.t('审批文件'),
      // allowEditing: false,
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: () => {
        return {
          template: cellUpload
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注')
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
            { id: 'Markfreeze', icon: 'a-icon_table_Markfreeze', title: i18n.t('冻结') },
            { id: 'Unmarkfreezing', icon: 'a-icon_table_Unmarkfreezing', title: i18n.t('解冻') }
          ],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      gridId: '9b955e0e-e34c-4bc3-9682-73745cc2fe3c',
      grid: {
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        // frozenColumns: 4,
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/idle/material/page/query',
          serializeList: (list) => {
            if (list.length > 0) {
              let serialNumber = 1
              list.forEach((item) => {
                // 添加序号
                item.serialNumber = serialNumber++
                item.itemCode = item.materialCode
                item.itemName = item.materialName
              })
            }
            return list
          }
        }
      }
    }
  ]
  return config
}
