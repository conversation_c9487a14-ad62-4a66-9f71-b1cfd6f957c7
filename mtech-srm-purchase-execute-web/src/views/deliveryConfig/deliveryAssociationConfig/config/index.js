import { ConfigStatus, ConfigStatusOptions } from './constant'
import { i18n } from '@/main.js'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '250'
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.cellTools = [
        {
          id: 'ConfigEdit',
          icon: 'icon_list_edit',
          permission: ['O_02_0525'],
          title: i18n.t('编辑')
        },
        {
          id: 'ConfigDelete',
          icon: 'icon_list_delete',
          permission: ['O_02_0520'],
          title: i18n.t('删除')
        }
      ]
      defaultCol.width = '150'
      defaultCol.ignore = true
    } else if (col.fieldCode === 'status') {
      // 配置状态
      defaultCol.valueConverter = {
        type: 'map',
        map: ConfigStatusOptions
      }
      defaultCol.cellTools = [
        {
          id: 'ConfigInactive',
          icon: '', // icon_list_disable
          permission: ['O_02_0522'],
          title: i18n.t('停用'),
          visibleCondition: (data) => data.status == ConfigStatus.active // 启用
        },
        {
          id: 'ConfigActive',
          icon: '', // icon_list_enable
          permission: ['O_02_0521'],
          title: i18n.t('启用'),
          visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
        }
      ]
      defaultCol.width = '150'
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier,
        placeholder: i18n.t('供应商')
      }
    } else if (col.fieldCode === 'companyCode') {
      // 公司
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany,
        placeholder: i18n.t('公司')
      }
    } else if (col.fieldCode === 'materialGroupCode') {
      // 物料组
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'materialGroupCode',
        secondKey: 'materialGroupName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup,
        placeholder: i18n.t('物料组')
      }
    } else if (col.fieldCode === 'moduleGroupCode') {
      // 组件物料组
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'moduleGroupCode',
        secondKey: 'moduleGroupName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup,
        placeholder: i18n.t('组件物料组')
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
