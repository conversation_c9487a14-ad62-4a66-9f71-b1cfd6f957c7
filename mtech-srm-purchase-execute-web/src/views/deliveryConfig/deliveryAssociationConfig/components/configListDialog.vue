<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="supplierCode" :label="$t('供应商')" class="">
        <debounce-filter-select
          v-model="formData.supplierCode"
          :request="getSupplier"
          :data-source="supplierOptions"
          :fields="{ text: 'theCodeName', value: 'supplierCode' }"
          :value-template="supplierCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="supplierCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="companyCode" :label="$t('公司')" class="">
        <debounce-filter-select
          v-model="formData.companyCode"
          :request="getCompany"
          :data-source="companyOptions"
          :fields="{ text: 'theCodeName', value: 'orgCode' }"
          :value-template="companyCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="companyCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="materialCode" :label="$t('物料编号')" class="">
        <mt-input
          v-model="formData.materialCode"
          maxlength="50"
          :show-clear-button="false"
          :disabled="false"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="materialGroupCode" :label="$t('物料组')" class="">
        <debounce-filter-select
          v-model="formData.materialGroupCode"
          :request="getMaterialGroupOptions"
          :data-source="materialGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'code' }"
          :value-template="materialGroupCodeValueTemplate"
          @change="materialGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="moduleCode" :label="$t('组件编号')" class="">
        <mt-input
          v-model="formData.moduleCode"
          maxlength="50"
          :show-clear-button="false"
          :disabled="false"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="moduleGroupCode" :label="$t('组件物料组')" class="">
        <debounce-filter-select
          v-model="formData.moduleGroupCode"
          :request="getModuleGroupOptions"
          :data-source="moduleGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'code' }"
          :value-template="moduleGroupCodeValueTemplate"
          @change="moduleGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { DialogActionType, ConfigStatus } from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { RegExpMap } from '@/utils/constant'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    const { halfWidthEngNumAndSymbolsReg } = RegExpMap
    // 物料编号
    const materialCodeValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('物料编号不可为空')))
      } else if (!halfWidthEngNumAndSymbolsReg.test(value)) {
        callback(new Error(this.$t('请输入字母、数字或半角符号')))
      } else {
        callback()
      }
    }
    // 组件编号
    const moduleCodeValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('组件编号不可为空')))
      } else if (!halfWidthEngNumAndSymbolsReg.test(value)) {
        callback(new Error(this.$t('请输入字母、数字或半角符号')))
      } else {
        callback()
      }
    }
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      supplierOptions: [], // 供应商 下拉选项
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }),
      companyOptions: [], // 公司 下拉选项
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'orgCode',
        secondKey: 'orgName'
      }),
      materialGroupOptions: [], // 物料组 下列选项
      materialGroupCodeValueTemplate: codeNameColumn({
        firstKey: 'code',
        secondKey: 'name'
      }),
      moduleGroupOptions: [], // 组件物料组 下列选项
      moduleGroupCodeValueTemplate: codeNameColumn({
        firstKey: 'code',
        secondKey: 'name'
      }),
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 供应商
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        // 公司
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        // 物料编码
        materialCode: [{ required: true, validator: materialCodeValidator, trigger: 'blur' }],
        // 物料组
        materialGroupCode: [
          {
            required: true,
            message: this.$t('请选择物料组'),
            trigger: 'blur'
          }
        ],
        // 组件
        moduleCode: [{ required: true, validator: moduleCodeValidator, trigger: 'blur' }],
        // 组件物料组
        moduleGroupCode: [
          {
            required: true,
            message: this.$t('请选择组件物料组'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',

        // 供应商
        supplierName: '',
        supplierCode: '',
        supplierId: '',
        // 公司
        companyName: '',
        companyId: '',
        companyCode: '',
        // 物料
        materialCode: '',
        // 物料组
        materialGroupName: '',
        materialGroupId: '',
        materialGroupCode: '',
        // 组件
        moduleCode: '',
        // 组件物料组
        moduleGroupName: '',
        moduleGroupId: '',
        moduleGroupCode: '',

        status: ConfigStatus.active, // 配置状态 停用
        tenantId: '' // 租户id
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息

      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',

          // 供应商
          supplierName: '',
          supplierCode: '',
          supplierId: '',
          // 公司
          companyName: '',
          companyId: '',
          companyCode: '',
          // 物料
          materialCode: '',
          // 物料组
          materialGroupName: '',
          materialGroupId: '',
          materialGroupCode: '',
          // 组件
          moduleCode: '',
          // 组件物料组
          moduleGroupName: '',
          moduleGroupId: '',
          moduleGroupCode: '',

          status: ConfigStatus.active, // 配置状态 启用
          tenantId // 租户id
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,

          // 供应商
          supplierName: selectData.supplierName,
          supplierCode: selectData.supplierCode,
          supplierId: selectData.supplierId,
          // 公司
          companyName: selectData.companyName,
          companyId: selectData.companyId,
          companyCode: selectData.companyCode,
          // 物料
          materialCode: selectData.materialCode,
          // 物料组
          materialGroupName: selectData.materialGroupName,
          materialGroupId: selectData.materialGroupId,
          materialGroupCode: selectData.materialGroupCode,
          // 组件
          moduleCode: selectData.moduleCode,
          // 组件物料组
          moduleGroupName: selectData.moduleGroupName,
          moduleGroupId: selectData.moduleGroupId,
          moduleGroupCode: selectData.moduleGroupCode,

          status: selectData.status, // 配置状态 停用
          tenantId // 租户id
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerDeliveryOrderAssociationConfigSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 供应商 change
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.supplierId = itemData.id
        this.formData.supplierCode = itemData.supplierCode
        this.formData.supplierName = itemData.supplierName
      } else {
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      }
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.companyId = itemData.id
        this.formData.companyCode = itemData.orgCode
        this.formData.companyName = itemData.orgName
      } else {
        this.formData.companyId = ''
        this.formData.companyCode = ''
        this.formData.companyName = ''
      }
    },
    // 物料组 change
    materialGroupCodeChange(e) {
      const { itemData } = e
      this.formData.materialGroupId = itemData?.id || ''
      this.formData.materialGroupCode = itemData?.code || ''
      this.formData.materialGroupName = itemData?.name || ''
    },
    // 组件物料组 change
    moduleGroupCodeChange(e) {
      const { itemData } = e
      this.formData.moduleGroupId = itemData?.id || ''
      this.formData.moduleGroupCode = itemData?.code || ''
      this.formData.moduleGroupName = itemData?.name || ''
    },
    // 送货关联配置-保存
    postBuyerDeliveryOrderAssociationConfigSave() {
      const params = {
        id: this.formData.id || undefined, // 不传为 新增

        // 供应商
        supplierName: this.formData.supplierName,
        supplierCode: this.formData.supplierCode,
        supplierId: this.formData.supplierId,
        // 公司
        companyName: this.formData.companyName,
        companyId: this.formData.companyId,
        companyCode: this.formData.companyCode,
        // 物料
        materialCode: this.formData.materialCode,
        // 物料组
        materialGroupName: this.formData.materialGroupName,
        materialGroupId: this.formData.materialGroupId,
        materialGroupCode: this.formData.materialGroupCode,
        // 组件
        moduleCode: this.formData.moduleCode,
        // 组件物料组
        moduleGroupName: this.formData.moduleGroupName,
        moduleGroupId: this.formData.moduleGroupId,
        moduleGroupCode: this.formData.moduleGroupCode,

        status: this.formData.status, // 配置状态 停用
        tenantId: this.formData.tenantId // 租户id
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDeliveryOrderAssociationConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 初始化时 获取主数据-物料组(组件物料组)
    postItemGroupCriteriaQuery() {
      const params = {}
      this.$API.masterData
        .postItemGroupCriteriaQuery(params)
        .then((res) => {
          if (res) {
            let list = res?.data || []
            list = addCodeNameKeyInList({
              firstKey: 'code',
              secondKey: 'name',
              list
            })
            this.materialGroupOptions = list
            this.moduleGroupOptions = list
          }
        })
        .catch(() => {})
    },
    // 获取主数据-物料组(组件物料组)
    getMaterialGroupOptions(e) {
      const { text, updateData, setSelectData } = e
      const params = {
        keyword: text
      }
      this.$API.masterData
        .postItemGroupCriteriaQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.materialGroupOptions = addCodeNameKeyInList({
              firstKey: 'code',
              secondKey: 'name',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.materialGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-组件物料组
    getModuleGroupOptions(e) {
      const { text, updateData, setSelectData } = e
      const params = {
        keyword: text
      }
      this.$API.masterData
        .postItemGroupCriteriaQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.moduleGroupOptions = addCodeNameKeyInList({
              firstKey: 'code',
              secondKey: 'name',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.moduleGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'orgCode',
              secondKey: 'orgName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // supplierCode 供应商
        this.getSupplier({
          text: selectData.supplierCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.supplierCode = selectData.supplierCode
          }
        })
        // materialGroupCode 物料组
        this.getMaterialGroupOptions({
          text: selectData.materialGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.materialGroupCode = selectData.materialGroupCode
          }
        })
        // moduleGroupCode 组件物料组
        this.getModuleGroupOptions({
          text: selectData.moduleGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.moduleGroupCode = selectData.moduleGroupCode
          }
        })
        // companyCode 公司列表
        this.getCompany({
          text: selectData.companyCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.companyCode = selectData.companyCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-供应商
        this.getSupplier({ text: undefined })
        // 初始化时 获取主数据-物料组(组件物料组)
        this.postItemGroupCriteriaQuery()
        // companyCode 公司列表
        this.getCompany({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
