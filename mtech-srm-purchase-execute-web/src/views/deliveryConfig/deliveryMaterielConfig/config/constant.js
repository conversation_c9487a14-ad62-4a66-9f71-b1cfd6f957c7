import { i18n } from '@/main.js'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    permission: ['O_02_0561'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0562'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0563'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0564'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0565'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0566'],
    title: i18n.t('导出')
  }
]

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态 1:启用 2:停用
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 对应的 Options
export const ConfigStatusOptions = [
  {
    // 启用
    value: ConfigStatus.active,
    text: ConfigStatusConst[ConfigStatus.active],
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    value: ConfigStatus.inactive,
    text: ConfigStatusConst[ConfigStatus.inactive],
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]

// 配置状态 CellTools
export const ConfigStatusCellTools = [
  {
    id: 'ConfigInactive',
    icon: '', // icon_list_disable
    title: i18n.t('停用'),
    permission: ['O_02_0564'],
    visibleCondition: (data) => data.status == ConfigStatus.active // 启用
  },
  {
    id: 'ConfigActive',
    icon: '', // icon_list_enable
    title: i18n.t('启用'),
    permission: ['O_02_0563'],
    visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
  }
]

// 序号 CellTools
export const SerialNumberCellTools = [
  {
    id: 'ConfigEdit',
    icon: 'icon_list_edit',
    permission: ['O_02_0567'],
    title: i18n.t('编辑')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_list_delete',
    permission: ['O_02_0562'],
    title: i18n.t('删除')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'groupType', // 类型
    fieldName: i18n.t('配置方式')
  },
  {
    fieldCode: 'siteCode', // 工厂 code-name
    fieldName: i18n.t('工厂')
    // siteName
    // siteId	工厂id
    // siteCode	工厂code
  },
  {
    fieldCode: 'businessTypeName', // 业务类型name
    fieldName: i18n.t('业务类型')
    // businessTypeId	业务类型id
    // businessTypeCode	业务类型code
  },
  {
    fieldCode: 'orderTypeName', // 订单类型name
    fieldName: i18n.t('订单类型')
    // orderTypeCode	订单类型code
    // orderTypeId	订单类型id
  },
  {
    fieldCode: 'materialGroupCode', // 物料组 code-name
    fieldName: i18n.t('物料组')
    // materialGroupName
    // materialGroupId	物料组id
    // materialGroupCode	物料组code
  },
  {
    fieldCode: 'supplierCode', // 供应商 code-name
    fieldName: i18n.t('供应商')
    // supplierName
    // supplierId	供应商id
    // supplierCode	供应商code
  },
  {
    fieldCode: 'updateUserName',
    fieldName: i18n.t('更新人')
  },
  {
    fieldCode: 'updateTime',
    fieldName: i18n.t('更新时间')
  }
]

// 获取主数据字典 masterData.getDictCode 字典code
export const DictCode = {
  businessType: 'businessType', // 业务类型字典code
  orderType: 'OrderType' // 订单类型字典code
}

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置方式
export const ConfigGroupType = {
  type1: 1, // 按工厂+业务类型+订单类型
  type2: 2 // 按工厂+物料组+供应商
}
// 配置方式 text
export const ConfigGroupTypeText = {
  [ConfigGroupType.type1]: i18n.t('按工厂+业务类型+订单类型'),
  [ConfigGroupType.type2]: i18n.t('按工厂+物料组+供应商')
}
// 配置方式 Options
export const ConfigGroupTypeOptions = [
  {
    // 按工厂+业务类型+订单类型
    text: ConfigGroupTypeText[ConfigGroupType.type1],
    value: ConfigGroupType.type1,
    cssClass: ''
  },
  {
    // 按工厂+物料组+供应商
    text: ConfigGroupTypeText[ConfigGroupType.type2],
    value: ConfigGroupType.type2,
    cssClass: ''
  }
]
