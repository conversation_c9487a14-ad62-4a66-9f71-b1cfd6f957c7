import {
  ConfigGroupTypeOptions,
  ConfigStatusOptions,
  ConfigStatusCellTools,
  SerialNumberCellTools
} from './constant'
import { timeDate, textByConfigGroupType } from './columnComponent'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { i18n } from '@/main.js'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150' // 自适应
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.cellTools = SerialNumberCellTools
    } else if (col.fieldCode === 'groupType') {
      // 配置方式
      defaultCol.valueConverter = {
        type: 'map',
        map: ConfigGroupTypeOptions
      }
      defaultCol.width = '250'
    } else if (col.fieldCode === 'siteCode') {
      // 工厂
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    } else if (col.fieldCode === 'updateTime') {
      // 更新时间
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
      defaultCol.width = 'auto'
    } else if (col.fieldCode === 'businessTypeName') {
      // 业务类型
      defaultCol.template = textByConfigGroupType({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'orderTypeName') {
      // 订单类型
      defaultCol.template = textByConfigGroupType({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'materialGroupCode') {
      // 物料组
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = textByConfigGroupType({
        dataKey: col.fieldCode
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = textByConfigGroupType({
        dataKey: col.fieldCode
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'status') {
      // 配置状态
      defaultCol.valueConverter = {
        type: 'map',
        map: ConfigStatusOptions
      }
      defaultCol.cellTools = ConfigStatusCellTools
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
