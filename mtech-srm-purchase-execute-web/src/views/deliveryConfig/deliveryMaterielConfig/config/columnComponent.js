import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { ConfigGroupType } from './constant'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, hasTime } = args

  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

// 配置方式改变的：业务类型、订单类型、物料组、供应商
export const textByConfigGroupType = (args) => {
  const { dataKey } = args

  const template = () => {
    return {
      template: Vue.component('textByConfigGroupType', {
        template: `
        <div>
          <div v-if="isShowData()"
          >{{componentValue}}</div>
          <div v-else>-</div>
        </div>`,
        data: function () {
          return {
            data: {},
            dataKey,
            ConfigGroupType,
            componentValue: ''
          }
        },
        mounted() {
          this.componentValue = this.formatValue()
        },
        methods: {
          // 是否显示当前数据
          isShowData() {
            let isShow = false
            if (
              (this.dataKey === 'businessTypeName' || this.dataKey === 'orderTypeName') &&
              this.data.groupType == this.ConfigGroupType.type1
            ) {
              // 配置方式 == 按工厂+业务类型+订单类型
              isShow = true
            } else if (
              (this.dataKey === 'materialGroupCode' || this.dataKey === 'supplierCode') &&
              this.data.groupType == this.ConfigGroupType.type2
            ) {
              // 配置方式 == 按工厂+物料组+供应商
              isShow = true
            }

            return isShow
          },
          // 格式化数据
          formatValue() {
            const toCodeName = (args) => {
              const { code, name } = args
              if (code && name) {
                return `${code}-${name}`
              } else if (code) {
                return code
              } else if (name) {
                return name
              }
            }
            if (dataKey === 'materialGroupCode') {
              // 物料组
              const code = this.data.materialGroupCode
              const name = this.data.materialGroupName
              return toCodeName({ code, name })
            } else if (dataKey === 'supplierCode') {
              // 供应商
              const code = this.data.supplierCode
              const name = this.data.supplierName
              return toCodeName({ code, name })
            } else {
              return this.data[dataKey]
            }
          }
        }
      })
    }
  }

  return template
}
