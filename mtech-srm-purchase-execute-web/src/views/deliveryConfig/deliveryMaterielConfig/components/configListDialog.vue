<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <!-- 配置方式 -->
      <mt-form-item prop="groupType" :label="$t('配置方式')" class="">
        <mt-select
          v-model="formData.groupType"
          :data-source="ConfigGroupTypeOptions"
          :show-clear-button="true"
          @change="configGroupTypeChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')" class="">
        <debounce-filter-select
          v-model="formData.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 显示： 配置方式 == 按工厂+业务类型+订单类型 -->
      <mt-form-item
        v-show="formData.groupType == ConfigGroupType.type1"
        prop="businessTypeCode"
        :label="$t('业务类型')"
        class=""
      >
        <mt-select
          v-model="formData.businessTypeCode"
          :data-source="businessTypeOptions"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="businessTypeCodeChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <!-- 显示： 配置方式 == 按工厂+业务类型+订单类型 -->
      <mt-form-item
        v-show="formData.groupType == ConfigGroupType.type1"
        prop="orderTypeCode"
        :label="$t('订单类型')"
        class=""
      >
        <mt-select
          v-model="formData.orderTypeCode"
          :data-source="orderTypeOptions"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="orderTypeCodeChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <!-- 显示： 配置方式 == 按工厂+物料组+供应商 -->
      <mt-form-item
        v-show="formData.groupType == ConfigGroupType.type2"
        prop="materialGroupCode"
        :label="$t('物料组')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.materialGroupCode"
          :request="postItemGroupCriteriaQuery"
          :data-source="materialGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'code' }"
          :value-template="materialGroupCodeValueTemplate"
          @change="materialGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 显示： 配置方式 == 按工厂+物料组+供应商 -->
      <mt-form-item
        v-show="formData.groupType == ConfigGroupType.type2"
        prop="supplierCode"
        :label="$t('供应商')"
      >
        <debounce-filter-select
          v-model="formData.supplierCode"
          :request="getSupplier"
          :data-source="supplierOptions"
          :fields="{ text: 'theCodeName', value: 'supplierCode' }"
          :value-template="supplierCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="supplierCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  DialogActionType,
  ConfigGroupTypeOptions,
  ConfigGroupType,
  DictCode,
  ConfigStatus
} from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      businessTypeOptions: [], // 业务类型 下列选项
      orderTypeOptions: [], // 订单类型 下列选项
      materialGroupOptions: [], // 物料组 下列选项
      materialGroupCodeValueTemplate: codeNameColumn({
        firstKey: 'code',
        secondKey: 'name'
      }), // 物料组
      supplierOptions: [], // 供应商 下列选项
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }), // 供应商
      ConfigGroupType, // 配置方式
      ConfigGroupTypeOptions, // 配置方式 下列选项
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 配置方式
        groupType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',
        status: '', // 状态
        // 工厂
        siteName: '',
        siteId: '',
        siteCode: '',
        // 业务类型
        businessTypeName: '',
        businessTypeId: '',
        businessTypeCode: '',
        // 订单类型
        orderTypeName: '',
        orderTypeCode: '',
        orderTypeId: '',
        // 物料组
        materialGroupName: '',
        materialGroupId: '',
        materialGroupCode: '',
        // 供应商
        supplierName: '',
        supplierId: '',
        supplierCode: '',

        groupType: '', // 配置方式
        tenantId: '' // 租户id
      }
    }
  },
  mounted() {
    // 获取主数据-业务类型
    this.getDictCodeBusinessType()
    // 获取主数据-订单类型
    this.getDictCodeOrderType()
  },

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',
          status: ConfigStatus.active, // 配置状态 启用
          // 工厂
          siteName: '',
          siteId: '',
          siteCode: '',
          // 业务类型
          businessTypeName: '',
          businessTypeId: '',
          businessTypeCode: '',
          // 订单类型
          orderTypeName: '',
          orderTypeCode: '',
          orderTypeId: '',
          // 物料组
          materialGroupName: '',
          materialGroupId: '',
          materialGroupCode: '',
          // 供应商
          supplierName: '',
          supplierId: '',
          supplierCode: '',

          groupType: '', // 配置方式
          tenantId // 租户id
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,
          status: selectData.status, // 状态
          // 工厂
          siteName: selectData.siteName,
          siteId: selectData.siteId,
          siteCode: selectData.siteCode,
          // 业务类型
          businessTypeName: selectData.businessTypeName,
          businessTypeId: selectData.businessTypeId,
          businessTypeCode: selectData.businessTypeCode,
          // 订单类型
          orderTypeName: selectData.orderTypeName,
          orderTypeCode: selectData.orderTypeCode,
          orderTypeId: selectData.orderTypeId,
          // 物料组
          materialGroupName: selectData.materialGroupName,
          materialGroupId: selectData.materialGroupId,
          materialGroupCode: selectData.materialGroupCode,
          // 供应商
          supplierName: selectData.supplierName,
          supplierId: selectData.supplierId,
          supplierCode: selectData.supplierCode,

          groupType: selectData.groupType, // 配置方式
          tenantId // 租户id
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerDeliveryOrderMaterialConfigSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 配置方式 Change
    configGroupTypeChange(e) {
      if (e.value == ConfigGroupType.type1) {
        // 配置方式 按工厂+业务类型+订单类型
        // 物料组
        this.formData.materialGroupName = ''
        this.formData.materialGroupId = ''
        this.formData.materialGroupCode = ''
        // 供应商
        this.formData.supplierName = ''
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
      } else if (e.value == ConfigGroupType.type2) {
        // 配置方式 按工厂+物料组+供应商
        // 业务类型
        this.formData.businessTypeName = ''
        this.formData.businessTypeId = ''
        this.formData.businessTypeCode = ''
        // 订单类型
        this.formData.orderTypeName = ''
        this.formData.orderTypeCode = ''
        this.formData.orderTypeId = ''
      } else {
        // 物料组
        this.formData.materialGroupName = ''
        this.formData.materialGroupId = ''
        this.formData.materialGroupCode = ''
        // 供应商
        this.formData.supplierName = ''
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        // 业务类型
        this.formData.businessTypeName = ''
        this.formData.businessTypeId = ''
        this.formData.businessTypeCode = ''
        // 订单类型
        this.formData.orderTypeName = ''
        this.formData.orderTypeCode = ''
        this.formData.orderTypeId = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.siteId = itemData.id
        this.formData.siteCode = itemData.siteCode
        this.formData.siteName = itemData.siteName
      } else {
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
      }
    },
    // 业务类型 change
    businessTypeCodeChange(e) {
      this.formData.businessTypeId = ''
      this.formData.businessTypeCode = ''
      this.formData.businessTypeName = ''
      if (e.value !== undefined && e.value !== null) {
        for (let i = 0; i < this.businessTypeOptions.length; i++) {
          if (this.businessTypeOptions[i].itemCode === e.value) {
            this.formData.businessTypeId = this.businessTypeOptions[i].dictId
            this.formData.businessTypeCode = this.businessTypeOptions[i].itemCode
            this.formData.businessTypeName = this.businessTypeOptions[i].itemName
            break
          }
        }
      }
    },
    // 订单类型 change
    orderTypeCodeChange(e) {
      this.formData.orderTypeId = ''
      this.formData.orderTypeCode = ''
      this.formData.orderTypeName = ''
      if (e.value !== undefined && e.value !== null) {
        for (let i = 0; i < this.orderTypeOptions.length; i++) {
          if (this.orderTypeOptions[i].itemCode === e.value) {
            this.formData.orderTypeId = this.orderTypeOptions[i].dictId
            this.formData.orderTypeCode = this.orderTypeOptions[i].itemCode
            this.formData.orderTypeName = this.orderTypeOptions[i].itemName
            break
          }
        }
      }
    },
    // 物料组 change
    materialGroupCodeChange(e) {
      const { itemData } = e
      this.formData.materialGroupId = itemData?.id || ''
      this.formData.materialGroupCode = itemData?.code || ''
      this.formData.materialGroupName = itemData?.name || ''
    },
    // 供应商 change
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.supplierId = itemData.id
        this.formData.supplierCode = itemData.supplierCode
        this.formData.supplierName = itemData.supplierName
      } else {
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      }
    },
    // 按采购订单送货物料配置-保存
    postBuyerDeliveryOrderMaterialConfigSave() {
      const params = {
        id: this.formData.id || undefined, // 不传即为新增
        status: this.formData.status, // 状态
        // 工厂
        siteName: this.formData.siteName,
        siteId: this.formData.siteId,
        siteCode: this.formData.siteCode,
        // 业务类型
        businessTypeName: this.formData.businessTypeName || undefined,
        businessTypeId: this.formData.businessTypeId || undefined,
        businessTypeCode: this.formData.businessTypeCode || undefined,
        // 订单类型
        orderTypeName: this.formData.orderTypeName || undefined,
        orderTypeCode: this.formData.orderTypeCode || undefined,
        orderTypeId: this.formData.orderTypeId || undefined,
        // 物料组
        materialGroupName: this.formData.materialGroupName || undefined,
        materialGroupId: this.formData.materialGroupId || undefined,
        materialGroupCode: this.formData.materialGroupCode || undefined,
        // 供应商
        supplierName: this.formData.supplierName || undefined,
        supplierId: this.formData.supplierId || undefined,
        supplierCode: this.formData.supplierCode || undefined,

        groupType: this.formData.groupType, // 配置方式
        tenantId: this.formData.tenantId // 租户id
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDeliveryOrderMaterialConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-物料组
    postItemGroupCriteriaQuery(e) {
      const { text, updateData, setSelectData } = e
      const params = {
        keyword: text || undefined
      }
      this.$API.masterData
        .postItemGroupCriteriaQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.materialGroupOptions = addCodeNameKeyInList({
              firstKey: 'code',
              secondKey: 'name',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.materialGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-业务类型
    getDictCodeBusinessType() {
      const params = {
        dictCode: DictCode.businessType
      }
      this.apiStartLoading()
      this.$API.masterData
        .getDictCode(params)
        .then((res) => {
          this.apiEndLoading()
          if (res) {
            const list = res?.data || []
            list.forEach((item) => {
              item.text = item.itemName
              item.value = item.itemCode
            })
            this.businessTypeOptions = list
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-订单类型
    getDictCodeOrderType() {
      const params = {
        dictCode: DictCode.orderType
      }
      this.apiStartLoading()
      this.$API.masterData
        .getDictCode(params)
        .then((res) => {
          this.apiEndLoading()
          if (res) {
            const list = res?.data || []
            list.forEach((item) => {
              item.text = item.itemName
              item.value = item.itemCode
            })
            this.orderTypeOptions = list
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // siteCode 工厂
        this.postSiteFuzzyQuery({
          text: selectData.siteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.siteCode = selectData.siteCode
          }
        })
        // materialGroupCode 物料组
        this.postItemGroupCriteriaQuery({
          text: selectData.materialGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.materialGroupCode = selectData.materialGroupCode
          }
        })
        // supplierCode 供应商
        this.getSupplier({
          text: selectData.supplierCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.supplierCode = selectData.supplierCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-工厂模糊查询
        this.postSiteFuzzyQuery({ text: undefined })
        // 获取主数据-物料组
        this.postItemGroupCriteriaQuery({ text: undefined })
        // 主数据 获取供应商
        this.getSupplier({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
