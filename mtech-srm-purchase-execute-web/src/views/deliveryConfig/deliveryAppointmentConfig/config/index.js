import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('启用'), value: 1 },
  { text: i18n.t('停用'), value: 0 }
]

export const controlTypeOptions = [
  { text: i18n.t('送货'), value: 0 },
  { text: i18n.t('入库'), value: 1 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'controlType',
    title: i18n.t('管控类型'),
    formatter: ({ cellValue }) => {
      let item = controlTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.categoryName : ''
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.itemName : ''
    }
  },
  {
    field: 'buyerOrgCode',
    title: i18n.t('采购组'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.buyerOrgName : ''
    }
  },
  {
    field: 'siteAddress',
    title: i18n.t('库存地点'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteAddressName : ''
    }
  },
  {
    field: 'excludeSiteAddress',
    title: i18n.t('排除的库存地点编码(多个/拼接)'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
