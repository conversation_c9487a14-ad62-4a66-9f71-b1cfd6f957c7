<!-- 采方-送货预约强控名单-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item :label="$t('管控类型')" prop="controlType">
        <mt-select
          v-model="formData.controlType"
          :data-source="controlTypeOptions"
          :fields="{ text: 'text', value: 'value' }"
          :show-clear-button="true"
          :placeholder="$t('请选择')"
          @change="controlTypeChange"
        />
      </mt-form-item>
      <mt-form-item v-if="isShow" prop="siteCode" :label="$t('工厂')">
        <RemoteAutocomplete
          v-model="formData.siteCode"
          :url="$API.masterData.getSiteListUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
          @change="siteChange"
        />
      </mt-form-item>
      <mt-form-item v-if="isShow" prop="siteAddressList" :label="$t('库存地点')">
        <RemoteAutocomplete
          v-model="formData.siteAddressList"
          url="/masterDataManagement/tenant/location/paged-query"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'locationName', value: 'locationCode' }"
          :search-fields="['locationName', 'locationCode']"
          :rule-params="[
            { field: 'siteCode', operator: 'contains', type: 'string', value: formData.siteCode }
          ]"
          :load-data="formData.siteCode"
        />
      </mt-form-item>
      <mt-form-item v-if="isShow" prop="excludeSiteAddress" :label="$t('排除的库存地点')">
        <RemoteAutocomplete
          v-model="formData.excludeSiteAddress"
          url="/masterDataManagement/tenant/location/paged-query"
          :multiple="true"
          :placeholder="$t('请选择')"
          :fields="{ text: 'locationName', value: 'locationCode' }"
          :search-fields="['locationName', 'locationCode']"
          :rule-params="[
            { field: 'siteCode', operator: 'contains', type: 'string', value: formData.siteCode }
          ]"
        />
      </mt-form-item>
      <mt-form-item v-if="isShow" prop="supplierCode" :label="$t('供应商')">
        <RemoteAutocomplete
          v-model="formData.supplierCode"
          url="/masterDataManagement/tenant/supplier/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          @change="supplierChange"
        />
      </mt-form-item>
      <mt-form-item v-if="isShow" prop="buyerOrgCode" :label="$t('采购组')">
        <RemoteAutocomplete
          v-model="formData.buyerOrgCode"
          :url="$API.masterData.getBusinessGroupUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
          @change="buyerOrgChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="isShow && formData.controlType === 1"
        prop="categoryCode"
        :label="$t('品类')"
      >
        <RemoteAutocomplete
          v-model="formData.categoryCode"
          url="/masterDataManagement/tenant/category/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'categoryName', value: 'categoryCode' }"
          :search-fields="['categoryName', 'categoryCode']"
          @change="categoryChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="isShow && formData.controlType === 1"
        prop="itemCodeList"
        :label="$t('物料编码')"
      >
        <RemoteAutocomplete
          v-model="formData.itemCodeList"
          :url="$API.masterData.getItemUrl"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :search-fields="['itemName', 'itemCode']"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { controlTypeOptions } from '../config/index'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},

      controlTypeOptions,
      isShow: false
    }
  },
  computed: {
    rules() {
      return {
        siteAddressList: [
          {
            required: this.formData?.controlType === 1,
            message: this.$t('请选择库存地点'),
            trigger: 'blur'
          }
        ],
        siteCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        buyerOrgCode: [
          {
            required: true,
            message: this.$t('请输入责任议价员'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {},
  methods: {
    controlTypeChange(e) {
      if (e.itemData?.value !== 1) {
        this.formData.categoryCode = null
        this.formData.categoryName = null
        this.formData.itemCodeList = []
      }
    },
    warehouseChange(e) {
      this.formData.siteAddressName = e.itemData?.siteAddressName
    },
    siteChange(e) {
      this.formData.siteName = e.itemData?.siteName
    },
    supplierChange(e) {
      this.formData.supplierName = e.itemData?.supplierName
    },
    buyerOrgChange(e) {
      this.formData.buyerOrgName = e.itemData?.groupName
    },
    categoryChange(e) {
      this.formData.categoryName = e.itemData?.categoryName
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
        this.formData.itemCodeList = row.itemCode
        this.formData.siteAddressList = row.siteAddress
        this.formData.excludeSiteAddress = row.excludeSiteAddress?.split('/')
      }
      this.isShow = true
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
      this.isShow = false
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.isShow = false
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.isShow = false
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      params.excludeSiteAddress = this.formData?.excludeSiteAddress?.join('/')
      if (this.actionType === 'edit') {
        params.itemCodeList = [this.formData.itemCodeList]
        params.siteAddressList = [this.formData.siteAddressList]
      }
      const api = this.$API.deliveryConfig.saveDeliveryAppointmentConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    }
  }
}
</script>
