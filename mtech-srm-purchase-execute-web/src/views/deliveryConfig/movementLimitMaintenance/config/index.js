import { i18n } from '@/main.js'
import Vue from 'vue'
import dayjs from 'dayjs'
import InputView from '../components/InputView.vue'
import Select from '../components/Select.vue'

const columnData = (that) => {
  const column = [
    {
      type: 'checkbox',
      width: 50,
      allowEditing: false
    },
    // {
    //   field: 'serialNumber', // 前端定义
    //   headerText: i18n.t('序号'),
    //   allowEditing: false
    // },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '245',
      editTemplate: () => {
        return { template: Select }
      },
      allowEditing: true,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('供应商编码')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200',
      editTemplate: () => {
        return { template: InputView }
      }
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司编码'),
      width: '245',
      headerTemplate: () => {
        return {
          template: Vue.component('headerTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('公司编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.companyCode}
              dataSource={that.companyOptions}
              popup-width={250}
              allow-filtering={true}
              filter-type='Contains'
              filtering={that.getCompanyOptions}
              placeholder={i18n.t('请选择公司')}
              fields={{ text: 'labelShow', value: 'orgCode' }}
              onChange={(e) => {
                const { orgName } = e.itemData
                scoped.companyName = orgName
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'materialCode',
      headerText: i18n.t('物料编码'),
      width: '245',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('物料编码')}}</span>
                </div>
              `
          })
        }
      },
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'materialName',
      headerText: i18n.t('物料描述'),
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'materialGroupCode',
      headerText: i18n.t('物料组')
    },
    {
      field: 'componentCode',
      headerText: i18n.t('组件编码'),
      width: '245',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('组件编码')}}</span>
                </div>
              `
          })
        }
      }
    },
    // {
    //   field: 'componentName',
    //   headerText: i18n.t('组件名称'),
    //   providedEditor: true,
    //   editorParams: {
    //     type: 'input',
    //     disabled: false
    //   }
    // },
    {
      field: 'componentMaterialGroupCode',
      headerText: i18n.t('组件物料组')
    },
    {
      field: 'enableFlag',
      headerText: i18n.t('启用标识'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('启用标识')}}</span>
                </div>
              `
          })
        }
      },
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      },
      // template: () => {
      //   return {
      //     template: Vue.component('enableFlag', {
      //       template: `<mt-checkbox :id="data.column.field" v-model="isChecked" disabled />`,
      //       data() {
      //         return {
      //           isChecked: false
      //         }
      //       },
      //       mounted() {
      //         if (this.data.enableFlag === 1) {
      //           this.isChecked = true
      //         } else {
      //           this.isChecked = false
      //         }
      //       }
      //     })
      //   }
      // },
      // editTemplate: () => {
      //   return {
      //     template: Vue.component('enableFlag', {
      //       template: `<mt-checkbox :id="data.column.field" v-model="checkStatus" @change="handleChange" />`,
      //       data() {
      //         return {}
      //       },
      //       computed: {
      //         checkStatus() {
      //           return this.data.enableFlag === 1 ? true : false
      //         }
      //       },
      //       methods: {
      //         handleChange(e) {
      //           if (e.checked) {
      //             this.data.enableFlag = 1
      //           } else {
      //             this.data.enableFlag = 0
      //           }
      //         }
      //       }
      //     })
      //   }
      // }
      editorRender(h, scoped) {
        return (
          <div>
            <mt-checkbox
              v-model={scoped.enableFlag}
              onChange={(e) => {
                if (e.checked) {
                  scoped.enableFlag = 1
                } else {
                  scoped.enableFlag = 0
                }
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      allowEditing: false
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('createTime', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (
                  this.data[this.data.column.field] &&
                  this.data[this.data.column.field] !== '0'
                ) {
                  return dayjs(Number(this.data[this.data.column.field])).format(
                    'YYYY-MM-DD HH:mm:ss'
                  )
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('createTime', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (
                  this.data[this.data.column.field] &&
                  this.data[this.data.column.field] !== '0'
                ) {
                  return dayjs(Number(this.data[this.data.column.field])).format(
                    'YYYY-MM-DD HH:mm:ss'
                  )
                }
                return ''
              }
            }
          })
        }
      }
    },
    {
      field: 'updateUserName',
      headerText: i18n.t('更新人'),
      allowEditing: false
    },
    {
      field: 'updateTime',
      headerText: i18n.t('更新时间'),
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('createTime', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (
                  this.data[this.data.column.field] &&
                  this.data[this.data.column.field] !== '0'
                ) {
                  return dayjs(Number(this.data[this.data.column.field])).format(
                    'YYYY-MM-DD HH:mm:ss'
                  )
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('createTime', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (
                  this.data[this.data.column.field] &&
                  this.data[this.data.column.field] !== '0'
                ) {
                  return dayjs(Number(this.data[this.data.column.field])).format(
                    'YYYY-MM-DD HH:mm:ss'
                  )
                }
                return ''
              }
            }
          })
        }
      }
    }
    // {
    //   field: 'remark',
    //   headerText: i18n.t('备注'),
    //   editTemplate: () => {
    //     return {
    //       template: Vue.component('headers', {
    //         template: `
    //             <mt-input
    //             :id="data.column.field" v-model="data[data.column.field]" />
    //           `
    //       })
    //     }
    //   }
    // }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      gridId: '044e65d3-513d-49d5-bcc6-d86fdca899f4',
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
            {
              id: 'closeEdit',
              icon: 'icon_table_delete',
              title: i18n.t('取消编辑')
            },
            { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
            // { id: 'Save', icon: 'icon_solid_Save', title: i18n.t('保存') },
            { id: 'Export', icon: 'icon_solid_export', title: i18n.t('导出') }
          ],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      grid: {
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        // frozenColumns: 4,
        asyncConfig: {
          recordsPosition: 'data',
          url: '/srm-purchase-execute/tenant/movement/astrict/relation/query',
          serializeList: (list) => {
            if (list.length > 0) {
              let serialNumber = 1
              list.forEach((item) => {
                // 添加序号
                item.serialNumber = serialNumber++
              })
            }
            return list
          }
        }
      }
    }
  ]
  return config
}
