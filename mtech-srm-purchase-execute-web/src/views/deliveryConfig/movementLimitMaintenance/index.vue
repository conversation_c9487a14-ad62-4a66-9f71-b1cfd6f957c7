<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
              <mt-multi-select
                ref="companyRef1"
                :popup-width="350"
                v-model="searchFormModel.companyCodes"
                :data-source="companyOptions"
                :show-clear-button="true"
                :fields="{ text: 'labelShow', value: 'orgCode' }"
                :allow-filtering="true"
                filter-type="Contains"
                :filtering="getCompanyOptions"
                :placeholder="$t('请选择公司')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
              <mt-input
                v-model="itemCodes"
                @change="itemChange"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              />
            </mt-form-item>
            <mt-form-item prop="materialGroup" :label="$t('物料组')" label-style="top">
              <mt-input
                v-model="searchFormModel.materialGroup"
                :show-clear-button="true"
                :placeholder="$t('请输入物料组')"
              />
            </mt-form-item>
            <mt-form-item prop="componentCodes" :label="$t('组件编码')" label-style="top">
              <mt-input
                v-model="componentCodes"
                @change="componentChange"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              />
            </mt-form-item>
            <mt-form-item
              prop="componentMaterialGroupCode"
              :label="$t('组件物料组')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.componentMaterialGroupCode"
                :show-clear-button="true"
                :placeholder="$t('请输入组件物料组')"
              />
            </mt-form-item>
            <mt-form-item prop="enableFlag" :label="$t('启用标识')" label-style="top">
              <mt-select
                v-model="searchFormModel.enableFlag"
                css-class="rule-element"
                :data-source="[
                  { value: 0, text: $t('否'), cssClass: '' },
                  { value: 1, text: $t('是'), cssClass: '' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择启用标识')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('更新人')" label-style="top">
              <mt-input
                v-model="searchFormModel.updateUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入更新人')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                @change="(e) => handleDateTimeChange(e, 'createTime')"
                :placeholder="$t('请选择创建时间')"
              />
            </mt-form-item>
            <mt-form-item prop="updateTime" :label="$t('更新时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.updateTime"
                @change="(e) => handleDateTimeChange(e, 'updateTime')"
                :placeholder="$t('请选择更新时间')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig } from './config'
import { utils } from '@mtech-common/utils'
import * as UTILS from '@/utils/utils'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      itemCodes: '',
      componentCodes: '',
      pageConfig: [],
      indicatorTypeList: [], // 自动发计算指标类型列表
      categoryList: [], // 类别列表
      templateIndexMap: [], // 指标名称列表
      companyOptions: [] //业务公司
    }
  },
  computed: {},
  created() {
    this.pageConfig = pageConfig(this)
  },
  mounted() {
    this.getCompanyOptions = utils.debounce(this.getCompanyOptions, 300)
    this.getCompanyOptions({ text: '' })
  },
  methods: {
    // 查询条件操作物料编码切割
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    // 查询条件操作组件编码切割
    componentChange(e) {
      if (e) {
        this.searchFormModel.componentCodes = this.componentCodes.split(' ')
      } else {
        this.searchFormModel.componentCodes = null
      }
    },
    //查询业务公司下拉数据
    getCompanyOptions(e = { text: '' }) {
      this.$API.masterData
        .getCompanyBySup({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: e.text
        })
        .then((res) => {
          const companyOptions = res.data
          companyOptions.forEach((item) => {
            item.labelShow = item.orgCode + ' - ' + item.orgName
          })
          this.companyOptions = companyOptions

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(companyOptions)
            }
          })
        })
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'From'] = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel[field + 'To'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel[field + 'From'] = null
        this.searchFormModel[field + 'To'] = null
      }
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()

      if (selectedRecords.length === 0 && ['Delete', 'Save'].includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'closeEdit') {
        this.$refs.templateRef.refreshCurrentGridData()
      } else if (toolbar.id === 'Save') {
        this.handleSave(selectedRecords)
      } else if (toolbar.id === 'Delete') {
        this.handleDelete(selectedRecords)
      } else if (toolbar.id === 'Export') {
        this.handleExport(selectedRecords)
      }
    },
    handleDelete(selectedRecords) {
      //删除
      const idList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const obj = selectedRecords[i]
        idList.push({
          id: obj.id,
          md5Code: obj.md5Code
        })
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的数据吗?')
        },
        success: () => {
          // let ids = items.map((item) => item.id)
          this.$API.deliveryConfig.movementDelete(idList).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleExport() {
      const params = {
        page: { current: 1, size: 9999 },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.deliveryConfig.movementExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin() {
      // const { requestType, data } = args
      // if (requestType === 'save') {
      //   const validateMap = {
      //     autoCalcIndexType: {
      //       value: data.autoCalcIndexType,
      //       msg: this.$t('请选择自动计算指标类别')
      //     },
      //     categCode: {
      //       value: data.categCode,
      //       msg: this.$t('请选择类别')
      //     },
      //     indexId: {
      //       value: data.indexId,
      //       msg: this.$t('请选择指标名称')
      //     }
      //   }
      //   for (const key in validateMap) {
      //     if (Object.hasOwnProperty.call(validateMap, key)) {
      //       const element = validateMap[key]
      //       if (!element.value) {
      //         this.$toast({ content: element.msg, type: 'warning' })
      //         args.cancel = true
      //         break
      //       }
      //     }
      //   }
      // }
    },
    actionComplete(args) {
      const { requestType, data, rowIndex, action, index } = args
      // if (requestType === 'save') {
      //   !args.cancel && this.handleSave(data, rowIndex)
      // }
      if (requestType === 'save' && action == 'edit') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        // 通过 id 判断这条数据是否为新增请求后，报错，自动再次编辑的数据
        if (data.id) {
          // 有 id，此数据为行编辑的数据
          this.handleSave({
            data: data,
            rowIndex,
            type: 'update'
          })
        } else {
          // 无 id，此数据为新增错误，再次编辑的数据
          this.handleSave({
            data: data,
            rowIndex,
            type: 'add'
          })
        }
      } else if (requestType === 'save' && action == 'add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        // 完成新增行
        this.handleSave({
          data: data,
          rowIndex: index,
          type: 'add'
        })
      }
    },
    // 保存
    handleSave(args) {
      const { data, rowIndex, type } = args
      const params = {
        ...data,
        enableFlag: data.enableFlag ? 1 : 0
      }
      this.getSaveApi(type)(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.startEdit(rowIndex)
          }
        })
        .catch(() => {
          this.startEdit(rowIndex)
        })
    },
    startEdit(index) {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    getSaveApi(type) {
      if (type === 'add') {
        return this.$API.deliveryConfig.movementAdd
      }
      return this.$API.deliveryConfig.movementUpdate
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
