import { i18n } from '@/main.js'
import Vue from 'vue'
// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    // permission: ["O_02_0547"],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    // permission: ["O_02_0548"],
    title: i18n.t('删除')
  }
  // {
  //   id: "ConfigActive",
  //   icon: "icon_solid_Activateorder",
  //   // permission: ["O_02_0549"],
  //   title: i18n.t("启用"),
  // },
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号'),
    width: '200'
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('工厂'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    fieldCode: 'locationName',
    fieldName: i18n.t('仓库')
  },
  // {
  //   fieldCode: "tmsUnloadCode",
  //   fieldName: i18n.t("ASN-WMS"), // 暂无
  // },
  {
    fieldCode: 'wmsUrl',
    fieldName: i18n.t('对端地址'),
    width: '800'
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态 1:启用 2:停用
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
