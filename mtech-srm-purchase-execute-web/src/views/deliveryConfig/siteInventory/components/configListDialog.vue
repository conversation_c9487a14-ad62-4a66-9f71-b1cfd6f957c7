<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="groupType" :label="$t('配置方式')" class="">
        <mt-input
          v-model="formData.companyName"
          :show-clear-button="false"
          :disabled="true"
          placeholder=""
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  DialogActionType,
  GroupTypeOptions,
  GroupType,
  ConfigType,
  ConfigStatus
} from '../config/constant'

export default {
  data() {
    // 计划组
    const planGroupCodeValidator = (rule, value, callback) => {
      if (!value && this.formData.groupType === GroupType.type2) {
        callback(new Error(this.$t('请选择计划组')))
      } else {
        callback()
      }
    }
    // 采购组
    const buyerOrgCodeValidator = (rule, value, callback) => {
      if (!value && this.formData.groupType === GroupType.type1) {
        callback(new Error(this.$t('请选择采购组')))
      } else {
        callback()
      }
    }
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      GroupTypeOptions, // 配置组合方式 下列选项
      GroupType, // 配置组合方式
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 配置方式
        groupType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 计划组
        planGroupCode: [
          {
            required: true,
            validator: planGroupCodeValidator,
            trigger: 'blur'
          }
        ],
        // 采购组
        buyerOrgCode: [
          {
            required: true,
            validator: buyerOrgCodeValidator,
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',

        // 工厂
        siteId: '',
        siteCode: '',
        siteName: '',
        // 计划组
        planGroupId: '',
        planGroupCode: '',
        planGroupName: '',
        // 采购组
        buyerOrgId: '',
        buyerOrgCode: '',
        buyerOrgName: '',

        groupType: GroupType.type1, // 配置方式 默认 工厂+采购组
        status: ConfigStatus.active, // 配置状态 停用
        tenantId: '', // 租户id
        type: ConfigType.type1 // 自动补单配置
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      // this.$refs.dialog.ejsRef.show();
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
    },

    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerDeliveryOrderConfigSave()
        }
      })
    },
    handleClose() {
      // this.$refs.dialog.ejsRef.hide();
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
