<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="$t('操作')"
      :buttons="buttons"
      @close="handleClose"
      :open="onOpen"
    >
      <mt-form ref="addForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <debounce-filter-select
            v-model="addForm.siteCode"
            :request="postSiteFuzzyQuery"
            :data-source="siteOptions"
            :show-clear-button="true"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :value-template="siteCodeValueTemplate"
            @change="siteCodeChange"
            :placeholder="$t('请选择')"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="locationCode" :label="$t('仓库')">
          <mt-select
            v-model="addForm.locationCode"
            :data-source="locationOptions"
            :allow-filtering="true"
            :filtering="postLocation"
            @change="locationChange"
            :show-clear-button="true"
            :fields="{ text: 'label', value: 'locationCode' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="wmsUrl" :label="$t('对端地址')">
          <mt-input v-model="addForm.wmsUrl" :placeholder="$t('对端地址')"></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  props: {
    ids: {
      type: Array,
      default: () => {}
    },
    topInfo: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      locationOptions: [],
      logisticsCompanyOptions: [], // 物流公司
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      deliveryTypeOptions: [
        {
          value: 1,
          text: this.$t('快递配送')
        },
        {
          value: 2,
          text: this.$t('物流配送')
        }
      ], // 业务类型
      addForm: {
        siteName: '',
        locationName: '',
        locationCode: '',
        siteCode: '',
        id: '',
        wmsUrl: ''
      },
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      siteOptions: [],
      rules: {
        siteCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        locationCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        wmsUrl: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ]
      },
      dimensionFields: { text: 'message', value: 'code' },
      dimensionList: [],
      editType: false,
      driverNameOptions: [] // 司机姓名下拉数据源
    }
  },
  mounted() {
    this.addForm.idList = this.ids
    this.$refs.dialog.ejsRef.show()
    if (this.topInfo.siteCode) {
      this.editType = true
      this.addForm = this.topInfo
      console.log(this.addForm)
    }
    this.addForm.siteCode
      ? this.postSiteFuzzyQuery({ text: this.addForm.siteCode })
      : this.postSiteFuzzyQuery({ text: undefined })
    this.postSiteFuzzyQuery({ text: undefined })

    // this.$refs.ruleForm.resetFields();
  },

  methods: {
    postLocation(e) {
      console.log(e)
      this.findLocation(this.addForm.siteCode, e.text)
    },
    // 切换方式
    selectChange(e) {
      console.log(e)
      this.$refs.addForm.resetFields()
      console.log(this.addForm)
    },
    postSiteFuzzyQuery(args) {
      const { text, updateData } = args
      //工厂下拉
      let obj = {
        buyerEnterpriseId: this.customerEnterpriseId,
        fuzzyParam: text
      }
      this.$API.masterData.postSiteFuzzyQuery(obj).then((res) => {
        if (res) {
          const list = res?.data || []
          this.siteOptions = addCodeNameKeyInList({
            firstKey: 'siteCode',
            secondKey: 'siteName',
            list
          })
          if (updateData && typeof updateData === 'function') {
            this.$nextTick(() => {
              updateData(this.siteOptions)
            })
          }
          // if (setSelectData) {
          //   this.$nextTick(() => {
          //     setSelectData();
          //   });
          // }
        }
      })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getDimension() {
      this.$API.moduleConfig.getDimension().then((res) => {
        console.log(res)
        this.dimensionList = res.data || []
      })
    },
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.siteId = itemData.id
        this.addForm.siteCode = itemData.siteCode
        this.addForm.siteName = itemData.siteName
      } else {
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
      }
      this.findLocation(itemData.siteCode, this.addForm.locationCode)
    },
    locationChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.locationName = itemData.locationName
        this.addForm.locationCode = itemData.locationCode
      } else {
        this.formData.locationName = ''
        this.formData.locationCode = ''
      }
    },
    findLocation(e, str) {
      let params = {
        commonCode: e, //工厂code
        dataLimit: 50,
        fuzzyParam: str //搜索参数
      }

      this.$API.masterData.getLocationFuzzyQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          //   if (item.externalCode) {
          //     item.locationCode = item.externalCode;
          //     if (item.externalName) {
          //       item.locationName = item.externalName;
          //     }
          //   }
          item.label = `${item.locationCode}-${item.locationName}`
        })
        this.locationOptions = list
        // this.fields = { text: "label", value: "locationCode" };
      })
    },

    confirm() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let params = this.addForm
          if (this.editType === true) {
            this.$API.deliveryConfig.postWmsAddressConfigConfigUpdate(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$emit('handleAddDialogShow')
              }
            })
          } else {
            this.$API.deliveryConfig.postWmsAddressConfigConfigAdd(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$emit('handleAddDialogShow')
              }
            })
          }
        }
      })
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    }
  }
}
</script>

<style></style>
