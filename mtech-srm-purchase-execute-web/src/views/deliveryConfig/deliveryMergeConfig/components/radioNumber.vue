<template>
  <div class="radio-number" :value="value">
    <div class="box">
      <mt-radio
        class="radio-select"
        v-model="value.select"
        @change="changeRadioSelect"
        :data-source="radioOptions"
        :placeholder="$t('请选择')"
        :show-clear-button="true"
      ></mt-radio>
      <div class="number-input">
        <mt-input-number
          @input="inputEmit"
          class="inline-block"
          v-if="value.select === showNumberInput"
          v-model="value.num"
          :min="0"
          :disabled="false"
          :show-spin-button="false"
        />
        <span v-if="value.select === showNumberInput" class="label-text">{{ label }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  mounted() {},
  props: {
    value: {
      type: Object,
      default: () => {
        return { select: '', num: '' }
      }
    },
    // 当选择的值等于这个值时，出现 NumberInput 组件
    showNumberInput: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 单位
    label: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 单选组数据
    radioOptions: {
      type: Array, // [{label: "", value: ""}]
      default: () => []
    }
  },
  methods: {
    changeRadioSelect() {
      this.value.num = 0
      this.$emit('input', this.value)
      this.$emit('changeSelect', this.value)
    },
    inputEmit() {
      this.$emit('input', this.value)
    }
  }
}
</script>
<style lang="scss" scoped>
.radio-number {
  // 行
  .box {
    display: flex;

    // 单选组
    .radio-select {
      width: 110px;
    }
    // 数字
    .number-input {
      display: flex;
      width: 110px;
    }
    /deep/ .number-input .mt-input-number {
      // 13px 单位 "天" 的宽度
      width: calc(110px - 13px);
    }
    /deep/ .number-input .mt-input-number input {
      // 13px 单位 "天" 的宽度， 22px 删除按钮的宽度
      width: calc(110px - 13px - 22px);
    }
    // 单位
    .label-text {
      line-height: 35px;
      margin-top: -1px;
    }
  }
}
</style>
