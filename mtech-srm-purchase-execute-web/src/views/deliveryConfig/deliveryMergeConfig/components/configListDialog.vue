<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="sameJIT" :label="$t('是否JIT')">
        <mt-radio
          @change="sameClick"
          v-model="formData.sameJIT"
          :data-source="RadioData"
        ></mt-radio>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')" class="">
        <debounce-filter-select
          v-model="formData.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="sameLocation" :label="$t('规则1-相同库存地点')">
        <mt-radio v-model="formData.sameLocation" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item prop="sameSupplier" :label="$t('规则2-相同供应商')">
        <mt-radio v-model="formData.sameSupplier" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        v-if="formData.sameJIT === '0'"
        prop="sameAddress"
        :label="$t('规则3-相同送货地址')"
      >
        <mt-radio v-model="formData.sameAddress" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        v-if="formData.sameJIT === '0'"
        prop="sameProcessor"
        :label="$t('规则4-相同加工商')"
      >
        <mt-radio v-model="formData.sameProcessor" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        prop="receiverContactName"
        v-if="formData.sameJIT === '0'"
        :label="$t('规则5-相同收货联系人')"
      >
        <mt-radio v-model="formData.receiverContactName" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        v-if="formData.sameJIT === '0'"
        prop="receiverContact"
        :label="$t('规则6-相同联系方式')"
      >
        <mt-radio v-model="formData.receiverContact" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        v-if="formData.sameJIT === '0'"
        prop="outsourcedType"
        :label="$t('规则7-相同委外方式')"
      >
        <mt-radio v-model="formData.outsourcedType" :data-source="RadioData"></mt-radio>
      </mt-form-item>

      <mt-form-item
        v-if="formData.sameJIT === '0'"
        prop="sameOrderType"
        :label="$t('规则8-相同关联订单类型')"
      >
        <mt-radio v-model="formData.sameOrderType" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        prop="sameBusinessType"
        v-if="formData.sameJIT === '0'"
        :label="$t('规则9-相同关联订单业务类型')"
      >
        <mt-radio v-model="formData.sameBusinessType" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        v-if="formData.sameJIT === '0'"
        prop="sameOrder"
        :label="$t('规则10-相同关联采购订单')"
      >
        <mt-radio v-model="formData.sameOrder" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        v-if="formData.sameJIT === '1'"
        prop="sameFactory"
        :label="$t('规则11-相同分厂')"
      >
        <mt-radio v-model="formData.sameFactory" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        prop="sameFactoryLocation"
        v-if="formData.sameJIT === '1'"
        :label="$t('规则12-相同分厂库存地点')"
      >
        <mt-radio v-model="formData.sameFactoryLocation" :data-source="RadioData"></mt-radio>
      </mt-form-item>

      <mt-form-item
        v-if="formData.sameJIT === '0'"
        prop="shipType"
        :label="$t('规则13-相同配送方式')"
      >
        <mt-radio v-model="formData.shipType" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item
        v-if="formData.sameJIT === '0'"
        prop="sameWarehouseKeeper"
        :label="$t('规则14-相同仓管员')"
      >
        <mt-radio v-model="formData.sameWarehouseKeeper" :data-source="RadioData"></mt-radio>
      </mt-form-item>
      <mt-form-item v-if="formData.sameJIT === '0'" prop="dateRangeObj" :label="$t('需求日期范围')">
        <radio-number
          :show-number-input="RadioParamsToVale.true"
          :label="$t('天')"
          v-model="formData.dateRangeObj"
          :radio-options="RadioData"
          @changeSelect="dateRangeObjChange"
        ></radio-number>
      </mt-form-item>
      <!-- 需求日期范围 -->
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  DialogActionType,
  RadioData,
  RadioParamsToVale,
  RadioValeToParams,
  ConfigStatus
} from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import RadioNumber from './radioNumber.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect,
    RadioNumber
  },
  data() {
    // 需求日期范围
    const dateRangeObjValidator = (rule, value, callback) => {
      if (
        this.formData.dateRangeObj.select === RadioParamsToVale.true &&
        this.formData.dateRangeObj.num === ''
      ) {
        callback(new Error(this.$t('请输入天数')))
      } else {
        this.$refs.ruleForm.clearValidate(['dateRangeObj'])
        callback()
      }
    }

    return {
      RadioData, // radio data-source
      RadioParamsToVale, // radio api值转绑定值
      apiWaitingQuantity: 0, // 调用的api正在等待数
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 需求日期范围
        dateRangeObj: [{ validator: dateRangeObjValidator, trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',
        status: '', // 状态

        // 工厂
        siteCode: '',
        siteId: '',
        siteName: '',
        shipType: RadioParamsToVale.false,
        sameWarehouseKeeper: RadioParamsToVale.false,
        sameLocation: RadioParamsToVale.false, // 规则1-相同库存地点
        sameSupplier: RadioParamsToVale.false, // 规则2-相同供应商
        sameAddress: RadioParamsToVale.false, // 规则3-相同送货地址
        sameProcessor: RadioParamsToVale.false, // 规则4-相同加工商
        receiverContactName: RadioParamsToVale.false, // 规则5-相同收货联系人
        receiverContact: RadioParamsToVale.false, // 规则6-相同联系人方式
        outsourcedType: RadioParamsToVale.false, // 规则7-相同委外方式
        sameJIT: RadioParamsToVale.false,
        sameOrderType: RadioParamsToVale.false, // 规则8-相同关联订单类型
        sameBusinessType: RadioParamsToVale.false, // 规则9-相同关联订单业务类型
        sameOrder: RadioParamsToVale.false, // 规则10-相同关联采购订单
        sameFactory: RadioParamsToVale.false, // 规则11-相同分厂
        sameFactoryLocation: RadioParamsToVale.false, // 规则12-相同分厂库存地点
        dateRangeObj: { select: RadioParamsToVale.false, num: '' } // 需求日期范围 前端定义
      }
    }
  },
  mounted() {},

  methods: {
    sameClick(e) {
      if (e.value === '1') {
        this.formData.sameFactoryLocation = '0'
        this.formData.sameFactory = '0'
      } else {
        this.formData.sameFactoryLocation = '0'
        this.formData.sameFactory = '0'
      }
    },
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',
          status: ConfigStatus.active, // 配置状态 启用

          // 工厂
          siteCode: '',
          siteId: '',
          siteName: '',

          sameLocation: RadioParamsToVale.false, // 规则1-相同库存地点
          sameSupplier: RadioParamsToVale.false, // 规则2-相同供应商
          sameAddress: RadioParamsToVale.false, // 规则3-相同送货地址
          sameProcessor: RadioParamsToVale.false, // 规则4-相同加工商
          receiverContactName: RadioParamsToVale.false, // 规则5-相同收货联系人
          receiverContact: RadioParamsToVale.false, // 规则6-相同联系人方式
          outsourcedType: RadioParamsToVale.false, // 规则7-相同委外方式
          sameJIT: RadioParamsToVale.false,
          shipType: RadioParamsToVale.false,
          sameWarehouseKeeper: RadioParamsToVale.false,
          sameOrderType: RadioParamsToVale.false, // 规则8-相同关联订单类型
          sameBusinessType: RadioParamsToVale.false, // 规则9-相同关联订单业务类型
          sameOrder: RadioParamsToVale.false, // 规则10-相同关联采购订单
          sameFactory: RadioParamsToVale.false, // 规则11-相同分厂
          sameFactoryLocation: RadioParamsToVale.false, // 规则12-相同分厂库存地点
          dateRangeObj: { select: RadioParamsToVale.false, num: '' } // 需求日期范围 前端定义
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,
          status: selectData.status, // 状态

          // 工厂
          siteCode: selectData.siteCode,
          siteId: selectData.siteId,
          siteName: selectData.siteName,

          sameLocation: RadioParamsToVale[selectData.sameLocation], // 规则1-相同库存地点
          sameSupplier: RadioParamsToVale[selectData.sameSupplier], // 规则2-相同供应商
          sameAddress: RadioParamsToVale[selectData.sameAddress], // 规则3-相同送货地址
          sameProcessor: RadioParamsToVale[selectData.sameProcessor], // 规则4-相同加工商
          receiverContactName: RadioParamsToVale[selectData.receiverContactName], // 规则5-相同收货联系人 FIXME 因为存在旧数据、暂时写死
          receiverContact: RadioParamsToVale[selectData.receiverContact], // 规则6-相同联系人方式 FIXME 因为存在旧数据、暂时写死
          outsourcedType: RadioParamsToVale[selectData.outsourcedType], // 规则7-相同委外方式 FIXME 因为存在旧数据、暂时写死
          sameJIT: RadioParamsToVale[selectData.sameJIT],
          shipType: RadioParamsToVale[selectData.shipType],
          sameWarehouseKeeper: RadioParamsToVale[selectData.sameWarehouseKeeper],

          sameOrderType: RadioParamsToVale[selectData.sameOrderType], // 规则8-相同关联订单类型
          sameBusinessType: RadioParamsToVale[selectData.sameBusinessType], // 规则9-相同关联订单业务类型
          sameOrder: RadioParamsToVale[selectData.sameOrder], // 规则10-相同关联采购订单
          sameFactory: RadioParamsToVale[selectData.sameFactory], // 规则11-相同分厂
          sameFactoryLocation: RadioParamsToVale[selectData.sameFactoryLocation], // 规则12-相同分厂库存地点
          dateRangeObj: {
            select: RadioParamsToVale[selectData.demandDate],
            num: selectData.demandDateRange
          } // 需求日期范围 前端定义
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerDeliveryCombineConfigSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 需求日期范围 changeSelect
    dateRangeObjChange() {
      this.$refs.ruleForm.clearValidate(['dateRangeObj'])
    },
    // // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      this.formData.siteId = itemData?.id || ''
      this.formData.siteCode = itemData?.siteCode || ''
      this.formData.siteName = itemData?.siteName || ''
    },
    // 送货单并单校验-保存
    postBuyerDeliveryCombineConfigSave() {
      const params = {
        id: this.formData.id || undefined, // 不传即为新增
        status: this.formData.status, // 状态

        // 工厂
        siteCode: this.formData.siteCode,
        siteId: this.formData.siteId,
        siteName: this.formData.siteName,

        sameLocation: RadioValeToParams[this.formData.sameLocation], // 规则1-相同库存地点
        sameSupplier: RadioValeToParams[this.formData.sameSupplier], // 规则2-相同供应商
        sameAddress: RadioValeToParams[this.formData.sameAddress], // 规则3-相同送货地址
        sameProcessor: RadioValeToParams[this.formData.sameProcessor], // 规则4-相同加工商
        receiverContactName: RadioValeToParams[this.formData.receiverContactName], // 规则5-相同收货联系人
        receiverContact: RadioValeToParams[this.formData.receiverContact], // 规则6-相同联系人方式
        outsourcedType: RadioValeToParams[this.formData.outsourcedType], // 规则7-相同委外方式
        sameJIT: RadioValeToParams[this.formData.sameJIT],
        shipType: RadioValeToParams[this.formData.shipType],
        sameWarehouseKeeper: RadioValeToParams[this.formData.sameWarehouseKeeper],
        sameOrderType: RadioValeToParams[this.formData.sameOrderType], // 规则8-相同关联订单类型
        sameBusinessType: RadioValeToParams[this.formData.sameBusinessType], // 规则9-相同关联订单业务类型
        sameOrder: RadioValeToParams[this.formData.sameOrder], // 规则10-相同关联采购订单
        sameFactory: RadioValeToParams[this.formData.sameFactory], // 规则11-相同分厂
        sameFactoryLocation: RadioValeToParams[this.formData.sameFactoryLocation], // 规则12-相同分厂库存地点
        demandDate: RadioValeToParams[this.formData.dateRangeObj.select], // 需求日期范围	boolean
        demandDateRange: RadioValeToParams[this.formData.dateRangeObj.select]
          ? Number(this.formData.dateRangeObj.num)
          : undefined // 需求日期范围	number
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDeliveryCombineConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // siteCode 工厂
        this.postSiteFuzzyQuery({
          text: selectData.siteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.siteCode = selectData.siteCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-获取工厂
        this.postSiteFuzzyQuery({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
