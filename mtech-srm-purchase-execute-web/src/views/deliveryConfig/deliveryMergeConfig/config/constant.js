import { i18n } from '@/main.js'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    permission: ['O_02_0514'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0515'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0516'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0517'],
    title: i18n.t('停用')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用 启用状态
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'siteCode', // 工厂 code-name
    fieldName: i18n.t('工厂')
    // siteName
    // siteId	工厂id
    // siteCode	工厂code
  },
  {
    fieldCode: 'sameLocation', // 规则1-相同库存地点
    fieldName: i18n.t('规则1-相同库存地点')
  },
  {
    fieldCode: 'sameSupplier', // 规则2-相同供应商
    fieldName: i18n.t('规则2-相同供应商')
  },
  {
    fieldCode: 'sameAddress', // 规则3-相同送货地址
    fieldName: i18n.t('规则3-相同送货地址')
  },
  {
    fieldCode: 'sameProcessor', // 规则4-相同加工商
    fieldName: i18n.t('规则4-相同加工商')
  },
  {
    fieldCode: 'receiverContactName', // 规则5-相同收货联系人
    fieldName: i18n.t('规则5-相同收货联系人')
  },
  {
    fieldCode: 'receiverContact', // 规则6-相同联系人方式
    fieldName: i18n.t('规则6-相同联系人方式')
  },
  {
    fieldCode: 'outsourcedType', // 规则7-相同委外方式
    fieldName: i18n.t('规则7-相同委外方式')
  },
  {
    fieldCode: 'sameOrderType', // 规则8-相同关联订单类型
    fieldName: i18n.t('规则8-相同关联订单类型')
  },
  {
    fieldCode: 'sameBusinessType', // 规则9-相同关联订单业务类型
    fieldName: i18n.t('规则9-相同关联订单业务类型')
  },
  {
    fieldCode: 'sameOrder', // 规则10-相同关联采购订单
    fieldName: i18n.t('规则10-相同关联采购订单')
  },
  {
    fieldCode: 'sameFactory', // 规则11-相同分厂
    fieldName: i18n.t('规则11-相同分厂')
  },
  {
    fieldCode: 'sameFactoryLocation', // 规则12-相同分厂库存地点
    fieldName: i18n.t('规则12-相同分厂库存地点')
  },
  {
    fieldCode: 'sameJIT', // 规则12-相同分厂库存地点
    fieldName: i18n.t('规则13-是否JIT')
  },
  {
    fieldCode: 'shipType', // 规则12-相同分厂库存地点
    fieldName: i18n.t('规则14-相同配送方式')
  },
  {
    fieldCode: 'sameWarehouseKeeper', // 规则12-相同分厂库存地点
    fieldName: i18n.t('相同仓管员')
  },
  {
    fieldCode: 'dateRangeObj', // 需求日期范围 前端定义
    fieldName: i18n.t('需求日期范围')
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 弹框 radio data-source
export const RadioData = [
  {
    label: i18n.t('是'),
    value: '1'
  },
  {
    label: i18n.t('否'),
    value: '0'
  }
]

// 弹框 radio api值转绑定值
export const RadioParamsToVale = {
  false: '0',
  true: '1'
}

// 弹框 radio 绑定值转api值
export const RadioValeToParams = {
  0: false,
  1: true
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 Options
export const ConfigStatusOptions = [
  {
    // 启用
    text: ConfigStatusConst[ConfigStatus.active],
    value: ConfigStatus.active,
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    text: ConfigStatusConst[ConfigStatus.inactive],
    value: ConfigStatus.inactive,
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]
