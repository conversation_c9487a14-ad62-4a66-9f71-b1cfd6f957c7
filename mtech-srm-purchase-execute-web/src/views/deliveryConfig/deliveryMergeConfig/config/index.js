import { checkbox, checkboxAndNumber } from './columnComponent'
import { ConfigStatusOptions, ConfigStatus } from './constant'
import { i18n } from '@/main.js'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '250'
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号 前端定义
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.cellTools = [
        {
          id: 'ConfigEdit',
          icon: 'icon_list_edit',
          permission: ['O_02_0518'],
          title: i18n.t('编辑')
        },
        {
          id: 'ConfigDelete',
          icon: 'icon_list_delete',
          permission: ['O_02_0515'],
          title: i18n.t('删除')
        }
      ]
      defaultCol.width = '150'
    } else if (col.fieldCode === 'sameLocation') {
      // 规则1-相同库存地点
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameSupplier') {
      // 规则2-相同供应商
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameAddress') {
      // 规则3-相同送货地址
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameProcessor') {
      // 规则4-相同加工商
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'receiverContactName') {
      // 规则5-相同收货联系人
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'receiverContact') {
      // 规则6-相同联系人方式
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'outsourcedType') {
      // 规则7-相同委外方式
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameOrderType') {
      // 规则8-相同关联订单类型
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameBusinessType') {
      // 规则9-相同关联订单业务类型
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameOrder') {
      // 规则10-相同关联采购订单
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameFactory') {
      // 规则11-相同分厂
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameFactoryLocation') {
      // 规则12-相同分厂库存地点
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameJIT') {
      // 规则12-相同分厂库存地点
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'shipType') {
      // 规则12-相同分厂库存地点
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sameWarehouseKeeper') {
      defaultCol.template = checkbox({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'dateRangeObj') {
      // 需求日期范围 前端定义
      defaultCol.template = checkboxAndNumber({
        dataKey: col.fieldCode,
        checkboxKey: 'select',
        numberKey: 'num',
        label: i18n.t('天')
      })
      defaultCol.width = '150'
    } else if (col.fieldCode === 'status') {
      // 配置状态
      defaultCol.valueConverter = {
        type: 'map',
        map: ConfigStatusOptions
      }
      defaultCol.cellTools = [
        {
          id: 'ConfigInactive',
          icon: '', // icon_list_disable
          permission: ['O_02_0517'],
          title: i18n.t('停用'),
          visibleCondition: (data) => data.status == ConfigStatus.active // 启用
        },
        {
          id: 'ConfigActive',
          icon: '', // icon_list_enable
          permission: ['O_02_0516'],
          title: i18n.t('启用'),
          visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
        }
      ]
    } else if (col.fieldCode === 'siteCode') {
      // 工厂
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
      // item.receiverContactName = true; // 规则5-相同收货联系人 FIXME 因为存在旧数据，暂时默认选中
      // item.receiverContact = true; // 规则6-相同联系人方式 FIXME 因为存在旧数据，暂时默认选中
      // item.outsourcedType = true; // 规则7-相同委外方式 FIXME 因为存在旧数据，暂时默认选中
      // 需求日期范围
      item.dateRangeObj = {
        select: item.demandDate, // 需求日期范围 boolean
        num: item.demandDateRange // 需求日期范围 number
      }
    })
  }
  return list
}
