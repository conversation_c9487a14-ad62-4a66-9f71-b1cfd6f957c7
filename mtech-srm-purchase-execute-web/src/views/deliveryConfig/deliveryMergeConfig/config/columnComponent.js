import Vue from 'vue'

export const checkbox = (args) => {
  const { dataKey } = args
  const template = () => {
    return {
      template: Vue.component('checkboxComponent', {
        template: `
          <div>
            <mt-checkbox v-model="data[dataKey]"
              :disabled="true"
              />
          </div>`,
        data: function () {
          return {
            dataKey,
            data: {}
          }
        },
        mounted() {},
        methods: {}
      })
    }
  }
  return template
}

export const checkboxAndNumber = (args) => {
  const { dataKey, checkboxKey, numberKey, label } = args
  const template = () => {
    return {
      template: Vue.component('checkboxComponent', {
        template: `
          <div class="cell-flex">
            <mt-checkbox
              v-model="data[dataKey][checkboxKey]"
              :disabled="true"
            />
            <mt-input-number
              v-if="data[dataKey][checkboxKey]"
              v-model="data[dataKey][numberKey]"
              :width="80"
              :height="26"
              :min="0"
              :disabled="true"
            />
            <span
              class="disable-label"
              v-if="data[dataKey][checkboxKey]"
            >{{label}}</span>
          </div>
          </div>`,
        data: function () {
          return {
            dataKey,
            checkboxKey,
            numberKey,
            label,
            data: {}
          }
        },
        mounted() {},
        methods: {}
      })
    }
  }
  return template
}
