import { i18n } from '@/main.js'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    permission: ['O_02_0500'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0501'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0502'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0503'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0504'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0505'],
    title: i18n.t('导出')
  }
]
// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'itemCode', // 物料编码
    fieldName: i18n.t('物料编号')
  },
  {
    fieldCode: 'siteCode', // 工厂 code-name
    fieldName: i18n.t('工厂')
    // siteName
    // siteId 工厂id
  },
  {
    fieldCode: 'companyCode', // 公司 code-name
    fieldName: i18n.t('公司')
    // companyName
    // companyId	公司id
  },
  {
    fieldCode: 'materialGroupCode', // 物料组 code-name
    fieldName: i18n.t('物料组')
    // materialGroupName
    // materialGroupId	物料组id
  },
  {
    fieldCode: 'planGroupCode', // 计划组
    fieldName: i18n.t('计划组')
    // planGroupName
    // planGroupId	计划组id
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组
    fieldName: i18n.t('采购组')
    // buyerOrgName
    // buyerOrgId	采购组id
  },
  {
    fieldCode: 'deliveryMethod', // 走货方式 非散货走货-NO-KD/SKH-散货走货/CKD/FA/CKD-全散件走货/非散件走货
    fieldName: i18n.t('走货方式')
  }
]

// 走货方式
export const DeliveryMethod = {
  type1: 1, // 非散货走货-NO-KD
  type2: 2, // SKH-散货走货
  type3: 3, // CKD
  type4: 4, // FA
  type5: 5, // CKD-全散件走货
  type6: 6 // 非散件走货
}
// 走货方式 text
export const DeliveryMethodText = {
  [DeliveryMethod.type1]: i18n.t('非散货走货-NO-KD'),
  [DeliveryMethod.type2]: i18n.t('SKH-散货走货'),
  [DeliveryMethod.type3]: i18n.t('CKD'),
  [DeliveryMethod.type4]: i18n.t('FA'),
  [DeliveryMethod.type5]: i18n.t('CKD-全散件走货'),
  [DeliveryMethod.type6]: i18n.t('非散件走货')
}
// 走货方式 Options
export const DeliveryMethodOptions = [
  {
    // 非散货走货-NO-KD
    text: DeliveryMethodText[DeliveryMethod.type1],
    value: DeliveryMethod.type1,
    cssClass: ''
  },
  {
    // SKH-散货走货
    text: DeliveryMethodText[DeliveryMethod.type2],
    value: DeliveryMethod.type2,
    cssClass: ''
  },
  {
    // CKD
    text: DeliveryMethodText[DeliveryMethod.type3],
    value: DeliveryMethod.type3,
    cssClass: ''
  },
  {
    // FA
    text: DeliveryMethodText[DeliveryMethod.type4],
    value: DeliveryMethod.type4,
    cssClass: ''
  },
  {
    // CKD-全散件走货
    text: DeliveryMethodText[DeliveryMethod.type5],
    value: DeliveryMethod.type5,
    cssClass: ''
  },
  {
    // 非散件走货
    text: DeliveryMethodText[DeliveryMethod.type6],
    value: DeliveryMethod.type6,
    cssClass: ''
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 Options
export const ConfigStatusOptions = [
  {
    // 启用
    text: ConfigStatusConst[ConfigStatus.active],
    value: ConfigStatus.active,
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    text: ConfigStatusConst[ConfigStatus.inactive],
    value: ConfigStatus.inactive,
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]
