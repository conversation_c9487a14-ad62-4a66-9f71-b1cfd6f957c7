<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="itemCode" :label="$t('物料编号')" class="">
        <mt-input
          v-model="formData.itemCode"
          maxlength="50"
          :show-clear-button="false"
          :disabled="false"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')" class="">
        <debounce-filter-select
          v-model="formData.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="companyName" :label="$t('公司')" class="">
        <mt-input
          v-model="formData.companyName"
          :show-clear-button="false"
          :disabled="true"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="materialGroupCode" :label="$t('物料组')" class="">
        <debounce-filter-select
          v-model="formData.materialGroupCode"
          :request="getMaterialGroupOptions"
          :data-source="materialGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'code' }"
          :value-template="materialGroupCodeValueTemplate"
          @change="materialGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="planGroupCode" :label="$t('计划组')" class="">
        <debounce-filter-select
          v-model="formData.planGroupCode"
          :request="getPlanGroupList"
          :data-source="planGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="planGroupValueTemplate"
          @change="planGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="buyerOrgCode" :label="$t('采购组')" class="">
        <debounce-filter-select
          v-model="formData.buyerOrgCode"
          :request="getBuyerOrgList"
          :data-source="buyerOrgOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="buyerOrgValueTemplate"
          @change="buyerOrgCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="deliveryMethod" :label="$t('走货方式')" class="">
        <mt-select
          v-model="formData.deliveryMethod"
          :data-source="DeliveryMethodOptions"
          :show-clear-button="true"
          :allow-filtering="false"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { DialogActionType, ConfigStatus, DeliveryMethodOptions } from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { RegExpMap } from '@/utils/constant'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    const { halfWidthEngNumAndSymbolsReg } = RegExpMap
    const itemCodeValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('物料编码不可为空')))
      } else if (!halfWidthEngNumAndSymbolsReg.test(value)) {
        callback(new Error(this.$t('请输入字母、数字或半角符号')))
      } else {
        callback()
      }
    }
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      materialGroupOptions: [], // 物料组 下拉选项
      materialGroupCodeValueTemplate: codeNameColumn({
        firstKey: 'code',
        secondKey: 'name'
      }), // 物料组
      planGroupOptions: [], // 计划组 下拉选项
      planGroupValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 计划组
      buyerOrgOptions: [], // 采购组 下拉选项
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 采购组
      DeliveryMethodOptions, // 走货方式 下列选项
      dialogTitle: '',
      // 业务组类型编码
      businessGroupCode: {
        purchase: '', // 采购组
        plan: '' // 计划组
      },
      selectData: null, // 当前编辑的数据
      rules: {
        // 物料编号
        itemCode: [{ required: true, validator: itemCodeValidator, trigger: 'blur' }],
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 走货方式
        deliveryMethod: [
          {
            required: true,
            message: this.$t('请选择走货方式'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',

        // 工厂
        siteName: '',
        siteId: '',
        siteCode: '',
        // 公司
        companyName: '',
        companyId: '',
        companyCode: '',
        // 物料组
        materialGroupName: '',
        materialGroupId: '',
        materialGroupCode: '',
        // 计划组
        planGroupName: '',
        planGroupId: '',
        planGroupCode: '',
        // 采购组
        buyerOrgName: '',
        buyerOrgId: '',
        buyerOrgCode: '',
        deliveryMethod: '', // 走货方式
        tenantId: '', // 租户id
        status: '' // 状态
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息

      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',

          // 物料编号
          itemCode: '',
          // 工厂
          siteName: '',
          siteId: '',
          siteCode: '',
          // 公司
          companyName: '',
          companyId: '',
          companyCode: '',
          // 物料组
          materialGroupName: '',
          materialGroupId: '',
          materialGroupCode: '',
          // 计划组
          planGroupName: '',
          planGroupId: '',
          planGroupCode: '',
          // 采购组
          buyerOrgName: '',
          buyerOrgId: '',
          buyerOrgCode: '',
          deliveryMethod: '', // 走货方式
          tenantId: tenantId, // 租户id
          status: ConfigStatus.active // 配置状态 启用
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,

          // 物料编号
          itemCode: selectData.itemCode,
          // 工厂
          siteName: selectData.siteName,
          siteId: selectData.siteId,
          siteCode: selectData.siteCode,
          // 公司
          companyName: selectData.companyName,
          companyId: selectData.companyId,
          companyCode: selectData.companyCode,
          // 物料组
          materialGroupName: selectData.materialGroupName,
          materialGroupId: selectData.materialGroupId,
          materialGroupCode: selectData.materialGroupCode,
          // 计划组
          planGroupName: selectData.planGroupName,
          planGroupId: selectData.planGroupId,
          planGroupCode: selectData.planGroupCode,
          // 采购组
          buyerOrgName: selectData.buyerOrgName,
          buyerOrgId: selectData.buyerOrgId,
          buyerOrgCode: selectData.buyerOrgCode,
          deliveryMethod: selectData.deliveryMethod, // 走货方式
          tenantId: tenantId, // 租户id
          status: selectData.status // 状态
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerProjectTextBatchConfigSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 物料组 change
    materialGroupCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.materialGroupId = itemData.id
        this.formData.materialGroupCode = itemData.code
        this.formData.materialGroupName = itemData.name
      } else {
        this.formData.materialGroupId = ''
        this.formData.materialGroupCode = ''
        this.formData.materialGroupName = ''
      }
    },
    // 计划组 change
    planGroupCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.planGroupId = itemData.id
        this.formData.planGroupCode = itemData.groupCode
        this.formData.planGroupName = itemData.groupName
      } else {
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
      }
    },
    // 采购组 change
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.buyerOrgId = itemData.id
        this.formData.buyerOrgCode = itemData.groupCode
        this.formData.buyerOrgName = itemData.groupName
      } else {
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // 工厂
        this.formData.siteId = itemData.id
        this.formData.siteCode = itemData.siteCode
        this.formData.siteName = itemData.siteName
        // 公司
        this.formData.companyId = itemData.parentId
        this.formData.companyCode = itemData.parentCode
        this.formData.companyName = itemData.parentName
      } else {
        // 工厂
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
        // 公司
        this.formData.companyId = ''
        this.formData.companyCode = ''
        this.formData.companyName = ''
      }
    },
    // 关联项目文本批次物料配置-保存
    postBuyerProjectTextBatchConfigSave() {
      const params = {
        id: this.formData.id || undefined, // 不传 id 就是新增

        // 物料编号
        itemCode: this.formData.itemCode,
        // 工厂
        siteName: this.formData.siteName,
        siteId: this.formData.siteId,
        siteCode: this.formData.siteCode,
        // 公司
        companyName: this.formData.companyName,
        companyId: this.formData.companyId,
        companyCode: this.formData.companyCode,
        // 物料组
        materialGroupName: this.formData.materialGroupName || undefined,
        materialGroupId: this.formData.materialGroupId || undefined,
        materialGroupCode: this.formData.materialGroupCode || undefined,
        // 计划组
        planGroupName: this.formData.planGroupName || undefined,
        planGroupId: this.formData.planGroupId || undefined,
        planGroupCode: this.formData.planGroupCode || undefined,
        // 采购组
        buyerOrgName: this.formData.buyerOrgName || undefined,
        buyerOrgId: this.formData.buyerOrgId || undefined,
        buyerOrgCode: this.formData.buyerOrgCode || undefined,
        deliveryMethod: this.formData.deliveryMethod || undefined, // 走货方式
        tenantId: this.formData.tenantId, // 租户id
        status: this.formData.status // 状态
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerProjectTextBatchConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-物料组
    getMaterialGroupOptions(e) {
      const { text, updateData, setSelectData } = e
      const params = {
        keyword: text
      }
      this.$API.masterData
        .postItemGroupCriteriaQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.materialGroupOptions = addCodeNameKeyInList({
              firstKey: 'code',
              secondKey: 'name',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.materialGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-采购组
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-计划组
    getPlanGroupList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001JH'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.planGroupOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.planGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // siteCode 工厂
        this.postSiteFuzzyQuery({
          text: selectData.siteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.siteCode = selectData.siteCode
          }
        })
        // materialGroupCode 物料组
        this.getMaterialGroupOptions({
          text: selectData.materialGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.materialGroupCode = selectData.materialGroupCode
          }
        })
        // planGroupCode 计划组
        this.getPlanGroupList({
          text: selectData.planGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.planGroupCode = selectData.planGroupCode
          }
        })
        // buyerOrgCode 采购组
        this.getBuyerOrgList({
          text: selectData.buyerOrgCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.buyerOrgCode = selectData.buyerOrgCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-获取工厂
        this.postSiteFuzzyQuery({ text: undefined })
        // 获取主数据-获取物料组
        this.getMaterialGroupOptions({ text: undefined })
        // 获取主数据-获取计划组
        this.getPlanGroupList({ text: undefined })
        // 获取主数据-获取采购组
        this.getBuyerOrgList({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
