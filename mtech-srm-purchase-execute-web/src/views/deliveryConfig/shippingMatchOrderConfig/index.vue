<template>
  <div class="orderConfig full-height">
    <!-- 发货匹配订单配置 -->
    <mt-template-page
      ref="template-0"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <!-- 配置新增/编辑弹框 -->
    <config-list-dialog ref="configListDialog" @updateList="updateList"></config-list-dialog>
  </div>
</template>
<script>
import { checkColumn, lastColumn } from './config/config'
import { BASE_TENANT } from '@/utils/constant'
export default {
  components: {
    ConfigListDialog: () => import('./components/configListDialog')
  },
  data() {
    return {
      downTemplateName: this.$t('发货匹配订单配置配置模板'),
      downTemplateParams: {}, // 下载模板参数
      uploadParams: {}, // 明细行上传excel的
      requestUrls: {},
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              permission: ['O_02_0568'],
              title: this.$t('新增')
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              permission: ['O_02_0569'],
              title: this.$t('删除')
            }
          ],
          gridId: this.$tableUUID.deliveryConfig.shippingMatchOrderConfig.list,
          grid: {
            columnData: checkColumn.concat(lastColumn),
            // frozenColumns: 1, // 冻结第一列
            asyncConfig: {
              url: `${BASE_TENANT}/buyerDeliveryMatchConfig/query`,
              serializeList: (list) => {
                list.forEach((item, index) => {
                  item.selfIndex = index + 1
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  methods: {
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport()
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      let selectRecords = e.gridRef.getMtechGridRecords()
      if (!selectRecords.length) {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'Enable') {
        this.handleEnable(selectRecords)
      }
      if (e.toolbar.id === 'Disable') {
        this.handleDisable(selectRecords)
      }
    },
    handleEnable(row) {
      //启用
      let hasOne = row.some((item) => {
        return item.status === 1
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择停用状态的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认启用选中的送货地址配置吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          let params = {
            idList: ids,
            status: 1
          }
          this.$API.deliveryConfig.postBuyerDeliveryMatchConfigStatus(params).then(() => {
            this.$toast({
              content: this.$t('启用送货地址配置操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleDisable(row) {
      //停用
      let hasOne = row.some((item) => {
        return item.status === 2
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择已启用状态的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认停用选中的送货地址配置吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          let params = {
            idList: ids,
            status: 2
          }
          this.$API.deliveryConfig.postBuyerDeliveryMatchConfigStatus(params).then(() => {
            this.$toast({
              content: this.$t('停用送货地址配置操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      //更新数据
      this.$refs[`template-0`].refreshCurrentGridData()
    },
    handleAdd() {
      //新增
      this.$refs.configListDialog.dialogInit({ title: this.$t('新增') })
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的发货匹配订单配置配置吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.deliveryConfig.postBuyerDeliveryMatchConfigDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除发货匹配订单配置配置操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleEdit(row) {
      //编辑
      this.$refs.configListDialog.dialogInit({
        title: this.$t('编辑'),
        row: row
      })
    },
    //点击表格的操作按钮
    handleClickCellTool(e) {
      console.log('方法2', e)
      if (e.tool.id === 'edit') {
        this.handleEdit(e.data)
      }
      if (e.tool.id === 'delete') {
        this.handleDelete([e.data])
      }
      if (e.tool.id === 'enable') {
        this.handleEnable([e.data])
      }
      if (e.tool.id === 'disable') {
        this.handleDisable([e.data])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.orderConfig {
}
</style>
