// import UTILS from "@/utils/utils";
import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'

export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const lastColumn = [
  {
    field: 'selfIndex',
    headerText: i18n.t('序号'),
    width: '150',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        permission: ['O_02_0570'],
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data
        }
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        permission: ['O_02_0569'],
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data
        }
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        id: 'disable',
        icon: '',
        title: i18n.t('停用'),
        permission: ['O_02_0704'],
        visibleCondition: (data) => data.status === 1
      },
      {
        id: 'enable',
        icon: '',
        title: i18n.t('启用'),
        permission: ['O_02_0703'],
        visibleCondition: (data) => data.status === 2
      }
    ]
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    width: '250',
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      placeholder: i18n.t('工厂')
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'matchType',
    headerText: i18n.t('匹配规则'),
    width: '250',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('强关联'), cssClass: '' },
        { value: 2, text: i18n.t('弱关联'), cssClass: '' }
      ]
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150'
  }
]
