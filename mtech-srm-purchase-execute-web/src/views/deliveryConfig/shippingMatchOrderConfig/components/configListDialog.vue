<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" autocomplete="off">
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <mt-select
          v-model="ruleForm.siteCode"
          :data-source="siteCodeOptions"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          :placeholder="$t('请选择')"
          :allow-filtering="true"
          :filtering="serchText"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="matchType" :label="$t('匹配规则')">
        <mt-select
          v-model="ruleForm.matchType"
          :data-source="matchTypeOptions"
          :fields="{ text: 'text', value: 'value' }"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  data() {
    return {
      siteCodeOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      matchTypeOptions: [
        { text: this.$t('强关联'), value: 1 },
        { text: this.$t('弱关联'), value: 2 }
      ], //匹配规则 下列选项
      dialogTitle: '',
      rules: {
        siteCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        matchType: [
          {
            required: true,
            message: this.$t('请选择匹配规则'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        siteCode: '',
        matchType: ''
      }
    }
  },
  mounted() {
    this.postSiteFuzzyQuery()
    this.postSiteFuzzyQuery = utils.debounce(this.postSiteFuzzyQuery, 1000)
  },
  methods: {
    postSiteFuzzyQuery(str1, val) {
      let str = str1 || this.ruleForm.siteCode
      let params = {
        dataLimit: 100,
        fuzzyParam: str || ''
      }
      this.$API.masterData.postSiteFuzzyQuery(params).then((res) => {
        let list = res.data || []
        this.siteCodeOptions = addCodeNameKeyInList({
          firstKey: 'siteCode',
          secondKey: 'siteName',
          list
        })
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.siteCodeOptions)
          })
        }
      })
    },
    async serchText(val) {
      await this.postSiteFuzzyQuery(val && val.text ? val.text : '', val)
    },
    // 初始化
    dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          siteCode: '',
          matchType: ''
        }
      }
      if (this.dialogTitle === this.$t('编辑')) {
        this.ruleForm = {
          siteCode: entryInfo.row.siteCode,
          id: entryInfo.row.id,
          matchType: entryInfo.row.matchType
        }
        this.postSiteFuzzyQuery()
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            matchType: this.ruleForm.matchType,
            siteCode: this.ruleForm.siteCode,
            siteId: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.id,
            siteName: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.siteName,
            status: this.ruleForm.status || 1
          }
          if (this.dialogTitle === this.$t('编辑')) {
            params.id = this.ruleForm.id
            this.$API.deliveryConfig.postBuyerDeliveryMatchConfigSave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
          if (this.dialogTitle === this.$t('新增')) {
            this.$API.deliveryConfig.postBuyerDeliveryMatchConfigSave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
