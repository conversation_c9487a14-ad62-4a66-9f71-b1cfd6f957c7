import { ConfigStatus, ConfigStatusOptions, CreateTypeOptions } from './constant'
import { i18n } from '@/main.js'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150' // 自适应
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.cellTools = [
        {
          id: 'ConfigEdit',
          icon: 'icon_list_edit',
          permission: ['O_02_0546'],
          title: i18n.t('编辑')
        },
        {
          id: 'ConfigDelete',
          icon: 'icon_list_delete',
          permission: ['O_02_0541'],
          title: i18n.t('删除')
        }
      ]
      defaultCol.ignore = true
    } else if (col.fieldCode === 'status') {
      // 配置状态
      defaultCol.valueConverter = {
        type: 'map',
        map: ConfigStatusOptions
      }
      defaultCol.cellTools = [
        {
          id: 'ConfigInactive',
          icon: '', // icon_list_disable
          permission: ['O_02_0543'],
          title: i18n.t('停用'),
          visibleCondition: (data) => data.status == ConfigStatus.active // 启用
        },
        {
          id: 'ConfigActive',
          icon: '', // icon_list_enable
          permission: ['O_02_0542'],
          title: i18n.t('启用'),
          visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
        }
      ]
    } else if (col.fieldCode === 'createType') {
      // 创建方式：1-手动,2-自动
      defaultCol.valueConverter = {
        type: 'map',
        map: CreateTypeOptions
      }
    } else if (col.fieldCode === 'siteCode') {
      // 工厂
      // code-name 形式
      defaultCol.width = '350'
      defaultCol.template = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    } else if (col.fieldCode === 'remark') {
      // 备注
      defaultCol.width = '350'
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
