import { ConfigStatus, ConfigStatusConst, ConfigStatusCssClass } from './constant'
import { i18n } from '@/main.js'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150' // 自适应
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.cellTools = [
        {
          id: 'ConfigEdit',
          icon: 'icon_list_edit',
          permission: ['O_02_0553'],
          title: i18n.t('编辑')
        },
        {
          id: 'ConfigDelete',
          icon: 'icon_list_delete',
          permission: ['O_02_0548'],
          title: i18n.t('删除')
        }
      ]
      defaultCol.ignore = true
    } else if (col.fieldCode === 'status') {
      // 配置状态
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            // 启用
            value: ConfigStatus.active,
            text: ConfigStatusConst[ConfigStatus.active],
            cssClass: ConfigStatusCssClass[ConfigStatus.active]
          },
          {
            // 停用
            value: ConfigStatus.inactive,
            text: ConfigStatusConst[ConfigStatus.inactive],
            cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
          }
        ]
      }
      defaultCol.cellTools = [
        {
          id: 'ConfigInactive',
          icon: '', // icon_list_disable
          permission: ['O_02_0550'],
          title: i18n.t('停用'),
          visibleCondition: (data) => data.status == ConfigStatus.active // 启用
        },
        {
          id: 'ConfigActive',
          icon: '', // icon_list_enable
          permission: ['O_02_0549'],
          title: i18n.t('启用'),
          visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
        }
      ]
    } else if (col.fieldCode === 'tmsFactoryCode') {
      // TMS分厂
      // code-name 形式
      defaultCol.width = '350'
      defaultCol.template = codeNameColumn({
        firstKey: 'tmsFactoryCode',
        secondKey: 'tmsFactoryName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('TMS分厂')
      }
    } else if (col.fieldCode === 'tmsUnloadCode') {
      // TMS卸货点
      // code-name 形式
      defaultCol.width = '350'
      defaultCol.template = codeNameColumn({
        firstKey: 'tmsUnloadCode',
        secondKey: 'tmsUnloadName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('TMS卸货点')
      }
    } else if (col.fieldCode === 'srmFactoryCode') {
      // SRM工厂
      // code-name 形式
      defaultCol.width = '350'
      defaultCol.template = codeNameColumn({
        firstKey: 'srmFactoryCode',
        secondKey: 'srmFactoryName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('SRM工厂')
      }
    } else if (col.fieldCode === 'configType') {
      // 配置方式
      // code-name 形式
      defaultCol.width = '290'
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            // 启用
            value: ConfigStatus.active,
            text: ConfigStatusConst[ConfigStatus.active],
            cssClass: ConfigStatusCssClass[ConfigStatus.active]
          },
          {
            // 停用
            value: ConfigStatus.inactive,
            text: ConfigStatusConst[ConfigStatus.inactive],
            cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
          }
        ]
      }
    } else if (col.fieldCode === 'vmiWarehouseCode') {
      // VMI仓
      // code-name 形式
      defaultCol.width = '290'
      defaultCol.template = codeNameColumn({
        firstKey: 'vmiWarehouseCode',
        secondKey: 'vmiWarehouseName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.wmiWarehouse,
        placeholder: i18n.t('VMI仓')
      }
    } else if (col.fieldCode === 'srmLocationCode') {
      // SRM库位
      // code-name 形式
      defaultCol.width = '350'
      defaultCol.template = codeNameColumn({
        firstKey: 'srmLocationCode',
        secondKey: 'srmLocationName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.stockAddress,
        placeholder: i18n.t('SRM库位')
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
