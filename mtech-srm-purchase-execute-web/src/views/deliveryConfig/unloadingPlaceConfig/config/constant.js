import { i18n } from '@/main.js'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    permission: ['O_02_0547'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0548'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0549'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0550'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0551'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0552'],
    title: i18n.t('导出')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'tmsFactoryCode', // TMS分厂 code-name
    fieldName: i18n.t('TMS分厂')
    // tmsFactoryName
    // tmsFactoryId TMS分厂id
    // tmsFactoryCode TMS分厂编码
  },
  {
    fieldCode: 'tmsUnloadCode', // TMS卸货点 code-name
    fieldName: i18n.t('TMS卸货点')
    // tmsUnloadName
    // tmsUnloadId TMS卸货点id
    // tmsUnloadCode TMS卸货点编码
  },
  {
    fieldCode: 'srmFactoryCode', // SRM工厂 code-name
    fieldName: i18n.t('工厂')
    // srmFactoryName
    // srmFactoryId SRM工厂id
    // srmFactoryCode SRM工厂编码
  },
  {
    fieldCode: 'configType', // 配置方式
    fieldName: i18n.t('配置方式')
  },
  {
    fieldCode: 'srmLocationCode', // SRM库位 code-name
    fieldName: i18n.t('SAP库位')
    // srmLocationName
    // srmLocationId SRM库位id
    // srmLocationCode SRM库位编码
  },
  {
    fieldCode: 'VmiWarehouseCode', // VMI仓
    fieldName: i18n.t('VMI仓')
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态 1:启用 2:停用
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}

// 配置方式
export const ConfigType = {
  sap: 3, // 工厂+SAP库位
  vmi: 5 // 工厂+VMI仓
}
// 配置方式 3:工厂+SAP库位 5:工厂+VMI仓
export const ConfigTypeConst = {
  [ConfigStatus.sap]: i18n.t('工厂+SAP库位'),
  [ConfigStatus.vmi]: i18n.t('工厂+VMI仓')
}
