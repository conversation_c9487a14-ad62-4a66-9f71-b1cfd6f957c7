<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="tmsFactoryCode" :label="$t('TMS分厂')" class="">
        <debounce-filter-select
          v-model="formData.tmsFactoryCode"
          :request="getTmsFactoryOptions"
          :data-source="tmsFactoryOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="tmsFactoryCodeValueTemplate"
          @change="tmsFactoryCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="tmsUnloadCode" :label="$t('TMS卸货点')" class="">
        <debounce-filter-select
          v-model="formData.tmsUnloadCode"
          :request="getTmsUnloadOptions"
          :data-source="tmsUnloadOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="tmsUnloadCodeValueTemplate"
          @change="tmsUnloadCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="srmFactoryCode" :label="$t('工厂')" class="">
        <debounce-filter-select
          v-model="formData.srmFactoryCode"
          :request="getSrmFactoryOptions"
          :data-source="srmFactoryOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="srmFactoryCodeValueTemplate"
          @change="srmFactoryCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="configType" :label="$t('配置方式')" class="">
        <mt-select
          v-model="formData.configType"
          :data-source="configTypeList"
          float-label-type="Never"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="srmLocationCode" :label="$t('SAP库位')" class="">
        <debounce-filter-select
          v-model="formData.srmLocationCode"
          :request="getLocationFuzzyQuery"
          :data-source="srmLocationOptions"
          :fields="{ text: 'theCodeName', value: 'locationCode' }"
          :value-template="srmLocationCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="srmLocationCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓')" class="">
        <debounce-filter-select
          v-model="formData.vmiWarehouseCode"
          :request="getLocationFuzzyQuery"
          :data-source="vmiWarehouseCodeOptions"
          :fields="{ text: 'theCodeName', value: 'vmiWarehouseCode' }"
          :value-template="vmiWarehouseCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="vmiWarehouseCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { DialogActionType, ConfigStatus } from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      tmsFactoryOptions: [], // TMS分厂 下列选项
      configTypeList: [
        { text: this.$t('工厂+SAP库位'), value: 3 },
        { text: this.$t('工厂+VMI仓'), value: 5 }
      ],
      tmsFactoryCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // TMS分厂
      tmsUnloadOptions: [], // TMS卸货点 下列选项
      tmsUnloadCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // TMS卸货点
      srmFactoryOptions: [], // SRM工厂 下列选项
      srmFactoryCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // SRM工厂
      srmLocationOptions: [], // SRM库位 下列选项
      srmLocationCodeValueTemplate: codeNameColumn({
        firstKey: 'locationCode',
        secondKey: 'locationName'
      }), // SRM库位
      vmiWarehouseCodeValueTemplate: codeNameColumn({
        firstKey: 'vmiWarehouseCode',
        secondKey: 'vmiWarehouseName'
      }), // VMi仓
      vmiWarehouseCodeOptions: [],
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      defaultrules: {
        // TMS分厂
        tmsFactoryCode: [
          {
            required: true,
            message: this.$t('请输入TMS分厂'),
            trigger: 'blur'
          }
        ],
        // TMS卸货点
        tmsUnloadCode: [
          {
            required: true,
            message: this.$t('请输入TMS卸货点'),
            trigger: 'blur'
          }
        ],
        // SRM工厂
        srmFactoryCode: [
          {
            required: true,
            message: this.$t('请输入SRM工厂'),
            trigger: 'blur'
          }
        ],
        // 配置方式
        configType: [
          {
            required: true,
            message: this.$t('请输入配置方式'),
            trigger: 'blur'
          }
        ]
      },

      sapRules: {
        // SAP库位
        srmLocationCode: [
          {
            required: true,
            message: this.$t('请输入SAP库位'),
            trigger: 'blur'
          }
        ]
      },
      vmiRules: {
        // VMI仓
        vmiWarehouseCode: [
          {
            required: true,
            message: this.$t('请输入VMI仓库'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',
        status: '', // 状态

        // TMS分厂
        tmsFactoryCode: '',
        tmsFactoryName: '',
        tmsFactoryId: '',
        // TMS卸货点
        tmsUnloadCode: '',
        tmsUnloadName: '',
        tmsUnloadId: '',
        // SRM工厂
        srmFactoryCode: '',
        srmFactoryName: '',
        srmFactoryId: '',
        // SRM库位
        srmLocationCode: '',
        srmLocationName: '',
        srmLocationId: '',
        // VMI仓
        vmiWarehouseCode: '',
        vmiWarehouseName: '',
        configType: '',
        tenantId: '' // 租户id
      }
    }
  },
  computed: {
    rules() {
      return this.formData.configType === 3
        ? { ...this.defaultRules, ...this.sapRules }
        : this.formData.configType === 5
        ? { ...this.defaultRules, ...this.vmiRules }
        : { ...this.defaultrules }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',
          status: ConfigStatus.active, // 配置状态 启用

          // TMS分厂
          tmsFactoryCode: '',
          tmsFactoryName: '',
          tmsFactoryId: '',
          // TMS卸货点
          tmsUnloadCode: '',
          tmsUnloadName: '',
          tmsUnloadId: '',
          // SRM工厂
          srmFactoryCode: '',
          srmFactoryName: '',
          srmFactoryId: '',
          // SRM库位
          srmLocationCode: '',
          srmLocationName: '',
          srmLocationId: '',
          // VMI 仓
          vmiWarehouseCode: '',
          vmiWarehouseName: '',

          tenantId // 租户id
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,
          status: selectData.status, // 状态

          // TMS分厂
          tmsFactoryCode: selectData.tmsFactoryCode,
          tmsFactoryName: selectData.tmsFactoryName,
          tmsFactoryId: selectData.tmsFactoryId,
          // TMS卸货点
          tmsUnloadCode: selectData.tmsUnloadCode,
          tmsUnloadName: selectData.tmsUnloadName,
          tmsUnloadId: selectData.tmsUnloadId,
          // SRM工厂
          srmFactoryCode: selectData.srmFactoryCode,
          srmFactoryName: selectData.srmFactoryName,
          srmFactoryId: selectData.srmFactoryId,
          // SRM库位
          srmLocationCode: selectData.srmLocationCode,
          srmLocationName: selectData.srmLocationName,
          srmLocationId: selectData.srmLocationId,
          // VMI 仓
          vmiWarehouseCode: selectData.vmiWarehouseCode,
          vmiWarehouseName: selectData.vmiWarehouseName,
          tenantId // 租户id
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerUnloadLocationSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // TMS分厂 change
    tmsFactoryCodeChange(e) {
      const { itemData } = e
      this.formData.tmsFactoryId = itemData?.id || ''
      this.formData.tmsFactoryCode = itemData?.siteCode || ''
      this.formData.tmsFactoryName = itemData?.siteName || ''
    },
    // TMS卸货点 change
    tmsUnloadCodeChange(e) {
      const { itemData } = e
      this.formData.tmsUnloadId = itemData?.id || ''
      this.formData.tmsUnloadCode = itemData?.siteCode || ''
      this.formData.tmsUnloadName = itemData?.siteName || ''
    },
    // SRM工厂 change
    srmFactoryCodeChange(e) {
      const { itemData } = e
      this.formData.srmFactoryId = itemData?.id || ''
      this.formData.srmFactoryCode = itemData?.siteCode || ''
      this.formData.srmFactoryName = itemData?.siteName || ''
      // 获取VMI仓数据
      this.getVmiWarehouse()
    },
    // SRM库位 change
    srmLocationCodeChange(e) {
      this.formData.srmLocationId = ''
      this.formData.srmLocationCode = ''
      this.formData.srmLocationName = ''
      if (e.value !== undefined && e.value !== null) {
        for (let i = 0; i < this.srmLocationOptions.length; i++) {
          if (this.srmLocationOptions[i].locationCode === e.value) {
            this.formData.srmLocationId = this.srmLocationOptions[i].id
            this.formData.srmLocationCode = this.srmLocationOptions[i].locationCode
            this.formData.srmLocationName = this.srmLocationOptions[i].locationName
            break
          }
        }
      }
    },
    // VMI仓 change
    vmiWarehouseCodeChange(e) {
      const { itemData } = e
      console.log(itemData)
      this.formData.vmiWarehouseCode = itemData?.vmiWarehouseCode || ''
      this.formData.vmiWarehouseName = itemData?.vmiWarehouseName || ''
    },
    // 保存卸货地点
    postBuyerUnloadLocationSave() {
      const params = {
        id: this.formData.id || undefined, // 不传 id 就是新增
        status: this.formData.status, // 状态
        // TMS分厂
        tmsFactoryCode: this.formData.tmsFactoryCode,
        tmsFactoryName: this.formData.tmsFactoryName,
        tmsFactoryId: this.formData.tmsFactoryId,
        // TMS卸货点
        tmsUnloadCode: this.formData.tmsUnloadCode,
        tmsUnloadName: this.formData.tmsUnloadName,
        tmsUnloadId: this.formData.tmsUnloadId,
        // SRM工厂
        srmFactoryCode: this.formData.srmFactoryCode,
        srmFactoryName: this.formData.srmFactoryName,
        srmFactoryId: this.formData.srmFactoryId,
        // SRM库位
        srmLocationCode: this.formData.srmLocationCode,
        srmLocationName: this.formData.srmLocationName,
        srmLocationId: this.formData.srmLocationId,
        // VMI仓
        vmiWarehouseCode: this.formData.vmiWarehouseCode,
        vmiWarehouseName: this.formData.vmiWarehouseName,
        tenantId: this.formData.tenantId // 租户id
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerUnloadLocationSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            let list = res?.data || []
            list = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            // TMS卸货点
            this.tmsUnloadOptions = list
            // SRM工厂
            this.srmFactoryOptions = list
            // TMS分厂
            this.tmsFactoryOptions = list
          }
        })
        .catch(() => {})
    },
    // TMS分厂
    getTmsFactoryOptions(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.tmsFactoryOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.tmsFactoryOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // Vmi仓
    getVmiWarehouse() {
      const params = {
        defaultRules: [
          {
            field: 'rel.site_code',
            operator: 'equal',
            value: this.formData.srmFactoryCode
          },
          {
            field: 'base.vmiWarehouse_type',
            operator: 'in',
            value: [0, 2]
          }
        ]
      }

      this.$API.supplierCoordination.getVmiWarehouse(params).then((res) => {
        if (res?.code == 200) {
          const list = res?.data || []
          this.vmiWarehouseCodeOptions = addCodeNameKeyInList({
            firstKey: 'vmiWarehouseCode',
            secondKey: 'vmiWarehouseName',
            list
          })
        }
      })
    },
    // TMS卸货点
    getTmsUnloadOptions(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.tmsUnloadOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.tmsUnloadOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // SRM工厂
    getSrmFactoryOptions(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.srmFactoryOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.srmFactoryOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-库位-SRM库位
    getLocationFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .getLocationFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.srmLocationOptions = addCodeNameKeyInList({
              firstKey: 'locationCode',
              secondKey: 'locationName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.srmLocationOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },

    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // tmsFactoryCode TMS分厂
        this.getTmsFactoryOptions({
          text: selectData.tmsFactoryCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.tmsFactoryCode = selectData.tmsFactoryCode
          }
        })
        // tmsUnloadCode TMS卸货点
        this.getTmsUnloadOptions({
          text: selectData.tmsUnloadCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.tmsUnloadCode = selectData.tmsUnloadCode
          }
        })
        // srmFactoryCode SRM工厂
        this.getSrmFactoryOptions({
          text: selectData.srmFactoryCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.srmFactoryCode = selectData.srmFactoryCode
          }
        })
        // srmLocationCode SRM库位
        this.getLocationFuzzyQuery({
          text: selectData.srmLocationCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.srmLocationCode = selectData.srmLocationCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-库位-SRM库位
        this.getLocationFuzzyQuery({ text: undefined })
        // 获取主数据-TMS卸货点、SRM工厂、TMS分厂
        this.postSiteFuzzyQuery({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
