import { i18n } from '@/main.js'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    permission: ['O_02_0533'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0534'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0535'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0536'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0537'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0538'],
    title: i18n.t('导出')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'groupType', // 配置方式
    fieldName: i18n.t('配置方式')
  },
  {
    fieldCode: 'siteCode', // 工厂 code-name
    fieldName: i18n.t('工厂')
    // siteName
    // siteId 工厂id
    // siteCode 工厂code
  },
  {
    fieldCode: 'planGroupCode', // 计划组 code-name
    fieldName: i18n.t('计划组')
    // planGroupName
    // planGroupId 计划组id
    // planGroupCode 计划组code
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组 code-name
    fieldName: i18n.t('采购组')
    // buyerOrgName
    // buyerOrgId 采购组id
    // buyerOrgCode 采购组code
  },
  {
    fieldCode: 'updateUserName',
    fieldName: i18n.t('更新人')
  },
  {
    fieldCode: 'updateTime',
    fieldName: i18n.t('更新时间')
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1-启用/2-停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 Options
export const ConfigStatusOptions = [
  {
    // 启用
    text: ConfigStatusConst[ConfigStatus.active],
    value: ConfigStatus.active,
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    text: ConfigStatusConst[ConfigStatus.inactive],
    value: ConfigStatus.inactive,
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]

// 类型,1-自动补单配置/2-无订单送货配置
export const ConfigType = {
  type1: 1, // 自动补单配置
  type2: 2 // 无订单送货配置
}

// 配置组合方式 1-工厂+采购组/2-工厂+计划组
export const GroupType = {
  type1: 1, // 工厂+采购组
  type2: 2 // 工厂+计划组
}
// 配置组合方式
export const GroupTypeConst = {
  [GroupType.type1]: i18n.t('工厂+采购组'),
  [GroupType.type2]: i18n.t('工厂+计划组')
}
// 配置组合方式 Options
export const GroupTypeOptions = [
  {
    // 工厂+采购组
    text: GroupTypeConst[GroupType.type1],
    value: GroupType.type1,
    cssClass: ''
  },
  {
    // 工厂+计划组
    text: GroupTypeConst[GroupType.type2],
    value: GroupType.type2,
    cssClass: ''
  }
]
