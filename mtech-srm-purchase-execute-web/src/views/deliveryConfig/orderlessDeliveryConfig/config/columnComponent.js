import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { GroupType } from '../config/constant'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, hasTime } = args

  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

// 配置方式改变的：采购组、计划组
export const textByGroupType = (args) => {
  const { dataKey } = args

  const template = () => {
    return {
      template: Vue.component('textByGroupType', {
        template: `
        <div>
          <div v-if="isShowData()"
          >{{componentValue}}</div>
          <div v-else>-</div>
        </div>`,
        data: function () {
          return {
            data: {},
            dataKey,
            GroupType,
            componentValue: ''
          }
        },
        mounted() {
          this.componentValue = this.formatValue()
        },
        methods: {
          // 是否显示当前数据
          isShowData() {
            let isShow = false
            if (this.dataKey === 'buyerOrgCode' && this.data.groupType == this.GroupType.type1) {
              // 配置方式 == 工厂+采购组
              isShow = true
            } else if (
              this.dataKey === 'planGroupCode' &&
              this.data.groupType == this.GroupType.type2
            ) {
              // 配置方式 == 工厂+计划组
              isShow = true
            }

            return isShow
          },
          // 格式化数据
          formatValue() {
            const toCodeName = (args) => {
              const { code, name } = args
              if (code && name) {
                return `${code}-${name}`
              } else if (code) {
                return code
              } else if (name) {
                return name
              }
            }
            if (dataKey === 'planGroupCode') {
              // 计划组
              const code = this.data.planGroupCode
              const name = this.data.planGroupName
              return toCodeName({ code, name })
            } else if (dataKey === 'buyerOrgCode') {
              // 采购组
              const code = this.data.buyerOrgCode
              const name = this.data.buyerOrgName
              return toCodeName({ code, name })
            } else {
              return this.data[dataKey]
            }
          }
        }
      })
    }
  }

  return template
}
