<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="groupType" :label="$t('配置方式')" class="">
        <mt-select
          v-model="formData.groupType"
          :data-source="GroupTypeOptions"
          @change="groupTypeChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')" class="">
        <debounce-filter-select
          v-model="formData.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        v-show="formData.groupType === GroupType.type1"
        prop="buyerOrgCode"
        :label="$t('采购组')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.buyerOrgCode"
          :request="getBuyerOrgList"
          :data-source="buyerOrgOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="buyerOrgValueTemplate"
          @change="buyerOrgCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        v-show="formData.groupType === GroupType.type2"
        prop="planGroupCode"
        :label="$t('计划组')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.planGroupCode"
          :request="getPlanGroupList"
          :data-source="planGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="planGroupValueTemplate"
          @change="planGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  DialogActionType,
  GroupTypeOptions,
  GroupType,
  ConfigType,
  ConfigStatus
} from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    // 计划组
    const planGroupCodeValidator = (rule, value, callback) => {
      if (!value && this.formData.groupType === GroupType.type2) {
        callback(new Error(this.$t('请选择计划组')))
      } else {
        callback()
      }
    }
    // 采购组
    const buyerOrgCodeValidator = (rule, value, callback) => {
      if (!value && this.formData.groupType === GroupType.type1) {
        callback(new Error(this.$t('请选择采购组')))
      } else {
        callback()
      }
    }
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      GroupTypeOptions, // 配置组合方式 下列选项
      GroupType, // 配置组合方式
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      planGroupOptions: [], // 计划组 下列选项
      planGroupValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 计划组
      buyerOrgOptions: [], // 采购组 下列选项
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 采购组
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 配置方式
        groupType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 计划组
        planGroupCode: [
          {
            required: true,
            validator: planGroupCodeValidator,
            trigger: 'blur'
          }
        ],
        // 采购组
        buyerOrgCode: [
          {
            required: true,
            validator: buyerOrgCodeValidator,
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',

        // 工厂
        siteId: '',
        siteCode: '',
        siteName: '',
        // 计划组
        planGroupId: '',
        planGroupCode: '',
        planGroupName: '',
        // 采购组
        buyerOrgId: '',
        buyerOrgCode: '',
        buyerOrgName: '',

        groupType: GroupType.type1, // 配置方式 默认 工厂+采购组
        status: ConfigStatus.active, // 配置状态 停用
        tenantId: '', // 租户id
        type: ConfigType.type2 // 无订单送货配置
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',

          // 工厂
          siteId: '',
          siteCode: '',
          siteName: '',
          // 计划组
          planGroupId: '',
          planGroupCode: '',
          planGroupName: '',
          // 采购组
          buyerOrgId: '',
          buyerOrgCode: '',
          buyerOrgName: '',

          groupType: GroupType.type1, // 配置方式 默认 工厂+采购组
          status: ConfigStatus.active, // 配置状态 启用
          tenantId: tenantId // 租户id
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,

          // 工厂
          siteId: selectData.siteId,
          siteCode: selectData.siteCode,
          siteName: selectData.siteName,
          // 计划组
          planGroupId: selectData.planGroupId,
          planGroupCode: selectData.planGroupCode,
          planGroupName: selectData.planGroupName,
          // 采购组
          buyerOrgId: selectData.buyerOrgId,
          buyerOrgCode: selectData.buyerOrgCode,
          buyerOrgName: selectData.buyerOrgName,

          groupType: selectData.groupType, // 配置方式
          status: selectData.status, // 配置状态 停用
          tenantId: tenantId // 租户id
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerDeliveryOrderConfigSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 配置组合方式 change
    groupTypeChange(e) {
      if (e.value == GroupType.type1) {
        // 配置组合方式 工厂+采购组
        this.$refs.ruleForm.clearValidate(['planGroupCode']) // 计划组
        // 计划组
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
      } else if (e.value == GroupType.type2) {
        // 配置组合方式 工厂+计划组
        this.$refs.ruleForm.clearValidate(['buyerOrgCode']) // 采购组
        // 采购组
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
      } else {
        // 计划组
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
        // 采购组
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      this.formData.siteId = itemData?.id || ''
      this.formData.siteCode = itemData?.siteCode || ''
      this.formData.siteName = itemData?.siteName || ''
    },
    // 计划组 change
    planGroupCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.planGroupId = itemData.id
        this.formData.planGroupCode = itemData.groupCode
        this.formData.planGroupName = itemData.groupName
      } else {
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
      }
    },
    // 采购组 change
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.buyerOrgId = itemData.id
        this.formData.buyerOrgCode = itemData.groupCode
        this.formData.buyerOrgName = itemData.groupName
      } else {
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
      }
    },
    // 收发货自动补单配置、无订单送货配置-保存配置
    postBuyerDeliveryOrderConfigSave() {
      const params = {
        id: this.formData.id || undefined, // 不传 id 就是新增

        // 工厂
        siteId: this.formData.siteId,
        siteCode: this.formData.siteCode,
        siteName: this.formData.siteName,
        // 计划组
        planGroupId: this.formData.planGroupId || undefined,
        planGroupCode: this.formData.planGroupCode || undefined,
        planGroupName: this.formData.planGroupName || undefined,
        // 采购组
        buyerOrgId: this.formData.buyerOrgId || undefined,
        buyerOrgCode: this.formData.buyerOrgCode || undefined,
        buyerOrgName: this.formData.buyerOrgName || undefined,

        groupType: this.formData.groupType, // 配置方式
        status: this.formData.status, // 配置状态
        tenantId: this.formData.tenantId, // 租户id
        type: ConfigType.type2 // 类型 无订单送货配置
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDeliveryOrderConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-采购组
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-计划组
    getPlanGroupList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001JH'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.planGroupOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.planGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // siteCode 工厂
        this.postSiteFuzzyQuery({
          text: selectData.siteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.siteCode = selectData.siteCode
          }
        })
        // planGroupCode 计划组
        this.getPlanGroupList({
          text: selectData.planGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.planGroupCode = selectData.planGroupCode
          }
        })
        // buyerOrgCode 采购组
        this.getBuyerOrgList({
          text: selectData.buyerOrgCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.buyerOrgCode = selectData.buyerOrgCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-工厂模糊查询
        this.postSiteFuzzyQuery({ text: undefined })
        // 获取主数据-获取计划组
        this.getPlanGroupList({ text: undefined })
        // 获取主数据-获取采购组
        this.getBuyerOrgList({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
