import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cssClass: '',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑')
        // permission: ["O_02_0125"],
        //   visibleCondition: (data) => data["status"] == '0',
      },
      {
        id: 'delete',
        icon: 'icon_Editor',
        title: i18n.t('删除')
        // permission: ["O_02_0126"],
        //   visibleCondition: (data) => data["status"] == '1',
      }
    ]
  },
  {
    // width: "150",
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 0, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        id: 'active',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        // permission: ["O_02_0125"],
        visibleCondition: (data) => data['status'] == '0'
      },
      {
        id: 'inactive',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        // permission: ["O_02_0126"],
        visibleCondition: (data) => data['status'] == '1'
      }
    ]
  },
  {
    // width: "150",
    field: 'createUserName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'createTime',
    headerText: i18n.t('工厂')
  },
  {
    // width: "150",
    field: 'remark',
    headerText: i18n.t('小时')
  }
]
// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
