<template>
  <!-- 批次表配置 -->
  <div class="full-height pc-my-apply">
    <mt-template-page
      slot="slot-0"
      ref="template-0"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>

    <config-list-dialog ref="configListDialog"></config-list-dialog>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData, serializeList } from './config/index.js'

export default {
  components: {
    ConfigListDialog: () => import('./components/configListDialog')
  },
  mounted() {
    let userInfo = sessionStorage.getItem('userInfo')
    if (userInfo) {
      this.userInfo = JSON.parse(userInfo)
      this.$set(this.pageConfig0[0].grid, 'asyncConfig', {
        url: `${BASE_TENANT}/requestHeader/query`,
        defaultRules: [
          {
            label: this.$t('创建人'),
            field: 'createUserId',
            type: 'string',
            operator: 'equal',
            value: this.userInfo?.uid
          }
        ]
      })
    }
  },
  data() {
    return {
      pageConfig: [{ title: this.$t('送货单列表') }],
      userInfo: null,
      currentTabIndex: 0,
      pageConfig1: [
        {
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增')
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除')
                },
                {
                  id: 'Enable',
                  icon: 'icon_solid_Activateorder',
                  title: this.$t('启用')
                },
                {
                  id: 'Disable',
                  icon: 'icon_solid_Pauseorder',
                  title: this.$t('停用')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/_distribute_rule/list',
              // 序列化
              serializeList: serializeList
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    handleEdit(row) {
      //编辑
      this.$refs.configListDialog.dialogInit({
        title: this.$t('编辑'),
        row: row
      })
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field == 'strategyName') {
        this.$router.push(`receipt-delivery-detail?id=${e.data.id}&type=view`)
      } else if (e.field == 'historyPriceNum') {
        // this.handlePrice(e.data);
      }
    },
    handleClickCellTool(e) {
      // 停用

      if (e.tool.id === 'inactive') {
        console.log('停用')
      }
      // 启用
      if (e.tool.id === 'active') {
        console.log('启用')
      }
      if (e.tool.id === 'edit') {
        this.handleEdit(e.data)
      }
      if (e.tool.id === 'delete') {
        this.handleDelete(e.data)
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let _status = [],
        _id = []

      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id), _status.push(item.status)
        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })
      if (e.toolbar.id == 'close') {
        for (let i of _status) {
          if (i === 1) {
            this.$toast({
              content: this.$t('只有状态在发货中才可以关闭'),
              type: 'warning'
            })
          }
        }
        this.handleClaim(_id)
      }
      // if (e.toolbar.id === "Delete") {
      // }
      // if (e.toolbar.id === "Enable") {
      // }
      // if (e.toolbar.id === "Disable") {
      // }
      console.log(e)
      console.log(_status)
    },
    handleAdd() {
      console.log(this.$refs.configListDialog)
      this.$refs.configListDialog.dialogInit({ title: this.$t('新增') })
    },
    handleDelete() {
      //删除
      console.log('删除')
    }
    // handleClaim(_id) {},
  }
}
</script>

<style></style>
