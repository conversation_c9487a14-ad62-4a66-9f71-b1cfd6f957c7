<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" autocomplete="off">
      <!-- 任务类型 定时发布/定时反馈 -->
      <mt-form-item prop="operateType" :label="$t('定时发布/反馈')">
        <mt-select
          v-model="ruleForm.operateType"
          :data-source="OperateTypeOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <mt-select
          v-model="ruleForm.siteCode"
          :data-source="siteOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <!-- 模糊搜索供应商编号-名称 显示：配置方式=采购组+工厂+供应商 || 配置方式=计划组+工厂+供应商 -->
      <mt-form-item prop="supplierCode" :label="$t('供应商')">
        <mt-select
          v-model="ruleForm.supplierCode"
          :data-source="supplierOptions"
          :show-clear-button="true"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  components: {
    // SelectWeeksTime: () => import("@/components/selectWeeksTime"),
  },
  data() {
    // 日期范围
    const dateRangeValidator = (rule, value, callback) => {
      console.log(value)
      if (!value.end && !value.start) {
        callback(new Error(this.$t('请选择日期范围')))
      } else {
        this.$refs.ruleForm.clearValidate(['dateRange'])
        callback()
      }
    }
    // 循环时间
    const circulationTimeValidator = (rule, value, callback) => {
      if (this.ruleForm.configType == '1') {
        // 配置类型 == 固定时间
        if ((!value.time && !value.week) || (!value.time && value?.week?.length == 0)) {
          callback(new Error(this.$t('请选择循环时间')))
        } else if (!value.time || !value.week || value?.week?.length == 0) {
          callback(new Error(this.$t('请选择完整的循环时间')))
        }
      } else {
        this.$refs.ruleForm.clearValidate(['circulationTime'])
        callback()
      }
    }
    return {
      planGroupOptions: [], // 计划组 下列选项
      buyerOrgOptions: [], // 采购组 下列选项
      siteOptions: [], // 工厂 下列选项
      supplierOptions: [], // 供应商 下列选项
      OperateTypeOptions: [
        { text: this.$t('定时发布'), value: '1' },
        { text: this.$t('定时反馈'), value: '2' }
      ], // 定时发布/反馈 下拉数据
      ConfigGroupTypeOptions: [
        { text: this.$t('按采购组+工厂+供应商'), value: '1' },
        { text: this.$t('按计划组+工厂+供应商'), value: '2' },
        { text: this.$t('按工厂'), value: '3' }
      ], // 配置方式 下拉数据
      ConfigTypeOptions: [
        { text: this.$t('固定时间'), value: '1' },
        { text: this.$t('超时时间'), value: '2' }
      ], // 配置类型 下列选项
      dialogTitle: '',
      WeekOptions: [
        { text: this.$t('周一'), value: 1 },
        { text: this.$t('周二'), value: 2 },
        { text: this.$t('周三'), value: 3 },
        { text: this.$t('周四'), value: 4 },
        { text: this.$t('周五'), value: 5 },
        { text: this.$t('周六'), value: 6 },
        { text: this.$t('周日'), value: 7 }
      ],
      rules: {
        // 任务类型
        operateType: [
          {
            required: true,
            message: this.$t('请选择任务类型'),
            trigger: 'blur'
          }
        ],
        // 配置方式
        configGroupType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        // 配置类型
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        // 计划组
        planGroupCode: [{ required: true, message: this.$t('请选择计划组'), trigger: 'blur' }],
        // 采购组
        buyerOrgCode: [{ required: true, message: this.$t('请选择采购组'), trigger: 'blur' }],
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 供应商
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        // 日期范围
        dateRange: [{ required: true, validator: dateRangeValidator, trigger: 'blur' }],
        // 循环时间
        circulationTime: [
          {
            required: true,
            validator: circulationTimeValidator,
            trigger: 'blur'
          }
        ],
        // 超时时间
        overtimeTime: [
          {
            required: true,
            message: this.$t('请选择超时时间'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        operateType: '', // 任务类型 定时发布/反馈
        configGroupType: '', // 配置方式
        configType: '', // 配置类型 配置类型:固定时间-1,超时时间-2
        planGroupName: '', // 计划组
        planGroupId: '', // 计划组id
        planGroupCode: '', // 计划组编码
        buyerOrgName: '', // 采购组
        buyerOrgId: '', // 采购组id
        buyerOrgCode: '', // 采购组编码
        siteName: '', // 工厂
        siteId: '', // 工厂id
        siteCode: '', // 工厂编码
        supplierName: '', // 供应商
        supplierId: '', // 供应商id
        supplierCode: '', // 供应商编码
        dateRange: {}, // 日期范围
        circulationTime: {}, // 前端定义 循环时间 timeValue.week timeValue.timeFormat
        overtimeTime: '' // 前端定义 超时时间 timeValue.hour
      },
      editInfo: {}
    }
  },
  mounted() {
    // 获取主数据-采购组
    this.getBuyerOrgList()
    // 获取主数据-计划组
    this.getPlanGroupList()
    // 获取主数据-工厂
    this.getSite()
    // 主数据 获取供应商
    this.getSupplier()
  },

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('编辑')) {
        this.editInfo = entryInfo.row
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log('可以提交了啊啊', this.ruleForm)
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 获取主数据-采购组
    getBuyerOrgList() {
      const params = {
        groupTypeName: '采购组'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        if (res) {
          const list = res?.data || []
          list.forEach((item) => {
            item.text = item.groupName
            item.value = item.groupCode
          })
          this.buyerOrgOptions = list
        }
      })
    },
    // 获取主数据-计划组
    getPlanGroupList() {
      const params = {
        groupTypeName: '计划组'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        if (res) {
          const list = res?.data || []
          list.forEach((item) => {
            item.text = item.groupName
            item.value = item.groupCode
          })
          this.planGroupOptions = list
        }
      })
    },
    // 获取主数据-工厂
    getSite() {
      this.$API.masterData.getSite().then((res) => {
        if (res) {
          const list = res?.data || []
          list.forEach((item) => {
            item.text = item.siteName
            item.value = item.siteCode
          })
          this.siteOptions = list
        }
      })
    },
    // 主数据 获取供应商
    getSupplier() {
      this.$API.masterData.getSupplier().then((res) => {
        if (res) {
          const list = res?.data || []
          list.forEach((item) => {
            item.text = item.supplierName
            item.value = item.supplierCode
          })
          this.supplierOptions = list
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
