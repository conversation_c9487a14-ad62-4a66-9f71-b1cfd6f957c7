import Vue from 'vue'
// import { utils } from "@mtech-common/utils";
import { i18n } from '@/main.js'

// 页面内容是否可编辑
export let editFlag = {
  isEditable: false
}

// 并单策略
export let orderConfigData = []

// 供应商送货单合并生成配置数据
export let supplierDataSource = []

// 供应商送货单合并生成配置
export const supplierColumnData = [
  {
    width: '130',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '130',
    field: 'businessType',
    headerText: i18n.t('业务类型')
  },
  {
    width: '130',
    field: 'orderType',
    headerText: i18n.t('订单类型')
  },
  {
    width: '130',
    field: 'rule1',
    headerText: i18n.t('规则1-公司'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus" cssClass="checkbox-checkeditem"  :disabled="true"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          computed: {
            checkStatus() {
              // supplierDataSource[this.data.index].rule1.ruleValue = 1;
              // orderConfigData[0].orderConsolidationConfigDTOList[
              //   this.data.index * 6
              // ].ruleValue = 1;
              return this.data.rule1.ruleValue ? true : false
            }
          }
        })
      }
    }
  },
  {
    width: '180',
    field: 'rule2',
    headerText: i18n.t('规则2-订单类型'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus" cssClass="checkbox-checkeditem"  :disabled="true"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          computed: {
            checkStatus() {
              // supplierDataSource[this.data.index].rule2.ruleValue = 1;
              // orderConfigData[0].orderConsolidationConfigDTOList[
              //   this.data.index * 6 + 1
              // ].ruleValue = 1;
              return this.data.rule2.ruleValue ? true : false
            }
          }
        })
      }
    }
  },
  {
    width: '200',
    field: 'rule3',
    headerText: i18n.t('规则11-相同订单编号'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            console.log('editFlag.isEditable', editFlag.isEditable)
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule3.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule3 = e.checked;
              // supplierDataSource[this.data.index].rule3.ruleValue = e.checked
              //   ? 1
              //   : 0;
              console.log('orderConfigData[0]', orderConfigData[0])
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 6 + 2
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '170',
    field: 'rule4',
    headerText: i18n.t('规则6-收货地址'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule4.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule4 = e.checked;
              // supplierDataSource[this.data.index].rule4.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 6 + 3
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule5',

    headerText: i18n.t('规则5-工厂'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule5.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule5 = e.checked;
              // supplierDataSource[this.data.index].rule5.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 6 + 4
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '380',
    field: 'rule6',
    headerText: i18n.t('送货单创建提前期（物流天数+提前期）小于(要求交期-今天)送货单才能创建成功'),
    headerTemplate: function () {
      return {
        template: Vue.component('uploadFile', {
          template: `
                  <div class="headers">
                    <span class="e-headertext">{{data.headerText}}</span>
                    <mt-tooltip :content="content" position="BottomCenter" target="#box">
                      <MtIcon id="box" name="icon_outline_prompt" />
                    </mt-tooltip>
                  </div>
                `,
          data() {
            return {
              content: function () {
                return {
                  template: Vue.component('demo', {
                    template: `
                    <div id="tooltip" ref="content" style="width:220px;font-size:12px;padding:6px 11px;">{{tips}}</div>`,
                    data() {
                      return {
                        tips: this.$t('（物流天数+提前期）小于(要求交期-今天)送货单才能创建成功'),
                        data: {}
                      }
                    }
                  })
                }
              },
              data: {}
            }
          }
        })
      }
    },

    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" :label="label"></mt-checkbox>
          <mt-inputNumber ref="inputNumber" width="120" height="26" :min="0" :max="999"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleInputNum" v-model="data.rule6.timeLimit"></mt-inputNumber>
          </div>`,
          data() {
            return { data: {}, isEditable: null, label: this.$t('间隔天数：') }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule6.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule6 = e.checked;
              // supplierDataSource[this.data.index].rule6.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 6 + 5
              ].ruleValue = e.checked ? 1 : 0
            },
            handleInputNum(e) {
              // supplierDataSource[this.data.index].rule6.timeLimit = e;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 6 + 5
              ].timeLimit = e
            }
          }
        })
      }
    }
  }
]
