<template>
  <!-- 送货单超时取消配置 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="true"
      class="template-height"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>

    <!-- 配置新增/编辑弹框 -->
    <config-list-dialog ref="configListDialog" @confirm="configDialogConfirm"></config-list-dialog>
    <!-- 配置导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { Toolbar, ColumnData, DialogActionType, ConfigStatus } from './config/constant'
import { formatTableColumnData, serializeList } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    ConfigListDialog: () => import('./components/configListDialog'),
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      uploadParams: {}, // 导入通知配置文件参数
      // 通知配置导入请求接口配置
      requestUrls: {
        templateUrlPre: 'deliveryConfig',
        templateUrl: 'postBuyerDeliveryOvertimeCancelExport', // 下载模板接口方法名
        uploadUrl: 'postBuyerDeliveryOvertimeCancelImport' // 上传接口方法名
      },
      componentConfig: [
        {
          toolbar: Toolbar,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.deliveryConfig.deliveryTimeoutConfig.list,
          grid: {
            lineSelection: 0, // 添加 checkbox 在第一列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData(ColumnData),
            dataSource: [
              // 测试数据
            ],
            asyncConfig: {
              // 获取超时自动取消配置列表
              url: `${BASE_TENANT}/buyerDeliveryOvertimeCancel/query`,
              // 序列化
              serializeList: serializeList
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.gridRef.getMtechGridRecords()
      const commonToolbar = [
        'ConfigAdd',
        'ConfigImport',
        'ConfigExport',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length == 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))
      if (e.toolbar.id === 'ConfigAdd') {
        // 配置-toolbar-新增
        this.configAdd()
      } else if (e.toolbar.id === 'ConfigDelete') {
        // 配置-删除
        this.handleConfigDelete(selectedId)
      } else if (e.toolbar.id === 'ConfigActive') {
        // 配置-启用
        this.handleConfigActive({
          data: selectRows,
          idList: selectedId,
          status: ConfigStatus.active
        })
      } else if (e.toolbar.id === 'ConfigInactive') {
        // 配置-停用
        this.handleConfigInactive({
          data: selectRows,
          idList: selectedId,
          status: ConfigStatus.inactive
        })
      } else if (e.toolbar.id === 'ConfigImport') {
        // 配置-导入
        this.showUploadExcel(true)
      } else if (e.toolbar.id === 'ConfigExport') {
        // 配置-导出
        this.postBuyerDeliveryOvertimeCancelExport()
      }
      console.log('-----', e.toolbar.id)
    },
    // CellTool
    handleClickCellTool(e) {
      if (e.tool.id === 'ConfigDelete') {
        // 配置-删除
        this.handleConfigDelete([e.data.id])
      } else if (e.tool.id === 'ConfigEdit') {
        // 配置-编辑
        this.handleConfigEdit(e.data)
      } else if (e.tool.id === 'ConfigActive') {
        // 配置-启用
        this.handleConfigActive({
          data: [e.data],
          idList: [e.data.id],
          status: ConfigStatus.active
        })
      } else if (e.tool.id === 'ConfigInactive') {
        // 配置-停用
        this.handleConfigInactive({
          data: [e.data],
          idList: [e.data.id],
          status: ConfigStatus.inactive
        })
      }
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 配置-删除
    handleConfigDelete(data) {
      this.postBuyerDeliveryOvertimeCancelDelete(data)
    },
    // 配置-编辑
    handleConfigEdit(data) {
      this.$refs.configListDialog.dialogInit({
        title: this.$t('编辑'),
        actionType: DialogActionType.Edit,
        selectData: data
      })
    },
    // 配置-启用
    handleConfigActive(args) {
      const { data, idList, status } = args
      const { valid, status: dataStatus } = this.verifyConfigActive(data)
      if (!valid) {
        this.$toast({
          content: this.$t('请选择相同状态的数据'),
          type: 'warning'
        })
      } else if (valid && dataStatus != ConfigStatus.inactive) {
        this.$toast({
          content: this.$t('请选择停用状态的数据'),
          type: 'warning'
        })
      } else if (valid && dataStatus == ConfigStatus.inactive) {
        // API
        this.postBuyerDeliveryOvertimeCancelStatus({
          idList,
          status
        })
      }
    },
    // 配置-停用
    handleConfigInactive(args) {
      const { data, idList, status } = args
      const { valid, status: dataStatus } = this.verifyConfigActive(data)
      if (!valid) {
        this.$toast({
          content: this.$t('请选择相同状态的数据'),
          type: 'warning'
        })
      } else if (valid && dataStatus != ConfigStatus.active) {
        this.$toast({
          content: this.$t('请选择启用状态的数据'),
          type: 'warning'
        })
      } else if (valid && dataStatus == ConfigStatus.active) {
        // API
        this.postBuyerDeliveryOvertimeCancelStatus({
          idList,
          status
        })
      }
    },
    // 验证配置的启用状态是否统一
    verifyConfigActive(data) {
      let valid = true
      let status = ''
      data.forEach((item, index) => {
        status = item.status
        if (item && data[index - 1] && item.status !== data[index - 1].status) {
          valid = false
        }
      })

      return { valid, status }
    },
    // 配置-toolbar-新增
    configAdd() {
      // 显示 配置 新增 弹框
      this.$refs.configListDialog.dialogInit({
        title: this.$t('新增'),
        actionType: DialogActionType.Add
      })
    },
    // 配置弹框 点击确认调用 API 成功
    configDialogConfirm() {
      // 刷新表格数据
      this.refreshColumns()
    },
    // 超时自动取消配置Excel导出
    postBuyerDeliveryOvertimeCancelExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.deliveryConfig.postBuyerDeliveryOvertimeCancelExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 修改超时自动取消配置状态
    postBuyerDeliveryOvertimeCancelStatus(args) {
      const { idList, status } = args
      const params = {
        idList,
        status
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDeliveryOvertimeCancelStatus(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 删除超时自动取消配置
    postBuyerDeliveryOvertimeCancelDelete(params) {
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDeliveryOvertimeCancelDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
