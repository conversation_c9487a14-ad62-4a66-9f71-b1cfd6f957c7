<!-- eslint-disable prettier/prettier -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="type" :label="$t('配置类型')">
        <mt-select
          v-model="formData.type"
          :data-source="ConfigTypeOptions"
          @change="typeChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="deliveryOrderType" :label="$t('送货单类型')">
        <mt-select
          v-model="formData.deliveryOrderType"
          :data-source="deliveryOrderOptions"
          @change="orderClick"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <debounce-filter-select
          v-model="formData.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 显示：配置类型=工厂+供应商 -->
      <mt-form-item
        v-if="formData.type === 1 || formData.type === 3"
        prop="supplierCode"
        :label="$t('供应商')"
      >
        <debounce-filter-select
          v-model="formData.supplierCode"
          :request="getSupplier"
          :data-source="supplierOptions"
          :fields="{ text: 'theCodeName', value: 'supplierCode' }"
          :value-template="supplierCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="supplierCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 显示：配置类型=工厂+品类 -->
      <mt-form-item v-if="formData.type === 5" prop="categoryCode" :label="$t('品类')">
        <debounce-filter-select
          v-model="formData.categoryCode"
          :request="getCategorys"
          :data-source="categoryOptions"
          :fields="{ text: 'theCodeName', value: 'categoryCode' }"
          :value-template="categroyValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="categoryCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="hour" :label="$t('小时')">
        <mt-input-number
          :min="NumberRules"
          :show-clear-button="true"
          :precision="0"
          :show-spin-button="false"
          v-model="formData.hour"
          :placeholder="$t('请输入')"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item
        prop="buyerOrgCode"
        v-show="formData.type === 3 || formData.type === 4"
        :label="$t('采购组')"
      >
        <debounce-filter-select
          v-model="formData.buyerOrgCode"
          :data-source="buyOptions"
          :request="getBuy"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="groupCodeValueTemplate"
          @change="buyerChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
/* eslint-disable prettier/prettier */
import {
  DialogActionType,
  ConfigStatus,
  ConfigType,
  ConfigTypeOptions,
  deliveryOrderOptions
} from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { RegExpMap } from '@/utils/constant'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    const { integerReg } = RegExpMap
    // 小时
    const hourValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入小时')))
      } else if (!integerReg.test(value)) {
        console.log(this.formData.deliveryOrderType, value)
        if (this.formData.deliveryOrderType === 6 && value === -1) {
          this.$refs.ruleForm.clearValidate(['hour'])
          callback()
        } else {
          callback(new Error(this.$t('请输入整数')))
        }
      } else {
        this.$refs.ruleForm.clearValidate(['hour'])
        callback()
      }
    }
    // 供应商
    const supplierCodeValidator = (rule, value, callback) => {
      if (!value && (this.formData.type == ConfigType.type1 || this.formData.type == 3)) {
        // 配置类型 == 工厂+供应商
        callback(new Error(this.$t('请选择供应商')))
      } else {
        this.$refs.ruleForm.clearValidate(['supplierCode'])
        callback()
      }
    }
    const categoryCodeValidator = (rule, value, callback) => {
      if (!value && this.formData.type === 5) {
        callback(new Error(this.$t('请选择品类')))
      } else {
        this.$refs.ruleForm.clearValidate(['categoryCode'])
        callback()
      }
    }
    const buyerOrgCodeValidator = (rule, value, callback) => {
      if (!value && (this.formData.type == 3 || this.formData.type == 4)) {
        // 配置类型 == 工厂+供应商
        callback(new Error(this.$t('请选择采购组')))
      } else {
        this.$refs.ruleForm.clearValidate(['supplierCode'])
        callback()
      }
    }
    return {
      categoryOptions: [],
      NumberRules: 0,
      ConfigType,
      ConfigTypeOptions,
      deliveryOrderOptions,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      siteOptions: [], // 工厂 下拉选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      buyerCodeValueTemplate: codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      }),
      supplierOptions: [], // 供应商 下列选项
      categroyValueTemplate: codeNameColumn({
        firstKey: 'categoryCode',
        secondKey: 'categoryName'
      }), // 品类
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }), // 供应商
      buyOptions: [],
      groupCodeValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 供应商
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 配置类型
        type: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        deliveryOrderType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 采购组
        buyerOrgCode: [{ required: true, validator: buyerOrgCodeValidator, trigger: 'blur' }],
        // 供应商
        supplierCode: [
          {
            required: true,
            validator: supplierCodeValidator,
            trigger: 'blur'
          }
        ],
        categoryCode: [
          {
            required: true,
            validator: categoryCodeValidator,
            trigger: 'blur'
          }
        ],
        // 小时
        hour: [{ required: true, validator: hourValidator, trigger: 'blur' }]
        // hour: [{ required: true, message: this.$t("请输入"), trigger: "blur" }],
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认 配置类型：新增
      formData: {
        id: '',
        buyerOrgCode: '',
        buyerOrgName: '',
        buyerOrgId: '',
        siteName: '', // 工厂
        siteId: '', // 工厂id
        siteCode: '', // 工厂编码

        categoryCode: '',
        categoryName: '',
        supplierName: '', // 供应商
        supplierId: '', // 供应商id
        supplierCode: '', // 供应商编码
        deliveryOrderType: '',
        hour: '', // 小时
        tenantId: '', // 租户id
        type: '', // 配置类型
        status: '', // 状态
        endTime: '', // 结束时间
        startTime: '' // 开始时间
      }
    }
  },
  mounted() {},

  methods: {
    orderClick(e = {}) {
      if (e?.itemData && e.itemData.value === 6) {
        this.NumberRules = -1
      } else {
        this.NumberRules = 0
      }
    },
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      this.getBuy({ text: '' })
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',

          siteName: '', // 工厂
          siteId: '', // 工厂id
          siteCode: '', // 工厂编码

          categoryCode: '',
          categoryName: '',
          supplierName: '', // 供应商
          supplierId: '', // 供应商id
          supplierCode: '', // 供应商编码
          deliveryOrderType: '',
          hour: '', // 小时
          tenantId, // 租户id
          type: ConfigType.type1, // 配置类型 默认 工厂+供应商
          status: ConfigStatus.active, // 配置状态 启用
          endTime: '', // 结束时间
          startTime: '' // 开始时间
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        console.log(selectData)
        formData = {
          id: selectData.id,

          siteName: selectData.siteName, // 工厂
          siteId: selectData.siteId, // 工厂id
          siteCode: selectData.siteCode, // 工厂编码
          categoryCode: selectData.categoryCode,
          categoryName: selectData.categoryName,

          supplierName: selectData.supplierName, // 供应商
          supplierId: selectData.supplierId, // 供应商id
          supplierCode: selectData.supplierCode, // 供应商编码
          buyerOrgCode: selectData.buyerOrgCode,
          deliveryOrderType: selectData.deliveryOrderType,
          buyerOrgName: selectData.buyerOrgName,
          buyerOrgId: selectData.buyerOrgId,
          hour: selectData.hour, // 小时
          tenantId, // 租户id
          type: selectData.type, // 配置类型:1-供应商+工厂；2-工厂
          status: selectData.status, // 状态
          endTime: selectData.endTime, // 结束时间
          startTime: selectData.startTime // 开始时间
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      console.log(this.formData)

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerDeliveryOvertimeCancelSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.siteId = itemData.id
        this.formData.siteCode = itemData.siteCode
        this.formData.siteName = itemData.siteName
      } else {
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
      }
    },
    buyerChange(e) {
      if (e.itemData) {
        console.log(e.itemData)
        this.formData.buyerOrgCode = e.itemData.groupCode
        this.formData.buyerOrgName = e.itemData.groupName
        this.formData.buyerOrgId = e.itemData.id
      } else {
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
      }
    },
    // 品类下拉change
    categoryCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.categoryCode = itemData.categoryCode
        this.formData.categoryName = itemData.categoryName
      } else {
        this.formData.categoryCode = ''
        this.formData.categoryName = ''
      }
    },
    // 供应商 change
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.supplierId = itemData.id
        this.formData.supplierCode = itemData.supplierCode
        this.formData.supplierName = itemData.supplierName
      } else {
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      }
    },
    // 保存超时自动取消配置
    postBuyerDeliveryOvertimeCancelSave() {
      const params = {
        id: this.formData.id || undefined, // 不传 id 就是新增
        // 工厂
        siteCode: this.formData.siteCode,
        siteId: this.formData.siteId,
        siteName: this.formData.siteName,
        // 供应商
        supplierName: this.formData.supplierName,
        supplierId: this.formData.supplierId,
        supplierCode: this.formData.supplierCode,
        buyerOrgCode: this.formData.buyerOrgCode,
        buyerOrgName: this.formData.buyerOrgName,
        buyerOrgId: this.formData.buyerOrgId,
        deliveryOrderType: this.formData.deliveryOrderType,
        categoryCode: this.formData.categoryCode,
        categoryName: this.formData.categoryName,
        hour: this.formData.hour, // 小时
        tenantId: this.formData.tenantId, // 租户id
        type: this.formData.type, // 配置类型:1-供应商+工厂；2-工厂
        status: this.formData.status, // 状态
        endTime: this.formData.endTime || undefined, // 结束时间
        startTime: this.formData.startTime || undefined // 开始时间
      }
      if (Number(params.hour) === 0) {
        this.$toast({ content: this.$t('小时不能为0'), type: 'warning' })

        return
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDeliveryOvertimeCancelSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },

    // 主数据 获取采购组
    getBuy(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.buyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    getCategorys(args) {
      const { text, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getCategoryfuzzy(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            list.map((item) => (item.value = item.categoryCode))
            this.categoryOptions = addCodeNameKeyInList({
              firstKey: 'categoryCode',
              secondKey: 'categoryName',
              list
            })
            if (setSelectData) {
              this.formData.categoryCode = null
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // siteCode 工厂
        this.postSiteFuzzyQuery({
          text: selectData.siteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.siteCode = selectData.siteCode
          }
        })
        // 获取品类
        this.getCategorys({
          text: selectData.categoryCode,
          setSelectData: () => {
            this.formData.categoryCode = selectData.categoryCode
          }
        })
        // supplierCode 供应商
        this.getSupplier({
          text: selectData.supplierCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.supplierCode = selectData.supplierCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-获取工厂
        this.postSiteFuzzyQuery({ text: undefined })
        // 主数据 获取供应商
        this.getSupplier({ text: undefined })
        // 获取 - 品类下拉信息
        this.getCategorys({ text: undefined })
      }
    },
    // 配置组合方式 change
    typeChange(e) {
      if (e.value === 1 || e.value === 2) {
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
        this.formData.buyerOrgId = ''

        this.$refs.ruleForm.clearValidate(['buyerOrgCode']) // 采购组
      }
      if (e.value == ConfigType.type2 || e.value == 4) {
        // 配置组合方式 工厂
        this.$refs.ruleForm.clearValidate(['supplierCode']) // 供应商
        // 供应商
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      } else if (e.value == ConfigType.type1) {
        // 配置组合方式 工厂+供应商
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
