import { i18n } from '@/main.js'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    permission: ['O_02_0554'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0555'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0556'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0557'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0558'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0559'],
    title: i18n.t('导出')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用 启用状态
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'type', // 配置类型
    fieldName: i18n.t('配置类型')
  },
  {
    fieldCode: 'siteCode', // 工厂 code-name
    fieldName: i18n.t('工厂')
    // siteName
    // siteId 工厂id
    // siteCode 工厂编码
  },
  {
    fieldCode: 'deliveryOrderType',
    fieldName: i18n.t('送货单类型'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('采购订单'),
        2: i18n.t('交货计划'),
        3: i18n.t('jit'),
        4: i18n.t('无需求'),
        5: i18n.t('vmi'),
        6: i18n.t('钢材')
      }
    }
    // siteName
    // siteId 工厂id
    // siteCode 工厂编码
  },
  {
    fieldCode: 'supplierCode', // 供应商 code-name
    fieldName: i18n.t('供应商')
    // supplierName
    // supplierId 供应商id
    // supplierCode 供应商编码
  },
  {
    fieldCode: 'categoryCode',
    fieldName: i18n.t('品类')
  },
  {
    fieldCode: 'buyerOrgCode', // 供应商 code-name
    fieldName: i18n.t('采购组')
    // supplierName
    // supplierId 供应商id
    // supplierCode 供应商编码
  },
  {
    fieldCode: 'hour', // 小时
    fieldName: i18n.t('小时')
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 Options
export const ConfigStatusOptions = [
  {
    // 启用
    text: ConfigStatusConst[ConfigStatus.active],
    value: ConfigStatus.active,
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    text: ConfigStatusConst[ConfigStatus.inactive],
    value: ConfigStatus.inactive,
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]

// 类型:1-供应商+工厂；2-工厂
export const ConfigType = {
  type1: 1, // 供应商+工厂
  type2: 2, // 工厂
  type3: 3, // 工厂
  type4: 4 // 工厂
}
// 配置类型
export const ConfigTypeText = {
  [ConfigType.type1]: i18n.t('供应商+工厂'),
  [ConfigType.type2]: i18n.t('工厂')
}
export const deliveryOrderOptions = [
  {
    text: i18n.t('采购订单 '),
    value: 1
  },
  {
    text: i18n.t('交货计划'),
    value: 2
  },
  {
    text: i18n.t('JIT'),
    value: 3
  },
  {
    text: i18n.t('无需求'),
    value: 4
  },
  {
    text: i18n.t('vmi'),
    value: 5
  },
  {
    text: i18n.t('钢材'),
    value: 6
  }
]

// 配置类型 Options

export const ConfigTypeOptions = [
  {
    // 供应商+工厂
    text: ConfigTypeText[ConfigType.type1],
    value: ConfigType.type1,
    cssClass: ''
  },
  {
    // 工厂
    text: ConfigTypeText[ConfigType.type2],
    value: ConfigType.type2,
    cssClass: ''
  },
  {
    // 供应商+工厂+采购组
    text: '供应商+工厂+采购组',
    value: 3,
    cssClass: ''
  },
  {
    // 工厂
    text: '工厂+采购组',
    value: 4,
    cssClass: ''
  },
  // 工厂 + 品类
  {
    text: '工厂+品类',
    value: 5,
    cssClass: ''
  }
]
