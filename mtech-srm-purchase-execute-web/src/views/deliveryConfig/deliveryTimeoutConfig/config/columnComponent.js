import Vue from 'vue'
import { ConfigType } from '../config/constant'

// 配置类型改变的：供应商 / 品类
export const textByGroupType = (args) => {
  const { dataKey } = args

  const template = () => {
    return {
      template: Vue.component('textByGroupType', {
        template: `
        <div>
          <div v-if="isShowData()"
          >{{componentValue}}</div>
          <div v-else>-</div>
        </div>`,
        data: function () {
          return {
            data: {},
            dataKey,
            componentValue: ''
          }
        },
        mounted() {
          this.componentValue = this.formatValue()
        },
        methods: {
          // 是否显示当前数据
          isShowData() {
            let isShow = false
            if (this.data.type == ConfigType.type2) {
              // 配置方式 == 工厂
              isShow = false
            } else if (this.dataKey === 'supplierCode' && this.data.type == ConfigType.type1) {
              // 配置方式 == 工厂+供应商
              isShow = true
            } else if (this.dataKey === 'supplierCode' && this.data.type == 3) {
              isShow = true
              // 配置类型: 工厂 + 品类
            } else if (this.dataKey === 'categoryCode' && this.data.type == 5) {
              isShow = true
            }

            return isShow
          },
          // 格式化数据
          formatValue() {
            const toCodeName = (args) => {
              const { code, name } = args
              if (code && name) {
                return `${code}-${name}`
              } else if (code) {
                return code
              } else if (name) {
                return name
              }
            }
            if (dataKey === 'supplierCode') {
              // 供应商
              const code = this.data.supplierCode
              const name = this.data.supplierName
              return toCodeName({ code, name })
            } else if (dataKey === 'categoryCode') {
              const code = this.data.categoryCode
              const name = this.data.categoryName
              return toCodeName({ code, name })
            } else {
              return this.data[dataKey]
            }
          }
        }
      })
    }
  }

  return template
}
