// 领料管理，创建领料单详情页 // VMI配置管理
<template>
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { headColumn } from './config/details.js'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  data() {
    return {
      pageConfig: [
        {
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          grid: {
            columnData: headColumn,
            dataSource: [
              {
                companyCode: '630847',
                companyName: this.$t('TCL空调器有限公司生产工厂'),
                warehousingCode: 'w20211227001',
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                WarehouseAddress: this.$t('深圳市福田区下梅林梅华路'),
                WarehouseType: this.$t('随着后勤'),
                isPassword: this.$t('是'),
                isScreening: this.$t('是'),
                stockStatus: this.$t('待检库存'),
                createPeople: 'jcs',
                createTime: '2021-12-28',
                isEffective: this.$t('是')
              }
            ],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/pe/business/configs",
            //   recordsPosition: "data",
            // },
            frozenColumns: 1
          }
        }
      ],
      headerInfo: {
        status: 1,
        createUserName: 'jcs',
        createTime: '2022-03-02',
        factory: this.$t('京东工厂'),
        supplierCode: '0001',
        companyDescribe: this.$t('描述'),
        warehouseCode: '0033333',
        warehouseDescribe: this.$t('描述'),
        remark: '',
        accept: false,
        confirm: false
      }
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle() {},
    // 表格头部操作
    handleClickToolBar() {},
    // 行内操作
    handleClickCellTool() {},
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
}
.set-country {
  height: 100%;
}
</style>
