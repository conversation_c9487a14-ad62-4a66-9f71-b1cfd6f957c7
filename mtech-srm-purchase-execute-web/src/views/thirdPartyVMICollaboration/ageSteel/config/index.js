import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export const columnObj = {
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '260',
      field: 'factoryCode',
      headerText: i18n.t('工厂'),
      allowEditing: false,
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.factoryCode, data?.factoryName)
      }
    },
    {
      width: '0',
      field: 'factoryName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierThird,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'warehouseCode',
      headerText: i18n.t('仓库'),
      searchOptions: MasterDataSelect.vmiThird,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.warehouseCode, data?.warehouseName)
      }
    },
    {
      width: '0',
      field: 'warehouseName',
      headerText: i18n.t('仓库名称'),
      ignore: true
    },
    {
      width: '150',
      field: 'stockStatus',
      headerText: i18n.t('库存状态'),
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('合格'), value: '合格' },
          { label: i18n.t('待检'), value: '待检' },
          { label: i18n.t('不合格'), value: '不合格' }
        ],
        fields: { text: 'label', value: 'value' }
      }
    },
    // {
    //   width: "260",
    //   field: "materialCode", //supplierCode
    //   headerText: i18n.t("物料"),
    //   searchOptions: MasterDataSelect.itemSupplier,
    //   valueAccessor: (field, data) => {
    //     return judgeFormatCodeName(data?.materialCode, data?.materialName);
    //   },
    // },
    {
      width: '130',
      field: 'materialCode', //supplierCode
      headerText: i18n.t('物料编码')
    },
    {
      width: '0130',
      field: 'materialName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '85',
      field: 'purchaseGroupCode', //purchaseGroupName
      headerText: i18n.t('采购组'),
      searchOptions: MasterDataSelect.businessCodeName,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.purchaseGroupCode, data?.purchaseGroupName)
      }
    },
    {
      width: '0',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组名称'),
      ignore: true
    },
    {
      width: '150',
      field: 'inTransitInventory',
      headerText: i18n.t('在途库存')
    },
    {
      width: '150',
      field: 'writeOffQty',
      headerText: i18n.t('已冲销库存')
    },
    {
      width: '150',
      field: 'deliversQuantity',
      headerText: i18n.t('已收货')
    },
    {
      width: '150',
      field: 'unconfirmedInstockQty',
      headerText: i18n.t('待确认入库数量')
    },
    {
      width: '150',
      field: 'invQty',
      headerText: i18n.t('当前库存数')
    },
    {
      width: '150',
      field: 'stockQty30',
      headerText: i18n.t('库龄小于30天库存数量')
    },
    {
      width: '150',
      field: 'stockQty3160',
      headerText: i18n.t('库龄31-60天库存数量')
    },
    {
      width: '150',
      field: 'stockQty61180',
      headerText: i18n.t('库龄61-180天库存数量')
    },
    {
      width: '150',
      field: 'stockQty180',
      headerText: i18n.t(' 库龄180天以上库存数量')
    }
  ]
}
