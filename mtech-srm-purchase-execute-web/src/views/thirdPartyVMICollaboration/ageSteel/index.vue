// 报表
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :permission-obj="permissionObj"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'

import { columnObj } from './config/index.js'

export default {
  data() {
    return {
      pageConfig: [
        {
          tab: { title: this.$t('库龄报表') },
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          // dataPermission: "a",
          // permissionCode: "T_02_0122",
          dataSource: [],
          useToolTemplate: false,
          gridId: '71754bcb-6898-40d0-9baa-65a545c11807',
          grid: {
            columnData: columnObj.headColumn,
            dataSource: [],
            asyncConfig: {
              url: '/statistics/tenant/vmiStock/logistics/stock-day-query'
            },
            frozenColumns: 1
          }
        }
      ],
      deliveryShow: false,

      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 头部操作
    handleClickToolBar(args) {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}

      if (args.toolbar.id === 'export1') {
        let obj = JSON.parse(
          sessionStorage.getItem('71754bcb-6898-40d0-9baa-65a545c11807')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.headColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.supplierLogisticsExcelExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
