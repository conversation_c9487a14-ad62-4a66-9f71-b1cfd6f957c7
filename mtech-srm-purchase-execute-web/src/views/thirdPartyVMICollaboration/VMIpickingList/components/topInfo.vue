<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[statusToClass(headerInfo.status), 'mr20']">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}
        {{ headerInfo.createTime | timeFormat }}
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.accept"
        :is-primary="true"
        @click="acceptBtn"
        >{{ $t('供方确认') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.endpoint"
        :is-primary="true"
        @click="endpointBtn"
        >{{ $t('供方端点') }}</mt-button
      >
      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item :label="$t('工厂')">
          <mt-select
            ref="businessRef"
            v-model="headerInfo.factory"
            :data-source="businessTypeList"
            :show-clear-button="false"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择工厂')"
            :disabled="true"
            @change="handleBusinessChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('原材料供应商编码')">
          <mt-select
            ref="businessRef"
            v-model="headerInfo.rawRaterial"
            :data-source="rawList"
            :show-clear-button="false"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择原材料供应商编码')"
            :disabled="true"
            @change="handleBusinessChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="rawRaterial" :label="$t('原材料供应商描述')">
          <mt-input
            v-model="headerInfo.rawRaterialDescribe"
            :disabled="true"
            :placeholder="$t('原材料供应商描述')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('VMI仓编码')">
          <mt-select
            ref="businessRef"
            v-model="headerInfo.warehouseCode"
            :data-source="warehouseList"
            :show-clear-button="false"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择VMI仓编码')"
            :disabled="true"
            @change="handleBusinessChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="warehouseDescribe" :label="$t('VMI仓描述')">
          <mt-input
            v-model="headerInfo.warehouseDescribe"
            :disabled="true"
            :placeholder="$t('VMI仓描述')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="receiveSupplierCode" :label="$t('供应商编码')">
          <mt-input
            v-model="headerInfo.supplierCode"
            :disabled="true"
            :placeholder="$t('供应商编码')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="receiveSupplierDescribe" :label="$t('供应商描述')">
          <mt-input
            v-model="headerInfo.companyDescribe"
            :disabled="true"
            :placeholder="$t('供应商描述')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('送货地址')">
          <mt-select
            ref="businessRef"
            v-model="headerInfo.shippingAddress"
            :data-source="addressList"
            :show-clear-button="false"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择VMI仓编码')"
            :disabled="true"
            @change="handleBusinessChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass, DeliveryTypeOptions } from '../config/constant.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      DeliveryTypeOptions,
      isExpand: true,
      rules: {},
      businessTypeList: [
        { itemName: 1, id: 1 },
        { itemName: 2, id: 2 }
      ],
      rawList: [
        { itemName: this.$t('第一个'), id: 1 },
        { itemName: this.$t('第二个'), id: 2 }
      ],
      warehouseList: [
        { itemName: this.$t('第一个'), id: 1 },
        { itemName: this.$t('第二个'), id: 2 }
      ],
      addressList: [
        { itemName: this.$t('北京'), id: 1 },
        { itemName: this.$t('郑州'), id: 2 }
      ]
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 接受
    acceptBtn() {
      this.$emit('acceptBtn')
    },
    // 确认
    endpointBtn() {
      this.$emit('endpointBtn')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    },
    handleBusinessChange() {
      debugger
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;
      .full-width {
        width: calc(100% - 10px) !important;
      }
    }
  }
}
</style>
