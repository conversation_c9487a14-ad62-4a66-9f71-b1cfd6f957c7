<template>
  <!-- 批量创建送货单-第三方 -->
  <div class="full-height pt20">
    <div class="top-info">
      <div class="header-box">
        <div class="middle-blank"></div>
        <!-- 右侧操作按钮 -->
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>
    <!-- 列模板 -->
    <mt-template-page
      id="supply-plan-supplier-table-container"
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { ColumnDataJit } from './config/create.js'
export default {
  components: {},
  data() {
    const batchDeliverySupplierData = JSON.parse(localStorage.getItem('asnList')) || [
      { orderCode: '099999' }
    ]
    const listArr = batchDeliverySupplierData.reduce((pre, now) => {
      if (now.orderDetail && now.orderDetail.siteName) delete now.orderDetail.siteName //删除内层工厂
      if (now.orderDetail) {
        pre.push({
          ids: now.id,
          lineNo: now.lineNo,
          orderCode: now.orderCode,
          message: now.message,
          count: now.count,
          success: now.success,
          companyName: now.companyName,
          vmiWarehouseCode: now.vmiWarehouseCode,
          warehouseStock: now.warehouseStock,
          shipType: now.shipType,
          itemDeliveryType: now.itemDeliveryType,
          preDeliveryQty: now.preDeliveryQty,
          waitCount: now.waitCount,
          requireDeliveryDate: now.requireDeliveryDate,
          buyerRemark: now.buyerRemark,
          supRemark: now.supRemark,
          siteName: now.siteName,
          ...now.orderDetail
        })
      } else {
        pre.push(now)
      }
      return pre
    }, [])
    return {
      pageConfig: [
        //配置tab
        {
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'submit',
                  icon: 'icon_table_batchacceptance',
                  title: this.$t('提交制造货单')
                },
                {
                  id: 'matching',
                  icon: 'icon_table_batchacceptance',
                  title: this.$t('重新匹配')
                }
              ], //新增  删除
              [
                'Filter',
                {
                  id: 'import',
                  icon: 'icon_table_batchacceptance',
                  title: this.$t('导入')
                },
                'Refresh',
                'Setting'
              ] //筛选   刷新  设置
            ]
          },
          grid: {
            columnData: ColumnDataJit,
            dataSource: listArr,
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  mounted() {
    console.log(this.pageConfig[0].grid.dataSource, 77777)
  },
  methods: {
    // 头部按钮操作
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length > 0 && e.toolbar.id === 'submit') {
        // 作废
        // this.redirectPage("purchase-execute/VMIpickingListCreate", {});
        const selectedRowData = e.grid.getSelectedRecords()
        const parameterObj = {
          itemList: []
        }
        let obj = null
        try {
          selectedRowData.forEach((item) => {
            if (item.success == 0) throw new Error(this.$t('存在未匹配的数据'))
            obj = {}
            obj.count = item.count
            obj.id = item.ids
            obj.lineNo = item.lineNo
            obj.orderCode = item.orderCode
            ;(obj.success = item.success), (obj.message = item.message)
            parameterObj.itemList.push(obj)
          })
        } catch (error) {
          this.$toast({
            content: this.$t('存在未匹配的数据'),
            type: 'error'
          })
          return false
        }
        // 提交制造货单
        this.$API.vmi.submitAsn(parameterObj).then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          }
        })
      } else if (e.grid.getSelectedRecords().length > 0 && e.toolbar.id === 'matching') {
        // 重新匹配
      } else if (e.grid.getSelectedRecords().length > 0 && e.toolbar.id === 'import') {
        // 导入
      } else if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id !== 'refreshDataByLocal') {
        return
      } else {
        // 刷新
      }
    },
    goBack() {
      this.$router.push({
        name: 'vmiPickingList'
      })
    },
    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addDimension'
      }
    },

    handleEdit(row) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        requestUrl: 'updateDimension',
        row: row
      }
    },

    handleDelete(id) {
      this.$dialog({
        data: {
          title: this.$t('删除审批流'),
          message: this.$t(
            '删除工作流将默认该节点工作流不启用，在相应节点则会默认审批自动通过。请慎重操作!'
          )
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.moduleConfig.deleteApprovalConfig({ configIds: id.join(',') }).then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.confirmSuccess()
            }
          })
        }
      })
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.moduleConfig.updateApprovalConfig({ ids: ids, statusId: flag }).then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.confirmSuccess()
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    },

    handleClickCellTool(e) {
      console.log(e, 'e')
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      console.log(flag, 'flag')
    },
    // 路由跳转
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-setting {
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-title {
    font-size: 14px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    text-indent: 10px;
    border-left: 5px solid #00469c;
    margin-bottom: 20px;
    border-radius: 2px 0 0 2px;
  }
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
</style>
