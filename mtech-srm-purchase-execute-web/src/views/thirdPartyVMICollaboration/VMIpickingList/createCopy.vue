<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>

    <!-- sourcing -->
  </div>
</template>

<script>
import { columnData, columnDataInfo } from './config/index.js'
export default {
  components: {},
  data() {
    return {
      pageConfig: [
        //配置tab
        {
          title: this.$t('头视图'),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'establish',
                  icon: 'icon_table_batchacceptance',
                  title: this.$t('创建送货单')
                }
              ],
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          grid: {
            columnData: columnData,
            // dataSource: [
            //   {
            //     businessTypeName: "w20211227001", //VMI领料单号
            //     status: 1, //状态
            //     plantCode: "630847", //工厂编号
            //     plantDescription: "TCL空调器有限公司生产工厂", //工厂描述
            //     rawMaterialSupplierCode: "G89001", //原材料供应商编码
            //     rawMaterialSupplierDescription:
            //       this.$t("广东克瑞达智能科技股份有限公司"), //原材料供应商描述
            //     VMIWarehouseCode: "0100001", //VMI仓编码
            //     VMIWarehouseDescription: "VMI红兴物流芜湖威灵电机", //VMI仓描述
            //     pickingSupplierCode: "2200101", //领料供应商编码
            //     pickingSupplierDescription: this.$t("广东新科科技股份有限公司"), //领料供应商描述
            //     shippingAddress: this.$t("深圳市福田区下梅林梅华路"), //送货地址
            //     preparationDate: "2021-12-28", //制单日期
            //     preparer: this.$t("许凯凯"), //制单人
            //   },
            // ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-pickup-order/logistic-page-query'
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('明细视图'),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'establish',
                  icon: 'icon_table_batchacceptance',
                  title: this.$t('创建送货单')
                }
              ], //新增  删除
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          grid: {
            columnData: columnDataInfo,
            // dataSource: [
            //   {
            //     businessTypeName: "222", //VMI领料单号
            //     status: 1, //状态
            //     plantCode: "630847", //工厂编号
            //     plantDescription: "TCL空调器有限公司生产工厂222", //工厂描述
            //     rawMaterialSupplierCode: "G89001222", //原材料供应商编码
            //     rawMaterialSupplierDescription:
            //       "广东克瑞达智能科技股份有限公司2222", //原材料供应商描述
            //     VMIWarehouseCode: "0100001222", //VMI仓编码
            //     VMIWarehouseDescription: "VMI红兴物流芜湖威灵电机222", //VMI仓描述
            //     pickingSupplierCode: "220010122", //领料供应商编码
            //     pickingSupplierDescription: "广东新科科技股份有限公司222", //领料供应商描述
            //     shippingAddress: "深圳市福田区下梅林梅华路222", //送货地址
            //     preparationDate: "2021-12-283333", //制单日期
            //     preparer: "许凯凯22", //制单人
            //   },
            // ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-pickup-order/logistic-item-page-query'
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  methods: {
    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addDimension'
      }
    },

    handleEdit(row) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        requestUrl: 'updateDimension',
        row: row
      }
    },

    handleDelete(id) {
      this.$dialog({
        data: {
          title: this.$t('删除审批流'),
          message: this.$t(
            '删除工作流将默认该节点工作流不启用，在相应节点则会默认审批自动通过。请慎重操作!'
          )
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.moduleConfig.deleteApprovalConfig({ configIds: id.join(',') }).then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.confirmSuccess()
            }
          })
        }
      })
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.moduleConfig.updateApprovalConfig({ ids: ids, statusId: flag }).then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.confirmSuccess()
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    },
    // 头部跳转
    handleClickToolBar(e) {
      let obj = {
        tabIndex: ''
      }
      if (e.tabIndex === 0) {
        obj.tabIndex = 0
      } else {
        obj.tabIndex = 1
      }
      if (e.toolbar.id === 'establish') {
        // 创建送货单
        this.redirectPage('purchase-execute/VMIpickingListCreate', obj)
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新方法
      } else {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },

    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        tabIndex: ''
      }
      if (e.tabIndex === 0) {
        obj.tabIndex = 0
      } else {
        obj.tabIndex = 1
      }
      if (e.field === 'businessTypeName') {
        this.redirectPage('purchase-execute/three-picking-details', obj)
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'submit') {
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('确认确认吗？')
          },
          success: () => {
            // TODO: 领料-供方-确认（列表-确认）
            this.$API.supplierCoordination.purOrderQueryConfirm({ id: e.data.id }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('提交成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            // TODO: 领料-供方-退回（列表-退回）
            // debugger
            this.$API.purchaseCoordination.VMIPickupOrderCancel({ id: e.data.id }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('退回成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      }
    },
    // 行内操作
    // handleClickCellTool(e) {
    //   if (e.tool.id == "submit") {
    //     // 启用
    //     this.$dialog({
    //       data: {
    //         title: this.$t("提交"),
    //         message: "确认提交吗？",
    //       },
    //       success: () => {
    //         if(e.tabIndex === 0){
    //           // 头视图提交
    //         }else{
    //           // 明细视图提交

    //         }
    //       },
    //     });
    //   } else if (e.tool.id == "confirm") {
    //     this.$dialog({
    //       data: {
    //         title: this.$t("确认"),
    //         message: "是否确认？",
    //       },
    //       success: () => {
    //         if(e.tabIndex === 0){
    //           // 头视图提交
    //         }else{
    //           // 明细视图提交

    //         }
    //       },
    //     });
    //   }
    // },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow() {},
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
