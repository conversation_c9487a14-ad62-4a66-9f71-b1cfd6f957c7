import Vue from 'vue'
import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
// 头视图
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'vmiOrderCode',
    headerText: i18n.t('VMI入库单号'),
    cellTools: []
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
        { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
        { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' },
        { value: 10, text: i18n.t('已退回'), cssClass: 'col-active' }
      ]
    }
    // cellTools: [
    //   {
    //     id: "submit",
    //     icon: "icon_list_enable",
    //     title: i18n.t("提交"),
    //     visibleCondition: (data) => {
    //       let isShow = false;
    //       if (data.status == 0) {
    //         isShow = true;
    //       }
    //       return isShow;
    //     },
    //   },
    //   {
    //     id: "confirm",
    //     icon: "icon_list_disable",
    //     title: i18n.t("确认"),
    //     visibleCondition: (data) => {
    //       let isShow = false;
    //       if (data.status == 1) {
    //         isShow = true;
    //       }
    //       return isShow;
    //     },
    //   },
    // ],
  },
  {
    width: '95',
    field: 'siteCode',
    headerText: i18n.t('工厂编号')
  },
  {
    width: '225',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '160',
    field: 'supplierCode',
    headerText: i18n.t('原材料供应商编码')
  },
  {
    width: '225',
    field: 'supplierName',
    headerText: i18n.t('原材料供应商名称')
  },
  {
    width: '105',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '115',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '140',
    field: 'processorCode',
    headerText: i18n.t('领料供应商编码')
  },
  {
    width: '200',
    field: 'processorName',
    headerText: i18n.t('领料供应商名称')
  },
  {
    width: '170',
    field: 'vmiWarehouseAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('制单日期')
  },
  {
    width: '80',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
export const columnDataInfo = () => {
  return [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI领料单号'),
      cellTools: []
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' },
          { value: 10, text: i18n.t('已退回'), cssClass: 'col-active' }
        ]
      }
    },
    {
      width: '95',
      field: 'siteCode',
      headerText: i18n.t('工厂编号')
    },
    {
      width: '225',
      field: 'siteName',
      headerText: i18n.t('工厂名称')
    },
    {
      width: '115',
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    },
    {
      width: '395',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '110',
      field: 'stockStatus',
      headerText: i18n.t('库存状态')
    },
    // {
    //   width: "110",
    //   field: "batchCode",
    //   headerText: i18n.t("批次/卷号"),
    // },
    // {
    //   width: "150",
    //   field: "countLimit",
    //   headerText: i18n.t("可领料数量"),
    // },
    {
      width: '110',
      field: 'checkCount',
      headerText: i18n.t('领料数量')
    },
    // {
    //   width: "140",
    //   field: "orderCode",
    //   headerText: i18n.t("关联采购订单号"),
    // },
    // {
    //   width: "150",
    //   field: "lineNo",
    //   headerText: i18n.t("关联采购订单行号"),
    // },
    {
      width: '70',
      field: 'itemUnit',
      headerText: i18n.t('单位')
    },
    {
      width: '100',
      field: 'itemUnit',
      headerText: i18n.t('采购组')
    },
    {
      width: '160',
      field: 'supplierCode',
      headerText: i18n.t('原材料供应商编码')
    },
    {
      width: '225',
      field: 'supplierName',
      headerText: i18n.t('原材料供应商名称')
    },
    {
      width: '115',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓编码')
    },
    {
      width: '120',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称')
    },
    {
      width: '150',
      field: 'processorCode',
      headerText: i18n.t('领料供应商编码')
    },
    {
      width: '225',
      field: 'processorName',
      headerText: i18n.t('领料供应商名称')
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('制单日期')
    },
    {
      width: '80',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'checkCount',
      headerText: i18n.t('发货数量'),
      // editType: "numericedit", //默认编辑类型之number
      template: () => {
        return {
          template: Vue.component('checkCount', {
            template: `<mt-input-number
                      :value="data.checkCount"
                      :show-clear-button="false"
                      @change="handleChange"
                      :placeholder="$t('请输入发货数量')">
                    </mt-input-number>`,
            data() {
              return { data: {} }
            },
            computed: {},
            methods: {
              handleChange(e) {
                this.$parent.$emit('changeCount', {
                  index: Number(this.data.index),
                  key: 'checkCount',
                  value: this.data,
                  val: e
                })
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      field: 'requireDeliveryDate',
      headerText: i18n.t('要求交货日期'),
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && `${e}`.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd HH:MM:SS',
              value: e
            })
          } else {
            return '-'
          }
        }
      }
    }
  ]
}
