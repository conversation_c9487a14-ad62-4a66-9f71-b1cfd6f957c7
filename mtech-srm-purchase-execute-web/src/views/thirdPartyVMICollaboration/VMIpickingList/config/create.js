// import Vue from "vue";
// 头视图
import { i18n } from '@/main.js'
export const ColumnDataJit = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'itemNo',
    headerText: i18n.t('序号'),
    ignore: true
  },
  {
    field: 'success', // 可创建送货单
    headerText: i18n.t('可创建送货单'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'message', // 无法创建原因
    headerText: i18n.t('无法创建原因')
  },
  {
    field: 'orderCode', // 关联采购订单 订单号
    headerText: i18n.t('关联采购订单')
  },
  // {
  //   field: "lineNo", // 关联采购订单行号 行号
  //   headerText: i18n.t("关联采购订单行号"),
  // },
  {
    field: 'siteName', // 工厂
    headerText: i18n.t('工厂')
  },
  {
    field: 'itemCode', // 物料号 品项编码
    headerText: i18n.t('物料号')
  },
  {
    field: 'itemName', // 物料名称
    headerText: i18n.t('物料名称')
  },
  {
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓库')
  },
  {
    field: 'warehouseStock',
    headerText: i18n.t('VMI库存')
  },
  {
    field: 'shipType', // 是否直送
    headerText: i18n.t('是否直送'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
    }
  },
  {
    field: 'itemDeliveryType', // 交货方式
    headerText: i18n.t('交货方式'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('采购订单'),
        2: i18n.t('交货计划'),
        3: i18n.t('jit'),
        4: i18n.t('无需求'),
        5: i18n.t('vmi'),
        6: i18n.t('钢材')
      }
    }
  },
  {
    field: 'companyName', // 公司
    headerText: i18n.t('公司')
  },
  {
    field: 'preDeliveryQty', // 供应商
    headerText: i18n.t('未清订单数量')
  },
  {
    field: 'waitCount', // 采购组
    headerText: i18n.t('待发货数量')
  },
  {
    field: 'requireDeliveryDate', // 关联销售订单号 TODO
    headerText: i18n.t('交货日期')
  },
  {
    field: 'buyerRemark', // 关联销售订单行号 TODO
    headerText: i18n.t('采购方备注')
  },
  {
    field: 'supRemark', // BOM号
    headerText: i18n.t('供应商备注')
  },
  {
    field: 'count', // 工序名称
    headerText: i18n.t('此次发货数量')
  }
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "150",
    field: 'businessTypeName',
    headerText: i18n.t('序号'),
    ignore: true
    // cellTools: ["edit", "delete"],
  },
  {
    // width: "150",
    field: 'status',
    headerText: i18n.t('可创建送货单'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('可创建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('不可创建'), cssClass: 'col-active' }
      ]
    }
  },
  {
    // width: "150",
    field: 'plantCode',
    headerText: i18n.t('工厂编号')
  },
  {
    // width: "150",
    field: 'plantDescription',
    headerText: i18n.t('工厂描述')
  },
  {
    field: 'rawMaterialSupplierCode',
    headerText: i18n.t('原材料供应商编码')
  },
  {
    // width: "150",
    field: 'rawMaterialSupplierDescription',
    headerText: i18n.t('原材料供应商描述')
  },
  {
    // width: "150",
    field: 'VMIWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    // width: "150",
    field: 'VMIWarehouseDescription',
    headerText: i18n.t('VMI仓描述')
  },
  {
    // width: "150",
    field: 'pickingSupplierCode',
    headerText: i18n.t('领料供应商编码')
  },
  {
    // width: "150",
    field: 'pickingSupplierDescription',
    headerText: i18n.t('领料供应商描述')
  },
  {
    // width: "150",
    field: 'shippingAddress',
    headerText: i18n.t('送货地址')
  },
  {
    // width: "150",
    field: 'preparationDate',
    headerText: i18n.t('制单日期')
  },
  {
    // width: "150",
    field: 'preparer',
    headerText: i18n.t('制单人')
  }
]
export const columnDataInfo = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "150",
    field: 'businessTypeName',
    headerText: i18n.t('VMI领料单号'),
    cellTools: ['edit', 'delete']
  },
  {
    // width: "150",
    field: 'status',
    headerText: i18n.t('状态')
  },
  {
    // width: "150",
    field: 'plantCode',
    headerText: i18n.t('工厂编号')
  },
  {
    // width: "150",
    field: 'plantDescription',
    headerText: i18n.t('工厂描述')
  },
  {
    field: 'rawMaterialSupplierCode',
    headerText: i18n.t('原材料供应商编码')
  },
  {
    // width: "150",
    field: 'rawMaterialSupplierDescription',
    headerText: i18n.t('原材料供应商描述')
  },
  {
    // width: "150",
    field: 'VMIWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    // width: "150",
    field: 'VMIWarehouseDescription',
    headerText: i18n.t('VMI仓描述')
  },
  {
    // width: "150",
    field: 'pickingSupplierCode',
    headerText: i18n.t('领料供应商编码')
  },
  {
    // width: "150",
    field: 'pickingSupplierDescription',
    headerText: i18n.t('领料供应商描述')
  },
  {
    // width: "150",
    field: 'shippingAddress',
    headerText: i18n.t('送货地址')
  },
  {
    // width: "150",
    field: 'preparationDate',
    headerText: i18n.t('制单日期')
  },
  {
    // width: "150",
    field: 'preparer',
    headerText: i18n.t('制单人')
  }
]
