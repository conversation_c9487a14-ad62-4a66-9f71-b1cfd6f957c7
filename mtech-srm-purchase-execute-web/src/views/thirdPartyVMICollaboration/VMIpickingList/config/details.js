import { i18n } from '@/main.js'
// VMI配置管理
export const headColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'companyCode',
    headerText: i18n.t('公司编号')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '150',
    field: 'warehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'warehouseDescribe',
    headerText: i18n.t('VMI仓描述')
  },
  {
    width: '150',
    field: 'WarehouseAddress',
    headerText: i18n.t('仓库地址')
  },
  {
    width: '150',
    field: 'WarehouseType',
    headerText: i18n.t('仓库类型')
  },
  {
    width: '150',
    field: 'isPassword',
    headerText: i18n.t('是否开启密码管理')
  },
  {
    width: '150',
    field: 'isScreening',
    headerText: i18n.t('是否需要IQC判定')
  },
  {
    width: '150',
    field: 'stockStatus',
    headerText: i18n.t('库存状况')
  },
  {
    width: '150',
    field: 'createPeople',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建日期')
  },
  {
    width: '150',
    field: 'isEffective',
    headerText: i18n.t('是否有效')
  }
]
