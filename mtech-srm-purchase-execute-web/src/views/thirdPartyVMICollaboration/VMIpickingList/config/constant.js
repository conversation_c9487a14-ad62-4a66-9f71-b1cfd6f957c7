import { i18n } from '@/main.js'
export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Bottom'
}
// import cellChanged from "@/components/normalEdit/cellChanged";
import { TextBox, NumericTextBox } from '@syncfusion/ej2-inputs'
import { Query } from '@syncfusion/ej2-data'
const numberFields = ['normalNumber1', 'normalNumber2', 'resNumber']
var wholeObj = {}
numberFields.forEach((item) => {
  wholeObj[`${item}Ele`] = null
  wholeObj[`${item}Obj`] = null
})

var commonEdit = (params) => {
  // type:单元格的类型 等于ej2包含的类型，默认字符串类型
  // field: 列的field，必填
  // pld：placeholder，可不填
  // canEdit：是否可以编辑，默认可以
  // required: 是否必填，默认非必填。。必填时给cssClass赋值isRequired
  // callback: 回调（按照业务逻辑来）
  // calc：计算参数，仅举例（按照业务逻辑来）
  let {
    type = 'stringedit',
    field,
    pld,
    canEdit = true,
    required = false,
    // callback,
    calc
  } = params
  return {
    create: () => {
      wholeObj[`${field}Ele`] = document.createElement('input')
      return wholeObj[`${field}Ele`]
    },
    read: () => {
      return wholeObj[`${field}Obj`].value
    },
    destroy: () => {
      wholeObj[`${field}Obj`].destroy()
    },
    write: (args) => {
      console.log(args)
      switch (type) {
        case 'stringedit':
          wholeObj[`${field}Obj`] = new TextBox({
            enabled: canEdit,
            cssClass: required ? 'isRequired' : null,
            value: args.rowData[args.column.field],
            placeholder: pld,
            floatLabelType: 'Never',
            change: (e) => {
              console.log(i18n.t('我改变了'), calc, e, wholeObj)
            }
          })
          break
        case 'numericedit':
          wholeObj[`${field}Obj`] = new NumericTextBox({
            enabled: canEdit,
            cssClass: required ? 'isRequired' : null,
            value: args.rowData[args.column.field],
            placeholder: pld,
            floatLabelType: 'Never',
            change: (e) => {
              // console.log(i18n.t("我改变了"), calc, e, wholeObj);
              if (calc && calc.length) {
                // 以简单乘法计算为例
                if (wholeObj[`${calc[0]}Obj`] && wholeObj[`${calc[1]}Obj`]) {
                  wholeObj[`${calc[1]}Obj`].value = wholeObj[`${calc[0]}Obj`].value * e.value
                }
              }
            }
          })
          break
        default:
          break
      }
      wholeObj[`${field}Obj`].appendTo(wholeObj[`${field}Ele`])
    }
  }
}
let currencyData = [
  {
    currencyName: i18n.t('测试1'),
    id: '01'
  },
  {
    currencyName: i18n.t('测试2'),
    id: '02'
  }
]
// tab
export const Tab = {
  list: 1, // 物料信息
  details: 2 // 物流信息
}

// 送货类型:1-关联采购订单,2-无采购订单
export const DeliveryType = {
  linked: 1, // 关联采购订单
  notLink: 2 // 无采购订单
}
// 送货类型 text
export const DeliveryTypeText = {
  [DeliveryType.linked]: i18n.t('关联采购订单'),
  [DeliveryType.notLink]: i18n.t('无采购订单')
}
// 送货类型 对应的 Options
export const DeliveryTypeOptions = [
  {
    // 关联采购订单
    value: DeliveryType.linked,
    text: DeliveryTypeText[DeliveryType.linked],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.notLink,
    text: DeliveryTypeText[DeliveryType.notLink],
    cssClass: ''
  }
]

// 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const Status = {
  beConfirmed: 1, // 待确认
  cancelled: 2, // 已取消
  returned: 3, // 已退回
  received: 4, // 已接收
  new: 5 // 已关闭
}
// 状态 text
export const StatusText = {
  [Status.beConfirmed]: i18n.t('待确认'),
  [Status.cancelled]: i18n.t('已取消'),
  [Status.returned]: i18n.t('已退回'),
  [Status.received]: i18n.t('已接收'),
  [Status.new]: i18n.t('新建')
}
// 状态 对应的 css class
export const StatusCssClass = {
  [Status.beConfirmed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.returned]: 'col-active',
  [Status.received]: 'col-active',
  [Status.new]: 'col-inactive'
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    // 新建
    value: Status.new,
    text: StatusText[Status.new],
    cssClass: StatusCssClass[Status.new]
  },
  {
    // 发货中
    value: Status.shipping,
    text: StatusText[Status.shipping],
    cssClass: StatusCssClass[Status.shipping]
  },
  {
    // 已完成
    value: Status.completed,
    text: StatusText[Status.completed],
    cssClass: StatusCssClass[Status.completed]
  },
  {
    // 已取消
    value: Status.cancelled,
    text: StatusText[Status.cancelled],
    cssClass: StatusCssClass[Status.cancelled]
  },
  {
    // 已关闭
    value: Status.closed,
    text: StatusText[Status.closed],
    cssClass: StatusCssClass[Status.closed]
  }
]

// 发货方式:1-快递配送，2-物流配送
export const ShippingType = {
  express: 1, // 快递配送
  logistics: 2 // 物流配送
}
// 发货方式 text
export const ShippingTypeText = {
  [ShippingType.express]: i18n.t('快递配送'),
  [ShippingType.logistics]: i18n.t('物流配送')
}
// 发货方式 对应的 Options
export const ShippingTypeOptions = [
  {
    // 快递配送
    value: ShippingType.express,
    text: ShippingTypeText[ShippingType.express],
    label: ShippingTypeText[ShippingType.express],
    cssClass: ''
  },
  {
    // 物流配送
    value: ShippingType.logistics,
    text: ShippingTypeText[ShippingType.logistics],
    label: ShippingTypeText[ShippingType.logistics],
    cssClass: ''
  }
]

// 物料信息 表格列数据
export const ColumnDataTab1 = [
  {
    fieldCode: 'rowNumber', // 行号
    fieldName: i18n.t('行号'),
    allowEditing: false
  },
  {
    fieldCode: 'materialCode', // 物料编码
    fieldName: i18n.t('物料编码'),
    allowEditing: false
  },
  {
    fieldCode: 'materialDescribe', // 物料描述
    fieldName: i18n.t('物料描述'),
    allowEditing: false
  },
  {
    fieldCode: 'tolerance', // 库存状态
    fieldName: i18n.t('容差'),
    allowEditing: false
  },
  {
    fieldCode: 'quantityNumber', // 批次/卷号
    fieldName: i18n.t('货品数量'),
    allowEditing: false
  },
  {
    fieldCode: 'receivingNumber', // 退货数量
    fieldName: i18n.t('收货数量'),
    allowEditing: false
  },
  {
    fieldCode: 'unitName', // 单位
    fieldName: i18n.t('单位'),
    allowEditing: false
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组'),
    allowEditing: false
  },
  {
    fieldCode: 'itemName', // 行备注
    fieldName: i18n.t('行备注'),
    allowEditing: false
  },
  {
    fieldCode: 'qualityResult', // 行备注
    fieldName: i18n.t('质检结果'),
    allowEditing: false
  },
  {
    fieldCode: 'unqualifiedClassification', // 行备注
    fieldName: i18n.t('不合格分类'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: currencyData,
        fields: { value: 'currencyName', text: 'currencyName' },
        query: new Query(),
        actionComplete: () => false,
        change: (e) => {
          console.log('我是下拉的change事件', e)
        }
      }
    }
  },
  {
    fieldCode: 'qualityRemarks', // 行备注
    fieldName: i18n.t('质检备注'),
    edit: commonEdit({
      field: 'qualityRemarks',
      pld: i18n.t('请输入质检备注')
    })
    // edit: commonEdit({
    //   type: "numericedit",
    //   field: "normalIpt",
    //   pld: "数字1请输入",
    //   calc: ["normalNumber2", "resNumber"],
    // }),
  }

  // {
  //   fieldCode: "orderCode", // 关联采购订单号
  //   fieldName: i18n.t("关联采购订单号"),
  //   allowEditing: false,
  // },
  // {
  //   fieldCode: "lineNo", // 关联采购订单行号
  //   fieldName: i18n.t("关联采购订单行号"),
  //   allowEditing: false,
  // },
  // {
  //   fieldCode: "encryptionNumber", // 关联采购订单行号
  //   fieldName: i18n.t("批次/卷号"),
  //   allowEditing: false,
  // },
  // {
  //   fieldCode: "vehicleNumber", // 关联采购订单行号
  //   fieldName: i18n.t("车号/船号"),
  //   allowEditing: false,
  // },
]
