import { shipmentsNum, timeDate, codeNameColumn } from './columnComponent'
import { i18n } from '@/main.js'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: col.width || '150'
    }
    if (col.fieldCode === 'theNumberOfShipments') {
      // 发货数量
      defaultCol.template = shipmentsNum()
      defaultCol.ignore = true
      defaultCol.width = '250'
    } else if (col.fieldCode === 'orderCode') {
      // 关联采购订单
      defaultCol.width = '250'
    } else if (col.fieldCode === 'itemNo') {
      // 关联采购订单行号
      defaultCol.width = '153'
    } else if (col.fieldCode === 'saleOrderRowCode') {
      // 关联销售订单行号
      defaultCol.width = '200'
    } else if (col.fieldCode === 'saleOrderNo') {
      // 关联销售订单号
      defaultCol.width = '140'
    } else if (col.fieldCode === 'checkDate') {
      // 需求日期 requiredDeliveryDate string
      // 确认日期 checkDate
      defaultCol.width = '95'
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    } else if (col.fieldCode === 'planDeliveryDate') {
      // 上版交货日期
      defaultCol.width = '160'
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    } else if (col.fieldCode === 'planDeliveryTime') {
      // 上版交货日期
      defaultCol.width = '160'
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    } else if (col.fieldCode === 'renewTime') {
      // JIT更新时间
      defaultCol.width = '160'
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    } else if (col.fieldCode === 'remainingQty') {
      // 剩余可创建数量
      defaultCol.width = '145'
    } else if (['jit', 'isJit'].includes(col.fieldCode)) {
      // 是否JIT
      defaultCol.valueConverter = {
        type: 'map',
        map: { 0: i18n.t('否'), 1: i18n.t('是') }
      }
    } else if (['isItemBatch'].includes(col.fieldCode)) {
      // 是否JIT
      defaultCol.valueConverter = {
        type: 'map',
        map: { 0: i18n.t('否'), 1: i18n.t('是') }
      }
    } else if (col.fieldCode === 'deliveryMethod') {
      // 是否直送
      defaultCol.valueConverter = {
        type: 'map',
        map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
      }
    } else if (col.fieldCode === 'outsourcedType') {
      // 委外方式
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('销售非委外'),
          1: i18n.t('销售委外'),
          2: i18n.t('非委外')
        }
      }
      defaultCol.searchOptions = {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('销售非委外'), value: 0 },
          { label: i18n.t('销售委外'), value: 1 },
          { label: i18n.t('非委外'), value: 2 }
        ],
        fields: { text: 'label', value: 'value' }
      }
    } else if (col.fieldCode === 'orderLineNo') {
      // 关联采购订单行号
      defaultCol.width = '153'
    } else if (col.fieldCode === 'warehouse') {
      // 交货库存地点 code+name 采购订单tab
      defaultCol.width = '220'
      defaultCol.template = codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouse'
      })
    } else if (col.fieldCode === 'warehouseName') {
      // 交货库存地点 code+name 交货计划tab
      defaultCol.width = '220'
      // defaultCol.template = codeNameColumn({
      //   firstKey: "warehouseCode",
      //   secondKey: "warehouseName",
      // });
    } else if (col.fieldCode === 'senderPhone') {
      // 送货联系电话 code+name 交货计划tab
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'senderPhone',
        secondKey: 'senderAddress'
      })
    }
    colData.push(defaultCol)
  })

  return colData
}
