import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

// tab 的 index
export const TabIndex = {
  deliverySchedule: 0, // 交货计划
  jit: 1, // 叫料计划
  purchaseOrder: 2 // 采购订单
}

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// Toolbar 按钮
export const Toolbar = {
  // 交货计划
  [TabIndex.deliverySchedule]: [
    {
      id: 'BatchPreCreateDelivery',
      icon: 'icon_solid_Createproject',
      permission: ['O_02_1116'],
      title: i18n.t('批量创建送货单')
    },
    // 不需要这个按钮，只有供方-收发货-交货计划tab才有
    // {
    //   id: "CreateNoOrder",
    //   icon: "icon_table_delivery_note",
    //   permission: ["O_02_1117"],
    //   title: i18n.t("创建无采购订单送货单"),
    // },
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_1118'],
      title: i18n.t('导出')
    }
  ],
  // 叫料计划
  [TabIndex.jit]: [
    {
      id: 'BatchPreCreateDelivery',
      icon: 'icon_solid_Createproject',
      permission: ['O_02_1163'],
      title: i18n.t('批量创建送货单')
    },
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_1159'],
      title: i18n.t('导出')
    }
  ],
  // 订单供货计划
  [TabIndex.purchaseOrder]: [
    {
      id: 'BatchPreCreateDelivery',
      icon: 'icon_solid_Createproject',
      permission: ['O_02_1120'],
      title: i18n.t('批量创建送货单')
    },
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_1121'],
      title: i18n.t('导出')
    }
  ]
}

// tab1 交货计划 表格列数据
export const ColumnDataTab2 = [
  {
    fieldCode: 'companyCode', // 公司编码
    fieldName: i18n.t('公司编码'),
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    fieldCode: 'companyName', // 公司
    fieldName: i18n.t('公司名称'),
    ignore: true
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    fieldCode: 'siteName', // 物料号 品项编码
    fieldName: i18n.t('工厂名称'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码
    fieldName: i18n.t('物料号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'orderCode', // 关联采购订单 采购订单号
    fieldName: i18n.t('关联采购订单'),
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    fieldCode: 'warehouseKeeper', // 关联采购订单 采购订单号
    fieldName: i18n.t('仓管员')
  },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期
    fieldName: i18n.t('需求日期'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.requiredDeliveryDate}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'timeInfoTimestamp'
    }
  },
  {
    fieldCode: 'quantity', // 需求数量
    fieldName: i18n.t('需求数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'remainingQty', // 剩余可创建数量
    fieldName: i18n.t('剩余可创建数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'theNumberOfShipments', // 发货数量 前端定义
    fieldName: i18n.t('发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'receiveQty', // 收货数量
    fieldName: i18n.t('收货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty', // 在途数量
    fieldName: i18n.t('在途数量'),
    allowFiltering: false,
    ignore: true
  },

  {
    fieldCode: 'warehouseCode', // 交货库存地点 库存地点 warehouseCode
    fieldName: i18n.t('库存地点编号'),
    searchOptions: {
      ...MasterDataSelect.stockSupplierAddressName,
      operator: 'in',
      renameField: 'warehouseCode'
    },
    allowFiltering: false
    // ignore: true,
  },
  {
    fieldCode: 'warehouseName', // 交货库存地点 库存地点 warehouseCode
    fieldName: i18n.t('库存地点名称'),
    allowFiltering: false,
    searchOptions: {
      operator: 'likeright'
    }
    // ignore: true,
  },
  {
    fieldCode: 'stockQty', // 库存量
    fieldName: i18n.t('库存量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'transferPlanName', // 计划员
    fieldName: i18n.t('计划员'),
    allowFiltering: false,
    ignore: true
  },
  {
    width: '260',
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierThird,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    fieldCode: 'supplierName', // 物料号 品项编码
    fieldName: i18n.t('供应商名称'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'planGroupName', // 计划组
    fieldName: i18n.t('计划组'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    },
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.buyerOrgCode, data?.buyerOrgName)
    }
  },
  {
    fieldCode: 'buyerOrgName', // 物料号 品项编码
    fieldName: i18n.t('采购组名称')
  },
  {
    fieldCode: 'projectTextBatch', // 项目文本批次
    fieldName: i18n.t('项目文本批次'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'outsourcedType', // 委外方式
    fieldName: i18n.t('委外方式')
  },
  {
    fieldCode: 'deliveryMethod', // 是否直送 配送方式 0直送1非直送
    fieldName: i18n.t('是否直送'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'associatedNumber', // 关联工单号
    fieldName: i18n.t('关联工单号'),
    allowFiltering: false,
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    }
  },
  {
    fieldCode: 'serialNumber', // 序列号
    fieldName: i18n.t('序列号')
  },
  {
    fieldCode: 'bom', // BOM号
    fieldName: i18n.t('BOM号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'saleOrderNo', // 关联销售订单号
    fieldName: i18n.t('关联销售订单号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'processName', // 工序名称
    fieldName: i18n.t('工序名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productCode', // 工序产品代码
    fieldName: i18n.t('工序产品代码'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'picNo', // 图号
    fieldName: i18n.t('图号'),
    allowFiltering: false,
    ignore: true
  },
  // {
  //   fieldCode: "orderLineNo", // 关联采购订单行号 行号
  //   fieldName: i18n.t("关联采购订单行号"),
  //   allowFiltering: false,
  //   ignore: true,
  // },
  {
    width: '260',
    fieldCode: 'processorCode',
    fieldName: i18n.t('加工商'),
    searchOptions: MasterDataSelect.supplierThird,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.processorCode, data?.processorName)
    }
  },
  {
    fieldCode: 'processorName', // 物料号 品项编码
    fieldName: i18n.t('加工商名称'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'systemRemark', // 系统备注
    fieldName: i18n.t('系统备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supplierRemark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'isJit', // 是否JIT 	是否jit 0否1是
    fieldName: i18n.t('是否JIT'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'isItemBatch', // 是否批次来料 0否1是
    fieldName: i18n.t('是否批次来料'),
    allowFiltering: false
  },
  {
    fieldCode: 'supplierCheckUser', // 供应商确认人
    fieldName: i18n.t('供应商确认人'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'checkDate', // 确认日期
    fieldName: i18n.t('确认日期'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productLine', // 生产线
    fieldName: i18n.t('生产线'),
    allowFiltering: false,
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    }
  },
  {
    fieldCode: 'batch', // 批量
    fieldName: i18n.t('批量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'senderPhone', // 送货联系电话 senderAddress
    fieldName: i18n.t('送货联系电话+送货地址'),
    allowFiltering: false,
    ignore: true
  }
  // {
  //   fieldCode: "senderAddress", // 送货地址
  //   fieldName: i18n.t("送货地址"),
  // },
]

// tab2 叫料计划 表格列数据
export const ColumnDataTab3 = [
  {
    fieldCode: 'companyCode',
    fieldName: i18n.t('公司编码'),
    searchOptions: { ...MasterDataSelect.companySupplier }
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('公司名称'),
    ignore: true
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂'),
    searchOptions: {
      ...MasterDataSelect.factorySupplierAddress
    }
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂编码'),
    ignore: true
  },
  {
    fieldCode: 'subSiteCode',
    fieldName: i18n.t('分厂'),
    searchOptions: {
      ...MasterDataSelect.subSiteCodeSupplier,
      url: '/srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=subSiteCode'
    }
  },
  {
    fieldCode: 'subSiteAddressCode',
    fieldName: i18n.t('分厂库存地点'),
    searchOptions: {
      ...MasterDataSelect.subSiteAddressSupplier,
      url: '/srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=subSiteAddressCode'
    }
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'workOrder',
    fieldName: i18n.t('生产工单'),
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    }
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称'),
    ignore: true
  },
  {
    fieldCode: 'rowCode',
    fieldName: i18n.t('JIT编号')
  },
  {
    fieldCode: 'orderCode',
    fieldName: i18n.t('关联采购订单'),
    searchOptions: {
      ...MasterDataSelect.orderCodeSupplier,
      url: '/srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=orderCode'
    }
  },
  {
    fieldCode: 'requiredDeliveryDate',
    fieldName: i18n.t('需求日期'),
    allowFiltering: false,
    template: () => {
      return {
        template: {
          template: `<div>{{data.requiredDeliveryDate}}{{transformValue()}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            transformValue() {
              let _flag = [1, '1'].includes(this.data.isJit)
              if (_flag) {
                return '-' + this.data.requiredDeliveryTime
              }
              return ''
            }
          }
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'deliveryDate'
    }
  },
  {
    fieldCode: 'quantity',
    fieldName: i18n.t('需求数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'remainingQty',
    fieldName: i18n.t('剩余可创建数量'),
    allowFiltering: false,
    type: 'number'
  },
  {
    fieldCode: 'theNumberOfShipments',
    fieldName: i18n.t('发货数量')
  },
  {
    fieldCode: 'receiveQty',
    fieldName: i18n.t('收货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty',
    fieldName: i18n.t('在途数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'warehouseName',
    fieldName: i18n.t('库存地点编号+库存地点名称'),
    allowFiltering: false
  },
  {
    fieldCode: 'transferPlanName',
    fieldName: i18n.t('计划员')
  },
  {
    fieldCode: 'storemanName',
    fieldName: i18n.t('仓管员'),
    allowEditing: false
  },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编码'),
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称'),
    allowFiltering: false,
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'planGroupName',
    fieldName: i18n.t('计划组'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerOrgName',
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    }
  },
  {
    fieldCode: 'deliveryMethod', // 0直送1非直送
    fieldName: i18n.t('是否直送'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'associatedNumber',
    fieldName: i18n.t('关联工单号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'bom',
    fieldName: i18n.t('BOM号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'saleOrderRowCode',
    fieldName: i18n.t('关联销售订单行号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'saleOrder',
    fieldName: i18n.t('关联销售订单'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'processName',
    fieldName: i18n.t('工序名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productCode',
    fieldName: i18n.t('产品代码'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'picNo',
    fieldName: i18n.t('图号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'systemRemark',
    fieldName: i18n.t('系统备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'dispatcherRemark',
    fieldName: i18n.t('叫料员备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'batchCode',
    fieldName: i18n.t('版次号'),
    allowFiltering: false
  },
  {
    fieldCode: 'createUserName',
    fieldName: i18n.t('创建人'),
    allowFiltering: false
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    fieldCode: 'renewTime',
    fieldName: i18n.t('JIT更新时间'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    fieldCode: 'supplierRemark',
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'isJit', // 0否1是
    fieldName: i18n.t('是否JIT'),
    allowFiltering: false
  },
  {
    fieldCode: 'isItemBatch', // 是否批次来料 0否1是
    fieldName: i18n.t('是否批次来料'),
    allowFiltering: false
  },
  // {
  //   fieldCode: 'uniqueCode',
  //   fieldName: i18n.t('大数据平台唯一编号'),
  //   width: 200
  // },
  {
    fieldCode: 'versionNo',
    fieldName: i18n.t('版本号')
  },
  // {
  //   fieldCode: 'remarkExplain',
  //   fieldName: i18n.t('大数据平台备注')
  // },
  // {
  //   fieldCode: 'origSupplierCode',
  //   fieldName: i18n.t('上版供应商编号')
  // },
  // {
  //   fieldCode: 'origSupplierName',
  //   fieldName: i18n.t('上版供应商名称')
  // },
  {
    fieldCode: 'planDeliveryDate',
    fieldName: i18n.t('上版交货日期'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'planDeliveryTime',
    fieldName: i18n.t('上版交货时间'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'callMaterialQty',
    fieldName: i18n.t('上版叫料数量')
  },
  {
    fieldCode: 'supplierCheckUser',
    fieldName: i18n.t('供应商确认人'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productLine',
    fieldName: i18n.t('生产线'),
    allowFiltering: false,
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    }
  },
  {
    fieldCode: 'batch',
    fieldName: i18n.t('批量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'senderPhone',
    fieldName: i18n.t('送货联系电话+送货地址'),
    allowFiltering: false,
    ignore: true
  }
]

// tab3 采购订单 表格列数据
export const ColumnDataTab1 = [
  {
    fieldCode: 'companyName', // 公司
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'companyCode', // 公司编码
    fieldName: i18n.t('公司编码')
  },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'siteCode', // 工厂编码
    fieldName: i18n.t('工厂编码')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码
    fieldName: i18n.t('物料号')
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单 订单号
    fieldName: i18n.t('关联采购订单')
  },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期 要求交期
    fieldName: i18n.t('需求日期'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.requiredDeliveryDate}}-{{data.requiredDeliveryTime}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'timeInfoTimestamp'
    }
  },
  {
    fieldCode: 'quantity', // 订单数量
    fieldName: i18n.t('订单数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'preDeliveryQty', // 待发货数量
    fieldName: i18n.t('待发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'theNumberOfShipments', // 发货数量 前端定义
    fieldName: i18n.t('发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty', // 已发货数量
    fieldName: i18n.t('已发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'receiveQty', // 已入库数量
    fieldName: i18n.t('已入库数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'warehouseCode', // 交货库存地点 库存地点 warehouseCode
    fieldName: i18n.t('库存地点编号'),
    allowFiltering: false
    // ignore: true,
  },
  {
    fieldCode: 'warehouseName', // 交货库存地点 库存地点 warehouseCode
    fieldName: i18n.t('库存地点名称'),
    allowFiltering: false
    // ignore: true,
  },
  {
    fieldCode: 'supplierName', // 供应商
    fieldName: i18n.t('供应商'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supplierCode', // 供应商编码
    fieldName: i18n.t('供应商编码')
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    }
  },
  {
    fieldCode: 'saleOrderNo', // 关联销售订单号
    fieldName: i18n.t('关联销售订单号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'projectTextBatch', // 项目文本批次
    fieldName: i18n.t('项目文本批次'),
    allowFiltering: false,
    ignore: true
  },
  // {
  //   fieldCode: "itemNo", // 关联采购订单行号 行号
  //   fieldName: i18n.t("关联采购订单行号"),
  //   allowFiltering: false,
  //   ignore: true,
  // },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supRemark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'consignee', // 联系人 收货人
    fieldName: i18n.t('联系人'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'contact', // 联系电话
    fieldName: i18n.t('联系电话'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'receiveAddress', // 送货地址
    fieldName: i18n.t('送货地址')
  }
]
