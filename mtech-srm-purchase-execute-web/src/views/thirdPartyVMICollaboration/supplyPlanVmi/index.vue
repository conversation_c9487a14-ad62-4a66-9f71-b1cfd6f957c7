<template>
  <!-- 供货计划-第三方物流 vmi -->
  <div class="full-height" ref="tableContainer">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      class="templateStyle"
      :hidden-tabs="false"
      :current-tab="currentTab"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleSelectTab="handleSelectTab"
      @cellEdit="cellEdit"
    >
      <div slot="slot-filter" class="zero-filter-switch">
        <mt-switch
          v-model="zeroFilterSwitch"
          :active-value="1"
          :inactive-value="0"
          :on-label="$t('隐藏不可创建送货单数据')"
          :off-label="$t('显示不可创建送货单数据')"
          v-permission="['O_02_1123']"
          @change="handleChangeZeroFilterSwitch"
        ></mt-switch>
      </div>
    </mt-template-page>
    <!-- 选择发货仓库弹框 -->
    <vmi-warehouse-dialog
      ref="vmiWarehouseDialog"
      @confirm="vmiWarehouseDialogConfirm"
    ></vmi-warehouse-dialog>
  </div>
</template>

<script>
import {
  ColumnDataTab2,
  ColumnDataTab3,
  Toolbar,
  TabIndex,
  DialogActionType
} from './config/constant'
import { formatTableColumnData } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import vmiWarehouseDialog from './components/vmiWarehouseDialog'

export default {
  components: {
    vmiWarehouseDialog
  },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0
    localStorage.removeItem('tabIndex')

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前列模板显示的 Tab
      editData: [], // 行编辑过的数据 { id, index, key, value }
      zeroFilterSwitch: 1, // 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterPlan: 1, // 交货计划 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterOrder: 1, // 采购订单 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterJit: 1, // JIT 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'Plan', permissionCode: 'T_02_0107' },
          { dataPermission: 'Order', permissionCode: 'T_02_0106' },
          { dataPermission: 'Jit', permissionCode: 'T_02_0113' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('交货计划'),
          dataPermission: 'Plan',
          permissionCode: 'T_02_0107',
          toolbar: Toolbar[TabIndex.deliverySchedule],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.thirdPartyVMICollaboration.supplyPlanVmi.deliverySchedule,
          grid: {
            allowPaging: true, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab2
            }),
            dataSource: [],
            asyncConfig: {
              // 供方收发货供货计划-vmi交货计划供货计划列表
              url: `${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/plan/query`,
              defaultRules: [],
              query: {
                zeroFilter: 1 // 默认 1: 过滤掉0的数据，其他：不过滤
              },
              // 序列化
              serializeList: this.serializeList
            }
            // frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('叫料计划') },
          dataPermission: 'Jit',
          permissionCode: 'T_02_0113',
          toolbar: Toolbar[TabIndex.jit],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: '0b51139f-546d-41a2-b9a2-0e01f9ed27a1',
          grid: {
            allowPaging: true, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab3
            }),
            dataSource: [],
            asyncConfig: {
              // 供方收发货供货计划-vmi-JIT供货计划列表
              url: `${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/jit/query`,
              query: {
                zeroFilter: 1 // 默认 1: 过滤掉0的数据，其他：不过滤
              },
              // 序列化
              serializeList: this.serializeList
            }
            // frozenColumns: 1
          }
        }
      ],
      batchDeliveryVmiParams: {} // 批量预创建发货单参数
    }
  },
  mounted() {},
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectRows = gridRef.getMtechGridRecords()
      const commonToolbar = [
        'TableExport',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))

      if (toolbar.id === 'TableExport') {
        // 导出
        this.handleExport()
      } else if (toolbar.id === 'BatchPreCreateDelivery') {
        // 批量创建送货单
        this.handleBatchPreCreateDelivery(selectRows)
      } else if (toolbar.id === 'CreateNoOrder') {
        // 创建无采购订单送货单
        this.handleCreateNoOrder(selectRows)
      }
    },
    // CellTool
    handleClickCellTool() {},
    // 导出
    handleExport() {
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      if (currentTabIndex === TabIndex.deliverySchedule) {
        // 交货计划-导出
        this.handlePlanExport()
      } else if (currentTabIndex === TabIndex.purchaseOrder) {
        // 采购订单-导出
        this.handleOrderExport()
      } else if (currentTabIndex === TabIndex.jit) {
        // JIT-导出
        this.handleJitExport()
      }
    },
    // 交货计划-导出
    handlePlanExport() {
      let obj = JSON.parse(
        sessionStorage.getItem(
          this.$tableUUID.thirdPartyVMICollaboration.supplyPlanVmi.deliverySchedule
        )
      )?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        formatTableColumnData({
          data: ColumnDataTab2
        }).forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.thirdPartyVMICollaboration.postVmiPlanExport(params, field).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 采购订单-导出
    handleOrderExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.thirdPartyVMICollaboration.postVmiOrderExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // JIT-导出
    handleJitExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.thirdPartyVMICollaboration
        .postVmiJitExport(params, {
          query: this.zeroFilterJit
        })
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 批量创建送货单
    handleBatchPreCreateDelivery(selectRows) {
      const selectRowsData = this.handleMatchEditDataById(selectRows, this.editData)
      // 校验 发货数量
      if (!this.checkTheNumberOfShipments(selectRowsData)) {
        // 校验失败
        return
      }
      let isNull = false
      const params = []
      selectRowsData.forEach((item) => {
        if (item.theNumberOfShipments > 0) {
          params.push({
            id: item.id,
            num: item.theNumberOfShipments, // 发货数量 前端定义
            supplierTenantId: item.supplierTenantId // 供应商租户id
          })
        } else {
          isNull = true
        }
      })

      if (isNull) {
        this.$toast({
          type: 'warning',
          content: this.$t('发货数量不允许小于等于0')
        })
        return
      }

      if (params.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }
      this.batchDeliveryVmiParams = params // 将参数保存起来
      // 显示 "选择发货仓库" 弹窗
      console.log(selectRows)

      this.vmiWarehouseDialogAdd(selectRows)
    },
    // 选择发货仓库 弹框 确定 数据校验通过时
    vmiWarehouseDialogConfirm(formData) {
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      const params = {
        vmiWarehouseCode: formData.vmiWarehouseCode,

        deliveryList: this.batchDeliveryVmiParams
      }
      if (currentTabIndex === TabIndex.deliverySchedule) {
        // 交货计划-预创建送货单
        this.postVmiPlanPreCreate({ params, formData })
      } else if (currentTabIndex === TabIndex.purchaseOrder) {
        // 采购订单-预创建送货单
        this.postVmiOrderPreCreate({ params, formData })
      } else if (currentTabIndex === TabIndex.jit) {
        // JIT-预创建送货单
        this.postVmiJitPreCreate({ params, formData })
      }
    },
    // 显示 选择发货仓库 弹框-新增状态
    async vmiWarehouseDialogAdd(selectRows) {
      // 判断如果只有一个VMI仓，那么就自动选择
      let VMIhouseData = await this.judgeVMIhouse({ text: null }, selectRows[0])
      console.log('VMIhouseData', VMIhouseData)

      if (VMIhouseData.length == 1) {
        let params = {
          vmiWarehouseCode: VMIhouseData[0].vmiWarehouseCode,
          vmiWarehouseName: VMIhouseData[0].vmiWarehouseName,
          vmiWarehouseId: VMIhouseData[0].vmiWarehouseId
        }

        // console.log(params);
        this.vmiWarehouseDialogConfirm(params)
      } else {
        for (let i of selectRows) {
          if (
            selectRows[0].siteCode !== i.siteCode ||
            selectRows[0].supplierCode !== i.supplierCode
          ) {
            this.$toast({
              content: this.$t('请查看工厂和供应商是否一致'),
              type: 'warning'
            })
            return
          }
        }
        this.$refs.vmiWarehouseDialog.dialogInit({
          title: this.$t('选择发货仓库'),
          actionType: DialogActionType.Add,
          data: selectRows[0]
        })
      }
    },
    // 判断 VMI仓库是否只有一个
    async judgeVMIhouse(args, data) {
      let VMIhouseData = []
      let params = {
        siteCode: data.siteCode,
        supplierCode: data.supplierCode
      }
      await this.$API.thirdPartyVMICollaboration
        .postNewVmiWarehouseLogisticQuery(params)
        .then((res) => {
          if (res) {
            VMIhouseData = res?.data || []
          }
        })
        .catch(() => {})
      return VMIhouseData
    },
    // 创建无采购订单送货单
    handleCreateNoOrder(selectRows) {
      const selectRowsData = this.handleMatchEditDataById(selectRows, this.editData)
      const currentTabIndex = this.$refs.templateRef.currentTabIndex

      const params = []
      selectRowsData.forEach((item) => {
        if (item.theNumberOfShipments > 0) {
          params.push({
            id: item.id,
            num: item.theNumberOfShipments, // 发货数量 前端定义
            supplierTenantId: item.supplierTenantId // 供应商租户id
          })
        }
      })

      if (params.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }

      if (currentTabIndex === TabIndex.jit) {
        // 供方收发货供货计划-JIT创建无订单送货单
        this.postSupplierDeliverySupplyPlanJitCreateNoOrder(params)
      } else if (currentTabIndex === TabIndex.deliverySchedule) {
        // 供方收发货供货计划-交货计划创建无订单送货单
        this.postVmiPlanCreateNoOrder(params)
      }
    },
    // 校验 发货数量
    checkTheNumberOfShipments(rowsData) {
      let isValid = true
      let isCheckPreDeliveryQty = false // 校验 采购订单tab 待发货数量
      let isCheckRemainingQty = false // 校验 交货计划tab、JIT tab 剩余可创建数量
      rowsData.forEach((itemRowData) => {
        if (itemRowData.preDeliveryQty !== undefined && !isNaN(itemRowData.preDeliveryQty)) {
          isCheckPreDeliveryQty = true
          // 采购订单tab 校验 待发货数量
          if (itemRowData.theNumberOfShipments > itemRowData.preDeliveryQty) {
            isValid = false
          }
        } else if (itemRowData.remainingQty !== undefined && !isNaN(itemRowData.remainingQty)) {
          isCheckRemainingQty = true
          // 交货计划tab、JIT tab 剩余可创建数量
          if (itemRowData.theNumberOfShipments > itemRowData.remainingQty) {
            isValid = false
          }
        }
      })
      if (isCheckPreDeliveryQty && !isValid) {
        this.$toast({
          content: this.$t('发货数量不可大于待发货数量'),
          type: 'warning'
        })
      } else if (isCheckRemainingQty && !isValid) {
        this.$toast({
          content: this.$t('发货数量不可大于剩余可创建数量'),
          type: 'warning'
        })
      }
      return isValid
    },
    // selectRows 与 editData 匹配 id，返回正确的数据，因为表格中 template 的数据与 dataSource 的数据不一致
    handleMatchEditDataById(selectRows, editData) {
      const result = []
      selectRows.forEach((item) => {
        editData.forEach((editItem) => {
          if (item.id === editItem.id) {
            item[editItem.key] = editItem.value
          }
        })
        result.push(item)
      })
      return result
    },
    // 序列化表格数据
    serializeList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 发货数量(前端定义) 默认等于 待发货数量 || 剩余可创建数量
          item.theNumberOfShipments = item.remainingQty
          // 将编辑过的数据设置到表格中
          // const editData = this.editData.find(
          //   (itemEdit) => itemEdit.id === item.id
          // );
          // if (editData !== undefined) {
          //   item[editData.key] = editData.value;
          // }
        })
      }
      return list
    },
    // 行编辑
    cellEdit(e) {
      const { id, index, key, value } = e
      if (id !== undefined) {
        // 如果是编辑行
        const editIndex = this.editData.findIndex((item) => item.id === id && item.key === key)
        if (editIndex >= 0) {
          // 更新编辑的数据
          this.editData[editIndex].value = value
        } else {
          // 保存编辑的数据，value 为空也存
          this.editData.push({
            id,
            key,
            index,
            value
          })
        }
      }
    },
    // 跳转到批量创建送货单 vmi
    goToBatchDeliveryVmi(args) {
      const { data, params, formData } = args
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      // 预创建订单数据 保存到 localStorage
      localStorage.setItem('batchDeliveryVmiData', JSON.stringify(data))
      // 预创建订单请求参数 保存到 localStorage
      localStorage.setItem('batchDeliveryVmiParams', JSON.stringify(params))
      // 预创建选择发货仓弹框的数据 保存到 localStorage
      localStorage.setItem('batchDeliveryVmiFormData', JSON.stringify(formData))
      // 将 lastTabIndex 放到 localStorage
      localStorage.setItem('lastTabIndex', JSON.stringify(currentTabIndex))
      this.editData = []

      // 页面跳转
      this.$router.push({
        name: 'batch-delivery-vmi',
        query: {
          type: currentTabIndex, // 页面类型
          date: new Date().getTime()
        }
      })
    },
    // 供方收发货供货计划-vmi订单预创建送货单
    postVmiOrderPreCreate(args) {
      const { params, formData } = args
      this.apiStartLoading()
      this.$API.thirdPartyVMICollaboration
        .postVmiOrderPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 跳转-批量创建送货单 vmi
            this.goToBatchDeliveryVmi({
              data: res.data || [],
              params,
              formData
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划预创建送货单
    postVmiPlanPreCreate(args) {
      const { params, formData } = args
      this.apiStartLoading()
      this.$API.thirdPartyVMICollaboration
        .postVmiPlanPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 跳转-批量创建送货单 vmi
            this.goToBatchDeliveryVmi({
              data: res.data || [],
              params,
              formData
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT预创建送货单
    postVmiJitPreCreate(args) {
      const { params, formData } = args
      this.apiStartLoading()
      let param = {
        query: {
          vmiWarehouseCode: params.vmiWarehouseCode
        },
        body: params.deliveryList
      }
      this.$API.thirdPartyVMICollaboration
        .postVmiJitPreCreate(param)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // this.$toast({
            //   content: this.$t("操作成功"),
            //   type: "success",
            // });
            // 跳转-批量创建送货单 vmi
            this.goToBatchDeliveryVmi({
              data: res.data || [],
              params,
              formData
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT创建无订单送货单
    postSupplierDeliverySupplyPlanJitCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 刷新-当前表格数据
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-vmi交货计划创建无订单送货单
    postVmiPlanCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.thirdPartyVMICollaboration
        .postVmiPlanCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 刷新-当前表格数据
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 是否显示不可创建送货单数据
    handleChangeZeroFilterSwitch(value) {
      console.log('value', value)
      // 保存当前 tab 选择的 zeroFilterSwitch 状态
      // 并修改列模板请求参数，调用api获取数据
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      if (currentTabIndex == TabIndex.deliverySchedule) {
        this.zeroFilterPlan = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      } else if (currentTabIndex == TabIndex.purchaseOrder) {
        this.zeroFilterOrder = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      } else if (currentTabIndex == TabIndex.jit) {
        this.zeroFilterJit = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      }
    },
    // 切换tab
    handleSelectTab(index) {
      // 将各自 tab 保存的 zeroFilterSwitch 赋值给 zeroFilterSwitch
      // 并修改列模板请求参数，调用api获取数据
      if (index == TabIndex.deliverySchedule) {
        this.zeroFilterSwitch = this.zeroFilterPlan
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterPlan
        )
      } else if (index == TabIndex.purchaseOrder) {
        this.zeroFilterSwitch = this.zeroFilterOrder
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterOrder
        )
      } else if (index == TabIndex.jit) {
        this.zeroFilterSwitch = this.zeroFilterJit
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterJit
        )
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// 表格容器
#table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
// 显示不可创建送货单数据
.zero-filter-switch {
  text-align: right;
}

/deep/ .templateStyle {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
      }
      & tbody td:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
    }
  }
}
</style>
