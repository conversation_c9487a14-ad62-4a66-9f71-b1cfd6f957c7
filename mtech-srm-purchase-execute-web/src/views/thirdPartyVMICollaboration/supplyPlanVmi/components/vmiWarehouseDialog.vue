<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    :height="290"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓库')" class="">
        <debounce-filter-select
          v-model="formData.vmiWarehouseCode"
          :request="getVmiWarehouseOptions"
          :data-source="vmiWarehouseOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'vmiWarehouseCode' }"
          :value-template="vmiWarehouseCodeValueTemplate"
          @change="vmiWarehouseCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { DialogActionType } from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      vmiWarehouseOptions: [], // VMI仓库 下列选项
      vmiWarehouseCodeValueTemplate: codeNameColumn({
        firstKey: 'vmiWarehouseCode',
        secondKey: 'vmiWarehouseName'
      }), // VMI仓库
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // VMI仓库
        vmiWarehouseCode: [
          {
            required: true,
            message: this.$t('请选择VMI仓库'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      rowData: '',
      formData: {
        // VMI仓库
        vmiWarehouseCode: '',
        vmiWarehouseName: '',
        vmiWarehouseId: ''
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData, data } = entryInfo
      console.log(selectData)
      this.rowData = data
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          // VMI仓库
          vmiWarehouseCode: '',
          vmiWarehouseName: '',
          vmiWarehouseId: ''
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          // VMI仓库
          vmiWarehouseCode: selectData.vmiWarehouseCode,
          vmiWarehouseName: selectData.vmiWarehouseName,
          vmiWarehouseId: selectData.vmiWarehouseId
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.$emit('confirm', this.formData)
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // VMI仓库 change
    vmiWarehouseCodeChange(e) {
      const { itemData } = e
      this.formData.vmiWarehouseId = itemData?.id || ''
      this.formData.vmiWarehouseCode = itemData?.vmiWarehouseCode || ''
      this.formData.vmiWarehouseName = itemData?.vmiWarehouseName || ''
    },
    // VMI仓库
    getVmiWarehouseOptions(args) {
      console.log(args)
      const { updateData, setSelectData } = args
      // const rules = {
      //   condition: "or",
      //   defaultRules: [
      //     {
      //       label: "仓库名称",
      //       field: "vmiWarehouseName",
      //       type: "string",
      //       operator: "contains", // 包含
      //       value: text,
      //     },
      //     {
      //       label: "仓库编码",
      //       field: "vmiWarehouseCode",
      //       type: "string",
      //       operator: "contains", // 包含
      //       value: text,
      //     },
      //   ],
      // };
      let params = {
        siteCode: this.rowData.siteCode,
        supplierCode: this.rowData.supplierCode
        // page: {
        //   current: 1,
        //   size: 100,
        // },
      }
      // if (text) {
      //   params = {
      //     page: {
      //       current: 1,
      //       size: 100,
      //     },
      //     ...rules,
      //   };
      // }
      this.$API.thirdPartyVMICollaboration
        .postNewVmiWarehouseLogisticQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.vmiWarehouseOptions = addCodeNameKeyInList({
              firstKey: 'vmiWarehouseCode',
              secondKey: 'vmiWarehouseName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.vmiWarehouseOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // vmiWarehouseCode VMI仓库
        this.getVmiWarehouseOptions({
          text: selectData.vmiWarehouseCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.vmiWarehouseCode = selectData.vmiWarehouseCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取VMI仓库
        this.getVmiWarehouseOptions({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
