export const headerInfo = {
  status: 1,
  vmiOrderCode: '',
  createUserName: '',
  createTime: '',
  submitTime: '',
  siteCode: '',
  siteName: '',
  supplierCode: '',
  supplierName: '',
  vmiWarehouseAddress: '',
  vmiWarehouseCode: '',
  vmiWarehouseName: '',
  vmiWarehouseType: null,
  remark: '',
  submitStatus: true,
  statusCreate: true, //判断是不是编辑页面 true不可编辑   false可编辑
  identificationStatus: null,
  isDisabled: false, //false可编辑  true不可编辑
  hasDetailRow: false // 是否有下面的明细，如果有，头部的数据不可编辑。仅限新增状态的，所以对于input不影响
}

export function headerInfoInit() {
  headerInfo.status = 1
  headerInfo.vmiOrderCode = ''
  headerInfo.createUserName = ''
  headerInfo.createTime = ''
  headerInfo.submitTime = ''
  headerInfo.siteCode = ''
  headerInfo.siteName = ''
  headerInfo.supplierCode = ''
  headerInfo.supplierName = ''
  headerInfo.vmiWarehouseAddress = ''
  headerInfo.vmiWarehouseCode = ''
  headerInfo.vmiWarehouseName = ''
  headerInfo.remark = ''
  headerInfo.submitStatus = true
  headerInfo.statusCreate = true //判断是不是编辑页面 true不可编辑   false可编辑
  headerInfo.identificationStatus = null
  headerInfo.isDisabled = false //false可编辑  true不可编辑
  headerInfo.hasDetailRow = false
}

export function setHeaderInfo(data) {
  headerInfo.status = data?.status
  headerInfo.vmiOrderCode = data.vmiOrderCode
  headerInfo.createUserName = data.createUserName
  headerInfo.createTime = data.createTime
  headerInfo.submitTime = data.submitTime
  headerInfo.siteCode = data.siteCode
  headerInfo.siteName = data.siteName
  headerInfo.supplierCode = data.supplierCode
  headerInfo.supplierName = data.supplierName
  headerInfo.vmiWarehouseAddress = data.vmiWarehouseAddress
  headerInfo.vmiWarehouseCode = data.vmiWarehouseCode
  headerInfo.vmiWarehouseName = data.vmiWarehouseName
  headerInfo.remark = data.remark
  headerInfo.submitStatus = data?.submitStatus
  headerInfo.statusCreate = data?.statusCreate
  headerInfo.identificationStatus = data?.identificationStatus
  headerInfo.hasDetailRow = data?.vmiOrderItemResponses?.length
}
