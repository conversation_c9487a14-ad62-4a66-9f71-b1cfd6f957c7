// import Vue from "vue";
import { i18n } from '@/main.js'
// import { MasterDataSelect } from "@/utils/constant";
// import { judgeFormatCodeName } from "@/utils/utils";

// 库存调整管理
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
    // searchOptions: MasterDataSelect.factorySupplierAddress,
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.siteCode, data?.siteName);
    // },
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
    // searchOptions: MasterDataSelect.supplierThird,
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName);
    // },
  },
  {
    width: '260',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码'),
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
    // searchOptions: MasterDataSelect.vmiThird,
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(
    //     data?.vmiWarehouseCode,
    //     data?.vmiWarehouseName
    //   );
    // },
  },
  {
    width: '150',
    field: 'vmiWarehouseType',
    headerText: i18n.t('VMI仓类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('SRM管理库存'),
        1: i18n.t('原厂'),
        2: i18n.t('WMS管理库存')
      }
    }
  },
  {
    width: '130',
    field: 'itemCode', //supplierCode
    headerText: i18n.t('物料编码'),
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
  },
  {
    width: '95',
    field: 'count',
    type: 'number',
    headerText: i18n.t('合格库存数量')
  },
  {
    width: '120',
    field: 'qcCount',
    type: 'number',
    headerText: i18n.t('待检库存数量')
  },
  {
    width: '140',
    field: 'qcLockCount',
    type: 'number',
    headerText: i18n.t('锁定-待检库存')
  },
  {
    width: '155',
    field: 'lockCount',
    type: 'number',
    headerText: i18n.t('合格-出库锁定库存')
  },
  {
    width: '150',
    field: 'deliveringQuantity',
    type: 'number',
    headerText: i18n.t('已打送货单数量')
  },
  {
    width: '150',
    field: 'unreceivedGoods',
    type: 'number',
    headerText: i18n.t('VMI送内仓途中')
  },
  {
    width: '150',
    field: 'returnVmi',
    type: 'number',
    headerText: i18n.t('内仓短收待返回VMI')
  },
  {
    width: '150',
    field: 'ineffectivenessLockCount',
    type: 'number',
    headerText: i18n.t('不合格库存数量')
  },
  {
    width: '150',
    field: 'ineffectivenessLockLockCount',
    type: 'number',
    headerText: i18n.t('锁定-不合格库存')
  },
  {
    width: '130',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '125',
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组')
    // searchOptions: {
    //   ...MasterDataSelect.businessCodeName,
    //   renameField: "purchaseGroupCode",
    // },
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(
    //     data?.purchaseGroupCode,
    //     data?.purchaseGroupName
    //   );
    // },
  },
  {
    field: 'purchaseGroupName', //supplierCode
    headerText: i18n.t('采购组名称'),
    ignore: true
  },
  {
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  }
]
