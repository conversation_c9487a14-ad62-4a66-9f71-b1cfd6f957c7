// 调入物料编码
<template>
  <div class="pc-select">
    <div class="in-cell">
      <!-- <mt-input :id="fieldName" disabled v-model="value" :width="130" /> -->
      <mt-select
        :id="fieldName"
        v-model="value"
        :allow-filtering="true"
        float-label-type="Never"
        :data-source="dataSourceList"
        :fields="{ text: 'codeAndName', value: 'itemCode' }"
        @change="handleSelectChange"
        :filtering="filteringList"
      ></mt-select>
      <mt-icon
        style="width: 20px; right: 18px"
        name="icon_list_refuse"
        @click.native="handleClear"
      ></mt-icon>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      ></mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
// 要改变的字段：采购组 exchangePurchaseGroupCode-purchaseGroupCode  exchangePurchaseGroupName-purchaseGroupName
import { PROXY_MDM_AUTH } from '@/utils/constant'

import { headerInfo } from '../config/variable.js'
import { throttle } from 'lodash'
import axios from 'axios'
export default {
  props: {},
  data() {
    return {
      fieldName: null,
      dataSourceList: [], // 物料下拉列表
      dialogShow: false,
      title: this.$t('请选择'),
      value: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '8dc3a8d8-e1d0-4892-9773-8e408987b09c',
          grid: {
            // height: 352,
            allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'exchangeItemCode',
                headerText: this.$t('物料编号'),
                searchOptions: {
                  renameField: 'itemCode'
                }
              },
              {
                width: '150',
                field: 'exchangeItemName',
                headerText: this.$t('物料名称'),
                searchOptions: {
                  renameField: 'itemName'
                }
              },
              {
                width: '150',
                field: 'baseMeasureUnitName',
                headerText: this.$t('单位'),
                ignore: true
              },
              {
                width: '150',
                field: 'purchaseGroupName',
                headerText: this.$t('采购组'),
                ignore: true
              }
            ],
            asyncConfig: {
              // url: "/srm-purchase-execute/tenant/vmi/common/logistic/queryItems",
              // recordsPosition: "data.records",
            }
          }
        }
      ],
      changeObj: [
        'exchangeItemCode',
        'exchangeItemName',
        'exchangePurchaseGroupCode',
        'exchangePurchaseGroupName',
        'exchangeItemUnitDescription', // 只有调出有单位
        'exchangeItemUnit'
      ]
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.value = this.data[this.fieldName]
    this.getBuildSelect()
    this.confirm(null, [this.data])
  },
  methods: {
    handleSelectChange(e) {
      // 下拉框选中事件
      let _data = e.itemData
      this.confirm(null, [_data])
      // const fieldValue = this.pageConfig[0]["grid"]["columnData"][0]["field"]
      // if (_data[fieldValue] !== this.text) {
      //   this.$emit("change", _data)
      // }
    },
    getParams(text) {
      let params = {
        // customerEnterpriseId: this.customerEnterpriseId,
        organizationCode: headerInfo.siteCode,
        page: {
          current: 1,
          size: 9999
        },
        pageFlag: true,
        tenantId: '10000'
      }
      const rules = []
      let columnData = [
        {
          width: '150',
          field: 'itemCode',
          headerText: this.$t('物料编号')
        },
        {
          width: '150',
          field: 'itemName',
          headerText: this.$t('物料名称')
        },
        {
          width: '150',
          field: 'baseMeasureUnitName',
          headerText: this.$t('单位'),
          ignore: true
        },
        {
          width: '150',
          field: 'purchaseGroupName',
          headerText: this.$t('采购组'),
          ignore: true
        }
      ]
      for (let i = 0; i < columnData.length; i++) {
        if (i < 1) {
          const field = columnData[i]?.field
          let obj = {
            field,
            label: '',
            operator: field.includes('Code') ? 'equal' : 'contains',
            type: 'string',
            value: text
          }
          rules.push(obj)
        }
      }
      if (rules.length) {
        // params.condition = "or";
        params.condition = 'and'
        params.rules = rules
      }
      return params
    },
    filteringList: throttle(function (e) {
      let { text } = e
      if (text) {
        const params = this.getParams(text)
        axios
          .post(
            `/api${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
              'currentBu'
            )}`,
            params
          )
          .then((res) => {
            const { code, data, msg } = res.data
            if (code === 200) {
              let records = data?.records || []
              this.dataSourceList = records.map((i) => {
                return {
                  ...i,
                  codeAndName: `${i.itemCode}-${i.itemName}`,
                  exchangeItemCode: i.itemCode,
                  exchangeItemName: i.itemName,
                  exchangePurchaseGroupCode:
                    i.purchasingBasicInOrgResponse?.purchasingInfo?.purchaseGroupCode,
                  exchangePurchaseGroupName:
                    i.purchasingBasicInOrgResponse?.purchasingInfo?.purchaseGroupName,
                  exchangeItemUnitDescription:
                    i.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitName,
                  exchangeItemUnit: i.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitCode
                }
              })
            } else {
              this.$toast({ content: msg, type: 'warning' })
            }
          })
          .catch(() => {
            this.$toast({ content: this.$t('系统异常，请稍后再试'), type: 'warning' })
          })
      }
    }, 300),
    getBuildSelect() {
      // this.$API.masterData.postBuyerCriteriaQuery().then((res) => {
      //   this.customerEnterpriseId = res.data[0].customerEnterpriseId;
      let obj = {
        // page: {
        //   current: 1,
        //   size: 10,
        // },
        pageFlag: true,
        tenantId: '10000',
        // customerEnterpriseId: res.data[0].customerEnterpriseId,
        organizationCode: headerInfo.siteCode
        // defaultRules: [
        //   {
        //     field: "siteCode",
        //     operator: "equal",
        //     value: headerInfo.siteCode,
        //   },
        //   {
        //     field: "supplierCode",
        //     operator: "equal",
        //     value: headerInfo.supplierCode,
        //   },
        //   {
        //     field: "vmiWarehouseCode",
        //     operator: "equal",
        //     value: headerInfo.vmiWarehouseCode,
        //   },
        // ],
      }
      this.pageConfig[0].grid.asyncConfig = {
        url: `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        recordsPosition: 'data.records',
        serializeList: this.serializeList,
        params: obj
      }
      // });
    },
    serializeList(data) {
      let obj = []
      data.forEach((item) => {
        obj.push({
          item,
          ...item.purchasingBasicInOrgResponse.itemInfo,
          ...item.purchasingBasicInOrgResponse.purchasingInfo
        })
        // return {
        //   item,
        //   ...item.purchasingBasicInOrgResponse.itemInfo,
        //   ...item.purchasingBasicInOrgResponse.purchasingInfo,
        // };
      })
      obj.forEach((element) => {
        element.exchangeItemCode = element.itemCode
        element.exchangeItemName = element.itemName
        element.exchangePurchaseGroupCode = element.purchaseGroupCode
        element.exchangePurchaseGroupName = element.purchaseGroupName
        element.exchangeItemUnitDescription = element.baseMeasureUnitName
        element.exchangeItemUnit = element.baseMeasureUnitCode
      })
      console.log(obj)
      return obj
    },
    recordDoubleClick(args) {
      this.confirm(null, [args.rowData])
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      console.log(records, '-=-=')
      //   this.value = records[0]?.itemName;
      if (records[0]?.itemCode) {
        this.dataSourceList = [
          {
            ...records[0],
            codeAndName: `${records[0]?.itemCode}-${records[0]?.itemName}`,
            exchangeItemCode: records[0]?.itemCode,
            exchangeItemName: records[0]?.itemName,
            exchangePurchaseGroupCode:
              records[0]?.purchasingBasicInOrgResponse?.purchasingInfo?.purchaseGroupCode,
            exchangePurchaseGroupName:
              records[0]?.purchasingBasicInOrgResponse?.purchasingInfo?.purchaseGroupName,
            exchangeItemUnitDescription:
              records[0]?.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitName,
            exchangeItemUnit:
              records[0]?.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitCode
          }
        ]
        this.value = records[0]?.itemCode
      }
      if (records[0]?.exchangeItemCode) {
        this.dataSourceList = [
          {
            ...records[0],
            codeAndName: `${records[0]?.exchangeItemCode}-${records[0]?.exchangeItemName}`,
            itemCode: records[0]?.exchangeItemCode,
            itemName: records[0]?.exchangeItemName,
            purchasingBasicInOrgResponse: {
              purchasingInfo: {
                purchaseGroupCode: records[0]?.purchaseGroupCode,
                purchaseGroupName: records[0]?.purchaseGroupName
              },
              itemInfo: {
                exchangeItemUnitDescription: records[0]?.exchangeItemUnitDescription,
                baseMeasureUnitCode: records[0]?.baseMeasureUnitCode
              }
            }
            // exchangePurchaseGroupCode:
            //   records[0]?.purchasingBasicInOrgResponse?.purchasingInfo
            //     ?.purchaseGroupCode,
            // exchangePurchaseGroupName:
            //   records[0]?.purchasingBasicInOrgResponse?.purchasingInfo
            //     ?.purchaseGroupName,
            // exchangeItemUnitDescription:
            //   records[0]?.purchasingBasicInOrgResponse?.itemInfo
            //     ?.baseMeasureUnitName,
            // exchangeItemUnit:
            //   records[0]?.purchasingBasicInOrgResponse?.itemInfo
            //     ?.baseMeasureUnitCode,
          }
        ]
        this.value = records[0]?.exchangeItemCode
      }
      //联动改变物料描述
      this.changeObj.forEach((i) => {
        this.$bus.$emit(`${i}Change`, records[0]?.[i])
      })

      // 关闭弹窗
      this.handleClose()
    },
    handleClear() {
      this.value = null
      //联动改变物料描述
      this.changeObj.forEach((i) => {
        this.$bus.$emit(`${i}Change`, null)
      })
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    },
    showDialog() {
      this.dialogShow = true
      this.$refs.dialog.ejsRef.show()
    }
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 40px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
