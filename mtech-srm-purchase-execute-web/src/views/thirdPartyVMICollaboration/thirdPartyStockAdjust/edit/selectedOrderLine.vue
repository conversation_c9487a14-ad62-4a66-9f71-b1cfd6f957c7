<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="$t('请选择库存状态')"
      @change="selectChange"
      :disabled="disabledStatus"
    >
    </mt-select>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      isDisabled: this.$route.path.includes('replace-create') ? true : false,
      // 调拨页面，不能显示质检库存。。替换页面，只能显示合格库存
      dataSource: this.$route.path.includes('allocation-create')
        ? [
            { label: this.$t('合格库存 '), value: this.$t('合格库存') },
            // { label: this.$t("质检库存"), value: this.$t("质检库存") },
            { label: this.$t('不合格库存'), value: this.$t('不合格库存') }
          ]
        : this.$route.path.includes('replace-create')
        ? [{ label: this.$t('合格库存 '), value: this.$t('合格库存') }]
        : [],
      fields: { text: 'label', value: 'value' },
      disabledStatus: false,
      itemInfo: null
    }
  },
  mounted() {
    this.$route.query.create === 'false'
      ? (this.disabledStatus = true)
      : this.$route.path.includes('replace-create')
      ? (this.disabledStatus = true)
      : (this.disabledStatus = false)

    this.itemInfo = this.data.itemInfo
      ? JSON.parse(this.data.itemInfo)
      : this.data?.vmiStockResponse
    this.$bus.$on('itemInfoChange', (e) => {
      this.itemInfo = e ? JSON.parse(e) : null
    })
  },
  methods: {
    selectChange(e) {
      // 产品要求不联动移入库状态
      if (this.data.column.field != 'sourceStockType') return
      console.log('出库状态发生了变化', e.itemData.value)
      console.log(this.itemData)
      console.log(this.data)

      // 默认从接口取，切换物料时存下物料相关数据；切换移出库状态时，如果有存储，取存储的，否则从接口取
      // 判断库存状态  然后取的 库存状态对应的不同的值
      if (this.itemInfo) {
        let _key =
          e.itemData.value == this.$t('合格库存')
            ? 'count'
            : e.itemData.value == this.$t('待检库存')
            ? 'qcCount'
            : 'ineffectivenessLockCount'
        console.log('我改变了可退货数量-状态', this.itemInfo[_key])
        this.$bus.$emit('lockCountChange', this.itemInfo?.[_key])
      }

      this.$bus.$emit(`${this.data.column.field}Change`, e.itemData.value)
    }
  }
}
</script>

<style scoped lang="scss"></style>
