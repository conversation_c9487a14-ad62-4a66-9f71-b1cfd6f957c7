<template>
  <div class="pc-select">
    <div class="in-cell">
      <!-- <mt-input :id="fieldName" disabled v-model="value" :width="130" /> -->
      <mt-select
        :id="fieldName"
        v-model="value"
        :allow-filtering="true"
        float-label-type="Never"
        :data-source="dataSourceList"
        :fields="{ text: 'codeAndName', value: 'itemCode' }"
        @change="handleSelectChange"
        :filtering="filteringList"
      ></mt-select>
      <mt-icon
        style="width: 20px; right: 18px"
        name="icon_list_refuse"
        @click.native="handleClear"
      ></mt-icon>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { headerInfo } from '../config/variable.js'
import { throttle } from 'lodash'
import axios from 'axios'

export default {
  props: {},
  data() {
    return {
      data: {},
      dialogShow: false,
      title: this.$t('请选择'),
      value: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      fieldName: '',
      dataSourceList: [], // 物料下拉列表
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '89035654-982e-46ed-aa06-947dbedd8358',
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              },
              {
                width: '150',
                field: 'itemUnitDescription',
                headerText: this.$t('单位')
              },
              {
                width: '150',
                field: 'purchaseGroupName',
                headerText: this.$t('采购组')
              },
              {
                width: '150',
                field: 'batchCode',
                headerText: this.$t('批次号')
              }
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      sourceStockType: null,
      allowcationArr: [
        'itemCode',
        'itemName',
        'itemUnitDescription',
        'itemUnit',
        'purchaseGroupName',
        'purchaseGroupCode',
        'itemInfo'
      ], // 调拨 物料要改变的字段
      replaceInArr: [
        'itemCode',
        'itemName',
        'itemUnitDescription',
        'itemUnit',
        'purchaseGroupName',
        'purchaseGroupCode',
        'batchCode',
        'itemInfo'
      ] // 替换 调出物料要改变的字段
    }
  },
  mounted() {
    if (this.data.itemCode) {
      let obj = {
        page: { current: 1, size: 20 },
        defaultRules: [
          {
            field: 'siteCode',
            operator: 'equal',
            value: headerInfo.siteCode
          },
          {
            field: 'supplierCode',
            operator: 'equal',
            value: headerInfo.supplierCode
          },
          {
            field: 'vmiWarehouseCode',
            operator: 'equal',
            value: headerInfo.vmiWarehouseCode
          },
          {
            field: 'itemCode',
            operator: 'equal',
            value: this.data.itemCode
          }
        ]
      }
      this.$API.thirdPartyVMICollaboration.postthirdPartyLogisticQuer(obj).then((res) => {
        this.confirm(null, [res.data.records[0]])
      })
      this.filteringList({ text: this.data.itemCode })
    }
    this.fieldName = this.data.column.field
    this.value = this.data[this.fieldName]
    this.pageConfig[0].grid.asyncConfig = {
      url: `${BASE_TENANT}/vmi_stock/logistic-page-query`,
      recordsPosition: 'data.records',
      defaultRules: [
        {
          field: 'siteCode',
          operator: 'equal',
          value: headerInfo.siteCode
        },
        {
          field: 'supplierCode',
          operator: 'equal',
          value: headerInfo.supplierCode
        },
        {
          field: 'vmiWarehouseCode',
          operator: 'equal',
          value: headerInfo.vmiWarehouseCode
        }
      ]
      // params: obj,
    }

    this.sourceStockType = this.data.sourceStockType
    // 监听 移除库存状态的值
    this.$bus.$on('sourceStockTypeChange', (e) => {
      this.sourceStockType = e
    })
  },
  methods: {
    handleSelectChange(e) {
      // 下拉框选中事件
      let _data = e.itemData
      this.confirm(null, [_data])
      // const fieldValue = this.pageConfig[0]["grid"]["columnData"][0]["field"]
      // if (_data[fieldValue] !== this.text) {
      //   this.$emit("change", _data)
      // }
    },
    getParams(text) {
      let params = {
        defaultRules: [
          {
            field: 'siteCode',
            operator: 'equal',
            value: headerInfo.siteCode
          },
          {
            field: 'supplierCode',
            operator: 'equal',
            value: headerInfo.supplierCode
          },
          {
            field: 'vmiWarehouseCode',
            operator: 'equal',
            value: headerInfo.vmiWarehouseCode
          }
        ],
        page: {
          current: 1,
          size: 9999
        }
      }
      const rules = []
      let columnData = this.pageConfig[0]['grid']['columnData']
      for (let i = 0; i < columnData.length; i++) {
        if (i < 1) {
          const field = columnData[i]?.field
          let obj = {
            field,
            label: '',
            operator: field.includes('Code') ? 'equal' : 'contains',
            type: 'string',
            value: text
          }
          rules.push(obj)
        }
      }
      if (rules.length) {
        // params.condition = "or";
        params.condition = 'and'
        params.rules = rules
      }
      if (
        this.pageConfig[0]['grid']['asyncConfig'] &&
        this.pageConfig[0]['grid']['asyncConfig']['params']
      ) {
        params = {
          ...params,
          ...this.pageConfig[0]['grid']['asyncConfig']['params']
        }
      }
      return params
    },
    filteringList: throttle(function (e) {
      let { text } = e
      if (text) {
        const params = this.getParams(text)
        axios
          .post(`/api${BASE_TENANT}/vmi_stock/logistic-page-query`, params)
          .then((res) => {
            const { code, data, msg } = res.data
            if (code === 200) {
              let records = data?.records || []
              this.dataSourceList = records.map((i) => {
                return {
                  ...i,
                  codeAndName: `${i.itemCode}-${i.itemName}`
                }
              })
            } else {
              this.$toast({ content: msg, type: 'warning' })
            }
          })
          .catch(() => {
            this.$toast({ content: this.$t('系统异常，请稍后再试'), type: 'warning' })
          })
      }
    }, 300),
    recordDoubleClick(args) {
      console.log(args)
      this.confirm(null, [args.rowData])
    },
    handleClear() {
      this.value = null
      this.$set(this.data, this.fieldName, null)

      let arrName = this.$route.query.name == 'allocation' ? 'allowcationArr' : 'replaceInArr'
      //联动改变物料描述
      this[arrName].forEach((i) => {
        this.$bus.$emit(`${i}Change`, null)
      })
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      console.log(records, '-=-=')
      //   this.value = records[0]?.itemName;
      this.value = records[0]?.itemCode
      this.dataSourceList = [
        {
          ...records[0],
          codeAndName: `${records[0]?.itemCode}-${records[0]?.itemName}`
        }
      ]
      // 缓存物料
      localStorage.setItem('itemCode', records[0]?.itemCode)

      // 选择完物料后，如果有调出状态，才能 修改可调出数量
      if (this.sourceStockType) {
        let _key =
          this.sourceStockType == this.$t('合格库存')
            ? 'count'
            : this.sourceStockType == this.$t('待检库存')
            ? 'qcCount'
            : 'ineffectivenessLockCount'
        console.log('我改变了可退货数量-物料', records[0]?.[_key])
        this.$bus.$emit('lockCountChange', records[0]?.[_key])
      }

      let arrName = this.$route.query.name == 'allocation' ? 'allowcationArr' : 'replaceInArr'
      //联动改变物料描述
      this[arrName].forEach((i) => {
        if (i == 'itemInfo') {
          this.$bus.$emit(`${i}Change`, JSON.stringify(records[0]))
        } else {
          this.$bus.$emit(`${i}Change`, records[0]?.[i])
        }
      })

      // 关闭弹窗
      this.handleClose()
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    },
    showDialog() {
      this.dialogShow = true
      this.pageConfig[0].grid.asyncConfig = {
        url: `${BASE_TENANT}/vmi_stock/logistic-page-query`,
        recordsPosition: 'data.records',
        defaultRules: [
          {
            field: 'siteCode',
            operator: 'equal',
            value: headerInfo.siteCode
          },
          {
            field: 'supplierCode',
            operator: 'equal',
            value: headerInfo.supplierCode
          },
          {
            field: 'vmiWarehouseCode',
            operator: 'equal',
            value: headerInfo.vmiWarehouseCode
          }
        ]
        // params: obj,
      }
      this.$refs.dialog.ejsRef.show()
    }
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 40px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}

.full-height {
  height: 100%;
}
/deep/.template-height {
  .mt-data-grid {
    height: 100%;

    > .e-grid {
      height: calc(100% - 40px);
      .e-content {
        height: 100% !important;
      }
    }
  }
}

/deep/.template-height.has-page {
  .repeat-template .mt-data-grid > .e-control {
    height: calc(100% - 40px) !important;
  }
}
</style>
