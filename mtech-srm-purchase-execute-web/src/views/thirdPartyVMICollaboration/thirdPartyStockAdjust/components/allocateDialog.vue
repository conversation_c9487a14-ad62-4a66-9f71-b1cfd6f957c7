<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :width="400"
    :height="400"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="toSiteCode" :label="$t('调入工厂')">
          <mt-input v-model="formObject.toSiteCode" :readonly="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="count" :label="$t('最大可调拨数量')">
          <mt-input v-model="formObject.count" :readonly="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="quantity" :label="$t('本次调拨数量')">
          <mt-input-number
            v-model="formObject.quantity"
            :placeholder="$t('请输入')"
            :min="0"
            :max="formObject.count"
          ></mt-input-number>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        toSiteCode: null,
        count: null,
        quantity: ''
      },
      //必填项
      formRules: {
        quantity: [
          {
            required: true,
            message: this.$t('请输入本次调拨数量'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.formObject.toSiteCode = this.modalData.toSiteCode
    this.formObject.count = this.modalData.count
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 20px 0;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
