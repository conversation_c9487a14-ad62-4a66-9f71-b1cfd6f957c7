<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div
        :class="[statusToClass(headerInfo.status), 'mr20']"
        v-if="headerInfo.isDisabled || headerInfo.status == 0"
      >
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20" v-if="headerInfo.isDisabled || headerInfo.status == 0">
        {{ $t('VMI入库单号：') }}{{ headerInfo.vmiOrderCode }}
      </div>
      <div class="infos mr20" v-if="headerInfo.isDisabled || headerInfo.status == 0">
        {{ $t('创建人：') }}{{ headerInfo.createUserName }}
      </div>
      <div class="infos" v-if="headerInfo.isDisabled || headerInfo.status == 0">
        {{ $t('创建时间：') }}{{ headerInfo.createTime }}
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.submitStatus && !headerInfo.isDisabled"
        :is-primary="true"
        @click="preservationBtn"
        >{{ $t('保存') }}</mt-button
      >
      <!-- <mt-button
        css-class="e-flat"
        v-if="headerInfo.cancelStatus  && !headerInfo.isDisabled"
        :is-primary="true"
        @click="cancelBtn"
        >{{ $t("保存") }}</mt-button
      > -->
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.submitStatus && !headerInfo.isDisabled"
        :is-primary="true"
        @click="submitBtn"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <mt-input v-if="headerInfo.isDisabled" v-model="site" disabled></mt-input>

          <mt-select
            v-else
            v-model="headerInfo.siteCode"
            :disabled="selectedDisabled"
            :placeholder="$t('工厂')"
            @change="factoryChange"
            :data-source="factorySelect"
            :allow-filtering="true"
            filter-type="Contains"
            @focus="focusSite('site')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <mt-input v-if="headerInfo.isDisabled" v-model="supplier" disabled></mt-input>
          <mt-select
            v-else
            v-model="headerInfo.supplierCode"
            :disabled="selectedDisabled"
            :placeholder="$t('供应商')"
            :data-source="supplierSelect"
            @change="supplierChange"
            :allow-filtering="true"
            filter-type="Contains"
            @focus="focusSupplier('supplier')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓')">
          <mt-input v-if="headerInfo.isDisabled" v-model="vmiWarehouse" disabled></mt-input>
          <mt-select
            v-else
            v-model="headerInfo.vmiWarehouseCode"
            :disabled="selectedDisabled"
            :placeholder="$t('VMI仓')"
            :data-source="warehouseSelect"
            :allow-filtering="true"
            filter-type="Contains"
            @focus="focusWarehouse('warehouse')"
            @change="warehouseChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('VMI仓类型')">
          <mt-input v-model="vmiWarehouseTypeDisplay" :disabled="true"></mt-input>
        </mt-form-item>

        <!-- 供方备注 -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="headerInfo.isDisabled"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass } from '../config/constant.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { headerInfo } from '../config/variable'

export default {
  props: {},
  data() {
    return {
      headerInfo,
      isExpand: true,
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        vmiWarehouseCode: [{ required: true, message: this.$t('请选择VMI仓'), trigger: 'blur' }]
      },
      factorySelect: [], //工厂
      supplierSelect: [], //供应商
      warehouseSelect: [], //vmi仓
      requestUrl: {
        pre: 'thirdPartyVMICollaboration',
        url: 'postthirdPartyFactorySelectList'
      },
      parameterValue: '',
      dataLimit: 20,
      labelShowObj: {}
    }
  },
  watch: {
    headerInfo: {
      handler() {
        // details === 1 是详情  否则不是
        if (this.$route.query.details == 1) {
          headerInfo.submitStatus = false
          headerInfo.isDisabled = true
          this.selectfun()
        } else if (this.$route.query.status == 0) {
          headerInfo.isDisabled = false
          headerInfo.submitStatus = true
          this.selectfun()
        }
      },
      deep: true
    }
  },
  computed: {
    site() {
      return `${headerInfo?.siteCode}-${headerInfo?.siteName}`
    },
    supplier() {
      return `${headerInfo?.supplierCode}-${headerInfo?.supplierName}`
    },

    vmiWarehouse() {
      return `${headerInfo?.vmiWarehouseCode}-${headerInfo?.vmiWarehouseName}`
    },

    selectedDisabled() {
      return Boolean(headerInfo.isDisabled || headerInfo.hasDetailRow)
    },
    vmiWarehouseTypeDisplay() {
      const _map = {
        0: this.$t('SRM管理库存'),
        1: this.$t('原厂'),
        2: this.$t('WMS管理库存')
      }
      return !_map[headerInfo.vmiWarehouseType]
        ? headerInfo.vmiWarehouseType
        : _map[headerInfo.vmiWarehouseType]
    }
  },
  mounted() {
    this.getFactoryList()
    this.getSelect('supplier')
    // this.getDatalist = utils.debounce(this.getDatalist, 300);
  },
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    selectfun() {
      this.factorySelect.push({
        text: headerInfo.siteCode,
        value: headerInfo.siteCode
      })
      this.supplierSelect.push({
        text: headerInfo.supplierCode,
        value: headerInfo.supplierCode
      })
      this.warehouseSelect.push({
        text: headerInfo.vmiWarehouseCode,
        value: headerInfo.vmiWarehouseCode
      })
    },
    checkForm() {
      let validStatus = false
      this.$refs.ruleForm.validate((valid) => {
        validStatus = valid
      })
      return validStatus
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 接受
    preservationBtn() {
      this.$emit('preservationBtn')
    },
    // 确认
    submitBtn() {
      this.$emit('submitBtn')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    },
    // 切换工厂时
    factoryChange(args) {
      const { itemData } = args
      headerInfo.siteName = itemData.label
      headerInfo.siteCode = itemData.value
      this.$nextTick(() => {
        // this.getSelect("warehouse");
        this.getVmiWarehouse()
      })
    },
    // 切换供应商时
    supplierChange() {
      this.$nextTick(() => {
        // this.getSelect("warehouse");
        this.getVmiWarehouse()
      })
    },
    // 查询VMI仓接口 - 新
    getVmiWarehouse() {
      const params = {
        defaultRules: [
          {
            field: 'rel.site_code',
            operator: 'equal',
            value: headerInfo.siteCode
          },
          {
            field: 'rel.supplier_code',
            operator: 'equal',
            value: headerInfo.supplierCode
          },
          {
            field: 'base.vmiWarehouse_type',
            operator: 'in',
            value: this.$route.query?.name === 'allocation' ? [0] : [0, 2]
          }
        ]
      }
      this.$API.supplierCoordination.getVmiWarehouseThird(params).then((res) => {
        if (res.code === 200 && res.data.length > 0) {
          this.warehouseSelect = res.data.reduce((pre, now) => {
            pre.push({
              text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
              value: now.vmiWarehouseCode,
              label: now.vmiWarehouseName,
              ...now
            })
            return pre
          }, [])
        }
      })
    },
    // 切换vmi仓
    warehouseChange(args) {
      const { itemData } = args
      headerInfo.vmiWarehouseCode = itemData.value
      headerInfo.vmiWarehouseName = itemData.label
      headerInfo.vmiWarehouseType = itemData.vmiWarehouseType
    },
    // 获取工厂列表接口
    getSelect(data) {
      let obj = {
        resultType: data,
        vmiWarehouseType: 0
      }
      // 如果是工厂/供应商改变，需要重新获取VMI。并且这俩都得有值
      if (data == 'warehouse') {
        if (headerInfo.siteCode && headerInfo.supplierCode) {
          obj.siteCode = headerInfo.siteCode
          obj.supplierCode = headerInfo.supplierCode
        } else {
          return
        }
      }
      this.$API.supplierCoordination.getVmiWarehouseThird(obj).then((res) => {
        if (res.code === 200 && res.data.length > 0) {
          if (data === 'supplier') {
            // 供应商
            let newObj = {}
            // 供应商
            this.supplierSelect = res.data.reduce((pre, now) => {
              newObj[now.supplierCode]
                ? ''
                : (newObj[now.supplierCode] = pre.push({
                    text: now.supplierCode + '-' + now.supplierName,
                    value: now.supplierCode,
                    label: now.supplierName,
                    ...now
                  }))
              return pre
            }, [])
          } else if (data === 'warehouse') {
            // 仓库
            this.warehouseSelect = res.data.reduce((pre, now) => {
              pre.push({
                text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
                value: now.vmiWarehouseCode,
                ...pre
              })
              return pre
            }, [])
          }
        }
      })
    },
    // 获取工厂接口
    getFactoryList() {
      const params = {
        defaultRules: [
          {
            field: 'base.vmiWarehouse_type',
            operator: 'in',
            value: [0, 2]
          }
        ]
      }
      this.$API.supplierCoordination.getVmiWarehouseThird(params).then((res) => {
        if (res.code === 200 && res.data.length > 0) {
          let newObj = {}
          this.factorySelect = res.data.reduce((pre, now) => {
            newObj[now.siteCode]
              ? ''
              : (newObj[now.siteCode] = pre.push({
                  text: now.siteCode + '-' + now.siteName,
                  value: now.siteCode,
                  label: now.siteName,
                  ...now
                }))
            return pre
          }, [])
        }
      })
    },
    // 获取焦点时
    focusSite(e) {
      this.parameterValue = e
      this.labelShowObj = {
        code: 'siteCode',
        name: 'siteName'
      }
    },
    focusWarehouse(e) {
      this.parameterValue = e
    },
    focusSupplier(e) {
      this.parameterValue = e
    },
    // 工厂，供应商，vmi仓，搜索    jcs
    getDatalist(e = { text: '' }) {
      let than = this
      let params = {
        resultType: this.parameterValue,
        keyword: e.text
      }
      // 如果是工厂改变，过滤仓库类型为 第三方物流
      if (than.parameterValue == 'warehouse') {
        params.vmiWarehouseType = 0
      }
      // dataLimit 限制返回条数
      this.$API[this.requestUrl.pre][this.requestUrl.url](params).then((res) => {
        let dataList = []
        if (than.parameterValue === 'site') {
          dataList = res.data.reduce((pre, now) => {
            pre.push({
              text: now.siteName + '-' + now.siteCode,
              value: now.siteCode,
              ...pre
            })
            return pre
          }, [])
        } else if (than.parameterValue === 'supplier') {
          dataList = res.data.reduce((pre, now) => {
            pre.push({
              text: now.supplierCode + '-' + now.supplierName,
              value: now.supplierCode,
              ...pre
            })
            return pre
          }, [])
        } else if (than.parameterValue === 'warehouse') {
          dataList = res.data.reduce((pre, now) => {
            pre.push({
              text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
              value: now.vmiWarehouseCode,
              ...pre
            })
            return pre
          }, [])
        }
        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(dataList)
          }
        })

        if (res.total > this.dataLimit) {
          this.$toast({
            content: this.$t('搜索结果较多，请再输入更精确的查询条件'),
            type: 'warning'
          })
        }

        this.selectVal = this.initVal
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(33% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
