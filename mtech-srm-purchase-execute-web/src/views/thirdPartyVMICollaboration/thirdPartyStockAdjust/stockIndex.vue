//第三方 VMI库存管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/stockIndex.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          activatedRefresh: false,

          toolbar: {
            tools: [
              [
                {
                  id: 'allocate',
                  title: this.$t('5500与5520调拨')
                },
                {
                  id: 'checkEditInfo',
                  title: this.$t('查看变更记录')
                }
              ],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: 'fe08e8ea-d748-4924-b856-26103f7e61a6',
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [
              {
                factoryCode: '01',
                factoryName: this.$t('测试1'),
                supplierCode: '001',
                supplierName: this.$t('京东'),
                warehouseCode: this.$t('仓库编码'),
                warehouseName: this.$t('仓库名称'),
                materialCode: '0001',
                materialName: this.$t('物料名称'),
                purchaseGroup: this.$t('采购组'),
                stockStatus: this.$t('充足'),
                batchNumber: this.$t('第一批次'),
                stockNumber: '99',
                founder: 'jcs'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi_stock/stock-logistic-page-query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      let _selectRows = e.grid.getSelectedRecords()

      if (e.toolbar.id === 'allocate') {
        if (_selectRows.length !== 1) {
          this.$toast({ content: this.$t('请选择一行数据进行调拨'), type: 'warning' })
          return
        }
        if (!['5500', '5520'].includes(_selectRows[0].siteCode)) {
          this.$toast({ content: this.$t('请选择5500或5520工厂进行调拨'), type: 'warning' })
          return
        }
        let toSiteCode = ''
        if (_selectRows[0].siteCode === '5500') {
          toSiteCode = '5520'
        } else {
          toSiteCode = '5500'
        }
        let count = _selectRows[0].count
        this.handleAllocate(_selectRows[0].id, toSiteCode, count)
      }
      if (e.toolbar.id === 'checkEditInfo') {
        if (_selectRows.length !== 1) {
          this.$toast({ content: this.$t('只能查看单条数据的变更记录'), type: 'warning' })
          return
        }
        sessionStorage.setItem('checkEditHeaderInfo', JSON.stringify(_selectRows[0]))
        this.$dialog({
          modal: () => import('@/views/supplierCoordination/stockAdministration/checkEditInfo.vue'),
          data: {
            headerTitle: this.$t('查看变更记录')
          },
          success: () => {}
        })
      }
      if (e.toolbar.id === 'export') {
        this.downloadTemplate()
      }
    },
    handleAllocate(id, toSiteCode, count) {
      this.$dialog({
        data: {
          title: this.$t('调拨'),
          toSiteCode,
          count
        },
        modal: () =>
          import(
            /* webpackChunkName: "./components/allocateDialog.vue" */ './components/allocateDialog.vue'
          ),
        success: (form) => {
          let params = {
            id,
            toSiteCode: form.toSiteCode,
            quantity: form.quantity
          }
          this.$store.commit('startLoading')
          this.$API.purchaseCoordination.stockAllocationKtApi(params).then((res) => {
            this.$store.commit('endLoading')
            if (res.code === 200) {
              this.$toast({ content: this.$t('调拨成功'), type: 'success' })
            }
          })
        }
      })
    },
    downloadTemplate() {
      let obj = JSON.parse(
        sessionStorage.getItem('fe08e8ea-d748-4924-b856-26103f7e61a6')
      )?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        columnData.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination.vmiLogStockExport(params, field).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
        this.$store.commit('endLoading')
      })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
