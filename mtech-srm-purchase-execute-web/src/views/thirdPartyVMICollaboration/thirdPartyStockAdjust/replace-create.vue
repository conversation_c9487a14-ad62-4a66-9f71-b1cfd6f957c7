// 第三方-vmi物料替换单-创建
<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      class="flex-keep"
      @goBack="goBack"
      @preservationBtn="preservationBtn"
      @submitBtn="submitBtn"
      ref="infoRules"
    ></top-info>
    <mt-template-page :template-config="componentConfig">
      <div slot="slot-0" :style="{ padding: '30px' }">
        <mt-data-grid
          ref="dataGrid"
          :data-source="dataSource"
          :column-data="columnData"
          :edit-settings="editing"
          :toolbar="toolbar"
          @actionBegin="actionBegin"
          @actionComplete="actionComplete"
          @toolbarClick="toolbarClick"
          v-if="gridStatus"
          @dataBound="dataBound"
        ></mt-data-grid>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import { headerInfo, headerInfoInit, setHeaderInfo } from './config/variable.js'
import { replaceCols, checkData } from './config'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  props: {},
  /**
   * 调出
      itemCode
      itemUnit
      purchaseGroupCode

    调入
      exchangeItemCode
      exchangePurchaseGroupCode
   */
  data() {
    return {
      componentConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false // 使用组件中的toolbar配置
        }
      ],
      dataSource: [
        // {
        //   itemCode: "1-1-1",
        //   itemName: "物料1 ",
        //   outStatus: this.$t("合格库存"),
        //   enterStatus: this.$t("不合格库存"),
        //   number: 0,
        //   baseMeasureUnitName: "",
        //   purchaseGroupCode: "",
        // },
      ],
      columnData: replaceCols,
      toolbar: [
        {
          text: this.$t('新增'),
          id: 'addinlinebtn',
          prefixIcon: 'e-add',
          fn: this.handleAdd
        },
        'Edit',
        'Cancel',
        'Update',
        'Delete'
      ],
      editing: {
        allowEditing: true, //是否允许编辑
        allowDeleting: true, //是否允许删除
        allowAdding: true, //是否允许新增
        showDeleteConfirmDialog: true
      },
      // headerInfo: {
      //   submitStatus: true,
      //   isDisabled: false, //false可编辑  true不可编辑
      // }, //头部信息
      gridStatus: false
    }
  },
  watch: {
    $route(to) {
      // 如果是返回列表，那么就移除dom，防止下拉组件多次触发change事件
      if (to.name == 'thirdPartyStockAdjust') {
        headerInfoInit()
        localStorage.removeItem(`itemCode`)
      }
    }
  },
  mounted() {
    if (this.$route.query.details == 1) {
      this.toolbar = []
    } else {
      this.gridStatus = true
    }
    if (this.$route.query.id) this.getDetails()
  },
  methods: {
    dataBound() {
      let dataRef = this.$refs.dataGrid.$refs.ejsRef
      let _dataSource = dataRef.getCurrentViewRecords()
      _dataSource.forEach((item, index) => {
        dataRef.setCellValue(item.addId, 'rowNum', Number(index + 1))
      })
    },
    toolbarClick(e) {
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (e.item.id == 'addinlinebtn') {
        if (!this.$refs.infoRules.checkForm()) return
        // 新增
        this.$refs.dataGrid.ejsRef.addRecord()
        headerInfo.hasDetailRow = true
      }
    },
    // 根据列新建行数据
    // newRowData() {
    //   let row = {};
    //   // 初始化数据
    //   this.columnData.forEach((item) => {
    //     if (item.field === "rowNum") {
    //       row[item.field] =
    //         this.$refs.dataGrid.ejsRef.getCurrentViewRecords().length + 1;
    //     }
    //     // else if(item.field === 'stockType'){
    //     //   row[item.field] = this.$t('合格库存')
    //     // }
    //   });
    //   return row;
    // },
    actionBegin(args) {
      console.log(args)
      if (this.$route.query.details == 1 && args.rowIndex == args.rowIndex) {
        args.cancel = true //禁止行编辑
      }
      if (args.requestType == 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        args.data.sourceStockType = this.$route.path.includes('replace-create')
          ? this.$t('合格库存')
          : ''
      }
    },
    actionComplete(args) {
      if (args.requestType == 'delete') {
        if (this.$refs.dataGrid?.$refs?.ejsRef?.getCurrentViewRecords()?.length === 0) {
          headerInfo.hasDetailRow = false // 使头部数据可改变，因为通过头部数据获取物料，表格没有数据时，头部数据可修改
        }
      }
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    handleClick() {
      //获取编辑后的整体数据
      console.log(this.$refs.dataGrid.ejsRef.getCurrentViewRecords(), '-=-=-=')
    },
    switchData(data) {
      let statusType = -1
      switch (data) {
        case this.$t('合格库存'):
          statusType = 0
          break
        case this.$t('待检库存'):
          statusType = 1
          break
        case this.$t('不合格库存'):
          statusType = 3
          break
        default:
          break
      }
      return statusType
    },
    switchNumber(data) {
      let statusType = ''
      switch (data) {
        case 0:
          statusType = this.$t('合格库存')
          break
        case 1:
          statusType = this.$t('待检库存')
          break
        case 3:
          statusType = this.$t('不合格库存')
          break
        default:
          break
      }
      return statusType
    },
    // 创建的数据处理
    createHandle() {
      let dataList = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()
      // 校验表格数据
      const { pass, msg } = checkData({ dataList })

      if (!pass) {
        this.$toast({
          content: msg,
          transportType: 'error'
        })
        return
      }

      let obj = {
        id: '',
        itemList: [],
        operationType: 1,
        remark: headerInfo.remark,
        siteCode: headerInfo.siteCode,
        siteName: headerInfo.siteName,
        supplierCode: headerInfo.supplierCode,
        vmiWarehouseCode: headerInfo.vmiWarehouseCode,
        vmiWarehouseName: headerInfo.vmiWarehouseName
      }
      if (this.$route.query.status == 0) {
        obj.id = this.$route.query.id
      }
      let arrObj = {}
      dataList.forEach((item) => {
        // if (this.$route.query.status == 0) arrObj.id = item.id;
        arrObj = { ...item }

        arrObj.sourceStockType = this.switchData(item.sourceStockType) //订单操作的库存属性分类
        arrObj.stockType = arrObj.sourceStockType //后端让保持一致就行
        // arrObj.sourceStockType = 0
        obj.itemList.push(arrObj)
      })
      return obj
    },
    // 保存
    preservationBtn() {
      const data = this.createHandle()
      data.operationType = 1
      if (data.itemList?.length === 0) {
        this.$toast({
          content: this.$t('【物料信息】未填写'),
          type: 'warning'
        })
        return
      }
      this.createData(data)
    },
    // 提交
    submitBtn() {
      const data = this.createHandle()
      data.operationType = 2
      if (data.itemList?.length === 0) {
        this.$toast({
          content: this.$t('【物料信息】未填写'),
          type: 'warning'
        })
        return
      }
      this.createData(data)
    },
    // 创建接口
    createData(data) {
      this.$API.thirdPartyVMICollaboration.postthirdPartyReplaceCreate(data).then((res) => {
        let successText = ''
        let errorText = ''
        if (data.operationType === 2) {
          successText = this.$t('提交成功')
          errorText = this.$t('提交失败')
        } else {
          successText = this.$t('保存成功')
          errorText = this.$t('保存失败')
        }
        if (res.code === 200) {
          this.$toast({ content: successText, type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: errorText, type: 'error' })
        }
      })
    },
    // 三方公用一个详情接口
    getDetails() {
      let _that = this
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination
        .postBuyerWarehousingReturnDetail(obj)
        .then((res) => {
          if (res.code === 200) {
            setHeaderInfo(res.data)

            if (this.$route.query.status != 0) headerInfo.isDisabled = true

            let dataSource = res.data.vmiOrderItemResponses

            _that.gridStatus = true
            dataSource.forEach((item) => {
              item.addId = item.id
              item.sourceStockType = this.switchNumber(item.sourceStockType)
              let _key =
                item.sourceStockType == this.$t('合格库存')
                  ? 'count'
                  : item.sourceStockType == this.$t('待检库存')
                  ? 'qcCount'
                  : 'ineffectivenessLockCount'
              item.lockCount = item?.vmiStockResponse?.[_key]
            })

            _that.dataSource = dataSource
          } else {
            _that.gridStatus = true
          }
        })
        .catch(() => {
          _that.gridStatus = true
        })
    }
  }
}
</script>

<style scoped lang="scss"></style>
