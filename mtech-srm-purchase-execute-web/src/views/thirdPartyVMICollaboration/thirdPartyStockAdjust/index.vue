<template>
  <!-- 第三方物流VWI协同-入库管理页-->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      class="templateStyle"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <!-- sourcing -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { columnData, columnData2 } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.pageConfig = [
        {
          title: this.$t('头视图'),

          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useCombinationSelection: false,
          toolbar: {
            //工具栏和右侧筛选
            tools: [
              [
                {
                  id: 'acceptanceBtn',
                  title: this.$t('创建VMI物料调拨单'),
                  icon: 'icon_solid_export',
                  permission: ['O_02_1107']
                },
                {
                  id: 'returnBtn',
                  title: this.$t('创建VMI物料替换单'),
                  icon: 'icon_solid_export',
                  permission: ['O_02_1108']
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                },
                {
                  id: 'upload',
                  title: this.$t('调拨单导入'),
                  icon: 'icon_solid_export'
                },
                {
                  id: 'upload2',
                  title: this.$t('替换单导入'),
                  icon: 'icon_solid_export'
                }
                // {
                //   id: "Delete",
                //   title: this.$t("删除"),
                //   icon: "icon_table_delete",
                // },
              ], //新增  删除
              [
                'Filter',
                {
                  id: 'export',
                  title: this.$t('导出'),
                  icon: 'icon_solid_export'
                },
                'Refresh',
                'Setting'
              ] //筛选   刷新  设置
            ]
          },
          gridId: '649dd60b-b73e-49eb-ab31-e6425621f169',
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/logistic-page-query',
              recordsPosition: 'data.records',
              defaultRules: JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules
            }
            // frozenColumns: 1,
          }
        },
        {
          useToolTemplate: false,
          title: this.$t('明细列表'),
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                'Filter',
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: '06056fd4-5d11-4b3a-807a-7f7315292675',
          grid: {
            columnData: columnData2,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/logistic-page-query-ext-detail',
              recordsPosition: 'data.records'
            }
            // frozenColumns: 1,
          }
        }
      ]
    } else {
      this.pageConfig = [
        {
          title: this.$t('头视图'),

          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useCombinationSelection: false,
          toolbar: {
            //工具栏和右侧筛选
            tools: [
              [
                {
                  id: 'acceptanceBtn',
                  title: this.$t('创建VMI物料调拨单'),
                  icon: 'icon_solid_export',
                  permission: ['O_02_1107']
                },
                {
                  id: 'returnBtn',
                  title: this.$t('创建VMI物料替换单'),
                  icon: 'icon_solid_export',
                  permission: ['O_02_1108']
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                },
                {
                  id: 'upload',
                  title: this.$t('调拨单导入'),
                  icon: 'icon_solid_export'
                },
                {
                  id: 'upload2',
                  title: this.$t('替换单导入'),
                  icon: 'icon_solid_export'
                }
                // {
                //   id: "Delete",
                //   title: this.$t("删除"),
                //   icon: "icon_table_delete",
                // },
              ], //新增  删除
              [
                'Filter',
                {
                  id: 'export',
                  title: this.$t('导出'),
                  icon: 'icon_solid_export'
                },
                'Refresh',
                'Setting'
              ] //筛选   刷新  设置
            ]
          },
          gridId: '649dd60b-b73e-49eb-ab31-e6425621f169',
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/logistic-page-query',
              recordsPosition: 'data.records',
              defaultRules: []
            }
            // frozenColumns: 1,
          }
        },
        {
          useToolTemplate: false,
          useCombinationSelection: false,
          title: this.$t('明细列表'),
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                'Filter',
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: '06056fd4-5d11-4b3a-807a-7f7315292675',
          grid: {
            columnData: columnData2,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/logistic-page-query-ext-detail',
              recordsPosition: 'data.records'
            }
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  data() {
    return {
      downTemplateParams: {
        flag: 0
      }, // 下载模板参数
      requestUrls: {}, // 上传下载接口地址
      pageConfig: [
        //配置tab
        // 库存调整管理
      ],
      addDialogShow: false,
      dialogData: null,
      allocationArr: [],
      replaceArr: []
    }
  },
  methods: {
    // 接口
    // 表格头部操作
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.refreshColumns()
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'upload') {
        this.requestUrls = {
          templateUrlPre: 'supplierCoordination',
          templateUrl: 'vmiAllocationOrderTemplateDownload',
          uploadUrl: 'vmiAllocationOrderImport'
        } // 上传下载接口地址
        this.showUploadExcel(true)
        return
      }
      if (e.toolbar.id == 'upload2') {
        this.requestUrls = {
          templateUrlPre: 'supplierCoordination',
          templateUrl: 'vmiReplaceOrderTemplateDownload',
          uploadUrl: 'vmiReplaceOrderImport'
        } // 上传下载接口地址
        this.showUploadExcel(true)
        return
      }
      if (e.toolbar.id === 'acceptanceBtn') {
        // 创建VMI物料调拨单
        // this.redirectPage("purchase-execute/allocation-create", {
        //   name: "allocation",
        // });
        this.$router.push({
          path: '/purchase-execute/allocation-create',
          query: {
            name: 'allocation'
          }
        })
      } else if (e.toolbar.id === 'returnBtn') {
        // 创建VMI物料替换单
        // this.redirectPage("purchase-execute/replace-create", {
        //   name: "replace",
        // });
        this.$router.push({
          path: '/purchase-execute/replace-create',
          query: {
            name: 'replace'
          }
        })
      } else if (e.toolbar.id === 'export') {
        let obj = JSON.parse(
          sessionStorage.getItem('649dd60b-b73e-49eb-ab31-e6425621f169')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        // 导出
        let params = {
          page: { current: 1, size: 0 },
          sortedColumnStr: field.toString()
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.logisticsHeaderStockExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('06056fd4-5d11-4b3a-807a-7f7315292675')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData2.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.logisticExcelExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id == 'synchronous') {
        const _records = e.grid.getSelectedRecords()
        if (_records.length <= 0 || _records.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.synchronousWms(_records[0])
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 刷新
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    //单元格操作按钮点击
    handleClickCellTool(e) {
      if (e.tool.id == 'submit') {
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.submit(e.data.id, e.data.vmiOrderType)
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(e.data.id, e.data.vmiOrderType)
          }
        })
      } else if (e.tool.id == 'delete') {
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            if (e.data.vmiOrderType === 5) {
              this.allocationArr.push(e.data.id)
              this.deleteAllocation()
            } else if (e.data.vmiOrderType === 6) {
              this.replaceArr.push(e.data.id)
              this.deleteReplace()
            }
          }
        })
      }
    },

    handleClickCellTitle(e) {
      if (e.field === 'vmiOrderCode') {
        let obj = {
          details: 1,
          id: e.data.id
        }
        if (e.data.status == 0) {
          obj.details = 0
          obj.status = 0
        }
        // 根据vmiOrderType判断
        // 调拨单
        if (e.data.vmiOrderType === 5) {
          obj.name = 'allocation'
          this.redirectPage('purchase-execute/allocation-create', obj)
        }
        // 替换单
        else if (e.data.vmiOrderType === 6) {
          obj.name = 'replace'
          this.redirectPage('purchase-execute/replace-create', obj)
        } else if (e.data.vmiOrderType === 4) {
          obj.details = 2 //-- 纯查看
          // 跳入 库存调整管理，手工录入供应商库存详情
          this.redirectPage('purchase-execute/supplier-stock-build', obj)
        } else if (e.data.vmiOrderType === 1) {
          // 跳入送货单
          obj.status = e.data.status
          this.redirectPage('purchase-execute/third-party-details', obj)
        } else if (e.data.vmiOrderType === 3) {
          // 跳入退货单
          obj.id = e.data.vmiOrderId ?? e.data.id
          obj.status = e.data.status
          this.redirectPage('purchase-execute/third-return-details', obj)
        }
      }
    },
    // 路由跳转
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 提交 三方共用一个接口
    submit(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'postthirdPartyAllocationSubmit'
      } else if (valType == 6) {
        apiInterface = 'postthirdPartyReplaceSubmit'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.thirdPartyVMICollaboration[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    },
    // 取消 三方共用一个接口
    cancel(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'postthirdPartyAllocationCancel'
      } else if (valType == 6) {
        apiInterface = 'postthirdPartyReplaceCancel'
      }
      let obj = {
        id: data
      }
      this.$API.thirdPartyVMICollaboration[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('取消失败'), type: 'error' })
        }
      })
    },
    // 删除-调拨单
    deleteAllocation() {
      let obj = {
        ids: this.allocationArr
      }
      this.$API.thirdPartyVMICollaboration.postthirdPartyAllocationDelete(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.allocationArr = [] //清空一下防止id累积
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('调拨单删除失败'), type: 'error' })
        }
      })
    },
    // 删除-替换单
    deleteReplace() {
      // if(this.replaceArr.length === 0) return
      let obj = {
        // ids: this.replaceArr.map(item=> item.id)
        ids: this.replaceArr
      }
      this.$API.thirdPartyVMICollaboration.postthirdPartyReplaceDelete(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.refreshColumns()
          this.replaceArr = [] //清空一下防止累积
        } else {
          this.$toast({ content: this.$t('替换单删除失败'), type: 'error' })
        }
      })
    },
    // 同步WMS  3 : 退货  6: 替换
    synchronousWms(data) {
      if (data.wmsSyncStatus === 1) {
        this.$toast({
          content: this.$t('入库单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      if (data.vmiOrderType === 3 || data.vmiOrderType === 6) {
        this.$API.purchaseCoordination[
          data.vmiOrderType === 3 ? 'returnSynchronousWms' : 'replaceSynchronousWms'
        ]({ id: data.id }).then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('同步成功'), type: 'success' })
            this.refreshColumns()
          } else {
            this.$toast({ content: this.$t('同步失败'), type: 'warning' })
          }
        })
      } else {
        this.$toast({
          content: this.$t('请选择退货单或物料替换单'),
          type: 'warning'
        })
      }
    },
    // 刷新页面
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
.set-country {
  height: 100%;
}
/deep/ .templateStyle {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
      }
      & tbody td:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
    }
  }
}
</style>
