// 第三方-vmi库存调拨单-新建
<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      class="flex-keep"
      @goBack="goBack"
      @preservationBtn="preservationBtn"
      @submitBtn="submitBtn"
      ref="infoRules"
    ></top-info>
    <mt-template-page :template-config="componentConfig">
      <div slot="slot-0" :style="{ padding: '30px' }">
        <mt-data-grid
          v-if="gridStatus"
          ref="dataGrid"
          :data-source="dataSource"
          :column-data="columnData"
          :edit-settings="editing"
          :toolbar="toolbar"
          @actionBegin="actionBegin"
          @actionComplete="actionComplete"
          @toolbarClick="toolbarClick"
          @dataBound="dataBound"
        ></mt-data-grid>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import { headerInfo, headerInfoInit, setHeaderInfo } from './config/variable.js'
import { allocationCols, checkData } from './config'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  props: {},
  data() {
    return {
      headerInfo,
      componentConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false // 使用组件中的toolbar配置
        }
      ],
      dataSource: [],
      columnData: allocationCols,
      toolbar: [
        {
          text: this.$t('新增'),
          id: 'addinlinebtn',
          prefixIcon: 'e-add'
        },
        'Edit',
        'Cancel',
        'Update',
        'Delete'
      ],
      editing: {
        allowEditing: true, //是否允许编辑
        allowDeleting: true, //是否允许删除
        allowAdding: true, //是否允许新增
        showDeleteConfirmDialog: true
      },
      // headerInfo: {
      //   submitStatus: true,
      //   isDisabled: false, //false可编辑  true不可编辑
      // }, //头部信息
      gridStatus: false //详情时获取数据后再加载表格
    }
  },
  watch: {
    $route(to) {
      // 如果是返回列表，那么就移除dom，防止下拉组件多次触发change事件
      if (to.name == 'thirdPartyStockAdjust') {
        headerInfoInit()
        localStorage.removeItem(`itemCode`)
      }
    }
  },
  mounted() {
    if (this.$route.query.details == 1) {
      this.toolbar = []
    } else {
      this.gridStatus = true
    }
    if (this.$route.query.id) this.getDetails()
  },
  methods: {
    dataBound() {
      let dataRef = this.$refs.dataGrid.$refs.ejsRef
      let _dataSource = dataRef.getCurrentViewRecords()
      _dataSource.forEach((item, index) => {
        dataRef.setCellValue(item.addId, 'rowNum', Number(index + 1))
      })
    },
    toolbarClick(e) {
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (e.item.id == 'addinlinebtn') {
        if (!this.$refs.infoRules.checkForm()) return
        // 新增
        this.$refs.dataGrid.ejsRef.addRecord()
        headerInfo.hasDetailRow = true
      }
    },
    // 根据列新建行数据
    // newRowData() {
    //   let row = {};
    //   // 初始化数据
    //   this.columnData.forEach((item) => {
    //     if (item.field === "rowNum") {
    //       row[item.field] =
    //         this.$refs.dataGrid.ejsRef.getCurrentViewRecords().length + 1;
    //     }
    //     // else if(item.field === 'outStatus'){
    //     //   row[item.field] = this.$t('合格库存')
    //     // }
    //   });
    //   return row;
    // },
    actionBegin(args) {
      if (this.$route.query.details == 1 && args.rowIndex == args.rowIndex) {
        args.cancel = true //禁止行编辑
      }
      if (args.requestType == 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      }
    },
    actionComplete(args) {
      if (args.requestType == 'delete') {
        if (this.$refs.dataGrid?.$refs?.ejsRef?.getCurrentViewRecords()?.length === 0) {
          headerInfo.hasDetailRow = false // 使头部数据可改变，因为通过头部数据获取物料，表格没有数据时，头部数据可修改
        }
      }
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    switchData(data) {
      let statusType = -1
      switch (data) {
        case this.$t('合格库存'):
          statusType = 0
          break
        case this.$t('待检库存'):
          statusType = 1
          break
        case this.$t('不合格库存'):
          statusType = 3
          break
        default:
          break
      }
      return statusType
    },
    switchNumber(data) {
      let statusType = ''
      switch (data) {
        case 0:
          statusType = this.$t('合格库存')
          break
        case 1:
          statusType = this.$t('待检库存')
          break
        case 3:
          statusType = this.$t('不合格库存')
          break
        default:
          break
      }
      return statusType
    },
    handleClick() {
      //获取编辑后的整体数据
      console.log(this.$refs.dataGrid.ejsRef.getCurrentViewRecords(), '-=-=-=')
    },
    // 创建的数据处理
    createHandle(val) {
      let dataList = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()
      // 校验表格数据
      console.log(dataList)
      const { pass, msg } = checkData({ dataList })

      if (!pass) {
        this.$toast({
          content: msg,
          transportType: 'error'
        })
        return
      }

      let data = {
        // id: "",
        itemList: [],
        operationType: val,
        remark: headerInfo.remark,
        siteCode: headerInfo.siteCode,
        siteName: headerInfo.siteName,
        supplierCode: headerInfo.supplierCode,
        vmiWarehouseCode: headerInfo.vmiWarehouseCode,
        vmiWarehouseName: headerInfo.vmiWarehouseName
      }

      console.log('开始创建修改', val, dataList)

      dataList.forEach((item) => {
        let arrObj = { ...item }
        arrObj.sourceStockType = this.switchData(item.sourceStockType) //移出
        arrObj.stockType = this.switchData(item.stockType) //移入
        data.itemList.push(arrObj)
      })
      return data
    },
    // 保存 1
    preservationBtn() {
      if (!this.$refs.infoRules.checkForm()) return

      const data = this.createHandle(1)
      // 校验每一行的 出入库状态 是否不一致
      if (!this.checkPass(data.itemList)) {
        return
      }
      this.createData(data)
    },
    // 提交 2
    submitBtn() {
      if (!this.$refs.infoRules.checkForm()) return
      const data = this.createHandle(2)

      // 校验每一行的 出入库状态 是否不一致
      if (!this.checkPass(data.itemList)) {
        return
      }
      this.createData(data)
    },

    // 校验每一行的 出入库状态 是否不一致
    checkPass(itemList) {
      if (!itemList.length) {
        this.$toast({
          content: this.$t('【物料信息】未填写'),
          type: 'warning'
        })
        return
      }
      try {
        itemList.forEach((item, index) => {
          if (item.sourceStockType == item.stockType) {
            this.$toast({
              content: this.$t(
                `${this.$t('第')}${Number(index + 1)}${this.$t('行，调入调出的库存状态不应一致')}`
              ),
              type: 'warning'
            })
            throw new Error(`第${Number(index + 1)}行，调入调出的库存状态不应一致`)
          }
        })
      } catch (e) {
        console.log('youwenti ')
        return
      }
      return true
    },

    // 新建接口
    createData(data) {
      console.log(data)
      for (let item of data.itemList) {
        if (
          item.count === undefined ||
          item.count === null ||
          item.itemName === undefined ||
          item.itemName === null ||
          item.itemCode === undefined ||
          item.itemCode === null ||
          item.itemUnit === undefined ||
          item.itemUnit === null ||
          item.purchaseGroupCode === undefined ||
          item.purchaseGroupCode === null ||
          item.sourceStockType === -1 ||
          item.stockType === -1
          // item.purchaseGroupName === undefined ||
        ) {
          this.$toast({
            content: this.$t('保存失败，请检查是否点击保存且是否填写必填项'),
            type: 'error'
          })

          return
        }
      }
      console.log('整合的参数：', data)
      this.$API.thirdPartyVMICollaboration.postthirdPartyFactoryCreate(data).then((res) => {
        let successText = ''
        let errorText = ''
        if (data.operationType === 2) {
          successText = this.$t('提交成功')
          errorText = this.$t('提交失败')
        } else {
          successText = this.$t('保存成功')
          errorText = this.$t('保存失败')
        }
        if (res.code === 200) {
          this.$toast({ content: successText, type: 'success' })
          this.$router.go(-1)
        } else {
          this.$toast({ content: errorText, type: 'error' })
        }
      })
    },
    // 三方公用一个详情接口
    getDetails() {
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination
        .postBuyerWarehousingReturnDetail(obj)
        .then((res) => {
          if (res.code === 200) {
            if (res?.data) {
              setHeaderInfo(res?.data)
            }
            let dataSource = res.data.vmiOrderItemResponses
            if (this.$route.query.status != 0) headerInfo.isDisabled = true
            // headerInfo.siteCode = res.data.siteCode + "-" + res.data.siteName;
            // headerInfo.supplierCode =
            //   res.data.supplierCode + "-" + res.data.supplierName;
            // headerInfo.vmiWarehouseCode =
            //   res.data.vmiWarehouseCode + "-" + res.data.vmiWarehouseName;
            this.gridStatus = true
            dataSource.forEach((item) => {
              item.addId = item.id
              item.stockType = this.switchNumber(item.stockType)
              item.sourceStockType = this.switchNumber(item.sourceStockType)

              let _key =
                item.sourceStockType == this.$t('合格库存')
                  ? 'count'
                  : item.sourceStockType == this.$t('待检库存')
                  ? 'qcCount'
                  : 'ineffectivenessLockCount'
              item.lockCount = item?.vmiStockResponse?.[_key]
            })
            this.dataSource = dataSource
          } else {
            this.gridStatus = true
          }
        })
        .catch(() => {
          this.gridStatus = true
        })
    }
  }
}
</script>

<style scoped lang="scss"></style>
