import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

// 标签
export const tagTemplate = (args) => {
  const { dataKey, tagCssClass, tagText } = args
  const template = () => {
    return {
      template: Vue.component('tagTemplate', {
        template: `<span :class="[dataToClass(data[dataKey])]">{{ data[dataKey] | dataTotText}}</span>`,
        data: function () {
          return { data: {}, dataKey, tagCssClass, tagText }
        },
        mounted() {},
        methods: {
          dataToClass(value) {
            let cssClass = ''
            if (this.tagCssClass[value]) {
              cssClass = this.tagCssClass[value]
            }

            return cssClass
          }
        },
        filters: {
          dataTotText: (value) => {
            let text = ''
            if (tagText[value]) {
              text = tagText[value]
            } else {
              text = value
            }

            return text
          }
        }
      })
    }
  }

  return template
}

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, hasTime } = args

  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
