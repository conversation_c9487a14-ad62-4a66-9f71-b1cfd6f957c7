import { tagTemplate, timeDate } from './columnComponent'
import { CreateMarkCssClass, CreateMarkText } from './constant'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      allowFiltering: false, // 不允许过滤
      width: '150'
    }
    if (col.fieldCode === 'orderCode') {
      // 关联采购订单
      defaultCol.width = '250'
    } else if (col.fieldCode === 'itemNo') {
      // 关联采购订单行号
      defaultCol.width = '250'
    } else if (col.fieldCode === 'createMark') {
      // 可创建送货单
      defaultCol.template = tagTemplate({
        dataKey: col.fieldCode,
        tagCssClass: CreateMarkCssClass,
        tagText: CreateMarkText
      })
    } else if (col.fieldCode === 'requiredDeliveryDate') {
      // 需求日期 requiredDeliveryDate string
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: false
      })
    }
    colData.push(defaultCol)
  })

  return colData
}

// 验证 状态是否统一
export const verifyCreateMark = (data) => {
  let valid = true
  let createMark = ''
  for (let i = 0; i < data.length; i++) {
    createMark = data[i].createMark
    if (data[i] && data[i - 1] && data[i].createMark !== data[i - 1].createMark) {
      valid = false
      break
    }
  }

  return { valid, createMark }
}
